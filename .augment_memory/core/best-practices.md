# Java Spring Boot 项目最佳实践

## 代码结构最佳实践

### 1. 包结构组织

#### 推荐的包结构
```
com.wbgame
├── controller/          # 控制器层
│   ├── adv2/           # 广告配置相关控制器
│   └── operate/        # 运营相关控制器
├── service/            # 服务层
│   ├── impl/           # 服务实现类
│   ├── adv2/           # 广告业务服务
│   └── operate/        # 运营业务服务
├── mapper/             # 数据访问层
│   ├── master/         # 主数据源
│   ├── slave2/         # 从数据源
│   └── adv2/           # 广告数据源
├── pojo/               # 数据对象
│   ├── adv2/           # 广告相关实体
│   └── operate/        # 运营相关实体
├── config/             # 配置类
├── aop/                # 切面类
└── annotation/         # 自定义注解
```

#### 最佳实践原则
- **按业务模块分包**: 相关功能放在同一包下
- **分层清晰**: controller、service、mapper分层明确
- **命名规范**: 包名小写，类名驼峰命名

### 2. 类命名规范

#### 控制器命名
```java
// ✅ 好的命名
@RestController
public class Adv2DefaultConfigController {
    // 明确表示广告配置控制器
}

// ❌ 避免的命名
public class AdController {
    // 名称过于简单，不够明确
}
```

#### 服务类命名
```java
// ✅ 接口和实现类命名
public interface AdCodeConfigService {
    // 接口名称清晰
}

@Service
public class AdCodeConfigServiceImpl implements AdCodeConfigService {
    // 实现类以Impl结尾
}
```

## API设计最佳实践

### 1. RESTful API设计

#### URL设计规范
```java
@RequestMapping(value = "/adv2")
public class Adv2DefaultConfigController {
    
    // ✅ 好的API设计
    @PostMapping(value = "/config2Adpos/list")      // 查询列表
    @PostMapping(value = "/config2Adpos/insert")    // 新增
    @PostMapping(value = "/config2Adpos/update")    // 更新
    @PostMapping(value = "/config2Adpos/delete")    // 删除
    
    // 资源名词复数，动作用HTTP方法表示
}
```

#### 参数设计规范
```java
@ApiImplicitParams({
    @ApiImplicitParam(name = "cha_id", value = "子渠道", dataType = "String"),
    @ApiImplicitParam(name = "adpos", value = "广告位", dataType = "String"),
    @ApiImplicitParam(name = "start", value = "页码", dataType = "int"),
    @ApiImplicitParam(name = "limit", value = "条数", dataType = "int")
})
public String config2AdposList(String cha_id, String adpos, String adpos_type, 
                              String config_first, String user_group, int start, int limit) {
    // 参数命名清晰，类型明确，有完整的API文档
}
```

### 2. 响应格式统一

#### 统一返回格式
```java
// ✅ 成功响应
return ReturnJson.success(ret);

// ✅ 错误响应
return ReturnJson.toErrorJson("配置中缺少相关渠道，请检查配置");

// ✅ 带数据的成功响应
HashMap<String, Object> ret = new HashMap<>();
ret.put("total", ((Page) extendAdposVos).getTotal());
ret.put("data", extendAdposVos);
return ReturnJson.success(ret);
```

#### 分页响应格式
```java
// 标准分页响应结构
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 100,
        "data": [...]
    }
}
```

## 数据库操作最佳实践

### 1. MyBatis使用规范

#### Mapper接口设计
```java
public interface Adv2DefaultConfigMapper {
    // ✅ 方法名清晰表达操作意图
    List<ExtendAdposVo> selectConfigToAdposDefaultConfig(
        @Param("cha_id") String cha_id,
        @Param("adpos_type") String adpos_type,
        @Param("adpos") String adpos,
        @Param("config_first") String config_first,
        @Param("user_group") String user_group
    );
    
    int insertConfigToAdposDefaultConfig(List<ExtendAdposVo> list);
    int updateBatchAdstyleConfig(@Param("adstyle") String adstyle, @Param("ids") String ids);
    int deleteConfigToAdposDefaultConfig(@Param("ids") String ids);
}
```

#### 批量操作优化
```java
// ✅ 批量插入优化
public String config2AdposInsertBatch(List<ExtendAdposVo> list) {
    int i = adv2DefaultConfigMapper.insertConfigToAdposDefaultConfig(list);
    return ReturnJson.success("成功插入 " + i + " 条");
}

// ✅ 单个操作复用批量方法
public String config2AdposInsert(ExtendAdposVo vo) {
    return config2AdposInsertBatch(Lists.newArrayList(vo));
}
```

### 2. 分页查询规范

#### PageHelper使用
```java
public String config2AdposList(String cha_id, String adpos, String adpos_type, 
                              String config_first, String user_group, int start, int limit) {
    // ✅ 分页参数在查询前设置
    PageHelper.startPage(start, limit);
    
    List<ExtendAdposVo> extendAdposVos = adv2DefaultConfigMapper.selectConfigToAdposDefaultConfig(
        cha_id, adpos_type, adpos, config_first, user_group);
    
    // ✅ 获取总数和数据
    HashMap<String, Object> ret = new HashMap<>();
    ret.put("total", ((Page) extendAdposVos).getTotal());
    ret.put("data", extendAdposVos);
    
    return ReturnJson.success(ret);
}
```

## 异常处理最佳实践

### 1. AOP统一异常处理

#### 全局异常捕获
```java
@Around("@within(com.wbgame.annotation.ControllerLoggingEnhancer)")
public Object handlerControllerMethod(ProceedingJoinPoint pjp) {
    long startTime = System.currentTimeMillis();
    String result;
    
    try {
        result = (String) pjp.proceed();
        logger.info(pjp.getSignature() + " use time:" + (System.currentTimeMillis() - startTime));
    } catch (Throwable e) {
        // ✅ 记录详细错误信息
        logger.error("unexpected error", e);
        // ✅ 返回用户友好的错误信息
        result = ReturnJson.toErrorJson(e.getMessage());
    }
    
    return result;
}
```

### 2. 业务异常处理

#### 参数验证
```java
// ✅ 文件上传验证
if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
    // 处理逻辑
} else {
    return ReturnJson.toErrorJson("请上传有效的Excel文件");
}

// ✅ 数据验证
if (CollectionUtils.isEmpty(list)) {
    return ReturnJson.toErrorJson("配置中缺少相关渠道，请检查配置");
}
```

## 配置管理最佳实践

### 1. 多环境配置

#### 配置文件分离
```yaml
# application.yml - 公共配置
spring:
  profiles:
    active: dev

# application-dev.yml - 开发环境
# application-test.yml - 测试环境  
# application-prod.yml - 生产环境
```

#### 条件化配置
```java
@Configuration
@ConditionalOnProperty(prefix = "schedule", name = "enabled", havingValue = "true")
public class ScheduleConfig implements SchedulingConfigurer {
    // 只在配置启用时加载
}
```

### 2. Bean配置管理

#### 统一Bean配置
```java
@Configuration
public class BeanConfig {
    
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
    
    // 其他Bean配置
}
```

## 性能优化最佳实践

### 1. 缓存使用

#### Redis缓存配置
```java
@Configuration
@EnableCaching
public class CacheConfig {
    // Redis缓存配置
}

// 使用缓存注解
@Cacheable(value = "adConfig", key = "#cha_id")
public List<ExtendAdposVo> getAdConfig(String cha_id) {
    // 缓存查询结果
}
```

### 2. 异步处理

#### 异步任务配置
```java
@Configuration
@EnableAsync
public class AsyncConfig implements AsyncConfigurer {
    
    @Bean("taskExecutor")
    public ExecutorService taskExecutor() {
        return Executors.newScheduledThreadPool(9);
    }
}

// 异步方法
@Async("taskExecutor")
public void processDataAsync(List<Data> dataList) {
    // 异步处理逻辑
}
```

## 安全最佳实践

### 1. 权限验证

#### Token验证
```java
@Around("@within(com.wbgame.annotation.ControllerLoggingEnhancer)")
public Object handlerControllerMethod(ProceedingJoinPoint pjp) {
    // ✅ 多渠道获取Token
    String token = request.getHeader(tokenName);
    if (StringUtils.isBlank(token)) {
        token = request.getParameter(tokenName);
    }
    
    if (StringUtils.isBlank(token)) {
        return ReturnJson.toErrorJson("登录状态失效");
    }
    
    // 验证Token有效性
}
```

### 2. 数据验证

#### 输入参数验证
```java
// ✅ 空值检查
if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
    continue;
}

// ✅ 文件类型验证
if (file.getOriginalFilename().endsWith(".xls")) {
    // 处理Excel文件
}
```

## 日志记录最佳实践

### 1. 日志级别使用

#### 合理的日志级别
```java
@Slf4j
public class Adv2DefaultConfigController {
    
    // ✅ 信息日志
    log.info("处理广告配置请求: {}", request);
    
    // ✅ 错误日志
    log.error("处理广告配置失败", e);
    
    // ✅ 调试日志
    log.debug("配置详情: {}", config);
}
```

### 2. 性能监控

#### 方法执行时间记录
```java
long startTime = System.currentTimeMillis();
try {
    result = (String) pjp.proceed();
    // ✅ 记录执行时间
    logger.info(pjp.getSignature() + " use time:" + (System.currentTimeMillis() - startTime));
} catch (Throwable e) {
    logger.error("unexpected error", e);
}
```

## 代码质量最佳实践

### 1. 代码复用

#### 方法复用
```java
// ✅ 单个操作复用批量方法
public String config2AdposInsert(ExtendAdposVo vo) {
    return config2AdposInsertBatch(Lists.newArrayList(vo));
}

public String adsid2AdposInsert(ExtendAdposVo vo) {
    return adsid2AdposInsertBatch(Lists.newArrayList(vo));
}
```

### 2. 代码可读性

#### 清晰的变量命名
```java
// ✅ 清晰的变量名
List<ExtendAdposVo> extendAdposVos = mapper.selectConfig();
HashMap<String, Object> ret = new HashMap<>();
int successCount = mapper.insertBatch(list);

// ✅ 有意义的常量
private static final String TOKEN_HEADER_NAME = "token";
private static final int DEFAULT_PAGE_SIZE = 10;
```

### 3. 注释和文档

#### API文档注解
```java
@ApiOperation(value = "广告配置", notes = "广告配置")
@ApiImplicitParams({
    @ApiImplicitParam(name = "cha_id", value = "子渠道", dataType = "String"),
    @ApiImplicitParam(name = "adpos", value = "广告位", dataType = "String")
})
public String config2AdposList(String cha_id, String adpos) {
    // 方法实现
}
```

## 测试最佳实践

### 1. 单元测试

#### 测试覆盖关键业务逻辑
```java
@RunWith(SpringRunner.class)
@SpringBootTest
public class Adv2ServiceTest {
    
    @Autowired
    private Adv2Service adv2Service;
    
    @Test
    public void testInsertAdConfig() {
        // 测试广告配置插入
    }
}
```

### 2. 集成测试

#### 测试完整的业务流程
```java
@Test
public void testAdConfigWorkflow() {
    // 1. 创建配置
    // 2. 查询配置
    // 3. 更新配置
    // 4. 删除配置
}
```
