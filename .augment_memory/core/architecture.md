# 项目架构设计

## 项目概述

**项目名称**: wb-ssds (广告配置管理系统)  
**技术栈**: Java 8 + Spring Boot 1.5.9 + MyBatis + Maven  
**主要功能**: 广告配置管理、数据同步、变现统计分析  

## 核心架构

### 1. 分层架构设计

```
┌─────────────────────────────────────────┐
│              Controller Layer           │
│  (REST API接口层 - 广告配置管理)          │
├─────────────────────────────────────────┤
│               Service Layer             │
│  (业务逻辑层 - 广告策略、数据同步)         │
├─────────────────────────────────────────┤
│               Mapper Layer              │
│  (数据访问层 - MyBatis映射)              │
├─────────────────────────────────────────┤
│              Database Layer             │
│  (多数据源 - MySQL + Redis)             │
└─────────────────────────────────────────┘
```

### 2. 核心模块

#### 2.1 广告配置管理模块 (adv2)
- **控制器**: `Adv2DefaultConfigController`
- **服务层**: `Adv2Service`, `Adv2ServiceImpl`
- **功能**: 
  - 广告位配置管理 (config2Adpos)
  - 广告源管理 (adsid2Adpos)
  - 批量导入/导出 (Excel文件处理)
  - 策略配置和优先级管理

#### 2.2 广告代码配置模块
- **服务层**: `AdCodeConfigService`
- **功能**:
  - 穿山甲(CSJ)广告位管理
  - 快手(KS)广告位管理  
  - 优量汇(YLH)广告位管理
  - CPM价格配置

#### 2.3 数据同步模块
- **服务层**: `AdServiceImpl`
- **功能**:
  - 头条广告数据同步
  - 变现数据汇总
  - 多平台数据整合

### 3. 数据库设计

#### 3.1 多数据源配置
- **主数据源**: 业务数据存储
- **从数据源**: 数据分析和报表
- **Redis**: 缓存和会话管理
- **清洗数据源**: 数据ETL处理

#### 3.2 核心表结构
- `dn_extend_adconfig`: 广告配置表
- `dn_extend_adsid_manage`: 广告源管理表
- `dn_extend_adpos_manage`: 广告位管理表

### 4. 技术特性

#### 4.1 Spring Boot集成
- **版本**: 1.5.9.RELEASE
- **核心依赖**:
  - spring-boot-starter-web (Web服务)
  - spring-boot-starter-aop (AOP切面)
  - spring-boot-starter-data-redis (Redis集成)

#### 4.2 数据访问层
- **MyBatis**: 1.3.1 (ORM框架)
- **PageHelper**: 5.1.4 (分页插件)
- **Druid**: 1.1.2 (连接池)
- **MySQL**: 数据库驱动

#### 4.3 第三方集成
- **FastJSON**: 1.2.58 (JSON处理)
- **JetCache**: 2.4.4 (缓存框架)
- **Knife4j**: 2.0.5 (API文档)
- **JXL**: 2.6.12 (Excel处理)

### 5. 关键设计模式

#### 5.1 AOP切面编程
- **日志增强**: `@ControllerLoggingEnhancer`
- **权限控制**: Token验证和用户权限过滤
- **异常处理**: 统一异常捕获和响应

#### 5.2 配置管理模式
- **多环境配置**: 支持开发、测试、生产环境
- **动态配置**: 数据库配置热更新
- **策略模式**: 广告策略动态选择

#### 5.3 数据处理模式
- **批量处理**: Excel导入/导出批量操作
- **异步处理**: `@EnableAsync`异步任务
- **定时任务**: `@EnableScheduling`定时数据同步

### 6. 安全和性能

#### 6.1 安全机制
- **Token认证**: 基于Token的用户认证
- **权限过滤**: 用户权限和数据权限控制
- **参数验证**: 输入参数校验和SQL注入防护

#### 6.2 性能优化
- **连接池**: Druid连接池优化
- **缓存策略**: Redis缓存和JetCache多级缓存
- **分页查询**: PageHelper分页优化
- **异步处理**: 耗时操作异步执行

## 架构优势

1. **模块化设计**: 清晰的分层架构，便于维护和扩展
2. **多数据源支持**: 灵活的数据源配置，支持读写分离
3. **丰富的第三方集成**: 完善的技术栈，功能全面
4. **良好的扩展性**: 基于Spring Boot的微服务架构
5. **完善的监控**: AOP日志和性能监控

## 待优化点

1. **Spring Boot版本**: 当前使用1.5.9，建议升级到2.x版本
2. **Java版本**: 当前使用Java 8，可考虑升级到Java 11+
3. **安全框架**: 可集成Spring Security增强安全性
4. **微服务化**: 可考虑拆分为多个微服务
5. **容器化**: 支持Docker容器化部署
