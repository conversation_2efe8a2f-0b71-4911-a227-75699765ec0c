# Java Spring Boot 技术栈配置

## 核心技术

### 运行环境
- **Java版本**: 1.8
- **编码格式**: UTF-8
- **构建工具**: Maven 4.0.0
- **打包方式**: JAR

### 框架版本
- **Spring Boot**: 1.5.9.RELEASE
- **Spring Framework**: 4.3.x (由Spring Boot管理)
- **项目坐标**: com.blankcw.example:wb-ssds:0.0.1-SNAPSHOT

## 核心依赖

### Web层
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>
```

### 数据访问层
```xml
<!-- MyBatis -->
<dependency>
    <groupId>org.mybatis.spring.boot</groupId>
    <artifactId>mybatis-spring-boot-starter</artifactId>
    <version>1.3.1</version>
</dependency>

<!-- MySQL驱动 -->
<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
</dependency>

<!-- Druid连接池 -->
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>druid</artifactId>
    <version>1.1.2</version>
</dependency>
```

### 缓存和Redis
```xml
<!-- Redis -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
    <version>1.5.13.RELEASE</version>
</dependency>

<!-- JetCache缓存框架 -->
<dependency>
    <groupId>com.alicp.jetcache</groupId>
    <artifactId>jetcache-starter-redis</artifactId>
    <version>2.4.4</version>
</dependency>
```

### AOP和工具类
```xml
<!-- AOP -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-aop</artifactId>
</dependency>

<!-- 分页插件 -->
<dependency>
    <groupId>com.github.pagehelper</groupId>
    <artifactId>pagehelper</artifactId>
    <version>5.1.4</version>
</dependency>
```

## 第三方集成

### JSON处理
- **FastJSON**: 1.2.58 - 阿里巴巴JSON处理库
- **Gson**: 2.8.6 - Google JSON处理库

### API文档
- **Knife4j**: 2.0.5 - Swagger增强版API文档工具

### 文件处理
- **JXL**: 2.6.12 - Excel文件读写
- **Apache POI**: 3.14 - Office文档处理
- **Commons FileUpload**: 1.3.1 - 文件上传

### 云服务集成
- **阿里云OSS**: 2.4.0/3.10.2 - 对象存储服务
- **阿里云CDN**: 3.0.7 - 内容分发网络
- **阿里云QuickBI**: 1.6.0 - 商业智能服务
- **华为云SDK**: 3.1 - 华为云服务
- **亚马逊S3**: 1.11.336 - AWS对象存储
- **火山引擎TOS**: 2.4.0 - 字节跳动对象存储

### 支付集成
- **支付宝SDK**: 4.39.234.ALL - 支付宝支付接口

### 微服务组件
- **Dubbo**: 2.6.2 - 分布式服务框架
- **Nacos**: 2.6.7 - 服务注册与配置中心

## 开发工具

### 构建配置
```xml
<build>
    <finalName>wb-ssds</finalName>
    <plugins>
        <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
            <configuration>
                <jvmArguments>-Dfile.encoding=UTF-8</jvmArguments>
            </configuration>
        </plugin>
    </plugins>
</build>
```

### 热部署
- **SpringLoaded**: 1.2.5.RELEASE - 热部署支持
- **启动命令**: `mvn spring-boot:run`

### 测试框架
- **Spring Boot Test**: 测试启动器
- **JUnit**: 单元测试框架

## 常用命令

### Maven命令
```bash
# 编译项目
mvn compile

# 运行项目
mvn spring-boot:run

# 打包项目
mvn package

# 清理项目
mvn clean

# 跳过测试打包
mvn package -DskipTests
```

### 项目启动
```bash
# 开发环境启动
java -jar wb-ssds.jar

# 指定配置文件启动
java -jar wb-ssds.jar --spring.profiles.active=dev

# 指定端口启动
java -jar wb-ssds.jar --server.port=8080
```

## 配置特点

### 多数据源配置
- 支持主从数据库配置
- Redis缓存数据源
- 清洗数据源配置

### 异步和定时任务
- `@EnableAsync` - 异步任务支持
- `@EnableScheduling` - 定时任务支持
- 线程池配置优化

### AOP增强
- 控制器日志增强
- 权限验证切面
- 异常统一处理

## 性能优化

### 连接池优化
- Druid连接池配置
- 连接池监控和统计

### 缓存策略
- Redis分布式缓存
- JetCache多级缓存
- 本地缓存配置

### 分页优化
- PageHelper分页插件
- 数据库分页查询优化

## 安全配置

### 认证授权
- Token认证机制
- 用户权限过滤
- 接口访问控制

### 数据安全
- SQL注入防护
- XSS攻击防护
- 敏感数据加密

## 监控和日志

### 日志配置
- SLF4J + Logback日志框架
- 日志级别配置
- 日志文件滚动

### 性能监控
- Druid监控页面
- 接口响应时间统计
- 系统资源监控

## 部署建议

### 环境要求
- JDK 1.8+
- MySQL 5.7+
- Redis 3.0+
- Maven 3.3+

### 配置文件
- application.yml - 主配置文件
- application-dev.yml - 开发环境
- application-prod.yml - 生产环境

### 启动参数
```bash
-Xms512m -Xmx1024m
-Dfile.encoding=UTF-8
-Dspring.profiles.active=prod
```
