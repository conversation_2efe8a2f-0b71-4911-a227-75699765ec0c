# 重要架构决策记录

## 技术选型决策

### 1. Spring Boot 1.5.9 选择

#### 决策背景
- 项目启动时间较早，Spring Boot 1.5.x是当时的稳定版本
- 团队对1.5.x版本较为熟悉
- 现有依赖库与1.5.x兼容性良好

#### 决策内容
- 使用Spring Boot 1.5.9.RELEASE作为基础框架
- Java版本选择1.8，保证兼容性
- Maven作为构建工具

#### 影响分析
- **优势**: 稳定性高，社区支持完善，学习成本低
- **劣势**: 版本相对较老，部分新特性无法使用
- **风险**: 长期维护可能面临安全更新问题

#### 后续建议
- 考虑逐步升级到Spring Boot 2.x版本
- 评估升级对现有功能的影响
- 制定升级计划和测试策略

### 2. MyBatis作为ORM框架

#### 决策背景
- 项目需要复杂的SQL查询和数据处理
- 团队对SQL有较强的控制需求
- 需要支持多数据源配置

#### 决策内容
- 选择MyBatis 1.3.1作为ORM框架
- 使用XML配置SQL映射
- 集成PageHelper进行分页处理

#### 影响分析
- **优势**: SQL控制灵活，性能优化空间大，学习成本低
- **劣势**: 需要手写SQL，开发效率相对较低
- **适用场景**: 复杂查询、报表统计、数据分析

### 3. 多数据源架构设计

#### 决策背景
- 业务数据和分析数据需要分离
- 不同模块使用不同的数据库
- 需要支持读写分离

#### 决策内容
```java
// 主数据源 - 业务数据
@Qualifier("masterDataSource")

// 从数据源 - 分析数据  
@Qualifier("slave2DataSource")

// 广告数据源 - 广告相关数据
@Qualifier("adv2DataSource")

// 清洗数据源 - ETL处理
@Qualifier("cleanDataSource")
```

#### 影响分析
- **优势**: 数据隔离，性能优化，职责分离
- **劣势**: 配置复杂，事务管理困难
- **维护成本**: 需要维护多个数据源连接

## 架构设计决策

### 1. 分层架构设计

#### 决策内容
```
Controller Layer (控制器层)
    ↓
Service Layer (业务逻辑层)
    ↓  
Mapper Layer (数据访问层)
    ↓
Database Layer (数据库层)
```

#### 设计原则
- **单一职责**: 每层只负责特定的功能
- **依赖倒置**: 上层依赖下层的抽象接口
- **开闭原则**: 对扩展开放，对修改关闭

#### 实施细节
- Controller只处理HTTP请求和响应
- Service处理业务逻辑和事务管理
- Mapper负责数据访问和SQL执行
- 使用接口定义层间契约

### 2. AOP切面设计

#### 决策背景
- 需要统一的日志记录
- 需要权限验证和异常处理
- 避免横切关注点代码重复

#### 决策内容
```java
// 日志增强切面
@ControllerLoggingEnhancer

// 权限验证切面  
@ControllerEnhancer

// 统一异常处理
@Around注解实现
```

#### 实施效果
- 代码复用率提高
- 横切关注点集中管理
- 业务代码更加清晰

### 3. API设计规范

#### 决策内容
- 采用RESTful API设计风格
- 统一使用POST方法（考虑参数复杂性）
- 统一返回JSON格式

#### URL设计规范
```java
/adv2/config2Adpos/list     // 查询列表
/adv2/config2Adpos/insert   // 新增
/adv2/config2Adpos/update   // 更新  
/adv2/config2Adpos/delete   // 删除
```

#### 响应格式规范
```json
{
    "code": 200,
    "message": "success", 
    "data": {
        "total": 100,
        "data": [...]
    }
}
```

## 业务架构决策

### 1. 广告配置管理设计

#### 业务背景
- 需要管理多种广告平台的配置
- 支持批量操作和Excel导入导出
- 需要灵活的策略配置

#### 设计决策
```java
// 配置类型分离
config2Adpos    // 广告位配置
adsid2Adpos     // 广告源配置

// 操作类型统一
list/insert/update/delete/batch
```

#### 核心特性
- **配置分离**: 广告位配置和广告源配置分开管理
- **批量操作**: 支持单个和批量操作
- **文件导入**: 支持Excel文件批量导入
- **策略配置**: 支持动态策略配置

### 2. 数据同步策略

#### 业务需求
- 多个广告平台数据同步
- 实时性和准确性要求
- 数据清洗和转换

#### 设计方案
```java
// 异步数据同步
@Async
public void syncAdData()

// 定时任务调度
@Scheduled
public void scheduleSync()

// 数据验证和清洗
public void validateAndClean()
```

#### 关键决策
- 使用异步处理提高性能
- 定时任务保证数据及时性
- 数据验证确保数据质量

## 性能优化决策

### 1. 缓存策略设计

#### 决策背景
- 配置数据查询频繁
- 数据库查询压力大
- 需要提高响应速度

#### 缓存方案
```java
// Redis分布式缓存
@Cacheable(value = "adConfig")

// JetCache多级缓存
@Cached(name = "adConfig", expire = 3600)

// 本地缓存
Map<String, Object> localCache
```

#### 缓存策略
- **热点数据**: 使用Redis缓存
- **配置数据**: 使用本地缓存
- **查询结果**: 使用多级缓存

### 2. 分页查询优化

#### 决策内容
- 使用PageHelper插件统一分页
- 前端传递start和limit参数
- 返回total和data字段

#### 实施方案
```java
PageHelper.startPage(start, limit);
List<ExtendAdposVo> list = mapper.selectList();
ret.put("total", ((Page) list).getTotal());
ret.put("data", list);
```

### 3. 批量操作优化

#### 设计原则
- 单个操作复用批量方法
- 批量操作减少数据库交互
- 事务控制保证数据一致性

#### 实施效果
- 性能提升明显
- 代码复用率高
- 维护成本降低

## 安全设计决策

### 1. 权限验证机制

#### 决策背景
- 需要用户身份验证
- 需要接口访问控制
- 需要数据权限过滤

#### 实施方案
```java
// Token验证
String token = request.getHeader("token");

// 权限过滤
userPermissionsUtils.filterAppidByPermission()

// AOP统一验证
@Around("@annotation(ControllerLoggingEnhancer)")
```

### 2. 数据安全保护

#### 安全措施
- SQL注入防护（使用参数化查询）
- XSS攻击防护（输入验证）
- 敏感数据加密存储

#### 实施细节
```java
// 参数化查询
@Param("cha_id") String cha_id

// 输入验证
if (BlankUtils.checkBlank(input)) {
    return error;
}
```

## 运维部署决策

### 1. 应用打包策略

#### 决策内容
```xml
<finalName>wb-ssds</finalName>
<plugin>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-maven-plugin</artifactId>
</plugin>
```

#### 特性支持
- 可执行JAR包
- 内嵌Tomcat服务器
- 热部署支持（开发环境）

### 2. 配置管理策略

#### 环境配置
- 开发环境：application-dev.yml
- 测试环境：application-test.yml  
- 生产环境：application-prod.yml

#### 配置外置
- 数据库连接配置
- Redis连接配置
- 第三方服务配置

### 3. 监控和日志

#### 日志策略
- 使用SLF4J + Logback
- 按级别分类记录
- 文件滚动和归档

#### 监控指标
- 接口响应时间
- 数据库连接池状态
- 系统资源使用情况

## 未来演进规划

### 1. 技术升级计划

#### 短期目标（6个月内）
- 升级Spring Boot到2.x版本
- 升级Java到11版本
- 优化数据库查询性能

#### 中期目标（1年内）
- 引入Spring Security安全框架
- 实施微服务架构改造
- 完善监控和告警系统

#### 长期目标（2年内）
- 容器化部署（Docker + Kubernetes）
- 云原生架构转型
- 大数据处理能力增强

### 2. 架构演进方向

#### 微服务化
- 按业务域拆分服务
- 服务间通信优化
- 分布式事务处理

#### 云原生
- 容器化部署
- 服务网格
- 自动化运维

#### 数据驱动
- 实时数据处理
- 机器学习集成
- 智能决策支持
