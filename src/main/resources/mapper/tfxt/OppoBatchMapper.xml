<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.tfxt.OppoBatchMapper">

    <select id="getAllOppoAccount" resultType="java.lang.String">
        select account from dn_oppo_account_spend
    </select>

    <select id="getOppoAppids" resultType="com.alibaba.fastjson.JSONObject">
        select tappid,tappname from yyhz_0308.adv_platform_app_info where platform = 'oppo' group by tappid
    </select>

    <select id="getOppoCity" resultType="com.alibaba.fastjson.JSONObject">
        select id,parent_id,name from dn_oppo_batch_city
        <if test="grade == 1">
            where parent_id = 0
        </if>
        <if test="grade == 2">
            where parent_id != 0
        </if>
    </select>

    <select id="selectOppoBatchTemplateList" resultType="com.wbgame.pojo.jettison.vo.OppoBatchTemplateVo">
        select a.temp_id,a.accounts,temp_name,extension_flow,flow_scene,GROUP_CONCAT(DISTINCT global_spec_name) global_spec_names,create_owner,update_time
        from dn_oppo_batch_create_template a
        left join dn_oppo_batch_creative_set b on a.temp_id = b.temp_id
        where 1=1
        <if test="temp_name != null and temp_name !=''">
            and temp_name like concat('%',#{temp_name},'%')
        </if>
        <if test="create_owner != null and create_owner !=''">
            and create_owner = #{create_owner}
        </if>
		<if test="extension_flow != null and extension_flow !=''">
			and extension_flow in (${extension_flow})
		</if>
        <if test="flow_scene != null and flow_scene !=''">
            and flow_scene in (${flow_scene})
        </if>
        <if test="global_spec_id != null and global_spec_id !=''">
            and global_spec_id in (${global_spec_id})
        </if>
        <if test="accounts != null and accounts !=''">
            and accounts in (${accounts})
        </if>
        <if test="startTime != null and startTime !=''">
            and update_time between concat(#{startTime}," 00:00:00") and concat(#{endTime}," 23:59:59")
        </if>
        group by temp_id
        order by update_time desc
    </select>

    <select id="selectOppoBatchTemplateDetail"  resultType="com.wbgame.pojo.jettison.param.OppoBatchTemplateParam">
        select * from dn_oppo_batch_create_template where temp_id = #{temp_id}
    </select>

    <select id="selectOppoBatchCreatives" resultType="com.wbgame.pojo.jettison.vo.OppoBatchCreativeVo">
        select * from dn_oppo_batch_creative_set where temp_id = #{temp_id}
    </select>

    <insert id="insertOppoBatchTemplate">
		insert into dn_oppo_batch_create_template(temp_id,temp_name,accounts,extension_type,delivery_mode,day_limit,campaign_day_budget,campaign_name,pacing_status
		,campaign_status,group_name,extension_flow,flow_scene,oppo_appid,advertise_type,time_set,time_limit,begin_time,endTime,is_day_allocation,billing_type,price
		,ocpc_price,ocpc_type,deep_ocpc_price,deep_ocpc_type,group_num,default_second_stage,page_type,page_id,page_url,ad_name,click_url,brand_name,content,create_owner
		,update_owner,region,sex,age,install_app,unstaill_time,creative_num,auto_openid_flag)
		value
		(#{temp_id},#{temp_name},#{accounts},#{extension_type},#{delivery_mode},#{day_limit},#{campaign_day_budget},#{campaign_name},#{pacing_status},#{campaign_status},
		#{group_name},#{extension_flow},#{flow_scene},#{oppo_appid},#{advertise_type},#{time_set},#{time_limit},#{begin_time},#{endTime},#{is_day_allocation},
		#{billing_type},#{price},#{ocpc_price},#{ocpc_type},#{deep_ocpc_price},#{deep_ocpc_type},#{group_num},#{default_second_stage},
		#{page_type},#{page_id},#{page_url},#{ad_name},#{click_url},#{brand_name},#{content},#{create_owner},#{update_owner},#{region},#{sex},#{age},#{install_app}
		,#{unstaill_time},#{creative_num},#{auto_openid_flag})
	</insert>

    <insert id="insertOppoBatchCreative">
        insert into dn_oppo_batch_creative_set(temp_id,global_spec_name,global_spec_id,signature,url,cover_signature,cover_url,type,button_txt,logo_signature,logo_url)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.temp_id},#{item.global_spec_name},#{item.global_spec_id},#{item.signature},#{item.url},#{item.cover_signature},#{item.cover_url},#{item.type},#{item.button_txt},#{item.logo_signature},#{item.logo_url})
        </foreach>
    </insert>

    <update id="updateOppoBatchTemplate">
		update dn_oppo_batch_create_template set
		accounts = #{accounts},
		temp_name = #{temp_name},
		extension_type = #{extension_type},
		delivery_mode = #{delivery_mode},
		day_limit = #{day_limit},
		campaign_day_budget = #{campaign_day_budget},
		campaign_name = #{campaign_name},
		pacing_status = #{pacing_status},
		campaign_status = #{campaign_status},
		group_name = #{group_name},
		extension_flow = #{extension_flow},
		flow_scene = #{flow_scene},
		oppo_appid = #{oppo_appid},
		advertise_type = #{advertise_type},
		time_set = #{time_set},
		time_limit = #{time_limit},
		begin_time = #{begin_time},
		endTime = #{endTime},
		is_day_allocation = #{is_day_allocation},
		billing_type = #{billing_type},
		price = #{price},
		ocpc_price = #{ocpc_price},
		ocpc_type = #{ocpc_type},
		deep_ocpc_price = #{deep_ocpc_price},
		deep_ocpc_type = #{deep_ocpc_type},
		group_num = #{group_num},
		default_second_stage = #{default_second_stage},
		page_type = #{page_type},
		page_id = #{page_id},
		page_url = #{page_url},
		ad_name = #{ad_name},
		click_url = #{click_url},
		brand_name = #{brand_name},
		content = #{content},
		create_owner = #{create_owner},
		update_owner = #{update_owner},
		region = #{region},
		sex = #{sex},
		age = #{age},
		install_app = #{install_app},
		unstaill_time = #{unstaill_time},
		creative_num = #{creative_num},
		auto_openid_flag = #{auto_openid_flag}
		where temp_id = #{temp_id}
	</update>

    <delete id="deleteOppoBatchCreative">
		delete from dn_oppo_batch_creative_set where temp_id = #{temp_id}
	</delete>

    <delete id="deleteOppoBatchTemplate">
		delete from dn_oppo_batch_create_template where temp_id = #{temp_id} limit 1
	</delete>

</mapper>