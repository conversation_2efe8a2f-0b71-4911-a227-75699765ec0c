<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.tfxt.BudgetWarningConfigMapper">

    <select id="selectBudgetWarningConfigByHour"
            resultType="com.wbgame.pojo.budgetWarning.BudgetWarningConfig">
        select * from adt_user_budget_warning_config
        where media = #{media} and enable = 1
        and trigger_start &lt;= #{hour} and trigger_end > #{hour}
    </select>
    <select id="selectAllStrategies" resultType="java.util.Map">
        select * from adt_user_budget_warning_strategy
    </select>

    <select id="getBudgetWarningIndex" resultType="com.wbgame.pojo.jettison.vo.BudgetWarningIndexVo">
        select id,index_name,index_id,table_id,unity from adt_user_budget_warning_index
        where 1=1
        <if test="table != null and table != ''">
            and table_id = #{table}
        </if>
    </select>

    <select id="selectBudgetWarningStrategy" resultType="com.wbgame.pojo.jettison.param.BudgetWarningStrategyVo">
        select id,name,media,strategy,create_user,create_time,update_user,update_time from adt_user_budget_warning_strategy
        where 1=1
        <if test="id != null and id != ''">
            and id = #{id}
        </if>
        <if test="name != null and name != ''">
        and name like  concat('%',#{name},'%')
        </if>
        <if test="exist_name != null and exist_name != ''">
            and name = #{exist_name}
        </if>
        <if test="media != null and media != ''">
            and media = #{media}
        </if>
        <if test="create_user != null and create_user != ''">
            and create_user like  concat('%',#{create_user},'%')
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>

    <insert id="addBudgetWarningStrategy">
        insert into adt_user_budget_warning_strategy(name,strategy,media,create_user,update_user)
        values
        (#{name},#{strategy},#{media},#{create_user},#{update_user})
    </insert>

    <update id="updateBudgetWarningStrategy">
        update adt_user_budget_warning_strategy set
        strategy = #{strategy},
        update_user = #{update_user},
        name = #{name}
        where id = #{id}
    </update>

    <select id="countBudgetWarningConfigByStrategyId" resultType="java.lang.Long">
        select count(1) from adt_user_budget_warning_config where strategy_id = #{strategy_id}
    </select>

    <delete id="deleteBudgetWarningStrategy">
        delete from adt_user_budget_warning_strategy where id = #{id} limit 1
    </delete>

    <select id="selectBudgetWarningConfig" resultType="com.wbgame.pojo.jettison.param.BudgetWarningConfigVo">
        select * from
        (select a.id,a.media,warning_user,dimention,dimention_value,group_by_field,strategy_id,trigger_start,trigger_end
        ,trigger_number,enable,a.create_user,a.create_time,a.update_user,a.update_time,b.name strategy_name,strategy,warning_number
        from adt_user_budget_warning_config a
        left join adt_user_budget_warning_strategy b on a.strategy_id = b.id
        where 1=1
        <if test="media != null and media != ''">
            and a.media = #{media}
        </if>
        <if test="create_user != null and create_user != ''">
            and a.create_user like concat('%',#{create_user},'%')
        </if>
        <if test="strategy_id != null and strategy_id != ''">
            and strategy_id in (${strategy_id})
        </if>
        <if test="enable != null">
            and enable = #{enable}
        </if>
        <if test="group_by_field != null and group_by_field != ''">
            and group_by_field = #{group_by_field}
        </if>
        <if test="warning_user_list != null and warning_user_list.size > 0">
            AND (
            <foreach collection="warning_user_list" separator="or" item="item">
                warning_user like "%"#{item}"%"
            </foreach>
            )
        </if>) a
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>

    <insert id="addBudgetWarningConfig">
        insert into adt_user_budget_warning_config(media,warning_user,dimention,dimention_value,group_by_field,strategy_id
        ,trigger_start,trigger_end,trigger_number,enable,create_user,update_user,warning_number)
        values
        (#{media},#{warning_user},#{dimention},#{dimention_value},#{group_by_field},#{strategy_id},#{trigger_start},#{trigger_end}
        ,#{trigger_number},#{enable},#{create_user},#{update_user},#{warning_number})
    </insert>

    <update id="updateBudgetWarningConfig">
        update adt_user_budget_warning_config set
        warning_user = #{warning_user},
        dimention = #{dimention},
        dimention_value = #{dimention_value},
        group_by_field = #{group_by_field},
        strategy_id = #{strategy_id},
        trigger_start = #{trigger_start},
        trigger_end = #{trigger_end},
        trigger_number = #{trigger_number},
        enable = #{enable},
        update_user = #{update_user},
        warning_number = #{warning_number}
        where id = #{id}
    </update>

    <delete id="deleteBudgetWarningConfig">
        delete from adt_user_budget_warning_config where id = #{id} limit 1
    </delete>
</mapper>