<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.tfxt.EmptyAccountLogMapper">

	<select id="selectEmptyAccountLog" resultType="com.wbgame.pojo.jettison.vo.EmptyAccountLogVo">
		select task_id,account,ad_platform,if(type = 1,'修改日期','修改时段') 'type',`status`,message,create_owner,create_time
		,ifnull(project_num,0) project_num,ifnull(success_project_num,0) success_project_num,ifnull(error_project_num,0) error_project_num
		from dn_empty_account_log a left join (
		select task_id p_taks_id,account p_account,count(case when status = 1 then project_id end) success_project_num
		,count(case when status = 0 then project_id end) error_project_num
		from dn_empty_project_log GROUP BY task_id,account
		) b on a.task_id = b.p_taks_id  and a.account = b.p_account
		where 1=1
		<if test="account != null and account !=''">
			and account in (${account})
		</if>
		<if test="create_owner != null and create_owner !=''">
			and create_owner = #{create_owner}
		</if>
		<if test="task_id != null and task_id !=''">
			and a.task_id = #{task_id}
		</if>
		<if test="ad_platform != null and ad_platform !=''">
			and ad_platform = #{ad_platform}
		</if>
		<if test="status != null">
			and status = #{status}
		</if>
		<if test="order_str != null and order_Str != ''">
			order by ${order_str}
		</if>
		<if test="order_str == null || order_Str == ''">
			order by create_time desc
		</if>
	</select>

	<select id="selectEmptyProjectLog" resultType="com.wbgame.pojo.jettison.vo.EmptyProjectLogVo">
		select task_id,account,ad_platform,create_owner,project_id,project_task_id,status,message,create_time
		,CASE type
		WHEN 1 THEN '修改日期'
		WHEN 2 THEN '修改时段'
		WHEN 3 THEN '修改状态'
		WHEN 4 THEN '修改出价'
		WHEN 5 THEN '修改深度出价'
		ELSE '未知'
		END AS type
		from dn_empty_project_log
		where 1=1
		<if test="account != null and account != ''" >
			and account in (${account})
		</if>
		<if test="ad_platform != null and ad_platform != ''" >
			and ad_platform = #{ad_platform}
		</if>
		<if test="create_owner != null and create_owner != ''" >
			and create_owner = #{create_owner}
		</if>
		<if test="task_id != null and task_id != ''" >
			and task_id = #{task_id}
		</if>
		<if test="status != null">
			and status = #{status}
		</if>
		<if test="order_str != null and order_Str != ''">
			order by ${order_str}
		</if>
		<if test="order_str == null || order_Str == ''">
			order by create_time desc
		</if>
	</select>

    <select id="selectEmptyAdvertLog" resultType="com.wbgame.pojo.jettison.vo.EmptyAdvertLogVo">
		select task_id,account,ad_platform,create_owner,advert_id,advert_task_id,status,message,create_time
		 ,CASE type
		WHEN 1 THEN '修改日期'
		WHEN 2 THEN '修改时段'
		WHEN 3 THEN '修改状态'
		WHEN 4 THEN '修改出价'
		WHEN 5 THEN '修改深度出价'
		ELSE '未知'
		END AS type
		 from dn_empty_advert_log
		where 1=1
		<if test="account != null and account != ''" >
			and account in (${account})
		</if>
		<if test="ad_platform != null and ad_platform != ''" >
			and ad_platform = #{ad_platform}
		</if>
		<if test="create_owner != null and create_owner != ''" >
			and create_owner = #{create_owner}
		</if>
		<if test="status != null">
			and status = #{status}
		</if>
		<if test="order_str != null and order_Str != ''">
			order by ${order_str}
		</if>
		<if test="order_str == null || order_Str == ''">
			order by create_time desc
		</if>
	</select>

</mapper>