<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.tfxt.TfxtMapper">

	<insert id="insertCsjBaseInfoList" parameterType="java.util.List">
		insert into dn_report_monetization_china(
			app,
			ad_platform,
			account,
			ad_type,
			day,
			placementId,
			requests,
			filled,
			impressions,
			clicks,
			earnings,
			channelType,
			channel,
			media,
			channelMark,
			createTime
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.app},
			#{li.ad_platform},
			#{li.account},
			#{li.ad_type},
			#{li.day},
			#{li.placementId},
			#{li.requests},
			#{li.filled},
			#{li.impressions},
			#{li.clicks},
			#{li.earnings},
			#{li.channelType},
			#{li.channel},
			#{li.media},
			#{li.channelMark},
			now())
		</foreach>

	</insert>
	<insert id="insertCsjBaseLogList" parameterType="java.util.List">
		insert into csj_base_log(
			`code`,
			message,
			app_id,
			app_name,
			stat_time_day,
			imei_check,
			os_type,
			rit_type,
			rit,
			rit_name,
			user_type,
			device_type,
			channel,
			sub_channel,
			req_num,
			send,
			`show`,
			click,
			earnings,
			sr,
			ssr,
			ctr,
			CPM
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.code},
			#{li.message},
			#{li.app_id},
			#{li.app_name},
			#{li.stat_time_day},
			#{li.imei_check},
			#{li.os_type},
			#{li.rit_type},
			#{li.rit},
			#{li.rit_name},
			#{li.user_type},
			#{li.device_type},
			#{li.channel},
			#{li.sub_channel},
			#{li.req_num},
			#{li.send},
			#{li.show},
			#{li.click},
			#{li.earnings},
			#{li.sr},
			#{li.ssr},
			#{li.ctr},
			#{li.CPM})
		</foreach>
	</insert>

	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>


	<insert id="insertDnplacement" parameterType="java.util.List">
		insert into dn_placement(
			adsize,
			ad_sid,
			agent,
			appid,
			appkey,
			`code`,
			creatdate,
			limitname,
			refreshinterval,
			textinfo,
			type,
			dn_appid
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.adsize},
			#{li.ad_sid},
			#{li.agent},
			#{li.appid},
			#{li.appkey},
			#{li.code},
			#{li.creatdate},
			#{li.limitname},
			#{li.refreshinterval},
			#{li.textinfo},
			#{li.type},
			#{li.dn_appid})
		</foreach>
	</insert>

	<insert id="insertDnapp" parameterType="java.util.List">
		insert into dn_app(
			app_id,
			app_key,
			app_name,
			create_time,
			app_category,
			type
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.app_id},
			#{li.app_key},
			#{li.app_name},
			#{li.create_time},
			#{li.app_category},
			#{li.type})
		</foreach>
	</insert>

	<update id="updateDnChannelInfo" parameterType="com.wbgame.pojo.DnChannelInfo" >
    update dn_channel_info
    set cha_type = #{chaType,jdbcType=INTEGER},
      cha_media = #{chaMedia,jdbcType=VARCHAR},
      cha_sub_launch = #{chaSubLaunch,jdbcType=VARCHAR},
      cha_id_byte = #{chaIdByte,jdbcType=INTEGER},
      cha_name_byte = #{chaNameByte,jdbcType=VARCHAR},
      cha_sub_byte = #{chaSubByte,jdbcType=VARCHAR},
      cha_sub_name_byte = #{chaSubNameByte,jdbcType=VARCHAR},
      <!-- cha_ratio = #{chaRatio,jdbcType=VARCHAR}, -->
      cha_remark = #{chaRemark,jdbcType=VARCHAR},
	  ry_cha = #{ry_cha},
	  dn_bus_model_id = #{dn_bus_model_id,jdbcType=INTEGER}
    where cha_id = #{chaId,jdbcType=VARCHAR}
  </update>

	<insert id="insertDnChannelInfo" parameterType="com.wbgame.pojo.DnChannelInfo" >
    insert into dn_channel_info (cha_id, cha_type, cha_media,
      cha_sub_launch, cha_id_byte, cha_name_byte,
      cha_sub_byte, cha_sub_name_byte, cha_remark,dn_bus_model_id,ry_cha
      <!-- ,cha_ratio -->
      )
    values (#{chaId,jdbcType=VARCHAR}, #{chaType,jdbcType=INTEGER}, #{chaMedia,jdbcType=VARCHAR},
      #{chaSubLaunch,jdbcType=VARCHAR}, #{chaIdByte,jdbcType=INTEGER}, #{chaNameByte,jdbcType=VARCHAR},
      #{chaSubByte,jdbcType=VARCHAR}, #{chaSubNameByte,jdbcType=VARCHAR}, #{chaRemark,jdbcType=VARCHAR}
      ,#{dn_bus_model_id,jdbcType=INTEGER},#{ry_cha}
      <!-- , #{chaRatio,jdbcType=VARCHAR} -->
      )
  </insert>

	<select id="selectDnGroup" resultType="com.wbgame.pojo.DnChannelVo" >
	 select CAST(id as char(20)) id , groupName from dn_group a where a.enabled = 1	
	</select>

	<select id="selectDnGroupApp" resultType="com.wbgame.pojo.DnChannelVo" parameterType="String" >
	 SELECT CAST(a.appId as char(20)) appId  ,b.app_name appName from dn_group_app a left JOIN dn_app b on a.appId = b.app_id
	  <if test="groupId != null and groupId != ''" >
    	 where a.groupId in (${groupId})
     </if>
     </select>

	<select id="getPlatformRegAndDauDataList" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.PlatformRegAndDauDataVo">
		select * from  dn_adt.dn_report_operation where 1=1
		<if test="ad_platform != null and ad_platform != ''">
			and ad_platform = #{ad_platform}
		</if>
		and `day` <![CDATA[>=]]> #{startTime} and `day` <![CDATA[<=]]> #{endTime}
	</select>

	<select id="getAllApp" resultType="com.wbgame.pojo.jettison.report.dto.AppDTO">
		SELECT app_id, app_name, `type`, app_category FROM dn_app
		<if test="appList != null and appList.size > 0">
			WHERE app_id IN
			<foreach collection="appList" item="app" open="(" separator="," close=")">
				#{app}
			</foreach>
		</if>
		ORDER BY create_time DESC
	</select>

	<select id="getAccountSpendReport" resultType="com.wbgame.pojo.jettison.report.dto.AccountSpendReportDTO">
		SELECT SUM(spend) AS spend, SUM(rebateSpend) AS rebateSpend, SUM(installs) AS installs,
		       SUM(cashSpend) cashSpend,SUM(cashRebateSpend) cashRebateSpend,
		       SUM(virtualSpend) virtualSpend, SUM(virtualRebateSpend) virtualRebateSpend
		,sum(frameSpend) frameSpend,sum(paySpend) paySpend,SUM(frameRebateSpend) frameRebateSpend,sum(payRebateSpend) payRebateSpend
		,sum(payRecAmount) payRecAmount,(sum(rebateSpend) - sum(payRebateSpend)) realSpend,sum(kzz_cost) kzz_cost
		,sum(d.cash_balance) cash_balance,sum(d.not_cash_balance) not_cash_balance,sum(d.balance) balance,sum(d.kzz_balance) kzz_balance
		,sum(exchange_cost) exchange_cost,sum(star_cost) star_cost,sum(rebate_cost) rebate_cost,sum(gift_cost) gift_cost
		,ifnull(sum(cash_in),0) cash_in,ifnull(sum(rebate_in),0) rebate_in,ifnull(sum(gift_in),0) gift_in
		,ifnull(sum(cash_out),0) cash_out,ifnull(sum(rebate_out),0) rebate_out,ifnull(sum(gift_out),0) gift_out
		,concat(round(ifnull(sum(virtualSpend)/sum(spend)*100,0),2),'%') virtual_rate
		<if test="group != null and group.size > 0">
			,
			<foreach collection="group" index="index" item="item" separator=",">
				`${item}`
			</foreach>
		</if>
		FROM dn_report_spend_account_china c
		LEFT JOIN (SELECT app_id id,app_category AS appCategory FROM dn_app) a ON a.id = c.app
		left join (select account hw_acc, case when isCPD=1 then '商店' else '非商店' end as isCPD from dn_huawei_account_spend) hwa on c.account = hwa.hw_acc
		left join
		(select a.day a_day,a.account a_account,cash_balance,kzz_balance,not_cash_balance,balance from
		(select day,account,cash_balance,not_cash_balance,kzz_balance,(cash_balance+not_cash_balance+ifnull(kzz_balance,0)) balance from dn_report_spend_account_china
		<include refid="getAccountReportCondition1"/>
		group by account,day) a
		inner join
		(select account,max(day) day from dn_report_spend_account_china
		<include refid="getAccountReportCondition1"/>
		<choose>
			<when test="group.contains('day')">
				group by account,day
			</when>
			<otherwise>
				group by account
			</otherwise>
		</choose>
		) b
		on a.account = b.account and a.day = b.day
		) d on c.day = d.a_day and c.account = d.a_account
		left join yyhz_0308.dn_account_transfer_log e
		on c.day = e.tf_day and c.account = e.tf_account and c.media = e.tf_platform and c.channel is not null
		<include refid="getAccountReportConditionCPD1"/>
		<if test="appCategory != null and appCategory.size > 0">
			AND appCategory IN
			<foreach collection="appCategory" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<include refid="getAccountReportCondition2"/>
	</select>

	<select id="getAccountSpendReportSummary" resultType="com.wbgame.pojo.jettison.report.dto.AccountSpendReportDTO">
		SELECT SUM(spend) AS spend, SUM(rebateSpend) AS rebateSpend, SUM(installs) AS installs,
		SUM(cashSpend) cashSpend,SUM(cashRebateSpend) cashRebateSpend,
		SUM(virtualSpend) virtualSpend, SUM(virtualRebateSpend) virtualRebateSpend
		,sum(frameSpend) frameSpend,sum(paySpend) paySpend,SUM(frameRebateSpend) frameRebateSpend,sum(payRebateSpend) payRebateSpend
		,sum(payRecAmount) payRecAmount,(sum(rebateSpend) - sum(payRebateSpend)) realSpend,sum(kzz_cost) kzz_cost
		,sum(d.cash_balance) cash_balance,sum(d.not_cash_balance) not_cash_balance,sum(d.balance) balance,sum(d.kzz_balance) kzz_balance
		,sum(exchange_cost) exchange_cost,sum(star_cost) star_cost,sum(rebate_cost) rebate_cost,sum(gift_cost) gift_cost
		,ifnull(sum(cash_in),0) cash_in,ifnull(sum(rebate_in),0) rebate_in,ifnull(sum(gift_in),0) gift_in
		,ifnull(sum(cash_out),0) cash_out,ifnull(sum(rebate_out),0) rebate_out,ifnull(sum(gift_out),0) gift_out
		,concat(round(ifnull(sum(virtualSpend)/sum(spend)*100,0),2),'%') virtual_rate
		FROM dn_report_spend_account_china c
		LEFT JOIN (SELECT app_id id,app_category AS appCategory FROM dn_app) a ON a.id = c.app
		left join (select account hw_acc, case when isCPD=1 then '商店' else '非商店' end as isCPD from dn_huawei_account_spend) hwa on c.account = hwa.hw_acc
		left join
		(select a.day a_day,a.account a_account,cash_balance,not_cash_balance,kzz_balance,balance from
		(select day,account,cash_balance,not_cash_balance,kzz_balance,(cash_balance+not_cash_balance+ifnull(kzz_balance,0)) balance from dn_report_spend_account_china
		<include refid="getAccountReportCondition1"/>
		group by account,day) a
		inner join
		(select account,max(day) day from dn_report_spend_account_china
		<include refid="getAccountReportCondition1"/>
		group by account
		) b
		on a.account = b.account and a.day = b.day
		) d on c.day = d.a_day and c.account = d.a_account
		left join yyhz_0308.dn_account_transfer_log e
		on c.day = e.tf_day and c.account = e.tf_account and c.media = e.tf_platform and c.channel is not null
		<include refid="getAccountReportConditionCPD1"/>
		<if test="appCategory != null and appCategory.size > 0">
			AND appCategory IN
			<foreach collection="appCategory" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="getAddUserReport" resultType="com.wbgame.pojo.jettison.report.dto.AddusersReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.AddUserReportParam">
		SELECT SUM(add_num) AS add_num, SUM(um_add_num) AS um_add_num, SUM(ry_add_num) AS ry_add_num
		<if test="group != null and group.size > 0">
			,
			<foreach collection="group" index="index" item="item" separator=",">
				`${item}`
			</foreach>
		</if>
		FROM dn_report_adduser
		<include refid="getAddUserReportCondition1"/>
		<include refid="getAddUserReportCondition2"/>
	</select>

	<select id="getAddUserSummary" resultType="com.wbgame.pojo.jettison.report.dto.AddusersReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.AddUserReportParam">
		SELECT SUM(add_num) AS add_num, SUM(um_add_num) AS um_add_num, SUM(ry_add_num) AS ry_add_num
		FROM dn_report_adduser
		<include refid="getAddUserReportCondition1"/>
	</select>

	<select id="getMonetizationReport" resultType="com.wbgame.pojo.jettison.report.dto.MonetizationReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.MonetizationReportParam">
		SELECT SUM(dau) AS dau, SUM(installs) AS installs, SUM(revenue) AS revenue,
		SUM(pv_video) AS pv_video, SUM(pv_plaque) AS pv_plaque, SUM(pv_banner) AS pv_banner, SUM(pv_splash) AS
		pv_splash, SUM(pv_msg) AS pv_msg,
		SUM(revenue_video) AS revenue_video, SUM(revenue_plaque) AS revenue_plaque, SUM(revenue_banner) AS
		revenue_banner, SUM(revenue_splash) AS revenue_splash, SUM(revenue_msg) AS revenue_msg,
		<if test="group2 != null and group2 != ''">
			CONCAT(${group2}) mapkey,
		</if>
		sec_TO_time(cast(avg(TIME_TO_SEC(duration)) as SIGNED)) AS duration
		<if test="group != null and group.size > 0">
			,
			<foreach collection="group" index="index" item="item" separator=",">
				`${item}`
			</foreach>
		</if>
		FROM dn_report_monetization_summary_china
		<include refid="getMonetizationReportCondition1"/>
		<include refid="getMonetizationReportCondition2"/>
	</select>

	<select id="getMonetizationSummary" resultType="com.wbgame.pojo.jettison.report.dto.MonetizationReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.MonetizationReportParam">
		SELECT SUM(dau) AS dau, SUM(installs) AS installs, SUM(revenue) AS revenue,
		SUM(pv_video) AS pv_video, SUM(pv_plaque) AS pv_plaque, SUM(pv_banner) AS pv_banner, SUM(pv_splash) AS
		pv_splash, SUM(pv_msg) AS pv_msg,
		SUM(revenue_video) AS revenue_video, SUM(revenue_plaque) AS revenue_plaque, SUM(revenue_banner) AS
		revenue_banner, SUM(revenue_splash) AS revenue_splash, SUM(revenue_msg) AS revenue_msg,
		sec_TO_time(cast(avg(TIME_TO_SEC(duration)) as SIGNED)) AS duration
		FROM dn_report_monetization_summary_china
		<include refid="getMonetizationReportCondition1"/>
	</select>

	<select id="getChinaPlatformAccount" resultType="java.util.HashMap">
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_toutiao_account_spend
		WHERE parentAccount IS NOT NULL
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_kuaishou_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_tx_account_spend
		WHERE parentAccount IS NOT NULL
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_mintegral_account
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_oppo_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_baidu_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_aqy_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_huawei_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_xiaomi_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_vivo_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, 1 AS groupId FROM dn_facebook_spend_account
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, 1 AS groupId FROM dn_google_spend_account
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_bzhan_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_spend_account
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_233_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_spend_rongyao_account
	</select>

	<sql id="getAccountReportCondition1">
		WHERE 1 = 1
		<if test="bus_type != null">
			AND bus_type = #{bus_type}
		</if>
		<if test="media != null and media.size > 0">
			AND media IN
			<foreach collection="media" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="type != null and type.size > 0">
			AND `type` IN
			<foreach collection="type" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="agent != null and agent.size > 0">
			AND agent IN
			<foreach collection="agent" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="putUser != null and putUser.size > 0">
			AND putUser IN
			<foreach collection="putUser" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="account != null and account.size > 0">
			AND account IN
			<foreach collection="account" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="app != null and app.size > 0">
			AND app IN
			<foreach collection="app" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="channel != null and channel.size > 0">
			AND channel IN
			<foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="start_date != null and end_date != null">
			AND `day` BETWEEN #{start_date} AND #{end_date}
		</if>
		<if test="app_type != null">
			AND app_type = #{app_type}
		</if>
	</sql>

	<sql id="getAccountReportConditionCPD1">
		WHERE 1 = 1
		<if test="bus_type != null">
			AND bus_type = #{bus_type}
		</if>
		<if test="media != null and media.size > 0">
			AND media IN
			<foreach collection="media" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="type != null and type.size > 0">
			AND `type` IN
			<foreach collection="type" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="agent != null and agent.size > 0">
			AND agent IN
			<foreach collection="agent" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="putUser != null and putUser.size > 0">
			AND putUser IN
			<foreach collection="putUser" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="account != null and account.size > 0">
			AND account IN
			<foreach collection="account" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="app != null and app.size > 0">
			AND app IN
			<foreach collection="app" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="channel != null and channel.size > 0">
			AND channel IN
			<foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="start_date != null and end_date != null">
			AND `day` BETWEEN #{start_date} AND #{end_date}
		</if>
		<if test="app_type != null">
			AND app_type = #{app_type}
		</if>
		<if test='isCPD != null and isCPD != ""'>
		    <choose>
				<when test='isCPD.contains("1")'>
					AND isCPD = '商店'
				</when>
				<otherwise>
					and (isCPD is null or isCPD = '非商店')
				</otherwise>
			</choose>
		</if>
	</sql>

	<sql id="getAccountReportCondition2">
		<if test="group != null and group.size > 0">
			GROUP BY
			<foreach collection="group" index="index" item="item" separator=",">
				`${item}`
			</foreach>
		</if>

		<if test="order_str != null and order_str != ''">
			ORDER BY ${order_str}
		</if>
		<if test="order_str == null or order_str == ''">
			ORDER BY `day` ASC, spend DESC
		</if>
	</sql>

	<sql id="getAddUserReportCondition1">
		WHERE 1 = 1
		<if test="app != null and app.size > 0">
			AND app IN
			<foreach collection="app" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="ad_platform != null and ad_platform.size > 0">
			AND ad_platform IN
			<foreach collection="ad_platform" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="channel != null and channel.size > 0">
			AND channel IN
			<foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="start_date != null and end_date != null">
			AND `day` BETWEEN #{start_date} AND #{end_date}
		</if>
	</sql>

	<sql id="getAddUserReportCondition2">
		<if test="group != null and group.size > 0">
			GROUP BY
			<foreach collection="group" index="index" item="item" separator=",">
				`${item}`
			</foreach>
		</if>
		<if test="order_str != null and order_str != ''">
			ORDER BY ${order_str}
		</if>
		<if test="order_str == null or order_str == ''">
			ORDER BY `day` ASC, add_num DESC
		</if>
	</sql>

	<sql id="getMonetizationReportCondition1">
		WHERE 1 = 1
		<if test="app != null and app.size > 0">
			AND app IN
			<foreach collection="app" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="cha_type_names != null and cha_type_names.size > 0">
			AND cha_type_name IN
			<foreach collection="cha_type_names" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="cha_medias != null and cha_medias.size > 0">
			AND cha_media IN
			<foreach collection="cha_medias" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="cha_ids != null and cha_ids.size > 0">
			AND cha_id IN
			<foreach collection="cha_ids" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="two_app_category != null and two_app_category.size > 0">
			AND app IN
			<foreach collection="two_app_category" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="country != null and country != ''">
			AND country = #{country}
		</if>
		<if test="start_date != null and end_date != null">
			AND `day` BETWEEN #{start_date} AND #{end_date}
		</if>
	</sql>

	<sql id="getMonetizationReportCondition2">
		<if test="group != null and group.size > 0">
			GROUP BY
			<foreach collection="group" index="index" item="item" separator=",">
				`${item}`
			</foreach>
		</if>
		<if test="order_str != null and order_str != ''">
			ORDER BY ${order_str}
		</if>
		<if test="order_str == null or order_str == ''">
			ORDER BY `day` ASC, revenue DESC
		</if>
	</sql>

	<select id="getChinaMonetizationReportCount" resultType="java.lang.Integer">
		SELECT COUNT(1)
		FROM dn_report_monetization_summary_china
		WHERE `day` = #{day}
	</select>

	<delete id="delChinaMonetizationReport">
		DELETE
		FROM dn_report_monetization_summary_china
		WHERE `day` = #{day}
	</delete>

	<insert id="batchAddChinaMonetizationReport" parameterType="com.wbgame.pojo.adv2.reportEntity.ChinaMonetizationReport">
		INSERT INTO dn_report_monetization_summary_china
		(`day`, app, cha_id, cha_type_name, cha_media, cha_sub_launch, duration, dau, installs, revenue, pv_video,
		pv_plaque, pv_banner, pv_splash, pv_msg, revenue_video, revenue_plaque, revenue_banner, revenue_splash,
		revenue_msg,country)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.day}, #{item.app}, #{item.cha_id}, #{item.cha_type_name}, #{item.cha_media}, #{item.cha_sub_launch},
			#{item.duration},#{item.dau}, #{item.installs},
			#{item.revenue}, #{item.pv_video}, #{item.pv_plaque},#{item.pv_banner},
			#{item.pv_splash}, #{item.pv_msg}, #{item.revenue_video}, #{item.revenue_plaque},
			#{item.revenue_banner}, #{item.revenue_splash}, #{item.revenue_msg},#{item.country}
			)
		</foreach>
	</insert>

	<select id="selectTfAccount2" resultType="java.util.Map">
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_toutiao_account_spend
		WHERE parentAccount IS NOT NULL and account like concat('%',#{account},'%')
		UNION
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_kuaishou_account_spend
		where account like concat('%',#{account},'%')
		UNION
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_tx_account_spend
		WHERE parentAccount IS NOT NULL and account like concat('%',#{account},'%')
		UNION
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_mintegral_account
		where account like concat('%',#{account},'%')
		UNION
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_oppo_account_spend
		where account like concat('%',#{account},'%')
		UNION
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_baidu_account_spend
		where account like concat('%',#{account},'%')
		UNION
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_aqy_account_spend
		where account like concat('%',#{account},'%')
		UNION
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_huawei_account_spend
		where account like concat('%',#{account},'%')
		UNION
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_xiaomi_account_spend
		where account like concat('%',#{account},'%')
		UNION
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_vivo_account_spend
		where account like concat('%',#{account},'%')
		UNION
		SELECT account, IFNULL(remark, '备注') AS remark, 1 AS groupId FROM dn_facebook_spend_account
		where account like concat('%',#{account},'%')
		UNION
		SELECT account, IFNULL(remark, '备注') AS remark, 1 AS groupId FROM dn_google_spend_account
		where account like concat('%',#{account},'%')
		UNION
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_bzhan_account_spend
		where account like concat('%',#{account},'%')
		UNION
		SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_spend_account
		where account like concat('%',#{account},'%')
	</select>

    <select id="selectCreativeTemplate" resultType="java.util.Map">
		select template_id,template_name from dn_creative_template
	</select>

	<select id="getChannelAccountSpendReportSum" resultType="com.wbgame.pojo.finance.FinanceReport">
		select
		   	sum(cash_cost) cash_cost,
			sum(cost) cost,
			sum(reward_cost) reward_cost,
			sum(cash_cost_rebate) cash_cost_rebate,
			sum(cost_rebate) cost_rebate,
			sum(reward_cost_rebate) reward_cost_rebate,
			sum(income) income,
			sum(transfer_in) transfer_in,
			sum(contract_rebate_real_charged) contract_rebate_real_charged,
			sum(contract_rebate_real_charged_rebate) contract_rebate_real_charged_rebate,
			sum(contract_rebate_real_recharged) contract_rebate_real_recharged,
			sum(cash_balance) cash_balance,
			sum(not_cash_balance) not_cash_balance,
			sum(exchange_cost) exchange_cost,
			sum(star_cost) star_cost,
			sum(rebate_cost) rebate_cost,
			sum(gift_cost) gift_cost,
			sum(virtualSpend) virtualSpend,
			sum(virtualSpend_rebate) virtualSpend_rebate,
			sum(spend) spend,
			sum(spend_rebate) spend_rebate,
			sum(installs) installs,
			sum(appstore_cost) appstore_cost,
			sum(feed_cost) feed_cost,
			sum(league_cost) league_cost,
			sum(general_cost) general_cost
		from
		(<include refid="channelAccountSpendReport"/>) a
	</select>
	<select id="getChannelAccountSpendReport" resultType="com.wbgame.pojo.finance.FinanceReport">
		<include refid="channelAccountSpendReport"/>
	</select>
    <sql id="channelAccountSpendReport">
		SELECT
			id,
			sum(cash_cost) cash_cost,
			sum(cost) cost,
			sum(reward_cost) reward_cost,
			sum(cash_cost)/rebate cash_cost_rebate,
			sum(cost)/rebate cost_rebate,
			sum(reward_cost)/rebate reward_cost_rebate,
			sum(income) income,
			sum(transfer_in) transfer_in,
			sum(contract_rebate_real_charged) contract_rebate_real_charged,
			sum(contract_rebate_real_charged)/rebate contract_rebate_real_charged_rebate,
			sum(contract_rebate_real_recharged) contract_rebate_real_recharged,
			<if test="group != null and group.size > 0">
				<foreach collection="group" index="index" item="item" separator=",">
					`${item}`
				</foreach>
			</if>
		       ,
			sum(cash_balance) cash_balance,
			sum(not_cash_balance) not_cash_balance,
			sum(exchange_cost) exchange_cost,
			sum(star_cost) star_cost,
			sum(rebate_cost) rebate_cost,
			sum(gift_cost) gift_cost,
			sum(installs) installs,
			sum(virtualSpend) virtualSpend,
			sum(virtualSpend) /rebate virtualSpend_rebate,
			sum(spend) spend,
			sum(spend)/rebate spend_rebate,
			rebate,
			sum(appstore_cost) appstore_cost,
			sum(feed_cost) feed_cost,
			sum(league_cost) league_cost,
			sum(general_cost) general_cost
		FROM
			`dn_channel_report_finance`
		<include refid="getAccountReportCondition1"/>
		<include refid="getAccountReportCondition2"/>
	</sql>

	<select id="selectAccountType" resultType="java.util.Map">
		select * from dn_account_type
	</select>
	<select id="getPlatformAccounts" resultType="java.util.Map">
		SELECT * FROM
	    (
			SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_toutiao_account_spend
			WHERE parentAccount IS NOT NULL
			UNION ALL
			SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_kuaishou_account_spend
			UNION ALL
			SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_tx_account_spend
			WHERE parentAccount IS NOT NULL
			UNION ALL
			SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_mintegral_account
			UNION ALL
			SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_oppo_account_spend
			UNION ALL
			SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_baidu_account_spend
			UNION ALL
			SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_aqy_account_spend
			UNION ALL
			SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_huawei_account_spend
			UNION ALL
			SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_xiaomi_account_spend
			UNION ALL
			SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_vivo_account_spend
			UNION ALL
			SELECT account, IFNULL(remark, '备注') AS remark, 1 AS groupId FROM dn_facebook_spend_account
			UNION ALL
			SELECT account, IFNULL(remark, '备注') AS remark, 1 AS groupId FROM dn_google_spend_account
			UNION ALL
			SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_bzhan_account_spend
			UNION ALL
			SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dn_spend_account
	    ) temp_table
		<where>
			<if test="accountList != null and accountList.size() > 0">
				account IN <foreach collection="accountList" item="account" open="(" close=")" separator=",">#{account}</foreach>
			</if>
			<if test="accountRemark != null and accountRemark != ''">
				and remark like concat('%',#{accountRemark},'%')
			</if>
		</where>

	</select>

	<select id="selectAccountsByGroup" resultType="java.lang.String">
		SELECT account FROM
		(
		SELECT account, IFNULL(remark, '备注') AS remark, groupId,putUser,media FROM dn_toutiao_account_spend
		WHERE parentAccount IS NOT NULL
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId,putUser,media FROM dn_kuaishou_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId,putUser,media FROM dn_tx_account_spend
		WHERE parentAccount IS NOT NULL
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId,putUser,media FROM dn_mintegral_account
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId,putUser,media FROM dn_oppo_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId,putUser,media FROM dn_baidu_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId,putUser,media FROM dn_aqy_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId,putUser,media FROM dn_huawei_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId,putUser,media FROM dn_xiaomi_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId,putUser,media FROM dn_vivo_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, 1 AS groupId,putUser,media FROM dn_facebook_spend_account
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, 1 AS groupId,putUser,media FROM dn_google_spend_account
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId,putUser,media FROM dn_bzhan_account_spend
		UNION ALL
		SELECT account, IFNULL(remark, '备注') AS remark, groupId,putUser,media FROM dn_spend_account
		) temp_table
		<where> groupId in <foreach collection="account_group" item="item" open="(" close=")" separator=",">#{item}</foreach>
			<if test="putUser != null and putUser.size() > 0">
				and putUser IN <foreach collection="putUser" item="item" open="(" close=")" separator=",">#{item}</foreach>
			</if>
			<if test="media != null and media.size() > 0">
				and media IN <foreach collection="media" item="item" open="(" close=")" separator=",">#{item}</foreach>
			</if>

		</where>
	</select>

</mapper>