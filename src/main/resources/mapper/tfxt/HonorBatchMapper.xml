<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.tfxt.HonorBatchMapper">


	<insert id="insertRule">
		insert into dn_honor_batch_rule(promotion_purpose,traffic_type,ad_placement_id,ad_placement_name,ad_creative_spec_id,ad_creative_spec_name,
		wide,height,size,format,material_type,cover_size,cover_wide,cover_height,cover_format,logo_size,logo_wide,logo_height,logo_format,required)
		values
		(0,#{traffic_type},#{ad_placement_id},#{ad_placement_name},#{ad_creative_spec_id},#{ad_creative_spec_name},#{wide},#{height}
		,#{size},#{format},#{material_type},#{cover_size},#{cover_wide},#{cover_height},#{cover_format},#{logo_size},#{logo_wide},#{logo_height}
		,#{logo_format},#{required})
	</insert>

	<select id="selectHonorAccounts" resultType="java.lang.String">
		select account from dn_spend_rongyao_account
	</select>

	<select id="getHonorAppids" resultType="com.alibaba.fastjson.JSONObject">
		select tappid,tappname from yyhz_0308.adv_platform_app_info where platform = 'honor' group by tappid
	</select>

	<select id="getHonorCity" resultType="com.alibaba.fastjson.JSONObject">
		select id,parent_id,name,code from dn_honor_batch_city where
		<if test="grade != 1">
			parent_id != 0
		</if>
		<if test="grade == 1">
			parent_id = 0
		</if>
	</select>

	<select id="getHonorCategory" resultType="com.alibaba.fastjson.JSONObject">
		select * from dn_honor_batch_category
	</select>

    <select id="selectVivoBatchTemplateList" resultType="com.wbgame.pojo.jettison.vo.HonorBatchTemplateVo">
		select a.temp_id,a.accounts,temp_name,if(traffic_type = 0,'推荐','搜索') traffic_type,
		b.ad_placement_name as adPlacementId,create_owner,update_time,concat(start_time,"至",end_time) tf_date
		from dn_honor_batch_create_template a left join
		(select ad_placement_id,ad_placement_name from dn_honor_batch_rule group by ad_placement_id) b
		on a.adPlacementId = b.ad_placement_id
		where 1=1
		<if test="temp_name != null and temp_name !=''">
			and temp_name like concat('%',#{temp_name},'%')
		</if>
		<if test="traffic_type != null and traffic_type !=''">
			and traffic_type = #{traffic_type}
		</if>
		<if test="create_owner != null and create_owner !=''">
			and create_owner = #{create_owner}
		</if>
		<if test="adPlacementId != null and adPlacementId !=''">
			and adPlacementId in (${adPlacementId})
		</if>
		<if test="accounts != null and accounts !=''">
			and accounts in (${accounts})
		</if>
		group by temp_id
		order by update_time desc
	</select>

    <select id="selectHonorBatchTemplateDetail"
            resultType="com.wbgame.pojo.jettison.param.HonorBatchTemplateParam">
		select temp_id,temp_name,accounts,campaign_name,campaign_daily_budget,campaign_next_budget,promotion_purpose
		,start_time,end_time,install_pkg_type,pkg_src,honor_appid,group_num,group_name,pay_type,traffic_type,adPlacementId
		,group_daily_budget,group_next_budget,delivery_time,ad_bid,conv_type,ocpx_sbp,ocpx_deep_price,ocpx_deep_type,click_url
		,bind_key_words,target_province,target_city,target_gender,target_age,target_traffic,oaid_dto,target_app,creative_num
		,content,ad_name,industry1,industry2,brand_name,button,page_url,page_id
		from dn_honor_batch_create_template where temp_id = #{temp_id} limit 1
	</select>

	<select id="selectHonorBatchCreatives" resultType="com.wbgame.pojo.jettison.vo.HonorBatchCreativeVo">
		select temp_id,ad_creative_spec_id,signature,url,cover_signature,cover_url,logo_signature,logo_url,type
		from dn_honor_batch_creative_set where temp_id = #{temp_id}
	</select>

	<insert id="insertHonorBatchTemplate">
        insert into dn_honor_batch_create_template(temp_id,temp_name,accounts,campaign_name,campaign_daily_budget,campaign_next_budget,promotion_purpose
		,start_time,end_time,install_pkg_type,pkg_src,honor_appid,group_num,group_name,pay_type,traffic_type,adPlacementId
		,group_daily_budget,group_next_budget,delivery_time,ad_bid,conv_type,ocpx_sbp,ocpx_deep_price,ocpx_deep_type,click_url
		,bind_key_words,target_province,target_city,target_gender,target_age,target_traffic,oaid_dto,target_app,creative_num
		,content,ad_name,industry1,industry2,brand_name,button,page_url,page_id,create_owner)
		values
		(#{temp_id},#{temp_name},#{accounts},#{campaign_name},#{campaign_daily_budget},#{campaign_next_budget},#{promotion_purpose},#{start_time},
		#{end_time},#{install_pkg_type},#{pkg_src},#{honor_appid},#{group_num},#{group_name},#{pay_type},#{traffic_type},#{adPlacementId},
		#{group_daily_budget},#{group_next_budget},#{delivery_time},#{ad_bid},#{conv_type},#{ocpx_sbp},#{ocpx_deep_price},#{ocpx_deep_type},
		#{click_url},#{bind_key_words},#{target_province},#{target_city},#{target_gender},#{target_age},#{target_traffic},#{oaid_dto},#{target_app},
		#{creative_num},#{content},#{ad_name},#{industry1},#{industry2},#{brand_name},#{button},#{page_url},#{page_id},#{create_owner})
	</insert>

	<insert id="insertHonorBatchCreative">
		insert into dn_honor_batch_creative_set(temp_id,ad_creative_spec_id,signature,url,cover_signature,cover_url,logo_signature,logo_url,type)
		values
		<foreach collection="list" item="item" separator=",">
			(#{item.temp_id},#{item.ad_creative_spec_id},#{item.signature},#{item.url},#{item.cover_signature},#{item.cover_url},
			#{item.logo_signature},#{item.logo_url},#{item.type})
		</foreach>
	</insert>

	<update id="updateHonorBatchTemplate">
          update dn_honor_batch_create_template set
          	temp_name = #{temp_name},
			accounts = #{accounts},
			campaign_name = #{campaign_name},
			campaign_daily_budget = #{campaign_daily_budget},
			campaign_next_budget = #{campaign_next_budget},
			promotion_purpose = #{promotion_purpose},
			start_time = #{start_time},
			end_time = #{end_time},
			install_pkg_type = #{install_pkg_type},
			pkg_src = #{pkg_src},
			honor_appid = #{honor_appid},
			group_num = #{group_num},
			group_name = #{group_name},
			pay_type = #{pay_type},
			traffic_type = #{traffic_type},
			adPlacementId = #{adPlacementId},
			group_daily_budget = #{group_daily_budget},
			group_next_budget = #{group_next_budget},
			delivery_time = #{delivery_time},
			ad_bid = #{ad_bid},
			conv_type = #{conv_type},
			ocpx_sbp = #{ocpx_sbp},
			ocpx_deep_price = #{ocpx_deep_price},
			ocpx_deep_type = #{ocpx_deep_type},
			click_url = #{click_url},
			bind_key_words = #{bind_key_words},
			target_province = #{target_province},
			target_city = #{target_city},
			target_gender = #{target_gender},
			target_age = #{target_age},
			target_traffic = #{target_traffic},
			oaid_dto = #{oaid_dto},
			target_app = #{target_app},
			creative_num = #{creative_num},
			content = #{content},
			ad_name = #{ad_name},
			industry1 = #{industry1},
			industry2 = #{industry2},
			brand_name = #{brand_name},
			button = #{button},
			page_url = #{page_url},
			page_id = #{page_id}
			where temp_id = #{temp_id}
	</update>

	<delete id="deleteHonorBatchCreative">
            delete from dn_honor_batch_creative_set where temp_id = #{temp_id}
	</delete>

	<delete id="deleteHonorBatchTemplate">
            delete from dn_honor_batch_create_template where temp_id = #{temp_id} limit 1
	</delete>


</mapper>