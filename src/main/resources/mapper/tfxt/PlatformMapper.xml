<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.tfxt.PlatformMapper">

    <insert id="addTXParentAccount" parameterType="com.wbgame.pojo.jettison.report.account.TXAccount">
        INSERT INTO dn_tx_account_spend(account, secret, appId, refreshToken) VALUES
        (#{account}, #{secret}, #{appId}, #{refreshToken})
    </insert>

    <insert id="addAccountBusiness" parameterType="java.lang.String">
        REPLACE INTO dn_account_business ( account, business,createTime)
        VALUES ( #{account}, #{business}, CURRENT_DATE() )
    </insert>

    <insert id="addToutiaoAccount" parameterType="com.wbgame.pojo.jettison.report.account.ToutiaoAccount">
        INSERT IGNORE INTO dn_toutiao_account_spend
        (
        account, accountName, parentAccount, media, `type`, first_agent, agent,
        putUser, rebate, remark, groupId, createUser, business,strategy
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.account}, #{item.accountName}, #{item.parentAccount}, #{item.media}, #{item.type}, #{item.first_agent}, #{item.agent},
            #{item.putUser}, #{item.rebate}, #{item.remark}, #{item.groupId}, #{item.createUser}, #{item.business},#{item.strategy}
            )
        </foreach>
    </insert>

    <insert id="addTXAccount" parameterType="com.wbgame.pojo.jettison.report.account.TXAccount">
        INSERT IGNORE INTO dn_tx_account_spend
        (
        account, accountName, parentAccount, media, `type`, first_agent, agent,
        putUser, rebate, remark, wxAccountId, installType, accountType, groupId, createUser, business,strategy
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.account}, #{item.accountName}, #{item.parentAccount}, #{item.media}, #{item.type}, #{item.first_agent},#{item.agent},
            #{item.putUser}, #{item.rebate}, #{item.remark}, #{item.wxAccountId}, #{item.installType}, #{item.accountType}, #{item.groupId}, #{item.createUser}, #{item.business},
            #{item.strategy}
            )
        </foreach>
    </insert>

    <insert id="addKuaishouAccount" parameterType="com.wbgame.pojo.jettison.report.account.KuaishouAccount">
        INSERT IGNORE INTO dn_kuaishou_account_spend
        (
        account, accountName, media, `type`, first_agent, agent,
        putUser, rebate, remark, secret, appId, groupId, createUser, business,strategy,url
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.account}, #{item.accountName}, #{item.media}, #{item.type}, #{item.first_agent}, #{item.agent},
            #{item.putUser}, #{item.rebate}, #{item.remark}, #{item.secret}, #{item.appId}, #{item.groupId}, #{item.createUser}, #{item.business},
            #{item.strategy},#{item.url}
            )
        </foreach>
    </insert>

    <insert id="addAQYAccount" parameterType="com.wbgame.pojo.jettison.report.account.AQYAccount">
        INSERT IGNORE INTO dn_aqy_account_spend
        (
        account,
        accountName,
        media,
        `type`,
        first_agent,
        agent,
        putUser,
        rebate,
        remark,
        secret,
        appId,
        groupId,
        createUser,
        business
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.account},
            #{item.accountName},
            #{item.media},
            #{item.type},
            #{item.first_agent},
            #{item.agent},
            #{item.putUser},
            #{item.rebate},
            #{item.remark},
            #{item.secret},
            #{item.appId},
            #{item.groupId},
            #{item.createUser},
            #{item.business}
            )
        </foreach>
    </insert>

    <insert id="addBaiduAccount" parameterType="com.wbgame.pojo.jettison.report.account.BaiduAccount">
        INSERT IGNORE INTO dn_baidu_account_spend
        (
        account,
        accountName,
        media,
        `type`,
        first_agent,
        agent,
        putUser,
        rebate,
        remark,
        password,
        token,
        groupId,
        createUser,
        business,
        dataType,
        parent_account
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.account},
            #{item.accountName},
            #{item.media},
            #{item.type},
            #{item.first_agent},
            #{item.agent},
            #{item.putUser},
            #{item.rebate},
            #{item.remark},
            #{item.password},
            #{item.baiduToken},
            #{item.groupId},
            #{item.createUser},
            #{item.business},
            #{item.dataType},
            #{item.parentAccount}
            )
        </foreach>
    </insert>

    <insert id="addOppoAccount" parameterType="com.wbgame.pojo.jettison.report.account.OppoAccount">
        INSERT IGNORE INTO dn_oppo_account_spend
        (
        account,
        accountName,
        media,
        `type`,
        first_agent,
        agent,
        putUser,
        rebate,
        remark,
        api_key,
        api_id,
        groupId,
        createUser,
        business,
        accountSubject
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.account},
            #{item.accountName},
            #{item.media},
            #{item.type},
            #{item.first_agent},
            #{item.agent},
            #{item.putUser},
            #{item.rebate},
            #{item.remark},
            #{item.api_key},
            #{item.api_id},
            #{item.groupId},
            #{item.createUser},
            #{item.business},
            #{item.accountSubject}
            )
        </foreach>
    </insert>

    <insert id="addQttAccount" parameterType="com.wbgame.pojo.jettison.report.account.QttAccount">
        INSERT IGNORE INTO dn_qtt_account_spend
        (
        account,
        appId,
        accountName,
        refreshToken,
        media,
        `type`,
        first_agent,
        agent,
        putUser,
        rebate,
        remark,
        groupId,
        createUser,
        business
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.account},
            #{item.appId},
            #{item.accountName},
            #{item.refreshToken},
            #{item.media},
            #{item.type},
            #{item.first_agent},
            #{item.agent},
            #{item.putUser},
            #{item.rebate},
            #{item.remark},
            #{item.groupId},
            #{item.createUser},
            #{item.business}
            )
        </foreach>
    </insert>

    <insert id="addHuaweiAccount" parameterType="com.wbgame.pojo.jettison.report.account.HuaweiAccount">
        INSERT IGNORE INTO dn_huawei_account_spend
        (
        account,
        accountName,
        client_id,
        client_secret,
        media,
        `type`,
        first_agent,
        agent,
        putUser,
        rebate,
        remark,
        groupId,
        createUser,
        isCPD,
        business,
        package_name,
        accountSubject
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.account},
            #{item.accountName},
            #{item.client_id},
            #{item.client_secret},
            #{item.media},
            #{item.type},
            #{item.first_agent},
            #{item.agent},
            #{item.putUser},
            #{item.rebate},
            #{item.remark},
            #{item.groupId},
            #{item.createUser},
            #{item.isCPD},
            #{item.business},
            #{item.package_name},
            #{item.accountSubject}
            )
        </foreach>
    </insert>

    <insert id="addVivoAccount" parameterType="com.wbgame.pojo.jettison.report.account.VivoAccount">
        INSERT IGNORE INTO dn_vivo_account_spend
        (
        account, client_id, client_secret, `type`, first_agent, agent,
        putUser, rebate, remark, groupId, createUser, business, accountSubject
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.account}, #{item.client_id}, #{item.client_secret}, #{item.type},  #{item.first_agent}, #{item.agent},
            #{item.putUser}, #{item.rebate}, #{item.remark}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.accountSubject}
            )
        </foreach>
    </insert>

    <insert id="addXiaomiAccount" parameterType="com.wbgame.pojo.jettison.report.account.XiaomiSpendAccount">
        INSERT IGNORE INTO dn_xiaomi_account_spend
        (
        account, accountName, signId, secretKey, `type`, first_agent, agent,
        putUser, rebate, remark, groupId, createUser, business, accountSubject
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.account}, #{item.accountName},#{item.signId}, #{item.secretKey}, #{item.type}, #{item.first_agent}, #{item.agent},
            #{item.putUser}, #{item.rebate}, #{item.remark}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.accountSubject}
            )
        </foreach>
    </insert>
    <insert id="addUcAccount">
        INSERT IGNORE INTO dn_spend_uc_account
		(
		account,  password, token, media, `type`, first_agent, agent, putUser,
		rebate, remark, groupId, createUser, business, strategy
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.password}, #{item.token}, #{item.media}, #{item.type}, #{item.first_agent}, #{item.agent}, #{item.putUser},
			#{item.rebate}, #{item.remark}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.strategy}
			)
		</foreach>
    </insert>

    <insert id="addHonorAccount" parameterType="com.wbgame.pojo.jettison.report.account.HuaweiAccount">
        INSERT IGNORE INTO dn_spend_rongyao_account
        (
        account,
        accountName,
        client_id,
        client_secret,
        media,
        `type`,
        first_agent,
        agent,
        putUser,
        rebate,
        remark,
        groupId,
        createUser,
        business,
        accountSubject
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.account},
            #{item.accountName},
            #{item.client_id},
            #{item.client_secret},
            #{item.media},
            #{item.type},
            #{item.first_agent},
            #{item.agent},
            #{item.putUser},
            #{item.rebate},
            #{item.remark},
            #{item.groupId},
            #{item.createUser},
            #{item.business},
            #{item.accountSubject}
            )
        </foreach>
    </insert>
    <update id="updateVivoAccount">
        <foreach collection="list" item="item" separator=";">
            UPDATE dn_vivo_account_spend
            SET
                uuid = #{item.uuid}
            WHERE
                account = #{item.account}
        </foreach>
    </update>
</mapper>