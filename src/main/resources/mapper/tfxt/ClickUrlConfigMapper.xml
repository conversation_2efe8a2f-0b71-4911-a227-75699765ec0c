<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.tfxt.ClickUrlConfigMapper">

	<select id="getClickUrlConfig" resultType="com.wbgame.pojo.jettison.vo.ClickUrlConfigVo">
		select id,ad_platform,key_name,type,click_url,game_name from dn_click_url_config
		where key_name = #{key_name}
		  and type = #{type}
		  and ad_platform = #{ad_platform}
	</select>

	<select id="selectClickUrlConfig" resultType="com.wbgame.pojo.jettison.vo.ClickUrlConfigVo">
		select id,ad_platform,key_name,if(type=1,'oppo商店',if(type = 2,'oppo联盟','vivo')) type_name,type,click_url,create_owner,update_owner,create_time,update_time,game_name from dn_click_url_config
		where 1=1
		<if test="ad_platform != null and ad_platform != ''">
			and ad_platform = #{ad_platform}
		</if>
		<if test="key_name != null and key_name != ''">
			and key_name like concat('%',#{key_name},'%')
		</if>
		<if test="type != null">
			and type = #{type}
		</if>
		<if test="create_owner != null and create_owner != ''">
			and create_owner = #{create_owner}
		</if>
		<if test="game_name != null and game_name != ''">
			and game_name in (${game_name})
		</if>
		order by create_time desc
	</select>

	<insert id="add">
         insert into dn_click_url_config(ad_platform,key_name,type,click_url,create_owner,update_owner,game_name) values
         (#{ad_platform},#{key_name},#{type},#{click_url},#{create_owner},#{update_owner},#{game_name})
	</insert>

	<update id="update">
		 update dn_click_url_config set click_url = #{click_url},update_owner = #{update_owner},ad_platform = #{ad_platform}
		 ,key_name = #{key_name},type = #{type},game_name = #{game_name}
		 where id = #{id}
	</update>

	<delete id="delete">
		delete from dn_click_url_config where id in
		<foreach collection="ids" item="item" separator="," open="(" close=")">
		          #{item}
		</foreach>
	</delete>

	<insert id="batchAdd">
		replace into dn_click_url_config(ad_platform,key_name,type,click_url,create_owner,update_owner,game_name) values
		<foreach collection="list" item="item" separator=",">
			(#{item.ad_platform},#{item.key_name},#{item.type},#{item.click_url},#{item.create_owner},#{item.update_owner},#{item.game_name})
		</foreach>
	</insert>

	<select id="getGameNames" resultType="com.wbgame.pojo.jettison.vo.GameNameVo">
		select platform,packagename key_name,tappname game_name from
		(select platform,packagename,tappname from yyhz_0308.adv_platform_app_info where platform = 'vivo' and bindEndTime >= CURDATE()
		<if test="key_name != null and key_name != ''">
			and packagename = #{key_name}
		</if>
		GROUP BY packagename
		UNION ALL
		select platform,tappid,tappname from yyhz_0308.adv_platform_app_info where platform = 'oppo' and bindEndTime >= CURDATE()
		<if test="key_name != null and key_name != ''">
			and tappid = #{key_name}
		</if>
		GROUP BY tappid) a
	    where 1=1
		<if test="platform != null and platform != ''">
			and platform = #{platform}
		</if>
	</select>

</mapper>