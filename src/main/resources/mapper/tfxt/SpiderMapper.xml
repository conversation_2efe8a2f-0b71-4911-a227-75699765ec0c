<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.tfxt.SpiderMapper">

    <select id="getValidCashToutiaoAccountList" resultType="com.wbgame.pojo.adv2.businessAccountEntity.CashToutiaoAccount">
        SELECT * FROM dn_toutiao_account
        WHERE enabled = 1
    </select>

    <select id="getValidCashKuaishouAccountList" resultType="com.wbgame.pojo.adv2.businessAccountEntity.CashKuaishouAccount">
        SELECT * FROM dn_kuaishou_account
        WHERE enabled = 1
    </select>

    <select id="getCashXiaomiAccountList" resultType="com.wbgame.pojo.adv2.businessAccountEntity.CashXiaomiAccount">
        SELECT * FROM dn_xiaomi_account
        WHERE enabled = 1
    </select>

    <select id="getXiaomiApp" resultType="java.util.HashMap">
        SELECT account, appid FROM dn_xiaomi_app
    </select>

    <select id="getValidCashHuaweiAccountList" resultType="com.wbgame.pojo.adv2.businessAccountEntity.CashHuaweiAccount">
        SELECT * FROM dn_huawei_account
        WHERE enabled = 1
    </select>


    <select id="getCashOppoAccountList"
            resultType="com.wbgame.pojo.adv2.businessAccountEntity.CashOppoAccount">
        SELECT * FROM dn_oppo_account WHERE enabled = 1 and apiKey is not null and apiKey != ''
    </select>

    <select id="getCashOppoAccountListV2"
            resultType="com.wbgame.pojo.adv2.businessAccountEntity.CashOppoAccount">
        SELECT * FROM dn_oppo_account WHERE enabled = 1 and (apiKey is null or apiKey = '')
    </select>

    <select id="getValidCashGdtAccountList" resultType="com.wbgame.pojo.adv2.businessAccountEntity.CashGdtAccount">
        SELECT * FROM dn_tx_account WHERE enabled = 1
    </select>

    <select id="getCashSigmobAccountList" resultType="com.wbgame.pojo.adv2.businessAccountEntity.CashSigmobAccount">
        SELECT * FROM dn_sigmob_account WHERE enabled = 1
    </select>

    <select id="getCashHonorAccountList" resultType="com.wbgame.pojo.adv2.businessAccountEntity.CashHonorAccount">
        SELECT * FROM dn_honor_account
        WHERE enabled = 1
    </select>

</mapper>