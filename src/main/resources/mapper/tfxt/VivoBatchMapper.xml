<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.tfxt.VivoBatchMapper">


	<select id="getAllVivoAccount" resultType="java.lang.String">
        select account from dn_vivo_account_spend
    </select>

	<select id="getVivoPackage" resultType="com.alibaba.fastjson.JSONObject">
        select packagename,tappname from yyhz_0308.adv_platform_app_info where platform = 'vivo' group by packagename
    </select>

	<select id="getVivoCategory" resultType="com.alibaba.fastjson.JSONObject">
        select first_category,first_category_name,second_category id,second_category_name name from dn_vivo_batch_category
    </select>

	<select id="getVivoCity" resultType="com.alibaba.fastjson.JSONObject">
		select id,parent_id,name from dn_vivo_batch_city
		<if test="grade == 1">
			where parent_id = 0
		</if>
		<if test="grade == 2">
			where parent_id != 0
		</if>
	</select>

	<insert id="insertVivoBatchTemplate">
		insert into dn_vivo_batch_create_template(temp_id,accounts,ad_type,media_type,promotion_type,campaign_type,campaign_daily_budget,campaign_name,
		group_num,pkg_name,industry1,industry2,tf_start_date,tf_end_date,schedule_time,group_daily_budget,spend_type,charge_type,price,ocpxPrice,
		second_cv_type,second_ocpx_price,bidding_strategy,conversion_filter_cycle,group_name,creative_num,general_switch,region_code_list,sex_list,
		age_list,installed_app,view_monitor_url,click_monitor_url,ad_name,app_name,h5_type,page_url,content,create_owner,temp_name,cv_type,qualification_id,campaign_id,is_exist_campaign)
		value
		(#{temp_id},#{accounts},#{ad_type},#{media_type},#{promotion_type},#{campaign_type},#{campaign_daily_budget},#{campaign_name},#{group_num},#{pkg_name},
		#{industry1},#{industry2},#{tf_start_date},#{tf_end_date},#{schedule_time},#{group_daily_budget},#{spend_type},#{charge_type},#{price},#{ocpxPrice},
		#{second_cv_type},#{second_ocpx_price},#{bidding_strategy},#{conversion_filter_cycle},#{group_name},#{creative_num},#{general_switch},#{region_code_list},
		#{sex_list},#{age_list},#{installed_app},#{view_monitor_url},#{click_monitor_url},#{ad_name},#{app_name},#{h5_type},#{page_url},#{content},#{create_owner},#{temp_name},#{cv_type},#{qualification_id},#{campaign_id},#{is_exist_campaign})
	</insert>

	<insert id="insertVivoBatchCreative">
        insert into dn_vivo_batch_creative_set(temp_id,place_type,material_norm_id,signature,virtual_position_id,url,cover_signature,cover_url,type,display_mode)
        values
        <foreach collection="list" item="item" separator=",">
			(#{item.temp_id},#{item.place_type},#{item.material_norm_id},#{item.signature},#{item.virtual_position_id},#{item.url},#{item.cover_signature},#{item.cover_url},#{item.type},#{item.display_mode})
		</foreach>
	</insert>

	<update id="updateVivoBatchTemplate">
		update dn_vivo_batch_create_template set
		accounts = #{accounts},
		campaign_daily_budget = #{campaign_daily_budget},
		campaign_name = #{campaign_name},
		group_num = #{group_num},
		pkg_name = #{pkg_name},
		industry1 = #{industry1},
		industry2 = #{industry2},
		region_code_list = #{region_code_list},
		sex_list = #{sex_list},
		age_list = #{age_list},
		installed_app = #{installed_app},
		tf_start_date = #{tf_start_date},
		tf_end_date = #{tf_end_date},
		schedule_time = #{schedule_time},
		group_daily_budget = #{group_daily_budget},
		charge_type = #{charge_type},
		spend_type = #{spend_type},
		price = #{price},
		ocpxPrice = #{ocpxPrice},
		second_cv_type = #{second_cv_type},
		second_ocpx_price = #{second_ocpx_price},
		bidding_strategy = #{bidding_strategy},
		conversion_filter_cycle = #{conversion_filter_cycle},
		group_name = #{group_name},
		creative_num = #{creative_num},
		general_switch = #{general_switch},
		view_monitor_url = #{view_monitor_url},
		click_monitor_url = #{click_monitor_url},
		ad_name = #{ad_name},
		app_name = #{app_name},
		h5_type = #{h5_type},
		page_url = #{page_url},
		content = #{content},
		update_owner = #{update_owner},
		temp_name = #{temp_name},
		cv_type = #{cv_type},
		qualification_id = #{qualification_id},
		campaign_id = #{campaign_id},
		is_exist_campaign = #{is_exist_campaign}
		where temp_id = #{temp_id}
	</update>

	<delete id="deleteVivoBatchCreative">
		delete from dn_vivo_batch_creative_set where temp_id = #{temp_id}
	</delete>

	<delete id="deleteVivoBatchTemplate">
		delete from dn_vivo_batch_create_template where temp_id = #{temp_id} limit 1
	</delete>

	<select id="selectVivoBatchTemplateList" resultType="com.wbgame.pojo.jettison.vo.VivoBatchTemplateVo">
        select a.temp_id,a.accounts,temp_name,ad_type,media_type,GROUP_CONCAT(DISTINCT place_type) place_types,create_owner,
        update_time,concat(tf_start_date,"至",tf_end_date) tf_date
        from dn_vivo_batch_create_template a
        left join dn_vivo_batch_creative_set b on a.temp_id = b.temp_id
        where 1=1
        <if test="temp_name != null and temp_name !=''">
			and temp_name like concat('%',#{temp_name},'%')
		</if>
		<if test="create_owner != null and create_owner !=''">
			and create_owner = #{create_owner}
		</if>
		<if test="media_type != null and media_type !=''">
			and media_type in (${media_type})
		</if>
		<if test="place_type != null and place_type !=''">
			and place_type in (${place_type})
		</if>
		<if test="accounts != null and accounts !=''">
			and accounts in (${accounts})
		</if>
		<if test="startTime != null and startTime !=''">
			and update_time between concat(#{startTime}," 00:00:00") and concat(#{endTime}," 23:59:59")
		</if>
		group by temp_id
		order by update_time desc
	</select>

	<select id="selectVivoBatchTemplateDetail"
			resultType="com.wbgame.pojo.jettison.param.VivoBatchTemplateParam">
		select * from dn_vivo_batch_create_template where temp_id = #{temp_id}
	</select>

	<select id="selectVivoBatchCreatives" resultType="com.wbgame.pojo.jettison.vo.VivoBatchCreativeVo">
		select * from dn_vivo_batch_creative_set where temp_id = #{temp_id}
	</select>

	<update id="batchUpdateTfDate">
		update dn_vivo_batch_create_template set
		tf_start_date = #{startDate},
		tf_end_date = #{endDate}
		where temp_id in (${temps})
	</update>

	<select id="selectVivoBatchCronTask" resultType="com.wbgame.pojo.jettison.VivoBatchCronTaskVo">
		select * from dn_vivo_batch_cron_task where 1=1
		<if test="status != null">
			and status = #{status}
		</if>
		<if test="account != null and account !=''">
			and account in (${account})
		</if>
		<if test="create_owner != null and create_owner !=''">
			and create_owner = #{create_owner}
		</if>
	</select>

	<select id="selectVivoBatchLog" resultType="com.wbgame.pojo.jettison.VivoBatchTaskLog">
        select * from dn_vivo_batch_log where batch_id = #{batchId}
	</select>

	<update id="updateVivoBatchCronTask">
        update dn_vivo_batch_cron_task set status = #{status},message = #{message} where id = #{id}
	</update>


</mapper>