<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.tfxt.AdtMapper">
	<delete id="deleteAccountBudgetByMedia">
		delete from adt_account_budget
		where media = #{media}
			and account in (<foreach collection="list" item="it" separator=",">
			    	#{it}
			    </foreach>
		)
	</delete>
	<delete id="deleteGroupBudgetByMedia">
		delete from adt_group_budget
		where media = #{media}
	</delete>
	<delete id="deleteCampaignBudgetByMedia">
		delete from adt_campaign_budget
		where media = #{media}
	</delete>

	<select id="getToutiaoParentAccount" resultType="java.lang.String">
		SELECT account FROM dn_toutiao_account_spend WHERE parentAccount IS NULL
	</select>

	<select id="getToutiaoAccountByParent" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.AccountParam">
		SELECT * FROM dn_toutiao_account_spend WHERE 1=1
		<if test="parentAccount != null and parentAccount != ''">
			AND parentAccount = #{parentAccount}
		</if>
		<if test="enabled != null">
			AND enabled = #{enabled}
		</if>
		<if test="accountName != null and accountName != ''">
			<bind name="accountNameSearch" value="'%' + accountName + '%'"/>
			AND (accountName LIKE #{accountNameSearch} OR account LIKE #{accountNameSearch})
		</if>
		<if test="first_agent != null and first_agent != ''">
			<bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
			AND first_agent LIKE #{firstAgentSearch}
		</if>
		<if test="agent != null and agent != ''">
			<bind name="agentSearch" value="'%' + agent + '%'"/>
			AND agent LIKE #{agentSearch}
		</if>
		<if test="putUsers != null and putUsers.size > 0">
			AND putUser IN
			<foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="groupIds != null and groupIds.size > 0">
			AND groupId IN
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="business != null and business.size > 0">
			AND business IN
			<foreach collection="business" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="type != null and type.size > 0">
			AND type IN
			<foreach collection="type" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="strategy != null and strategy.size > 0">
			AND strategy IN
			<foreach collection="strategy" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="remark != null and remark != ''">
			<bind name="remarkSearch" value="'%' + remark + '%'"/>
			AND remark LIKE #{remarkSearch}
		</if>
		ORDER BY id DESC
	</select>

	<select id="getToutiaoAccountByAccount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM dn_toutiao_account_spend WHERE account = #{account}
	</select>

	<insert id="addToutiaoParentAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		INSERT INTO dn_toutiao_account_spend
		(
			account, accountName, appId, secret, createUser
		)
		VALUES
		(
			#{account}, #{accountName}, #{clientId}, #{clientSecret}, #{createUser}
		)
	</insert>

	<insert id="addToutiaoAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		INSERT IGNORE INTO dn_toutiao_account_spend
		(
		account, accountName, parentAccount, media, `type`, first_agent, agent,
		putUser, rebate, remark, groupId, createUser, business, strategy
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.accountName}, #{item.parentAccount}, #{item.media}, #{item.type}, #{item.first_agent}, #{item.agent},
			#{item.putUser}, #{item.rebate}, #{item.remark}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.strategy}
			)
		</foreach>
	</insert>

	<update id="updateToutiaoAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		UPDATE dn_toutiao_account_spend SET
			account = #{account},
			accountName = #{accountName},
			parentAccount = #{parentAccount},
			media = #{media},
			`type` = #{type},
			first_agent = #{first_agent},
			agent = #{agent},
			putUser = #{putUser},
			rebate = #{rebate},
			remark = #{remark},
			groupId = #{groupId},
			updateUser = #{updateUser},
			business = #{business},
			strategy = #{strategy},
			updateTime = now()
		WHERE id = #{id}
	</update>

	<select id="getKuaishouAccount" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.AccountParam">
		SELECT * FROM dn_kuaishou_account_spend
		WHERE 1 = 1
		<if test="accountName != null and accountName != ''">
			<bind name="accountNameSearch" value="'%' + accountName + '%'"/>
			AND (accountName LIKE #{accountNameSearch} OR account LIKE #{accountNameSearch})
		</if>
		<if test="enabled != null">
			AND enabled = #{enabled}
		</if>
		<if test="first_agent != null and first_agent != ''">
			<bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
			AND first_agent LIKE #{firstAgentSearch}
		</if>
		<if test="agent != null and agent != ''">
			<bind name="agentSearch" value="'%' + agent + '%'"/>
			AND agent LIKE #{agentSearch}
		</if>
		<if test="putUsers != null and putUsers.size > 0">
			AND putUser IN
			<foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="groupIds != null and groupIds.size > 0">
			AND groupId IN
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="business != null and business.size > 0">
			AND business IN
			<foreach collection="business" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="remark != null and remark != ''">
			<bind name="remarkSearch" value="'%' + remark + '%'"/>
			AND remark LIKE #{remarkSearch}
		</if>
		<if test="type != null and type.size > 0">
			AND type IN
			<foreach collection="type" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="strategy != null and strategy.size > 0">
			AND strategy IN
			<foreach collection="strategy" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		ORDER BY id DESC
	</select>

	<select id="getKuaishouAccountByAccount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM dn_kuaishou_account_spend WHERE account = #{account}
	</select>

	<insert id="addKuaishouAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		INSERT IGNORE INTO dn_kuaishou_account_spend
		(
		account, accountName, media, `type`, first_agent, agent,
		putUser, rebate, remark, secret, appId, groupId, createUser, business, strategy,url
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.accountName}, #{item.media}, #{item.type}, #{item.first_agent}, #{item.agent},
			#{item.putUser}, #{item.rebate}, #{item.remark}, #{item.clientSecret}, #{item.appId}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.strategy}, #{item.url}
			)
		</foreach>
	</insert>

	<update id="updateKuaishouAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		UPDATE dn_kuaishou_account_spend SET
			 account = #{account},
			 accountName = #{accountName},
			 media = #{media},
			 `type` = #{type},
			 first_agent = #{first_agent},
			 agent = #{agent},
			 putUser = #{putUser},
			 rebate = #{rebate},
			 remark = #{remark},
			 groupId = #{groupId},
			 updateUser = #{updateUser},
			 business = #{business},
			 strategy = #{strategy},
			 updateTime = now()
		WHERE id = #{id}
	</update>

	<select id="getTXParentAccount" resultType="java.lang.String">
		SELECT account FROM dn_tx_account_spend WHERE parentAccount IS NULL
	</select>

	<select id="getTXAccountByParent" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.AccountParam">
		SELECT * FROM dn_tx_account_spend WHERE 1=1
		<if test="parentAccount != null and parentAccount != ''">
			AND parentAccount = #{parentAccount}
		</if>
		<if test="enabled != null">
			AND enabled = #{enabled}
		</if>
		<if test="accountName != null and accountName != ''">
			<bind name="accountNameSearch" value="'%' + accountName + '%'"/>
			AND (accountName LIKE #{accountNameSearch} OR account LIKE #{accountNameSearch})
		</if>
		<if test="first_agent != null and first_agent != ''">
			<bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
			AND first_agent LIKE #{firstAgentSearch}
		</if>
		<if test="agent != null and agent != ''">
			<bind name="agentSearch" value="'%' + agent + '%'"/>
			AND agent LIKE #{agentSearch}
		</if>
		<if test="putUsers != null and putUsers.size > 0">
			AND putUser IN
			<foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="groupIds != null and groupIds.size > 0">
			AND groupId IN
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="business != null and business.size > 0">
			AND business IN
			<foreach collection="business" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="remark != null and remark != ''">
			<bind name="remarkSearch" value="'%' + remark + '%'"/>
			AND remark LIKE #{remarkSearch}
		</if>
		<if test="type != null and type.size > 0">
			AND type IN
			<foreach collection="type" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="strategy != null and strategy.size > 0">
			AND strategy IN
			<foreach collection="strategy" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		ORDER BY id DESC
	</select>

	<select id="getTXAccountByAccount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM dn_tx_account_spend WHERE account = #{account}
	</select>

	<insert id="addTXAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		INSERT IGNORE INTO dn_tx_account_spend
		(
		account, accountName, parentAccount, media, `type`, first_agent, agent,
		putUser, rebate, remark, wxAccountId, installType, accountType, groupId, createUser, business, strategy
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.accountName}, #{item.parentAccount}, #{item.media}, #{item.type}, #{item.first_agent},#{item.agent}, #{item.putUser},
			#{item.rebate}, #{item.remark}, #{item.wxAccountId}, #{item.installType}, #{item.accountType}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.strategy}
			)
		</foreach>
	</insert>

	<update id="updateTXAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		UPDATE dn_tx_account_spend SET
		   account = #{account},
		   accountName = #{accountName},
		   media = #{media},
		   `type` = #{type},
		   first_agent = #{first_agent},
		   agent = #{agent},
		   putUser = #{putUser},
		   rebate = #{rebate},
		   remark = #{remark},
		   wxAccountId = #{wxAccountId},
		   installType = #{installType},
		   accountType = #{accountType},
		   groupId = #{groupId},
		   updateUser = #{updateUser},
		   business = #{business},
		   strategy = #{strategy},
		   updateTime = now()
		WHERE id = #{id}
	</update>

	<select id="getAQYAccount" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.AccountParam">
		SELECT *,appId AS clientId,secret AS clientSecret FROM dn_aqy_account_spend
		WHERE 1 = 1
		<if test="accountName != null and accountName != ''">
			<bind name="accountNameSearch" value="'%' + accountName + '%'"/>
			AND (accountName LIKE #{accountNameSearch} OR account LIKE #{accountNameSearch})
		</if>
		<if test="enabled != null">
			AND enabled = #{enabled}
		</if>
		<if test="first_agent != null and first_agent != ''">
			<bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
			AND first_agent LIKE #{firstAgentSearch}
		</if>
		<if test="agent != null and agent != ''">
			<bind name="agentSearch" value="'%' + agent + '%'"/>
			AND agent LIKE #{agentSearch}
		</if>
		<if test="putUsers != null and putUsers.size > 0">
			AND putUser IN
			<foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="groupIds != null and groupIds.size > 0">
			AND groupId IN
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="business != null and business.size > 0">
			AND business IN
			<foreach collection="business" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		ORDER BY id DESC
	</select>

	<select id="getAQYSpendAccountByAccount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM dn_aqy_account_spend WHERE account = #{account}
	</select>

	<insert id="addAQYAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		INSERT IGNORE INTO dn_aqy_account_spend
		(
		account, accountName, media, `type`, first_agent, agent, putUser,
		rebate, remark, secret, appId, groupId, createUser, business, strategy
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.accountName}, #{item.media}, #{item.type}, #{item.first_agent}, #{item.agent}, #{item.putUser},
			#{item.rebate}, #{item.remark}, #{item.clientSecret}, #{item.clientId}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.strategy}
			)
		</foreach>
	</insert>

	<update id="updateAQYAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		UPDATE dn_aqy_account_spend SET
			account = #{account},
			accountName = #{accountName},
			media = #{media},
			`type` = #{type},
			first_agent = #{first_agent},
			agent = #{agent},
			putUser = #{putUser},
			rebate = #{rebate},
			remark = #{remark},
			secret = #{clientSecret},
			appId = #{clientId},
			groupId = #{groupId},
			updateUser = #{updateUser},
			business = #{business},
			strategy = #{strategy}
		WHERE id = #{id}
	</update>

	<select id="getBaiduParentAccount" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		select account,accountName from dn_baidu_account_spend
		where app_id is not null and secret is not null
	</select>

	<select id="getBaiduAccount" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.AccountParam">
		SELECT id,account,accountName,media,type,first_agent,agent,putUser,rebate,remark,groupId,enabled,createTime
		 ,createUser,business,dataType,updateUser,strategy,updateTime,parent_account parentAccount,b.url,refreshToken
		 FROM dn_baidu_account_spend a left join
		 (select account paccount,url from dn_baidu_account_spend where app_id is not null) b on a.parent_account = b.paccount
		WHERE 1 = 1
		<if test="accountName != null and accountName != ''">
			<bind name="accountNameSearch" value="'%' + accountName + '%'"/>
			AND (accountName LIKE #{accountNameSearch} OR account LIKE #{accountNameSearch})
		</if>
		<if test="enabled != null">
			AND enabled = #{enabled}
		</if>
		<if test="dataType != null">
			AND dataType = #{dataType}
		</if>
		<if test="remark != null and remark != ''">
			AND remark like concat('%',#{remark},'%')
		</if>
		<if test="parentAccount != null and parentAccount != ''">
			AND parent_account like concat('%',#{parentAccount},'%')
		</if>
		<if test="first_agent != null and first_agent != ''">
			<bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
			AND first_agent LIKE #{firstAgentSearch}
		</if>
		<if test="agent != null and agent != ''">
			<bind name="agentSearch" value="'%' + agent + '%'"/>
			AND agent LIKE #{agentSearch}
		</if>
		<if test="putUsers != null and putUsers.size > 0">
			AND putUser IN
			<foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="groupIds != null and groupIds.size > 0">
			AND groupId IN
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="business != null and business.size > 0">
			AND business IN
			<foreach collection="business" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="remark != null and remark != ''">
			<bind name="remarkSearch" value="'%' + remark + '%'"/>
			AND remark LIKE #{remarkSearch}
		</if>
		ORDER BY id DESC
	</select>

	<select id="getBaiduAccountByAccount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM dn_baidu_account_spend WHERE account = #{account}
	</select>

	<insert id="addBaiduAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		INSERT IGNORE INTO dn_baidu_account_spend
		(
		account, accountName, media, `type`, first_agent, agent, putUser,
		rebate, remark, groupId, createUser, business, strategy , parent_account,dataType
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.accountName}, #{item.media}, #{item.type}, #{item.first_agent}, #{item.agent}, #{item.putUser},
			#{item.rebate}, #{item.remark},  #{item.groupId}, #{item.createUser}, #{item.business}, #{item.strategy} , #{item.parentAccount}
			,#{item.dataType}
			)
		</foreach>
	</insert>

	<update id="updateBaiduAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		UPDATE dn_baidu_account_spend SET
		  account = #{account},
		  accountName = #{accountName},
		  media = #{media},
		  `type` = #{type},
		  first_agent = #{first_agent},
		  agent = #{agent},
		  putUser = #{putUser},
		  rebate = #{rebate},
		  remark = #{remark},
		  groupId = #{groupId},
		  updateUser = #{updateUser},
		  business = #{business},
		  strategy = #{strategy},
		  dataType = #{dataType},
		  parent_account = #{parentAccount}
		WHERE id = #{id}
	</update>

	<select id="getOppoAccount" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.AccountParam">
		SELECT *,api_key AS clientSecret, api_id AS clientId FROM dn_oppo_account_spend
		WHERE 1 = 1
		<if test="account != null and account != ''">
			AND account in (${account})
		</if>
		<if test="accountName != null and accountName != ''">
			<bind name="accountNameSearch" value="'%' + accountName + '%'"/>
			AND (accountName LIKE #{accountNameSearch} OR account LIKE #{accountNameSearch})
		</if>
		<if test="accountSubject != null and accountSubject != ''">
			AND if(accountSubject is null, '-1', accountSubject) in (${accountSubject})
		</if>
		<if test="enabled != null">
			AND enabled = #{enabled}
		</if>
		<if test="first_agent != null and first_agent != ''">
			<bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
			AND first_agent LIKE #{firstAgentSearch}
		</if>
		<if test="agent != null and agent != ''">
			<bind name="agentSearch" value="'%' + agent + '%'"/>
			AND agent LIKE #{agentSearch}
		</if>
		<if test="putUsers != null and putUsers.size > 0">
			AND putUser IN
			<foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="groupIds != null and groupIds.size > 0">
			AND groupId IN
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="business != null and business.size > 0">
			AND business IN
			<foreach collection="business" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="type != null and type.size > 0">
			AND type IN
			<foreach collection="type" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="strategy != null and strategy.size > 0">
			AND strategy IN
			<foreach collection="strategy" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="remark != null and remark != ''">
			<bind name="remarkSearch" value="'%' + remark + '%'"/>
			AND remark LIKE #{remarkSearch}
		</if>
		ORDER BY id DESC
	</select>

	<select id="getOppoAccountByAccount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM dn_oppo_account_spend WHERE account = #{account}
	</select>

	<insert id="addOppoAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		INSERT IGNORE INTO dn_oppo_account_spend
		(
		account, accountName, media, `type`, first_agent, agent, putUser,
		rebate, remark, api_key, api_id, groupId, createUser, business, strategy,accountSubject
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.accountName}, #{item.media}, #{item.type}, #{item.first_agent}, #{item.agent}, #{item.putUser},
			#{item.rebate}, #{item.remark}, #{item.clientSecret}, #{item.clientId}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.strategy}, #{item.accountSubject}
			)
		</foreach>
	</insert>

	<update id="updateOppoAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		UPDATE dn_oppo_account_spend SET
			 account = #{account},
			 accountName = #{accountName},
			 media = #{media},
			 `type` = #{type},
			 first_agent = #{first_agent},
			 agent = #{agent},
			 putUser = #{putUser},
			 rebate = #{rebate},
			 remark = #{remark},
			 api_key = #{clientSecret},
			 api_id = #{clientId},
			 groupId = #{groupId},
			 updateUser = #{updateUser},
			 business = #{business},
			 strategy = #{strategy},
			 accountSubject = #{accountSubject},
			 updateTime = now()
		WHERE id = #{id}
	</update>

	<update id="updateOppoAccountRemark">
		UPDATE dn_oppo_account_spend SET
		        <if test="remark != null and remark != ''">
					remark = #{remark}
				</if>
		        <if test="accountName!=null and accountName != ''">
					accountName = #{accountName}
				</if>
		WHERE account = #{account}
	</update>

	<select id="getQttAccount" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.AccountParam">
		SELECT * FROM dn_qtt_account_spend
		WHERE 1 = 1
		<if test="accountName != null and accountName != ''">
			<bind name="accountNameSearch" value="'%' + accountName + '%'"/>
			AND (accountName LIKE #{accountNameSearch} OR account LIKE #{accountNameSearch})
		</if>
		<if test="enabled != null">
			AND enabled = #{enabled}
		</if>
		<if test="first_agent != null and first_agent != ''">
			<bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
			AND first_agent LIKE #{firstAgentSearch}
		</if>
		<if test="agent != null and agent != ''">
			<bind name="agentSearch" value="'%' + agent + '%'"/>
			AND agent LIKE #{agentSearch}
		</if>
		<if test="putUsers != null and putUsers.size > 0">
			AND putUser IN
			<foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="groupIds != null and groupIds.size > 0">
			AND groupId IN
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="business != null and business.size > 0">
			AND business IN
			<foreach collection="business" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		ORDER BY id DESC
	</select>

	<select id="getQttAccountByAccount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM dn_qtt_account_spend WHERE account = #{account}
	</select>

	<insert id="addQttAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		INSERT IGNORE INTO dn_qtt_account_spend
		(
		account, appId, accountName, refreshToken, media, `type`, first_agent, agent, putUser,
		rebate, remark, groupId, createUser, business, strategy
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.appId}, #{item.accountName}, #{item.refreshToken}, #{item.media}, #{item.type}, #{item.first_agent}, #{item.agent}, #{item.putUser},
			#{item.rebate}, #{item.remark}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.strategy}
			)
		</foreach>
	</insert>

	<update id="updateQttAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		UPDATE dn_qtt_account_spend SET
			account = #{account},
			appId = #{appId},
			accountName = #{accountName},
			refreshToken = #{refreshToken},
			media = #{media},
			`type` = #{type},
			first_agent = #{first_agent},
			agent = #{agent},
			putUser = #{putUser},
			rebate = #{rebate},
			remark = #{remark},
			groupId = #{groupId},
			updateUser = #{updateUser},
			business = #{business},
			strategy = #{strategy}
		WHERE id = #{id}
	</update>


	<select id="getHuaweiAccount" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.AccountParam">
		SELECT *,client_id AS clientId, client_secret AS clientSecret FROM dn_huawei_account_spend WHERE 1 = 1
		<if test="accountName != null and accountName != ''">
			<bind name="accountNameSearch" value="'%' + accountName + '%'"/>
			AND (accountName LIKE #{accountNameSearch} OR account LIKE #{accountNameSearch})
		</if>
		<if test="accountSubject != null and accountSubject != ''">
			AND if(accountSubject is null, '-1', accountSubject) in (${accountSubject})
		</if>
		<if test="enabled != null">
			AND enabled = #{enabled}
		</if>
		<if test="first_agent != null and first_agent != ''">
			<bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
			AND first_agent LIKE #{firstAgentSearch}
		</if>
		<if test="agent != null and agent != ''">
			<bind name="agentSearch" value="'%' + agent + '%'"/>
			AND agent LIKE #{agentSearch}
		</if>
		<if test="putUsers != null and putUsers.size > 0">
			AND putUser IN
			<foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="groupIds != null and groupIds.size > 0">
			AND groupId IN
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="business != null and business.size > 0">
			AND business IN
			<foreach collection="business" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="type != null and type.size > 0">
			AND type IN
			<foreach collection="type" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="strategy != null and strategy.size > 0">
			AND strategy IN
			<foreach collection="strategy" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="remark != null and remark != ''">
			<bind name="remarkSearch" value="'%' + remark + '%'"/>
			AND remark LIKE #{remarkSearch}
		</if>
		ORDER BY createTime DESC
	</select>

	<select id="getHuaweiAccountByAccount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM dn_huawei_account_spend WHERE account = #{account}
	</select>

	<insert id="addHuaweiAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		INSERT IGNORE INTO dn_huawei_account_spend
		(
		account, accountName, client_id, client_secret, media, `type`, first_agent, agent, putUser,
		rebate, remark, groupId, createUser, isCPD, business, strategy, package_name, accountSubject
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.accountName}, #{item.clientId}, #{item.clientSecret}, #{item.media}, #{item.type}, #{item.first_agent}, #{item.agent}, #{item.putUser},
			#{item.rebate}, #{item.remark}, #{item.groupId}, #{item.createUser}, #{item.isCPD}, #{item.business}, #{item.strategy}, #{item.package_name}, #{item.accountSubject}
			)
		</foreach>
	</insert>

	<update id="updateHuaweiAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		UPDATE dn_huawei_account_spend SET
		   account = #{account},
		   accountName = #{accountName},
		   client_id = #{clientId},
		   client_secret = #{clientSecret},
		   media = #{media},
		   `type` = #{type},
		   first_agent = #{first_agent},
		   agent = #{agent},
		   putUser = #{putUser},
		   rebate = #{rebate},
		   remark = #{remark},
		   groupId = #{groupId},
		   isCPD = #{isCPD},
		   updateUser = #{updateUser},
		   business = #{business},
		   strategy = #{strategy},
		   accountSubject = #{accountSubject},
		   updateTime = now(),
		   package_name = #{package_name}
		WHERE account = #{account}
	</update>

	<select id="getVivoAccount" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO" 
			parameterType="com.wbgame.pojo.jettison.report.param.AccountParam">
		SELECT *,client_id AS clientId, client_secret AS clientSecret,access_token as accessToken FROM dn_vivo_account_spend WHERE 1 = 1
		<if test="type != null and type.size > 0">
			AND type in
			<foreach collection="type" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="account != null and account != ''">
			<bind name="accountSearch" value="'%' + account + '%'"/>
			AND account LIKE #{accountSearch}
		</if>
		<if test="accountSubject != null and accountSubject != ''">
			AND if(accountSubject is null, '-1', accountSubject) in (${accountSubject})
		</if>
		<if test="enabled != null">
			and enabled = #{enabled}
		</if>
		<if test="first_agent != null and first_agent != ''">
			<bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
			AND first_agent LIKE #{firstAgentSearch}
		</if>
		<if test="agent != null and agent != ''">
			<bind name="agentSearch" value="'%' + agent + '%'"/>
			AND agent LIKE #{agentSearch}
		</if>
		<if test="putUsers != null and putUsers.size > 0">
			AND putUser IN
			<foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="groupIds != null and groupIds.size > 0">
			AND groupId IN
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="business != null and business.size > 0">
			AND business IN
			<foreach collection="business" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="remark != null and remark != ''">
			<bind name="remarkSearch" value="'%' + remark + '%'"/>
			AND remark LIKE #{remarkSearch}
		</if>
		<if test="type != null and type.size > 0">
			AND type IN
			<foreach collection="type" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="strategy != null and strategy.size > 0">
			AND strategy IN
			<foreach collection="strategy" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		ORDER BY createTime DESC
	</select>

	<select id="getVivoAccountByAccount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM dn_vivo_account_spend WHERE account = #{account}
	</select>

	<insert id="addVivoAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		INSERT IGNORE INTO dn_vivo_account_spend
		(
		account, client_id, client_secret, `type`, first_agent, agent,
		putUser, rebate, remark, groupId, createUser, business, strategy,accountSubject
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.clientId}, #{item.clientSecret}, #{item.type},  #{item.first_agent}, #{item.agent},
			#{item.putUser}, #{item.rebate}, #{item.remark}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.strategy}, #{item.accountSubject}
			)
		</foreach>
	</insert>

	<update id="updateVivoAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		UPDATE dn_vivo_account_spend SET
			 account = #{account},
			 client_id = #{clientId},
			 client_secret = #{clientSecret},
			 `type` = #{type},
			 first_agent = #{first_agent},
			 agent = #{agent},
			 putUser = #{putUser},
			 rebate = #{rebate},
			 remark = #{remark},
			 groupId = #{groupId},
			 updateUser = #{updateUser},
			 business = #{business},
			 strategy = #{strategy},
			 accountSubject = #{accountSubject},
			 updateTime = now()
		WHERE account = #{account}
	</update>
	<update id="batchUpdateVivoAccount" parameterType="Map">
		UPDATE dn_vivo_account_spend SET
		    <if test="account.account != null and account.account != ''">
					account = #{account.account},
				</if>
					<if test="account.clientId != null and account.clientId != ''">
					client_id = #{account.clientId},
				</if>
					<if test="account.clientSecret != null and account.clientSecret != ''">
					client_secret = #{account.clientSecret},
				</if>
					<if test="account.type != null and account.type != ''">
					`type` = #{account.type},
				</if>
					<if test="account.first_agent != null and account.first_agent != ''">
					first_agent = #{account.first_agent},
				</if>
					<if test="account.agent != null and account.agent != ''">
					agent = #{account.agent},
				</if>
					<if test="account.putUser != null and account.putUser != ''">
					putUser = #{account.putUser},
				</if>
					<if test="account.rebate != null and account.rebate != ''">
					rebate = #{account.rebate},
				</if>
					<if test="account.remark != null and account.remark != ''">
					remark = #{account.remark},
				</if>
					<if test="account.groupId != null and account.groupId != ''">
					groupId = #{account.groupId},
				</if>
					<if test="account.updateUser != null and account.updateUser != ''">
					updateUser = #{account.updateUser},
				</if>
					<if test="account.business != null and account.business != ''">
					business = #{account.business},
				</if>
					<if test="account.strategy != null and account.strategy != ''">
					strategy = #{account.strategy},
				</if>
					<if test="account.accountSubject != null and account.accountSubject != ''">
					accountSubject = #{account.accountSubject},
				</if>
					updateTime = now()
				where account in (<foreach collection="list" item="item" separator=",">
				    #{item}
				</foreach>
		)
	</update>

	<select id="getXiaomiAccount" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO" 
			parameterType="com.wbgame.pojo.jettison.report.param.AccountParam">
		SELECT *,signId AS clientId, secretKey AS ClientSecret FROM dn_xiaomi_account_spend WHERE 1 = 1
		<if test="accountName != null and accountName != ''">
			<bind name="accountNameSearch" value="'%' + accountName + '%'"/>
			AND (accountName LIKE #{accountNameSearch} OR account LIKE #{accountNameSearch})
		</if>
		<if test="accountSubject != null and accountSubject != ''">
			AND if(accountSubject is null, '-1', accountSubject) in (${accountSubject})
		</if>
		<if test="enabled != null">
			and enabled = #{enabled}
		</if>
		<if test="first_agent != null and first_agent != ''">
			<bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
			AND first_agent LIKE #{firstAgentSearch}
		</if>
		<if test="agent != null and agent != ''">
			<bind name="agentSearch" value="'%' + agent + '%'"/>
			AND agent LIKE #{agentSearch}
		</if>
		<if test="putUsers != null and putUsers.size > 0">
			AND putUser IN
			<foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="groupIds != null and groupIds.size > 0">
			AND groupId IN
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="business != null and business.size > 0">
			AND business IN
			<foreach collection="business" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="remark != null and remark != ''">
			<bind name="remarkSearch" value="'%' + remark + '%'"/>
			AND remark LIKE #{remarkSearch}
		</if>
		<if test="type != null and type.size > 0">
			AND type IN
			<foreach collection="type" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="strategy != null and strategy.size > 0">
			AND strategy IN
			<foreach collection="strategy" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		ORDER BY createTime DESC
	</select>

	<select id="getXiaomiAccountByAccount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM dn_xiaomi_account_spend WHERE account = #{account}
	</select>

	<insert id="addXiaomiAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		INSERT IGNORE INTO dn_xiaomi_account_spend
		(
		account, accountName, signId, secretKey, `type`, first_agent, agent,
		putUser, rebate, remark, groupId, createUser, business, strategy, accountSubject
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.accountName},#{item.clientId}, #{item.clientSecret}, #{item.type}, #{item.first_agent}, #{item.agent},
			#{item.putUser}, #{item.rebate}, #{item.remark}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.strategy}, #{item.accountSubject}
			)
		</foreach>
	</insert>

	<update id="updateXiaomiAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		UPDATE dn_xiaomi_account_spend SET
		   account = #{account},
		   accountName = #{accountName},
		   signId = #{clientId},
		   secretKey = #{clientSecret},
		   `type` = #{type},
		   first_agent = #{first_agent},
		   agent = #{agent},
		   putUser = #{putUser},
		   rebate = #{rebate},
		   remark = #{remark},
		   groupId = #{groupId},
		   updateUser = #{updateUser},
		   business = #{business},
		   strategy = #{strategy},
		   accountSubject = #{accountSubject},
		   updateTime = now()
		WHERE account = #{account}
	</update>

	<select id="getBzhanAccount" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.AccountParam">
		SELECT *,appName AS accountName,appKey AS clientId, secretKey AS ClientSecret FROM dn_bzhan_account_spend WHERE 1 = 1
		<if test="account != null and account != ''">
			<bind name="accountSearch" value="'%' + account + '%'"/>
			AND account LIKE #{accountSearch}
		</if>
		<if test="enabled != null">
			and enabled = #{enabled}
		</if>
		<if test="first_agent != null and first_agent != ''">
			<bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
			AND first_agent LIKE #{firstAgentSearch}
		</if>
		<if test="agent != null and agent != ''">
			<bind name="agentSearch" value="'%' + agent + '%'"/>
			AND agent LIKE #{agentSearch}
		</if>
		<if test="putUsers != null and putUsers.size > 0">
			AND putUser IN
			<foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="groupIds != null and groupIds.size > 0">
			AND groupId IN
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="business != null and business.size > 0">
			AND business IN
			<foreach collection="business" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		ORDER BY createTime DESC
	</select>

	<select id="getBzhanAccountByAccount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM dn_bzhan_account_spend WHERE account = #{account}
	</select>

	<insert id="addBzhanAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		INSERT IGNORE INTO dn_bzhan_account_spend
		(
		account, appName, appKey, secretKey, `type`, first_agent, agent,
		putUser, rebate, remark, groupId, createUser, business, strategy
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.accountName},#{item.clientId}, #{item.clientSecret}, #{item.type}, #{item.first_agent}, #{item.agent},
			#{item.putUser}, #{item.rebate}, #{item.remark}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.strategy}
			)
		</foreach>
	</insert>

	<update id="updateBzhanAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		UPDATE dn_bzhan_account_spend SET
		   account = #{account},
		   appName = #{accountName},
		   appKey = #{clientId},
		   secretKey = #{clientSecret},
		   `type` = #{type},
		   first_agent = #{first_agent},
		   agent = #{agent},
		   putUser = #{putUser},
		   rebate = #{rebate},
		   remark = #{remark},
		   groupId = #{groupId},
		   updateUser = #{updateUser},
		   business = #{business},
		   strategy = #{strategy}
		WHERE account = #{account}
	</update>

	<select id="getWeiboAccount" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.AccountParam">
		SELECT * FROM dn_spend_account WHERE 1 = 1
		<if test="account != null and account != ''">
			<bind name="accountNameSearch" value="'%' + account + '%'"/>
			AND account LIKE #{accountNameSearch}
		</if>
		<if test="enabled != null">
			and enabled = #{enabled}
		</if>
		<if test="first_agent != null and first_agent != ''">
			<bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
			AND first_agent LIKE #{firstAgentSearch}
		</if>
		<if test="agent != null and agent != ''">
			<bind name="agentSearch" value="'%' + agent + '%'"/>
			AND agent LIKE #{agentSearch}
		</if>
		<if test="putUsers != null and putUsers.size > 0">
			AND putUser IN
			<foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="groupIds != null and groupIds.size > 0">
			AND groupId IN
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="business != null and business.size > 0">
			AND business IN
			<foreach collection="business" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="remark != null and remark != ''">
			<bind name="remarkSearch" value="'%' + remark + '%'"/>
			AND remark LIKE #{remarkSearch}
		</if>
		ORDER BY createTime DESC
	</select>

	<select id="getWeiboAccountByAccount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM dn_spend_account WHERE account = #{account}
	</select>

	<insert id="addWeiboAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		INSERT IGNORE INTO dn_spend_account
		(
		account, accountName, clientId, clientSecret, `type`, first_agent, agent,
		putUser, rebate, remark, groupId, createUser, business, strategy,media
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.accountName},#{item.clientId}, #{item.clientSecret}, #{item.type}, #{item.first_agent}, #{item.agent},
			#{item.putUser}, #{item.rebate}, #{item.remark}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.strategy},#{item.media}
			)
		</foreach>
	</insert>

	<update id="updateWeiboAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		UPDATE dn_spend_account SET
		   account = #{account},
		   accountName = #{accountName},
		   clientId = #{clientId},
		   clientSecret = #{clientSecret},
		   `type` = #{type},
		   first_agent = #{first_agent},
		   agent = #{agent},
		   putUser = #{putUser},
		   rebate = #{rebate},
		   remark = #{remark},
		   groupId = #{groupId},
		   updateUser = #{updateUser},
		   business = #{business},
		   strategy = #{strategy}
		WHERE account = #{account}
	</update>

	<insert id="addAccountBusiness" parameterType="java.lang.String">
		REPLACE INTO dn_account_business ( account, business,createTime)
        VALUES ( #{account}, #{business}, CURRENT_DATE() )
	</insert>
	<insert id="addUcAccount">
		INSERT IGNORE INTO dn_spend_uc_account
		(
		account,  password, token, media, `type`, first_agent, agent, putUser,
		rebate, remark, groupId, createUser, business, strategy
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.password}, #{item.token}, #{item.media}, #{item.type}, #{item.first_agent}, #{item.agent}, #{item.putUser},
			#{item.rebate}, #{item.remark}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.strategy}
			)
		</foreach>
	</insert>
	<insert id="insertAccountBudget">
		insert into adt_account_budget
		(media, account, budget, balance, budget_state)
		values
		    <foreach collection="list" item="it" separator=",">
				(#{media}, #{it.account}, #{it.budget}, #{it.balance}, #{it.budget_state})
			</foreach>
	</insert>
	<insert id="insertGroupBudget">
		insert into adt_group_budget
		(media, account, budget, group_id, group_name, budget_state)
		values
		    <foreach collection="list" item="it" separator=",">
				(#{it.media}, #{it.account}, #{it.budget}, #{it.group_id}, #{it.group_name}, #{it.budget_state})
			</foreach>
	</insert>
	<insert id="insertCampaignBudget">
		insert into adt_campaign_budget
		(media, account, budget, group_id, group_name, campaign_id, campaign_name, budget_state)
		values
		    <foreach collection="list" item="it" separator=",">
				(#{it.media}, #{it.account}, #{it.budget}, #{it.group_id}, #{it.group_name}, #{it.campaign_id}, #{it.campaign_name}, #{it.budget_state})
			</foreach>
	</insert>
	<insert id="insertCreativeXiaomiUpdateStatusDto">
		insert into creative_xiaomi_update_status
			(taskId,creativeId,account,success,message,stage, operate, updateMethod, amount)
		values
			(#{taskId},#{creativeId},#{account},#{success},#{message},#{stage}, #{operate}, #{updateMethod}, #{amount})
	</insert>

	<update id="updateEnabled" parameterType="com.wbgame.pojo.jettison.report.param.AccountSpendEnabledParam">
		UPDATE ${table} SET enabled = #{enabled},updateUser = #{updateUser},updateTime = now()
		WHERE account IN
		<foreach collection="accountList" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</update>

	<update id="updateGroup" parameterType="com.wbgame.pojo.jettison.report.param.AccountSpendGroupParam">
		UPDATE ${table} SET groupId = #{groupId},updateUser = #{updateUser},updateTime = now()
		WHERE account IN
		<foreach collection="accountList" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</update>
	<update id="updateUcAccount">
		UPDATE dn_spend_uc_account SET
			account = #{account},
			password = #{password},
			token = #{token},
			media = #{media},
			`type` = #{type},
			first_agent = #{first_agent},
			agent = #{agent},
			putUser = #{putUser},
			rebate = #{rebate},
			remark = #{remark},
			groupId = #{groupId},
			updateUser = #{updateUser},
			business = #{business},
			strategy = #{strategy}
		WHERE account = #{account}
	</update>

	<select id="getUcAccount" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		SELECT * FROM dn_spend_uc_account WHERE 1 = 1
		<if test="account != null and account != ''">
			<bind name="accountNameSearch" value="'%' + account + '%'"/>
			AND  account LIKE #{accountNameSearch}
		</if>
		<if test="enabled != null">
			AND enabled = #{enabled}
		</if>
		<if test="first_agent != null and first_agent != ''">
			<bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
			AND first_agent LIKE #{firstAgentSearch}
		</if>
		<if test="agent != null and agent != ''">
			<bind name="agentSearch" value="'%' + agent + '%'"/>
			AND agent LIKE #{agentSearch}
		</if>
		<if test="putUsers != null and putUsers.size > 0">
			AND putUser IN
			<foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="groupIds != null and groupIds.size > 0">
			AND groupId IN
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="business != null and business.size > 0">
			AND business IN
			<foreach collection="business" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		ORDER BY createTime DESC
	</select>
	<select id="getUcAccountByAccount" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM dn_spend_uc_account WHERE account = #{account}
	</select>
    <select id="selectAccountBudget" resultType="com.wbgame.pojo.budgetWarning.AccountBudget">
		select * from adt_account_budget
		    where media = #{media}
		<if test="list != null and list.size() > 0">
			and account in (<foreach collection="list" item="it" separator=",">
			#{it}
		</foreach>)
		</if>
		order by balance desc
	</select>
	<select id="selectGroupBudget" resultType="com.wbgame.pojo.budgetWarning.GroupBudget">
		select * from adt_group_budget
		    where media = #{media}
		<if test="list != null">
			and group_name in (<foreach collection="list" item="it" separator=",">
		      #{it}
		</foreach>)
		</if>
	</select>
	<select id="selectCampaignBudget" resultType="com.wbgame.pojo.budgetWarning.CampaignBudget">
		select * from adt_campaign_budget
		    where media = #{media}
		<if test="list != null">
			and campaign_name in (<foreach collection="list" item="it" separator=",">
		      #{it}
		</foreach>)
		</if>
	</select>

	<select id="selectCampaignIdBudget" resultType="com.wbgame.pojo.budgetWarning.CampaignBudget">
		select * from adt_campaign_budget
		    where media = #{media}
		<if test="list != null">
			and campaign_id in (<foreach collection="list" item="it" separator=",">
		      #{it}
		</foreach>)
		</if>
	</select>

	<select id="selectKuaiShouDeveloperById" resultType="com.wbgame.pojo.jettison.KuaiShouDeveloper">
		select * from kuaishou_developer_app where id = #{id}
	</select>

	<select id="selectKuaiShouDeveloper" resultType="com.wbgame.pojo.jettison.KuaiShouDeveloper">
		select * from kuaishou_developer_app
	</select>

	<select id="selectHonorAccount" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.AccountParam">
		SELECT *,client_id AS clientId, client_secret AS clientSecret FROM dn_spend_rongyao_account WHERE 1 = 1
		<if test="accountName != null and accountName != ''">
			<bind name="accountNameSearch" value="'%' + accountName + '%'"/>
			AND (accountName LIKE #{accountNameSearch} OR account LIKE #{accountNameSearch})
		</if>
		<if test="enabled != null">
			AND enabled = #{enabled}
		</if>
		<if test="accountSubject != null and accountSubject != ''">
			AND if(accountSubject is null, '-1', accountSubject) in (${accountSubject})
		</if>
		<if test="first_agent != null and first_agent != ''">
			<bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
			AND first_agent LIKE #{firstAgentSearch}
		</if>
		<if test="agent != null and agent != ''">
			<bind name="agentSearch" value="'%' + agent + '%'"/>
			AND agent LIKE #{agentSearch}
		</if>
		<if test="putUsers != null and putUsers.size > 0">
			AND putUser IN
			<foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="groupIds != null and groupIds.size > 0">
			AND groupId IN
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="business != null and business.size > 0">
			AND business IN
			<foreach collection="business" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="remark != null and remark != ''">
			<bind name="remarkSearch" value="'%' + remark + '%'"/>
			AND remark LIKE #{remarkSearch}
		</if>
		ORDER BY createTime DESC
	</select>

	<select id="selectHonorAccountByAccount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM dn_spend_rongyao_account WHERE account = #{account}
	</select>

	<insert id="addHonorAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		INSERT IGNORE INTO dn_spend_rongyao_account
		(
		account, accountName, client_id, client_secret, media, `type`, first_agent, agent, putUser,
		rebate, remark, groupId, createUser, business, strategy, accountSubject
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.accountName}, #{item.clientId}, #{item.clientSecret}, #{item.media}, #{item.type}, #{item.first_agent}, #{item.agent}, #{item.putUser},
			#{item.rebate}, #{item.remark}, #{item.groupId}, #{item.createUser},  #{item.business}, #{item.strategy}, #{item.accountSubject}
			)
		</foreach>
	</insert>

	<update id="updateHonorAccount" parameterType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		UPDATE dn_spend_rongyao_account SET
		   account = #{account},
		   accountName = #{accountName},
		   client_id = #{clientId},
		   client_secret = #{clientSecret},
		   media = #{media},
		   `type` = #{type},
		   first_agent = #{first_agent},
		   agent = #{agent},
		   putUser = #{putUser},
		   rebate = #{rebate},
		   remark = #{remark},
		   groupId = #{groupId},
		   updateUser = #{updateUser},
		   business = #{business},
		   strategy = #{strategy},
		   accountSubject = #{accountSubject},
		   updateTime = now()
		WHERE account = #{account}
	</update>
	<select id="getValidXiaomiSpendAccountList"
			resultType="com.wbgame.pojo.adv2.businessAccountEntity.SpendXiaomiAccount">
SELECT account, media, `type`,first_agent, agent, putUser, rebate, signId, secretKey FROM dnwx_adt.dn_xiaomi_account_spend
WHERE enabled = 1 AND signId IS NOT NULL AND secretKey IS NOT NULL
	</select>
	<select id="getCreativeXiaomiUpdateStatusDto"
			resultType="com.wbgame.pojo.jettison.report.dto.CreativeXiaomiUpdateStatusDto">
		select taskId,creativeId,account,success,message,stage, operate, updateMethod, truncate(amount, 2) amount,
				dd.putUser,dd.gameName, cc.createtime create_time
		from creative_xiaomi_update_status cc
		LEFT JOIN (select creative_id,putUser,gameName from dnwx_adt.dn_xiaomi_creative_info_temp where creative_id in
						(select DISTINCT creativeId from creative_xiaomi_update_status) group by creative_id) dd
		ON cc.creativeId=dd.creative_id

		<where>
			<if test="taskId != null and taskId != ''">
				and taskId = #{taskId}
			</if>
			<if test="creativeId != null and creativeId != ''">
				and creativeId = #{creativeId}
			</if>
			<if test="account != null and account != ''">
				and account = #{account}
			</if>
			<if test="success != null and success != ''">
				and success = #{success}
			</if>
			<if test="putUser != null and putUser != ''">
				and dd.putUser in (${putUser})
			</if>
			<if test="gameName != null and gameName != ''">
				and dd.gameName in (${gameName})
			</if>
			<if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
				and cc.createtime between #{startTime} and #{endTime}
			</if>
		</where>
		order by createtime desc
	</select>

	<select id="selectKuaishouAccountByAccount" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
		select * from dn_kuaishou_account_spend where account = #{account}
	</select>

	<update id="updateKuaishouAuth">
		UPDATE dn_kuaishou_account_spend SET
			 appId = #{appId},
			 secret = #{clientSecret},
			 url = #{url},
			 updateUser = #{updateUser},
			 updateTime = now()
		WHERE account = #{account}
	</update>
	<update id="updateShortcutConfig">
		UPDATE common_click_callback_shortcut SET
		    update_user = #{update_user},
		    update_time = now(),
		    enabled = #{enabled},
		    special_name = #{special_name}
		<if test="events != null and events != '' ">
			,events = #{events}
		</if>
		where shortcut = #{shortcut}
	</update>
	<update id="updateOauthVivoAccount">
		UPDATE common_vivo_oauth_account SET
		    access_token = #{access_token},
		    refresh_token = #{refresh_token},
			refresh_token_date = #{refresh_token_date},
			token_date = #{token_date}
		WHERE account = #{account}
	</update>
	<update id="updateAccountBudget">
		update adt_account_budget
		set budget = #{budget}
		where account = #{account}
	</update>
	<insert id="updateOauthVivoSrcId">
		insert into common_vivo_oauth_appid_acc (appid, src_id, account)
		values
		    (#{appid}, #{src_id}, #{account})
	</insert>

	<select id="getGroupNameById" resultType="java.util.Map">
		select  id, groupName, enabled, createUser, createTime
		from dn_account_group
	</select>
	<select id="getShortcutConfigList" resultType="com.wbgame.pojo.jettison.vo.ShortcutConfig">
		select * from common_click_callback_shortcut where 1 =1
		<if test="appid != null and appid != '' ">
			and appid in (${appid})
		</if>
        <if test="specialName != null and specialName != '' ">
			and special_name like '%${specialName}%'
		</if>
		<if test="enabled != null ">
			and enabled = #{enabled}
		</if>
		order by update_time desc
	</select>
	<select id="isShortcutDefined" resultType="java.lang.Integer">
		select count(1) from common_click_callback_shortcut where shortcut = #{shortcut}
	</select>
	<select id="getXiaomiCampaignUpdatePriceRecord"
			resultType="com.wbgame.pojo.jettison.report.param.CreativeXiaomiUpdatePriceParam">
		select taskId,operate,updateMethod,amount,gameNames,putUser,account,lowType,transferType,groupName,start_date,end_date,roi_range_min,roi_range_max,spend_range_min,spend_range_max,`limit`, create_time
		    from dnwx_adt.dn_xiaomi_campaign_update_price_record
		<where>
			<if test="taskId != null and taskId != ''">
				and taskId = #{taskId}
			</if>
			<if test="account != null and account != ''">
				and account = #{account}
			</if>
		    <if test="gameName != null and gameName != ''">
				and gameNames like concat('%', #{gameName}, '%')
			</if>
		    <if test="putUser != null and putUser != ''">
				and putUser like concat('%', #{putUser}, '%')
			</if>
			<if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
					and create_time between #{startTime} and #{endTime}
			</if>
		</where>
		order by create_time desc
	</select>
	<select id="selectSpecialNames" resultType="java.lang.String">
		select distinct special_name from common_click_callback_shortcut
	</select>
	<select id="selectOauthVivoAccountByAccount"
			resultType="com.wbgame.controller.jettison.attribution.VivoOAuthDto">
		select account,client_id, client_secret, create_user from common_vivo_oauth_account
		where account = #{state}
	</select>
	<select id="selectOauthVivoAppid"
			resultType="com.wbgame.controller.jettison.attribution.VivoOAuthDto">
		select appid, src_id, account
		from common_vivo_oauth_appid_acc
		where appid = #{appid}
	</select>
	<select id="selectOauthVivoAppidByAccount"
			resultType="com.wbgame.controller.jettison.attribution.VivoOAuthDto">
		select appid, src_id, account
		from common_vivo_oauth_appid_acc
		where account = #{account}
	</select>
	<select id="selectOauthVivoAccounts"
			resultType="com.wbgame.controller.jettison.attribution.VivoOAuthDto">
		select account, client_id, client_secret, token_date
		from common_vivo_oauth_account
		<if test="account != null and account != ''">
			and account like concat('%', #{account}, '%')
		</if>
		order by update_time desc
	</select>
    <select id="selectXiaomiCreativeLowTypeOld" resultType="java.lang.String">
		select distinct lowType from dnwx_adt.dn_xiaomi_creative_type
	</select>
	<select id="selectXiaomiCreativeMidTypeOld" resultType="java.lang.String">
		select distinct midType from dnwx_adt.dn_xiaomi_creative_type
	</select>
	<select id="selectXiaomiCreativeHighTypeOld" resultType="java.lang.String">
		select distinct highType from dnwx_adt.dn_xiaomi_creative_type
	</select>

	<insert id="insertCreativeXiaomiCreativeIdList">
		INSERT IGNORE dnwx_adt.dn_xiaomi_creative_info_temp(creative_id,account,gameName,putUser,transferType,lowType,groupName)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
				#{item.creative_id},#{item.account},#{item.gameName},#{item.putUser},
				#{item.transferType},#{item.lowType},#{item.groupName}
			)
		</foreach>
	</insert>
	<insert id="insertShortcutConfig">
		INSERT IGNORE INTO common_click_callback_shortcut
		(
		appid, `app_category`, media, channel, create_time, create_user, enabled, `events`, shortcut, special_name, update_time, update_user
		)
		VALUES
		(
		#{appid}, #{app_category}, #{media}, #{channel}, now(), #{create_user}, #{enabled}, #{events}, #{shortcut}, #{special_name}, now(), #{update_user}
		)
	</insert>
	<insert id="InsertXiaomiCampaignUpdatePriceRecord">
		INSERT INTO dnwx_adt.dn_xiaomi_campaign_update_price_record
		(
			taskId,operate,updateMethod,amount,gameNames,putUser,account,lowType,transferType,groupName,start_date,end_date,roi_range_min,roi_range_max,spend_range_min,spend_range_max,`limit`
		)
		    values
		    (#{taskId},#{operate},#{updateMethod},#{amount},#{gameNames},#{putUser},#{account},#{lowType},#{transferType},#{groupName},#{start_date},#{end_date},#{roi_range_min},#{roi_range_max},#{spend_range_min},#{spend_range_max},#{limit})
	</insert>
    <insert id="insertOauthVivoAccount">
		insert into common_vivo_oauth_account
		(account,client_id, client_secret, create_user,update_user)
		values
		(#{account},#{client_id},#{client_secret},#{create_user},#{update_user})
	</insert>
	<insert id="insertBudgetUpdateRecord">
		insert into adt_budget_update_records(media,
											  account,
											  planId,
											  operate,
											  operateValue,
											  originValue,
											  requestUrl,
											  response,
											  create_user,
											  stat,
											  message,
											  budget)
		values (
				   #{media},
				   #{account},
				   #{planId},
				   #{operate},
				   #{operateValue},
				   #{originValue},
				   #{requestUrl},
				   #{response},
				   #{create_user},
				   #{stat},
				   #{message},
				   #{budget}
			   )
	</insert>
	<insert id="batchInsertOppoBalanceConfig">
		insert into oppo_account_balance_budget_config
		(account, strategy,  update_user, create_user, enabled)
		values
		<foreach collection="list" item="item" separator=",">
			(
			#{item.account}, #{item.strategy}, #{item.update_user}, #{item.create_user}, #{item.enabled}
			)
		</foreach>
		on duplicate key update strategy = values(strategy), update_user = values(update_user), enabled = values(enabled)
	</insert>

	<select id="selectBudgetUpdateRecord"
			resultType="com.wbgame.pojo.jettison.report.dto.BudgetUpdateRecord">
		select * from adt_budget_update_records
		<where>
			<if test="media != null and media != ''">
				and media = #{media}
			</if>
			<if test="account != null and account != ''">
				and account in (${account})
			</if>
			<if test="planId != null and planId != ''">
				and planId in (${planId})
			</if>
			<if test="operate != null and operate != ''">
				and operate = #{operate}
			</if>
			<if test="create_user != null and create_user != ''">
				and create_user = #{create_user}
			</if>
			<if test="stat != null and stat != ''">
				and stat = #{stat}
			</if>
		</where>
		order by create_time desc
	</select>
	<select id="selectOppoBalanceConfig"
			resultType="com.wbgame.pojo.jettison.report.dto.OppoBalanceConfig">
		select * from oppo_account_balance_budget_config
		<where>
			<if test="account != null and account.size() > 0">
				and account in
				<foreach collection="account" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="strategy != null and strategy != ''">
				and strategy = #{strategy}
			</if>
		    <if test="enabled != null and enabled != ''">
				and enabled = #{enabled}
			</if>
		</where>
	</select>
	<select id="selectBalanceConfig"
			resultType="com.wbgame.pojo.jettison.report.dto.OppoBalanceConfig">
		select a.account, balance, strategy, create_user from adt_account_budget a
				left join oppo_account_balance_budget_config b
				on a.account = b.account
		where enabled = 1
	</select>

    <select id="selectPlatformAccounts" resultType="java.lang.String">
		select account from ${table} where account in
		(
		<foreach collection="accounts" item="item" separator=",">
			#{item}
		</foreach>
		)
	</select>

	<update id="batchUpdatePlatformAccount">
		UPDATE ${table} SET
		<if test="updates.contains('parentAccount')">
			parentAccount = #{parentAccount},
		</if>
		<if test="updates.contains('type')">
			`type` = #{type},
		</if>
		<if test="updates.contains('first_agent')">
			first_agent = #{first_agent},
		</if>
		<if test="updates.contains('agent')">
			agent = #{agent},
		</if>
		<if test="updates.contains('putUser')">
			putUser = #{putUser},
		</if>
		<if test="updates.contains('rebate')">
			rebate = #{rebate},
		</if>
		<if test="updates.contains('remark')">
			remark = #{remark},
		</if>
		<if test="updates.contains('groupId')">
			groupId = #{groupId},
		</if>
		<if test="updates.contains('business')">
			business = #{business},
		</if>
		<if test="updates.contains('strategy')">
			strategy = #{strategy},
		</if>
		<if test="updates.contains('enabled')">
			enabled = #{enabled},
		</if>
		updateUser = #{update_user},
		updateTime = now()
		WHERE account in
		(
		<foreach collection="accounts" item="item" separator=",">
			#{item}
		</foreach>
		)
	</update>

</mapper>