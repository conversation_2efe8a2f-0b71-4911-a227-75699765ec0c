<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.yxcgame.GameMapper">

<!-- ``` 
	自定义测试 selectTest ID_200418120746

	游戏排行数据配置-查询 gameRankSetCkRun ID_201126153501
	-游戏排行数据配置-新增 gameRankSetAdd ID_201126153503
	-游戏排行数据配置-编辑 gameRankSetEdit ID_201126154531
	-游戏排行数据配置-删除 gameRankSetDel ID_201126154532
	
	搜索结果统计-查询模板 SearchResultsSumCkMod ID_201105171734
	-搜索结果统计-查询 SearchResultsSumCkFn ID_201105171712
	-搜索结果统计-统计 SearchResultsSumCkSumFn ID_201105171713
	-搜索结果统计-导出 SearchResultsSumExportFn ID_201105171715

	金币变化查询-查询模板 GoldVariatioQueryModeCk ID_200914171213
	- 金币变化查询-查询 GoldVariatioQueryCk ID_200914170955
	X- 金币变化查询-导出 GoldVariatioQueryExport ID_200914171029

	生成用户查询-查询 newUserQueryCk ID_200811153347
	- 生成用户查询-查询模板 newUserQueryModCk ID_200814122555
	- 生成用户查询-导出 newUserQueryFileExport ID_200811170419
	- 生成用户查询-统计 newUserQuerysum ID_200811173601

	提现审核-查询 cashOutAuditCk ID_200814120329
	-提现审核-查询模板 cashOutAuditModCk ID_200814143603
	-提现审核-统计 cashOutAuditSum ID_200814122433
    -提现审核-导出 cashOutAuditExport ID_200814120330
	-提现审核-操作_审核 cashOutAuditconState ID_200814120331

	金币产出配置-查询 rewardRuleCk ID_200818141044
	-金币产出配置-查询模板 rewardRuleModCk ID_200818141123
	-金币产出配置-统计 rewardRuleModSum ID_200818153222
	-金币产出配置-获取查询条件 rewardRuleCkVal ID_200818141240
	-金币产出配置-删除数据 rewardRuleCleat ID_200818141141
	-金币产出配置-导入 rewardRuleImport ID_200818141159
	X-金币产出配置-导出 rewardRuleExpOpr ID_200818141213

	游戏类型-查询 gameClassCkMp ID_200921145716
    -游戏类型-新增 gameClassAddMp ID_200921145848
    -游戏类型-删除 gameClassDelMp ID_200921152458
    
    游戏明细-查询 gameDetailCkMp ID_200921150000
    -游戏明细-新增 gameDetailAddMp ID_200921150001
    -游戏明细-编辑 gameDetailEditMp ID_200921182504
    -游戏明细-删除 gameDetailDelMp ID_200921152501

    专辑配置-查询  selectAlbumConfigList ID_202010300003
    -专辑配置-新增 AlbumConfigAddMp ID_202010300004
    -专辑配置-编辑 AlbumConfigEditMp ID_202010300005
    -专辑配置-删除 AlbumConfigDelMp ID_202010300006

	活动配置-查询 selectActionConfigList ID_202010301755
	-活动配置-新增 actionConfigAddMp ID_202010301823
	-活动配置-编辑 actionConfigEditMp ID_202010301824
	-专辑配置-删除 actionConfigDelMp ID_202010301825

-->

	
	<!-- 自定义测试 ID_200418120746 -->
	<select id="selectTest" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		SELECT * FROM `yxc_rususer_info` 
	</select>

	<!-- -游戏排行数据配置-查询 gameRankSetCkRun ID_201126153501 -->
	<select id="gameRankSetCkRun" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		SELECT `prjid`,`ver`,`rankType`,`array` 
		FROM `yxc_ranktype_info` where 1=1
		<if test="prjid != null and prjid != ''"> and prjid = #{prjid}</if>
		<if test="ver != null and ver != ''"> and ver = #{ver}</if>
		<if test="rankType != null and rankType != ''"> and rankType = #{rankType}</if>
		<if test="array != null and array != ''"> and array LIKE CONCAT('%','${array}','%' )  </if>
		<choose>
			<when test="order !=null and order != ''">
				ORDER BY ${order}
			</when>
			<otherwise>
				ORDER BY `prjid` DESC
			</otherwise>
		</choose>
	</select>

	<!-- -游戏排行数据配置-新增 gameRankSetAdd ID_201126153503 -->
	<insert id="gameRankSetAdd" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
		INSERT INTO `yxc_ranktype_info` (  
			`prjid`
			,`ver`
			,`rankType`
			<if test="array != null">,`array`</if>
		)VALUES (
			#{prjid}
			,#{ver}
			,#{rankType}
			<if test="array != null">,#{array}</if>
		);
	</insert>

	<!-- -游戏排行数据配置-编辑 gameRankSetEdit ID_201126154531 -->
	<update id="gameRankSetEdit" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
		UPDATE `yxc_ranktype_info` 
		<set >
			<!-- prjid = #{prjid,jdbcType=VARCHAR},
			ver = #{ver,jdbcType=VARCHAR},
			rankType = #{rankType,jdbcType=VARCHAR}, -->
			<if test="array != null" >
				array = #{array,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE
			prjid = #{prjid}
			AND ver = #{ver}
			AND rankType = #{rankType}
	</update>

	<!-- -游戏排行数据配置-删除 gameRankSetDel ID_201126154532 -->
	<delete id="gameRankSetDel" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
		DELETE FROM `yxc_ranktype_info` 
		WHERE
			prjid = #{prjid}
			AND ver = #{ver}
			AND rankType = #{rankType}
	</delete>

	<!-- 搜索结果统计-查询模板 SearchResultsSumCkMod ID_201105171734 -->
	<sql id="SearchResultsSumCkMod">
		SELECT
			keyword AS 'keyword',
			COUNT(*) AS 'keyword_sum',
			COUNT(DISTINCT userId) as 'user_sum',
			result as 'app_list'
		FROM 
			yxc_searchkey_log
		WHERE 1=1 
		<if test ="start_date != null and end_date != null ">
			AND `tdate` BETWEEN #{start_date} AND #{end_date}</if>	
		<if test ="prjid != null and prjid != ''">AND prjid = #{prjid}</if>
		<if test ="ver != null and ver != ''">AND ver = #{ver}</if>
		<if test ="userId != null and userId != ''">AND userId = #{userId}</if>
		<if test ="lsn != null and lsn != ''">AND lsn = #{lsn}</if>
		<if test ="keyword != null and keyword != ''">AND `keyword` LIKE CONCAT('%','${keyword}','%' )</if>
		<if test ="reqip != null and reqip != ''">AND reqip = #{reqip}</if>
		<if test ="createtime != null and createtime != ''">AND createtime = #{createtime}</if>
		GROUP BY 
		keyword
		<if test="order !=null and order != ''"> ORDER BY ${order}</if>
	</sql>

	<!-- -搜索结果统计-查询 SearchResultsSumCkFn ID_201105171712 -->
	<select id="SearchResultsSumCkFn" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		<include refid="SearchResultsSumCkMod"></include>
	</select>

	<!-- -搜索结果统计-统计 SearchResultsSumCkSumFn ID_201105171713 -->
	<select id="SearchResultsSumCkSumFn" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		SELECT 
			sum(`keyword_sum`) keyword_sum,
			sum(`user_sum`) user_sum
		FROM (<include refid="SearchResultsSumCkMod" />) aa
	</select>

	<!-- -搜索结果统计-导出 SearchResultsSumExportFn ID_201105171715 -->
	<select id="SearchResultsSumExportFn" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		<include refid="SearchResultsSumCkMod"></include>
	</select>

	<!-- 金币变化查询-查询模板 GoldVariatioQueryModeCk ID_200914171213 -->
	<sql id="GoldVariatioQueryModeCk">
		<foreach collection="array" item="it" separator=" UNION ALL ">
			SELECT `appid`,`prjid`,`userId`,`lsn`,`openId`,`amount`,`state`,`type`,`gold`,`redBalance`,`date`
			FROM yxc_income_history${it} 
			<where>
				<!-- createtime BETWEEN '${today} 00:00:00' AND '${today} 23:59:59' -->
				<!-- <if test="appid != null and appid != ''"> and appid = #{appid}</if>
				<if test="prjid != null and prjid != ''"> and prjid = #{prjid}</if> -->
				<if test="userId != null and userId != ''"> and userId = #{userId}</if>
				<!-- <if test="lsn != null and lsn != ''"> and lsn = #{lsn}</if>
				<if test="openId != null and openId != ''"> and openId = #{openId}</if>
				<if test="amount != null and amount != ''"> and amount = #{amount}</if>
				<if test="state != null and state != ''"> and state = #{state}</if>
				<if test="type != null and type != ''"> and type = #{type}</if>
				<if test="date != null and date != ''"> and date = #{date}</if> -->
			</where>
		</foreach>
	</sql>

	<!-- 金币变化查询-查询 GoldVariatioQueryCk ID_200914170955 -->
	<select id="GoldVariatioQueryCk" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		SELECT aa.* from (<include refid="GoldVariatioQueryModeCk"/>) aa <!-- ORDER BY aa.date DESC  -->
		<choose>
			<when test="order !=null and order != ''">
				ORDER BY ${order}
			</when>
			<otherwise>
				ORDER BY `date` DESC 
			</otherwise>
		</choose>
	</select>

	<!-- 金币变化查询-导出 GoldVariatioQueryExport ID_200914171029 -->
	<!-- <select id="GoldVariatioQueryExport" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		<include refid="newUserQueryModCk">
		</include>
	</select> -->

	<!-- 生成用户查询-查询 newUserQueryCk ID_200811153347 -->
	<select id="newUserQueryCk" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		<include refid="newUserQueryModCk">
		</include>
	</select>

	<!-- 生成用户查询-导出 newUserQueryFileExport ID_200811170419 -->
	<select id="newUserQueryFileExport" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		<include refid="newUserQueryModCk">
		</include>
	</select>

	<!-- 生成用户查询-统计 newUserQuerysum ID_200811173601 -->
	<select id="newUserQuerysum" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		SELECT 
			sum(`redBalance`) redBalance,sum(`completeNum`) completeNum,sum(`gold`) gold
		FROM `yxc_rususer_info` where 1=1
		<if test ="start_date != null and end_date != null ">
			and `createtime` BETWEEN #{start_date} AND #{end_date}</if>	
		<if test="appid != null and appid != ''"> and appid = #{appid}</if>
		<if test="prjid != null and prjid != ''"> and prjid = #{prjid}</if>
		<if test="userId != null and userId != ''"> and userId = #{userId}</if>
		<if test="lsn != null and lsn != ''"> and lsn = #{lsn}</if>
		<if test="openId != null and openId != ''"> and openId = #{openId}</if>
		<if test="userType != null and userType != ''"> and userType = #{userType}</if>
		<if test="apiToken != null and apiToken != ''"> and apiToken = #{apiToken}</if>
		<if test="nickName != null and nickName != ''"> and nickName = #{nickName}</if>
		<if test="headUrl != null and headUrl != ''"> and headUrl = #{headUrl}</if>
		<if test="platform != null and platform != ''"> and platform = #{platform}</if>
		<if test="completeNum != null and completeNum != ''"> and completeNum = #{completeNum}</if>
		<if test="gold != null and gold != ''"> and gold = #{gold}</if>
		<if test="redBalance != null and redBalance != ''"> and redBalance = #{redBalance}</if>
		<if test="createtime != null and createtime != ''"> and createtime = #{createtime}</if>
		<if test="lasttime != null and lasttime != ''"> and lasttime = #{lasttime}</if>
	</select>
	
	<!-- 生成用户查询-查询模板 newUserQueryModCk ID_200814122555 -->
	<sql id="newUserQueryModCk">
		SELECT 
		`appid`, `prjid`, `userId`, `lsn`, `openId`, `userType`, `apiToken`, `nickName`, `headUrl`,
		`platform`, `completeNum`, `gold`, `redBalance`, `createtime`, `lasttime`
		FROM `yxc_rususer_info` where 1=1
		<if test ="start_date != null and end_date != null ">
			and `createtime` BETWEEN #{start_date} AND #{end_date}</if>	
		<if test="appid != null and appid != ''"> and appid = #{appid}</if>
		<if test="prjid != null and prjid != ''"> and prjid = #{prjid}</if>
		<if test="userId != null and userId != ''"> and userId = #{userId}</if>
		<if test="lsn != null and lsn != ''"> and lsn = #{lsn}</if>
		<if test="openId != null and openId != ''"> and openId = #{openId}</if>
		<if test="userType != null and userType != ''"> and userType = #{userType}</if>
		<if test="apiToken != null and apiToken != ''"> and apiToken = #{apiToken}</if>
		<if test="nickName != null and nickName != ''"> and nickName LIKE CONCAT('%','${nickName}','%' )  </if>
		<if test="headUrl != null and headUrl != ''"> and headUrl = #{headUrl}</if>
		<if test="platform != null and platform != ''"> and platform = #{platform}</if>
		<if test="completeNum != null and completeNum != ''"> and completeNum = #{completeNum}</if>
		<if test="gold != null and gold != ''"> and gold = #{gold}</if>
		<if test="redBalance != null and redBalance != ''"> and redBalance = #{redBalance}</if>
		<!-- <if test="completeType != null and completeType != ''"> and completeType = #{completeType}</if> -->
		<if test="createtime != null and createtime != ''"> and createtime = #{createtime}</if>
		<if test="lasttime != null and lasttime != ''"> and lasttime = #{lasttime}</if>
		<choose>
			<when test="order !=null and order != ''">
				ORDER BY ${order}
			</when>
			<otherwise>
				ORDER BY `createtime` DESC
			</otherwise>
		</choose>
	</sql>

	<!-- 提现审核-查询 cashOutAuditCk ID_200814120329 -->
	<select id="cashOutAuditCk" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		<include refid="cashOutAuditModCk">
		</include>
	</select>

	<!-- 提现审核-导出 cashOutAuditExport ID_200814120330 -->
	<select id="cashOutAuditExport" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		<include refid="cashOutAuditModCk">
		</include>
	</select>

	<!-- 提现审核-统计 cashOutAuditSum ID_200814122433 -->
	<select id="cashOutAuditSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		SELECT 
			sum(`amount`) amount
		FROM `yxc_withdraw_history` WHERE 1=1
		<if test ="start_date != null and end_date != null ">
			and `date` BETWEEN #{start_date} AND #{end_date}</if>	
		<if test="appid != null and appid != ''"> and appid = #{appid}</if>
		<if test="prjid != null and prjid != ''"> and prjid = #{prjid}</if>
		<if test="userId != null and userId != ''"> and userId = #{userId}</if>
		<if test="lsn != null and lsn != ''"> and lsn = #{lsn}</if>
		<if test="openId != null and openId != ''"> and openId = #{openId}</if>
		<if test="nickName != null and nickName != ''"> and nickName = #{nickName}</if>
		<if test="amount != null and amount != ''"> and amount = #{amount}</if>
		<if test="isbad != null and isbad != ''"> and isbad = #{isbad}</if>
		<if test="state != null and state != ''"> and state = #{state}</if>
		<if test="desc != null and desc != ''"> and desc = #{desc}</if>
		<if test="date != null and date != ''"> and date = #{date}</if>
		<if test="id != null and id != ''"> and id = #{id}</if>
	</select>

	<!-- 提现审核-查询模板 cashOutAuditModCk ID_200814143603 -->
	<sql id="cashOutAuditModCk">
		<!-- SELECT 
		`appid`, `prjid`, `userId`, `lsn`, `openId`, `nickName`, `amount`,
		`isbad`, `state`, `desc`, DATE(`date`) AS date, `id`
		FROM `yxc_withdraw_history` WHERE 1=1 -->
		SELECT 
		a.`date`, a.`appid`, a.`prjid`, a.`userId`, a.`lsn`, a.`openId`, a.`nickName`, a.`amount`,
		a.`isbad`, a.`state`, a.`desc`, a.`desc`, a.`id`, b.gold, b.redBalance
		FROM `yxc_withdraw_history` a left join yxc_rususer_info b on a.userId=b.userId
		where 1=1 
		<if test ="start_date != null and end_date != null ">
			and `date` BETWEEN #{start_date} AND #{end_date}</if>	
		<if test="appid != null and appid != ''"> and a.appid = #{appid}</if>
		<if test="prjid != null and prjid != ''"> and a.prjid = #{prjid}</if>
		<if test="userId != null and userId != ''"> and a.userId = #{userId}</if>
		<if test="lsn != null and lsn != ''"> and a.lsn = #{lsn}</if>
		<if test="openId != null and openId != ''"> and a.openId = #{openId}</if>
		<if test="nickName != null and nickName != ''"> and a.nickName = #{nickName}</if>
		<if test="amount != null and amount != ''"> and a.amount = #{amount}</if>
		<if test="isbad != null and isbad != ''"> and a.isbad = #{isbad}</if>
		<if test="state != null and state != ''"> and a.state = #{state}</if>
		<if test="desc != null and desc != ''"> and a.desc = #{desc}</if>
		<if test="gold != null and gold != ''"> and a.gold = #{gold}</if>
		<if test="redBalance != null and redBalance != ''"> and a.redBalance = #{redBalance}</if>
		<if test="date != null and date != ''"> and a.date = #{date}</if>
		<if test="id != null and id != ''"> and a.id = #{id}</if>
		<choose>
			<when test="order !=null and order != ''">
				ORDER BY ${order}
			</when>
			<otherwise>
				ORDER BY `date` DESC
			</otherwise>
		</choose>
	</sql>

	<!-- 提现审核-操作_审核 cashOutAuditconState ID_200814120331 -->
	<update id="cashOutAuditconState" parameterType="com.wbgame.pojo.WaibaoUserVo">
		UPDATE `yxc_withdraw_history` 
		<trim prefix="set" suffixOverrides=",">
			<if test="state != null and state != ''">`state`='${state}',</if>
			<if test="desc != null and desc != ''">`desc`='${desc}',</if>
		</trim>
		WHERE id = #{id}
	</update>

	<!-- 奖励规则区间-查询 rewardRuleCk ID_200818141044 -->
	<select id="rewardRuleCk" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		<include refid="rewardRuleModCk">
		</include>
	</select>

	<!-- 金币产出配置-查询模板 rewardRuleModCk ID_200818141123 -->
	<sql id="rewardRuleModCk">
		SELECT 
		`pid`,
		`ver`,
		`amount_min`,
		`amount_max`,
		`had_min`,
		`had_max`,
		`video_min`,
		`video_max`,
		`coin_min`,
		`coin_max`,
		`mult_min`,
		`mult_max`
		FROM `yxc_reward_range` WHERE 1=1
		<if test="pid != null and pid != ''"> and pid = #{pid}</if>
		<if test="ver != null and ver != ''"> and ver = #{ver}</if>
		<if test="amount_min != null and amount_min != ''"> and amount_min &gt;= #{amount_min}</if>
		<if test="amount_max != null and amount_max != ''"> and amount_max &lt;= #{amount_max}</if>
		<if test="had_min != null and had_min != ''"> and had_min &gt;= #{had_min}</if>
		<if test="had_max != null and had_max != ''"> and had_max &lt;= #{had_max}</if>
		<if test="video_min != null and video_min != ''"> and video_min &gt;= #{video_min}</if>
		<if test="video_max != null and video_max != ''"> and video_max &lt;= #{video_max}</if>
		<if test="coin_min != null and coin_min != ''"> and coin_min &gt;= #{coin_min}</if>
		<if test="coin_max != null and coin_max != ''"> and coin_max &lt;= #{coin_max}</if>
		<if test="mult_min != null and mult_min != ''"> and mult_min &gt;= #{mult_min}</if>
		<if test="mult_max != null and mult_max != ''"> and mult_max &lt;= #{mult_max}</if>
		<if test="order !=null and order != ''"> ORDER BY ${order}</if>
	</sql>

	<!-- -金币产出配置-统计 rewardRuleModSum ID_200818153222 -->
	<select id="rewardRuleModSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		SELECT
		CONCAT('平均:',TRUNCATE(AVG(`amount_min`),2)) `amount_min`,
		CONCAT('平均:',TRUNCATE(AVG(`amount_max`),2)) `amount_max`,
			SUM(`had_min`) `had_min`,
			SUM(`had_max`) `had_max`,
			SUM(`video_min`) `video_min`,
			SUM(`video_max`) `video_max`,
			SUM(`coin_min`) `coin_min`,
			SUM(`coin_max`) `coin_max`,
			SUM(`mult_min`) `mult_min`,
			SUM(`mult_max`) `mult_max`
		FROM `yxc_reward_range` WHERE 1=1
		<if test="amount_min != null and amount_min != ''"> and amount_min = #{amount_min}</if>
		<if test="amount_max != null and amount_max != ''"> and amount_max = #{amount_max}</if>
		<if test="had_min != null and had_min != ''"> and had_min = #{had_min}</if>
		<if test="had_max != null and had_max != ''"> and had_max = #{had_max}</if>
		<if test="video_min != null and video_min != ''"> and video_min = #{video_min}</if>
		<if test="video_max != null and video_max != ''"> and video_max = #{video_max}</if>
		<if test="coin_min != null and coin_min != ''"> and coin_min = #{coin_min}</if>
		<if test="coin_max != null and coin_max != ''"> and coin_max = #{coin_max}</if>
		<if test="mult_min != null and mult_min != ''"> and mult_min = #{mult_min}</if>
		<if test="mult_max != null and mult_max != ''"> and mult_max = #{mult_max}</if>
	</select>

	<!-- -金币产出配置-获取查询条件 rewardRuleCkVal ID_200818141240 -->
	<select id="rewardRuleCkVal" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		SELECT 
		max(`amount_max`) amount,
		max(`had_max`) had,
		max(`video_max`) video,
		max(`coin_max`) coin,
		max(`mult_max`) mult
		FROM `yxc_reward_range`
	</select>

	<!-- -金币产出配置-删除数据 rewardRuleCleat ID_200818141141 -->
	<delete id="rewardRuleCleat" parameterType="java.util.Map">
		<!-- DELETE FROM `yyhz_0308`.`yxc_reward_range` WHERE pid=77 AND ver=66 -->
		<!-- TRUNCATE `yxc_reward_range` -->
		<if test="pid != null and pid != '' and ver != null and ver != ''">
			DELETE FROM `yxc_reward_range` WHERE pid = #{pid} AND ver = #{ver}
		</if>
	</delete>

	<!-- -金币产出配置-导入 rewardRuleImport ID_200818141159 -->
	<insert id="rewardRuleImport" parameterType="com.wbgame.pojo.yxcGame.GameCityVo" >
		REPLACE INTO `yxc_reward_range` (
			`pid`,
			`ver`,
			`amount_min`,
			`amount_max`,
			`had_min`,
			`had_max`,
			`video_min`,
			`video_max`,
			`coin_min`,
			`coin_max`,
			`mult_min`,
			`mult_max`
		)VALUES
		<!-- ( '1', '222', '333', '444', '555', '666', '777', '888', '999', '101010'),
		( '11', '22', '33', '44', '55', '66', '77', '88', '99', '1010'), -->
		<foreach collection="list" item="li" separator=","> 
			(
				#{li.pid,jdbcType=VARCHAR},
				#{li.ver,jdbcType=VARCHAR},
				#{li.amount_min,jdbcType=VARCHAR},
				#{li.amount_max,jdbcType=VARCHAR},
				#{li.had_min,jdbcType=VARCHAR},
				#{li.had_max,jdbcType=VARCHAR},
				#{li.video_min,jdbcType=VARCHAR},
				#{li.video_max,jdbcType=VARCHAR},
				#{li.coin_min,jdbcType=VARCHAR},
				#{li.coin_max,jdbcType=VARCHAR},
				#{li.mult_min,jdbcType=VARCHAR},
				#{li.mult_max,jdbcType=VARCHAR}
			)
		</foreach>
		<!-- ON DUPLICATE KEY UPDATE
			amount_max = VALUES(amount_max),
			had_min = VALUES(had_min),
			had_max = VALUES(had_max),
			video_min = VALUES(video_min),
			video_max = VALUES(video_max),
			coin_min = VALUES(coin_min),
			coin_max = VALUES(coin_max),
			mult_min = VALUES(mult_min),
			mult_max = VALUES(mult_max) -->
	</insert>

	<!-- 游戏类型-查询 gameClassCkMp ID_200921145716 -->
	<select id="gameClassCkMp" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
	SELECT 
		`typeId`,`typeName`,`typeIcon`,`lv`,`array`
	FROM `yxc_gametype_info`  WHERE 1=1
	<if test ="typeId != null and typeId != ''">AND typeId = #{typeId}</if>
	<if test ="lv != null and lv != ''">AND lv = #{lv}</if>
	<if test ="array != null and array != ''">AND array = #{array}</if>
	<if test ="typeName != null and typeName != ''">AND `typeName` LIKE CONCAT('%','${typeName}','%' )</if>
	<if test ="typeIcon != null and typeIcon != ''">AND `typeIcon` LIKE CONCAT('%','${typeIcon}','%' )</if>
	<choose>
		<when test="order !=null and order != ''">
			ORDER BY ${order}
		</when>
		<otherwise>
			<!-- ORDER BY `createtime` DESC -->
		</otherwise>
	</choose>
	</select>

	<!-- 游戏类型-新增 gameClassAddMp ID_200921145848 -->
	<insert id="gameClassAddMp" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
		INSERT INTO `yxc_gametype_info` (  
			`typeId`
			<if test="typeName != null">,`typeName`</if>
			<if test="lv != null">,`lv`</if>
			<if test="typeIcon != null">,`typeIcon`</if>
			<if test="array != null">,`array`</if>
			<!-- ,`orderTime` -->
		)VALUES (
			#{typeId}
			<if test="typeName != null">,#{typeName}</if>
			<if test="lv != null">,#{lv}</if>
			<if test="typeIcon != null">,#{typeIcon}</if>
			<if test="array != null">,#{array}</if>
			<!-- ,unix_timestamp() -->
		);
	</insert>

	<!-- 游戏类型-删除 gameClassDelMp ID_200921152458 -->
	<delete id="gameClassDelMp" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
		DELETE FROM `yxc_gametype_info` 
		WHERE typeId = #{typeId}
		<if test ="typeName != null and typeName != ''">AND typeName = #{typeName}</if>
	</delete>
	
	<select id="selectTypeId" resultType="String">
	SELECT 	DISTINCT `typeId` FROM `yxc_gametype_info` where typeId is not null
	</select>
	
	<update id="gameClassEditMp" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
	update yxc_gametype_info
    set  
		 `typeName` = #{typeName},
		 `typeIcon` = #{typeIcon},
		 `lv` = #{lv},
		 `array` = #{array}
    where `typeId` = #{typeId}
	</update>

	<!-- 游戏明细-查询 gameDetailCkMp ID_200921150000 -->
	<select id="gameDetailCkMp" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		SELECT * 
		FROM `yxc_game_detail`  WHERE 1=1
		<if test ="gameId != null and gameId != ''">AND gameId = #{gameId}</if>
		<if test ="typeId != null and typeId != ''">AND typeId = #{typeId}</if>
		<if test ="priority != null and priority != ''">AND `priority` = #{priority}</if>
		<if test ="statu != null and statu != ''">AND statu = #{statu}</if>
		<if test ="info != null and info != ''">AND `info` LIKE CONCAT('%','${info}','%' )</if>
		<if test ="keyword != null and keyword != ''">AND `keyword` LIKE CONCAT('%','${keyword}','%' )</if>
		<choose>
			<when test="order !=null and order != ''">
				ORDER BY ${order}
			</when>
			<otherwise>
				<!-- ORDER BY `createtime` DESC -->
			</otherwise>
		</choose>
	</select>
	
	<select id="gameDetailList" resultType="com.wbgame.pojo.yxcGame.GameListVo">
	SELECT gameId,json_unquote(json_extract(info, '$.appName')) appName FROM `yxc_game_detail`;
	</select>
	
	<!-- 游戏明细-新增 gameDetailAddMp ID_200921150001 -->
	<insert id="gameDetailAddMp" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
		INSERT INTO `yxc_game_detail` (  
			`gameId`
			<if test="typeId != null">,`typeId`</if>
			<if test="priority != null">,`priority`</if>
			<if test="statu != null">,`statu`</if>
			<if test="info != null">,`info`</if>
			<if test="keyword != null">,`keyword`</if>
			<!-- ,`orderTime` -->
		)VALUES (
			#{gameId}
			<if test="typeId != null">,#{typeId}</if>
			<if test="priority != null">,#{priority}</if>
			<if test="statu != null">,#{statu}</if>
			<if test="info != null">,#{info}</if>
			<if test="keyword != null">,#{keyword}</if>
			<!-- ,unix_timestamp() -->
		);
	</insert>

	<!-- 游戏明细-编辑 gameDetailEditMp ID_200921182504 -->
	<update id="gameDetailEditMp" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
		UPDATE `yxc_game_detail` 
		<set >
			<if test="typeId != null" >
				typeId = #{typeId,jdbcType=VARCHAR},
			</if>
			<if test="priority != null" >
				priority = #{priority,jdbcType=VARCHAR},
			</if>
			<if test="statu != null" >
				statu = #{statu,jdbcType=VARCHAR},
			</if>
			<if test="info != null" >
				info = #{info,jdbcType=VARCHAR},
			</if>
			<if test="keyword != null" >
				keyword = #{keyword,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE (`gameId`='${gameId}') LIMIT 1
	</update>

	<!-- 游戏明细-删除 gameDetailDelMp ID_200921152501 -->
	<delete id="gameDetailDelMp" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
		DELETE FROM `yxc_game_detail` 
		WHERE gameId = #{gameId}
		<if test ="typeId != null and typeId != ''">AND typeId = #{typeId}</if>
		<if test ="priority != null and priority != ''">AND priority = #{priority}</if>
		<if test ="statu != null and statu != ''">AND statu = #{statu}</if>
		<!-- <if test ="info != null and info != ''">AND info = #{info}</if> -->
	</delete>
	
	

	<!-- 专辑配置-查询 ID_202010300003 -->
	<select id="selectAlbumConfigList" resultType="com.wbgame.pojo.yxcGame.AlbumConfigVo">
		select albumId,albumName,albumIconUrl,albumDesc,priority,array
		from yxc_gamealbum_info
		<where>1=1
			<if test ="albumId != null and albumId != ''">
			AND albumId = #{albumId}
			</if>
			<if test ="albumName != null and albumName != ''">
			AND albumName LIKE CONCAT('%','${albumName}','%' )
			</if>
		</where>
	</select>


	<!-- 专辑配置-新增 ID_202010300004 -->
	<insert id="AlbumConfigAddMp" parameterType="com.wbgame.pojo.yxcGame.AlbumConfigVo">
		INSERT INTO `yxc_gamealbum_info` (
		`albumId`
		<if test="albumName != null">,`albumName`</if>
		<if test="albumIconUrl != null">,`albumIconUrl`</if>
		<if test="albumDesc != null">,`albumDesc`</if>
		<if test="priority != null and priority != ''">,`priority`</if>
		<if test="array != null">,`array`</if>
		)VALUES (
		#{albumId}
		<if test="albumName != null">,#{albumName}</if>
		<if test="albumIconUrl != null">,#{albumIconUrl}</if>
		<if test="albumDesc != null">,#{albumDesc}</if>
		<if test="priority != null and priority != ''">,#{priority}</if>
		<if test="array != null">,#{array}</if>
		);
	</insert>


	<!-- 专辑配置-编辑 ID_202010300005 -->
	<update id="AlbumConfigEditMp" parameterType="com.wbgame.pojo.yxcGame.AlbumConfigVo">
		UPDATE `yxc_gamealbum_info`
		<set >
			<if test="albumName != null" >
				albumName = #{albumName,jdbcType=VARCHAR},
			</if>
			<if test="albumIconUrl != null" >
				albumIconUrl = #{albumIconUrl,jdbcType=VARCHAR},
			</if>
			<if test="albumDesc != null" >
				albumDesc = #{albumDesc,jdbcType=VARCHAR},
			</if>
			<if test="priority != null and priority != ''" >
				priority = #{priority,jdbcType=INTEGER},
			</if>
			<if test="array != null" >
				array = #{array,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE albumId= #{albumId}
	</update>


	<!-- 专辑配置-删除 ID_202010300006 -->
	<delete id="AlbumConfigDelMp" parameterType="com.wbgame.pojo.yxcGame.AlbumConfigVo">
		DELETE FROM `yxc_gamealbum_info`
		WHERE albumId = #{albumId}
	</delete>

	<!-- 活动配置-查询 ID_202010301755 -->
	<select id="selectActionConfigList" resultType="com.wbgame.pojo.yxcGame.ActionConfigVo">
            select prjid,ver,config,signkey,day_sum,day_count,excRate,awardLimit,taskList,activeTaskList,appCheck
            from yxc_action_config
            <where>1=1
				<if test ="prjid != null and prjid != ''">
					AND prjid = #{prjid}
				</if>
				<if test ="ver != null and ver != ''">
					AND ver = #{ver}
				</if>
			</where>
	</select>

    <!-- 活动配置-新增 ID_202010301823 -->
	<insert id="actionConfigAddMp">
		INSERT INTO `yxc_action_config` (
		`prjid`,ver
		<if test="config != null">,`config`</if>
		<if test="signkey != null">,`signkey`</if>
		<if test="day_sum != null and day_sum != ''">,`day_sum`</if>
		<if test="day_count != null and day_count != ''">,`day_count`</if>
		<if test="excRate != null and excRate != ''">,`excRate`</if>
		<if test="awardLimit != null and awardLimit != ''">,`awardLimit`</if>
		<if test="taskList != null">,`taskList`</if>
		<if test="activeTaskList != null">,`activeTaskList`</if>
		<if test="appCheck != null">,`appCheck`</if>
		)VALUES (
		#{prjid},#{ver}
		<if test="config != null">,#{config}</if>
		<if test="signkey != null">,#{signkey}</if>
		<if test="day_sum != null and day_sum != ''">,#{day_sum}</if>
		<if test="day_count != null and day_count != ''">,#{day_count}</if>
		<if test="excRate != null and excRate != ''">,#{excRate}</if>
		<if test="awardLimit != null and awardLimit != ''">,#{awardLimit}</if>
		<if test="taskList != null">,#{taskList}</if>
		<if test="activeTaskList != null">,#{activeTaskList}</if>
		<if test="appCheck != null">,#{appCheck}</if>
		);
	</insert>

	<!-- 活动配置-编辑 ID_202010301824 -->
	<update id="actionConfigEditMp">
		UPDATE `yxc_action_config`
		<set >
			<if test="config != null" >
				config = #{config,jdbcType=VARCHAR},
			</if>
			<if test="signkey != null" >
				signkey = #{signkey,jdbcType=VARCHAR},
			</if>
			<if test="day_sum != null and day_sum != ''" >
				day_sum = #{day_sum,jdbcType=INTEGER},
			</if>
			<if test="day_count != null and day_count != ''" >
				day_count = #{day_count,jdbcType=INTEGER},
			</if>
			<if test="excRate != null and excRate != ''" >
				excRate = #{excRate,jdbcType=INTEGER},
			</if>
			<if test="awardLimit != null and awardLimit != ''" >
				awardLimit = #{awardLimit,jdbcType=INTEGER},
			</if>
			<if test="taskList != null" >
				taskList = #{taskList,jdbcType=VARCHAR},
			</if>
			<if test="activeTaskList != null" >
				activeTaskList = #{activeTaskList,jdbcType=VARCHAR},
			</if>
			<if test="appCheck != null" >
				appCheck = #{appCheck,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE prjid= #{prjid} and ver= #{ver}
	</update>

	<!-- 活动配置-删除 ID_202010301825 -->
	<delete id="actionConfigDelMp">
        DELETE FROM `yxc_action_config`
		WHERE prjid = #{prjid} and ver = #{ver} limit 1
	</delete>

	<select id="selectXycPromotions" resultType="java.util.Map">
		select * from yxc_promotion_config where 1=1
		<if test="game_id != null and game_id != ''">
			and game_id in (${game_id})
		</if>
		<if test="status != null and status != ''">
			and status = #{status}
		</if>
		<choose>
			<when test="order !=null and order != ''">
				ORDER BY ${order}
			</when>
			<otherwise>
				ORDER BY `create_time` DESC
			</otherwise>
		</choose>
	</select>

	<insert id="addXycPromotions">
		insert into yxc_promotion_config(game_id,image_url,promotion,create_owner,update_owner,create_time,update_time,status)
		values
		(#{game_id},#{image_url},#{promotion},#{create_owner},#{update_owner},now(),now(),#{status})
	</insert>

	<update id="updateXycPromotions">
		update yxc_promotion_config set
		image_url = #{image_url},
		promotion = #{promotion},
		update_owner = #{update_owner},
		update_time = now(),
		status = #{status}
		where game_id = #{game_id}
	</update>

	<delete id="deleteXycPromotions">
		delete from yxc_promotion_config where game_id = #{game_id}
	</delete>

	<update id="updatePromotionSwitch">
		update yxc_promotion_config set
		update_owner = #{update_owner},
		update_time = now(),
		status = #{status}
		where game_id in (${ids})
	</update>
	
</mapper>