<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.yxcgame.GameMapper2">

<!-- ``` 
   
    -游戏明细-查询collectionDetailCkMp
    -游戏明细-新增collectionDetailAddMp
    -游戏明细-编辑collectionDetailEditMp
    -游戏明细-删除collectionDetailDelMp

-->

	<select id="collectionDetailList" resultType="com.wbgame.pojo.yxcGame.GameListVo">
		SELECT gameId,json_unquote(json_extract(info, '$.appName')) appName FROM `yxc_collection_detail`;
	</select>
	
	<!-- 游戏明细-查询collectionDetailCkMp -->
	<select id="collectionDetailCkMp" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		SELECT * 
		FROM `yxc_collection_detail` WHERE 1=1 
		<if test ="gameId != null and gameId != ''"> AND gameId = #{gameId}</if>
		<if test ="priority != null and priority != ''"> AND `priority` LIKE CONCAT('%','${priority}','%' )</if>
		<if test ="statu != null and statu != ''"> AND statu = #{statu}</if>
		<if test ="info != null and info != ''"> AND `info` LIKE CONCAT('%',#{info},'%')</if>
		<choose>
			<when test="order !=null and order != ''">
				ORDER BY ${order}
			</when>
			<otherwise>
				<!-- ORDER BY `createtime` DESC -->
			</otherwise>
		</choose>
	</select>
	
	
	<!-- 游戏明细-新增 collectionDetailAddMp -->
	<insert id="collectionDetailAddMp" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
		INSERT INTO `yxc_collection_detail` (  
			`gameId`
			<if test="priority != null">,`priority`</if>
			<if test="statu != null">,`statu`</if>
			<if test="info != null">,`info`</if>
			<!-- ,`orderTime` -->
		)VALUES (
			#{gameId}
			<if test="priority != null">,#{priority}</if>
			<if test="statu != null">,#{statu}</if>
			<if test="info != null">,#{info}</if>
			<!-- ,unix_timestamp() -->
		);
	</insert>

	<!-- 游戏明细-编辑 collectionDetailEditMp -->
	<update id="collectionDetailEditMp" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
		UPDATE `yxc_collection_detail` 
		<set >
			<if test="priority != null" >
				priority = #{priority,jdbcType=VARCHAR},
			</if>
			<if test="statu != null" >
				statu = #{statu,jdbcType=VARCHAR},
			</if>
			<if test="info != null" >
				info = #{info,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE `gameId`=#{gameId} 
	</update>

	<!-- 游戏明细-删除 collectionDetailDelMp -->
	<delete id="collectionDetailDelMp" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
		DELETE FROM `yxc_collection_detail` 
		WHERE gameId = #{gameId}
		<if test ="typeId != null and typeId != ''">AND typeId = #{typeId}</if>
		<if test ="priority != null and priority != ''">AND priority = #{priority}</if>
		<if test ="statu != null and statu != ''">AND statu = #{statu}</if>
		<!-- <if test ="info != null and info != ''">AND info = #{info}</if> -->
	</delete>
	
	
	<!-- -小合集游戏数据配置-查询 collectionGameSetCkRun -->
	<select id="collectionGameSetCkRun" parameterType="java.util.Map" resultType="com.wbgame.pojo.yxcGame.GameCityVo">
		SELECT * 
		FROM `yxc_collection_info` where 1=1 
		<if test="prjid != null and prjid != ''"> and prjid = #{prjid}</if>
		<if test="ver != null and ver != ''"> and ver = #{ver}</if>
		<if test="array != null and array != ''"> and array LIKE CONCAT('%',#{array},'%') </if>
		<choose>
			<when test="order !=null and order != ''">
				ORDER BY ${order}
			</when>
			<otherwise>
				ORDER BY `prjid` DESC
			</otherwise>
		</choose>
	</select>

	<!-- -小合集游戏数据配置-新增 collectionGameSetAdd -->
	<insert id="collectionGameSetAdd" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
		INSERT INTO `yxc_collection_info` (  
			`prjid`
			,`ver`
			,`priority`
			,`array`
			,localGames
			,adIntervalNum
		)VALUES (
			#{prjid}
			,#{ver}
			,#{priority}
			,#{array}
			,#{localGames}
			,#{adIntervalNum}
		);
	</insert>

	<!-- -小合集游戏数据配置-编辑 collectionGameSetEdit -->
	<update id="collectionGameSetEdit" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
		UPDATE `yxc_collection_info` SET 
			priority=#{priority},array=#{array},localGames=#{localGames},adIntervalNum=#{adIntervalNum}
		WHERE
			prjid = #{prjid}
			AND ver = #{ver}
	</update>

	<!-- -小合集游戏数据配置-删除 collectionGameSetDel -->
	<delete id="collectionGameSetDel" parameterType="com.wbgame.pojo.yxcGame.GameCityVo">
		DELETE FROM `yxc_collection_info` 
		WHERE
			prjid = #{prjid}
			AND ver = #{ver}
	</delete>
	
	
</mapper>