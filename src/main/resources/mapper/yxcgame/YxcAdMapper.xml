<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.yxcgame.YxcAdMapper">

	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>

	<select id="selectGameEventInfo" parameterType="java.util.Map" resultType="java.util.Map">
		select * 
		from ${tableName}  
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid} 
		</if>
		<if test="eventId != null and eventId != ''">
			and eventId like concat('%',#{eventId},'%') 
		</if>
		<if test="eventMsg != null and eventMsg != ''">
			and eventMsg like concat('%',#{eventMsg},'%') 
		</if>
		<if test="ctype != null and ctype != ''">
			and ctype = #{ctype} 
		</if>
		
	</select>
	
	<!-- 事件统计结果入库  -->
	<insert id="insertGameLaunchInfo" parameterType="java.util.Map">
		replace into tj_gamelaunch_info 
		SELECT aa.tdate,aa.prjid,aa.eventId,'1次',COUNT(DISTINCT andrId) '人数',SUM(aa.num) '总次数',TRUNCATE(SUM(aa.num)/COUNT(DISTINCT andrId),2) '人均启动次数' from
			(SELECT * FROM `yxc_eventpost_log` where tdate BETWEEN DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND prjid != 100 AND prjid != 333351
			GROUP BY tdate,prjid,andrId,eventId HAVING COUNT(eventMsg) = 1) aa 
		WHERE aa.eventId like 'onGame%'
		GROUP BY aa.tdate,aa.prjid,aa.eventId 
		
		UNION ALL
		
		SELECT aa.tdate,aa.prjid,aa.eventId,'2次',COUNT(DISTINCT andrId) '人数',SUM(aa.num) '总次数',TRUNCATE(SUM(aa.num)/COUNT(DISTINCT andrId),2) '人均启动次数' from
			(SELECT * FROM `yxc_eventpost_log` where tdate BETWEEN DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND prjid != 100 AND prjid != 333351
			GROUP BY tdate,prjid,andrId,eventId HAVING COUNT(eventMsg) = 2) aa 
		WHERE aa.eventId like 'onGame%'
		GROUP BY aa.tdate,aa.prjid,aa.eventId 
		
		UNION ALL
		
		SELECT aa.tdate,aa.prjid,aa.eventId,'3次',COUNT(DISTINCT andrId) '人数',SUM(aa.num) '总次数',TRUNCATE(SUM(aa.num)/COUNT(DISTINCT andrId),2) '人均启动次数' from
			(SELECT * FROM `yxc_eventpost_log` where tdate BETWEEN DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND prjid != 100 AND prjid != 333351
			GROUP BY tdate,prjid,andrId,eventId HAVING COUNT(eventMsg) = 3) aa 
		WHERE aa.eventId like 'onGame%'
		GROUP BY aa.tdate,aa.prjid,aa.eventId 
		
		UNION ALL
		
		SELECT aa.tdate,aa.prjid,aa.eventId,'大于3次',COUNT(DISTINCT andrId) '人数',SUM(aa.num) '总次数',TRUNCATE(SUM(aa.num)/COUNT(DISTINCT andrId),2) '人均启动次数' from
			(SELECT * FROM `yxc_eventpost_log` where tdate BETWEEN DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND prjid != 100 AND prjid != 333351
			GROUP BY tdate,prjid,andrId,eventId HAVING COUNT(eventMsg) > 3) aa 
		WHERE aa.eventId like 'onGame%'
		GROUP BY aa.tdate,aa.prjid,aa.eventId
	</insert>
	
	<insert id="insertGameEventInfo" parameterType="java.util.Map">
		replace into tj_gameevent_info 
		SELECT tdate,prjid,aa.eventId,COUNT(DISTINCT andrId) '人数',SUM(aa.num) '总次数',TRUNCATE(SUM(aa.num)/COUNT(DISTINCT andrId),2) '人均启动次数' 
		from `yxc_eventpost_log` aa where tdate BETWEEN DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND prjid != 100 AND prjid != 333351
		GROUP BY tdate,prjid,eventId
		ORDER BY tdate,prjid,eventId,COUNT(DISTINCT andrId) desc
		
	</insert>
	
	<insert id="insertGameRankInfo" parameterType="java.util.Map">
		replace into tj_gamerank_info
		(SELECT tdate,prjid,eventId,eventMsg,'人数排行',COUNT(DISTINCT andrId) '人数' FROM `yxc_eventpost_log` 
		where tdate BETWEEN DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND prjid != 100 AND prjid != 333351 and (eventId like 'onGame%' or eventId = 'onStartInstallEvent')
		GROUP BY tdate,prjid,eventId,eventMsg ORDER BY tdate,prjid,eventId,COUNT(DISTINCT andrId) desc limit 999999999)
		
		union all
		
		(SELECT tdate,prjid,eventId,eventMsg,'次数排行',SUM(num) '次数' FROM `yxc_eventpost_log` 
		where tdate BETWEEN DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND prjid != 100 AND prjid != 333351 and (eventId like 'onGame%' or eventId = 'onStartInstallEvent')
		GROUP BY tdate,prjid,eventId,eventMsg ORDER BY tdate,prjid,eventId,SUM(num) desc limit 999999999)
	</insert>
	
	
</mapper>