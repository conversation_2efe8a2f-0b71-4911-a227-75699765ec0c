<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.track.OverseaReportMapper">


	<insert id="insertPlacementReport" parameterType="java.util.List">
		INSERT INTO placement_report (
			tdate, channel, media, account, appid, campaign_id, campaign_name, group_id,
			group_name, placement, country, area, app_id, country_code, promotion_platform,
			agent, putUser, gameName, company, accountSubject, adsensePosition, adsenseType, strategy,
			spend, installs, activeUser, `show`, click, conversions,
			ad_revenue_d0, ad_revenue_d1, ad_revenue_d2, ad_revenue_d3, ad_revenue_d4,
			ad_revenue_d5, ad_revenue_d6, ad_revenue_d7, ad_revenue_d14, ad_revenue_d30,
			ad_revenue_total, retention_d1, retention_d2, retention_d3, retention_d4,
			retention_d5, retention_d6, retention_d7, retention_d14, retention_d30
		) VALUES
		<foreach collection="list" item="item" separator=",">
			(
				#{item.tdate}, #{item.channel}, #{item.media}, #{item.account}, #{item.appid},
				#{item.campaign_id}, #{item.campaign_name}, #{item.group_id},
				#{item.group_name}, #{item.placement}, #{item.country}, #{item.area},
				#{item.app_id}, #{item.country_code}, #{item.promotion_platform},
				#{item.agent}, #{item.putUser}, #{item.gameName}, #{item.company},
				#{item.accountSubject}, #{item.adsensePosition}, #{item.adsenseType},
				#{item.strategy}, #{item.spend}, #{item.installs}, #{item.activeUser},
				#{item.show}, #{item.click}, #{item.conversions},
				#{item.ad_revenue_d0}, #{item.ad_revenue_d1}, #{item.ad_revenue_d2},
				#{item.ad_revenue_d3}, #{item.ad_revenue_d4}, #{item.ad_revenue_d5},
				#{item.ad_revenue_d6}, #{item.ad_revenue_d7}, #{item.ad_revenue_d14},
				#{item.ad_revenue_d30}, #{item.ad_revenue_total},
				#{item.retention_d1}, #{item.retention_d2}, #{item.retention_d3},
				#{item.retention_d4}, #{item.retention_d5}, #{item.retention_d6},
				#{item.retention_d7}, #{item.retention_d14}, #{item.retention_d30}
			)
		</foreach>
	</insert>
	<delete id="deletePlacementReport">
		DELETE FROM placement_report WHERE tdate = #{tdate}
	</delete>
	<select id="selectOverSeaCampaignReport" resultType="com.wbgame.pojo.advert.oversea.OverseaPlacementReport">
		select
         IFNULL(convert(sum(spend),decimal(20,3)),0.00) spend,sum(activeUser) active_user,sum(`show`) show_num,sum(click) click
		     ,sum(installs) installs
        ,IFNULL(convert(sum(spend)/sum(installs),decimal(20,3)),0.00) as cpi
        ,IFNULL(convert(sum(spend)/sum(activeUser),decimal(20,3)),0.00) as active_cost
        ,IFNULL(convert(sum(spend)/sum(`show`)*1000,decimal(20,3)),0.00) as ecpm
        ,IFNULL(convert(sum(click)/sum(`show`)*100,decimal(20,2)),0.00) as ctr
        ,IFNULL(convert(sum(activeUser)/sum(click)*100,decimal(20,2)),0.00) as cvr
        ,IFNULL(convert(sum(ad_revenue_d0)/sum(spend)*100,decimal(20,2)),0.00) as roas_0
        ,IFNULL(convert(sum(ad_revenue_d1)/sum(spend)*100,decimal(20,2)),0.00) as roas_1
        ,IFNULL(convert(sum(ad_revenue_d2)/sum(spend)*100,decimal(20,2)),0.00) as roas_2
        ,IFNULL(convert(sum(ad_revenue_d3)/sum(spend)*100,decimal(20,2)),0.00) as roas_3
        ,IFNULL(convert(sum(ad_revenue_d4)/sum(spend)*100,decimal(20,2)),0.00) as roas_4
        ,IFNULL(convert(sum(ad_revenue_d5)/sum(spend)*100,decimal(20,2)),0.00) as roas_5
        ,IFNULL(convert(sum(ad_revenue_d6)/sum(spend)*100,decimal(20,2)),0.00) as roas_6
        ,IFNULL(convert(sum(ad_revenue_d7)/sum(spend)*100,decimal(20,2)),0.00) as roas_7
        ,IFNULL(convert(sum(ad_revenue_d14)/sum(spend)*100,decimal(20,2)),0.00) as roas_14
        ,IFNULL(convert(sum(ad_revenue_d30)/sum(spend)*100,decimal(20,2)),0.00) as roas_30
        ,IFNULL(convert(sum(ad_revenue_total)/sum(spend)*100,decimal(20,2)),0.00) as roas_total

        ,IFNULL(convert(sum(retention_d1)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r1
        ,IFNULL(convert(sum(retention_d2)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r2
        ,IFNULL(convert(sum(retention_d3)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r3
        ,IFNULL(convert(sum(retention_d4)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r4
        ,IFNULL(convert(sum(retention_d5)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r5
        ,IFNULL(convert(sum(retention_d6)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r6
        ,IFNULL(convert(sum(retention_d7)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r7
        ,IFNULL(convert(sum(retention_d14)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r14
        ,IFNULL(convert(sum(retention_d30)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r30
        <if test="custom_date != null and custom_date.size() > 0">
            ,concat(#{start_date},'至',#{end_date}) as tdate
        </if>
        <if test="group != null and group.size > 0">
            <foreach collection="group" index="index" item="item">
                <choose>
                    <when test="item == 'week'">
                        ,DATE_FORMAT(tdate, '%x-%v') as tdate
                        ,DATE_FORMAT(tdate, '%x-%v') as week
                    </when>
                    <when test="item == 'month'">
                        ,DATE_FORMAT(tdate,'%Y-%m') as tdate
                        ,DATE_FORMAT(tdate,'%Y-%m') as month
                    </when>
                    <when test="item == 'beek'">
                        ,CONCAT(DATE_FORMAT(tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 1,UNSIGNED),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 2,UNSIGNED),2,"0"))  AS tdate
                        ,CONCAT(DATE_FORMAT(tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 1,UNSIGNED),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 2,UNSIGNED),2,"0"))  AS beek
                    </when>
                    <when test="item == 'appid'">
                        ,appid,app_name
                    </when>
                    <when test="item == 'country'">
                        ,country,country_name
                    </when>
                    <when test="item == 'campaign_id'">
                        ,campaign_id,campaign_name
                    </when>
                    <when test="item == 'group_id'">
                        ,group_id,group_name
                    </when>
                    <when test="item == 'placement'">
						,placement
					</when>
                    <otherwise>
                        ,${item}
                    </otherwise>
                </choose>
            </foreach>
        </if>
        from placement_report a left join app_info b on a.appid = b.id left join dim_country c on a.country = c.country_code
        <include refid="overSeaCampaignWhere"/>
        <if test="group != null and group.size > 0">
            GROUP BY
            <foreach collection="group" index="index" item="item" separator=",">
                `${item}`
            </foreach>
        </if>
        <if test="order_str == null or order_str == ''">
            ORDER BY `tdate` ASC, spend DESC
        </if>
        <if test="order_str != null and order_str != ''">
            ORDER BY ${order_str}
        </if>
	</select>
	<select id="selectOverSeaCampaignSummary"
			resultType="com.wbgame.pojo.advert.oversea.OverseaPlacementReport">
select  IFNULL(convert(sum(spend),decimal(20,3)),0.00) spend,sum(activeUser) active_user,sum(`show`) show_num,sum(click) click
        ,sum(installs) installs
        ,IFNULL(convert(sum(spend)/sum(installs),decimal(20,3)),0.00) as cpi
        ,IFNULL(convert(sum(spend)/sum(activeUser),decimal(20,3)),0.00) as active_cost
        ,IFNULL(convert(sum(spend)/sum(`show`)*1000,decimal(20,3)),0.00) as ecpm
        ,IFNULL(convert(sum(click)/sum(`show`)*100,decimal(20,2)),0.00) as ctr
        ,IFNULL(convert(sum(activeUser)/sum(click)*100,decimal(20,2)),0.00) as cvr
        ,IFNULL(convert(sum(ad_revenue_d0)/sum(spend)*100,decimal(20,2)),0.00) as roas_0
        ,IFNULL(convert(sum(ad_revenue_d1)/sum(spend)*100,decimal(20,2)),0.00) as roas_1
        ,IFNULL(convert(sum(ad_revenue_d2)/sum(spend)*100,decimal(20,2)),0.00) as roas_2
        ,IFNULL(convert(sum(ad_revenue_d3)/sum(spend)*100,decimal(20,2)),0.00) as roas_3
        ,IFNULL(convert(sum(ad_revenue_d4)/sum(spend)*100,decimal(20,2)),0.00) as roas_4
        ,IFNULL(convert(sum(ad_revenue_d5)/sum(spend)*100,decimal(20,2)),0.00) as roas_5
        ,IFNULL(convert(sum(ad_revenue_d6)/sum(spend)*100,decimal(20,2)),0.00) as roas_6
        ,IFNULL(convert(sum(ad_revenue_d7)/sum(spend)*100,decimal(20,2)),0.00) as roas_7
        ,IFNULL(convert(sum(ad_revenue_d14)/sum(spend)*100,decimal(20,2)),0.00) as roas_14
        ,IFNULL(convert(sum(ad_revenue_d30)/sum(spend)*100,decimal(20,2)),0.00) as roas_30
        ,IFNULL(convert(sum(ad_revenue_total)/sum(spend)*100,decimal(20,2)),0.00) as roas_total

        ,IFNULL(convert(sum(retention_d1)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r1
        ,IFNULL(convert(sum(retention_d2)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r2
        ,IFNULL(convert(sum(retention_d3)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r3
        ,IFNULL(convert(sum(retention_d4)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r4
        ,IFNULL(convert(sum(retention_d5)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r5
        ,IFNULL(convert(sum(retention_d6)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r6
        ,IFNULL(convert(sum(retention_d7)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r7
        ,IFNULL(convert(sum(retention_d14)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r14
        ,IFNULL(convert(sum(retention_d30)/sum(activeUser)*100,decimal(20,2)),0.00) as retention_r30
        from placement_report
        <include refid="overSeaCampaignWhere"/>
	</select>

<sql id="overSeaCampaignWhere">
        where tdate <![CDATA[ >= ]]> #{start_date} AND tdate <![CDATA[ <= ]]> #{end_date}
        <if test="appid != null and appid.size > 0">
            AND appid IN
            <foreach collection="appid" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="account != null and account.size > 0">
            AND account IN
            <foreach collection="account" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="country != null and country.size > 0">
            AND country IN
            <foreach collection="country" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="area != null and area.size > 0">
            AND area IN
            <foreach collection="area" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="channel != null and channel.size > 0">
            AND channel IN
            <foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="campaign_id != null and campaign_id != ''">
            and campaign_id = #{campaign_id}
        </if>
        <if test="campaign_name != null and campaign_name != ''">
            and campaign_name like concat('%',#{campaign_name},'%')
        </if>
        <if test="group_id != null and group_id != ''">
            and group_id = #{group_id}
        </if>
        <if test="group_name != null and group_name != ''">
            and group_name like concat('%',#{group_name},'%')
        </if>
        <if test="placement != null and placement != ''">
			and placement like concat('%',#{placement},'%')
		</if>
		<if test="agent != null and agent.size > 0">
            AND agent IN
            <foreach collection="agent" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="putUser != null and putUser.size > 0">
            AND putUser IN
            <foreach collection="putUser" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="company != null and company.size > 0">
            AND company IN
            <foreach collection="company" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="accountSubject != null and accountSubject.size > 0">
            AND accountSubject IN
            <foreach collection="accountSubject" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="adsensePosition != null and adsensePosition.size > 0">
            AND adsensePosition IN
            <foreach collection="adsensePosition" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="adsenseType != null and adsenseType.size > 0">
            AND adsenseType IN
            <foreach collection="adsenseType" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="strategy != null and strategy.size > 0">
            AND strategy IN
            <foreach collection="strategy" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="gameName != null and gameName != ''">
            and gameName like concat('%',#{gameName},'%')
        </if>
    </sql>

</mapper>