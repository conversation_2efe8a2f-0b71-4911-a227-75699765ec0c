<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.haiwai2adb.Haiwai2DnwxBiUsMapper" >

    <insert id="insertUmengAdIncomeList" parameterType="java.util.List">
        replace into umeng_ad_income (
        tdate,appid,appname,appkey,channel,media,actnum,total_income,dau_arpu,gameName,
        banner_pv,plaque_pv,splash_pv,video_pv,banner_arpu,plaque_arpu,splash_arpu,
        video_arpu,banner_ecpm,plaque_ecpm,splash_ecpm,
        native_banner_ecpm,native_plaque_ecpm,native_splash_ecpm,
        video_ecpm,banner_show,plaque_show,splash_show,native_banner_show,
        native_plaque_show,native_splash_show,video_show,banner_income,
        plaque_income,splash_income,native_banner_income,native_plaque_income,
        native_splash_income,video_income,
        plaque_video_show,plaque_video_ecpm,plaque_video_income,
        msg_pv, msg_arpu, native_msg_ecpm, native_msg_show, native_msg_income,
        plaque_video_pv, plaque_video_arpu,addnum,avgnum,
        banner_click,plaque_click,splash_click,native_banner_click,native_plaque_click,
        native_splash_click,video_click,plaque_video_click,native_msg_click,
        system_splash_show,native_new_plaque_show,native_new_banner_show,suspend_icon_show,
        system_splash_pv,native_new_plaque_pv,native_new_banner_pv,suspend_icon_pv,
        system_splash_arpu,native_new_plaque_arpu,native_new_banner_arpu,suspend_icon_arpu,
        system_splash_ecpm,native_new_plaque_ecpm,native_new_banner_ecpm,suspend_icon_ecpm,
        system_splash_income,native_new_plaque_income,native_new_banner_income,suspend_icon_income,
        system_splash_click,native_new_plaque_click,native_new_banner_click,suspend_icon_click,
        native_plaque_pv,native_banner_pv,temp_id,temp_name,source,active_temp_id,active_temp_name,
        banner_request,plaque_request,splash_request,video_request,native_banner_request,native_plaque_request,
        native_splash_request,plaque_video_request,native_msg_request,system_splash_request,native_new_plaque_request,
        native_new_banner_request,suspend_icon_request,banner_fill,plaque_fill,splash_fill,video_fill,
        native_banner_fill,native_plaque_fill,native_splash_fill,plaque_video_fill,native_msg_fill,
        system_splash_fill,native_new_plaque_fill,native_new_banner_fill,suspend_icon_fill,

        country
        ) values
        <foreach collection="list" item="li" separator=",">
            (#{li.tdate},#{li.appid},#{li.appname},#{li.appkey},#{li.channel},#{li.media},#{li.actnum},
            #{li.total_income},#{li.dau_arpu},#{li.gameName},#{li.banner_pv},#{li.plaque_pv},
            #{li.splash_pv},#{li.video_pv},#{li.banner_arpu},#{li.plaque_arpu},
            #{li.splash_arpu},#{li.video_arpu},#{li.banner_ecpm},#{li.plaque_ecpm},
            #{li.splash_ecpm},#{li.native_banner_ecpm},#{li.native_plaque_ecpm},
            #{li.native_splash_ecpm},#{li.video_ecpm},#{li.banner_show},#{li.plaque_show},
            #{li.splash_show},#{li.native_banner_show},#{li.native_plaque_show},
            #{li.native_splash_show},#{li.video_show},#{li.banner_income},#{li.plaque_income},
            #{li.splash_income},#{li.native_banner_income},#{li.native_plaque_income},
            #{li.native_splash_income},#{li.video_income},
            #{li.plaque_video_show},#{li.plaque_video_ecpm},#{li.plaque_video_income},
            #{li.msg_pv},#{li.msg_arpu},#{li.native_msg_ecpm},#{li.native_msg_show},#{li.native_msg_income},
            #{li.plaque_video_pv},#{li.plaque_video_arpu},#{li.addnum},#{li.avgnum},
            #{li.banner_click},#{li.plaque_click},#{li.splash_click},#{li.native_banner_click},#{li.native_plaque_click},
            #{li.native_splash_click},#{li.video_click},#{li.plaque_video_click},#{li.native_msg_click},
            #{li.system_splash_show},#{li.native_new_plaque_show},#{li.native_new_banner_show},#{li.suspend_icon_show},
            #{li.system_splash_pv},#{li.native_new_plaque_pv},#{li.native_new_banner_pv},#{li.suspend_icon_pv},
            #{li.system_splash_arpu},#{li.native_new_plaque_arpu},#{li.native_new_banner_arpu},#{li.suspend_icon_arpu},
            #{li.system_splash_ecpm},#{li.native_new_plaque_ecpm},#{li.native_new_banner_ecpm},#{li.suspend_icon_ecpm},
            #{li.system_splash_income},#{li.native_new_plaque_income},#{li.native_new_banner_income},#{li.suspend_icon_income},
            #{li.system_splash_click},#{li.native_new_plaque_click},#{li.native_new_banner_click},#{li.suspend_icon_click},
            #{li.native_plaque_pv},#{li.native_banner_pv},#{li.temp_id},#{li.temp_name}
            <if test="li.source != null and li.source != ''">
                ,#{li.source}
            </if>
            <if test="li.source == null or li.source == ''">
                ,1
            </if>
            ,#{li.active_temp_id},#{li.active_temp_name}
            ,#{li.banner_request},#{li.plaque_request},#{li.splash_request},#{li.video_request},#{li.native_banner_request}
            ,#{li.native_plaque_request},#{li.native_splash_request},#{li.plaque_video_request},#{li.native_msg_request}
            ,#{li.system_splash_request},#{li.native_new_plaque_request},#{li.native_new_banner_request},#{li.suspend_icon_request}
            ,#{li.banner_fill},#{li.plaque_fill},#{li.splash_fill},#{li.video_fill},#{li.native_banner_fill}
            ,#{li.native_plaque_fill},#{li.native_splash_fill},#{li.plaque_video_fill},#{li.native_msg_fill}
            ,#{li.system_splash_fill},#{li.native_new_plaque_fill},#{li.native_new_banner_fill},#{li.suspend_icon_fill}

            ,#{li.country}
            )
        </foreach>
    </insert>

</mapper>