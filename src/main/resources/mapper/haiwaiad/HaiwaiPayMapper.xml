<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.haiwaiad.HaiwaiPayMapper">

    <select id="selectExchangeCodes" resultType="com.wbgame.pojo.operate.ExchangeCodeVo">
        select code,status,type,value,num,create_owner,date_format(create_time, '%Y-%m-%d %H:%i:%S') create_time,
        date_format(update_time, '%Y-%m-%d %H:%i:%S') update_time,date_format(expire_time, '%Y-%m-%d %H:%i:%S') expire_time,ifnull(use_num,0) use_num
        from dnwx_pay.exchange_code_config a left join
        (select code code_log,count(1) use_num from dnwx_pay.exchange_code_log group by code) b on a.code = b.code_log
        where 1=1
        <if test="code != null and code != ''">
            and code = #{code}
        </if>
        <if test="code_value != null and code_value != ''">
            and value = #{code_value}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="create_owner != null and create_owner != ''">
            and create_owner = #{create_owner}
        </if>
        order by
        <choose>
            <when test="order != null and order != ''">
                ${order}
            </when>
            <otherwise>
                create_time desc
            </otherwise>
        </choose>
    </select>

    <insert id="addExchangeCodes">
         insert into dnwx_pay.exchange_code_config(code,type,value,num,status,create_owner,create_time,expire_time)
         values (#{code},#{type},#{value},#{num},1,#{create_owner},now(),#{expire_time})
    </insert>

    <insert id="addbatchExchangeCodes">
        insert into dnwx_pay.exchange_code_config(code,type,value,num,status,create_owner,create_time,expire_time)
        values
        <foreach collection="list" separator="," item="it">
            (#{it.code},#{it.type},#{it.value},#{it.num},1,#{it.create_owner},now(),#{it.expire_time})
        </foreach>
    </insert>

</mapper>