<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.haiwaiad.HwWbPayInfoMapper">

    <select id="selectNewPayInfo" resultType="java.util.Map">
        select truncate(sum(money)/100,2) as amount,
        count(distinct ifnull(imei,'')) as payNumber,count(*) as payCount,
        truncate(sum(money)/100/count(distinct ifnull(imei,'')),2) as arpu,
        date(createtime) creattime,tdate
        ,pid AS prjid,paytype AS payWay,appid,c.name appCategory,param2 mchid,zone_id
        from
        (select *,
        <choose>
            <when test="custom_date != null and custom_date != ''">
                concat(#{begin},'至',#{end}) as tdate
            </when>
            <when test="group != null and group.contains('tdate')">
                date(createtime) as tdate
            </when>
            <when test="group != null and group.contains('week')">
                DATE_FORMAT(createtime, '%x-%v') as tdate,
                DATE_FORMAT(createtime, '%x-%v') as week
            </when>
            <when test="group != null and group.contains('month')">
                DATE_FORMAT(createtime,'%Y-%m') as tdate,
                DATE_FORMAT(createtime,'%Y-%m') as `month`
            </when>
            <when test="group != null and group != '' and group.contains('beek')">
                CONCAT(DATE_FORMAT(createtime, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 1,CHAR),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 2,CHAR),2,"0"))  AS tdate,
                CONCAT(DATE_FORMAT(createtime, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 1,CHAR),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 2,CHAR),2,"0"))  AS beek
            </when>
            <otherwise>
                concat(#{begin},'至',#{end}) as tdate
            </otherwise>
        </choose>
        from dnwx_pay.wb_pay_info
        where createtime BETWEEN concat(#{begin},' 00:00:00') AND concat(#{end},' 23:59:59') and orderstatus = 'SUCCESS'
        <if test="app != null and app !=''">
            and appid in (${app})
        </if>
        <if test="prjId != null and prjId !=''">
            and pid = #{prjId}
        </if>
        <if test="payWays != null and payWays !=''">
            and paytype in (${payWays})
        </if>
        <choose>
            <when test="type.contains('test')">
                and oaid = 'test'
            </when>
            <otherwise>
                and (oaid != 'test' or oaid is null) and (param3 != 'test' or param3 is null)
            </otherwise>
        </choose>) a
        left join dnwx_cfg.app_info b on a.appid = b.id
        left join dnwx_cfg.app_category c on b.app_category = c.id
        <if test="appCategory != null and appCategory !=''">
            where app_category = #{appCategory}
        </if>
        group by ${group}
        order by ${order}
    </select>


    <select id="countNewPayInfo" resultType="java.util.Map">
        select
        truncate(sum(xx.amount),2) amount,
        sum(xx.payNumber) payNumber,
        sum(xx.payCount) payCount,
        truncate(sum(xx.amount)/sum(xx.payNumber), 2) arpu
        from
        (select truncate(sum(money)/100,2) as amount,
        count(distinct ifnull(imei,'')) as payNumber,count(*) as payCount,
        truncate(sum(money)/100/count(distinct ifnull(imei,'')),2) as arpu,
        date(createtime) creattime
        ,pid AS prjid,paytype AS payWay,appid,c.name appCategory
        from
        (select *,
        <choose>
            <when test="custom_date != null and custom_date != ''">
                concat(#{begin},'至',#{end}) as tdate
            </when>
            <when test="group != null and group.contains('tdate')">
                date(createtime) as tdate
            </when>
            <when test="group != null and group.contains('week')">
                DATE_FORMAT(createtime, '%x-%v') as tdate,
                DATE_FORMAT(createtime, '%x-%v') as week
            </when>
            <when test="group != null and group.contains('month')">
                DATE_FORMAT(createtime,'%Y-%m') as tdate,
                DATE_FORMAT(createtime,'%Y-%m') as `month`
            </when>
            <when test="group != null and group != '' and group.contains('beek')">
                CONCAT(DATE_FORMAT(createtime, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 1,CHAR),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 2,CHAR),2,"0"))  AS tdate,
                CONCAT(DATE_FORMAT(createtime, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 1,CHAR),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 2,CHAR),2,"0"))  AS beek
            </when>
            <otherwise>
                concat(#{begin},'至',#{end}) as tdate
            </otherwise>
        </choose>
        from dnwx_pay.wb_pay_info
        where createtime BETWEEN concat(#{begin},' 00:00:00') AND concat(#{end},' 23:59:59') and orderstatus = 'SUCCESS'
        <if test="app != null and app !=''">
            and appid in (${app})
        </if>
        <if test="prjId != null and prjId !=''">
            and pid = #{prjId}
        </if>
        <if test="payWays != null and payWays !=''">
            and paytype in (${payWays})
        </if>
        <choose>
            <when test="type.contains('test')">
                and oaid = 'test'
            </when>
            <otherwise>
                and (oaid != 'test' or oaid is null) and (param3 != 'test' or param3 is null)
            </otherwise>
        </choose>) a
        left join dnwx_cfg.app_info b on a.appid = b.id
        left join dnwx_cfg.app_category c on b.app_category = c.id
        <if test="appCategory != null and appCategory !=''">
            where app_category = #{appCategory}
        </if>
        group by ${group}
        order by ${order}) xx
    </select>

    <select id="selectPayInfoDetailOutside" resultType="com.wbgame.pojo.view.PayInfoDetailVo">
        select date(createtime) tdate,zone_id,pid,paytype,c.name app_category,app_name appid,if(is_new=1,'新用戶',if(is_new=0,'老用戶','未知')) is_new
        ,truncate(sum(money)/100,2) as amount
        ,count(distinct ifnull(imei,'')) as payNumber
        ,count(*) as payCount
        ,truncate(sum(money)/100/count(distinct ifnull(imei,'')),2) as arpu
        from dnwx_pay.wb_pay_info a
        left join dnwx_cfg.app_info b on a.appid = b.id
        left join dnwx_cfg.app_category c on b.app_category = c.id
        where createtime BETWEEN concat(#{start_date},' 00:00:00') AND concat(#{end_date},' 23:59:59')
        and orderstatus = 'SUCCESS'
        <if test="appid != null and appid !=''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid !=''">
            and pid = #{pid}
        </if>
        <if test="zone_id != null and zone_id !=''">
            and zone_id = #{zone_id}
        </if>
        <if test="paytype != null and paytype !=''">
            and paytype = #{paytype}
        </if>
        <if test="app_category != null and app_category !=''">
            and app_category in (${app_category})
        </if>
        <if test="is_new != null and is_new != ''">
            and is_new = #{is_new}
        </if>
        <choose>
            <when test="type.contains('test')">
                and oaid = 'test'
            </when>
            <otherwise>
                and (oaid != 'test' or oaid is null)
            </otherwise>
        </choose>
        group by ${group}
        order by ${order_str}
    </select>

    <select id="countPayInfoDetailOutside" resultType="com.wbgame.pojo.view.PayInfoDetailVo">
        select truncate(sum(xx.amount),2) as amount,
        sum(xx.payNumber) as payNumber,
        sum(xx.payCount) as payCount,
        truncate(sum(xx.amount)/sum(xx.payNumber),2) as arpu
        from
        (select date(createtime) tdate,zone_id,pid,paytype,c.name app_category,app_name appid,if(is_new=1,'新用戶',if(is_new=0,'老用戶','未知')) is_new
        ,truncate(sum(money)/100,2) as amount
        ,count(distinct ifnull(imei,'')) as payNumber
        ,count(*) as payCount
        ,truncate(sum(money)/100/count(distinct ifnull(imei,'')),2) as arpu
        from dnwx_pay.wb_pay_info a
        left join dnwx_cfg.app_info b on a.appid = b.id
        left join dnwx_cfg.app_category c on b.app_category = c.id
        where createtime BETWEEN concat(#{start_date},' 00:00:00') AND concat(#{end_date},' 23:59:59')
        and orderstatus = 'SUCCESS'
        <if test="appid != null and appid !=''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid !=''">
            and pid = #{pid}
        </if>
        <if test="zone_id != null and zone_id !=''">
            and zone_id = #{zone_id}
        </if>
        <if test="paytype != null and paytype !=''">
            and paytype = #{paytype}
        </if>
        <if test="app_category != null and app_category !=''">
            and app_category in (${app_category})
        </if>
        <if test="is_new != null and is_new != ''">
            and is_new = #{is_new}
        </if>
        <choose>
            <when test="type.contains('test')">
                and oaid = 'test'
            </when>
            <otherwise>
                and (oaid != 'test' or oaid is null)
            </otherwise>
        </choose>
        group by ${group}) xx
    </select>
</mapper>