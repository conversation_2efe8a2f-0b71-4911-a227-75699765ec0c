<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.haiwaiad.GoogleKeyConfigMapper">

    <sql id="Base_Column_List">
        appid,
        package as googlePackage,
        key_json as keyJson,
        create_time as createTime,
        create_owner as createOwner,
        update_time as updateTime,
        update_owner as updateOwner
    </sql>

    <insert id="insertGoogleKey">
        insert into dnwx_pay.google_key_config(appid,package,key_json,create_time,create_owner)
        values (#{appid},#{googlePackage},#{keyJson},now(),#{createOwner})
    </insert>

    <update id="updateGoogleKey">
        update dnwx_pay.google_key_config
        <set>
            <if test="googlePackage != null">
                package = #{googlePackage,jdbcType=VARCHAR},
            </if>
            <if test="keyJson != null">
                key_json = #{keyJson,jdbcType=VARCHAR},
            </if>
            <if test="updateOwner != null">
                update_owner = #{updateOwner,jdbcType=VARCHAR},
            </if>
            update_time = now()
        </set>
        where appid = #{appid,jdbcType=INTEGER}
    </update>

    <delete id="deleteGoogleKey">
        DELETE FROM dnwx_pay.google_key_config WHERE appid IN
        <foreach collection="appidList" open="(" close=")" separator="," item="appid">
            #{appid}
        </foreach>
    </delete>
    <select id="existGoogleKey" resultType="java.lang.Integer">
        SELECT count(1) FROM dnwx_pay.google_key_config WHERE appid = #{appid} limit 1
    </select>
    <select id="selectByCondition" resultType="com.wbgame.pojo.operate.GoogleKeyConfig">
        select
        <include refid="Base_Column_List"/>
        from dnwx_pay.google_key_config
        <where>
            <if test="appid != null and appid != ''">
                appid IN (${appid})
            </if>
        </where>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
        <if test="order_str == null or order_str == ''">
            order by create_time desc
        </if>
    </select>

</mapper>