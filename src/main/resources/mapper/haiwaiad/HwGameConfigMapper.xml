<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.haiwaiad.HwGameConfigMapper">

    <select id="selectHwQATypeList" resultType="com.wbgame.pojo.game.config.QATypeConfigVo">
        select * from game_feedback_type_config
        <where>
            <if test="pqaName != null and pqaName != ''">
                and pqaName like concat('%',#{pqaName},'%')
            </if>
            <if test="state != null and state != ''">
                and state = #{state}
            </if>
        </where>
        order by create_time desc
    </select>


    <insert id="saveQATypeConfig" parameterType="com.wbgame.pojo.game.config.QATypeConfigVo"
            useGeneratedKeys="true" keyProperty="pqaId" keyColumn="pqaId">
        insert into game_feedback_type_config (pqaName,pqaIcon,`index`,state,create_user,create_time)
        values (#{pqaName},#{pqaIcon},#{index},#{state},#{create_user},now())
    </insert>


    <update id="updateQATypeConfig" parameterType="com.wbgame.pojo.game.config.QATypeConfigVo">
        update game_feedback_type_config set pqaName = #{pqaName},pqaIcon = #{pqaIcon},`index` = #{index},state = #{state},
        modify_user = #{modify_user},modify_time = now() where pqaId = #{pqaId}
    </update>


    <delete id="delQATypeConfig" parameterType="java.lang.String">
        delete from game_feedback_type_config where pqaId in (${pqaId});
    </delete>


    <select id="selectQAConfigList" resultType="com.wbgame.pojo.game.config.QAConfigVo">
        select * from  game_feedback_detail_config
        <where>
            <if test="pqaId != null and pqaId != ''">
                pqaId in (${pqaId})
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="qaName != null and qaName != ''">
                and qaName like concat('%',#{qaName},'%')
            </if>
            <if test="qaInfo != null and qaInfo != ''">
                and qaInfo like concat('%',#{qaInfo},'%')
            </if>
            <if test="state != null and state != ''">
                and state = #{state}
            </if>
        </where>
        order by create_time desc
    </select>



    <insert id="saveQAConfig" parameterType="com.wbgame.pojo.game.config.QAConfigVo"
            useGeneratedKeys="true" keyProperty="qaId" keyColumn="qaId">
        insert into game_feedback_detail_config (appid,pqaId,qaName,qaUrl,qaInfo,relationIds,`index`,state,create_user,create_time)
        values (#{appid},#{pqaId},#{qaName},#{qaUrl},#{qaInfo},#{relationIds},#{index},#{state},#{create_user},now())
    </insert>


    <update id="updateQAConfig" parameterType="com.wbgame.pojo.game.config.QAConfigVo">
        update game_feedback_detail_config set appid = #{appid},pqaId = #{pqaId},state = #{state},
        qaName = #{qaName},qaUrl = #{qaUrl},qaInfo = #{qaInfo},relationIds = #{relationIds},
        `index` = #{index},modify_user = #{modify_user},modify_time = now() where qaId = #{qaId}
    </update>


    <delete id="deleteQAConfig" parameterType="java.lang.String">
        delete from game_feedback_detail_config where qaId in (${qaId});
    </delete>



</mapper>