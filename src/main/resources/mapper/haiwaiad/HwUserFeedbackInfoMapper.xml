<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.haiwaiad.HwUserFeedbackInfoMapper">

    <select id="selectFeedbackList" resultType="com.wbgame.pojo.game.userinfo.FeedbackVo">
        select ufi.*,ai.app_name from user_feedback_info ufi left join app_info ai on ufi.appid = ai.id
        <where>
            DATE(ufi.create_time) <![CDATA[ >= ]]> #{start_date} and DATE(ufi.create_time) <![CDATA[ <= ]]> #{end_date}
            <if test="appid != null and appid != ''">
                and ufi.appid in (${appid})
            </if>
            <if test="pid != null and pid != ''">
                and ufi.pid = #{pid}
            </if>
            <if test="loginid != null and loginid != ''">
                and ufi.loginid = #{loginid}
            </if>
            <if test="msgtype != null and msgtype != ''">
                and ufi.msgtype = #{msgtype}
            </if>
            <if test="state != null and state != ''">
                and ufi.state = #{state}
            </if>
            <if test="note != null and note != ''">
                and ufi.note like concat('%',#{note},'%')
            </if>
            <if test="device_id != null and device_id != ''">
                and (ufi.androidid = #{device_id} or ufi.lsn = #{device_id} or ufi.idfa = #{device_id})
            </if>
        </where>
        order by ufi.create_time desc
    </select>



    <update id="updateFeedback" parameterType="com.wbgame.pojo.CustomerServiceDTO">
        update user_feedback_info set state=#{state},
            <if test="note != null">
                note=#{note},
            </if>
            modify_user=#{modify_user},modify_time=now() where id in(${id})
    </update>


</mapper>