<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.haiwaiad.HaiwaiAdNoticeMapper" >

	<select id="getNoticeConfigList" parameterType="com.wbgame.pojo.game.config.NoticeVo" resultType="com.wbgame.pojo.game.config.NoticeVo">
		select * from game_notice_config where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel})
		</if>
		<if test="noticeType != null and noticeType != ''">
			and noticeType = #{noticeType}
		</if>
		<if test="actionType != null and actionType != ''">
			and actionType = #{actionType}
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="ver != null and ver != ''">
			and ver = #{ver}
		</if>
		<if test="title != null and title != ''">
			and title like '%${title}%'
		</if>
		<if test="content != null and content != ''">
			and content like '%${content}%'
		</if>
		<if test="state != null and state != ''">
			and state = #{state}
		</if>
		<if test="start_date != null and start_date != ''">
			and DATE_FORMAT(create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{start_date}
		</if>
		<if test="end_date != null and end_date != ''">
			and DATE_FORMAT(create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{end_date}
		</if>
	</select>

	<insert id="insertNoticeConfig" parameterType="com.wbgame.pojo.game.config.NoticeVo">
		insert into game_notice_config (appid,channel,pid,ver,noticeType,noticeTypeName,
		title,content,popType,popTimes,actionType,imgs,start_date,end_date,state,`index`,create_user,create_time)
		values (#{appid},#{channel},#{pid},#{ver},#{noticeType},#{noticeTypeName},#{title},#{content},
		#{popType},#{popTimes},#{actionType},#{imgs},#{start_date},#{end_date},#{state},#{index},#{create_user},now())
	</insert>

	<update id="updateNoticeConfig" parameterType="com.wbgame.pojo.game.config.NoticeVo">
		update game_notice_config set appid = #{appid},channel = #{channel},
		pid = #{pid},ver = #{ver},noticeType = #{noticeType},noticeTypeName = #{noticeTypeName},
		title = #{title},content = #{content},popType = #{popType},popTimes = #{popTimes},
		actionType = #{actionType},imgs = #{imgs},start_date = #{start_date},end_date = #{end_date},
		state = #{state},`index` = #{index},modify_user = #{modify_user},modify_time = now()
		where noticeId = #{noticeId}
	</update>

	<delete id="deleteNoticeConfig" parameterType="com.wbgame.pojo.game.config.NoticeVo">
		delete from game_notice_config where noticeId in (${noticeId})
	</delete>
</mapper>