<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.haiwaiad.HaiwaiCfgMapper">

	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>

	<update id="updateAppInfo" parameterType="com.wbgame.pojo.AppInfoVo">
		update app_info
		<set>
			umeng_key = #{umeng_key},sync_umeng = #{sync_umeng},
			<if test="app_name != null and app_name != ''">
				app_name = #{app_name},
			</if>
			<if test="os_type != null and os_type != ''">
				os_type = #{os_type},
			</if>
			<if test="app_category != null and app_category != ''">
				app_category = #{app_category},
			</if>
			<if test="xyx_id != null and xyx_id != ''">
				xyx_id = #{xyx_id},
			</if>
			<if test="reyun_key != null and reyun_key != ''">
				reyun_key = #{reyun_key},
			</if>
			<if test="bus_category != null and bus_category != ''">
				bus_category = #{bus_category},
			</if>
			<if test="two_app_category != null and two_app_category != ''">
				two_app_category = #{two_app_category},
			</if>
			<if test="cp != null and cp != ''">
				cp = #{cp},
			</if>
		</set>

		where id = #{id}
	</update>


	<select id="selectPartnerAppRevenueNewBilling" parameterType="java.lang.String" resultType="java.util.Map">

		SELECT xx.*,IFNULL(yy.pay_revenue,0) pay_revenue,IFNULL(yy.refund_revenue,0) refund_revenue,IFNULL(yy.paynum,0) paynum,'0' ischeck
		FROM
			(
				select tdate,appid,
				       SUM(act_users) actnum,SUM(new_users) addnum
				from dnwx_bi.dn_active_add_summary where tdate='${tdate}'
					and appid in (select id from dnwx_cfg.app_info where app_category in (15,16))
				group by tdate,appid
			) xx
				LEFT JOIN
			(
				select tdate,appid,
					sum(paynum) paynum,
					IFNULL(TRUNCATE(SUM(pay_revenue) /100, 2), 0) pay_revenue,
					IFNULL(TRUNCATE(SUM(refund_revenue) /100, 2), 0) refund_revenue
			 	from (
					  select DATE_FORMAT(createtime,'%Y-%m-%d') as tdate,appid,'US' country_code,IFNULL(chaid,'') chaid,IFNULL(paytype,'') paytype,
							 IFNULL(SUM(money), 0) pay_revenue,'0' refund_revenue,COUNT(DISTINCT lsn) paynum
					  from dnwx_pay.wb_pay_info
					  where createtime BETWEEN '${tdate} 00:00:00' AND '${tdate} 23:59:59'
						and orderstatus='SUCCESS'
						and (oaid != 'test' or oaid is null) and (param3 != 'test' or param3 is null)
						and appid is not null and paytype is not null
					  group by DATE_FORMAT(createtime,'%Y-%m-%d'),appid,chaid,paytype
				  ) x
			 	group by tdate,appid
			) yy

			ON xx.tdate=yy.tdate and xx.appid=yy.appid
			  <!-- and xx.country_code=yy.country_code -->
	</select>

</mapper>