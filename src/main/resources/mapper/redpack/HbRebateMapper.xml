<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.HbRebateMapper">

    <select id="selectByAll"  resultType="java.util.Map">
        SELECT pid,amount,num,redpack,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') update_time,create_owner,update_owner FROM hb_withdraw_rebate WHERE 1=1
        <if test="pid !=null and pid !=''">
            AND pid = #{pid}
        </if>
        <if test="amount !=null and amount !=''">
            AND amount = #{amount}
        </if>
        <if test="num !=null and num !=''">
            AND num = #{num}
        </if>
        AND pid != '0'
    </select>

    <insert id="insert" parameterType="com.wbgame.pojo.redpack.HbRebate">
        INSERT INTO hb_withdraw_rebate (pid,amount,num,redpack,create_time,update_time,create_owner,update_owner)
        VALUES (#{pid},#{amount},#{num},#{redpack},NOW(),NOW(),#{create_owner},#{update_owner})
    </insert>

    <update id="update" parameterType="com.wbgame.pojo.redpack.HbRebate">
        UPDATE hb_withdraw_rebate SET
        redpack = #{redpack},
        update_owner = #{update_owner},
        update_time = NOW()
        WHERE pid = #{pid} and amount = #{amount} and num = #{num}
    </update>

    <delete id="delete" parameterType="com.wbgame.pojo.redpack.HbRebate">
        DELETE FROM hb_withdraw_rebate
        WHERE pid = #{pid} and amount = #{amount} and num = #{num}
    </delete>

</mapper>