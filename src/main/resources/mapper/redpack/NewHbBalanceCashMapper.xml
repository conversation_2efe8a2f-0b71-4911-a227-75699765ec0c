<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.NewHbBalanceCashMapper">

    <select id="selectHbWithDrawConfig" resultType="com.wbgame.pojo.RusConfigInfo" parameterType="com.wbgame.pojo.RusConfigInfo" >
        select
        pid,type,amount,`desc`,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createStr,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') updateStr
        ,create_owner,update_owner,is_examine,day_num,total_num from hb_withdraw_config
        where 1=1
        <if test="pid != null and pid != ''" >
            and pid = #{pid,jdbcType=VARCHAR}
        </if>
        <if test="type != null and type != ''" >
            and type = #{type,jdbcType=INTEGER}
        </if>
    </select>
    <delete id="deleteHbWithDrawConfig" parameterType="com.wbgame.pojo.RusConfigInfo" >
        delete from hb_withdraw_config
        where pid = #{pid,jdbcType=VARCHAR}
        and type = #{type,jdbcType=INTEGER}
    </delete>
    <insert id="insertHbWithDrawConfig" parameterType="com.wbgame.pojo.RusConfigInfo" >
        insert into hb_withdraw_config (pid, type, amount,
        `desc`,create_owner,update_owner,create_time,update_time,is_examine,day_num,total_num)
        values (#{pid,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL},
        #{desc,jdbcType=VARCHAR},#{create_owner},#{update_owner},now(),now(),#{is_examine},#{day_num},#{total_num})
    </insert>

    <update id="updateHbWithDrawConfig" parameterType="com.wbgame.pojo.RusConfigInfo" >
        update hb_withdraw_config
        set
        amount = #{amount},
        day_num = #{day_num},
        total_num = #{total_num},
        <if test="update_owner != null and update_owner != ''">
            update_owner = #{update_owner},
        </if>
        <if test="desc != null">
            `desc` = #{desc,jdbcType=VARCHAR},
        </if>
        is_examine = #{is_examine},
        update_time = now()
        where pid = #{pid,jdbcType=VARCHAR}
        and type = #{type,jdbcType=INTEGER}
    </update>

    <insert id="addHbWithDrawConfigList" parameterType="java.util.List">
        insert into hb_withdraw_config (pid, type, amount, `desc`,create_owner,update_owner,is_examine,day_num,total_num,create_time,update_time)
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.pid},
            #{lpc.type},
            #{lpc.amount},
            #{lpc.desc},
            #{lpc.create_owner,jdbcType=VARCHAR},
            #{lpc.update_owner,jdbcType=VARCHAR},
            #{lpc.is_examine},
            #{lpc.day_num},
            #{lpc.total_num},
            now(),
            now())
        </foreach>

    </insert>

</mapper>