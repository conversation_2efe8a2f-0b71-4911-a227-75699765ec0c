<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.redpack.NewHbRequireWithdrawConfigMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.redpack.HbRequireWithdrawConfig">
    <!--@mbg.generated-->
    <!--@Table hb_require_withdraw_config-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <id column="pid" jdbcType="VARCHAR" property="pid" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="is_examine" jdbcType="INTEGER" property="isExamine" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="day_num" jdbcType="INTEGER" property="dayNum" />
    <result column="total_num" jdbcType="INTEGER" property="totalNum" />
    <result column="grade" jdbcType="INTEGER" property="grade" />
    <result column="require" jdbcType="LONGVARCHAR" property="require" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_owner" jdbcType="VARCHAR" property="createOwner" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_owner" jdbcType="VARCHAR" property="updateOwner" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="mark" jdbcType="VARCHAR" property="mark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, pid, `type`, amount, is_examine, `status`, day_num, total_num, grade, `require`, 
    create_time, create_owner, update_time, update_owner, `desc`, mark
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hb_require_withdraw_config
    where id = #{id,jdbcType=INTEGER}
      and pid = #{pid,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--@mbg.generated-->
    delete from hb_require_withdraw_config
    where id = #{id,jdbcType=INTEGER}
      and pid = #{pid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.wbgame.pojo.redpack.HbRequireWithdrawConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hb_require_withdraw_config (pid, `type`, amount, 
      is_examine, `status`, day_num, 
      total_num, grade, `require`, 
      create_time, create_owner, update_time, 
      update_owner, `desc`, mark
      )
    values (#{pid,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, 
      #{isExamine,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{dayNum,jdbcType=INTEGER}, 
      #{totalNum,jdbcType=INTEGER}, #{grade,jdbcType=INTEGER}, #{require,jdbcType=LONGVARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOwner,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOwner,jdbcType=VARCHAR}, #{desc,jdbcType=VARCHAR}, #{mark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective"  parameterType="com.wbgame.pojo.redpack.HbRequireWithdrawConfig">
    <!--@mbg.generated-->
    insert into hb_require_withdraw_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="isExamine != null">
        is_examine,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="dayNum != null">
        day_num,
      </if>
      <if test="totalNum != null">
        total_num,
      </if>
      <if test="grade != null">
        grade,
      </if>
      <if test="require != null">
        `require`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOwner != null">
        create_owner,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOwner != null">
        update_owner,
      </if>
      <if test="desc != null">
        `desc`,
      </if>
      <if test="mark != null">
        mark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="isExamine != null">
        #{isExamine,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="dayNum != null">
        #{dayNum,jdbcType=INTEGER},
      </if>
      <if test="totalNum != null">
        #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="grade != null">
        #{grade,jdbcType=INTEGER},
      </if>
      <if test="require != null">
        #{require,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwner != null">
        #{createOwner,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOwner != null">
        #{updateOwner,jdbcType=VARCHAR},
      </if>
      <if test="desc != null">
        #{desc,jdbcType=VARCHAR},
      </if>
      <if test="mark != null">
        #{mark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.redpack.HbRequireWithdrawConfig">
    <!--@mbg.generated-->
    update hb_require_withdraw_config
    <set>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="isExamine != null">
        is_examine = #{isExamine,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="dayNum != null">
        day_num = #{dayNum,jdbcType=INTEGER},
      </if>
      <if test="totalNum != null">
        total_num = #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="require != null">
        `require` = #{require,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwner != null">
        create_owner = #{createOwner,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOwner != null">
        update_owner = #{updateOwner,jdbcType=VARCHAR},
      </if>
      <if test="desc != null">
        `desc` = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="mark != null">
        mark = #{mark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
      and pid = #{pid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.redpack.HbRequireWithdrawConfig">
    <!--@mbg.generated-->
    update hb_require_withdraw_config
    set `type` = #{type,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DECIMAL},
      is_examine = #{isExamine,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      day_num = #{dayNum,jdbcType=INTEGER},
      total_num = #{totalNum,jdbcType=INTEGER},
      grade = #{grade,jdbcType=INTEGER},
      `require` = #{require,jdbcType=LONGVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_owner = #{createOwner,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_owner = #{updateOwner,jdbcType=VARCHAR},
      `desc` = #{desc,jdbcType=VARCHAR},
      mark = #{mark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
      and pid = #{pid,jdbcType=VARCHAR}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-07-07-->
  <select id="selectByAll" resultMap="BaseResultMap">
        select
    id, pid, `type`, amount, is_examine, `status`, day_num, total_num, grade,`require`,
    DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createStr, create_owner,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') updateStr, update_owner, `desc`, mark
        from hb_require_withdraw_config
        <where>
            <if test="pid != null and pid != ''">
                and pid=#{pid,jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != ''">
                and `type`=#{type,jdbcType=VARCHAR}
            </if>
          <if test="grade != null">
            and grade=#{grade}
          </if>
        </where>
    </select>
  <select id="selectRequires" resultType="java.util.Map">
      select id,name from hb_require_type
  </select>
</mapper>