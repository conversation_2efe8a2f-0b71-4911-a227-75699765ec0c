<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.redpack.NewHbAdvertRateConfigMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.redpack.HbAdvertRateConfig">
    <!--@mbg.generated-->
    <!--@Table hb_advert_rate_config-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="appid" jdbcType="INTEGER" property="appid" />
    <result column="pid" jdbcType="VARCHAR" property="pid" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_owner" jdbcType="VARCHAR" property="createOwner" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_owner" jdbcType="VARCHAR" property="updateOwner" />
    <result column="video" jdbcType="INTEGER" property="video" />
    <result column="plaque" jdbcType="INTEGER" property="plaque" />
    <result column="splash" jdbcType="INTEGER" property="splash" />
    <result column="msg" jdbcType="INTEGER" property="msg" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, appid, pid, channel, `type`, create_time, create_owner, update_time, update_owner, 
    video, plaque, splash, msg, `status`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hb_advert_rate_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from hb_advert_rate_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.wbgame.pojo.redpack.HbAdvertRateConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hb_advert_rate_config (appid, pid, channel, 
      `type`, create_time, create_owner, 
      update_time, update_owner, video, 
      plaque, splash, msg, 
      `status`)
    values (#{appid,jdbcType=INTEGER}, #{pid,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOwner,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOwner,jdbcType=VARCHAR}, #{video,jdbcType=INTEGER}, 
      #{plaque,jdbcType=INTEGER}, #{splash,jdbcType=INTEGER}, #{msg,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.wbgame.pojo.redpack.HbAdvertRateConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hb_advert_rate_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appid != null">
        appid,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOwner != null">
        create_owner,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOwner != null">
        update_owner,
      </if>
      <if test="video != null">
        video,
      </if>
      <if test="plaque != null">
        plaque,
      </if>
      <if test="splash != null">
        splash,
      </if>
      <if test="msg != null">
        msg,
      </if>
      <if test="status != null">
        `status`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appid != null">
        #{appid,jdbcType=INTEGER},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwner != null">
        #{createOwner,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOwner != null">
        #{updateOwner,jdbcType=VARCHAR},
      </if>
      <if test="video != null">
        #{video,jdbcType=INTEGER},
      </if>
      <if test="plaque != null">
        #{plaque,jdbcType=INTEGER},
      </if>
      <if test="splash != null">
        #{splash,jdbcType=INTEGER},
      </if>
      <if test="msg != null">
        #{msg,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.redpack.HbAdvertRateConfig">
    <!--@mbg.generated-->
    update hb_advert_rate_config
    <set>
      <if test="appid != null">
        appid = #{appid,jdbcType=INTEGER},
      </if>
      <if test="pid != null">
        pid = #{pid,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwner != null">
        create_owner = #{createOwner,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOwner != null">
        update_owner = #{updateOwner,jdbcType=VARCHAR},
      </if>
      <if test="video != null">
        video = #{video,jdbcType=INTEGER},
      </if>
      <if test="plaque != null">
        plaque = #{plaque,jdbcType=INTEGER},
      </if>
      <if test="splash != null">
        splash = #{splash,jdbcType=INTEGER},
      </if>
      <if test="msg != null">
        msg = #{msg,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.redpack.HbAdvertRateConfig">
    <!--@mbg.generated-->
    update hb_advert_rate_config
    set appid = #{appid,jdbcType=INTEGER},
      pid = #{pid,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_owner = #{createOwner,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_owner = #{updateOwner,jdbcType=VARCHAR},
      video = #{video,jdbcType=INTEGER},
      plaque = #{plaque,jdbcType=INTEGER},
      splash = #{splash,jdbcType=INTEGER},
      msg = #{msg,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-07-07-->
  <select id="selectByAll" resultMap="BaseResultMap">
        select
    id, appid, pid, channel, `type`,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createStr, create_owner,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') updateStr, update_owner,
    video, plaque, splash, msg, `status`
        from hb_advert_rate_config
        <where>
            <if test="appid != null">
                and appid=#{appid,jdbcType=INTEGER}
            </if>
            <if test="pid != null and pid != ''">
                and pid=#{pid,jdbcType=VARCHAR}
            </if>
            <if test="channel != null and channel != ''">
                and channel=#{channel,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and `type`=#{type,jdbcType=INTEGER}
            </if>
        </where>
    </select>
</mapper>