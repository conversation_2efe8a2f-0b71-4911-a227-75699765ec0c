<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.AliOnePayConfigMapper">

    <resultMap id="BaseResultMap" type="com.wbgame.pojo.operate.AliOnePayConfig">
        <id column="appid" property="appid" jdbcType="VARCHAR"/>
        <id column="sub_code" property="sub_code" jdbcType="VARCHAR"/>
        <result column="appname" property="appname" jdbcType="VARCHAR"/>
        <result column="alipay_param1" property="alipay_param1" jdbcType="VARCHAR"/>
        <result column="alipay_param4" property="alipay_param4" jdbcType="VARCHAR"/>
        <result column="alipay_param2" property="alipay_param2" jdbcType="LONGVARCHAR"/>
        <result column="alipay_param3" property="alipay_param3" jdbcType="LONGVARCHAR"/>
        <result column="create_user" property="create_user" jdbcType="VARCHAR"/>
        <result column="update_user" property="update_user" jdbcType="VARCHAR"/>
        <result column="create_time" property="create_time" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="update_time" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        appid,sub_code, appname, alipay_param1,alipay_param4,alipay_param2, alipay_param3,create_user,update_user,create_time,update_time, status
    </sql>

    <delete id="deleteAliOnePayConfig">
        <foreach collection="keyList" item="data" separator=";">
            DELETE FROM ali_one_pay_config WHERE appid = #{data.appid,jdbcType=VARCHAR}
            AND sub_code = #{data.sub_code,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="selectAliOnePayConfig" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.operate.AliOnePayConfigDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ali_one_pay_config
        <where>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="appidList != null and appidList.size() > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
            <if test="alipay_param4 != null and alipay_param4 != ''">
                AND alipay_param4 = #{alipay_param4}
            </if>
        </where>
        <if test="order_str != null and order_str != ''">
            ORDER BY ${order_str}
        </if>
        <if test="order_str == null or order_str == ''">
            ORDER BY create_time DESC
        </if>
    </select>


    <insert id="insertAliOnePayConfig" parameterType="com.wbgame.pojo.operate.AliOnePayConfig">
        insert into ali_one_pay_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appid != null">
                appid,
            </if>
            <if test="sub_code != null">
                sub_code,
            </if>
            <if test="appname != null">
                appname,
            </if>
            <if test="alipay_param1 != null">
                alipay_param1,
            </if>
            <if test="alipay_param2 != null">
                alipay_param2,
            </if>
            <if test="alipay_param3 != null">
                alipay_param3,
            </if>
            <if test="alipay_param4 != null">
                alipay_param4,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="create_user != null">
                create_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appid != null">
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="sub_code != null">
                #{sub_code,jdbcType=VARCHAR},
            </if>
            <if test="appname != null">
                #{appname,jdbcType=VARCHAR},
            </if>
            <if test="alipay_param1 != null">
                #{alipay_param1,jdbcType=VARCHAR},
            </if>
            <if test="alipay_param2 != null">
                #{alipay_param2,jdbcType=LONGVARCHAR},
            </if>
            <if test="alipay_param3 != null">
                #{alipay_param3,jdbcType=LONGVARCHAR},
            </if>
            <if test="alipay_param4 != null">
                #{alipay_param4,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="create_user != null">
                #{create_user},
            </if>
        </trim>
    </insert>

    <update id="updateAliOnePayConfig" parameterType="com.wbgame.pojo.operate.AliOnePayConfig">
        update ali_one_pay_config
        <set>
            <if test="appname != null">
                appname = #{appname,jdbcType=VARCHAR},
            </if>
            <if test="alipay_param1 != null">
                alipay_param1 = #{alipay_param1,jdbcType=VARCHAR},
            </if>
            <if test="alipay_param2 != null">
                alipay_param2 = #{alipay_param2,jdbcType=LONGVARCHAR},
            </if>
            <if test="alipay_param3 != null">
                alipay_param3 = #{alipay_param3,jdbcType=LONGVARCHAR},
            </if>
            <if test="alipay_param4 != null">
                alipay_param4 = #{alipay_param4,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="update_user != null">
                update_user = #{update_user},
            </if>
            update_time = now()
        </set>
        where appid = #{appid,jdbcType=VARCHAR}
        AND sub_code = #{sub_code,jdbcType=VARCHAR}
    </update>

    <insert id="batchInsert">
        <if test="dtoList != null and dtoList.size() > 0">
            insert into ali_one_pay_config (appid,sub_code,appname,alipay_param1,alipay_param2,alipay_param3,alipay_param4,status,create_user)
            values
            <foreach collection="dtoList" separator="," item="dto">
                (#{dto.appid,jdbcType=VARCHAR},#{dto.sub_code,jdbcType=VARCHAR},#{dto.appname,jdbcType=VARCHAR},#{dto.alipay_param1,jdbcType=VARCHAR},#{dto.alipay_param2,jdbcType=LONGVARCHAR},
                #{dto.alipay_param3,jdbcType=LONGVARCHAR},#{dto.alipay_param4,jdbcType=VARCHAR},#{dto.status},#{dto.create_user})
            </foreach>
        </if>
    </insert>



</mapper>