<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.WbPayConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.operate.WbPayConfigVO">
        <id column="appid" property="appid" jdbcType="VARCHAR"/>
        <id column="pid" property="pid" jdbcType="VARCHAR"/>
        <id column="cha" property="cha" jdbcType="VARCHAR"/>
        <result column="pkg" property="pkg" jdbcType="VARCHAR"/>
        <result column="appname" property="appname" jdbcType="VARCHAR"/>
        <result column="vivo_param1" property="vivoParam1" jdbcType="VARCHAR"/>
        <result column="vivo_param2" property="vivoParam2" jdbcType="VARCHAR"/>
        <result column="vivo_param3" property="vivoParam3" jdbcType="VARCHAR"/>
        <result column="alipay_param1" property="alipayParam1" jdbcType="VARCHAR"/>
        <result column="alipay_param4" property="alipayParam4" jdbcType="VARCHAR"/>
        <result column="alipay_param2" property="alipayParam2" jdbcType="LONGVARCHAR"/>
        <result column="alipay_param3" property="alipayParam3" jdbcType="LONGVARCHAR"/>

        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        appid
        , pid, cha, pkg, appname, vivo_param1, vivo_param2, vivo_param3, alipay_param1,
        alipay_param4,alipay_param2, alipay_param3,

        create_user,
        update_user,
        create_time,
        update_time,
        status
    </sql>

    <select id="selectWbPayConfig" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.operate.WbPayConfig">
        select

        <include refid="Base_Column_List"/>
        from wb_pay_config

        <where>

            <if test="status != null">
                and status = #{status}
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

            <if test="alipayParam4 != null and alipayParam4 != ''">
                and alipay_param4 = #{alipayParam4}
            </if>

        </where>
        order by create_time desc
    </select>

    <delete id="deleteWbPayConfig" parameterType="com.wbgame.pojo.operate.WbPayConfig">

        <foreach collection="list" item="data" separator=";">

            delete
            from wb_pay_config
            where appid = #{data.appid,jdbcType=VARCHAR}
            and pid = #{data.pid,jdbcType=VARCHAR}
            and cha = #{data.cha,jdbcType=VARCHAR}
        </foreach>

    </delete>

    <insert id="insertWbPayConfig" parameterType="com.wbgame.pojo.operate.WbPayConfig">
        insert into wb_pay_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appid != null">
                appid,
            </if>
            <if test="pid != null">
                pid,
            </if>
            <if test="cha != null">
                cha,
            </if>
            <if test="pkg != null">
                pkg,
            </if>
            <if test="appname != null">
                appname,
            </if>
            <if test="vivoParam1 != null">
                vivo_param1,
            </if>
            <if test="vivoParam2 != null">
                vivo_param2,
            </if>
            <if test="vivoParam3 != null">
                vivo_param3,
            </if>
            <if test="alipayParam1 != null">
                alipay_param1,
            </if>
            <if test="alipayParam4 != null">
                alipay_param4,
            </if>
            <if test="alipayParam2 != null">
                alipay_param2,
            </if>
            <if test="alipayParam3 != null">
                alipay_param3,
            </if>

            <if test="status != null">
                status,
            </if>

            <if test="createUser != null">
                create_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appid != null">
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="pid != null">
                #{pid,jdbcType=VARCHAR},
            </if>
            <if test="cha != null">
                #{cha,jdbcType=VARCHAR},
            </if>
            <if test="pkg != null">
                #{pkg,jdbcType=VARCHAR},
            </if>
            <if test="appname != null">
                #{appname,jdbcType=VARCHAR},
            </if>
            <if test="vivoParam1 != null">
                #{vivoParam1,jdbcType=VARCHAR},
            </if>
            <if test="vivoParam2 != null">
                #{vivoParam2,jdbcType=VARCHAR},
            </if>
            <if test="vivoParam3 != null">
                #{vivoParam3,jdbcType=VARCHAR},
            </if>
            <if test="alipayParam1 != null">
                #{alipayParam1,jdbcType=VARCHAR},
            </if>
            <if test="alipayParam4 != null">
                #{alipayParam4,jdbcType=VARCHAR},
            </if>
            <if test="alipayParam2 != null">
                #{alipayParam2,jdbcType=LONGVARCHAR},
            </if>
            <if test="alipayParam3 != null">
                #{alipayParam3,jdbcType=LONGVARCHAR},
            </if>

            <if test="status != null">
                #{status},
            </if>

            <if test="createUser != null">
                #{createUser},
            </if>
        </trim>
    </insert>

    <update id="updateWbPayConfig" parameterType="com.wbgame.pojo.operate.WbPayConfig">
        update wb_pay_config
        <set>
            <if test="pkg != null">
                pkg = #{pkg,jdbcType=VARCHAR},
            </if>
            <if test="appname != null">
                appname = #{appname},
            </if>
            <if test="vivoParam1 != null">
                vivo_param1 = #{vivoParam1,jdbcType=VARCHAR},
            </if>
            <if test="vivoParam2 != null">
                vivo_param2 = #{vivoParam2,jdbcType=VARCHAR},
            </if>
            <if test="vivoParam3 != null">
                vivo_param3 = #{vivoParam3,jdbcType=VARCHAR},
            </if>
            <if test="alipayParam1 != null">
                alipay_param1 = #{alipayParam1,jdbcType=VARCHAR},
            </if>
            <if test="alipayParam4 != null">
                alipay_param4 = #{alipayParam4,jdbcType=VARCHAR},
            </if>
            <if test="alipayParam2 != null">
                alipay_param2 = #{alipayParam2,jdbcType=LONGVARCHAR},
            </if>
            <if test="alipayParam3 != null">
                alipay_param3 = #{alipayParam3,jdbcType=LONGVARCHAR},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            update_time = now()
        </set>
        where appid = #{appid,jdbcType=VARCHAR}
        and pid = #{pid,jdbcType=VARCHAR}
        and cha = #{cha,jdbcType=VARCHAR}
    </update>

    <select id="selectExist" parameterType="com.wbgame.pojo.operate.WbPayConfig" resultType="java.lang.Integer">


        select 1  from wb_pay_config
        where

        appid = #{appid} and cha = #{cha} and pid = #{pid}

        limit 1
    </select>

    <insert id="batchInsert">
        <if test="dtoList != null and dtoList.size() > 0 ">
            insert into wb_pay_config (appid,pid,cha,pkg,appname,vivo_param1,vivo_param2,vivo_param3,alipay_param1,alipay_param4,alipay_param2,alipay_param3,status,create_user)
            values
            <foreach collection="dtoList" item="dto" separator=",">
                (#{dto.appid,jdbcType=VARCHAR},#{dto.pid,jdbcType=VARCHAR},#{dto.cha,jdbcType=VARCHAR},#{dto.pkg,jdbcType=VARCHAR},#{dto.appname,jdbcType=VARCHAR},
                #{dto.vivoParam1,jdbcType=VARCHAR},#{dto.vivoParam2,jdbcType=VARCHAR},#{dto.vivoParam3,jdbcType=VARCHAR},#{dto.alipayParam1,jdbcType=VARCHAR},
                #{dto.alipayParam4,jdbcType=VARCHAR},#{dto.alipayParam2,jdbcType=LONGVARCHAR},#{dto.alipayParam3,jdbcType=LONGVARCHAR},#{dto.status},#{dto.createUser})
            </foreach>
        </if>
    </insert>



</mapper>