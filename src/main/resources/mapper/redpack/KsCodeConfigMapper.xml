<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.redpack.KsCodeConfigMapper">

    <sql id="Base_Column_List">
        appid,
        ksAppId,
        ksAppSecret,
        create_time as createTime,
        create_user as createOwner,
        update_time as updateTime,
        update_user as updateOwner,
        ks_pay_key as ksPayKey,
        ks_roll_key as ksRollKey
    </sql>

    <delete id="deleteByAppid" parameterType="java.lang.Integer">
        delete from ks_micgame_config
        where appid in
        <foreach collection="list" item="appid" open="(" separator="," close=")">
            #{appid,jdbcType=INTEGER}
        </foreach>
    </delete>

    <insert id="insertKsCodeConfig" parameterType="com.wbgame.pojo.redpack.KsCodeConfig">
        insert into ks_micgame_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appid != null and appid != ''">
                appid,
            </if>
            <if test="ksAppId != null and ksAppId != ''">
                ksAppId,
            </if>
            <if test="ksAppSecret != null and ksAppSecret != ''">
                ksAppSecret,
            </if>

            <if test="createOwner != null and createOwner != ''">
                create_user,
            </if>
            <if test="ksPayKey != null and ksPayKey != ''">
                ks_pay_key,
            </if>
            <if test="ksRollKey != null and ksRollKey != ''">
                ks_roll_key,
            </if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appid != null and  appid != ''">
                #{appid},
            </if>
            <if test="ksAppId != null and ksAppId != ''">
                #{ksAppId,jdbcType=VARCHAR},
            </if>
            <if test="ksAppSecret != null and ksAppSecret != ''">
                #{ksAppSecret,jdbcType=VARCHAR},
            </if>

            <if test="createOwner != null and createOwner != ''">
                #{createOwner,jdbcType=VARCHAR},
            </if>
            <if test="ksPayKey != null and ksPayKey != ''">
                #{ksPayKey,jdbcType=VARCHAR},
            </if>
            <if test="ksRollKey != null and ksRollKey != ''">
                #{ksRollKey,jdbcType=VARCHAR},
            </if>
            CURRENT_TIMESTAMP
        </trim>
    </insert>

    <update id="updateByKsCodeConfig">
        update ks_micgame_config
        <set>
            <if test="ksAppId != null and ksAppId != ''">
                ksAppId = #{ksAppId,jdbcType=VARCHAR},
            </if>
            <if test="ksAppSecret != null and ksAppSecret != ''">
                ksAppSecret = #{ksAppSecret,jdbcType=VARCHAR},
            </if>

            <if test="updateOwner != null and updateOwner != ''">
                update_user = #{updateOwner,jdbcType=VARCHAR},
            </if>
            ks_roll_key = #{ksRollKey,jdbcType=VARCHAR},
            ks_pay_key = #{ksPayKey,jdbcType=VARCHAR},
            update_time = current_timestamp
        </set>
        where appid = #{appid}
    </update>

    <select id="selectByCondition" resultType="com.wbgame.pojo.redpack.KsCodeConfigVO">
        select
            <include refid="Base_Column_List"/>
        from ks_micgame_config
        <where>
            <choose>
                <when test="appidList != null and appidList.size > 0 ">
                    and appid in
                    <foreach collection="appidList" item="aid" open="(" separator="," close=")">
                        #{aid, jdbcType=INTEGER}
                    </foreach>
                </when>
                <otherwise>
                    and 1 = 1
                </otherwise>
            </choose>
        </where>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>
</mapper>