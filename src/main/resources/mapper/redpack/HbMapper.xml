<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.HbMapper">

    <select id="selectHbWithdrawQuery" resultType="com.wbgame.pojo.redpack.HbWithdrawQuery">
        select ds,appid,pid,channel,amount,round(sum(amount),2) total,count(DISTINCT userid) users,count(1) nums from
        (select ds,appid,pid,channel,amount,userid from hb_new_withdraw_log
        where ds between #{startTime} and #{endTime}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="amount != null and amount != ''">
            and amount = #{amount}
        </if>
        union all
        select date(create_time),appid,pid,channel,amount,imei from hb_ddx_withdraw_log
        where date(create_time) between #{startTime} and #{endTime}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="amount != null and amount != ''">
            and amount = #{amount}
        </if>
        ) a
        group by ${group}
        <choose>
            <when test="order != null and order != ''">
                order by ${order}
            </when>
            <otherwise>
                order by ds asc,users desc
            </otherwise>
        </choose>
    </select>

    <select id="selectHbExamines" resultType="java.util.Map">
    select id,create_time,install_date,appid,pid,channel,userid,nick_name,red_ticket,statusName,total,video,ecpm,amount,withdraw_num,`status`,updateStr,update_owner from
            (select a.id,DATE_FORMAT(a.create_time,'%Y-%m-%d %H:%i:%s') create_time,b.install_date,b.appid,b.pid,b.channel,a.userid,b.nick_name,b.red_ticket,if(status = 0,'待审核',if(status =1,'审核通过','审核拒绝')) statusName,
            b.total,b.video,ROUND(b.ecpm_num/b.video,2) ecpm,a.amount,b.withdraw_num,a.`status`,DATE_FORMAT(a.update_time,'%Y-%m-%d %H:%i:%s') updateStr, update_owner
            from hb_user_withdraw_examine_new a left join hb_redpack_user_new b on a.userid = b.userid and a.appid = b.appid
    <where>1=1
        <if test="appid != null and appid != ''">
            and b.appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and b.channel in (${channel})
        </if>
        <if test="pid != null and pid != ''">
            and b.pid = #{pid}
        </if>
        <if test="userid != null and userid != ''">
            and a.userid = #{userid}
        </if>
        <if test="amount != null">
            and amount = #{amount}
        </if>
        <if test="status != null">
            and a.`status` = #{status}
        </if>
        <if test="minRedticket != null">
            and total <![CDATA[>=]]> #{minRedticket}
        </if>
        <if test="maxRedticket != null">
            and total <![CDATA[<=]]> #{maxRedticket}
        </if>
        <if test="minVideo != null">
            and video <![CDATA[>=]]> #{minVideo}
        </if>
        <if test="maxVideo != null">
            and video <![CDATA[<=]]> #{maxVideo}
        </if>
        <if test="minecpm != null">
            and ROUND(b.ecpm_num/b.video,2) <![CDATA[>=]]> #{minecpm}
        </if>
        <if test="maxecpm != null">
            and ROUND(b.ecpm_num/b.video,2) <![CDATA[<=]]> #{maxecpm}
        </if>
        <if test="minWithdraw != null">
            and withdraw_num <![CDATA[>=]]> #{minWithdraw}
        </if>
        <if test="maxWithdraw != null">
            and withdraw_num <![CDATA[<=]]> #{maxWithdraw}
        </if>
    </where>
    ) a
    order by ${order}
    </select>

    <select id="selectByPrimaryKey" resultType="com.wbgame.pojo.HbUserWithdrawExamine">
        select id,ver
        from hb_user_withdraw_examine_new
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectUserRedpackLog" resultType="com.wbgame.pojo.redpack.HbRedpackLog">
        <foreach collection="list" item="table" separator="union all" >
             select userid,install_date,number,redpack from ${table}
            where install_date between #{startTime} and #{endTime} and appid = #{appid}
            <if test="pid != null and pid != ''">
                and pid = #{pid}
            </if>
        </foreach>
        order by userid,number
    </select>

    <select id="selectUserEcpmLog" resultType="com.wbgame.pojo.redpack.HbEcpmLog">
        <foreach collection="list" item="table" separator="union all" >
            select userid,install_date,number,ecpm from ${table}
            where install_date between #{startTime} and #{endTime} and appid = #{appid}
            <if test="pid != null and pid != ''">
                and pid = #{pid}
            </if>
        </foreach>
        order by userid,number
    </select>

    <select id="selectMonthTables" resultType="java.lang.String">
    select TABLE_NAME from information_schema.TABLES where TABLE_NAME  like #{s}
    </select>

    <select id="countHbWithdrawQuery" resultType="com.wbgame.pojo.redpack.HbWithdrawQuery">
        select round(sum(amount),2) total,count(DISTINCT userid) users,count(1) nums from
        (select ds,appid,pid,channel,amount,userid from hb_new_withdraw_log
        where ds between #{startTime} and #{endTime}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="amount != null and amount != ''">
            and amount = #{amount}
        </if>
        union all
        select date(create_time),appid,pid,channel,amount,imei from hb_ddx_withdraw_log
        where date(create_time) between #{startTime} and #{endTime}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="amount != null and amount != ''">
            and amount = #{amount}
        </if>
        ) a
    </select>

    <!--  红包提现参数配置 删除 -->
    <delete id="deleteRedpackWxconfig" parameterType="com.wbgame.pojo.redpack.HbWithdrawParamHandle" >
        delete from home_diamond_redpack_wxconfig
        where appid = #{appid,jdbcType=INTEGER}
        and pkg = #{pkg,jdbcType=VARCHAR}
        limit 1
    </delete>

    <!--  红包提现参数配置 添加 -->
    <insert id="insertRedpackWxconfig" parameterType="com.wbgame.pojo.redpack.HbWithdrawParamHandle" >
        insert into home_diamond_redpack_wxconfig (appid, pkg, wxAppid,mchid, mchKey,`passwd`,mchName,`wishing`,mode
        ,create_user,update_user,create_time,update_time
        )
        values (#{appid,jdbcType=INTEGER}, #{pkg,jdbcType=VARCHAR}, #{wxAppid,jdbcType=VARCHAR},
        #{mchid,jdbcType=INTEGER}, #{mchKey,jdbcType=VARCHAR},
        #{passwd,jdbcType=VARCHAR}, #{mchName,jdbcType=VARCHAR}, #{wishing,jdbcType=VARCHAR}, #{mode}
        ,#{create_owner},#{update_owner},now(),now()
        )
    </insert>

    <!--  红包提现参数配置 修改 -->
    <update id="updateRedpackWxconfig" parameterType="com.wbgame.pojo.redpack.HbWithdrawParamHandle" >
        update home_diamond_redpack_wxconfig
        <set >
            <if test="wxAppid != null" >
                wxAppid = #{wxAppid,jdbcType=VARCHAR},
            </if>
            <if test="mchid != null" >
                mchid = #{mchid,jdbcType=INTEGER},
            </if>
            <if test="mchKey != null" >
                mchKey = #{mchKey,jdbcType=VARCHAR},
            </if>
            <if test="passwd != null" >
                passwd = #{passwd,jdbcType=VARCHAR},
            </if>
            <if test="mchName != null" >
                mchName = #{mchName,jdbcType=VARCHAR},
            </if>
            <if test="wishing != null" >
                wishing = #{wishing,jdbcType=VARCHAR},
            </if>
            <if test="mode != null" >
                mode = #{mode,jdbcType=VARCHAR},
            </if>
            update_user = #{update_owner},
            update_time = now()
        </set>
        where appid = #{appid,jdbcType=INTEGER}
        and pkg = #{pkg,jdbcType=VARCHAR}
    </update>

    <select id="selectNewRedpackdraw" resultType="com.wbgame.pojo.operate.HbRedpackWithdrawDetailVo">
        select appid,pid,channel,imei,amount,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time
        from hb_new_withdraw_log
        where ds between #{startTime} and #{endTime}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        union all
        select appid,pid,channel,imei,amount,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time
        from hb_ddx_withdraw_log
        where date(create_time) between #{startTime} and #{endTime}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        order by ${order}
    </select>

    <select id="countNewRedpackdraw" resultType="com.wbgame.pojo.operate.HbRedpackWithdrawDetailVo">
        select round(sum(amount),2) amount from
        (select appid,pid,channel,imei,amount,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time
        from hb_new_withdraw_log
        where ds between #{startTime} and #{endTime}
        <if test="appid != null and appid != ''">
            and appid = #{appid}
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="channel != null and channel != ''">
            and channel = #{channel}
        </if>
        union all
        select appid,pid,channel,imei,amount,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time
        from hb_ddx_withdraw_log
        where date(create_time) between #{startTime} and #{endTime}
        <if test="appid != null and appid != ''">
            and appid = #{appid}
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="channel != null and channel != ''">
            and channel = #{channel}
        </if>) a
    </select>

    <select id="selectRedpackTools" resultType="java.util.Map">
        select * from hb_test_tool
    </select>

    <select id="selectMonthChannelRedpackShares" resultType="java.util.Map">
        select #{tdate} tdate,appid,channel,sum(consume) user_share from
        (select appid,channel,round(sum(amount),2) consume from hb_new_withdraw_log where ds between #{startTime} and #{endTime} GROUP BY appid,channel
        union all
        select appid,channel,round(sum(amount),2) consume from hb_user_withdraw_log
        where create_time between concat(#{startTime},' 00:00:00') and concat(#{endTime},' 23:59:59') and channel is not null GROUP BY appid,channel) a
        GROUP BY appid,channel
    </select>

    <select id="selectVivoPayConfig" resultType="com.wbgame.pojo.mobile.ApiPayConfigVo">
        select appid,pkg,public_key,notifyUrl,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time,
        DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') update_time,create_owner,update_owner
        from vivo_pay_config
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pkg != null and pkg != ''">
            and pkg like concat('%',#{pkg},'%')
        </if>
        <if test="apps != null and apps != ''">
            and appid in (${apps})
        </if>
        order by
        <choose>
            <when test="order != null and order != ''">
                ${order}
            </when>
            <otherwise>
                create_time desc
            </otherwise>
        </choose>
    </select>

    <insert id="insertVivoPayConfig">
        insert into vivo_pay_config(appid,pkg,public_key,notifyUrl,create_time,update_time,create_owner,update_owner)
        values
        (#{appid},#{pkg},#{public_key},#{notifyUrl},now(),now(),#{create_owner},#{update_owner})
    </insert>

    <update id="updateVivoPayConfig">
        update vivo_pay_config set
        public_key = #{public_key},
        notifyUrl = #{notifyUrl},
        update_time = now(),
        update_owner = #{update_owner}
        where appid = #{appid} and pkg = #{pkg}
    </update>

    <delete id="deleteVivoPayConfig">
        delete from vivo_pay_config where appid = #{appid} and pkg = #{pkg} limit 1;
    </delete>

    <select id="selectHuaweiPayConfig" resultType="com.wbgame.pojo.mobile.ApiPayConfigVo">
        select appid,pkg,public_key,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time,
        DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') update_time,create_owner,update_owner,client_secret,client_id
        from huawei_pay_config
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pkg != null and pkg != ''">
            and pkg like concat('%',#{pkg},'%')
        </if>
        <if test="apps != null and apps != ''">
            and appid in (${apps})
        </if>
        order by
        <choose>
            <when test="order != null and order != ''">
                ${order}
            </when>
            <otherwise>
                create_time desc
            </otherwise>
        </choose>
    </select>

    <insert id="insertHuaweiPayConfig">
        insert into huawei_pay_config(appid,pkg,public_key,create_time,update_time,create_owner,update_owner,client_secret,client_id)
        values
        (#{appid},#{pkg},#{public_key},now(),now(),#{create_owner},#{update_owner},#{client_secret},#{client_id})
    </insert>

    <update id="updateHuaweiPayConfig">
        update huawei_pay_config set
        public_key = #{public_key},
        client_secret = #{client_secret},
        client_id = #{client_id},
        update_time = now(),
        update_owner = #{update_owner}
        where appid = #{appid} and pkg = #{pkg}
    </update>

    <delete id="deleteHuaweiPayConfig">
        delete from huawei_pay_config where appid = #{appid} and pkg = #{pkg} limit 1;
    </delete>

    <select id="selectMeizuPayConfig" resultType="com.wbgame.pojo.mobile.ApiPayConfigVo">
        select appid,pkg,public_key,notifyUrl,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time,
        DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') update_time,create_owner,update_owner
        from meizu_pay_config
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pkg != null and pkg != ''">
            and pkg like concat('%',#{pkg},'%')
        </if>
        <if test="apps != null and apps != ''">
            and appid in (${apps})
        </if>
        order by
        <choose>
            <when test="order != null and order != ''">
                ${order}
            </when>
            <otherwise>
                create_time desc
            </otherwise>
        </choose>
    </select>

    <insert id="insertMeizuPayConfig">
        insert into meizu_pay_config(appid,pkg,public_key,notifyUrl,create_time,update_time,create_owner,update_owner)
        values
        (#{appid},#{pkg},#{public_key},#{notifyUrl},now(),now(),#{create_owner},#{update_owner})
    </insert>

    <update id="updateMeizuPayConfig">
        update meizu_pay_config set
        public_key = #{public_key},
        notifyUrl = #{notifyUrl},
        update_time = now(),
        update_owner = #{update_owner}
        where appid = #{appid} and pkg = #{pkg}
    </update>

    <delete id="deleteMeizuPayConfig">
        delete from meizu_pay_config where appid = #{appid} and pkg = #{pkg} limit 1;
    </delete>

    <select id="selectFtnnPayConfig" resultType="com.wbgame.pojo.mobile.ApiPayConfigVo">
        select appid,public_key,notifyUrl,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time,
        DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') update_time,create_owner,update_owner
        from ftnn_pay_config
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="apps != null and apps != ''">
            and appid in (${apps})
        </if>
        order by
        <choose>
            <when test="order != null and order != ''">
                ${order}
            </when>
            <otherwise>
                create_time desc
            </otherwise>
        </choose>
    </select>

    <insert id="insertFtnnPayConfig">
        insert into ftnn_pay_config(appid,public_key,notifyUrl,create_time,update_time,create_owner,update_owner)
        values
        (#{appid},#{public_key},#{notifyUrl},now(),now(),#{create_owner},#{update_owner})
    </insert>

    <update id="updateFtnnPayConfig">
        update ftnn_pay_config set
        public_key = #{public_key},
        notifyUrl = #{notifyUrl},
        update_time = now(),
        update_owner = #{update_owner}
        where appid = #{appid}
    </update>

    <delete id="deleteFtnnPayConfig">
        delete from ftnn_pay_config where appid = #{appid} limit 1;
    </delete>


    <select id="selectDouyinConfig" parameterType="com.wbgame.pojo.mobile.DouyinConfigVo" resultType="com.wbgame.pojo.mobile.DouyinConfigVo">
        select appid, channel, `key`, create_time, update_time, create_owner, update_owner
        from dy_pay_config
        where 1 = 1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="key != null and key != ''">
            and `key` = #{key}
        </if>
        order by
        <choose>
            <when test="order != null and order != ''">
                ${order}
            </when>
            <otherwise>
                create_time desc
            </otherwise>
        </choose>
    </select>

    <insert id="insertDouyinPayConfig" parameterType="com.wbgame.pojo.mobile.DouyinConfigVo">
        insert into dy_pay_config(appid,channel,`key`,create_time,create_owner)
        values(#{appid},#{channel},#{key},now(),#{create_owner})
    </insert>

    <update id="updateDouyinPayConfig" parameterType="com.wbgame.pojo.mobile.DouyinConfigVo">
        update dy_pay_config set
        `key` = #{key},
        update_time = now(),
        update_owner = #{update_owner}
        where appid = #{appid}
    </update>

    <delete id="deleteDouyinPayConfig" parameterType="com.wbgame.pojo.mobile.DouyinConfigVo">
        delete from dy_pay_config where appid = #{appid} limit 1;
    </delete>



    <select id="selectIosConfig" parameterType="com.wbgame.pojo.mobile.IosConfigVo" resultType="com.wbgame.pojo.mobile.IosConfigVo">
        select product_id, appid, money, product_name, create_time, update_time, create_owner, update_owner
        from ios_pay_product_config
        where 1=1
        <if test="product_id != null and product_id != ''">
            and product_id = #{product_id}
        </if>
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>

        <if test="product_name != null and product_name != ''">
            and product_name like CONCAT("%",#{product_name},"%")
        </if>
        order by
        <choose>
            <when test="order != null and order != ''">
                ${order}
            </when>
            <otherwise>
                create_time desc
            </otherwise>
        </choose>
    </select>


    <insert id="insertIosPayConfig" parameterType="com.wbgame.pojo.mobile.IosConfigVo">
        insert into ios_pay_product_config(
            product_id,appid,money,product_name,create_time,create_owner
        )
        values(#{product_id},#{appid},#{money}, #{product_name},now(),#{create_owner})
    </insert>

    <update id="updateIosPayConfig" parameterType="com.wbgame.pojo.mobile.IosConfigVo">
        update ios_pay_product_config set
         money = #{money},
         product_name = #{product_name},
         update_time = now(),
         update_owner = #{update_owner}
        where product_id = #{product_id}
    </update>

    <delete id="deleteIosPayConfig" parameterType="com.wbgame.pojo.mobile.IosConfigVo">
        delete from ios_pay_product_config where product_id = #{product_id} and appid = #{appid} limit 1;
    </delete>

    <select id="selectMdsConfig" resultType="java.util.Map">
        select appkey,offer_id,wxappid from xyx_mds_config where appid = #{appid}
    </select>

    <select id="selectMdsConfigs" resultType="com.wbgame.pojo.operate.MdsConfigVo">
        select appid,appkey,offer_id,wxappid,new_key,api_ver,
        DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time,
        DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') update_time,create_owner,update_owner
        from xyx_mds_config where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        order by
        <choose>
            <when test="order != null and order != ''">
                ${order}
            </when>
            <otherwise>
                create_time desc
            </otherwise>
        </choose>
    </select>

    <insert id="addMdsConfig">
        insert into xyx_mds_config(
            appid,appkey,offer_id,wxappid,new_key,api_ver,create_time,create_owner,update_time,update_owner
        )
        values(#{appid},#{appkey},#{offer_id}, #{wxappid},#{new_key},#{api_ver},now(),#{create_owner},now(),#{update_owner})
    </insert>

    <update id="updateMdsConfig">
        update xyx_mds_config set
         appkey = #{appkey},
         offer_id = #{offer_id},
         wxappid = #{wxappid},
         new_key = #{new_key},
         api_ver = #{api_ver},
         update_time = now(),
         update_owner = #{update_owner}
        where appid = #{appid}
    </update>

    <delete id="deleteMdsConfig">
        delete from xyx_mds_config where appid = #{appid} limit 1
    </delete>

    <select id="selectExchangeCodes" resultType="com.wbgame.pojo.operate.ExchangeCodeVo">
        select code,status,type,value,num,create_owner,date_format(create_time, '%Y-%m-%d %H:%i:%S') create_time,
        date_format(update_time, '%Y-%m-%d %H:%i:%S') update_time,date_format(expire_time, '%Y-%m-%d %H:%i:%S') expire_time,ifnull(use_num,0) use_num
        from exchange_code_config a left join
        (select code code_log,count(1) use_num from exchange_code_log group by code) b on a.code = b.code_log
        where 1=1
        <if test="code != null and code != ''">
            and code = #{code}
        </if>
        <if test="code_value != null and code_value != ''">
            and value = #{code_value}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="create_owner != null and create_owner != ''">
            and create_owner = #{create_owner}
        </if>
        order by
        <choose>
            <when test="order != null and order != ''">
                ${order}
            </when>
            <otherwise>
                create_time desc
            </otherwise>
        </choose>
    </select>

    <insert id="addExchangeCodes">
         insert into exchange_code_config(code,type,value,num,status,create_owner,create_time,expire_time)
         values (#{code},#{type},#{value},#{num},1,#{create_owner},now(),#{expire_time})
    </insert>

    <insert id="addbatchExchangeCodes">
        insert into exchange_code_config(code,type,value,num,status,create_owner,create_time,expire_time)
        values
        <foreach collection="list" separator="," item="it">
            (#{it.code},#{it.type},#{it.value},#{it.num},1,#{it.create_owner},now(),#{it.expire_time})
        </foreach>
    </insert>

    <select id="selectWxAmountWithdraws" resultType="com.alibaba.fastjson.JSONObject">
        select mchid,amount,scan_time,alarm_amount from wx_amount_withdraw_monitor where status = 1
    </select>

    <select id="selectMchidAmount" resultType="com.alibaba.fastjson.JSONObject">
        select sum(amount) amount from
        (select round(sum(amount),2) amount from hb_ddx_withdraw_log a
        left join home_diamond_redpack_wxconfig b on a.package = b.pkg and a.appid = b.appid
        where a.create_time between #{scan_time} and now() and mode = 1 and mchid =#{mchid}
        union all
        select sum(amount) amount from hb_new_withdraw_log a
        left join hb_redpack_user_new b on a.userid = b.userid
        left join home_diamond_redpack_wxconfig c on a.appid = c.appid and b.package_name = c.pkg
        where a.create_time BETWEEN #{scan_time} and now() and `mode` = 1 and mchid =#{mchid}) a

    </select>

    <select id="selectRedpackWxconfig" resultType="com.wbgame.pojo.redpack.HbWithdrawParamVo">
        select appid, pkg, wxAppid,mchid, mchKey,`passwd`,mchName,`wishing`,mode
        ,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') update_time
        ,create_user,update_user
        from home_diamond_redpack_wxconfig
        where 1=1
        <if test="appid != null and appid != ''" >
            and appid = #{appid}
        </if>
        <if test="pkg != null and pkg != ''" >
            and pkg = #{pkg}
        </if>
        <if test="mchid != null and mchid != ''" >
            and mchid = #{mchid}
        </if>
        <if test="mode != null and mode != ''" >
            and mode = #{mode}
        </if>
        order by update_time desc
    </select>

    <select id="selectVivoMiniConfigs" resultType="com.wbgame.pojo.operate.VivoMiniConfigVo">
        select * from vivo_quick_config where 1=1
        <if test="appid != null and appid != ''" >
            and appid in (${appid})
        </if>
        <if test="pkgName != null and pkgName != ''" >
            and pkgName like concat('%',#{pkgName},'%')
        </if>
        <if test="tappid != null and tappid != ''" >
            and tappid like concat('%',#{tappid},'%')
        </if>
        order by
        <choose>
            <when test="order != null and order != ''">
                ${order}
            </when>
            <otherwise>
                create_time desc
            </otherwise>
        </choose>
    </select>

    <insert id="addVivoMiniConfig">
        insert ignore into vivo_quick_config(appid,tappid,tappName,appKey,appSecret,pkgName,state,create_owner,update_owner)
        values
        <foreach collection="list" separator="," item="it">
            (#{it.appid},#{it.tappid},#{it.tappName},#{it.appKey},#{it.appSecret},#{it.pkgName},#{it.state},#{it.create_owner},#{it.update_owner})
        </foreach>
    </insert>

    <update id="updateVivoMiniConfig">
        update vivo_quick_config set
         appid = #{appid},
         tappid = #{tappid},
         tappName = #{tappName},
         appKey = #{appKey},
         appSecret = #{appSecret},
         pkgName = #{pkgName},
         state = #{state},
         update_owner = #{update_owner}
         where appid = #{appid}
    </update>

    <delete id="deleteVivoMiniConfig">
        delete from vivo_quick_config where appid = #{appid} limit 1
    </delete>

    <select id="selectPayWarnConfig" resultType="com.wbgame.pojo.operate.PayWarnConfigVo">
        select appid,createtime,updatetime,create_owner,update_owner from pay_no_warn_config
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        order by createtime desc
    </select>

    <insert id="addPayWarnConfig">
        insert ignore into pay_no_warn_config(appid,createtime,updatetime,create_owner,update_owner)
        values (#{appid},#{createtime},#{updatetime},#{create_owner},#{update_owner})
    </insert>

    <delete id="deletePayWarnConfig">
        delete from pay_no_warn_config where appid = #{appid} limit 1
    </delete>

    <select id="selectSubPayConfig" resultType="com.wbgame.pojo.WbPayConfig">
        select * from wb_pay_config where appid = #{appid} and alipay_param4 = #{param2} limit 1
    </select>

    <select id="selectOnePayConfig" resultType="com.wbgame.pojo.WbPayConfig">
        select * from ali_one_pay_config where appid = #{appid} and alipay_param4 = #{param2} limit 1
    </select>

    <select id="selectcpRefundUrl" resultType="java.lang.String">
        select refund_url from cp_pay_rollback_config where appid = #{appid}
    </select>

    <select id="selectCloseExchange" resultType="com.wbgame.pojo.operate.ExchangeCodeVo">
        select code,status,type,create_owner from exchange_code_config
        where code in
        (<foreach collection="list" item="item" separator=",">
        #{item}
        </foreach>)
    </select>

</mapper>