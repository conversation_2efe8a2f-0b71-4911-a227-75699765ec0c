<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.WxAmountWithdrawMonitorMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.operate.WxAmountWithdrawMonitorVO">
        <id column="mchid" property="mchid" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="scan_time" property="scanTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_owner" property="createOwner" jdbcType="VARCHAR"/>
        <result column="update_owner" property="updateOwner" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="alarm_amount" property="alarmAmount" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        mchid
        , amount, DATE_FORMAT(scan_time, '%Y-%m-%d %H:%i:%s') scan_time,
        DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') create_time,
        DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') update_time, create_owner, update_owner, status,
    alarm_amount
    </sql>

    <select id="selectWxAmountWithdrawMonitor" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.operate.WxAmountWithdrawMonitorDTO">
        select
        <include refid="Base_Column_List"/>
        from wx_amount_withdraw_monitor
        <where>

            <if test="mchid != null and mchid != ''">
                and mchid = #{mchid}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        order by create_time desc
    </select>
    <delete id="deleteByKey" parameterType="java.lang.String">
        delete
        from wx_amount_withdraw_monitor
        where mchid in
        <foreach collection="list" item="m" open="(" separator="," close=")">
            #{m}
        </foreach>
    </delete>


    <insert id="insertWxAmountWithdrawMonitor" parameterType="com.wbgame.pojo.operate.WxAmountWithdrawMonitor">
        insert into wx_amount_withdraw_monitor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mchid != null and mchid != ''">
                mchid,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="scanTime != null and scanTime != ''">
                scan_time,
            </if>

            <if test="createOwner != null and createOwner != ''">
                create_owner,
            </if>

            <if test="status != null">
                status,
            </if>
            <if test="alarmAmount != null">
                alarm_amount,
            </if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mchid != null and mchid != ''">
                #{mchid,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="scanTime != null and scanTime != ''">
                #{scanTime,jdbcType=TIMESTAMP},
            </if>

            <if test="createOwner != null and createOwner != ''">
                #{createOwner,jdbcType=VARCHAR},
            </if>

            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="alarmAmount != null">
                #{alarmAmount,jdbcType=DECIMAL},
            </if>
            current_timestamp,
            current_timestamp
        </trim>
    </insert>


    <update id="updateByKey" parameterType="com.wbgame.pojo.operate.WxAmountWithdrawMonitor">
        update wx_amount_withdraw_monitor
        <set>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="scanTime != null">
                scan_time = #{scanTime,jdbcType=TIMESTAMP},
            </if>

            <if test="updateOwner != null and updateOwner != ''">
                update_owner = #{updateOwner,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="alarmAmount != null">
                alarm_amount = #{alarmAmount,jdbcType=DECIMAL},
            </if>
            update_time = current_timestamp
        </set>
        where mchid = #{mchid,jdbcType=VARCHAR}
    </update>

</mapper>