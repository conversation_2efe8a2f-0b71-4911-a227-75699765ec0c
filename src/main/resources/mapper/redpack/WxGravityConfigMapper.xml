<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.redpack.WxGravityConfigMapper">

    <sql id="Base_Column_List">
        id,
        appid,
        wxappid,
        gravity_appid as gravityAppid,
        gravity_key as gravityKey,
        gravity_access_token as gravityAccessToken,
        create_user as createUser,
        create_time as createTime,
        modify_user as modifyUser,
        modify_time as modifyTime
    </sql>

    <delete id="deleteByIdList" parameterType="java.lang.Integer">
        DELETE FROM wx_gravity_config
        WHERE id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <insert id="insertWxGravityConfig" parameterType="com.wbgame.pojo.redpack.WxGravityConfig">
        INSERT INTO wx_gravity_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appid != null and appid != ''">
                appid,
            </if>
            <if test="wxappid != null and wxappid != ''">
                wxappid,
            </if>
            <if test="gravityAppid != null and gravityAppid != ''">
                gravity_appid,
            </if>
            <if test="gravityKey != null and gravityKey != ''">
                gravity_key,
            </if>
            <if test="gravityAccessToken != null and gravityAccessToken != ''">
                gravity_access_token,
            </if>
            <if test="createUser != null and createUser != ''">
                create_user,
            </if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appid != null and  appid != ''">
                #{appid},
            </if>
            <if test="wxappid != null and wxappid != ''">
                #{wxappid,jdbcType=VARCHAR},
            </if>
            <if test="gravityAppid != null and gravityAppid != ''">
                #{gravityAppid,jdbcType=VARCHAR},
            </if>
            <if test="gravityKey != null and gravityKey != ''">
                #{gravityKey,jdbcType=VARCHAR},
            </if>
            <if test="gravityAccessToken != null and gravityAccessToken != ''">
                #{gravityAccessToken,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>
            CURRENT_TIMESTAMP
        </trim>
    </insert>

    <update id="updateById">
        UPDATE wx_gravity_config
        <set>
            <if test="wxappid != null and wxappid != ''">
                wxappid = #{wxappid,jdbcType=VARCHAR},
            </if>
            <if test="gravityAppid != null and gravityAppid != ''">
                gravity_appid = #{gravityAppid,jdbcType=VARCHAR},
            </if>
            <if test="gravityKey != null and gravityKey != ''">
                gravity_key = #{gravityKey,jdbcType=VARCHAR},
            </if>
            <if test="gravityAccessToken != null and gravityAccessToken != ''">
                gravity_access_token = #{gravityAccessToken,jdbcType=VARCHAR},
            </if>
            <if test="modifyUser != null and modifyUser != ''">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            modify_time = current_timestamp
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectByCondition" resultType="com.wbgame.pojo.redpack.WxGravityConfig">
        SELECT
            <include refid="Base_Column_List"/>
        FROM wx_gravity_config
        <where>
            <if test="appidList != null and appidList.size() > 0">
                appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid, jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        <choose>
            <when test="order_str != null and order_str != ''">
                ORDER BY ${order_str}
            </when>
            <otherwise>
                ORDER BY create_time DESC
            </otherwise>
        </choose>
    </select>
    <select id="selectByIdList" resultType="com.wbgame.pojo.redpack.WxGravityConfig">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wx_gravity_config
        <where>
            <if test="idList != null and idList.size() > 0">
                id IN
                <foreach collection="idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
</mapper>