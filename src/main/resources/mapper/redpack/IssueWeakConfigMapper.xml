<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.IssueWeakConfigMapper">

    <select id="selectIssueWeakConfigs" resultType="com.wbgame.pojo.redpack.HbIssueWeakConfig">
        select pid,min_money,max_money,rate
        ,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_str
        ,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') update_str
        ,create_owner,update_owner
        from hb_issue_rate_config
        where 1=1
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <choose>
            <when test="order != null and order != ''">
                order by ${order}
            </when>
            <otherwise>
                order by create_time desc
            </otherwise>
        </choose>
    </select>

    <delete id="deleteIssueWeakConfig">
        delete from hb_issue_rate_config where pid = #{pid}
        <if test="min_money != null">
            and min_money = #{min_money}
        </if>
        <if test="max_money != null">
            and max_money = #{max_money}
        </if>
    </delete>

    <insert id="addIssueWeakConfig">
        insert into hb_issue_rate_config(pid,min_money,max_money,rate,create_time,update_time,create_owner,update_owner)
        values
        <foreach collection="list" item="it" separator=",">
            (#{it.pid},#{it.min_money},#{it.max_money},#{it.rate},now(),now(),#{it.create_owner},#{it.update_owner})
        </foreach>
    </insert>

    <update id="updateIssueWeakConfig">
        update hb_issue_rate_config set
        rate = #{rate},
        update_owner = #{update_owner},
        update_time = now()
        where pid = #{pid} and min_money = #{min_money} and max_money = #{max_money}
    </update>


</mapper>