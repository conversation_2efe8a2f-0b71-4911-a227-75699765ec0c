<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.redpack.HbWithdrawChannelControlMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.mobile.HbWithdrawChannelControl">
    <!--@mbg.generated-->
    <!--@Table hb_withdraw_channel_control-->
    <id column="appid" jdbcType="INTEGER" property="appid" />
    <id column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_owner" jdbcType="VARCHAR" property="updateOwner" />
    <result column="create_owner" jdbcType="VARCHAR" property="createOwner" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    appid, channel, update_time, create_time, update_owner, create_owner
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hb_withdraw_channel_control
    where appid = #{appid,jdbcType=INTEGER}
      and channel = #{channel,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--@mbg.generated-->
    delete from hb_withdraw_channel_control
    where appid = #{appid,jdbcType=INTEGER}
      and channel = #{channel,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.wbgame.pojo.mobile.HbWithdrawChannelControl">
    <!--@mbg.generated-->
    insert into hb_withdraw_channel_control (appid, channel, update_time, 
      create_time, update_owner, create_owner
      )
    values (#{appid,jdbcType=INTEGER}, #{channel,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateOwner,jdbcType=VARCHAR}, #{createOwner,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wbgame.pojo.mobile.HbWithdrawChannelControl">
    <!--@mbg.generated-->
    insert into hb_withdraw_channel_control
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appid != null">
        appid,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateOwner != null">
        update_owner,
      </if>
      <if test="createOwner != null">
        create_owner,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appid != null">
        #{appid,jdbcType=INTEGER},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOwner != null">
        #{updateOwner,jdbcType=VARCHAR},
      </if>
      <if test="createOwner != null">
        #{createOwner,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.mobile.HbWithdrawChannelControl">
    <!--@mbg.generated-->
    update hb_withdraw_channel_control
    <set>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOwner != null">
        update_owner = #{updateOwner,jdbcType=VARCHAR},
      </if>
      <if test="createOwner != null">
        create_owner = #{createOwner,jdbcType=VARCHAR},
      </if>
    </set>
    where appid = #{appid,jdbcType=INTEGER}
      and channel = #{channel,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.mobile.HbWithdrawChannelControl">
    <!--@mbg.generated-->
    update hb_withdraw_channel_control
    set update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_owner = #{updateOwner,jdbcType=VARCHAR},
      create_owner = #{createOwner,jdbcType=VARCHAR}
    where appid = #{appid,jdbcType=INTEGER}
      and channel = #{channel,jdbcType=VARCHAR}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-11-10-->
  <select id="selectByAll" resultMap="BaseResultMap">
        select
    appid, channel, update_owner, create_owner,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createStr,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') updateStr
        from hb_withdraw_channel_control
        <where>
            <if test="appid != null">
                and appid=#{appid,jdbcType=INTEGER}
            </if>
            <if test="channel != null and channel != ''">
                and channel=#{channel,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and update_time=#{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createTime != null">
                and create_time=#{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateOwner != null">
                and update_owner=#{updateOwner,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>