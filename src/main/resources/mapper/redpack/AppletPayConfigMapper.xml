<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.AppletPayConfigMapper">

    <resultMap id="BaseResultMap" type="com.wbgame.pojo.operate.TtAppletPayConfig">
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="tt_id" property="tt_id" jdbcType="VARCHAR"/>
        <result column="app_secret" property="app_secret" jdbcType="VARCHAR"/>
        <result column="public_version" property="public_version" jdbcType="VARCHAR"/>
        <result column="private_key" property="private_key" jdbcType="VARCHAR"/>
        <result column="create_user" property="create_user" jdbcType="VARCHAR"/>
        <result column="update_user" property="update_user" jdbcType="VARCHAR"/>
        <result column="create_time" property="create_time" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        appid,tt_id,app_secret,public_version,private_key, create_user,update_user,create_time,update_time
    </sql>

    <delete id="deleteByAppids">
        DELETE FROM tt_applet_pay_config WHERE appid IN
        <foreach collection="appids" item="appid" open="(" close=")" separator=",">
            #{appid}
        </foreach>
    </delete>

    <select id="selectAppletPayConfig" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.operate.TtAppletPayConfig">
        select
        <include refid="Base_Column_List"/>
        from tt_applet_pay_config
        <where>
            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>
        <if test="order_str == null or order_str == ''">
            order by create_time desc
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>


    <insert id="insertAppletPayConfig" parameterType="com.wbgame.pojo.operate.TtAppletPayConfig">
        insert into tt_applet_pay_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appid != null">
                appid,
            </if>
            <if test="tt_id != null">
                tt_id,
            </if>
            <if test="app_secret != null">
                app_secret,
            </if>
            <if test="public_version != null">
                public_version,
            </if>
            <if test="private_key != null">
                private_key,
            </if>
            <if test="create_user != null">
                create_user
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appid != null">
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="tt_id != null">
                #{tt_id,jdbcType=VARCHAR},
            </if>
            <if test="app_secret != null">
                #{app_secret,jdbcType=VARCHAR},
            </if>
            <if test="public_version != null">
                #{public_version,jdbcType=VARCHAR},
            </if>
            <if test="private_key != null">
                #{private_key,jdbcType=LONGVARCHAR},
            </if>
            <if test="create_user != null">
                #{create_user}
            </if>
        </trim>
    </insert>

    <update id="updateAppletPayConfig" parameterType="com.wbgame.pojo.operate.TtAppletPayConfig">
        update tt_applet_pay_config
        <set>
            <if test="tt_id != null">
                tt_id = #{tt_id,jdbcType=VARCHAR},
            </if>
            <if test="app_secret != null">
                app_secret = #{app_secret,jdbcType=VARCHAR},
            </if>
            <if test="private_key != null">
                private_key = #{private_key,jdbcType=VARCHAR},
            </if>
            <if test="update_user != null">
                update_user = #{update_user},
            </if>
            <if test="public_version != null">
                public_version = #{public_version},
            </if>
            update_time = now()
        </set>
        where appid = #{appid,jdbcType=INTEGER}
    </update>

</mapper>