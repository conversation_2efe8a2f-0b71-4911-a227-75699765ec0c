<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.HbFunctonSwitchMapper">

    <select id="selectFunctonSwitchs" resultType="com.wbgame.pojo.mobile.HbFunctionSwitch">
        select *,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createStr,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s')  updateStr from hb_function_switch where 1=1
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
    </select>

    <insert id="addFunctonSwitch">
         insert into hb_function_switch(pid,`natural`,extra,decline,rebate,`limit`,create_owner,update_owner,create_time,update_time)
         values
         (#{pid},#{natural},#{extra},#{decline},#{rebate},#{limit},#{create_owner},#{update_owner},now(),now())
    </insert>

    <update id="updateFunctonSwitch">
        update hb_function_switch set
        `natural` = #{natural},
        extra = #{extra},
        decline = #{decline},
        rebate = #{rebate},
        `limit` = #{limit},
        update_owner = #{update_owner},
        update_time = now()
        where pid = #{pid}
    </update>

    <delete id="deleteFunctonSwitch">
        delete from hb_function_switch where pid = #{pid} limit 1
    </delete>


</mapper>