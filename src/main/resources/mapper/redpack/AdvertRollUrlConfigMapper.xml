<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.AdvertRollUrlConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.redpack.AdvertRollUrlConfigVO">
        <id column="appid" property="appid" jdbcType="INTEGER"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        appid
        , url, create_user, update_user,
        date_format(create_time, '%Y-%m-%d %H:%i:%s') create_time,
        date_format(update_time, '%Y-%m-%d %H:%i:%s') update_time,
        status
    </sql>
    <select id="selectAdvertRollUrlConfig" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.redpack.AdvertRollUrlConfig">
        select

        <include refid="Base_Column_List"/>
        from advert_roll_url_config
        <where>

            <if test="status != null">
                and status = #{status}
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

        </where>

        order by create_time desc

    </select>

    <delete id="deleteAdvertRollUrlConfig" parameterType="java.lang.Integer">
        delete
        from advert_roll_url_config
        where appid in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertAdvertRollUrlConfig" parameterType="com.wbgame.pojo.redpack.AdvertRollUrlConfig">
        insert into advert_roll_url_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appid != null">
                appid,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="createUser != null">
                create_user,
            </if>

            <if test="status != null">
                status,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appid != null">
                #{appid,jdbcType=INTEGER},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status},
            </if>

        </trim>
    </insert>


    <update id="updateAdvertRollUrlConfig" parameterType="com.wbgame.pojo.redpack.AdvertRollUrlConfig">
        update advert_roll_url_config
        <set>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>

            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>

            <if test="status != null">
                status = #{status},
            </if>

            update_time = now()
        </set>
        where appid = #{appid,jdbcType=INTEGER}
    </update>

</mapper>