<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.PayMerchantConfigMapper">

    <resultMap id="BaseResultMap" type="com.wbgame.pojo.operate.PayMerchantConfig">
        <result column="mch_id" property="mch_id" jdbcType="VARCHAR"/>
        <result column="method" property="method" jdbcType="INTEGER"/>
        <result column="company" property="company" jdbcType="VARCHAR"/>
        <result column="notes" property="notes" jdbcType="VARCHAR"/>
        <result column="create_time" property="create_time" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="update_time" jdbcType="TIMESTAMP"/>
        <result column="create_owner" property="create_owner" jdbcType="VARCHAR"/>
        <result column="update_owner" property="update_owner" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        mch_id,`method`,company,notes,create_time, update_time,create_owner,update_owner
    </sql>

    <delete id="deletePayMchConfig">
        DELETE FROM pay_merchant_config WHERE mch_id IN
        <foreach collection="mchidList" item="mch_id" open="(" close=")" separator=",">
            #{mch_id}
        </foreach>
    </delete>

    <select id="selectPayMchConfig" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.operate.PayMerchantConfigDTO">
        select
        <include refid="Base_Column_List"/>
        from pay_merchant_config
        <where>
            <if test="mch_id != null and mch_id != ''">
                mch_id like concat('%',#{mch_id},'%')
            </if>
            <if test="method != null">
                and `method` = #{method}
            </if>
            <if test="company != null and company != ''">
                and company in (${company})
            </if>
        </where>
        <if test="order_str == null or order_str == ''">
            order by create_time desc
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>


    <insert id="insertPayMchConfig" parameterType="com.wbgame.pojo.operate.PayMerchantConfigDTO">
        insert into pay_merchant_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mch_id != null">
                mch_id,
            </if>
            <if test="method != null">
                method,
            </if>
            <if test="company != null">
                company,
            </if>
            <if test="notes != null">
                notes,
            </if>
            <if test="create_owner != null">
                create_owner
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mch_id != null">
                #{mch_id,jdbcType=VARCHAR},
            </if>
            <if test="method != null">
                #{method,jdbcType=INTEGER},
            </if>
            <if test="company != null">
                #{company,jdbcType=VARCHAR},
            </if>
            <if test="notes != null">
                #{notes,jdbcType=VARCHAR},
            </if>
            <if test="create_owner != null">
                #{create_owner,jdbcType=LONGVARCHAR}
            </if>
        </trim>
    </insert>

    <update id="updatePayMchConfig" parameterType="com.wbgame.pojo.operate.PayMerchantConfigDTO">
        update pay_merchant_config
        <set>
            <if test="method != null">
                method = #{method,jdbcType=INTEGER},
            </if>
            <if test="company != null">
                company = #{company,jdbcType=VARCHAR},
            </if>
            <if test="notes != null">
                notes = #{notes,jdbcType=VARCHAR},
            </if>
            <if test="update_owner != null">
                update_owner = #{update_owner},
            </if>
            update_time = now()
        </set>
        where mch_id = #{mch_id,jdbcType=VARCHAR}
    </update>

</mapper>