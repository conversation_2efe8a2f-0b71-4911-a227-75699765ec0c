<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.SubscriptionTemplateConfig">


    <select id="selectList" resultType="com.wbgame.pojo.operate.SubscriptionTemplateConfigVo">
        select appid, template_id, page, json, create_time, update_time, create_owner, update_owner,template_type
        from wx_subscribe_template
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="template_type != null and template_type != ''">
            and template_type = #{template_type}
        </if>
        <if test="template_id != null and template_id != ''">
            and template_id = #{template_id}
        </if>
        <if test="page != null and page != ''">
            and page = #{page}
        </if>
        <if test="json != null and json != ''">
            and json like concat("%",#{json},"%")
        </if>

        <choose>
            <when test="order != null and order != ''">
                order by ${order}
            </when>
            <otherwise>
                order by update_time desc
            </otherwise>
        </choose>
    </select>

    <insert id="SubscriptionTemplateConfigAdd" parameterType="com.wbgame.pojo.operate.SubscriptionTemplateConfigVo">
        insert into wx_subscribe_template (appid,template_type,template_id,page,json,create_time,create_owner) value(
            #{appid},#{template_type},#{template_id},#{page},#{json},now(),#{create_owner}
        )
    </insert>

    <update id="SubscriptionTemplateConfigEdit" parameterType="com.wbgame.pojo.operate.SubscriptionTemplateConfigVo" >
        update wx_subscribe_template set
        page = #{page},
        json = #{json},
        update_time = now(),
        update_owner = #{update_owner},
        template_type = #{template_type}
        where appid = #{appid} and template_id = #{template_id}
    </update>

    <delete id="SubscriptionTemplateConfigDel" parameterType="com.wbgame.pojo.operate.SubscriptionTemplateConfigVo">
        delete from wx_subscribe_template where appid = #{appid} and template_id = #{template_id}
    </delete>


</mapper>