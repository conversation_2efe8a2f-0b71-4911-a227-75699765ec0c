<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.HbWithdrawDescMapper">

    <select id="selectByAll" resultType="java.util.Map">
        select appid,`desc`,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') update_time,create_owner,update_owner
        from hb_withdraw_desc
        where 1=1
        <if test="appid != null and appid != ''">
            and appid = #{appid}
        </if>
    </select>

    <insert id="insertWithdrawDesc">
        insert into hb_withdraw_desc(appid,`desc`,create_time,update_time,create_owner,update_owner)
        values
        (#{appid},#{desc},#{create_time},#{update_time},#{create_owner},#{update_owner})
    </insert>

    <update id="updateWithdrawDesc">
        update hb_withdraw_desc set
        `desc` = #{desc},
        update_time = #{update_time},
        update_owner = #{update_owner}
        where  appid = #{appid}
    </update>

</mapper>