<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.TtCodeConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.redpack.TtCodeConfigVO">
        <id column="appid" property="appid" jdbcType="INTEGER"/>
        <result column="ttid" property="ttid" jdbcType="VARCHAR"/>
        <result column="app_secret" property="appSecret" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_owner" property="createOwner" jdbcType="VARCHAR"/>
        <result column="update_owner" property="updateOwner" jdbcType="VARCHAR"/>
        <result column="ios_pay_switch" property="iosPaySwitch" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        appid, ttid, app_secret, create_time, update_time, create_owner, update_owner,ios_pay_switch
    </sql>

    <delete id="deleteByAppid" parameterType="java.lang.Integer">
        delete
        from tt_code_config
        where appid in
        <foreach collection="list" item="appid" open="(" separator="," close=")">

            #{appid,jdbcType=INTEGER}
        </foreach>

    </delete>


    <insert id="insertTtCodeConfig" parameterType="com.wbgame.pojo.redpack.TtCodeConfig">
        insert into tt_code_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appid != null and appid != ''">
                appid,
            </if>
            <if test="ttid != null and ttid != ''">
                ttid,
            </if>
            <if test="appSecret != null and appSecret != ''">
                app_secret,
            </if>

            <if test="createOwner != null and createOwner != ''">
                create_owner,
            </if>
            <if test="iosPaySwitch != null and iosPaySwitch != ''">
                ios_pay_switch,
            </if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appid != null and  appid != ''">
                #{appid,jdbcType=INTEGER},
            </if>
            <if test="ttid != null and ttid != ''">
                #{ttid,jdbcType=VARCHAR},
            </if>
            <if test="appSecret != null and appSecret != ''">
                #{appSecret,jdbcType=VARCHAR},
            </if>

            <if test="createOwner != null and createOwner != ''">
                #{createOwner,jdbcType=VARCHAR},
            </if>
            <if test="iosPaySwitch != null and iosPaySwitch != ''">
                #{iosPaySwitch,jdbcType=VARCHAR},
            </if>
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        </trim>
    </insert>


    <update id="updateByTtCodeConfig" parameterType="com.wbgame.pojo.redpack.TtCodeConfig">
        update tt_code_config
        <set>
            <if test="ttid != null and ttid != ''">
                ttid = #{ttid,jdbcType=VARCHAR},
            </if>
            <if test="appSecret != null and appSecret != ''">
                app_secret = #{appSecret,jdbcType=VARCHAR},
            </if>

            <if test="updateOwner != null and updateOwner != ''">
                update_owner = #{updateOwner,jdbcType=VARCHAR},
            </if>
            <if test="iosPaySwitch != null and iosPaySwitch != ''">
                ios_pay_switch = #{iosPaySwitch,jdbcType=VARCHAR},
            </if>
            update_time = current_timestamp
        </set>
        where appid = #{appid,jdbcType=INTEGER}
    </update>

    <select id="selectByCondition" parameterType="com.wbgame.pojo.redpack.TtCodeConfigDTO" resultMap="BaseResultMap">

        select
        <include refid="Base_Column_List"/>
        from tt_code_config
        <where>

            <if test="appidList != null and appidList.size > 0 ">
                and appid in
                <foreach collection="appidList" item="aid" open="(" separator="," close=")">

                    #{aid,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="ttid != null and ttid != ''">
                and ttid = #{ttid}
            </if>
            <if test="appSecret != null and appSecret != ''">
                and app_secret like #{appSecret} "%"
            </if>
            <if test="iosPaySwitch != null and iosPaySwitch != ''">
                and ios_pay_switch = #{iosPaySwitch}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>