<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.InGamePaymentConfigMapper">


    <select id="selectList" resultType="com.wbgame.pojo.operate.InGamePaymentConfigVo">
        select
            appid,
            channel,
            machid,
            wxappid,
            mch_serial,
            api_key,
            private_key,
            notify_url,
            app_secret,
            create_owner,
            update_owner,
            DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time,
            DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') update_time
        from xyx_pay_config
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="machid != null and machid != ''">
            and machid = #{machid}
        </if>
        <if test="wxappid != null and wxappid != ''">
            and wxappid = #{wxappid}
        </if>
        <if test="mch_serial != null and mch_serial != ''">
            and mch_serial = #{mch_serial}
        </if>
        <if test="api_key != null and api_key != ''">
            and api_key = #{api_key}
        </if>
        <choose>
            <when test="order != null and order != ''">
                order by ${order}
            </when>
            <otherwise>
                order by update_time desc
            </otherwise>
        </choose>
    </select>

    <insert id="inGamePaymentAdd" parameterType="com.wbgame.pojo.operate.InGamePaymentHandelVo">
        insert into xyx_pay_config  (
            appid,channel,machid,wxappid,mch_serial,api_key,private_key,notify_url,create_time,create_owner,app_secret
        ) value(
            #{appid},#{channel},#{machid},#{wxappid},#{mch_serial},#{api_key},#{private_key},#{notify_url},now(),#{create_owner},#{app_secret}
        )
    </insert>

    <update id="inGamePaymentEdit" parameterType="com.wbgame.pojo.operate.InGamePaymentHandelVo" >
        update xyx_pay_config set
        machid = #{machid},
        wxappid = #{wxappid},
        mch_serial = #{mch_serial},
        api_key = #{api_key},
        private_key = #{private_key},
        notify_url = #{notify_url},
        update_time = now(),
        update_owner = #{update_owner},
        app_secret = #{app_secret}
        where appid = #{appid} and channel = #{channel}
    </update>

    <delete id="inGamePaymentDel" parameterType="com.wbgame.pojo.operate.InGamePaymentHandelVo">
        delete from xyx_pay_config where appid = #{appid} and channel = #{channel}
    </delete>


</mapper>