<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.CpPayRollbackConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.mobile.CpPayRollbackConfigVO">
        <id column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_owner" property="createOwner" jdbcType="VARCHAR"/>
        <result column="update_owner" property="updateOwner" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        appid
        , url, create_time, update_time, create_owner, update_owner, status
    </sql>
    <select id="selectCpPayRollbackConfig" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.mobile.CpPayRollbackConfig">
        select

        <include refid="Base_Column_List"/>
        from cp_pay_rollback_config
        <where>

            <if test="status != null">
                and status = #{status}
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

        </where>

        order by create_time desc
    </select>

    <delete id="deleteCpPayRollbackConfig" parameterType="java.lang.String">
        delete
        from cp_pay_rollback_config
        where appid in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertCpPayRollbackConfig" parameterType="com.wbgame.pojo.mobile.CpPayRollbackConfig">
        insert into cp_pay_rollback_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appid != null and appid != null">
                appid,
            </if>
            <if test="url != null and url != null">
                url,
            </if>

            <if test="createOwner != null and createOwner != null">
                create_owner,
            </if>

            <if test="status != null">
                status,
            </if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appid != null and appid != null">
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="url != null and url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="createOwner != null and createOwner != null">
                #{createOwner,jdbcType=VARCHAR},
            </if>

            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>

            now(),
            now()
        </trim>
    </insert>

    <update id="updateCpPayRollbackConfig" parameterType="com.wbgame.pojo.mobile.CpPayRollbackConfig">
        update cp_pay_rollback_config
        <set>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>

            <if test="updateOwner != null">
                update_owner = #{updateOwner,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>

            update_time = now()
        </set>
        where appid = #{appid,jdbcType=VARCHAR}
    </update>

</mapper>