<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.CronMapper">

    <insert id="batchInsertRedPackWithdrawLog">
        insert into hb_user_withdraw_log(userid,lsn,appid,pid,imei,openid,amount,type,create_time,channel,install_time,red_ticket,total)values
        <foreach collection="list" separator="," item="it">
            (#{it.userid},#{it.lsn},#{it.appid},#{it.pid},#{it.imei},#{it.openid},#{it.amount},#{it.type},#{it.create_time},#{it.channel},#{it.install_time},#{it.red_ticket},#{it.total})
        </foreach>
    </insert>

    <delete id="deleteRedPackWithdrawLog">
        delete from hb_user_withdraw_log where date(create_time) = #{ds}
    </delete>

</mapper>