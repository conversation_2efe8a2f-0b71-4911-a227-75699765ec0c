<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.AliCyclePayConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.AliCyclePayConfigVO">
        <id column="appid" property="appid" jdbcType="INTEGER"/>
        <id column="type" property="type" jdbcType="INTEGER"/>
        <result column="money" property="money" jdbcType="DECIMAL"/>
        <result column="free_use" property="free_use" jdbcType="INTEGER"/>
        <result column="first_money" property="first_money" jdbcType="DECIMAL"/>
        <result column="first_day" property="first_day" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_owner" property="createOwner" jdbcType="VARCHAR"/>
        <result column="update_owner" property="updateOwner" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="day" property="day" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        appid
        , type, money,free_use,first_money,first_day, DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') create_time,
        DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') update_time,
        create_owner, update_owner, remark,
    day
    </sql>

    <delete id="deleteByKey" parameterType="com.wbgame.pojo.clean.AliCyclePayConfig">
        delete
        from ali_cycle_pay_config
        <where>

            <if test="list != null and list.size > 0">
                <foreach collection="list" item="data">
                    or (appid = #{data.appid,jdbcType=INTEGER}
                    and type = #{data.type,jdbcType=INTEGER})
                </foreach>
            </if>
        </where>
    </delete>


    <update id="updateByKey" parameterType="com.wbgame.pojo.clean.AliCyclePayConfig">
        update ali_cycle_pay_config
        <set>
            <if test="money != null">
                money = #{money,jdbcType=DECIMAL},
            </if>
            <if test="free_use != null">
                free_use = #{free_use,jdbcType=DECIMAL},
            </if>
            <if test="first_money != null">
                first_money = #{first_money,jdbcType=DECIMAL},
            </if>
            <if test="updateOwner != null and updateOwner != ''">
                update_owner = #{updateOwner,jdbcType=VARCHAR},
            </if>
            <if test="day != null">
                day = #{day,jdbcType=INTEGER},
            </if>
            remark = #{remark,jdbcType=VARCHAR},
            first_day = #{first_day,jdbcType=INTEGER},
            update_time = now()
        </set>
        where appid = #{appid,jdbcType=INTEGER}
        and type = #{type,jdbcType=INTEGER}
    </update>

    <insert id="insertAliCyclePayConfig" parameterType="com.wbgame.pojo.clean.AliCyclePayConfig">
        insert into ali_cycle_pay_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appid != null">
                appid,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="money != null">
                money,
            </if>
            <if test="free_use != null">
                free_use,
            </if>
            <if test="first_money != null">
                first_money,
            </if>
            <if test="first_day != null">
                first_day,
            </if>
            <if test="createOwner != null and createOwner != ''">
                create_owner,
            </if>

            <if test="remark != null and remark != ''">
                remark,
            </if>
            <if test="day != null">
                day,
            </if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appid != null">
                #{appid,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="money != null">
                #{money,jdbcType=DECIMAL},
            </if>
            <if test="free_use != null">
                #{free_use,jdbcType=INTEGER},
            </if>
            <if test="first_money != null">
                #{first_money,jdbcType=DECIMAL},
            </if>
            <if test="first_day != null">
                #{first_day,jdbcType=INTEGER},
            </if>
            <if test="createOwner != null and createOwner != ''">
                #{createOwner,jdbcType=VARCHAR},
            </if>

            <if test="remark != null and remark != ''">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="day != null">
                #{day,jdbcType=INTEGER},
            </if>
            now(),
            now()
        </trim>
    </insert>
    
    <select id="selectAliCyclePayConfig" parameterType="com.wbgame.pojo.clean.AliCyclePayConfigDTO" resultMap="BaseResultMap">
        
        select <include refid="Base_Column_List"></include> from ali_cycle_pay_config
        <where>

            <if test="type != null">
                and type = #{type}
            </if>
            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>
        order by create_time desc
    </select>
    
    
</mapper>