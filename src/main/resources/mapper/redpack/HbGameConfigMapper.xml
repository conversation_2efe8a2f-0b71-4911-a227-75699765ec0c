<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.redpack.HbGameConfigMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.GameOnlineConfig">
    <!--@mbg.generated-->
    <!--@Table game_online_config-->
    <id column="appid" jdbcType="INTEGER" property="appid" />
    <id column="channel" jdbcType="VARCHAR" property="channel" />
    <id column="version" jdbcType="VARCHAR" property="version" />
    <result column="pid" jdbcType="VARCHAR" property="pid" />
    <result column="json" jdbcType="LONGVARCHAR" property="json" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_own" jdbcType="VARCHAR" property="createOwn" />
    <result column="update_own" jdbcType="VARCHAR" property="updateOwn" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="effect_type" jdbcType="VARCHAR" property="effect_type" />
    <result column="buy_id" jdbcType="VARCHAR" property="buy_id" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    appid, channel, version, pid, json, create_time, update_time, create_own, update_own, 
    `status`,remark,city,effect_type,buy_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from game_online_config
    where appid = #{appid,jdbcType=INTEGER}
        and channel = #{channel,jdbcType=VARCHAR}
        and version = #{version,jdbcType=VARCHAR}
        and effect_type = #{effect_type,jdbcType=VARCHAR}
        and buy_id = #{buy_id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--@mbg.generated-->
    delete from game_online_config
    where appid = #{appid,jdbcType=INTEGER}
        and channel = #{channel,jdbcType=VARCHAR}
        and version = #{version,jdbcType=VARCHAR}
        and effect_type = #{effect_type,jdbcType=VARCHAR}
        and buy_id = #{buy_id,jdbcType=VARCHAR}
  </delete>
  <insert id="insertSelective" parameterType="com.wbgame.pojo.GameOnlineConfig">
    <!--@mbg.generated-->
    insert into game_online_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appid != null">
        appid,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="effect_type != null">
        effect_type,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="json != null">
        json,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createOwn != null">
        create_own,
      </if>
      <if test="updateOwn != null">
        update_own,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="remark != null">
        `remark`,
      </if>
      <if test="city != null">
        `city`,
      </if>
      <if test="buy_id != null">
        `buy_id`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appid != null">
        #{appid,jdbcType=INTEGER},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="effect_type != null">
        #{effect_type,jdbcType=VARCHAR},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=VARCHAR},
      </if>
      <if test="json != null">
        #{json,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwn != null">
        #{createOwn,jdbcType=VARCHAR},
      </if>
      <if test="updateOwn != null">
        #{updateOwn,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="buy_id != null">
        #{buy_id,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.GameOnlineConfig">
    <!--@mbg.generated-->
    update game_online_config
    <set>
      <if test="pid != null">
        pid = #{pid,jdbcType=VARCHAR},
      </if>
      <if test="json != null">
        json = #{json,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwn != null">
        create_own = #{createOwn,jdbcType=VARCHAR},
      </if>
      <if test="updateOwn != null">
        update_own = #{updateOwn,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
    </set>
    where appid = #{appid,jdbcType=INTEGER}
        and channel = #{channel,jdbcType=VARCHAR}
        and version = #{version,jdbcType=VARCHAR}
        and effect_type = #{effect_type,jdbcType=VARCHAR}
        and buy_id = #{buy_id,jdbcType=VARCHAR}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-05-13-->
  <select id="selectByAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createStr,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') updateStr
    from game_online_config
    <where>
      <if test="appid != null and appid != ''">
        and appid=#{appid,jdbcType=INTEGER}
      </if>
      <if test="channel != null and channel != ''">
        and channel=#{channel,jdbcType=VARCHAR}
      </if>
      <if test="version != null and version != '' ">
        and version=#{version,jdbcType=VARCHAR}
      </if>
      <if test="pid != null and pid != ''">
        and pid=#{pid,jdbcType=VARCHAR}
      </if>
      <if test="effect_type != null and effect_type != ''">
        and effect_type=#{effect_type,jdbcType=VARCHAR}
      </if>
      <if test="buy_id != null and buy_id != ''">
        and buy_id=#{buy_id,jdbcType=VARCHAR}
      </if>
      <if test="status != null">
        and status=#{status,jdbcType=INTEGER}
      </if>
    </where>
    order by update_time desc
  </select>

</mapper>