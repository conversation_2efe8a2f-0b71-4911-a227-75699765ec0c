<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.redpack.RedPacketConfigurationMapper" >
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.mobile.WxMachMonitorConfigVO" >
    <id column="machid" property="machid" jdbcType="VARCHAR" />
    <id column="wxappid" property="wxappid" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="create_owner" property="createOwner" jdbcType="VARCHAR" />
    <result column="update_owner" property="updateOwner" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    machid, wxappid, status, create_time, update_time, create_owner, update_owner
  </sql>


  <insert id="insertSelective" parameterType="com.wbgame.pojo.mobile.WxMachMonitorConfig">

    insert into wx_mach_monitor_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="machid != null" >
        machid,
      </if>
      <if test="wxappid != null" >
        wxappid,
      </if>
      <if test="status != null" >
        status,
      </if>

      <if test="createOwner != null" >
        create_owner,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="machid != null" >
        #{machid,jdbcType=VARCHAR},
      </if>
      <if test="wxappid != null" >
        #{wxappid,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>

      <if test="createOwner != null" >
        #{createOwner,jdbcType=VARCHAR},
      </if>

    </trim>
  </insert>

  <update id="updateSelective" parameterType="com.wbgame.pojo.mobile.WxMachMonitorConfig" >
    update wx_mach_monitor_config
    <set >
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>

      <if test="updateOwner != null" >
        update_owner = #{updateOwner,jdbcType=VARCHAR},
      </if>
      update_time = now()
    </set>
    where machid = #{machid,jdbcType=VARCHAR}
    and wxappid = #{wxappid,jdbcType=VARCHAR}
  </update>

  <delete id="deleteSelective" parameterType="com.wbgame.pojo.mobile.WxMachMonitorConfigDTO" >

        <if test="list != null and list.size > 0">
          delete from wx_mach_monitor_config
          <where>
            <foreach collection="list" item="config" separator="or">

              (machid = #{config.machid,jdbcType=VARCHAR}
              and wxappid = #{config.wxappid,jdbcType=VARCHAR})
            </foreach>
          </where>

        </if>

  </delete>

  <select id="selectWxMachMonitor" parameterType="com.wbgame.pojo.mobile.WxMachMonitorConfigQuery" resultMap="BaseResultMap">
    select machid, wxappid, status, create_time, update_time, create_owner, update_owner
    from wx_mach_monitor_config
    <where>
      <if test="machid != null and machid !=''">
        and machid like #{machid} "%"
      </if>
      <if test="wxappid != null and wxappid !=''">
        and wxappid like #{wxappid} "%"
      </if>

      <if test="status != null">
        and status = #{status}
      </if>
    </where>
    order by update_time desc
  </select>


  <insert id="insertHbWithdrawIpConfig" parameterType="com.wbgame.pojo.mobile.HbWithdrawIpConfig" >
    insert into hb_withdraw_ip_config
    <trim prefix="(" suffix=")" suffixOverrides="," >

      <if test="ip != null" >
        ip,
      </if>
      <if test="status != null" >
        status,
      </if>

      <if test="createOwner != null" >
        create_owner,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >

      <if test="ip != null" >
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>

      <if test="createOwner != null" >
        #{createOwner,jdbcType=VARCHAR},
      </if>

    </trim>
  </insert>

  <update id="updateHbWithdrawIpConfig" parameterType="com.wbgame.pojo.mobile.HbWithdrawIpConfig" >
    update hb_withdraw_ip_config
    <set >
      <if test="ip != null and ip != ''" >
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>

      <if test="updateOwner != null" >
        update_owner = #{updateOwner,jdbcType=VARCHAR},
      </if>
      update_time = now()
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <delete id="deleteHbWithdrawIpConfig" parameterType="java.lang.Integer" >
    delete from hb_withdraw_ip_config
    where id in
    <foreach collection="list" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <resultMap id="hbWithDrawMap" type="com.wbgame.pojo.mobile.HbWithdrawIpConfigVO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="ip" property="ip" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="create_owner" property="createOwner" jdbcType="VARCHAR" />
    <result column="update_owner" property="updateOwner" jdbcType="VARCHAR" />
  </resultMap>
  <select id="selectWithdrawConfig" parameterType="com.wbgame.pojo.mobile.HbWithdrawIpConfigQuery" resultMap="hbWithDrawMap">

        select * from hb_withdraw_ip_config
        <where>
          <if test="status != null ">
               and status = #{status}
          </if>
          <if test="ip != null and ip != ''">
               and ip like #{ip} "%"
          </if>
        </where>
        order by id desc
  </select>
</mapper>