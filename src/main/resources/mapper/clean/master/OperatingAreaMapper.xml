<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.OperatingAreaMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.toonstory.OperatingAreaVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="area_name" property="areaName" jdbcType="VARCHAR"/>
        <result column="desc_txt" property="descTxt" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
        <result column="modify_user" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="BIGINT"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        a.id,
        a.area_name,
        a.desc_txt,
        a.status,
        a.create_user,
        from_unixtime( a.create_time / 1000, '%Y-%m-%d %H:%i:%s' ) create_time,
        a.modify_user,
        from_unixtime( a.modify_time / 1000, '%Y-%m-%d %H:%i:%s' ) modify_time
    </sql>

    <select id="selectOperatingAreaMapper" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.clean.toonstory.OperatingAreaDTO">

        select

        <include refid="Base_Column_List"/>,
        group_concat(b.area) area
        from gj_b.toon_area a
        left join gj_b.toon_area_detail b on a.id = b.area_id
        <where>


            <if test="areaName != null and areaName != ''">
                and area_name like "%" #{areaName} "%"
            </if>
            <if test="status != null">
                and status = #{status}
            </if>

        </where>

        group by a.id,
                a.area_name,
                a.desc_txt,
                a.status,
                a.create_user,
                a.create_time,
                a.modify_user,
                a.modify_time
        order by a.id desc
    </select>

    <insert id="insertOperatingArea" parameterType="com.wbgame.pojo.clean.toonstory.OperatingArea"
            useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into gj_b.toon_area
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="appId != null and appId != ''">
                app_id,
            </if>
            <if test="areaName != null and areaName != ''">
                area_name,
            </if>
            <if test="descTxt != null and descTxt != ''">
                desc_txt,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createUser != null and createUser != ''">
                create_user,
            </if>

            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>


        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="appId != null and appId != ''">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="areaName != null and areaName != ''">
                #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="descTxt != null and descTxt != ''">
                #{descTxt,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>

            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
        </trim>
    </insert>

    <update id="updateOperatingArea" parameterType="com.wbgame.pojo.clean.toonstory.OperatingArea">
        update gj_b.toon_area
        <set>
            <if test="appId != null and appId != ''">
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="areaName != null and areaName != ''">
                area_name = #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="descTxt != null and descTxt != ''">
                desc_txt = #{descTxt,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>

            <if test="modifyUser != null and modifyUser != ''">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>


            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>

        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>