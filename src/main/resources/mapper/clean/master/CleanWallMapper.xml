<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.CleanWallMapper">

    <select id="selectWallTypeList" parameterType="com.wbgame.pojo.clean.img.ImgTypeVo" resultType="com.wbgame.pojo.clean.img.ImgTypeVo">
        select * from super_img_type
        where 1=1 AND dataType = 2
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectWallTypeByType" resultType="com.wbgame.pojo.clean.img.ImgTypeVo">
        select * from super_img_type where 1=1 and  `type` = #{type} AND dataType = 2
    </select>

    <select id="selectWallTypeByTypeSort" resultType="com.wbgame.pojo.clean.img.ImgTypeVo">
        select * from super_img_type where 1=1 and  `typeSort` = #{typeSort} AND dataType = 2
    </select>

    <select id="selectWallTypeById" resultType="com.wbgame.pojo.clean.img.ImgTypeVo">
        select * from super_img_type where 1=1 and  `id` = #{id} AND dataType = 2
    </select>


    <delete id="deleteWallType" parameterType="com.wbgame.pojo.clean.img.ImgTypeVo">
        delete from super_img_type where id = #{id} AND dataType = 2
    </delete>
    <insert id="insertWallType" parameterType="com.wbgame.pojo.clean.img.ImgTypeVo">
        insert into super_img_type (`type`, typeSort,dataType,status,modifyUser,createTime)
        values  (#{type,jdbcType=VARCHAR}, #{typeSort,jdbcType=VARCHAR},2,#{status,jdbcType=VARCHAR},
                 #{modifyUser,jdbcType=VARCHAR},NOW())
    </insert>
    <update id="updateWallType" parameterType="com.wbgame.pojo.clean.img.ImgTypeVo">
        update super_img_type
        set `type` = #{type,jdbcType=VARCHAR},
            typeSort = #{typeSort,jdbcType=VARCHAR},
            modifyTime = NOW(),
            modifyUser = #{modifyUser,jdbcType=VARCHAR},
            status = #{status,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR} AND dataType = 2
    </update>

    <select id="selectWallConfigList" resultType="com.wbgame.pojo.clean.img.ImgConfigVo">
        select
        *
        from super_img_config
        where 1=1 AND dataType = 2
        <if test="appid != null and appid != ''">
            and appid = #{appid,jdbcType=VARCHAR}
        </if>
        <if test="prjid != null and prjid != ''">
            and prjid = #{prjid,jdbcType=VARCHAR}
        </if>
        <if test="cha != null and cha != ''">
            and cha = #{cha,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
        <if test="typeSort != null and typeSort != ''">
            and typeSort = #{typeSort,jdbcType=VARCHAR}
        </if>
        order by typeSort,createTime desc
    </select>
    <delete id="deleteWallConfig" parameterType="com.wbgame.pojo.clean.img.ImgConfigVo">
        delete from super_img_config where id in
        <foreach  item="item" collection="ids" index="index"  open="(" separator="," close=")">
            #{item}
        </foreach>
        AND dataType = 2
    </delete>

    <insert id="insertWallConfig" parameterType="com.wbgame.pojo.clean.img.ImgConfigVo">
    insert into super_img_config (`appid`,
	`cha`,
	`prjid`,
	`type`,
	`typeSort`,
	`name`,
	`imgSort`,
	`url`,
	`preUrl`,
	`imgUseCount`,
	`imgLikeCount`,
	`status`,
	`dataType`,
	`fileType`,
	`createTime`,
	`modifyUser`
	)
    values (#{appid},#{cha},#{prjid},#{type},#{typeSort},#{name},#{imgSort},#{url},#{preUrl},#{imgUseCount},#{imgLikeCount},#{status},2,#{fileType},NOW(),#{modifyUser})
    </insert>


    <update id="updateWallConfig" parameterType="com.wbgame.pojo.clean.img.ImgConfigVo">
    update super_img_config
    set  `appid` = #{appid},
		 `cha` = #{cha},
		 `prjid` = #{prjid},
		 `type` = #{type},
		 `typeSort` = #{typeSort},
		 `name` = #{name},
		 `imgSort` = #{imgSort},
		 `url` = #{url},
		 `preUrl` = #{preUrl},
		 `fileType`= #{fileType},
		 `imgUseCount` = #{imgUseCount},
		 `imgLikeCount` = #{imgLikeCount},
		 `status` = #{status},
		 `modifyUser` = #{modifyUser},
		 `modifyTime` = NOW()
    where id = #{id} AND dataType = 2
    </update>


    <update id="updateWallConfigStatus" parameterType="com.wbgame.pojo.clean.img.ImgConfigVo">
        update super_img_config set status = #{status} where id=#{id} AND dataType = 2
    </update>


    <update id="updateWallConfigStatusBatch" parameterType="com.wbgame.pojo.clean.img.ImgConfigVo">
        update super_img_config set status = #{status} where id in
        <foreach  item="item" collection="ids" index="index"  open="(" separator="," close=")">
            #{item}
        </foreach>
        AND dataType = 2
    </update>

    <update id="updateWallConfigTypeSortBatch" parameterType="com.wbgame.pojo.clean.img.ImgConfigVo">
        update super_img_config set `type` = #{type} ,typeSort =#{typeSort} where id in
        <foreach  item="item" collection="ids" index="index"  open="(" separator="," close=")">
            #{item}
        </foreach>
        AND dataType = 2
    </update>

    <insert id="copyWallConfigBatch" parameterType="java.util.List">
        insert into super_img_config (appid,cha,prjid,`type`,typeSort,`name`,imgSort,url,preUrl,imgUseCount,imgLikeCount,status,dataType,fileType,createTime,modifyUser)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
                #{item.appid},#{item.cha},#{item.prjid},#{item.type},#{item.typeSort},
                #{item.name},#{item.imgSort},#{item.url},#{item.preUrl},#{item.imgUseCount},#{item.imgLikeCount},
                #{item.status},2,#{item.fileType},NOW(),#{item.modifyUser}
            )
        </foreach>
    </insert>

    <delete id="deleteWallConfigByTypeSort" parameterType="java.lang.String">
        delete  from super_img_config where typeSort = #{typeSort} AND dataType = 2
    </delete>

    <update id="updateWallConfigByTypeChange" parameterType="com.wbgame.pojo.clean.img.ImgTypeVo">
        update super_img_config set `type` =#{type} ,typeSort =#{typeSort} where typeSort = #{oldTypeSort} AND dataType = 2
    </update>

</mapper>