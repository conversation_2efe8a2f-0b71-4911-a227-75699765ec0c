<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.FacePlusAiModelMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.aipaint.FacePlusAiModelVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="model_name" property="modelName" jdbcType="VARCHAR"/>
        <result column="model_keyword" property="modelKeyword" jdbcType="VARCHAR"/>
        <result column="model_strength" property="modelStrength" jdbcType="DECIMAL"/>
        <result column="model_seed" property="modelSeed" jdbcType="INTEGER"/>
        <result column="make_template_count" property="makeTemplateCount" jdbcType="INTEGER"/>
        <result column="tpl_convert_vip_ount" property="tplConvertVipOunt" jdbcType="INTEGER"/>
        <result column="region" property="region" jdbcType="TINYINT"/>
        <result column="cover_url" property="coverUrl" jdbcType="VARCHAR"/>
        <result column="source_url" property="sourceUrl" jdbcType="VARCHAR"/>
        <result column="temp_type" property="tempType" jdbcType="TINYINT"/>
        <result column="task_time" property="taskTime" jdbcType="BIGINT"/>
        <result column="state" property="state" jdbcType="TINYINT"/>
        <result column="shelve_time" property="shelveTime" jdbcType="BIGINT"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
        <result column="modify_user" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        a.id,
        a.model_name,
        a.model_keyword,
        a.model_strength,
        a.model_seed,
        a.make_template_count,
        a.tpl_convert_vip_ount,
        a.region,
        a.cover_url,
        a.source_url,
        a.temp_type,
        a.state,
        if(a.shelve_time = 0, "", from_unixtime( a.shelve_time / 1000, '%Y-%m-%d' )) shelve_time,
        if(a.task_time = 0, "", from_unixtime( a.task_time / 1000, '%Y-%m-%d %H:%i:%s' )) task_time,
        a.sort,
        a.create_user,
        from_unixtime( a.create_time / 1000, '%Y-%m-%d %H:%i:%s' ) create_time,
        a.modify_user,
        from_unixtime( a.modify_time / 1000, '%Y-%m-%d %H:%i:%s' ) modify_time,
        concat(ifnull(truncate(tpl_convert_vip_ount / make_template_count * 100, 2), 0), "%")  conversionRate
    </sql>

    <select id="selectModel" parameterType="com.wbgame.pojo.clean.aipaint.FacePlusAiModelDTO" resultMap="BaseResultMap">
        select
        distinct
        <include refid="Base_Column_List"/>
        from gj_b.face_plus_ai_model a left join gj_b.face_plus_ai_model_region b
        on a.id = b.model_id
        <where>

            <if test="id != null">
                and a.id = #{id}
            </if>
            <if test="modelName != null and modelName != ''">
                and a.model_name like "%" #{modelName} "%"
            </if>
            <if test="areaId != null">
                and b.area_id = #{areaId}
            </if>

            <if test="state != null">

                and a.state = #{state}
            </if>
            <if test="tempType != null">
                and a.temp_type = #{tempType}
            </if>

        </where>

        order by a.id desc
    </select>
    <delete id="deleteModelByIdList" parameterType="java.lang.Long">
        delete
        from gj_b.face_plus_ai_model
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertModel" parameterType="com.wbgame.pojo.clean.aipaint.FacePlusAiModel"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into gj_b.face_plus_ai_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="modelName != null">
                model_name,
            </if>
            <if test="modelKeyword != null">
                model_keyword,
            </if>
            <if test="modelStrength != null">
                model_strength,
            </if>
            <if test="modelSeed != null">
                model_seed,
            </if>
            <if test="makeTemplateCount != null">
                make_template_count,
            </if>
            <if test="tplConvertVipOunt != null">
                tpl_convert_vip_ount,
            </if>
            <if test="region != null">
                region,
            </if>
            <if test="coverUrl != null">
                cover_url,
            </if>
            <if test="sourceUrl != null">
                source_url,
            </if>
            <if test="tempType != null">
                temp_type,
            </if>
            <if test="taskTime != null">
                task_time,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="shelveTime != null">
                shelve_time,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyUser != null">
                modify_user,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="modelName != null">
                #{modelName,jdbcType=VARCHAR},
            </if>
            <if test="modelKeyword != null">
                #{modelKeyword,jdbcType=VARCHAR},
            </if>
            <if test="modelStrength != null">
                #{modelStrength,jdbcType=DECIMAL},
            </if>
            <if test="modelSeed != null">
                #{modelSeed,jdbcType=INTEGER},
            </if>
            <if test="makeTemplateCount != null">
                #{makeTemplateCount,jdbcType=INTEGER},
            </if>
            <if test="tplConvertVipOunt != null">
                #{tplConvertVipOunt,jdbcType=INTEGER},
            </if>
            <if test="region != null">
                #{region,jdbcType=TINYINT},
            </if>
            <if test="coverUrl != null">
                #{coverUrl,jdbcType=VARCHAR},
            </if>
            <if test="sourceUrl != null">
                #{sourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="tempType != null">
                #{tempType,jdbcType=TINYINT},
            </if>
            <if test="taskTime != null">
                #{taskTime,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="shelveTime != null">
                #{shelveTime,jdbcType=BIGINT},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="modifyUser != null">
                #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateModel" parameterType="com.wbgame.pojo.clean.aipaint.FacePlusAiModel">
        update gj_b.face_plus_ai_model
        <set>
            <if test="modelName != null">
                model_name = #{modelName,jdbcType=VARCHAR},
            </if>
            <if test="modelKeyword != null">
                model_keyword = #{modelKeyword,jdbcType=VARCHAR},
            </if>
            <if test="modelStrength != null">
                model_strength = #{modelStrength,jdbcType=DECIMAL},
            </if>
            <if test="modelSeed != null">
                model_seed = #{modelSeed,jdbcType=INTEGER},
            </if>
            <if test="makeTemplateCount != null">
                make_template_count = #{makeTemplateCount,jdbcType=INTEGER},
            </if>
            <if test="tplConvertVipOunt != null">
                tpl_convert_vip_ount = #{tplConvertVipOunt,jdbcType=INTEGER},
            </if>
            <if test="region != null">
                region = #{region,jdbcType=TINYINT},
            </if>
            <if test="coverUrl != null">
                cover_url = #{coverUrl,jdbcType=VARCHAR},
            </if>
            <if test="sourceUrl != null">
                source_url = #{sourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="tempType != null">
                temp_type = #{tempType,jdbcType=TINYINT},
            </if>
            <if test="taskTime != null">
                task_time = #{taskTime,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="shelveTime != null">
                shelve_time = #{shelveTime,jdbcType=BIGINT},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="modifyUser != null">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <update id="updateStatus" parameterType="com.wbgame.pojo.clean.aipaint.FacePlusAiModel">
        update gj_b.face_plus_ai_model
        <set>

            <if test="tempType != null">
                temp_type = #{tempType,jdbcType=TINYINT},
            </if>

            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>

            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>

            <if test="modifyUser != null and modifyUser != ''">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateModelTempType" parameterType="com.wbgame.pojo.clean.aipaint.FacePlusAiModel">
        update gj_b.face_plus_ai_model
        <set>

            <if test="tempType != null">
                temp_type = #{tempType,jdbcType=TINYINT},
            </if>

            <if test="modifyUser != null and modifyUser != ''">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>