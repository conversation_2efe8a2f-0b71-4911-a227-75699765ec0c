<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.CleanYdMapper">

	
  <!-- 渠道新闻页参数配置 查询 -->
  <select id="selectDnChannelConfig" resultType="com.wbgame.pojo.clean.DnCleanChannelConfig" parameterType="com.wbgame.pojo.clean.DnCleanChannelConfig" >
    select * from super_clear_url_config_cname
    where 1=1 
     <if test="cname != null and cname != ''">
		and  cname like concat('%',#{cname},'%')
	</if>
  </select>
  <!-- 渠道新闻页参数配置 新增 -->
  <insert id="insertDnCleanChannelConfig" parameterType="com.wbgame.pojo.clean.DnCleanChannelConfig" >
    insert into super_clear_url_config_cname (cname, urlType, value1, value2, statusType, createTime,updateUser,updateTime,appid
      )
    values (#{cname}, #{urlType}, #{value1}, #{value2}, #{statusType}, NOW(), #{updateUser},NOW(),#{appid}
      )
  </insert>
  <!-- 渠道新闻页参数配置 编辑 -->
  <update id="updateDnCleanChannelConfig" parameterType="com.wbgame.pojo.clean.DnCleanChannelConfig" >
    update super_clear_url_config_cname
    set 
      value1 = #{value1},
      value2 = #{value2},
      statusType = #{statusType},
      updateTime = NOW(),
      updateUser = #{updateUser}
    where cname = #{cname} and urlType = #{urlType} and appid = #{appid}
  </update>
  
  <!-- 渠道新闻页参数配置 打开关闭 -->
  <update id="updateDnChannelConfigOnOff" parameterType="com.wbgame.pojo.clean.DnCleanChannelConfig" >
    update super_clear_url_config_cname
    set 
      statusType = #{statusType},
      updateTime = NOW(),
      updateUser = #{updateUser}
    where cname = #{cname} and urlType = #{urlType} and appid = #{appid}
  </update>
   <!-- 渠道新闻页参数配置 删除 -->
  <delete id="deleteDnCleanChannelConfig" parameterType="com.wbgame.pojo.clean.DnCleanChannelConfig" >
    delete from super_clear_url_config_cname
    where cname = #{cname} and urlType = #{urlType} and appid = #{appid}
  </delete>
  
  <select id="selectSuperGameConfig" resultType="com.wbgame.pojo.clean.SuperGameConfig" parameterType="com.wbgame.pojo.clean.SuperGameConfig" >
    select 
   pid, unlockPop, unlockPopPercent, unlockPop1, unlockPopPercent1
    from super_game_config
    where 1=1 
     <if test="pid != null and pid != ''">
		 and  pid = #{pid,jdbcType=VARCHAR}
	</if>
  </select>
  
  <insert id="insertSuperGameConfig" parameterType="com.wbgame.pojo.clean.SuperGameConfig" >
    insert into super_game_config (pid, unlockPop, unlockPopPercent, 
      unlockPop1, unlockPopPercent1)
    values (#{pid,jdbcType=VARCHAR}, #{unlockpop,jdbcType=VARCHAR}, #{unlockpoppercent,jdbcType=VARCHAR}, 
      #{unlockpop1,jdbcType=VARCHAR}, #{unlockpoppercent1,jdbcType=VARCHAR})
  </insert>
  
  <delete id="deleteSuperGameConfig" parameterType="java.lang.String" >
    delete from super_game_config
    where pid = #{pid,jdbcType=VARCHAR}
  </delete>
  
    <update id="updateSuperGameConfig" parameterType="com.wbgame.pojo.clean.SuperGameConfig" >
    update super_game_config
    set unlockPop = #{unlockpop,jdbcType=VARCHAR},
      unlockPopPercent = #{unlockpoppercent,jdbcType=VARCHAR},
      unlockPop1 = #{unlockpop1,jdbcType=VARCHAR},
      unlockPopPercent1 = #{unlockpoppercent1,jdbcType=VARCHAR}
    where pid = #{pid,jdbcType=VARCHAR}
  </update>
  
  <!-- PID新闻页参数配置 查询 -->
  <select id="selectDnChannelPid" resultType="com.wbgame.pojo.clean.DnCleanChannelConfig" parameterType="com.wbgame.pojo.clean.DnCleanChannelConfig" >
    select * from super_clear_url_config_pid
    where 1=1 
     <if test="pid != null and pid != ''">
		and  pid like concat('%',#{pid},'%')
	</if>
  </select>
  <!-- PID新闻页参数配置 新增 -->
  <insert id="insertDnCleanChannelPid" parameterType="com.wbgame.pojo.clean.DnCleanChannelConfig" >
    insert into super_clear_url_config_pid (pid, urlType, value1, value2, statusType, createTime,updateUser,updateTime
      )
    values (#{pid}, #{urlType}, #{value1}, #{value2}, #{statusType}, NOW(), #{updateUser},NOW()
      )
  </insert>
  <!-- PID新闻页参数配置 编辑 -->
  <update id="updateDnCleanChannelPid" parameterType="com.wbgame.pojo.clean.DnCleanChannelConfig" >
    update super_clear_url_config_pid
    set 
      value1 = #{value1},
      value2 = #{value2},
      statusType = #{statusType},
      updateTime = NOW(),
      updateUser = #{updateUser}
    where pid = #{pid} and urlType = #{urlType}
  </update>
  
  <!-- PID新闻页参数配置 打开关闭 -->
  <update id="updateDnChannelPidOnOff" parameterType="com.wbgame.pojo.clean.DnCleanChannelConfig" >
    update super_clear_url_config_pid
    set 
      statusType = #{statusType},
      updateTime = NOW(),
      updateUser = #{updateUser}
    where pid = #{pid} and urlType = #{urlType}
  </update>
   <!-- 渠道新闻页参数配置 删除 -->
  <delete id="deleteDnCleanChannelPid" parameterType="com.wbgame.pojo.clean.DnCleanChannelConfig" >
    delete from super_clear_url_config_pid
    where pid = #{pid} and urlType = #{urlType}
  </delete>
  
  	<select id="selectAdTableConfig" parameterType="Map" resultType="com.wbgame.pojo.clean.NpActiveFree">
		select * from np_param_active_free where 1=1
		<if test="prjmid != null and prjmid != ''">
			and PRJMID = #{prjmid}
		</if>
		<if test="testids != null and testids != ''">
			and (prjmid in (${testids}) or prjmid like '3333%')
		</if>
	</select>

	<insert id="insertAdTableConfig" parameterType="com.wbgame.pojo.clean.NpActiveFree">
		insert into np_param_active_free(prjmid,free_num,plaquelimitinterval,productid,carrier_type,seq_id,remark_info,sfcityids,
		mmid,clicklimitnum,update_time,splashlimitinterval,bannerupdateinterval,adopenlimitinterval,reloadinterval)
		values(#{prjmid},#{free_num},#{plaquelimitinterval},#{productid},#{carrier_type},#{seq_id},
		#{remark_info},#{sfcityids},#{mmid},#{clicklimitnum},#{update_time},#{splashlimitinterval},
		#{bannerupdateinterval},#{adopenlimitinterval},#{reloadinterval})
	</insert>

	<update id="updateAdTableConfig" parameterType="com.wbgame.pojo.clean.NpActiveFree">
		update np_param_active_free set free_num = #{free_num},plaquelimitinterval = #{plaquelimitinterval},
		productid = #{productid},carrier_type = #{carrier_type},seq_id = #{seq_id},remark_info = #{remark_info},
		sfcityids = #{sfcityids},mmid = #{mmid},clicklimitnum = #{clicklimitnum},update_time = #{update_time},
		splashlimitinterval = #{splashlimitinterval},bannerupdateinterval = #{bannerupdateinterval},
		adopenlimitinterval=#{adopenlimitinterval},reloadinterval=#{reloadinterval}
		where prjmid = #{prjmid}
	</update>
  
  	<update id="createNewTable" parameterType="String" >  
	 
       CREATE TABLE ${tableName} (
	  `appid` varchar(255) DEFAULT NULL,
	  `prjid` varchar(255) DEFAULT NULL,
	  `sessionId` varchar(255) DEFAULT NULL,
	  `deviceId` varchar(255) DEFAULT NULL,
	  `versionName` varchar(255) DEFAULT NULL,
	  `channel` varchar(255) DEFAULT NULL,
	  `timestamp` varchar(255) DEFAULT NULL,
	  `eventId` varchar(255) DEFAULT NULL,
	  `param1` varchar(255) DEFAULT NULL,
	  `param2` varchar(255) DEFAULT NULL
	 
	) ENGINE=InnoDB DEFAULT CHARSET=utf8;
    </update>
    
    <select id="selectNewTable"  resultType="String">  
       select * from tablenames
    </select> 
    
    <update id="createNewView" parameterType="String" >
  	DROP view IF EXISTS  ${vName};  
	create view ${vName}
	as
			SELECT
			`aa`.`appid` AS `appid`,
			`aa`.`prjid` AS `prjid`,
			`aa`.`sessionId` AS `sessionId`,
			`aa`.`deviceId` AS `deviceId`,
			`aa`.`versionName` AS `versionName`,
			from_unixtime(`aa`.`timestamp`) AS `FROM_UNIXTIME(``timestamp``)`,
			`aa`.`eventId` AS `eventId`,
			`aa`.`param1` AS `param1`,
			`aa`.`param2` AS `param2`,
			`bb`.`mac` AS `mac`,
			`bb`.`model` AS `model`,
			`bb`.`appName` AS `appName`,
			`aa`.`channel` AS `channel`,
			`bb`.`netState` AS `netState`,
			`bb`.`os` AS `os`,
			`bb`.`osVersion` AS `osVersion`,
			`bb`.`uiVersion` AS `uiVersion`,
			`bb`.`sdkVersion` AS `sdkVersion`,
			`bb`.`resolution` AS `resolution`,
			`bb`.`country` AS `country`,
			`bb`.`pkg` AS `pkg`,
			`bb`.`createtime` AS `createtime`,
			`bb`.`ip` AS `ip`,
			`bb`.`lsn` AS `lsn`,
			`bb`.`oaid` AS `oaid`,
			`bb`.`deviceType` AS `deviceType`,
			`bb`.`brand` AS `brand`
		FROM
			(
				`${tableName}` `aa`
				LEFT JOIN `push_start_user` `bb` ON (
					(
						(
							`aa`.`sessionId` = `bb`.`sessionId`
						)
						AND (
							`aa`.`deviceId` = `bb`.`deviceId`
						)
					)
				)
			)
    </update>
    
    <insert id="insertSuperConfigLock" parameterType="com.wbgame.pojo.clean.SuperConfigLock">
    insert into super_config_lock (appid, cha, prjid, 
      lockStyle, unLockInterval, unLockStype, 
      city, creatTime, modifyTime, 
      modifyUser, cityStatus,popType,
      chargeSplash,firstLock,firstLockNew,firstUnLock,totalNum ,status,home_ldy,home_Interval,home_auto,ldy_auto,home_num,homeStype,home_popType
      )
    values (#{appid,jdbcType=VARCHAR}, #{cha,jdbcType=VARCHAR}, #{prjid,jdbcType=VARCHAR}, 
      #{lockStyle,jdbcType=VARCHAR}, #{unLockInterval,jdbcType=VARCHAR}, #{unLockStype,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, NOW(),  NOW(), 
      #{modifyUser,jdbcType=VARCHAR}, #{cityStatus,jdbcType=VARCHAR}, #{popType,jdbcType=VARCHAR},
      #{chargeSplash,jdbcType=VARCHAR},#{firstLock,jdbcType=VARCHAR},#{firstLockNew,jdbcType=VARCHAR},#{firstUnLock,jdbcType=VARCHAR},#{totalNum,jdbcType=VARCHAR},#{status,jdbcType=VARCHAR}
      ,#{home_ldy,jdbcType=VARCHAR},#{home_Interval,jdbcType=VARCHAR},#{home_auto,jdbcType=VARCHAR},#{ldy_auto,jdbcType=VARCHAR},#{home_num,jdbcType=VARCHAR},#{homeStype,jdbcType=VARCHAR},#{home_popType,jdbcType=VARCHAR}
      )
  </insert>
    <delete id="deleteSuperConfigLock" parameterType="com.wbgame.pojo.clean.SuperConfigLock">
    delete from super_config_lock
    where id = #{id}
  </delete>
  <select id="selectSuperConfigLock" resultType="com.wbgame.pojo.clean.SuperConfigLock">
    select 
  	 *
    from super_config_lock
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
  </select>
  <update id="updateSuperConfigLock" parameterType="com.wbgame.pojo.clean.SuperConfigLock">
    update super_config_lock
    set lockStyle = #{lockStyle,jdbcType=VARCHAR},
      unLockInterval = #{unLockInterval,jdbcType=VARCHAR},
      unLockStype = #{unLockStype,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      modifyTime = NOW(),
      modifyUser = #{modifyUser,jdbcType=VARCHAR},
      cityStatus = #{cityStatus,jdbcType=VARCHAR},
      popType = #{popType,jdbcType=VARCHAR},
      firstLock = #{firstLock,jdbcType=VARCHAR},
      firstLockNew = #{firstLockNew,jdbcType=VARCHAR},
      firstUnLock = #{firstUnLock,jdbcType=VARCHAR},
      totalNum = #{totalNum,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      home_ldy = #{home_ldy,jdbcType=VARCHAR},
      home_Interval = #{home_Interval,jdbcType=VARCHAR},
      ldy_auto = #{ldy_auto,jdbcType=VARCHAR},
      home_auto = #{home_auto,jdbcType=VARCHAR},
      home_num = #{home_num,jdbcType=VARCHAR},
      homeStype = #{homeStype,jdbcType=VARCHAR},
      home_popType = #{home_popType,jdbcType=VARCHAR},
      chargeSplash = #{chargeSplash,jdbcType=VARCHAR}
    where id = #{id}
  </update>
  
   <update id="updateSuperConfigLockStatus" parameterType="com.wbgame.pojo.clean.SuperConfigLock">
    update super_config_lock
    set 
     `modifyUser` = #{modifyUser},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id}
  </update>
  
  <update id="updateSuperConfigDeeplinkStatus" parameterType="com.wbgame.pojo.clean.SuperConfigDeeplink">
    update super_config_deeplink
    set 
     `modifyUser` = #{modifyUser},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id}
  </update>
  
  <update id="updateSuperConfigNewsStatus" parameterType="com.wbgame.pojo.clean.SuperConfigNews">
    update super_config_news
    set 
     `modifyUser` = #{modifyUser},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id}
  </update>
  
  <update id="updateSuperConfigTabStatus" parameterType="com.wbgame.pojo.clean.SuperConfigTab">
    update super_config_tab
    set 
     `modifyUser` = #{modifyUser},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id}
  </update>
  
  <update id="updateSuperConfigParamsStatus" parameterType="com.wbgame.pojo.clean.SuperConfigParams">
    update super_config_params
    set 
     `modifyUser` = #{modifyUser},
     `modifyTime` = now(),
      status = #{status,jdbcType=VARCHAR}
    where id = #{id}
  </update>
  
  <update id="updateSuperConfigPublicStatus" parameterType="com.wbgame.pojo.clean.SuperConfigPublic">
    update super_config_public
    set 
     `modifyUser` = #{modifyUser},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id}
  </update>
  
  <update id="updateSuperConfigWinmsgStatus" parameterType="com.wbgame.pojo.clean.SuperConfigWinmsg">
    update super_config_winmsg
    set 
     `modifyUser` = #{modifyUser},
      status = #{status,jdbcType=VARCHAR}
    where msgId = #{msgId}
  </update>
  
  <update id="updatebBatchLock" parameterType="com.wbgame.pojo.clean.SuperConfigLock">
    update super_config_lock
    set lockStyle = #{lockStyle,jdbcType=VARCHAR},
      unLockInterval = #{unLockInterval,jdbcType=VARCHAR},
      unLockStype = #{unLockStype,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      modifyTime = NOW(),
      modifyUser = #{modifyUser,jdbcType=VARCHAR},
      popType = #{popType,jdbcType=VARCHAR},
      cityStatus = #{cityStatus,jdbcType=VARCHAR},
      firstLock = #{firstLock,jdbcType=VARCHAR},
      firstLockNew = #{firstLockNew,jdbcType=VARCHAR},
      firstUnLock = #{firstUnLock,jdbcType=VARCHAR},
      totalNum = #{totalNum,jdbcType=VARCHAR},
      home_ldy = #{home_ldy,jdbcType=VARCHAR},
      home_Interval = #{home_Interval,jdbcType=VARCHAR},
      ldy_auto = #{ldy_auto,jdbcType=VARCHAR},
      home_auto = #{home_auto,jdbcType=VARCHAR},
      home_num = #{home_num,jdbcType=VARCHAR},
      homeStype = #{homeStype,jdbcType=VARCHAR},
      home_popType = #{home_popType,jdbcType=VARCHAR},
      chargeSplash = #{chargeSplash,jdbcType=VARCHAR}
    where id in (${ids}) 
  </update>

  <select id="selectSuperConfigTab" resultType="com.wbgame.pojo.clean.SuperConfigTab">
    select 
   		*
    from super_config_tab
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
		<if test="buy_act != null and buy_act != ''">
		 and  buy_act = #{buy_act,jdbcType=VARCHAR}
	</if>
	<if test="buy_id != null and buy_id != ''">
		 and  buy_id = #{buy_id,jdbcType=VARCHAR}
	</if>
	order by modifyTime  desc
  </select>
  <delete id="deleteSuperConfigTab" parameterType="com.wbgame.pojo.clean.SuperConfigTab">
    delete from super_config_tab
    where id = #{id}
  </delete>
   <insert id="insertSuperConfigTab" parameterType="com.wbgame.pojo.clean.SuperConfigTab">
    insert into super_config_tab (appid, cha, prjid, buy_act,buy_id,
      tab1, tab2, tab3, tab4, 
      creatTime, modifyTime, modifyUser, 
      sdkAppid, sdkCode,sdkType,sdkPartner,promoteld,storyStar,storyUse,storyVideo,
      interstitialCodeId,bannerCodeId,csjAppid
      
      )
    values (#{appid,jdbcType=VARCHAR}, #{cha,jdbcType=VARCHAR}, #{prjid,jdbcType=VARCHAR}, #{buy_act,jdbcType=VARCHAR}, #{buy_id,jdbcType=VARCHAR}, 
      #{tab1,jdbcType=VARCHAR}, #{tab2,jdbcType=VARCHAR}, #{tab3,jdbcType=VARCHAR}, #{tab4,jdbcType=VARCHAR}, 
      now(), now(), #{modifyUser,jdbcType=VARCHAR}, 
       #{sdkAppid,jdbcType=VARCHAR}, #{sdkCode,jdbcType=VARCHAR},#{sdkType,jdbcType=VARCHAR}, #{sdkPartner,jdbcType=VARCHAR}, #{promoteld,jdbcType=VARCHAR}
       , #{storyStar,jdbcType=VARCHAR}, #{storyUse,jdbcType=VARCHAR}, #{storyVideo,jdbcType=VARCHAR},
        #{interstitialCodeId,jdbcType=VARCHAR} , #{bannerCodeId,jdbcType=VARCHAR} , #{csjAppid,jdbcType=VARCHAR}
      )
  </insert>
   <update id="updateSuperConfigTab" parameterType="com.wbgame.pojo.clean.SuperConfigTab">
    update super_config_tab
    set tab1 = #{tab1,jdbcType=VARCHAR},
      tab2 = #{tab2,jdbcType=VARCHAR},
      tab3 = #{tab3,jdbcType=VARCHAR},
      tab4 = #{tab4,jdbcType=VARCHAR},
      modifyTime = now(),
      modifyUser = #{modifyUser,jdbcType=VARCHAR},
      sdkAppid = #{sdkAppid,jdbcType=VARCHAR},
      sdkCode = #{sdkCode,jdbcType=VARCHAR},
      sdkType = #{sdkType,jdbcType=VARCHAR},
      promoteld = #{promoteld,jdbcType=VARCHAR},
      storyStar = #{storyStar,jdbcType=VARCHAR},
      storyUse = #{storyUse,jdbcType=VARCHAR},
      storyVideo = #{storyVideo,jdbcType=VARCHAR},
      interstitialCodeId = #{interstitialCodeId,jdbcType=VARCHAR},
      bannerCodeId = #{bannerCodeId,jdbcType=VARCHAR},
      csjAppid = #{csjAppid,jdbcType=VARCHAR},
      sdkPartner = #{sdkPartner,jdbcType=VARCHAR}
    where id = #{id}
  </update>
  
  <update id="updatebBatchTab" parameterType="com.wbgame.pojo.clean.SuperConfigTab">
    update super_config_tab
     <set >
      <if test="tab1 != null and tab1 != ''" >
        tab1 = #{tab1,jdbcType=VARCHAR},
      </if>
     <if test="tab2 != null and tab2 != ''" >
        tab2 = #{tab2,jdbcType=VARCHAR},
      </if>
       <if test="tab3 != null and tab3 != ''" >
        tab3 = #{tab3,jdbcType=VARCHAR},
      </if>
       <if test="tab4 != null and tab4 != ''" >
        tab4 = #{tab4,jdbcType=VARCHAR},
      </if>
       <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
       <if test="sdkAppid != null and sdkAppid != ''" >
        sdkAppid = #{sdkAppid,jdbcType=VARCHAR},
      </if>
       <if test="sdkCode != null and sdkCode != ''" >
        sdkCode = #{sdkCode,jdbcType=VARCHAR},
      </if>
       <if test="storyStar != null and storyStar != ''" >
        storyStar = #{storyStar,jdbcType=VARCHAR},
      </if>
       <if test="promoteld != null and promoteld != ''" >
        promoteld = #{promoteld,jdbcType=VARCHAR},
      </if>
       <if test="storyUse != null and storyUse != ''" >
        storyUse = #{storyUse,jdbcType=VARCHAR},
      </if>
       <if test="storyVideo != null and storyVideo != ''" >
        storyVideo = #{storyVideo,jdbcType=VARCHAR},
      </if>
       <if test="interstitialCodeId != null and interstitialCodeId != ''" >
        interstitialCodeId = #{interstitialCodeId,jdbcType=VARCHAR},
      </if>
       <if test="bannerCodeId != null and bannerCodeId != ''" >
        bannerCodeId = #{bannerCodeId,jdbcType=VARCHAR},
      </if>
       <if test="csjAppid != null and csjAppid != ''" >
        csjAppid = #{csjAppid,jdbcType=VARCHAR},
      </if>
      modifyTime = now()
      </set>
    where id in (${ids}) 
  </update>
  
   <update id="updatebBatchNews" parameterType="com.wbgame.pojo.clean.SuperConfigNews">
    update super_config_news
    <set >
    <if test="sdk_type1 != null and sdk_type1 != ''" >
        sdk_type1 = #{sdk_type1,jdbcType=VARCHAR},
    </if>
    <if test="sdk_type2 != null and sdk_type2 != '' " >
        sdk_type2 = #{sdk_type2,jdbcType=VARCHAR},
    </if>
    <if test="sdk_type3 != null and sdk_type3 != '' " >
        sdk_type3 = #{sdk_type3,jdbcType=VARCHAR},
    </if>
	<if test="app1 != null and app1 != '' " >  
        app1 = #{app1,jdbcType=VARCHAR},
    </if> 
    <if test="appkey != null and appkey != ''" >
        appkey = #{appkey,jdbcType=VARCHAR},
    </if>
    <if test="list_msg != null and list_msg != ''" >
        list_msg = #{list_msg,jdbcType=VARCHAR},
    </if>
    <if test="news_top != null and news_top != ''" >
        news_top = #{news_top,jdbcType=VARCHAR},
    </if>
    <if test="news_base != null and news_base != ''" >
        news_base = #{news_base,jdbcType=VARCHAR},
    </if>
    <if test="video_back != null and video_back != ''" >
        video_back = #{video_back,jdbcType=VARCHAR},
    </if>
    <if test="video_base != null and video_base != ''" >
        video_base = #{video_base,jdbcType=VARCHAR},
    </if>
    <if test="related_msg != null and related_msg != ''" >
        related_msg = #{related_msg,jdbcType=VARCHAR},
    </if>
    <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
    </if>
     <if test="ratio2 != null and ratio2 != ''  " >
        ratio2 = #{ratio2,jdbcType=VARCHAR},
    </if>
     <if test="ratio3 != null and ratio3 != '' " >
        ratio3 = #{ratio3,jdbcType=VARCHAR},
    </if>
     <if test="ratio1 != null and ratio1 != '' " >
        ratio1 = #{ratio1,jdbcType=VARCHAR},
    </if>
     <if test="app2 != null and app2 != ''" >
        app2 = #{app2,jdbcType=VARCHAR},
    </if>
     <if test="code2 != null and code2 != ''" >
        code2 = #{code2,jdbcType=VARCHAR},
    </if>
    <if test="useNewsAd != null and useNewsAd != ''" >
        useNewsAd = #{useNewsAd,jdbcType=VARCHAR},
    </if>
    <if test="newsFirstAd != null and newsFirstAd != ''" >
        newsFirstAd = #{newsFirstAd,jdbcType=VARCHAR},
    </if>
    <if test="newsAdInterval != null and newsAdInterval != ''" >
        newsAdInterval = #{newsAdInterval,jdbcType=VARCHAR},
    </if>
    <if test="newsFont != null and newsFont != ''" >
        newsFont = #{newsFont,jdbcType=VARCHAR},
    </if>
    <if test="ttLink != null and ttLink != ''" >
        ttLink = #{ttLink,jdbcType=VARCHAR},
    </if>   
    </set>
    where id in (${ids}) 
  </update>
  
  <select id="selectSuperConfigNews" resultType="com.wbgame.pojo.clean.SuperConfigNews">
    select 
   		*
    from super_config_news
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	<if test="app1 != null and app1 != ''"> 
		 and  app1 LIKE CONCAT('%','${app1}','%' )
	</if>
  </select>
  <delete id="deleteSuperConfigNews" parameterType="com.wbgame.pojo.clean.SuperConfigNews">
    delete from super_config_news
    where id = #{id}
  </delete>
   <insert id="insertSuperConfigNews" parameterType="com.wbgame.pojo.clean.SuperConfigNews">
    insert into super_config_news (`appid`,
	`cha`,
	`prjid`,
	`sdk_type1`,
	`app1`,
	`code1`,
	`appkey`,
	`list_msg`,
	`news_top`,
	`news_base`,
	`video_back`,
	`video_base`,
	`related_msg`,
	`creatTime`,
	`modifyTime`,
	`modifyUser`,`ratio1`,`sdk_type2`,`ratio2`,`sdk_type3`,`ratio3`,`app2`,`code2`,useNewsAd,newsFirstAd,newsAdInterval,newsFont)
    values (#{appid},#{cha},#{prjid},#{sdk_type1},#{app1},#{code1},#{appkey},#{list_msg},#{news_top},#{news_base}
    ,#{video_back},#{video_base},#{related_msg},NOW(),NOW(),#{modifyUser},#{ratio1},#{sdk_type2},#{ratio2},#{sdk_type3},#{ratio3},#{app2},#{code2}
    ,#{useNewsAd},#{newsFirstAd},#{newsAdInterval},#{newsFont})
  </insert>
   <update id="updateSuperConfigNews" parameterType="com.wbgame.pojo.clean.SuperConfigNews">
    update super_config_news
    set  `sdk_type1` = #{sdk_type1},
		 `sdk_type2` = #{sdk_type2},
		 `sdk_type3` = #{sdk_type3},
		 `app1` = #{app1},
		 `code1` = #{code1},
		 `appkey` = #{appkey},
		 `list_msg` = #{list_msg},
		 `news_top` = #{news_top},
		 `news_base` = #{news_base},
		 `video_back` = #{video_back},
		 `video_base` = #{video_base},
		 `related_msg` = #{related_msg},
		 `modifyTime` = NOW(),
		 `modifyUser` = #{modifyUser},
		 `ratio2` = #{ratio2},
		 `ratio3` = #{ratio3},
		 `ratio1` = #{ratio1},
		 `app2` = #{app2},
		 `useNewsAd` = #{useNewsAd},
		 `newsFirstAd` = #{newsFirstAd},
		 `newsAdInterval` = #{newsAdInterval},
		 `newsFont` = #{newsFont},
         `ttLink` = #{ttLink,jdbcType=VARCHAR},
		 `code2` = #{code2}
    where id = #{id}
  </update>
  
  
  
  <select id="selectSuperConfigDeeplink" resultType="com.wbgame.pojo.clean.SuperConfigDeeplink">
    select 
   		*
    from super_config_deeplink
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
  </select>
  <delete id="deleteSuperConfigDeeplink" parameterType="com.wbgame.pojo.clean.SuperConfigDeeplink">
    delete from super_config_deeplink
    where id = #{id}
  </delete>
   <insert id="insertSuperConfigDeeplink" parameterType="com.wbgame.pojo.clean.SuperConfigDeeplink">
    insert into super_config_deeplink (
    `appid`,
	`cha`,
	`prjid`,
	`url`,
	`model`,
	`creatTime`,
	`modifyTime`,
	`modifyUser`,
	`status`,
	`model1`,
	`model2`,`postUrl`,maxNumber,maxTimes)
    values (#{appid},#{cha},#{prjid},#{url},#{model},NOW(),NOW(),#{modifyUser},#{status},#{model1},#{model2},#{postUrl},#{maxNumber},#{maxTimes})
  </insert>
   <update id="updateSuperConfigDeeplink" parameterType="com.wbgame.pojo.clean.SuperConfigDeeplink">
    update super_config_deeplink
    set  
		 `appid` = #{appid},
		 `cha` = #{cha},
		 `prjid` = #{prjid},
		 `url` = #{url},
		 `model` = #{model},
		 `modifyTime` = NOW(),
		 `modifyUser` = #{modifyUser},
		 `model1` = #{model1},
		 `model2` = #{model2},
		 `status` = #{status},
		 maxNumber = #{maxNumber},
		 maxTimes = #{maxTimes}
    where id = #{id}
  </update>
  
  <update id="updatebBatchDeeplink" parameterType="com.wbgame.pojo.clean.SuperConfigDeeplink">
    update super_config_deeplink
    set
		 `url` = #{url},
		 `model` = #{model},
		 `modifyTime` = NOW(),
		 `modifyUser` = #{modifyUser},
		 `model1` = #{model1},
		 `model2` = #{model2},
		 maxNumber = #{maxNumber},
		 maxTimes = #{maxTimes}
    where id in (${ids}) 
  </update>
  
  
  
   <select id="selectSuperConfigParams" resultType="com.wbgame.pojo.clean.SuperConfigParams">
    select 
   		*
    from super_config_params
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
       <if test="ruleType != null and ruleType != ''">
           and ruleType = #{ruleType}
       </if>
       <if test="country != null and country != ''">
           and FIND_IN_SET(country,#{country})
       </if>

       <if test="params != null and params != ''">
           and json_contains_path(params, "all", '$.${params}')
       </if>
    order by id desc
  </select>

    <delete id="deleteSuperConfigParams" parameterType="com.wbgame.pojo.clean.SuperConfigParams">
    delete from super_config_params
    where id = #{id}
  </delete>

    <insert id="insertSuperConfigParams" parameterType="com.wbgame.pojo.clean.SuperConfigParams">
    insert into super_config_params (
    `appid`,
	`cha`,
	`prjid`,
	`params`,
	`ruleType`,
	`country`,
	`creatTime`,
	`modifyTime`,
	`modifyUser`,
	`status`,
    createUser
	)
    values (#{appid},#{cha},#{prjid},#{params},#{ruleType},#{country},NOW(),NOW(),#{modifyUser},#{status}, #{createUser})
  </insert>

    <update id="updateSuperConfigParams" parameterType="com.wbgame.pojo.clean.SuperConfigParams">
    update super_config_params
    set  
		 `appid` = #{appid},
		 `cha` = #{cha},
		 `prjid` = #{prjid},
		 `params` = #{params},
		 `ruleType` = #{ruleType},
		 `country` = #{country},
		 `modifyTime` = NOW(),
		 `status` = #{status},
		 `modifyUser` = #{modifyUser}
    where id = #{id}
  </update>

    <select id="selectWhiteList" resultType="com.wbgame.pojo.clean.SuperWhiteList">
        select type, num, device_model deviceModel, owner, cuser, cdatetime
        from super_white_list
        where  1=1
        <if test="type != null and type != ''">
            and  type = #{type,jdbcType=VARCHAR}
        </if>
        <if test="num != null and num != ''">
            and  num = #{num,jdbcType=VARCHAR}
        </if>
    </select>

    <insert id="insertWhiteList" parameterType="com.wbgame.pojo.clean.SuperWhiteList">
        insert into super_white_list(type,num,device_model,owner,cuser,cdatetime)
        values (#{type},#{num},#{deviceModel},#{owner},#{cuser},now())
    </insert>

    <delete id="deleteWhiteList" parameterType="com.wbgame.pojo.clean.SuperWhiteList">
         delete from super_white_list
         where type = #{type} and num = #{num}
         limit 1
    </delete>

    <insert id="insertBatchWhiteList" parameterType="java.util.List">
        insert into super_white_list(type,num,device_model,owner,cuser,cdatetime)values
        <foreach collection="list"  index="index" item="item" separator=",">
            (#{item.type},#{item.num},#{item.deviceModel},#{item.owner},#{item.cuser},now())
        </foreach>
    </insert>
    
    
    <select id="selectSuperConfigPublic" resultType="com.wbgame.pojo.clean.SuperConfigPublic">
    select 
   		*
    from super_config_public
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
  </select>
  <delete id="deleteSuperConfigPublic" parameterType="com.wbgame.pojo.clean.SuperConfigPublic">
    delete from super_config_public
    where id = #{id}
  </delete>
   <insert id="insertSuperConfigPublic" parameterType="com.wbgame.pojo.clean.SuperConfigPublic">
    insert into super_config_public (
    `appid`,
	`cha`,
	`prjid`,
	`tool`,
	`wallpaper`,
	`agreementStyle`,
	`creatTime`,
	`modifyTime`,
	`modifyUser`,
	`status`,phone,lottery,ggl,ccy,wd,yx
	,csy,csyStatus,csyCity,mux,zEnable,zActType,zRatio )
    values (#{appid},#{cha},#{prjid},#{tool},#{wallpaper},#{agreementStyle},NOW(),NOW(),#{modifyUser},#{status},#{phone},#{lottery},
    #{ggl},#{ccy},#{wd},#{yx}
    ,#{csy},#{csyStatus},#{csyCity},#{mux},#{zEnable},#{zActType},#{zRatio})
  </insert>
   <update id="updateSuperConfigPublic" parameterType="com.wbgame.pojo.clean.SuperConfigPublic">
    update super_config_public
    set  
		 `appid` = #{appid},
		 `cha` = #{cha},
		 `prjid` = #{prjid},
		 `tool` = #{tool},
		 `wallpaper` = #{wallpaper},
		 `agreementStyle` = #{agreementStyle},
		 `modifyTime` = NOW(),
		 `modifyUser` = #{modifyUser},
		 `phone` = #{phone},
		 `lottery` = #{lottery},
		 `ggl` = #{ggl},
		 `ccy` = #{ccy},
		 `wd` = #{wd},
		 `yx` = #{yx},
		 `mux` = #{mux},
		 `csy` = #{csy},
		 `zEnable` = #{zEnable},
		 `zActType` = #{zActType},
		 `csyStatus` = #{csyStatus},
		 `csyCity` = #{csyCity},
		 `status` = #{status},
        zRatio = #{zRatio}
    where id = #{id}
  </update>
  
  <update id="updatebBatchPublic" parameterType="com.wbgame.pojo.clean.SuperConfigPublic">
    update super_config_public
    set
		 `tool` = #{tool},
		 `wallpaper` = #{wallpaper},
		 `agreementStyle` = #{agreementStyle},
		 `modifyTime` = NOW(),
		 `modifyUser` = #{modifyUser},
		 `phone` = #{phone},
		 `lottery` = #{lottery},
		 `ggl` = #{ggl},
		 `ccy` = #{ccy},
		 `yx` = #{yx},
		 `wd` = #{wd},
		 `mux` = #{mux},
		 `csy` = #{csy},
		 `zEnable` = #{zEnable},
		 `zActType` = #{zActType},
		 `csyStatus` = #{csyStatus},
		 `csyCity` = #{csyCity},
		 `status` = #{status},
         zRatio = #{zRatio}
    where id in (${ids}) 
  </update>
  
  <insert id="insertDnapp" parameterType="java.util.List">
		insert into app_info(
			id,
			channel_id,
			app_id,
			app_name,
			create_time,
			sync_umeng,
			umeng_key,
			app_category,
		    os_type
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.id},
			#{li.channel_id},
			#{li.app_id},
			#{li.app_name},
			#{li.create_time},
			#{li.sync_umeng},
			#{li.umeng_key},
			#{li.app_category},
            #{li.os_type}
            )
		</foreach>
	</insert>
	
	 <select id="selectSuperDrawRange" resultType="com.wbgame.pojo.clean.SuperDrawRange">
    select 
   		*
    from super_draw_range
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid in (${appid})
	</if>
	<if test="tdate != null and tdate != ''">
		 and  tdate = #{tdate,jdbcType=VARCHAR}
	</if>
  </select>
  
  
  
   <select id="selectSuperConfigWinmsg" resultType="com.wbgame.pojo.clean.SuperConfigWinmsg">
    select 
   		*
    from super_config_winmsg
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	<if test="msgType != null and msgType != ''">
		 and  msgType = #{msgType,jdbcType=VARCHAR}
	</if>
  </select>
  <delete id="deleteSuperConfigWinmsg" parameterType="com.wbgame.pojo.clean.SuperConfigWinmsg">
    delete from super_config_winmsg
    where msgId in (${msgId})
  </delete>
   <insert id="insertSuperConfigWinmsg" parameterType="com.wbgame.pojo.clean.SuperConfigWinmsg">
    insert into super_config_winmsg (
    `appid`,
	`cha`,
	`prjid`,
	`icon1`,
	`text1`,
	`btnText`,
	`creatTime`,
	`modifyTime`,
	`modifyUser`,
	`status`,icon2,text2,per,msgType,loadingMsg,finshMsg)
    values (#{appid},#{cha},#{prjid},#{icon1},#{text1},#{btnText},NOW(),NOW(),#{modifyUser},#{status},#{icon2},#{text2},#{per},#{msgType},#{loadingMsg},#{finshMsg})
  </insert>
   <update id="updateSuperConfigWinmsg" parameterType="com.wbgame.pojo.clean.SuperConfigWinmsg">
    update super_config_winmsg
    set  
		 `appid` = #{appid},
		 `cha` = #{cha},
		 `prjid` = #{prjid},
		 `icon1` = #{icon1},
		 `text1` = #{text1},
		 `btnText` = #{btnText},
		 `modifyTime` = NOW(),
		 `modifyUser` = #{modifyUser},
		 `icon2` = #{icon2},
		 `text2` = #{text2},
		 `per` = #{per},
		 `msgType` = #{msgType},
		 `loadingMsg` = #{loadingMsg},
		 `finshMsg` = #{finshMsg},
		 `status` = #{status}
    where msgId = #{msgId}
  </update>

    <insert id="insertSuperConfigWinmsgBatch" parameterType="java.util.List">
        insert into super_config_winmsg
        (
        `appid`,
        `cha`,
        `prjid`,
        `icon1`,
        `text1`,
        `btnText`,
        `creatTime`,
        `modifyTime`,
        `modifyUser`,
        `status`,icon2,text2,per,msgType,loadingMsg,finshMsg)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.appid},#{item.cha},#{item.prjid},#{item.icon1},
            #{item.text1},#{item.btnText},NOW(),NOW(),#{item.modifyUser},
            #{item.status},#{item.icon2},#{item.text2},#{item.per},#{item.msgType},
            #{item.loadingMsg},#{item.finshMsg}
            )
        </foreach>
    </insert>



  
  <select id="selectCleanUmengBaseData" parameterType = "Map" resultType = "Map" >
    select 
   		`app_key`, `install_channel`, `app_version`, `ds`, `add_num`, `act_num`, TRUNCATE(`duration`/1000,0) duration, TRUNCATE(`duration2`/1000,0) duration2, `start_num`, `break`, a.`appid`
    from umeng_base_data a
    <if test = "x1 != null and  x1 != ''">
        inner join dn_channel_info b on a.install_channel =b.cha_id
    </if>
    <if test = "x2 != null and  x2 != ''">
        inner join dn_channel_info c on a.install_channel =c.cha_id
    </if>
      <if test = "appGroups != null and  appGroups != ''">
          inner join dn_group_app d on a.appid =d.appId
      </if>
    where  1=1
    <if test = "start_time != null and end_time != null">
        and  ds  BETWEEN #{start_time} AND #{end_time}
    </if>
    <if test="appid != null and appid != ''">
         and a.appid in (${appid})
    </if>
	<if test="install_channel != null and install_channel != ''">
         and install_channel in (${install_channel})
	</if>
	<if test="app_version != null and app_version != ''">
		 and  app_version in (${app_version})
	</if>
    <if test = "x1 != null and  x1 != ''">
         and b.cha_type in (${x1})
    </if>
    <if test = "x2 != null and  x2 != ''">
         and c.cha_media in (${x2})
    </if>
      <if test = "appGroups != null and  appGroups != ''">
          and d.groupId in (${appGroups})
      </if>
  </select>
  
  <select id="selectSumCleanUmengBaseData" parameterType = "Map" resultType = "Map" >
    select 
   		 sum(`add_num`) add_num, sum(`act_num`) act_num 
    from umeng_base_data a
      <if test = "x1 != null and  x1 != ''">
           inner join dn_channel_info b on a.install_channel =b.cha_id
      </if>
      <if test = "x2 != null and  x2 != ''">
           inner join dn_channel_info c on a.install_channel =c.cha_id
      </if>
      <if test = "appGroups != null and  appGroups != ''">
          inner join dn_group_app d on a.appid =d.appId
      </if>

    where  1=1
    <if test = "start_time != null and end_time != null">
          and  ds  BETWEEN #{start_time} AND #{end_time}
    </if>
    <if test="appid != null and appid != ''">
          and a.appid in (${appid})
    </if>
    <if test="install_channel != null and install_channel != ''">
          and install_channel in (${install_channel})
    </if>
    <if test="app_version != null and app_version != ''">
          and  app_version in (${app_version})
    </if>
      <if test = "x1 != null and  x1 != ''">
          and b.cha_type in (${x1})
      </if>
      <if test = "x2 != null and  x2 != ''">
          and c.cha_media in (${x2})
      </if>
      <if test = "appGroups != null and  appGroups != ''">
          and d.groupId in (${appGroups})
      </if>
  </select>
  
  
  <select id="selectSuperDeeplinkNew" resultType="com.wbgame.pojo.clean.SuperDeeplinkNew">
    select 
   		*
    from super_deeplink_new
    where  1=1 
     <if test="pkg != null and pkg != ''">
		 and  pkg = #{pkg,jdbcType=VARCHAR}
	</if>
	<if test="dpId != null and dpId != ''">
		 and  dpId = #{dpId,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
  </select>
  <delete id="deleteSuperDeeplinkNew" parameterType="com.wbgame.pojo.clean.SuperDeeplinkNew">
    delete from super_deeplink_new
    where dpId = #{dpId}
  </delete>
   <insert id="insertSuperDeeplinkNew" parameterType="com.wbgame.pojo.clean.SuperDeeplinkNew">
    insert into super_deeplink_new (
	`pkg`,
	`dpUrl`,
	`dpModel`,
	`normalPer`,
	`noFellPer`,
	`creatTime`,
	`modifyTime`,
	`modifyUser`,
	`status`)
    values (#{pkg},#{dpUrl},#{dpModel},#{normalPer},#{noFellPer},NOW(),NOW(),#{modifyUser},#{status})
  </insert>
  
   <update id="updateSuperDeeplinkNew" parameterType="com.wbgame.pojo.clean.SuperDeeplinkNew">
    update super_deeplink_new
    set  
		 `dpUrl` = #{dpUrl},
		 `dpModel` = #{dpModel},
		 `normalPer` = #{normalPer},
		 `noFellPer` = #{noFellPer},
		 `modifyTime` = NOW(),
		 `modifyUser` = #{modifyUser},
		 `status` = #{status}
    where dpId = #{dpId}
  </update>
  
  <select id="selectSuperDeeplinkTask" resultType="com.wbgame.pojo.clean.SuperDeeplinkTask">
    select 
   		*
    from super_deeplink_task
    where  1=1 
     <if test="taskId != null and taskId != ''">
		 and  taskId = #{taskId,jdbcType=VARCHAR}
	</if>
	<if test="dpId != null and dpId != ''">
		 and  dpId = #{dpId,jdbcType=VARCHAR}
	</if>
  </select>
  
  <delete id="deleteSuperDeeplinkTask" parameterType="com.wbgame.pojo.clean.SuperDeeplinkTask">
    delete from super_deeplink_task
    where taskId = #{taskId}
  </delete>
   <insert id="insertSuperDeeplinkTask" parameterType="com.wbgame.pojo.clean.SuperDeeplinkTask">
    insert into super_deeplink_task (
    `dpId`,
	`startDt`,
	`endDt`,
	`total`,
	`times`,
	`days`,
	`startTime`,
	`endTime`,
	`deeplinkDay`,
	`userPolicy`,
	`creatTime`,
	`modifyTime`,
	`status`,
	`modifyUser`
	)
    values (#{dpId},#{startDt},#{endDt},#{total},#{times},#{days},#{startTime},#{endTime},#{deeplinkDay},#{userPolicy},NOW(),NOW(),#{status},#{modifyUser})
  </insert>
  
   <update id="updateSuperDeeplinkTask" parameterType="com.wbgame.pojo.clean.SuperDeeplinkTask">
    update super_deeplink_task
    set  
		 `startDt` = #{startDt},
		 `endDt` = #{endDt},
		 `total` = #{total},
		 `times` = #{times},
		 `days` = #{days},
		 `startTime` = #{startTime},
		 `endTime` = #{endTime},
		 `deeplinkDay` = #{deeplinkDay},
		 `userPolicy` = #{userPolicy},
		 `modifyTime` = NOW(),
		 `status` = #{status},
		 `modifyUser` = #{modifyUser}
    where taskId = #{taskId}
  </update>
  
   <select id="selectSuperDeeplinkReport" resultType="com.wbgame.pojo.clean.SuperDeeplinkReport">
    select 
   		*
    from super_deeplink_report
    where  1=1 
     <if test="taskId != null and taskId != ''">
		 and  taskId = #{taskId,jdbcType=VARCHAR}
	</if>
	<if test="dpId != null and dpId != ''">
		 and  dpId = #{dpId,jdbcType=VARCHAR}
	</if>
	 <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="pid != null and pid != ''">
		 and  pid = #{pid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test = "startTime != null and startTime != '' and endTime != null  and endTime != ''">
		and tdate BETWEEN #{startTime} AND #{endTime}
	</if>
	
  </select>
  
     <select id="selectSuperDeeplinkPackageReport" resultType="com.wbgame.pojo.clean.SuperDeeplinkPackageReport">
    select 
   		*
    from super_deeplink_package_nums
    where  1=1 
	<if test = "startTime != null and startTime != '' and endTime != null  and endTime != ''">
		and tdate BETWEEN #{startTime} AND #{endTime}
	</if>
	<if test="packages != null and packages != ''">
		 and  packages = #{packages,jdbcType=VARCHAR}
	</if>
	
  </select>
  
   <select id="selectSuperDeeplinkPolicy" resultType="com.wbgame.pojo.clean.SuperDeeplinkPolicy">
    select 
   		*
    from super_deeplink_policy
    where  1=1 
     <if test="policyId != null and policyId != ''">
		 and  policyId = #{policyId,jdbcType=VARCHAR}
	</if>
	 <if test="policyName != null and policyName != ''">
		 and  policyName = #{policyName,jdbcType=VARCHAR}
	</if>
  </select>
  
  <delete id="deleteSuperDeeplinkPolicy" parameterType="com.wbgame.pojo.clean.SuperDeeplinkPolicy">
    delete from super_deeplink_policy
    where policyId = #{policyId}
  </delete>
   <insert id="insertSuperDeeplinkPolicy" parameterType="com.wbgame.pojo.clean.SuperDeeplinkPolicy">
    insert into super_deeplink_policy (
	`pidList`,
	`chaList`,
	`creatTime`,
	`modifyTime`,
	`modifyUser`,
	`policyName`,
	`city`,
	`cityStatus`,
	`appidList`
	)
    values (#{pidList},#{chaList},NOW(),NOW(),#{modifyUser},#{policyName},#{city},#{cityStatus},#{appidList})
  </insert>
  
   <update id="updateSuperDeeplinkPolicy" parameterType="com.wbgame.pojo.clean.SuperDeeplinkPolicy">
    update super_deeplink_policy
    set  
		 `pidList` = #{pidList},
		 `chaList` = #{chaList},
		 `policyName` = #{policyName},
		 `appidList` = #{appidList},
		 `city` = #{city},
		 `cityStatus` = #{cityStatus},
		 `modifyTime` = NOW(),
		 `modifyUser` = #{modifyUser}
    where policyId = #{policyId}
  </update>
  
  <select id="selectSuperLockNew" resultType="com.wbgame.pojo.clean.SuperLockNew">
    select 
   		*
    from super_lock_new
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	<if test="buy_act != null and buy_act != ''">
		 and  buy_act = #{buy_act,jdbcType=VARCHAR}
	</if>
	<if test="buy_id != null and buy_id != ''">
		 and  buy_id = #{buy_id,jdbcType=VARCHAR}
	</if>
	order by modifyTime desc
  </select>
  
   <delete id="deleteSuperLockNew" parameterType="com.wbgame.pojo.clean.SuperLockNew">
    delete from super_lock_new
    where id = #{id}
  </delete>
   <insert id="insertSuperLockNew" parameterType="com.wbgame.pojo.clean.SuperLockNew">
    insert into super_lock_new (appid, cha, 
      prjid,buy_act,buy_id, firstLock, firstLockNew, 
      firstUnLock, firstHome, creatTime, 
      modifyTime, modifyUser, status, 
      lockTotalNum, unlockTotalNum, homeNum, 
      lockSwitch, lockStyle, unLockSwitch, 
      unLockInterval, unLockStyle, homeSwitch, 
      homeInterval, homeAuto, ldyAuto, 
      homeStyle, cityStatus, city, 
      lockStatus, lockCity, unLockStatus, 
      dianLiangSwitch,dianLiangPer,dianLiangModel,
      dianLiangStyle,chongdianSwitch,chongdianStyle,wifiSwitch,wifiStyle,ejectInterval,
      unLockCity, homeStatus, homeCity,cuser,outNewsSw
      ,taFd,taSt,taEt,taIt,taSta,taCity,taNum,taSw,taSty,nqDt
      ,ul_fas,h_fas,ta_fas,wf_fas,bat_fas,power_fas
      ,taPointStyle,pSt,pEt,pIt,pFas,pSw,highAdStyle,minInterval,maxInterval,highAdFas,dayTotalNum,taAdIt,
      highSw,wkPakList,wkInterval,wkStatus,wkCity,wkSw,openLifeTime,showSettingPage,ipShield,markShield
      )
    values (#{appid,jdbcType=VARCHAR}, #{cha,jdbcType=VARCHAR}, 
      #{prjid,jdbcType=VARCHAR},#{buy_act,jdbcType=VARCHAR} ,#{buy_id,jdbcType=VARCHAR}, #{firstLock,jdbcType=VARCHAR}, #{firstLockNew,jdbcType=VARCHAR}, 
      #{firstUnLock,jdbcType=VARCHAR}, #{firstHome,jdbcType=VARCHAR}, NOW(),
      NOW(),#{modifyUser},#{status,jdbcType=VARCHAR}, 
      #{lockTotalNum,jdbcType=VARCHAR}, #{unlockTotalNum,jdbcType=VARCHAR}, #{homeNum,jdbcType=VARCHAR}, 
      #{lockSwitch,jdbcType=VARCHAR}, #{lockStyle,jdbcType=VARCHAR}, #{unLockSwitch,jdbcType=VARCHAR}, 
      #{unLockInterval,jdbcType=VARCHAR}, #{unLockStyle,jdbcType=OTHER}, #{homeSwitch,jdbcType=VARCHAR}, 
      #{homeInterval,jdbcType=VARCHAR}, #{homeAuto,jdbcType=VARCHAR}, #{ldyAuto,jdbcType=VARCHAR}, 
      #{homeStyle,jdbcType=OTHER}, #{cityStatus,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{lockStatus,jdbcType=VARCHAR}, #{lockCity,jdbcType=VARCHAR}, #{unLockStatus,jdbcType=VARCHAR}, 
      #{dianLiangSwitch},#{dianLiangPer},#{dianLiangModel},
      #{dianLiangStyle},#{chongdianSwitch},#{chongdianStyle},#{wifiSwitch},#{wifiStyle},#{ejectInterval},
      #{unLockCity,jdbcType=VARCHAR}, #{homeStatus,jdbcType=VARCHAR}, #{homeCity,jdbcType=VARCHAR},#{cuser,jdbcType=VARCHAR},#{outNewsSw,jdbcType=VARCHAR}
      ,#{taFd},#{taSt},#{taEt},#{taIt},#{taSta},#{taCity},#{taNum},#{taSw},#{taSty},#{nqDt}
      ,#{ul_fas},#{h_fas},#{ta_fas},#{wf_fas},#{bat_fas},#{power_fas}
       ,#{taPointStyle},#{pSt},#{pEt},#{pIt},#{pFas},#{pSw},
        #{highAdStyle},#{minInterval},#{maxInterval},#{highAdFas},#{dayTotalNum},#{taAdIt},
            #{highSw},#{wkPakList},#{wkInterval},#{wkStatus},#{wkCity},#{wkSw},#{openLifeTime},#{showSettingPage},#{ipShield},#{markShield}
      )
  </insert>
   <update id="updateSuperLockNew" parameterType="com.wbgame.pojo.clean.SuperLockNew" >
    update super_lock_new
    <set >
      <if test="firstLock != null" >
        firstLock = #{firstLock,jdbcType=VARCHAR},
      </if>
      <if test="firstLockNew != null" >
        firstLockNew = #{firstLockNew,jdbcType=VARCHAR},
      </if>
      <if test="firstUnLock != null" >
        firstUnLock = #{firstUnLock,jdbcType=VARCHAR},
      </if>
      <if test="firstHome != null" >
        firstHome = #{firstHome,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null" >
        modifyTime = NOW(),
      </if>
      <if test="modifyUser != null" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="lockTotalNum != null" >
        lockTotalNum = #{lockTotalNum,jdbcType=VARCHAR},
      </if>
      <if test="unlockTotalNum != null" >
        unlockTotalNum = #{unlockTotalNum,jdbcType=VARCHAR},
      </if>
      <if test="homeNum != null" >
        homeNum = #{homeNum,jdbcType=VARCHAR},
      </if>
      <if test="lockSwitch != null" >
        lockSwitch = #{lockSwitch,jdbcType=VARCHAR},
      </if>
      <if test="lockStyle != null" >
        lockStyle = #{lockStyle,jdbcType=VARCHAR},
      </if>
      <if test="unLockSwitch != null" >
        unLockSwitch = #{unLockSwitch,jdbcType=VARCHAR},
      </if>
      <if test="unLockInterval != null" >
        unLockInterval = #{unLockInterval,jdbcType=VARCHAR},
      </if>
      <if test="unLockStyle != null" >
        unLockStyle = #{unLockStyle,jdbcType=OTHER},
      </if>
      <if test="homeSwitch != null" >
        homeSwitch = #{homeSwitch,jdbcType=VARCHAR},
      </if>
      <if test="homeInterval != null" >
        homeInterval = #{homeInterval,jdbcType=VARCHAR},
      </if>
      <if test="homeAuto != null" >
        homeAuto = #{homeAuto,jdbcType=VARCHAR},
      </if>
      <if test="ldyAuto != null" >
        ldyAuto = #{ldyAuto,jdbcType=VARCHAR},
      </if>
      <if test="homeStyle != null" >
        homeStyle = #{homeStyle,jdbcType=OTHER},
      </if>
      <if test="cityStatus != null" >
        cityStatus = #{cityStatus,jdbcType=VARCHAR},
      </if>
      <if test="nqDt != null" >
        nqDt = #{nqDt,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="lockStatus != null" >
        lockStatus = #{lockStatus,jdbcType=VARCHAR},
      </if>
      <if test="lockCity != null" >
        lockCity = #{lockCity,jdbcType=VARCHAR},
      </if>
      <if test="unLockStatus != null" >
        unLockStatus = #{unLockStatus,jdbcType=VARCHAR},
      </if>
      <if test="unLockCity != null" >
        unLockCity = #{unLockCity,jdbcType=VARCHAR},
      </if>
      <if test="homeStatus != null" >
        homeStatus = #{homeStatus,jdbcType=VARCHAR},
      </if>
      <if test="dianLiangSwitch != null" >
        dianLiangSwitch = #{dianLiangSwitch,jdbcType=VARCHAR},
      </if>
      <if test="dianLiangPer != null" >
        dianLiangPer = #{dianLiangPer,jdbcType=VARCHAR},
      </if>
      <if test="dianLiangModel != null" >
        dianLiangModel = #{dianLiangModel,jdbcType=VARCHAR},
      </if>
      <if test="dianLiangStyle != null" >
        dianLiangStyle = #{dianLiangStyle,jdbcType=VARCHAR},
      </if>
      <if test="chongdianSwitch != null" >
        chongdianSwitch = #{chongdianSwitch,jdbcType=VARCHAR},
      </if>
      <if test="chongdianStyle != null" >
        chongdianStyle = #{chongdianStyle,jdbcType=VARCHAR},
      </if>
      <if test="wifiSwitch != null" >
        wifiSwitch = #{wifiSwitch,jdbcType=VARCHAR},
      </if>
      <if test="wifiStyle != null" >
        wifiStyle = #{wifiStyle,jdbcType=VARCHAR},
      </if>
      <if test="ejectInterval != null" >
        ejectInterval = #{ejectInterval,jdbcType=VARCHAR},
      </if>
      <if test="homeCity != null" >
        homeCity = #{homeCity,jdbcType=VARCHAR},
      </if>
      <if test="outNewsSw != null" >
        outNewsSw = #{outNewsSw,jdbcType=VARCHAR},
      </if>
       <if test="taFd != null" >
        taFd = #{taFd,jdbcType=VARCHAR},
      </if>
      <if test="taSt != null" >
        taSt = #{taSt,jdbcType=VARCHAR},
      </if>
      <if test="taEt != null" >
        taEt = #{taEt,jdbcType=VARCHAR},
      </if>
      <if test="taIt != null" >
        taIt = #{taIt,jdbcType=VARCHAR},
      </if>
      <if test="taSta != null" >
        taSta = #{taSta,jdbcType=VARCHAR},
      </if>
      <if test="taCity != null" >
        taCity = #{taCity,jdbcType=VARCHAR},
      </if>
      <if test="taNum != null" >
        taNum = #{taNum,jdbcType=VARCHAR},
      </if>
      <if test="taSw != null" >
        taSw = #{taSw,jdbcType=VARCHAR},
      </if>
       <if test="taSty != null" >
        taSty = #{taSty,jdbcType=VARCHAR},
      </if>
      <if test="ul_fas != null" >
        ul_fas = #{ul_fas,jdbcType=VARCHAR},
      </if>
      <if test="h_fas != null" >
        h_fas = #{h_fas,jdbcType=VARCHAR},
      </if>
      <if test="ta_fas != null" >
        ta_fas = #{ta_fas,jdbcType=VARCHAR},
      </if>
      <if test="wf_fas != null" >
        wf_fas = #{wf_fas,jdbcType=VARCHAR},
      </if>
      <if test="bat_fas != null" >
        bat_fas = #{bat_fas,jdbcType=VARCHAR},
      </if>
       <if test="power_fas != null" >
        power_fas = #{power_fas,jdbcType=VARCHAR},
      </if>
       <if test="taPointStyle != null" >
        taPointStyle = #{taPointStyle,jdbcType=VARCHAR},
      </if>
       <if test="pSt != null" >
        pSt = #{pSt,jdbcType=VARCHAR},
      </if>
       <if test="pEt != null" >
        pEt = #{pEt,jdbcType=VARCHAR},
      </if>
       <if test="pIt != null" >
        pIt = #{pIt,jdbcType=VARCHAR},
      </if>
       <if test="pFas != null" >
        pFas = #{pFas,jdbcType=VARCHAR},
      </if>
       <if test="pSw != null" >
        pSw = #{pSw,jdbcType=VARCHAR},
      </if>

        <if test="highAdStyle != null" >
            highAdStyle = #{highAdStyle,jdbcType=VARCHAR},
        </if>
        <if test="minInterval != null" >
            minInterval = #{minInterval,jdbcType=VARCHAR},
        </if>
        <if test="maxInterval != null" >
            maxInterval = #{maxInterval,jdbcType=VARCHAR},
        </if>
        <if test="highAdFas != null" >
            highAdFas = #{highAdFas,jdbcType=VARCHAR},
        </if>
        <if test="dayTotalNum != null" >
            dayTotalNum = #{dayTotalNum,jdbcType=VARCHAR},
        </if>

        <if test="taAdIt != null" >
            taAdIt = #{taAdIt,jdbcType=VARCHAR},
        </if>

        <if test="highSw != null" >
            highSw = #{highSw,jdbcType=VARCHAR},
        </if>
        <if test="wkPakList != null" >
            wkPakList = #{wkPakList,jdbcType=VARCHAR},
        </if>
        <if test="wkInterval != null" >
            wkInterval = #{wkInterval,jdbcType=VARCHAR},
        </if>
        <if test="wkStatus != null" >
            wkStatus = #{wkStatus,jdbcType=VARCHAR},
        </if>
        <if test="wkCity != null" >
            wkCity = #{wkCity,jdbcType=VARCHAR},
        </if>
        <if test="wkSw != null" >
            wkSw = #{wkSw,jdbcType=VARCHAR},
        </if>

        <if test="openLifeTime != null" >
            openLifeTime = #{openLifeTime,jdbcType=VARCHAR},
        </if>
        <if test="showSettingPage != null" >
            showSettingPage = #{showSettingPage,jdbcType=VARCHAR},
        </if>
        <if test="ipShield != null" >
            ipShield = #{ipShield,jdbcType=VARCHAR},
        </if>
        <if test="markShield != null" >
            markShield = #{markShield,jdbcType=VARCHAR},
        </if>
    </set>
    where id in (${id})
  </update>
  
  <select id="selectAppUmeng" resultType="com.wbgame.pojo.custom.AdMsgTotalVo2">
    select 
   		  ${group_by} , SUM(show_num) show_num ,  SUM(click_num) click_num,
   		  SUM(unique_num) unique_num,
   		  concat(round(SUM(click_num) / SUM(show_num)*100, 2),'%') click_rate 
   		  <if test="group_by != null and group_by != ''">
   		  ,`act_num` act_num,concat(round(SUM(unique_num) / act_num*100, 2),'%') ratio 
   		  </if>
    from umeng_admsg_total3
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid in (${appid})
	</if>
	 <if test="ad_pos != null and ad_pos != ''">
		 and  ad_pos = #{ad_pos,jdbcType=VARCHAR}
	</if>
	<if test="app_version != null and app_version != ''">
		 and  app_version = #{app_version,jdbcType=VARCHAR}
	</if>
	<if test="install_channel != null and install_channel != ''">
		 and  install_channel in (${install_channel})
	</if>
	<if test = "startTime != null and startTime != '' and endTime != null  and endTime != ''">
		and tdate BETWEEN #{startTime} AND #{endTime}
	</if>
	group by ${group_by}
	order by ${order}
  </select>
  
   <select id="selectSumAppUmeng" resultType="com.wbgame.pojo.custom.AdMsgTotalVo2">
    select 
   		 SUM(show_num) show_num ,  SUM(click_num) click_num,
   		  SUM(unique_num) unique_num
   		 
    from umeng_admsg_total3
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="ad_pos != null and ad_pos != ''">
		 and  ad_pos = #{ad_pos,jdbcType=VARCHAR}
	</if>
	<if test="app_version != null and app_version != ''">
		 and  app_version = #{app_version,jdbcType=VARCHAR}
	</if>
	<if test="install_channel != null and install_channel != ''">
		 and  install_channel = #{install_channel,jdbcType=VARCHAR}
	</if>
	<if test = "startTime != null and startTime != '' and endTime != null  and endTime != ''">
		and tdate BETWEEN #{startTime} AND #{endTime}
	</if>

  </select>
  
  <select id="selectAppUmengType" resultType="com.wbgame.pojo.custom.AdMsgTotalVo3">
    select 
   		`tdate`, `appid`, `type`,  SUM(load_num) load_num ,  SUM(loaded_num) loaded_num,
   		  SUM(loadfail_num) loadfail_num, SUM(show_num) show_num, SUM(showed_num) showed_num,
   		   SUM(click_num) click_num,SUM(unique_num) unique_num,
   		  concat(round(SUM(showed_num) / SUM(loaded_num)*100, 2),'%') show_rate, 
   		  concat(round(SUM(click_num) / SUM(showed_num)*100, 2),'%') click_rate 
   		  <if test="group_by != null and group_by != ''">
   		  ,`act_num` act_num,concat(round(SUM(unique_num) / act_num*100, 2),'%') unique_rate 
   		  </if>
   		  <if test="group_by != null and group_by != ''">
	 		${group_by}
	 	 </if>
    from umeng_admsg_total4
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid in (${appid})
	</if>
	 <if test="type != null and type != ''">
		 and  type = #{type,jdbcType=VARCHAR}
	</if>
	<if test="app_version != null and app_version != ''">
		 and  app_version = #{app_version,jdbcType=VARCHAR}
	</if>
	<if test="install_channel != null and install_channel != ''">
		 and  install_channel = #{install_channel,jdbcType=VARCHAR}
	</if>
	<if test = "startTime != null and startTime != '' and endTime != null  and endTime != ''">
		and tdate BETWEEN #{startTime} AND #{endTime}
	</if>
	group by `tdate`, `appid` , `type`
	<if test="group_by != null and group_by != ''">
	 ${group_by}
	 </if>
	 order by ${order}
  </select>
  
  <select id="selectSumAppUmengType" resultType="com.wbgame.pojo.custom.AdMsgTotalVo3">
    select 
   		 SUM(show_num) show_num ,  SUM(click_num) click_num,
   		 SUM(load_num) load_num ,  SUM(loaded_num) loaded_num,
   		 SUM(unique_num) unique_num,SUM(loadfail_num) loadfail_num
   		 
    from umeng_admsg_total4
   	where  1=1 
     <if test="appid != null and appid != ''">
		and  appid in (${appid})
	</if>
	 <if test="type != null and type != ''">
		 and  type = #{type,jdbcType=VARCHAR}
	</if>
	<if test="app_version != null and app_version != ''">
		 and  app_version = #{app_version,jdbcType=VARCHAR}
	</if>
	<if test="install_channel != null and install_channel != ''">
		 and  install_channel = #{install_channel,jdbcType=VARCHAR}
	</if>
	<if test = "startTime != null and startTime != '' and endTime != null  and endTime != ''">
		and tdate BETWEEN #{startTime} AND #{endTime}
	</if>

  </select>
  
  <select id="selectQpConfigTag" resultType="com.wbgame.pojo.clean.QpConfigTag">
    select 
   		*
    from qp_config_tag
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="tag_title != null and tag_title != ''">
		 and  tag_title = #{tag_title,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	order by appid desc , modifyTime desc
  </select>
  <delete id="deleteQpConfigTag" parameterType="com.wbgame.pojo.clean.QpConfigTag">
    delete from qp_config_tag
    where id = #{id}
  </delete>
   <insert id="insertQpConfigTag" parameterType="com.wbgame.pojo.clean.QpTagTemp">
    insert into qp_config_tag (appid, cha, 
      prjid, tag_title, creatTime, 
      modifyTime, modifyUser, status,sort
      )
    values (#{appid,jdbcType=VARCHAR}, #{cha,jdbcType=VARCHAR}, 
      #{prjid,jdbcType=VARCHAR}, #{tag_title,jdbcType=VARCHAR}, NOW(),
      NOW(),#{modifyUser},#{status,jdbcType=VARCHAR},#{sort,jdbcType=VARCHAR}
      )
  </insert>
   <update id="updateQpConfigTag" parameterType="com.wbgame.pojo.clean.QpConfigTag" >
    update qp_config_tag
    <set >
	<if test="appid != null and appid != ''">
		   appid = #{appid,jdbcType=VARCHAR},
	</if>
	<if test="prjid != null and prjid != ''">
		   prjid = #{prjid,jdbcType=VARCHAR},
	</if>
	<if test="cha != null and cha != ''">
		   cha = #{cha,jdbcType=VARCHAR},
	</if>
      <if test="tag_title != null" >
        tag_title = #{tag_title,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null" >
        modifyTime = NOW(),
      </if>
      <if test="modifyUser != null" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        sort = #{sort,jdbcType=VARCHAR},
      </if>
    </set>
    where id in (${id})
  </update>

  <select id="selectQpTagTemp" resultType="com.wbgame.pojo.clean.QpTagTemp">
     select 
   		a.*,b.appid
    from qp_tag_temp a LEFT JOIN qp_config_tag b ON a.tagid = b.id
    where  1=1 
     <if test="id != null and id != ''">
		 and  a.id = #{id,jdbcType=VARCHAR}
	</if>
	<if test="tagid != null and tagid != ''">
		 and  a.tagid = #{tagid,jdbcType=VARCHAR}
	</if>
	<if test="temp_title != null and temp_title != ''">
		 and  a.temp_title like concat('%',#{temp_title},'%')
	</if>
	<if test="status != null and status != ''">
		 and  a.status = #{status,jdbcType=VARCHAR}
	</if>
	<if test="appid != null and appid != ''">
		 and  b.appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="zoneId != null and zoneId != ''">
		 and  a.zoneId = #{zoneId,jdbcType=VARCHAR}
	</if>
	order by a.tagid
  </select>
  <delete id="deleteQpTagTemp" parameterType="com.wbgame.pojo.clean.QpTagTemp">
    delete from qp_tag_temp
    where id = #{id}
  </delete>
   <insert id="insertQpTagTemp" parameterType="com.wbgame.pojo.clean.QpTagTemp">
    insert into qp_tag_temp (tagid, temp_title, 
      video_url, video_width,video_height,
      img_url, img_width,img_height,
      res_url, res_size,res_md5,like_cnt,
      creatTime, modifyTime, modifyUser, status,`type`,plcolor,micro_video_url,micro_video_width,micro_video_height,temp_type
      ,material_url,sort,`describe`,desc_text,tag_text,taskTime,vipTemp,isTmep,hot ,zoneId
      )
    values (#{tagid,jdbcType=VARCHAR}, #{temp_title,jdbcType=VARCHAR}, 
      #{video_url,jdbcType=VARCHAR}, #{video_width,jdbcType=VARCHAR},#{video_height,jdbcType=VARCHAR},
       #{img_url,jdbcType=VARCHAR}, #{img_width,jdbcType=VARCHAR},#{img_height,jdbcType=VARCHAR},
       #{res_url,jdbcType=VARCHAR}, #{res_size,jdbcType=VARCHAR},#{res_md5,jdbcType=VARCHAR},#{like_cnt,jdbcType=VARCHAR},
       NOW(),NOW(),#{modifyUser},#{status,jdbcType=VARCHAR},#{type,jdbcType=VARCHAR},#{plcolor,jdbcType=VARCHAR}
       ,#{micro_video_url,jdbcType=VARCHAR},#{micro_video_width,jdbcType=VARCHAR},#{micro_video_height,jdbcType=VARCHAR},#{temp_type}
       ,#{material_url},#{sort},#{describe},#{desc_text},#{tag_text},#{taskTime},#{vipTemp},#{isTmep},#{hot},#{zoneId}
      )
  </insert>
  
   <update id="editQpTagTemp" parameterType="com.wbgame.pojo.clean.QpTagTemp" >
    update qp_tag_temp
    <set >
      <if test="like_cnt != null and like_cnt != ''" >
        like_cnt = #{like_cnt,jdbcType=VARCHAR},
      </if>
      <if test="sort != null and sort != ''" >
        sort = #{sort,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id}
  </update>
  
   <update id="editQpTagTempNum" parameterType="com.wbgame.pojo.clean.QpTagTemp" >
    update qp_tag_temp
    <set >
      <if test="hot != null and hot != ''" >
        hot = #{hot,jdbcType=VARCHAR},
      </if>
       <if test="status != null and status != ''" >
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id}
  </update>
  
   <update id="editQpSignTemp" parameterType="com.wbgame.pojo.clean.QpTagTemp" >
    update qp_sign_temp
    <set >
      <if test="like_cnt != null and like_cnt != ''" >
        like_cnt = #{like_cnt,jdbcType=VARCHAR},
      </if>
      <if test="sort != null and sort != ''" >
        sort = #{sort,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id}
  </update>
  
   <update id="updateQpTagTemp" parameterType="com.wbgame.pojo.clean.QpTagTemp" >
    update qp_tag_temp
    <set >
      <if test="tagid != null and tagid != ''" >
        tagid = #{tagid,jdbcType=VARCHAR},
      </if>
       <if test="type != null and type != ''" >
        `type` = #{type,jdbcType=VARCHAR},
      </if>
       desc_text = #{desc_text,jdbcType=VARCHAR},
       tag_text = #{tag_text,jdbcType=VARCHAR},
       material_url = #{material_url,jdbcType=VARCHAR},
       modifyTime = NOW(),
       <if test="describe != null and describe != ''" >
        `describe` = #{describe,jdbcType=VARCHAR},
      </if>
       <if test="temp_title != null and temp_title != ''" >
        temp_title = #{temp_title,jdbcType=VARCHAR},
      </if>
      <if test="vipTemp != null and vipTemp != ''" >
        vipTemp = #{vipTemp,jdbcType=VARCHAR},
      </if>
      <if test="isTmep != null and isTmep != ''" >
        isTmep = #{isTmep,jdbcType=VARCHAR},
      </if>
        video_url = #{video_url,jdbcType=VARCHAR},
      <if test="video_width != null and video_width != ''" >
        video_width = #{video_width,jdbcType=VARCHAR},
      </if>
      <if test="video_height != null and video_height != ''" >
        video_height = #{video_height,jdbcType=VARCHAR},
      </if>
      
        img_url = #{img_url,jdbcType=VARCHAR},
        
      <if test="img_width != null and img_width != ''" >
        img_width = #{img_width,jdbcType=VARCHAR},
      </if>
      <if test="img_height != null and img_height != ''" >
        img_height = #{img_height,jdbcType=VARCHAR},
      </if>
      
        res_url = #{res_url,jdbcType=VARCHAR},
        
      <if test="res_size != null and res_size != ''" >
        res_size = #{res_size,jdbcType=VARCHAR},
      </if>
        zoneId = #{zoneId,jdbcType=VARCHAR},
      <if test="res_md5 != null and res_md5 != ''" >
        res_md5 = #{res_md5,jdbcType=VARCHAR},
      </if>
      <if test="like_cnt != null and like_cnt != ''" >
        like_cnt = #{like_cnt,jdbcType=VARCHAR},
      </if>
        micro_video_url = #{micro_video_url,jdbcType=VARCHAR},
      <if test="micro_video_width != null and micro_video_width != ''" >
        micro_video_width = #{micro_video_width,jdbcType=VARCHAR},
      </if>
      <if test="micro_video_height != null and micro_video_height != ''" >
        micro_video_height = #{micro_video_height,jdbcType=VARCHAR},
      </if>
      <if test="temp_type != null and temp_type != ''" >
        temp_type = #{temp_type,jdbcType=VARCHAR},
      </if>
      <if test="sort != null and sort != ''" >
        sort = #{sort,jdbcType=VARCHAR},
      </if>
       <if test="taskTime != null and taskTime != ''" >
        taskTime = #{taskTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyUser != null" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="hot != null" >
        hot = #{hot,jdbcType=VARCHAR},
      </if>
    </set>
    where id in (${id})
  </update>

    <insert id="batchInsertDnGroupAppToClean">
        <foreach collection="list" item="li" separator=";">
            insert into dn_group_app(id,groupId,appId,appname)
            select #{li.id},#{li.groupId},#{li.appId},app_name from app_info where id = #{li.appId}
        </foreach>
    </insert>

    <delete id="delCleanDnGroupApp">
		delete from dn_group_app;
	</delete>
	
	
	 <select id="selectQpSignTemp" resultType="com.wbgame.pojo.clean.QpSignTemp">
    select 
   		*
    from qp_sign_temp
    where  1=1 
    <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="temp_title != null and temp_title != ''">
		 and  temp_title = #{temp_title,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
  </select>
  <delete id="deleteQpSignTemp" parameterType="com.wbgame.pojo.clean.QpSignTemp">
    delete from qp_sign_temp
    where id = #{id}
  </delete>
   <insert id="insertQpSignTemp" parameterType="com.wbgame.pojo.clean.QpSignTemp">
    insert into qp_sign_temp (appid,temp_title, 
      video_url, video_width,video_height,
      img_url, img_width,img_height,
      res_url, res_size,res_md5,like_cnt,
      creatTime, modifyTime, modifyUser, status,`type`,plcolor,micro_video_url,micro_video_width,micro_video_height,temp_type
      ,material_url,sort,`describe`,desc_text,tag_text,creatUser,vipTemp
      )
    values ( #{appid,jdbcType=VARCHAR}, #{temp_title,jdbcType=VARCHAR}, 
      #{video_url,jdbcType=VARCHAR}, #{video_width,jdbcType=VARCHAR},#{video_height,jdbcType=VARCHAR},
       #{img_url,jdbcType=VARCHAR}, #{img_width,jdbcType=VARCHAR},#{img_height,jdbcType=VARCHAR},
       #{res_url,jdbcType=VARCHAR}, #{res_size,jdbcType=VARCHAR},#{res_md5,jdbcType=VARCHAR},#{like_cnt,jdbcType=VARCHAR},
       NOW(),NOW(),#{modifyUser},#{status,jdbcType=VARCHAR},#{type,jdbcType=VARCHAR},#{plcolor,jdbcType=VARCHAR}
       ,#{micro_video_url,jdbcType=VARCHAR},#{micro_video_width,jdbcType=VARCHAR},#{micro_video_height,jdbcType=VARCHAR},#{temp_type}
       ,#{material_url},#{sort},#{describe},#{desc_text},#{tag_text},#{creatUser},#{vipTemp}
      )
  </insert>
   <update id="updateQpSignTemp" parameterType="com.wbgame.pojo.clean.QpSignTemp" >
    update qp_sign_temp
    <set >
      <if test="appid != null and type != ''" >
        `appid` = #{appid,jdbcType=VARCHAR},
      </if>
       <if test="type != null and type != ''" >
        `type` = #{type,jdbcType=VARCHAR},
      </if>
       desc_text = #{desc_text,jdbcType=VARCHAR},
       tag_text = #{tag_text,jdbcType=VARCHAR},
       material_url = #{material_url,jdbcType=VARCHAR},
       modifyTime = NOW(), 
       <if test="describe != null and describe != ''" >
        `describe` = #{describe,jdbcType=VARCHAR},
      </if>
       <if test="sort != null and sort != ''" >
        sort = #{sort,jdbcType=VARCHAR},
      </if>
       <if test="temp_title != null and temp_title != ''" >
        temp_title = #{temp_title,jdbcType=VARCHAR},
      </if>
      <if test="vipTemp != null and vipTemp != ''" >
        vipTemp = #{vipTemp,jdbcType=VARCHAR},
      </if>
      
        video_url = #{video_url,jdbcType=VARCHAR},
        
      <if test="video_width != null and video_width != ''" >
        video_width = #{video_width,jdbcType=VARCHAR},
      </if>
      <if test="video_height != null and video_height != ''" >
        video_height = #{video_height,jdbcType=VARCHAR},
      </if>
      
        img_url = #{img_url,jdbcType=VARCHAR},
        
      <if test="img_width != null and img_width != ''" >
        img_width = #{img_width,jdbcType=VARCHAR},
      </if>
      <if test="img_height != null and img_height != ''" >
        img_height = #{img_height,jdbcType=VARCHAR},
      </if>
      
        res_url = #{res_url,jdbcType=VARCHAR},
        
      <if test="res_size != null and res_size != ''" >
        res_size = #{res_size,jdbcType=VARCHAR},
      </if>
      <if test="res_md5 != null and res_md5 != ''" >
        res_md5 = #{res_md5,jdbcType=VARCHAR},
      </if>
      <if test="like_cnt != null and like_cnt != ''" >
        like_cnt = #{like_cnt,jdbcType=VARCHAR},
      </if>
      
        micro_video_url = #{micro_video_url,jdbcType=VARCHAR},
        
      <if test="micro_video_width != null and micro_video_width != ''" >
        micro_video_width = #{micro_video_width,jdbcType=VARCHAR},
      </if>
      <if test="micro_video_height != null and micro_video_height != ''" >
        micro_video_height = #{micro_video_height,jdbcType=VARCHAR},
      </if>
      <if test="temp_type != null and temp_type != ''" >
        temp_type = #{temp_type,jdbcType=VARCHAR},
      </if>
       
      <if test="modifyUser != null" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id in (${id})
  </update>
  
   
  
   <select id="selectQpWordsList" resultType="com.wbgame.pojo.clean.QpWordsList">
    select 
   		*
    from qp_words_list
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="statu != null and statu != ''">
		 and  statu = #{statu,jdbcType=VARCHAR}
	</if>
  </select>
  
  <insert id="insertQpWordsList" parameterType="com.wbgame.pojo.clean.QpWordsList">
    insert into qp_words_list (appid, 
      words, statu,cuser,
      createtime, euser,endtime
      )
    values ( #{appid,jdbcType=VARCHAR}, 
      #{words,jdbcType=VARCHAR}, #{statu,jdbcType=VARCHAR},#{cuser,jdbcType=VARCHAR},
       NOW(), #{euser,jdbcType=VARCHAR},NOW()
      )
  </insert>
  
  <delete id="deleteQpWordsList" parameterType="com.wbgame.pojo.clean.QpWordsList">
    delete from qp_words_list
    where id = #{id}
  </delete>
  
   <update id="updateQpWordsList" parameterType="com.wbgame.pojo.clean.QpWordsList" >
    update qp_words_list
    set 
    `appid` = #{appid,jdbcType=VARCHAR},
    `words` = #{words,jdbcType=VARCHAR},
    `statu` = #{statu,jdbcType=VARCHAR},
    `euser` = #{euser,jdbcType=VARCHAR},
    `endtime` =  NOW()
     where id in (${id})
   </update>
   
   
   <select id="selectQpWeekUrl" resultType="com.wbgame.pojo.clean.QpWeekUrl">
    select 
   		*
    from qp_week_characters
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="statu != null and statu != ''">
		 and  statu = #{statu,jdbcType=VARCHAR}
	</if>
  </select>
  
  <insert id="insertQpWeekUrl" parameterType="com.wbgame.pojo.clean.QpWeekUrl">
    insert into qp_week_characters (appid, Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday
      , statu,cuser,
      createtime, euser,endtime
      )
    values ( #{appid,jdbcType=VARCHAR}, #{Monday,jdbcType=VARCHAR},#{Tuesday,jdbcType=VARCHAR},#{Wednesday,jdbcType=VARCHAR},
    #{Thursday,jdbcType=VARCHAR},#{Friday,jdbcType=VARCHAR},#{Saturday,jdbcType=VARCHAR},
      #{Sunday,jdbcType=VARCHAR}, #{statu,jdbcType=VARCHAR},#{cuser,jdbcType=VARCHAR},
       NOW(), #{euser,jdbcType=VARCHAR},NOW()
      )
  </insert>
  
  <delete id="deleteQpWeekUrl" parameterType="com.wbgame.pojo.clean.QpWeekUrl">
    delete from qp_week_characters
    where appid = #{appid}
  </delete>
  
   <update id="updateQpWeekUrl" parameterType="com.wbgame.pojo.clean.QpWeekUrl" >
    update qp_week_characters
    set 
    `appid` = #{appid,jdbcType=VARCHAR},
    `Monday` = #{Monday,jdbcType=VARCHAR},
    `Tuesday` = #{Tuesday,jdbcType=VARCHAR},
    `Wednesday` = #{Wednesday,jdbcType=VARCHAR},
    `Thursday` = #{Thursday,jdbcType=VARCHAR},
    `Friday` = #{Friday,jdbcType=VARCHAR},
    `Saturday` = #{Saturday,jdbcType=VARCHAR},
    `Sunday` = #{Sunday,jdbcType=VARCHAR},
    `statu` = #{statu,jdbcType=VARCHAR},
    `euser` = #{euser,jdbcType=VARCHAR},
    `endtime` =  NOW()
     where appid in (${appid})
   </update>
   
   <update id="updateQpSignTempStatus" parameterType="com.wbgame.pojo.clean.QpSignTemp" >
    update qp_sign_temp
    set
    	 status = #{status,jdbcType=VARCHAR}
    where id in (${id})
    </update>
    
    <update id="updateQpWordsListStatus" parameterType="com.wbgame.pojo.clean.QpWordsList" >
    update qp_words_list
    set
    	 statu = #{statu,jdbcType=VARCHAR}
    where id  = #{id}
    </update>
    
    <update id="updateQpWeekUrlStatus" parameterType="com.wbgame.pojo.clean.QpWeekUrl" >
    update qp_week_characters
    set
    	 statu = #{statu,jdbcType=VARCHAR}
    where appid  = #{appid}
    </update>
    
    <insert id="copySignTemp" parameterType="com.wbgame.pojo.clean.QpSignTemp" >
    INSERT INTO `qp_sign_temp` (`temp_title`,`video_url`,`video_width`,`video_height`,`img_url`,`img_width`,`img_height`,`res_url`,`res_size`,`res_md5`,`micro_video_url`,`micro_video_width`,`micro_video_height`,
	`status`,`creatUser`,`like_cnt`,`creatTime`,`modifyTime`,`modifyUser`,`type`,`plcolor`,`temp_type`,`material_url`,`sort`,`describe`,`tag_text`,`desc_text`,`appid`
	) SELECT `temp_title`,`video_url`,`video_width`,`video_height`,`img_url`,`img_width`,`img_height`,`res_url`,`res_size`,`res_md5`,`micro_video_url`,`micro_video_width`,`micro_video_height`,`status`,
		`creatUser`,`like_cnt`,now(),now(),`modifyUser`,`type`,`plcolor`,`temp_type`,`material_url`,`sort`,`describe`,`tag_text`,`desc_text`,'38151' AS appid
	FROM
		`qp_tag_temp`
	WHERE
		id = #{id}
    </insert>
    
   <select id="selectCardTagConfig" resultType="com.wbgame.pojo.clean.CardTagConfig">
    select 
   		*
    from card_tag_config
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
  </select>
  <delete id="deleteCardTagConfig" parameterType="com.wbgame.pojo.clean.CardTagConfig">
    delete from card_tag_config
    where tagId = #{tagId}
  </delete>
   <insert id="insertCardTagConfig" parameterType="com.wbgame.pojo.clean.CardTagConfig">
    insert into card_tag_config (appid, cha, 
      prjid, tagName, createtime, cuser,
      endtime, euser, status,sort
      )
    values (#{appid,jdbcType=VARCHAR}, #{cha,jdbcType=VARCHAR}, 
      #{prjid,jdbcType=VARCHAR}, #{tagName,jdbcType=VARCHAR}, NOW(),#{cuser},
      NOW(),#{euser},#{status,jdbcType=VARCHAR},#{sort,jdbcType=VARCHAR}
      )
  </insert>
   <update id="updateCardTagConfig" parameterType="com.wbgame.pojo.clean.CardTagConfig" >
    update card_tag_config
    <set >
      <if test="tagName != null" >
        tagName = #{tagName,jdbcType=VARCHAR},
      </if>
      <if test="endtime != null" >
        endtime = NOW(),
      </if>
      <if test="euser != null" >
        euser = #{euser,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        sort = #{sort,jdbcType=VARCHAR},
      </if>
    </set>
    where tagId in (${tagId})
  </update>
  
  <select id="selectCardInfoConfig" resultType="com.wbgame.pojo.clean.CardInfoConfig">
    select 
   		*
    from card_info_config
    where  1=1 
     <if test="tagId != null and tagId != ''">
		 and  tagId = #{tagId,jdbcType=VARCHAR}
	</if>
	<if test="giftName != null and giftName != ''">
		 and  giftName LIKE CONCAT('%','${giftName}','%' )
	</if>
	<if test="giftType != null and giftType != ''">
		 and  giftType = #{giftType,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
  </select>
  <delete id="deleteCardInfoConfig" parameterType="com.wbgame.pojo.clean.CardInfoConfig">
    delete from card_info_config
    where giftId = #{giftId}
  </delete>
   <insert id="insertCardInfoConfig" parameterType="com.wbgame.pojo.clean.CardInfoConfig">
    insert into card_info_config (tagId, giftName, 
      cardLogo, giftPic, giftSumNum, giftCardId,giftCardEName, giftCardName,giftCardNum, giftType,
      createtime, cuser,
      endtime, euser, status,sort
      )
    values (#{tagId,jdbcType=VARCHAR}, #{giftName,jdbcType=VARCHAR}, 
      #{cardLogo,jdbcType=VARCHAR}, #{giftPic,jdbcType=VARCHAR},
      #{giftSumNum,jdbcType=VARCHAR}, #{giftCardId,jdbcType=VARCHAR},#{giftCardEName,jdbcType=VARCHAR}, #{giftCardName,jdbcType=VARCHAR},
      #{giftCardNum,jdbcType=VARCHAR}, #{giftType,jdbcType=VARCHAR},
       NOW(),#{cuser},
      NOW(),#{euser},#{status,jdbcType=VARCHAR},#{sort,jdbcType=VARCHAR}
      )
  </insert>
   <update id="updateCardInfoConfig" parameterType="com.wbgame.pojo.clean.CardInfoConfig" >
    update card_info_config
    <set >
      <if test="tagId != null" >
        tagId = #{tagId,jdbcType=VARCHAR},
      </if>
      <if test="giftName != null" >
        giftName = #{giftName,jdbcType=VARCHAR},
      </if>
      <if test="cardLogo != null" >
        cardLogo = #{cardLogo,jdbcType=VARCHAR},
      </if>
      <if test="giftPic != null" >
        giftPic = #{giftPic,jdbcType=VARCHAR},
      </if>
      <if test="giftSumNum != null" >
        giftSumNum = #{giftSumNum,jdbcType=VARCHAR},
      </if>
      <if test="giftCardId != null" >
        giftCardId = #{giftCardId,jdbcType=VARCHAR},
      </if>
      <if test="giftCardEName != null" >
        giftCardEName = #{giftCardEName,jdbcType=VARCHAR},
      </if>
      <if test="giftCardName != null" >
        giftCardName = #{giftCardName,jdbcType=VARCHAR},
      </if>
      <if test="giftCardNum != null" >
        giftCardNum = #{giftCardNum,jdbcType=VARCHAR},
      </if>
      <if test="giftType != null" >
        giftType = #{giftType,jdbcType=VARCHAR},
      </if>
      <if test="endtime != null" >
        endtime = NOW(),
      </if>
      <if test="euser != null" >
        euser = #{euser,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        sort = #{sort,jdbcType=VARCHAR},
      </if>
    </set>
    where giftId in (${giftId})
  </update>
    
     <select id="selectQpPopVo" resultType="com.wbgame.pojo.clean.QpPopVo">
    select 
   		*
    from qp_pop_config
    where  1=1 
	<if test="popdesc != null and popdesc != ''">
		 and  popdesc LIKE CONCAT('%','${popdesc}','%' )
	</if>
	<if test="popName != null and popName != ''">
		 and  popName LIKE CONCAT('%','${popName}','%' )
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
  </select>
  <delete id="deleteQpPopVo" parameterType="com.wbgame.pojo.clean.QpPopVo">
    delete from qp_pop_config
    where id = #{id}
  </delete>
   <insert id="insertQpPopVo" parameterType="com.wbgame.pojo.clean.QpPopVo">
    insert into qp_pop_config (`appid`, `popName`, `popsign`, `poppic`,
     `poptitle`, `popdesc`, `popbutton`, `poptemp`,
      `createtime`, `cuser`, `endtime`, `euser`, `status`
      )
    values (#{appid,jdbcType=VARCHAR}, #{popName,jdbcType=VARCHAR}, 
      #{popsign,jdbcType=VARCHAR}, #{poppic,jdbcType=VARCHAR},
      #{poptitle,jdbcType=VARCHAR}, #{popdesc,jdbcType=VARCHAR},#{popbutton,jdbcType=VARCHAR}, #{poptemp,jdbcType=VARCHAR},
       NOW(),#{cuser},
      NOW(),#{euser},#{status,jdbcType=VARCHAR}
      )
  </insert>
   <update id="updateQpPopVo" parameterType="com.wbgame.pojo.clean.QpPopVo" >
    update qp_pop_config
    <set >
      <if test="popName != null" >
        popName = #{popName,jdbcType=VARCHAR},
      </if>
      <if test="popsign != null" >
        popsign = #{popsign,jdbcType=VARCHAR},
      </if>
      <if test="poppic != null" >
        poppic = #{poppic,jdbcType=VARCHAR},
      </if>
      <if test="poptitle != null" >
        poptitle = #{poptitle,jdbcType=VARCHAR},
      </if>
      <if test="popdesc != null" >
        popdesc = #{popdesc,jdbcType=VARCHAR},
      </if>
      <if test="popbutton != null" >
        popbutton = #{popbutton,jdbcType=VARCHAR},
      </if>
      <if test="poptemp != null" >
        poptemp = #{poptemp,jdbcType=VARCHAR},
      </if>
      <if test="endtime != null" >
        endtime = NOW(),
      </if>
      <if test="euser != null" >
        euser = #{euser,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id in (${id})
  </update>

  <select id="selectQpEmojiTitle" resultType="com.wbgame.pojo.clean.QpEmojiTitle">
    select 
   		*
    from qp_emoji_title
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	 <if test="title != null and title != ''">
		and  title like concat('%',#{title},'%')
	</if>
  </select>
  <delete id="deleteQpEmojiTitle" parameterType="com.wbgame.pojo.clean.QpEmojiTitle">
    delete from qp_emoji_title
    where titleId = #{titleId}
  </delete>
   <insert id="insertQpEmojiTitle" parameterType="com.wbgame.pojo.clean.QpEmojiTitle">
    insert into qp_emoji_title (appid, cha, 
      prjid, title,picture1,picture2,picture3, createTime, createUser,
      modifyTime, modifyUser, status,sort
      )
    values (#{appid,jdbcType=VARCHAR}, #{cha,jdbcType=VARCHAR}, 
      #{prjid,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR},
      #{picture1,jdbcType=VARCHAR}, #{picture2,jdbcType=VARCHAR}, #{picture3,jdbcType=VARCHAR},
       NOW(),#{createUser},
      NOW(),#{modifyUser},#{status,jdbcType=VARCHAR},#{sort,jdbcType=VARCHAR}
      )
  </insert>
   <update id="updateQpEmojiTitle" parameterType="com.wbgame.pojo.clean.QpEmojiTitle" >
    update qp_emoji_title
    <set >
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
       <if test="picture1 != null" >
        picture1 = #{picture1,jdbcType=VARCHAR},
      </if>
       <if test="picture2 != null" >
        picture2 = #{picture2,jdbcType=VARCHAR},
      </if>
       <if test="picture3 != null" >
        picture3 = #{picture3,jdbcType=VARCHAR},
      </if>

      <if test="modifyUser != null" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        sort = #{sort,jdbcType=VARCHAR},
      </if>
      modifyTime = NOW()
    </set>
    where titleId in (${titleId})
  </update>
  
   <select id="selectQpEmojiInfo" resultType="com.wbgame.pojo.clean.QpEmojiInfo">
    select 
   		*
    from qp_emoji_info
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	 <if test="emojiName != null and emojiName != ''">
		and  emojiName like concat('%',#{emojiName},'%')
	</if>
	 <if test="titleId != null and titleId != ''">
		and  titleId = #{titleId,jdbcType=VARCHAR}
	</if>
  </select>
  <delete id="deleteQpEmojiInfo" parameterType="com.wbgame.pojo.clean.QpEmojiInfo">
    delete from qp_emoji_info
    where id = #{id}
  </delete>
   <insert id="insertQpEmojiInfo" parameterType="com.wbgame.pojo.clean.QpEmojiInfo">
    insert into qp_emoji_info (appid, cha, 
      prjid, emojiUrl,titleId,emojiName, createTime, createUser,
      modifyTime, modifyUser, status,sort
      )
    values (#{appid,jdbcType=VARCHAR}, #{cha,jdbcType=VARCHAR}, 
      #{prjid,jdbcType=VARCHAR}, #{emojiUrl,jdbcType=VARCHAR},
      #{titleId,jdbcType=VARCHAR}, #{emojiName,jdbcType=VARCHAR},
       NOW(),#{createUser},
      NOW(),#{modifyUser},#{status,jdbcType=VARCHAR},#{sort,jdbcType=VARCHAR}
      )		  
  </insert>
   <update id="updateQpEmojiInfo" parameterType="com.wbgame.pojo.clean.QpEmojiInfo" >
    update qp_emoji_info
    <set >
      <if test="emojiUrl != null and emojiUrl != ''" >
        emojiUrl = #{emojiUrl,jdbcType=VARCHAR},
      </if>
      <if test="titleId != null and titleId != ''" >
        titleId = #{titleId,jdbcType=VARCHAR},
      </if>
      <if test="emojiName != null and emojiName != ''" >
        emojiName = #{emojiName,jdbcType=VARCHAR},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null and sort != ''" >
        sort = #{sort,jdbcType=VARCHAR},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
    <select id="selectCleandbInfo" resultType="com.wbgame.pojo.clean.CleandbInfo">
    select 
   		*
    from cleandb
    where  1=1 
	<if test="junk_type != null and junk_type != ''">
		 and  junk_type = #{junk_type}
	</if>
	<if test="file_type != null and file_type != ''">
		 and  file_type = #{file_type}
	</if>
	<if test="app_name != null and app_name != ''">
		and  app_name like concat('%',#{app_name},'%')
	</if>
	 <if test="pkg_name != null and pkg_name != ''">
		and  pkg_name like concat('%',#{pkg_name},'%')
	</if>
  </select>
  <delete id="deleteCleandbInfo" parameterType="com.wbgame.pojo.clean.CleandbInfo">
    delete from cleandb
    where id = #{id}
  </delete>
   <insert id="insertCleandbInfo" parameterType="com.wbgame.pojo.clean.CleandbInfo">
    insert into cleandb (pkg_name, app_name, 
      junk_type, file_type,`desc`,file_path, root_path, strategy,
      `update`
      )
    values (#{pkg_name}, #{app_name}, 
      #{junk_type}, #{file_type},
      #{desc}, #{file_path},
       #{root_path},#{strategy},
      NOW()
      )		  
  </insert>
   <update id="updateCleandbInfo" parameterType="com.wbgame.pojo.clean.CleandbInfo" >
    update cleandb
    <set >
      <if test="pkg_name != null and pkg_name != ''" >
        pkg_name = #{pkg_name},
      </if>
      <if test="app_name != null and app_name != ''" >
        app_name = #{app_name},
      </if>
      <if test="junk_type != null and junk_type != ''" >
        junk_type = #{junk_type},
      </if>
      <if test="file_type != null and file_type != ''" >
        file_type = #{file_type},
      </if>
      <if test="desc != null and desc != ''" >
        `desc` = #{desc},
      </if>
      <if test="file_path != null and file_path != ''" >
        file_path = #{file_path},
      </if>
      <if test="root_path != null and root_path != ''" >
        root_path = #{root_path},
      </if>
      <if test="strategy != null and strategy != ''" >
        strategy = #{strategy},
      </if>
      `update` = NOW()
    </set>
    where id in (${id})
  </update>
  
  <insert id="insertQpEmojiInfoList" parameterType="com.wbgame.pojo.clean.QpEmojiInfo">
 
     insert into qp_emoji_info (appid, cha, 
      prjid, emojiUrl,titleId,emojiName, createTime, createUser,
      modifyTime, modifyUser, status,sort
      )
    values 
     <foreach collection="list" item="li" separator=",">
      (#{li.appid,jdbcType=VARCHAR}, #{li.cha,jdbcType=VARCHAR}, 
      #{li.prjid,jdbcType=VARCHAR}, #{li.emojiUrl,jdbcType=VARCHAR},
      #{li.titleId,jdbcType=VARCHAR}, #{li.emojiName,jdbcType=VARCHAR},
       NOW(),'',
      NOW(),'',#{li.status,jdbcType=VARCHAR},#{li.sort,jdbcType=VARCHAR}
      )		 
	</foreach>
	
  </insert>
  
  <select id="selectSuperMapCity" resultType="com.wbgame.pojo.clean.SuperMapCity">
    select 
   		*
    from super_map_city
    where  1=1 
	<if test="type != null and type != ''">
		 and  type = #{type,jdbcType=VARCHAR}
	</if>
	<if test="sort != null and sort != ''">
		 and  sort = #{sort,jdbcType=VARCHAR}
	</if>
	<if test="status != null ">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	 <if test="name != null and name != ''">
		and  name like concat('%',#{name},'%')
	</if>
	order by sort
  </select>
  <delete id="deleteSuperMapCity" parameterType="com.wbgame.pojo.clean.SuperMapCity">
    delete from super_map_city
    where id in (${id})
  </delete>
   <insert id="insertSuperMapCity" parameterType="com.wbgame.pojo.clean.SuperMapCity">
    insert into super_map_city (name, image, 
      type,createTime, createUser,
      modifyTime, modifyUser, status,sort
      )
    values (#{name,jdbcType=VARCHAR}, #{image,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR},
       NOW(),#{createUser},
      NOW(),#{modifyUser},#{status,jdbcType=VARCHAR},#{sort,jdbcType=VARCHAR}
      )
  </insert>
   <update id="updateSuperMapCity" parameterType="com.wbgame.pojo.clean.SuperMapCity" >
    update super_map_city
    <set >
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
       <if test="image != null" >
        image = #{image,jdbcType=VARCHAR},
      </if>
       <if test="type != null" >
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="modifyUser != null" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        sort = #{sort,jdbcType=VARCHAR},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
    <select id="selectSuperMapMaterial" resultType="com.wbgame.pojo.clean.SuperMapMaterial">
    select 
   		*
    from super_map_material
    where  1=1 
	<if test="url != null and url != ''">
		 and  url = #{url,jdbcType=VARCHAR}
	</if>
	<if test="cityId != null ">
		 and  cityId = #{cityId,jdbcType=VARCHAR}
	</if>
	<if test="stars != null ">
		 and  stars = #{stars,jdbcType=VARCHAR}
	</if>
	<if test="sort != null ">
		 and  sort = #{sort,jdbcType=VARCHAR}
	</if>
	<if test="image != null and image != ''">
		 and  image = #{image,jdbcType=VARCHAR}
	</if>
	 <if test="title != null and title != ''">
		and  title like concat('%',#{title},'%')
	</if>
	order by sort
  </select>
  <delete id="deleteSuperMapMaterial" parameterType="com.wbgame.pojo.clean.SuperMapMaterial">
    delete from super_map_material
    where id in (${id})
  </delete>
   <insert id="insertSuperMapMaterial" parameterType="com.wbgame.pojo.clean.SuperMapMaterial">
    insert into super_map_material (title, image, 
      url,cityId,stars,
      createTime, createUser,
      modifyTime, modifyUser, status,sort
      )
    values (#{title,jdbcType=VARCHAR}, #{image,jdbcType=VARCHAR}, 
      #{url,jdbcType=VARCHAR},#{cityId,jdbcType=VARCHAR},#{stars,jdbcType=VARCHAR},
       NOW(),#{createUser},
      NOW(),#{modifyUser},#{status,jdbcType=VARCHAR},#{sort,jdbcType=VARCHAR}
      )
  </insert>
   <update id="updateSuperMapMaterial" parameterType="com.wbgame.pojo.clean.SuperMapMaterial" >
    update super_map_material
    <set >
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
       <if test="image != null" >
        image = #{image,jdbcType=VARCHAR},
      </if>
       <if test="url != null" >
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null" >
        cityId = #{cityId,jdbcType=VARCHAR},
      </if>
      <if test="stars != null" >
        stars = #{stars,jdbcType=VARCHAR},
      </if>
      <if test="modifyUser != null" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        sort = #{sort,jdbcType=VARCHAR},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
  <select id="selectSuperMapConfig" resultType="com.wbgame.pojo.clean.SuperMapConfig">
    select 
   		*
    from super_map_config
    where  1=1 
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
  </select>
  <delete id="deleteSuperMapConfig" parameterType="com.wbgame.pojo.clean.SuperMapConfig">
    delete from super_map_config
    where id in (${id})
  </delete>
   <insert id="insertSuperMapConfig" parameterType="com.wbgame.pojo.clean.SuperMapConfig">
    insert into super_map_config (prjid, appid, 
      cha,vrTimes,scTimes,privilege,recharge,
      createTime, createUser,
      modifyTime, modifyUser, status
      )
    values (#{prjid,jdbcType=VARCHAR}, #{appid,jdbcType=VARCHAR}, 
      #{cha,jdbcType=VARCHAR},#{vrTimes,jdbcType=VARCHAR},#{scTimes,jdbcType=VARCHAR},
      #{privilege,jdbcType=VARCHAR},#{recharge,jdbcType=VARCHAR},
       NOW(),#{createUser},
      NOW(),#{modifyUser},#{status,jdbcType=VARCHAR}
      )
  </insert>
   <update id="updateSuperMapConfig" parameterType="com.wbgame.pojo.clean.SuperMapConfig" >
    update super_map_config
    <set >
      <if test="vrTimes != null and vrTimes != ''" >
        vrTimes = #{vrTimes,jdbcType=VARCHAR},
      </if>
       <if test="scTimes != null and scTimes != ''" >
        scTimes = #{scTimes,jdbcType=VARCHAR},
      </if>
       <if test="privilege != null and privilege != ''" >
        privilege = #{privilege,jdbcType=VARCHAR},
      </if>
      <if test="recharge != null and recharge != ''" >
        recharge = #{recharge,jdbcType=VARCHAR},
      </if>
      <if test="modifyUser != null" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
  
  <select id="selectSuperQupaiConfig" resultType="com.wbgame.pojo.clean.SuperQupaiConfig">
    select 
   		*
    from super_qupai_config
    where  1=1 
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null ">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
  </select>
  <delete id="deleteSuperQupaiConfig" parameterType="com.wbgame.pojo.clean.SuperQupaiConfig">
    delete from super_qupai_config
    where id in (${id})
  </delete>
   <insert id="insertSuperQupaiConfig" parameterType="com.wbgame.pojo.clean.SuperQupaiConfig">
    insert into super_qupai_config (prjid, appid, 
      cha,privilege,recharge,
      createTime, createUser,
      modifyTime, modifyUser, status
      )
    values (#{prjid,jdbcType=VARCHAR}, #{appid,jdbcType=VARCHAR}, 
      #{cha,jdbcType=VARCHAR},
      #{privilege,jdbcType=VARCHAR},#{recharge,jdbcType=VARCHAR},
       NOW(),#{createUser},
      NOW(),#{modifyUser},#{status,jdbcType=VARCHAR}
      )
  </insert>
   <update id="updateSuperQupaiConfig" parameterType="com.wbgame.pojo.clean.SuperQupaiConfig" >
    update super_qupai_config
    <set >
       <if test="privilege != null and privilege != ''" >
        privilege = #{privilege,jdbcType=VARCHAR},
      </if>
      <if test="recharge != null and recharge != ''" >
        recharge = #{recharge,jdbcType=VARCHAR},
      </if>
      <if test="modifyUser != null" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
  <select id="selectSuperLockCenable" resultType="com.wbgame.pojo.clean.SuperLockCenable">
    select 
   		*
    from super_config_cenable
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	<if test="buy_act != null and buy_act != ''">
		 and  buy_act = #{buy_act,jdbcType=VARCHAR}
	</if>
	<if test="buy_id != null and buy_id != ''">
		 and  buy_id = #{buy_id,jdbcType=VARCHAR}
	</if>
	order by modifyTime desc
  </select>
  
   <delete id="deleteSuperLockCenable" parameterType="com.wbgame.pojo.clean.SuperLockCenable">
    delete from super_config_cenable
    where id = #{id}
  </delete>
   <insert id="insertSuperLockCenable" parameterType="com.wbgame.pojo.clean.SuperLockCenable">
    insert into super_config_cenable (appid, cha, prjid,buy_act,buy_id,
      creatTime,modifyTime, modifyUser, status, cuser,config
      )
    values (#{appid,jdbcType=VARCHAR}, #{cha,jdbcType=VARCHAR},  #{prjid,jdbcType=VARCHAR},#{buy_act,jdbcType=VARCHAR} ,#{buy_id,jdbcType=VARCHAR}, 
      NOW(),NOW(),#{modifyUser,jdbcType=VARCHAR},#{status,jdbcType=VARCHAR},#{cuser,jdbcType=VARCHAR},#{config,jdbcType=VARCHAR}
      )
  </insert>
   <update id="updateSuperLockCenable" parameterType="com.wbgame.pojo.clean.SuperLockCenable" >
    update super_config_cenable
    <set >
      <if test="modifyUser != null" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="config != null" >
        config = #{config,jdbcType=VARCHAR},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
  
  <select id="selectAppWbPayInfo" resultType="com.wbgame.pojo.AppWbPayInfo">
    select 
   		*
    from wb_pay_info
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid in (${appid})
	</if>
	<if test="pid != null and pid != ''">
		 and  pid = #{pid,jdbcType=VARCHAR}
	</if>
	<if test="payname != null and payname != ''">
		 and  payname = #{payname,jdbcType=VARCHAR}
	</if>
	<if test="startTime != null and startTime != ''">AND DATE(createtime) <![CDATA[>=]]> #{startTime}</if>
 	<if test="endTime != null and endTime != ''">AND DATE(createtime) <![CDATA[<=]]>#{endTime}</if>

	order by createtime desc
  </select>
  
  
  <select id="selectApplicationPatchInfo" resultType="com.wbgame.pojo.clean.ApplicationPatchInfo">
    select 
   		*
    from super_app_patch
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="masterVersion != null and masterVersion != ''">
		 and  masterVersion = #{masterVersion,jdbcType=VARCHAR}
	</if>
	<if test="patchVersion != null and patchVersion != ''">
		 and  patchVersion = #{patchVersion,jdbcType=VARCHAR}
	</if>
	
	order by patchVersion desc
  </select>
  
   <delete id="deleteApplicationPatchInfo" parameterType="com.wbgame.pojo.clean.ApplicationPatchInfo">
    delete from super_app_patch
    where id = #{id}
  </delete>
   <insert id="insertApplicationPatchInfo" parameterType="com.wbgame.pojo.clean.ApplicationPatchInfo">
    insert into super_app_patch (appid, masterVersion, patchVersion,patchNote,patchUrl,
      status,able, createTime, updateTime, createUser,updateUser,rate,md5,addWhitelist
      )
    values (#{appid}, #{masterVersion},  #{patchVersion},#{patchNote} ,#{patchUrl}, 
      #{status},#{able},NOW(),NOW(),#{createUser},#{updateUser},#{rate},#{md5},#{addWhitelist}
      )
  </insert>
   <update id="updateApplicationPatchInfo" parameterType="com.wbgame.pojo.clean.ApplicationPatchInfo" >
    update super_app_patch
    <set >
      <if test="patchVersion != null and patchVersion != ''" >
        patchVersion = #{patchVersion},
      </if>
      <if test="patchNote != null and patchNote != ''" >
        patchNote = #{patchNote},
      </if>
      <if test="patchUrl != null and patchUrl != ''" >
        patchUrl = #{patchUrl},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="able != null and able != ''" >
        able = #{able},
      </if>
      <if test="updateUser != null and updateUser != ''" >
        updateUser = #{updateUser},
      </if>
        <if test="rate != null and rate != ''" >
        rate = #{rate},
      </if>
        <if test="md5 != null and md5 != ''" >
        md5 = #{md5},
      </if>
       <if test="addWhitelist != null and addWhitelist != ''" >
        addWhitelist = #{addWhitelist},
      </if>
      updateTime = NOW()
    </set>
    where id in (${id})
  </update>
  
  <select id="selectApplicationAutoActivation" resultType="com.wbgame.pojo.clean.ApplicationAutoActivation">
    select 
   		*
    from super_auto_activation
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	
	order by modifyTime desc
  </select>

    <select id="selectAppExits" parameterType="com.wbgame.pojo.clean.ApplicationAutoActivation"
            resultType="java.lang.Integer">
        select
        1
        from super_auto_activation

        where appid = #{appid,jdbcType=VARCHAR}

       <choose>
           <when test="prjid != null and prjid != ''">
               and  prjid = #{prjid,jdbcType=VARCHAR}
           </when>
           <otherwise>
               and  cha = #{cha,jdbcType=VARCHAR}
           </otherwise>
       </choose>
        limit 1
    </select>
  
   <delete id="deleteApplicationAutoActivation" parameterType="com.wbgame.pojo.clean.ApplicationAutoActivation">
    delete from super_auto_activation
    where id = #{id}
  </delete>
   <insert id="insertApplicationAutoActivation" parameterType="com.wbgame.pojo.clean.ApplicationAutoActivation">
    insert into super_auto_activation (appid, cha, prjid,rate,atTime,
      mode,reportRate,createTime,modifyTime, city, cityStatus, createUser,modifyUser,status,scheduleReport,sfCtr,scit
      )
    values (#{appid}, #{cha},  #{prjid},#{rate} ,#{atTime}, 
      #{mode},#{reportRate},NOW(),NOW(),#{city},#{cityStatus},#{createUser},#{modifyUser},#{status},#{scheduleReport},
      #{sfCtr}, #{scit})
  </insert>
   <update id="updateApplicationAutoActivation" parameterType="com.wbgame.pojo.clean.ApplicationAutoActivation" >
    update super_auto_activation
    <set >
      <if test="rate != null" >
        rate = #{rate},
      </if>
      <if test="atTime != null " >
        atTime = #{atTime},
      </if>
      <if test="scheduleReport != null and scheduleReport != ''" >
        scheduleReport = #{scheduleReport},
      </if>
       <if test="mode != null and mode != ''" >
        mode = #{mode},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="reportRate != null " >
        reportRate = #{reportRate},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
        <if test="city != null and city != ''" >
        city = #{city},
      </if>
        <if test="cityStatus != null and cityStatus != ''" >
        cityStatus = #{cityStatus},
      </if>

        <if test="sfCtr != null" >
            sfCtr = #{sfCtr},
        </if>

        <if test="scit != null" >
            scit = #{scit},
        </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
      <select id="selectApplicationIterationConfig" resultType="com.wbgame.pojo.clean.ApplicationIterationConfig">
    select 
   		*
    from app_iteration_config
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	
	order by modifyTime desc
  </select>
  
   <delete id="deleteApplicationIterationConfig" parameterType="com.wbgame.pojo.clean.ApplicationIterationConfig">
    delete from app_iteration_config
    where id = #{id}
  </delete>
   <insert id="insertApplicationIterationConfig" parameterType="com.wbgame.pojo.clean.ApplicationIterationConfig">
    insert into app_iteration_config (appid, cha, prjid,times,url,`force`,download,createTime,modifyTime, `describe`, createUser,modifyUser,status )
   							 values (#{appid}, #{cha},  #{prjid},#{times} ,#{url}, #{force},#{download},NOW(),NOW(),#{describe},#{createUser},#{modifyUser},#{status} )
  </insert>
   <update id="updateApplicationIterationConfig" parameterType="com.wbgame.pojo.clean.ApplicationIterationConfig" >
    update app_iteration_config
    <set >
      <if test="times != null and times != ''" >
        times = #{times},
      </if>
      <if test="url != null and url != ''" >
        url = #{url},
      </if>
      <if test="force != null and force != ''" >
        `force` = #{force},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="download != null and download != ''" >
        download = #{download},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
        <if test="describe != null and describe != ''" >
        `describe` = #{describe},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
    <select id="selectQupaiAiZone" resultType="com.wbgame.pojo.clean.QupaiAiZone">
    select 
   		*
    from qupai_ai_zone
    where  1=1 
     <if test="aiTitle != null and aiTitle != ''">
		 and  aiTitle like concat('%',#{aiTitle},'%')
	</if>
	<if test="aiType != null and aiType != ''">
		 and  aiType like concat('%',#{aiType},'%')
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	order by modifyTime desc
  </select>
  
   <delete id="deleteQupaiAiZone" parameterType="com.wbgame.pojo.clean.QupaiAiZone">
    delete from qupai_ai_zone
    where id = #{id}
  </delete>
   <insert id="insertQupaiAiZone" parameterType="com.wbgame.pojo.clean.QupaiAiZone">
    insert into qupai_ai_zone (sort,aiTitle,aiImg, aiVideo, isVip, aiType,isHot,
      faceId,mid,createTime,modifyTime, createUser,modifyUser,status,`foreign`,actId
      )
    values (#{sort} ,#{aiTitle},#{aiImg}, #{aiVideo}, #{isVip}, #{aiType},#{isHot},
      #{faceId},#{mid},NOW(),NOW(),#{createUser},#{modifyUser},#{status},#{foreign},#{actId}
      )
  </insert>
   <update id="updateQupaiAiZone" parameterType="com.wbgame.pojo.clean.QupaiAiZone" >
    update qupai_ai_zone
    <set >
        sort = #{sort},
      <if test="aiTitle != null and aiTitle != ''" >
        aiTitle = #{aiTitle},
      </if>
      <if test="aiImg != null and aiImg != ''" >
        aiImg = #{aiImg},
      </if>
       <if test="aiVideo != null and aiVideo != ''" >
        aiVideo = #{aiVideo},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="isVip != null and isVip != ''" >
        isVip = #{isVip},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
        <if test="isHot != null and isHot != ''" >
        isHot = #{isHot},
      </if>
      <if test="faceId != null and faceId != ''" >
        faceId = #{faceId},
      </if>
      <if test="mid != null and mid != ''" >
        mid = #{mid},
      </if>
      <if test="foreign != null and foreign != ''" >
        `foreign` = #{foreign},
      </if>
        aiType = #{aiType},
        actId = #{actId},
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
   <select id="selectQupaiRecommendZone" resultType="com.wbgame.pojo.clean.QupaiRecommendZone">
    select 
   		*
    from qupai_recommend_zone
    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	
	order by modifyTime desc
  </select>
  
   <delete id="deleteQupaiRecommendZone" parameterType="com.wbgame.pojo.clean.QupaiRecommendZone">
    delete from qupai_recommend_zone
    where id = #{id}
  </delete>
   <insert id="insertQupaiRecommendZone" parameterType="com.wbgame.pojo.clean.QupaiRecommendZone">
    insert into qupai_recommend_zone (appid, sort,zoneTitle,
     createTime,modifyTime,createUser,modifyUser,status
      )
    values (#{appid},#{sort} ,#{zoneTitle}, 
     NOW(),NOW(),#{createUser},#{modifyUser},#{status}
      )
  </insert>
   <update id="updateQupaiRecommendZone" parameterType="com.wbgame.pojo.clean.QupaiRecommendZone" >
    update qupai_recommend_zone
    <set >
      <if test="sort != null" >
        sort = #{sort},
      </if>
      <if test="zoneTitle != null and zoneTitle != ''" >
        zoneTitle = #{zoneTitle},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
  <select id="selectQupaiConfigClassify" resultType="com.wbgame.pojo.clean.QupaiConfigClassify">
    select 
   		*
    from qupai_config_classify
    where  1=1 
     <if test="configType != null and configType != ''">
		 and  configType = #{configType,jdbcType=VARCHAR}
	</if>
	 <if test="id != null ">
		 and  id = #{id,jdbcType=VARCHAR}
	</if>
  </select>
  
   <delete id="deleteQupaiConfigClassify" parameterType="com.wbgame.pojo.clean.QupaiConfigClassify">
    delete from qupai_config_classify
    where id = #{id}
  </delete>
   <insert id="insertQupaiConfigClassify" parameterType="com.wbgame.pojo.clean.QupaiConfigClassify">
    insert into qupai_config_classify (configType, configContent
      )
    values (#{configType}, #{configContent}
      )
  </insert>
   <update id="updateQupaiConfigClassify" parameterType="com.wbgame.pojo.clean.QupaiConfigClassify" >
    update qupai_config_classify
    <set >
      <if test="configContent != null and configContent != ''" >
        configContent = #{configContent},
      </if>
      <if test="configType != null and configType != ''" >
        configType = #{configType},
      </if>
    </set>
    where id in (${id})
  </update>
  
  <select id="selectLockTemplateConfig" resultType="com.wbgame.pojo.clean.SuperLockNew">
    select 
   		*
    from lock_template_config
    where  id = #{id} 
  
  </select>
  
    <select id="selectAppSafe" resultType="com.wbgame.pojo.clean.AppSafeConfig">
    select 
   		*
    from super_safe_control
	    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid in (${appid})
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha in (${cha})
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	order by modifyTime desc
  </select>
  
   <delete id="deleteAppSafe" parameterType="com.wbgame.pojo.clean.AppSafeConfig">
    delete from super_safe_control
    where id = #{id}
  </delete>
   <insert id="insertAppSafe" parameterType="com.wbgame.pojo.clean.AppSafeConfig">
     insert into super_safe_control (appid, cha, prjid,safeCtr,exitApp,
      filterPkg,dCtr,createTime,modifyTime, city, cityStatus, createUser,modifyUser,status,lrCtr,reqInv,fastCtr,wifiKey
      )
    values (#{appid}, #{cha},  #{prjid},#{safeCtr} ,#{exitApp}, 
      #{filterPkg},#{dCtr},NOW(),NOW(),#{city},#{cityStatus},#{createUser},#{modifyUser},#{status},#{lrCtr},#{reqInv},
            #{fastCtr},#{wifiKey}
      )
  </insert>
   <update id="updateAppSafe" parameterType="com.wbgame.pojo.clean.AppSafeConfig" >
    update super_safe_control
     <set >
      <if test="safeCtr != null and safeCtr != ''" >
        safeCtr = #{safeCtr},
      </if>
      <if test="exitApp != null and exitApp != ''" >
        exitApp = #{exitApp},
      </if>
      <if test="filterPkg != null and filterPkg != ''" >
        filterPkg = #{filterPkg},
      </if>
       <if test="dCtr != null and dCtr != ''" >
        dCtr = #{dCtr},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="lrCtr != null and lrCtr != '' " >
        lrCtr = #{lrCtr},
      </if>
      <if test="reqInv != null and reqInv != '' " >
        reqInv = #{reqInv},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
        <if test="cityStatus != null and cityStatus != ''" >
        cityStatus = #{cityStatus},
      </if>

     <if test="fastCtr != null and fastCtr != ''" >
         fastCtr = #{fastCtr},
     </if>
     <if test="wifiKey != null and wifiKey != ''" >
         wifiKey = #{wifiKey},
     </if>
      city = #{city},
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
  <select id="selectHaiwaiWifiConfig" resultType="com.wbgame.pojo.clean.wifi.HaiwaiWifiConfig">
    select 
   		*
    from haiwai_wifi_config
	    where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid in (${appid})
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha  in (${cha})
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
  </select>
  
   <delete id="deleteHaiwaiWifiConfig" parameterType="com.wbgame.pojo.clean.wifi.HaiwaiWifiConfig">
    delete from haiwai_wifi_config
    where id = #{id}
  </delete>
   <insert id="insertHaiwaiWifiConfig" parameterType="com.wbgame.pojo.clean.wifi.HaiwaiWifiConfig">
     insert into haiwai_wifi_config (appid, cha, prjid,privateSwitch,ischeck,
      serviceEmail,proAddress,createTime,modifyTime, createUser,modifyUser,status,proVersion,adSw,anim
      )
    values (#{appid}, #{cha},  #{prjid},#{privateSwitch} ,#{ischeck}, 
      #{serviceEmail},#{proAddress},NOW(),NOW(),#{createUser},#{modifyUser},#{status},#{proVersion},#{adSw},#{anim}
      )
  </insert>
   <update id="updateHaiwaiWifiConfig" parameterType="com.wbgame.pojo.clean.wifi.HaiwaiWifiConfig" >
    update haiwai_wifi_config
     <set >
      <if test="privateSwitch != null and privateSwitch != ''" >
        privateSwitch = #{privateSwitch},
      </if>
      <if test="ischeck != null and ischeck != ''" >
        ischeck = #{ischeck},
      </if>
      <if test="serviceEmail != null and serviceEmail != ''" >
        serviceEmail = #{serviceEmail},
      </if>
       <if test="proAddress != null and proAddress != ''" >
        proAddress = #{proAddress},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="proVersion != null and proVersion != '' " >
        proVersion = #{proVersion},
      </if>
       <if test="adSw != null and adSw != '' " >
        adSw = #{adSw},
      </if>
       <if test="anim != null and anim != '' " >
        anim = #{anim},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
  
   <select id="selectQpChannelSort" resultType="com.wbgame.pojo.clean.qp.QpChannelSort">
    select 
   		*
    from qupai_hot_sort
	    where  1=1 
	<if test="cha != null and cha != ''">
		 and  cha = #{cha,jdbcType=VARCHAR}
	</if>
	<if test="tempId != null and tempId != ''">
		 and  tempId = #{tempId,jdbcType=VARCHAR}
	</if>
	<if test="tagId != null and tagId != ''">
		 and  tagId = #{tagId,jdbcType=VARCHAR}
	</if>
  </select>
  
   <delete id="deleteQpChannelSort" parameterType="com.wbgame.pojo.clean.qp.QpChannelSort">
    delete from qupai_hot_sort
    where id = #{id}
  </delete>
   <insert id="insertQpChannelSort" parameterType="com.wbgame.pojo.clean.qp.QpChannelSort">
     insert into qupai_hot_sort (tempId, cha, num,tagId,createTime,modifyTime,createUser,modifyUser)
    					values (#{tempId}, #{cha},  #{num},#{tagId} ,NOW(),NOW(),#{createUser},#{modifyUser})
  </insert>
   <update id="updateQpChannelSort" parameterType="com.wbgame.pojo.clean.qp.QpChannelSort" >
    update qupai_hot_sort
     <set >
      <if test="num != null and num != ''" >
        num = #{num},
      </if>
       <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
  	<select id="selectCoinsWithdrawConfig" resultType="com.wbgame.pojo.clean.coins.CoinsWithdrawConfig">
	    select 
	   		*
	    from xyxtj.coins_withdraw_config
	    where  1=1 
	     <if test="appid != null and appid != ''">
			 and  appid = #{appid,jdbcType=VARCHAR}
		</if>
		<if test="prjid != null and prjid != ''">
			 and  prjid = #{prjid,jdbcType=VARCHAR}
		</if>
		<if test="cha != null and cha != ''">
			 and  cha = #{cha,jdbcType=VARCHAR}
		</if>
		<if test="status != null and status != ''">
			 and  status = #{status,jdbcType=VARCHAR}
		</if>
	</select>
	<delete id="deleteCoinsWithdrawConfig" parameterType="com.wbgame.pojo.clean.coins.CoinsWithdrawConfig">
    delete from xyxtj.coins_withdraw_config
    where id = #{id}
  </delete>
   <insert id="insertCoinsWithdrawConfig" parameterType="com.wbgame.pojo.clean.coins.CoinsWithdrawConfig">
    insert into xyxtj.coins_withdraw_config (appid, cha, prjid, coins,money,
      times, 
      createTime, modifyTime, createUser, 
      modifyUser, status
      )
    values (#{appid,jdbcType=VARCHAR}, #{cha,jdbcType=VARCHAR}, #{prjid,jdbcType=VARCHAR}, #{coins,jdbcType=VARCHAR}, #{money,jdbcType=VARCHAR}, 
      #{times,jdbcType=VARCHAR},
       now(),now(),#{createUser,jdbcType=VARCHAR},
        #{modifyUser,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateCoinsWithdrawConfig" parameterType="com.wbgame.pojo.clean.coins.CoinsWithdrawConfig">
    update xyxtj.coins_withdraw_config
     <set >
      <if test="coins != null and coins != ''" >
        coins = #{coins,jdbcType=VARCHAR},
      </if>
     <if test="money != null and money != ''" >
        money = #{money,jdbcType=VARCHAR},
      </if>
       <if test="times != null and times != ''" >
        times = #{times,jdbcType=VARCHAR},
      </if>
       <if test="status != null and status != ''" >
        status = #{status,jdbcType=VARCHAR},
      </if>
       <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      modifyTime = now()
      </set>
    where id in (${id}) 
  </update>
  
  
	<select id="selectCoinsConfig" resultType="com.wbgame.pojo.clean.coins.CoinsConfig">
	    select 
	   		*
	    from xyxtj.super_coins_cfg
	    where  1=1 
	     <if test="appid != null and appid != ''">
			 and  appid = #{appid,jdbcType=VARCHAR}
		</if>
		<if test="status != null and status != ''">
			 and  status = #{status,jdbcType=VARCHAR}
		</if>
	</select>
	<delete id="deleteCoinsConfig" parameterType="com.wbgame.pojo.clean.coins.CoinsConfig">
    delete from xyxtj.super_coins_cfg
    where appid = #{appid}
  </delete>
   <insert id="insertCoinsConfig" parameterType="com.wbgame.pojo.clean.coins.CoinsConfig">
    insert into xyxtj.super_coins_cfg (appid, newPack, loginDaily, bonus,bubble,stepReward,
      turntable, turntableExtra,ggl,answer,wallpaper,extraBag,openPack,luckyStar,news,video,chat,goldenEgg,watch,
      createTime, modifyTime, createUser, 
      modifyUser, status,welfare
      )
    values (#{appid,jdbcType=VARCHAR}, #{newPack,jdbcType=VARCHAR}, #{loginDaily,jdbcType=VARCHAR}, #{bonus,jdbcType=VARCHAR}, #{bubble,jdbcType=VARCHAR}, #{stepReward,jdbcType=VARCHAR}, 
      #{turntable,jdbcType=VARCHAR},#{turntableExtra,jdbcType=VARCHAR},#{ggl,jdbcType=VARCHAR},#{answer,jdbcType=VARCHAR},#{wallpaper,jdbcType=VARCHAR},
      #{extraBag,jdbcType=VARCHAR},#{openPack,jdbcType=VARCHAR},#{luckyStar,jdbcType=VARCHAR},#{news,jdbcType=VARCHAR},#{video,jdbcType=VARCHAR},#{chat,jdbcType=VARCHAR},#{goldenEgg,jdbcType=VARCHAR},#{watch,jdbcType=VARCHAR},
       now(),now(),#{createUser,jdbcType=VARCHAR},
        #{modifyUser,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{welfare,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateCoinsConfig" parameterType="com.wbgame.pojo.clean.coins.CoinsConfig">
    update xyxtj.super_coins_cfg
     <set >
      <if test="newPack != null and newPack != ''" >
        newPack = #{newPack,jdbcType=VARCHAR},
      </if>
      <if test="loginDaily != null and loginDaily != ''" >
        loginDaily = #{loginDaily,jdbcType=VARCHAR},
      </if>
       <if test="bonus != null and bonus != ''" >
        bonus = #{bonus,jdbcType=VARCHAR},
      </if>
       <if test="bubble != null and bubble != ''" >
        bubble = #{bubble,jdbcType=VARCHAR},
      </if>
      <if test="turntable != null and turntable != ''" >
        turntable = #{turntable,jdbcType=VARCHAR},
      </if>
      <if test="turntableExtra != null and turntableExtra != ''" >
        turntableExtra = #{turntableExtra,jdbcType=VARCHAR},
      </if>
       <if test="ggl != null and ggl != ''" >
        ggl = #{ggl,jdbcType=VARCHAR},
      </if>
       <if test="answer != null and answer != ''" >
        answer = #{answer,jdbcType=VARCHAR},
      </if>
      <if test="wallpaper != null and wallpaper != ''" >
        wallpaper = #{wallpaper,jdbcType=VARCHAR},
      </if>
      <if test="extraBag != null and extraBag != ''" >
        extraBag = #{extraBag,jdbcType=VARCHAR},
      </if>
       <if test="openPack != null and openPack != ''" >
        openPack = #{openPack,jdbcType=VARCHAR},
      </if>
       <if test="luckyStar != null and luckyStar != ''" >
        luckyStar = #{luckyStar,jdbcType=VARCHAR},
      </if>
      <if test="news != null and news != ''" >
        news = #{news,jdbcType=VARCHAR},
      </if>
      <if test="video != null and video != ''" >
        video = #{video,jdbcType=VARCHAR},
      </if>
       <if test="chat != null and chat != ''" >
        chat = #{chat,jdbcType=VARCHAR},
      </if>
       <if test="goldenEgg != null and goldenEgg != ''" >
        goldenEgg = #{goldenEgg,jdbcType=VARCHAR},
      </if>
       <if test="watch != null and watch != ''" >
        watch = #{watch,jdbcType=VARCHAR},
      </if>
      <if test="welfare != null and welfare != ''" >
        welfare = #{welfare,jdbcType=VARCHAR},
      </if>
       <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      modifyTime = now()
      </set>
    where appid  = #{appid,jdbcType=VARCHAR}
  </update>
  
  <select id="selectSuperLockLotte" resultType="com.wbgame.pojo.clean.SuperLockLotte">
    select 
   		*
    from super_lock_lotte
	    where  1=1 
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="lotteName != null and lotteName != ''">
		 and  lotteName = #{lotteName,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	order by modifyTime desc
  </select>
  
   <delete id="deleteSuperLockLotte" parameterType="com.wbgame.pojo.clean.SuperLockLotte">
    delete from super_lock_lotte
    where id = #{id}
  </delete>
   <insert id="insertSuperLockLotte" parameterType="com.wbgame.pojo.clean.SuperLockLotte">
     insert into super_lock_lotte ( prjid,lotteName,lotteUrl,
      deepLinkUrl,openDelay,createTime,modifyTime, createUser,modifyUser,status
      )
    values (#{prjid},#{lotteName} ,#{lotteUrl}, 
      #{deepLinkUrl},#{openDelay},NOW(),NOW(),#{createUser},#{modifyUser},#{status}
      )
  </insert>
   <update id="updateSuperLockLotte" parameterType="com.wbgame.pojo.clean.SuperLockLotte" >
    update super_lock_lotte
     <set >
      <if test="prjid != null and prjid != ''" >
        prjid = #{prjid},
      </if>
      <if test="lotteUrl != null and lotteUrl != ''" >
        lotteUrl = #{lotteUrl},
      </if>
      <if test="deepLinkUrl != null and deepLinkUrl != ''" >
        deepLinkUrl = #{deepLinkUrl},
      </if>
       <if test="openDelay != null and openDelay != ''" >
        openDelay = #{openDelay},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
    <select id="selectFaceClassConfig" resultType="com.wbgame.pojo.clean.face.FaceClassConfig">
    select 
   		a.*,b.areaName
    from face_class_config a LEFT JOIN face_area_config b on a.areaId =b.id
	    where  1=1 
	<if test="areaId != null and areaId != ''">
		 and  areaId = #{areaId,jdbcType=VARCHAR}
	</if>
	<if test="className != null and className != ''">
		 and  className = #{className,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  a.status = #{status,jdbcType=VARCHAR}
	</if>
	order by sort asc, modifyTime desc
  </select>
  
   <delete id="deleteFaceClassConfig" parameterType="com.wbgame.pojo.clean.face.FaceClassConfig">
    delete from face_class_config
    where id = #{id}
  </delete>
   <insert id="insertFaceClassConfig" parameterType="com.wbgame.pojo.clean.face.FaceClassConfig">
     insert into face_class_config ( className,areaId,sort,
     createTime,modifyTime, createUser,modifyUser,status
      )
    values (#{className},#{areaId} ,#{sort}, 
     NOW(),NOW(),#{createUser},#{modifyUser},#{status}
      )
  </insert>
   <update id="updateFaceClassConfig" parameterType="com.wbgame.pojo.clean.face.FaceClassConfig" >
    update face_class_config
     <set >
      <if test="className != null and className != ''" >
        className = #{className},
      </if>
      <if test="areaId != null " >
        areaId = #{areaId},
      </if>
      <if test="sort != null " >
        sort = #{sort},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
  
  <select id="selectFaceAreaConfig" resultType="com.wbgame.pojo.clean.face.FaceAreaConfig">
    select 
   		*
    from face_area_config
	    where  1=1 
	<if test="areaName != null and areaName != ''">
		 and  areaName = #{areaName,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	order by modifyTime desc
  </select>
  
   <delete id="deleteFaceAreaConfig" parameterType="com.wbgame.pojo.clean.face.FaceAreaConfig">
    delete from face_area_config
    where id = #{id}
  </delete>
   <insert id="insertFaceAreaConfig" parameterType="com.wbgame.pojo.clean.face.FaceAreaConfig">
     insert into face_area_config ( areaName,descTxt,area,
     createTime,modifyTime, createUser,modifyUser,status
      )
    values (#{areaName},#{descTxt} ,#{area}, 
     NOW(),NOW(),#{createUser},#{modifyUser},#{status}
      )
  </insert>
   <update id="updateFaceAreaConfig" parameterType="com.wbgame.pojo.clean.face.FaceAreaConfig" >
    update face_area_config
     <set >
      <if test="areaName != null and areaName != ''" >
        areaName = #{areaName},
      </if>
       <if test="descTxt != null and descTxt != ''" >
        descTxt = #{descTxt},
      </if>
       <if test="area != null and area != ''" >
        area = #{area},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
  <select id="selectFaceModelConfig" resultType="com.wbgame.pojo.clean.face.FaceModelConfig">
      select * from (

          SELECT
          <if test="areaId != null">
              mcc.areaId,
          </if>
          <if test="classId != null">
              mcc.classId,
          </if>
          m.*,
          mc.*,
          concat( TRUNCATE( mc.tplConvertVipCount / mc.makeTemplateCount * 100, 2 ), "%" ) conversionRate

          FROM
          face_model_config m
          LEFT JOIN face_model_count mc ON m.id = mc.modelId
          LEFT JOIN (
              select distinct
                  <if test="areaId != null">
                      areaId,
                  </if>
                  <if test="classId != null">
                      classId,
                  </if>
                   modelId from face_model_class_config

              ) mcc ON m.id = mcc.modelId
          WHERE
            1 =1
          <if test="tplConvertVipCount > 0">
              and mc.tplConvertVipCount &lt; ${tplConvertVipCount}
          </if>
      ) a

      WHERE
      1 =1
    <if test="tempTitle != null and tempTitle != ''">
		 and  tempTitle like "%" #{tempTitle,jdbcType=VARCHAR} "%"
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
	<if test="state != null and state != ''">
		 and  state = #{state,jdbcType=VARCHAR}
	</if>
	<if test="tempType != null and tempType != ''">
		 and  tempType = #{tempType,jdbcType=VARCHAR}
	</if>

    <if test="classId != null and classId != ''">
     and  classId = #{classId,jdbcType=VARCHAR}
    </if>

      <if test="areaId != null and areaId != ''">
          and  areaId = #{areaId}
      </if>
<!--	<if test="status != null and status != ''">-->
<!--		 and  status = #{status,jdbcType=VARCHAR}-->
<!--	</if>-->
      <if test="id != null and id != ''">
          and  id = #{id,jdbcType=VARCHAR}
      </if>
      <if test="mid != null and mid != ''">
          and  mid = #{mid,jdbcType=VARCHAR}
      </if>

      <if test="createTime != null">
          and  createTime &lt; #{createTime}
      </if>

	order by id desc, modifyTime desc
  </select>
  
   <select id="selectFaceModelSortConfig" resultType="com.wbgame.pojo.clean.face.FaceModelConfig">
       SELECT
       *
       FROM
       (
       SELECT
       a.classId,
       a.areaId,
       a.modelId,
       a.typeSort,
       a.typeModifyTime,
       DATEDIFF(CURRENT_DATE, b.createTime) upTime,
       b.*,
       c.makeTemplateCount,
       c.tplConvertVipCount,
       concat(truncate(c.tplConvertVipCount / c.makeTemplateCount * 100, 2), "%") conversionRate
       FROM
       `face_model_class_config` a
       LEFT JOIN face_model_config b ON a.modelId = b.id
       LEFT JOIN face_model_count c ON a.modelId = c.modelId
       ) c
       WHERE
       c.id != ""
       AND c.state = 1
	<if test="classId!= null ">
		 and  c.classId = #{classId,jdbcType=VARCHAR}
	</if>

       <if test="tempTitle!= null and tempTitle != ''">
           and  tempTitle = #{tempTitle}
       </if>
       <if test="templateFaceID!= null and templateFaceID != ''">
           and  templateFaceID = #{templateFaceID}
       </if>
       <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
           and  createTime between #{start_date} and concat(#{end_date}, ' 23:59:59')
       </if>
	order by  typeSort asc,typeModifyTime desc
  </select>
  
   <delete id="deleteFaceModelConfig" parameterType="com.wbgame.pojo.clean.face.FaceModelConfig">
    delete from face_model_config
    where id = #{id}
  </delete>
   <insert id="insertFaceModelConfig" parameterType="com.wbgame.pojo.clean.face.FaceModelConfig">
     insert into face_model_config ( mid,tempTitle,tempUrl,videoUrl,tempType,state,isTop,videoHeight,videoWidth,projectId,templateFaceID,taskTime,
     createTime,modifyTime, createUser,modifyUser,status,region
      )
    values (#{mid},#{tempTitle} ,#{tempUrl}, #{videoUrl},#{tempType} ,#{state}, #{isTop},#{videoHeight} ,#{videoWidth},#{projectId} ,#{templateFaceID}, #{taskTime}, 
     NOW(),NOW(),#{createUser},#{modifyUser},#{status},#{region}
      )
  </insert>
   <update id="updateFaceModelConfig" parameterType="com.wbgame.pojo.clean.face.FaceModelConfig" >
    update face_model_config
     <set >
      <if test="mid != null and mid != ''" >
        mid = #{mid},
      </if>
       <if test="tempTitle != null and tempTitle != ''" >
        tempTitle = #{tempTitle},
      </if>
       <if test="tempUrl != null and tempUrl != ''" >
        tempUrl = #{tempUrl},
      </if>
      <if test="videoUrl != null and videoUrl != ''" >
        videoUrl = #{videoUrl},
      </if>
       <if test="tempType != null and tempType != ''" >
        tempType = #{tempType},
      </if>
      <if test="taskTime != null and taskTime != ''" >
        taskTime = #{taskTime},
      </if>
        <if test="templateFaceID != null and templateFaceID != ''" >
        templateFaceID = #{templateFaceID},
      </if>
       <if test="state != null and state != ''" >
        state = #{state},
      </if>
      <if test="isTop != null " >
        isTop = #{isTop},
      </if>
       <if test="sort != null " >
        sort = #{sort},
      </if>
       <if test="videoHeight != null and videoHeight != ''" >
        videoHeight = #{videoHeight},
      </if>
      <if test="videoWidth != null and videoWidth != ''" >
        videoWidth = #{videoWidth},
      </if>
      <if test="projectId != null and projectId != ''" >
        projectId = #{projectId},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
      <if test="region != null" >
          region = #{region},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
   <update id="updateFaceModelConfigSort" parameterType="com.wbgame.pojo.clean.face.FaceModelConfig" >
    update face_model_class_config
    set
      typeModifyTime = NOW(),
      typeSort = #{sort}
    where modelId = #{id}  and classId = #{classId}
  </update>
  
  <update id="updateFaceModelConfigTop" parameterType="com.wbgame.pojo.clean.face.FaceModelConfig" >
    update face_model_class_config
    set
      typeSort = 0,
      typeModifyTime = NOW()
    where  modelId = #{id}  and classId = #{classId}
  </update>
  
  <delete id="deleteModelClassConfigbyClassId" parameterType="com.wbgame.pojo.clean.face.ModelClassConfig">
    delete from face_model_class_config
    where modelId = #{modelId} and classId = #{classId}
  </delete>
  
   <select id="selectModelClassConfig" resultType="com.wbgame.pojo.clean.face.ModelClassConfig">
    select 
   		*
    from face_model_class_config
	    where   modelId = #{modelId}
  </select>
   <delete id="deleteModelClassConfig" parameterType="com.wbgame.pojo.clean.face.ModelClassConfig">
    delete from face_model_class_config
    where modelId = #{modelId}
  </delete>
   <insert id="insertModelClassConfigList" parameterType="com.wbgame.pojo.clean.face.ModelClassConfig">
     insert into face_model_class_config ( classId,areaId,modelId,typeSort,typeModifyTime)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.classId,jdbcType=VARCHAR}, #{li.areaId,jdbcType=VARCHAR},#{li.modelId,jdbcType=VARCHAR},0,NOW() )
        </foreach>
  </insert>
  
  <select id="selectFaceProductConfig" resultType="com.wbgame.pojo.clean.face.FaceProductConfig">
    select 
   		*
    from face_product_config
	    where  1=1 
	<if test="productName != null and productName != ''">
		 and  productName like #{productName} "%"
	</if>
	<if test="cha != null and cha != ''">
		 and  cha = #{cha}
	</if>
	<if test="period != null and period != ''">
		 and  period = #{period}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status}
	</if>
      <if test="id != null and id != ''">
          and  id = #{id}
      </if>
      <if test="platformProductId != null and platformProductId != ''">
          and  platformProductId = #{platformProductId}
      </if>
	order by sort , modifyTime desc
  </select>
  
   <delete id="deleteFaceProductConfig" parameterType="com.wbgame.pojo.clean.face.FaceProductConfig">
    delete from face_product_config
    where id = #{id}
  </delete>
   <insert id="insertFaceProductConfig" parameterType="com.wbgame.pojo.clean.face.FaceProductConfig">
     insert into face_product_config ( cha,productName,productPrice, period,feeDay,platformProductId,moneyType,productDescribe,
     createTime,modifyTime, createUser,modifyUser,status
      )
    values (#{cha},#{productName} ,#{productPrice}, #{period},#{feeDay} ,#{platformProductId}, #{moneyType}, #{productDescribe}, 
     NOW(),NOW(),#{createUser},#{modifyUser},#{status}
      )
  </insert>
   <update id="updateFaceProductConfig" parameterType="com.wbgame.pojo.clean.face.FaceProductConfig" >
    update face_product_config
     <set >
      <if test="cha != null and cha != ''" >
        cha = #{cha},
      </if>
       <if test="productName != null and productName != ''" >
        productName = #{productName},
      </if>
      <if test="productDescribe != null and productDescribe != ''" >
        productDescribe = #{productDescribe},
      </if>
       <if test="productPrice != null and productPrice != ''" >
        productPrice = #{productPrice},
      </if>
      <if test="period != null and period != ''" >
        period = #{period},
      </if>
      <if test="feeDay != null and feeDay != ''" >
        feeDay = #{feeDay},
      </if>
      <if test="platformProductId != null and platformProductId != ''" >
        platformProductId = #{platformProductId},
      </if>
        <if test="moneyType != null and moneyType != ''" >
        moneyType = #{moneyType},
      </if>
       <if test="sort != null " >
        sort = #{sort},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
  <select id="selectFaceSwitchConfig" resultType="com.wbgame.pojo.clean.face.FaceSwitchConfig">
    select 
   		*
    from face_switch_config
  </select>
  
   <delete id="deleteFaceSwitchConfig" parameterType="com.wbgame.pojo.clean.face.FaceSwitchConfig">
    delete from face_switch_config
    where id = #{id}
  </delete>
   <insert id="insertFaceSwitchConfig" parameterType="com.wbgame.pojo.clean.face.FaceSwitchConfig">
     insert into face_switch_config ( fristProductionPic,faceCartoonPic,changeAgeOldPic, changeAgeYongPic,subscriptionPage,swapGenderPic,guidePage,projectId,modelId,templateFaceID,tempUrl,bynouOffOn,homeADSwitch,splashADSwitch,onlyfnoushowOnffOn
     createTime,modifyTime, createUser,modifyUser,status
      )
    values (#{fristProductionPic},#{faceCartoonPic} ,#{changeAgeOldPic}, #{changeAgeYongPic},#{subscriptionPage} ,#{swapGenderPic}, #{guidePage}, #{projectId} ,#{modelId}, #{templateFaceID},#{tempUrl},#{bynouOffOn},#{homeADSwitch},#{splashADSwitch},#{onlyfnoushowOnffOn},
     NOW(),NOW(),#{createUser},#{modifyUser},#{status}
      )
  </insert>
   <update id="updateFaceSwitchConfig" parameterType="com.wbgame.pojo.clean.face.FaceSwitchConfig" >
    update face_switch_config
     <set >
      <if test="fristProductionPic != null and fristProductionPic != ''" >
        fristProductionPic = #{fristProductionPic},
      </if>
       <if test="faceCartoonPic != null and faceCartoonPic != '' " >
        faceCartoonPic = #{faceCartoonPic},
      </if>
       <if test="changeAgeOldPic != null and changeAgeOldPic != '' " >
        changeAgeOldPic = #{changeAgeOldPic},
      </if>
      <if test="changeAgeYongPic != null and changeAgeYongPic != '' " >
        changeAgeYongPic = #{changeAgeYongPic},
      </if>
      <if test="swapGenderPic != null and swapGenderPic != ''" >
        swapGenderPic = #{swapGenderPic},
      </if>
      <if test="subscriptionPage != null and subscriptionPage != ''" >
        subscriptionPage = #{subscriptionPage},
      </if>
        <if test="guidePage != null and guidePage != ''" >
        guidePage = #{guidePage},
      </if>
 	  <if test="modelId != null and modelId != ''" >
        modelId = #{modelId},
      </if>
      <if test="projectId != null and projectId != ''" >
        projectId = #{projectId},
      </if>
      <if test="templateFaceID != null and templateFaceID != ''" >
        templateFaceID = #{templateFaceID},
      </if>
       <if test="tempUrl != null and tempUrl != ''" >
        tempUrl = #{tempUrl},
      </if>
       <if test="bynouOffOn != null and bynouOffOn != ''" >
        bynouOffOn = #{bynouOffOn},
      </if>
       <if test="homeADSwitch != null and homeADSwitch != ''" >
        homeADSwitch = #{homeADSwitch},
      </if>
       <if test="splashADSwitch != null and splashADSwitch != ''" >
        splashADSwitch = #{splashADSwitch},
      </if>
        <if test="onlyfnoushowOnffOn != null and onlyfnoushowOnffOn != ''" >
        onlyfnoushowOnffOn = #{onlyfnoushowOnffOn},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
     <if test="bynoubsOffOn != null and bynoubsOffOn != ''" >
         bynoubsOffOn = #{bynoubsOffOn},
     </if>
     <if test="timeCameraOffOn != null" >
         timeCameraOffOn = #{timeCameraOffOn},
     </if>
     <if test="showPayGuideLoc != null" >
         showPayGuideLoc = #{showPayGuideLoc},
     </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
 
  <select id="selectSuperUseLevel" resultType="com.wbgame.pojo.clean.SuperUseLevel">
    select 
   		*
    from super_user_level
      where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid in (${appid})
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha in (${cha})
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
  </select>
  
   <delete id="deleteSuperUseLevel" parameterType="com.wbgame.pojo.clean.SuperUseLevel">
    delete from super_user_level
    where id = #{id}
  </delete>
   <insert id="insertSuperUseLevel" parameterType="com.wbgame.pojo.clean.SuperUseLevel">
     insert into super_user_level ( appid,cha,prjid, levelA,levelB,levelC,adType,adTime,
     createTime,modifyTime, createUser,modifyUser,status
      )
    values (#{appid},#{cha} ,#{prjid}, #{levelA},#{levelB} ,#{levelC}, #{adType}, #{adTime} ,
     NOW(),NOW(),#{createUser},#{modifyUser},#{status}
      )
  </insert>
   <update id="updateSuperUseLevel" parameterType="com.wbgame.pojo.clean.SuperUseLevel" >
    update super_user_level
     <set >
     <if test="cha != null " >
         cha = #{cha},
     </if>
      <if test="levelA != null and levelA != ''" >
        levelA = #{levelA},
      </if>
       <if test="levelB != null and levelB != '' " >
        levelB = #{levelB},
      </if>
       <if test="levelC != null and levelC != '' " >
        levelC = #{levelC},
      </if>
      <if test="adType != null and adType != ''" >
        adType = #{adType},
      </if>
      <if test="adTime != null" >
        adTime = #{adTime},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
     <if test="prjid != null and prjid != ''" >
         prjid = #{prjid},
     </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
 
  <select id="selectSuperConfigFloat" resultType="com.wbgame.pojo.clean.SuperConfigFloat">
    select 
   		*
    from super_config_float
      where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid in (${appid})
	</if>
	<if test="prjid != null and prjid != ''">
		 and  prjid = #{prjid,jdbcType=VARCHAR}
	</if>
	<if test="cha != null and cha != ''">
		 and  cha in (${cha})
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
  </select>
  
   <delete id="deleteSuperConfigFloat" parameterType="com.wbgame.pojo.clean.SuperConfigFloat">
    delete from super_config_float
    where id = #{id}
  </delete>
   <insert id="insertSuperConfigFloat" parameterType="com.wbgame.pojo.clean.SuperConfigFloat">
     insert into super_config_float ( appid,cha,prjid, timeInv,animDelay,minToggleInv,fas,popStyle,styles,
     createTime,modifyTime, createUser,modifyUser,status
      )
    values (#{appid},#{cha} ,#{prjid}, #{timeInv},#{animDelay} ,#{minToggleInv}, #{fas}, #{popStyle} ,#{styles} ,
     NOW(),NOW(),#{createUser},#{modifyUser},#{status}
      )
  </insert>
   <update id="updateSuperConfigFloat" parameterType="com.wbgame.pojo.clean.SuperConfigFloat" >
    update super_config_float
     <set >
      <if test="timeInv != null and timeInv != ''" >
        timeInv = #{timeInv},
      </if>
       <if test="animDelay != null and animDelay != '' " >
        animDelay = #{animDelay},
      </if>
       <if test="minToggleInv != null and minToggleInv != '' " >
        minToggleInv = #{minToggleInv},
      </if>
      <if test="styles != null and styles != ''" >
        styles = #{styles},
      </if>
      <if test="popStyle != null and popStyle != ''" >
        popStyle = #{popStyle},
      </if>
       <if test="fas != null and fas != ''" >
        fas = #{fas},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>
  
  
   <select id="selectSuperConfigNewV2" resultType="com.wbgame.pojo.clean.SuperConfigNewV2">
    select 
   		*
    from super_config_news_v2
      where  1=1 
     <if test="appid != null and appid != ''">
		 and  appid in (${appid})
	</if>
	 <if test="keyCode != null and keyCode != ''">
		 and  keyCode = #{keyCode,jdbcType=VARCHAR}
	</if>
	<if test="status != null and status != ''">
		 and  status = #{status,jdbcType=VARCHAR}
	</if>
  </select>
  
   <delete id="deleteSuperConfigNewV2" parameterType="com.wbgame.pojo.clean.SuperConfigNewV2">
    delete from super_config_news_v2
    where id = #{id}
  </delete>
   <insert id="insertSuperConfigNewV2" parameterType="com.wbgame.pojo.clean.SuperConfigNewV2">
     insert into super_config_news_v2 ( appid,keyCode,
     createTime,modifyTime, createUser,modifyUser,status,contentId
      )
    values (#{appid},#{keyCode} ,
     NOW(),NOW(),#{createUser},#{modifyUser},#{status},#{contentId}
      )
  </insert>
   <update id="updateSuperConfigNewV2" parameterType="com.wbgame.pojo.clean.SuperConfigNewV2" >
    update super_config_news_v2
     <set >
      <if test="keyCode != null and keyCode != ''" >
        keyCode = #{keyCode},
      </if>
      <if test="status != null and status != ''" >
        status = #{status},
      </if>
      <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser},
      </if>
      <if test="contentId != null and contentId != ''" >
          contentId = #{contentId},
      </if>
      modifyTime = NOW()
    </set>
    where id in (${id})
  </update>

<!--    新增超前配置信息-->
    <insert id="insertSuperAdvanceConfig" parameterType="com.wbgame.pojo.clean.SuperAdvanceConfig"
    keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into super_advance_config
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="appid != null" >
                appid,
            </if>
            <if test="cha != null" >
                cha,
            </if>
            <if test="prjid != null" >
                prjid,
            </if>

            <if test="showPrivacy != null" >
                showPrivacy,
            </if>
            <if test="showWallpaper != null" >
                showWallpaper,
            </if>

            <if test="modifyUser != null" >
                modifyUser,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="id != null" >
                id,
            </if>
            <if test="createUser != null" >
                createUser,
            </if>
            <if test="showSplash != null" >
                showSplash,
            </if>
            <if test="showPermission != null" >
                showPermission,
            </if>
            <if test="audit != null" >
                audit,
            </if>
            <if test="noActiveMode != null" >
                noActiveMode,
            </if>
            <if test="cpsEnable != null" >
                cpsEnable,
            </if>
            <if test="wallpaperChannel != null" >
                wallpaperChannel,
            </if>
            <if test="isoCodeShield != null" >
                isoCodeShield,
            </if>
            createTime,
            modifyTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="appid != null" >
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="cha != null" >
                #{cha,jdbcType=VARCHAR},
            </if>
            <if test="prjid != null" >
                #{prjid,jdbcType=VARCHAR},
            </if>

            <if test="showPrivacy != null" >
                #{showPrivacy,jdbcType=VARCHAR},
            </if>
            <if test="showWallpaper != null" >
                #{showWallpaper,jdbcType=VARCHAR},
            </if>

            <if test="modifyUser != null" >
                #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="createUser != null" >
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="showSplash != null" >
                #{showSplash,jdbcType=VARCHAR},
            </if>
            <if test="showPermission != null" >
                #{showPermission,jdbcType=VARCHAR},
            </if>
            <if test="audit != null" >
                #{audit,jdbcType=VARCHAR},
            </if>
            <if test="noActiveMode != null" >
                #{noActiveMode},
            </if>
            <if test="cpsEnable != null" >
                #{cpsEnable},
            </if>
            <if test="wallpaperChannel != null" >
                #{wallpaperChannel},
            </if>
            <if test="isoCodeShield != null" >
                #{isoCodeShield},
            </if>
            now(),
            now()
        </trim>
    </insert>


    <update id="updateSuperAdvanceConfig" parameterType="com.wbgame.pojo.clean.SuperAdvanceConfig" >
        update super_advance_config
        <set >
            <if test="appid != null" >
                appid = #{appid,jdbcType=VARCHAR},
            </if>
            <if test="cha != null" >
                cha = #{cha,jdbcType=VARCHAR},
            </if>
            <if test="prjid != null" >
                prjid = #{prjid,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                createTime = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="showPrivacy != null" >
                showPrivacy = #{showPrivacy,jdbcType=VARCHAR},
            </if>
            <if test="showWallpaper != null" >
                showWallpaper = #{showWallpaper,jdbcType=VARCHAR},
            </if>

            <if test="modifyUser != null" >
                modifyUser = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="id != null" >
                id = #{id,jdbcType=INTEGER},
            </if>
            <if test="createUser != null" >
                createUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="showSplash != null" >
                showSplash = #{showSplash,jdbcType=VARCHAR},
            </if>
            <if test="showPermission != null" >
                showPermission = #{showPermission,jdbcType=VARCHAR},
            </if>
            <if test="audit != null" >
                audit = #{audit},
            </if>
            <if test="noActiveMode != null" >
                noActiveMode = #{noActiveMode},
            </if>
            <if test="cpsEnable != null" >
                cpsEnable = #{cpsEnable},
            </if>
            <if test="wallpaperChannel != null" >
                wallpaperChannel = #{wallpaperChannel},
            </if>
            <if test="isoCodeShield != null" >
                isoCodeShield = #{isoCodeShield},
            </if>
            modifyTime = now()
        </set>

        where id = #{id}
    </update>

    <select id="selectSuperAdvanceConfig" resultType="com.wbgame.pojo.clean.SuperAdvanceConfigVO">

        select * from super_advance_config
        <where>

            <if test="status != null and status != ''">
                and status = #{status}
            </if>

            <if test="appids != null ">
                and appid in
                <foreach collection="appids" item="appid" open="(" close=")" separator="," >

                    #{appid}
                </foreach>
            </if>

            <if test="chas != null ">
                and cha in
                <foreach collection="chas" item="cha" open="(" close=")" separator="," >

                    #{cha}
                </foreach>
            </if>

            <if test="prjids != null ">
                and prjid in
                <foreach collection="prjids" item="prjid" open="(" close=")" separator="," >

                    #{prjid}
                </foreach>
            </if>


        </where>
        order by modifyTime desc
    </select>

    <update id="batchEditSuperAdvanceConfig" >

        update super_advance_config
        <set>
            <if test="config.status !=null and config.status != ''">
                status = #{config.status},
            </if>
            <if test="config.showPrivacy !=null and config.showPrivacy != ''">
                showPrivacy = #{config.showPrivacy},
            </if>
            <if test="config.showWallpaper !=null and config.showWallpaper != ''">
                showWallpaper = #{config.showWallpaper},
            </if>
            <if test="config.showSplash !=null and config.showSplash != ''">
                showSplash = #{config.showSplash},
            </if>
            <if test="config.showPermission !=null and config.showPermission != ''">
                showPermission = #{config.showPermission},
            </if>
            <if test="config.audit !=null and config.audit != ''">
                audit = #{config.audit},
            </if>
            <if test="config.noActiveMode !=null and config.noActiveMode != ''">
                noActiveMode = #{config.noActiveMode},
            </if>
            <if test="config.cpsEnable !=null and config.cpsEnable != ''">
                cpsEnable = #{config.cpsEnable},
            </if>
            <if test="config.wallpaperChannel !=null">
                wallpaperChannel = #{config.wallpaperChannel},
            </if>
            <if test="config.isoCodeShield !=null">
                isoCodeShield = #{config.isoCodeShield},
            </if>
            modifyTime = now(),
            modifyUser = #{config.modifyUser}

        </set>

        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="selectSuperUseLevelByIds" parameterType="java.lang.String" resultType="com.wbgame.pojo.clean.SuperUseLevel">

        select id, appid, prjid from super_user_level where id in (${id})
    </select>


    <delete id="deleteFilterListById" parameterType="java.lang.Long" >
        delete from dnwx_filter_list
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <resultMap id="dnwxFilterListMap" type="com.wbgame.pojo.clean.DnwxFilterListVO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="appid" property="appid" jdbcType="INTEGER" />
        <result column="topic" property="topic" jdbcType="VARCHAR" />
        <result column="prjid" property="prjid" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    </resultMap>
    <insert id="insertFilterList" parameterType="com.wbgame.pojo.clean.DnwxFilterList" >
        insert into dnwx_filter_list
        <trim prefix="(" suffix=")" suffixOverrides="," >

            <if test="appid != null" >
                appid,
            </if>
            <if test="topic != null and topic != null" >
                topic,
            </if>
            <if test="prjid != null and prjid != null" >
                prjid,
            </if>

            <if test="createUser != null and createUser != null" >
                create_user,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >

            <if test="appid != null" >
                #{appid,jdbcType=INTEGER},
            </if>
            <if test="topic != null and topic != null" >
                #{topic,jdbcType=VARCHAR},
            </if>
            <if test="prjid != null and prjid != null" >
                #{prjid,jdbcType=VARCHAR},
            </if>

            <if test="createUser != null and createUser != null" >
                #{createUser,jdbcType=VARCHAR},
            </if>

        </trim>
    </insert>

    <select id="selectFilterList" parameterType="com.wbgame.pojo.clean.DnwxFilterListQuery"
            resultMap="dnwxFilterListMap">

        select id, appid, topic, prjid, create_user, create_time from dnwx_filter_list
        <where>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>

            <if test="topic != null and topic != ''">
                and topic like #{topic} "%"
            </if>
            <if test="prjid != null and prjid != ''">
                and prjid like #{prjid} "%"
            </if>
        </where>
        order by  id desc
    </select>

    <insert id="batchClassArea" parameterType="com.wbgame.pojo.clean.face.FaceModelConfig">

        <foreach collection="list" item="data" separator=";">

            insert into face_model_class_config(classId,
            areaId,
            modelId,
            typeModifyTime)
            values (#{data.classId},
            #{data.areaId},
            #{data.id},
            now())
        </foreach>

    </insert>

    <delete id="deleteFaceModelConfigBatch" parameterType="java.lang.String">

        delete from face_model_class_config
        where modelId IN
        <foreach collection="list" item="mid" open="(" separator="," close=")">
            #{mid}
        </foreach>
    </delete>

    <select id="selectSuperLockNewExist" resultType="java.lang.Integer">

        select 1 FROM ${table}
        where appid = #{param.appid}
        <if test="param.prjid != null and param.prjid != ''">
            and prjid = #{param.prjid}
        </if>
        <if test="param.cha != null and param.cha != ''">
            and cha = #{param.cha}
        </if>
        <if test="param.buy_act != null and param.buy_act != ''">
            and buy_act = #{param.buy_act}
        </if>
        <if test="param.buy_id != null and param.buy_id != ''">
            and buy_id = #{param.buy_id}
        </if>
        limit 1
    </select>

    <insert id="batchInsertCopyFaceModel" parameterType="com.wbgame.pojo.clean.face.FaceModelConfig">

        <foreach collection="list" item="data" separator=";">

            insert into face_model_config ( mid,tempTitle,tempUrl,videoUrl,tempType,state,isTop,videoHeight,videoWidth,projectId,templateFaceID,taskTime,
            createTime,modifyTime, createUser,modifyUser,status,region
            )
            values (#{data.mid},#{data.tempTitle} ,#{data.tempUrl}, #{data.videoUrl},#{data.tempType} ,#{data.state}, #{data.isTop},#{data.videoHeight} ,#{data.videoWidth},#{data.projectId} ,#{data.templateFaceID}, #{data.taskTime},
            NOW(),NOW(),#{data.createUser},#{data.modifyUser},#{data.status},#{data.region}
            )
        </foreach>

    </insert>

    <select id="selectByModelNameList"
            parameterType="com.wbgame.pojo.clean.face.FaceModelConfig" resultType="com.wbgame.pojo.clean.face.FaceModelConfig">


        <if test="list != null and list.size > 0">
            select * from face_model_config where
            tempTitle in
            <foreach collection="list" item="temName" separator="," open="(" close=")">
                #{temName.tempTitle}
            </foreach>
        </if>
    </select>

    <update id="batchUpdateFaceModelToOffShelve" parameterType="java.util.List">
        <foreach collection="midList" item="mid" separator=";">
            update face_model_config
            <set>
                state = 2,
                modifyUser = #{loginUserName,jdbcType=VARCHAR},
                modifyTime = NOW()
            </set>
            where
            mid = #{mid,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateFaceModelToOffShelve" >
        update face_model_config
        <set>
            state = 2,
            modifyUser = #{loginUserName,jdbcType=VARCHAR},
            modifyTime = NOW()
        </set>
        where
        mid = #{mid,jdbcType=VARCHAR}
    </update>

</mapper>