<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.CleanCallMapper">

    <select id="getCallTagList" parameterType="com.wbgame.pojo.clean.call.CallTagConfigVo" resultType="com.wbgame.pojo.clean.call.CallTagConfigVo">
        select * from call_config_title
        where 1=1
        <if test="appid != null and appid != ''">
            and appid = #{appid,jdbcType=VARCHAR}
        </if>
        <if test="cha != null and cha != ''">
            and cha = #{cha,jdbcType=VARCHAR}
        </if>
        <if test="prjid != null and prjid != ''">
            and prjid = #{prjid,jdbcType=VARCHAR}
        </if>
        <if test="title != null and title != ''">
            and title like concat('%',#{title},'%')
        </if>
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
    </select>

    <insert id="saveCallTag" parameterType="com.wbgame.pojo.clean.call.CallTagConfigVo">
        insert into call_config_title (`appid`, prjid,cha,title,`sort`,status,createUser,createTime)
        values  (#{appid}, #{prjid},#{cha},
                 #{title},#{sort},1,#{createUser},NOW())
    </insert>

    <insert id="saveCallTagBatch" parameterType="java.util.List">
        insert into call_config_title (appid,cha,prjid,`title`,sort,`status`,createUser,createTime)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.appid},#{item.cha},#{item.prjid},#{item.title},#{item.sort},
            #{item.status},#{item.modifyUser},#{item.createTime}
            )
        </foreach>
    </insert>

    <delete id="delCallTag" parameterType="com.wbgame.pojo.clean.call.CallTagConfigVo">
        delete from call_config_title where  id in (${id})
    </delete>

    <update id="updateCallTag" parameterType="com.wbgame.pojo.clean.call.CallTagConfigVo">
        update call_config_title
        set `appid` = #{appid},
            prjid = #{prjid},
            `cha` = #{cha},
            `title` = #{title},
            `sort` = #{sort},
            modifyTime = NOW(),
            modifyUser = #{modifyUser},
            status = #{status}
        where id = #{id}
    </update>





    <select id="getCallList" resultType="com.wbgame.pojo.clean.call.CallConfigVo">
        select * from call_info_list
        where 1=1
        <if test="appid != null and appid != ''">
            and appid = #{appid,jdbcType=VARCHAR}
        </if>
        <if test="tagId != null and tagId != ''">
            and tagId = #{tagId,jdbcType=VARCHAR}
        </if>
        <if test="title != null and title != ''">
            and title like concat('%',#{title},'%')
        </if>
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
    </select>


    <insert id="saveCall" parameterType="com.wbgame.pojo.clean.call.CallConfigVo">
        insert into call_info_list (`appid`, prjid,cha,title,`sort`,tagId,fileType,imgUrl,videoUrl,useCount,
                likeCount,bgColor,status,createUser,createTime)
        values  (#{appid}, #{prjid},#{cha},
                #{title},#{sort},#{tagId},#{fileType},#{imgUrl},#{videoUrl},#{useCount},#{likeCount},#{bgColor},1,#{createUser},NOW())
    </insert>


    <update id="updateCall" parameterType="com.wbgame.pojo.clean.call.CallConfigVo">
        update call_info_list
        set `appid` = #{appid},
            prjid = #{prjid},
            `cha` = #{cha},
            `title` = #{title},
            `tagId` = #{tagId},
            `fileType` = #{fileType},
            `imgUrl` = #{imgUrl},
            `videoUrl` = #{videoUrl},
            `useCount` = #{useCount},
            `likeCount` = #{likeCount},
            `bgColor` = #{bgColor},
            `sort` = #{sort},
            modifyTime = NOW(),
            modifyUser = #{modifyUser},
            status = #{status}
        where id = #{id}
    </update>


    <delete id="delCall" parameterType="com.wbgame.pojo.clean.call.CallConfigVo">
        delete from call_info_list where id in (${id})
    </delete>

    <insert id="saveCallBatch" parameterType="java.util.List">
        insert into call_info_list (appid,cha,prjid,`title`,sort,tagId,fileType,imgUrl,videoUrl,useCount,
            likeCount,bgColor,`status`,createUser,createTime)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.appid},#{item.cha},#{item.prjid},#{item.title},#{item.sort},
            #{item.tagId},#{item.fileType},#{item.imgUrl},#{item.videoUrl},#{item.useCount},#{item.likeCount},#{item.bgColor},
            #{item.status},#{item.modifyUser},#{item.createTime}
            )
        </foreach>
    </insert>

    <delete id="delBatchCall" parameterType="com.wbgame.pojo.clean.call.CallConfigVo">
        delete from call_info_list where appid=#{appid} and tagId =#{tagId}
    </delete>


</mapper>