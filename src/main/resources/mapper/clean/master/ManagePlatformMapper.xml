<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.ManagePlatformMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.ManagePlatformVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="state" property="state" jdbcType="TINYINT"/>
        <result column="appName" property="appName" jdbcType="VARCHAR"/>
        <result column="gameName" property="gameName" jdbcType="VARCHAR"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="company" property="company" jdbcType="INTEGER"/>
        <result column="packageName" property="packageName" jdbcType="VARCHAR"/>
        <result column="pjId" property="pjId" jdbcType="VARCHAR"/>
        <result column="versionName" property="versionName" jdbcType="VARCHAR"/>
        <result column="versionCode" property="versionCode" jdbcType="VARCHAR"/>
        <result column="planners" property="planners" jdbcType="VARCHAR"/>
        <result column="flag" property="flag" jdbcType="TINYINT"/>
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="endTime" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="sign" property="sign" jdbcType="VARCHAR"/>
        <result column="apk" property="apk" jdbcType="VARCHAR"/>
        <result column="res" property="res" jdbcType="LONGVARCHAR"/>
        <result column="manifestPlaceHolder" property="manifestPlaceHolder" jdbcType="LONGVARCHAR"/>
        <result column="defaultConfig" property="defaultConfig" jdbcType="LONGVARCHAR"/>

        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="storepass" property="storepass" jdbcType="VARCHAR"/>
        <result column="keypass" property="keypass" jdbcType="VARCHAR"/>
        <result column="alias" property="alias" jdbcType="VARCHAR"/>
        <result column="keystore" property="keystore" jdbcType="VARCHAR"/>
        <result column="MD5" property="md5" jdbcType="LONGVARCHAR"/>
        <result column="SHA1" property="sha1" jdbcType="LONGVARCHAR"/>
        <result column="SHA256" property="sha256" jdbcType="LONGVARCHAR"/>
        <result column="sdkFiles" property="sdkFiles" jdbcType="LONGVARCHAR"/>

    </resultMap>

    <sql id="Base_Column_List">
        id
        , state, appName, gameName, channel, company, packageName, pjId, versionName, versionCode,
        planners, flag,
        DATE_FORMAT(createTime, '%Y-%m-%d %H:%i:%s') createTime,
        DATE_FORMAT(updateTime, '%Y-%m-%d %H:%i:%s') updateTime,
        DATE_FORMAT(endTime, '%Y-%m-%d %H:%i:%s') endTime, sign, apk,
        res,manifestPlaceHolder, defaultConfig,storepass,keypass,alias,keystore,MD5,SHA1,SHA256,icon,
        createUser, updateUser, sdkFiles
    </sql>

    <select id="selectManagePlatform" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.ManagePlatform">
        select
        <include refid="Base_Column_List"/>

        from manage_platform
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="state != null">
                and state = #{state,jdbcType=TINYINT}
            </if>

            <if test="gameName != null and gameName != ''">
                and gameName = #{gameName,jdbcType=VARCHAR}
            </if>

            <if test="packageName != null and packageName != ''">
                and packageName = #{packageName,jdbcType=VARCHAR}
            </if>
            <if test="pjId != null and pjId != ''">
                and pjId = #{pjId,jdbcType=VARCHAR}
            </if>
            <if test="versionName != null and versionName != ''">
                and versionName = #{versionName,jdbcType=VARCHAR}
            </if>
            <if test="versionCode != null and versionCode != ''">
                and versionCode = #{versionCode,jdbcType=VARCHAR}
            </if>
            <if test="planners != null and planners != ''">
                and planners = #{planners,jdbcType=VARCHAR}
            </if>
            <if test="flag != null and flag != ''">
                and flag = #{flag,jdbcType=TINYINT}
            </if>
            <if test="endTime != null">
                and endTime = #{endTime,jdbcType=TIMESTAMP}
            </if>

            <if test="res != null and res != ''">
                and json_contains_path(res, "all", '$.${res}')
            </if>
            <if test="manifestPlaceHolder != null and manifestPlaceHolder != ''">
                and json_contains_path(manifestPlaceHolder, "all", '$.${manifestPlaceHolder}')
            </if>
            <if test="defaultConfig != null and defaultConfig != ''">
                and json_contains_path(defaultConfig, "all", '$.${defaultConfig}')
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appName IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

            <if test="channelList != null and channelList.size > 0">
                AND channel IN
                <foreach collection="channelList" item="cha" open="(" separator="," close=")">
                    #{cha}
                </foreach>
            </if>

            <if test="companyList != null and companyList.size > 0">

                AND company IN
                <foreach collection="companyList" item="com" open="(" separator="," close=")">
                    #{com}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>
    <delete id="deleteManagePlatform" parameterType="java.lang.Integer">
        delete
        from manage_platform
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertManagePlatform" parameterType="com.wbgame.pojo.clean.ManagePlatform">
        insert into manage_platform
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="state != null">
                state,
            </if>
            <if test="appName != null and appName != ''">
                appName,
            </if>
            <if test="gameName != null and gameName != ''">
                gameName,
            </if>
            <if test="channel != null and channel != ''">
                channel,
            </if>
            <if test="company != null">
                company,
            </if>
            <if test="packageName != null and packageName != ''">
                packageName,
            </if>
            <if test="pjId != null and pjId != ''">
                pjId,
            </if>
            <if test="versionName != null and versionName != ''">
                versionName,
            </if>
            <if test="versionCode != null and versionCode != ''">
                versionCode,
            </if>
            <if test="planners != null and planners != ''">
                planners,
            </if>
            <if test="flag != null and flag != ''">
                flag,
            </if>

            <if test="endTime != null">
                endTime,
            </if>
            <if test="sign != null and sign != ''">
                sign,
            </if>
            <if test="apk != null and apk != ''">
                apk,
            </if>
            <if test="res != null and res != ''">
                res,
            </if>
            <if test="manifestPlaceHolder != null and manifestPlaceHolder != ''">
                manifestPlaceHolder,
            </if>
            <if test="defaultConfig != null and defaultConfig != ''">
                defaultConfig,
            </if>

            <if test="storepass != null and storepass != ''">
                storepass,
            </if>
            <if test="keypass != null and keypass != ''">
                keypass,
            </if>
            <if test="alias != null and alias != ''">
                alias,
            </if>
            <if test="keystore != null and keystore != ''">
                keystore,
            </if>
            <if test="md5 != null and md5 != ''">
                md5,
            </if>
            <if test="sha1 != null and sha1 != ''">
                sha1,
            </if>
            <if test="sha256 != null and sha256 != ''">
                sha256,
            </if>
            <if test="icon != null and icon != ''">
                icon,
            </if>
            <if test="createUser != null and createUser != ''">
                createUser,
            </if>
            <if test="sdkFiles != null and sdkFiles != ''">
                sdkFiles,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="appName != null and appName != ''">
                #{appName,jdbcType=VARCHAR},
            </if>
            <if test="gameName != null and gameName != ''">
                #{gameName,jdbcType=VARCHAR},
            </if>
            <if test="channel != null and channel != ''">
                #{channel,jdbcType=VARCHAR},
            </if>
            <if test="company != null">
                #{company,jdbcType=INTEGER},
            </if>
            <if test="packageName != null and packageName != ''">
                #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="pjId != null and pjId != ''">
                #{pjId,jdbcType=VARCHAR},
            </if>
            <if test="versionName != null and versionName != ''">
                #{versionName,jdbcType=VARCHAR},
            </if>
            <if test="versionCode != null and versionCode != ''">
                #{versionCode,jdbcType=VARCHAR},
            </if>
            <if test="planners != null and planners != ''">
                #{planners,jdbcType=VARCHAR},
            </if>
            <if test="flag != null and flag != ''">
                #{flag,jdbcType=TINYINT},
            </if>

            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sign != null and sign != ''">
                #{sign,jdbcType=VARCHAR},
            </if>
            <if test="apk != null and apk != ''">
                #{apk,jdbcType=VARCHAR},
            </if>
            <if test="res != null and res != ''">
                #{res,jdbcType=LONGVARCHAR},
            </if>
            <if test="manifestPlaceHolder != null and manifestPlaceHolder != ''">
                #{manifestPlaceHolder,jdbcType=LONGVARCHAR},
            </if>
            <if test="defaultConfig != null and defaultConfig != ''">
                #{defaultConfig,jdbcType=LONGVARCHAR},
            </if>

            <if test="storepass != null and storepass != ''">
                #{storepass},
            </if>
            <if test="keypass != null and keypass != ''">
                #{keypass},
            </if>
            <if test="alias != null and alias != ''">
                #{alias},
            </if>
            <if test="keystore != null and keystore != ''">
                #{keystore},
            </if>
            <if test="md5 != null and md5 != ''">
                #{md5},
            </if>
            <if test="sha1 != null and sha1 != ''">
                #{sha1},
            </if>
            <if test="sha256 != null and sha256 != ''">
                #{sha256},
            </if>

            <if test="icon != null and icon != ''">
                #{icon},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser},
            </if>
            <if test="sdkFiles != null and sdkFiles != ''">
                #{sdkFiles},
            </if>
        </trim>
    </insert>

    <update id="updateManagePlatform" parameterType="com.wbgame.pojo.clean.ManagePlatform">
        update manage_platform
        <set>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="appName != null and appName != ''">
                appName = #{appName,jdbcType=VARCHAR},
            </if>
            <if test="gameName != null and gameName != ''">
                gameName = #{gameName,jdbcType=VARCHAR},
            </if>
            <if test="channel != null and channel != ''">
                channel = #{channel,jdbcType=VARCHAR},
            </if>
            <if test="company != null">
                company = #{company,jdbcType=INTEGER},
            </if>
            <if test="packageName != null and packageName != ''">
                packageName = #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="pjId != null and pjId != ''">
                pjId = #{pjId,jdbcType=VARCHAR},
            </if>
            <if test="versionName != null and versionName != ''">
                versionName = #{versionName,jdbcType=VARCHAR},
            </if>
            <if test="versionCode != null and versionCode != ''">
                versionCode = #{versionCode,jdbcType=VARCHAR},
            </if>
            <if test="planners != null and planners != ''">
                planners = #{planners,jdbcType=VARCHAR},
            </if>
            <if test="flag != null and flag != ''">
                flag = #{flag,jdbcType=TINYINT},
            </if>
            <if test="endTime != null">
                endTime = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sign != null and sign != ''">
                sign = #{sign,jdbcType=VARCHAR},
            </if>
            <if test="apk != null and apk != ''">
                apk = #{apk,jdbcType=VARCHAR},
            </if>
            <if test="res != null and res != ''">
                res = #{res,jdbcType=LONGVARCHAR},
            </if>
            <if test="manifestPlaceHolder != null and manifestPlaceHolder != ''">
                manifestPlaceHolder = #{manifestPlaceHolder,jdbcType=LONGVARCHAR},
            </if>
            <if test="defaultConfig != null and defaultConfig != ''">
                defaultConfig = #{defaultConfig,jdbcType=LONGVARCHAR},
            </if>

            <if test="icon != null and icon != ''">
                icon = #{icon},
            </if>

            <if test="updateUser != null and updateUser != ''">
                updateUser = #{updateUser},
            </if>
            <if test="sdkFiles != null and sdkFiles != ''">
                sdkFiles = #{sdkFiles},
            </if>
            updateTime = CURRENT_TIMESTAMP
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.clean.ManagePlatform">
        update manage_platform
        set state       = #{state,jdbcType=TINYINT},
            appName     = #{appName,jdbcType=VARCHAR},
            gameName    = #{gameName,jdbcType=VARCHAR},
            channel     = #{channel,jdbcType=VARCHAR},
            company     = #{company,jdbcType=INTEGER},
            packageName = #{packageName,jdbcType=VARCHAR},
            pjId        = #{pjId,jdbcType=VARCHAR},
            versionName = #{versionName,jdbcType=VARCHAR},
            versionCode = #{versionCode,jdbcType=VARCHAR},
            planners    = #{planners,jdbcType=VARCHAR},
            flag        = #{flag,jdbcType=TINYINT},
            createTime  = #{createTime,jdbcType=TIMESTAMP},
            endTime     = #{endTime,jdbcType=TIMESTAMP},
            sign        = #{sign,jdbcType=VARCHAR},
            apk         = #{apk,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>