<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.SuperSafeControlV3Mapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.SuperSafeControlV3VO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="cha" property="cha" jdbcType="VARCHAR"/>
        <result column="prjid" property="prjid" jdbcType="VARCHAR"/>
        <result column="createTime" property="createTime" jdbcType="VARCHAR"/>
        <result column="enable" property="enable" jdbcType="VARCHAR"/>
        <result column="queryInvTime" property="queryInvTime" jdbcType="VARCHAR"/>
        <result column="modifyTime" property="modifyTime" jdbcType="VARCHAR"/>
        <result column="modifyUser" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="createUser" property="createUser" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="Base_Column_List">
        id
        , appid, cha, prjid, createTime, enable, queryInvTime, modifyTime, modifyUser,
    status, createUser
    </sql>

    <delete id="deleteByIdList" parameterType="java.lang.Integer">
        delete from super_safe_control_v3
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">

            #{id,jdbcType=INTEGER}
        </foreach>

    </delete>


    <insert id="insertSuperSafe" parameterType="com.wbgame.pojo.clean.SuperSafeControlV3">
        insert into super_safe_control_v3
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="appid != null and appid != ''">
                appid,
            </if>
            <if test="cha != null and cha != ''">
                cha,
            </if>
            <if test="prjid != null and prjid != ''">
                prjid,
            </if>

            <if test="enable != null and enable != ''">
                enable,
            </if>
            <if test="queryInvTime != null and queryInvTime != ''">
                queryInvTime,
            </if>

            <if test="status != null and status != ''">
                status,
            </if>
            <if test="createUser != null and createUser != ''">
                createUser,
            </if>
            <if test="ruleOutList != null and ruleOutList != ''">
                ruleOutList,
            </if>
            <if test="delayRank != null and delayRank != ''">
                delayRank,
            </if>
            createTime,
            modifyTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="appid != null and appid != ''">
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="cha != null and cha != ''">
                #{cha,jdbcType=VARCHAR},
            </if>
            <if test="prjid != null and prjid != ''">
                #{prjid,jdbcType=VARCHAR},
            </if>

            <if test="enable != null and enable != ''">
                #{enable,jdbcType=VARCHAR},
            </if>
            <if test="queryInvTime != null and queryInvTime != ''">
                #{queryInvTime,jdbcType=VARCHAR},
            </if>


            <if test="status != null and status != ''">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="ruleOutList != null and ruleOutList != ''">
                #{ruleOutList,jdbcType=LONGVARCHAR},
            </if>
            <if test="delayRank != null and delayRank != ''">
                #{delayRank,jdbcType=LONGVARCHAR},
            </if>
            current_timestamp,
            current_timestamp
        </trim>
    </insert>

    <update id="updateSuperSafeControlV3" >
        update super_safe_control_v3
        <set>

            <if test="super.enable != null and super.enable != ''">
                enable = #{super.enable,jdbcType=VARCHAR},
            </if>
            <if test="super.queryInvTime != null and super.queryInvTime != ''">
                queryInvTime = #{super.queryInvTime,jdbcType=VARCHAR},
            </if>

            <if test="super.modifyUser != null and super.modifyUser != ''">
                modifyUser = #{super.modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="super.status != null and super.status != ''">
                status = #{super.status,jdbcType=VARCHAR},
            </if>

            <if test="super.ruleOutList != null and super.ruleOutList != ''">
                ruleOutList = #{super.ruleOutList,jdbcType=LONGVARCHAR},
            </if>
            <if test="super.delayRank != null and super.delayRank != ''">
                delayRank = #{super.delayRank,jdbcType=LONGVARCHAR},
            </if>
            modifyTime = current_timestamp
        </set>
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">

            #{id,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.clean.SuperSafeControlV3">
        update super_safe_control_v3
        set appid        = #{appid,jdbcType=VARCHAR},
            cha          = #{cha,jdbcType=VARCHAR},
            prjid        = #{prjid,jdbcType=VARCHAR},
            createTime   = #{createTime,jdbcType=VARCHAR},
            enable       = #{enable,jdbcType=VARCHAR},
            queryInvTime = #{queryInvTime,jdbcType=VARCHAR},
            modifyTime   = #{modifyTime,jdbcType=VARCHAR},
            modifyUser   = #{modifyUser,jdbcType=VARCHAR},
            status       = #{status,jdbcType=VARCHAR},
            createUser   = #{createUser,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByCondition" parameterType="com.wbgame.pojo.clean.SuperSafeControlV3DTO"
            resultMap="BaseResultMap">


        select * from super_safe_control_v3

        <where>

            <if test="appidList != null and appidList.size > 0">
                and appid in
                <foreach collection="appidList" item="apid" open="(" separator="," close=")">

                    #{apid,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="chaList != null and chaList.size > 0 ">
                and cha in
                <foreach collection="chaList" item="c" open="(" separator="," close=")">

                    #{c,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="prjid != null and prjid != ''">
                and prjid = #{prjid}
            </if>

            <if test="enable != null and enable != ''">
                and enable = #{enable}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
        order by id desc
    </select>

    <select id="checkForUniqueness" parameterType="com.wbgame.pojo.clean.SuperSafeControlV3DTO"
            resultType="java.lang.Integer">

        select count(*) from super_safe_control_v3

        <where>

            appid = #{appid}
            <choose>
                <when test="cha != null and cha !=''">
                    and cha = #{cha}
                </when>
                <when test="prjid != null and prjid !=''">
                    and prjid = #{prjid}
                </when>

                <otherwise></otherwise>
            </choose>


        </where>
    </select>
</mapper>