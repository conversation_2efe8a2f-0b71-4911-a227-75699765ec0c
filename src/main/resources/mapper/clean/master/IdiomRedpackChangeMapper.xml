<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.IdiomRedpackChangeMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.IdiomRedpackChangeVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="android_id" property="androidId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="prjid" property="prjid" jdbcType="VARCHAR"/>
        <result column="change_type" property="changeType" jdbcType="TINYINT"/>
        <result column="income" property="income" jdbcType="VARCHAR"/>
        <result column="changed_redpack" property="changedRedpack" jdbcType="INTEGER"/>
        <result column="current_ecpm" property="currentEcpm" jdbcType="INTEGER"/>
        <result column="epcm_total" property="epcmTotal" jdbcType="INTEGER"/>
        <result column="epcm_avg" property="epcmAvg" jdbcType="VARCHAR"/>
        <result column="current_level" property="currentLevel" jdbcType="INTEGER"/>
        <result column="is_high_ad" property="isHighAd" jdbcType="TINYINT"/>
        <result column="task_sn" property="taskSn" jdbcType="INTEGER"/>
        <result column="withdraw_mark" property="withdrawMark" jdbcType="VARCHAR"/>
        <result column="level_high_task" property="levelHighTask" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , app_id, prjid, change_type, income, changed_redpack, current_ecpm,user_id,
        epcm_total, epcm_avg, current_level, from_unixtime(create_time / 1000, '%Y-%m-%d %H:%i:%s') create_time,
       case change_type when 9 then current_level when 10 then current_level when 11 then concat( '关卡_', current_level, '_', is_high_ad )
       when 7 then concat( '任务_', task_sn ) end  level_high_task
    </sql>

    <select id="selectIdiomRedpackChange" resultMap="BaseResultMap">

        SELECT <include refid="Base_Column_List"/>

        FROM
        <choose>
            <when test="environment != 'test'">
                gj_b.idiom_redpack_change
            </when>
            <otherwise>
                idiom_redpack_change
            </otherwise>
        </choose>

        <where>

            income != "+0"
            <if test="dto.start_date != null and dto.start_date != '' and dto.end_date != null and dto.end_date != ''">
                and create_time BETWEEN #{dto.start_date}
                AND #{dto.end_date}
            </if>
            <if test="dto.userId != null and dto.userId != ''">
                and user_id = #{dto.userId,jdbcType=VARCHAR}
            </if>
            <if test="dto.changeType != null and dto.changeType != ''">
                and change_type = #{dto.changeType}
            </if>
        </where>

       order by id desc

    </select>

    <select id="countIdiomRedpackChange" resultMap="BaseResultMap">

        select
        sum(income) income,
        sum(changed_redpack) changed_redpack,
        sum(current_ecpm) current_ecpm,
        sum(epcm_total) epcm_total,
        sum(epcm_avg) epcm_avg

        from (


            SELECT from_unixtime(create_time / 1000, '%Y-%m-%d %H:%i:%s') create_time,app_id,prjid,

            <if test="dto.selectGroup != null and dto.selectGroup != ''">
                ${dto.selectGroup},
            </if>

            sum(ifnull(income, 0)) income,
            sum(ifnull(changed_redpack, 0)) changed_redpack,
            sum(ifnull(current_ecpm, 0)) current_ecpm,
            sum(ifnull(epcm_total, 0)) epcm_total,
            sum(ifnull(epcm_avg, 0)) epcm_avg

            FROM
            <choose>
                <when test="environment != 'test'">
                    gj_b.idiom_redpack_change
                </when>
                <otherwise>
                    idiom_redpack_change
                </otherwise>
            </choose>


        <where>
            <if test="dto.start_date != null and dto.start_date != '' and dto.end_date != null and dto.end_date != ''">
                create_time BETWEEN #{dto.start_date}
                AND #{dto.end_date}
            </if>
            <if test="dto.userId != null">
                and user_id = #{dto.userId,jdbcType=VARCHAR}
            </if>
        </where>

            group by create_time,app_id,prjid
        <if test="dto.group != null and dto.group != ''">
            ,${dto.group}
        </if>
        ) a

    </select>

</mapper>