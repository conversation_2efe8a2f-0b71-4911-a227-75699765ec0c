<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.BargainOrderInfoMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.chop.BargainOrderInfo">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="userPicture" property="userPicture" jdbcType="VARCHAR"/>
        <result column="userName" property="userName" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="picture" property="picture" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , userPicture, userName, content, picture
    </sql>

    <select id="selectOrderInfo" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.chop.BargainOrderInfo">
        select
        <include refid="Base_Column_List"/>
        from bargain_order_info
        <where>

            <if test="userPicture != null and userPicture != ''">
                and userPicture = #{userPicture}
            </if>
            <if test="picture != null and picture != ''">
                and picture = #{picture}
            </if>

        </where>
        order by id desc
    </select>
    <delete id="deleteOrderInfo" parameterType="java.lang.Integer">
        delete
        from bargain_order_info
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertOrderInfo" parameterType="com.wbgame.pojo.clean.chop.BargainOrderInfo">
        insert into bargain_order_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userPicture != null and userPicture != ''">
                userPicture,
            </if>
            <if test="userName != null and userName != ''">
                userName,
            </if>
            <if test="content != null and content != ''">
                content,
            </if>
            <if test="picture != null and picture != ''">
                picture,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="userPicture != null and userPicture != ''">
                #{userPicture,jdbcType=VARCHAR},
            </if>
            <if test="userName != null and userName != ''">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != ''">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="picture != null and picture != ''">
                #{picture,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateOrderInfo" parameterType="com.wbgame.pojo.clean.chop.BargainOrderInfo">
        update bargain_order_info
        <set>
            <if test="userPicture != null and userPicture != ''">
                userPicture = #{userPicture,jdbcType=VARCHAR},
            </if>
            <if test="userName != null and userName != ''">
                userName = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != ''">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="picture != null and picture != ''">
                picture = #{picture,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

</mapper>