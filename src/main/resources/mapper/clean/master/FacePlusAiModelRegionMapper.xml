<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.FacePlusAiModelRegionMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.aipaint.FacePlusAiModelRegionVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="model_id" property="modelId" jdbcType="BIGINT"/>
        <result column="area_id" property="areaId" jdbcType="BIGINT"/>
        <result column="modify_user" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="BIGINT"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="temp_type" property="tempType" jdbcType="TINYINT"/>
        <result column="model_name" property="modelName" jdbcType="TINYINT"/>
        <result column="cover_url" property="coverUrl" jdbcType="VARCHAR"/>
        <result column="make_template_count" property="makeTemplateCount" jdbcType="INTEGER"/>
        <result column="tpl_convert_vip_ount" property="tplConvertVipOunt" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , model_id, area_id, modify_user, modify_time, sort
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from gj_b.face_plus_ai_model_region
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteModelRegionByModelId" parameterType="java.lang.Long">
        delete
        from gj_b.face_plus_ai_model_region
        where model_id = #{modelId}

    </delete>

    <insert id="insertToonModelRegionRel" parameterType="com.wbgame.pojo.clean.aipaint.FacePlusAiModelRegion">

        <foreach collection="list" item="rel" separator=";">

            insert into gj_b.face_plus_ai_model_region(model_id,area_id,modify_user,modify_time)
            values(#{rel.modelId}, #{rel.areaId}, #{rel.modifyUser}, #{rel.modifyTime})
        </foreach>

    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.clean.aipaint.FacePlusAiModelRegion">
        update gj_b.face_plus_ai_model_region
        <set>
            <if test="modelId != null">
                model_id = #{modelId,jdbcType=BIGINT},
            </if>
            <if test="classId != null">
                class_id = #{classId,jdbcType=BIGINT},
            </if>
            <if test="areaId != null">
                area_id = #{areaId,jdbcType=BIGINT},
            </if>

            <if test="modifyUser != null">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectModelByArea" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
               a.*,
               b.model_name,
               b.temp_type,
               b.cover_url,
               b.make_template_count,
               b.tpl_convert_vip_ount,

               concat(ifnull(truncate(b.tpl_convert_vip_ount / b.make_template_count * 100, 2), 0), "%") conversionRate
        FROM gj_b.face_plus_ai_model_region a
                 LEFT JOIN gj_b.face_plus_ai_model b ON a.model_id = b.id
        WHERE area_id = #{areaId}
        order by a.sort, a.modify_time desc
    </select>

    <update id="updateRegionSort" parameterType="com.wbgame.pojo.clean.aipaint.FacePlusAiModelRegion">

        <foreach collection="list" item="data" separator=";">

            update gj_b.face_plus_ai_model_region
            set sort = #{data.sort}, modify_user = #{data.modifyUser}, modify_time = #{data.modifyTime}
            where id = #{data.id}
        </foreach>
    </update>

</mapper>