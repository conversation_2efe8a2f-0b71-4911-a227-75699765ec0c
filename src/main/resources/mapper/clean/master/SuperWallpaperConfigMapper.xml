<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.SuperWallpaperConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.img.SuperWallpaperConfigVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="typeId" property="typeId" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="imgSort" property="imgSort" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="preUrl" property="preUrl" jdbcType="VARCHAR"/>
        <result column="modelUrl" property="modelUrl" jdbcType="VARCHAR"/>
        <result column="imgUseCount" property="imgUseCount" jdbcType="VARCHAR"/>
        <result column="imgLikeCount" property="imgLikeCount" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="fileType" property="fileType" jdbcType="VARCHAR"/>
        <result column="createTime" property="createTime" jdbcType="VARCHAR"/>
        <result column="createUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="modifyTime" property="modifyTime" jdbcType="VARCHAR"/>
        <result column="modifyUser" property="modifyUser" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , appid, typeId, name, imgSort, url, preUrl, modelUrl, imgUseCount, imgLikeCount,
    status, fileType, createTime, createUser, modifyTime, modifyUser
    </sql>
    <select id="selectSuperWallpaperConfig" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.clean.img.SuperWallpaperConfig">
        select

        <include refid="Base_Column_List"/>
        from super_wallpaper_config

        <where>


            <if test="typeId != null and typeId != ''">
                and typeId = #{typeId}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="fileType != null and fileType != ''">
                and fileType = #{fileType}
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>

        order by id desc
    </select>

    <delete id="deleteSuperWallpaperConfig" parameterType="java.lang.Integer">
        delete
        from super_wallpaper_config
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <insert id="insertSuperWallpaperConfig" parameterType="com.wbgame.pojo.clean.img.SuperWallpaperConfig">
        insert into super_wallpaper_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="appid != null and appid != ''">
                appid,
            </if>
            <if test="typeId != null and typeId != ''">
                typeId,
            </if>
            <if test="name != null and name != ''">
                name,
            </if>
            <if test="imgSort != null and imgSort != ''">
                imgSort,
            </if>
            <if test="url != null and url != ''">
                url,
            </if>
            <if test="preUrl != null and preUrl != ''">
                preUrl,
            </if>
            <if test="modelUrl != null and modelUrl != ''">
                modelUrl,
            </if>
            <if test="imgUseCount != null and imgUseCount != ''">
                imgUseCount,
            </if>
            <if test="imgLikeCount != null and imgLikeCount != ''">
                imgLikeCount,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
            <if test="fileType != null and fileType != ''">
                fileType,
            </if>
            <if test="createUser != null and createUser != ''">
                createUser,
            </if>
            createTime,
            modifyTime

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="appid != null and appid != ''">
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="typeId != null and typeId != ''">
                #{typeId,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != ''">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="imgSort != null and imgSort != ''">
                #{imgSort,jdbcType=VARCHAR},
            </if>
            <if test="url != null and url != ''">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="preUrl != null and preUrl != ''">
                #{preUrl,jdbcType=VARCHAR},
            </if>
            <if test="modelUrl != null and modelUrl != ''">
                #{modelUrl,jdbcType=VARCHAR},
            </if>
            <if test="imgUseCount != null and imgUseCount != ''">
                #{imgUseCount,jdbcType=VARCHAR},
            </if>
            <if test="imgLikeCount != null and imgLikeCount != ''">
                #{imgLikeCount,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="fileType != null and fileType != ''">
                #{fileType,jdbcType=VARCHAR},
            </if>

            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>

            now(), now()
        </trim>
    </insert>

    <update id="updateSuperWallpaperConfig" parameterType="com.wbgame.pojo.clean.img.SuperWallpaperConfig">
        update super_wallpaper_config
        <set>

            <if test="appid != null and appid != ''">
                appid = #{appid},
            </if>

            <if test="typeId != null and typeId != ''">
                typeId = #{typeId,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>

            <if test="imgSort != null and imgSort != ''">
                imgSort = #{imgSort,jdbcType=VARCHAR},
            </if>
            <if test="url != null and url != ''">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="preUrl != null and preUrl != ''">
                preUrl = #{preUrl,jdbcType=VARCHAR},
            </if>
            <if test="modelUrl != null and modelUrl != ''">
                modelUrl = #{modelUrl,jdbcType=VARCHAR},
            </if>
            <if test="imgUseCount != null and imgUseCount != ''">
                imgUseCount = #{imgUseCount,jdbcType=VARCHAR},
            </if>
            <if test="imgLikeCount != null and imgLikeCount != ''">
                imgLikeCount = #{imgLikeCount,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="fileType != null and fileType != ''">
                fileType = #{fileType,jdbcType=VARCHAR},
            </if>

            <if test="modifyUser != null and modifyUser != ''">
                modifyUser = #{modifyUser,jdbcType=VARCHAR},
            </if>
            modifyTime = now()
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

</mapper>