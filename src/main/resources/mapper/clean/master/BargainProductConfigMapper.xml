<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.BargainProductConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.chop.BargainProductConfig">
        <id column="productId" property="productId" jdbcType="INTEGER"/>
        <result column="productName" property="productName" jdbcType="VARCHAR"/>
        <result column="productPrice" property="productPrice" jdbcType="DECIMAL"/>
        <result column="productNum" property="productNum" jdbcType="INTEGER"/>
        <result column="productTotalPrice" property="productTotalPrice" jdbcType="DECIMAL"/>
        <result column="productShowPrice" property="productShowPrice" jdbcType="DECIMAL"/>
        <result column="productUrl" property="productUrl" jdbcType="VARCHAR"/>
        <result column="productTitle" property="productTitle" jdbcType="VARCHAR"/>
        <result column="productPicture" property="productPicture" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="createUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="updateUser" property="updateUser" jdbcType="VARCHAR"/>
        <result column="createTime" property="createTime" jdbcType="VARCHAR"/>
        <result column="updateTime" property="updateTime" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="resMap">
        productId
        , productName, productPrice, productNum, productTotalPrice, productShowPrice,
        productUrl, productTitle, productPicture,status,createUser,updateUser,banner,
                date_format(createTime, '%Y-%m-%d %H:%i:%s') createTime,
                date_format(updateTime, '%Y-%m-%d %H:%i:%s') updateTime
    </sql>
    <select id="selectBargainProductConfig" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.clean.chop.BargainProductConfig">
        select

        <include refid="resMap"/>
        from bargain_product_config

        <where>
            <if test="productName != null and productName != ''">
                and productName = #{productName}
            </if>
            <if test="productId != null">
                and productId = #{productId}
            </if>
        </where>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>

            <otherwise>
                order by productId desc
            </otherwise>
        </choose>

    </select>

    <delete id="deleteBargainProductConfig" parameterType="java.lang.Integer">
        delete
        from bargain_product_config
        where productId in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </delete>

    <insert id="insertBargainProductConfig" parameterType="com.wbgame.pojo.clean.chop.BargainProductConfig"
            useGeneratedKeys="true" keyProperty="productId" keyColumn="productId">
        insert into bargain_product_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="productName != null and productName != ''">
                productName,
            </if>
            <if test="productPrice != null">
                productPrice,
            </if>
            <if test="productNum != null">
                productNum,
            </if>
            <if test="productTotalPrice != null">
                productTotalPrice,
            </if>
            <if test="productShowPrice != null">
                productShowPrice,
            </if>
            <if test="productUrl != null and productUrl != ''">
                productUrl,
            </if>
            <if test="productTitle != null and productTitle != ''">
                productTitle,
            </if>
            <if test="productPicture != null and productPicture != ''">
                productPicture,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createUser != null and createUser != ''">
                createUser,
            </if>
            <if test="banner != null and banner != ''">
                banner,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="productName != null and productName != ''">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productPrice != null">
                #{productPrice,jdbcType=DECIMAL},
            </if>
            <if test="productNum != null">
                #{productNum,jdbcType=INTEGER},
            </if>
            <if test="productTotalPrice != null">
                #{productTotalPrice,jdbcType=DECIMAL},
            </if>
            <if test="productShowPrice != null">
                #{productShowPrice,jdbcType=DECIMAL},
            </if>
            <if test="productUrl != null and productUrl != ''">
                #{productUrl,jdbcType=VARCHAR},
            </if>
            <if test="productTitle != null and productTitle != ''">
                #{productTitle,jdbcType=VARCHAR},
            </if>
            <if test="productPicture != null and productPicture != ''">
                #{productPicture,jdbcType=VARCHAR},
            </if>

            <if test="status != null">
                #{status},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser},
            </if>
            <if test="banner != null and banner != ''">
                #{banner},
            </if>
        </trim>
    </insert>

    <update id="updateBargainProductConfig" parameterType="com.wbgame.pojo.clean.chop.BargainProductConfig">
        update bargain_product_config
        <set>
            <if test="productName != null and productName != ''">
                productName = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productPrice != null">
                productPrice = #{productPrice,jdbcType=DECIMAL},
            </if>
            <if test="productNum != null">
                productNum = #{productNum,jdbcType=INTEGER},
            </if>
            <if test="productTotalPrice != null">
                productTotalPrice = #{productTotalPrice,jdbcType=DECIMAL},
            </if>
            <if test="productShowPrice != null">
                productShowPrice = #{productShowPrice,jdbcType=DECIMAL},
            </if>
            <if test="productUrl != null and productUrl != ''">
                productUrl = #{productUrl,jdbcType=VARCHAR},
            </if>
            <if test="productTitle != null and productTitle != ''">
                productTitle = #{productTitle,jdbcType=VARCHAR},
            </if>
            <if test="productPicture != null and productPicture != ''">
                productPicture = #{productPicture,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="updateUser != null and updateUser != ''">
                updateUser = #{updateUser},
            </if>
            <if test="banner != null and banner != ''">
                banner = #{banner},
            </if>
            updateTime = now()
        </set>
        where productId = #{productId,jdbcType=INTEGER}
    </update>

    <select id="selectCountProductById" parameterType="java.lang.Integer" resultMap="BaseResultMap">

        select productId,
               productName,
               productPrice,
               productNum,
               productTotalPrice,
               productShowPrice
        from  bargain_product_config
        where productId in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by productId
    </select>

</mapper>