<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.CleanKxpMapper">

    <select id="getKxpTitleList" parameterType="com.wbgame.pojo.clean.kxp.KxpTitleVo" resultType="com.wbgame.pojo.clean.kxp.KxpTitleVo">
        select * from qp_material_title
        where 1=1
        <if test="modelType != null and modelType != ''">
            and modelType = #{modelType,jdbcType=VARCHAR}
        </if>
        <if test="modelTitle != null and modelTitle != ''">
            and modelTitle like concat('%',#{modelTitle},'%')
        </if>
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
        order by createTime desc
    </select>

    <insert id="addKxpTitle" parameterType="com.wbgame.pojo.clean.kxp.KxpTitleVo">
        insert into qp_material_title (modelType, modelTitle,`sort`,`status`,createUser,createTime)
        values  (#{modelType,jdbcType=VARCHAR}, #{modelTitle,jdbcType=VARCHAR},#{sort,jdbcType=VARCHAR},
                 #{status,jdbcType=VARCHAR},#{createUser,jdbcType=VARCHAR},NOW())
    </insert>

    <update id="updateKxpTitle" parameterType="com.wbgame.pojo.clean.kxp.KxpTitleVo">
        update qp_material_title
        set `modelType` = #{modelType,jdbcType=VARCHAR},
            modelTitle = #{modelTitle,jdbcType=VARCHAR},
            sort = #{sort,jdbcType=VARCHAR},
            status = #{status,jdbcType=VARCHAR},
            modifyUser = #{modifyUser,jdbcType=VARCHAR},
            modifyTime = now()
        where modelId = #{modelId,jdbcType=VARCHAR}
    </update>

    <delete id="delKxpTitle" parameterType="com.wbgame.pojo.clean.kxp.KxpTitleVo">
        delete from qp_material_title where modelId = #{modelId}
    </delete>


    <select id="getKxpMaterialList" parameterType="com.wbgame.pojo.clean.kxp.KxpMaterialVo" resultType="com.wbgame.pojo.clean.kxp.KxpMaterialVo">
        select * from qp_material_info
        where 1=1
        <if test="modelId != null and modelId != ''">
            and modelId = #{modelId}
        </if>
        <if test="titleName != null and titleName != ''">
            and titleName like concat('%',#{titleName},'%')
        </if>
        <if test="musicTitle != null and musicTitle != ''">
            and musicTitle like concat('%',#{musicTitle},'%')
        </if>
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
        order by createTime desc
    </select>

    <insert id="addKxpMaterial" parameterType="com.wbgame.pojo.clean.kxp.KxpMaterialVo">
        insert into qp_material_info (modelId, isVip,`music`,`url`,timelength,musicTitle,titleName,`sort`,`status`,createUser,createTime)
        values  (#{modelId}, #{isVip},#{music},#{url}, #{timelength},#{musicTitle},#{titleName},#{sort},
                 #{status},#{createUser},NOW())
    </insert>

    <update id="updateKxpMaterial" parameterType="com.wbgame.pojo.clean.kxp.KxpMaterialVo">
        update qp_material_info
        set modelId = #{modelId,jdbcType=VARCHAR},
            isVip = #{isVip,jdbcType=VARCHAR},
            music = #{music,jdbcType=VARCHAR},
            url = #{url,jdbcType=VARCHAR},
            timelength = #{timelength,jdbcType=VARCHAR},
            musicTitle = #{musicTitle,jdbcType=VARCHAR},
            titleName = #{titleName,jdbcType=VARCHAR},
            sort =#{sort},
            status = #{status,jdbcType=VARCHAR},
            modifyUser = #{modifyUser,jdbcType=VARCHAR},
            modifyTime = now()
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <delete id="delKxpMaterial" parameterType="com.wbgame.pojo.clean.kxp.KxpMaterialVo">
        delete from qp_material_info where id = #{id}
    </delete>



</mapper>