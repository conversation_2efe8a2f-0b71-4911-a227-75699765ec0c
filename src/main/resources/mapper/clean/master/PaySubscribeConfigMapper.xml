<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.PaySubscribeConfigMapper">
    <resultMap id="paySubscribeConfigMap" type="com.wbgame.pojo.clean.PaySubscribeConfigVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="cha" property="cha" jdbcType="VARCHAR"/>
        <result column="prjid" property="prjid" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="createTime" property="createTime" jdbcType="VARCHAR"/>
        <result column="modifyTime" property="modifyTime" jdbcType="VARCHAR"/>
        <result column="createUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="modifyUser" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="selectLin">
        p.id, p.appid, p.cha, p.prjid, p.country, p.createTime, p.modifyTime,
        p.createUser, p.modifyUser, p.status, p.payCfg
    </sql>


    <delete id="deletePaySubscribeConfig" parameterType="java.lang.Integer">
        delete
        from pay_subscribe_config
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <insert id="insertPaySubscribeConfig" parameterType="com.wbgame.pojo.clean.PaySubscribeConfig"
            useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into pay_subscribe_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="appid != null and appid != ''">
                appid,
            </if>
            <choose>
                <when test="cha != null and cha != ''">
                    cha,
                </when>
                <when test="prjid != null and prjid != ''">
                    prjid,
                </when>
            </choose>
            <if test="country != null and country != ''">
                country,
            </if>

            <if test="createUser != null and createUser != ''">
                createUser,
            </if>

            <if test="status != null and status != ''">
                status,
            </if>
            <if test="payCfg != null and payCfg != ''">
                payCfg,
            </if>
            createTime,
            modifyTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="appid != null and appid != ''">
                #{appid,jdbcType=VARCHAR},
            </if>
            <choose>
                <when test="cha != null and cha != ''">
                    #{cha,jdbcType=VARCHAR},
                </when>
                <when test="prjid != null and prjid != ''">
                    #{prjid,jdbcType=VARCHAR},
                </when>
            </choose>
            <if test="country != null and country != ''">
                #{country,jdbcType=VARCHAR},
            </if>

            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>

            <if test="status != null and status != ''">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="payCfg != null and payCfg != ''">
                #{payCfg,jdbcType=LONGVARCHAR},
            </if>
            current_timestamp,
            current_timestamp
        </trim>
    </insert>


    <update id="updatePaySubscribeConfig" parameterType="com.wbgame.pojo.clean.PaySubscribeConfig">
        update pay_subscribe_config
        <set>

            <if test="country != null">
                country = #{country,jdbcType=VARCHAR},
            </if>

            <if test="modifyUser != null">
                modifyUser = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="payCfg != null">
                payCfg = #{payCfg,jdbcType=LONGVARCHAR},
            </if>
            modifyTime = current_timestamp
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.clean.PaySubscribeConfig">
        update pay_subscribe_config
        set appid      = #{appid,jdbcType=VARCHAR},
            cha        = #{cha,jdbcType=VARCHAR},
            prjid      = #{prjid,jdbcType=VARCHAR},
            country    = #{country,jdbcType=VARCHAR},
            createTime = #{createTime,jdbcType=VARCHAR},
            modifyTime = #{modifyTime,jdbcType=VARCHAR},
            createUser = #{createUser,jdbcType=VARCHAR},
            modifyUser = #{modifyUser,jdbcType=VARCHAR},
            status     = #{status,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectPaySubscribeConfig" resultMap="paySubscribeConfigMap"
            parameterType="com.wbgame.pojo.clean.PaySubscribeConfigDTO">


        select
        <include refid="selectLin"/>, a.app_name
        from pay_subscribe_config p left join app_info a on p.appid = a.id
        <where>

            <if test="prjid != null and prjid != ''">
                and p.prjid = #{prjid}
            </if>
            <if test="status != null and status != ''">
                and p.status = #{status}
            </if>
            <if test="appidList != null and appidList.size > 0">

                and p.appid in
                <foreach collection="appidList" item="aid" open="(" separator="," close=")">
                    #{aid}
                </foreach>

            </if>
            <if test="chaList != null and chaList.size > 0">
                and p.cha in
                <foreach collection="chaList" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>

            <if test="countryList != null and countryList.size > 0">
                and p.country in
                <foreach collection="countryList" item="c" open="(" separator="," close=")">
                    #{c}
                </foreach>
            </if>
        </where>

        order by id desc

    </select>

    <select id="selectAppIdOrChaExist" resultMap="paySubscribeConfigMap"
            parameterType="com.wbgame.pojo.clean.PaySubscribeConfigDTO">


        select <include refid="selectLin"/>
        from pay_subscribe_config p
        <where>

            <if test="appid != null and appid != ''">
                 and appid = #{appid}
            </if>
            <if test="country != null and country != ''">
                and country = #{country}
            </if>
            <choose>
                <when test="cha != null and cha != ''">
                    and cha = #{cha}
                </when>
                <when test="prjid != null and prjid != ''">
                    and prjid = #{prjid}
                </when>
            </choose>
        </where>

    </select>
</mapper>