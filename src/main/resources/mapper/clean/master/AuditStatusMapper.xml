<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.AuditStatusMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.AuditStatus">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="prj_id" property="prjId" jdbcType="VARCHAR"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="audit_status" property="auditStatus" jdbcType="TINYINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
        <result column="modify_user" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , app_id, prj_id, channel, audit_status, status,
        create_user,
        if(create_time = 0, "", from_unixtime( create_time / 1000, '%Y-%m-%d %H:%i:%s' )) create_time,
        modify_user,
        if(modify_time = 0, "", from_unixtime( modify_time / 1000, '%Y-%m-%d %H:%i:%s' )) modify_time
    </sql>
    <select id="selectAuditStatus" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.AuditStatus">
        select

        <include refid="Base_Column_List"/>
        from gj_b.audit_status
        <where>

            <if test="status != null">
                and status = #{status}
            </if>

            <if test="auditStatus != null">
                and audit_status = #{auditStatus}
            </if>


            <if test="prjidList != null and prjidList.size > 0">
                AND prj_id IN
                <foreach collection="prjidList" item="prjid" open="(" separator="," close=")">
                    #{prjid}
                </foreach>
            </if>

            <if test="channelList != null and channelList.size > 0">
                AND channel IN
                <foreach collection="channelList" item="cha" open="(" separator="," close=")">
                    #{cha}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND app_id IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <delete id="deleteAuditStatus" parameterType="java.lang.Long">
        delete
        from gj_b.audit_status
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertAuditStatus" parameterType="com.wbgame.pojo.clean.AuditStatus" useGeneratedKeys="true"
            keyColumn="id" keyProperty="id">
        insert into gj_b.audit_status
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="appId != null and appId != ''">
                app_id,
            </if>
            <if test="prjId != null and prjId != ''">
                prj_id,
            </if>
            <if test="channel != null and channel != ''">
                channel,
            </if>
            <if test="auditStatus != null">
                audit_status,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createUser != null and createUser != ''">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>

            <if test="modifyTime != null">
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="appId != null and appId != ''">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="prjId != null and prjId != ''">
                #{prjId,jdbcType=VARCHAR},
            </if>
            <if test="channel != null and channel != ''">
                #{channel,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                #{auditStatus,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>

            <if test="modifyTime != null">
                #{modifyTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateAuditStatus" parameterType="com.wbgame.pojo.clean.AuditStatus">
        update gj_b.audit_status
        <set>
            <if test="appId != null and appId != ''">
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="prjId != null and prjId != ''">
                prj_id = #{prjId,jdbcType=VARCHAR},
            </if>
            <if test="channel != null and channel != ''">
                channel = #{channel,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>

            <if test="modifyUser != null and modifyUser != ''">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectPrjIdOrChannelLinkAppId" resultType="java.lang.Long" parameterType="com.wbgame.pojo.clean.AuditStatus">
        select

        id
        from gj_b.audit_status
        where app_id = #{appId}
        <choose>

            <when test="prjId != null and prjId != ''">

                and prj_id = #{prjId}
            </when>
            <when test="channel != null and channel != ''">

                and channel = #{channel}
            </when>

        </choose>
        limit 1
    </select>

    <select id="selectById" resultType="com.wbgame.pojo.clean.AuditStatus" parameterType="java.lang.Long">
        select app_id appId, prj_id prjId, channel from gj_b.audit_status
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>