<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.FaceOpreateSortMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.face.FaceOpreateSort">
        <id column="productId" property="productId" jdbcType="INTEGER"/>
        <id column="area" property="area" jdbcType="VARCHAR"/>
        <result column="sortId" property="sortId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        productId
        , area, sortId
    </sql>

    <delete id="deleteFaceOpreateSort" parameterType="java.lang.Integer">
        delete
        from face_opreate_sort
        where productId = #{prodcId}

    </delete>

    <insert id="insertFaceOpreateSort" parameterType="com.wbgame.pojo.clean.face.FaceOpreateSort">

        <foreach collection="list" item="data" separator=";">

            insert into face_opreate_sort
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="data.productId != null">
                    productId,
                </if>
                <if test="data.area != null and data.area != ''">
                    area,
                </if>
                <if test="data.sortId != null">
                    sortId,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="data.productId != null">
                    #{data.productId},
                </if>
                <if test="data.area != null and data.area != ''">
                    #{data.area,jdbcType=VARCHAR},
                </if>
                <if test="data.sortId != null">
                    #{data.sortId},
                </if>
            </trim>
        </foreach>
    </insert>

    <update id="updateFaceOpreateSort" parameterType="com.wbgame.pojo.clean.face.FaceOpreateSort">

        <foreach collection="list" item="data" separator=";">

            update face_opreate_sort
            set sortId = #{data.sortId}

            where productId = #{data.productId} and
            area = #{data.area}
        </foreach>
    </update>


    <select id="selectConfigByArea" resultType="com.wbgame.pojo.clean.face.FaceProductConfig" parameterType="com.wbgame.pojo.clean.face.FaceProductConfig">

        SELECT
            sortId,
            area,
            productId,
            b.productName,
            b.STATUS,
            b.createUser,
            b.createTime,
            b.modifyUser,
            b.modifyTime
        FROM
            face_opreate_sort a
            RIGHT JOIN face_product_config b ON a.productId = b.id

        where area = #{area} and cha = #{cha}
        order by sortId
    </select>
</mapper>