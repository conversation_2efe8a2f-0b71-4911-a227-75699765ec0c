<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.clean.master.ToonModelRegionRelMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.toonstory.ToonModelRegionRelVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="model_id" jdbcType="BIGINT" property="modelId"/>
        <result column="class_id" jdbcType="BIGINT" property="classId"/>
        <result column="area_id" jdbcType="BIGINT" property="areaId"/>
        <result column="modify_user" jdbcType="VARCHAR" property="modifyUser"/>
        <result column="modify_time" jdbcType="BIGINT" property="modifyTime"/>
        <result column="temp_title" jdbcType="VARCHAR" property="tempTitle"/>
        <result column="cover_url" jdbcType="VARCHAR" property="coverUrl"/>
        <result column="source_url" jdbcType="VARCHAR" property="sourceUrl"/>
        <result column="make_template_count" jdbcType="INTEGER" property="makeTemplateCount"/>
        <result column="tpl_convert_vip_ount" jdbcType="INTEGER" property="tplConvertVipOunt"/>
        <result column="temp_type" jdbcType="TINYINT" property="tempType"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , model_id, class_id, area_id, create_user, create_time, modify_user, modify_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from gj_b.toon_model_region_rel
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteModelRegionByModelId" parameterType="java.lang.Long">
        delete
        from gj_b.toon_model_region_rel
        where model_id = #{modelId}

    </delete>

    <insert id="insertToonModelRegionRel" parameterType="com.wbgame.pojo.clean.toonstory.ToonModelRegionRel">

        <foreach collection="list" item="rel" separator=";">

            insert into gj_b.toon_model_region_rel
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="rel.modelId">
                    model_id,
                </if>
                <if test="rel.classId">
                    class_id,
                </if>
                <if test="rel.areaId">
                    area_id,
                </if>
                <if test="rel.modifyUser">
                    modify_user,
                </if>
                <if test="rel.modifyTime">
                    modify_time,
                </if>
                <if test="rel.sort">
                    sort,
                </if>

            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">

                <if test="rel.modelId">
                    #{rel.modelId},
                </if>
                <if test="rel.classId">
                    #{rel.classId},
                </if>
                <if test="rel.areaId">
                    #{rel.areaId},
                </if>
                <if test="rel.modifyUser">
                    #{rel.modifyUser},
                </if>
                <if test="rel.modifyTime">
                    #{rel.modifyTime},
                </if>
                <if test="rel.sort">
                    #{rel.sort},
                </if>
            </trim>
        </foreach>

    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.clean.toonstory.ToonModelRegionRel">
        update gj_b.toon_model_region_rel
        <set>
            <if test="modelId != null">
                model_id = #{modelId,jdbcType=BIGINT},
            </if>
            <if test="classId != null">
                class_id = #{classId,jdbcType=BIGINT},
            </if>
            <if test="areaId != null">
                area_id = #{areaId,jdbcType=BIGINT},
            </if>

            <if test="modifyUser != null">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectToonModelByClass" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT a.*, b.temp_title, b.temp_type, b.cover_url,
               concat(ifnull(truncate(b.tpl_convert_vip_ount / b.make_template_count * 100, 2), 0), "%")  conversionRate
        FROM gj_b.toon_model_region_rel a
                 LEFT JOIN gj_b.toon_model b ON a.model_id = b.id
        WHERE class_id = #{classId} and model_type = 1
        order by a.sort, a.modify_time desc
    </select>

    <update id="updateRegionSort" parameterType="com.wbgame.pojo.clean.toonstory.ToonModelRegionRel" >

        <foreach collection="list" item="data" separator=";">

            update gj_b.toon_model_region_rel
            set sort = #{data.sort}, modify_user = #{data.modifyUser}, modify_time = #{data.modifyTime}
            where id = #{data.id}
        </foreach>
    </update>

    <select id="selectModelByArea" parameterType="java.lang.Long" resultMap="BaseResultMap">

        SELECT a.*, b.temp_title, b.temp_type, b.cover_url,
               concat(ifnull(truncate(b.tpl_convert_vip_ount / b.make_template_count * 100, 2), 0), "%")  conversionRate
        FROM gj_b.toon_model_region_rel a
                 LEFT JOIN gj_b.toon_model b ON a.model_id = b.id
        WHERE area_id = #{areaId} and model_type = 2 and b.state = 1
        order by a.sort, a.modify_time desc
    </select>
</mapper>