<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.ScreenLockScenarioConfigMapper">
    <resultMap id="V2ConfigMap" type="com.wbgame.pojo.clean.ScreenLockScenarioConfigVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="txt" property="txt" jdbcType="VARCHAR"/>
        <result column="val" property="val" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , txt, val, type, status, date_format(create_time, "%Y-%m-%d %H:%i:%s") create_time,
            date_format(update_time, "%Y-%m-%d %H:%i:%s") update_time, create_user, update_user
    </sql>

    <select id="selectScreenLockScenarioConfig" resultMap="V2ConfigMap" parameterType="com.wbgame.pojo.clean.ScreenLockScenarioConfigDTO">
        select
        <include refid="Base_Column_List"/>
        from screen_lock_scenario_config
        <where>

            <if test="txt != null and txt != ''">
                and txt like "%" #{txt} "%"
            </if>
            <if test="val != null and val != ''">
                and val like "%" #{val} "%"
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        order by id desc
    </select>
    <delete id="deleteByIdList" parameterType="java.lang.Integer">
        delete
        from screen_lock_scenario_config
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <insert id="insertScreenLockScenarioConfig" parameterType="com.wbgame.pojo.clean.ScreenLockScenarioConfig">
        insert into screen_lock_scenario_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="txt != null and txt != ''">
                txt,
            </if>
            <if test="val != null and val != ''">
                val,
            </if>
            <if test="type != null and type != ''">
                type,
            </if>
            <if test="status != null">
                status,
            </if>

            <if test="createUser != null and createUser != ''">
                create_user,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="txt != null and txt != ''">
                #{txt,jdbcType=VARCHAR},
            </if>
            <if test="val != null and val != ''">
                #{val,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != ''">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>

            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>

        </trim>
    </insert>

    <update id="updateScreenLockScenarioConfigById" parameterType="com.wbgame.pojo.clean.ScreenLockScenarioConfig">
        update screen_lock_scenario_config
        <set>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>

            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            update_time = current_timestamp
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

</mapper>