<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.WifiProductConfigMapper" >
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.WifiProductConfigVO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="cha" property="cha" jdbcType="VARCHAR" />
    <result column="productName" property="productName" jdbcType="VARCHAR" />
    <result column="productPrice" property="productPrice" jdbcType="VARCHAR" />
    <result column="period" property="period" jdbcType="TINYINT" />
    <result column="feeDay" property="feeDay" jdbcType="VARCHAR" />
    <result column="platformProductId" property="platformProductId" jdbcType="VARCHAR" />
    <result column="sort" property="sort" jdbcType="INTEGER" />
    <result column="createTime" property="createTime" jdbcType="VARCHAR" />
    <result column="modifyTime" property="modifyTime" jdbcType="VARCHAR" />
    <result column="createUser" property="createUser" jdbcType="VARCHAR" />
    <result column="modifyUser" property="modifyUser" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="productDescribe" property="productDescribe" jdbcType="VARCHAR" />
    <result column="moneyType" property="moneyType" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, cha, productName, productPrice, period, feeDay, platformProductId, sort, createTime, 
    modifyTime, createUser, modifyUser, status, productDescribe, moneyType
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from wifi_product_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteById" parameterType="java.lang.Integer" >
    delete from wifi_product_config
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="com.wbgame.pojo.clean.WifiProductConfig" >
    insert into wifi_product_config (id, cha, productName, 
      productPrice, period, feeDay, 
      platformProductId, sort, createTime, 
      modifyTime, createUser, modifyUser, 
      status, productDescribe, moneyType
      )
    values (#{id,jdbcType=INTEGER}, #{cha,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, 
      #{productPrice,jdbcType=VARCHAR}, #{period,jdbcType=TINYINT}, #{feeDay,jdbcType=VARCHAR}, 
      #{platformProductId,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{createTime,jdbcType=VARCHAR}, 
      #{modifyTime,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, #{modifyUser,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{productDescribe,jdbcType=VARCHAR}, #{moneyType,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertProductConfig" parameterType="com.wbgame.pojo.clean.WifiProductConfig" >
    insert into wifi_product_config
    <trim prefix="(" suffix=")" suffixOverrides="," >

      <if test="cha != null and cha != ''">
        cha,
      </if>

      <if test="productPrice != null and productPrice != ''" >
        productPrice,
      </if>
      <if test="period != null" >
        period,
      </if>
      <if test="feeDay != null and feeDay != ''" >
        feeDay,
      </if>
      <if test="platformProductId != null and platformProductId != ''" >
        platformProductId,
      </if>
      <if test="sort != null" >
        sort,
      </if>

      <if test="createUser != null and createUser != ''" >
        createUser,
      </if>

      <if test="status != null and status != ''" >
        status,
      </if>
      <if test="productDescribe != null and productDescribe != ''" >
        productDescribe,
      </if>
      <if test="moneyType != null and moneyType != ''" >
        moneyType,
      </if>
      <if test="productName!=null and productName!=''" >
        productName,
      </if>
        createTime,
        modifyTime
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >

      <if test="cha != null and cha != ''" >
        #{cha,jdbcType=VARCHAR},
      </if>



      <if test="productPrice != null and productPrice != ''" >
        #{productPrice,jdbcType=VARCHAR},
      </if>

      <if test="period != null" >
        #{period,jdbcType=TINYINT},
      </if>

      <if test="feeDay != null and feeDay != ''" >
        #{feeDay,jdbcType=VARCHAR},
      </if>
      <if test="platformProductId != null and platformProductId != ''" >
        #{platformProductId,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        #{sort,jdbcType=INTEGER},
      </if>

      <if test="createUser != null and createUser != ''" >
        #{createUser,jdbcType=VARCHAR},
      </if>

      <if test="status != null and status != ''" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="productDescribe != null and productDescribe != ''" >
        #{productDescribe,jdbcType=VARCHAR},
      </if>
      <if test="moneyType != null and moneyType != ''" >
        #{moneyType,jdbcType=VARCHAR},
      </if>
      <if test="productName != null and productName != ''" >
        #{productName,jdbcType=VARCHAR},
      </if>
        now(),
        now()
    </trim>
  </insert>


  <update id="updateProductConfigById" parameterType="com.wbgame.pojo.clean.WifiProductConfig" >
    update wifi_product_config
    <set >
      <if test="cha != null and cha != ''" >
        cha = #{cha,jdbcType=VARCHAR},
      </if>
      <if test="productName != null and productName != ''" >
        productName = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="productPrice != null and productPrice != ''" >
        productPrice = #{productPrice,jdbcType=VARCHAR},
      </if>
      <if test="period != null" >
        period = #{period,jdbcType=TINYINT},
      </if>
      <if test="feeDay != null and feeDay != ''" >
        feeDay = #{feeDay,jdbcType=VARCHAR},
      </if>
      <if test="platformProductId != null and platformProductId !=''" >
        platformProductId = #{platformProductId,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        sort = #{sort,jdbcType=INTEGER},
      </if>

      <if test="modifyUser != null and modifyUser !=''" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status !=''" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="productDescribe != null and productDescribe !=''" >
        productDescribe = #{productDescribe,jdbcType=VARCHAR},
      </if>
      <if test="moneyType != null and moneyType !=''" >
        moneyType = #{moneyType,jdbcType=VARCHAR},
      </if>
      modifyTime = now()
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.clean.WifiProductConfig" >
    update wifi_product_config
    set cha = #{cha,jdbcType=VARCHAR},
      productName = #{productName,jdbcType=VARCHAR},
      productPrice = #{productPrice,jdbcType=VARCHAR},
      period = #{period,jdbcType=TINYINT},
      feeDay = #{feeDay,jdbcType=VARCHAR},
      platformProductId = #{platformProductId,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      createTime = #{createTime,jdbcType=VARCHAR},
      modifyTime = #{modifyTime,jdbcType=VARCHAR},
      createUser = #{createUser,jdbcType=VARCHAR},
      modifyUser = #{modifyUser,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      productDescribe = #{productDescribe,jdbcType=VARCHAR},
      moneyType = #{moneyType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectProductConfigAllList" parameterType="com.wbgame.pojo.clean.WifiProductConfigDTO" resultMap="BaseResultMap">

        select * from wifi_product_config 
        <where>
          
          <if test="id != null">
            and id = #{id}
          </if>

          <if test="platformProductId != null and platformProductId != ''">
            and platformProductId like #{platformProductId} "%"
          </if>
          <if test="productName != null and productName != ''">
            and productName like #{productName} "%"
          </if>

          <if test="cha != null and cha != ''">
            and cha = #{cha}
          </if>

          <if test="period != null">
            and period = #{period}
          </if>

          <if test="status != null and status != ''">
            and status = #{status}
          </if>
        </where>

        order by id desc
  </select>

  <update id="batchUpdateSort" parameterType="com.wbgame.pojo.clean.WifiProductConfig" >

        <foreach collection="list" item="config" separator=";">

            update wifi_product_config set sort = #{config.sort} where id = #{config.id}
        </foreach>
  </update>

  <select id="countProduceByIdsAndCha" resultType="java.lang.Integer">

      select count(*) from wifi_product_config
      <where>

          <if test="ids.length != 0">

            and id in
              <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
              </foreach>
          </if>

        <if test="cha != null and cha != ''">
          and cha = #{cha}
        </if>


      </where>
  </select>
</mapper>