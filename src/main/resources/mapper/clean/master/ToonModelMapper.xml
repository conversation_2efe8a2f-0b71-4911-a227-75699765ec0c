<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.clean.master.ToonModelMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.toonstory.ToonModelVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="temp_title" jdbcType="VARCHAR" property="tempTitle"/>
        <result column="make_template_count" jdbcType="INTEGER" property="makeTemplateCount"/>
        <result column="tpl_convert_vip_ount" jdbcType="INTEGER" property="tplConvertVipOunt"/>
        <result column="region" jdbcType="TINYINT" property="region"/>
        <result column="cover_url" jdbcType="VARCHAR" property="coverUrl"/>
        <result column="source_url" jdbcType="VARCHAR" property="sourceUrl"/>
        <result column="temp_type" jdbcType="TINYINT" property="tempType"/>
        <result column="task_time" jdbcType="BIGINT" property="taskTime"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="shelve_time" jdbcType="BIGINT" property="shelveTime"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="modify_user" jdbcType="VARCHAR" property="modifyUser"/>
        <result column="modify_time" jdbcType="BIGINT" property="modifyTime"/>
        <result column="model_keyword" jdbcType="VARCHAR" property="modelKeyword"/>
        <result column="model_strength" jdbcType="DECIMAL" property="modelStrength"/>
        <result column="model_seed" jdbcType="INTEGER" property="modelSeed"/>
        <result column="model_type" jdbcType="INTEGER" property="modelType"/>
        <result column="source_width" jdbcType="INTEGER" property="sourceWidth"/>
        <result column="source_height" jdbcType="INTEGER" property="sourceHeight"/>
    </resultMap>

    <sql id="Base_Column_List">
        a.id,
        a.temp_title,
        a.make_template_count,
        a.tpl_convert_vip_ount,
        a.region,
        a.cover_url,
        a.source_url,
        a.temp_type,
        a.state,
        if(a.shelve_time = 0, "", from_unixtime( a.shelve_time / 1000, '%Y-%m-%d' )) shelve_time,
        if(a.task_time = 0, "", from_unixtime( a.task_time / 1000, '%Y-%m-%d %H:%i:%s' )) task_time,
        a.sort,
        a.model_keyword,
        a.model_strength,
        a.model_seed,
        a.create_user,
        a.model_type,
        a.source_width,
        a.source_height,
        from_unixtime( a.create_time / 1000, '%Y-%m-%d %H:%i:%s' ) create_time,
        a.modify_user,
        from_unixtime( a.modify_time / 1000, '%Y-%m-%d %H:%i:%s' ) modify_time,
        concat(ifnull(truncate(tpl_convert_vip_ount / make_template_count * 100, 2), 0), "%")  conversionRate
    </sql>

    <select id="selectToonModel" parameterType="com.wbgame.pojo.clean.toonstory.ToonModelDTO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from gj_b.toon_model a left join gj_b.toon_model_region_rel b
        on a.id = b.model_id
        <where>

            <if test="id != null">
                and a.id = #{id}
            </if>
            <if test="tempTitle != null and tempTitle != ''">
                and a.temp_title like "%" #{tempTitle} "%"
            </if>
            <if test="areaId != null">
                and b.area_id = #{areaId}
            </if>
            <if test="classId != null">

               and b.class_id = #{classId}
            </if>
            <if test="state != null">

                and a.state = #{state}
            </if>
            <if test="tempType != null">
                and a.temp_type = #{tempType}
            </if>
            <if test="modelType != null">
                and a.model_type = #{modelType}
            </if>

        </where>
        group by a.id,
                a.temp_title,
                a.make_template_count,
                a.tpl_convert_vip_ount,
                a.region,
                a.cover_url,
                a.source_url,
                a.temp_type,
                a.state,
                a.shelve_time,
                a.task_time,
                a.sort,
                a.model_keyword,
                a.model_strength,
                a.model_seed,
                a.model_type,
                a.source_width,
                a.source_height,
                a.create_user,
                a.create_time,
                a.modify_user,
                modify_time
        order by a.id desc
    </select>
    <delete id="deleteToonModelByIdList" parameterType="java.lang.Long">
        delete
        from gj_b.toon_model
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertToonModel" parameterType="com.wbgame.pojo.clean.toonstory.ToonModel"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into gj_b.toon_model
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="tempTitle != null and tempTitle != ''">
                temp_title,
            </if>
            <if test="makeTemplateCount != null">
                make_template_count,
            </if>
            <if test="tplConvertVipOunt != null">
                tpl_convert_vip_ount,
            </if>
            <if test="region != null">
                region,
            </if>
            <if test="coverUrl != null and coverUrl != ''">
                cover_url,
            </if>
            <if test="sourceUrl != null and sourceUrl != ''">
                source_url,
            </if>
            <if test="tempType != null">
                temp_type,
            </if>
            <if test="taskTime != null">
                task_time,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="shelveTime != null">
                shelve_time,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="createUser != null and createUser != ''">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>

            <if test="modifyTime != null">
                modify_time,
            </if>

            <if test="modelKeyword != null">
                model_keyword,
            </if>

            <if test="modelStrength != null">
                model_strength,
            </if>

            <if test="modelSeed != null">
                model_seed,
            </if>

            <if test="sourceWidth != null">
                source_width,
            </if>

            <if test="sourceHeight != null">
                source_height,
            </if>

            <if test="modelType != null">
                model_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="tempTitle != null and tempTitle != ''">
                #{tempTitle,jdbcType=VARCHAR},
            </if>
            <if test="makeTemplateCount != null">
                #{makeTemplateCount,jdbcType=INTEGER},
            </if>
            <if test="tplConvertVipOunt != null">
                #{tplConvertVipOunt,jdbcType=INTEGER},
            </if>
            <if test="region != null">
                #{region,jdbcType=TINYINT},
            </if>
            <if test="coverUrl != null and coverUrl != ''">
                #{coverUrl,jdbcType=VARCHAR},
            </if>
            <if test="sourceUrl != null and sourceUrl != ''">
                #{sourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="tempType != null">
                #{tempType,jdbcType=TINYINT},
            </if>
            <if test="taskTime != null">
                #{taskTime,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="shelveTime != null">
                #{shelveTime,jdbcType=BIGINT},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>

            <if test="modifyTime != null">
                #{modifyTime,jdbcType=BIGINT},
            </if>

            <if test="modelKeyword != null">
                #{modelKeyword},
            </if>

            <if test="modelStrength != null">
                #{modelStrength},
            </if>

            <if test="modelSeed != null">
                #{modelSeed},
            </if>

            <if test="sourceWidth != null">
                #{sourceWidth},
            </if>

            <if test="sourceHeight != null">
                #{sourceHeight},
            </if>

            <if test="modelType != null">
                #{modelType},
            </if>
        </trim>
    </insert>

    <update id="updateToonModel" parameterType="com.wbgame.pojo.clean.toonstory.ToonModel">
        update gj_b.toon_model
        <set>
            <if test="tempTitle != null and tempTitle != ''">
                temp_title = #{tempTitle,jdbcType=VARCHAR},
            </if>
            <if test="makeTemplateCount != null">
                make_template_count = #{makeTemplateCount,jdbcType=INTEGER},
            </if>
            <if test="tplConvertVipOunt != null">
                tpl_convert_vip_ount = #{tplConvertVipOunt,jdbcType=INTEGER},
            </if>
            <if test="region != null">
                region = #{region,jdbcType=TINYINT},
            </if>
            <if test="coverUrl != null and coverUrl != ''">
                cover_url = #{coverUrl,jdbcType=VARCHAR},
            </if>
            <if test="sourceUrl != null and sourceUrl != ''">
                source_url = #{sourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="tempType != null">
                temp_type = #{tempType,jdbcType=TINYINT},
            </if>
            <if test="taskTime != null">
                task_time = #{taskTime,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="shelveTime != null">
                shelve_time = #{shelveTime,jdbcType=BIGINT},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>

            <if test="modifyUser != null and modifyUser != ''">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>

            <if test="modelKeyword != null">
                model_keyword = #{modelKeyword},
            </if>

            <if test="modelStrength != null">
                model_strength = #{modelStrength},
            </if>

            <if test="modelSeed != null">
                model_seed = #{modelSeed},
            </if>
            <if test="modelSeed != null">
                source_width = #{sourceWidth},
            </if>
            <if test="modelSeed != null">
                source_height = #{sourceHeight},
            </if>

            <if test="modelType != null">
                model_type = #{modelType},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <update id="updateStatus" parameterType="com.wbgame.pojo.clean.toonstory.ToonModel">
        update gj_b.toon_model
        <set>

            <if test="tempType != null">
                temp_type = #{tempType,jdbcType=TINYINT},
            </if>

            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>

            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>

            <if test="modifyUser != null and modifyUser != ''">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateToonModelTempType" parameterType="com.wbgame.pojo.clean.toonstory.ToonModel">
        update gj_b.toon_model
        <set>

            <if test="tempType != null">
                temp_type = #{tempType,jdbcType=TINYINT},
            </if>

            <if test="modifyUser != null and modifyUser != ''">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>