<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.SuperWallpaperTitleConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.img.SuperWallpaperTitleConfigVO">
        <id column="typeId" property="typeId" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="createTime" property="createTime" jdbcType="VARCHAR"/>
        <result column="createUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="modifyTime" property="modifyTime" jdbcType="VARCHAR"/>
        <result column="modifyUser" property="modifyUser" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        typeId
        , type, appid, status, sort, createTime, createUser, modifyTime, modifyUser
    </sql>
    <select id="selectSuperWallpaperTitleConfig" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.clean.img.SuperWallpaperTitleConfig">
        select

        <include refid="Base_Column_List"/>
        from super_wallpaper_title_config

        <where>

            <if test="typeId != null">
                and typeId = #{typeId}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>

            <if test="status != null and status != ''">
                and status = #{status}
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>

        order by typeId desc
    </select>

    <delete id="deleteSuperWallpaperTitleConfig" parameterType="java.lang.Integer">
        delete
        from super_wallpaper_title_config
        where typeId in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <insert id="insertSuperWallpaperTitleConfig" parameterType="com.wbgame.pojo.clean.img.SuperWallpaperTitleConfig">
        insert into super_wallpaper_title_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="type != null and type != ''">
                type,
            </if>
            <if test="appid != null and appid != ''">
                appid,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
            <if test="sort != null">
                sort,
            </if>

            <if test="createUser != null">
                createUser,
            </if>

            createTime,
            modifyTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="type != null and type != ''">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="appid != null and appid != ''">
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>

            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            now(),
            now()
        </trim>
    </insert>

    <update id="updateSuperWallpaperTitleConfig" parameterType="com.wbgame.pojo.clean.img.SuperWallpaperTitleConfig">
        update super_wallpaper_title_config
        <set>

            <if test="appid != null and appid != ''">
                appid = #{appid,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>

            <if test="modifyUser != null and modifyUser != ''">
                modifyUser = #{modifyUser,jdbcType=VARCHAR},
            </if>
            modifyTime = now()
        </set>
        where typeId = #{typeId,jdbcType=INTEGER}
    </update>

</mapper>