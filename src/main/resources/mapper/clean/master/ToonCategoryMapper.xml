<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.clean.master.ToonCategoryMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.toonstory.ToonCategoryVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="class_name" jdbcType="VARCHAR" property="className"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="area_id" jdbcType="BIGINT" property="areaId"/>
        <result column="area_name" jdbcType="BIGINT" property="areaName"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="modify_user" jdbcType="VARCHAR" property="modifyUser"/>
        <result column="modify_time" jdbcType="BIGINT" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        a.id,
        a.class_name,
        a.sort,
        a.area_id,
        a.STATUS,
        a.create_user,
        from_unixtime( a.create_time / 1000, '%Y-%m-%d %H:%i:%s' ) create_time,
        a.modify_user,
        from_unixtime( a.modify_time / 1000, '%Y-%m-%d %H:%i:%s' ) modify_time

    </sql>

    <insert id="insertToonCategory" parameterType="com.wbgame.pojo.clean.toonstory.ToonCategory"
            useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into gj_b.toon_category
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="className != null">
                class_name,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="areaId != null">
                area_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>

            <if test="modifyTime != null">
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="className != null">
                #{className,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="areaId != null">
                #{areaId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>

            <if test="modifyTime != null">
                #{modifyTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateToonCategory" parameterType="com.wbgame.pojo.clean.toonstory.ToonCategory">
        update gj_b.toon_category
        <set>
            <if test="className != null and className != ''">
                class_name = #{className,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="areaId != null">
                area_id = #{areaId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="createUser != null and createUser != ''">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="modifyUser != null and modifyUser != ''">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectToonCategory" parameterType="com.wbgame.pojo.clean.toonstory.ToonCategoryDTO" resultMap="BaseResultMap">

        select <include refid="Base_Column_List"/>,
               b.area_name
        from gj_b.toon_category a
        left join gj_b.toon_area b
        on a.area_id = b.id
        <where>

            <if test="className != null and className != ''">
                and class_name like #{className,jdbcType=VARCHAR} "%"
            </if>
            <if test="status != null">
                and a.status = #{status}
            </if>

            <if test="areaId != null">
                and area_id = #{areaId}
            </if>
        </where>

        order by a.id desc

    </select>

    <select id="selectToonCategoryByArea" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select id, sort, class_name, modify_user, area_id,
               from_unixtime( modify_time / 1000, '%Y-%m-%d %H:%i:%s' ) modify_time

        from gj_b.toon_category
        where status = 1 and area_id = #{areaId}
        order by sort
    </select>

    <select id="selectAreaIdById" parameterType="java.lang.Long" resultType="java.lang.Long">

        SELECT distinct area_id FROM gj_b.toon_category
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </select>

    <update id="updateSort" parameterType="com.wbgame.pojo.clean.toonstory.ToonCategory">

        <foreach collection="list" item="ca" separator=";">

            update gj_b.toon_category
            set sort = #{ca.sort}, modify_user = #{ca.modifyUser},  modify_time = #{ca.modifyTime}
            where id = #{ca.id}
        </foreach>
    </update>
</mapper>