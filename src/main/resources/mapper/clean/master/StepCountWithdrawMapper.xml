<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.clean.master.StepCountWithdrawMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.stepcount.StepCountWithdrawVO">
        <result column="androidid" jdbcType="VARCHAR" property="androidid"/>
        <result column="orderId" jdbcType="VARCHAR" property="orderId"/>
        <result column="appid" jdbcType="VARCHAR" property="appid"/>
        <result column="prjid" jdbcType="VARCHAR" property="prjid"/>
        <result column="cha" jdbcType="VARCHAR" property="channel"/>
        <result column="openId" jdbcType="VARCHAR" property="openId"/>
        <result column="package" jdbcType="VARCHAR" property="pkg"/>
        <result column="timestamp" jdbcType="VARCHAR" property="timestamp"/>
        <result column="version" jdbcType="VARCHAR" property="version"/>
        <result column="imei" jdbcType="VARCHAR" property="imei"/>
        <result column="lsn" jdbcType="VARCHAR" property="lsn"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="withdrawStatus" jdbcType="TINYINT" property="withdrawStatus"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="ecpmTotal" jdbcType="INTEGER" property="ecpmTotal"/>
        <result column="createUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="createTime" jdbcType="BIGINT" property="createTime"/>
        <result column="modifyUser" jdbcType="VARCHAR" property="modifyUser"/>
        <result column="modifyTime" jdbcType="BIGINT" property="modifyTime"/>
        <result column="auditor" jdbcType="VARCHAR" property="auditor"/>
        <result column="auditTime" jdbcType="BIGINT" property="auditTime"/>
        <result column="arrivalStatus" jdbcType="VARCHAR" property="arrivalStatus"/>
        <result column="arrivalTime" jdbcType="BIGINT" property="arrivalTime"/>
        <result column="approvalNumber" jdbcType="INTEGER" property="approvalNumber"/>
    </resultMap>

    <sql id="Base_Column_List">
        androidid
        , prjid,
        withdrawStatus,
        orderId, appid, cha, openId, package, `timestamp`,
        version, imei, lsn, brand, amount, ecpmTotal, createUser, modifyUser, auditor, arrivalStatus,
        if(createTime = 0, "", from_unixtime( createTime / 1000, '%Y-%m-%d %H:%i:%s' )) createTime,
        if(auditTime = 0, "", from_unixtime( auditTime / 1000, '%Y-%m-%d %H:%i:%s' )) auditTime,
        if(arrivalTime = 0, "", from_unixtime( arrivalTime / 1000, '%Y-%m-%d %H:%i:%s' )) arrivalTime,
        if(modifyTime = 0, "", from_unixtime( modifyTime / 1000, '%Y-%m-%d %H:%i:%s' )) modifyTime,
        account,
        currentEcpm,
        currentEcpmVideo,
        currentEcpmPlaque,
        ipuTime,
        ipuVideoTime,
        ipuPlaqueTime,
        signTime,
        approvalNumber
    </sql>
    <select id="selectStepCount" parameterType="com.wbgame.pojo.clean.stepcount.StepCountWithdraw"
            resultMap="BaseResultMap">
        select

        <include refid="Base_Column_List"/>
        from xyxtj.step_count_withdraw

        <where>

            <if test="prjid != null and prjid != ''">
                AND prjid  = #{prjid}
            </if>
            <if test="withdrawStatus != null and withdrawStatus != ''">
                AND withdrawStatus  = #{withdrawStatus}
            </if>
            <if test="channelList != null and channelList.size > 0">
                AND cha IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>
            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>
       order by createTime desc
    </select>

    <delete id="deleteStepCount" parameterType="com.wbgame.pojo.clean.stepcount.StepCountWithdrawKey">

        <if test="list != null and list.size > 0">

            <foreach collection="list" item="data" separator=";">

                delete
                from xyxtj.step_count_withdraw
                where androidid = #{data.androidid,jdbcType=VARCHAR}
                and prjid = #{data.prjid,jdbcType=VARCHAR}
                and orderId = #{data.orderId}
            </foreach>
        </if>

    </delete>


    <update id="updateStepCountStatus" parameterType="com.wbgame.pojo.clean.stepcount.StepCountWithdraw">
        update xyxtj.step_count_withdraw
        <set>

            <if test="modifyUser != null">
                modifyUser = #{modifyUser,jdbcType=VARCHAR},
            </if>

            <if test="withdrawStatus != null">
                withdrawStatus = #{withdrawStatus},
            </if>
            <if test="modifyTime != null">
                modifyTime = #{modifyTime,jdbcType=BIGINT},
            </if>
            <if test="auditor != null">
                auditor = #{auditor,jdbcType=VARCHAR},
            </if>
            <if test="auditTime != null">
                auditTime = #{auditTime,jdbcType=BIGINT},
            </if>
            <if test="arrivalStatus != null">
                arrivalStatus = #{arrivalStatus,jdbcType=VARCHAR},
            </if>
            <if test="arrivalTime != null">
                arrivalTime = #{arrivalTime,jdbcType=BIGINT},
            </if>

            <if test="approvalNumber != null">
                approvalNumber = approvalNumber + 1,
            </if>
        </set>
        where androidid = #{androidid,jdbcType=VARCHAR}
        and prjid = #{prjid,jdbcType=VARCHAR}
        and orderId = #{orderId}
    </update>

    <select id="selectStepCountByKey" parameterType="com.wbgame.pojo.clean.stepcount.StepCountWithdraw"
            resultMap="BaseResultMap">
        select
        prjid, appid, cha, openId, package, `timestamp`, imei, lsn, amount, androidid, orderId
        from xyxtj.step_count_withdraw
        where androidid = #{androidid} AND prjid = #{prjid} and orderId = #{orderId}
        limit 1
    </select>

    <update id="updateStepCountStatusList" parameterType="com.wbgame.pojo.clean.stepcount.StepCountWithdraw">

        <foreach collection="list" item="data" separator=";">

            update xyxtj.step_count_withdraw
            <set>

                <if test="data.modifyUser != null">
                    modifyUser = #{data.modifyUser,jdbcType=VARCHAR},
                </if>

                <if test="data.withdrawStatus != null">
                    withdrawStatus = #{data.withdrawStatus},
                </if>
                <if test="data.modifyTime != null">
                    modifyTime = #{data.modifyTime,jdbcType=BIGINT},
                </if>
                <if test="data.auditor != null">
                    auditor = #{data.auditor,jdbcType=VARCHAR},
                </if>
                <if test="data.auditTime != null">
                    auditTime = #{data.auditTime,jdbcType=BIGINT},
                </if>
                <if test="data.arrivalStatus != null">
                    arrivalStatus = #{data.arrivalStatus,jdbcType=VARCHAR},
                </if>
                <if test="data.arrivalTime != null">
                    arrivalTime = #{data.arrivalTime,jdbcType=BIGINT},
                </if>
                <if test="data.approvalNumber != null">
                    approvalNumber = approvalNumber + 1,
                </if>

            </set>
            where androidid = #{data.androidid,jdbcType=VARCHAR}
            and prjid = #{data.prjid,jdbcType=VARCHAR}
            and orderId = #{data.orderId}
        </foreach>
    </update>

    <update id="batchReview" parameterType="com.wbgame.pojo.clean.stepcount.StepCountWithdraw">
        update xyxtj.step_count_withdraw
        <set>

            <if test="modifyUser != null">
                modifyUser = #{modifyUser,jdbcType=VARCHAR},
            </if>

            <if test="withdrawStatus != null">
                withdrawStatus = #{withdrawStatus},
            </if>
            <if test="modifyTime != null">
                modifyTime = #{modifyTime,jdbcType=BIGINT},
            </if>
            <if test="auditor != null">
                auditor = #{auditor,jdbcType=VARCHAR},
            </if>
            <if test="auditTime != null">
                auditTime = #{auditTime,jdbcType=BIGINT},
            </if>
            <if test="arrivalStatus != null">
                arrivalStatus = #{arrivalStatus,jdbcType=VARCHAR},
            </if>
            <if test="arrivalTime != null">
                arrivalTime = #{arrivalTime,jdbcType=BIGINT},
            </if>
        </set>
        where withdrawStatus = 1 and  prjid = #{prjid} and ecpmTotal >= #{ecpmTotal}
    </update>

    <select id="selectCountWithdrawByPrjidAndEcpmTotal" parameterType="com.wbgame.pojo.clean.stepcount.StepCountWithdraw"
            resultMap="BaseResultMap">
        select prjid, appid, cha, openId, package, `timestamp`, imei, lsn, amount, androidid, orderId
            from xyxtj.step_count_withdraw
            where withdrawStatus = 1

            <if test="prjidList != null and prjidList.size > 0">
                AND prjid IN
                <foreach collection="prjidList" item="prjid" open="(" separator="," close=")">
                    #{prjid}
                </foreach>
            </if>
            <if test="ecpmTotal != null">
                and ecpmTotal > #{ecpmTotal}
            </if>

    </select>


    <select id="selectWithDrawByConfig" resultMap="BaseResultMap">

        SELECT
            c.*
        FROM
            xyxtj.step_audit_config a,
            xyxtj.step_count_withdraw c
        WHERE
            a.appid = c.appid
          AND a.pid = c.prjid
          AND a.cha = c.cha
          AND if(a.account = 'notMatch', 1=1, a.account = c.account)
          AND c.withdrawStatus = 1
          AND c.signTime > a.signDay
          AND c.ecpmTotal > a.ecpm
          AND a.status = 1
          AND c.amount = a.amount

    </select>
</mapper>