<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.clean.master.StepCountConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.stepcount.StepCountConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="appid" jdbcType="VARCHAR" property="appid"/>
        <result column="cha" jdbcType="VARCHAR" property="cha"/>
        <result column="prjid" jdbcType="VARCHAR" property="prjid"/>
        <result column="splash" jdbcType="INTEGER" property="splash"/>
        <result column="plaque" jdbcType="INTEGER" property="plaque"/>
        <result column="video" jdbcType="INTEGER" property="video"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="modify_user" jdbcType="VARCHAR" property="modifyUser"/>
        <result column="modify_time" jdbcType="BIGINT" property="modifyTime"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , appid, cha, prjid, splash, plaque, video,
        create_user,
        from_unixtime( create_time / 1000, '%Y-%m-%d %H:%i:%s' ) create_time,
        modify_user,
        from_unixtime( modify_time / 1000, '%Y-%m-%d %H:%i:%s' ) modify_time,
        status
    </sql>

    <select id="selectStepCountConfig" parameterType="com.wbgame.pojo.clean.stepcount.StepCountConfig" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xyxtj.step_count_config
        <where>

            <if test="prjid != null and prjid != ''">
                and prjid = #{prjid}
            </if>
            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

            <if test="cha != null and cha != ''">
                and cha in (${cha})
            </if>
        </where>
        order by id desc
    </select>
    <delete id="deleteStepCountConfig" parameterType="java.lang.Long">
        delete
        from xyxtj.step_count_config
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insert" parameterType="com.wbgame.pojo.clean.stepcount.StepCountConfig">
        insert into xyxtj.step_count_config (id, appid, cha,
                                       prjid, splash, plaque,
                                       video, create_user, create_time,
                                       modify_user, modify_time, status)
        values (#{id,jdbcType=BIGINT}, #{appid,jdbcType=VARCHAR}, #{cha,jdbcType=VARCHAR},
                #{prjid,jdbcType=VARCHAR}, #{splash,jdbcType=INTEGER}, #{plaque,jdbcType=INTEGER},
                #{video,jdbcType=INTEGER}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT},
                #{modifyUser,jdbcType=VARCHAR}, #{modifyTime,jdbcType=BIGINT}, #{status,jdbcType=VARCHAR})
    </insert>
    <insert id="insertStepCountConfig" parameterType="com.wbgame.pojo.clean.stepcount.StepCountConfig"
            useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into xyxtj.step_count_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="appid != null">
                appid,
            </if>

            <choose>
                <when test="prjid != null and prjid != ''">
                    prjid,
                </when>
                <otherwise>
                    cha,
                </otherwise>
            </choose>

            <if test="splash != null">
                splash,
            </if>
            <if test="plaque != null">
                plaque,
            </if>
            <if test="video != null">
                video,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyUser != null">
                modify_user,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="status != null">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="appid != null">
                #{appid,jdbcType=VARCHAR},
            </if>
            <choose>
                <when test="prjid != null and prjid != ''">
                    #{prjid},
                </when>
                <otherwise>
                    #{cha},
                </otherwise>
            </choose>
            <if test="splash != null">
                #{splash,jdbcType=INTEGER},
            </if>
            <if test="plaque != null">
                #{plaque,jdbcType=INTEGER},
            </if>
            <if test="video != null">
                #{video,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="modifyUser != null">
                #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateStepCountConfig" parameterType="com.wbgame.pojo.clean.stepcount.StepCountConfig">
        update xyxtj.step_count_config
        <set>

            <if test="splash != null">
                splash = #{splash,jdbcType=INTEGER},
            </if>
            <if test="plaque != null">
                plaque = #{plaque,jdbcType=INTEGER},
            </if>
            <if test="video != null">
                video = #{video,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="modifyUser != null">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectStepCountExits" parameterType="com.wbgame.pojo.clean.stepcount.StepCountConfig" resultType="java.lang.Integer">
        select
        1
        from xyxtj.step_count_config
        where appid = #{appid}
        <choose>
            <when test="prjid != null and prjid != ''">
                and prjid = #{prjid}
            </when>
            <otherwise>
                and cha = #{cha}
            </otherwise>
        </choose>
        limit 1
    </select>
</mapper>