<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.ToonClientConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.toonstory.ToonClientConfigVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="config_type" property="configType" jdbcType="TINYINT"/>
        <result column="bynou_off_on" property="bynouOffOn" jdbcType="TINYINT"/>
        <result column="subscription_page" property="subscriptionPage" jdbcType="VARCHAR"/>
        <result column="model_id" property="modelId" jdbcType="BIGINT"/>
        <result column="modify_user" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="BIGINT"/>
        <result column="sub_guide" property="subGuide" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id , config_type, subscription_page, model_id,
        modify_user,
        bynou_off_on,
        sub_guide,
        from_unixtime( modify_time / 1000, '%Y-%m-%d %H:%i:%s' ) modify_time
    </sql>

    <select id="selectToonClientConfig" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from gj_b.toon_client_config

    </select>

    <update id="updateToonClientConfig" parameterType="com.wbgame.pojo.clean.toonstory.ToonClientConfig">
        update gj_b.toon_client_config
        <set>
            <if test="configType != null">
                config_type = #{configType,jdbcType=TINYINT},
            </if>
            <if test="subscriptionPage != null">
                subscription_page = #{subscriptionPage,jdbcType=VARCHAR},
            </if>
            <if test="modelId != null">
                model_id = #{modelId,jdbcType=BIGINT},
            </if>
            <if test="modifyUser != null">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
            <if test="bynouOffOn != null">
                bynou_off_on = #{bynouOffOn},
            </if>
            <if test="subGuide != null">
                sub_guide = #{subGuide},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.clean.toonstory.ToonClientConfig">
        update gj_b.toon_client_config
        set config_type       = #{configType,jdbcType=TINYINT},
            subscription_page = #{subscriptionPage,jdbcType=VARCHAR},
            model_id          = #{modelId,jdbcType=BIGINT},
            modify_user       = #{modifyUser,jdbcType=VARCHAR},
            modify_time       = #{modifyTime,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>