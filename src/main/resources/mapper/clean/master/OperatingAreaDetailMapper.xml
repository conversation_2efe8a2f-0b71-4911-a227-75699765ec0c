<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.OperatingAreaDetailMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.toonstory.OperatingAreaDetail">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="area_id" property="areaId" jdbcType="BIGINT"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , area_id, area, create_user, create_time
    </sql>

    <insert id="insertOperatingAreaDetail" parameterType="com.wbgame.pojo.clean.toonstory.OperatingAreaDetail">
        insert into gj_b.toon_area_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="areaId != null">
                area_id,
            </if>
            <if test="area != null and area != ''">
                area,
            </if>
            <if test="createUser != null and createUser != ''">
                create_user,
            </if>

            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="areaId != null">
                #{areaId,jdbcType=BIGINT},
            </if>
            <if test="area != null and area != ''">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <update id="updateOperatingAreaDetail" parameterType="com.wbgame.pojo.clean.toonstory.OperatingAreaDetail">
        update gj_b.toon_area_detail
        <set>
            <if test="areaId != null">
                area_id = #{areaId,jdbcType=BIGINT},
            </if>
            <if test="area != null and area != ''">
                area = #{area,jdbcType=VARCHAR},
            </if>

        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="insertBatchOperatingAreaDetail" >

        insert into gj_b.toon_area_detail(area_id,area,create_user,create_time)
        values
        <foreach collection="areaList" item="area" separator=",">
            (#{record.areaId}, #{area}, #{record.createUser}, #{record.createTime})
        </foreach>
    </insert>

    <select id="getOperatingAreaDetailByArea" parameterType="java.lang.String" resultType="java.lang.String" >

        select area from gj_b.toon_area_detail
        where
        <if test="areaId != null">
            area_id != #{areaId} and
        </if>
        area in

        <foreach collection="areaList" item="area" open="(" separator="," close=")">
            #{area}
        </foreach>
    </select>

</mapper>