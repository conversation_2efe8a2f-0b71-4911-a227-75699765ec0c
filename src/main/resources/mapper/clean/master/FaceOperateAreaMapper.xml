<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.FaceOperateAreaMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.face.FaceOperateArea">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="operateAreaName" property="operateAreaName" jdbcType="VARCHAR"/>
        <result column="operateArea" property="operateArea" jdbcType="VARCHAR"/>
        <result column="operateAreaDesc" property="operateAreaDesc" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>

    </resultMap>

    <sql id="Base_Column_List">

        id, operateAreaName, operateArea, operateAreaDesc, status,
        createUser,
        date_format( createTime , '%Y-%m-%d %H:%i:%s' ) createTime,
        updateUser,
        date_format( updateTime, '%Y-%m-%d %H:%i:%s' ) updateTime
    </sql>
    <select id="selectFaceOperateArea" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.face.FaceOperateArea">
        select

        <include refid="Base_Column_List"/>
        from face_operate_area
        <where>

            <if test="status != null and status != ''">

                and status = #{status}
            </if>
            <if test="operateAreaName != null and operateAreaName != ''">
                and operateAreaName = #{operateAreaName}
            </if>
            <if test="operateArea != null and operateArea != ''">
                and operateArea like "%" #{operateArea} "%"
            </if>
        </where>
        order by id desc
    </select>

    <delete id="deleteFaceOperateArea" parameterType="java.lang.Integer">
        delete
        from face_operate_area
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insertFaceOperateArea" parameterType="com.wbgame.pojo.clean.face.FaceOperateArea">
        insert into face_operate_area
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="operateAreaName != null and operateAreaName != ''">
                operateAreaName,
            </if>
            <if test="operateArea != null and operateArea != ''">
                operateArea,
            </if>
            <if test="operateAreaDesc != null and operateAreaDesc != ''">
                operateAreaDesc,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
            <if test="createUser != null and createUser != ''">
                createUser,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="operateAreaName != null and operateAreaName != ''">
                #{operateAreaName,jdbcType=VARCHAR},
            </if>
            <if test="operateArea != null and operateArea != ''">
                #{operateArea,jdbcType=VARCHAR},
            </if>
            <if test="operateAreaDesc != null and operateAreaDesc != ''">
                #{operateAreaDesc,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser},
            </if>
        </trim>
    </insert>


    <update id="updateFaceOperateArea" parameterType="com.wbgame.pojo.clean.face.FaceOperateArea">
        update face_operate_area
        <set>
            <if test="operateAreaName != null and operateAreaName != ''">
                operateAreaName = #{operateAreaName,jdbcType=VARCHAR},
            </if>
            <if test="operateArea != null and operateArea != ''">
                operateArea = #{operateArea,jdbcType=VARCHAR},
            </if>

            <if test="status != null and status != ''">
                status = #{status,jdbcType=VARCHAR},
            </if>

            <if test="updateUser != null and updateUser != ''">
                updateUser = #{updateUser},
            </if>
            operateAreaDesc = #{operateAreaDesc,jdbcType=VARCHAR},
            updateTime = now()
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

</mapper>