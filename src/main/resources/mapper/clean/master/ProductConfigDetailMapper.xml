<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.ProductConfigDetailMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.ProductConfigDetail">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="config_id" property="configId" jdbcType="BIGINT"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="guide_pop" property="guidePop" jdbcType="TINYINT"/>
        <result column="guide_right_hand" property="guideRightHand" jdbcType="TINYINT"/>
        <result column="guide_auto_time" property="guideAutoTime" jdbcType="BIGINT"/>
        <result column="clean_pop" property="cleanPop" jdbcType="TINYINT"/>
        <result column="clean_right_hand" property="cleanRightHand" jdbcType="TINYINT"/>
        <result column="clean_auto_time" property="cleanAutoTime" jdbcType="BIGINT"/>
        <result column="discount_pop" property="discountPop" jdbcType="TINYINT"/>
        <result column="discount_right_hand" property="discountRightHand" jdbcType="TINYINT"/>
        <result column="discount_auto_time" property="discountAutoTime" jdbcType="BIGINT"/>
        <result column="cleaned_pop" property="cleanedPop" jdbcType="TINYINT"/>
        <result column="cleaned_right_hand" property="cleanedRightHand" jdbcType="TINYINT"/>
        <result column="cleaned_auto_time" property="cleanedAutoTime" jdbcType="BIGINT"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
        <result column="modify_user" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , config_id, group_name, guide_pop, guide_right_hand, guide_auto_time, clean_pop,
    clean_right_hand, clean_auto_time, discount_pop, discount_right_hand, discount_auto_time, 
    cleaned_pop, cleaned_right_hand, cleaned_auto_time, create_user, create_time, modify_user, 
    modify_time
    </sql>

    <select id="selectProductConfigDetail" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from gj_b.product_config_detail
        where config_id = #{configId}
    </select>
    <delete id="deleteProductConfigDetail" parameterType="java.lang.Long">
        delete
        from gj_b.product_config_detail
        where config_id in
        <foreach collection="list" item="cfgId" open="(" separator="," close=")">
            #{cfgId}
        </foreach>
    </delete>


    <insert id="insertProductConfigDetail" parameterType="com.wbgame.pojo.clean.ProductConfigDetail">

        <foreach collection="list" item="data" separator=";">

            insert into gj_b.product_config_detail
            <trim prefix="(" suffix=")" suffixOverrides=",">

                <if test="data.configId != null">
                    config_id,
                </if>
                <if test="data.groupName != null and data.groupName != ''">
                    group_name,
                </if>
                <if test="data.guidePop != null">
                    guide_pop,
                </if>
                <if test="data.guideRightHand != null">
                    guide_right_hand,
                </if>
                <if test="data.guideAutoTime != null">
                    guide_auto_time,
                </if>
                <if test="data.cleanPop != null">
                    clean_pop,
                </if>
                <if test="data.cleanRightHand != null">
                    clean_right_hand,
                </if>
                <if test="data.cleanAutoTime != null">
                    clean_auto_time,
                </if>
                <if test="data.discountPop != null">
                    discount_pop,
                </if>
                <if test="data.discountRightHand != null">
                    discount_right_hand,
                </if>
                <if test="data.discountAutoTime != null">
                    discount_auto_time,
                </if>
                <if test="data.cleanedPop != null">
                    cleaned_pop,
                </if>
                <if test="data.cleanedRightHand != null">
                    cleaned_right_hand,
                </if>
                <if test="data.cleanedAutoTime != null">
                    cleaned_auto_time,
                </if>
                <if test="data.createUser != null and data.createUser != ''">
                    create_user,
                </if>
                <if test="data.createTime != null">
                    create_time,
                </if>

                <if test="data.modifyTime != null">
                    modify_time,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">

                <if test="data.configId != null">
                    #{data.configId,jdbcType=BIGINT},
                </if>
                <if test="data.groupName != null and data.groupName != ''">
                    #{data.groupName,jdbcType=VARCHAR},
                </if>
                <if test="data.guidePop != null">
                    #{data.guidePop,jdbcType=TINYINT},
                </if>
                <if test="data.guideRightHand != null">
                    #{data.guideRightHand,jdbcType=TINYINT},
                </if>
                <if test="data.guideAutoTime != null">
                    #{data.guideAutoTime,jdbcType=BIGINT},
                </if>
                <if test="data.cleanPop != null">
                    #{data.cleanPop,jdbcType=TINYINT},
                </if>
                <if test="data.cleanRightHand != null">
                    #{data.cleanRightHand,jdbcType=TINYINT},
                </if>
                <if test="data.cleanAutoTime != null">
                    #{data.cleanAutoTime,jdbcType=BIGINT},
                </if>
                <if test="data.discountPop != null">
                    #{data.discountPop,jdbcType=TINYINT},
                </if>
                <if test="data.discountRightHand != null">
                    #{data.discountRightHand,jdbcType=TINYINT},
                </if>
                <if test="data.discountAutoTime != null">
                    #{data.discountAutoTime,jdbcType=BIGINT},
                </if>
                <if test="data.cleanedPop != null">
                    #{data.cleanedPop,jdbcType=TINYINT},
                </if>
                <if test="data.cleanedRightHand != null">
                    #{data.cleanedRightHand,jdbcType=TINYINT},
                </if>
                <if test="data.cleanedAutoTime != null">
                    #{data.cleanedAutoTime,jdbcType=BIGINT},
                </if>
                <if test="data.createUser != null and data.createUser != ''">
                    #{data.createUser,jdbcType=VARCHAR},
                </if>
                <if test="data.createTime != null">
                    #{data.createTime,jdbcType=BIGINT},
                </if>

                <if test="data.modifyTime != null">
                    #{data.modifyTime,jdbcType=BIGINT},
                </if>
            </trim>
        </foreach>
    </insert>


    <update id="updateProductConfigDetail" parameterType="com.wbgame.pojo.clean.ProductConfigDetail">
        update gj_b.product_config_detail
        <set>
            <if test="configId != null">
                config_id = #{configId,jdbcType=BIGINT},
            </if>
            <if test="groupName != null and groupName != ''">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="guidePop != null">
                guide_pop = #{guidePop,jdbcType=TINYINT},
            </if>
            <if test="guideRightHand != null">
                guide_right_hand = #{guideRightHand,jdbcType=TINYINT},
            </if>
            <if test="guideAutoTime != null">
                guide_auto_time = #{guideAutoTime,jdbcType=BIGINT},
            </if>
            <if test="cleanPop != null">
                clean_pop = #{cleanPop,jdbcType=TINYINT},
            </if>
            <if test="cleanRightHand != null">
                clean_right_hand = #{cleanRightHand,jdbcType=TINYINT},
            </if>
            <if test="cleanAutoTime != null">
                clean_auto_time = #{cleanAutoTime,jdbcType=BIGINT},
            </if>
            <if test="discountPop != null">
                discount_pop = #{discountPop,jdbcType=TINYINT},
            </if>
            <if test="discountRightHand != null">
                discount_right_hand = #{discountRightHand,jdbcType=TINYINT},
            </if>
            <if test="discountAutoTime != null">
                discount_auto_time = #{discountAutoTime,jdbcType=BIGINT},
            </if>
            <if test="cleanedPop != null">
                cleaned_pop = #{cleanedPop,jdbcType=TINYINT},
            </if>
            <if test="cleanedRightHand != null">
                cleaned_right_hand = #{cleanedRightHand,jdbcType=TINYINT},
            </if>
            <if test="cleanedAutoTime != null">
                cleaned_auto_time = #{cleanedAutoTime,jdbcType=BIGINT},
            </if>
            <if test="modifyUser != null and modifyUser != ''">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>