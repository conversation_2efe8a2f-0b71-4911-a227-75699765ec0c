<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.IdiomUserInfoMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.IdiomUserInfoVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="prj_id" property="prjId" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="pkg" property="pkg" jdbcType="VARCHAR"/>
        <result column="wx_profile" property="wxProfile" jdbcType="VARCHAR"/>
        <result column="wx_name" property="wxName" jdbcType="VARCHAR"/>
        <result column="random_name" property="randomName" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="wx_open_id" property="wxOpenId" jdbcType="VARCHAR"/>
        <result column="is_new" property="isNew" jdbcType="TINYINT"/>
        <result column="sign_total" property="signTotal" jdbcType="INTEGER"/>
        <result column="sign_time" property="signTime" jdbcType="BIGINT"/>
        <result column="current_level" property="currentLevel" jdbcType="INTEGER"/>
        <result column="android_id" property="androidId" jdbcType="VARCHAR"/>
        <result column="cur_montree_num" property="curMontreeNum" jdbcType="INTEGER"/>
        <result column="cur_click_num" property="curClickNum" jdbcType="INTEGER"/>
        <result column="cur_shake_num" property="curShakeNum" jdbcType="INTEGER"/>
        <result column="homepage_update_time" property="homepageUpdateTime" jdbcType="BIGINT"/>
        <result column="cur_withdraw_limit" property="curWithdrawLimit" jdbcType="INTEGER"/>
        <result column="withdraw_time" property="withdrawTime" jdbcType="BIGINT"/>
        <result column="user_type" property="userType" jdbcType="TINYINT"/>
        <result column="digital_id" property="digitalId" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
        <result column="modify_user" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , prj_id, app_id, pkg, wx_profile, wx_name, random_name, user_id, wx_open_id, is_new,
    sign_total, sign_time, current_level, android_id, cur_montree_num, cur_click_num, 
    cur_shake_num, homepage_update_time, cur_withdraw_limit, withdraw_time, user_type, 
    digital_id, create_user, create_time, modify_user, modify_time
    </sql>


    <delete id="deleteById" parameterType="java.lang.Long">
        delete
        from gj_b.idiom_user_info
        where id = #{id,jdbcType=BIGINT}
    </delete>


    <insert id="insertIdiomUserInfo" parameterType="com.wbgame.pojo.clean.IdiomUserInfo">
        insert into gj_b.idiom_user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="prjId != null">
                prj_id,
            </if>
            <if test="appId != null">
                app_id,
            </if>
            <if test="pkg != null">
                pkg,
            </if>
            <if test="wxProfile != null">
                wx_profile,
            </if>
            <if test="wxName != null">
                wx_name,
            </if>
            <if test="randomName != null">
                random_name,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="wxOpenId != null">
                wx_open_id,
            </if>
            <if test="isNew != null">
                is_new,
            </if>
            <if test="signTotal != null">
                sign_total,
            </if>
            <if test="signTime != null">
                sign_time,
            </if>
            <if test="currentLevel != null">
                current_level,
            </if>
            <if test="androidId != null">
                android_id,
            </if>
            <if test="curMontreeNum != null">
                cur_montree_num,
            </if>
            <if test="curClickNum != null">
                cur_click_num,
            </if>
            <if test="curShakeNum != null">
                cur_shake_num,
            </if>
            <if test="homepageUpdateTime != null">
                homepage_update_time,
            </if>
            <if test="curWithdrawLimit != null">
                cur_withdraw_limit,
            </if>
            <if test="withdrawTime != null">
                withdraw_time,
            </if>
            <if test="userType != null">
                user_type,
            </if>
            <if test="digitalId != null">
                digital_id,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyUser != null">
                modify_user,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="prjId != null">
                #{prjId,jdbcType=VARCHAR},
            </if>
            <if test="appId != null">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="pkg != null">
                #{pkg,jdbcType=VARCHAR},
            </if>
            <if test="wxProfile != null">
                #{wxProfile,jdbcType=VARCHAR},
            </if>
            <if test="wxName != null">
                #{wxName,jdbcType=VARCHAR},
            </if>
            <if test="randomName != null">
                #{randomName,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="wxOpenId != null">
                #{wxOpenId,jdbcType=VARCHAR},
            </if>
            <if test="isNew != null">
                #{isNew,jdbcType=TINYINT},
            </if>
            <if test="signTotal != null">
                #{signTotal,jdbcType=INTEGER},
            </if>
            <if test="signTime != null">
                #{signTime,jdbcType=BIGINT},
            </if>
            <if test="currentLevel != null">
                #{currentLevel,jdbcType=INTEGER},
            </if>
            <if test="androidId != null">
                #{androidId,jdbcType=VARCHAR},
            </if>
            <if test="curMontreeNum != null">
                #{curMontreeNum,jdbcType=INTEGER},
            </if>
            <if test="curClickNum != null">
                #{curClickNum,jdbcType=INTEGER},
            </if>
            <if test="curShakeNum != null">
                #{curShakeNum,jdbcType=INTEGER},
            </if>
            <if test="homepageUpdateTime != null">
                #{homepageUpdateTime,jdbcType=BIGINT},
            </if>
            <if test="curWithdrawLimit != null">
                #{curWithdrawLimit,jdbcType=INTEGER},
            </if>
            <if test="withdrawTime != null">
                #{withdrawTime,jdbcType=BIGINT},
            </if>
            <if test="userType != null">
                #{userType,jdbcType=TINYINT},
            </if>
            <if test="digitalId != null">
                #{digitalId,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="modifyUser != null">
                #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateIdiomUserInfo" parameterType="com.wbgame.pojo.clean.IdiomUserInfo">
        update <choose>
        <when test="environment != 'test'">
            gj_b.idiom_user_info
        </when>
        <otherwise>
            idiom_user_info
        </otherwise>
    </choose>

        <set>

            <if test="record.userType != null">
                user_type = #{record.userType,jdbcType=TINYINT},
            </if>
            <if test="record.modifyUser != null and record.modifyUser != ''">
                modify_user = #{record.modifyUser,jdbcType=TINYINT},
            </if>

            <if test="record.modifyTime != null">
                modify_time = #{record.modifyTime,jdbcType=TINYINT},
            </if>

        </set>
        where id = #{record.id,jdbcType=BIGINT}
    </update>

    <select id="selectIdiomUserInfo" resultMap="BaseResultMap">

        SELECT
            id,
            app_id,
            prj_id,
            android_id,
            user_id,
            wx_name,
            user_type,
            create_user,
        from_unixtime(create_time / 1000, '%Y-%m-%d %H:%i:%s') create_time,
        modify_user,
        from_unixtime(modify_time / 1000, '%Y-%m-%d %H:%i:%s') modify_time
        FROM
        <choose>
            <when test="environment != 'test'">
                gj_b.idiom_user_info
            </when>
            <otherwise>
                idiom_user_info
            </otherwise>
        </choose>

        <where>

            <if test="dto.prjId != null and dto.prjId != ''">
                AND prj_id = #{dto.prjId}
            </if>

            <if test="dto.androidId != null and dto.androidId != ''">
                AND android_id = #{dto.androidId}
            </if>

            <if test="dto.userId != null and dto.userId != ''">
                AND user_id = #{dto.userId}
            </if>

            <if test="dto.userType != null">
                AND user_type = #{dto.userType}
            </if>

            <if test="dto.wxName != null and dto.wxName != ''">
                AND wx_name = #{dto.wxName}
            </if>

            <if test="dto.appidList != null and dto.appidList.size > 0">
                AND app_id IN
                <foreach collection="dto.appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

        </where>

        order by id desc

    </select>


    <select id="selectIdiomUser" resultMap="BaseResultMap">

        SELECT
        app_id,
        prj_id,
        android_id,
        user_id,
        wx_name,
        user_type
        FROM
        <choose>
            <when test="environment != 'test'">
                gj_b.idiom_user_info
            </when>
            <otherwise>
                idiom_user_info
            </otherwise>
        </choose>

        where id  = #{id}

        limit 1

    </select>

</mapper>