<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.IdiomConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.IdiomConfigVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="prj_id" property="prjId" jdbcType="VARCHAR"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="cur_montree_num" property="curMontreeNum" jdbcType="INTEGER"/>
        <result column="cur_click_num" property="curClickNum" jdbcType="INTEGER"/>
        <result column="cur_shake_num" property="curShakeNum" jdbcType="INTEGER"/>
        <result column="cur_withdraw_limit" property="curWithdrawLimit" jdbcType="INTEGER"/>
        <result column="first_redpack" property="firstRedpack" jdbcType="INTEGER"/>
        <result column="open_ecpm" property="openEcpm" jdbcType="INTEGER"/>
        <result column="video_ecpm" property="videoEcpm" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , app_id, prj_id, channel, cur_montree_num, cur_click_num, cur_shake_num, cur_withdraw_limit,
        first_redpack, open_ecpm, video_ecpm, date_format(create_time, '%Y-%m-%d %H:%i:%s') create_time,
        date_format(update_time, '%Y-%m-%d %H:%i:%s') update_time,
        status, create_user, update_user
    </sql>

    <select id="selectIdiomConfig" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from
        <choose>
            <when test="environment != 'test'">
                gj_b.idiom_config
            </when>
            <otherwise>
                idiom_config
            </otherwise>
        </choose>
        <where>

            <if test="dto.channel != null and dto.channel != ''">

                and channel in (${dto.channel})
            </if>
            <if test="dto.prjId != null and dto.prjId != ''">

                and prj_id = #{dto.prjId}
            </if>


            <if test="dto.appidList != null and dto.appidList.size > 0">
                AND app_id IN
                <foreach collection="dto.appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>

        order by id desc
    </select>
    <delete id="deleteById" >
        delete
        from
        <choose>
            <when test="environment != 'test'">
                gj_b.idiom_config
            </when>
            <otherwise>
                idiom_config
            </otherwise>
        </choose>
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertIdiomConfig" parameterType="com.wbgame.pojo.clean.IdiomConfig"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into
        <choose>
            <when test="environment != 'test'">
                gj_b.idiom_config
            </when>
            <otherwise>
                idiom_config
            </otherwise>
        </choose>
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="record.appId != null">
                app_id,
            </if>
            <if test="record.prjId != null">
                prj_id,
            </if>
            <if test="record.channel != null">
                channel,
            </if>
            <if test="record.curMontreeNum != null">
                cur_montree_num,
            </if>
            <if test="record.curClickNum != null">
                cur_click_num,
            </if>
            <if test="record.curShakeNum != null">
                cur_shake_num,
            </if>
            <if test="record.curWithdrawLimit != null">
                cur_withdraw_limit,
            </if>
            <if test="record.firstRedpack != null">
                first_redpack,
            </if>
            <if test="record.openEcpm != null">
                open_ecpm,
            </if>
            <if test="record.videoEcpm != null">
                video_ecpm,
            </if>
            <if test="record.status != null">
                status,
            </if>
            <if test="record.createUser != null and record.createUser != ''">
                create_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="record.appId != null">
                #{record.appId,jdbcType=VARCHAR},
            </if>
            <if test="record.prjId != null">
                #{record.prjId,jdbcType=VARCHAR},
            </if>
            <if test="record.channel != null">
                #{record.channel,jdbcType=VARCHAR},
            </if>
            <if test="record.curMontreeNum != null">
                #{record.curMontreeNum,jdbcType=INTEGER},
            </if>
            <if test="record.curClickNum != null">
                #{record.curClickNum,jdbcType=INTEGER},
            </if>
            <if test="record.curShakeNum != null">
                #{record.curShakeNum,jdbcType=INTEGER},
            </if>
            <if test="record.curWithdrawLimit != null">
                #{record.curWithdrawLimit,jdbcType=INTEGER},
            </if>
            <if test="record.firstRedpack != null">
                #{record.firstRedpack,jdbcType=INTEGER},
            </if>
            <if test="record.openEcpm != null">
                #{record.openEcpm,jdbcType=INTEGER},
            </if>
            <if test="record.videoEcpm != null">
                #{record.videoEcpm,jdbcType=INTEGER},
            </if>
            <if test="record.status != null">
                #{record.status},
            </if>
            <if test="record.createUser != null and record.createUser != ''">
                #{record.createUser},
            </if>
        </trim>
    </insert>

    <update id="updateIdiomConfigMapperById" parameterType="com.wbgame.pojo.clean.IdiomConfig">
        update
        <choose>
            <when test="environment != 'test'">
                gj_b.idiom_config
            </when>
            <otherwise>
                idiom_config
            </otherwise>
        </choose>
        <set>

            <if test="record.curMontreeNum != null">
                cur_montree_num = #{record.curMontreeNum,jdbcType=INTEGER},
            </if>
            <if test="record.curClickNum != null">
                cur_click_num = #{record.curClickNum,jdbcType=INTEGER},
            </if>
            <if test="record.curShakeNum != null">
                cur_shake_num = #{record.curShakeNum,jdbcType=INTEGER},
            </if>
            <if test="record.curWithdrawLimit != null">
                cur_withdraw_limit = #{record.curWithdrawLimit,jdbcType=INTEGER},
            </if>
            <if test="record.firstRedpack != null">
                first_redpack = #{record.firstRedpack,jdbcType=INTEGER},
            </if>
            <if test="record.openEcpm != null">
                open_ecpm = #{record.openEcpm,jdbcType=INTEGER},
            </if>
            <if test="record.videoEcpm != null">
                video_ecpm = #{record.videoEcpm,jdbcType=INTEGER},
            </if>
            <if test="record.status != null">
                status = #{record.status},
            </if>
            <if test="record.updateUser != null and record.updateUser != ''">
                update_user = #{record.updateUser},
            </if>
        </set>
        where id = #{record.id,jdbcType=BIGINT}
    </update>

    <update id="batchUpdateIomConfig" parameterType="com.wbgame.pojo.clean.IdiomConfig">
        update
        <choose>
            <when test="environment != 'test'">
                gj_b.idiom_config
            </when>
            <otherwise>
                idiom_config
            </otherwise>
        </choose>
        <set>

            <if test="record.curMontreeNum != null">
                cur_montree_num = #{record.curMontreeNum,jdbcType=INTEGER},
            </if>
            <if test="record.curClickNum != null">
                cur_click_num = #{record.curClickNum,jdbcType=INTEGER},
            </if>
            <if test="record.curShakeNum != null">
                cur_shake_num = #{record.curShakeNum,jdbcType=INTEGER},
            </if>
            <if test="record.curWithdrawLimit != null">
                cur_withdraw_limit = #{record.curWithdrawLimit,jdbcType=INTEGER},
            </if>
            <if test="record.firstRedpack != null">
                first_redpack = #{record.firstRedpack,jdbcType=INTEGER},
            </if>
            <if test="record.openEcpm != null">
                open_ecpm = #{record.openEcpm,jdbcType=INTEGER},
            </if>
            <if test="record.videoEcpm != null">
                video_ecpm = #{record.videoEcpm,jdbcType=INTEGER},
            </if>
            <if test="record.status != null">
                status = #{record.status},
            </if>
            <if test="record.updateUser != null and record.updateUser != ''">
                update_user = #{record.updateUser},
            </if>
            update_time = current_timestamp
        </set>
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getIdiomConfig" resultMap="BaseResultMap">

        select <include refid="Base_Column_List"/> from
        <choose>
            <when test="environment != 'test'">
                gj_b.idiom_config
            </when>
            <otherwise>
                idiom_config
            </otherwise>
        </choose>
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </select>

    <select id="getIdiomConfigExits" resultType="java.lang.Integer" >

        select 1

        from
        <choose>
            <when test="environment != 'test'">
                gj_b.idiom_config
            </when>
            <otherwise>
                idiom_config
            </otherwise>
        </choose>

        <where>
            <if test="record.appId != null">
                and app_id = #{record.appId,jdbcType=VARCHAR}
            </if>
            <if test="record.prjId != null and record.prjId != ''">
                and prj_id = #{record.prjId,jdbcType=VARCHAR}
            </if>
            <if test="record.channel != null and record.channel != ''">
                and  channel = #{record.channel,jdbcType=VARCHAR}
            </if>
        </where>
        limit 1
    </select>

</mapper>