<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.CoinsGroupConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.CoinsGroupConfigVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="group" property="group" jdbcType="VARCHAR"/>
        <result column="withdrawMoneys" property="withdrawMoneys" jdbcType="LONGVARCHAR"/>
        <result column="newUserAward" property="newUserAward" jdbcType="DOUBLE"/>
        <result column="isFreeze" property="isFreeze" jdbcType="VARCHAR"/>
        <result column="activeDays" property="activeDays" jdbcType="INTEGER"/>
        <result column="convertStep" property="convertStep" jdbcType="INTEGER"/>
        <result column="lookAdNum" property="lookAdNum" jdbcType="INTEGER"/>
        <result column="auditDays" property="auditDays" jdbcType="INTEGER"/>
        <result column="auditConvertStep" property="auditConvertStep" jdbcType="INTEGER"/>
        <result column="auditLookAdNum" property="auditLookAdNum" jdbcType="INTEGER"/>
        <result column="lowPrice" property="lowPrice" jdbcType="DOUBLE"/>
        <result column="highPrice" property="highPrice" jdbcType="DOUBLE"/>
        <result column="convertPriceUnit" property="convertPriceUnit" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , `group`, newUserAward, isFreeze, activeDays, convertStep, lookAdNum, auditDays,
    auditConvertStep, auditLookAdNum, lowPrice, highPrice, convertPriceUnit,withdrawMoneys
    </sql>


    <delete id="deleteByIdList" parameterType="java.lang.Integer">
        delete from coins_group_config
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
        #{id}
        </foreach>
    </delete>


    <insert id="insertCoinsGroupConfig" parameterType="com.wbgame.pojo.clean.CoinsGroupConfig">
        insert into coins_group_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="group != null">
                `group`,
            </if>
            <if test="newUserAward != null">
                newUserAward,
            </if>
            <if test="isFreeze != null">
                isFreeze,
            </if>
            <if test="activeDays != null">
                activeDays,
            </if>
            <if test="convertStep != null">
                convertStep,
            </if>
            <if test="lookAdNum != null">
                lookAdNum,
            </if>
            <if test="auditDays != null">
                auditDays,
            </if>
            <if test="auditConvertStep != null">
                auditConvertStep,
            </if>
            <if test="auditLookAdNum != null">
                auditLookAdNum,
            </if>
            <if test="lowPrice != null">
                lowPrice,
            </if>
            <if test="highPrice != null">
                highPrice,
            </if>
            <if test="convertPriceUnit != null">
                convertPriceUnit,
            </if>
            <if test="withdrawMoneys != null">
                withdrawMoneys,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="group != null">
                #{group,jdbcType=VARCHAR},
            </if>
            <if test="newUserAward != null">
                #{newUserAward,jdbcType=DOUBLE},
            </if>
            <if test="isFreeze != null">
                #{isFreeze,jdbcType=VARCHAR},
            </if>
            <if test="activeDays != null">
                #{activeDays,jdbcType=INTEGER},
            </if>
            <if test="convertStep != null">
                #{convertStep,jdbcType=INTEGER},
            </if>
            <if test="lookAdNum != null">
                #{lookAdNum,jdbcType=INTEGER},
            </if>
            <if test="auditDays != null">
                #{auditDays,jdbcType=INTEGER},
            </if>
            <if test="auditConvertStep != null">
                #{auditConvertStep,jdbcType=INTEGER},
            </if>
            <if test="auditLookAdNum != null">
                #{auditLookAdNum,jdbcType=INTEGER},
            </if>
            <if test="lowPrice != null">
                #{lowPrice,jdbcType=DOUBLE},
            </if>
            <if test="highPrice != null">
                #{highPrice,jdbcType=DOUBLE},
            </if>
            <if test="convertPriceUnit != null">
                #{convertPriceUnit,jdbcType=INTEGER},
            </if>
            <if test="withdrawMoneys != null">
                #{withdrawMoneys,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>


    <update id="updateCoinsGroupConfig" parameterType="com.wbgame.pojo.clean.CoinsGroupConfig">
        update coins_group_config
        <set>
            <if test="group != null">
                `group` = #{group,jdbcType=VARCHAR},
            </if>
            <if test="newUserAward != null">
                newUserAward = #{newUserAward,jdbcType=DOUBLE},
            </if>
            <if test="isFreeze != null">
                isFreeze = #{isFreeze,jdbcType=VARCHAR},
            </if>
            <if test="activeDays != null">
                activeDays = #{activeDays,jdbcType=INTEGER},
            </if>
            <if test="convertStep != null">
                convertStep = #{convertStep,jdbcType=INTEGER},
            </if>
            <if test="lookAdNum != null">
                lookAdNum = #{lookAdNum,jdbcType=INTEGER},
            </if>
            <if test="auditDays != null">
                auditDays = #{auditDays,jdbcType=INTEGER},
            </if>
            <if test="auditConvertStep != null">
                auditConvertStep = #{auditConvertStep,jdbcType=INTEGER},
            </if>
            <if test="auditLookAdNum != null">
                auditLookAdNum = #{auditLookAdNum,jdbcType=INTEGER},
            </if>
            <if test="lowPrice != null">
                lowPrice = #{lowPrice,jdbcType=DOUBLE},
            </if>
            <if test="highPrice != null">
                highPrice = #{highPrice,jdbcType=DOUBLE},
            </if>
            <if test="convertPriceUnit != null">
                convertPriceUnit = #{convertPriceUnit,jdbcType=INTEGER},
            </if>
            <if test="withdrawMoneys != null">
                withdrawMoneys = #{withdrawMoneys,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>


    <select id="selectCoinsGroupConfig" parameterType="com.wbgame.pojo.clean.CoinsGroupConfigDTO"
            resultMap="BaseResultMap">

        select <include refid="Base_Column_List"/> from  coins_group_config

    </select>
</mapper>