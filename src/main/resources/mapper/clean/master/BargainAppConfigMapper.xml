<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.BargainAppConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.chop.BargainAppConfig">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="prjid" property="prjid" jdbcType="VARCHAR"/>
        <result column="account" property="account" jdbcType="VARCHAR"/>
        <result column="predictAdd" property="predictAdd" jdbcType="INTEGER"/>
        <result column="cost" property="cost" jdbcType="INTEGER"/>
        <result column="behaviorRate" property="behaviorRate" jdbcType="INTEGER"/>
        <result column="deductionCoefficient" property="deductionCoefficient" jdbcType="DECIMAL"/>
        <result column="switchingCoefficient" property="switchingCoefficient" jdbcType="DECIMAL"/>
        <result column="costCoefficient" property="costCoefficient" jdbcType="DECIMAL"/>
        <result column="rc" property="rc" jdbcType="INTEGER"/>
        <result column="defaultEcpmVideo" property="defaultEcpmVideo" jdbcType="INTEGER"/>
        <result column="defaultEcpmPlaque" property="defaultEcpmPlaque" jdbcType="INTEGER"/>
        <result column="defaultEcpmMsg" property="defaultEcpmMsg" jdbcType="INTEGER"/>
        <result column="defaultNum" property="defaultNum" jdbcType="INTEGER"/>
        <result column="InitCost" property="initCost" jdbcType="VARCHAR"/>
        <result column="avgPurchaseCost" property="avgPurchaseCost" jdbcType="DECIMAL"/>
        <result column="avgCommodityPrice" property="avgCommodityPrice" jdbcType="DECIMAL"/>
        <result column="proCfg" property="proCfg" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , appid, prjid, account, predictAdd, cost, behaviorRate, deductionCoefficient,
        switchingCoefficient, costCoefficient, rc, defaultEcpmVideo, defaultEcpmPlaque, defaultEcpmMsg,
        defaultNum, InitCost, avgPurchaseCost, avgCommodityPrice,proCfg,status ,createUser,updateUser,
        date_format(createTime, '%Y-%m-%d %H:%i:%s') createTime,
        date_format(updateTime, '%Y-%m-%d %H:%i:%s') updateTime

    </sql>

    <select id="selectBargainAppConfig" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.clean.chop.BargainAppConfig">
        select
        <include refid="Base_Column_List"/>

        from bargain_app_config

        <where>

            <if test="account != null and account != ''">

                and account = #{account}
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

            <if test="prjidList != null and prjidList.size > 0">
                AND prjid IN
                <foreach collection="prjidList" item="prjid" open="(" separator="," close=")">
                    #{prjid}
                </foreach>
            </if>
        </where>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>

            <otherwise>
                order by id desc
            </otherwise>
        </choose>

    </select>

    <delete id="deleteBargainAppConfig" parameterType="java.lang.Integer">
        delete
        from bargain_app_config
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertBargainAppConfig" parameterType="com.wbgame.pojo.clean.chop.BargainAppConfig"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into bargain_app_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="appid != null and appid != ''">
                appid,
            </if>
            <if test="prjid != null and prjid != ''">
                prjid,
            </if>
            <if test="account != null and account != ''">
                account,
            </if>
            <if test="predictAdd != null">
                predictAdd,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="cost != null">
                cost,
            </if>
            <if test="behaviorRate != null">
                behaviorRate,
            </if>
            <if test="deductionCoefficient != null">
                deductionCoefficient,
            </if>
            <if test="switchingCoefficient != null">
                switchingCoefficient,
            </if>
            <if test="costCoefficient != null">
                costCoefficient,
            </if>
            <if test="rc != null">
                rc,
            </if>
            <if test="defaultEcpmVideo != null">
                defaultEcpmVideo,
            </if>
            <if test="defaultEcpmPlaque != null">
                defaultEcpmPlaque,
            </if>
            <if test="defaultEcpmMsg != null">
                defaultEcpmMsg,
            </if>
            <if test="defaultNum != null">
                defaultNum,
            </if>
            <if test="initCost != null and initCost != ''">
                InitCost,
            </if>
            <if test="avgPurchaseCost != null and avgPurchaseCost != ''">
                avgPurchaseCost,
            </if>
            <if test="avgCommodityPrice != null and avgCommodityPrice != ''">
                avgCommodityPrice,
            </if>
            <if test="proCfg != null and proCfg != ''">
                proCfg,
            </if>
            <if test="createUser != null and createUser != ''">
                createUser,
            </if>


        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="appid != null and appid != ''">
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="prjid != null and prjid != ''">
                #{prjid,jdbcType=VARCHAR},
            </if>
            <if test="account != null and account != ''">
                #{account,jdbcType=VARCHAR},
            </if>
            <if test="predictAdd != null">
                #{predictAdd,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="cost != null">
                #{cost,jdbcType=INTEGER},
            </if>
            <if test="behaviorRate != null">
                #{behaviorRate,jdbcType=INTEGER},
            </if>
            <if test="deductionCoefficient != null">
                #{deductionCoefficient,jdbcType=INTEGER},
            </if>
            <if test="switchingCoefficient != null">
                #{switchingCoefficient,jdbcType=DECIMAL},
            </if>
            <if test="costCoefficient != null">
                #{costCoefficient,jdbcType=DECIMAL},
            </if>
            <if test="rc != null">
                #{rc,jdbcType=INTEGER},
            </if>
            <if test="defaultEcpmVideo != null">
                #{defaultEcpmVideo,jdbcType=INTEGER},
            </if>
            <if test="defaultEcpmPlaque != null">
                #{defaultEcpmPlaque,jdbcType=INTEGER},
            </if>
            <if test="defaultEcpmMsg != null">
                #{defaultEcpmMsg,jdbcType=INTEGER},
            </if>
            <if test="defaultNum != null">
                #{defaultNum,jdbcType=INTEGER},
            </if>
            <if test="initCost != null and initCost != ''">
                #{initCost,jdbcType=VARCHAR},
            </if>
            <if test="avgPurchaseCost != null and avgPurchaseCost != ''">
                #{avgPurchaseCost,jdbcType=DECIMAL},
            </if>
            <if test="avgCommodityPrice != null and avgCommodityPrice != ''">
                #{avgCommodityPrice,jdbcType=DECIMAL},
            </if>
            <if test="proCfg != null and proCfg != ''">
                #{proCfg,jdbcType=LONGVARCHAR},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser},
            </if>


        </trim>
    </insert>

    <update id="updateBargainAppConfig" parameterType="com.wbgame.pojo.clean.chop.BargainAppConfig">
        update bargain_app_config
        <set>
            <if test="appid != null">
                appid = #{appid,jdbcType=VARCHAR},
            </if>
            <if test="prjid != null">
                prjid = #{prjid,jdbcType=VARCHAR},
            </if>
            <if test="account != null">
                account = #{account,jdbcType=VARCHAR},
            </if>
            <if test="predictAdd != null">
                predictAdd = #{predictAdd,jdbcType=INTEGER},
            </if>
            <if test="cost != null">
                cost = #{cost,jdbcType=INTEGER},
            </if>
            <if test="behaviorRate != null">
                behaviorRate = #{behaviorRate,jdbcType=INTEGER},
            </if>
            <if test="deductionCoefficient != null">
                deductionCoefficient = #{deductionCoefficient,jdbcType=INTEGER},
            </if>
            <if test="switchingCoefficient != null">
                switchingCoefficient = #{switchingCoefficient,jdbcType=DECIMAL},
            </if>
            <if test="costCoefficient != null">
                costCoefficient = #{costCoefficient,jdbcType=DECIMAL},
            </if>
            <if test="rc != null">
                rc = #{rc,jdbcType=INTEGER},
            </if>
            <if test="defaultEcpmVideo != null">
                defaultEcpmVideo = #{defaultEcpmVideo,jdbcType=INTEGER},
            </if>
            <if test="defaultEcpmPlaque != null">
                defaultEcpmPlaque = #{defaultEcpmPlaque,jdbcType=INTEGER},
            </if>
            <if test="defaultEcpmMsg != null">
                defaultEcpmMsg = #{defaultEcpmMsg,jdbcType=INTEGER},
            </if>
            <if test="defaultNum != null">
                defaultNum = #{defaultNum,jdbcType=INTEGER},
            </if>
            <if test="initCost != null">
                InitCost = #{initCost,jdbcType=VARCHAR},
            </if>
            <if test="avgPurchaseCost != null">
                avgPurchaseCost = #{avgPurchaseCost,jdbcType=DECIMAL},
            </if>
            <if test="avgCommodityPrice != null">
                avgCommodityPrice = #{avgCommodityPrice,jdbcType=DECIMAL},
            </if>
            <if test="proCfg != null">
                proCfg = #{proCfg,jdbcType=LONGVARCHAR},
            </if>
            <if test="updateUser != null and updateUser != ''">
                updateUser = #{updateUser},
            </if>
            <if test="status != null">
                status = #{status},
            </if>

            updateTime = current_timestamp
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>



</mapper>