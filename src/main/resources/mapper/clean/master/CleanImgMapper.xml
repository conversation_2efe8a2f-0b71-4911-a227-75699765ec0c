<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.CleanImgMapper">

    <select id="selectImgTypeList" parameterType="com.wbgame.pojo.clean.img.ImgTypeVo" resultType="com.wbgame.pojo.clean.img.ImgTypeVo">
        select * from super_img_type
        where 1=1 AND dataType = 1
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectImgTypeByType" resultType="com.wbgame.pojo.clean.img.ImgTypeVo">
        select * from super_img_type where `type` = #{type} AND dataType = 1
    </select>

    <select id="selectImgTypeByTypeSort" resultType="com.wbgame.pojo.clean.img.ImgTypeVo">
        select * from super_img_type where `typeSort` = #{typeSort} AND dataType = 1
    </select>

    <select id="selectImgTypeById" resultType="com.wbgame.pojo.clean.img.ImgTypeVo">
        select * from super_img_type where 1=1 and  `id` = #{id} AND dataType = 1
    </select>

    <delete id="deleteImgType" parameterType="com.wbgame.pojo.clean.img.ImgTypeVo">
        delete from super_img_type where id = #{id} AND dataType = 1
    </delete>
    <insert id="insertImgType" parameterType="com.wbgame.pojo.clean.img.ImgTypeVo">
        insert into super_img_type (`type`, typeSort,dataType,status,modifyUser,createTime)
        values  (#{type,jdbcType=VARCHAR}, #{typeSort,jdbcType=VARCHAR},1,#{status,jdbcType=VARCHAR},
                 #{modifyUser,jdbcType=VARCHAR},NOW())
    </insert>
    <update id="updateImgType" parameterType="com.wbgame.pojo.clean.img.ImgTypeVo">
        update super_img_type
        set `type` = #{type,jdbcType=VARCHAR},
            typeSort = #{typeSort,jdbcType=VARCHAR},
            modifyTime = NOW(),
            modifyUser = #{modifyUser,jdbcType=VARCHAR},
            status = #{status,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR} AND dataType = 1
    </update>

    <select id="selectImgConfigList" resultType="com.wbgame.pojo.clean.img.ImgConfigVo">
        select
        *
        from super_img_config
        where 1=1 AND dataType = 1
        <if test="appid != null and appid != ''">
            and appid = #{appid,jdbcType=VARCHAR}
        </if>
        <if test="prjid != null and prjid != ''">
            and prjid = #{prjid,jdbcType=VARCHAR}
        </if>
        <if test="cha != null and cha != ''">
            and cha = #{cha,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
        <if test="typeSort != null and typeSort != ''">
            and typeSort = #{typeSort,jdbcType=VARCHAR}
        </if>
        order by typeSort,createTime desc
    </select>
    <delete id="deleteImgConfig" parameterType="com.wbgame.pojo.clean.img.ImgConfigVo">
        delete from super_img_config where id in
        <foreach  item="item" collection="ids" index="index"  open="(" separator="," close=")">
            #{item}
        </foreach>
        AND dataType = 1
    </delete>

    <insert id="insertImgConfig" parameterType="com.wbgame.pojo.clean.img.ImgConfigVo">
    insert into super_img_config (`appid`,
	`cha`,
	`prjid`,
	`type`,
	`typeSort`,
	`name`,
	`imgSort`,
	`url`,
	`imgUseCount`,
	`imgLikeCount`,
	`status`,
	`dataType`,
	`createTime`,
	`modifyUser`,
	`modelUrl`
	)
    values (#{appid},#{cha},#{prjid},#{type},#{typeSort},#{name},#{imgSort},#{url},#{imgUseCount},#{imgLikeCount},#{status},1,NOW(),#{modifyUser},#{modelUrl})
    </insert>


    <update id="updateImgConfig" parameterType="com.wbgame.pojo.clean.img.ImgConfigVo">
    update super_img_config
    set  `appid` = #{appid},
		 `cha` = #{cha},
		 `prjid` = #{prjid},
		 `type` = #{type},
		 `typeSort` = #{typeSort},
		 `name` = #{name},
		 `imgSort` = #{imgSort},
		 `url` = #{url},
		 `imgUseCount` = #{imgUseCount},
		 `imgLikeCount` = #{imgLikeCount},
		 `status` = #{status},
		 `modifyUser` = #{modifyUser},
		 `modifyTime` = NOW(),
		 `modelUrl` = #{modelUrl}
    where id = #{id} AND dataType = 1
    </update>


    <update id="updateImgConfigStatus" parameterType="com.wbgame.pojo.clean.img.ImgConfigVo">
        update super_img_config set status = #{status} where id=#{id} AND dataType = 1
    </update>


    <update id="updateImgConfigStatusBatch" parameterType="com.wbgame.pojo.clean.img.ImgConfigVo">
        update super_img_config set status = #{status} where id in
        <foreach  item="item" collection="ids" index="index"  open="(" separator="," close=")">
            #{item}
        </foreach>
        AND dataType = 1
    </update>

    <update id="updateImgConfigTypeSortBatch" parameterType="com.wbgame.pojo.clean.img.ImgConfigVo">
        update super_img_config set `type` = #{type} ,typeSort =#{typeSort}, modifyTime = now() where id in
        <foreach  item="item" collection="ids" index="index"  open="(" separator="," close=")">
            #{item}
        </foreach>
        AND dataType = 1
    </update>



    <select id="selectMaxImgSortId" resultType="java.lang.Integer" parameterType="java.lang.String">
        select max(imgSort) from  super_img_config where `typeSort` = #{typeSort} AND dataType = 1
    </select>


    <insert id="copyImgConfigBatch" parameterType="java.util.List">
        insert into super_img_config (appid,cha,prjid,`type`,typeSort,`name`,imgSort,url,imgUseCount,imgLikeCount,status,dataType,createTime,modifyUser,modelUrl)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
                #{item.appid},#{item.cha},#{item.prjid},#{item.type},#{item.typeSort},
                #{item.name},#{item.imgSort},#{item.url},#{item.imgUseCount},#{item.imgLikeCount},
                #{item.status},1,NOW(),#{item.modifyUser},#{item.modelUrl}
            )
        </foreach>
    </insert>

    <delete id="deleteImgConfigByTypeSort" parameterType="java.lang.String">
        delete  from super_img_config where typeSort = #{typeSort} AND dataType = 1
    </delete>

    <update id="updateImgConfigByTypeChange" parameterType="com.wbgame.pojo.clean.img.ImgTypeVo">
        update super_img_config set `type` =#{type} ,typeSort =#{typeSort} where typeSort = #{oldTypeSort} AND dataType = 1
    </update>


    <select id="selectImgRandNum" resultType="com.wbgame.pojo.clean.img.ImgRandNumVo">
        select * from super_img_rand
    </select>

    <update id="updateImgRandNum" parameterType="com.wbgame.pojo.clean.img.ImgRandNumVo">
        update super_img_rand
        set
        useMin =#{useMin},useMax =#{useMax},likeMin =#{likeMin},likeMax =#{likeMax},modifyUser =#{modifyUser}, modifyTime = NOW()
    </update>
</mapper>