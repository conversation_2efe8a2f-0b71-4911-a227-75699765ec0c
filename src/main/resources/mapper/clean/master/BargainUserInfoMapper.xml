<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.BargainUserInfoMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.chop.BargainUserInfo">
        <id column="appid" property="appid" jdbcType="VARCHAR"/>
        <id column="pid" property="pid" jdbcType="VARCHAR"/>
        <id column="account" property="account" jdbcType="VARCHAR"/>
        <id column="lsn" property="lsn" jdbcType="VARCHAR"/>
        <result column="androidId" property="androidId" jdbcType="VARCHAR"/>
        <result column="currentProductId" property="currentProductId" jdbcType="INTEGER"/>
        <result column="currentProductPrice" property="currentProductPrice" jdbcType="VARCHAR"/>
        <result column="currentLevel" property="currentLevel" jdbcType="INTEGER"/>
        <result column="VT" property="VT" jdbcType="VARCHAR"/>
        <result column="PT" property="PT" jdbcType="VARCHAR"/>
        <result column="MT" property="MT" jdbcType="VARCHAR"/>
        <result column="avgLevel" property="avgLevel" jdbcType="VARCHAR"/>
        <result column="remainingLevel" property="remainingLevel" jdbcType="VARCHAR"/>
        <result column="currentBasefValue" property="currentBasefValue" jdbcType="VARCHAR"/>
        <result column="currentLevelfValue" property="currentLevelfValue" jdbcType="VARCHAR"/>
        <result column="MVT" property="MVT" jdbcType="VARCHAR"/>
        <result column="MPT" property="MPT" jdbcType="VARCHAR"/>
        <result column="MMT" property="MMT" jdbcType="VARCHAR"/>
        <result column="IVT" property="IVT" jdbcType="VARCHAR"/>
        <result column="IPT" property="IPT" jdbcType="VARCHAR"/>
        <result column="IMT" property="IMT" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        appid
        , pid, account, lsn, androidId, currentProductId, currentProductPrice, currentLevel,
    VT, PT, MT, avgLevel, remainingLevel, currentBasefValue, currentLevelfValue, MVT, 
    MPT, MMT, IVT, IPT, IMT
    </sql>

    <select id="selectUserInfo" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.chop.BargainUserInfoKey">
        SELECT
        p.productName currentProductName,
        ( a.InitCost + + a.avgPurchaseCost + a.avgCommodityPrice ) fullInitialCost,
        a.cost initialPurchaseCost,
        ( VT + PT + MT ) totalAmount,
        ( a.InitCost + a.avgPurchaseCost + a.avgCommodityPrice - ( VT + PT + MT ) ) fullRemainingCost,
        ( a.cost - ( VT + PT + MT ) ) purchaseRemainingCost,
        u.*,
        changeProductTime
        FROM
        bargain_user_info u
        LEFT JOIN bargain_app_config a ON u.appid = a.appid
        AND u.pid = a.prjid
        AND u.account = a.account
        LEFT JOIN bargain_product_config p ON u.currentProductId = p.productId
        <where>


            <if test="pid != null and pid != ''">

                and u.pid = #{pid}
            </if>
            <if test="account != null and account != ''">

                and u.account = #{account}
            </if>
            <if test="androidId != null and androidId != ''">

                and u.androidId = #{androidId}
            </if>
            <if test="lsn != null and lsn != ''">

                and u.lsn = #{lsn}
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND u.appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

        </where>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by u.appid desc, u.pid desc, u.account desc, u.lsn desc
            </otherwise>
        </choose>

    </select>

</mapper>