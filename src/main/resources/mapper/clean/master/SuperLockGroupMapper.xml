<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.SuperLockGroupMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.SuperLockGroup">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="cha" property="cha" jdbcType="VARCHAR"/>
        <result column="prjid" property="prjid" jdbcType="VARCHAR"/>
        <result column="firstLock" property="firstLock" jdbcType="VARCHAR"/>
        <result column="firstLockNew" property="firstLockNew" jdbcType="VARCHAR"/>
        <result column="firstUnLock" property="firstUnLock" jdbcType="VARCHAR"/>
        <result column="firstHome" property="firstHome" jdbcType="VARCHAR"/>
        <result column="creatTime" property="creatTime" jdbcType="VARCHAR"/>
        <result column="modifyTime" property="modifyTime" jdbcType="VARCHAR"/>
        <result column="modifyUser" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="lockTotalNum" property="lockTotalNum" jdbcType="VARCHAR"/>
        <result column="unlockTotalNum" property="unlockTotalNum" jdbcType="VARCHAR"/>
        <result column="homeNum" property="homeNum" jdbcType="VARCHAR"/>
        <result column="lockSwitch" property="lockSwitch" jdbcType="VARCHAR"/>
        <result column="lockStyle" property="lockStyle" jdbcType="VARCHAR"/>
        <result column="unLockSwitch" property="unLockSwitch" jdbcType="VARCHAR"/>
        <result column="unLockInterval" property="unLockInterval" jdbcType="VARCHAR"/>
        <result column="homeSwitch" property="homeSwitch" jdbcType="VARCHAR"/>
        <result column="homeInterval" property="homeInterval" jdbcType="VARCHAR"/>
        <result column="homeAuto" property="homeAuto" jdbcType="VARCHAR"/>
        <result column="ldyAuto" property="ldyAuto" jdbcType="VARCHAR"/>
        <result column="cityStatus" property="cityStatus" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="lockStatus" property="lockStatus" jdbcType="VARCHAR"/>
        <result column="lockCity" property="lockCity" jdbcType="VARCHAR"/>
        <result column="unLockStatus" property="unLockStatus" jdbcType="VARCHAR"/>
        <result column="unLockCity" property="unLockCity" jdbcType="VARCHAR"/>
        <result column="homeStatus" property="homeStatus" jdbcType="VARCHAR"/>
        <result column="homeCity" property="homeCity" jdbcType="VARCHAR"/>
        <result column="dianLiangSwitch" property="dianLiangSwitch" jdbcType="VARCHAR"/>
        <result column="dianLiangModel" property="dianLiangModel" jdbcType="VARCHAR"/>
        <result column="chongdianSwitch" property="chongdianSwitch" jdbcType="VARCHAR"/>
        <result column="wifiSwitch" property="wifiSwitch" jdbcType="VARCHAR"/>
        <result column="ejectInterval" property="ejectInterval" jdbcType="VARCHAR"/>
        <result column="cuser" property="cuser" jdbcType="VARCHAR"/>
        <result column="buy_id" property="buy_id" jdbcType="VARCHAR"/>
        <result column="buy_act" property="buy_act" jdbcType="VARCHAR"/>
        <result column="outNewsSw" property="outNewsSw" jdbcType="VARCHAR"/>
        <result column="taFd" property="taFd" jdbcType="VARCHAR"/>
        <result column="taSt" property="taSt" jdbcType="VARCHAR"/>
        <result column="taEt" property="taEt" jdbcType="VARCHAR"/>
        <result column="taIt" property="taIt" jdbcType="VARCHAR"/>
        <result column="taSta" property="taSta" jdbcType="VARCHAR"/>
        <result column="taCity" property="taCity" jdbcType="VARCHAR"/>
        <result column="taNum" property="taNum" jdbcType="VARCHAR"/>
        <result column="taSw" property="taSw" jdbcType="VARCHAR"/>
        <result column="nqDt" property="nqDt" jdbcType="VARCHAR"/>
        <result column="ul_fas" property="ul_fas" jdbcType="VARCHAR"/>
        <result column="h_fas" property="h_fas" jdbcType="VARCHAR"/>
        <result column="ta_fas" property="ta_fas" jdbcType="VARCHAR"/>
        <result column="wf_fas" property="wf_fas" jdbcType="VARCHAR"/>
        <result column="bat_fas" property="bat_fas" jdbcType="VARCHAR"/>
        <result column="power_fas" property="power_fas" jdbcType="VARCHAR"/>
        <result column="pSt" property="pSt" jdbcType="VARCHAR"/>
        <result column="pEt" property="pEt" jdbcType="VARCHAR"/>
        <result column="pIt" property="pIt" jdbcType="VARCHAR"/>
        <result column="pFas" property="pFas" jdbcType="VARCHAR"/>
        <result column="pSw" property="pSw" jdbcType="VARCHAR"/>
        <result column="unLockStyle" property="unLockStyle" jdbcType="LONGVARCHAR"/>
        <result column="userGroup" property="userGroup" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , appid, cha, prjid, firstLock, firstLockNew, firstUnLock, firstHome, creatTime,
    modifyTime, modifyUser, status, lockTotalNum, unlockTotalNum, homeNum, lockSwitch, 
    lockStyle, unLockSwitch, unLockInterval, homeSwitch, homeInterval, homeAuto, ldyAuto, 
    cityStatus, city, lockStatus, lockCity, unLockStatus, unLockCity, homeStatus, homeCity, 
    dianLiangSwitch, dianLiangModel, chongdianSwitch, wifiSwitch, ejectInterval, cuser, 
    buy_id, buy_act, outNewsSw, taFd, taSt, taEt, taIt, taSta, taCity, taNum, taSw, nqDt, 
    ul_fas, h_fas, ta_fas, wf_fas, bat_fas, power_fas, pSt, pEt, pIt, pFas, pSw, userGroup
    </sql>

    <delete id="deleteSuperLockGroupByIdList" parameterType="java.lang.Integer">
        delete from super_lock_group
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">

            #{id}
        </foreach>
    </delete>


    <insert id="insertSuperLockGroup" parameterType="com.wbgame.pojo.clean.SuperLockGroup">
        insert into super_lock_group
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="appid != null">
                appid,
            </if>
            <if test="cha != null">
                cha,
            </if>
            <if test="prjid != null">
                prjid,
            </if>
            <if test="firstLock != null">
                firstLock,
            </if>
            <if test="firstLockNew != null">
                firstLockNew,
            </if>
            <if test="firstUnLock != null">
                firstUnLock,
            </if>
            <if test="firstHome != null">
                firstHome,
            </if>


            <if test="status != null">
                status,
            </if>
            <if test="lockTotalNum != null">
                lockTotalNum,
            </if>
            <if test="unlockTotalNum != null">
                unlockTotalNum,
            </if>
            <if test="homeNum != null">
                homeNum,
            </if>
            <if test="lockSwitch != null">
                lockSwitch,
            </if>
            <if test="lockStyle != null">
                lockStyle,
            </if>
            <if test="unLockSwitch != null">
                unLockSwitch,
            </if>
            <if test="unLockInterval != null">
                unLockInterval,
            </if>
            <if test="homeSwitch != null">
                homeSwitch,
            </if>
            <if test="homeInterval != null">
                homeInterval,
            </if>
            <if test="homeAuto != null">
                homeAuto,
            </if>
            <if test="ldyAuto != null">
                ldyAuto,
            </if>
            <if test="cityStatus != null">
                cityStatus,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="lockStatus != null">
                lockStatus,
            </if>
            <if test="lockCity != null">
                lockCity,
            </if>
            <if test="unLockStatus != null">
                unLockStatus,
            </if>
            <if test="unLockCity != null">
                unLockCity,
            </if>
            <if test="homeStatus != null">
                homeStatus,
            </if>
            <if test="homeCity != null">
                homeCity,
            </if>
            <if test="dianLiangSwitch != null">
                dianLiangSwitch,
            </if>
            <if test="dianLiangModel != null">
                dianLiangModel,
            </if>
            <if test="chongdianSwitch != null">
                chongdianSwitch,
            </if>
            <if test="wifiSwitch != null">
                wifiSwitch,
            </if>
            <if test="ejectInterval != null">
                ejectInterval,
            </if>
            <if test="cuser != null">
                cuser,
            </if>
            <if test="buy_id != null">
                buy_id,
            </if>
            <if test="buy_act != null">
                buy_act,
            </if>
            <if test="outNewsSw != null">
                outNewsSw,
            </if>
            <if test="taFd != null">
                taFd,
            </if>
            <if test="taSt != null">
                taSt,
            </if>
            <if test="taEt != null">
                taEt,
            </if>
            <if test="taIt != null">
                taIt,
            </if>
            <if test="taSta != null">
                taSta,
            </if>
            <if test="taCity != null">
                taCity,
            </if>
            <if test="taNum != null">
                taNum,
            </if>
            <if test="taSw != null">
                taSw,
            </if>
            <if test="nqDt != null">
                nqDt,
            </if>
            <if test="ul_fas != null">
                ul_fas,
            </if>
            <if test="h_fas != null">
                h_fas,
            </if>
            <if test="ta_fas != null">
                ta_fas,
            </if>
            <if test="wf_fas != null">
                wf_fas,
            </if>
            <if test="bat_fas != null">
                bat_fas,
            </if>
            <if test="power_fas != null">
                power_fas,
            </if>
            <if test="pSt != null">
                pSt,
            </if>
            <if test="pEt != null">
                pEt,
            </if>
            <if test="pIt != null">
                pIt,
            </if>
            <if test="pFas != null">
                pFas,
            </if>
            <if test="pSw != null">
                pSw,
            </if>
            <if test="userGroup != null">
                userGroup,
            </if>
            <if test="unLockStyle != null">
                unLockStyle,
            </if>
            <if test="homeStyle != null">
                homeStyle,
            </if>
            <if test="dianLiangPer != null">
                dianLiangPer,
            </if>
            <if test="dianLiangStyle != null">
                dianLiangStyle,
            </if>
            <if test="chongdianStyle != null">
                chongdianStyle,
            </if>
            <if test="wifiStyle != null">
                wifiStyle,
            </if>
            <if test="taSty != null">
                taSty,
            </if>
            <if test="taPointStyle != null">
                taPointStyle,
            </if>
            <if test="dayTotalNum != null">
                dayTotalNum,
            </if>
            creatTime,
            modifyTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="appid != null">
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="cha != null">
                #{cha,jdbcType=VARCHAR},
            </if>
            <if test="prjid != null">
                #{prjid,jdbcType=VARCHAR},
            </if>
            <if test="firstLock != null">
                #{firstLock,jdbcType=VARCHAR},
            </if>
            <if test="firstLockNew != null">
                #{firstLockNew,jdbcType=VARCHAR},
            </if>
            <if test="firstUnLock != null">
                #{firstUnLock,jdbcType=VARCHAR},
            </if>
            <if test="firstHome != null">
                #{firstHome,jdbcType=VARCHAR},
            </if>

            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="lockTotalNum != null">
                #{lockTotalNum,jdbcType=VARCHAR},
            </if>
            <if test="unlockTotalNum != null">
                #{unlockTotalNum,jdbcType=VARCHAR},
            </if>
            <if test="homeNum != null">
                #{homeNum,jdbcType=VARCHAR},
            </if>
            <if test="lockSwitch != null">
                #{lockSwitch,jdbcType=VARCHAR},
            </if>
            <if test="lockStyle != null">
                #{lockStyle,jdbcType=VARCHAR},
            </if>
            <if test="unLockSwitch != null">
                #{unLockSwitch,jdbcType=VARCHAR},
            </if>
            <if test="unLockInterval != null">
                #{unLockInterval,jdbcType=VARCHAR},
            </if>
            <if test="homeSwitch != null">
                #{homeSwitch,jdbcType=VARCHAR},
            </if>
            <if test="homeInterval != null">
                #{homeInterval,jdbcType=VARCHAR},
            </if>
            <if test="homeAuto != null">
                #{homeAuto,jdbcType=VARCHAR},
            </if>
            <if test="ldyAuto != null">
                #{ldyAuto,jdbcType=VARCHAR},
            </if>
            <if test="cityStatus != null">
                #{cityStatus,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="lockStatus != null">
                #{lockStatus,jdbcType=VARCHAR},
            </if>
            <if test="lockCity != null">
                #{lockCity,jdbcType=VARCHAR},
            </if>
            <if test="unLockStatus != null">
                #{unLockStatus,jdbcType=VARCHAR},
            </if>
            <if test="unLockCity != null">
                #{unLockCity,jdbcType=VARCHAR},
            </if>
            <if test="homeStatus != null">
                #{homeStatus,jdbcType=VARCHAR},
            </if>
            <if test="homeCity != null">
                #{homeCity,jdbcType=VARCHAR},
            </if>
            <if test="dianLiangSwitch != null">
                #{dianLiangSwitch,jdbcType=VARCHAR},
            </if>
            <if test="dianLiangModel != null">
                #{dianLiangModel,jdbcType=VARCHAR},
            </if>
            <if test="chongdianSwitch != null">
                #{chongdianSwitch,jdbcType=VARCHAR},
            </if>
            <if test="wifiSwitch != null">
                #{wifiSwitch,jdbcType=VARCHAR},
            </if>
            <if test="ejectInterval != null">
                #{ejectInterval,jdbcType=VARCHAR},
            </if>
            <if test="cuser != null">
                #{cuser,jdbcType=VARCHAR},
            </if>
            <if test="buy_id != null">
                #{buy_id,jdbcType=VARCHAR},
            </if>
            <if test="buy_act != null">
                #{buy_act,jdbcType=VARCHAR},
            </if>
            <if test="outNewsSw != null">
                #{outNewsSw,jdbcType=VARCHAR},
            </if>
            <if test="taFd != null">
                #{taFd,jdbcType=VARCHAR},
            </if>
            <if test="taSt != null">
                #{taSt,jdbcType=VARCHAR},
            </if>
            <if test="taEt != null">
                #{taEt,jdbcType=VARCHAR},
            </if>
            <if test="taIt != null">
                #{taIt,jdbcType=VARCHAR},
            </if>
            <if test="taSta != null">
                #{taSta,jdbcType=VARCHAR},
            </if>
            <if test="taCity != null">
                #{taCity,jdbcType=VARCHAR},
            </if>
            <if test="taNum != null">
                #{taNum,jdbcType=VARCHAR},
            </if>
            <if test="taSw != null">
                #{taSw,jdbcType=VARCHAR},
            </if>
            <if test="nqDt != null">
                #{nqDt,jdbcType=VARCHAR},
            </if>
            <if test="ul_fas != null">
                #{ul_fas,jdbcType=VARCHAR},
            </if>
            <if test="h_fas != null">
                #{h_fas,jdbcType=VARCHAR},
            </if>
            <if test="ta_fas != null">
                #{ta_fas,jdbcType=VARCHAR},
            </if>
            <if test="wf_fas != null">
                #{wf_fas,jdbcType=VARCHAR},
            </if>
            <if test="bat_fas != null">
                #{bat_fas,jdbcType=VARCHAR},
            </if>
            <if test="power_fas != null">
                #{power_fas,jdbcType=VARCHAR},
            </if>
            <if test="pSt != null">
                #{pSt,jdbcType=VARCHAR},
            </if>
            <if test="pEt != null">
                #{pEt,jdbcType=VARCHAR},
            </if>
            <if test="pIt != null">
                #{pIt,jdbcType=VARCHAR},
            </if>
            <if test="pFas != null">
                #{pFas,jdbcType=VARCHAR},
            </if>
            <if test="pSw != null">
                #{pSw,jdbcType=VARCHAR},
            </if>
            <if test="userGroup != null">
                #{userGroup,jdbcType=VARCHAR},
            </if>
            <if test="unLockStyle != null">
                #{unLockStyle,jdbcType=LONGVARCHAR},
            </if>
            <if test="homeStyle != null">
                #{homeStyle,jdbcType=LONGVARCHAR},
            </if>
            <if test="dianLiangPer != null">
                #{dianLiangPer,jdbcType=LONGVARCHAR},
            </if>
            <if test="dianLiangStyle != null">
                #{dianLiangStyle,jdbcType=LONGVARCHAR},
            </if>
            <if test="chongdianStyle != null">
                #{chongdianStyle,jdbcType=LONGVARCHAR},
            </if>
            <if test="wifiStyle != null">
                #{wifiStyle,jdbcType=LONGVARCHAR},
            </if>
            <if test="taSty != null">
                #{taSty,jdbcType=LONGVARCHAR},
            </if>
            <if test="taPointStyle != null">
                #{taPointStyle,jdbcType=LONGVARCHAR},
            </if>
            <if test="dayTotalNum != null">
                #{dayTotalNum,jdbcType=VARCHAR},
            </if>
            now(),
            now()
        </trim>
    </insert>


    <update id="updateSuperLockGroupByIdList">
        update super_lock_group
        <set>

            <if test="record.firstLock != null">
                firstLock = #{record.firstLock,jdbcType=VARCHAR},
            </if>
            <if test="record.firstLockNew != null">
                firstLockNew = #{record.firstLockNew,jdbcType=VARCHAR},
            </if>
            <if test="record.firstUnLock != null">
                firstUnLock = #{record.firstUnLock,jdbcType=VARCHAR},
            </if>
            <if test="record.firstHome != null">
                firstHome = #{record.firstHome,jdbcType=VARCHAR},
            </if>

            <if test="record.modifyUser != null">
                modifyUser = #{record.modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=VARCHAR},
            </if>
            <if test="record.lockTotalNum != null">
                lockTotalNum = #{record.lockTotalNum,jdbcType=VARCHAR},
            </if>
            <if test="record.unlockTotalNum != null">
                unlockTotalNum = #{record.unlockTotalNum,jdbcType=VARCHAR},
            </if>
            <if test="record.homeNum != null">
                homeNum = #{record.homeNum,jdbcType=VARCHAR},
            </if>
            <if test="record.lockSwitch != null">
                lockSwitch = #{record.lockSwitch,jdbcType=VARCHAR},
            </if>
            <if test="record.lockStyle != null">
                lockStyle = #{record.lockStyle,jdbcType=VARCHAR},
            </if>
            <if test="record.unLockSwitch != null">
                unLockSwitch = #{record.unLockSwitch,jdbcType=VARCHAR},
            </if>
            <if test="record.unLockInterval != null">
                unLockInterval = #{record.unLockInterval,jdbcType=VARCHAR},
            </if>
            <if test="record.homeSwitch != null">
                homeSwitch = #{record.homeSwitch,jdbcType=VARCHAR},
            </if>
            <if test="record.homeInterval != null">
                homeInterval = #{record.homeInterval,jdbcType=VARCHAR},
            </if>
            <if test="record.homeAuto != null">
                homeAuto = #{record.homeAuto,jdbcType=VARCHAR},
            </if>
            <if test="record.ldyAuto != null">
                ldyAuto = #{record.ldyAuto,jdbcType=VARCHAR},
            </if>
            <if test="record.cityStatus != null">
                cityStatus = #{record.cityStatus,jdbcType=VARCHAR},
            </if>
            <if test="record.city != null">
                city = #{record.city,jdbcType=VARCHAR},
            </if>
            <if test="record.lockStatus != null">
                lockStatus = #{record.lockStatus,jdbcType=VARCHAR},
            </if>
            <if test="record.lockCity != null">
                lockCity = #{record.lockCity,jdbcType=VARCHAR},
            </if>
            <if test="record.unLockStatus != null">
                unLockStatus = #{record.unLockStatus,jdbcType=VARCHAR},
            </if>
            <if test="record.unLockCity != null">
                unLockCity = #{record.unLockCity,jdbcType=VARCHAR},
            </if>
            <if test="record.homeStatus != null">
                homeStatus = #{record.homeStatus,jdbcType=VARCHAR},
            </if>
            <if test="record.homeCity != null">
                homeCity = #{record.homeCity,jdbcType=VARCHAR},
            </if>
            <if test="record.dianLiangSwitch != null">
                dianLiangSwitch = #{record.dianLiangSwitch,jdbcType=VARCHAR},
            </if>
            <if test="record.dianLiangModel != null">
                dianLiangModel = #{record.dianLiangModel,jdbcType=VARCHAR},
            </if>
            <if test="record.chongdianSwitch != null">
                chongdianSwitch = #{record.chongdianSwitch,jdbcType=VARCHAR},
            </if>
            <if test="record.wifiSwitch != null">
                wifiSwitch = #{record.wifiSwitch,jdbcType=VARCHAR},
            </if>
            <if test="record.ejectInterval != null">
                ejectInterval = #{record.ejectInterval,jdbcType=VARCHAR},
            </if>

            <if test="record.outNewsSw != null">
                outNewsSw = #{record.outNewsSw,jdbcType=VARCHAR},
            </if>
            <if test="record.taFd != null">
                taFd = #{record.taFd,jdbcType=VARCHAR},
            </if>
            <if test="record.taSt != null">
                taSt = #{record.taSt,jdbcType=VARCHAR},
            </if>
            <if test="record.taEt != null">
                taEt = #{record.taEt,jdbcType=VARCHAR},
            </if>
            <if test="record.taIt != null">
                taIt = #{record.taIt,jdbcType=VARCHAR},
            </if>
            <if test="record.taSta != null">
                taSta = #{record.taSta,jdbcType=VARCHAR},
            </if>
            <if test="record.taCity != null">
                taCity = #{record.taCity,jdbcType=VARCHAR},
            </if>
            <if test="record.taNum != null">
                taNum = #{record.taNum,jdbcType=VARCHAR},
            </if>
            <if test="record.taSw != null">
                taSw = #{record.taSw,jdbcType=VARCHAR},
            </if>
            <if test="record.nqDt != null">
                nqDt = #{record.nqDt,jdbcType=VARCHAR},
            </if>
            <if test="record.ul_fas != null">
                ul_fas = #{record.ul_fas,jdbcType=VARCHAR},
            </if>
            <if test="record.h_fas != null">
                h_fas = #{record.h_fas,jdbcType=VARCHAR},
            </if>
            <if test="record.ta_fas != null">
                ta_fas = #{record.ta_fas,jdbcType=VARCHAR},
            </if>
            <if test="record.wf_fas != null">
                wf_fas = #{record.wf_fas,jdbcType=VARCHAR},
            </if>
            <if test="record.bat_fas != null">
                bat_fas = #{record.bat_fas,jdbcType=VARCHAR},
            </if>
            <if test="record.power_fas != null">
                power_fas = #{record.power_fas,jdbcType=VARCHAR},
            </if>
            <if test="record.pSt != null">
                pSt = #{record.pSt,jdbcType=VARCHAR},
            </if>
            <if test="record.pEt != null">
                pEt = #{record.pEt,jdbcType=VARCHAR},
            </if>
            <if test="record.pIt != null">
                pIt = #{record.pIt,jdbcType=VARCHAR},
            </if>
            <if test="record.pFas != null">
                pFas = #{record.pFas,jdbcType=VARCHAR},
            </if>
            <if test="record.pSw != null">
                pSw = #{record.pSw,jdbcType=VARCHAR},
            </if>

            <if test="record.unLockStyle != null">
                unLockStyle = #{record.unLockStyle,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.homeStyle != null">
                homeStyle = #{record.homeStyle,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.dianLiangPer != null">
                dianLiangPer = #{record.dianLiangPer,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.dianLiangStyle != null">
                dianLiangStyle = #{record.dianLiangStyle,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.chongdianStyle != null">
                chongdianStyle = #{record.chongdianStyle,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.wifiStyle != null">
                wifiStyle = #{record.wifiStyle,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.taSty != null">
                taSty = #{record.taSty,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.taPointStyle != null">
                taPointStyle = #{record.taPointStyle,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.dayTotalNum != null">
                dayTotalNum = #{record.dayTotalNum,jdbcType=VARCHAR},
            </if>
            modifyTime = now()
        </set>
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </update>

    <select id="selectSuperLockGroup" parameterType="com.wbgame.pojo.clean.SuperLockGroupDTO" resultMap="BaseResultMap">

        select * from super_lock_group
        <where>
            <if test="appid != null and appid !=''">
                and appid = #{appid}
            </if>

            <if test="cha != null and cha !=''">
                and cha like #{cha} "%"
            </if>

            <if test="prjid != null and prjid !=''">
                and prjid like #{prjid} "%"
            </if>

            <if test="buy_id != null and buy_id !=''">
                and buy_id like #{buy_id} "%"
            </if>

            <if test="buy_act != null and buy_act !=''">
                and buy_act like #{buy_act} "%"
            </if>
            <if test="userGroup != null and userGroup !=''">
                and userGroup like #{userGroup} "%"
            </if>
        </where>

        order by id desc
    </select>

    <select id="checkForUniqueness" parameterType="com.wbgame.pojo.clean.SuperLockGroupDTO"
            resultType="java.lang.Integer">

        select count(*) from super_lock_group

        <where>

            appid = #{appid} and userGroup = #{userGroup}

            <choose>
                <when test="cha != null and cha !=''">
                    and cha = #{cha}
                </when>
                <when test="prjid != null and prjid !=''">
                    and prjid = #{prjid}
                </when>
                <when test="buy_id != null and buy_id !=''">
                    and buy_id = #{buy_id}
                </when>
                <when test="buy_act != null and buy_act !=''">
                    and buy_act = #{buy_act}
                </when>

                <otherwise></otherwise>
            </choose>


        </where>
    </select>


    <update id="updateOnOff">

        update super_lock_group set
            modifyUser = #{userName},
            modifyTime = now(),
            status = concat(format(status + 1, 0) % 2, "")
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </update>

    <select id="checkAppidByChaOrPrjidOrBuyIdOrbuyAct" parameterType="com.wbgame.pojo.clean.SuperLockGroup"
            resultType="java.lang.Integer">
        select count(*) from super_lock_group

        <where>

            appid = #{appid}

            <if test="userGroup != null and userGroup != ''">
                and userGroup = #{userGroup}
            </if>

            <choose>
                <when test="cha != null and cha !=''">
                    and cha = #{cha}
                </when>
                <when test="prjid != null and prjid !=''">
                    and prjid = #{prjid}
                </when>

                <otherwise></otherwise>
            </choose>

            <if test="buy_id != null and buy_id !=''">
                and buy_id = #{buy_id}
            </if>
            <if test="buy_act != null and buy_act !=''">
                and buy_act = #{buy_act}
            </if>

        </where>
    </select>
</mapper>