<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.BargainOrderReviewMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.chop.BargainOrderReview">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="prjid" property="prjid" jdbcType="VARCHAR"/>
        <result column="account" property="account" jdbcType="VARCHAR"/>
        <result column="androidid" property="androidid" jdbcType="VARCHAR"/>
        <result column="productId" property="productId" jdbcType="INTEGER"/>
        <result column="ecpmSum" property="ecpmSum" jdbcType="VARCHAR"/>
        <result column="userName" property="userName" jdbcType="VARCHAR"/>
        <result column="userPhone" property="userPhone" jdbcType="VARCHAR"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="orderStatus" property="orderStatus" jdbcType="INTEGER"/>
        <result column="orderId" property="orderId" jdbcType="VARCHAR"/>
        <result column="createTime" property="createTime" jdbcType="VARCHAR"/>
        <result column="createUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="modifyUser" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="modifyTime" property="modifyTime" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , appid, prjid, account, androidid, productId, ecpmSum, userName, userPhone, area,
    address, orderStatus, orderId, createTime, createUser,modifyUser,modifyTime, lsn,appOrderId
    </sql>

    <select id="selectOrderReview" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        id
        , appid, prjid, account, androidid, o.productId, ecpmSum, userName, userPhone, area,
        address, orderStatus, orderId, o.createTime, o.createUser,o.modifyUser,o.modifyTime, lsn,appOrderId,

        productName,
        productPrice
        from bargain_order_review o
        left join bargain_product_config p
        on o.productId = p.productId
        <where>
            <if test="androidid != null and androidid != ''">
                and androidid = #{androidid}
            </if>
            <if test="prjid != null and prjid != ''">
                and prjid = #{prjid}
            </if>
            <if test="account != null and account != ''">
                and account = #{account}
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>


        </where>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>

            <otherwise>
                order by id desc
            </otherwise>
        </choose>

    </select>

    <select id="selectOrderReviewById" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from bargain_order_review
        where id = #{id}

    </select>


    <update id="updateOrderReview" parameterType="com.wbgame.pojo.clean.chop.BargainOrderReview">
        update bargain_order_review
        <set>

            <if test="orderStatus != null">
                orderStatus = #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="orderId != null and orderId != ''">
                orderId = #{orderId,jdbcType=VARCHAR},
            </if>

            <if test="modifyUser != null and modifyUser != ''">
                modifyUser = #{modifyUser},
            </if>

            modifyTime = now()

        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

</mapper>