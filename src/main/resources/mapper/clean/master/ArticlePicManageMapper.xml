<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.clean.master.ArticlePicManageMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.ArticlePicManage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="article_id" jdbcType="BIGINT" property="articleId"/>
        <result column="pic_seq" jdbcType="INTEGER" property="picSeq"/>
        <result column="pic_url" jdbcType="VARCHAR" property="picUrl"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="BIGINT" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , article_id, pic_seq, pic_url, creator, create_time, modifier, modify_time
    </sql>

    <select id="selectArticlePicManage" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from gj_b.article_pic_manage
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteArticlePicManageByArticleId" parameterType="java.lang.Long">
        delete
        from gj_b.article_pic_manage
        where article_id in
        <foreach collection="list" item="articleId" open="(" separator="," close=")">
            #{articleId}
        </foreach>
    </delete>


    <insert id="insertArticlePicManage" parameterType="com.wbgame.pojo.clean.ArticlePicManage">
        INSERT INTO gj_b.article_pic_manage ( article_id, pic_seq, pic_url, creator, create_time, modify_time )
        values
        <foreach collection="list" item="obj" separator=",">
            (#{obj.articleId},
            #{obj.picSeq},
            #{obj.picUrl},
            #{obj.creator},
            #{obj.createTime},
            #{obj.modifyTime})
        </foreach>

    </insert>

    <update id="updateArticlePicManage" parameterType="com.wbgame.pojo.clean.ArticlePicManage">
        update gj_b.article_pic_manage
        <set>
            <if test="articleId != null">
                article_id = #{articleId,jdbcType=BIGINT},
            </if>
            <if test="picSeq != null">
                pic_seq = #{picSeq,jdbcType=INTEGER},
            </if>
            <if test="picUrl != null and picUrl != ''">
                pic_url = #{picUrl,jdbcType=VARCHAR},
            </if>

            <if test="modifier != null and modifier != ''">
                modifier = #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where article_id = #{articleId}
    </update>

    <select id="getPicManageByPicUrl" parameterType="java.lang.String" resultType="java.lang.String">
        
        select * from gj_b.article_pic_manage

        pic_url in
            <foreach collection="list" item="picUl" open="(" separator="," close=")">
                #{picUl}
            </foreach>

    </select>

</mapper>