<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.FaceModelMapper" >

    <select id="selectFaceAreaConfig" resultType="com.wbgame.pojo.clean.face.CopyFaceAreaConfig">
        select
        *
        from copy_face_area_config
        where  1=1
        <if test="areaName != null and areaName != ''">
            and  areaName = #{areaName,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            and  status = #{status,jdbcType=VARCHAR}
        </if>
        order by modifyTime desc
    </select>

    <insert id="insertFaceAreaConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceAreaConfig">
        insert into copy_face_area_config ( areaName,descTxt,area,
                                       createTime,modifyTime, createUser,modifyUser,status
        )
        values (#{areaName},#{descTxt} ,#{area},
                NOW(),NOW(),#{createUser},#{modifyUser},#{status}
               )
    </insert>

    <update id="updateFaceAreaConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceAreaConfig" >
        update copy_face_area_config
        <set >
            <if test="areaName != null and areaName != ''" >
                areaName = #{areaName},
            </if>
            <if test="descTxt != null" >
                descTxt = #{descTxt},
            </if>
            <if test="area != null and area != ''" >
                area = #{area},
            </if>
            <if test="status != null and status != ''" >
                status = #{status},
            </if>
            <if test="modifyUser != null and modifyUser != ''" >
                modifyUser = #{modifyUser},
            </if>
            modifyTime = NOW()
        </set>
        where id in (${id})
    </update>

    <delete id="deleteFaceAreaConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceAreaConfig">
        delete from copy_face_area_config
        where id = #{id}
    </delete>


    <select id="selectFaceClassConfig" resultType="com.wbgame.pojo.clean.face.CopyFaceClassConfig">
        select
        a.*,b.areaName
        from copy_face_class_config a LEFT JOIN copy_face_area_config b on a.areaId =b.id
        where  1=1
        <if test="areaId != null and areaId != ''">
            and  areaId = #{areaId,jdbcType=VARCHAR}
        </if>
        <if test="className != null and className != ''">
            and  className = #{className,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            and  a.status = #{status,jdbcType=VARCHAR}
        </if>
        order by sort asc, modifyTime desc
    </select>

    <insert id="insertFaceClassConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceClassConfig">
        insert into copy_face_class_config ( className,areaId,sort,
                                        createTime,modifyTime, createUser,modifyUser,status
        )
        values (#{className},#{areaId} ,#{sort},
                NOW(),NOW(),#{createUser},#{modifyUser},#{status}
               )
    </insert>
    <update id="updateFaceClassConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceClassConfig" >
        update copy_face_class_config
        <set >
            <if test="className != null and className != ''" >
                className = #{className},
            </if>
            <if test="areaId != null " >
                areaId = #{areaId},
            </if>
            <if test="sort != null " >
                sort = #{sort},
            </if>
            <if test="status != null and status != ''" >
                status = #{status},
            </if>
            <if test="modifyUser != null and modifyUser != ''" >
                modifyUser = #{modifyUser},
            </if>
            modifyTime = NOW()
        </set>
        where id in (${id})
    </update>

    <delete id="deleteFaceClassConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceClassConfig">
        delete from copy_face_class_config
        where id = #{id}
    </delete>


    <select id="selectModelClassConfig" resultType="com.wbgame.pojo.clean.face.ModelClassConfig">
        select
            *
        from copy_face_model_class_config
        where   modelId = #{modelId}
    </select>
    <insert id="insertModelClassConfigList" parameterType="com.wbgame.pojo.clean.face.ModelClassConfig">
        insert into copy_face_model_class_config ( classId,areaId,modelId,typeSort,typeModifyTime)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.classId,jdbcType=VARCHAR}, #{li.areaId,jdbcType=VARCHAR},#{li.modelId,jdbcType=VARCHAR},0,NOW() )
        </foreach>
    </insert>

    <delete id="deleteModelClassConfigbyClassId" parameterType="com.wbgame.pojo.clean.face.ModelClassConfig">
        delete from copy_face_model_class_config
        where modelId = #{modelId} and classId = #{classId}
    </delete>

    <select id="selectFaceModelConfig" resultType="com.wbgame.pojo.clean.face.CopyFaceModelConfig">
        select distinct
        m.*, mc.*, concat(truncate(mc.tplConvertVipCount / mc.makeTemplateCount * 100, 2), "%") conversionRate
        from copy_face_model_config m left join  copy_face_model_count mc on m.id = mc.modelId
        left join copy_face_model_class_config c on m.id = c.modelId
        where  1=1
        <if test="tplConvertVipCount > 0">
            and mc.tplConvertVipCount &lt; ${tplConvertVipCount}
        </if>

        <if test="tempTitle != null and tempTitle != ''">
            and  tempTitle like "%" #{tempTitle,jdbcType=VARCHAR} "%"
        </if>
        <if test="status != null and status != ''">
            and  status = #{status,jdbcType=VARCHAR}
        </if>
        <if test="state != null and state != ''">
            and  state = #{state,jdbcType=VARCHAR}
        </if>
        <if test="tempType != null and tempType != ''">
            and  tempType = #{tempType,jdbcType=VARCHAR}
        </if>
        <!--	<if test="status != null and status != ''">-->
        <!--		 and  status = #{status,jdbcType=VARCHAR}-->
        <!--	</if>-->
        <if test="id != null and id != ''">
            and  m.id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="mid != null and mid != ''">
            and  mid = #{mid,jdbcType=VARCHAR}
        </if>

        <if test="classId != null">
            and  classId = #{classId}
        </if>

        <if test="areaId != null">
            and  areaId = #{areaId}
        </if>

        <if test="createTime != null">
            and  createTime &lt; #{createTime}
        </if>

        order by id desc, modifyTime desc
    </select>


    <insert id="insertFaceModelConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceModelConfig">
        insert into copy_face_model_config ( mid,tempTitle,tempUrl,videoUrl,tempType,state,isTop,videoHeight,videoWidth,projectId,templateFaceID,taskTime,
                                        createTime,modifyTime, createUser,modifyUser,status,region
        )
        values (#{mid},#{tempTitle} ,#{tempUrl}, #{videoUrl},#{tempType} ,#{state}, #{isTop},#{videoHeight} ,#{videoWidth},#{projectId} ,#{templateFaceID}, #{taskTime},
                NOW(),NOW(),#{createUser},#{modifyUser},#{status},#{region}
               )
    </insert>

    <update id="updateFaceModelConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceModelConfig" >
        update copy_face_model_config
        <set >
            <if test="mid != null and mid != ''" >
                mid = #{mid},
            </if>
            <if test="tempTitle != null and tempTitle != ''" >
                tempTitle = #{tempTitle},
            </if>
            <if test="tempUrl != null and tempUrl != ''" >
                tempUrl = #{tempUrl},
            </if>
            <if test="videoUrl != null and videoUrl != ''" >
                videoUrl = #{videoUrl},
            </if>
            <if test="tempType != null and tempType != ''" >
                tempType = #{tempType},
            </if>
            <if test="taskTime != null and taskTime != ''" >
                taskTime = #{taskTime},
            </if>
            <if test="templateFaceID != null and templateFaceID != ''" >
                templateFaceID = #{templateFaceID},
            </if>
            <if test="state != null and state != ''" >
                state = #{state},
            </if>
            <if test="isTop != null " >
                isTop = #{isTop},
            </if>
            <if test="sort != null " >
                sort = #{sort},
            </if>
            <if test="region != null " >
                region = #{region},
            </if>
            <if test="videoHeight != null and videoHeight != ''" >
                videoHeight = #{videoHeight},
            </if>
            <if test="videoWidth != null and videoWidth != ''" >
                videoWidth = #{videoWidth},
            </if>
            <if test="projectId != null and projectId != ''" >
                projectId = #{projectId},
            </if>
            <if test="status != null and status != ''" >
                status = #{status},
            </if>
            <if test="modifyUser != null and modifyUser != ''" >
                modifyUser = #{modifyUser},
            </if>
            modifyTime = NOW()
        </set>
        where id in (${id})
    </update>

    <update id="updateFaceModelConfigTop" parameterType="com.wbgame.pojo.clean.face.CopyFaceModelConfig" >
        update copy_face_model_class_config
        set
            typeSort = 0,
            typeModifyTime = NOW()
        where  modelId = #{id}  and classId = #{classId}
    </update>
    <delete id="deleteFaceModelConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceModelConfig">
        delete from copy_face_model_config
        where id = #{id}
    </delete>
    <update id="updateFaceModelConfigSort" parameterType="com.wbgame.pojo.clean.face.CopyFaceModelConfig" >
        update copy_face_model_class_config
        set
            typeModifyTime = NOW(),
            typeSort = #{sort}
        where modelId = #{id}  and classId = #{classId}
    </update>
    <select id="selectFaceModelSortConfig" resultType="com.wbgame.pojo.clean.face.CopyFaceModelConfig">
        select * from (

        SELECT
        DATEDIFF(CURRENT_DATE, b.createTime) upTime,
        a.classId,
        a.areaId,
        a.modelId,
        a.typeSort,
        a.typeModifyTime,
        b.*,
        c.tplConvertVipCount,
        c.makeTemplateCount,
        concat( TRUNCATE ( c.tplConvertVipCount / c.makeTemplateCount * 100, 2 ), "%" ) conversionRate
        FROM
        `copy_face_model_class_config` a
        LEFT JOIN copy_face_model_config b ON a.modelId = b.id
        LEFT JOIN copy_face_model_count c ON a.modelId = c.modelId
        ) c
        WHERE
        c.id != ""
        AND c.state = 1
        <if test="classId!= null ">
            and  c.classId = #{classId,jdbcType=VARCHAR}
        </if>
        <if test="tempTitle!= null and tempTitle != ''">
            and  tempTitle = #{tempTitle}
        </if>
        <if test="templateFaceID!= null and templateFaceID != ''">
            and  templateFaceID = #{templateFaceID}
        </if>
        <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
            and  createTime between #{start_date} and concat(#{end_date}, ' 23:59:59')
        </if>
        order by  typeSort asc,typeModifyTime desc
    </select>


    <select id="selectFaceProductConfig" resultType="com.wbgame.pojo.clean.face.CopyFaceProductConfig">
        select
        *
        from copy_face_product_config
        where  1=1
        <if test="productName != null and productName != ''">
            and  productName = #{productName}
        </if>
        <if test="cha != null and cha != ''">
            and  cha = #{cha}
        </if>
        <if test="period != null and period != ''">
            and  period = #{period}
        </if>
        <if test="status != null and status != ''">
            and  status = #{status}
        </if>
        <if test="id != null and id != ''">
            and  id = #{id}
        </if>
        <if test="platformProductId != null and platformProductId != ''">
            and  platformProductId = #{platformProductId}
        </if>
        order by sort , modifyTime desc
    </select>
    <insert id="insertFaceProductConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceProductConfig">
        insert into copy_face_product_config ( cha,productName,productPrice, period,feeDay,platformProductId,moneyType,productDescribe,
                                          createTime,modifyTime, createUser,modifyUser,status
        )
        values (#{cha},#{productName} ,#{productPrice}, #{period},#{feeDay} ,#{platformProductId}, #{moneyType}, #{productDescribe},
                NOW(),NOW(),#{createUser},#{modifyUser},#{status}
               )
    </insert>
    <update id="updateFaceProductConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceProductConfig" >
        update copy_face_product_config
        <set >
            <if test="cha != null and cha != ''" >
                cha = #{cha},
            </if>
            <if test="productName != null and productName != ''" >
                productName = #{productName},
            </if>
            <if test="productDescribe != null and productDescribe != ''" >
                productDescribe = #{productDescribe},
            </if>
            <if test="productPrice != null and productPrice != ''" >
                productPrice = #{productPrice},
            </if>
            <if test="period != null and period != ''" >
                period = #{period},
            </if>
            <if test="feeDay != null and feeDay != ''" >
                feeDay = #{feeDay},
            </if>
            <if test="platformProductId != null and platformProductId != ''" >
                platformProductId = #{platformProductId},
            </if>
            <if test="moneyType != null and moneyType != ''" >
                moneyType = #{moneyType},
            </if>
            <if test="sort != null " >
                sort = #{sort},
            </if>
            <if test="status != null and status != ''" >
                status = #{status},
            </if>
            <if test="modifyUser != null and modifyUser != ''" >
                modifyUser = #{modifyUser},
            </if>
            modifyTime = NOW()
        </set>
        where id in (${id})
    </update>
    <delete id="deleteFaceProductConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceProductConfig">
        delete from copy_face_product_config
        where id = #{id}
    </delete>

    <select id="selectFaceSwitchConfig" resultType="com.wbgame.pojo.clean.face.CopyFaceSwitchConfig">
        select
            *
        from copy_face_switch_config
    </select>
    <update id="updateFaceSwitchConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceSwitchConfig" >
        update copy_face_switch_config
        <set >
            <if test="fristProductionPic != null and fristProductionPic != ''" >
                fristProductionPic = #{fristProductionPic},
            </if>
            <if test="faceCartoonPic != null and faceCartoonPic != '' " >
                faceCartoonPic = #{faceCartoonPic},
            </if>
            <if test="changeAgeOldPic != null and changeAgeOldPic != '' " >
                changeAgeOldPic = #{changeAgeOldPic},
            </if>
            <if test="changeAgeYongPic != null and changeAgeYongPic != '' " >
                changeAgeYongPic = #{changeAgeYongPic},
            </if>
            <if test="swapGenderPic != null and swapGenderPic != ''" >
                swapGenderPic = #{swapGenderPic},
            </if>
            <if test="subscriptionPage != null and subscriptionPage != ''" >
                subscriptionPage = #{subscriptionPage},
            </if>
            <if test="guidePage != null and guidePage != ''" >
                guidePage = #{guidePage},
            </if>
            <if test="modelId != null and modelId != ''" >
                modelId = #{modelId},
            </if>
            <if test="projectId != null and projectId != ''" >
                projectId = #{projectId},
            </if>
            <if test="templateFaceID != null and templateFaceID != ''" >
                templateFaceID = #{templateFaceID},
            </if>
            <if test="tempUrl != null and tempUrl != ''" >
                tempUrl = #{tempUrl},
            </if>
            <if test="bynouOffOn != null and bynouOffOn != ''" >
                bynouOffOn = #{bynouOffOn},
            </if>
            <if test="homeADSwitch != null and homeADSwitch != ''" >
                homeADSwitch = #{homeADSwitch},
            </if>
            <if test="splashADSwitch != null and splashADSwitch != ''" >
                splashADSwitch = #{splashADSwitch},
            </if>
            <if test="onlyfnoushowOnffOn != null and onlyfnoushowOnffOn != ''" >
                onlyfnoushowOnffOn = #{onlyfnoushowOnffOn},
            </if>
            <if test="status != null and status != ''" >
                status = #{status},
            </if>
            <if test="modifyUser != null and modifyUser != ''" >
                modifyUser = #{modifyUser},
            </if>
            <if test="bynoubsOffOn != null and bynoubsOffOn != ''" >
                bynoubsOffOn = #{bynoubsOffOn},
            </if>
            <if test="timeCameraOffOn != null" >
                timeCameraOffOn = #{timeCameraOffOn},
            </if>
            <if test="showPayGuideLoc != null" >
                showPayGuideLoc = #{showPayGuideLoc},
            </if>
            modifyTime = NOW()
        </set>
        where id in (${id})
    </update>
    <delete id="deleteFaceSwitchConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceSwitchConfig">
        delete from copy_face_switch_config
        where id = #{id}
    </delete>



    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.FaceCreateConfigVO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="promotesCha" property="promotesCha" jdbcType="VARCHAR" />
        <result column="createId" property="createId" jdbcType="VARCHAR" />
        <result column="modelId" property="modelId" jdbcType="VARCHAR" />
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modifyTime" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="createUser" property="createUser" jdbcType="VARCHAR" />
        <result column="modifyUser" property="modifyUser" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="tempTitle" property="tempTitle" jdbcType="VARCHAR" />

        <result column="modelType" property="modelType" jdbcType="INTEGER" />
        <result column="modelTypeName" property="modelTypeName" jdbcType="VARCHAR" />
        <result column="typeName" property="typeName" jdbcType="VARCHAR" />
        <result column="tempUrl" property="tempUrl" jdbcType="VARCHAR" />
        <result column="videoUrl" property="videoUrl" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, promotesCha, createId, modelId, createTime, modifyTime, createUser, modifyUser,
    status
    </sql>

    <select id="selectFaceCreateConfigByCondition" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select
        c.*, m.tempTitle, m.tempUrl, m.videoUrl, mt.modelType modelTypeName
        from copy_face_create_config c left join  copy_face_model_config m on c.modelId = m.id
        left join copy_face_model_type mt on c.modelType = mt.id

        <where>

            <if test="promotesCha != null and promotesCha != ''">
                and c.promotesCha = #{promotesCha}
            </if>

            <if test="createId != null and createId != ''">
                and c.createId like #{createId} "%"
            </if>

            <if test="modelId != null">
                and c.modelId = #{modelId}
            </if>

            <if test="status != null and status != ''">
                and c.status = #{status}
            </if>

            <if test="modelType != null">
                and c.modelType = #{modelType}
            </if>
        </where>

        order by c.id desc
    </select>
    <delete id="deleteFaceCreateConfigById" parameterType="java.lang.Integer" >
        delete from copy_face_create_config
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertFaceCreateConfig" parameterType="com.wbgame.pojo.clean.FaceCreateConfig" >
        insert into copy_face_create_config
        <trim prefix="(" suffix=")" suffixOverrides="," >

            <if test="promotesCha != null" >
                promotesCha,
            </if>
            <if test="createId != null" >
                createId,
            </if>
            <if test="modelId != null" >
                modelId,
            </if>

            <if test="createUser != null" >
                createUser,
            </if>

            <if test="status != null" >
                status,
            </if>

            <if test="modelType != null" >
                modelType,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >

            <if test="promotesCha != null and promotesCha != ''" >
                #{promotesCha,jdbcType=VARCHAR},
            </if>
            <if test="createId != null and createId != ''" >
                #{createId,jdbcType=VARCHAR},
            </if>
            <if test="modelId != null" >
                #{modelId},
            </if>

            <if test="createUser != null and createUser != ''" >
                #{createUser,jdbcType=VARCHAR},
            </if>

            <if test="status != null and status != ''" >
                #{status,jdbcType=VARCHAR},
            </if>

            <if test="modelType != null" >
                #{modelType}
            </if>
        </trim>
    </insert>

    <update id="updateFaceCreateConfigById" parameterType="com.wbgame.pojo.clean.FaceCreateConfig" >
        update copy_face_create_config
        <set >
            <if test="promotesCha != null" >
                promotesCha = #{promotesCha,jdbcType=VARCHAR},
            </if>
            <if test="createId != null" >
                createId = #{createId,jdbcType=VARCHAR},
            </if>
            <if test="modelId != null" >
                modelId = #{modelId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                createTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modifyTime = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null" >
                createUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyUser != null" >
                modifyUser = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateFaceCreateConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceModelType" >
        update copy_face_create_config
        set promotesCha = #{promotesCha,jdbcType=VARCHAR},
            createId = #{createId,jdbcType=VARCHAR},
            modelId = #{modelId,jdbcType=VARCHAR},
            createTime = #{createTime,jdbcType=TIMESTAMP},
            modifyTime = #{modifyTime,jdbcType=TIMESTAMP},
            createUser = #{createUser,jdbcType=VARCHAR},
            modifyUser = #{modifyUser,jdbcType=VARCHAR},
            status = #{status,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>




    <resultMap id="BaseResultMap1" type="com.wbgame.pojo.clean.FaceModelTypeVO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="modelType" property="modelType" jdbcType="VARCHAR" />

    </resultMap>

    <sql id="Base_Column_List1" >
        id, model_type, type_name
    </sql>

    <delete id="deleteModelTypeById" parameterType="java.lang.Integer" >
        delete from copy_face_model_type
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">

            #{id}
        </foreach>
    </delete>

    <insert id="insertModelType" parameterType="com.wbgame.pojo.clean.face.CopyFaceModelType" >
        insert into copy_face_model_type
        <trim prefix="(" suffix=")" suffixOverrides="," >

            <if test="modelType != null" >
                modelType,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >

            <if test="modelType != null" >
                #{modelType,jdbcType=VARCHAR},
            </if>

        </trim>
    </insert>
    <update id="updateModelTypeById" parameterType="com.wbgame.pojo.clean.face.CopyFaceModelType" >
        update copy_face_model_type
        <set >
            <if test="modelType != null" >
                modelType = #{modelType,jdbcType=VARCHAR},
            </if>

        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectAll" resultMap="BaseResultMap1">

        select * from copy_face_model_type order by id desc
    </select>



    <resultMap id="BaseResultMap2" type="com.wbgame.pojo.clean.face.SuperFaceUserVO">
        <id column="userId" property="userId" jdbcType="VARCHAR"/>
        <!--    <result column="wxIcon" property="wxIcon" jdbcType="VARCHAR" />-->
        <!--    <result column="wxName" property="wxName" jdbcType="VARCHAR" />-->
        <result column="creatTime" property="creatTime" jdbcType="VARCHAR"/>
        <result column="phoneNumber" property="phoneNumber" jdbcType="VARCHAR"/>
        <!--    <result column="lsn" property="lsn" jdbcType="VARCHAR" />-->
        <!--    <result column="appid" property="appid" jdbcType="VARCHAR" />-->
        <!--    <result column="imei" property="imei" jdbcType="VARCHAR" />-->
        <!--    <result column="prjid" property="prjid" jdbcType="VARCHAR" />-->
        <!--    <result column="cha" property="cha" jdbcType="VARCHAR" />-->
        <!--    <result column="oaid" property="oaid" jdbcType="VARCHAR" />-->
        <!--    <result column="platform" property="platform" jdbcType="VARCHAR" />-->
        <!--    <result column="param1" property="param1" jdbcType="VARCHAR" />-->
        <!--    <result column="param2" property="param2" jdbcType="VARCHAR" />-->
        <!--    <result column="param3" property="param3" jdbcType="VARCHAR" />-->
        <!--    <result column="lastTime" property="lastTime" jdbcType="VARCHAR" />-->
        <result column="expiresDateMs" property="expiresDateMs" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectAllByCondition" parameterType="com.wbgame.pojo.clean.face.SuperFaceUserDTO"
            resultMap="BaseResultMap2">

        select userId, phoneNumber, creatTime, expiresDateMs,

        case when expiresDateMs / 1000 > UNIX_TIMESTAMP(CURRENT_TIMESTAMP) then "会员" else "非会员" end membership
        from super_face_user
        <where>

            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and creatTime between #{start_date} and #{end_date}
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                and phoneNumber like #{phoneNumber} "%"
            </if>
            <if test="userId != null and userId != ''">
                and userId = #{userId}
            </if>
        </where>

        order by creatTime desc
    </select>

    <insert id="batchClassArea" parameterType="com.wbgame.pojo.clean.face.CopyFaceModelConfig">

        <foreach collection="list" item="data" separator=";">

            insert into copy_face_model_class_config(classId,
            areaId,
            modelId,
            typeModifyTime)
            values (#{data.classId},
            #{data.areaId},
            #{data.id},
            now())
        </foreach>

    </insert>

    <delete id="deleteFaceModelConfigBatch" parameterType="java.lang.String">

        delete from copy_face_model_class_config
        where modelId IN
        <foreach collection="list" item="mid" open="(" separator="," close=")">
            #{mid}
        </foreach>
    </delete>

    <insert id="batchInsertCopyFaceModel" parameterType="com.wbgame.pojo.clean.face.CopyFaceModelConfig">

        <foreach collection="list" item="data" separator=";">

            insert into copy_face_model_config ( mid,tempTitle,tempUrl,videoUrl,tempType,state,isTop,videoHeight,videoWidth,projectId,templateFaceID,taskTime,
            createTime,modifyTime, createUser,modifyUser,status,region
            )
            values (#{data.mid},#{data.tempTitle} ,#{data.tempUrl}, #{data.videoUrl},#{data.tempType} ,#{data.state}, #{data.isTop},#{data.videoHeight} ,#{data.videoWidth},#{data.projectId} ,#{data.templateFaceID}, #{data.taskTime},
            NOW(),NOW(),#{data.createUser},#{data.modifyUser},#{data.status},#{data.region}
            )
        </foreach>

    </insert>

    <select id="selectByModelNameList" resultType="com.wbgame.pojo.clean.face.CopyFaceModelConfig" parameterType="com.wbgame.pojo.clean.face.CopyFaceModelConfig">


        <if test="list != null and list.size > 0">

            select * from copy_face_model_config where
            tempTitle in
            <foreach collection="list" item="data" open="(" separator="," close=")">
                #{data.tempTitle}
            </foreach>
        </if>


    </select>

    <update id="batchUpdateCopyFaceModelToOffShelve" parameterType="java.util.List">
        <foreach collection="midList" item="mid" separator=";">
        update copy_face_model_config
            <set>
                state = 2,
                modifyUser = #{loginUserName,jdbcType=VARCHAR},
                modifyTime = NOW()
            </set>
        where
            mid = #{mid,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateCopyFaceModelToOffShelve">
        update copy_face_model_config
        <set>
            state = 2,
            modifyUser = #{loginUserName,jdbcType=VARCHAR},
            modifyTime = NOW()
        </set>
        where
        mid = #{mid,jdbcType=VARCHAR}
    </update>
</mapper>