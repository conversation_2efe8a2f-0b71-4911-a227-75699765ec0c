<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.BargainLevelConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.chop.BargainLevelConfig">
        <id column="levelId" property="levelId" jdbcType="INTEGER"/>
        <result column="levelTitleNum" property="levelTitleNum" jdbcType="VARCHAR"/>
        <result column="defaultBasic" property="defaultBasic" jdbcType="DECIMAL"/>
        <result column="defaultLevel" property="defaultLevel" jdbcType="DECIMAL"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="createUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="updateUser" property="updateUser" jdbcType="VARCHAR"/>
        <result column="switchingCoefficient" property="switchingCoefficient" jdbcType="VARCHAR"/>
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updateTime" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="levelCfg" property="levelCfg" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        levelId
        , levelTitleNum,levelCfg, defaultBasic, defaultLevel, status, createUser, updateUser,switchingCoefficient,
            date_format(createTime, '%Y-%m-%d %H:%i:%s') createTime,
            date_format(updateTime, '%Y-%m-%d %H:%i:%s') updateTime
    </sql>

    <select id="selectLevelConfig" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.clean.chop.BargainLevelConfig">
        select
        <include refid="Base_Column_List"/>

        from bargain_level_config
        <where>

            <if test="levelTitleNum != null and levelTitleNum != ''">
                and levelTitleNum = #{levelTitleNum}
            </if>

            <if test="levelId != null">
                and levelId = #{levelId}
            </if>
        </where>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>

            <otherwise>
                order by levelId desc
            </otherwise>
        </choose>


    </select>
    <delete id="deleteLevelConfig" parameterType="java.lang.Integer">
        delete
        from bargain_level_config
        where levelId in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertLevelConfig" parameterType="com.wbgame.pojo.clean.chop.BargainLevelConfig"
            useGeneratedKeys="true" keyColumn="levelId" keyProperty="levelId">
        insert into bargain_level_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="levelTitleNum != null and levelTitleNum != ''">
                levelTitleNum,
            </if>
            <if test="defaultBasic != null">
                defaultBasic,
            </if>
            <if test="defaultLevel != null">
                defaultLevel,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createUser != null and createUser != ''">
                createUser,
            </if>

            <if test="createTime != null">
                createTime,
            </if>
            <if test="updateTime != null">
                updateTime,
            </if>
            <if test="levelCfg != null and levelCfg != ''">
                levelCfg,
            </if>
            <if test="switchingCoefficient != null and switchingCoefficient != ''">
                switchingCoefficient,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="levelTitleNum != null and levelTitleNum != ''">
                #{levelTitleNum,jdbcType=VARCHAR},
            </if>
            <if test="defaultBasic != null">
                #{defaultBasic,jdbcType=INTEGER},
            </if>
            <if test="defaultLevel != null">
                #{defaultLevel,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>

            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="levelCfg != null and levelCfg != ''">
                #{levelCfg,jdbcType=LONGVARCHAR},
            </if>
            <if test="switchingCoefficient != null and switchingCoefficient != ''">
                #{switchingCoefficient},
            </if>
        </trim>
    </insert>

    <update id="updateLevelConfig" parameterType="com.wbgame.pojo.clean.chop.BargainLevelConfig">
        update bargain_level_config
        <set>
            <if test="levelTitleNum != null and levelTitleNum != ''">
                levelTitleNum = #{levelTitleNum,jdbcType=VARCHAR},
            </if>
            <if test="defaultBasic != null">
                defaultBasic = #{defaultBasic,jdbcType=INTEGER},
            </if>
            <if test="defaultLevel != null">
                defaultLevel = #{defaultLevel,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="updateUser != null and updateUser != ''">
                updateUser = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                createTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="switchingCoefficient != null">
                switchingCoefficient = #{switchingCoefficient},
            </if>
            levelCfg = #{levelCfg,jdbcType=LONGVARCHAR},
            updateTime = now()
        </set>
        where levelId = #{levelId,jdbcType=INTEGER}
    </update>

    <select id="selectCountLevelById" parameterType="java.lang.Integer" resultMap="BaseResultMap">

        select levelId,  levelTitleNum from  bargain_level_config
        where levelId in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by levelId
    </select>

</mapper>