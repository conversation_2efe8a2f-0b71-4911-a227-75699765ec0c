<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.CleanAdMapper">

	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>


	<select id="selectExtendAdinfoVo" parameterType="com.wbgame.pojo.clean.adconfig.ExtendAdinfoVo" resultType="com.wbgame.pojo.clean.adconfig.ExtendAdinfoVo" >
	    select 
	    id, appid, cha_id, prjid, user_group, shield_name, policy_name
	    from dn_extend_adinfo_manage
	    where 1=1
	    <if test="appid != null and appid != ''">
		 and  appid = #{appid,jdbcType=VARCHAR}
		</if>
		<if test="cha_id != null and cha_id != ''">
		 and  cha_id = #{cha_id,jdbcType=VARCHAR}
		</if>
		<if test="shield_name != null and shield_name != ''">
			 and  shield_name = #{shield_name,jdbcType=VARCHAR}
		</if>
		<if test="policy_name != null and policy_name != ''">
			 and  policy_name = #{policy_name,jdbcType=VARCHAR}
		</if>
  	</select>
	  <update id="updateExtendAdinfoVo" parameterType="com.wbgame.pojo.clean.adconfig.ExtendAdinfoVo">
	    update dn_extend_adinfo_manage
	    set appid = #{appid,jdbcType=INTEGER},
	      cha_id = #{cha_id,jdbcType=VARCHAR},
	      prjid = #{prjid,jdbcType=INTEGER},
	      user_group = #{user_group,jdbcType=VARCHAR},
	      shield_name = #{shield_name,jdbcType=VARCHAR},
	      policy_name = #{policy_name,jdbcType=VARCHAR}
	    where id = #{id,jdbcType=INTEGER}
	  </update>
	  <insert id="insertExtendAdinfoVo" parameterType="com.wbgame.pojo.clean.adconfig.ExtendAdinfoVo">
	    insert into dn_extend_adinfo_manage (appid, cha_id, prjid, 
	      user_group, shield_name, policy_name
	      )
	    values (#{appid,jdbcType=INTEGER}, #{cha_id,jdbcType=VARCHAR}, #{prjid,jdbcType=INTEGER}, 
	      #{user_group,jdbcType=VARCHAR}, #{shield_name,jdbcType=VARCHAR}, #{policy_name,jdbcType=VARCHAR}
	      )
	  </insert>
	   <delete id="deleteExtendAdinfoVo" parameterType="com.wbgame.pojo.clean.adconfig.ExtendAdinfoVo">
	    delete from dn_extend_adinfo_manage
	    where id = #{id,jdbcType=INTEGER}
	  </delete>
	
	<update id="updateExtendAdshieldVo" parameterType="com.wbgame.pojo.clean.adconfig.ExtendAdshieldVo" >
	    update dn_extend_shield_manage
	    set shield_addr = #{shield_addr,jdbcType=VARCHAR},
	      shield_manu = #{shield_manu,jdbcType=VARCHAR},
	      shield_net = #{shield_net,jdbcType=VARCHAR},
	      shield_ip = #{shield_ip,jdbcType=VARCHAR},
	      shield_num = #{shield_num,jdbcType=VARCHAR}
	    where shield_name = #{shield_name,jdbcType=VARCHAR}
	  </update>
	 <select id="selectExtendAdshieldVo" resultType="com.wbgame.pojo.clean.adconfig.ExtendAdshieldVo" >
	    select 
	   	shield_name, shield_addr, shield_manu, shield_net, shield_ip, shield_num
	    from dn_extend_shield_manage
	    where  1=1
	    <if test="shield_name != null and shield_name != ''">
		 and  shield_name = #{shield_name,jdbcType=VARCHAR}
		</if>
  	</select>
		<insert id="insertExtendAdshieldVo" parameterType="com.wbgame.pojo.clean.adconfig.ExtendAdshieldVo" >
	    insert into dn_extend_shield_manage (shield_name, shield_addr, shield_manu, shield_net, shield_ip, shield_num )
	   	 values (#{shield_name,jdbcType=VARCHAR}, #{shield_addr,jdbcType=VARCHAR}, #{shield_manu,jdbcType=VARCHAR}, 
	      #{shield_net,jdbcType=VARCHAR}, #{shield_ip,jdbcType=VARCHAR}, #{shield_num,jdbcType=VARCHAR}
	      )
	  </insert>
	   <delete id="deleteExtendAdshieldVo" parameterType="com.wbgame.pojo.clean.adconfig.ExtendAdshieldVo" >
	    delete from dn_extend_shield_manage
	    where shield_name = #{shield_name,jdbcType=VARCHAR}
	  </delete>
	  
	  <select id="selectExtendAdtsVo" resultType="com.wbgame.pojo.clean.adconfig.ExtendAdtsVo" >
	    select 
	    policy_name, ad_type, time_interval, ad_limit, ad_day, times
	    from dn_extend_adts_manage
	    where 1=1
	    <if test="policy_name != null and policy_name != ''">
		 and  policy_name = #{policy_name,jdbcType=VARCHAR}
		</if>
		<if test="ad_type != null and ad_type != ''">
		 and  ad_type = #{ad_type,jdbcType=VARCHAR}
		</if>
	  </select>
	  <update id="updateExtendAdtsVo" parameterType="com.wbgame.pojo.clean.adconfig.ExtendAdtsVo" >
	    update dn_extend_adts_manage
	    set ad_type = #{ad_type,jdbcType=VARCHAR},
	      time_interval = #{time_interval,jdbcType=INTEGER},
	      ad_limit = #{ad_limit,jdbcType=INTEGER},
	      ad_day = #{ad_day,jdbcType=INTEGER},
	      times = #{times,jdbcType=INTEGER}
	    where policy_name = #{policy_name,jdbcType=VARCHAR}
	  </update>
	  <insert id="insertExtendAdtsVo" parameterType="com.wbgame.pojo.clean.adconfig.ExtendAdtsVo" >
	    insert into dn_extend_adts_manage (policy_name, ad_type, time_interval, 
	      ad_limit, ad_day, times
	      )
	    values (#{policy_name,jdbcType=VARCHAR}, #{ad_type,jdbcType=VARCHAR}, #{time_interval,jdbcType=INTEGER}, 
	      #{ad_limit,jdbcType=INTEGER}, #{ad_day,jdbcType=INTEGER}, #{times,jdbcType=INTEGER}
	      )
	  </insert>
	   <delete id="deleteExtendAdtsVo" parameterType="com.wbgame.pojo.clean.adconfig.ExtendAdtsVo" >
	    delete from dn_extend_adts_manage
	    where policy_name = #{policy_name,jdbcType=VARCHAR}
	  </delete>
	  
	   <select id="selectShieldName" resultType="String" >
	    select 
	   	shield_name
	    from dn_extend_shield_manage
  		</select>
  		
  		 <select id="selectPolicyName" resultType="String" >
	    select 
	    policy_name
	    from dn_extend_adts_manage
  		</select>
</mapper>