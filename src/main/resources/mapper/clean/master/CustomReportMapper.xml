<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.clean.master.CustomReportMapper">


  <select id="getTables" resultType="com.wbgame.pojo.mobile.TableVo">
    select TABLE_NAME as name,if(TABLE_COMMENT='',TABLE_NAME,TABLE_COMMENT)  as comment from INFORMATION_SCHEMA.TABLES
    where TABLE_SCHEMA in ('yyhz_0308','superm')  and TABLE_COMMENT != '' and TABLE_NAME like concat('umeng','%')
    order by CREATE_TIME desc
  </select>

  <select id="getColumns" resultType="com.wbgame.pojo.mobile.ColumnVo">
    SELECT
        A.COLUMN_NAME 'column',
        A<PERSON>DATA_TYPE type,
        <PERSON><PERSON>COLUMN_COMMENT name
    FROM INFORMATION_SCHEMA.COLUMNS A
    WHERE A.TABLE_SCHEMA in ('yyhz_0308','superm')
    and A.TABLE_NAME = #{table}
    ORDER BY A.ORDINAL_POSITION
  </select>

    <select id="getStrColumns" resultType="java.lang.String">
        SELECT
        A.COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS A
        WHERE A.TABLE_SCHEMA in ('yyhz_0308','superm')
        and A.TABLE_NAME = #{table}
        ORDER BY A.ORDINAL_POSITION
    </select>

    <select id="selectCustomReport" resultType="java.util.Map">
        select
        <foreach collection="columns" item="it" separator=",">
            ${it}
        </foreach>
         from ${table}
        <where> ds between #{begin} and #{end} and appid = #{appid,jdbcType=VARCHAR}
            <if test="channel != null and channel != ''">
                and install_channel = #{channel}
            </if>
            <if test="version != null and version != ''">
                and app_version = #{version}
            </if>
        </where>
        order by ${order_str}
    </select>
</mapper>