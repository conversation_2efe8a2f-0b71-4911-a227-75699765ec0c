<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.GoodsConfigDetailMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.GoodsConfigDetail">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="config_id" property="configId" jdbcType="BIGINT"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="charge_name" property="chargeName" jdbcType="VARCHAR"/>
        <result column="good_id" property="goodId" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
        <result column="modify_user" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , config_id, group_name, charge_name, good_id, create_user, create_time, modify_user,
    modify_time
    </sql>
    <select id="selectGoodsConfigDetail" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.GoodsConfigDetail">
        select

        <include refid="Base_Column_List"/>
        from gj_b.goods_config_detail

    </select>

    <select id="selectGoodsConfigDetailById" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.GoodsConfigDetail">
        select

        <include refid="Base_Column_List"/>
        from gj_b.goods_config_detail
        where config_id = #{configId}

    </select>

    <delete id="deleteGoodsConfigDetail" parameterType="java.lang.Long">
        delete
        from gj_b.goods_config_detail
        where config_id in
        <foreach collection="list" item="cfgId" open="(" separator="," close=")">
            #{cfgId}
        </foreach>
    </delete>


    <insert id="insertGoodsConfigDetail" parameterType="com.wbgame.pojo.clean.GoodsConfigDetail">

        <foreach collection="list" item="data" separator=";">


            insert into gj_b.goods_config_detail
            <trim prefix="(" suffix=")" suffixOverrides=",">

                <if test="data.configId != null">
                    config_id,
                </if>
                <if test="data.groupName != null">
                    group_name,
                </if>
                <if test="data.chargeName != null">
                    charge_name,
                </if>
                <if test="data.goodId != null">
                    good_id,
                </if>
                <if test="data.createUser != null">
                    create_user,
                </if>
                <if test="data.createTime != null">
                    create_time,
                </if>

                <if test="data.modifyTime != null">
                    modify_time,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">

                <if test="data.configId != null">
                    #{data.configId,jdbcType=BIGINT},
                </if>
                <if test="data.groupName != null">
                    #{data.groupName,jdbcType=VARCHAR},
                </if>
                <if test="data.chargeName != null">
                    #{data.chargeName,jdbcType=VARCHAR},
                </if>
                <if test="data.goodId != null">
                    #{data.goodId,jdbcType=VARCHAR},
                </if>
                <if test="data.createUser != null">
                    #{data.createUser,jdbcType=VARCHAR},
                </if>
                <if test="data.createTime != null">
                    #{data.createTime,jdbcType=BIGINT},
                </if>

                <if test="data.modifyTime != null">
                    #{data.modifyTime,jdbcType=BIGINT},
                </if>
            </trim>
        </foreach>
    </insert>

    <update id="updateGoodsConfigDetail" parameterType="com.wbgame.pojo.clean.GoodsConfigDetail">
        update gj_b.goods_config_detail
        <set>
            <if test="configId != null">
                config_id = #{configId,jdbcType=BIGINT},
            </if>
            <if test="groupName != null">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="chargeName != null">
                charge_name = #{chargeName,jdbcType=VARCHAR},
            </if>
            <if test="goodId != null">
                good_id = #{goodId,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="modifyUser != null">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>