<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.SuperFaceUserMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.face.SuperFaceUserVO">
        <id column="userId" property="userId" jdbcType="VARCHAR"/>
        <!--    <result column="wxIcon" property="wxIcon" jdbcType="VARCHAR" />-->
        <!--    <result column="wxName" property="wxName" jdbcType="VARCHAR" />-->
        <result column="creatTime" property="creatTime" jdbcType="VARCHAR"/>
        <result column="phoneNumber" property="phoneNumber" jdbcType="VARCHAR"/>
        <!--    <result column="lsn" property="lsn" jdbcType="VARCHAR" />-->
        <!--    <result column="appid" property="appid" jdbcType="VARCHAR" />-->
        <!--    <result column="imei" property="imei" jdbcType="VARCHAR" />-->
        <!--    <result column="prjid" property="prjid" jdbcType="VARCHAR" />-->
        <!--    <result column="cha" property="cha" jdbcType="VARCHAR" />-->
        <!--    <result column="oaid" property="oaid" jdbcType="VARCHAR" />-->
        <!--    <result column="platform" property="platform" jdbcType="VARCHAR" />-->
        <!--    <result column="param1" property="param1" jdbcType="VARCHAR" />-->
        <!--    <result column="param2" property="param2" jdbcType="VARCHAR" />-->
        <!--    <result column="param3" property="param3" jdbcType="VARCHAR" />-->
        <!--    <result column="lastTime" property="lastTime" jdbcType="VARCHAR" />-->
        <result column="expiresDateMs" property="expiresDateMs" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        userId
        , wxIcon, wxName, creatTime, phoneNumber, lsn, appid, imei, prjid, cha, oaid,
    platform, param1, param2, param3, lastTime, expiresDateMs
    </sql>

    <select id="selectAllByCondition" parameterType="com.wbgame.pojo.clean.face.SuperFaceUserDTO"
            resultMap="BaseResultMap">

        select userId, phoneNumber, creatTime, expiresDateMs,

        case when expiresDateMs / 1000 > UNIX_TIMESTAMP(CURRENT_TIMESTAMP) then "会员" else "非会员" end membership
        from super_face_user
        <where>

            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and creatTime between #{start_date} and #{end_date}
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                and phoneNumber like #{phoneNumber} "%"
            </if>
            <if test="userId != null and userId != ''">
                and userId = #{userId}
            </if>
        </where>

        order by creatTime desc
    </select>
</mapper>