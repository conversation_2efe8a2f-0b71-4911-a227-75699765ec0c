<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.VivoDefaultConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.VivoDefaultConfig">
        <id column="prjid" property="prjid" jdbcType="VARCHAR"/>
        <result column="m" property="m" jdbcType="INTEGER"/>
        <result column="n" property="n" jdbcType="INTEGER"/>
        <result column="m1" property="m1" jdbcType="INTEGER"/>
        <result column="n1" property="n1" jdbcType="INTEGER"/>
        <result column="r" property="r" jdbcType="DOUBLE"/>
        <result column="defaultAvgEcpm" property="defaultAvgEcpm" jdbcType="DOUBLE"/>
        <result column="createTime" property="createTime" jdbcType="VARCHAR"/>
        <result column="createUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="modifyUser" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="modifyTime" property="modifyTime" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        prjid
        , m, n, m1, n1, r, defaultAvgEcpm, createTime, createUser, modifyUser, modifyTime, status,appid
    </sql>
    <select id="selectVivoConfig" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.VivoDefaultConfig">
        select

        <include refid="Base_Column_List"/>
        from vivo_default_config

        <where>
            <if test="prjid != null and prjid != ''">
                and prjid = #{prjid}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>
        order by modifyTime desc
    </select>

    <delete id="deleteVivoConfig" parameterType="java.lang.String">
        delete
        from vivo_default_config
        where prjid in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertVivoConfig" parameterType="com.wbgame.pojo.clean.VivoDefaultConfig">
        insert into vivo_default_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="prjid != null and prjid != ''">
                prjid,
            </if>
            <if test="m != null">
                m,
            </if>
            <if test="n != null">
                n,
            </if>
            <if test="m1 != null">
                m1,
            </if>
            <if test="n1 != null">
                n1,
            </if>
            <if test="r != null">
                r,
            </if>
            <if test="defaultAvgEcpm != null">
                defaultAvgEcpm,
            </if>

            <if test="createUser != null and createUser != ''">
                createUser,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="appid != null and appid != ''">
                appid,
            </if>
            createTime,
            modifyTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="prjid != null and prjid != ''">
                #{prjid,jdbcType=VARCHAR},
            </if>
            <if test="m != null">
                #{m,jdbcType=INTEGER},
            </if>
            <if test="n != null">
                #{n,jdbcType=INTEGER},
            </if>
            <if test="m1 != null">
                #{m1,jdbcType=INTEGER},
            </if>
            <if test="n1 != null">
                #{n1,jdbcType=INTEGER},
            </if>
            <if test="r != null">
                #{r},
            </if>
            <if test="defaultAvgEcpm != null">
                #{defaultAvgEcpm,jdbcType=DOUBLE},
            </if>
            <if test="createTime != null and createTime != ''">
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>

            <if test="modifyTime != null and modifyTime != ''">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="appid != null and appid != ''">
                #{appid},
            </if>
            now(),
            now()
        </trim>
    </insert>

    <update id="updateVivoConfig" parameterType="com.wbgame.pojo.clean.VivoDefaultConfig">
        update vivo_default_config

        set

            m = #{m,jdbcType=INTEGER},
            n = #{n,jdbcType=INTEGER},
            m1 = #{m1,jdbcType=INTEGER},
            n1 = #{n1,jdbcType=INTEGER},
            r = #{r},
            defaultAvgEcpm = #{defaultAvgEcpm,jdbcType=DOUBLE},
            modifyUser = #{modifyUser,jdbcType=VARCHAR},
            status = #{status},
            modifyTime = now()


        where prjid = #{prjid,jdbcType=VARCHAR}
    </update>


    <update id="batchUpdateVivoConfig" parameterType="com.wbgame.pojo.clean.VivoDefaultConfig">


        <if test="prjidList != null and prjidList.size > 0">

            update vivo_default_config

            set

            m = #{m,jdbcType=INTEGER},
            n = #{n,jdbcType=INTEGER},
            m1 = #{m1,jdbcType=INTEGER},
            n1 = #{n1,jdbcType=INTEGER},
            r = #{r},
            defaultAvgEcpm = #{defaultAvgEcpm,jdbcType=DOUBLE},
            modifyUser = #{modifyUser,jdbcType=VARCHAR},
            status = #{status},
            modifyTime = now()
            where prjid in

            <foreach collection="prjidList" item="prj" open="(" separator="," close=")">
                #{prj}
            </foreach>
        </if>

    </update>

</mapper>