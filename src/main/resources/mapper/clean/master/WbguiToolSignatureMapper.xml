<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.WbguiToolSignatureMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.WbguiToolSignature">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="storepass" property="storepass" jdbcType="VARCHAR"/>
        <result column="keypass" property="keypass" jdbcType="VARCHAR"/>
        <result column="alias" property="alias" jdbcType="VARCHAR"/>
        <result column="keystore" property="keystore" jdbcType="VARCHAR"/>
        <result column="MD5" property="md5" jdbcType="LONGVARCHAR"/>
        <result column="SHA1" property="sha1" jdbcType="LONGVARCHAR"/>
        <result column="SHA256" property="sha256" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , storepass, keypass, alias, keystore,MD5, SHA1, SHA256,appid
    </sql>

    <select id="selectWbguiToolSignature" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.WbguiToolSignature">
        select
        <include refid="Base_Column_List"/>

        from wbgui_tool_signature
        <where>
            <if test="storepass != null and storepass != ''">
                and storepass = #{storepass,jdbcType=VARCHAR}
            </if>
            <if test="keypass != null and keypass != ''">
                and keypass = #{keypass,jdbcType=VARCHAR}
            </if>
            <if test="alias != null and alias != ''">
                and alias = #{alias,jdbcType=VARCHAR}
            </if>
            <if test="keystore != null and keystore != ''">
                and keystore = #{keystore,jdbcType=VARCHAR}
            </if>
            <if test="md5 != null and md5 != ''">
                and MD5 = #{md5,jdbcType=LONGVARCHAR}
            </if>
            <if test="sha1 != null and sha1 != ''">
                and SHA1 = #{sha1,jdbcType=LONGVARCHAR}
            </if>
            <if test="sha256 != null and sha256 != ''">
                and SHA256 = #{sha256,jdbcType=LONGVARCHAR}
            </if>
        </where>
        order by id desc
    </select>
    <delete id="deleteWbguiToolSignature" parameterType="java.lang.Integer">
        delete from wbgui_tool_signature
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <insert id="insertWbguiToolSignature" parameterType="com.wbgame.pojo.clean.WbguiToolSignature">
        insert into wbgui_tool_signature
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="storepass != null and storepass != ''">
                storepass,
            </if>
            <if test="keypass != null and keypass != ''">
                keypass,
            </if>
            <if test="alias != null and alias != ''">
                alias,
            </if>
            <if test="keystore != null and keystore != ''">
                keystore,
            </if>
            <if test="md5 != null and md5 != ''">
                MD5,
            </if>
            <if test="sha1 != null and sha1 != ''">
                SHA1,
            </if>
            <if test="sha256 != null and sha256 != ''">
                SHA256,
            </if>
            <if test="appid != null and appid != ''">
                appid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="storepass != null and storepass != ''">
                #{storepass,jdbcType=VARCHAR},
            </if>
            <if test="keypass != null and keypass != ''">
                #{keypass,jdbcType=VARCHAR},
            </if>
            <if test="alias != null and alias != ''">
                #{alias,jdbcType=VARCHAR},
            </if>
            <if test="keystore != null and keystore != ''">
                #{keystore,jdbcType=VARCHAR},
            </if>
            <if test="md5 != null and md5 != ''">
                #{md5,jdbcType=LONGVARCHAR},
            </if>
            <if test="sha1 != null and sha1 != ''">
                #{sha1,jdbcType=LONGVARCHAR},
            </if>
            <if test="sha256 != null and sha256 != ''">
                #{sha256,jdbcType=LONGVARCHAR},
            </if>
            <if test="appid != null and appid != ''">
                #{appid},
            </if>
        </trim>
    </insert>

    <update id="updateWbguiToolSignature" parameterType="com.wbgame.pojo.clean.WbguiToolSignature">
        update wbgui_tool_signature
        <set>
            <if test="storepass != null and storepass != ''">
                storepass = #{storepass,jdbcType=VARCHAR},
            </if>
            <if test="keypass != null and keypass != ''">
                keypass = #{keypass,jdbcType=VARCHAR},
            </if>
            <if test="alias != null and alias != ''">
                alias = #{alias,jdbcType=VARCHAR},
            </if>
            <if test="keystore != null and keystore != ''">
                keystore = #{keystore,jdbcType=VARCHAR},
            </if>
            <if test="md5 != null and md5 != ''">
                MD5 = #{md5,jdbcType=LONGVARCHAR},
            </if>
            <if test="sha1 != null and sha1 != ''">
                SHA1 = #{sha1,jdbcType=LONGVARCHAR},
            </if>
            <if test="sha256 != null and sha256 != ''">
                SHA256 = #{sha256,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

</mapper>