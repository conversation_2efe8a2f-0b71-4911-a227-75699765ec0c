<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.AppConfigControlMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.AppConfigControlVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="cha" property="cha" jdbcType="VARCHAR"/>
        <result column="prjid" property="prjid" jdbcType="VARCHAR"/>
        <result column="audit" property="audit" jdbcType="VARCHAR"/>
        <result column="createTime" property="createTime" jdbcType="VARCHAR"/>
        <result column="modifyTime" property="modifyTime" jdbcType="VARCHAR"/>
        <result column="modifyUser" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="createUser" property="createUser" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , appid, cha, prjid, audit, createTime, modifyTime, modifyUser, status,createUser
    </sql>
    <select id="selectAppConfig" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.AppConfigControl">
        select

        <include refid="Base_Column_List"/>
        from app_config_control

        <where>

            <if test="audit != null and audit != ''">
                and audit = #{audit}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>

            <if test="channelList != null and channelList.size > 0">
                AND cha IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

            <if test="prjidList != null and prjidList.size > 0">
                AND prjid IN
                <foreach collection="prjidList" item="prj" open="(" separator="," close=")">
                    #{prj}
                </foreach>
            </if>
        </where>

        order by id desc
    </select>

    <delete id="deleteAppConfig" parameterType="java.lang.Integer">
        delete
        from app_config_control
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertAppConfig" parameterType="com.wbgame.pojo.clean.AppConfigControl">
        insert into app_config_control (id, appid, cha,
                                        prjid, audit, createTime,
                                        modifyTime, status,createUser)
        values (#{id,jdbcType=INTEGER}, #{appid,jdbcType=VARCHAR}, #{cha,jdbcType=VARCHAR},
                #{prjid,jdbcType=VARCHAR}, #{audit,jdbcType=VARCHAR}, now(),
                now(), #{status,jdbcType=VARCHAR},#{createUser})
    </insert>

    <update id="updateAppConfig" parameterType="com.wbgame.pojo.clean.AppConfigControl">
        update app_config_control
        set
            audit      = #{audit,jdbcType=VARCHAR},
            modifyTime = now(),
            modifyUser = #{modifyUser,jdbcType=VARCHAR},
            status     = #{status,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
    
    <select id="duplicateCheck" parameterType="com.wbgame.pojo.clean.AppConfigControl" resultType="java.lang.Integer">
        
        select id from app_config_control
        where appid = #{appid}
        
        <if test="cha != null and cha != ''">
            and cha = #{cha}
        </if>
        <if test="prjid != null and prjid != ''">
            and prjid = #{prjid}
        </if>
              
        limit 1
    </select>
</mapper>