<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.CouponsMapper">

    <select id="getCouponsValidateWithdrawList" parameterType="com.wbgame.pojo.clean.coupons.CouponsWithdrawVo" resultType="com.wbgame.pojo.clean.coupons.CouponsWithdrawVo">
        select * from coupons_withdraw_record
        where 1=1 and isvalidate = 1
        <if test="appid != null and appid != ''">
            and appid in(${appid})
        </if>
        <if test="prjid != null and prjid != ''">
            and prjid in(${prjid})
        </if>
        <if test="cha != null and cha != ''">
            and cha in (${cha})
        </if>
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
        <if test="amount != null and amount != ''">
            and amount = #{amount,jdbcType=VARCHAR}
        </if>
        <if test="userid != null and userid != ''">
            and userid = #{userid,jdbcType=VARCHAR}
        </if>
        and DATE(ordertime) <![CDATA[ >= ]]> DATE (#{startTime}) and DATE(ordertime) <![CDATA[ <= ]]> DATE(#{endTime})
    </select>

    <select id="getCouponsWithdrawList" parameterType="com.wbgame.pojo.clean.coupons.CouponsWithdrawVo" resultType="com.wbgame.pojo.clean.coupons.CouponsWithdrawVo">
        select * from coupons_withdraw_record
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in(${appid})
        </if>
        <if test="prjid != null and prjid != ''">
            and prjid in(${prjid})
        </if>
        <if test="cha != null and cha != ''">
            and cha in (${cha})
        </if>
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
        <if test="amount != null and amount != ''">
            and amount = #{amount,jdbcType=VARCHAR}
        </if>
        <if test="userid != null and userid != ''">
            and userid = #{userid,jdbcType=VARCHAR}
        </if>
        and DATE(ordertime) <![CDATA[ >= ]]> DATE (#{startTime}) and DATE(ordertime) <![CDATA[ <= ]]> DATE(#{endTime})
    </select>


    <select id="getCouponsOrderList" parameterType="com.wbgame.pojo.clean.coupons.CouponsOrderVo" resultType="com.wbgame.pojo.clean.coupons.CouponsOrderVo">
        select * from coupons_user_order_info
        where 1=1
        <if test="userid != null and userid != ''">
            and userid = #{userid,jdbcType=VARCHAR}
        </if>
        <if test="orderId != null and orderId != ''">
            and orderId = #{orderId,jdbcType=VARCHAR}
        </if>
        <if test="platform != null and platform != ''">
            and platform = #{platform,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
        and DATE(orderTime) <![CDATA[ >= ]]> DATE (#{startTime}) and DATE(orderTime) <![CDATA[ <= ]]> DATE(#{endTime})
    </select>

    <select id="getCouponsTbOrderList" resultType="com.wbgame.pojo.clean.coupons.CouponsTbOrderVo" parameterType="com.wbgame.pojo.clean.coupons.CouponsTbOrderVo">
        select * from coupons_tb_order where 1=1
        <if test="trade_id != null and trade_id != ''">
            and trade_id = #{trade_id,jdbcType=VARCHAR}
        </if>
        <if test="trade_parent_id != null and trade_parent_id != ''">
            and trade_parent_id = #{trade_parent_id,jdbcType=VARCHAR}
        </if>
        <if test="tk_status != null and tk_status != ''">
            and tk_status = #{tk_status,jdbcType=VARCHAR}
        </if>
        and DATE(tk_create_time) <![CDATA[ >= ]]> DATE (#{startTime}) and DATE(tk_create_time) <![CDATA[ <= ]]> DATE(#{endTime})
    </select>

    <select id="getCouponsMtOrderList" resultType="com.wbgame.pojo.clean.coupons.CouponsMtOrderVo" parameterType="com.wbgame.pojo.clean.coupons.CouponsMtOrderVo">
        select * from coupons_mt_order where 1=1
        <if test="orderid != null and orderid != ''">
            and orderid = #{orderid,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
        and DATE(FROM_UNIXTIME(paytime)) <![CDATA[ >= ]]> DATE (#{startTime}) and DATE(FROM_UNIXTIME(paytime)) <![CDATA[ <= ]]> DATE(#{endTime})
    </select>

    <select id="getCouponsRatioConfigList" resultType="com.wbgame.pojo.clean.coupons.CouponsRatioConfigVo" parameterType="com.wbgame.pojo.clean.coupons.CouponsRatioConfigVo">
        select * from coupons_ratio_config where 1=1
        <if test="prjid != null and prjid != ''">
            and prjid = #{prjid,jdbcType=VARCHAR}
        </if>
        <if test="cha != null and cha != ''">
            and cha = #{cha,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
    </select>


    <delete id="delCouponsRatio" parameterType="com.wbgame.pojo.clean.coupons.CouponsRatioConfigVo">
        delete from coupons_ratio_config where id = #{id}
    </delete>

    <insert id="addCouponsRatio" parameterType="com.wbgame.pojo.clean.coupons.CouponsRatioConfigVo">
        insert into coupons_ratio_config (`prjid`, cha,ratio,`status`,createUser,createTime)
        values  (#{prjid,jdbcType=VARCHAR}, #{cha,jdbcType=VARCHAR},#{ratio,jdbcType=VARCHAR},
                 #{status,jdbcType=VARCHAR},#{createUser,jdbcType=VARCHAR},NOW())
    </insert>
    <update id="updateCouponsRatio" parameterType="com.wbgame.pojo.clean.coupons.CouponsRatioConfigVo">
        update coupons_ratio_config
        set `prjid` = #{prjid,jdbcType=VARCHAR},
            cha = #{cha,jdbcType=VARCHAR},
            ratio = #{ratio,jdbcType=VARCHAR},
            modifyUser = #{modifyUser,jdbcType=VARCHAR},
            modifyTime = now(),
            status = #{status,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>


</mapper>