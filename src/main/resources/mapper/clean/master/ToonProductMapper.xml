<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.ToonProductMapper">

    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.toonstory.ToonProductVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="platform_product_id" jdbcType="VARCHAR" property="platformProductId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_describe" jdbcType="VARCHAR" property="productDescribe"/>
        <result column="product_price" jdbcType="VARCHAR" property="productPrice"/>
        <result column="cycle" jdbcType="TINYINT" property="cycle"/>
        <result column="feeDay" jdbcType="INTEGER" property="feeDay"/>
        <result column="cha" jdbcType="VARCHAR" property="cha"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="modify_user" jdbcType="VARCHAR" property="modifyUser"/>
        <result column="modify_time" jdbcType="BIGINT" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        platform_product_id,
        product_name,
        product_describe,
        product_price,
        cycle,
        feeDay,
        cha,
        STATUS,
        sort,
        create_user,
        from_unixtime( create_time / 1000, '%Y-%m-%d %H:%i:%s' ) create_time,
        modify_user,
        from_unixtime( modify_time / 1000, '%Y-%m-%d %H:%i:%s' ) modify_time
    </sql>

    <select id="selectToonProduct" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.clean.toonstory.ToonProductDTO">
        select
        <include refid="Base_Column_List"/>
        from gj_b.toon_product
        <where>

            <if test="id != null">
                and id = #{id}
            </if>
            <if test="platformProductId != null and platformProductId != ''">
                and platform_product_id = #{platformProductId}
            </if>
            <if test="productName != null and productName != ''">
                and product_name = #{productName}
            </if>
            <if test="cha != null and cha != ''">
                and cha = #{cha}
            </if>
            <if test="cycle != null">
                and cycle = #{cycle}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>

        order by id desc
    </select>
    <delete id="deleteToonProduct" parameterType="java.lang.Long">
        delete
        from gj_b.toon_product
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertToonProduct" parameterType="com.wbgame.pojo.clean.toonstory.ToonProduct"
            useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into gj_b.toon_product
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="platformProductId != null and platformProductId != ''">
                platform_product_id,
            </if>
            <if test="productName != null and productName != ''">
                product_name,
            </if>
            <if test="productDescribe != null and productDescribe != ''">
                product_describe,
            </if>
            <if test="productPrice != null and productPrice != ''">
                product_price,
            </if>
            <if test="cycle != null">
                cycle,
            </if>
            <if test="feeDay != null">
                feeDay,
            </if>
            <if test="cha != null and cha != ''">
                cha,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="createUser != null and createUser != ''">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyUser != null and modifyUser != ''">
                modify_user,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="platformProductId != null and platformProductId != ''">
                #{platformProductId,jdbcType=VARCHAR},
            </if>
            <if test="productName != null and productName != ''">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productDescribe != null and productDescribe != ''">
                #{productDescribe,jdbcType=VARCHAR},
            </if>
            <if test="productPrice != null and productPrice != ''">
                #{productPrice,jdbcType=VARCHAR},
            </if>
            <if test="cycle != null">
                #{cycle,jdbcType=TINYINT},
            </if>
            <if test="feeDay != null">
                #{feeDay,jdbcType=INTEGER},
            </if>
            <if test="cha != null and cha != ''">
                #{cha,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="modifyUser != null and modifyUser != ''">
                #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateToonProduct" parameterType="com.wbgame.pojo.clean.toonstory.ToonProduct">
        update gj_b.toon_product
        <set>
            <if test="platformProductId != null and platformProductId != ''">
                platform_product_id = #{platformProductId,jdbcType=VARCHAR},
            </if>
            <if test="productName != null and productName != ''">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productDescribe != null and productDescribe != ''">
                product_describe = #{productDescribe,jdbcType=VARCHAR},
            </if>
            <if test="productPrice != null and productPrice != ''">
                product_price = #{productPrice,jdbcType=VARCHAR},
            </if>
            <if test="cycle != null">
                cycle = #{cycle,jdbcType=TINYINT},
            </if>
            <if test="feeDay != null">
                feeDay = #{feeDay,jdbcType=INTEGER},
            </if>
            <if test="cha != null and cha != ''">
                cha = #{cha,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>

            <if test="modifyUser != null and modifyUser != ''">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.clean.toonstory.ToonProduct">
        update gj_b.toon_product
        set platform_product_id = #{platformProductId,jdbcType=VARCHAR},
            product_name        = #{productName,jdbcType=VARCHAR},
            product_describe    = #{productDescribe,jdbcType=VARCHAR},
            product_price       = #{productPrice,jdbcType=VARCHAR},
            cycle               = #{cycle,jdbcType=TINYINT},
            feeDay              = #{feeDay,jdbcType=INTEGER},
            cha                 = #{cha,jdbcType=VARCHAR},
            status              = #{status,jdbcType=TINYINT},
            sort                = #{sort,jdbcType=INTEGER},
            create_user         = #{createUser,jdbcType=VARCHAR},
            create_time         = #{createTime,jdbcType=BIGINT},
            modify_user         = #{modifyUser,jdbcType=VARCHAR},
            modify_time         = #{modifyTime,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateProductSort" parameterType="com.wbgame.pojo.clean.toonstory.ToonModelRegionRel">

        <foreach collection="list" item="data" separator=";">

            update gj_b.toon_product
            set sort = #{data.sort}, modify_user = #{data.modifyUser}, modify_time = #{data.modifyTime}
            where id = #{data.id}
        </foreach>
    </update>

    <update id="updateStatus" parameterType="com.wbgame.pojo.clean.toonstory.ToonModelRegionRel">

        update gj_b.toon_product
        set status = #{status} % 2, modify_user = #{modifyUser}, modify_time = #{modifyTime}
        where id = #{id}

    </update>

    <select id="selectCache" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.toonstory.ToonProductDTO">


        SELECT
            id,
            platform_product_id,
            product_name,
            product_describe,
            product_price,
            cycle,
            feeDay,
            cha
        FROM
            gj_b.toon_product
        WHERE
            cha = #{cha}
          AND STATUS = 1
        ORDER BY
            sort ASC
    </select>

    <select id="selectToonProductByCha" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from gj_b.toon_product
        where cha = #{cha}

        order by sort
    </select>
</mapper>