<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.clean.master.ArticleManageMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.ArticleManageVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="prj_id" jdbcType="VARCHAR" property="prjId"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="back_url" jdbcType="VARCHAR" property="backUrl"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="area_type" jdbcType="TINYINT" property="areaType"/>
        <result column="vip_flag" jdbcType="TINYINT" property="vipFlag"/>
        <result column="tab_type" jdbcType="TINYINT" property="tabType"/>
        <result column="article_type" jdbcType="TINYINT" property="articleType"/>
        <result column="article_seq" jdbcType="INTEGER" property="articleSeq"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="BIGINT" property="modifyTime"/>
        <result column="pic_url" jdbcType="VARCHAR" property="picUrl"/>
    </resultMap>

    <sql id="Base_Column_List">
        a.id,
        a.prj_id,
        a.app_id,
        a.back_url,
        a.title,
        a.area_type,
        a.vip_flag,
        a.tab_type,
        a.article_type,
        a.article_seq,
        a.creator,
        from_unixtime( a.create_time / 1000, '%Y-%m-%d %H:%i:%s' ) create_time,
        a.modifier,
        from_unixtime( a.modify_time / 1000, '%Y-%m-%d %H:%i:%s' ) modify_time,
        GROUP_CONCAT( b.pic_url separator " ^_^ ")  pic_url
    </sql>

    <select id="selectArticleManage" parameterType="com.wbgame.pojo.clean.ArticleManageDTO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM gj_b.article_manage a
        LEFT JOIN gj_b.article_pic_manage b ON a.id = b.article_id
        WHERE a.is_deleted = 0

        <if test="prjId != null and prjId != ''">
            and prj_id = #{prjId}
        </if>
        <if test="appidList != null and appidList.size > 0">
            and  app_id in
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        <if test="title != null and title != ''">
            and title like "%" #{title} "%"
        </if>
        <if test="areaType != null">
            and area_type = #{areaType}
        </if>
        <if test="vipFlag != null">
            and vip_flag = #{vipFlag}
        </if>
        <if test="tabType != null">
            and tab_type = #{tabType}
        </if>
        <if test="articleType != null">
            and article_type = #{articleType}
        </if>
        GROUP BY
            a.id,
            a.prj_id,
            a.app_id,
            a.back_url,
            a.title,
            a.area_type,
            a.vip_flag,
            a.tab_type,
            a.article_type,
            a.article_seq,
            a.creator,
            a.create_time,
            a.modifier,
            a.modify_time

        <choose>
            <when test="articleType != null and articleType == 1">
                order by article_seq, id desc
            </when>
            <otherwise>
                order by id
            </otherwise>
        </choose>

    </select>
    <update id="updateByIsDeleted" parameterType="com.wbgame.pojo.clean.ArticleManageDTO">

        <foreach collection="idList" item="id" separator=";">
            update gj_b.article_manage
            set is_deleted  = 1, modify_time = #{modifyTime}, modifier = #{modifier}
            where id = #{id}
        </foreach>
    </update>

    <insert id="insertArticleManage" parameterType="com.wbgame.pojo.clean.ArticleManage"
            useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into gj_b.article_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="prjId != null and prjId != ''">
                prj_id,
            </if>
            <if test="appId != null and appId != ''">
                app_id,
            </if>
            <if test="backUrl != null and backUrl != ''">
                back_url,
            </if>
            <if test="title != null and title != ''">
                title,
            </if>
            <if test="areaType != null">
                area_type,
            </if>
            <if test="vipFlag != null">
                vip_flag,
            </if>

            <if test="articleType != null">
                article_type,
            </if>

            <if test="creator != null and creator != ''">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifier != null and modifier != ''">
                modifier,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            tab_type,
            article_seq
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="prjId != null and prjId != ''">
                #{prjId,jdbcType=VARCHAR},
            </if>
            <if test="appId != null and appId != ''">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="backUrl != null and backUrl != ''">
                #{backUrl,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != ''">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="areaType != null">
                #{areaType,jdbcType=TINYINT},
            </if>
            <if test="vipFlag != null">
                #{vipFlag,jdbcType=TINYINT},
            </if>

            <if test="articleType != null">
                #{articleType,jdbcType=TINYINT},
            </if>

            <if test="creator != null and creator != ''">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="modifier != null and modifier != ''">
                #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=BIGINT},
            </if>
            #{tabType,jdbcType=TINYINT},
            #{articleSeq,jdbcType=INTEGER}
        </trim>
    </insert>

    <update id="updateArticleManage" parameterType="com.wbgame.pojo.clean.ArticleManage">
        update gj_b.article_manage
        <set>
            <if test="prjId != null and prjId != ''">
                prj_id = #{prjId,jdbcType=VARCHAR},
            </if>
            <if test="appId != null and appId != ''">
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="backUrl != null and backUrl != ''">
                back_url = #{backUrl,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != ''">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="areaType != null">
                area_type = #{areaType,jdbcType=TINYINT},
            </if>
            <if test="vipFlag != null">
                vip_flag = #{vipFlag,jdbcType=TINYINT},
            </if>

            <if test="articleType != null">
                article_type = #{articleType,jdbcType=TINYINT},
            </if>

            <if test="creator != null and creator != ''">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="modifier != null and modifier != ''">
                modifier = #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
            article_seq = #{articleSeq,jdbcType=INTEGER},
            tab_type = #{tabType,jdbcType=TINYINT}
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteArticleManageById" parameterType="java.lang.Long">
        delete
        from gj_b.article_manage
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>