<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.CleanSomeMapper" >
  
 	<delete id="deleteApiAdConfig" parameterType="Map">
		delete from AD_CONFIGURE where PRJMID = #{prijmid} and AD_SID = #{ad_sid}
	</delete>

	<update id="openApiAdConfig" parameterType="Map">
		update AD_CONFIGURE set statu = #{statu} where PRJMID = #{prijmid} and AD_SID = #{ad_sid}
	</update>

	<select id="selectNewAdConfig" parameterType="Map" resultType="com.wbgame.pojo.clean.ExtendVo">
		select agent,appid,appkey,type,code,limitname,refreshinterval,ad_sid,textinfo,adsize,
		DATE_FORMAT(creatdate,'%Y-%m-%d %H:%i:%s') creatdate from EXTEND_ADINFO t  where 1=1
		<if test="ad_sid != null and ad_sid != ''">
			and ad_sid like concat('%',#{ad_sid},'%')
		</if>
		<if test="code != null and code != ''">
			and code like concat('%',#{code},'%')
		</if>
		<if test="app_id != null and app_id != ''">
			and appid like concat('%',#{app_id},'%')
		</if>
		<if test="tj_agent != null and tj_agent != ''">
			and lower(agent) like concat('%',#{tj_agent},'%')
		</if>
	</select>

	<update id="updateNewAdConfig" parameterType="com.wbgame.pojo.clean.ExtendVo">
		update EXTEND_ADINFO set code=#{code},appid=#{appid},textinfo=#{textinfo},
		refreshinterval=#{refreshinterval},limitname=#{limitname},adsize=#{adsize},creatdate=now(),appkey=#{appkey}
		where ad_sid=#{ad_sid}
	</update>

	<delete id="deleteNewAdConfig" parameterType="String">
		delete from EXTEND_ADINFO where ad_sid in (#{ids})
	</delete>
	
	<select id="selectSDKConfigInfo" parameterType="com.wbgame.pojo.clean.SDKConfigInfo" resultType="com.wbgame.pojo.clean.SDKConfigInfo">
    select 
     id, sdk_txt AS sdkTxt , sdk_val AS sdkVal, sdk_status AS sdkStatus
    from sdk_config
    where 1=1 
    <if test="id != null and id != ''" >
        and id = #{id,jdbcType=VARCHAR}
    </if>
    <if test="sdkTxt != null and sdkTxt != ''" >
        and sdk_txt  like concat('%',#{sdkTxt},'%')
     </if>
     <if test="sdkVal != null and sdkVal != ''" >
        and sdk_val  like concat('%',#{sdkVal},'%')
    </if>
    <if test="sdkStatus != null and sdkStatus != ''" >
        and sdk_status = #{sdkStatus,jdbcType=INTEGER}
     </if>
  </select>
  
  <select id="selectNewAdConfigBySid" parameterType="List" resultType="com.wbgame.pojo.clean.ExtendVo">
		select agent,appid,appkey,type,code,limitname,refreshinterval,ad_sid,textinfo,adsize,
		DATE_FORMAT(creatdate,'%Y-%m-%d %H:%i:%s') creatdate from EXTEND_ADINFO t  where 1=1 and ad_sid in (${str})
	</select>

	<insert id="insertNewAdConfig" parameterType="com.wbgame.pojo.clean.ExtendVo">
		insert into EXTEND_ADINFO(agent,appid,appkey,type,code,limitname,refreshinterval,ad_sid,textinfo,adsize,creatdate)
		values(#{agent},#{appid},#{appkey},#{type},#{code},#{limitname},#{refreshinterval},#{ad_sid},#{textinfo},#{adsize},now())
	</insert>

	<insert id="batchInsertNewAdConfig" parameterType="java.util.List">
		insert into EXTEND_ADINFO(agent,appid,appkey,type,code,limitname,refreshinterval,ad_sid,textinfo,adsize,creatdate)values
		<foreach collection="list"  index="index" item="item" separator=",">
		(#{item.agent},#{item.appid},#{item.appkey},#{item.type},#{item.code},#{item.limitname},#{item.refreshinterval},#{item.ad_sid},
			#{item.textinfo},#{item.adsize},now())
		</foreach>
	</insert>

	<select id="selectNewAdOpenConfigForSql" parameterType="String" resultType="com.wbgame.pojo.clean.push.ConfigVo">
		${sql}
	</select>

	<select id="selectNewAdOpenConfig" parameterType="Map" resultType="com.wbgame.pojo.clean.push.ConfigVo">
		select t.limit_num,t.seqid, t.prjmid,t.name,t.type,t.rate,t.ad_sid_str,t.agentpecent,t.round,t.statu,
		t.delaytime,t.activ_cityid,t.activ_telecom,t.activ_statu,DATE_FORMAT(t.createdate,'%Y-%m-%d %H:%i:%s') createdate,
		t.delaydays as limit_date,t.delaysecond as limit_second,t.showmodel
		from EXTEND_ADCONFIG t where 1=1
		<if test="ad_sid != null and ad_sid != ''">
			and t.ad_sid_str like concat('%',#{ad_sid},'%')
		</if>
		<if test="prjid != null and prjid != ''">
			and t.prjmid like concat('%',#{prjid},'%')
		</if>
		<if test="status != null and status != ''">
			and t.statu = #{status}
		</if>
	</select>

	<insert id="insertNewAdOpenConfig" parameterType="com.wbgame.pojo.clean.push.ConfigVo">
		insert into EXTEND_ADCONFIG(prjmid,name,type,rate,limit_num,ad_sid_str,agentpecent,round,statu,
		delaytime,activ_cityid,activ_telecom,activ_statu,createdate,delaydays,delaysecond,showmodel) values
		(#{prjmid},#{name},#{type},#{rate},#{limit_num},#{ad_sid_str},#{agentpecent},#{round},#{statu},
		#{delaytime},#{activ_cityid},#{activ_telecom},#{activ_statu},now(),#{limit_date},#{limit_second},#{showmodel})
	</insert>

	<insert id="batchInsertNewAdOpenConfig" parameterType="java.util.List">
		insert into EXTEND_ADCONFIG(prjmid,name,type,rate,limit_num,ad_sid_str,agentpecent,round,statu,
		delaytime,activ_cityid,activ_telecom,activ_statu,createdate,delaydays,delaysecond,showmodel) values
		<foreach collection="list"  index="index" item="item" separator=",">
			(#{item.prjmid},#{item.name},#{item.type},#{item.rate},#{item.limit_num},#{item.ad_sid_str},#{item.agentpecent},
			#{item.round},#{item.statu},
			#{item.delaytime},#{item.activ_cityid},#{item.activ_telecom},#{item.activ_statu},now(),#{item.limit_date},
			#{item.limit_second},#{item.showmodel})
		</foreach>
	</insert>

	<update id="updateNewAdOpenConfig" parameterType="com.wbgame.pojo.clean.push.ConfigVo">
		update EXTEND_ADCONFIG set prjmid=#{prjmid},type=#{type},ad_sid_str=#{ad_sid_str},name=#{name},
		rate=#{rate},limit_num=#{limit_num},agentpecent=#{agentpecent},statu=#{statu},round=#{round},
		delaytime=#{delaytime},activ_telecom=#{activ_telecom},activ_cityid=#{activ_cityid},
		activ_statu=#{activ_statu},delaydays=#{limit_date},delaysecond=#{limit_second},
		showmodel=#{showmodel} where seqid=#{seqid}
	</update>

	<update id="batchupdateNewAdOpenConfig" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" open="" close="" separator=";">
			update EXTEND_ADCONFIG
			<set>
				prjmid=#{item.prjmid},type=#{item.type},ad_sid_str=#{item.ad_sid_str},name=#{item.name},
				rate=#{item.rate},limit_num=#{item.limit_num},agentpecent=#{item.agentpecent},statu=#{item.statu},round=#{item.round},
				delaytime=#{item.delaytime},activ_telecom=#{item.activ_telecom},
				<if test="item.activ_cityid != null and item.activ_cityid != ''">
					activ_cityid=#{item.activ_cityid},
				</if>
				<if test="item.showmodel != null and item.showmodel != ''">
					showmodel=#{item.showmodel},
				</if>
				
				activ_statu=#{item.activ_statu},delaydays=#{item.limit_date},delaysecond=#{item.limit_second}
			</set>
			where seqid=#{item.seqid}
		</foreach>
	</update>

	<delete id="deleteNewAdOpenConfig" parameterType="Map">
		delete from EXTEND_ADCONFIG where SEQID in (${seqids})
	</delete>

	<update id="openNewAdOpenConfig" parameterType="Map">
		update EXTEND_ADCONFIG set statu = #{statu} where seqid in (${ids})
	</update>

	 <select id="selectNewAdOpenConfigByPrj" parameterType="String" resultType="com.wbgame.pojo.clean.push.ConfigVo">
		 select limit_num,seqid,prjmid,name,type,rate,ad_sid_str,agentpecent,round,statu,delaytime,
		 activ_cityid,activ_telecom,activ_statu,DATE_FORMAT(createdate,'%Y-%m-%d %H:%i:%s') createdate,
		 delaydays as limit_date,delaysecond as limit_second,showmodel
		 from EXTEND_ADCONFIG t where 1=1 and prjmid=#{id}
	 </select>
	 
	 <select id="selectNewAdNameOpenConfig" parameterType="String" resultType="com.wbgame.pojo.clean.push.ConfigVo">
		 select limit_num,seqid,prjmid,name,type,rate,ad_sid_str,agentpecent,round,statu,delaytime,
		 activ_cityid,activ_telecom,activ_statu,DATE_FORMAT(createdate,'%Y-%m-%d %H:%i:%s') createdate,
		 delaydays as limit_date,delaysecond as limit_second,showmodel
		 from EXTEND_ADCONFIG t where 1=1 and seqid in (${seqids})
	 </select>

	<select id="selectNewAdOpenConfigInfoById" parameterType="List" resultType="com.wbgame.pojo.clean.push.ConfigVo">
		select limit_num,seqid,prjmid,name,type,rate,ad_sid_str,agentpecent,round,statu,delaytime,
		activ_cityid,activ_telecom,activ_statu,DATE_FORMAT(createdate,'%Y-%m-%d %H:%i:%s') createdate,
		delaydays as limit_date,delaysecond as limit_second,showmodel
		from EXTEND_ADCONFIG t where seqid in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="selectNewAdOpenConfigInfo" parameterType="Map" resultType="com.wbgame.pojo.clean.push.ConfigVo">
		select limit_num,seqid,prjmid,name,type,rate,ad_sid_str,agentpecent,round,statu,delaytime,
		activ_cityid,activ_telecom,activ_statu,DATE_FORMAT(createdate,'%Y-%m-%d %H:%i:%s') createdate,
		delaydays as limit_date,delaysecond as limit_second,showmodel
		from EXTEND_ADCONFIG t where 1=1
		<if test="opid != null and opid != ''">
			and prjmid = #{opid}
		</if>
		<if test="ad_sid != null and ad_sid != ''">
			and ad_sid_str = #{ad_sid}
		</if>
		<if test="ad_name != null and ad_name != ''">
			and name = #{ad_name}
		</if>
	</select>
</mapper>