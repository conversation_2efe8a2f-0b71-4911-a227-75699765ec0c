<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.CleanConfigControlMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.CleanConfigControlVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="cha" property="cha" jdbcType="VARCHAR"/>
        <result column="prjid" property="prjid" jdbcType="VARCHAR"/>
        <result column="audit" property="audit" jdbcType="VARCHAR"/>
        <result column="createTime" property="createTime" jdbcType="VARCHAR"/>
        <result column="modifyTime" property="modifyTime" jdbcType="VARCHAR"/>
        <result column="modifyUser" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
    </resultMap>

    <delete id="deleteCleanConfig" parameterType="java.lang.Integer">
        delete
        from clean_config_control
        where  id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertCleanConfig" parameterType="com.wbgame.pojo.clean.CleanConfigControl"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into clean_config_control
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="appid != null and appid != ''">
                appid,
            </if>

            <choose>
                <when test="cha != null and cha != ''">
                    cha,
                </when>
                <when test="prjid != null and prjid != ''">
                    prjid,
                </when>
            </choose>

            <if test="audit != null and audit != ''">
                audit,
            </if>

            <if test="modifyUser != null and modifyUser != ''">
                modifyUser,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
            createTime,
            modifyTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="appid != null and appid != ''">
                #{appid,jdbcType=VARCHAR},
            </if>

            <choose>
                <when test="cha != null and cha != ''">
                    #{cha,jdbcType=VARCHAR},
                </when>
                <when test="prjid != null and prjid != ''">
                    #{prjid,jdbcType=VARCHAR},
                </when>
            </choose>

            <if test="audit != null and audit != ''">
                #{audit,jdbcType=VARCHAR},
            </if>
            
            <if test="modifyUser != null and modifyUser != ''">
                #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                #{status,jdbcType=VARCHAR},
            </if>
            current_timestamp,
            current_timestamp
        </trim>
    </insert>

    <update id="updateCleanConfig" parameterType="com.wbgame.pojo.clean.CleanConfigControl">
        update clean_config_control
        <set>
            
            <if test="audit != null and audit != ''">
                audit = #{audit,jdbcType=VARCHAR},
            </if>
            
            <if test="modifyUser != null and modifyUser != ''">
                modifyUser = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                status = #{status,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectAppIdOrChaExist" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.clean.CleanConfigControlDTO">


        select *
        from clean_config_control
        <where>

            <if test="appid != null and appid != ''">
                and appid = #{appid}
            </if>

            <choose>
                <when test="cha != null and cha != ''">
                    and cha = #{cha}
                </when>
                <when test="prjid != null and prjid != ''">
                    and prjid = #{prjid}
                </when>
            </choose>
        </where>

    </select>
    
    <select id="selectCleanConfigControl" parameterType="com.wbgame.pojo.clean.CleanConfigControlDTO" resultMap="BaseResultMap">

        select
        c.*, a.app_name
        from clean_config_control c left join app_info a on appid = a.id

        <where>

            <if test="prjid != null and prjid != ''">
                and prjid = #{prjid}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="audit != null and audit != ''">
                and audit = #{audit}
            </if>
            <if test="appidList != null and appidList.size > 0">

                and appid in
                <foreach collection="appidList" item="aid" open="(" separator="," close=")">
                    #{aid}
                </foreach>

            </if>
            <if test="chaList != null and chaList.size > 0">
                and cha in
                <foreach collection="chaList" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>

        </where>
    </select>
</mapper>