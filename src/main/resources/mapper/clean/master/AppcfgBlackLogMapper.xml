<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.AppcfgBlackLogMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.AppcfgBlackLog">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="lsn" property="lsn" jdbcType="VARCHAR"/>
        <result column="imei" property="imei" jdbcType="VARCHAR"/>
        <result column="andId" property="andId" jdbcType="VARCHAR"/>
        <result column="prjid" property="prjid" jdbcType="VARCHAR"/>
        <result column="createTime" property="createTime" jdbcType="VARCHAR"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="ip" property="ip" jdbcType="VARCHAR"/>
        <result column="total_interception" property="totalInterception" jdbcType="VARCHAR"/>
        <result column="intercepted_yesterday" property="interceptedYesterday" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , lsn, imei, andId, prjid, createTime, reason
    </sql>
    <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.AppcfgBlackLog">



        select ip,
                total_interception,
               sum(intercepted_yesterday) intercepted_yesterday from (

            SELECT
            ip,
            count( distinct lsn ) total_interception,
            0 intercepted_yesterday
            FROM
            appcfg_black_log
            WHERE
            reason = 3

            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and createTime between #{start_date} and #{end_date}
            </if>
            <if test="prjidList != null and prjidList.size > 0">
                AND prjid IN
                <foreach collection="prjidList" item="prj" open="(" separator="," close=")">
                    #{prj}
                </foreach>
            </if>

            <if test="ipList != null and ipList.size > 0">
                AND ip IN
                <foreach collection="ipList" item="i" open="(" separator="," close=")">
                    #{i}
                </foreach>
            </if>
            <if test="lsn != null and lsn != ''">
                and lsn = #{lsn}
            </if>
            <if test="imei != null and imei != ''">
                and imei = #{imei}
            </if>
            <if test="andId != null and andId != ''">
                and andId = #{andId}
            </if>
            GROUP BY  ip
            union all

            SELECT
            ip,
            0 total_interception,
            count( distinct lsn ) intercepted_yesterday
            FROM
            appcfg_black_log
            WHERE
            reason = 3

            and createTime like concat(DATE_ADD( CURRENT_DATE, INTERVAL -1 DAY ), '%')
            <if test="prjidList != null and prjidList.size > 0">
                AND prjid IN
                <foreach collection="prjidList" item="prj" open="(" separator="," close=")">
                    #{prj}
                </foreach>
            </if>

            <if test="ipList != null and ipList.size > 0">
                AND ip IN
                <foreach collection="ipList" item="i" open="(" separator="," close=")">
                    #{i}
                </foreach>
            </if>
            <if test="lsn != null and lsn != ''">
                and lsn = #{lsn}
            </if>
            <if test="imei != null and imei != ''">
                and imei = #{imei}
            </if>
            <if test="andId != null and andId != ''">
                and andId = #{andId}
            </if>
            GROUP BY  ip

                          ) a
            group by ip

            having ip is not null

            <if test="order_str != null and order_str != ''">
                order by ${order_str}
            </if>

    </select>

    <delete id="deleteLogByIpList" parameterType="java.lang.String">


        delete  from appcfg_black_log
        where ip in
        <foreach collection="list" item="i" open="(" separator="," close=")">
            #{i}
        </foreach>
    </delete>

</mapper>