<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.AppCoinsRedpackdrawWithdrawHistoryV6Mapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.AppCoinsRedpackdrawWithdrawHistoryV6VO">

        <result column="appid" property="appid" jdbcType="INTEGER"/>
        <result column="prjid" property="prjid" jdbcType="INTEGER"/>
        <result column="amount" property="amount" jdbcType="VARCHAR"/>
        <result column="createtime" property="createtime" jdbcType="TIMESTAMP"/>
        <result column="success" property="success" jdbcType="BIGINT"/>
        <result column="fail" property="fail" jdbcType="BIGINT"/>
        <result column="count" property="count" jdbcType="BIGINT"/>
    </resultMap>


    <select id="selectByCondition" parameterType="com.wbgame.pojo.clean.AppCoinsRedpackdrawWithdrawHistoryV6DTO"
            resultMap="BaseResultMap">


        SELECT
        <choose>
            <when test="group != null and group != ''">

                ${group},
            </when>
            <otherwise>
                createtime,
            </otherwise>
        </choose>
        sum(cast(amount as decimal(10, 2))) amount,
        count(*) count,
        count(if(result = "success", 1, null)) success,
        count(if(result = "fail", 1, null)) fail
        FROM (
                 SELECT DATE_FORMAT(createtime, "%Y-%m-%d") createtime,
                        appid,
                        prjid,
                        cha,
                        lsn,
                        imei,
                        openid,
                        amount,
                        orderId,
                        packg,
                        withdrawHistory,
                        result
                 FROM xyxtj.app_coins_redpackdraw_withdraw_history_v6

                 <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                     WHERE createtime BETWEEN #{start_date}
                     AND #{end_date}
                 </if>

                 ORDER BY createtime DESC
             ) t

        <where>

            <if test="appid != null and appid.size > 0">

                AND appid in
                <foreach collection="appid" item="apid" open="(" separator="," close=")">
                    #{apid}
                </foreach>
            </if>

            <if test="cha != null and cha.size > 0">

                AND cha in
                <foreach collection="cha" item="c" open="(" separator="," close=")">
                    #{c}
                </foreach>
            </if>
            <if test="prjid != null">

                AND prjid = #{prjid}

            </if>
        </where>
        <choose>

            <when test="group != null and group != ''">

                GROUP BY ${group}
            </when>
            <otherwise>
                GROUP BY createtime
            </otherwise>
        </choose>

        <choose>

            <when test="order_str != null and order_str != ''">

                ORDER BY ${order_str}
            </when>
            <otherwise>
                ORDER BY createtime
            </otherwise>
        </choose>

    </select>

</mapper>