<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.UserGroupConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.UserGroupConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="prj_id" property="prjId" jdbcType="VARCHAR"/>
        <result column="cha" property="cha" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
        <result column="modify_user" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="BIGINT"/>
        <result column="config_id" property="configId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="rate" property="rate" jdbcType="INTEGER"/>

        <collection property="userGroupDetailList" column="id" javaType="java.util.List"
                    ofType="com.wbgame.pojo.clean.UserGroupDetail" select="com.wbgame.mapper.clean.master.UserGroupDetailMapper.selectUserGroupDetailById"/>
    </resultMap>

    <sql id="Base_Column_List">
        c.id
        , app_id, prj_id, cha, country, c.status,
        c.create_user,
        from_unixtime( c.create_time / 1000, '%Y-%m-%d %H:%i:%s' ) create_time,
        c.modify_user,
        from_unixtime( c.modify_time / 1000, '%Y-%m-%d %H:%i:%s' ) modify_time
    </sql>
    <select id="selectUserGroupConfig" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.UserGroupConfig">
        select
        distinct
        <include refid="Base_Column_List"/>
        from gj_b.user_group_config c
        left join gj_b.user_group_detail d
        on c.id = d.config_id
        <where>

           <if test="status != null">
               and status = #{status}
           </if>

            <if test="countryList != null and countryList.size > 0">
                AND country IN
                <foreach collection="countryList" item="ct" open="(" separator="," close=")">
                    #{ct}
                </foreach>
            </if>

            <if test="prjidList != null and prjidList.size > 0">
                AND prj_id IN
                <foreach collection="prjidList" item="prjid" open="(" separator="," close=")">
                    #{prjid}
                </foreach>
            </if>

            <if test="channelList != null and channelList.size > 0">
                AND cha IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND app_id IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectUserGroupConfigById" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.UserGroupConfig">
        select

        <include refid="Base_Column_List"/>
        from gj_b.user_group_config c
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <delete id="deleteUserGroupConfig" parameterType="java.lang.Long">
        delete
        from gj_b.user_group_config
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </delete>

    <insert id="insertUserGroupConfig" parameterType="com.wbgame.pojo.clean.UserGroupConfig"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into gj_b.user_group_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="appId != null  and appId != ''">
                app_id,
            </if>
            <if test="prjId != null  and prjId != ''">
                prj_id,
            </if>
            <if test="cha != null  and cha != ''">
                cha,
            </if>
            <if test="country != null  and country != ''">
                country,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createUser != null  and createUser != ''">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>

            <if test="modifyTime != null">
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="appId != null and appId != ''">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="prjId != null and prjId != ''">
                #{prjId,jdbcType=VARCHAR},
            </if>
            <if test="cha != null and cha != ''">
                #{cha,jdbcType=VARCHAR},
            </if>
            <if test="country != null and country != ''">
                #{country,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>

            <if test="modifyTime != null">
                #{modifyTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateUserGroupConfig" parameterType="com.wbgame.pojo.clean.UserGroupConfig">
        update gj_b.user_group_config
        <set>

            <if test="country != null and country != ''">
                country = #{country,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>

            <if test="modifyUser != null and modifyUser != ''">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <select id="selectUserGroupConfigUniqueness" resultType="java.lang.Long" parameterType="com.wbgame.pojo.clean.UserGroupConfig">
        select
               id
        from gj_b.user_group_config

        <where>

            and app_id = #{appId} and country = #{country}
            <choose>


                <when test="cha != null and cha != ''">
                    and cha = #{cha} and prj_id = ''
                </when>

                <when test="prjId != null and prjId != ''">
                    and prj_id = #{prjId} and cha = ''
                </when>

            </choose>


        </where>
        limit 1
    </select>
</mapper>