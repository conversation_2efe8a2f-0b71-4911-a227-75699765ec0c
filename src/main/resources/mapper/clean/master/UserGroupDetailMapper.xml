<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.UserGroupDetailMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.UserGroupDetail">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="config_id" property="configId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="rate" property="rate" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
        <result column="modify_user" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , config_id, name, rate, create_user, create_time, modify_user, modify_time
    </sql>
    <select id="selectUserGroupDetail" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.UserGroupDetail">
        select

        id , config_id, name, rate
        from gj_b.user_group_detail
        where name in
        <foreach collection="list" item="data" open="(" separator="," close=")">
             #{data.name}
        </foreach>
    </select>

    <select id="selectUserGroupDetailById" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.UserGroupDetail">
        select

       <include refid="Base_Column_List"/>
        from gj_b.user_group_detail
        where config_id = #{configId}
    </select>

    <delete id="deleteUserGroupDetail" parameterType="java.lang.Long">
        delete
        from gj_b.user_group_detail
        where config_id in
        <foreach collection="list" item="cfgId" open="(" separator="," close=")">
            #{cfgId}
        </foreach>
    </delete>

    <insert id="insertUserGroupDetail" parameterType="com.wbgame.pojo.clean.UserGroupDetail">

        <foreach collection="list" item="data" separator=";" >
            insert into gj_b.user_group_detail
            <trim prefix="(" suffix=")" suffixOverrides=",">

                <if test="data.configId != null">
                    config_id,
                </if>
                <if test="data.name != null and data.name != ''">
                    name,
                </if>
                <if test="data.rate != null">
                    rate,
                </if>
                <if test="data.createUser != null and data.createUser != ''">
                    create_user,
                </if>
                <if test="data.createTime != null">
                    create_time,
                </if>

                <if test="data.modifyTime != null">
                    modify_time,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">

                <if test="data.configId != null">
                    #{data.configId,jdbcType=BIGINT},
                </if>
                <if test="data.name != null and data.name != ''">
                    #{data.name,jdbcType=VARCHAR},
                </if>
                <if test="data.rate != null">
                    #{data.rate,jdbcType=INTEGER},
                </if>
                <if test="data.createUser != null and data.createUser != ''">
                    #{data.createUser,jdbcType=VARCHAR},
                </if>
                <if test="data.createTime != null">
                    #{data.createTime,jdbcType=BIGINT},
                </if>

                <if test="data.modifyTime != null">
                    #{data.modifyTime,jdbcType=BIGINT},
                </if>
            </trim>
        </foreach>

    </insert>


    <update id="updateUserGroupDetail" parameterType="com.wbgame.pojo.clean.UserGroupDetail">
        update gj_b.user_group_detail
        <set>
            <if test="configId != null">
                config_id = #{configId,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != ''">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="rate != null">
                rate = #{rate,jdbcType=INTEGER},
            </if>

            <if test="modifyUser != null and modifyUser != ''">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectUserGroupDetailCountry" resultMap="BaseResultMap" >
        select

        d.id, config_id, name, rate
        from gj_b.user_group_config c
        left join gj_b.user_group_detail d
        on c.id = d.config_id
        <where>

            app_id = #{appId} and country = #{country}

            <choose>

                <when test="prjId != null and prjId != '' and cha != null and cha != ''">
                    and prj_id = #{prjId} and cha = #{cha}
                </when>
                <when test="prjId != null and prjId != ''">
                    and prj_id = #{prjId} and cha = ''
                </when>
                <when test="cha != null and cha != ''">
                    and cha = #{cha} and prj_id = ''
                </when>

                <otherwise>
                    and prj_id is null and cha is null
                </otherwise>
            </choose>


        </where>

    </select>

</mapper>