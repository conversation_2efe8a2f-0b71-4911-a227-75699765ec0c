<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.StepAuditConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.StepAuditConfig">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="pid" property="pid" jdbcType="VARCHAR"/>
        <result column="cha" property="cha" jdbcType="VARCHAR"/>
        <result column="account" property="account" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="signDay" property="signDay" jdbcType="INTEGER"/>
        <result column="ecpm" property="ecpm" jdbcType="INTEGER"/>
        <result column="createUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="updateUser" property="updateUser" jdbcType="VARCHAR"/>
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updateTime" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , appid, pid, cha, account, status, signDay, ecpm, createUser, updateUser,amount,
        date_format(createTime, '%Y-%m-%d %H:%i:%s') createTime,
        date_format(updateTime, '%Y-%m-%d %H:%i:%s') updateTime
    </sql>
    <select id="selectStepAuditConfig" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.StepAuditConfig">
        select

        <include refid="Base_Column_List"/>
        from xyxtj.step_audit_config

        <where>

            <if test="account != null and account != ''">
                and account = #{account}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>

            <if test="channelList != null and channelList.size > 0">
                AND cha IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

            <if test="prjidList != null and prjidList.size > 0">
                AND pid IN
                <foreach collection="prjidList" item="prj" open="(" separator="," close=")">
                    #{prj}
                </foreach>
            </if>

        </where>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>

                order by id desc
            </otherwise>
        </choose>

    </select>

    <delete id="deleteStepAuditConfig" parameterType="java.lang.Integer">
        delete
        from xyxtj.step_audit_config
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertStepAuditConfig" parameterType="com.wbgame.pojo.clean.StepAuditConfig">
        insert into xyxtj.step_audit_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="appid != null and appid != ''">
                appid,
            </if>
            <if test="pid != null and pid != ''">
                pid,
            </if>
            <if test="cha != null and cha != ''">
                cha,
            </if>
            <if test="account != null and account != ''">
                account,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
            <if test="signDay != null">
                signDay,
            </if>
            <if test="ecpm != null">
                ecpm,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="createUser != null and createUser != ''">
                createUser,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="appid != null and appid != ''">
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="pid != null and pid != ''">
                #{pid,jdbcType=VARCHAR},
            </if>
            <if test="cha != null and cha != ''">
                #{cha,jdbcType=VARCHAR},
            </if>
            <if test="account != null and account != ''">
                #{account,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="signDay != null">
                #{signDay,jdbcType=INTEGER},
            </if>
            <if test="ecpm != null">
                #{ecpm,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>

        </trim>
    </insert>

    <update id="updateStepAuditConfig" parameterType="com.wbgame.pojo.clean.StepAuditConfig">
        update xyxtj.step_audit_config
        <set>

            <if test="status != null and status != ''">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="signDay != null">
                signDay = #{signDay,jdbcType=INTEGER},
            </if>
            <if test="ecpm != null">
                ecpm = #{ecpm,jdbcType=INTEGER},
            </if>

            <if test="amount != null">
                amount = #{amount},
            </if>

            <if test="updateUser != null and updateUser != ''">
                updateUser = #{updateUser,jdbcType=VARCHAR},
            </if>

            updateTime = now()

        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

</mapper>