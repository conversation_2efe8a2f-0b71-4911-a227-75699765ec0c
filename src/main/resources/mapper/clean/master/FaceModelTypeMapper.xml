<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.FaceModelTypeMapper" >
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.FaceModelTypeVO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="modelType" property="modelType" jdbcType="VARCHAR" />

  </resultMap>

  <sql id="Base_Column_List" >
    id, model_type, type_name
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from face_model_type
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteModelTypeById" parameterType="java.lang.Integer" >
    delete from face_model_type
    where id in
    <foreach collection="list" item="id" open="(" separator="," close=")">

        #{id}
    </foreach>
  </delete>


  <insert id="insertModelType" parameterType="com.wbgame.pojo.clean.FaceModelType" >
    insert into face_model_type
    <trim prefix="(" suffix=")" suffixOverrides="," >

      <if test="modelType != null" >
        modelType,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >

      <if test="modelType != null" >
        #{modelType,jdbcType=VARCHAR},
      </if>

    </trim>
  </insert>


  <update id="updateModelTypeById" parameterType="com.wbgame.pojo.clean.FaceModelType" >
    update face_model_type
    <set >
      <if test="modelType != null" >
        modelType = #{modelType,jdbcType=VARCHAR},
      </if>

    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.clean.FaceModelType" >
    update face_model_type
    set modelType = #{modelType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectAll" resultMap="BaseResultMap">

        select * from face_model_type order by id desc
  </select>
</mapper>