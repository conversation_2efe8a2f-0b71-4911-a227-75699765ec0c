<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.IdiomUserWithdrawMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.IdiomUserWithdrawVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="prj_id" property="prjId" jdbcType="VARCHAR"/>
        <result column="user_type" property="userType" jdbcType="TINYINT"/>
        <result column="arrival_status" property="arrivalStatus" jdbcType="TINYINT"/>
        <result column="andriod_id" property="andriodId" jdbcType="VARCHAR"/>
        <result column="open_id" property="openId" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="pkg" property="pkg" jdbcType="VARCHAR"/>
        <result column="timestamp" property="timestamp" jdbcType="VARCHAR"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>
        <result column="wx_name" property="wxName" jdbcType="VARCHAR"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
        <result column="lsn" property="lsn" jdbcType="VARCHAR"/>
        <result column="brand" property="brand" jdbcType="VARCHAR"/>
        <result column="withdraw_message" property="withdrawMessage" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="withdraw_remark" property="withdrawRemark" jdbcType="VARCHAR"/>
        <result column="withdraw_status" property="withdrawStatus" jdbcType="TINYINT"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
        <result column="modify_user" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        a.id,
        a.user_id,
        a.andriod_id,
        a.open_id,
        a.app_id,
        a.prj_id,
        a.pkg,
        a.`timestamp`,
        a.channel,
        a.version,
        a.lsn,
        a.brand,
        a.withdraw_message,
        a.amount,
        a.arrival_status,
        a.withdraw_remark,
        case a.withdraw_status when 1 then '审核中' when 2 then '已通过' else '不通过' end withdraw_status,
        a.create_user,
        from_unixtime( a.create_time / 1000, '%Y-%m-%d %H:%i:%s' ) create_time,
        a.modify_user,
        from_unixtime( a.modify_time / 1000, '%Y-%m-%d %H:%i:%s' ) modify_time
    </sql>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete
        from
        <choose>
            <when test="environment != 'test'">
                gj_b.idiom_user_withdraw
            </when>
            <otherwise>
                idiom_user_withdraw
            </otherwise>
        </choose>
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <insert id="insertIdiomUserWithdraw" parameterType="com.wbgame.pojo.clean.IdiomUserWithdraw">
        insert into
        <choose>
            <when test="environment != 'test'">
                gj_b.idiom_user_withdraw
            </when>
            <otherwise>
                idiom_user_withdraw
            </otherwise>
        </choose>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="record.id != null">
                id,
            </if>
            <if test="record.userId != null">
                user_id,
            </if>
            <if test="record.andriodId != null">
                andriod_id,
            </if>
            <if test="record.openId != null">
                open_id,
            </if>
            <if test="record.appId != null">
                app_id,
            </if>
            <if test="record.pkg != null">
                pkg,
            </if>
            <if test="record.timestamp != null">
                `timestamp`,
            </if>
            <if test="record.channel != null">
                channel,
            </if>
            <if test="record.version != null">
                version,
            </if>
            <if test="record.lsn != null">
                lsn,
            </if>
            <if test="record.brand != null">
                brand,
            </if>
            <if test="record.withdrawMessage != null">
                withdraw_message,
            </if>
            <if test="record.amount != null">
                amount,
            </if>
            <if test="record.withdrawRemark != null">
                withdraw_remark,
            </if>
            <if test="record.withdrawStatus != null">
                withdraw_status,
            </if>
            <if test="record.createUser != null">
                create_user,
            </if>
            <if test="record.createTime != null">
                create_time,
            </if>
            <if test="record.modifyUser != null">
                modify_user,
            </if>
            <if test="record.modifyTime != null">
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="record.id != null">
                #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.userId != null">
                #{record.userId,jdbcType=VARCHAR},
            </if>
            <if test="record.andriodId != null">
                #{record.andriodId,jdbcType=VARCHAR},
            </if>
            <if test="record.openId != null">
                #{record.openId,jdbcType=VARCHAR},
            </if>
            <if test="record.appId != null">
                #{record.appId,jdbcType=VARCHAR},
            </if>
            <if test="record.pkg != null">
                #{record.pkg,jdbcType=VARCHAR},
            </if>
            <if test="record.timestamp != null">
                #{record.timestamp,jdbcType=VARCHAR},
            </if>
            <if test="record.channel != null">
                #{record.channel,jdbcType=VARCHAR},
            </if>
            <if test="record.version != null">
                #{record.version,jdbcType=VARCHAR},
            </if>
            <if test="record.lsn != null">
                #{record.lsn,jdbcType=VARCHAR},
            </if>
            <if test="record.brand != null">
                #{record.brand,jdbcType=VARCHAR},
            </if>
            <if test="record.withdrawMessage != null">
                #{record.withdrawMessage,jdbcType=VARCHAR},
            </if>
            <if test="record.amount != null">
                #{record.amount,jdbcType=DECIMAL},
            </if>
            <if test="record.withdrawRemark != null">
                #{record.withdrawRemark,jdbcType=VARCHAR},
            </if>
            <if test="record.withdrawStatus != null">
                #{record.withdrawStatus,jdbcType=TINYINT},
            </if>
            <if test="record.createUser != null">
                #{record.createUser,jdbcType=VARCHAR},
            </if>
            <if test="record.createTime != null">
                #{record.createTime,jdbcType=BIGINT},
            </if>
            <if test="record.modifyUser != null">
                #{record.modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="record.modifyTime != null">
                #{record.modifyTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateIdiomUserWithdraw" parameterType="com.wbgame.pojo.clean.IdiomUserWithdraw">
        update
        <choose>
            <when test="environment != 'test'">
                gj_b.idiom_user_withdraw
            </when>
            <otherwise>
                idiom_user_withdraw
            </otherwise>
        </choose>
        <set>


            <if test="record.withdrawStatus != null">
                withdraw_status = #{record.withdrawStatus,jdbcType=TINYINT},
            </if>

            <if test="record.arrivalStatus != null and record.arrivalStatus != ''">
                arrival_status = #{record.arrivalStatus},
            </if>

            <if test="record.modifyUser != null">
                modify_user = #{record.modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="record.modifyTime != null">
                modify_time = #{record.modifyTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{record.id,jdbcType=BIGINT}
    </update>

    <select id="selectIdiomUserWithdraw" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.IdiomUserWithdrawDTO">

        select <include refid="Base_Column_List"/>,
            CASE
            b.user_type
            WHEN 0 THEN
            '正常'
            WHEN 1 THEN
            '黑名单'
            WHEN 2 THEN
            '白名单'
            WHEN 3 THEN
            '嫌疑'
            ELSE '正常'
            END user_type,
            b.wx_name
            FROM
                <choose>
                    <when test="environment != 'test'">
                        gj_b.idiom_user_withdraw a
                    </when>
                    <otherwise>
                        idiom_user_withdraw a
                    </otherwise>
                </choose>

               left join

                <choose>
                    <when test="environment != 'test'">
                        gj_b.idiom_user_info b
                    </when>
                    <otherwise>
                        idiom_user_info b
                    </otherwise>
                </choose>

               on  a.user_id = b.user_id
                   left join app_info ai on ai.id = a.app_id
        <where>

            <if test="dto.start_date != null and dto.start_date != '' and dto.end_date != null and dto.end_date != ''">
                a.create_time BETWEEN unix_timestamp(#{dto.start_date}) * 1000
                AND unix_timestamp(#{dto.end_date})  * 1000
            </if>
            <if test="dto.userId != null and dto.userId != ''">
                and a.user_id = #{dto.userId,jdbcType=VARCHAR}
            </if>

            <if test="dto.withdrawStatus != null">
                and a.withdraw_status = #{dto.withdrawStatus,jdbcType=TINYINT}
            </if>

            <if test="dto.userType != null and dto.userType != ''">
                and b.user_type = #{dto.userType}
            </if>

            <if test="dto.arrivalStatus != null and dto.arrivalStatus != ''">
                and a.arrival_status like #{dto.arrivalStatus} "%"
            </if>

            <if test="dto.prjId != null and dto.prjId != ''">
                and a.prj_id = #{dto.prjId}
            </if>

            <if test="dto.amount != null and dto.amount != ''">
                and a.amount = #{dto.amount}
            </if>
        </where>

        order by id desc
    </select>

    <select id="getUserIdById" resultType="java.lang.String" >

        select user_id userId
        from
        <choose>
            <when test="environment != 'test'">
                gj_b.idiom_user_withdraw
            </when>
            <otherwise>
                idiom_user_withdraw
            </otherwise>
        </choose>
        where id = #{id}
    </select>

</mapper>