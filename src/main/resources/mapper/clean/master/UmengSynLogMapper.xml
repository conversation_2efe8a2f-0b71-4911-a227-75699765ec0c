<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.clean.master.UmengSynLogMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.UmengSynLog">
    <!--@mbg.generated-->
    <!--@Table umeng_syn_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ds" jdbcType="VARCHAR" property="ds" />
    <result column="is_syn" jdbcType="INTEGER" property="isSyn" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
    <result column="infer" jdbcType="VARCHAR" property="infer" />
    <result column="time" jdbcType="VARCHAR" property="time" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, ds, is_syn, msg, infer, `time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from umeng_syn_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from umeng_syn_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.wbgame.pojo.clean.UmengSynLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into umeng_syn_log (create_time, update_time, ds, 
      is_syn, msg, infer, 
      `time`)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{ds,jdbcType=VARCHAR}, 
      #{isSyn,jdbcType=INTEGER}, #{msg,jdbcType=VARCHAR}, #{infer,jdbcType=VARCHAR}, 
      #{time,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.wbgame.pojo.clean.UmengSynLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into umeng_syn_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="ds != null">
        ds,
      </if>
      <if test="isSyn != null">
        is_syn,
      </if>
      <if test="msg != null">
        msg,
      </if>
      <if test="infer != null">
        infer,
      </if>
      <if test="time != null">
        `time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ds != null">
        #{ds,jdbcType=VARCHAR},
      </if>
      <if test="isSyn != null">
        #{isSyn,jdbcType=INTEGER},
      </if>
      <if test="msg != null">
        #{msg,jdbcType=VARCHAR},
      </if>
      <if test="infer != null">
        #{infer,jdbcType=VARCHAR},
      </if>
      <if test="time != null">
        #{time,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.clean.UmengSynLog">
    <!--@mbg.generated-->
    update umeng_syn_log
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ds != null">
        ds = #{ds,jdbcType=VARCHAR},
      </if>
      <if test="isSyn != null">
        is_syn = #{isSyn,jdbcType=INTEGER},
      </if>
      <if test="msg != null">
        msg = #{msg,jdbcType=VARCHAR},
      </if>
      <if test="infer != null">
        infer = #{infer,jdbcType=VARCHAR},
      </if>
      <if test="time != null">
        `time` = #{time,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.clean.UmengSynLog">
    <!--@mbg.generated-->
    update umeng_syn_log
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      ds = #{ds,jdbcType=VARCHAR},
      is_syn = #{isSyn,jdbcType=INTEGER},
      msg = #{msg,jdbcType=VARCHAR},
      infer = #{infer,jdbcType=VARCHAR},
      `time` = #{time,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchExecSql" parameterType="java.util.Map">
    ${sql1}
    <foreach collection="list" item="li" separator=",">
      ${sql2}
    </foreach>
    ${sql3}
  </insert>
</mapper>