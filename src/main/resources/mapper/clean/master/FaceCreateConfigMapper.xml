<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.FaceCreateConfigMapper" >
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.FaceCreateConfigVO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="promotesCha" property="promotesCha" jdbcType="VARCHAR" />
    <result column="createId" property="createId" jdbcType="VARCHAR" />
    <result column="modelId" property="modelId" jdbcType="VARCHAR" />
    <result column="createTime" property="createTime" jdbcType="TIMESTAMP" />
    <result column="modifyTime" property="modifyTime" jdbcType="TIMESTAMP" />
    <result column="createUser" property="createUser" jdbcType="VARCHAR" />
    <result column="modifyUser" property="modifyUser" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="tempTitle" property="tempTitle" jdbcType="VARCHAR" />

    <result column="modelType" property="modelType" jdbcType="INTEGER" />
    <result column="modelTypeName" property="modelTypeName" jdbcType="VARCHAR" />
    <result column="typeName" property="typeName" jdbcType="VARCHAR" />
    <result column="tempUrl" property="tempUrl" jdbcType="VARCHAR" />
    <result column="videoUrl" property="videoUrl" jdbcType="VARCHAR" />
  </resultMap>
  
  <sql id="Base_Column_List" >
    id, promotesCha, createId, modelId, createTime, modifyTime, createUser, modifyUser, 
    status
  </sql>
 
  <select id="selectFaceCreateConfigByCondition" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    c.*, m.tempTitle, m.tempUrl, m.videoUrl, mt.modelType modelTypeName
    from face_create_config c left join  face_model_config m on c.modelId = m.id
    left join face_model_type mt on c.modelType = mt.id

    <where>

      <if test="promotesCha != null and promotesCha != ''">
        and c.promotesCha = #{promotesCha}
      </if>

      <if test="createId != null and createId != ''">
        and c.createId like #{createId} "%"
      </if>

      <if test="modelId != null">
        and c.modelId = #{modelId}
      </if>

      <if test="status != null and status != ''">
        and c.status = #{status}
      </if>

      <if test="modelType != null">
        and c.modelType = #{modelType}
      </if>
    </where>

    order by c.id desc
  </select>
  <delete id="deleteFaceCreateConfigById" parameterType="java.lang.Integer" >
    delete from face_create_config
    where id in
    <foreach collection="list" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <insert id="insertFaceCreateConfig" parameterType="com.wbgame.pojo.clean.FaceCreateConfig" >
    insert into face_create_config
    <trim prefix="(" suffix=")" suffixOverrides="," >

      <if test="promotesCha != null" >
        promotesCha,
      </if>
      <if test="createId != null" >
        createId,
      </if>
      <if test="modelId != null" >
        modelId,
      </if>

      <if test="createUser != null" >
        createUser,
      </if>

      <if test="status != null" >
        status,
      </if>

      <if test="modelType != null" >
        modelType,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >

      <if test="promotesCha != null and promotesCha != ''" >
        #{promotesCha,jdbcType=VARCHAR},
      </if>
      <if test="createId != null and createId != ''" >
        #{createId,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null" >
        #{modelId},
      </if>

      <if test="createUser != null and createUser != ''" >
        #{createUser,jdbcType=VARCHAR},
      </if>

      <if test="status != null and status != ''" >
        #{status,jdbcType=VARCHAR},
      </if>

      <if test="modelType != null" >
        #{modelType}
      </if>
    </trim>
  </insert>

  <update id="updateFaceCreateConfigById" parameterType="com.wbgame.pojo.clean.FaceCreateConfig" >
    update face_create_config
    <set >
      <if test="promotesCha != null" >
        promotesCha = #{promotesCha,jdbcType=VARCHAR},
      </if>
      <if test="createId != null" >
        createId = #{createId,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null" >
        modelId = #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        createTime = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null" >
        modifyTime = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null" >
        createUser = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="modifyUser != null" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.clean.FaceCreateConfig" >
    update face_create_config
    set promotesCha = #{promotesCha,jdbcType=VARCHAR},
      createId = #{createId,jdbcType=VARCHAR},
      modelId = #{modelId,jdbcType=VARCHAR},
      createTime = #{createTime,jdbcType=TIMESTAMP},
      modifyTime = #{modifyTime,jdbcType=TIMESTAMP},
      createUser = #{createUser,jdbcType=VARCHAR},
      modifyUser = #{modifyUser,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>