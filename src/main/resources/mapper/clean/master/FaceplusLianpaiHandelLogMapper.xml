<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.FaceplusLianpaiHandelLogMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.FaceplusLianpaiHandelLog">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="model_name" property="modelName" jdbcType="VARCHAR"/>
        <result column="page_name" property="pageName" jdbcType="VARCHAR"/>
        <result column="update_records" property="updateRecords" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="region" property="region" jdbcType="TINYINT"/>

    </resultMap>

    <sql id="Base_Column_List">
        id,region
        , model_name, page_name, update_records, create_user, date_format(create_time, '%Y-%m-%d %H:%i:%s') create_time
    </sql>
    <select id="selectLog" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.FaceplusLianpaiHandelLog">
        select

        <include refid="Base_Column_List"/>
        from faceplus_lianpai_handel_log

        <where>
            <if test="modelName != null and modelName != ''">

                and model_name = #{modelName}
            </if>
            <if test="pageName != null and pageName != ''">

                and page_name = #{pageName}
            </if>
            <if test="updateRecords != null and updateRecords != ''">

                and update_records = #{updateRecords}
            </if>
            <if test="createUser != null and createUser != ''">

                and create_user = #{createUser}
            </if>

            <if test="region != null">

                and region = #{region}
            </if>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and tdate between #{start_date} and #{end_date}
            </if>

        </where>
        order by id desc
    </select>

    <insert id="insert" parameterType="com.wbgame.pojo.clean.FaceplusLianpaiHandelLog">
        insert into faceplus_lianpai_handel_log (id, model_name, page_name,
                                                 update_records, create_user,create_time,region)
        values (#{id,jdbcType=INTEGER}, #{modelName,jdbcType=VARCHAR}, #{pageName,jdbcType=VARCHAR},
                #{updateRecords,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR},now(),#{region})
    </insert>

</mapper>