<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.AdverConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.AdverConfig">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="adver_name" property="adverName" jdbcType="VARCHAR"/>
        <result column="adver_id" property="adverId" jdbcType="VARCHAR"/>
        <result column="expand" property="expand" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , adver_name, adver_id, expand, status, create_user, update_user,
        date_format(create_time, '%Y-%m-%d %H:%i:%s') create_time,
        date_format(update_time, '%Y-%m-%d %H:%i:%s') update_time

    </sql>
    <select id="selectAdverConfig" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.AdverConfig">
        select

        <include refid="Base_Column_List"/>
        from adver_config

        <where>

            <if test="adverName != null and adverName != ''">
                and adver_name = #{adverName}
            </if>
            <if test="adverId != null and adverId != ''">
                and adver_id = #{adverId}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>

            <if test="expand != null and expand != ''">
                and expand = #{expand}
            </if>
        </where>
        order by id desc
    </select>

    <delete id="deleteAdverConfig" parameterType="java.lang.Integer">
        delete
        from adver_config
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertAdverConfig" parameterType="com.wbgame.pojo.clean.AdverConfig">
        insert into adver_config (id, adver_name, adver_id,
                                  expand, status, create_user)
        values (#{id,jdbcType=INTEGER}, #{adverName,jdbcType=VARCHAR}, #{adverId,jdbcType=VARCHAR},
                #{expand,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{createUser,jdbcType=VARCHAR}
                )
    </insert>

    <update id="updateAdverConfig" parameterType="com.wbgame.pojo.clean.AdverConfig">
        update adver_config
        <set>

            <if test="adverId != null">
                adver_id = #{adverId,jdbcType=VARCHAR},
            </if>
            <if test="expand != null">
                expand = #{expand,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>

            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>


            update_time = now()

        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

</mapper>