<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.master.CleanAppOutsideFunctionMapper" >
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.SafeMarkConfigVO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="markId" property="markId" jdbcType="VARCHAR" />
        <result column="poisonType" property="poisonType" jdbcType="VARCHAR" />
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modifyTime" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="modifyUser" property="modifyUser" jdbcType="VARCHAR" />
        <result column="createUser" property="createUser" jdbcType="VARCHAR" />
        <result column="isPoison" property="isPoison" jdbcType="TINYINT" />
        <result column="description" property="description" jdbcType="VARCHAR" />
    </resultMap>

    <insert id="insertSafeMarkConfig" parameterType="com.wbgame.pojo.clean.SafeMarkConfig" >
        insert into safe_mark_config
        <trim prefix="(" suffix=")" suffixOverrides="," >

            <if test="markId != null" >
                markId,
            </if>
            <if test="poisonType != null" >
                poisonType,
            </if>

            <if test="createUser != null" >
                createUser,
            </if>
            <if test="description != null and description != ''" >
                description,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >

            <if test="markId != null and markId != ''" >
                #{markId},
            </if>
            <if test="poisonType != null and poisonType != ''" >
                #{poisonType},
            </if>

            <if test="createUser != null and createUser != ''" >
                #{createUser},
            </if>
            <if test="description != null and description != ''" >
                #{description},
            </if>
        </trim>
    </insert>

    <delete id="deleteById" parameterType="java.lang.String" >
        delete from safe_mark_config
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">

            #{id}
        </foreach>
    </delete>

    <select id="selectAll" parameterType="com.wbgame.pojo.clean.SafeMarkConfigQuery" resultMap="BaseResultMap">

        select *, IF(LEFT(poisonType,5) = "white",0,1) isPoison
        from safe_mark_config
        <where>
            <if test="markId != null and markId != ''">
                and markId like #{markId} "%"
            </if>
            <if test="poisonType != null and poisonType != ''">
                and poisonType = #{poisonType}
            </if>
            <if test="description != null and description != ''">
                and description like "%" #{description} "%"
            </if>
        </where>
        order by id desc
    </select>

    <insert id="batchImport" parameterType="com.wbgame.pojo.clean.SafeMarkConfig" >

        <foreach collection="list" item="data" separator=";">
            insert into safe_mark_config
            <trim prefix="(" suffix=")" suffixOverrides="," >

                <if test="data.markId != null" >
                    markId,
                </if>
                <if test="data.poisonType != null" >
                    poisonType,
                </if>

                <if test="data.createUser != null" >
                    createUser,
                </if>
                <if test="data.description != null and data.description != ''" >
                    `description`,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >

                <if test="data.markId != null and data.markId != ''" >
                    #{data.markId},
                </if>
                <if test="data.poisonType != null and data.poisonType != ''" >
                    #{data.poisonType},
                </if>

                <if test="data.createUser != null and data.createUser != ''" >
                    #{data.createUser},
                </if>

                <if test="data.description != null and data.description != ''" >
                    #{data.description},
                </if>
            </trim>
        </foreach>
    </insert>

    <delete id="deleteSafeMarkConfigByMarkId" parameterType="java.lang.String" >
        delete from safe_mark_config
        where markId in
        <foreach collection="list" item="markId" open="(" separator="," close=")">

            #{markId}
        </foreach>
    </delete>
</mapper>