<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.clean.slave.CleanWaiBaoSysMapper">

<!-- ``` 
	应用列表_查询 selectApp ID_200521181616
	应用列表_新增 insertApp ID_200521181823
	应用列表_修改 updataApp ID_200521181824
	应用列表_删除 deleteApp ID_200521181826
	角色列表_查询 selectRole ID_200519193709
	角色列表_新增 insertWbRole ID_200519193753
	角色列表_修改 updateWbRole ID_200519193755
	角色列表_删除 deleteWbRole ID_200519193756
	用户列表_删除 deleteUserList ID_200427142743
	用户列表_修改 upDataUserList ID_200427142745
	用户列表_新增 insertUserList ID_200427142746
	用户列表_查询 selectUserList ID_200427140858
	系统权限_删除 deleteWbOrg ID_200424164923
	系统权限_修改 upDataWbOrg ID_200424164922
	系统权限_新增 insertWbOrg ID_200424164927
	系统完整菜单_删除 deleteWbMenu ID_200423210903
	系统完整菜单_修改 updateWbMenu ID_200423205935
	系统完整菜单_新增 insertWbMenu ID_200423200312
	系统完整菜单_查询 selectMenu ID_200423143907
	系统权限菜单 selectOrgMenu ID_200420172358
	权限查询 selectOrg ID_200420151543
	用户查询验证 selectUser ID_200420140303
	自定义测试 selectTest ID_200418120746
-->

	<!-- 应用列表_查询 selectApp ID_200521181616 -->
	<select id="selectApp" parameterType="java.util.Map" resultType="com.wbgame.pojo.clean.WaibaoUserVo">
		SELECT `app_key`, `app_name` FROM clean_sys_app where 1=1
		<if test="app_key != null and app_key != ''">
			and app_key = #{app_key}
		</if>
		<if test="app_name != null and app_name != ''">
			and `app_name` LIKE '%${app_name}%'
		</if>
		ORDER BY `change_time` DESC
	</select>

	<!-- 应用列表_新增 insertApp ID_200521181823 -->
	<insert id="insertApp" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		INSERT INTO `clean_sys_app` (  
			`app_key`
			,`app_name`
			<!-- <if test="app_name != null">,`app_name`</if> -->
			,`change_time`
		)VALUES (
			<if test="app_key != null and app_key != ''">'${app_key}'</if>
			<if test="app_name != null and app_name != ''">,'${app_name}'</if>
			,REPLACE(unix_timestamp(current_timestamp(3)),'.','')<!-- 使用毫秒时间戳 -->
		);
	</insert>

	<!-- 应用列表_修改 updataApp ID_200521181824 -->
	<update id="updataApp" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		UPDATE `clean_sys_app` 
		<set >
			<if test="app_name != null" >
				app_name = #{app_name,jdbcType=VARCHAR},
			</if>
			<!-- <if test="role_app_slot != null" >
				role_app_slot = #{role_app_slot,jdbcType=VARCHAR},
			</if> -->
			change_time = REPLACE(unix_timestamp(current_timestamp(3)),'.',''),
		</set>
		WHERE (`app_key`='${app_key}') LIMIT 1
	</update>

	<!-- 应用列表_删除 deleteApp ID_200521181826 -->
	<delete id="deleteApp" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		DELETE FROM `clean_sys_app` WHERE (`app_key`='${app_key}')
	</delete>

	<!-- 角色列表_查询 selectRole ID_200519193709 -->
	<select id="selectRole" parameterType="java.util.Map" resultType="com.wbgame.pojo.clean.WaibaoUserVo">
		SELECT `role_id`, `role_name`, `role_app_slot` FROM clean_sys_role where 1=1
		<if test="role_id != null and role_id != ''">
			and role_id = #{role_id}
		</if>
		<if test="role_name != null and role_name != ''">
			and `role_name` LIKE concat('%',#{role_name},'%')
		</if>
		ORDER BY `change_time` DESC
	</select>

	<!-- 角色列表_新增 insertWbRole ID_200519193753 -->
	<insert id="insertWbRole" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		INSERT INTO `clean_sys_role` (  
			`role_id`
			<if test="role_name != null">,`role_name`</if>
			<if test="role_app_slot != null">,`role_app_slot`</if>
			,`change_time`
		)VALUES (
			<if test="role_id != null and role_id != ''">'${role_id}'</if>
			<if test="role_name != null and role_name != ''">,'${role_name}'</if>
			<if test="role_app_slot != null and role_app_slot != ''">,'${role_app_slot}'</if>
			,REPLACE(unix_timestamp(current_timestamp(3)),'.','')<!-- 使用毫秒时间戳 -->
		);
	</insert>

	<!-- 角色列表_修改 updateWbRole ID_200519193755 -->
	<update id="updateWbRole" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		UPDATE `clean_sys_role` 
		<set >
			<if test="role_name != null" >
				role_name = #{role_name,jdbcType=VARCHAR},
			</if>
			<if test="role_app_slot != null" >
				role_app_slot = #{role_app_slot,jdbcType=VARCHAR},
			</if>
			change_time = REPLACE(unix_timestamp(current_timestamp(3)),'.',''),
		</set>
		WHERE (`role_id`='${role_id}') LIMIT 1
	</update>

	<!-- 角色列表_删除 deleteWbRole ID_200519193756 -->
	<delete id="deleteWbRole" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		DELETE FROM `clean_sys_role` WHERE (`role_id`='${role_id}')
	</delete>

	<!-- 用户列表_删除 deleteUserList ID_200427142743 -->
	<delete id="deleteUserList" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		DELETE FROM `clean_sys_user` WHERE (`user_id`='${user_id}')
	</delete>
	
	<!-- 用户列表_修改 upDataUserList ID_200427142745 -->
	<update id="upDataUserList" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		UPDATE `clean_sys_user` 
		<set >
			<if test="password != null" >
				password = #{password,jdbcType=VARCHAR},
			</if>
			<if test="user_name != null" >
				user_name = #{user_name,jdbcType=VARCHAR},
			</if>
			<if test="org_id != null" >
				org_id = #{org_id,jdbcType=VARCHAR},
			</if>
			<if test="role_id != null" >
				role_id = #{role_id,jdbcType=VARCHAR},
			</if>
			<if test="sys != null" >
				sys = #{sys,jdbcType=VARCHAR},
			</if>
			<if test="company != null" >
				company = #{company,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE (`user_id`='${user_id}') LIMIT 1
	</update>
	
	<!-- 用户列表_新增 insertUserList ID_200427142746 -->
	<insert id="insertUserList" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		INSERT INTO `clean_sys_user` (  
			`user_id`
			,`password`
			,`user_name`
			<if test="org_id != null">,`org_id`</if>
			<if test="role_id != null">,`role_id`</if>
			<if test="sys != null and sys != ''">,`sys`</if>
		    <if test="company != null">,company</if>
			<!-- ,`orderTime` -->
		)VALUES (
			'${user_id}'
			,'${password}'
			,'${user_name}'
			<if test="org_id != null">,'${org_id}'</if>
			<if test="role_id != null">,'${role_id}'</if>
			<if test="sys != null and sys != ''">,'${sys}'</if>
		    <if test="company != null">,#{company,jdbcType=VARCHAR}</if>
			<!-- ,unix_timestamp() -->
		);
	</insert>

	<!-- 用户列表_查询 selectUserList ID_200427140858 -->
	<select id="selectUserList" parameterType="com.wbgame.pojo.clean.WaibaoUserVo" resultType="com.wbgame.pojo.clean.WaibaoUserVo">
		SELECT `key`,`user_id`, `password`, `user_name`, `org_id`, `role_id`, `sys`,company FROM clean_sys_user where 1=1
		<if test="user_id != null and user_id != ''">
			<!-- and user_id = #{user_id} -->
			and `user_id` LIKE '%${user_id}%'
		</if>
		<if test="password != null and password != ''">
			and password = #{password}
		</if>
		<if test="user_name != null and user_name != ''">
			and `user_name` LIKE '%${user_name}%'
		</if>
		<if test="org_id != null and org_id != ''">
			and `org_id` LIKE '%${org_id}%'
		</if>
		<if test="role_id != null and role_id != ''">
			and `role_id` LIKE '%${role_id}%'
		</if>
		<if test="sys != null and sys != ''">
			and `sys` = #{sys}
		</if>
		ORDER BY `key` DESC
	</select>

	<!-- 系统权限_删除 deleteWbOrg ID_200424164923 -->
	<delete id="deleteWbOrg" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		DELETE FROM `clean_sys_org` WHERE (`org_id`='${org_id}')
	</delete>
	
	<!-- 系统权限_修改 upDataWbOrg ID_200424164922 -->
	<update id="upDataWbOrg" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		UPDATE `clean_sys_org` SET 
		`org_id`='${org_id}'
		,`org_name`='${org_name}'
		,`hidden_menu_list`='${hidden_menu_list}'
		,`page_list`='${page_list}'
		,sys_type = #{sys_type}
		WHERE (`org_id`='${org_id}') LIMIT 1
	</update>
	
	<!-- 系统权限_新增 insertWbOrg ID_200424164927 -->
	<insert id="insertWbOrg" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		INSERT INTO `clean_sys_org` (  
			`org_id`
			<if test="org_name != null and org_name != ''">,`org_name`</if>
			<if test="hidden_menu_list != null and hidden_menu_list != ''">,`hidden_menu_list`</if>
			<if test="page_list != null and page_list != ''">,`page_list`</if>
		    <if test="sys_type != null and sys_type != ''">,sys_type</if>
			,`orderTime`
		)VALUES (
			'${org_id}'
			<if test="org_name != null and org_name != ''">,'${org_name}'</if>
			<if test="hidden_menu_list != null and hidden_menu_list != ''">,'${hidden_menu_list}'</if>
			<if test="page_list != null and page_list != ''">,'${page_list}'</if>
		    <if test="sys_type != null and sys_type != ''">,${sys_type}</if>
			,unix_timestamp()
		);
	</insert>

	<!-- 系统完整菜单_删除 deleteWbMenu ID_200423210903 -->
	<delete id="deleteWbMenu" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		DELETE FROM `clean_sys_menu` WHERE (`index`='${index}')
	</delete>

	<!-- 系统完整菜单_修改 updateWbMenu ID_200423205935 -->
	<update id="updateWbMenu" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		UPDATE `clean_sys_menu` SET 
		`index`='${index}'
		<if test="title != null and title != ''">,`title`='${title}'</if>
		<if test="style != null and style != ''">,`style`='${style}'</if>
		<if test="menu != null and menu != ''">,`menu`='${menu}'</if>
		<if test="off != null and off != ''">,`off`='${off}'</if>
		<if test="icon != null and icon != ''">,`icon`='${icon}'</if>
		<if test="slot != null and slot != ''">,`slot`='${slot}'</if>
		WHERE (`index`='${index}') LIMIT 1
	</update>

	<!-- 系统完整菜单_新增 insertWbMenu ID_200423200312 -->
	<insert id="insertWbMenu" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		INSERT INTO `clean_sys_menu` (  
			`index`
			<if test="title != null and title != ''">,`title`</if>
			<if test="style != null and style != ''">,`style`</if>
			<if test="menu != null and menu != ''">,`menu`</if>
			<if test="off != null and off != ''">,`off`</if>
			<if test="icon != null and icon != ''">,`icon`</if>
			<if test="slot != null and slot != ''">,`slot`</if>
		)VALUES (
			'${index}'
			<if test="title != null and title != ''">,'${title}'</if>
			<if test="style != null and style != ''">,'${style}'</if>
			<if test="menu != null and menu != ''">,'${menu}'</if>
			<if test="off != null and off != ''">,'${off}'</if>
			<if test="icon != null and icon != ''">,'${icon}'</if>
			<if test="slot != null and slot != ''">,'${slot}'</if>
		);
	</insert>
	
	<!-- 系统完整菜单_查询 selectMenu ID_200423143907 -->
	<select id="selectMenu" parameterType="com.wbgame.pojo.clean.WaibaoUserVo" resultType="com.wbgame.pojo.clean.WaibaoUserVo">
		SELECT * FROM clean_sys_menu where 1=1
		<if test="style != null and style != ''">
			and style = #{style}
		</if>
		<if test="index != null and index != ''">
			and `index` = #{index}
		</if>
		<if test="title != null and title != ''">
			and `title` LIKE '%${title}%'
		</if>
		ORDER BY `orderID` DESC
	</select>
	
	<!-- 系统权限菜单 ID_200420172358 -->
	<select id="selectOrgMenu" parameterType="java.util.Map" resultType="com.wbgame.pojo.clean.WaibaoUserVo">
		SELECT 
		`index`, `title`, `style`, `menu`, `off`, `icon`, `slot`
		FROM clean_sys_menu where `index` IN 
		<foreach item="page" collection="array" open="(" separator="," close=")">
			#{page}
		</foreach>
		or style IN ("class","menu")
		ORDER BY `orderID` DESC
	</select>
	
	<!-- 权限查询 ID_200420151543 -->
	<select id="selectOrg" parameterType="com.wbgame.pojo.clean.WaibaoUserVo" resultType="com.wbgame.pojo.clean.WaibaoUserVo">
		SELECT `org_id`, `org_name`, `hidden_menu_list`, `page_list`,sys_type FROM clean_sys_org where 1=1
		<if test="org_id != null and org_id != ''"> AND org_id = #{org_id}</if>
		<if test="org_name != null and org_name != ''"> AND org_name LIKE '%${org_name}%'</if>
		<if test="hidden_menu_list != null and hidden_menu_list != ''"> AND hidden_menu_list = #{hidden_menu_list}</if>
		<if test="page_list != null and page_list != ''"> AND page_list LIKE '%${page_list}%'</if>
		ORDER BY `orderTime` DESC
	</select>
	
	<!-- 用户查询验证 ID_200420140303 -->
	<select id="selectUser" parameterType="com.wbgame.pojo.clean.WaibaoUserVo" resultType="com.wbgame.pojo.clean.WaibaoUserVo">
		SELECT 
		user_id,
		user_name,
		org_id,
		password,
		sys,
		company
		FROM clean_sys_user where user_id = #{user_id} and password = #{password}
		ORDER BY `key` DESC
	</select>
	
	<!-- 自定义测试 ID_200418120746 -->
	<select id="selectTest" parameterType="com.wbgame.pojo.clean.WaibaoUserVo" resultType="com.wbgame.pojo.clean.WaibaoUserVo">
		select * from z_test where keysx = #{user}
	</select>
	
	<select id="selectWbSysUser" parameterType="java.util.Map" resultType="com.wbgame.pojo.clean.WaibaoUserVo">
		select * from waibao_sys_user 
		where login_name = #{login_name} and password = #{password}
	</select>
	
	<select id="selectWbSysUserInfoList" parameterType="com.wbgame.pojo.clean.WaibaoUserVo" 
					resultType="com.wbgame.pojo.clean.WaibaoUserVo">
					
		select * from waibao_sys_user where 1=1
		<if test="login_name != null and login_name != ''">
			and login_name = #{login_name}
		</if>
		<if test="nick_name != null and nick_name != ''">
			and nick_name like concat('%',#{nick_name},'%')
		</if>
	</select>
	<select id="selectWbSysRoleInfoList" parameterType="com.wbgame.pojo.clean.WaibaoUserVo" 
					resultType="com.wbgame.pojo.clean.WaibaoUserVo">
					
		select * from waibao_sys_role where 1=1
		<if test="role_name != null and role_name != ''">
			and role_name like '%${role_name}%'
		</if>
	</select>

	<insert id="insertWbSysUser" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		insert into waibao_sys_user(
				login_name,
				password,
				nick_name,
				role_id,
				role_name,
				app_group,
				email,
				phone,
				status,
				date

		) values(
				#{login_name},
				#{password},
				#{nick_name},
				#{role_id},
				#{role_name},
				#{app_group},
				#{email},
				#{phone},
				#{status},
				now()
		)
	</insert>
	<update id="updateWbSysUser" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		update waibao_sys_user set 
			<if test="password != null and password != ''">
				password = #{password},
			</if>
			nick_name = #{nick_name},
			role_id = #{role_id},
			role_name = #{role_name},
			email = #{email},
			phone = #{phone},
			status = #{status}
		where login_name = #{login_name}
	</update>
	<delete id="deleteWbSysUser" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		delete from waibao_sys_user where login_name = #{login_name}
	</delete>
	
	
	<insert id="insertWbSysRole" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		insert into waibao_sys_role(
				role_name,
				app_group
		) values(
				#{role_name},
				#{app_group}
		)
	</insert>
	<update id="updateWbSysRole" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		update waibao_sys_role set 
			role_name = #{role_name},
			app_group = #{app_group}
		where role_id = #{role_id}
	</update>
	<delete id="deleteWbSysRole" parameterType="com.wbgame.pojo.clean.WaibaoUserVo">
		delete from waibao_sys_role where role_id = #{role_id}
	</delete>
    <select id="userIf" resultType="com.wbgame.pojo.SysTempVo">
		SELECT `user_id`,org_id FROM clean_sys_user WHERE user_id = #{user_id}
	</select>

	<!-- 用户权限验证 -->
	<select id="orgIf" parameterType="java.util.Map" resultType="com.wbgame.pojo.SysTempVo">
		SELECT `org_id`,`sys_type` FROM  clean_sys_org WHERE org_id = #{org_id}
	</select>

</mapper>