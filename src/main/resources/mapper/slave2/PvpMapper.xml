<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave2.PvpMapper">
    <!-- 对战提现汇总数据相关-->
    <select id="getPvpWithdrawList" resultType="com.wbgame.pojo.mobile.PvpWithdrawVo" parameterType="java.util.Map">
        select  DATE(ordertime) AS tdate,appid,`versionName` AS version,
        `prjid`,amount,ROUND(SUM(amount),2) AS txNum,
        COUNT(0) AS txTimes from pvp_withdraw_record where 1=1 AND
        DATE(ordertime)<![CDATA[>=]]> #{startTime} and DATE(ordertime)<![CDATA[<=]]> #{endTime}
        <if test="version != null and version != ''">
            and  `versionName` = #{version}
        </if>
        <if test="prjid != null and prjid != ''">
            and  `prjid` = #{prjid}
        </if>
        group by DATE(ordertime),appid,prjid,versionName,amount
        order by  DATE(ordertime) desc
    </select>


    <!-- 对战提现详情数据相关-->
    <select id="getPvpWithdrawDetailList" resultType="com.wbgame.pojo.mobile.PvpWithdrawDetailVo" parameterType="java.util.Map">
        select  DATE (a.ordertime) AS tdate,a.* from  pvp_withdraw_record a where  1=1
        and DATE(ordertime)<![CDATA[>=]]> #{startTime} and DATE(ordertime)<![CDATA[<=]]> #{endTime}
        <if test="prjid != null and prjid != ''">
            and  `prjid` = #{prjid}
        </if>
        <if test="userid != null and userid != ''">
            and  `userid` = #{userid}
        </if>
        order by ordertime desc
    </select>


</mapper>