<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.slave2.HbUserWithdrawExamineMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.HbUserWithdrawExamine">
        <!--@mbg.generated-->
        <!--@Table hb_user_withdraw_examine-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="appid" jdbcType="INTEGER" property="appid"/>
        <result column="pid" jdbcType="VARCHAR" property="pid"/>
        <result column="amount" jdbcType="VARCHAR" property="amount"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="userid" jdbcType="VARCHAR" property="userid"/>
        <result column="openid" jdbcType="VARCHAR" property="openid"/>
        <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
        <result column="head" jdbcType="LONGVARCHAR" property="head"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="red_ticket" jdbcType="INTEGER" property="redTicket"/>
        <result column="total" jdbcType="INTEGER" property="total"/>
        <result column="package_name" jdbcType="VARCHAR" property="packageName"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_owner" jdbcType="VARCHAR" property="updateOwner"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, appid, pid, amount, `type`, userid, openid, nick_name, head, create_time, red_ticket,
        total, package_name, ip, `status`, update_time, update_owner,ver
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from hb_user_withdraw_examine
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from hb_user_withdraw_examine
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.wbgame.pojo.HbUserWithdrawExamine"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into hb_user_withdraw_examine (appid, pid, amount,
        `type`, userid, openid,
        nick_name, head, create_time,
        red_ticket, total, package_name,
        ip, `status`, update_time,
        update_owner)
        values (#{appid,jdbcType=INTEGER}, #{pid,jdbcType=VARCHAR}, #{amount,jdbcType=VARCHAR},
        #{type,jdbcType=VARCHAR}, #{userid,jdbcType=VARCHAR}, #{openid,jdbcType=VARCHAR},
        #{nickName,jdbcType=VARCHAR}, #{head,jdbcType=LONGVARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{redTicket,jdbcType=INTEGER}, #{total,jdbcType=INTEGER}, #{packageName,jdbcType=VARCHAR},
        #{ip,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP},
        #{updateOwner,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.wbgame.pojo.HbUserWithdrawExamine"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into hb_user_withdraw_examine
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appid != null">
                appid,
            </if>
            <if test="pid != null">
                pid,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="userid != null">
                userid,
            </if>
            <if test="openid != null">
                openid,
            </if>
            <if test="nickName != null">
                nick_name,
            </if>
            <if test="head != null">
                head,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="redTicket != null">
                red_ticket,
            </if>
            <if test="total != null">
                total,
            </if>
            <if test="packageName != null">
                package_name,
            </if>
            <if test="ip != null">
                ip,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateOwner != null">
                update_owner,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appid != null">
                #{appid,jdbcType=INTEGER},
            </if>
            <if test="pid != null">
                #{pid,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="userid != null">
                #{userid,jdbcType=VARCHAR},
            </if>
            <if test="openid != null">
                #{openid,jdbcType=VARCHAR},
            </if>
            <if test="nickName != null">
                #{nickName,jdbcType=VARCHAR},
            </if>
            <if test="head != null">
                #{head,jdbcType=LONGVARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="redTicket != null">
                #{redTicket,jdbcType=INTEGER},
            </if>
            <if test="total != null">
                #{total,jdbcType=INTEGER},
            </if>
            <if test="packageName != null">
                #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="ip != null">
                #{ip,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOwner != null">
                #{updateOwner,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.HbUserWithdrawExamine">
        <!--@mbg.generated-->
        update hb_user_withdraw_examine
        <set>
            <if test="appid != null">
                appid = #{appid,jdbcType=INTEGER},
            </if>
            <if test="pid != null">
                pid = #{pid,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=VARCHAR},
            </if>
            <if test="userid != null">
                userid = #{userid,jdbcType=VARCHAR},
            </if>
            <if test="openid != null">
                openid = #{openid,jdbcType=VARCHAR},
            </if>
            <if test="nickName != null">
                nick_name = #{nickName,jdbcType=VARCHAR},
            </if>
            <if test="head != null">
                head = #{head,jdbcType=LONGVARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="redTicket != null">
                red_ticket = #{redTicket,jdbcType=INTEGER},
            </if>
            <if test="total != null">
                total = #{total,jdbcType=INTEGER},
            </if>
            <if test="packageName != null">
                package_name = #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="ip != null">
                ip = #{ip,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOwner != null">
                update_owner = #{updateOwner,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.HbUserWithdrawExamine">
        <!--@mbg.generated-->
        update hb_user_withdraw_examine
        set appid = #{appid,jdbcType=INTEGER},
        pid = #{pid,jdbcType=VARCHAR},
        amount = #{amount,jdbcType=VARCHAR},
        `type` = #{type,jdbcType=VARCHAR},
        userid = #{userid,jdbcType=VARCHAR},
        openid = #{openid,jdbcType=VARCHAR},
        nick_name = #{nickName,jdbcType=VARCHAR},
        head = #{head,jdbcType=LONGVARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        red_ticket = #{redTicket,jdbcType=INTEGER},
        total = #{total,jdbcType=INTEGER},
        package_name = #{packageName,jdbcType=VARCHAR},
        ip = #{ip,jdbcType=VARCHAR},
        `status` = #{status,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        update_owner = #{updateOwner,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!--auto generated by MybatisCodeHelper on 2021-06-08-->
    <select id="selectByAll" resultMap="BaseResultMap">
        select a.id, a.appid,a.channel,a.pid, amount, `type`, a.userid, a.openid, a.nick_name, a.create_time,
        ifnull(b.red_ticket,a.red_ticket) red_ticket,ifnull(b.total,a.total) total, a.ip, `status`,
        if(status = 0,'待审核',if(status =1,'审核通过','审核拒绝')) statusName, DATE_FORMAT(a.update_time,'%Y-%m-%d %H:%i:%s') updateStr, update_owner,c.app_name
        from hb_user_withdraw_examine a left join hb_redpack_user b on a.userid = b.userid left join app_info c on a.appid = c.id
        <where> ver = #{ver}
            <if test="appid != null">
                and a.appid=#{appid,jdbcType=INTEGER}
            </if>
            <if test="pid != null and pid != ''">
                and a.pid=#{pid,jdbcType=VARCHAR}
            </if>
            <if test="userid != null and userid != ''">
                and a.userid=#{userid,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and a.status=#{status,jdbcType=INTEGER}
            </if>
            <if test="channel != null and channel != ''">
                and a.channel=#{channel,jdbcType=INTEGER}
            </if>
            <if test="amount != null and amount != ''">
                and a.amount = #{amount,jdbcType=VARCHAR}
            </if>
            <if test="minTotal != null">
                and ifnull(b.total,a.total)  <![CDATA[>=]]>  #{minTotal}
            </if>
            <if test="maxTotal != null">
                and ifnull(b.total,a.total)  <![CDATA[<=]]>  #{maxTotal}
            </if>
            <if test="minRedTicket != null">
                and ifnull(b.red_ticket,a.red_ticket)  <![CDATA[>=]]>  #{minRedTicket}
            </if>
            <if test="maxRedTicket != null">
                and ifnull(b.red_ticket,a.red_ticket)  <![CDATA[<=]]>  #{maxRedTicket}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>