<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.slave2.HbMsgQueueConfigMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.HbMsgQueueConfig">
    <!--@mbg.generated-->
    <!--@Table hb_msg_queue_config-->
    <id column="pid" jdbcType="VARCHAR" property="pid" />
    <id column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="session_name" jdbcType="VARCHAR" property="sessionName" />
    <result column="session_icon" jdbcType="VARCHAR" property="sessionIcon" />
    <result column="min_msg_time" jdbcType="INTEGER" property="minMsgTime" />
    <result column="max_msg_time" jdbcType="INTEGER" property="maxMsgTime" />
    <result column="msg_queue" jdbcType="LONGVARCHAR" property="msgQueue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_owner" jdbcType="VARCHAR" property="createOwner" />
    <result column="update_owner" jdbcType="VARCHAR" property="updateOwner" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    pid, session_id, session_name, session_icon, min_msg_time, max_msg_time, msg_queue, 
    create_time, update_time, create_owner, update_owner
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hb_msg_queue_config
    where pid = #{pid,jdbcType=VARCHAR}
      and session_id = #{sessionId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--@mbg.generated-->
    delete from hb_msg_queue_config
    where pid = #{pid,jdbcType=VARCHAR}
      and session_id = #{sessionId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.wbgame.pojo.HbMsgQueueConfig">
    <!--@mbg.generated-->
    insert into hb_msg_queue_config (pid, session_id, session_name, 
      session_icon, min_msg_time, max_msg_time, 
      msg_queue, create_time, update_time, 
      create_owner, update_owner)
    values (#{pid,jdbcType=VARCHAR}, #{sessionId,jdbcType=VARCHAR}, #{sessionName,jdbcType=VARCHAR}, 
      #{sessionIcon,jdbcType=VARCHAR}, #{minMsgTime,jdbcType=INTEGER}, #{maxMsgTime,jdbcType=INTEGER}, 
      #{msgQueue,jdbcType=LONGVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createOwner,jdbcType=VARCHAR}, #{updateOwner,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wbgame.pojo.HbMsgQueueConfig">
    <!--@mbg.generated-->
    insert into hb_msg_queue_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pid != null">
        pid,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="sessionName != null">
        session_name,
      </if>
      <if test="sessionIcon != null">
        session_icon,
      </if>
      <if test="minMsgTime != null">
        min_msg_time,
      </if>
      <if test="maxMsgTime != null">
        max_msg_time,
      </if>
      <if test="msgQueue != null">
        msg_queue,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createOwner != null">
        create_owner,
      </if>
      <if test="updateOwner != null">
        update_owner,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pid != null">
        #{pid,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="sessionName != null">
        #{sessionName,jdbcType=VARCHAR},
      </if>
      <if test="sessionIcon != null">
        #{sessionIcon,jdbcType=VARCHAR},
      </if>
      <if test="minMsgTime != null">
        #{minMsgTime,jdbcType=INTEGER},
      </if>
      <if test="maxMsgTime != null">
        #{maxMsgTime,jdbcType=INTEGER},
      </if>
      <if test="msgQueue != null">
        #{msgQueue,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwner != null">
        #{createOwner,jdbcType=VARCHAR},
      </if>
      <if test="updateOwner != null">
        #{updateOwner,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.HbMsgQueueConfig">
    <!--@mbg.generated-->
    update hb_msg_queue_config
    <set>
      <if test="sessionName != null">
        session_name = #{sessionName,jdbcType=VARCHAR},
      </if>
      <if test="sessionIcon != null">
        session_icon = #{sessionIcon,jdbcType=VARCHAR},
      </if>
      <if test="minMsgTime != null">
        min_msg_time = #{minMsgTime,jdbcType=INTEGER},
      </if>
      <if test="maxMsgTime != null">
        max_msg_time = #{maxMsgTime,jdbcType=INTEGER},
      </if>
      <if test="msgQueue != null">
        msg_queue = #{msgQueue,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwner != null">
        create_owner = #{createOwner,jdbcType=VARCHAR},
      </if>
      <if test="updateOwner != null">
        update_owner = #{updateOwner,jdbcType=VARCHAR},
      </if>
    </set>
    where pid = #{pid,jdbcType=VARCHAR}
      and session_id = #{sessionId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.HbMsgQueueConfig">
    <!--@mbg.generated-->
    update hb_msg_queue_config
    set session_name = #{sessionName,jdbcType=VARCHAR},
      session_icon = #{sessionIcon,jdbcType=VARCHAR},
      min_msg_time = #{minMsgTime,jdbcType=INTEGER},
      max_msg_time = #{maxMsgTime,jdbcType=INTEGER},
      msg_queue = #{msgQueue,jdbcType=LONGVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_owner = #{createOwner,jdbcType=VARCHAR},
      update_owner = #{updateOwner,jdbcType=VARCHAR}
    where pid = #{pid,jdbcType=VARCHAR}
      and session_id = #{sessionId,jdbcType=VARCHAR}
  </update>

  <select id="selectByAll" resultMap="BaseResultMap">
    select pid, session_id, session_name, session_icon, min_msg_time, max_msg_time, msg_queue,
    DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') as createStr, DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') as updateStr, create_owner, update_owner
    from hb_msg_queue_config
    where 1=1
    <if test="pid != null and pid != ''">
      and pid = #{pid,jdbcType=VARCHAR}
    </if>
    <if test="sessionId != null and sessionId != ''">
      and session_id = #{sessionId,jdbcType=VARCHAR}
    </if>
  </select>
  <delete id="deleteAllMsgTypes">
    delete from hb_msg_type_config where pid = #{pid} and session_id = #{sessionId}
  </delete>

  <select id="selectAllMsgs" resultType="com.wbgame.pojo.mobile.HbMsgTypeConfig">
    select id,type,msg,session_id sessionId from hb_msg_type_config where 1=1
    <if test="pid != null and pid != ''">
      and pid = #{pid,jdbcType=VARCHAR}
    </if>
    <if test="sessionId != null and sessionId != ''">
      and session_id = #{sessionId,jdbcType=VARCHAR}
    </if>

  </select>

  <insert id="batchInsertMsgTypes">
    insert into hb_msg_type_config(sid,pid,session_id,type,msg,create_time,update_time,create_owner,update_owner) values
    <foreach collection="list" item="it" separator=",">
      (#{it.sid},#{it.pid},#{it.sessionId},#{it.type},#{it.msg},#{it.createTime},#{it.updateTime},#{it.createOwner},#{it.updateOwner})
    </foreach>
  </insert>

  <select id="selectMsgTypeByPrimaryKey" resultType="com.wbgame.pojo.mobile.HbMsgTypeConfig">
    select * from hb_msg_type_config where id = #{id}
  </select>

  <update id="updateMsgType">
    update hb_msg_type_config
    <set>
      <if test="type != null and type != ''">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="msg != null and msg != ''">
        msg = #{msg,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOwner != null">
        update_owner = #{updateOwner,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}

  </update>

  <delete id="batchDeleteMsgTypes">
    delete from hb_msg_type_config where id in
    <foreach collection="array" separator="," item="it" open="(" close=")">
      #{it}
    </foreach>
  </delete>

    <select id="selectByPid" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/>
      from hb_msg_queue_config where pid = #{oldpid}
    </select>

</mapper>