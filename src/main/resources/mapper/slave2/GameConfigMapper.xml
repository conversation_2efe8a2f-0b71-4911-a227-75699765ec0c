<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave2.GameConfigMapper" >

	<select id="getABTestConfigList" parameterType="com.wbgame.pojo.game.config.ABTestConfigVo" resultType="com.wbgame.pojo.game.config.ABTestConfigVo">
		select * from  game_abtest_config where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id})
		</if>
		<if test="ver != null and ver != ''">
			and ver = #{ver}
		</if>
		<if test="state != null and state != ''">
			and state = #{state}
		</if>
		<if test="action_name != null and action_name != ''">
			and action_name like '%${action_name}%'
		</if>
		<if test="create_user != null and create_user != ''">
			and create_user like '%${create_user}%'
		</if>
		<if test="stime != null and stime != ''">
			and DATE_FORMAT(stime,'%Y-%m-%d') <![CDATA[ >= ]]> #{stime}
		</if>
		<if test="etime != null and etime != ''">
			and DATE_FORMAT(etime,'%Y-%m-%d') <![CDATA[ <= ]]> #{etime}
		</if>
		order by create_time desc
	</select>

	<insert id="saveABTestConfig"  parameterType="com.wbgame.pojo.game.config.ABTestConfigVo"
			useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into game_abtest_config (appid,cha_id,ver,action_name,action_info,action_result,action_desc,state,stime,etime,create_time,create_user) values
		 (#{appid},#{cha_id},#{ver},#{action_name},#{action_info},#{action_result},#{action_desc},#{state},#{stime},#{etime},now(),#{create_user})

	</insert>

	<update id="updateABTestConfig" parameterType="com.wbgame.pojo.game.config.ABTestConfigVo" >
		update game_abtest_config set state = #{state}
		<if test="state =='-1' ">
			,state_end_time = now()
		</if>
		,modify_user = #{modify_user},modify_time = now()
		where id = #{id}
	</update>

	<delete id="delABTestConfig" parameterType="java.lang.String">
		delete from game_abtest_config where id in (${id})
	</delete>


	<select id="getQATypeConfigList" resultType="com.wbgame.pojo.game.config.QATypeConfigVo">
		select * from game_feedback_type_config where 1=1
		<if test="pqaId != null and pqaId != ''">
			and pqaId = #{pqaId}
		</if>
		<if test="pqaName != null and pqaName != ''">
			and pqaName like '%${pqaName}%'
		</if>
		<if test="state != null and state != ''">
			and state = #{state}
		</if>
		order by create_time desc
	</select>

	<insert id="saveQATypeConfig" parameterType="com.wbgame.pojo.game.config.QATypeConfigVo"
			useGeneratedKeys="true" keyProperty="pqaId" keyColumn="pqaId">
		insert into game_feedback_type_config (pqaName,pqaIcon,`index`,state,create_user,create_time)
		values (#{pqaName},#{pqaIcon},#{index},#{state},#{create_user},now())
	</insert>

	<update id="updateQATypeConfig" parameterType="com.wbgame.pojo.game.config.QATypeConfigVo">
		update game_feedback_type_config set pqaName = #{pqaName},pqaIcon = #{pqaIcon},`index` = #{index},state = #{state},
		modify_user = #{modify_user},modify_time = now() where pqaId = #{pqaId}
	</update>

	<delete id="delQATypeConfig" parameterType="java.lang.String">
		delete from game_feedback_type_config where pqaId in (${pqaId});
	</delete>


	<select id="getQAConfigList" resultType="com.wbgame.pojo.game.config.QAConfigVo">
		select * from  game_feedback_detail_config where 1=1
		<if test="pqaId != null and pqaId != ''">
			and pqaId in (${pqaId})
		</if>
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="qaName != null and qaName != ''">
			and qaName like '%${qaName}%'
		</if>
		<if test="qaInfo != null and qaInfo != ''">
			and qaInfo like '%${qaInfo}%'
		</if>
		<if test="state != null and state != ''">
			and state = #{state}
		</if>
		order by create_time desc
	</select>

	<insert id="saveQAConfig" parameterType="com.wbgame.pojo.game.config.QAConfigVo"
			useGeneratedKeys="true" keyProperty="qaId" keyColumn="qaId">
		insert into game_feedback_detail_config (appid,pqaId,qaName,qaUrl,qaInfo,relationIds,`index`,state,create_user,create_time)
		values (#{appid},#{pqaId},#{qaName},#{qaUrl},#{qaInfo},#{relationIds},#{index},#{state},#{create_user},now())
	</insert>

	<update id="updateQAConfig" parameterType="com.wbgame.pojo.game.config.QAConfigVo">
		update game_feedback_detail_config set appid = #{appid},pqaId = #{pqaId},state = #{state},
		qaName = #{qaName},qaUrl = #{qaUrl},qaInfo = #{qaInfo},relationIds = #{relationIds},
		`index` = #{index},modify_user = #{modify_user},modify_time = now() where qaId = #{qaId}
	</update>

	<delete id="delQAConfig" parameterType="java.lang.String">
		delete from game_feedback_detail_config where qaId in (${qaId});
	</delete>

	<select id="getNoticeConfigList" parameterType="com.wbgame.pojo.game.config.NoticeVo" resultType="com.wbgame.pojo.game.config.NoticeVo">
		select * from game_notice_config where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel})
		</if>
		<if test="noticeType != null and noticeType != ''">
			and noticeType = #{noticeType}
		</if>
		<if test="actionType != null and actionType != ''">
			and actionType = #{actionType}
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="ver != null and ver != ''">
			and ver = #{ver}
		</if>
		<if test="title != null and title != ''">
			and title like '%${title}%'
		</if>
		<if test="content != null and content != ''">
			and content like '%${content}%'
		</if>
		<if test="state != null and state != ''">
			and state = #{state}
		</if>
		<if test="start_date != null and start_date != ''">
			and DATE_FORMAT(create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{start_date}
		</if>
		<if test="end_date != null and end_date != ''">
			and DATE_FORMAT(create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{end_date}
		</if>
	</select>

	<insert id="insertNoticeConfig" parameterType="com.wbgame.pojo.game.config.NoticeVo">
		insert into game_notice_config (appid,channel,pid,ver,noticeType,noticeTypeName,
		title,content,popType,popTimes,actionType,imgs,start_date,end_date,state,`index`,create_user,create_time)
		values (#{appid},#{channel},#{pid},#{ver},#{noticeType},#{noticeTypeName},#{title},#{content},
		#{popType},#{popTimes},#{actionType},#{imgs},#{start_date},#{end_date},#{state},#{index},#{create_user},now())
	</insert>

	<update id="updateNoticeConfig" parameterType="com.wbgame.pojo.game.config.NoticeVo">
		update game_notice_config set appid = #{appid},channel = #{channel},
		pid = #{pid},ver = #{ver},noticeType = #{noticeType},noticeTypeName = #{noticeTypeName},
		title = #{title},content = #{content},popType = #{popType},popTimes = #{popTimes},
		actionType = #{actionType},imgs = #{imgs},start_date = #{start_date},end_date = #{end_date},
		state = #{state},`index` = #{index},modify_user = #{modify_user},modify_time = now()
		where noticeId = #{noticeId}
	</update>

	<delete id="deleteNoticeConfig" parameterType="com.wbgame.pojo.game.config.NoticeVo">
		delete from game_notice_config where noticeId in (${noticeId})
	</delete>

	<select id="getSubscribeHandleLogList" parameterType="com.wbgame.pojo.game.report.query.SubscribeSendLogQueryVo"
			resultType="com.wbgame.pojo.game.report.SubscribeSendLogVo">
		select * from subscribe_handle_log where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="template_id != null and template_id != ''">
			and template_id = #{template_id}
		</if>

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by create_time desc
			</otherwise>
		</choose>
	</select>

	<select id="getSubscribeHandleLogListSum" parameterType="com.wbgame.pojo.game.report.query.SubscribeSendLogQueryVo"
			resultType="com.wbgame.pojo.game.report.SubscribeSendLogVo">
		select sum(total) total,sum(success) success,
		sum(refuse) refuse,sum(unkown) unkown,sum(token) token
		from subscribe_handle_log where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="template_id != null and template_id != ''">
			and template_id = #{template_id}
		</if>

	</select>
</mapper>