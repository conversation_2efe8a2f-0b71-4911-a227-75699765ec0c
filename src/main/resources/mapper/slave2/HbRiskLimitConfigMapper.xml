<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.slave2.HbRiskLimitConfigMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.HbRiskLimitConfig">
    <!--@mbg.generated-->
    <!--@Table hb_risk_limit_config-->
    <id column="pid" jdbcType="VARCHAR" property="pid" />
    <result column="limit_num" jdbcType="VARCHAR" property="limitNum" />
    <result column="limit_money" jdbcType="VARCHAR" property="limitMoney" />
    <result column="limit_total" jdbcType="VARCHAR" property="limitTotal" />
    <result column="limit_ip" jdbcType="VARCHAR" property="limitIp" />
    <result column="limit_video" jdbcType="VARCHAR" property="limitVideo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_owner" jdbcType="VARCHAR" property="createOwner" />
    <result column="update_owner" jdbcType="VARCHAR" property="updateOwner" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    pid, limit_num, limit_money, limit_total, limit_ip, limit_video, create_time, update_time, 
    create_owner, update_owner
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hb_risk_limit_config
    where pid = #{pid,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from hb_risk_limit_config
    where pid = #{pid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.wbgame.pojo.HbRiskLimitConfig">
    <!--@mbg.generated-->
    insert into hb_risk_limit_config (pid, limit_num, limit_money, 
      limit_total, limit_ip, limit_video, 
      create_time, update_time, create_owner, 
      update_owner)
    values (#{pid,jdbcType=VARCHAR}, #{limitNum,jdbcType=VARCHAR}, #{limitMoney,jdbcType=VARCHAR}, 
      #{limitTotal,jdbcType=VARCHAR}, #{limitIp,jdbcType=VARCHAR}, #{limitVideo,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createOwner,jdbcType=VARCHAR}, 
      #{updateOwner,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wbgame.pojo.HbRiskLimitConfig">
    <!--@mbg.generated-->
    insert into hb_risk_limit_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pid != null">
        pid,
      </if>
      <if test="limitNum != null">
        limit_num,
      </if>
      <if test="limitMoney != null">
        limit_money,
      </if>
      <if test="limitTotal != null">
        limit_total,
      </if>
      <if test="limitIp != null">
        limit_ip,
      </if>
      <if test="limitVideo != null">
        limit_video,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createOwner != null">
        create_owner,
      </if>
      <if test="updateOwner != null">
        update_owner,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pid != null">
        #{pid,jdbcType=VARCHAR},
      </if>
      <if test="limitNum != null">
        #{limitNum,jdbcType=VARCHAR},
      </if>
      <if test="limitMoney != null">
        #{limitMoney,jdbcType=VARCHAR},
      </if>
      <if test="limitTotal != null">
        #{limitTotal,jdbcType=VARCHAR},
      </if>
      <if test="limitIp != null">
        #{limitIp,jdbcType=VARCHAR},
      </if>
      <if test="limitVideo != null">
        #{limitVideo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwner != null">
        #{createOwner,jdbcType=VARCHAR},
      </if>
      <if test="updateOwner != null">
        #{updateOwner,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.HbRiskLimitConfig">
    <!--@mbg.generated-->
    update hb_risk_limit_config
    <set>
      <if test="limitNum != null">
        limit_num = #{limitNum,jdbcType=VARCHAR},
      </if>
      <if test="limitMoney != null">
        limit_money = #{limitMoney,jdbcType=VARCHAR},
      </if>
      <if test="limitTotal != null">
        limit_total = #{limitTotal,jdbcType=VARCHAR},
      </if>
      <if test="limitIp != null">
        limit_ip = #{limitIp,jdbcType=VARCHAR},
      </if>
      <if test="limitVideo != null">
        limit_video = #{limitVideo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwner != null">
        create_owner = #{createOwner,jdbcType=VARCHAR},
      </if>
      <if test="updateOwner != null">
        update_owner = #{updateOwner,jdbcType=VARCHAR},
      </if>
    </set>
    where pid = #{pid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.HbRiskLimitConfig">
    <!--@mbg.generated-->
    update hb_risk_limit_config
    set limit_num = #{limitNum,jdbcType=VARCHAR},
      limit_money = #{limitMoney,jdbcType=VARCHAR},
      limit_total = #{limitTotal,jdbcType=VARCHAR},
      limit_ip = #{limitIp,jdbcType=VARCHAR},
      limit_video = #{limitVideo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_owner = #{createOwner,jdbcType=VARCHAR},
      update_owner = #{updateOwner,jdbcType=VARCHAR}
    where pid = #{pid,jdbcType=VARCHAR}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-05-26-->
  <select id="selectByAll" resultMap="BaseResultMap">
    select
    pid, limit_num, limit_money, limit_total, limit_ip, limit_video,
    DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createStr,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') updateStr,
    create_owner, update_owner
        from hb_risk_limit_config
        <where>
            <if test="pid != null and pid != ''">
                and pid=#{pid,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>