<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave2.NnjyMapper">

	 <select id="selectHomeLevelTotalByLog" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.nnjy.NnjyLevelTotalVo">
		select '${tdate}' tdate,aa.*,bb.*,cc.*,dd.*,ee.*,gg.*,hh.*,
			IFNULL(ii.continents_id,0) region 
		from 
			(SELECT appid,pid,lv,fromname,COUNT(DISTINCT `user`) joinNum,COUNT(`user`) joinCount,
				TRUNCATE(avg(gold),2) goldAvg
			FROM home_level_log${tdate}
			WHERE type = 1
			GROUP BY appid,pid,lv) aa
		left join
			(SELECT appid,pid,lv,COUNT(DISTINCT `user`) successNum,COUNT(`user`) successCount,
				TRUNCATE(avg(gametimes),0) successAvg,TRUNCATE(avg(nums),2) overAvg
			FROM home_level_log${tdate}
			WHERE type = 2
			GROUP BY appid,pid,lv) bb
		on aa.appid = bb.appid and aa.pid = bb.pid and aa.lv = bb.lv
		left join
			(SELECT appid,pid,lv,COUNT(`user`) failCount,TRUNCATE(avg(gametimes),0) failAvg
			FROM home_level_log${tdate}
			WHERE type = 3
			GROUP BY appid,pid,lv) cc
		on aa.appid = cc.appid and aa.pid = cc.pid and aa.lv = cc.lv
		left join	
			(SELECT appid,pid,lv,COUNT(`user`) outCount
			FROM home_level_log${tdate}
			WHERE type = 4
			GROUP BY appid,pid,lv) dd
		on aa.appid = dd.appid and aa.pid = dd.pid and aa.lv = dd.lv
		left join	
			(SELECT appid,pid,lv,COUNT(`user`) retryCount
			FROM home_level_log${tdate}
			WHERE type = 5
			GROUP BY appid,pid,lv) ee
		on aa.appid = ee.appid and aa.pid = ee.pid and aa.lv = ee.lv
		left join
			(SELECT appid,pid,lv,COUNT(`user`) toolCount
			FROM home_tool_log${tdate}
			WHERE type = 1
			GROUP BY appid,pid,lv) gg
		on aa.appid = gg.appid and aa.pid = gg.pid and aa.lv = gg.lv
		left join	
			(SELECT appid,pid,lv,COUNT(DISTINCT `user`) feeNum,TRUNCATE(SUM(fee),2) feeAmount
			FROM home_fee_log${tdate}
			GROUP BY appid,pid,lv) hh
		on aa.appid = hh.appid and aa.pid = hh.pid and aa.lv = hh.lv
		
		left join np_country ii on aa.fromname = ii.c_name
	</select> 
	
	<insert id="insertHomeLevelTotalList" parameterType="java.util.List">
		insert into home_level_total_info(
			tdate,
			appid,
			pid,
			lv,
			joinNum,
			joinCount,
			successNum,
			successAvg,
			failCount,
			failAvg,
			overAvg,
			outCount,
			retryCount,
			stop1Num,
			stop2Num,
			toolCount,
			feeNum,
			feeAmount,
			goldAvg,
			region,
			reviveTimes,
			passDays
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.appid},
			#{li.pid},
			#{li.lv},
			#{li.joinNum},
			#{li.joinCount},
			#{li.successNum},
			#{li.successAvg},
			#{li.failCount},
			#{li.failAvg},
			#{li.overAvg},
			#{li.outCount},
			#{li.retryCount},
			#{li.stop1Num},
			#{li.stop2Num},
			#{li.toolCount},
			#{li.feeNum},
			#{li.feeAmount},
			#{li.goldAvg},
			#{li.region},
			#{li.reviveTimes},
			#{li.passDays}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE
		joinNum = VALUES(joinNum)
	</insert>
	
	
	<select id="selectHomeLevelUseToolByLog" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.nnjy.NnjyLevelUseToolVo">
		<!-- 平均存量，消耗数量，购买数量  -->
		SELECT '${tdate}' tdate,xx.appid,xx.pid,xx.lv,xx.tool,
			yy.useNum,zz.buyNum,
			TRUNCATE(SUM(xx.toolNums)/COUNT(xx.user),2) saveAvg
		FROM (
			select appid,pid,lv,tool,`user`,toolNums,times from
			(select * from home_tool_log${tdate} order by times desc) aa
			group by appid,pid,lv,tool,`user`
		) xx
		
		LEFT JOIN
		(SELECT appid,pid,lv,tool,COUNT(1) useNum FROM home_tool_log${tdate}
		WHERE type = 1 GROUP BY appid,pid,lv,tool) yy
		
		ON xx.appid=yy.appid and xx.pid=yy.pid and xx.lv=yy.lv and xx.tool=yy.tool
		
		LEFT JOIN
		(SELECT appid,pid,lv,tool,COUNT(1) buyNum FROM home_tool_log${tdate}
		WHERE type = 2 GROUP BY appid,pid,lv,tool) zz
		
		ON xx.appid=zz.appid and xx.pid=zz.pid and xx.lv=zz.lv and xx.tool=zz.tool
		
		GROUP BY xx.appid,xx.pid,xx.lv,xx.tool
	</select>
	
	<insert id="insertHomeLevelUseToolList" parameterType="java.util.List">
		insert into home_level_usetool_info(
			tdate,
			appid,
			pid,
			lv,
			tool,
			successNum,
			saveAvg,
			useNum,
			buyNum

		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.appid},
			#{li.pid},
			#{li.lv},
			#{li.tool},
			#{li.successNum},
			#{li.saveAvg},
			#{li.useNum},
			#{li.buyNum})
		</foreach>
		ON DUPLICATE KEY UPDATE
		saveAvg = VALUES(saveAvg)
	</insert>
	
	<update id="updateHomeLevelSuccessByAttr" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			update ${tableName} 
			set ${sets}
			where tdate = #{li.tdate} and appid = #{li.appid} and pid = #{li.pid} and lv = #{li.lv}
		</foreach>
	</update>
	
	<insert id="insertHomeUserguideReportByBatch" parameterType="java.util.Map">
		insert into home_userguide_report(tdate,appid,pid,id,num,counts)

		SELECT '${tdate}' tdate,appid,pid,id,COUNT(DISTINCT `user`) num,COUNT(`user`) counts 
		FROM home_userguide_log${tdate}
		where id not in (2,57)
		group by appid,pid,id
		
		UNION ALL
		
		select '${tdate}' tdate,aa.*,IFNULL(bb.num,0) counts from
		(SELECT appid,pid,'2',COUNT(DISTINCT `user`) num
		FROM home_userguide_log${tdate} where id = 2 and param = 0
		group by appid,pid) aa
		left join
		(SELECT appid,pid,'2',COUNT(DISTINCT `user`) num
		FROM home_userguide_log${tdate} where id = 2 and param = 1
		group by appid,pid) bb
		on aa.appid = bb.appid and aa.pid = bb.pid
		
		UNION ALL
		
		SELECT '${tdate}' tdate,appid,pid,'57',COUNT(DISTINCT `user`) num,TRUNCATE(AVG(loadtimes),0) counts 
		FROM home_userguide_log${tdate}
		where id = 57
		group by appid,pid
		
		UNION ALL
		
		SELECT '${tdate}' tdate,appid,pid,concat('57',loadtimes),COUNT(DISTINCT `user`) num,0
		FROM home_userguide_log${tdate}
		where id = 57
		group by appid,pid,loadtimes
	</insert>
	
	<insert id="insertHomeToolRemainStepByBatch" parameterType="java.util.Map">
		insert into home_tool_remain_step(tdate,appid,pid,lv,tool,step0,step1,step2,step3,step4,step5,step6)

		SELECT '${tdate}' tdate,xx.*,aa.step0,bb.step1,cc.step2,dd.step3,ee.step4,ff.step5,gg.step6 FROM
		(SELECT appid,pid,lv,tool
		FROM home_tool_log${tdate} WHERE type = 1 GROUP BY appid,pid,lv,tool) xx
		
		LEFT JOIN 
		(SELECT appid,pid,lv,tool,COUNT(DISTINCT `user`) step0 
		FROM home_tool_log${tdate} WHERE type = 1 AND remainNums = 0
		GROUP BY appid,pid,lv,tool) aa
		ON xx.appid=aa.appid AND xx.pid=aa.pid AND xx.lv=aa.lv AND xx.tool=aa.tool
		
		LEFT JOIN 
		(SELECT appid,pid,lv,tool,COUNT(DISTINCT `user`) step1 
		FROM home_tool_log${tdate} WHERE type = 1 AND remainNums = 1
		GROUP BY appid,pid,lv,tool) bb
		ON xx.appid=bb.appid AND xx.pid=bb.pid AND xx.lv=bb.lv AND xx.tool=bb.tool
		
		LEFT JOIN 
		(SELECT appid,pid,lv,tool,COUNT(DISTINCT `user`) step2 
		FROM home_tool_log${tdate} WHERE type = 1 AND remainNums = 2
		GROUP BY appid,pid,lv,tool) cc
		ON xx.appid=cc.appid AND xx.pid=cc.pid AND xx.lv=cc.lv AND xx.tool=cc.tool
		
		LEFT JOIN 
		(SELECT appid,pid,lv,tool,COUNT(DISTINCT `user`) step3 
		FROM home_tool_log${tdate} WHERE type = 1 AND remainNums = 3
		GROUP BY appid,pid,lv,tool) dd
		ON xx.appid=dd.appid AND xx.pid=dd.pid AND xx.lv=dd.lv AND xx.tool=dd.tool
		
		LEFT JOIN 
		(SELECT appid,pid,lv,tool,COUNT(DISTINCT `user`) step4 
		FROM home_tool_log${tdate} WHERE type = 1 AND remainNums = 4
		GROUP BY appid,pid,lv,tool) ee
		ON xx.appid=ee.appid AND xx.pid=ee.pid AND xx.lv=ee.lv AND xx.tool=ee.tool
		
		LEFT JOIN 
		(SELECT appid,pid,lv,tool,COUNT(DISTINCT `user`) step5 
		FROM home_tool_log${tdate} WHERE type = 1 AND remainNums = 5
		GROUP BY appid,pid,lv,tool) ff
		ON xx.appid=ff.appid AND xx.pid=ff.pid AND xx.lv=ff.lv AND xx.tool=ff.tool
		
		LEFT JOIN 
		(SELECT appid,pid,lv,tool,COUNT(DISTINCT `user`) step6 
		FROM home_tool_log${tdate} WHERE type = 1 AND remainNums > 5
		GROUP BY appid,pid,lv,tool) gg
		ON xx.appid=gg.appid AND xx.pid=gg.pid AND xx.lv=gg.lv AND xx.tool=gg.tool
	</insert>
	
	<insert id="insertHomeSignReportByBatch" parameterType="java.util.Map">
		insert into home_sign_report(tdate,appid,pid,actDay,sign1,sign2,sign3,sign4,sign5,sign6,sign7)
	
		SELECT '${tdate}' tdate,xx.*,aa.sign1,bb.sign2,cc.sign3,dd.sign4,ee.sign5,ff.sign6,gg.sign7 FROM
		(SELECT appid,pid,actDay
		FROM home_sign_log${tdate} GROUP BY appid,pid,actDay) xx
		
		LEFT JOIN 
		(SELECT appid,pid,actDay,COUNT(DISTINCT `user`) sign1 
		FROM home_sign_log${tdate} WHERE signDay = 1
		GROUP BY appid,pid,actDay) aa
		ON xx.appid=aa.appid AND xx.pid=aa.pid AND xx.actDay=aa.actDay
		
		LEFT JOIN 
		(SELECT appid,pid,actDay,COUNT(DISTINCT `user`) sign2 
		FROM home_sign_log${tdate} WHERE signDay = 2
		GROUP BY appid,pid,actDay) bb
		ON xx.appid=bb.appid AND xx.pid=bb.pid AND xx.actDay=bb.actDay
		
		LEFT JOIN 
		(SELECT appid,pid,actDay,COUNT(DISTINCT `user`) sign3 
		FROM home_sign_log${tdate} WHERE signDay = 3
		GROUP BY appid,pid,actDay) cc
		ON xx.appid=cc.appid AND xx.pid=cc.pid AND xx.actDay=cc.actDay
		
		LEFT JOIN 
		(SELECT appid,pid,actDay,COUNT(DISTINCT `user`) sign4 
		FROM home_sign_log${tdate} WHERE signDay = 4
		GROUP BY appid,pid,actDay) dd
		ON xx.appid=dd.appid AND xx.pid=dd.pid AND xx.actDay=dd.actDay
		
		LEFT JOIN 
		(SELECT appid,pid,actDay,COUNT(DISTINCT `user`) sign5 
		FROM home_sign_log${tdate} WHERE signDay = 5
		GROUP BY appid,pid,actDay) ee
		ON xx.appid=ee.appid AND xx.pid=ee.pid AND xx.actDay=ee.actDay
		
		LEFT JOIN 
		(SELECT appid,pid,actDay,COUNT(DISTINCT `user`) sign6 
		FROM home_sign_log${tdate} WHERE signDay = 6
		GROUP BY appid,pid,actDay) ff
		ON xx.appid=ff.appid AND xx.pid=ff.pid AND xx.actDay=ff.actDay
		
		LEFT JOIN 
		(SELECT appid,pid,actDay,COUNT(DISTINCT `user`) sign7 
		FROM home_sign_log${tdate} WHERE signDay = 7
		GROUP BY appid,pid,actDay) gg
		ON xx.appid=gg.appid AND xx.pid=gg.pid AND xx.actDay=gg.actDay
	</insert>
	
	<insert id="insertHomeFeeRemainStepByBatch" parameterType="java.util.Map">
		insert into home_fee_remain_step(tdate,appid,pid,lv,step0,step1,step2,step3,step4,step5,step6)

		SELECT '${tdate}' tdate,xx.*,t0.step0,t1.step1,t2.step2,t3.step3,t4.step4,t5.step5,t6.step6 FROM
		(SELECT appid,pid,lv FROM home_fee_log${tdate} GROUP BY appid,pid,lv) xx
		
		LEFT JOIN 
		<foreach collection="array1" item="item" separator=" LEFT JOIN ">
			(SELECT appid,pid,lv,COUNT(DISTINCT `user`) step${item} 
			FROM home_fee_log${tdate} WHERE remainNums = ${item} GROUP BY appid,pid,lv) t${item}
			ON xx.appid=t${item}.appid AND xx.pid=t${item}.pid AND xx.lv=t${item}.lv
		</foreach>
		
		LEFT JOIN 
		(SELECT appid,pid,lv,COUNT(DISTINCT `user`) step6 
		FROM home_fee_log${tdate} WHERE remainNums > 5 GROUP BY appid,pid,lv) t6
		ON xx.appid=t6.appid AND xx.pid=t6.pid AND xx.lv=t6.lv
	</insert>
	
	<insert id="insertHomeFeeTotalInfoByBatch" parameterType="java.util.Map">
		insert into home_fee_total_info(tdate,appid,pid,lv,buyFiveNums,successNum,successCount,feeNum,feeCount,remainAvg)
		
		SELECT '${tdate}' tdate,appid,pid,lv,buyFiveNums,0,0,
			COUNT(DISTINCT `user`) feeNum,0,
			TRUNCATE(AVG(remainNums),2) remainAvg
		FROM home_level_log${tdate} WHERE type = 2
		GROUP BY appid,pid,lv,buyFiveNums

		union all

		SELECT '${tdate}' tdate,appid,pid,lv,'-1',0,0,
			COUNT(DISTINCT `user`) feeNum,SUM(buyFiveNums) feeCount,
			TRUNCATE(AVG(remainNums),2) remainAvg
		FROM home_level_log${tdate} WHERE type = 2 AND buyFiveNums > 0
		GROUP BY appid,pid,lv
	</insert>
	
	
	<insert id="insertHomeUserTaskByBatch" parameterType="java.util.Map">
		insert into home_usertask_info(tdate,appid,pid,taskId,taskNums,taskModel,freeNum,finishNum,taskDay)
		
		SELECT '${tdate}' tdate,appid,pid,taskId,null,1,
			COUNT(DISTINCT CASE WHEN taskType = 1 THEN `user` END) as freeNum,
			COUNT(DISTINCT CASE WHEN taskType = 2 THEN `user` END) as finishNum,
				TRUNCATE(avg(taskDay),0) taskDay
		FROM home_usertask_log${tdate} WHERE taskModel = 1
		GROUP BY appid,pid,taskId
		
		UNION ALL
		
		SELECT '${tdate}' tdate,appid,pid,taskId,taskNums,2,
			COUNT(DISTINCT CASE WHEN taskType = 1 THEN `user` END) as freeNum,
			COUNT(DISTINCT CASE WHEN taskType = 2 THEN `user` END) as finishNum,
			TRUNCATE(avg(taskDay),0) taskDay
		FROM home_usertask_log${tdate} WHERE taskModel = 2
		GROUP BY appid,pid,taskId,taskNums
	</insert>
	<insert id="insertHomePlayActionByBatch" parameterType="java.util.Map">
		insert into home_playaction_info(tdate,appid,pid,actId,
			field0,field1,field2,field3,switchNum,
			field20,field21,field22,field23,switchCount)
		
		SELECT '${tdate}' tdate,appid,pid,actId,
			COUNT(DISTINCT CASE WHEN actType = 0 THEN `user` END) as field0,
			COUNT(DISTINCT CASE WHEN actType = 1 THEN `user` END) as field1,
			COUNT(DISTINCT CASE WHEN actType = 2 THEN `user` END) as field2,
			COUNT(DISTINCT CASE WHEN actType = 3 THEN `user` END) as field3,
			COUNT(DISTINCT CASE WHEN actType = 4 THEN `user` END) as switchNum,
			SUM(CASE WHEN actType = 0 THEN isFirst ELSE 0 END) as field20,
			SUM(CASE WHEN actType = 1 THEN isFirst ELSE 0 END) as field21,
			SUM(CASE WHEN actType = 2 THEN isFirst ELSE 0 END) as field22,
			SUM(CASE WHEN actType = 3 THEN isFirst ELSE 0 END) as field23,
			SUM(CASE WHEN actType = 4 THEN isFirst ELSE 0 END) as switchCount
		FROM home_playaction_log${tdate}
		GROUP BY appid,pid,actId
	</insert>
	
	<insert id="insertHomeStoryByBatch" parameterType="java.util.Map">
		insert into home_story_info(tdate,appid,pid,storyId,chooseType,
				startNum,successNum,timeAvg,passNum)
		
		SELECT '${tdate}' tdate,appid,pid,storyId,chooseType,
			COUNT(DISTINCT CASE WHEN type = 1 THEN `user` END) as startNum,
			COUNT(DISTINCT CASE WHEN type = 2 THEN `user` END) as successNum,
			ROUND(avg(useTimes)) as timeAvg,
			COUNT(DISTINCT CASE WHEN isPass = 1 THEN `user` END) as passNum
		FROM home_story_log${tdate}
		GROUP BY appid,pid,storyId,chooseType
	</insert>
	
	<insert id="insertHomeUserRangeList" parameterType="java.util.Map">
		insert into ${tableName}(tdate,appid,${cp},num1) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.param1},
			#{li.param2},
			#{li.param3})
		</foreach>
	</insert>
	
	<update id="updateHomeUserRangeNumTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			update ${tableName} set 
	    		${daynum} = #{li.param3}
	    	where tdate = #{li.tdate} and appid = #{li.param1} and ${cp} = #{li.param2}
    	</foreach>
	</update>
	
</mapper>