<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.slave2.MobileIntegralConfigMapper">
	
	<!--查询所有的商品配置列表-->
  	<select id="selectByAll" resultType="com.wbgame.pojo.ProductPriceConfigVo">
	    select product_id productNo, product_name productName, product_short_name productShortName,  price, `remark` as remark ,statues, cuser,  createtime, euser, endtime
	    from kb_product_price_config where 1=1
	      <if test="productNo != null and productNo != ''">
	        and product_id=#{productNo,jdbcType=VARCHAR}
	      </if>
  	</select>
  	<!--根据商品编号删除商品配置信息-->
  	<delete id="deleteByPrimaryKey" parameterType="com.wbgame.pojo.ProductPriceConfigVo">
	    delete from kb_product_price_config
	    where product_id = #{productNo,jdbcType=VARCHAR}
	 </delete>
	 <!--添加配置信息-->
	 <insert id="insert" parameterType="com.wbgame.pojo.ProductPriceConfigVo">
	    insert into kb_product_price_config (product_id, product_name, product_short_name,  price, statues, cuser,  createtime, euser, endtime,`remark`)
	    values (#{productNo,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{productShortName,jdbcType=VARCHAR}, 
	      #{price,jdbcType=DOUBLE}, #{statu,jdbcType=INTEGER}, #{cuser,jdbcType=VARCHAR}, 
	      #{createtime,jdbcType=DATE}, #{euser,jdbcType=VARCHAR}, #{endtime,jdbcType=DATE}, #{remark,jdbcType=VARCHAR})
	 </insert>
	 <!--更新商品价格-->
	 <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.ProductPriceConfigVo">
	    update kb_product_price_config
	    set price = #{price,jdbcType=DOUBLE},
	    	`remark` = #{remark,jdbcType=VARCHAR},
	      endtime = #{endtime,jdbcType=DATE},
	      euser = #{euser,jdbcType=DATE}
	      where product_id = #{productNo,jdbcType=VARCHAR}
	 </update>
	 
	 <!--汇总昨日各产品收入-->
	 <insert id="insertProductRevenue">
	    INSERT kb_product_revenue(appid,cha,app_ver,product_id,product_name,revenue,date)
		SELECT
			o.appid,
			o.cha,
			o.app_ver,
			p.product_id,
			(SELECT product_name FROM kb_product_price_config where product_id=p.product_id) as productName,
			TRUNCATE(sum(p.quantity)*(SELECT price FROM kb_product_price_config where product_id=p.product_id)/100, 1) as revenue,
			DATE_SUB(curdate(),INTERVAL 1 DAY) as date
		FROM
			kb_coupons_record o
		LEFT JOIN kb_product_exchange_record p ON o.kb_order_id = p.kb_order_id
		WHERE
			o.order_id LIKE CONCAT(DATE_FORMAT(DATE_SUB(curdate(),INTERVAL 1 DAY),'%Y%m%d'),'%')
		AND is_sued = 2
		GROUP BY
			o.appid,
			o.cha,
			o.app_ver,
		p.product_id
	 </insert>
	 <!--查询商品收入-->
  	<select id="selectRevenueList" parameterType="java.util.Map" resultType="com.wbgame.pojo.ProductRevenueVo">
	    select date,appid as  appId,cha as cha,app_ver as appVer,product_id productNo, product_name productName,`revenue` as  revenue
	    from kb_product_revenue where 1=1
	      <if test="appid != null and appid != ''">
	        and appid =#{appid,jdbcType=VARCHAR}
	      </if>
	      <if test="appids != null and appids.length>0">
	      	and appid in
	         <foreach collection="appids" index="index" item="appids"
	            open="(" separator="," close=")">
	            #{appids}
        	</foreach>
	      </if>
	      <if test="sdate != null">
	     		<![CDATA[and date>=#{sdate,jdbcType=DATE} ]]>
	      </if>
	      <if test="edate != null">
	    	  	<![CDATA[and date<=#{edate,jdbcType=DATE}]]>
	      </if>
	      order  by `revenue` desc
  	</select>
</mapper>