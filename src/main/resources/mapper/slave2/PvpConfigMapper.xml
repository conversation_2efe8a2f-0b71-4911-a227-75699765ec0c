<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave2.PvpConfigMapper">
    <!-- 好友邀请收益配置相关-->
    <select id="getPvpMatchList" resultType="com.wbgame.pojo.mobile.PvpMatchVo" parameterType="com.wbgame.pojo.mobile.PvpMatchVo">
        select  * from pvp_match_config where 1=1
        <if test="prjid != null and prjid != ''">
            and  `prjid` = #{prjid}
        </if>
        <if test="channel != null and channel != ''">
            and  `channel` = #{channel}
        </if>
        <if test="statu != null and statu != ''">
            and  `statu` = #{statu}
        </if>
        order by createtime desc
    </select>

    <insert id="addPvpMatchConfig" parameterType="com.wbgame.pojo.mobile.PvpMatchVo">
        insert into pvp_match_config (`prjid`,channel,matchId,ecpmLimit,`dayLimitTimes`,dayLimit,statu,createUser,createtime)
        values (#{prjid},#{channel},#{matchId},#{ecpmLimit},#{dayLimitTimes},#{dayLimit},#{statu},#{createUser},NOW())
    </insert>

    <update id="updatePvpMatchConfig" parameterType="com.wbgame.pojo.mobile.PvpMatchVo">
        update pvp_match_config set `prjid`= #{prjid},channel = #{channel},matchId = #{matchId},ecpmLimit = #{ecpmLimit},dayLimitTimes =#{dayLimitTimes},
        `dayLimit` = #{dayLimit},`statu` = #{statu},modifyUser = #{modifyUser},modifyTime = NOW()
        where id = #{id}
    </update>

    <delete id="delPvpMatchConfig" parameterType="com.wbgame.pojo.mobile.PvpMatchVo">
        delete from pvp_match_config where id = #{id}
    </delete>




    <!-- 比赛详情配置相关-->
    <select id="getPvpMatchDetailList" resultType="com.wbgame.pojo.mobile.PvpMatchDetailVo" parameterType="com.wbgame.pojo.mobile.PvpMatchDetailVo">
        select  * from  pvp_matchinfo_config where  1=1
        <if test="statu != null and statu != ''">
            and  `statu` = #{statu}
        </if>
        order by createtime desc
    </select>


    <insert id="addPvpMatchDetailConfig" parameterType="com.wbgame.pojo.mobile.PvpMatchDetailVo">
        insert pvp_matchinfo_config (matchId,awardConfig,awardNum,ticket,matchNum,matchName,matchTime,firstPrize,statu,createUser,createtime)
        values (#{matchId},#{awardConfig},#{awardNum},#{ticket},#{matchNum},#{matchName},#{matchTime},#{firstPrize},#{statu},#{createUser},NOW())
    </insert>


    <update id="updatePvpMatchDetailConfig" parameterType="com.wbgame.pojo.mobile.PvpMatchDetailVo">
        update pvp_matchinfo_config set `matchId` = #{matchId},awardConfig = #{awardConfig},
        `awardNum` = #{awardNum},ticket = #{ticket},
        `matchNum` = #{matchNum},matchName = #{matchName},
        matchTime = #{matchTime},`firstPrize` = #{firstPrize},
        statu = #{statu},modifyUser = #{modifyUser},modifyTime = NOW()
        where id = #{id}
    </update>

    <delete id="delPvpMatchDetailConfig" parameterType="com.wbgame.pojo.mobile.PvpMatchDetailVo">
        delete from pvp_matchinfo_config where id =#{id}
    </delete>




    <!-- 比赛胜率配置-->
    <select id="getPvpWinRatioList" resultType="com.wbgame.pojo.mobile.PvpWinRatioVo" parameterType="com.wbgame.pojo.mobile.PvpWinRatioVo">
        select  * from  pvp_winratio_config where  1=1
        <if test="statu != null and statu != ''">
            and  `statu` = #{statu}
        </if>
        order by createtime desc
    </select>


    <insert id="addPvpWinRatioConfig" parameterType="com.wbgame.pojo.mobile.PvpWinRatioVo">
        insert pvp_winratio_config (matchId,minEcpm,maxEcpm,ratio,statu,createUser,createtime)
        values (#{matchId},#{minEcpm},#{maxEcpm},#{ratio},#{statu},#{createUser},NOW())
    </insert>


    <update id="updatePvpWinRatioConfig" parameterType="com.wbgame.pojo.mobile.PvpWinRatioVo">
        update pvp_winratio_config set `matchId` = #{matchId},minEcpm = #{minEcpm},
        `maxEcpm` = #{maxEcpm},ratio = #{ratio},
        statu = #{statu},modifyUser = #{modifyUser},modifyTime = NOW()
        where id = #{id}
    </update>

    <delete id="delPvpWinRatioConfig" parameterType="com.wbgame.pojo.mobile.PvpWinRatioVo">
        delete from pvp_winratio_config where id =#{id}
    </delete>

    <!-- 比赛奖励配置-->
    <select id="getPvpAwardList" resultType="com.wbgame.pojo.mobile.PvpAwardVo" parameterType="com.wbgame.pojo.mobile.PvpAwardVo">
        select  * from  pvp_award_config where  1=1
        <if test="statu != null and statu != ''">
            and  `statu` = #{statu}
        </if>
        order by createtime desc
    </select>


    <insert id="addPvpAwardConfig" parameterType="com.wbgame.pojo.mobile.PvpAwardVo">
        insert pvp_award_config (awardid,awardDesc,amount,devote,canReceive,statu,createUser,createtime)
        values (#{awardid},#{awardDesc},#{amount},#{devote},#{canReceive},#{statu},#{createUser},NOW())
    </insert>


    <update id="updatePvpAwardConfig" parameterType="com.wbgame.pojo.mobile.PvpAwardVo">
        update pvp_award_config set `awardid` = #{awardid},awardDesc = #{awardDesc},
        `amount` = #{amount},devote = #{devote},canReceive = #{canReceive},
        statu = #{statu},modifyUser = #{modifyUser},modifyTime = NOW()
        where id = #{id}
    </update>

    <delete id="delPvpAwardConfig" parameterType="com.wbgame.pojo.mobile.PvpAwardVo">
        delete from pvp_award_config where id =#{id}
    </delete>


    <!-- 比赛门票配置-->
    <select id="getPvpTicketList" resultType="com.wbgame.pojo.mobile.PvpTicketVo" parameterType="com.wbgame.pojo.mobile.PvpTicketVo">
        select  * from  pvp_ticket_config where 1=1
        <if test="prjid != null and prjid != ''">
            and  `prjid` = #{prjid}
        </if>
        <if test="channel != null and channel != ''">
            and  `channel` = #{channel}
        </if>
        <if test="statu != null and statu != ''">
            and  `statu` = #{statu}
        </if>
        order by createtime desc
    </select>


    <insert id="addPvpTicketConfig" parameterType="com.wbgame.pojo.mobile.PvpTicketVo">
        insert pvp_ticket_config (prjid,channel,dailyLimit,dailyTips,eachLimit,eachTips,
                                    totalLimit,totalTips,statu,createUser,createtime)
        values (#{prjid},#{channel},#{dailyLimit},#{dailyTips},#{eachLimit},#{eachTips},#{totalLimit},#{totalTips},
                                    #{statu},#{createUser},NOW())
    </insert>


    <update id="updatePvpTicketConfig" parameterType="com.wbgame.pojo.mobile.PvpTicketVo">
        update pvp_ticket_config set `prjid` = #{prjid},channel = #{channel},
        `dailyLimit` = #{dailyLimit},dailyTips = #{dailyTips},
        `eachLimit` = #{eachLimit},eachTips = #{eachTips},
        `totalLimit` = #{totalLimit},totalTips = #{totalTips},
        statu = #{statu},modifyUser = #{modifyUser},modifyTime = NOW()
        where id = #{id}
    </update>

    <delete id="delPvpTicketConfig" parameterType="com.wbgame.pojo.mobile.PvpTicketVo">
        delete from pvp_ticket_config where id =#{id}
    </delete>



</mapper>