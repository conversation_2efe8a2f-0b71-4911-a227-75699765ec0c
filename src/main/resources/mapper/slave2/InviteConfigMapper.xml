<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave2.InviteConfigMapper">
    <!-- 好友邀请收益配置相关-->
    <select id="selectInviteIncomeConfig" resultType="com.wbgame.pojo.mobile.InviteIncomeConfigVo" parameterType="com.wbgame.pojo.mobile.InviteIncomeConfigVo">
        select  * from invite_adincome_config where 1=1
        <if test="prjid != null and prjid != ''">
            and  `prjid` = #{prjid}
        </if>
        order by createtime desc
    </select>

    <insert id="saveInviteIncomeConfig" parameterType="com.wbgame.pojo.mobile.InviteIncomeConfigVo">
        insert into invite_adincome_config (`prjid`,adType,limitMin,limitMax,incomeMin,incomeMax,cuser,createtime)
        values (#{prjid},#{adType},#{limitMin},#{limitMax},#{incomeMin},#{incomeMax},#{cuser},NOW())
    </insert>

    <update id="updateInviteIncomeConfig" parameterType="com.wbgame.pojo.mobile.InviteIncomeConfigVo">
        update invite_adincome_config set `prjid`= #{prjid},adType = #{adType},limitMin = #{limitMin},limitMax = #{limitMax},incomeMin =#{incomeMin},
        incomeMax = #{incomeMax},euser = #{euser},endtime = NOW()
        where id = #{id}
    </update>

    <delete id="delInviteIncomeConfig" parameterType="com.wbgame.pojo.mobile.InviteIncomeConfigVo">
        delete from invite_adincome_config where id = #{id}
    </delete>

    <!-- 好友邀请开关配置相关-->
    <select id="selectInviteCheckConfig" resultType="com.wbgame.pojo.mobile.InviteCheckConfigVo" parameterType="com.wbgame.pojo.mobile.InviteCheckConfigVo">
        select  * from  invite_check_config where  1=1
        <if test="prjid != null and prjid != ''">
            and  `prjid` = #{prjid}
        </if>
    </select>

    <insert id="saveInviteCheckConfig" parameterType="com.wbgame.pojo.mobile.InviteCheckConfigVo">
        insert invite_check_config (`prjid`,bindCheck3,bindLimitCheck,bindLimitNum,devoteCheck1,devoteCheck2,devoteCheck4,withdrawType,popCheck,hbLimit,
        tips,cuser,createtime)
        values (#{prjid},#{bindCheck3},#{bindLimitCheck},#{bindLimitNum},#{devoteCheck1},#{devoteCheck2},#{devoteCheck4},#{withdrawType},#{popCheck},
        #{hbLimit},#{tips},#{cuser},NOW())
    </insert>


    <update id="updateInviteCheckConfig" parameterType="com.wbgame.pojo.mobile.InviteCheckConfigVo">
        update invite_check_config set `prjid` = #{prjid},bindCheck3 = #{bindCheck3},bindLimitNum = #{bindLimitNum},
        `devoteCheck1` = #{devoteCheck1},devoteCheck2 = #{devoteCheck2},devoteCheck4 = #{devoteCheck4},
        `withdrawType` = #{withdrawType},popCheck = #{popCheck},hbLimit = #{hbLimit},bindLimitCheck =#{bindLimitCheck},
        `tips` = #{tips},euser = #{euser},endtime = NOW()
        where id = #{id}
    </update>

    <delete id="delInviteCheckConfig" parameterType="com.wbgame.pojo.mobile.InviteCheckConfigVo">
        delete from invite_check_config where id =#{id}
    </delete>


</mapper>