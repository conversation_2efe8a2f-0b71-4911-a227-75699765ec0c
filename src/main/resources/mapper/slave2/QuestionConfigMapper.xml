<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave2.QuestionConfigMapper">
    <!-- 好友邀请收益配置相关-->
    <select id="getQuestionPopList" resultType="com.wbgame.pojo.mobile.QuestionPopCofigVo" parameterType="com.wbgame.pojo.mobile.QuestionPopCofigVo">
        select  * from question_pop_config where 1=1
        <if test="prjid != null and prjid != ''">
            and  `prjid` = #{prjid}
        </if>
        <if test="channel != null and channel != ''">
            and  `channel` = #{channel}
        </if>
        <if test="version != null and version != ''">
            and  `version` = #{version}
        </if>
        <if test="statu != null and statu != ''">
            and  `statu` = #{statu}
        </if>
        order by createtime desc
    </select>

    <insert id="addQuestionPopConfig" parameterType="com.wbgame.pojo.mobile.QuestionPopCofigVo">
        insert into question_pop_config (`prjid`,channel,version,popid,questionid,`close`,statu,createUser,createtime)
        values (#{prjid},#{channel},#{version},#{popid},#{questionid},#{close},#{statu},#{createUser},NOW())
    </insert>

    <update id="updateQuestionPopConfig" parameterType="com.wbgame.pojo.mobile.QuestionPopCofigVo">
        update question_pop_config set `prjid`= #{prjid},channel = #{channel},version = #{version},popid = #{popid},questionid =#{questionid},
        `close` = #{close},`statu` = #{statu},modifyUser = #{modifyUser},modifytime = NOW()
        where id = #{id}
    </update>

    <delete id="delQuestionPopConfig" parameterType="com.wbgame.pojo.mobile.QuestionPopCofigVo">
        delete from question_pop_config where id = #{id}
    </delete>




    <!-- 问卷问题配置相关-->
    <select id="getQuestionConfigList" resultType="com.wbgame.pojo.mobile.QuestionAnswerConfigVo" parameterType="com.wbgame.pojo.mobile.QuestionAnswerConfigVo">
        select  * from  question_answer_config where  1=1
        <if test="question != null and question != ''">
            and  `question` LIKE '${question}'
        </if>
    </select>


    <insert id="addQuestionConfig" parameterType="com.wbgame.pojo.mobile.QuestionAnswerConfigVo">
        insert question_answer_config (question,answerList,statu,createUser,createtime)
        values (#{question},#{answerList},#{statu},#{createUser},NOW())
    </insert>


    <update id="updateQuestionConfig" parameterType="com.wbgame.pojo.mobile.QuestionAnswerConfigVo">
        update question_answer_config set `question` = #{question},answerList = #{answerList},statu = #{statu},
        modifyUser = #{modifyUser},modifytime = NOW()
        where questionid = #{questionid}
    </update>

    <delete id="delQuestionConfig" parameterType="com.wbgame.pojo.mobile.QuestionAnswerConfigVo">
        delete from question_answer_config where questionid =#{questionid}
    </delete>


</mapper>