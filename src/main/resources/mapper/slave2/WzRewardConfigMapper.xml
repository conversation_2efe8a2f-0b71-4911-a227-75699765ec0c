<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave2.wz.WzRewardConfigMapper">

        <select id="queryAll" resultType="com.wbgame.pojo.adv2.wz.HomeWzRewardConfig">
            SELECT * FROM home_wz_reward_config
            <where>
                <if test="reward_id != null and reward_id != ''">
                    and reward_id = #{reward_id}
                </if>
                <if test="reward_type != null and reward_type != ''">
                    and reward_type = #{reward_type}
                </if>
                <if test="appid != null and appid != ''">
                    and appid in (${appid})
                </if>
                <if test="channel != null and channel != ''">
                    and channel in (${channel})
                </if>
                <if test="cuser != null and cuser != ''">
                    and cuser = #{cuser}
                </if>
            </where>
            order by createtime desc
        </select>
        <select id="queryById" resultType="com.wbgame.pojo.adv2.wz.HomeWzRewardConfig" parameterType="com.wbgame.pojo.adv2.wz.HomeWzRewardConfig">
            SELECT * FROM home_wz_reward_config WHERE appid=#{appid} and channel=#{channel} and reward_id=#{reward_id}
        </select>
        <insert id="insert" parameterType="com.wbgame.pojo.adv2.wz.HomeWzRewardConfig">
            INSERT INTO home_wz_reward_config (appid, channel, reward_id, reward_type, gold_amount, ecpm_coefficient_range, float_coefficient, pop_up_times, video_reward_times, close_btn_show, delayed_auto_reward, cuser, createtime)
            VALUES (#{appid}, #{channel}, #{reward_id}, #{reward_type}, #{gold_amount}, #{ecpm_coefficient_range}, #{float_coefficient}, #{pop_up_times}, #{video_reward_times}, #{close_btn_show}, #{delayed_auto_reward}, #{cuser}, now())
        </insert>
        <update id="update" parameterType="com.wbgame.pojo.adv2.wz.HomeWzRewardConfig">
            UPDATE home_wz_reward_config SET reward_type = #{reward_type}, gold_amount = #{gold_amount}, ecpm_coefficient_range = #{ecpm_coefficient_range},
                    float_coefficient=#{float_coefficient}, pop_up_times=#{pop_up_times}, video_reward_times=#{video_reward_times},close_btn_show=#{close_btn_show},delayed_auto_reward=#{delayed_auto_reward}, euser=#{euser}, endtime=now()
            WHERE appid=#{appid} and channel=#{channel} and reward_id=#{reward_id}
        </update>
        <delete id="delete" parameterType="com.wbgame.pojo.adv2.wz.HomeWzRewardConfig">
            DELETE FROM home_wz_reward_config WHERE appid=#{appid} and channel=#{channel} and reward_id=#{reward_id}
        </delete>


    <insert id="insertWzWithdrawal" parameterType="com.wbgame.pojo.adv2.wz.HomeWzWithdrawalSetting"
            useGeneratedKeys="true" keyProperty="wlevel_id" keyColumn="wlevel_id">
        INSERT INTO home_wz_withdrawal_setting
            (appid, channel, withdrawal_level, withdrawal_amount, min_red_packet_times, min_continuous_login_days, withdrawal_times, payout_method, cuser, createtime)
        VALUES
            (#{appid}, #{channel}, #{withdrawal_level}, #{withdrawal_amount}, #{min_red_packet_times}, #{min_continuous_login_days}, #{withdrawal_times}, #{payout_method}, #{cuser}, now())
    </insert>

    <update id="updateWzWithdrawal" parameterType="com.wbgame.pojo.adv2.wz.HomeWzWithdrawalSetting">
        UPDATE home_wz_withdrawal_setting
        SET appid = #{appid},
            channel = #{channel},
            withdrawal_level = #{withdrawal_level},
            withdrawal_amount = #{withdrawal_amount},
            min_red_packet_times = #{min_red_packet_times},
            min_continuous_login_days = #{min_continuous_login_days},
            withdrawal_times = #{withdrawal_times},
            payout_method = #{payout_method},
            euser = #{euser},
            endtime = now()
        WHERE wlevel_id = #{wlevel_id}
    </update>

    <delete id="deleteWzWithdrawal" parameterType="com.wbgame.pojo.adv2.wz.HomeWzWithdrawalSetting">
        DELETE FROM home_wz_withdrawal_setting WHERE wlevel_id = #{wlevel_id}
    </delete>

    <select id="queryAllByIdWzWithdrawal" parameterType="int" resultType="com.wbgame.pojo.adv2.wz.HomeWzWithdrawalSetting">
        SELECT * FROM home_wz_withdrawal_setting WHERE wlevel_id = #{wlevel_id}
    </select>

    <select id="queryAllWzWithdrawal" parameterType="com.wbgame.pojo.adv2.wz.HomeWzWithdrawalSetting" resultType="com.wbgame.pojo.adv2.wz.HomeWzWithdrawalSetting">
        SELECT * FROM home_wz_withdrawal_setting
        <where>
            <if test="wlevel_id != null and wlevel_id != ''">
                and wlevel_id = #{wlevel_id}
            </if>
            <if test="withdrawal_level != null and withdrawal_level != ''">
                and withdrawal_level = #{withdrawal_level}
            </if>
            <if test="payout_method != null and payout_method != ''">
                and payout_method = #{payout_method}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="cuser != null and cuser != ''">
                and cuser = #{cuser}
            </if>
        </where>
        order by wlevel_id desc
    </select>


    <insert id="insertWzLimitConfig" parameterType="com.wbgame.pojo.adv2.wz.HomeWzLimitConfig"
            useGeneratedKeys="true" keyProperty="wlevel_id" keyColumn="wlevel_id">
        INSERT INTO home_wz_limit_config
            (appid,channel,daily_video_reward_times,withdrawal_rule_desc,user_limit_counts,user_limit_money,device_limit_counts,device_limit_money,app_limit_counts,app_limit_money,cuser,createtime)
        VALUES
            (#{appid},#{channel},#{daily_video_reward_times},#{withdrawal_rule_desc},#{user_limit_counts},#{user_limit_money},#{device_limit_counts},#{device_limit_money},#{app_limit_counts},#{app_limit_money},#{cuser},now())
    </insert>

    <update id="updateWzLimitConfig" parameterType="com.wbgame.pojo.adv2.wz.HomeWzLimitConfig">
        UPDATE home_wz_limit_config
        SET
            daily_video_reward_times=#{daily_video_reward_times},
            withdrawal_rule_desc=#{withdrawal_rule_desc},
            user_limit_counts=#{user_limit_counts},
            user_limit_money=#{user_limit_money},
            device_limit_counts=#{device_limit_counts},
            device_limit_money=#{device_limit_money},
            app_limit_counts=#{app_limit_counts},
            app_limit_money=#{app_limit_money},
            euser = #{euser},
            endtime = now()
        WHERE appid=#{appid} and channel=#{channel}
    </update>

    <delete id="deleteWzLimitConfig" parameterType="com.wbgame.pojo.adv2.wz.HomeWzLimitConfig">
        DELETE FROM home_wz_limit_config WHERE appid=#{appid} and channel=#{channel}
    </delete>

    <select id="queryByIdWzLimitConfig" parameterType="com.wbgame.pojo.adv2.wz.HomeWzLimitConfig" resultType="com.wbgame.pojo.adv2.wz.HomeWzLimitConfig">
        SELECT * FROM home_wz_limit_config where appid=#{appid} and channel=#{channel}
    </select>

    <select id="queryAllWzLimitConfig" parameterType="com.wbgame.pojo.adv2.wz.HomeWzLimitConfig" resultType="com.wbgame.pojo.adv2.wz.HomeWzLimitConfig">
        SELECT * FROM home_wz_limit_config
        <where>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="cuser != null and cuser != ''">
                and cuser = #{cuser}
            </if>
        </where>
        order by createtime desc
    </select>


    <insert id="insertWzTiwaiConfig" parameterType="com.wbgame.pojo.adv2.wz.HomeWzTiwaiConfig"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO home_wz_tiwai_config
            (appid,channel,prjid,user_group,ver,`status`,content,cuser,createtime)
        VALUES
            (#{appid},#{channel},#{prjid},#{user_group},#{ver},#{status},#{content},#{cuser},now())
    </insert>

    <update id="updateWzTiwaiConfig" parameterType="com.wbgame.pojo.adv2.wz.HomeWzTiwaiConfig">
        UPDATE home_wz_tiwai_config
        SET
            appid=#{appid},
            channel=#{channel},
            prjid=#{prjid},
            user_group=#{user_group},
            ver=#{ver},
            `status`=#{status},
            content=#{content},
            euser = #{euser},
            endtime = now()
        WHERE id=#{id}
    </update>

    <delete id="deleteWzTiwaiConfig" parameterType="com.wbgame.pojo.adv2.wz.HomeWzTiwaiConfig">
        DELETE FROM home_wz_tiwai_config WHERE id=#{id}
    </delete>

    <select id="queryAllWzTiwaiConfig" parameterType="com.wbgame.pojo.adv2.wz.HomeWzTiwaiConfig" resultType="com.wbgame.pojo.adv2.wz.HomeWzTiwaiConfig">
        SELECT * FROM home_wz_tiwai_config
        <where>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="prjid != null and prjid != ''">
                and prjid = #{prjid}
            </if>
            <if test="user_group != null and user_group != ''">
                and user_group in (${user_group})
            </if>
            <if test="status != null and status != ''">
                and `status` = #{status}
            </if>
        </where>
        order by createtime desc
    </select>

</mapper>