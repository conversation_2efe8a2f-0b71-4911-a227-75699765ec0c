<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave2.RedPackMapper">
	
	<select id="selectRedPackInfo" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.realAuth.RedPackInfoDto">
		select DATE_FORMAT(createtime,'%Y%m%d') c_date,appid c_appid,pid c_pid,'0.3' c_price,count(distinct imei) c_users,ROUND(sum(amount),2) c_total
			from home_diamond_redpack_withdraw
			where 1=1
			and DATE_FORMAT(createtime,'%Y%m%d') = '${tdate}' 
			and (amount = '0.3' or amount = '0.30')
			and pid not in ('333351','333359')
			group by DATE_FORMAT(createtime,'%Y%m%d'),appid,pid,amount
			UNION ALL
			select DATE_FORMAT(createtime, '%Y%m%d') c_date,
				appid c_appid,
				pid c_pid,
				'0.3' c_price,
				count(DISTINCT imei) c_users,
				ROUND(sum(amount), 2) c_total from home_diamond_redpackdraw_withdraw
			WHERE
				1 = 1
			AND DATE_FORMAT(createtime, '%Y%m%d') = '${tdate}' 
			and (amount = '0.3' or amount = '0.30')
			and pid not in ('333351','333359')
			GROUP BY
				DATE_FORMAT(createtime, '%Y%m%d'),
				appid,
				pid,
				amount
	</select>
	
	<select id="selectTwoRate" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.realAuth.RedPackInfoDto">
		select DATE_FORMAT(a.createtime,'%Y%m%d') c_date,a.appid c_appid,a.pid c_pid,count(a.imei) c_twoUsers from home_diamond_redpack_withdraw a,home_diamond_log${yesterday} b 
			where DATE_FORMAT(a.createtime,'%Y%m%d') = '${tdate}'
			and a.pid = b.pid
			and a.lsn = b.lsn
			and a.appid = b.appid
			and (a.amount = '0.3' or a.amount = '0.30')
			group by DATE_FORMAT(a.createtime,'%Y%m%d'),a.appid,a.pid
			UNION ALL
			SELECT
			DATE_FORMAT(a.createtime, '%Y%m%d') c_date,
			a.appid c_appid,
			a.pid c_pid,
			count(a.imei) c_twoUsers
		FROM
			home_diamond_redpackdraw_withdraw a,
			home_diamond_log${yesterday} b
		WHERE
			DATE_FORMAT(a.createtime, '%Y%m%d') = '${tdate}'
		AND a.pid = b.pid
		AND a.lsn = b.lsn
		AND a.appid = b.appid
		and (a.amount = '0.3' or a.amount = '0.30')
		GROUP BY
			DATE_FORMAT(a.createtime, '%Y%m%d'),
			a.appid,
			a.pid
	</select>
	<select id="selectDau" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.realAuth.RedPackInfoDto">
		select appid c_appid,pid c_pid,count(distinct imei) c_dau from home_diamond_log${tdate}
		group by appid,pid
	</select>
	<select id="selectOverUser" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.realAuth.RedPackInfoDto">
		select pid c_pid,count(distinct imei) c_overUser from event_postpoint_log${tdate} where 1=1
			and eventId = 'reachGoal'
			and pid not in ('333311','333351')
			group by pid
	</select>
	<select id="selectWxSuccess" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.realAuth.RedPackInfoDto">
		select pid c_pid,count(distinct imei) wx_success from event_postpoint_log${tdate} where 1=1
			and eventId = 'wxLogin'
			and eventValue = 'success'
			and pid not in ('333311','333351')
			group by pid
	</select>
	<select id="selectWxFail" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.realAuth.RedPackInfoDto">
		select pid c_pid,count(distinct imei) wx_fail from event_postpoint_log${tdate} where 1=1
			and eventId = 'wxLogin'
			and eventValue = 'fail'
			and pid not in ('333311','333351')
			group by pid
	</select>
	<select id="selectWithDrawRequest" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.realAuth.RedPackInfoDto">
		select pid c_pid,count(distinct imei) c_withdraw from event_postpoint_log${tdate} where 1=1
			and eventId = 'redpackwithdraw'
			and eventValue = 'request'
			and pid not in ('333311','333351')
			group by pid
	</select>
	<select id="selectOpenUser" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.realAuth.RedPackInfoDto">
		select pid c_pid,count(distinct imei) c_openUser from home_diamond_actopen_userinfo_${mdate}
		where DATE_FORMAT(createtime,'%Y%m%d') = '${tdate}'
		group by pid
	</select>
	<select id="selectCashUser" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.realAuth.RedPackInfoDto">
		select pid c_pid,count(distinct imei) c_cashUser from event_postpoint_log${tdate} where 1=1
			and eventId = 'cashUseValue'
			and eventValue = '1'
			and pid not in ('333311','333351','333320')
			group by pid
	</select>
	<select id="selectWithdrawUser" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.realAuth.RedPackInfoDto">
		select pid c_pid,count(distinct imei) c_withdrawUser from event_postpoint_log${tdate} where 1=1
			and eventId = 'withdrawUseValue'
			and eventValue = '1'
			and pid not in ('333311','333351','333320')
			group by pid
	</select>
	<select id="selectWithdrawRet" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.realAuth.RedPackInfoDto">
		select pid c_pid,count(distinct imei) c_withdrawRet from event_postpoint_log${tdate} where 1=1
			and eventId = 'withdrawRetValue'
			and eventValue = '1'
			and pid not in ('333311','333351','333320')
			group by pid
	</select>

    <select id="selectHomeDiamondLog" resultType="java.util.Map">
		select appid,pid,lsn,imei from home_diamond_log${ds}
		where 1=1
		<if test="appid != null and appid != ''">
              and appid = #{appid}
		</if>
		GROUP BY appid,pid,lsn
	</select>

	<insert id="batchinsertUserRecord">
		insert ignore xyx_user_record(appid,pid,lsn,imei) values
		<foreach collection="list" item="it" separator=",">
			(#{it.appid},#{it.pid},#{it.lsn},#{it.imei})
		</foreach>
	</insert>

	<select id="selectNewRedpackdraw" resultType="java.util.Map">

		select userid,lsn,appid,pid,imei,openid,amount,type,DATE_FORMAT(a.create_time,'%Y-%m-%d %H:%i:%s') create_time,b.app_name
		from hb_user_withdraw_log a left join app_info b on a.appid = b.id
		where DATE_FORMAT(a.create_time,'%Y-%m-%d') between #{startTime} and #{endTime}
		<if test="appid != null and appid != ''">
			and appid = #{appid}
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="userid != null and userid != ''">
			and userid = #{userid}
		</if>
        order by ${order}

	</select>

    <select id="selectUserWithdrawTotal" resultType="java.util.Map">
		select a.userid,a.create_time,a.channel,ifnull(b.amount,0) amount,ifnull(b.num,0) num,ifnull(b.money,0) money,a.red_ticket,a.total from hb_redpack_user a left join
		(select userid,amount,count(1) num,round(amount*count(1),2) money from hb_user_withdraw_log where appid = #{appid}
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		  GROUP BY userid,amount) b on a.userid = b.userid
		where a.appid = #{appid} and date(a.create_time) between #{startTime} and #{endTime}
		<if test="pid != null and pid != ''">
			and a.pid = #{pid}
		</if>

	</select>

	<select id="selectRedPackWithdrawLog" resultType="java.util.Map">
		select * from hb_user_withdraw_log where date(create_time) = #{ds}
	</select>

    <select id="selectOldMchidAmount" resultType="com.alibaba.fastjson.JSONObject">
		select round(sum(amount),2) amount from app_video_redpackdraw_withdraw_history_v1 a
		left join home_diamond_redpack_wxconfig b on a.appid = b.appid and a.packg = b.pkg
		where mchid = #{mchid} and mode = 1 and createtime between #{scan_time} and now()
	</select>
</mapper>