<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave2.AdMsgMapper">

	<insert id="insertAdMsgTotalGroup" parameterType="java.util.List">
		
		insert into admsg_total(
			tdate,
			pid,
			ad_sid,
			ad_pos,
			game_type,
			ad_pos_type,
			req_num,
			success_num,
			hold_ratio,
			show_num,
			click_num,
			click_ratio,
			device_num,
			selfshow_num

		) values 
		<foreach collection="list" item="li" separator=","> 
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			'default',
			'default',
			#{li.ad_pos_type},
			#{li.req_num},
			#{li.success_num},
			#{li.hold_ratio},
			#{li.show_num},
			#{li.click_num},
			#{li.click_ratio},
			#{li.device_num},
			#{li.selfshow_num})
			
		</foreach>
		ON DUPLICATE KEY UPDATE 
		ad_pos_type=VALUES(ad_pos_type),
		show_num=VALUES(show_num),
		click_num=VALUES(click_num),
		click_ratio=VALUES(click_ratio),
		device_num=VALUES(device_num),
		selfshow_num=VALUES(selfshow_num)
	</insert>
	
	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>
	
	<update id="updateAdMsgTotalGroupOfActnum" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update admsg_total 
			set act_num = #{li.act_num},
				seep_ratio = CONCAT(truncate(device_num/#{li.act_num}*100,2),'%')
			where tdate = #{li.tdate} and pid = #{li.pid}
		</foreach>
	</update>
	
	<select id="selectAdMsgReqGroup" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgTotalVo">
		
		select 
			'${tdate}' as tdate,
			aa.pid,aa.ad_sid,<!-- aa.ad_pos_type, -->
			aa.req_num,aa.success_num,
			CONCAT(truncate(aa.success_num/aa.req_num*100,2),'%') as hold_ratio
		from 
			(SELECT pid,ad_sid,ad_pos_type,SUM(num) as req_num,
				SUM(CASE WHEN ad_flag = 1 THEN num ELSE 0 END) as success_num
			FROM admsg_request_${tdate} 
			GROUP BY pid,ad_sid<!-- ,ad_pos_type --> ) aa

	</select>
	<select id="selectAdMsgActionGroup" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgTotalVo">
		
		select 
			'${tdate}' as tdate,
			aa.pid,aa.ad_sid,aa.ad_pos_type,
			aa.show_num,aa.device_num,aa.click_num,cc.selfshow_num,
			CONCAT(truncate(aa.click_num/aa.show_num*100,2),'%') as click_ratio
		from 
			(SELECT pid,ad_sid,ad_pos_type,
				SUM(CASE WHEN ad_type = 1 THEN num ELSE 0 END) as show_num,
				SUM(CASE WHEN ad_type = 2 THEN num ELSE 0 END) as click_num,
				COUNT(DISTINCT CASE WHEN ad_type = 1 THEN imei END) as device_num
			FROM admsg_action_${tdate} 
			GROUP BY pid,ad_sid,ad_pos_type ) aa

		left join 
			(SELECT pid,ad_sid,ad_pos_type,SUM(num) as selfshow_num 
			FROM admsg_selfshow_${tdate}  
			GROUP BY pid,ad_sid,ad_pos_type ) cc
		on aa.pid = cc.pid and aa.ad_sid = cc.ad_sid and aa.ad_pos_type = cc.ad_pos_type
	</select>
	
	<select id="selectAdMsgTotal" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgTotalVo">
		
		select 
			tdate,
			pid,
			<choose>
				<when test="group_postype != null and group_postype == 'ok'">
					ad_pos_type,
				</when>
				<otherwise>
					ad_sid,
				</otherwise>
			</choose>
			(sum(req_num)+sum(success_num)) as req_num,
			sum(success_num) as success_num,
			CONCAT(truncate(sum(success_num)/(sum(req_num)+sum(success_num))*100,2),'%') as hold_ratio,
			sum(show_num) as show_num,
			sum(selfshow_num) as selfshow_num,
			sum(click_num) as click_num,
			CONCAT(truncate(sum(click_num)/sum(show_num)*100,2),'%') as click_ratio,
			CONCAT(truncate(sum(show_num)/(sum(req_num)+sum(success_num))*100,2),'%') as show_ratio,
			act_num,
			sum(device_num) as device_num,
			CONCAT(truncate(sum(device_num)/act_num*100,2),'%') as seep_ratio
			
		from admsg_total_${year} 
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="pid != null and pid != ''">
			and pid = #{pid} 
		</if>
		<if test="ad_sid != null and ad_sid != ''">
			and ad_sid like '%${ad_sid}%' 
		</if>
		<if test="ad_pos != null and ad_pos != ''">
			and ad_pos = #{ad_pos} 
		</if>
		<if test="ad_pos_type != null and ad_pos_type != ''">
			and ad_pos_type = #{ad_pos_type} 
		</if>
		
		group by tdate,pid
			<choose>
				<when test="group_postype != null and group_postype == 'ok'">
					,ad_pos_type 
				</when>
				<otherwise>
					,ad_sid 
				</otherwise>
			</choose>
		
		<if test="order_str != null and order_str != ''">
		 	order by ${order_str} 
		</if>
	</select>
	<select id="selectAdMsgTotalB" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgTotalVo">
		
		select 
			aa.*,bb.device_num,
			CONCAT(truncate(bb.device_num/aa.act_num*100,2),'%') as seep_ratio 
		from (select tdate,pid,
				<choose>
					<when test="group_game != null and group_game == 'ok'">
						ad_sid,game_type,
					</when>
					<when test="group_adpos != null and group_adpos == 'ok'">
						ad_pos,
					</when>
					<when test="group_postype != null and group_postype == 'ok'">
						ad_pos_type,
					</when>
					<otherwise>
						ad_sid,
					</otherwise>
				</choose>
				act_num,
				sum(req_num) as req_num,
				sum(success_num) as success_num,
				CONCAT(truncate(sum(success_num)/sum(req_num)*100,2),'%') as hold_ratio,
				sum(show_num) as show_num,
				sum(selfshow_num) as selfshow_num,
				sum(click_num) as click_num,
				CONCAT(truncate(sum(click_num)/sum(show_num)*100,2),'%') as click_ratio,
				CONCAT(truncate(sum(show_num)/sum(req_num)*100,2),'%') as show_ratio
			from admsg_total_${year} 
			where tdate BETWEEN #{start_date} AND #{end_date} 
			<if test="pid != null and pid != ''">
				and pid = #{pid} 
			</if>
			<if test="ad_sid != null and ad_sid != ''">
				and ad_sid like '%${ad_sid}%' 
			</if>
			<if test="ad_pos != null and ad_pos != ''">
				and ad_pos = #{ad_pos} 
			</if>
			<if test="game_type != null and game_type != ''">
				and game_type like concat('%',#{game_type},'%') 
			</if>
			<if test="ad_pos_type != null and ad_pos_type != ''">
				and ad_pos_type = #{ad_pos_type} 
			</if>
			
			<choose>
				<when test="group_game != null and group_game == 'ok'">
					group by tdate,pid,ad_sid,game_type
				</when>
				<when test="group_adpos != null and group_adpos == 'ok'">
					group by tdate,pid,ad_pos
				</when>
				<when test="group_postype != null and group_postype == 'ok'">
					group by tdate,pid,ad_pos_type
				</when>
				<otherwise>
					group by tdate,pid,ad_sid
				</otherwise>
			</choose>) aa
		
		<choose>
			<when test="group_game != null and group_game == 'ok'">
				left join 
					(SELECT tdate,pid,ad_sid,game_type,sum(device_num) as device_num 
						FROM admsg_total_game_type_group WHERE tdate BETWEEN #{start_date} AND #{end_date} 
						GROUP BY tdate,pid,ad_sid,game_type) bb
				on aa.tdate = bb.tdate and aa.pid = bb.pid and aa.ad_sid = bb.ad_sid and aa.game_type = bb.game_type
			</when>
			<when test="group_adpos != null and group_adpos == 'ok'">
				left join 
					(SELECT tdate,pid,ad_pos,sum(device_num) as device_num  
						FROM admsg_total_adpos_group WHERE tdate BETWEEN #{start_date} AND #{end_date} 
						GROUP BY tdate,pid,ad_pos) bb
				on aa.tdate = bb.tdate and aa.pid = bb.pid and aa.ad_pos = bb.ad_pos
			</when>
			<when test="group_postype != null and group_postype == 'ok'">
				left join 
					(SELECT tdate,pid,ad_pos_type,sum(device_num) as device_num 
						FROM admsg_total_adpos_type_group WHERE tdate BETWEEN #{start_date} AND #{end_date} 
						GROUP BY tdate,pid,ad_pos_type) bb
				on aa.tdate = bb.tdate and aa.pid = bb.pid and aa.ad_pos_type = bb.ad_pos_type
			</when>
			<otherwise>
				left join 
					(SELECT tdate,pid,ad_sid,sum(device_num) as device_num 
						FROM admsg_total_adsid_group WHERE tdate BETWEEN #{start_date} AND #{end_date} 
						GROUP BY tdate,pid,ad_sid) bb
				on aa.tdate = bb.tdate and aa.pid = bb.pid and aa.ad_sid = bb.ad_sid
			</otherwise>
		</choose>
	</select>
	
	<select id="selectAdMsgHourTask" parameterType="java.util.Map" 
			resultType="java.util.Map">
			
		SELECT SUM(aa.num) as req_num,
				COUNT(1) as count_num,
				bb.show_num,bb.click_num 
		FROM admsg_request_${tdate} aa
		
		left join 
		(SELECT 
			SUM(CASE WHEN ad_type = 1 THEN num ELSE 0 END) as show_num,
			SUM(CASE WHEN ad_type = 2 THEN num ELSE 0 END) as click_num
		FROM admsg_action_${tdate}) bb
		on 1=1
	</select>
	<insert id="insertAdMsgHourTask" parameterType="java.util.Map">
		insert into admsg_total_hour(tdate, adtype, hour${hour}) 
		values(#{tdate}, #{adtype}, #{num})
		
		ON DUPLICATE KEY UPDATE 
		hour${hour}=VALUES(hour${hour})
	</insert>
	
	<select id="selectAdMsgTotalHour" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdTotalHourTwoVo">
		
		select * from admsg_total_hour
		where tdate = #{tdate} 
		<if test="adtype != null and adtype != ''">
			and adtype = #{adtype} 
		</if>
	</select>
	
	<insert id="insertAdMsgHourTaskNew" parameterType="java.util.Map">
		insert into admsg_total_hour_new(
			tdate,
			adtype,
			pid,
			ad_pos_type,
			gname,
			channel,
			ver,
			hour${hour}
		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			'${adtype}',
			#{li.pid},
			#{li.ad_pos_type},
			#{li.gname},
			#{li.channel},
			#{li.ver},
			<if test="adtype != null and adtype == 1">
				#{li.show_num}
			</if>
			<if test="adtype != null and adtype == 2">
				#{li.click_num}
			</if>
			<if test="adtype != null and adtype == 3">
				#{li.req_num}
			</if>
			)
		</foreach>
		ON DUPLICATE KEY UPDATE 
		hour${hour}=VALUES(hour${hour})
	</insert>
	<sql id="hourArray">
		<foreach collection="array" item="it" separator=",">
			sum(${it}) as ${it}
		</foreach>
	</sql>
	<select id="selectAdMsgTotalHourNew" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdTotalHourTwoVo">
		
		select 
			tdate,adtype,ad_pos_type,
			<include refid="hourArray"/>
		from admsg_total_hour_new_two
		where tdate = #{tdate} and adtype = #{adtype} 
			and ad_pos_type = #{ad_pos_type}
			
		<if test="pid != null and pid != ''">
			and pid = #{pid} 
		</if>
		<if test="channel != null and channel != ''">
			and channel = #{channel} 
		</if>
		<if test="ver != null and ver != ''">
			and ver = #{ver} 
		</if>
	</select>
	
	
	<insert id="insertAdMsgAdsidInfo" parameterType="java.util.Map">
	
		INSERT INTO admsg_adsid_info(tdate,pid,ad_sid,req_num,selfshow_num)
		
		SELECT '${tdate}' tdate,aa.pid,aa.ad_sid,aa.req_num,cc.selfshow_num
		FROM 
			(SELECT pid,ad_sid,SUM(num) as req_num
				from admsg_request_${tdate} GROUP BY pid,ad_sid) aa
		
		left join 
			(SELECT pid,ad_sid,SUM(num) as selfshow_num
				from admsg_selfshow_${tdate} GROUP BY pid,ad_sid) cc
		on aa.pid = cc.pid and aa.ad_sid = cc.ad_sid
		
		ON DUPLICATE KEY UPDATE
		req_num = VALUES(req_num),
		selfshow_num = VALUES(selfshow_num)
	</insert>
	
	<!-- 广告源、游戏类型、广告位置、广告类型 分组设备数抽取 -->
	<insert id="insertAdMsgTotalAdsidGroup" parameterType="java.util.Map">
		INSERT INTO admsg_total_adsid_group
	
		SELECT 
			'${tdate}' as tdate,pid,ad_sid,
			SUM(num) AS show_num,
			COUNT(DISTINCT imei) AS device_num
		FROM admsg_action_${tdate}
		WHERE ad_type = 1
		GROUP BY pid,ad_sid
	
	</insert>
	<insert id="insertAdMsgTotalGameTypeGroup" parameterType="java.util.Map">
		INSERT INTO admsg_total_game_type_group
	
		SELECT 
			'${tdate}' as tdate,pid,ad_sid,game_type,
			SUM(num) AS show_num,
			COUNT(DISTINCT imei) AS device_num
		FROM admsg_action_${tdate}
		WHERE ad_type = 1
		GROUP BY pid,ad_sid,game_type
	
	</insert>
	<insert id="insertAdMsgTotalAdposGroup" parameterType="java.util.Map">
		INSERT INTO admsg_total_adpos_group
	
		SELECT 
			'${tdate}' as tdate,pid,ad_pos,
			SUM(num) AS show_num,
			COUNT(DISTINCT imei) AS device_num
		FROM admsg_action_${tdate}
		WHERE ad_type = 1
		GROUP BY pid,ad_pos
	
	</insert>
	<insert id="insertAdMsgTotalAdposTypeGroup" parameterType="java.util.Map">
		INSERT INTO admsg_total_adpos_type_group
	
		SELECT 
			'${tdate}' as tdate,pid,ad_pos_type,
			SUM(num) AS show_num,
			COUNT(DISTINCT imei) AS device_num
		FROM admsg_action_${tdate}
		WHERE ad_type = 1
		GROUP BY pid,ad_pos_type
	</insert>
	
	<!-- 广告数据运营  -->
	<insert id="insertAdmsgStatsOne" parameterType="java.util.List">
		insert into admsg_stats_total(
			tdate,
			appid,
			pid,
			act_num,
			add_num
		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.product_id},
			#{li.projectid},
			#{li.act_num},
			#{li.add_num})
		</foreach>
		ON DUPLICATE KEY UPDATE 
		pid=VALUES(pid)
	</insert>
	<select id="selectAdmsgStatsLogTwo" parameterType="java.lang.String" 
				resultType="com.wbgame.pojo.custom.AdMsgStatsTotalVo">
				
		select '${tdate}' as tdate,pid,
			(SUM(req_num)+SUM(success_num)) req_num,SUM(show_num) show_num,SUM(click_num) click_num
		from admsg_total_${year} where tdate = '${tdate}' group by pid
	</select>
	<update id="updateAdmsgStatsThree" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update admsg_stats_total 
			set req_num = #{li.req_num},
				show_num = #{li.show_num},
				click_num = #{li.click_num}
			where tdate = #{li.tdate} and pid = #{li.pid}
		</foreach>
	</update>
	<update id="updateAdmsgStatsFour" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update admsg_stats_total 
			set act_num = #{li.act_num},
				add_num = #{li.add_num}
			where tdate = #{li.tdate} and appid = #{li.appid}
		</foreach>
	</update>
	
	<select id="selectAdmsgStatsTotalInfoTwo" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgStatsTotalVo">
		
		select tdate,appid,
			act_num,
			add_num,
			SUM(req_num) as req_num,
			SUM(show_num) as show_num,
			SUM(click_num) as click_num
		from admsg_stats_total
		where tdate BETWEEN #{start_date} AND #{end_date} 
			<if test="appid != null and appid != ''">
				and appid in (${appid})
			</if>
		group by tdate,appid
		order by add_num desc
	</select>
	<select id="selectAdmsgStatsTotalInfoThree" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgStatsTotalVo">
		
		select aa.tdate, 
			SUM(aa.act_num) as act_num,
			SUM(aa.add_num) as add_num,
			SUM(aa.req_num) as req_num,
			SUM(aa.show_num) as show_num,
			SUM(aa.click_num) as click_num
		from (
			select 
				tdate,appid,act_num,add_num,
				SUM(req_num) as req_num,
				SUM(show_num) as show_num,
				SUM(click_num) as click_num
			from admsg_stats_total
			where tdate BETWEEN #{start_date} AND #{end_date} 
				<if test="appid != null and appid != ''">
					and appid in (${appid})
				</if>
			group by tdate,appid
		) aa 
		group by aa.tdate
		order by aa.tdate asc
	</select>
	
	<select id="selectAdmsgDetailByLog" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgDetailVo" flushCache="true">
		select xx.*,yy.req_num from 
			(select 
				'${tdate}' as tdate,aa.pid,aa.ad_pos_type,
				aa.show_num,aa.device_num,bb.click_num
			from 
				(SELECT pid,ad_pos_type,SUM(num) as show_num,COUNT(DISTINCT imei) as device_num 
				FROM admsg_selfshow_${tdate} 
				GROUP BY pid,ad_pos_type ) aa
			left join 
				(SELECT pid,ad_pos_type,SUM(num) as click_num FROM admsg_action_${tdate} 
				WHERE ad_type = 2 
				GROUP BY pid,ad_pos_type ) bb
			on aa.pid = bb.pid and aa.ad_pos_type = bb.ad_pos_type) xx
		join 
			(select pid,SUM(num) as req_num FROM admsg_request_${tdate} 
			 group by pid) yy
		on xx.pid = yy.pid
	</select>
	<select id="selectAdmsgDetailByLogPlanB" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgDetailVo">
		select 
			'${tdate}' as tdate,aa.pid,aa.ad_sid,aa.ad_pos_type,
			aa.show_num,aa.device_num,bb.click_num,cc.req_num
		from 
			(SELECT pid,ad_sid,ad_pos_type,SUM(num) as show_num,COUNT(DISTINCT imei) as device_num 
			FROM admsg_selfshow_${tdate} 
			GROUP BY pid,ad_sid,ad_pos_type ) aa
		left join 
			(SELECT pid,ad_sid,ad_pos_type,SUM(num) as click_num FROM admsg_action_${tdate} 
			WHERE ad_type = 2 
			GROUP BY pid,ad_sid,ad_pos_type ) bb
		on aa.pid = bb.pid and aa.ad_sid = bb.ad_sid and aa.ad_pos_type = bb.ad_pos_type 
			
		left join 
			(SELECT pid,ad_sid,ad_pos_type,SUM(num) as req_num FROM admsg_request_${tdate} 
			GROUP BY pid,ad_sid,ad_pos_type ) cc
		on aa.pid = cc.pid and aa.ad_sid = cc.ad_sid and aa.ad_pos_type = cc.ad_pos_type
	</select>
	<insert id="insertAdmsgDetailTwo" parameterType="java.util.List">
		insert into admsg_detail(
			tdate,
			pid,
			ad_pos_type,
			ad_sid,
			gname,
			channel,
			ver,
			act_num,
			add_num,
			req_num,
			show_num,
			click_num,
			device_num
		
		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_pos_type},
			#{li.ad_sid},
			#{li.gname},
			#{li.channel},
			#{li.ver},
			#{li.act_num},
			#{li.add_num},
			#{li.req_num},
			#{li.show_num},
			#{li.click_num},
			#{li.device_num})
		</foreach>
			
	</insert>
	<update id="updateAdmsgDetailThree" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update admsg_total_${li.year} set 
				act_num = #{li.act_num},
				add_num = #{li.add_num} 
			where tdate = #{li.tdate} and pid = #{li.projectid}
		</foreach>
	</update>
	<select id="selectAdmsgDetail" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgDetailVo">
				
	</select>
	<select id="selectAdmsgDetailGroup" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgDetailVo">
		
		select 
			<if test="group_adsid != 'ok' or group_adpos != 'ok'">
				bb.device_num,
			</if>
			aa.*,yy.act_num,yy.add_num,
			CONCAT(truncate(aa.click_num/aa.show_num*100,2),'%') as click_ratio,
			CONCAT(truncate(aa.show_num/aa.req_num*100,2),'%') as show_ratio,
			CONCAT(truncate(device_num/yy.act_num*100,2),'%') as seep_ratio 
		from (select 
				tdate,
				pid,
				ad_pos_type
				${group},
				(sum(req_num)+sum(success_num)) as req_num,
				sum(show_num) as show_num,
				sum(click_num) as click_num,
				sum(selfshow_num) as selfshow_num
				<if test="group_adsid == 'ok' and group_adpos == 'ok'">
					,sum(device_num) as device_num
				</if>
			from admsg_total_${year}
			where tdate BETWEEN #{sdate} AND #{edate} ${where}
			group by tdate,pid,ad_pos_type ${group} ) aa
			
		<choose>
			<when test="group_adsid != null and group_adsid == 'ok' and group_adpos != 'ok'">
				join 
					(SELECT tdate,pid,ad_pos_type,ad_sid,sum(device_num) as device_num 
						FROM admsg_total_adsid_group WHERE tdate BETWEEN #{sdate} AND #{edate} 
						GROUP BY tdate,pid,ad_pos_type,ad_sid) bb
				on aa.tdate = bb.tdate and aa.pid = bb.pid and aa.ad_pos_type = bb.ad_pos_type and aa.ad_sid = bb.ad_sid 
			</when>
			<when test="group_adpos != null and group_adpos == 'ok' and group_adsid != 'ok'">
				join 
					(SELECT tdate,pid,ad_pos_type,ad_pos,sum(device_num) as device_num  
						FROM admsg_total_adpos_group WHERE tdate BETWEEN #{sdate} AND #{edate} 
						GROUP BY tdate,pid,ad_pos_type,ad_pos) bb
				on aa.tdate = bb.tdate and aa.pid = bb.pid and aa.ad_pos_type = bb.ad_pos_type and aa.ad_pos = bb.ad_pos
			</when>
			<when test="group_adsid != 'ok' and group_adpos != 'ok'">
				join 
					(SELECT tdate,pid,ad_pos_type,sum(device_num) as device_num 
						FROM admsg_total_adpos_type_group WHERE tdate BETWEEN #{sdate} AND #{edate} 
						GROUP BY tdate,pid,ad_pos_type) bb
				on aa.tdate = bb.tdate and aa.pid = bb.pid and aa.ad_pos_type = bb.ad_pos_type
			</when>
		</choose>
		
		left join
			(select tdate,pid,act_num,add_num
			from admsg_total_${year}
			where tdate BETWEEN #{sdate} AND #{edate} ${where}
			group by tdate,pid) yy
		on aa.tdate = yy.tdate and aa.pid = yy.pid
			
	</select>
	
	<insert id="insertApkUserListHash" parameterType="java.util.Map">
		<!-- 忽略已有数据 INSERT IGNORE INTO -->
		INSERT INTO ${table_name} (
			product_id,
			imei,
			imsi,
			fromaddr,
			cityid,
			createtime
			
		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.product_id},
			#{li.imei},
			#{li.imsi},
			#{li.fromaddr},
			#{li.cityid},
			#{li.createtime})
			
		</foreach> 
		ON DUPLICATE KEY UPDATE
		imsi = VALUES(imsi),
		fromaddr = VALUES(fromaddr),
		cityid = VALUES(cityid)
	</insert>
	
	<!-- 广告加载转化率   -->
	<select id="selectAdMsgLoadByLog" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgLoadVo">
		select '${tdate}' as tdate,aa.pid,
			aa.switch_num,aa.switch_count,
			bb.get_num,bb.get_count,
			cc.load_num,cc.load_count,
			ee.error_switch_num,ee.error_switch_count,
			ff.error_get_num,ff.error_get_count,
			gg.error_load_num,gg.error_load_count
		from 
			(select pid,COUNT(DISTINCT imei) switch_num,SUM(num) switch_count from admsg_postpoint_${tdate}
			where post_type = 1 group by pid) aa
		LEFT JOIN
			(select pid,COUNT(DISTINCT imei) get_num,SUM(num) get_count from admsg_postpoint_${tdate}
			where post_type = 2 group by pid) bb
		ON aa.pid = bb.pid
		LEFT JOIN
			(select pid,COUNT(DISTINCT imei) load_num,SUM(num) load_count from admsg_postpoint_${tdate}
			where post_type = 3 group by pid) cc
		ON aa.pid = cc.pid
		
		<!-- 异常人数部分  -->
		LEFT JOIN
			(select xx.pid,COUNT(xx.imei) error_switch_num,SUM(xx.switch_count) error_switch_count 
			from 
				(select pid,imei,SUM(num) switch_count from admsg_postpoint_${tdate}
				where post_type = 1 group by pid,imei) xx
			where xx.switch_count >= 15 group by xx.pid) ee
		ON aa.pid = ee.pid
		LEFT JOIN
			(select yy.pid,COUNT(yy.imei) error_get_num,SUM(yy.get_count) error_get_count 
			from 
				(select pid,imei,SUM(num) get_count from admsg_postpoint_${tdate}
				where post_type = 2 group by pid,imei) yy
			where yy.get_count >= 15 group by yy.pid) ff
		ON aa.pid = ff.pid
		LEFT JOIN
			(select zz.pid,COUNT(zz.imei) error_load_num,SUM(zz.load_count) error_load_count 
			from 
				(select pid,imei,SUM(num) load_count from admsg_postpoint_${tdate}
				where post_type = 3 group by pid,imei) zz
			where zz.load_count >= 15 group by zz.pid) gg
		ON aa.pid = gg.pid
		
	</select>
	<insert id="insertAdMsgLoadList" parameterType="java.util.List">
	
		INSERT INTO admsg_load (
			tdate,
			pid,
			switch_num,
			act_num,
			req_num,
			get_num,
			load_num,
			switch_count,
			act_count,
			req_count,
			get_count,
			load_count,
			error_switch_num,
			error_act_num,
			error_req_num,
			error_get_num,
			error_load_num,
			error_switch_count,
			error_act_count,
			error_req_count,
			error_get_count,
			error_load_count
		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.switch_num},
			#{li.act_num},
			#{li.req_num},
			#{li.get_num},
			#{li.load_num},
			#{li.switch_count},
			#{li.act_count},
			#{li.req_count},
			#{li.get_count},
			#{li.load_count},
			#{li.error_switch_num},
			#{li.error_act_num},
			#{li.error_req_num},
			#{li.error_get_num},
			#{li.error_load_num},
			#{li.error_switch_count},
			#{li.error_act_count},
			#{li.error_req_count},
			#{li.error_get_count},
			#{li.error_load_count})
			
		</foreach> 
	</insert>
	<select id="selectAdMsgLoad" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgLoadVo">
		select 
			tdate,
			<if test="pid_group != null and pid_group != ''">
				pid,
			</if>
			sum(switch_num) switch_num,
			sum(act_num) act_num,
			sum(req_num) req_num,
			sum(get_num) get_num,
			sum(load_num) load_num,
			sum(switch_count) switch_count,
			sum(act_count) act_count,
			sum(req_count) req_count,
			sum(get_count) get_count,
			sum(load_count) load_count,
			sum(error_switch_num) error_switch_num,
			sum(error_act_num) error_act_num,
			sum(error_req_num) error_req_num,
			sum(error_get_num) error_get_num,
			sum(error_load_num) error_load_num,
			sum(error_switch_count) error_switch_count,
			sum(error_act_count) error_act_count,
			sum(error_req_count) error_req_count,
			sum(error_get_count) error_get_count,
			sum(error_load_count) error_load_count
		from admsg_load 
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="pid != null and pid != ''">
			and pid = #{pid} 
		</if>
		group by tdate
		<if test="pid_group != null and pid_group != ''">
			,pid
		</if>
		order by tdate asc,switch_num desc
	</select>
	
	<!-- 模块加载转化率  -->
	<select id="selectAdMsgModuleLoadByLog" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgLoadVo">
		select '${tdate}' as tdate,aa.pid,aa.join_num,aa.join_time,
			bb.finish_num,bb.finish_time,cc.enter_num,cc.enter_time,dd.switch_num,ee.switch2_num
		from 
			(select pid,COUNT(DISTINCT imei) as join_num,truncate(AVG(times),0) as join_time 
				from admsg_postconver_${tdate} where post_type = 1 group by pid) aa
		LEFT JOIN
			(select pid,COUNT(DISTINCT imei) as finish_num,truncate(AVG(times),0) as finish_time 
				from admsg_postconver_${tdate} where post_type = 2 group by pid) bb
		ON aa.pid = bb.pid
		LEFT JOIN
			(select pid,COUNT(DISTINCT imei) as enter_num,truncate(AVG(times),0) as enter_time 
				from admsg_postconver_${tdate} where post_type = 3 group by pid) cc
		ON aa.pid = cc.pid
		LEFT JOIN
			(select pid,COUNT(DISTINCT imei) as switch_num from admsg_postpoint_${tdate}
			where post_type = 1 group by pid) dd
		ON aa.pid = dd.pid
		LEFT JOIN
			(select pid,COUNT(DISTINCT imei) as switch2_num from admsg_postconver_${tdate} 
			where post_type = 0 group by pid) ee
		ON aa.pid = ee.pid
		
	</select>
	<insert id="insertAdMsgModuleLoadList" parameterType="java.util.List">
	
		INSERT INTO admsg_module_load (
			tdate,
			pid,
			join_num,
			join_time,
			finish_num,
			finish_time,
			enter_num,
			enter_time,
			switch_num,
			switch2_num

		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.join_num},
			#{li.join_time},
			#{li.finish_num},
			#{li.finish_time},
			#{li.enter_num},
			#{li.enter_time},
			#{li.switch_num},
			#{li.switch2_num})
			
		</foreach> 
	</insert>
	
	<!-- 新增广告加载转化率 -->
	<select id="selectAdMsgAddLoadByLog" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgLoadVo">
		
		<!-- select '${tdate}' as tdate,aa.pid, 新增统计方式
			aa.switch_num,bb.get_num,cc.load_num
		from 
			(select pid,COUNT(DISTINCT lsn) switch_num from admsg_postpoint_type1_total
			where create_date = #{tdate} group by pid) aa
		LEFT JOIN
			(select pid,COUNT(DISTINCT lsn) get_num from admsg_postpoint_type2_total
			where create_date = #{tdate} group by pid) bb
		ON aa.pid = bb.pid
		LEFT JOIN
			(select pid,COUNT(DISTINCT lsn) load_num from admsg_postpoint_type3_total
			where create_date = #{tdate} group by pid) cc
		ON aa.pid = cc.pid -->
		
		select '${tdate}' as tdate,pid,
			COUNT(DISTINCT CASE WHEN post_type = 1 THEN lsn END) as switch_num,
			COUNT(DISTINCT CASE WHEN post_type = 2 THEN lsn END) as get_num,
			COUNT(DISTINCT CASE WHEN post_type = 3 THEN lsn END) as load_num
		from admsg_postpoint_${tdate}
		group by pid
	</select>
	<insert id="insertAdMsgAddLoadList" parameterType="java.util.List">
	
		INSERT INTO admsg_add_load (
			tdate,
			pid,
			switch_num,
			act_num,
			req_num,
			get_num,
			load_num
		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.switch_num},
			#{li.act_num},
			#{li.req_num},
			#{li.get_num},
			#{li.load_num})
			
		</foreach> 
	</insert>
	<select id="selectAdMsgAddLoad" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgLoadVo">
		select 
			tdate,
			<if test="pid_group != null and pid_group != ''">
				pid,
			</if>
			sum(switch_num) switch_num,
			sum(act_num) act_num,
			sum(req_num) req_num,
			sum(get_num) get_num,
			sum(load_num) load_num
		from admsg_add_load 
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="pid != null and pid != ''">
			and pid = #{pid} 
		</if>
		group by tdate
		<if test="pid_group != null and pid_group != ''">
			,pid
		</if>
		order by tdate asc,switch_num desc
	</select>
	
	<!-- 查询填充  -->
	<insert id="selectAdMsgFilled" parameterType="java.util.Map">
		insert into admsg_filled_hour
		
		select 
			'${tdate}' as tdate,'${hour}' as hour,aa.ad_sid,
			aa.req_num, bb.success_num,
			CONCAT(truncate(bb.success_num/aa.req_num*100,2),'%') as hold_ratio,
			cc.show_num, dd.selfshow_num
		from 
				(select ad_sid,SUM(num) as req_num from admsg_request_${tdate} where 
					(<foreach collection="pids" item="pid" separator=" or ">
						pid = ${pid}
					</foreach>)
					and ad_sid = #{ad_sid}
				) aa
		join 
				(select ad_sid,SUM(num) as success_num from admsg_request_${tdate} where 
					(<foreach collection="pids" item="pid" separator=" or ">
						pid = ${pid}
					</foreach>)
					and ad_flag = 1 
					and ad_sid = #{ad_sid}
				) bb
		join
				(SELECT ad_sid,SUM(num) as show_num FROM admsg_action_${tdate} where 
					(<foreach collection="pids" item="pid" separator=" or ">
						pid = ${pid}
					</foreach>)
					and ad_type = 1 
					and ad_sid = #{ad_sid}
				) cc
		join
				(SELECT ad_sid,SUM(num) as selfshow_num FROM admsg_selfshow_${tdate} where 
					(<foreach collection="pids" item="pid" separator=" or ">
						pid = ${pid}
					</foreach>)
					and ad_sid = #{ad_sid}
				) dd
	</insert>
	
	
	<update id="updateRedPackFeeList" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update home_diamond_redpack_config set 
				dayFee = #{li.param2},
				totalFee = totalFee+#{li.param2}
	    	where appid = #{li.param1} 
    	</foreach>
	</update>
	
	<select id="selectHomeDiamond" parameterType="com.wbgame.pojo.custom.HomeDiamondVo"
		resultType="com.wbgame.pojo.custom.HomeDiamondVo">
		select * from home_diamond_log${tdate} 
		where 1=1 
		<if test="appid != null and appid != ''">
			and appid = #{appid} 
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid} 
		</if>
		<if test="lsn != null and lsn != ''">
			and lsn like concat('%',#{lsn},'%') 
		</if>
		<if test="imei != null and imei != ''">
			and imei = #{imei} 
		</if>
		order by coins desc
	</select>
	
	
	<insert id="insertReyunAdsidInfoList" parameterType="java.lang.String">
		insert into reyun_adsid_info(tdate,adplan_id,adcreative_id,ad_sid,show_num)
		
		SELECT '${tdate}' as tdate,aa.adplan_id,aa.adcreative_id,bb.ad_sid,SUM(bb.show_num) 
		from reyun_trackdata_temp aa
		LEFT JOIN admsg_adsid_info bb
		ON aa.imei = bb.imei where bb.tdate = '${tdate}'
		GROUP BY aa.adplan_id,aa.adcreative_id,bb.ad_sid
	</insert>
	
	<!-- 事件启动加载转化率  -->
	<insert id="insertAdmsgStartLoadReport" parameterType="java.lang.String">
	
		REPLACE INTO admsg_start_load
		SELECT '${tdate}',null,null,pid,
			COUNT(DISTINCT CASE WHEN eventId = 'app_start_show' THEN lsn END) as num0,
			COUNT(DISTINCT CASE WHEN eventId = 'app_agreement_show' THEN lsn END) as num1,
			COUNT(DISTINCT CASE WHEN eventId = 'app_user_agreement_click' THEN lsn END) as num2,
			COUNT(DISTINCT CASE WHEN eventId = 'app_privacy_agreement_click' THEN lsn END) as num3,
			COUNT(DISTINCT CASE WHEN eventId = 'app_agreement_agree_click' THEN lsn END) as num4,
			COUNT(DISTINCT CASE WHEN eventId = 'app_agreement_close_click' THEN lsn END) as num5,
			COUNT(DISTINCT CASE WHEN eventId = 'app_agreement_x_click' THEN lsn END) as num6,
			COUNT(DISTINCT CASE WHEN eventId = 'app_agreement_show_again' THEN lsn END) as num7,
			COUNT(DISTINCT CASE WHEN eventId = 'app_agreement_again_agree_click' THEN lsn END) as num8,
			COUNT(DISTINCT CASE WHEN eventId = 'app_agreement_again_close_click' THEN lsn END) as num9,
			COUNT(DISTINCT CASE WHEN eventId = 'app_agreement_again_x_click' THEN lsn END) as num10,
			COUNT(DISTINCT CASE WHEN eventId = 'app_reqpermission_show' THEN lsn END) as num11,
			COUNT(DISTINCT CASE WHEN eventId = 'app_reqpermission_over' THEN lsn END) as num12
		FROM `event_postpoint_log${tdate}` where appid in (38078,38083)
		GROUP BY pid
	</insert>
	<select id="selectAdmsgStartLoadReport" parameterType="java.util.Map" resultType="java.util.Map">
		select tdate,appid,cha_id,pid, 
			SUM(num0) num0,
			SUM(num1) num1,
			SUM(num2) num2,
			SUM(num3) num3,
			SUM(num4) num4,
			SUM(num5) num5,
			SUM(num6) num6,
			SUM(num7) num7,
			SUM(num8) num8,
			SUM(num9) num9,
			SUM(num10) num10,
			SUM(num11) num11,
			SUM(num12) num12
		from admsg_start_load
		where tdate BETWEEN #{sdate} AND #{edate} 
		<if test="appid != null and appid != ''">
			and appid = #{appid} 
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid} 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id = #{cha_id} 
		</if>
	</select>
	
	<!-- 广告源数据采集和查询  -->
	<select id="selectAdmsgAdsidList" parameterType="java.util.Map" 
						resultType="com.wbgame.pojo.custom.AdMsgTotalVo">
		select aa.*,
			truncate(show_num/act_num,2) as show_avg,
			CONCAT(truncate(success_num/req_num*100,2),'%') as hold_ratio,
			CONCAT(truncate(click_num/show_num*100,2),'%') as click_ratio,
			CONCAT(truncate(show_num/req_num*100,2),'%') as show_ratio,
			CONCAT(truncate(device_num/act_num*100,2),'%') as seep_ratio 
		from (select 
				tdate,
				SUBSTR(pid FROM 1 FOR 5) appid,
				pid,
				ad_sid,
				ad_pos_type,
				ad_pos,
				(sum(req_num)+sum(success_num)) as req_num,
				sum(success_num) as success_num,
				sum(show_num) as show_num,
				sum(click_num) as click_num,
				sum(selfshow_num) as selfshow_num,
				act_num,
				sum(device_num) as device_num
			from admsg_total_${year}
				where tdate BETWEEN #{tdate} AND #{tdate} 
			group by pid,ad_pos_type,ad_sid,ad_pos) aa
		<!-- WHERE req_num IS NOT NULL -->
		
	</select>
	
	<select id="selectAdmsgAdsidDetail" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgTotalVo">
		
		select 
			tdate,
			appid,
			gname
			${group},
			ad_pos_type,
			agent,
			sum(req_num) as req_num,
			sum(success_num) as success_num,
			sum(show_num) as show_num,
			sum(click_num) as click_num,
			sum(selfshow_num) as selfshow_num,
			sum(device_num) as device_num,
			CONCAT(truncate(sum(success_num)/sum(req_num)*100,2),'%') as hold_ratio,
			CONCAT(truncate(sum(click_num)/sum(show_num)*100,2),'%') as click_ratio,
			CONCAT(truncate(sum(show_num)/sum(req_num)*100,2),'%') as show_ratio,
			<choose>
				<when test="group != null and group == ',pid'">
					sum(act_num) act_num,
					truncate(sum(show_num)/sum(act_num),2) as show_avg,
					CONCAT(truncate(sum(device_num)/sum(act_num)*100,2),'%') as seep_ratio 
				</when>
				<otherwise>
					act_num,
					truncate(sum(show_num)/act_num,2) as show_avg,
					CONCAT(truncate(sum(device_num)/act_num*100,2),'%') as seep_ratio 
				</otherwise>
			</choose>
			
		from admsg_adsid_total 
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid} 
		</if>
		<if test="ad_sid != null and ad_sid != ''">
			and ad_sid like concat('%',#{ad_sid},'%') 
		</if>
		<if test="ad_pos != null and ad_pos != ''">
			and ad_pos = #{ad_pos} 
		</if>
		<if test="ad_pos_type != null and ad_pos_type != ''">
			and ad_pos_type in 
		<foreach collection="ad_pos_type" index="index" item="item" open="(" separator="," close=")">
		        #{item}
		    </foreach>
		</if>
		<if test="agent != null and agent != ''">
			and agent in 
			<foreach collection="agent" index="index" item="item" open="(" separator="," close=")">
		        #{item}
		    </foreach>
		</if>
		
		<if test="total == null or total == ''">
		 	group by tdate ${group} 
		</if>
		<if test="order_str != null and order_str != ''">
		 	order by ${order_str} 
		</if>
	</select>
	
	
	<select id="selectAdmsgAdsidListTwo" parameterType="java.util.Map" 
						resultType="com.wbgame.pojo.custom.AdMsgTotalVo">
		select aa.*,bb.device_num,
			truncate(show_num/act_num,2) as show_avg,
			CONCAT(truncate(success_num/req_num*100,2),'%') as hold_ratio,
			CONCAT(truncate(click_num/show_num*100,2),'%') as click_ratio,
			CONCAT(truncate(show_num/req_num*100,2),'%') as show_ratio,
			CONCAT(truncate(bb.device_num/act_num*100,2),'%') as seep_ratio 
		from (select 
				tdate,
				SUBSTR(pid FROM 1 FOR 5) appid,
				pid,
				ad_sid,
				ad_pos_type,
				(sum(req_num)+sum(success_num)) as req_num,
				sum(success_num) as success_num,
				sum(show_num) as show_num,
				sum(click_num) as click_num,
				sum(selfshow_num) as selfshow_num,
				act_num
			from admsg_total_${year}
				where tdate BETWEEN #{tdate} AND #{tdate} 
			group by pid,ad_sid,ad_pos_type) aa
			
		left join 
			(SELECT tdate,pid,ad_sid,ad_pos_type,sum(device_num) as device_num 
				FROM admsg_total_adsid_group WHERE tdate BETWEEN #{tdate} AND #{tdate} 
				GROUP BY tdate,pid,ad_sid,ad_pos_type) bb
		on aa.pid = bb.pid and aa.ad_sid = bb.ad_sid and aa.ad_pos_type = bb.ad_pos_type
		
	</select>
	
	<select id="selectAdmsgAdsidTotal" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AdMsgTotalVo">
		
		select 
			tdate,
			appid,
			gname
			${group},
			ad_pos_type,
			agent,
			cha_type,
			cha_media,
			cha_sub_launch,
			sum(ecpm) ecpm,
			sum(req_num) as req_num,
			sum(success_num) as success_num,
			sum(show_num) as show_num,
			sum(click_num) as click_num,
			sum(selfshow_num) as selfshow_num,
			sum(device_num) as device_num,
			CONCAT(truncate(sum(success_num)/sum(req_num)*100,2),'%') as hold_ratio,
			CONCAT(truncate(sum(click_num)/sum(show_num)*100,2),'%') as click_ratio,
			CONCAT(truncate(sum(show_num)/sum(req_num)*100,2),'%') as show_ratio,
			<choose>
				<when test="group != null and group == ',pid'">
					sum(act_num) act_num,
					truncate(sum(show_num)/sum(act_num),2) as show_avg,
					CONCAT(truncate(sum(device_num)/sum(act_num)*100,2),'%') as seep_ratio 
				</when>
				<otherwise>
					act_num,
					truncate(sum(show_num)/act_num,2) as show_avg,
					CONCAT(truncate(sum(device_num)/act_num*100,2),'%') as seep_ratio 
				</otherwise>
			</choose>
			
		from admsg_adsid_cha_total 
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="appid != null and appid != ''">
			and appid = #{appid} 
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid} 
		</if>
		<if test="ad_sid != null and ad_sid != ''">
			and ad_sid like concat('%',#{ad_sid},'%') 
		</if>
		<!-- <if test="ad_pos_type != null and ad_pos_type != ''">
			and ad_pos_type = #{ad_pos_type} 
		</if> -->
		<if test="ad_pos_type != null and ad_pos_type != ''">
			and ad_pos_type in 
			<foreach collection="ad_pos_type" index="index" item="item" open="(" separator="," close=")">
		        #{item}
		    </foreach>
		</if>
		
		<if test="agent != null and agent != ''">
			and agent = #{agent} 
		</if>
		<if test="cha_type != null and cha_type != ''">
			and cha_type = #{cha_type} 
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media = #{cha_media} 
		</if>
		<if test="cha_sub_launch != null and cha_sub_launch != ''">
			and cha_sub_launch = #{cha_sub_launch} 
		</if>
		
		<if test="total == null or total == ''">
		 	group by tdate ${group} 
		</if>
		<if test="order_str != null and order_str != ''">
		 	order by ${order_str} 
		</if>
	</select>
	
	<select id="selectApitjList" resultType="com.wbgame.pojo.ApiInfoVo">
		SELECT * FROM `api_tj_click_show` where t = date_add(curdate(), interval -1 day)
	</select>
	
	<insert id="insertUmengAdMsgTotalReqNum" parameterType="java.util.List">
	insert into umeng_admsg_total(
			`tdate`,
			`pid`,
			`ad_sid`,
			`ad_pos_type`,
			`ad_pos`,
			`req_num`
			) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.req_num}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE 
		req_num=VALUES(req_num)
	</insert>
	
	<insert id="insertUmengAdMsgSuccessNum" parameterType="java.util.List">
	insert into umeng_admsg_total(
			`tdate`,
			`pid`,
			`ad_sid`,
			`ad_pos_type`,
			`ad_pos`,
			`success_num`
			) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.success_num}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE 
		success_num=VALUES(success_num)
	</insert>
	
	<insert id="insertUmengAdMsgTotalClickNum" parameterType="java.util.List">
	insert into umeng_admsg_total(
			`tdate`,
			`pid`,
			`ad_sid`,
			`ad_pos_type`,
			`ad_pos`,
			`click_num`
			) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.click_num}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE 
		click_num=VALUES(click_num)
	</insert>
	
	<insert id="insertUmengAdMsgTotalShowNum" parameterType="java.util.List">
	insert into umeng_admsg_total(
			`tdate`,
			`pid`,
			`ad_sid`,
			`ad_pos_type`,
			`ad_pos`,
			`show_num`
			) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.show_num}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE 
		click_num=VALUES(show_num)
	</insert>
	
	<insert id="insertUmengAdMsgTotalDeviceNum" parameterType="java.util.List">
	insert into umeng_admsg_total(
			`tdate`,
			`pid`,
			`ad_sid`,
			`ad_pos_type`,
			`ad_pos`,
			`device_num`,
			`selfshow_num`
			) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.device_num},
			#{li.selfshow_num}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE 
		device_num=VALUES(device_num),
		selfshow_num=VALUES(selfshow_num)
	</insert>
	
	<update id="insertUmengAdMsgTotalNum" parameterType="java.util.List">
	<foreach collection="list" item="item" index="index" open="" close="" separator=";">
        update umeng_admsg_total
        <set>
            add_num=${item.add_num},
            act_num=${item.act_num}
        </set>
        where pid = ${item.pid} and tdate = #{item.tdate}
    </foreach>     
	</update>
	
	<delete id="delUmengAdMsgTotal" parameterType="String">
        delete from umeng_admsg_total
        where   tdate = #{tdate}
	</delete>

    <select id="selectAdMsgTotalUmeng" resultType="com.wbgame.pojo.custom.AdMsgTotalVo">
		select
		tdate,
		pid,
		<choose>
			<when test="group_postype != null and group_postype == 'ok'">
				ad_pos_type,
			</when>
			<otherwise>
				ad_sid,
			</otherwise>
		</choose>
		(sum(req_num)+sum(success_num)) as req_num,
		sum(success_num) as success_num,
		CONCAT(truncate(sum(success_num)/(sum(req_num)+sum(success_num))*100,2),'%') as hold_ratio,
		sum(show_num) as show_num,
		sum(selfshow_num) as selfshow_num,
		sum(click_num) as click_num,
		CONCAT(truncate(sum(click_num)/sum(show_num)*100,2),'%') as click_ratio,
		CONCAT(truncate(sum(show_num)/(sum(req_num)+sum(success_num))*100,2),'%') as show_ratio,
		act_num,
		sum(device_num) as device_num,
		CONCAT(truncate(sum(device_num)/act_num*100,2),'%') as seep_ratio

		from umeng_admsg_total2
		where tdate BETWEEN #{start_date} AND #{end_date}
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="ad_sid != null and ad_sid != ''">
			and ad_sid like '%${ad_sid}%'
		</if>
		<if test="ad_pos != null and ad_pos != ''">
			and ad_pos = #{ad_pos}
		</if>
		<if test="ad_pos_type != null and ad_pos_type != ''">
			and ad_pos_type = #{ad_pos_type}
		</if>

		group by tdate,pid
		<choose>
			<when test="group_postype != null and group_postype == 'ok'">
				,ad_pos_type
			</when>
			<otherwise>
				,ad_sid
			</otherwise>
		</choose>

		<if test="order_str != null and order_str != ''">
			order by ${order_str}
		</if>
	</select>
	
	
	<insert id="insertUmengAdMsgTotalReqNum2" parameterType="java.util.List">
	insert into umeng_admsg_total2(
			`tdate`,
			`pid`,
			`ad_sid`,
			`ad_pos_type`,
			`ad_pos`,
			`req_num`
			,app_channel
			) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.req_num}
			,#{li.app_channel}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE 
		req_num=VALUES(req_num)
	</insert>
	
	<insert id="insertUmengAdMsgSuccessNum2" parameterType="java.util.List">
	insert into umeng_admsg_total2(
			`tdate`,
			`pid`,
			`ad_sid`,
			`ad_pos_type`,
			`ad_pos`,
			`success_num`
			,app_channel
			) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.success_num}
			,#{li.app_channel}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE 
		success_num=VALUES(success_num)
	</insert>
	
	<insert id="insertUmengAdMsgTotalClickNum2" parameterType="java.util.List">
	insert into umeng_admsg_total2(
			`tdate`,
			`pid`,
			`ad_sid`,
			`ad_pos_type`,
			`ad_pos`,
			`click_num`
			,app_channel
			) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.click_num}
			,#{li.app_channel}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE 
		click_num=VALUES(click_num)
	</insert>
	
	<insert id="insertUmengAdMsgTotalShowNum2" parameterType="java.util.List">
	insert into umeng_admsg_total2(
			`tdate`,
			`pid`,
			`ad_sid`,
			`ad_pos_type`,
			`ad_pos`,
			`show_num`
			,app_channel
			) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.show_num}
			,#{li.app_channel}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE 
		click_num=VALUES(show_num)
	</insert>
	
	<insert id="insertUmengAdMsgTotalDeviceNum2" parameterType="java.util.List">
	insert into umeng_admsg_total2(
			`tdate`,
			`pid`,
			`ad_sid`,
			`ad_pos_type`,
			`ad_pos`,
			`device_num`
			,app_channel			
			) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.device_num}
			,#{li.app_channel}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE 
		device_num=VALUES(device_num),
		selfshow_num=VALUES(selfshow_num)
	</insert>
	
	<update id="insertUmengAdMsgTotalNum2" parameterType="java.util.List">
	<foreach collection="list" item="item" index="index" open="" close="" separator=";">
        update umeng_admsg_total2
        <set>
            add_num=${item.add_num},
            act_num=${item.act_num}
        </set>
        where pid = ${item.pid} and tdate = #{item.tdate}
    </foreach>     
	</update>
	
	<delete id="delUmengAdMsgTotal2" parameterType="String">
        delete from umeng_admsg_total2
        where   tdate = #{tdate}
	</delete>

	<select id="selectStatisticProject" resultType="com.wbgame.pojo.mobile.StatisticProjectVo">
		select id,gname from adsid_game_list
		<where>1=1
			<if test="gname != null and gname != ''">
				and gname = #{gname}
			</if>
		</where>
	</select>

	<insert id="insertStatisticProject">
		insert into adsid_game_list  (gname) values (#{gname});
	</insert>
	<delete id="deleteStatisticProject">
		delete from adsid_game_list where id =#{id} limit 1
	</delete>

	<select id="selectDnRevenueActuser" parameterType="java.lang.String" resultType="com.wbgame.pojo.usertag.UsertagActUserVo">
		SELECT adsid,chaid cha_id,
			SUM(CASE WHEN num = 1 THEN num ELSE 0 END) pv1,
			SUM(CASE WHEN num = 2 THEN num ELSE 0 END) pv2,
			SUM(CASE WHEN num = 3 THEN num ELSE 0 END) pv3,
			SUM(CASE WHEN num = 4 THEN num ELSE 0 END) pv4,
			SUM(CASE WHEN num = 5 THEN num ELSE 0 END) pv5,
			SUM(CASE WHEN num = 6 THEN num ELSE 0 END) pv6,
			SUM(CASE WHEN num = 7 THEN num ELSE 0 END) pv7,
			SUM(CASE WHEN num = 8 THEN num ELSE 0 END) pv8,
			SUM(CASE WHEN num = 9 THEN num ELSE 0 END) pv9,
			SUM(CASE WHEN num = 10 THEN num ELSE 0 END) pv10,
			SUM(CASE WHEN num >= 11 and num &lt;= 15 THEN num ELSE 0 END) pv11,
			SUM(CASE WHEN num >= 16 and num &lt;= 20 THEN num ELSE 0 END) pv12,
			SUM(CASE WHEN num >= 21 and num &lt;= 25 THEN num ELSE 0 END) pv13,
			SUM(CASE WHEN num > 25 THEN num ELSE 0 END) pv14
			
		FROM admsg_v2_adsidgroup_${tdate} where num &lt;= 1000000 GROUP BY adsid,chaid

	</select>

    <insert id="batchInsertDnapp">
		insert into app_info(
		id,
		channel_id,
		app_id,
		app_name,
		create_time,
		sync_umeng,
		umeng_key,
		app_category,
		os_type
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.id},
			#{li.channel_id},
			#{li.app_id},
			#{li.app_name},
			#{li.create_time},
			#{li.sync_umeng},
			#{li.umeng_key},
			#{li.app_category},
			#{li.os_type}
			)
		</foreach>
	</insert>

	<select id="selectHbWithDrawConfig" resultType="com.wbgame.pojo.RusConfigInfo" parameterType="com.wbgame.pojo.RusConfigInfo" >
		select
		pid,type,amount,`desc`,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createStr,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') updateStr
		,create_owner,update_owner,is_examine,day_num,total_num from hb_withdraw_config
		where 1=1
		<if test="pid != null and pid != ''" >
			and pid = #{pid,jdbcType=VARCHAR}
		</if>
		<if test="type != null and type != ''" >
			and type = #{type,jdbcType=INTEGER}
		</if>
	</select>
	<delete id="deleteHbWithDrawConfig" parameterType="com.wbgame.pojo.RusConfigInfo" >
		delete from hb_withdraw_config
		where pid = #{pid,jdbcType=VARCHAR}
		and type = #{type,jdbcType=INTEGER}
	</delete>
	<insert id="insertHbWithDrawConfig" parameterType="com.wbgame.pojo.RusConfigInfo" >
		insert into hb_withdraw_config (pid, type, amount,
		`desc`,create_owner,update_owner,create_time,update_time,is_examine,day_num,total_num)
		values (#{pid,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL},
		#{desc,jdbcType=VARCHAR},#{create_owner},#{update_owner},now(),now(),#{is_examine},#{day_num},#{total_num})
	</insert>

	<update id="updateHbWithDrawConfig" parameterType="com.wbgame.pojo.RusConfigInfo" >
		update hb_withdraw_config
		set
		amount = #{amount},
		day_num = #{day_num},
		total_num = #{total_num},
		<if test="update_owner != null and update_owner != ''">
			update_owner = #{update_owner},
		</if>
		<if test="desc != null and desc != ''">
			`desc` = #{desc,jdbcType=VARCHAR},
		</if>
		is_examine = #{is_examine},
		update_time = now()
		where pid = #{pid,jdbcType=VARCHAR}
		and type = #{type,jdbcType=INTEGER}
	</update>

	<insert id="addHbWithDrawConfigList" parameterType="java.util.List">
		insert into hb_withdraw_config (pid, type, amount, `desc`,create_owner,update_owner,is_examine,day_num,total_num,create_time,update_time)
		values
		<foreach collection="list" item="lpc" separator=",">
			(#{lpc.pid},
			#{lpc.type},
			#{lpc.amount},
			#{lpc.desc},
			#{lpc.create_owner,jdbcType=VARCHAR},
			#{lpc.update_owner,jdbcType=VARCHAR},
			#{lpc.is_examine},
			#{lpc.day_num},
			#{lpc.total_num},
			now(),
			now())
		</foreach>

	</insert>





	<!--友盟应用数据抽取-->
	<delete id="delUmengAppEventTotal" parameterType="String">
        delete from umeng_admsg_app_total
        where   tdate = #{tdate}
	</delete>

	<insert id="insertUmengAppEventTotalReqNum" parameterType="java.util.List">
		insert into umeng_admsg_app_total(
		`tdate`,
		`pid`,
		`ad_sid`,
		`ad_pos_type`,
		`ad_pos`,
		`req_num`
		,app_channel
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.req_num}
			,#{li.app_channel}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE
		req_num=VALUES(req_num)
	</insert>

	<insert id="insertUmengAppEventTotalSuccessNum" parameterType="java.util.List">
		insert into umeng_admsg_app_total(
		`tdate`,
		`pid`,
		`ad_sid`,
		`ad_pos_type`,
		`ad_pos`,
		`success_num`
		,app_channel
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.success_num}
			,#{li.app_channel}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE
		success_num=VALUES(success_num)
	</insert>

	<insert id="insertUmengAppEventTotalClickNum" parameterType="java.util.List">
		insert into umeng_admsg_app_total(
		`tdate`,
		`pid`,
		`ad_sid`,
		`ad_pos_type`,
		`ad_pos`,
		`click_num`
		,app_channel
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.click_num}
			,#{li.app_channel}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE
		click_num=VALUES(click_num)
	</insert>

	<insert id="insertUmengAppEventTotalShowNum" parameterType="java.util.List">
		insert into umeng_admsg_app_total(
		`tdate`,
		`pid`,
		`ad_sid`,
		`ad_pos_type`,
		`ad_pos`,
		`show_num`
		,app_channel
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.show_num}
			,#{li.app_channel}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE
		click_num=VALUES(show_num)
	</insert>

	<insert id="insertUmengAppEventTotalDeviceNum" parameterType="java.util.List">
		insert into umeng_admsg_app_total(
		`tdate`,
		`pid`,
		`ad_sid`,
		`ad_pos_type`,
		`ad_pos`,
		`device_num`
		,app_channel
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.ad_sid},
			#{li.ad_pos_type},
			#{li.ad_pos},
			#{li.device_num}
			,#{li.app_channel}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE
		device_num=VALUES(device_num),
		selfshow_num=VALUES(selfshow_num)
	</insert>

	<update id="insertUmengAppEventTotalNum" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" open="" close="" separator=";">
			update umeng_admsg_app_total
			<set>
				add_num=${item.add_num},
				act_num=${item.act_num}
			</set>
			where pid = ${item.pid} and tdate = #{item.tdate}
		</foreach>
	</update>




</mapper>