<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.slave2.HbIssueValueConfigMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.HbIssueValueConfig">
    <!--@mbg.generated-->
    <!--@Table hb_issue_value_config-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="pid" jdbcType="VARCHAR" property="pid" />
    <result column="min_total" jdbcType="INTEGER" property="minTotal" />
    <result column="max_total" jdbcType="INTEGER" property="maxTotal" />
    <result column="min_ecpm" jdbcType="INTEGER" property="minEcpm" />
    <result column="max_ecpm" jdbcType="INTEGER" property="maxEcpm" />
    <result column="min_value" jdbcType="VARCHAR" property="minValue" />
    <result column="max_value" jdbcType="VARCHAR" property="maxValue" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_owner" jdbcType="VARCHAR" property="createOwner" />
    <result column="update_owner" jdbcType="VARCHAR" property="updateOwner" />
    <result column="new_total" jdbcType="INTEGER" property="newTotal" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, pid, min_total, max_total, min_ecpm, max_ecpm, min_value, max_value, `type`, 
    create_time, update_time, create_owner, update_owner, new_total
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hb_issue_value_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from hb_issue_value_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.wbgame.pojo.HbIssueValueConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hb_issue_value_config (pid, min_total, max_total, 
      min_ecpm, max_ecpm, min_value, 
      max_value, `type`, create_time, 
      update_time, create_owner, update_owner, 
      new_total)
    values (#{pid,jdbcType=VARCHAR}, #{minTotal,jdbcType=INTEGER}, #{maxTotal,jdbcType=INTEGER}, 
      #{minEcpm,jdbcType=INTEGER}, #{maxEcpm,jdbcType=INTEGER}, #{minValue,jdbcType=VARCHAR}, 
      #{maxValue,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createOwner,jdbcType=VARCHAR}, #{updateOwner,jdbcType=VARCHAR}, 
      #{newTotal,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.wbgame.pojo.HbIssueValueConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hb_issue_value_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pid != null">
        pid,
      </if>
      <if test="minTotal != null">
        min_total,
      </if>
      <if test="maxTotal != null">
        max_total,
      </if>
      <if test="minEcpm != null">
        min_ecpm,
      </if>
      <if test="maxEcpm != null">
        max_ecpm,
      </if>
      <if test="minValue != null">
        min_value,
      </if>
      <if test="maxValue != null">
        max_value,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createOwner != null">
        create_owner,
      </if>
      <if test="updateOwner != null">
        update_owner,
      </if>
      <if test="newTotal != null">
        new_total,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pid != null">
        #{pid,jdbcType=VARCHAR},
      </if>
      <if test="minTotal != null">
        #{minTotal,jdbcType=INTEGER},
      </if>
      <if test="maxTotal != null">
        #{maxTotal,jdbcType=INTEGER},
      </if>
      <if test="minEcpm != null">
        #{minEcpm,jdbcType=INTEGER},
      </if>
      <if test="maxEcpm != null">
        #{maxEcpm,jdbcType=INTEGER},
      </if>
      <if test="minValue != null">
        #{minValue,jdbcType=VARCHAR},
      </if>
      <if test="maxValue != null">
        #{maxValue,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwner != null">
        #{createOwner,jdbcType=VARCHAR},
      </if>
      <if test="updateOwner != null">
        #{updateOwner,jdbcType=VARCHAR},
      </if>
      <if test="newTotal != null">
        #{newTotal,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.HbIssueValueConfig">
    <!--@mbg.generated-->
    update hb_issue_value_config
    <set>
      <if test="pid != null">
        pid = #{pid,jdbcType=VARCHAR},
      </if>
      <if test="minTotal != null">
        min_total = #{minTotal,jdbcType=INTEGER},
      </if>
      <if test="maxTotal != null">
        max_total = #{maxTotal,jdbcType=INTEGER},
      </if>
      <if test="minEcpm != null">
        min_ecpm = #{minEcpm,jdbcType=INTEGER},
      </if>
      <if test="maxEcpm != null">
        max_ecpm = #{maxEcpm,jdbcType=INTEGER},
      </if>
      <if test="minValue != null">
        min_value = #{minValue,jdbcType=VARCHAR},
      </if>
      <if test="maxValue != null">
        max_value = #{maxValue,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwner != null">
        create_owner = #{createOwner,jdbcType=VARCHAR},
      </if>
      <if test="updateOwner != null">
        update_owner = #{updateOwner,jdbcType=VARCHAR},
      </if>
      <if test="newTotal != null">
        new_total = #{newTotal,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.HbIssueValueConfig">
    <!--@mbg.generated-->
    update hb_issue_value_config
    set pid = #{pid,jdbcType=VARCHAR},
      min_total = #{minTotal,jdbcType=INTEGER},
      max_total = #{maxTotal,jdbcType=INTEGER},
      min_ecpm = #{minEcpm,jdbcType=INTEGER},
      max_ecpm = #{maxEcpm,jdbcType=INTEGER},
      min_value = #{minValue,jdbcType=VARCHAR},
      max_value = #{maxValue,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_owner = #{createOwner,jdbcType=VARCHAR},
      update_owner = #{updateOwner,jdbcType=VARCHAR},
      new_total = #{newTotal,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-05-24-->
  <select id="selectByAll" resultMap="BaseResultMap">
        select
    id, pid, min_total, max_total, min_ecpm, max_ecpm, min_value,
    max_value, create_owner, update_owner, new_total,type,
    DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createStr,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') updateStr
        from hb_issue_value_config
        <where>
            <if test="pid != null and pid != ''">
                and pid=#{pid,jdbcType=VARCHAR}
            </if>
          <if test="minTotal != null">
            and min_total=#{minTotal,jdbcType=VARCHAR}
          </if>
          <if test="maxTotal != null">
            and max_total=#{maxTotal,jdbcType=VARCHAR}
          </if>

        </where>
    </select>

    <select id="selectByPid" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from hb_issue_value_config
      where pid = #{pid,jdbcType=INTEGER}
    </select>

  <insert id="batchInsertHbIssueValueConfigs">
    insert into hb_issue_value_config (pid, min_total, max_total,
      min_ecpm, max_ecpm, min_value,
      max_value, `type`, create_time,
      update_time, create_owner, update_owner,
      new_total) values
      <foreach collection="list" item="it" separator=",">
        (#{it.pid,jdbcType=VARCHAR}, #{it.minTotal,jdbcType=INTEGER}, #{it.maxTotal,jdbcType=INTEGER},
        #{it.minEcpm,jdbcType=INTEGER}, #{it.maxEcpm,jdbcType=INTEGER}, #{it.minValue,jdbcType=VARCHAR},
        #{it.maxValue,jdbcType=VARCHAR}, #{it.type,jdbcType=INTEGER}, #{it.createTime,jdbcType=TIMESTAMP},
        #{it.updateTime,jdbcType=TIMESTAMP}, #{it.createOwner,jdbcType=VARCHAR}, #{it.updateOwner,jdbcType=VARCHAR},
        #{it.newTotal,jdbcType=INTEGER})
      </foreach>
  </insert>
</mapper>