<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave2.YdSlave2Mapper">
  <!--删除参数配置 -->
  <delete id="deleteRedpackAct" parameterType="java.lang.Integer" >
    delete from home_diamond_redpack_act
    where pid = #{pid,jdbcType=INTEGER}
  </delete>
  
  <!--添加参数配置 -->
  <insert id="insertRedpackAct" parameterType="com.wbgame.pojo.RedpackActInfo" >
    insert into home_diamond_redpack_act (pid, totalMoney, auditScore, 
      sendMoney)
    values (#{pid,jdbcType=INTEGER}, #{totalMoney,jdbcType=INTEGER}, #{auditScore,jdbcType=INTEGER}, 
      #{sendMoney,jdbcType=DECIMAL})
  </insert>
 
 <!--查询参数配置 -->
  <select id="selectRedpackAct" resultType="com.wbgame.pojo.RedpackActInfo"  >
    select 
    pid, totalMoney, auditScore,sendMoney
    from home_diamond_redpack_act
    where 1=1
    <if test="pid != null and pid != ''">
	 and pid = #{pid,jdbcType=INTEGER}
	</if>
  </select>
  
  <!--修改参数配置 -->
  <update id="updateRedpackAct" parameterType="com.wbgame.pojo.RedpackActInfo" >
    update home_diamond_redpack_act
    <set >
      <if test="totalMoney != null" >
        totalMoney = #{totalMoney,jdbcType=INTEGER},
      </if>
      <if test="auditScore != null" >
        auditScore = #{auditScore,jdbcType=INTEGER},
      </if>
      <if test="sendMoney != null" >
        sendMoney = #{sendMoney,jdbcType=DECIMAL},
      </if>
    </set>
    where pid = #{pid,jdbcType=INTEGER}
  </update>
  
  <!-- 红包产品参数配置  删除--> 
  <delete id="deleteRedpackConfig" parameterType="java.lang.Integer" >
    delete from home_diamond_redpack_config
    where appid = #{appid,jdbcType=INTEGER}
  </delete>
  
  <!-- 红包产品参数配置  添加--> 
  <insert id="insertRedpackConfig" parameterType="com.wbgame.pojo.RedpackConfigInfo" >
    insert into home_diamond_redpack_config (appid, appname, `maxvalue`, 
      depositFee, totalFee, dayFee, descUrl,boundsValue,videoNums,balanceNums
      )
    values (#{appid,jdbcType=INTEGER}, #{appName,jdbcType=VARCHAR}, #{maxValue,jdbcType=INTEGER}, 
      #{depositFee,jdbcType=DECIMAL}, #{totalFee,jdbcType=DECIMAL}, #{dayFee,jdbcType=DECIMAL}, #{descUrl,jdbcType=VARCHAR}, #{boundsValue,jdbcType=INTEGER},
      #{videoNums,jdbcType=INTEGER}, #{balanceNums,jdbcType=INTEGER})
  </insert>
  
  <!-- 红包产品参数配置  查询--> 
  <select id="selectRedpackConfig" resultType="com.wbgame.pojo.RedpackConfigInfo" >
    select 
   appid, appname AS appName, `maxvalue` AS `maxValue`, depositFee, totalFee, dayFee,descUrl,boundsValue,videoNums,balanceNums
    from home_diamond_redpack_config a left join app_info b on a.appid = b.id
    where 1=1
    <if test="appid != null and appid != ''">
	 and appid = #{appid,jdbcType=INTEGER}
	</if>
	<if test="appName != null and appName != ''">
	 and appname = #{appName,jdbcType=VARCHAR}
	</if>
	<if test="appids != null and appids != ''">
		and appid in (${appids})
	</if>
      <if test="appCategory != null and appCategory != ''">
          and b.app_category = #{appCategory}
      </if>
	ORDER BY totalFee desc
  </select>
  
  <!-- 红包产品参数配置  修改--> 
  <update id="updateRedpackConfig" parameterType="com.wbgame.pojo.RedpackConfigInfo" >
    update home_diamond_redpack_config
    <set >
      <if test="appName != null" >
        appname = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="maxValue != null" >
        `maxvalue` = #{maxValue,jdbcType=INTEGER},
      </if>
      <if test="depositFee != null" >
        depositFee = #{depositFee,jdbcType=DECIMAL},
      </if>
      <if test="totalFee != null" >
        totalFee = #{totalFee,jdbcType=DECIMAL},
      </if>
      <if test="dayFee != null" >
        dayFee = #{dayFee,jdbcType=DECIMAL},
      </if>
      <if test="descUrl != null" >
        descUrl = #{descUrl,jdbcType=DECIMAL},
      </if>
      <if test="boundsValue != null" >
        boundsValue = #{boundsValue,jdbcType=INTEGER},
      </if>
      <if test="videoNums != null" >
        videoNums = #{videoNums,jdbcType=INTEGER},
      </if>
      <if test="balanceNums != null" >
        balanceNums = #{balanceNums,jdbcType=INTEGER},
      </if>
    </set>
    where appid = #{appid,jdbcType=INTEGER}
  </update>
 	
  <!--批量导入  -->
  <insert id="batchInsertWithdraw" parameterType="java.util.List" >
    insert into home_diamond_redpack_withdraw (appid, pid, lsn, 
      imei, openid, accessToken, 
      amount, createtime) values 
      <foreach collection="list"  index="index" item="item" separator=",">
		(#{item.appid}, #{item.pid}, #{item.lsn}, #{item.imei}, #{item.openid}, #{item.accessToken}, 
      #{item.amount}, NOW()) 
	  </foreach>
  </insert>
  
  <!--查询 红包大额活动配置  -->
  <select id="selectRedpackdrawConfig" resultType="com.wbgame.pojo.RedpackdrawConfigInfo">
    select 
    pid, type, amount, `desc` AS `desc`, total, boundNum
    from home_diamond_redpackdraw_cfg
    where 1=1
    <if test="pid != null and pid != ''">
	 and pid = #{pid,jdbcType=INTEGER}
	</if>
	<if test="type != null and type != ''">
	 and type = #{type,jdbcType=INTEGER}
	</if>
	ORDER BY total desc
  </select>
  <!-- 删除 红包大额活动配置 -->
  <delete id="deleteRedpackdrawConfig" parameterType="com.wbgame.pojo.RedpackdrawConfigInfo" >
    delete from home_diamond_redpackdraw_cfg
    where pid = #{pid,jdbcType=VARCHAR} and type = #{type}
  </delete>
  <!-- 添加 红包大额活动配置 -->
  <insert id="insertRedpackdrawConfig" parameterType="com.wbgame.pojo.RedpackdrawConfigInfo" >
    insert into home_diamond_redpackdraw_cfg (pid, type, amount, 
      `desc`, total, boundNum
      )
    values (#{pid,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, 
      #{desc,jdbcType=VARCHAR}, #{total,jdbcType=INTEGER}, #{boundNum,jdbcType=INTEGER}
      )
  </insert>
  <!-- 修改 红包大额活动配置 -->
  <update id="updateRedpackdrawConfig" parameterType="com.wbgame.pojo.RedpackdrawConfigInfo" >
    update home_diamond_redpackdraw_cfg
    <set >
      <if test="desc != null" >
        `desc` = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="total != null" >
        total = #{total,jdbcType=INTEGER},
      </if>
      <if test="boundNum != null" >
        boundNum = #{boundNum,jdbcType=INTEGER},
      </if>
    </set>
    where pid = #{pid,jdbcType=VARCHAR} and type = #{type}
  </update>
  
  <select id="selectWithdrawDetails" resultType="com.wbgame.pojo.RedPackConfig"  >
  	SELECT
	    DATE_FORMAT( createtime, '%Y%m%d' ) cDate,
	    <if test="hide != 1">
	    pid cPid,
	    </if>
	    <if test="hide == 1">
	    appid cAppid,
	    </if>
		amount cPrice,
		ROUND( sum( amount ), 2 ) cTotal,
		count( DISTINCT imei ) cUsers 
	FROM
		home_diamond_redpackdraw_withdraw 
	WHERE
      DATE_FORMAT( createtime, '%Y%m%d' ) = #{startTime}
		<if test="hide != 1">
	    and pid = #{cPid}
	    </if>
	    <if test="hide == 1">
	    and appid = #{cAppid} 
	    </if>
	GROUP BY
		DATE_FORMAT( createtime, '%Y%m%d' ),
		<if test="hide != 1">
	    pid ,
	    </if>
	    <if test="hide == 1">
	    appid ,
	    </if>
		amount UNION ALL
	SELECT
		DATE_FORMAT( createtime, '%Y%m%d' ) cDate,
		<if test="hide != 1">
	     pid cPid,
	    </if>
	    <if test="hide == 1">
	    appid cAppid,
	    </if>
		amount cPrice,
		ROUND( sum( amount ), 2 ) cTotal,
		count( DISTINCT imei ) cUsers 
	FROM
		home_diamond_redpack_withdraw 
	WHERE
		DATE_FORMAT( createtime, '%Y%m%d' ) = #{startTime} 
		<if test="hide != 1">
	    and pid = #{cPid}
	    </if>
	    <if test="hide == 1">
	    and appid = #{cAppid}
	    </if>
	GROUP BY
		DATE_FORMAT( createtime, '%Y%m%d' ),
		<if test="hide != 1">
	    pid ,
	    </if>
	    <if test="hide == 1">
	    appid ,
	    </if>
		amount
    </select>
    
    <!-- 红包产品提现  查询--> 
  <select id="selectRedpackdrawVo" resultType="com.wbgame.pojo.DiamondWithdrawInfo" >
	SELECT
		appid,pid,lsn,imei,openid,amount,type,createtime AS createTime
	FROM
		home_diamond_redpackdraw_withdraw 
	WHERE 1=1
	<if test="pid != null and pid != ''">
		and pid = #{pid}
	</if>
	<if test="appid != null and appid != ''">
		and appid = #{appid}
	</if>
	<if test = "startTime != null and endTime != null">
		and DATE_FORMAT(createtime,'%Y-%m-%d') BETWEEN #{startTime} AND #{endTime}
	</if>
	<if test="lsn != null and lsn != ''">
		and lsn = #{lsn}
	</if>
	<if test="imei != null and imei != ''">
		and imei = #{imei}
	</if>
	UNION ALL
	SELECT
		appid,pid,lsn,imei,openid,amount,1 AS type,createtime AS createTime
	FROM
		home_diamond_redpack_withdraw 
	WHERE
		1=1
	<if test="pid != null and pid != ''">
		and pid = #{pid}
	</if>
	<if test="appid != null and appid != ''">
		and appid = #{appid}
	</if>
	<if test = "startTime != null and endTime != null">
		and DATE_FORMAT(createtime,'%Y-%m-%d') BETWEEN #{startTime} AND #{endTime}
	</if>
	<if test="lsn != null and lsn != ''">
		and lsn = #{lsn}
	</if>
	<if test="imei != null and imei != ''">
		and imei = #{imei}
	</if>
    <if test="order_str != null and order_str != ''">
        order by ${order_str}
    </if>
  </select>
  
   <!--  红包提现参数配置 查询 -->
   <select id="selectRedpackWxconfig" resultType="com.wbgame.pojo.RedpackWxconfigInfo" parameterType="com.wbgame.pojo.RedpackWxconfigInfo" >
    select 
    appid,pkg,mchid,wxAppid,mchKey,mchPath,`passwd`,`mchName`,
           `wishing`,`actName`,`iconId`,`bannerId`,`minValue`,
           `maxValue`,`notifyUrl`,if(mode = 2,'api提现','证书提现') modeName,mode,
           create_time createTime, update_time updateTime, create_user createUser, update_user updateUser
    from home_diamond_redpack_wxconfig
    where 1=1 
    <if test="appid != null and appid != ''">
		and appid = #{appid}
	</if>
	<if test="pkg != null and pkg != ''">
		and pkg = #{pkg}
	</if>
    <if test="mchid != null and mchid != ''">
           and mchid = #{mchid}
    </if>
  </select>
  
   <!--  红包提现参数配置 删除 -->
  <delete id="deleteRedpackWxconfig" parameterType="com.wbgame.pojo.RedpackWxconfigInfo" >
    delete from home_diamond_redpack_wxconfig
    where appid = #{appid,jdbcType=INTEGER}
      and pkg = #{pkg,jdbcType=VARCHAR}
  </delete>
  
   <!--  红包提现参数配置 添加 -->
  <insert id="insertRedpackWxconfig" parameterType="com.wbgame.pojo.RedpackWxconfigInfo" >
    insert into home_diamond_redpack_wxconfig (appid, pkg, wxAppid, 
      mchid, mchKey, mchPath,`passwd`,`mchName`,`wishing`,`actName`,
      `iconId`,`bannerId`,`minValue`,`maxValue`,`notifyUrl`,mode,create_user
      )
    values (#{appid,jdbcType=INTEGER}, #{pkg,jdbcType=VARCHAR}, #{wxAppid,jdbcType=VARCHAR}, 
      #{mchid,jdbcType=INTEGER}, #{mchKey,jdbcType=VARCHAR}, #{mchPath,jdbcType=VARCHAR},
      #{passwd,jdbcType=VARCHAR}, #{mchName,jdbcType=VARCHAR}, #{wishing,jdbcType=VARCHAR}, #{actName,jdbcType=VARCHAR}
      , #{iconId,jdbcType=VARCHAR}, #{bannerId,jdbcType=VARCHAR}, #{minValue,jdbcType=VARCHAR}, #{maxValue,jdbcType=VARCHAR}
      , #{notifyUrl,jdbcType=VARCHAR},#{mode},#{createUser}
      )
  </insert>
  
  <!--  红包提现参数配置 修改 -->
   <update id="updateRedpackWxconfig" parameterType="com.wbgame.pojo.RedpackWxconfigInfo" >
    update home_diamond_redpack_wxconfig
    <set >
      <if test="wxAppid != null" >
        wxAppid = #{wxAppid,jdbcType=VARCHAR},
      </if>
      <if test="mchid != null" >
        mchid = #{mchid,jdbcType=INTEGER},
      </if>
      <if test="mchKey != null" >
        mchKey = #{mchKey,jdbcType=VARCHAR},
      </if>
      <if test="mchPath != null" >
        mchPath = #{mchPath,jdbcType=VARCHAR},
      </if>
      <if test="passwd != null" >
        passwd = #{passwd,jdbcType=VARCHAR},
      </if>
      <if test="mchName != null" >
        mchName = #{mchName,jdbcType=VARCHAR},
      </if>
      <if test="wishing != null" >
        wishing = #{wishing,jdbcType=VARCHAR},
      </if>
      <if test="actName != null" >
        actName = #{actName,jdbcType=VARCHAR},
      </if>
      <if test="iconId != null" >
        iconId = #{iconId,jdbcType=VARCHAR},
      </if>
      <if test="bannerId != null" >
        bannerId = #{bannerId,jdbcType=VARCHAR},
      </if>
      <if test="minValue != null" >
       `minValue` = #{minValue,jdbcType=VARCHAR},
      </if>
      <if test="maxValue != null" >
       `maxValue` = #{maxValue,jdbcType=VARCHAR},
      </if>
      <if test="notifyUrl != null" >
        notifyUrl = #{notifyUrl,jdbcType=VARCHAR},
      </if>
        <if test="mode != null" >
            mode = #{mode,jdbcType=VARCHAR},
        </if>
        <if test="updateUser != null" >
            update_user = #{updateUser,jdbcType=VARCHAR},
        </if>
       update_time = now()
    </set>
    where appid = #{appid,jdbcType=INTEGER}
      and pkg = #{pkg,jdbcType=VARCHAR}
  </update>
  
    <!-- 查询前一天的红包产品明细数据 -->
    <select id="selectWithdrawDetailsSlave2" resultType="com.wbgame.pojo.RedPackConfig"  >
		SELECT
		DATE( createtime ) cDate,
		appid cAppid,
		pid cPid,
		CAST(amount as decimal(8,2)) cPrice,
		count( DISTINCT imei ) cUsers,
		ROUND( sum( amount ), 2 ) cTotal 
	FROM
		home_diamond_redpack_withdraw 
	WHERE
		1 = 1 
		AND	DATE( createtime ) = date_sub(curdate(),interval 1 day)
		AND pid NOT IN ( '333351', '333359' ) 
	GROUP BY
		DATE( createtime ),
		appid,
		pid,
		CAST(amount as decimal(8,2)) UNION ALL
	SELECT
		DATE( createtime ) cDate,
		appid cAppid,
		pid cPid,
		CAST(amount as decimal(8,2)) cPrice,
		count( DISTINCT imei ) cUsers,
		ROUND( sum( amount ), 2 ) cTotal 
	FROM
		home_diamond_redpackdraw_withdraw 
	WHERE
		1 = 1 
		AND	DATE( createtime ) = date_sub(curdate(),interval 1 day) 
		AND pid NOT IN ( '333351', '333359' ) 
	GROUP BY
		DATE( createtime ),
		appid,
		pid,
		CAST(amount as decimal(8,2))
  </select>
  
  <select id="selectWithdrawDetailsSlave2ForQQ" 
  					resultType="com.wbgame.pojo.RedPackConfig">
  		select 
			DATE(createtime) cDate,
			appid cAppid,
			pid cPid,
			amount cPrice,
			count(DISTINCT openid) cUsers,
			ROUND(sum( amount ), 2) cTotal  
		from home_diamond_qqdraw_withdraw
		
		where DATE(createtime) = date_sub(curdate(),interval 1 day)
		group by appid,pid,amount
		
		union all
		
		select DATE_FORMAT(a.time_end,'%Y-%m-%d') as c_date,b.appid as c_appid,
			b.pkg as c_pid,ROUND(a.total_fee/100,2) as c_price,count(a.openid) as c_users,ROUND(sum(a.total_fee)/100,2) as c_total 
		from home_diamond_qqh5_withdraw a,home_diamond_redpack_wxconfig b where a.appid = b.wxAppid
			and DATE_FORMAT(a.time_end,'%Y-%m-%d') = date_sub(curdate(),interval 1 day)
		group by DATE_FORMAT(a.time_end,'%Y-%m-%d'),b.appid,b.pkg,ROUND(a.total_fee/100,2)
		
  </select>
  <select id="selectWithdrawRealTimeForQQ" resultType="java.util.Map">
  					
  		select aa.*,bb.c_overUser,bb.c_withdraw FROM
		(select DATE_FORMAT(a.time_end,'%Y-%m-%d') as c_date,
				b.appid as c_appid,
					b.pkg as c_pid,
				ROUND(a.total_fee/100,2) as c_price,
				count(a.openid) as c_users,
				ROUND(sum(a.total_fee)/100,2) as c_total 
			from home_diamond_qqh5_withdraw a,home_diamond_redpack_wxconfig b 
			where a.appid = b.wxAppid
			and DATE_FORMAT(a.time_end,'%Y%m%d') = '${tdate}'
			and a.total_fee = 3
			group by DATE_FORMAT(a.time_end,'%Y-%m-%d'),b.appid,b.pkg,ROUND(a.total_fee/100,2)) aa
		LEFT JOIN
		
		(select appid,pid,
				COUNT(DISTINCT CASE WHEN eventId = 'reachGoal' THEN imei END) c_overUser,
				COUNT(DISTINCT CASE WHEN eventId = 'redpackwithdraw' THEN imei END) c_withdraw
			from event_postpoint_log${tdate} where pid not in ('333311','333351','333320')
			group by appid,pid) bb
		on aa.c_appid = bb.appid and aa.c_pid = bb.pid
		
  </select>
  
    <!--删除参数配置 -->
  <delete id="deleteWbPayConfig" parameterType="com.wbgame.pojo.WbPayConfig" >
    delete from wb_pay_config
    where pid = #{pid,jdbcType=INTEGER}
    	AND cha = #{cha,jdbcType=INTEGER}
    	AND appid = #{appid,jdbcType=INTEGER}
  </delete>
  
  <!--添加参数配置 -->
  <insert id="insertWbPayConfig" parameterType="com.wbgame.pojo.WbPayConfig" >
    insert into wb_pay_config (appid, pid, cha, 
      pkg, appname, vivo_param1, 
      vivo_param2, vivo_param3, alipay_param1, 
      alipay_param4, alipay_param2, alipay_param3
      )
    values (#{appid,jdbcType=VARCHAR}, #{pid,jdbcType=VARCHAR}, #{cha,jdbcType=VARCHAR}, 
      #{pkg,jdbcType=VARCHAR}, #{appname,jdbcType=VARCHAR}, #{vivo_param1,jdbcType=VARCHAR}, 
      #{vivo_param2,jdbcType=VARCHAR}, #{vivo_param3,jdbcType=VARCHAR}, #{alipay_param1,jdbcType=VARCHAR}, 
      #{alipay_param4,jdbcType=VARCHAR}, #{alipay_param2,jdbcType=LONGVARCHAR}, #{alipay_param3,jdbcType=LONGVARCHAR}
      )
  </insert>
 
 <!--查询参数配置 -->
  <select id="selectWbPayConfig" resultType="com.wbgame.pojo.WbPayConfig"  >
    select 
    *
    from wb_pay_config
    where 1=1
    <if test="pid != null and pid != ''">
	 and pid = #{pid,jdbcType=INTEGER}
	</if>
	 <if test="appid != null and appid != ''">
	 and appid = #{appid,jdbcType=INTEGER}
	</if>
	 <if test="cha != null and cha != ''">
	 and cha = #{cha,jdbcType=INTEGER}
	</if>
  </select>
  
  <!--修改参数配置 -->
  <update id="updateWbPayConfig" parameterType="com.wbgame.pojo.WbPayConfig" >
    update wb_pay_config
    set pkg = #{pkg,jdbcType=VARCHAR},
      appname = #{appname,jdbcType=VARCHAR},
      vivo_param1 = #{vivo_param1,jdbcType=VARCHAR},
      vivo_param2 = #{vivo_param2,jdbcType=VARCHAR},
      vivo_param3 = #{vivo_param3,jdbcType=VARCHAR},
      alipay_param1 = #{alipay_param1,jdbcType=VARCHAR},
      alipay_param2 = #{alipay_param2,jdbcType=VARCHAR},
      alipay_param3 = #{alipay_param3,jdbcType=VARCHAR},
      alipay_param4 = #{alipay_param4,jdbcType=VARCHAR}
    where appid = #{appid,jdbcType=VARCHAR}
      and pid = #{pid,jdbcType=VARCHAR}
      and cha = #{cha,jdbcType=VARCHAR}
  </update>
  
  <select id="selectApitj" resultType="com.wbgame.pojo.adv2.ApiClickVo">
		SELECT `id`, `t`,
		<if test="group_pid != null and group_pid != ''">
			pid,SUBSTR(pid, 1, 5 ) AS appid,dau,
		</if>
		`source`,cha_id,cha_type_name,
		SUM(clicks) clicks,
		SUM(shows) shows,
		c.pkgs,sourceUrl,
		SUBSTRING_INDEX(SUBSTRING_INDEX(`id`,'_',2),'_',-1) sdktype,
		CONCAT(ROUND(SUM(clicks)/SUM(shows)*100, 1),'%') rate
		FROM `api_tj_click_show`  a LEFT JOIN api_source_md5 b ON a.source = b.sourceMd5  LEFT JOIN api_code_pkgs c ON a.id = c.code
		where 1=1
		<if test="startTime != null and endTime != null">
			and t BETWEEN #{startTime} AND #{endTime}
		</if>
		<if test="appid != null and appid != ''">
			and SUBSTR(pid, 1, 5 ) in (${appid})
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid} 
		</if>
		<if test="id != null and id != ''">
			and id like concat('%',#{id},'%') 
		</if>
		<if test="sdktype != null and sdktype != ''">
			and SUBSTRING_INDEX(SUBSTRING_INDEX(id,'_',2),'_',-1) = #{sdktype}
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name}) 
		</if>
		
		group by t,id
		<if test="group_pid != null and group_pid != ''">
			,pid 
		</if>
	</select>

    <select id="selectHbUserWithdrawLogSum" resultType="com.wbgame.pojo.RedPackConfig">
        SELECT
		DATE( create_time ) cDate,
		appid cAppid,
		pid cPid,
		amount cPrice,
		count( DISTINCT userid ) cUsers,
		ROUND( sum( amount ), 2 ) cTotal
	FROM
		hb_user_withdraw_log
	WHERE
		1 = 1
		AND	DATE( create_time ) = date_sub(curdate(),interval 1 day)
	GROUP BY
		DATE( create_time ),
		appid,
		pid,
		amount
    </select>
    
	<select id="selectCoinsWithdrawConfig" resultType="com.wbgame.pojo.clean.coins.CoinsWithdrawConfig">
	    select 
	   		*
	    from coins_withdraw_config
	    where  1=1 
	     <if test="appid != null and appid != ''">
			 and  appid = #{appid,jdbcType=VARCHAR}
		</if>
		<if test="prjid != null and prjid != ''">
			 and  prjid = #{prjid,jdbcType=VARCHAR}
		</if>
		<if test="cha != null and cha != ''">
			 and  cha = #{cha,jdbcType=VARCHAR}
		</if>
		<if test="status != null and status != ''">
			 and  status = #{status,jdbcType=VARCHAR}
		</if>
	</select>
	<delete id="deleteCoinsWithdrawConfig" parameterType="com.wbgame.pojo.clean.coins.CoinsWithdrawConfig">
    delete from coins_withdraw_config
    where id = #{id}
  </delete>
   <insert id="insertCoinsWithdrawConfig" parameterType="com.wbgame.pojo.clean.coins.CoinsWithdrawConfig">
    insert into coins_withdraw_config (appid, cha, prjid, coins,money,
      times, 
      createTime, modifyTime, createUser, 
      modifyUser, status
      )
    values (#{appid,jdbcType=VARCHAR}, #{cha,jdbcType=VARCHAR}, #{prjid,jdbcType=VARCHAR}, #{coins,jdbcType=VARCHAR}, #{money,jdbcType=VARCHAR}, 
      #{times,jdbcType=VARCHAR},
       now(),now(),#{createUser,jdbcType=VARCHAR},
        #{modifyUser,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateCoinsWithdrawConfig" parameterType="com.wbgame.pojo.clean.coins.CoinsWithdrawConfig">
    update coins_withdraw_config
     <set >
      <if test="coins != null and coins != ''" >
        coins = #{coins,jdbcType=VARCHAR},
      </if>
     <if test="money != null and money != ''" >
        money = #{money,jdbcType=VARCHAR},
      </if>
       <if test="times != null and times != ''" >
        times = #{times,jdbcType=VARCHAR},
      </if>
       <if test="status != null and status != ''" >
        status = #{status,jdbcType=VARCHAR},
      </if>
       <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      modifyTime = now()
      </set>
    where id in (${id}) 
  </update>
  
  
	<select id="selectCoinsConfig" resultType="com.wbgame.pojo.clean.coins.CoinsConfig">
	    select 
	   		*
	    from super_coins_cfg
	    where  1=1 
	     <if test="appid != null and appid != ''">
			 and  appid = #{appid,jdbcType=VARCHAR}
		</if>
		<if test="status != null and status != ''">
			 and  status = #{status,jdbcType=VARCHAR}
		</if>
	</select>
	<delete id="deleteCoinsConfig" parameterType="com.wbgame.pojo.clean.coins.CoinsConfig">
    delete from super_coins_cfg
    where appid = #{appid}
  </delete>
   <insert id="insertCoinsConfig" parameterType="com.wbgame.pojo.clean.coins.CoinsConfig">
    insert into super_coins_cfg (appid, newPack, loginDaily, bonus,bubble,stepReward,
      turntable, turntableExtra,ggl,answer,wallpaper,extraBag,openPack,luckyStar,news,video,chat,goldenEgg,watch,
      createTime, modifyTime, createUser, 
      modifyUser, status,welfare
      )
    values (#{appid,jdbcType=VARCHAR}, #{newPack,jdbcType=VARCHAR}, #{loginDaily,jdbcType=VARCHAR}, #{bonus,jdbcType=VARCHAR}, #{bubble,jdbcType=VARCHAR}, #{stepReward,jdbcType=VARCHAR}, 
      #{turntable,jdbcType=VARCHAR},#{turntableExtra,jdbcType=VARCHAR},#{ggl,jdbcType=VARCHAR},#{answer,jdbcType=VARCHAR},#{wallpaper,jdbcType=VARCHAR},
      #{extraBag,jdbcType=VARCHAR},#{openPack,jdbcType=VARCHAR},#{luckyStar,jdbcType=VARCHAR},#{news,jdbcType=VARCHAR},#{video,jdbcType=VARCHAR},#{chat,jdbcType=VARCHAR},#{goldenEgg,jdbcType=VARCHAR},#{watch,jdbcType=VARCHAR},
       now(),now(),#{createUser,jdbcType=VARCHAR},
        #{modifyUser,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{welfare,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateCoinsConfig" parameterType="com.wbgame.pojo.clean.coins.CoinsConfig">
    update super_coins_cfg
     <set >
      <if test="newPack != null and newPack != ''" >
        newPack = #{newPack,jdbcType=VARCHAR},
      </if>
      <if test="loginDaily != null and loginDaily != ''" >
        loginDaily = #{loginDaily,jdbcType=VARCHAR},
      </if>
       <if test="bonus != null and bonus != ''" >
        bonus = #{bonus,jdbcType=VARCHAR},
      </if>
       <if test="bubble != null and bubble != ''" >
        bubble = #{bubble,jdbcType=VARCHAR},
      </if>
      <if test="turntable != null and turntable != ''" >
        turntable = #{turntable,jdbcType=VARCHAR},
      </if>
      <if test="turntableExtra != null and turntableExtra != ''" >
        turntableExtra = #{turntableExtra,jdbcType=VARCHAR},
      </if>
       <if test="ggl != null and ggl != ''" >
        ggl = #{ggl,jdbcType=VARCHAR},
      </if>
       <if test="answer != null and answer != ''" >
        answer = #{answer,jdbcType=VARCHAR},
      </if>
      <if test="wallpaper != null and wallpaper != ''" >
        wallpaper = #{wallpaper,jdbcType=VARCHAR},
      </if>
      <if test="extraBag != null and extraBag != ''" >
        extraBag = #{extraBag,jdbcType=VARCHAR},
      </if>
       <if test="openPack != null and openPack != ''" >
        openPack = #{openPack,jdbcType=VARCHAR},
      </if>
       <if test="luckyStar != null and luckyStar != ''" >
        luckyStar = #{luckyStar,jdbcType=VARCHAR},
      </if>
      <if test="news != null and news != ''" >
        news = #{news,jdbcType=VARCHAR},
      </if>
      <if test="video != null and video != ''" >
        video = #{video,jdbcType=VARCHAR},
      </if>
       <if test="chat != null and chat != ''" >
        chat = #{chat,jdbcType=VARCHAR},
      </if>
       <if test="goldenEgg != null and goldenEgg != ''" >
        goldenEgg = #{goldenEgg,jdbcType=VARCHAR},
      </if>
       <if test="watch != null and watch != ''" >
        watch = #{watch,jdbcType=VARCHAR},
      </if>
      <if test="welfare != null and welfare != ''" >
        welfare = #{welfare,jdbcType=VARCHAR},
      </if>
       <if test="modifyUser != null and modifyUser != ''" >
        modifyUser = #{modifyUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      modifyTime = now()
      </set>
    where appid  = #{appid,jdbcType=VARCHAR}
  </update>
	
</mapper>