<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adv2.AdvCommonAdCodeMapper">
    <select id="getAdCodeAccountList" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo" resultType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        select  * from  adv_adcode_account_info where  1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="tappid != null and tappid != ''">
            and tappid = #{tappid}
        </if>
        <if test="channel != null and channel != ''">
            and channel = #{channel}
        </if>
        <if test="note != null and note != ''">
            and note like '%{note}%'
        </if>
        <if test="platform != null and platform != ''">
            and platform in (${platform})
        </if>

    </select>

    <select id="getCSJAdCodeAccountList" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo" resultType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        select  * from  adv_adcode_account_info where  (channel = 'csj' or channel = 'zhubao')
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="tappid != null and tappid != ''">
            and tappid = #{tappid}
        </if>
        <if test="note != null and note != ''">
            and note like '%{note}%'
        </if>
        <if test="platform != null and platform != ''">
            and platform = (${platform})
        </if>
    </select>

    <select id="getAdCodeAccountByMix" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo" resultType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        select  * from  adv_adcode_account_info where  1=1
                                                  and appid = #{appid} and tappid =#{tappid} and taccountid =#{taccountid} and platform =#{platform}
    </select>

    <insert id="addAdCodeAccount" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        insert into adv_adcode_account_info (appid,tappid,taccountid,tsecrect,platform,platformName,note,createUser,createTime, channel)
        values (#{appid},#{tappid},#{taccountid},#{tsecrect},#{platform},#{platformName},#{note},#{createUser},now(), #{channel})
            ON DUPLICATE KEY UPDATE
                                 modifyUser=VALUES(modifyUser),
                                 modifyTime=now()
    </insert>
    <insert id="batchAddAdCodeAccount" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        insert into adv_adcode_account_info (appid,tappid,taccountid,tsecrect,platform,platformName,note,createUser,createTime, channel)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.appid},#{li.tappid},#{li.taccountid},#{li.tsecrect},#{li.platform},#{li.platformName},#{li.note},#{li.createUser},now(),#{li.channel})
        </foreach>
            ON DUPLICATE KEY UPDATE
            modifyUser=VALUES(modifyUser),
            modifyTime=now()
    </insert>

    <update id="updateAdCodeAccount" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        update adv_adcode_account_info set appid = #{appid},tappid = #{tappid},
                                           taccountid = #{taccountid},tsecrect = #{tsecrect},platform = #{platform},
                                           platformName = #{platformName},note = #{note},modifyUser =#{modifyUser},modifyTime =now(), channel = #{channel}
        where id = #{id}
    </update>

    <delete id="delAdCodeAccount" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo" >
        delete from adv_adcode_account_info where id =#{id}
    </delete>


    <select id="getHeadlineAdCodeList" parameterType="com.wbgame.pojo.adv2.CSJAdcodeVo" resultType="com.wbgame.pojo.adv2.CSJAdcodeVo">
        select * from  adv_adcode_headline_info where  1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="ad_slot_type != null and ad_slot_type != ''">
            and ad_slot_type =#{ad_slot_type}
        </if>
        <if test="ad_slot_id != null and ad_slot_id != ''">
            and ad_slot_id = #{ad_slot_id}
        </if>
        <if test="ad_slot_name != null and ad_slot_name != ''">
            and ad_slot_name like  concat('%',#{ad_slot_name},'%')
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        order by createTime desc
    </select>

    <select id="getYLHAdCodeList" parameterType="com.wbgame.pojo.adv2.YLHAdcodeVo" resultType="com.wbgame.pojo.adv2.YLHAdcodeVo">
        select * from  adv_adcode_ylh_info where  1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="scene != null and scene != ''">
            and scene = #{scene}
        </if>
        <if test="placement_id != null and placement_id != ''">
            and placement_id = #{placement_id}
        </if>
        <if test="placement_name != null and placement_name != ''">
            and placement_name like  concat('%',#{placement_name},'%')
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        order by createTime desc
    </select>

    <select id="getKSAdCodeList" parameterType="com.wbgame.pojo.adv2.KSAdcodeVo" resultType="com.wbgame.pojo.adv2.KSAdcodeVo">
        select * from  adv_adcode_ks_info where  1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="adStyle != null and adStyle != ''">
            and adStyle = #{adStyle}
        </if>
        <if test="positionId != null and positionId != ''">
            and positionId = #{positionId}
        </if>
        <if test="name != null and name != ''">
            and `name` like  concat('%',#{name},'%')
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="priceStrategy != null and priceStrategy != ''">
            and priceStrategy = #{priceStrategy}
        </if>
        order by createTime desc
    </select>

    <insert id="saveHeadlineAdCode" parameterType="com.wbgame.pojo.adv2.CSJAdcodeVo" >
        insert into adv_adcode_headline_info (
            ad_slot_id,appid,channel,adExtensionName,remark,user_id,role_id,app_id,ad_slot_type,sdk_ad_type,open_type,
            ad_slot_name,use_mediation,cpm,render_type,bidding_type,
            ad_categories,tpl_list,accept_material_type,slide_banner,
            width,height,use_icon,orientation,reward_name,reward_count,
            reward_is_callback,reward_callback_url,ad_rollout_size,skip_duration,use_endcard,strategy,createUser,createTime,params,create_record)
        values (
                   #{ad_slot_id},#{appid},#{channel},#{adExtensionName},#{remark},#{user_id},#{role_id},#{app_id},#{ad_slot_type},#{sdk_ad_type},#{open_type},
                   #{ad_slot_name},#{use_mediation},#{cpm},#{render_type},#{bidding_type},
                   #{ad_categories},#{tpl_list},#{accept_material_type},#{slide_banner},
                   #{width},#{height},#{use_icon},#{orientation},#{reward_name},#{reward_count},
                   #{reward_is_callback},#{reward_callback_url},#{ad_rollout_size},#{skip_duration},#{use_endcard},#{strategy},#{createUser},now(),#{params},#{create_record})
    </insert>

    <insert id="saveKSAdCode" parameterType="com.wbgame.pojo.adv2.KSAdcodeVo">
        insert into adv_adcode_ks_info (positionId,appid,channel,adExtensionName,remark,app_id,`name`,adStyle,renderType,materialTypeList, multiTemplateParams,
                                        templateId,rewardedType,rewardedNum,callbackStatus,callbackUrl,sdk_ad_type,cpm,open_type,bidding_type,
                                        skipAdMode,countdownShow,voice,strategy,priceStrategy,createUser,createTime,params,create_record)
        values (
                   #{positionId},#{appid},#{channel},#{adExtensionName},#{remark},#{app_id},#{name},#{adStyle},#{renderType},#{materialTypeList},#{multiTemplateParams},
                   #{templateId},#{rewardedType},#{rewardedNum},#{callbackStatus},#{callbackUrl},#{sdk_ad_type},#{cpm},#{open_type},#{bidding_type},
                   #{skipAdMode},#{countdownShow},#{voice},#{strategy},#{priceStrategy},#{createUser},now(),#{params},#{create_record}
               )
    </insert>

    <insert id="saveYLHAdCode" parameterType="com.wbgame.pojo.adv2.YLHAdcodeVo">
        insert into adv_adcode_ylh_info(placement_id,member_id,appid,channel,adExtensionName,remark,app_id,placement_name,
                                        scene,rewarded_video_scene,rewarded_video_description,ad_pull_mode,render_type,sdk_ad_type,open_type,
                                        ad_crt_type_list,ad_crt_template_type,ad_crt_normal_type,ad_crt_normal_types,flash_crt_type,is_open_rewarded,
                                        rewarded_video_crt_type,ad_feedback_element,need_server_verify,transfer_url,secret,price_strategy_type,real_time_bidding_type,
                                        ecpm_price,placement_test_status,strategy,createUser,createTime,params,create_record)
        values (
                   #{placement_id},#{member_id},#{appid},#{channel},#{adExtensionName},#{remark},#{app_id},#{placement_name},
                   #{scene},#{rewarded_video_scene},#{rewarded_video_description},#{ad_pull_mode},#{render_type},#{sdk_ad_type},#{open_type},
                   #{ad_crt_type_list},#{ad_crt_template_type},#{ad_crt_normal_type},#{ad_crt_normal_types},#{flash_crt_type},#{is_open_rewarded},
                   #{rewarded_video_crt_type},#{ad_feedback_element},#{need_server_verify},#{transfer_url},#{secret},#{price_strategy_type},#{real_time_bidding_type},
                   #{ecpm_price},#{placement_test_status},#{strategy},#{createUser},now(),#{params},#{create_record}
               )
    </insert>

    <update id="updateKSCpm" parameterType="com.wbgame.pojo.adv2.KSAdcodeVo">
        update adv_adcode_ks_info set cpm = #{cpm},modifyUser=#{modifyUser},modifyTime = now()  where positionId =#{positionId}
    </update>

    <update id="updateYLHCpm" parameterType="com.wbgame.pojo.adv2.YLHAdcodeVo">
        update adv_adcode_ylh_info set ecpm_price =#{ecpm_price},modifyUser=#{modifyUser},modifyTime = now() where placement_id =#{placement_id}
    </update>

    <update id="updateCSJCpm" parameterType="com.wbgame.pojo.adv2.CSJAdcodeVo">
        update adv_adcode_headline_info set cpm =#{cpm},modifyUser=#{modifyUser},modifyTime =now() where ad_slot_id =#{ad_slot_id}
    </update>

    <insert id="saveYLHMedium" parameterType="com.wbgame.pojo.adv2.YLHMediumVo">
        insert into adv_medium_ylh_info (appid,app_id,member_id,medium_name,industry_id,
                                         industry_id_v2,os,detail_url,affiliation,package_name,full_package_name,sha1,wechat_app_id,
                                         wechat_universal_link,icp_picture_img_id,soft_right_img_id,company_ship_img_id,
                                         biz_right_img_id,game_isbn_img_id,medium_test_status,profit_mode,convert_to_formal,create_user,create_time)
        values (#{appid},#{app_id},#{member_id},#{medium_name},#{industry_id},
                #{industry_id_v2},#{os},#{detail_url},#{affiliation},#{package_name},#{full_package_name},#{sha1},#{wechat_app_id},
                #{wechat_universal_link},#{icp_picture_img_id},#{soft_right_img_id},#{company_ship_img_id},
                #{biz_right_img_id},#{game_isbn_img_id},#{medium_test_status},#{profit_mode},#{convert_to_formal},#{create_user},now())
            ON DUPLICATE KEY UPDATE appid =values(appid),medium_name =values(medium_name),industry_id =values(industry_id),
            industry_id_v2 =values(industry_id_v2),os =values(os),detail_url =values(detail_url),
                                 affiliation =values(affiliation),package_name =values(package_name),full_package_name =values(full_package_name),
                                 sha1 =values(sha1),wechat_app_id =values(wechat_app_id),wechat_universal_link =values(wechat_universal_link),
                                 icp_picture_img_id =values(icp_picture_img_id),soft_right_img_id =values(soft_right_img_id),company_ship_img_id =values(company_ship_img_id),
                                 biz_right_img_id =values(biz_right_img_id),game_isbn_img_id =values(game_isbn_img_id),medium_test_status =values(medium_test_status),
                                 profit_mode =values(profit_mode),convert_to_formal =values(convert_to_formal),modify_user =values(modify_user),modify_time = now()
    </insert>

    <insert id="batchSaveYLHMedium" parameterType="java.util.List">
        insert into adv_medium_ylh_info (appid,app_id,member_id,medium_name,industry_id,
        industry_id_v2,os,detail_url,affiliation,package_name,full_package_name,sha1,wechat_app_id,
        wechat_universal_link,icp_picture_img_id,soft_right_img_id,company_ship_img_id,
        biz_right_img_id,game_isbn_img_id,medium_test_status,profit_mode,status) values
        <foreach collection="list" item="item" separator=",">
            (#{item.appid},#{item.app_id},#{item.member_id},#{item.medium_name},#{item.industry_id},
            #{item.industry_id_v2},#{item.os},#{item.detail_url},#{item.affiliation},#{item.package_name},#{item.full_package_name},#{item.sha1},#{item.wechat_app_id},
            #{item.wechat_universal_link},#{item.icp_picture_img_id},#{item.soft_right_img_id},#{item.company_ship_img_id},
            #{item.biz_right_img_id},#{item.game_isbn_img_id},#{item.medium_test_status},#{item.profit_mode},#{item.status})
        </foreach>
        ON DUPLICATE KEY UPDATE member_id =values(member_id),medium_name = values(medium_name),industry_id = values(industry_id),
        industry_id_v2 = values(industry_id_v2),os = values(os),detail_url = values(detail_url),
        affiliation = values(affiliation),package_name = values(package_name),full_package_name = values(full_package_name),
        sha1 = values(sha1),wechat_app_id = values(wechat_app_id),wechat_universal_link = values(wechat_universal_link),
        icp_picture_img_id = values(icp_picture_img_id),soft_right_img_id = values(soft_right_img_id),
        company_ship_img_id = values(company_ship_img_id),biz_right_img_id = values(biz_right_img_id),
        game_isbn_img_id = values(game_isbn_img_id),medium_test_status = values(medium_test_status),
        profit_mode = values(profit_mode),status = values(status)
    </insert>

    <update id="updateYLHMediumBaseInfo" parameterType="com.wbgame.pojo.adv2.YLHMediumVo">
        update adv_medium_ylh_info set appid = #{appid},modify_user =#{modify_user},modify_time = now()
        where app_id = #{app_id}
    </update>

    <select id="getYLHMediumList" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.YLHMediumVo">
        select * from  adv_medium_ylh_info where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="member_id != null and member_id != ''">
            and member_id = #{member_id}
        </if>
        <if test="medium_name != null and medium_name != ''">
            and medium_name like '%${medium_name}%'
        </if>
    </select>

    <select id="getMobvistaAdCodeList" parameterType="com.wbgame.pojo.adv2.MobvistaAdVo" resultType="com.wbgame.pojo.adv2.MobvistaAdVo">
        select * from  adv_adcode_mobvista_info where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="ad_type != null and ad_type != ''">
            and ad_type = #{ad_type}
        </if>
        <if test="unit_id != null and unit_id != ''">
            and unit_id = #{unit_id}
        </if>
        <if test="unit_name != null and unit_name != ''">
            and (unit_name like '%${unit_name}%' or hb_unit_name like '%${unit_name}%' )
        </if>
        order by modifyTime desc
    </select>

    <select id="selectTappidList" resultType="java.lang.String">
        select distinct tappid
        from adv_adcode_account_info
        where
            platformName = #{platformName}
          and channel = #{channel}
    </select>

    <insert id="saveMobvistaAdCode" parameterType="com.wbgame.pojo.adv2.MobvistaAdVo">
        insert into adv_adcode_mobvista_info (appid,app_id,placement_name,ad_type,integrate_type,
                                              content_type,video_orientation,show_close_button,auto_fresh,hb_unit_name,placement_id,
                                              bidding_type,unit_name,ecpm_floor,target_ecpm,unit_id,channel,adExtensionName,remark,
                                              sdk_ad_type,open_type,createTime,createUser,ecpm,strategy,modifyTime,params)
        values (#{appid},#{app_id},#{placement_name},#{ad_type},#{integrate_type},
                #{content_type},#{video_orientation},#{show_close_button},#{auto_fresh},#{hb_unit_name},#{placement_id},
                #{bidding_type},#{unit_name},#{ecpm_floor},#{target_ecpm},#{unit_id},#{channel},#{adExtensionName},#{remark},
                #{sdk_ad_type},#{open_type},now(),#{createUser},#{ecpm_price},#{strategy},now(),#{params})
    </insert>

    <insert id="insertSigmobAdCode" parameterType="com.wbgame.pojo.adv2.SigmobAdVo">
        insert into adv_adcode_sigmob_info (
            appid,app_id,adtype,name,scenario,frequencyd,addirect,reward_setting,reward_name,reward_sum,reward_url,
            render_type,material,direction,min_duration,max_duration,is_mute,is_auto_play,becpm_state,becpm,internal_bidding,
            channel,adExtensionName,remark,sdk_ad_type,open_type,strategy,placementid,createTime,createUser,modifyTime,modifyUser,params
        )
        values
            (
                #{appid},#{app_id},#{adtype},#{name},#{scenario},#{frequencyd},#{addirect},#{reward_setting},#{reward_name},#{reward_sum},#{reward_url},
                #{render_type},#{material},#{direction},#{min_duration},#{max_duration},#{is_mute},#{is_auto_play},#{becpm_state},#{becpm},#{internal_bidding},
                #{channel},#{adExtensionName},#{remark},#{sdk_ad_type},#{open_type},#{strategy},#{placementid},now(),#{createUser},now(),#{modifyUser},#{params}
            )
    </insert>

    <select id="selectSigmobAdCodeList" resultType="com.wbgame.pojo.adv2.SigmobAdVo" parameterType="com.wbgame.pojo.adv2.SigmobAdVo">
        select
        appid,app_id,adtype,`name`,scenario,frequencyd,addirect,reward_setting,reward_name,reward_sum,reward_url,
        render_type,material,direction,min_duration,max_duration,is_mute,is_auto_play,becpm_state,becpm,internal_bidding,
        channel,adExtensionName,remark,sdk_ad_type,open_type,strategy,placementid,createTime,createUser,modifyTime,modifyUser,params
        from
        adv_adcode_sigmob_info
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="adtype != null and adtype != ''">
            and adtype = #{adtype}
        </if>
        <if test="placementid != null and placementid != ''">
            and placementid = #{placementid}
        </if>
        <if test="name != null and name != ''">
            and (`name` like '%${name}%')
        </if>
        order by modifyTime desc
    </select>
    <select id="getAdCodeAccountListByTappid" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo" resultType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        select  * from  adv_adcode_account_info where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="tappid != null and tappid != ''">
            and tappid = #{tappid}
        </if>
        <if test="note != null and note != ''">
            and note like '%{note}%'
        </if>
        <if test="platform != null and platform != ''">
            and platform = (${platform})
        </if>
    </select>

    <insert id="insertCSJModule" parameterType="com.wbgame.pojo.adv2.adcodeModule.CSJAdcodeModuleVo">
        insert into adv_adcode_module_headline_info
        (
            module_name,platform,sdk_ad_type,open_type,strategy,ad_slot_name,bidding_type,cpmFormula,cpmStr,adCodeNum,
            use_mediation,ad_slot_type,render_type,ad_categories,tpl_list,accept_material_type,
            slide_banner,width,height,use_icon,orientation,reward_name,reward_count,reward_is_callback,reward_callback_url,
            ad_rollout_size,skip_duration,use_endcard,createUser,createTime,modifyUser,modifyTime,exName,params
        ) values (
                     #{module_name},#{platform},#{sdk_ad_type},#{open_type},#{strategy},#{ad_slot_name},#{bidding_type},#{cpmFormula},#{cpmStr},#{adCodeNum},
                     #{use_mediation},#{ad_slot_type},#{render_type},#{ad_categories},#{tpl_list},#{accept_material_type},
                     #{slide_banner},#{width},#{height},#{use_icon},#{orientation},#{reward_name},#{reward_count},#{reward_is_callback},#{reward_callback_url},
                     #{ad_rollout_size},#{skip_duration},#{use_endcard},#{createUser},now(),#{modifyUser},now(),#{exName},#{params}
                 )
    </insert>

    <insert id="insertYLHModule" parameterType="com.wbgame.pojo.adv2.adcodeModule.YLHAdcodeModuleVo">
        insert into  adv_adcode_module_ylh_info
        (
         module_name,platform,adCodeNum,cpmFormula,cpmStr,sdk_ad_type,open_type,strategy,scene,ad_pull_mode,render_type,
         ad_crt_normal_type, ad_crt_normal_types,ad_crt_template_type,ad_crt_type_list,flash_crt_type,need_server_verify,rewarded_video_scene,
         rewarded_video_crt_type,transfer_url,secret,is_open_rewarded,placement_id,placement_name,price_strategy_type,
         real_time_bidding_type,createUser,createTime,modifyUser,modifyTime,exName,params)
        values (
        #{module_name},#{platform},#{adCodeNum},#{cpmFormula},#{cpmStr},#{sdk_ad_type},#{open_type},#{strategy},#{scene},#{ad_pull_mode},#{render_type},
                #{ad_crt_normal_type}, #{ad_crt_normal_types},#{ad_crt_template_type},#{ad_crt_type_list},#{flash_crt_type},#{need_server_verify},#{rewarded_video_scene},
                #{rewarded_video_crt_type},#{transfer_url},#{secret},#{is_open_rewarded},#{placement_id},#{placement_name},#{price_strategy_type},
                #{real_time_bidding_type},#{createUser},now(),#{modifyUser},now(),#{exName},#{params}
               )
    </insert>
    <insert id="insertKSModule" parameterType="com.wbgame.pojo.adv2.adcodeModule.KSAdcodeModuleVo">
        insert into adv_adcode_module_ks_info
        (
         module_name,platform,adCodeNum,cpmFormula,cpmStr,sdk_ad_type,open_type,strategy,adStyle,renderType,materialTypeList,
         templateId,skipAdMode,countdownShow,voice,rewardedType,rewardedNum,callbackStatus,callbackUrll,multiTemplateParams,
         positionId,name,priceStrategy,bidding_type,createUser,createTime,modifyUser,modifyTime,exName,params)
        values (
        #{module_name},#{platform},#{adCodeNum},#{cpmFormula},#{cpmStr},#{sdk_ad_type},#{open_type},#{strategy},#{adStyle},#{renderType},#{materialTypeList},
        #{templateId},#{skipAdMode},#{countdownShow},#{voice},#{rewardedType},#{rewardedNum},#{callbackStatus},#{callbackUrll},#{multiTemplateParams},
        #{positionId},#{name},#{priceStrategy},#{bidding_type},#{createUser},now(),#{modifyUser},now(),#{exName},#{params}
               )
    </insert>
    <insert id="createModuleGroup">
        insert into adcode_module_group (group_name, platform, module_id) values
        <foreach collection="list" item="item" separator=",">
            (#{item.group_name}, #{item.platform}, #{item.module_id})
        </foreach>
    </insert>
    <insert id="insertOppoAdCode">
        insert into adv_adcode_oppo_info
        (appid,app_id,posScene,posName,devCrtType,biddingPattern,targetPriceOpen,targetPrice,renderMode,
         multiDevCrtTypes,fullScreenClientSwitch,btnAreaClientJumpSwitch,notBtnAreaClientJumpSwitch,ecpm,configRate,
         openVoiceStyle,videoPlayDirection,multiEndPageStyles,adMultiDevCrtTypes,channel,adExtensionName,remark,sdk_ad_type,
         open_type,strategy,placementid,createTime,createUser,modifyTime,modifyUser,params,create_record,select_type)
        values
        (#{appid},#{app_id},#{posScene},#{posName},#{devCrtType},#{biddingPattern},#{targetPriceOpen},#{targetPrice},#{renderMode},
         #{multiDevCrtTypes},#{fullScreenClientSwitch},#{btnAreaClientJumpSwitch},#{notBtnAreaClientJumpSwitch},#{ecpm},#{configRate},
         #{openVoiceStyle},#{videoPlayDirection},#{multiEndPageStyles},#{adMultiDevCrtTypes},#{channel},#{adExtensionName},#{remark},#{sdk_ad_type},
         #{open_type},#{strategy},#{placementid},now(),#{createUser},now(),#{modifyUser},#{params},#{create_record},#{select_type})
    </insert>
    <insert id="insertOppoModule">
         insert into adv_adcode_module_oppo_info
         (module_name,platform,adCodeNum,cpmFormula,cpmStr,posScene,devCrtType,biddingPattern,targetPriceOpen,
          targetPrice,renderMode,multiDevCrtTypes,fullScreenClientSwitch,btnAreaClientJumpSwitch,ecpm,
          notBtnAreaClientJumpSwitch,openVoiceStyle,videoPlayDirection,multiEndPageStyles,remark,sdk_ad_type,
          open_type,strategy,createTime,createUser,modifyTime,modifyUser,adMultiDevCrtTypes,posName,exName,configRate,params,select_type)
         values
             (#{module_name},#{platform},#{adCodeNum},#{cpmFormula},#{cpmStr},#{posScene},#{devCrtType},#{biddingPattern},#{targetPriceOpen},
              #{targetPrice},#{renderMode},#{multiDevCrtTypes},#{fullScreenClientSwitch},#{btnAreaClientJumpSwitch},#{ecpm},
              #{notBtnAreaClientJumpSwitch},#{openVoiceStyle},#{videoPlayDirection},#{multiEndPageStyles},#{remark},#{sdk_ad_type},
              #{open_type},#{strategy},now(),#{createUser},now(),#{modifyUser},#{adMultiDevCrtTypes},#{posName},#{exName},#{configRate},#{params},#{select_type})
    </insert>
    <insert id="insertBaiduAdCode">
        insert into adv_adcode_baidu_info
        (
            id,appid,app_sid,ad_type,ad_name,price_type,ad_tags,scene_tag,cpm,info_flow_style_control_type,
         info_flow_material_types,info_flow_templates,style_types,sizes,splash_material_types,splash_show_control,
         interstitial_ad_scene,interstitial_style_types,interstitial_material_types,reward_video_return_control,
         reward_video_return_url,reward_video_voice_control,channel,adExtensionName,remark,sdk_ad_type,open_type,
         strategy,placementid,createTime,createUser,modifyTime,modifyUser,params
        )
        values
            (#{id},#{appid},#{app_sid},#{ad_type},#{ad_name},#{price_type},#{ad_tags},#{scene_tag},#{cpm},#{info_flow_style_control_type},
             #{info_flow_material_types},#{info_flow_templates},#{style_types},#{sizes},#{splash_material_types},#{splash_show_control},
             #{interstitial_ad_scene},#{interstitial_style_types},#{interstitial_material_types},#{reward_video_return_control},
             #{reward_video_return_url},#{reward_video_voice_control},#{channel},#{adExtensionName},#{remark},#{sdk_ad_type},#{open_type},
             #{strategy},#{placementid},now(),#{createUser},now(),#{modifyUser},#{params})
    </insert>
    <select id="selectCSJModuleByGroup" resultType="java.util.Map">
        select id,module_name,platform,sdk_ad_type,adCodeNum,open_type,strategy,ad_slot_name,bidding_type,cpmFormula,cpmStr,
               use_mediation,ad_slot_type,render_type,ad_categories,tpl_list,accept_material_type,
               slide_banner,width,height,use_icon,orientation,reward_name,reward_count,reward_is_callback,reward_callback_url,
               ad_rollout_size,skip_duration,use_endcard,createUser,createTime,modifyUser,modifyTime,exName,params
        from adv_adcode_module_headline_info
        where groupName is not null
    </select>

    <delete id="deleteCSJModule">
        delete from adv_adcode_module_headline_info
        where id = #{id}
    </delete>
    <delete id="deleteYLHModule">
        delete from adv_adcode_module_ylh_info
        where id = #{id}
    </delete>
    <delete id="deleteKSModule">
        delete from adv_adcode_module_ks_info
        where id = #{id}
    </delete>

    <update id="updateCSJModuleGroupName">
        update adv_adcode_module_headline_info
        set group_name = #{groupName}
        where <foreach collection="ids" item="id" separator=" or ">
            id = #{id}
        </foreach>
    </update>
    <update id="updateKSModule">
        update adv_adcode_module_ks_info set
            module_name = #{module_name},
            platform = #{platform},
            adCodeNum = #{adCodeNum},
            cpmFormula = #{cpmFormula},
            cpmStr = #{cpmStr},
            sdk_ad_type = #{sdk_ad_type},
            open_type = #{open_type},
            strategy = #{strategy},
            adStyle = #{adStyle},
            renderType = #{renderType},
            materialTypeList = #{materialTypeList},
            templateId = #{templateId},
            skipAdMode = #{skipAdMode},
            multiTemplateParams = #{multiTemplateParams},
            countdownShow = #{countdownShow},
            voice = #{voice},
            rewardedType = #{rewardedType},
            rewardedNum = #{rewardedNum},
            callbackStatus = #{callbackStatus},
            callbackUrll = #{callbackUrll},
            positionId = #{positionId},
            name = #{name},
            priceStrategy = #{priceStrategy},
            bidding_type = #{bidding_type},
            createUser = #{createUser},
            createTime = #{createTime},
            modifyUser = #{modifyUser},
            modifyTime = now(),
            params = #{params},
            exName = #{exName}
        where id = #{id}
    </update>
    <update id="updateYLHModule">
        update adv_adcode_module_ylh_info set
            module_name = #{module_name},
            platform = #{platform},
            adCodeNum = #{adCodeNum},
            cpmFormula = #{cpmFormula},
            cpmStr = #{cpmStr},
            sdk_ad_type = #{sdk_ad_type},
            open_type = #{open_type},
            strategy = #{strategy},
            scene = #{scene},
            ad_pull_mode = #{ad_pull_mode},
            render_type = #{render_type},
            ad_crt_normal_type = #{ad_crt_normal_type},
            ad_crt_normal_types = #{ad_crt_normal_types},
            ad_crt_template_type = #{ad_crt_template_type},
            ad_crt_type_list = #{ad_crt_type_list},
            flash_crt_type = #{flash_crt_type},
            need_server_verify = #{need_server_verify},
            rewarded_video_scene = #{rewarded_video_scene},
            rewarded_video_crt_type = #{rewarded_video_crt_type},
            transfer_url = #{transfer_url},
            secret = #{secret},
            is_open_rewarded = #{is_open_rewarded},
            placement_id = #{placement_id},
            placement_name = #{placement_name},
            price_strategy_type = #{price_strategy_type},
            real_time_bidding_type = #{real_time_bidding_type},
            createUser = #{createUser},
            createTime = #{createTime},
            modifyUser = #{modifyUser},
            modifyTime = now(),
            params = #{params},
            exName = #{exName}
        where id = #{id}
    </update>
    <update id="updateCSJModule">
        update adv_adcode_module_headline_info set
            module_name = #{module_name},
            platform = #{platform},
            sdk_ad_type = #{sdk_ad_type},
            adCodeNum = #{adCodeNum},
            open_type = #{open_type},
            strategy = #{strategy},
            ad_slot_name = #{ad_slot_name},
            bidding_type = #{bidding_type},
            cpmFormula = #{cpmFormula},
            cpmStr = #{cpmStr},
            use_mediation = #{use_mediation},
            ad_slot_type = #{ad_slot_type},
            render_type = #{render_type},
            ad_categories = #{ad_categories},
            tpl_list = #{tpl_list},
            accept_material_type = #{accept_material_type},
            slide_banner = #{slide_banner},
            width = #{width},
            height = #{height},
            use_icon = #{use_icon},
            orientation = #{orientation},
            reward_name = #{reward_name},
            reward_count = #{reward_count},
            reward_is_callback = #{reward_is_callback},
            reward_callback_url = #{reward_callback_url},
            ad_rollout_size = #{ad_rollout_size},
            skip_duration = #{skip_duration},
            use_endcard = #{use_endcard},
            createUser = #{createUser},
            createTime = #{createTime},
            modifyUser = #{modifyUser},
            modifyTime = now(),
            params = #{params},
            exName = #{exName}
        where id = #{id}
    </update>
    <update id="updateOppoModule">
        update adv_adcode_module_oppo_info set
                                               module_name = #{module_name},
                                               platform = #{platform},
                                               adCodeNum = #{adCodeNum},
                                               cpmFormula = #{cpmFormula},
                                               cpmStr = #{cpmStr},
                                               posScene = #{posScene},
                                               devCrtType = #{devCrtType},
                                               biddingPattern = #{biddingPattern},
                                               targetPriceOpen = #{targetPriceOpen},
                                               targetPrice = #{targetPrice},
                                               renderMode = #{renderMode},
                                               multiDevCrtTypes = #{multiDevCrtTypes},
                                               fullScreenClientSwitch = #{fullScreenClientSwitch},
                                               btnAreaClientJumpSwitch = #{btnAreaClientJumpSwitch},
                                               notBtnAreaClientJumpSwitch = #{notBtnAreaClientJumpSwitch},
                                               openVoiceStyle = #{openVoiceStyle},
                                               ecpm = #{ecpm},
                                               videoPlayDirection = #{videoPlayDirection},
                                               multiEndPageStyles = #{multiEndPageStyles},
                                               remark = #{remark},
                                               sdk_ad_type = #{sdk_ad_type},
                                               open_type = #{open_type},
                                               strategy = #{strategy},
                                               modifyTime = now(),
                                               modifyUser = #{modifyUser},
                                               adMultiDevCrtTypes = #{adMultiDevCrtTypes},
                                               exName = #{exName},
                                               configRate = #{configRate},
                                               params = #{params},
                                               posName = #{posName},
                                               select_type = #{select_type}
        where id = #{id}
    </update>

    <delete id="deleteModuleGroup">
        delete
        from adcode_module_group
        where group_name = #{group_name}
        <if test="list != null and list.size() > 0">
        and (
            <foreach collection="list" item="item" separator="or">
                (platform = #{item.platform} and module_id = #{item.id})
            </foreach>
            )
        </if>
    </delete>
    <delete id="deleteOppoModule">
        delete from adv_adcode_module_oppo_info
        where id = #{id}
    </delete>

    <select id="selectModuleGroup" resultType="com.wbgame.pojo.adv2.adcodeModule.ModuleGroupDto">
        select id, group_name, platform, module_id
        from adcode_module_group
        <where>
            <if test="group_name != null and group_name != ''">
                group_name like CONCAT('%',#{group_name},'%')
            </if>
        </where>
    </select>

    <select id="selectBatchCSJModule" resultType="com.wbgame.pojo.adv2.adcodeModule.CSJAdcodeModuleVo">
        select id,module_name,platform,sdk_ad_type,adCodeNum,open_type,strategy,ad_slot_name,bidding_type,cpmFormula,cpmStr,
               use_mediation,ad_slot_type,render_type,ad_categories,tpl_list,accept_material_type,
               slide_banner,width,height,use_icon,orientation,reward_name,reward_count,reward_is_callback,reward_callback_url,
               ad_rollout_size,skip_duration,use_endcard,createUser,createTime,modifyUser,modifyTime,exName,params
        from adv_adcode_module_headline_info
        where
        id in (
            <foreach collection="list" item="item" separator=",">
                ${item}
            </foreach>
        )
    </select>
    <select id="selectYLHModule" resultType="java.util.Map">
        select id,module_name,platform,adCodeNum,cpmFormula,cpmStr,sdk_ad_type,open_type,strategy,scene,ad_pull_mode,render_type,
        ad_crt_normal_type, ad_crt_normal_types,ad_crt_template_type,ad_crt_type_list,flash_crt_type,need_server_verify,rewarded_video_scene,
        rewarded_video_crt_type,transfer_url,secret,is_open_rewarded,placement_id,placement_name,price_strategy_type,
        real_time_bidding_type,createUser,createTime,modifyUser,modifyTime,exName,params
        from adv_adcode_module_ylh_info
        <if test="list != null">
            where
            id in (
            <foreach collection="list" item="item" separator=",">
                ${item}
            </foreach>
            )
        </if>
        order by createTime desc
    </select>
    <select id="selectKSModule" resultType="java.util.Map">
        select id,module_name,platform,adCodeNum,cpmFormula,cpmStr,sdk_ad_type,open_type,strategy,adStyle,renderType,materialTypeList,
        templateId,skipAdMode,countdownShow,voice,rewardedType,rewardedNum,callbackStatus,callbackUrll,multiTemplateParams,
        positionId,name,priceStrategy,bidding_type,createUser,createTime,modifyUser,modifyTime,exName,params
        from adv_adcode_module_ks_info
        <if test="list != null">
            where
            id in (
            <foreach collection="list" item="item" separator=",">
                ${item}
            </foreach>
            )
        </if>
        order by createTime desc
    </select>
    <select id="selectCSJModule" resultType="java.util.Map">
        select id,module_name,platform,sdk_ad_type,adCodeNum,open_type,strategy,ad_slot_name,bidding_type,cpmFormula,cpmStr,
        use_mediation,ad_slot_type,render_type,ad_categories,tpl_list,accept_material_type,
        slide_banner,width,height,use_icon,orientation,reward_name,reward_count,reward_is_callback,reward_callback_url,
        ad_rollout_size,skip_duration,use_endcard,createUser,createTime,modifyUser,modifyTime,exName,params
        from adv_adcode_module_headline_info
        order by createTime desc
    </select>
    <select id="selectOppoAdCodeList" parameterType="com.wbgame.pojo.adv2.OppoAdVo" resultType="com.wbgame.pojo.adv2.OppoAdVo">
        select
        appid,app_id,posScene,posName,devCrtType,biddingPattern,targetPriceOpen,targetPrice,renderMode,multiDevCrtTypes,adMultiDevCrtTypes,
        fullScreenClientSwitch,btnAreaClientJumpSwitch,notBtnAreaClientJumpSwitch,openVoiceStyle,videoPlayDirection,ecpm,configRate,
        multiEndPageStyles,channel,adExtensionName,remark,sdk_ad_type,open_type,strategy,placementid,createTime,
        createUser,modifyTime,modifyUser,params,create_record,select_type
        from
        adv_adcode_oppo_info
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="posScene != null and posScene != ''">
            and posScene = #{posScene}
        </if>
        <if test="placementid != null and placementid != ''">
            and placementid = #{placementid}
        </if>
        <if test="posName != null and posName != ''">
            and (`posName` like '%${posName}%')
        </if>
        order by createTime desc
    </select>
    <select id="selectOppoModule" resultType="java.util.Map">
        select id,module_name,platform,adCodeNum,cpmFormula,cpmStr,posScene,devCrtType,biddingPattern,targetPriceOpen,
               targetPrice,renderMode,multiDevCrtTypes,fullScreenClientSwitch,btnAreaClientJumpSwitch,notBtnAreaClientJumpSwitch,
               openVoiceStyle,videoPlayDirection,multiEndPageStyles,remark,sdk_ad_type,open_type,strategy,placementid,ecpm,
               createTime,createUser,modifyTime,modifyUser,adMultiDevCrtTypes,posName,exName,configRate,params,select_type
            from adv_adcode_module_oppo_info
        <if test="list != null">
            where
            id in (
            <foreach collection="list" item="item" separator=",">
                ${item}
            </foreach>
            )
        </if>
        order by createTime desc
    </select>
    <select id="selectBaiduAdCodeList" resultType="com.wbgame.pojo.adv2.BaiduAdCodeVo">
        select
        id,appid,app_sid,ad_type,ad_name,price_type,ad_tags,scene_tag,cpm,info_flow_style_control_type,
         info_flow_material_types,info_flow_templates,style_types,sizes,splash_material_types,splash_show_control,
         interstitial_ad_scene,interstitial_style_types,interstitial_material_types,reward_video_return_control,
         reward_video_return_url,reward_video_voice_control,channel,adExtensionName,remark,sdk_ad_type,open_type,
         strategy,placementid,createTime,createUser,modifyTime,modifyUser,params
        from
        adv_adcode_baidu_info
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="ad_type != null and ad_type != ''">
            and ad_type = #{ad_type}
        </if>
        <if test="placementid != null and placementid != ''">
            and placementid = #{placementid}
        </if>
        <if test="ad_name != null and ad_name != ''">
            and (`ad_name` like '%${ad_name}%')
        </if>
        order by createTime desc
    </select>

    <insert id="insertXiaomiAdCode">
        INSERT INTO adv_adcode_xiaomi_info
            (appid,tappid,channel,sdk_ad_type,open_type,adExtensionName,remark,strategy,params,placementid,adName,adType,detail_style,constraintId,
             drawing_type,return_elements,render_type,scenes,target_price,ecpm,configRate,`out`,bidding,createTime,createUser)
        VALUES
            (#{appid},#{tappid},#{channel},#{sdk_ad_type},#{open_type},#{adExtensionName},#{remark},#{strategy},#{params},#{placementid},#{adName},#{adType},#{detail_style},
             #{constraintId},#{drawing_type},#{return_elements},#{render_type},#{scenes},#{target_price},#{ecpm},#{configRate},#{out},#{bidding},now(),#{createUser});
    </insert>

    <select id="selectXiaomiAdCodeList" resultType="com.wbgame.pojo.adv2.AdvAdcodeXiaomiInfo">
        select *,tappid as app_id from adv_adcode_xiaomi_info
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="adName != null and adName != ''">
            and adName like concat('%',#{adName},'%')
        </if>
        <if test="adType != null and adType != ''">
            and adType = #{adType}
        </if>
        <if test="placementid != null and placementid != ''">
            and placementid = #{placementid}
        </if>
        <if test="createUser != null and createUser != ''">
            and createUser like concat('%',#{createUser},'%')
        </if>
        <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
            and createTime between concat(#{start_date},' 00:00:00') and concat(#{end_date},' 23:59:59')
        </if>
        order by createTime desc
    </select>


    <insert id="insertVivoAdCode">
        insert into adv_adcode_vivo_info
        (appid,app_id,posType,posName,renderType,secondScene,orientation,renderStyles,channel,adExtensionName,remark,sdk_ad_type,
         open_type,strategy,placementid,createTime,createUser,modifyTime,modifyUser,params,ecpm,configRate,adNorms,accessType,create_record)
        values
        (#{appid},#{app_id},#{posType},#{posName},#{renderType},#{secondScene},#{orientation}
        ,#{renderStyles},#{channel},#{adExtensionName},#{remark},#{sdk_ad_type},#{open_type},#{strategy}
        ,#{placementid},now(),#{createUser},now(),#{modifyUser},#{params},#{ecpm},#{configRate},#{adNorms},#{accessType},#{create_record})
    </insert>

    <select id="selectVivoAdCodeList" resultType="com.wbgame.pojo.adv2.VivoAdVo">
        select
        appid,app_id,posType,posName,ecpm,configRate,renderType,renderStyles,secondScene,orientation,
        channel,adExtensionName,remark,sdk_ad_type,open_type,strategy,placementid,createTime,
        createUser,modifyTime,modifyUser,params,accessType,adNorms,create_record
        from
        adv_adcode_vivo_info
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="posType != null and posType != ''">
            and posType = #{posType}
        </if>
        <if test="placementid != null and placementid != ''">
            and placementid = #{placementid}
        </if>
        <if test="posName != null and posName != ''">
            and (`posName` like '%${posName}%')
        </if>
        order by createTime desc
    </select>

    <select id="selectVivoModule" resultType="java.util.Map">
        select id,module_name,platform,adCodeNum,cpmFormula,cpmStr,posType,posName,renderType,secondScene,orientation,renderStyles
        ,remark,sdk_ad_type,open_type,strategy,createTime,createUser,modifyTime,modifyUser,params,ecpm
        ,configRate,adNorms,accessType,exName
        from adv_adcode_module_vivo_info
        <if test="list != null">
            where
            id in (
            <foreach collection="list" item="item" separator=",">
                ${item}
            </foreach>
            )
        </if>
        order by createTime desc
    </select>

    <insert id="insertVivoModule">
        insert into adv_adcode_module_vivo_info
        (module_name,platform,adCodeNum,cpmFormula,cpmStr,posType,posName,renderType,secondScene,orientation,renderStyles
        ,remark,sdk_ad_type,open_type,strategy,createTime,createUser,modifyTime,modifyUser,params,ecpm
        ,configRate,adNorms,accessType,exName)
        values
        (#{module_name},#{platform},#{adCodeNum},#{cpmFormula},#{cpmStr},#{posType},#{posName},#{renderType},#{secondScene},#{orientation}
        ,#{renderStyles},#{remark},#{sdk_ad_type},#{open_type},#{strategy}
        ,now(),#{createUser},now(),#{modifyUser},#{params},#{ecpm},#{configRate},#{adNorms},#{accessType},#{exName})
    </insert>

    <update id="updateVivoModule">
        update adv_adcode_module_vivo_info set
            module_name = #{module_name},
            platform = #{platform},
            adCodeNum = #{adCodeNum},
            cpmFormula = #{cpmFormula},
            cpmStr = #{cpmStr},
            posType = #{posType},
            posName = #{posName},
            renderType = #{renderType},
            secondScene = #{secondScene},
            orientation = #{orientation},
            renderStyles = #{renderStyles},
            accessType = #{accessType},
            adNorms = #{adNorms},
            ecpm = #{ecpm},
            remark = #{remark},
            sdk_ad_type = #{sdk_ad_type},
            open_type = #{open_type},
            strategy = #{strategy},
            modifyTime = now(),
            modifyUser = #{modifyUser},
            exName = #{exName},
            configRate = #{configRate},
            params = #{params}
            where id = #{id}
    </update>

    <delete id="deleteVivoModule">
        delete from adv_adcode_module_vivo_info
        where id = #{id}
    </delete>

    <insert id="insertXiaomiModule">
        INSERT INTO adv_adcode_module_xiaomi_info
            (module_name,platform,adCodeNum,cpmFormula,cpmStr,sdk_ad_type,open_type,remark,strategy,params,placementid,adName,adType,detailType,
             constraintId,drawingType,returnElements,renderType,scenes,target_price,ecpm,configRate,createTime,createUser,`out`,bidding,exName)
        VALUES
            (#{module_name},#{platform},#{adCodeNum},#{cpmFormula},#{cpmStr},#{sdk_ad_type},#{open_type},#{remark},#{strategy},#{params},#{placementid},#{adName},
             #{adType},#{detailType},#{constraintId},#{drawingType},#{returnElements},#{renderType},#{scenes},#{target_price},#{ecpm},#{configRate},now(),#{createUser},#{out},#{bidding},#{exName})
    </insert>

    <select id="selectXiaomiModule" resultType="java.util.Map">
        select id,module_name,platform,adCodeNum,cpmFormula,cpmStr,sdk_ad_type,open_type,remark,strategy,params,placementid,adName,adType,detailType,
        constraintId,drawingType,returnElements,renderType,scenes,target_price,ecpm,configRate,createTime,createUser,modifyTime,modifyUser,`out`,bidding,exName
        from adv_adcode_module_xiaomi_info
        <if test="list != null">
            where
            id in (
            <foreach collection="list" item="item" separator=",">
                ${item}
            </foreach>
            )
        </if>
        order by createTime desc
    </select>

    <update id="updateXiaomiModule">
        update adv_adcode_module_xiaomi_info set
           module_name = #{module_name},
           platform = #{platform},
           adCodeNum = #{adCodeNum},
           cpmFormula = #{cpmFormula},
           cpmStr = #{cpmStr},
           sdk_ad_type = #{sdk_ad_type},
           open_type = #{open_type},
           remark = #{remark},
           strategy = #{strategy},
           params = #{params},
           placementid = #{placementid},
           adName = #{adName},
           adType = #{adType},
           detailType = #{detailType},
           constraintId = #{constraintId},
           drawingType = #{drawingType},
           returnElements = #{returnElements},
           renderType = #{renderType},
           scenes = #{scenes},
           target_price = #{target_price},
           ecpm = #{ecpm},
           configRate = #{configRate},
           `out` = #{out},
           bidding = #{bidding},
           exName = #{exName},
           modifyTime = now(),
           modifyUser = #{modifyUser}
        where id = #{id}
    </update>

    <delete id="deleteXiaomiModule">
        delete from adv_adcode_module_xiaomi_info
        where id = #{id}
    </delete>


</mapper>