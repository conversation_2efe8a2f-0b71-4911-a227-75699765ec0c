<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adv2.MonetizationSummaryMapper">

    <select id="getMonetizationReport" resultType="com.wbgame.pojo.jettison.report.dto.MonetizationReportDTO"
            parameterType="com.wbgame.pojo.jettison.report.param.MonetizationReportParam">
        SELECT
        IFNULL(SUM(dau),0) AS dau,
        IFNULL(SUM(installs),0) AS installs,
        IFNULL(SUM(revenue),0) AS revenue,
        IFNULL(SUM(pv_video),0) AS pv_video,
        IFNULL(SUM(pv_plaque),0) AS pv_plaque,
        IFNULL(SUM(pv_banner),0) AS pv_banner,
        IFNULL(SUM(pv_splash),0) AS pv_splash,
        IFNULL(SUM(pv_msg),0) AS pv_msg,
        IFNULL(SUM(revenue_video),0) AS revenue_video,
        IFNULL(SUM(revenue_plaque),0) AS revenue_plaque,
        IFNULL(SUM(revenue_banner),0) AS revenue_banner,
        IFNULL(SUM(revenue_splash),0) AS revenue_splash,
        IFNULL(SUM(revenue_msg),0) AS revenue_msg,

        IFNULL(SUM(out_dau),0) AS out_dau,
IFNULL(SUM(out_installs),0) AS out_installs,
IFNULL(SUM(out_revenue),0) AS out_revenue,
IFNULL(SUM(out_pv_video),0) AS out_pv_video,
IFNULL(SUM(out_pv_plaque),0) AS out_pv_plaque,
IFNULL(SUM(out_pv_banner),0) AS out_pv_banner,
IFNULL(SUM(out_pv_splash),0) AS out_pv_splash,
IFNULL(SUM(out_pv_msg),0) AS out_pv_msg,
IFNULL(SUM(out_revenue_video),0) AS out_revenue_video,
IFNULL(SUM(out_revenue_plaque),0) AS out_revenue_plaque,
IFNULL(SUM(out_revenue_banner),0) AS out_revenue_banner,
IFNULL(SUM(out_revenue_splash),0) AS out_revenue_splash,
IFNULL(SUM(out_revenue_msg),0) AS out_revenue_msg,
        <if test="group2 != null and group2 != ''">
            CONCAT(${group2}) mapkey,
        </if>
        sec_TO_time(cast(avg(TIME_TO_SEC(duration)) as SIGNED)) AS duration,
        sec_TO_time(cast(avg(TIME_TO_SEC(out_duration)) as SIGNED)) AS out_duration
        <if test="group != null and group.size > 0">
            ,
            <foreach collection="group" index="index" item="item" separator=",">
                `${item}`
            </foreach>
        </if>
        FROM dn_report_monetization_summary_china
        <include refid="getMonetizationReportCondition1"/>
        <include refid="getMonetizationReportCondition2"/>
    </select>

    <select id="getMonetizationSummary" resultType="com.wbgame.pojo.jettison.report.dto.MonetizationReportDTO"
            parameterType="com.wbgame.pojo.jettison.report.param.MonetizationReportParam">
        SELECT SUM(dau) AS dau, SUM(installs) AS installs, SUM(revenue) AS revenue,
        SUM(pv_video) AS pv_video, SUM(pv_plaque) AS pv_plaque, SUM(pv_banner) AS pv_banner, SUM(pv_splash) AS
        pv_splash, SUM(pv_msg) AS pv_msg,
        SUM(revenue_video) AS revenue_video, SUM(revenue_plaque) AS revenue_plaque, SUM(revenue_banner) AS
        revenue_banner, SUM(revenue_splash) AS revenue_splash, SUM(revenue_msg) AS revenue_msg,
        sec_TO_time(cast(avg(TIME_TO_SEC(duration)) as SIGNED)) AS duration,
        SUM(out_dau) AS out_dau,
SUM(out_installs) AS out_installs,
SUM(out_revenue) AS out_revenue,
SUM(out_pv_video) AS out_pv_video,
SUM(out_pv_plaque) AS out_pv_plaque,
SUM(out_pv_banner) AS out_pv_banner,
SUM(out_pv_splash) AS out_pv_splash,
SUM(out_pv_msg) AS out_pv_msg,
SUM(out_revenue_video) AS out_revenue_video,
SUM(out_revenue_plaque) AS out_revenue_plaque,
SUM(out_revenue_banner) AS out_revenue_banner,
SUM(out_revenue_splash) AS out_revenue_splash,
SUM(out_revenue_msg) AS out_revenue_msg,
sec_TO_time(cast(avg(TIME_TO_SEC(duration)) as SIGNED)) AS out_duration
        FROM dn_report_monetization_summary_china
        <include refid="getMonetizationReportCondition1"/>
    </select>

    <sql id="getMonetizationReportCondition1">
        WHERE 1 = 1
        <if test="app != null and app.size > 0">
            AND app IN
            <foreach collection="app" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cha_type_names != null and cha_type_names.size > 0">
            AND cha_type_name IN
            <foreach collection="cha_type_names" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cha_medias != null and cha_medias.size > 0">
            AND cha_media IN
            <foreach collection="cha_medias" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cha_ids != null and cha_ids.size > 0">
            AND cha_id IN
            <foreach collection="cha_ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="two_app_category != null and two_app_category.size > 0">
            AND app IN
            <foreach collection="two_app_category" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="country != null and country != ''">
            AND country = #{country}
        </if>
        <if test="start_date != null and end_date != null">
            AND `day` BETWEEN #{start_date} AND #{end_date}
        </if>
    </sql>

    <sql id="getMonetizationReportCondition2">
        <if test="group != null and group.size > 0">
            GROUP BY
            <foreach collection="group" index="index" item="item" separator=",">
                `${item}`
            </foreach>
        </if>
        <if test="order_str != null and order_str != ''">
            ORDER BY ${order_str}
        </if>
        <if test="order_str == null or order_str == ''">
            ORDER BY `day` ASC, revenue DESC
        </if>
    </sql>

    <select id="getChinaMonetizationReportCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM dn_report_monetization_summary_china
        WHERE `day` = #{day}
    </select>

    <delete id="delChinaMonetizationReport">
        DELETE
        FROM dn_report_monetization_summary_china
        WHERE `day` = #{day}
    </delete>

    <insert id="batchAddChinaMonetizationReport" parameterType="com.wbgame.pojo.adv2.reportEntity.ChinaMonetizationReport">
        INSERT INTO dn_report_monetization_summary_china
        (`day`, app, cha_id, cha_type_name, cha_media, cha_sub_launch, duration, dau, installs, revenue, pv_video,
        pv_plaque, pv_banner, pv_splash, pv_msg, revenue_video, revenue_plaque, revenue_banner, revenue_splash,
        revenue_msg,country,out_duration,out_dau,out_installs,out_revenue,out_pv_video,out_pv_plaque,out_pv_banner,out_pv_splash,out_pv_msg,out_revenue_video,out_revenue_plaque,out_revenue_banner,out_revenue_splash,out_revenue_msg)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.day}, #{item.app}, #{item.cha_id}, #{item.cha_type_name}, #{item.cha_media}, #{item.cha_sub_launch},
            #{item.duration},#{item.dau}, #{item.installs},
            #{item.revenue}, #{item.pv_video}, #{item.pv_plaque},#{item.pv_banner},
            #{item.pv_splash}, #{item.pv_msg}, #{item.revenue_video}, #{item.revenue_plaque},
            #{item.revenue_banner}, #{item.revenue_splash}, #{item.revenue_msg},#{item.country},
#{item.out_duration},
#{item.out_dau},
#{item.out_installs},
#{item.out_revenue},
#{item.out_pv_video},
#{item.out_pv_plaque},
#{item.out_pv_banner},
#{item.out_pv_splash},
#{item.out_pv_msg},
#{item.out_revenue_video},
#{item.out_revenue_plaque},
#{item.out_revenue_banner},
#{item.out_revenue_splash},
#{item.out_revenue_msg}
            )
        </foreach>
    </insert>

</mapper>