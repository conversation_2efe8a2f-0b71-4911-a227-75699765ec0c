<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adv2.Adv2Mapper">

	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>

	<select id="selectExtendAdinfoVo" parameterType="com.wbgame.pojo.adv2.ExtendAdinfoVo" resultType="com.wbgame.pojo.adv2.ExtendAdinfoVo" >
	    select * from dn_extend_adinfo_manage 
	    where 1=1
	    <if test="appid != null and appid != ''">
		 	and appid = #{appid,jdbcType=VARCHAR}
		</if>
		<if test="cha_id != null and cha_id != ''">
		 	and cha_id = #{cha_id,jdbcType=VARCHAR}
		</if>
		<if test="prjid != null and prjid != ''">
		 	and prjid = #{prjid,jdbcType=VARCHAR}
		</if>
		<if test="shield_name != null and shield_name != ''">
			and shield_name = #{shield_name,jdbcType=VARCHAR}
		</if>
		<if test="policy_name != null and policy_name != ''">
			and policy_name = #{policy_name,jdbcType=VARCHAR}
		</if>
		<if test="buy_id != null and buy_id != ''">
		 	and buy_id = #{buy_id}
		</if>
		<if test="buy_act != null and buy_act != ''">
		 	and buy_act = #{buy_act}
		</if>
		ORDER BY `id` DESC
  	</select>
  	
	  <update id="updateExtendAdinfoVo" parameterType="com.wbgame.pojo.adv2.ExtendAdinfoVo">
	    update dn_extend_adinfo_manage
	    set 
	      appid = #{appid,jdbcType=INTEGER},
	      cha_id = #{cha_id,jdbcType=VARCHAR},
	      prjid = #{prjid,jdbcType=INTEGER},
	      user_group = #{user_group,jdbcType=VARCHAR},
	      shield_name = #{shield_name,jdbcType=VARCHAR},
	      policy_name = #{policy_name,jdbcType=VARCHAR},
	      statu = #{statu,jdbcType=VARCHAR},
	      euser = #{euser,jdbcType=VARCHAR},
	      endtime = now(),
	      buy_id = #{buy_id},
	      buy_act = #{buy_act}
	    where id = #{id,jdbcType=INTEGER}
	  </update>
	  
	  <update id="updateBatchExtendAdinfoVo" parameterType="com.wbgame.pojo.adv2.ExtendAdinfoVo">
	    update dn_extend_adinfo_manage
	    <set >
	     <if test="user_group != null and user_group != ''" >
        `user_group` = #{user_group,jdbcType=VARCHAR},
      	</if>
      	<if test="shield_name != null and shield_name != ''" >
        `shield_name` = #{shield_name,jdbcType=VARCHAR},
      	</if>
      	<if test="policy_name != null and policy_name != ''" >
        `policy_name` = #{policy_name,jdbcType=VARCHAR},
      	</if>
      	  statu = #{statu,jdbcType=VARCHAR},
	      euser = #{euser,jdbcType=VARCHAR},
	      endtime = now()
	    </set>
	    where id in (${id})
	  </update>
	  
	  <insert id="insertExtendAdinfoVo" parameterType="com.wbgame.pojo.adv2.ExtendAdinfoVo">
	    insert into dn_extend_adinfo_manage (appid, cha_id, prjid, buy_id, buy_act,
	      	user_group, shield_name, policy_name,statu,cuser,createtime,euser,endtime
	      )
	    values (#{appid}, #{cha_id}, #{prjid}, #{buy_id}, #{buy_act}, 
		    #{user_group,jdbcType=VARCHAR}, #{shield_name,jdbcType=VARCHAR}, #{policy_name,jdbcType=VARCHAR},
		    #{statu,jdbcType=VARCHAR}, #{cuser,jdbcType=VARCHAR}, now(), #{euser,jdbcType=VARCHAR}, now()
	      )
	  </insert>
	   <delete id="deleteExtendAdinfoVo" parameterType="com.wbgame.pojo.adv2.ExtendAdinfoVo">
	    delete from dn_extend_adinfo_manage
	    where id = #{id,jdbcType=INTEGER}
	  </delete>

	<update id="updateExtendAdshieldVo" parameterType="com.wbgame.pojo.adv2.ExtendAdshieldVo">
		update dn_extend_shield_manage
		set splash_shield_addr = #{splash_shield_addr,jdbcType=VARCHAR},
			video_shield_addr = #{video_shield_addr,jdbcType=VARCHAR},
			msg_shield_addr = #{msg_shield_addr,jdbcType=VARCHAR},
			banner_shield_addr = #{banner_shield_addr,jdbcType=VARCHAR},
			plaque_shield_addr = #{plaque_shield_addr,jdbcType=VARCHAR},
			icon_shield_addr = #{icon_shield_addr,jdbcType=VARCHAR},
			note = #{note}
		where shield_name = #{shield_name,jdbcType=VARCHAR}
	</update>
	<select id="selectExtendAdshieldVo" resultType="com.wbgame.pojo.adv2.ExtendAdshieldVo">
		select * from dn_extend_shield_manage
		where 1=1
		<if test="shield_name != null and shield_name != ''">
			and shield_name like concat('%',#{shield_name},'%')
		</if>
	</select>
	<insert id="insertExtendAdshieldVo" parameterType="com.wbgame.pojo.adv2.ExtendAdshieldVo">
		insert into dn_extend_shield_manage (
			shield_name, 
			splash_shield_addr,
			video_shield_addr, 
			msg_shield_addr, 
			banner_shield_addr,
			plaque_shield_addr, 
			icon_shield_addr,
			note )
		values (
			#{shield_name,jdbcType=VARCHAR},
			#{splash_shield_addr,jdbcType=VARCHAR},
			#{video_shield_addr,jdbcType=VARCHAR},
			#{msg_shield_addr,jdbcType=VARCHAR},
			#{banner_shield_addr,jdbcType=VARCHAR},
			#{plaque_shield_addr,jdbcType=VARCHAR},
			#{icon_shield_addr,jdbcType=VARCHAR},
			#{note}
		)
	</insert>
	<delete id="deleteExtendAdshieldVo" parameterType="com.wbgame.pojo.adv2.ExtendAdshieldVo">
		delete from dn_extend_shield_manage
		where shield_name = #{shield_name,jdbcType=VARCHAR}
	</delete>

	<select id="selectExtendAdtsVo" resultType="com.wbgame.pojo.adv2.ExtendAdtsVo">
		select * from dn_extend_adts_manage
		where 1=1
		<if test="policy_name != null and policy_name != ''">
			and policy_name like concat('%',#{policy_name},'%')
		</if>
		<if test="ad_type != null and ad_type != ''">
			and ad_type = #{ad_type,jdbcType=VARCHAR}
		</if>
	</select>
  	<update id="updateExtendAdtsVo" parameterType="com.wbgame.pojo.adv2.ExtendAdtsVo">
	    update dn_extend_adts_manage
	    set 
	      	 `ad_type` = #{ad_type,jdbcType=VARCHAR},
			 `plaque_interval` = #{plaque_interval,jdbcType=INTEGER},
			 `plaque_limit` = #{plaque_limit,jdbcType=INTEGER},
			 `plaque_day` = #{plaque_day,jdbcType=INTEGER},
			 `plaque_times` = #{plaque_times,jdbcType=INTEGER},
			 `video_interval` = #{video_interval,jdbcType=INTEGER},
			 `video_limit` = #{video_limit,jdbcType=INTEGER},
			 `video_day` = #{video_day,jdbcType=INTEGER},
			 `video_times` = #{video_times,jdbcType=INTEGER},
			 `banner_interval` = #{banner_interval,jdbcType=INTEGER},
			 `banner_limit` = #{banner_limit,jdbcType=INTEGER},
			 `banner_day` = #{banner_day,jdbcType=INTEGER},
			 `banner_times` = #{banner_times,jdbcType=INTEGER},
			 `msg_interval` = #{msg_interval,jdbcType=INTEGER},
			 `msg_limit` = #{msg_limit,jdbcType=INTEGER},
			 `msg_day` = #{msg_day,jdbcType=INTEGER},
			 `msg_times` = #{msg_times,jdbcType=INTEGER},
			 `splash_interval` = #{splash_interval,jdbcType=INTEGER},
			 `splash_limit` = #{splash_limit,jdbcType=INTEGER},
			 `splash_day` = #{splash_day,jdbcType=INTEGER},
			 `splash_times` = #{splash_times,jdbcType=INTEGER},
			 `icon_interval` = #{icon_interval,jdbcType=INTEGER},
			 `icon_limit` = #{icon_limit,jdbcType=INTEGER},
			 `icon_day` = #{icon_day,jdbcType=INTEGER},
			 `icon_times` = #{icon_times,jdbcType=INTEGER},
			 
			 `plaque_loadLimit` = #{plaque_loadLimit,jdbcType=INTEGER},
			 `video_loadLimit` = #{video_loadLimit,jdbcType=INTEGER},
			 `msg_loadLimit` = #{msg_loadLimit,jdbcType=INTEGER},
			 `banner_loadLimit` = #{banner_loadLimit,jdbcType=INTEGER},
			 `splash_loadLimit` = #{splash_loadLimit,jdbcType=INTEGER},
			 `icon_loadLimit` = #{icon_loadLimit,jdbcType=INTEGER},
			 
			 `plaque_timeout` = #{plaque_timeout,jdbcType=INTEGER},
			 `video_timeout` = #{video_timeout,jdbcType=INTEGER},
			 `msg_timeout` = #{msg_timeout,jdbcType=INTEGER},
			 `banner_timeout` = #{banner_timeout,jdbcType=INTEGER},
			 `splash_timeout` = #{splash_timeout,jdbcType=INTEGER},
			 `icon_timeout` = #{icon_timeout,jdbcType=INTEGER},
			 
			 `plaque_loop` = #{plaque_loop,jdbcType=INTEGER},
			 `video_loop` = #{video_loop,jdbcType=INTEGER},
			 `msg_loop` = #{msg_loop,jdbcType=INTEGER},
			 `banner_loop` = #{banner_loop,jdbcType=INTEGER},
			 `splash_loop` = #{splash_loop,jdbcType=INTEGER},
			 `icon_loop` = #{icon_loop,jdbcType=INTEGER},
			 
			 reLoadInterval = #{reLoadInterval},
			 checkInvalidInterval = #{checkInvalidInterval}, 
			 video_title = #{video_title},
			 note = #{note}

	    where policy_name = #{policy_name,jdbcType=VARCHAR}
  	</update>

  	<insert id="insertExtendAdtsVo" parameterType="com.wbgame.pojo.adv2.ExtendAdtsVo" >
	    insert into dn_extend_adts_manage (`policy_name`,`ad_type`,
					`plaque_interval`,
					`plaque_limit`,
					`plaque_day`,
					`plaque_times`,
					`video_interval`,
					`video_limit`,
					`video_day`,
					`video_times`,
					`banner_interval`,
					`banner_limit`,
					`banner_day`,
					`banner_times`,
					`msg_interval`,
					`msg_limit`,
					`msg_day`,
					`msg_times`,
					splash_interval,
					splash_limit,
					splash_day,
					splash_times,
					icon_interval,
					icon_limit,
					icon_day,
					icon_times,
					
					plaque_loadLimit,
					video_loadLimit,
					msg_loadLimit,
					banner_loadLimit,
					splash_loadLimit,
					icon_loadLimit,
					
					plaque_timeout,
					video_timeout,
					msg_timeout,
					banner_timeout,
					splash_timeout,
					icon_timeout,
					
					plaque_loop,
					video_loop,
					msg_loop,
					banner_loop,
					splash_loop,
					icon_loop,
					
					reLoadInterval,
					checkInvalidInterval,
					video_title,
					note

	      ) 
	    values (#{policy_name}, #{ad_type},
		    #{plaque_interval},#{plaque_limit},#{plaque_day},#{plaque_times},
		    #{video_interval},#{video_limit},#{video_day},#{video_times},
		    #{banner_interval},#{banner_limit},#{banner_day},#{banner_times},
		    #{msg_interval},#{msg_limit},#{msg_day},#{msg_times},
		    #{splash_interval},#{splash_limit},#{splash_day},#{splash_times},
		    #{icon_interval},#{icon_limit},#{icon_day},#{icon_times},
		    #{plaque_loadLimit},#{video_loadLimit},#{msg_loadLimit},#{banner_loadLimit},#{splash_loadLimit},#{icon_loadLimit},
		    #{plaque_timeout},#{video_timeout},#{msg_timeout},#{banner_timeout},#{splash_timeout},#{icon_timeout},
		    #{plaque_loop},#{video_loop},#{msg_loop},#{banner_loop},#{splash_loop},#{icon_loop},
		    #{reLoadInterval},#{checkInvalidInterval},#{video_title},#{note}
		)
  	</insert>
	<delete id="deleteExtendAdtsVo" parameterType="com.wbgame.pojo.adv2.ExtendAdtsVo">
		delete from dn_extend_adts_manage
		where policy_name = #{policy_name,jdbcType=VARCHAR}
	</delete>

	<select id="selectShieldName" resultType="String">
		select shield_name from dn_extend_shield_manage
	</select>

	<select id="selectPolicyName" resultType="String">
		select policy_name from dn_extend_adts_manage
	</select>
  	
  	
  	<!-- 聚合数据查询  -->
  	<select id="selectDnGroupData" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_group_sql"/>
	</select>
	<select id="selectDnGroupDataSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.income) income,
			SUM(xx.req_num) req_num,
			SUM(xx.fill_num) fill_num,
			SUM(xx.show_num) show_num,
			SUM(xx.click_num) click_num,
			TRUNCATE(sum(xx.income)/sum(xx.show_num)*1000,2) ecpm,
			CONCAT(ROUND(sum(xx.fill_num)/sum(xx.req_num)*100, 2),'%') fill_rate,
			CONCAT(ROUND(sum(xx.show_num)/sum(xx.fill_num)*100, 2),'%') show_rate,
			CONCAT(ROUND(sum(xx.click_num)/sum(xx.show_num)*100, 2),'%') click_rate
			
		from (<include refid="dn_group_sql"/>) xx
	</select>
	<sql id="dn_group_sql">
		select 
			<if test="group != null and group != ''">
			${group},
			</if>
			concat(tdate,'') date,
			appid,
			<if test="ver == null or ver == ''">
			cha_id,
			</if>
			strategy,
			statu,
			adpos_type,
			ecpm,
			rate,
			TRUNCATE(ecpm*sum(aa.show_num)/1000,2) income,
			SUM(aa.req_num) req_num,
			SUM(aa.fill_num) fill_num,
			SUM(aa.show_num) show_num,
			SUM(aa.click_num) click_num,
			CONCAT(ROUND(sum(aa.fill_num)/sum(aa.req_num)*100, 2),'%') fill_rate,
			CONCAT(ROUND(sum(aa.show_num)/sum(aa.fill_num)*100, 2),'%') show_rate,
			CONCAT(ROUND(sum(aa.click_num)/sum(aa.show_num)*100, 2),'%') click_rate
		from ${tableName} aa 
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid}
		</if>
		<if test="statu != null and statu != ''">
			and statu = #{statu} 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="user_group != null and user_group != ''">
			and user_group = #{user_group} 
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid = #{adsid} 
		</if>
		
		<if test="group != null and group != ''">
		 	group by ${group} 
		</if>
		 order by tdate asc,adpos_type asc,ecpm desc 
	</sql>
	
	<!-- 聚合综合查询  -->
  	<select id="selectDnGroupCom" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_group_com_sql"/>
	</select>
	<select id="selectDnGroupComSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.income) income,
			SUM(xx.req_num) req_num,
			SUM(xx.fill_num) fill_num,
			SUM(xx.show_num) show_num,
			SUM(xx.click_num) click_num,
			CONCAT(TRUNCATE(sum(xx.fill_num)/sum(xx.req_num)*100, 1),'%') fill_rate,
			CONCAT(TRUNCATE(sum(xx.show_num)/sum(xx.fill_num)*100, 1),'%') show_rate,
			CONCAT(TRUNCATE(sum(xx.click_num)/sum(xx.show_num)*100, 1),'%') click_rate,
			
			SUM(xx.platform_show) platform_show,
			SUM(xx.platform_fill) platform_fill,
			SUM(xx.platform_req) platform_req,
			CONCAT(TRUNCATE((SUM(xx.platform_show)-SUM(xx.show_num)) / SUM(xx.platform_show) * 100, 1),'%') show_gap,
			CONCAT(TRUNCATE((SUM(xx.platform_fill)-SUM(xx.fill_num)) / SUM(xx.platform_fill) * 100, 1),'%') fill_gap,
			CONCAT(TRUNCATE((SUM(xx.platform_req)-SUM(xx.req_num)) / SUM(xx.platform_req) * 100, 1),'%') req_gap
		from (<include refid="dn_group_com_sql"/>) xx
	</select>
	<sql id="dn_group_com_sql">
		select 
			concat(tdate,'') date,
			appid,
			cha_id,
			adsid,
			strategy,
			adpos_type,
			agent,
			sdk_adtype,
			ecpm,
			is_newuser,
			SUM(aa.income) income,
			SUM(aa.req_num) req_num,
			SUM(aa.fill_num) fill_num,
			SUM(aa.show_num) show_num,
			SUM(aa.click_num) click_num,
			aa.ad_load_duration,
			CONCAT(TRUNCATE(sum(aa.fill_num)/sum(aa.req_num)*100, 1),'%') fill_rate,
			CONCAT(TRUNCATE(sum(aa.show_num)/sum(aa.fill_num)*100, 1),'%') show_rate,
			CONCAT(TRUNCATE(sum(aa.click_num)/sum(aa.show_num)*100, 1),'%') click_rate,
			
			platform_ecpm,
			CONCAT(TRUNCATE((platform_ecpm-ecpm) / ecpm * 100, 1),'%') ecpm_gap,
			SUM(aa.platform_show) platform_show,
			CONCAT(TRUNCATE((SUM(aa.platform_show)-SUM(aa.show_num)) / SUM(aa.platform_show) * 100, 1),'%') show_gap,
			SUM(aa.platform_fill) platform_fill,
			CONCAT(TRUNCATE((SUM(aa.platform_fill)-SUM(aa.fill_num)) / SUM(aa.platform_fill) * 100, 1),'%') fill_gap,
			SUM(aa.platform_req) platform_req,
			CONCAT(TRUNCATE((SUM(aa.platform_req)-SUM(aa.req_num)) / SUM(aa.platform_req) * 100, 1),'%') req_gap
			
		from dn_extend_group_sum aa
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id = #{cha_id} 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid = #{adsid} 
		</if>
		<if test="agent != null and agent != ''">
			and agent = #{agent} 
		</if>
		<if test="sdk_adtype != null and sdk_adtype != ''">
			and sdk_adtype = #{sdk_adtype} 
		</if>
		<if test="is_newuser != null and is_newuser != ''">
			and is_newuser in (${is_newuser}) 
		</if>
		<if test="strategy != null and strategy != ''">
			and strategy like concat('%',#{strategy},'%') 
		</if>
		<if test="income_beyond != null and income_beyond != ''">
			and income > 0
		</if>
		group by tdate,adsid
		order by tdate asc,strategy asc,ecpm desc
	</sql>
	
  	<!-- 变现-广告位数据查询  -->
  	<select id="selectAdposData" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_adpos_sql"/>
	</select>
	<select id="selectAdposDataSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.income) income,
			SUM(xx.show_num) show_num,
			SUM(xx.click_num) click_num,
			CONCAT(ROUND(sum(xx.click_num)/sum(xx.show_num)*100, 2),'%') click_rate
			<!-- CONCAT(ROUND(sum(xx.device_num)/xx.dau*100,2),'%') as seep_rate -->
		from (<include refid="dn_adpos_sql"/>) xx
	</select>
	<sql id="dn_adpos_sql">
		select 
			concat(appid,cha_id,adpos) mapkey,
			concat(tdate,'') tdate,
			appid,cha_id,prjid ${group},
			dau,
			TRUNCATE(sum(income),2) income,
			SUM(show_num) show_num,
			SUM(click_num) click_num,
			SUM(device_num) device_num,
			CONCAT(ROUND(sum(click_num)/sum(show_num)*100, 2),'%') click_rate,
			CONCAT(ROUND(sum(device_num)/dau*100,2),'%') as seep_rate,
			TRUNCATE(sum(show_num)/dau, 1) as per_pv,
			TRUNCATE(sum(show_num)/sum(device_num), 1) as seep_per_pv
		from yyhz_0308.dn_extend_adpos_data 
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid}
		</if>
		<if test="adpos != null and adpos != ''">
			and adpos = #{adpos} 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid = #{adsid} 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>
		
		group by tdate,appid,cha_id,prjid ${group}
		order by tdate asc,adpos_type asc,income desc 
	</sql>
	
	<select id="selectDnExIncomeData" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_exincome_sql"/>
	</select>
	<select id="selectDnExIncomeDataSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.income) income,
			SUM(xx.req_num) req_num,
			SUM(xx.fill_num) fill_num,
			SUM(xx.show_num) show_num,
			SUM(xx.click_num) click_num,
			CONCAT(ROUND(sum(xx.fill_num)/sum(xx.req_num)*100, 2),'%') fill_rate,
			CONCAT(ROUND(sum(xx.show_num)/sum(xx.fill_num)*100, 2),'%') show_rate,
			CONCAT(ROUND(sum(xx.click_num)/sum(xx.show_num)*100, 2),'%') click_rate
		from (<include refid="dn_exincome_sql"/>) xx
	</select>
	<sql id="dn_exincome_sql">
		select 
			<if test="group != null and group != ''">
			${group},
			</if>
			bb.strategy,
			concat(tdate,'') date,
			sdk_adtype,
			agent,
			ecpm,
			act_num,
			add_num,
			SUM(aa.income) income,
			SUM(aa.req_num) req_num,
			SUM(aa.fill_num) fill_num,
			SUM(aa.show_num) show_num,
			SUM(aa.click_num) click_num,
			CONCAT(ROUND(sum(aa.fill_num)/sum(aa.req_num)*100, 2),'%') fill_rate,
			CONCAT(ROUND(sum(aa.show_num)/sum(aa.fill_num)*100, 2),'%') show_rate,
			CONCAT(ROUND(sum(aa.click_num)/sum(aa.show_num)*100, 2),'%') click_rate
		from ${tableName} aa LEFT JOIN yyhz_0308.dn_extend_adsid_list bb ON aa.adsid=bb.sid
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid}
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid like concat('%',#{adsid},'%') 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="user_group != null and user_group != ''">
			and user_group like concat('%',#{user_group},'%') 
		</if>
		<if test="agent != null and agent != ''">
			and agent = #{agent} 
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name = #{cha_type_name}
		</if>
		<if test="strategy != null and strategy != ''">
			and bb.strategy = #{strategy}
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>
		
		<if test="group != null and group != ''">
		 	group by ${group} 
		</if>
		<if test="order_str != null and order_str != ''">
		 	order by ${order_str} 
		</if>
	</sql>
	
	<!-- 数据监控-变现数据汇总  -->
	<select id="selectExtendRevenueReport" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT date,agent `group`,TRUNCATE(sum(revenue),2) revenue,sum(pv) impressions,
			TRUNCATE(sum(return_count)/sum(request_count)*100, 2) filled_rate,
			TRUNCATE(sum(revenue)/sum(pv)*1000,2) ecpm
		FROM dnwx_cfg.dn_extend_revenue_report where date BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and dnappid in (${appid}) 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and placement_type = #{adpos_type} 
		</if>
		GROUP BY date,agent
		ORDER BY date,agent
	</select>
	
	<update id="editExtendAdsidVo" parameterType="com.wbgame.pojo.adv2.ExtendAdsidVo" >
	    update dn_extend_adsid_manage
	    <set>
	      <if test="agent != null and agent != ''" >
	        agent = #{agent,jdbcType=VARCHAR},
	      </if>
	      <if test="sdk_code != null and sdk_code != ''" >
	        sdk_code = #{sdk_code,jdbcType=VARCHAR},
	      </if>
	      <if test="sdk_appid != null and sdk_appid != ''" >
	        sdk_appid = #{sdk_appid,jdbcType=VARCHAR},
	      </if>
	      <if test="sdk_appkey != null and sdk_appkey != ''" >
	        sdk_appkey = #{sdk_appkey,jdbcType=VARCHAR},
	      </if>
	      <if test="sdk_adtype != null and sdk_adtype != ''" >
	        sdk_adtype = #{sdk_adtype,jdbcType=VARCHAR},
	      </if>
	      <if test="note != null and note != ''" >
	        note = #{note,jdbcType=VARCHAR},
	      </if>
	      <if test="appid != null and appid != ''" >
	        appid = #{appid,jdbcType=INTEGER},
	      </if>
	      <if test="cha_id != null and cha_id != ''" >
	        cha_id = #{cha_id,jdbcType=VARCHAR},
	      </if>
	      <if test="cuser != null and cuser != ''" >
	        cuser = #{cuser,jdbcType=VARCHAR},
	      </if>
		  <if test="bidding != null and bidding != ''" >
			bidding = #{bidding,jdbcType=VARCHAR},
		  </if>
		  <if test="unit_id != null and unit_id != ''" >
			unit_id = #{unit_id,jdbcType=VARCHAR},
		  </if>
		  <if test="params != null and params != ''" >
			  params = #{params,jdbcType=VARCHAR},
		  </if>
		  <if test="out != null and out != ''" >
			  `out` = #{out},
		  </if>
	      	lasttime = now()
	    </set>
	    where adsid in ('${adsid}')
	</update>

	<select id="selectExtendGroupMonitor" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.DnExtendGroupMonitorVo">
		<include refid="dn_group_monitor_sql"/>
	</select>
	<sql id="dn_group_monitor_sql">
		SELECT tdate,appid,prjid,cha_id,SUM(actnum) actnum,SUM(addnum) addnum,SUM(income) income,
			CONCAT(TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1),'%') addrate,
			TRUNCATE(SUM(income) / SUM(actnum), 1) dau_arpu,
			TRUNCATE(SUM(plaque_pv) / SUM(actnum), 1) plaque_per_pv,
			TRUNCATE(SUM(plaque_income) / SUM(plaque_pv)*1000, 1) plaque_ecpm,
			TRUNCATE(SUM(video_pv) / SUM(actnum), 1) video_per_pv,
			TRUNCATE(SUM(video_income) / SUM(video_pv)*1000, 1) video_ecpm,
			TRUNCATE(SUM(msg_pv) / SUM(actnum), 1) msg_per_pv,
			TRUNCATE(SUM(msg_income) / SUM(msg_pv)*1000, 1) msg_ecpm
		
		FROM dnwx_bi.dn_extend_group_monitor WHERE tdate BETWEEN #{sdate} AND #{edate} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid in (${prjid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>
		and actnum >= 50 
		GROUP BY tdate, appid, prjid, cha_id 
		ORDER BY tdate desc,actnum desc
	</sql>
	
	<!-- 变现-数据gap统计  -->
  	<select id="selectDnShowGapTotal" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_showgap_sql"/>
	</select>
	<select id="selectDnShowGapTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.pv) pv,
			SUM(xx.selfshow_num) selfshow_num,
			CONCAT(TRUNCATE((sum(xx.selfshow_num)-sum(xx.pv))/sum(xx.selfshow_num)*100, 1),'%') pv_gap,
			SUM(xx.click) click,
			SUM(xx.click_num) click_num,
			CONCAT(TRUNCATE((sum(xx.click_num)-sum(xx.click))/sum(xx.click_num)*100, 1),'%') click_gap
		from (<include refid="dn_showgap_sql"/>) xx
	</select>
	<sql id="dn_showgap_sql">
		select 
			${group},
			SUM(pv) pv,
			SUM(selfshow_num) selfshow_num,
			SUM(click) click,
			SUM(click_num) click_num,
			CONCAT(TRUNCATE((sum(selfshow_num)-sum(pv))/sum(selfshow_num)*100, 1),'%') pv_gap,
			CONCAT(TRUNCATE((sum(click_num)-sum(click))/sum(click_num)*100, 1),'%') click_gap
		from yyhz_0308.dn_show_gap 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid in (${adsid}) 
		</if>
		<if test="agent != null and agent != ''">
			and agent = #{agent} 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>
		AND adpos_type not in ('banner')
		
		group by ${group}
		order by tdate desc,pv desc
	</sql>
	
	<!-- 变现-自推广收入分析 -->
  	<select id="selectSelfPromotionIncome" parameterType="java.util.Map" resultType="java.util.Map">
		select
		tdate,
		appid,
		adpos_type,
		open_type,
		revenue,
		pv,
		ecpm,

		CONCAT(headline_pv, '%') headline_pv,
		CONCAT(gdt_pv, '%') gdt_pv,
		CONCAT(kuaishou_pv, '%') kuaishou_pv,
		CONCAT(mobvista_pv, '%') mobvista_pv,
		CONCAT(admob_pv, '%') admob_pv,
		CONCAT(sigmob_pv, '%') sigmob_pv,

		CONCAT(headline_revenue, '%') headline_revenue,
		CONCAT(gdt_revenue, '%') gdt_revenue,
		CONCAT(kuaishou_revenue, '%') kuaishou_revenue,
		CONCAT(mobvista_revenue, '%') mobvista_revenue,
		CONCAT(admob_revenue, '%') admob_revenue,
		CONCAT(sigmob_revenue, '%') sigmob_revenue,

		headline_ecpm,
		gdt_ecpm,
		kuaishou_ecpm,
		mobvista_ecpm,
		admob_ecpm,
		sigmob_ecpm
		from (<include refid="dn_spincome_sql"/>) t
	</select>
	<!-- <select id="selectSelfPromotionIncomeSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.pv) pv,
			SUM(xx.selfshow_num) selfshow_num,
			CONCAT(TRUNCATE((sum(xx.selfshow_num)-sum(xx.pv))/sum(xx.selfshow_num)*100, 1),'%') pv_gap
		from (<include refid="dn_spincome_sql"/>) xx
	</select> -->
	<sql id="dn_spincome_sql">
		select
		tdate,
		appid,
		adpos_type,
		open_type,
		IFNULL(revenue,0) revenue,
		IFNULL(pv,0) pv,
		ifnull(convert(ecpm, DECIMAL(10,2)),0) ecpm,
		IFNULL(TRUNCATE(headline_pv/pv*100, 1),0.0) headline_pv,
		IFNULL(TRUNCATE(gdt_pv/pv*100, 1),0.0) gdt_pv,
		IFNULL(TRUNCATE(kuaishou_pv/pv*100, 1),0.0) kuaishou_pv,
		IFNULL(TRUNCATE(mobvista_pv/pv*100, 1),0.0) mobvista_pv,
		IFNULL(TRUNCATE(admob_pv/pv*100, 1),0.0) admob_pv,
		IFNULL(TRUNCATE(sigmob_pv/pv*100, 1),0.0) sigmob_pv,

		IFNULL(TRUNCATE(headline_revenue/revenue*100, 1),0.0) headline_revenue,
		IFNULL(TRUNCATE(gdt_revenue/revenue*100, 1),0.0) gdt_revenue,
		IFNULL(TRUNCATE(kuaishou_revenue/revenue*100, 1),0.0) kuaishou_revenue,
		IFNULL(TRUNCATE(mobvista_revenue/revenue*100, 1),0.0) mobvista_revenue,
		IFNULL(TRUNCATE(admob_revenue/revenue*100, 1),0.0) admob_revenue,
		IFNULL(TRUNCATE(sigmob_revenue/revenue*100, 1),0.0) sigmob_revenue,

		IFNULL(TRUNCATE(headline_revenue/headline_pv*1000,2), 0) headline_ecpm,
		IFNULL(TRUNCATE(gdt_revenue/gdt_pv*1000,2), 0) gdt_ecpm,
		IFNULL(TRUNCATE(kuaishou_revenue/kuaishou_pv*1000,2), 0) kuaishou_ecpm,
		IFNULL(TRUNCATE(mobvista_revenue/mobvista_pv*1000,2), 0) mobvista_ecpm,
		IFNULL(TRUNCATE(admob_revenue/admob_pv*1000,2), 0) admob_ecpm,
		IFNULL(TRUNCATE(sigmob_revenue/sigmob_pv*1000,2), 0) sigmob_ecpm

		from dn_selfpromotion_income
		where tdate BETWEEN #{sdate} AND #{edate} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="open_type != null and open_type != ''">
			and open_type = #{open_type} 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate desc,pv desc
			</otherwise>
		</choose>
	</sql>
	
	<!-- 变现收入校准  -->
  	<select id="selectExtendIncomeRevise" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_revise_income_sql"/>
	</select>
	<select id="selectExtendIncomeReviseSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.self_show) self_show,
			SUM(xx.revise_show) revise_show,
			SUM(xx.revise_revenue) revise_revenue
			<!-- CONCAT(TRUNCATE((sum(xx.selfshow_num)-sum(xx.pv))/sum(xx.selfshow_num)*100, 1),'%') pv_gap -->
		from (<include refid="dn_revise_income_sql"/>) xx
	</select>
	<sql id="dn_revise_income_sql">
		select 
			tdate,appid,cha_id,prjid,adsid,adpos_type,
			ecpm,
			gap_val,
			self_show,
			TRUNCATE(self_show*(1-gap_val*0.01),0) revise_show,
			TRUNCATE(ecpm*(self_show*(1-gap_val*0.01))/1000, 2) revise_revenue,
			actnum,
			addnum
		from yyhz_0308.dn_extend_revise_income 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid}
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid in (${adsid}) 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>
		
		order by tdate desc,self_show desc
	</sql>
	
	<!-- 汇总数据校准  -->
  	<select id="selectAdtypeTotalRevise" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_revise_adtype_sql"/>
	</select>
	<select id="selectAdtypeTotalReviseSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.actnum) actnum,
			SUM(xx.addnum) addnum,
			CONCAT(TRUNCATE(SUM(xx.addnum) / SUM(xx.actnum)*100, 1),'%') addrate,
			SUM(xx.sum_revenue) sum_revenue,
			
			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.addnum), 2),0) add_arpu,
			
			
			IFNULL(TRUNCATE(SUM(xx.pv_splash) / SUM(xx.actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque) / SUM(xx.actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner) / SUM(xx.actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video) / SUM(xx.actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg) / SUM(xx.actnum), 1),0) avg_pv_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.pv_msg)*1000, 1),0) ecpm_msg,
			
			IFNULL(TRUNCATE(SUM(xx.pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg), 0),0) pv_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg), 2),0) revenue_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.actnum), 2),0) arpu_msg
			
		from (<include refid="dn_revise_adtype_sql"/>) xx
	</select>
	<sql id="dn_revise_adtype_sql">
		select 
			${group},
			SUM(actnum) actnum,
			SUM(addnum) addnum,
			CONCAT(TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1),'%') addrate,
			SUM(sum_revenue) sum_revenue,
			IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(addnum), 2),0) add_arpu,
			
			
			IFNULL(TRUNCATE(SUM(pv_splash) / SUM(actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque) / SUM(actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner) / SUM(actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video) / SUM(actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg) / SUM(actnum), 1),0) avg_pv_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(pv_msg)*1000, 1),0) ecpm_msg,
			
			IFNULL(TRUNCATE(SUM(pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg), 0),0) pv_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(revenue_msg), 2),0) revenue_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(actnum), 2),0) arpu_msg
			
		from yyhz_0308.dn_extend_revise_adtype 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name}) 
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media}) 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>
		
		group by ${group} 
		order by tdate desc,actnum desc
	</sql>

	<!-- 二级子渠道展示收入  -->
  	<select id="selectSubchaTotal" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_subcha_total_sql"/>
	</select>
	<select id="selectSubchaTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
			SUM(xx.actnum) actnum,
			SUM(xx.addnum) addnum,
			CONCAT(TRUNCATE(SUM(xx.actnum) / SUM(xx.addnum)*100, 1),'%') addrate,
			SUM(xx.sum_revenue) sum_revenue,

			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.actnum), 2),0) add_arpu,


			IFNULL(TRUNCATE(SUM(xx.pv_splash) / SUM(xx.actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque) / SUM(xx.actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner) / SUM(xx.actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video) / SUM(xx.actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg) / SUM(xx.actnum), 1),0) avg_pv_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.pv_msg)*1000, 1),0) ecpm_msg,

			IFNULL(TRUNCATE(SUM(xx.pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg), 0),0) pv_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg), 2),0) revenue_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.actnum), 2),0) arpu_msg

		from (<include refid="dn_subcha_total_sql"/>) xx
	</select>
	<sql id="dn_subcha_total_sql">
		select
			<if test="group != null and group != ''">
				 ${group},
			</if>
			actnum,
			addnum,
			CONCAT(TRUNCATE(addnum / actnum*100, 1),'%') addrate,
			SUM(sum_revenue) sum_revenue,
			IFNULL(TRUNCATE(SUM(sum_revenue) / actnum, 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(sum_revenue) / addnum, 2),0) add_arpu,


			IFNULL(TRUNCATE(SUM(pv_splash) / actnum, 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque) / actnum, 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner) / actnum, 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video) / actnum, 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg) / actnum, 1),0) avg_pv_msg,

			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(pv_msg)*1000, 1),0) ecpm_msg,

			IFNULL(TRUNCATE(SUM(pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg), 0),0) pv_msg,

			IFNULL(TRUNCATE(SUM(revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(revenue_msg), 2),0) revenue_msg,

			IFNULL(TRUNCATE(SUM(revenue_splash) / actnum, 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / actnum, 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / actnum, 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / actnum, 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / actnum, 2),0) arpu_msg

		from dnwx_bi.dn_extend_subcha_total
		where tdate BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id})
		</if>
		<if test="sub_cha != null and sub_cha != ''">
			and sub_cha in (${sub_cha})
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media})
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>

		<if test="group != null and group != ''">
			group by ${group}
		</if>

		order by tdate desc,actnum desc
	</sql>

	<!-- 项目ID收入预估  -->
  	<select id="selectPrjidTotalIncome" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_prjid_income_sql"/>
	</select>
	<select id="selectPrjidTotalIncomeSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.actnum) actnum,
			SUM(xx.addnum) addnum,
			CONCAT(TRUNCATE(SUM(xx.actnum) / SUM(xx.addnum)*100, 1),'%') addrate,
			SUM(xx.sum_revenue) sum_revenue,
			
			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.actnum), 2),0) add_arpu,
			
			
			IFNULL(TRUNCATE(SUM(xx.pv_splash) / SUM(xx.actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque) / SUM(xx.actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner) / SUM(xx.actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video) / SUM(xx.actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg) / SUM(xx.actnum), 1),0) avg_pv_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.pv_msg)*1000, 1),0) ecpm_msg,
			
			IFNULL(TRUNCATE(SUM(xx.pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg), 0),0) pv_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg), 2),0) revenue_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.actnum), 2),0) arpu_msg
			
		from (<include refid="dn_prjid_income_sql"/>) xx
	</select>
	<sql id="dn_prjid_income_sql">
		select 
			tdate,appid,prjid,cha_id,cha_type_name,cha_media,
			actnum,
			addnum,
			CONCAT(TRUNCATE(addnum / actnum*100, 1),'%') addrate,
			SUM(sum_revenue) sum_revenue,
			IFNULL(TRUNCATE(SUM(sum_revenue) / actnum, 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(sum_revenue) / addnum, 2),0) add_arpu,
			
			
			IFNULL(TRUNCATE(SUM(pv_splash) / actnum, 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque) / actnum, 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner) / actnum, 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video) / actnum, 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg) / actnum, 1),0) avg_pv_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(pv_msg)*1000, 1),0) ecpm_msg,
			
			IFNULL(TRUNCATE(SUM(pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg), 0),0) pv_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(revenue_msg), 2),0) revenue_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash) / actnum, 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / actnum, 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / actnum, 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / actnum, 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / actnum, 2),0) arpu_msg
			
		from yyhz_0308.dn_extend_prjid_income 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid in (${prjid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name}) 
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media}) 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>

		group by tdate,prjid,cha_id
		order by tdate desc,actnum desc
	</sql>

	<select id="getDnAdSid" resultType="com.wbgame.pojo.adv2.ExtendAdsidVo" parameterType="java.lang.String">
        select * from dn_extend_adsid_manage where adsid =#{adsid}
    </select>

	<select id="getUserStrategyConfigList" resultType="com.wbgame.pojo.adv2.UserStrategyV2Vo" parameterType="java.util.Map">
		select * from dn_extend_user_strategy_two where 1=1
		<if test="sid != null and sid != ''">
			and sid like concat('%',#{sid},'%')
		</if>
		<if test="group != null and group != ''">
			and `group` = #{group}
		</if>
		<if test="stype != null and stype != ''">
			and `stype` = #{stype}
		</if>
		<if test="param1 != null and param1 != ''">
			and `param1` = #{param1}
		</if>
		<if test="param2 != null and param2 != ''">
			and `param2` = #{param2}
		</if>
		<if test="param3 != null and param3 != ''">
			and param3 like concat('%',#{param3},'%')
		</if>
		ORDER BY sid ,`group`
	</select>

	<insert id="copyUserStrategyConfigList" parameterType="com.wbgame.pojo.adv2.UserStrategyV2Vo">
		insert into  dn_extend_user_strategy_two_backend
		    (sid,`group`,stype,note,param1,param2,param3,createtime,cuser,euser,endtime)
		select sid,`group`,stype,note,param1,param2,param3,createtime,cuser,euser,endtime
		from  dn_extend_user_strategy_two a
		where a.sid =#{sid} and `group` = #{group}
	</insert>

	<insert id="saveUserStrategyConfigList" parameterType="java.util.List">
		insert into dn_extend_user_strategy_two(sid,`group`,stype,note,
		param1,param2,param3,cuser,createtime,euser,endtime) values
		<foreach collection="list" item="li" separator=",">
			(#{li.sid},
			#{li.group},
			#{li.stype},
			#{li.note},
			#{li.param1},
			#{li.param2},
			#{li.param3},
			#{li.cuser},
			now(),
			#{li.euser},
			now())
		</foreach>
	</insert>

	<delete id="delUserStrategyConfig" parameterType="com.wbgame.pojo.adv2.UserStrategyV2Vo">
		delete from dn_extend_user_strategy_two where sid =#{sid} and `group` = #{group}
	</delete>

	<insert id="insertDnExtendAdconfigCSJ" parameterType="com.wbgame.pojo.adv2.ExtendAdconfigVo">
		insert into dn_extend_adconfig
		    (appid,is_newuser,user_group,adpos_type,strategy,adsid,statu,ecpm,priority,rate,createtime,lasttime,cuser)
		values
		    (#{appid},#{is_newuser},#{user_group},#{adpos_type},#{strategy},#{adsid},#{statu},#{ecpm},#{priority},#{rate},now(),now(),#{cuser})
	</insert>

	<insert id="insertDnExtendAdconfig" parameterType="com.wbgame.pojo.adv2.ExtendAdconfigVo">
		insert into
		    dn_extend_adconfig
		    (appid,cha_id,is_newuser,user_group,adpos_type,strategy,adsid,statu,ecpm,priority,rate,createtime,lasttime,cuser)
		values
		    (#{appid},#{cha_id},#{is_newuser},#{user_group},#{adpos_type},#{strategy},#{adsid},#{statu},#{ecpm},#{priority},#{rate},now(),now(),#{cuser})

	</insert>
	<insert id="insertDnExtendAdsidManage" parameterType="com.wbgame.pojo.adv2.ExtendAdsidVo">
		insert into dn_extend_adsid_manage
		    (adsid,agent,sdk_code,sdk_appid,sdk_appkey,sdk_adtype,adpos_type,note,
		     appid,cha_id,createtime,lasttime,cuser,cpmFloor,open_type,bidding,params)
		values
		    (#{adsid},#{agent},#{sdk_code},#{sdk_appid},#{sdk_appkey},#{sdk_adtype},#{adpos_type},#{note},
		     #{appid},#{cha_id},now(),now(),#{cuser},#{cpmFloor},#{open_type},#{bidding},#{params})
		</insert>

	<insert id="insertDnExtendAdsidManageV2" parameterType="com.wbgame.pojo.adv2.ExtendAdsidVo">
		insert into dn_extend_adsid_manage
		(adsid,agent,sdk_code,sdk_appid,sdk_appkey,sdk_adtype,adpos_type,note,
		 appid,cha_id,createtime,lasttime,cuser,cpmFloor,open_type,bidding,params,`out`)
		values
			(#{adsid},#{agent},#{sdk_code},#{sdk_appid},#{sdk_appkey},#{sdk_adtype},#{adpos_type},#{note},
			 #{appid},#{cha_id},now(),now(),#{cuser},#{cpmFloor},#{open_type},#{bidding},#{params},#{out})
	</insert>

	<select id="selectAdconfigToAdpos" resultType="com.wbgame.pojo.adv2.ExtendAdposVo" parameterType="java.util.Map">

		SELECT xx.adpos,xx.adstyle,xx.adtype,xx.channel,xx.custom_param,
				IFNULL(appid,'${appid}') appid,IFNULL(cha_id,'${cha_id}') cha_id,IFNULL(prjid,'${prjid}') prjid,
		       	IFNULL(xx.user_group, IFNULL(yy.user_group,'${user_group}')) user_group,
		       	IFNULL(adpos_type,xx.adtype) adpos_type,config_first,
		       	case when config_first = 1 then IFNULL(xx.strategy, yy.strategy)
					else IFNULL(yy.strategy, xx.strategy) end as strategy

		FROM dn_config_default_adpos xx
		LEFT JOIN

			(select appid,IF(cha_id='',NULL,cha_id) cha_id,IF(prjid='',NULL,prjid) prjid,user_group,adpos_type,strategy  from dn_extend_adconfig
				where statu=1 and appid=#{appid} and user_group=#{user_group}

			<choose>
				<when test="cha_id != null and cha_id != ''">
					and cha_id=#{cha_id}
				</when>
				<otherwise>
					and (cha_id = '' or cha_id is null)
				</otherwise>
			</choose>

			<choose>
				<when test="prjid != null and prjid != ''">
					and prjid=#{prjid}
				</when>
				<otherwise>
					and (prjid = '' or prjid is null)
				</otherwise>
			</choose>
			group by appid,IF(cha_id='',NULL,cha_id),IF(prjid='',NULL,prjid),user_group,adpos_type ) yy

		ON xx.adtype=yy.adpos_type
		where xx.channel=#{channel}
		and ((xx.user_group is null or xx.user_group = '') or xx.user_group = #{user_group})

	</select>

	<select id="selectAccountListByPlatform" resultType="com.wbgame.pojo.adv2.CashCommonApiAccountVo">
		select account, accountName, platform, company
		from dnwx_cfg.dn_api_platform_account
		where platform = #{platform}
	</select>
	<select id="selectSecretByAccount" resultType="com.wbgame.pojo.adv2.CashCommonApiAccountVo">
		select  secret
		from dnwx_cfg.dn_api_platform_account
		where accountName = #{accountName}
	</select>

	<!-- 没有使用 -->
	<select id="selectSdkRelationReport" parameterType="java.util.Map" resultType="java.util.Map">
		select
		*
		from dn_extend_sdk_relation
		where tdate BETWEEN #{sdate} AND #{edate}
		<include refid="dn_extend_where_sql"/>
		<if test="sdk_name != null and sdk_name != ''">
			and sdk_name like concat('%',#{sdk_name},'%')
		</if>
		<if test="ver != null and ver != ''">
			and ver like concat('%',#{ver},'%')
		</if>
		<if test="local_ver != null and local_ver != ''">
			and local_ver like concat('%',#{local_ver},'%')
		</if>

	</select>


	<sql id="dn_extend_where_sql">
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid in (${prjid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id})
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type}
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media})
		</if>
		<if test="cha_sub_launch != null and cha_sub_launch != ''">
			and cha_sub_launch in (${cha_sub_launch})
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>
	</sql>

	<insert id="insertSelfPromotionIncome" parameterType="java.lang.String">
		REPLACE INTO dn_selfpromotion_income(tdate,appid,adpos_type,open_type,revenue,pv,ecpm,headline_pv,gdt_pv,kuaishou_pv,mobvista_pv,admob_pv,sigmob_pv,headline_revenue,gdt_revenue,kuaishou_revenue,mobvista_revenue,admob_revenue,sigmob_revenue)
		select '${tdate}' tdate,dnappid,placement_type,bb.open_type,SUM(revenue) revenue,SUM(pv) pv,TRUNCATE(SUM(revenue)/SUM(pv)*1000,2) ecpm,
			   SUM(CASE WHEN agent = 'headline' THEN pv ELSE 0 END) headline_pv,
			   SUM(CASE WHEN agent = 'gdt' THEN pv ELSE 0 END) gdt_pv,
			   SUM(CASE WHEN agent = 'kuaishou' THEN pv ELSE 0 END) kuaishou_pv,
			   SUM(CASE WHEN agent = 'mobvista' THEN pv ELSE 0 END) mobvista_pv,
			   SUM(CASE WHEN agent = 'admob' THEN pv ELSE 0 END) admob_pv,
			   SUM(CASE WHEN agent = 'sigmob' THEN pv ELSE 0 END) sigmob_pv,
			   SUM(CASE WHEN agent = 'headline' THEN revenue ELSE 0 END) headline_revenue,
			   SUM(CASE WHEN agent = 'gdt' THEN revenue ELSE 0 END) gdt_revenue,
			   SUM(CASE WHEN agent = 'kuaishou' THEN revenue ELSE 0 END) kuaishou_revenue,
			   SUM(CASE WHEN agent = 'mobvista' THEN revenue ELSE 0 END) mobvista_revenue,
			   SUM(CASE WHEN agent = 'admob' THEN revenue ELSE 0 END) admob_revenue,
			   SUM(CASE WHEN agent = 'sigmob' THEN revenue ELSE 0 END) sigmob_revenue
		from yyhz_0308.dn_cha_cash_total aa JOIN (select adsid,open_type from dnwx_cfg.dn_extend_adsid_manage group by adsid) bb ON aa.ad_sid=bb.adsid
		where date = '${tdate}' and app_id != '0' and dnappid != '0' and ad_sid is not null and bb.open_type is not null
		  and agent in ('headline','gdt','kuaishou','mobvista','admob','sigmob')
		group by dnappid,placement_type,bb.open_type
	</insert>

	<select id="selectAllAdconfig" resultType="java.util.Map">
		select appid,cha_id,prjid,buy_id,buy_act,is_newuser,user_group,adpos_type,strategy,adsid,statu,ecpm,priority,rate,createtime,lasttime,cuser
		from dn_extend_adconfig
		where 1 = 1
		<if test="channel != null and channel != ''">
			and cha_id = #{channel}
		</if>
		<if test="appid != null and appid != ''">
			and appid = #{appid}
		</if>
		<if test="adtype != null and adtype != ''">
			and adpos_type = #{adtype}
		</if>
		<if test="strategy != null and strategy != ''">
			and strategy = #{strategy}
		</if>
	</select>

	<select id="selectExtendAdsid" resultType="com.wbgame.pojo.adv2.ExtendAdsidVo">
		select * from dn_extend_adsid_manage
		where 1 = 1
		<if test="channel != null and channel != ''">
			and cha_id = #{channel}
		</if>
		<if test="appid != null and appid != ''">
			and appid = #{appid}
		</if>
		<if test="adtype != null and adtype != ''">
			and open_type = #{adtype}
		</if>
	</select>

	<select id="selectExtendAdsidByTobidId" resultType="com.wbgame.pojo.adv2.ExtendAdsidVo">
		select * from dn_extend_adsid_manage
		where 1 = 1
		<if test="tobid_id != null and tobid_id != ''">
			and tobid_id in (${tobid_id})
		</if>
		<if test="sdk_code != null and sdk_code != ''">
			and sdk_code = ${sdk_code}
		</if>
		order by lasttime desc
	</select>

	<select id="selectExtendAdsidByPubcode" resultType="com.wbgame.pojo.adv2.ExtendAdsidVo">
		select * from dn_extend_adsid_manage
		where sdk_code in (
		    <foreach collection="list" item="it" separator=",">
				#{it}
			</foreach>
			)
		order by lasttime desc
	</select>

	<update id="updateExtendAdsidTobidId">
		update dn_extend_adsid_manage
		set tobid_id = #{tobid_id}
		where adsid = #{adsid}
	</update>

	<insert id="insertBatchAdConfig" parameterType="java.util.List">
		insert into dn_extend_adconfig
		    (appid,cha_id,prjid,prjid_group_id,is_newuser,user_group,adpos_type,
		    strategy,adsid,statu,ecpm,priority,rate,createtime,lasttime,cuser)
		values
			<foreach collection="list" item="item" separator=",">
			(#{item.appid},#{item.cha_id},#{item.prjid},#{item.prjid_group_id},#{item.is_newuser},#{item.user_group},#{item.adpos_type},
			 #{item.strategy},#{item.adsid},#{item.statu},#{item.ecpm},#{item.priority},#{item.rate},now(),now(),#{item.cuser})
			</foreach>
	</insert>

	<select id="getUserStrategyConfigListV2" resultType="com.wbgame.pojo.adv2.UserStrategyV2Vo">
		select * from dn_extend_user_strategy_two where 1=1
		<if test="sid != null and sid != ''">
			and sid like concat('%',#{sid},'%')
		</if>
		<if test="group != null and group != ''">
			and `group` = #{group}
		</if>
		<if test="param1 != null and param1 != ''">
			and `param1` = #{param1}
		</if>
		<if test="param2 != null and param2 != ''">
			and `param2` = #{param2}
		</if>
		<if test="param3 != null and param3 != ''">
			and param3 like concat('%',#{param3},'%')
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order_str}
			</when>
			<otherwise>
				ORDER BY sid ,`group`
			</otherwise>
		</choose>

	</select>
	<!--	<select id="selectTextConfig" resultType="java.lang.String">-->
	<!--		select extra_config from dn_manage_to_adpos_config-->
	<!--	</select>-->
	<select id="selectTextConfig" resultType="com.wbgame.pojo.adv2.ExtendAdposVo" parameterType="java.util.Map">

		SELECT xx.*,IFNULL(appid,'${appid}') appid,IFNULL(cha_id,'${cha_id}') cha_id,IFNULL(prjid,'${prjid}') prjid,
		       IFNULL(user_group,'${user_group}') user_group,IFNULL(adpos_type,xx.adtype) adpos_type,strategy

		FROM dn_manage_to_adpos_config xx
		LEFT JOIN

			(select appid,IF(cha_id='',NULL,cha_id) cha_id,IF(prjid='',NULL,prjid) prjid,user_group,adpos_type,strategy  from dn_extend_adconfig
				where statu=1 and appid=#{appid} and user_group=#{user_group}

			<choose>
				<when test="cha_id != null and cha_id != ''">
					and cha_id=#{cha_id}
				</when>
				<otherwise>
					and (cha_id = '' or cha_id is null)
				</otherwise>
			</choose>

			<choose>
				<when test="prjid != null and prjid != ''">
					and prjid=#{prjid}
				</when>
				<otherwise>
					and (prjid = '' or prjid is null)
				</otherwise>
			</choose>
			group by appid,IF(cha_id='',NULL,cha_id),IF(prjid='',NULL,prjid),user_group,adpos_type ) yy

		ON xx.adtype=yy.adpos_type
		where xx.channel=#{cha_id}

	</select>

	<select id="selectAdsidToAdposDefaultConfig" resultType="com.wbgame.pojo.adv2.ExtendAdposVo">
		select cha_id,prjid,user_group,adpos_type,adpos,strategy,adstyle,showrate,statu,note,delaySecond,
		       startLv,endLv,lvInterval,xdelay,autoInterval,`out`,buy_act,extraparam,custom_param,extra_config
		from
			dn_adsid_to_adpos_default_config
		where
		    cha_id = #{cha_id}
	</select>
	<select id="selectDistinctAdpos" resultType="java.lang.Integer">
		select count(*) from dn_extend_adpos_manage
		where appid = #{appid} and cha_id = #{cha_id}
	</select>

	<insert id="insertBatchAdpos">
		insert into dn_extend_adpos_manage
		    (appid,cha_id,prjid,user_group,adpos_type,adpos,strategy,adstyle,showrate,statu,note,delaySecond,startLv,
		     endLv,lvInterval,xdelay,autoInterval,`out`,custom_param,createtime,lasttime,cuser)
		values <foreach collection="list" item="obj" separator=",">
		    (#{obj.appid},#{obj.cha_id},#{obj.prjid},#{obj.user_group},#{obj.adpos_type},#{obj.adpos},#{obj.strategy},#{obj.adstyle},#{obj.showrate},#{obj.statu},
		     #{obj.note},#{obj.delaySecond},#{obj.startLv},#{obj.endLv},#{obj.lvInterval},#{obj.xdelay},#{obj.autoInterval},#{obj.out},#{obj.custom_param},now(),now(),#{obj.cuser})
		</foreach>
	</insert>
	<insert id="insertBatchAdposRecords">
		insert into dn_extend_adpos_manage_record
		(id,appid,cha_id,prjid,user_group,adpos_type,adpos,strategy,adstyle,showrate,statu,note,delaySecond,startLv,
		endLv,lvInterval,xdelay,autoInterval,`out`,custom_param,createtime,lasttime,cuser)
		values <foreach collection="list" item="obj" separator=",">
		(#{obj.id},#{obj.appid},#{obj.cha_id},#{obj.prjid},#{obj.user_group},#{obj.adpos_type},#{obj.adpos},#{obj.strategy},#{obj.adstyle},#{obj.showrate},#{obj.statu},
		#{obj.note},#{obj.delaySecond},#{obj.startLv},#{obj.endLv},#{obj.lvInterval},#{obj.xdelay},#{obj.autoInterval},#{obj.out},#{obj.custom_param},now(),now(),#{obj.cuser})
	</foreach>
	</insert>
	<insert id="insertBatchDnRedlineCtrRecords" parameterType="java.util.List">
		INSERT INTO dn_redline_ctr_record (tdate, appid, cha_id, prjid, user_group, adpos_type, sdktype, real_ctr)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(#{item.note}, #{item.appid}, #{item.cha_id}, #{item.prjid}, #{item.user_group}, #{item.adpos_type}, #{item.sdktype}, #{item.real_ctr})
		</foreach>
	</insert>

	<select id="selectAdconfig" resultType="java.util.Map">
		SELECT xx.*,m.sdk_code sdk_code,m.sdk_appid sdk_appid FROM dn_extend_adconfig xx
	    LEFT JOIN dn_extend_adsid_manage m  ON xx.adsid = m.adsid
		<where>
			<if test="list != null and list.size() > 0">
				m.sdk_code IN
				<foreach collection="list" item="it" separator="," open="(" close=")">
					#{it}
				</foreach>
			</if>
			<if test="userGroup != null and userGroup != ''">
				AND xx.user_group = #{userGroup}
			</if>
			<if test="status != null">
				AND xx.statu = #{status}
			</if>
		</where>
		ORDER BY xx.lasttime DESC
	</select>



	<!-- 查询已存在的广告位配置 -->
	<select id="queryExistingAdposMap" parameterType="java.util.List" resultType="java.util.Map">
		SELECT id, appid, cha_id, prjid, user_group, adpos,
			IFNULL(json_extract_c(custom_param,'refresh_time'),60) refresh_time,
			IFNULL(json_extract_c(custom_param,'autoShow_time'),30) autoShow_time,
			CASE
				WHEN (cha_id IS NOT NULL AND cha_id != '') AND (prjid IS NOT NULL AND prjid != '') AND (adpos='time_ad997' or adpos='time_ad998' or adpos='time_ad999') THEN
				CONCAT(appid, '#', cha_id, '#', prjid, '#', user_group, '#', adpos)
				WHEN (cha_id IS NOT NULL AND cha_id != '') AND (prjid IS NULL OR prjid = '') AND (adpos='time_ad997' or adpos='time_ad998' or adpos='time_ad999') THEN
				CONCAT(appid, '#', cha_id, '#',user_group, '#', adpos)
				WHEN (cha_id IS NOT NULL AND cha_id != '') AND (prjid IS NULL OR prjid = '') THEN
				CONCAT(appid, '#', cha_id, '#',user_group)
				WHEN (cha_id IS NOT NULL AND cha_id != '') AND (prjid IS NOT NULL AND prjid != '') THEN
				CONCAT(appid, '#', cha_id, '#', prjid, '#', user_group)
				ELSE
				CAST(appid AS CHAR)
			END AS config
		FROM dnwx_cfg.dn_extend_adpos_manage
		WHERE
		<if test="list != null and list.size() > 0">
			<foreach collection="list" item="item" separator=" OR ">
				(appid = #{item.appid} AND cha_id = #{item.cha_id} AND prjid = #{item.prjid} AND user_group = #{item.user_group})
				or
				(appid = #{item.appid} AND cha_id = #{item.cha_id} AND ifnull(prjid,'') = '' AND user_group = #{item.user_group})
			</foreach>
		</if>
		<if test="list == null or list.size() == 0">
			adpos in ('time_ad997','time_ad998','time_ad999')
		</if>
	</select>

	<select id="queryExistingXconfigMap" parameterType="java.util.List" resultType="com.wbgame.pojo.product.DnwxX3X4ConfigManageV2Dto">
		SELECT id, appid, channel, prjid, x3_content, x4_content,`x_type`,`status`,
			CASE
				WHEN (prjid IS NOT NULL AND prjid != '') THEN
					CONCAT(prjid)
				WHEN appid IS NOT NULL AND (channel IS NOT NULL AND channel != '') AND (prjid IS NULL OR prjid = '') THEN
					CONCAT(appid, '#', channel)
				ELSE 
					CAST(channel AS CHAR)
			END AS `value`
		FROM dnwx_cfg.dn_extend_x_config_v2
		WHERE
		<foreach collection="list" item="item" separator=" OR ">
			(appid = #{item.appid} AND channel = #{item.cha_id} AND prjid = #{item.prjid})
			or
			(prjid = #{item.prjid})
			or
			(appid = #{item.appid} AND channel = #{item.cha_id} AND ifnull(prjid,'') = '')
			or
			(appid = #{item.appid} AND ifnull(channel,'') = '' AND ifnull(prjid,'') = '')
		</foreach>
	</select>

	<select id="queryExistingAdconfigList" parameterType="java.util.List" resultType="com.wbgame.pojo.adv2.ExtendAdposVo">
		SELECT id, appid, cha_id, prjid, user_group, adpos_type, strategy, adsid, sdktype,
			CASE
				WHEN appid IS NOT NULL AND (cha_id IS NOT NULL AND cha_id != '') AND (prjid IS NOT NULL AND prjid != '') THEN
					CONCAT(appid, '#', cha_id, '#', prjid, '#', user_group, '#', sdktype)
				WHEN appid IS NOT NULL AND (cha_id IS NULL OR cha_id = '') AND (prjid IS NOT NULL AND prjid != '') THEN
					CONCAT(appid, '#', prjid, '#', user_group, '#', sdktype)
				WHEN appid IS NOT NULL AND (cha_id IS NOT NULL AND cha_id != '') AND (prjid IS NULL OR prjid = '') THEN
					CONCAT(appid, '#', cha_id, '#', user_group, '#', sdktype)
				ELSE
					CAST(appid AS CHAR)
			END AS config
		FROM (SELECT x.*,SUBSTRING_INDEX(SUBSTRING_INDEX(x.adsid, '_', 2), '_', -1) sdktype from dnwx_cfg.dn_extend_adconfig x) zz
		WHERE
		<foreach collection="list" item="item" separator=" OR ">
			(
		    	(
				(appid = #{item.appid} AND cha_id = #{item.cha_id} AND prjid = #{item.prjid})
				or
				(appid = #{item.appid} AND ifnull(cha_id,'') = '' AND prjid = #{item.prjid})
				or
				(appid = #{item.appid} AND cha_id = #{item.cha_id} AND ifnull(prjid,'') = '')
				or
				(appid = #{item.appid} AND ifnull(cha_id,'') = '' AND ifnull(prjid,'') = '')
				)
		    and user_group=#{item.user_group} and adpos_type=#{item.adpos_type}
			and sdktype = #{item.sdktype}
			and strategy not like '%_zs_%'
			)
		</foreach>
	</select>

	<!-- 批量更新广告位配置 -->
	<update id="updateBatchAdpos" parameterType="java.util.List">
		<foreach collection="list" item="item" separator=";">
			UPDATE dn_extend_adpos_manage
			<set>
				custom_param = #{item.custom_param},
				lasttime = NOW(),
				euser = #{item.cuser}
				<!--
                adpos_type = #{item.adpos_type},
                strategy = #{item.strategy},
                adstyle = #{item.adstyle},
                xdelay = #{item.xdelay},
                showrate = #{item.showrate},
                delaySecond = #{item.delaySecond},
                startLv = #{item.startLv},
                endLv = #{item.endLv},
                lvInterval = #{item.lvInterval},
                autoInterval = #{item.autoInterval},
                `out` = #{item.out},
                custom_param = #{item.custom_param},
                statu = #{item.statu},
                note = #{item.note},
                lasttime = NOW(),
                cuser = #{item.cuser}
                 -->
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

	<!--
	<update id="updateExistingAdconfig" parameterType="com.wbgame.pojo.adv2.ExtendAdposVo">

			UPDATE dn_extend_adconfig
			<set>
				strategy = CONCAT(strategy, ',', #{strategy}),
				cuser = 'api',
				lasttime = NOW()
			</set>
			WHERE id in (
				${id}
			) and strategy not like '%_zs_%'

	</update> -->
	<insert id="insertCopyExistingAdconfig" parameterType="com.wbgame.pojo.adv2.ExtendAdposVo">
		insert into dn_extend_adconfig
			(appid,cha_id,is_newuser,user_group,adpos_type,strategy,adsid,statu,ecpm,priority,rate,createtime,lasttime,cuser)
		SELECT
			appid,cha_id,is_newuser,user_group,adpos_type,#{strategy},adsid,statu,ecpm,priority,rate,now(),now(),'api'
		FROM dn_extend_adconfig
		WHERE id IN (${id}) and adsid not in (select adsid from dn_extend_adconfig where strategy=#{strategy})
	</insert>


</mapper>
