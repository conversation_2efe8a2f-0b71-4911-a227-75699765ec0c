<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adv2.RealizationMapper">

	<insert id="insertNewExtendAdinfoVo">
		insert into dn_extend_adinfo_manage_new (appid, cha_id, prjid,user_group, shield_name,cuser,createtime,euser,endtime,
			ad_type,reLoadInterval,checkInvalidInterval,plaque_interval,banner_interval,msg_interval,icon_interval,params,a_banner,auto_ctr_time
	      )
	    values (#{appid}, #{cha_id}, #{prjid},#{user_group,jdbcType=VARCHAR}, #{shield_name,jdbcType=VARCHAR},
		     #{cuser,jdbcType=VARCHAR}, now(), #{euser,jdbcType=VARCHAR}, now(),
		    #{ad_type},#{reLoadInterval},#{checkInvalidInterval},#{plaque_interval},#{banner_interval},#{msg_interval},#{icon_interval},#{params},#{a_banner},IFNULL(#{auto_ctr_time},0)
	      )
	</insert>
	<insert id="batchInsertNewExtendAdinfoVo">
		insert into dn_extend_adinfo_manage_new (appid, cha_id, prjid,user_group, shield_name,cuser,createtime,euser,endtime,
			ad_type,reLoadInterval,checkInvalidInterval,plaque_interval,banner_interval,msg_interval,icon_interval,params,a_banner,auto_ctr_time
	      )
		values
		<foreach collection="list" item="li" separator=",">
	    	(#{li.appid}, #{li.cha_id}, #{li.prjid}, #{li.user_group}, #{li.shield_name},
		     #{li.cuser}, now(), #{li.euser}, now(),#{li.ad_type},#{li.reLoadInterval},#{li.checkInvalidInterval},
	    	 #{li.plaque_interval},#{li.banner_interval},#{li.msg_interval},#{li.icon_interval},#{li.params},#{li.a_banner},IFNULL(#{li.auto_ctr_time},0)
	      	)
		</foreach>
	</insert>

	<update id="updateNewExtendAdinfoVo">
		update dn_extend_adinfo_manage_new
		<set >
			<if test="user_group != null and user_group != ''" >
				`user_group` = #{user_group,jdbcType=VARCHAR},
			</if>
			<if test="ad_type != null and ad_type != ''" >
				`ad_type` = #{ad_type,jdbcType=VARCHAR},
			</if>
			<if test="reLoadInterval != null" >
				`reLoadInterval` = #{reLoadInterval,jdbcType=INTEGER},
			</if>
			<if test="checkInvalidInterval != null" >
				`checkInvalidInterval` = #{checkInvalidInterval,jdbcType=INTEGER},
			</if>
			<if test="plaque_interval != null" >
				`plaque_interval` = #{plaque_interval,jdbcType=INTEGER},
			</if>
			<if test="banner_interval != null" >
				`banner_interval` = #{banner_interval,jdbcType=INTEGER},
			</if>
			<if test="msg_interval != null" >
				`msg_interval` = #{msg_interval,jdbcType=INTEGER},
			</if>
			<if test="icon_interval != null" >
				`icon_interval` = #{icon_interval,jdbcType=INTEGER},
			</if>
			<if test="a_banner != null" >
				`a_banner` = #{a_banner},
			</if>
			<if test="auto_ctr_time != null" >
				`auto_ctr_time` = #{auto_ctr_time},
			</if>
			`shield_name` = #{shield_name,jdbcType=VARCHAR},
			`params` = #{params},
			euser = #{euser,jdbcType=VARCHAR},
			endtime = now()
		</set>
		where id in (${id})
	</update>

	<delete id="deleteNewExtendAdinfoVo">
        delete from dn_extend_adinfo_manage_new
	    where id = #{id,jdbcType=INTEGER}
	</delete>


	<select id="selectNewExtendAdinfoVo" resultType="com.wbgame.pojo.adv2.ExtendAdinfoVo">
		select * from dn_extend_adinfo_manage_new
		where 1=1
		<if test="appid != null and appid != ''">
			and appid in ${appid}
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in ${cha_id}
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid,jdbcType=VARCHAR}
		</if>
		<if test="shield_name != null and shield_name != ''">
			and shield_name = #{shield_name,jdbcType=VARCHAR}
		</if>
		<if test="user_group != null and user_group != ''">
			and user_group = #{user_group}
		</if>
		<if test="id != null and id != ''">
			and id in (${id})
		</if>
		<if test="filter_id != null and filter_id != ''">
			and id in (${filter_id})
		</if>
		<if test="match_str != null and match_str != ''">
			and ${match_str}
		</if>
		<if test="appid_tag != null and appid_tag != ''">
			<choose>
				<when test="appid_tag_rev != null and appid_tag_rev != ''">
					AND CONCAT(appid,'#',cha_id) not in (${appid_tag})
				</when>
				<otherwise>
					AND CONCAT(appid,'#',cha_id) in (${appid_tag})
				</otherwise>
			</choose>
		</if>

		ORDER BY `id` DESC
	</select>

	<select id="countNewExtendAdinfoVo" resultType="java.lang.Long">
		select count(1) from dn_extend_adinfo_manage_new where 1=1
		<choose>
			<when test="cha_id != null and cha_id != ''">
				and (appid = #{appid} and cha_id = #{cha_id})
			</when>
			<when test="prjid != null and prjid != ''">
				AND (appid = #{appid} and prjid = #{prjid})
			</when>
			<otherwise>
				AND appid = #{appid} and (length(cha_id) = 0 or cha_id is null) and (length(prjid) = 0 or prjid is null)
			</otherwise>
		</choose>
	</select>

	<select id="countNewExtendAdinfoConfig" resultType="java.lang.Long">
		select count(1) from dn_extend_adinfo_manage_new where 1=1
		<choose>
			<when test="appid != null">
				and	appid = #{appid}
			</when>
			<otherwise>
				and (appid = '' or appid is null)
			</otherwise>
		</choose>
		<choose>
			<when test="cha_id != null and cha_id != ''">
				and cha_id = #{cha_id}
			</when>
			<otherwise>
				AND (cha_id = '' or cha_id is null)
			</otherwise>
		</choose>
		<choose>
			<when test="prjid != null">
				AND prjid = #{prjid}
			</when>
			<otherwise>
				and (prjid = '' or prjid is null)
			</otherwise>
		</choose>
		<choose>
			<when test="user_group != null and user_group != ''">
				and	user_group = #{user_group}
			</when>
			<otherwise>
				and (user_group = '' or user_group is null)
			</otherwise>
		</choose>
		<if test="id != null and id !=''">
			and id != #{id}
		</if>
	</select>


	<select id="selectApiPullConfigs" resultType="com.wbgame.pojo.adv2.ApiPullConfigVo">
		select * from dn_extend_api_pull_config
		where 1=1
		<if test="account != null and account != ''">
			and account = #{account}
		</if>
		<if test="platform != null and platform != ''">
			and platform like concat('%',#{platform},'%')
		</if>
		<if test="incompany != null and incompany != ''">
			and incompany like concat('%',#{incompany},'%')
		</if>
		<if test="paycompany != null and paycompany != ''">
			and paycompany like concat('%',#{paycompany},'%')
		</if>
	</select>

	<insert id="addApiPullConfigs">
		insert into dn_extend_api_pull_config(account,platform,param1,param2,param3,incompany,paycompany,owner,cuser,euser,createtime,endtime,remark)
		values
		(#{account},#{platform},#{param1},#{param2},#{param3},#{incompany},#{paycompany},#{owner},#{cuser},#{euser},now(),now(),#{remark})
	</insert>

	<update id="updateApiPullConfigs">
		update dn_extend_api_pull_config
		<set >
			<if test="incompany != null and incompany != ''" >
				`incompany` = #{incompany,jdbcType=VARCHAR},
			</if>
			<if test="paycompany != null and paycompany != ''" >
				`paycompany` = #{paycompany,jdbcType=VARCHAR},
			</if>
			<if test="owner != null and owner != ''" >
				`owner` = #{owner,jdbcType=VARCHAR},
			</if>
			`param1` = #{param1,jdbcType=VARCHAR},
			`param2` = #{param2,jdbcType=VARCHAR},
			`param3` = #{param3,jdbcType=VARCHAR},
			`remark` = #{remark,jdbcType=VARCHAR},
			euser = #{euser,jdbcType=VARCHAR},
			endtime = now()
		</set>
		where account = #{account} and platform = #{platform}

	</update>

	<delete id="deleteApiPullConfigs">
            delete from dn_extend_api_pull_config where account = #{account} and platform = #{platform} limit 1
	</delete>



</mapper>