<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adv2.SubjectConfigMapper">

	<insert id="addSubjectConfig">
         insert into dn_email_info(id,company,email,company_type,seal_admin,state,
         create_owner,update_owner,create_time,update_time,`domain`,c_en) values
         (#{id},#{company},#{email},#{company_type},#{seal_admin},#{state},
         #{create_owner},#{update_owner},now(),now(),#{domain},#{c_en})
	</insert>

	<update id="updateSubjectConfig">
		update dn_email_info set
		company = #{company},
		email = #{email},
		c_en = #{c_en},
		`domain` = #{domain},
		company_type = #{company_type},
		seal_admin = #{seal_admin},
		state = #{state},
		update_owner = #{update_owner},
		update_time = now()
		where id = #{id}
	</update>

	<delete id="deleteSubjectConfig">
		delete from dn_email_info where id = #{id} limit 1
	</delete>


	<select id="selectSubjectConfigs" resultType="com.wbgame.pojo.advert.SubjectConfigVo">
        select id,company,email,c_en,`domain`,create_owner,update_owner,company_type,seal_admin,state,
        DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') update_time
        from dn_email_info where 1=1
        <if test="id != null and id != ''">
			and id = #{id}
		</if>
		<if test="company_type != null and company_type != ''">
			and company_type = #{company_type}
		</if>
		<if test="seal_admin != null and seal_admin != ''">
			and seal_admin = #{seal_admin}
		</if>
		<if test="state != null and state != ''">
			and state = #{state}
		</if>
		<if test="company != null and company != ''">
			and company like concat('%',#{company},'%')
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by id
			</otherwise>
		</choose>
	</select>

</mapper>