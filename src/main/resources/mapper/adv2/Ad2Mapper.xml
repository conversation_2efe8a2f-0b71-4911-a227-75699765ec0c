<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adv2.Ad2Mapper">
	<!-- ``` 原生API广告配置-查询 primevalApiAdvConfigCkMp -原生API广告配置-新增 primevalApiAdvConfigAddMp 
		-原生API广告配置-编辑 primevalApiAdvConfigEditMp -原生API广告配置-删除 primevalApiAdvConfigDelMp -->

	<!-- 原生API广告配置-查询 primevalApiAdvConfigCkMp -->
	<select id="primevalApiAdvConfigCkMp" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.adv2.ExtendAdApiConfigVo">
		SELECT `appid`, `sdkcode`, `sdkappid`, `title`, `desc`, `icon`,
		`imgs`, `video`, `logo`, `size`, `pkg`, `open`, `renderType`,
		`extraParam`, `statu`, `createTime`, `code`,`down`, `adextra`,
		`sdktype`,`pkgs`
		FROM `dn_native_adconfig`
		<where>
			<if test="appid != null and appid != ''"> and appid = #{appid}</if>
			<if test="sdkcode != null and sdkcode != ''"> and sdkcode = #{sdkcode}</if>
			<if test="sdkappid != null and sdkappid != ''"> and sdkappid = #{sdkappid}</if>
			<if test="title != null and title != ''"> and title LIKE CONCAT('%','${title}','%' )</if>
			<if test="desc != null and desc != ''"> and desc LIKE CONCAT('%','${desc}','%' )</if>
			<if test="icon != null and icon != ''"> and icon = #{icon}</if>
			<if test="imgs != null and imgs != ''"> and imgs LIKE CONCAT('%','${imgs}','%' )</if>
			<if test="video != null and video != ''"> and video = #{video}</if>
			<if test="logo != null and logo != ''"> and logo = #{logo}</if>
			<if test="size != null and size != ''"> and size = #{size}</if>
			<if test="pkg != null and pkg != ''"> and pkg = #{pkg}</if>
			<if test="open != null and open != ''"> and open LIKE CONCAT('%','${open}','%' )</if>
			<if test="renderType != null and renderType != ''"> and renderType = #{renderType}</if>
			<if test="extraParam != null and extraParam != ''"> and extraParam = #{extraParam}</if>
			<if test="statu != null and statu != ''"> and statu = #{statu}</if>
			<if test="code != null and code != ''"> and code  LIKE CONCAT('%','${code}','%' )</if>
			<if test="down != null and down != ''"> and down = #{down}</if>
			<if test="adextra != null and adextra != ''"> and adextra = #{adextra}</if>
			<if test="sdktype != null and sdktype != ''"> and sdktype = #{sdktype}</if>
			<!-- <if test="start_date != null and start_date != '' and end_date != 
				null and end_date != ''"> and createtime BETWEEN '${start_date} 00:00:00' 
				AND '${end_date} 23:59:59' </if> -->
			<!-- <if test="nickName != null and nickName != ''"> and nickName LIKE 
				CONCAT('%','${nickName}','%' ) </if> -->
		</where>
		<choose>
			<when test="order !=null and order != ''">
				ORDER BY ${order}
			</when>
			<otherwise>
				ORDER BY `createtime` DESC
			</otherwise>
		</choose>
	</select>
	
	<select id="selectDnNativeconfigs" 
		resultType="com.wbgame.pojo.adv2.ExtendAdApiConfigVo">
		SELECT *
		FROM `dn_native_adconfig`
		where sdktype in ('${sdktype}')
	</select>	
	

	<!-- 原生API广告配置-新增 primevalApiAdvConfigAddMp -->
	<insert id="primevalApiAdvConfigAddMp" parameterType="com.wbgame.pojo.adv2.ExtendAdApiConfigVo">
		INSERT INTO `dn_native_adconfig` (
		`appid`
		,`sdkcode`
		,`sdkappid`
		<if test="title != null">,`title`</if>
		<if test="desc != null">,`desc`</if>
		<if test="icon != null">,`icon`</if>
		<if test="imgs != null">,`imgs`</if>
		<if test="video != null">,`video`</if>
		<if test="logo != null">,`logo`</if>
		<if test="size != null">,`size`</if>
		<if test="pkg != null">,`pkg`</if>
		<if test="pkgs != null">,`pkgs`</if>
		<if test="open != null">,`open`</if>
		<if test="renderType != null">,`renderType`</if>
		<if test="extraParam != null">,`extraParam`</if>
		<if test="statu != null">,`statu`</if>
		,`code`
		<if test="down != null">,`down`</if>
		<if test="adextra != null">,`adextra`</if>
		<if test="sdktype != null">,`sdktype`</if>
		,`createtime`
		)VALUES (
		#{appid}
		,#{sdkcode}
		,#{sdkappid}
		<if test="title != null">,#{title}</if>
		<if test="desc != null">,#{desc}</if>
		<if test="icon != null">,#{icon}</if>
		<if test="imgs != null">,#{imgs}</if>
		<if test="video != null">,#{video}</if>
		<if test="logo != null">,#{logo}</if>
		<if test="size != null">,#{size}</if>
		<if test="pkg != null">,#{pkg}</if>
		<if test="pkgs != null">,#{pkgs}</if>
		<if test="open != null">,#{open}</if>
		<if test="renderType != null">,#{renderType}</if>
		<if test="extraParam != null">,#{extraParam}</if>
		<if test="statu != null">,#{statu}</if>
		,#{code}
		<if test="down != null">,#{down}</if>
		<if test="adextra != null">,#{adextra}</if>
		<if test="sdktype != null">,#{sdktype}</if>
		,unix_timestamp()
		);
	</insert>

	<!-- 原生API广告配置-编辑 primevalApiAdvConfigEditMp -->
	<update id="primevalApiAdvConfigEditMp" parameterType="com.wbgame.pojo.adv2.ExtendAdApiConfigVo">
		UPDATE `dn_native_adconfig`
		<set>
			<!-- appid = #{appid}, cha_id = #{cha_id}, -->
			<if test="appid != null and appid != ''"> appid = #{appid},</if>
			<if test="sdkcode != null and sdkcode != ''"> sdkcode = #{sdkcode},</if>
			<if test="sdkappid != null and sdkappid != ''"> sdkappid = #{sdkappid},</if>
			<if test="title != null and title != ''"> title = #{title} ,</if>
			<if test="desc != null and desc != ''"> `desc` = #{desc}, </if>
			<if test="icon != null and icon != ''"> `icon` = #{icon},</if>
			<if test="imgs != null and imgs != ''"> imgs = #{imgs},</if>
			<if test="video != null and video != ''"> video = #{video},</if>
			<if test="logo != null and logo != ''"> logo = #{logo},</if>
			<if test="size != null and size != ''"> size = #{size},</if>
			<if test="pkg != null and pkg != ''"> pkg = #{pkg},</if>
			<if test="pkgs != null and pkgs != ''"> pkgs = #{pkgs},</if>
			<if test="open != null and open != ''"> open = #{open},</if>
			<if test="renderType != null and renderType != ''"> renderType = #{renderType},</if>
			<if test="extraParam != null and extraParam != ''"> extraParam = #{extraParam},</if>
			<if test="statu != null and statu != ''"> statu = #{statu},</if>
			<if test="down != null and down != ''"> down = #{down},</if>
			<if test="adextra != null and adextra != ''"> adextra = #{adextra},</if>
			<if test="sdktype != null and sdktype != ''"> sdktype = #{sdktype},</if>
			<!-- createtime = #{unix_timestamp(),jdbcType=VARCHAR} -->
		</set>
		WHERE
		code = #{code}
		<!-- <where> appid = #{appid} AND cha_id = #{cha_id} </where> LIMIT 1 -->
	</update>

	<!-- 原生API广告配置-编辑 primevalApiAdvConfigEditMp -->
	<update id="primevalApiAdvConfigSomeEditMp" parameterType="com.wbgame.pojo.adv2.ExtendAdApiConfigVo">
		UPDATE `dn_native_adconfig`
		<set>
			<!-- appid = #{appid}, cha_id = #{cha_id}, -->
			<if test="appid != null and appid != ''"> appid = #{appid},</if>
			<if test="sdkcode != null and sdkcode != ''"> sdkcode = #{sdkcode},</if>
			<if test="sdkappid != null and sdkappid != ''"> sdkappid = #{sdkappid},</if>
			<if test="title != null and title != ''"> title = #{title} ,</if>
			<if test="desc != null and desc != ''"> `desc` = #{desc}, </if>
			<if test="icon != null and icon != ''"> `icon` = #{icon},</if>
			<if test="imgs != null and imgs != ''"> imgs = #{imgs},</if>
			<if test="video != null and video != ''"> video = #{video},</if>
			<if test="logo != null and logo != ''"> logo = #{logo},</if>
			<if test="size != null and size != ''"> size = #{size},</if>
			<if test="pkg != null and pkg != ''"> pkg = #{pkg},</if>
			<if test="pkgs != null and pkgs != ''"> pkgs = #{pkgs},</if>
			<if test="open != null and open != ''"> open = #{open},</if>
			<if test="renderType != null and renderType != ''"> renderType = #{renderType},</if>
			<if test="extraParam != null and extraParam != ''"> extraParam = #{extraParam},</if>
			<if test="statu != null and statu != ''"> statu = #{statu},</if>
			<if test="down != null and down != ''"> down = #{down},</if>
			<if test="adextra != null and adextra != ''"> adextra = #{adextra},</if>
			<if test="sdktype != null and sdktype != ''"> sdktype = #{sdktype},</if>
			<!-- createtime = #{unix_timestamp(),jdbcType=VARCHAR} -->
		</set>
		WHERE
		code in ('${code}')
		<!-- <where> appid = #{appid} AND cha_id = #{cha_id} </where> LIMIT 1 -->
	</update>



	<!-- 原生API广告配置-删除 primevalApiAdvConfigDelMp -->
	<delete id="primevalApiAdvConfigDelMp" parameterType="com.wbgame.pojo.adv2.ExtendAdApiConfigVo">
		DELETE FROM `dn_native_adconfig`
		WHERE code = #{code}
	</delete>


	<insert id="insertDnNativeGroup" parameterType="com.wbgame.pojo.adv2.DnNativeGroup">
		insert into dn_native_group
		(groupName,codes,createTime,updateTime,updateUser,statu,sdk_adtype)
		values (#{groupName,jdbcType=VARCHAR}, #{codes,jdbcType=LONGVARCHAR},
		now(),now(),#{updateUser,jdbcType=VARCHAR},#{statu,jdbcType=VARCHAR},#{sdk_adtype})
	</insert>
	<update id="updateDnNativeGroup" parameterType="com.wbgame.pojo.adv2.DnNativeGroup">
		update dn_native_group
		set codes = #{codes},
		updateTime = now(),
		updateUser = #{updateUser,jdbcType=VARCHAR},
		statu = #{statu,jdbcType=VARCHAR},
		sdk_adtype = #{sdk_adtype,jdbcType=VARCHAR}
		where groupName = #{groupName}
	</update>
	<select id="selectDnNativeGroup" parameterType="com.wbgame.pojo.adv2.DnNativeGroup"
		resultType="com.wbgame.pojo.adv2.DnNativeGroup">
		select * from dn_native_group
		where 1=1
		<if test="groupName != null and groupName != ''">
			and groupName like concat('%',#{groupName},'%')
		</if>
		<if test="sdk_adtype != null and sdk_adtype != ''">
			and sdk_adtype = #{sdk_adtype}
		</if>
	</select>
	<delete id="deleteDnNativeGroup" parameterType="com.wbgame.pojo.adv2.DnNativeGroup">
		delete from dn_native_group
		where groupName = #{groupName,jdbcType=VARCHAR}
	</delete>
	
	<select id="selectDnNativeAllCode" resultType="String">
		select `code` from dn_native_adconfig
	</select>


	<select id="selectApitj" resultType="com.wbgame.pojo.adv2.ApiClickVo">
		SELECT `id`, `t`, `pid`,SUBSTR(pid, 1, 5 ) AS appid ,`source`,
		`clicks`,
		`shows`,round((clicks/shows),2) rate FROM `api_tj_click_show`
		where 1=1
		<if test="appid != null and appid != ''">
			and SUBSTR(pid, 1, 5 ) = #{appid,jdbcType=VARCHAR}
		</if>
		<if test="id != null and id != ''">
			and id = #{id}
		</if>
		<if test="startTime != null and endTime != null">
			and t BETWEEN #{startTime} AND #{endTime}
		</if>
	</select>

    <select id="selectXyxAppkey" resultType="java.util.Map">
		select appid,sdk_appid,sdk_appkey from dn_extend_adsid_manage
		where agent = 'weixin' and LENGTH(trim(sdk_appid)) > 0 and LENGTH(trim(sdk_appkey)) > 0
		GROUP BY appid
	</select>

	<select id="selectXyxAdsidReport" resultType="com.alibaba.fastjson.JSONObject">
        select adsid,agent,sdk_code,sdk_appid,sdk_appkey,sdk_adtype,adpos_type,sdk_adtype,open_type,a.cha_id,b.cha_media,b.cha_sub_launch,cha_type,type_name,appid from dnwx_cfg.dn_extend_adsid_manage a
		left join yyhz_0308.dn_channel_info b on a.cha_id = b.cha_id
		left join yyhz_0308.dn_channel_type c on b.cha_type = c.type_id
		where agent = 'weixin'
	</select>


</mapper>