<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adv2.OppoH5Mapper">
	
	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>
	
	
	<select id="selectOppoReportList" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="oppo_report_sql"/> 
	</select>
	
	<select id="selectOppoReportListTwo" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT tdate,SUM(rebateSpend) rebateSpend,SUM(revenue) revenue,SUM(income) income FROM oppo_report_monthly xx
		where 1=1 
		<if test="sdate != null and edate != ''">
			and xx.tdate BETWEEN #{sdate} AND #{edate} 
		</if>
		<if test="productId != null and productId != ''">
			and xx.productId = #{productId} 
		</if>
		<if test="productName != null and productName != ''">
			and xx.productName in (${productName}) 
		</if>
		GROUP BY tdate
	</select>
	
	<select id="selectOppoReportListThree" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT '${year}' tdate,SUM(rebateSpend) rebateSpend,SUM(revenue) revenue,SUM(income) income FROM oppo_report_monthly xx
		where xx.tdate like '${year}%'
		
		<if test="productId != null and productId != ''">
			and xx.productId = #{productId} 
		</if>
		<if test="productName != null and productName != ''">
			and xx.productName in (${productName}) 
		</if>
	</select>
	
	<select id="selectOppoReportListSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			TRUNCATE(SUM(xx.rebateSpend),2) rebateSpend,
			TRUNCATE(SUM(xx.revenue),2) revenue,
			TRUNCATE(SUM(xx.revenue)-SUM(xx.rebateSpend),2) income
			
		from (<include refid="oppo_report_sql"/>) xx
		
	</select>
	
	<sql id="oppo_report_sql">
	
		SELECT *
		
		FROM oppo_report_monthly xx
		where 1=1 
		<if test="sdate != null and edate != ''">
			and xx.tdate BETWEEN #{sdate} AND #{edate} 
		</if>
		<if test="productId != null and productId != ''">
			and xx.productId = #{productId} 
		</if>
		<if test="productName != null and productName != ''">
			and xx.productName in (${productName})  
		</if>
		order by tdate desc 
	</sql>
	
	
	<!-- OPPO媒体报表系统2 -->
	<select id="selectOppoReportTab1" parameterType="java.util.Map" resultType="java.util.Map">
		select * from oppo_report_tab1 
	</select>
	
	
	<sql id="oppo_report_tab2_sql">
		select * from oppo_report_tab2 
		where tdate BETWEEN #{sdate} AND #{edate} 
		order by tdate desc 
	</sql>
	
	<select id="selectOppoReportTab2" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="oppo_report_tab2_sql"/>
	</select>
	<select id="selectOppoReportTab2Sum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(`show`) `show`,
			SUM(click) click,
			TRUNCATE(SUM(click)/SUM(`show`)*100,2) click_rate,
			TRUNCATE(SUM(revenue2),2) revenue2
			
		from (<include refid="oppo_report_tab2_sql"/>) xx
	</select>
	
	
	<sql id="oppo_report_tab3_sql">
		select * from oppo_report_tab3 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<if test="productId != null and productId != ''">
			and productId in (${productId})  
		</if>
		<if test="productName != null and productName != ''">
			and productName in (${productName}) 
		</if>
		<if test="platform != null and platform != ''">
			and platform in (${platform}) 
		</if>
		order by tdate desc 
	</sql>
	
	<select id="selectOppoReportTab3" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="oppo_report_tab3_sql"/>
	</select>
	<select id="selectOppoReportTab3Group" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			xx.tdate,
			SUM(xx.req) req,
			SUM(xx.fill) fill,
			SUM(xx.`show`) `show`,
			SUM(xx.click) click,
			TRUNCATE(SUM(xx.click)/SUM(xx.`show`)*100,2) click_rate,
			TRUNCATE(SUM(xx.revenue2),2) revenue2
			
		from (<include refid="oppo_report_tab3_sql"/>) xx
		group by xx.tdate
	</select>
	<select id="selectOppoReportTab3Sum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.req) req,
			SUM(xx.fill) fill,
			SUM(xx.`show`) `show`,
			SUM(xx.click) click,
			TRUNCATE(SUM(xx.click)/SUM(xx.`show`)*100,2) click_rate,
			TRUNCATE(SUM(xx.revenue2),2) revenue2
			
		from (<include refid="oppo_report_tab3_sql"/>) xx
	</select>
	
	<select id="selectOppoReportTab4" parameterType="java.util.Map" resultType="java.util.Map">
		select * from oppo_report_tab4 
	</select>
	
	<select id="selectOppoReportTab5" parameterType="java.util.Map" resultType="java.util.Map">
		select * from oppo_report_tab5 where ctype=#{ctype} 
	</select>
	
</mapper>