<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adv2.Adv2DefaultConfigMapper">
    <insert id="insertAdsidToAdposDefaultConfig">
        insert into dn_adsid_to_adpos_default_config
        (cha_id, adpos_type, adpos, adstyle, strategy)
        values
            <foreach collection="list" item="it" separator=",">
                (#{it.cha_id},#{it.adpos_type},#{it.adpos},#{it.adstyle},#{it.strategy})
            </foreach>
    </insert>
    <insert id="insertConfigToAdposDefaultConfig">
        insert into dn_config_default_adpos
        (channel, adtype, adpos, adstyle, strategy, user_group, config_first)
        values
            <foreach collection="list" item="it" separator=",">
                (#{it.cha_id},#{it.adpos_type},#{it.adpos},#{it.adstyle},#{it.strategy},#{it.user_group}, #{it.config_first})
            </foreach>
    </insert>
    <update id="updateBatchAdstyle">
        update  dn_adsid_to_adpos_default_config
        set adstyle = #{adstyle}
        where id in  (${ids})
    </update>
    <update id="updateBatchAdstyleConfig">
        update  dn_config_default_adpos
        set adstyle = #{adstyle}
        where id in (${ids})
    </update>

    <update id="updateConfigById">
        update dn_config_default_adpos
        set
        <if test="cha_id != null">
            channel = #{cha_id},
        </if>
        <if test="adpos != null">
            adpos = #{adpos},
        </if>
        <if test="adpos_type != null">
            adtype = #{adpos_type},
        </if>
        <if test="adstyle != null">
            adstyle = #{adstyle},
        </if>
        <if test="strategy != null">
            strategy = #{strategy},
        </if>
        <if test="user_group != null">
            user_group = #{user_group},
        </if>
        <if test="config_first != null">
            config_first = #{config_first},
        </if>
        endtime = now()

        where id in (${id})
    </update>
    <update id="updateAdsidById">
        update  dn_adsid_to_adpos_default_config
        set
            <if test="cha_id != null">
                cha_id = #{cha_id},
            </if>
            <if test="adpos != null">
                adpos = #{adpos},
            </if>
            <if test="adpos_type != null">
                adpos_type = #{adpos_type},
            </if>
            <if test="adstyle != null">
                adstyle = #{adstyle},
            </if>
            <if test="strategy != null">
                strategy = #{strategy},
            </if>
            lasttime = now()

        where id in (${id})
    </update>
    <delete id="deleteAdsidToAdposDefaultConfig">
        delete from dn_adsid_to_adpos_default_config
        where id in (${ids})
    </delete>
    <delete id="deleteConfigToAdposDefaultConfig">
        delete from dn_config_default_adpos
        where id in (${ids})
    </delete>

    <select id="selectAdsidToAdposDefaultConfig" resultType="com.wbgame.pojo.adv2.ExtendAdposVo">
		select id, cha_id,prjid,user_group,adpos_type,adpos,strategy,adstyle,showrate,statu,note,delaySecond,
               startLv,endLv,lvInterval,xdelay,autoInterval,`out`,buy_act,extraparam,custom_param,extra_config
        from
            dn_adsid_to_adpos_default_config
        where
            1=1
            <if test="cha_id != null and cha_id != ''">
            and cha_id in (${cha_id})
		    </if>
		    <if test="adtype != null and adtype != ''">
            and adpos_type = #{adtype}
		    </if>
		<if test="adpos != null and adpos != ''">
            and adpos = #{adpos}
		    </if>
		order by lasttime desc
	</select>
    <select id="selectConfigToAdposDefaultConfig" resultType="com.wbgame.pojo.adv2.ExtendAdposVo">
        select id, channel as cha_id,adtype as adpos_type,adpos,adstyle, strategy, user_group, config_first
        from
            dn_config_default_adpos
        where 1=1

            <if test="cha_id != null and cha_id != ''">
                and channel in (${cha_id})
		    </if>
		    <if test="adtype != null and adtype != ''">
                and adtype = #{adtype}
		    </if>
		    <if test="adpos != null and adpos != ''">
                and adpos = #{adpos}
		    </if>
		    <if test="config_first != null and config_first != ''">
                and config_first = #{config_first}
		    </if>
		    <if test="user_group != null and user_group != ''">
                and user_group = #{user_group}
		    </if>
        order by createtime desc
    </select>

</mapper>