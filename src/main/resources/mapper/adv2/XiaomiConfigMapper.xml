<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adv2.XiaomiConfigMapper">
    <insert id="insertXiaomiAccount" parameterType="com.wbgame.pojo.adv2.union.XiaomiAccountVo">
            insert into dnwx_cfg.dn_xiaomi_account
            (
                account,userId,cookie,appSecret,enabled,createTime,createUser
            )
            values
            (
                #{account},#{userId},#{cookie},#{appSecret},#{enabled},now(),#{createUser}
            )
    </insert>

    <update id="updateXiaomiAccount" parameterType="com.wbgame.pojo.adv2.union.XiaomiAccountVo">
        update dnwx_cfg.dn_xiaomi_account
        <set >
            enabled = #{enabled},
            <if test="account != null and account != ''">
                account = #{account},
            </if>
            <if test="userId != null and userId != ''">
                userId = #{userId},
            </if>
            <if test="cookie != null and cookie != ''">
                cookie = #{cookie},
            </if>
            <if test="appSecret != null and appSecret != ''">
                appSecret = #{appSecret},
            </if>
        </set>

        where
            id = #{id}
    </update>

<!--    <update id="updateXiaomiAccountEnable" parameterType="com.wbgame.pojo.adv2.union.XiaomiAccountVo">-->
<!--        update dnwx_cfg.dn_xiaomi_account-->
<!--        set-->
<!--                enabled = #{enabled}-->
<!--        where-->
<!--        1=1-->
<!--        <if test="account != null and account != ''">-->
<!--            and account = #{account}-->
<!--        </if>-->
<!--        <if test="userId != null and userId != ''">-->
<!--            and userId = #{userId}-->
<!--        </if>-->
<!--        <if test="cookie != null and cookie != ''">-->
<!--            and (cookie like '%${cookie}%')-->
<!--        </if>-->
<!--    </update>-->

    <select id="selectXiaomiAccountList" resultType="com.wbgame.pojo.adv2.union.XiaomiAccountVo">
        select
            id,account,userId,cookie,appSecret,enabled,createTime,createUser
        from
            dnwx_cfg.dn_xiaomi_account
        where
            1=1
        <if test="account != null and account != ''">
            and account = #{account}
        </if>
        <if test="userId != null and userId != ''">
            and userId = #{userId}
        </if>
        <if test="cookie != null and cookie != ''">
            and (cookie like '%${cookie}%')
        </if>
        <if test="enabled != null">
            and enabled = #{enabled}
        </if>
        order by createTime desc
    </select>

    <select id="selectXiaomiAppList" resultType="com.wbgame.pojo.adv2.union.XiaomiAppVo">
        select
            id,account,appid,createTime,`status`, createUser, endUser, endTime
        from
            dnwx_cfg.dn_xiaomi_app
        where
        1=1
        <if test="account != null and account != ''">
            and account = #{account}
        </if>

        <if test="status != null">
            and `status` = #{status}
        </if>
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        order by createTime desc
    </select>

    <select id="selectXiaomiAppId" resultType="java.lang.Long">
        select
            id
        from
            dnwx_cfg.dn_xiaomi_app
        where
            account = #{account}
        and appid = #{appid}
    </select>
    <select id="isExistXiaomiApp" resultType="com.wbgame.pojo.adv2.union.XiaomiAppVo" parameterType="com.wbgame.pojo.adv2.union.XiaomiAppVo">
        select
            account, appid
        from
            dnwx_cfg.dn_xiaomi_app
        where
        <foreach collection="list" item="item" separator="or">
            (account = #{item.account} and appid = #{item.appid})
        </foreach>
    </select>
    <select id="selectAccountMap" resultType="java.util.Map">
        SELECT common_account, cash_account FROM common_account_2_xiaomi_account
    </select>


    <insert id="insertXiaomiApp" parameterType="com.wbgame.pojo.adv2.union.XiaomiAppVo">
        insert into dnwx_cfg.dn_xiaomi_app
        (
            account,appid,createTime,`status`, createUser, endUser, endTime
        )
        values
            (
                #{account},#{appid},now(),1, #{createUser},#{endUser}, now()
            )
    </insert>

    <insert id="batchInsertXiaomiApp" parameterType="com.wbgame.pojo.adv2.union.XiaomiAppVo">
        insert into dnwx_cfg.dn_xiaomi_app
        (
        account,appid,createTime,`status`, createUser, endUser, endTime
        ) values
        <foreach collection="list" item="item" separator=",">
            (#{item.account},#{item.appid},now(),1, #{item.createUser},#{item.endUser}, now())
        </foreach>
    </insert>

    <update id="udpateXiaomiApp" parameterType="com.wbgame.pojo.adv2.union.XiaomiAppVo">
        update dnwx_cfg.dn_xiaomi_app
        <set >
            `status` = #{status},
            endUser = #{endUser},
            endTime = now()
            <if test="account != null and account != ''">
                ,account = #{account}
            </if>
            <if test="appid != null and appid != ''">
                ,appid = #{appid}
            </if>
        </set>
        where
            id = #{id}
    </update>
</mapper>