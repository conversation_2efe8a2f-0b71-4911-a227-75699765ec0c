<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adv2.DnApiPlatformAccountMapper">


    <insert id="insert" parameterType="com.wbgame.pojo.DnApiPlatformAccount">
        insert into dn_api_platform_account (account,platform,accountName,secret,company,createUser,createTime,extra1,extra2,enabled,accessid) values
        (#{account},#{platform},#{accountName},#{secret},#{company},#{createUser},now(),#{extra1},#{extra2},#{enabled},#{accessid})
    </insert>

    <update id="updateById" parameterType="com.wbgame.pojo.DnApiPlatformAccount">
        update dn_api_platform_account set
            account = #{account},
            platform = #{platform},
            accountName = #{accountName},
            secret = #{secret},
            company = #{company},
            modifyTime = now(),
            modifyUser = #{modifyUser},
            extra1 = #{extra1},
            extra2 = #{extra2},
            enabled = #{enabled},
            accessid = #{accessid}
        where id = #{id}
    </update>

    <delete id="deleteById">
        delete from dn_api_platform_account where id = #{id}
    </delete>


    <select id="queryList" resultType="com.wbgame.pojo.DnApiPlatformAccount">
        select * from dn_api_platform_account
        <where>
            <if test="account != null and account != ''">
                account = #{account}
            </if>
            <if test="platform != null and platform != ''">
                and platform = #{platform}
            </if>
            <if test="accountName != null and accountName != ''">
                and accountName like concat('%',#{accountName},'%')
            </if>
            <if test="enabled != null">
                and enabled = #{enabled}
            </if>
        </where>
        order by createTime desc
    </select>



</mapper>