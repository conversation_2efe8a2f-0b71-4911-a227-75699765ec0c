<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.leying.OrderMapper">

	<select id="selectLeyingOrderList" resultType="com.wbgame.pojo.game.pay.LeyingOrderVo">
		select a.app_id AS appid,a.user_id AS userid,dn_deviceId AS imei,pkg AS pn,inner_order AS orderid,
		plat_order AS uid,total_money AS money,30 AS pay_type,currency,cha AS chaid,
		pid ,pay_goods_id,a.create_time AS createtime,a.update_time AS updatetime,
		app_ver AS version,model ,ip ,pay_goods_id AS payname,IF(platform !='default' AND platform !='notMatch',platform,'') AS buy_id,
		IF(DATE(b.create_time)=DATE(a.create_time) ,1,0) is_new,idfa
		from order_pay a
		left join user_base_info_39680 b on a.user_id = b.userId
		left join ios_product_config c on a.pay_goods_id = c.payGoodsId
		where a.create_time between #{startTime} and #{endTime} and a.trade_status IN (2,7)
	</select>

</mapper>
