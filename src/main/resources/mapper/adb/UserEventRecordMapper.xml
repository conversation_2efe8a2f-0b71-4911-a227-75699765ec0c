<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adb.UserEventRecordMapper">

    <insert id="batchInsert">
        replace into user_event_record (
        user_id,tdate,lcp,inp,cls,fcp,user_sys,stop_time,events,hour_request,`system`
        ) values
        <foreach collection="recordList" item="li" separator=",">
            (#{li.user_id},#{li.tdate},#{li.lcp},#{li.inp},#{li.cls},#{li.fcp},#{li.user_sys},#{li.stop_time},#{li.events},#{li.hour_request},#{li.system})
        </foreach>
    </insert>
    
    
    <select id="queryList" resultType="com.wbgame.pojo.mobile.UserEventRecord">
        select * from user_event_record
        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                tdate between #{start_date} and #{end_date}
            </if>
            <if test="user_id != null and user_id != ''">
                and user_id = #{user_id}
            </if>
            <if test="user_sys != null and user_sys != ''">
                and user_sys = #{user_sys}
            </if>
            <if test="system != null and system != ''">
                and `system` = #{system}
            </if>
        </where>
    </select>
    <select id="queryOne" resultType="com.wbgame.pojo.mobile.UserEventRecord">
        select * from user_event_record
        <where>
        <if test="tdate != null and tdate != ''">
            tdate = #{tdate}
        </if>
        <if test="userId != null and userId != ''">
            and user_id = #{userId}
        </if>
        <if test="userSys != null and userSys != ''">
            and user_sys = #{userSys}
        </if>
        <if test="system != null and system != ''">
            and `system` = #{system}
        </if>
        </where>
    </select>
</mapper>