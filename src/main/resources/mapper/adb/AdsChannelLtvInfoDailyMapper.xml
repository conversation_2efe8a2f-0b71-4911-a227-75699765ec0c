<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsChannelLtvInfoDailyMapper">


    <select id="selectChannelLtvInfo" resultType="com.wbgame.pojo.clean.AdsChannelLtvInfoDaily"
            parameterType="com.wbgame.pojo.clean.AdsChannelLtvInfoDaily">

        <include refid="selectData"/>

    </select>

    <select id="countChannelLtvInfo" resultType="com.wbgame.pojo.clean.AdsChannelLtvInfoDaily"
            parameterType="com.wbgame.pojo.clean.AdsChannelLtvInfoDaily">
        select

        sum(addUser) addUser,
        sum(ltv1) ltv1,
        sum(ltv2) ltv2,
        sum(ltv3) ltv3,
        sum(ltv4) ltv4,
        sum(ltv5) ltv5,
        sum(ltv6) ltv6,
        sum(ltv7) ltv7,
        sum(ltv8) ltv8,
        sum(ltv9) ltv9,
        sum(ltv10) ltv10,
        sum(ltv11) ltv11,
        sum(ltv12) ltv12,
        sum(ltv13) ltv13,
        sum(ltv14) ltv14,
        sum(ltv15) ltv15,
        sum(ltv16) ltv16,
        sum(ltv17) ltv17,
        sum(ltv18) ltv18,
        sum(ltv19) ltv19,
        sum(ltv20) ltv20,
        sum(ltv21) ltv21,
        sum(ltv22) ltv22,
        sum(ltv23) ltv23,
        sum(ltv24) ltv24,
        sum(ltv25) ltv25,
        sum(ltv26) ltv26,
        sum(ltv27) ltv27,
        sum(ltv28) ltv28,
        sum(ltv29) ltv29,
        sum(ltv30) ltv30,
        sum(ltv36) ltv36,
        sum(ltv42) ltv42,
        sum(ltv48) ltv48,
        sum(ltv54) ltv54,
        sum(ltv60) ltv60,
        sum(iapLtv1) iapLtv1,
        sum(iapLtv2) iapLtv2,
        sum(iapLtv3) iapLtv3,
        sum(iapLtv4) iapLtv4,
        sum(iapLtv5) iapLtv5,
        sum(iapLtv6) iapLtv6,
        sum(iapLtv7) iapLtv7,
        sum(iapLtv8) iapLtv8,
        sum(iapLtv9) iapLtv9,
        sum(iapLtv10) iapLtv10,
        sum(iapLtv11) iapLtv11,
        sum(iapLtv12) iapLtv12,
        sum(iapLtv13) iapLtv13,
        sum(iapLtv14) iapLtv14,
        sum(iapLtv15) iapLtv15,
        sum(iapLtv16) iapLtv16,
        sum(iapLtv17) iapLtv17,
        sum(iapLtv18) iapLtv18,
        sum(iapLtv19) iapLtv19,
        sum(iapLtv20) iapLtv20,
        sum(iapLtv21) iapLtv21,
        sum(iapLtv22) iapLtv22,
        sum(iapLtv23) iapLtv23,
        sum(iapLtv24) iapLtv24,
        sum(iapLtv25) iapLtv25,
        sum(iapLtv26) iapLtv26,
        sum(iapLtv27) iapLtv27,
        sum(iapLtv28) iapLtv28,
        sum(iapLtv29) iapLtv29,
        sum(iapLtv30) iapLtv30,
        sum(iapLtv36) iapLtv36,
        sum(iapLtv42) iapLtv42,
        sum(iapLtv48) iapLtv48,
        sum(iapLtv54) iapLtv54,
        sum(iapLtv60) iapLtv60,
        sum(iaaLtv1) iaaLtv1,
        sum(iaaLtv2) iaaLtv2,
        sum(iaaLtv3) iaaLtv3,
        sum(iaaLtv4) iaaLtv4,
        sum(iaaLtv5) iaaLtv5,
        sum(iaaLtv6) iaaLtv6,
        sum(iaaLtv7) iaaLtv7,
        sum(iaaLtv8) iaaLtv8,
        sum(iaaLtv9) iaaLtv9,
        sum(iaaLtv10) iaaLtv10,
        sum(iaaLtv11) iaaLtv11,
        sum(iaaLtv12) iaaLtv12,
        sum(iaaLtv13) iaaLtv13,
        sum(iaaLtv14) iaaLtv14,
        sum(iaaLtv15) iaaLtv15,
        sum(iaaLtv16) iaaLtv16,
        sum(iaaLtv17) iaaLtv17,
        sum(iaaLtv18) iaaLtv18,
        sum(iaaLtv19) iaaLtv19,
        sum(iaaLtv20) iaaLtv20,
        sum(iaaLtv21) iaaLtv21,
        sum(iaaLtv22) iaaLtv22,
        sum(iaaLtv23) iaaLtv23,
        sum(iaaLtv24) iaaLtv24,
        sum(iaaLtv25) iaaLtv25,
        sum(iaaLtv26) iaaLtv26,
        sum(iaaLtv27) iaaLtv27,
        sum(iaaLtv28) iaaLtv28,
        sum(iaaLtv29) iaaLtv29,
        sum(iaaLtv30) iaaLtv30,
        sum(iaaLtv36) iaaLtv36,
        sum(iaaLtv42) iaaLtv42,
        sum(iaaLtv48) iaaLtv48,
        sum(iaaLtv54) iaaLtv54,
        sum(iaaLtv60) iaaLtv60

        from (

        <include refid="selectData"/>
        ) a

    </select>


    <select id="selectChannelLtvInfo2" resultType="com.wbgame.pojo.clean.AdsChannelLtvInfoDaily"
            parameterType="com.wbgame.pojo.clean.AdsChannelLtvInfoDaily">

        <include refid="select2"/>

    </select>


    <select id="countChannelLtvInfo2" resultType="com.wbgame.pojo.clean.AdsChannelLtvInfoDaily"
            parameterType="com.wbgame.pojo.clean.AdsChannelLtvInfoDaily">
        select

        sum(addUser) addUser,

        sum(ltv2) ltv2,
        sum(ltv3) ltv3,
        sum(ltv4) ltv4,
        sum(ltv5) ltv5,
        sum(ltv6) ltv6,
        sum(ltv7) ltv7,
        sum(ltv8) ltv8,
        sum(ltv9) ltv9,
        sum(ltv10) ltv10,
        sum(ltv11) ltv11,
        sum(ltv12) ltv12,
        sum(ltv13) ltv13,
        sum(ltv14) ltv14,
        sum(ltv15) ltv15,
        sum(ltv16) ltv16,
        sum(ltv17) ltv17,
        sum(ltv18) ltv18,
        sum(ltv19) ltv19,
        sum(ltv20) ltv20,
        sum(ltv21) ltv21,
        sum(ltv22) ltv22,
        sum(ltv23) ltv23,
        sum(ltv24) ltv24,
        sum(ltv25) ltv25,
        sum(ltv26) ltv26,
        sum(ltv27) ltv27,
        sum(ltv28) ltv28,
        sum(ltv29) ltv29,
        sum(ltv30) ltv30,
        sum(ltv36) ltv36,
        sum(ltv42) ltv42,
        sum(ltv48) ltv48,
        sum(ltv54) ltv54,
        sum(ltv60) ltv60,
        sum(iapLtv2) iapLtv2,
        sum(iapLtv3) iapLtv3,
        sum(iapLtv4) iapLtv4,
        sum(iapLtv5) iapLtv5,
        sum(iapLtv6) iapLtv6,
        sum(iapLtv7) iapLtv7,
        sum(iapLtv8) iapLtv8,
        sum(iapLtv9) iapLtv9,
        sum(iapLtv10) iapLtv10,
        sum(iapLtv11) iapLtv11,
        sum(iapLtv12) iapLtv12,
        sum(iapLtv13) iapLtv13,
        sum(iapLtv14) iapLtv14,
        sum(iapLtv15) iapLtv15,
        sum(iapLtv16) iapLtv16,
        sum(iapLtv17) iapLtv17,
        sum(iapLtv18) iapLtv18,
        sum(iapLtv19) iapLtv19,
        sum(iapLtv20) iapLtv20,
        sum(iapLtv21) iapLtv21,
        sum(iapLtv22) iapLtv22,
        sum(iapLtv23) iapLtv23,
        sum(iapLtv24) iapLtv24,
        sum(iapLtv25) iapLtv25,
        sum(iapLtv26) iapLtv26,
        sum(iapLtv27) iapLtv27,
        sum(iapLtv28) iapLtv28,
        sum(iapLtv29) iapLtv29,
        sum(iapLtv30) iapLtv30,
        sum(iapLtv36) iapLtv36,
        sum(iapLtv42) iapLtv42,
        sum(iapLtv48) iapLtv48,
        sum(iapLtv54) iapLtv54,
        sum(iapLtv60) iapLtv60,
        sum(iaaLtv2) iaaLtv2,
        sum(iaaLtv3) iaaLtv3,
        sum(iaaLtv4) iaaLtv4,
        sum(iaaLtv5) iaaLtv5,
        sum(iaaLtv6) iaaLtv6,
        sum(iaaLtv7) iaaLtv7,
        sum(iaaLtv8) iaaLtv8,
        sum(iaaLtv9) iaaLtv9,
        sum(iaaLtv10) iaaLtv10,
        sum(iaaLtv11) iaaLtv11,
        sum(iaaLtv12) iaaLtv12,
        sum(iaaLtv13) iaaLtv13,
        sum(iaaLtv14) iaaLtv14,
        sum(iaaLtv15) iaaLtv15,
        sum(iaaLtv16) iaaLtv16,
        sum(iaaLtv17) iaaLtv17,
        sum(iaaLtv18) iaaLtv18,
        sum(iaaLtv19) iaaLtv19,
        sum(iaaLtv20) iaaLtv20,
        sum(iaaLtv21) iaaLtv21,
        sum(iaaLtv22) iaaLtv22,
        sum(iaaLtv23) iaaLtv23,
        sum(iaaLtv24) iaaLtv24,
        sum(iaaLtv25) iaaLtv25,
        sum(iaaLtv26) iaaLtv26,
        sum(iaaLtv27) iaaLtv27,
        sum(iaaLtv28) iaaLtv28,
        sum(iaaLtv29) iaaLtv29,
        sum(iaaLtv30) iaaLtv30,
        sum(iaaLtv36) iaaLtv36,
        sum(iaaLtv42) iaaLtv42,
        sum(iaaLtv48) iaaLtv48,
        sum(iaaLtv54) iaaLtv54,
        sum(iaaLtv60) iaaLtv60

        from (

        <include refid="select2"/>
        ) a



    </select>

    <select id="selectChannelLtvInfo3" resultType="com.wbgame.pojo.clean.AdsChannelLtvInfoDaily"
            parameterType="com.wbgame.pojo.clean.AdsChannelLtvInfoDaily">

        <include refid="select3"/>

    </select>

    <select id="selectChannelLtvInfo4" resultType="com.wbgame.pojo.clean.AdsChannelLtvInfoDaily"
            parameterType="com.wbgame.pojo.clean.AdsChannelLtvInfoDaily">

        <include refid="select4"/>

    </select>

    <select id="selectChannelLtvInfo5" resultType="com.wbgame.pojo.clean.AdsChannelLtvInfoDaily"
            parameterType="com.wbgame.pojo.clean.AdsChannelLtvInfoDaily">

        <include refid="select5"/>

    </select>

<!--    公共查询sql-->
    <sql id="selectData">


        select

        <if test="group != null and group != ''">
            ${group},
        </if>
        sum(add_user)                                                                                         addUser,
        cast((sum(add_revenue_1) + sum(add_purchase_revenue_1)) / sum(add_user) / 100 as decimal(18, 2))   ltv1,
        cast((sum(add_revenue_2) + sum(add_purchase_revenue_2)) / sum(add_user) / 100 as decimal(18, 2))   ltv2,
        cast((sum(add_revenue_3) + sum(add_purchase_revenue_3)) / sum(add_user) / 100 as decimal(18, 2))   ltv3,
        cast((sum(add_revenue_4) + sum(add_purchase_revenue_4)) / sum(add_user) / 100 as decimal(18, 2))   ltv4,
        cast((sum(add_revenue_5) + sum(add_purchase_revenue_5)) / sum(add_user) / 100 as decimal(18, 2))   ltv5,
        cast((sum(add_revenue_6) + sum(add_purchase_revenue_6)) / sum(add_user) / 100 as decimal(18, 2))   ltv6,
        cast((sum(add_revenue_7) + sum(add_purchase_revenue_7)) / sum(add_user) / 100 as decimal(18, 2))   ltv7,
        cast((sum(add_revenue_8) + sum(add_purchase_revenue_8)) / sum(add_user) / 100 as decimal(18, 2))   ltv8,
        cast((sum(add_revenue_9) + sum(add_purchase_revenue_9)) / sum(add_user) / 100 as decimal(18, 2))   ltv9,
        cast((sum(add_revenue_10) + sum(add_purchase_revenue_10)) / sum(add_user) / 100 as decimal(18, 2)) ltv10,
        cast((sum(add_revenue_11) + sum(add_purchase_revenue_11)) / sum(add_user) / 100 as decimal(18, 2)) ltv11,
        cast((sum(add_revenue_12) + sum(add_purchase_revenue_12)) / sum(add_user) / 100 as decimal(18, 2)) ltv12,
        cast((sum(add_revenue_13) + sum(add_purchase_revenue_13)) / sum(add_user) / 100 as decimal(18, 2)) ltv13,
        cast((sum(add_revenue_14) + sum(add_purchase_revenue_14)) / sum(add_user) / 100 as decimal(18, 2)) ltv14,
        cast((sum(add_revenue_15) + sum(add_purchase_revenue_15)) / sum(add_user) / 100 as decimal(18, 2)) ltv15,
        cast((sum(add_revenue_16) + sum(add_purchase_revenue_16)) / sum(add_user) / 100 as decimal(18, 2)) ltv16,
        cast((sum(add_revenue_17) + sum(add_purchase_revenue_17)) / sum(add_user) / 100 as decimal(18, 2)) ltv17,
        cast((sum(add_revenue_18) + sum(add_purchase_revenue_18)) / sum(add_user) / 100 as decimal(18, 2)) ltv18,
        cast((sum(add_revenue_19) + sum(add_purchase_revenue_19)) / sum(add_user) / 100 as decimal(18, 2)) ltv19,
        cast((sum(add_revenue_20) + sum(add_purchase_revenue_20)) / sum(add_user) / 100 as decimal(18, 2)) ltv20,
        cast((sum(add_revenue_21) + sum(add_purchase_revenue_21)) / sum(add_user) / 100 as decimal(18, 2)) ltv21,
        cast((sum(add_revenue_22) + sum(add_purchase_revenue_22)) / sum(add_user) / 100 as decimal(18, 2)) ltv22,
        cast((sum(add_revenue_23) + sum(add_purchase_revenue_23)) / sum(add_user) / 100 as decimal(18, 2)) ltv23,
        cast((sum(add_revenue_24) + sum(add_purchase_revenue_24)) / sum(add_user) / 100 as decimal(18, 2)) ltv24,
        cast((sum(add_revenue_25) + sum(add_purchase_revenue_25)) / sum(add_user) / 100 as decimal(18, 2)) ltv25,
        cast((sum(add_revenue_26) + sum(add_purchase_revenue_26)) / sum(add_user) / 100 as decimal(18, 2)) ltv26,
        cast((sum(add_revenue_27) + sum(add_purchase_revenue_27)) / sum(add_user) / 100 as decimal(18, 2)) ltv27,
        cast((sum(add_revenue_28) + sum(add_purchase_revenue_28)) / sum(add_user) / 100 as decimal(18, 2)) ltv28,
        cast((sum(add_revenue_29) + sum(add_purchase_revenue_29)) / sum(add_user) / 100 as decimal(18, 2)) ltv29,
        cast((sum(add_revenue_30) + sum(add_purchase_revenue_30)) / sum(add_user) / 100 as decimal(18, 2)) ltv30,
        cast((sum(add_revenue_36) + sum(add_purchase_revenue_36)) / sum(add_user) / 100 as decimal(18, 2)) ltv36,
        cast((sum(add_revenue_42) + sum(add_purchase_revenue_42)) / sum(add_user) / 100 as decimal(18, 2)) ltv42,
        cast((sum(add_revenue_48) + sum(add_purchase_revenue_48)) / sum(add_user) / 100 as decimal(18, 2)) ltv48,
        cast((sum(add_revenue_54) + sum(add_purchase_revenue_54)) / sum(add_user) / 100 as decimal(18, 2)) ltv54,
        cast((sum(add_revenue_60) + sum(add_purchase_revenue_60)) / sum(add_user) / 100 as decimal(18, 2)) ltv60,


        cast(sum(add_purchase_revenue_1) / sum(add_user) / 100 as decimal(18, 2))                        iapLtv1,
        cast(sum(add_purchase_revenue_2) / sum(add_user) / 100 as decimal(18, 2))                        iapLtv2,
        cast(sum(add_purchase_revenue_3) / sum(add_user) / 100 as decimal(18, 2))                        iapLtv3,
        cast(sum(add_purchase_revenue_4) / sum(add_user) / 100 as decimal(18, 2))                        iapLtv4,
        cast(sum(add_purchase_revenue_5) / sum(add_user) / 100 as decimal(18, 2))                        iapLtv5,
        cast(sum(add_purchase_revenue_6) / sum(add_user) / 100 as decimal(18, 2))                        iapLtv6,
        cast(sum(add_purchase_revenue_7) / sum(add_user) / 100 as decimal(18, 2))                        iapLtv7,
        cast(sum(add_purchase_revenue_8) / sum(add_user) / 100 as decimal(18, 2))                        iapLtv8,
        cast(sum(add_purchase_revenue_9) / sum(add_user) / 100 as decimal(18, 2))                        iapLtv9,
        cast(sum(add_purchase_revenue_10) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv10,
        cast(sum(add_purchase_revenue_11) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv11,
        cast(sum(add_purchase_revenue_12) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv12,
        cast(sum(add_purchase_revenue_13) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv13,
        cast(sum(add_purchase_revenue_14) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv14,
        cast(sum(add_purchase_revenue_15) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv15,
        cast(sum(add_purchase_revenue_16) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv16,
        cast(sum(add_purchase_revenue_17) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv17,
        cast(sum(add_purchase_revenue_18) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv18,
        cast(sum(add_purchase_revenue_19) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv19,
        cast(sum(add_purchase_revenue_20) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv20,
        cast(sum(add_purchase_revenue_21) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv21,
        cast(sum(add_purchase_revenue_22) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv22,
        cast(sum(add_purchase_revenue_23) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv23,
        cast(sum(add_purchase_revenue_24) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv24,
        cast(sum(add_purchase_revenue_25) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv25,
        cast(sum(add_purchase_revenue_26) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv26,
        cast(sum(add_purchase_revenue_27) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv27,
        cast(sum(add_purchase_revenue_28) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv28,
        cast(sum(add_purchase_revenue_29) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv29,
        cast(sum(add_purchase_revenue_30) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv30,
        cast(sum(add_purchase_revenue_36) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv36,
        cast(sum(add_purchase_revenue_42) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv42,
        cast(sum(add_purchase_revenue_48) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv48,
        cast(sum(add_purchase_revenue_54) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv54,
        cast(sum(add_purchase_revenue_60) / sum(add_user) / 100 as decimal(18, 2))                       iapLtv60,

        cast(sum(add_revenue_1) / sum(add_user) / 100 as decimal(18, 2))                                 iaaLtv1,
        cast(sum(add_revenue_2) / sum(add_user) / 100 as decimal(18, 2))                                 iaaLtv2,
        cast(sum(add_revenue_3) / sum(add_user) / 100 as decimal(18, 2))                                 iaaLtv3,
        cast(sum(add_revenue_4) / sum(add_user) / 100 as decimal(18, 2))                                 iaaLtv4,
        cast(sum(add_revenue_5) / sum(add_user) / 100 as decimal(18, 2))                                 iaaLtv5,
        cast(sum(add_revenue_6) / sum(add_user) / 100 as decimal(18, 2))                                 iaaLtv6,
        cast(sum(add_revenue_7) / sum(add_user) / 100 as decimal(18, 2))                                 iaaLtv7,
        cast(sum(add_revenue_8) / sum(add_user) / 100 as decimal(18, 2))                                 iaaLtv8,
        cast(sum(add_revenue_9) / sum(add_user) / 100 as decimal(18, 2))                                 iaaLtv9,
        cast(sum(add_revenue_10) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv10,
        cast(sum(add_revenue_11) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv11,
        cast(sum(add_revenue_12) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv12,
        cast(sum(add_revenue_13) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv13,
        cast(sum(add_revenue_14) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv14,
        cast(sum(add_revenue_15) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv15,
        cast(sum(add_revenue_16) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv16,
        cast(sum(add_revenue_17) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv17,
        cast(sum(add_revenue_18) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv18,
        cast(sum(add_revenue_19) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv19,
        cast(sum(add_revenue_20) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv20,
        cast(sum(add_revenue_21) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv21,
        cast(sum(add_revenue_22) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv22,
        cast(sum(add_revenue_23) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv23,
        cast(sum(add_revenue_24) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv24,
        cast(sum(add_revenue_25) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv25,
        cast(sum(add_revenue_26) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv26,
        cast(sum(add_revenue_27) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv27,
        cast(sum(add_revenue_28) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv28,
        cast(sum(add_revenue_29) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv29,
        cast(sum(add_revenue_30) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv30,
        cast(sum(add_revenue_36) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv36,
        cast(sum(add_revenue_42) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv42,
        cast(sum(add_revenue_48) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv48,
        cast(sum(add_revenue_54) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv54,
        cast(sum(add_revenue_60) / sum(add_user) / 100 as decimal(18, 2))                                iaaLtv60

        from ads_channel_ltv_info_daily

        <where>

            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and tdate between #{start_date} and #{end_date}
            </if>

            <if test="ver != null and ver != ''">
                and ver = #{ver}
            </if>

            <if test="channelList != null and channelList.size > 0">
                AND channel IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

            <if test="prjidList != null and prjidList.size > 0">
                AND pid IN
                <foreach collection="prjidList" item="prj" open="(" separator="," close=")">
                    #{prj}
                </foreach>
            </if>


        </where>

        <if test="group != null and group != ''">
            group by ${group}

            having sum(add_user) > 0 and sum(add_user) is not null
        </if>


        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>

                order by tdate
            </otherwise>
        </choose>
    </sql>

<!--    报表二-->
    <sql id="select2">


        select
        <if test="group != null and group != ''">
            ${group},
        </if>
        addUser,
        cast(ltv2 / ltv1 as decimal (18,2))          ltv2,
        cast(ltv3 / ltv1 as decimal (18,2))          ltv3,
        cast(ltv4 / ltv1 as decimal (18,2))          ltv4,
        cast(ltv5 / ltv1 as decimal (18,2))          ltv5,
        cast(ltv6 / ltv1 as decimal (18,2))          ltv6,
        cast(ltv7 / ltv1 as decimal (18,2))          ltv7,
        cast(ltv8 / ltv1 as decimal (18,2))          ltv8,
        cast(ltv9 / ltv1 as decimal (18,2))          ltv9,
        cast(ltv10 / ltv1 as decimal (18,2))             ltv10,
        cast(ltv11 / ltv1 as decimal (18,2))             ltv11,
        cast(ltv12 / ltv1 as decimal (18,2))             ltv12,
        cast(ltv13 / ltv1 as decimal (18,2))             ltv13,
        cast(ltv14 / ltv1 as decimal (18,2))             ltv14,
        cast(ltv15 / ltv1 as decimal (18,2))             ltv15,
        cast(ltv16 / ltv1 as decimal (18,2))             ltv16,
        cast(ltv17 / ltv1 as decimal (18,2))             ltv17,
        cast(ltv18 / ltv1 as decimal (18,2))             ltv18,
        cast(ltv19 / ltv1 as decimal (18,2))             ltv19,
        cast(ltv20 / ltv1 as decimal (18,2))             ltv20,
        cast(ltv21 / ltv1 as decimal (18,2))             ltv21,
        cast(ltv22 / ltv1 as decimal (18,2))             ltv22,
        cast(ltv23 / ltv1 as decimal (18,2))             ltv23,
        cast(ltv24 / ltv1 as decimal (18,2))             ltv24,
        cast(ltv25 / ltv1 as decimal (18,2))             ltv25,
        cast(ltv26 / ltv1 as decimal (18,2))             ltv26,
        cast(ltv27 / ltv1 as decimal (18,2))             ltv27,
        cast(ltv28 / ltv1 as decimal (18,2))             ltv28,
        cast(ltv29 / ltv1 as decimal (18,2))             ltv29,
        cast(ltv30 / ltv1 as decimal (18,2))             ltv30,
        cast(ltv36 / ltv1 as decimal (18,2))             ltv36,
        cast(ltv42 / ltv1 as decimal (18,2))             ltv42,
        cast(ltv48 / ltv1 as decimal (18,2))             ltv48,
        cast(ltv54 / ltv1 as decimal (18,2))             ltv54,
        cast(ltv60 / ltv1 as decimal (18,2))             ltv60,

        cast(iapLtv2 / iapLtv1 as decimal (18, 2))                     iapLtv2,
        cast(iapLtv3 / iapLtv1 as decimal (18, 2))                     iapLtv3,
        cast(iapLtv4 / iapLtv1 as decimal (18, 2))                     iapLtv4,
        cast(iapLtv5 / iapLtv1 as decimal (18, 2))                     iapLtv5,
        cast(iapLtv6 / iapLtv1 as decimal (18, 2))                     iapLtv6,
        cast(iapLtv7 / iapLtv1 as decimal (18, 2))                     iapLtv7,
        cast(iapLtv8 / iapLtv1 as decimal (18, 2))                     iapLtv8,
        cast(iapLtv9 / iapLtv1 as decimal (18, 2))                     iapLtv9,
        cast(iapLtv10 / iapLtv1 as decimal (18, 2))                    iapLtv10,
        cast(iapLtv11 / iapLtv1 as decimal (18, 2))                    iapLtv11,
        cast(iapLtv12 / iapLtv1 as decimal (18, 2))                    iapLtv12,
        cast(iapLtv13 / iapLtv1 as decimal (18, 2))                    iapLtv13,
        cast(iapLtv14 / iapLtv1 as decimal (18, 2))                    iapLtv14,
        cast(iapLtv15 / iapLtv1 as decimal (18, 2))                    iapLtv15,
        cast(iapLtv16 / iapLtv1 as decimal (18, 2))                    iapLtv16,
        cast(iapLtv17 / iapLtv1 as decimal (18, 2))                    iapLtv17,
        cast(iapLtv18 / iapLtv1 as decimal (18, 2))                    iapLtv18,
        cast(iapLtv19 / iapLtv1 as decimal (18, 2))                    iapLtv19,
        cast(iapLtv20 / iapLtv1 as decimal (18, 2))                    iapLtv20,
        cast(iapLtv21 / iapLtv1 as decimal (18, 2))                    iapLtv21,
        cast(iapLtv22 / iapLtv1 as decimal (18, 2))                    iapLtv22,
        cast(iapLtv23 / iapLtv1 as decimal (18, 2))                    iapLtv23,
        cast(iapLtv24 / iapLtv1 as decimal (18, 2))                    iapLtv24,
        cast(iapLtv25 / iapLtv1 as decimal (18, 2))                    iapLtv25,
        cast(iapLtv26 / iapLtv1 as decimal (18, 2))                    iapLtv26,
        cast(iapLtv27 / iapLtv1 as decimal (18, 2))                    iapLtv27,
        cast(iapLtv28 / iapLtv1 as decimal (18, 2))                    iapLtv28,
        cast(iapLtv29 / iapLtv1 as decimal (18, 2))                    iapLtv29,
        cast(iapLtv30 / iapLtv1 as decimal (18, 2))                    iapLtv30,
        cast(iapLtv36 / iapLtv1 as decimal (18, 2))                    iapLtv36,
        cast(iapLtv42 / iapLtv1 as decimal (18, 2))                    iapLtv42,
        cast(iapLtv48 / iapLtv1 as decimal (18, 2))                    iapLtv48,
        cast(iapLtv54 / iapLtv1 as decimal (18, 2))                    iapLtv54,
        cast(iapLtv60 / iapLtv1 as decimal (18, 2))                    iapLtv60,

        cast(iaaLtv2 / iaaLtv1 as  decimal (18, 2))                    iaaLtv2,
        cast(iaaLtv3 / iaaLtv1 as  decimal (18, 2))                    iaaLtv3,
        cast(iaaLtv4 / iaaLtv1 as  decimal (18, 2))                    iaaLtv4,
        cast(iaaLtv5 / iaaLtv1 as  decimal (18, 2))                    iaaLtv5,
        cast(iaaLtv6 / iaaLtv1 as  decimal (18, 2))                    iaaLtv6,
        cast(iaaLtv7 / iaaLtv1 as  decimal (18, 2))                    iaaLtv7,
        cast(iaaLtv8 / iaaLtv1 as  decimal (18, 2))                    iaaLtv8,
        cast(iaaLtv9 / iaaLtv1 as  decimal (18, 2))                    iaaLtv9,
        cast(iaaLtv10 / iaaLtv1 as  decimal (18, 2))                   iaaLtv10,
        cast(iaaLtv11 / iaaLtv1 as  decimal (18, 2))                   iaaLtv11,
        cast(iaaLtv12 / iaaLtv1 as  decimal (18, 2))                   iaaLtv12,
        cast(iaaLtv13 / iaaLtv1 as  decimal (18, 2))                   iaaLtv13,
        cast(iaaLtv14 / iaaLtv1 as  decimal (18, 2))                   iaaLtv14,
        cast(iaaLtv15 / iaaLtv1 as  decimal (18, 2))                   iaaLtv15,
        cast(iaaLtv16 / iaaLtv1 as  decimal (18, 2))                   iaaLtv16,
        cast(iaaLtv17 / iaaLtv1 as  decimal (18, 2))                   iaaLtv17,
        cast(iaaLtv18 / iaaLtv1 as  decimal (18, 2))                   iaaLtv18,
        cast(iaaLtv19 / iaaLtv1 as  decimal (18, 2))                   iaaLtv19,
        cast(iaaLtv20 / iaaLtv1 as  decimal (18, 2))                   iaaLtv20,
        cast(iaaLtv21 / iaaLtv1 as  decimal (18, 2))                   iaaLtv21,
        cast(iaaLtv22 / iaaLtv1 as  decimal (18, 2))                   iaaLtv22,
        cast(iaaLtv23 / iaaLtv1 as  decimal (18, 2))                   iaaLtv23,
        cast(iaaLtv24 / iaaLtv1 as  decimal (18, 2))                   iaaLtv24,
        cast(iaaLtv25 / iaaLtv1 as  decimal (18, 2))                   iaaLtv25,
        cast(iaaLtv26 / iaaLtv1 as  decimal (18, 2))                   iaaLtv26,
        cast(iaaLtv27 / iaaLtv1 as  decimal (18, 2))                   iaaLtv27,
        cast(iaaLtv28 / iaaLtv1 as  decimal (18, 2))                   iaaLtv28,
        cast(iaaLtv29 / iaaLtv1 as  decimal (18, 2))                   iaaLtv29,
        cast(iaaLtv30 / iaaLtv1 as  decimal (18, 2))                   iaaLtv30,
        cast(iaaLtv36 / iaaLtv1 as  decimal (18, 2))                   iaaLtv36,
        cast(iaaLtv42 / iaaLtv1 as  decimal (18, 2))                   iaaLtv42,
        cast(iaaLtv48 / iaaLtv1 as  decimal (18, 2))                   iaaLtv48,
        cast(iaaLtv54 / iaaLtv1 as  decimal (18, 2))                   iaaLtv54,
        cast(iaaLtv60 / iaaLtv1 as  decimal (18, 2))                   iaaLtv60

        from (

            <include refid="selectData"/>
        ) a

        where ltv1 != 0
        or iapLtv1 != 0
        or iaaLtv1 != 0


    </sql>

<!--    图表LTV 数据-->
    <sql id="select3">

        SELECT
            channel,
            sum(addUser)                                   addUser,
            cast(sum(ltv2) / sum(if(ltv2 > 0, ltv1, 0)) as decimal(18, 2))  ltv2,
            cast(sum(ltv3) / sum(if(ltv3 > 0, ltv1, 0)) as decimal(18, 2))  ltv3,
            cast(sum(ltv4) / sum(if(ltv4 > 0, ltv1, 0)) as decimal(18, 2))  ltv4,
            cast(sum(ltv5) / sum(if(ltv5 > 0, ltv1, 0)) as decimal(18, 2))  ltv5,
            cast(sum(ltv6) / sum(if(ltv6 > 0, ltv1, 0)) as decimal(18, 2))  ltv6,
            cast(sum(ltv7) / sum(if(ltv7 > 0, ltv1, 0)) as decimal(18, 2))  ltv7,
            cast(sum(ltv8) / sum(if(ltv8 > 0, ltv1, 0)) as decimal(18, 2))  ltv8,
            cast(sum(ltv9) / sum(if(ltv9 > 0, ltv1, 0)) as decimal(18, 2))  ltv9,
            cast(sum(ltv10) / sum(if(ltv10 > 0, ltv1, 0)) as decimal(18, 2)) ltv10,
            cast(sum(ltv11) / sum(if(ltv11 > 0, ltv1, 0)) as decimal(18, 2)) ltv11,
            cast(sum(ltv12) / sum(if(ltv12 > 0, ltv1, 0)) as decimal(18, 2)) ltv12,
            cast(sum(ltv13) / sum(if(ltv13 > 0, ltv1, 0)) as decimal(18, 2)) ltv13,
            cast(sum(ltv14) / sum(if(ltv14 > 0, ltv1, 0)) as decimal(18, 2)) ltv14,
            cast(sum(ltv15) / sum(if(ltv15 > 0, ltv1, 0)) as decimal(18, 2)) ltv15,
            cast(sum(ltv16) / sum(if(ltv16 > 0, ltv1, 0)) as decimal(18, 2)) ltv16,
            cast(sum(ltv17) / sum(if(ltv17 > 0, ltv1, 0)) as decimal(18, 2)) ltv17,
            cast(sum(ltv18) / sum(if(ltv18 > 0, ltv1, 0)) as decimal(18, 2)) ltv18,
            cast(sum(ltv19) / sum(if(ltv19 > 0, ltv1, 0)) as decimal(18, 2)) ltv19,
            cast(sum(ltv20) / sum(if(ltv20 > 0, ltv1, 0)) as decimal(18, 2)) ltv20,
            cast(sum(ltv21) / sum(if(ltv21 > 0, ltv1, 0)) as decimal(18, 2)) ltv21,
            cast(sum(ltv22) / sum(if(ltv22 > 0, ltv1, 0)) as decimal(18, 2)) ltv22,
            cast(sum(ltv23) / sum(if(ltv23 > 0, ltv1, 0)) as decimal(18, 2)) ltv23,
            cast(sum(ltv24) / sum(if(ltv24 > 0, ltv1, 0)) as decimal(18, 2)) ltv24,
            cast(sum(ltv25) / sum(if(ltv25 > 0, ltv1, 0)) as decimal(18, 2)) ltv25,
            cast(sum(ltv26) / sum(if(ltv26 > 0, ltv1, 0)) as decimal(18, 2)) ltv26,
            cast(sum(ltv27) / sum(if(ltv27 > 0, ltv1, 0)) as decimal(18, 2)) ltv27,
            cast(sum(ltv28) / sum(if(ltv28 > 0, ltv1, 0)) as decimal(18, 2)) ltv28,
            cast(sum(ltv29) / sum(if(ltv29 > 0, ltv1, 0)) as decimal(18, 2)) ltv29,
            cast(sum(ltv30) / sum(if(ltv30 > 0, ltv1, 0)) as decimal(18, 2)) ltv30,
            cast(sum(ltv36) / sum(if(ltv36 > 0, ltv1, 0)) as decimal(18, 2)) ltv36,
            cast(sum(ltv42) / sum(if(ltv42 > 0, ltv1, 0)) as decimal(18, 2)) ltv42,
            cast(sum(ltv48) / sum(if(ltv48 > 0, ltv1, 0)) as decimal(18, 2)) ltv48,
            cast(sum(ltv54) / sum(if(ltv54 > 0, ltv1, 0)) as decimal(18, 2)) ltv54,
            cast(sum(ltv60) / sum(if(ltv60 > 0, ltv1, 0)) as decimal(18, 2)) ltv60

        from (
                 select
                    tdate,
                     appid,
                     channel,
                     sum(add_user)                                      addUser,
                     sum(add_revenue_1) + sum(add_purchase_revenue_1)   ltv1,
                     sum(add_revenue_2) + sum(add_purchase_revenue_2)   ltv2,
                     sum(add_revenue_3) + sum(add_purchase_revenue_3)   ltv3,
                     sum(add_revenue_4) + sum(add_purchase_revenue_4)   ltv4,
                     sum(add_revenue_5) + sum(add_purchase_revenue_5)   ltv5,
                     sum(add_revenue_6) + sum(add_purchase_revenue_6)   ltv6,
                     sum(add_revenue_7) + sum(add_purchase_revenue_7)   ltv7,
                     sum(add_revenue_8) + sum(add_purchase_revenue_8)   ltv8,
                     sum(add_revenue_9) + sum(add_purchase_revenue_9)   ltv9,
                     sum(add_revenue_10) + sum(add_purchase_revenue_10) ltv10,
                     sum(add_revenue_11) + sum(add_purchase_revenue_11) ltv11,
                     sum(add_revenue_12) + sum(add_purchase_revenue_12) ltv12,
                     sum(add_revenue_13) + sum(add_purchase_revenue_13) ltv13,
                     sum(add_revenue_14) + sum(add_purchase_revenue_14) ltv14,
                     sum(add_revenue_15) + sum(add_purchase_revenue_15) ltv15,
                     sum(add_revenue_16) + sum(add_purchase_revenue_16) ltv16,
                     sum(add_revenue_17) + sum(add_purchase_revenue_17) ltv17,
                     sum(add_revenue_18) + sum(add_purchase_revenue_18) ltv18,
                     sum(add_revenue_19) + sum(add_purchase_revenue_19) ltv19,
                     sum(add_revenue_20) + sum(add_purchase_revenue_20) ltv20,
                     sum(add_revenue_21) + sum(add_purchase_revenue_21) ltv21,
                     sum(add_revenue_22) + sum(add_purchase_revenue_22) ltv22,
                     sum(add_revenue_23) + sum(add_purchase_revenue_23) ltv23,
                     sum(add_revenue_24) + sum(add_purchase_revenue_24) ltv24,
                     sum(add_revenue_25) + sum(add_purchase_revenue_25) ltv25,
                     sum(add_revenue_26) + sum(add_purchase_revenue_26) ltv26,
                     sum(add_revenue_27) + sum(add_purchase_revenue_27) ltv27,
                     sum(add_revenue_28) + sum(add_purchase_revenue_28) ltv28,
                     sum(add_revenue_29) + sum(add_purchase_revenue_29) ltv29,
                     sum(add_revenue_30) + sum(add_purchase_revenue_30) ltv30,
                     sum(add_revenue_36) + sum(add_purchase_revenue_36) ltv36,
                     sum(add_revenue_42) + sum(add_purchase_revenue_42) ltv42,
                     sum(add_revenue_48) + sum(add_purchase_revenue_48) ltv48,
                     sum(add_revenue_54) + sum(add_purchase_revenue_54) ltv54,
                     sum(add_revenue_60) + sum(add_purchase_revenue_60) ltv60


                 from ads_channel_ltv_info_daily

                 <include refid="condition"/>

                 group by tdate, appid, channel


             ) a

        group by channel

        having sum(ltv1) > 0
        order by channel

    </sql>

<!--    IAP_LTV数据-->
    <sql id="select4">

        select
            channel,
            cast(sum(iapLtv2) / sum(if(iapLtv2 > 0, iapLtv1, 0)) as decimal(18, 2))  iapLtv2,
            cast(sum(iapLtv3) / sum(if(iapLtv3 > 0, iapLtv1, 0)) as decimal(18, 2))  iapLtv3,
            cast(sum(iapLtv4) / sum(if(iapLtv4 > 0, iapLtv1, 0)) as decimal(18, 2))  iapLtv4,
            cast(sum(iapLtv5) / sum(if(iapLtv5 > 0, iapLtv1, 0)) as decimal(18, 2))  iapLtv5,
            cast(sum(iapLtv6) / sum(if(iapLtv6 > 0, iapLtv1, 0)) as decimal(18, 2))  iapLtv6,
            cast(sum(iapLtv7) / sum(if(iapLtv7 > 0, iapLtv1, 0)) as decimal(18, 2))  iapLtv7,
            cast(sum(iapLtv8) / sum(if(iapLtv8 > 0, iapLtv1, 0)) as decimal(18, 2))  iapLtv8,
            cast(sum(iapLtv9) / sum(if(iapLtv9 > 0, iapLtv1, 0)) as decimal(18, 2))  iapLtv9,
            cast(sum(iapLtv10) / sum(if(iapLtv10 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv10,
            cast(sum(iapLtv11) / sum(if(iapLtv11 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv11,
            cast(sum(iapLtv12) / sum(if(iapLtv12 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv12,
            cast(sum(iapLtv13) / sum(if(iapLtv13 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv13,
            cast(sum(iapLtv14) / sum(if(iapLtv14 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv14,
            cast(sum(iapLtv15) / sum(if(iapLtv15 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv15,
            cast(sum(iapLtv16) / sum(if(iapLtv16 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv16,
            cast(sum(iapLtv17) / sum(if(iapLtv17 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv17,
            cast(sum(iapLtv18) / sum(if(iapLtv18 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv18,
            cast(sum(iapLtv19) / sum(if(iapLtv19 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv19,
            cast(sum(iapLtv20) / sum(if(iapLtv20 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv20,
            cast(sum(iapLtv21) / sum(if(iapLtv21 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv21,
            cast(sum(iapLtv22) / sum(if(iapLtv22 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv22,
            cast(sum(iapLtv23) / sum(if(iapLtv23 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv23,
            cast(sum(iapLtv24) / sum(if(iapLtv24 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv24,
            cast(sum(iapLtv25) / sum(if(iapLtv25 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv25,
            cast(sum(iapLtv26) / sum(if(iapLtv26 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv26,
            cast(sum(iapLtv27) / sum(if(iapLtv27 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv27,
            cast(sum(iapLtv28) / sum(if(iapLtv28 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv28,
            cast(sum(iapLtv29) / sum(if(iapLtv29 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv29,
            cast(sum(iapLtv30) / sum(if(iapLtv30 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv30,
            cast(sum(iapLtv36) / sum(if(iapLtv36 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv36,
            cast(sum(iapLtv42) / sum(if(iapLtv42 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv42,
            cast(sum(iapLtv48) / sum(if(iapLtv48 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv48,
            cast(sum(iapLtv54) / sum(if(iapLtv54 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv54,
            cast(sum(iapLtv60) / sum(if(iapLtv60 > 0, iapLtv1, 0)) as decimal(18, 2)) iapLtv60
        from (
                 select
                        tdate,
                        appid,
                        channel,
                        sum(add_user)                addUser,
                        sum(add_purchase_revenue_1)  iapLtv1,
                        sum(add_purchase_revenue_2)  iapLtv2,
                        sum(add_purchase_revenue_3)  iapLtv3,
                        sum(add_purchase_revenue_4)  iapLtv4,
                        sum(add_purchase_revenue_5)  iapLtv5,
                        sum(add_purchase_revenue_6)  iapLtv6,
                        sum(add_purchase_revenue_7)  iapLtv7,
                        sum(add_purchase_revenue_8)  iapLtv8,
                        sum(add_purchase_revenue_9)  iapLtv9,
                        sum(add_purchase_revenue_10) iapLtv10,
                        sum(add_purchase_revenue_11) iapLtv11,
                        sum(add_purchase_revenue_12) iapLtv12,
                        sum(add_purchase_revenue_13) iapLtv13,
                        sum(add_purchase_revenue_14) iapLtv14,
                        sum(add_purchase_revenue_15) iapLtv15,
                        sum(add_purchase_revenue_16) iapLtv16,
                        sum(add_purchase_revenue_17) iapLtv17,
                        sum(add_purchase_revenue_18) iapLtv18,
                        sum(add_purchase_revenue_19) iapLtv19,
                        sum(add_purchase_revenue_20) iapLtv20,
                        sum(add_purchase_revenue_21) iapLtv21,
                        sum(add_purchase_revenue_22) iapLtv22,
                        sum(add_purchase_revenue_23) iapLtv23,
                        sum(add_purchase_revenue_24) iapLtv24,
                        sum(add_purchase_revenue_25) iapLtv25,
                        sum(add_purchase_revenue_26) iapLtv26,
                        sum(add_purchase_revenue_27) iapLtv27,
                        sum(add_purchase_revenue_28) iapLtv28,
                        sum(add_purchase_revenue_29) iapLtv29,
                        sum(add_purchase_revenue_30) iapLtv30,
                        sum(add_purchase_revenue_36) iapLtv36,
                        sum(add_purchase_revenue_42) iapLtv42,
                        sum(add_purchase_revenue_48) iapLtv48,
                        sum(add_purchase_revenue_54) iapLtv54,
                        sum(add_purchase_revenue_60) iapLtv60

                 from ads_channel_ltv_info_daily


                 <include refid="condition"/>

                 group by tdate, appid, channel


             ) a

        group by channel

        having sum(iapLtv1) > 0
        order by channel

    </sql>

    <sql id="select5">

        select
            channel,
            cast(sum(iaaLtv2) / sum(if(iaaLtv2 > 0, iaaLtv1, 0)) as decimal(18, 2))  iaaLtv2,
            cast(sum(iaaLtv3) / sum(if(iaaLtv3 > 0, iaaLtv1, 0)) as decimal(18, 2))  iaaLtv3,
            cast(sum(iaaLtv4) / sum(if(iaaLtv4 > 0, iaaLtv1, 0)) as decimal(18, 2))  iaaLtv4,
            cast(sum(iaaLtv5) / sum(if(iaaLtv5 > 0, iaaLtv1, 0)) as decimal(18, 2))  iaaLtv5,
            cast(sum(iaaLtv6) / sum(if(iaaLtv6 > 0, iaaLtv1, 0)) as decimal(18, 2))  iaaLtv6,
            cast(sum(iaaLtv7) / sum(if(iaaLtv7 > 0, iaaLtv1, 0)) as decimal(18, 2))  iaaLtv7,
            cast(sum(iaaLtv8) / sum(if(iaaLtv8 > 0, iaaLtv1, 0)) as decimal(18, 2))  iaaLtv8,
            cast(sum(iaaLtv9) / sum(if(iaaLtv9 > 0, iaaLtv1, 0)) as decimal(18, 2))  iaaLtv9,
            cast(sum(iaaLtv10) / sum(if(iaaLtv10 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv10,
            cast(sum(iaaLtv11) / sum(if(iaaLtv11 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv11,
            cast(sum(iaaLtv12) / sum(if(iaaLtv12 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv12,
            cast(sum(iaaLtv13) / sum(if(iaaLtv13 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv13,
            cast(sum(iaaLtv14) / sum(if(iaaLtv14 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv14,
            cast(sum(iaaLtv15) / sum(if(iaaLtv15 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv15,
            cast(sum(iaaLtv16) / sum(if(iaaLtv16 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv16,
            cast(sum(iaaLtv17) / sum(if(iaaLtv17 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv17,
            cast(sum(iaaLtv18) / sum(if(iaaLtv18 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv18,
            cast(sum(iaaLtv19) / sum(if(iaaLtv19 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv19,
            cast(sum(iaaLtv20) / sum(if(iaaLtv20 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv20,
            cast(sum(iaaLtv21) / sum(if(iaaLtv21 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv21,
            cast(sum(iaaLtv22) / sum(if(iaaLtv22 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv22,
            cast(sum(iaaLtv23) / sum(if(iaaLtv23 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv23,
            cast(sum(iaaLtv24) / sum(if(iaaLtv24 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv24,
            cast(sum(iaaLtv25) / sum(if(iaaLtv25 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv25,
            cast(sum(iaaLtv26) / sum(if(iaaLtv26 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv26,
            cast(sum(iaaLtv27) / sum(if(iaaLtv27 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv27,
            cast(sum(iaaLtv28) / sum(if(iaaLtv28 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv28,
            cast(sum(iaaLtv29) / sum(if(iaaLtv29 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv29,
            cast(sum(iaaLtv30) / sum(if(iaaLtv30 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv30,
            cast(sum(iaaLtv36) / sum(if(iaaLtv36 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv36,
            cast(sum(iaaLtv42) / sum(if(iaaLtv42 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv42,
            cast(sum(iaaLtv48) / sum(if(iaaLtv48 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv48,
            cast(sum(iaaLtv54) / sum(if(iaaLtv54 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv54,
            cast(sum(iaaLtv60) / sum(if(iaaLtv60 > 0, iaaLtv1, 0)) as decimal(18, 2)) iaaLtv60
        from (
                 select
                        tdate,
                        appid,
                        channel,
                        sum(add_user)                addUser,
                        sum(add_revenue_1)  iaaLtv1,
                        sum(add_revenue_2)  iaaLtv2,
                        sum(add_revenue_3)  iaaLtv3,
                        sum(add_revenue_4)  iaaLtv4,
                        sum(add_revenue_5)  iaaLtv5,
                        sum(add_revenue_6)  iaaLtv6,
                        sum(add_revenue_7)  iaaLtv7,
                        sum(add_revenue_8)  iaaLtv8,
                        sum(add_revenue_9)  iaaLtv9,
                        sum(add_revenue_10) iaaLtv10,
                        sum(add_revenue_11) iaaLtv11,
                        sum(add_revenue_12) iaaLtv12,
                        sum(add_revenue_13) iaaLtv13,
                        sum(add_revenue_14) iaaLtv14,
                        sum(add_revenue_15) iaaLtv15,
                        sum(add_revenue_16) iaaLtv16,
                        sum(add_revenue_17) iaaLtv17,
                        sum(add_revenue_18) iaaLtv18,
                        sum(add_revenue_19) iaaLtv19,
                        sum(add_revenue_20) iaaLtv20,
                        sum(add_revenue_21) iaaLtv21,
                        sum(add_revenue_22) iaaLtv22,
                        sum(add_revenue_23) iaaLtv23,
                        sum(add_revenue_24) iaaLtv24,
                        sum(add_revenue_25) iaaLtv25,
                        sum(add_revenue_26) iaaLtv26,
                        sum(add_revenue_27) iaaLtv27,
                        sum(add_revenue_28) iaaLtv28,
                        sum(add_revenue_29) iaaLtv29,
                        sum(add_revenue_30) iaaLtv30,
                        sum(add_revenue_36) iaaLtv36,
                        sum(add_revenue_42) iaaLtv42,
                        sum(add_revenue_48) iaaLtv48,
                        sum(add_revenue_54) iaaLtv54,
                        sum(add_revenue_60) iaaLtv60

                 from ads_channel_ltv_info_daily


                <include refid="condition"/>

                 group by tdate, appid, channel


             ) a


        group by channel

        having sum(iaaLtv1) > 0

        order by channel
    </sql>

    <sql id="condition">

       <where>
           <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
               and tdate between #{start_date} and #{end_date}
           </if>

           <if test="ver != null and ver != ''">
               and ver = #{ver}
           </if>

           <if test="channelList != null and channelList.size > 0">
               AND channel IN
               <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                   #{channel}
               </foreach>
           </if>

           <if test="appidList != null and appidList.size > 0">
               AND appid IN
               <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                   #{appid}
               </foreach>
           </if>

           <if test="prjidList != null and prjidList.size > 0">
               AND pid IN
               <foreach collection="prjidList" item="prj" open="(" separator="," close=")">
                   #{prj}
               </foreach>
           </if>
       </where>
    </sql>
</mapper>