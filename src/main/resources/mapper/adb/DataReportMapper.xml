<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.DataReportMapper">

	<select id="selectToolOperateReport" resultType="java.util.Map">
		select tdate,app_name,media,buy_act,brand,add_num,act_num
		,concat(add_rate,'%') add_rate
		,advert_arpu,inside_splash_avg,inside_plaque_avg,inside_video_avg,inside_msg_avg
		,outside_splash_avg,outside_plaque_avg,outside_video_avg,outside_msg_avg
		,ecpm_splash,ecpm_plaque,ecpm_video,ecpm_msg
		,concat(news_seep_rate,'%') news_seep_rate
		,concat(adv_effect,'%') adv_effect
		,news_avg,adv_show_avg
		,concat(permiss_show_rate,'%') permiss_show_rate
		,concat(permiss_result_rate,'%') permiss_result_rate
		,concat(home_rate,'%') home_rate
		,concat(alive5,'%') alive5
		,concat(alive15,'%') alive15
		,concat(alive60,'%') alive60
		,concat(keep1,'%') keep1,concat(keep3,'%') keep3,concat(keep7,'%') keep7
		,baidu_arpu
		,baidupv
		,concat(baidu_click_rate,'%') baidu_click_rate
		,baidu_ecpm
		from
		(select tdate,app_name,ad_buy_media media,account as buy_act,brand,sum(base_increment_user) add_num,sum(base_active_user_count) act_num
		,round(sum(bh_5_count)/sum(base_increment_user)*100,2) alive5,round(sum(bh_15_count)/sum(base_increment_user)*100,2) alive15,round(sum(bh_60_count)/sum(base_increment_user)*100,2) alive60
		,ifnull(round(sum(base_increment_user)/sum(base_active_user_count)*100,2),0) add_rate
		,ifnull(round(sum(base_advertise_revenue)/100/sum(base_active_user_count),2),0) advert_arpu
		,ifnull(round(sum(inside_splash_count)/sum(base_active_user_count),2),0) inside_splash_avg
		,ifnull(round(sum(inside_plaque_count)/sum(base_active_user_count),2),0) inside_plaque_avg
		,ifnull(round(sum(inside_video_count)/sum(base_active_user_count),2),0) inside_video_avg
		,ifnull(round(sum(inside_msg_count)/sum(base_active_user_count),2),0) inside_msg_avg
		,ifnull(round(sum(outside_splash_count)/sum(base_active_user_count),2),0) outside_splash_avg
		,ifnull(round(sum(outside_plaque_count)/sum(base_active_user_count),2),0) outside_plaque_avg
		,ifnull(round(sum(outside_video_count)/sum(base_active_user_count),2),0) outside_video_avg
		,ifnull(round(sum(outside_msg_count)/sum(base_active_user_count),2),0) outside_msg_avg
		,ifnull(round(sum(ecpm_splash_revenue)/100/sum(ecpm_splash_count)*1000,2),0) ecpm_splash
		,ifnull(round(sum(ecpm_plaque_revenue)/100/sum(ecpm_plaque_count)*1000,2),0) ecpm_plaque
		,ifnull(round(sum(ecpm_video_revenue)/100/sum(ecpm_video_count)*1000,2),0) ecpm_video
		,ifnull(round(sum(ecpm_msg_revenue)/100/sum(ecpm_msg_count)*1000,2),0) ecpm_msg
		,ifnull(round(sum(lock_news_display_success_user_count)/sum(base_active_user_count)*100,2),0) news_seep_rate
		,ifnull(round(sum(lock_ad_display_count)/sum(lock_news_display_success_cnt)*100,2),0) adv_effect
		,ifnull(round(sum(lock_news_display_success_cnt)/sum(base_active_user_count),2),0) news_avg
		,ifnull(round(sum(lock_ad_display_count)/sum(base_active_user_count),2),0) adv_show_avg
		,ifnull(round(sum(permissions_page_display_user_count)/sum(base_increment_user)*100,2),0) permiss_show_rate
		,ifnull(round(sum(permissions_result_page_display_user_count)/sum(base_increment_user)*100,2),0) permiss_result_rate
		,ifnull(round(sum(homepage_display_user_count)/sum(base_increment_user)*100,2),0) home_rate
		,ifnull(round(sum(retention_1day_user_count)/sum(base_increment_user)*100,2),0) keep1
		,ifnull(round(sum(retention_3day_user_count)/sum(base_increment_user)*100,2),0) keep3
		,ifnull(round(sum(retention_7day_user_count)/sum(base_increment_user)*100,2),0) keep7
		,baidu_arpu
		,baidupv
		,baidu_click_rate
		,baidu_ecpm
		from dnwx_bi.ads_product_operation_analyze_daily a left join
		<choose>
			<when test="baidugroup == 1">
				(select baiduappid,ifnull(round(sum(baidu_revenue)/100/sum(base_active_user_count),2),0) baidu_arpu
				,ifnull(round(sum(baidu_pv)/sum(base_active_user_count),2),0) baidupv
				,round(sum(baidu_click)/sum(baidu_show_count)*100,2) baidu_click_rate
				,round(sum(baidu_revenue)/100/sum(baidu_show_count)*10,2) baidu_ecpm
				from
				(select tdate baidutdate,appid baiduappid,baidu_revenue,sum(base_active_user_count) base_active_user_count
				,baidu_pv,baidu_click,baidu_show_count
				from dnwx_bi.ads_product_operation_analyze_daily
				<where> tdate between #{startTime} and #{endTime}
					<if test="appid != null and appid != ''">
						and appid = #{appid}
					</if>
					group by tdate,appid
				</where>) a group by baiduappid) b
				on a.appid = b.baiduappid
			</when>
			<otherwise>
				(select tdate baidutdate,appid baiduappid,ifnull(round(baidu_revenue/100/sum(base_active_user_count),2),0) baidu_arpu
				,ifnull(round(baidu_pv/sum(base_active_user_count),2),0) baidupv
				,ifnull(round(baidu_click/baidu_show_count,2),0) baidu_click_rate
				,ifnull(round(baidu_revenue/baidu_show_count*10,2),0) baidu_ecpm
				from dnwx_bi.ads_product_operation_analyze_daily
				<where> tdate between #{startTime} and #{endTime}
					<if test="appid != null and appid != ''">
						and appid = #{appid}
					</if>
					group by tdate,appid
				</where>) b
				on a.tdate = b.baidutdate and a.appid = b.baiduappid
			</otherwise>
		</choose>
		<where> tdate between #{startTime} and #{endTime} and base_active_user_count >= 1
			<if test="appid != null and appid != ''">
				and appid = #{appid}
			</if>
			<if test="media != null and media != ''">
				and ad_buy_media in (${media})
			</if>
			<if test="account != null and account != ''">
				and account in (${account})
			</if>
			<if test="brand != null and brand != ''">
				and brand in (${brand})
			</if>
		</where>
		GROUP BY ${group}
		order by ${order}) a
	</select>

	<select id="selectUmengDateUpdateTime" resultType="java.lang.String">
      select DATE_FORMAT(tb_lst_time,'%Y-%m-%d %H:%i:%s') from dnwx_bi.ads_table_finish_time
      where tb_name = #{table}
    </select>

    <select id="selectAddKeepModelReport" resultType="java.util.Map">
       select a.tdate,a.appid,a.app_name,a.download_channel,a.app_ver,a.model
       ,a.add_num,a.act_num
       ,concat(ifnull(add_rate,0),'%') add_rate
	   ,concat(ifnull(brand_rate,0),'%') brand_rate
	   ,concat(ifnull(keep1,0),'%') keep1
	   ,concat(ifnull(keep2,0),'%') keep2
	   ,concat(ifnull(keep3,0),'%') keep3
	   ,concat(ifnull(keep4,0),'%') keep4
	   ,concat(ifnull(keep5,0),'%') keep5
	   ,concat(ifnull(keep6,0),'%') keep6
	   ,concat(ifnull(keep7,0),'%') keep7
	   ,concat(ifnull(keep14,0),'%') keep14
	   ,concat(ifnull(keep30,0),'%') keep30
	   ,concat(ifnull(keep60,0),'%') keep60
       from
       (select a.tdate,a.appid,a.app_name,a.download_channel,a.app_ver,a.model,a.brand
       ,sum(reg_user_cnt) add_num
       ,sum(active_user_cnt) act_num
       ,round(sum(reg_user_cnt)/sum(active_user_cnt)*100,2) add_rate
	   ,round(sum(a.reg_user_cnt)/b.brand_num*100,2) brand_rate
       ,round(sum(reg_retention_1day_user_cnt)/sum(reg_user_cnt)*100,2) keep1
	   ,round(sum(reg_retention_2day_user_cnt)/sum(reg_user_cnt)*100,2) keep2
	   ,round(sum(reg_retention_3day_user_cnt)/sum(reg_user_cnt)*100,2) keep3
	   ,round(sum(reg_retention_4day_user_cnt)/sum(reg_user_cnt)*100,2) keep4
	   ,round(sum(reg_retention_5day_user_cnt)/sum(reg_user_cnt)*100,2) keep5
	   ,round(sum(reg_retention_6day_user_cnt)/sum(reg_user_cnt)*100,2) keep6
	   ,round(sum(reg_retention_7day_user_cnt)/sum(reg_user_cnt)*100,2) keep7
	   ,round(sum(reg_retention_14day_user_cnt)/sum(reg_user_cnt)*100,2) keep14
	   ,round(sum(reg_retention_30day_user_cnt)/sum(reg_user_cnt)*100,2) keep30
	   ,round(sum(reg_retention_60day_user_cnt)/sum(reg_user_cnt)*100,2) keep60
       from dnwx_bi.new_ads_device_brand_model_reg_retention_daily a
       left join
       (select tdate btdate,appid bappid,download_channel bdownload_channel,app_ver bapp_ver,brand bbrand,sum(reg_user_cnt) brand_num from dnwx_bi.new_ads_device_brand_reg_retention_daily
		<where> tdate between #{startTime} and #{endTime} and appid = #{appid} and reg_user_cnt >= 1
			<if test="channel != null and channel != ''">
				and download_channel in (${channel})
			</if>
			<if test="version != null and version != ''">
				and app_ver in (${version})
			</if>
		</where>
       group by ${bgroup}) b
       on ${join}
		<where> tdate between #{startTime} and #{endTime} and appid = #{appid} and reg_user_cnt >= 1
			<if test="channel != null and channel != ''">
				and download_channel in (${channel})
			</if>
			<if test="version != null and version != ''">
				and app_ver in (${version})
			</if>
			<if test="model != null and model != ''">
				and model = #{model}
			</if>
		</where>
		group by ${group}
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by tdate desc
			</otherwise>
		</choose>
       ) a
	</select>

    <select id="selectAddKeepBrandReport" resultType="java.util.Map">
		select a.tdate,a.appid,a.app_name,a.download_channel,a.app_ver,a.brand
		,a.add_num,a.act_num
		,concat(ifnull(add_rate,0),'%') add_rate
		,concat(ifnull(keep1,0),'%') keep1
		,concat(ifnull(keep2,0),'%') keep2
		,concat(ifnull(keep3,0),'%') keep3
		,concat(ifnull(keep4,0),'%') keep4
		,concat(ifnull(keep5,0),'%') keep5
		,concat(ifnull(keep6,0),'%') keep6
		,concat(ifnull(keep7,0),'%') keep7
		,concat(ifnull(keep14,0),'%') keep14
		,concat(ifnull(keep30,0),'%') keep30
		,concat(ifnull(keep60,0),'%') keep60
		from
		(select a.tdate,a.appid,a.app_name,a.download_channel,a.app_ver,a.brand
		,sum(reg_user_cnt) add_num
		,sum(active_user_cnt) act_num
		,round(sum(reg_user_cnt)/sum(active_user_cnt)*100,2) add_rate
		,round(sum(reg_retention_1day_user_cnt)/sum(reg_user_cnt)*100,2) keep1
		,round(sum(reg_retention_2day_user_cnt)/sum(reg_user_cnt)*100,2) keep2
		,round(sum(reg_retention_3day_user_cnt)/sum(reg_user_cnt)*100,2) keep3
		,round(sum(reg_retention_4day_user_cnt)/sum(reg_user_cnt)*100,2) keep4
		,round(sum(reg_retention_5day_user_cnt)/sum(reg_user_cnt)*100,2) keep5
		,round(sum(reg_retention_6day_user_cnt)/sum(reg_user_cnt)*100,2) keep6
		,round(sum(reg_retention_7day_user_cnt)/sum(reg_user_cnt)*100,2) keep7
		,round(sum(reg_retention_14day_user_cnt)/sum(reg_user_cnt)*100,2) keep14
		,round(sum(reg_retention_30day_user_cnt)/sum(reg_user_cnt)*100,2) keep30
		,round(sum(reg_retention_60day_user_cnt)/sum(reg_user_cnt)*100,2) keep60
		from dnwx_bi.new_ads_device_brand_reg_retention_daily a
		<where> tdate between #{startTime} and #{endTime} and appid = #{appid} and reg_user_cnt >= 1
			<if test="channel != null and channel != ''">
				and download_channel in (${channel})
			</if>
			<if test="version != null and version != ''">
				and app_ver in (${version})
			</if>
			<if test="brand != null and brand != ''">
				and brand = #{brand}
			</if>
		</where>
		group by ${group}
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by tdate desc
			</otherwise>
		</choose>
		) a
	</select>

	<select id="selectActKeepModelReport" resultType="java.util.Map">
		select a.tdate,a.appid,a.app_name,a.download_channel,a.app_ver,a.model
		,a.add_num,a.act_num
		,concat(ifnull(add_rate,0),'%') add_rate
		,concat(ifnull(brand_rate,0),'%') brand_rate
		,concat(ifnull(keep1,0),'%') keep1
		,concat(ifnull(keep2,0),'%') keep2
		,concat(ifnull(keep3,0),'%') keep3
		,concat(ifnull(keep4,0),'%') keep4
		,concat(ifnull(keep5,0),'%') keep5
		,concat(ifnull(keep6,0),'%') keep6
		,concat(ifnull(keep7,0),'%') keep7
		,concat(ifnull(keep14,0),'%') keep14
		,concat(ifnull(keep30,0),'%') keep30
		,concat(ifnull(keep60,0),'%') keep60
		from
		(select a.tdate,a.appid,a.app_name,a.download_channel,a.app_ver,a.model,a.brand
		,sum(reg_user_cnt) add_num
		,sum(active_user_cnt) act_num
		,round(sum(reg_user_cnt)/sum(active_user_cnt)*100,2) add_rate
		,round(sum(a.reg_user_cnt)/b.brand_num*100,2) brand_rate
		,round(sum(active_retention_1day_user_cnt)/sum(active_user_cnt)*100,2) keep1
		,round(sum(active_retention_2day_user_cnt)/sum(active_user_cnt)*100,2) keep2
		,round(sum(active_retention_3day_user_cnt)/sum(active_user_cnt)*100,2) keep3
		,round(sum(active_retention_4day_user_cnt)/sum(active_user_cnt)*100,2) keep4
		,round(sum(active_retention_5day_user_cnt)/sum(active_user_cnt)*100,2) keep5
		,round(sum(active_retention_6day_user_cnt)/sum(active_user_cnt)*100,2) keep6
		,round(sum(active_retention_7day_user_cnt)/sum(active_user_cnt)*100,2) keep7
		,round(sum(active_retention_14day_user_cnt)/sum(active_user_cnt)*100,2) keep14
		,round(sum(active_retention_30day_user_cnt)/sum(active_user_cnt)*100,2) keep30
		,round(sum(active_retention_60day_user_cnt)/sum(active_user_cnt)*100,2) keep60
		from dnwx_bi.new_ads_device_brand_model_active_retention_daily a
		left join
		(select tdate btdate,appid bappid,download_channel bdownload_channel,app_ver bapp_ver,brand bbrand,sum(reg_user_cnt) brand_num from dnwx_bi.new_ads_device_brand_active_retention_daily
		<where> tdate between #{startTime} and #{endTime} and appid = #{appid} and active_user_cnt >= 1
			<if test="channel != null and channel != ''">
				and download_channel in (${channel})
			</if>
			<if test="version != null and version != ''">
				and app_ver in (${version})
			</if>
		</where>
		group by ${bgroup}) b
		on ${join}
		<where> tdate between #{startTime} and #{endTime} and appid = #{appid} and active_user_cnt >= 1
			<if test="channel != null and channel != ''">
				and download_channel in (${channel})
			</if>
			<if test="version != null and version != ''">
				and app_ver in (${version})
			</if>
			<if test="model != null and model != ''">
				and model = #{model}
			</if>
		</where>
		group by ${group}
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by tdate desc
			</otherwise>
		</choose>
		) a
	</select>

	<select id="selectActKeepBrandReport" resultType="java.util.Map">
		select a.tdate,a.appid,a.app_name,a.download_channel,a.app_ver,a.brand
		,a.add_num,a.act_num
		,concat(ifnull(add_rate,0),'%') add_rate
		,concat(ifnull(keep1,0),'%') keep1
		,concat(ifnull(keep2,0),'%') keep2
		,concat(ifnull(keep3,0),'%') keep3
		,concat(ifnull(keep4,0),'%') keep4
		,concat(ifnull(keep5,0),'%') keep5
		,concat(ifnull(keep6,0),'%') keep6
		,concat(ifnull(keep7,0),'%') keep7
		,concat(ifnull(keep14,0),'%') keep14
		,concat(ifnull(keep30,0),'%') keep30
		,concat(ifnull(keep60,0),'%') keep60
		from
		(select a.tdate,a.appid,a.app_name,a.download_channel,a.app_ver,a.brand
		,sum(reg_user_cnt) add_num
		,sum(active_user_cnt) act_num
		,round(sum(reg_user_cnt)/sum(active_user_cnt)*100,2) add_rate
		,round(sum(active_retention_1day_user_cnt)/sum(active_user_cnt)*100,2) keep1
		,round(sum(active_retention_2day_user_cnt)/sum(active_user_cnt)*100,2) keep2
		,round(sum(active_retention_3day_user_cnt)/sum(active_user_cnt)*100,2) keep3
		,round(sum(active_retention_4day_user_cnt)/sum(active_user_cnt)*100,2) keep4
		,round(sum(active_retention_5day_user_cnt)/sum(active_user_cnt)*100,2) keep5
		,round(sum(active_retention_6day_user_cnt)/sum(active_user_cnt)*100,2) keep6
		,round(sum(active_retention_7day_user_cnt)/sum(active_user_cnt)*100,2) keep7
		,round(sum(active_retention_14day_user_cnt)/sum(active_user_cnt)*100,2) keep14
		,round(sum(active_retention_30day_user_cnt)/sum(active_user_cnt)*100,2) keep30
		,round(sum(active_retention_60day_user_cnt)/sum(active_user_cnt)*100,2) keep60
		from dnwx_bi.new_ads_device_brand_active_retention_daily a
		<where> tdate between #{startTime} and #{endTime} and appid = #{appid} and active_user_cnt >= 1
			<if test="channel != null and channel != ''">
				and download_channel in (${channel})
			</if>
			<if test="version != null and version != ''">
				and app_ver in (${version})
			</if>
			<if test="brand != null and brand != ''">
				and brand = #{brand}
			</if>
		</where>
		group by ${group}
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by tdate desc
			</otherwise>
		</choose>
		) a
	</select>

    <select id="selectrealPidKeep" resultType="java.util.Map">
		select right(dthour,2) hour,sum(active_user) active_user,round(sum(retention_hourly)/sum(add_user)*100,2) keep_rate,sum(retention_hourly) keep_num
        from dnwx_bi.ads_new_stay_channel_pid_hourly where appid = #{appid} and dthour between concat(#{tdate},'01') and concat(#{tdate},'24')
        <if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="channel != null and channel != ''">
			and channel = #{channel}
		</if>
		group by dthour
		order by dthour asc
	</select>


	<select id="getAccounts" resultType="java.lang.String">
		select account from dnwx_bi.ads_product_operation_analyze_daily where appid = #{appid} and account != -1 GROUP BY account
	</select>


	<select id="getBrands" resultType="java.lang.String">
		select brand from dnwx_bi.ads_product_operation_analyze_daily where appid = #{appid} GROUP BY brand
	</select>

	<select id="selectKeepUserIpu" resultType="java.util.Map">
        select  `day`,app_id,game_name,ad_buy_media,account,ad_buy_channel,ad_type,user_type,spread_name,campaign_id,campaign_name
		,user_number
		,pv_avg_number
		,median_pv_number
		,concat(0P,'%') 0P
		,concat(1P,'%') 1P
		,concat(2P,'%') 2P
		,concat(3P,'%') 3P
		,concat(4P,'%') 4P
		,concat(5P,'%') 5P
		,concat(6P,'%') 6P
		,concat(7P,'%') 7P
		,concat(8P,'%') 8P
		,concat(9P,'%') 9P
		,concat(10P,'%') 10P
		,concat(11P,'%') 11P
		,concat(12P,'%') 12P
		,concat(13P,'%') 13P
		,concat(14P,'%') 14P
		,concat(15P,'%') 15P
		,concat(16_20P,'%') 16_20P
		,concat(21_25P,'%') 21_25P
		,concat(26_30P,'%') 26_30P
		,concat(31_35P,'%') 31_35P
		,concat(36_40P,'%') 36_40P
		,concat(41_45P,'%') 41_45P
		,concat(46_50P,'%') 46_50P
		,concat(50P_more,'%') 50P_more
		from
        (select `day`,app_id,app_name game_name,ad_buy_media,account,ad_buy_channel,ad_type,user_type,spread_name,campaign_id,campaign_name
		,sum(user_number) user_number
		,round(sum(sum_pv_number)/sum(user_number),2) pv_avg_number
		,median_pv_number
		,round(sum(P_0)/sum(user_number)*100,2) 0P
		,round(sum(P_1)/sum(user_number)*100,2) 1P
		,round(sum(P_2)/sum(user_number)*100,2) 2P
		,round(sum(P_3)/sum(user_number)*100,2) 3P
		,round(sum(P_4)/sum(user_number)*100,2) 4P
		,round(sum(P_5)/sum(user_number)*100,2) 5P
		,round(sum(P_6)/sum(user_number)*100,2) 6P
		,round(sum(P_7)/sum(user_number)*100,2) 7P
		,round(sum(P_8)/sum(user_number)*100,2) 8P
		,round(sum(P_9)/sum(user_number)*100,2) 9P
		,round(sum(P_10)/sum(user_number)*100,2) 10P
		,round(sum(P_11)/sum(user_number)*100,2) 11P
		,round(sum(P_12)/sum(user_number)*100,2) 12P
		,round(sum(P_13)/sum(user_number)*100,2) 13P
		,round(sum(P_14)/sum(user_number)*100,2) 14P
		,round(sum(P_15)/sum(user_number)*100,2) 15P
		,round(sum(P_16_20)/sum(user_number)*100,2) 16_20P
		,round(sum(P_21_25)/sum(user_number)*100,2) 21_25P
		,round(sum(P_26_30)/sum(user_number)*100,2) 26_30P
		,round(sum(P_31_35)/sum(user_number)*100,2) 31_35P
		,round(sum(P_36_40)/sum(user_number)*100,2) 36_40P
		,round(sum(P_41_45)/sum(user_number)*100,2) 41_45P
		,round(sum(P_46_50)/sum(user_number)*100,2) 46_50P
		,round(sum(P_50_more)/sum(user_number)*100,2) 50P_more
		FROM dnwx_bi.ads_retention_user_ipu_daily a
		left join dnwx_bi.dim_app_game b on a.app_id = b.appid
		where `day` between #{startTime} and #{endTime} and app_id in (${appid}) and ad_type in (${ad_type})
		<if test="account != null and account != ''">
			and account = #{account}
		</if>
		<if test="ad_buy_channel != null and ad_buy_channel != '' ">
			and ad_buy_channel in (${ad_buy_channel})
		</if>
		<if test="spread_name != null and spread_name != ''">
			and spread_name = #{spread_name}
		</if>
		<if test="campaign_id != null and campaign_id != ''">
			and campaign_id = #{campaign_id}
		</if>
		<choose>
			<when test="group != null and group != ''">
				GROUP by ${group},user_type
			</when>
			<otherwise>
				GROUP BY day,app_id,ad_buy_media,account,ad_buy_channel,ad_type,spread_name,campaign_id,user_type
			</otherwise>
		</choose>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by user_number desc
			</otherwise>
		</choose>)a
	</select>

	<select id="selectKeepUserEcpm" resultType="java.util.Map">
		select  `day`,app_id,game_name,ad_buy_media,account,ad_buy_channel,ad_type,user_type,spread_name,campaign_id,campaign_name
		,user_number
		,ecpm_avg_number
		,median_ecpm
		,concat(0_9ECPM,'%') 0_9ECPM
		,concat(10_19ECPM,'%') 10_19ECPM
		,concat(20_29ECPM,'%') 20_29ECPM
		,concat(30_39ECPM,'%') 30_39ECPM
		,concat(40_49ECPM,'%') 40_49ECPM
		,concat(50_59ECPM,'%') 50_59ECPM
		,concat(60_69ECPM,'%') 60_69ECPM
		,concat(70_79ECPM,'%') 70_79ECPM
		,concat(80_89ECPM,'%') 80_89ECPM
		,concat(90_99ECPM,'%') 90_99ECPM
		,concat(100_119ECPM,'%') 100_119ECPM
		,concat(120_139ECPM,'%') 120_139ECPM
		,concat(140_159ECPM,'%') 140_159ECPM
		,concat(160_179ECPM,'%') 160_179ECPM
		,concat(180_199ECPM,'%') 180_199ECPM
		,concat(200_249ECPM,'%') 200_249ECPM
		,concat(250_299ECPM,'%') 250_299ECPM
		,concat(300_399ECPM,'%') 300_399ECPM
		,concat(400_599ECPM,'%') 400_599ECPM
		,concat(600_799ECPM,'%') 600_799ECPM
		,concat(800_999ECPM,'%') 800_999ECPM
		,concat(1000ECPM_more,'%') 1000ECPM_more
		from
		(select `day`,app_id,app_name game_name,ad_buy_media,account,ad_buy_channel,ad_type,user_type,spread_name,campaign_id,campaign_name
		,sum(user_number) user_number
		,round(sum(sum_ecpm)/sum(user_number),2) ecpm_avg_number
		,median_ecpm
		,round(sum(ECPM_0_9)/sum(user_number)*100,2) 0_9ECPM
		,round(sum(ECPM_10_19)/sum(user_number)*100,2) 10_19ECPM
		,round(sum(ECPM_20_29)/sum(user_number)*100,2) 20_29ECPM
		,round(sum(ECPM_30_39)/sum(user_number)*100,2) 30_39ECPM
		,round(sum(ECPM_40_49)/sum(user_number)*100,2) 40_49ECPM
		,round(sum(ECPM_50_59)/sum(user_number)*100,2) 50_59ECPM
		,round(sum(ECPM_60_69)/sum(user_number)*100,2) 60_69ECPM
		,round(sum(ECPM_70_79)/sum(user_number)*100,2) 70_79ECPM
		,round(sum(ECPM_80_89)/sum(user_number)*100,2) 80_89ECPM
		,round(sum(ECPM_90_99)/sum(user_number)*100,2) 90_99ECPM
		,round(sum(ECPM_100_119)/sum(user_number)*100,2) 100_119ECPM
		,round(sum(ECPM_120_139)/sum(user_number)*100,2) 120_139ECPM
		,round(sum(ECPM_140_159)/sum(user_number)*100,2) 140_159ECPM
		,round(sum(ECPM_160_179)/sum(user_number)*100,2) 160_179ECPM
		,round(sum(ECPM_180_199)/sum(user_number)*100,2) 180_199ECPM
		,round(sum(ECPM_200_249)/sum(user_number)*100,2) 200_249ECPM
		,round(sum(ECPM_250_299)/sum(user_number)*100,2) 250_299ECPM
		,round(sum(ECPM_300_399)/sum(user_number)*100,2) 300_399ECPM
		,round(sum(ECPM_400_599)/sum(user_number)*100,2) 400_599ECPM
		,round(sum(ECPM_600_799)/sum(user_number)*100,2) 600_799ECPM
		,round(sum(ECPM_800_999)/sum(user_number)*100,2) 800_999ECPM
		,round(sum(ECPM_1000_more)/sum(user_number)*100,2) 1000ECPM_more
		FROM dnwx_bi.ads_retention_user_ecpm_daily a
		left join dnwx_bi.dim_app_game b on a.app_id = b.appid
		where `day` between #{startTime} and #{endTime} and app_id in (${appid}) and ad_type in (${ad_type})
		<if test="account != null and account != ''">
			and account = #{account}
		</if>
		<if test="ad_buy_channel != null and ad_buy_channel != '' ">
			and ad_buy_channel in (${ad_buy_channel})
		</if>
		<if test="spread_name != null and spread_name != ''">
			and spread_name = #{spread_name}
		</if>
		<if test="campaign_id != null and campaign_id != ''">
			and campaign_id = #{campaign_id}
		</if>
		<choose>
			<when test="group != null and group != ''">
				group by ${group},user_type
			</when>
			<otherwise>
				GROUP BY day,app_id,ad_buy_media,account,ad_buy_channel,ad_type,spread_name,campaign_id,user_type
			</otherwise>
		</choose>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by user_number desc
			</otherwise>
		</choose>)a
	</select>

	<select id="selectMarketModelTotal" resultType="com.wbgame.pojo.operate.MarketModelTotalVo">
		select tb_date,appid,app_name,pid,app_ver,download_channel,new_user,act_user
		,concat(add_rate,'%') add_rate
		,concat(app_start_show_rate,'%') app_start_show_rate
		,concat(app_startflow_agreement_show_rate,'%') app_startflow_agreement_show_rate
		,concat(app_startflow_agreement_agree_click_rate,'%') app_startflow_agreement_agree_click_rate
		,concat(app_startflow_permission_show_rate,'%') app_startflow_permission_show_rate
		,concat(app_startflow_permission_over_rate,'%') app_startflow_permission_over_rate
		,concat(app_startflow_wallpaper_show_rate,'%') app_startflow_wallpaper_show_rate
		,concat(app_startflow_wallpaper_success_rate,'%') app_startflow_wallpaper_success_rate
		,concat(app_startflow_splash_uv_rate,'%') app_startflow_splash_uv_rate
		,concat(app_home_show_rate,'%') app_home_show_rate
		,concat(alive_time_0_rate,'%') alive_time_0_rate
		,concat(alive_time_5_rate,'%') alive_time_5_rate
		,concat(alive_time_15_rate,'%') alive_time_15_rate
		,concat(alive_time_30_rate,'%') alive_time_30_rate
		,concat(alive_time_60_rate,'%') alive_time_60_rate
		,concat(alive_time_120_rate,'%') alive_time_120_rate
		,concat(alive_time_360_rate,'%') alive_time_360_rate
		,concat(lock_screen_showed_uv_rate,'%') lock_screen_showed_uv_rate
		,concat(lock_news_ad_show_uv_rate,'%') lock_news_ad_show_uv_rate
		,concat(lock_success_rate,'%') lock_success_rate
		,lock_screen_showed_pv_rate
		,lock_news_content_click_pv_rate
		,concat(function_show_rate,'%') function_show_rate
		,concat(use_click_rate,'%') use_click_rate
		,concat(animation_close_rate,'%') animation_close_rate
		,concat(if_ad_1_pv_rate,'%') if_ad_1_pv_rate
		,concat(ad_show_pv_rate,'%') ad_show_pv_rate
		,concat(result_show_pv_rate,'%') result_show_pv_rate
		,concat(flow3_exit_pv_rate,'%') flow3_exit_pv_rate
		,concat(flow12_exit_pv_rate,'%') flow12_exit_pv_rate
		,concat(force_success_pv_rate,'%') force_success_pv_rate
		from (select tb_date,appid,app_name,pid,app_ver,download_channel
		,sum(new_user) new_user,sum(act_user) act_user,ifnull(round(sum(new_user)/sum(act_user)*100,2),0) add_rate
		,ifnull(round(sum(app_start_show_uv)/sum(new_user)*100,2),0) app_start_show_rate
		,ifnull(round(sum(app_startflow_agreement_show_uv)/sum(new_user)*100,2),0) app_startflow_agreement_show_rate
		,ifnull(round(sum(app_startflow_agreement_agree_click_uv)/sum(new_user)*100,2),0) app_startflow_agreement_agree_click_rate
		,ifnull(round(sum(app_startflow_permission_show_uv)/sum(new_user)*100,2),0) app_startflow_permission_show_rate
		,ifnull(round(sum(app_startflow_permission_over_uv)/sum(new_user)*100,2),0) app_startflow_permission_over_rate
		,ifnull(round(sum(app_startflow_wallpaper_show_uv)/sum(new_user)*100,2),0) app_startflow_wallpaper_show_rate
		,ifnull(round(sum(app_startflow_wallpaper_success_uv)/sum(new_user)*100,2),0) app_startflow_wallpaper_success_rate
		,ifnull(round(sum(app_startflow_splash_uv)/sum(new_user)*100,2),0) app_startflow_splash_uv_rate
		,ifnull(round(sum(app_home_show_uv)/sum(new_user)*100,2),0) app_home_show_rate
		,ifnull(round(sum(alive_time_0_uv)/sum(new_user)*100,2),0) alive_time_0_rate
		,ifnull(round(sum(alive_time_5_uv)/sum(new_user)*100,2),0) alive_time_5_rate
		,ifnull(round(sum(alive_time_15_uv)/sum(new_user)*100,2),0) alive_time_15_rate
		,ifnull(round(sum(alive_time_30_uv)/sum(new_user)*100,2),0) alive_time_30_rate
		,ifnull(round(sum(alive_time_60_uv)/sum(new_user)*100,2),0) alive_time_60_rate
		,ifnull(round(sum(alive_time_120_uv)/sum(new_user)*100,2),0) alive_time_120_rate
		,ifnull(round(sum(alive_time_360_uv)/sum(new_user)*100,2),0) alive_time_360_rate
		,ifnull(round(sum(B_lock_screen_showed_uv)/sum(act_user)*100,2),0) lock_screen_showed_uv_rate
		,ifnull(round(sum(B_lock_news_ad_show_uv)/sum(act_user)*100,2),0) lock_news_ad_show_uv_rate
		,ifnull(round(sum(B_lock_news_ad_show_pv)/sum(B_lock_screen_showed_pv)*100,2),0) lock_success_rate
		,ifnull(round(sum(B_lock_screen_showed_pv)/sum(act_user),2),0) lock_screen_showed_pv_rate
		,ifnull(round(sum(B_lock_news_content_click_pv)/sum(act_user),2),0) lock_news_content_click_pv_rate
		,ifnull(round(sum(function_show_pv)/sum(page_creating_pv)*100,2),0) function_show_rate
		,ifnull(round(sum(use_click_pv)/sum(function_show_pv)*100,2),0) use_click_rate
		,ifnull(round(sum(animation_close_pv)/sum(function_animation_pv)*100,2),0) animation_close_rate
		,ifnull(round(sum(if_ad_1_pv)/sum(function_show_pv)*100,2),0) if_ad_1_pv_rate
		,ifnull(round(sum(ad_show_pv)/sum(if_ad_1_pv)*100,2),0) ad_show_pv_rate
		,ifnull(round(sum(result_show_pv)/sum(function_show_pv)*100,2),0) result_show_pv_rate
		,ifnull(round(sum(flow3_exit_pv)/sum(ad_show_pv)*100,2),0) flow3_exit_pv_rate
		,ifnull(round(sum((flow1_exit_pv+flow2_exit_pv))/sum(function_show_pv)*100,2),0) flow12_exit_pv_rate
		,ifnull(round(sum(force_success_pv)/sum(flow1_exit_pv+flow2_exit_pv)*100,2),0) force_success_pv_rate
		from ads_tool_marketing_module_daily
		where tb_date between #{startTime} and #{endTime}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and download_channel in (${channel})
		</if>
		<if test="version != null and version != ''">
			and app_ver in (${version})
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<choose>
			<when test="group != null and group != ''">
				GROUP by ${group}
			</when>
			<otherwise>
				GROUP BY tb_date,appid,download_channel,app_ver,pid
			</otherwise>
		</choose>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by new_user desc
			</otherwise>
		</choose>
		) a

	</select>

	<select id="selectAppModelSysKeep" resultType="com.wbgame.pojo.mobile.AppModelSysKeepVo">
        select tdate,appid,app_name,download_channel,app_ver,brand,model,os_ver,reg_user_cnt,active_user_cnt
		,concat(add_rate,'%') add_rate
		,concat(keep1,'%') keep1
		,concat(keep2,'%') keep2
		,concat(keep3,'%') keep3
		,concat(keep4,'%') keep4
		,concat(keep5,'%') keep5
		,concat(keep6,'%') keep6
		,concat(keep7,'%') keep7
		,concat(keep14,'%') keep14
		,concat(keep30,'%') keep30
		,concat(keep60,'%') keep60
		from (select tdate,appid,app_name,download_channel,app_ver,brand,model,os_ver
		,sum(reg_user_cnt) reg_user_cnt,sum(active_user_cnt) active_user_cnt
		,ifnull(round(sum(reg_user_cnt)/sum(active_user_cnt)*100,2),0) add_rate
		,ifnull(round(sum(reg_retention_1day_user_cnt)/sum(reg_user_cnt)*100,2),0) keep1
		,ifnull(round(sum(reg_retention_2day_user_cnt)/sum(reg_user_cnt)*100,2),0) keep2
		,ifnull(round(sum(reg_retention_3day_user_cnt)/sum(reg_user_cnt)*100,2),0) keep3
		,ifnull(round(sum(reg_retention_4day_user_cnt)/sum(reg_user_cnt)*100,2),0) keep4
		,ifnull(round(sum(reg_retention_5day_user_cnt)/sum(reg_user_cnt)*100,2),0) keep5
		,ifnull(round(sum(reg_retention_6day_user_cnt)/sum(reg_user_cnt)*100,2),0) keep6
		,ifnull(round(sum(reg_retention_7day_user_cnt)/sum(reg_user_cnt)*100,2),0) keep7
		,ifnull(round(sum(reg_retention_14day_user_cnt)/sum(reg_user_cnt)*100,2),0) keep14
		,ifnull(round(sum(reg_retention_30day_user_cnt)/sum(reg_user_cnt)*100,2),0) keep30
		,ifnull(round(sum(reg_retention_60day_user_cnt)/sum(reg_user_cnt),2)*100,0) keep60
		from ads_device_brand_model_os_reg_retention_daily
		where tdate between #{startTime} and #{endTime}
		<if test="appid != null and appid != ''">
			and appid = #{appid}
		</if>
		<if test="channel != null and channel != ''">
			and download_channel = #{channel}
		</if>
		<if test="version != null and version != ''">
			and app_ver = #{version}
		</if>
		<if test="brand != null and brand != ''">
			and brand = #{brand}
		</if>
		<if test="model != null and model != ''">
			and model = #{model}
		</if>
		<if test="os_ver != null and os_ver != ''">
			and os_ver = #{os_ver}
		</if>
		<choose>
			<when test="group != null and group != ''">
				GROUP by ${group}
			</when>
			<otherwise>
				GROUP BY tdate,appid,app_name,download_channel,app_ver,brand,model,os_ver
			</otherwise>
		</choose>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by tdate asc,reg_user_cnt desc
			</otherwise>
		</choose>
		) a

	</select>

	<select id="countAppModelSysKeep" resultType="com.wbgame.pojo.mobile.AppModelSysKeepVo">
        select sum(reg_user_cnt) reg_user_cnt,sum(active_user_cnt) active_user_cnt
        from ads_device_brand_model_os_reg_retention_daily
		where tdate between #{startTime} and #{endTime}
		<if test="appid != null and appid != ''">
			and appid = #{appid}
		</if>
		<if test="channel != null and channel != ''">
			and download_channel = #{channel}
		</if>
		<if test="version != null and version != ''">
			and app_ver = #{version}
		</if>
		<if test="brand != null and brand != ''">
			and brand = #{brand}
		</if>
		<if test="model != null and model != ''">
			and model = #{model}
		</if>
		<if test="os_ver != null and os_ver != ''">
			and os_ver = #{os_ver}
		</if>
	</select>

	<select id="selectKeepOsVers" resultType="java.lang.String">
		select os_ver from ads_device_brand_model_os_reg_retention_daily
		where tdate between #{startTime} and #{endTime}
		<if test="appid != null and appid != ''">
			and appid = #{appid}
		</if>
		<if test="channel != null and channel != ''">
			and download_channel = #{channel}
		</if>
		<if test="version != null and version != ''">
			and app_ver = #{version}
		</if>
		<if test="brand != null and brand != ''">
			and brand = #{brand}
		</if>
		<if test="model != null and model != ''">
			and model = #{model}
		</if>
		group by os_ver
		order by cast(os_ver as signed) asc
	</select>

	<select id="selectNewGuides" resultType="com.alibaba.fastjson.JSONObject">
		select t_date,a.appid,b.app app_name,pid,download_channel,if(is_new=1,'新用户','老用户') is_new
		${sql}
		from ads_user_tutorial_daily a
		left join dim_app_game b on a.appid = b.appid
		where t_date between #{param.startTime} and #{param.endTime}
		and a.appid in (${param.appid})
		<if test="param.channel != null and param.channel != ''">
			and download_channel in (${param.channel})
		</if>
		<if test="param.pid != null and param.pid != ''">
			and pid = #{param.pid}
		</if>
		<if test="param.is_new != null and param.is_new != ''">
			and is_new = #{param.is_new}
		</if>
		<choose>
			<when test="param.group != null and param.group != ''">
				GROUP by ${param.group}
			</when>
			<otherwise>
				GROUP BY t_date,a.appid,download_channel,pid,is_new
			</otherwise>
		</choose>
		<choose>
			<when test="param.order != null and param.order != ''">
				order by ${param.order}
			</when>
			<otherwise>
				order by t_date desc
			</otherwise>
		</choose>
	</select>

	<select id="countNewGuides" resultType="com.alibaba.fastjson.JSONObject">
		select appid ${sql}
		from ads_user_tutorial_daily
		where t_date between #{param.startTime} and #{param.endTime}
		and appid in (${param.appid})
		<if test="param.channel != null and param.channel != ''">
			and download_channel in (${param.channel})
		</if>
		<if test="param.pid != null and param.pid != ''">
			and pid = #{param.pid}
		</if>
		<if test="param.is_new != null and param.is_new != ''">
			and is_new = #{param.is_new}
		</if>
	</select>

	<select id="selectNewGuidesView" resultType="com.alibaba.fastjson.JSONObject">
		select appid ${sql}
		from ads_user_tutorial_daily
		where t_date between #{param.startTime} and #{param.endTime}
		and appid in (${param.appid})
		<if test="param.channel != null and param.channel != ''">
			and download_channel in (${param.channel})
		</if>
		<if test="param.pid != null and param.pid != ''">
			and pid = #{param.pid}
		</if>
		<if test="param.is_new != null and param.is_new != ''">
			and is_new = #{param.is_new}
		</if>
	</select>

	<select id="selectOutConvertFunnel" resultType="com.wbgame.pojo.operate.OutConvertFunnelVo">
        select ds,app,channel,ver,event_name,new_users,act_users,concat(add_rate,'%') add_rate
		,concat(pull_rate,'%') pull_rate
		,dau_pull_avg_num
		,concat(pull_seep_rate,'%') pull_seep_rate
		,concat(pull_success_rate,'%') pull_success_rate
		,dau_pull_seep_avg_num
		,concat(pull_user_click_seep_rate,'%') pull_user_click_seep_rate
		,concat(pull_user_click_convert_rate,'%') pull_user_click_convert_rate
		,dau_pull_avg_click_num
		,concat(pull_close_click_seep_rate,'%') pull_close_click_seep_rate
		,concat(pull_close_convert_rate,'%') pull_close_convert_rate
		,dau_pull_close_avg_num
		,concat(acrtoon_show_seep_rate,'%') acrtoon_show_seep_rate
		,concat(acrtoon_show_success_rate,'%') acrtoon_show_success_rate
		,dau_acrtoon_show_avg_num
		,concat(acrtoon_over_seep_rate,'%') acrtoon_over_seep_rate
		,concat(acrtoon_over_success_rate,'%') acrtoon_over_success_rate
		,dau_acrtoon_over_avg_num
		,concat(page_before_advert_load_success_rate,'%') page_before_advert_load_success_rate
		,dau_page_before_advert_load_success_avg_num
		,concat(page_before_advert_show_seep_rate,'%') page_before_advert_show_seep_rate
		,concat(page_before_advert_show_success_rate,'%') page_before_advert_show_success_rate
		,dau_page_before_advert_show_success_avg_num
		,concat(page_show_seep_rate,'%') page_show_seep_rate
		,concat(page_show_success_rate,'%') page_show_success_rate
		,dau_page_show_avg_num
		,concat(force_pull_seep_rate,'%') force_pull_seep_rate
		,dau_force_pull_avg_num
		,concat(force_pull_advert_show_success_rate,'%') force_pull_advert_show_success_rate
		,dau_force_pull_success_avg_num
		,concat(force_pull_home_seep_rate,'%') force_pull_home_seep_rate
		,concat(force_pull_home_convert_rate,'%') force_pull_home_convert_rate
		,dau_force_pull_home_avg_num from
		(select ds,
		b.app,
		channel,
		ver,
		sum(new_users) new_users,
		sum(act_users) act_users,
		ifnull(round(sum(new_users)/sum(act_users)*100,2),0) add_rate,
		case event_name when 'scenes_home' then 'home'
		when 'scenes_wifi' then 'wifi'
		when 'scenes_battery' then '电量'
		when 'scenes_charge' then '充电'
		when 'scenes_news' then '锁屏新闻'
		when 'scenes_unlock' then '解锁弹窗'
		when 'scenes_ta' then '定时弹窗'
		when 'scenes_install' then '安装'
		when 'scenes_uninstall' then '卸载'
		when 'scenes_um' then '屏幕使用提醒'
		end as event_name,
		ifnull(round((sum(弹窗人数)/sum(act_users)*100),2),0) pull_rate,
		ifnull(round(sum(弹窗次数)/sum(act_users),2),0) dau_pull_avg_num,
		ifnull(round((sum(弹窗成功人数)/sum(act_users)*100),2),0) pull_seep_rate,
		ifnull(round((sum(弹窗成功次数)/sum(弹窗次数)*100),2),0) pull_success_rate,
		ifnull(round(sum(弹窗成功次数)/sum(act_users),2),0) dau_pull_seep_avg_num,
		ifnull(round((sum(按钮点击成功人数)/sum(act_users)*100),2),0) pull_user_click_seep_rate,
		ifnull(round((sum(按钮点击成功次数)/sum(弹窗成功次数)*100),2),0) pull_user_click_convert_rate,
		ifnull(round(sum(按钮点击成功次数)/sum(act_users),2),0) dau_pull_avg_click_num,
		ifnull(round((sum(按钮点击关闭人数)/sum(act_users)*100),2),0) pull_close_click_seep_rate,
		ifnull(round((sum(按钮点击关闭次数)/sum(弹窗成功次数)*100),2),0) pull_close_convert_rate,
		ifnull(round(sum(按钮点击关闭次数)/sum(act_users),2),0) dau_pull_close_avg_num,
		ifnull(round((sum(动画展示人数)/sum(act_users)*100),2),0) acrtoon_show_seep_rate,
		ifnull(round((sum(动画展示次数)/sum(弹窗成功次数)*100),2),0) acrtoon_show_success_rate,
		ifnull(round(sum(动画展示次数)/sum(act_users),2),0) dau_acrtoon_show_avg_num,
		ifnull(round((sum(动画展示结束人数)/sum(act_users)*100),2),0) acrtoon_over_seep_rate,
		ifnull(round((sum(动画展示结束次数)/sum(弹窗成功次数)*100),2),0) acrtoon_over_success_rate,
		ifnull(round(sum(动画展示结束次数)/sum(act_users),2),0) dau_acrtoon_over_avg_num,
		ifnull(round((sum(落地页前广告加载成功次数)/sum(弹窗成功次数)*100),2),0) page_before_advert_load_success_rate,
		ifnull(round(sum(落地页前广告加载成功次数)/sum(act_users),2),0) dau_page_before_advert_load_success_avg_num,
		ifnull(round((sum(落地页前广告展示人数)/sum(act_users)*100),2),0) page_before_advert_show_seep_rate,
		ifnull(round((sum(落地页前广告展示次数)/sum(弹窗成功次数)*100),2),0) page_before_advert_show_success_rate,
		ifnull(round(sum(落地页前广告展示次数)/sum(act_users),2),0) dau_page_before_advert_show_success_avg_num,
		ifnull(round((sum(落地页展示人数)/sum(act_users)*100),2),0) page_show_seep_rate,
		ifnull(round((sum(落地页展示次数)/sum(弹窗成功次数)*100),2),0) page_show_success_rate,
		ifnull(round(sum(落地页展示次数)/sum(act_users),2),0) dau_page_show_avg_num,
		ifnull(round((sum(有广告缓存并开始强弹人数)/sum(act_users)*100),2),0) force_pull_seep_rate,
		ifnull(round(sum(有广告缓存并开始强弹次数)/sum(act_users),2),0) dau_force_pull_avg_num,
		ifnull(round((sum(强弹广告展示次数)/sum(弹窗成功次数)*100),2),0) force_pull_advert_show_success_rate,
		ifnull(round(sum(强弹广告展示次数)/sum(act_users),2),0) dau_force_pull_success_avg_num,
		ifnull(round((sum(强弹后home人数)/sum(act_users)*100),2),0) force_pull_home_seep_rate,
		ifnull(round((sum(强弹后home次数)/sum(强弹广告展示次数)*100),2),0) force_pull_home_convert_rate,
		ifnull(round(sum(强弹后home次数)/sum(act_users),2),0) dau_force_pull_home_avg_num
		from ads_tool_outside_app_ad_transform_daily a left join dim_app_game b on a.appid = b.appid
		where ds between #{startTime} and #{endTime}
				<if test="appid != null and appid != ''">
					and a.appid in (${appid})
			    </if>
				<if test="channel != null and channel != ''">
					and channel in (${channel})
				</if>
				<if test="version != null and version != ''">
					and ver = #{version}
				</if>
				<if test="event_name != null and event_name != ''">
					and event_name in (${event_name})
				</if>
				<choose>
					<when test="group != null and group != ''">
						GROUP by ${group}
					</when>
					<otherwise>
						GROUP BY ds,a.appid,channel,ver,event_name
					</otherwise>
				</choose>
				<choose>
					<when test="order != null and order != ''">
						order by ${order}
					</when>
					<otherwise>
						order by ds asc,new_users desc
					</otherwise>
				</choose>) a
	</select>

	<select id="countOutConvertFunnel" resultType="com.wbgame.pojo.operate.OutConvertFunnelVo">
           select
		sum(new_users) new_users,
		sum(act_users) act_users,
		ifnull(round(sum(弹窗次数)/sum(act_users),2),0) dau_pull_avg_num,
		ifnull(round(sum(弹窗成功次数)/sum(act_users),2),0) dau_pull_seep_avg_num,
		ifnull(round(sum(按钮点击成功次数)/sum(act_users),2),0) dau_pull_avg_click_num,
		ifnull(round(sum(按钮点击关闭次数)/sum(act_users),2),0) dau_pull_close_avg_num,
		ifnull(round(sum(动画展示次数)/sum(act_users),2),0) dau_acrtoon_show_avg_num,
		ifnull(round(sum(动画展示结束次数)/sum(act_users),2),0) dau_acrtoon_over_avg_num,
		ifnull(round(sum(落地页前广告加载成功次数)/sum(act_users),2),0) dau_page_before_advert_load_success_avg_num,
		ifnull(round(sum(落地页前广告展示次数)/sum(act_users),2),0) dau_page_before_advert_show_success_avg_num,
		ifnull(round(sum(落地页展示次数)/sum(act_users),2),0) dau_page_show_avg_num,
		ifnull(round(sum(有广告缓存并开始强弹次数)/sum(act_users),2),0) dau_force_pull_avg_num,
		ifnull(round(sum(强弹广告展示次数)/sum(act_users),2),0) dau_force_pull_success_avg_num,
		ifnull(round(sum(强弹后home次数)/sum(act_users),2),0) dau_force_pull_home_avg_num
		from ads_tool_outside_app_ad_transform_daily
		where ds between #{startTime} and #{endTime}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel})
		</if>
		<if test="version != null and version != ''">
			and ver = #{version}
		</if>
		<if test="event_name != null and event_name != ''">
			and event_name in (${event_name})
		</if>
	</select>

    <select id="selectActivityTake" resultType="com.wbgame.pojo.operate.ActivityTakeVo">
        select data_date,appid,app_name,pid,download_channel,activity_id
		,activity_qualified_sum
		,game_activity_sum
		,concat(activity_sum_rate,'%') activity_sum_rate
		,activity_qualified_new
		,game_activity_new
		,concat(activity_new_rate,'%') activity_new_rate
		,activity_qualified_old
		,game_activity_old
		,concat(activity_old_rate,'%') activity_old_rate
		,game_awards_one,game_awards_three,game_awards_five,game_awards_seven,game_awards_nine
        from (select data_date,a.appid,app_name,pid,download_channel,activity_id
        ,sum(activity_qualified_new)+sum(activity_qualified_old) activity_qualified_sum
        ,sum(game_activity_new)+sum(game_activity_old) game_activity_sum
        ,round((sum(game_activity_new)+sum(game_activity_old))/(sum(activity_qualified_new)+sum(activity_qualified_old))*100,2) activity_sum_rate
        ,sum(activity_qualified_new) activity_qualified_new
        ,sum(game_activity_new) game_activity_new
        ,round(sum(game_activity_new)/sum(activity_qualified_new)*100,2) activity_new_rate
        ,sum(activity_qualified_old) activity_qualified_old
        ,sum(game_activity_old) game_activity_old
        ,round(sum(game_activity_old)/sum(activity_qualified_old)*100,2) activity_old_rate
        ,sum(game_awards_one_new)+sum(game_awards_one_old) game_awards_one
        ,sum(game_awards_three_new)+sum(game_awards_three_old) game_awards_three
        ,sum(game_awards_five_new)+sum(game_awards_five_old) game_awards_five
        ,sum(game_awards_seven_new)+sum(game_awards_seven_old) game_awards_seven
        ,sum(game_awards_nine_new)+sum(game_awards_nine_old) game_awards_nine
        from ads_activity_join_daily a left join dim_app_game b on a.appid = b.appid
        where data_date between #{startTime} and #{endTime}
		<if test="appid != null and appid != ''">
			and a.appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and download_channel in (${channel})
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="activity_id != null and activity_id != ''">
			and activity_id = #{activity_id}
		</if>
		<choose>
			<when test="group != null and group != ''">
				GROUP by ${group}
			</when>
			<otherwise>
				GROUP BY data_date,a.appid,pid,download_channel,activity_id
			</otherwise>
		</choose>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by data_date asc,activity_qualified_sum desc
			</otherwise>
		</choose>) a
	</select>

	<select id="countActivityTake" resultType="com.wbgame.pojo.operate.ActivityTakeVo">
		select sum(activity_qualified_new)+sum(activity_qualified_old) activity_qualified_sum
		,sum(game_activity_new)+sum(game_activity_old) game_activity_sum
		,sum(activity_qualified_new) activity_qualified_new
		,sum(game_activity_new) game_activity_new
		,sum(activity_qualified_old) activity_qualified_old
		,sum(game_activity_old) game_activity_old
		,sum(game_awards_one_new)+sum(game_awards_one_old) game_awards_one
		,sum(game_awards_three_new)+sum(game_awards_three_old) game_awards_three
		,sum(game_awards_five_new)+sum(game_awards_five_old) game_awards_five
		,sum(game_awards_seven_new)+sum(game_awards_seven_old) game_awards_seven
		,sum(game_awards_nine_new)+sum(game_awards_nine_old) game_awards_nine
		from ads_activity_join_daily
		where data_date between #{startTime} and #{endTime}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and download_channel in (${channel})
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="activity_id != null and activity_id != ''">
			and activity_id = #{activity_id}
		</if>
	</select>

	<select id="selectChannelTotalReport2" resultType="com.alibaba.fastjson.JSONObject">
		select a.day,if(a.app = '',10001,a.app) app,ifnull(channel1,'') channel1,sum(a.spend) spend,sum(register) register,sum(a.installs) installs,sum(payRevenue1) payRevenue1,sum(payRevenue) payRevenue
		,sum(payRevenue7) payRevenue7,sum(revenue1) revenue1,sum(revenue7) revenue7,SUM(addPayCount) addPayCount,pay_num_24,pay_money_24,ifnull(sum(a.ticket),0) AS ticket
		,CASE channel1
        WHEN 'oppo' THEN SUM(CASE WHEN adsenseType = '商店' THEN payRevenue ELSE 0 END)*(1-0.05)*0.5+SUM(CASE WHEN adsenseType = '联盟' THEN payRevenue ELSE 0 END)*(1-0.05)*0.9
		+SUM(CASE WHEN adsenseType = '非商店' THEN payRevenue ELSE 0 END)*(1-0.05)*0.9
        WHEN 'vivo' THEN SUM(CASE WHEN adsenseType = '商店' THEN payRevenue ELSE 0 END)*(1-0.05)*0.5+SUM(CASE WHEN adsenseType = '联盟' THEN payRevenue ELSE 0 END)*(1-0.05)*0.9
		+SUM(CASE WHEN adsenseType = '非商店' THEN payRevenue ELSE 0 END)*(1-0.05)*0.9
		WHEN 'honor' THEN SUM(CASE WHEN adsenseType = '商店' THEN payRevenue ELSE 0 END)*(1-0.02)*0.5+SUM(CASE WHEN adsenseType = '联盟' THEN payRevenue ELSE 0 END)*(1-0.02)*0.9
        WHEN 'xiaomi' THEN sum(payRevenue)*(1-0.05)*0.9
        WHEN 'oppoml' THEN sum(payRevenue)*(1-0.05)*0.9
        WHEN 'oppomj' THEN sum(payRevenue)*(1-0.05)*0.9
        WHEN 'vivoml' THEN sum(payRevenue)*(1-0.05)*0.9
        WHEN 'xiaomimj' THEN sum(payRevenue)*(1-0.05)*0.7
        WHEN 'huawei' THEN sum(payRevenue)*(1-0.05)*0.5
        ELSE sum(payRevenue)
        END pay_revenue_share_after
		,sum(ifnull(case when adsenseType = '商店' then a.spend else 0 end,0)) shop_spend
		,sum(ifnull(case when adsenseType = '商店' then a.impressions else 0 end,0)) shop_impressions
		,sum(ifnull(case when adsenseType = '商店' then a.clicks else 0 end,0)) shop_clicks
		,sum(ifnull(case when adsenseType = '商店' then a.download else 0 end,0)) shop_download
		,sum(ifnull(case when adsenseType = '商店' then a.installs else 0 end,0)) shop_installs
		,sum(ifnull(case when adsenseType = '商店' then a.register else 0 end,0)) shop_register
		,sum(ifnull(case when adsenseType = '商店' then a.gamePayCount else 0 end,0)) shop_gamePayCount
		,sum(ifnull(case when adsenseType = '商店' then a.payRevenue else 0 end,0)) shop_payRevenue
		,sum(ifnull(case when adsenseType = '商店' then a.payRevenue1 else 0 end,0)) shop_payRevenue1
		,sum(ifnull(case when adsenseType = '商店' then a.payRevenue3 else 0 end,0)) shop_payRevenue3
		,sum(ifnull(case when adsenseType = '商店' then a.payRevenue7 else 0 end,0)) shop_payRevenue7
		,sum(ifnull(case when adsenseType = '商店' then a.payRevenue30 else 0 end,0)) shop_payRevenue30
		,sum(ifnull(case when adsenseType = '商店' then a.ticket else 0 end,0)) shop_ticket
		,sum(ifnull(case when ifnull(adsenseType,'') != '商店' then a.spend else 0 end,0)) other_spend
		,sum(ifnull(case when ifnull(adsenseType,'') != '商店' then a.impressions else 0 end,0)) other_impressions
		,sum(ifnull(case when ifnull(adsenseType,'') != '商店' then a.clicks else 0 end,0)) other_clicks
		,sum(ifnull(case when ifnull(adsenseType,'') != '商店' then a.download else 0 end,0)) other_download
		,sum(ifnull(case when ifnull(adsenseType,'') != '商店' then a.installs else 0 end,0)) other_installs
		,sum(ifnull(case when ifnull(adsenseType,'') != '商店' then a.register else 0 end,0)) other_register
		,sum(ifnull(case when ifnull(adsenseType,'') != '商店' then a.gamePayCount else 0 end,0)) other_gamePayCount
		,sum(ifnull(case when ifnull(adsenseType,'') != '商店' then a.payRevenue else 0 end,0)) other_payRevenue
		,sum(ifnull(case when ifnull(adsenseType,'') != '商店' then a.payRevenue1 else 0 end,0)) other_payRevenue1
		,sum(ifnull(case when ifnull(adsenseType,'') != '商店' then a.payRevenue3 else 0 end,0)) other_payRevenue3
		,sum(ifnull(case when ifnull(adsenseType,'') != '商店' then a.payRevenue7 else 0 end,0)) other_payRevenue7
		,sum(ifnull(case when ifnull(adsenseType,'') != '商店' then a.payRevenue30 else 0 end,0)) other_payRevenue30
		,sum(ifnull(case when ifnull(adsenseType,'') != '商店' then a.ticket else 0 end,0)) other_ticket
		from dnwx_adt.dn_report_spend_china a
		left join
		(select day,app,channel,sum(gamePuH24) pay_num_24,sum(gamePfH24) pay_money_24 from
		dnwx_adt.dn_xiaomi_creative_report b where day between #{startTime} and #{endTime}
		<if test="channel != null and channel != ''">
			and channel = #{channel}
		</if>
		GROUP BY day,app,channel) b
		on a.day=b.day and a.app = b.app and a.channel1 = b.channel
		where a.day between #{startTime} and #{endTime} and a.app is not null
		<if test="channel != null and channel != ''">
			and channel1 = #{channel}
		</if>
		group by a.day,a.app,channel1
	</select>

	<select id="selectAppNewGuideIds" resultType="com.wbgame.pojo.operate.NewProcessNoteVo">
		select id,id name,1 as order
		from ads_user_tutorial_daily
		where t_date between #{startTime} and #{endTime}
		and appid in (${appid})
    	GROUP BY id
	</select>

	<select id="selectChannelTotalReport3" resultType="com.alibaba.fastjson.JSONObject">
		select dnappid,cha_id,date,sum(pv) total_advert_show
		,sum(case when open_type = 'video' then revenue end) video_income
		,sum(case when open_type = 'video' then pv end) video_show
		,ifnull(sum(revenue),0) sub_revenue
		from dn_cha_cash_total
		where date between #{startTime} and #{endTime} and dnappid is not null
		and cha_id in ('oppo','oppo2','oppomj','oppomj2','oppoml','vivo','vivo2','vivoml','vivoml2','huawei','huawei2','huaweiml','huaweiml2','xiaomi','xiaomimj','xiaomiml','xiaomiwt')
		<if test="channel != null and channel != ''">
			and cha_id = #{channel}
		</if>
		group by dnappid,cha_id,date
	</select>

	<select id="selectChannelTotalReport4" resultType="com.alibaba.fastjson.JSONObject">
		select tdate,appid,download_channel channel,sum(iap_revenue_add_user_cnt) pay_reg_num
		from ads_user_iap_revenue_info_daily
		where tdate between #{startTime} and #{endTime} and download_channel != 'wechat'
		<if test="channel != null and channel != ''">
			and download_channel = #{channel}
		</if>
		GROUP BY tdate,appid,download_channel
		HAVING sum(iap_revenue_add_user_cnt) > 0
	</select>

	<select id="selectxycAdAct1" resultType="com.alibaba.fastjson.JSONObject">
		select tdate,appid,if(provider = 'wechat','h5_wechat',provider) cname,sum(active_user_cnt) actnum,sum(reg_user_cnt) addnum,sum(pay_reg_user_cnt) pay_reg_num
		,ifnull(convert(sum(reg_retention_1_day)/sum(reg_user_cnt)*100,DECIMAL(10,2)),0) keep_num1
		from ads_wechat_user_cnt_daily
		where tdate between #{startTime} and #{endTime} and provider != 'NA'
		<if test="channel != null and channel != ''">
			and provider = #{channel}
		</if>
		GROUP BY tdate,appid,provider
	</select>

	<select id="selectxycAdAct2" resultType="com.alibaba.fastjson.JSONObject">
		select ifnull(dnappid,10001) appid,date tdate,ifnull(cha_id,'') cname,ifnull(sum(revenue),0) sub_revenue
		from dn_cha_cash_total a left join app_info b on a.dnappid = b.id
		where date between #{startTime} and #{endTime} and bus_category = 2
		<if test="channel != null and channel != ''">
			and cha_id = #{channel}
		</if>
		GROUP BY dnappid,date,cha_id
	</select>

	<select id="selectxycAdAct3" resultType="com.alibaba.fastjson.JSONObject">
		select ifnull(appid,10001) appid,date(createtime) tdate,ifnull(chaid,'') cname,convert(sum(money/100),DECIMAL(10,2)) pay_total
		from adb_wb_pay_info a left join app_info b on a.appid = b.id
		where orderstatus = 'SUCCESS' and date(createtime) between #{startTime} and #{endTime} and
		(bus_category = 2 or chaid not in ('oppo','oppomj','oppoml','vivo','vivoml','xiaomi','xiaomiml','xiaomimj','huawei','huaweiml'))
		<if test="channel != null and channel != ''">
			and chaid = #{channel}
		</if>
		group by appid,date(createtime),chaid
	</select>

	<select id="selectChannelTotalReport6" resultType="com.alibaba.fastjson.JSONObject">
		select appid dnappid,tdate date,cha_id,sum(sum_revenue) sub_revenue,sum(pv_video) video_show,sum(revenue_video) video_income
		,sum(ifnull(pv_splash,0)+ifnull(pv_plaque,0)+ifnull(pv_banner,0)+ifnull(pv_video,0)+ifnull(pv_msg,0)) total_advert_show
		from ads_dn_extend_revise_adtype_daily
		where tdate between #{startTime} and #{endTime} and appid is not null and cha_id is not null
		and cha_id not in ('oppo','oppo2','oppomj','oppomj2','oppoml','vivo','vivo2','vivoml','vivoml2','huawei','huawei2','huaweiml','huaweiml2','xiaomi','xiaomimj','xiaomiml','xiaomiwt')
		<if test="channel != null and channel != ''">
			and cha_id = #{channel}
		</if>
		group by appid,cha_id,tdate
	</select>

	<select id="selectChannelTotalReport12" resultType="com.alibaba.fastjson.JSONObject">
		select ifnull(appid,10001) appid,date(createtime) tdate,ifnull(chaid,'') cname,count(DISTINCT imei) pay_num
		from adb_wb_pay_info
		where orderstatus = 'SUCCESS' and date(createtime) between #{startTime} and #{endTime}
		<if test="channel != null and channel != ''">
			and chaid = #{channel}
		</if>
		group by appid,date(createtime),chaid
	</select>

	<select id="selectOldArpu" resultType="com.alibaba.fastjson.JSONObject">
		select tdate,
			   appid,
			   cast(sum(old_user_iap_revenue)/sum(old_user_cnt)/100 as decimal(18, 2)) old_arpu
		from ads_user_iap_revenue_info_daily
		where tdate between #{startTime} and #{endTime}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		group by tdate,appid order by tdate asc
	</select>

</mapper>
