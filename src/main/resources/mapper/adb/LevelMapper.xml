<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adb.LevelMapper">

    <select id="getUserLevelLoseList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.UserLevelLoseVo">
        select data_date,appid,pid,download_channel,level_id,sum(user_count_new) user_count_new,
        sum(user_count_old) user_count_old,sum(loss_user_count_new) loss_user_count_new,
        sum(loss_user_count_old) loss_user_count_old,(sum(loss_user_count_new)+sum(loss_user_count_old)) lose_count,

        CONCAT(ROUND(sum(loss_user_count_new)/((sum(user_count_new)))*100, 2),'%') lose_level_ratio_new,
        CONCAT(ROUND(sum(loss_user_count_old)/((sum(user_count_old)))*100, 2),'%') lose_level_ratio_old
        FROM dnwx_bi.ads_user_loss_daily where 1=1
        and DATE(data_date) <![CDATA[>=]]> #{start_date} and DATE(data_date) <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid =#{pid}
        </if>
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
        <if test="level_id != null and level_id != ''">
            and level_id=#{level_id}
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by data_date asc,user_count_new desc
            </otherwise>
        </choose>
    </select>

    <select id="getUserLevelLoseSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.UserLevelLoseVo">
        select sum(user_count_new) user_count_new,
        sum(user_count_old) user_count_old,sum(loss_user_count_new) loss_user_count_new,
        sum(loss_user_count_old) loss_user_count_old,(sum(loss_user_count_new)+sum(loss_user_count_old)) lose_count,

        CONCAT(ROUND(sum(loss_user_count_new)/((sum(user_count_new)))*100, 2),'%') lose_level_ratio_new,
        CONCAT(ROUND(sum(loss_user_count_old)/((sum(user_count_old)))*100, 2),'%') lose_level_ratio_old
        FROM dnwx_bi.ads_user_loss_daily where 1=1
        and DATE(data_date) <![CDATA[>=]]> #{start_date} and DATE(data_date) <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid =#{pid}
        </if>
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
        <if test="level_id != null and level_id != ''">
            and level_id=#{level_id}
        </if>
    </select>

</mapper>