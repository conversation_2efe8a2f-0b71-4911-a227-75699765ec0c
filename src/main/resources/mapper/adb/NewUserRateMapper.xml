<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.NewUserRateMapper">


    <select id="queryList" resultType="com.wbgame.pojo.adv2.bigdata.NewUserRateVo">
        SELECT
            <if test="group != null and group != ''">
                ${group},
                <if test="group.contains('appid')">
                    app_name,
                </if>
                <if test="group.contains('buy_act')">
                    account_remark,
                </if>
            </if>
            sum(accept_agreement_show_user_cnt) accept_agreement_show_user_cnt,
            sum(accept_agreement_pass_user_cnt) accept_agreement_pass_user_cnt,
            CAST(sum(accept_agreement_pass_user_cnt)/sum(accept_agreement_show_user_cnt) * 100 AS DECIMAL(10,2)) pass_rate,
            sum(ad_cnt) ad_cnt,
            sum(in_ad_cnt) in_ad_cnt,
            CAST(sum(in_ad_cnt)/sum(ad_cnt) * 100 AS DECIMAL(10,2)) in_ad_rate,
            CAST(sum(ad_cnt)/sum(accept_agreement_pass_user_cnt) AS DECIMAL(10,2)) pass_ad_pv,
            CAST(sum(in_ad_cnt)/sum(accept_agreement_pass_user_cnt) AS DECIMAL(10,2)) pass_in_ad_pv,
            CAST((sum(ad_cnt)-sum(in_ad_cnt))/sum(accept_agreement_pass_user_cnt) AS DECIMAL(10,2)) pass_out_ad_pv,
            CAST((sum(video_revenue)/sum(video_cnt))/100*1000 AS DECIMAL(10,2)) video_ecpm,
            CAST((sum(plaque_revenue)/sum(plaque_cnt))/100*1000 AS DECIMAL(10,2)) plaque_ecpm,
            CAST((sum(splash_revenue)/sum(splash_cnt))/100*1000 AS DECIMAL(10,2)) splash_ecpm,
            CAST(sum(spend)/100 AS DECIMAL(10,2)) spend,
            sum(reg_users) reg_users,
            CAST(sum(revenue_1)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_one,
            CAST(sum(revenue_2)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_two,
            CAST(sum(revenue_3)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_three,
            CAST(sum(revenue_4)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_four,
            CAST(sum(revenue_5)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_five,
            CAST(sum(revenue_6)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_six,
            CAST(sum(revenue_7)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_seven,
            CAST(sum(revenue_15)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_fifteen,
            CAST(sum(revenue_30)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_thirty,
            sum(wallpaper_show_user_cnt) wallpaper_show_user_cnt,
            sum(wallpaper_own_user_cnt) wallpaper_own_user_cnt,
            CAST(sum(wallpaper_own_user_cnt)/sum(wallpaper_show_user_cnt) * 100 AS DECIMAL(10,2)) wallpaper_own_user_rate
        FROM `ads_tool_channel_reg_user_approval_rate_ad_info_daily`
        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                tdate between #{start_date} and #{end_date}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="pid != null and pid != ''">
                and pid like concat('%',#{pid},'%')
            </if>
            <if test="buy_id != null and buy_id != ''">
                and buy_id in (${buy_id})
            </if>
            <if test="buy_act != null and buy_act != ''">
                and buy_act like concat('%',#{buy_act},'%')
            </if>
            <if test="accounts != null and accounts != ''">
                and FIND_IN_SET(buy_act,#{accounts})
            </if>
        </where>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>


    <select id="queryTotal" resultType="com.wbgame.pojo.adv2.bigdata.NewUserRateVo">
        SELECT
            sum(accept_agreement_show_user_cnt) accept_agreement_show_user_cnt,
            sum(accept_agreement_pass_user_cnt) accept_agreement_pass_user_cnt,
            CAST(sum(accept_agreement_pass_user_cnt)/sum(accept_agreement_show_user_cnt) * 100 AS DECIMAL(10,2)) pass_rate,
            sum(ad_cnt) ad_cnt,
            sum(in_ad_cnt) in_ad_cnt,
            CAST(sum(in_ad_cnt)/sum(ad_cnt) * 100 AS DECIMAL(10,2)) in_ad_rate,
            CAST(sum(ad_cnt)/sum(accept_agreement_pass_user_cnt) AS DECIMAL(10,2)) pass_ad_pv,
            CAST(sum(in_ad_cnt)/sum(accept_agreement_pass_user_cnt) AS DECIMAL(10,2)) pass_in_ad_pv,
            CAST((sum(ad_cnt)-sum(in_ad_cnt))/sum(accept_agreement_pass_user_cnt) AS DECIMAL(10,2)) pass_out_ad_pv,
            CAST((sum(video_revenue)/sum(video_cnt))/100*1000 AS DECIMAL(10,2)) video_ecpm,
            CAST((sum(plaque_revenue)/sum(plaque_cnt))/100*1000 AS DECIMAL(10,2)) plaque_ecpm,
            CAST((sum(splash_revenue)/sum(splash_cnt))/100*1000 AS DECIMAL(10,2)) splash_ecpm,
            CAST(sum(spend)/100 AS DECIMAL(10,2)) spend,
            sum(reg_users) reg_users,
            CAST(sum(revenue_1)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_one,
            CAST(sum(revenue_2)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_two,
            CAST(sum(revenue_3)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_three,
            CAST(sum(revenue_4)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_four,
            CAST(sum(revenue_5)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_five,
            CAST(sum(revenue_6)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_six,
            CAST(sum(revenue_7)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_seven,
            CAST(sum(revenue_15)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_fifteen,
            CAST(sum(revenue_30)/sum(spend) * 100 AS DECIMAL(10,2)) revenue_thirty,
            sum(wallpaper_show_user_cnt) wallpaper_show_user_cnt,
            sum(wallpaper_own_user_cnt) wallpaper_own_user_cnt,
            CAST(sum(wallpaper_own_user_cnt)/sum(wallpaper_show_user_cnt) * 100 AS DECIMAL(10,2)) wallpaper_own_user_rate
        FROM `ads_tool_channel_reg_user_approval_rate_ad_info_daily`
        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                tdate between #{start_date} and #{end_date}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="pid != null and pid != ''">
                and pid like concat('%',#{pid},'%')
            </if>
            <if test="buy_id != null and buy_id != ''">
                and buy_id in (${buy_id})
            </if>
            <if test="buy_act != null and buy_act != ''">
                and buy_act like concat('%',#{buy_act},'%')
            </if>
            <if test="accounts != null and accounts != ''">
                and FIND_IN_SET(buy_act,#{accounts})
            </if>
        </where>
    </select>
</mapper>