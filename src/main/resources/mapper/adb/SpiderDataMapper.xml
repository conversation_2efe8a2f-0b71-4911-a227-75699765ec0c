<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.SpiderDataMapper">

    <select id="selectSpiderTaskList" resultType="com.wbgame.pojo.jettison.report.SpiderTaskVo">
        select task_id,task_name,day,start_time,end_time,status,message,num,create_user,task_time,start_day,end_day,ad_platform from dnwx_adt.dn_spider_task_log
        where 1=1
        <if test="task_name != null and task_name != ''">
            and task_name in (${task_name})
        </if>
        <if test="ad_platform != null and ad_platform != ''">
            and ad_platform in (${ad_platform})
        </if>
        <if test="create_user != null and create_user != ''">
            and create_user like concat('%',#{create_user},'%')
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="start_day != null and start_day != ''">
            and day between #{start_day} and #{end_day}
        </if>
        <if test="start_date != null and start_date != ''">
            and start_time between concat(#{start_date},' 00:00:00') and concat(#{end_date},' 23:59:59')
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                ORDER BY ${order_str}
            </when>
            <otherwise>
                ORDER BY start_time desc
            </otherwise>
        </choose>
    </select>

    <select id="selectSpiderDataList" resultType="com.wbgame.pojo.jettison.report.SpiderDataVo">
        select account,day,start_date,end_date,status,msg,num,spider_time,save_time,ad_platform,create_time,msg from dnwx_adt.dn_spider_data_log
        where 1=1
        <if test="account != null and account != ''">
            and account in (${account})
        </if>
        <if test="ad_platform != null and ad_platform != ''">
            and ad_platform in (${ad_platform})
        </if>
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        <if test="start_day != null and start_day != ''">
            and day between #{start_day} and #{end_day}
        </if>
        <if test="start_date != null and start_date != ''">
            and create_time between concat(#{start_date},' 00:00:00') and concat(#{end_date},' 23:59:59')
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                ORDER BY ${order_str}
            </when>
            <otherwise>
                ORDER BY start_date desc
            </otherwise>
        </choose>
    </select>

</mapper>