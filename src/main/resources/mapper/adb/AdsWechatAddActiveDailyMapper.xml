<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsWechatAddActiveDailyMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.AdsWechatAddActiveDailyVO">
        <result column="tdate" property="tdate" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
        <result column="provider" property="provider" jdbcType="VARCHAR"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>
        <result column="user_uv" property="userUv" jdbcType="INTEGER"/>
        <result column="activate_add" property="activateAdd" jdbcType="INTEGER"/>
        <result column="active_user" property="activeUser" jdbcType="INTEGER"/>
        <result column="startup_pv" property="startupPv" jdbcType="INTEGER"/>
        <result column="active_time" property="activeTime" jdbcType="INTEGER"/>
        <result column="per_capita_start" property="perCapitaStart" jdbcType="INTEGER"/>
        <result column="per_capita_active" property="perCapitaActive" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        tdate
        , appid, app_name, provider, channel, version, user_uv, activate_add, active_user,
    startup_pv, active_time
    </sql>

    <select id="selectAddedActivityMiniGames" parameterType="com.wbgame.pojo.AdsWechatAddActiveDailyDTO"
            resultMap="BaseResultMap">

        SELECT
            <if test="group != null and group != ''">
                ${group},
            </if>
            sum(user_uv) user_uv,
            sum( active_user ) active_user,
            sum( activate_add ) activate_add,
            round( sum( startup_pv ) / sum( active_user ), 2 ) per_capita_start,
            round( sum( active_time ) / sum( active_user ) / 60, 2 ) per_capita_active
        FROM
            (SELECT
                 tdate,
                 appid,
                 app_name,
                 provider,
                 ( CASE channel WHEN '-1' THEN '自然量' ELSE channel END ) channel,
                 version,
                 user_uv,
                 active_user,
                 activate_add,
                 startup_pv,
                 active_time
            FROM
                ads_wechat_add_active_daily
            <where>
                <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                    tdate BETWEEN #{start_date}
                    AND #{end_date}
                </if>

                 <if test="version != null and version != ''">
                     AND version like #{version} "%"
                 </if>

                <if test="providerList != null and providerList.size > 0">
                    AND provider IN
                    <foreach collection="providerList" item="provider" open="(" separator="," close=")">
                        #{provider}
                    </foreach>
                </if>

                <if test="channelList != null and channelList.size > 0">
                    AND channel IN
                    <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                        #{channel}
                    </foreach>
                </if>

                <if test="appidList != null and appidList.size > 0">
                    AND appid IN
                    <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                        #{appid}
                    </foreach>
                </if>

             </where>

            ) tmp

        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate, appid desc
            </otherwise>
        </choose>
    </select>

</mapper>