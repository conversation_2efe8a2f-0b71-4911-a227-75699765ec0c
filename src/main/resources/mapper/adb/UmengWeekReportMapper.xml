<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adb.TfUmengWeekReportMapper">

    <select id="getUmengWeekReportRevenueDataList" parameterType="java.util.Map" resultType="com.wbgame.pojo.advert.UmChWeekReportVo">
        select
        app appid,
        channel,
        IFNULL(ROUND(sum(spend),2),0) cost ,
        IFNULL(sum(buy_installs),0) buyAddNum,
        IFNULL(ROUND(SUM(buy_revenue),2) ,0) buyEarn,
        IFNULL(ROUND(SUM(spend)/sum(buy_installs),2),0) buyCost,
        IFNULL(ROUND(SUM(buy_revenue)/sum(spend),2),0) buyRoi
        from dnwx_adt.dn_report_operation_summary
        where 1=1 and `day` BETWEEN #{date1} AND #{date2}
        <if test="appid != null and appid != ''">
            and app in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        group by app,channel
    </select>


</mapper>