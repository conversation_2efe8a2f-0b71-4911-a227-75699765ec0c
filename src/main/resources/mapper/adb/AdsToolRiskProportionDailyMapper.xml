<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsToolRiskProportionDailyMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.AdsToolRiskProportionDailyVO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="tdate" property="tdate" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
        <result column="media" property="media" jdbcType="VARCHAR"/>
        <result column="download_channel" property="downloadChannel" jdbcType="VARCHAR"/>
        <result column="pid" property="pid" jdbcType="VARCHAR"/>
        <result column="risk_type" property="riskType" jdbcType="VARCHAR"/>
        <result column="value_section" property="valueSection" jdbcType="VARCHAR"/>
        <result column="user_type" property="userType" jdbcType="VARCHAR"/>
        <result column="total_user_cnt" property="totalUserCnt" jdbcType="BIGINT"/>
        <result column="user_cnt" property="userCnt" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , tdate, appid, app_name, media, download_channel, pid, risk_type, value_section,
    user_type, total_user_cnt, user_cnt
    </sql>
    <select id="selectRiskProportionDaily" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.clean.AdsToolRiskProportionDailyDTO">
        SELECT tdate, appid, app_name, user_type,

        <if test="group != null and group != ''">
            ${group},
        </if>

        sum(total_user_cnt) total_user_cnt,
        sum(user_cnt) user_cnt,
        truncate(ifnull(sum(user_cnt) / sum(total_user_cnt), 0) * 100, 2) proportion
        FROM ads_tool_risk_proportion_daily

        <where>

            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and tdate between #{start_date} and #{end_date}
            </if>


            <if test="pid != null and pid != ''">
                and pid = #{pid}
            </if>
            <if test="riskType != null and riskType != ''">
                and risk_type = #{riskType}
            </if>

            <if test="userType != null and userType != ''">
                and user_type = #{userType}
            </if>

            <if test="valueSection != null and valueSection != ''">
                and value_section = #{valueSection}
            </if>

            <if test="mediaList != null and mediaList.size > 0">
                AND media IN
                <foreach collection="mediaList" item="m" open="(" separator="," close=")">
                    #{m}
                </foreach>
            </if>
            <if test="channelList != null and channelList.size > 0">
                AND download_channel IN
                <foreach collection="channelList" item="cha" open="(" separator="," close=")">
                    #{cha}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

        </where>

        group by tdate, appid, app_name, user_type
        <if test="group != null and group != ''">
            ,${group}
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate, appid
            </otherwise>
        </choose>
    </select>


    <select id="countRiskProportionDaily" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.clean.AdsToolRiskProportionDailyDTO">
            select
                sum(total_user_cnt) total_user_cnt,
                sum(user_cnt) user_cnt
            from (

                SELECT tdate, appid, app_name, user_type,

                <if test="group != null and group != ''">
                    ${group},
                </if>
                sum(total_user_cnt) total_user_cnt,
                sum(user_cnt) user_cnt
                FROM ads_tool_risk_proportion_daily

                <where>
                    <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                        and tdate between #{start_date} and #{end_date}
                    </if>

                    <if test="media != null and media != ''">
                        and media = #{media}
                    </if>

                    <if test="pid != null and pid != ''">
                        and pid = #{pid}
                    </if>
                    <if test="riskType != null and riskType != ''">
                        and risk_type = #{riskType}
                    </if>

                    <if test="userType != null and userType != ''">
                        and user_type = #{userType}
                    </if>

                    <if test="channelList != null and channelList.size > 0">
                        AND download_channel IN
                        <foreach collection="channelList" item="cha" open="(" separator="," close=")">
                            #{cha}
                        </foreach>
                    </if>

                    <if test="appidList != null and appidList.size > 0">
                        AND appid IN
                        <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                            #{appid}
                        </foreach>
                    </if>

                </where>

                group by tdate, appid, app_name, user_type
                <if test="group != null and group != ''">
                    ,${group}
                </if>

                          ) a
    </select>
</mapper>