<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsEcpmDimensionDailyMapper" >
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.AdsEcpmDimensionDailyVO" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="a_day" property="aDay" jdbcType="VARCHAR" />
    <result column="appid" property="appid" jdbcType="VARCHAR" />
    <result column="appname" property="appname" jdbcType="VARCHAR" />
    <result column="media" property="media" jdbcType="VARCHAR" />
    <result column="channel" property="channel" jdbcType="VARCHAR" />
    <result column="channeltype" property="channeltype" jdbcType="VARCHAR" />
    <result column="adsource_type" property="adsourceType" jdbcType="VARCHAR" />
    <result column="ad_type" property="adType" jdbcType="VARCHAR" />
<!--    <result column="avg_ecpm_appid" property="avgEcpmAppid" jdbcType="DECIMAL" />-->
<!--    <result column="avg_ecpm_appid_type" property="avgEcpmAppidType" jdbcType="DECIMAL" />-->
<!--    <result column="avg_ecpm_appid_type_media" property="avgEcpmAppidTypeMedia" jdbcType="DECIMAL" />-->
<!--    <result column="avg_ecpm_appid_type_media_channel" property="avgEcpmAppidTypeMediaChannel" jdbcType="DECIMAL" />-->
    <result column="revenue" property="revenue" jdbcType="DECIMAL" />
    <result column="pv" property="pv" jdbcType="BIGINT" />
    <result column="ECPM_0_10" property="ecpm010" jdbcType="BIGINT" />
    <result column="ECPM_10_20" property="ecpm1020" jdbcType="BIGINT" />
    <result column="ECPM_20_40" property="ecpm2040" jdbcType="BIGINT" />
    <result column="ECPM_40_70" property="ecpm4070" jdbcType="BIGINT" />
    <result column="ECPM_70_100" property="ecpm70100" jdbcType="BIGINT" />
    <result column="ECPM_100_150" property="ecpm100150" jdbcType="BIGINT" />
    <result column="ECPM_150_200" property="ecpm150200" jdbcType="BIGINT" />
    <result column="ECPM_200_300" property="ecpm200300" jdbcType="BIGINT" />
    <result column="ECPM_300_500" property="ecpm300500" jdbcType="BIGINT" />
    <result column="ECPM_500_800" property="ecpm500800" jdbcType="BIGINT" />
    <result column="ECPM_800_more" property="ecpm800More" jdbcType="BIGINT" />
    <result column="createTime" property="createtime" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, a_day, appid, appname, media, channel, channeltype, adsource_type, ad_type, avg_ecpm_appid, 
    avg_ecpm_appid_type, avg_ecpm_appid_type_media, avg_ecpm_appid_type_media_channel, 
    revenue, pv, ECPM_0_10, ECPM_10_20, ECPM_20_40, ECPM_40_70, ECPM_70_100, ECPM_100_150, 
    ECPM_150_200, ECPM_200_300, ECPM_300_500, ECPM_500_800, ECPM_800_more, createTime
  </sql>

  <select id="selectEcpmDimentsionByConditional" parameterType="com.wbgame.pojo.AdsEcpmDimensionDailyQuery"
          resultMap="BaseResultMap">

        select
               <if test="group != null and group != ''">
                   ${group},
               </if>

              sum(revenue) revenue,
              sum(pv) pv,
              CONCAT(round(sum(ECPM_0_10)/sum(pv)*100, 2),"%") ECPM_0_10,
              CONCAT(round(sum(ECPM_10_20)/sum(pv)*100, 2),"%") ECPM_10_20,
              CONCAT(round(sum(ECPM_20_40)/sum(pv)*100, 2),"%") ECPM_20_40,
              CONCAT(round(sum(ECPM_40_70)/sum(pv)*100, 2),"%") ECPM_40_70,
              CONCAT(round(sum(ECPM_70_100)/sum(pv)*100, 2),"%") ECPM_70_100,
              CONCAT(round(sum(ECPM_100_150)/sum(pv)*100, 2),"%") ECPM_100_150,
              CONCAT(round(sum(ECPM_150_200)/sum(pv)*100, 2),"%") ECPM_150_200,
              CONCAT(round(sum(ECPM_200_300)/sum(pv)*100, 2),"%") ECPM_200_300,
              CONCAT(round(sum(ECPM_300_500)/sum(pv)*100, 2),"%") ECPM_300_500,
              CONCAT(round(sum(ECPM_500_800)/sum(pv)*100, 2),"%") ECPM_500_800,
              CONCAT(round(sum(ECPM_800_more)/sum(pv)*100, 2),"%") ECPM_800_more

          <choose>
              <when test="type != null and type == 'appid'">
                  ,avg_ecpm_appid avgEcpm
              </when>
              <when test="type != null and type == 'type'">
                  ,avg_ecpm_appid_type avgEcpm
              </when>

              <when test="type != null and type == 'media'">
                  ,avg_ecpm_appid_type_media avgEcpm
              </when>

              <when test="type != null and type == 'channel'">
                  ,avg_ecpm_appid_type_media_channel avgEcpm
              </when>
              <otherwise>
              </otherwise>
          </choose>

        from ads_ecpm_dimension_daily
        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and a_day between #{start_date} and #{end_date}
            </if>


            <if test="appid != null and appid.size > 0">

                and appid in
                <foreach collection="appid" item="aid" open="(" separator="," close=")">
                    #{aid}
                </foreach>
            </if>

            <if test="media != null and media.size > 0">

                and media in
                <foreach collection="media" item="m" open="(" separator="," close=")">
                    #{m}
                </foreach>
            </if>

            <if test="channel != null and channel.size > 0">
                and channel in
                <foreach collection="channel" item="c" open="(" separator="," close=")">
                    #{c}
                </foreach>
            </if>

            <if test="channeltype != null and channeltype.size > 0">
                and channeltype in
                <foreach collection="channeltype" item="ct" open="(" separator="," close=")">
                    #{ct}
                </foreach>
            </if>

            <if test="adsourceType != null and adsourceType != ''">
                and adsource_type = #{adsourceType}

            </if>

            <if test="adType != null and adType.size > 0 ">
                and ad_type in
                <foreach collection="adType" item="at" open="(" separator="," close=")">
                    #{at}
                </foreach>
            </if>
        </where>

      <if test="group != null and group != ''">
          group by ${group}
      </if>

      <choose>
          <when test="order_str == null or order_str == ''">
              order by a_day desc
          </when>
          <otherwise>
            order by ${order_str}
          </otherwise>
      </choose>
  </select>
</mapper>