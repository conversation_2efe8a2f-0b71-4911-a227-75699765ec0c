<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adb.ADBOrderMapper">

    <insert id="insertSuccessPayOrders">
        replace into adb_wb_pay_info(orderid,uid,money,paytype,imei,pid,appid,orderstatus,payname,paynote
              ,createtime,param1,param2,param3,lsn,chaid,androidid,oaid,idfa,lsn_new,payid,userid,custom,buy_id,pn,model,is_new,zone_id)
              values
              (#{orderid},#{uid},#{money},#{paytype},#{imei},#{pid},#{appid},#{orderstatus},#{payname},#{paynote}
              ,#{createtime},#{param1},#{param2},#{param3},#{lsn},#{chaid},#{androidid},#{oaid},#{idfa},#{lsn_new},#{payid}
              ,#{userid},#{custom},#{buy_id},#{pn},#{model},#{is_new},#{zone_id})
    </insert>

    <select id="selectADBNewPayInfo" resultType="java.util.Map">
        select truncate(sum(money<if test="type.contains('inside')">/100</if>),2) as amount,
        count(distinct ifnull(imei,'')) as payNumber,count(*) as payCount,
        truncate(sum(money)<if test="type.contains('inside')">/100</if>/count(distinct ifnull(imei,'')),2) as arpu,
        date(createtime) creattime,tdate
        ,pid AS prjid,paytype AS payWay,a.appid,app_category_name appCategory,param2 mchid,zone_id
        from
        (select *,
        <choose>
            <when test="custom_date != null and custom_date != ''">
                concat(#{begin},'至',#{end}) as tdate
            </when>
            <when test="group != null and group.contains('tdate')">
                date(createtime) as tdate
            </when>
            <when test="group != null and group.contains('week')">
                DATE_FORMAT(createtime, '%x-%v') as tdate,
                DATE_FORMAT(createtime, '%x-%v') as week
            </when>
            <when test="group != null and group.contains('month')">
                DATE_FORMAT(createtime,'%Y-%m') as tdate,
                DATE_FORMAT(createtime,'%Y-%m') as `month`
            </when>
            <when test="group != null and group != '' and group.contains('beek')">
                CONCAT(DATE_FORMAT(createtime, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 1,CHAR),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 2,CHAR),2,"0"))  AS tdate,
                CONCAT(DATE_FORMAT(createtime, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 1,CHAR),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 2,CHAR),2,"0"))  AS beek
            </when>
            <otherwise>
                concat(#{begin},'至',#{end}) as tdate
            </otherwise>
        </choose>
        from adb_wb_pay_info) a
        left join dim_app_game b on a.appid = b.appid
        where createtime BETWEEN concat(#{begin},' 00:00:00') AND concat(#{end},' 23:59:59') and orderstatus = 'SUCCESS'
        <if test="app != null and app !=''">
            and a.appid in (${app})
        </if>
        <if test="prjId != null and prjId !=''">
            and pid = #{prjId}
        </if>
        <if test="zone_id != null and zone_id !=''">
            and zone_id = #{zone_id}
        </if>
        <if test="payWays != null and payWays !=''">
            and paytype in (${payWays})
        </if>
        <if test="appCategory != null and appCategory !=''">
            and app_category = #{appCategory}
        </if>
        <choose>
            <when test="type.contains('test')">
                and (oaid = 'test' or param3 = 'test')
            </when>
            <otherwise>
                and (oaid != 'test' or oaid is null) and (param3 != 'test' or param3 is null)
            </otherwise>
        </choose>
        group by ${group}
        order by ${order}
    </select>

    <select id="countADBNewPayInfo" resultType="java.util.Map">
        select
            truncate(sum(xx.amount),2) amount,
            sum(xx.payNumber) payNumber,
            sum(xx.payCount) payCount,
            truncate(sum(xx.amount)/sum(xx.payNumber), 2) arpu
        from

            (select truncate(sum(money<if test="type.contains('inside')">/100</if>),2) as amount,
            count(distinct ifnull(imei,'')) as payNumber,count(*) as payCount,
            truncate(sum(money)<if test="type.contains('inside')">/100</if>/count(distinct ifnull(imei,'')),2) as arpu,
            date(createtime) creattime
            ,pid AS prjid,paytype AS payWay,a.appid,app_category_name appCategory,param2 mchid,zone_id
            from
            (select *,
            <choose>
                <when test="custom_date != null and custom_date != ''">
                    concat(#{begin},'至',#{end}) as tdate
                </when>
                <when test="group != null and group.contains('tdate')">
                    date(createtime) as tdate
                </when>
                <when test="group != null and group.contains('week')">
                    DATE_FORMAT(createtime, '%x-%v') as tdate,
                    DATE_FORMAT(createtime, '%x-%v') as week
                </when>
                <when test="group != null and group.contains('month')">
                    DATE_FORMAT(createtime,'%Y-%m') as tdate,
                    DATE_FORMAT(createtime,'%Y-%m') as `month`
                </when>
                <when test="group != null and group != '' and group.contains('beek')">
                    CONCAT(DATE_FORMAT(createtime, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 1,CHAR),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 2,CHAR),2,"0"))  AS tdate,
                    CONCAT(DATE_FORMAT(createtime, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 1,CHAR),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 2,CHAR),2,"0"))  AS beek
                </when>
                <otherwise>
                    concat(#{begin},'至',#{end}) as tdate
                </otherwise>
            </choose>
            from adb_wb_pay_info) a
            left join dim_app_game b on a.appid = b.appid
            where createtime BETWEEN concat(#{begin},' 00:00:00') AND concat(#{end},' 23:59:59') and orderstatus = 'SUCCESS'
            <if test="app != null and app !=''">
                and a.appid in (${app})
            </if>
            <if test="prjId != null and prjId !=''">
                and pid = #{prjId}
            </if>
            <if test="zone_id != null and zone_id !=''">
                and zone_id = #{zone_id}
            </if>
            <if test="payWays != null and payWays !=''">
                and paytype in (${payWays})
            </if>
            <if test="appCategory != null and appCategory !=''">
                and app_category = #{appCategory}
            </if>
            <choose>
                <when test="type.contains('test')">
                    and (oaid = 'test' or param3 = 'test')
                </when>
                <otherwise>
                    and (oaid != 'test' or oaid is null) and (param3 != 'test' or param3 is null)
                </otherwise>
            </choose>
            group by ${group}
            order by ${order}) xx
    </select>

    <select id="selectADBLaunchPayInfo" resultType="java.util.Map">
        select truncate(sum(money/100),2) as amount,
        count(distinct ifnull(imei,'')) as payNumber,count(*) as payCount,
        truncate(sum(money)/100/count(distinct ifnull(imei,'')),2) as arpu,
        date(createtime) creattime,chaid,app
        ,pid AS prjid,paytype AS payWay,a.appid,app_category_name appCategory,param2 mchid,buy_id,if(buy_id='default' or buy_id = 'notMatch',buy_id,cha_media) cha_media
        from adb_wb_pay_info a
        left join dim_app_game b on a.appid = b.appid
        left join dn_channel_info c on a.buy_id = c.ry_cha
        where createtime BETWEEN concat(#{begin},' 00:00:00') AND concat(#{end},' 23:59:59') and orderstatus = 'SUCCESS'
        <if test="app != null and app !=''">
            and a.appid in (${app})
        </if>
        <if test="prjId != null and prjId !=''">
            and pid = #{prjId}
        </if>
        <if test="payWays != null and payWays !=''">
            and paytype = #{payWays}
        </if>
        <if test="appCategory != null and appCategory !=''">
            and app_category = #{appCategory}
        </if>
        <if test="cha_media != null and cha_media !=''">
            and cha_media in (${cha_media})
        </if>
        <if test="buy_id != null and buy_id !=''">
            and buy_id = #{buy_id}
        </if>
        <choose>
            <when test="type.contains('test')">
                and oaid = 'test'
            </when>
            <otherwise>
                and (oaid != 'test' or oaid is null)
            </otherwise>
        </choose>
        group by ${group}
        order by ${order}
    </select>

    <select id="countADBLaunchPayInfo" resultType="java.util.Map">
        select truncate(sum(money/100),2) as amount,
        count(distinct ifnull(imei,'')) as payNumber,count(*) as payCount,
        truncate(sum(money)/100/count(distinct ifnull(imei,'')),2) as arpu
        from adb_wb_pay_info a
        left join dim_app_game b on a.appid = b.appid
        left join dn_channel_info c on a.buy_id = c.ry_cha
        where createtime BETWEEN concat(#{begin},' 00:00:00') AND concat(#{end},' 23:59:59') and orderstatus = 'SUCCESS'
        <if test="app != null and app !=''">
            and a.appid in (${app})
        </if>
        <if test="prjId != null and prjId !=''">
            and pid = #{prjId}
        </if>
        <if test="payWays != null and payWays !=''">
            and paytype = #{payWays}
        </if>
        <if test="appCategory != null and appCategory !=''">
            and app_category = #{appCategory}
        </if>
        <if test="cha_media != null and cha_media !=''">
            and cha_media in (${cha_media})
        </if>
        <if test="buy_id != null and buy_id !=''">
            and buy_id = #{buy_id}
        </if>
        <choose>
            <when test="type.contains('test')">
                and oaid = 'test'
            </when>
            <otherwise>
                and (oaid != 'test' or oaid is null)
            </otherwise>
        </choose>
    </select>

    <insert id="batchInsertDnChannelInfoToADB">
        replace into dn_channel_info(cha_id,cha_type,cha_media,cha_sub_launch,cha_id_byte,cha_name_byte,cha_sub_byte,cha_sub_name_byte,cha_remark,cha_ratio,dn_bus_model_id,ry_cha) values
        <foreach collection="list" item="li" separator=",">
            (#{li.cha_id},#{li.cha_type},#{li.cha_media},#{li.cha_sub_launch},#{li.cha_id_byte},#{li.cha_name_byte},#{li.cha_sub_byte},#{li.cha_sub_name_byte},#{li.cha_remark},#{li.cha_ratio},#{li.dn_bus_model_id},#{li.ry_cha})
        </foreach>
    </insert>

</mapper>