<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.BigDataMapper">


	<select id="getHourPvMonitorList" resultType="com.wbgame.pojo.mobile.HourPvMonitorVo" parameterType="java.util.Map">
		select `日期` tdate,appid,`项目id` pid,`广告类型` ad_type,
		IFNULL(`大数据pv`,0) big_data_pv,IFNULL(`曹威自统计pv`,0) dn_pv,
		IFNULL(`曹威转发接口pv`,0) dn_forward_pv,IFNULL(`大数据_自统计pv差值率`,0) big_data_pv_rate,
		IFNULL(`大数据_转发pv差值率`,0) big_data_forward_pv_rate
		from dnwx_bi.ads_warnings_ad_pv_hourly where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="pid != null and pid != ''">
			and `项目id` = #{pid}
		</if>
		<if test="ad_type != null and ad_type != ''">
			and `广告类型` = #{ad_type}
		</if>
		and `日期` <![CDATA[>=]]> #{start_date} and `日期` <![CDATA[<=]]> #{end_date}
		order  by `日期` asc
	</select>

	<select id="getDayPvMonitorList" resultType="com.wbgame.pojo.mobile.DayPvMonitorVo" parameterType="java.util.Map">
		select `日期` tdate,appid,`广告类型` ad_type,
		IFNULL(`大数据pv`,0) big_data_pv,IFNULL(`曹威自统计pv`,0) dn_pv,
		IFNULL(`广告平台pv`,0) ad_platform_pv,IFNULL(`大数据_曹威自统计差值`,0) dn_pv_differential,
		IFNULL(`大数据_曹威自统计pv差值率`,0) dn_pv_rate,
		IFNULL(`大数据_广告平台pv差值`,0) ad_platform_pv_differebtial,
		IFNULL(`大数据_广告平台pv差值率`,0) ad_platform_pv_rate
		from dnwx_bi.ads_warnings_ad_allplatform_pv_daily where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="ad_type != null and ad_type != ''">
			and `广告类型` = #{ad_type}
		</if>
		and `日期` <![CDATA[>=]]> #{start_date} and `日期` <![CDATA[<=]]> #{end_date}
		order  by `日期` asc
	</select>

	<select id="getAdStyleDataList" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.AdStyleDataVo">
		<include refid="ad_style_sql"/>
	</select>

	<select id="getAdStyleDataListSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.AdStyleDataVo">
		select
		sum(xx.xdelay) xdelay,
		sum(xx.self_show_cnt) self_show_cnt,
		sum(xx.self_click_cnt) self_click_cnt,
		sum(xx.estimate_revenue) estimate_revenue,
		TRUNCATE(sum(xx.estimate_revenue)/sum(xx.self_show_cnt)*1000,2) ecpm,
		CONCAT(ROUND(sum(xx.self_click_cnt)/sum(xx.self_show_cnt)*100, 2),'%') self_click_ratio,
		ROUND(sum(xx.estimate_revenue)/sum(xx.self_click_cnt),2) cpc
		from (<include refid="ad_style_sql"/>) xx
	</select>

	<sql id="ad_style_sql">
		select appid,
		sum(platform_revenue)  platform_revenue,
		sum(platform_show_cnt) platform_show_cnt,
		sum(xdelay) xdelay,
		sum(self_show_cnt) self_show_cnt,
		sum(self_click_cnt) self_click_cnt,
		convert(sum(estimate_revenue),DECIMAL(10,2)) estimate_revenue,
		TRUNCATE(sum(estimate_revenue)/sum(self_show_cnt)*1000,2) ecpm,
		CONCAT(ROUND(sum(self_click_cnt)/sum(self_show_cnt)*100, 2),'%') self_click_ratio,
		ROUND(sum(estimate_revenue)/sum(self_click_cnt),2) cpc
		<if test="a_date_group != null and a_date_group != ''">
			,a_date
		</if>
		<if test="download_channel_group != null and download_channel_group != ''">
			,download_channel
		</if>
		<if test="channel_type_group != null and channel_type_group != ''">
			,channel_type
		</if>
		<if test="pid_group != null and pid_group != ''">
			,pid
		</if>
		<if test="ad_type_group != null and ad_type_group != ''">
			,ad_type
		</if>
		<if test="ad_sub_style_group != null and ad_sub_style_group != ''">
			,ad_sub_style
		</if>
		<if test="ad_sid_group != null and ad_sid_group != ''">
			,ad_sid,sdk_adtype
		</if>
		from dnwx_bi.ads_ad_style_daily where 1=1
		and a_date BETWEEN #{start_date} AND #{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="download_channel != null and download_channel != ''">
			and download_channel in (${download_channel})
		</if>
		<if test="channel_type != null and channel_type != ''">
			and channel_type in (${channel_type})
		</if>
		<if test="pid != null and pid != ''">
			and pid= #{pid}
		</if>
		<if test="ad_type != null and ad_type != ''">
			and ad_type = #{ad_type}
		</if>
		<if test="ad_sub_style != null and ad_sub_style != ''">
			and ad_sub_style = #{ad_sub_style}
		</if>
		<if test="ad_sid != null and ad_sid != ''">
			and ad_sid =#{ad_sid}
		</if>
		group by appid
		<if test="a_date_group != null and a_date_group != ''">
			,a_date
		</if>
		<if test="download_channel_group != null and download_channel_group != ''">
			,download_channel
		</if>
		<if test="channel_type_group != null and channel_type_group != ''">
			,channel_type
		</if>
		<if test="pid_group != null and pid_group != ''">
			,pid
		</if>
		<if test="ad_type_group != null and ad_type_group != ''">
			,ad_type
		</if>
		<if test="ad_sub_style_group != null and ad_sub_style_group != ''">
			,ad_sub_style
		</if>
		<if test="ad_sid_group != null and ad_sid_group != ''">
			,ad_sid,sdk_adtype
		</if>
		<if test="order_str != null and order_str != ''">
			order by ${order_str}
		</if>
	</sql>

	<select id="getMicGameRegAndDauData" parameterType="java.lang.String" resultType="com.wbgame.pojo.advert.MicGameIncomeVo">
		select tdate,appid,IFNULL(sum(reg_user_cnt),0) add_num,IFNULL(sum(active_user_cnt),0) dau,
		IFNULL(sum(pay_reg_user_cnt),0) pay_reg_user_num,IFNULL(sum(android_order_cnt),0) android_order_cnt,
		IFNULL(sum(ios_order_cnt),0) ios_order_cnt
		from  dnwx_bi.ads_wechat_user_cnt_daily
		where 1=1 and tdate = #{tdate} group  by tdate,appid
	</select>

	<select id="selectPayUserRetentionList" resultType="java.util.Map">
		SELECT '中国' as country,
		SUM(active_user_count) active_user_count,SUM(active_user_count_two_remain)/SUM(active_user_count) active_user_count_two_remain,SUM(active_user_count_three_remain)/SUM(active_user_count) active_user_count_three_remain,SUM(active_user_count_seven_remain)/SUM(active_user_count) active_user_count_seven_remain,
		SUM(active_user_count_fourteen_remain)/SUM(active_user_count) active_user_count_fourteen_remain,SUM(active_user_count_thirty_remain)/SUM(active_user_count) active_user_count_thirty_remain,SUM(active_user_count_sixty_remain)/SUM(active_user_count) active_user_count_sixty_remain,
		SUM(new_user_count) new_user_count,SUM(new_user_count_two_remain)/SUM(new_user_count) new_user_count_two_remain,SUM(new_user_count_three_remain)/SUM(new_user_count) new_user_count_three_remain,SUM(new_user_count_seven_remain)/SUM(new_user_count) new_user_count_seven_remain,
		SUM(new_user_count_fourteen_remain)/SUM(new_user_count) new_user_count_fourteen_remain,SUM(new_user_count_thirty_remain)/SUM(new_user_count) new_user_count_thirty_remain,SUM(new_user_count_sixty_remain)/SUM(new_user_count) new_user_count_sixty_remain,
		SUM(section_zero_user_count) section_zero_user_count,SUM(section_zero_two_remain)/SUM(section_zero_user_count) section_zero_two_remain,SUM(section_zero_three_remain)/SUM(section_zero_user_count) section_zero_three_remain,SUM(section_zero_seven_remain)/SUM(section_zero_user_count) section_zero_seven_remain,
		SUM(section_zero_fourteen_remain)/SUM(section_zero_user_count) section_zero_fourteen_remain,SUM(section_zero_thirty_remain)/SUM(section_zero_user_count) section_zero_thirty_remain,SUM(section_zero_sixty_remain)/SUM(section_zero_user_count) section_zero_sixty_remain,
		
		SUM(section_one_user_count) section_one_user_count,SUM(section_one_two_remain)/SUM(section_one_user_count) section_one_two_remain,SUM(section_one_three_remain)/SUM(section_one_user_count) section_one_three_remain,SUM(section_one_seven_remain)/SUM(section_one_user_count) section_one_seven_remain,
		SUM(section_one_fourteen_remain)/SUM(section_one_user_count) section_one_fourteen_remain,SUM(section_one_thirty_remain)/SUM(section_one_user_count) section_one_thirty_remain,SUM(section_one_sixty_remain)/SUM(section_one_user_count) section_one_sixty_remain,
		
		SUM(section_three_user_count) section_three_user_count,SUM(section_three_two_remain)/SUM(section_three_user_count) section_three_two_remain,SUM(section_three_three_remain)/SUM(section_three_user_count) section_three_three_remain,SUM(section_three_seven_remain)/SUM(section_three_user_count) section_three_seven_remain,
		SUM(section_three_fourteen_remain)/SUM(section_three_user_count) section_three_fourteen_remain,SUM(section_three_thirty_remain)/SUM(section_three_user_count) section_three_thirty_remain,SUM(section_three_sixty_remain)/SUM(section_three_user_count) section_three_sixty_remain,
		SUM(section_four_user_count) section_four_user_count,SUM(section_four_two_remain)/SUM(section_four_user_count) section_four_two_remain,SUM(section_four_three_remain)/SUM(section_four_user_count) section_four_three_remain,SUM(section_four_seven_remain)/SUM(section_four_user_count) section_four_seven_remain,
		SUM(section_four_fourteen_remain)/SUM(section_four_user_count) section_four_fourteen_remain,SUM(section_four_thirty_remain)/SUM(section_four_user_count) section_four_thirty_remain,SUM(section_four_sixty_remain)/SUM(section_four_user_count) section_four_sixty_remain,
		
		SUM(section_five_user_count) section_five_user_count,SUM(section_five_two_remain)/SUM(section_five_user_count) section_five_two_remain,SUM(section_five_three_remain)/SUM(section_five_user_count) section_five_three_remain,SUM(section_five_seven_remain)/SUM(section_five_user_count) section_five_seven_remain,
		SUM(section_five_fourteen_remain)/SUM(section_five_user_count) section_five_fourteen_remain,SUM(section_five_thirty_remain)/SUM(section_five_user_count) section_five_thirty_remain,SUM(section_five_sixty_remain)/SUM(section_five_user_count) section_five_sixty_remain,
		
		SUM(section_seven_user_count) section_seven_user_count,SUM(section_seven_two_remain)/SUM(section_seven_user_count) section_seven_two_remain,SUM(section_seven_three_remain)/SUM(section_seven_user_count) section_seven_three_remain,SUM(section_seven_seven_remain)/SUM(section_seven_user_count) section_seven_seven_remain,
		SUM(section_seven_fourteen_remain)/SUM(section_seven_user_count) section_seven_fourteen_remain,SUM(section_seven_thirty_remain)/SUM(section_seven_user_count) section_seven_thirty_remain,SUM(section_seven_sixty_remain)/SUM(section_seven_user_count) section_seven_sixty_remain,
		
		SUM(section_nine_user_count) section_nine_user_count,SUM(section_nine_two_remain)/SUM(section_nine_user_count) section_nine_two_remain,SUM(section_nine_three_remain)/SUM(section_nine_user_count) section_nine_three_remain,SUM(section_nine_seven_remain)/SUM(section_nine_user_count) section_nine_seven_remain,
		SUM(section_nine_fourteen_remain)/SUM(section_nine_user_count) section_nine_fourteen_remain,SUM(section_nine_thirty_remain)/SUM(section_nine_user_count) section_nine_thirty_remain,SUM(section_nine_sixty_remain)/SUM(section_nine_user_count) section_nine_sixty_remain,
		
		SUM(section_twelve_user_count) section_twelve_user_count,SUM(section_twelve_user_count_two_remain)/SUM(section_twelve_user_count) section_twelve_two_remain,SUM(section_twelve_user_count_three_remain)/SUM(section_twelve_user_count) section_twelve_three_remain,SUM(section_twelve_user_count_seven_remain)/SUM(section_twelve_user_count) section_twelve_seven_remain,
		SUM(section_twelve_user_count_fourteen_remain)/SUM(section_twelve_user_count) section_twelve_fourteen_remain,SUM(section_twelve_user_count_thirty_remain)/SUM(section_twelve_user_count) section_twelve_thirty_remain,SUM(section_twelve_user_count_sixty_remain)/SUM(section_twelve_user_count) section_twelve_sixty_remain,
		
		SUM(active_user_count)-SUM(section_zero_user_count) pay_user_count,(SUM(active_user_count_two_remain)-SUM(section_zero_two_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(section_zero_three_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(section_zero_seven_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(section_zero_fourteen_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(section_zero_thirty_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(section_zero_sixty_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_sixty_remain,
		SUM(active_user_count)-SUM(new_user_count) old_pay_user_count,(SUM(active_user_count_two_remain)-SUM(new_user_count_two_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(new_user_count_three_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(new_user_count_seven_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(new_user_count_fourteen_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(new_user_count_thirty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(new_user_count_sixty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_sixty_remain,
		IFNULL(SUM(first_pay_count),0) first_pay_count,SUM(first_pay_two_remain)/SUM(first_pay_count) first_pay_two_remain,SUM(first_pay_three_remain)/SUM(first_pay_count) first_pay_three_remain,SUM(first_pay_seven_remain)/SUM(first_pay_count) first_pay_seven_remain,SUM(first_pay_fourteen_remain)/SUM(first_pay_count) first_pay_fourteen_remain,
		SUM(first_pay_thirty_remain)/SUM(first_pay_count) first_pay_thirty_remain,SUM(first_pay_sixty_remain)/SUM(first_pay_count) first_pay_sixty_remain,
		IFNULL(SUM(new_first_pay_count),0) new_first_pay_count,SUM(new_first_pay_two_remain)/SUM(new_first_pay_count) new_first_pay_two_remain,SUM(new_first_pay_three_remain)/SUM(new_first_pay_count) new_first_pay_three_remain,SUM(new_first_pay_seven_remain)/SUM(new_first_pay_count) new_first_pay_seven_remain,SUM(new_first_pay_fourteen_remain)/SUM(new_first_pay_count) new_first_pay_fourteen_remain,
		SUM(new_first_pay_thirty_remain)/SUM(new_first_pay_count) new_first_pay_thirty_remain,SUM(new_first_pay_sixty_remain)/SUM(new_first_pay_count) new_first_pay_sixty_remain,
		sum(day0_retention_users)/sum(reg_users)  d0_retention_rate,sum(stage_one_valid_users)  stage_one_valid_users,sum(stage_one_retention_users)  stage_one_retention_users,
		sum(stage_two_retention_users)  stage_two_retention_users ,sum(stage_one_retention_users)/sum(stage_one_valid_users) rank_1_entry_ratio,
		sum(stage_two_retention_users)/sum(stage_one_retention_users) rank_2_entry_ratio
		<if test="group != null and group != ''">
			,${group}
		</if>
		FROM (
		SELECT create_date,appid,
		SUM(active_user_count) active_user_count,SUM(active_user_count_two_remain) active_user_count_two_remain,SUM(active_user_count_three_remain) active_user_count_three_remain,SUM(active_user_count_seven_remain) active_user_count_seven_remain,
		SUM(active_user_count_fourteen_remain) active_user_count_fourteen_remain,SUM(active_user_count_thirty_remain) active_user_count_thirty_remain,SUM(active_user_count_sixty_remain) active_user_count_sixty_remain,
		SUM(new_user_count) new_user_count,SUM(new_user_count_two_remain) new_user_count_two_remain,SUM(new_user_count_three_remain) new_user_count_three_remain,SUM(new_user_count_seven_remain) new_user_count_seven_remain,
		SUM(new_user_count_fourteen_remain) new_user_count_fourteen_remain,SUM(new_user_count_thirty_remain) new_user_count_thirty_remain,SUM(new_user_count_sixty_remain) new_user_count_sixty_remain,
		SUM(section_zero_user_count) section_zero_user_count,SUM(section_zero_two_remain) section_zero_two_remain,SUM(section_zero_three_remain) section_zero_three_remain,SUM(section_zero_seven_remain) section_zero_seven_remain,
		SUM(section_zero_fourteen_remain) section_zero_fourteen_remain,SUM(section_zero_thirty_remain) section_zero_thirty_remain,SUM(section_zero_sixty_remain) section_zero_sixty_remain,
		
		SUM(section_one_user_count+section_two_user_count) section_one_user_count,SUM(section_one_two_remain+section_two_two_remain) section_one_two_remain,
		SUM(section_one_three_remain+section_two_three_remain) section_one_three_remain,SUM(section_one_seven_remain+section_two_seven_remain) section_one_seven_remain,
		SUM(section_one_fourteen_remain+section_two_fourteen_remain) section_one_fourteen_remain,SUM(section_one_thirty_remain+section_two_thirty_remain) section_one_thirty_remain,
		SUM(section_one_sixty_remain+section_two_sixty_remain) section_one_sixty_remain,
		
		SUM(section_three_user_count) section_three_user_count,SUM(section_three_two_remain) section_three_two_remain,SUM(section_three_three_remain) section_three_three_remain,SUM(section_three_seven_remain) section_three_seven_remain,
		SUM(section_three_fourteen_remain) section_three_fourteen_remain,SUM(section_three_thirty_remain) section_three_thirty_remain,SUM(section_three_sixty_remain) section_three_sixty_remain,
		SUM(section_four_user_count) section_four_user_count,SUM(section_four_two_remain) section_four_two_remain,SUM(section_four_three_remain) section_four_three_remain,SUM(section_four_seven_remain) section_four_seven_remain,
		SUM(section_four_fourteen_remain) section_four_fourteen_remain,SUM(section_four_thirty_remain) section_four_thirty_remain,SUM(section_four_sixty_remain) section_four_sixty_remain,
		
		SUM(section_five_user_count+section_six_user_count) section_five_user_count,SUM(section_five_two_remain+section_six_two_remain) section_five_two_remain,
		SUM(section_five_three_remain+section_six_three_remain) section_five_three_remain,SUM(section_five_seven_remain+section_six_seven_remain) section_five_seven_remain,
		SUM(section_five_fourteen_remain+section_six_fourteen_remain) section_five_fourteen_remain,SUM(section_five_thirty_remain+section_six_thirty_remain) section_five_thirty_remain,
		SUM(section_five_sixty_remain+section_six_sixty_remain) section_five_sixty_remain,
		
		SUM(section_seven_user_count+section_eight_user_count) section_seven_user_count,SUM(section_seven_two_remain+section_eight_two_remain) section_seven_two_remain,
		SUM(section_seven_three_remain+section_eight_three_remain) section_seven_three_remain,SUM(section_seven_seven_remain+section_eight_seven_remain) section_seven_seven_remain,
		SUM(section_seven_fourteen_remain+section_eight_fourteen_remain) section_seven_fourteen_remain,SUM(section_seven_thirty_remain+section_eight_thirty_remain) section_seven_thirty_remain,
		SUM(section_seven_sixty_remain+section_eight_sixty_remain) section_seven_sixty_remain,
		
		SUM(section_nine_user_count+section_ten_user_count+section_eleven_user_count) section_nine_user_count,
		SUM(section_nine_two_remain+section_ten_two_remain+section_eleven_user_count_two_remain) section_nine_two_remain,
		SUM(section_nine_three_remain+section_ten_three_remain+section_eleven_user_count_three_remain) section_nine_three_remain,
		SUM(section_nine_seven_remain+section_ten_seven_remain+section_eleven_user_count_seven_remain) section_nine_seven_remain,
		SUM(section_nine_fourteen_remain+section_ten_fourteen_remain+section_eleven_user_count_fourteen_remain) section_nine_fourteen_remain,
		SUM(section_nine_thirty_remain+section_ten_thirty_remain+section_eleven_user_count_thirty_remain) section_nine_thirty_remain,
		SUM(section_nine_sixty_remain+section_ten_sixty_remain+section_eleven_user_count_sixty_remain) section_nine_sixty_remain,
		
		SUM(section_twelve_user_count+section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_twelve_user_count,
		SUM(section_twelve_user_count_two_remain+section_thirteen_user_count_two_remain+section_fourteen_user_count_two_remain+section_fifteen_user_count_two_remain) section_twelve_user_count_two_remain,
		SUM(section_twelve_user_count_three_remain+section_thirteen_user_count_three_remain+section_fourteen_user_count_three_remain+section_fifteen_user_count_three_remain) section_twelve_user_count_three_remain,
		SUM(section_twelve_user_count_seven_remain+section_thirteen_user_count_seven_remain+section_fourteen_user_count_seven_remain+section_fifteen_user_count_seven_remain) section_twelve_user_count_seven_remain,
		SUM(section_twelve_user_count_fourteen_remain+section_thirteen_user_count_fourteen_remain+section_fourteen_user_count_fourteen_remain+section_fifteen_user_count_fourteen_remain) section_twelve_user_count_fourteen_remain,
		SUM(section_twelve_user_count_thirty_remain+section_thirteen_user_count_thirty_remain+section_fourteen_user_count_thirty_remain+section_fifteen_user_count_thirty_remain) section_twelve_user_count_thirty_remain,
		SUM(section_twelve_user_count_sixty_remain+section_thirteen_user_count_sixty_remain+section_fourteen_user_count_sixty_remain+section_fifteen_user_count_thirty_remain) section_twelve_user_count_sixty_remain
		
		FROM ads_pay_user_remain_daily
		WHERE create_date BETWEEN #{startTime} AND #{endTime}
		GROUP BY create_date,appid
		) a
		LEFT JOIN (
		SELECT tdate,appid AS bappid,sum(first_count) first_pay_count,sum(two_remain) first_pay_two_remain,sum(three_remain) first_pay_three_remain,
		sum(seven_remain) first_pay_seven_remain,sum(fourteen_remain) first_pay_fourteen_remain,sum(thirty_remain) first_pay_thirty_remain,sum(sixty_remain) first_pay_sixty_remain
		FROM ads_first_pay_user_remain_daily WHERE tdate BETWEEN #{startTime} AND #{endTime}
		GROUP BY tdate,appid
		) b on a.create_date = b.tdate AND a.appid = b.bappid
		LEFT JOIN (
		SELECT tdate,appid AS cappid,sum(first_count) new_first_pay_count,sum(two_remain) new_first_pay_two_remain,sum(three_remain) new_first_pay_three_remain,
		sum(seven_remain) new_first_pay_seven_remain,sum(fourteen_remain) new_first_pay_fourteen_remain,sum(thirty_remain) new_first_pay_thirty_remain,sum(sixty_remain) new_first_pay_sixty_remain
		FROM ads_first_pay_user_remain_daily WHERE tdate BETWEEN #{startTime} AND #{endTime} AND user_type=1
		GROUP BY tdate,appid
		) c on a.create_date = c.tdate AND a.appid = c.cappid
		LEFT JOIN (
			SELECT tdate,appid AS dappid,sum(day0_retention_users)  day0_retention_users,sum(stage_one_valid_users)  stage_one_valid_users,sum(reg_users) reg_users,
			sum(stage_one_retention_users)  stage_one_retention_users,sum(stage_two_retention_users)  stage_two_retention_users 
			from ads_user_login_rate_daily WHERE tdate BETWEEN #{startTime} AND #{endTime} GROUP BY tdate,appid 
		) d on a.create_date = d.tdate AND a.appid = d.dappid
		WHERE 1=1
		<if test="appid != null and appid != ''">
			AND appid in (
				${appid}
			)
		</if>
		<if test="group != null and group != ''">
			GROUP BY ${group}
		</if>
		<if test="order != null and order != ''">
			ORDER BY ${order}
		</if>
	</select>

	<select id="selectSumPayUserRetentionList" resultType="java.util.Map">
		SELECT
		SUM(active_user_count) active_user_count,SUM(active_user_count_two_remain)/SUM(active_user_count) active_user_count_two_remain,SUM(active_user_count_three_remain)/SUM(active_user_count) active_user_count_three_remain,SUM(active_user_count_seven_remain)/SUM(active_user_count) active_user_count_seven_remain,
		SUM(active_user_count_fourteen_remain)/SUM(active_user_count) active_user_count_fourteen_remain,SUM(active_user_count_thirty_remain)/SUM(active_user_count) active_user_count_thirty_remain,SUM(active_user_count_sixty_remain)/SUM(active_user_count) active_user_count_sixty_remain,
		SUM(new_user_count) new_user_count,SUM(new_user_count_two_remain)/SUM(new_user_count) new_user_count_two_remain,SUM(new_user_count_three_remain)/SUM(new_user_count) new_user_count_three_remain,SUM(new_user_count_seven_remain)/SUM(new_user_count) new_user_count_seven_remain,
		SUM(new_user_count_fourteen_remain)/SUM(new_user_count) new_user_count_fourteen_remain,SUM(new_user_count_thirty_remain)/SUM(new_user_count) new_user_count_thirty_remain,SUM(new_user_count_sixty_remain)/SUM(new_user_count) new_user_count_sixty_remain,
		SUM(section_zero_user_count) section_zero_user_count,SUM(section_zero_two_remain)/SUM(section_zero_user_count) section_zero_two_remain,SUM(section_zero_three_remain)/SUM(section_zero_user_count) section_zero_three_remain,SUM(section_zero_seven_remain)/SUM(section_zero_user_count) section_zero_seven_remain,
		SUM(section_zero_fourteen_remain)/SUM(section_zero_user_count) section_zero_fourteen_remain,SUM(section_zero_thirty_remain)/SUM(section_zero_user_count) section_zero_thirty_remain,SUM(section_zero_sixty_remain)/SUM(section_zero_user_count) section_zero_sixty_remain,
		
		SUM(section_one_user_count) section_one_user_count,SUM(section_one_two_remain)/SUM(section_one_user_count) section_one_two_remain,SUM(section_one_three_remain)/SUM(section_one_user_count) section_one_three_remain,SUM(section_one_seven_remain)/SUM(section_one_user_count) section_one_seven_remain,
		SUM(section_one_fourteen_remain)/SUM(section_one_user_count) section_one_fourteen_remain,SUM(section_one_thirty_remain)/SUM(section_one_user_count) section_one_thirty_remain,SUM(section_one_sixty_remain)/SUM(section_one_user_count) section_one_sixty_remain,
		
		SUM(section_three_user_count) section_three_user_count,SUM(section_three_two_remain)/SUM(section_three_user_count) section_three_two_remain,SUM(section_three_three_remain)/SUM(section_three_user_count) section_three_three_remain,SUM(section_three_seven_remain)/SUM(section_three_user_count) section_three_seven_remain,
		SUM(section_three_fourteen_remain)/SUM(section_three_user_count) section_three_fourteen_remain,SUM(section_three_thirty_remain)/SUM(section_three_user_count) section_three_thirty_remain,SUM(section_three_sixty_remain)/SUM(section_three_user_count) section_three_sixty_remain,
		SUM(section_four_user_count) section_four_user_count,SUM(section_four_two_remain)/SUM(section_four_user_count) section_four_two_remain,SUM(section_four_three_remain)/SUM(section_four_user_count) section_four_three_remain,SUM(section_four_seven_remain)/SUM(section_four_user_count) section_four_seven_remain,
		SUM(section_four_fourteen_remain)/SUM(section_four_user_count) section_four_fourteen_remain,SUM(section_four_thirty_remain)/SUM(section_four_user_count) section_four_thirty_remain,SUM(section_four_sixty_remain)/SUM(section_four_user_count) section_four_sixty_remain,
		
		SUM(section_five_user_count) section_five_user_count,SUM(section_five_two_remain)/SUM(section_five_user_count) section_five_two_remain,SUM(section_five_three_remain)/SUM(section_five_user_count) section_five_three_remain,SUM(section_five_seven_remain)/SUM(section_five_user_count) section_five_seven_remain,
		SUM(section_five_fourteen_remain)/SUM(section_five_user_count) section_five_fourteen_remain,SUM(section_five_thirty_remain)/SUM(section_five_user_count) section_five_thirty_remain,SUM(section_five_sixty_remain)/SUM(section_five_user_count) section_five_sixty_remain,
		
		SUM(section_seven_user_count) section_seven_user_count,SUM(section_seven_two_remain)/SUM(section_seven_user_count) section_seven_two_remain,SUM(section_seven_three_remain)/SUM(section_seven_user_count) section_seven_three_remain,SUM(section_seven_seven_remain)/SUM(section_seven_user_count) section_seven_seven_remain,
		SUM(section_seven_fourteen_remain)/SUM(section_seven_user_count) section_seven_fourteen_remain,SUM(section_seven_thirty_remain)/SUM(section_seven_user_count) section_seven_thirty_remain,SUM(section_seven_sixty_remain)/SUM(section_seven_user_count) section_seven_sixty_remain,
		
		SUM(section_nine_user_count) section_nine_user_count,SUM(section_nine_two_remain)/SUM(section_nine_user_count) section_nine_two_remain,SUM(section_nine_three_remain)/SUM(section_nine_user_count) section_nine_three_remain,SUM(section_nine_seven_remain)/SUM(section_nine_user_count) section_nine_seven_remain,
		SUM(section_nine_fourteen_remain)/SUM(section_nine_user_count) section_nine_fourteen_remain,SUM(section_nine_thirty_remain)/SUM(section_nine_user_count) section_nine_thirty_remain,SUM(section_nine_sixty_remain)/SUM(section_nine_user_count) section_nine_sixty_remain,
		
		SUM(section_twelve_user_count) section_twelve_user_count,SUM(section_twelve_user_count_two_remain)/SUM(section_twelve_user_count) section_twelve_two_remain,SUM(section_twelve_user_count_three_remain)/SUM(section_twelve_user_count) section_twelve_three_remain,SUM(section_twelve_user_count_seven_remain)/SUM(section_twelve_user_count) section_twelve_seven_remain,
		SUM(section_twelve_user_count_fourteen_remain)/SUM(section_twelve_user_count) section_twelve_fourteen_remain,SUM(section_twelve_user_count_thirty_remain)/SUM(section_twelve_user_count) section_twelve_thirty_remain,SUM(section_twelve_user_count_sixty_remain)/SUM(section_twelve_user_count) section_twelve_sixty_remain,
		
		SUM(active_user_count)-SUM(section_zero_user_count) pay_user_count,(SUM(active_user_count_two_remain)-SUM(section_zero_two_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(section_zero_three_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(section_zero_seven_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(section_zero_fourteen_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(section_zero_thirty_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(section_zero_sixty_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_sixty_remain,
		SUM(active_user_count)-SUM(new_user_count) old_pay_user_count,(SUM(active_user_count_two_remain)-SUM(new_user_count_two_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(new_user_count_three_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(new_user_count_seven_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(new_user_count_fourteen_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(new_user_count_thirty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(new_user_count_sixty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_sixty_remain,
		IFNULL(SUM(first_pay_count),0) first_pay_count,SUM(first_pay_two_remain)/SUM(first_pay_count) first_pay_two_remain,SUM(first_pay_three_remain)/SUM(first_pay_count) first_pay_three_remain,SUM(first_pay_seven_remain)/SUM(first_pay_count) first_pay_seven_remain,SUM(first_pay_fourteen_remain)/SUM(first_pay_count) first_pay_fourteen_remain,
		SUM(first_pay_thirty_remain)/SUM(first_pay_count) first_pay_thirty_remain,SUM(first_pay_sixty_remain)/SUM(first_pay_count) first_pay_sixty_remain,
		IFNULL(SUM(new_first_pay_count),0) new_first_pay_count,SUM(new_first_pay_two_remain)/SUM(new_first_pay_count) new_first_pay_two_remain,SUM(new_first_pay_three_remain)/SUM(new_first_pay_count) new_first_pay_three_remain,SUM(new_first_pay_seven_remain)/SUM(new_first_pay_count) new_first_pay_seven_remain,SUM(new_first_pay_fourteen_remain)/SUM(new_first_pay_count) new_first_pay_fourteen_remain,
		SUM(new_first_pay_thirty_remain)/SUM(new_first_pay_count) new_first_pay_thirty_remain,SUM(new_first_pay_sixty_remain)/SUM(new_first_pay_count) new_first_pay_sixty_remain,
		sum(day0_retention_users)/sum(reg_users)  d0_retention_rate,sum(stage_one_valid_users)  stage_one_valid_users,sum(stage_one_retention_users)  stage_one_retention_users,
		sum(stage_two_retention_users)  stage_two_retention_users ,sum(stage_one_retention_users)/sum(stage_one_valid_users) rank_1_entry_ratio,
		sum(stage_two_retention_users)/sum(stage_one_retention_users) rank_2_entry_ratio
		FROM (
		SELECT create_date,appid,
		SUM(active_user_count) active_user_count,SUM(active_user_count_two_remain) active_user_count_two_remain,SUM(active_user_count_three_remain) active_user_count_three_remain,SUM(active_user_count_seven_remain) active_user_count_seven_remain,
		SUM(active_user_count_fourteen_remain) active_user_count_fourteen_remain,SUM(active_user_count_thirty_remain) active_user_count_thirty_remain,SUM(active_user_count_sixty_remain) active_user_count_sixty_remain,
		SUM(new_user_count) new_user_count,SUM(new_user_count_two_remain) new_user_count_two_remain,SUM(new_user_count_three_remain) new_user_count_three_remain,SUM(new_user_count_seven_remain) new_user_count_seven_remain,
		SUM(new_user_count_fourteen_remain) new_user_count_fourteen_remain,SUM(new_user_count_thirty_remain) new_user_count_thirty_remain,SUM(new_user_count_sixty_remain) new_user_count_sixty_remain,
		SUM(section_zero_user_count) section_zero_user_count,SUM(section_zero_two_remain) section_zero_two_remain,SUM(section_zero_three_remain) section_zero_three_remain,SUM(section_zero_seven_remain) section_zero_seven_remain,
		SUM(section_zero_fourteen_remain) section_zero_fourteen_remain,SUM(section_zero_thirty_remain) section_zero_thirty_remain,SUM(section_zero_sixty_remain) section_zero_sixty_remain,
		
		SUM(section_one_user_count+section_two_user_count) section_one_user_count,SUM(section_one_two_remain+section_two_two_remain) section_one_two_remain,
		SUM(section_one_three_remain+section_two_three_remain) section_one_three_remain,SUM(section_one_seven_remain+section_two_seven_remain) section_one_seven_remain,
		SUM(section_one_fourteen_remain+section_two_fourteen_remain) section_one_fourteen_remain,SUM(section_one_thirty_remain+section_two_thirty_remain) section_one_thirty_remain,
		SUM(section_one_sixty_remain+section_two_sixty_remain) section_one_sixty_remain,
		
		SUM(section_three_user_count) section_three_user_count,SUM(section_three_two_remain) section_three_two_remain,SUM(section_three_three_remain) section_three_three_remain,SUM(section_three_seven_remain) section_three_seven_remain,
		SUM(section_three_fourteen_remain) section_three_fourteen_remain,SUM(section_three_thirty_remain) section_three_thirty_remain,SUM(section_three_sixty_remain) section_three_sixty_remain,
		SUM(section_four_user_count) section_four_user_count,SUM(section_four_two_remain) section_four_two_remain,SUM(section_four_three_remain) section_four_three_remain,SUM(section_four_seven_remain) section_four_seven_remain,
		SUM(section_four_fourteen_remain) section_four_fourteen_remain,SUM(section_four_thirty_remain) section_four_thirty_remain,SUM(section_four_sixty_remain) section_four_sixty_remain,
		
		SUM(section_five_user_count+section_six_user_count) section_five_user_count,SUM(section_five_two_remain+section_six_two_remain) section_five_two_remain,
		SUM(section_five_three_remain+section_six_three_remain) section_five_three_remain,SUM(section_five_seven_remain+section_six_seven_remain) section_five_seven_remain,
		SUM(section_five_fourteen_remain+section_six_fourteen_remain) section_five_fourteen_remain,SUM(section_five_thirty_remain+section_six_thirty_remain) section_five_thirty_remain,
		SUM(section_five_sixty_remain+section_six_sixty_remain) section_five_sixty_remain,
		
		SUM(section_seven_user_count+section_eight_user_count) section_seven_user_count,SUM(section_seven_two_remain+section_eight_two_remain) section_seven_two_remain,
		SUM(section_seven_three_remain+section_eight_three_remain) section_seven_three_remain,SUM(section_seven_seven_remain+section_eight_seven_remain) section_seven_seven_remain,
		SUM(section_seven_fourteen_remain+section_eight_fourteen_remain) section_seven_fourteen_remain,SUM(section_seven_thirty_remain+section_eight_thirty_remain) section_seven_thirty_remain,
		SUM(section_seven_sixty_remain+section_eight_sixty_remain) section_seven_sixty_remain,
		
		SUM(section_nine_user_count+section_ten_user_count+section_eleven_user_count) section_nine_user_count,
		SUM(section_nine_two_remain+section_ten_two_remain+section_eleven_user_count_two_remain) section_nine_two_remain,
		SUM(section_nine_three_remain+section_ten_three_remain+section_eleven_user_count_three_remain) section_nine_three_remain,
		SUM(section_nine_seven_remain+section_ten_seven_remain+section_eleven_user_count_seven_remain) section_nine_seven_remain,
		SUM(section_nine_fourteen_remain+section_ten_fourteen_remain+section_eleven_user_count_fourteen_remain) section_nine_fourteen_remain,
		SUM(section_nine_thirty_remain+section_ten_thirty_remain+section_eleven_user_count_thirty_remain) section_nine_thirty_remain,
		SUM(section_nine_sixty_remain+section_ten_sixty_remain+section_eleven_user_count_sixty_remain) section_nine_sixty_remain,
		
		SUM(section_twelve_user_count+section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_twelve_user_count,
		SUM(section_twelve_user_count_two_remain+section_thirteen_user_count_two_remain+section_fourteen_user_count_two_remain+section_fifteen_user_count_two_remain) section_twelve_user_count_two_remain,
		SUM(section_twelve_user_count_three_remain+section_thirteen_user_count_three_remain+section_fourteen_user_count_three_remain+section_fifteen_user_count_three_remain) section_twelve_user_count_three_remain,
		SUM(section_twelve_user_count_seven_remain+section_thirteen_user_count_seven_remain+section_fourteen_user_count_seven_remain+section_fifteen_user_count_seven_remain) section_twelve_user_count_seven_remain,
		SUM(section_twelve_user_count_fourteen_remain+section_thirteen_user_count_fourteen_remain+section_fourteen_user_count_fourteen_remain+section_fifteen_user_count_fourteen_remain) section_twelve_user_count_fourteen_remain,
		SUM(section_twelve_user_count_thirty_remain+section_thirteen_user_count_thirty_remain+section_fourteen_user_count_thirty_remain+section_fifteen_user_count_thirty_remain) section_twelve_user_count_thirty_remain,
		SUM(section_twelve_user_count_sixty_remain+section_thirteen_user_count_sixty_remain+section_fourteen_user_count_sixty_remain+section_fifteen_user_count_thirty_remain) section_twelve_user_count_sixty_remain
		
		FROM ads_pay_user_remain_daily
		WHERE create_date BETWEEN #{startTime} AND #{endTime}
		GROUP BY create_date,appid
		) a
		LEFT JOIN (
		SELECT tdate,appid AS bappid,sum(first_count) first_pay_count,sum(two_remain) first_pay_two_remain,sum(three_remain) first_pay_three_remain,
		sum(seven_remain) first_pay_seven_remain,sum(fourteen_remain) first_pay_fourteen_remain,sum(thirty_remain) first_pay_thirty_remain,sum(sixty_remain) first_pay_sixty_remain
		FROM ads_first_pay_user_remain_daily WHERE tdate BETWEEN #{startTime} AND #{endTime}
		GROUP BY tdate,appid
		) b on a.create_date = b.tdate AND a.appid = b.bappid
		LEFT JOIN (
		SELECT tdate,appid AS cappid,sum(first_count) new_first_pay_count,sum(two_remain) new_first_pay_two_remain,sum(three_remain) new_first_pay_three_remain,
		sum(seven_remain) new_first_pay_seven_remain,sum(fourteen_remain) new_first_pay_fourteen_remain,sum(thirty_remain) new_first_pay_thirty_remain,sum(sixty_remain) new_first_pay_sixty_remain
		FROM ads_first_pay_user_remain_daily WHERE tdate BETWEEN #{startTime} AND #{endTime} AND user_type=1
		GROUP BY tdate,appid
		) c on a.create_date = c.tdate AND a.appid = c.cappid
		LEFT JOIN (
			SELECT tdate,appid AS dappid,sum(day0_retention_users)  day0_retention_users,sum(stage_one_valid_users)  stage_one_valid_users,sum(reg_users) reg_users,
			sum(stage_one_retention_users)  stage_one_retention_users,sum(stage_two_retention_users)  stage_two_retention_users 
			from ads_user_login_rate_daily WHERE tdate BETWEEN #{startTime} AND #{endTime} GROUP BY tdate,appid
		) d on a.create_date = d.tdate AND a.appid = d.dappid
		WHERE 1=1
		<if test="appid != null and appid != ''">
			AND appid in (
			${appid}
			)
		</if>
	</select>

	<select id="selectChartPayUserRetentionList" resultType="java.util.Map">
		SELECT create_date,'中国' as country,
		SUM(active_user_count) active_user_count,SUM(active_user_count_two_remain)/SUM(active_user_count) active_user_count_two_remain,SUM(active_user_count_three_remain)/SUM(active_user_count) active_user_count_three_remain,SUM(active_user_count_seven_remain)/SUM(active_user_count) active_user_count_seven_remain,
		SUM(active_user_count_fourteen_remain)/SUM(active_user_count) active_user_count_fourteen_remain,SUM(active_user_count_thirty_remain)/SUM(active_user_count) active_user_count_thirty_remain,SUM(active_user_count_sixty_remain)/SUM(active_user_count) active_user_count_sixty_remain,
		SUM(new_user_count) new_user_count,SUM(new_user_count_two_remain)/SUM(new_user_count) new_user_count_two_remain,SUM(new_user_count_three_remain)/SUM(new_user_count) new_user_count_three_remain,SUM(new_user_count_seven_remain)/SUM(new_user_count) new_user_count_seven_remain,
		SUM(new_user_count_fourteen_remain)/SUM(new_user_count) new_user_count_fourteen_remain,SUM(new_user_count_thirty_remain)/SUM(new_user_count) new_user_count_thirty_remain,SUM(new_user_count_sixty_remain)/SUM(new_user_count) new_user_count_sixty_remain,
		SUM(section_zero_user_count) section_zero_user_count,SUM(section_zero_two_remain)/SUM(section_zero_user_count) section_zero_two_remain,SUM(section_zero_three_remain)/SUM(section_zero_user_count) section_zero_three_remain,SUM(section_zero_seven_remain)/SUM(section_zero_user_count) section_zero_seven_remain,
		SUM(section_zero_fourteen_remain)/SUM(section_zero_user_count) section_zero_fourteen_remain,SUM(section_zero_thirty_remain)/SUM(section_zero_user_count) section_zero_thirty_remain,SUM(section_zero_sixty_remain)/SUM(section_zero_user_count) section_zero_sixty_remain,
		
		SUM(section_one_user_count) section_one_user_count,SUM(section_one_two_remain)/SUM(section_one_user_count) section_one_two_remain,SUM(section_one_three_remain)/SUM(section_one_user_count) section_one_three_remain,SUM(section_one_seven_remain)/SUM(section_one_user_count) section_one_seven_remain,
		SUM(section_one_fourteen_remain)/SUM(section_one_user_count) section_one_fourteen_remain,SUM(section_one_thirty_remain)/SUM(section_one_user_count) section_one_thirty_remain,SUM(section_one_sixty_remain)/SUM(section_one_user_count) section_one_sixty_remain,
		
		SUM(section_three_user_count) section_three_user_count,SUM(section_three_two_remain)/SUM(section_three_user_count) section_three_two_remain,SUM(section_three_three_remain)/SUM(section_three_user_count) section_three_three_remain,SUM(section_three_seven_remain)/SUM(section_three_user_count) section_three_seven_remain,
		SUM(section_three_fourteen_remain)/SUM(section_three_user_count) section_three_fourteen_remain,SUM(section_three_thirty_remain)/SUM(section_three_user_count) section_three_thirty_remain,SUM(section_three_sixty_remain)/SUM(section_three_user_count) section_three_sixty_remain,
		SUM(section_four_user_count) section_four_user_count,SUM(section_four_two_remain)/SUM(section_four_user_count) section_four_two_remain,SUM(section_four_three_remain)/SUM(section_four_user_count) section_four_three_remain,SUM(section_four_seven_remain)/SUM(section_four_user_count) section_four_seven_remain,
		SUM(section_four_fourteen_remain)/SUM(section_four_user_count) section_four_fourteen_remain,SUM(section_four_thirty_remain)/SUM(section_four_user_count) section_four_thirty_remain,SUM(section_four_sixty_remain)/SUM(section_four_user_count) section_four_sixty_remain,
		
		SUM(section_five_user_count) section_five_user_count,SUM(section_five_two_remain)/SUM(section_five_user_count) section_five_two_remain,SUM(section_five_three_remain)/SUM(section_five_user_count) section_five_three_remain,SUM(section_five_seven_remain)/SUM(section_five_user_count) section_five_seven_remain,
		SUM(section_five_fourteen_remain)/SUM(section_five_user_count) section_five_fourteen_remain,SUM(section_five_thirty_remain)/SUM(section_five_user_count) section_five_thirty_remain,SUM(section_five_sixty_remain)/SUM(section_five_user_count) section_five_sixty_remain,
		
		SUM(section_seven_user_count) section_seven_user_count,SUM(section_seven_two_remain)/SUM(section_seven_user_count) section_seven_two_remain,SUM(section_seven_three_remain)/SUM(section_seven_user_count) section_seven_three_remain,SUM(section_seven_seven_remain)/SUM(section_seven_user_count) section_seven_seven_remain,
		SUM(section_seven_fourteen_remain)/SUM(section_seven_user_count) section_seven_fourteen_remain,SUM(section_seven_thirty_remain)/SUM(section_seven_user_count) section_seven_thirty_remain,SUM(section_seven_sixty_remain)/SUM(section_seven_user_count) section_seven_sixty_remain,
		
		SUM(section_nine_user_count) section_nine_user_count,SUM(section_nine_two_remain)/SUM(section_nine_user_count) section_nine_two_remain,SUM(section_nine_three_remain)/SUM(section_nine_user_count) section_nine_three_remain,SUM(section_nine_seven_remain)/SUM(section_nine_user_count) section_nine_seven_remain,
		SUM(section_nine_fourteen_remain)/SUM(section_nine_user_count) section_nine_fourteen_remain,SUM(section_nine_thirty_remain)/SUM(section_nine_user_count) section_nine_thirty_remain,SUM(section_nine_sixty_remain)/SUM(section_nine_user_count) section_nine_sixty_remain,
		
		SUM(section_twelve_user_count) section_twelve_user_count,SUM(section_twelve_user_count_two_remain)/SUM(section_twelve_user_count) section_twelve_two_remain,SUM(section_twelve_user_count_three_remain)/SUM(section_twelve_user_count) section_twelve_three_remain,SUM(section_twelve_user_count_seven_remain)/SUM(section_twelve_user_count) section_twelve_seven_remain,
		SUM(section_twelve_user_count_fourteen_remain)/SUM(section_twelve_user_count) section_twelve_fourteen_remain,SUM(section_twelve_user_count_thirty_remain)/SUM(section_twelve_user_count) section_twelve_thirty_remain,SUM(section_twelve_user_count_sixty_remain)/SUM(section_twelve_user_count) section_twelve_sixty_remain,
	
				
		SUM(active_user_count)-SUM(section_zero_user_count) pay_user_count,(SUM(active_user_count_two_remain)-SUM(section_zero_two_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(section_zero_three_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(section_zero_seven_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(section_zero_fourteen_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(section_zero_thirty_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(section_zero_sixty_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_sixty_remain,
		SUM(active_user_count)-SUM(new_user_count) old_pay_user_count,(SUM(active_user_count_two_remain)-SUM(new_user_count_two_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(new_user_count_three_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(new_user_count_seven_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(new_user_count_fourteen_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(new_user_count_thirty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(new_user_count_sixty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_sixty_remain,
		IFNULL(SUM(first_pay_count),0) first_pay_count,SUM(first_pay_two_remain)/SUM(first_pay_count) first_pay_two_remain,SUM(first_pay_three_remain)/SUM(first_pay_count) first_pay_three_remain,SUM(first_pay_seven_remain)/SUM(first_pay_count) first_pay_seven_remain,SUM(first_pay_fourteen_remain)/SUM(first_pay_count) first_pay_fourteen_remain,
		SUM(first_pay_thirty_remain)/SUM(first_pay_count) first_pay_thirty_remain,SUM(first_pay_sixty_remain)/SUM(first_pay_count) first_pay_sixty_remain,
		IFNULL(SUM(new_first_pay_count),0) new_first_pay_count,SUM(new_first_pay_two_remain)/SUM(new_first_pay_count) new_first_pay_two_remain,SUM(new_first_pay_three_remain)/SUM(new_first_pay_count) new_first_pay_three_remain,SUM(new_first_pay_seven_remain)/SUM(new_first_pay_count) new_first_pay_seven_remain,SUM(new_first_pay_fourteen_remain)/SUM(new_first_pay_count) new_first_pay_fourteen_remain,
		SUM(new_first_pay_thirty_remain)/SUM(new_first_pay_count) new_first_pay_thirty_remain,SUM(new_first_pay_sixty_remain)/SUM(new_first_pay_count) new_first_pay_sixty_remain,
		sum(day0_retention_users)/sum(reg_users)  d0_retention_rate,sum(stage_one_valid_users)  stage_one_valid_users,sum(stage_one_retention_users)  stage_one_retention_users,
		sum(stage_two_retention_users)  stage_two_retention_users ,sum(stage_one_retention_users)/sum(stage_one_valid_users) rank_1_entry_ratio,
		sum(stage_two_retention_users)/sum(stage_one_retention_users) rank_2_entry_ratio
		FROM (
		SELECT create_date,appid,
		SUM(active_user_count) active_user_count,SUM(active_user_count_two_remain) active_user_count_two_remain,SUM(active_user_count_three_remain) active_user_count_three_remain,SUM(active_user_count_seven_remain) active_user_count_seven_remain,
		SUM(active_user_count_fourteen_remain) active_user_count_fourteen_remain,SUM(active_user_count_thirty_remain) active_user_count_thirty_remain,SUM(active_user_count_sixty_remain) active_user_count_sixty_remain,
		SUM(new_user_count) new_user_count,SUM(new_user_count_two_remain) new_user_count_two_remain,SUM(new_user_count_three_remain) new_user_count_three_remain,SUM(new_user_count_seven_remain) new_user_count_seven_remain,
		SUM(new_user_count_fourteen_remain) new_user_count_fourteen_remain,SUM(new_user_count_thirty_remain) new_user_count_thirty_remain,SUM(new_user_count_sixty_remain) new_user_count_sixty_remain,
		SUM(section_zero_user_count) section_zero_user_count,SUM(section_zero_two_remain) section_zero_two_remain,SUM(section_zero_three_remain) section_zero_three_remain,SUM(section_zero_seven_remain) section_zero_seven_remain,
		SUM(section_zero_fourteen_remain) section_zero_fourteen_remain,SUM(section_zero_thirty_remain) section_zero_thirty_remain,SUM(section_zero_sixty_remain) section_zero_sixty_remain,
		
		SUM(section_one_user_count+section_two_user_count) section_one_user_count,SUM(section_one_two_remain+section_two_two_remain) section_one_two_remain,
		SUM(section_one_three_remain+section_two_three_remain) section_one_three_remain,SUM(section_one_seven_remain+section_two_seven_remain) section_one_seven_remain,
		SUM(section_one_fourteen_remain+section_two_fourteen_remain) section_one_fourteen_remain,SUM(section_one_thirty_remain+section_two_thirty_remain) section_one_thirty_remain,
		SUM(section_one_sixty_remain+section_two_sixty_remain) section_one_sixty_remain,
		
		SUM(section_three_user_count) section_three_user_count,SUM(section_three_two_remain) section_three_two_remain,SUM(section_three_three_remain) section_three_three_remain,SUM(section_three_seven_remain) section_three_seven_remain,
		SUM(section_three_fourteen_remain) section_three_fourteen_remain,SUM(section_three_thirty_remain) section_three_thirty_remain,SUM(section_three_sixty_remain) section_three_sixty_remain,
		SUM(section_four_user_count) section_four_user_count,SUM(section_four_two_remain) section_four_two_remain,SUM(section_four_three_remain) section_four_three_remain,SUM(section_four_seven_remain) section_four_seven_remain,
		SUM(section_four_fourteen_remain) section_four_fourteen_remain,SUM(section_four_thirty_remain) section_four_thirty_remain,SUM(section_four_sixty_remain) section_four_sixty_remain,
		
		SUM(section_five_user_count+section_six_user_count) section_five_user_count,SUM(section_five_two_remain+section_six_two_remain) section_five_two_remain,
		SUM(section_five_three_remain+section_six_three_remain) section_five_three_remain,SUM(section_five_seven_remain+section_six_seven_remain) section_five_seven_remain,
		SUM(section_five_fourteen_remain+section_six_fourteen_remain) section_five_fourteen_remain,SUM(section_five_thirty_remain+section_six_thirty_remain) section_five_thirty_remain,
		SUM(section_five_sixty_remain+section_six_sixty_remain) section_five_sixty_remain,
		
		SUM(section_seven_user_count+section_eight_user_count) section_seven_user_count,SUM(section_seven_two_remain+section_eight_two_remain) section_seven_two_remain,
		SUM(section_seven_three_remain+section_eight_three_remain) section_seven_three_remain,SUM(section_seven_seven_remain+section_eight_seven_remain) section_seven_seven_remain,
		SUM(section_seven_fourteen_remain+section_eight_fourteen_remain) section_seven_fourteen_remain,SUM(section_seven_thirty_remain+section_eight_thirty_remain) section_seven_thirty_remain,
		SUM(section_seven_sixty_remain+section_eight_sixty_remain) section_seven_sixty_remain,
		
		SUM(section_nine_user_count+section_ten_user_count+section_eleven_user_count) section_nine_user_count,
		SUM(section_nine_two_remain+section_ten_two_remain+section_eleven_user_count_two_remain) section_nine_two_remain,
		SUM(section_nine_three_remain+section_ten_three_remain+section_eleven_user_count_three_remain) section_nine_three_remain,
		SUM(section_nine_seven_remain+section_ten_seven_remain+section_eleven_user_count_seven_remain) section_nine_seven_remain,
		SUM(section_nine_fourteen_remain+section_ten_fourteen_remain+section_eleven_user_count_fourteen_remain) section_nine_fourteen_remain,
		SUM(section_nine_thirty_remain+section_ten_thirty_remain+section_eleven_user_count_thirty_remain) section_nine_thirty_remain,
		SUM(section_nine_sixty_remain+section_ten_sixty_remain+section_eleven_user_count_sixty_remain) section_nine_sixty_remain,
		
		SUM(section_twelve_user_count+section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_twelve_user_count,
		SUM(section_twelve_user_count_two_remain+section_thirteen_user_count_two_remain+section_fourteen_user_count_two_remain+section_fifteen_user_count_two_remain) section_twelve_user_count_two_remain,
		SUM(section_twelve_user_count_three_remain+section_thirteen_user_count_three_remain+section_fourteen_user_count_three_remain+section_fifteen_user_count_three_remain) section_twelve_user_count_three_remain,
		SUM(section_twelve_user_count_seven_remain+section_thirteen_user_count_seven_remain+section_fourteen_user_count_seven_remain+section_fifteen_user_count_seven_remain) section_twelve_user_count_seven_remain,
		SUM(section_twelve_user_count_fourteen_remain+section_thirteen_user_count_fourteen_remain+section_fourteen_user_count_fourteen_remain+section_fifteen_user_count_fourteen_remain) section_twelve_user_count_fourteen_remain,
		SUM(section_twelve_user_count_thirty_remain+section_thirteen_user_count_thirty_remain+section_fourteen_user_count_thirty_remain+section_fifteen_user_count_thirty_remain) section_twelve_user_count_thirty_remain,
		SUM(section_twelve_user_count_sixty_remain+section_thirteen_user_count_sixty_remain+section_fourteen_user_count_sixty_remain+section_fifteen_user_count_thirty_remain) section_twelve_user_count_sixty_remain
		FROM ads_pay_user_remain_daily
		WHERE create_date BETWEEN #{startTime} AND #{endTime}
		GROUP BY create_date,appid
		) a
		LEFT JOIN (
		SELECT tdate,appid AS bappid,sum(first_count) first_pay_count,sum(two_remain) first_pay_two_remain,sum(three_remain) first_pay_three_remain,
		sum(seven_remain) first_pay_seven_remain,sum(fourteen_remain) first_pay_fourteen_remain,sum(thirty_remain) first_pay_thirty_remain,sum(sixty_remain) first_pay_sixty_remain
		FROM ads_first_pay_user_remain_daily WHERE tdate BETWEEN #{startTime} AND #{endTime}
		GROUP BY tdate,appid
		) b on a.create_date = b.tdate AND a.appid = b.bappid
		LEFT JOIN (
		SELECT tdate,appid AS cappid,sum(first_count) new_first_pay_count,sum(two_remain) new_first_pay_two_remain,sum(three_remain) new_first_pay_three_remain,
		sum(seven_remain) new_first_pay_seven_remain,sum(fourteen_remain) new_first_pay_fourteen_remain,sum(thirty_remain) new_first_pay_thirty_remain,sum(sixty_remain) new_first_pay_sixty_remain
		FROM ads_first_pay_user_remain_daily WHERE tdate BETWEEN #{startTime} AND #{endTime} AND user_type=1
		GROUP BY tdate,appid
		) c on a.create_date = c.tdate AND a.appid = c.cappid
		LEFT JOIN (
			SELECT tdate,appid AS dappid,sum(day0_retention_users)  day0_retention_users,sum(stage_one_valid_users)  stage_one_valid_users,sum(reg_users) reg_users,
			sum(stage_one_retention_users)  stage_one_retention_users,sum(stage_two_retention_users)  stage_two_retention_users 
			from ads_user_login_rate_daily WHERE tdate BETWEEN #{startTime} AND #{endTime} GROUP BY tdate,appid
		) d on a.create_date = d.tdate AND a.appid = d.dappid 
		WHERE 1=1
		<if test="appid != null and appid != ''">
			AND appid in (
			${appid}
			)
		</if>
		GROUP BY create_date ORDER BY create_date ASC
	</select>

	<select id="selectTotalPayUserRetentionList" resultType="java.util.Map">
		SELECT
		SUM(active_user_count) active_user_count,SUM(active_user_count_two_remain)/SUM(active_user_count) active_user_count_two_remain,SUM(active_user_count_three_remain)/SUM(active_user_count) active_user_count_three_remain,SUM(active_user_count_seven_remain)/SUM(active_user_count) active_user_count_seven_remain,
		SUM(active_user_count_fourteen_remain)/SUM(active_user_count) active_user_count_fourteen_remain,SUM(active_user_count_thirty_remain)/SUM(active_user_count) active_user_count_thirty_remain,SUM(active_user_count_sixty_remain)/SUM(active_user_count) active_user_count_sixty_remain,
		SUM(new_user_count) new_user_count,SUM(new_user_count_two_remain)/SUM(new_user_count) new_user_count_two_remain,SUM(new_user_count_three_remain)/SUM(new_user_count) new_user_count_three_remain,SUM(new_user_count_seven_remain)/SUM(new_user_count) new_user_count_seven_remain,
		SUM(new_user_count_fourteen_remain)/SUM(new_user_count) new_user_count_fourteen_remain,SUM(new_user_count_thirty_remain)/SUM(new_user_count) new_user_count_thirty_remain,SUM(new_user_count_sixty_remain)/SUM(new_user_count) new_user_count_sixty_remain,
		SUM(section_zero_user_count) section_zero_user_count,SUM(section_zero_two_remain)/SUM(section_zero_user_count) section_zero_two_remain,SUM(section_zero_three_remain)/SUM(section_zero_user_count) section_zero_three_remain,SUM(section_zero_seven_remain)/SUM(section_zero_user_count) section_zero_seven_remain,
		SUM(section_zero_fourteen_remain)/SUM(section_zero_user_count) section_zero_fourteen_remain,SUM(section_zero_thirty_remain)/SUM(section_zero_user_count) section_zero_thirty_remain,SUM(section_zero_sixty_remain)/SUM(section_zero_user_count) section_zero_sixty_remain,
		
		SUM(section_one_user_count+section_two_user_count) section_one_user_count,
		SUM(section_one_two_remain+section_two_two_remain)/SUM(section_one_user_count+section_two_user_count) section_one_two_remain,
		SUM(section_one_three_remain+section_two_three_remain)/SUM(section_one_user_count+section_two_user_count) section_one_three_remain,
		SUM(section_one_seven_remain+section_two_seven_remain)/SUM(section_one_user_count+section_two_user_count) section_one_seven_remain,
		SUM(section_one_fourteen_remain+section_two_fourteen_remain)/SUM(section_one_user_count+section_two_user_count) section_one_fourteen_remain,
		SUM(section_one_thirty_remain+section_two_thirty_remain)/SUM(section_one_user_count+section_two_user_count) section_one_thirty_remain,
		SUM(section_one_sixty_remain+section_two_sixty_remain)/SUM(section_one_user_count+section_two_user_count) section_one_sixty_remain,
		
		SUM(section_three_user_count) section_three_user_count,SUM(section_three_two_remain)/SUM(section_three_user_count) section_three_two_remain,SUM(section_three_three_remain)/SUM(section_three_user_count) section_three_three_remain,SUM(section_three_seven_remain)/SUM(section_three_user_count) section_three_seven_remain,
		SUM(section_three_fourteen_remain)/SUM(section_three_user_count) section_three_fourteen_remain,SUM(section_three_thirty_remain)/SUM(section_three_user_count) section_three_thirty_remain,SUM(section_three_sixty_remain)/SUM(section_three_user_count) section_three_sixty_remain,
		SUM(section_four_user_count) section_four_user_count,SUM(section_four_two_remain)/SUM(section_four_user_count) section_four_two_remain,SUM(section_four_three_remain)/SUM(section_four_user_count) section_four_three_remain,SUM(section_four_seven_remain)/SUM(section_four_user_count) section_four_seven_remain,
		SUM(section_four_fourteen_remain)/SUM(section_four_user_count) section_four_fourteen_remain,SUM(section_four_thirty_remain)/SUM(section_four_user_count) section_four_thirty_remain,SUM(section_four_sixty_remain)/SUM(section_four_user_count) section_four_sixty_remain,
		
		SUM(section_five_user_count+section_six_user_count) section_five_user_count,
		SUM(section_five_two_remain+section_six_two_remain)/SUM(section_five_user_count+section_six_user_count) section_five_two_remain,
		SUM(section_five_three_remain+section_six_three_remain)/SUM(section_five_user_count+section_six_user_count) section_five_three_remain,
		SUM(section_five_seven_remain+section_six_seven_remain)/SUM(section_five_user_count+section_six_user_count) section_five_seven_remain,
		SUM(section_five_fourteen_remain+section_six_fourteen_remain)/SUM(section_five_user_count+section_six_user_count) section_five_fourteen_remain,
		SUM(section_five_thirty_remain+section_six_thirty_remain)/SUM(section_five_user_count+section_six_user_count) section_five_thirty_remain,
		SUM(section_five_sixty_remain+section_six_sixty_remain)/SUM(section_five_user_count+section_six_user_count) section_five_sixty_remain,
		
		SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_user_count,
		SUM(section_nine_two_remain+section_seven_two_remain+section_eight_two_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_two_remain,
		SUM(section_nine_three_remain+section_seven_three_remain+section_eight_three_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_three_remain,
		SUM(section_nine_seven_remain+section_seven_seven_remain+section_eight_seven_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_seven_remain,
		SUM(section_nine_fourteen_remain+section_seven_fourteen_remain+section_eight_fourteen_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_fourteen_remain,
		SUM(section_nine_thirty_remain+section_seven_thirty_remain+section_eight_thirty_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_thirty_remain,
		SUM(section_nine_sixty_remain+section_seven_sixty_remain+section_eight_sixty_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_sixty_remain,
		
		SUM(section_ten_user_count) section_ten_user_count,SUM(section_ten_two_remain)/SUM(section_ten_user_count) section_ten_two_remain,SUM(section_ten_three_remain)/SUM(section_ten_user_count) section_ten_three_remain,SUM(section_ten_seven_remain)/SUM(section_ten_user_count) section_ten_seven_remain,
		SUM(section_ten_fourteen_remain)/SUM(section_ten_user_count) section_ten_fourteen_remain,SUM(section_ten_thirty_remain)/SUM(section_ten_user_count) section_ten_thirty_remain,SUM(section_ten_sixty_remain)/SUM(section_ten_user_count) section_ten_sixty_remain,
		SUM(section_eleven_user_count) section_eleven_user_count,SUM(section_eleven_user_count_two_remain)/SUM(section_eleven_user_count) section_eleven_two_remain,SUM(section_eleven_user_count_three_remain)/SUM(section_eleven_user_count) section_eleven_three_remain,SUM(section_eleven_user_count_seven_remain)/SUM(section_eleven_user_count) section_eleven_seven_remain,
		SUM(section_eleven_user_count_fourteen_remain)/SUM(section_eleven_user_count) section_eleven_fourteen_remain,SUM(section_eleven_user_count_thirty_remain)/SUM(section_eleven_user_count) section_eleven_thirty_remain,SUM(section_eleven_user_count_sixty_remain)/SUM(section_eleven_user_count) section_eleven_sixty_remain,
		SUM(section_twelve_user_count) section_twelve_user_count,SUM(section_twelve_user_count_two_remain)/SUM(section_twelve_user_count) section_twelve_two_remain,SUM(section_twelve_user_count_three_remain)/SUM(section_twelve_user_count) section_twelve_three_remain,SUM(section_twelve_user_count_seven_remain)/SUM(section_twelve_user_count) section_twelve_seven_remain,
		SUM(section_twelve_user_count_fourteen_remain)/SUM(section_twelve_user_count) section_twelve_fourteen_remain,SUM(section_twelve_user_count_thirty_remain)/SUM(section_twelve_user_count) section_twelve_thirty_remain,SUM(section_twelve_user_count_sixty_remain)/SUM(section_twelve_user_count) section_twelve_sixty_remain,
		
		SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_user_count,
		SUM(section_thirteen_user_count_two_remain+section_fourteen_user_count_two_remain+section_fifteen_user_count_two_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_two_remain,
		SUM(section_thirteen_user_count_three_remain+section_fourteen_user_count_three_remain+section_fifteen_user_count_three_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_three_remain,
		SUM(section_thirteen_user_count_seven_remain+section_fourteen_user_count_seven_remain+section_fifteen_user_count_seven_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_seven_remain,
		SUM(section_thirteen_user_count_fourteen_remain+section_fourteen_user_count_fourteen_remain+section_fifteen_user_count_fourteen_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_fourteen_remain,
		SUM(section_thirteen_user_count_thirty_remain+section_fourteen_user_count_thirty_remain+section_fifteen_user_count_thirty_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_thirty_remain,
		SUM(section_thirteen_user_count_sixty_remain+section_fourteen_user_count_sixty_remain+section_fifteen_user_count_sixty_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_sixty_remain,
		
		SUM(active_user_count)-SUM(section_zero_user_count) pay_user_count,(SUM(active_user_count_two_remain)-SUM(section_zero_two_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(section_zero_three_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(section_zero_seven_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(section_zero_fourteen_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(section_zero_thirty_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(section_zero_sixty_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_sixty_remain,
		SUM(active_user_count)-SUM(new_user_count) old_pay_user_count,(SUM(active_user_count_two_remain)-SUM(new_user_count_two_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(new_user_count_three_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(new_user_count_seven_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(new_user_count_fourteen_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(new_user_count_thirty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(new_user_count_sixty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_sixty_remain
		<if test="group != null and group != ''">
			,${group}
		</if>
		FROM ads_total_pay_user_remain_daily WHERE create_date BETWEEN #{startTime} AND #{endTime}
		<if test="appid != null and appid != ''">
			AND appid in (
			${appid}
			)
		</if>
		<if test="group != null and group != ''">
			GROUP BY ${group}
		</if>
		<if test="order != null and order != ''">
			ORDER BY ${order}
		</if>
	</select>

	<select id="selectTotalSumPayUserRetentionList" resultType="java.util.Map">
		SELECT
		SUM(active_user_count) active_user_count,SUM(active_user_count_two_remain)/SUM(active_user_count) active_user_count_two_remain,SUM(active_user_count_three_remain)/SUM(active_user_count) active_user_count_three_remain,SUM(active_user_count_seven_remain)/SUM(active_user_count) active_user_count_seven_remain,
		SUM(active_user_count_fourteen_remain)/SUM(active_user_count) active_user_count_fourteen_remain,SUM(active_user_count_thirty_remain)/SUM(active_user_count) active_user_count_thirty_remain,SUM(active_user_count_sixty_remain)/SUM(active_user_count) active_user_count_sixty_remain,
		SUM(new_user_count) new_user_count,SUM(new_user_count_two_remain)/SUM(new_user_count) new_user_count_two_remain,SUM(new_user_count_three_remain)/SUM(new_user_count) new_user_count_three_remain,SUM(new_user_count_seven_remain)/SUM(new_user_count) new_user_count_seven_remain,
		SUM(new_user_count_fourteen_remain)/SUM(new_user_count) new_user_count_fourteen_remain,SUM(new_user_count_thirty_remain)/SUM(new_user_count) new_user_count_thirty_remain,SUM(new_user_count_sixty_remain)/SUM(new_user_count) new_user_count_sixty_remain,
		SUM(section_zero_user_count) section_zero_user_count,SUM(section_zero_two_remain)/SUM(section_zero_user_count) section_zero_two_remain,SUM(section_zero_three_remain)/SUM(section_zero_user_count) section_zero_three_remain,SUM(section_zero_seven_remain)/SUM(section_zero_user_count) section_zero_seven_remain,
		SUM(section_zero_fourteen_remain)/SUM(section_zero_user_count) section_zero_fourteen_remain,SUM(section_zero_thirty_remain)/SUM(section_zero_user_count) section_zero_thirty_remain,SUM(section_zero_sixty_remain)/SUM(section_zero_user_count) section_zero_sixty_remain,
		
		SUM(section_one_user_count+section_two_user_count) section_one_user_count,
		SUM(section_one_two_remain+section_two_two_remain)/SUM(section_one_user_count+section_two_user_count) section_one_two_remain,
		SUM(section_one_three_remain+section_two_three_remain)/SUM(section_one_user_count+section_two_user_count) section_one_three_remain,
		SUM(section_one_seven_remain+section_two_seven_remain)/SUM(section_one_user_count+section_two_user_count) section_one_seven_remain,
		SUM(section_one_fourteen_remain+section_two_fourteen_remain)/SUM(section_one_user_count+section_two_user_count) section_one_fourteen_remain,
		SUM(section_one_thirty_remain+section_two_thirty_remain)/SUM(section_one_user_count+section_two_user_count) section_one_thirty_remain,
		SUM(section_one_sixty_remain+section_two_sixty_remain)/SUM(section_one_user_count+section_two_user_count) section_one_sixty_remain,
		
		SUM(section_three_user_count) section_three_user_count,SUM(section_three_two_remain)/SUM(section_three_user_count) section_three_two_remain,SUM(section_three_three_remain)/SUM(section_three_user_count) section_three_three_remain,SUM(section_three_seven_remain)/SUM(section_three_user_count) section_three_seven_remain,
		SUM(section_three_fourteen_remain)/SUM(section_three_user_count) section_three_fourteen_remain,SUM(section_three_thirty_remain)/SUM(section_three_user_count) section_three_thirty_remain,SUM(section_three_sixty_remain)/SUM(section_three_user_count) section_three_sixty_remain,
		SUM(section_four_user_count) section_four_user_count,SUM(section_four_two_remain)/SUM(section_four_user_count) section_four_two_remain,SUM(section_four_three_remain)/SUM(section_four_user_count) section_four_three_remain,SUM(section_four_seven_remain)/SUM(section_four_user_count) section_four_seven_remain,
		SUM(section_four_fourteen_remain)/SUM(section_four_user_count) section_four_fourteen_remain,SUM(section_four_thirty_remain)/SUM(section_four_user_count) section_four_thirty_remain,SUM(section_four_sixty_remain)/SUM(section_four_user_count) section_four_sixty_remain,
		
		SUM(section_five_user_count+section_six_user_count) section_five_user_count,
		SUM(section_five_two_remain+section_six_two_remain)/SUM(section_five_user_count+section_six_user_count) section_five_two_remain,
		SUM(section_five_three_remain+section_six_three_remain)/SUM(section_five_user_count+section_six_user_count) section_five_three_remain,
		SUM(section_five_seven_remain+section_six_seven_remain)/SUM(section_five_user_count+section_six_user_count) section_five_seven_remain,
		SUM(section_five_fourteen_remain+section_six_fourteen_remain)/SUM(section_five_user_count+section_six_user_count) section_five_fourteen_remain,
		SUM(section_five_thirty_remain+section_six_thirty_remain)/SUM(section_five_user_count+section_six_user_count) section_five_thirty_remain,
		SUM(section_five_sixty_remain+section_six_sixty_remain)/SUM(section_five_user_count+section_six_user_count) section_five_sixty_remain,
		
		SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_user_count,
		SUM(section_nine_two_remain+section_seven_two_remain+section_eight_two_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_two_remain,
		SUM(section_nine_three_remain+section_seven_three_remain+section_eight_three_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_three_remain,
		SUM(section_nine_seven_remain+section_seven_seven_remain+section_eight_seven_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_seven_remain,
		SUM(section_nine_fourteen_remain+section_seven_fourteen_remain+section_eight_fourteen_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_fourteen_remain,
		SUM(section_nine_thirty_remain+section_seven_thirty_remain+section_eight_thirty_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_thirty_remain,
		SUM(section_nine_sixty_remain+section_seven_sixty_remain+section_eight_sixty_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_sixty_remain,
		
		SUM(section_ten_user_count) section_ten_user_count,SUM(section_ten_two_remain)/SUM(section_ten_user_count) section_ten_two_remain,SUM(section_ten_three_remain)/SUM(section_ten_user_count) section_ten_three_remain,SUM(section_ten_seven_remain)/SUM(section_ten_user_count) section_ten_seven_remain,
		SUM(section_ten_fourteen_remain)/SUM(section_ten_user_count) section_ten_fourteen_remain,SUM(section_ten_thirty_remain)/SUM(section_ten_user_count) section_ten_thirty_remain,SUM(section_ten_sixty_remain)/SUM(section_ten_user_count) section_ten_sixty_remain,
		SUM(section_eleven_user_count) section_eleven_user_count,SUM(section_eleven_user_count_two_remain)/SUM(section_eleven_user_count) section_eleven_two_remain,SUM(section_eleven_user_count_three_remain)/SUM(section_eleven_user_count) section_eleven_three_remain,SUM(section_eleven_user_count_seven_remain)/SUM(section_eleven_user_count) section_eleven_seven_remain,
		SUM(section_eleven_user_count_fourteen_remain)/SUM(section_eleven_user_count) section_eleven_fourteen_remain,SUM(section_eleven_user_count_thirty_remain)/SUM(section_eleven_user_count) section_eleven_thirty_remain,SUM(section_eleven_user_count_sixty_remain)/SUM(section_eleven_user_count) section_eleven_sixty_remain,
		SUM(section_twelve_user_count) section_twelve_user_count,SUM(section_twelve_user_count_two_remain)/SUM(section_twelve_user_count) section_twelve_two_remain,SUM(section_twelve_user_count_three_remain)/SUM(section_twelve_user_count) section_twelve_three_remain,SUM(section_twelve_user_count_seven_remain)/SUM(section_twelve_user_count) section_twelve_seven_remain,
		SUM(section_twelve_user_count_fourteen_remain)/SUM(section_twelve_user_count) section_twelve_fourteen_remain,SUM(section_twelve_user_count_thirty_remain)/SUM(section_twelve_user_count) section_twelve_thirty_remain,SUM(section_twelve_user_count_sixty_remain)/SUM(section_twelve_user_count) section_twelve_sixty_remain,
		
		SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_user_count,
		SUM(section_thirteen_user_count_two_remain+section_fourteen_user_count_two_remain+section_fifteen_user_count_two_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_two_remain,
		SUM(section_thirteen_user_count_three_remain+section_fourteen_user_count_three_remain+section_fifteen_user_count_three_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_three_remain,
		SUM(section_thirteen_user_count_seven_remain+section_fourteen_user_count_seven_remain+section_fifteen_user_count_seven_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_seven_remain,
		SUM(section_thirteen_user_count_fourteen_remain+section_fourteen_user_count_fourteen_remain+section_fifteen_user_count_fourteen_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_fourteen_remain,
		SUM(section_thirteen_user_count_thirty_remain+section_fourteen_user_count_thirty_remain+section_fifteen_user_count_thirty_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_thirty_remain,
		SUM(section_thirteen_user_count_sixty_remain+section_fourteen_user_count_sixty_remain+section_fifteen_user_count_sixty_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_sixty_remain,
		
		SUM(active_user_count)-SUM(section_zero_user_count) pay_user_count,(SUM(active_user_count_two_remain)-SUM(section_zero_two_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(section_zero_three_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(section_zero_seven_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(section_zero_fourteen_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(section_zero_thirty_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(section_zero_sixty_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_sixty_remain,
		SUM(active_user_count)-SUM(new_user_count) old_pay_user_count,(SUM(active_user_count_two_remain)-SUM(new_user_count_two_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(new_user_count_three_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(new_user_count_seven_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(new_user_count_fourteen_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(new_user_count_thirty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(new_user_count_sixty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_sixty_remain
		FROM ads_total_pay_user_remain_daily WHERE create_date BETWEEN #{startTime} AND #{endTime}
		<if test="appid != null and appid != ''">
			AND appid in (
			${appid}
			)
		</if>
	</select>

	<select id="selectTotalChartPayUserRetentionList" resultType="java.util.Map">
		SELECT create_date,
		SUM(active_user_count) active_user_count,SUM(active_user_count_two_remain)/SUM(active_user_count) active_user_count_two_remain,SUM(active_user_count_three_remain)/SUM(active_user_count) active_user_count_three_remain,SUM(active_user_count_seven_remain)/SUM(active_user_count) active_user_count_seven_remain,
		SUM(active_user_count_fourteen_remain)/SUM(active_user_count) active_user_count_fourteen_remain,SUM(active_user_count_thirty_remain)/SUM(active_user_count) active_user_count_thirty_remain,SUM(active_user_count_sixty_remain)/SUM(active_user_count) active_user_count_sixty_remain,
		SUM(new_user_count) new_user_count,SUM(new_user_count_two_remain)/SUM(new_user_count) new_user_count_two_remain,SUM(new_user_count_three_remain)/SUM(new_user_count) new_user_count_three_remain,SUM(new_user_count_seven_remain)/SUM(new_user_count) new_user_count_seven_remain,
		SUM(new_user_count_fourteen_remain)/SUM(new_user_count) new_user_count_fourteen_remain,SUM(new_user_count_thirty_remain)/SUM(new_user_count) new_user_count_thirty_remain,SUM(new_user_count_sixty_remain)/SUM(new_user_count) new_user_count_sixty_remain,
		SUM(section_zero_user_count) section_zero_user_count,SUM(section_zero_two_remain)/SUM(section_zero_user_count) section_zero_two_remain,SUM(section_zero_three_remain)/SUM(section_zero_user_count) section_zero_three_remain,SUM(section_zero_seven_remain)/SUM(section_zero_user_count) section_zero_seven_remain,
		SUM(section_zero_fourteen_remain)/SUM(section_zero_user_count) section_zero_fourteen_remain,SUM(section_zero_thirty_remain)/SUM(section_zero_user_count) section_zero_thirty_remain,SUM(section_zero_sixty_remain)/SUM(section_zero_user_count) section_zero_sixty_remain,
		
		SUM(section_one_user_count+section_two_user_count) section_one_user_count,
		SUM(section_one_two_remain+section_two_two_remain)/SUM(section_one_user_count+section_two_user_count) section_one_two_remain,
		SUM(section_one_three_remain+section_two_three_remain)/SUM(section_one_user_count+section_two_user_count) section_one_three_remain,
		SUM(section_one_seven_remain+section_two_seven_remain)/SUM(section_one_user_count+section_two_user_count) section_one_seven_remain,
		SUM(section_one_fourteen_remain+section_two_fourteen_remain)/SUM(section_one_user_count+section_two_user_count) section_one_fourteen_remain,
		SUM(section_one_thirty_remain+section_two_thirty_remain)/SUM(section_one_user_count+section_two_user_count) section_one_thirty_remain,
		SUM(section_one_sixty_remain+section_two_sixty_remain)/SUM(section_one_user_count+section_two_user_count) section_one_sixty_remain,
		
		SUM(section_three_user_count) section_three_user_count,SUM(section_three_two_remain)/SUM(section_three_user_count) section_three_two_remain,SUM(section_three_three_remain)/SUM(section_three_user_count) section_three_three_remain,SUM(section_three_seven_remain)/SUM(section_three_user_count) section_three_seven_remain,
		SUM(section_three_fourteen_remain)/SUM(section_three_user_count) section_three_fourteen_remain,SUM(section_three_thirty_remain)/SUM(section_three_user_count) section_three_thirty_remain,SUM(section_three_sixty_remain)/SUM(section_three_user_count) section_three_sixty_remain,
		SUM(section_four_user_count) section_four_user_count,SUM(section_four_two_remain)/SUM(section_four_user_count) section_four_two_remain,SUM(section_four_three_remain)/SUM(section_four_user_count) section_four_three_remain,SUM(section_four_seven_remain)/SUM(section_four_user_count) section_four_seven_remain,
		SUM(section_four_fourteen_remain)/SUM(section_four_user_count) section_four_fourteen_remain,SUM(section_four_thirty_remain)/SUM(section_four_user_count) section_four_thirty_remain,SUM(section_four_sixty_remain)/SUM(section_four_user_count) section_four_sixty_remain,
		
		SUM(section_five_user_count+section_six_user_count) section_five_user_count,
		SUM(section_five_two_remain+section_six_two_remain)/SUM(section_five_user_count+section_six_user_count) section_five_two_remain,
		SUM(section_five_three_remain+section_six_three_remain)/SUM(section_five_user_count+section_six_user_count) section_five_three_remain,
		SUM(section_five_seven_remain+section_six_seven_remain)/SUM(section_five_user_count+section_six_user_count) section_five_seven_remain,
		SUM(section_five_fourteen_remain+section_six_fourteen_remain)/SUM(section_five_user_count+section_six_user_count) section_five_fourteen_remain,
		SUM(section_five_thirty_remain+section_six_thirty_remain)/SUM(section_five_user_count+section_six_user_count) section_five_thirty_remain,
		SUM(section_five_sixty_remain+section_six_sixty_remain)/SUM(section_five_user_count+section_six_user_count) section_five_sixty_remain,
		
		SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_user_count,
		SUM(section_nine_two_remain+section_seven_two_remain+section_eight_two_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_two_remain,
		SUM(section_nine_three_remain+section_seven_three_remain+section_eight_three_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_three_remain,
		SUM(section_nine_seven_remain+section_seven_seven_remain+section_eight_seven_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_seven_remain,
		SUM(section_nine_fourteen_remain+section_seven_fourteen_remain+section_eight_fourteen_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_fourteen_remain,
		SUM(section_nine_thirty_remain+section_seven_thirty_remain+section_eight_thirty_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_thirty_remain,
		SUM(section_nine_sixty_remain+section_seven_sixty_remain+section_eight_sixty_remain)/SUM(section_nine_user_count+section_seven_user_count+section_eight_user_count) section_seven_sixty_remain,
		
		SUM(section_ten_user_count) section_ten_user_count,SUM(section_ten_two_remain)/SUM(section_ten_user_count) section_ten_two_remain,SUM(section_ten_three_remain)/SUM(section_ten_user_count) section_ten_three_remain,SUM(section_ten_seven_remain)/SUM(section_ten_user_count) section_ten_seven_remain,
		SUM(section_ten_fourteen_remain)/SUM(section_ten_user_count) section_ten_fourteen_remain,SUM(section_ten_thirty_remain)/SUM(section_ten_user_count) section_ten_thirty_remain,SUM(section_ten_sixty_remain)/SUM(section_ten_user_count) section_ten_sixty_remain,
		SUM(section_eleven_user_count) section_eleven_user_count,SUM(section_eleven_user_count_two_remain)/SUM(section_eleven_user_count) section_eleven_two_remain,SUM(section_eleven_user_count_three_remain)/SUM(section_eleven_user_count) section_eleven_three_remain,SUM(section_eleven_user_count_seven_remain)/SUM(section_eleven_user_count) section_eleven_seven_remain,
		SUM(section_eleven_user_count_fourteen_remain)/SUM(section_eleven_user_count) section_eleven_fourteen_remain,SUM(section_eleven_user_count_thirty_remain)/SUM(section_eleven_user_count) section_eleven_thirty_remain,SUM(section_eleven_user_count_sixty_remain)/SUM(section_eleven_user_count) section_eleven_sixty_remain,
		SUM(section_twelve_user_count) section_twelve_user_count,SUM(section_twelve_user_count_two_remain)/SUM(section_twelve_user_count) section_twelve_two_remain,SUM(section_twelve_user_count_three_remain)/SUM(section_twelve_user_count) section_twelve_three_remain,SUM(section_twelve_user_count_seven_remain)/SUM(section_twelve_user_count) section_twelve_seven_remain,
		SUM(section_twelve_user_count_fourteen_remain)/SUM(section_twelve_user_count) section_twelve_fourteen_remain,SUM(section_twelve_user_count_thirty_remain)/SUM(section_twelve_user_count) section_twelve_thirty_remain,SUM(section_twelve_user_count_sixty_remain)/SUM(section_twelve_user_count) section_twelve_sixty_remain,
		
		SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_user_count,
		SUM(section_thirteen_user_count_two_remain+section_fourteen_user_count_two_remain+section_fifteen_user_count_two_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_two_remain,
		SUM(section_thirteen_user_count_three_remain+section_fourteen_user_count_three_remain+section_fifteen_user_count_three_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_three_remain,
		SUM(section_thirteen_user_count_seven_remain+section_fourteen_user_count_seven_remain+section_fifteen_user_count_seven_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_seven_remain,
		SUM(section_thirteen_user_count_fourteen_remain+section_fourteen_user_count_fourteen_remain+section_fifteen_user_count_fourteen_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_fourteen_remain,
		SUM(section_thirteen_user_count_thirty_remain+section_fourteen_user_count_thirty_remain+section_fifteen_user_count_thirty_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_thirty_remain,
		SUM(section_thirteen_user_count_sixty_remain+section_fourteen_user_count_sixty_remain+section_fifteen_user_count_sixty_remain)/SUM(section_thirteen_user_count+section_fourteen_user_count+section_fifteen_user_count) section_thirteen_sixty_remain,
		
		SUM(active_user_count)-SUM(section_zero_user_count) pay_user_count,(SUM(active_user_count_two_remain)-SUM(section_zero_two_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(section_zero_three_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(section_zero_seven_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(section_zero_fourteen_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(section_zero_thirty_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(section_zero_sixty_remain))/(SUM(active_user_count)-SUM(section_zero_user_count)) pay_user_count_sixty_remain,
		SUM(active_user_count)-SUM(new_user_count) old_pay_user_count,(SUM(active_user_count_two_remain)-SUM(new_user_count_two_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(new_user_count_three_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(new_user_count_seven_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(new_user_count_fourteen_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(new_user_count_thirty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(new_user_count_sixty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_sixty_remain
		FROM ads_total_pay_user_remain_daily WHERE create_date BETWEEN #{startTime} AND #{endTime}
		<if test="appid != null and appid != ''">
			AND appid in (
			${appid}
			)
		</if>
		GROUP BY create_date ORDER BY create_date ASC
	</select>

	<select id="getRoiList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.report.RoiVo">
		select  tdate,appid,sum(rebate_cost)/100 cost,sum(add_user) add_user,sum(active_user) active_user,
		sum(add_revenue_1)/100  add_revenue_1,
		sum(add_revenue_2)/100  add_revenue_2,
		sum(add_revenue_3)/100  add_revenue_3,
		sum(add_revenue_4)/100  add_revenue_4,
		sum(add_revenue_5)/100  add_revenue_5,
		sum(add_revenue_6)/100  add_revenue_6,
		sum(add_revenue_7)/100  add_revenue_7,
		sum(add_revenue_8)/100  add_revenue_8,
		sum(add_revenue_9)/100  add_revenue_9,
		sum(add_revenue_10)/100  add_revenue_10,
		sum(add_revenue_11)/100  add_revenue_11,
		sum(add_revenue_12)/100  add_revenue_12,
		sum(add_revenue_13)/100  add_revenue_13,
		sum(add_revenue_14)/100  add_revenue_14,
		sum(add_revenue_21)/100  add_revenue_21,
		sum(add_revenue_28)/100  add_revenue_28,
		sum(add_revenue_36)/100  add_revenue_36,
		sum(add_revenue_48)/100  add_revenue_48,
		sum(add_revenue_60)/100  add_revenue_60,
		sum(add_purchase_revenue_1)/100 add_purchase_revenue_1,
		sum(add_purchase_revenue_2)/100 add_purchase_revenue_2,
		sum(add_purchase_revenue_3)/100 add_purchase_revenue_3,
		sum(add_purchase_revenue_4)/100 add_purchase_revenue_4,
		sum(add_purchase_revenue_5)/100 add_purchase_revenue_5,
		sum(add_purchase_revenue_6)/100 add_purchase_revenue_6,
		sum(add_purchase_revenue_7)/100 add_purchase_revenue_7,
		sum(add_purchase_revenue_8)/100 add_purchase_revenue_8,
		sum(add_purchase_revenue_9)/100 add_purchase_revenue_9,
		sum(add_purchase_revenue_10)/100 add_purchase_revenue_10,
		sum(add_purchase_revenue_11)/100 add_purchase_revenue_11,
		sum(add_purchase_revenue_12)/100 add_purchase_revenue_12,
		sum(add_purchase_revenue_13)/100 add_purchase_revenue_13,
		sum(add_purchase_revenue_14)/100 add_purchase_revenue_14,
		sum(add_purchase_revenue_21)/100 add_purchase_revenue_21,
		sum(add_purchase_revenue_28)/100 add_purchase_revenue_28,
		sum(add_purchase_revenue_36)/100 add_purchase_revenue_36,
		sum(add_purchase_revenue_48)/100 add_purchase_revenue_48,
		sum(add_purchase_revenue_60)/100 add_purchase_revenue_60,

		IFNULL((IFNULL(sum(add_revenue_1),0.00)+IFNULL(sum(add_purchase_revenue_1),0.00))/100/sum(add_user),0.00) add_ltv_1,
		IFNULL((IFNULL(sum(add_revenue_2),0.00)+IFNULL(sum(add_purchase_revenue_2),0.00))/100/sum(add_user),0.00) add_ltv_2,
		IFNULL((IFNULL(sum(add_revenue_3),0.00)+IFNULL(sum(add_purchase_revenue_3),0.00))/100/sum(add_user),0.00) add_ltv_3,
		IFNULL((IFNULL(sum(add_revenue_4),0.00)+IFNULL(sum(add_purchase_revenue_4),0.00))/100/sum(add_user),0.00) add_ltv_4,
		IFNULL((IFNULL(sum(add_revenue_5),0.00)+IFNULL(sum(add_purchase_revenue_5),0.00))/100/sum(add_user),0.00) add_ltv_5,
		IFNULL((IFNULL(sum(add_revenue_6),0.00)+IFNULL(sum(add_purchase_revenue_6),0.00))/100/sum(add_user),0.00) add_ltv_6,
		IFNULL((IFNULL(sum(add_revenue_7),0.00)+IFNULL(sum(add_purchase_revenue_7),0.00))/100/sum(add_user),0.00) add_ltv_7,
		IFNULL((IFNULL(sum(add_revenue_8),0.00)+IFNULL(sum(add_purchase_revenue_8),0.00))/100/sum(add_user),0.00) add_ltv_8,
		IFNULL((IFNULL(sum(add_revenue_9),0.00)+IFNULL(sum(add_purchase_revenue_9),0.00))/100/sum(add_user),0.00) add_ltv_9,

		IFNULL((IFNULL(sum(add_revenue_10),0.00)+IFNULL(sum(add_purchase_revenue_10),0.00))/100/sum(add_user),0.00) add_ltv_10,
		IFNULL((IFNULL(sum(add_revenue_11),0.00)+IFNULL(sum(add_purchase_revenue_11),0.00))/100/sum(add_user),0.00) add_ltv_11,
		IFNULL((IFNULL(sum(add_revenue_12),0.00)+IFNULL(sum(add_purchase_revenue_12),0.00))/100/sum(add_user),0.00) add_ltv_12,
		IFNULL((IFNULL(sum(add_revenue_13),0.00)+IFNULL(sum(add_purchase_revenue_13),0.00))/100/sum(add_user),0.00) add_ltv_13,
		IFNULL((IFNULL(sum(add_revenue_14),0.00)+IFNULL(sum(add_purchase_revenue_14),0.00))/100/sum(add_user),0.00) add_ltv_14,
		IFNULL((IFNULL(sum(add_revenue_21),0.00)+IFNULL(sum(add_purchase_revenue_21),0.00))/100/sum(add_user),0.00) add_ltv_21,
		IFNULL((IFNULL(sum(add_revenue_28),0.00)+IFNULL(sum(add_purchase_revenue_28),0.00))/100/sum(add_user),0.00) add_ltv_28,
		IFNULL((IFNULL(sum(add_revenue_36),0.00)+IFNULL(sum(add_purchase_revenue_36),0.00))/100/sum(add_user),0.00) add_ltv_36,
		IFNULL((IFNULL(sum(add_revenue_48),0.00)+IFNULL(sum(add_purchase_revenue_48),0.00))/100/sum(add_user),0.00) add_ltv_48,
		IFNULL((IFNULL(sum(add_revenue_60),0.00)+IFNULL(sum(add_purchase_revenue_60),0.00))/100/sum(add_user),0.00) add_ltv_60,

		IFNULL((IFNULL(sum(add_revenue_1),0.00)+IFNULL(sum(add_purchase_revenue_1),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_1,
		IFNULL((IFNULL(sum(add_revenue_2),0.00)+IFNULL(sum(add_purchase_revenue_2),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_2,
		IFNULL((IFNULL(sum(add_revenue_3),0.00)+IFNULL(sum(add_purchase_revenue_3),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_3,
		IFNULL((IFNULL(sum(add_revenue_4),0.00)+IFNULL(sum(add_purchase_revenue_4),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_4,
		IFNULL((IFNULL(sum(add_revenue_5),0.00)+IFNULL(sum(add_purchase_revenue_5),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_5,
		IFNULL((IFNULL(sum(add_revenue_6),0.00)+IFNULL(sum(add_purchase_revenue_6),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_6,
		IFNULL((IFNULL(sum(add_revenue_7),0.00)+IFNULL(sum(add_purchase_revenue_7),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_7,
		IFNULL((IFNULL(sum(add_revenue_8),0.00)+IFNULL(sum(add_purchase_revenue_8),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_8,
		IFNULL((IFNULL(sum(add_revenue_9),0.00)+IFNULL(sum(add_purchase_revenue_9),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_9,

		IFNULL((IFNULL(sum(add_revenue_10),0.00)+IFNULL(sum(add_purchase_revenue_10),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_10,
		IFNULL((IFNULL(sum(add_revenue_11),0.00)+IFNULL(sum(add_purchase_revenue_11),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_11,
		IFNULL((IFNULL(sum(add_revenue_12),0.00)+IFNULL(sum(add_purchase_revenue_12),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_12,
		IFNULL((IFNULL(sum(add_revenue_13),0.00)+IFNULL(sum(add_purchase_revenue_13),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_13,
		IFNULL((IFNULL(sum(add_revenue_14),0.00)+IFNULL(sum(add_purchase_revenue_14),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_14,
		IFNULL((IFNULL(sum(add_revenue_21),0.00)+IFNULL(sum(add_purchase_revenue_21),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_21,
		IFNULL((IFNULL(sum(add_revenue_28),0.00)+IFNULL(sum(add_purchase_revenue_28),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_28,
		IFNULL((IFNULL(sum(add_revenue_36),0.00)+IFNULL(sum(add_purchase_revenue_36),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_36,
		IFNULL((IFNULL(sum(add_revenue_48),0.00)+IFNULL(sum(add_purchase_revenue_48),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_48,
		IFNULL((IFNULL(sum(add_revenue_60),0.00)+IFNULL(sum(add_purchase_revenue_60),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_60

		from dnwx_bi.ads_add_user_roi_daily_v2 where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		AND tdate <![CDATA[ >= ]]> #{start_date} and tdate <![CDATA[ <= ]]> #{end_date}
		<if test="group != null and group != ''">
			group by ${group}
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate asc
			</otherwise>
		</choose>
	</select>

    <select id="getRoiListChart" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.report.RoiVo">
		select  tdate,
        cast(IFNULL((IFNULL(sum(add_revenue_1),0.00)+IFNULL(sum(add_purchase_revenue_1),0.00))/100/(sum(rebate_cost)/100),0.0000) * 100 as decimal (18, 2)) add_roi_1,
        cast(IFNULL((IFNULL(sum(add_revenue_2),0.00)+IFNULL(sum(add_purchase_revenue_2),0.00))/100/(sum(rebate_cost)/100),0.0000) * 100 as decimal (18, 2)) add_roi_2,
        cast(IFNULL((IFNULL(sum(add_revenue_3),0.00)+IFNULL(sum(add_purchase_revenue_3),0.00))/100/(sum(rebate_cost)/100),0.0000) * 100 as decimal (18, 2)) add_roi_3,
        cast(IFNULL((IFNULL(sum(add_revenue_4),0.00)+IFNULL(sum(add_purchase_revenue_4),0.00))/100/(sum(rebate_cost)/100),0.0000) * 100 as decimal (18, 2)) add_roi_4,
        cast(IFNULL((IFNULL(sum(add_revenue_5),0.00)+IFNULL(sum(add_purchase_revenue_5),0.00))/100/(sum(rebate_cost)/100),0.0000) * 100 as decimal (18, 2)) add_roi_5,
        cast(IFNULL((IFNULL(sum(add_revenue_6),0.00)+IFNULL(sum(add_purchase_revenue_6),0.00))/100/(sum(rebate_cost)/100),0.0000) * 100 as decimal (18, 2)) add_roi_6,
        cast(IFNULL((IFNULL(sum(add_revenue_7),0.00)+IFNULL(sum(add_purchase_revenue_7),0.00))/100/(sum(rebate_cost)/100),0.0000) * 100 as decimal (18, 2)) add_roi_7,

        cast(IFNULL((IFNULL(sum(add_revenue_14),0.00)+IFNULL(sum(add_purchase_revenue_14),0.00))/100/(sum(rebate_cost)/100),0.0000) * 100 as decimal (18, 2)) add_roi_14,
        cast(IFNULL((IFNULL(sum(add_revenue_21),0.00)+IFNULL(sum(add_purchase_revenue_21),0.00))/100/(sum(rebate_cost)/100),0.0000) * 100 as decimal (18, 2)) add_roi_21,
        cast(IFNULL((IFNULL(sum(add_revenue_28),0.00)+IFNULL(sum(add_purchase_revenue_28),0.00))/100/(sum(rebate_cost)/100),0.0000) * 100 as decimal (18, 2)) add_roi_28,
        cast(IFNULL((IFNULL(sum(add_revenue_36),0.00)+IFNULL(sum(add_purchase_revenue_36),0.00))/100/(sum(rebate_cost)/100),0.0000) * 100 as decimal (18, 2)) add_roi_36,
        cast(IFNULL((IFNULL(sum(add_revenue_48),0.00)+IFNULL(sum(add_purchase_revenue_48),0.00))/100/(sum(rebate_cost)/100),0.0000) * 100 as decimal (18, 2)) add_roi_48,
        cast(IFNULL((IFNULL(sum(add_revenue_60),0.00)+IFNULL(sum(add_purchase_revenue_60),0.00))/100/(sum(rebate_cost)/100),0.0000) * 100 as decimal (18, 2)) add_roi_60

        from dnwx_bi.ads_add_user_roi_daily_v2 where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		AND tdate <![CDATA[ >= ]]> #{start_date} and tdate <![CDATA[ <= ]]> #{end_date}

        group by tdate

        order by tdate
	</select>

	<select id="getRoiSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.report.RoiVo">
		select  sum(rebate_cost)/100 cost,sum(add_user) add_user,sum(active_user) active_user,
		sum(add_revenue_1)/100  add_revenue_1,
		sum(add_revenue_2)/100  add_revenue_2,
		sum(add_revenue_3)/100  add_revenue_3,
		sum(add_revenue_4)/100  add_revenue_4,
		sum(add_revenue_5)/100  add_revenue_5,
		sum(add_revenue_6)/100  add_revenue_6,
		sum(add_revenue_7)/100  add_revenue_7,
		sum(add_revenue_8)/100  add_revenue_8,
		sum(add_revenue_9)/100  add_revenue_9,
		sum(add_revenue_10)/100  add_revenue_10,
		sum(add_revenue_11)/100  add_revenue_11,
		sum(add_revenue_12)/100  add_revenue_12,
		sum(add_revenue_13)/100  add_revenue_13,
		sum(add_revenue_14)/100  add_revenue_14,
		sum(add_revenue_21)/100  add_revenue_21,
		sum(add_revenue_28)/100  add_revenue_28,
		sum(add_revenue_36)/100  add_revenue_36,
		sum(add_revenue_48)/100  add_revenue_48,
		sum(add_revenue_60)/100  add_revenue_60,
		sum(add_purchase_revenue_1)/100 add_purchase_revenue_1,
		sum(add_purchase_revenue_2)/100 add_purchase_revenue_2,
		sum(add_purchase_revenue_3)/100 add_purchase_revenue_3,
		sum(add_purchase_revenue_4)/100 add_purchase_revenue_4,
		sum(add_purchase_revenue_5)/100 add_purchase_revenue_5,
		sum(add_purchase_revenue_6)/100 add_purchase_revenue_6,
		sum(add_purchase_revenue_7)/100 add_purchase_revenue_7,
		sum(add_purchase_revenue_8)/100 add_purchase_revenue_8,
		sum(add_purchase_revenue_9)/100 add_purchase_revenue_9,
		sum(add_purchase_revenue_10)/100 add_purchase_revenue_10,
		sum(add_purchase_revenue_11)/100 add_purchase_revenue_11,
		sum(add_purchase_revenue_12)/100 add_purchase_revenue_12,
		sum(add_purchase_revenue_13)/100 add_purchase_revenue_13,
		sum(add_purchase_revenue_14)/100 add_purchase_revenue_14,
		sum(add_purchase_revenue_21)/100 add_purchase_revenue_21,
		sum(add_purchase_revenue_28)/100 add_purchase_revenue_28,
		sum(add_purchase_revenue_36)/100 add_purchase_revenue_36,
		sum(add_purchase_revenue_48)/100 add_purchase_revenue_48,
		sum(add_purchase_revenue_60)/100 add_purchase_revenue_60,

		IFNULL((IFNULL(sum(add_revenue_1),0.00)+IFNULL(sum(add_purchase_revenue_1),0.00))/100/sum(add_user),0.00) add_ltv_1,
		IFNULL((IFNULL(sum(add_revenue_2),0.00)+IFNULL(sum(add_purchase_revenue_2),0.00))/100/sum(add_user),0.00) add_ltv_2,
		IFNULL((IFNULL(sum(add_revenue_3),0.00)+IFNULL(sum(add_purchase_revenue_3),0.00))/100/sum(add_user),0.00) add_ltv_3,
		IFNULL((IFNULL(sum(add_revenue_4),0.00)+IFNULL(sum(add_purchase_revenue_4),0.00))/100/sum(add_user),0.00) add_ltv_4,
		IFNULL((IFNULL(sum(add_revenue_5),0.00)+IFNULL(sum(add_purchase_revenue_5),0.00))/100/sum(add_user),0.00) add_ltv_5,
		IFNULL((IFNULL(sum(add_revenue_6),0.00)+IFNULL(sum(add_purchase_revenue_6),0.00))/100/sum(add_user),0.00) add_ltv_6,
		IFNULL((IFNULL(sum(add_revenue_7),0.00)+IFNULL(sum(add_purchase_revenue_7),0.00))/100/sum(add_user),0.00) add_ltv_7,
		IFNULL((IFNULL(sum(add_revenue_8),0.00)+IFNULL(sum(add_purchase_revenue_8),0.00))/100/sum(add_user),0.00) add_ltv_8,
		IFNULL((IFNULL(sum(add_revenue_9),0.00)+IFNULL(sum(add_purchase_revenue_9),0.00))/100/sum(add_user),0.00) add_ltv_9,

		IFNULL((IFNULL(sum(add_revenue_10),0.00)+IFNULL(sum(add_purchase_revenue_10),0.00))/100/sum(add_user),0.00) add_ltv_10,
		IFNULL((IFNULL(sum(add_revenue_11),0.00)+IFNULL(sum(add_purchase_revenue_11),0.00))/100/sum(add_user),0.00) add_ltv_11,
		IFNULL((IFNULL(sum(add_revenue_12),0.00)+IFNULL(sum(add_purchase_revenue_12),0.00))/100/sum(add_user),0.00) add_ltv_12,
		IFNULL((IFNULL(sum(add_revenue_13),0.00)+IFNULL(sum(add_purchase_revenue_13),0.00))/100/sum(add_user),0.00) add_ltv_13,
		IFNULL((IFNULL(sum(add_revenue_14),0.00)+IFNULL(sum(add_purchase_revenue_14),0.00))/100/sum(add_user),0.00) add_ltv_14,
		IFNULL((IFNULL(sum(add_revenue_21),0.00)+IFNULL(sum(add_purchase_revenue_21),0.00))/100/sum(add_user),0.00) add_ltv_21,
		IFNULL((IFNULL(sum(add_revenue_28),0.00)+IFNULL(sum(add_purchase_revenue_28),0.00))/100/sum(add_user),0.00) add_ltv_28,
		IFNULL((IFNULL(sum(add_revenue_36),0.00)+IFNULL(sum(add_purchase_revenue_36),0.00))/100/sum(add_user),0.00) add_ltv_36,
		IFNULL((IFNULL(sum(add_revenue_48),0.00)+IFNULL(sum(add_purchase_revenue_48),0.00))/100/sum(add_user),0.00) add_ltv_48,
		IFNULL((IFNULL(sum(add_revenue_60),0.00)+IFNULL(sum(add_purchase_revenue_60),0.00))/100/sum(add_user),0.00) add_ltv_60,

		IFNULL((IFNULL(sum(add_revenue_1),0.00)+IFNULL(sum(add_purchase_revenue_1),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_1,
		IFNULL((IFNULL(sum(add_revenue_2),0.00)+IFNULL(sum(add_purchase_revenue_2),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_2,
		IFNULL((IFNULL(sum(add_revenue_3),0.00)+IFNULL(sum(add_purchase_revenue_3),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_3,
		IFNULL((IFNULL(sum(add_revenue_4),0.00)+IFNULL(sum(add_purchase_revenue_4),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_4,
		IFNULL((IFNULL(sum(add_revenue_5),0.00)+IFNULL(sum(add_purchase_revenue_5),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_5,
		IFNULL((IFNULL(sum(add_revenue_6),0.00)+IFNULL(sum(add_purchase_revenue_6),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_6,
		IFNULL((IFNULL(sum(add_revenue_7),0.00)+IFNULL(sum(add_purchase_revenue_7),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_7,
		IFNULL((IFNULL(sum(add_revenue_8),0.00)+IFNULL(sum(add_purchase_revenue_8),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_8,
		IFNULL((IFNULL(sum(add_revenue_9),0.00)+IFNULL(sum(add_purchase_revenue_9),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_9,

		IFNULL((IFNULL(sum(add_revenue_10),0.00)+IFNULL(sum(add_purchase_revenue_10),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_10,
		IFNULL((IFNULL(sum(add_revenue_11),0.00)+IFNULL(sum(add_purchase_revenue_11),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_11,
		IFNULL((IFNULL(sum(add_revenue_12),0.00)+IFNULL(sum(add_purchase_revenue_12),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_12,
		IFNULL((IFNULL(sum(add_revenue_13),0.00)+IFNULL(sum(add_purchase_revenue_13),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_13,
		IFNULL((IFNULL(sum(add_revenue_14),0.00)+IFNULL(sum(add_purchase_revenue_14),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_14,
		IFNULL((IFNULL(sum(add_revenue_21),0.00)+IFNULL(sum(add_purchase_revenue_21),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_21,
		IFNULL((IFNULL(sum(add_revenue_28),0.00)+IFNULL(sum(add_purchase_revenue_28),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_28,
		IFNULL((IFNULL(sum(add_revenue_36),0.00)+IFNULL(sum(add_purchase_revenue_36),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_36,
		IFNULL((IFNULL(sum(add_revenue_48),0.00)+IFNULL(sum(add_purchase_revenue_48),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_48,
		IFNULL((IFNULL(sum(add_revenue_60),0.00)+IFNULL(sum(add_purchase_revenue_60),0.00))/100/(sum(rebate_cost)/100),0.0000) add_roi_60

		from dnwx_bi.ads_add_user_roi_daily_v2 where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		AND tdate <![CDATA[ >= ]]> #{start_date} and tdate <![CDATA[ <= ]]> #{end_date}
	</select>

    <select id="selectAdsDauAndAdd" resultType="com.wbgame.pojo.redpack.HbWithdrawQuery">
        select concat(${group}) mapkey,sum(new_users) add_num,sum(act_users) act_num
        from ads_dim_users_info_4d_hourly
		where tdate between #{startTime} and #{endTime}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="channel != null and channel != ''">
			and download_channel in (${channel})
		</if>
		group by ${group}
	</select>
	<select id="selectPayUserRetentionOversea" resultType="java.util.Map">
		SELECT
		country,SUM(active_user_count) active_user_count,SUM(active_user_count_two_remain)/SUM(active_user_count) active_user_count_two_remain,SUM(active_user_count_three_remain)/SUM(active_user_count) active_user_count_three_remain,SUM(active_user_count_seven_remain)/SUM(active_user_count) active_user_count_seven_remain,
		SUM(active_user_count_fourteen_remain)/SUM(active_user_count) active_user_count_fourteen_remain,SUM(active_user_count_thirty_remain)/SUM(active_user_count) active_user_count_thirty_remain,SUM(active_user_count_sixty_remain)/SUM(active_user_count) active_user_count_sixty_remain,
		SUM(new_user_count) new_user_count,SUM(new_user_count_two_remain)/SUM(new_user_count) new_user_count_two_remain,SUM(new_user_count_three_remain)/SUM(new_user_count) new_user_count_three_remain,SUM(new_user_count_seven_remain)/SUM(new_user_count) new_user_count_seven_remain,
		SUM(new_user_count_fourteen_remain)/SUM(new_user_count) new_user_count_fourteen_remain,SUM(new_user_count_thirty_remain)/SUM(new_user_count) new_user_count_thirty_remain,SUM(new_user_count_sixty_remain)/SUM(new_user_count) new_user_count_sixty_remain,
		SUM(active_user_count)-SUM(new_user_count) old_pay_user_count,(SUM(active_user_count_two_remain)-SUM(new_user_count_two_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(new_user_count_three_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(new_user_count_seven_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(new_user_count_fourteen_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(new_user_count_thirty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(new_user_count_sixty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_sixty_remain,
		IFNULL(SUM(first_pay_count),0) first_pay_count,SUM(first_pay_two_remain)/SUM(first_pay_count) first_pay_two_remain,SUM(first_pay_three_remain)/SUM(first_pay_count) first_pay_three_remain,SUM(first_pay_seven_remain)/SUM(first_pay_count) first_pay_seven_remain,SUM(first_pay_fourteen_remain)/SUM(first_pay_count) first_pay_fourteen_remain,
		SUM(first_pay_thirty_remain)/SUM(first_pay_count) first_pay_thirty_remain,SUM(first_pay_sixty_remain)/SUM(first_pay_count) first_pay_sixty_remain,
		IFNULL(SUM(new_first_pay_count),0) new_first_pay_count,SUM(new_first_pay_two_remain)/SUM(new_first_pay_count) new_first_pay_two_remain,SUM(new_first_pay_three_remain)/SUM(new_first_pay_count) new_first_pay_three_remain,SUM(new_first_pay_seven_remain)/SUM(new_first_pay_count) new_first_pay_seven_remain,SUM(new_first_pay_fourteen_remain)/SUM(new_first_pay_count) new_first_pay_fourteen_remain,
		SUM(new_first_pay_thirty_remain)/SUM(new_first_pay_count) new_first_pay_thirty_remain,SUM(new_first_pay_sixty_remain)/SUM(new_first_pay_count) new_first_pay_sixty_remain,
		sum(day0_retention_users)/sum(reg_users)  d0_retention_rate,sum(stage_one_valid_users)  stage_one_valid_users,sum(stage_one_retention_users)  stage_one_retention_users,
		sum(stage_two_retention_users)  stage_two_retention_users ,sum(stage_one_retention_users)/sum(stage_one_valid_users) rank_1_entry_ratio,
		sum(stage_two_retention_users)/sum(stage_one_retention_users) rank_2_entry_ratio
		<if test="group != null and group != ''">
			,${group}
		</if>
		FROM (
			SELECT
				tdate as create_date,
				appid,
				country,
				SUM( active_user_cnt) active_user_count,
				SUM( active_retention_1day_user_cnt ) active_user_count_two_remain,
				SUM( active_retention_3day_user_cnt ) active_user_count_three_remain,
				SUM( active_retention_7day_user_cnt ) active_user_count_seven_remain,
				SUM( active_retention_14day_user_cnt ) active_user_count_fourteen_remain,
				SUM( active_retention_30day_user_cnt ) active_user_count_thirty_remain,
				SUM( active_retention_60day_user_cnt ) active_user_count_sixty_remain,
				SUM( reg_user_cnt ) new_user_count,
				SUM( reg_retention_1day_user_cnt ) new_user_count_two_remain,
				SUM( reg_retention_3day_user_cnt ) new_user_count_three_remain,
				SUM( reg_retention_7day_user_cnt ) new_user_count_seven_remain,
				SUM( reg_retention_14day_user_cnt ) new_user_count_fourteen_remain,
				SUM( reg_retention_30day_user_cnt ) new_user_count_thirty_remain,
				SUM( reg_retention_60day_user_cnt ) new_user_count_sixty_remain
			FROM
				ads_pay_user_remain_daily_oversea
			WHERE
				tdate BETWEEN #{startTime} AND #{endTime}
			<if test="country != null and country != ''">
				AND country IN (${country})
			</if>
			GROUP BY
				tdate,appid,country
		) a
		LEFT JOIN (
		SELECT tdate,appid AS bappid,sum(first_count) first_pay_count,sum(two_remain) first_pay_two_remain,sum(three_remain) first_pay_three_remain,
		sum(seven_remain) first_pay_seven_remain,sum(fourteen_remain) first_pay_fourteen_remain,sum(thirty_remain) first_pay_thirty_remain,sum(sixty_remain) first_pay_sixty_remain
		FROM ads_first_pay_user_remain_daily WHERE tdate BETWEEN #{startTime} AND #{endTime}
		GROUP BY tdate,appid
		) b on a.create_date = b.tdate AND a.appid = b.bappid
		LEFT JOIN (
		SELECT tdate,appid AS cappid,sum(first_count) new_first_pay_count,sum(two_remain) new_first_pay_two_remain,sum(three_remain) new_first_pay_three_remain,
		sum(seven_remain) new_first_pay_seven_remain,sum(fourteen_remain) new_first_pay_fourteen_remain,sum(thirty_remain) new_first_pay_thirty_remain,sum(sixty_remain) new_first_pay_sixty_remain
		FROM ads_first_pay_user_remain_daily WHERE tdate BETWEEN #{startTime} AND #{endTime} AND user_type=1
		GROUP BY tdate,appid
		) c on a.create_date = c.tdate AND a.appid = c.cappid
		LEFT JOIN (
		SELECT tdate,appid AS dappid,sum(day0_retention_users)  day0_retention_users,sum(stage_one_valid_users)  stage_one_valid_users,sum(reg_users) reg_users,
		sum(stage_one_retention_users)  stage_one_retention_users,sum(stage_two_retention_users)  stage_two_retention_users
		from ads_user_login_rate_daily WHERE tdate BETWEEN #{startTime} AND #{endTime} GROUP BY tdate,appid
		) d on a.create_date = d.tdate AND a.appid = d.dappid
		WHERE 1=1
		<if test="appid != null and appid != ''">
			AND appid in (
			${appid}
			)
		</if>
		<if test="group != null and group != ''">
			GROUP BY ${group}
		</if>
		<if test="order != null and order != ''">
			ORDER BY ${order}
		</if>
	</select>
	<select id="selectSumPayUserRetentionOversea" resultType="java.util.Map">
		SELECT
		country,SUM(active_user_count) active_user_count,SUM(active_user_count_two_remain)/SUM(active_user_count) active_user_count_two_remain,SUM(active_user_count_three_remain)/SUM(active_user_count) active_user_count_three_remain,SUM(active_user_count_seven_remain)/SUM(active_user_count) active_user_count_seven_remain,
		SUM(active_user_count_fourteen_remain)/SUM(active_user_count) active_user_count_fourteen_remain,SUM(active_user_count_thirty_remain)/SUM(active_user_count) active_user_count_thirty_remain,SUM(active_user_count_sixty_remain)/SUM(active_user_count) active_user_count_sixty_remain,
		SUM(new_user_count) new_user_count,SUM(new_user_count_two_remain)/SUM(new_user_count) new_user_count_two_remain,SUM(new_user_count_three_remain)/SUM(new_user_count) new_user_count_three_remain,SUM(new_user_count_seven_remain)/SUM(new_user_count) new_user_count_seven_remain,
		SUM(new_user_count_fourteen_remain)/SUM(new_user_count) new_user_count_fourteen_remain,SUM(new_user_count_thirty_remain)/SUM(new_user_count) new_user_count_thirty_remain,SUM(new_user_count_sixty_remain)/SUM(new_user_count) new_user_count_sixty_remain,
		SUM(active_user_count)-SUM(new_user_count) old_pay_user_count,(SUM(active_user_count_two_remain)-SUM(new_user_count_two_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(new_user_count_three_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(new_user_count_seven_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(new_user_count_fourteen_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(new_user_count_thirty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(new_user_count_sixty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_sixty_remain,
		IFNULL(SUM(first_pay_count),0) first_pay_count,SUM(first_pay_two_remain)/SUM(first_pay_count) first_pay_two_remain,SUM(first_pay_three_remain)/SUM(first_pay_count) first_pay_three_remain,SUM(first_pay_seven_remain)/SUM(first_pay_count) first_pay_seven_remain,SUM(first_pay_fourteen_remain)/SUM(first_pay_count) first_pay_fourteen_remain,
		SUM(first_pay_thirty_remain)/SUM(first_pay_count) first_pay_thirty_remain,SUM(first_pay_sixty_remain)/SUM(first_pay_count) first_pay_sixty_remain,
		IFNULL(SUM(new_first_pay_count),0) new_first_pay_count,SUM(new_first_pay_two_remain)/SUM(new_first_pay_count) new_first_pay_two_remain,SUM(new_first_pay_three_remain)/SUM(new_first_pay_count) new_first_pay_three_remain,SUM(new_first_pay_seven_remain)/SUM(new_first_pay_count) new_first_pay_seven_remain,SUM(new_first_pay_fourteen_remain)/SUM(new_first_pay_count) new_first_pay_fourteen_remain,
		SUM(new_first_pay_thirty_remain)/SUM(new_first_pay_count) new_first_pay_thirty_remain,SUM(new_first_pay_sixty_remain)/SUM(new_first_pay_count) new_first_pay_sixty_remain,
		sum(day0_retention_users)/sum(reg_users)  d0_retention_rate,sum(stage_one_valid_users)  stage_one_valid_users,sum(stage_one_retention_users)  stage_one_retention_users,
		sum(stage_two_retention_users)  stage_two_retention_users ,sum(stage_one_retention_users)/sum(stage_one_valid_users) rank_1_entry_ratio,
		sum(stage_two_retention_users)/sum(stage_one_retention_users) rank_2_entry_ratio
		FROM (
		SELECT
		tdate as create_date,appid,country,
		SUM( active_user_cnt) active_user_count,
		SUM( active_retention_1day_user_cnt ) active_user_count_two_remain,
		SUM( active_retention_3day_user_cnt ) active_user_count_three_remain,
		SUM( active_retention_7day_user_cnt ) active_user_count_seven_remain,
		SUM( active_retention_14day_user_cnt ) active_user_count_fourteen_remain,
		SUM( active_retention_30day_user_cnt ) active_user_count_thirty_remain,
		SUM( active_retention_60day_user_cnt ) active_user_count_sixty_remain,
		SUM( reg_user_cnt ) new_user_count,
		SUM( reg_retention_1day_user_cnt ) new_user_count_two_remain,
		SUM( reg_retention_3day_user_cnt ) new_user_count_three_remain,
		SUM( reg_retention_7day_user_cnt ) new_user_count_seven_remain,
		SUM( reg_retention_14day_user_cnt ) new_user_count_fourteen_remain,
		SUM( reg_retention_30day_user_cnt ) new_user_count_thirty_remain,
		SUM( reg_retention_60day_user_cnt ) new_user_count_sixty_remain
		FROM
		ads_pay_user_remain_daily_oversea
		WHERE
		tdate BETWEEN #{startTime} AND #{endTime}
		<if test="country != null and country != ''">
			AND country IN (${country})
		</if>
		GROUP BY
		tdate,appid,country
		) a
		LEFT JOIN (
		SELECT tdate,appid AS bappid,sum(first_count) first_pay_count,sum(two_remain) first_pay_two_remain,sum(three_remain) first_pay_three_remain,
		sum(seven_remain) first_pay_seven_remain,sum(fourteen_remain) first_pay_fourteen_remain,sum(thirty_remain) first_pay_thirty_remain,sum(sixty_remain) first_pay_sixty_remain
		FROM ads_first_pay_user_remain_daily WHERE tdate BETWEEN #{startTime} AND #{endTime}
		GROUP BY tdate,appid
		) b on a.create_date = b.tdate AND a.appid = b.bappid
		LEFT JOIN (
		SELECT tdate,appid AS cappid,sum(first_count) new_first_pay_count,sum(two_remain) new_first_pay_two_remain,sum(three_remain) new_first_pay_three_remain,
		sum(seven_remain) new_first_pay_seven_remain,sum(fourteen_remain) new_first_pay_fourteen_remain,sum(thirty_remain) new_first_pay_thirty_remain,sum(sixty_remain) new_first_pay_sixty_remain
		FROM ads_first_pay_user_remain_daily WHERE tdate BETWEEN #{startTime} AND #{endTime} AND user_type=1
		GROUP BY tdate,appid
		) c on a.create_date = c.tdate AND a.appid = c.cappid
		LEFT JOIN (
		SELECT tdate,appid AS dappid,sum(day0_retention_users)  day0_retention_users,sum(stage_one_valid_users)  stage_one_valid_users,sum(reg_users) reg_users,
		sum(stage_one_retention_users)  stage_one_retention_users,sum(stage_two_retention_users)  stage_two_retention_users
		from ads_user_login_rate_daily WHERE tdate BETWEEN #{startTime} AND #{endTime} GROUP BY tdate,appid
		) d on a.create_date = d.tdate AND a.appid = d.dappid
		WHERE 1=1
		<if test="appid != null and appid != ''">
			AND appid in (
			${appid}
			)
		</if>
	</select>
	<select id="selectChartPayUserRetentionOversea" resultType="java.util.Map">
		SELECT create_date,
		country,SUM(active_user_count) active_user_count,SUM(active_user_count_two_remain)/SUM(active_user_count) active_user_count_two_remain,SUM(active_user_count_three_remain)/SUM(active_user_count) active_user_count_three_remain,SUM(active_user_count_seven_remain)/SUM(active_user_count) active_user_count_seven_remain,
		SUM(active_user_count_fourteen_remain)/SUM(active_user_count) active_user_count_fourteen_remain,SUM(active_user_count_thirty_remain)/SUM(active_user_count) active_user_count_thirty_remain,SUM(active_user_count_sixty_remain)/SUM(active_user_count) active_user_count_sixty_remain,
		SUM(new_user_count) new_user_count,SUM(new_user_count_two_remain)/SUM(new_user_count) new_user_count_two_remain,SUM(new_user_count_three_remain)/SUM(new_user_count) new_user_count_three_remain,SUM(new_user_count_seven_remain)/SUM(new_user_count) new_user_count_seven_remain,
		SUM(new_user_count_fourteen_remain)/SUM(new_user_count) new_user_count_fourteen_remain,SUM(new_user_count_thirty_remain)/SUM(new_user_count) new_user_count_thirty_remain,SUM(new_user_count_sixty_remain)/SUM(new_user_count) new_user_count_sixty_remain,
		SUM(active_user_count)-SUM(new_user_count) old_pay_user_count,(SUM(active_user_count_two_remain)-SUM(new_user_count_two_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_two_remain,(SUM(active_user_count_three_remain)-SUM(new_user_count_three_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_three_remain,
		(SUM(active_user_count_seven_remain)-SUM(new_user_count_seven_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_seven_remain,(SUM(active_user_count_fourteen_remain)-SUM(new_user_count_fourteen_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_fourteen_remain,
		(SUM(active_user_count_thirty_remain)-SUM(new_user_count_thirty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_thirty_remain,(SUM(active_user_count_sixty_remain)-SUM(new_user_count_sixty_remain))/(SUM(active_user_count)-SUM(new_user_count)) old_pay_user_count_sixty_remain,
		IFNULL(SUM(first_pay_count),0) first_pay_count,SUM(first_pay_two_remain)/SUM(first_pay_count) first_pay_two_remain,SUM(first_pay_three_remain)/SUM(first_pay_count) first_pay_three_remain,SUM(first_pay_seven_remain)/SUM(first_pay_count) first_pay_seven_remain,SUM(first_pay_fourteen_remain)/SUM(first_pay_count) first_pay_fourteen_remain,
		SUM(first_pay_thirty_remain)/SUM(first_pay_count) first_pay_thirty_remain,SUM(first_pay_sixty_remain)/SUM(first_pay_count) first_pay_sixty_remain,
		IFNULL(SUM(new_first_pay_count),0) new_first_pay_count,SUM(new_first_pay_two_remain)/SUM(new_first_pay_count) new_first_pay_two_remain,SUM(new_first_pay_three_remain)/SUM(new_first_pay_count) new_first_pay_three_remain,SUM(new_first_pay_seven_remain)/SUM(new_first_pay_count) new_first_pay_seven_remain,SUM(new_first_pay_fourteen_remain)/SUM(new_first_pay_count) new_first_pay_fourteen_remain,
		SUM(new_first_pay_thirty_remain)/SUM(new_first_pay_count) new_first_pay_thirty_remain,SUM(new_first_pay_sixty_remain)/SUM(new_first_pay_count) new_first_pay_sixty_remain,
		sum(day0_retention_users)/sum(reg_users)  d0_retention_rate,sum(stage_one_valid_users)  stage_one_valid_users,sum(stage_one_retention_users)  stage_one_retention_users,
		sum(stage_two_retention_users)  stage_two_retention_users ,sum(stage_one_retention_users)/sum(stage_one_valid_users) rank_1_entry_ratio,
		sum(stage_two_retention_users)/sum(stage_one_retention_users) rank_2_entry_ratio
		FROM (
		SELECT
		tdate as create_date,appid,country,
		SUM( active_user_cnt) active_user_count,
		SUM( active_retention_1day_user_cnt ) active_user_count_two_remain,
		SUM( active_retention_3day_user_cnt ) active_user_count_three_remain,
		SUM( active_retention_7day_user_cnt ) active_user_count_seven_remain,
		SUM( active_retention_14day_user_cnt ) active_user_count_fourteen_remain,
		SUM( active_retention_30day_user_cnt ) active_user_count_thirty_remain,
		SUM( active_retention_60day_user_cnt ) active_user_count_sixty_remain,
		SUM( reg_user_cnt ) new_user_count,
		SUM( reg_retention_1day_user_cnt ) new_user_count_two_remain,
		SUM( reg_retention_3day_user_cnt ) new_user_count_three_remain,
		SUM( reg_retention_7day_user_cnt ) new_user_count_seven_remain,
		SUM( reg_retention_14day_user_cnt ) new_user_count_fourteen_remain,
		SUM( reg_retention_30day_user_cnt ) new_user_count_thirty_remain,
		SUM( reg_retention_60day_user_cnt ) new_user_count_sixty_remain
		FROM
		ads_pay_user_remain_daily_oversea
		WHERE
		tdate BETWEEN #{startTime} AND #{endTime}
		<if test="country != null and country != ''">
			AND country IN (${country})
		</if>
		GROUP BY
		tdate,appid,country
		) a
		LEFT JOIN (
		SELECT tdate,appid AS bappid,sum(first_count) first_pay_count,sum(two_remain) first_pay_two_remain,sum(three_remain) first_pay_three_remain,
		sum(seven_remain) first_pay_seven_remain,sum(fourteen_remain) first_pay_fourteen_remain,sum(thirty_remain) first_pay_thirty_remain,sum(sixty_remain) first_pay_sixty_remain
		FROM ads_first_pay_user_remain_daily WHERE tdate BETWEEN #{startTime} AND #{endTime}
		GROUP BY tdate,appid
		) b on a.create_date = b.tdate AND a.appid = b.bappid
		LEFT JOIN (
		SELECT tdate,appid AS cappid,sum(first_count) new_first_pay_count,sum(two_remain) new_first_pay_two_remain,sum(three_remain) new_first_pay_three_remain,
		sum(seven_remain) new_first_pay_seven_remain,sum(fourteen_remain) new_first_pay_fourteen_remain,sum(thirty_remain) new_first_pay_thirty_remain,sum(sixty_remain) new_first_pay_sixty_remain
		FROM ads_first_pay_user_remain_daily WHERE tdate BETWEEN #{startTime} AND #{endTime} AND user_type=1
		GROUP BY tdate,appid
		) c on a.create_date = c.tdate AND a.appid = c.cappid
		LEFT JOIN (
		SELECT tdate,appid AS dappid,sum(day0_retention_users)  day0_retention_users,sum(stage_one_valid_users)  stage_one_valid_users,sum(reg_users) reg_users,
		sum(stage_one_retention_users)  stage_one_retention_users,sum(stage_two_retention_users)  stage_two_retention_users
		from ads_user_login_rate_daily WHERE tdate BETWEEN #{startTime} AND #{endTime} GROUP BY tdate,appid
		) d on a.create_date = d.tdate AND a.appid = d.dappid
		WHERE 1=1
		<if test="appid != null and appid != ''">
			AND appid in (
			${appid}
			)
		</if>
		<if test="order != null and order != ''">
			ORDER BY ${order}
		</if>
	</select>
</mapper>
