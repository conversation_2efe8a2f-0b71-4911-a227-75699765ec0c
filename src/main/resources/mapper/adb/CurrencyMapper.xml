<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.CurrencyMapper">

	<select id="getMicGameCurrencyList" parameterType="com.wbgame.pojo.game.report.query.MGCurrencyQueryVo" resultType="com.wbgame.pojo.game.report.MGCurrencyReportVo">
		select * from dnwx_bi.ads_wechat_token_change_daily where 1=1
		<if test="appid != null and appid.size != 0">
			and appid in
			<foreach collection="appid" index="index" item="appid"
					 open="(" separator="," close=")">
				#{appid}
			</foreach>
		</if>
		<if test="openid != null and openid != ''">
			and user_id = #{openid}
		</if>
		<if test="usercode != null and usercode != ''">
			and usercode = #{usercode}
		</if>
		<if test="token_type != null and token_type != ''">
			and token_type in (${token_type})
		</if>
		<if test="token_change_start != null and token_change_start != ''">
			and token_change <![CDATA[>=]]> ${token_change_start}
		</if>
		<if test="token_change_end != null and token_change_end != ''">
			and  token_change <![CDATA[<=]]> ${token_change_end}
		</if>
		<if test="money_start != null and money_start != ''">
			and money <![CDATA[>=]]> #{money_start}
		</if>
		<if test="money_end != null and money_end != ''">
			and  money <![CDATA[<=]]> #{money_end}
		</if>
		<if test="login_id != null and login_id != ''">
			and login_id = #{login_id}
		</if>
		<if test="game_id != null and game_id != ''">
			and game_id = #{game_id}
		</if>
		and t_date <![CDATA[>=]]> #{start_date} and t_date <![CDATA[<=]]> #{end_date}
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by t_date asc,ts desc
			</otherwise>
		</choose>
	</select>

	<select id="getMicGameCurrencyExport" parameterType="com.wbgame.pojo.game.report.query.MGCurrencyQueryVo" resultType="com.wbgame.pojo.game.report.MGCurrencyReportVo">
		select * from dnwx_bi.ads_wechat_token_change_daily where 1=1
		<if test="appid != null and appid.size != 0">
			and appid in
			<foreach collection="appid" index="index" item="appid"
					 open="(" separator="," close=")">
				#{appid}
			</foreach>
		</if>
		<if test="openid != null and openid != ''">
			and user_id = #{openid}
		</if>
		<if test="usercode != null and usercode != ''">
			and usercode = #{usercode}
		</if>
		<if test="token_type != null and token_type != ''">
			and token_type in (${token_type})
		</if>
		<if test="token_change_start != null and token_change_start != ''">
			and token_change <![CDATA[>=]]> ${token_change_start}
		</if>
		<if test="token_change_end != null and token_change_end != ''">
			and  token_change <![CDATA[<=]]> ${token_change_end}
		</if>
		<if test="money_start != null and money_start != ''">
			and money <![CDATA[>=]]> #{money_start}
		</if>
		<if test="money_end != null and money_end != ''">
			and  money <![CDATA[<=]]> #{money_end}
		</if>
		<if test="login_id != null and login_id != ''">
			and login_id = #{login_id} 
		</if>
		<if test="game_id != null and game_id != ''">
			and game_id = #{game_id}
		</if>
		and t_date <![CDATA[>=]]> #{start_date} and t_date <![CDATA[<=]]> #{end_date}
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by t_date asc,ts desc
			</otherwise>
		</choose>
		limit 100000
	</select>

</mapper>
