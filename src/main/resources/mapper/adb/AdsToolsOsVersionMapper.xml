<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsToolsOsVersionMapper">

    <select id="queryInsideVersions" resultType="java.lang.String">
        select distinct os_ver from ads_device_brand_model_os_reg_retention_daily
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                tdate between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="os_ver != null and os_ver != ''">
                and os_ver like concat('%',#{os_ver},'%')
            </if>
        </where>
        order by os_ver desc
    </select>

    <select id="queryInsideBrands" resultType="java.lang.String">
        select distinct lower(brand) from ads_device_brand_model_os_reg_retention_daily
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                tdate between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="brand != null and brand != ''">
                and lower(brand) like concat('%',#{brand},'%')
            </if>
        </where>
        order by lower(brand) desc
    </select>

    <select id="queryInsideModels" resultType="java.lang.String">
        select distinct lower(model) from ads_device_brand_model_os_reg_retention_daily
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                tdate between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="model != null and model != ''">
                and lower(model) like concat('%',#{model},'%')
            </if>
        </where>
        order by lower(model) desc
    </select>

    <select id="queryOutsideVersions" resultType="java.lang.String">
        select distinct os_ver from ads_device_brand_model_os_reg_retention_daily_oversea
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                reg_date between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="os_ver != null and os_ver != ''">
                and os_ver like concat('%',#{os_ver},'%')
            </if>
        </where>
        order by os_ver desc
    </select>


    <select id="queryOutsideBrands" resultType="java.lang.String">
        select distinct lower(brand) from ads_device_brand_model_os_reg_retention_daily_oversea
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                reg_date between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="brand != null and brand != ''">
                and lower(brand) like concat('%',#{brand},'%')
            </if>
        </where>
        order by lower(brand) desc
    </select>

    <select id="queryOutsideModels" resultType="java.lang.String">
        select distinct lower(model) from ads_device_brand_model_os_reg_retention_daily_oversea
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                reg_date between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="model != null and model != ''">
                and lower(model) like concat('%',#{model},'%')
            </if>
        </where>
        order by lower(model) desc
    </select>


    <select id="queryInsideList" resultType="com.wbgame.pojo.adv2.bigdata.AdsToolsOsVersionDataVo">
        SELECT
            a.tdate,
            a.appid,
            a.app_name,
            a.os_ver,
            os_ver_reg_users reg_users,
            round( os_ver_reg_users / total_reg_users * 100, 2 ) os_ver_rate
        FROM
        (
        SELECT
            tdate,
            app_name,
            appid,
            os_ver,
            sum( reg_user_cnt ) os_ver_reg_users
        FROM
            ads_device_brand_model_os_reg_retention_daily
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                tdate between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="os_ver != null and os_ver != ''">
                and os_ver in (${os_ver})
            </if>
        </where>
        GROUP BY
            tdate,
            appid,
            os_ver
        ) a
        LEFT JOIN (
        SELECT
            tdate tdate_b,
            appid appid_b,
            sum( reg_user_cnt ) total_reg_users
        FROM
            ads_device_brand_model_os_reg_retention_daily
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                tdate between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
        </where>
        GROUP BY
            tdate,
            appid
        ) b ON a.tdate = b.tdate_b AND a.appid= b.appid_b
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>


    <select id="queryOutsideList" resultType="com.wbgame.pojo.adv2.bigdata.AdsToolsOsVersionDataVo">
        SELECT
        a.reg_date tdate,
        a.appid,
        a.os_ver,
        os_ver_reg_users reg_users,
        round( os_ver_reg_users / total_reg_users * 100, 2 ) os_ver_rate
        FROM
        (
        SELECT
        reg_date,
        appid,
        os_ver,
        sum( reg_user_cnt ) os_ver_reg_users
        FROM
        ads_device_brand_model_os_reg_retention_daily_oversea
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                reg_date between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="os_ver != null and os_ver != ''">
                and os_ver in (${os_ver})
            </if>
        </where>
        GROUP BY
        reg_date,
        appid,
        os_ver
        ) a
        LEFT JOIN (
        SELECT
        reg_date,
        appid appid_b,
        sum( reg_user_cnt ) total_reg_users
        FROM
        ads_device_brand_model_os_reg_retention_daily_oversea
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                reg_date between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
        </where>
        GROUP BY
        reg_date,
        appid
        ) b ON a.reg_date = b.reg_date AND a.appid = b.appid_b
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>

    <select id="queryInsideRetentionList" resultType="com.wbgame.pojo.adv2.bigdata.AdsBrandRetentionDataVo">
        select
            <if test="group != null and group != ''">
                <if test="group.contains('tdate')">
                    tdate,
                </if>
                <if test="group.contains('appid')">
                    app_name,
                    appid,
                </if>
                <if test="group.contains('brand')">
                    lower(brand) brand,
                </if>
                <if test="group.contains('model')">
                    lower(model) model,
                </if>
                <if test="group.contains('os_ver')">
                    os_ver,
                </if>
            </if>
            sum(reg_user_cnt) reg_users,
            round(sum(reg_retention_1day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_1day,
            round(sum(reg_retention_2day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_2day,
            round(sum(reg_retention_3day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_3day,
            round(sum(reg_retention_4day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_4day,
            round(sum(reg_retention_5day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_5day,
            round(sum(reg_retention_6day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_6day,
            round(sum(reg_retention_14day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_14day,
            round(sum(reg_retention_30day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_30day,
            round(sum(reg_retention_60day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_60day
        from ads_device_brand_model_os_reg_retention_daily
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                tdate between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="os_ver != null and os_ver != ''">
                and os_ver in (${os_ver})
            </if>
            <if test="brand != null and brand != ''">
                and lower(brand) in (${brand})
            </if>
            <if test="model != null and model != ''">
                and lower(model) in (${model})
            </if>
        </where>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>

    <select id="queryOutsideRetentionList" resultType="com.wbgame.pojo.adv2.bigdata.AdsBrandRetentionDataVo">
        select
            <if test="group != null and group != ''">
                <if test="group.contains('tdate')">
                    reg_date tdate,
                </if>
                <if test="group.contains('appid')">
                    appid,
                </if>
                <if test="group.contains('brand')">
                    lower(brand) brand,
                </if>
                <if test="group.contains('model')">
                    lower(model) model,
                </if>
                <if test="group.contains('os_ver')">
                    os_ver,
                </if>
            </if>
            sum(reg_user_cnt) reg_users,
            round(sum(reg_retention_1day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_1day,
            round(sum(reg_retention_2day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_2day,
            round(sum(reg_retention_3day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_3day,
            round(sum(reg_retention_4day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_4day,
            round(sum(reg_retention_5day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_5day,
            round(sum(reg_retention_6day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_6day,
            round(sum(reg_retention_14day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_14day,
            round(sum(reg_retention_30day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_30day,
            round(sum(reg_retention_60day_user_cnt)/sum(reg_user_cnt)*100,2) reg_retention_60day
        from ads_device_brand_model_os_reg_retention_daily_oversea
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                reg_date between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="os_ver != null and os_ver != ''">
                and os_ver in (${os_ver})
            </if>
            <if test="brand != null and brand != ''">
                and lower(brand) in (${brand})
            </if>
            <if test="model != null and model != ''">
                and lower(model) in (${model})
            </if>
        </where>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>


</mapper>