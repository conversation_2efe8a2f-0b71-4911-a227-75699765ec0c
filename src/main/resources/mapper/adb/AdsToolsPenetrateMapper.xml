<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsToolsPenetrateMapper">

    <select id="queryList" resultType="com.wbgame.pojo.adv2.bigdata.AdsOutsideToolsPenetrateDataVo">
        select
        <if test="group != null and group != ''">
            ${group},
        </if>
        sum(dau) dau,
        sum(natural_uv) natural_uv,
        round(sum(topc_init)/sum(dau) * 100,2) topc_penetration,
        round(sum(cmode_init)/sum(dau) * 100,2) cmode_penetration,
        round(sum(bhmode_init)/sum(dau) * 100,2) bhmode_penetration,
        sum(news) news,
        sum(auto_bh_uv) auto_bh_uv,
        round(sum(creating_uv)/sum(dau) * 100,2) create_uv_penetration,
        round(sum(creating_pv)/sum(dau),2) create_pv_penetration,
        round(sum(created_pv)/sum(creating_pv) * 100,2) create_success_rate,
        round(sum(created_pv)/sum(dau),2) avg_popup,
        round(sum(game_ad_pv)/sum(dau),2) avg_game_ad_pv,
        round(sum(game_ad_pv)/sum(creating_pv) * 100,2) ad_pv_success_rate,
        round(sum(game_ad_uv)/sum(dau) * 100,2) ad_uv_penetration
        from ads_tools_agree_create_flow_daily
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                tdate between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="brand != null and brand != ''">
                and brand in (${brand})
            </if>
            <if test="os_version != null and os_version != ''">
                and os_version in (${os_version})
            </if>
            <if test="isnew != null">
                and isnew = #{isnew}
            </if>
        </where>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        HAVING sum(dau) IS NOT NULL
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>



    <select id="queryTotal" resultType="com.wbgame.pojo.adv2.bigdata.AdsOutsideToolsPenetrateDataVo">
        select
        ifnull(sum(dau),0) dau,
        ifnull(sum(natural_uv),0) natural_uv,
        ifnull(round(sum(topc_init)/sum(dau) * 100,2),0) topc_penetration,
        ifnull(round(sum(cmode_init)/sum(dau) * 100,2),0) cmode_penetration,
        ifnull(round(sum(bhmode_init)/sum(dau) * 100,2),0) bhmode_penetration,
        ifnull(sum(news),0) news,
        ifnull(sum(auto_bh_uv),0) auto_bh_uv,
        ifnull(round(sum(creating_uv)/sum(dau) * 100,2),0) create_uv_penetration,
        ifnull(round(sum(creating_pv)/sum(dau),2),0) create_pv_penetration,
        ifnull(round(sum(created_pv)/sum(creating_pv) * 100,2),0) create_success_rate,
        ifnull(round(sum(created_pv)/sum(dau),2),0) avg_popup,
        ifnull(round(sum(game_ad_pv)/sum(dau),2),0) avg_game_ad_pv,
        ifnull(round(sum(game_ad_pv)/sum(creating_pv) * 100,2),0) ad_pv_success_rate,
        ifnull(round(sum(game_ad_uv)/sum(dau) * 100,2),0) ad_uv_penetration
        from ads_tools_agree_create_flow_daily
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                tdate between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="brand != null and brand != ''">
                and brand in (${brand})
            </if>
            <if test="os_version != null and os_version != ''">
                and os_version in (${os_version})
            </if>
            <if test="isnew != null">
                and isnew = #{isnew}
            </if>
        </where>
    </select>

    <select id="queryBrands" resultType="java.lang.String">
        select distinct brand from ${tableName} order by brand
    </select>

    <select id="queryVersions" resultType="java.lang.String">
        select distinct os_version from ${tableName} order by os_version
    </select>

    <select id="queryInsideList" resultType="com.wbgame.pojo.adv2.bigdata.AdsInsideToolsPenetrateDataVo">
        select
        <if test="group != null and group != ''">
            ${group},
        </if>
        sum(dau) dau,
        sum(agree_true) agree_true,
        sum(agree_show) agree_show,
        round(sum(agree_click)/sum(agree_show) * 100,2) agree_penetration,
        round(sum(app_auto_init)/sum(agree_show) * 100,2) init_penetration,
        round(sum(b_alive_first_start)/sum(agree_show) * 100,2) first_start_penetration,
        sum(app_auto_init) app_auto_init,
        sum(b_alive_first_start) b_alive_first_start,
        round(sum(creating_uv)/sum(agree_true) * 100,2) create_uv_penetration,
        round(sum(creating_pv)/sum(agree_true),2) create_pv_penetration,
        round(sum(created_pv)/sum(creating_pv) * 100,2) create_success_rate,
        round(sum(function_show_pv)/sum(agree_true),2) avg_popup,
        round((sum(animation_show_pv)-sum(flow2_exit_pv))/sum(agree_true),2) avg_normal_process,
        round(sum(if_ad_1)/(sum(if_ad_1)+sum(if_ad_0))* 100,2) ad_fill_rate,
        round(sum(ad_show)/sum(agree_true),2) avg_ad_show,
        round((sum(flow1_exit_pv)+sum(flow2_exit_pv))/sum(agree_true),2) avg_flow_up,
        round(sum(start_force_pv)/sum(agree_true),2) avg_start_force,
        round(sum(start_force_pv)/(sum(flow1_exit_pv)+sum(flow2_exit_pv))* 100,2) start_force_rate,
        round(sum(force_adshow_pv)/sum(agree_true),2) avg_force_adshow,
        round(sum(gagvshow)/sum(creating_pv)* 100,2) ad_pv_success_rate,
        round(sum(gagvshow_uv)/sum(agree_true)* 100,2) ad_uv_penetration
        from ads_tools_agree_create_flow_china_daily
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                tdate between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="brand != null and brand != ''">
                and brand in (${brand})
            </if>
            <if test="os_version != null and os_version != ''">
                and os_version in (${os_version})
            </if>
            <if test="isnew != null">
                and isnew = #{isnew}
            </if>
        </where>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        HAVING sum(dau) IS NOT NULL
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>

    <select id="queryInsideTotal" resultType="com.wbgame.pojo.adv2.bigdata.AdsInsideToolsPenetrateDataVo">
        select
        ifnull(sum(dau),0) dau,
        ifnull(sum(agree_true),0) agree_true,
        ifnull(sum(agree_show),0) agree_show,
        ifnull(round(sum(agree_click)/sum(agree_show) * 100,2),0) agree_penetration,
        ifnull(round(sum(app_auto_init)/sum(agree_show) * 100,2),0) init_penetration,
        ifnull(round(sum(b_alive_first_start)/sum(agree_show) * 100,2),0) first_start_penetration,
        ifnull(sum(app_auto_init),0) app_auto_init,
        ifnull(sum(b_alive_first_start),0) b_alive_first_start,
        ifnull(round(sum(creating_uv)/sum(agree_true) * 100,2),0) create_uv_penetration,
        ifnull(round(sum(creating_pv)/sum(agree_true),2),0) create_pv_penetration,
        ifnull(round(sum(created_pv)/sum(creating_pv) * 100,2),0) create_success_rate,
        ifnull(round(sum(function_show_pv)/sum(agree_true),2),0) avg_popup,
        ifnull(round((sum(animation_show_pv)-sum(flow2_exit_pv))/sum(agree_true),2),0) avg_normal_process,
        ifnull(round(sum(if_ad_1)/(sum(if_ad_1)+sum(if_ad_0))* 100,2),0) ad_fill_rate,
        ifnull(round(sum(ad_show)/sum(agree_true),2),0) avg_ad_show,
        ifnull(round((sum(flow1_exit_pv)+sum(flow2_exit_pv))/sum(agree_true),2),0) avg_flow_up,
        ifnull(round(sum(start_force_pv)/sum(agree_true),2),0) avg_start_force,
        ifnull(round(sum(start_force_pv)/(sum(flow1_exit_pv)+sum(flow2_exit_pv))* 100,2),0) start_force_rate,
        ifnull(round(sum(force_adshow_pv)/sum(agree_true),2),0) avg_force_adshow,
        ifnull(round(sum(gagvshow)/sum(creating_pv)* 100,2),0) ad_pv_success_rate,
        ifnull(round(sum(gagvshow_uv)/sum(agree_true)* 100,2),0) ad_uv_penetration
        from ads_tools_agree_create_flow_china_daily
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                tdate between #{startDate} and #{endDate}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="brand != null and brand != ''">
                and brand in (${brand})
            </if>
            <if test="os_version != null and os_version != ''">
                and os_version in (${os_version})
            </if>
            <if test="isnew != null">
                and isnew = #{isnew}
            </if>
        </where>
    </select>
</mapper>