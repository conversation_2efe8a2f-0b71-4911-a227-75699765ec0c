<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adb.AdsCameraLineRevenueMapper">
    <sql id="select_sql">
        select
        <if test="group_date != null and group_date != ''">
            ${group_date} as tDate,
        </if>
        <if test="group_appid != null and group_appid != ''">
            app_id as appid,
        </if>
        <if test="group_appid != null and group_appid != ''">
            app as appName,
        </if>
            round(sum(ifnull(rebate_spend, 0)), 0) as spend,
            round(sum(ifnull(revenue, 0)), 0) as revenue,
            round(sum(ifnull(sub_revenue, 0)), 0) as sub_revenue,
            round(sum(ifnull(total_revenue, 0)), 0) as total_revenue,
            ifnull(round(sum(revenue) / sum(rebate_spend), 4), 0) as roi,
            ifnull(round(sum(sub_revenue) / sum(total_revenue), 4), 0) as sub_rate,
            ifnull(sum(total_revenue - rebate_spend), 0) as profit,
            ifnull(round(sum(total_revenue - rebate_spend) / sum(total_revenue), 4), 0)  as profit_rate
        from ads_camera_line_spend_and_revenue_summary
        where tdate between #{begin_date} and #{end_date}
        <if test="appid != null and appid != ''">
            and app_id in (${appid})
        </if>
        <if test="app_category != null and app_category != ''">
            and app_id in (select id from app_info where app_category in (${app_category}))
        </if>
        <if test="two_app_category != null and two_app_category != ''">
            and app_id in (select id from app_info where two_app_category in (${two_app_category}))
        </if>
        group by
        <if test="group_date != null and group_date != ''">
            ${group_date}
        </if>
        <if test="group_appid != null and group_appid != ''">
            , appid
        </if>
        <if test="group_appid != null and group_appid != ''">
            , app
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </sql>

    <select id="selectCameraLineRevenue"
            parameterType="java.util.Map"
            resultType="com.wbgame.pojo.advert.CameraLineRevenueVo">
        <include refid="select_sql"/>
    </select>

    <select id="selectCameraLineRevenueTotal"
            parameterType="java.util.Map"
            resultType="com.wbgame.pojo.advert.CameraLineRevenueVo">
        select  round(sum(ifnull(spend, 0)), 0) as spend,
                round(sum(ifnull(revenue, 0)), 0) as revenue,
                round(sum(ifnull(sub_revenue, 0)), 0) as sub_revenue,
                round(sum(ifnull(total_revenue, 0)), 0) as total_revenue,
                ifnull(round(sum(revenue) / sum(spend), 4), 0) as roi,
                ifnull(round(sum(sub_revenue) / sum(total_revenue), 4), 0) as sub_rate,
                ifnull(sum(total_revenue - spend), 0) as profit,
                ifnull(round(sum(total_revenue - spend) / sum(total_revenue), 4), 0)  as profit_rate
        from (<include refid="select_sql"/>)
    </select>

    <select id="selectTemplateMakeDataList" parameterType="com.wbgame.pojo.clean.request.TemplateMakeDataRequestParam"
            resultType="com.wbgame.pojo.clean.response.TemplateMakeDataResponse">
        select
            tdate,app_id,
            template_id,
            template_name,
            ability_tag,
            sum(success_count) AS success_count,
            sum(failure_count) AS failure_count,
            sum(total_count) AS total_count,
            ROUND( sum( failure_count )/ sum( total_count ) * 100 , 2 )  failure_ratio
        from dnwx_bi.ads_tool_template_make_data where 1=1
        <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
            AND tdate BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="abilityTags != null and abilityTags !='' ">
            AND ability_tag in (${abilityTags})
        </if>
        <if test="app_id != null and app_id != '' ">
            AND app_id in (${app_id})
        </if>
        <if test="templateName != null and templateName != ''">
            AND template_name like '%${templateName}%'
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate desc , total_count desc
            </otherwise>
        </choose>
    </select>

    <select id="selectTemplateMakeDataSum" parameterType="com.wbgame.pojo.clean.request.TemplateMakeDataRequestParam"
            resultType="com.wbgame.pojo.clean.response.TemplateMakeDataResponse">
        select
            tdate,app_id,
            template_id,
            template_name,
            ability_tag,
            sum(success_count) AS success_count,
            sum(failure_count) AS failure_count,
            sum(total_count) AS total_count,
            ROUND( sum( failure_count )/ sum( total_count ) * 100 , 2 )  failure_ratio
        from dnwx_bi.ads_tool_template_make_data where 1=1
        <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
            AND tdate BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="abilityTags != null and abilityTags !='' ">
            AND ability_tag in (${abilityTags})
        </if>
        <if test="app_id != null and app_id != '' ">
            AND  app_id in (${app_id})
        </if>
        <if test="templateName != null and templateName != ''">
            AND template_name like '%${templateName}%'
        </if>
    </select>
</mapper>