<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adb.UserPayInfoMapper">

    <select id="getUserPayInfoList" parameterType="java.util.Map"
            resultType="com.wbgame.pojo.mobile.UserPayInfoVo">
        select  order_id,a.create_time,ROUND(charge_money/100,2) charge_money,b.gift_item_name gift_name,android_id,a.appid,a.pid,
        app_version,download_channel,ad_buy_channel,order_status,account_delay,login_id,pay_channel,gift_id,a.uid,d.param3,if(d.param3 = 'notice',d.param1,d.custom) custom
        from dnwx_bi.ads_refund_detail_hourly a left join dnwx_bi.ads_gift_info b on a.gift_id = b.gift_item_id and a.appid =b.appid
        left join adb_wb_pay_info d on a.order_id = d.orderid
        where  1=1
        <if test="appid != null and appid != ''">
            and a.appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and a.pid = #{pid}
        </if>
        <if test="download_channel != null and download_channel != ''">
            and a.download_channel in (${download_channel})
        </if>
        <if test="order_status != null and order_status != ''">
            and a.order_status = #{order_status}
        </if>
        <if test="order_id != null and order_id != ''">
            and a.order_id = #{order_id}
        </if>
        <if test="android_id != null and android_id != ''">
            and a.android_id = #{android_id}
        </if>
        <if test="login_id != null and login_id != ''">
            and a.login_id = #{login_id}
        </if>
        <if test="pay_channel != null and pay_channel != ''">
            and a.pay_channel in (${pay_channel})
        </if>
        <if test="uid != null and uid != ''">
            and a.uid = #{uid}
        </if>
        <if test="start_account_delay != null and start_account_delay != ''">
            and a.account_delay <![CDATA[>=]]> ${start_account_delay} and a.account_delay <![CDATA[<]]> ${end_account_delay}
        </if>
        and a.create_time <![CDATA[>=]]> concat(#{start_date}, ' 00:00:00') and a.create_time <![CDATA[<=]]> concat(#{end_date},' 23:59:59')
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by a.create_time desc
            </otherwise>
        </choose>

    </select>

    <select id="getUserPayInfoSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.mobile.UserPayInfoVo">
        select  IFNULL(ROUND(sum(charge_money)/100,2),0) charge_money from dnwx_bi.ads_refund_detail_hourly where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
        <if test="order_status != null and order_status != ''">
            and order_status = #{order_status}
        </if>
        <if test="order_id != null and order_id != ''">
            and order_id = #{order_id}
        </if>
        <if test="android_id != null and android_id != ''">
            and android_id = #{android_id}
        </if>
        <if test="login_id != null and login_id != ''">
            and login_id = #{login_id}
        </if>
        <if test="pay_channel != null and pay_channel != ''">
            and pay_channel in (${pay_channel})
        </if>
        <if test="uid != null and uid != ''">
            and uid = #{uid}
        </if>
        <if test="start_account_delay != null and start_account_delay != ''">
            and account_delay <![CDATA[>=]]> ${start_account_delay} and account_delay <![CDATA[<]]> ${end_account_delay}
        </if>
        and DATE(create_time) <![CDATA[>=]]> #{start_date} and DATE(create_time) <![CDATA[<=]]> #{end_date}
    </select>
    

    <select id="getPayGapList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.PayGapVo">
        select
            tdate,
            appid,
            channel,
            pid,
            ROUND(SUM(active_use),0) active_use,
            ROUND(SUM(add_use)) add_use,
            ROUND(SUM(time_sever)) time_sever,
            ROUND(SUM(time_client)) time_client,
            ROUND(SUM(num_server)) num_server,
            ROUND(SUM(money_sever) / 100, 2) money_sever,
            ROUND(SUM(num_client)) num_client,
            ROUND(SUM(money_client) / 100, 2) money_client,
            ROUND((SUM(money_sever)-SUM(money_client))/100,2) money_gap
            from dnwx_bi.ads_pay_gap_daily where 1=1
            and tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc,add_use desc
            </otherwise>
        </choose>
    </select>

    <select id="getPayGapSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.PayGapVo">
        select
        ROUND(SUM(active_use),0) active_use,
        ROUND(SUM(add_use)) add_use,
        ROUND(SUM(time_sever)) time_sever,
        ROUND(SUM(time_client)) time_client,
        ROUND(SUM(num_server)) num_server,
        ROUND(SUM(money_sever) / 100, 2) money_sever,
        ROUND(SUM(num_client)) num_client,
        ROUND(SUM(money_client) / 100, 2) money_client,
        ROUND((SUM(money_sever)-SUM(money_client))/100,2) money_gap
        from dnwx_bi.ads_pay_gap_daily where 1=1
        and tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
    </select>


    <select id="getPayCashList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.PayCashAnalysisVo">
        <include refid="pay_cash_sql"/>
    </select>

    <select id="getPayCashSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.PayCashAnalysisVo">
        select
        SUM(xx.today_pay_num_1) today_pay_num_1,
        SUM(xx.today_pay_num_2) today_pay_num_2,
        SUM(xx.today_pay_num_3) today_pay_num_3,
        SUM(xx.today_pay_num_4) today_pay_num_4,
        SUM(xx.today_pay_num_5) today_pay_num_5,
        SUM(xx.today_pay_num_6) today_pay_num_6,
        SUM(xx.today_pay_num_7) today_pay_num_7,
        SUM(xx.today_pay_num_8) today_pay_num_8,
        SUM(xx.today_pay_num_9) today_pay_num_9,
        SUM(xx.today_pay_num_10) today_pay_num_10,
        SUM(xx.today_pay_num_11) today_pay_num_11,
        SUM(xx.today_pay_num_12) today_pay_num_12,
        SUM(xx.today_pay_num_13) today_pay_num_13,
        SUM(xx.today_pay_num_14) today_pay_num_14,
        SUM(xx.today_pay_num_15) today_pay_num_15,
        SUM(xx.today_pay_num_0) today_pay_num_0,
        SUM(xx.today_pay_num_total) today_pay_num_total,

        SUM(xx.today_pay_times_1) today_pay_times_1,
        SUM(xx.today_pay_times_2) today_pay_times_2,
        SUM(xx.today_pay_times_3) today_pay_times_3,
        SUM(xx.today_pay_times_4) today_pay_times_4,
        SUM(xx.today_pay_times_5) today_pay_times_5,
        SUM(xx.today_pay_times_6) today_pay_times_6,
        SUM(xx.today_pay_times_7) today_pay_times_7,
        SUM(xx.today_pay_times_8) today_pay_times_8,
        SUM(xx.today_pay_times_9) today_pay_times_9,
        SUM(xx.today_pay_times_10) today_pay_times_10,
        SUM(xx.today_pay_times_11) today_pay_times_11,
        SUM(xx.today_pay_times_12) today_pay_times_12,
        SUM(xx.today_pay_times_13) today_pay_times_13,
        SUM(xx.today_pay_times_14) today_pay_times_14,
        SUM(xx.today_pay_times_15) today_pay_times_15,
        SUM(xx.today_pay_times_total) today_pay_times_total,
        ROUND((SUM(xx.today_pay_times_total)/SUM(xx.today_pay_num_total)),2) today_pay_times_avg,

        SUM(xx.today_pay_money_1) today_pay_money_1,
        SUM(xx.today_pay_money_2) today_pay_money_2,
        SUM(xx.today_pay_money_3) today_pay_money_3,
        SUM(xx.today_pay_money_4) today_pay_money_4,
        SUM(xx.today_pay_money_5) today_pay_money_5,
        SUM(xx.today_pay_money_6) today_pay_money_6,
        SUM(xx.today_pay_money_7) today_pay_money_7,
        SUM(xx.today_pay_money_8) today_pay_money_8,
        SUM(xx.today_pay_money_9) today_pay_money_9,
        SUM(xx.today_pay_money_10) today_pay_money_10,
        SUM(xx.today_pay_money_11) today_pay_money_11,
        SUM(xx.today_pay_money_12) today_pay_money_12,
        SUM(xx.today_pay_money_13) today_pay_money_13,
        SUM(xx.today_pay_money_14) today_pay_money_14,
        SUM(xx.today_pay_money_15) today_pay_money_15,
        SUM(xx.today_pay_money_0) today_pay_money_0,
        SUM(xx.today_pay_total) today_pay_total,
        ROUND((SUM(xx.today_pay_total)/SUM(xx.today_pay_num_total)),2) today_pay_arpu
        from  (<include refid="pay_cash_sql"/>) xx
    </select>


    <sql id="pay_cash_sql">
        select aa.*,
        ROUND((aa.today_pay_total/aa.today_pay_num_total),2) today_pay_arpu,
        ROUND((aa.today_pay_times_total/aa.today_pay_num_total),2) today_pay_times_avg
        from (
        select
        create_date,
        <if test="appid_group != null and appid_group != ''">
            appid,
        </if>
        <if test="pid_group != null and pid_group != ''">
            pid,
        </if>
        <if test="download_channel_group != null and download_channel_group != ''">
            download_channel,
        </if>
        COUNT(DISTINCT android_id) today_pay_num_total,

        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)=0 THEN
        true ELSE null END) today_pay_num_0,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 0 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 1 THEN true ELSE
        null END) today_pay_num_1,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 1 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 3 THEN true ELSE
        null END) today_pay_num_2,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 3 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 6 THEN true ELSE
        null END) today_pay_num_3,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 6 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 12 THEN true
        ELSE null END) today_pay_num_4,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 12 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 24 THEN true
        ELSE null END) today_pay_num_5,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 24 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 36 THEN true
        ELSE null END) today_pay_num_6,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 36 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 68 THEN true
        ELSE null END) today_pay_num_7,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 68 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 128 THEN true
        ELSE null END) today_pay_num_8,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 128 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 198 THEN true
        ELSE null END) today_pay_num_9,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 198 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 328 THEN
        true ELSE null END) today_pay_num_10,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 328 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 648 THEN
        true ELSE null END) today_pay_num_11,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 648 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 3000 THEN
        true ELSE null END) today_pay_num_12,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 3000 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 5000 THEN
        true ELSE null END) today_pay_num_13,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 5000 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 10000 THEN
        true ELSE null END) today_pay_num_14,
        COUNT(DISTINCT android_id,CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 10000 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 999999999 THEN
        true ELSE null END) today_pay_num_15,


        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)=0
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_0,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 0 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 1
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_1,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 1 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 3
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_2,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 3 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 6
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_3,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 6 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 12
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_4,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 12 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 24
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_5,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 24 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 36
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_6,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 36 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 68
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_7,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 68 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 128
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_8,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 128 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 198
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_9,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 198 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 328
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_10,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 328 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 648
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_11,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 648 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 3000
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_12,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 3000 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 5000
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_13,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 5000 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 10000
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_14,
        ROUND((SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 10000 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 999999999
        THEN charge_money ELSE 0 END)/100),2) today_pay_money_15,

        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 0 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 1 THEN times ELSE
        0 END) today_pay_times_1,
        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 1 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 3 THEN times ELSE
        0 END) today_pay_times_2,
        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 3 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 6 THEN times ELSE
        0 END) today_pay_times_3,
        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 6 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 12 THEN times
        ELSE 0 END) today_pay_times_4,
        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 12 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 24 THEN times
        ELSE 0 END) today_pay_times_5,
        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 24 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 36 THEN times
        ELSE 0 END) today_pay_times_6,
        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 36 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 68 THEN times
        ELSE 0 END) today_pay_times_7,
        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 68 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 128 THEN times
        ELSE 0 END) today_pay_times_8,
        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 128 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 198 THEN times
        ELSE 0 END) today_pay_times_9,
        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 198 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 328 THEN
        times ELSE 0 END) today_pay_times_10,
        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 328 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 648 THEN
        times ELSE 0 END) today_pay_times_11,
        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 648 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 3000 THEN
        times ELSE 0 END) today_pay_times_12,
        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 3000 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 5000 THEN
        times ELSE 0 END) today_pay_times_13,
        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 5000 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 10000 THEN
        times ELSE 0 END) today_pay_times_14,
        SUM(CASE WHEN ROUND(charge_money/100,1)<![CDATA[>]]> 10000 AND ROUND(charge_money/100,1) <![CDATA[<=]]> 999999999 THEN
        times ELSE 0 END) today_pay_times_15,
        SUM(times) today_pay_times_total,


        (SUM(charge_money)/100) today_pay_total
        from  (
        select sum(charge_money) charge_money,count(0) times, android_id,create_date,appid,pid,download_channel
        from dnwx_bi.ads_order_analysis_daily where 1=1 group by create_date,appid,pid,download_channel,android_id
        ) a
        where 1=1
        and create_date <![CDATA[>=]]> #{start_date} and create_date <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        ) aa
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by create_date asc
            </otherwise>
        </choose>
    </sql>



    <select id="getPayCashTotalList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.PayCashAnalysisVo">
        select aa.*,
        ROUND((aa.pay_total/aa.pay_num_total),2) total_pay_avg,
        ROUND((aa.total_pay_times_total/aa.pay_num_total),2) total_pay_times_avg
        from (
        select
        '${tdate}' create_date,
        <if test="appid_group != null and appid_group != ''">
            appid,
        </if>
        <if test="pid_group != null and pid_group != ''">
            pid,
        </if>
        <if test="download_channel_group != null and download_channel_group != ''">
            download_channel,
        </if>
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 0 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 1 THEN true ELSE null END) total_pay_num_1,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 1 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 6 THEN true ELSE null END) total_pay_num_2,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 6 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 12 THEN true ELSE null END) total_pay_num_3,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 12 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 36 THEN true ELSE null END) total_pay_num_4,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 36 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 68 THEN true ELSE null END) total_pay_num_5,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 68 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 128 THEN true ELSE null END) total_pay_num_6,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 128 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 198 THEN true ELSE null END) total_pay_num_7,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 198 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 328 THEN true ELSE null END) total_pay_num_8,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 328 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 648 THEN true ELSE null END) total_pay_num_9,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 648 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 3000 THEN true ELSE null END) total_pay_num_10,

        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 3000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 5000 THEN true ELSE null END) total_pay_num_11,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 5000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 10000 THEN true ELSE null END) total_pay_num_12,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 10000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 30000 THEN true ELSE null END) total_pay_num_13,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 30000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 50000 THEN true ELSE null END) total_pay_num_14,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 50000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 999999999 THEN true ELSE null END) total_pay_num_15,

        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 0 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 1 THEN times ELSE null END) total_pay_times_1,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 1 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 6 THEN times ELSE null END) total_pay_times_2,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 6 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 12 THEN times ELSE null END) total_pay_times_3,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 12 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 36 THEN times ELSE null END) total_pay_times_4,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 36 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 68 THEN times ELSE null END) total_pay_times_5,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 68 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 128 THEN times ELSE null END) total_pay_times_6,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 128 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 198 THEN times ELSE null END) total_pay_times_7,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 198 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 328 THEN times ELSE null END) total_pay_times_8,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 328 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 648 THEN times ELSE null END) total_pay_times_9,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 648 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 3000 THEN times ELSE null END) total_pay_times_10,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 3000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 5000 THEN times ELSE null END) total_pay_times_11,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 5000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 10000 THEN times ELSE null END) total_pay_times_12,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 10000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 30000 THEN times ELSE null END) total_pay_times_13,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 30000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 50000 THEN times ELSE null END) total_pay_times_14,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 50000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 999999999 THEN times ELSE null END) total_pay_times_15,
        SUM(times) total_pay_times_total,

        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)=0 THEN true ELSE null END) total_pay_num_0,

        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 0 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 1 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_1,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 1 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 6 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_2,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 6 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 12 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_3,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 12 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 36 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_4,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 36 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 68 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_5,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 68 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 128 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_6,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 128 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 198 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_7,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 198 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 328 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_8,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 328 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 648 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_9,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 648 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 3000 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_10,

        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 3000 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 5000 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_11,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 5000 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 10000 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_12,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 10000 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 30000 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_13,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 30000 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 50000 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_14,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 50000 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 999999999 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_15,

        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)=0 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_0,
        count(DISTINCT android_id) pay_num_total,
        ROUND((SUM(accumulative_pay_amount)/100),2) pay_total
        from (
            select max(accumulative_pay_amount) accumulative_pay_amount,android_id,count(0) times,
            <if test="appid_group != null and appid_group != ''">
                appid,
            </if>
            <if test="pid_group != null and pid_group != ''">
                pid,
            </if>
            <if test="download_channel_group != null and download_channel_group != ''">
                download_channel,
            </if>
            create_date
            from dnwx_bi.ads_order_analysis_daily where 1=1 and create_date <![CDATA[<=]]> #{tdate}
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="download_channel != null and download_channel != ''">
                and download_channel in (${download_channel})
            </if>
            <if test="pid != null and pid != ''">
                and pid = #{pid}
            </if>
            group by
            <if test="appid_group != null and appid_group != ''">
                appid,
            </if>
            <if test="pid_group != null and pid_group != ''">
                pid,
            </if>
            <if test="download_channel_group != null and download_channel_group != ''">
                download_channel,
            </if>
            android_id
        ) a where 1=1
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        ) aa
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by create_date asc
            </otherwise>
        </choose>

    </select>

    <select id="getPayCashTotalListTotal" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.PayCashAnalysisVo">

        select
        SUM(xx.today_pay_num_1) today_pay_num_1,
        SUM(xx.today_pay_num_2) today_pay_num_2,
        SUM(xx.today_pay_num_3) today_pay_num_3,
        SUM(xx.today_pay_num_4) today_pay_num_4,
        SUM(xx.today_pay_num_5) today_pay_num_5,
        SUM(xx.today_pay_num_6) today_pay_num_6,
        SUM(xx.today_pay_num_7) today_pay_num_7,
        SUM(xx.today_pay_num_8) today_pay_num_8,
        SUM(xx.today_pay_num_9) today_pay_num_9,
        SUM(xx.today_pay_num_10) today_pay_num_10,
        SUM(xx.today_pay_num_11) today_pay_num_11,
        SUM(xx.today_pay_num_12) today_pay_num_12,
        SUM(xx.today_pay_num_13) today_pay_num_13,
        SUM(xx.today_pay_num_14) today_pay_num_14,
        SUM(xx.today_pay_num_15) today_pay_num_15,
        SUM(xx.today_pay_num_0) today_pay_num_0,
        SUM(xx.today_pay_num_total) today_pay_num_total,

        SUM(xx.today_pay_times_1) today_pay_times_1,
        SUM(xx.today_pay_times_2) today_pay_times_2,
        SUM(xx.today_pay_times_3) today_pay_times_3,
        SUM(xx.today_pay_times_4) today_pay_times_4,
        SUM(xx.today_pay_times_5) today_pay_times_5,
        SUM(xx.today_pay_times_6) today_pay_times_6,
        SUM(xx.today_pay_times_7) today_pay_times_7,
        SUM(xx.today_pay_times_8) today_pay_times_8,
        SUM(xx.today_pay_times_9) today_pay_times_9,
        SUM(xx.today_pay_times_10) today_pay_times_10,
        SUM(xx.today_pay_times_11) today_pay_times_11,
        SUM(xx.today_pay_times_12) today_pay_times_12,
        SUM(xx.today_pay_times_13) today_pay_times_13,
        SUM(xx.today_pay_times_14) today_pay_times_14,
        SUM(xx.today_pay_times_15) today_pay_times_15,
        SUM(xx.today_pay_times_total) today_pay_times_total,
        ROUND((SUM(xx.today_pay_times_total)/SUM(xx.today_pay_num_total)),2) today_pay_times_avg,

        SUM(xx.today_pay_money_1) today_pay_money_1,
        SUM(xx.today_pay_money_2) today_pay_money_2,
        SUM(xx.today_pay_money_3) today_pay_money_3,
        SUM(xx.today_pay_money_4) today_pay_money_4,
        SUM(xx.today_pay_money_5) today_pay_money_5,
        SUM(xx.today_pay_money_6) today_pay_money_6,
        SUM(xx.today_pay_money_7) today_pay_money_7,
        SUM(xx.today_pay_money_8) today_pay_money_8,
        SUM(xx.today_pay_money_9) today_pay_money_9,
        SUM(xx.today_pay_money_10) today_pay_money_10,
        SUM(xx.today_pay_money_11) today_pay_money_11,
        SUM(xx.today_pay_money_12) today_pay_money_12,
        SUM(xx.today_pay_money_13) today_pay_money_13,
        SUM(xx.today_pay_money_14) today_pay_money_14,
        SUM(xx.today_pay_money_15) today_pay_money_15,
        SUM(xx.today_pay_money_0) today_pay_money_0,
        SUM(xx.today_pay_total) today_pay_total,
        ROUND((SUM(xx.today_pay_total)/SUM(xx.today_pay_num_total)),2) today_pay_arpu
        from  (

        select aa.*,
        ROUND((aa.pay_total/aa.pay_num_total),2) total_pay_avg,
        ROUND((aa.total_pay_times_total/aa.pay_num_total),2) total_pay_times_avg
        from (
        select
        '${tdate}' create_date,
        <if test="appid_group != null and appid_group != ''">
            appid,
        </if>
        <if test="pid_group != null and pid_group != ''">
            pid,
        </if>
        <if test="download_channel_group != null and download_channel_group != ''">
            download_channel,
        </if>
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 0 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 1 THEN true ELSE null END) total_pay_num_1,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 1 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 6 THEN true ELSE null END) total_pay_num_2,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 6 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 12 THEN true ELSE null END) total_pay_num_3,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 12 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 36 THEN true ELSE null END) total_pay_num_4,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 36 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 68 THEN true ELSE null END) total_pay_num_5,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 68 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 128 THEN true ELSE null END) total_pay_num_6,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 128 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 198 THEN true ELSE null END) total_pay_num_7,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 198 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 328 THEN true ELSE null END) total_pay_num_8,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 328 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 648 THEN true ELSE null END) total_pay_num_9,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 648 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 3000 THEN true ELSE null END) total_pay_num_10,

        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 3000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 5000 THEN true ELSE null END) total_pay_num_11,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 5000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 10000 THEN true ELSE null END) total_pay_num_12,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 10000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 30000 THEN true ELSE null END) total_pay_num_13,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 30000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 50000 THEN true ELSE null END) total_pay_num_14,
        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 50000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 999999999 THEN true ELSE null END) total_pay_num_15,

        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 0 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 1 THEN times ELSE null END) total_pay_times_1,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 1 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 6 THEN times ELSE null END) total_pay_times_2,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 6 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 12 THEN times ELSE null END) total_pay_times_3,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 12 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 36 THEN times ELSE null END) total_pay_times_4,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 36 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 68 THEN times ELSE null END) total_pay_times_5,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 68 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 128 THEN times ELSE null END) total_pay_times_6,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 128 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 198 THEN times ELSE null END) total_pay_times_7,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 198 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 328 THEN times ELSE null END) total_pay_times_8,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 328 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 648 THEN times ELSE null END) total_pay_times_9,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 648 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 3000 THEN times ELSE null END) total_pay_times_10,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 3000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 5000 THEN times ELSE null END) total_pay_times_11,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 5000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 10000 THEN times ELSE null END) total_pay_times_12,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 10000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 30000 THEN times ELSE null END) total_pay_times_13,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 30000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 50000 THEN times ELSE null END) total_pay_times_14,
        SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 50000 AND ROUND(accumulative_pay_amount/100,1)
        <![CDATA[<=]]> 999999999 THEN times ELSE null END) total_pay_times_15,
        SUM(times) total_pay_times_total,

        COUNT(DISTINCT android_id,
        CASE WHEN ROUND(accumulative_pay_amount/100,1)=0 THEN true ELSE null END) total_pay_num_0,

        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 0 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 1 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_1,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 1 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 6 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_2,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 6 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 12 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_3,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 12 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 36 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_4,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 36 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 68 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_5,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 68 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 128 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_6,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 128 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 198 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_7,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 198 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 328 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_8,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 328 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 648 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_9,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 648 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 3000 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_10,

        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 3000 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 5000 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_11,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 5000 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 10000 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_12,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 10000 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 30000 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_13,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 30000 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 50000 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_14,
        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)<![CDATA[>]]> 50000 AND
        ROUND(accumulative_pay_amount/100,1) <![CDATA[<=]]> 999999999 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_15,

        ROUND((SUM(CASE WHEN ROUND(accumulative_pay_amount/100,1)=0 THEN accumulative_pay_amount ELSE 0 END)/100),2)
        total_pay_money_0,
        count(DISTINCT android_id) pay_num_total,
        ROUND((SUM(accumulative_pay_amount)/100),2) pay_total
        from (
        select max(accumulative_pay_amount) accumulative_pay_amount,android_id,count(0) times,
        <if test="appid_group != null and appid_group != ''">
            appid,
        </if>
        <if test="pid_group != null and pid_group != ''">
            pid,
        </if>
        <if test="download_channel_group != null and download_channel_group != ''">
            download_channel,
        </if>
        create_date
        from dnwx_bi.ads_order_analysis_daily where 1=1 and create_date <![CDATA[<=]]> #{tdate}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        group by
        <if test="appid_group != null and appid_group != ''">
            appid,
        </if>
        <if test="pid_group != null and pid_group != ''">
            pid,
        </if>
        <if test="download_channel_group != null and download_channel_group != ''">
            download_channel,
        </if>
        android_id
        ) a where 1=1
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        ) aa
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by create_date asc
            </otherwise>
        </choose>

        ) xx


    </select>

    <select id="getTimingTrendList" resultType="com.wbgame.pojo.game.TimingTrendVo" parameterType="java.util.Map">
        <include refid="timing_trend_sql"/>
    </select>

    <select id="getTimingTrendSum" resultType="com.wbgame.pojo.game.TimingTrendVo" parameterType="java.util.Map">
        select
            sum(aa.add_user) add_user,
            sum(aa.active_user) active_user,
            sum(aa.pay_num) pay_num,
            sum(aa.ad_revenue) ad_revenue,
            sum(aa.iap_revenue) iap_revenue,
            sum(aa.total_revenue) total_revenue,
            ROUND((sum(aa.ad_revenue)/sum(aa.active_user))/100,2) ad_arpu,
            ROUND((sum(aa.iap_revenue)/sum(aa.active_user))/100,2) iap_arpu,
            ROUND((sum(aa.total_revenue)/sum(aa.active_user))/100,2) total_arpu
        from (<include refid="timing_trend_sql"/>) aa
    </select>

    <sql id="timing_trend_sql">
        select
        DATE(tdate) tdate,
        CONVERT(hourly, SIGNED) hourly,
        appid,
        pid,
        channel,
        sum(add_user) add_user,
        sum(active_user) active_user,
        sum(pay_num) pay_num,
        ROUND((sum(ad_revenue)/100),2) ad_revenue,
        ROUND((sum(iap_revenue)/100),2) iap_revenue,
        ROUND((sum(ad_revenue)+sum(iap_revenue))/100,2) total_revenue,
        ROUND((sum(ad_revenue)/100)/sum(active_user),2) ad_arpu,
        ROUND((sum(iap_revenue)/100)/sum(active_user),2) iap_arpu,
        ROUND(((sum(ad_revenue)+sum(iap_revenue))/100)/sum(active_user),2) total_arpu
        from dnwx_bi.ads_trend_income_hourly where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        and DATE(tdate) <![CDATA[>=]]> #{start_date} and DATE(tdate) <![CDATA[<=]]> #{end_date}
        <if test="group != null and group != ''">
            group by ${group}
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc,hourly asc,add_user desc
            </otherwise>
        </choose>
    </sql>

    <select id="getTimingDaysList" parameterType="java.util.Map" resultType="java.util.Map">
        select
            hourly,
            sum(add_user) add_user,
            sum(active_user) active_user,
            sum(pay_num) pay_num,
            ROUND((sum(ad_revenue)/100),2) ad_revenue,
            ROUND((sum(iap_revenue)/100),2) iap_revenue
        from dnwx_bi.ads_trend_income_hourly where 1=1
        and tdate = #{tdate}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        group by hourly
        order by hourly asc
    </select>

    <select id="getPayAdValueList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.PayAdValueVo">
        select
        t_date,appid,pid,download_channel,
        ROUND((SUM(iaa_revenue)/100/SUM(user_cnt)),2) total_ad_arpu,
        ROUND((SUM(plaque_cnt)/SUM(user_cnt)),2) total_plaque_ipu,
        ROUND((SUM(plaque_revenue)/100/SUM(plaque_cnt))*1000,2) total_plaque_ecpm,
        ROUND((SUM(video_cnt)/SUM(user_cnt)),2) total_video_ipu,
        ROUND((SUM(video_revenue)/100/SUM(video_cnt))*1000,2) total_video_ecpm,

        IFNULL(SUM(if(flag='flag_0',user_cnt,0)),0)  user_cnt_1,
        ROUND(SUM(if(flag='flag_0',iaa_revenue,0))/100/SUM(if(flag='flag_0',user_cnt,0)),2) ad_arpu_1,
        ROUND(SUM(if(flag='flag_0',plaque_cnt,0))/SUM(if(flag='flag_0',user_cnt,0)),2) plaque_ipu_1,
        ROUND((SUM(if(flag='flag_0',plaque_revenue,0))/100/SUM(if(flag='flag_0',plaque_cnt,0)))*1000,2) plaque_ecpm_1,
        ROUND(SUM(if(flag='flag_0',video_cnt,0))/SUM(if(flag='flag_0',user_cnt,0)),2) video_ipu_1,
        ROUND((SUM(if(flag='flag_0',video_revenue,0))/100/SUM(if(flag='flag_0',video_cnt,0)))*1000,2) video_ecpm_1,

        IFNULL(SUM(if(flag='flag_0_1',user_cnt,0)),0)  user_cnt_2,
        ROUND(SUM(if(flag='flag_0_1',iaa_revenue,0))/100/SUM(if(flag='flag_0_1',user_cnt,0)),2) ad_arpu_2,
        ROUND(SUM(if(flag='flag_0_1',plaque_cnt,0))/SUM(if(flag='flag_0_1',user_cnt,0)),2) plaque_ipu_2,
        ROUND((SUM(if(flag='flag_0_1',plaque_revenue,0))/100/SUM(if(flag='flag_0_1',plaque_cnt,0)))*1000,2) plaque_ecpm_2,
        ROUND(SUM(if(flag='flag_0_1',video_cnt,0))/SUM(if(flag='flag_0_1',user_cnt,0)),2) video_ipu_2,
        ROUND((SUM(if(flag='flag_0_1',video_revenue,0))/100/SUM(if(flag='flag_0_1',video_cnt,0)))*1000,2) video_ecpm_2,

        IFNULL(SUM(if(flag='flag_1_3',user_cnt,0)),0)  user_cnt_3,
        ROUND(SUM(if(flag='flag_1_3',iaa_revenue,0))/100/SUM(if(flag='flag_1_3',user_cnt,0)),2) ad_arpu_3,
        ROUND(SUM(if(flag='flag_1_3',plaque_cnt,0))/SUM(if(flag='flag_1_3',user_cnt,0)),2) plaque_ipu_3,
        ROUND((SUM(if(flag='flag_1_3',plaque_revenue,0))/100/SUM(if(flag='flag_1_3',plaque_cnt,0)))*1000,2) plaque_ecpm_3,
        ROUND(SUM(if(flag='flag_1_3',video_cnt,0))/SUM(if(flag='flag_1_3',user_cnt,0)),2) video_ipu_3,
        ROUND((SUM(if(flag='flag_1_3',video_revenue,0))/100/SUM(if(flag='flag_1_3',video_cnt,0)))*1000,2) video_ecpm_3,

        IFNULL(SUM(if(flag='flag_3_6',user_cnt,0)),0)  user_cnt_4,
        ROUND(SUM(if(flag='flag_3_6',iaa_revenue,0))/100/SUM(if(flag='flag_3_6',user_cnt,0)),2) ad_arpu_4,
        ROUND(SUM(if(flag='flag_3_6',plaque_cnt,0))/SUM(if(flag='flag_3_6',user_cnt,0)),2) plaque_ipu_4,
        ROUND((SUM(if(flag='flag_3_6',plaque_revenue,0))/100/SUM(if(flag='flag_3_6',plaque_cnt,0)))*1000,2) plaque_ecpm_4,
        ROUND(SUM(if(flag='flag_3_6',video_cnt,0))/SUM(if(flag='flag_3_6',user_cnt,0)),2) video_ipu_4,
        ROUND((SUM(if(flag='flag_3_6',video_revenue,0))/100/SUM(if(flag='flag_3_6',video_cnt,0)))*1000,2) video_ecpm_4,

        IFNULL(SUM(if(flag='flag_6_12',user_cnt,0)),0)  user_cnt_5,
        ROUND(SUM(if(flag='flag_6_12',iaa_revenue,0))/100/SUM(if(flag='flag_6_12',user_cnt,0)),2) ad_arpu_5,
        ROUND(SUM(if(flag='flag_6_12',plaque_cnt,0))/SUM(if(flag='flag_6_12',user_cnt,0)),2) plaque_ipu_5,
        ROUND((SUM(if(flag='flag_6_12',plaque_revenue,0))/100/SUM(if(flag='flag_6_12',plaque_cnt,0)))*1000,2) plaque_ecpm_5,
        ROUND(SUM(if(flag='flag_6_12',video_cnt,0))/SUM(if(flag='flag_6_12',user_cnt,0)),2) video_ipu_5,
        ROUND((SUM(if(flag='flag_6_12',video_revenue,0))/100/SUM(if(flag='flag_6_12',video_cnt,0)))*1000,2) video_ecpm_5,

        IFNULL(SUM(if(flag='flag_12_24',user_cnt,0)),0)  user_cnt_6,
        ROUND(SUM(if(flag='flag_12_24',iaa_revenue,0))/100/SUM(if(flag='flag_12_24',user_cnt,0)),2) ad_arpu_6,
        ROUND(SUM(if(flag='flag_12_24',plaque_cnt,0))/SUM(if(flag='flag_12_24',user_cnt,0)),2) plaque_ipu_6,
        ROUND((SUM(if(flag='flag_12_24',plaque_revenue,0))/100/SUM(if(flag='flag_12_24',plaque_cnt,0)))*1000,2) plaque_ecpm_6,
        ROUND(SUM(if(flag='flag_12_24',video_cnt,0))/SUM(if(flag='flag_12_24',user_cnt,0)),2) video_ipu_6,
        ROUND((SUM(if(flag='flag_12_24',video_revenue,0))/100/SUM(if(flag='flag_12_24',video_cnt,0)))*1000,2) video_ecpm_6,

        IFNULL(SUM(if(flag='flag_24_36',user_cnt,0)),0)  user_cnt_7,
        ROUND(SUM(if(flag='flag_24_36',iaa_revenue,0))/100/SUM(if(flag='flag_24_36',user_cnt,0)),2) ad_arpu_7,
        ROUND(SUM(if(flag='flag_24_36',plaque_cnt,0))/SUM(if(flag='flag_24_36',user_cnt,0)),2) plaque_ipu_7,
        ROUND((SUM(if(flag='flag_24_36',plaque_revenue,0))/100/SUM(if(flag='flag_24_36',plaque_cnt,0)))*1000,2) plaque_ecpm_7,
        ROUND(SUM(if(flag='flag_24_36',video_cnt,0))/SUM(if(flag='flag_24_36',user_cnt,0)),2) video_ipu_7,
        ROUND((SUM(if(flag='flag_24_36',video_revenue,0))/100/SUM(if(flag='flag_24_36',video_cnt,0)))*1000,2) video_ecpm_7,

        IFNULL(SUM(if(flag='flag_36_68',user_cnt,0)),0)  user_cnt_8,
        ROUND(SUM(if(flag='flag_36_68',iaa_revenue,0))/100/SUM(if(flag='flag_36_68',user_cnt,0)),2) ad_arpu_8,
        ROUND(SUM(if(flag='flag_36_68',plaque_cnt,0))/SUM(if(flag='flag_36_68',user_cnt,0)),2) plaque_ipu_8,
        ROUND((SUM(if(flag='flag_36_68',plaque_revenue,0))/100/SUM(if(flag='flag_36_68',plaque_cnt,0)))*1000,2) plaque_ecpm_8,
        ROUND(SUM(if(flag='flag_36_68',video_cnt,0))/SUM(if(flag='flag_36_68',user_cnt,0)),2) video_ipu_8,
        ROUND((SUM(if(flag='flag_36_68',video_revenue,0))/100/SUM(if(flag='flag_36_68',video_cnt,0)))*1000,2) video_ecpm_8,

        IFNULL(SUM(if(flag='flag_68_128',user_cnt,0)),0)  user_cnt_9,
        ROUND(SUM(if(flag='flag_68_128',iaa_revenue,0))/100/SUM(if(flag='flag_68_128',user_cnt,0)),2) ad_arpu_9,
        ROUND(SUM(if(flag='flag_68_128',plaque_cnt,0))/SUM(if(flag='flag_68_128',user_cnt,0)),2) plaque_ipu_9,
        ROUND((SUM(if(flag='flag_68_128',plaque_revenue,0))/100/SUM(if(flag='flag_68_128',plaque_cnt,0)))*1000,2) plaque_ecpm_9,
        ROUND(SUM(if(flag='flag_68_128',video_cnt,0))/SUM(if(flag='flag_68_128',user_cnt,0)),2) video_ipu_9,
        ROUND((SUM(if(flag='flag_68_128',video_revenue,0))/100/SUM(if(flag='flag_68_128',video_cnt,0)))*1000,2) video_ecpm_9,

        IFNULL(SUM(if(flag='flag_128_198',user_cnt,0)),0)  user_cnt_10,
        ROUND(SUM(if(flag='flag_128_198',iaa_revenue,0))/100/SUM(if(flag='flag_128_198',user_cnt,0)),2) ad_arpu_10,
        ROUND(SUM(if(flag='flag_128_198',plaque_cnt,0))/SUM(if(flag='flag_128_198',user_cnt,0)),2) plaque_ipu_10,
        ROUND((SUM(if(flag='flag_128_198',plaque_revenue,0))/100/SUM(if(flag='flag_128_198',plaque_cnt,0)))*1000,2) plaque_ecpm_10,
        ROUND(SUM(if(flag='flag_128_198',video_cnt,0))/SUM(if(flag='flag_128_198',user_cnt,0)),2) video_ipu_10,
        ROUND((SUM(if(flag='flag_128_198',video_revenue,0))/100/SUM(if(flag='flag_128_198',video_cnt,0)))*1000,2) video_ecpm_10,

        IFNULL(SUM(if(flag='flag_198',user_cnt,0)),0)  user_cnt_11,
        ROUND(SUM(if(flag='flag_198',iaa_revenue,0))/100/SUM(if(flag='flag_198',user_cnt,0)),2) ad_arpu_11,
        ROUND(SUM(if(flag='flag_198',plaque_cnt,0))/SUM(if(flag='flag_198',user_cnt,0)),2) plaque_ipu_11,
        ROUND((SUM(if(flag='flag_198',plaque_revenue,0))/100/SUM(if(flag='flag_198',plaque_cnt,0)))*1000,2) plaque_ecpm_11,
        ROUND(SUM(if(flag='flag_198',video_cnt,0))/SUM(if(flag='flag_198',user_cnt,0)),2) video_ipu_11,
        ROUND((SUM(if(flag='flag_198',video_revenue,0))/100/SUM(if(flag='flag_198',video_cnt,0)))*1000,2) video_ecpm_11,

        IFNULL(SUM(if(flag='flag_198_328',user_cnt,0)),0)  user_cnt_12,
        ROUND(SUM(if(flag='flag_198_328',iaa_revenue,0))/100/SUM(if(flag='flag_198_328',user_cnt,0)),2) ad_arpu_12,
        ROUND(SUM(if(flag='flag_198_328',plaque_cnt,0))/SUM(if(flag='flag_198_328',user_cnt,0)),2) plaque_ipu_12,
        ROUND((SUM(if(flag='flag_198_328',plaque_revenue,0))/100/SUM(if(flag='flag_198_328',plaque_cnt,0)))*1000,2) plaque_ecpm_12,
        ROUND(SUM(if(flag='flag_198_328',video_cnt,0))/SUM(if(flag='flag_198_328',user_cnt,0)),2) video_ipu_12,
        ROUND((SUM(if(flag='flag_198_328',video_revenue,0))/100/SUM(if(flag='flag_198_328',video_cnt,0)))*1000,2) video_ecpm_12,


        IFNULL(SUM(if(flag='flag_328_648',user_cnt,0)),0)  user_cnt_13,
        ROUND(SUM(if(flag='flag_328_648',iaa_revenue,0))/100/SUM(if(flag='flag_328_648',user_cnt,0)),2) ad_arpu_13,
        ROUND(SUM(if(flag='flag_328_648',plaque_cnt,0))/SUM(if(flag='flag_328_648',user_cnt,0)),2) plaque_ipu_13,
        ROUND((SUM(if(flag='flag_328_648',plaque_revenue,0))/100/SUM(if(flag='flag_328_648',plaque_cnt,0)))*1000,2) plaque_ecpm_13,
        ROUND(SUM(if(flag='flag_328_648',video_cnt,0))/SUM(if(flag='flag_328_648',user_cnt,0)),2) video_ipu_13,
        ROUND((SUM(if(flag='flag_328_648',video_revenue,0))/100/SUM(if(flag='flag_328_648',video_cnt,0)))*1000,2) video_ecpm_13,


        IFNULL(SUM(if(flag='flag_648_3000',user_cnt,0)),0)  user_cnt_14,
        ROUND(SUM(if(flag='flag_648_3000',iaa_revenue,0))/100/SUM(if(flag='flag_648_3000',user_cnt,0)),2) ad_arpu_14,
        ROUND(SUM(if(flag='flag_648_3000',plaque_cnt,0))/SUM(if(flag='flag_648_3000',user_cnt,0)),2) plaque_ipu_14,
        ROUND((SUM(if(flag='flag_648_3000',plaque_revenue,0))/100/SUM(if(flag='flag_648_3000',plaque_cnt,0)))*1000,2) plaque_ecpm_14,
        ROUND(SUM(if(flag='flag_648_3000',video_cnt,0))/SUM(if(flag='flag_648_3000',user_cnt,0)),2) video_ipu_14,
        ROUND((SUM(if(flag='flag_648_3000',video_revenue,0))/100/SUM(if(flag='flag_648_3000',video_cnt,0)))*1000,2) video_ecpm_14,


        IFNULL(SUM(if(flag='flag_3000_5000',user_cnt,0)),0)  user_cnt_15,
        ROUND(SUM(if(flag='flag_3000_5000',iaa_revenue,0))/100/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) ad_arpu_15,
        ROUND(SUM(if(flag='flag_3000_5000',plaque_cnt,0))/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) plaque_ipu_15,
        ROUND((SUM(if(flag='flag_3000_5000',plaque_revenue,0))/100/SUM(if(flag='flag_3000_5000',plaque_cnt,0)))*1000,2) plaque_ecpm_15,
        ROUND(SUM(if(flag='flag_3000_5000',video_cnt,0))/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) video_ipu_15,
        ROUND((SUM(if(flag='flag_3000_5000',video_revenue,0))/100/SUM(if(flag='flag_3000_5000',video_cnt,0)))*1000,2) video_ecpm_15,


        IFNULL(SUM(if(flag='flag_5000_10000',user_cnt,0)),0)  user_cnt_16,
        ROUND(SUM(if(flag='flag_5000_10000',iaa_revenue,0))/100/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) ad_arpu_16,
        ROUND(SUM(if(flag='flag_5000_10000',plaque_cnt,0))/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) plaque_ipu_16,
        ROUND((SUM(if(flag='flag_5000_10000',plaque_revenue,0))/100/SUM(if(flag='flag_5000_10000',plaque_cnt,0)))*1000,2) plaque_ecpm_16,
        ROUND(SUM(if(flag='flag_5000_10000',video_cnt,0))/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) video_ipu_16,
        ROUND((SUM(if(flag='flag_5000_10000',video_revenue,0))/100/SUM(if(flag='flag_5000_10000',video_cnt,0)))*1000,2) video_ecpm_16,


        IFNULL(SUM(if(flag='flag_10000',user_cnt,0)),0)  user_cnt_17,
        ROUND(SUM(if(flag='flag_10000',iaa_revenue,0))/100/SUM(if(flag='flag_10000',user_cnt,0)),2) ad_arpu_17,
        ROUND(SUM(if(flag='flag_10000',plaque_cnt,0))/SUM(if(flag='flag_10000',user_cnt,0)),2) plaque_ipu_17,
        ROUND((SUM(if(flag='flag_10000',plaque_revenue,0))/100/SUM(if(flag='flag_10000',plaque_cnt,0)))*1000,2) plaque_ecpm_17,
        ROUND(SUM(if(flag='flag_10000',video_cnt,0))/SUM(if(flag='flag_10000',user_cnt,0)),2) video_ipu_17,
        ROUND((SUM(if(flag='flag_10000',video_revenue,0))/100/SUM(if(flag='flag_10000',video_cnt,0)))*1000,2) video_ecpm_17


        from dnwx_bi.ads_user_ad_price_today_daily where 1=1
        and DATE(t_date) <![CDATA[>=]]> #{start_date} and DATE(t_date) <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by t_date asc ,iaa_revenue desc
            </otherwise>
        </choose>
    </select>


    <select id="getPayAdValueSum" resultType="com.wbgame.pojo.game.PayAdValueVo" parameterType="java.util.Map">
        select
        FORMAT((SUM(iaa_revenue)/100/SUM(user_cnt)),2) total_ad_arpu,
        FORMAT((SUM(plaque_cnt)/SUM(user_cnt)),2) total_plaque_ipu,
        FORMAT((SUM(plaque_revenue)/100/SUM(plaque_cnt))*1000,2) total_plaque_ecpm,
        FORMAT((SUM(video_cnt)/SUM(user_cnt)),2) total_video_ipu,
        FORMAT((SUM(video_revenue)/100/SUM(video_cnt))*1000,2) total_video_ecpm,

        IFNULL(SUM(if(flag='flag_0',user_cnt,0)),0)  user_cnt_1,
        FORMAT(SUM(if(flag='flag_0',iaa_revenue,0))/100/SUM(if(flag='flag_0',user_cnt,0)),2) ad_arpu_1,
        FORMAT(SUM(if(flag='flag_0',plaque_cnt,0))/SUM(if(flag='flag_0',user_cnt,0)),2) plaque_ipu_1,
        FORMAT((SUM(if(flag='flag_0',plaque_revenue,0))/100/SUM(if(flag='flag_0',plaque_cnt,0)))*1000,2) plaque_ecpm_1,
        FORMAT(SUM(if(flag='flag_0',video_cnt,0))/SUM(if(flag='flag_0',user_cnt,0)),2) video_ipu_1,
        FORMAT((SUM(if(flag='flag_0',video_revenue,0))/100/SUM(if(flag='flag_0',video_cnt,0)))*1000,2) video_ecpm_1,

        IFNULL(SUM(if(flag='flag_0_1',user_cnt,0)),0)  user_cnt_2,
        FORMAT(SUM(if(flag='flag_0_1',iaa_revenue,0))/100/SUM(if(flag='flag_0_1',user_cnt,0)),2) ad_arpu_2,
        FORMAT(SUM(if(flag='flag_0_1',plaque_cnt,0))/SUM(if(flag='flag_0_1',user_cnt,0)),2) plaque_ipu_2,
        FORMAT((SUM(if(flag='flag_0_1',plaque_revenue,0))/100/SUM(if(flag='flag_0_1',plaque_cnt,0)))*1000,2) plaque_ecpm_2,
        FORMAT(SUM(if(flag='flag_0_1',video_cnt,0))/SUM(if(flag='flag_0_1',user_cnt,0)),2) video_ipu_2,
        FORMAT((SUM(if(flag='flag_0_1',video_revenue,0))/100/SUM(if(flag='flag_0_1',video_cnt,0)))*1000,2) video_ecpm_2,

        IFNULL(SUM(if(flag='flag_1_3',user_cnt,0)),0)  user_cnt_3,
        FORMAT(SUM(if(flag='flag_1_3',iaa_revenue,0))/100/SUM(if(flag='flag_1_3',user_cnt,0)),2) ad_arpu_3,
        FORMAT(SUM(if(flag='flag_1_3',plaque_cnt,0))/SUM(if(flag='flag_1_3',user_cnt,0)),2) plaque_ipu_3,
        FORMAT((SUM(if(flag='flag_1_3',plaque_revenue,0))/100/SUM(if(flag='flag_1_3',plaque_cnt,0)))*1000,2) plaque_ecpm_3,
        FORMAT(SUM(if(flag='flag_1_3',video_cnt,0))/SUM(if(flag='flag_1_3',user_cnt,0)),2) video_ipu_3,
        FORMAT((SUM(if(flag='flag_1_3',video_revenue,0))/100/SUM(if(flag='flag_1_3',video_cnt,0)))*1000,2) video_ecpm_3,

        IFNULL(SUM(if(flag='flag_3_6',user_cnt,0)),0)  user_cnt_4,
        FORMAT(SUM(if(flag='flag_3_6',iaa_revenue,0))/100/SUM(if(flag='flag_3_6',user_cnt,0)),2) ad_arpu_4,
        FORMAT(SUM(if(flag='flag_3_6',plaque_cnt,0))/SUM(if(flag='flag_3_6',user_cnt,0)),2) plaque_ipu_4,
        FORMAT((SUM(if(flag='flag_3_6',plaque_revenue,0))/100/SUM(if(flag='flag_3_6',plaque_cnt,0)))*1000,2) plaque_ecpm_4,
        FORMAT(SUM(if(flag='flag_3_6',video_cnt,0))/SUM(if(flag='flag_3_6',user_cnt,0)),2) video_ipu_4,
        FORMAT((SUM(if(flag='flag_3_6',video_revenue,0))/100/SUM(if(flag='flag_3_6',video_cnt,0)))*1000,2) video_ecpm_4,

        IFNULL(SUM(if(flag='flag_6_12',user_cnt,0)),0)  user_cnt_5,
        FORMAT(SUM(if(flag='flag_6_12',iaa_revenue,0))/100/SUM(if(flag='flag_6_12',user_cnt,0)),2) ad_arpu_5,
        FORMAT(SUM(if(flag='flag_6_12',plaque_cnt,0))/SUM(if(flag='flag_6_12',user_cnt,0)),2) plaque_ipu_5,
        FORMAT((SUM(if(flag='flag_6_12',plaque_revenue,0))/100/SUM(if(flag='flag_6_12',plaque_cnt,0)))*1000,2) plaque_ecpm_5,
        FORMAT(SUM(if(flag='flag_6_12',video_cnt,0))/SUM(if(flag='flag_6_12',user_cnt,0)),2) video_ipu_5,
        FORMAT((SUM(if(flag='flag_6_12',video_revenue,0))/100/SUM(if(flag='flag_6_12',video_cnt,0)))*1000,2) video_ecpm_5,

        IFNULL(SUM(if(flag='flag_12_24',user_cnt,0)),0)  user_cnt_6,
        FORMAT(SUM(if(flag='flag_12_24',iaa_revenue,0))/100/SUM(if(flag='flag_12_24',user_cnt,0)),2) ad_arpu_6,
        FORMAT(SUM(if(flag='flag_12_24',plaque_cnt,0))/SUM(if(flag='flag_12_24',user_cnt,0)),2) plaque_ipu_6,
        FORMAT((SUM(if(flag='flag_12_24',plaque_revenue,0))/100/SUM(if(flag='flag_12_24',plaque_cnt,0)))*1000,2) plaque_ecpm_6,
        FORMAT(SUM(if(flag='flag_12_24',video_cnt,0))/SUM(if(flag='flag_12_24',user_cnt,0)),2) video_ipu_6,
        FORMAT((SUM(if(flag='flag_12_24',video_revenue,0))/100/SUM(if(flag='flag_12_24',video_cnt,0)))*1000,2) video_ecpm_6,

        IFNULL(SUM(if(flag='flag_24_36',user_cnt,0)),0)  user_cnt_7,
        FORMAT(SUM(if(flag='flag_24_36',iaa_revenue,0))/100/SUM(if(flag='flag_24_36',user_cnt,0)),2) ad_arpu_7,
        FORMAT(SUM(if(flag='flag_24_36',plaque_cnt,0))/SUM(if(flag='flag_24_36',user_cnt,0)),2) plaque_ipu_7,
        FORMAT((SUM(if(flag='flag_24_36',plaque_revenue,0))/100/SUM(if(flag='flag_24_36',plaque_cnt,0)))*1000,2) plaque_ecpm_7,
        FORMAT(SUM(if(flag='flag_24_36',video_cnt,0))/SUM(if(flag='flag_24_36',user_cnt,0)),2) video_ipu_7,
        FORMAT((SUM(if(flag='flag_24_36',video_revenue,0))/100/SUM(if(flag='flag_24_36',video_cnt,0)))*1000,2) video_ecpm_7,

        IFNULL(SUM(if(flag='flag_36_68',user_cnt,0)),0)  user_cnt_8,
        FORMAT(SUM(if(flag='flag_36_68',iaa_revenue,0))/100/SUM(if(flag='flag_36_68',user_cnt,0)),2) ad_arpu_8,
        FORMAT(SUM(if(flag='flag_36_68',plaque_cnt,0))/SUM(if(flag='flag_36_68',user_cnt,0)),2) plaque_ipu_8,
        FORMAT((SUM(if(flag='flag_36_68',plaque_revenue,0))/100/SUM(if(flag='flag_36_68',plaque_cnt,0)))*1000,2) plaque_ecpm_8,
        FORMAT(SUM(if(flag='flag_36_68',video_cnt,0))/SUM(if(flag='flag_36_68',user_cnt,0)),2) video_ipu_8,
        FORMAT((SUM(if(flag='flag_36_68',video_revenue,0))/100/SUM(if(flag='flag_36_68',video_cnt,0)))*1000,2) video_ecpm_8,

        IFNULL(SUM(if(flag='flag_68_128',user_cnt,0)),0)  user_cnt_9,
        FORMAT(SUM(if(flag='flag_68_128',iaa_revenue,0))/100/SUM(if(flag='flag_68_128',user_cnt,0)),2) ad_arpu_9,
        FORMAT(SUM(if(flag='flag_68_128',plaque_cnt,0))/SUM(if(flag='flag_68_128',user_cnt,0)),2) plaque_ipu_9,
        FORMAT((SUM(if(flag='flag_68_128',plaque_revenue,0))/100/SUM(if(flag='flag_68_128',plaque_cnt,0)))*1000,2) plaque_ecpm_9,
        FORMAT(SUM(if(flag='flag_68_128',video_cnt,0))/SUM(if(flag='flag_68_128',user_cnt,0)),2) video_ipu_9,
        FORMAT((SUM(if(flag='flag_68_128',video_revenue,0))/100/SUM(if(flag='flag_68_128',video_cnt,0)))*1000,2) video_ecpm_9,

        IFNULL(SUM(if(flag='flag_128_198',user_cnt,0)),0)  user_cnt_10,
        FORMAT(SUM(if(flag='flag_128_198',iaa_revenue,0))/100/SUM(if(flag='flag_128_198',user_cnt,0)),2) ad_arpu_10,
        FORMAT(SUM(if(flag='flag_128_198',plaque_cnt,0))/SUM(if(flag='flag_128_198',user_cnt,0)),2) plaque_ipu_10,
        FORMAT((SUM(if(flag='flag_128_198',plaque_revenue,0))/100/SUM(if(flag='flag_128_198',plaque_cnt,0)))*1000,2) plaque_ecpm_10,
        FORMAT(SUM(if(flag='flag_128_198',video_cnt,0))/SUM(if(flag='flag_128_198',user_cnt,0)),2) video_ipu_10,
        FORMAT((SUM(if(flag='flag_128_198',video_revenue,0))/100/SUM(if(flag='flag_128_198',video_cnt,0)))*1000,2) video_ecpm_10,

        IFNULL(SUM(if(flag='flag_198',user_cnt,0)),0)  user_cnt_11,
        FORMAT(SUM(if(flag='flag_198',iaa_revenue,0))/100/SUM(if(flag='flag_198',user_cnt,0)),2) ad_arpu_11,
        FORMAT(SUM(if(flag='flag_198',plaque_cnt,0))/SUM(if(flag='flag_198',user_cnt,0)),2) plaque_ipu_11,
        FORMAT((SUM(if(flag='flag_198',plaque_revenue,0))/100/SUM(if(flag='flag_198',plaque_cnt,0)))*1000,2) plaque_ecpm_11,
        FORMAT(SUM(if(flag='flag_198',video_cnt,0))/SUM(if(flag='flag_198',user_cnt,0)),2) video_ipu_11,
        FORMAT((SUM(if(flag='flag_198',video_revenue,0))/100/SUM(if(flag='flag_198',video_cnt,0)))*1000,2) video_ecpm_11,

        IFNULL(SUM(if(flag='flag_198_328',user_cnt,0)),0)  user_cnt_12,
        FORMAT(SUM(if(flag='flag_198_328',iaa_revenue,0))/100/SUM(if(flag='flag_198_328',user_cnt,0)),2) ad_arpu_12,
        FORMAT(SUM(if(flag='flag_198_328',plaque_cnt,0))/SUM(if(flag='flag_198_328',user_cnt,0)),2) plaque_ipu_12,
        FORMAT((SUM(if(flag='flag_198_328',plaque_revenue,0))/100/SUM(if(flag='flag_198_328',plaque_cnt,0)))*1000,2) plaque_ecpm_12,
        FORMAT(SUM(if(flag='flag_198_328',video_cnt,0))/SUM(if(flag='flag_198_328',user_cnt,0)),2) video_ipu_12,
        FORMAT((SUM(if(flag='flag_198_328',video_revenue,0))/100/SUM(if(flag='flag_198_328',video_cnt,0)))*1000,2) video_ecpm_12,

        IFNULL(SUM(if(flag='flag_328_648',user_cnt,0)),0)  user_cnt_13,
        FORMAT(SUM(if(flag='flag_328_648',iaa_revenue,0))/100/SUM(if(flag='flag_328_648',user_cnt,0)),2) ad_arpu_13,
        FORMAT(SUM(if(flag='flag_328_648',plaque_cnt,0))/SUM(if(flag='flag_328_648',user_cnt,0)),2) plaque_ipu_13,
        FORMAT((SUM(if(flag='flag_328_648',plaque_revenue,0))/100/SUM(if(flag='flag_328_648',plaque_cnt,0)))*1000,2) plaque_ecpm_13,
        FORMAT(SUM(if(flag='flag_328_648',video_cnt,0))/SUM(if(flag='flag_328_648',user_cnt,0)),2) video_ipu_13,
        FORMAT((SUM(if(flag='flag_328_648',video_revenue,0))/100/SUM(if(flag='flag_328_648',video_cnt,0)))*1000,2) video_ecpm_13,

        IFNULL(SUM(if(flag='flag_648_3000',user_cnt,0)),0)  user_cnt_14,
        FORMAT(SUM(if(flag='flag_648_3000',iaa_revenue,0))/100/SUM(if(flag='flag_648_3000',user_cnt,0)),2) ad_arpu_14,
        FORMAT(SUM(if(flag='flag_648_3000',plaque_cnt,0))/SUM(if(flag='flag_648_3000',user_cnt,0)),2) plaque_ipu_14,
        FORMAT((SUM(if(flag='flag_648_3000',plaque_revenue,0))/100/SUM(if(flag='flag_648_3000',plaque_cnt,0)))*1000,2) plaque_ecpm_14,
        FORMAT(SUM(if(flag='flag_648_3000',video_cnt,0))/SUM(if(flag='flag_648_3000',user_cnt,0)),2) video_ipu_14,
        FORMAT((SUM(if(flag='flag_648_3000',video_revenue,0))/100/SUM(if(flag='flag_648_3000',video_cnt,0)))*1000,2) video_ecpm_14,

        IFNULL(SUM(if(flag='flag_3000_5000',user_cnt,0)),0)  user_cnt_15,
        FORMAT(SUM(if(flag='flag_3000_5000',iaa_revenue,0))/100/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) ad_arpu_15,
        FORMAT(SUM(if(flag='flag_3000_5000',plaque_cnt,0))/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) plaque_ipu_15,
        FORMAT((SUM(if(flag='flag_3000_5000',plaque_revenue,0))/100/SUM(if(flag='flag_3000_5000',plaque_cnt,0)))*1000,2) plaque_ecpm_15,
        FORMAT(SUM(if(flag='flag_3000_5000',video_cnt,0))/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) video_ipu_15,
        FORMAT((SUM(if(flag='flag_3000_5000',video_revenue,0))/100/SUM(if(flag='flag_3000_5000',video_cnt,0)))*1000,2) video_ecpm_15,

        IFNULL(SUM(if(flag='flag_5000_10000',user_cnt,0)),0)  user_cnt_16,
        FORMAT(SUM(if(flag='flag_5000_10000',iaa_revenue,0))/100/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) ad_arpu_16,
        FORMAT(SUM(if(flag='flag_5000_10000',plaque_cnt,0))/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) plaque_ipu_16,
        FORMAT((SUM(if(flag='flag_5000_10000',plaque_revenue,0))/100/SUM(if(flag='flag_5000_10000',plaque_cnt,0)))*1000,2) plaque_ecpm_16,
        FORMAT(SUM(if(flag='flag_5000_10000',video_cnt,0))/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) video_ipu_16,
        FORMAT((SUM(if(flag='flag_5000_10000',video_revenue,0))/100/SUM(if(flag='flag_5000_10000',video_cnt,0)))*1000,2) video_ecpm_16,

        IFNULL(SUM(if(flag='flag_10000',user_cnt,0)),0)  user_cnt_17,
        FORMAT(SUM(if(flag='flag_10000',iaa_revenue,0))/100/SUM(if(flag='flag_10000',user_cnt,0)),2) ad_arpu_17,
        FORMAT(SUM(if(flag='flag_10000',plaque_cnt,0))/SUM(if(flag='flag_10000',user_cnt,0)),2) plaque_ipu_17,
        FORMAT((SUM(if(flag='flag_10000',plaque_revenue,0))/100/SUM(if(flag='flag_10000',plaque_cnt,0)))*1000,2) plaque_ecpm_17,
        FORMAT(SUM(if(flag='flag_10000',video_cnt,0))/SUM(if(flag='flag_10000',user_cnt,0)),2) video_ipu_17,
        FORMAT((SUM(if(flag='flag_10000',video_revenue,0))/100/SUM(if(flag='flag_10000',video_cnt,0)))*1000,2) video_ecpm_17




        from dnwx_bi.ads_user_ad_price_today_daily where 1=1
        and DATE(t_date) <![CDATA[>=]]> #{start_date} and DATE(t_date) <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
    </select>


    <select id="getPayAdValueDayReportList" resultType="com.wbgame.pojo.game.report.PayAdValueReportVo" parameterType="java.util.Map">
        select
        IFNULL(SUM(if(flag='flag_0',user_cnt,0)),0)  user_cnt_1,
        ROUND(SUM(if(flag='flag_0',iaa_revenue,0))/100/SUM(if(flag='flag_0',user_cnt,0)),2) ad_arpu_1,
        ROUND(SUM(if(flag='flag_0',plaque_cnt,0))/SUM(if(flag='flag_0',user_cnt,0)),2) plaque_ipu_1,
        ROUND((SUM(if(flag='flag_0',plaque_revenue,0))/100/SUM(if(flag='flag_0',plaque_cnt,0)))*1000,2) plaque_ecpm_1,
        ROUND(SUM(if(flag='flag_0',video_cnt,0))/SUM(if(flag='flag_0',user_cnt,0)),2) video_ipu_1,
        ROUND((SUM(if(flag='flag_0',video_revenue,0))/100/SUM(if(flag='flag_0',video_cnt,0)))*1000,2) video_ecpm_1,

        IFNULL(SUM(if(flag='flag_0_1',user_cnt,0)),0)  user_cnt_2,
        ROUND(SUM(if(flag='flag_0_1',iaa_revenue,0))/100/SUM(if(flag='flag_0_1',user_cnt,0)),2) ad_arpu_2,
        ROUND(SUM(if(flag='flag_0_1',plaque_cnt,0))/SUM(if(flag='flag_0_1',user_cnt,0)),2) plaque_ipu_2,
        ROUND((SUM(if(flag='flag_0_1',plaque_revenue,0))/100/SUM(if(flag='flag_0_1',plaque_cnt,0)))*1000,2) plaque_ecpm_2,
        ROUND(SUM(if(flag='flag_0_1',video_cnt,0))/SUM(if(flag='flag_0_1',user_cnt,0)),2) video_ipu_2,
        ROUND((SUM(if(flag='flag_0_1',video_revenue,0))/100/SUM(if(flag='flag_0_1',video_cnt,0)))*1000,2) video_ecpm_2,

        IFNULL(SUM(if(flag='flag_1_3',user_cnt,0)),0)  user_cnt_3,
        ROUND(SUM(if(flag='flag_1_3',iaa_revenue,0))/100/SUM(if(flag='flag_1_3',user_cnt,0)),2) ad_arpu_3,
        ROUND(SUM(if(flag='flag_1_3',plaque_cnt,0))/SUM(if(flag='flag_1_3',user_cnt,0)),2) plaque_ipu_3,
        ROUND((SUM(if(flag='flag_1_3',plaque_revenue,0))/100/SUM(if(flag='flag_1_3',plaque_cnt,0)))*1000,2) plaque_ecpm_3,
        ROUND(SUM(if(flag='flag_1_3',video_cnt,0))/SUM(if(flag='flag_1_3',user_cnt,0)),2) video_ipu_3,
        ROUND((SUM(if(flag='flag_1_3',video_revenue,0))/100/SUM(if(flag='flag_1_3',video_cnt,0)))*1000,2) video_ecpm_3,

        IFNULL(SUM(if(flag='flag_3_6',user_cnt,0)),0)  user_cnt_4,
        ROUND(SUM(if(flag='flag_3_6',iaa_revenue,0))/100/SUM(if(flag='flag_3_6',user_cnt,0)),2) ad_arpu_4,
        ROUND(SUM(if(flag='flag_3_6',plaque_cnt,0))/SUM(if(flag='flag_3_6',user_cnt,0)),2) plaque_ipu_4,
        ROUND((SUM(if(flag='flag_3_6',plaque_revenue,0))/100/SUM(if(flag='flag_3_6',plaque_cnt,0)))*1000,2) plaque_ecpm_4,
        ROUND(SUM(if(flag='flag_3_6',video_cnt,0))/SUM(if(flag='flag_3_6',user_cnt,0)),2) video_ipu_4,
        ROUND((SUM(if(flag='flag_3_6',video_revenue,0))/100/SUM(if(flag='flag_3_6',video_cnt,0)))*1000,2) video_ecpm_4,

        IFNULL(SUM(if(flag='flag_6_12',user_cnt,0)),0)  user_cnt_5,
        ROUND(SUM(if(flag='flag_6_12',iaa_revenue,0))/100/SUM(if(flag='flag_6_12',user_cnt,0)),2) ad_arpu_5,
        ROUND(SUM(if(flag='flag_6_12',plaque_cnt,0))/SUM(if(flag='flag_6_12',user_cnt,0)),2) plaque_ipu_5,
        ROUND((SUM(if(flag='flag_6_12',plaque_revenue,0))/100/SUM(if(flag='flag_6_12',plaque_cnt,0)))*1000,2) plaque_ecpm_5,
        ROUND(SUM(if(flag='flag_6_12',video_cnt,0))/SUM(if(flag='flag_6_12',user_cnt,0)),2) video_ipu_5,
        ROUND((SUM(if(flag='flag_6_12',video_revenue,0))/100/SUM(if(flag='flag_6_12',video_cnt,0)))*1000,2) video_ecpm_5,

        IFNULL(SUM(if(flag='flag_12_24',user_cnt,0)),0)  user_cnt_6,
        ROUND(SUM(if(flag='flag_12_24',iaa_revenue,0))/100/SUM(if(flag='flag_12_24',user_cnt,0)),2) ad_arpu_6,
        ROUND(SUM(if(flag='flag_12_24',plaque_cnt,0))/SUM(if(flag='flag_12_24',user_cnt,0)),2) plaque_ipu_6,
        ROUND((SUM(if(flag='flag_12_24',plaque_revenue,0))/100/SUM(if(flag='flag_12_24',plaque_cnt,0)))*1000,2) plaque_ecpm_6,
        ROUND(SUM(if(flag='flag_12_24',video_cnt,0))/SUM(if(flag='flag_12_24',user_cnt,0)),2) video_ipu_6,
        ROUND((SUM(if(flag='flag_12_24',video_revenue,0))/100/SUM(if(flag='flag_12_24',video_cnt,0)))*1000,2) video_ecpm_6,

        IFNULL(SUM(if(flag='flag_24_36',user_cnt,0)),0)  user_cnt_7,
        ROUND(SUM(if(flag='flag_24_36',iaa_revenue,0))/100/SUM(if(flag='flag_24_36',user_cnt,0)),2) ad_arpu_7,
        ROUND(SUM(if(flag='flag_24_36',plaque_cnt,0))/SUM(if(flag='flag_24_36',user_cnt,0)),2) plaque_ipu_7,
        ROUND((SUM(if(flag='flag_24_36',plaque_revenue,0))/100/SUM(if(flag='flag_24_36',plaque_cnt,0)))*1000,2) plaque_ecpm_7,
        ROUND(SUM(if(flag='flag_24_36',video_cnt,0))/SUM(if(flag='flag_24_36',user_cnt,0)),2) video_ipu_7,
        ROUND((SUM(if(flag='flag_24_36',video_revenue,0))/100/SUM(if(flag='flag_24_36',video_cnt,0)))*1000,2) video_ecpm_7,

        IFNULL(SUM(if(flag='flag_36_68',user_cnt,0)),0)  user_cnt_8,
        ROUND(SUM(if(flag='flag_36_68',iaa_revenue,0))/100/SUM(if(flag='flag_36_68',user_cnt,0)),2) ad_arpu_8,
        ROUND(SUM(if(flag='flag_36_68',plaque_cnt,0))/SUM(if(flag='flag_36_68',user_cnt,0)),2) plaque_ipu_8,
        ROUND((SUM(if(flag='flag_36_68',plaque_revenue,0))/100/SUM(if(flag='flag_36_68',plaque_cnt,0)))*1000,2) plaque_ecpm_8,
        ROUND(SUM(if(flag='flag_36_68',video_cnt,0))/SUM(if(flag='flag_36_68',user_cnt,0)),2) video_ipu_8,
        ROUND((SUM(if(flag='flag_36_68',video_revenue,0))/100/SUM(if(flag='flag_36_68',video_cnt,0)))*1000,2) video_ecpm_8,

        IFNULL(SUM(if(flag='flag_68_128',user_cnt,0)),0)  user_cnt_9,
        ROUND(SUM(if(flag='flag_68_128',iaa_revenue,0))/100/SUM(if(flag='flag_68_128',user_cnt,0)),2) ad_arpu_9,
        ROUND(SUM(if(flag='flag_68_128',plaque_cnt,0))/SUM(if(flag='flag_68_128',user_cnt,0)),2) plaque_ipu_9,
        ROUND((SUM(if(flag='flag_68_128',plaque_revenue,0))/100/SUM(if(flag='flag_68_128',plaque_cnt,0)))*1000,2) plaque_ecpm_9,
        ROUND(SUM(if(flag='flag_68_128',video_cnt,0))/SUM(if(flag='flag_68_128',user_cnt,0)),2) video_ipu_9,
        ROUND((SUM(if(flag='flag_68_128',video_revenue,0))/100/SUM(if(flag='flag_68_128',video_cnt,0)))*1000,2) video_ecpm_9,

        IFNULL(SUM(if(flag='flag_128_198',user_cnt,0)),0)  user_cnt_10,
        ROUND(SUM(if(flag='flag_128_198',iaa_revenue,0))/100/SUM(if(flag='flag_128_198',user_cnt,0)),2) ad_arpu_10,
        ROUND(SUM(if(flag='flag_128_198',plaque_cnt,0))/SUM(if(flag='flag_128_198',user_cnt,0)),2) plaque_ipu_10,
        ROUND((SUM(if(flag='flag_128_198',plaque_revenue,0))/100/SUM(if(flag='flag_128_198',plaque_cnt,0)))*1000,2) plaque_ecpm_10,
        ROUND(SUM(if(flag='flag_128_198',video_cnt,0))/SUM(if(flag='flag_128_198',user_cnt,0)),2) video_ipu_10,
        ROUND((SUM(if(flag='flag_128_198',video_revenue,0))/100/SUM(if(flag='flag_128_198',video_cnt,0)))*1000,2) video_ecpm_10,

        IFNULL(SUM(if(flag='flag_198',user_cnt,0)),0)  user_cnt_11,
        ROUND(SUM(if(flag='flag_198',iaa_revenue,0))/100/SUM(if(flag='flag_198',user_cnt,0)),2) ad_arpu_11,
        ROUND(SUM(if(flag='flag_198',plaque_cnt,0))/SUM(if(flag='flag_198',user_cnt,0)),2) plaque_ipu_11,
        ROUND((SUM(if(flag='flag_198',plaque_revenue,0))/100/SUM(if(flag='flag_198',plaque_cnt,0)))*1000,2) plaque_ecpm_11,
        ROUND(SUM(if(flag='flag_198',video_cnt,0))/SUM(if(flag='flag_198',user_cnt,0)),2) video_ipu_11,
        ROUND((SUM(if(flag='flag_198',video_revenue,0))/100/SUM(if(flag='flag_198',video_cnt,0)))*1000,2) video_ecpm_11,

        IFNULL(SUM(if(flag='flag_198_328',user_cnt,0)),0)  user_cnt_12,
        ROUND(SUM(if(flag='flag_198_328',iaa_revenue,0))/100/SUM(if(flag='flag_198_328',user_cnt,0)),2) ad_arpu_12,
        ROUND(SUM(if(flag='flag_198_328',plaque_cnt,0))/SUM(if(flag='flag_198_328',user_cnt,0)),2) plaque_ipu_12,
        ROUND((SUM(if(flag='flag_198_328',plaque_revenue,0))/100/SUM(if(flag='flag_198_328',plaque_cnt,0)))*1000,2) plaque_ecpm_12,
        ROUND(SUM(if(flag='flag_198_328',video_cnt,0))/SUM(if(flag='flag_198_328',user_cnt,0)),2) video_ipu_12,
        ROUND((SUM(if(flag='flag_198_328',video_revenue,0))/100/SUM(if(flag='flag_198_328',video_cnt,0)))*1000,2) video_ecpm_12,


        IFNULL(SUM(if(flag='flag_328_648',user_cnt,0)),0)  user_cnt_13,
        ROUND(SUM(if(flag='flag_328_648',iaa_revenue,0))/100/SUM(if(flag='flag_328_648',user_cnt,0)),2) ad_arpu_13,
        ROUND(SUM(if(flag='flag_328_648',plaque_cnt,0))/SUM(if(flag='flag_328_648',user_cnt,0)),2) plaque_ipu_13,
        ROUND((SUM(if(flag='flag_328_648',plaque_revenue,0))/100/SUM(if(flag='flag_328_648',plaque_cnt,0)))*1000,2) plaque_ecpm_13,
        ROUND(SUM(if(flag='flag_328_648',video_cnt,0))/SUM(if(flag='flag_328_648',user_cnt,0)),2) video_ipu_13,
        ROUND((SUM(if(flag='flag_328_648',video_revenue,0))/100/SUM(if(flag='flag_328_648',video_cnt,0)))*1000,2) video_ecpm_13,


        IFNULL(SUM(if(flag='flag_648_3000',user_cnt,0)),0)  user_cnt_14,
        ROUND(SUM(if(flag='flag_648_3000',iaa_revenue,0))/100/SUM(if(flag='flag_648_3000',user_cnt,0)),2) ad_arpu_14,
        ROUND(SUM(if(flag='flag_648_3000',plaque_cnt,0))/SUM(if(flag='flag_648_3000',user_cnt,0)),2) plaque_ipu_14,
        ROUND((SUM(if(flag='flag_648_3000',plaque_revenue,0))/100/SUM(if(flag='flag_648_3000',plaque_cnt,0)))*1000,2) plaque_ecpm_14,
        ROUND(SUM(if(flag='flag_648_3000',video_cnt,0))/SUM(if(flag='flag_648_3000',user_cnt,0)),2) video_ipu_14,
        ROUND((SUM(if(flag='flag_648_3000',video_revenue,0))/100/SUM(if(flag='flag_648_3000',video_cnt,0)))*1000,2) video_ecpm_14,


        IFNULL(SUM(if(flag='flag_3000_5000',user_cnt,0)),0)  user_cnt_15,
        ROUND(SUM(if(flag='flag_3000_5000',iaa_revenue,0))/100/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) ad_arpu_15,
        ROUND(SUM(if(flag='flag_3000_5000',plaque_cnt,0))/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) plaque_ipu_15,
        ROUND((SUM(if(flag='flag_3000_5000',plaque_revenue,0))/100/SUM(if(flag='flag_3000_5000',plaque_cnt,0)))*1000,2) plaque_ecpm_15,
        ROUND(SUM(if(flag='flag_3000_5000',video_cnt,0))/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) video_ipu_15,
        ROUND((SUM(if(flag='flag_3000_5000',video_revenue,0))/100/SUM(if(flag='flag_3000_5000',video_cnt,0)))*1000,2) video_ecpm_15,


        IFNULL(SUM(if(flag='flag_5000_10000',user_cnt,0)),0)  user_cnt_16,
        ROUND(SUM(if(flag='flag_5000_10000',iaa_revenue,0))/100/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) ad_arpu_16,
        ROUND(SUM(if(flag='flag_5000_10000',plaque_cnt,0))/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) plaque_ipu_16,
        ROUND((SUM(if(flag='flag_5000_10000',plaque_revenue,0))/100/SUM(if(flag='flag_5000_10000',plaque_cnt,0)))*1000,2) plaque_ecpm_16,
        ROUND(SUM(if(flag='flag_5000_10000',video_cnt,0))/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) video_ipu_16,
        ROUND((SUM(if(flag='flag_5000_10000',video_revenue,0))/100/SUM(if(flag='flag_5000_10000',video_cnt,0)))*1000,2) video_ecpm_16,


        IFNULL(SUM(if(flag='flag_10000',user_cnt,0)),0)  user_cnt_17,
        ROUND(SUM(if(flag='flag_10000',iaa_revenue,0))/100/SUM(if(flag='flag_10000',user_cnt,0)),2) ad_arpu_17,
        ROUND(SUM(if(flag='flag_10000',plaque_cnt,0))/SUM(if(flag='flag_10000',user_cnt,0)),2) plaque_ipu_17,
        ROUND((SUM(if(flag='flag_10000',plaque_revenue,0))/100/SUM(if(flag='flag_10000',plaque_cnt,0)))*1000,2) plaque_ecpm_17,
        ROUND(SUM(if(flag='flag_10000',video_cnt,0))/SUM(if(flag='flag_10000',user_cnt,0)),2) video_ipu_17,
        ROUND((SUM(if(flag='flag_10000',video_revenue,0))/100/SUM(if(flag='flag_10000',video_cnt,0)))*1000,2) video_ecpm_17
        from dnwx_bi.ads_user_ad_price_today_daily
        where 1=1 and DATE(t_date) <![CDATA[>=]]> #{start_date} and DATE(t_date) <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>

    </select>


    <select id="getPayAdValueTotalList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.PayAdValueVo">
        select
        t_date,appid,pid,download_channel,
        ROUND((SUM(iaa_revenue)/100/SUM(user_cnt)),2) total_ad_arpu,
        ROUND((SUM(plaque_cnt)/SUM(user_cnt)),2) total_plaque_ipu,
        ROUND((SUM(plaque_revenue)/100/SUM(plaque_cnt))*1000,2) total_plaque_ecpm,
        ROUND((SUM(video_cnt)/SUM(user_cnt)),2) total_video_ipu,
        ROUND((SUM(video_revenue)/100/SUM(video_cnt))*1000,2) total_video_ecpm,

        IFNULL(SUM(if(flag='flag_0',user_cnt,0)),0) user_cnt_1,
        ROUND(SUM(if(flag='flag_0',iaa_revenue,0))/100/SUM(if(flag='flag_0',user_cnt,0)),2) ad_arpu_1,
        ROUND(SUM(if(flag='flag_0',plaque_cnt,0))/SUM(if(flag='flag_0',user_cnt,0)),2) plaque_ipu_1,
        ROUND((SUM(if(flag='flag_0',plaque_revenue,0))/100/SUM(if(flag='flag_0',plaque_cnt,0)))*1000,2) plaque_ecpm_1,
        ROUND(SUM(if(flag='flag_0',video_cnt,0))/SUM(if(flag='flag_0',user_cnt,0)),2) video_ipu_1,
        ROUND((SUM(if(flag='flag_0',video_revenue,0))/100/SUM(if(flag='flag_0',video_cnt,0)))*1000,2) video_ecpm_1,

        IFNULL(SUM(if(flag='flag_0_1',user_cnt,0)),0) user_cnt_2,
        ROUND(SUM(if(flag='flag_0_1',iaa_revenue,0))/100/SUM(if(flag='flag_0_1',user_cnt,0)),2) ad_arpu_2,
        ROUND(SUM(if(flag='flag_0_1',plaque_cnt,0))/SUM(if(flag='flag_0_1',user_cnt,0)),2) plaque_ipu_2,
        ROUND((SUM(if(flag='flag_0_1',plaque_revenue,0))/100/SUM(if(flag='flag_0_1',plaque_cnt,0)))*1000,2) plaque_ecpm_2,
        ROUND(SUM(if(flag='flag_0_1',video_cnt,0))/SUM(if(flag='flag_0_1',user_cnt,0)),2) video_ipu_2,
        ROUND((SUM(if(flag='flag_0_1',video_revenue,0))/100/SUM(if(flag='flag_0_1',video_cnt,0)))*1000,2) video_ecpm_2,

        IFNULL(SUM(if(flag='flag_1_6',user_cnt,0)),0) user_cnt_3,
        ROUND(SUM(if(flag='flag_1_6',iaa_revenue,0))/100/SUM(if(flag='flag_1_6',user_cnt,0)),2) ad_arpu_3,
        ROUND(SUM(if(flag='flag_1_6',plaque_cnt,0))/SUM(if(flag='flag_1_6',user_cnt,0)),2) plaque_ipu_3,
        ROUND((SUM(if(flag='flag_1_6',plaque_revenue,0))/100/SUM(if(flag='flag_1_6',plaque_cnt,0)))*1000,2) plaque_ecpm_3,
        ROUND(SUM(if(flag='flag_1_6',video_cnt,0))/SUM(if(flag='flag_1_6',user_cnt,0)),2) video_ipu_3,
        ROUND((SUM(if(flag='flag_1_6',video_revenue,0))/100/SUM(if(flag='flag_1_6',video_cnt,0)))*1000,2) video_ecpm_3,

        IFNULL(SUM(if(flag='flag_6_12',user_cnt,0)),0) user_cnt_4,
        ROUND(SUM(if(flag='flag_6_12',iaa_revenue,0))/100/SUM(if(flag='flag_6_12',user_cnt,0)),2) ad_arpu_4,
        ROUND(SUM(if(flag='flag_6_12',plaque_cnt,0))/SUM(if(flag='flag_6_12',user_cnt,0)),2) plaque_ipu_4,
        ROUND((SUM(if(flag='flag_6_12',plaque_revenue,0))/100/SUM(if(flag='flag_6_12',plaque_cnt,0)))*1000,2) plaque_ecpm_4,
        ROUND(SUM(if(flag='flag_6_12',video_cnt,0))/SUM(if(flag='flag_6_12',user_cnt,0)),2) video_ipu_4,
        ROUND((SUM(if(flag='flag_6_12',video_revenue,0))/100/SUM(if(flag='flag_6_12',video_cnt,0)))*1000,2) video_ecpm_4,

        IFNULL(SUM(if(flag='flag_12_36',user_cnt,0)),0) user_cnt_5,
        ROUND(SUM(if(flag='flag_12_36',iaa_revenue,0))/100/SUM(if(flag='flag_12_36',user_cnt,0)),2) ad_arpu_5,
        ROUND(SUM(if(flag='flag_12_36',plaque_cnt,0))/SUM(if(flag='flag_12_36',user_cnt,0)),2) plaque_ipu_5,
        ROUND((SUM(if(flag='flag_12_36',plaque_revenue,0))/100/SUM(if(flag='flag_12_36',plaque_cnt,0)))*1000,2) plaque_ecpm_5,
        ROUND(SUM(if(flag='flag_12_36',video_cnt,0))/SUM(if(flag='flag_12_36',user_cnt,0)),2) video_ipu_5,
        ROUND((SUM(if(flag='flag_12_36',video_revenue,0))/100/SUM(if(flag='flag_12_36',video_cnt,0)))*1000,2) video_ecpm_5,

        IFNULL(SUM(if(flag='flag_36_68',user_cnt,0)),0) user_cnt_6,
        ROUND(SUM(if(flag='flag_36_68',iaa_revenue,0))/100/SUM(if(flag='flag_36_68',user_cnt,0)),2) ad_arpu_6,
        ROUND(SUM(if(flag='flag_36_68',plaque_cnt,0))/SUM(if(flag='flag_36_68',user_cnt,0)),2) plaque_ipu_6,
        ROUND((SUM(if(flag='flag_36_68',plaque_revenue,0))/100/SUM(if(flag='flag_36_68',plaque_cnt,0)))*1000,2) plaque_ecpm_6,
        ROUND(SUM(if(flag='flag_36_68',video_cnt,0))/SUM(if(flag='flag_36_68',user_cnt,0)),2) video_ipu_6,
        ROUND((SUM(if(flag='flag_36_68',video_revenue,0))/100/SUM(if(flag='flag_36_68',video_cnt,0)))*1000,2) video_ecpm_6,

        IFNULL(SUM(if(flag='flag_68_128',user_cnt,0)),0) user_cnt_7,
        ROUND(SUM(if(flag='flag_68_128',iaa_revenue,0))/100/SUM(if(flag='flag_68_128',user_cnt,0)),2) ad_arpu_7,
        ROUND(SUM(if(flag='flag_68_128',plaque_cnt,0))/SUM(if(flag='flag_68_128',user_cnt,0)),2) plaque_ipu_7,
        ROUND((SUM(if(flag='flag_68_128',plaque_revenue,0))/100/SUM(if(flag='flag_68_128',plaque_cnt,0)))*1000,2) plaque_ecpm_7,
        ROUND(SUM(if(flag='flag_68_128',video_cnt,0))/SUM(if(flag='flag_68_128',user_cnt,0)),2) video_ipu_7,
        ROUND((SUM(if(flag='flag_68_128',video_revenue,0))/100/SUM(if(flag='flag_68_128',video_cnt,0)))*1000,2) video_ecpm_7,

        IFNULL(SUM(if(flag='flag_128_198',user_cnt,0)),0) user_cnt_8,
        ROUND(SUM(if(flag='flag_128_198',iaa_revenue,0))/100/SUM(if(flag='flag_128_198',user_cnt,0)),2) ad_arpu_8,
        ROUND(SUM(if(flag='flag_128_198',plaque_cnt,0))/SUM(if(flag='flag_128_198',user_cnt,0)),2) plaque_ipu_8,
        ROUND((SUM(if(flag='flag_128_198',plaque_revenue,0))/100/SUM(if(flag='flag_128_198',plaque_cnt,0)))*1000,2) plaque_ecpm_8,
        ROUND(SUM(if(flag='flag_128_198',video_cnt,0))/SUM(if(flag='flag_128_198',user_cnt,0)),2) video_ipu_8,
        ROUND((SUM(if(flag='flag_128_198',video_revenue,0))/100/SUM(if(flag='flag_128_198',video_cnt,0)))*1000,2) video_ecpm_8,

        IFNULL(SUM(if(flag='flag_198_328',user_cnt,0)),0) user_cnt_9,
        ROUND(SUM(if(flag='flag_198_328',iaa_revenue,0))/100/SUM(if(flag='flag_198_328',user_cnt,0)),2) ad_arpu_9,
        ROUND(SUM(if(flag='flag_198_328',plaque_cnt,0))/SUM(if(flag='flag_198_328',user_cnt,0)),2) plaque_ipu_9,
        ROUND((SUM(if(flag='flag_198_328',plaque_revenue,0))/100/SUM(if(flag='flag_198_328',plaque_cnt,0)))*1000,2) plaque_ecpm_9,
        ROUND(SUM(if(flag='flag_198_328',video_cnt,0))/SUM(if(flag='flag_198_328',user_cnt,0)),2) video_ipu_9,
        ROUND((SUM(if(flag='flag_198_328',video_revenue,0))/100/SUM(if(flag='flag_198_328',video_cnt,0)))*1000,2) video_ecpm_9,

        IFNULL(SUM(if(flag='flag_328_648',user_cnt,0)),0) user_cnt_10,
        ROUND(SUM(if(flag='flag_328_648',iaa_revenue,0))/100/SUM(if(flag='flag_328_648',user_cnt,0)),2) ad_arpu_10,
        ROUND(SUM(if(flag='flag_328_648',plaque_cnt,0))/SUM(if(flag='flag_328_648',user_cnt,0)),2) plaque_ipu_10,
        ROUND((SUM(if(flag='flag_328_648',plaque_revenue,0))/100/SUM(if(flag='flag_328_648',plaque_cnt,0)))*1000,2) plaque_ecpm_10,
        ROUND(SUM(if(flag='flag_328_648',video_cnt,0))/SUM(if(flag='flag_328_648',user_cnt,0)),2) video_ipu_10,
        ROUND((SUM(if(flag='flag_328_648',video_revenue,0))/100/SUM(if(flag='flag_328_648',video_cnt,0)))*1000,2) video_ecpm_10,

        IFNULL(SUM(if(flag='flag_648',user_cnt,0)),0) user_cnt_11,
        ROUND(SUM(if(flag='flag_648',iaa_revenue,0))/100/SUM(if(flag='flag_648',user_cnt,0)),2) ad_arpu_11,
        ROUND(SUM(if(flag='flag_648',plaque_cnt,0))/SUM(if(flag='flag_648',user_cnt,0)),2) plaque_ipu_11,
        ROUND((SUM(if(flag='flag_648',plaque_revenue,0))/100/SUM(if(flag='flag_648',plaque_cnt,0)))*1000,2) plaque_ecpm_11,
        ROUND(SUM(if(flag='flag_648',video_cnt,0))/SUM(if(flag='flag_648',user_cnt,0)),2) video_ipu_11,
        ROUND((SUM(if(flag='flag_648',video_revenue,0))/100/SUM(if(flag='flag_648',video_cnt,0)))*1000,2) video_ecpm_11,


        IFNULL(SUM(if(flag='flag_648_3000',user_cnt,0)),0) user_cnt_12,
        ROUND(SUM(if(flag='flag_648_3000',iaa_revenue,0))/100/SUM(if(flag='flag_648_3000',user_cnt,0)),2) ad_arpu_12,
        ROUND(SUM(if(flag='flag_648_3000',plaque_cnt,0))/SUM(if(flag='flag_648_3000',user_cnt,0)),2) plaque_ipu_12,
        ROUND((SUM(if(flag='flag_648_3000',plaque_revenue,0))/100/SUM(if(flag='flag_648_3000',plaque_cnt,0)))*1000,2) plaque_ecpm_12,
        ROUND(SUM(if(flag='flag_648_3000',video_cnt,0))/SUM(if(flag='flag_648_3000',user_cnt,0)),2) video_ipu_12,
        ROUND((SUM(if(flag='flag_648_3000',video_revenue,0))/100/SUM(if(flag='flag_648_3000',video_cnt,0)))*1000,2) video_ecpm_12,


        IFNULL(SUM(if(flag='flag_3000_5000',user_cnt,0)),0) user_cnt_13,
        ROUND(SUM(if(flag='flag_3000_5000',iaa_revenue,0))/100/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) ad_arpu_13,
        ROUND(SUM(if(flag='flag_3000_5000',plaque_cnt,0))/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) plaque_ipu_13,
        ROUND((SUM(if(flag='flag_3000_5000',plaque_revenue,0))/100/SUM(if(flag='flag_3000_5000',plaque_cnt,0)))*1000,2) plaque_ecpm_13,
        ROUND(SUM(if(flag='flag_3000_5000',video_cnt,0))/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) video_ipu_13,
        ROUND((SUM(if(flag='flag_3000_5000',video_revenue,0))/100/SUM(if(flag='flag_3000_5000',video_cnt,0)))*1000,2) video_ecpm_13,


        IFNULL(SUM(if(flag='flag_5000_10000',user_cnt,0)),0) user_cnt_14,
        ROUND(SUM(if(flag='flag_5000_10000',iaa_revenue,0))/100/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) ad_arpu_14,
        ROUND(SUM(if(flag='flag_5000_10000',plaque_cnt,0))/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) plaque_ipu_14,
        ROUND((SUM(if(flag='flag_5000_10000',plaque_revenue,0))/100/SUM(if(flag='flag_5000_10000',plaque_cnt,0)))*1000,2) plaque_ecpm_14,
        ROUND(SUM(if(flag='flag_5000_10000',video_cnt,0))/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) video_ipu_14,
        ROUND((SUM(if(flag='flag_5000_10000',video_revenue,0))/100/SUM(if(flag='flag_5000_10000',video_cnt,0)))*1000,2) video_ecpm_14,


        IFNULL(SUM(if(flag='flag_10000_30000',user_cnt,0)),0) user_cnt_15,
        ROUND(SUM(if(flag='flag_10000_30000',iaa_revenue,0))/100/SUM(if(flag='flag_10000_30000',user_cnt,0)),2) ad_arpu_15,
        ROUND(SUM(if(flag='flag_10000_30000',plaque_cnt,0))/SUM(if(flag='flag_10000_30000',user_cnt,0)),2) plaque_ipu_15,
        ROUND((SUM(if(flag='flag_10000_30000',plaque_revenue,0))/100/SUM(if(flag='flag_10000_30000',plaque_cnt,0)))*1000,2) plaque_ecpm_15,
        ROUND(SUM(if(flag='flag_10000_30000',video_cnt,0))/SUM(if(flag='flag_10000_30000',user_cnt,0)),2) video_ipu_15,
        ROUND((SUM(if(flag='flag_10000_30000',video_revenue,0))/100/SUM(if(flag='flag_10000_30000',video_cnt,0)))*1000,2) video_ecpm_15,


        IFNULL(SUM(if(flag='flag_30000_50000',user_cnt,0)),0) user_cnt_16,
        ROUND(SUM(if(flag='flag_30000_50000',iaa_revenue,0))/100/SUM(if(flag='flag_30000_50000',user_cnt,0)),2) ad_arpu_16,
        ROUND(SUM(if(flag='flag_30000_50000',plaque_cnt,0))/SUM(if(flag='flag_30000_50000',user_cnt,0)),2) plaque_ipu_16,
        ROUND((SUM(if(flag='flag_30000_50000',plaque_revenue,0))/100/SUM(if(flag='flag_30000_50000',plaque_cnt,0)))*1000,2) plaque_ecpm_16,
        ROUND(SUM(if(flag='flag_30000_50000',video_cnt,0))/SUM(if(flag='flag_30000_50000',user_cnt,0)),2) video_ipu_16,
        ROUND((SUM(if(flag='flag_30000_50000',video_revenue,0))/100/SUM(if(flag='flag_30000_50000',video_cnt,0)))*1000,2) video_ecpm_16,


        IFNULL(SUM(if(flag='flag_50000',user_cnt,0)),0) user_cnt_17,
        ROUND(SUM(if(flag='flag_50000',iaa_revenue,0))/100/SUM(if(flag='flag_50000',user_cnt,0)),2) ad_arpu_17,
        ROUND(SUM(if(flag='flag_50000',plaque_cnt,0))/SUM(if(flag='flag_50000',user_cnt,0)),2) plaque_ipu_17,
        ROUND((SUM(if(flag='flag_50000',plaque_revenue,0))/100/SUM(if(flag='flag_50000',plaque_cnt,0)))*1000,2) plaque_ecpm_17,
        ROUND(SUM(if(flag='flag_50000',video_cnt,0))/SUM(if(flag='flag_50000',user_cnt,0)),2) video_ipu_17,
        ROUND((SUM(if(flag='flag_50000',video_revenue,0))/100/SUM(if(flag='flag_50000',video_cnt,0)))*1000,2) video_ecpm_17


        from dnwx_bi.ads_user_ad_price_acc_daily where 1=1
        and DATE(t_date) <![CDATA[>=]]> #{start_date} and DATE(t_date) <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by t_date asc ,iaa_revenue desc
            </otherwise>
        </choose>
    </select>

    <select id="getPayAdValueTotalSum" resultType="com.wbgame.pojo.game.PayAdValueVo" parameterType="java.util.Map">
        select

        FORMAT((SUM(iaa_revenue)/100/SUM(user_cnt)),2) total_ad_arpu,
        FORMAT((SUM(plaque_cnt)/SUM(user_cnt)),2) total_plaque_ipu,
        FORMAT((SUM(plaque_revenue)/100/SUM(plaque_cnt))*1000,2) total_plaque_ecpm,
        FORMAT((SUM(video_cnt)/SUM(user_cnt)),2) total_video_ipu,
        FORMAT((SUM(video_revenue)/100/SUM(video_cnt))*1000,2) total_video_ecpm,

        IFNULL(SUM(if(flag='flag_0',user_cnt,0)),0) user_cnt_1,
        FORMAT(SUM(if(flag='flag_0',iaa_revenue,0))/100/SUM(if(flag='flag_0',user_cnt,0)),2) ad_arpu_1,
        FORMAT(SUM(if(flag='flag_0',plaque_cnt,0))/SUM(if(flag='flag_0',user_cnt,0)),2) plaque_ipu_1,
        FORMAT((SUM(if(flag='flag_0',plaque_revenue,0))/100/SUM(if(flag='flag_0',plaque_cnt,0)))*1000,2) plaque_ecpm_1,
        FORMAT(SUM(if(flag='flag_0',video_cnt,0))/SUM(if(flag='flag_0',user_cnt,0)),2) video_ipu_1,
        FORMAT((SUM(if(flag='flag_0',video_revenue,0))/100/SUM(if(flag='flag_0',video_cnt,0)))*1000,2) video_ecpm_1,

        IFNULL(SUM(if(flag='flag_0_1',user_cnt,0)),0) user_cnt_2,
        FORMAT(SUM(if(flag='flag_0_1',iaa_revenue,0))/100/SUM(if(flag='flag_0_1',user_cnt,0)),2) ad_arpu_2,
        FORMAT(SUM(if(flag='flag_0_1',plaque_cnt,0))/SUM(if(flag='flag_0_1',user_cnt,0)),2) plaque_ipu_2,
        FORMAT((SUM(if(flag='flag_0_1',plaque_revenue,0))/100/SUM(if(flag='flag_0_1',plaque_cnt,0)))*1000,2) plaque_ecpm_2,
        FORMAT(SUM(if(flag='flag_0_1',video_cnt,0))/SUM(if(flag='flag_0_1',user_cnt,0)),2) video_ipu_2,
        FORMAT((SUM(if(flag='flag_0_1',video_revenue,0))/100/SUM(if(flag='flag_0_1',video_cnt,0)))*1000,2) video_ecpm_2,

        IFNULL(SUM(if(flag='flag_1_6',user_cnt,0)),0) user_cnt_3,
        FORMAT(SUM(if(flag='flag_1_6',iaa_revenue,0))/100/SUM(if(flag='flag_1_6',user_cnt,0)),2) ad_arpu_3,
        FORMAT(SUM(if(flag='flag_1_6',plaque_cnt,0))/SUM(if(flag='flag_1_6',user_cnt,0)),2) plaque_ipu_3,
        FORMAT((SUM(if(flag='flag_1_6',plaque_revenue,0))/100/SUM(if(flag='flag_1_6',plaque_cnt,0)))*1000,2) plaque_ecpm_3,
        FORMAT(SUM(if(flag='flag_1_6',video_cnt,0))/SUM(if(flag='flag_1_6',user_cnt,0)),2) video_ipu_3,
        FORMAT((SUM(if(flag='flag_1_6',video_revenue,0))/100/SUM(if(flag='flag_1_6',video_cnt,0)))*1000,2) video_ecpm_3,

        IFNULL(SUM(if(flag='flag_6_12',user_cnt,0)),0) user_cnt_4,
        FORMAT(SUM(if(flag='flag_6_12',iaa_revenue,0))/100/SUM(if(flag='flag_6_12',user_cnt,0)),2) ad_arpu_4,
        FORMAT(SUM(if(flag='flag_6_12',plaque_cnt,0))/SUM(if(flag='flag_6_12',user_cnt,0)),2) plaque_ipu_4,
        FORMAT((SUM(if(flag='flag_6_12',plaque_revenue,0))/100/SUM(if(flag='flag_6_12',plaque_cnt,0)))*1000,2) plaque_ecpm_4,
        FORMAT(SUM(if(flag='flag_6_12',video_cnt,0))/SUM(if(flag='flag_6_12',user_cnt,0)),2) video_ipu_4,
        FORMAT((SUM(if(flag='flag_6_12',video_revenue,0))/100/SUM(if(flag='flag_6_12',video_cnt,0)))*1000,2) video_ecpm_4,

        IFNULL(SUM(if(flag='flag_12_36',user_cnt,0)),0) user_cnt_5,
        FORMAT(SUM(if(flag='flag_12_36',iaa_revenue,0))/100/SUM(if(flag='flag_12_36',user_cnt,0)),2) ad_arpu_5,
        FORMAT(SUM(if(flag='flag_12_36',plaque_cnt,0))/SUM(if(flag='flag_12_36',user_cnt,0)),2) plaque_ipu_5,
        FORMAT((SUM(if(flag='flag_12_36',plaque_revenue,0))/100/SUM(if(flag='flag_12_36',plaque_cnt,0)))*1000,2) plaque_ecpm_5,
        FORMAT(SUM(if(flag='flag_12_36',video_cnt,0))/SUM(if(flag='flag_12_36',user_cnt,0)),2) video_ipu_5,
        FORMAT((SUM(if(flag='flag_12_36',video_revenue,0))/100/SUM(if(flag='flag_12_36',video_cnt,0)))*1000,2) video_ecpm_5,

        IFNULL(SUM(if(flag='flag_36_68',user_cnt,0)),0) user_cnt_6,
        FORMAT(SUM(if(flag='flag_36_68',iaa_revenue,0))/100/SUM(if(flag='flag_36_68',user_cnt,0)),2) ad_arpu_6,
        FORMAT(SUM(if(flag='flag_36_68',plaque_cnt,0))/SUM(if(flag='flag_36_68',user_cnt,0)),2) plaque_ipu_6,
        FORMAT((SUM(if(flag='flag_36_68',plaque_revenue,0))/100/SUM(if(flag='flag_36_68',plaque_cnt,0)))*1000,2) plaque_ecpm_6,
        FORMAT(SUM(if(flag='flag_36_68',video_cnt,0))/SUM(if(flag='flag_36_68',user_cnt,0)),2) video_ipu_6,
        FORMAT((SUM(if(flag='flag_36_68',video_revenue,0))/100/SUM(if(flag='flag_36_68',video_cnt,0)))*1000,2) video_ecpm_6,

        IFNULL(SUM(if(flag='flag_68_128',user_cnt,0)),0) user_cnt_7,
        FORMAT(SUM(if(flag='flag_68_128',iaa_revenue,0))/100/SUM(if(flag='flag_68_128',user_cnt,0)),2) ad_arpu_7,
        FORMAT(SUM(if(flag='flag_68_128',plaque_cnt,0))/SUM(if(flag='flag_68_128',user_cnt,0)),2) plaque_ipu_7,
        FORMAT((SUM(if(flag='flag_68_128',plaque_revenue,0))/100/SUM(if(flag='flag_68_128',plaque_cnt,0)))*1000,2) plaque_ecpm_7,
        FORMAT(SUM(if(flag='flag_68_128',video_cnt,0))/SUM(if(flag='flag_68_128',user_cnt,0)),2) video_ipu_7,
        FORMAT((SUM(if(flag='flag_68_128',video_revenue,0))/100/SUM(if(flag='flag_68_128',video_cnt,0)))*1000,2) video_ecpm_7,

        IFNULL(SUM(if(flag='flag_128_198',user_cnt,0)),0) user_cnt_8,
        FORMAT(SUM(if(flag='flag_128_198',iaa_revenue,0))/100/SUM(if(flag='flag_128_198',user_cnt,0)),2) ad_arpu_8,
        FORMAT(SUM(if(flag='flag_128_198',plaque_cnt,0))/SUM(if(flag='flag_128_198',user_cnt,0)),2) plaque_ipu_8,
        FORMAT((SUM(if(flag='flag_128_198',plaque_revenue,0))/100/SUM(if(flag='flag_128_198',plaque_cnt,0)))*1000,2) plaque_ecpm_8,
        FORMAT(SUM(if(flag='flag_128_198',video_cnt,0))/SUM(if(flag='flag_128_198',user_cnt,0)),2) video_ipu_8,
        FORMAT((SUM(if(flag='flag_128_198',video_revenue,0))/100/SUM(if(flag='flag_128_198',video_cnt,0)))*1000,2) video_ecpm_8,

        IFNULL(SUM(if(flag='flag_198_328',user_cnt,0)),0) user_cnt_9,
        FORMAT(SUM(if(flag='flag_198_328',iaa_revenue,0))/100/SUM(if(flag='flag_198_328',user_cnt,0)),2) ad_arpu_9,
        FORMAT(SUM(if(flag='flag_198_328',plaque_cnt,0))/SUM(if(flag='flag_198_328',user_cnt,0)),2) plaque_ipu_9,
        FORMAT((SUM(if(flag='flag_198_328',plaque_revenue,0))/100/SUM(if(flag='flag_198_328',plaque_cnt,0)))*1000,2) plaque_ecpm_9,
        FORMAT(SUM(if(flag='flag_198_328',video_cnt,0))/SUM(if(flag='flag_198_328',user_cnt,0)),2) video_ipu_9,
        FORMAT((SUM(if(flag='flag_198_328',video_revenue,0))/100/SUM(if(flag='flag_198_328',video_cnt,0)))*1000,2) video_ecpm_9,

        IFNULL(SUM(if(flag='flag_328_648',user_cnt,0)),0) user_cnt_10,
        FORMAT(SUM(if(flag='flag_328_648',iaa_revenue,0))/100/SUM(if(flag='flag_328_648',user_cnt,0)),2) ad_arpu_10,
        FORMAT(SUM(if(flag='flag_328_648',plaque_cnt,0))/SUM(if(flag='flag_328_648',user_cnt,0)),2) plaque_ipu_10,
        FORMAT((SUM(if(flag='flag_328_648',plaque_revenue,0))/100/SUM(if(flag='flag_328_648',plaque_cnt,0)))*1000,2) plaque_ecpm_10,
        FORMAT(SUM(if(flag='flag_328_648',video_cnt,0))/SUM(if(flag='flag_328_648',user_cnt,0)),2) video_ipu_10,
        FORMAT((SUM(if(flag='flag_328_648',video_revenue,0))/100/SUM(if(flag='flag_328_648',video_cnt,0)))*1000,2) video_ecpm_10,

        IFNULL(SUM(if(flag='flag_648',user_cnt,0)),0) user_cnt_11,
        FORMAT(SUM(if(flag='flag_648',iaa_revenue,0))/100/SUM(if(flag='flag_648',user_cnt,0)),2) ad_arpu_11,
        FORMAT(SUM(if(flag='flag_648',plaque_cnt,0))/SUM(if(flag='flag_648',user_cnt,0)),2) plaque_ipu_11,
        FORMAT((SUM(if(flag='flag_648',plaque_revenue,0))/100/SUM(if(flag='flag_648',plaque_cnt,0)))*1000,2) plaque_ecpm_11,
        FORMAT(SUM(if(flag='flag_648',video_cnt,0))/SUM(if(flag='flag_648',user_cnt,0)),2) video_ipu_11,
        FORMAT((SUM(if(flag='flag_648',video_revenue,0))/100/SUM(if(flag='flag_648',video_cnt,0)))*1000,2) video_ecpm_11,

        IFNULL(SUM(if(flag='flag_648_3000',user_cnt,0)),0) user_cnt_12,
        FORMAT(SUM(if(flag='flag_648_3000',iaa_revenue,0))/100/SUM(if(flag='flag_648_3000',user_cnt,0)),2) ad_arpu_12,
        FORMAT(SUM(if(flag='flag_648_3000',plaque_cnt,0))/SUM(if(flag='flag_648_3000',user_cnt,0)),2) plaque_ipu_12,
        FORMAT((SUM(if(flag='flag_648_3000',plaque_revenue,0))/100/SUM(if(flag='flag_648_3000',plaque_cnt,0)))*1000,2) plaque_ecpm_12,
        FORMAT(SUM(if(flag='flag_648_3000',video_cnt,0))/SUM(if(flag='flag_648_3000',user_cnt,0)),2) video_ipu_12,
        FORMAT((SUM(if(flag='flag_648_3000',video_revenue,0))/100/SUM(if(flag='flag_648_3000',video_cnt,0)))*1000,2) video_ecpm_12,

        IFNULL(SUM(if(flag='flag_3000_5000',user_cnt,0)),0) user_cnt_13,
        FORMAT(SUM(if(flag='flag_3000_5000',iaa_revenue,0))/100/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) ad_arpu_13,
        FORMAT(SUM(if(flag='flag_3000_5000',plaque_cnt,0))/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) plaque_ipu_13,
        FORMAT((SUM(if(flag='flag_3000_5000',plaque_revenue,0))/100/SUM(if(flag='flag_3000_5000',plaque_cnt,0)))*1000,2) plaque_ecpm_13,
        FORMAT(SUM(if(flag='flag_3000_5000',video_cnt,0))/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) video_ipu_13,
        FORMAT((SUM(if(flag='flag_3000_5000',video_revenue,0))/100/SUM(if(flag='flag_3000_5000',video_cnt,0)))*1000,2) video_ecpm_13,

        IFNULL(SUM(if(flag='flag_5000_10000',user_cnt,0)),0) user_cnt_14,
        FORMAT(SUM(if(flag='flag_5000_10000',iaa_revenue,0))/100/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) ad_arpu_14,
        FORMAT(SUM(if(flag='flag_5000_10000',plaque_cnt,0))/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) plaque_ipu_14,
        FORMAT((SUM(if(flag='flag_5000_10000',plaque_revenue,0))/100/SUM(if(flag='flag_5000_10000',plaque_cnt,0)))*1000,2) plaque_ecpm_14,
        FORMAT(SUM(if(flag='flag_5000_10000',video_cnt,0))/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) video_ipu_14,
        FORMAT((SUM(if(flag='flag_5000_10000',video_revenue,0))/100/SUM(if(flag='flag_5000_10000',video_cnt,0)))*1000,2) video_ecpm_14,

        IFNULL(SUM(if(flag='flag_10000_30000',user_cnt,0)),0) user_cnt_15,
        FORMAT(SUM(if(flag='flag_10000_30000',iaa_revenue,0))/100/SUM(if(flag='flag_10000_30000',user_cnt,0)),2) ad_arpu_15,
        FORMAT(SUM(if(flag='flag_10000_30000',plaque_cnt,0))/SUM(if(flag='flag_10000_30000',user_cnt,0)),2) plaque_ipu_15,
        FORMAT((SUM(if(flag='flag_10000_30000',plaque_revenue,0))/100/SUM(if(flag='flag_10000_30000',plaque_cnt,0)))*1000,2) plaque_ecpm_15,
        FORMAT(SUM(if(flag='flag_10000_30000',video_cnt,0))/SUM(if(flag='flag_10000_30000',user_cnt,0)),2) video_ipu_15,
        FORMAT((SUM(if(flag='flag_10000_30000',video_revenue,0))/100/SUM(if(flag='flag_10000_30000',video_cnt,0)))*1000,2) video_ecpm_15,

        IFNULL(SUM(if(flag='flag_30000_50000',user_cnt,0)),0) user_cnt_16,
        FORMAT(SUM(if(flag='flag_30000_50000',iaa_revenue,0))/100/SUM(if(flag='flag_30000_50000',user_cnt,0)),2) ad_arpu_16,
        FORMAT(SUM(if(flag='flag_30000_50000',plaque_cnt,0))/SUM(if(flag='flag_30000_50000',user_cnt,0)),2) plaque_ipu_16,
        FORMAT((SUM(if(flag='flag_30000_50000',plaque_revenue,0))/100/SUM(if(flag='flag_30000_50000',plaque_cnt,0)))*1000,2) plaque_ecpm_16,
        FORMAT(SUM(if(flag='flag_30000_50000',video_cnt,0))/SUM(if(flag='flag_30000_50000',user_cnt,0)),2) video_ipu_16,
        FORMAT((SUM(if(flag='flag_30000_50000',video_revenue,0))/100/SUM(if(flag='flag_30000_50000',video_cnt,0)))*1000,2) video_ecpm_16,

        IFNULL(SUM(if(flag='flag_50000',user_cnt,0)),0) user_cnt_17,
        FORMAT(SUM(if(flag='flag_50000',iaa_revenue,0))/100/SUM(if(flag='flag_50000',user_cnt,0)),2) ad_arpu_17,
        FORMAT(SUM(if(flag='flag_50000',plaque_cnt,0))/SUM(if(flag='flag_50000',user_cnt,0)),2) plaque_ipu_17,
        FORMAT((SUM(if(flag='flag_50000',plaque_revenue,0))/100/SUM(if(flag='flag_50000',plaque_cnt,0)))*1000,2) plaque_ecpm_17,
        FORMAT(SUM(if(flag='flag_50000',video_cnt,0))/SUM(if(flag='flag_50000',user_cnt,0)),2) video_ipu_17,
        FORMAT((SUM(if(flag='flag_50000',video_revenue,0))/100/SUM(if(flag='flag_50000',video_cnt,0)))*1000,2) video_ecpm_17


        from dnwx_bi.ads_user_ad_price_acc_daily where 1=1
        and DATE(t_date) <![CDATA[>=]]> #{start_date} and DATE(t_date) <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
    </select>

    <select id="getPayAdValueTotalReportList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.report.PayAdValueReportVo">
        select
        IFNULL(SUM(if(flag='flag_0',user_cnt,0)),0) user_cnt_1,
        ROUND(SUM(if(flag='flag_0',iaa_revenue,0))/100/SUM(if(flag='flag_0',user_cnt,0)),2) ad_arpu_1,
        ROUND(SUM(if(flag='flag_0',plaque_cnt,0))/SUM(if(flag='flag_0',user_cnt,0)),2) plaque_ipu_1,
        ROUND((SUM(if(flag='flag_0',plaque_revenue,0))/100/SUM(if(flag='flag_0',plaque_cnt,0)))*1000,2) plaque_ecpm_1,
        ROUND(SUM(if(flag='flag_0',video_cnt,0))/SUM(if(flag='flag_0',user_cnt,0)),2) video_ipu_1,
        ROUND((SUM(if(flag='flag_0',video_revenue,0))/100/SUM(if(flag='flag_0',video_cnt,0)))*1000,2) video_ecpm_1,

        IFNULL(SUM(if(flag='flag_0_1',user_cnt,0)),0) user_cnt_2,
        ROUND(SUM(if(flag='flag_0_1',iaa_revenue,0))/100/SUM(if(flag='flag_0_1',user_cnt,0)),2) ad_arpu_2,
        ROUND(SUM(if(flag='flag_0_1',plaque_cnt,0))/SUM(if(flag='flag_0_1',user_cnt,0)),2) plaque_ipu_2,
        ROUND((SUM(if(flag='flag_0_1',plaque_revenue,0))/100/SUM(if(flag='flag_0_1',plaque_cnt,0)))*1000,2) plaque_ecpm_2,
        ROUND(SUM(if(flag='flag_0_1',video_cnt,0))/SUM(if(flag='flag_0_1',user_cnt,0)),2) video_ipu_2,
        ROUND((SUM(if(flag='flag_0_1',video_revenue,0))/100/SUM(if(flag='flag_0_1',video_cnt,0)))*1000,2) video_ecpm_2,

        IFNULL(SUM(if(flag='flag_1_6',user_cnt,0)),0) user_cnt_3,
        ROUND(SUM(if(flag='flag_1_6',iaa_revenue,0))/100/SUM(if(flag='flag_1_6',user_cnt,0)),2) ad_arpu_3,
        ROUND(SUM(if(flag='flag_1_6',plaque_cnt,0))/SUM(if(flag='flag_1_6',user_cnt,0)),2) plaque_ipu_3,
        ROUND((SUM(if(flag='flag_1_6',plaque_revenue,0))/100/SUM(if(flag='flag_1_6',plaque_cnt,0)))*1000,2) plaque_ecpm_3,
        ROUND(SUM(if(flag='flag_1_6',video_cnt,0))/SUM(if(flag='flag_1_6',user_cnt,0)),2) video_ipu_3,
        ROUND((SUM(if(flag='flag_1_6',video_revenue,0))/100/SUM(if(flag='flag_1_6',video_cnt,0)))*1000,2) video_ecpm_3,

        IFNULL(SUM(if(flag='flag_6_12',user_cnt,0)),0) user_cnt_4,
        ROUND(SUM(if(flag='flag_6_12',iaa_revenue,0))/100/SUM(if(flag='flag_6_12',user_cnt,0)),2) ad_arpu_4,
        ROUND(SUM(if(flag='flag_6_12',plaque_cnt,0))/SUM(if(flag='flag_6_12',user_cnt,0)),2) plaque_ipu_4,
        ROUND((SUM(if(flag='flag_6_12',plaque_revenue,0))/100/SUM(if(flag='flag_6_12',plaque_cnt,0)))*1000,2) plaque_ecpm_4,
        ROUND(SUM(if(flag='flag_6_12',video_cnt,0))/SUM(if(flag='flag_6_12',user_cnt,0)),2) video_ipu_4,
        ROUND((SUM(if(flag='flag_6_12',video_revenue,0))/100/SUM(if(flag='flag_6_12',video_cnt,0)))*1000,2) video_ecpm_4,

        IFNULL(SUM(if(flag='flag_12_36',user_cnt,0)),0) user_cnt_5,
        ROUND(SUM(if(flag='flag_12_36',iaa_revenue,0))/100/SUM(if(flag='flag_12_36',user_cnt,0)),2) ad_arpu_5,
        ROUND(SUM(if(flag='flag_12_36',plaque_cnt,0))/SUM(if(flag='flag_12_36',user_cnt,0)),2) plaque_ipu_5,
        ROUND((SUM(if(flag='flag_12_36',plaque_revenue,0))/100/SUM(if(flag='flag_12_36',plaque_cnt,0)))*1000,2) plaque_ecpm_5,
        ROUND(SUM(if(flag='flag_12_36',video_cnt,0))/SUM(if(flag='flag_12_36',user_cnt,0)),2) video_ipu_5,
        ROUND((SUM(if(flag='flag_12_36',video_revenue,0))/100/SUM(if(flag='flag_12_36',video_cnt,0)))*1000,2) video_ecpm_5,

        IFNULL(SUM(if(flag='flag_36_68',user_cnt,0)),0) user_cnt_6,
        ROUND(SUM(if(flag='flag_36_68',iaa_revenue,0))/100/SUM(if(flag='flag_36_68',user_cnt,0)),2) ad_arpu_6,
        ROUND(SUM(if(flag='flag_36_68',plaque_cnt,0))/SUM(if(flag='flag_36_68',user_cnt,0)),2) plaque_ipu_6,
        ROUND((SUM(if(flag='flag_36_68',plaque_revenue,0))/100/SUM(if(flag='flag_36_68',plaque_cnt,0)))*1000,2) plaque_ecpm_6,
        ROUND(SUM(if(flag='flag_36_68',video_cnt,0))/SUM(if(flag='flag_36_68',user_cnt,0)),2) video_ipu_6,
        ROUND((SUM(if(flag='flag_36_68',video_revenue,0))/100/SUM(if(flag='flag_36_68',video_cnt,0)))*1000,2) video_ecpm_6,

        IFNULL(SUM(if(flag='flag_68_128',user_cnt,0)),0) user_cnt_7,
        ROUND(SUM(if(flag='flag_68_128',iaa_revenue,0))/100/SUM(if(flag='flag_68_128',user_cnt,0)),2) ad_arpu_7,
        ROUND(SUM(if(flag='flag_68_128',plaque_cnt,0))/SUM(if(flag='flag_68_128',user_cnt,0)),2) plaque_ipu_7,
        ROUND((SUM(if(flag='flag_68_128',plaque_revenue,0))/100/SUM(if(flag='flag_68_128',plaque_cnt,0)))*1000,2) plaque_ecpm_7,
        ROUND(SUM(if(flag='flag_68_128',video_cnt,0))/SUM(if(flag='flag_68_128',user_cnt,0)),2) video_ipu_7,
        ROUND((SUM(if(flag='flag_68_128',video_revenue,0))/100/SUM(if(flag='flag_68_128',video_cnt,0)))*1000,2) video_ecpm_7,

        IFNULL(SUM(if(flag='flag_128_198',user_cnt,0)),0) user_cnt_8,
        ROUND(SUM(if(flag='flag_128_198',iaa_revenue,0))/100/SUM(if(flag='flag_128_198',user_cnt,0)),2) ad_arpu_8,
        ROUND(SUM(if(flag='flag_128_198',plaque_cnt,0))/SUM(if(flag='flag_128_198',user_cnt,0)),2) plaque_ipu_8,
        ROUND((SUM(if(flag='flag_128_198',plaque_revenue,0))/100/SUM(if(flag='flag_128_198',plaque_cnt,0)))*1000,2) plaque_ecpm_8,
        ROUND(SUM(if(flag='flag_128_198',video_cnt,0))/SUM(if(flag='flag_128_198',user_cnt,0)),2) video_ipu_8,
        ROUND((SUM(if(flag='flag_128_198',video_revenue,0))/100/SUM(if(flag='flag_128_198',video_cnt,0)))*1000,2) video_ecpm_8,

        IFNULL(SUM(if(flag='flag_198_328',user_cnt,0)),0) user_cnt_9,
        ROUND(SUM(if(flag='flag_198_328',iaa_revenue,0))/100/SUM(if(flag='flag_198_328',user_cnt,0)),2) ad_arpu_9,
        ROUND(SUM(if(flag='flag_198_328',plaque_cnt,0))/SUM(if(flag='flag_198_328',user_cnt,0)),2) plaque_ipu_9,
        ROUND((SUM(if(flag='flag_198_328',plaque_revenue,0))/100/SUM(if(flag='flag_198_328',plaque_cnt,0)))*1000,2) plaque_ecpm_9,
        ROUND(SUM(if(flag='flag_198_328',video_cnt,0))/SUM(if(flag='flag_198_328',user_cnt,0)),2) video_ipu_9,
        ROUND((SUM(if(flag='flag_198_328',video_revenue,0))/100/SUM(if(flag='flag_198_328',video_cnt,0)))*1000,2) video_ecpm_9,

        IFNULL(SUM(if(flag='flag_328_648',user_cnt,0)),0) user_cnt_10,
        ROUND(SUM(if(flag='flag_328_648',iaa_revenue,0))/100/SUM(if(flag='flag_328_648',user_cnt,0)),2) ad_arpu_10,
        ROUND(SUM(if(flag='flag_328_648',plaque_cnt,0))/SUM(if(flag='flag_328_648',user_cnt,0)),2) plaque_ipu_10,
        ROUND((SUM(if(flag='flag_328_648',plaque_revenue,0))/100/SUM(if(flag='flag_328_648',plaque_cnt,0)))*1000,2) plaque_ecpm_10,
        ROUND(SUM(if(flag='flag_328_648',video_cnt,0))/SUM(if(flag='flag_328_648',user_cnt,0)),2) video_ipu_10,
        ROUND((SUM(if(flag='flag_328_648',video_revenue,0))/100/SUM(if(flag='flag_328_648',video_cnt,0)))*1000,2) video_ecpm_10,

        IFNULL(SUM(if(flag='flag_648',user_cnt,0)),0) user_cnt_11,
        ROUND(SUM(if(flag='flag_648',iaa_revenue,0))/100/SUM(if(flag='flag_648',user_cnt,0)),2) ad_arpu_11,
        ROUND(SUM(if(flag='flag_648',plaque_cnt,0))/SUM(if(flag='flag_648',user_cnt,0)),2) plaque_ipu_11,
        ROUND((SUM(if(flag='flag_648',plaque_revenue,0))/100/SUM(if(flag='flag_648',plaque_cnt,0)))*1000,2) plaque_ecpm_11,
        ROUND(SUM(if(flag='flag_648',video_cnt,0))/SUM(if(flag='flag_648',user_cnt,0)),2) video_ipu_11,
        ROUND((SUM(if(flag='flag_648',video_revenue,0))/100/SUM(if(flag='flag_648',video_cnt,0)))*1000,2) video_ecpm_11,


        IFNULL(SUM(if(flag='flag_648_3000',user_cnt,0)),0) user_cnt_12,
        ROUND(SUM(if(flag='flag_648_3000',iaa_revenue,0))/100/SUM(if(flag='flag_648_3000',user_cnt,0)),2) ad_arpu_12,
        ROUND(SUM(if(flag='flag_648_3000',plaque_cnt,0))/SUM(if(flag='flag_648_3000',user_cnt,0)),2) plaque_ipu_12,
        ROUND((SUM(if(flag='flag_648_3000',plaque_revenue,0))/100/SUM(if(flag='flag_648_3000',plaque_cnt,0)))*1000,2) plaque_ecpm_12,
        ROUND(SUM(if(flag='flag_648_3000',video_cnt,0))/SUM(if(flag='flag_648_3000',user_cnt,0)),2) video_ipu_12,
        ROUND((SUM(if(flag='flag_648_3000',video_revenue,0))/100/SUM(if(flag='flag_648_3000',video_cnt,0)))*1000,2) video_ecpm_12,


        IFNULL(SUM(if(flag='flag_3000_5000',user_cnt,0)),0) user_cnt_13,
        ROUND(SUM(if(flag='flag_3000_5000',iaa_revenue,0))/100/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) ad_arpu_13,
        ROUND(SUM(if(flag='flag_3000_5000',plaque_cnt,0))/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) plaque_ipu_13,
        ROUND((SUM(if(flag='flag_3000_5000',plaque_revenue,0))/100/SUM(if(flag='flag_3000_5000',plaque_cnt,0)))*1000,2) plaque_ecpm_13,
        ROUND(SUM(if(flag='flag_3000_5000',video_cnt,0))/SUM(if(flag='flag_3000_5000',user_cnt,0)),2) video_ipu_13,
        ROUND((SUM(if(flag='flag_3000_5000',video_revenue,0))/100/SUM(if(flag='flag_3000_5000',video_cnt,0)))*1000,2) video_ecpm_13,


        IFNULL(SUM(if(flag='flag_5000_10000',user_cnt,0)),0) user_cnt_14,
        ROUND(SUM(if(flag='flag_5000_10000',iaa_revenue,0))/100/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) ad_arpu_14,
        ROUND(SUM(if(flag='flag_5000_10000',plaque_cnt,0))/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) plaque_ipu_14,
        ROUND((SUM(if(flag='flag_5000_10000',plaque_revenue,0))/100/SUM(if(flag='flag_5000_10000',plaque_cnt,0)))*1000,2) plaque_ecpm_14,
        ROUND(SUM(if(flag='flag_5000_10000',video_cnt,0))/SUM(if(flag='flag_5000_10000',user_cnt,0)),2) video_ipu_14,
        ROUND((SUM(if(flag='flag_5000_10000',video_revenue,0))/100/SUM(if(flag='flag_5000_10000',video_cnt,0)))*1000,2) video_ecpm_14,


        IFNULL(SUM(if(flag='flag_10000_30000',user_cnt,0)),0) user_cnt_15,
        ROUND(SUM(if(flag='flag_10000_30000',iaa_revenue,0))/100/SUM(if(flag='flag_10000_30000',user_cnt,0)),2) ad_arpu_15,
        ROUND(SUM(if(flag='flag_10000_30000',plaque_cnt,0))/SUM(if(flag='flag_10000_30000',user_cnt,0)),2) plaque_ipu_15,
        ROUND((SUM(if(flag='flag_10000_30000',plaque_revenue,0))/100/SUM(if(flag='flag_10000_30000',plaque_cnt,0)))*1000,2) plaque_ecpm_15,
        ROUND(SUM(if(flag='flag_10000_30000',video_cnt,0))/SUM(if(flag='flag_10000_30000',user_cnt,0)),2) video_ipu_15,
        ROUND((SUM(if(flag='flag_10000_30000',video_revenue,0))/100/SUM(if(flag='flag_10000_30000',video_cnt,0)))*1000,2) video_ecpm_15,


        IFNULL(SUM(if(flag='flag_30000_50000',user_cnt,0)),0) user_cnt_16,
        ROUND(SUM(if(flag='flag_30000_50000',iaa_revenue,0))/100/SUM(if(flag='flag_30000_50000',user_cnt,0)),2) ad_arpu_16,
        ROUND(SUM(if(flag='flag_30000_50000',plaque_cnt,0))/SUM(if(flag='flag_30000_50000',user_cnt,0)),2) plaque_ipu_16,
        ROUND((SUM(if(flag='flag_30000_50000',plaque_revenue,0))/100/SUM(if(flag='flag_30000_50000',plaque_cnt,0)))*1000,2) plaque_ecpm_16,
        ROUND(SUM(if(flag='flag_30000_50000',video_cnt,0))/SUM(if(flag='flag_30000_50000',user_cnt,0)),2) video_ipu_16,
        ROUND((SUM(if(flag='flag_30000_50000',video_revenue,0))/100/SUM(if(flag='flag_30000_50000',video_cnt,0)))*1000,2) video_ecpm_16,


        IFNULL(SUM(if(flag='flag_50000',user_cnt,0)),0) user_cnt_17,
        ROUND(SUM(if(flag='flag_50000',iaa_revenue,0))/100/SUM(if(flag='flag_50000',user_cnt,0)),2) ad_arpu_17,
        ROUND(SUM(if(flag='flag_50000',plaque_cnt,0))/SUM(if(flag='flag_50000',user_cnt,0)),2) plaque_ipu_17,
        ROUND((SUM(if(flag='flag_50000',plaque_revenue,0))/100/SUM(if(flag='flag_50000',plaque_cnt,0)))*1000,2) plaque_ecpm_17,
        ROUND(SUM(if(flag='flag_50000',video_cnt,0))/SUM(if(flag='flag_50000',user_cnt,0)),2) video_ipu_17,
        ROUND((SUM(if(flag='flag_50000',video_revenue,0))/100/SUM(if(flag='flag_50000',video_cnt,0)))*1000,2) video_ecpm_17
        from dnwx_bi.ads_user_ad_price_acc_daily
        where 1=1 and DATE(t_date) <![CDATA[>=]]> #{start_date} and DATE(t_date) <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
    </select>


    <select id="getPayActivityList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.pay.PayActivityVo">
        select tdate,appid,pid,download_channel,
        SUM(rmb_gift_reg_user_cnt + rmb_gift_active_user_cnt) rmb_gift_cnt,

        SUM(rmb_gifg_reg_user_amount + rmb_gifg_active_user_amount)/100 rmb_gift_amount,

        SUM(activity_reg_user_amount + activity_active_user_amount)/100 activity_amount,

        (SUM(rmb_gifg_reg_user_amount + rmb_gifg_active_user_amount)
        - SUM(activity_reg_user_amount + activity_active_user_amount))/100 no_activity_gift_amount,

        SUM(only_activity_reg_user_cnt + only_activity_active_user_cnt) only_activity_cnt,

        SUM(only_activity_reg_user_amount + only_activity_active_user_amount)/100 only_activity_amount,

        SUM(only_activity_reg_user_amount + only_activity_active_user_amount)/100/
        SUM(only_activity_reg_user_cnt + only_activity_active_user_cnt) only_activity_avg_amount,

        SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt) no_activity_cnt,

        SUM(no_activity_reg_user_amount + no_activity_active_user_amount)/100 no_activity_amount,

        SUM(no_activity_reg_user_amount + no_activity_active_user_amount)/100/
        SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt) no_activity_avg_amount,

        SUM(rmb_gift_reg_user_cnt + rmb_gift_active_user_cnt)
        -SUM(only_activity_reg_user_cnt + only_activity_active_user_cnt)
        -SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt) activity_and_no_activity_cnt,

        (SUM(activity_reg_user_amount + activity_active_user_amount)
        -SUM(only_activity_reg_user_amount + only_activity_active_user_amount))/100 activity_and_no_activity_amount,

        ((SUM(rmb_gifg_reg_user_amount + rmb_gifg_active_user_amount)
        - SUM(activity_reg_user_amount + activity_active_user_amount))
        -SUM(no_activity_reg_user_amount + no_activity_active_user_amount))/100 activity_and_no_activity_no_activity_amount,

        (SUM(activity_reg_user_amount + activity_active_user_amount)
        -SUM(only_activity_reg_user_amount + only_activity_active_user_amount))/100/
        (SUM(rmb_gift_reg_user_cnt + rmb_gift_active_user_cnt)
        -SUM(only_activity_reg_user_cnt + only_activity_active_user_cnt)
        -SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt)) activity_and_no_activity_activity_avg_amount,

        ((SUM(rmb_gifg_reg_user_amount + rmb_gifg_active_user_amount)
        - SUM(activity_reg_user_amount + activity_active_user_amount))
        -SUM(no_activity_reg_user_amount + no_activity_active_user_amount))/100/
        (SUM(rmb_gift_reg_user_cnt + rmb_gift_active_user_cnt)
        -SUM(only_activity_reg_user_cnt + only_activity_active_user_cnt)
        -SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt)) activity_and_no_activity_no_activity_avg_amount,


        ((SUM(activity_reg_user_amount + activity_active_user_amount)
        -SUM(only_activity_reg_user_amount + only_activity_active_user_amount))+
        ((SUM(rmb_gifg_reg_user_amount + rmb_gifg_active_user_amount)
        - SUM(activity_reg_user_amount + activity_active_user_amount))
        -SUM(no_activity_reg_user_amount + no_activity_active_user_amount)))/100/
        (SUM(rmb_gift_reg_user_cnt + rmb_gift_active_user_cnt)
        -SUM(only_activity_reg_user_cnt + only_activity_active_user_cnt)
        -SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt)) activity_and_no_activity_avg_amount,

        SUM(rmb_gifg_reg_user_amount + rmb_gifg_active_user_amount)/100/SUM(rmb_gift_reg_user_cnt + rmb_gift_active_user_cnt) total_avg_amount,

        (((SUM(rmb_gifg_reg_user_amount + rmb_gifg_active_user_amount)
        - SUM(activity_reg_user_amount + activity_active_user_amount))
        -SUM(no_activity_reg_user_amount + no_activity_active_user_amount))+SUM(no_activity_reg_user_amount + no_activity_active_user_amount))/100/
        (SUM(rmb_gift_reg_user_cnt + rmb_gift_active_user_cnt)
        -SUM(only_activity_reg_user_cnt + only_activity_active_user_cnt)
        -SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt)+SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt)) total_no_activity_avg_amount


        from dnwx_bi.ads_activity_pay_daily where 1=1
        and DATE(tdate) <![CDATA[>=]]> #{start_date} and DATE(tdate) <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc ,rmb_gift_reg_user_cnt desc
            </otherwise>
        </choose>
    </select>

    <select id="getPayActivitySum" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.pay.PayActivityVo">
        select tdate,appid,pid,download_channel,
        SUM(rmb_gift_reg_user_cnt + rmb_gift_active_user_cnt) rmb_gift_cnt,

        SUM(rmb_gifg_reg_user_amount + rmb_gifg_active_user_amount)/100 rmb_gift_amount,

        SUM(activity_reg_user_amount + activity_active_user_amount)/100 activity_amount,

        (SUM(rmb_gifg_reg_user_amount + rmb_gifg_active_user_amount)
        - SUM(activity_reg_user_amount + activity_active_user_amount))/100 no_activity_gift_amount,

        SUM(only_activity_reg_user_cnt + only_activity_active_user_cnt) only_activity_cnt,

        SUM(only_activity_reg_user_amount + only_activity_active_user_amount)/100 only_activity_amount,

        SUM(only_activity_reg_user_amount + only_activity_active_user_amount)/100/
        SUM(only_activity_reg_user_cnt + only_activity_active_user_cnt) only_activity_avg_amount,

        SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt) no_activity_cnt,

        SUM(no_activity_reg_user_amount + no_activity_active_user_amount)/100 no_activity_amount,

        SUM(no_activity_reg_user_amount + no_activity_active_user_amount)/100/
        SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt) no_activity_avg_amount,

        SUM(rmb_gift_reg_user_cnt + rmb_gift_active_user_cnt)
        -SUM(only_activity_reg_user_cnt + only_activity_active_user_cnt)
        -SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt) activity_and_no_activity_cnt,

        (SUM(activity_reg_user_amount + activity_active_user_amount)
        -SUM(only_activity_reg_user_amount + only_activity_active_user_amount))/100 activity_and_no_activity_amount,

        ((SUM(rmb_gifg_reg_user_amount + rmb_gifg_active_user_amount)
        - SUM(activity_reg_user_amount + activity_active_user_amount))
        -SUM(no_activity_reg_user_amount + no_activity_active_user_amount))/100 activity_and_no_activity_no_activity_amount,

        (SUM(activity_reg_user_amount + activity_active_user_amount)
        -SUM(only_activity_reg_user_amount + only_activity_active_user_amount))/100/
        (SUM(rmb_gift_reg_user_cnt + rmb_gift_active_user_cnt)
        -SUM(only_activity_reg_user_cnt + only_activity_active_user_cnt)
        -SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt)) activity_and_no_activity_activity_avg_amount,

        ((SUM(rmb_gifg_reg_user_amount + rmb_gifg_active_user_amount)
        - SUM(activity_reg_user_amount + activity_active_user_amount))
        -SUM(no_activity_reg_user_amount + no_activity_active_user_amount))/100/
        (SUM(rmb_gift_reg_user_cnt + rmb_gift_active_user_cnt)
        -SUM(only_activity_reg_user_cnt + only_activity_active_user_cnt)
        -SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt)) activity_and_no_activity_no_activity_avg_amount,


        ((SUM(activity_reg_user_amount + activity_active_user_amount)
        -SUM(only_activity_reg_user_amount + only_activity_active_user_amount))+
        ((SUM(rmb_gifg_reg_user_amount + rmb_gifg_active_user_amount)
        - SUM(activity_reg_user_amount + activity_active_user_amount))
        -SUM(no_activity_reg_user_amount + no_activity_active_user_amount)))/100/
        (SUM(rmb_gift_reg_user_cnt + rmb_gift_active_user_cnt)
        -SUM(only_activity_reg_user_cnt + only_activity_active_user_cnt)
        -SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt)) activity_and_no_activity_avg_amount,

        SUM(rmb_gifg_reg_user_amount + rmb_gifg_active_user_amount)/100/SUM(rmb_gift_reg_user_cnt + rmb_gift_active_user_cnt) total_avg_amount,

        (((SUM(rmb_gifg_reg_user_amount + rmb_gifg_active_user_amount)
        - SUM(activity_reg_user_amount + activity_active_user_amount))
        -SUM(no_activity_reg_user_amount + no_activity_active_user_amount))+SUM(no_activity_reg_user_amount + no_activity_active_user_amount))/100/
        (SUM(rmb_gift_reg_user_cnt + rmb_gift_active_user_cnt)
        -SUM(only_activity_reg_user_cnt + only_activity_active_user_cnt)
        -SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt)+SUM(no_activity_reg_user_cnt + no_activity_active_user_cnt)) total_no_activity_avg_amount


        from dnwx_bi.ads_activity_pay_daily where 1=1
        and DATE(tdate) <![CDATA[>=]]> #{start_date} and DATE(tdate) <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
    </select>

    <select id="getAppPayDauDataList" resultType="com.wbgame.pojo.game.pay.PayTotalVo">
        select tdate,appid,pid,act_users as act_num
        from dnwx_bi.ads_dim_users_info_4d_hourly
        where tdate  <![CDATA[>=]]>  #{start_date} and tdate <![CDATA[<=]]> #{end_date}
    </select>

    <select id="getMicGameDauDataList" resultType="com.wbgame.pojo.game.pay.PayTotalVo">
        select tdate,appid,appid as pid,sum(active_user_cnt) as act_num
        from dnwx_bi.ads_wechat_user_cnt_daily
        where tdate  <![CDATA[>=]]>  #{start_date} and tdate <![CDATA[<=]]> #{end_date}
        group by tdate,appid
    </select>


    <insert id="insertLeyingOrderToAdbWbPayInfo">
        insert ignore into adb_wb_pay_info
        (
            orderid,uid,money,paytype,imei,pid,appid,is_new,
            orderstatus,payname,paynote,createtime,param1,param2,param3,chaid,androidid,
            payid,userid,buy_id,pn,model,idfa
        )
        value
        (
            #{orderid},#{uid},#{money},#{paytype},#{imei},#{pid},#{appid},#{is_new},
            #{orderstatus},#{payname},#{paynote},#{createtime},#{param1},#{param2},#{param3},#{chaid},#{androidid},
            #{payid},#{userid},#{buy_id},#{pn},#{model},#{idfa}
        )

    </insert>

    <insert id="insertOrderUpdateMarkLog">
        insert into dnwx_bi.order_update_mark_log(owner_name,owner_time,orderid,mark,owner_date) values
        (#{owner_name},now(),#{orderid},#{mark},now())
    </insert>

    <update id="updateADBOrderMark">
        update dnwx_bi.adb_wb_pay_info set param3 = #{mark} where orderid = #{orderid}
    </update>

</mapper>