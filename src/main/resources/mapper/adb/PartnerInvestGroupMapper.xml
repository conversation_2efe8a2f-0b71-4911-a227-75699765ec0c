<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.PartnerInvestGroupMapper">

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, `day`, app_category, appname, appid, channelType, media, spend, rebateSpend, reduceAmount, operSpend, create_time, update_time
    </sql>
    
    <!-- 合作方投放明细聚合查询 -->
    <select id="selectPartnerInvestGroup" parameterType="java.util.Map" resultType="java.util.Map">
        <include refid="partner_invest_group_sql"/>
    </select>
    
    <!-- 合作方投放明细聚合汇总查询 -->
    <select id="selectPartnerInvestGroupSum" parameterType="java.util.Map" resultType="java.util.Map">
        select
            IFNULL(truncate(sum(xx.spend),2), 0) spend,
            IFNULL(truncate(sum(xx.rebateSpend),2), 0) rebateSpend,
            IFNULL(truncate(sum(xx.reduceAmount),2), 0) reduceAmount,
            IFNULL(truncate(sum(xx.operSpend),2), 0) operSpend
        from (<include refid="partner_invest_group_sql"/>) xx
    </select>
    
    <!-- 合作方投放明细聚合查询SQL -->
    <sql id="partner_invest_group_sql">
        SELECT
            app_category,appid,channelType,media,
            <choose>
                <when test="custom_date != null and custom_date.size() > 0">
                    concat(#{sdate},'至',#{edate}) as day
                </when>
                <when test="group != null and group.size() > 0 and group.contains('week')">
                    DATE_FORMAT(day, '%x-%v') as day,DATE_FORMAT(day, '%x-%v') as week
                </when>
                <when test="group != null and group.size() > 0 and group.contains('month')">
                    DATE_FORMAT(day,'%Y-%m') as day,
                    DATE_FORMAT(day,'%Y-%m') as month
                </when>
                <when test="group != null and group.size() > 0 and group.contains('beek')">
                    CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  as day
                    ,CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0")) as beek
                </when>
                <otherwise>
                    concat(day,'') day
                </otherwise>
            </choose>
            ,
            IFNULL(truncate(sum(spend),2), 0) spend,
            IFNULL(truncate(sum(rebateSpend)-IFNULL(sum(reduceAmount),0),2), 0) rebateSpend,
            IFNULL(truncate(sum(reduceAmount),2), 0) reduceAmount,
            IFNULL(truncate(sum(spend)-IFNULL(sum(reduceAmount),0),2), 0) operSpend
        FROM
            dnwx_adt.partner_invest_group
        WHERE 
            `day` BETWEEN #{sdate} AND #{edate}
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="app_category != null and app_category != ''">
                and app_category in (${app_category})
            </if>
            <if test="channelType != null and channelType != ''">
                and channelType in (${channelType})
            </if>
            <if test="media != null and media != ''">
                and media in (${media})
            </if>
            <if test="apps != null and apps != ''">
                and appid in (${apps})
            </if>

        <if test="group != null and group.size > 0">
        GROUP BY
            <foreach collection="group" index="index" item="item" separator=",">
                <choose>
                    <when test="item == 'week'">
                        DATE_FORMAT(day, '%x-%v')
                    </when>
                    <when test="item == 'month'">
                        DATE_FORMAT(day,'%Y-%m')
                    </when>
                    <when test="item == 'beek'">
                        CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))
                    </when>
                    <when test="item == 'appCategory' or item == 'app_category'">
                        app_category
                    </when>
                    <otherwise>
                        `${item}`
                    </otherwise>
                </choose>
            </foreach>
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                ORDER BY ${order_str}
            </when>
            <otherwise>
                ORDER BY `day` ASC, spend DESC
            </otherwise>
        </choose>
    </sql>
    
    <!-- 同步数据 -->
    <insert id="syncDataFromDnReport" parameterType="com.wbgame.pojo.advert.partnerNew.PartnerInvestGroupDTO">
        REPLACE INTO dnwx_adt.partner_invest_group (`day`, app_category, appname, appid, channelType, media, spend, rebateSpend, reduceAmount)
        SELECT 
            d.day,
            a.app_category AS app_category,
            a.app_name AS appname,
            d.app AS appid,
            IFNULL(d.channelType,'') AS channelType,
            d.media AS media,
            TRUNCATE(SUM(IFNULL(CONCAT(d.spend,''), 0)),2) AS spend,
            TRUNCATE(SUM(IFNULL(CONCAT(d.rebateSpend,''), 0)),2) AS rebateSpend,
            0 AS reduceAmount
        FROM 
            dnwx_adt.dn_report_spend_china d
        LEFT JOIN 
            app_info a ON d.app = a.id
        WHERE 
            d.day BETWEEN #{sdate} AND #{edate}
        GROUP BY 
            d.day, d.app, d.channelType, d.media
    </insert>
    
    <!-- 更新核减金额 -->
    <update id="updateReduceAmount" parameterType="com.wbgame.pojo.advert.partnerNew.PartnerInvestGroupDTO">
        UPDATE dnwx_adt.partner_invest_group
        SET reduceAmount = #{reduceAmount},
            update_time = NOW()
        WHERE day = #{day} and appid = #{appid} and channelType = #{channelType} and media = #{media}
    </update>
    
    <!-- 审核数据 -->
    <update id="checkData" parameterType="java.lang.String">
        UPDATE dnwx_adt.partner_invest_group
        SET update_user = #{cuser},
            update_time = NOW()
        WHERE `day` = #{tdate} AND ischeck != '1'
    </update>
</mapper>