<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsAddUserRoiDailyMapper">


    <select id="selectAdsAddUserRoiDaily" resultType="com.wbgame.pojo.bigdata.AdsAddUserRoiDaily"
            parameterType="com.wbgame.pojo.bigdata.AdsAddUserRoiDaily">

        <include refid="collect"/>

    </select>

    <select id="getAdsAddUserRoiDailyMaxData" resultType="com.wbgame.pojo.bigdata.AdsAddUserRoiDaily"
            parameterType="com.wbgame.pojo.bigdata.AdsAddUserRoiDaily">
        select
        max(add_user) add_user,
        cast(max(originalLtv1) as decimal(18, 2)) originalLtv1,
        round(max(ltv1), 2) ltv1,
        round(max(ltv2), 2) ltv2,
        round(max(ltv3), 2) ltv3,
        round(max(ltv4), 2) ltv4,
        round(max(ltv5), 2) ltv5,
        round(max(ltv6), 2) ltv6,
        round(max(ltv7), 2) ltv7,
        round(max(ltv8), 2) ltv8,
        round(max(ltv9), 2) ltv9,
        round(max(ltv10), 2) ltv10,
        round(max(ltv11), 2) ltv11,
        round(max(ltv12), 2) ltv12,
        round(max(ltv13), 2) ltv13,
        round(max(ltv14), 2) ltv14,
        round(max(ltv21), 2) ltv21,
        round(max(ltv28), 2) ltv28,
        round(max(ltv30), 2) ltv30,
        round(max(ltv36), 2) ltv36,
        round(max(ltv48), 2) ltv48,
        round(max(ltv60), 2) ltv60,

        round(max(ltv75), 2) ltv75,
        round(max(ltv90), 2) ltv90,
        round(max(ltv105), 2) ltv105,
        round(max(ltv120), 2) ltv120,
        round(max(ltv135), 2) ltv135,
        round(max(ltv150), 2) ltv150,
        round(max(ltv165), 2) ltv165,
        round(max(ltv180), 2) ltv180,
        round(max(ltv195), 2) ltv195,
        round(max(ltv210), 2) ltv210,
        round(max(ltv225), 2) ltv225,
        round(max(ltv240), 2) ltv240,
        round(max(ltv255), 2) ltv255,
        round(max(ltv270), 2) ltv270,
        round(max(ltv285), 2) ltv285,
        round(max(ltv300), 2) ltv300,
        round(max(ltv315), 2) ltv315,
        round(max(ltv330), 2) ltv330,
        round(max(ltv345), 2) ltv345,
        round(max(ltv360), 2) ltv360,

        round(max(ltv14Scale7), 2) ltv14Scale7,
        round(max(ltv21Scale7), 2) ltv21Scale7,
        round(max(ltv28Scale7), 2) ltv28Scale7,
        round(max(ltv30Scale7), 2) ltv30Scale7,
        round(max(ltv36Scale7), 2) ltv36Scale7,
        round(max(ltv48Scale7), 2) ltv48Scale7,
        round(max(ltv60Scale7), 2) ltv60Scale7,

        round(max(ltv75Scale7), 2) ltv75Scale7,
        round(max(ltv90Scale7), 2) ltv90Scale7,
        round(max(ltv105Scale7), 2) ltv105Scale7,
        round(max(ltv120Scale7), 2) ltv120Scale7,
        round(max(ltv135Scale7), 2) ltv135Scale7,
        round(max(ltv150Scale7), 2) ltv150Scale7,
        round(max(ltv165Scale7), 2) ltv165Scale7,
        round(max(ltv180Scale7), 2) ltv180Scale7,
        round(max(ltv195Scale7), 2) ltv195Scale7,
        round(max(ltv210Scale7), 2) ltv210Scale7,
        round(max(ltv225Scale7), 2) ltv225Scale7,
        round(max(ltv240Scale7), 2) ltv240Scale7,
        round(max(ltv255Scale7), 2) ltv255Scale7,
        round(max(ltv270Scale7), 2) ltv270Scale7,
        round(max(ltv285Scale7), 2) ltv285Scale7,
        round(max(ltv300Scale7), 2) ltv300Scale7,
        round(max(ltv315Scale7), 2) ltv315Scale7,
        round(max(ltv330Scale7), 2) ltv330Scale7,
        round(max(ltv345Scale7), 2) ltv345Scale7,
        round(max(ltv360Scale7), 2) ltv360Scale7

        from (
        <include refid="collect"/>
        ) a
    </select>

    <select id="selectAdsAddUserRoiDailyCollect" resultType="com.wbgame.pojo.bigdata.AdsAddUserRoiDaily"
            parameterType="com.wbgame.pojo.bigdata.AdsAddUserRoiDaily">

        select
        SUM(add_user) add_user,
        cast(avg(originalLtv1) as decimal(18, 2)) originalLtv1,
        round(avg(ltv1), 2) ltv1,
        round(avg(ltv2), 2) ltv2,
        round(avg(ltv3), 2) ltv3,
        round(avg(ltv4), 2) ltv4,
        round(avg(ltv5), 2) ltv5,
        round(avg(ltv6), 2) ltv6,
        round(avg(ltv7), 2) ltv7,
        round(avg(ltv8), 2) ltv8,
        round(avg(ltv9), 2) ltv9,
        round(avg(ltv10), 2) ltv10,
        round(avg(ltv11), 2) ltv11,
        round(avg(ltv12), 2) ltv12,
        round(avg(ltv13), 2) ltv13,
        round(avg(ltv14), 2) ltv14,
        round(avg(ltv21), 2) ltv21,
        round(avg(ltv28), 2) ltv28,
        round(avg(ltv30), 2) ltv30,
        round(avg(ltv36), 2) ltv36,
        round(avg(ltv48), 2) ltv48,
        round(avg(ltv60), 2) ltv60,

        round(avg(ltv75), 2) ltv75,
        round(avg(ltv90), 2) ltv90,
        round(avg(ltv105), 2) ltv105,
        round(avg(ltv120), 2) ltv120,
        round(avg(ltv135), 2) ltv135,
        round(avg(ltv150), 2) ltv150,
        round(avg(ltv165), 2) ltv165,
        round(avg(ltv180), 2) ltv180,
        round(avg(ltv195), 2) ltv195,
        round(avg(ltv210), 2) ltv210,
        round(avg(ltv225), 2) ltv225,
        round(avg(ltv240), 2) ltv240,
        round(avg(ltv255), 2) ltv255,
        round(avg(ltv270), 2) ltv270,
        round(avg(ltv285), 2) ltv285,
        round(avg(ltv300), 2) ltv300,
        round(avg(ltv315), 2) ltv315,
        round(avg(ltv330), 2) ltv330,
        round(avg(ltv345), 2) ltv345,
        round(avg(ltv360), 2) ltv360,

        round(avg(ltv14Scale7), 2) ltv14Scale7,
        round(avg(ltv21Scale7), 2) ltv21Scale7,
        round(avg(ltv28Scale7), 2) ltv28Scale7,
        round(avg(ltv30Scale7), 2) ltv30Scale7,
        round(avg(ltv36Scale7), 2) ltv36Scale7,
        round(avg(ltv48Scale7), 2) ltv48Scale7,
        round(avg(ltv60Scale7), 2) ltv60Scale7,

        round(avg(ltv75Scale7), 2) ltv75Scale7,
        round(avg(ltv90Scale7), 2) ltv90Scale7,
        round(avg(ltv105Scale7), 2) ltv105Scale7,
        round(avg(ltv120Scale7), 2) ltv120Scale7,
        round(avg(ltv135Scale7), 2) ltv135Scale7,
        round(avg(ltv150Scale7), 2) ltv150Scale7,
        round(avg(ltv165Scale7), 2) ltv165Scale7,
        round(avg(ltv180Scale7), 2) ltv180Scale7,
        round(avg(ltv195Scale7), 2) ltv195Scale7,
        round(avg(ltv210Scale7), 2) ltv210Scale7,
        round(avg(ltv225Scale7), 2) ltv225Scale7,
        round(avg(ltv240Scale7), 2) ltv240Scale7,
        round(avg(ltv255Scale7), 2) ltv255Scale7,
        round(avg(ltv270Scale7), 2) ltv270Scale7,
        round(avg(ltv285Scale7), 2) ltv285Scale7,
        round(avg(ltv300Scale7), 2) ltv300Scale7,
        round(avg(ltv315Scale7), 2) ltv315Scale7,
        round(avg(ltv330Scale7), 2) ltv330Scale7,
        round(avg(ltv345Scale7), 2) ltv345Scale7,
        round(avg(ltv360Scale7), 2) ltv360Scale7

        from (


            <include refid="collect"/>
                          ) a

    </select>

    <select id="selectAdsAddUserRoiDailyScheme" resultType="com.wbgame.pojo.bigdata.AdsAddUserRoiDaily"
            parameterType="com.wbgame.pojo.bigdata.AdsAddUserRoiDaily">

        <include refid="collectScheme"/>

    </select>

    <!--    图：汇总-->
    <sql id="collectScheme">

        select

        <if test="group != null and group != ''">
            ${group},
        </if>
        sum(add_user)                                                      add_user,
        sum(originalLtv1) originalLtv1,
        cast(sum(if(ltv1 > 0, ltv1, 0)) / count(if(ltv1 > 0, 1, null)) as decimal(18, 2))   ltv1,
        cast(sum(if(ltv2 > 0, ltv2, 0)) / count(if(ltv2 > 0, 1, null)) as decimal(18, 2))   ltv2,
        cast(sum(if(ltv3 > 0, ltv3, 0)) / count(if(ltv3 > 0, 1, null)) as decimal(18, 2))   ltv3,
        cast(sum(if(ltv4 > 0, ltv4, 0)) / count(if(ltv4 > 0, 1, null)) as decimal(18, 2))   ltv4,
        cast(sum(if(ltv5 > 0, ltv5, 0)) / count(if(ltv5 > 0, 1, null)) as decimal(18, 2))   ltv5,
        cast(sum(if(ltv6 > 0, ltv6, 0)) / count(if(ltv6 > 0, 1, null)) as decimal(18, 2))   ltv6,
        cast(sum(if(ltv7 > 0, ltv7, 0)) / count(if(ltv7 > 0, 1, null)) as decimal(18, 2))   ltv7,
        cast(sum(if(ltv8 > 0, ltv8, 0)) / count(if(ltv8 > 0, 1, null)) as decimal(18, 2))   ltv8,
        cast(sum(if(ltv9 > 0, ltv9, 0)) / count(if(ltv9 > 0, 1, null)) as decimal(18, 2))   ltv9,
        cast(sum(if(ltv10 > 0, ltv10, 0)) / count(if(ltv10 > 0, 1, null)) as decimal(18, 2)) ltv10,
        cast(sum(if(ltv11 > 0, ltv11, 0)) / count(if(ltv11 > 0, 1, null)) as decimal(18, 2)) ltv11,
        cast(sum(if(ltv12 > 0, ltv12, 0)) / count(if(ltv12 > 0, 1, null)) as decimal(18, 2)) ltv12,
        cast(sum(if(ltv13 > 0, ltv13, 0)) / count(if(ltv13 > 0, 1, null)) as decimal(18, 2)) ltv13,
        cast(sum(if(ltv14 > 0, ltv14, 0)) / count(if(ltv14 > 0, 1, null)) as decimal(18, 2)) ltv14,
        cast(sum(if(ltv21 > 0, ltv21, 0)) / count(if(ltv21 > 0, 1, null)) as decimal(18, 2)) ltv21,
        cast(sum(if(ltv28 > 0, ltv28, 0)) / count(if(ltv28 > 0, 1, null)) as decimal(18, 2)) ltv28,
        cast(sum(if(ltv30 > 0, ltv30, 0)) / count(if(ltv30 > 0, 1, null)) as decimal(18, 2)) ltv30,
        cast(sum(if(ltv36 > 0, ltv36, 0)) / count(if(ltv36 > 0, 1, null)) as decimal(18, 2)) ltv36,
        cast(sum(if(ltv48 > 0, ltv48, 0)) / count(if(ltv48 > 0, 1, null)) as decimal(18, 2)) ltv48,
        cast(sum(if(ltv60 > 0, ltv60, 0)) / count(if(ltv60 > 0, 1, null)) as decimal(18, 2)) ltv60,


        cast(sum(if(ltv75 > 0, ltv75, 0)) / count(if(ltv75 > 0, 1, null)) as decimal(18, 2))   ltv75,
        cast(sum(if(ltv90 > 0, ltv90, 0)) / count(if(ltv90 > 0, 1, null)) as decimal(18, 2))   ltv90,
        cast(sum(if(ltv105 > 0, ltv105, 0)) / count(if(ltv105 > 0, 1, null)) as decimal(18, 2))   ltv105,
        cast(sum(if(ltv120 > 0, ltv120, 0)) / count(if(ltv120 > 0, 1, null)) as decimal(18, 2))   ltv120,
        cast(sum(if(ltv135 > 0, ltv135, 0)) / count(if(ltv135 > 0, 1, null)) as decimal(18, 2))   ltv135,
        cast(sum(if(ltv150 > 0, ltv150, 0)) / count(if(ltv150 > 0, 1, null)) as decimal(18, 2))   ltv150,
        cast(sum(if(ltv165 > 0, ltv165, 0)) / count(if(ltv165 > 0, 1, null)) as decimal(18, 2))   ltv165,
        cast(sum(if(ltv180 > 0, ltv180, 0)) / count(if(ltv180 > 0, 1, null)) as decimal(18, 2))   ltv180,
        cast(sum(if(ltv195 > 0, ltv195, 0)) / count(if(ltv195 > 0, 1, null)) as decimal(18, 2))   ltv195,
        cast(sum(if(ltv210 > 0, ltv210, 0)) / count(if(ltv210 > 0, 1, null)) as decimal(18, 2)) ltv210,
        cast(sum(if(ltv225 > 0, ltv225, 0)) / count(if(ltv225 > 0, 1, null)) as decimal(18, 2)) ltv225,
        cast(sum(if(ltv240 > 0, ltv240, 0)) / count(if(ltv240 > 0, 1, null)) as decimal(18, 2)) ltv240,
        cast(sum(if(ltv255 > 0, ltv255, 0)) / count(if(ltv255 > 0, 1, null)) as decimal(18, 2)) ltv255,
        cast(sum(if(ltv270 > 0, ltv270, 0)) / count(if(ltv270 > 0, 1, null)) as decimal(18, 2)) ltv270,
        cast(sum(if(ltv285 > 0, ltv285, 0)) / count(if(ltv285 > 0, 1, null)) as decimal(18, 2)) ltv285,
        cast(sum(if(ltv300 > 0, ltv300, 0)) / count(if(ltv300 > 0, 1, null)) as decimal(18, 2)) ltv300,
        cast(sum(if(ltv315 > 0, ltv315, 0)) / count(if(ltv315 > 0, 1, null)) as decimal(18, 2)) ltv315,
        cast(sum(if(ltv330 > 0, ltv330, 0)) / count(if(ltv330 > 0, 1, null)) as decimal(18, 2)) ltv330,
        cast(sum(if(ltv345 > 0, ltv345, 0)) / count(if(ltv345 > 0, 1, null)) as decimal(18, 2)) ltv345,
        cast(sum(if(ltv360 > 0, ltv360, 0)) / count(if(ltv360 > 0, 1, null)) as decimal(18, 2)) ltv360,


        1 ltv7Scale7,

        cast(cast(sum(if(ltv14 > 0, ltv14, 0)) / count(if(ltv14 > 0, 1, null)) as decimal(18, 2))
            / cast(sum(if(ltv14 > 0, ltv7, 0)) / count(if(ltv14 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv14Scale7,

        cast(cast(sum(if(ltv21 > 0, ltv21, 0)) / count(if(ltv21 > 0, 1, null)) as decimal(18, 2))
            / cast(sum(if(ltv21 > 0, ltv14, 0)) / count(if(ltv21 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv21Scale7,

        cast(cast(sum(if(ltv28 > 0, ltv28, 0)) / count(if(ltv28 > 0, 1, null)) as decimal(18, 2))
            / cast(sum(if(ltv28 > 0, ltv21, 0)) / count(if(ltv28 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv28Scale7,

        cast(cast(sum(if(ltv30 > 0, ltv30, 0)) / count(if(ltv30 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv30 > 0, ltv28, 0)) / count(if(ltv30 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv30Scale7,


        cast(cast(sum(if(ltv36 > 0, ltv36, 0)) / count(if(ltv36 > 0, 1, null)) as decimal(18, 2))
            / cast(sum(if(ltv36 > 0, ltv30, 0)) / count(if(ltv36 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv36Scale7,

        cast(cast(sum(if(ltv48 > 0, ltv48, 0)) / count(if(ltv48 > 0, 1, null)) as decimal(18, 2))
            / cast(sum(if(ltv48 > 0, ltv36, 0)) / count(if(ltv48 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv48Scale7,

        cast(cast(sum(if(ltv60 > 0, ltv60, 0)) / count(if(ltv60 > 0, 1, null)) as decimal(18, 2))
            / cast(sum(if(ltv60 > 0, ltv48, 0)) / count(if(ltv60 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv60Scale7,

        cast(cast(sum(if(ltv75 > 0, ltv75, 0)) / count(if(ltv75 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv75 > 0, ltv60, 0)) / count(if(ltv75 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv75Scale7,

        cast(cast(sum(if(ltv90 > 0, ltv90, 0)) / count(if(ltv90 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv90 > 0, ltv75, 0)) / count(if(ltv90 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv90Scale7,

        cast(cast(sum(if(ltv105 > 0, ltv105, 0)) / count(if(ltv105 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv105 > 0, ltv90, 0)) / count(if(ltv105 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv105Scale7,

        cast(cast(sum(if(ltv120 > 0, ltv120, 0)) / count(if(ltv120 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv120 > 0, ltv105, 0)) / count(if(ltv120 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv120Scale7,

        cast(cast(sum(if(ltv135 > 0, ltv135, 0)) / count(if(ltv135 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv135 > 0, ltv120, 0)) / count(if(ltv135 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv135Scale7,

        cast(cast(sum(if(ltv150 > 0, ltv150, 0)) / count(if(ltv150 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv150 > 0, ltv135, 0)) / count(if(ltv150 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv150Scale7,

        cast(cast(sum(if(ltv165 > 0, ltv165, 0)) / count(if(ltv165 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv165 > 0, ltv150, 0)) / count(if(ltv165 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv165Scale7,

        cast(cast(sum(if(ltv180 > 0, ltv180, 0)) / count(if(ltv180 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv180 > 0, ltv165, 0)) / count(if(ltv180 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv180Scale7,

        cast(cast(sum(if(ltv195 > 0, ltv195, 0)) / count(if(ltv195 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv195 > 0, ltv180, 0)) / count(if(ltv195 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv195Scale7,

        cast(cast(sum(if(ltv210 > 0, ltv210, 0)) / count(if(ltv210 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv210 > 0, ltv195, 0)) / count(if(ltv210 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv210Scale7,

        cast(cast(sum(if(ltv225 > 0, ltv225, 0)) / count(if(ltv225 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv225 > 0, ltv210, 0)) / count(if(ltv225 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv225Scale7,

        cast(cast(sum(if(ltv240 > 0, ltv240, 0)) / count(if(ltv240 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv240 > 0, ltv225, 0)) / count(if(ltv240 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv240Scale7,

        cast(cast(sum(if(ltv255 > 0, ltv255, 0)) / count(if(ltv255 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv255 > 0, ltv240, 0)) / count(if(ltv255 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv255Scale7,

        cast(cast(sum(if(ltv270 > 0, ltv270, 0)) / count(if(ltv270 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv270 > 0, ltv255, 0)) / count(if(ltv270 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv270Scale7,

        cast(cast(sum(if(ltv285 > 0, ltv285, 0)) / count(if(ltv285 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv285 > 0, ltv270, 0)) / count(if(ltv285 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv285Scale7,

        cast(cast(sum(if(ltv300 > 0, ltv300, 0)) / count(if(ltv300 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv300 > 0, ltv285, 0)) / count(if(ltv300 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv300Scale7,

        cast(cast(sum(if(ltv315 > 0, ltv315, 0)) / count(if(ltv315 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv315 > 0, ltv300, 0)) / count(if(ltv315 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv315Scale7,

        cast(cast(sum(if(ltv330 > 0, ltv330, 0)) / count(if(ltv330 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv330 > 0, ltv315, 0)) / count(if(ltv330 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv330Scale7,

        cast(cast(sum(if(ltv345 > 0, ltv345, 0)) / count(if(ltv345 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv345 > 0, ltv330, 0)) / count(if(ltv345 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv345Scale7,

        cast(cast(sum(if(ltv360 > 0, ltv360, 0)) / count(if(ltv360 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv360 > 0, ltv345, 0)) / count(if(ltv360 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv360Scale7

        from (

        <include refid="handleDatesAccordingToYearWeekAndMonth"/>
        ) c

        <if test="group != null and group != ''">
            group by ${group}
        </if>


        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>

                order by tdate
            </otherwise>
        </choose>
    </sql>

<!--    汇总-->
    <sql id="collect">


        select

                <if test="group != null and group != ''">
                    ${group},
                </if>
                sum(add_user)                                                      add_user,
                cast(avg(originalLtv1) as decimal(18, 2)) originalLtv1,
                cast(sum(if(ltv1 > 0, ltv1, 0)) / count(if(ltv1 > 0, 1, null)) as decimal(18, 2))   ltv1,
                cast(sum(if(ltv2 > 0, ltv2, 0)) / count(if(ltv2 > 0, 1, null)) as decimal(18, 2))   ltv2,
                cast(sum(if(ltv3 > 0, ltv3, 0)) / count(if(ltv3 > 0, 1, null)) as decimal(18, 2))   ltv3,
                cast(sum(if(ltv4 > 0, ltv4, 0)) / count(if(ltv4 > 0, 1, null)) as decimal(18, 2))   ltv4,
                cast(sum(if(ltv5 > 0, ltv5, 0)) / count(if(ltv5 > 0, 1, null)) as decimal(18, 2))   ltv5,
                cast(sum(if(ltv6 > 0, ltv6, 0)) / count(if(ltv6 > 0, 1, null)) as decimal(18, 2))   ltv6,
                cast(sum(if(ltv7 > 0, ltv7, 0)) / count(if(ltv7 > 0, 1, null)) as decimal(18, 2))   ltv7,
                cast(sum(if(ltv8 > 0, ltv8, 0)) / count(if(ltv8 > 0, 1, null)) as decimal(18, 2))   ltv8,
                cast(sum(if(ltv9 > 0, ltv9, 0)) / count(if(ltv9 > 0, 1, null)) as decimal(18, 2))   ltv9,
                cast(sum(if(ltv10 > 0, ltv10, 0)) / count(if(ltv10 > 0, 1, null)) as decimal(18, 2)) ltv10,
                cast(sum(if(ltv11 > 0, ltv11, 0)) / count(if(ltv11 > 0, 1, null)) as decimal(18, 2)) ltv11,
                cast(sum(if(ltv12 > 0, ltv12, 0)) / count(if(ltv12 > 0, 1, null)) as decimal(18, 2)) ltv12,
                cast(sum(if(ltv13 > 0, ltv13, 0)) / count(if(ltv13 > 0, 1, null)) as decimal(18, 2)) ltv13,
                cast(sum(if(ltv14 > 0, ltv14, 0)) / count(if(ltv14 > 0, 1, null)) as decimal(18, 2)) ltv14,
                cast(sum(if(ltv21 > 0, ltv21, 0)) / count(if(ltv21 > 0, 1, null)) as decimal(18, 2)) ltv21,
                cast(sum(if(ltv28 > 0, ltv28, 0)) / count(if(ltv28 > 0, 1, null)) as decimal(18, 2)) ltv28,
                cast(sum(if(ltv30 > 0, ltv30, 0)) / count(if(ltv30 > 0, 1, null)) as decimal(18, 2)) ltv30,
                cast(sum(if(ltv36 > 0, ltv36, 0)) / count(if(ltv36 > 0, 1, null)) as decimal(18, 2)) ltv36,
                cast(sum(if(ltv48 > 0, ltv48, 0)) / count(if(ltv48 > 0, 1, null)) as decimal(18, 2)) ltv48,
                cast(sum(if(ltv60 > 0, ltv60, 0)) / count(if(ltv60 > 0, 1, null)) as decimal(18, 2)) ltv60,

        cast(sum(if(ltv75 > 0, ltv75, 0)) / count(if(ltv75 > 0, 1, null)) as decimal(18, 2))   ltv75,
        cast(sum(if(ltv90 > 0, ltv90, 0)) / count(if(ltv90 > 0, 1, null)) as decimal(18, 2))   ltv90,
        cast(sum(if(ltv105 > 0, ltv105, 0)) / count(if(ltv105 > 0, 1, null)) as decimal(18, 2))   ltv105,
        cast(sum(if(ltv120 > 0, ltv120, 0)) / count(if(ltv120 > 0, 1, null)) as decimal(18, 2))   ltv120,
        cast(sum(if(ltv135 > 0, ltv135, 0)) / count(if(ltv135 > 0, 1, null)) as decimal(18, 2))   ltv135,
        cast(sum(if(ltv150 > 0, ltv150, 0)) / count(if(ltv150 > 0, 1, null)) as decimal(18, 2))   ltv150,
        cast(sum(if(ltv165 > 0, ltv165, 0)) / count(if(ltv165 > 0, 1, null)) as decimal(18, 2))   ltv165,
        cast(sum(if(ltv180 > 0, ltv180, 0)) / count(if(ltv180 > 0, 1, null)) as decimal(18, 2))   ltv180,
        cast(sum(if(ltv195 > 0, ltv195, 0)) / count(if(ltv195 > 0, 1, null)) as decimal(18, 2))   ltv195,
        cast(sum(if(ltv210 > 0, ltv210, 0)) / count(if(ltv210 > 0, 1, null)) as decimal(18, 2)) ltv210,
        cast(sum(if(ltv225 > 0, ltv225, 0)) / count(if(ltv225 > 0, 1, null)) as decimal(18, 2)) ltv225,
        cast(sum(if(ltv240 > 0, ltv240, 0)) / count(if(ltv240 > 0, 1, null)) as decimal(18, 2)) ltv240,
        cast(sum(if(ltv255 > 0, ltv255, 0)) / count(if(ltv255 > 0, 1, null)) as decimal(18, 2)) ltv255,
        cast(sum(if(ltv270 > 0, ltv270, 0)) / count(if(ltv270 > 0, 1, null)) as decimal(18, 2)) ltv270,
        cast(sum(if(ltv285 > 0, ltv285, 0)) / count(if(ltv285 > 0, 1, null)) as decimal(18, 2)) ltv285,
        cast(sum(if(ltv300 > 0, ltv300, 0)) / count(if(ltv300 > 0, 1, null)) as decimal(18, 2)) ltv300,
        cast(sum(if(ltv315 > 0, ltv315, 0)) / count(if(ltv315 > 0, 1, null)) as decimal(18, 2)) ltv315,
        cast(sum(if(ltv330 > 0, ltv330, 0)) / count(if(ltv330 > 0, 1, null)) as decimal(18, 2)) ltv330,
        cast(sum(if(ltv345 > 0, ltv345, 0)) / count(if(ltv345 > 0, 1, null)) as decimal(18, 2)) ltv345,
        cast(sum(if(ltv360 > 0, ltv360, 0)) / count(if(ltv360 > 0, 1, null)) as decimal(18, 2)) ltv360,

                cast(cast(sum(if(ltv14 > 0, ltv14, 0)) / count(if(ltv14 > 0, 1, null)) as decimal(18, 2))
                / cast(sum(if(ltv14 > 0, ltv7, 0)) / count(if(ltv14 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv14Scale7,

                cast(cast(sum(if(ltv21 > 0, ltv21, 0)) / count(if(ltv21 > 0, 1, null)) as decimal(18, 2))
                / cast(sum(if(ltv21 > 0, ltv14, 0)) / count(if(ltv21 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv21Scale7,

                cast(cast(sum(if(ltv28 > 0, ltv28, 0)) / count(if(ltv28 > 0, 1, null)) as decimal(18, 2))
                / cast(sum(if(ltv28 > 0, ltv21, 0)) / count(if(ltv28 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv28Scale7,

                cast(cast(sum(if(ltv30 > 0, ltv30, 0)) / count(if(ltv30 > 0, 1, null)) as decimal(18, 2))
                / cast(sum(if(ltv30 > 0, ltv28, 0)) / count(if(ltv30 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv30Scale7,

                cast(cast(sum(if(ltv36 > 0, ltv36, 0)) / count(if(ltv36 > 0, 1, null)) as decimal(18, 2))
                / cast(sum(if(ltv36 > 0, ltv30, 0)) / count(if(ltv36 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv36Scale7,

                cast(cast(sum(if(ltv48 > 0, ltv48, 0)) / count(if(ltv48 > 0, 1, null)) as decimal(18, 2))
                / cast(sum(if(ltv48 > 0, ltv36, 0)) / count(if(ltv48 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv48Scale7,

                cast(cast(sum(if(ltv60 > 0, ltv60, 0)) / count(if(ltv60 > 0, 1, null)) as decimal(18, 2))
                / cast(sum(if(ltv60 > 0, ltv48, 0)) / count(if(ltv60 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv60Scale7,

        cast(cast(sum(if(ltv75 > 0, ltv75, 0)) / count(if(ltv75 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv75 > 0, ltv60, 0)) / count(if(ltv75 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv75Scale7,

        cast(cast(sum(if(ltv90 > 0, ltv90, 0)) / count(if(ltv90 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv90 > 0, ltv75, 0)) / count(if(ltv90 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv90Scale7,

        cast(cast(sum(if(ltv105 > 0, ltv105, 0)) / count(if(ltv105 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv105 > 0, ltv90, 0)) / count(if(ltv105 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv105Scale7,

        cast(cast(sum(if(ltv120 > 0, ltv120, 0)) / count(if(ltv120 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv120 > 0, ltv105, 0)) / count(if(ltv120 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv120Scale7,

        cast(cast(sum(if(ltv135 > 0, ltv135, 0)) / count(if(ltv135 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv135 > 0, ltv120, 0)) / count(if(ltv135 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv135Scale7,

        cast(cast(sum(if(ltv150 > 0, ltv150, 0)) / count(if(ltv150 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv150 > 0, ltv135, 0)) / count(if(ltv150 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv150Scale7,

        cast(cast(sum(if(ltv165 > 0, ltv165, 0)) / count(if(ltv165 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv165 > 0, ltv150, 0)) / count(if(ltv165 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv165Scale7,

        cast(cast(sum(if(ltv180 > 0, ltv180, 0)) / count(if(ltv180 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv180 > 0, ltv165, 0)) / count(if(ltv180 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv180Scale7,

        cast(cast(sum(if(ltv195 > 0, ltv195, 0)) / count(if(ltv195 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv195 > 0, ltv180, 0)) / count(if(ltv195 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv195Scale7,

        cast(cast(sum(if(ltv210 > 0, ltv210, 0)) / count(if(ltv210 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv210 > 0, ltv195, 0)) / count(if(ltv210 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv210Scale7,

        cast(cast(sum(if(ltv225 > 0, ltv225, 0)) / count(if(ltv225 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv225 > 0, ltv210, 0)) / count(if(ltv225 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv225Scale7,

        cast(cast(sum(if(ltv240 > 0, ltv240, 0)) / count(if(ltv240 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv240 > 0, ltv225, 0)) / count(if(ltv240 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv240Scale7,

        cast(cast(sum(if(ltv255 > 0, ltv255, 0)) / count(if(ltv255 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv255 > 0, ltv240, 0)) / count(if(ltv255 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv255Scale7,

        cast(cast(sum(if(ltv270 > 0, ltv270, 0)) / count(if(ltv270 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv270 > 0, ltv255, 0)) / count(if(ltv270 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv270Scale7,

        cast(cast(sum(if(ltv285 > 0, ltv285, 0)) / count(if(ltv285 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv285 > 0, ltv270, 0)) / count(if(ltv285 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv285Scale7,

        cast(cast(sum(if(ltv300 > 0, ltv300, 0)) / count(if(ltv300 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv300 > 0, ltv285, 0)) / count(if(ltv300 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv300Scale7,

        cast(cast(sum(if(ltv315 > 0, ltv315, 0)) / count(if(ltv315 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv315 > 0, ltv300, 0)) / count(if(ltv315 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv315Scale7,

        cast(cast(sum(if(ltv330 > 0, ltv330, 0)) / count(if(ltv330 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv330 > 0, ltv315, 0)) / count(if(ltv330 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv330Scale7,

        cast(cast(sum(if(ltv345 > 0, ltv345, 0)) / count(if(ltv345 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv345 > 0, ltv330, 0)) / count(if(ltv345 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv345Scale7,

        cast(cast(sum(if(ltv360 > 0, ltv360, 0)) / count(if(ltv360 > 0, 1, null)) as decimal(18, 2))
        / cast(sum(if(ltv360 > 0, ltv345, 0)) / count(if(ltv360 > 0, 1, null)) as decimal(18, 2)) as decimal(18, 2)) ltv360Scale7


        from (

                        <include refid="handleDatesAccordingToYearWeekAndMonth"/>
                          ) c



        <if test="group != null and group != ''">
            group by ${group}
        </if>


        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>

            <otherwise>

                order by tdate
            </otherwise>
        </choose>
    </sql>

<!--    根据天周月处理日期-->
    <sql id="handleDatesAccordingToYearWeekAndMonth">

        select

               <choose>
                   <when test="summary == 1">

                        tdate,
                   </when>
                   <when test="summary == 2">

                       date_format(tdate, '%Y-%u') tdate,
                   </when>
                   <when test="summary == 3">

                        date_format(tdate, '%Y-%m') tdate,
                   </when>
                    <otherwise>

                        tdate,
                    </otherwise>

               </choose>
                appid,
                add_user,
                originalLtv1,
                ltv1,ltv2,ltv3,ltv4,ltv5,ltv6,ltv7,ltv8,ltv9,ltv10,
                ltv11,ltv12,ltv13,ltv14,ltv21,ltv28,ltv30,ltv36,ltv48,ltv60,
                ltv75,ltv90,ltv105,ltv120,ltv135,ltv150,ltv165,ltv180,ltv195,
                ltv210,ltv225,ltv240,ltv255,ltv270,ltv285,ltv300,ltv315,ltv330,ltv345,ltv360
        from (

            <include refid="growthMultiple"/>
                          ) b

        <where>
            <if test="exclusionDateList != null and exclusionDateList.size > 0">

                tdate not in
                <foreach collection="exclusionDateList" item="td" open="(" separator="," close=")">
                    #{td}
                </foreach>
            </if>
        </where>
    </sql>

<!--    每天单个增涨倍数-->
    <sql id="growthMultiple">


        select
                tdate,
                appid,
                add_user,
                cast(ltv1 as decimal(18, 2))  originalLtv1,
                cast(ltv1 / ltv1 as decimal(18, 10))  ltv1,
                cast(ltv2 / ltv1 as decimal(18, 10))  ltv2,
                cast(ltv3 / ltv1 as decimal(18, 10))  ltv3,
                cast(ltv4 / ltv1 as decimal(18, 10))  ltv4,
                cast(ltv5 / ltv1 as decimal(18, 10))  ltv5,
                cast(ltv6 / ltv1 as decimal(18, 10))  ltv6,
                cast(ltv7 / ltv1 as decimal(18, 10))  ltv7,
                cast(ltv8 / ltv1 as decimal(18, 10))  ltv8,
                cast(ltv9 / ltv1 as decimal(18, 10))  ltv9,
                cast(ltv10 / ltv1 as decimal(18, 10)) ltv10,
                cast(ltv11 / ltv1 as decimal(18, 10)) ltv11,
                cast(ltv12 / ltv1 as decimal(18, 10)) ltv12,
                cast(ltv13 / ltv1 as decimal(18, 10)) ltv13,
                cast(ltv14 / ltv1 as decimal(18, 10)) ltv14,
                cast(ltv21 / ltv1 as decimal(18, 10)) ltv21,
                cast(ltv28 / ltv1 as decimal(18, 10)) ltv28,
                cast(ltv30 / ltv1 as decimal(18, 10)) ltv30,
                cast(ltv36 / ltv1 as decimal(18, 10)) ltv36,
                cast(ltv48 / ltv1 as decimal(18, 10)) ltv48,
                cast(ltv60 / ltv1 as decimal(18, 10)) ltv60,

        cast(ltv75 / ltv1 as decimal(18, 10))  ltv75,
        cast(ltv90 / ltv1 as decimal(18, 10))  ltv90,
        cast(ltv105 / ltv1 as decimal(18, 10))  ltv105,
        cast(ltv120 / ltv1 as decimal(18, 10))  ltv120,
        cast(ltv135 / ltv1 as decimal(18, 10))  ltv135,
        cast(ltv150 / ltv1 as decimal(18, 10))  ltv150,
        cast(ltv165 / ltv1 as decimal(18, 10))  ltv165,
        cast(ltv180 / ltv1 as decimal(18, 10))  ltv180,
        cast(ltv195 / ltv1 as decimal(18, 10))  ltv195,
        cast(ltv210 / ltv1 as decimal(18, 10)) ltv210,
        cast(ltv225 / ltv1 as decimal(18, 10)) ltv225,
        cast(ltv240 / ltv1 as decimal(18, 10)) ltv240,
        cast(ltv255 / ltv1 as decimal(18, 10)) ltv255,
        cast(ltv270 / ltv1 as decimal(18, 10)) ltv270,
        cast(ltv285 / ltv1 as decimal(18, 10)) ltv285,
        cast(ltv300 / ltv1 as decimal(18, 10)) ltv300,
        cast(ltv315 / ltv1 as decimal(18, 10)) ltv315,
        cast(ltv330 / ltv1 as decimal(18, 10)) ltv330,
        cast(ltv345 / ltv1 as decimal(18, 10)) ltv345,
        cast(ltv360 / ltv1 as decimal(18, 10)) ltv360
        from (

            <include refid="calculateLTVPerDay"/>
                 ) a
        where ltv1 != 0
    </sql>


<!--     每天的LTV-->
    <sql id="calculateLTVPerDay">


        select tdate,
               appid,
               add_user,
               (add_revenue_1 + add_purchase_revenue_1) / 100 / add_user   ltv1,
               (add_revenue_2 + add_purchase_revenue_2) / 100 / add_user   ltv2,
               (add_revenue_3 + add_purchase_revenue_3) / 100 / add_user   ltv3,
               (add_revenue_4 + add_purchase_revenue_4) / 100 / add_user   ltv4,
               (add_revenue_5 + add_purchase_revenue_5) / 100 / add_user   ltv5,
               (add_revenue_6 + add_purchase_revenue_6) / 100 / add_user   ltv6,
               (add_revenue_7 + add_purchase_revenue_7) / 100 / add_user   ltv7,
               (add_revenue_8 + add_purchase_revenue_8) / 100 / add_user   ltv8,
               (add_revenue_9 + add_purchase_revenue_9) / 100 / add_user   ltv9,
               (add_revenue_10 + add_purchase_revenue_10) / 100 / add_user ltv10,
               (add_revenue_11 + add_purchase_revenue_11) / 100 / add_user ltv11,
               (add_revenue_12 + add_purchase_revenue_12) / 100 / add_user ltv12,
               (add_revenue_13 + add_purchase_revenue_13) / 100 / add_user ltv13,
               (add_revenue_14 + add_purchase_revenue_14) / 100 / add_user ltv14,
               (add_revenue_21 + add_purchase_revenue_21) / 100 / add_user ltv21,
               (add_revenue_28 + add_purchase_revenue_28) / 100 / add_user ltv28,
               (add_revenue_30 + add_purchase_revenue_30) / 100 / add_user ltv30,
               (add_revenue_36 + add_purchase_revenue_36) / 100 / add_user ltv36,
               (add_revenue_48 + add_purchase_revenue_48) / 100 / add_user ltv48,
               (add_revenue_60 + add_purchase_revenue_60) / 100 / add_user ltv60,

        (add_revenue_75 + add_purchase_revenue_75) / 100 / add_user   ltv75,
        (add_revenue_90 + add_purchase_revenue_90) / 100 / add_user   ltv90,
        (add_revenue_105 + add_purchase_revenue_105) / 100 / add_user   ltv105,
        (add_revenue_120 + add_purchase_revenue_120) / 100 / add_user   ltv120,
        (add_revenue_135 + add_purchase_revenue_135) / 100 / add_user   ltv135,
        (add_revenue_150 + add_purchase_revenue_150) / 100 / add_user   ltv150,
        (add_revenue_165 + add_purchase_revenue_165) / 100 / add_user   ltv165,
        (add_revenue_180 + add_purchase_revenue_180) / 100 / add_user   ltv180,
        (add_revenue_195 + add_purchase_revenue_195) / 100 / add_user   ltv195,
        (add_revenue_210 + add_purchase_revenue_210) / 100 / add_user ltv210,
        (add_revenue_225 + add_purchase_revenue_225) / 100 / add_user ltv225,
        (add_revenue_240 + add_purchase_revenue_240) / 100 / add_user ltv240,
        (add_revenue_255 + add_purchase_revenue_255) / 100 / add_user ltv255,
        (add_revenue_270 + add_purchase_revenue_270) / 100 / add_user ltv270,
        (add_revenue_285 + add_purchase_revenue_285) / 100 / add_user ltv285,
        (add_revenue_300 + add_purchase_revenue_300) / 100 / add_user ltv300,
        (add_revenue_315 + add_purchase_revenue_315) / 100 / add_user ltv315,
        (add_revenue_330 + add_purchase_revenue_330) / 100 / add_user ltv330,
        (add_revenue_345 + add_purchase_revenue_345) / 100 / add_user ltv345,
        (add_revenue_360 + add_purchase_revenue_360) / 100 / add_user ltv360
        from ads_add_user_roi_daily_v2

        where add_user > 0

        <if test="add_user != null">
            and add_user >= #{add_user}
        </if>

        <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
            and tdate between #{start_date} and #{end_date}
        </if>


        <if test="appidList != null and appidList.size > 0">
            AND appid IN
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>

    </sql>
</mapper>