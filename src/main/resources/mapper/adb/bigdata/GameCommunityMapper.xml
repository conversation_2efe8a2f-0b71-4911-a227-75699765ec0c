<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.bigdata.GameCommunityMapper">

    <select id="selectAICustomListOrigin" parameterType="java.util.Map"
            resultType="com.wbgame.pojo.game.report.response.AICustomResponse">
        select * from dnwx_bi.ads_jiari_user_question_info_hourly
        where DATE(commit_date) between #{start_date} and #{end_date}
    </select>

</mapper>
