<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.bigdata.AdsUserRevenueOverseaMapper">

	<select id="selectAdshowActuser" resultType="java.util.Map">
		SELECT
		tdate,
		appid,
		concat(app_name,'-',appid) appname,
		download_channel channel,
		<if test="adpos_type_group != null and adpos_type_group != ''">
			ad_type adpos_type,
		</if>
		<if test="group != null and group != ''">
			${group},
		</if>
		IFNULL(sum(dau),0) actnum,
		IFNULL(sum(reg_user_cnt),0) addnum,
		IFNULL(sum(global_ad_active_user),0) global_aduser,
		IFNULL(sum(ad_active_user),0) aduser,

		IFNULL(truncate(sum(reg_user_cnt)/sum(dau)*100,2),0) add_rate,
		IFNULL(truncate(sum(ad_active_user)/sum(dau)*100,2),0) seep_rate,
		IFNULL(truncate(sum(ad_active_user)/sum(reg_user_cnt)*100,2),0) add_seep_rate,
		IFNULL(truncate(sum(global_ad_active_user)/sum(dau)*100,2),0) global_seep_rate,
		IFNULL(truncate(sum(global_ad_active_user)/sum(reg_user_cnt)*100,2),0) add_global_seep_rate,
		IFNULL(round(sum(ad_pv_active_user_pv_0)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv0,
		IFNULL(round(sum(ad_pv_active_user_pv_1)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv1,
		IFNULL(round(sum(ad_pv_active_user_pv_2)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv2,
		IFNULL(round(sum(ad_pv_active_user_pv_3)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv3,
		IFNULL(round(sum(ad_pv_active_user_pv_4)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv4,
		IFNULL(round(sum(ad_pv_active_user_pv_5)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv5,
		IFNULL(round(sum(ad_pv_active_user_pv_6)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv6,
		IFNULL(round(sum(ad_pv_active_user_pv_7)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv7,
		IFNULL(round(sum(ad_pv_active_user_pv_8)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv8,
		IFNULL(round(sum(ad_pv_active_user_pv_9)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv9,
		IFNULL(round(sum(ad_pv_active_user_pv_10)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv10,
		IFNULL(round(sum(ad_pv_active_user_pv_11_15)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv11,
		IFNULL(round(sum(ad_pv_active_user_pv_16_20)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv12,
		IFNULL(round(sum(ad_pv_active_user_pv_21_25)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv13,
		IFNULL(round(sum(ad_pv_active_user_pv_gt_25)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv14
		FROM dnwx_bi.ads_active_user_revenue_ecpm_pv_analyze_daily_oversea
		where tdate BETWEEN #{start_date} AND #{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="app_category != null and app_category != ''">
			and appid in (select id from dnwx_bi.app_info where app_category in (${app_category}))
		</if>
		<if test="channel != null and channel != ''">
			and download_channel in (${channel})
		</if>
		<if test="prjid != null and prjid != ''">
			and pid = #{prjid}
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and ad_type = #{adpos_type}
		</if>
		<if test="user_type != null and user_type != ''">
			and active_type = #{user_type}
		</if>
		<if test="country != null and country != ''">
			and country in (${country})
		</if>
		<if test="appid_tag != null and appid_tag != ''">
			<choose>
				<when test="appid_tag_rev != null and appid_tag != ''">
					AND CONCAT(ifnull(appid,''),'#',ifnull(download_channel,'')) not in (${appid_tag})
				</when>
				<otherwise>
					AND CONCAT(ifnull(appid,''),'#',ifnull(download_channel,'')) in (${appid_tag})
				</otherwise>
			</choose>
		</if>
		and download_channel is not null and country is not null and reg_user_cnt > 0
		group by tdate,appid,channel
		<!-- 增加广告位类型-->
		<if test="adpos_type_group != null and adpos_type_group != ''">
			,adpos_type
		</if>
		<if test="group != null and group != ''">
			,${group}
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY ${order_str}
			</when>
			<otherwise>
				order by tdate desc,actnum desc
			</otherwise>
		</choose>
	</select>

</mapper>