<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.bigdata.DouyinDnwxBiMapper">
    <insert id="insertBatchFeedSpend">
            insert into dn_douyin_xyx_feed_spend_data
        (tdate,appid,app_id,hostApp,activeUser,adClickPv,adConsume,adIncome,adShowPv,cashAdPlayPv,clickRate,
         costPerAction,costPerClick,ecpm,loadSuccessPv,newAdIncome,newOrderIncome,newUser,orderIncome,stayTimePerUser,totalIncome,
         activeVidoePer,totalArpu,adArpu,orderArpu,totalRoi,adRoi,orderRoi
         )
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.tdate},#{lpc.appid},#{lpc.app_id},#{lpc.hostApp},#{lpc.activeUser},#{lpc.adClickPv},#{lpc.adConsume},#{lpc.adIncome},#{lpc.adShowPv},#{lpc.cashAdPlayPv},#{lpc.clickRate},
             #{lpc.costPerAction},#{lpc.costPerClick},#{lpc.ecpm},#{lpc.loadSuccessPv},#{lpc.newAdIncome},#{lpc.newOrderIncome},#{lpc.newUser},#{lpc.orderIncome},#{lpc.stayTimePerUser},#{lpc.totalIncome},
             #{lpc.activeVidoePer},#{lpc.totalArpu},#{lpc.adArpu},#{lpc.orderArpu},#{lpc.totalRoi},#{lpc.adRoi},#{lpc.orderRoi}
            )
        </foreach>ON DUPLICATE KEY UPDATE
            activeUser = VALUES(activeUser),
            adClickPv = VALUES(adClickPv),
            adConsume = VALUES(adConsume),
            adIncome = VALUES(adIncome),
            adShowPv = VALUES(adShowPv),
            cashAdPlayPv = VALUES(cashAdPlayPv),
            clickRate = VALUES(clickRate),
            costPerAction = VALUES(costPerAction),
            costPerClick = VALUES(costPerClick),
            ecpm = VALUES(ecpm),
            loadSuccessPv = VALUES(loadSuccessPv),
            newAdIncome = VALUES(newAdIncome),
            newOrderIncome = VALUES(newOrderIncome),
            newUser = VALUES(newUser),
            orderIncome = VALUES(orderIncome),
            stayTimePerUser = VALUES(stayTimePerUser),
            totalIncome = VALUES(totalIncome),
            activeVidoePer = VALUES(activeVidoePer),
            totalArpu = VALUES(totalArpu),
            adArpu = VALUES(adArpu),
            orderArpu = VALUES(orderArpu),
            totalRoi = VALUES(totalRoi),
            adRoi = VALUES(adRoi),
            orderRoi = VALUES(orderRoi)
    </insert>
    <insert id="insertBatchLTV">
        insert into dn_douyin_xyx_ltv_data
        (tdate,appid,app_id,hostApp,adLtv1,adLtv2,adLtv3,adLtv4,adLtv5,adLtv6,adLtv7,adLtv15,adLtv30,adLtv60,adLtv150,
         ltv1,ltv2,ltv3,ltv4,ltv5,ltv6,ltv7,ltv15,ltv30,ltv60,ltv150,orderLtv1,orderLtv2,orderLtv3,orderLtv4,orderLtv5,
         orderLtv6,orderLtv7,orderLtv15,orderLtv30,orderLtv60,orderLtv150)
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.tdate},#{lpc.appid},#{lpc.app_id},#{lpc.hostApp},#{lpc.adLtv1},#{lpc.adLtv2},#{lpc.adLtv3},#{lpc.adLtv4},#{lpc.adLtv5},#{lpc.adLtv6},#{lpc.adLtv7},#{lpc.adLtv15},#{lpc.adLtv30},#{lpc.adLtv60},#{lpc.adLtv150},
             #{lpc.ltv1},#{lpc.ltv2},#{lpc.ltv3},#{lpc.ltv4},#{lpc.ltv5},#{lpc.ltv6},#{lpc.ltv7},#{lpc.ltv15},#{lpc.ltv30},#{lpc.ltv60},#{lpc.ltv150},#{lpc.orderLtv1},#{lpc.orderLtv2},#{lpc.orderLtv3},#{lpc.orderLtv4},#{lpc.orderLtv5},
             #{lpc.orderLtv6},#{lpc.orderLtv7},#{lpc.orderLtv15},#{lpc.orderLtv30},#{lpc.orderLtv60},#{lpc.orderLtv150})
        </foreach>ON DUPLICATE KEY UPDATE
            adLtv1 = VALUES(adLtv1),
            adLtv2 = VALUES(adLtv2),
            adLtv3 = VALUES(adLtv3),
            adLtv4 = VALUES(adLtv4),
            adLtv5 = VALUES(adLtv5),
            adLtv6 = VALUES(adLtv6),
            adLtv7 = VALUES(adLtv7),
            adLtv15 = VALUES(adLtv15),
            adLtv30 = VALUES(adLtv30),
            adLtv60 = VALUES(adLtv60),
            adLtv150 = VALUES(adLtv150),
            ltv1 = VALUES(ltv1),
            ltv2 = VALUES(ltv2),
            ltv3 = VALUES(ltv3),
            ltv4 = VALUES(ltv4),
            ltv5 = VALUES(ltv5),
            ltv6 = VALUES(ltv6),
            ltv7 = VALUES(ltv7),
            ltv15 = VALUES(ltv15),
            ltv30 = VALUES(ltv30),
            ltv60 = VALUES(ltv60),
            ltv150 = VALUES(ltv150),
            orderLtv1 = VALUES(orderLtv1),
            orderLtv2 = VALUES(orderLtv2),
            orderLtv3 = VALUES(orderLtv3),
            orderLtv4 = VALUES(orderLtv4),
            orderLtv5 = VALUES(orderLtv5),
            orderLtv6 = VALUES(orderLtv6),
            orderLtv7 = VALUES(orderLtv7),
            orderLtv15 = VALUES(orderLtv15),
            orderLtv30 = VALUES(orderLtv30),
            orderLtv60 = VALUES(orderLtv60),
            orderLtv150 = VALUES(orderLtv150)
    </insert>
    <insert id="insertBatchROI">
        insert into dn_douyin_xyx_roi_data
        (tdate,appid,app_id,hostApp,adRoi1,adRoi2,adRoi3,adRoi4,adRoi5,adRoi6,adRoi7,adRoi15,adRoi30,adRoi60,adRoi150,
         roi1,roi2,roi3,roi4,roi5,roi6,roi7,roi15,roi30,roi60,roi150,orderRoi1,orderRoi2,orderRoi3,orderRoi4,orderRoi5,
         orderRoi6,orderRoi7,orderRoi15,orderRoi30,orderRoi60,orderRoi150)
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.tdate},#{lpc.appid},#{lpc.app_id},#{lpc.hostApp},#{lpc.adRoi1},#{lpc.adRoi2},#{lpc.adRoi3},#{lpc.adRoi4},#{lpc.adRoi5},#{lpc.adRoi6},#{lpc.adRoi7},#{lpc.adRoi15},#{lpc.adRoi30},#{lpc.adRoi60},#{lpc.adRoi150},
             #{lpc.roi1},#{lpc.roi2},#{lpc.roi3},#{lpc.roi4},#{lpc.roi5},#{lpc.roi6},#{lpc.roi7},#{lpc.roi15},#{lpc.roi30},#{lpc.roi60},#{lpc.roi150},#{lpc.orderRoi1},#{lpc.orderRoi2},#{lpc.orderRoi3},#{lpc.orderRoi4},#{lpc.orderRoi5},
             #{lpc.orderRoi6},#{lpc.orderRoi7},#{lpc.orderRoi15},#{lpc.orderRoi30},#{lpc.orderRoi60},#{lpc.orderRoi150})
        </foreach>ON DUPLICATE KEY UPDATE
            adRoi1 = VALUES(adRoi1),
            adRoi2 = VALUES(adRoi2),
            adRoi3 = VALUES(adRoi3),
            adRoi4 = VALUES(adRoi4),
            adRoi5 = VALUES(adRoi5),
            adRoi6 = VALUES(adRoi6),
            adRoi7 = VALUES(adRoi7),
            adRoi15 = VALUES(adRoi15),
            adRoi30 = VALUES(adRoi30),
            adRoi60 = VALUES(adRoi60),
            adRoi150 = VALUES(adRoi150),
            roi1 = VALUES(roi1),
            roi2 = VALUES(roi2),
            roi3 = VALUES(roi3),
            roi4 = VALUES(roi4),
            roi5 = VALUES(roi5),
            roi6 = VALUES(roi6),
            roi7 = VALUES(roi7),
            roi15 = VALUES(roi15),
            roi30 = VALUES(roi30),
            roi60 = VALUES(roi60),
            roi150 = VALUES(roi150),
            orderRoi1 = VALUES(orderRoi1),
            orderRoi2 = VALUES(orderRoi2),
            orderRoi3 = VALUES(orderRoi3),
            orderRoi4 = VALUES(orderRoi4),
            orderRoi5 = VALUES(orderRoi5),
            orderRoi6 = VALUES(orderRoi6),
            orderRoi7 = VALUES(orderRoi7),
            orderRoi15 = VALUES(orderRoi15),
            orderRoi30 = VALUES(orderRoi30),
            orderRoi60 = VALUES(orderRoi60),
            orderRoi150 = VALUES(orderRoi150)
    </insert>

</mapper>