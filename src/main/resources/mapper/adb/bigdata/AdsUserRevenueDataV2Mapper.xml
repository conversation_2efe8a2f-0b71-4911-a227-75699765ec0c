<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.bigdata.AdsUserRevenueDataV2Mapper">

	<select id="selectAdshowActuser" resultType="com.wbgame.pojo.adv2.bigdata.AdsUserRevenueVo">
		select
		a.*,
		b.actnum,
		b.addnum,
		b.global_aduser,
		IFNULL(truncate(b.addnum/b.actnum*100,2),0) add_rate,
		IFNULL(truncate(a.aduser/b.actnum*100,2),0) seep_rate,
		IFNULL(truncate(a.aduser/b.addnum*100,2),0) add_seep_rate,
		IFNULL(truncate(b.global_aduser/b.actnum*100,2),0) global_seep_rate,
		IFNULL(truncate(b.global_aduser/b.addnum*100,2),0) add_global_seep_rate
		from (
		SELECT
			<if test="group != null and group != ''">
				${group},
				<if test="group.contains('appid')">
					app_name,
				</if>
			</if>
			<if test="group == null or group == '' or !group.contains('tdate')">
				tdate,
			</if>
			IFNULL(sum(ad_active_user),0) aduser,
			IFNULL(round(sum(ad_pv_active_user_pv_0)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv0,
			IFNULL(round(sum(ad_pv_active_user_pv_1)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv1,
			IFNULL(round(sum(ad_pv_active_user_pv_2)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv2,
			IFNULL(round(sum(ad_pv_active_user_pv_3)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv3,
			IFNULL(round(sum(ad_pv_active_user_pv_4)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv4,
			IFNULL(round(sum(ad_pv_active_user_pv_5)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv5,
			IFNULL(round(sum(ad_pv_active_user_pv_6)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv6,
			IFNULL(round(sum(ad_pv_active_user_pv_7)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv7,
			IFNULL(round(sum(ad_pv_active_user_pv_8)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv8,
			IFNULL(round(sum(ad_pv_active_user_pv_9)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv9,
			IFNULL(round(sum(ad_pv_active_user_pv_10)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv10,
			IFNULL(round(sum(ad_pv_active_user_pv_11_15)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv11,
			IFNULL(round(sum(ad_pv_active_user_pv_16_20)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv12,
			IFNULL(round(sum(ad_pv_active_user_pv_21_25)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv13,
			IFNULL(round(sum(ad_pv_active_user_pv_gt_25)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv14
		FROM
		    (select
				`appid`,`app_name`,`download_channel`,`global_ad_active_user`,`active_type`,`ad_type`,`revenue`,`dau`,`ad_cnt`,`reg_user_cnt`,`ad_active_user`,`ad_pv_active_user_pv_0`,`ad_pv_active_user_pv_1`,`ad_pv_active_user_pv_2`,`ad_pv_active_user_pv_3`,`ad_pv_active_user_pv_4`,`ad_pv_active_user_pv_5`,`ad_pv_active_user_pv_6`,`ad_pv_active_user_pv_7`,`ad_pv_active_user_pv_8`,`ad_pv_active_user_pv_9`,`ad_pv_active_user_pv_10`,`ad_pv_active_user_pv_11_15`,`ad_pv_active_user_pv_16_20`,
				`ad_pv_active_user_pv_21_25`,`ad_pv_active_user_pv_gt_25`,`ad_active_user_revenue_pv_0`,`ad_active_user_revenue_pv_1`,`ad_active_user_revenue_pv_2`,`ad_active_user_revenue_pv_3`,`ad_active_user_revenue_pv_4`,`ad_active_user_revenue_pv_5`,`ad_active_user_revenue_pv_6`,`ad_active_user_revenue_pv_7`,`ad_active_user_revenue_pv_8`,`ad_active_user_revenue_pv_9`,`ad_active_user_revenue_pv_10`,`ad_active_user_revenue_pv_11_15`,`ad_active_user_revenue_pv_16_20`,`ad_active_user_revenue_pv_21_25`,
				`ad_active_user_revenue_pv_gt_25`,`ad_active_user_pv_0`,`ad_active_user_pv_1`,`ad_active_user_pv_2`,`ad_active_user_pv_3`,`ad_active_user_pv_4`,`ad_active_user_pv_5`,`ad_active_user_pv_6`,`ad_active_user_pv_7`,`ad_active_user_pv_8`,`ad_active_user_pv_9`,`ad_active_user_pv_10`,`ad_active_user_pv_11_15`,`ad_active_user_pv_16_20`,`ad_active_user_pv_21_25`,`ad_active_user_pv_gt_25`,`pid`,`user_label`,`ad_pv_active_user_click_cnt_0`,`ad_pv_active_user_click_cnt_1`,`ad_pv_active_user_click_cnt_2`,
				`ad_pv_active_user_click_cnt_3`,`ad_pv_active_user_click_cnt_4`,`ad_pv_active_user_click_cnt_5`,`ad_pv_active_user_click_cnt_6`,`ad_pv_active_user_click_cnt_7`,`ad_pv_active_user_click_cnt_8`,`ad_pv_active_user_click_cnt_9`,`ad_pv_active_user_click_cnt_10`,`ad_pv_active_user_click_cnt_11_15`,`ad_pv_active_user_click_cnt_16_20`,`ad_pv_active_user_click_cnt_21_25`,`ad_pv_active_user_click_cnt_gt_25`,
		        <if test="full_dimension_flag != null and full_dimension_flag == true">
					`sdk_type`,`agent`,
				</if>
				<choose>
					<when test="custom_date != null and custom_date != ''">
						concat(#{start_date},'至',#{end_date}) as tdate
					</when>
					<when test="group != null and group != '' and group.contains('tdate')">
						tdate
					</when>
					<when test="group != null and group != '' and group.contains('week')">
						DATE_FORMAT(tdate, '%x-%v') as tdate,
						DATE_FORMAT(tdate, '%x-%v') as week
					</when>
					<when test="group != null and group != '' and group.contains('month')">
						DATE_FORMAT(tdate,'%Y-%m') as tdate,
						DATE_FORMAT(tdate,'%Y-%m') as `month`
					</when>
					<when test="group != null and group != '' and group.contains('beek')">
						CONCAT(DATE_FORMAT(tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS tdate,
						CONCAT(DATE_FORMAT(tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS beek
					</when>
					<otherwise>
						concat(#{start_date},'至',#{end_date}) as tdate
					</otherwise>
				</choose>
		 	from ${table}
		 	<where>
				<if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
					tdate BETWEEN #{start_date} AND #{end_date}
				</if>
				<if test="appid != null and appid != ''">
					and appid in (${appid})
				</if>
				<if test="download_channel != null and download_channel != ''">
					and download_channel in (${download_channel})
				</if>
				<if test="prjid != null and prjid != ''">
					and pid = #{prjid}
				</if>
				<if test="ad_type != null and ad_type != ''">
					and ad_type = #{ad_type}
				</if>
				<if test="sdk_type != null and sdk_type != ''">
					and sdk_type = #{sdk_type}
				</if>
				<if test="agent != null and agent != ''">
					and agent = #{agent}
				</if>
				<if test="user_type != null and user_type != ''">
					and active_type = #{user_type}
				</if>
				<if test="appid_tag != null and appid_tag != ''">
					<choose>
						<when test="appid_tag_rev != null and appid_tag_rev != ''">
							AND CONCAT(appid,'#',download_channel) not in (${appid_tag})
						</when>
						<otherwise>
							AND CONCAT(appid,'#',download_channel) in (${appid_tag})
						</otherwise>
					</choose>
				</if>
			</where>
		     ) a
		<choose>
			<when test="group != null and group != ''">
				group by ${group}
			</when>
			<otherwise>
				having sum(dau) is not null
			</otherwise>
		</choose>
		) a left join (<include refid="user_revenue_data_4d"/>) b
		on a.tdate = b.tdate2 and a.appid = b.appid2 and a.download_channel = b. download_channel2
		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY ${order_str}
			</when>
			<otherwise>
				order by tdate desc,actnum desc
			</otherwise>
		</choose>
	</select>

	<sql id="user_revenue_data_4d">
		SELECT
		appid appid2,download_channel download_channel2,
		IFNULL(sum(dau),0) actnum,
		IFNULL(sum(reg_user_cnt),0) addnum,
		IFNULL(sum(global_ad_active_user),0) global_aduser,
		<choose>
			<when test="custom_date != null and custom_date != ''">
				concat(#{start_date},'至',#{end_date}) as tdate2
			</when>
			<when test="group != null and group != '' and group.contains('tdate')">
				tdate tdate2
			</when>
			<when test="group != null and group != '' and group.contains('week')">
				DATE_FORMAT(tdate, '%x-%v') as tdate2,
				DATE_FORMAT(tdate, '%x-%v') as week
			</when>
			<when test="group != null and group != '' and group.contains('month')">
				DATE_FORMAT(tdate,'%Y-%m') as tdate2,
				DATE_FORMAT(tdate,'%Y-%m') as `month`
			</when>
			<when test="group != null and group != '' and group.contains('beek')">
				CONCAT(DATE_FORMAT(tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS tdate2,
				CONCAT(DATE_FORMAT(tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS beek
			</when>
			<otherwise>
				concat(#{start_date},'至',#{end_date}) as tdate2
			</otherwise>
		</choose>
		FROM (select tdate,appid,download_channel,ad_type,
		max(dau) dau,max(reg_user_cnt) reg_user_cnt,max(global_ad_active_user) global_ad_active_user,sum(ad_active_user) ad_active_user
		from ${table}
		<where>
			<if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
				tdate BETWEEN #{start_date} AND #{end_date}
			</if>
			<if test="appid != null and appid != ''">
				and appid in (${appid})
			</if>
			<if test="download_channel != null and download_channel != ''">
				and download_channel in (${download_channel})
			</if>
			<if test="prjid != null and prjid != ''">
				and pid = #{prjid}
			</if>
			<if test="ad_type != null and ad_type != ''">
				and ad_type = #{ad_type}
			</if>
			<if test="sdk_type != null and sdk_type != ''">
				and sdk_type = #{sdk_type}
			</if>
			<if test="agent != null and agent != ''">
				and agent = #{agent}
			</if>
			<if test="user_type != null and user_type != ''">
				and active_type = #{user_type}
			</if>
			<if test="appid_tag != null and appid_tag != ''">
				<choose>
					<when test="appid_tag_rev != null and appid_tag_rev != ''">
						AND CONCAT(appid,'#',download_channel) not in (${appid_tag})
					</when>
					<otherwise>
						AND CONCAT(appid,'#',download_channel) in (${appid_tag})
					</otherwise>
				</choose>
			</if>
		</where>
		group by tdate,appid,download_channel
		) b group by appid,download_channel
		<choose>
			<when test="group != null and group != '' and group.contains('tdate')">
				,tdate
			</when>
			<when test="group != null and group != '' and group.contains('week')">
				,DATE_FORMAT(tdate, '%x-%v')
			</when>
			<when test="group != null and group != '' and group.contains('month')">
				,DATE_FORMAT(tdate,'%Y-%m')
			</when>
			<when test="group != null and group != '' and group.contains('beek')">
				,CONCAT(DATE_FORMAT(tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))
			</when>
		</choose>
	</sql>

	<select id="selectAdshowActuserTotal" resultType="com.wbgame.pojo.adv2.bigdata.AdsUserRevenueVo">
		SELECT
			IFNULL(sum(dau),0) actnum,
			IFNULL(sum(reg_user_cnt),0) addnum,
			IFNULL(sum(global_ad_active_user),0) global_aduser,
			IFNULL(sum(ad_active_user),0) aduser,
			IFNULL(truncate(sum(reg_user_cnt)/sum(dau)*100,2),0) add_rate,
			IFNULL(truncate(sum(avg_ad_active_user)/sum(dau)*100,2),0) seep_rate,
			IFNULL(truncate(sum(avg_ad_active_user)/sum(reg_user_cnt)*100,2),0) add_seep_rate,
			IFNULL(truncate(sum(global_ad_active_user)/sum(dau)*100,2),0) global_seep_rate,
			IFNULL(truncate(sum(global_ad_active_user)/sum(reg_user_cnt)*100,2),0) add_global_seep_rate,
			IFNULL(round(sum(ad_pv_active_user_pv_0)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv0,
			IFNULL(round(sum(ad_pv_active_user_pv_1)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv1,
			IFNULL(round(sum(ad_pv_active_user_pv_2)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv2,
			IFNULL(round(sum(ad_pv_active_user_pv_3)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv3,
			IFNULL(round(sum(ad_pv_active_user_pv_4)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv4,
			IFNULL(round(sum(ad_pv_active_user_pv_5)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv5,
			IFNULL(round(sum(ad_pv_active_user_pv_6)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv6,
			IFNULL(round(sum(ad_pv_active_user_pv_7)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv7,
			IFNULL(round(sum(ad_pv_active_user_pv_8)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv8,
			IFNULL(round(sum(ad_pv_active_user_pv_9)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv9,
			IFNULL(round(sum(ad_pv_active_user_pv_10)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv10,
			IFNULL(round(sum(ad_pv_active_user_pv_11_15)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv11,
			IFNULL(round(sum(ad_pv_active_user_pv_16_20)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv12,
			IFNULL(round(sum(ad_pv_active_user_pv_21_25)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv13,
			IFNULL(round(sum(ad_pv_active_user_pv_gt_25)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv14
		FROM (select
				max(dau) dau,max(reg_user_cnt) reg_user_cnt,max(global_ad_active_user) global_ad_active_user,sum(ad_active_user) ad_active_user,avg(ad_active_user) avg_ad_active_user,sum(ad_pv_active_user_pv_0) ad_pv_active_user_pv_0,sum(ad_pv_active_user_pv_1) ad_pv_active_user_pv_1,sum(ad_pv_active_user_pv_2) ad_pv_active_user_pv_2,sum(ad_pv_active_user_pv_3) ad_pv_active_user_pv_3,sum(ad_pv_active_user_pv_4) ad_pv_active_user_pv_4,sum(ad_pv_active_user_pv_5) ad_pv_active_user_pv_5,sum(ad_pv_active_user_pv_6) ad_pv_active_user_pv_6,
				sum(ad_pv_active_user_pv_7) ad_pv_active_user_pv_7,sum(ad_pv_active_user_pv_8) ad_pv_active_user_pv_8,sum(ad_pv_active_user_pv_9) ad_pv_active_user_pv_9,sum(ad_pv_active_user_pv_10) ad_pv_active_user_pv_10,sum(ad_pv_active_user_pv_11_15) ad_pv_active_user_pv_11_15,sum(ad_pv_active_user_pv_16_20) ad_pv_active_user_pv_16_20,sum(ad_pv_active_user_pv_21_25) ad_pv_active_user_pv_21_25,sum(ad_pv_active_user_pv_gt_25) ad_pv_active_user_pv_gt_25
		      from ${table}
			<where>
				<if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
					tdate BETWEEN #{start_date} AND #{end_date}
				</if>
				<if test="appid != null and appid != ''">
					and appid in (${appid})
				</if>
				<if test="download_channel != null and download_channel != ''">
					and download_channel in (${download_channel})
				</if>
				<if test="prjid != null and prjid != ''">
					and pid = #{prjid}
				</if>
				<if test="ad_type != null and ad_type != ''">
					and ad_type = #{ad_type}
				</if>
				<if test="sdk_type != null and sdk_type != ''">
					and sdk_type = #{sdk_type}
				</if>
				<if test="agent != null and agent != ''">
					and agent = #{agent}
				</if>
				<if test="user_type != null and user_type != ''">
					and active_type = #{user_type}
				</if>
				<if test="appid_tag != null and appid_tag != ''">
					<choose>
						<when test="appid_tag_rev != null and appid_tag_rev != ''">
							AND CONCAT(appid,'#',download_channel) not in (${appid_tag})
						</when>
						<otherwise>
							AND CONCAT(appid,'#',download_channel) in (${appid_tag})
						</otherwise>
					</choose>
				</if>
			</where>
			group by tdate,appid,download_channel
	    ) a
	</select>


</mapper>