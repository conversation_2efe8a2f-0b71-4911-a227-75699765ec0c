<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.bigdata.CashAdbReportMapper">
    <select id="getReportCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM dnwx_bi.dn_cha_cash_total WHERE `date` = #{date} AND agent = #{agent} AND member_id = #{member_id}
    </select>

    <delete id="delReport">
        DELETE FROM dnwx_bi.dn_cha_cash_total WHERE `date` = #{date} AND agent = #{agent} AND member_id = #{member_id}
    </delete>

    <insert id="batchAddCashReport" parameterType="com.wbgame.pojo.adv2.reportEntity.ReportChina">
        INSERT INTO dnwx_bi.dn_cha_cash_total
        (
        dnappid, app_id, agent, placement_type, placement_id,open_type, country, `date`, pv, click, revenue, dollar_revenue, buy_revenue,
        member_id, ad_sid, cha_type_name, cha_media, cha_sub_launch, cha_id, cha_type, request_count, return_count,`source`, `out`
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.dnappid}, #{item.app_id}, #{item.agent}, #{item.placement_type}, #{item.placement_id}, #{item.open_type}, #{item.country},
            #{item.date},#{item.pv}, #{item.click}, #{item.revenue}, #{item.dollar_revenue}, #{item.buy_revenue}, #{item.member_id},
            #{item.ad_sid}, #{item.cha_type_name}, #{item.cha_media}, #{item.cha_sub_launch}, #{item.cha_id}, #{item.cha_type}, #{item.request_count},
            #{item.return_count},#{item.source},#{item.out}
            )
        </foreach>
    </insert>

    <insert id="batchInsertXyxAdvData">
        insert into dnwx_bi.dn_cha_cash_total(member_id,app_id,placement_id,`date`,return_count,pv,click,revenue,dnappid,agent,ad_sid,cha_type
        ,cha_media,cha_sub_launch,cha_id,cha_type_name,country,open_type,placement_type,`out`) values
        <foreach collection="list" item="it" separator=",">
            (#{it.sdk_appid},#{it.sdk_appid},#{it.ad_unit_id},#{it.date},#{it.req_succ_count},#{it.exposure_count},#{it.click_count},#{it.income},#{it.appid}
            ,#{it.agent},#{it.adsid},#{it.cha_type},#{it.cha_media},#{it.cha_sub_launch},#{it.cha_id},#{it.type_name},'CN',#{it.open_type},#{it.sdk_adtype},#{it.out})
        </foreach>
    </insert>

    <delete id="batchDeleteXyxAdbData">
        delete from dnwx_bi.dn_cha_cash_total
        where date BETWEEN #{startTime} AND  #{endTime} and agent = 'weixin'
        AND member_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            '${item}'
        </foreach>
    </delete>
    <delete id="deleteDouyinApps">
        DELETE FROM dnwx_bi.dn_cha_cash_total
        where date = #{date} and agent = #{platform}
        AND app_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


    <select id="selectSumRevenueByAppid" resultType="java.lang.Double">
        select ifnull(sum(Revenue),0) from dnwx_bi.dn_cha_cash_total
        where
            `date` = #{date}
        and dnappid = #{appid}
    </select>
</mapper>
