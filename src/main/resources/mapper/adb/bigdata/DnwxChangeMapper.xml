<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.bigdata.DnwxChangeMapper">

<!--    新增/活跃用户展示分布-->
    <select id="selectAdshowActuser" resultType="java.util.Map">
        SELECT
        tdate,
        appid,
        download_channel channel,
        <if test="adpos_type_group != null and adpos_type_group != ''">
				ad_type adpos_type,
		</if>
        IFNULL(dau,0) actnum,
        IFNULL(reg_user_cnt,0) addnum,
        IFNULL(global_ad_active_user,0) global_aduser,
        IFNULL(ad_active_user,0) aduser,

        IFNULL(truncate(reg_user_cnt/dau*100,2),0) add_rate,
        IFNULL(truncate(ad_active_user/dau*100,2),0) seep_rate,
        IFNULL(truncate(ad_active_user/reg_user_cnt*100,2),0) add_seep_rate,
        IFNULL(truncate(global_ad_active_user/dau*100,2),0) global_seep_rate,
        IFNULL(truncate(global_ad_active_user/reg_user_cnt*100,2),0) add_global_seep_rate,
        IFNULL(round(sum(ad_pv_active_user_pv_0)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv0,
        IFNULL(round(sum(ad_pv_active_user_pv_1)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv1,
        IFNULL(round(sum(ad_pv_active_user_pv_2)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv2,
        IFNULL(round(sum(ad_pv_active_user_pv_3)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv3,
        IFNULL(round(sum(ad_pv_active_user_pv_4)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv4,
        IFNULL(round(sum(ad_pv_active_user_pv_5)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv5,
        IFNULL(round(sum(ad_pv_active_user_pv_6)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv6,
        IFNULL(round(sum(ad_pv_active_user_pv_7)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv7,
        IFNULL(round(sum(ad_pv_active_user_pv_8)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv8,
        IFNULL(round(sum(ad_pv_active_user_pv_9)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv9,
        IFNULL(round(sum(ad_pv_active_user_pv_10)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv10,
        IFNULL(round(sum(ad_pv_active_user_pv_11_15)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv11,
        IFNULL(round(sum(ad_pv_active_user_pv_16_20)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv12,
        IFNULL(round(sum(ad_pv_active_user_pv_21_25)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv13,
        IFNULL(round(sum(ad_pv_active_user_pv_gt_25)/(sum(ad_pv_active_user_pv_0)+sum(ad_pv_active_user_pv_1)+sum(ad_pv_active_user_pv_2)+sum(ad_pv_active_user_pv_3)+sum(ad_pv_active_user_pv_4)+sum(ad_pv_active_user_pv_5)+sum(ad_pv_active_user_pv_6)+sum(ad_pv_active_user_pv_7)+sum(ad_pv_active_user_pv_8)+sum(ad_pv_active_user_pv_9)+sum(ad_pv_active_user_pv_10)+sum(ad_pv_active_user_pv_11_15)+sum(ad_pv_active_user_pv_16_20)+sum(ad_pv_active_user_pv_21_25)+sum(ad_pv_active_user_pv_gt_25))*100,2),0) pv14
        FROM ${table_name}
        where tdate BETWEEN #{start_date} AND #{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and download_channel in (${channel})
		</if>
		<if test="prjid != null and prjid != ''">
			and pid = #{prjid}
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and ad_type = #{adpos_type}
		</if>
		<if test="user_type != null and user_type != ''">
			and active_type = #{user_type}
		</if>
		<if test="appid_tag != null and appid_tag != ''">
			<choose>
				<when test="appid_tag_rev != null and appid_tag_rev != ''">
					AND CONCAT(appid,'#',download_channel) not in (${appid_tag})
				</when>
				<otherwise>
					AND CONCAT(appid,'#',download_channel) in (${appid_tag})
				</otherwise>
			</choose>
		</if>

		 group by tdate,appid,channel
        <!-- 增加广告位类型-->
        <if test="adpos_type_group != null and adpos_type_group != ''">
			,adpos_type
		</if>

		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY ${order_str}
			</when>

			<otherwise>
				order by tdate desc,actnum desc
			</otherwise>
		</choose>
    </select>
    <select id="selectDnEcpmActuser" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_ecpm_actuser_sql"/>
	</select>
	<select id="selectDnEcpmActuserSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
			TRUNCATE(SUM(xx.sum_revenue),2) sum_revenue,
			TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.sum_pv)*1000, 2) ecpm

		from (<include refid="dn_ecpm_actuser_sql"/>) xx
	</select>

    <sql id="dn_ecpm_actuser_sql">
		select
			tdate,appid,cha_id,adpos_type,
			IFNULL(actnum,0) actnum,
			IFNULL(addnum,0) addnum,
			IFNULL(sum_pv,0) sum_pv,
			IFNULL(TRUNCATE(addnum / actnum*100, 1),0) addrate,
			IFNULL(sum_revenue,0) sum_revenue,
			IFNULL(TRUNCATE(sum_revenue / sum_pv *1000/100,2),0) ecpm,
			IFNULL(TRUNCATE( pv1,2),0) pv1,
			IFNULL(TRUNCATE( pv2,2),0) pv2,
			IFNULL(TRUNCATE( pv3,2),0) pv3,
			IFNULL(TRUNCATE( pv4,2),0) pv4,
			IFNULL(TRUNCATE( pv5,2),0) pv5,
			IFNULL(TRUNCATE( pv6,2),0) pv6,
			IFNULL(TRUNCATE( pv7,2),0) pv7,
			IFNULL(TRUNCATE( pv8,2),0) pv8,
			IFNULL(TRUNCATE( pv9,2),0) pv9,
			IFNULL(TRUNCATE( pv10,2),0) pv10,
			IFNULL(TRUNCATE( pv11,2),0) pv11,
			IFNULL(TRUNCATE( pv12,2),0) pv12,
			IFNULL(TRUNCATE( pv13,2),0) pv13,
			IFNULL(TRUNCATE( pv14,2),0) pv14
		from
			(select  tdate,
					appid,
					download_channel cha_id,
					ad_type  adpos_type,

					dau  actnum,
					reg_user_cnt  addnum,
					round(sum(revenue),2) sum_revenue,
					sum(ad_cnt)  sum_pv,
					round((convert(ad_active_user_revenue_pv_1, varchar) / convert(ad_active_user_pv_1,varchar) )* 1000 / 100, 2 ) pv1,
					round((convert(ad_active_user_revenue_pv_2, varchar) / convert(ad_active_user_pv_2,varchar) )* 1000 / 100, 2 ) pv2,
					round((convert(ad_active_user_revenue_pv_3, varchar) / convert(ad_active_user_pv_3,varchar) )* 1000 / 100, 2 ) pv3,
					round((convert(ad_active_user_revenue_pv_4, varchar) / convert(ad_active_user_pv_4,varchar) )* 1000 / 100, 2 ) pv4,
					round((convert(ad_active_user_revenue_pv_5, varchar) / convert(ad_active_user_pv_5,varchar) )* 1000 / 100, 2 ) pv5,
					round((convert(ad_active_user_revenue_pv_6, varchar) / convert(ad_active_user_pv_6,varchar) )* 1000 / 100, 2 ) pv6,
					round((convert(ad_active_user_revenue_pv_7, varchar) / convert(ad_active_user_pv_7,varchar) )* 1000 / 100, 2 ) pv7,
					round((convert(ad_active_user_revenue_pv_8, varchar) / convert(ad_active_user_pv_8,varchar) )* 1000 / 100, 2 ) pv8,
					round((convert(ad_active_user_revenue_pv_9, varchar) / convert(ad_active_user_pv_9,varchar) )* 1000 / 100, 2 ) pv9,
					round((convert(ad_active_user_revenue_pv_10, varchar) / convert(ad_active_user_pv_10,varchar) )* 1000 / 100, 2 ) pv10,
					round((convert(ad_active_user_revenue_pv_11_15, varchar) / convert(ad_active_user_pv_11_15,varchar) )* 1000 / 100, 2 ) pv11,
					round((convert(ad_active_user_revenue_pv_16_20, varchar) / convert(ad_active_user_pv_16_20,varchar) )* 1000 / 100, 2 ) pv12,
					round((convert(ad_active_user_revenue_pv_21_25, varchar) / convert(ad_active_user_pv_21_25,varchar) )* 1000 / 100, 2 ) pv13,
					round((convert(ad_active_user_revenue_pv_gt_25, varchar) / convert(ad_active_user_pv_gt_25,varchar) )* 1000 / 100, 2 ) pv14
					from ${table_name}
			where tdate BETWEEN #{sdate} AND #{edate}
			<include refid="dn_extend_where_sql"/>

			<if test="user_type != null and user_type != ''">
				and active_type = #{user_type}
			</if>
			group by tdate,appid,cha_id,adpos_type) aa

		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY ${order_str}
			</when>

			<otherwise>
				order by tdate desc,sum_revenue desc
			</otherwise>
		</choose>
	</sql>



	<!-- 活跃用户收入分次数分布  -->
	<select id="selectDnRevenueActuser" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_revenue_actuser_sql"/>
	</select>
	<select id="selectDnRevenueActuserSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
			TRUNCATE(SUM(xx.sum_revenue),2) sum_revenue
		from (<include refid="dn_revenue_actuser_sql"/>) xx
	</select>
	<sql id="dn_revenue_actuser_sql">
		select xx.* from

			(select
				tdate,
				appid,
				download_channel cha_id,
				ad_type adpos_type,
				dau actnum,
				reg_user_cnt addnum,
				IFNULL(TRUNCATE(reg_user_cnt / dau*100, 1),0) addrate,
				IFNULL(TRUNCATE(sum(revenue)/100,2),0) sum_revenue,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_0,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv0,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_1,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv1,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_2,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv2,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_3,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv3,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_4,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv4,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_5,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv5,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_6,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv6,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_7,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv7,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_8,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv8,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_9,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv9,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_10,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv10,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_11_15,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv11,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_16_20,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv12,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_21_25,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv13,
				IFNULL(TRUNCATE(sum(CONCAT(ad_active_user_revenue_pv_gt_25,''))/(sum(ad_active_user_revenue_pv_0)+sum(ad_active_user_revenue_pv_1)+sum(ad_active_user_revenue_pv_2)+sum(ad_active_user_revenue_pv_3)+sum(ad_active_user_revenue_pv_4)+sum(ad_active_user_revenue_pv_5)+sum(ad_active_user_revenue_pv_6)+sum(ad_active_user_revenue_pv_7)+sum(ad_active_user_revenue_pv_8)+sum(ad_active_user_revenue_pv_9)+sum(ad_active_user_revenue_pv_10)+sum(ad_active_user_revenue_pv_11_15)+sum(ad_active_user_revenue_pv_16_20)+sum(ad_active_user_revenue_pv_21_25)+sum(ad_active_user_revenue_pv_gt_25))*100,2),0) pv14
			FROM ${table_name}
			where tdate BETWEEN #{sdate} AND #{edate}
			<include refid="dn_extend_where_sql"/>

			<if test="user_type != null and user_type != ''">
				and active_type = #{user_type}
			</if>
			group by tdate,appid,download_channel,ad_type) xx

		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY ${order_str}
			</when>

			<otherwise>
				order by tdate desc,sum_revenue desc
			</otherwise>
		</choose>
	</sql>

	<!-- 新增用户IPU日趋势 -->
	<select id="selectAdshowAdduser" parameterType="java.util.Map" resultType="java.util.Map">
		select xx.* from

		(select
			concat(tdate,'') tdate,
			appid,
			download_channel channel,
			IFNULL(reg_user_cnt,0) addnum,
			ad_type adpos_type,
			IFNULL(round(ad_pv_1/reg_user_cnt,2),0) pv1,
			IFNULL(round(ad_pv_2/reg_user_cnt,2),0) pv2,
			IFNULL(round(ad_pv_3/reg_user_cnt,2),0) pv3,
			IFNULL(round(ad_pv_4/reg_user_cnt,2),0) pv4,
			IFNULL(round(ad_pv_5/reg_user_cnt,2),0) pv5,
			IFNULL(round(ad_pv_6/reg_user_cnt,2),0) pv6,
			IFNULL(round(ad_pv_7/reg_user_cnt,2),0) pv7,
			IFNULL(round(ad_pv_8/reg_user_cnt,2),0) pv8,
			IFNULL(round(ad_pv_9/reg_user_cnt,2),0) pv9,
			IFNULL(round(ad_pv_10/reg_user_cnt,2),0) pv10,
			IFNULL(round(ad_pv_11/reg_user_cnt,2),0) pv11,
			IFNULL(round(ad_pv_12/reg_user_cnt,2),0) pv12,
			IFNULL(round(ad_pv_13/reg_user_cnt,2),0) pv13,
			IFNULL(round(ad_pv_14/reg_user_cnt,2),0) pv14,
			IFNULL(round(ad_pv_15/reg_user_cnt,2),0) pv15,
			IFNULL(round(ad_pv_16/reg_user_cnt,2),0) pv16,
			IFNULL(round(ad_pv_17/reg_user_cnt,2),0) pv17,
			IFNULL(round(ad_pv_18/reg_user_cnt,2),0) pv18,
			IFNULL(round(ad_pv_19/reg_user_cnt,2),0) pv19,
			IFNULL(round(ad_pv_20/reg_user_cnt,2),0) pv20,
			IFNULL(round(ad_pv_21/reg_user_cnt,2),0) pv21,
			IFNULL(round(ad_pv_22/reg_user_cnt,2),0) pv22,
			IFNULL(round(ad_pv_23/reg_user_cnt,2),0) pv23,
			IFNULL(round(ad_pv_24/reg_user_cnt,2),0) pv24,
			IFNULL(round(ad_pv_25/reg_user_cnt,2),0) pv25,
			IFNULL(round(ad_pv_26/reg_user_cnt,2),0) pv26,
			IFNULL(round(ad_pv_27/reg_user_cnt,2),0) pv27,
			IFNULL(round(ad_pv_28/reg_user_cnt,2),0) pv28,
			IFNULL(round(ad_pv_29/reg_user_cnt,2),0) pv29,
			IFNULL(round(ad_pv_30/reg_user_cnt,2),0) pv30

		from ${table_name}
		where tdate BETWEEN #{start_date} AND #{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and download_channel in (${channel})
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and ad_type = #{adpos_type}
		</if>

		group by tdate,appid,download_channel,ad_type) xx

		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY ${order_str}
			</when>

			<otherwise>
				order by tdate desc,addnum desc
			</otherwise>
		</choose>
	</select>


	<!-- 新增用户eCPM日趋势 -->
	<select id="selectDnEcpmAdduser" parameterType="java.util.Map" resultType="java.util.Map">
		select xx.* from

			(select
				concat(tdate,'') tdate,
				appid,
				download_channel cha_id,
				IFNULL(reg_user_cnt,0) addnum,
				ad_type adpos_type,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_1,'')/ad_pv_1*10,2),0) ecpm1,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_2,'')/ad_pv_2*10,2),0) ecpm2,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_3,'')/ad_pv_3*10,2),0) ecpm3,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_4,'')/ad_pv_4*10,2),0) ecpm4,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_5,'')/ad_pv_5*10,2),0) ecpm5,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_6,'')/ad_pv_6*10,2),0) ecpm6,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_7,'')/ad_pv_7*10,2),0) ecpm7,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_8,'')/ad_pv_8*10,2),0) ecpm8,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_9,'')/ad_pv_9*10,2),0) ecpm9,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_10,'')/ad_pv_10*10,2),0) ecpm10,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_11,'')/ad_pv_11*10,2),0) ecpm11,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_12,'')/ad_pv_12*10,2),0) ecpm12,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_13,'')/ad_pv_13*10,2),0) ecpm13,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_14,'')/ad_pv_14*10,2),0) ecpm14,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_15,'')/ad_pv_15*10,2),0) ecpm15,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_16,'')/ad_pv_16*10,2),0) ecpm16,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_17,'')/ad_pv_17*10,2),0) ecpm17,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_18,'')/ad_pv_18*10,2),0) ecpm18,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_19,'')/ad_pv_19*10,2),0) ecpm19,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_20,'')/ad_pv_20*10,2),0) ecpm20,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_21,'')/ad_pv_21*10,2),0) ecpm21,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_22,'')/ad_pv_22*10,2),0) ecpm22,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_23,'')/ad_pv_23*10,2),0) ecpm23,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_24,'')/ad_pv_24*10,2),0) ecpm24,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_25,'')/ad_pv_25*10,2),0) ecpm25,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_26,'')/ad_pv_26*10,2),0) ecpm26,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_27,'')/ad_pv_27*10,2),0) ecpm27,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_28,'')/ad_pv_28*10,2),0) ecpm28,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_29,'')/ad_pv_29*10,2),0) ecpm29,
				IFNULL(TRUNCATE(CONCAT(add_revenue_arpu_30,'')/ad_pv_30*10,2),0) ecpm30

			from ${table_name}
			where tdate BETWEEN #{sdate} AND #{edate}
			<if test="appid != null and appid != ''">
				and appid in (${appid})
			</if>
			<if test="cha_id != null and cha_id != ''">
				and download_channel in (${cha_id})
			</if>
			<if test="adpos_type != null and adpos_type != ''">
				and ad_type = #{adpos_type}
			</if>

			group by tdate,appid,download_channel,ad_type) xx

		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY ${order_str}
			</when>

			<otherwise>
				order by tdate desc,addnum desc
			</otherwise>
		</choose>
	</select>


	<!-- 新增用户LTV趋势  -->
	<select id="selectSumLtvAdduser" parameterType="java.util.Map" resultType="java.util.Map">
		select
			xx.*,
			IFNULL(TRUNCATE(ltv1+ltv2+ltv3,2),0) sum_ltv3,
			IFNULL(TRUNCATE(ltv1+ltv2+ltv3+ltv4+ltv5+ltv6+ltv7,2),0) sum_ltv7,
			IFNULL(TRUNCATE(ltv1+ltv2+ltv3+ltv4+ltv5+ltv6+ltv7+ltv8+ltv9+ltv10+ltv11+ltv12+ltv13+ltv14,2),0) sum_ltv14,
			IFNULL(TRUNCATE(ltv1+ltv2+ltv3+ltv4+ltv5+ltv6+ltv7+ltv8+ltv9+ltv10+ltv11+ltv12+ltv13+ltv14+ltv15+ltv16+ltv17+ltv18+ltv19+ltv20+ltv21+ltv22+ltv23+ltv24+ltv25+ltv26+ltv27+ltv28+ltv29+ltv30,2),0) sum_ltv30

		from (<include refid="dn_sumltv_adduser_sql"/>) xx
	</select>
	<select id="selectSumLtvAdduserSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
			TRUNCATE(SUM(xx.ltv1), 2) ltv1,
			TRUNCATE(SUM(xx.ltv2), 2) ltv2,
			TRUNCATE(SUM(xx.ltv3), 2) ltv3,
			TRUNCATE(SUM(xx.ltv4), 2) ltv4,
			TRUNCATE(SUM(xx.ltv5), 2) ltv5,
			TRUNCATE(SUM(xx.ltv6), 2) ltv6,
			TRUNCATE(SUM(xx.ltv7), 2) ltv7,
			TRUNCATE(SUM(xx.ltv8), 2) ltv8,
			TRUNCATE(SUM(xx.ltv9), 2) ltv9,
			TRUNCATE(SUM(xx.ltv10), 2) ltv10,
			TRUNCATE(SUM(xx.ltv11), 2) ltv11,
			TRUNCATE(SUM(xx.ltv12), 2) ltv12,
			TRUNCATE(SUM(xx.ltv13), 2) ltv13,
			TRUNCATE(SUM(xx.ltv14), 2) ltv14,
			TRUNCATE(SUM(xx.ltv15), 2) ltv15,
			TRUNCATE(SUM(xx.ltv16), 2) ltv16,
			TRUNCATE(SUM(xx.ltv17), 2) ltv17,
			TRUNCATE(SUM(xx.ltv18), 2) ltv18,
			TRUNCATE(SUM(xx.ltv19), 2) ltv19,
			TRUNCATE(SUM(xx.ltv20), 2) ltv20,
			TRUNCATE(SUM(xx.ltv21), 2) ltv21,
			TRUNCATE(SUM(xx.ltv22), 2) ltv22,
			TRUNCATE(SUM(xx.ltv23), 2) ltv23,
			TRUNCATE(SUM(xx.ltv24), 2) ltv24,
			TRUNCATE(SUM(xx.ltv25), 2) ltv25,
			TRUNCATE(SUM(xx.ltv26), 2) ltv26,
			TRUNCATE(SUM(xx.ltv27), 2) ltv27,
			TRUNCATE(SUM(xx.ltv28), 2) ltv28,
			TRUNCATE(SUM(xx.ltv29), 2) ltv29,
			TRUNCATE(SUM(xx.ltv30), 2) ltv30,

			TRUNCATE(SUM(xx.ltv1)+SUM(xx.ltv2)+SUM(xx.ltv3),2) sum_ltv3,
			TRUNCATE(SUM(xx.ltv1)+SUM(xx.ltv2)+SUM(xx.ltv3)+SUM(xx.ltv4)+SUM(xx.ltv5)+SUM(xx.ltv6)+SUM(xx.ltv7),2) sum_ltv7,
			TRUNCATE(SUM(xx.ltv1)+SUM(xx.ltv2)+SUM(xx.ltv3)+SUM(xx.ltv4)+SUM(xx.ltv5)+SUM(xx.ltv6)+SUM(xx.ltv7)+SUM(xx.ltv8)+SUM(xx.ltv9)+SUM(xx.ltv10)+SUM(xx.ltv11)+SUM(xx.ltv12)+SUM(xx.ltv13)+SUM(xx.ltv14),2) sum_ltv14,
			TRUNCATE(SUM(xx.ltv1)+SUM(xx.ltv2)+SUM(xx.ltv3)+SUM(xx.ltv4)+SUM(xx.ltv5)+SUM(xx.ltv6)+SUM(xx.ltv7)+SUM(xx.ltv8)+SUM(xx.ltv9)+SUM(xx.ltv10)+SUM(xx.ltv11)+SUM(xx.ltv12)+SUM(xx.ltv13)+SUM(xx.ltv14)+SUM(xx.ltv15)+SUM(xx.ltv16)+SUM(xx.ltv17)+SUM(xx.ltv18)+SUM(xx.ltv19)+SUM(xx.ltv20)+SUM(xx.ltv21)+SUM(xx.ltv22)+SUM(xx.ltv23)+SUM(xx.ltv24)+SUM(xx.ltv25)+SUM(xx.ltv26)+SUM(xx.ltv27)+SUM(xx.ltv28)+SUM(xx.ltv29)+SUM(xx.ltv30),2) sum_ltv30

		from (<include refid="dn_sumltv_adduser_sql"/>) xx
	</select>
	<sql id="dn_sumltv_adduser_sql">
		select
			CONCAT(tdate,'') tdate,
			appid,
			download_channel cha_id,
			<if test="adpos_type_group != null and adpos_type_group != ''">ad_type adpos_type,</if>
			IFNULL(reg_user_cnt,0) addnum,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_1)/reg_user_cnt/100,2),0) ltv1,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_2)/reg_user_cnt/100,2),0) ltv2,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_3)/reg_user_cnt/100,2),0) ltv3,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_4)/reg_user_cnt/100,2),0) ltv4,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_5)/reg_user_cnt/100,2),0) ltv5,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_6)/reg_user_cnt/100,2),0) ltv6,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_7)/reg_user_cnt/100,2),0) ltv7,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_8)/reg_user_cnt/100,2),0) ltv8,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_9)/reg_user_cnt/100,2),0) ltv9,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_10)/reg_user_cnt/100,2),0) ltv10,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_11)/reg_user_cnt/100,2),0) ltv11,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_12)/reg_user_cnt/100,2),0) ltv12,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_13)/reg_user_cnt/100,2),0) ltv13,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_14)/reg_user_cnt/100,2),0) ltv14,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_15)/reg_user_cnt/100,2),0) ltv15,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_16)/reg_user_cnt/100,2),0) ltv16,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_17)/reg_user_cnt/100,2),0) ltv17,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_18)/reg_user_cnt/100,2),0) ltv18,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_19)/reg_user_cnt/100,2),0) ltv19,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_20)/reg_user_cnt/100,2),0) ltv20,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_21)/reg_user_cnt/100,2),0) ltv21,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_22)/reg_user_cnt/100,2),0) ltv22,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_23)/reg_user_cnt/100,2),0) ltv23,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_24)/reg_user_cnt/100,2),0) ltv24,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_25)/reg_user_cnt/100,2),0) ltv25,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_26)/reg_user_cnt/100,2),0) ltv26,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_27)/reg_user_cnt/100,2),0) ltv27,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_28)/reg_user_cnt/100,2),0) ltv28,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_29)/reg_user_cnt/100,2),0) ltv29,
			IFNULL(TRUNCATE(SUM(add_revenue_arpu_30)/reg_user_cnt/100,2),0) ltv30

		from ${table_name}
		where tdate BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and download_channel in (${cha_id})
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and ad_type = #{adpos_type}
		</if>
		group by tdate,appid,download_channel
		<if test="adpos_type_group != null and adpos_type_group != ''">,ad_type</if>

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>

			<otherwise>
				order by tdate desc,addnum desc
			</otherwise>
		</choose>
	</sql>


    <sql id="dn_extend_where_sql">
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="prjid != null and prjid != ''">
			and pid in (${prjid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and download_channel in (${cha_id})
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and ad_type = #{adpos_type}
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>
	</sql>


	<select id="selectClickActuser" resultType="java.util.Map">
		SELECT
		tdate,
		appid,
		download_channel channel,
		<if test="adpos_type_group != null and adpos_type_group != ''">
			ad_type adpos_type,
			ad_type ad_type,
		</if>
		IFNULL(dau,0) actnum,
		IFNULL(reg_user_cnt,0) addnum,
		IFNULL(global_ad_active_user,0) global_aduser,
		IFNULL(ad_active_user,0) aduser,
		IFNULL(truncate(reg_user_cnt/dau*100,2),0) add_rate,
		IFNULL(truncate(ad_active_user/dau*100,2),0) seep_rate,
		IFNULL(truncate(ad_active_user/reg_user_cnt*100,2),0) add_seep_rate,
		IFNULL(truncate(global_ad_active_user/dau*100,2),0) global_seep_rate,
		IFNULL(truncate(global_ad_active_user/reg_user_cnt*100,2),0) add_global_seep_rate,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_0)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv0,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_1)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv1,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_2)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv2,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_3)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv3,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_4)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv4,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_5)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv5,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_6)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv6,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_7)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv7,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_8)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv8,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_9)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv9,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_10)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv10,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_11_15)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv11,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_16_20)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv12,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_21_25)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv13,
		IFNULL(round(sum(ad_pv_active_user_click_cnt_gt_25)/(sum(ad_pv_active_user_click_cnt_0)+sum(ad_pv_active_user_click_cnt_1)+sum(ad_pv_active_user_click_cnt_2)+sum(ad_pv_active_user_click_cnt_3)+sum(ad_pv_active_user_click_cnt_4)+sum(ad_pv_active_user_click_cnt_5)+sum(ad_pv_active_user_click_cnt_6)+sum(ad_pv_active_user_click_cnt_7)+sum(ad_pv_active_user_click_cnt_8)+sum(ad_pv_active_user_click_cnt_9)+sum(ad_pv_active_user_click_cnt_10)+sum(ad_pv_active_user_click_cnt_11_15)+sum(ad_pv_active_user_click_cnt_16_20)+sum(ad_pv_active_user_click_cnt_21_25)+sum(ad_pv_active_user_click_cnt_gt_25))*100,2),0) pv14
		from
		(
		select a.tdate,a.appid,a.download_channel,a.ad_type,a.active_type,a.dau,a.reg_user_cnt,
		SUM(a.ad_pv_active_user_pv_0) AS ad_pv_active_user_pv_0, SUM(a.ad_pv_active_user_pv_1) AS ad_pv_active_user_pv_1, SUM(a.ad_pv_active_user_pv_2) AS ad_pv_active_user_pv_2, SUM(a.ad_pv_active_user_pv_3) AS ad_pv_active_user_pv_3, SUM(a.ad_pv_active_user_pv_4) AS ad_pv_active_user_pv_4, SUM(a.ad_pv_active_user_pv_5) AS ad_pv_active_user_pv_5, SUM(a.ad_pv_active_user_pv_6) AS ad_pv_active_user_pv_6, SUM(a.ad_pv_active_user_pv_7) AS ad_pv_active_user_pv_7, SUM(a.ad_pv_active_user_pv_8) AS ad_pv_active_user_pv_8, SUM(a.ad_pv_active_user_pv_9) AS ad_pv_active_user_pv_9,
		SUM(a.ad_pv_active_user_pv_10) AS ad_pv_active_user_pv_10, SUM(a.ad_pv_active_user_pv_11_15) AS ad_pv_active_user_pv_11_15, SUM(a.ad_pv_active_user_pv_16_20) AS ad_pv_active_user_pv_16_20, SUM(a.ad_pv_active_user_pv_21_25) AS ad_pv_active_user_pv_21_25, SUM(a.ad_pv_active_user_pv_gt_25) AS ad_pv_active_user_pv_gt_25, SUM(a.ad_active_user_revenue_pv_0) AS ad_active_user_revenue_pv_0, SUM(a.ad_active_user_revenue_pv_1) AS ad_active_user_revenue_pv_1, SUM(a.ad_active_user_revenue_pv_2) AS ad_active_user_revenue_pv_2, SUM(a.ad_active_user_revenue_pv_3) AS ad_active_user_revenue_pv_3,
		SUM(a.ad_active_user_revenue_pv_4) AS ad_active_user_revenue_pv_4, SUM(a.ad_active_user_revenue_pv_5) AS ad_active_user_revenue_pv_5, SUM(a.ad_active_user_revenue_pv_6) AS ad_active_user_revenue_pv_6, SUM(a.ad_active_user_revenue_pv_7) AS ad_active_user_revenue_pv_7, SUM(a.ad_active_user_revenue_pv_8) AS ad_active_user_revenue_pv_8, SUM(a.ad_active_user_revenue_pv_9) AS ad_active_user_revenue_pv_9, SUM(a.ad_active_user_revenue_pv_10) AS ad_active_user_revenue_pv_10, SUM(a.ad_active_user_revenue_pv_11_15) AS ad_active_user_revenue_pv_11_15,
		SUM(a.ad_active_user_revenue_pv_16_20) AS ad_active_user_revenue_pv_16_20, SUM(a.ad_active_user_revenue_pv_21_25) AS ad_active_user_revenue_pv_21_25, SUM(a.ad_active_user_revenue_pv_gt_25) AS ad_active_user_revenue_pv_gt_25, SUM(a.ad_active_user_pv_0) AS ad_active_user_pv_0, SUM(a.ad_active_user_pv_1) AS ad_active_user_pv_1, SUM(a.ad_active_user_pv_2) AS ad_active_user_pv_2, SUM(a.ad_active_user_pv_3) AS ad_active_user_pv_3, SUM(a.ad_active_user_pv_4) AS ad_active_user_pv_4, SUM(a.ad_active_user_pv_5) AS ad_active_user_pv_5, SUM(a.ad_active_user_pv_6) AS ad_active_user_pv_6,
		SUM(a.ad_active_user_pv_7) AS ad_active_user_pv_7, SUM(a.ad_active_user_pv_8) AS ad_active_user_pv_8, SUM(a.ad_active_user_pv_9) AS ad_active_user_pv_9, SUM(a.ad_active_user_pv_10) AS ad_active_user_pv_10, SUM(a.ad_active_user_pv_11_15) AS ad_active_user_pv_11_15, SUM(a.ad_active_user_pv_16_20) AS ad_active_user_pv_16_20, SUM(a.ad_active_user_pv_21_25) AS ad_active_user_pv_21_25, SUM(a.ad_active_user_pv_gt_25) AS ad_active_user_pv_gt_25, SUM(a.ad_pv_active_user_click_cnt_0) AS ad_pv_active_user_click_cnt_0, SUM(a.ad_pv_active_user_click_cnt_1) AS ad_pv_active_user_click_cnt_1,
		SUM(a.ad_pv_active_user_click_cnt_2) AS ad_pv_active_user_click_cnt_2, SUM(a.ad_pv_active_user_click_cnt_3) AS ad_pv_active_user_click_cnt_3, SUM(a.ad_pv_active_user_click_cnt_4) AS ad_pv_active_user_click_cnt_4, SUM(a.ad_pv_active_user_click_cnt_5) AS ad_pv_active_user_click_cnt_5, SUM(a.ad_pv_active_user_click_cnt_6) AS ad_pv_active_user_click_cnt_6, SUM(a.ad_pv_active_user_click_cnt_7) AS ad_pv_active_user_click_cnt_7, SUM(a.ad_pv_active_user_click_cnt_8) AS ad_pv_active_user_click_cnt_8, SUM(a.ad_pv_active_user_click_cnt_9) AS ad_pv_active_user_click_cnt_9,
		SUM(a.ad_pv_active_user_click_cnt_10) AS ad_pv_active_user_click_cnt_10, SUM(a.ad_pv_active_user_click_cnt_11_15) AS ad_pv_active_user_click_cnt_11_15, SUM(a.ad_pv_active_user_click_cnt_16_20) AS ad_pv_active_user_click_cnt_16_20, SUM(a.ad_pv_active_user_click_cnt_21_25) AS ad_pv_active_user_click_cnt_21_25, SUM(a.ad_pv_active_user_click_cnt_gt_25) AS ad_pv_active_user_click_cnt_gt_25,
		b.global_click_user global_ad_active_user,
		CASE a.ad_type
			WHEN 'splash' THEN b.splash_click_users
			WHEN 'plaque' THEN b.plaque_click_users
			WHEN 'msg' THEN b.msg_click_users
			WHEN 'video' THEN b.video_click_users
			WHEN 'icon' THEN b.icon_click_users
			WHEN 'banner' THEN b.banner_click_users
		END as ad_active_user
	 	FROM ads_active_user_revenue_ecpm_pv_analyze_daily a
		left join ads_user_ad_click_penetration_reg_and_active b on a.appid = b.appid and a.download_channel = b.download_channel and a.active_type = b.active_type and a.tdate = b.tdate
		where a.tdate BETWEEN #{start_date} AND #{end_date}
		<if test="appid != null and appid != ''">
			and a.appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and a.download_channel in (${channel})
		</if>
		<if test="prjid != null and prjid != ''">
			and a.pid = #{prjid}
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and a.ad_type = #{adpos_type}
		</if>
		<if test="user_type != null and user_type != ''">
			and a.active_type = #{user_type}
		</if>
		<if test="appid_tag != null and appid_tag != ''">
			<choose>
				<when test="appid_tag_rev != null and appid_tag_rev != ''">
					AND CONCAT(a.appid,'#',a.download_channel) not in (${appid_tag})
				</when>
				<otherwise>
					AND CONCAT(a.appid,'#',a.download_channel) in (${appid_tag})
				</otherwise>
			</choose>
		</if>
	 	group by a.tdate,a.appid,a.download_channel,a.ad_type,a.active_type
		) a
		group by tdate,appid,channel
		<!-- 增加广告位类型-->
		<if test="adpos_type_group != null and adpos_type_group != ''">
			,adpos_type
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY ${order_str}
			</when>
			<otherwise>
				order by tdate desc,actnum desc
			</otherwise>
		</choose>
	</select>


	<!-- 新增用户IPU日趋势 -->
	<select id="selectAdRegClickUser" parameterType="java.util.Map" resultType="java.util.Map">
		select xx.* from
		(select
		concat(tdate,'') tdate,
		appid,
		download_channel channel,
		IFNULL(reg_user_cnt,0) addnum,
		ad_type adpos_type,
		IFNULL(round(ad_click_pv_1/reg_user_cnt,2),0) pv1,
		IFNULL(round(ad_click_pv_2/reg_user_cnt,2),0) pv2,
		IFNULL(round(ad_click_pv_3/reg_user_cnt,2),0) pv3,
		IFNULL(round(ad_click_pv_4/reg_user_cnt,2),0) pv4,
		IFNULL(round(ad_click_pv_5/reg_user_cnt,2),0) pv5,
		IFNULL(round(ad_click_pv_6/reg_user_cnt,2),0) pv6,
		IFNULL(round(ad_click_pv_7/reg_user_cnt,2),0) pv7,
		IFNULL(round(ad_click_pv_8/reg_user_cnt,2),0) pv8,
		IFNULL(round(ad_click_pv_9/reg_user_cnt,2),0) pv9,
		IFNULL(round(ad_click_pv_10/reg_user_cnt,2),0) pv10,
		IFNULL(round(ad_click_pv_11/reg_user_cnt,2),0) pv11,
		IFNULL(round(ad_click_pv_12/reg_user_cnt,2),0) pv12,
		IFNULL(round(ad_click_pv_13/reg_user_cnt,2),0) pv13,
		IFNULL(round(ad_click_pv_14/reg_user_cnt,2),0) pv14,
		IFNULL(round(ad_click_pv_15/reg_user_cnt,2),0) pv15,
		IFNULL(round(ad_click_pv_16/reg_user_cnt,2),0) pv16,
		IFNULL(round(ad_click_pv_17/reg_user_cnt,2),0) pv17,
		IFNULL(round(ad_click_pv_18/reg_user_cnt,2),0) pv18,
		IFNULL(round(ad_click_pv_19/reg_user_cnt,2),0) pv19,
		IFNULL(round(ad_click_pv_20/reg_user_cnt,2),0) pv20,
		IFNULL(round(ad_click_pv_21/reg_user_cnt,2),0) pv21,
		IFNULL(round(ad_click_pv_22/reg_user_cnt,2),0) pv22,
		IFNULL(round(ad_click_pv_23/reg_user_cnt,2),0) pv23,
		IFNULL(round(ad_click_pv_24/reg_user_cnt,2),0) pv24,
		IFNULL(round(ad_click_pv_25/reg_user_cnt,2),0) pv25,
		IFNULL(round(ad_click_pv_26/reg_user_cnt,2),0) pv26,
		IFNULL(round(ad_click_pv_27/reg_user_cnt,2),0) pv27,
		IFNULL(round(ad_click_pv_28/reg_user_cnt,2),0) pv28,
		IFNULL(round(ad_click_pv_29/reg_user_cnt,2),0) pv29,
		IFNULL(round(ad_click_pv_30/reg_user_cnt,2),0) pv30
		from ads_reg_user_ltv_ecpm_pv_analyze_daily
		where tdate BETWEEN #{start_date} AND #{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and download_channel in (${channel})
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and ad_type = #{adpos_type}
		</if>
		group by tdate,appid,download_channel,ad_type) xx
		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY ${order_str}
			</when>

			<otherwise>
				order by tdate desc,addnum desc
			</otherwise>
		</choose>
	</select>

</mapper>