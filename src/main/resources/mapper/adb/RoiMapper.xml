<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.RoiMapper">

	
	<!-- 微信小游戏ROI报表列表查询  -->
  	<select id="wechatGameRoiReport" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam" resultType="com.wbgame.pojo.jettison.AdRoiDTO">
		<include refid="wx_game_roi_sql"/>
	</select>
	<!-- 微信小游戏ROI报表汇总  -->
	<select id="wechatGameRoiReportTotal" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam"  resultType="com.wbgame.pojo.jettison.AdRoiDTO">
		select 
			day as day,appId as appId,media media,channel channel,accountId accountId,groupName groupName,campaignName campaignName,campaignId campaignId,
			SUM(t.addUser) addUser,SUM(t.activeUser) activeUser,SUM(t.activeUser) activeUser,SUM(t.totalIncome) totalIncome,SUM(t.ltv1) ltv1,sum(cost) as cost,
			SUM(t.ltv2) ltv2,SUM(t.ltv3) ltv3,SUM(t.ltv4) ltv4,SUM(t.ltv5) ltv5,sum(ltv6) as ltv6,SUM(t.ltv14) ltv14,SUM(t.ltv30) ltv30,SUM(t.ltv36) ltv36,
			sum(t.ltv8) as ltv8,sum(t.ltv9) as ltv9,sum(t.ltv10) as ltv10,sum(t.ltv11) as ltv11,sum(t.ltv12) as ltv12,sum(t.ltv13) as ltv13,sum(t.ltv15) as ltv15,
			sum(t.ltv16) as ltv16,sum(t.ltv17) as ltv17,sum(t.ltv18) as ltv18,sum(t.ltv19) as ltv19,sum(t.ltv20) as ltv20,sum(t.ltv21) as ltv21,sum(t.ltv22) as ltv22,
			sum(t.ltv23) as ltv23,sum(t.ltv24) as ltv24,sum(t.ltv25) as ltv25,sum(t.ltv26) as ltv26,sum(t.ltv27) as ltv27,sum(t.ltv28) as ltv28,sum(t.ltv29) as ltv29,
			SUM(t.ltv42) ltv42,SUM(t.ltv48) ltv48,SUM(t.ltv54) ltv54,SUM(t.ltv60) ltv60,SUM(t.ltv75) ltv75,SUM(t.ltv90) ltv90,SUM(t.ltv120) ltv120,
			sum(t.ltv105) as ltv105,sum(t.ltv135) as ltv135,sum(t.ltv150) as ltv150,sum(t.ltv165) as ltv165,sum(t.ltv180) as ltv180,sum(t.ltv195) as ltv195,
			sum(t.ltv210) as ltv210,sum(t.ltv225) as ltv225,sum(t.ltv240) as ltv240,sum(t.ltv255) as ltv255,sum(t.ltv270) as ltv270,sum(t.ltv285) as ltv285,
			sum(t.ltv300) as ltv300,sum(t.ltv315) as ltv315,sum(t.ltv330) as ltv330,sum(t.ltv345) as ltv345,sum(t.ltv360) as ltv360,
			
			SUM(t.rdOne) rdOne,SUM(t.rdTwo) rdTwo,SUM(t.rdThree) rdThree,SUM(t.rdFour) rdFour,SUM(t.rdFive) rdFive,
			SUM(t.rdSix) rdSix,SUM(t.rdSeven) rdSeven,SUM(t.rdFourteen) rdFourteen,SUM(t.rdThrity) rdThrity,
			sum(t.rdThritySix) as rdThritySix,sum(t.rdFortyTwo) AS rdFortyTwo,sum(t.rdFortyEight) as rdFortyEight,sum(t.rdFiftyFour) rdFiftyFour,sum(t.rdSixty) rdSixty,
			sum(t.rd8) as rd8,sum(t.rd9) as rd9,sum(t.rd10) as rd10,sum(t.rd11) as rd11,sum(t.rd12) as rd12,sum(t.rd13) as rd13,sum(t.rd15) as rd15,
			sum(t.rd16) as rd16,sum(t.rd17) as rd17,sum(t.rd18) as rd18,sum(t.rd19) as rd19,sum(t.rd20) as rd20,sum(t.rd21) as rd21,sum(t.rd22) as rd22,
			sum(t.rd23) as rd23,sum(t.rd24) as rd24,sum(t.rd25) as rd25,sum(t.rd26) as rd26,sum(t.rd27) as rd27,sum(t.rd28) as rd28,sum(t.rd29) as rd29,
			sum(t.rd75) as rd75,sum(t.rd90) as rd90,sum(t.rd120) as rd120,
			sum(t.rd105) as rd105,sum(t.rd135) as rd135,sum(t.rd150) as rd150,sum(t.rd165) as rd165,sum(t.rd180) as rd180,sum(t.rd195) as rd195,
			sum(t.rd210) as rd210,sum(t.rd225) as rd225,sum(t.rd240) as rd240,sum(t.rd255) as rd255,sum(t.rd270) as rd270,sum(t.rd285) as rd285,
			sum(t.rd300) as rd300,sum(t.rd315) as rd315,sum(t.rd330) as rd330,sum(t.rd345) as rd345,sum(t.rd360) as rd360,
			
			
			sum(t.ipaLtv8) as ipaLtv8,sum(t.ipaLtv9) as ipaLtv9,sum(t.ipaLtv10) as ipaLtv10,sum(t.ipaLtv11) as ipaLtv11,sum(t.ipaLtv12) as ipaLtv12,sum(t.ipaLtv13) as ipaLtv13,
			sum(t.ipaLtv15) as ipaLtv15,sum(t.ipaLtv16) as ipaLtv16,sum(t.ipaLtv17) as ipaLtv17,sum(t.ipaLtv18) as ipaLtv18,sum(t.ipaLtv19) as ipaLtv19,
			sum(t.ipaLtv20) as ipaLtv20,sum(t.ipaLtv21) as ipaLtv21,sum(t.ipaLtv22) as ipaLtv22,sum(t.ipaLtv23) as ipaLtv23,sum(t.ipaLtv24) as ipaLtv24,sum(t.ipaLtv25) as ipaLtv25,
			sum(t.ipaLtv26) as ipaLtv26,sum(t.ipaLtv27) as ipaLtv27,sum(t.ipaLtv28) as ipaLtv28,sum(t.ipaLtv29) as ipaLtv29,
			sum(t.ipaLtv2) as ipaLtv2,sum(t.ipaLtv3) as ipaLtv3,sum(t.ipaLtv4) as ipaLtv4,sum(t.ipaLtv5) as ipaLtv5,
			sum(t.ipaLtv6) as ipaLtv6,sum(t.ipaLtv7) as ipaLtv7,sum(t.ipaLtv14) as ipaLtv14,sum(t.ipaLtv30) as ipaLtv30,
			sum(t.ipaLtv36) as ipaLtv36,sum(t.ipaLtv42) as ipaLtv42,sum(t.ipaLtv48) as ipaLtv48,sum(t.ipaLtv54) as ipaLtv54,
			sum(t.ipaLtv60) as ipaLtv60,sum(t.ipaLtv75) as ipaLtv75,sum(t.ipaLtv90) as ipaLtv90,sum(t.ipaLtv120) as ipaLtv120,
			sum(t.ipaLtv105) as ipaLtv105,sum(t.ipaLtv135) as ipaLtv135,sum(t.ipaLtv150) as ipaLtv150,sum(t.ipaLtv165) as ipaLtv165,sum(t.ipaLtv180) as ipaLtv180,sum(t.ipaLtv195) as ipaLtv195,
			sum(t.ipaLtv210) as ipaLtv210,sum(t.ipaLtv225) as ipaLtv225,sum(t.ipaLtv240) as ipaLtv240,sum(t.ipaLtv255) as ipaLtv255,sum(t.ipaLtv270) as ipaLtv270,sum(t.ipaLtv285) as ipaLtv285,
			sum(t.ipaLtv300) as ipaLtv300,sum(t.ipaLtv315) as ipaLtv315,sum(t.ipaLtv330) as ipaLtv330,sum(t.ipaLtv345) as ipaLtv345,sum(t.ipaLtv360) as ipaLtv360,
			
			
			sum(t.ipaRd1) as ipaRd1,sum(t.ipaRd2) as ipaRd2,sum(t.ipaRd3) as ipaRd3,sum(t.ipaRd4) as ipaRd4,
			sum(t.ipaRd5) as ipaRd5,sum(t.ipaRd6) as ipaRd6,sum(t.ipaRd7) as ipaRd7,sum(t.ipaRd14) as ipaRd14,
			sum(t.ipaRd8) as ipaRd8,sum(t.ipaRd9) as ipaRd9,sum(t.ipaRd10) as ipaRd10,sum(t.ipaRd11) as ipaRd11,sum(t.ipaRd12) as ipaRd12,sum(t.ipaRd13) as ipaRd13,
			sum(t.ipaRd15) as ipaRd15,sum(t.ipaRd16) as ipaRd16,sum(t.ipaRd17) as ipaRd17,sum(t.ipaRd18) as ipaRd18,sum(t.ipaRd19) as ipaRd19,
			sum(t.ipaRd20) as ipaRd20,sum(t.ipaRd21) as ipaRd21,sum(t.ipaRd22) as ipaRd22,sum(t.ipaRd23) as ipaRd23,sum(t.ipaRd24) as ipaRd24,sum(t.ipaRd25) as ipaRd25,
			sum(t.ipaRd26) as ipaRd26,sum(t.ipaRd27) as ipaRd27,sum(t.ipaRd28) as ipaRd28,sum(t.ipaRd29) as ipaRd29,sum(t.ipaRd30) as ipaRd30,sum(t.ipaRd60) as ipaRd60,
			sum(t.ipaRd36) as ipaRd36,sum(t.ipaRd42) as ipaRd42,sum(t.ipaRd48) as ipaRd48,sum(t.ipaRd54) as ipaRd54,sum(t.ipaRd60) as ipaRd60,
			sum(t.ipaRd75) as ipaRd75,sum(t.ipaRd90) as ipaRd90,sum(t.ipaRd120) as ipaRd120,
			sum(t.ipaRd105) as ipaRd105,sum(t.ipaRd135) as ipaRd135,sum(t.ipaRd150) as ipaRd150,sum(t.ipaRd165) as ipaRd165,sum(t.ipaRd180) as ipaRd180,sum(t.ipaRd195) as ipaRd195,
			sum(t.ipaRd210) as ipaRd210,sum(t.ipaRd225) as ipaRd225,sum(t.ipaRd240) as ipaRd240,sum(t.ipaRd255) as ipaRd255,sum(t.ipaRd270) as ipaRd270,sum(t.ipaRd285) as ipaRd285,
			sum(t.ipaRd300) as ipaRd300,sum(t.ipaRd315) as ipaRd315,sum(t.ipaRd330) as ipaRd330,sum(t.ipaRd345) as ipaRd345,sum(t.ipaRd360) as ipaRd360,
			
			sum(t.splashShowTimes) as splashShowTimes,sum(t.bannerShowTimes) as bannerShowTimes,sum(t.addIpa) addIpa,
			sum(t.infoFlowShowTimes) as infoFlowShowTimes,sum(t.plaqueShowTimes) as plaqueShowTimes,
			sum(t.videoShowTimes) as videoShowTimes,sum(t.asplashShowTimes) as asplashShowTimes,sum(t.abannerShowTimes) as abannerShowTimes, 
			sum(t.ainfoFlowShowTimes) as ainfoFlowShowTimes,sum(t.aplaqueShowTimes) as aplaqueShowTimes,sum(t.spend) as spend,t.accountType as accountType,
			sum(t.impressions) as impressions,sum(t.clicks) as clicks,sum(t.installs) as installs,sum(t.`convert`) as 'convert',sum(t.register) as register,
			sum(t.avideoShowTimes) as avideoShowTimes ,sum(t.addUserLtv0) as addUserLtv0,t.putUser as putUser ,t.strategy as strategy,t.company as company,
			sum(t.payCount) as payCount ,sum(t.payCount7) as payCount7,sum(t.gamePayCount) as gamePayCount, sum(t.payCountFirst7) as payCountFirst7,
			sum(t.addPurchaseUsers) as addPurchaseUsers,sum(t.activePurchaseUsers) as activePurchaseUsers,
			sum(t.videoLtv) as videoLtv,sum(t.plaqueLtv) AS plaqueLtv,sum(t.activeIpa) as activeIpa,sum(t.bid) bid,sum(t.gameAddiction) as gameAddiction from
		 (<include refid="wx_game_roi_sql"/>) t
	</select>
	<sql id="wx_game_roi_sql">
		SELECT 
		<choose>
			<when test='group.contains("week")'>
				DATE_FORMAT(tdate,'%Y-%u') day,
				DATE_FORMAT(tdate,'%Y-%u') week,
			</when>
			<when test='group.contains("month")'>
				DATE_FORMAT(tdate,'%Y-%m') day,
				DATE_FORMAT(tdate,'%Y-%m') month,
			</when>
			<otherwise>
			 	tdate as day,
			</otherwise>
		</choose>
		appid as appId ,ad_buy_media as media,ad_buy_channel as channel,account accountId,group_name as groupName,
				campaign_name as campaignName,campaign_id as campaignId,anchor_related_type,
				put_user as putUser,transfer_type as strategy,company ,sum(cost) as cost,
			sum(reg_user_cnt) as addUser,sum(active_user_cnt) as activeUser,sum(active_user_revenue) as totalIncome,sum(revenue_2) as ltv1,sum(revenue_3) as ltv2,
			sum(revenue_4) as ltv3,sum(revenue_5) as ltv4,sum(revenue_6) as ltv5,sum(revenue_7) as ltv6,sum(revenue_14) as ltv14,sum(revenue_30) as ltv30,
			sum(revenue_36) as ltv36,sum(revenue_42) as ltv42,sum(revenue_48) as ltv48,sum(revenue_54) as ltv54,sum(revenue_60) as ltv60,
			sum(revenue_8) as ltv8,sum(revenue_9) as ltv9,sum(revenue_10) as ltv10,sum(revenue_11) as ltv11,sum(revenue_12) as ltv12,sum(revenue_13) as ltv13,
			sum(revenue_15) as ltv15,sum(revenue_16) as ltv16,sum(revenue_17) as ltv17,sum(revenue_18) as ltv18,sum(revenue_19) as ltv19,
			sum(revenue_20) as ltv20,sum(revenue_21) as ltv21,sum(revenue_22) as ltv22,sum(revenue_23) as ltv23,sum(revenue_24) as ltv24,
			sum(revenue_25) as ltv25,sum(revenue_26) as ltv26,sum(revenue_27) as ltv27,sum(revenue_28) as ltv28,sum(revenue_29) as ltv29,
			sum(revenue_75) as ltv75,sum(revenue_90) as ltv90,sum(revenue_120) as ltv120,
			sum(revenue_105) as ltv105,sum(revenue_135) as ltv135,sum(revenue_150) as ltv150,sum(revenue_165) as ltv165,sum(revenue_180) as ltv180,sum(revenue_195) as ltv195,
			sum(revenue_210) as ltv210,sum(revenue_225) as ltv225,sum(revenue_240) as ltv240,sum(revenue_255) as ltv255,sum(revenue_270) as ltv270,sum(revenue_285) as ltv285,
			sum(revenue_300) as ltv300,sum(revenue_315) as ltv315,sum(revenue_330) as ltv330,sum(revenue_345) as ltv345,sum(revenue_360) as ltv360,
			
			sum(retention_1day_user_cnt) as rdOne,sum(retention_2day_user_cnt) as rdTwo,sum(retention_3day_user_cnt) as rdThree,sum(retention_4day_user_cnt) as rdFour,
			sum(retention_5day_user_cnt) as rdFive,sum(retention_6day_user_cnt) as rdSix,sum(retention_7day_user_cnt) as rdSeven,sum(retention_14day_user_cnt) as rdFourteen,
			sum(retention_30day_user_cnt) as rdThrity,sum(retention_8day_user_cnt) as rd8,sum(retention_9day_user_cnt) as rd9,sum(retention_10day_user_cnt) as rd10,
			sum(retention_11day_user_cnt) as rd11,sum(retention_12day_user_cnt) as rd12,sum(retention_13day_user_cnt) as rd13,sum(retention_15day_user_cnt) as rd15,
			sum(retention_16day_user_cnt) as rd16,sum(retention_17day_user_cnt) as rd17,sum(retention_18day_user_cnt) as rd18,sum(retention_19day_user_cnt) as rd19,
			sum(retention_20day_user_cnt) as rd20,sum(retention_21day_user_cnt) as rd21,sum(retention_22day_user_cnt) as rd22,sum(retention_23day_user_cnt) as rd23,
			sum(retention_24day_user_cnt) as rd24,sum(retention_25day_user_cnt) as rd25,sum(retention_26day_user_cnt) as rd26,
			sum(retention_27day_user_cnt) as rd27,sum(retention_28day_user_cnt) as rd28,sum(retention_29day_user_cnt) as rd29,
			sum(retention_75day_user_cnt) as rd75,sum(retention_90day_user_cnt) as rd90,sum(retention_120day_user_cnt) as rd120,
			
			sum(retention_105day_user_cnt) as rd105,sum(retention_135day_user_cnt) as rd135,sum(retention_150day_user_cnt) as rd150,
			sum(retention_165day_user_cnt) as rd165,sum(retention_180day_user_cnt) as rd180,sum(retention_195day_user_cnt) as rd195,
			sum(retention_210day_user_cnt) as rd210,sum(retention_225day_user_cnt) as rd225,sum(retention_240day_user_cnt) as rd240,
			sum(retention_255day_user_cnt) as rd255,sum(retention_270day_user_cnt) as rd270,sum(retention_285day_user_cnt) as rd285,
			sum(retention_300day_user_cnt) as rd300,sum(retention_315day_user_cnt) as rd315,sum(retention_330day_user_cnt) as rd330,
			sum(retention_345day_user_cnt) as rd345,sum(retention_360day_user_cnt) as rd360,
			
			sum(active_user_splash_cnt) as splashShowTimes,sum(active_user_banner_cnt) as bannerShowTimes,sum(reg_user_iap_revenue) addIpa,
			sum(iap_revenue_2) as ipaLtv2,sum(iap_revenue_3) as ipaLtv3,sum(iap_revenue_4) as ipaLtv4,sum(iap_revenue_5) as ipaLtv5,
			sum(iap_revenue_6) as ipaLtv6,sum(iap_revenue_7) as ipaLtv7,sum(iap_revenue_14) as ipaLtv14,sum(iap_revenue_30) as ipaLtv30,
			sum(iap_revenue_8) as ipaLtv8,sum(iap_revenue_9) as ipaLtv9,sum(iap_revenue_10) as ipaLtv10,sum(iap_revenue_11) as ipaLtv11,
			sum(iap_revenue_12) as ipaLtv12,sum(iap_revenue_13) as ipaLtv13,sum(iap_revenue_15) as ipaLtv15,sum(iap_revenue_16) as ipaLtv16,
			sum(iap_revenue_17) as ipaLtv17,sum(iap_revenue_18) as ipaLtv18,sum(iap_revenue_19) as ipaLtv19,sum(iap_revenue_20) as ipaLtv20,
			sum(iap_revenue_21) as ipaLtv21,sum(iap_revenue_22) as ipaLtv22,sum(iap_revenue_23) as ipaLtv23,sum(iap_revenue_24) as ipaLtv24,
			sum(iap_revenue_25) as ipaLtv25,sum(iap_revenue_26) as ipaLtv26,sum(iap_revenue_27) as ipaLtv27,sum(iap_revenue_28) as ipaLtv28,
			sum(iap_revenue_29) as ipaLtv29,sum(iap_revenue_36) as ipaLtv36,sum(iap_revenue_42) as ipaLtv42,sum(iap_revenue_48) as ipaLtv48,sum(iap_revenue_54) as ipaLtv54,
			sum(iap_revenue_60) as ipaLtv60,sum(iap_revenue_75) as ipaLtv75,sum(iap_revenue_90) as ipaLtv90,sum(iap_revenue_120) as ipaLtv120,
			sum(iap_revenue_105) as ipaLtv105,sum(iap_revenue_135) as ipaLtv135,sum(iap_revenue_150) as ipaLtv150,
			sum(iap_revenue_165) as ipaLtv165,sum(iap_revenue_180) as ipaLtv180,sum(iap_revenue_195) as ipaLtv195,
			sum(iap_revenue_210) as ipaLtv210,sum(iap_revenue_225) as ipaLtv225,sum(iap_revenue_240) as ipaLtv240,
			sum(iap_revenue_255) as ipaLtv255,sum(iap_revenue_270) as ipaLtv270,sum(iap_revenue_285) as ipaLtv285,
			sum(iap_revenue_300) as ipaLtv300,sum(iap_revenue_315) as ipaLtv315,sum(iap_revenue_330) as ipaLtv330,
			sum(iap_revenue_345) as ipaLtv345,sum(iap_revenue_360) as ipaLtv360,
			
			sum(active_user_msg_cnt) as infoFlowShowTimes,sum(active_user_plaque_cnt) as plaqueShowTimes,
			sum(active_user_video_cnt) as videoShowTimes,sum(reg_user_splash_cnt) as asplashShowTimes,sum(reg_user_banner_cnt) as abannerShowTimes, 
			sum(reg_user_msg_cnt) as ainfoFlowShowTimes,sum(reg_user_plaque_cnt) as aplaqueShowTimes,sum(rebate_cost) as spend,account_type as accountType,
			sum(impressions) as impressions,sum(clicks) as clicks,sum(installs) as installs,sum(`convert`) as 'convert',sum(register) as register,sum(game_addiction) as gameAddiction,
			sum(reg_user_video_cnt) as avideoShowTimes ,sum(revenue_1) as addUserLtv0,
			sum(pay_count) as payCount ,sum(pay_count_7) as payCount7,sum(game_pay_count) as gamePayCount, sum(pay_count_first_7) as payCountFirst7,
			sum(iap_retention_1day_user_cnt) as ipaRd1,sum(iap_retention_2day_user_cnt) as ipaRd2,sum(iap_retention_3day_user_cnt) as ipaRd3,sum(iap_retention_4day_user_cnt) as ipaRd4,
			sum(iap_retention_5day_user_cnt) as ipaRd5,sum(iap_retention_6day_user_cnt) as ipaRd6,sum(iap_retention_7day_user_cnt) as ipaRd7,
			sum(iap_retention_8day_user_cnt) ipaRd8,sum(iap_retention_9day_user_cnt) ipaRd9,sum(iap_retention_10day_user_cnt) ipaRd10,sum(iap_retention_11day_user_cnt) ipaRd11,
			sum(iap_retention_12day_user_cnt) ipaRd12,sum(iap_retention_13day_user_cnt) ipaRd13,sum(iap_retention_14day_user_cnt) ipaRd14,sum(iap_retention_15day_user_cnt) ipaRd15,
			sum(iap_retention_16day_user_cnt) ipaRd16,sum(iap_retention_17day_user_cnt) ipaRd17,sum(iap_retention_18day_user_cnt) ipaRd18,sum(iap_retention_19day_user_cnt) ipaRd19,
			sum(iap_retention_20day_user_cnt) ipaRd20,sum(iap_retention_21day_user_cnt) ipaRd21,sum(iap_retention_22day_user_cnt) ipaRd22,sum(iap_retention_23day_user_cnt) ipaRd23,
			sum(iap_retention_24day_user_cnt) ipaRd24,sum(iap_retention_25day_user_cnt) ipaRd25,sum(iap_retention_26day_user_cnt) ipaRd26,sum(iap_retention_27day_user_cnt) ipaRd27,
			sum(iap_retention_28day_user_cnt) ipaRd28,sum(iap_retention_29day_user_cnt) ipaRd29,sum(iap_retention_30day_user_cnt) as ipaRd30,
			sum(iap_retention_36day_user_cnt) as ipaRd36,sum(iap_retention_42day_user_cnt) as ipaRd42,sum(iap_retention_48day_user_cnt) as ipaRd48,
			sum(iap_retention_54day_user_cnt) as ipaRd54,sum(iap_retention_60day_user_cnt) as ipaRd60,sum(iap_retention_75day_user_cnt) as ipaRd75,
			sum(iap_retention_90day_user_cnt) as ipaRd90,sum(iap_retention_120day_user_cnt) as ipaRd120,
			sum(iap_retention_105day_user_cnt) as ipaRd105,sum(iap_retention_135day_user_cnt) as ipaRd135,sum(iap_retention_150day_user_cnt) as ipaRd150,
			sum(iap_retention_165day_user_cnt) as ipaRd165,sum(iap_retention_180day_user_cnt) as ipaRd180,sum(iap_retention_195day_user_cnt) as ipaRd195,
			sum(iap_retention_210day_user_cnt) as ipaRd210,sum(iap_retention_225day_user_cnt) as ipaRd225,sum(iap_retention_240day_user_cnt) as ipaRd240,
			sum(iap_retention_255day_user_cnt) as ipaRd255,sum(iap_retention_270day_user_cnt) as ipaRd270,sum(iap_retention_285day_user_cnt) as ipaRd285,
			sum(iap_retention_300day_user_cnt) as ipaRd300,sum(iap_retention_315day_user_cnt) as ipaRd315,sum(iap_retention_330day_user_cnt) as ipaRd330,
			sum(iap_retention_345day_user_cnt) as ipaRd345,sum(iap_retention_360day_user_cnt) as ipaRd360,
			
			sum(iap_reg_user_cnt) as addPurchaseUsers,sum(iap_active_user_cnt) as activePurchaseUsers,
			sum(reg_user_video_revenue) as videoLtv,sum(reg_user_plaque_revenue) AS plaqueLtv,sum(active_user_iap_revenue) as activeIpa,sum(bid) bid ,
			sum(retention_36day_user_cnt) as rdThritySix ,sum(retention_42day_user_cnt) as rdFortyTwo,sum(retention_48day_user_cnt) as rdFortyEight, sum(retention_54day_user_cnt) as rdFiftyFour, 
			sum(retention_60day_user_cnt) as rdSixty from ads_wechat_game_roi
		where tdate BETWEEN #{beginDate} AND #{endDate} 
		<if test="appId != null and appId != ''">
			and appid in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and ad_buy_media in (${media}) 
		</if>
		<if test="channel != null and channel != ''">
			and ad_buy_channel in (${channel}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account in (${accountId}) 
		</if>
		<if test="putUser != null and putUser != ''">
			and put_user in (${putUser}) 
		</if>
		<if test="campaignId != null and campaignId != ''">
			and campaign_id in (${campaignId}) 
		</if>
		<if test="campaignName != null and campaignName != ''">
			and campaign_name = #{campaignName} 
		</if>
		<if test="groupName != null and groupName != ''">
			and group_name = #{groupName} 
		</if>
		<if test="company != null and company != ''">
			and company in (${company}) 
		</if>
		<if test="adsensePosition != null and adsensePosition != ''">
			and adsensePosition in (${adsensePosition}) 
		</if>
		<if test="strategy != null and strategy != ''">
			and transfer_type in (${strategy}) 
		</if>
		<if test="accountType != null and accountType != ''">
			and account_type in (${accountType}) 
		</if>
		<if test="anchor_related_type != null and anchor_related_type != ''">
			and anchor_related_type = #{anchor_related_type} 
		</if>
		group by appid
		<if test="group != null and group != ''">
			,${group}
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by day asc,appId asc ,spend desc
			</otherwise>
		</choose>
	</sql>
	
	<!-- 广告ROI报表列表查询  -->
  	<select id="adRoiReport" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam" resultType="com.wbgame.pojo.jettison.AdRoiReportDTO2">
		<include refid="ad_roi_sql"/> 
		group by app_id
		<if test="group != null and group != ''">
			,${group}
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				<choose>
					<when test='customizes.contains("spend") and group.contains("day")'>
						order by day asc ,spend desc
					</when>
					<when test='customizes.contains("spend") '>
						order by spend desc
					</when>
					<when test='group.contains("day") '>
						order by day asc
					</when>
				</choose>
			</otherwise>
		</choose>
	</select>
	<!-- 广告ROI报表汇总  -->
	<select id="adRoiReportTotal" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam"  resultType="com.wbgame.pojo.jettison.AdRoiReportDTO2">
			<include refid="ad_roi_sql"/>
	</select>
	<sql id="ad_roi_sql">
		SELECT app_id appId,
		<if test='group.contains("day")'>
			day,
		</if>
		<if test='group.contains("week")'>
			DATE_FORMAT(day,'%Y-%u') day,
			DATE_FORMAT(day,'%Y-%u') week,
		</if>
		<if test='group.contains("month")'>
			DATE_FORMAT(day,'%Y-%m') day,
			DATE_FORMAT(day,'%Y-%m') month,
		</if>
		<if test='group.contains("media")'>
			IFNULL(media,'自然量') media,
		</if>
		<if test='group.contains("channel")'>
			IFNULL(channel,'自然量') channel,
		</if>
		<if test='group.contains("accountId")'>
			account accountId,
		</if>
		<if test='group.contains("putUser")'>
			putUser putUser,
		</if>
		<if test='group.contains("groupName")'>
			group_name groupName,
		</if>
		<if test='group.contains("campaignName")'>
			campaign_name campaignName,
		</if>
		<if test='group.contains("campaignId")'>
			campaign_id campaignId,
		</if>
		<if test='group.contains("accountType")'>
			account_type accountType,
		</if>
		<if test='group.contains("strategy")'>
			transfer_type strategy,
		</if>
		<if test='group.contains("company")'>
			company company,
		</if>
		<if test='group.contains("adsensePosition")'>
			adsensePosition adsensePosition,
		</if>
		<if test='customizes.contains("addUser")'>
			sum(add_user) addUser,
		</if>
		<if test='customizes.contains("activeUser")'>
			sum(active_user) activeUser,
		</if>
		<if test='customizes.contains("spend")'>
			sum(rebateCost)/100 spend,
		</if>
		<if test='customizes.contains("dupAddUser")'>
			sum(dup_add_user) as dupAddUser,
		</if>
		<if test='customizes.contains("cpa")'>
			round(sum(rebateCost)/100/sum(add_user),2) cpa,
		</if>
		<if test='customizes.contains("avgVideo")'>
			round(sum(add_user_pv_video)/sum(add_user),2) avgVideo,
		</if>
		<if test='customizes.contains("avgPlaque")'>
			round(sum(add_user_pv_plaque)/sum(add_user),2) avgPlaque,
		</if>
		<if test='customizes.contains("activeAvgVideo")'>
			round(sum(active_user_pv_video)/sum(active_user),2) activeAvgVideo,
		</if>
		<if test='customizes.contains("activeAvgPlaque")'>
			round(sum(active_user_pv_plaque)/sum(active_user),2) activeAvgPlaque,
		</if>
		<if test='customizes.contains("videoEcpm")'>
			round(sum(add_user_revenue_video)/sum(add_user_pv_video)*10,2) videoEcpm,
		</if>
		<if test='customizes.contains("plaqueEcpm")'>
			round(sum(add_user_revenue_plaque)/sum(add_user_pv_plaque)*10,2) plaqueEcpm,
		</if>
		<if test='customizes.contains("firstPayCount")'>
			sum(payCount) firstPayCount,
		</if>
		<if test='customizes.contains("paymentTimes")'>
			sum(gamePayCount) paymentTimes,
		</if>
		<if test='customizes.contains("firstChargeCost")'>
			FORMAT(sum(rebateCost)/100/sum(payCount),2) firstChargeCost,
		</if>
		<if test='customizes.contains("firstPaymentRate")'>
			CONCAT(IFNULL(FORMAT(sum(payCount)/sum(installs)*100,2),0.00),'%') firstPaymentRate,
		</if>
		<if test='customizes.contains("paymentTimes7")'>
			sum(payCount7) paymentTimes7,
		</if>
		<if test='customizes.contains("avgPayCount7")'>
			round(sum(payCount7)/sum(payCountFirst7),2) avgPayCount7,
		</if>
		<if test='customizes.contains("payCost")'>
			FORMAT(sum(rebateCost)/100/sum(gamePayCount),2)  payCost,
		</if>
		<if test='customizes.contains("addPurchaseUsers")'>
			sum(add_purchase_users) addPurchaseUsers,
		</if>
		<if test='customizes.contains("activePurchaseUsers")'>
			sum(active_purchase_users) activePurchaseUsers,
		</if>
		<if test='customizes.contains("addUserPayCost")'>
			round(sum(rebateCost)/100/sum(add_purchase_users),2) addUserPayCost,
		</if>
		<if test='customizes.contains("activePurchaseUsers")'>
			round(sum(add_purchase_revenue_1)/100/sum(add_purchase_users),2) addUserPayArpu,
		</if>
		<if test='customizes.contains("addUserPayRate")'>
			CONCAT(IFNULL(FORMAT(sum(add_purchase_users)/sum(add_user)*100,2),0.00),'%') addUserPayRate,
		</if>
		<if test='customizes.contains("payRate")'>
			CONCAT(IFNULL(FORMAT(sum(active_purchase_users)/sum(active_user)*100,2),0.00),'%') payRate,
		</if>
		<if test='customizes.contains("payArpu")'>
			round(sum(active_purchase_revenue)/100/sum(active_purchase_users),2) payArpu,
		</if>
		<if test='customizes.contains("convertSpend")'>
			FORMAT(sum(rebateCost)/100/sum(`convert`),2) convertSpend,
		</if>
		<if test='customizes.contains("registerSpend")'>
			FORMAT(sum(rebateCost)/100/sum(register),2) registerSpend,
		</if>
		<if test='customizes.contains("addictionSpend")'>
			FORMAT(sum(rebateCost)/100/sum(game_addiction),2) addictionSpend,
		</if>
		<if test='customizes.contains("avgShowSpend")'>
			FORMAT(sum(rebateCost)/sum(impressions)*10,2) avgShowSpend,
		</if>
		<if test='customizes.contains("convertRate")'>
			CONCAT(IFNULL(FORMAT(sum(`convert`)/sum(clicks)*100,2),0.00),'%') convertRate,
		</if>
		<if test='customizes.contains("installRate")'>
			CONCAT(IFNULL(FORMAT(sum(installs)/sum(clicks)*100,2),0.00),'%') installRate,
		</if>
		<if test='customizes.contains("registerRate")'>
			CONCAT(IFNULL(FORMAT(sum(register)/sum(installs)*100,2),0.00),'%') registerRate,
		</if>
		<if test='customizes.contains("addictionRate")'>
			CONCAT(IFNULL(FORMAT(sum(game_addiction)/sum(installs)*100,2),0.00),'%') addictionRate,
		</if>
		<if test='customizes.contains("clickRate")'>
			CONCAT(IFNULL(FORMAT(sum(clicks)/sum(impressions)*100,2),0.00),'%') clickRate,
		</if>
		<if test='customizes.contains("gameAddiction")'>
			sum(game_addiction) as gameAddiction,
		</if>
		<if test='customizes.contains("rdOneRate")'>
			<choose>
				<when test="diffdate &gt;=1 or group.contains('day')">
				CONCAT(IFNULL(FORMAT(sum(retention_day_1)/sum(add_user)*100,2),0.00),'%') rdOneRate,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_1)/sum(add_user)*100,2),0.00),'%*') rdOneRate,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdTwo")'>
			<choose>
				<when test=" diffdate &gt;=2 or group.contains('day') ">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_2)/sum(add_user)*100,2),0.00),'%') rdTwo,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_2)/sum(add_user)*100,2),0.00),'%*') rdTwo,
				</otherwise>
			</choose>		
		</if>
		<if test='customizes.contains("rdThree")'>
			<choose>
				<when test=" diffdate &gt;=3 or group.contains('day') ">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_3)/sum(add_user)*100,2),0.00),'%') rdThree,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_3)/sum(add_user)*100,2),0.00),'%*') rdThree,
				</otherwise>
			</choose>		
		</if>
		<if test='customizes.contains("rdFour")'>
			<choose>
				<when test=" diffdate &gt;=4 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_4)/sum(add_user)*100,2),0.00),'%') rdFour,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_4)/sum(add_user)*100,2),0.00),'%*') rdFour,
				</otherwise>
			</choose>	
		</if>
		<if test='customizes.contains("rdFive")'>
			<choose>
				<when test=" diffdate &gt;=5 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_5)/sum(add_user)*100,2),0.00),'%') rdFive,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_5)/sum(add_user)*100,2),0.00),'%*') rdFive,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdSix")'>
			<choose>
				<when test=" diffdate &gt;=6 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_6)/sum(add_user)*100,2),0.00),'%') rdSix,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_6)/sum(add_user)*100,2),0.00),'%*') rdSix,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdSeven")'>
			<choose>
				<when test=" diffdate &gt;=7 or group.contains('day')">
			     CONCAT(IFNULL(FORMAT(sum(retention_day_7)/sum(add_user)*100,2),0.00),'%') rdSeven,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_7)/sum(add_user)*100,2),0.00),'%*') rdSeven,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdFourteen")'>
			<choose>
				<when test=" diffdate &gt;=14 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_14)/sum(add_user)*100,2),0.00),'%') rdFourteen,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_14)/sum(add_user)*100,2),0.00),'%*') rdFourteen,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdThrity")'>
			<choose>
				<when test=" diffdate &gt;=30 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_30)/sum(add_user)*100,2),0.00),'%') rdThrity,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_30)/sum(add_user)*100,2),0.00),'%*') rdThrity,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdThritySix")'>
			<choose>
				<when test=" diffdate &gt;=36 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_36)/sum(add_user)*100,2),0.00),'%') rdThritySix,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_36)/sum(add_user)*100,2),0.00),'%*') rdThritySix,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdFortyTwo")'>
			<choose>
				<when test=" diffdate &gt;=42 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_42)/sum(add_user)*100,2),0.00),'%') rdFortyTwo,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_42)/sum(add_user)*100,2),0.00),'%*') rdFortyTwo,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdFortyEight")'>
			<choose>
				<when test=" diffdate &gt;=48 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_48)/sum(add_user)*100,2),0.00),'%') rdFortyEight,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_48)/sum(add_user)*100,2),0.00),'%*') rdFortyEight,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdFiftyFour")'>
			<choose>
				<when test=" diffdate &gt;=54 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_54)/sum(add_user)*100,2),0.00),'%') rdFiftyFour,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_54)/sum(add_user)*100,2),0.00),'%*') rdFiftyFour,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdSixty")'>
			<choose>
				<when test=" diffdate &gt;=60 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_60)/sum(add_user)*100,2),0.00),'%') rdSixty,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_60)/sum(add_user)*100,2),0.00),'%*') rdSixty,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd8")'>
			<choose>
				<when test=" diffdate &gt;=8 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_8)/sum(add_user)*100,2),0.00),'%') rd8,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_day_8)/sum(add_user)*100,2),0.00),'%*') rd8,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd9")'>
			<choose>
				<when test=" diffdate &gt;=9 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_9)/sum(add_user)*100,2),0.00),'%') rd9,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_day_9)/sum(add_user)*100,2),0.00),'%*') rd9,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd10")'>
			<choose>
				<when test=" diffdate &gt;=10 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_10)/sum(add_user)*100,2),0.00),'%') rd10,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_10)/sum(add_user)*100,2),0.00),'%*') rd10,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd11")'>
			<choose>
				<when test=" diffdate &gt;=11 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_11)/sum(add_user)*100,2),0.00),'%') rd11,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_11)/sum(add_user)*100,2),0.00),'%*') rd11,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd12")'>
			<choose>
				<when test=" diffdate &gt;=12 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_12)/sum(add_user)*100,2),0.00),'%') rd12,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_12)/sum(add_user)*100,2),0.00),'%*') rd12,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd13")'>
			<choose>
				<when test=" diffdate &gt;=13 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_13)/sum(add_user)*100,2),0.00),'%') rd13,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_day_13)/sum(add_user)*100,2),0.00),'%*') rd13,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd15")'>
			<choose>
				<when test=" diffdate &gt;=15 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_15)/sum(add_user)*100,2),0.00),'%') rd15,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_day_15)/sum(add_user)*100,2),0.00),'%*') rd15,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd16")'>
			<choose>
				<when test=" diffdate &gt;=16 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_16)/sum(add_user)*100,2),0.00),'%') rd16,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_16)/sum(add_user)*100,2),0.00),'%*') rd16,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd17")'>
			<choose>
				<when test=" diffdate &gt;=17 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_17)/sum(add_user)*100,2),0.00),'%') rd17,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_17)/sum(add_user)*100,2),0.00),'%*') rd17,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd18")'>
			<choose>
				<when test=" diffdate &gt;=18 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_18)/sum(add_user)*100,2),0.00),'%') rd18,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_18)/sum(add_user)*100,2),0.00),'%*') rd18,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd19")'>
			<choose>
				<when test=" diffdate &gt;=19 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_19)/sum(add_user)*100,2),0.00),'%') rd19,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_19)/sum(add_user)*100,2),0.00),'%*') rd19,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd20")'>
			<choose>
				<when test=" diffdate &gt;=20 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_20)/sum(add_user)*100,2),0.00),'%') rd20,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_20)/sum(add_user)*100,2),0.00),'%*') rd20,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd21")'>
			<choose>
				<when test=" diffdate &gt;=21 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_21)/sum(add_user)*100,2),0.00),'%') rd21,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_21)/sum(add_user)*100,2),0.00),'%*') rd21,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd22")'>
			<choose>
				<when test=" diffdate &gt;=22 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_22)/sum(add_user)*100,2),0.00),'%') rd22,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_22)/sum(add_user)*100,2),0.00),'%*') rd22,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd23")'>
			<choose>
				<when test=" diffdate &gt;=23 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_23)/sum(add_user)*100,2),0.00),'%') rd23,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_23)/sum(add_user)*100,2),0.00),'%*') rd23,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd24")'>
			<choose>
				<when test=" diffdate &gt;=24 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_24)/sum(add_user)*100,2),0.00),'%') rd24,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_24)/sum(add_user)*100,2),0.00),'%*') rd24,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd25")'>
			<choose>
				<when test=" diffdate &gt;=25 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_25)/sum(add_user)*100,2),0.00),'%') rd25,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_25)/sum(add_user)*100,2),0.00),'%*') rd25,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd26")'>
			<choose>
				<when test=" diffdate &gt;=26 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_26)/sum(add_user)*100,2),0.00),'%') rd26,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_26)/sum(add_user)*100,2),0.00),'%*') rd26,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd27")'>
			<choose>
				<when test=" diffdate &gt;=27 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_27)/sum(add_user)*100,2),0.00),'%') rd27,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_day_27)/sum(add_user)*100,2),0.00),'%*') rd27,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd28")'>
			<choose>
				<when test=" diffdate &gt;=28 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_28)/sum(add_user)*100,2),0.00),'%') rd28,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_28)/sum(add_user)*100,2),0.00),'%*') rd28,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd29")'>
			<choose>
				<when test=" diffdate &gt;=29 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_29)/sum(add_user)*100,2),0.00),'%') rd29,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_29)/sum(add_user)*100,2),0.00),'%*') rd29,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd75")'>
			<choose>
				<when test=" diffdate &gt;=75 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_75)/sum(add_user)*100,2),0.00),'%') rd75,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_day_75)/sum(add_user)*100,2),0.00),'%*') rd75,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd90")'>
			<choose>
				<when test=" diffdate &gt;=90 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_90)/sum(add_user)*100,2),0.00),'%') rd90,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_90)/sum(add_user)*100,2),0.00),'%*') rd90,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd105")'>
			<choose>
				<when test=" diffdate &gt;=105 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_105)/sum(add_user)*100,2),0.00),'%') rd105,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_105)/sum(add_user)*100,2),0.00),'%*') rd105,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd120")'>
			<choose>
				<when test=" diffdate &gt;=120 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_120)/sum(add_user)*100,2),0.00),'%') rd120,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_120)/sum(add_user)*100,2),0.00),'%*') rd120,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd135")'>
			<choose>
				<when test=" diffdate &gt;=135 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_135)/sum(add_user)*100,2),0.00),'%') rd135,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_135)/sum(add_user)*100,2),0.00),'%*') rd135,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd150")'>
			<choose>
				<when test=" diffdate &gt;=150 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_150)/sum(add_user)*100,2),0.00),'%') rd150,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_150)/sum(add_user)*100,2),0.00),'%*') rd150,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd165")'>
			<choose>
				<when test=" diffdate &gt;=165 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_165)/sum(add_user)*100,2),0.00),'%') rd165,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_165)/sum(add_user)*100,2),0.00),'%*') rd165,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd180")'>
			<choose>
				<when test=" diffdate &gt;=180 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_180)/sum(add_user)*100,2),0.00),'%') rd180,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_180)/sum(add_user)*100,2),0.00),'%*') rd180,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd195")'>
			<choose>
				<when test=" diffdate &gt;=195 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_195)/sum(add_user)*100,2),0.00),'%') rd195,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_195)/sum(add_user)*100,2),0.00),'%*') rd195,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd210")'>
			<choose>
				<when test=" diffdate &gt;=210 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_210)/sum(add_user)*100,2),0.00),'%') rd210,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_210)/sum(add_user)*100,2),0.00),'%*') rd210,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd225")'>
			<choose>
				<when test=" diffdate &gt;=225 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_225)/sum(add_user)*100,2),0.00),'%') rd225,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_225)/sum(add_user)*100,2),0.00),'%*') rd225,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd240")'>
			<choose>
				<when test=" diffdate &gt;=240 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_240)/sum(add_user)*100,2),0.00),'%') rd240,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_240)/sum(add_user)*100,2),0.00),'%*') rd240,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd255")'>
			<choose>
				<when test=" diffdate &gt;=255 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_255)/sum(add_user)*100,2),0.00),'%') rd255,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_255)/sum(add_user)*100,2),0.00),'%*') rd255,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd270")'>
			<choose>
				<when test=" diffdate &gt;=270 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_270)/sum(add_user)*100,2),0.00),'%') rd270,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_270)/sum(add_user)*100,2),0.00),'%*') rd270,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd285")'>
			<choose>
				<when test=" diffdate &gt;=285 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_285)/sum(add_user)*100,2),0.00),'%') rd285,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_285)/sum(add_user)*100,2),0.00),'%*') rd285,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd300")'>
			<choose>
				<when test=" diffdate &gt;=300 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_300)/sum(add_user)*100,2),0.00),'%') rd300,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_300)/sum(add_user)*100,2),0.00),'%*') rd300,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd315")'>
			<choose>
				<when test=" diffdate &gt;=315 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_315)/sum(add_user)*100,2),0.00),'%') rd315,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_315)/sum(add_user)*100,2),0.00),'%*') rd315,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd330")'>
			<choose>
				<when test=" diffdate &gt;=330 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_330)/sum(add_user)*100,2),0.00),'%') rd330,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_330)/sum(add_user)*100,2),0.00),'%*') rd330,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd345")'>
			<choose>
				<when test=" diffdate &gt;=345 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_345)/sum(add_user)*100,2),0.00),'%') rd345,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_345)/sum(add_user)*100,2),0.00),'%*') rd345,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd360")'>
			<choose>
				<when test=" diffdate &gt;=360 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_360)/sum(add_user)*100,2),0.00),'%') rd360,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_360)/sum(add_user)*100,2),0.00),'%*') rd360,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd1")'>
			<choose>
				<when test=" diffdate &gt;=1 or group.contains('day')">
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_1)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd1,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_1)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd1,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd2")'>
			<choose>
				<when test=" diffdate &gt;=2 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_2)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd2,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_2)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd2,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd3")'>
			<choose>
				<when test=" diffdate &gt;=3 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_3)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd3,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_3)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd3,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd4")'>
			<choose>
				<when test=" diffdate &gt;=4 or group.contains('day')">
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_4)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd4,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_4)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd4,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd5")'>
			<choose>
				<when test=" diffdate &gt;=5 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_5)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd5,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_5)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd5,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd6")'>
			<choose>
				<when test=" diffdate &gt;=6 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_6)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd6,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_6)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd6,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd7")'>
			<choose>
				<when test=" diffdate &gt;=7 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_7)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd7,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_7)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd7,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd14")'>
			<choose>
				<when test=" diffdate &gt;=14 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_14)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd14,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_14)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd14,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd30")'>
			<choose>
				<when test=" diffdate &gt;=30 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_30)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd30,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_30)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd30,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd36")'>
			<choose>
				<when test=" diffdate &gt;=36 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_36)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd36,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_36)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd36,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd42")'>
			<choose>
				<when test=" diffdate &gt;=42 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_42)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd42,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_42)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd42,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd48")'>
			<choose>
				<when test=" diffdate &gt;=48 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_48)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd48,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_48)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd48,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd54")'>
			<choose>
				<when test=" diffdate &gt;=54 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_54)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd54,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_54)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd54,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd60")'>
			<choose>
				<when test=" diffdate &gt;=60 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_60)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd60,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_60)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd60,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd8")'>
			<choose>
				<when test=" diffdate &gt;=8 or group.contains('day')">
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_8)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd8,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_8)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd8,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd9")'>
			<choose>
				<when test=" diffdate &gt;=9 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_9)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd9,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_9)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd9,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd10")'>
			<choose>
				<when test=" diffdate &gt;=10 or group.contains('day')">
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_10)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd10,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_10)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd10,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd11")'>
			<choose>
				<when test=" diffdate &gt;=11 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_11)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd11,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_11)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd11,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd12")'>
			<choose>
				<when test=" diffdate &gt;=12 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_12)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd12,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_12)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd12,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd13")'>
			<choose>
				<when test=" diffdate &gt;=13 or group.contains('day')">
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_13)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd13,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_13)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd13,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd15")'>
			<choose>
				<when test=" diffdate &gt;=15 or group.contains('day')">
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_15)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd15,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_15)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd15,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd16")'>
			<choose>
				<when test=" diffdate &gt;=16 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_16)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd16,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_16)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd16,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd17")'>
			<choose>
				<when test=" diffdate &gt;=17 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_17)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd17,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_17)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd17,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd18")'>
			<choose>
				<when test=" diffdate &gt;=18 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_18)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd18,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_18)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd18,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd19")'>
			<choose>
				<when test=" diffdate &gt;=19 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_19)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd19,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_19)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd19,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd20")'>
			<choose>
				<when test=" diffdate &gt;=20 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_20)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd20,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_20)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd20,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd21")'>
			<choose>
				<when test=" diffdate &gt;=21 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_21)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd21,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_21)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd21,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd22")'>
			<choose>
				<when test=" diffdate &gt;=22 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_22)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd22,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_22)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd22,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd23")'>
			<choose>
				<when test=" diffdate &gt;=23 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_23)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd23,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_23)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd23,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd24")'>
			<choose>
				<when test=" diffdate &gt;=24 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_24)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd24,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_24)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd24,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd25")'>
			<choose>
				<when test=" diffdate &gt;=25 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_25)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd25,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_25)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd25,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd26")'>
			<choose>
				<when test=" diffdate &gt;=26 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_26)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd26,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_26)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd26,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd27")'>
			<choose>
				<when test=" diffdate &gt;=27 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_27)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd27,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_27)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd27,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd28")'>
			<choose>
				<when test=" diffdate &gt;=28 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_28)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd28,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_28)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd28,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd29")'>
			<choose>
				<when test=" diffdate &gt;=29 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_29)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd29,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_29)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd29,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd75")'>
			<choose>
				<when test=" diffdate &gt;=75 or group.contains('day')">
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_75)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd75,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_75)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd75,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd90")'>
			<choose>
				<when test=" diffdate &gt;=90 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_90)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd90,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_90)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd90,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd105")'>
			<choose>
				<when test=" diffdate &gt;=105 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_105)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd105,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_105)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd105,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd120")'>
			<choose>
				<when test=" diffdate &gt;=120 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_120)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd120,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_120)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd120,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd135")'>
			<choose>
				<when test=" diffdate &gt;=135 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_135)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd135,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_135)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd135,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd150")'>
			<choose>
				<when test=" diffdate &gt;=150 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_150)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd150,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_150)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd150,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd165")'>
			<choose>
				<when test=" diffdate &gt;=165 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_165)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd165,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_165)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd165,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd180")'>
			<choose>
				<when test=" diffdate &gt;=180 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_180)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd180,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_180)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd180,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd195")'>
			<choose>
				<when test=" diffdate &gt;=195 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_195)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd195,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_195)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd195,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd210")'>
			<choose>
				<when test=" diffdate &gt;=210 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_210)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd210,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_210)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd210,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd225")'>
			<choose>
				<when test=" diffdate &gt;=225 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_225)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd225,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_225)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd225,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd240")'>
			<choose>
				<when test=" diffdate &gt;=240 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_240)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd240,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_240)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd240,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd255")'>
			<choose>
				<when test=" diffdate &gt;=255 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_255)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd255,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_255)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd255,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd270")'>
			<choose>
				<when test=" diffdate &gt;=270 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_270)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd270,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_270)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd270,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd285")'>
			<choose>
				<when test=" diffdate &gt;=285 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_285)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd285,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_285)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd285,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd300")'>
			<choose>
				<when test=" diffdate &gt;=300 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_300)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd300,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_300)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd300,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd315")'>
			<choose>
				<when test=" diffdate &gt;=315 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_315)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd315,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_315)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd315,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd330")'>
			<choose>
				<when test=" diffdate &gt;=330 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_330)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd330,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_330)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd330,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd345")'>
			<choose>
				<when test=" diffdate &gt;=345 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_345)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd345,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_345)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd345,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd360")'>
			<choose>
				<when test=" diffdate &gt;=360 or group.contains('day')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_360)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd360,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_360)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd360,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaIncome")'>
			round(sum(add_purchase_revenue_1)/100,2)  ipaIncome,
		</if>
		<if test='customizes.contains("realizeIncome")'>
			round(sum(add_revenue_1)/100,2) realizeIncome,
		</if>
		<if test='customizes.contains("firstDayIncome")'>
			round(sum(add_purchase_revenue_1)/100+sum(add_revenue_1)/100,2) firstDayIncome,
		</if>
		<if test='customizes.contains("realizeRoi")'>
			CONCAT(IFNULL(FORMAT(sum(add_revenue_1)/sum(rebateCost)*100,2),0.00),'%') realizeRoi,
		</if>
		<if test='customizes.contains("ipaRoi")'>
			CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_1)/sum(rebateCost)*100,2),0.00),'%') ipaRoi,
		</if>
		<if test='customizes.contains("firstDayRoi")'>
			CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_1)+sum(add_revenue_1))/sum(rebateCost)*100,2),0.00),'%') firstDayRoi,
		</if>
		<if test='customizes.contains("adTotalRevenue")'>
			round(sum(active_revenue)/100,2)  adTotalRevenue,
		</if>
		<if test='customizes.contains("ipaTotalRevenue")'>
			round(sum(active_purchase_revenue)/100,2) ipaTotalRevenue,
		</if>
		<if test='customizes.contains("activeRoi")'>
			CONCAT(IFNULL(FORMAT((sum(active_purchase_revenue)+sum(active_revenue))/sum(rebateCost)*100,2),0.00),'%') activeRoi,
		</if>
		<if test='customizes.contains("activeIpaRoi")'>
			CONCAT(IFNULL(FORMAT(sum(active_purchase_revenue)/sum(rebateCost)*100,2),0.00),'%') activeIpaRoi,
		</if>
		<if test='customizes.contains("activeAdRoi")'>
			CONCAT(IFNULL(FORMAT(sum(active_revenue)/sum(rebateCost)*100,2),0.00),'%') activeAdRoi,
		</if>
		<if test='group.contains("campaign_id") or group.contains("campaignName")'>
			sum(rebateCost)/100/sum(`convert`)/sum(bid),
		</if>
		<choose>
            <when test="revenueSource ==1">
                <if test='customizes.contains("totalIncome")'>
					round(sum(active_revenue)/100,2) totalIncome,
				</if>
				<if test='customizes.contains("addArpu")'>
					round(sum(add_revenue_1)/100/sum(add_user),2) addArpu,
				</if>
				<if test='customizes.contains("dauArpu")'>
					round(sum(active_revenue)/100/sum(active_user),2) dauArpu,
				</if>
				<if test='customizes.contains("addUserLtv0")'>
					round(sum(add_revenue_1)/100,2) addUserLtv0,
				</if>
				<if test='customizes.contains("ltv1")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day')">
						FORMAT(sum(add_revenue_2)/100/sum(add_user),2) ltv1,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_2)/100/sum(add_user),2),'*') ltv1,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv2")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day')">
						FORMAT(sum(add_revenue_3)/100/sum(add_user),2) ltv2,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_3)/100/sum(add_user),2),'*') ltv2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv3")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day')">
						FORMAT(sum(add_revenue_4)/100/sum(add_user),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_4)/100/sum(add_user),2),'*') ltv3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv4")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day')">
						FORMAT(sum(add_revenue_5)/100/sum(add_user),2) ltv4,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_5)/100/sum(add_user),2),'*') ltv4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv5")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day')">
						FORMAT(sum(add_revenue_6)/100/sum(add_user),2) ltv5,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_6)/100/sum(add_user),2),'*') ltv5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv6")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day')">
						FORMAT(sum(add_revenue_7)/100/sum(add_user),2) ltv6,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_7)/100/sum(add_user),2) ltv6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day')">
						FORMAT(sum(add_revenue_8)/100/sum(add_user),2) ltv8,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_8)/100/sum(add_user),2),'*') ltv8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day')">
						FORMAT(sum(add_revenue_9)/100/sum(add_user),2) ltv9,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_9)/100/sum(add_user),2),'*')  ltv9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day')">
						FORMAT(sum(add_revenue_10)/100/sum(add_user),2) ltv10,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_10)/100/sum(add_user),2),'*') ltv10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day')">
						FORMAT(sum(add_revenue_11)/100/sum(add_user),2) ltv11,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_11)/100/sum(add_user),2),'*') ltv11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day')">
						FORMAT(sum(add_revenue_12)/100/sum(add_user),2) ltv12,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_12)/100/sum(add_user),2),'*') ltv12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day')">
						FORMAT(sum(add_revenue_13)/100/sum(add_user),2) ltv13,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_13)/100/sum(add_user),2),'*') ltv13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day')">
						FORMAT(sum(add_revenue_14)/100/sum(add_user),2) ltv14,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_14)/100/sum(add_user),2),'*') ltv14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day')">
						FORMAT(sum(add_revenue_15)/100/sum(add_user),2) ltv15,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_15)/100/sum(add_user),2),'*') ltv15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day')">
						FORMAT(sum(add_revenue_16)/100/sum(add_user),2) ltv16,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_16)/100/sum(add_user),2),'*') ltv16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day')">
						FORMAT(sum(add_revenue_17)/100/sum(add_user),2) ltv17,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_17)/100/sum(add_user),2),'*') ltv17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day')">
						FORMAT(sum(add_revenue_18)/100/sum(add_user),2) ltv18,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_18)/100/sum(add_user),2),'*') ltv18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day')">
						FORMAT(sum(add_revenue_19)/100/sum(add_user),2) ltv19,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_19)/100/sum(add_user),2),'*') ltv19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day')">
						FORMAT(sum(add_revenue_20)/100/sum(add_user),2) ltv20,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_20)/100/sum(add_user),2),'*') ltv20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day')">
						FORMAT(sum(add_revenue_21)/100/sum(add_user),2) ltv21,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_21)/100/sum(add_user),2),'*') ltv21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv22")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day')">
						FORMAT(sum(add_revenue_4)/100/sum(add_user),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_4)/100/sum(add_user),2),'*') ltv3,
						</otherwise>
					</choose>
					FORMAT(sum(add_revenue_22)/100/sum(add_user),2) ltv22,
				</if>
				<if test='customizes.contains("ltv23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day')">
						FORMAT(sum(add_revenue_23)/100/sum(add_user),2) ltv23,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_23)/100/sum(add_user),2),'*') ltv23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day')">
						FORMAT(sum(add_revenue_24)/100/sum(add_user),2) ltv24,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_24)/100/sum(add_user),2),'*') ltv24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day')">
						FORMAT(sum(add_revenue_25)/100/sum(add_user),2) ltv25,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_25)/100/sum(add_user),2),'*') ltv25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day')">
						FORMAT(sum(add_revenue_26)/100/sum(add_user),2) ltv26,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_26)/100/sum(add_user),2),'*') ltv26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day')">
						FORMAT(sum(add_revenue_27)/100/sum(add_user),2) ltv27,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_27)/100/sum(add_user),2),'*') ltv27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day')">
						FORMAT(sum(add_revenue_28)/100/sum(add_user),2) ltv28,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_28)/100/sum(add_user),2),'*') ltv28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day')">
						FORMAT(sum(add_revenue_29)/100/sum(add_user),2) ltv29,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_29)/100/sum(add_user),2),'*') ltv29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day')">
						FORMAT(sum(add_revenue_30)/100/sum(add_user),2) ltv30,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_30)/100/sum(add_user),2),'*') ltv30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day')">
						FORMAT(sum(add_revenue_36)/100/sum(add_user),2) ltv36,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_36)/100/sum(add_user),2),'*') ltv36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day')">
						FORMAT(sum(add_revenue_42)/100/sum(add_user),2) ltv42,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_42)/100/sum(add_user),2),'*') ltv42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day')">
						FORMAT(sum(add_revenue_48)/100/sum(add_user),2) ltv48,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_48)/100/sum(add_user),2),'*') ltv48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day')">
						FORMAT(sum(add_revenue_54)/100/sum(add_user),2) ltv54,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_54)/100/sum(add_user),2),'*') ltv54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day')">
						FORMAT(sum(add_revenue_60)/100/sum(add_user),2) ltv60,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_60)/100/sum(add_user),2),'*') ltv60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day')">
						FORMAT(sum(add_revenue_75)/100/sum(add_user),2) ltv75,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_75)/100/sum(add_user),2),'*') ltv75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day')">
						FORMAT(sum(add_revenue_90)/100/sum(add_user),2) ltv90,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_90)/100/sum(add_user),2),'*') ltv90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day')">
						FORMAT(sum(add_revenue_105)/100/sum(add_user),2) ltv105,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_105)/100/sum(add_user),2),'*') ltv105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day')">
						FORMAT(sum(add_revenue_120)/100/sum(add_user),2) ltv120,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_120)/100/sum(add_user),2),'*') ltv120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day')">
						FORMAT(sum(add_revenue_135)/100/sum(add_user),2) ltv135,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_135)/100/sum(add_user),2),'*') ltv135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day')">
						FORMAT(sum(add_revenue_150)/100/sum(add_user),2) ltv150,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_150)/100/sum(add_user),2),'*') ltv150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day')">
						FORMAT(sum(add_revenue_165)/100/sum(add_user),2) ltv165,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_165)/100/sum(add_user),2),'*') ltv165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day')">
						FORMAT(sum(add_revenue_180)/100/sum(add_user),2) ltv180,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_180)/100/sum(add_user),2),'*') ltv180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day')">
						FORMAT(sum(add_revenue_195)/100/sum(add_user),2) ltv195,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_195)/100/sum(add_user),2),'*') ltv195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day')">
						FORMAT(sum(add_revenue_210)/100/sum(add_user),2) ltv210,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_210)/100/sum(add_user),2),'*') ltv210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day')">
						FORMAT(sum(add_revenue_225)/100/sum(add_user),2) ltv225,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_225)/100/sum(add_user),2),'*') ltv225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day')">
						FORMAT(sum(add_revenue_240)/100/sum(add_user),2) ltv240,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_240)/100/sum(add_user),2),'*') ltv240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day')">
						FORMAT(sum(add_revenue_255)/100/sum(add_user),2) ltv255,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_255)/100/sum(add_user),2),'*') ltv255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day')">
						FORMAT(sum(add_revenue_270)/100/sum(add_user),2) ltv270,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_270)/100/sum(add_user),2),'*') ltv270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day')">
						FORMAT(sum(add_revenue_285)/100/sum(add_user),2) ltv285,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_285)/100/sum(add_user),2),'*') ltv285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day')">
						FORMAT(sum(add_revenue_300)/100/sum(add_user),2) ltv300,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_300)/100/sum(add_user),2),'*') ltv300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day')">
						FORMAT(sum(add_revenue_315)/100/sum(add_user),2) ltv315,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_315)/100/sum(add_user),2),'*') ltv315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day')">
						FORMAT(sum(add_revenue_330)/100/sum(add_user),2) ltv330,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_330)/100/sum(add_user),2),'*') ltv330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day')">
						FORMAT(sum(add_revenue_345)/100/sum(add_user),2) ltv345,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_345)/100/sum(add_user),2),'*') ltv345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day')">
						FORMAT(sum(add_revenue_360)/100/sum(add_user),2) ltv360,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_360)/100/sum(add_user),2),'*') ltv360,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("aRoi")'>
					CONCAT(IFNULL(FORMAT(sum(add_revenue_1)/sum(rebateCost)*100,2),0.00),'%') aRoi,
				</if>
				<if test='customizes.contains("roi2")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_2)/sum(rebateCost)*100,2),0.00),'%') roi2,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_2)/sum(rebateCost)*100,2),0.00),'%*') roi2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi3")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_3)/sum(rebateCost)*100,2),0.00),'%') roi3,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_3)/sum(rebateCost)*100,2),0.00),'%*') roi3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi4")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_4)/sum(rebateCost)*100,2),0.00),'%') roi4,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_4)/sum(rebateCost)*100,2),0.00),'%*') roi4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi5")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_5)/sum(rebateCost)*100,2),0.00),'%') roi5,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_5)/sum(rebateCost)*100,2),0.00),'%*') roi5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi6")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_7)/sum(rebateCost)*100,2),0.00),'%') roi6,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_7)/sum(rebateCost)*100,2),0.00),'%*') roi6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi7")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_7)/sum(rebateCost)*100,2),0.00),'%') roi7,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_7)/sum(rebateCost)*100,2),0.00),'%*') roi7,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_8)/sum(rebateCost)*100,2),0.00),'%') roi8,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_8)/sum(rebateCost)*100,2),0.00),'%*') roi8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_9)/sum(rebateCost)*100,2),0.00),'%') roi9,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_9)/sum(rebateCost)*100,2),0.00),'%*') roi9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_10)/sum(rebateCost)*100,2),0.00),'%') roi10,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_10)/sum(rebateCost)*100,2),0.00),'%*') roi10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_11)/sum(rebateCost)*100,2),0.00),'%') roi11,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_11)/sum(rebateCost)*100,2),0.00),'%*') roi11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_12)/sum(rebateCost)*100,2),0.00),'%') roi12,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_12)/sum(rebateCost)*100,2),0.00),'%*') roi12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_13)/sum(rebateCost)*100,2),0.00),'%') roi13,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_13)/sum(rebateCost)*100,2),0.00),'%*') roi13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_14)/sum(rebateCost)*100,2),0.00),'%') roi14,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_14)/sum(rebateCost)*100,2),0.00),'%*') roi14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_15)/sum(rebateCost)*100,2),0.00),'%') roi15,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_15)/sum(rebateCost)*100,2),0.00),'%*') roi15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_16)/sum(rebateCost)*100,2),0.00),'%') roi16,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_16)/sum(rebateCost)*100,2),0.00),'%*') roi16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_17)/sum(rebateCost)*100,2),0.00),'%') roi17,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_17)/sum(rebateCost)*100,2),0.00),'%*') roi17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_18)/sum(rebateCost)*100,2),0.00),'%') roi18,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_18)/sum(rebateCost)*100,2),0.00),'%*') roi18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_19)/sum(rebateCost)*100,2),0.00),'%') roi19,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_19)/sum(rebateCost)*100,2),0.00),'%*') roi19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_20)/sum(rebateCost)*100,2),0.00),'%') roi20,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_20)/sum(rebateCost)*100,2),0.00),'%*') roi20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_21)/sum(rebateCost)*100,2),0.00),'%') roi21,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_21)/sum(rebateCost)*100,2),0.00),'%') roi21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi22")'>
					<choose>
						<when test=" diffdate &gt;=22 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_22)/sum(rebateCost)*100,2),0.00),'%') roi22,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_22)/sum(rebateCost)*100,2),0.00),'%*') roi22,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_23)/sum(rebateCost)*100,2),0.00),'%') roi23,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_23)/sum(rebateCost)*100,2),0.00),'%*') roi23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_24)/sum(rebateCost)*100,2),0.00),'%') roi24,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_24)/sum(rebateCost)*100,2),0.00),'%*') roi24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_25)/sum(rebateCost)*100,2),0.00),'%') roi25,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_25)/sum(rebateCost)*100,2),0.00),'%*') roi25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_26)/sum(rebateCost)*100,2),0.00),'%') roi26,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_26)/sum(rebateCost)*100,2),0.00),'%*') roi26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_27)/sum(rebateCost)*100,2),0.00),'%') roi27,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_27)/sum(rebateCost)*100,2),0.00),'%*') roi27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_28)/sum(rebateCost)*100,2),0.00),'%') roi28,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_28)/sum(rebateCost)*100,2),0.00),'%*') roi28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_29)/sum(rebateCost)*100,2),0.00),'%') roi29,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_29)/sum(rebateCost)*100,2),0.00),'%*') roi29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_30)/sum(rebateCost)*100,2),0.00),'%') roi30,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_30)/sum(rebateCost)*100,2),0.00),'%*') roi30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_36)/sum(rebateCost)*100,2),0.00),'%') roi36,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_36)/sum(rebateCost)*100,2),0.00),'%*') roi36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_42)/sum(rebateCost)*100,2),0.00),'%') roi42,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_42)/sum(rebateCost)*100,2),0.00),'%*') roi42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_48)/sum(rebateCost)*100,2),0.00),'%') roi48,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_48)/sum(rebateCost)*100,2),0.00),'%*') roi48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_54)/sum(rebateCost)*100,2),0.00),'%') roi54,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_54)/sum(rebateCost)*100,2),0.00),'%*') roi54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_60)/sum(rebateCost)*100,2),0.00),'%') roi60,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_60)/sum(rebateCost)*100,2),0.00),'%*') roi60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_75)/sum(rebateCost)*100,2),0.00),'%') roi75,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_75)/sum(rebateCost)*100,2),0.00),'%*') roi75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_90)/sum(rebateCost)*100,2),0.00),'%') roi90,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_90)/sum(rebateCost)*100,2),0.00),'%*') roi90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_105)/sum(rebateCost)*100,2),0.00),'%') roi105,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_105)/sum(rebateCost)*100,2),0.00),'%*') roi105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_120)/sum(rebateCost)*100,2),0.00),'%') roi120,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_120)/sum(rebateCost)*100,2),0.00),'%*') roi120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_135)/sum(rebateCost)*100,2),0.00),'%') roi135,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_135)/sum(rebateCost)*100,2),0.00),'%*') roi135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_150)/sum(rebateCost)*100,2),0.00),'%') roi150,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_150)/sum(rebateCost)*100,2),0.00),'%*') roi150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_165)/sum(rebateCost)*100,2),0.00),'%') roi165,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_165)/sum(rebateCost)*100,2),0.00),'%*') roi165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_180)/sum(rebateCost)*100,2),0.00),'%') roi180,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_180)/sum(rebateCost)*100,2),0.00),'%*') roi180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_195)/sum(rebateCost)*100,2),0.00),'%') roi195,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_195)/sum(rebateCost)*100,2),0.00),'%*') roi195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_210)/sum(rebateCost)*100,2),0.00),'%') roi210,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_210)/sum(rebateCost)*100,2),0.00),'%*') roi210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_225)/sum(rebateCost)*100,2),0.00),'%') roi225,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_225)/sum(rebateCost)*100,2),0.00),'%*') roi225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_240)/sum(rebateCost)*100,2),0.00),'%') roi240,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_240)/sum(rebateCost)*100,2),0.00),'%*') roi240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_255)/sum(rebateCost)*100,2),0.00),'%') roi255,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_255)/sum(rebateCost)*100,2),0.00),'%*') roi255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_270)/sum(rebateCost)*100,2),0.00),'%') roi270,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_270)/sum(rebateCost)*100,2),0.00),'%*') roi270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_285)/sum(rebateCost)*100,2),0.00),'%') roi285,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_285)/sum(rebateCost)*100,2),0.00),'%*') roi285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_300)/sum(rebateCost)*100,2),0.00),'%') roi300,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_300)/sum(rebateCost)*100,2),0.00),'%*') roi300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_315)/sum(rebateCost)*100,2),0.00),'%') roi315,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_315)/sum(rebateCost)*100,2),0.00),'%*') roi315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_330)/sum(rebateCost)*100,2),0.00),'%') roi330,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_330)/sum(rebateCost)*100,2),0.00),'%*') roi330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_345)/sum(rebateCost)*100,2),0.00),'%') roi345,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_345)/sum(rebateCost)*100,2),0.00),'%*') roi345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_360)/sum(rebateCost)*100,2),0.00),'%') roi360,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_360)/sum(rebateCost)*100,2),0.00),'%*') roi360,
						</otherwise>
					</choose>
				</if>
            </when>
            <when test="revenueSource ==2">
            	<if test='customizes.contains("addUserLtv0")'>
					round(sum(add_purchase_revenue_1)/100,2) addUserLtv0,
				</if>
                 <if test='customizes.contains("totalIncome")'>
					round(sum(active_purchase_revenue)/100,2) as totalIncome,
				</if>
				<if test='customizes.contains("addArpu")'>
					round(sum(add_purchase_revenue_1)/100/sum(add_user),2) addArpu,
				</if>
				<if test='customizes.contains("dauArpu")'>
					round(sum(active_purchase_revenue)/100/sum(active_user),2) dauArpu,
				</if>
				<if test='customizes.contains("ltv1")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_2)/100/sum(add_user),2) ltv1,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_2)/100/sum(add_user),2),'*') ltv1,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv2")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_3)/100/sum(add_user),2) ltv2,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_3)/100/sum(add_user),2),'*') ltv2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv3")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_4)/100/sum(add_user),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_4)/100/sum(add_user),2),'*') ltv3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv4")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_5)/100/sum(add_user),2) ltv4,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_5)/100/sum(add_user),2),'*') ltv4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv5")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_6)/100/sum(add_user),2) ltv5,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_6)/100/sum(add_user),2),'*') ltv5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv6")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_7)/100/sum(add_user),2) ltv6,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_7)/100/sum(add_user),2) ltv6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_8)/100/sum(add_user),2) ltv8,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_8)/100/sum(add_user),2),'*') ltv8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_9)/100/sum(add_user),2) ltv9,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_9)/100/sum(add_user),2),'*')  ltv9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_10)/100/sum(add_user),2) ltv10,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_10)/100/sum(add_user),2),'*') ltv10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_11)/100/sum(add_user),2) ltv11,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_11)/100/sum(add_user),2),'*') ltv11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_12)/100/sum(add_user),2) ltv12,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_12)/100/sum(add_user),2),'*') ltv12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_13)/100/sum(add_user),2) ltv13,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_13)/100/sum(add_user),2),'*') ltv13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_14)/100/sum(add_user),2) ltv14,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_14)/100/sum(add_user),2),'*') ltv14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_15)/100/sum(add_user),2) ltv15,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_15)/100/sum(add_user),2),'*') ltv15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_16)/100/sum(add_user),2) ltv16,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_16)/100/sum(add_user),2),'*') ltv16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_17)/100/sum(add_user),2) ltv17,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_17)/100/sum(add_user),2),'*') ltv17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_18)/100/sum(add_user),2) ltv18,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_18)/100/sum(add_user),2),'*') ltv18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_19)/100/sum(add_user),2) ltv19,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_19)/100/sum(add_user),2),'*') ltv19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_20)/100/sum(add_user),2) ltv20,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_20)/100/sum(add_user),2),'*') ltv20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_21)/100/sum(add_user),2) ltv21,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_21)/100/sum(add_user),2),'*') ltv21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv22")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_4)/100/sum(add_user),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_4)/100/sum(add_user),2),'*') ltv3,
						</otherwise>
					</choose>
					FORMAT(sum(add_purchase_revenue_22)/100/sum(add_user),2) ltv22,
				</if>
				<if test='customizes.contains("ltv23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_23)/100/sum(add_user),2) ltv23,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_23)/100/sum(add_user),2),'*') ltv23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_24)/100/sum(add_user),2) ltv24,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_24)/100/sum(add_user),2),'*') ltv24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_25)/100/sum(add_user),2) ltv25,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_25)/100/sum(add_user),2),'*') ltv25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_26)/100/sum(add_user),2) ltv26,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_26)/100/sum(add_user),2),'*') ltv26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_27)/100/sum(add_user),2) ltv27,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_27)/100/sum(add_user),2),'*') ltv27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_28)/100/sum(add_user),2) ltv28,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_28)/100/sum(add_user),2),'*') ltv28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_29)/100/sum(add_user),2) ltv29,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_29)/100/sum(add_user),2),'*') ltv29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_30)/100/sum(add_user),2) ltv30,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_30)/100/sum(add_user),2),'*') ltv30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_36)/100/sum(add_user),2) ltv36,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_36)/100/sum(add_user),2),'*') ltv36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_42)/100/sum(add_user),2) ltv42,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_42)/100/sum(add_user),2),'*') ltv42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_48)/100/sum(add_user),2) ltv48,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_48)/100/sum(add_user),2),'*') ltv48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_54)/100/sum(add_user),2) ltv54,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_54)/100/sum(add_user),2),'*') ltv54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_60)/100/sum(add_user),2) ltv60,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_60)/100/sum(add_user),2),'*') ltv60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_75)/100/sum(add_user),2) ltv75,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_75)/100/sum(add_user),2),'*') ltv75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_90)/100/sum(add_user),2) ltv90,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_90)/100/sum(add_user),2),'*') ltv90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_105)/100/sum(add_user),2) ltv105,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_105)/100/sum(add_user),2),'*') ltv105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_120)/100/sum(add_user),2) ltv120,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_120)/100/sum(add_user),2),'*') ltv120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_135)/100/sum(add_user),2) ltv135,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_135)/100/sum(add_user),2),'*') ltv135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_150)/100/sum(add_user),2) ltv150,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_150)/100/sum(add_user),2),'*') ltv150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_165)/100/sum(add_user),2) ltv165,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_165)/100/sum(add_user),2),'*') ltv165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_180)/100/sum(add_user),2) ltv180,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_180)/100/sum(add_user),2),'*') ltv180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_195)/100/sum(add_user),2) ltv195,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_195)/100/sum(add_user),2),'*') ltv195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_210)/100/sum(add_user),2) ltv210,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_210)/100/sum(add_user),2),'*') ltv210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_225)/100/sum(add_user),2) ltv225,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_225)/100/sum(add_user),2),'*') ltv225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_240)/100/sum(add_user),2) ltv240,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_240)/100/sum(add_user),2),'*') ltv240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_255)/100/sum(add_user),2) ltv255,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_255)/100/sum(add_user),2),'*') ltv255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_270)/100/sum(add_user),2) ltv270,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_270)/100/sum(add_user),2),'*') ltv270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_285)/100/sum(add_user),2) ltv285,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_285)/100/sum(add_user),2),'*') ltv285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_300)/100/sum(add_user),2) ltv300,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_300)/100/sum(add_user),2),'*') ltv300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_315)/100/sum(add_user),2) ltv315,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_315)/100/sum(add_user),2),'*') ltv315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_330)/100/sum(add_user),2) ltv330,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_330)/100/sum(add_user),2),'*') ltv330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_345)/100/sum(add_user),2) ltv345,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_345)/100/sum(add_user),2),'*') ltv345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day')">
						FORMAT(sum(add_purchase_revenue_360)/100/sum(add_user),2) ltv360,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_360)/100/sum(add_user),2),'*') ltv360,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("aRoi")'>
					CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_1)/sum(rebateCost)*100,2),0.00),'%') aRoi,
				</if>
				<if test='customizes.contains("roi2")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_2)/sum(rebateCost)*100,2),0.00),'%') roi2,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_2)/sum(rebateCost)*100,2),0.00),'%*') roi2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi3")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_3)/sum(rebateCost)*100,2),0.00),'%') roi3,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_3)/sum(rebateCost)*100,2),0.00),'%*') roi3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi4")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_4)/sum(rebateCost)*100,2),0.00),'%') roi4,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_4)/sum(rebateCost)*100,2),0.00),'%*') roi4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi5")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_5)/sum(rebateCost)*100,2),0.00),'%') roi5,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_5)/sum(rebateCost)*100,2),0.00),'%*') roi5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi6")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_7)/sum(rebateCost)*100,2),0.00),'%') roi6,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_7)/sum(rebateCost)*100,2),0.00),'%*') roi6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi7")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_7)/sum(rebateCost)*100,2),0.00),'%') roi7,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_7)/sum(rebateCost)*100,2),0.00),'%*') roi7,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_8)/sum(rebateCost)*100,2),0.00),'%') roi8,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_8)/sum(rebateCost)*100,2),0.00),'%*') roi8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_9)/sum(rebateCost)*100,2),0.00),'%') roi9,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_9)/sum(rebateCost)*100,2),0.00),'%*') roi9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_10)/sum(rebateCost)*100,2),0.00),'%') roi10,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_10)/sum(rebateCost)*100,2),0.00),'%*') roi10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_11)/sum(rebateCost)*100,2),0.00),'%') roi11,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_11)/sum(rebateCost)*100,2),0.00),'%*') roi11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_12)/sum(rebateCost)*100,2),0.00),'%') roi12,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_12)/sum(rebateCost)*100,2),0.00),'%*') roi12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_13)/sum(rebateCost)*100,2),0.00),'%') roi13,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_13)/sum(rebateCost)*100,2),0.00),'%*') roi13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_14)/sum(rebateCost)*100,2),0.00),'%') roi14,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_14)/sum(rebateCost)*100,2),0.00),'%*') roi14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_15)/sum(rebateCost)*100,2),0.00),'%') roi15,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_15)/sum(rebateCost)*100,2),0.00),'%*') roi15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_16)/sum(rebateCost)*100,2),0.00),'%') roi16,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_16)/sum(rebateCost)*100,2),0.00),'%*') roi16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_17)/sum(rebateCost)*100,2),0.00),'%') roi17,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_17)/sum(rebateCost)*100,2),0.00),'%*') roi17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_18)/sum(rebateCost)*100,2),0.00),'%') roi18,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_18)/sum(rebateCost)*100,2),0.00),'%*') roi18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_19)/sum(rebateCost)*100,2),0.00),'%') roi19,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_19)/sum(rebateCost)*100,2),0.00),'%*') roi19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_20)/sum(rebateCost)*100,2),0.00),'%') roi20,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_20)/sum(rebateCost)*100,2),0.00),'%*') roi20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_21)/sum(rebateCost)*100,2),0.00),'%') roi21,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_21)/sum(rebateCost)*100,2),0.00),'%') roi21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi22")'>
					<choose>
						<when test=" diffdate &gt;=22 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_22)/sum(rebateCost)*100,2),0.00),'%') roi22,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_22)/sum(rebateCost)*100,2),0.00),'%*') roi22,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_23)/sum(rebateCost)*100,2),0.00),'%') roi23,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_23)/sum(rebateCost)*100,2),0.00),'%*') roi23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_24)/sum(rebateCost)*100,2),0.00),'%') roi24,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_24)/sum(rebateCost)*100,2),0.00),'%*') roi24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_25)/sum(rebateCost)*100,2),0.00),'%') roi25,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_25)/sum(rebateCost)*100,2),0.00),'%*') roi25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_26)/sum(rebateCost)*100,2),0.00),'%') roi26,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_26)/sum(rebateCost)*100,2),0.00),'%*') roi26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_27)/sum(rebateCost)*100,2),0.00),'%') roi27,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_27)/sum(rebateCost)*100,2),0.00),'%*') roi27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_28)/sum(rebateCost)*100,2),0.00),'%') roi28,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_28)/sum(rebateCost)*100,2),0.00),'%*') roi28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_29)/sum(rebateCost)*100,2),0.00),'%') roi29,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_29)/sum(rebateCost)*100,2),0.00),'%*') roi29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_30)/sum(rebateCost)*100,2),0.00),'%') roi30,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_30)/sum(rebateCost)*100,2),0.00),'%*') roi30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_36)/sum(rebateCost)*100,2),0.00),'%') roi36,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_36)/sum(rebateCost)*100,2),0.00),'%*') roi36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_42)/sum(rebateCost)*100,2),0.00),'%') roi42,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_42)/sum(rebateCost)*100,2),0.00),'%*') roi42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_48)/sum(rebateCost)*100,2),0.00),'%') roi48,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_48)/sum(rebateCost)*100,2),0.00),'%*') roi48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_54)/sum(rebateCost)*100,2),0.00),'%') roi54,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_54)/sum(rebateCost)*100,2),0.00),'%*') roi54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_60)/sum(rebateCost)*100,2),0.00),'%') roi60,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_60)/sum(rebateCost)*100,2),0.00),'%*') roi60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_75)/sum(rebateCost)*100,2),0.00),'%') roi75,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_75)/sum(rebateCost)*100,2),0.00),'%*') roi75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_90)/sum(rebateCost)*100,2),0.00),'%') roi90,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_90)/sum(rebateCost)*100,2),0.00),'%*') roi90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_105)/sum(rebateCost)*100,2),0.00),'%') roi105,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_105)/sum(rebateCost)*100,2),0.00),'%*') roi105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_120)/sum(rebateCost)*100,2),0.00),'%') roi120,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_120)/sum(rebateCost)*100,2),0.00),'%*') roi120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_135)/sum(rebateCost)*100,2),0.00),'%') roi135,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_135)/sum(rebateCost)*100,2),0.00),'%*') roi135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_150)/sum(rebateCost)*100,2),0.00),'%') roi150,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_150)/sum(rebateCost)*100,2),0.00),'%*') roi150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_165)/sum(rebateCost)*100,2),0.00),'%') roi165,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_165)/sum(rebateCost)*100,2),0.00),'%*') roi165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_180)/sum(rebateCost)*100,2),0.00),'%') roi180,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_180)/sum(rebateCost)*100,2),0.00),'%*') roi180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_195)/sum(rebateCost)*100,2),0.00),'%') roi195,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_195)/sum(rebateCost)*100,2),0.00),'%*') roi195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_210)/sum(rebateCost)*100,2),0.00),'%') roi210,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_210)/sum(rebateCost)*100,2),0.00),'%*') roi210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_225)/sum(rebateCost)*100,2),0.00),'%') roi225,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_225)/sum(rebateCost)*100,2),0.00),'%*') roi225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_240)/sum(rebateCost)*100,2),0.00),'%') roi240,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_240)/sum(rebateCost)*100,2),0.00),'%*') roi240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_255)/sum(rebateCost)*100,2),0.00),'%') roi255,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_255)/sum(rebateCost)*100,2),0.00),'%*') roi255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_270)/sum(rebateCost)*100,2),0.00),'%') roi270,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_270)/sum(rebateCost)*100,2),0.00),'%*') roi270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_285)/sum(rebateCost)*100,2),0.00),'%') roi285,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_285)/sum(rebateCost)*100,2),0.00),'%*') roi285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_300)/sum(rebateCost)*100,2),0.00),'%') roi300,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_300)/sum(rebateCost)*100,2),0.00),'%*') roi300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_315)/sum(rebateCost)*100,2),0.00),'%') roi315,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_315)/sum(rebateCost)*100,2),0.00),'%*') roi315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_330)/sum(rebateCost)*100,2),0.00),'%') roi330,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_330)/sum(rebateCost)*100,2),0.00),'%*') roi330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_345)/sum(rebateCost)*100,2),0.00),'%') roi345,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_345)/sum(rebateCost)*100,2),0.00),'%*') roi345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_360)/sum(rebateCost)*100,2),0.00),'%') roi360,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_360)/sum(rebateCost)*100,2),0.00),'%*') roi360,
						</otherwise>
					</choose>
				</if>
            </when>
            <otherwise>
                <if test='customizes.contains("addUserLtv0")'>
					round(sum(add_purchase_revenue_1)/100+sum(add_revenue_1)/100,2) addUserLtv0,
				</if>
                 <if test='customizes.contains("totalIncome")'>
					round(sum(active_revenue)/100+sum(active_purchase_revenue)/100,2) totalIncome,
				</if>
				<if test='customizes.contains("addArpu")'>
					round((sum(add_purchase_revenue_1)/100+sum(add_revenue_1)/100)/sum(add_user),2) addArpu,
				</if>
				<if test='customizes.contains("dauArpu")'>
					round((sum(active_purchase_revenue)/100+sum(active_revenue)/100)/sum(active_user),2) dauArpu,
				</if>
				<if test='customizes.contains("ltv1")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_2)/100+sum(add_revenue_2)/100)/sum(add_user),2) ltv1,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_2)/100+sum(add_revenue_2)/100)/sum(add_user),2),'*') ltv1,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv2")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_3)/100+sum(add_revenue_3)/100)/sum(add_user),2) ltv2,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_3)/100+sum(add_revenue_3)/100)/sum(add_user),2),'*') ltv2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv3")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_4)/100+sum(add_revenue_4)/100)/sum(add_user),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_4)/100+sum(add_revenue_4)/100)/sum(add_user),2),'*') ltv3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv4")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_5)/100+sum(add_revenue_5)/100)/sum(add_user),2) ltv4,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_5)/100+sum(add_revenue_5)/100)/sum(add_user),2),'*') ltv4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv5")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_6)/100+sum(add_revenue_6)/100)/sum(add_user),2) ltv5,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_6)/100+sum(add_revenue_6)/100)/sum(add_user),2) ,'*') ltv5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv6")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_7)/100+sum(add_revenue_7)/100)/sum(add_user),2) ltv6,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_7)/100+sum(add_revenue_7)/100)/sum(add_user),2),'*') ltv6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_8)/100+sum(add_revenue_8)/100)/sum(add_user),2) ltv8,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_8)/100+sum(add_revenue_8)/100)/sum(add_user),2),'*') ltv8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_9)/100+sum(add_revenue_9)/100)/sum(add_user),2) ltv9,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_9)/100+sum(add_revenue_9)/100)/sum(add_user),2),'*') ltv9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_10)/100+sum(add_revenue_10)/100)/sum(add_user),2) ltv10,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_10)/100+sum(add_revenue_10)/100)/sum(add_user),2),'*') ltv10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_11)/100+sum(add_revenue_11)/100)/sum(add_user),2) ltv11,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_11)/100+sum(add_revenue_11)/100)/sum(add_user),2),'*') ltv11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_12)/100+sum(add_revenue_12)/100)/sum(add_user),2) ltv12,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_12)/100+sum(add_revenue_12)/100)/sum(add_user),2),'*') ltv12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_13)/100+sum(add_revenue_13)/100)/sum(add_user),2) ltv13,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_13)/100+sum(add_revenue_13)/100)/sum(add_user),2),'*') ltv13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_14)/100+sum(add_revenue_14)/100)/sum(add_user),2) ltv14,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_14)/100+sum(add_revenue_14)/100)/sum(add_user),2),'*') ltv14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_15)/100+sum(add_revenue_15)/100)/sum(add_user),2) ltv15,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_15)/100+sum(add_revenue_15)/100)/sum(add_user),2),'*') ltv15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_16)/100+sum(add_revenue_16)/100)/sum(add_user),2) ltv16,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_16)/100+sum(add_revenue_16)/100)/sum(add_user),2),'*') ltv16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_17)/100+sum(add_revenue_17)/100)/sum(add_user),2) ltv17,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_17)/100+sum(add_revenue_17)/100)/sum(add_user),2),'*') ltv17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_18)/100+sum(add_revenue_18)/100)/sum(add_user),2) ltv18,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_18)/100+sum(add_revenue_18)/100)/sum(add_user),2),'*') ltv18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_19)/100+sum(add_revenue_19)/100)/sum(add_user),2) ltv19,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_19)/100+sum(add_revenue_19)/100)/sum(add_user),2),'*') ltv19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_20)/100+sum(add_revenue_20)/100)/sum(add_user),2) ltv20,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_20)/100+sum(add_revenue_20)/100)/sum(add_user),2),'*') ltv20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_21)/100+sum(add_revenue_21)/100)/sum(add_user),2) ltv21,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_21)/100+sum(add_revenue_21)/100)/sum(add_user),2),'*') ltv21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv22")'>
					<choose>
						<when test=" diffdate &gt;=22 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_22)/100+sum(add_revenue_22)/100)/sum(add_user),2) ltv22,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_22)/100+sum(add_revenue_22)/100)/sum(add_user),2),'*') ltv22,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_23)/100+sum(add_revenue_23)/100)/sum(add_user),2) ltv23,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_23)/100+sum(add_revenue_23)/100)/sum(add_user),2),'*') ltv23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_24)/100+sum(add_revenue_24)/100)/sum(add_user),2) ltv24,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_24)/100+sum(add_revenue_24)/100)/sum(add_user),2),'*') ltv24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_25)/100+sum(add_revenue_25)/100)/sum(add_user),2) ltv25,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_25)/100+sum(add_revenue_25)/100)/sum(add_user),2),'*') ltv25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_26)/100+sum(add_revenue_26)/100)/sum(add_user),2) ltv26,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_26)/100+sum(add_revenue_26)/100)/sum(add_user),2),'*') ltv26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_27)/100+sum(add_revenue_27)/100)/sum(add_user),2) ltv27,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_27)/100+sum(add_revenue_27)/100)/sum(add_user),2),'*') ltv27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_28)/100+sum(add_revenue_28)/100)/sum(add_user),2) ltv28,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_28)/100+sum(add_revenue_28)/100)/sum(add_user),2),'*') ltv28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_29)/100+sum(add_revenue_29)/100)/sum(add_user),2) ltv29,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_29)/100+sum(add_revenue_29)/100)/sum(add_user),2),'*') ltv29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_30)/100+sum(add_revenue_30)/100)/sum(add_user),2) ltv30,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_30)/100+sum(add_revenue_30)/100)/sum(add_user),2),'*') ltv30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_36)/100+sum(add_revenue_36)/100)/sum(add_user),2) ltv36,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_36)/100+sum(add_revenue_36)/100)/sum(add_user),2),'*') ltv36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_42)/100+sum(add_revenue_42)/100)/sum(add_user),2) ltv42,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_42)/100+sum(add_revenue_42)/100)/sum(add_user),2),'*') ltv42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_48)/100+sum(add_revenue_48)/100)/sum(add_user),2) ltv48,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_48)/100+sum(add_revenue_48)/100)/sum(add_user),2),'*') ltv48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_54)/100+sum(add_revenue_54)/100)/sum(add_user),2) ltv54,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_54)/100+sum(add_revenue_54)/100)/sum(add_user),2),'*') ltv54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_60)/100+sum(add_revenue_60)/100)/sum(add_user),2) ltv60,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_60)/100+sum(add_revenue_60)/100)/sum(add_user),2),'*') ltv60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_75)/100+sum(add_revenue_75)/100)/sum(add_user),2) ltv75,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_75)/100+sum(add_revenue_75)/100)/sum(add_user),2),'*') ltv75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_90)/100+sum(add_revenue_90)/100)/sum(add_user),2) ltv90,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_90)/100+sum(add_revenue_90)/100)/sum(add_user),2),'*') ltv90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_105)/100+sum(add_revenue_105)/100)/sum(add_user),2) ltv105,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_105)/100+sum(add_revenue_105)/100)/sum(add_user),2),'*') ltv105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_120)/100+sum(add_revenue_120)/100)/sum(add_user),2) ltv120,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_120)/100+sum(add_revenue_120)/100)/sum(add_user),2),'*') ltv120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_135)/100+sum(add_revenue_135)/100)/sum(add_user),2) ltv135,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_135)/100+sum(add_revenue_135)/100)/sum(add_user),2),'*') ltv135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_150)/100+sum(add_revenue_150)/100)/sum(add_user),2) ltv150,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_150)/100+sum(add_revenue_150)/100)/sum(add_user),2),'*') ltv150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_165)/100+sum(add_revenue_165)/100)/sum(add_user),2) ltv165,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_165)/100+sum(add_revenue_165)/100)/sum(add_user),2),'*') ltv165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_180)/100+sum(add_revenue_180)/100)/sum(add_user),2) ltv180,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_180)/100+sum(add_revenue_180)/100)/sum(add_user),2),'*') ltv180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_195)/100+sum(add_revenue_195)/100)/sum(add_user),2) ltv195,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_195)/100+sum(add_revenue_195)/100)/sum(add_user),2),'*') ltv195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_210)/100+sum(add_revenue_210)/100)/sum(add_user),2) ltv210,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_210)/100+sum(add_revenue_210)/100)/sum(add_user),2),'*') ltv210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_225)/100+sum(add_revenue_225)/100)/sum(add_user),2) ltv225,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_225)/100+sum(add_revenue_225)/100)/sum(add_user),2),'*') ltv225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_240)/100+sum(add_revenue_240)/100)/sum(add_user),2) ltv240,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_240)/100+sum(add_revenue_240)/100)/sum(add_user),2),'*') ltv240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_255)/100+sum(add_revenue_255)/100)/sum(add_user),2) ltv255,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_255)/100+sum(add_revenue_255)/100)/sum(add_user),2),'*') ltv255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_270)/100+sum(add_revenue_270)/100)/sum(add_user),2) ltv270,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_270)/100+sum(add_revenue_270)/100)/sum(add_user),2),'*') ltv270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_285)/100+sum(add_revenue_285)/100)/sum(add_user),2) ltv285,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_285)/100+sum(add_revenue_285)/100)/sum(add_user),2),'*') ltv285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_300)/100+sum(add_revenue_300)/100)/sum(add_user),2) ltv300,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_300)/100+sum(add_revenue_300)/100)/sum(add_user),2),'*') ltv300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_315)/100+sum(add_revenue_315)/100)/sum(add_user),2) ltv315,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_315)/100+sum(add_revenue_315)/100)/sum(add_user),2),'*') ltv315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_330)/100+sum(add_revenue_330)/100)/sum(add_user),2) ltv330,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_330)/100+sum(add_revenue_330)/100)/sum(add_user),2),'*') ltv330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_345)/100+sum(add_revenue_345)/100)/sum(add_user),2) ltv345,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_345)/100+sum(add_revenue_345)/100)/sum(add_user),2),'*') ltv345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day')">
						FORMAT((sum(add_purchase_revenue_360)/100+sum(add_revenue_360)/100)/sum(add_user),2) ltv360,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_360)/100+sum(add_revenue_360)/100)/sum(add_user),2),'*') ltv360,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("aRoi")'>
					CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_1)+sum(add_revenue_1))/sum(rebateCost)*100,2),0.00),'%') aRoi,
				</if>
				<if test='customizes.contains("roi2")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_2)+sum(add_revenue_2))/sum(rebateCost)*100,2),0.00),'%') roi2,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_2)+sum(add_revenue_2))/sum(rebateCost)*100,2),0.00),'%*') roi2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi3")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_3)+sum(add_revenue_3))/sum(rebateCost)*100,2),0.00),'%') roi3,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_3)+sum(add_revenue_3))/sum(rebateCost)*100,2),0.00),'%*') roi3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi4")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_4)+sum(add_revenue_4))/sum(rebateCost)*100,2),0.00),'%') roi4,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_4)+sum(add_revenue_4))/sum(rebateCost)*100,2),0.00),'%*') roi4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi5")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_5)+sum(add_revenue_5))/sum(rebateCost)*100,2),0.00),'%') roi5,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_5)+sum(add_revenue_5))/sum(rebateCost)*100,2),0.00),'%*') roi5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi6")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_6)+sum(add_revenue_6))/sum(rebateCost)*100,2),0.00),'%') roi6,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_6)+sum(add_revenue_6))/sum(rebateCost)*100,2),0.00),'%*') roi6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi7")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_7)+sum(add_revenue_7))/sum(rebateCost)*100,2),0.00),'%') roi7,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_7)+sum(add_revenue_7))/sum(rebateCost)*100,2),0.00),'%*') roi7,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_8)+sum(add_revenue_8))/sum(rebateCost)*100,2),0.00),'%') roi8,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_8)+sum(add_revenue_8))/sum(rebateCost)*100,2),0.00),'%*') roi8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_9)+sum(add_revenue_9))/sum(rebateCost)*100,2),0.00),'%') roi9,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_9)+sum(add_revenue_9))/sum(rebateCost)*100,2),0.00),'%*') roi9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_10)+sum(add_revenue_10))/sum(rebateCost)*100,2),0.00),'%') roi10,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_10)+sum(add_revenue_10))/sum(rebateCost)*100,2),0.00),'%*') roi10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_11)+sum(add_revenue_11))/sum(rebateCost)*100,2),0.00),'%') roi11,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_11)+sum(add_revenue_11))/sum(rebateCost)*100,2),0.00),'%*') roi11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_12)+sum(add_revenue_12))/sum(rebateCost)*100,2),0.00),'%') roi12,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_12)+sum(add_revenue_12))/sum(rebateCost)*100,2),0.00),'%*') roi12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_13)+sum(add_revenue_13))/sum(rebateCost)*100,2),0.00),'%') roi13,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_13)+sum(add_revenue_13))/sum(rebateCost)*100,2),0.00),'%*') roi13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_14)+sum(add_revenue_14))/sum(rebateCost)*100,2),0.00),'%') roi14,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_14)+sum(add_revenue_14))/sum(rebateCost)*100,2),0.00),'%*') roi14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_15)+sum(add_revenue_15))/sum(rebateCost)*100,2),0.00),'%') roi15,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_15)+sum(add_revenue_15))/sum(rebateCost)*100,2),0.00),'%*') roi15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_16)+sum(add_revenue_16))/sum(rebateCost)*100,2),0.00),'%') roi16,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_16)+sum(add_revenue_16))/sum(rebateCost)*100,2),0.00),'%*') roi16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_17)+sum(add_revenue_17))/sum(rebateCost)*100,2),0.00),'%') roi17,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_17)+sum(add_revenue_17))/sum(rebateCost)*100,2),0.00),'%*') roi17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_18)+sum(add_revenue_18))/sum(rebateCost)*100,2),0.00),'%') roi18,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_18)+sum(add_revenue_18))/sum(rebateCost)*100,2),0.00),'%*') roi18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_19)+sum(add_revenue_19))/sum(rebateCost)*100,2),0.00),'%') roi19,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_19)+sum(add_revenue_19))/sum(rebateCost)*100,2),0.00),'%*') roi19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_20)+sum(add_revenue_20))/sum(rebateCost)*100,2),0.00),'%') roi20,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_20)+sum(add_revenue_20))/sum(rebateCost)*100,2),0.00),'%*') roi20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_21)+sum(add_revenue_21))/sum(rebateCost)*100,2),0.00),'%') roi21,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_21)+sum(add_revenue_21))/sum(rebateCost)*100,2),0.00),'%*') roi21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi22")'>
					<choose>
						<when test=" diffdate &gt;=22 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_22)+sum(add_revenue_22))/sum(rebateCost)*100,2),0.00),'%') roi22,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_22)+sum(add_revenue_22))/sum(rebateCost)*100,2),0.00),'%*') roi22,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_23)+sum(add_revenue_23))/sum(rebateCost)*100,2),0.00),'%') roi23,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_23)+sum(add_revenue_23))/sum(rebateCost)*100,2),0.00),'%*') roi23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_24)+sum(add_revenue_24))/sum(rebateCost)*100,2),0.00),'%') roi24,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_24)+sum(add_revenue_24))/sum(rebateCost)*100,2),0.00),'%*') roi24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_25)+sum(add_revenue_25))/sum(rebateCost)*100,2),0.00),'%') roi25,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_25)+sum(add_revenue_25))/sum(rebateCost)*100,2),0.00),'%*') roi25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_26)+sum(add_revenue_26))/sum(rebateCost)*100,2),0.00),'%') roi26,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_26)+sum(add_revenue_26))/sum(rebateCost)*100,2),0.00),'%*') roi26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_27)+sum(add_revenue_27))/sum(rebateCost)*100,2),0.00),'%') roi27,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_27)+sum(add_revenue_27))/sum(rebateCost)*100,2),0.00),'%*') roi27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_28)+sum(add_revenue_28))/sum(rebateCost)*100,2),0.00),'%') roi28,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_28)+sum(add_revenue_28))/sum(rebateCost)*100,2),0.00),'%*') roi28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_29)+sum(add_revenue_29))/sum(rebateCost)*100,2),0.00),'%') roi29,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_29)+sum(add_revenue_29))/sum(rebateCost)*100,2),0.00),'%*') roi29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_30)+sum(add_revenue_30))/sum(rebateCost)*100,2),0.00),'%') roi30,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_30)+sum(add_revenue_30))/sum(rebateCost)*100,2),0.00),'%*') roi30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_36)+sum(add_revenue_36))/sum(rebateCost)*100,2),0.00),'%') roi36,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_36)+sum(add_revenue_36))/sum(rebateCost)*100,2),0.00),'%*') roi36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_42)+sum(add_revenue_42))/sum(rebateCost)*100,2),0.00),'%') roi42,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_42)+sum(add_revenue_42))/sum(rebateCost)*100,2),0.00),'%*') roi42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_48)+sum(add_revenue_48))/sum(rebateCost)*100,2),0.00),'%') roi48,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_48)+sum(add_revenue_48))/sum(rebateCost)*100,2),0.00),'%*') roi48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_54)+sum(add_revenue_54))/sum(rebateCost)*100,2),0.00),'%') roi54,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_54)+sum(add_revenue_54))/sum(rebateCost)*100,2),0.00),'%*') roi54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_60)+sum(add_revenue_60))/sum(rebateCost)*100,2),0.00),'%') roi60,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_60)+sum(add_revenue_60))/sum(rebateCost)*100,2),0.00),'%*') roi60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_75)+sum(add_revenue_75))/sum(rebateCost)*100,2),0.00),'%') roi75,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_75)+sum(add_revenue_75))/sum(rebateCost)*100,2),0.00),'%*') roi75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_90)+sum(add_revenue_90))/sum(rebateCost)*100,2),0.00),'%') roi90,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_90)+sum(add_revenue_90))/sum(rebateCost)*100,2),0.00),'%*') roi90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_105)+sum(add_revenue_105))/sum(rebateCost)*100,2),0.00),'%') roi105,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_105)+sum(add_revenue_105))/sum(rebateCost)*100,2),0.00),'%*') roi105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_120)+sum(add_revenue_120))/sum(rebateCost)*100,2),0.00),'%') roi120,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_120)+sum(add_revenue_120))/sum(rebateCost)*100,2),0.00),'%*') roi120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_135)+sum(add_revenue_135))/sum(rebateCost)*100,2),0.00),'%') roi135,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_135)+sum(add_revenue_135))/sum(rebateCost)*100,2),0.00),'%*') roi135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_150)+sum(add_revenue_150))/sum(rebateCost)*100,2),0.00),'%') roi150,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_150)+sum(add_revenue_150))/sum(rebateCost)*100,2),0.00),'%*') roi150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_165)+sum(add_revenue_165))/sum(rebateCost)*100,2),0.00),'%') roi165,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_165)+sum(add_revenue_165))/sum(rebateCost)*100,2),0.00),'%*') roi165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_180)+sum(add_revenue_180))/sum(rebateCost)*100,2),0.00),'%') roi180,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_180)+sum(add_revenue_180))/sum(rebateCost)*100,2),0.00),'%*') roi180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_195)+sum(add_revenue_195))/sum(rebateCost)*100,2),0.00),'%') roi195,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_195)+sum(add_revenue_195))/sum(rebateCost)*100,2),0.00),'%*') roi195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_210)+sum(add_revenue_210))/sum(rebateCost)*100,2),0.00),'%') roi210,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_210)+sum(add_revenue_210))/sum(rebateCost)*100,2),0.00),'%*') roi210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_225)+sum(add_revenue_225))/sum(rebateCost)*100,2),0.00),'%') roi225,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_225)+sum(add_revenue_225))/sum(rebateCost)*100,2),0.00),'%*') roi225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_240)+sum(add_revenue_240))/sum(rebateCost)*100,2),0.00),'%') roi240,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_240)+sum(add_revenue_240))/sum(rebateCost)*100,2),0.00),'%*') roi240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_255)+sum(add_revenue_255))/sum(rebateCost)*100,2),0.00),'%') roi255,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_255)+sum(add_revenue_255))/sum(rebateCost)*100,2),0.00),'%*') roi255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_270)+sum(add_revenue_270))/sum(rebateCost)*100,2),0.00),'%') roi270,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_270)+sum(add_revenue_270))/sum(rebateCost)*100,2),0.00),'%*') roi270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_285)+sum(add_revenue_285))/sum(rebateCost)*100,2),0.00),'%') roi285,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_285)+sum(add_revenue_285))/sum(rebateCost)*100,2),0.00),'%*') roi285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_300)+sum(add_revenue_300))/sum(rebateCost)*100,2),0.00),'%') roi300,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_300)+sum(add_revenue_300))/sum(rebateCost)*100,2),0.00),'%*') roi300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_315)+sum(add_revenue_315))/sum(rebateCost)*100,2),0.00),'%') roi315,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_315)+sum(add_revenue_315))/sum(rebateCost)*100,2),0.00),'%*') roi315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_330)+sum(add_revenue_330))/sum(rebateCost)*100,2),0.00),'%') roi330,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_330)+sum(add_revenue_330))/sum(rebateCost)*100,2),0.00),'%*') roi330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_345)+sum(add_revenue_345))/sum(rebateCost)*100,2),0.00),'%') roi345,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_345)+sum(add_revenue_345))/sum(rebateCost)*100,2),0.00),'%*') roi345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_360)+sum(add_revenue_360))/sum(rebateCost)*100,2),0.00),'%') roi360,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_360)+sum(add_revenue_360))/sum(rebateCost)*100,2),0.00),'%*') roi360,
						</otherwise>
					</choose>
				</if>
            </otherwise>
        </choose>
       	p_channel childChannel  from  #{tableName}  where   day BETWEEN #{beginDate} AND #{endDate} 
		<if test="appId != null and appId != ''">
			and app_id in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and media in (${media}) 
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account in (${accountId}) 
		</if>
		<if test="putUser != null and putUser != ''">
			and putUser in (${putUser}) 
		</if>
		<if test="campaignId != null and campaignId != ''">
			and campaign_id in (${campaignId}) 
		</if>
		<if test="campaignName != null and campaignName != ''">
			and campaign_name = #{campaignName} 
		</if>
		<if test="childChannel != null and childChannel != ''">
			and p_channel in (${childChannel})
		</if>
		<if test="groupName != null and groupName != ''">
			and group_name = #{groupName} 
		</if>
		<if test="company != null and company != ''">
			and company in (${company}) 
		</if>
		<if test="adsensePosition != null and adsensePosition != ''">
			and adsensePosition in (${adsensePosition}) 
		</if>
		<if test="strategy != null and strategy != ''">
			and transfer_type in (${strategy}) 
		</if>
		<if test="accountType != null and accountType != ''">
			and account_type in (${accountType}) 
		</if>
		<if test="scope != null and scope != ''">
          <if test="index == 'spend'">
              and rebateCost/100 <![CDATA[ ${symbol} ]]> #{number}
          </if>
        </if>
	</sql>
	
	
	<!-- 活跃用户分账号收入查询  -->
  	<select id="activeUserIncomeReport" parameterType="com.wbgame.pojo.jettison.param.ActiveUserIncomParam" resultType="com.wbgame.pojo.jettison.vo.ActiveUserIncomVo">
		<include refid="active_user_income_sql"/>
	</select>
	<!-- 活跃用户分账号收入汇总  -->
	<select id="activeUserIncomeReportTotal" parameterType="com.wbgame.pojo.jettison.param.ActiveUserIncomParam"  resultType="com.wbgame.pojo.jettison.vo.ActiveUserIncomVo">
			SELECT media,channel,day,appId, accountId,company,sum(activeUser) activeUser,
			sum(totalIncome) totalIncome,putUser,sum(spend) spend,sum(ipaIncome) ipaIncome 
		from (<include refid="active_user_income_sql"/>) 
	</select>
	<sql id="active_user_income_sql">
		SELECT media,channel,day,app_id as appId,account as accountId,company,sum(active_user) activeUser,
			sum(active_revenue)/100 totalIncome,putUser,sum(rebateCost)/100 spend,sum(active_purchase_revenue)/100 ipaIncome from ads_account_reyun
		where day BETWEEN #{beginDate} AND #{endDate}  and active_user>0
		<if test="appId != null and appId != ''">
			and app_id in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and media in (${media}) 
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account in (${accountId}) 
		</if>
		<if test="putUser != null and putUser != ''">
			and putUser in (${putUser}) 
		</if>
		<if test="company != null and company != ''">
			and company in (${company}) 
		</if>
		group by app_id
		<if test="group != null and group != ''">
			,${group}
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by day asc ,spend desc
			</otherwise>
		</choose>
	</sql>
	<!-- 创意ROI报表查询  -->
  	<select id="creativeReport" parameterType="com.wbgame.pojo.jettison.param.CreativeReportParam" resultType="com.wbgame.pojo.jettison.vo.MediaCreativeVo">
		<include refid="creative_roi_sql"/>
	</select>
	<!-- 创意ROI报表汇总  -->
	<select id="creativeReportTotal" parameterType="com.wbgame.pojo.jettison.param.CreativeReportParam"  resultType="com.wbgame.pojo.jettison.vo.MediaCreativeVo">
			SELECT media,channel,a_day day,app_id as appId,creative_id as creativeId,putUser,artist,account as accountId,round(sum(add_revenue_1)/100,2) ltv1,round(sum(rebateCost)/100,2) spend,
		sum(add_user) addUser,round(sum(add_purchase_revenue_1)/100+sum(add_revenue_1)/100,2) firstDayIncome,round(sum(add_purchase_revenue_1)/100,2) ipaIncome,
		FORMAT(IFNULL(sum(add_user_revenue_video)/sum(add_user_pv_video)*10,0.00),2) videoEcpm,FORMAT(IFNULL(sum(add_user_revenue_plaque)/sum(add_user_pv_plaque)*10,0.00),2) plaqueEcpm,
		FORMAT(IFNULL(sum(rebateCost)/100/sum(add_user),0.00),2) cpa,FORMAT(IFNULL((sum(add_purchase_revenue_1)/100+sum(add_revenue_1)/100)/sum(add_user),0.00),2) arpu,
		round(sum(add_user_pv_plaque)/sum(add_user),2) avgPlaqueShowTimes,round(sum(add_user_pv_video)/sum(add_user),2) avgVideoShowTimes,
		CONCAT(FORMAT(IFNULL(sum(add_revenue_1)/sum(rebateCost)*100,0.00),2),'%') roi,CONCAT(FORMAT(IFNULL(sum(add_purchase_revenue_1)/sum(rebateCost)*100,0.00),2),'%') ipaRoi,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_1)+sum(add_revenue_1))/sum(rebateCost)*100,0.00),2),'%') firstDayRoi,FORMAT(sum(rebateCost)/100/sum(impressions)*1000,2) avgShowSpend,
		CONCAT(FORMAT(IFNULL(sum(installs)/sum(clicks)*100,0.00),2),'%') installRate,CONCAT(FORMAT(IFNULL(sum(clicks)/sum(impressions)*100,0.00),2),'%') clickRate,
		CONCAT(FORMAT(IFNULL(sum(retention_day_1)/sum(add_user)*100,0.00),2),'%') retentionOneDayRate,CONCAT(FORMAT(IFNULL(sum(retention_day_2)/sum(add_user)*100,0.00),2),'%') rd2,
		CONCAT(FORMAT(IFNULL(sum(retention_day_3)/sum(add_user)*100,0.00),2),'%') rd3,CONCAT(FORMAT(IFNULL(sum(retention_day_4)/sum(add_user)*100,0.00),2),'%') rd4,
		CONCAT(FORMAT(IFNULL(sum(retention_day_5)/sum(add_user)*100,0.00),2),'%') rd5,CONCAT(FORMAT(IFNULL(sum(retention_day_6)/sum(add_user)*100,0.00),2),'%') rd6,
		CONCAT(FORMAT(IFNULL(sum(retention_day_7)/sum(add_user)*100,0.00),2),'%') rd7,CONCAT(FORMAT(IFNULL(sum(retention_day_8)/sum(add_user)*100,0.00),2),'%') rd8,
		CONCAT(FORMAT(IFNULL(sum(retention_day_9)/sum(add_user)*100,0.00),2),'%') rd9,CONCAT(FORMAT(IFNULL(sum(retention_day_10)/sum(add_user)*100,0.00),2),'%') rd10,
		CONCAT(FORMAT(IFNULL(sum(retention_day_11)/sum(add_user)*100,0.00),2),'%') rd11,CONCAT(FORMAT(IFNULL(sum(retention_day_12)/sum(add_user)*100,0.00),2),'%') rd12,
		CONCAT(FORMAT(IFNULL(sum(retention_day_13)/sum(add_user)*100,0.00),2),'%') rd13,CONCAT(FORMAT(IFNULL(sum(retention_day_14)/sum(add_user)*100,0.00),2),'%') rd14,
		CONCAT(FORMAT(IFNULL(sum(retention_day_15)/sum(add_user)*100,0.00),2),'%') rd15,CONCAT(FORMAT(IFNULL(sum(retention_day_16)/sum(add_user)*100,0.00),2),'%') rd16,
		CONCAT(FORMAT(IFNULL(sum(retention_day_17)/sum(add_user)*100,0.00),2),'%') rd17,CONCAT(FORMAT(IFNULL(sum(retention_day_18)/sum(add_user)*100,0.00),2),'%') rd18,
		CONCAT(FORMAT(IFNULL(sum(retention_day_19)/sum(add_user)*100,0.00),2),'%') rd19,CONCAT(FORMAT(IFNULL(sum(retention_day_20)/sum(add_user)*100,0.00),2),'%') rd20,
		CONCAT(FORMAT(IFNULL(sum(retention_day_21)/sum(add_user)*100,0.00),2),'%') rd21,CONCAT(FORMAT(IFNULL(sum(retention_day_22)/sum(add_user)*100,0.00),2),'%') rd22,
		CONCAT(FORMAT(IFNULL(sum(retention_day_23)/sum(add_user)*100,0.00),2),'%') rd23,CONCAT(FORMAT(IFNULL(sum(retention_day_24)/sum(add_user)*100,0.00),2),'%') rd24,
		CONCAT(FORMAT(IFNULL(sum(retention_day_25)/sum(add_user)*100,0.00),2),'%') rd25,CONCAT(FORMAT(IFNULL(sum(retention_day_26)/sum(add_user)*100,0.00),2),'%') rd26,
		CONCAT(FORMAT(IFNULL(sum(retention_day_27)/sum(add_user)*100,0.00),2),'%') rd27,CONCAT(FORMAT(IFNULL(sum(retention_day_28)/sum(add_user)*100,0.00),2),'%') rd28,
		CONCAT(FORMAT(IFNULL(sum(retention_day_29)/sum(add_user)*100,0.00),2),'%') rd29,CONCAT(FORMAT(IFNULL(sum(retention_day_30)/sum(add_user)*100,0.00),2),'%') rd30,
		sum(active_purchase_users) active_purchase_users,sum(add_purchase_users) add_purchase_users,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_1)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd1,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_2)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd2,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_3)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd3,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_4)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd4,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_5)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd5,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_6)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd6,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_7)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd7,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_8)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd8,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_9)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd9,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_10)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd10,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_11)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd11,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_12)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd12,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_13)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd13,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_14)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd14,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_15)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd15,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_16)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd16,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_17)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd17,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_18)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd18,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_19)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd19,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_20)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd20,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_21)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd21,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_22)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd22,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_23)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd23,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_24)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd24,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_25)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd25,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_26)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd26,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_27)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd27,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_28)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd28,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_29)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd29,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_30)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd30,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_2)+sum(add_revenue_2))/sum(rebateCost)*100,0.00),2),'%') roi2,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_3)+sum(add_revenue_3))/sum(rebateCost)*100,0.00),2),'%') roi3,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_4)+sum(add_revenue_4))/sum(rebateCost)*100,0.00),2),'%') roi4,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_5)+sum(add_revenue_5))/sum(rebateCost)*100,0.00),2),'%') roi5,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_6)+sum(add_revenue_6))/sum(rebateCost)*100,0.00),2),'%') roi6,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_7)+sum(add_revenue_7))/sum(rebateCost)*100,0.00),2),'%') roi7,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_8)+sum(add_revenue_8))/sum(rebateCost)*100,0.00),2),'%') roi8,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_9)+sum(add_revenue_9))/sum(rebateCost)*100,0.00),2),'%') roi9,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_10)+sum(add_revenue_10))/sum(rebateCost)*100,0.00),2),'%') roi10,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_11)+sum(add_revenue_11))/sum(rebateCost)*100,0.00),2),'%') roi11,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_12)+sum(add_revenue_12))/sum(rebateCost)*100,0.00),2),'%') roi12,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_13)+sum(add_revenue_13))/sum(rebateCost)*100,0.00),2),'%') roi13,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_14)+sum(add_revenue_14))/sum(rebateCost)*100,0.00),2),'%') roi14,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_15)+sum(add_revenue_15))/sum(rebateCost)*100,0.00),2),'%') roi15,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_16)+sum(add_revenue_16))/sum(rebateCost)*100,0.00),2),'%') roi16,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_17)+sum(add_revenue_17))/sum(rebateCost)*100,0.00),2),'%') roi17,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_18)+sum(add_revenue_18))/sum(rebateCost)*100,0.00),2),'%') roi18,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_19)+sum(add_revenue_19))/sum(rebateCost)*100,0.00),2),'%') roi19,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_20)+sum(add_revenue_20))/sum(rebateCost)*100,0.00),2),'%') roi20,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_21)+sum(add_revenue_21))/sum(rebateCost)*100,0.00),2),'%') roi21,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_22)+sum(add_revenue_22))/sum(rebateCost)*100,0.00),2),'%') roi22,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_23)+sum(add_revenue_23))/sum(rebateCost)*100,0.00),2),'%') roi23,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_24)+sum(add_revenue_24))/sum(rebateCost)*100,0.00),2),'%') roi24,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_25)+sum(add_revenue_25))/sum(rebateCost)*100,0.00),2),'%') roi25,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_26)+sum(add_revenue_26))/sum(rebateCost)*100,0.00),2),'%') roi26,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_27)+sum(add_revenue_27))/sum(rebateCost)*100,0.00),2),'%') roi27,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_28)+sum(add_revenue_28))/sum(rebateCost)*100,0.00),2),'%') roi28,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_29)+sum(add_revenue_29))/sum(rebateCost)*100,0.00),2),'%') roi29,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_30)+sum(add_revenue_30))/sum(rebateCost)*100,0.00),2),'%') roi30,FORMAT(IFNULL(sum(rebateCost)/100/sum(gamepaycount),0.00),2) payCost,
		FORMAT(IFNULL(sum(rebateCost)/100/sum(paycount),0.00),2) firstChargeCost,FORMAT(sum(paycount)/sum(installs),2) firstPaymentRate,sum(paycount) firstPayCount,sum(gamepaycount) paymentTimes,
		FORMAT(IFNULL((sum(add_purchase_revenue_2)/100+sum(add_revenue_2)/100)/sum(add_user),0.00),2) ltv2,FORMAT(IFNULL((sum(add_purchase_revenue_3)/100+sum(add_revenue_3)/100)/sum(add_user),0.00),2) ltv3,
		FORMAT(IFNULL((sum(add_purchase_revenue_4)/100+sum(add_revenue_4)/100)/sum(add_user),0.00),2) ltv4,FORMAT(IFNULL((sum(add_purchase_revenue_5)/100+sum(add_revenue_5)/100)/sum(add_user),0.00),2) ltv5,
		FORMAT(IFNULL((sum(add_purchase_revenue_6)/100+sum(add_revenue_6)/100)/sum(add_user),0.00),2) ltv6,FORMAT(IFNULL((sum(add_purchase_revenue_7)/100+sum(add_revenue_7)/100)/sum(add_user),0.00),2) ltv7,
		FORMAT(IFNULL((sum(add_purchase_revenue_8)/100+sum(add_revenue_8)/100)/sum(add_user),0.00),2) ltv8,FORMAT(IFNULL((sum(add_purchase_revenue_9)/100+sum(add_revenue_9)/100)/sum(add_user),0.00),2) ltv9,
		FORMAT(IFNULL((sum(add_purchase_revenue_10)/100+sum(add_revenue_10)/100)/sum(add_user),0.00),2) ltv10,FORMAT(IFNULL((sum(add_purchase_revenue_11)/100+sum(add_revenue_11)/100)/sum(add_user),0.00),2) ltv11,
		FORMAT(IFNULL((sum(add_purchase_revenue_12)/100+sum(add_revenue_12)/100)/sum(add_user),0.00),2) ltv12,FORMAT(IFNULL((sum(add_purchase_revenue_13)/100+sum(add_revenue_13)/100)/sum(add_user),0.00),2) ltv13,
		FORMAT(IFNULL((sum(add_purchase_revenue_14)/100+sum(add_revenue_14)/100)/sum(add_user),0.00),2) ltv14,FORMAT(IFNULL((sum(add_purchase_revenue_15)/100+sum(add_revenue_15)/100)/sum(add_user),0.00),2) ltv15,
		FORMAT(IFNULL((sum(add_purchase_revenue_16)/100+sum(add_revenue_16)/100)/sum(add_user),0.00),2) ltv16,FORMAT(IFNULL((sum(add_purchase_revenue_17)/100+sum(add_revenue_17)/100)/sum(add_user),0.00),2) ltv17,
		FORMAT(IFNULL((sum(add_purchase_revenue_18)/100+sum(add_revenue_18)/100)/sum(add_user),0.00),2) ltv18,FORMAT(IFNULL((sum(add_purchase_revenue_19)/100+sum(add_revenue_19)/100)/sum(add_user),0.00),2) ltv19,
		FORMAT(IFNULL((sum(add_purchase_revenue_20)/100+sum(add_revenue_20)/100)/sum(add_user),0.00),2) ltv20,FORMAT(IFNULL((sum(add_purchase_revenue_21)/100+sum(add_revenue_21)/100)/sum(add_user),0.00),2) ltv21,
		FORMAT(IFNULL((sum(add_purchase_revenue_22)/100+sum(add_revenue_22)/100)/sum(add_user),0.00),2) ltv22,FORMAT(IFNULL((sum(add_purchase_revenue_23)/100+sum(add_revenue_23)/100)/sum(add_user),0.00),2) ltv23,
		FORMAT(IFNULL((sum(add_purchase_revenue_24)/100+sum(add_revenue_24)/100)/sum(add_user),0.00),2) ltv24,FORMAT(IFNULL((sum(add_purchase_revenue_25)/100+sum(add_revenue_25)/100)/sum(add_user),0.00),2) ltv25,
		FORMAT(IFNULL((sum(add_purchase_revenue_26)/100+sum(add_revenue_26)/100)/sum(add_user),0.00),2) ltv26,FORMAT(IFNULL((sum(add_purchase_revenue_27)/100+sum(add_revenue_27)/100)/sum(add_user),0.00),2) ltv27,
		FORMAT(IFNULL((sum(add_purchase_revenue_28)/100+sum(add_revenue_28)/100)/sum(add_user),0.00),2) ltv28,FORMAT(IFNULL((sum(add_purchase_revenue_29)/100+sum(add_revenue_29)/100)/sum(add_user),0.00),2) ltv29,
		FORMAT(IFNULL((sum(add_purchase_revenue_30)/100+sum(add_revenue_30)/100)/sum(add_user),0.00),2) ltv30
		from ads_creative_reyun_v2 where a_day BETWEEN #{beginDate} AND #{endDate}  
		<if test="appId != null and appId != ''">
			and app_id in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and media in (${media}) 
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account in (${accountId}) 
		</if>
		<if test="putUser != null and putUser != ''">
			and putUser in (${putUser}) 
		</if>
		<if test="artist != null and artist.size > 0">
            AND (
            <foreach collection="artist" index="index" item="item" separator="OR">
                artist LIKE "%"#{item}"%"
            </foreach>
            )
        </if>
        <if test="scope != null and scope != ''">
          <if test="index == 'spend'">
              and  rebateCost/100 <![CDATA[ ${symbol} ]]> #{number}
          </if>
        </if>
	</select>
	
	<sql id="creative_roi_sql">
		SELECT media,channel,a_day day,app_id as appId,creative_id as creativeId,putUser,artist,account as accountId,round(sum(add_revenue_1)/100,2) ltv1,round(sum(rebateCost)/100,2) spend,
		sum(add_user) addUser,round(sum(add_purchase_revenue_1)/100+sum(add_revenue_1)/100,2) firstDayIncome,round(sum(add_purchase_revenue_1)/100,2) ipaIncome,
		FORMAT(IFNULL(sum(add_user_revenue_video)/sum(add_user_pv_video)*10,0.00),2) videoEcpm,FORMAT(IFNULL(sum(add_user_revenue_plaque)/sum(add_user_pv_plaque)*10,0.00),2) plaqueEcpm,
		FORMAT(IFNULL(sum(rebateCost)/100/sum(add_user),0.00),2) cpa,FORMAT(IFNULL((sum(add_purchase_revenue_1)/100+sum(add_revenue_1)/100)/sum(add_user),0.00),2) arpu,
		round(sum(add_user_pv_plaque)/sum(add_user),2) avgPlaqueShowTimes,round(sum(add_user_pv_video)/sum(add_user),2) avgVideoShowTimes,
		CONCAT(FORMAT(IFNULL(sum(add_revenue_1)/sum(rebateCost)*100,0.00),2),'%') roi,CONCAT(FORMAT(IFNULL(sum(add_purchase_revenue_1)/sum(rebateCost)*100,0.00),2),'%') ipaRoi,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_1)+sum(add_revenue_1))/sum(rebateCost)*100,0.00),2),'%') firstDayRoi,FORMAT(sum(rebateCost)/100/sum(impressions)*1000,2) avgShowSpend,
		CONCAT(FORMAT(IFNULL(sum(installs)/sum(clicks)*100,0.00),2),'%') installRate,CONCAT(FORMAT(IFNULL(sum(clicks)/sum(impressions)*100,0.00),2),'%') clickRate,
		CONCAT(FORMAT(IFNULL(sum(retention_day_1)/sum(add_user)*100,0.00),2),'%') retentionOneDayRate,CONCAT(FORMAT(IFNULL(sum(retention_day_2)/sum(add_user)*100,0.00),2),'%') rd2,
		CONCAT(FORMAT(IFNULL(sum(retention_day_3)/sum(add_user)*100,0.00),2),'%') rd3,CONCAT(FORMAT(IFNULL(sum(retention_day_4)/sum(add_user)*100,0.00),2),'%') rd4,
		CONCAT(FORMAT(IFNULL(sum(retention_day_5)/sum(add_user)*100,0.00),2),'%') rd5,CONCAT(FORMAT(IFNULL(sum(retention_day_6)/sum(add_user)*100,0.00),2),'%') rd6,
		CONCAT(FORMAT(IFNULL(sum(retention_day_7)/sum(add_user)*100,0.00),2),'%') rd7,CONCAT(FORMAT(IFNULL(sum(retention_day_8)/sum(add_user)*100,0.00),2),'%') rd8,
		CONCAT(FORMAT(IFNULL(sum(retention_day_9)/sum(add_user)*100,0.00),2),'%') rd9,CONCAT(FORMAT(IFNULL(sum(retention_day_10)/sum(add_user)*100,0.00),2),'%') rd10,
		CONCAT(FORMAT(IFNULL(sum(retention_day_11)/sum(add_user)*100,0.00),2),'%') rd11,CONCAT(FORMAT(IFNULL(sum(retention_day_12)/sum(add_user)*100,0.00),2),'%') rd12,
		CONCAT(FORMAT(IFNULL(sum(retention_day_13)/sum(add_user)*100,0.00),2),'%') rd13,CONCAT(FORMAT(IFNULL(sum(retention_day_14)/sum(add_user)*100,0.00),2),'%') rd14,
		CONCAT(FORMAT(IFNULL(sum(retention_day_15)/sum(add_user)*100,0.00),2),'%') rd15,CONCAT(FORMAT(IFNULL(sum(retention_day_16)/sum(add_user)*100,0.00),2),'%') rd16,
		CONCAT(FORMAT(IFNULL(sum(retention_day_17)/sum(add_user)*100,0.00),2),'%') rd17,CONCAT(FORMAT(IFNULL(sum(retention_day_18)/sum(add_user)*100,0.00),2),'%') rd18,
		CONCAT(FORMAT(IFNULL(sum(retention_day_19)/sum(add_user)*100,0.00),2),'%') rd19,CONCAT(FORMAT(IFNULL(sum(retention_day_20)/sum(add_user)*100,0.00),2),'%') rd20,
		CONCAT(FORMAT(IFNULL(sum(retention_day_21)/sum(add_user)*100,0.00),2),'%') rd21,CONCAT(FORMAT(IFNULL(sum(retention_day_22)/sum(add_user)*100,0.00),2),'%') rd22,
		CONCAT(FORMAT(IFNULL(sum(retention_day_23)/sum(add_user)*100,0.00),2),'%') rd23,CONCAT(FORMAT(IFNULL(sum(retention_day_24)/sum(add_user)*100,0.00),2),'%') rd24,
		CONCAT(FORMAT(IFNULL(sum(retention_day_25)/sum(add_user)*100,0.00),2),'%') rd25,CONCAT(FORMAT(IFNULL(sum(retention_day_26)/sum(add_user)*100,0.00),2),'%') rd26,
		CONCAT(FORMAT(IFNULL(sum(retention_day_27)/sum(add_user)*100,0.00),2),'%') rd27,CONCAT(FORMAT(IFNULL(sum(retention_day_28)/sum(add_user)*100,0.00),2),'%') rd28,
		CONCAT(FORMAT(IFNULL(sum(retention_day_29)/sum(add_user)*100,0.00),2),'%') rd29,CONCAT(FORMAT(IFNULL(sum(retention_day_30)/sum(add_user)*100,0.00),2),'%') rd30,
		sum(active_purchase_users) active_purchase_users,sum(add_purchase_users) add_purchase_users,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_1)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd1,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_2)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd2,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_3)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd3,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_4)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd4,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_5)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd5,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_6)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd6,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_7)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd7,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_8)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd8,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_9)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd9,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_10)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd10,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_11)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd11,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_12)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd12,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_13)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd13,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_14)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd14,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_15)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd15,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_16)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd16,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_17)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd17,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_18)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd18,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_19)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd19,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_20)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd20,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_21)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd21,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_22)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd22,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_23)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd23,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_24)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd24,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_25)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd25,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_26)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd26,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_27)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd27,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_28)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd28,
		CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_29)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd29,CONCAT(FORMAT(IFNULL(sum(add_purchase_retention_30)/sum(add_purchase_users)*100,0.00),2),'%') ipaRd30,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_2)+sum(add_revenue_2))/sum(rebateCost)*100,0.00),2),'%') roi2,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_3)+sum(add_revenue_3))/sum(rebateCost)*100,0.00),2),'%') roi3,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_4)+sum(add_revenue_4))/sum(rebateCost)*100,0.00),2),'%') roi4,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_5)+sum(add_revenue_5))/sum(rebateCost)*100,0.00),2),'%') roi5,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_6)+sum(add_revenue_6))/sum(rebateCost)*100,0.00),2),'%') roi6,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_7)+sum(add_revenue_7))/sum(rebateCost)*100,0.00),2),'%') roi7,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_8)+sum(add_revenue_8))/sum(rebateCost)*100,0.00),2),'%') roi8,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_9)+sum(add_revenue_9))/sum(rebateCost)*100,0.00),2),'%') roi9,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_10)+sum(add_revenue_10))/sum(rebateCost)*100,0.00),2),'%') roi10,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_11)+sum(add_revenue_11))/sum(rebateCost)*100,0.00),2),'%') roi11,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_12)+sum(add_revenue_12))/sum(rebateCost)*100,0.00),2),'%') roi12,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_13)+sum(add_revenue_13))/sum(rebateCost)*100,0.00),2),'%') roi13,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_14)+sum(add_revenue_14))/sum(rebateCost)*100,0.00),2),'%') roi14,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_15)+sum(add_revenue_15))/sum(rebateCost)*100,0.00),2),'%') roi15,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_16)+sum(add_revenue_16))/sum(rebateCost)*100,0.00),2),'%') roi16,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_17)+sum(add_revenue_17))/sum(rebateCost)*100,0.00),2),'%') roi17,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_18)+sum(add_revenue_18))/sum(rebateCost)*100,0.00),2),'%') roi18,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_19)+sum(add_revenue_19))/sum(rebateCost)*100,0.00),2),'%') roi19,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_20)+sum(add_revenue_20))/sum(rebateCost)*100,0.00),2),'%') roi20,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_21)+sum(add_revenue_21))/sum(rebateCost)*100,0.00),2),'%') roi21,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_22)+sum(add_revenue_22))/sum(rebateCost)*100,0.00),2),'%') roi22,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_23)+sum(add_revenue_23))/sum(rebateCost)*100,0.00),2),'%') roi23,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_24)+sum(add_revenue_24))/sum(rebateCost)*100,0.00),2),'%') roi24,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_25)+sum(add_revenue_25))/sum(rebateCost)*100,0.00),2),'%') roi25,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_26)+sum(add_revenue_26))/sum(rebateCost)*100,0.00),2),'%') roi26,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_27)+sum(add_revenue_27))/sum(rebateCost)*100,0.00),2),'%') roi27,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_28)+sum(add_revenue_28))/sum(rebateCost)*100,0.00),2),'%') roi28,CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_29)+sum(add_revenue_29))/sum(rebateCost)*100,0.00),2),'%') roi29,
		CONCAT(FORMAT(IFNULL((sum(add_purchase_revenue_30)+sum(add_revenue_30))/sum(rebateCost)*100,0.00),2),'%') roi30,FORMAT(IFNULL(sum(rebateCost)/100/sum(gamepaycount),0.00),2) payCost,
		FORMAT(IFNULL(sum(rebateCost)/100/sum(paycount),0.00),2) firstChargeCost,FORMAT(sum(paycount)/sum(installs),2) firstPaymentRate,sum(paycount) firstPayCount,sum(gamepaycount) paymentTimes,
		FORMAT(IFNULL((sum(add_purchase_revenue_2)/100+sum(add_revenue_2)/100)/sum(add_user),0.00),2) ltv2,FORMAT(IFNULL((sum(add_purchase_revenue_3)/100+sum(add_revenue_3)/100)/sum(add_user),0.00),2) ltv3,
		FORMAT(IFNULL((sum(add_purchase_revenue_4)/100+sum(add_revenue_4)/100)/sum(add_user),0.00),2) ltv4,FORMAT(IFNULL((sum(add_purchase_revenue_5)/100+sum(add_revenue_5)/100)/sum(add_user),0.00),2) ltv5,
		FORMAT(IFNULL((sum(add_purchase_revenue_6)/100+sum(add_revenue_6)/100)/sum(add_user),0.00),2) ltv6,FORMAT(IFNULL((sum(add_purchase_revenue_7)/100+sum(add_revenue_7)/100)/sum(add_user),0.00),2) ltv7,
		FORMAT(IFNULL((sum(add_purchase_revenue_8)/100+sum(add_revenue_8)/100)/sum(add_user),0.00),2) ltv8,FORMAT(IFNULL((sum(add_purchase_revenue_9)/100+sum(add_revenue_9)/100)/sum(add_user),0.00),2) ltv9,
		FORMAT(IFNULL((sum(add_purchase_revenue_10)/100+sum(add_revenue_10)/100)/sum(add_user),0.00),2) ltv10,FORMAT(IFNULL((sum(add_purchase_revenue_11)/100+sum(add_revenue_11)/100)/sum(add_user),0.00),2) ltv11,
		FORMAT(IFNULL((sum(add_purchase_revenue_12)/100+sum(add_revenue_12)/100)/sum(add_user),0.00),2) ltv12,FORMAT(IFNULL((sum(add_purchase_revenue_13)/100+sum(add_revenue_13)/100)/sum(add_user),0.00),2) ltv13,
		FORMAT(IFNULL((sum(add_purchase_revenue_14)/100+sum(add_revenue_14)/100)/sum(add_user),0.00),2) ltv14,FORMAT(IFNULL((sum(add_purchase_revenue_15)/100+sum(add_revenue_15)/100)/sum(add_user),0.00),2) ltv15,
		FORMAT(IFNULL((sum(add_purchase_revenue_16)/100+sum(add_revenue_16)/100)/sum(add_user),0.00),2) ltv16,FORMAT(IFNULL((sum(add_purchase_revenue_17)/100+sum(add_revenue_17)/100)/sum(add_user),0.00),2) ltv17,
		FORMAT(IFNULL((sum(add_purchase_revenue_18)/100+sum(add_revenue_18)/100)/sum(add_user),0.00),2) ltv18,FORMAT(IFNULL((sum(add_purchase_revenue_19)/100+sum(add_revenue_19)/100)/sum(add_user),0.00),2) ltv19,
		FORMAT(IFNULL((sum(add_purchase_revenue_20)/100+sum(add_revenue_20)/100)/sum(add_user),0.00),2) ltv20,FORMAT(IFNULL((sum(add_purchase_revenue_21)/100+sum(add_revenue_21)/100)/sum(add_user),0.00),2) ltv21,
		FORMAT(IFNULL((sum(add_purchase_revenue_22)/100+sum(add_revenue_22)/100)/sum(add_user),0.00),2) ltv22,FORMAT(IFNULL((sum(add_purchase_revenue_23)/100+sum(add_revenue_23)/100)/sum(add_user),0.00),2) ltv23,
		FORMAT(IFNULL((sum(add_purchase_revenue_24)/100+sum(add_revenue_24)/100)/sum(add_user),0.00),2) ltv24,FORMAT(IFNULL((sum(add_purchase_revenue_25)/100+sum(add_revenue_25)/100)/sum(add_user),0.00),2) ltv25,
		FORMAT(IFNULL((sum(add_purchase_revenue_26)/100+sum(add_revenue_26)/100)/sum(add_user),0.00),2) ltv26,FORMAT(IFNULL((sum(add_purchase_revenue_27)/100+sum(add_revenue_27)/100)/sum(add_user),0.00),2) ltv27,
		FORMAT(IFNULL((sum(add_purchase_revenue_28)/100+sum(add_revenue_28)/100)/sum(add_user),0.00),2) ltv28,FORMAT(IFNULL((sum(add_purchase_revenue_29)/100+sum(add_revenue_29)/100)/sum(add_user),0.00),2) ltv29,
		FORMAT(IFNULL((sum(add_purchase_revenue_30)/100+sum(add_revenue_30)/100)/sum(add_user),0.00),2) ltv30
		from ads_creative_reyun_v2 where a_day BETWEEN #{beginDate} AND #{endDate}  
		<if test="appId != null and appId != ''">
			and app_id in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and media in (${media}) 
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account in (${accountId}) 
		</if>
		<if test="putUser != null and putUser != ''">
			and putUser in (${putUser}) 
		</if>
		<if test="artist != null and artist.size > 0">
            AND (
            <foreach collection="artist" index="index" item="item" separator="OR">
                artist LIKE "%"#{item}"%"
            </foreach>
            )
        </if>
		group by app_id
		<if test="group != null and group != ''">
				,${group}
		</if>
		<if test="scope != null and scope != ''">
          <if test="index == 'spend'">
              HAVING IFNULL(ROUND(SUM(rebateCost)/100,2),0.00) <![CDATA[ ${symbol} ]]> #{number}
          </if>
        </if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by day asc ,spend desc
			</otherwise>
		</choose>
	</sql>	
	<!-- 微信小游戏创意ROI报表查询  -->
  	<select id="WxGameCreativeReport" parameterType="com.wbgame.pojo.jettison.param.CreativeReportParam" resultType="com.wbgame.pojo.jettison.vo.MediaCreativeVo">
		<include refid="wx_game_creative_roi_sql"/>
	</select>
	<!-- 微信小游戏创意ROI报表汇总  -->
	<select id="WxGameCreativeReportTotal" parameterType="com.wbgame.pojo.jettison.param.CreativeReportParam"  resultType="com.wbgame.pojo.jettison.vo.MediaCreativeVo">
		SELECT ad_buy_media media,ad_buy_channel channel,app_name appName,tdate day,channel_type channelType,appid as appId,creative_id as creativeId,put_user putUser,artist,account as accountId,
		round(sum(revenue_1)/100,2) ltv1,round(sum(rebate_spend)/100,2) spend,
		sum(reg_user_cnt) addUser,round(sum(iap_revenue_1)/100+sum(revenue_1)/100,2) firstDayIncome,round(sum(iap_revenue_1)/100,2) ipaIncome,
		FORMAT(IFNULL(sum(reg_user_video_revenue)/sum(reg_user_video_cnt)*10,0.00),2) videoEcpm,FORMAT(IFNULL(sum(reg_user_plaque_revenue)/sum(reg_user_plaque_cnt)*10,0.00),2) plaqueEcpm,
		FORMAT(IFNULL(sum(rebate_spend)/100/sum(reg_user_cnt),0.00),2) cpa,FORMAT(IFNULL((sum(iap_revenue_1)/100+sum(revenue_1)/100)/sum(reg_user_cnt),0.00),2) arpu,
		round(sum(reg_user_plaque_cnt)/sum(reg_user_cnt),2) avgPlaqueShowTimes,round(sum(reg_user_video_cnt)/sum(reg_user_cnt),2) avgVideoShowTimes,
		CONCAT(FORMAT(IFNULL(sum(revenue_1)/sum(rebate_spend)*100,0.00),2),'%') roi,CONCAT(FORMAT(IFNULL(sum(iap_revenue_1)/sum(rebate_spend)*100,0.00),2),'%') ipaRoi,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_1)+sum(revenue_1))/sum(rebate_spend)*100,0.00),2),'%') firstDayRoi,FORMAT(sum(rebate_spend)/100/sum(impressions)*1000,2) avgShowSpend,
		CONCAT(FORMAT(IFNULL(sum(installs)/sum(clicks)*100,0.00),2),'%') installRate,CONCAT(FORMAT(IFNULL(sum(clicks)/sum(impressions)*100,0.00),2),'%') clickRate,
		CONCAT(FORMAT(IFNULL(sum(retention_1day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') retentionOneDayRate,CONCAT(FORMAT(IFNULL(sum(retention_2day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd2,
		CONCAT(FORMAT(IFNULL(sum(retention_3day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd3,CONCAT(FORMAT(IFNULL(sum(retention_4day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd4,
		CONCAT(FORMAT(IFNULL(sum(retention_5day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd5,CONCAT(FORMAT(IFNULL(sum(retention_6day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd6,
		CONCAT(FORMAT(IFNULL(sum(retention_7day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd7,CONCAT(FORMAT(IFNULL(sum(retention_8day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd8,
		CONCAT(FORMAT(IFNULL(sum(retention_9day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd9,CONCAT(FORMAT(IFNULL(sum(retention_10day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd10,
		CONCAT(FORMAT(IFNULL(sum(retention_11day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd11,CONCAT(FORMAT(IFNULL(sum(retention_12day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd12,
		CONCAT(FORMAT(IFNULL(sum(retention_13day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd13,CONCAT(FORMAT(IFNULL(sum(retention_14day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd14,
		CONCAT(FORMAT(IFNULL(sum(retention_15day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd15,CONCAT(FORMAT(IFNULL(sum(retention_16day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd16,
		CONCAT(FORMAT(IFNULL(sum(retention_17day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd17,CONCAT(FORMAT(IFNULL(sum(retention_18day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd18,
		CONCAT(FORMAT(IFNULL(sum(retention_19day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd19,CONCAT(FORMAT(IFNULL(sum(retention_20day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd20,
		CONCAT(FORMAT(IFNULL(sum(retention_21day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd21,CONCAT(FORMAT(IFNULL(sum(retention_22day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd22,
		CONCAT(FORMAT(IFNULL(sum(retention_23day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd23,CONCAT(FORMAT(IFNULL(sum(retention_24day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd24,
		CONCAT(FORMAT(IFNULL(sum(retention_25day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd25,CONCAT(FORMAT(IFNULL(sum(retention_26day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd26,
		CONCAT(FORMAT(IFNULL(sum(retention_27day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd27,CONCAT(FORMAT(IFNULL(sum(retention_28day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd28,
		CONCAT(FORMAT(IFNULL(sum(retention_29day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd29,CONCAT(FORMAT(IFNULL(sum(retention_30day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd30,
		sum(iap_active_user_cnt) active_purchase_users,sum(iap_reg_user_cnt) add_purchase_users, 
		CONCAT(FORMAT(IFNULL(sum(iap_retention_1day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd1,CONCAT(FORMAT(IFNULL(sum(iap_retention_2day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd2,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_3day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd3,CONCAT(FORMAT(IFNULL(sum(iap_retention_4day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd4,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_5day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd5,CONCAT(FORMAT(IFNULL(sum(iap_retention_6day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd6,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_7day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd7,CONCAT(FORMAT(IFNULL(sum(iap_retention_8day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd8,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_9day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd9,CONCAT(FORMAT(IFNULL(sum(iap_retention_10day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd10,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_11day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd11,CONCAT(FORMAT(IFNULL(sum(iap_retention_12day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd12,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_13day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd13,CONCAT(FORMAT(IFNULL(sum(iap_retention_14day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd14,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_15day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd15,CONCAT(FORMAT(IFNULL(sum(iap_retention_16day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd16,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_17day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd17,CONCAT(FORMAT(IFNULL(sum(iap_retention_18day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd18,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_19day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd19,CONCAT(FORMAT(IFNULL(sum(iap_retention_20day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd20,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_21day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd21,CONCAT(FORMAT(IFNULL(sum(iap_retention_22day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd22,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_23day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd23,CONCAT(FORMAT(IFNULL(sum(iap_retention_24day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd24,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_25day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd25,CONCAT(FORMAT(IFNULL(sum(iap_retention_26day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd26,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_27day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd27,CONCAT(FORMAT(IFNULL(sum(iap_retention_28day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd28,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_29day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd29,CONCAT(FORMAT(IFNULL(sum(iap_retention_30day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd30,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_2)+sum(revenue_2))/sum(rebate_spend)*100,0.00),2),'%') roi2,CONCAT(FORMAT(IFNULL((sum(iap_revenue_3)+sum(revenue_3))/sum(rebate_spend)*100,0.00),2),'%') roi3,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_4)+sum(revenue_4))/sum(rebate_spend)*100,0.00),2),'%') roi4,CONCAT(FORMAT(IFNULL((sum(iap_revenue_5)+sum(revenue_5))/sum(rebate_spend)*100,0.00),2),'%') roi5,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_6)+sum(revenue_6))/sum(rebate_spend)*100,0.00),2),'%') roi6,CONCAT(FORMAT(IFNULL((sum(iap_revenue_7)+sum(revenue_7))/sum(rebate_spend)*100,0.00),2),'%') roi7,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_8)+sum(revenue_8))/sum(rebate_spend)*100,0.00),2),'%') roi8,CONCAT(FORMAT(IFNULL((sum(iap_revenue_9)+sum(revenue_9))/sum(rebate_spend)*100,0.00),2),'%') roi9,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_10)+sum(revenue_10))/sum(rebate_spend)*100,0.00),2),'%') roi10,CONCAT(FORMAT(IFNULL((sum(iap_revenue_11)+sum(revenue_11))/sum(rebate_spend)*100,0.00),2),'%') roi11,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_12)+sum(revenue_12))/sum(rebate_spend)*100,0.00),2),'%') roi12,CONCAT(FORMAT(IFNULL((sum(iap_revenue_13)+sum(revenue_13))/sum(rebate_spend)*100,0.00),2),'%') roi13,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_14)+sum(revenue_14))/sum(rebate_spend)*100,0.00),2),'%') roi14,CONCAT(FORMAT(IFNULL((sum(iap_revenue_15)+sum(revenue_15))/sum(rebate_spend)*100,0.00),2),'%') roi15,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_16)+sum(revenue_16))/sum(rebate_spend)*100,0.00),2),'%') roi16,CONCAT(FORMAT(IFNULL((sum(iap_revenue_17)+sum(revenue_17))/sum(rebate_spend)*100,0.00),2),'%') roi17,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_18)+sum(revenue_18))/sum(rebate_spend)*100,0.00),2),'%') roi18,CONCAT(FORMAT(IFNULL((sum(iap_revenue_19)+sum(revenue_19))/sum(rebate_spend)*100,0.00),2),'%') roi19,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_20)+sum(revenue_20))/sum(rebate_spend)*100,0.00),2),'%') roi20,CONCAT(FORMAT(IFNULL((sum(iap_revenue_21)+sum(revenue_21))/sum(rebate_spend)*100,0.00),2),'%') roi21,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_22)+sum(revenue_22))/sum(rebate_spend)*100,0.00),2),'%') roi22,CONCAT(FORMAT(IFNULL((sum(iap_revenue_23)+sum(revenue_23))/sum(rebate_spend)*100,0.00),2),'%') roi23,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_24)+sum(revenue_24))/sum(rebate_spend)*100,0.00),2),'%') roi24,CONCAT(FORMAT(IFNULL((sum(iap_revenue_25)+sum(revenue_25))/sum(rebate_spend)*100,0.00),2),'%') roi25,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_26)+sum(revenue_26))/sum(rebate_spend)*100,0.00),2),'%') roi26,CONCAT(FORMAT(IFNULL((sum(iap_revenue_27)+sum(revenue_27))/sum(rebate_spend)*100,0.00),2),'%') roi27,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_28)+sum(revenue_28))/sum(rebate_spend)*100,0.00),2),'%') roi28,CONCAT(FORMAT(IFNULL((sum(iap_revenue_29)+sum(revenue_29))/sum(rebate_spend)*100,0.00),2),'%') roi29,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_30)+sum(revenue_30))/sum(rebate_spend)*100,0.00),2),'%') roi30,
		FORMAT(IFNULL(sum(rebate_spend)/100/sum(game_pay_count),0.00),2) payCost,FORMAT(IFNULL(sum(rebate_spend)/100/sum(pay_count),0.00),2) firstChargeCost,
		FORMAT(sum(pay_count)/sum(installs),2) firstPaymentRate,sum(pay_count) firstPayCount,sum(game_pay_count) paymentTimes,
		FORMAT(IFNULL((sum(iap_revenue_2)/100+sum(revenue_2)/100)/sum(reg_user_cnt),0.00),2) ltv2,FORMAT(IFNULL((sum(iap_revenue_3)/100+sum(revenue_3)/100)/sum(reg_user_cnt),0.00),2) ltv3,
		FORMAT(IFNULL((sum(iap_revenue_4)/100+sum(revenue_4)/100)/sum(reg_user_cnt),0.00),2) ltv4,FORMAT(IFNULL((sum(iap_revenue_5)/100+sum(revenue_5)/100)/sum(reg_user_cnt),0.00),2) ltv5,
		FORMAT(IFNULL((sum(iap_revenue_6)/100+sum(revenue_6)/100)/sum(reg_user_cnt),0.00),2) ltv6,FORMAT(IFNULL((sum(iap_revenue_7)/100+sum(revenue_7)/100)/sum(reg_user_cnt),0.00),2) ltv7,
		FORMAT(IFNULL((sum(iap_revenue_8)/100+sum(revenue_8)/100)/sum(reg_user_cnt),0.00),2) ltv8,FORMAT(IFNULL((sum(iap_revenue_9)/100+sum(revenue_9)/100)/sum(reg_user_cnt),0.00),2) ltv9,
		FORMAT(IFNULL((sum(iap_revenue_10)/100+sum(revenue_10)/100)/sum(reg_user_cnt),0.00),2) ltv10,FORMAT(IFNULL((sum(iap_revenue_11)/100+sum(revenue_11)/100)/sum(reg_user_cnt),0.00),2) ltv11,
		FORMAT(IFNULL((sum(iap_revenue_12)/100+sum(revenue_12)/100)/sum(reg_user_cnt),0.00),2) ltv12,FORMAT(IFNULL((sum(iap_revenue_13)/100+sum(revenue_13)/100)/sum(reg_user_cnt),0.00),2) ltv13,
		FORMAT(IFNULL((sum(iap_revenue_14)/100+sum(revenue_14)/100)/sum(reg_user_cnt),0.00),2) ltv14,FORMAT(IFNULL((sum(iap_revenue_15)/100+sum(revenue_15)/100)/sum(reg_user_cnt),0.00),2) ltv15,
		FORMAT(IFNULL((sum(iap_revenue_16)/100+sum(revenue_16)/100)/sum(reg_user_cnt),0.00),2) ltv16,FORMAT(IFNULL((sum(iap_revenue_17)/100+sum(revenue_17)/100)/sum(reg_user_cnt),0.00),2) ltv17,
		FORMAT(IFNULL((sum(iap_revenue_18)/100+sum(revenue_18)/100)/sum(reg_user_cnt),0.00),2) ltv18,FORMAT(IFNULL((sum(iap_revenue_19)/100+sum(revenue_19)/100)/sum(reg_user_cnt),0.00),2) ltv19,
		FORMAT(IFNULL((sum(iap_revenue_20)/100+sum(revenue_20)/100)/sum(reg_user_cnt),0.00),2) ltv20,FORMAT(IFNULL((sum(iap_revenue_21)/100+sum(revenue_21)/100)/sum(reg_user_cnt),0.00),2) ltv21,
		FORMAT(IFNULL((sum(iap_revenue_22)/100+sum(revenue_22)/100)/sum(reg_user_cnt),0.00),2) ltv22,FORMAT(IFNULL((sum(iap_revenue_23)/100+sum(revenue_23)/100)/sum(reg_user_cnt),0.00),2) ltv23,
		FORMAT(IFNULL((sum(iap_revenue_24)/100+sum(revenue_24)/100)/sum(reg_user_cnt),0.00),2) ltv24,FORMAT(IFNULL((sum(iap_revenue_25)/100+sum(revenue_25)/100)/sum(reg_user_cnt),0.00),2) ltv25,
		FORMAT(IFNULL((sum(iap_revenue_26)/100+sum(revenue_26)/100)/sum(reg_user_cnt),0.00),2) ltv26,FORMAT(IFNULL((sum(iap_revenue_27)/100+sum(revenue_27)/100)/sum(reg_user_cnt),0.00),2) ltv27,
		FORMAT(IFNULL((sum(iap_revenue_28)/100+sum(revenue_28)/100)/sum(reg_user_cnt),0.00),2) ltv28,FORMAT(IFNULL((sum(iap_revenue_29)/100+sum(revenue_29)/100)/sum(reg_user_cnt),0.00),2) ltv29,
		FORMAT(IFNULL((sum(iap_revenue_30)/100+sum(revenue_30)/100)/sum(reg_user_cnt),0.00),2) ltv30
		from new_ads_creative_wechat_roi_daily where tdate BETWEEN #{beginDate} AND #{endDate}  
		<if test="appId != null and appId != ''">
			and appid in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and ad_buy_media in (${media}) 
		</if>
		<if test="channelType != null and channelType != ''">
			and channel_type in (${channelType}) 
		</if>
		<if test="channel != null and channel != ''">
			and ad_buy_channel in (${channel}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account in (${accountId}) 
		</if>
		<if test="putUser != null and putUser != ''">
			and put_user in (${putUser}) 
		</if>
		<if test="artist != null and artist.size > 0">
            AND (
            <foreach collection="artist" index="index" item="item" separator="OR">
                artist LIKE "%"#{item}"%"
            </foreach>
            )
        </if>
        <if test="creativeId != null and creativeId != ''">
			and creative_id = #{creativeId}  
		</if>
		group by appid
		<if test="group != null and group != ''">
			,${group}
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by day asc ,spend desc
			</otherwise>
		</choose>
		union all
		SELECT ad_buy_media media,ad_buy_channel channel,app_name appName,tdate day,channel_type channelType,appid as appId,creative_id as creativeId,put_user putUser,artist,account as accountId,
		round(sum(revenue_1)/100,2) ltv1,round(sum(rebate_spend)/100,2) spend,
		sum(reg_user_cnt) addUser,round(sum(iap_revenue_1)/100+sum(revenue_1)/100,2) firstDayIncome,round(sum(iap_revenue_1)/100,2) ipaIncome,
		FORMAT(IFNULL(sum(reg_user_video_revenue)/sum(reg_user_video_cnt)*10,0.00),2) videoEcpm,FORMAT(IFNULL(sum(reg_user_plaque_revenue)/sum(reg_user_plaque_cnt)*10,0.00),2) plaqueEcpm,
		FORMAT(IFNULL(sum(rebate_spend)/100/sum(reg_user_cnt),0.00),2) cpa,FORMAT(IFNULL((sum(iap_revenue_1)/100+sum(revenue_1)/100)/sum(reg_user_cnt),0.00),2) arpu,
		round(sum(reg_user_plaque_cnt)/sum(reg_user_cnt),2) avgPlaqueShowTimes,round(sum(reg_user_video_cnt)/sum(reg_user_cnt),2) avgVideoShowTimes,
		CONCAT(FORMAT(IFNULL(sum(revenue_1)/sum(rebate_spend)*100,0.00),2),'%') roi,CONCAT(FORMAT(IFNULL(sum(iap_revenue_1)/sum(rebate_spend)*100,0.00),2),'%') ipaRoi,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_1)+sum(revenue_1))/sum(rebate_spend)*100,0.00),2),'%') firstDayRoi,FORMAT(sum(rebate_spend)/100/sum(impressions)*1000,2) avgShowSpend,
		CONCAT(FORMAT(IFNULL(sum(installs)/sum(clicks)*100,0.00),2),'%') installRate,CONCAT(FORMAT(IFNULL(sum(clicks)/sum(impressions)*100,0.00),2),'%') clickRate,
		CONCAT(FORMAT(IFNULL(sum(retention_1day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') retentionOneDayRate,CONCAT(FORMAT(IFNULL(sum(retention_2day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd2,
		CONCAT(FORMAT(IFNULL(sum(retention_3day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd3,CONCAT(FORMAT(IFNULL(sum(retention_4day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd4,
		CONCAT(FORMAT(IFNULL(sum(retention_5day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd5,CONCAT(FORMAT(IFNULL(sum(retention_6day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd6,
		CONCAT(FORMAT(IFNULL(sum(retention_7day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd7,CONCAT(FORMAT(IFNULL(sum(retention_8day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd8,
		CONCAT(FORMAT(IFNULL(sum(retention_9day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd9,CONCAT(FORMAT(IFNULL(sum(retention_10day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd10,
		CONCAT(FORMAT(IFNULL(sum(retention_11day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd11,CONCAT(FORMAT(IFNULL(sum(retention_12day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd12,
		CONCAT(FORMAT(IFNULL(sum(retention_13day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd13,CONCAT(FORMAT(IFNULL(sum(retention_14day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd14,
		CONCAT(FORMAT(IFNULL(sum(retention_15day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd15,CONCAT(FORMAT(IFNULL(sum(retention_16day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd16,
		CONCAT(FORMAT(IFNULL(sum(retention_17day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd17,CONCAT(FORMAT(IFNULL(sum(retention_18day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd18,
		CONCAT(FORMAT(IFNULL(sum(retention_19day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd19,CONCAT(FORMAT(IFNULL(sum(retention_20day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd20,
		CONCAT(FORMAT(IFNULL(sum(retention_21day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd21,CONCAT(FORMAT(IFNULL(sum(retention_22day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd22,
		CONCAT(FORMAT(IFNULL(sum(retention_23day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd23,CONCAT(FORMAT(IFNULL(sum(retention_24day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd24,
		CONCAT(FORMAT(IFNULL(sum(retention_25day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd25,CONCAT(FORMAT(IFNULL(sum(retention_26day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd26,
		CONCAT(FORMAT(IFNULL(sum(retention_27day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd27,CONCAT(FORMAT(IFNULL(sum(retention_28day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd28,
		CONCAT(FORMAT(IFNULL(sum(retention_29day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd29,CONCAT(FORMAT(IFNULL(sum(retention_30day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd30,
		sum(iap_active_user_cnt) active_purchase_users,sum(iap_reg_user_cnt) add_purchase_users, 
		CONCAT(FORMAT(IFNULL(sum(iap_retention_1day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd1,CONCAT(FORMAT(IFNULL(sum(iap_retention_2day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd2,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_3day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd3,CONCAT(FORMAT(IFNULL(sum(iap_retention_4day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd4,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_5day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd5,CONCAT(FORMAT(IFNULL(sum(iap_retention_6day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd6,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_7day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd7,CONCAT(FORMAT(IFNULL(sum(iap_retention_8day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd8,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_9day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd9,CONCAT(FORMAT(IFNULL(sum(iap_retention_10day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd10,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_11day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd11,CONCAT(FORMAT(IFNULL(sum(iap_retention_12day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd12,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_13day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd13,CONCAT(FORMAT(IFNULL(sum(iap_retention_14day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd14,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_15day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd15,CONCAT(FORMAT(IFNULL(sum(iap_retention_16day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd16,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_17day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd17,CONCAT(FORMAT(IFNULL(sum(iap_retention_18day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd18,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_19day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd19,CONCAT(FORMAT(IFNULL(sum(iap_retention_20day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd20,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_21day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd21,CONCAT(FORMAT(IFNULL(sum(iap_retention_22day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd22,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_23day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd23,CONCAT(FORMAT(IFNULL(sum(iap_retention_24day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd24,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_25day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd25,CONCAT(FORMAT(IFNULL(sum(iap_retention_26day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd26,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_27day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd27,CONCAT(FORMAT(IFNULL(sum(iap_retention_28day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd28,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_29day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd29,CONCAT(FORMAT(IFNULL(sum(iap_retention_30day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd30,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_2)+sum(revenue_2))/sum(rebate_spend)*100,0.00),2),'%') roi2,CONCAT(FORMAT(IFNULL((sum(iap_revenue_3)+sum(revenue_3))/sum(rebate_spend)*100,0.00),2),'%') roi3,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_4)+sum(revenue_4))/sum(rebate_spend)*100,0.00),2),'%') roi4,CONCAT(FORMAT(IFNULL((sum(iap_revenue_5)+sum(revenue_5))/sum(rebate_spend)*100,0.00),2),'%') roi5,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_6)+sum(revenue_6))/sum(rebate_spend)*100,0.00),2),'%') roi6,CONCAT(FORMAT(IFNULL((sum(iap_revenue_7)+sum(revenue_7))/sum(rebate_spend)*100,0.00),2),'%') roi7,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_8)+sum(revenue_8))/sum(rebate_spend)*100,0.00),2),'%') roi8,CONCAT(FORMAT(IFNULL((sum(iap_revenue_9)+sum(revenue_9))/sum(rebate_spend)*100,0.00),2),'%') roi9,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_10)+sum(revenue_10))/sum(rebate_spend)*100,0.00),2),'%') roi10,CONCAT(FORMAT(IFNULL((sum(iap_revenue_11)+sum(revenue_11))/sum(rebate_spend)*100,0.00),2),'%') roi11,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_12)+sum(revenue_12))/sum(rebate_spend)*100,0.00),2),'%') roi12,CONCAT(FORMAT(IFNULL((sum(iap_revenue_13)+sum(revenue_13))/sum(rebate_spend)*100,0.00),2),'%') roi13,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_14)+sum(revenue_14))/sum(rebate_spend)*100,0.00),2),'%') roi14,CONCAT(FORMAT(IFNULL((sum(iap_revenue_15)+sum(revenue_15))/sum(rebate_spend)*100,0.00),2),'%') roi15,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_16)+sum(revenue_16))/sum(rebate_spend)*100,0.00),2),'%') roi16,CONCAT(FORMAT(IFNULL((sum(iap_revenue_17)+sum(revenue_17))/sum(rebate_spend)*100,0.00),2),'%') roi17,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_18)+sum(revenue_18))/sum(rebate_spend)*100,0.00),2),'%') roi18,CONCAT(FORMAT(IFNULL((sum(iap_revenue_19)+sum(revenue_19))/sum(rebate_spend)*100,0.00),2),'%') roi19,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_20)+sum(revenue_20))/sum(rebate_spend)*100,0.00),2),'%') roi20,CONCAT(FORMAT(IFNULL((sum(iap_revenue_21)+sum(revenue_21))/sum(rebate_spend)*100,0.00),2),'%') roi21,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_22)+sum(revenue_22))/sum(rebate_spend)*100,0.00),2),'%') roi22,CONCAT(FORMAT(IFNULL((sum(iap_revenue_23)+sum(revenue_23))/sum(rebate_spend)*100,0.00),2),'%') roi23,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_24)+sum(revenue_24))/sum(rebate_spend)*100,0.00),2),'%') roi24,CONCAT(FORMAT(IFNULL((sum(iap_revenue_25)+sum(revenue_25))/sum(rebate_spend)*100,0.00),2),'%') roi25,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_26)+sum(revenue_26))/sum(rebate_spend)*100,0.00),2),'%') roi26,CONCAT(FORMAT(IFNULL((sum(iap_revenue_27)+sum(revenue_27))/sum(rebate_spend)*100,0.00),2),'%') roi27,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_28)+sum(revenue_28))/sum(rebate_spend)*100,0.00),2),'%') roi28,CONCAT(FORMAT(IFNULL((sum(iap_revenue_29)+sum(revenue_29))/sum(rebate_spend)*100,0.00),2),'%') roi29,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_30)+sum(revenue_30))/sum(rebate_spend)*100,0.00),2),'%') roi30,
		FORMAT(IFNULL(sum(rebate_spend)/100/sum(game_pay_count),0.00),2) payCost,FORMAT(IFNULL(sum(rebate_spend)/100/sum(pay_count),0.00),2) firstChargeCost,
		FORMAT(sum(pay_count)/sum(installs),2) firstPaymentRate,sum(pay_count) firstPayCount,sum(game_pay_count) paymentTimes,
		FORMAT(IFNULL((sum(iap_revenue_2)/100+sum(revenue_2)/100)/sum(reg_user_cnt),0.00),2) ltv2,FORMAT(IFNULL((sum(iap_revenue_3)/100+sum(revenue_3)/100)/sum(reg_user_cnt),0.00),2) ltv3,
		FORMAT(IFNULL((sum(iap_revenue_4)/100+sum(revenue_4)/100)/sum(reg_user_cnt),0.00),2) ltv4,FORMAT(IFNULL((sum(iap_revenue_5)/100+sum(revenue_5)/100)/sum(reg_user_cnt),0.00),2) ltv5,
		FORMAT(IFNULL((sum(iap_revenue_6)/100+sum(revenue_6)/100)/sum(reg_user_cnt),0.00),2) ltv6,FORMAT(IFNULL((sum(iap_revenue_7)/100+sum(revenue_7)/100)/sum(reg_user_cnt),0.00),2) ltv7,
		FORMAT(IFNULL((sum(iap_revenue_8)/100+sum(revenue_8)/100)/sum(reg_user_cnt),0.00),2) ltv8,FORMAT(IFNULL((sum(iap_revenue_9)/100+sum(revenue_9)/100)/sum(reg_user_cnt),0.00),2) ltv9,
		FORMAT(IFNULL((sum(iap_revenue_10)/100+sum(revenue_10)/100)/sum(reg_user_cnt),0.00),2) ltv10,FORMAT(IFNULL((sum(iap_revenue_11)/100+sum(revenue_11)/100)/sum(reg_user_cnt),0.00),2) ltv11,
		FORMAT(IFNULL((sum(iap_revenue_12)/100+sum(revenue_12)/100)/sum(reg_user_cnt),0.00),2) ltv12,FORMAT(IFNULL((sum(iap_revenue_13)/100+sum(revenue_13)/100)/sum(reg_user_cnt),0.00),2) ltv13,
		FORMAT(IFNULL((sum(iap_revenue_14)/100+sum(revenue_14)/100)/sum(reg_user_cnt),0.00),2) ltv14,FORMAT(IFNULL((sum(iap_revenue_15)/100+sum(revenue_15)/100)/sum(reg_user_cnt),0.00),2) ltv15,
		FORMAT(IFNULL((sum(iap_revenue_16)/100+sum(revenue_16)/100)/sum(reg_user_cnt),0.00),2) ltv16,FORMAT(IFNULL((sum(iap_revenue_17)/100+sum(revenue_17)/100)/sum(reg_user_cnt),0.00),2) ltv17,
		FORMAT(IFNULL((sum(iap_revenue_18)/100+sum(revenue_18)/100)/sum(reg_user_cnt),0.00),2) ltv18,FORMAT(IFNULL((sum(iap_revenue_19)/100+sum(revenue_19)/100)/sum(reg_user_cnt),0.00),2) ltv19,
		FORMAT(IFNULL((sum(iap_revenue_20)/100+sum(revenue_20)/100)/sum(reg_user_cnt),0.00),2) ltv20,FORMAT(IFNULL((sum(iap_revenue_21)/100+sum(revenue_21)/100)/sum(reg_user_cnt),0.00),2) ltv21,
		FORMAT(IFNULL((sum(iap_revenue_22)/100+sum(revenue_22)/100)/sum(reg_user_cnt),0.00),2) ltv22,FORMAT(IFNULL((sum(iap_revenue_23)/100+sum(revenue_23)/100)/sum(reg_user_cnt),0.00),2) ltv23,
		FORMAT(IFNULL((sum(iap_revenue_24)/100+sum(revenue_24)/100)/sum(reg_user_cnt),0.00),2) ltv24,FORMAT(IFNULL((sum(iap_revenue_25)/100+sum(revenue_25)/100)/sum(reg_user_cnt),0.00),2) ltv25,
		FORMAT(IFNULL((sum(iap_revenue_26)/100+sum(revenue_26)/100)/sum(reg_user_cnt),0.00),2) ltv26,FORMAT(IFNULL((sum(iap_revenue_27)/100+sum(revenue_27)/100)/sum(reg_user_cnt),0.00),2) ltv27,
		FORMAT(IFNULL((sum(iap_revenue_28)/100+sum(revenue_28)/100)/sum(reg_user_cnt),0.00),2) ltv28,FORMAT(IFNULL((sum(iap_revenue_29)/100+sum(revenue_29)/100)/sum(reg_user_cnt),0.00),2) ltv29,
		FORMAT(IFNULL((sum(iap_revenue_30)/100+sum(revenue_30)/100)/sum(reg_user_cnt),0.00),2) ltv30
		from new_ads_material_wechat_roi_daily where tdate BETWEEN #{beginDate} AND #{endDate}  
		<if test="appId != null and appId != ''">
			and appid in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and ad_buy_media in (${media}) 
		</if>
		<if test="channelType != null and channelType != ''">
			and channel_type in (${channelType}) 
		</if>
		<if test="channel != null and channel != ''">
			and ad_buy_channel in (${channel}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account in (${accountId}) 
		</if>
		<if test="putUser != null and putUser != ''">
			and put_user in (${putUser}) 
		</if>
		<if test="artist != null and artist.size > 0">
            AND (
            <foreach collection="artist" index="index" item="item" separator="OR">
                artist LIKE "%"#{item}"%"
            </foreach>
            )
        </if>
        <if test="creativeId != null and creativeId != ''">
			and creative_id = #{creativeId}  
		</if>
	</select>
	
	<sql id="wx_game_creative_roi_sql">
		SELECT ad_buy_media media,ad_buy_channel channel,app_name appName,tdate day,channel_type channelType,appid as appId,creative_id as creativeId,put_user putUser,artist,account as accountId,
		round(sum(revenue_1)/100,2) ltv1,round(sum(rebate_spend)/100,2) spend,
		sum(reg_user_cnt) addUser,round(sum(iap_revenue_1)/100+sum(revenue_1)/100,2) firstDayIncome,round(sum(iap_revenue_1)/100,2) ipaIncome,
		FORMAT(IFNULL(sum(reg_user_video_revenue)/sum(reg_user_video_cnt)*10,0.00),2) videoEcpm,FORMAT(IFNULL(sum(reg_user_plaque_revenue)/sum(reg_user_plaque_cnt)*10,0.00),2) plaqueEcpm,
		FORMAT(IFNULL(sum(rebate_spend)/100/sum(reg_user_cnt),0.00),2) cpa,FORMAT(IFNULL((sum(iap_revenue_1)/100+sum(revenue_1)/100)/sum(reg_user_cnt),0.00),2) arpu,
		round(sum(reg_user_plaque_cnt)/sum(reg_user_cnt),2) avgPlaqueShowTimes,round(sum(reg_user_video_cnt)/sum(reg_user_cnt),2) avgVideoShowTimes,
		CONCAT(FORMAT(IFNULL(sum(revenue_1)/sum(rebate_spend)*100,0.00),2),'%') roi,CONCAT(FORMAT(IFNULL(sum(iap_revenue_1)/sum(rebate_spend)*100,0.00),2),'%') ipaRoi,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_1)+sum(revenue_1))/sum(rebate_spend)*100,0.00),2),'%') firstDayRoi,FORMAT(sum(rebate_spend)/100/sum(impressions)*1000,2) avgShowSpend,
		CONCAT(FORMAT(IFNULL(sum(installs)/sum(clicks)*100,0.00),2),'%') installRate,CONCAT(FORMAT(IFNULL(sum(clicks)/sum(impressions)*100,0.00),2),'%') clickRate,
		CONCAT(FORMAT(IFNULL(sum(retention_1day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') retentionOneDayRate,CONCAT(FORMAT(IFNULL(sum(retention_2day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd2,
		CONCAT(FORMAT(IFNULL(sum(retention_3day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd3,CONCAT(FORMAT(IFNULL(sum(retention_4day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd4,
		CONCAT(FORMAT(IFNULL(sum(retention_5day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd5,CONCAT(FORMAT(IFNULL(sum(retention_6day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd6,
		CONCAT(FORMAT(IFNULL(sum(retention_7day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd7,CONCAT(FORMAT(IFNULL(sum(retention_8day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd8,
		CONCAT(FORMAT(IFNULL(sum(retention_9day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd9,CONCAT(FORMAT(IFNULL(sum(retention_10day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd10,
		CONCAT(FORMAT(IFNULL(sum(retention_11day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd11,CONCAT(FORMAT(IFNULL(sum(retention_12day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd12,
		CONCAT(FORMAT(IFNULL(sum(retention_13day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd13,CONCAT(FORMAT(IFNULL(sum(retention_14day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd14,
		CONCAT(FORMAT(IFNULL(sum(retention_15day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd15,CONCAT(FORMAT(IFNULL(sum(retention_16day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd16,
		CONCAT(FORMAT(IFNULL(sum(retention_17day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd17,CONCAT(FORMAT(IFNULL(sum(retention_18day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd18,
		CONCAT(FORMAT(IFNULL(sum(retention_19day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd19,CONCAT(FORMAT(IFNULL(sum(retention_20day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd20,
		CONCAT(FORMAT(IFNULL(sum(retention_21day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd21,CONCAT(FORMAT(IFNULL(sum(retention_22day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd22,
		CONCAT(FORMAT(IFNULL(sum(retention_23day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd23,CONCAT(FORMAT(IFNULL(sum(retention_24day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd24,
		CONCAT(FORMAT(IFNULL(sum(retention_25day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd25,CONCAT(FORMAT(IFNULL(sum(retention_26day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd26,
		CONCAT(FORMAT(IFNULL(sum(retention_27day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd27,CONCAT(FORMAT(IFNULL(sum(retention_28day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd28,
		CONCAT(FORMAT(IFNULL(sum(retention_29day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd29,CONCAT(FORMAT(IFNULL(sum(retention_30day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd30,
		sum(iap_active_user_cnt) active_purchase_users,sum(iap_reg_user_cnt) add_purchase_users, 
		CONCAT(FORMAT(IFNULL(sum(iap_retention_1day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd1,CONCAT(FORMAT(IFNULL(sum(iap_retention_2day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd2,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_3day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd3,CONCAT(FORMAT(IFNULL(sum(iap_retention_4day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd4,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_5day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd5,CONCAT(FORMAT(IFNULL(sum(iap_retention_6day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd6,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_7day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd7,CONCAT(FORMAT(IFNULL(sum(iap_retention_8day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd8,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_9day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd9,CONCAT(FORMAT(IFNULL(sum(iap_retention_10day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd10,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_11day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd11,CONCAT(FORMAT(IFNULL(sum(iap_retention_12day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd12,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_13day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd13,CONCAT(FORMAT(IFNULL(sum(iap_retention_14day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd14,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_15day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd15,CONCAT(FORMAT(IFNULL(sum(iap_retention_16day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd16,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_17day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd17,CONCAT(FORMAT(IFNULL(sum(iap_retention_18day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd18,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_19day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd19,CONCAT(FORMAT(IFNULL(sum(iap_retention_20day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd20,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_21day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd21,CONCAT(FORMAT(IFNULL(sum(iap_retention_22day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd22,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_23day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd23,CONCAT(FORMAT(IFNULL(sum(iap_retention_24day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd24,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_25day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd25,CONCAT(FORMAT(IFNULL(sum(iap_retention_26day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd26,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_27day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd27,CONCAT(FORMAT(IFNULL(sum(iap_retention_28day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd28,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_29day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd29,CONCAT(FORMAT(IFNULL(sum(iap_retention_30day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd30,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_2)+sum(revenue_2))/sum(rebate_spend)*100,0.00),2),'%') roi2,CONCAT(FORMAT(IFNULL((sum(iap_revenue_3)+sum(revenue_3))/sum(rebate_spend)*100,0.00),2),'%') roi3,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_4)+sum(revenue_4))/sum(rebate_spend)*100,0.00),2),'%') roi4,CONCAT(FORMAT(IFNULL((sum(iap_revenue_5)+sum(revenue_5))/sum(rebate_spend)*100,0.00),2),'%') roi5,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_6)+sum(revenue_6))/sum(rebate_spend)*100,0.00),2),'%') roi6,CONCAT(FORMAT(IFNULL((sum(iap_revenue_7)+sum(revenue_7))/sum(rebate_spend)*100,0.00),2),'%') roi7,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_8)+sum(revenue_8))/sum(rebate_spend)*100,0.00),2),'%') roi8,CONCAT(FORMAT(IFNULL((sum(iap_revenue_9)+sum(revenue_9))/sum(rebate_spend)*100,0.00),2),'%') roi9,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_10)+sum(revenue_10))/sum(rebate_spend)*100,0.00),2),'%') roi10,CONCAT(FORMAT(IFNULL((sum(iap_revenue_11)+sum(revenue_11))/sum(rebate_spend)*100,0.00),2),'%') roi11,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_12)+sum(revenue_12))/sum(rebate_spend)*100,0.00),2),'%') roi12,CONCAT(FORMAT(IFNULL((sum(iap_revenue_13)+sum(revenue_13))/sum(rebate_spend)*100,0.00),2),'%') roi13,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_14)+sum(revenue_14))/sum(rebate_spend)*100,0.00),2),'%') roi14,CONCAT(FORMAT(IFNULL((sum(iap_revenue_15)+sum(revenue_15))/sum(rebate_spend)*100,0.00),2),'%') roi15,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_16)+sum(revenue_16))/sum(rebate_spend)*100,0.00),2),'%') roi16,CONCAT(FORMAT(IFNULL((sum(iap_revenue_17)+sum(revenue_17))/sum(rebate_spend)*100,0.00),2),'%') roi17,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_18)+sum(revenue_18))/sum(rebate_spend)*100,0.00),2),'%') roi18,CONCAT(FORMAT(IFNULL((sum(iap_revenue_19)+sum(revenue_19))/sum(rebate_spend)*100,0.00),2),'%') roi19,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_20)+sum(revenue_20))/sum(rebate_spend)*100,0.00),2),'%') roi20,CONCAT(FORMAT(IFNULL((sum(iap_revenue_21)+sum(revenue_21))/sum(rebate_spend)*100,0.00),2),'%') roi21,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_22)+sum(revenue_22))/sum(rebate_spend)*100,0.00),2),'%') roi22,CONCAT(FORMAT(IFNULL((sum(iap_revenue_23)+sum(revenue_23))/sum(rebate_spend)*100,0.00),2),'%') roi23,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_24)+sum(revenue_24))/sum(rebate_spend)*100,0.00),2),'%') roi24,CONCAT(FORMAT(IFNULL((sum(iap_revenue_25)+sum(revenue_25))/sum(rebate_spend)*100,0.00),2),'%') roi25,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_26)+sum(revenue_26))/sum(rebate_spend)*100,0.00),2),'%') roi26,CONCAT(FORMAT(IFNULL((sum(iap_revenue_27)+sum(revenue_27))/sum(rebate_spend)*100,0.00),2),'%') roi27,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_28)+sum(revenue_28))/sum(rebate_spend)*100,0.00),2),'%') roi28,CONCAT(FORMAT(IFNULL((sum(iap_revenue_29)+sum(revenue_29))/sum(rebate_spend)*100,0.00),2),'%') roi29,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_30)+sum(revenue_30))/sum(rebate_spend)*100,0.00),2),'%') roi30,
		FORMAT(IFNULL(sum(rebate_spend)/100/sum(game_pay_count),0.00),2) payCost,FORMAT(IFNULL(sum(rebate_spend)/100/sum(pay_count),0.00),2) firstChargeCost,
		FORMAT(sum(pay_count)/sum(installs),2) firstPaymentRate,sum(pay_count) firstPayCount,sum(game_pay_count) paymentTimes,
		FORMAT(IFNULL((sum(iap_revenue_2)/100+sum(revenue_2)/100)/sum(reg_user_cnt),0.00),2) ltv2,FORMAT(IFNULL((sum(iap_revenue_3)/100+sum(revenue_3)/100)/sum(reg_user_cnt),0.00),2) ltv3,
		FORMAT(IFNULL((sum(iap_revenue_4)/100+sum(revenue_4)/100)/sum(reg_user_cnt),0.00),2) ltv4,FORMAT(IFNULL((sum(iap_revenue_5)/100+sum(revenue_5)/100)/sum(reg_user_cnt),0.00),2) ltv5,
		FORMAT(IFNULL((sum(iap_revenue_6)/100+sum(revenue_6)/100)/sum(reg_user_cnt),0.00),2) ltv6,FORMAT(IFNULL((sum(iap_revenue_7)/100+sum(revenue_7)/100)/sum(reg_user_cnt),0.00),2) ltv7,
		FORMAT(IFNULL((sum(iap_revenue_8)/100+sum(revenue_8)/100)/sum(reg_user_cnt),0.00),2) ltv8,FORMAT(IFNULL((sum(iap_revenue_9)/100+sum(revenue_9)/100)/sum(reg_user_cnt),0.00),2) ltv9,
		FORMAT(IFNULL((sum(iap_revenue_10)/100+sum(revenue_10)/100)/sum(reg_user_cnt),0.00),2) ltv10,FORMAT(IFNULL((sum(iap_revenue_11)/100+sum(revenue_11)/100)/sum(reg_user_cnt),0.00),2) ltv11,
		FORMAT(IFNULL((sum(iap_revenue_12)/100+sum(revenue_12)/100)/sum(reg_user_cnt),0.00),2) ltv12,FORMAT(IFNULL((sum(iap_revenue_13)/100+sum(revenue_13)/100)/sum(reg_user_cnt),0.00),2) ltv13,
		FORMAT(IFNULL((sum(iap_revenue_14)/100+sum(revenue_14)/100)/sum(reg_user_cnt),0.00),2) ltv14,FORMAT(IFNULL((sum(iap_revenue_15)/100+sum(revenue_15)/100)/sum(reg_user_cnt),0.00),2) ltv15,
		FORMAT(IFNULL((sum(iap_revenue_16)/100+sum(revenue_16)/100)/sum(reg_user_cnt),0.00),2) ltv16,FORMAT(IFNULL((sum(iap_revenue_17)/100+sum(revenue_17)/100)/sum(reg_user_cnt),0.00),2) ltv17,
		FORMAT(IFNULL((sum(iap_revenue_18)/100+sum(revenue_18)/100)/sum(reg_user_cnt),0.00),2) ltv18,FORMAT(IFNULL((sum(iap_revenue_19)/100+sum(revenue_19)/100)/sum(reg_user_cnt),0.00),2) ltv19,
		FORMAT(IFNULL((sum(iap_revenue_20)/100+sum(revenue_20)/100)/sum(reg_user_cnt),0.00),2) ltv20,FORMAT(IFNULL((sum(iap_revenue_21)/100+sum(revenue_21)/100)/sum(reg_user_cnt),0.00),2) ltv21,
		FORMAT(IFNULL((sum(iap_revenue_22)/100+sum(revenue_22)/100)/sum(reg_user_cnt),0.00),2) ltv22,FORMAT(IFNULL((sum(iap_revenue_23)/100+sum(revenue_23)/100)/sum(reg_user_cnt),0.00),2) ltv23,
		FORMAT(IFNULL((sum(iap_revenue_24)/100+sum(revenue_24)/100)/sum(reg_user_cnt),0.00),2) ltv24,FORMAT(IFNULL((sum(iap_revenue_25)/100+sum(revenue_25)/100)/sum(reg_user_cnt),0.00),2) ltv25,
		FORMAT(IFNULL((sum(iap_revenue_26)/100+sum(revenue_26)/100)/sum(reg_user_cnt),0.00),2) ltv26,FORMAT(IFNULL((sum(iap_revenue_27)/100+sum(revenue_27)/100)/sum(reg_user_cnt),0.00),2) ltv27,
		FORMAT(IFNULL((sum(iap_revenue_28)/100+sum(revenue_28)/100)/sum(reg_user_cnt),0.00),2) ltv28,FORMAT(IFNULL((sum(iap_revenue_29)/100+sum(revenue_29)/100)/sum(reg_user_cnt),0.00),2) ltv29,
		FORMAT(IFNULL((sum(iap_revenue_30)/100+sum(revenue_30)/100)/sum(reg_user_cnt),0.00),2) ltv30
		from new_ads_creative_wechat_roi_daily where tdate BETWEEN #{beginDate} AND #{endDate}  
		<if test="appId != null and appId != ''">
			and appid in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and ad_buy_media in (${media}) 
		</if>
		<if test="channelType != null and channelType != ''">
			and channel_type in (${channelType}) 
		</if>
		<if test="channel != null and channel != ''">
			and ad_buy_channel in (${channel}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account in (${accountId}) 
		</if>
		<if test="putUser != null and putUser != ''">
			and put_user in (${putUser}) 
		</if>
		<if test="artist != null and artist.size > 0">
            AND (
            <foreach collection="artist" index="index" item="item" separator="OR">
                artist LIKE "%"#{item}"%"
            </foreach>
            )
        </if>
        <if test="creativeId != null and creativeId != ''">
			and creative_id = #{creativeId}  
		</if>
		group by appid
		<if test="group != null and group != ''">
			,${group}
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by day asc ,spend desc
			</otherwise>
		</choose>
		union all
		SELECT ad_buy_media media,ad_buy_channel channel,app_name appName,tdate day,channel_type channelType,appid as appId,creative_id as creativeId,put_user putUser,artist,account as accountId,
		round(sum(revenue_1)/100,2) ltv1,round(sum(rebate_spend)/100,2) spend,
		sum(reg_user_cnt) addUser,round(sum(iap_revenue_1)/100+sum(revenue_1)/100,2) firstDayIncome,round(sum(iap_revenue_1)/100,2) ipaIncome,
		FORMAT(IFNULL(sum(reg_user_video_revenue)/sum(reg_user_video_cnt)*10,0.00),2) videoEcpm,FORMAT(IFNULL(sum(reg_user_plaque_revenue)/sum(reg_user_plaque_cnt)*10,0.00),2) plaqueEcpm,
		FORMAT(IFNULL(sum(rebate_spend)/100/sum(reg_user_cnt),0.00),2) cpa,FORMAT(IFNULL((sum(iap_revenue_1)/100+sum(revenue_1)/100)/sum(reg_user_cnt),0.00),2) arpu,
		round(sum(reg_user_plaque_cnt)/sum(reg_user_cnt),2) avgPlaqueShowTimes,round(sum(reg_user_video_cnt)/sum(reg_user_cnt),2) avgVideoShowTimes,
		CONCAT(FORMAT(IFNULL(sum(revenue_1)/sum(rebate_spend)*100,0.00),2),'%') roi,CONCAT(FORMAT(IFNULL(sum(iap_revenue_1)/sum(rebate_spend)*100,0.00),2),'%') ipaRoi,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_1)+sum(revenue_1))/sum(rebate_spend)*100,0.00),2),'%') firstDayRoi,FORMAT(sum(rebate_spend)/100/sum(impressions)*1000,2) avgShowSpend,
		CONCAT(FORMAT(IFNULL(sum(installs)/sum(clicks)*100,0.00),2),'%') installRate,CONCAT(FORMAT(IFNULL(sum(clicks)/sum(impressions)*100,0.00),2),'%') clickRate,
		CONCAT(FORMAT(IFNULL(sum(retention_1day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') retentionOneDayRate,CONCAT(FORMAT(IFNULL(sum(retention_2day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd2,
		CONCAT(FORMAT(IFNULL(sum(retention_3day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd3,CONCAT(FORMAT(IFNULL(sum(retention_4day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd4,
		CONCAT(FORMAT(IFNULL(sum(retention_5day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd5,CONCAT(FORMAT(IFNULL(sum(retention_6day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd6,
		CONCAT(FORMAT(IFNULL(sum(retention_7day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd7,CONCAT(FORMAT(IFNULL(sum(retention_8day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd8,
		CONCAT(FORMAT(IFNULL(sum(retention_9day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd9,CONCAT(FORMAT(IFNULL(sum(retention_10day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd10,
		CONCAT(FORMAT(IFNULL(sum(retention_11day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd11,CONCAT(FORMAT(IFNULL(sum(retention_12day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd12,
		CONCAT(FORMAT(IFNULL(sum(retention_13day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd13,CONCAT(FORMAT(IFNULL(sum(retention_14day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd14,
		CONCAT(FORMAT(IFNULL(sum(retention_15day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd15,CONCAT(FORMAT(IFNULL(sum(retention_16day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd16,
		CONCAT(FORMAT(IFNULL(sum(retention_17day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd17,CONCAT(FORMAT(IFNULL(sum(retention_18day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd18,
		CONCAT(FORMAT(IFNULL(sum(retention_19day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd19,CONCAT(FORMAT(IFNULL(sum(retention_20day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd20,
		CONCAT(FORMAT(IFNULL(sum(retention_21day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd21,CONCAT(FORMAT(IFNULL(sum(retention_22day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd22,
		CONCAT(FORMAT(IFNULL(sum(retention_23day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd23,CONCAT(FORMAT(IFNULL(sum(retention_24day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd24,
		CONCAT(FORMAT(IFNULL(sum(retention_25day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd25,CONCAT(FORMAT(IFNULL(sum(retention_26day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd26,
		CONCAT(FORMAT(IFNULL(sum(retention_27day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd27,CONCAT(FORMAT(IFNULL(sum(retention_28day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd28,
		CONCAT(FORMAT(IFNULL(sum(retention_29day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd29,CONCAT(FORMAT(IFNULL(sum(retention_30day_user_cnt)/sum(reg_user_cnt)*100,0.00),2),'%') rd30,
		sum(iap_active_user_cnt) active_purchase_users,sum(iap_reg_user_cnt) add_purchase_users, 
		CONCAT(FORMAT(IFNULL(sum(iap_retention_1day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd1,CONCAT(FORMAT(IFNULL(sum(iap_retention_2day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd2,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_3day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd3,CONCAT(FORMAT(IFNULL(sum(iap_retention_4day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd4,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_5day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd5,CONCAT(FORMAT(IFNULL(sum(iap_retention_6day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd6,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_7day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd7,CONCAT(FORMAT(IFNULL(sum(iap_retention_8day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd8,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_9day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd9,CONCAT(FORMAT(IFNULL(sum(iap_retention_10day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd10,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_11day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd11,CONCAT(FORMAT(IFNULL(sum(iap_retention_12day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd12,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_13day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd13,CONCAT(FORMAT(IFNULL(sum(iap_retention_14day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd14,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_15day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd15,CONCAT(FORMAT(IFNULL(sum(iap_retention_16day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd16,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_17day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd17,CONCAT(FORMAT(IFNULL(sum(iap_retention_18day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd18,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_19day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd19,CONCAT(FORMAT(IFNULL(sum(iap_retention_20day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd20,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_21day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd21,CONCAT(FORMAT(IFNULL(sum(iap_retention_22day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd22,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_23day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd23,CONCAT(FORMAT(IFNULL(sum(iap_retention_24day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd24,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_25day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd25,CONCAT(FORMAT(IFNULL(sum(iap_retention_26day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd26,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_27day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd27,CONCAT(FORMAT(IFNULL(sum(iap_retention_28day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd28,
		CONCAT(FORMAT(IFNULL(sum(iap_retention_29day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd29,CONCAT(FORMAT(IFNULL(sum(iap_retention_30day_user_cnt)/sum(iap_reg_user_cnt)*100,0.00),2),'%') ipaRd30,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_2)+sum(revenue_2))/sum(rebate_spend)*100,0.00),2),'%') roi2,CONCAT(FORMAT(IFNULL((sum(iap_revenue_3)+sum(revenue_3))/sum(rebate_spend)*100,0.00),2),'%') roi3,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_4)+sum(revenue_4))/sum(rebate_spend)*100,0.00),2),'%') roi4,CONCAT(FORMAT(IFNULL((sum(iap_revenue_5)+sum(revenue_5))/sum(rebate_spend)*100,0.00),2),'%') roi5,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_6)+sum(revenue_6))/sum(rebate_spend)*100,0.00),2),'%') roi6,CONCAT(FORMAT(IFNULL((sum(iap_revenue_7)+sum(revenue_7))/sum(rebate_spend)*100,0.00),2),'%') roi7,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_8)+sum(revenue_8))/sum(rebate_spend)*100,0.00),2),'%') roi8,CONCAT(FORMAT(IFNULL((sum(iap_revenue_9)+sum(revenue_9))/sum(rebate_spend)*100,0.00),2),'%') roi9,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_10)+sum(revenue_10))/sum(rebate_spend)*100,0.00),2),'%') roi10,CONCAT(FORMAT(IFNULL((sum(iap_revenue_11)+sum(revenue_11))/sum(rebate_spend)*100,0.00),2),'%') roi11,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_12)+sum(revenue_12))/sum(rebate_spend)*100,0.00),2),'%') roi12,CONCAT(FORMAT(IFNULL((sum(iap_revenue_13)+sum(revenue_13))/sum(rebate_spend)*100,0.00),2),'%') roi13,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_14)+sum(revenue_14))/sum(rebate_spend)*100,0.00),2),'%') roi14,CONCAT(FORMAT(IFNULL((sum(iap_revenue_15)+sum(revenue_15))/sum(rebate_spend)*100,0.00),2),'%') roi15,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_16)+sum(revenue_16))/sum(rebate_spend)*100,0.00),2),'%') roi16,CONCAT(FORMAT(IFNULL((sum(iap_revenue_17)+sum(revenue_17))/sum(rebate_spend)*100,0.00),2),'%') roi17,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_18)+sum(revenue_18))/sum(rebate_spend)*100,0.00),2),'%') roi18,CONCAT(FORMAT(IFNULL((sum(iap_revenue_19)+sum(revenue_19))/sum(rebate_spend)*100,0.00),2),'%') roi19,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_20)+sum(revenue_20))/sum(rebate_spend)*100,0.00),2),'%') roi20,CONCAT(FORMAT(IFNULL((sum(iap_revenue_21)+sum(revenue_21))/sum(rebate_spend)*100,0.00),2),'%') roi21,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_22)+sum(revenue_22))/sum(rebate_spend)*100,0.00),2),'%') roi22,CONCAT(FORMAT(IFNULL((sum(iap_revenue_23)+sum(revenue_23))/sum(rebate_spend)*100,0.00),2),'%') roi23,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_24)+sum(revenue_24))/sum(rebate_spend)*100,0.00),2),'%') roi24,CONCAT(FORMAT(IFNULL((sum(iap_revenue_25)+sum(revenue_25))/sum(rebate_spend)*100,0.00),2),'%') roi25,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_26)+sum(revenue_26))/sum(rebate_spend)*100,0.00),2),'%') roi26,CONCAT(FORMAT(IFNULL((sum(iap_revenue_27)+sum(revenue_27))/sum(rebate_spend)*100,0.00),2),'%') roi27,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_28)+sum(revenue_28))/sum(rebate_spend)*100,0.00),2),'%') roi28,CONCAT(FORMAT(IFNULL((sum(iap_revenue_29)+sum(revenue_29))/sum(rebate_spend)*100,0.00),2),'%') roi29,
		CONCAT(FORMAT(IFNULL((sum(iap_revenue_30)+sum(revenue_30))/sum(rebate_spend)*100,0.00),2),'%') roi30,
		FORMAT(IFNULL(sum(rebate_spend)/100/sum(game_pay_count),0.00),2) payCost,FORMAT(IFNULL(sum(rebate_spend)/100/sum(pay_count),0.00),2) firstChargeCost,
		FORMAT(sum(pay_count)/sum(installs),2) firstPaymentRate,sum(pay_count) firstPayCount,sum(game_pay_count) paymentTimes,
		FORMAT(IFNULL((sum(iap_revenue_2)/100+sum(revenue_2)/100)/sum(reg_user_cnt),0.00),2) ltv2,FORMAT(IFNULL((sum(iap_revenue_3)/100+sum(revenue_3)/100)/sum(reg_user_cnt),0.00),2) ltv3,
		FORMAT(IFNULL((sum(iap_revenue_4)/100+sum(revenue_4)/100)/sum(reg_user_cnt),0.00),2) ltv4,FORMAT(IFNULL((sum(iap_revenue_5)/100+sum(revenue_5)/100)/sum(reg_user_cnt),0.00),2) ltv5,
		FORMAT(IFNULL((sum(iap_revenue_6)/100+sum(revenue_6)/100)/sum(reg_user_cnt),0.00),2) ltv6,FORMAT(IFNULL((sum(iap_revenue_7)/100+sum(revenue_7)/100)/sum(reg_user_cnt),0.00),2) ltv7,
		FORMAT(IFNULL((sum(iap_revenue_8)/100+sum(revenue_8)/100)/sum(reg_user_cnt),0.00),2) ltv8,FORMAT(IFNULL((sum(iap_revenue_9)/100+sum(revenue_9)/100)/sum(reg_user_cnt),0.00),2) ltv9,
		FORMAT(IFNULL((sum(iap_revenue_10)/100+sum(revenue_10)/100)/sum(reg_user_cnt),0.00),2) ltv10,FORMAT(IFNULL((sum(iap_revenue_11)/100+sum(revenue_11)/100)/sum(reg_user_cnt),0.00),2) ltv11,
		FORMAT(IFNULL((sum(iap_revenue_12)/100+sum(revenue_12)/100)/sum(reg_user_cnt),0.00),2) ltv12,FORMAT(IFNULL((sum(iap_revenue_13)/100+sum(revenue_13)/100)/sum(reg_user_cnt),0.00),2) ltv13,
		FORMAT(IFNULL((sum(iap_revenue_14)/100+sum(revenue_14)/100)/sum(reg_user_cnt),0.00),2) ltv14,FORMAT(IFNULL((sum(iap_revenue_15)/100+sum(revenue_15)/100)/sum(reg_user_cnt),0.00),2) ltv15,
		FORMAT(IFNULL((sum(iap_revenue_16)/100+sum(revenue_16)/100)/sum(reg_user_cnt),0.00),2) ltv16,FORMAT(IFNULL((sum(iap_revenue_17)/100+sum(revenue_17)/100)/sum(reg_user_cnt),0.00),2) ltv17,
		FORMAT(IFNULL((sum(iap_revenue_18)/100+sum(revenue_18)/100)/sum(reg_user_cnt),0.00),2) ltv18,FORMAT(IFNULL((sum(iap_revenue_19)/100+sum(revenue_19)/100)/sum(reg_user_cnt),0.00),2) ltv19,
		FORMAT(IFNULL((sum(iap_revenue_20)/100+sum(revenue_20)/100)/sum(reg_user_cnt),0.00),2) ltv20,FORMAT(IFNULL((sum(iap_revenue_21)/100+sum(revenue_21)/100)/sum(reg_user_cnt),0.00),2) ltv21,
		FORMAT(IFNULL((sum(iap_revenue_22)/100+sum(revenue_22)/100)/sum(reg_user_cnt),0.00),2) ltv22,FORMAT(IFNULL((sum(iap_revenue_23)/100+sum(revenue_23)/100)/sum(reg_user_cnt),0.00),2) ltv23,
		FORMAT(IFNULL((sum(iap_revenue_24)/100+sum(revenue_24)/100)/sum(reg_user_cnt),0.00),2) ltv24,FORMAT(IFNULL((sum(iap_revenue_25)/100+sum(revenue_25)/100)/sum(reg_user_cnt),0.00),2) ltv25,
		FORMAT(IFNULL((sum(iap_revenue_26)/100+sum(revenue_26)/100)/sum(reg_user_cnt),0.00),2) ltv26,FORMAT(IFNULL((sum(iap_revenue_27)/100+sum(revenue_27)/100)/sum(reg_user_cnt),0.00),2) ltv27,
		FORMAT(IFNULL((sum(iap_revenue_28)/100+sum(revenue_28)/100)/sum(reg_user_cnt),0.00),2) ltv28,FORMAT(IFNULL((sum(iap_revenue_29)/100+sum(revenue_29)/100)/sum(reg_user_cnt),0.00),2) ltv29,
		FORMAT(IFNULL((sum(iap_revenue_30)/100+sum(revenue_30)/100)/sum(reg_user_cnt),0.00),2) ltv30
		from new_ads_material_wechat_roi_daily where tdate BETWEEN #{beginDate} AND #{endDate}  
		<if test="appId != null and appId != ''">
			and appid in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and ad_buy_media in (${media}) 
		</if>
		<if test="channelType != null and channelType != ''">
			and channel_type in (${channelType}) 
		</if>
		<if test="channel != null and channel != ''">
			and ad_buy_channel in (${channel}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account in (${accountId}) 
		</if>
		<if test="putUser != null and putUser != ''">
			and put_user in (${putUser}) 
		</if>
		<if test="artist != null and artist.size > 0">
            AND (
            <foreach collection="artist" index="index" item="item" separator="OR">
                artist LIKE "%"#{item}"%"
            </foreach>
            )
        </if>
        <if test="creativeId != null and creativeId != ''">
			and creative_id = #{creativeId}  
		</if>
		group by appid
		<if test="group != null and group != ''">
			,${group}
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by day asc ,spend desc
			</otherwise>
		</choose>
	</sql>			
	<!-- 关键行为达成用户分析查询  -->
  	<select id="keyBehaviorReport" parameterType="com.wbgame.pojo.jettison.param.KeyBehaviorParam" resultType="com.wbgame.pojo.jettison.vo.KeyBehaviorVo">
		<include refid="key_behavior_sql"/>
	</select>
	<!-- 关键行为达成用户分析汇总  -->
	<select id="keyBehaviorReportTotal" parameterType="com.wbgame.pojo.jettison.param.KeyBehaviorParam"  resultType="com.wbgame.pojo.jettison.vo.KeyBehaviorVo">
			SELECT tdate date,appid appId,ad_buy_media media,ad_buy_channel channel,account accountId,putuser putUser,group_id groupId,campaign_id campaignId
			,campaign_name campaginName,putuser putUser,SUM(add_user) addUser,(case when is_reach =1 then '达成' else '未达成' end) isReached 
			,SUM(is_reached_num+(case when ytd_tdy_reach is null then 0 else ytd_tdy_reach end)) isReachedNum 
			,CONCAT(round(SUM(is_reached_num+(case when ytd_tdy_reach is null then 0 else ytd_tdy_reach end))/SUM(add_user)*100,2),'%') reacheRate
			,sum((case when ytd_tdy_reach is null then 0 else ytd_tdy_reach end))  ytdTdyReach 
			,round(SUM(cost)/SUM(is_reached_num)/100,2) cpa 
			,CONCAT(round((SUM(iaa_revenue)+SUM(iap_revenue))/SUM(cost)*100,2),'%') roi
			,round((SUM(iaa_revenue)+SUM(iap_revenue))/SUM(is_reached_num)/100,2) arpu 
			,round(SUM(iaa_revenue)/SUM(is_reached_num)/100,3) adArpu 
			,round(SUM(iap_revenue)/(is_reached_num)/100,3) ipaArpu
			,CONCAT(round(SUM(iap_usenum)/SUM(is_reached_num),2),'%') payRate 
			,SUM(iap_number) ipaCount 
			,SUM(iap_usenum) ipaUserCount 
			,round(SUM(plaque_cnt)/SUM(is_reached_num),2) avgPlaque 
			,round(SUM(video_cnt)/SUM(is_reached_num),2) avgVideo 
			,round(SUM(msg_cnt)/SUM(is_reached_num),2) avgMsg 
			,round(SUM(plaque_revenue)/SUM(plaque_cnt)*10,2) plaqueEcpm
			,round(SUM(video_revenue)/SUM(video_cnt)*10,2) videoEcpm 
			,round(SUM(msg_revenue)/SUM(msg_cnt)*10,2) msgEcpm 
			,round(SUM(reach_time_client)/( SUM(ytd_tdy_reach) +SUM(is_reached_num)),2) avgClientReachTime 
			,round(SUM(reach_time_sever)/( SUM(ytd_tdy_reach) +SUM(is_reached_num)),2) avgServerReachTime
			,reachtime_client_median medianClientReachTime 
			,reachtime_sever_median medianServerReachTime FROM ads_user_add_action_reach_daily  
			where tdate BETWEEN #{beginDate} AND #{endDate}  
			<if test="appId != null and appId != ''">
				and appid in (${appId}) 
			</if>
			<if test="media != null and media != ''">
				and ad_buy_media in (${media}) 
			</if>
			<if test="channel != null and channel != ''">
				and ad_buy_channel in (${channel}) 
			</if>
			<if test="putUser != null and putUser != ''">
				and putuser in (${putUser}) 
			</if>
			<if test="accountId != null and accountId != ''">
				and account in (${accountId}) 
			</if>
			<if test="groupId != null and groupId != ''">
				and group_id in (${groupId}) 
			</if>
			<if test="campaignId != null and campaignId != ''">
				and campaign_id = #{campaignId} 
			</if>
			<if test="campaginName != null and campaginName != ''">
				and campaign_name = #{campaginName} 
			</if>
	</select>
	
	<sql id="key_behavior_sql">
			SELECT tdate date,appid appId,ad_buy_media media,ad_buy_channel channel,account accountId,putuser putUser,group_id groupId,campaign_id campaignId
			,campaign_name campaginName,putuser putUser,SUM(add_user) addUser,(case when is_reach =1 then '达成' else '未达成' end) isReached 
			,SUM(is_reached_num+(case when ytd_tdy_reach is null then 0 else ytd_tdy_reach end)) isReachedNum 
			,CONCAT(round(SUM(is_reached_num+(case when ytd_tdy_reach is null then 0 else ytd_tdy_reach end))/SUM(add_user)*100,2),'%') reacheRate
			,sum((case when ytd_tdy_reach is null then 0 else ytd_tdy_reach end))  ytdTdyReach 
			,round(SUM(cost)/SUM(is_reached_num)/100,2) cpa 
			,CONCAT(round((SUM(iaa_revenue)+SUM(iap_revenue))/SUM(cost)*100,2),'%') roi
			,round((SUM(iaa_revenue)+SUM(iap_revenue))/SUM(is_reached_num)/100,2) arpu 
			,round(SUM(iaa_revenue)/SUM(is_reached_num)/100,3) adArpu 
			,round(SUM(iap_revenue)/(is_reached_num)/100,3) ipaArpu
			,CONCAT(round(SUM(iap_usenum)/SUM(is_reached_num),2),'%') payRate 
			,SUM(iap_number) ipaCount 
			,SUM(iap_usenum) ipaUserCount 
			,round(SUM(plaque_cnt)/SUM(is_reached_num),2) avgPlaque 
			,round(SUM(video_cnt)/SUM(is_reached_num),2) avgVideo 
			,round(SUM(msg_cnt)/SUM(is_reached_num),2) avgMsg 
			,round(SUM(plaque_revenue)/SUM(plaque_cnt)*10,2) plaqueEcpm
			,round(SUM(video_revenue)/SUM(video_cnt)*10,2) videoEcpm 
			,round(SUM(msg_revenue)/SUM(msg_cnt)*10,2) msgEcpm 
			,round(SUM(reach_time_client)/( SUM(ytd_tdy_reach) +SUM(is_reached_num)),2) avgClientReachTime 
			,round(SUM(reach_time_sever)/( SUM(ytd_tdy_reach) +SUM(is_reached_num)),2) avgServerReachTime
			,reachtime_client_median medianClientReachTime 
			,reachtime_sever_median medianServerReachTime FROM ads_user_add_action_reach_daily  
			where tdate BETWEEN #{beginDate} AND #{endDate}  
			<if test="appId != null and appId != ''">
				and appid in (${appId}) 
			</if>
			<if test="media != null and media != ''">
				and ad_buy_media in (${media}) 
			</if>
			<if test="channel != null and channel != ''">
				and ad_buy_channel in (${channel}) 
			</if>
			<if test="putUser != null and putUser != ''">
				and putuser in (${putUser}) 
			</if>
			<if test="accountId != null and accountId != ''">
				and account in (${accountId}) 
			</if>
			<if test="groupId != null and groupId != ''">
				and group_id in (${groupId}) 
			</if>
			<if test="campaignId != null and campaignId != ''">
				and campaign_id = #{campaignId} 
			</if>
			<if test="campaginName != null and campaginName != ''">
				and campaign_name = #{campaginName} 
			</if>
			group by is_reach
			<if test="group != null and group != ''">
				,${group}
			</if>
			<choose>
				<when test="order != null and order != ''">
					order by ${order}
				</when>
				<otherwise>
					order by  ${group}  desc
				</otherwise>
		</choose>
	</sql>		
	<!-- 查询报表最近更新时间  -->
  	<select id="queryCreateTime" parameterType="java.lang.String" resultType="java.util.Map">
		SELECT dt,end_time endTime , create_time createTime FROM ads_dqc_table_update_info WHERE table_name = #{tableName}
		 AND dt = (SELECT MAX(dt) FROM ads_dqc_table_update_info WHERE SUBSTR(dt, 1, 8) = #{dateSuff} AND table_name =#{tableName} ) ORDER BY create_time DESC limit 1
	</select>
	
	<!-- 素材周数据报表查询  -->
  	<select id="materialWeeList" parameterType="com.wbgame.pojo.jettison.param.MaterialWeekParam" resultType="com.wbgame.pojo.jettison.vo.MaterialWeekVo">
		<include refid="material_week_sql"/>
	</select>
	<!-- 素材周数据报表汇总  -->
	<select id="materialWeeListTotal" parameterType="com.wbgame.pojo.jettison.param.MaterialWeekParam" resultType="com.wbgame.pojo.jettison.vo.MaterialWeekVo">
		SELECT id,tdate_week,artist,appid,app_name,material_type,sum(all_material_cnt) all_material_cnt,sum(online_material_cnt) online_material_cnt,
					sum(spend_material_cnt) spend_material_cnt,sum(all_material_spend) all_material_spend,creatives,producer3d,
					CONCAT(round(SUM(online_material_cnt)/SUM(all_material_cnt)*100,2),'%') usage_rate,
					CONCAT(ROUND(sum(spend_material_cnt)/((sum(all_material_cnt)))*100, 2),'%') effective_rate,
					sum(material_week_1) material_week_1,sum(material_week_2) material_week_2,
					sum(material_week_3) material_week_3,sum(material_week_4) material_week_4,
					sum(material_week_5) material_week_5,sum(material_week_6) material_week_6,
					sum(material_week_7) material_week_7,sum(material_week_8) material_week_8,
					sum(material_week_9) material_week_9,sum(material_week_10) material_week_10,
					sum(material_week_11) material_week_11 ,sum(material_week_12) material_week_12
			from (<include refid="material_week_sql"/>) 
	</select>
	
	<sql id="material_week_sql">
			SELECT id,
					tdate_week,
					artist,
					appid,
					app_name,
					material_type,
					creatives,producer3d,
					sum(all_material_cnt)  all_material_cnt,
					sum(online_material_cnt) online_material_cnt,
					sum(spend_material_cnt) spend_material_cnt,
					round(sum(all_material_spend),2) all_material_spend,
					CONCAT(round(SUM(online_material_cnt)/SUM(all_material_cnt)*100,2),'%') usage_rate,
					CONCAT(ROUND(sum(spend_material_cnt)/((sum(all_material_cnt)))*100, 2),'%') effective_rate,
					round(sum(material_week_1),2) material_week_1,
					round(sum(material_week_2),2) material_week_2,
					round(sum(material_week_3),2) material_week_3,
					round(sum(material_week_4),2) material_week_4,
					round(sum(material_week_5),2) material_week_5,
					round(sum(material_week_6),2) material_week_6,
					round(sum(material_week_7),2) material_week_7,
					round(sum(material_week_8),2) material_week_8,
					round(sum(material_week_9),2) material_week_9,
					round(sum(material_week_10),2) material_week_10,
					round(sum(material_week_11),2) material_week_11 ,
					round(sum(material_week_12),2) material_week_12 FROM ads_material_week_spend_daily  
			where tdate_week in  (
					 <foreach collection="tdate_week" index="index" item="item" separator=",">
		                #{item}
		            </foreach>
			) 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="material_type != null and material_type != ''">
				and material_type in (${material_type}) 
			</if>
			<if test="creatives != null and creatives.size > 0">
            	AND (
		            <foreach collection="creatives" index="index" item="item" separator="OR">
		                creatives LIKE "%"#{item}"%"
		            </foreach>
		            )
        	</if>
			<if test="producer3d != null and producer3d.size > 0">
            	AND (
		            <foreach collection="producer3d" index="index" item="item" separator="OR">
		                producer3d LIKE "%"#{item}"%"
		            </foreach>
		            )
        	</if>
			<if test="artist != null and artist.size > 0">
            	AND (
		            <foreach collection="artist" index="index" item="item" separator="OR">
		                artist LIKE "%"#{item}"%"
		            </foreach>
		            )
        	</if>
			<if test="group != null and group != ''">
				group by ${group}
			</if>
			<choose>
				<when test="order != null and order != ''">
					order by ${order}
				</when>
				<otherwise>
					order by tdate_week asc ,all_material_cnt desc
				</otherwise>
		</choose>
	</sql>		
	
</mapper>