<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.warning.SpecialWarningMapper">

	<select id="selectAppGroupResult" resultType="com.wbgame.pojo.budgetWarning.SpecialWarningResult">
				select app, app_name appName, media,
					   IFNULL(sum(spend),0) spend,
					   IFNULL(sum(recoveryRevenue)/sum(spend),0) roiJ,
					   IFNULL(sum(oppoRealTimeRevenue)/100/sum(spend),0) roiOp,
					   IFNULL(sum(revenue1)/sum(spend),0) roi,
					   IFNULL(sum(game_addiction),0) game_addiction,
					   IFNULL(sum(spend)/sum(game_addiction),0) game_addiction_spend
				from dnwx_adt.dn_report_spend_china a
						 LEFT JOIN dnwx_bi.app_info b on a.app=b.id
						 left join dnwx_adt.dn_report_spend_oppo_realtime c on a.`day` = c.tdate and a.campaignId = c.campaign_id
				where `day` = #{date}
				  and b.app_category = 1
				group by app, media
				having spend > 0
				ORDER BY app;
	</select>
	<select id="selectAppGroupHistoryResult"
			resultType="com.wbgame.pojo.budgetWarning.SpecialWarningResult">
		select app,app_name appName,  media,
			   IFNULL(sum(spend),0) spend,
			   IFNULL(sum(recoveryRevenue)/sum(spend),0) roiJ,
			   IFNULL(sum(oppoRealTimeRevenue)/100/sum(spend),0) roiOp,
			   IFNULL(sum(revenue1)/sum(spend),0) roi,
			   IFNULL(sum(game_addiction),0) game_addiction,
			   IFNULL(sum(spend)/sum(game_addiction),0) game_addiction_spend
				 from dnwx_adt.dn_report_spend_china a
						  LEFT JOIN dnwx_bi.app_info b on a.app=b.id
						  left join dnwx_adt.dn_report_spend_oppo_realtime c on a.`day` = c.tdate and a.campaignId = c.campaign_id
				 where `day` between #{start} and #{end}
				   and b.app_category = 1
		group by app, media
		having spend > 0
		ORDER BY app;
	</select>
	<select id="selectAccountGroupResult"
			resultType="com.wbgame.pojo.budgetWarning.SpecialWarningResult">
		select app,app_name appName,  media, account,
			   IFNULL(sum(spend),0) spend,
			   IFNULL(sum(recoveryRevenue)/sum(spend),0) roiJ,
			   IFNULL(sum(oppoRealTimeRevenue)/100/sum(spend),0) roiOp,
			   IFNULL(sum(revenue1)/sum(spend),0) roi,
			   IFNULL(sum(game_addiction),0) game_addiction,
			   IFNULL(sum(spend)/sum(game_addiction),0) game_addiction_spend
		from dnwx_adt.dn_report_spend_china a
				 LEFT JOIN dnwx_bi.app_info b on a.app=b.id
				 left join dnwx_adt.dn_report_spend_oppo_realtime c on a.`day` = c.tdate and a.campaignId = c.campaign_id
		where `day` between #{start} and #{end}
		  and b.app_category = 1
		group by app, media, account
		having spend > 0
		ORDER BY app;
	</select>

</mapper>