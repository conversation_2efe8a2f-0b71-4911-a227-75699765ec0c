<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsToolPopCreateDailyMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.AdsToolPopCreateDailyVO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="tdate" property="tdate" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="app" property="app" jdbcType="VARCHAR"/>
        <result column="cha_media" property="chaMedia" jdbcType="VARCHAR"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="pid" property="pid" jdbcType="VARCHAR"/>
        <result column="os" property="os" jdbcType="VARCHAR"/>
        <result column="os_ver" property="osVer" jdbcType="VARCHAR"/>
        <result column="brand" property="brand" jdbcType="VARCHAR"/>
        <result column="model" property="model" jdbcType="VARCHAR"/>
        <result column="scenes" property="scenes" jdbcType="VARCHAR"/>
        <result column="user_type" property="userType" jdbcType="VARCHAR"/>
        <result column="creation_success_rate" property="creationSuccessRate" jdbcType="VARCHAR"/>
        <result column="creating_proportion" property="creatingProportion" jdbcType="VARCHAR"/>
        <result column="create_proportion" property="createProportion" jdbcType="VARCHAR"/>
        <result column="creating_per_capita_pv" property="creatingPerCapitaPv" jdbcType="VARCHAR"/>
        <result column="create_Per_capita_pv" property="createPerCapitaPv" jdbcType="VARCHAR"/>
        <result column="total_users" property="totalUsers" jdbcType="BIGINT"/>
        <result column="page_creating_uv" property="pageCreatingUv" jdbcType="BIGINT"/>
        <result column="page_created_uv" property="pageCreatedUv" jdbcType="BIGINT"/>
        <result column="page_creating_pv" property="pageCreatingPv" jdbcType="BIGINT"/>
        <result column="page_created_pv" property="pageCreatedPv" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , tdate, appid, app, cha_media, channel, pid, os, os_ver, brand, model, scenes,
    user_type, total_users, page_creating_uv, page_created_uv, page_creating_pv, page_created_pv
    </sql>

    <sql id="queryCondition">
        <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
            and tdate between #{start_date} and #{end_date}
        </if>


        <if test="channel != null and channel != ''">
            and channel = #{channel}
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="osVer != null and osVer != ''">
            and os_ver = #{osVer}
        </if>
        <if test="brand != null and brand != ''">
            and brand = #{brand}
        </if>
        <if test="model != null and model != ''">
            and model = #{model}
        </if>
        <if test="scenes != null and scenes != ''">
            and scenes = #{scenes}
        </if>
        <if test="userType != null and userType != ''">
            and user_type = #{userType}
        </if>

        <if test="mediaList != null and mediaList.size > 0">
            AND cha_media IN
            <foreach collection="mediaList" item="m" open="(" separator="," close=")">
                #{m}
            </foreach>
        </if>
        <if test="channelList != null and channelList.size > 0">
            AND channel IN
            <foreach collection="channelList" item="cha" open="(" separator="," close=")">
                #{cha}
            </foreach>
        </if>

        <if test="appidList != null and appidList.size > 0">
            AND appid IN
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
    </sql>

    <select id="selectByAdsToolPopCreateDaily" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.AdsToolPopCreateDailyDTO">


        <include refid="selectData"/>


    </select>

    <select id="countByAdsToolPopCreateDaily" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.clean.AdsToolPopCreateDailyDTO">
        select  sum(total_users) total_users,
                sum(page_creating_uv) page_creating_uv,
                sum(page_created_uv) page_created_uv,
                sum(page_creating_pv) page_creating_pv ,
                sum(page_created_pv) page_created_pv
        from (

            <include refid="selectData"/>
                          ) a
    </select>

    <sql id="selectData">

        select tdate,
        appid,
        app,
        user_type,
        scenes,

        <if test="group != null and group != ''">
            ${group},
        </if>
        total_users,
        page_creating_uv,
        page_created_uv,
        page_creating_pv,
        page_created_pv,
        TRUNCATE(ifnull(page_creating_uv / total_users, 0) * 100, 2)     creating_proportion,
        TRUNCATE(ifnull(page_created_uv / total_users, 0) * 100, 2)      create_proportion,
        TRUNCATE(ifnull(page_creating_pv / total_users, 0), 2)           creating_per_capita_pv,
        TRUNCATE(ifnull(page_created_pv / total_users, 0), 2)            create_Per_capita_pv,
        TRUNCATE(ifnull(page_created_pv / page_creating_pv, 0) * 100, 2) creation_success_rate

        from (
        select tdate,
        appid,
        app,
        user_type,
        scenes,

        <if test="group != null and group != ''">
            ${group},
        </if>

        sum(page_creating_uv) page_creating_uv,
        sum(page_created_uv)  page_created_uv,
        sum(page_creating_pv) page_creating_pv,
        sum(page_created_pv)  page_created_pv


        from ads_tool_pop_create_daily

        <where>

            <include refid="queryCondition"/>
        </where>

        group by

        tdate,
        appid,
        app,
        user_type,
        scenes
        <if test="group != null and group != ''">
            ,${group}
        </if>
        ) a

        left join (
        select
        tdate            tdate1,
        appid            appid1,
        app              app1,
        user_type        user_type1,

        <if test="group_b != null and group_b != ''">
            ${group_b}
        </if>
        sum(total_users) total_users
        from (
        select
        tdate,
        appid,
        app,
        cha_media,
        channel,
        pid,
        os,
        os_ver,
        brand,
        model,

        user_type,
        total_users


        from ads_tool_pop_create_daily

        <where>

            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and tdate between #{start_date} and #{end_date}
            </if>


            <if test="channel != null and channel != ''">
                and channel = #{channel}
            </if>
            <if test="pid != null and pid != ''">
                and pid = #{pid}
            </if>
            <if test="osVer != null and osVer != ''">
                and os_ver = #{osVer}
            </if>
            <if test="brand != null and brand != ''">
                and brand = #{brand}
            </if>
            <if test="model != null and model != ''">
                and model = #{model}
            </if>

            <if test="userType != null and userType != ''">
                and user_type = #{userType}
            </if>

            <if test="mediaList != null and mediaList.size > 0">
                AND cha_media IN
                <foreach collection="mediaList" item="m" open="(" separator="," close=")">
                    #{m}
                </foreach>
            </if>
            <if test="channelList != null and channelList.size > 0">
                AND channel IN
                <foreach collection="channelList" item="cha" open="(" separator="," close=")">
                    #{cha}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>

        group by tdate,
          appid,
        app,
        cha_media,
        channel,
        pid,
        os,
        os_ver,
        brand,
        model,
        user_type,
        total_users
        ) h

        group by   tdate,
        appid,
        app,
        user_type
        <if test="group != null and group != ''">
            ,${group}
        </if>

        ) b
        on
        a.tdate = b.tdate1 and
        a.appid = b.appid1 and
        a.app = b.app1 and
        a.user_type = b.user_type1

        <if test="associateCondition != null and associateCondition != ''">

            ${associateCondition}
        </if>


        group by tdate,
        appid,
        app,
        user_type,
        scenes
        <if test="group != null and group != ''">
            ,${group}
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate, appid
            </otherwise>
        </choose>
    </sql>

</mapper>