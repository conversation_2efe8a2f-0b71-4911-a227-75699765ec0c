<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsUserGroupLevelSumDailyMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.operate.AdsUserGroupLevelSumDailyVO">
        <result column="tdate" property="tdate" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="pid" property="pid" jdbcType="VARCHAR"/>
        <result column="level_id" property="levelId" jdbcType="VARCHAR"/>
        <result column="group_id" property="groupId" jdbcType="VARCHAR"/>
        <result column="stay_num" property="stayNum" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        tdate
        , appid, channel, pid, level_id, group_id, stay_num
    </sql>
    <select id="selectByExample" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.operate.AdsUserGroupLevelSumDaily">

        SELECT
        DATE_FORMAT(tdate, '%Y-%m-%d') tdate,
        appid,
        level_id,
        <if test="group != null and group != ''">
            ${group},
        </if>
        cast(sum( stay_num ) as decimal(30, 0)) stay_num
        FROM
        ads_user_group_level_sum_daily

        <include refid="condition"/>
        GROUP BY tdate, appid, level_id
        <if test="group != null and group != ''">
            ,${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>

                order by tdate, stay_num desc
            </otherwise>
        </choose>

    </select>

    <select id="selectByExampleMap" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.operate.AdsUserGroupLevelSumDaily">

        SELECT

        if(level_id is null or level_id = '', '未知', level_id) level_id,
        <if test="group != null and group != ''">
            ${group},
        </if>
        cast(sum( stay_num ) as decimal(30, 0)) stay_num
        FROM
        ads_user_group_level_sum_daily

        <where>

            tdate between #{start_date} and #{end_date}

            <if test="levelId != null and levelId != ''">
                and level_id = #{levelId}
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

            <if test="prjidList != null and prjidList.size > 0">
                AND pid IN
                <foreach collection="prjidList" item="prj" open="(" separator="," close=")">
                    #{prj}
                </foreach>
            </if>

            <if test="channelList != null and channelList.size > 0">
                AND channel IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>

            <if test="groupIdList != null and groupIdList.size > 0">
                AND group_id IN
                <foreach collection="groupIdList" item="grp" open="(" separator="," close=")">
                    #{grp}
                </foreach>
            </if>

        </where>

        
        GROUP BY level_id
        <if test="group != null and group != ''">
            ,${group}
        </if>

        ORDER BY cast(substring_index(level_id,'_',1) as bigint) asc,cast(substring_index(level_id,'_',-1) as bigint) asc

    </select>

    <select id="selectCountByExample" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.operate.AdsUserGroupLevelSumDaily">
        select cast(sum( stay_num ) as decimal(30, 0)) stay_num

        from (


            SELECT

            level_id,
            <if test="group != null and group != ''">
                ${group},
            </if>
            cast(sum( stay_num ) as decimal(30, 2)) stay_num
            FROM
            ads_user_group_level_sum_daily

            <include refid="condition"/>

            GROUP BY level_id
            <if test="group != null and group != ''">
                ,${group}
            </if>
                           ) a

    </select>

    
    <sql id="condition">
        
        <where>

            tdate between #{start_date} and #{end_date}

            <if test="levelId != null and levelId != ''">
                and level_id = #{levelId}
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

            <if test="prjidList != null and prjidList.size > 0">
                AND pid IN
                <foreach collection="prjidList" item="prj" open="(" separator="," close=")">
                    #{prj}
                </foreach>
            </if>

            <if test="channelList != null and channelList.size > 0">
                AND channel IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>

            <if test="groupIdList != null and groupIdList.size > 0">
                AND group_id IN
                <foreach collection="groupIdList" item="grp" open="(" separator="," close=")">
                    #{grp}
                </foreach>
            </if>

        </where>
    </sql>
</mapper>