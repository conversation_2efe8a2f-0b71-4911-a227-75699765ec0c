<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.RetentionMapper">

	<select id="getMicGameModelRetentionList" parameterType="com.wbgame.pojo.game.report.MicGameModelRetentionReportVo"
			resultType="com.wbgame.pojo.game.report.MicGameModelRetentionReportVo">
		select
		<if test="group != null and group != ''">
			${group},
		</if>
			app_name,
			sum(reg_user_cnt) reg_user_cnt,
			sum(active_user_cnt) active_user_cnt,
			sum(iap_user_cnt) iap_user_cnt,

			FORMAT(sum(reg_retention_1day_user_cnt)/sum(reg_user_cnt),4) reg_retention_1,
			FORMAT(sum(reg_retention_2day_user_cnt)/sum(reg_user_cnt),4) reg_retention_3,
			FORMAT(sum(reg_retention_6day_user_cnt)/sum(reg_user_cnt),4) reg_retention_7,
			FORMAT(sum(reg_retention_14day_user_cnt)/sum(reg_user_cnt),4) reg_retention_14,
			FORMAT(sum(reg_retention_30day_user_cnt)/sum(reg_user_cnt),4) reg_retention_30,
			FORMAT(sum(reg_retention_60day_user_cnt)/sum(reg_user_cnt),4) reg_retention_60,

			FORMAT(sum(active_retention_1day_user_cnt)/sum(active_user_cnt),4) active_retention_1,
			FORMAT(sum(active_retention_2day_user_cnt)/sum(active_user_cnt),4) active_retention_3,
			FORMAT(sum(active_retention_6day_user_cnt)/sum(active_user_cnt),4) active_retention_7,
			FORMAT(sum(active_retention_14day_user_cnt)/sum(active_user_cnt),4) active_retention_14,
			FORMAT(sum(active_retention_30day_user_cnt)/sum(active_user_cnt),4) active_retention_30,
			FORMAT(sum(active_retention_60day_user_cnt)/sum(active_user_cnt),4) active_retention_60,

			FORMAT(sum(iap_retention_1day_user_cnt)/sum(iap_user_cnt),4) iap_retention_1,
			FORMAT(sum(iap_retention_2day_user_cnt)/sum(iap_user_cnt),4) iap_retention_3,
			FORMAT(sum(iap_retention_6day_user_cnt)/sum(iap_user_cnt),4) iap_retention_7,
			FORMAT(sum(iap_retention_14day_user_cnt)/sum(iap_user_cnt),4) iap_retention_14,
			FORMAT(sum(iap_retention_30day_user_cnt)/sum(iap_user_cnt),4) iap_retention_30,
			FORMAT(sum(iap_retention_60day_user_cnt)/sum(iap_user_cnt),4) iap_retention_60
		from
		(
			select tdate,appid,app_name,download_channel,brand,model,os_ver,
			reg_user_cnt,reg_retention_1day_user_cnt,reg_retention_2day_user_cnt,
			reg_retention_3day_user_cnt,reg_retention_4day_user_cnt,reg_retention_5day_user_cnt,
			reg_retention_6day_user_cnt,reg_retention_7day_user_cnt,reg_retention_14day_user_cnt,
			reg_retention_30day_user_cnt,reg_retention_60day_user_cnt,

			0 active_user_cnt,0 active_retention_1day_user_cnt,0 active_retention_2day_user_cnt,
			0 active_retention_3day_user_cnt,0 active_retention_4day_user_cnt,0 active_retention_5day_user_cnt,
			0 active_retention_6day_user_cnt,0 active_retention_7day_user_cnt,0 active_retention_14day_user_cnt,
			0 active_retention_30day_user_cnt,0 active_retention_60day_user_cnt,

			0 iap_user_cnt ,0 iap_retention_1day_user_cnt ,0 iap_retention_2day_user_cnt,
			0 iap_retention_3day_user_cnt,0 iap_retention_4day_user_cnt,0 iap_retention_5day_user_cnt,
			0 iap_retention_6day_user_cnt,0 iap_retention_7day_user_cnt,0 iap_retention_14day_user_cnt,
			0 iap_retention_30day_user_cnt,0 iap_retention_60day_user_cnt

			from ads_wechat_device_brand_model_os_reg_retention_daily where
			<if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
				tdate between #{start_date} and #{end_date}
			</if>
			union all

			select tdate,appid,app_name,download_channel,brand,model,os_ver,
			0 reg_user_cnt,0 reg_retention_1day_user_cnt,0 reg_retention_2day_user_cnt,
			0 reg_retention_3day_user_cnt,0 reg_retention_4day_user_cnt,0 reg_retention_5day_user_cnt,
			0 reg_retention_6day_user_cnt,0 reg_retention_7day_user_cnt,0 reg_retention_14day_user_cnt,
			0 reg_retention_30day_user_cnt,0 reg_retention_60day_user_cnt,

			active_user_cnt,active_retention_1day_user_cnt,active_retention_2day_user_cnt,
			active_retention_3day_user_cnt,active_retention_4day_user_cnt,active_retention_5day_user_cnt,
			active_retention_6day_user_cnt,active_retention_7day_user_cnt,active_retention_14day_user_cnt,
			active_retention_30day_user_cnt,active_retention_60day_user_cnt,

			0 iap_user_cnt ,0 iap_retention_1day_user_cnt ,0 iap_retention_2day_user_cnt,
			0 iap_retention_3day_user_cnt,0 iap_retention_4day_user_cnt,0 iap_retention_5day_user_cnt,
			0 iap_retention_6day_user_cnt,0 iap_retention_7day_user_cnt,0 iap_retention_14day_user_cnt,
			0 iap_retention_30day_user_cnt,0 iap_retention_60day_user_cnt

			from ads_wechat_device_brand_model_os_active_retention_daily where
			<if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
				tdate between #{start_date} and #{end_date}
			</if>

			union all

			select tdate,appid,app_name,download_channel,brand,model,os_ver,
			0 reg_user_cnt,0 active_user_cnt,0 reg_retention_1day_user_cnt,0 reg_retention_2day_user_cnt,
			0 reg_retention_3day_user_cnt,0 reg_retention_4day_user_cnt,0 reg_retention_5day_user_cnt,
			0 reg_retention_6day_user_cnt,0 reg_retention_7day_user_cnt,0 reg_retention_14day_user_cnt,
			0 reg_retention_30day_user_cnt,0 reg_retention_60day_user_cnt,

			0 active_retention_1day_user_cnt,0 active_retention_2day_user_cnt,
			0 active_retention_3day_user_cnt,0 active_retention_4day_user_cnt,0 active_retention_5day_user_cnt,
			0 active_retention_6day_user_cnt,0 active_retention_7day_user_cnt,0 active_retention_14day_user_cnt,
			0 active_retention_30day_user_cnt,0 active_retention_60day_user_cnt,

			iap_user_cnt ,iap_retention_1day_user_cnt ,iap_retention_2day_user_cnt,
			iap_retention_3day_user_cnt,iap_retention_4day_user_cnt,iap_retention_5day_user_cnt,
			iap_retention_6day_user_cnt,iap_retention_7day_user_cnt,iap_retention_14day_user_cnt,
			iap_retention_30day_user_cnt,iap_retention_60day_user_cnt

			from ads_wechat_device_brand_model_os_iap_retention_daily where
			<if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
				tdate between #{start_date} and #{end_date}
			</if>
		) a where 1=1
		<if test="start_date != null and start_date != ''">
			and tdate between #{start_date} and #{end_date}
		</if>
		<if test="appidList != null and appidList.size > 0">
			AND appid IN
			<foreach collection="appidList" item="appid" open="(" separator="," close=")">
				#{appid}
			</foreach>
		</if>
		<if test="brand != null and brand != ''">
			and brand = #{brand}
		</if>
		<if test="model != null and model != ''">
			and model = #{model}
		</if>
		<if test="os_ver != null and os_ver != ''">
			and os_ver = #{os_ver}
		</if>
		<if test="group != null and group != ''">
			group by ${group}
		</if>
		having sum(active_user_cnt)>100 or sum(reg_user_cnt)>100 or  sum(iap_user_cnt)>100
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate asc , active_user_cnt desc
			</otherwise>
		</choose>
	</select>


	<select id="getMicGameModelRetentionSum" parameterType="com.wbgame.pojo.game.report.MicGameModelRetentionReportVo"
			resultType="com.wbgame.pojo.game.report.MicGameModelRetentionReportVo">
		select
			sum(reg_user_cnt) reg_user_cnt,
			sum(active_user_cnt) active_user_cnt,
			sum(iap_user_cnt) iap_user_cnt,

			FORMAT(sum(reg_retention_1day_user_cnt)/sum(reg_user_cnt),4) reg_retention_1,
			FORMAT(sum(reg_retention_2day_user_cnt)/sum(reg_user_cnt),4) reg_retention_3,
			FORMAT(sum(reg_retention_6day_user_cnt)/sum(reg_user_cnt),4) reg_retention_7,
			FORMAT(sum(reg_retention_14day_user_cnt)/sum(reg_user_cnt),4) reg_retention_14,
			FORMAT(sum(reg_retention_30day_user_cnt)/sum(reg_user_cnt),4) reg_retention_30,
			FORMAT(sum(reg_retention_60day_user_cnt)/sum(reg_user_cnt),4) reg_retention_60,

			FORMAT(sum(active_retention_1day_user_cnt)/sum(active_user_cnt),4) active_retention_1,
			FORMAT(sum(active_retention_2day_user_cnt)/sum(active_user_cnt),4) active_retention_3,
			FORMAT(sum(active_retention_6day_user_cnt)/sum(active_user_cnt),4) active_retention_7,
			FORMAT(sum(active_retention_14day_user_cnt)/sum(active_user_cnt),4) active_retention_14,
			FORMAT(sum(active_retention_30day_user_cnt)/sum(active_user_cnt),4) active_retention_30,
			FORMAT(sum(active_retention_60day_user_cnt)/sum(active_user_cnt),4) active_retention_60,

			FORMAT(sum(iap_retention_1day_user_cnt)/sum(iap_user_cnt),4) iap_retention_1,
			FORMAT(sum(iap_retention_2day_user_cnt)/sum(iap_user_cnt),4) iap_retention_3,
			FORMAT(sum(iap_retention_6day_user_cnt)/sum(iap_user_cnt),4) iap_retention_7,
			FORMAT(sum(iap_retention_14day_user_cnt)/sum(iap_user_cnt),4) iap_retention_14,
			FORMAT(sum(iap_retention_30day_user_cnt)/sum(iap_user_cnt),4) iap_retention_30,
			FORMAT(sum(iap_retention_60day_user_cnt)/sum(iap_user_cnt),4) iap_retention_60
		from (

		select
		<if test="group != null and group != ''">
			${group},
		</if>
		app_name,
		sum(reg_user_cnt) reg_user_cnt,
		sum(active_user_cnt) active_user_cnt,
		sum(iap_user_cnt) iap_user_cnt,

		sum(reg_retention_1day_user_cnt) reg_retention_1day_user_cnt,
		sum(reg_retention_2day_user_cnt) reg_retention_2day_user_cnt,
		sum(reg_retention_6day_user_cnt) reg_retention_6day_user_cnt,
		sum(reg_retention_14day_user_cnt) reg_retention_14day_user_cnt,
		sum(reg_retention_30day_user_cnt) reg_retention_30day_user_cnt,
		sum(reg_retention_60day_user_cnt) reg_retention_60day_user_cnt,

		sum(active_retention_1day_user_cnt) active_retention_1day_user_cnt,
		sum(active_retention_2day_user_cnt) active_retention_2day_user_cnt,
		sum(active_retention_6day_user_cnt) active_retention_6day_user_cnt,
		sum(active_retention_14day_user_cnt) active_retention_14day_user_cnt,
		sum(active_retention_30day_user_cnt) active_retention_30day_user_cnt,
		sum(active_retention_60day_user_cnt) active_retention_60day_user_cnt,

		sum(iap_retention_1day_user_cnt) iap_retention_1day_user_cnt,
		sum(iap_retention_2day_user_cnt) iap_retention_2day_user_cnt,
		sum(iap_retention_6day_user_cnt) iap_retention_6day_user_cnt,
		sum(iap_retention_14day_user_cnt) iap_retention_14day_user_cnt,
		sum(iap_retention_30day_user_cnt) iap_retention_30day_user_cnt,
		sum(iap_retention_60day_user_cnt) iap_retention_60day_user_cnt

		from
		(
		select tdate,appid,app_name,download_channel,brand,model,os_ver,
		reg_user_cnt,reg_retention_1day_user_cnt,reg_retention_2day_user_cnt,
		reg_retention_3day_user_cnt,reg_retention_4day_user_cnt,reg_retention_5day_user_cnt,
		reg_retention_6day_user_cnt,reg_retention_7day_user_cnt,reg_retention_14day_user_cnt,
		reg_retention_30day_user_cnt,reg_retention_60day_user_cnt,

		0 active_user_cnt,0 active_retention_1day_user_cnt,0 active_retention_2day_user_cnt,
		0 active_retention_3day_user_cnt,0 active_retention_4day_user_cnt,0 active_retention_5day_user_cnt,
		0 active_retention_6day_user_cnt,0 active_retention_7day_user_cnt,0 active_retention_14day_user_cnt,
		0 active_retention_30day_user_cnt,0 active_retention_60day_user_cnt,

		0 iap_user_cnt ,0 iap_retention_1day_user_cnt ,0 iap_retention_2day_user_cnt,
		0 iap_retention_3day_user_cnt,0 iap_retention_4day_user_cnt,0 iap_retention_5day_user_cnt,
		0 iap_retention_6day_user_cnt,0 iap_retention_7day_user_cnt,0 iap_retention_14day_user_cnt,
		0 iap_retention_30day_user_cnt,0 iap_retention_60day_user_cnt

		from ads_wechat_device_brand_model_os_reg_retention_daily where
		<if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
			tdate between #{start_date} and #{end_date}
		</if>
		union all

		select tdate,appid,app_name,download_channel,brand,model,os_ver,
		0 reg_user_cnt,0 reg_retention_1day_user_cnt,0 reg_retention_2day_user_cnt,
		0 reg_retention_3day_user_cnt,0 reg_retention_4day_user_cnt,0 reg_retention_5day_user_cnt,
		0 reg_retention_6day_user_cnt,0 reg_retention_7day_user_cnt,0 reg_retention_14day_user_cnt,
		0 reg_retention_30day_user_cnt,0 reg_retention_60day_user_cnt,

		active_user_cnt,active_retention_1day_user_cnt,active_retention_2day_user_cnt,
		active_retention_3day_user_cnt,active_retention_4day_user_cnt,active_retention_5day_user_cnt,
		active_retention_6day_user_cnt,active_retention_7day_user_cnt,active_retention_14day_user_cnt,
		active_retention_30day_user_cnt,active_retention_60day_user_cnt,

		0 iap_user_cnt ,0 iap_retention_1day_user_cnt ,0 iap_retention_2day_user_cnt,
		0 iap_retention_3day_user_cnt,0 iap_retention_4day_user_cnt,0 iap_retention_5day_user_cnt,
		0 iap_retention_6day_user_cnt,0 iap_retention_7day_user_cnt,0 iap_retention_14day_user_cnt,
		0 iap_retention_30day_user_cnt,0 iap_retention_60day_user_cnt

		from ads_wechat_device_brand_model_os_active_retention_daily where
		<if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
			tdate between #{start_date} and #{end_date}
		</if>

		union all

		select tdate,appid,app_name,download_channel,brand,model,os_ver,
		0 reg_user_cnt,0 active_user_cnt,0 reg_retention_1day_user_cnt,0 reg_retention_2day_user_cnt,
		0 reg_retention_3day_user_cnt,0 reg_retention_4day_user_cnt,0 reg_retention_5day_user_cnt,
		0 reg_retention_6day_user_cnt,0 reg_retention_7day_user_cnt,0 reg_retention_14day_user_cnt,
		0 reg_retention_30day_user_cnt,0 reg_retention_60day_user_cnt,

		0 active_retention_1day_user_cnt,0 active_retention_2day_user_cnt,
		0 active_retention_3day_user_cnt,0 active_retention_4day_user_cnt,0 active_retention_5day_user_cnt,
		0 active_retention_6day_user_cnt,0 active_retention_7day_user_cnt,0 active_retention_14day_user_cnt,
		0 active_retention_30day_user_cnt,0 active_retention_60day_user_cnt,

		iap_user_cnt ,iap_retention_1day_user_cnt ,iap_retention_2day_user_cnt,
		iap_retention_3day_user_cnt,iap_retention_4day_user_cnt,iap_retention_5day_user_cnt,
		iap_retention_6day_user_cnt,iap_retention_7day_user_cnt,iap_retention_14day_user_cnt,
		iap_retention_30day_user_cnt,iap_retention_60day_user_cnt

		from ads_wechat_device_brand_model_os_iap_retention_daily where
		<if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
			tdate between #{start_date} and #{end_date}
		</if>
		) a where 1=1

		<if test="start_date != null and start_date != ''">
			and tdate between #{start_date} and #{end_date}
		</if>
		<if test="appidList != null and appidList.size > 0">
			AND appid IN
			<foreach collection="appidList" item="appid" open="(" separator="," close=")">
				#{appid}
			</foreach>
		</if>
		<if test="brand != null and brand != ''">
			and brand = #{brand}
		</if>
		<if test="model != null and model != ''">
			and model = #{model}
		</if>
		<if test="os_ver != null and os_ver != ''">
			and os_ver = #{os_ver}
		</if>
		<if test="group != null and group != ''">
			group by ${group}
		</if>
		having sum(active_user_cnt)>100 or sum(reg_user_cnt)>100 or  sum(iap_user_cnt)>100
		) b
	</select>

	<select id="getActiveActivityList" parameterType="com.wbgame.pojo.game.report.ActiveActivityReportVo" resultType="com.wbgame.pojo.game.report.ActiveActivityReportVo">
		select
		<if test="group != null and group != ''">
			${group},
		</if>
		app_name,
		sum(active_user_cnt) active_user_cnt,
		FORMAT(sum(iap_revenue_total/100),2) iap_revenue_total,
		sum(activity_user_cnt) activity_user_cnt,

		FORMAT(sum(activity_user_cnt)/sum(active_user_cnt),4) activity_ratio,
		sum(iap_user_cnt) iap_user_cnt,
		FORMAT(sum(iap_revenue)/100,2) iap_revenue,

		FORMAT(sum(iap_user_cnt)/sum(activity_user_cnt),4) iap_ratio,
		FORMAT(sum(iap_revenue)/sum(iap_user_cnt)/100,2) iap_arpu,
		FORMAT(sum(iap_revenue)/sum(iap_revenue_total),4) iap_revenue_ratio
		from (
		 	select tdate,appid,app_name,group_id,activity_id,active_user_cnt,
		 	iap_revenue_total,activity_user_cnt,iap_user_cnt,iap_revenue
		 	from ads_group_user_activity_iap_analysis_daily where 1=1
			<if test="start_date != null and start_date != ''">
				and tdate between #{start_date} and #{end_date}
			</if>
			union all
			select tdate,appid,app_name,group_id,activity_id,active_user_cnt,
			iap_revenue_total,activity_user_cnt,iap_user_cnt,iap_revenue
			from ads_wechat_group_user_activity_iap_analysis_daily where 1=1
			<if test="start_date != null and start_date != ''">
				and tdate between #{start_date} and #{end_date}
			</if>
		) a where 1=1
		<if test="appidList != null and appidList.size > 0">
			AND appid IN
			<foreach collection="appidList" item="appid" open="(" separator="," close=")">
				#{appid}
			</foreach>
		</if>
		<if test="group_idList != null and group_idList.size > 0">
			AND group_id IN
			<foreach collection="group_idList" item="group_id" open="(" separator="," close=")">
				#{group_id}
			</foreach>
		</if>
		<if test="activity_idList != null and activity_idList.size > 0">
			AND activity_id IN
			<foreach collection="activity_idList" item="activityid" open="(" separator="," close=")">
				#{activityid}
			</foreach>
		</if>
		<if test="group != null and group != ''">
			group by ${group}
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate asc , active_user_cnt desc
			</otherwise>
		</choose>
	</select>

	<select id="getActiveActivitySum" parameterType="com.wbgame.pojo.game.report.ActiveActivityReportVo" resultType="com.wbgame.pojo.game.report.ActiveActivityReportVo">
		select

		sum(active_user_cnt) active_user_cnt,
		FORMAT(sum(iap_revenue_total/100),2) iap_revenue_total,
		sum(activity_user_cnt) activity_user_cnt,

		FORMAT(sum(activity_user_cnt)/sum(active_user_cnt),4) activity_ratio,
		sum(iap_user_cnt) iap_user_cnt,
		FORMAT(sum(iap_revenue)/100,2) iap_revenue,

		FORMAT(sum(iap_user_cnt)/sum(activity_user_cnt),4) iap_ratio,
		FORMAT(sum(iap_revenue)/sum(iap_user_cnt)/100,2) iap_arpu,
		FORMAT(sum(iap_revenue)/sum(iap_revenue_total),4) iap_revenue_ratio
		from (
		select tdate,appid,app_name,group_id,activity_id,active_user_cnt,
		iap_revenue_total,activity_user_cnt,iap_user_cnt,iap_revenue
		from ads_group_user_activity_iap_analysis_daily where 1=1
		<if test="start_date != null and start_date != ''">
			and tdate between #{start_date} and #{end_date}
		</if>
		union all
		select tdate,appid,app_name,group_id,activity_id,active_user_cnt,
		iap_revenue_total,activity_user_cnt,iap_user_cnt,iap_revenue
		from ads_wechat_group_user_activity_iap_analysis_daily where 1=1
		<if test="start_date != null and start_date != ''">
			and tdate between #{start_date} and #{end_date}
		</if>
		) a where 1=1
		<if test="appidList != null and appidList.size > 0">
			AND appid IN
			<foreach collection="appidList" item="appid" open="(" separator="," close=")">
				#{appid}
			</foreach>
		</if>
		<if test="group_idList != null and group_idList.size > 0">
			AND group_id IN
			<foreach collection="group_idList" item="group_id" open="(" separator="," close=")">
				#{group_id}
			</foreach>
		</if>
		<if test="activity_idList != null and activity_idList.size > 0">
			AND activity_id IN
			<foreach collection="activity_idList" item="activityid" open="(" separator="," close=")">
				#{activityid}
			</foreach>
		</if>
	</select>

	<select id="getActiveCurrencyList" parameterType="com.wbgame.pojo.game.report.ActiveCurrencyReportVo" resultType="com.wbgame.pojo.game.report.ActiveCurrencyReportVo">
		select
		t_date,appid,group_id,token_coin_avg,token_power_avg,
		token_diamond_avg,token_coin_median,token_power_median,token_diamond_median
		from ads_active_token_check_daily where 1=1
		<if test="appidList != null and appidList.size > 0">
			AND appid IN
			<foreach collection="appidList" item="appid" open="(" separator="," close=")">
				#{appid}
			</foreach>
		</if>
		<if test="group_idList != null and group_idList.size > 0">
			AND group_id IN
			<foreach collection="group_idList" item="group_id" open="(" separator="," close=")">
				#{group_id}
			</foreach>
		</if>
		<if test="start_date != null and start_date != ''">
			and t_date between #{start_date} and #{end_date}
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by t_date asc , token_coin_avg desc
			</otherwise>
		</choose>
	</select>
</mapper>
