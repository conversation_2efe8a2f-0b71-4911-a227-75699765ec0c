<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adb.ToolMapper">

    <select id="getToolEventList" resultType="com.wbgame.pojo.game.report.ToolEventVo" parameterType="java.util.Map">
        select DISTINCT(name_k_v) name_k_v from dnwx_bi.dim_name_k_v
    </select>

    <select id="getToolEventListLimit" resultType="com.wbgame.pojo.game.report.ToolEventVo" parameterType="java.util.Map">
        select DISTINCT(name_k_v) name_k_v from dnwx_bi.dim_name_k_v ORDER BY name_k_v asc limit 200
    </select>

    <select id="getToolEventDataList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.report.ToolEventVo">
        select ds,appid,pid,channel,name_k_v,is_auto,active_cat,sum(uv) uv,sum(pv) pv,brand, os_ver
        from dnwx_bi.ads_tool_event_funnel_daily where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="is_auto != null and is_auto != ''">
            and is_auto = #{is_auto}
        </if>
        <if test="active_cat != null and active_cat != ''">
            and active_cat = #{active_cat}
        </if>
        <if test="name_k_v != null and name_k_v != ''">
            and name_k_v in (${name_k_v})
        </if>
        <if test="brand != null and brand != ''">
            and LOWER(brand) in (${brand})
        </if>
        <if test="os_ver != null and os_ver != ''">
            and os_ver = #{os_ver}
        </if>
        and DATE(ds) <![CDATA[>=]]> #{start_date} and DATE(ds) <![CDATA[<=]]> #{end_date}
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by ds asc,uv desc
            </otherwise>
        </choose>
    </select>


    <select id="getToolEventDataSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.report.ToolEventVo">
        select sum(uv) uv,sum(pv) pv
        from dnwx_bi.ads_tool_event_funnel_daily where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="is_auto != null and is_auto != ''">
            and is_auto = #{is_auto}
        </if>
        <if test="active_cat != null and active_cat != ''">
            and active_cat = #{active_cat}
        </if>
        <if test="name_k_v != null and name_k_v != ''">
            and name_k_v in (${name_k_v})
        </if>
        <if test="brand != null and brand != ''">
            and LOWER(brand) in (${brand})
        </if>
        <if test="os_ver != null and os_ver != ''">
            and os_ver = #{os_ver}
        </if>
        and DATE(ds) <![CDATA[>=]]> #{start_date} and DATE(ds) <![CDATA[<=]]> #{end_date}
    </select>

    <select id="getMicGameEventList" parameterType="com.wbgame.pojo.game.report.query.MGEventReportQueryVo" resultType="com.wbgame.pojo.game.report.MGEventNameVo">
        select distinct(event_id) event_id from dnwx_bi.ads_wechat_event_funnel_daily where 1=1
        <if test="appid != null and appid.size != 0">
            and appid in
            <foreach collection="appid" index="index" item="appid"
                     open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        and DATE(tdate) <![CDATA[>=]]> #{start_date} and DATE(tdate) <![CDATA[<=]]> #{end_date} order by event_id asc
    </select>

    <select id="getMicGameEventReportList" parameterType="com.wbgame.pojo.game.report.query.MGEventReportQueryVo" resultType="com.wbgame.pojo.game.report.MGEventReportVo">
        select tdate,appid,event_id,version,brand,model,sum(new_user_cnt) new_user_cnt,sum(old_user_cnt) old_user_cnt
        from dnwx_bi.ads_wechat_event_funnel_daily where 1=1
        <if test="appid != null and appid.size != 0">
            and appid in
            <foreach collection="appid" index="index" item="appid"
                     open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        <if test="event_id != null and event_id.size != 0">
            and event_id in
            <foreach collection="event_id" index="index" item="event_id"
                     open="(" separator="," close=")">
                #{event_id}
            </foreach>
        </if>
        <if test="version != null and version.size != 0">
            and version in
            <foreach collection="version" index="index" item="version"
                     open="(" separator="," close=")">
                #{version}
            </foreach>
        </if>
        <if test="brand != null and brand.size != 0">
            and brand in
            <foreach collection="brand" index="index" item="brand"
                     open="(" separator="," close=")">
                #{brand}
            </foreach>
        </if>
        <if test="model != null and model != ''">
            and model in (${model})
        </if>
        and DATE(tdate) <![CDATA[>=]]> #{start_date} and DATE(tdate) <![CDATA[<=]]> #{end_date}
        <if test="group != null and group.size != 0">
            group by
            <foreach collection="group" index="index" item="group" separator="," >
                ${group}
            </foreach>
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc,new_user_cnt desc,event_id asc
            </otherwise>
        </choose>
    </select>

    <select id="getMicGameEventReportSum" parameterType="com.wbgame.pojo.game.report.query.MGEventReportQueryVo" resultType="com.wbgame.pojo.game.report.MGEventReportVo">
        select sum(new_user_cnt) new_user_cnt,sum(old_user_cnt) old_user_cnt
        from dnwx_bi.ads_wechat_event_funnel_daily where 1=1
        <if test="appid != null and appid.size != 0">
            and appid in
            <foreach collection="appid" index="index" item="appid"
                     open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        <if test="event_id != null and event_id.size != 0">
            and event_id in
            <foreach collection="event_id" index="index" item="event_id"
                     open="(" separator="," close=")">
                #{event_id}
            </foreach>
        </if>
        <if test="version != null and version.size != 0">
            and version in
            <foreach collection="version" index="index" item="version"
                     open="(" separator="," close=")">
                #{version}
            </foreach>
        </if>
        <if test="brand != null and brand.size != 0">
            and brand in
            <foreach collection="brand" index="index" item="brand"
                     open="(" separator="," close=")">
                #{brand}
            </foreach>
        </if>
        <if test="model != null and model != ''">
            and model in (${model})
        </if>
        and DATE(tdate) <![CDATA[>=]]> #{start_date} and DATE(tdate) <![CDATA[<=]]> #{end_date}
    </select>

    <select id="selectMGEventReportTable" resultType="com.alibaba.fastjson.JSONObject">
        select ${group_str}
        ,ifnull(round(sum(home_new_users)/sum(login_new_users)*100,2),0) home_new_rate
        ,ifnull(round(sum(home_old_users)/sum(login_old_users)*100,2),0) home_old_rate
        ,ifnull(round(sum(load_new_users)/sum(login_new_users)*100,2),0) load_new_rate
        ,ifnull(round(sum(load_old_users)/sum(login_old_users)*100,2),0) load_old_rate
        from (select ${group_str}
        ,sum(CASE when event_id = 'app_home_show' then new_user_cnt end) home_new_users
        ,sum(CASE when event_id = 'app_home_show' then old_user_cnt end) home_old_users
        ,sum(CASE when event_id = 'app_loading_show' then new_user_cnt end) load_new_users
        ,sum(CASE when event_id = 'app_loading_show' then old_user_cnt end) load_old_users
        ,sum(CASE when event_id = 'login' then new_user_cnt end) login_new_users
        ,sum(CASE when event_id = 'login' then old_user_cnt end) login_old_users
        from dnwx_bi.ads_wechat_event_funnel_daily
        WHERE 1=1
        <if test="appid != null and appid.size != 0">
        and appid in
        <foreach collection="appid" index="index" item="appid"
                 open="(" separator="," close=")">
            #{appid}
        </foreach>
        </if>
        <if test="version != null and version.size != 0">
            and version in
            <foreach collection="version" index="index" item="version"
                     open="(" separator="," close=")">
                #{version}
            </foreach>
        </if>
        <if test="brand != null and brand.size != 0">
            and brand in
            <foreach collection="brand" index="index" item="brand"
                     open="(" separator="," close=")">
                #{brand}
            </foreach>
        </if>
        <if test="model != null and model != ''">
            and model in (${model})
        </if>
         and DATE(tdate) <![CDATA[>=]]> #{start_date} and DATE(tdate) <![CDATA[<=]]> #{end_date}
        GROUP BY ${group_str}) a
        where login_new_users >= 100 or login_old_users >= 100
        group by ${group_str}
        order by ${group_str}
    </select>

    <select id="selectMGEventReportData" resultType="java.lang.String">
        select version
        from dnwx_bi.ads_wechat_event_funnel_daily
        WHERE DATE(tdate) <![CDATA[>=]]> #{start_date} and DATE(tdate) <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid.size != 0">
            and appid in
            <foreach collection="appid" index="index" item="appid"
                     open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        group by version
    </select>

</mapper>