<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adb.ADBUmengMapper">

    <insert id="batchInsertUserChannelTotal">
        insert ignore into data_ym.umeng_user_channel_total (tdate, appid, app_key,
        add_num, act_num,start_num, install_channel) values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.tdate},#{item.appid}, #{item.appKey,jdbcType=VARCHAR},
            #{item.addNum,jdbcType=INTEGER}, #{item.actNum,jdbcType=INTEGER},
            #{item.startNum,jdbcType=INTEGER}, #{item.installChannel,jdbcType=INTEGER})
        </foreach>
    </insert>

    <insert id="replaceIntoUserChannelTotal">
        replace into data_ym.umeng_user_channel_total (tdate, appid, app_key,
        add_num, act_num,start_num, install_channel,duration,daily_per_duration) values
        <foreach collection="channels" index="index" item="item" separator=",">
            (#{item.tdate},#{item.appid}, #{item.appKey,jdbcType=VARCHAR},
            #{item.addNum,jdbcType=INTEGER}, #{item.actNum,jdbcType=INTEGER},
            #{item.startNum,jdbcType=INTEGER}, #{item.installChannel,jdbcType=VARCHAR},
            #{item.duration,jdbcType=VARCHAR}, #{item.dailyPerDuration,jdbcType=VARCHAR})
        </foreach>
    </insert>



    <select id="selectUmengBaseReport" resultType="java.util.Map">
        select tdate,appid,app_name,app_category,install_channel,app_version,'中国' as country,add_num,act_num,second_avg,single_avg,start_num,start_rate
        ,concat(round(error_rate*100,2),'%') error_rate
        ,concat(round(keep1*100,2),'%') keep1
        ,concat(round(keep2*100,2),'%') keep2
        ,concat(round(keep3*100,2),'%') keep3
        ,concat(round(keep4*100,2),'%') keep4
        ,concat(round(keep5*100,2),'%') keep5
        ,concat(round(keep6*100,2),'%') keep6
        ,concat(round(keep7*100,2),'%') keep7
        ,concat(round(keep14*100,2),'%') keep14
        from (select
                a.a_day tdate,
                a.appid,
                app_name,
                c.app_category,
                a.install_channel,
                a.app_version,
                IFNULL(sum(a.add_num),0) add_num,
                IFNULL(sum(a.act_num),0) act_num,
                IFNULL(a.second_avg,0) second_avg,
                IFNULL(a.single_avg,0) single_avg,
                IFNULL(sum(a.start_num),0) start_num,
                IFNULL(round(sum(a.start_num)/sum(a.act_num),2),0) start_rate,
                IFNULL(round(sum(a.error_num)/sum(start_num),4),0) error_rate,
                IFNULL(round(sum(keep1)/sum(b.add_num),4),0) keep1,
                IFNULL(round(sum(keep2)/sum(b.add_num),4),0) keep2,
                IFNULL(round(sum(keep3)/sum(b.add_num),4),0) keep3,
                IFNULL(round(sum(keep4)/sum(b.add_num),4),0) keep4,
                IFNULL(round(sum(keep5)/sum(b.add_num),4),0) keep5,
                IFNULL(round(sum(keep6)/sum(b.add_num),4),0) keep6,
                IFNULL(round(sum(keep7)/sum(b.add_num),4),0) keep7,
                IFNULL(round(sum(keep14)/sum(b.add_num),4),0) keep14
        from ads_umeng_custom_total_daily a
        left join ads_umeng_user_app_keep_daily b
        on a.a_day = b.a_day and a.appid = b.appid and a.install_channel = b.install_channel and a.app_version = b.app_version
        left join dim_app_game c on a.appid = c.appid
        where a.a_day between #{startTime} and #{endTime}
        <if test="appid != null and appid != ''">
            and a.appid in ${appid}
        </if>
        <if test="channel != null and channel != ''">
            and a.install_channel in ${channel}
        </if>
        <if test="version != null and version != ''">
            and a.app_version in ${version}
        </if>
        <if test="app_category != null and app_category != ''">
            and c.app_category = #{app_category}
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        order by ${order_str}
        ) a

    </select>

    <select id="countUmengBaseReport" resultType="java.util.Map">
        select sum(add_num) add_num,sum(act_num) act_num
        ,sum(start_num) start_num,round(sum(start_num)/sum(act_num),2) start_rate
        from ads_umeng_custom_total_daily a left join dim_app_game b on a.appid = b.appid
        where a.a_day between #{startTime} and #{endTime}
        <if test="appid != null and appid != ''">
            and a.appid in ${appid}
        </if>
        <if test="channel != null and channel != ''">
            and install_channel in ${channel}
        </if>
        <if test="version != null and version != ''">
            and app_version in ${version}
        </if>
        <if test="app_category != null and app_category != ''">
            and app_category = #{app_category}
        </if>
    </select>

    <select id="selectDeviceReport" resultType="java.lang.String">
        select std_device_model from data_ym.umeng_device_report
    </select>

    <select id="selectAdvSQL" resultType="com.wbgame.pojo.AdvEvent">
        select ad_sid id,ad_type type
        from ads_umeng_ad_data_all_daily
        where a_day between #{startTime} and #{endTime}
        and appid = #{appid} and ad_type = #{module}
        <if test="app_type != null and app_type != ''">
            <choose>
                <when test="app_type == 'app_outside'">
                    and ad_sid in ('lock_msg','lock_kuaishou','news_landing','lock_splash','game_clean','game_mfzs','game_draw','game_splash','game_msg','game_video','game_ad','game_push','olanding_msg','olanding_plaque')
                </when>
                <otherwise>
                    and ad_sid not in ('lock_msg','lock_kuaishou','news_landing','lock_splash','game_clean','game_mfzs','game_draw','game_splash','game_msg','game_video','game_ad','game_push','olanding_msg','olanding_plaque')
                </otherwise>
            </choose>
        </if>
        GROUP BY
        ad_sid
        ,ad_type
    </select>

    <select id="selectAdvAppInOuttReport" resultType="java.util.Map">
        select a.a_day ds,b.app_name,a.app_version
             ,a.install_channel as install_channel,if(a.user_type=0,'老用户',if(a.user_type=1,'新用户','全部用户')) user_type
        ,if(a.user_type=0,act_num-a.add_num,if(a.user_type=1,a.add_num,act_num)) act_num
        ,if(a.user_type=0,0,if(a.user_type=1,a.add_num,a.add_num)) add_num
        ,ifnull(c.keep_rate,0) keep_rate
        ,case when ad_sid in ('lock_msg','lock_kuaishou','news_landing','lock_splash','game_clean','game_mfzs','game_draw','game_splash','game_msg','game_video','game_ad','game_push','olanding_msg','olanding_plaque') then '应用外'
        else '应用内' end app_type
        ,ifnull(round(sum(case when ad_type = 'video' and a.type = 1 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) video_sum_1
        ,ifnull(round(sum(case when ad_type = 'video' and a.type = 2 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) video_sum_2
        ,ifnull(round(sum(case when ad_type = 'splash' and a.type = 1 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) splash_sum_1
        ,ifnull(round(sum(case when ad_type = 'splash' and a.type = 2 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) splash_sum_2
        ,ifnull(round(sum(case when ad_type = 'msg' and a.type = 1 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) msg_sum_1
        ,ifnull(round(sum(case when ad_type = 'msg' and a.type = 2 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) msg_sum_2
        ,ifnull(round(sum(case when ad_type = 'plaque' and a.type = 1 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) plaque_sum_1
        ,ifnull(round(sum(case when ad_type = 'plaque' and a.type = 2 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) plaque_sum_2
        <if test="adv != null and adv != ''">
            , ${adv}
        </if>
        from ads_umeng_ad_data_all_daily a
        left join dim_app_game b on a.appid = b.appid
        left join ads_umeng_custom_keep_daily c on a.app_version = c.app_version and a.install_channel = c.install_channel and a.a_day = c.a_day and a.appid = c.appid
        where a.a_day between #{startTime} and #{endTime}
        and a.appid = #{appid}
        <if test="install_channel !=null and install_channel != ''">
            and a.install_channel = #{install_channel}
        </if>
        <if test="app_version !=null and app_version != ''">
            and a.app_version = #{app_version}
        </if>
        <if test="user_type !=null and user_type != ''">
            and a.user_type = #{user_type}
        </if>
        <if test="module != 'all'">
            and ad_type = #{module}
        </if>
        <if test="app_type != null and app_type != ''">
            <choose>
                <when test="app_type == 'app_outside'">
                    and ad_sid in ('lock_msg','lock_kuaishou','news_landing','lock_splash','game_clean','game_mfzs','game_draw','game_splash','game_msg','game_video','game_ad','game_push','olanding_msg','olanding_plaque')
                </when>
                <otherwise>
                    and ad_sid not in ('lock_msg','lock_kuaishou','news_landing','lock_splash','game_clean','game_mfzs','game_draw','game_splash','game_msg','game_video','game_ad','game_push','olanding_msg','olanding_plaque')
                </otherwise>
            </choose>
        </if>
        group by a.a_day,a.install_channel,a.app_version,a.user_type,app_type
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by act_num desc
            </otherwise>
        </choose>
    </select>

    <select id="selectAdvEventHead" resultType="com.wbgame.pojo.AdvEvent">
        select
        CONCAT(ad_type,'_',a.ad_sid) id,CONCAT(ad_type,'_',ifnull(c.name,a.ad_sid)) name
        from ads_umeng_ad_data_all_daily a left join dim_app_game b
        on a.appid = b.appid
        left join (select id,name from adv_space_config where appid = #{appid}) c on a.ad_sid = c.id
        where a_day between #{startTime} and #{endTime}
        and a.appid = #{appid} and ad_type = #{module}
        <if test="install_channel !=null and install_channel != ''">
            and install_channel = #{install_channel}
        </if>
        <if test="app_version !=null and app_version != ''">
            and app_version = #{app_version}
        </if>
        <if test="app_type != null and app_type != ''">
            <choose>
                <when test="app_type == 'app_outside'">
                    and ad_sid in ('lock_msg','lock_kuaishou','news_landing','lock_splash','game_clean','game_mfzs','game_draw','game_splash','game_msg','game_video','game_ad','game_push','olanding_msg','olanding_plaque')
                </when>
                <otherwise>
                    and ad_sid not in ('lock_msg','lock_kuaishou','news_landing','lock_splash','game_clean','game_mfzs','game_draw','game_splash','game_msg','game_video','game_ad','game_push','olanding_msg','olanding_plaque')
                </otherwise>
            </choose>
        </if>
        GROUP BY
        a.ad_sid,a.ad_type
        order by a.ad_type
    </select>

    <select id="selectAdvEventReport" resultType="java.util.Map">
        select a.a_day ds,b.app_name,a.app_version as app_version,a.install_channel as install_channel,if(a.user_type=0,'老用户',if(a.user_type=1,'新用户','全部用户')) user_type,
        ifnull(act_num,0) act_num,ifnull(a.add_num,0) add_num,ifnull(c.keep_rate,0) keep_rate
        ,ifnull(round(sum(case when ad_type = 'video' and a.type = 1 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) video_sum_1
        ,ifnull(round(sum(case when ad_type = 'video' and a.type = 2 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) video_sum_2
        ,ifnull(round(sum(case when ad_type = 'splash' and a.type = 1 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) splash_sum_1
        ,ifnull(round(sum(case when ad_type = 'splash' and a.type = 2 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) splash_sum_2
        ,ifnull(round(sum(case when ad_type = 'msg' and a.type = 1 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) msg_sum_1
        ,ifnull(round(sum(case when ad_type = 'msg' and a.type = 2 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) msg_sum_2
        ,ifnull(round(sum(case when ad_type = 'plaque' and a.type = 1 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) plaque_sum_1
        ,ifnull(round(sum(case when ad_type = 'plaque' and a.type = 2 then time_num end)/if(a.user_type=0,(act_num-a.add_num),if(a.user_type=1,a.add_num,act_num)),2),0) plaque_sum_2
        <if test="adv != null and adv != ''">
            , ${adv}
        </if>
        from ads_umeng_ad_data_all_daily a
        left join dim_app_game b on a.appid = b.appid
        left join ads_umeng_custom_keep_daily c on a.app_version = c.app_version and a.install_channel = c.install_channel and a.a_day = c.a_day and a.appid = c.appid
        where a.a_day between #{startTime} and #{endTime}
        and a.appid = #{appid}
        <if test="install_channel !=null and install_channel != ''">
            and a.install_channel = #{install_channel}
        </if>
        <if test="app_version !=null and app_version != ''">
            and a.app_version = #{app_version}
        </if>
        <if test="user_type !=null and user_type != ''">
            and a.user_type = #{user_type}
        </if>
        <if test="module != 'all'">
            and ad_type = #{module}
        </if>
        group by a.a_day,a.install_channel,a.app_version,a.user_type
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by act_num desc
            </otherwise>
        </choose>
    </select>

    <select id="selectAdvSQL2" resultType="com.wbgame.pojo.AdvEvent">
        select ad_sid id,ad_type type
        from ads_umeng_ad_data_all_two_daily
        where a_day between #{startTime} and #{endTime}
        and appid = #{appid} and ad_type = #{module}
        GROUP BY
        ad_sid
        ,ad_type
    </select>

    <select id="selectAdvEventReport2" resultType="java.util.Map">
        select a.a_day ds,b.app_name,a.app_version  as app_version,a.app_channel as app_channel,if(a.user_type=0,'老用户',if(a.user_type=1,'新用户','全部用户')) user_type,
               ifnull(act_num,0) act_num,ifnull(a.up_num,0) up_num,ifnull(a.add_num,0) add_num,ifnull(c.keep_rate,0) keep_rate
        ,ifnull(round(sum(case when ad_type = 'video' and a.type = 1 then time_num end)/if(a.user_type=0,(act_num-a.add_num-a.up_num),if(a.user_type=1,(a.add_num+a.up_num),act_num)),2),0) video_sum_1
        ,ifnull(round(sum(case when ad_type = 'video' and a.type = 2 then time_num end)/if(a.user_type=0,(act_num-a.add_num-a.up_num),if(a.user_type=1,(a.add_num+a.up_num),act_num)),2),0) video_sum_2
        ,ifnull(round(sum(case when ad_type = 'splash' and a.type = 1 then time_num end)/if(a.user_type=0,(act_num-a.add_num-a.up_num),if(a.user_type=1,(a.add_num+a.up_num),act_num)),2),0) splash_sum_1
        ,ifnull(round(sum(case when ad_type = 'splash' and a.type = 2 then time_num end)/if(a.user_type=0,(act_num-a.add_num-a.up_num),if(a.user_type=1,(a.add_num+a.up_num),act_num)),2),0) splash_sum_2
        ,ifnull(round(sum(case when ad_type = 'msg' and a.type = 1 then time_num end)/if(a.user_type=0,(act_num-a.add_num-a.up_num),if(a.user_type=1,(a.add_num+a.up_num),act_num)),2),0) msg_sum_1
        ,ifnull(round(sum(case when ad_type = 'msg' and a.type = 2 then time_num end)/if(a.user_type=0,(act_num-a.add_num-a.up_num),if(a.user_type=1,(a.add_num+a.up_num),act_num)),2),0) msg_sum_2
        ,ifnull(round(sum(case when ad_type = 'plaque' and a.type = 1 then time_num end)/if(a.user_type=0,(act_num-a.add_num-a.up_num),if(a.user_type=1,(a.add_num+a.up_num),act_num)),2),0) plaque_sum_1
        ,ifnull(round(sum(case when ad_type = 'plaque' and a.type = 2 then time_num end)/if(a.user_type=0,(act_num-a.add_num-a.up_num),if(a.user_type=1,(a.add_num+a.up_num),act_num)),2),0) plaque_sum_2
        <if test="adv != null and adv != ''">
            , ${adv}
        </if>
        from ads_umeng_ad_data_all_two_daily a
        left join dim_app_game b on a.appid = b.appid
        left join ads_umeng_custom_keep_daily c on a.app_version = c.app_version and a.app_channel = c.install_channel and a.a_day = c.a_day and a.appid = c.appid
        where a.a_day between #{startTime} and #{endTime}
        and a.appid = #{appid}
        <if test="app_channel !=null and app_channel != ''">
            and a.app_channel = #{app_channel}
        </if>
        <if test="app_version !=null and app_version != ''">
            and a.app_version = #{app_version}
        </if>
        <if test="user_type !=null and user_type != ''">
            and a.user_type = #{user_type}
        </if>
        <if test="module != 'all'">
            and ad_type = #{module}
        </if>
        group by a.a_day,a.app_channel,a.app_version,a.user_type
         <choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
                order by act_num desc
			</otherwise>
		</choose>
    </select>

    <select id="selectAdvEventHead2" resultType="com.wbgame.pojo.AdvEvent">
        select
        CONCAT(ad_type,'_',a.ad_sid) id,CONCAT(ad_type,'_',ifnull(c.name,a.ad_sid)) name
        from ads_umeng_ad_data_all_two_daily a left join dim_app_game b
        on a.appid = b.appid
        left join (select id,name from adv_space_config where appid = #{appid}) c on a.ad_sid = c.id
        where a_day between #{startTime} and #{endTime}
        and a.appid = #{appid} and ad_type = #{module}
        <if test="app_channel !=null and app_channel != ''">
            and app_channel = #{app_channel}
        </if>
        <if test="app_version !=null and app_version != ''">
            and app_version = #{app_version}
        </if>
        GROUP BY
        a.ad_sid,a.ad_type
        order by a.ad_type
    </select>

    <select id="selectAppLockReport" resultType="java.util.Map">
        select a_day ds,install_channel,app_version,model,
                IFNULL(sum(case when event_name = 'B_lock_screen_start' then time_num end),0) as 'B_lock_screen_start_time',
                IFNULL(sum(case when event_name = 'B_lock_screen_start' then person_num end),0) as 'B_lock_screen_start_person',
                IFNULL(sum(case when event_name = 'B_lock_screen_started' then time_num end),0) as 'B_lock_screen_started_time',
                IFNULL(sum(case when event_name = 'B_lock_screen_started' then person_num end),0) as 'B_lock_screen_started_person',
                IFNULL(sum(case when event_name = 'B_lock_screen_show' then time_num end),0) as 'B_lock_screen_show_time',
                IFNULL(sum(case when event_name = 'B_lock_screen_show' then person_num end),0) as 'B_lock_screen_show_person',
                IFNULL(sum(case when event_name = 'B_lock_news_screen_show' then time_num end),0) as 'B_lock_news_screen_show_time',
                IFNULL(sum(case when event_name = 'B_lock_news_screen_show' then person_num end),0) as 'B_lock_news_screen_show_person',
                IFNULL(sum(case when event_name = 'B_lock_news_show_success' then time_num end),0) as 'B_lock_news_show_success_time',
                IFNULL(sum(case when event_name = 'B_lock_news_show_success' then person_num end),0) as 'B_lock_news_show_success_person',
                IFNULL(sum(case when event_name = 'B_popup_page_trigger' then time_num end),0) as 'B_popup_page_trigger_time',
                IFNULL(sum(case when event_name = 'B_popup_page_trigger' then person_num end),0) as 'B_popup_page_trigger_person',
                IFNULL(sum(case when event_name = 'B_popup_page_perform_show' then time_num end),0) as 'B_popup_page_perform_show_time',
                IFNULL(sum(case when event_name = 'B_popup_page_perform_show' then person_num end),0) as 'B_popup_page_perform_show_person',
                IFNULL(sum(case when event_name = 'B_popup_page_created' then time_num end),0) as 'B_popup_page_created_time',
                IFNULL(sum(case when event_name = 'B_popup_page_created' then person_num end),0) as 'B_popup_page_created_person'
        from ads_umeng_event_lock_daily
        <where> a_day between #{begin} and #{end} and appid = #{appid}
            <if test="version != null and version != ''">
                and app_version = #{version,jdbcType=VARCHAR}
            </if>
            <if test="channel != null and channel != ''">
                and install_channel = #{channel,jdbcType=VARCHAR}
            </if>
            <if test="model != null and model != ''">
                and model = #{model,jdbcType=VARCHAR}
            </if>
            <if test="isNew != null and isNew != ''">
                and is_new_install = #{isNew,jdbcType=VARCHAR}
            </if>
        </where>
        GROUP BY a_day,install_channel,app_version,model
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>

    <select id="selectDauUserByChannel" resultType="com.wbgame.pojo.ProdutChannelDataVo">
       select tdate by_date,sum(act_num) as dauCount,sum(add_num) as newCount
		from data_ym.umeng_user_channel_total where appid = #{appid} and install_channel = #{channel}
		and tdate &gt;= #{createtime} and tdate &lt;= #{endtime}
		group  by tdate
	</select>

    <select id="getChannelsAndVersions" resultType="java.util.Map">
        select 'channel' type,channel as val from ads_dim_app_info_daily  where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        and channel is not null GROUP BY channel
        union all
        select 'ver' type,ver as val from ads_dim_app_info_daily  where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        and ver is not null GROUP BY ver
    </select>

    <select id="selectAdvertKeeps" resultType="java.util.Map">
        select a_day tdate,appid,cid cname
        ,round(keep1/add_num*100,2) keep_num1
        ,round(keep2/add_num*100,2) keep_num2
        ,round(keep3/add_num*100,2) keep_num3
        ,round(keep4/add_num*100,2) keep_num4
        ,round(keep5/add_num*100,2) keep_num5
        ,round(keep6/add_num*100,2) keep_num6
        ,round(keep7/add_num*100,2) keep_num7
        ,round(keep14/add_num*100,2) keep_num14
        ,round(keep30/add_num*100,2) keep_num30
        from ads_umeng_channel_total_daily
        where a_day in
        <foreach collection="dates" separator="," item="it" open="(" close=")">
                #{it}
        </foreach>
    </select>

    <select id="selectMonthChannelTotal" resultType="java.util.Map">
        select a_day tdate,appid,install_channel,ifnull(act_num,0) act_num,ifnull(add_num,0) add_num,year,month from ads_umeng_month_user_total_daily
        where a_day = #{tdate}
    </select>

    <select id="selectBaiduActRate" resultType="java.util.Map">
        select a.appid,a.install_channel,round(act_num/actnum,3) rate from
		(select appid,install_channel,act_num from ads_umeng_month_user_total_daily where a_day = #{tdate} and install_channel != 'unknown' GROUP BY appid,install_channel) a
		left join
		(select appid,sum(act_num) actnum from ads_umeng_month_user_total_daily where a_day = #{tdate} GROUP BY appid) b
		on a.appid = b.appid
    </select>


    <select id="selectUmengOverseaReport" resultType="java.util.Map">
        select tdate,appid,app_name,app_category,install_channel,app_version,country,add_num,act_num,second_avg,single_avg,start_num,start_rate
        ,concat(round(error_rate*100,2),'%') error_rate
        ,concat(round(keep1*100,2),'%') keep1
        ,concat(round(keep2*100,2),'%') keep2
        ,concat(round(keep3*100,2),'%') keep3
        ,concat(round(keep4*100,2),'%') keep4
        ,concat(round(keep5*100,2),'%') keep5
        ,concat(round(keep6*100,2),'%') keep6
        ,concat(round(keep7*100,2),'%') keep7
        ,concat(round(keep14*100,2),'%') keep14
        from (select
        a.a_day tdate,
        a.appid,
        app_name,
        c.app_category,
        a.install_channel,
        a.app_version,
        a.country,
        IFNULL(sum(a.add_num),0) add_num,
        IFNULL(sum(a.act_num),0) act_num,
        IFNULL(a.second_avg,0) second_avg,
        IFNULL(a.single_avg,0) single_avg,
        IFNULL(sum(a.start_num),0) start_num,
        IFNULL(round(sum(a.start_num)/sum(a.act_num),2),0) start_rate,
        IFNULL(round(sum(a.error_num)/sum(start_num),4),0) error_rate,
        IFNULL(round(sum(keep1)/sum(b.add_num),4),0) keep1,
        IFNULL(round(sum(keep2)/sum(b.add_num),4),0) keep2,
        IFNULL(round(sum(keep3)/sum(b.add_num),4),0) keep3,
        IFNULL(round(sum(keep4)/sum(b.add_num),4),0) keep4,
        IFNULL(round(sum(keep5)/sum(b.add_num),4),0) keep5,
        IFNULL(round(sum(keep6)/sum(b.add_num),4),0) keep6,
        IFNULL(round(sum(keep7)/sum(b.add_num),4),0) keep7,
        IFNULL(round(sum(keep14)/sum(b.add_num),4),0) keep14
        from ads_umeng_custom_total_daily_oversea a
        left join ads_umeng_user_app_keep_daily_oversea b
        on a.a_day = b.a_day and a.appid = b.appid and a.install_channel = b.install_channel and a.app_version = b.app_version and a.country = b.country
        left join dim_app_game c on a.appid = c.appid
        where a.a_day between #{startTime} and #{endTime}
        <if test="appid != null and appid != ''">
            and a.appid in ${appid}
        </if>
        <if test="channel != null and channel != ''">
            and a.install_channel in ${channel}
        </if>
        <if test="version != null and version != ''">
            and a.app_version in ${version}
        </if>
        <if test="app_category != null and app_category != ''">
            and c.app_category = #{app_category}
        </if>
        <if test="country != null and country != ''">
            and a.country in (${country})
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        order by ${order_str}
        ) a
    </select>

    <select id="countUmengOverseaReport" resultType="java.util.Map">
        select sum(add_num) add_num,sum(act_num) act_num
        ,sum(start_num) start_num,round(sum(start_num)/sum(act_num),2) start_rate
        from ads_umeng_custom_total_daily_oversea a left join dim_app_game b on a.appid = b.appid
        where a.a_day between #{startTime} and #{endTime}
        <if test="appid != null and appid != ''">
            and a.appid in ${appid}
        </if>
        <if test="channel != null and channel != ''">
            and install_channel in ${channel}
        </if>
        <if test="version != null and version != ''">
            and app_version in ${version}
        </if>
        <if test="app_category != null and app_category != ''">
            and app_category = #{app_category}
        </if>
        <if test="country != null and country != ''">
            and a.country in (${country})
        </if>
    </select>

</mapper>