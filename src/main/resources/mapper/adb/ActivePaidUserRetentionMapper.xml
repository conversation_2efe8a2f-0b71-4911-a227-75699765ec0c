<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wbgame.mapper.adb.ActivePaidUserRetentionMapper">

	<sql id="sql_tdate">
		<choose>
			<when test='null!=group and group.contains("week")'>
				DATE_FORMAT(tdate,'%x-%v') tdate,
			</when>
			<when test='null!=group and group.contains("month")'>
				DATE_FORMAT(tdate,'%Y-%m') tdate,
			</when>
			<when test='null!=group and group.contains("year")'>
				DATE_FORMAT(tdate,'%Y') tdate,
			</when>
			<otherwise>
				tdate,
			</otherwise>
		</choose>
	</sql>

    <select id="list" resultType="com.wbgame.pojo.operate.ActivePaidUserRetentionVo"
            parameterType="com.wbgame.pojo.param.ActivePaidUserRetentioParam">
        select app_name,appid,group_id,
		<include refid="sql_tdate"/>
		case when group_id=1 then '新用户'
		     when group_id=0 then '整体'
		     when group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7 then '轻度用户' 
		     when group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14 then '中度用户'
		     when group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30 then '重度用户'   
		     when group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45 then '超重度用户'   
		     when group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60 then '轻硬核用户'   
		     when group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90 then '硬核用户'      
		     when group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120 then '超硬核用户'
		     when group_id<![CDATA[>=]]>121 then '骨灰级用户' 
		     else group_id end as gp,
		sum(iap_user_cnt) iap_user_cnt,cast(sum(iap_user_cnt)/count(distinct tdate) as decimal (10, 0)) day_avg_iap_user,
		concat(round(sum(retention_1day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_1day,
		concat(round(sum(retention_3day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_3day,
		concat(round(sum(retention_7day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_7day,
		concat(round(sum(retention_14day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_14day,
		concat(round(sum(retention_30day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_30day,
		concat(round(sum(retention_60day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_60day,
		concat(round(sum(iap_retention_1day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_1day,
		concat(round(sum(iap_retention_3day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_3day,
		concat(round(sum(iap_retention_7day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_7day,
		concat(round(sum(iap_retention_14day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_14day,
		concat(round(sum(iap_retention_30day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_30day,
		concat(round(sum(iap_retention_60day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_60day
		from ads_wechat_iap_user_group_retention_daily
		where  tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
		<if test="gp != null and gp != ''">
			and ( 1=2
			<if test='gp.contains("新用户")'>
				or group_id=1
			</if>
			<if test='gp.contains("整体")'>
				or group_id=0
			</if>
			<if test='gp.contains("轻度用户")'>
				or (group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7)
			</if>
			<if test='gp.contains("中度用户")'>
				or (group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14)
			</if>
			<if test='gp.contains("重度用户") and !gp.contains("超重度用户")'>
				or (group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30)
			</if>
			<if test='gp.contains("超重度用户")'>
				or (group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45)
			</if>
			<if test='gp.contains("轻硬核用户")'>
				or (group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60)
			</if>
			<if test='gp.contains("硬核用户") and !gp.contains("超硬核用户") and !gp.contains("轻硬核用户")'>
				or (group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90)
			</if>
			<if test='gp.contains("超硬核用户")'>
				or (group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120)
			</if>
			<if test='gp.contains("骨灰级用户")'>
				or (group_id<![CDATA[>=]]>121)
			</if>
			)
        </if>
		group by appid,gp
		<if test="group != null and group != ''">
			<choose>
				<when test='null!=group and group.contains("week")'>
					,DATE_FORMAT(tdate,'%x-%v')
				</when>
				<when test='null!=group and group.contains("month")'>
					,DATE_FORMAT(tdate,'%Y-%m')
				</when>
				<when test='null!=group and group.contains("year")'>
					,DATE_FORMAT(tdate,'%Y')
				</when>
				<otherwise>
					,tdate
				</otherwise>
			</choose>
		</if>
		union all
		select app_name,appid,group_id,
		<include refid="sql_tdate"/>
		case when group_id=1 then '新用户'
			 when group_id=0 then '整体'
			 when group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7 then '轻度用户'
		     when group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14 then '中度用户'
		     when group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30 then '重度用户'   
		     when group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45 then '超重度用户'   
		     when group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60 then '轻硬核用户'   
		     when group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90 then '硬核用户'      
		     when group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120 then '超硬核用户'
		     when group_id<![CDATA[>=]]>121 then '骨灰级用户' 
		     else group_id end as gp,
		sum(iap_user_cnt) iap_user_cnt,cast(sum(iap_user_cnt)/count(distinct tdate) as decimal (10, 0)) day_avg_iap_user,
		concat(round(sum(retention_1day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_1day,
		concat(round(sum(retention_3day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_3day,
		concat(round(sum(retention_7day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_7day,
		concat(round(sum(retention_14day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_14day,
		concat(round(sum(retention_30day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_30day,
		concat(round(sum(retention_60day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_60day,
		concat(round(sum(iap_retention_1day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_1day,
		concat(round(sum(iap_retention_3day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_3day,
		concat(round(sum(iap_retention_7day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_7day,
		concat(round(sum(iap_retention_14day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_14day,
		concat(round(sum(iap_retention_30day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_30day,
		concat(round(sum(iap_retention_60day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_60day
		from ads_iap_user_group_retention_daily 
		where  tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
		<if test="gp != null and gp != ''">
			and ( 1=2
			<if test='gp.contains("新用户")'>
				or group_id=1
			</if>
			<if test='gp.contains("整体")'>
				or group_id=0
			</if>
			<if test='gp.contains("轻度用户")'>
				or (group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7)
			</if>
			<if test='gp.contains("中度用户")'>
				or (group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14)
			</if>
			<if test='gp.contains("重度用户") and !gp.contains("超重度用户")'>
				or (group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30)
			</if>
			<if test='gp.contains("超重度用户")'>
				or (group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45)
			</if>
			<if test='gp.contains("轻硬核用户")'>
				or (group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60)
			</if>
			<if test='gp.contains("硬核用户") and !gp.contains("超硬核用户") and !gp.contains("轻硬核用户")'>
				or (group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90)
			</if>
			<if test='gp.contains("超硬核用户")'>
				or (group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120)
			</if>
			<if test='gp.contains("骨灰级用户")'>
				or (group_id<![CDATA[>=]]>121)
			</if>
			)
        </if>
		group by appid,gp
		<if test="group != null and group != ''">
			<choose>
				<when test='null!=group and group.contains("week")'>
					,DATE_FORMAT(tdate,'%x-%v')
				</when>
				<when test='null!=group and group.contains("month")'>
					,DATE_FORMAT(tdate,'%Y-%m')
				</when>
				<when test='null!=group and group.contains("year")'>
					,DATE_FORMAT(tdate,'%Y')
				</when>
				<otherwise>
					,tdate
				</otherwise>
			</choose>
		</if>
		union all
		select app_name,appid,group_id,
		<include refid="sql_tdate"/>
		case when group_id=1 then '新用户'
			 when group_id=0 then '整体'
		     when group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7 then '轻度用户' 
		     when group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14 then '中度用户'
		     when group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30 then '重度用户'   
		     when group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45 then '超重度用户'   
		     when group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60 then '轻硬核用户'   
		     when group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90 then '硬核用户'      
		     when group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120 then '超硬核用户'
		     when group_id<![CDATA[>=]]>121 then '骨灰级用户' 
		     else group_id end as gp,
		sum(iap_user_cnt) iap_user_cnt,cast(sum(iap_user_cnt)/count(distinct tdate) as decimal (10, 0)) day_avg_iap_user,
		concat(round(sum(retention_1day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_1day,
		concat(round(sum(retention_3day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_3day,
		concat(round(sum(retention_7day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_7day,
		concat(round(sum(retention_14day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_14day,
		concat(round(sum(retention_30day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_30day,
		concat(round(sum(retention_60day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_60day,
		concat(round(sum(iap_retention_1day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_1day,
		concat(round(sum(iap_retention_3day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_3day,
		concat(round(sum(iap_retention_7day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_7day,
		concat(round(sum(iap_retention_14day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_14day,
		concat(round(sum(iap_retention_30day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_30day,
		concat(round(sum(iap_retention_60day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_60day
		from ads_iap_user_group_retention_ios_daily
		where  tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
		<if test="gp != null and gp != ''">
			and ( 1=2
			<if test='gp.contains("新用户")'>
				or group_id=1
			</if>
			<if test='gp.contains("整体")'>
				or group_id=0
			</if>
			<if test='gp.contains("轻度用户")'>
				or (group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7)
			</if>
			<if test='gp.contains("中度用户")'>
				or (group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14)
			</if>
			<if test='gp.contains("重度用户") and !gp.contains("超重度用户")'>
				or (group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30)
			</if>
			<if test='gp.contains("超重度用户")'>
				or (group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45)
			</if>
			<if test='gp.contains("轻硬核用户")'>
				or (group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60)
			</if>
			<if test='gp.contains("硬核用户") and !gp.contains("超硬核用户") and !gp.contains("轻硬核用户")'>
				or (group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90)
			</if>
			<if test='gp.contains("超硬核用户")'>
				or (group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120)
			</if>
			<if test='gp.contains("骨灰级用户")'>
				or (group_id<![CDATA[>=]]>121)
			</if>
			)
        </if>
		group by appid,gp
		<if test="group != null and group != ''">
			<choose>
				<when test='null!=group and group.contains("week")'>
					,DATE_FORMAT(tdate,'%x-%v')
				</when>
				<when test='null!=group and group.contains("month")'>
					,DATE_FORMAT(tdate,'%Y-%m')
				</when>
				<when test='null!=group and group.contains("year")'>
					,DATE_FORMAT(tdate,'%Y')
				</when>
				<otherwise>
					,tdate
				</otherwise>
			</choose>
		</if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc,group_id asc
            </otherwise>
        </choose>
    </select>
    
    <select id="total" resultType="com.wbgame.pojo.operate.ActivePaidUserRetentionVo"
            parameterType="com.wbgame.pojo.param.ActivePaidUserRetentioParam">
        select 
        sum(iap_user_cnt) iap_user_cnt,
		concat(round(sum(retention_1day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_1day,
		concat(round(sum(retention_3day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_3day,
		concat(round(sum(retention_7day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_7day,
		concat(round(sum(retention_14day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_14day,
		concat(round(sum(retention_30day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_30day,
		concat(round(sum(retention_60day_iap_user_cnt)/sum(iap_user_cnt)*100,2),'%') retention_60day,
		concat(round(sum(iap_retention_1day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_1day,
		concat(round(sum(iap_retention_3day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_3day,
		concat(round(sum(iap_retention_7day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_7day,
		concat(round(sum(iap_retention_14day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_14day,
		concat(round(sum(iap_retention_30day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_30day,
		concat(round(sum(iap_retention_60day_user_cnt)/sum(iap_user_cnt)*100,2),'%') iap_retention_60day
		from(
            select
		sum(iap_user_cnt) iap_user_cnt,
		sum(retention_1day_iap_user_cnt) retention_1day_iap_user_cnt,
		sum(retention_3day_iap_user_cnt) retention_3day_iap_user_cnt,
		sum(retention_7day_iap_user_cnt) retention_7day_iap_user_cnt,
		sum(retention_14day_iap_user_cnt) retention_14day_iap_user_cnt,
		sum(retention_30day_iap_user_cnt) retention_30day_iap_user_cnt,
		sum(retention_60day_iap_user_cnt) retention_60day_iap_user_cnt,
		sum(iap_retention_1day_user_cnt) iap_retention_1day_user_cnt,
		sum(iap_retention_3day_user_cnt) iap_retention_3day_user_cnt,
		sum(iap_retention_7day_user_cnt) iap_retention_7day_user_cnt,
		sum(iap_retention_14day_user_cnt) iap_retention_14day_user_cnt,
		sum(iap_retention_30day_user_cnt) iap_retention_30day_user_cnt,
		sum(iap_retention_60day_user_cnt) iap_retention_60day_user_cnt
		from ads_wechat_iap_user_group_retention_daily
		where  tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date} and group_id != 0
		<if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
		<if test="gp != null and gp != ''">
			and ( 1=2
			<if test='gp.contains("新用户")'>
				or group_id=1
			</if>
			<if test='gp.contains("轻度用户")'>
				or (group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7)
			</if>
			<if test='gp.contains("中度用户")'>
				or (group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14)
			</if>
			<if test='gp.contains("重度用户") and !gp.contains("超重度用户")'>
				or (group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30)
			</if>
			<if test='gp.contains("超重度用户")'>
				or (group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45)
			</if>
			<if test='gp.contains("轻硬核用户")'>
				or (group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60)
			</if>
			<if test='gp.contains("硬核用户") and !gp.contains("超硬核用户") and !gp.contains("轻硬核用户")'>
				or (group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90)
			</if>
			<if test='gp.contains("超硬核用户")'>
				or (group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120)
			</if>
			<if test='gp.contains("骨灰级用户")'>
				or (group_id<![CDATA[>=]]>121)
			</if>
			)
        </if>
		union all
		select 
		sum(iap_user_cnt) iap_user_cnt,
		sum(retention_1day_iap_user_cnt) retention_1day_iap_user_cnt,
		sum(retention_3day_iap_user_cnt) retention_3day_iap_user_cnt,
		sum(retention_7day_iap_user_cnt) retention_7day_iap_user_cnt,
		sum(retention_14day_iap_user_cnt) retention_14day_iap_user_cnt,
		sum(retention_30day_iap_user_cnt) retention_30day_iap_user_cnt,
		sum(retention_60day_iap_user_cnt) retention_60day_iap_user_cnt,
		sum(iap_retention_1day_user_cnt) iap_retention_1day_user_cnt,
		sum(iap_retention_3day_user_cnt) iap_retention_3day_user_cnt,
		sum(iap_retention_7day_user_cnt) iap_retention_7day_user_cnt,
		sum(iap_retention_14day_user_cnt) iap_retention_14day_user_cnt,
		sum(iap_retention_30day_user_cnt) iap_retention_30day_user_cnt,
		sum(iap_retention_60day_user_cnt) iap_retention_60day_user_cnt
		from ads_iap_user_group_retention_daily 
		where  tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date} and group_id != 0
		<if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
		<if test="gp != null and gp != ''">
			and ( 1=2
			<if test='gp.contains("新用户")'>
				or group_id=1
			</if>
			<if test='gp.contains("轻度用户")'>
				or (group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7)
			</if>
			<if test='gp.contains("中度用户")'>
				or (group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14)
			</if>
			<if test='gp.contains("重度用户") and !gp.contains("超重度用户")'>
				or (group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30)
			</if>
			<if test='gp.contains("超重度用户")'>
				or (group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45)
			</if>
			<if test='gp.contains("轻硬核用户")'>
				or (group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60)
			</if>
			<if test='gp.contains("硬核用户") and !gp.contains("超硬核用户") and !gp.contains("轻硬核用户")'>
				or (group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90)
			</if>
			<if test='gp.contains("超硬核用户")'>
				or (group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120)
			</if>
			<if test='gp.contains("骨灰级用户")'>
				or (group_id<![CDATA[>=]]>121)
			</if>
			)
        </if>
		union all
		select 
		sum(iap_user_cnt) iap_user_cnt,
		sum(retention_1day_iap_user_cnt) retention_1day_iap_user_cnt,
		sum(retention_3day_iap_user_cnt) retention_3day_iap_user_cnt,
		sum(retention_7day_iap_user_cnt) retention_7day_iap_user_cnt,
		sum(retention_14day_iap_user_cnt) retention_14day_iap_user_cnt,
		sum(retention_30day_iap_user_cnt) retention_30day_iap_user_cnt,
		sum(retention_60day_iap_user_cnt) retention_60day_iap_user_cnt,
		sum(iap_retention_1day_user_cnt) iap_retention_1day_user_cnt,
		sum(iap_retention_3day_user_cnt) iap_retention_3day_user_cnt,
		sum(iap_retention_7day_user_cnt) iap_retention_7day_user_cnt,
		sum(iap_retention_14day_user_cnt) iap_retention_14day_user_cnt,
		sum(iap_retention_30day_user_cnt) iap_retention_30day_user_cnt,
		sum(iap_retention_60day_user_cnt) iap_retention_60day_user_cnt
		from ads_iap_user_group_retention_ios_daily
		where  tdate<![CDATA[>=]]>#{start_date} and  tdate<![CDATA[<=]]>#{end_date} and group_id != 0
		<if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
		<if test="gp != null and gp != ''">
			and ( 1=2
			<if test='gp.contains("新用户")'>
				or group_id=1
			</if>
			<if test='gp.contains("轻度用户")'>
				or (group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7)
			</if>
			<if test='gp.contains("中度用户")'>
				or (group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14)
			</if>
			<if test='gp.contains("重度用户") and !gp.contains("超重度用户")'>
				or (group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30)
			</if>
			<if test='gp.contains("超重度用户")'>
				or (group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45)
			</if>
			<if test='gp.contains("轻硬核用户")'>
				or (group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60)
			</if>
			<if test='gp.contains("硬核用户") and !gp.contains("超硬核用户") and !gp.contains("轻硬核用户")'>
				or (group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90)
			</if>
			<if test='gp.contains("超硬核用户")'>
				or (group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120)
			</if>
			<if test='gp.contains("骨灰级用户")'>
				or (group_id<![CDATA[>=]]>121)
			</if>
			)
        </if>
        ) 
    </select>

	<sql id="sql_online_duration_group">
		group by appid,gp
		<if test="group != null and group != ''">
			<choose>
				<when test='null!=group and group.contains("week")'>
					,DATE_FORMAT(tdate,'%x-%v')
				</when>
				<when test='null!=group and group.contains("month")'>
					,DATE_FORMAT(tdate,'%Y-%m')
				</when>
				<when test='null!=group and group.contains("year")'>
					,DATE_FORMAT(tdate,'%Y')
				</when>
				<otherwise>
					,tdate
				</otherwise>
			</choose>
		</if>
	</sql>

    <!-- 活跃度用户在线时长分析  -->
    <select id="OnlineDurationList" resultType="com.wbgame.pojo.operate.ActiveUserOnlineDurationVo"
            parameterType="com.wbgame.pojo.param.ActivePaidUserRetentioParam">
		select app_name,appid,
		<include refid="sql_tdate"/>
		case  when group_id=1 then '新用户'
		    when group_id = 0 then '整体'
	        when group_id=2 then '轻度用户' 
	        when group_id=3 then '中度用户'
	        when group_id=4 then '重度用户'   
	        when group_id=5 then '超重度用户'
	        when group_id=6 then '轻硬核用户'
	        when group_id=7 then '硬核用户'
	        when group_id=8 then '超硬核用户'
	        when group_id=9 then '骨灰级用户'
		     else group_id end as gp,
		        sum(active_user_cnt) active_user_cnt,cast(sum(active_user_cnt)/count(distinct tdate) as decimal (10, 0)) day_avg_user,
				cast(sum(avg_active_time*active_user_cnt)/sum(active_user_cnt) as decimal (10, 2)) avg_active_time ,
				cast(avg(mid_active_time) as decimal (10, 2)) mid_active_time ,
				cast(min(min_active_time) as decimal (10, 2)) min_active_time ,
				cast(max(max_active_time) as decimal (10, 2)) max_active_time ,
				group_id
		from ads_wechat_user_group_active_time_analysis_daily
		where  tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
		<if test="gp != null and gp != ''">
			and  group_id in(${gp})
        </if>
        <include refid="sql_online_duration_group"/>
		union all
		select app_name,appid,
		<include refid="sql_tdate"/>
		case  when group_id=1 then '新用户'
		when group_id = 0 then '整体'
		when group_id=2 then '轻度用户'
		when group_id=3 then '中度用户'
		when group_id=4 then '重度用户'
		when group_id=5 then '超重度用户'
		when group_id=6 then '轻硬核用户'
		when group_id=7 then '硬核用户'
		when group_id=8 then '超硬核用户'
		when group_id=9 then '骨灰级用户'
		else group_id end as gp,
		sum(active_user_cnt) active_user_cnt,cast(sum(active_user_cnt)/count(distinct tdate) as decimal (10, 0)) day_avg_user,
		cast(sum(avg_active_time*active_user_cnt)/sum(active_user_cnt) as decimal (10, 2)) avg_active_time ,
		cast(avg(mid_active_time) as decimal (10, 2)) mid_active_time ,
		cast(min(min_active_time) as decimal (10, 2)) min_active_time ,
		cast(max(max_active_time) as decimal (10, 2)) max_active_time ,
		group_id
		from ads_user_group_active_time_analysis_daily
		where  tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
		<if test="gp != null and gp != ''">
			and  group_id in(${gp})
        </if>
		<include refid="sql_online_duration_group"/>
		union all
		select app_name,appid,
		<include refid="sql_tdate"/>
		case  when group_id=1 then '新用户'
		when group_id = 0 then '整体'
		when group_id=2 then '轻度用户'
		when group_id=3 then '中度用户'
		when group_id=4 then '重度用户'
		when group_id=5 then '超重度用户'
		when group_id=6 then '轻硬核用户'
		when group_id=7 then '硬核用户'
		when group_id=8 then '超硬核用户'
		when group_id=9 then '骨灰级用户'
		else group_id end as gp,
		sum(active_user_cnt) active_user_cnt,cast(sum(active_user_cnt)/count(distinct tdate) as decimal (10, 0)) day_avg_user,
		cast(sum(avg_active_time*active_user_cnt)/sum(active_user_cnt) as decimal (10, 2)) avg_active_time ,
		cast(avg(mid_active_time) as decimal (10, 2)) mid_active_time ,
		cast(min(min_active_time) as decimal (10, 2)) min_active_time ,
		cast(max(max_active_time) as decimal (10, 2)) max_active_time ,
		group_id
		from ads_user_group_active_time_analysis_ios_daily
		where  tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
		<if test="gp != null and gp != ''">
			and  group_id in(${gp})
        </if>
		<include refid="sql_online_duration_group"/>
		<choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc,group_id asc
            </otherwise>
        </choose>
    </select>
    
    
    
     <select id="OnlineDurationChart" resultType="com.wbgame.pojo.operate.ActiveUserOnlineDurationVo"
            parameterType="com.wbgame.pojo.param.ActivePaidUserRetentioParam">
        select   <include refid="sql_tdate"/>t.gp gp ,
		 sum(active_user_cnt) active_user_cnt,cast(sum(active_user_cnt)/count(distinct tdate) as decimal (10, 0)) day_avg_user,
		 cast(sum(avg_active_time*active_user_cnt)/sum(active_user_cnt) as decimal (10, 2)) avg_active_time ,
		 cast(avg(mid_active_time) as decimal (10, 2)) mid_active_time ,
		 cast(min(min_active_time) as decimal (10, 2)) min_active_time ,
		 cast(max(max_active_time) as decimal (10, 2)) max_active_time
        from (
		select tdate,
		case  when group_id=1 then '新用户'
		 	when group_id=0 then '整体'
	        when group_id=2 then '轻度用户' 
	        when group_id=3 then '中度用户'
	        when group_id=4 then '重度用户'   
	        when group_id=5 then '超重度用户'
	        when group_id=6 then '轻硬核用户'
	        when group_id=7 then '硬核用户'
	        when group_id=8 then '超硬核用户'
	        when group_id=9 then '骨灰级用户'
		     else group_id end as gp,
		        active_user_cnt, 
		        avg_active_time ,
		        mid_active_time ,
		        min_active_time ,
		        max_active_time ,
				group_id AS order_str
		from ads_wechat_user_group_active_time_analysis_daily
		where  tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
		<if test="gp != null and gp != ''">
			and  group_id in(${gp})
        </if>
		union all 
		select tdate,
		case  when group_id=1 then '新用户'
		 when group_id=0 then '整体'
		 when group_id=2 then '轻度用户'
	        when group_id=3 then '中度用户'
	        when group_id=4 then '重度用户'   
	        when group_id=5 then '超重度用户'
	        when group_id=6 then '轻硬核用户'
	        when group_id=7 then '硬核用户'
	        when group_id=8 then '超硬核用户'
	        when group_id=9 then '骨灰级用户'
		     else group_id end as gp,
		        active_user_cnt, 
		        avg_active_time ,
		        mid_active_time ,
		        min_active_time ,
		        max_active_time ,
				group_id AS order_str
		from ads_user_group_active_time_analysis_daily
		where  tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
		<if test="gp != null and gp != ''">
			and  group_id in(${gp})
        </if>
		union all
		select tdate,
		case  when group_id=1 then '新用户'
		 when group_id=0 then '整体'
		 when group_id=2 then '轻度用户'
	        when group_id=3 then '中度用户'
	        when group_id=4 then '重度用户'   
	        when group_id=5 then '超重度用户'
	        when group_id=6 then '轻硬核用户'
	        when group_id=7 then '硬核用户'
	        when group_id=8 then '超硬核用户'
	        when group_id=9 then '骨灰级用户'
		     else group_id end as gp,
		        active_user_cnt, 
		        avg_active_time ,
		        mid_active_time ,
		        min_active_time ,
		        max_active_time ,
				group_id AS order_str
		from ads_user_group_active_time_analysis_ios_daily
		where  tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
		<if test="gp != null and gp != ''">
			and  group_id in(${gp})
        </if>
        )  t
		 group by gp
		 <if test="group != null and group != ''">
			 <choose>
				 <when test='null!=group and group.contains("week")'>
					 ,DATE_FORMAT(tdate,'%x-%v')
				 </when>
				 <when test='null!=group and group.contains("month")'>
					 ,DATE_FORMAT(tdate,'%Y-%m')
				 </when>
				 <when test='null!=group and group.contains("year")'>
					 ,DATE_FORMAT(tdate,'%Y')
				 </when>
				 <otherwise>
					 ,tdate
				 </otherwise>
			 </choose>
		 </if>
        ORDER BY 	t.order_str ASC
    </select>
    
    <select id="wxGameOnlineTimeList" resultType="com.wbgame.pojo.operate.WxGameOnlineTimeVo"
            parameterType="com.wbgame.pojo.param.WxGameOnlineTimeParam" >
    	select 
		<choose>
           <when test='null!=group and group.contains("model") and group.contains("osui")'>
              tdate,appid,app_name,brand,model,osui,
           </when>
           <when test='null!=group and  group.contains("model")'>
             tdate,appid,app_name,brand,model,
           </when>
           <when test=' null!=group and  group.contains("osui")'>
             tdate,appid,app_name,brand, osui,
           </when>
           <otherwise>
           tdate,appid,app_name,brand,
           </otherwise>
     	</choose>
		sum(reg_user_cnt) reg_user_cnt,sum(old_user_cnt) old_user_cnt,
    	round(sum(reg_user_cnt*online_time_avg_new)/sum(reg_user_cnt)/60,2) online_time_avg_new,
    	round(sum(reg_user_cnt*online_startup_median_new)/sum(reg_user_cnt),2) online_startup_median_new,
    	round(sum(reg_user_cnt*online_startup_avg_new)/sum(reg_user_cnt),2) online_startup_avg_new,
    	round(sum(reg_user_cnt*online_time_median_new)/sum(reg_user_cnt)/60,2) online_time_median_new,
    	round(sum(old_user_cnt*online_time_avg_old)/sum(old_user_cnt)/60,2) online_time_avg_old,
    	round(sum(old_user_cnt*online_startup_median_old)/sum(old_user_cnt),2) online_startup_median_old,
    	round(sum(old_user_cnt*online_startup_avg_old)/sum(old_user_cnt),2) online_startup_avg_old,
    	round(sum(old_user_cnt*online_time_median_old)/sum(old_user_cnt)/60,2) online_time_median_old,
    	round(sum(reg_user_cnt*online_time_avg_new)/sum(reg_user_cnt)/(sum(reg_user_cnt*online_startup_avg_new)/sum(reg_user_cnt))/60,2) single_online_time_avg_new,
    	round(sum(old_user_cnt*online_time_avg_old)/sum(old_user_cnt)/(sum(old_user_cnt*online_startup_avg_old)/sum(old_user_cnt))/60,2) single_online_time_avg_old
		FROM    ads_wechat_device_startup_active_time_daily
		where   tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
         	and appid in (${appid})
        </if>
        <if test="brand != null and brand != ''">
         	and brand  LIKE "%"#{brand}"%"
        </if>
        <if test="model != null and model != ''">
         	and model LIKE "%"#{model}"%"
        </if>
        <if test="osui != null and osui != ''">
         	and osui LIKE "%"#{osui}"%"
        </if>
        <choose>
           <when test=' null!=group and   group.contains("model") and group.contains("osui")'>
              group by tdate,appid,app_name,brand,model,osui
           </when>
           <when test=' null!=group and  group.contains("model")'>
             group by tdate,appid,app_name,brand,model
           </when>
           <when test=' null!=group and  group.contains("osui")'>
             group by tdate,appid,app_name,brand, osui
           </when>
           <otherwise>
           	group by tdate,appid,app_name,brand
           </otherwise>
     	</choose>
     	 HAVING sum(reg_user_cnt)>100 or sum(old_user_cnt)>100
        <choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate asc
			</otherwise>
		</choose>
    </select>
    
    <select id="wxGameOnlineTimeTotal" resultType="com.wbgame.pojo.operate.WxGameOnlineTimeTotalVo"
            parameterType="com.wbgame.pojo.param.WxGameOnlineTimeParam" >
    	select 
		sum(reg_user_cnt) reg_user_cnt,sum(old_user_cnt) old_user_cnt,
    	sum(reg_user_cnt*online_time_avg_new) online_time_avg_new,
    	sum(reg_user_cnt*online_startup_median_new) online_startup_median_new,
    	sum(reg_user_cnt*online_startup_avg_new) online_startup_avg_new,
    	sum(reg_user_cnt*online_time_median_new) online_time_median_new,
    	sum(old_user_cnt*online_time_avg_old) online_time_avg_old,
    	sum(old_user_cnt*online_startup_median_old) online_startup_median_old,
    	sum(old_user_cnt*online_startup_avg_old) online_startup_avg_old,
    	sum(old_user_cnt*online_time_median_old) online_time_median_old
		FROM    ads_wechat_device_startup_active_time_daily
		where   tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
         	and appid in (${appid})
        </if>
        <if test="brand != null and brand != ''">
         	and brand  LIKE "%"#{brand}"%"
        </if>
        <if test="model != null and model != ''">
         	and model LIKE "%"#{model}"%"
        </if>
        <if test="osui != null and osui != ''">
         	and osui LIKE "%"#{osui}"%"
        </if>
        <choose>
           <when test=' null!=group and   group.contains("model") and group.contains("osui")'>
              group by tdate,appid,app_name,brand,model,osui
           </when>
           <when test=' null!=group and  group.contains("model")'>
             group by tdate,appid,app_name,brand,model
           </when>
           <when test=' null!=group and  group.contains("osui")'>
             group by tdate,appid,app_name,brand, osui
           </when>
           <otherwise>
           	group by tdate,appid,app_name,brand
           </otherwise>
     	</choose>
     	HAVING sum(reg_user_cnt)>100 or sum(old_user_cnt)>100
    </select>
    
     <select id="wxGameOnlineTimeChart" resultType="com.wbgame.pojo.operate.WxGameOnlineTimeVo"
            parameterType="com.wbgame.pojo.param.WxGameOnlineTimeParam" >
    	select 
		tdate,appid,app_name,brand,
		sum(reg_user_cnt) reg_user_cnt,sum(old_user_cnt) old_user_cnt,
    	FORMAT(sum(reg_user_cnt*online_time_avg_new)/sum(reg_user_cnt)/60,2) online_time_avg_new,
    	FORMAT(sum(reg_user_cnt*online_startup_median_new)/sum(reg_user_cnt),2) online_startup_median_new,
    	FORMAT(sum(reg_user_cnt*online_startup_avg_new)/sum(reg_user_cnt),2) online_startup_avg_new,
    	FORMAT(sum(reg_user_cnt*online_time_median_new)/sum(reg_user_cnt)/60,2) online_time_median_new,
    	FORMAT(sum(old_user_cnt*online_time_avg_old)/sum(old_user_cnt)/60,2) online_time_avg_old,
    	FORMAT(sum(old_user_cnt*online_startup_median_old)/sum(old_user_cnt),2) online_startup_median_old,
    	FORMAT(sum(old_user_cnt*online_startup_avg_old)/sum(old_user_cnt),2) online_startup_avg_old,
    	FORMAT(sum(old_user_cnt*online_time_median_old)/sum(old_user_cnt)/60,2) online_time_median_old,
    	FORMAT(sum(reg_user_cnt*online_time_avg_new)/sum(reg_user_cnt)/(sum(reg_user_cnt*online_startup_avg_new)/sum(reg_user_cnt))/60,2) single_online_time_avg_new,
    	FORMAT(sum(old_user_cnt*online_time_avg_old)/sum(old_user_cnt)/(sum(old_user_cnt*online_startup_avg_old)/sum(old_user_cnt))/60,2) single_online_time_avg_old
		FROM    ads_wechat_device_startup_active_time_daily
		where   tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
         	and appid in (${appid})
        </if>
        <if test="brand != null and brand != ''">
         	and brand  LIKE "%"#{brand}"%"
        </if>
        <if test="model != null and model != ''">
         	and model LIKE "%"#{model}"%"
        </if>
        <if test="osui != null and osui != ''">
         	and osui LIKE "%"#{osui}"%"
        </if>
         group by tdate,appid,app_name,brand HAVING sum(reg_user_cnt)>100 or sum(old_user_cnt)>100
    </select>
    
    
    <!--用户分R分析-->
    <select id="userTagRList" resultType="com.wbgame.pojo.operate.UserTagRVo"
            parameterType="com.wbgame.pojo.param.UserTagRParam" >
    	select 
		<choose>
           <when test='null!=group and group.contains("week")'>
              DATE_FORMAT(t_date,'%x-%v') tdate,
           </when>
           <when test='null!=group and group.contains("month")'>
             DATE_FORMAT(t_date,'%Y-%m') tdate,
           </when>
			<when test='null!=group and group.contains("year")'>
				DATE_FORMAT(t_date,'%Y') tdate,
			</when>
           <when test='null!=group and group.contains("tdate")'>
            t_date tdate,
           </when>
           <otherwise>
           </otherwise>
     	</choose>
     	appid,tag_r,
		cast(sum(active_user_cnt)/count(distinct t_date) as decimal (10, 0)) day_avg_active_user,
		cast(sum(pay_user_cnt)/count(distinct t_date) as decimal (10, 0)) day_avg_pay_user,
		sum(active_user_cnt) active_user_cnt,sum(pay_user_cnt) pay_user_cnt,sum(pay_amount)/100 pay_amount,
		round(sum(pay_user_cnt)/sum(active_user_cnt)*100,2) pay_rate,
		round(sum(pay_amount)/100/sum(active_user_cnt),2) arpu,
		round(sum(pay_amount)/100/sum(pay_user_cnt),2) arppu,
		round(sum(ret_2)/sum(case when ret_2>0 then active_user_cnt end)*100,2) rd1,
		round(sum(ret_3)/sum( case when ret_3>0 then active_user_cnt end)*100,2) rd3,
		round(sum(ret_7)/sum( case when ret_7>0 then active_user_cnt end )*100,2) rd7,
		round(sum(ret_14)/sum(case when ret_14>0 then active_user_cnt end)*100,2) rd14,
		round(sum(ret_30)/sum(case when ret_30>0 then active_user_cnt end)*100,2) rd30,
		round(sum(ret_60)/sum(case when ret_60>0 then active_user_cnt end)*100,2) rd60,
		round(avg(avg_time)/60,2) avg_time,
		round(avg(median_time)/60,2) median_time
		FROM    ads_user_r_info_daily
		where   t_date<![CDATA[>=]]>#{start_date} and t_date<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
         	and appid in (${appid})
        </if>
        <if test="tag_r != null and tag_r != ''">
         	and tag_r  in (${tag_r})
        </if>
        group by appid,tag_r
        <if test="group != null and group != ''">
         	,tdate
        </if>
        <choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
		</choose>
    </select>
    <select id="userTagRTotal" resultType="com.wbgame.pojo.operate.UserTagRVo"
            parameterType="com.wbgame.pojo.param.UserTagRParam" >
    	select 
		sum(active_user_cnt) active_user_cnt,sum(pay_user_cnt) pay_user_cnt,sum(pay_amount)/100 pay_amount,
		concat(FORMAT(sum(pay_user_cnt)/sum(active_user_cnt)*100,2),'%') pay_rate,
		FORMAT(sum(pay_amount)/100/sum(active_user_cnt),2) arpu,
		FORMAT(sum(pay_amount)/100/sum(pay_user_cnt),2) arppu,
		concat(FORMAT(sum(ret_2)/sum(case when ret_2>0 then active_user_cnt end)*100,2),'%') rd1,
		concat(FORMAT(sum(ret_3)/sum( case when ret_3>0 then active_user_cnt end)*100,2),'%') rd3,
		concat(FORMAT(sum(ret_7)/sum( case when ret_7>0 then active_user_cnt end )*100,2),'%') rd7,
		concat(FORMAT(sum(ret_14)/sum(case when ret_14>0 then active_user_cnt end)*100,2),'%') rd14,
		concat(FORMAT(sum(ret_30)/sum(case when ret_30>0 then active_user_cnt end)*100,2),'%') rd30,
		concat(FORMAT(sum(ret_60)/sum(case when ret_60>0 then active_user_cnt end)*100,2),'%') rd60
		FROM    ads_user_r_info_daily
		where   t_date<![CDATA[>=]]>#{start_date} and t_date<![CDATA[<=]]>#{end_date} and tag_r != '整体'
		<if test="appid != null and appid != ''">
         	and appid in (${appid})
        </if>
        <if test="tag_r != null and tag_r != ''">
         	and tag_r  in (${tag_r})
        </if>
    </select>
	<select id="userTagRChart" resultType="com.wbgame.pojo.operate.UserTagRVo">
		select
		<choose>
			<when test='null!=group and group.contains("week")'>
				DATE_FORMAT(t_date,'%x-%v') tdate,
			</when>
			<when test='null!=group and group.contains("month")'>
				DATE_FORMAT(t_date,'%Y-%m') tdate,
			</when>
			<when test='null!=group and group.contains("year")'>
				DATE_FORMAT(t_date,'%Y') tdate,
			</when>
			<when test='null!=group and group.contains("tdate")'>
				t_date tdate,
			</when>
			<otherwise>
			</otherwise>
		</choose>
		tag_r,
		cast(sum(active_user_cnt)/count(distinct t_date) as decimal (10, 0)) day_avg_active_user,
		cast(sum(pay_user_cnt)/count(distinct t_date) as decimal (10, 0)) day_avg_pay_user,
		sum(active_user_cnt) active_user_cnt,sum(pay_user_cnt) pay_user_cnt,sum(pay_amount)/100 pay_amount,
		concat(FORMAT(sum(pay_user_cnt)/sum(active_user_cnt)*100,2),'%') pay_rate,
		FORMAT(sum(pay_amount)/100/sum(active_user_cnt),2) arpu,
		FORMAT(sum(pay_amount)/100/sum(pay_user_cnt),2) arppu,
		concat(FORMAT(sum(ret_2)/sum(case when ret_2>0 then active_user_cnt end)*100,2),'%') rd1,
		concat(FORMAT(sum(ret_3)/sum( case when ret_3>0 then active_user_cnt end)*100,2),'%') rd3,
		concat(FORMAT(sum(ret_7)/sum( case when ret_7>0 then active_user_cnt end )*100,2),'%') rd7,
		concat(FORMAT(sum(ret_14)/sum(case when ret_14>0 then active_user_cnt end)*100,2),'%') rd14,
		concat(FORMAT(sum(ret_30)/sum(case when ret_30>0 then active_user_cnt end)*100,2),'%') rd30,
		concat(FORMAT(sum(ret_60)/sum(case when ret_60>0 then active_user_cnt end)*100,2),'%') rd60,
		round(avg(avg_time)/60,2) avg_time,
		round(avg(median_time)/60,2) median_time
		FROM    ads_user_r_info_daily
		where   t_date<![CDATA[>=]]>#{start_date} and t_date<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="tag_r != null and tag_r != ''">
			and tag_r  in (${tag_r})
		</if>
		group by tag_r
		<if test="group != null and group != ''">
			,tdate
		</if>
		order by tdate
	</select>
	<select id="dyXyxInfoFlowList" resultType="com.wbgame.pojo.jettison.vo.DyxyxInfoFlowReportVo">
		select round(sum(totalIncome),2) totalIncome,round(sum(adIncome),2) adIncome,round(sum(orderIncome),2) orderIncome,round(sum(newAdIncome),2) newAdIncome,
		round(sum(newOrderIncome),2) newOrderIncome, round(sum(adConsume),2) adConsume,sum(adShowPv) adShowPv,sum(adClickPv) adClickPv,sum(cashAdPlayPv) cashAdPlayPv,
		<if test="group != null and group != ''">
			<choose>
			    <when test="group.contains('week')">
					 DATE_FORMAT(aa.tdate, '%x-%v') as tdate,
				 </when>
				<when test="group != null and group != '' and group.contains('month')">
					DATE_FORMAT(aa.tdate,'%Y-%m') as tdate,
				</when>
				<when test="group.contains('beek')">
					CONCAT(DATE_FORMAT(aa.tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(aa.tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(aa.tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS tdate,
				</when>
				<otherwise>
			 		aa.tdate tdate,
				</otherwise>
			</choose>
		</if>
		aa.appid appid,app.app_name appName,sum(loadSuccessPv) loadSuccessPv, sum(newUser) newUser, sum(activeUser) activeUser,
		round(sum(totalIncome)/sum(activeUser),2) arpu,round(sum(adIncome)/sum(activeUser),2) adArpu,round(sum(orderIncome)/sum(activeUser),2) orderArpu,
		round(sum(adConsume)/sum(adClickPv),2) cpc,round(sum(adConsume)/sum(newUser),2) cpa,round(sum(adIncome)/sum(cashAdPlayPv)*1000,2) ecpm,
		round(sum(cashAdPlayPv)/sum(activeUser),2) avgVideoShowTimes,round(sum(stayTimePerUser*activeUser)/sum(activeUser)/60,2) stayTimePerUser,
		
		round(sum(ltv1*newUser)/sum(newUser),2) ltv1, round(sum(ltv2*newUser)/sum(newUser),2) ltv2, round(sum(ltv3*newUser)/sum(newUser),2) ltv3,
		round(sum(ltv4*newUser)/sum(newUser),2) ltv4, round(sum(ltv5*newUser)/sum(newUser),2) ltv5, round(sum(ltv6*newUser)/sum(newUser),2) ltv6,
		round(sum(ltv7*newUser)/sum(newUser),2) ltv7, round(sum(ltv15*newUser)/sum(newUser),2) ltv15,round(sum(ltv30*newUser)/sum(newUser),2) ltv30,
		round(sum(ltv60*newUser)/sum(newUser),2) ltv60,round(sum(ltv150*newUser)/sum(newUser),2) ltv150,
       
        round(sum(adLtv1*newUser)/sum(newUser),2) adLtv1,round(sum(adLtv2*newUser)/sum(newUser),2) adLtv2 ,round(sum(adLtv3*newUser)/sum(newUser),2) adLtv3,
        round(sum(adLtv4*newUser)/sum(newUser),2) adLtv4,round(sum(adLtv5*newUser)/sum(newUser),2) adLtv5,round(sum(adLtv6*newUser)/sum(newUser),2) adLtv6,
        round(sum(adLtv7*newUser)/sum(newUser),2) adLtv7,round(sum(adLtv15*newUser)/sum(newUser),2) adLtv15 ,round(sum(adLtv30*newUser)/sum(newUser),2) adLtv30,
        round(sum(adLtv60*newUser)/sum(newUser),2) adLtv60 ,round(sum(adLtv150*newUser)/sum(newUser),2) adLtv150,
        
        round(sum(orderLtv1*newUser)/sum(newUser),2) orderLtv1,round(sum(orderLtv2*newUser)/sum(newUser),2) orderLtv2 ,round(sum(orderLtv3*newUser)/sum(newUser),2) orderLtv3,
        round(sum(orderLtv4*newUser)/sum(newUser),2) orderLtv4,round(sum(orderLtv5*newUser)/sum(newUser),2) orderLtv5,round(sum(orderLtv6*newUser)/sum(newUser),2) orderLtv6,
        round(sum(orderLtv7*newUser)/sum(newUser),2) orderLtv7,round(sum(orderLtv15*newUser)/sum(newUser),2) orderLtv15 ,round(sum(orderLtv30*newUser)/sum(newUser),2) orderLtv30,
        round(sum(orderLtv60*newUser)/sum(newUser),2) orderLtv60 ,round(sum(orderLtv150*newUser)/sum(newUser),2) orderLtv150, 
        
        
        round(sum(adClickPv)/sum(adShowPv),4) ctr,
		round(sum(totalIncome)/sum(adConsume),4) roi,round(sum(adIncome)/sum(adConsume),4) adRoi,round(sum(orderIncome)/sum(adConsume),4) orderRoi,
		
		round(sum(roi1*adConsume)/sum(adConsume),4) roi1,round(sum(roi2*adConsume)/sum(adConsume),4) roi2,round(sum(roi3*adConsume)/sum(adConsume),4) roi3,
		round(sum(roi4*adConsume)/sum(adConsume),4) roi4,round(sum(roi5*adConsume)/sum(adConsume),4) roi5,round(sum(roi6*adConsume)/sum(adConsume),4) roi6,
		round(sum(roi7*adConsume)/sum(adConsume),4) roi7,round(sum(roi15*adConsume)/sum(adConsume),4) roi15,round(sum(roi30*adConsume)/sum(adConsume),4) roi30,
		round(sum(roi60*adConsume)/sum(adConsume),4) roi60,round(sum(roi150*adConsume)/sum(adConsume),4) roi150,
		
		round(sum(adRoi1*adConsume)/sum(adConsume),4) adRoi1,round(sum(adRoi2*adConsume)/sum(adConsume),4) adRoi2,round(sum(adRoi3*adConsume)/sum(adConsume),4) adRoi3,
		round(sum(adRoi4*adConsume)/sum(adConsume),4) adRoi4,round(sum(adRoi5*adConsume)/sum(adConsume),4) adRoi5,round(sum(adRoi6*adConsume)/sum(adConsume),4) adRoi6,
		round(sum(adRoi7*adConsume)/sum(adConsume),4) adRoi7,round(sum(adRoi15*adConsume)/sum(adConsume),4) adRoi15,round(sum(adRoi30*adConsume)/sum(adConsume),4) adRoi30,
		round(sum(adRoi60*adConsume)/sum(adConsume),4) adRoi60,round(sum(adRoi150*adConsume)/sum(adConsume),4) adRoi150,
		
		round(sum(orderRoi1*adConsume)/sum(adConsume),4) orderRoi1,round(sum(orderRoi2*adConsume)/sum(adConsume),4) orderRoi2,round(sum(orderRoi3*adConsume)/sum(adConsume),4) orderRoi3,
		round(sum(orderRoi4*adConsume)/sum(adConsume),4) orderRoi4,round(sum(orderRoi5*adConsume)/sum(adConsume),4) orderRoi5,round(sum(orderRoi6*adConsume)/sum(adConsume),4) orderRoi6,
		round(sum(orderRoi7*adConsume)/sum(adConsume),4) orderRoi7,round(sum(orderRoi15*adConsume)/sum(adConsume),4) orderRoi15,round(sum(orderRoi30*adConsume)/sum(adConsume),4) orderRoi30,
		round(sum(orderRoi60*adConsume)/sum(adConsume),4) orderRoi60,round(sum(orderRoi150*adConsume)/sum(adConsume),4) orderRoi150
		from dn_douyin_xyx_feed_spend_data aa
		left join dn_douyin_xyx_ltv_data bb on aa.tdate=bb.tdate  and aa.app_id=bb.app_id
		left join  dn_douyin_xyx_roi_data cc on aa.tdate=cc.tdate  and aa.app_id=cc.app_id
		left join  app_info app on aa.appid=app.id
		where aa.tdate <![CDATA[>=]]>#{start_date} and aa.tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
			and aa.appid in (${appid})
		</if>
		GROUP BY appid
		<if test="group != null and group != ''">
			<choose>
			    <when test="group.contains('week')">
					 ,DATE_FORMAT(aa.tdate, '%x-%v')
				 </when>
				<when test="group.contains('month')">
					,DATE_FORMAT(aa.tdate,'%Y-%m')
				</when>
				<when test="group.contains('beek')">
					,CONCAT(DATE_FORMAT(aa.tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(aa.tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(aa.tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))
				</when>
				<otherwise>
			 		,aa.tdate
				</otherwise>
			</choose>
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				<choose>
					<when test="group != null and group != ''">
						order by tdate asc
					</when>
					<otherwise>
						order by adConsume desc
					</otherwise>
				</choose>
			</otherwise>
		</choose>
	</select>
	<select id="dyXyxInfoFlowTotal" resultType="com.wbgame.pojo.jettison.vo.DyxyxInfoFlowReportVo">
		select round(sum(totalIncome),2) totalIncome,round(sum(adIncome),2) adIncome,round(sum(orderIncome),2) orderIncome,round(sum(newAdIncome),2) newAdIncome,
		round(sum(newOrderIncome),2) newOrderIncome, round(sum(adConsume),2) adConsume,sum(adShowPv) adShowPv,sum(adClickPv) adClickPv,sum(cashAdPlayPv) cashAdPlayPv,
		sum(loadSuccessPv) loadSuccessPv, sum(newUser) newUser, sum(activeUser) activeUser,
		round(sum(totalIncome)/sum(activeUser),2) arpu,round(sum(adIncome)/sum(activeUser),2) adArpu,round(sum(orderIncome)/sum(activeUser),2) orderArpu,
		round(sum(adConsume)/sum(adClickPv),2) cpc,round(sum(adConsume)/sum(newUser),2) cpa,round(sum(adIncome)/sum(cashAdPlayPv)*1000,2) ecpm,
		round(sum(cashAdPlayPv)/sum(activeUser),2) avgVideoShowTimes,round(sum(stayTimePerUser*activeUser)/sum(activeUser)/60,2) stayTimePerUser,
		
		round(sum(ltv1*newUser)/sum(newUser),2) ltv1, round(sum(ltv2*newUser)/sum(newUser),2) ltv2, round(sum(ltv3*newUser)/sum(newUser),2) ltv3,
		round(sum(ltv4*newUser)/sum(newUser),2) ltv4, round(sum(ltv5*newUser)/sum(newUser),2) ltv5, round(sum(ltv6*newUser)/sum(newUser),2) ltv6,
		round(sum(ltv7*newUser)/sum(newUser),2) ltv7, round(sum(ltv15*newUser)/sum(newUser),2) ltv15,round(sum(ltv30*newUser)/sum(newUser),2) ltv30,
		round(sum(ltv60*newUser)/sum(newUser),2) ltv60,round(sum(ltv150*newUser)/sum(newUser),2) ltv150,
       
        round(sum(adLtv1*newUser)/sum(newUser),2) adLtv1,round(sum(adLtv2*newUser)/sum(newUser),2) adLtv2 ,round(sum(adLtv3*newUser)/sum(newUser),2) adLtv3,
        round(sum(adLtv4*newUser)/sum(newUser),2) adLtv4,round(sum(adLtv5*newUser)/sum(newUser),2) adLtv5,round(sum(adLtv6*newUser)/sum(newUser),2) adLtv6,
        round(sum(adLtv7*newUser)/sum(newUser),2) adLtv7,round(sum(adLtv15*newUser)/sum(newUser),2) adLtv15 ,round(sum(adLtv30*newUser)/sum(newUser),2) adLtv30,
        round(sum(adLtv60*newUser)/sum(newUser),2) adLtv60 ,round(sum(adLtv150*newUser)/sum(newUser),2) adLtv150,
        
        round(sum(orderLtv1*newUser)/sum(newUser),2) orderLtv1,round(sum(orderLtv2*newUser)/sum(newUser),2) orderLtv2 ,round(sum(orderLtv3*newUser)/sum(newUser),2) orderLtv3,
        round(sum(orderLtv4*newUser)/sum(newUser),2) orderLtv4,round(sum(orderLtv5*newUser)/sum(newUser),2) orderLtv5,round(sum(orderLtv6*newUser)/sum(newUser),2) orderLtv6,
        round(sum(orderLtv7*newUser)/sum(newUser),2) orderLtv7,round(sum(orderLtv15*newUser)/sum(newUser),2) orderLtv15 ,round(sum(orderLtv30*newUser)/sum(newUser),2) orderLtv30,
        round(sum(orderLtv60*newUser)/sum(newUser),2) orderLtv60 ,round(sum(orderLtv150*newUser)/sum(newUser),2) orderLtv150, 
        
        
        round(sum(adClickPv)/sum(adShowPv),4) ctr,
		round(sum(totalIncome)/sum(adConsume),4) roi,round(sum(adIncome)/sum(adConsume),4) adRoi,round(sum(orderIncome)/sum(adConsume),4) orderRoi,
		
		round(sum(roi1*adConsume)/sum(adConsume),4) roi1,round(sum(roi2*adConsume)/sum(adConsume),4) roi2,round(sum(roi3*adConsume)/sum(adConsume),4) roi3,
		round(sum(roi4*adConsume)/sum(adConsume),4) roi4,round(sum(roi5*adConsume)/sum(adConsume),4) roi5,round(sum(roi6*adConsume)/sum(adConsume),4) roi6,
		round(sum(roi7*adConsume)/sum(adConsume),4) roi7,round(sum(roi15*adConsume)/sum(adConsume),4) roi15,round(sum(roi30*adConsume)/sum(adConsume),4) roi30,
		round(sum(roi60*adConsume)/sum(adConsume),4) roi60,round(sum(roi150*adConsume)/sum(adConsume),4) roi150,
		
		round(sum(adRoi1*adConsume)/sum(adConsume),4) adRoi1,round(sum(adRoi2*adConsume)/sum(adConsume),4) adRoi2,round(sum(adRoi3*adConsume)/sum(adConsume),4) adRoi3,
		round(sum(adRoi4*adConsume)/sum(adConsume),4) adRoi4,round(sum(adRoi5*adConsume)/sum(adConsume),4) adRoi5,round(sum(adRoi6*adConsume)/sum(adConsume),4) adRoi6,
		round(sum(adRoi7*adConsume)/sum(adConsume),4) adRoi7,round(sum(adRoi15*adConsume)/sum(adConsume),4) adRoi15,round(sum(adRoi30*adConsume)/sum(adConsume),4) adRoi30,
		round(sum(adRoi60*adConsume)/sum(adConsume),4) adRoi60,round(sum(adRoi150*adConsume)/sum(adConsume),4) adRoi150,
		
		round(sum(orderRoi1*adConsume)/sum(adConsume),4) orderRoi1,round(sum(orderRoi2*adConsume)/sum(adConsume),4) orderRoi2,round(sum(orderRoi3*adConsume)/sum(adConsume),4) orderRoi3,
		round(sum(orderRoi4*adConsume)/sum(adConsume),4) orderRoi4,round(sum(orderRoi5*adConsume)/sum(adConsume),4) orderRoi5,round(sum(orderRoi6*adConsume)/sum(adConsume),4) orderRoi6,
		round(sum(orderRoi7*adConsume)/sum(adConsume),4) orderRoi7,round(sum(orderRoi15*adConsume)/sum(adConsume),4) orderRoi15,round(sum(orderRoi30*adConsume)/sum(adConsume),4) orderRoi30,
		round(sum(orderRoi60*adConsume)/sum(adConsume),4) orderRoi60,round(sum(orderRoi150*adConsume)/sum(adConsume),4) orderRoi150
		from dn_douyin_xyx_feed_spend_data aa
		left join dn_douyin_xyx_ltv_data bb on aa.tdate=bb.tdate  and aa.app_id=bb.app_id
		left join  dn_douyin_xyx_roi_data cc on aa.tdate=cc.tdate  and aa.app_id=cc.app_id
		where aa.tdate <![CDATA[>=]]>#{start_date} and aa.tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
			and aa.appid in (${appid})
		</if>
	</select>
</mapper>