<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsWechatUserStartupInfoDailyMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.AdsWechatUserStartupInfoDaily">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="tdate" property="tdate" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
        <result column="online_time_avg_new" property="onlineTimeAvgNew" jdbcType="DECIMAL"/>
        <result column="online_startup_median_new" property="onlineStartupMedianNew" jdbcType="BIGINT"/>
        <result column="online_startup_avg_new" property="onlineStartupAvgNew" jdbcType="DECIMAL"/>
        <result column="online_time_median_new" property="onlineTimeMedianNew" jdbcType="DECIMAL"/>
        <result column="online_time_avg_old" property="onlineTimeAvgOld" jdbcType="DECIMAL"/>
        <result column="online_startup_median_old" property="onlineStartupMedianOld" jdbcType="BIGINT"/>
        <result column="online_startup_avg_old" property="onlineStartupAvgOld" jdbcType="DECIMAL"/>
        <result column="online_time_median_old" property="onlineTimeMedianOld" jdbcType="DECIMAL"/>
        <result column="online_time_new" property="onlineTimeNew" jdbcType="BIGINT"/>
        <result column="online_time_old" property="onlineTimeOld" jdbcType="BIGINT"/>
        <result column="ad_buy_media" property="adBuyMedia" jdbcType="VARCHAR"/>
        <result column="reg_user_cnt" property="regUserCnt" jdbcType="VARCHAR"/>
        <result column="old_user_cnt" property="oldUserCnt" jdbcType="VARCHAR"/>

    </resultMap>

    <select id="selectWechatUserStartupInfo" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.clean.AdsWechatUserStartupInfoDaily">

        <include refid="selectData"/>

    </select>

    <select id="selectWechatUserStartupInfoNotMedia" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.clean.AdsWechatUserStartupInfoDaily">


        <include refid="notMedia"/>
    </select>

    <select id="countWechatUserStartupInfo" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.clean.AdsWechatUserStartupInfoDaily">

        select
            round(avg(online_time_avg_new),2)       online_time_avg_new,
            round(avg(online_startup_median_new),2) online_startup_median_new,
            round(avg(online_startup_avg_new),2)    online_startup_avg_new,
            round(avg(online_time_median_new),2)    online_time_median_new,
            round(avg(online_time_avg_old),2)       online_time_avg_old,
            round(avg(online_startup_median_old),2) online_startup_median_old,
            round(avg(online_startup_avg_old),2)    online_startup_avg_old,
            round(avg(online_time_median_old),2)    online_time_median_old,
            cast(avg(online_time_new) as decimal(18, 2))    online_time_new,
            cast(avg(online_time_old) as decimal(18, 2))    online_time_old,
            round(avg(reg_user_cnt),2)    reg_user_cnt,
            round(avg(old_user_cnt),2)    old_user_cnt

        from  (

            <include refid="selectData"/>
            ) a

    </select>

    <select id="countWechatUserStartupInfoNotMedia" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.clean.AdsWechatUserStartupInfoDaily">

        select
            cast(avg(online_time_avg_new) as decimal(18, 2))       online_time_avg_new,
            cast(avg(online_startup_median_new) as decimal(18, 2)) online_startup_median_new,
            cast(avg(online_startup_avg_new) as decimal(18, 2))    online_startup_avg_new,
            cast(avg(online_time_median_new) as decimal(18, 2))    online_time_median_new,
            cast(avg(online_time_avg_old) as decimal(18, 2))       online_time_avg_old,
            cast(avg(online_startup_median_old) as decimal(18, 2)) online_startup_median_old,
            cast(avg(online_startup_avg_old) as decimal(18, 2))    online_startup_avg_old,
            cast(avg(online_time_median_old) as decimal(18, 2))    online_time_median_old,
            cast(avg(online_time_new) as decimal(18, 2))   online_time_new,
            cast(avg(online_time_old) as decimal(18, 2))   online_time_old,
            cast(avg(reg_user_cnt) as decimal(18, 2))    reg_user_cnt,
            cast(avg(old_user_cnt) as decimal(18, 2))    old_user_cnt

        from  (

            <include refid="notMedia"/>
            ) a

    </select>


    <sql id="selectData" >

        select
               <if test="group != null and group != ''">
                   ${group},
               </if>
               ifnull(cast(avg(online_time_avg_new) / 60 as decimal(18,2)), 0) online_time_avg_new,
               truncate(ifnull(avg(online_startup_median_new), 0), 2) online_startup_median_new,
               cast(avg(online_startup_avg_new) as decimal(18,2) ) online_startup_avg_new,
               ifnull(cast(avg(online_time_median_new)/60 as decimal(18,2)),0) online_time_median_new,
               cast(ifnull(sum(online_time_avg_new) / 60 / sum(online_startup_avg_new), 0) as decimal(18,2)) online_time_new,

               ifnull(cast(avg(online_time_avg_old) / 60 as decimal(18,2)), 0) online_time_avg_old,
               truncate(ifnull(avg(online_startup_median_old), 0), 2) online_startup_median_old,
               cast(avg(online_startup_avg_old) as decimal(18, 2)) online_startup_avg_old,
               ifnull(cast(avg(online_time_median_old)/60 as decimal(18,2)),0) online_time_median_old,
               cast(ifnull(sum(online_time_avg_old) / 60 / sum(online_startup_avg_old), 0) as decimal(18,2)) online_time_old,

               cast(avg(reg_user_cnt) as decimal(18, 2)) reg_user_cnt,
               cast(avg(old_user_cnt) as decimal(18, 2)) old_user_cnt

        from ads_wechat_user_startup_info_daily
        
        <where>

            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and tdate between #{start_date} and #{end_date}
            </if>
            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
            <if test="ad_buy_media != null and ad_buy_media != ''">
                and ad_buy_media in (${ad_buy_media})
            </if>

        </where>

        <if test="group != null and group != ''">
            group by  ${group}
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc, appid desc
            </otherwise>
        </choose>


    </sql>

<!--    不选择媒体维度的sql-->
    <sql id="notMedia">

        select

        <if test="group != null and group != ''">
            ${group},
        </if>

        cast(sum(online_time_avg_new) / sum(reg_user_cnt) as decimal (18,2)) online_time_avg_new,
        cast(sum(online_startup_median_new) / sum(reg_user_cnt) as decimal (18,2)) online_startup_median_new,
        cast(sum(online_startup_avg_new) / sum(reg_user_cnt) as decimal (18,2)) online_startup_avg_new,
        cast(sum(online_time_median_new) / sum(reg_user_cnt) as decimal (18,2)) online_time_median_new,
        cast(ifnull(cast(sum(online_time_avg_new) / sum(reg_user_cnt) as decimal (18,2)) / cast(sum(online_startup_avg_new) / sum(reg_user_cnt) as decimal (18,2)), 0) as decimal(18,2)) online_time_new,

        cast(sum(online_time_avg_old) / sum(old_user_cnt) as decimal (18,2)) online_time_avg_old,
        cast(sum(online_startup_median_old) / sum(old_user_cnt) as decimal (18,2)) online_startup_median_old,
        cast(sum(online_startup_avg_old) / sum(old_user_cnt) as decimal (18,2)) online_startup_avg_old,
        cast(sum(online_time_median_old) / sum(old_user_cnt) as decimal (18,2)) online_time_median_old,
        cast(ifnull(cast(sum(online_time_avg_old) / sum(old_user_cnt) as decimal (18,2)) / cast(sum(online_startup_avg_old) / sum(old_user_cnt) as decimal (18,2)), 0) as decimal(18,2)) online_time_old,

        cast(sum(reg_user_cnt) as decimal(18, 2)) reg_user_cnt,
        cast(sum(old_user_cnt) as decimal(18, 2)) old_user_cnt


        from (

        select
        tdate,appid, app_name, ad_buy_media,
        online_time_avg_new * reg_user_cnt         online_time_avg_new,
        online_startup_median_new * reg_user_cnt   online_startup_median_new,
        online_startup_avg_new * reg_user_cnt      online_startup_avg_new,
        online_time_median_new * reg_user_cnt      online_time_median_new,

        online_time_avg_old * old_user_cnt       online_time_avg_old,
        online_startup_median_old * old_user_cnt online_startup_median_old,
        online_startup_avg_old * old_user_cnt    online_startup_avg_old,
        online_time_median_old * old_user_cnt    online_time_median_old,

        online_time_new ,
        online_time_old ,
        reg_user_cnt ,
        old_user_cnt

        from  (

        select
        tdate,appid, app_name, ad_buy_media,
        ifnull(cast(avg(online_time_avg_new) / 60 as decimal(18,2)), 0) online_time_avg_new,
        truncate(ifnull(avg(online_startup_median_new), 0), 2) online_startup_median_new,
        cast(avg(online_startup_avg_new) as decimal(18,2) ) online_startup_avg_new,
        ifnull(cast(avg(online_time_median_new)/60 as decimal(18,2)),0) online_time_median_new,
        cast(ifnull(sum(online_time_avg_new) / 60 / sum(online_startup_avg_new), 0) as decimal(18,2)) online_time_new,

        ifnull(cast(avg(online_time_avg_old) / 60 as decimal(18,2)), 0) online_time_avg_old,
        truncate(ifnull(avg(online_startup_median_old), 0), 2) online_startup_median_old,
        cast(avg(online_startup_avg_old) as decimal(18, 2)) online_startup_avg_old,
        ifnull(cast(avg(online_time_median_old)/60 as decimal(18,2)),0) online_time_median_old,
        cast(ifnull(sum(online_time_avg_old) / 60 / sum(online_startup_avg_old), 0) as decimal(18,2)) online_time_old,

        cast(sum(reg_user_cnt) as decimal(18, 2)) reg_user_cnt,
        cast(sum(old_user_cnt) as decimal(18, 2)) old_user_cnt

        from ads_wechat_user_startup_info_daily

        <where>

            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and tdate between #{start_date} and #{end_date}
            </if>
            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
            <if test="ad_buy_media != null and ad_buy_media != ''">
                and ad_buy_media in (${ad_buy_media})
            </if>

        </where>

        group by tdate,appid, app_name, ad_buy_media
        ) a

        ) b

        <if test="group != null and group != ''">
            group by  ${group}
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc, appid desc
            </otherwise>
        </choose>
    </sql>
</mapper>