<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adb.CashPlatformDetailMapper">

    <select id="selectDnChaCashTotal" resultType="com.wbgame.pojo.response.DnChaCashTotalResponse">
        <include refid="dncha_cash_sql"/>
    </select>
    <select id="selectDnChaCashTotalSum" resultType="com.wbgame.pojo.response.DnChaCashTotalResponse">
        select
        CONVERT(sum(xx.request_count),SIGNED) request_count,
        CONVERT(sum(xx.return_count),SIGNED) return_count,
        CONCAT(ROUND(sum(xx.return_count)/sum(xx.request_count)*100, 2),'%') fill_rate,
        CONVERT(sum(xx.pv),SIGNED) pv,
        CONCAT(ROUND(sum(xx.pv)/sum(xx.return_count)*100, 2),'%') pv_rate,
        TRUNCATE(sum(xx.revenue),2) revenue,
        TRUNCATE(sum(xx.dollar_revenue),3) dollar_revenue,
        TRUNCATE(sum(xx.dollar_revenue)/sum(xx.pv)*1000,3) ecpm,
        CONVERT(sum(xx.click),SIGNED) click,
        CONCAT(ROUND(sum(xx.click)/sum(xx.pv)*100, 2),'%') click_rate,
        CONCAT(IFNULL(CAST(SUM(xx.dollar_revenue)/SUM(click) AS decimal(18,3)), 0),'') cpc
        from (<include refid="dncha_cash_sql"/>) xx
    </select>

    <sql id="dncha_cash_sql">
        select  d.cha_sub_launch, d.cha_type_name,a.app_name appname,d.`out`,
        CONVERT(IFNULL(TRUNCATE(sum(request_count),2), 0),SIGNED) request_count,
        CONVERT(IFNULL(TRUNCATE(sum(return_count),2), 0),SIGNED) return_count,
        CONVERT(IFNULL(TRUNCATE(sum(pv),2), 0),SIGNED) pv,
        IFNULL(TRUNCATE(sum(revenue),2), 0) revenue,
        IFNULL(TRUNCATE(sum(dollar_revenue),3), 0) dollar_revenue,
        IFNULL(TRUNCATE(sum(dollar_revenue)/sum(pv)*1000,3), 0) ecpm,
        CONVERT(IFNULL(TRUNCATE(sum(click),2), 0),SIGNED) click,
        IFNULL(TRUNCATE(sum(return_count)/sum(request_count)*100,2), 0) fill_rate,
        IFNULL(TRUNCATE(sum(pv)/sum(return_count)*100,2), 0) pv_rate,
        IFNULL(TRUNCATE(sum(click)/sum(pv)*100,2), 0) click_rate,
        IFNULL(CAST(SUM(dollar_revenue)/SUM(click) AS decimal(18,3)), 0) cpc
        <if test="groupList != null and groupList.size > 0">
            ,
            <foreach collection="groupList" index="index" item="item" separator=",">
                <choose>
                    <when test="item == 'sdk_appid'">
                        d.app_id sdk_appid
                    </when>
                    <when test="item == 'sdk_code'">
                        d.placement_id sdk_code
                    </when>
                    <otherwise>
                        `${item}`
                    </otherwise>
                </choose>
            </foreach>
        </if>
        FROM dnwx_bi.ods_dn_cha_cash_total_hw d
        LEFT JOIN dnwx_bi.app_info a on d.dnappid = a.id
        WHERE `date` BETWEEN #{start_date} AND #{end_date} and d.app_id != '0'
        <if test="dnappid != null and dnappid != ''">
            and dnappid in (${dnappid})
        </if>
        <if test="cha_type != null and cha_type != ''">
            and cha_type in (${cha_type})
        </if>
        <if test="cha_media != null and cha_media != ''">
            and cha_media in (${cha_media})
        </if>
        <if test="cha_sub_launch != null and cha_sub_launch != ''">
            and cha_sub_launch = #{cha_sub_launch}
        </if>
        <if test="cha_id != null and cha_id != ''">
            and cha_id in (${cha_id})
        </if>
        <if test="agent != null and agent != ''">
            and agent = #{agent}
        </if>
        <if test="placement_type != null and placement_type != ''">
            and placement_type = #{placement_type}
        </if>
        <if test="ad_sid != null and ad_sid != ''">
            and ad_sid like concat('%',#{ad_sid},'%')
        </if>
        <if test="detail_ad_sid != null and detail_ad_sid != ''">
            and ad_sid = #{detail_ad_sid}
        </if>
        <if test="country != null and country != ''">
            and country = #{country}
        </if>
        <if test="open_type != null and open_type != ''">
            and open_type = #{open_type}
        </if>
        <if test="out != null and out != ''">
            and `out` = #{out}
        </if>
        <if test="sdk_code != null and sdk_code != ''">
            and d.placement_id = #{sdk_code}
        </if>
        <if test="sdk_appid != null and sdk_appid != ''">
            and d.app_id = #{sdk_appid}
        </if>
        <if test="app_category != null and app_category != ''">
            and a.app_category in (${app_category})
        </if>
        <if test="appid_tag != null and appid_tag != ''">
            <choose>
                <when test="appid_tag_rev != null and appid_tag != ''">
                    AND CONCAT(dnappid,'#',cha_id) not in (${appid_tag})
                </when>
                <otherwise>
                    AND CONCAT(dnappid,'#',cha_id) in (${appid_tag})
                </otherwise>
            </choose>
        </if>
        <if test="groupList != null and groupList.size > 0">
            GROUP BY
            <foreach collection="groupList" index="index" item="item" separator=",">
                `${item}`
            </foreach>
        </if>
        <if test="ad_sid_group != null and ad_sid_group != ''">
            having (sum(ifnull(pv,0))+sum(ifnull(revenue,0))+sum(ifnull(request_count,0))) > 0
        </if>
        <if test="order_str == null or order_str == ''">
            ORDER BY `date` ASC, revenue DESC
        </if>
        <if test="order_str != null and order_str != ''">
            ORDER BY
            <foreach collection="groupList" index="index" item="item" >
                CASE WHEN ${item} is null or ${item}='' THEN 1 ELSE 0 END,
            </foreach>
            ${order_str}
        </if>
    </sql>

</mapper>