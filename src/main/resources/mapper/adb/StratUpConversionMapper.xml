<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">



<mapper namespace="com.wbgame.mapper.adb.StratUpConversionMapper">
	<select id="list" resultType="com.wbgame.pojo.jettison.vo.StratUpConversionVo" parameterType="com.wbgame.pojo.jettison.param.StratUpConversionParam"> 
		select
		sum(login_cnt) login_cnt,sum(active_cnt) active_cnt,
		<if test="null!=group and group!=''">
			${group},
		</if>
		i.app_name app_name,
		round(sum(active_cnt)/sum(login_cnt),4) conversion_rate
		FROM    ads_login_active_cnt_daily a left join app_info i on a.appid=i.id
		where   tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and channel  in (${channel})
		</if>
		<if test="account != null and account != ''">
			and account  in (${account})
		</if>
		<if test="group_name != null and group_name != ''">
			and group_name  in (${group_name})
		</if>
		<if test="adsenseposition != null and adsenseposition.size > 0">
			AND (
			<foreach collection="adsenseposition" index="index" item="item" separator="OR">
				adsenseposition LIKE  concat('%',${item},'%')
			</foreach>
			)
		</if>
		<if test="campaign_id != null and campaign_id != ''">
			and campaign_id  in (${campaign_id})
		</if>
		<if test="min_login_cnt != null">
			and login_cnt <![CDATA[>=]]>#{min_login_cnt}
		</if>
		<if test="group != null and group != ''">
			group by ${group}
		</if>
		<choose >
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate desc ,active_cnt desc 
			</otherwise>
		</choose>
	</select>
	<select id="total" resultType="com.wbgame.pojo.jettison.vo.StratUpConversionVo" parameterType="com.wbgame.pojo.jettison.param.StratUpConversionParam"> 
		select
		sum(login_cnt) login_cnt,sum(active_cnt) active_cnt,
		round(sum(active_cnt)/sum(login_cnt),4) conversion_rate
		FROM    ads_login_active_cnt_daily
		where   tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and channel  in (${channel})
		</if>
		<if test="account != null and account != ''">
			and account  in (${account})
		</if>
		<if test="group_name != null and group_name != ''">
			and group_name  in (${group_name})
		</if>
		<if test="adsenseposition != null and adsenseposition.size > 0">
			AND (
			<foreach collection="adsenseposition" index="index" item="item" separator="OR">
				adsenseposition LIKE  concat('%',${item},'%')
			</foreach>
			)
		</if>
		<if test="min_login_cnt != null">
			and login_cnt <![CDATA[>=]]>#{min_login_cnt}
		</if>
		<if test="campaign_id != null and campaign_id != ''">
			and campaign_id  in (${campaign_id})
		</if>
	</select>
</mapper>