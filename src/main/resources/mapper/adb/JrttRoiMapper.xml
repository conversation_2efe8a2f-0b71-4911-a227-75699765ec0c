<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.JrttRoiMapper">

	
	
	<!-- 头条广告ROI报表列表查询  -->
  	<select id="jrttAdRoiReport" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam" resultType="com.wbgame.pojo.jettison.AdRoiReportDTO2">
		<include refid="ad_roi_sql"/> 
		group by appId
		<if test="group != null and group != ''">
			,${group}
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				<choose>
					<when test='customizes.contains("spend") and group.contains("day")'>
						order by day asc ,spend desc
					</when>
					<when test='customizes.contains("spend") '>
						order by spend desc
					</when>
					<when test='group.contains("day") '>
						order by day asc
					</when>
				</choose>
			</otherwise>
		</choose>
	</select>
	<!-- 广告ROI报表汇总  -->
	<select id="jrttAdRoiReportTotal" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam"  resultType="com.wbgame.pojo.jettison.AdRoiReportDTO2">
			<include refid="ad_roi_sql"/>
	</select>
	
	<!-- 微信小游戏头条广告ROI报表列表查询  -->
  	<select id="jrttWxGameAdRoiReport" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam" resultType="com.wbgame.pojo.jettison.AdRoiReportDTO2">
		<include refid="wx_game_ad_roi_sql"/> 
		group by appId
		<if test="group != null and group != ''">
			,${group}
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				<choose>
					<when test='customizes.contains("spend") and group.contains("day")'>
						order by day asc ,spend desc
					</when>
					<when test='customizes.contains("spend") '>
						order by spend desc
					</when>
					<when test='group.contains("day") '>
						order by day asc
					</when>
				</choose>
			</otherwise>
		</choose>
	</select>
	<!-- 微信小游戏头条广告ROI报表汇总  -->
	<select id="jrttWxGameAdRoiReportTotal" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam"  resultType="com.wbgame.pojo.jettison.AdRoiReportDTO2">
			<include refid="wx_game_ad_roi_sql"/>
	</select>
	<sql id="ad_roi_sql">
		SELECT sum((add_user)) u,
		<if test='group.contains("day")'>
			day,
		</if>
		<if test='group.contains("week")'>
			DATE_FORMAT(day,'%Y-%u') day,
			DATE_FORMAT(day,'%Y-%u') week,
		</if>
		<if test='group.contains("month")'>
			DATE_FORMAT(day,'%Y-%m') day,
			DATE_FORMAT(day,'%Y-%m') month,
		</if>
		<if test='group.contains("media")'>
			IFNULL(media,'自然量') media,
		</if>
		<if test='group.contains("channel")'>
			IFNULL(channel,'自然量') channel,
		</if>
		<if test='group.contains("accountId")'>
			account accountId,
		</if>
		<if test='group.contains("putUser")'>
			putUser putUser,
		</if>
		<if test='group.contains("groupName")'>
			group_name groupName,
		</if>
		<if test='group.contains("campaignName")'>
			campaign_name campaignName,
		</if>
		<if test='group.contains("campaignId")'>
			campaign_id campaignId,
		</if>
		<if test='group.contains("accountType")'>
			account_type accountType,
		</if>
		<if test='group.contains("strategy")'>
			transfer_type strategy,
		</if>
		<if test='group.contains("company")'>
			company company,
		</if>
		<if test='group.contains("adsensePosition")'>
			adsensePosition adsensePosition,
		</if>
		<if test='group.contains("anchor_related_type")'>
			anchor_related_type,
		</if>
		<if test='group.contains("delivery_mode")'>
			delivery_mode,
		</if>
		<if test='customizes.contains("addUser")'>
			sum(add_user) addUser,
		</if>
		<if test='customizes.contains("activeUser")'>
			sum(active_user) activeUser,
		</if>
		<if test='customizes.contains("spend")'>
			round(sum(rebateCost)/100,2) spend,
		</if>
		<if test='customizes.contains("dupAddUser")'>
			sum(dup_add_user) as dupAddUser,
		</if>
		<if test='customizes.contains("cpa")'>
			round(sum(rebateCost)/100/sum(add_user),2) cpa,
		</if>
		<if test='customizes.contains("avgVideo")'>
			round(sum(add_user_pv_video)/sum(add_user),2) avgVideo,
		</if>
		<if test='customizes.contains("avgPlaque")'>
			round(sum(add_user_pv_plaque)/sum(add_user),2) avgPlaque,
		</if>
		<if test='customizes.contains("activeAvgVideo")'>
			round(sum(active_user_pv_video)/sum(active_user),2) activeAvgVideo,
		</if>
		<if test='customizes.contains("activeAvgPlaque")'>
			round(sum(active_user_pv_plaque)/sum(active_user),2) activeAvgPlaque,
		</if>
		<if test='customizes.contains("videoEcpm")'>
			round(sum(add_user_revenue_video)/sum(add_user_pv_video)*10,2) videoEcpm,
		</if>
		<if test='customizes.contains("plaqueEcpm")'>
			round(sum(add_user_revenue_plaque)/sum(add_user_pv_plaque)*10,2) plaqueEcpm,
		</if>
		<if test='customizes.contains("firstPayCount")'>
			sum(payCount) firstPayCount,
		</if>
		<if test='customizes.contains("paymentTimes")'>
			sum(gamePayCount) paymentTimes,
		</if>
		<if test='customizes.contains("firstChargeCost")'>
			FORMAT(sum(rebateCost)/100/sum(payCount),2) firstChargeCost,
		</if>
		<if test='customizes.contains("firstPaymentRate")'>
			CONCAT(IFNULL(FORMAT(sum(payCount)/sum(installs)*100,2),0.00),'%') firstPaymentRate,
		</if>
		<if test='customizes.contains("paymentTimes7")'>
			sum(payCount7) paymentTimes7,
		</if>
		<if test='customizes.contains("avgPayCount7")'>
			round(sum(payCount7)/sum(payCountFirst7),2) avgPayCount7,
		</if>
		<if test='customizes.contains("payCost")'>
			FORMAT(sum(rebateCost)/100/sum(gamePayCount),2)  payCost,
		</if>
		<if test='customizes.contains("addPurchaseUsers")'>
			sum(add_purchase_users) addPurchaseUsers,
		</if>
		<if test='customizes.contains("activePurchaseUsers")'>
			sum(active_purchase_users) activePurchaseUsers,
		</if>
		<if test='customizes.contains("addUserPayCost")'>
			round(sum(rebateCost)/100/sum(add_purchase_users),2) addUserPayCost,
		</if>
		<if test='customizes.contains("addUserPayArpu")'>
			round(sum(add_purchase_revenue_1)/100/sum(add_purchase_users),2) addUserPayArpu,
		</if>
		<if test='customizes.contains("addUserPayRate")'>
			CONCAT(IFNULL(FORMAT(sum(add_purchase_users)/sum(add_user)*100,2),0.00),'%') addUserPayRate,
		</if>
		<if test='customizes.contains("payRate")'>
			CONCAT(IFNULL(FORMAT(sum(active_purchase_users)/sum(active_user)*100,2),0.00),'%') payRate,
		</if>
		<if test='customizes.contains("payArpu")'>
			round(sum(active_purchase_revenue)/100/sum(active_purchase_users),2) payArpu,
		</if>
		<if test='customizes.contains("convertSpend")'>
			FORMAT(sum(rebateCost)/100/sum(`convert`),2) convertSpend,
		</if>
		<if test='customizes.contains("registerSpend")'>
			FORMAT(sum(rebateCost)/100/sum(register),2) registerSpend,
		</if>
		<if test='customizes.contains("addictionSpend")'>
			FORMAT(sum(rebateCost)/100/sum(game_addiction),2) addictionSpend,
		</if>
		<if test='customizes.contains("avgShowSpend")'>
			FORMAT(sum(rebateCost)/sum(impressions)*10,2) avgShowSpend,
		</if>
		<if test='customizes.contains("convertRate")'>
			CONCAT(IFNULL(FORMAT(sum(`convert`)/sum(clicks)*100,2),0.00),'%') convertRate,
		</if>
		<if test='customizes.contains("installRate")'>
			CONCAT(IFNULL(FORMAT(sum(installs)/sum(clicks)*100,2),0.00),'%') installRate,
		</if>
		<if test='customizes.contains("registerRate")'>
			CONCAT(IFNULL(FORMAT(sum(register)/sum(installs)*100,2),0.00),'%') registerRate,
		</if>
		<if test='customizes.contains("addictionRate")'>
			CONCAT(IFNULL(FORMAT(sum(game_addiction)/sum(installs)*100,2),0.00),'%') addictionRate,
		</if>
		<if test='customizes.contains("clickRate")'>
			CONCAT(IFNULL(FORMAT(sum(clicks)/sum(impressions)*100,2),0.00),'%') clickRate,
		</if>
		<if test='customizes.contains("gameAddiction")'>
			sum(game_addiction) as gameAddiction,
		</if>
		<if test='customizes.contains("rdOneRate")'>
			<choose>
				<when test="diffdate &gt;=1 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(retention_day_1)/sum(add_user)*100,2),0.00),'%') rdOneRate,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_1)/sum(add_user)*100,2),0.00),'%*') rdOneRate,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdTwo")'>
			<choose>
				<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month') ">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_2)/sum(add_user)*100,2),0.00),'%') rdTwo,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_2)/sum(add_user)*100,2),0.00),'%*') rdTwo,
				</otherwise>
			</choose>		
		</if>
		<if test='customizes.contains("rdThree")'>
			<choose>
				<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month') ">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_3)/sum(add_user)*100,2),0.00),'%') rdThree,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_3)/sum(add_user)*100,2),0.00),'%*') rdThree,
				</otherwise>
			</choose>		
		</if>
		<if test='customizes.contains("rdFour")'>
			<choose>
				<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_4)/sum(add_user)*100,2),0.00),'%') rdFour,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_4)/sum(add_user)*100,2),0.00),'%*') rdFour,
				</otherwise>
			</choose>	
		</if>
		<if test='customizes.contains("rdFive")'>
			<choose>
				<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_5)/sum(add_user)*100,2),0.00),'%') rdFive,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_5)/sum(add_user)*100,2),0.00),'%*') rdFive,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdSix")'>
			<choose>
				<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_6)/sum(add_user)*100,2),0.00),'%') rdSix,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_6)/sum(add_user)*100,2),0.00),'%*') rdSix,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdSeven")'>
			<choose>
				<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
			     CONCAT(IFNULL(FORMAT(sum(retention_day_7)/sum(add_user)*100,2),0.00),'%') rdSeven,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_7)/sum(add_user)*100,2),0.00),'%*') rdSeven,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdFourteen")'>
			<choose>
				<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_14)/sum(add_user)*100,2),0.00),'%') rdFourteen,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_14)/sum(add_user)*100,2),0.00),'%*') rdFourteen,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdThrity")'>
			<choose>
				<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_30)/sum(add_user)*100,2),0.00),'%') rdThrity,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_30)/sum(add_user)*100,2),0.00),'%*') rdThrity,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdThritySix")'>
			<choose>
				<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_36)/sum(add_user)*100,2),0.00),'%') rdThritySix,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_36)/sum(add_user)*100,2),0.00),'%*') rdThritySix,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdFortyTwo")'>
			<choose>
				<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_42)/sum(add_user)*100,2),0.00),'%') rdFortyTwo,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_42)/sum(add_user)*100,2),0.00),'%*') rdFortyTwo,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdFortyEight")'>
			<choose>
				<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_48)/sum(add_user)*100,2),0.00),'%') rdFortyEight,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_48)/sum(add_user)*100,2),0.00),'%*') rdFortyEight,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdFiftyFour")'>
			<choose>
				<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_54)/sum(add_user)*100,2),0.00),'%') rdFiftyFour,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_54)/sum(add_user)*100,2),0.00),'%*') rdFiftyFour,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdSixty")'>
			<choose>
				<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_60)/sum(add_user)*100,2),0.00),'%') rdSixty,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_60)/sum(add_user)*100,2),0.00),'%*') rdSixty,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd8")'>
			<choose>
				<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_8)/sum(add_user)*100,2),0.00),'%') rd8,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_day_8)/sum(add_user)*100,2),0.00),'%*') rd8,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd9")'>
			<choose>
				<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_9)/sum(add_user)*100,2),0.00),'%') rd9,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_day_9)/sum(add_user)*100,2),0.00),'%*') rd9,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd10")'>
			<choose>
				<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_10)/sum(add_user)*100,2),0.00),'%') rd10,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_10)/sum(add_user)*100,2),0.00),'%*') rd10,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd11")'>
			<choose>
				<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_11)/sum(add_user)*100,2),0.00),'%') rd11,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_11)/sum(add_user)*100,2),0.00),'%*') rd11,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd12")'>
			<choose>
				<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_12)/sum(add_user)*100,2),0.00),'%') rd12,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_12)/sum(add_user)*100,2),0.00),'%*') rd12,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd13")'>
			<choose>
				<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_13)/sum(add_user)*100,2),0.00),'%') rd13,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_day_13)/sum(add_user)*100,2),0.00),'%*') rd13,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd15")'>
			<choose>
				<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_15)/sum(add_user)*100,2),0.00),'%') rd15,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_day_15)/sum(add_user)*100,2),0.00),'%*') rd15,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd16")'>
			<choose>
				<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_16)/sum(add_user)*100,2),0.00),'%') rd16,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_16)/sum(add_user)*100,2),0.00),'%*') rd16,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd17")'>
			<choose>
				<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_17)/sum(add_user)*100,2),0.00),'%') rd17,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_17)/sum(add_user)*100,2),0.00),'%*') rd17,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd18")'>
			<choose>
				<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_18)/sum(add_user)*100,2),0.00),'%') rd18,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_18)/sum(add_user)*100,2),0.00),'%*') rd18,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd19")'>
			<choose>
				<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_19)/sum(add_user)*100,2),0.00),'%') rd19,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_19)/sum(add_user)*100,2),0.00),'%*') rd19,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd20")'>
			<choose>
				<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_20)/sum(add_user)*100,2),0.00),'%') rd20,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_20)/sum(add_user)*100,2),0.00),'%*') rd20,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd21")'>
			<choose>
				<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_21)/sum(add_user)*100,2),0.00),'%') rd21,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_21)/sum(add_user)*100,2),0.00),'%*') rd21,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd22")'>
			<choose>
				<when test=" diffdate &gt;=22 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_22)/sum(add_user)*100,2),0.00),'%') rd22,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_22)/sum(add_user)*100,2),0.00),'%*') rd22,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd23")'>
			<choose>
				<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_23)/sum(add_user)*100,2),0.00),'%') rd23,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_23)/sum(add_user)*100,2),0.00),'%*') rd23,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd24")'>
			<choose>
				<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_24)/sum(add_user)*100,2),0.00),'%') rd24,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_24)/sum(add_user)*100,2),0.00),'%*') rd24,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd25")'>
			<choose>
				<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_25)/sum(add_user)*100,2),0.00),'%') rd25,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_25)/sum(add_user)*100,2),0.00),'%*') rd25,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd26")'>
			<choose>
				<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_26)/sum(add_user)*100,2),0.00),'%') rd26,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_26)/sum(add_user)*100,2),0.00),'%*') rd26,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd27")'>
			<choose>
				<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_27)/sum(add_user)*100,2),0.00),'%') rd27,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_day_27)/sum(add_user)*100,2),0.00),'%*') rd27,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd28")'>
			<choose>
				<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_28)/sum(add_user)*100,2),0.00),'%') rd28,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_28)/sum(add_user)*100,2),0.00),'%*') rd28,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd29")'>
			<choose>
				<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_29)/sum(add_user)*100,2),0.00),'%') rd29,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_29)/sum(add_user)*100,2),0.00),'%*') rd29,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd75")'>
			<choose>
				<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_75)/sum(add_user)*100,2),0.00),'%') rd75,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_day_75)/sum(add_user)*100,2),0.00),'%*') rd75,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd90")'>
			<choose>
				<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_90)/sum(add_user)*100,2),0.00),'%') rd90,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_90)/sum(add_user)*100,2),0.00),'%*') rd90,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd105")'>
			<choose>
				<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_105)/sum(add_user)*100,2),0.00),'%') rd105,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_105)/sum(add_user)*100,2),0.00),'%*') rd105,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd120")'>
			<choose>
				<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_120)/sum(add_user)*100,2),0.00),'%') rd120,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_120)/sum(add_user)*100,2),0.00),'%*') rd120,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd135")'>
			<choose>
				<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_135)/sum(add_user)*100,2),0.00),'%') rd135,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_135)/sum(add_user)*100,2),0.00),'%*') rd135,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd150")'>
			<choose>
				<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_150)/sum(add_user)*100,2),0.00),'%') rd150,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_150)/sum(add_user)*100,2),0.00),'%*') rd150,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd165")'>
			<choose>
				<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_165)/sum(add_user)*100,2),0.00),'%') rd165,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_165)/sum(add_user)*100,2),0.00),'%*') rd165,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd180")'>
			<choose>
				<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_180)/sum(add_user)*100,2),0.00),'%') rd180,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_180)/sum(add_user)*100,2),0.00),'%*') rd180,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd195")'>
			<choose>
				<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_195)/sum(add_user)*100,2),0.00),'%') rd195,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_195)/sum(add_user)*100,2),0.00),'%*') rd195,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd210")'>
			<choose>
				<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_210)/sum(add_user)*100,2),0.00),'%') rd210,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_210)/sum(add_user)*100,2),0.00),'%*') rd210,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd225")'>
			<choose>
				<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_225)/sum(add_user)*100,2),0.00),'%') rd225,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_225)/sum(add_user)*100,2),0.00),'%*') rd225,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd240")'>
			<choose>
				<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_240)/sum(add_user)*100,2),0.00),'%') rd240,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_240)/sum(add_user)*100,2),0.00),'%*') rd240,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd255")'>
			<choose>
				<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_255)/sum(add_user)*100,2),0.00),'%') rd255,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_255)/sum(add_user)*100,2),0.00),'%*') rd255,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd270")'>
			<choose>
				<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_270)/sum(add_user)*100,2),0.00),'%') rd270,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_270)/sum(add_user)*100,2),0.00),'%*') rd270,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd285")'>
			<choose>
				<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_285)/sum(add_user)*100,2),0.00),'%') rd285,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_285)/sum(add_user)*100,2),0.00),'%*') rd285,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd300")'>
			<choose>
				<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_300)/sum(add_user)*100,2),0.00),'%') rd300,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_300)/sum(add_user)*100,2),0.00),'%*') rd300,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd315")'>
			<choose>
				<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_315)/sum(add_user)*100,2),0.00),'%') rd315,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_315)/sum(add_user)*100,2),0.00),'%*') rd315,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd330")'>
			<choose>
				<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_330)/sum(add_user)*100,2),0.00),'%') rd330,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_330)/sum(add_user)*100,2),0.00),'%*') rd330,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd345")'>
			<choose>
				<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_345)/sum(add_user)*100,2),0.00),'%') rd345,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_345)/sum(add_user)*100,2),0.00),'%*') rd345,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd360")'>
			<choose>
				<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_day_360)/sum(add_user)*100,2),0.00),'%') rd360,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_day_360)/sum(add_user)*100,2),0.00),'%*') rd360,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd1")'>
			<choose>
				<when test=" diffdate &gt;=1 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_1)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd1,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_1)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd1,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd2")'>
			<choose>
				<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_2)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd2,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_2)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd2,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd3")'>
			<choose>
				<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_3)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd3,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_3)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd3,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd4")'>
			<choose>
				<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_4)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd4,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_4)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd4,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd5")'>
			<choose>
				<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_5)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd5,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_5)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd5,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd6")'>
			<choose>
				<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_6)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd6,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_6)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd6,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd7")'>
			<choose>
				<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_7)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd7,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_7)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd7,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd14")'>
			<choose>
				<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_14)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd14,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_14)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd14,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd30")'>
			<choose>
				<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_30)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd30,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_30)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd30,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd36")'>
			<choose>
				<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_36)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd36,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_36)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd36,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd42")'>
			<choose>
				<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_42)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd42,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_42)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd42,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd48")'>
			<choose>
				<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_48)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd48,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_48)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd48,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd54")'>
			<choose>
				<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_54)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd54,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_54)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd54,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd60")'>
			<choose>
				<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_60)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd60,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_60)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd60,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd8")'>
			<choose>
				<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_8)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd8,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_8)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd8,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd9")'>
			<choose>
				<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_9)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd9,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_9)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd9,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd10")'>
			<choose>
				<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_10)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd10,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_10)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd10,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd11")'>
			<choose>
				<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_11)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd11,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_11)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd11,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd12")'>
			<choose>
				<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_12)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd12,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_12)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd12,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd13")'>
			<choose>
				<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_13)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd13,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_13)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd13,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd15")'>
			<choose>
				<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_15)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd15,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_15)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd15,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd16")'>
			<choose>
				<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_16)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd16,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_16)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd16,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd17")'>
			<choose>
				<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_17)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd17,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_17)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd17,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd18")'>
			<choose>
				<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_18)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd18,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_18)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd18,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd19")'>
			<choose>
				<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_19)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd19,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_19)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd19,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd20")'>
			<choose>
				<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_20)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd20,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_20)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd20,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd21")'>
			<choose>
				<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_21)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd21,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_21)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd21,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd22")'>
			<choose>
				<when test=" diffdate &gt;=22 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_22)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd22,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_22)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd22,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd23")'>
			<choose>
				<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_23)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd23,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_23)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd23,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd24")'>
			<choose>
				<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_24)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd24,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_24)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd24,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd25")'>
			<choose>
				<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_25)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd25,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_25)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd25,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd26")'>
			<choose>
				<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_26)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd26,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_26)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd26,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd27")'>
			<choose>
				<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_27)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd27,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_27)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd27,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd28")'>
			<choose>
				<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_28)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd28,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_28)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd28,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd29")'>
			<choose>
				<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_29)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd29,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_29)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd29,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd75")'>
			<choose>
				<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_75)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd75,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_75)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd75,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd90")'>
			<choose>
				<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_90)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd90,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_90)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd90,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd105")'>
			<choose>
				<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_105)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd105,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_105)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd105,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd120")'>
			<choose>
				<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_120)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd120,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_120)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd120,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd135")'>
			<choose>
				<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_135)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd135,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_135)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd135,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd150")'>
			<choose>
				<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_150)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd150,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_150)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd150,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd165")'>
			<choose>
				<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_165)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd165,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_165)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd165,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd180")'>
			<choose>
				<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_180)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd180,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_180)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd180,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd195")'>
			<choose>
				<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_195)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd195,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_195)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd195,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd210")'>
			<choose>
				<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_210)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd210,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_210)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd210,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd225")'>
			<choose>
				<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_225)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd225,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_225)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd225,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd240")'>
			<choose>
				<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_240)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd240,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_240)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd240,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd255")'>
			<choose>
				<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_255)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd255,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_255)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd255,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd270")'>
			<choose>
				<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_270)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd270,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_270)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd270,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd285")'>
			<choose>
				<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_285)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd285,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_285)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd285,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd300")'>
			<choose>
				<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_300)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd300,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_300)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd300,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd315")'>
			<choose>
				<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_315)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd315,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_315)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd315,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd330")'>
			<choose>
				<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_330)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd330,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_330)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd330,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd345")'>
			<choose>
				<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_345)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd345,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_345)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd345,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd360")'>
			<choose>
				<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_360)/sum(add_purchase_users)*100,2),0.00),'%') ipaRd360,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(add_purchase_retention_360)/sum(add_purchase_users)*100,2),0.00),'%*') ipaRd360,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaIncome")'>
			round(sum(add_purchase_revenue_1)/100,2)  ipaIncome,
		</if>
		<if test='customizes.contains("realizeIncome")'>
			round(sum(add_revenue_1)/100,2) realizeIncome,
		</if>
		<if test='customizes.contains("firstDayIncome")'>
			round(sum(add_purchase_revenue_1)/100+sum(add_revenue_1)/100,2) firstDayIncome,
		</if>
		<if test='customizes.contains("realizeRoi")'>
			CONCAT(IFNULL(FORMAT(sum(add_revenue_1)/sum(rebateCost)*100,2),0.00),'%') realizeRoi,
		</if>
		<if test='customizes.contains("ipaRoi")'>
			CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_1)/sum(rebateCost)*100,2),0.00),'%') ipaRoi,
		</if>
		<if test='customizes.contains("firstDayRoi")'>
			CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_1)+sum(add_revenue_1))/sum(rebateCost)*100,2),0.00),'%') firstDayRoi,
		</if>
		<if test='customizes.contains("adTotalRevenue")'>
			round(sum(active_revenue)/100,2)  adTotalRevenue,
		</if>
		<if test='customizes.contains("ipaTotalRevenue")'>
			round(sum(active_purchase_revenue)/100,2) ipaTotalRevenue,
		</if>
		<if test='customizes.contains("activeRoi")'>
			CONCAT(IFNULL(FORMAT((sum(active_purchase_revenue)+sum(active_revenue))/sum(rebateCost)*100,2),0.00),'%') activeRoi,
		</if>
		<if test='customizes.contains("activeIpaRoi")'>
			CONCAT(IFNULL(FORMAT(sum(active_purchase_revenue)/sum(rebateCost)*100,2),0.00),'%') activeIpaRoi,
		</if>
		<if test='customizes.contains("activeAdRoi")'>
			CONCAT(IFNULL(FORMAT(sum(active_revenue)/sum(rebateCost)*100,2),0.00),'%') activeAdRoi,
		</if>
		<if test='group.contains("campaign_id") or group.contains("campaignName")'>
			sum(rebateCost)/100/sum(`convert`)/sum(bid),
		</if>
		<choose>
            <when test="revenueSource ==1">
                <if test='customizes.contains("totalIncome")'>
					round(sum(active_revenue)/100,2) totalIncome,
				</if>
				<if test='customizes.contains("addArpu")'>
					round(sum(add_revenue_1)/100/sum(add_user),2) addArpu,
				</if>
				<if test='customizes.contains("dauArpu")'>
					round(sum(active_revenue)/100/sum(active_user),2) dauArpu,
				</if>
				<if test='customizes.contains("addUserLtv0")'>
					round(sum(add_revenue_1)/100,2) addUserLtv0,
				</if>
				<if test='customizes.contains("ltv1")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_2)/100/sum(add_user),2) ltv1,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_2)/100/sum(add_user),2),'*') ltv1,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv2")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_3)/100/sum(add_user),2) ltv2,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_3)/100/sum(add_user),2),'*') ltv2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv3")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_4)/100/sum(add_user),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_4)/100/sum(add_user),2),'*') ltv3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv4")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_5)/100/sum(add_user),2) ltv4,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_5)/100/sum(add_user),2),'*') ltv4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv5")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_6)/100/sum(add_user),2) ltv5,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_6)/100/sum(add_user),2),'*') ltv5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv6")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_7)/100/sum(add_user),2) ltv6,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_7)/100/sum(add_user),2) ltv6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_8)/100/sum(add_user),2) ltv8,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_8)/100/sum(add_user),2),'*') ltv8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_9)/100/sum(add_user),2) ltv9,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_9)/100/sum(add_user),2),'*')  ltv9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_10)/100/sum(add_user),2) ltv10,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_10)/100/sum(add_user),2),'*') ltv10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_11)/100/sum(add_user),2) ltv11,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_11)/100/sum(add_user),2),'*') ltv11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_12)/100/sum(add_user),2) ltv12,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_12)/100/sum(add_user),2),'*') ltv12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_13)/100/sum(add_user),2) ltv13,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_13)/100/sum(add_user),2),'*') ltv13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_14)/100/sum(add_user),2) ltv14,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_14)/100/sum(add_user),2),'*') ltv14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_15)/100/sum(add_user),2) ltv15,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_15)/100/sum(add_user),2),'*') ltv15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_16)/100/sum(add_user),2) ltv16,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_16)/100/sum(add_user),2),'*') ltv16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_17)/100/sum(add_user),2) ltv17,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_17)/100/sum(add_user),2),'*') ltv17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_18)/100/sum(add_user),2) ltv18,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_18)/100/sum(add_user),2),'*') ltv18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_19)/100/sum(add_user),2) ltv19,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_19)/100/sum(add_user),2),'*') ltv19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_20)/100/sum(add_user),2) ltv20,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_20)/100/sum(add_user),2),'*') ltv20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_21)/100/sum(add_user),2) ltv21,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_21)/100/sum(add_user),2),'*') ltv21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv22")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_4)/100/sum(add_user),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_4)/100/sum(add_user),2),'*') ltv3,
						</otherwise>
					</choose>
					FORMAT(sum(add_revenue_22)/100/sum(add_user),2) ltv22,
				</if>
				<if test='customizes.contains("ltv23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_23)/100/sum(add_user),2) ltv23,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_23)/100/sum(add_user),2),'*') ltv23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_24)/100/sum(add_user),2) ltv24,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_24)/100/sum(add_user),2),'*') ltv24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_25)/100/sum(add_user),2) ltv25,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_25)/100/sum(add_user),2),'*') ltv25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_26)/100/sum(add_user),2) ltv26,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_26)/100/sum(add_user),2),'*') ltv26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_27)/100/sum(add_user),2) ltv27,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_27)/100/sum(add_user),2),'*') ltv27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_28)/100/sum(add_user),2) ltv28,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_28)/100/sum(add_user),2),'*') ltv28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_29)/100/sum(add_user),2) ltv29,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_29)/100/sum(add_user),2),'*') ltv29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_30)/100/sum(add_user),2) ltv30,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_30)/100/sum(add_user),2),'*') ltv30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_36)/100/sum(add_user),2) ltv36,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_36)/100/sum(add_user),2),'*') ltv36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_42)/100/sum(add_user),2) ltv42,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_42)/100/sum(add_user),2),'*') ltv42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_48)/100/sum(add_user),2) ltv48,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_48)/100/sum(add_user),2),'*') ltv48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_54)/100/sum(add_user),2) ltv54,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_54)/100/sum(add_user),2),'*') ltv54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_60)/100/sum(add_user),2) ltv60,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_60)/100/sum(add_user),2),'*') ltv60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_75)/100/sum(add_user),2) ltv75,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_75)/100/sum(add_user),2),'*') ltv75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_90)/100/sum(add_user),2) ltv90,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_90)/100/sum(add_user),2),'*') ltv90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_105)/100/sum(add_user),2) ltv105,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_105)/100/sum(add_user),2),'*') ltv105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_120)/100/sum(add_user),2) ltv120,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_120)/100/sum(add_user),2),'*') ltv120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_135)/100/sum(add_user),2) ltv135,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_135)/100/sum(add_user),2),'*') ltv135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_150)/100/sum(add_user),2) ltv150,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_150)/100/sum(add_user),2),'*') ltv150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_165)/100/sum(add_user),2) ltv165,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_165)/100/sum(add_user),2),'*') ltv165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_180)/100/sum(add_user),2) ltv180,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_180)/100/sum(add_user),2),'*') ltv180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_195)/100/sum(add_user),2) ltv195,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_195)/100/sum(add_user),2),'*') ltv195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_210)/100/sum(add_user),2) ltv210,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_210)/100/sum(add_user),2),'*') ltv210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_225)/100/sum(add_user),2) ltv225,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_225)/100/sum(add_user),2),'*') ltv225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_240)/100/sum(add_user),2) ltv240,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_240)/100/sum(add_user),2),'*') ltv240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_255)/100/sum(add_user),2) ltv255,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_255)/100/sum(add_user),2),'*') ltv255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_270)/100/sum(add_user),2) ltv270,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_270)/100/sum(add_user),2),'*') ltv270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_285)/100/sum(add_user),2) ltv285,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_285)/100/sum(add_user),2),'*') ltv285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_300)/100/sum(add_user),2) ltv300,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_300)/100/sum(add_user),2),'*') ltv300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_315)/100/sum(add_user),2) ltv315,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_315)/100/sum(add_user),2),'*') ltv315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_330)/100/sum(add_user),2) ltv330,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_330)/100/sum(add_user),2),'*') ltv330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_345)/100/sum(add_user),2) ltv345,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_345)/100/sum(add_user),2),'*') ltv345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_revenue_360)/100/sum(add_user),2) ltv360,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_revenue_360)/100/sum(add_user),2),'*') ltv360,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("aRoi")'>
					CONCAT(IFNULL(FORMAT(sum(add_revenue_1)/sum(rebateCost)*100,2),0.00),'%') aRoi,
				</if>
				<if test='customizes.contains("roi2")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_2)/sum(rebateCost)*100,2),0.00),'%') roi2,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_2)/sum(rebateCost)*100,2),0.00),'%*') roi2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi3")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_3)/sum(rebateCost)*100,2),0.00),'%') roi3,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_3)/sum(rebateCost)*100,2),0.00),'%*') roi3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi4")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_4)/sum(rebateCost)*100,2),0.00),'%') roi4,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_4)/sum(rebateCost)*100,2),0.00),'%*') roi4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi5")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_5)/sum(rebateCost)*100,2),0.00),'%') roi5,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_5)/sum(rebateCost)*100,2),0.00),'%*') roi5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi6")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_7)/sum(rebateCost)*100,2),0.00),'%') roi6,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_7)/sum(rebateCost)*100,2),0.00),'%*') roi6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi7")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_7)/sum(rebateCost)*100,2),0.00),'%') roi7,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_7)/sum(rebateCost)*100,2),0.00),'%*') roi7,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_8)/sum(rebateCost)*100,2),0.00),'%') roi8,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_8)/sum(rebateCost)*100,2),0.00),'%*') roi8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_9)/sum(rebateCost)*100,2),0.00),'%') roi9,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_9)/sum(rebateCost)*100,2),0.00),'%*') roi9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_10)/sum(rebateCost)*100,2),0.00),'%') roi10,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_10)/sum(rebateCost)*100,2),0.00),'%*') roi10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_11)/sum(rebateCost)*100,2),0.00),'%') roi11,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_11)/sum(rebateCost)*100,2),0.00),'%*') roi11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_12)/sum(rebateCost)*100,2),0.00),'%') roi12,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_12)/sum(rebateCost)*100,2),0.00),'%*') roi12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_13)/sum(rebateCost)*100,2),0.00),'%') roi13,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_13)/sum(rebateCost)*100,2),0.00),'%*') roi13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_14)/sum(rebateCost)*100,2),0.00),'%') roi14,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_14)/sum(rebateCost)*100,2),0.00),'%*') roi14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_15)/sum(rebateCost)*100,2),0.00),'%') roi15,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_15)/sum(rebateCost)*100,2),0.00),'%*') roi15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_16)/sum(rebateCost)*100,2),0.00),'%') roi16,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_16)/sum(rebateCost)*100,2),0.00),'%*') roi16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_17)/sum(rebateCost)*100,2),0.00),'%') roi17,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_17)/sum(rebateCost)*100,2),0.00),'%*') roi17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_18)/sum(rebateCost)*100,2),0.00),'%') roi18,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_18)/sum(rebateCost)*100,2),0.00),'%*') roi18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_19)/sum(rebateCost)*100,2),0.00),'%') roi19,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_19)/sum(rebateCost)*100,2),0.00),'%*') roi19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_20)/sum(rebateCost)*100,2),0.00),'%') roi20,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_20)/sum(rebateCost)*100,2),0.00),'%*') roi20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_21)/sum(rebateCost)*100,2),0.00),'%') roi21,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_21)/sum(rebateCost)*100,2),0.00),'%') roi21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi22")'>
					<choose>
						<when test=" diffdate &gt;=22 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_22)/sum(rebateCost)*100,2),0.00),'%') roi22,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_22)/sum(rebateCost)*100,2),0.00),'%*') roi22,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_23)/sum(rebateCost)*100,2),0.00),'%') roi23,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_23)/sum(rebateCost)*100,2),0.00),'%*') roi23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_24)/sum(rebateCost)*100,2),0.00),'%') roi24,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_24)/sum(rebateCost)*100,2),0.00),'%*') roi24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_25)/sum(rebateCost)*100,2),0.00),'%') roi25,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_25)/sum(rebateCost)*100,2),0.00),'%*') roi25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_26)/sum(rebateCost)*100,2),0.00),'%') roi26,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_26)/sum(rebateCost)*100,2),0.00),'%*') roi26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_27)/sum(rebateCost)*100,2),0.00),'%') roi27,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_27)/sum(rebateCost)*100,2),0.00),'%*') roi27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_28)/sum(rebateCost)*100,2),0.00),'%') roi28,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_28)/sum(rebateCost)*100,2),0.00),'%*') roi28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_29)/sum(rebateCost)*100,2),0.00),'%') roi29,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_29)/sum(rebateCost)*100,2),0.00),'%*') roi29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_30)/sum(rebateCost)*100,2),0.00),'%') roi30,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_30)/sum(rebateCost)*100,2),0.00),'%*') roi30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_36)/sum(rebateCost)*100,2),0.00),'%') roi36,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_36)/sum(rebateCost)*100,2),0.00),'%*') roi36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_42)/sum(rebateCost)*100,2),0.00),'%') roi42,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_42)/sum(rebateCost)*100,2),0.00),'%*') roi42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_48)/sum(rebateCost)*100,2),0.00),'%') roi48,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_48)/sum(rebateCost)*100,2),0.00),'%*') roi48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_54)/sum(rebateCost)*100,2),0.00),'%') roi54,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_54)/sum(rebateCost)*100,2),0.00),'%*') roi54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_60)/sum(rebateCost)*100,2),0.00),'%') roi60,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_60)/sum(rebateCost)*100,2),0.00),'%*') roi60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_75)/sum(rebateCost)*100,2),0.00),'%') roi75,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_75)/sum(rebateCost)*100,2),0.00),'%*') roi75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_revenue_90)/sum(rebateCost)*100,2),0.00),'%') roi90,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_revenue_90)/sum(rebateCost)*100,2),0.00),'%*') roi90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_105)/sum(rebateCost)*100,2),0.00),'%') roi105,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_105)/sum(rebateCost)*100,2),0.00),'%*') roi105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_120)/sum(rebateCost)*100,2),0.00),'%') roi120,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_120)/sum(rebateCost)*100,2),0.00),'%*') roi120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_135)/sum(rebateCost)*100,2),0.00),'%') roi135,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_135)/sum(rebateCost)*100,2),0.00),'%*') roi135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_150)/sum(rebateCost)*100,2),0.00),'%') roi150,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_150)/sum(rebateCost)*100,2),0.00),'%*') roi150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_165)/sum(rebateCost)*100,2),0.00),'%') roi165,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_165)/sum(rebateCost)*100,2),0.00),'%*') roi165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_180)/sum(rebateCost)*100,2),0.00),'%') roi180,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_180)/sum(rebateCost)*100,2),0.00),'%*') roi180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_195)/sum(rebateCost)*100,2),0.00),'%') roi195,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_195)/sum(rebateCost)*100,2),0.00),'%*') roi195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_210)/sum(rebateCost)*100,2),0.00),'%') roi210,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_210)/sum(rebateCost)*100,2),0.00),'%*') roi210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_225)/sum(rebateCost)*100,2),0.00),'%') roi225,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_225)/sum(rebateCost)*100,2),0.00),'%*') roi225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_240)/sum(rebateCost)*100,2),0.00),'%') roi240,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_240)/sum(rebateCost)*100,2),0.00),'%*') roi240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_255)/sum(rebateCost)*100,2),0.00),'%') roi255,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_255)/sum(rebateCost)*100,2),0.00),'%*') roi255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_270)/sum(rebateCost)*100,2),0.00),'%') roi270,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_270)/sum(rebateCost)*100,2),0.00),'%*') roi270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_285)/sum(rebateCost)*100,2),0.00),'%') roi285,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_285)/sum(rebateCost)*100,2),0.00),'%*') roi285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_300)/sum(rebateCost)*100,2),0.00),'%') roi300,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_300)/sum(rebateCost)*100,2),0.00),'%*') roi300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_315)/sum(rebateCost)*100,2),0.00),'%') roi315,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_315)/sum(rebateCost)*100,2),0.00),'%*') roi315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_330)/sum(rebateCost)*100,2),0.00),'%') roi330,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_330)/sum(rebateCost)*100,2),0.00),'%*') roi330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_345)/sum(rebateCost)*100,2),0.00),'%') roi345,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_345)/sum(rebateCost)*100,2),0.00),'%*') roi345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_360)/sum(rebateCost)*100,2),0.00),'%') roi360,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_revenue_360)/sum(rebateCost)*100,2),0.00),'%*') roi360,
						</otherwise>
					</choose>
				</if>
            </when>
            <when test="revenueSource ==2">
            	<if test='customizes.contains("addUserLtv0")'>
					round(sum(add_purchase_revenue_1)/100,2) addUserLtv0,
				</if>
                 <if test='customizes.contains("totalIncome")'>
					round(sum(active_purchase_revenue)/100,2) as totalIncome,
				</if>
				<if test='customizes.contains("addArpu")'>
					round(sum(add_purchase_revenue_1)/100/sum(add_user),2) addArpu,
				</if>
				<if test='customizes.contains("dauArpu")'>
					round(sum(active_purchase_revenue)/100/sum(active_user),2) dauArpu,
				</if>
				<if test='customizes.contains("ltv1")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_2)/100/sum(add_user),2) ltv1,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_2)/100/sum(add_user),2),'*') ltv1,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv2")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_3)/100/sum(add_user),2) ltv2,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_3)/100/sum(add_user),2),'*') ltv2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv3")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_4)/100/sum(add_user),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_4)/100/sum(add_user),2),'*') ltv3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv4")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_5)/100/sum(add_user),2) ltv4,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_5)/100/sum(add_user),2),'*') ltv4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv5")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_6)/100/sum(add_user),2) ltv5,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_6)/100/sum(add_user),2),'*') ltv5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv6")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_7)/100/sum(add_user),2) ltv6,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_7)/100/sum(add_user),2) ltv6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_8)/100/sum(add_user),2) ltv8,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_8)/100/sum(add_user),2),'*') ltv8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_9)/100/sum(add_user),2) ltv9,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_9)/100/sum(add_user),2),'*')  ltv9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_10)/100/sum(add_user),2) ltv10,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_10)/100/sum(add_user),2),'*') ltv10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_11)/100/sum(add_user),2) ltv11,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_11)/100/sum(add_user),2),'*') ltv11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_12)/100/sum(add_user),2) ltv12,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_12)/100/sum(add_user),2),'*') ltv12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_13)/100/sum(add_user),2) ltv13,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_13)/100/sum(add_user),2),'*') ltv13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_14)/100/sum(add_user),2) ltv14,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_14)/100/sum(add_user),2),'*') ltv14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_15)/100/sum(add_user),2) ltv15,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_15)/100/sum(add_user),2),'*') ltv15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_16)/100/sum(add_user),2) ltv16,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_16)/100/sum(add_user),2),'*') ltv16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_17)/100/sum(add_user),2) ltv17,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_17)/100/sum(add_user),2),'*') ltv17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_18)/100/sum(add_user),2) ltv18,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_18)/100/sum(add_user),2),'*') ltv18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_19)/100/sum(add_user),2) ltv19,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_19)/100/sum(add_user),2),'*') ltv19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_20)/100/sum(add_user),2) ltv20,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_20)/100/sum(add_user),2),'*') ltv20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_21)/100/sum(add_user),2) ltv21,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_21)/100/sum(add_user),2),'*') ltv21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv22")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_4)/100/sum(add_user),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_4)/100/sum(add_user),2),'*') ltv3,
						</otherwise>
					</choose>
					FORMAT(sum(add_purchase_revenue_22)/100/sum(add_user),2) ltv22,
				</if>
				<if test='customizes.contains("ltv23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_23)/100/sum(add_user),2) ltv23,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_23)/100/sum(add_user),2),'*') ltv23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_24)/100/sum(add_user),2) ltv24,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_24)/100/sum(add_user),2),'*') ltv24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_25)/100/sum(add_user),2) ltv25,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_25)/100/sum(add_user),2),'*') ltv25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_26)/100/sum(add_user),2) ltv26,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_26)/100/sum(add_user),2),'*') ltv26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_27)/100/sum(add_user),2) ltv27,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_27)/100/sum(add_user),2),'*') ltv27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_28)/100/sum(add_user),2) ltv28,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_28)/100/sum(add_user),2),'*') ltv28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_29)/100/sum(add_user),2) ltv29,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_29)/100/sum(add_user),2),'*') ltv29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_30)/100/sum(add_user),2) ltv30,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_30)/100/sum(add_user),2),'*') ltv30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_36)/100/sum(add_user),2) ltv36,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_36)/100/sum(add_user),2),'*') ltv36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_42)/100/sum(add_user),2) ltv42,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_42)/100/sum(add_user),2),'*') ltv42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_48)/100/sum(add_user),2) ltv48,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_48)/100/sum(add_user),2),'*') ltv48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_54)/100/sum(add_user),2) ltv54,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_54)/100/sum(add_user),2),'*') ltv54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_60)/100/sum(add_user),2) ltv60,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_60)/100/sum(add_user),2),'*') ltv60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_75)/100/sum(add_user),2) ltv75,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_75)/100/sum(add_user),2),'*') ltv75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_90)/100/sum(add_user),2) ltv90,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_90)/100/sum(add_user),2),'*') ltv90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_105)/100/sum(add_user),2) ltv105,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_105)/100/sum(add_user),2),'*') ltv105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_120)/100/sum(add_user),2) ltv120,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_120)/100/sum(add_user),2),'*') ltv120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_135)/100/sum(add_user),2) ltv135,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_135)/100/sum(add_user),2),'*') ltv135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_150)/100/sum(add_user),2) ltv150,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_150)/100/sum(add_user),2),'*') ltv150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_165)/100/sum(add_user),2) ltv165,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_165)/100/sum(add_user),2),'*') ltv165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_180)/100/sum(add_user),2) ltv180,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_180)/100/sum(add_user),2),'*') ltv180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_195)/100/sum(add_user),2) ltv195,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_195)/100/sum(add_user),2),'*') ltv195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_210)/100/sum(add_user),2) ltv210,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_210)/100/sum(add_user),2),'*') ltv210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_225)/100/sum(add_user),2) ltv225,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_225)/100/sum(add_user),2),'*') ltv225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_240)/100/sum(add_user),2) ltv240,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_240)/100/sum(add_user),2),'*') ltv240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_255)/100/sum(add_user),2) ltv255,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_255)/100/sum(add_user),2),'*') ltv255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_270)/100/sum(add_user),2) ltv270,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_270)/100/sum(add_user),2),'*') ltv270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_285)/100/sum(add_user),2) ltv285,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_285)/100/sum(add_user),2),'*') ltv285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_300)/100/sum(add_user),2) ltv300,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_300)/100/sum(add_user),2),'*') ltv300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_315)/100/sum(add_user),2) ltv315,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_315)/100/sum(add_user),2),'*') ltv315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_330)/100/sum(add_user),2) ltv330,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_330)/100/sum(add_user),2),'*') ltv330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_345)/100/sum(add_user),2) ltv345,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_345)/100/sum(add_user),2),'*') ltv345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(add_purchase_revenue_360)/100/sum(add_user),2) ltv360,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(add_purchase_revenue_360)/100/sum(add_user),2),'*') ltv360,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("aRoi")'>
					CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_1)/sum(rebateCost)*100,2),0.00),'%') aRoi,
				</if>
				<if test='customizes.contains("roi2")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_2)/sum(rebateCost)*100,2),0.00),'%') roi2,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_2)/sum(rebateCost)*100,2),0.00),'%*') roi2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi3")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_3)/sum(rebateCost)*100,2),0.00),'%') roi3,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_3)/sum(rebateCost)*100,2),0.00),'%*') roi3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi4")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_4)/sum(rebateCost)*100,2),0.00),'%') roi4,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_4)/sum(rebateCost)*100,2),0.00),'%*') roi4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi5")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_5)/sum(rebateCost)*100,2),0.00),'%') roi5,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_5)/sum(rebateCost)*100,2),0.00),'%*') roi5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi6")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_7)/sum(rebateCost)*100,2),0.00),'%') roi6,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_7)/sum(rebateCost)*100,2),0.00),'%*') roi6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi7")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_7)/sum(rebateCost)*100,2),0.00),'%') roi7,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_7)/sum(rebateCost)*100,2),0.00),'%*') roi7,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_8)/sum(rebateCost)*100,2),0.00),'%') roi8,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_8)/sum(rebateCost)*100,2),0.00),'%*') roi8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_9)/sum(rebateCost)*100,2),0.00),'%') roi9,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_9)/sum(rebateCost)*100,2),0.00),'%*') roi9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_10)/sum(rebateCost)*100,2),0.00),'%') roi10,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_10)/sum(rebateCost)*100,2),0.00),'%*') roi10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_11)/sum(rebateCost)*100,2),0.00),'%') roi11,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_11)/sum(rebateCost)*100,2),0.00),'%*') roi11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_12)/sum(rebateCost)*100,2),0.00),'%') roi12,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_12)/sum(rebateCost)*100,2),0.00),'%*') roi12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_13)/sum(rebateCost)*100,2),0.00),'%') roi13,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_13)/sum(rebateCost)*100,2),0.00),'%*') roi13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_14)/sum(rebateCost)*100,2),0.00),'%') roi14,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_14)/sum(rebateCost)*100,2),0.00),'%*') roi14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_15)/sum(rebateCost)*100,2),0.00),'%') roi15,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_15)/sum(rebateCost)*100,2),0.00),'%*') roi15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_16)/sum(rebateCost)*100,2),0.00),'%') roi16,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_16)/sum(rebateCost)*100,2),0.00),'%*') roi16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_17)/sum(rebateCost)*100,2),0.00),'%') roi17,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_17)/sum(rebateCost)*100,2),0.00),'%*') roi17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_18)/sum(rebateCost)*100,2),0.00),'%') roi18,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_18)/sum(rebateCost)*100,2),0.00),'%*') roi18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_19)/sum(rebateCost)*100,2),0.00),'%') roi19,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_19)/sum(rebateCost)*100,2),0.00),'%*') roi19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_20)/sum(rebateCost)*100,2),0.00),'%') roi20,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_20)/sum(rebateCost)*100,2),0.00),'%*') roi20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_21)/sum(rebateCost)*100,2),0.00),'%') roi21,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_21)/sum(rebateCost)*100,2),0.00),'%') roi21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi22")'>
					<choose>
						<when test=" diffdate &gt;=22 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_22)/sum(rebateCost)*100,2),0.00),'%') roi22,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_22)/sum(rebateCost)*100,2),0.00),'%*') roi22,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_23)/sum(rebateCost)*100,2),0.00),'%') roi23,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_23)/sum(rebateCost)*100,2),0.00),'%*') roi23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_24)/sum(rebateCost)*100,2),0.00),'%') roi24,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_24)/sum(rebateCost)*100,2),0.00),'%*') roi24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_25)/sum(rebateCost)*100,2),0.00),'%') roi25,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_25)/sum(rebateCost)*100,2),0.00),'%*') roi25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_26)/sum(rebateCost)*100,2),0.00),'%') roi26,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_26)/sum(rebateCost)*100,2),0.00),'%*') roi26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_27)/sum(rebateCost)*100,2),0.00),'%') roi27,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_27)/sum(rebateCost)*100,2),0.00),'%*') roi27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_28)/sum(rebateCost)*100,2),0.00),'%') roi28,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_28)/sum(rebateCost)*100,2),0.00),'%*') roi28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_29)/sum(rebateCost)*100,2),0.00),'%') roi29,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_29)/sum(rebateCost)*100,2),0.00),'%*') roi29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_30)/sum(rebateCost)*100,2),0.00),'%') roi30,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_30)/sum(rebateCost)*100,2),0.00),'%*') roi30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_36)/sum(rebateCost)*100,2),0.00),'%') roi36,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_36)/sum(rebateCost)*100,2),0.00),'%*') roi36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_42)/sum(rebateCost)*100,2),0.00),'%') roi42,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_42)/sum(rebateCost)*100,2),0.00),'%*') roi42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_48)/sum(rebateCost)*100,2),0.00),'%') roi48,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_48)/sum(rebateCost)*100,2),0.00),'%*') roi48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_54)/sum(rebateCost)*100,2),0.00),'%') roi54,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_54)/sum(rebateCost)*100,2),0.00),'%*') roi54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_60)/sum(rebateCost)*100,2),0.00),'%') roi60,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_60)/sum(rebateCost)*100,2),0.00),'%*') roi60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_75)/sum(rebateCost)*100,2),0.00),'%') roi75,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_75)/sum(rebateCost)*100,2),0.00),'%*') roi75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_90)/sum(rebateCost)*100,2),0.00),'%') roi90,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_90)/sum(rebateCost)*100,2),0.00),'%*') roi90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_105)/sum(rebateCost)*100,2),0.00),'%') roi105,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_105)/sum(rebateCost)*100,2),0.00),'%*') roi105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_120)/sum(rebateCost)*100,2),0.00),'%') roi120,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_120)/sum(rebateCost)*100,2),0.00),'%*') roi120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_135)/sum(rebateCost)*100,2),0.00),'%') roi135,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_135)/sum(rebateCost)*100,2),0.00),'%*') roi135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_150)/sum(rebateCost)*100,2),0.00),'%') roi150,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_150)/sum(rebateCost)*100,2),0.00),'%*') roi150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_165)/sum(rebateCost)*100,2),0.00),'%') roi165,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_165)/sum(rebateCost)*100,2),0.00),'%*') roi165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_180)/sum(rebateCost)*100,2),0.00),'%') roi180,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_180)/sum(rebateCost)*100,2),0.00),'%*') roi180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_195)/sum(rebateCost)*100,2),0.00),'%') roi195,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_195)/sum(rebateCost)*100,2),0.00),'%*') roi195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_210)/sum(rebateCost)*100,2),0.00),'%') roi210,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_210)/sum(rebateCost)*100,2),0.00),'%*') roi210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_225)/sum(rebateCost)*100,2),0.00),'%') roi225,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_225)/sum(rebateCost)*100,2),0.00),'%*') roi225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_240)/sum(rebateCost)*100,2),0.00),'%') roi240,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_240)/sum(rebateCost)*100,2),0.00),'%*') roi240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_255)/sum(rebateCost)*100,2),0.00),'%') roi255,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_255)/sum(rebateCost)*100,2),0.00),'%*') roi255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_270)/sum(rebateCost)*100,2),0.00),'%') roi270,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_270)/sum(rebateCost)*100,2),0.00),'%*') roi270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_285)/sum(rebateCost)*100,2),0.00),'%') roi285,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_285)/sum(rebateCost)*100,2),0.00),'%*') roi285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_300)/sum(rebateCost)*100,2),0.00),'%') roi300,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_300)/sum(rebateCost)*100,2),0.00),'%*') roi300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_315)/sum(rebateCost)*100,2),0.00),'%') roi315,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_315)/sum(rebateCost)*100,2),0.00),'%*') roi315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_330)/sum(rebateCost)*100,2),0.00),'%') roi330,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_330)/sum(rebateCost)*100,2),0.00),'%*') roi330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_345)/sum(rebateCost)*100,2),0.00),'%') roi345,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_345)/sum(rebateCost)*100,2),0.00),'%*') roi345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_360)/sum(rebateCost)*100,2),0.00),'%') roi360,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(add_purchase_revenue_360)/sum(rebateCost)*100,2),0.00),'%*') roi360,
						</otherwise>
					</choose>
				</if>
            </when>
            <otherwise>
                <if test='customizes.contains("addUserLtv0")'>
					round(sum(add_purchase_revenue_1)/100+sum(add_revenue_1)/100,2) addUserLtv0,
				</if>
                 <if test='customizes.contains("totalIncome")'>
					round(sum(active_revenue)/100+sum(active_purchase_revenue)/100,2) totalIncome,
				</if>
				<if test='customizes.contains("addArpu")'>
					round((sum(add_purchase_revenue_1)/100+sum(add_revenue_1)/100)/sum(add_user),2) addArpu,
				</if>
				<if test='customizes.contains("dauArpu")'>
					round((sum(active_purchase_revenue)/100+sum(active_revenue)/100)/sum(active_user),2) dauArpu,
				</if>
				<if test='customizes.contains("ltv1")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_2)/100+sum(add_revenue_2)/100)/sum(add_user),2) ltv1,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_2)/100+sum(add_revenue_2)/100)/sum(add_user),2),'*') ltv1,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv2")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_3)/100+sum(add_revenue_3)/100)/sum(add_user),2) ltv2,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_3)/100+sum(add_revenue_3)/100)/sum(add_user),2),'*') ltv2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv3")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_4)/100+sum(add_revenue_4)/100)/sum(add_user),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_4)/100+sum(add_revenue_4)/100)/sum(add_user),2),'*') ltv3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv4")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_5)/100+sum(add_revenue_5)/100)/sum(add_user),2) ltv4,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_5)/100+sum(add_revenue_5)/100)/sum(add_user),2),'*') ltv4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv5")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_6)/100+sum(add_revenue_6)/100)/sum(add_user),2) ltv5,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_6)/100+sum(add_revenue_6)/100)/sum(add_user),2) ,'*') ltv5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv6")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_7)/100+sum(add_revenue_7)/100)/sum(add_user),2) ltv6,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_7)/100+sum(add_revenue_7)/100)/sum(add_user),2),'*') ltv6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_8)/100+sum(add_revenue_8)/100)/sum(add_user),2) ltv8,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_8)/100+sum(add_revenue_8)/100)/sum(add_user),2),'*') ltv8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_9)/100+sum(add_revenue_9)/100)/sum(add_user),2) ltv9,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_9)/100+sum(add_revenue_9)/100)/sum(add_user),2),'*') ltv9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_10)/100+sum(add_revenue_10)/100)/sum(add_user),2) ltv10,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_10)/100+sum(add_revenue_10)/100)/sum(add_user),2),'*') ltv10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_11)/100+sum(add_revenue_11)/100)/sum(add_user),2) ltv11,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_11)/100+sum(add_revenue_11)/100)/sum(add_user),2),'*') ltv11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_12)/100+sum(add_revenue_12)/100)/sum(add_user),2) ltv12,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_12)/100+sum(add_revenue_12)/100)/sum(add_user),2),'*') ltv12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_13)/100+sum(add_revenue_13)/100)/sum(add_user),2) ltv13,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_13)/100+sum(add_revenue_13)/100)/sum(add_user),2),'*') ltv13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_14)/100+sum(add_revenue_14)/100)/sum(add_user),2) ltv14,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_14)/100+sum(add_revenue_14)/100)/sum(add_user),2),'*') ltv14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_15)/100+sum(add_revenue_15)/100)/sum(add_user),2) ltv15,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_15)/100+sum(add_revenue_15)/100)/sum(add_user),2),'*') ltv15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_16)/100+sum(add_revenue_16)/100)/sum(add_user),2) ltv16,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_16)/100+sum(add_revenue_16)/100)/sum(add_user),2),'*') ltv16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_17)/100+sum(add_revenue_17)/100)/sum(add_user),2) ltv17,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_17)/100+sum(add_revenue_17)/100)/sum(add_user),2),'*') ltv17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_18)/100+sum(add_revenue_18)/100)/sum(add_user),2) ltv18,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_18)/100+sum(add_revenue_18)/100)/sum(add_user),2),'*') ltv18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_19)/100+sum(add_revenue_19)/100)/sum(add_user),2) ltv19,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_19)/100+sum(add_revenue_19)/100)/sum(add_user),2),'*') ltv19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_20)/100+sum(add_revenue_20)/100)/sum(add_user),2) ltv20,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_20)/100+sum(add_revenue_20)/100)/sum(add_user),2),'*') ltv20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_21)/100+sum(add_revenue_21)/100)/sum(add_user),2) ltv21,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_21)/100+sum(add_revenue_21)/100)/sum(add_user),2),'*') ltv21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv22")'>
					<choose>
						<when test=" diffdate &gt;=22 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_22)/100+sum(add_revenue_22)/100)/sum(add_user),2) ltv22,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_22)/100+sum(add_revenue_22)/100)/sum(add_user),2),'*') ltv22,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_23)/100+sum(add_revenue_23)/100)/sum(add_user),2) ltv23,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_23)/100+sum(add_revenue_23)/100)/sum(add_user),2),'*') ltv23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_24)/100+sum(add_revenue_24)/100)/sum(add_user),2) ltv24,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_24)/100+sum(add_revenue_24)/100)/sum(add_user),2),'*') ltv24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_25)/100+sum(add_revenue_25)/100)/sum(add_user),2) ltv25,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_25)/100+sum(add_revenue_25)/100)/sum(add_user),2),'*') ltv25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_26)/100+sum(add_revenue_26)/100)/sum(add_user),2) ltv26,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_26)/100+sum(add_revenue_26)/100)/sum(add_user),2),'*') ltv26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_27)/100+sum(add_revenue_27)/100)/sum(add_user),2) ltv27,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_27)/100+sum(add_revenue_27)/100)/sum(add_user),2),'*') ltv27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_28)/100+sum(add_revenue_28)/100)/sum(add_user),2) ltv28,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_28)/100+sum(add_revenue_28)/100)/sum(add_user),2),'*') ltv28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_29)/100+sum(add_revenue_29)/100)/sum(add_user),2) ltv29,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_29)/100+sum(add_revenue_29)/100)/sum(add_user),2),'*') ltv29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_30)/100+sum(add_revenue_30)/100)/sum(add_user),2) ltv30,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_30)/100+sum(add_revenue_30)/100)/sum(add_user),2),'*') ltv30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_36)/100+sum(add_revenue_36)/100)/sum(add_user),2) ltv36,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_36)/100+sum(add_revenue_36)/100)/sum(add_user),2),'*') ltv36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_42)/100+sum(add_revenue_42)/100)/sum(add_user),2) ltv42,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_42)/100+sum(add_revenue_42)/100)/sum(add_user),2),'*') ltv42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_48)/100+sum(add_revenue_48)/100)/sum(add_user),2) ltv48,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_48)/100+sum(add_revenue_48)/100)/sum(add_user),2),'*') ltv48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_54)/100+sum(add_revenue_54)/100)/sum(add_user),2) ltv54,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_54)/100+sum(add_revenue_54)/100)/sum(add_user),2),'*') ltv54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_60)/100+sum(add_revenue_60)/100)/sum(add_user),2) ltv60,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_60)/100+sum(add_revenue_60)/100)/sum(add_user),2),'*') ltv60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_75)/100+sum(add_revenue_75)/100)/sum(add_user),2) ltv75,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_75)/100+sum(add_revenue_75)/100)/sum(add_user),2),'*') ltv75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_90)/100+sum(add_revenue_90)/100)/sum(add_user),2) ltv90,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_90)/100+sum(add_revenue_90)/100)/sum(add_user),2),'*') ltv90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_105)/100+sum(add_revenue_105)/100)/sum(add_user),2) ltv105,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_105)/100+sum(add_revenue_105)/100)/sum(add_user),2),'*') ltv105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_120)/100+sum(add_revenue_120)/100)/sum(add_user),2) ltv120,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_120)/100+sum(add_revenue_120)/100)/sum(add_user),2),'*') ltv120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_135)/100+sum(add_revenue_135)/100)/sum(add_user),2) ltv135,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_135)/100+sum(add_revenue_135)/100)/sum(add_user),2),'*') ltv135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_150)/100+sum(add_revenue_150)/100)/sum(add_user),2) ltv150,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_150)/100+sum(add_revenue_150)/100)/sum(add_user),2),'*') ltv150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_165)/100+sum(add_revenue_165)/100)/sum(add_user),2) ltv165,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_165)/100+sum(add_revenue_165)/100)/sum(add_user),2),'*') ltv165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_180)/100+sum(add_revenue_180)/100)/sum(add_user),2) ltv180,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_180)/100+sum(add_revenue_180)/100)/sum(add_user),2),'*') ltv180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_195)/100+sum(add_revenue_195)/100)/sum(add_user),2) ltv195,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_195)/100+sum(add_revenue_195)/100)/sum(add_user),2),'*') ltv195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_210)/100+sum(add_revenue_210)/100)/sum(add_user),2) ltv210,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_210)/100+sum(add_revenue_210)/100)/sum(add_user),2),'*') ltv210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_225)/100+sum(add_revenue_225)/100)/sum(add_user),2) ltv225,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_225)/100+sum(add_revenue_225)/100)/sum(add_user),2),'*') ltv225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_240)/100+sum(add_revenue_240)/100)/sum(add_user),2) ltv240,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_240)/100+sum(add_revenue_240)/100)/sum(add_user),2),'*') ltv240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_255)/100+sum(add_revenue_255)/100)/sum(add_user),2) ltv255,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_255)/100+sum(add_revenue_255)/100)/sum(add_user),2),'*') ltv255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_270)/100+sum(add_revenue_270)/100)/sum(add_user),2) ltv270,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_270)/100+sum(add_revenue_270)/100)/sum(add_user),2),'*') ltv270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_285)/100+sum(add_revenue_285)/100)/sum(add_user),2) ltv285,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_285)/100+sum(add_revenue_285)/100)/sum(add_user),2),'*') ltv285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_300)/100+sum(add_revenue_300)/100)/sum(add_user),2) ltv300,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_300)/100+sum(add_revenue_300)/100)/sum(add_user),2),'*') ltv300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_315)/100+sum(add_revenue_315)/100)/sum(add_user),2) ltv315,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_315)/100+sum(add_revenue_315)/100)/sum(add_user),2),'*') ltv315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_330)/100+sum(add_revenue_330)/100)/sum(add_user),2) ltv330,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_330)/100+sum(add_revenue_330)/100)/sum(add_user),2),'*') ltv330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_345)/100+sum(add_revenue_345)/100)/sum(add_user),2) ltv345,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_345)/100+sum(add_revenue_345)/100)/sum(add_user),2),'*') ltv345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(add_purchase_revenue_360)/100+sum(add_revenue_360)/100)/sum(add_user),2) ltv360,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(add_purchase_revenue_360)/100+sum(add_revenue_360)/100)/sum(add_user),2),'*') ltv360,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("aRoi")'>
					CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_1)+sum(add_revenue_1))/sum(rebateCost)*100,2),0.00),'%') aRoi,
				</if>
				<if test='customizes.contains("roi2")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_2)+sum(add_revenue_2))/sum(rebateCost)*100,2),0.00),'%') roi2,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_2)+sum(add_revenue_2))/sum(rebateCost)*100,2),0.00),'%*') roi2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi3")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_3)+sum(add_revenue_3))/sum(rebateCost)*100,2),0.00),'%') roi3,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_3)+sum(add_revenue_3))/sum(rebateCost)*100,2),0.00),'%*') roi3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi4")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_4)+sum(add_revenue_4))/sum(rebateCost)*100,2),0.00),'%') roi4,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_4)+sum(add_revenue_4))/sum(rebateCost)*100,2),0.00),'%*') roi4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi5")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_5)+sum(add_revenue_5))/sum(rebateCost)*100,2),0.00),'%') roi5,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_5)+sum(add_revenue_5))/sum(rebateCost)*100,2),0.00),'%*') roi5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi6")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_6)+sum(add_revenue_6))/sum(rebateCost)*100,2),0.00),'%') roi6,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_6)+sum(add_revenue_6))/sum(rebateCost)*100,2),0.00),'%*') roi6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi7")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_7)+sum(add_revenue_7))/sum(rebateCost)*100,2),0.00),'%') roi7,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_7)+sum(add_revenue_7))/sum(rebateCost)*100,2),0.00),'%*') roi7,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_8)+sum(add_revenue_8))/sum(rebateCost)*100,2),0.00),'%') roi8,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_8)+sum(add_revenue_8))/sum(rebateCost)*100,2),0.00),'%*') roi8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_9)+sum(add_revenue_9))/sum(rebateCost)*100,2),0.00),'%') roi9,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_9)+sum(add_revenue_9))/sum(rebateCost)*100,2),0.00),'%*') roi9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_10)+sum(add_revenue_10))/sum(rebateCost)*100,2),0.00),'%') roi10,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_10)+sum(add_revenue_10))/sum(rebateCost)*100,2),0.00),'%*') roi10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_11)+sum(add_revenue_11))/sum(rebateCost)*100,2),0.00),'%') roi11,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_11)+sum(add_revenue_11))/sum(rebateCost)*100,2),0.00),'%*') roi11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_12)+sum(add_revenue_12))/sum(rebateCost)*100,2),0.00),'%') roi12,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_12)+sum(add_revenue_12))/sum(rebateCost)*100,2),0.00),'%*') roi12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_13)+sum(add_revenue_13))/sum(rebateCost)*100,2),0.00),'%') roi13,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_13)+sum(add_revenue_13))/sum(rebateCost)*100,2),0.00),'%*') roi13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_14)+sum(add_revenue_14))/sum(rebateCost)*100,2),0.00),'%') roi14,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_14)+sum(add_revenue_14))/sum(rebateCost)*100,2),0.00),'%*') roi14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_15)+sum(add_revenue_15))/sum(rebateCost)*100,2),0.00),'%') roi15,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_15)+sum(add_revenue_15))/sum(rebateCost)*100,2),0.00),'%*') roi15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_16)+sum(add_revenue_16))/sum(rebateCost)*100,2),0.00),'%') roi16,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_16)+sum(add_revenue_16))/sum(rebateCost)*100,2),0.00),'%*') roi16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_17)+sum(add_revenue_17))/sum(rebateCost)*100,2),0.00),'%') roi17,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_17)+sum(add_revenue_17))/sum(rebateCost)*100,2),0.00),'%*') roi17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_18)+sum(add_revenue_18))/sum(rebateCost)*100,2),0.00),'%') roi18,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_18)+sum(add_revenue_18))/sum(rebateCost)*100,2),0.00),'%*') roi18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_19)+sum(add_revenue_19))/sum(rebateCost)*100,2),0.00),'%') roi19,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_19)+sum(add_revenue_19))/sum(rebateCost)*100,2),0.00),'%*') roi19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_20)+sum(add_revenue_20))/sum(rebateCost)*100,2),0.00),'%') roi20,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_20)+sum(add_revenue_20))/sum(rebateCost)*100,2),0.00),'%*') roi20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_21)+sum(add_revenue_21))/sum(rebateCost)*100,2),0.00),'%') roi21,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_21)+sum(add_revenue_21))/sum(rebateCost)*100,2),0.00),'%*') roi21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi22")'>
					<choose>
						<when test=" diffdate &gt;=22 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_22)+sum(add_revenue_22))/sum(rebateCost)*100,2),0.00),'%') roi22,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_22)+sum(add_revenue_22))/sum(rebateCost)*100,2),0.00),'%*') roi22,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_23)+sum(add_revenue_23))/sum(rebateCost)*100,2),0.00),'%') roi23,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_23)+sum(add_revenue_23))/sum(rebateCost)*100,2),0.00),'%*') roi23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_24)+sum(add_revenue_24))/sum(rebateCost)*100,2),0.00),'%') roi24,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_24)+sum(add_revenue_24))/sum(rebateCost)*100,2),0.00),'%*') roi24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_25)+sum(add_revenue_25))/sum(rebateCost)*100,2),0.00),'%') roi25,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_25)+sum(add_revenue_25))/sum(rebateCost)*100,2),0.00),'%*') roi25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_26)+sum(add_revenue_26))/sum(rebateCost)*100,2),0.00),'%') roi26,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_26)+sum(add_revenue_26))/sum(rebateCost)*100,2),0.00),'%*') roi26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_27)+sum(add_revenue_27))/sum(rebateCost)*100,2),0.00),'%') roi27,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_27)+sum(add_revenue_27))/sum(rebateCost)*100,2),0.00),'%*') roi27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_28)+sum(add_revenue_28))/sum(rebateCost)*100,2),0.00),'%') roi28,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_28)+sum(add_revenue_28))/sum(rebateCost)*100,2),0.00),'%*') roi28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_29)+sum(add_revenue_29))/sum(rebateCost)*100,2),0.00),'%') roi29,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_29)+sum(add_revenue_29))/sum(rebateCost)*100,2),0.00),'%*') roi29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_30)+sum(add_revenue_30))/sum(rebateCost)*100,2),0.00),'%') roi30,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_30)+sum(add_revenue_30))/sum(rebateCost)*100,2),0.00),'%*') roi30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_36)+sum(add_revenue_36))/sum(rebateCost)*100,2),0.00),'%') roi36,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_36)+sum(add_revenue_36))/sum(rebateCost)*100,2),0.00),'%*') roi36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_42)+sum(add_revenue_42))/sum(rebateCost)*100,2),0.00),'%') roi42,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_42)+sum(add_revenue_42))/sum(rebateCost)*100,2),0.00),'%*') roi42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_48)+sum(add_revenue_48))/sum(rebateCost)*100,2),0.00),'%') roi48,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_48)+sum(add_revenue_48))/sum(rebateCost)*100,2),0.00),'%*') roi48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_54)+sum(add_revenue_54))/sum(rebateCost)*100,2),0.00),'%') roi54,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_54)+sum(add_revenue_54))/sum(rebateCost)*100,2),0.00),'%*') roi54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_60)+sum(add_revenue_60))/sum(rebateCost)*100,2),0.00),'%') roi60,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_60)+sum(add_revenue_60))/sum(rebateCost)*100,2),0.00),'%*') roi60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_75)+sum(add_revenue_75))/sum(rebateCost)*100,2),0.00),'%') roi75,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_75)+sum(add_revenue_75))/sum(rebateCost)*100,2),0.00),'%*') roi75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_90)+sum(add_revenue_90))/sum(rebateCost)*100,2),0.00),'%') roi90,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_90)+sum(add_revenue_90))/sum(rebateCost)*100,2),0.00),'%*') roi90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_105)+sum(add_revenue_105))/sum(rebateCost)*100,2),0.00),'%') roi105,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_105)+sum(add_revenue_105))/sum(rebateCost)*100,2),0.00),'%*') roi105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_120)+sum(add_revenue_120))/sum(rebateCost)*100,2),0.00),'%') roi120,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_120)+sum(add_revenue_120))/sum(rebateCost)*100,2),0.00),'%*') roi120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_135)+sum(add_revenue_135))/sum(rebateCost)*100,2),0.00),'%') roi135,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_135)+sum(add_revenue_135))/sum(rebateCost)*100,2),0.00),'%*') roi135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_150)+sum(add_revenue_150))/sum(rebateCost)*100,2),0.00),'%') roi150,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_150)+sum(add_revenue_150))/sum(rebateCost)*100,2),0.00),'%*') roi150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_165)+sum(add_revenue_165))/sum(rebateCost)*100,2),0.00),'%') roi165,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_165)+sum(add_revenue_165))/sum(rebateCost)*100,2),0.00),'%*') roi165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_180)+sum(add_revenue_180))/sum(rebateCost)*100,2),0.00),'%') roi180,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_180)+sum(add_revenue_180))/sum(rebateCost)*100,2),0.00),'%*') roi180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_195)+sum(add_revenue_195))/sum(rebateCost)*100,2),0.00),'%') roi195,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_195)+sum(add_revenue_195))/sum(rebateCost)*100,2),0.00),'%*') roi195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_210)+sum(add_revenue_210))/sum(rebateCost)*100,2),0.00),'%') roi210,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_210)+sum(add_revenue_210))/sum(rebateCost)*100,2),0.00),'%*') roi210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_225)+sum(add_revenue_225))/sum(rebateCost)*100,2),0.00),'%') roi225,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_225)+sum(add_revenue_225))/sum(rebateCost)*100,2),0.00),'%*') roi225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_240)+sum(add_revenue_240))/sum(rebateCost)*100,2),0.00),'%') roi240,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_240)+sum(add_revenue_240))/sum(rebateCost)*100,2),0.00),'%*') roi240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_255)+sum(add_revenue_255))/sum(rebateCost)*100,2),0.00),'%') roi255,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_255)+sum(add_revenue_255))/sum(rebateCost)*100,2),0.00),'%*') roi255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_270)+sum(add_revenue_270))/sum(rebateCost)*100,2),0.00),'%') roi270,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_270)+sum(add_revenue_270))/sum(rebateCost)*100,2),0.00),'%*') roi270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_285)+sum(add_revenue_285))/sum(rebateCost)*100,2),0.00),'%') roi285,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_285)+sum(add_revenue_285))/sum(rebateCost)*100,2),0.00),'%*') roi285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_300)+sum(add_revenue_300))/sum(rebateCost)*100,2),0.00),'%') roi300,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_300)+sum(add_revenue_300))/sum(rebateCost)*100,2),0.00),'%*') roi300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_315)+sum(add_revenue_315))/sum(rebateCost)*100,2),0.00),'%') roi315,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_315)+sum(add_revenue_315))/sum(rebateCost)*100,2),0.00),'%*') roi315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_330)+sum(add_revenue_330))/sum(rebateCost)*100,2),0.00),'%') roi330,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_330)+sum(add_revenue_330))/sum(rebateCost)*100,2),0.00),'%*') roi330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_345)+sum(add_revenue_345))/sum(rebateCost)*100,2),0.00),'%') roi345,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_345)+sum(add_revenue_345))/sum(rebateCost)*100,2),0.00),'%*') roi345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_360)+sum(add_revenue_360))/sum(rebateCost)*100,2),0.00),'%') roi360,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(add_purchase_revenue_360)+sum(add_revenue_360))/sum(rebateCost)*100,2),0.00),'%*') roi360,
						</otherwise>
					</choose>
				</if>
            </otherwise>
        </choose>
       app_id appId from  #{tableName}  where day BETWEEN #{beginDate} AND #{endDate} 
		<if test="appId != null and appId != ''">
			and app_id in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and media in (${media}) 
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account in (${accountId}) 
		</if>
		<if test="putUser != null and putUser != ''">
			and putUser in (${putUser}) 
		</if>
		<if test="campaignId != null and campaignId != ''">
			and campaign_id in (${campaignId}) 
		</if>
		<if test="campaignName != null and campaignName != ''">
			and campaign_name = #{campaignName} 
		</if>
		<if test="childChannel != null and childChannel != ''">
			and p_channel in (${childChannel})
		</if>
		<if test="groupName != null and groupName != ''">
			and group_name = #{groupName} 
		</if>
		<if test="company != null and company != ''">
			and company in (${company}) 
		</if>
		<if test="adsensePosition != null and adsensePosition != ''">
			and adsensePosition in (${adsensePosition}) 
		</if>
		<if test="strategy != null and strategy != ''">
			and transfer_type in (${strategy}) 
		</if>
		<if test="accountType != null and accountType != ''">
			and account_type in (${accountType}) 
		</if>
		<if test="delivery_mode != null and delivery_mode != ''">
			and delivery_mode = #{delivery_mode} 
		</if>
		<if test="anchor_related_type != null and anchor_related_type != ''">
			and anchor_related_type = #{anchor_related_type} 
		</if>
		<if test="scope != null and scope != ''">
          <if test="index == 'spend'">
              and rebateCost/100 <![CDATA[ ${symbol} ]]> #{number}
          </if>
        </if>
	</sql>
	<sql id="wx_game_ad_roi_sql" >
		SELECT  sum(reg_user_cnt) u,
		<if test='group.contains("day")'>
			tdate day,
		</if>
		<if test='group.contains("week")'>
			DATE_FORMAT(tdate,'%Y-%u') day,
			DATE_FORMAT(tdate,'%Y-%u') week,
		</if>
		<if test='group.contains("month")'>
			DATE_FORMAT(tdate,'%Y-%m') day,
			DATE_FORMAT(tdate,'%Y-%m') month,
		</if>
		<if test='group.contains("media")'>
			IFNULL(ad_buy_media,'自然量') media,
		</if>
		<if test='group.contains("channel")'>
			IFNULL(ad_buy_channel,'自然量') channel,
		</if>
		<if test='group.contains("accountId")'>
			account accountId,
		</if>
		<if test='group.contains("putUser")'>
			put_user putUser,
		</if>
		<if test='group.contains("groupName")'>
			group_name groupName,
		</if>
		<if test='group.contains("campaignName")'>
			campaign_name campaignName,
		</if>
		<if test='group.contains("campaignId")'>
			campaign_id campaignId,
		</if>
		<if test='group.contains("accountType")'>
			account_type accountType,
		</if>
		<if test='group.contains("strategy")'>
			transfer_type strategy,
		</if>
		<if test='group.contains("company")'>
			company company,
		</if>
		<if test='group.contains("adsensePosition")'>
			adsensePosition adsensePosition,
		</if>
		<if test='group.contains("anchor_related_type")'>
			anchor_related_type,
		</if>
		<if test='group.contains("delivery_mode")'>
			delivery_mode,
		</if>
		<if test='customizes.contains("addUser")'>
			sum(reg_user_cnt) addUser,
		</if>
		<if test='customizes.contains("activeUser")'>
			sum(active_user_cnt) activeUser,
		</if>
		<if test='customizes.contains("spend")'>
			sum(rebate_cost)/100 spend,
		</if>
		<if test='customizes.contains("dupAddUser")'>
			sum(reg_user_cnt) as dupAddUser,
		</if>
		<if test='customizes.contains("cpa")'>
			round(sum(rebate_cost)/100/sum(reg_user_cnt),2) cpa,
		</if>
		<if test='customizes.contains("avgVideo")'>
			round(sum(reg_user_video_cnt)/sum(reg_user_cnt),2) avgVideo,
		</if>
		<if test='customizes.contains("avgPlaque")'>
			round(sum(reg_user_plaque_cnt)/sum(reg_user_cnt),2) avgPlaque,
		</if>
		<if test='customizes.contains("activeAvgVideo")'>
			round(sum(active_user_video_cnt)/sum(active_user_cnt),2) activeAvgVideo,
		</if>
		<if test='customizes.contains("activeAvgPlaque")'>
			round(sum(active_user_plaque_cnt)/sum(active_user_cnt),2) activeAvgPlaque,
		</if>
		<if test='customizes.contains("videoEcpm")'>
			round(sum(reg_user_video_revenue)/sum(reg_user_video_cnt)*10,2) videoEcpm,
		</if>
		<if test='customizes.contains("plaqueEcpm")'>
			round(sum(reg_user_plaque_revenue)/sum(reg_user_plaque_cnt)*10,2) plaqueEcpm,
		</if>
		<if test='customizes.contains("firstPayCount")'>
			sum(pay_count) firstPayCount,
		</if>
		<if test='customizes.contains("paymentTimes")'>
			sum(game_pay_count) paymentTimes,
		</if>
		<if test='customizes.contains("firstChargeCost")'>
			FORMAT(sum(rebate_cost)/100/sum(pay_count),2) firstChargeCost,
		</if>
		<if test='customizes.contains("firstPaymentRate")'>
			CONCAT(IFNULL(FORMAT(sum(pay_count)/sum(installs)*100,2),0.00),'%') firstPaymentRate,
		</if>
		<if test='customizes.contains("paymentTimes7")'>
			sum(pay_count_7) paymentTimes7,
		</if>
		<if test='customizes.contains("avgPayCount7")'>
			round(sum(pay_count_7)/sum(pay_count_first_7),2) avgPayCount7,
		</if>
		<if test='customizes.contains("payCost")'>
			FORMAT(sum(rebate_cost)/100/sum(game_pay_count),2)  payCost,
		</if>
		<if test='customizes.contains("addPurchaseUsers")'>
			sum(iap_reg_user_cnt) addPurchaseUsers,
		</if>
		<if test='customizes.contains("activePurchaseUsers")'>
			sum(iap_active_user_cnt) activePurchaseUsers,
		</if>
		<if test='customizes.contains("addUserPayCost")'>
			round(sum(rebate_cost)/100/sum(iap_reg_user_cnt),2) addUserPayCost,
		</if>
		<if test='customizes.contains("addUserPayArpu")'>
			round(sum(iap_revenue_1)/100/sum(iap_reg_user_cnt),2) addUserPayArpu,
		</if>
		<if test='customizes.contains("addUserPayRate")'>
			CONCAT(IFNULL(FORMAT(sum(iap_reg_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') addUserPayRate,
		</if>
		<if test='customizes.contains("payRate")'>
			CONCAT(IFNULL(FORMAT(sum(iap_active_user_cnt)/sum(active_user_cnt)*100,2),0.00),'%') payRate,
		</if>
		<if test='customizes.contains("payArpu")'>
			round(sum(active_user_iap_revenue)/100/sum(iap_active_user_cnt),2) payArpu,
		</if>
		<if test='customizes.contains("convertSpend")'>
			FORMAT(sum(rebate_cost)/100/sum(`convert`),2) convertSpend,
		</if>
		<if test='customizes.contains("registerSpend")'>
			FORMAT(sum(rebate_cost)/100/sum(register),2) registerSpend,
		</if>
		<if test='customizes.contains("addictionSpend")'>
			FORMAT(sum(rebate_cost)/100/sum(game_addiction),2) addictionSpend,
		</if>
		<if test='customizes.contains("avgShowSpend")'>
			FORMAT(sum(rebate_cost)/sum(impressions)*10,2) avgShowSpend,
		</if>
		<if test='customizes.contains("convertRate")'>
			CONCAT(IFNULL(FORMAT(sum(`convert`)/sum(clicks)*100,2),0.00),'%') convertRate,
		</if>
		<if test='customizes.contains("installRate")'>
			CONCAT(IFNULL(FORMAT(sum(installs)/sum(clicks)*100,2),0.00),'%') installRate,
		</if>
		<if test='customizes.contains("registerRate")'>
			CONCAT(IFNULL(FORMAT(sum(register)/sum(installs)*100,2),0.00),'%') registerRate,
		</if>
		<if test='customizes.contains("addictionRate")'>
			CONCAT(IFNULL(FORMAT(sum(game_addiction)/sum(installs)*100,2),0.00),'%') addictionRate,
		</if>
		<if test='customizes.contains("clickRate")'>
			CONCAT(IFNULL(FORMAT(sum(clicks)/sum(impressions)*100,2),0.00),'%') clickRate,
		</if>
		<if test='customizes.contains("gameAddiction")'>
			sum(game_addiction) as gameAddiction,
		</if>
		<if test='customizes.contains("rdOneRate")'>
			<choose>
				<when test="diffdate &gt;=1 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(retention_1day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rdOneRate,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_1day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rdOneRate,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdTwo")'>
			<choose>
				<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month') ">
				 CONCAT(IFNULL(FORMAT(sum(retention_2day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rdTwo,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_2day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rdTwo,
				</otherwise>
			</choose>		
		</if>
		<if test='customizes.contains("rdThree")'>
			<choose>
				<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month') ">
				 CONCAT(IFNULL(FORMAT(sum(retention_3day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rdThree,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_3day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rdThree,
				</otherwise>
			</choose>		
		</if>
		<if test='customizes.contains("rdFour")'>
			<choose>
				<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_4day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rdFour,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_4day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rdFour,
				</otherwise>
			</choose>	
		</if>
		<if test='customizes.contains("rdFive")'>
			<choose>
				<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_5day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rdFive,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_5day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rdFive,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdSix")'>
			<choose>
				<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_6day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rdSix,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_6day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rdSix,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdSeven")'>
			<choose>
				<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
			     CONCAT(IFNULL(FORMAT(sum(retention_7day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rdSeven,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_7day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rdSeven,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdFourteen")'>
			<choose>
				<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_14day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rdFourteen,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_14day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rdFourteen,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdThrity")'>
			<choose>
				<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_30day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rdThrity,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_30day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rdThrity,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdThritySix")'>
			<choose>
				<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_36day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rdThritySix,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_36day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rdThritySix,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdFortyTwo")'>
			<choose>
				<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_42day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rdFortyTwo,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_42day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rdFortyTwo,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdFortyEight")'>
			<choose>
				<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_48day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rdFortyEight,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_48day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rdFortyEight,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdFiftyFour")'>
			<choose>
				<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_54day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rdFiftyFour,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_54day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rdFiftyFour,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rdSixty")'>
			<choose>
				<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_60day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rdSixty,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_60day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rdSixty,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd8")'>
			<choose>
				<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_8day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd8,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_8day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd8,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd9")'>
			<choose>
				<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_9day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd9,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_9day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd9,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd10")'>
			<choose>
				<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_10day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd10,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_10day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd10,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd11")'>
			<choose>
				<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_11day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd11,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_11day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd11,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd12")'>
			<choose>
				<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_12day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd12,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_12day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd12,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd13")'>
			<choose>
				<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_13day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd13,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_13day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd13,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd15")'>
			<choose>
				<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_15day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd15,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_15day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd15,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd16")'>
			<choose>
				<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_16day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd16,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_16day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd16,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd17")'>
			<choose>
				<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_17day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd17,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_17day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd17,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd18")'>
			<choose>
				<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_18day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd18,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_18day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd18,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd19")'>
			<choose>
				<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_19day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd19,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_19day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd19,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd20")'>
			<choose>
				<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_20day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd20,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_20day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd20,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd21")'>
			<choose>
				<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_21day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd21,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_21day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd21,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd22")'>
			<choose>
				<when test=" diffdate &gt;=22 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_22day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd22,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_22day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd22,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd23")'>
			<choose>
				<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_23day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd23,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_23day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd23,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd24")'>
			<choose>
				<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_24day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd24,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_24day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd24,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd25")'>
			<choose>
				<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_25day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd25,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_25day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd25,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd26")'>
			<choose>
				<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_26day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd26,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_26day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd26,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd27")'>
			<choose>
				<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_27day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd27,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_27day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd27,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd28")'>
			<choose>
				<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_28day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd28,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_28day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd28,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd29")'>
			<choose>
				<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_29day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd29,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_29day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd29,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd75")'>
			<choose>
				<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_75day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd75,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(retention_75day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd75,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd90")'>
			<choose>
				<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_90day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd90,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_90day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd90,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd105")'>
			<choose>
				<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_105day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd105,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_105day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd105,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd120")'>
			<choose>
				<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_120day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd120,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_120day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd120,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd135")'>
			<choose>
				<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_135day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd135,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_135day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd135,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd150")'>
			<choose>
				<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_150day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd150,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_150day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd150,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd165")'>
			<choose>
				<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_165day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd165,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_165day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd165,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd180")'>
			<choose>
				<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_180day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd180,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_180day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd180,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd195")'>
			<choose>
				<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_195day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd195,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_195day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd195,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd210")'>
			<choose>
				<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_210day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd210,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_210day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd210,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd225")'>
			<choose>
				<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_225day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd225,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_225day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd225,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd240")'>
			<choose>
				<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_240day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd240,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_240day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd240,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd255")'>
			<choose>
				<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_255day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd255,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_255day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd255,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd270")'>
			<choose>
				<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_270day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd270,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_270day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd270,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd285")'>
			<choose>
				<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_285day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd285,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_285day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd285,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd300")'>
			<choose>
				<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_300day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd300,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_300day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd300,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd315")'>
			<choose>
				<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_315day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd315,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_315day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd315,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd330")'>
			<choose>
				<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_330day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd330,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_330day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd330,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd345")'>
			<choose>
				<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_345day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd345,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_345day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd345,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("rd360")'>
			<choose>
				<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(retention_360day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%') rd360,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(retention_360day_user_cnt)/sum(reg_user_cnt)*100,2),0.00),'%*') rd360,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd1")'>
			<choose>
				<when test=" diffdate &gt;=1 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(iap_retention_1day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd1,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(iap_retention_1day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd1,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd2")'>
			<choose>
				<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_2day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd2,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_2day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd2,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd3")'>
			<choose>
				<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_3day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd3,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_3day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd3,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd4")'>
			<choose>
				<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(iap_retention_4day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd4,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_4day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd4,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd5")'>
			<choose>
				<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_5day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd5,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_5day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd5,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd6")'>
			<choose>
				<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_6day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd6,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_6day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd6,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd7")'>
			<choose>
				<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_7day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd7,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_7day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd7,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd14")'>
			<choose>
				<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_14day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd14,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_14day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd14,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd30")'>
			<choose>
				<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_30day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd30,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_30day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd30,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd36")'>
			<choose>
				<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_36day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd36,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_36day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd36,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd42")'>
			<choose>
				<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_42day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd42,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_42day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd42,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd48")'>
			<choose>
				<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_48day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd48,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(iap_retention_48day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd48,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd54")'>
			<choose>
				<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_54day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd54,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_54day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd54,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd60")'>
			<choose>
				<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_60day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd60,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_60day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd60,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd8")'>
			<choose>
				<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(iap_retention_8day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd8,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_8day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd8,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd9")'>
			<choose>
				<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_9day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd9,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_9day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd9,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd10")'>
			<choose>
				<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(iap_retention_10day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd10,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_10day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd10,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd11")'>
			<choose>
				<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_11day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd11,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_11day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd11,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd12")'>
			<choose>
				<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_12day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd12,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_12day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd12,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd13")'>
			<choose>
				<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(iap_retention_13day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd13,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_13day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd13,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd15")'>
			<choose>
				<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(iap_retention_15day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd15,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_15day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd15,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd16")'>
			<choose>
				<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_16day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd16,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_16day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd16,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd17")'>
			<choose>
				<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_17day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd17,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_17day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd17,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd18")'>
			<choose>
				<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_18day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd18,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_18day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd18,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd19")'>
			<choose>
				<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_19day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd19,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_19day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd19,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd20")'>
			<choose>
				<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_20day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd20,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_20day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd20,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd21")'>
			<choose>
				<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_21day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd21,
				</when>
				<otherwise>
				CONCAT(IFNULL(FORMAT(sum(iap_retention_21day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd21,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd22")'>
			<choose>
				<when test=" diffdate &gt;=22 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_22day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd22,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_22day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd22,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd23")'>
			<choose>
				<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_23day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd23,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_23day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd23,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd24")'>
			<choose>
				<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_24day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd24,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_24day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd24,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd25")'>
			<choose>
				<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_25day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd25,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_25day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd25,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd26")'>
			<choose>
				<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_26day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd26,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_26day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd26,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd27")'>
			<choose>
				<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_27day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd27,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_27day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd27,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd28")'>
			<choose>
				<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_28day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd28,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_28day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd28,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd29")'>
			<choose>
				<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_29day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd29,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_29day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd29,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd75")'>
			<choose>
				<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
				CONCAT(IFNULL(FORMAT(sum(iap_retention_75day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd75,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_75day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd75,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd90")'>
			<choose>
				<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_90day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd90,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_90day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd90,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd105")'>
			<choose>
				<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_105day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd105,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_105day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd105,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd120")'>
			<choose>
				<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_120day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd120,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_120day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd120,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd135")'>
			<choose>
				<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_135day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd135,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_135day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd135,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd150")'>
			<choose>
				<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_150day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd150,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_150day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd150,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd165")'>
			<choose>
				<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_165day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd165,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_165day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd165,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd180")'>
			<choose>
				<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_180day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd180,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_180day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd180,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd195")'>
			<choose>
				<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_195day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd195,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_195day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd195,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd210")'>
			<choose>
				<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_210day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd210,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_210day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd210,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd225")'>
			<choose>
				<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_225day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd225,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_225day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd225,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd240")'>
			<choose>
				<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_240day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd240,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_240day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd240,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd255")'>
			<choose>
				<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_255day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd255,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_255day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd255,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd270")'>
			<choose>
				<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_270day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd270,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_270day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd270,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd285")'>
			<choose>
				<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_285day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd285,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_285day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd285,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd300")'>
			<choose>
				<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_300day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd300,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_300day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd300,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd315")'>
			<choose>
				<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_315day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd315,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_315day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd315,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd330")'>
			<choose>
				<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_330day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd330,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_330day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd330,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd345")'>
			<choose>
				<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_345day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd345,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_345day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd345,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaRd360")'>
			<choose>
				<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_360day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%') ipaRd360,
				</when>
				<otherwise>
				 CONCAT(IFNULL(FORMAT(sum(iap_retention_360day_user_cnt)/sum(iap_reg_user_cnt)*100,2),0.00),'%*') ipaRd360,
				</otherwise>
			</choose>
		</if>
		<if test='customizes.contains("ipaIncome")'>
			round(sum(iap_revenue_1)/100,2)  ipaIncome,
		</if>
		<if test='customizes.contains("realizeIncome")'>
			round(sum(revenue_1)/100,2) realizeIncome,
		</if>
		<if test='customizes.contains("firstDayIncome")'>
			round(sum(iap_revenue_1)/100+sum(revenue_1)/100,2) firstDayIncome,
		</if>
		<if test='customizes.contains("realizeRoi")'>
			CONCAT(IFNULL(FORMAT(sum(revenue_1)/sum(rebate_cost)*100,2),0.00),'%') realizeRoi,
		</if>
		<if test='customizes.contains("ipaRoi")'>
			CONCAT(IFNULL(FORMAT(sum(iap_revenue_1)/sum(rebate_cost)*100,2),0.00),'%') ipaRoi,
		</if>
		<if test='customizes.contains("firstDayRoi")'>
			CONCAT(IFNULL(FORMAT((sum(iap_revenue_1)+sum(revenue_1))/sum(rebate_cost)*100,2),0.00),'%') firstDayRoi,
		</if>
		<if test='customizes.contains("adTotalRevenue")'>
			round(sum(active_user_revenue)/100,2)  adTotalRevenue,
		</if>
		<if test='customizes.contains("ipaTotalRevenue")'>
			round(sum(active_user_iap_revenue)/100,2) ipaTotalRevenue,
		</if>
		<if test='customizes.contains("activeRoi")'>
			CONCAT(IFNULL(FORMAT((sum(active_user_iap_revenue)+sum(active_user_revenue))/sum(rebate_cost)*100,2),0.00),'%') activeRoi,
		</if>
		<if test='customizes.contains("activeIpaRoi")'>
			CONCAT(IFNULL(FORMAT(sum(active_user_iap_revenue)/sum(rebate_cost)*100,2),0.00),'%') activeIpaRoi,
		</if>
		<if test='customizes.contains("activeAdRoi")'>
			CONCAT(IFNULL(FORMAT(sum(active_user_revenue)/sum(rebate_cost)*100,2),0.00),'%') activeAdRoi,
		</if>
		<if test='group.contains("campaign_id") or group.contains("campaignName")'>
			sum(rebate_cost)/100/sum(`convert`)/sum(bid),
		</if>
		<choose>
            <when test="revenueSource ==1">
                <if test='customizes.contains("totalIncome")'>
					round(sum(active_user_revenue)/100,2) totalIncome,
				</if>
				<if test='customizes.contains("addArpu")'>
					round(sum(revenue_1)/100/sum(reg_user_cnt),2) addArpu,
				</if>
				<if test='customizes.contains("dauArpu")'>
					round(sum(active_user_revenue)/100/sum(active_user_cnt),2) dauArpu,
				</if>
				<if test='customizes.contains("addUserLtv0")'>
					round(sum(revenue_1)/100,2) addUserLtv0,
				</if>
				<if test='customizes.contains("ltv1")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_2)/100/sum(reg_user_cnt),2) ltv1,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_2)/100/sum(reg_user_cnt),2),'*') ltv1,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv2")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_3)/100/sum(reg_user_cnt),2) ltv2,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_3)/100/sum(reg_user_cnt),2),'*') ltv2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv3")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_4)/100/sum(reg_user_cnt),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_4)/100/sum(reg_user_cnt),2),'*') ltv3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv4")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_5)/100/sum(reg_user_cnt),2) ltv4,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_5)/100/sum(reg_user_cnt),2),'*') ltv4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv5")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_6)/100/sum(reg_user_cnt),2) ltv5,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_6)/100/sum(reg_user_cnt),2),'*') ltv5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv6")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_7)/100/sum(reg_user_cnt),2) ltv6,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_7)/100/sum(reg_user_cnt),2) ltv6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_8)/100/sum(reg_user_cnt),2) ltv8,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_8)/100/sum(reg_user_cnt),2),'*') ltv8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_9)/100/sum(reg_user_cnt),2) ltv9,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_9)/100/sum(reg_user_cnt),2),'*')  ltv9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_10)/100/sum(reg_user_cnt),2) ltv10,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_10)/100/sum(reg_user_cnt),2),'*') ltv10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_11)/100/sum(reg_user_cnt),2) ltv11,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_11)/100/sum(reg_user_cnt),2),'*') ltv11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_12)/100/sum(reg_user_cnt),2) ltv12,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_12)/100/sum(reg_user_cnt),2),'*') ltv12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_13)/100/sum(reg_user_cnt),2) ltv13,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_13)/100/sum(reg_user_cnt),2),'*') ltv13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_14)/100/sum(reg_user_cnt),2) ltv14,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_14)/100/sum(reg_user_cnt),2),'*') ltv14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_15)/100/sum(reg_user_cnt),2) ltv15,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_15)/100/sum(reg_user_cnt),2),'*') ltv15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_16)/100/sum(reg_user_cnt),2) ltv16,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_16)/100/sum(reg_user_cnt),2),'*') ltv16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_17)/100/sum(reg_user_cnt),2) ltv17,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_17)/100/sum(reg_user_cnt),2),'*') ltv17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_18)/100/sum(reg_user_cnt),2) ltv18,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_18)/100/sum(reg_user_cnt),2),'*') ltv18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_19)/100/sum(reg_user_cnt),2) ltv19,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_19)/100/sum(reg_user_cnt),2),'*') ltv19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_20)/100/sum(reg_user_cnt),2) ltv20,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_20)/100/sum(reg_user_cnt),2),'*') ltv20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_21)/100/sum(reg_user_cnt),2) ltv21,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_21)/100/sum(reg_user_cnt),2),'*') ltv21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv22")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_4)/100/sum(reg_user_cnt),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_4)/100/sum(reg_user_cnt),2),'*') ltv3,
						</otherwise>
					</choose>
					FORMAT(sum(revenue_22)/100/sum(reg_user_cnt),2) ltv22,
				</if>
				<if test='customizes.contains("ltv23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_23)/100/sum(reg_user_cnt),2) ltv23,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_23)/100/sum(reg_user_cnt),2),'*') ltv23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_24)/100/sum(reg_user_cnt),2) ltv24,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_24)/100/sum(reg_user_cnt),2),'*') ltv24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_25)/100/sum(reg_user_cnt),2) ltv25,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_25)/100/sum(reg_user_cnt),2),'*') ltv25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_26)/100/sum(reg_user_cnt),2) ltv26,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_26)/100/sum(reg_user_cnt),2),'*') ltv26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_27)/100/sum(reg_user_cnt),2) ltv27,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_27)/100/sum(reg_user_cnt),2),'*') ltv27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_28)/100/sum(reg_user_cnt),2) ltv28,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_28)/100/sum(reg_user_cnt),2),'*') ltv28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_29)/100/sum(reg_user_cnt),2) ltv29,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_29)/100/sum(reg_user_cnt),2),'*') ltv29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_30)/100/sum(reg_user_cnt),2) ltv30,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_30)/100/sum(reg_user_cnt),2),'*') ltv30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_36)/100/sum(reg_user_cnt),2) ltv36,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_36)/100/sum(reg_user_cnt),2),'*') ltv36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_42)/100/sum(reg_user_cnt),2) ltv42,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_42)/100/sum(reg_user_cnt),2),'*') ltv42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_48)/100/sum(reg_user_cnt),2) ltv48,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_48)/100/sum(reg_user_cnt),2),'*') ltv48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_54)/100/sum(reg_user_cnt),2) ltv54,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_54)/100/sum(reg_user_cnt),2),'*') ltv54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_60)/100/sum(reg_user_cnt),2) ltv60,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_60)/100/sum(reg_user_cnt),2),'*') ltv60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_75)/100/sum(reg_user_cnt),2) ltv75,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_75)/100/sum(reg_user_cnt),2),'*') ltv75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_90)/100/sum(reg_user_cnt),2) ltv90,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_90)/100/sum(reg_user_cnt),2),'*') ltv90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_105)/100/sum(reg_user_cnt),2) ltv105,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_105)/100/sum(reg_user_cnt),2),'*') ltv105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_120)/100/sum(reg_user_cnt),2) ltv120,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_120)/100/sum(reg_user_cnt),2),'*') ltv120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_135)/100/sum(reg_user_cnt),2) ltv135,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_135)/100/sum(reg_user_cnt),2),'*') ltv135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_150)/100/sum(reg_user_cnt),2) ltv150,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_150)/100/sum(reg_user_cnt),2),'*') ltv150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_165)/100/sum(reg_user_cnt),2) ltv165,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_165)/100/sum(reg_user_cnt),2),'*') ltv165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_180)/100/sum(reg_user_cnt),2) ltv180,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_180)/100/sum(reg_user_cnt),2),'*') ltv180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_195)/100/sum(reg_user_cnt),2) ltv195,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_195)/100/sum(reg_user_cnt),2),'*') ltv195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_210)/100/sum(reg_user_cnt),2) ltv210,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_210)/100/sum(reg_user_cnt),2),'*') ltv210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_225)/100/sum(reg_user_cnt),2) ltv225,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_225)/100/sum(reg_user_cnt),2),'*') ltv225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_240)/100/sum(reg_user_cnt),2) ltv240,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_240)/100/sum(reg_user_cnt),2),'*') ltv240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_255)/100/sum(reg_user_cnt),2) ltv255,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_255)/100/sum(reg_user_cnt),2),'*') ltv255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_270)/100/sum(reg_user_cnt),2) ltv270,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_270)/100/sum(reg_user_cnt),2),'*') ltv270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_285)/100/sum(reg_user_cnt),2) ltv285,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_285)/100/sum(reg_user_cnt),2),'*') ltv285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_300)/100/sum(reg_user_cnt),2) ltv300,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_300)/100/sum(reg_user_cnt),2),'*') ltv300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_315)/100/sum(reg_user_cnt),2) ltv315,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_315)/100/sum(reg_user_cnt),2),'*') ltv315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_330)/100/sum(reg_user_cnt),2) ltv330,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_330)/100/sum(reg_user_cnt),2),'*') ltv330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_345)/100/sum(reg_user_cnt),2) ltv345,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_345)/100/sum(reg_user_cnt),2),'*') ltv345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(revenue_360)/100/sum(reg_user_cnt),2) ltv360,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(revenue_360)/100/sum(reg_user_cnt),2),'*') ltv360,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("aRoi")'>
					CONCAT(IFNULL(FORMAT(sum(revenue_1)/sum(rebate_cost)*100,2),0.00),'%') aRoi,
				</if>
				<if test='customizes.contains("roi2")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_2)/sum(rebate_cost)*100,2),0.00),'%') roi2,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_2)/sum(rebate_cost)*100,2),0.00),'%*') roi2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi3")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_3)/sum(rebate_cost)*100,2),0.00),'%') roi3,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_3)/sum(rebate_cost)*100,2),0.00),'%*') roi3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi4")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_4)/sum(rebate_cost)*100,2),0.00),'%') roi4,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_4)/sum(rebate_cost)*100,2),0.00),'%*') roi4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi5")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_5)/sum(rebate_cost)*100,2),0.00),'%') roi5,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_5)/sum(rebate_cost)*100,2),0.00),'%*') roi5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi6")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_7)/sum(rebate_cost)*100,2),0.00),'%') roi6,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_7)/sum(rebate_cost)*100,2),0.00),'%*') roi6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi7")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_7)/sum(rebate_cost)*100,2),0.00),'%') roi7,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_7)/sum(rebate_cost)*100,2),0.00),'%*') roi7,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_8)/sum(rebate_cost)*100,2),0.00),'%') roi8,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_8)/sum(rebate_cost)*100,2),0.00),'%*') roi8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_9)/sum(rebate_cost)*100,2),0.00),'%') roi9,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_9)/sum(rebate_cost)*100,2),0.00),'%*') roi9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_10)/sum(rebate_cost)*100,2),0.00),'%') roi10,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_10)/sum(rebate_cost)*100,2),0.00),'%*') roi10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_11)/sum(rebate_cost)*100,2),0.00),'%') roi11,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_11)/sum(rebate_cost)*100,2),0.00),'%*') roi11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_12)/sum(rebate_cost)*100,2),0.00),'%') roi12,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_12)/sum(rebate_cost)*100,2),0.00),'%*') roi12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_13)/sum(rebate_cost)*100,2),0.00),'%') roi13,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_13)/sum(rebate_cost)*100,2),0.00),'%*') roi13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_14)/sum(rebate_cost)*100,2),0.00),'%') roi14,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_14)/sum(rebate_cost)*100,2),0.00),'%*') roi14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_15)/sum(rebate_cost)*100,2),0.00),'%') roi15,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_15)/sum(rebate_cost)*100,2),0.00),'%*') roi15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_16)/sum(rebate_cost)*100,2),0.00),'%') roi16,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_16)/sum(rebate_cost)*100,2),0.00),'%*') roi16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_17)/sum(rebate_cost)*100,2),0.00),'%') roi17,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_17)/sum(rebate_cost)*100,2),0.00),'%*') roi17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_18)/sum(rebate_cost)*100,2),0.00),'%') roi18,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_18)/sum(rebate_cost)*100,2),0.00),'%*') roi18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_19)/sum(rebate_cost)*100,2),0.00),'%') roi19,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_19)/sum(rebate_cost)*100,2),0.00),'%*') roi19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_20)/sum(rebate_cost)*100,2),0.00),'%') roi20,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_20)/sum(rebate_cost)*100,2),0.00),'%*') roi20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_21)/sum(rebate_cost)*100,2),0.00),'%') roi21,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_21)/sum(rebate_cost)*100,2),0.00),'%') roi21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi22")'>
					<choose>
						<when test=" diffdate &gt;=22 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_22)/sum(rebate_cost)*100,2),0.00),'%') roi22,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_22)/sum(rebate_cost)*100,2),0.00),'%*') roi22,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_23)/sum(rebate_cost)*100,2),0.00),'%') roi23,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_23)/sum(rebate_cost)*100,2),0.00),'%*') roi23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_24)/sum(rebate_cost)*100,2),0.00),'%') roi24,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_24)/sum(rebate_cost)*100,2),0.00),'%*') roi24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_25)/sum(rebate_cost)*100,2),0.00),'%') roi25,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_25)/sum(rebate_cost)*100,2),0.00),'%*') roi25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_26)/sum(rebate_cost)*100,2),0.00),'%') roi26,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_26)/sum(rebate_cost)*100,2),0.00),'%*') roi26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_27)/sum(rebate_cost)*100,2),0.00),'%') roi27,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_27)/sum(rebate_cost)*100,2),0.00),'%*') roi27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_28)/sum(rebate_cost)*100,2),0.00),'%') roi28,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_28)/sum(rebate_cost)*100,2),0.00),'%*') roi28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_29)/sum(rebate_cost)*100,2),0.00),'%') roi29,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_29)/sum(rebate_cost)*100,2),0.00),'%*') roi29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_30)/sum(rebate_cost)*100,2),0.00),'%') roi30,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_30)/sum(rebate_cost)*100,2),0.00),'%*') roi30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_36)/sum(rebate_cost)*100,2),0.00),'%') roi36,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_36)/sum(rebate_cost)*100,2),0.00),'%*') roi36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_42)/sum(rebate_cost)*100,2),0.00),'%') roi42,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_42)/sum(rebate_cost)*100,2),0.00),'%*') roi42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_48)/sum(rebate_cost)*100,2),0.00),'%') roi48,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_48)/sum(rebate_cost)*100,2),0.00),'%*') roi48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_54)/sum(rebate_cost)*100,2),0.00),'%') roi54,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_54)/sum(rebate_cost)*100,2),0.00),'%*') roi54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_60)/sum(rebate_cost)*100,2),0.00),'%') roi60,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_60)/sum(rebate_cost)*100,2),0.00),'%*') roi60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_75)/sum(rebate_cost)*100,2),0.00),'%') roi75,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_75)/sum(rebate_cost)*100,2),0.00),'%*') roi75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(revenue_90)/sum(rebate_cost)*100,2),0.00),'%') roi90,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(revenue_90)/sum(rebate_cost)*100,2),0.00),'%*') roi90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_105)/sum(rebate_cost)*100,2),0.00),'%') roi105,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_105)/sum(rebate_cost)*100,2),0.00),'%*') roi105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_120)/sum(rebate_cost)*100,2),0.00),'%') roi120,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_120)/sum(rebate_cost)*100,2),0.00),'%*') roi120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_135)/sum(rebate_cost)*100,2),0.00),'%') roi135,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_135)/sum(rebate_cost)*100,2),0.00),'%*') roi135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_150)/sum(rebate_cost)*100,2),0.00),'%') roi150,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_150)/sum(rebate_cost)*100,2),0.00),'%*') roi150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_165)/sum(rebate_cost)*100,2),0.00),'%') roi165,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_165)/sum(rebate_cost)*100,2),0.00),'%*') roi165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_180)/sum(rebate_cost)*100,2),0.00),'%') roi180,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_180)/sum(rebate_cost)*100,2),0.00),'%*') roi180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_195)/sum(rebate_cost)*100,2),0.00),'%') roi195,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_195)/sum(rebate_cost)*100,2),0.00),'%*') roi195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_210)/sum(rebate_cost)*100,2),0.00),'%') roi210,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_210)/sum(rebate_cost)*100,2),0.00),'%*') roi210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_225)/sum(rebate_cost)*100,2),0.00),'%') roi225,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_225)/sum(rebate_cost)*100,2),0.00),'%*') roi225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_240)/sum(rebate_cost)*100,2),0.00),'%') roi240,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_240)/sum(rebate_cost)*100,2),0.00),'%*') roi240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_255)/sum(rebate_cost)*100,2),0.00),'%') roi255,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_255)/sum(rebate_cost)*100,2),0.00),'%*') roi255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_270)/sum(rebate_cost)*100,2),0.00),'%') roi270,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_270)/sum(rebate_cost)*100,2),0.00),'%*') roi270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_285)/sum(rebate_cost)*100,2),0.00),'%') roi285,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_285)/sum(rebate_cost)*100,2),0.00),'%*') roi285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_300)/sum(rebate_cost)*100,2),0.00),'%') roi300,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_300)/sum(rebate_cost)*100,2),0.00),'%*') roi300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_315)/sum(rebate_cost)*100,2),0.00),'%') roi315,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_315)/sum(rebate_cost)*100,2),0.00),'%*') roi315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_330)/sum(rebate_cost)*100,2),0.00),'%') roi330,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_330)/sum(rebate_cost)*100,2),0.00),'%*') roi330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_345)/sum(rebate_cost)*100,2),0.00),'%') roi345,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_345)/sum(rebate_cost)*100,2),0.00),'%*') roi345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(revenue_360)/sum(rebate_cost)*100,2),0.00),'%') roi360,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(revenue_360)/sum(rebate_cost)*100,2),0.00),'%*') roi360,
						</otherwise>
					</choose>
				</if>
            </when>
            <when test="revenueSource ==2">
            	<if test='customizes.contains("addUserLtv0")'>
					round(sum(iap_revenue_1)/100,2) addUserLtv0,
				</if>
                 <if test='customizes.contains("totalIncome")'>
					round(sum(active_user_iap_revenue)/100,2) as totalIncome,
				</if>
				<if test='customizes.contains("addArpu")'>
					round(sum(iap_revenue_1)/100/sum(reg_user_cnt),2) addArpu,
				</if>
				<if test='customizes.contains("dauArpu")'>
					round(sum(active_user_iap_revenue)/100/sum(active_user_cnt),2) dauArpu,
				</if>
				<if test='customizes.contains("ltv1")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_2)/100/sum(reg_user_cnt),2) ltv1,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_2)/100/sum(reg_user_cnt),2),'*') ltv1,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv2")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_3)/100/sum(reg_user_cnt),2) ltv2,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_3)/100/sum(reg_user_cnt),2),'*') ltv2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv3")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_4)/100/sum(reg_user_cnt),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_4)/100/sum(reg_user_cnt),2),'*') ltv3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv4")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_5)/100/sum(reg_user_cnt),2) ltv4,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_5)/100/sum(reg_user_cnt),2),'*') ltv4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv5")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_6)/100/sum(reg_user_cnt),2) ltv5,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_6)/100/sum(reg_user_cnt),2),'*') ltv5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv6")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_7)/100/sum(reg_user_cnt),2) ltv6,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_7)/100/sum(reg_user_cnt),2) ltv6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_8)/100/sum(reg_user_cnt),2) ltv8,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_8)/100/sum(reg_user_cnt),2),'*') ltv8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_9)/100/sum(reg_user_cnt),2) ltv9,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_9)/100/sum(reg_user_cnt),2),'*')  ltv9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_10)/100/sum(reg_user_cnt),2) ltv10,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_10)/100/sum(reg_user_cnt),2),'*') ltv10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_11)/100/sum(reg_user_cnt),2) ltv11,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_11)/100/sum(reg_user_cnt),2),'*') ltv11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_12)/100/sum(reg_user_cnt),2) ltv12,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_12)/100/sum(reg_user_cnt),2),'*') ltv12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_13)/100/sum(reg_user_cnt),2) ltv13,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_13)/100/sum(reg_user_cnt),2),'*') ltv13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_14)/100/sum(reg_user_cnt),2) ltv14,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_14)/100/sum(reg_user_cnt),2),'*') ltv14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_15)/100/sum(reg_user_cnt),2) ltv15,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_15)/100/sum(reg_user_cnt),2),'*') ltv15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_16)/100/sum(reg_user_cnt),2) ltv16,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_16)/100/sum(reg_user_cnt),2),'*') ltv16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_17)/100/sum(reg_user_cnt),2) ltv17,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_17)/100/sum(reg_user_cnt),2),'*') ltv17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_18)/100/sum(reg_user_cnt),2) ltv18,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_18)/100/sum(reg_user_cnt),2),'*') ltv18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_19)/100/sum(reg_user_cnt),2) ltv19,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_19)/100/sum(reg_user_cnt),2),'*') ltv19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_20)/100/sum(reg_user_cnt),2) ltv20,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_20)/100/sum(reg_user_cnt),2),'*') ltv20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_21)/100/sum(reg_user_cnt),2) ltv21,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_21)/100/sum(reg_user_cnt),2),'*') ltv21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv22")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_4)/100/sum(reg_user_cnt),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_4)/100/sum(reg_user_cnt),2),'*') ltv3,
						</otherwise>
					</choose>
					FORMAT(sum(iap_revenue_22)/100/sum(reg_user_cnt),2) ltv22,
				</if>
				<if test='customizes.contains("ltv23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_23)/100/sum(reg_user_cnt),2) ltv23,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_23)/100/sum(reg_user_cnt),2),'*') ltv23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_24)/100/sum(reg_user_cnt),2) ltv24,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_24)/100/sum(reg_user_cnt),2),'*') ltv24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_25)/100/sum(reg_user_cnt),2) ltv25,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_25)/100/sum(reg_user_cnt),2),'*') ltv25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_26)/100/sum(reg_user_cnt),2) ltv26,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_26)/100/sum(reg_user_cnt),2),'*') ltv26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_27)/100/sum(reg_user_cnt),2) ltv27,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_27)/100/sum(reg_user_cnt),2),'*') ltv27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_28)/100/sum(reg_user_cnt),2) ltv28,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_28)/100/sum(reg_user_cnt),2),'*') ltv28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_29)/100/sum(reg_user_cnt),2) ltv29,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_29)/100/sum(reg_user_cnt),2),'*') ltv29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_30)/100/sum(reg_user_cnt),2) ltv30,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_30)/100/sum(reg_user_cnt),2),'*') ltv30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_36)/100/sum(reg_user_cnt),2) ltv36,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_36)/100/sum(reg_user_cnt),2),'*') ltv36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_42)/100/sum(reg_user_cnt),2) ltv42,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_42)/100/sum(reg_user_cnt),2),'*') ltv42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_48)/100/sum(reg_user_cnt),2) ltv48,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_48)/100/sum(reg_user_cnt),2),'*') ltv48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_54)/100/sum(reg_user_cnt),2) ltv54,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_54)/100/sum(reg_user_cnt),2),'*') ltv54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_60)/100/sum(reg_user_cnt),2) ltv60,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_60)/100/sum(reg_user_cnt),2),'*') ltv60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_75)/100/sum(reg_user_cnt),2) ltv75,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_75)/100/sum(reg_user_cnt),2),'*') ltv75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_90)/100/sum(reg_user_cnt),2) ltv90,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_90)/100/sum(reg_user_cnt),2),'*') ltv90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_105)/100/sum(reg_user_cnt),2) ltv105,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_105)/100/sum(reg_user_cnt),2),'*') ltv105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_120)/100/sum(reg_user_cnt),2) ltv120,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_120)/100/sum(reg_user_cnt),2),'*') ltv120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_135)/100/sum(reg_user_cnt),2) ltv135,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_135)/100/sum(reg_user_cnt),2),'*') ltv135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_150)/100/sum(reg_user_cnt),2) ltv150,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_150)/100/sum(reg_user_cnt),2),'*') ltv150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_165)/100/sum(reg_user_cnt),2) ltv165,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_165)/100/sum(reg_user_cnt),2),'*') ltv165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_180)/100/sum(reg_user_cnt),2) ltv180,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_180)/100/sum(reg_user_cnt),2),'*') ltv180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_195)/100/sum(reg_user_cnt),2) ltv195,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_195)/100/sum(reg_user_cnt),2),'*') ltv195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_210)/100/sum(reg_user_cnt),2) ltv210,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_210)/100/sum(reg_user_cnt),2),'*') ltv210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_225)/100/sum(reg_user_cnt),2) ltv225,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_225)/100/sum(reg_user_cnt),2),'*') ltv225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_240)/100/sum(reg_user_cnt),2) ltv240,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_240)/100/sum(reg_user_cnt),2),'*') ltv240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_255)/100/sum(reg_user_cnt),2) ltv255,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_255)/100/sum(reg_user_cnt),2),'*') ltv255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_270)/100/sum(reg_user_cnt),2) ltv270,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_270)/100/sum(reg_user_cnt),2),'*') ltv270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_285)/100/sum(reg_user_cnt),2) ltv285,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_285)/100/sum(reg_user_cnt),2),'*') ltv285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_300)/100/sum(reg_user_cnt),2) ltv300,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_300)/100/sum(reg_user_cnt),2),'*') ltv300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_315)/100/sum(reg_user_cnt),2) ltv315,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_315)/100/sum(reg_user_cnt),2),'*') ltv315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_330)/100/sum(reg_user_cnt),2) ltv330,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_330)/100/sum(reg_user_cnt),2),'*') ltv330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_345)/100/sum(reg_user_cnt),2) ltv345,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_345)/100/sum(reg_user_cnt),2),'*') ltv345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT(sum(iap_revenue_360)/100/sum(reg_user_cnt),2) ltv360,
						</when>
						<otherwise>
						CONCAT(FORMAT(sum(iap_revenue_360)/100/sum(reg_user_cnt),2),'*') ltv360,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("aRoi")'>
					CONCAT(IFNULL(FORMAT(sum(iap_revenue_1)/sum(rebate_cost)*100,2),0.00),'%') aRoi,
				</if>
				<if test='customizes.contains("roi2")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_2)/sum(rebate_cost)*100,2),0.00),'%') roi2,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_2)/sum(rebate_cost)*100,2),0.00),'%*') roi2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi3")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_3)/sum(rebate_cost)*100,2),0.00),'%') roi3,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_3)/sum(rebate_cost)*100,2),0.00),'%*') roi3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi4")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_4)/sum(rebate_cost)*100,2),0.00),'%') roi4,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_4)/sum(rebate_cost)*100,2),0.00),'%*') roi4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi5")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_5)/sum(rebate_cost)*100,2),0.00),'%') roi5,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_5)/sum(rebate_cost)*100,2),0.00),'%*') roi5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi6")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_7)/sum(rebate_cost)*100,2),0.00),'%') roi6,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_7)/sum(rebate_cost)*100,2),0.00),'%*') roi6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi7")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_7)/sum(rebate_cost)*100,2),0.00),'%') roi7,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_7)/sum(rebate_cost)*100,2),0.00),'%*') roi7,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_8)/sum(rebate_cost)*100,2),0.00),'%') roi8,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_8)/sum(rebate_cost)*100,2),0.00),'%*') roi8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_9)/sum(rebate_cost)*100,2),0.00),'%') roi9,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_9)/sum(rebate_cost)*100,2),0.00),'%*') roi9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_10)/sum(rebate_cost)*100,2),0.00),'%') roi10,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_10)/sum(rebate_cost)*100,2),0.00),'%*') roi10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_11)/sum(rebate_cost)*100,2),0.00),'%') roi11,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_11)/sum(rebate_cost)*100,2),0.00),'%*') roi11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_12)/sum(rebate_cost)*100,2),0.00),'%') roi12,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_12)/sum(rebate_cost)*100,2),0.00),'%*') roi12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_13)/sum(rebate_cost)*100,2),0.00),'%') roi13,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_13)/sum(rebate_cost)*100,2),0.00),'%*') roi13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_14)/sum(rebate_cost)*100,2),0.00),'%') roi14,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_14)/sum(rebate_cost)*100,2),0.00),'%*') roi14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_15)/sum(rebate_cost)*100,2),0.00),'%') roi15,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_15)/sum(rebate_cost)*100,2),0.00),'%*') roi15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_16)/sum(rebate_cost)*100,2),0.00),'%') roi16,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_16)/sum(rebate_cost)*100,2),0.00),'%*') roi16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_17)/sum(rebate_cost)*100,2),0.00),'%') roi17,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_17)/sum(rebate_cost)*100,2),0.00),'%*') roi17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_18)/sum(rebate_cost)*100,2),0.00),'%') roi18,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_18)/sum(rebate_cost)*100,2),0.00),'%*') roi18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_19)/sum(rebate_cost)*100,2),0.00),'%') roi19,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_19)/sum(rebate_cost)*100,2),0.00),'%*') roi19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_20)/sum(rebate_cost)*100,2),0.00),'%') roi20,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_20)/sum(rebate_cost)*100,2),0.00),'%*') roi20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_21)/sum(rebate_cost)*100,2),0.00),'%') roi21,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_21)/sum(rebate_cost)*100,2),0.00),'%') roi21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi22")'>
					<choose>
						<when test=" diffdate &gt;=22 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_22)/sum(rebate_cost)*100,2),0.00),'%') roi22,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_22)/sum(rebate_cost)*100,2),0.00),'%*') roi22,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_23)/sum(rebate_cost)*100,2),0.00),'%') roi23,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_23)/sum(rebate_cost)*100,2),0.00),'%*') roi23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_24)/sum(rebate_cost)*100,2),0.00),'%') roi24,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_24)/sum(rebate_cost)*100,2),0.00),'%*') roi24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_25)/sum(rebate_cost)*100,2),0.00),'%') roi25,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_25)/sum(rebate_cost)*100,2),0.00),'%*') roi25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_26)/sum(rebate_cost)*100,2),0.00),'%') roi26,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_26)/sum(rebate_cost)*100,2),0.00),'%*') roi26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_27)/sum(rebate_cost)*100,2),0.00),'%') roi27,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_27)/sum(rebate_cost)*100,2),0.00),'%*') roi27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_28)/sum(rebate_cost)*100,2),0.00),'%') roi28,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_28)/sum(rebate_cost)*100,2),0.00),'%*') roi28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_29)/sum(rebate_cost)*100,2),0.00),'%') roi29,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_29)/sum(rebate_cost)*100,2),0.00),'%*') roi29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_30)/sum(rebate_cost)*100,2),0.00),'%') roi30,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_30)/sum(rebate_cost)*100,2),0.00),'%*') roi30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_36)/sum(rebate_cost)*100,2),0.00),'%') roi36,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_36)/sum(rebate_cost)*100,2),0.00),'%*') roi36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_42)/sum(rebate_cost)*100,2),0.00),'%') roi42,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_42)/sum(rebate_cost)*100,2),0.00),'%*') roi42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_48)/sum(rebate_cost)*100,2),0.00),'%') roi48,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_48)/sum(rebate_cost)*100,2),0.00),'%*') roi48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_54)/sum(rebate_cost)*100,2),0.00),'%') roi54,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_54)/sum(rebate_cost)*100,2),0.00),'%*') roi54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_60)/sum(rebate_cost)*100,2),0.00),'%') roi60,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_60)/sum(rebate_cost)*100,2),0.00),'%*') roi60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_75)/sum(rebate_cost)*100,2),0.00),'%') roi75,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_75)/sum(rebate_cost)*100,2),0.00),'%*') roi75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_90)/sum(rebate_cost)*100,2),0.00),'%') roi90,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT(sum(iap_revenue_90)/sum(rebate_cost)*100,2),0.00),'%*') roi90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_105)/sum(rebate_cost)*100,2),0.00),'%') roi105,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_105)/sum(rebate_cost)*100,2),0.00),'%*') roi105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_120)/sum(rebate_cost)*100,2),0.00),'%') roi120,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_120)/sum(rebate_cost)*100,2),0.00),'%*') roi120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_135)/sum(rebate_cost)*100,2),0.00),'%') roi135,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_135)/sum(rebate_cost)*100,2),0.00),'%*') roi135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_150)/sum(rebate_cost)*100,2),0.00),'%') roi150,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_150)/sum(rebate_cost)*100,2),0.00),'%*') roi150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_165)/sum(rebate_cost)*100,2),0.00),'%') roi165,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_165)/sum(rebate_cost)*100,2),0.00),'%*') roi165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_180)/sum(rebate_cost)*100,2),0.00),'%') roi180,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_180)/sum(rebate_cost)*100,2),0.00),'%*') roi180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_195)/sum(rebate_cost)*100,2),0.00),'%') roi195,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_195)/sum(rebate_cost)*100,2),0.00),'%*') roi195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_210)/sum(rebate_cost)*100,2),0.00),'%') roi210,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_210)/sum(rebate_cost)*100,2),0.00),'%*') roi210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_225)/sum(rebate_cost)*100,2),0.00),'%') roi225,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_225)/sum(rebate_cost)*100,2),0.00),'%*') roi225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_240)/sum(rebate_cost)*100,2),0.00),'%') roi240,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_240)/sum(rebate_cost)*100,2),0.00),'%*') roi240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_255)/sum(rebate_cost)*100,2),0.00),'%') roi255,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_255)/sum(rebate_cost)*100,2),0.00),'%*') roi255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_270)/sum(rebate_cost)*100,2),0.00),'%') roi270,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_270)/sum(rebate_cost)*100,2),0.00),'%*') roi270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_285)/sum(rebate_cost)*100,2),0.00),'%') roi285,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_285)/sum(rebate_cost)*100,2),0.00),'%*') roi285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_300)/sum(rebate_cost)*100,2),0.00),'%') roi300,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_300)/sum(rebate_cost)*100,2),0.00),'%*') roi300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_315)/sum(rebate_cost)*100,2),0.00),'%') roi315,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_315)/sum(rebate_cost)*100,2),0.00),'%*') roi315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_330)/sum(rebate_cost)*100,2),0.00),'%') roi330,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_330)/sum(rebate_cost)*100,2),0.00),'%*') roi330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_345)/sum(rebate_cost)*100,2),0.00),'%') roi345,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_345)/sum(rebate_cost)*100,2),0.00),'%*') roi345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_360)/sum(rebate_cost)*100,2),0.00),'%') roi360,
						</when>
						<otherwise>
						 CONCAT(IFNULL(FORMAT(sum(iap_revenue_360)/sum(rebate_cost)*100,2),0.00),'%*') roi360,
						</otherwise>
					</choose>
				</if>
            </when>
            <otherwise>
                <if test='customizes.contains("addUserLtv0")'>
					round(sum(iap_revenue_1)/100+sum(revenue_1)/100,2) addUserLtv0,
				</if>
                 <if test='customizes.contains("totalIncome")'>
					round(sum(active_user_revenue)/100+sum(active_user_iap_revenue)/100,2) totalIncome,
				</if>
				<if test='customizes.contains("addArpu")'>
					round((sum(iap_revenue_1)/100+sum(revenue_1)/100)/sum(reg_user_cnt),2) addArpu,
				</if>
				<if test='customizes.contains("dauArpu")'>
					round((sum(active_user_iap_revenue)/100+sum(active_user_revenue)/100)/sum(active_user_cnt),2) dauArpu,
				</if>
				<if test='customizes.contains("ltv1")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_2)/100+sum(revenue_2)/100)/sum(reg_user_cnt),2) ltv1,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_2)/100+sum(revenue_2)/100)/sum(reg_user_cnt),2),'*') ltv1,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv2")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_3)/100+sum(revenue_3)/100)/sum(reg_user_cnt),2) ltv2,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_3)/100+sum(revenue_3)/100)/sum(reg_user_cnt),2),'*') ltv2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv3")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_4)/100+sum(revenue_4)/100)/sum(reg_user_cnt),2) ltv3,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_4)/100+sum(revenue_4)/100)/sum(reg_user_cnt),2),'*') ltv3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv4")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_5)/100+sum(revenue_5)/100)/sum(reg_user_cnt),2) ltv4,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_5)/100+sum(revenue_5)/100)/sum(reg_user_cnt),2),'*') ltv4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv5")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_6)/100+sum(revenue_6)/100)/sum(reg_user_cnt),2) ltv5,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_6)/100+sum(revenue_6)/100)/sum(reg_user_cnt),2) ,'*') ltv5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv6")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_7)/100+sum(revenue_7)/100)/sum(reg_user_cnt),2) ltv6,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_7)/100+sum(revenue_7)/100)/sum(reg_user_cnt),2),'*') ltv6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_8)/100+sum(revenue_8)/100)/sum(reg_user_cnt),2) ltv8,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_8)/100+sum(revenue_8)/100)/sum(reg_user_cnt),2),'*') ltv8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_9)/100+sum(revenue_9)/100)/sum(reg_user_cnt),2) ltv9,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_9)/100+sum(revenue_9)/100)/sum(reg_user_cnt),2),'*') ltv9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_10)/100+sum(revenue_10)/100)/sum(reg_user_cnt),2) ltv10,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_10)/100+sum(revenue_10)/100)/sum(reg_user_cnt),2),'*') ltv10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_11)/100+sum(revenue_11)/100)/sum(reg_user_cnt),2) ltv11,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_11)/100+sum(revenue_11)/100)/sum(reg_user_cnt),2),'*') ltv11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_12)/100+sum(revenue_12)/100)/sum(reg_user_cnt),2) ltv12,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_12)/100+sum(revenue_12)/100)/sum(reg_user_cnt),2),'*') ltv12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_13)/100+sum(revenue_13)/100)/sum(reg_user_cnt),2) ltv13,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_13)/100+sum(revenue_13)/100)/sum(reg_user_cnt),2),'*') ltv13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_14)/100+sum(revenue_14)/100)/sum(reg_user_cnt),2) ltv14,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_14)/100+sum(revenue_14)/100)/sum(reg_user_cnt),2),'*') ltv14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_15)/100+sum(revenue_15)/100)/sum(reg_user_cnt),2) ltv15,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_15)/100+sum(revenue_15)/100)/sum(reg_user_cnt),2),'*') ltv15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_16)/100+sum(revenue_16)/100)/sum(reg_user_cnt),2) ltv16,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_16)/100+sum(revenue_16)/100)/sum(reg_user_cnt),2),'*') ltv16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_17)/100+sum(revenue_17)/100)/sum(reg_user_cnt),2) ltv17,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_17)/100+sum(revenue_17)/100)/sum(reg_user_cnt),2),'*') ltv17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_18)/100+sum(revenue_18)/100)/sum(reg_user_cnt),2) ltv18,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_18)/100+sum(revenue_18)/100)/sum(reg_user_cnt),2),'*') ltv18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_19)/100+sum(revenue_19)/100)/sum(reg_user_cnt),2) ltv19,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_19)/100+sum(revenue_19)/100)/sum(reg_user_cnt),2),'*') ltv19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_20)/100+sum(revenue_20)/100)/sum(reg_user_cnt),2) ltv20,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_20)/100+sum(revenue_20)/100)/sum(reg_user_cnt),2),'*') ltv20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_21)/100+sum(revenue_21)/100)/sum(reg_user_cnt),2) ltv21,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_21)/100+sum(revenue_21)/100)/sum(reg_user_cnt),2),'*') ltv21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv22")'>
					<choose>
						<when test=" diffdate &gt;=22 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_22)/100+sum(revenue_22)/100)/sum(reg_user_cnt),2) ltv22,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_22)/100+sum(revenue_22)/100)/sum(reg_user_cnt),2),'*') ltv22,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_23)/100+sum(revenue_23)/100)/sum(reg_user_cnt),2) ltv23,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_23)/100+sum(revenue_23)/100)/sum(reg_user_cnt),2),'*') ltv23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_24)/100+sum(revenue_24)/100)/sum(reg_user_cnt),2) ltv24,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_24)/100+sum(revenue_24)/100)/sum(reg_user_cnt),2),'*') ltv24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_25)/100+sum(revenue_25)/100)/sum(reg_user_cnt),2) ltv25,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_25)/100+sum(revenue_25)/100)/sum(reg_user_cnt),2),'*') ltv25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_26)/100+sum(revenue_26)/100)/sum(reg_user_cnt),2) ltv26,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_26)/100+sum(revenue_26)/100)/sum(reg_user_cnt),2),'*') ltv26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_27)/100+sum(revenue_27)/100)/sum(reg_user_cnt),2) ltv27,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_27)/100+sum(revenue_27)/100)/sum(reg_user_cnt),2),'*') ltv27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_28)/100+sum(revenue_28)/100)/sum(reg_user_cnt),2) ltv28,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_28)/100+sum(revenue_28)/100)/sum(reg_user_cnt),2),'*') ltv28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_29)/100+sum(revenue_29)/100)/sum(reg_user_cnt),2) ltv29,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_29)/100+sum(revenue_29)/100)/sum(reg_user_cnt),2),'*') ltv29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_30)/100+sum(revenue_30)/100)/sum(reg_user_cnt),2) ltv30,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_30)/100+sum(revenue_30)/100)/sum(reg_user_cnt),2),'*') ltv30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_36)/100+sum(revenue_36)/100)/sum(reg_user_cnt),2) ltv36,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_36)/100+sum(revenue_36)/100)/sum(reg_user_cnt),2),'*') ltv36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_42)/100+sum(revenue_42)/100)/sum(reg_user_cnt),2) ltv42,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_42)/100+sum(revenue_42)/100)/sum(reg_user_cnt),2),'*') ltv42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_48)/100+sum(revenue_48)/100)/sum(reg_user_cnt),2) ltv48,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_48)/100+sum(revenue_48)/100)/sum(reg_user_cnt),2),'*') ltv48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_54)/100+sum(revenue_54)/100)/sum(reg_user_cnt),2) ltv54,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_54)/100+sum(revenue_54)/100)/sum(reg_user_cnt),2),'*') ltv54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_60)/100+sum(revenue_60)/100)/sum(reg_user_cnt),2) ltv60,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_60)/100+sum(revenue_60)/100)/sum(reg_user_cnt),2),'*') ltv60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_75)/100+sum(revenue_75)/100)/sum(reg_user_cnt),2) ltv75,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_75)/100+sum(revenue_75)/100)/sum(reg_user_cnt),2),'*') ltv75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_90)/100+sum(revenue_90)/100)/sum(reg_user_cnt),2) ltv90,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_90)/100+sum(revenue_90)/100)/sum(reg_user_cnt),2),'*') ltv90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_105)/100+sum(revenue_105)/100)/sum(reg_user_cnt),2) ltv105,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_105)/100+sum(revenue_105)/100)/sum(reg_user_cnt),2),'*') ltv105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_120)/100+sum(revenue_120)/100)/sum(reg_user_cnt),2) ltv120,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_120)/100+sum(revenue_120)/100)/sum(reg_user_cnt),2),'*') ltv120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_135)/100+sum(revenue_135)/100)/sum(reg_user_cnt),2) ltv135,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_135)/100+sum(revenue_135)/100)/sum(reg_user_cnt),2),'*') ltv135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_150)/100+sum(revenue_150)/100)/sum(reg_user_cnt),2) ltv150,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_150)/100+sum(revenue_150)/100)/sum(reg_user_cnt),2),'*') ltv150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_165)/100+sum(revenue_165)/100)/sum(reg_user_cnt),2) ltv165,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_165)/100+sum(revenue_165)/100)/sum(reg_user_cnt),2),'*') ltv165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_180)/100+sum(revenue_180)/100)/sum(reg_user_cnt),2) ltv180,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_180)/100+sum(revenue_180)/100)/sum(reg_user_cnt),2),'*') ltv180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_195)/100+sum(revenue_195)/100)/sum(reg_user_cnt),2) ltv195,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_195)/100+sum(revenue_195)/100)/sum(reg_user_cnt),2),'*') ltv195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_210)/100+sum(revenue_210)/100)/sum(reg_user_cnt),2) ltv210,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_210)/100+sum(revenue_210)/100)/sum(reg_user_cnt),2),'*') ltv210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_225)/100+sum(revenue_225)/100)/sum(reg_user_cnt),2) ltv225,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_225)/100+sum(revenue_225)/100)/sum(reg_user_cnt),2),'*') ltv225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_240)/100+sum(revenue_240)/100)/sum(reg_user_cnt),2) ltv240,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_240)/100+sum(revenue_240)/100)/sum(reg_user_cnt),2),'*') ltv240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_255)/100+sum(revenue_255)/100)/sum(reg_user_cnt),2) ltv255,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_255)/100+sum(revenue_255)/100)/sum(reg_user_cnt),2),'*') ltv255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_270)/100+sum(revenue_270)/100)/sum(reg_user_cnt),2) ltv270,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_270)/100+sum(revenue_270)/100)/sum(reg_user_cnt),2),'*') ltv270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_285)/100+sum(revenue_285)/100)/sum(reg_user_cnt),2) ltv285,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_285)/100+sum(revenue_285)/100)/sum(reg_user_cnt),2),'*') ltv285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_300)/100+sum(revenue_300)/100)/sum(reg_user_cnt),2) ltv300,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_300)/100+sum(revenue_300)/100)/sum(reg_user_cnt),2),'*') ltv300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_315)/100+sum(revenue_315)/100)/sum(reg_user_cnt),2) ltv315,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_315)/100+sum(revenue_315)/100)/sum(reg_user_cnt),2),'*') ltv315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_330)/100+sum(revenue_330)/100)/sum(reg_user_cnt),2) ltv330,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_330)/100+sum(revenue_330)/100)/sum(reg_user_cnt),2),'*') ltv330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_345)/100+sum(revenue_345)/100)/sum(reg_user_cnt),2) ltv345,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_345)/100+sum(revenue_345)/100)/sum(reg_user_cnt),2),'*') ltv345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("ltv360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
						FORMAT((sum(iap_revenue_360)/100+sum(revenue_360)/100)/sum(reg_user_cnt),2) ltv360,
						</when>
						<otherwise>
						CONCAT(FORMAT((sum(iap_revenue_360)/100+sum(revenue_360)/100)/sum(reg_user_cnt),2),'*') ltv360,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("aRoi")'>
					CONCAT(IFNULL(FORMAT((sum(iap_revenue_1)+sum(revenue_1))/sum(rebate_cost)*100,2),0.00),'%') aRoi,
				</if>
				<if test='customizes.contains("roi2")'>
					<choose>
						<when test=" diffdate &gt;=2 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_2)+sum(revenue_2))/sum(rebate_cost)*100,2),0.00),'%') roi2,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_2)+sum(revenue_2))/sum(rebate_cost)*100,2),0.00),'%*') roi2,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi3")'>
					<choose>
						<when test=" diffdate &gt;=3 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_3)+sum(revenue_3))/sum(rebate_cost)*100,2),0.00),'%') roi3,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_3)+sum(revenue_3))/sum(rebate_cost)*100,2),0.00),'%*') roi3,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi4")'>
					<choose>
						<when test=" diffdate &gt;=4 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_4)+sum(revenue_4))/sum(rebate_cost)*100,2),0.00),'%') roi4,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_4)+sum(revenue_4))/sum(rebate_cost)*100,2),0.00),'%*') roi4,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi5")'>
					<choose>
						<when test=" diffdate &gt;=5 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_5)+sum(revenue_5))/sum(rebate_cost)*100,2),0.00),'%') roi5,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_5)+sum(revenue_5))/sum(rebate_cost)*100,2),0.00),'%*') roi5,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi6")'>
					<choose>
						<when test=" diffdate &gt;=6 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_6)+sum(revenue_6))/sum(rebate_cost)*100,2),0.00),'%') roi6,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_6)+sum(revenue_6))/sum(rebate_cost)*100,2),0.00),'%*') roi6,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi7")'>
					<choose>
						<when test=" diffdate &gt;=7 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_7)+sum(revenue_7))/sum(rebate_cost)*100,2),0.00),'%') roi7,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_7)+sum(revenue_7))/sum(rebate_cost)*100,2),0.00),'%*') roi7,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi8")'>
					<choose>
						<when test=" diffdate &gt;=8 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_8)+sum(revenue_8))/sum(rebate_cost)*100,2),0.00),'%') roi8,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_8)+sum(revenue_8))/sum(rebate_cost)*100,2),0.00),'%*') roi8,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi9")'>
					<choose>
						<when test=" diffdate &gt;=9 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_9)+sum(revenue_9))/sum(rebate_cost)*100,2),0.00),'%') roi9,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_9)+sum(revenue_9))/sum(rebate_cost)*100,2),0.00),'%*') roi9,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi10")'>
					<choose>
						<when test=" diffdate &gt;=10 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_10)+sum(revenue_10))/sum(rebate_cost)*100,2),0.00),'%') roi10,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_10)+sum(revenue_10))/sum(rebate_cost)*100,2),0.00),'%*') roi10,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi11")'>
					<choose>
						<when test=" diffdate &gt;=11 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_11)+sum(revenue_11))/sum(rebate_cost)*100,2),0.00),'%') roi11,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_11)+sum(revenue_11))/sum(rebate_cost)*100,2),0.00),'%*') roi11,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi12")'>
					<choose>
						<when test=" diffdate &gt;=12 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_12)+sum(revenue_12))/sum(rebate_cost)*100,2),0.00),'%') roi12,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_12)+sum(revenue_12))/sum(rebate_cost)*100,2),0.00),'%*') roi12,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi13")'>
					<choose>
						<when test=" diffdate &gt;=13 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_13)+sum(revenue_13))/sum(rebate_cost)*100,2),0.00),'%') roi13,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_13)+sum(revenue_13))/sum(rebate_cost)*100,2),0.00),'%*') roi13,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi14")'>
					<choose>
						<when test=" diffdate &gt;=14 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_14)+sum(revenue_14))/sum(rebate_cost)*100,2),0.00),'%') roi14,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_14)+sum(revenue_14))/sum(rebate_cost)*100,2),0.00),'%*') roi14,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi15")'>
					<choose>
						<when test=" diffdate &gt;=15 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_15)+sum(revenue_15))/sum(rebate_cost)*100,2),0.00),'%') roi15,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_15)+sum(revenue_15))/sum(rebate_cost)*100,2),0.00),'%*') roi15,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi16")'>
					<choose>
						<when test=" diffdate &gt;=16 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_16)+sum(revenue_16))/sum(rebate_cost)*100,2),0.00),'%') roi16,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_16)+sum(revenue_16))/sum(rebate_cost)*100,2),0.00),'%*') roi16,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi17")'>
					<choose>
						<when test=" diffdate &gt;=17 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_17)+sum(revenue_17))/sum(rebate_cost)*100,2),0.00),'%') roi17,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_17)+sum(revenue_17))/sum(rebate_cost)*100,2),0.00),'%*') roi17,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi18")'>
					<choose>
						<when test=" diffdate &gt;=18 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_18)+sum(revenue_18))/sum(rebate_cost)*100,2),0.00),'%') roi18,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_18)+sum(revenue_18))/sum(rebate_cost)*100,2),0.00),'%*') roi18,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi19")'>
					<choose>
						<when test=" diffdate &gt;=19 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_19)+sum(revenue_19))/sum(rebate_cost)*100,2),0.00),'%') roi19,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_19)+sum(revenue_19))/sum(rebate_cost)*100,2),0.00),'%*') roi19,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi20")'>
					<choose>
						<when test=" diffdate &gt;=20 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_20)+sum(revenue_20))/sum(rebate_cost)*100,2),0.00),'%') roi20,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_20)+sum(revenue_20))/sum(rebate_cost)*100,2),0.00),'%*') roi20,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi21")'>
					<choose>
						<when test=" diffdate &gt;=21 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_21)+sum(revenue_21))/sum(rebate_cost)*100,2),0.00),'%') roi21,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_21)+sum(revenue_21))/sum(rebate_cost)*100,2),0.00),'%*') roi21,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi22")'>
					<choose>
						<when test=" diffdate &gt;=22 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_22)+sum(revenue_22))/sum(rebate_cost)*100,2),0.00),'%') roi22,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_22)+sum(revenue_22))/sum(rebate_cost)*100,2),0.00),'%*') roi22,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi23")'>
					<choose>
						<when test=" diffdate &gt;=23 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_23)+sum(revenue_23))/sum(rebate_cost)*100,2),0.00),'%') roi23,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_23)+sum(revenue_23))/sum(rebate_cost)*100,2),0.00),'%*') roi23,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi24")'>
					<choose>
						<when test=" diffdate &gt;=24 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_24)+sum(revenue_24))/sum(rebate_cost)*100,2),0.00),'%') roi24,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_24)+sum(revenue_24))/sum(rebate_cost)*100,2),0.00),'%*') roi24,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi25")'>
					<choose>
						<when test=" diffdate &gt;=25 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_25)+sum(revenue_25))/sum(rebate_cost)*100,2),0.00),'%') roi25,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_25)+sum(revenue_25))/sum(rebate_cost)*100,2),0.00),'%*') roi25,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi26")'>
					<choose>
						<when test=" diffdate &gt;=26 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_26)+sum(revenue_26))/sum(rebate_cost)*100,2),0.00),'%') roi26,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_26)+sum(revenue_26))/sum(rebate_cost)*100,2),0.00),'%*') roi26,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi27")'>
					<choose>
						<when test=" diffdate &gt;=27 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_27)+sum(revenue_27))/sum(rebate_cost)*100,2),0.00),'%') roi27,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_27)+sum(revenue_27))/sum(rebate_cost)*100,2),0.00),'%*') roi27,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi28")'>
					<choose>
						<when test=" diffdate &gt;=28 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_28)+sum(revenue_28))/sum(rebate_cost)*100,2),0.00),'%') roi28,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_28)+sum(revenue_28))/sum(rebate_cost)*100,2),0.00),'%*') roi28,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi29")'>
					<choose>
						<when test=" diffdate &gt;=29 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_29)+sum(revenue_29))/sum(rebate_cost)*100,2),0.00),'%') roi29,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_29)+sum(revenue_29))/sum(rebate_cost)*100,2),0.00),'%*') roi29,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi30")'>
					<choose>
						<when test=" diffdate &gt;=30 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_30)+sum(revenue_30))/sum(rebate_cost)*100,2),0.00),'%') roi30,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_30)+sum(revenue_30))/sum(rebate_cost)*100,2),0.00),'%*') roi30,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi36")'>
					<choose>
						<when test=" diffdate &gt;=36 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_36)+sum(revenue_36))/sum(rebate_cost)*100,2),0.00),'%') roi36,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_36)+sum(revenue_36))/sum(rebate_cost)*100,2),0.00),'%*') roi36,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi42")'>
					<choose>
						<when test=" diffdate &gt;=42 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_42)+sum(revenue_42))/sum(rebate_cost)*100,2),0.00),'%') roi42,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_42)+sum(revenue_42))/sum(rebate_cost)*100,2),0.00),'%*') roi42,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi48")'>
					<choose>
						<when test=" diffdate &gt;=48 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_48)+sum(revenue_48))/sum(rebate_cost)*100,2),0.00),'%') roi48,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_48)+sum(revenue_48))/sum(rebate_cost)*100,2),0.00),'%*') roi48,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi54")'>
					<choose>
						<when test=" diffdate &gt;=54 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_54)+sum(revenue_54))/sum(rebate_cost)*100,2),0.00),'%') roi54,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_54)+sum(revenue_54))/sum(rebate_cost)*100,2),0.00),'%*') roi54,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi60")'>
					<choose>
						<when test=" diffdate &gt;=60 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_60)+sum(revenue_60))/sum(rebate_cost)*100,2),0.00),'%') roi60,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_60)+sum(revenue_60))/sum(rebate_cost)*100,2),0.00),'%*') roi60,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi75")'>
					<choose>
						<when test=" diffdate &gt;=75 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_75)+sum(revenue_75))/sum(rebate_cost)*100,2),0.00),'%') roi75,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_75)+sum(revenue_75))/sum(rebate_cost)*100,2),0.00),'%*') roi75,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi90")'>
					<choose>
						<when test=" diffdate &gt;=90 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_90)+sum(revenue_90))/sum(rebate_cost)*100,2),0.00),'%') roi90,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_90)+sum(revenue_90))/sum(rebate_cost)*100,2),0.00),'%*') roi90,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi105")'>
					<choose>
						<when test=" diffdate &gt;=105 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_105)+sum(revenue_105))/sum(rebate_cost)*100,2),0.00),'%') roi105,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_105)+sum(revenue_105))/sum(rebate_cost)*100,2),0.00),'%*') roi105,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi120")'>
					<choose>
						<when test=" diffdate &gt;=120 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_120)+sum(revenue_120))/sum(rebate_cost)*100,2),0.00),'%') roi120,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_120)+sum(revenue_120))/sum(rebate_cost)*100,2),0.00),'%*') roi120,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi135")'>
					<choose>
						<when test=" diffdate &gt;=135 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_135)+sum(revenue_135))/sum(rebate_cost)*100,2),0.00),'%') roi135,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_135)+sum(revenue_135))/sum(rebate_cost)*100,2),0.00),'%*') roi135,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi150")'>
					<choose>
						<when test=" diffdate &gt;=150 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_150)+sum(revenue_150))/sum(rebate_cost)*100,2),0.00),'%') roi150,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_150)+sum(revenue_150))/sum(rebate_cost)*100,2),0.00),'%*') roi150,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi165")'>
					<choose>
						<when test=" diffdate &gt;=165 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_165)+sum(revenue_165))/sum(rebate_cost)*100,2),0.00),'%') roi165,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_165)+sum(revenue_165))/sum(rebate_cost)*100,2),0.00),'%*') roi165,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi180")'>
					<choose>
						<when test=" diffdate &gt;=180 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_180)+sum(revenue_180))/sum(rebate_cost)*100,2),0.00),'%') roi180,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_180)+sum(revenue_180))/sum(rebate_cost)*100,2),0.00),'%*') roi180,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi195")'>
					<choose>
						<when test=" diffdate &gt;=195 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_195)+sum(revenue_195))/sum(rebate_cost)*100,2),0.00),'%') roi195,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_195)+sum(revenue_195))/sum(rebate_cost)*100,2),0.00),'%*') roi195,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi210")'>
					<choose>
						<when test=" diffdate &gt;=210 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_210)+sum(revenue_210))/sum(rebate_cost)*100,2),0.00),'%') roi210,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_210)+sum(revenue_210))/sum(rebate_cost)*100,2),0.00),'%*') roi210,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi225")'>
					<choose>
						<when test=" diffdate &gt;=225 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_225)+sum(revenue_225))/sum(rebate_cost)*100,2),0.00),'%') roi225,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_225)+sum(revenue_225))/sum(rebate_cost)*100,2),0.00),'%*') roi225,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi240")'>
					<choose>
						<when test=" diffdate &gt;=240 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_240)+sum(revenue_240))/sum(rebate_cost)*100,2),0.00),'%') roi240,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_240)+sum(revenue_240))/sum(rebate_cost)*100,2),0.00),'%*') roi240,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi255")'>
					<choose>
						<when test=" diffdate &gt;=255 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_255)+sum(revenue_255))/sum(rebate_cost)*100,2),0.00),'%') roi255,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_255)+sum(revenue_255))/sum(rebate_cost)*100,2),0.00),'%*') roi255,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi270")'>
					<choose>
						<when test=" diffdate &gt;=270 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_270)+sum(revenue_270))/sum(rebate_cost)*100,2),0.00),'%') roi270,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_270)+sum(revenue_270))/sum(rebate_cost)*100,2),0.00),'%*') roi270,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi285")'>
					<choose>
						<when test=" diffdate &gt;=285 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_285)+sum(revenue_285))/sum(rebate_cost)*100,2),0.00),'%') roi285,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_285)+sum(revenue_285))/sum(rebate_cost)*100,2),0.00),'%*') roi285,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi300")'>
					<choose>
						<when test=" diffdate &gt;=300 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_300)+sum(revenue_300))/sum(rebate_cost)*100,2),0.00),'%') roi300,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_300)+sum(revenue_300))/sum(rebate_cost)*100,2),0.00),'%*') roi300,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi315")'>
					<choose>
						<when test=" diffdate &gt;=315 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_315)+sum(revenue_315))/sum(rebate_cost)*100,2),0.00),'%') roi315,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_315)+sum(revenue_315))/sum(rebate_cost)*100,2),0.00),'%*') roi315,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi330")'>
					<choose>
						<when test=" diffdate &gt;=330 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_330)+sum(revenue_330))/sum(rebate_cost)*100,2),0.00),'%') roi330,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_330)+sum(revenue_330))/sum(rebate_cost)*100,2),0.00),'%*') roi330,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi345")'>
					<choose>
						<when test=" diffdate &gt;=345 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_345)+sum(revenue_345))/sum(rebate_cost)*100,2),0.00),'%') roi345,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_345)+sum(revenue_345))/sum(rebate_cost)*100,2),0.00),'%*') roi345,
						</otherwise>
					</choose>
				</if>
				<if test='customizes.contains("roi360")'>
					<choose>
						<when test=" diffdate &gt;=360 or group.contains('day') or group.contains('week') or group.contains('month')">
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_360)+sum(revenue_360))/sum(rebate_cost)*100,2),0.00),'%') roi360,
						</when>
						<otherwise>
						CONCAT(IFNULL(FORMAT((sum(iap_revenue_360)+sum(revenue_360))/sum(rebate_cost)*100,2),0.00),'%*') roi360,
						</otherwise>
					</choose>
				</if>
            </otherwise>
        </choose>
       	appid appId from  ads_wechat_game_roi  where  tdate BETWEEN #{beginDate} AND #{endDate} 
		<if test="appId != null and appId != ''">
			and appid in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and ad_buy_media in (${media}) 
		</if>
		<if test="channel != null and channel != ''">
			and ad_buy_channel in (${channel}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account in (${accountId}) 
		</if>
		<if test="putUser != null and putUser != ''">
			and put_user in (${putUser}) 
		</if>
		<if test="campaignId != null and campaignId != ''">
			and campaign_id in (${campaignId}) 
		</if>
		<if test="campaignName != null and campaignName != ''">
			and campaign_name = #{campaignName} 
		</if>
		<if test="groupName != null and groupName != ''">
			and group_name = #{groupName} 
		</if>
		<if test="company != null and company != ''">
			and company in (${company}) 
		</if>
		<if test="adsensePosition != null and adsensePosition != ''">
			and adsensePosition in (${adsensePosition}) 
		</if>
		<if test="strategy != null and strategy != ''">
			and transfer_type in (${strategy}) 
		</if>
		<if test="accountType != null and accountType != ''">
			and account_type in (${accountType}) 
		</if>
		<if test="delivery_mode != null and delivery_mode != ''">
			and delivery_mode = #{delivery_mode} 
		</if>
		<if test="anchor_related_type != null and anchor_related_type != ''">
			and anchor_related_type = #{anchor_related_type} 
		</if>
		<if test="scope != null and scope != ''">
          <if test="index == 'spend'">
              and rebate_cost/100 <![CDATA[ ${symbol} ]]> #{number}
          </if>
        </if>
	</sql>
</mapper>