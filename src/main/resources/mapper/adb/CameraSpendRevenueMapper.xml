<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.CameraSpendRevenueMapper">

	<select id="queryList" resultType="java.util.Map">
		SELECT
		CAST(ROUND( sum( rebate_spend ), 2 ) AS DECIMAL(10, 2)) rebate_spend,
		CAST(ROUND( sum( total_revenue ), 2 ) AS DECIMAL(10, 2)) total_revenue,
		CAST(ROUND( sum( first_revenue ), 2 ) AS DECIMAL(10, 2)) first_revenue,
		CAST(ROUND( sum( renew_revenue ), 2 ) AS DECIMAL(10, 2)) renew_revenue,
		CAST(ROUND( sum( sub_sign_revenue ), 2 ) AS DECIMAL(10, 2)) sub_sign_revenue,
		CAST(ROUND( sum( single_revenue ), 2 ) AS DECIMAL(10, 2)) single_revenue,
		CAST(ROUND( sum( profix ), 2 ) AS DECIMAL(10, 2)) profix,
		CAST(ROUND( sum( renew_revenue ) / sum( total_revenue ) * 100, 2 ) AS DECIMAL(10, 2)) renew_revenue_rate,
		CAST(ROUND( sum( profix ) / sum( total_revenue ) * 100, 2 ) AS DECIMAL(10, 2)) profix_rate
		<if test="group != null and group != ''">
			,
			<if test="group.contains('appid')">
				appid,
				app,
			</if>
			<if test="group.contains('user_type')">
				user_type,
			</if>
			<choose>
				<when test="custom_date != null and custom_date != ''">
					concat(#{start_date},'至',#{end_date}) as tdate,
				</when>
				<when test="group != null and group.contains('week')">
					DATE_FORMAT(tdate, '%x-%v') as tdate,
				</when>
				<when test="group != null and group.contains('month')">
					DATE_FORMAT(tdate,'%Y-%m') as tdate,
				</when>
				<when test="group != null and group.contains('beek')">
					CONCAT(DATE_FORMAT(tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS tdate,
				</when>
				<otherwise>
					tdate,
				</otherwise>
			</choose>
			tag
		</if>

		FROM
		(
		SELECT
		a.*,
		b.app_name app
		FROM
		(
		SELECT
		tdate,
		appid,
		user_type user_type,
		rebate_spend rebate_spend,
		( sub_sign_revenue + sub_not_sign_revenue + renew_revenue + single_revenue ) total_revenue,
		'0' AS first_revenue,
		renew_revenue renew_revenue,
		sub_sign_revenue sub_sign_revenue,
		single_revenue single_revenue,
		( ( sub_sign_revenue + sub_not_sign_revenue + renew_revenue + single_revenue ) - rebate_spend ) profix,
		'国内' tag
		FROM
		ads_android_camera_series_spend_and_revenue_summary
		) a
		JOIN ( SELECT id, app_name FROM app_info WHERE app_category = 48 ) b ON a.appid = b.id UNION ALL
		SELECT
		a.*,
		b.app_name app
		FROM
		(
		SELECT
		tdate,
		app_id,
		user_type user_type,
		rebate_spend rebate_spend,
		total_revenue * 0.85 total_revenue,
		revenue * 0.85 first_revenue,
		sub_revenue * 0.85 renew_revenue,
		'0' AS sub_sign_revenue,
		single_revenue,
		( total_revenue * 0.85 - rebate_spend ) profix,
		tag
		FROM
		ads_leying_spend_and_revenue_summary
		WHERE
		tdate <![CDATA[<=]]> '2024-07-12'
		) a
		JOIN ( SELECT id, app_name FROM app_info WHERE app_category IN ( 3, 15, 18, 19, 20, 48 ) ) b ON a.app_id = b.id UNION ALL
		SELECT
		a.*,
		b.app_name app
		FROM
		(
		SELECT
		tdate,
		app_id,
		user_type user_type,
		rebate_spend rebate_spend,
		total_revenue total_revenue,
		revenue first_revenue,
		sub_revenue renew_revenue,
		'0' AS sub_sign_revenue,
		single_revenue,
		( total_revenue - rebate_spend ) profix,
		tag
		FROM
		ads_leying_spend_and_revenue_summary
		WHERE
		tdate <![CDATA[>=]]> '2024-07-13'
		) a
		JOIN (
		SELECT
		id,
		app_name
		FROM
		app_info
		WHERE
		app_category IN ( 3, 15, 18, 19, 20, 48 )
		AND os_type = '2'
		) b ON a.app_id = b.id
		) t
		<where>
			t.tdate between #{start_date} AND #{end_date}
			<if test="appid != null and appid != ''">
				AND t.appid in (${appid})
			</if>
			<if test="user_type != null and user_type != ''">
				AND t.user_type IN (${user_type})
			</if>
			<if test="tag != null and tag != ''">
				AND tag IN (${tag})
			</if>
		</where>
		<if test="group != null and group != ''">
			GROUP BY
			<if test="group != null and group != ''">
				<if test="group.contains('appid')">
					appid,
				</if>
				<if test="group.contains('user_type')">
					user_type,
				</if>
				<choose>
					<when test="custom_date != null and custom_date != ''">
						concat(#{start_date},'至',#{end_date}),
					</when>
					<when test="group != null and group.contains('week')">
						DATE_FORMAT(tdate, '%x-%v'),
					</when>
					<when test="group != null and group.contains('month')">
						DATE_FORMAT(tdate,'%Y-%m'),
					</when>
					<when test="group != null and group.contains('beek')">
						CONCAT(DATE_FORMAT(tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0")),
					</when>
					<otherwise>
						tdate,
					</otherwise>
				</choose>
				tag
			</if>

		</if>
		<if test="order_str != null and order_str != ''">
			ORDER BY ${order_str}
		</if>
	</select>

	<select id="queryTotal" resultType="java.util.Map">
		SELECT
		CAST(ROUND( sum( rebate_spend ), 2 ) AS DECIMAL(10, 2)) rebate_spend,
		CAST(ROUND( sum( total_revenue ), 2 ) AS DECIMAL(10, 2)) total_revenue,
		CAST(ROUND( sum( first_revenue ), 2 ) AS DECIMAL(10, 2)) first_revenue,
		CAST(ROUND( sum( renew_revenue ), 2 ) AS DECIMAL(10, 2)) renew_revenue,
		CAST(ROUND( sum( sub_sign_revenue ), 2 ) AS DECIMAL(10, 2)) sub_sign_revenue,
		CAST(ROUND( sum( single_revenue ), 2 ) AS DECIMAL(10, 2)) single_revenue,
		CAST(ROUND( sum( profix ), 2 ) AS DECIMAL(10, 2)) profix,
		CAST(ROUND( sum( renew_revenue ) / sum( total_revenue ) * 100, 2 ) AS DECIMAL(10, 2)) renew_revenue_rate,
		CAST(ROUND( sum( profix ) / sum( total_revenue ) * 100, 2 ) AS DECIMAL(10, 2)) profix_rate
		FROM
		(
		SELECT
		a.*,
		b.app_name app
		FROM
		(
		SELECT
		tdate,
		appid,
		user_type user_type,
		rebate_spend rebate_spend,
		( sub_sign_revenue + sub_not_sign_revenue + renew_revenue + single_revenue ) total_revenue,
		'0' AS first_revenue,
		renew_revenue renew_revenue,
		sub_sign_revenue sub_sign_revenue,
		single_revenue single_revenue,
		( ( sub_sign_revenue + sub_not_sign_revenue + renew_revenue + single_revenue ) - rebate_spend ) profix,
		'国内' tag
		FROM
		ads_android_camera_series_spend_and_revenue_summary
		) a
		JOIN ( SELECT id, app_name FROM app_info WHERE app_category = 48 ) b ON a.appid = b.id UNION ALL
		SELECT
		a.*,
		b.app_name app
		FROM
		(
		SELECT
		tdate,
		app_id,
		user_type user_type,
		rebate_spend rebate_spend,
		total_revenue * 0.85 total_revenue,
		revenue * 0.85 first_revenue,
		sub_revenue * 0.85 renew_revenue,
		'0' AS sub_sign_revenue,
		single_revenue,
		( total_revenue * 0.85- rebate_spend ) profix,
		tag
		FROM
		ads_leying_spend_and_revenue_summary
		WHERE
		tdate <![CDATA[<=]]> '2024-07-12'
		) a
		JOIN ( SELECT id, app_name FROM app_info WHERE app_category IN ( 3, 15, 18, 19, 20, 48 ) ) b ON a.app_id = b.id UNION ALL
		SELECT
		a.*,
		b.app_name app
		FROM
		(
		SELECT
		tdate,
		app_id,
		user_type user_type,
		rebate_spend rebate_spend,
		total_revenue total_revenue,
		revenue first_revenue,
		sub_revenue renew_revenue,
		'0' AS sub_sign_revenue,
		single_revenue,
		( total_revenue - rebate_spend ) profix,
		tag
		FROM
		ads_leying_spend_and_revenue_summary
		WHERE
		tdate <![CDATA[>=]]> '2024-07-13'
		) a
		JOIN (
		SELECT
		id,
		app_name
		FROM
		app_info
		WHERE
		app_category IN ( 3, 15, 18, 19, 20, 48 )
		AND os_type = '2'
		) b ON a.app_id = b.id
		) t
		<where>
			t.tdate between #{start_date} AND #{end_date}
			<if test="appid != null and appid != ''">
				AND t.appid in (${appid})
			</if>
			<if test="user_type != null and user_type != ''">
				AND user_type IN (${user_type})
			</if>
			<if test="tag != null and tag != ''">
				AND tag IN (${tag})
			</if>
		</where>
	</select>

</mapper>
