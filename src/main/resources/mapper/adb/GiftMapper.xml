<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.GiftMapper">
	<select id="getGiftInfoList" resultType="com.wbgame.pojo.game.GiftInfoVo" parameterType="com.wbgame.pojo.game.GiftInfoVo">
		select *
		from dnwx_bi.ads_gift_info where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="gift_item_type != null and gift_item_type != ''">
			and gift_item_type = #{gift_item_type}
		</if>
		<if test="gift_item_id != null and gift_item_id != ''">
			and gift_item_id like '%${gift_item_id}%'
		</if>
		<if test="id != null and id != ''">
			and id  in (${id})
		</if>
		order by create_time desc
	</select>
	
	<!-- 通过产品ID查询该产品下所有礼包的金额 -->
	<select id="getGiftPriceByAppid" resultType="java.lang.Integer" parameterType="java.lang.Integer">
		SELECT t.p FROM(SELECT DISTINCT(TRIM(price)) p FROM ads_gift_info WHERE appid=#{appid} GROUP BY price) t ORDER BY CAST(t.p AS signed) ASC 
	</select>

		<!-- 通过产品ID查询该产品下所有礼包的id -->
	<select id="getGiftInfoByAppid" resultType="java.util.Map" parameterType="java.util.Map" >
		SELECT gift_item_id,gift_item_name FROM (
			SELECT gift_item_name,gift_item_id ,price  FROM ads_gift_info WHERE  1=1
			<if test="appid != null">
				and appid=#{appid}
			</if>
			 GROUP BY gift_item_id,gift_item_name
		 ) ORDER BY CAST(price AS signed) ASC;

	</select>

	<insert id="saveGiftInfo" parameterType="com.wbgame.pojo.game.GiftInfoVo">
		replace into dnwx_bi.ads_gift_info (appid,gift_item_id,gift_item_name,gift_item_content,gift_item_type,gift_buy_type,
		currency_type,price,online_time,offline_time,gift_period,gift_activity_id,create_user,create_time,check_money) values
		(#{appid},#{gift_item_id},#{gift_item_name},#{gift_item_content},#{gift_item_type},#{gift_buy_type},
		#{currency_type},#{price},#{online_time},#{offline_time},#{gift_period},#{gift_activity_id},#{create_user},#{create_time},#{check_money})
	</insert>

	<insert id="batchSaveGiftInfo" parameterType="java.util.List">
		replace into dnwx_bi.ads_gift_info (appid,gift_item_id,gift_item_name,gift_item_content,gift_item_type,gift_buy_type,
		currency_type,price,online_time,offline_time,gift_period,gift_activity_id,create_user,create_time,check_money) values
		<foreach collection="list"  index="index" item="item" separator=",">
			(#{item.appid}, #{item.gift_item_id},#{item.gift_item_name},#{item.gift_item_content},#{item.gift_item_type},#{item.gift_buy_type},
			#{item.currency_type},#{item.price},#{item.online_time},#{item.offline_time},#{item.gift_period},#{item.gift_activity_id},#{item.create_user},#{item.create_time},#{item.check_money})
		</foreach>
	</insert>

	<insert id="updateGiftInfo" parameterType="com.wbgame.pojo.game.GiftInfoVo">
		replace into dnwx_bi.ads_gift_info (appid,gift_item_id,gift_item_name,gift_item_content,gift_item_type,gift_buy_type,
		currency_type,price,online_time,offline_time,gift_period,gift_activity_id,create_user,create_time,modify_user,modify_time,check_money) values
		(#{appid},#{gift_item_id},#{gift_item_name},#{gift_item_content},#{gift_item_type},#{gift_buy_type},
		#{currency_type},#{price},#{online_time},#{offline_time},#{gift_period},#{gift_activity_id},#{create_user},#{create_time},#{modify_user},#{modify_time},#{check_money})
	</insert>

	<delete id="delGiftInfo" parameterType="com.wbgame.pojo.game.GiftInfoVo">
		delete from dnwx_bi.ads_gift_info where id in (${id})
	</delete>

	<sql id="group_id_sql">
	   ,case when group_id=1 then '新用户'
	   when <![CDATA[ group_id>=2 and group_id<=7 ]]> then '轻度用户'
	   when <![CDATA[group_id>=8 and group_id<=14 ]]> then '中度用户'
	   when <![CDATA[group_id>=15 and group_id<=30 ]]> then '重度用户'
	   when <![CDATA[group_id>=31 and group_id<=45 ]]> then '超重度用户'
	   when <![CDATA[group_id>=46 and group_id<=60 ]]> then '轻硬核用户'
	   when <![CDATA[group_id>=61 and group_id<=90 ]]> then '硬核用户'
	   when <![CDATA[group_id>=91 and group_id<=120 ]]> then '超硬核用户'
	   when <![CDATA[group_id>=121 ]]> then '骨灰级用户'
	   else group_id end as group_id
	</sql>

	<select id="getGiftAnalysisList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.GiftAnalysisVo">
		select
		<if test="buy_date_group != null and buy_date_group != ''">
			buy_date,
		</if>
		<if test="appid_group != null and appid_group != ''">
			a.appid,
		</if>
		<if test="pid_group != null and pid_group != ''">
			a.pid,
		</if>
		<if test="download_channel_group != null and download_channel_group != ''">
			a.download_channel,
		</if>
		<if test="gift_item_id_group != null and gift_item_id_group != ''">
			a.gift_item_id,
			gift_buy_type,
			gift_item_name,
			ROUND((b.price)/100,2) price,
			currency_type,
			gift_item_content,
		</if>
		<if test="gift_item_type_group != null and gift_item_type_group != ''">
			b.gift_item_type,
		</if>
		<if test="tag_r_group != null and tag_r_group != ''">
			a.tag_r,
		</if>
		<if test="group_id_group != null and group_id_group != ''">
			a.group_id,
		</if>

		ROUND(SUM(token_amount)/100,2) sale_sum,
		ROUND(SUM(if(is_new_user=1,token_amount,0))/100,2) sale_sum_new,
		ROUND(SUM(if(is_new_user=0,token_amount,0))/100,2) sale_sum_old,
		SUM(total_num) sale_count,
		COUNT(DISTINCT(android_id)) buy_count,
		SUM(if(is_new_user=1,total_num,0)) sale_count_new,
		COUNT(DISTINCT android_id,if(is_new_user=1,true,null)) buy_count_new,
		SUM(if(is_new_user=0,total_num,0)) sale_count_old,
		COUNT(DISTINCT android_id,if(is_new_user=0,true,null)) buy_count_old,
		COUNT(DISTINCT android_id,if(is_first_buy=1,true,null)) first_buy_count,
		COUNT(DISTINCT android_id,CASE WHEN is_new_user=0 AND is_first_buy=1 THEN  true  ELSE null END)first_buy_count_old,
		sum(add_user_count) new_user_count,sum(active_user_count) active_user_count
		from (
		select appid,pid,download_channel,android_id,level_id,gift_item_id,token_type,buy_time,token_amount,total_num,is_new_user,is_first_buy,buy_date,tag_r
		<include refid="group_id_sql"/>
		from dnwx_bi.ads_gift_analyse_daily where 1=1 AND buy_date <![CDATA[ >= ]]> #{start_date} and buy_date <![CDATA[ <= ]]> #{end_date}
		union all
		select appid,pid,download_channel,android_id,level_id,gift_item_id,token_type,buy_time,token_amount,total_num,is_new_user,is_first_buy,buy_date,tag_r
		<include refid="group_id_sql"/>
		from dnwx_bi.ads_gift_analyse_xyx_daily where 1=1 AND buy_date <![CDATA[ >= ]]> #{start_date} and buy_date <![CDATA[ <= ]]> #{end_date}
		) a
		left join  dnwx_bi.ads_active_stats_daily
		 c on a.buy_date =c.tdate and a.appid =c.appid and a.pid =c.pid and a.download_channel = c.download_channel
		left join dnwx_bi.ads_gift_info b on a.gift_item_id = b.gift_item_id and a.appid =b.appid
		where  1=1
		<if test="appid != null and appid != ''">
			and a.appid in (${appid})
		</if>
		<if test="download_channel != null and download_channel != ''">
			and a.download_channel in (${download_channel})
		</if>
		<if test="gift_item_type != null and gift_item_type != ''">
			and gift_item_type = #{gift_item_type}
		</if>
		<if test="gift_item_id != null and gift_item_id != ''">
			and a.gift_item_id = #{gift_item_id}
		</if>
		<if test="gift_item_name != null and gift_item_name != ''">
			and b.gift_item_name REGEXP #{gift_item_name}
		</if>
		<if test="gift_buy_type != null and gift_buy_type != ''">
			and b.gift_buy_type = #{gift_buy_type}
		</if>
		<if test="pid != null and pid != ''">
			and a.pid = #{pid}
		</if>
		<if test="tag_r != null and tag_r != ''">
			and a.tag_r in (${tag_r})
		</if>
		<if test="group_id != null and group_id != ''">
			and a.group_id in (${group_id})
		</if>
		AND buy_date <![CDATA[ >= ]]> #{start_date} and buy_date <![CDATA[ <= ]]> #{end_date}
		AND b.currency_type = #{currency_type}
		<if test="group != null and group != ''">
			group by ${group}
		</if>

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by buy_date asc
			</otherwise>
		</choose>
	</select>
	<select id="getLineChart" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT g.buy_date date,FORMAT(g.buy_count_new/a.add_user_count*100,2)rate ,
		FORMAT(g.buy_count_old/(a.active_user_count-a.add_user_count)*100,2) old_user_rate ,
		FORMAT((g.buy_count_new+g.buy_count_old)/a.active_user_count*100,2) total_rate
		FROM
		(
		select a.buy_date,a.appid,COUNT(DISTINCT android_id,if(is_new_user=1,true,null)) buy_count_new,
		COUNT(DISTINCT android_id,if(is_new_user=0,true,null)) buy_count_old    
		from (
		select appid,pid,download_channel,android_id,is_new_user,gift_item_id,buy_date,tag_r <include refid="group_id_sql"/>
		from dnwx_bi.ads_gift_analyse_daily where 1=1 AND buy_date <![CDATA[ >= ]]> #{start_date} and buy_date <![CDATA[ <= ]]> #{end_date}
		union all
		select appid,pid,download_channel,android_id,is_new_user,gift_item_id,buy_date,tag_r <include refid="group_id_sql"/>
		from dnwx_bi.ads_gift_analyse_xyx_daily where 1=1 AND buy_date <![CDATA[ >= ]]> #{start_date} and buy_date <![CDATA[ <= ]]> #{end_date}
		) a 
		left join dnwx_bi.ads_gift_info b on a.gift_item_id = b.gift_item_id and a.appid =b.appid
		where  a.appid in (${appid})  AND buy_date BETWEEN #{start_date} and  #{end_date}  AND b.currency_type = #{currency_type}
		<if test="download_channel != null and download_channel != ''">
			and a.download_channel in (${download_channel})
		</if>
		<if test="gift_item_type != null and gift_item_type != ''">
			and gift_item_type = #{gift_item_type}
		</if>
		<if test="gift_item_id != null and gift_item_id != ''">
			and a.gift_item_id = #{gift_item_id}
		</if>
		<if test="gift_item_name != null and gift_item_name != ''">
			and b.gift_item_name REGEXP #{gift_item_name}
		</if>
		<if test="gift_buy_type != null and gift_buy_type != ''">
			and b.gift_buy_type = #{gift_buy_type}
		</if>
		<if test="pid != null and pid != ''">
			and a.pid = #{pid}
		</if>
		<if test="tag_r != null and tag_r != ''">
			and a.tag_r in (${tag_r})
		</if>
		<if test="group_id != null and group_id != ''">
			and a.group_id in (${group_id})
		</if>
		GROUP BY a.buy_date 
		) g
		LEFT JOIN
		( 
			SELECT tdate,appid,SUM(active_user_cnt) active_user_count,SUM(add_user_cnt) add_user_count FROM ads_user_iap_revenue_info_daily WHERE tdate BETWEEN #{start_date} and  #{end_date} and appid in (${appid})
				GROUP BY tdate
		) a ON g.buy_date=a.tdate order by  g.buy_date asc
	</select>
	
	<select id="getGiftAnalysisSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.GiftAnalysisVo">

		select sum(aa.sale_sum) sale_sum,sum(aa.sale_count) sale_count,
		sum(aa.sale_sum_new) sale_sum_new,
		sum(aa.sale_sum_old) sale_sum_old,
		sum(aa.sale_count_new) sale_count_new,sum(aa.sale_count_old) sale_count_old,
		sum(aa.buy_count) buy_count,sum(aa.buy_count_new) buy_count_new,sum(aa.buy_count_old) buy_count_old,
		sum(aa.first_buy_count) first_buy_count,sum(aa.first_buy_count_old) first_buy_count_old,
		sum(aa.new_user_count) new_user_count,sum(aa.active_user_count) active_user_count
		from (
		SELECT
		ROUND(SUM(token_amount)/100,2) sale_sum,
		ROUND(SUM(if(is_new_user=1,token_amount,0))/100,2) sale_sum_new,
		ROUND(SUM(if(is_new_user=0,token_amount,0))/100,2) sale_sum_old,
		SUM(total_num) sale_count,
		COUNT(DISTINCT(android_id)) buy_count,
		SUM(if(is_new_user=1,total_num,0)) sale_count_new,
		COUNT(DISTINCT android_id,if(is_new_user=1,true,null)) buy_count_new,
		SUM(if(is_new_user=0,total_num,0)) sale_count_old,
		COUNT(DISTINCT android_id,if(is_new_user=0,true,null)) buy_count_old,
		COUNT(DISTINCT android_id,if(is_first_buy=1,true,null)) first_buy_count,
		COUNT(DISTINCT android_id,CASE WHEN is_new_user=0 AND is_first_buy=1 THEN  true  ELSE null END)first_buy_count_old,
		sum(add_user_count) new_user_count,sum(active_user_count) active_user_count
		from (
		select appid,pid,download_channel,android_id,level_id,gift_item_id,token_type,buy_time,token_amount,total_num,is_new_user,is_first_buy,buy_date
		,tag_r <include refid="group_id_sql"/>
		from dnwx_bi.ads_gift_analyse_daily where 1=1 AND buy_date <![CDATA[ >= ]]> #{start_date} and buy_date <![CDATA[ <= ]]> #{end_date}
		union all
		select appid,pid,download_channel,android_id,level_id,gift_item_id,token_type,buy_time,token_amount,total_num,is_new_user,is_first_buy,buy_date
		,tag_r <include refid="group_id_sql"/>
		from dnwx_bi.ads_gift_analyse_xyx_daily where 1=1 AND buy_date <![CDATA[ >= ]]> #{start_date} and buy_date <![CDATA[ <= ]]> #{end_date}
		) a
		left join  dnwx_bi.ads_active_stats_daily c on a.buy_date =c.tdate and a.appid =c.appid and a.pid =c.pid and a.download_channel = c.download_channel
		left join dnwx_bi.ads_gift_info b on a.gift_item_id = b.gift_item_id and a.appid =b.appid
		where  1=1
		<if test="appid != null and appid != ''">
			and a.appid in (${appid})
		</if>
		<if test="download_channel != null and download_channel != ''">
			and a.download_channel in (${download_channel})
		</if>
		<if test="gift_item_type != null and gift_item_type != ''">
			and gift_item_type = #{gift_item_type}
		</if>
		<if test="gift_item_id != null and gift_item_id != ''">
			and a.gift_item_id = #{gift_item_id}
		</if>
		<if test="gift_item_name != null and gift_item_name != ''">
			and b.gift_item_name REGEXP #{gift_item_name}
		</if>
		<if test="gift_buy_type != null and gift_buy_type != ''">
			and b.gift_buy_type = #{gift_buy_type}
		</if>
		<if test="pid != null and pid != ''">
			and a.pid = #{pid}
		</if>
		<if test="tag_r != null and tag_r != ''">
			and a.tag_r in (${tag_r})
		</if>
		<if test="group_id != null and group_id != ''">
			and a.group_id in (${group_id})
		</if>
		AND buy_date <![CDATA[ >= ]]> #{start_date} and buy_date <![CDATA[ <= ]]> #{end_date}
		AND b.currency_type = #{currency_type}
		) aa
	</select>

	<select id="getGiftAnalysisUserList" resultType="com.wbgame.pojo.game.GiftAnalysisVo" parameterType="java.util.Map">
		select
		<if test="buy_date_group != null and buy_date_group != ''">
			tdate buy_date,
		</if>
		<if test="appid_group != null and appid_group != ''">
			appid,
		</if>
		<if test="pid_group != null and pid_group != ''">
			pid,
		</if>
		<if test="download_channel_group != null and download_channel_group != ''">
			download_channel,
		</if>
		sum(add_user_count) new_user_count,sum(active_user_count) active_user_count
		from dnwx_bi.ads_active_stats_daily  where  1=1
		AND tdate <![CDATA[ >= ]]> #{start_date} and tdate <![CDATA[ <= ]]> #{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="download_channel != null and download_channel != ''">
			and download_channel in (${download_channel})
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="userNumGroup != null and userNumGroup != ''">
			group by ${group}
		</if>
	</select>


	<select id="getGiftFirstPayList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.GiftFirstPayVo">
		select
		<if test="create_date_group != null and create_date_group != ''">
			create_date,
		</if>
		<if test="appid_group != null and appid_group != ''">
			a.appid,
		</if>
		<if test="pid_group != null and pid_group != ''">
			a.pid,
		</if>
		<if test="download_channel_group != null and download_channel_group != ''">
			a.download_channel,
		</if>
		<if test="level_id_group != null and level_id_group != ''">
			a.level_id,
		</if>
		<if test="gift_id_group != null and gift_id_group != ''">
			a.gift_id,
			gift_item_name,
			gift_item_type,
		</if>
			COUNT(DISTINCT android_id) total_pay_num,
			COUNT(DISTINCT android_id,if(first_charge=1,true,null)) first_pay_total_num,
			COUNT(DISTINCT android_id,CASE WHEN first_charge=1 AND flag_new_user=1 THEN  true  ELSE null END) first_pay_new_num,
			COUNT(DISTINCT android_id,CASE WHEN first_charge=1 AND flag_new_user=0 THEN  true  ELSE null END) first_pay_old_num,
			CONCAT(TRUNCATE((COUNT(DISTINCT android_id,if(first_charge=1,true,null))/COUNT(DISTINCT android_id))*100,2),'%') first_pay_level_ratio
			from dnwx_bi.ads_order_analysis_daily a
			left join dnwx_bi.ads_gift_info b on a.gift_id = b.gift_item_id and a.appid =b.appid  where 1=1
			AND create_date <![CDATA[ >= ]]> #{start_date} and create_date <![CDATA[ <= ]]> #{end_date}
		<if test="appid != null and appid != ''">
			and a.appid in (${appid})
		</if>
		<if test="download_channel != null and download_channel != ''">
			and a.download_channel in (${download_channel})
		</if>
		<if test="pid != null and pid != ''">
			and a.pid = #{pid}
		</if>
		<if test="gift_id != null and gift_id != ''">
			and a.gift_id = #{gift_id}
		</if>
		<if test="level_id != null and level_id != ''">
			and a.level_id = #{level_id}
		</if>
		<if test="group != null and group != ''">
			group by ${group}
		</if>

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by create_date asc ,level_id desc
			</otherwise>
		</choose>
	</select>

	<select id="getGiftFirstPaySum" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.GiftFirstPayVo">

		select sum(aa.total_pay_num) total_pay_num,sum(aa.first_pay_total_num) first_pay_total_num ,
			sum(aa.first_pay_new_num) first_pay_new_num,sum(aa.first_pay_old_num) first_pay_old_num,
			CONCAT(TRUNCATE((sum(aa.first_pay_total_num)/sum(aa.total_pay_num)*100),2),'%') first_pay_level_ratio
		FROM (
			select
			COUNT(DISTINCT android_id) total_pay_num,
			COUNT(DISTINCT android_id,if(first_charge=1,true,null)) first_pay_total_num,
			COUNT(DISTINCT android_id,CASE WHEN first_charge=1 AND flag_new_user=1 THEN  true  ELSE null END) first_pay_new_num,
			COUNT(DISTINCT android_id,CASE WHEN first_charge=1 AND flag_new_user=0 THEN  true  ELSE null END) first_pay_old_num
			from dnwx_bi.ads_order_analysis_daily a
			left join dnwx_bi.ads_gift_info b on a.gift_id = b.gift_item_id and a.appid =b.appid  where 1=1
			AND create_date <![CDATA[ >= ]]> #{start_date} and create_date <![CDATA[ <= ]]> #{end_date}
		<if test="appid != null and appid != ''">
			and a.appid in (${appid})
		</if>
		<if test="download_channel != null and download_channel != ''">
			and a.download_channel in (${download_channel})
		</if>
		<if test="pid != null and pid != ''">
			and a.pid = #{pid}
		</if>
		<if test="gift_id != null and gift_id != ''">
			and a.gift_id = #{gift_id}
		</if>
		<if test="level_id != null and level_id != ''">
			and a.level_id = #{level_id}
		</if>
		) aa
	</select>

	<select id="getActivityEffectList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.ActivityEffectVo">
		select data_date,appid,pid,download_channel,activity_id,activity_progress,
		sum(progress_new) progress_new,sum(progress_old) progress_old
		from  dnwx_bi.ads_activity_effect_daily where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="download_channel != null and download_channel != ''">
			and download_channel in (${download_channel})
		</if>
		<if test="activity_id != null and activity_id != ''">
			and activity_id = #{activity_id}
		</if>
		AND data_date <![CDATA[ >= ]]> #{start_date} and data_date <![CDATA[ <= ]]> #{end_date}
		<if test="group != null and group != ''">
			group by ${group}
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by data_date asc,progress_old desc
			</otherwise>
		</choose>
	</select>

	<select id="getActivityEffectSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.ActivityEffectVo">
		select sum(progress_new) progress_new,sum(progress_old) progress_old from dnwx_bi.ads_activity_effect_daily where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="download_channel != null and download_channel != ''">
			and download_channel in (${download_channel})
		</if>
		<if test="activity_id != null and activity_id != ''">
			and activity_id = #{activity_id}
		</if>
		AND data_date <![CDATA[ >= ]]> #{start_date} and data_date <![CDATA[ <= ]]> #{end_date}
	</select>

	<select id="getPayConversionList" parameterType="com.wbgame.pojo.game.report.query.PayConversionQueryVo"
			resultType="com.wbgame.pojo.game.report.PayConversionVo">
		select  t_date,a.appid,channel,pid,gift_id,gift_item_name,
		sum(new_click_cnt+old_click_cnt) click_cnt,
		sum(new_window_cnt+old_window_cnt) window_cnt,
		sum(new_choose_cnt+old_choose_cnt) choose_cnt,
		sum(new_pay_status_cnt+old_pay_status_cnt) pay_status_cnt,
		sum(new_on_account_cnt+old_on_account_cnt) on_account_cnt,
		sum(new_pay_status_cnt+old_pay_status_cnt)/(sum(new_click_cnt+old_click_cnt)) conversion_rate,
		sum(new_on_account_cnt+old_on_account_cnt)/(sum(new_pay_status_cnt+old_pay_status_cnt)) account_rate
		from  dnwx_bi.ads_pay_funnel_daily a left join dnwx_bi.ads_gift_info b
		on a.appid=b.appid and a.gift_id = b.gift_item_id where 1=1
		<if test="appid != null and appid != ''">
			and a.appid in (${appid})
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel})
		</if>
		<if test="gift_item_name != null and gift_item_name != ''">
			and gift_item_name  like '%${gift_item_name}%'
		</if>
		AND t_date <![CDATA[ >= ]]> #{start_date} and t_date <![CDATA[ <= ]]> #{end_date}
		<if test="group != null and group != ''">
			group by ${group}
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by t_date asc,new_click_cnt desc
			</otherwise>
		</choose>
	</select>

    <select id="getPayConversionListChart" parameterType="com.wbgame.pojo.game.report.query.PayConversionQueryVo"
			resultType="com.wbgame.pojo.game.report.PayConversionVo">


        select  t_date,
        (sum(new_click_cnt)+sum(old_click_cnt)) click_cnt,
        round((sum(new_pay_status_cnt)+sum(old_pay_status_cnt))/(sum(new_click_cnt)+sum(old_click_cnt)) * 100, 2) conversion_rate,
        round((sum(new_on_account_cnt)+sum(old_on_account_cnt))/(sum(new_pay_status_cnt)+sum(old_pay_status_cnt)) * 100, 2) account_rate
        from dnwx_bi.ads_pay_funnel_daily aa
        left join dnwx_bi.ads_gift_info bb
        on aa.appid=bb.appid and aa.gift_id=bb.gift_item_id
        where 1=1
        <if test="appid != null and appid != ''">
            and aa.appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="gift_item_name != null and gift_item_name != ''">
            and gift_item_name  like '%${gift_item_name}%'
        </if>
        AND t_date <![CDATA[ >= ]]> #{start_date} and t_date <![CDATA[ <= ]]> #{end_date}
        group by t_date
        order by t_date


	</select>

	<select id="getPayConversionSum" parameterType="com.wbgame.pojo.game.report.query.PayConversionQueryVo"
			resultType="com.wbgame.pojo.game.report.PayConversionVo">
		select 	sum(new_click_cnt+old_click_cnt) click_cnt,
		sum(new_window_cnt+old_window_cnt) window_cnt,
		sum(new_choose_cnt+old_choose_cnt) choose_cnt,
		sum(new_pay_status_cnt+old_pay_status_cnt) pay_status_cnt,
		sum(new_on_account_cnt+old_on_account_cnt) on_account_cnt,
		sum(new_pay_status_cnt+old_pay_status_cnt)/(sum(new_click_cnt+old_click_cnt)) conversion_rate,
		sum(new_on_account_cnt+old_on_account_cnt)/(sum(new_pay_status_cnt+old_pay_status_cnt)) account_rate
		from  dnwx_bi.ads_pay_funnel_daily a left join dnwx_bi.ads_gift_info b
		on a.appid=b.appid and a.gift_id = b.gift_item_id where 1=1
		<if test="appid != null and appid != ''">
			and a.appid in (${appid})
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel})
		</if>
		<if test="gift_item_name != null and gift_item_name != ''">
			and gift_item_name  like '%${gift_item_name}%'
		</if>
		AND t_date <![CDATA[ >= ]]> #{start_date} and t_date <![CDATA[ <= ]]> #{end_date}

	</select>

	<select id="getPayDailyList" parameterType="com.wbgame.pojo.game.report.query.PayDailyQueryVo"
			resultType="com.wbgame.pojo.game.report.PayDailyVo">
		select
		<choose>
           <when test="date_group == 'weekly'">
              DATE_FORMAT(tdate,'%Y-%u') tdate,
           </when>
           <when test="date_group == 'month'">
             DATE_FORMAT(tdate,'%Y-%m') tdate,
           </when>
           <when test="date_group == 'daily'">
            tdate tdate,
           </when>
           <otherwise>
           </otherwise>
     	</choose>
		    appid,app_name,download_channel,
		IFNULL(sum(add_user_cnt),0) add_user_cnt,sum(old_user_cnt) old_user_cnt,
		IFNULL(sum(active_user_cnt) ,0) active_user_cnt,
		IFNULL(sum(add_user_iap_revenue)/100 ,0) add_user_iap_revenue,
		IFNULL(sum(old_user_iap_revenue)/100 ,0) old_user_iap_revenue,
		IFNULL((sum(add_user_iap_revenue) + sum(old_user_iap_revenue))/100 ,0) iap_revenue_total,

		IFNULL(sum(active_user_iap_revenue)/100/sum(active_user_cnt),0) arpu_total,
		IFNULL(sum(add_user_iap_revenue)/100/sum(add_user_cnt),0) arpu_new,
		IFNULL(sum(old_user_iap_revenue)/100/sum(old_user_cnt),0) arpu_old,

		IFNULL(sum(active_user_iap_revenue)/100/sum(iap_revenue_active_user_cnt),0) pay_arpu_total,
		IFNULL(sum(old_user_iap_revenue)/100/sum(iap_revenue_old_user_cnt),0) pay_arpu_old,
		IFNULL(sum(add_user_iap_revenue)/100/sum(iap_revenue_add_user_cnt),0) pay_arpu_new,

		IFNULL(sum(iap_revenue_active_user_cnt)/sum(active_user_cnt),0) pay_ratio_total,
		IFNULL(sum(iap_revenue_old_user_cnt)/sum(old_user_cnt),0) pay_ratio_old,
		IFNULL(sum(iap_revenue_add_user_cnt)/sum(add_user_cnt),0) pay_ratio_new,
		IFNULL(sum(iap_revenue_add_user_cnt),0) iap_revenue_add_user_cnt,
		IFNULL(sum(iap_revenue_old_user_cnt),0) iap_revenue_old_user_cnt,
		IFNULL(sum(iap_revenue_active_user_cnt),0) iap_revenue_active_user_cnt

		from  dnwx_bi.ads_user_iap_revenue_info_daily  where 1=1

		<if test="appid != null and appid.size != 0">
			and appid in
			<foreach collection="appid" index="index" item="appid"
					 open="(" separator="," close=")">
				#{appid}
			</foreach>
		</if>

		<if test="channel != null and channel.size != 0">
			and download_channel in
			<foreach collection="channel" index="index" item="channel"
					 open="(" separator="," close=")">
				#{channel}
			</foreach>
		</if>

		AND tdate <![CDATA[ >= ]]> #{start_date} and tdate <![CDATA[ <= ]]> #{end_date}
		group by
		<choose>
           <when test="date_group == 'weekly'">
              DATE_FORMAT(tdate,'%Y-%u')
           </when>
           <when test="date_group == 'month'">
             DATE_FORMAT(tdate,'%Y-%m')
           </when>
           <when test="date_group == 'daily'">
            tdate
           </when>
           <otherwise>
           </otherwise>
     	</choose>
		<if test="group != null and group.size != 0">
			,
			<foreach collection="group" index="index" item="group" separator="," >
				${group}
			</foreach>
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate asc,active_user_cnt desc
			</otherwise>
		</choose>
	</select>


	<select id="getPayDailySum" parameterType="com.wbgame.pojo.game.report.query.PayDailyQueryVo"
			resultType="com.wbgame.pojo.game.report.PayDailyVo">
		select
		sum(add_user_cnt) add_user_cnt,sum(old_user_cnt) old_user_cnt,
		sum(active_user_cnt) active_user_cnt,

		sum(active_user_iap_revenue)/100/sum(active_user_cnt) arpu_total,
		sum(add_user_iap_revenue)/100/sum(add_user_cnt) arpu_new,
		sum(old_user_iap_revenue)/100/sum(old_user_cnt) arpu_old,

		sum(active_user_iap_revenue)/100/sum(iap_revenue_active_user_cnt) pay_arpu_total,
		sum(old_user_iap_revenue)/100/sum(iap_revenue_old_user_cnt) pay_arpu_old,
		sum(add_user_iap_revenue)/100/sum(iap_revenue_add_user_cnt) pay_arpu_new,

		sum(iap_revenue_active_user_cnt)/sum(active_user_cnt) pay_ratio_total,
		sum(iap_revenue_old_user_cnt)/sum(old_user_cnt) pay_ratio_old,
		sum(iap_revenue_add_user_cnt)/sum(add_user_cnt) pay_ratio_new,
		sum(iap_revenue_add_user_cnt) iap_revenue_add_user_cnt,
		sum(iap_revenue_old_user_cnt) iap_revenue_old_user_cnt,
		sum(iap_revenue_active_user_cnt) iap_revenue_active_user_cnt,

		IFNULL(sum(add_user_iap_revenue)/100 ,0) add_user_iap_revenue,
		IFNULL(sum(old_user_iap_revenue)/100 ,0) old_user_iap_revenue,
		IFNULL((sum(add_user_iap_revenue) + sum(old_user_iap_revenue))/100 ,0) iap_revenue_total

		from  dnwx_bi.ads_user_iap_revenue_info_daily  where 1=1

		<if test="appid != null and appid.size != 0">
			and appid in
			<foreach collection="appid" index="index" item="appid"
					 open="(" separator="," close=")">
				#{appid}
			</foreach>
		</if>
		<if test="channel != null and channel.size != 0">
			and download_channel in
			<foreach collection="channel" index="index" item="channel"
					 open="(" separator="," close=")">
				#{channel}
			</foreach>
		</if>
		AND tdate <![CDATA[ >= ]]> #{start_date} and tdate <![CDATA[ <= ]]> #{end_date}
 	</select>


    <select id="selectPackageAnalysisSales" resultType="java.util.Map" parameterType="java.util.Map">


        SELECT
            a.buy_date,
            a.appid,
            a.is_new_user,
            a.gift_item_id,
            b.gift_item_name,
            sum(a.total_num) total_num
        FROM
            dnwx_bi.ads_gift_analyse_daily a
                LEFT JOIN dnwx_bi.ads_gift_info b ON a.gift_item_id = b.gift_item_id
                AND a.appid = b.appid
        where  a.appid in (${appid})  AND buy_date BETWEEN #{start_date} and  #{end_date}  AND b.currency_type = #{currency_type}
        <if test="download_channel != null and download_channel != ''">
            and a.download_channel in (${download_channel})
        </if>
        <if test="gift_item_type != null and gift_item_type != ''">
            and gift_item_type = #{gift_item_type}
        </if>
        <if test="gift_item_id != null and gift_item_id != ''">
            and a.gift_item_id = #{gift_item_id}
        </if>
        <if test="gift_item_name != null and gift_item_name != ''">
            and b.gift_item_name REGEXP #{gift_item_name}
        </if>
        <if test="pid != null and pid != ''">
            and a.pid = #{pid}
        </if>
        GROUP BY
            a.buy_date, a.appid,a.gift_item_id, a.is_new_user, gift_item_name

        UNION ALL

        SELECT
            a.buy_date,
            a.appid,
            -1 is_new_user,
            a.gift_item_id,
            b.gift_item_name,
            sum(a.total_num) total_num
        FROM
            dnwx_bi.ads_gift_analyse_daily a
                LEFT JOIN dnwx_bi.ads_gift_info b ON a.gift_item_id = b.gift_item_id
                AND a.appid = b.appid
        where  a.appid in (${appid})  AND buy_date BETWEEN #{start_date} and  #{end_date}  AND b.currency_type = #{currency_type}
        <if test="download_channel != null and download_channel != ''">
            and a.download_channel in (${download_channel})
        </if>
        <if test="gift_item_type != null and gift_item_type != ''">
            and gift_item_type = #{gift_item_type}
        </if>
        <if test="gift_item_id != null and gift_item_id != ''">
            and a.gift_item_id = #{gift_item_id}
        </if>
        <if test="gift_item_name != null and gift_item_name != ''">
            and b.gift_item_name REGEXP #{gift_item_name}
        </if>
        <if test="pid != null and pid != ''">
            and a.pid = #{pid}
        </if>
        GROUP BY
            a.buy_date, a.appid,a.gift_item_id, gift_item_name,
        a.is_new_user


        union all

        SELECT

        a.buy_date,
        a.appid,
        a.is_new_user,
        a.gift_item_id,
        b.gift_item_name,

        sum( a.total_num ) total_num
        FROM
        dnwx_bi.ads_gift_analyse_xyx_daily a
        LEFT JOIN dnwx_bi.ads_gift_info b ON a.gift_item_id = b.gift_item_id
        AND a.appid = b.appid

        where  a.appid in (${appid})  AND buy_date BETWEEN #{start_date} and  #{end_date}  AND b.currency_type = #{currency_type}
        <if test="download_channel != null and download_channel != ''">
            and a.download_channel in (${download_channel})
        </if>
        <if test="gift_item_type != null and gift_item_type != ''">
            and gift_item_type = #{gift_item_type}
        </if>
        <if test="gift_item_id != null and gift_item_id != ''">
            and a.gift_item_id = #{gift_item_id}
        </if>
        <if test="gift_item_name != null and gift_item_name != ''">
            and b.gift_item_name REGEXP #{gift_item_name}
        </if>
        <if test="pid != null and pid != ''">
            and a.pid = #{pid}
        </if>
        GROUP BY
        a.buy_date,
        a.appid,
        a.gift_item_id,
        a.is_new_user,
        gift_item_name
        UNION ALL
        SELECT
        a.buy_date,
        a.appid,
        -1 is_new_user,
        a.gift_item_id,
        b.gift_item_name,

        sum( a.total_num ) total_num
        FROM
        dnwx_bi.ads_gift_analyse_xyx_daily a
        LEFT JOIN dnwx_bi.ads_gift_info b ON a.gift_item_id = b.gift_item_id
        AND a.appid = b.appid

        where  a.appid in (${appid})  AND buy_date BETWEEN #{start_date} and  #{end_date}  AND b.currency_type = #{currency_type}
        <if test="download_channel != null and download_channel != ''">
            and a.download_channel in (${download_channel})
        </if>
        <if test="gift_item_type != null and gift_item_type != ''">
            and gift_item_type = #{gift_item_type}
        </if>
        <if test="gift_item_id != null and gift_item_id != ''">
            and a.gift_item_id = #{gift_item_id}
        </if>
        <if test="gift_item_name != null and gift_item_name != ''">
            and b.gift_item_name REGEXP #{gift_item_name}
        </if>
        <if test="pid != null and pid != ''">
            and a.pid = #{pid}
        </if>
        GROUP BY
        a.buy_date,
        a.appid,
        a.gift_item_id,
        gift_item_name,
        a.is_new_user

        order by buy_date
    </select>


    <select id="selectFirstPayToNewOldUser" resultType="com.wbgame.pojo.game.GiftFirstPayVo"
            parameterType="com.wbgame.pojo.game.GiftFirstPayDTO">


        SELECT  create_date,
        level_id,
        count(distinct android_id) first_pay_total_num,
        count(distinct if(flag_new_user=1,android_id,null)) first_pay_new_num,
        count(distinct if(flag_new_user=0,android_id,null)) first_pay_old_num
        FROM `ads_order_analysis_daily`
        <where>

            first_charge=1
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and create_date between #{start_date} and #{end_date}
            </if>
            <if test="gift_id != null and gift_id != ''">
                and gift_id = #{gift_id}
            </if>
            <if test="level_id != null and level_id != ''">
                and level_id = #{level_id}
            </if>

            <if test="channelList != null and channelList.size > 0">
                AND download_channel IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

            <if test="prjidList != null and prjidList.size > 0">
                AND pid IN
                <foreach collection="prjidList" item="prj" open="(" separator="," close=")">
                    #{prj}
                </foreach>
            </if>


        </where>

        group  by create_date,level_id
    </select>

	<select id="getJskpList" resultType="com.wbgame.pojo.game.GameJskpVo">
		SELECT * FROM  user_info_jskp_result WHERE dataPointId is not null  and dataPointId !=''
	</select>

</mapper>
