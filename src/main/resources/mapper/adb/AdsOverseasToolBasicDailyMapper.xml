<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsOverseasToolBasicDailyMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.AdsOverseasToolBasicDailyVO">
        <result column="t_date" property="t_date" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="ver" property="ver" jdbcType="VARCHAR"/>
        <result column="brand_model" property="brandModel" jdbcType="VARCHAR"/>
        <result column="reg_user" property="reg_user" jdbcType="INTEGER"/>
        <result column="active_user" property="active_user" jdbcType="INTEGER"/>

    </resultMap>

    <sql id="Base_Column_List">
        t_date
        , appid, channel, country, ver, brand_model, reg_user, active_user, level_flash_show_uv,
    level_flash_show_pv, home_function_click_uv, home_function_click_pv, level_flash_end_uv, 
    level_flash_end_pv, level_ad_show_uv, level_ad_show_pv, B_popup_show_uv, B_popup_show_pv, 
    B_popup_start_uv, B_popup_start_pv, B_popup_use_click_uv, B_popup_use_click_pv, app_home_show_uv, 
    B_alive_first_1min_uv, B_alive_first_3min_uv, B_alive_first_5min_uv, B_alive_first_10min_uv, 
    B_alive_first_15min_uv, B_alive_first_30min_uv, B_alive_first_60min_uv, B_alive_first_120min_uv, 
    B_alive_first_360min_uv, B_alive_first_24h_uv, B_alive_first_48h_uv
    </sql>

    <select id="selectByCondition" parameterType="com.wbgame.pojo.AdsOverseasToolBasicDailyDTO"
            resultMap="BaseResultMap">

        SELECT
        <if test="group != null and group != ''">
            ${group},
        </if>
        sum( reg_user ) reg_user,
        sum( active_user ) active_user,
        concat( round(sum(reg_user)/sum(active_user) * 100, 2), "%" ) newProportion,
        concat(round(sum(level_flash_show_uv)/sum(home_function_click_uv) * 100, 2), "%") animationDisplaySuccessRateUv,
        concat(round(sum(level_flash_show_pv)/sum(home_function_click_pv) * 100, 2), "%") animationDisplaySuccessRatePv,
        concat(round(sum(level_flash_end_uv)/sum(level_flash_show_uv) * 100, 2), "%") animationCompletionRateUv,
        concat(round(sum(level_flash_end_pv)/sum(level_flash_show_pv) * 100, 2), "%") animationCompletionRatePv,
        concat(round(sum(level_ad_show_uv)/sum(level_flash_end_uv) * 100, 2), "%") interstitialAdImpressionRateUv,
        concat(round(sum(level_ad_show_pv)/sum(level_flash_end_pv) * 100, 2), "%") interstitialAdImpressionRatePv,
        concat(round(sum(B_popup_show_uv)/sum(B_popup_start_uv) * 100, 2), "%") popupImpressionRateUv,
        concat(round(sum(B_popup_show_pv)/sum(B_popup_start_pv) * 100, 2), "%") popupImpressionRatePv,
        round(sum(B_popup_show_pv)/sum(B_popup_show_uv) * 100, 2) popUpsPerCapita,
        concat(round(sum(B_popup_use_click_uv)/sum(B_popup_show_uv) * 100, 2), "%") popupCTRUv,
        concat(round(sum(B_popup_use_click_pv)/sum(B_popup_show_pv) * 100, 2), "%") popupCTRPv,
        concat(round(sum(app_home_show_uv)/sum(reg_user) * 100, 2), "%") homepagePenetration,
        concat(round(sum(B_alive_first_1min_uv)/sum(reg_user) * 100, 2), "%") minute1,
        concat(round(sum(B_alive_first_3min_uv)/sum(reg_user) * 100, 2), "%") minute3,
        concat(round(sum(B_alive_first_5min_uv)/sum(reg_user) * 100, 2), "%") minute5,
        concat(round(sum(B_alive_first_10min_uv)/sum(reg_user) * 100, 2), "%") minute10,
        concat(round(sum(B_alive_first_15min_uv)/sum(reg_user) * 100, 2), "%") minute15,
        concat(round(sum(B_alive_first_30min_uv)/sum(reg_user) * 100, 2), "%") minute30,
        concat(round(sum(B_alive_first_60min_uv)/sum(reg_user) * 100, 2), "%") hour1,
        concat(round(sum(B_alive_first_120min_uv)/sum(reg_user) * 100, 2), "%") hour2,
        concat(round(sum(B_alive_first_360min_uv)/sum(reg_user) * 100, 2), "%") hour6,
        concat(round(sum(B_alive_first_24h_uv)/sum(reg_user) * 100, 2), "%") hour24,
        concat(round(sum(B_alive_first_48h_uv)/sum(reg_user) * 100, 2), "%") hour48
        FROM
        ads_overseas_tool_basic_daily

        <where>

            <if test=" start_date != null and start_date != '' and  end_date != null and end_date != ''">
                and t_date between #{start_date} and #{end_date}
            </if>

            <if test="appid != null and appid.size > 0">
                and appid in

                <foreach collection="appid" item="aid" open="(" separator="," close=")">
                    #{aid}
                </foreach>

            </if>

            <if test="channel != null and channel.size > 0">
                and channel in

                <foreach collection="channel" item="c" open="(" separator="," close=")">
                    #{c}
                </foreach>
            </if>

            <if test="country != null and country.size > 0">
                and country in

                <foreach collection="country" item="ct" open="(" separator="," close=")">
                    #{ct}
                </foreach>
            </if>

            <if test="ver != null and ver != ''">
                and ver like #{ver} "%"
            </if>
            <if test="brandModel != null and brandModel != ''">
                and LOWER(brand_model) = #{brandModel}
            </if>
        </where>

        <if test="group != null and group != ''">
            group by ${group}
        </if>
      	<choose>
			<when test="order_str != null and order_str != ''">
				 order by ${order_str}
			</when>
			<otherwise>
				 ORDER BY t_date DESC
			</otherwise>
		</choose>


    </select>
</mapper>