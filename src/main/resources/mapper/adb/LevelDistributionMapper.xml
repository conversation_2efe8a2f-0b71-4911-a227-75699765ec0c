<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adb.LevelDistributionMapper">

    <select id="queryLevelDistribution" parameterType="com.wbgame.pojo.param.LevelDistributionParam"
            resultType="com.wbgame.pojo.LevelDistributionVo">
        <include refid="queryLevelDistributionSql"/>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by date asc,intoCountNew desc
            </otherwise>
        </choose>
    </select>
    <sql id="queryLevelDistributionSql">
        SELECT
		DATE_FORMAT( data_date, '%Y-%m-%d' ) date,
		appid,
		pid,
		download_channel channel,
		level_id levelId,
		ifnull( sum( into_count_new ), 0 ) AS intoCountNew,
		ifnull( sum( into_count_old ), 0 ) AS intoCountOld,
		ifnull( sum( stay_count_new ), 0 ) AS stayCountNew,
		ifnull( sum( stay_count_old ), 0 ) AS stayCountOld,
		ifnull( sum( charge_count_new ), 0 ) AS chargeCountNew,
		ifnull( sum( charge_count_old ), 0 ) AS chargeCountOld,
		ifnull( sum( charge_user_count_new ), 0 ) AS chargeUserCountNnew,
		ifnull( sum( charge_user_count_old ), 0 ) AS chargeUserCountOld,
		ifnull( sum( concat( charge_money_new, '' ) )/ 100, 0 ) AS chargeMoneyNew,
		ifnull( sum( concat( charge_money_old, '' ) )/ 100, 0 ) AS chargeMoneyOld,
		ifnull(( sum( into_count_new )+ sum( into_count_old )), 0 ) AS into_count,
		ifnull(( sum( stay_count_new )+ sum( stay_count_old )), 0 ) AS stay_count,
		ifnull( sum( today_pay_stay_count ), 0 ) AS today_pay_stay_count,
		ifnull( sum( history_pay_stay_count ), 0 ) AS history_pay_stay_count,
		ifnull( TRUNCATE (( sum( charge_user_count_new )+ sum( charge_user_count_old )), 2 ), 0 ) AS charge_user_count,
		ifnull( TRUNCATE ( sum( concat( charge_money_new, '' ) / 100 + concat( charge_money_old, '' ) / 100 ), 2 ), 0 ) AS charge_money,
		ifnull(( sum( charge_count_new )+ sum( charge_count_old )), 0 ) AS charge_count
       from  ads_game_level_distribute_daily  where  1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid =#{pid}
        </if>
        <if test="channel != null and channel != ''">
            and download_channel in (${channel})
        </if>
        <if test="levelId != null and levelId != ''">
            and level_id=#{levelId}
        </if>
        and data_date <![CDATA[>=]]> #{startDate} and data_date <![CDATA[<=]]> #{endtDate}
        group by ${group}
    </sql>

    <select id="queryLevelTrendChart" parameterType="com.wbgame.pojo.param.LevelDistributionParam"
            resultType="com.wbgame.pojo.LevelDistributionVo">
       select level_id as levelId,
       (sum(stay_count_new)+sum(stay_count_old)) as stay_count,sum(stay_count_new) as stayCountNew,sum(stay_count_old) as stayCountOld,
       sum(today_pay_stay_count) today_pay_stay_count,sum(history_pay_stay_count) history_pay_stay_count
       from  ads_game_level_distribute_daily  where  1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid =#{pid}
        </if>
        <if test="channel != null and channel != ''">
            and download_channel in (${channel})
        </if>
        <if test="levelId != null and levelId != ''">
            and level_id=#{levelId}
        </if>
        and DATE(data_date) <![CDATA[>=]]> #{startDate} and DATE(data_date) <![CDATA[<=]]> #{endtDate}
        group by level_id
        ORDER BY CAST(SUBSTRING_INDEX(level_id,'_',1) AS BIGINT) ASC,CAST(SUBSTRING_INDEX(level_id,'_',-1) AS BIGINT) ASC
    </select>
    
    <select id="queryTotalLevelDistribution" parameterType="com.wbgame.pojo.param.LevelDistributionParam"
            resultType="com.wbgame.pojo.LevelDistributionVo">
        select  sum( intoCountNew ) AS intoCountNew,
	sum( intoCountOld ) AS intoCountOld,
	sum( stayCountNew ) AS stayCountNew,
	sum( stayCountOld ) AS stayCountOld,
	sum( chargeCountNew ) AS chargeCountNew,
	sum( chargeCountOld ) AS chargeCountOld,
	sum( chargeUserCountNnew ) AS chargeUserCountNnew,
	sum( chargeUserCountOld ) AS chargeUserCountOld,
	TRUNCATE(sum( concat( chargeMoneyNew, '' ) ),2) AS chargeMoneyNew,
	TRUNCATE(sum( concat( chargeMoneyOld, '' ) ),2) AS chargeMoneyOld,
	sum( into_count ) AS into_count,
	sum( stay_count ) AS stay_count,
	sum( today_pay_stay_count ) AS today_pay_stay_count,
	sum( history_pay_stay_count ) AS history_pay_stay_count,
	sum( charge_user_count ) AS charge_user_count,
	TRUNCATE(sum( concat( charge_money, '' ) ),2) AS charge_money,
	sum( charge_count ) AS charge_count
         from  (<include refid="queryLevelDistributionSql"/>)
    </select>
    <!-- 月度ROI报表 -->
     <select id="queryRoiMonthly" parameterType="com.wbgame.pojo.param.ROIMonthlyParam"
            resultType="com.wbgame.pojo.operate.ROIMonthlyVo">
 	 	select `month`,app_id,calculation_method,round(sum(rebatespend)/100,2) rebatespend,SUM(reg_users) reg_users,SUM(active_users) active_users,
 	 	round(sum(rebatespend)/100/SUM(reg_users),2) cpa,
 	 	SUM(cumulative_users_1) cumulative_users_1,SUM(cumulative_users_2) cumulative_users_2,SUM(cumulative_users_3) cumulative_users_3,
 	 	SUM(cumulative_users_4) cumulative_users_4,SUM(cumulative_users_5) cumulative_users_5,SUM(cumulative_users_6) cumulative_users_6,
 	 	SUM(cumulative_users_7) cumulative_users_7,SUM(cumulative_users_8) cumulative_users_8,SUM(cumulative_users_9) cumulative_users_9,
 	 	SUM(cumulative_users_10) cumulative_users_10,SUM(cumulative_users_11) cumulative_users_11,SUM(cumulative_users_12) cumulative_users_12,
 	 	round((ifnull(SUM(add_revenue_1),0)+ifnull(SUM(add_purchase_revenue_1),0))/100/SUM(reg_users),2) add_revenue_1, round((ifnull(SUM(add_revenue_2),0)+ifnull(SUM(add_purchase_revenue_2),0))/100/SUM(reg_users),2) add_revenue_2,
 	 	round((ifnull(SUM(add_revenue_3),0)+ifnull(SUM(add_purchase_revenue_3),0))/100/SUM(reg_users),2) add_revenue_3, round((ifnull(SUM(add_revenue_4),0)+ifnull(SUM(add_purchase_revenue_4),0))/100/SUM(reg_users),2) add_revenue_4,
 	 	round((ifnull(SUM(add_revenue_5),0)+ifnull(SUM(add_purchase_revenue_5),0))/100/SUM(reg_users),2) add_revenue_5, round((ifnull(SUM(add_revenue_6),0)+ifnull(SUM(add_purchase_revenue_6),0))/100/SUM(reg_users),2) add_revenue_6,
 	 	round((ifnull(SUM(add_revenue_7),0)+ifnull(SUM(add_purchase_revenue_7),0))/100/SUM(reg_users),2) add_revenue_7, round((ifnull(SUM(add_revenue_8),0)+ifnull(SUM(add_purchase_revenue_8),0))/100/SUM(reg_users),2) add_revenue_8,
 	 	round((ifnull(SUM(add_revenue_9),0)+ifnull(SUM(add_purchase_revenue_9),0))/100/SUM(reg_users),2) add_revenue_9, round((ifnull(SUM(add_revenue_10),0)+ifnull(SUM(add_purchase_revenue_10),0))/100/SUM(reg_users),2) add_revenue_10,
 	 	round((ifnull(SUM(add_revenue_11),0)+ifnull(SUM(add_purchase_revenue_11),0))/100/SUM(reg_users),2) add_revenue_11, round((ifnull(SUM(add_revenue_12),0)+ifnull(SUM(add_purchase_revenue_12),0))/100/SUM(reg_users),2) add_revenue_12,
 	 	round(sum(rebatespend)/100/sum(cumulative_users_1),2) cost_payment_1,round(sum(rebatespend)/100/sum(cumulative_users_2),2) cost_payment_2,
 	 	round(sum(rebatespend)/100/sum(cumulative_users_3),2) cost_payment_3,round(sum(rebatespend)/100/sum(cumulative_users_4),2) cost_payment_4,
 	 	round(sum(rebatespend)/100/sum(cumulative_users_5),2) cost_payment_5,round(sum(rebatespend)/100/sum(cumulative_users_6),2) cost_payment_6,
 	 	round(sum(rebatespend)/100/sum(cumulative_users_7),2) cost_payment_7,round(sum(rebatespend)/100/sum(cumulative_users_8),2) cost_payment_8,
 	 	round(sum(rebatespend)/100/sum(cumulative_users_9),2) cost_payment_9,round(sum(rebatespend)/100/sum(cumulative_users_10),2) cost_payment_10,
 	 	round(sum(rebatespend)/100/sum(cumulative_users_11),2) cost_payment_11,round(sum(rebatespend)/100/sum(cumulative_users_12),2) cost_payment_12,
 	 	round((ifnull(SUM(add_revenue_1),0)+ifnull(SUM(add_purchase_revenue_1),0))/SUM(rebatespend),4) roi_1,
 	 	round((ifnull(SUM(add_revenue_2),0)+ifnull(SUM(add_purchase_revenue_2),0))/SUM(rebatespend),4) roi_2,
 	 	round((ifnull(SUM(add_revenue_3),0)+ifnull(SUM(add_purchase_revenue_3),0))/SUM(rebatespend),4) roi_3,
 	 	round((ifnull(SUM(add_revenue_4),0)+ifnull(SUM(add_purchase_revenue_4),0))/SUM(rebatespend),4) roi_4,
 	 	round((ifnull(SUM(add_revenue_5),0)+ifnull(SUM(add_purchase_revenue_5),0))/SUM(rebatespend),4) roi_5,
 	 	round((ifnull(SUM(add_revenue_6),0)+ifnull(SUM(add_purchase_revenue_6),0))/SUM(rebatespend),4) roi_6,
 	 	round((ifnull(SUM(add_revenue_7),0)+ifnull(SUM(add_purchase_revenue_7),0))/SUM(rebatespend),4) roi_7,
 	 	round((ifnull(SUM(add_revenue_8),0)+ifnull(SUM(add_purchase_revenue_8),0))/SUM(rebatespend),4) roi_8,
 	 	round((ifnull(SUM(add_revenue_9),0)+ifnull(SUM(add_purchase_revenue_9),0))/SUM(rebatespend),4) roi_9,
 	 	round((ifnull(SUM(add_revenue_10),0)+ifnull(SUM(add_purchase_revenue_10),0))/SUM(rebatespend),4) roi_10,
 	 	round((ifnull(SUM(add_revenue_11),0)+ifnull(SUM(add_purchase_revenue_11),0))/SUM(rebatespend),4) roi_11,
 	 	round((ifnull(SUM(add_revenue_12),0)+ifnull(SUM(add_purchase_revenue_12),0))/SUM(rebatespend),4) roi_12
        from  ads_roi_report_monthly  where  1=1
        <if test="app_id != null and app_id != ''">
            and app_id in (${app_id})
        </if>
        <if test="calculation_method != null and calculation_method != ''">
            and calculation_method=#{calculation_method}
        </if>
        <if test="beginDate != null and endDate != ''">
             and `month` between #{beginDate} and #{endDate}
        </if>
        <if test="group != null and group != ''">
             group by ${group}
        </if>
        <choose>
            <when test="order != null and order != ''">
                order by ${order}
            </when>
            <otherwise>
                order by `month` asc,rebatespend desc
            </otherwise>
        </choose>
    </select>
    
    <select id="queryTotalRoiMonthly" parameterType="com.wbgame.pojo.param.ROIMonthlyParam"
            resultType="com.wbgame.pojo.operate.ROIMonthlyVo">
       	 	select  FORMAT(sum(rebatespend)/100,2) rebatespend,SUM(reg_users) reg_users,SUM(active_users) active_users,FORMAT(sum(rebatespend)/100/SUM(reg_users),2) cpa,
 	 	SUM(cumulative_users_1) cumulative_users_1,SUM(cumulative_users_2) cumulative_users_2,SUM(cumulative_users_3) cumulative_users_3,
 	 	SUM(cumulative_users_4) cumulative_users_4,SUM(cumulative_users_5) cumulative_users_5,SUM(cumulative_users_6) cumulative_users_6,
 	 	SUM(cumulative_users_7) cumulative_users_7,SUM(cumulative_users_8) cumulative_users_8,SUM(cumulative_users_9) cumulative_users_9,
 	 	SUM(cumulative_users_10) cumulative_users_10,SUM(cumulative_users_11) cumulative_users_11,SUM(cumulative_users_12) cumulative_users_12,
 	 	FORMAT((ifnull(SUM(add_revenue_1),0)+ifnull(SUM(add_purchase_revenue_1),0))/100/SUM(reg_users),2) add_revenue_1, FORMAT((ifnull(SUM(add_revenue_2),0)+ifnull(SUM(add_purchase_revenue_2),0))/100/SUM(reg_users),2) add_revenue_2,
 	 	FORMAT((ifnull(SUM(add_revenue_3),0)+ifnull(SUM(add_purchase_revenue_3),0))/100/SUM(reg_users),2) add_revenue_3, FORMAT((ifnull(SUM(add_revenue_4),0)+ifnull(SUM(add_purchase_revenue_4),0))/100/SUM(reg_users),2) add_revenue_4,
 	 	FORMAT((ifnull(SUM(add_revenue_5),0)+ifnull(SUM(add_purchase_revenue_5),0))/100/SUM(reg_users),2) add_revenue_5, FORMAT((ifnull(SUM(add_revenue_6),0)+ifnull(SUM(add_purchase_revenue_6),0))/100/SUM(reg_users),2) add_revenue_6,
 	 	FORMAT((ifnull(SUM(add_revenue_7),0)+ifnull(SUM(add_purchase_revenue_7),0))/100/SUM(reg_users),2) add_revenue_7, FORMAT((ifnull(SUM(add_revenue_8),0)+ifnull(SUM(add_purchase_revenue_8),0))/100/SUM(reg_users),2) add_revenue_8,
 	 	FORMAT((ifnull(SUM(add_revenue_9),0)+ifnull(SUM(add_purchase_revenue_9),0))/100/SUM(reg_users),2) add_revenue_9, FORMAT((ifnull(SUM(add_revenue_10),0)+ifnull(SUM(add_purchase_revenue_10),0))/100/SUM(reg_users),2) add_revenue_10,
 	 	FORMAT((ifnull(SUM(add_revenue_11),0)+ifnull(SUM(add_purchase_revenue_11),0))/100/SUM(reg_users),2) add_revenue_11, FORMAT((ifnull(SUM(add_revenue_12),0)+ifnull(SUM(add_purchase_revenue_12),0))/100/SUM(reg_users),2) add_revenue_12,
 	 	FORMAT(sum(rebatespend)/100/cumulative_users_1,2) cost_payment_1,FORMAT(sum(rebatespend)/100/cumulative_users_2,2) cost_payment_2,
 	 	FORMAT(sum(rebatespend)/100/cumulative_users_3,2) cost_payment_3,FORMAT(sum(rebatespend)/100/cumulative_users_4,2) cost_payment_4,
 	 	FORMAT(sum(rebatespend)/100/cumulative_users_5,2) cost_payment_5,FORMAT(sum(rebatespend)/100/cumulative_users_6,2) cost_payment_6,
 	 	FORMAT(sum(rebatespend)/100/cumulative_users_7,2) cost_payment_7,FORMAT(sum(rebatespend)/100/cumulative_users_8,2) cost_payment_8,
 	 	FORMAT(sum(rebatespend)/100/cumulative_users_9,2) cost_payment_9,FORMAT(sum(rebatespend)/100/cumulative_users_10,2) cost_payment_10,
 	 	FORMAT(sum(rebatespend)/100/cumulative_users_11,2) cost_payment_11,FORMAT(sum(rebatespend)/100/cumulative_users_12,2) cost_payment_12,
 	 	CONCAT(FORMAT((ifnull(SUM(add_revenue_1),0)+ifnull(SUM(add_purchase_revenue_1),0))/SUM(rebatespend)*100,2),'%') roi_1,
 	 	CONCAT(FORMAT((ifnull(SUM(add_revenue_2),0)+ifnull(SUM(add_purchase_revenue_2),0))/SUM(rebatespend)*100,2),'%') roi_2,
 	 	CONCAT(FORMAT((ifnull(SUM(add_revenue_3),0)+ifnull(SUM(add_purchase_revenue_3),0))/SUM(rebatespend)*100,2),'%') roi_3,
 	 	CONCAT(FORMAT((ifnull(SUM(add_revenue_4),0)+ifnull(SUM(add_purchase_revenue_4),0))/SUM(rebatespend)*100,2),'%') roi_4,
 	 	CONCAT(FORMAT((ifnull(SUM(add_revenue_5),0)+ifnull(SUM(add_purchase_revenue_5),0))/SUM(rebatespend)*100,2),'%') roi_5,
 	 	CONCAT(FORMAT((ifnull(SUM(add_revenue_6),0)+ifnull(SUM(add_purchase_revenue_6),0))/SUM(rebatespend)*100,2),'%') roi_6,
 	 	CONCAT(FORMAT((ifnull(SUM(add_revenue_7),0)+ifnull(SUM(add_purchase_revenue_7),0))/SUM(rebatespend)*100,2),'%') roi_7,
 	 	CONCAT(FORMAT((ifnull(SUM(add_revenue_8),0)+ifnull(SUM(add_purchase_revenue_8),0))/SUM(rebatespend)*100,2),'%') roi_8,
 	 	CONCAT(FORMAT((ifnull(SUM(add_revenue_9),0)+ifnull(SUM(add_purchase_revenue_9),0))/SUM(rebatespend)*100,2),'%') roi_9,
 	 	CONCAT(FORMAT((ifnull(SUM(add_revenue_10),0)+ifnull(SUM(add_purchase_revenue_10),0))/SUM(rebatespend)*100,2),'%') roi_10,
 	 	CONCAT(FORMAT((ifnull(SUM(add_revenue_11),0)+ifnull(SUM(add_purchase_revenue_11),0))/SUM(rebatespend)*100,2),'%') roi_11,
 	 	CONCAT(FORMAT((ifnull(SUM(add_revenue_12),0)+ifnull(SUM(add_purchase_revenue_12),0))/SUM(rebatespend)*100,2),'%') roi_12
        from  ads_roi_report_monthly  where  1=1
        <if test="app_id != null and app_id != ''">
            and app_id in (${app_id})
        </if>
        <if test="calculation_method != null and calculation_method != ''">
            and calculation_method=#{calculation_method}
        </if>
         <if test="beginDate != null and endDate != ''">
             and `month` between #{beginDate} and #{endDate}
        </if>
    </select>
    <!-- 代币产销监控报表 -->
    <select id="queryTokenProduction" parameterType="com.wbgame.pojo.param.TokenProductionParam"
            resultType="com.wbgame.pojo.operate.TokenProductionVo">
        select 	
            ${group},token_type,CAST(SUBSTRING_INDEX(level_id,'_',1) AS BIGINT) level_id_order ,
		   IFNULL(round(AVG(consume_avg),2),0) avg_consume,
			IFNULL(round(AVG(produce_avg),2),0) avg_produce,
			SUM(consume) total_consume,
			SUM(produce) total_produce,
			SUM(consume_cnt) consume_count,
			SUM(produce_cnt) produce_count,
			CONCAT(round(SUM(consume)/SUM(produce)*100,2),'%') production_sales_ratio,
			IFNULL(round(AVG(produce_median),2),0) median_produce,
			IFNULL(round(AVG(consume_median),2),0) median_consume
        from ads_all_check_token_result_daily where appid in (${appid})
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
        <if test="level_id != null and level_id != ''">
            and level_id=#{level_id}
        </if>
        <if test="token_type != null and token_type != ''">
            and token_type=#{token_type}
        </if>
        <if test="reason != null and reason != ''">
            and reason=#{reason}
        </if>
        <if test="reg_falg != null">
            and reg_falg=#{reg_falg}
        </if>
		<if test="tag_r != null and tag_r != ''">
			and tag_r in (${tag_r})
		</if>
        <choose>
            <when test='group.contains("level_id") and group.contains("reason")'>
                and reason!=-100 and level_id!=-100
            </when>
            <when test='group.contains("level_id")'>
                and level_id!=-100 and  reason=-100
            </when>
            <when test='group.contains("reason")'>
                and reason!=-100 and level_id=-100
            </when>
            <otherwise>
                and reason=-100 and level_id=-100
            </otherwise>
        </choose>
        and DATE(t_date) <![CDATA[>=]]> #{start_date} and DATE(t_date) <![CDATA[<=]]> #{end_date}
        group by  ${group},token_type
        <choose>
            <when test="order != null and order != ''">
                order by ${order}
            </when>
            <otherwise>
                order by t_date asc,total_produce desc,total_consume desc
            </otherwise>
        </choose>
    </select>
    <select id="queryTotalTokenProduction" parameterType="com.wbgame.pojo.param.TokenProductionParam"
            resultType="com.wbgame.pojo.operate.TokenProductionVo">
        select 	
        	IFNULL(FORMAT(AVG(consume_avg),2),0) avg_consume,
			IFNULL(FORMAT(AVG(produce_avg),2),0) avg_produce,
			SUM(consume) total_consume,
			SUM(produce) total_produce,
			SUM(consume_cnt) consume_count,
			SUM(produce_cnt) produce_count,
			CONCAT(FORMAT(SUM(consume)/SUM(produce)*100,2),'%') production_sales_ratio,
			IFNULL(FORMAT(AVG(produce_median),2),0) median_produce,
			IFNULL(FORMAT(AVG(consume_median),2),0) median_consume
        from ads_all_check_token_result_daily where appid in (${appid})
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
        <if test="level_id != null and level_id != ''">
            and level_id=#{level_id}
        </if>
        <if test="token_type != null and token_type != ''">
            and token_type=#{token_type}
        </if>
        <if test="reason != null and reason != ''">
            and reason=#{reason}
        </if>
        <if test="reg_falg != null">
            and reg_falg=#{reg_falg}
        </if>
		<if test="tag_r != null and tag_r != ''">
			and tag_r in (${tag_r})
		</if>
        <choose>
            <when test='group.contains("level_id") and group.contains("reason")'>
                and reason!=-100 and level_id!=-100
            </when>
            <when test='group.contains("level_id")'>
                and level_id!=-100 and reason=-100
            </when>
            <when test='group.contains("reason")'>
                and reason!=-100 and level_id=-100
            </when>
            <otherwise>
                and reason=-100 and level_id=-100
            </otherwise>
        </choose>
        and DATE(t_date) <![CDATA[>=]]> #{start_date} and DATE(t_date) <![CDATA[<=]]> #{end_date}
    </select>
    <select id="isXList" parameterType="java.util.Map" resultType="com.wbgame.pojo.advert.UmengAdIncomeReportVo">
     select
        tdate
        <if test="group != null and group != '' and group.contains('appkey') ">
            ,appid
        </if>
        <if test="group != null and group != '' and group.contains('channel') ">
            ,channel
        </if>
     ,CASE WHEN SUM(simu_ad_show) > 0 THEN 1 ELSE 0 END AS is_x FROM ads_simulation_ad_analysis_daily 
     WHERE   tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}
	 GROUP BY tdate
	 <if test="group != null and group != '' and group.contains('appkey') ">
          ,appid
     </if>
     <if test="group != null and group != '' and group.contains('channel') ">
          ,channel
     </if>
    </select>
    
    <select id="querySuperCasualReportList"  parameterType="com.wbgame.pojo.param.SuperCasualReporParam" resultType="com.wbgame.pojo.advert.SuperCasualReportVo" >
		<include refid="SuperCasualReportSql" />
	</select>
	<select id="querySuperCasualReportTotal"  parameterType="com.wbgame.pojo.param.SuperCasualReporParam" resultType="com.wbgame.pojo.advert.SuperCasualReportVo" >
		select sum(avg_add_user) avg_add_user,sum(avg_active_user) avg_active_user,round(sum(avg_revenue),2) avg_revenue,round(sum(arpu),2) arpu,
		sum(plaque_pv) plaque_pv,round(sum(plaque_revenue),2) plaque_revenue,round(sum(plaque_ipu),2) plaque_ipu,round(sum(plaque_ecpm),2) plaque_ecpm,
		sum(video_pv) video_pv,round(sum(video_revenue),2) video_revenue,round(sum(video_ipu),2) video_ipu,round(sum(video_ecpm),2) video_ecpm
		 from (<include refid="SuperCasualReportSql" />)
	</select>
	<sql id="SuperCasualReportSql">
		select 
			<if test="group != null and group != '' and group.contains('tdate') ">
		          tdate,
		     </if>
		     <if test="group != null and group != '' and group.contains('two_app_category') ">
		          name as two_app_category,
		     </if>			
		     <if test="group != null and group != '' and group.contains('cha_id') ">
		          cha_id,
		     </if>
		     <if test="group != null and group != '' and group.contains('media_type') ">
		          media_type,
		     </if>
	        avg(total_addnum) avg_add_user,  
	        avg(total_actnum) avg_active_user,  
	        round(avg(total_sum_revenue),2) avg_revenue,
	        round(avg(total_sum_revenue)/avg(total_actnum),2) arpu,
	
	        avg(total_pv_plaque) plaque_pv,
	        round(avg(total_revenue_plaque),2) plaque_revenue,
	        round(avg(total_pv_plaque)/avg(total_actnum),2) plaque_ipu,
	        round(avg(total_revenue_plaque)/avg(total_pv_plaque)*1000,2) plaque_ecpm,
	
	        avg(total_pv_video) video_pv,
	        round(avg(total_revenue_video),2) video_revenue,
	        round(avg(total_pv_video)/avg(total_actnum),2) video_ipu,
	        round(avg(total_revenue_video)/avg(total_pv_video)*1000,2)  video_ecpm
		from
			(select 
			       tdate,
			       name,
			       cha_id,
			       media_type,
			       sum(actnum) total_actnum,
			       sum(addnum) total_addnum,    
			       sum(sum_revenue) total_sum_revenue,
			       sum(pv_plaque) total_pv_plaque,
			       sum(pv_video) total_pv_video,
			       sum(revenue_plaque) total_revenue_plaque,
			       sum(revenue_video) total_revenue_video
			from (  
					SELECT 
						tdate, 
		                appid, 
		                cha_id,
		                case when cha_type_name='自推广' then '自推广'
		                    when cha_type_name='IOS' then 'iOS'
		                    when cha_media='oppo' then 'oppo'
		                    when cha_media='vivo' then 'vivo'
		                    when cha_media='华为' then '华为'
		                    when cha_media='小米' then '小米'
		                else 'other' 
		                end as media_type, 
		                actnum, 
		                addnum, 
		                sum_revenue,  
		                pv_plaque, 
		                pv_video,  
		                revenue_plaque, 
		                revenue_video
			          FROM dnwx_bi.ads_dn_extend_revise_adtype_daily
			          where tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}
			          and appid in ( select id from (select id,two_app_category  from app_info  where app_category = 1  group by id,two_app_category)
			          )
			      ) m0
			
			join 
			
			(select id,two_app_category from app_info where app_category = 1 group by id,two_app_category) 
			 
			 m1  on m0.appid = m1.id
			left join 
			(select id,name from two_app_category) 
			
			m2  on m1.two_app_category = m2.id
			where  1=1
					<if test="two_app_category != null and two_app_category != ''  ">
			         	and m2.id in(${two_app_category})
			     	</if>
			     	<choose>
						<when test="media_type != null and media_type != ''">
							and m0.media_type in(${media_type})
						</when>
						<otherwise>
							and m0.media_type<![CDATA[<>]]>'other'
						</otherwise>
					</choose>
			     	<if test="cha_id != null and cha_id != ''  ">
			         	and m0.cha_id in(${cha_id})
			     	</if>
			group by tdate,name,cha_id,media_type
			)
		<if test="group != null and group != ''  ">
         	group by ${group}
     	</if>
     	<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				<if test="group != null and group != '' and group.contains('tdate') ">
				       order by tdate asc 
				 </if>
			</otherwise>
		</choose>
	</sql>
</mapper>