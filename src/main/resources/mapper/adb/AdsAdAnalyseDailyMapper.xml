<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsAdAnalyseDailyMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.adv2.AdsAdAnalyseDailyVO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="tdate" property="tdate" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
        <result column="agent" property="agent" jdbcType="VARCHAR"/>
        <result column="strategy_name" property="strategyName" jdbcType="VARCHAR"/>
        <result column="ad_sid" property="adSid" jdbcType="VARCHAR"/>
        <result column="sdk_code" property="sdkCode" jdbcType="VARCHAR"/>
        <result column="bidding" property="bidding" jdbcType="INTEGER"/>
        <result column="user_type" property="userType" jdbcType="INTEGER"/>
        <result column="ecpm" property="ecpm" jdbcType="VARCHAR"/>
        <result column="adjusted_revenue" property="adjustedRevenue" jdbcType="DECIMAL"/>
        <result column="adjusted_request_cnt" property="adjustedRequestCnt" jdbcType="DECIMAL"/>
        <result column="adjusted_request_success_cnt" property="adjustedRequestSuccessCnt" jdbcType="DECIMAL"/>
        <result column="adjusted_pv" property="adjustedPv" jdbcType="DECIMAL"/>
        <result column="adjusted_click_cnt" property="adjustedClickCnt" jdbcType="DECIMAL"/>
        <result column="fill_rate" property="fillRate" jdbcType="VARCHAR"/>
        <result column="impression_rate" property="impressionRate" jdbcType="VARCHAR"/>
        <result column="ctr" property="ctr" jdbcType="VARCHAR"/>
        <result column="user_label" property="userLabel" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , tdate, appid, app_name, agent, strategy_name, ad_sid, sdk_code, bidding, user_type,
    ecpm, adjusted_revenue, adjusted_request_cnt, adjusted_request_success_cnt, adjusted_pv, 
    adjusted_click_cnt
    </sql>


    <select id="selectAdsAdAnalyseDaily" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.adv2.AdsAdAnalyseDailyDTO">

        select
            if(locate("-", ecpm) > 1, ecpm, truncate(ecpm, 2)) ecpm,user_label,
               <if test="selectResGroup != null and selectResGroup != ''">
                   ${selectResGroup},
               </if>
            TRUNCATE(sum(cast(adjusted_revenue as decimal(18, 0))), 0) adjusted_revenue,
            TRUNCATE(sum(cast(adjusted_request_cnt as decimal(18, 0))), 0) adjusted_request_cnt,
            TRUNCATE(sum(cast(adjusted_request_success_cnt as decimal(18, 0))), 0) adjusted_request_success_cnt,
            TRUNCATE(sum(cast(adjusted_pv as decimal(18, 0))), 0) adjusted_pv,
            TRUNCATE(sum(cast(adjusted_click_cnt as decimal(18, 0))), 0) adjusted_click_cnt,

            TRUNCATE(cast(ifnull(sum(adjusted_request_success_cnt) / sum(adjusted_request_cnt), 0) * 100 as decimal(18, 2)) , 2) fill_rate,
            TRUNCATE(cast(ifnull(sum(adjusted_pv) / sum(adjusted_request_success_cnt), 0) * 100 as decimal(18, 2) ) , 2) impression_rate,
            TRUNCATE(cast(ifnull(sum(adjusted_click_cnt) / sum(adjusted_pv), 0) * 100 as decimal(18, 2) ) , 2) ctr

        from ads_ad_analyse_daily

        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                tdate BETWEEN #{start_date}
                AND #{end_date}
            </if>

            <if test="agent != null and agent != ''">
                and agent in (${agent}) 
            </if>

            <if test="userType != null">
                and user_type = #{userType}
            </if>

            <if test="strategyName != null and strategyName != ''">
                and strategy_name in (${strategyName}) 
            </if>

            <if test="adSid != null and adSid != ''">
                and ad_sid = #{adSid}
            </if>

            <if test="sdkCode != null and sdkCode != ''">
                and sdk_code = #{sdkCode}
            </if>

            <if test="bidding != null and bidding != ''">
                and bidding = #{bidding}
            </if>

            <if test="userLabel != null and userLabel != ''">
                and user_label = #{userLabel}

            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

        </where>

        group by ecpm,user_label
        <if test="group != null and group != ''">
              ,${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate desc, app_name desc
            </otherwise>
        </choose>


    </select>


    <select id="selectCountAdsAdAnalyseDaily" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.adv2.AdsAdAnalyseDailyDTO">

        select

            sum(adjusted_revenue) adjusted_revenue,
            sum(adjusted_request_cnt) adjusted_request_cnt,
            sum(adjusted_request_success_cnt) adjusted_request_success_cnt,
            sum(adjusted_pv) adjusted_pv,
            sum(adjusted_click_cnt) adjusted_click_cnt


        from (
            select
            ecpm,
            user_label,
            <if test="selectResGroup != null and selectResGroup != ''">
                ${selectResGroup},
            </if>
            TRUNCATE(sum(cast(adjusted_revenue as decimal(18, 0))), 0) adjusted_revenue,
            TRUNCATE(sum(cast(adjusted_request_cnt as decimal(18, 0))), 0) adjusted_request_cnt,
            TRUNCATE(sum(cast(adjusted_request_success_cnt as decimal(18, 0))), 0) adjusted_request_success_cnt,
            TRUNCATE(sum(cast(adjusted_pv as decimal(18, 0))), 0) adjusted_pv,
            TRUNCATE(sum(cast(adjusted_click_cnt as decimal(18, 0))), 0) adjusted_click_cnt


            from ads_ad_analyse_daily

            <where>
                <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                    tdate BETWEEN #{start_date}
                    AND #{end_date}
                </if>

                <if test="agent != null and agent != ''">
                    and agent = #{agent}
                </if>

                <if test="userType != null">
                    and user_type = #{userType}
                </if>

                <if test="strategyName != null and strategyName != ''">
                    and strategy_name = #{strategyName}
                </if>

                <if test="adSid != null and adSid != ''">
                    and ad_sid = #{adSid}
                </if>

                <if test="sdkCode != null and sdkCode != ''">
                    and sdk_code = #{sdkCode}
                </if>

                <if test="bidding != null and bidding != ''">
                    and bidding = #{bidding}
                </if>
                <if test="userLabel != null and userLabel != ''">
                    and user_label = #{userLabel}

                </if>

                <if test="appidList != null and appidList.size > 0">
                    AND appid IN
                    <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                        #{appid}
                    </foreach>
                </if>

            </where>

            group by ecpm,user_label
            <if test="group != null and group != ''">
                 ,${group}
            </if>
        ) a



    </select>

</mapper>