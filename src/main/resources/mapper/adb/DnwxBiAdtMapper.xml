<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.DnwxBiAdtMapper">
	<update id="updatePlanIdBudget">
		update dnwx_adt.oppo_group_info
		set dayBudget = #{budget}
		where opaccount = #{account}
		and planId = #{planId}
	</update>

	<delete id="deleteAppInfo">
        delete from dnwx_adt.app_info where 1=1
    </delete>

	<delete id="deleteDnwxBiAppInfo">
        delete from dnwx_bi.app_info where 1=1
    </delete>
    <delete id="deleteAppleSpendManual">
		delete from dnwx_bi.apple_manual_spend_data where
			<foreach collection="list" item="li" separator="or">
				(tdate = #{li.tdate} and appid=#{li.appid} and media=#{li.media})
			</foreach>
	</delete>
	<delete id="deleteReyunIncome">
		delete from dnwx_bi.camera_line_tracking_io_data_suppliment where
			<foreach collection="list" item="li" separator="or">
				(tdate = #{li.tdate} and appid=#{li.appid})
			</foreach>
	</delete>

	<insert id="batchInsertAppInfo">
		insert into dnwx_adt.app_info(
		id,
		channel_id,
		app_id,
		app_name,
		create_time,
		sync_umeng,
		umeng_key,
		app_category,
		umeng_account,
		find_vals,
		os_type,
		xyx_id,
		reyun_key,
		bus_category,
		two_app_category
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.id},
			#{li.channel_id},
			#{li.app_id},
			#{li.app_name},
			#{li.create_time},
			#{li.sync_umeng},
			#{li.umeng_key},
			#{li.app_category},
			#{li.umeng_account},
			#{li.find_vals},
			#{li.os_type},
			#{li.xyx_id},
			#{li.reyun_key},
			#{li.bus_category},
			#{li.two_app_category}
			)
		</foreach>
	</insert>

	<insert id="batchInsertDnwxBiAppInfo">
		insert into dnwx_bi.app_info(
		id,
		channel_id,
		app_id,
		app_name,
		create_time,
		sync_umeng,
		umeng_key,
		app_category,
		umeng_account,
		find_vals,
		os_type,
		xyx_id,
		reyun_key,
		bus_category,
		two_app_category
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.id},
			#{li.channel_id},
			#{li.app_id},
			#{li.app_name},
			#{li.create_time},
			#{li.sync_umeng},
			#{li.umeng_key},
			#{li.app_category},
			#{li.umeng_account},
			#{li.find_vals},
			#{li.os_type},
			#{li.xyx_id},
			#{li.reyun_key},
			#{li.bus_category},
			#{li.two_app_category}
			)
		</foreach>
	</insert>

	<select id="selectDnChaCashTotal" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dncha_cash_sql"/>
	</select>


	<select id="selectCashTotalHuaweiOversea"
			resultType="com.wbgame.pojo.adv2.reportEntity.ReportChina">
		SELECT * FROM `dn_cha_cash_total`
		where date = #{date}
		  and agent = 'Huawei'
		  and cha_id = 'google_huawei'
	</select>

	<select id="selectDnChaCashTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
		TRUNCATE(sum(xx.request_count),2) request_count,
		TRUNCATE(sum(xx.return_count),2) return_count,
		CONCAT(ROUND(sum(xx.return_count)/sum(xx.request_count)*100, 2),'%') fill_rate,
		TRUNCATE(sum(xx.pv),2) pv,
		CONCAT(ROUND(sum(xx.pv)/sum(xx.return_count)*100, 2),'%') pv_rate,
		TRUNCATE(sum(xx.revenue),2) revenue,
		TRUNCATE(sum(xx.dollar_revenue),2) dollar_revenue,
		TRUNCATE(sum(xx.revenue)/sum(xx.pv)*1000,2) ecpm,
		TRUNCATE(sum(xx.click),2) click,
		CONCAT(ROUND(sum(xx.click)/sum(xx.pv)*100, 2),'%') click_rate,
		CONCAT(IFNULL(CAST(SUM(revenue)/SUM(click) AS decimal(18,3)), 0),'') cpc
		from (<include refid="dncha_cash_sql"/>) xx
	</select>

	<insert id="batchInsertXyxAdvData">
		insert into dn_cha_cash_total(member_id,app_id,placement_id,`date`,return_count,pv,click,revenue,dnappid,agent,ad_sid,cha_type
		,cha_media,cha_sub_launch,cha_id,cha_type_name,country,open_type,placement_type) values
		<foreach collection="list" item="it" separator=",">
			(#{it.sdk_appid},#{it.sdk_appid},#{it.ad_unit_id},#{it.date},#{it.req_succ_count},#{it.exposure_count},#{it.click_count},#{it.income},#{it.appid}
			,#{it.agent},#{it.adsid},#{it.cha_type},#{it.cha_media},#{it.cha_sub_launch},#{it.cha_id},#{it.type_name},'CN',#{it.open_type},#{it.sdk_adtype})
		</foreach>
	</insert>
	<insert id="insertCreativeXiaomiCreativeIdReport">
		insert into dnwx_adt.dn_xiaomi_creative_info_temp
		SELECT creative_id, account, gameName,putUser,transferType,lowType,groupName FROM dnwx_adt.dn_xiaomi_creative_report
		WHERE `day` = #{date} and creative_id not in (select creative_id from dnwx_adt.dn_xiaomi_creative_info_temp)
		group by creative_id
		having sum(spend) > 0 or sum(gameIncome) > 0
	</insert>
	<select id="selectCreativeXiaomiCreativeIdList" resultType="java.util.Map">
		SELECT creative_id, account, gameName,putUser,transferType,lowType,groupName FROM dnwx_adt.dn_xiaomi_creative_report
		WHERE `day` = #{date}
		group by creative_id
		having sum(spend) > 0 or sum(gameIncome) > 0
	</select>

	<insert id="insertAppleSpendManual">
		insert into apple_manual_spend_data
		(tdate, appid, appName, media, spend, putUser, updateTime)
		values
		    <foreach collection="list" item="it" separator=",">
				(#{it.tdate},#{it.appid},#{it.appName},#{it.media},#{it.spend}, #{it.putUser}, #{it.updateTime})
			</foreach>
		on duplicate key update
		    spend = values(spend),
		    putUser = values(putUser),
		                  updateTime = values(updateTime)

	</insert>
	<insert id="insertReyunIncome">
		insert into camera_line_tracking_io_data_suppliment
		(tdate, appid, amendment_income, putUser, updateTime)
		values
		    <foreach collection="list" item="it" separator=",">
				(#{it.tdate},#{it.appid},#{it.amendment_income}, #{it.putUser}, #{it.updateTime})
			</foreach>
		on duplicate key update
		    amendment_income = values(amendment_income),
		        putUser = values(putUser),
		                      updateTime = values(updateTime)
	</insert>

	<select id="getOperationReport" resultType="com.wbgame.pojo.jettison.report.dto.OperationReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.OperationReportParam">
		select spend,buy_installs,buy_revenue,buy_spendRevenue,revenue,add_num,active_num,um_add_num,um_active_num,self_act_num,self_add_num,add_revenue,add_revenue3,add_revenue7,add_revenue30
		,reActive,uninstalls,register,buy_cost,buy_profit,concat(buy_roi,'%') buy_roi,concat(buy_spendRoi,'%') buy_spendRoi,natural_revenue,natural_spendRevenue
		,revenue_profit,concat(revenue_profit_rate,'%') revenue_profit_rate,concat(roi,'%') roi,concat(add_roi,'%') add_roi,concat(add_roi3,'%') add_roi3
		,concat(add_roi7,'%') add_roi7,concat(add_roi30,'%') add_roi30,preReActive,preUninstalls,activeLtv1,activeLtv7,registerLtv1,registerLtv7
		,lt3,lt7,registerCost,concat(buy_rate,'%') buy_rate,cost,arpu,install_arpu,natural_install,concat(f_roi_total,'%') f_roi_total,concat(buy_roi_total,'%') buy_roi_total
		,f_cash_revenue,buy_revenue_total,revenueChannel,revenueSelf,concat(t_roi_channel,'%') t_roi_channel,t_rapu_channel,t_rapu_self
		,concat(natural_revenue_roi,'%') natural_revenue_roi,concat(natural_spendRevenue_roi,'%') natural_spendRevenue_roi,new_user_avg_duration
		, appCategory,app, ad_platform, channel, companyName, gameName,day,packageName,app_type,avg_duration,direct_revenue,not_direct_revenue,concat(not_direct_roi,'%') not_direct_roi
		, concat(retain_umeng, '%') retain_umeng, concat(retain_kp, '%') retain_kp, concat(store_spend_rate, '%') store_spend_rate
		, non_store_spend, store_spend,concat(huawei_profit_roi,'%') huawei_profit_roi
		, store_register_cost,non_store_register_cost,store_installs_cost,non_store_installs_cost
		, store_register_ltv1,non_store_register_ltv1,store_installs_ltv1,non_store_installs_ltv1
		<choose>
			<when test="group != null and group.size() > 0 and (group.contains('ad_platform') or group.contains('channel'))">
				,policyLtv1,policyLtv,ltv,lt,estimated_profit
			</when>
		</choose>
		from (select spend,buy_installs,buy_revenue,buy_spendRevenue,revenue,add_num,active_num,um_add_num,um_active_num,self_act_num,self_add_num,add_revenue,add_revenue3,add_revenue7,add_revenue30
		,reActive,uninstalls,register,buy_cost,buy_profit,buy_roi,buy_spendRoi,natural_revenue,natural_spendRevenue
		,revenue_profit,revenue_profit_rate,roi,add_roi,add_roi3
		,add_roi7,add_roi30,preReActive,preUninstalls,activeLtv1,activeLtv7,registerLtv1,registerLtv7
		,lt3,lt7,registerCost,buy_rate,cost,arpu,install_arpu,natural_install,f_roi_total,buy_roi_total
		,f_cash_revenue,buy_revenue_total,revenueChannel,revenueSelf,t_roi_channel,t_rapu_channel,t_rapu_self
		,natural_revenue_roi,natural_spendRevenue_roi,new_user_avg_duration
		, appCategory,app, ad_platform, channel, companyName, gameName,day,packageName,app_type,avg_duration,direct_revenue,not_direct_revenue,not_direct_roi
        ,retain_umeng, retain_kp, non_store_spend, store_spend, IFNULL(convert(store_spend*100/spend,decimal(20,2)),0.00) AS store_spend_rate,huawei_profit_roi
		,IFNULL(convert(store_spend/store_register,decimal(20,2)),0.00) store_register_cost,IFNULL(convert(non_store_spend/non_store_register,decimal(20,2)),0.00) non_store_register_cost
		,IFNULL(convert(store_spend/store_installs,decimal(20,2)),0.00) store_installs_cost,IFNULL(convert(non_store_spend/non_store_installs,decimal(20,2)),0.00) non_store_installs_cost
		,IFNULL(convert(store_revenue1/store_register,decimal(20,2)),0.00) store_register_ltv1,IFNULL(convert(non_store_revenue1/non_store_register,decimal(20,2)),0.00) non_store_register_ltv1
		,IFNULL(convert(store_revenue1/store_installs,decimal(20,2)),0.00) store_installs_ltv1,IFNULL(convert(non_store_revenue1/non_store_installs,decimal(20,2)),0.00) non_store_installs_ltv1
        <choose>
            <when test="group != null and group.size() > 0 and (group.contains('ad_platform') or group.contains('channel'))">
                ,ltv,
				IFNULL(convert(policyLtv1,decimal(20,2)),0.00) AS policyLtv1,
				IFNULL(convert(policyLtv,decimal(20,2)),0.00) AS policyLtv,
				CASE ad_platform
				WHEN 'vivo' THEN IFNULL(convert(ltv/registerLtv1,decimal(20,2)),0.00)
				WHEN '华为' THEN IFNULL(convert(policyLtv/policyLtv1,decimal(20,2)),0.00)
				ELSE IFNULL(convert(ltv/activeLtv1,decimal(20,2)),0.00)
				END lt,
				CASE ad_platform
				WHEN 'vivo' THEN IFNULL(convert(policyLtv/registerCost,decimal(20,2)),0.00)
				ELSE IFNULL(convert(policyLtv/buy_cost,decimal(20,2)),0.00)
				END estimated_profit
            </when>
        </choose>
		from (SELECT SEC_TO_TIME(convert(avg( duration ),int)) avg_duration,SEC_TO_TIME(CONVERT(avg( new_user_avg_duration ), INT)) AS new_user_avg_duration,convert(SUM(spend),decimal(20,2)) AS spend, SUM(buy_installs) AS buy_installs, convert(SUM(buy_revenue),decimal(20,2)) AS buy_revenue, convert(SUM(buy_spendRevenue),decimal(20,2)) AS buy_spendRevenue,
		convert(SUM(revenue),decimal(20,2)) AS revenue, SUM(add_num) AS add_num, SUM(active_num) AS active_num, SUM(um_add_num) AS um_add_num,SUM(um_active_num) AS um_active_num,sum(self_act_num) self_act_num,sum(self_add_num) self_add_num,
		convert(SUM(add_revenue),decimal(20,2)) AS add_revenue,convert(SUM(add_revenue3),decimal(20,2)) AS add_revenue3,convert(SUM(add_revenue7),decimal(20,2)) AS add_revenue7,convert(SUM(add_revenue30),decimal(20,2)) AS add_revenue30,
		SUM(reActive) AS reActive,SUM(uninstalls) AS uninstalls, SUM(register) AS register,
		IFNULL(convert(SUM(spend)/SUM(buy_installs),decimal(20,2)),0.00) AS buy_cost,
		IFNULL(convert(SUM(buy_spendRevenue)-SUM(spend),decimal(20,2)),0.00) AS buy_profit,
		IFNULL(convert(SUM(buy_revenue)/SUM(spend) * 100,decimal(20,2)),0.00) AS buy_roi,
		IFNULL(convert(SUM(buy_spendRevenue)/SUM(spend) * 100,decimal(20,2)),0.00) AS buy_spendRoi,
		IFNULL(convert(SUM(revenue)-SUM(buy_revenue),decimal(20,2)),0.00) AS natural_revenue,
		IFNULL(convert(SUM(revenue)-SUM(buy_spendRevenue),decimal(20,2)),0.00) AS natural_spendRevenue,
		IFNULL(convert((SUM(revenue)-SUM(buy_revenue))/sum(spend)*100,decimal(20,2)),0.00) AS natural_revenue_roi,
		IFNULL(convert((SUM(revenue)-SUM(buy_spendRevenue))/sum(spend)*100,decimal(20,2)),0.00) AS natural_spendRevenue_roi,
		IFNULL(convert(SUM(revenue)-SUM(spend),decimal(20,2)),0.00) AS revenue_profit,
		IFNULL(convert((SUM(revenue)-SUM(spend))/SUM(revenue) * 100,decimal(20,2)),0.00) AS revenue_profit_rate,
		IFNULL(convert(SUM(revenue)/SUM(spend) * 100,decimal(20,2)),0.00) AS roi,
		IFNULL(convert(SUM(add_revenue)/SUM(spend) * 100,decimal(20,2)),0.00) AS add_roi,
		IFNULL(convert(SUM(add_revenue3)/SUM(spend) * 100,decimal(20,2)),0.00) AS add_roi3,
		IFNULL(convert(SUM(add_revenue7)/SUM(spend) * 100,decimal(20,2)),0.00) AS add_roi7,
		IFNULL(convert(SUM(add_revenue30)/SUM(spend) * 100,decimal(20,2)),0.00) AS add_roi30,
		IFNULL(convert(SUM(spend)/SUM(reActive),decimal(20,2)),0.00) AS preReActive,
		IFNULL(convert(SUM(spend)/SUM(uninstalls),decimal(20,2)),0.00) AS preUninstalls,
		IFNULL(convert(SUM(add_revenue)/SUM(buy_installs),decimal(20,2)),0.00) AS activeLtv1,
		IFNULL(convert(SUM(add_revenue7)/SUM(buy_installs),decimal(20,2)),0.00) AS activeLtv7,
		IFNULL(convert(SUM(add_revenue)/SUM(register),decimal(20,2)),0.00) AS registerLtv1,
		IFNULL(convert(SUM(add_revenue7)/SUM(register),decimal(20,2)),0.00) AS registerLtv7,
		IFNULL(convert(SUM(add_revenue3)/SUM(add_revenue),decimal(20,2)),0.00) AS lt3,
		IFNULL(convert(SUM(add_revenue7)/SUM(add_revenue),decimal(20,2)),0.00) AS lt7,
		IFNULL(convert(SUM(spend)/SUM(register),decimal(20,2)),0.00) AS registerCost,
		IFNULL(convert(SUM(f_cash_revenue)/SUM(spend)*100,decimal(20,2)),0.00) AS f_roi_total,
		IFNULL(convert(SUM(buy_revenue_total)/SUM(spend)*100,decimal(20,2)),0.00) AS buy_roi_total,
		IFNULL(convert(SUM(f_cash_revenue),decimal(20,2)),0.00) AS f_cash_revenue,
		IFNULL(convert(SUM(buy_revenue_total),decimal(20,2)),0.00) AS buy_revenue_total,
		IFNULL(convert(SUM(revenueChannel),decimal(20,2)),0.00) AS revenueChannel,
		IFNULL(convert(SUM(revenueSelf),decimal(20,2)),0.00) AS revenueSelf,
		IFNULL(convert(avg(retain_kp),decimal(20,2)),0.00) AS retain_kp,
		IFNULL(convert(avg(retain_umeng),decimal(20,2)),0.00) AS retain_umeng,
		IFNULL(convert(SUM(revenueChannel)/SUM(spend)*100,decimal(20,2)),0.00) AS t_roi_channel,
		convert(SUM(direct_revenue),decimal(20,2)) AS direct_revenue,
		IFNULL(convert(SUM(revenue)-ifnull(SUM(direct_revenue),0),decimal(20,2)),0.00) AS not_direct_revenue,
		IFNULL(convert((SUM(revenue)-SUM(direct_revenue))/sum(spend)*100,decimal(20,2)),0.00) AS not_direct_roi,
		convert(SUM(store_spend),decimal(20,2)) AS store_spend,
		convert(SUM(non_store_spend),decimal(20,2)) AS non_store_spend,
		convert(SUM(store_register),decimal(20,2)) AS store_register,
		convert(SUM(non_store_register),decimal(20,2)) AS non_store_register,
		convert(SUM(store_installs),decimal(20,2)) AS store_installs,
		convert(SUM(non_store_installs),decimal(20,2)) AS non_store_installs,
		convert(SUM(store_revenue1),decimal(20,2)) AS store_revenue1,
		convert(SUM(non_store_revenue1),decimal(20,2)) AS non_store_revenue1,
        <choose>
            <when test="group != null and group.size() > 0 and (group.contains('ad_platform') or group.contains('channel'))">
                CASE ad_platform
                WHEN 'oppo' THEN ROUND(ROUND(SUM(add_revenue) / SUM(buy_installs), 2) * 2.2,2)
                WHEN 'vivo' THEN ROUND(ROUND(SUM(add_revenue) / SUM(register), 2) * 1.3,2)
                WHEN '小米' THEN ROUND(ROUND(SUM(add_revenue) / SUM(buy_installs), 2) * 1.4,2)
                WHEN '华为' THEN ROUND(SUM(add_revenue) / SUM(buy_installs), 2) * 2
                WHEN '荣耀' THEN ROUND(SUM(add_revenue) / SUM(buy_installs), 2)
                ELSE 0
                END policyLtv1,
                CASE ad_platform
                WHEN 'oppo' THEN ROUND(ROUND(SUM(buy_spendRevenue) / SUM(buy_installs), 2) * 2.2,2)
                WHEN 'vivo' THEN ROUND(ROUND(SUM(buy_spendRevenue) / SUM(register), 2) * 1.3,2)
                WHEN '小米' THEN ROUND(ROUND(SUM(buy_spendRevenue) / SUM(buy_installs), 2) * 1.4,2)
                WHEN '华为' THEN ROUND(SUM(buy_spendRevenue) / SUM(buy_installs), 2)
                WHEN '荣耀' THEN ROUND(SUM(buy_spendRevenue) / SUM(buy_installs), 2)
                ELSE 0
                END policyLtv,
				CASE ad_platform
				WHEN 'vivo' THEN IFNULL(convert(SUM(buy_spendRevenue)/SUM(register),decimal(20,2)),0.00)
				ELSE IFNULL(convert(SUM(buy_spendRevenue)/SUM(buy_installs),decimal(20,2)),0.00)
				END ltv,
            </when>
        </choose>
		<choose>
			<when test="choose == 'channel'">
				IFNULL(convert(SUM(revenueChannel)/SUM(active_num),decimal(20,2)),0.00) AS t_rapu_channel,
				IFNULL(convert(SUM(revenueSelf)/SUM(active_num),decimal(20,2)),0.00) AS t_rapu_self,
				IFNULL(convert(SUM(buy_installs)/SUM(add_num) * 100,decimal(20,2)),0.00) AS buy_rate,
				IFNULL(convert(SUM(spend)/SUM(add_num),decimal(20,2)),0.00) AS cost,
				IFNULL(convert(SUM(revenue)/SUM(active_num),decimal(20,2)),0.00) AS arpu,
				IFNULL(convert(SUM(revenue)/SUM(add_num),decimal(20,2)),0.00) AS install_arpu,
				IFNULL(round(SUM(add_num)-SUM(buy_installs),0),0) AS natural_install
			</when>
			<when test="choose == 'self'">
				IFNULL(convert(SUM(revenueChannel)/SUM(self_act_num),decimal(20,2)),0.00) AS t_rapu_channel,
				IFNULL(convert(SUM(revenueSelf)/SUM(self_act_num),decimal(20,2)),0.00) AS t_rapu_self,
				IFNULL(convert(SUM(buy_installs)/SUM(self_add_num) * 100,decimal(20,2)),0.00) AS buy_rate,
				IFNULL(convert(SUM(spend)/SUM(self_add_num),decimal(20,2)),0.00) AS cost,
				IFNULL(convert(SUM(revenue)/SUM(self_act_num),decimal(20,2)),0.00) AS arpu,
				IFNULL(convert(SUM(revenue)/SUM(self_add_num),decimal(20,2)),0.00) AS install_arpu,
				IFNULL(round(SUM(self_add_num)-SUM(buy_installs),0),0) AS natural_install
			</when>
			<otherwise>
				IFNULL(convert(SUM(revenueChannel)/SUM(um_active_num),decimal(20,2)),0.00) AS t_rapu_channel,
				IFNULL(convert(SUM(revenueSelf)/SUM(um_active_num),decimal(20,2)),0.00) AS t_rapu_self,
				IFNULL(convert(SUM(buy_installs)/SUM(um_add_num) * 100,decimal(20,2)),0.00) AS buy_rate,
				IFNULL(convert(SUM(spend)/SUM(um_add_num),decimal(20,2)),0.00) AS cost,
				IFNULL(convert(SUM(revenue)/SUM(um_active_num),decimal(20,2)),0.00) AS arpu,
				IFNULL(convert(SUM(revenue)/SUM(um_add_num),decimal(20,2)),0.00) AS install_arpu,
				IFNULL(round(SUM(um_add_num)-SUM(buy_installs),0),0) AS natural_install
			</otherwise>
		</choose>
		,IFNULL(convert(SUM(buy_spendRevenue)/SUM(spend) * 100 * #{huawei_profit_roi_rate},decimal(20,2)),0.00) + IFNULL(convert((SUM(revenue)-SUM(direct_revenue))/sum(spend)*100,decimal(20,2)),0.00) as huawei_profit_roi
		, app_category appCategory,app, ad_platform, channel, companyName, gameName, packageName,if(app_type = 1,'新游',if(app_type = 2,'老游','无')) app_type
		, <choose>
			<when test="custom_date != null and custom_date.size() > 0">
			     concat(#{start_date},'至',#{end_date}) as day
			</when>
		     <when test="group != null and group.size() > 0 and group.contains('week')">
				 DATE_FORMAT(day, '%x-%v') as day,DATE_FORMAT(day, '%x-%v')
			 </when>
			<when test="group != null and group.size() > 0 and group.contains('month')">
				DATE_FORMAT(day,'%Y-%m') as day,
				DATE_FORMAT(day,'%Y-%m')
			</when>
			<when test="group != null and group.size() > 0 and group.contains('beek')">
				CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS day
				,CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0")) as beek
			</when>
			<otherwise>
				day
			</otherwise>
	      </choose>
		FROM dnwx_adt.dn_report_operation_summary a left join dnwx_adt.app_info b on a.app = b.id
		<include refid="getOperationReportCondition1"/>
		<include refid="getOperationReportCondition2"/>)a
		<if test="key != null and key != ''">
			<if test="index == 'arpu'">
				WHERE IFNULL(t_rapu_self,0.00) <![CDATA[ ${symbol} ]]> #{number}
			</if>
		</if>

		<if test="order_str == null or order_str == ''">
			ORDER BY `day` ASC, spend DESC
		</if>
		<if test="order_str != null and order_str != ''">
			ORDER BY ${order_str}
		</if>)a
	</select>

	<select id="getOperationSummary" resultType="com.wbgame.pojo.jettison.report.dto.OperationReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.OperationReportParam">
		select spend,buy_installs,buy_revenue,buy_spendRevenue,revenue,add_num,active_num,um_add_num,um_active_num,self_act_num,self_add_num,add_revenue,add_revenue3,add_revenue7,add_revenue30
		,reActive,uninstalls,register,buy_cost,buy_profit,concat(buy_roi,'%') buy_roi,concat(buy_spendRoi,'%') buy_spendRoi,natural_revenue,natural_spendRevenue
		,revenue_profit,concat(revenue_profit_rate,'%') revenue_profit_rate,concat(roi,'%') roi,concat(add_roi,'%') add_roi,concat(add_roi3,'%') add_roi3
		,concat(add_roi7,'%') add_roi7,concat(add_roi30,'%') add_roi30,preReActive,preUninstalls,activeLtv1,activeLtv7,registerLtv1,registerLtv7
		,lt3,lt7,registerCost,concat(buy_rate,'%') buy_rate,cost,arpu,install_arpu,natural_install,concat(f_roi_total,'%') f_roi_total,concat(buy_roi_total,'%') buy_roi_total
		,f_cash_revenue,buy_revenue_total,revenueChannel,revenueSelf,concat(t_roi_channel,'%') t_roi_channel,t_rapu_channel,t_rapu_self
		,concat(natural_revenue_roi,'%') natural_revenue_roi,concat(natural_spendRevenue_roi,'%') natural_spendRevenue_roi, avg_duration,new_user_avg_duration
		,direct_revenue,not_direct_revenue,concat(not_direct_roi,'%') not_direct_roi
		,concat(retain_umeng, '%') retain_umeng, concat(retain_kp, '%') retain_kp, concat(store_spend_rate, '%') store_spend_rate
		, non_store_spend, store_spend,concat(huawei_profit_roi,'%') huawei_profit_roi
		, store_register_cost,non_store_register_cost,store_installs_cost,non_store_installs_cost
		, store_register_ltv1,non_store_register_ltv1,store_installs_ltv1,non_store_installs_ltv1
		FROM (
		SELECT SEC_TO_TIME(convert(avg( duration ),int)) avg_duration,SEC_TO_TIME(CONVERT(avg( new_user_avg_duration ), INT)) AS new_user_avg_duration,convert(SUM(spend),decimal(20,2)) AS spend, SUM(buy_installs) AS buy_installs, convert(SUM(buy_revenue),decimal(20,2)) AS buy_revenue, convert(SUM(buy_spendRevenue),decimal(20,2)) AS buy_spendRevenue,
		convert(SUM(revenue),decimal(20,2)) AS revenue, SUM(add_num) AS add_num, SUM(active_num) AS active_num, SUM(um_add_num) AS um_add_num,SUM(um_active_num) AS um_active_num,SUM(self_act_num) self_act_num,SUM(self_add_num) self_add_num,
		convert(SUM(add_revenue),decimal(20,2)) AS add_revenue,convert(SUM(add_revenue3),decimal(20,2)) AS add_revenue3,convert(SUM(add_revenue7),decimal(20,2)) AS add_revenue7,convert(SUM(add_revenue30),decimal(20,2)) AS add_revenue30,
		SUM(reActive) AS reActive,SUM(uninstalls) AS uninstalls, SUM(register) AS register,
		IFNULL(convert(SUM(spend)/SUM(buy_installs),decimal(20,2)),0.00) AS buy_cost,
		IFNULL(convert(SUM(buy_spendRevenue)-SUM(spend),decimal(20,2)),0.00) AS buy_profit,
		IFNULL(convert(SUM(buy_revenue)/SUM(spend) * 100,decimal(20,2)),0.00) AS buy_roi,
		IFNULL(convert(SUM(buy_spendRevenue)/SUM(spend) * 100,decimal(20,2)),0.00) AS buy_spendRoi,
		IFNULL(convert(SUM(revenue)-SUM(buy_revenue),decimal(20,2)),0.00) AS natural_revenue,
		IFNULL(convert(SUM(revenue)-SUM(buy_spendRevenue),decimal(20,2)),0.00) AS natural_spendRevenue,
		IFNULL(convert((SUM(revenue)-SUM(buy_revenue))/sum(spend)*100,decimal(20,2)),0.00) AS natural_revenue_roi,
		IFNULL(convert((SUM(revenue)-SUM(buy_spendRevenue))/sum(spend)*100,decimal(20,2)),0.00) AS natural_spendRevenue_roi,
		IFNULL(convert(SUM(revenue)-SUM(spend),decimal(20,2)),0.00) AS revenue_profit,
		IFNULL(convert((SUM(revenue)-SUM(spend))/SUM(revenue) * 100,decimal(20,2)),0.00) AS revenue_profit_rate,
		IFNULL(convert(SUM(revenue)/SUM(spend) * 100,decimal(20,2)),0.00) AS roi,
		IFNULL(convert(SUM(add_revenue)/SUM(spend) * 100,decimal(20,2)),0.00) AS add_roi,
		IFNULL(convert(SUM(add_revenue3)/SUM(spend) * 100,decimal(20,2)),0.00) AS add_roi3,
		IFNULL(convert(SUM(add_revenue7)/SUM(spend) * 100,decimal(20,2)),0.00) AS add_roi7,
		IFNULL(convert(SUM(add_revenue30)/SUM(spend) * 100,decimal(20,2)),0.00) AS add_roi30,
		IFNULL(convert(SUM(spend)/SUM(reActive),decimal(20,2)),0.00) AS preReActive,
		IFNULL(convert(SUM(spend)/SUM(uninstalls),decimal(20,2)),0.00) AS preUninstalls,
		IFNULL(convert(SUM(add_revenue)/SUM(buy_installs),decimal(20,2)),0.00) AS activeLtv1,
		IFNULL(convert(SUM(add_revenue7)/SUM(buy_installs),decimal(20,2)),0.00) AS activeLtv7,
		IFNULL(convert(SUM(add_revenue)/SUM(register),decimal(20,2)),0.00) AS registerLtv1,
		IFNULL(convert(SUM(add_revenue7)/SUM(register),decimal(20,2)),0.00) AS registerLtv7,
		IFNULL(convert(SUM(add_revenue3)/SUM(add_revenue),decimal(20,2)),0.00) AS lt3,
		IFNULL(convert(SUM(add_revenue7)/SUM(add_revenue),decimal(20,2)),0.00) AS lt7,
		IFNULL(convert(SUM(spend)/SUM(register),decimal(20,2)),0.00) AS registerCost,
		IFNULL(convert(SUM(f_cash_revenue)/SUM(spend)*100,decimal(20,2)),0.00) AS f_roi_total,
		IFNULL(convert(SUM(buy_revenue_total)/SUM(spend)*100,decimal(20,2)),0.00) AS buy_roi_total,
		IFNULL(convert(SUM(f_cash_revenue),decimal(20,2)),0.00) AS f_cash_revenue,
		IFNULL(convert(SUM(buy_revenue_total),decimal(20,2)),0.00) AS buy_revenue_total,
		IFNULL(convert(SUM(revenueChannel),decimal(20,2)),0.00) AS revenueChannel,
		IFNULL(convert(SUM(revenueSelf),decimal(20,2)),0.00) AS revenueSelf,
		IFNULL(convert(SUM(revenueChannel)/SUM(spend)*100,decimal(20,2)),0.00) AS t_roi_channel,
		convert(SUM(direct_revenue),decimal(20,2)) AS direct_revenue,
		IFNULL(convert(SUM(revenue)-SUM(direct_revenue),decimal(20,2)),0.00) AS not_direct_revenue,
		IFNULL(convert((SUM(revenue)-SUM(direct_revenue))/sum(spend)*100,decimal(20,2)),0.00) AS not_direct_roi,
		retain_umeng, retain_kp,non_store_spend, store_spend, IFNULL(convert(store_spend*100/spend,decimal(20,2)),0.00) AS store_spend_rate,
		IFNULL(convert(SUM(buy_spendRevenue)/SUM(spend) * 100 * #{huawei_profit_roi_rate},decimal(20,2)),0.00) + IFNULL(convert((SUM(revenue)-SUM(direct_revenue))/sum(spend)*100,decimal(20,2)),0.00) as huawei_profit_roi,
		IFNULL(convert(store_spend/store_register,decimal(20,2)),0.00) store_register_cost,IFNULL(convert(non_store_spend/non_store_register,decimal(20,2)),0.00) non_store_register_cost,
		IFNULL(convert(store_spend/store_installs,decimal(20,2)),0.00) store_installs_cost,IFNULL(convert(non_store_spend/non_store_installs,decimal(20,2)),0.00) non_store_installs_cost,
		IFNULL(convert(store_revenue1/store_register,decimal(20,2)),0.00) store_register_ltv1,IFNULL(convert(non_store_revenue1/non_store_register,decimal(20,2)),0.00) non_store_register_ltv1,
		IFNULL(convert(store_revenue1/store_installs,decimal(20,2)),0.00) store_installs_ltv1,IFNULL(convert(non_store_revenue1/non_store_installs,decimal(20,2)),0.00) non_store_installs_ltv1,
		<choose>
			<when test="choose == 'channel'">
				IFNULL(convert(SUM(revenueChannel)/SUM(active_num),decimal(20,2)),0.00) AS t_rapu_channel,
				IFNULL(convert(SUM(revenueSelf)/SUM(active_num),decimal(20,2)),0.00) AS t_rapu_self,
				IFNULL(convert(SUM(buy_installs)/SUM(add_num) * 100,decimal(20,2)),0.00) AS buy_rate,
				IFNULL(convert(SUM(spend)/SUM(add_num),decimal(20,2)),0.00) AS cost,
				IFNULL(convert(SUM(revenue)/SUM(active_num),decimal(20,2)),0.00) AS arpu,
				IFNULL(convert(SUM(revenue)/SUM(add_num),decimal(20,2)),0.00) AS install_arpu,
				IFNULL(round(SUM(add_num)-SUM(buy_installs),0),0) AS natural_install
			</when>
			<when test="choose == 'self'">
				IFNULL(convert(SUM(revenueChannel)/SUM(self_act_num),decimal(20,2)),0.00) AS t_rapu_channel,
				IFNULL(convert(SUM(revenueSelf)/SUM(self_act_num),decimal(20,2)),0.00) AS t_rapu_self,
				IFNULL(convert(SUM(buy_installs)/SUM(self_add_num) * 100,decimal(20,2)),0.00) AS buy_rate,
				IFNULL(convert(SUM(spend)/SUM(self_add_num),decimal(20,2)),0.00) AS cost,
				IFNULL(convert(SUM(revenue)/SUM(self_act_num),decimal(20,2)),0.00) AS arpu,
				IFNULL(convert(SUM(revenue)/SUM(self_add_num),decimal(20,2)),0.00) AS install_arpu,
				IFNULL(round(SUM(self_add_num)-SUM(buy_installs),0),0) AS natural_install
			</when>
			<otherwise>
				IFNULL(convert(SUM(revenueChannel)/SUM(um_active_num),decimal(20,2)),0.00) AS t_rapu_channel,
				IFNULL(convert(SUM(revenueSelf)/SUM(um_active_num),decimal(20,2)),0.00) AS t_rapu_self,
				IFNULL(convert(SUM(buy_installs)/SUM(um_add_num) * 100,decimal(20,2)),0.00) AS buy_rate,
				IFNULL(convert(SUM(spend)/SUM(um_add_num),decimal(20,2)),0.00) AS cost,
				IFNULL(convert(SUM(revenue)/SUM(um_active_num),decimal(20,2)),0.00) AS arpu,
				IFNULL(convert(SUM(revenue)/SUM(um_add_num),decimal(20,2)),0.00) AS install_arpu,
				IFNULL(round(SUM(um_add_num)-SUM(buy_installs),0),0) AS natural_install
			</otherwise>
		</choose>
		FROM
		(select avg(duration) duration,AVG(new_user_avg_duration) new_user_avg_duration,
		convert(SUM(spend),decimal(20,2)) AS spend, SUM(buy_installs) AS buy_installs, convert(SUM(buy_revenue),decimal(20,2)) AS buy_revenue, convert(SUM(buy_spendRevenue),decimal(20,2)) AS buy_spendRevenue,
		convert(SUM(revenue),decimal(20,2)) AS revenue, SUM(add_num) AS add_num, SUM(active_num) AS active_num, SUM(um_add_num) AS um_add_num,SUM(um_active_num) AS um_active_num,
		convert(SUM(add_revenue),decimal(20,2)) AS add_revenue,convert(SUM(add_revenue3),decimal(20,2)) AS add_revenue3,convert(SUM(add_revenue7),decimal(20,2)) AS add_revenue7,convert(SUM(add_revenue30),decimal(20,2)) AS add_revenue30,
		SUM(reActive) AS reActive,SUM(uninstalls) AS uninstalls, SUM(register) AS register,sum (self_act_num) self_act_num,sum(self_add_num) self_add_num,
		IFNULL(convert(SUM(f_cash_revenue),decimal(20,2)),0.00) AS f_cash_revenue,
		IFNULL(convert(SUM(buy_revenue_total),decimal(20,2)),0.00) AS buy_revenue_total,
		IFNULL(convert(SUM(revenueChannel),decimal(20,2)),0.00) AS revenueChannel,
		IFNULL(convert(SUM(revenueSelf),decimal(20,2)),0.00) AS revenueSelf,
        IFNULL(convert(SUM(direct_revenue),decimal(20,2)),0.00) AS direct_revenue,
        IFNULL(convert(avg(retain_kp),decimal(20,2)),0.00) AS retain_kp,
		IFNULL(convert(avg(retain_umeng),decimal(20,2)),0.00) AS retain_umeng,
		 convert(SUM(store_spend),decimal(20,2)) AS store_spend,
		convert(SUM(non_store_spend),decimal(20,2)) AS non_store_spend,
		convert(SUM(store_register),decimal(20,2)) AS store_register,
		convert(SUM(non_store_register),decimal(20,2)) AS non_store_register,
		convert(SUM(store_installs),decimal(20,2)) AS store_installs,
		convert(SUM(non_store_installs),decimal(20,2)) AS non_store_installs,
		convert(SUM(store_revenue1),decimal(20,2)) AS store_revenue1,
		convert(SUM(non_store_revenue1),decimal(20,2)) AS non_store_revenue1
		FROM dnwx_adt.dn_report_operation_summary a left join dnwx_adt.app_info b on a.app = b.id
		<include refid="getOperationReportCondition1"/>
		<include refid="getOperationReportCondition2"/>
		<if test="key != null and key != ''">
			<if test="index == 'arpu'">
				and IFNULL(convert(revenueSelf/um_active_num,decimal(20,2)),0.00) <![CDATA[ ${symbol} ]]> #{number}
			</if>
		</if>) a
		) a
	</select>

	<select id="getOperationReport_v2" resultType="com.wbgame.pojo.jettison.report.dto.OperationReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.OperationReportParam">
		select day,app_name app,spend,buy_cost,concat(add_roi,'%') add_roi
		,concat(buy_spendRoi,'%') buy_spendRoi
		,concat(not_direct_roi,'%') not_direct_roi
		,buy_spendRevenue,not_direct_revenue
		from (SELECT convert(SUM(spend),decimal(20,2)) AS spend,
		IFNULL(convert(SUM(spend)/SUM(buy_installs),decimal(20,2)),0.00) AS buy_cost,
		IFNULL(convert(SUM(add_revenue)/SUM(spend) * 100,decimal(20,2)),0.00) AS add_roi,
		IFNULL(convert(SUM(buy_spendRevenue)/SUM(spend) * 100,decimal(20,2)),0.00) AS buy_spendRoi,
		IFNULL(convert((SUM(revenue)-SUM(direct_revenue))/sum(spend)*100,decimal(20,2)),0.00) AS not_direct_roi,
		convert(SUM(buy_spendRevenue),decimal(20,2)) AS buy_spendRevenue,
		IFNULL(convert(SUM(revenue)-ifnull(SUM(direct_revenue),0),decimal(20,2)),0.00) AS not_direct_revenue,
		app_name
		, <choose>
		<when test="custom_date != null and custom_date.size() > 0">
			concat(#{start_date},'至',#{end_date}) as day
		</when>
		<when test="group != null and group.size() > 0 and group.contains('week')">
			DATE_FORMAT(day, '%x-%v') as day,DATE_FORMAT(day, '%x-%v')
		</when>
		<when test="group != null and group.size() > 0 and group.contains('month')">
			DATE_FORMAT(day,'%Y-%m') as day,
			DATE_FORMAT(day,'%Y-%m')
		</when>
		<when test="group != null and group.size() > 0 and group.contains('beek')">
			CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS day
			,CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0")) as beek
		</when>
		<otherwise>
			day
		</otherwise>
	</choose>
		FROM dnwx_adt.dn_report_operation_summary a left join dnwx_adt.app_info b on a.app = b.id
		WHERE day <![CDATA[ >= ]]> #{start_date} AND day <![CDATA[ <= ]]> #{end_date}
		<if test="app != null and app.size > 0">
			AND app IN
			<foreach collection="app" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="channel != null and channel.size > 0">
			AND channel IN
			<foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="group != null and group.size > 0">
			GROUP BY
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'week'">
						DATE_FORMAT(day, '%x-%v')
					</when>
					<when test="item == 'month'">
						DATE_FORMAT(day,'%Y-%m')
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>)a
		<if test="order_str == null or order_str == ''">
			ORDER BY `day` ASC, spend DESC
		</if>
		<if test="order_str != null and order_str != ''">
			ORDER BY ${order_str}
		</if>
	</select>

	<select id="getOperationSummary_v2" resultType="com.wbgame.pojo.jettison.report.dto.OperationReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.OperationReportParam">
		select spend,buy_cost,concat(add_roi,'%') add_roi
		,concat(buy_spendRoi,'%') buy_spendRoi
		,concat(not_direct_roi,'%') not_direct_roi
		,buy_spendRevenue,not_direct_revenue
		FROM
		(SELECT convert(SUM(spend),decimal(20,2)) AS spend,
		IFNULL(convert(SUM(spend)/SUM(buy_installs),decimal(20,2)),0.00) AS buy_cost,
		IFNULL(convert(SUM(add_revenue)/SUM(spend) * 100,decimal(20,2)),0.00) AS add_roi,
		IFNULL(convert(SUM(buy_spendRevenue)/SUM(spend) * 100,decimal(20,2)),0.00) AS buy_spendRoi,
		IFNULL(convert((SUM(revenue)-SUM(direct_revenue))/sum(spend)*100,decimal(20,2)),0.00) AS not_direct_roi,
		convert(SUM(buy_spendRevenue),decimal(20,2)) AS buy_spendRevenue,
		IFNULL(convert(SUM(revenue)-ifnull(SUM(direct_revenue),0),decimal(20,2)),0.00) AS not_direct_revenue
		FROM dnwx_adt.dn_report_operation_summary
		WHERE day <![CDATA[ >= ]]> #{start_date} AND day <![CDATA[ <= ]]> #{end_date}
		<if test="app != null and app.size > 0">
			AND app IN
			<foreach collection="app" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="channel != null and channel.size > 0">
			AND channel IN
			<foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		) a
	</select>


	<select id="getSpendReport" resultType="com.wbgame.pojo.jettison.report.dto.SpendReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.SpendReportParam">
		SELECT SUM(spend) AS spend,SUM(reActiveSpend) AS reActiveSpend,SUM(uninstallsSpend) AS uninstallsSpend, SUM(rebateSpend) AS rebateSpend,SUM(dollarSpend) AS dollarSpend,SUM(dollarRebateSpend) AS dollarRebateSpend,
		SUM(installs) AS installs, SUM(impressions) AS impressions, SUM(clicks) AS clicks,
		SUM(totalRevenue1) AS revenue,SUM(revenue1) AS revenue1, SUM(revenue3) AS revenue3, SUM(revenue7) AS revenue7, SUM(revenue14) AS revenue14,SUM(revenue30) AS revenue30,
		SUM(retain1) retain1, SUM(retain3) retain3, SUM(retain7) retain7, SUM(`convert`) AS `convert`,sum(revenue_hour_24) revenue_hour_24,sum(pay_hour_24) pay_hour_24,sum(pay_hour_24_user) pay_hour_24_user,
		SUM(payRevenue) AS payRevenue,SUM(payRevenue1) AS payRevenue1, SUM(payRevenue3) AS payRevenue3, SUM(payRevenue7) AS payRevenue7, SUM(payRevenue14) AS payRevenue14, SUM(payRevenue30) AS payRevenue30,
		SUM(IFNULL(totalRevenue1,0.00)+IFNULL(payRevenue,0.00)) AS totalRevenue,SUM(register) AS register, SUM(game_addiction) AS game_addiction,SUM(download) AS download,
		SUM(reActive) AS reActive,SUM(uninstalls) AS uninstalls,SUM(cashSpend) AS cashSpend,SUM(virtualSpend) AS virtualSpend,
		SUM(addPayCount) AS addPayCount,SUM(payCount) AS payCount,SUM(gamePayCount) AS gamePayCount,SUM(payCount7) AS payCount7,SUM(payCountFirst7) AS payCountFirst7,
		SUM(b_install) AS b_install,SUM(b_active) AS b_active,SUM(b_addRevenue) AS b_addRevenue,SUM(b_activeRevenue) AS b_activeRevenue,
		SUM(b_retain1) AS b_retain1,SUM(b_addVideo) AS b_addVideo,SUM(b_addPlaque) AS b_addPlaque,
		SUM(beforeRevenue1) AS beforeRevenue1,SUM(beforePayRevenue1) AS beforePayRevenue1,
		SUM(recoveryRevenue) AS recoveryRevenue,SUM(recoveryPayRevenue) AS recoveryPayRevenue,
		IFNULL(SUM(installs)/SUM(impressions) * 100,0.00) AS impressionConvertRate,
		IFNULL(SUM(installs)/SUM(clicks) * 100,0.00) AS clickConvertRate,
		IFNULL(SUM(clicks)/SUM(impressions) * 100,0.00) AS clickRate,
		IFNULL(SUM(rebateSpend)/SUM(installs),0.00) AS cpi,
		IFNULL(SUM(revenue1)/SUM(clicks),0.00) AS clickLtv1,
		IFNULL(SUM(revenue1)/SUM(installs),0.00) AS installsLtv1,
		IFNULL(SUM(revenue3)/SUM(installs),0.00) AS installsLtv3,
		IFNULL(SUM(revenue7)/SUM(installs),0.00) AS installsLtv7,
		IFNULL(SUM(revenue14)/SUM(installs),0.00) AS installsLtv14,
		IFNULL(SUM(revenue30)/SUM(installs),0.00) AS installsLtv30,
		IFNULL(SUM(revenue1)/SUM(register),0.00) AS registerLtv1,
		IFNULL(SUM(revenue3)/SUM(register),0.00) AS registerLtv3,
		IFNULL(SUM(revenue7)/SUM(register),0.00) AS registerLtv7,
		IFNULL(SUM(revenue14)/SUM(register),0.00) AS registerLtv14,
		IFNULL(SUM(revenue30)/SUM(register),0.00) AS registerLtv30,
		IFNULL(SUM(payRevenue1)/SUM(installs),0.00) AS installsPayLtv1,
		IFNULL(SUM(payRevenue3)/SUM(installs),0.00) AS installsPayLtv3,
		IFNULL(SUM(payRevenue7)/SUM(installs),0.00) AS installsPayLtv7,
		IFNULL(SUM(payRevenue30)/SUM(installs),0.00) AS installsPayLtv30,
		IFNULL(SUM(payRevenue1)/SUM(register),0.00) AS registerPayLtv1,
		IFNULL(SUM(payRevenue3)/SUM(register),0.00) AS registerPayLtv3,
		IFNULL(SUM(payRevenue7)/SUM(register),0.00) AS registerPayLtv7,
		IFNULL(SUM(payRevenue30)/SUM(register),0.00) AS registerPayLtv30,
		IFNULL(SUM(revenue1+payRevenue1)/SUM(installs),0.00) AS installsTotalLtv1,
		IFNULL(SUM(revenue3+payRevenue3)/SUM(installs),0.00) AS installsTotalLtv3,
		IFNULL(SUM(revenue7+payRevenue7)/SUM(installs),0.00) AS installsTotalLtv7,
		IFNULL(SUM(revenue30+payRevenue30)/SUM(installs),0.00) AS installsTotalLtv30,
		IFNULL(SUM(revenue1+payRevenue1)/SUM(register),0.00) AS registerTotalLtv1,
		IFNULL(SUM(revenue3+payRevenue3)/SUM(register),0.00) AS registerTotalLtv3,
		IFNULL(SUM(revenue7+payRevenue7)/SUM(register),0.00) AS registerTotalLtv7,
		IFNULL(SUM(revenue30+payRevenue30)/SUM(register),0.00) AS registerTotalLtv30,
		IFNULL(SUM(revenue3)/SUM(revenue1),0.00) AS lt3,
		IFNULL(SUM(revenue7)/SUM(revenue1),0.00) AS lt7,
		IFNULL(SUM(revenue14)/SUM(revenue1),0.00) AS lt14,
		IFNULL(SUM(revenue30)/SUM(revenue1),0.00) AS lt30,
		IFNULL(SUM(revenue1)/SUM(spend) * 100,0.00) AS roi1,
		IFNULL(SUM(revenue3)/SUM(spend) * 100,0.00) AS roi3,
		IFNULL(SUM(revenue7)/SUM(spend) * 100,0.00) AS roi7,
		IFNULL(SUM(revenue14)/SUM(spend) * 100,0.00) AS roi14,
		IFNULL(SUM(revenue30)/SUM(spend) * 100,0.00) AS roi30,
		IFNULL(SUM(revenue_hour_24)/SUM(spend) * 100,0.00) AS revenue_hour_roi24,
		IFNULL(SUM(pay_hour_24)/SUM(spend) * 100,0.00) AS pay_hour_roi24,
		IFNULL(SUM(pay_hour_24)/SUM(pay_hour_24_user) * 100,0.00) AS pay_hour_24_arpu,
		IFNULL(SUM(payRevenue1)/SUM(spend) * 100,0.00) AS payRoi1,
		IFNULL(SUM(payRevenue3)/SUM(spend) * 100,0.00) AS payRoi3,
		IFNULL(SUM(payRevenue7)/SUM(spend) * 100,0.00) AS payRoi7,
		IFNULL(SUM(payRevenue30)/SUM(spend) * 100,0.00) AS payRoi30,
		IFNULL(SUM(revenue1+payRevenue1)/SUM(spend) * 100,0.00) AS totalRoi1,
		IFNULL(SUM(revenue3+payRevenue3)/SUM(spend) * 100,0.00) AS totalRoi3,
		IFNULL(SUM(revenue7+payRevenue7)/SUM(spend) * 100,0.00) AS totalRoi7,
		IFNULL(SUM(revenue14+payRevenue14)/SUM(spend) * 100,0.00) AS totalRoi14,
		IFNULL(SUM(revenue30+payRevenue30)/SUM(spend) * 100,0.00) AS totalRoi30,
		IFNULL((ifnull(SUM(revenue3),0.00)+ifnull(sum(payRevenue3),0.00))/(ifnull(SUM(revenue1),0.00)+ifnull(sum(payRevenue1),0.00)),0.00) AS totalLt3,
		IFNULL((ifnull(SUM(revenue7),0.00)+ifnull(sum(payRevenue7),0.00))/(ifnull(SUM(revenue1),0.00)+ifnull(sum(payRevenue1),0.00)),0.00) AS totalLt7,
		IFNULL((ifnull(SUM(revenue14),0.00)+ifnull(sum(payRevenue14),0.00))/(ifnull(SUM(revenue1),0.00)+ifnull(sum(payRevenue1),0.00)),0.00) AS totalLt14,
		IFNULL((ifnull(SUM(revenue30),0.00)+ifnull(sum(payRevenue30),0.00))/(ifnull(SUM(revenue1),0.00)+ifnull(sum(payRevenue1),0.00)),0.00) AS totalLt30,
		IFNULL(SUM(`convert`)/SUM(clicks) * 100,0.00) AS convertRate,
		IFNULL(SUM(register)/SUM(installs) * 100,0.00) AS registerRate,
		IFNULL(SUM(game_addiction)/SUM(installs) * 100,0.00) AS addictionRate,
		IFNULL(SUM(spend)/SUM(`convert`),0.00) AS convertSpend,
		IFNULL(SUM(spend)/SUM(register),0.00) AS registerSpend,
		IFNULL(SUM(spend)/SUM(game_addiction),0.00) AS addictionSpend,
		IFNULL(SUM(installs)/SUM(clicks) * 100,0.00) AS installRate,
		IFNULL(SUM(spend)/SUM(impressions)*1000,0.00) AS avgShowSpend,
		IFNULL(SUM(spend)/SUM(download),0.00) AS downloadcost,
		IFNULL(SUM(spend)/SUM(reActive),0.00) AS preReActive,
		IFNULL(SUM(spend)/SUM(uninstalls),0.00) AS preUninstalls,
		IFNULL(SUM(IFNULL(totalRevenue1,0.00)+IFNULL(payRevenue,0.00))/SUM(rebateSpend),0.00) AS buyROI,
		IFNULL(SUM(spend)/SUM(payCount),0.00) AS activePayCost,
		IFNULL(SUM(payCount)/SUM(installs),0.00) AS installsActivePayRate,
		IFNULL(SUM(payCount)/SUM(register),0.00) AS registerActivePayRate,
		IFNULL(SUM(spend)/SUM(addPayCount),0.00) AS addPayCost,
		IFNULL(SUM(addPayCount)/SUM(installs),0.00) AS installsAddPayRate,
		IFNULL(SUM(addPayCount)/SUM(register),0.00) AS registerAddPayRate,
		IFNULL(SUM(spend)/SUM(gamePayCount),0.00) AS gamePayCost,
		IFNULL(SUM(payCount7)/SUM(payCountFirst7),0.00) AS payCountPer7,
		IFNULL(SUM(retain1)/SUM(installs),0.00) AS installsRetainRate1,
		IFNULL(SUM(retain3)/SUM(installs),0.00) AS installsRetainRate3,
		IFNULL(SUM(retain7)/SUM(installs),0.00) AS installsRetainRate7,
		IFNULL(SUM(retain1)/SUM(register),0.00) AS registerRetainRate1,
		IFNULL(SUM(retain3)/SUM(register),0.00) AS registerRetainRate3,
		IFNULL(SUM(retain7)/SUM(register),0.00) AS registerRetainRate7,
		IFNULL(SUM(rebateSpend)/SUM(b_install),0.00) AS b_cpa,
		IFNULL(SUM(b_addRevenue)/SUM(rebateSpend),0.00) AS b_roi1,
		IFNULL(SUM(b_addRevenue)/SUM(b_install),0.00) AS b_addArpu,
		IFNULL(SUM(b_activeRevenue)/SUM(b_active),0.00) AS b_activeArpu,
		IFNULL(SUM(b_addPlaque)/SUM(b_install),0.00) AS b_pvPlaque,
		IFNULL(SUM(b_addVideo)/SUM(b_install),0.00) AS b_pvVideo,
		IFNULL(SUM(b_retain1)/SUM(b_install),0.00) AS b_retainRate1,
		IFNULL(SUM(beforeRevenue1)/SUM(spend),0.00) AS beforeRoi1,
		IFNULL(SUM(recoveryRevenue)/SUM(spend),0.00) AS recoveryRoi1,
		IFNULL(SUM(beforePayRevenue1)/SUM(spend),0.00) AS beforePayRoi1,
		IFNULL(SUM(recoveryPayRevenue)/SUM(spend),0.00) AS recoveryPayRoi1,
		IFNULL(SUM(recoveryRevenue)/SUM(recoveryRevenue),0.00) AS revenueRate1,
		IFNULL(SUM(recoveryPayRevenue)/SUM(beforePayRevenue1),0.00) AS payRevenueRate1
		,ifnull(sum(backActivateCount),0.00) as backActivateCount
		,ifnull(convert(sum(backActivateCount)/sum(installs)*100,decimal(10,2)),0.00) as backActivateRate
		,ifnull(convert(sum(backActiveJ)/sum(installs)*100,decimal(10,2)),0.00) as activeJRate
		,ifnull(sum(activeJ),0) activeJ
		,ifnull(sum(registerJ),0) registerJ
		,ifnull(sum(backActiveJ),0) backActiveJ
		,ifnull(sum(backRegisterJ),0) backRegisterJ
		,ifnull(convert(sum(spend)/sum(activeJ),decimal(10,2)),0.0) activeJSpend
		,ifnull(convert(sum(spend)/sum(backActivateCount),decimal(10,2)),0.0) activeZSpend
		,ifnull(convert(sum(spend)/sum(registerJ),decimal(10,2)),0.0) registerJSpend
		,ifnull(convert(sum(spend)/sum(backActiveJ),decimal(10,2)),0.0) backActiveJSpend
		,ifnull(convert(sum(spend)/sum(backRegisterJ),decimal(10,2)),0.0) backRegisterJSpend
		,ifnull(sum(revenue1)/sum(register),0.00) as add_arpu_register
		,ifnull(sum(revenue1)/sum(first_cash_user),0.00) as add_arpu_cash
		,ifnull(sum(first_cash_user)/sum(register)*100,0.00) as adv_perm_rate
		,ifnull(sum(cash_user)/sum(register)*100,0.00) as register_cash_rate
		,ifnull(sum(first_cash_user),0) first_cash_user
		,ifnull(sum(cash_user),0) cash_user
		,ifnull(convert(sum(revenue1)/sum(download),decimal(10,2)),0.00) down_ltv1
		,convert(ifnull(sum(ticket),0),decimal(10,2)) ticket
		<if test="media != null and media.contains('oppo')">
		       ,ifnull(sum(oppoRealTimeRevenue)/100,0) realTimeRevenue
				,ifnull(sum(oppoRealTimeRevenue)/100/SUM(spend),0) realTimeRoi
		</if>
		<if test="group != null and group.size > 0">
			,
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'campaign'">
						campaign, bid
					</when>
					<when test="item == 'groupName'">
						c.groupName AS groupName
					</when>
					<when test="item == 'groupId'">
						c.groupId AS groupId
					</when>
					<when test="item == 'appCategory'">
						a.app_category AS appCategory
					</when>
					<when test="item == 'agent'">
						first_agent, agent
					</when>
					<when test="item == 'week'">
						DATE_FORMAT(day, '%x-%v') as day,DATE_FORMAT(day, '%x-%v')
					</when>
					<when test="item == 'month'">
						DATE_FORMAT(day,'%Y-%m') as day,
						DATE_FORMAT(day,'%Y-%m')
					</when>
					<when test="item == 'beek'">
						CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS day
						,CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0")) as beek
					</when>
					<when test="item == 'planId'">
						planId, planName
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		<if test="custom_date != null and custom_date.size() > 0">
			,concat(#{start_date},'至',#{end_date}) as day
		</if>
		FROM dnwx_adt.dn_report_spend_china c
		LEFT JOIN dnwx_adt.app_info a on c.app = a.id
		<if test="media != null and media.contains('oppo')">
		LEFT JOIN dnwx_adt.dn_report_spend_oppo_realtime ort
		    on ort.`tdate` = c.`day` and ort.campaign_id = c.campaignId
		</if>
		left join dnwx_adt.oppo_group_info op on c.groupId = op.adGroupId
		<include refid="getSpendReportCondition1"/>
		<include refid="getSpendReportCondition2"/>
	</select>

	<select id="getSpendReportSummary" resultType="com.wbgame.pojo.jettison.report.dto.SpendReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.SpendReportParam">
		SELECT media,SUM(spend) AS spend,SUM(reActiveSpend) AS reActiveSpend,SUM(uninstallsSpend) AS uninstallsSpend, SUM(rebateSpend) AS rebateSpend,SUM(dollarSpend) AS dollarSpend,SUM(dollarRebateSpend) AS dollarRebateSpend,
		SUM(installs) AS installs, SUM(impressions) AS impressions, SUM(clicks) AS clicks,
		SUM(revenue) AS revenue,SUM(revenue1) AS revenue1, SUM(revenue3) AS revenue3, SUM(revenue7) AS revenue7,SUM(revenue14) AS revenue14,SUM(revenue30) AS revenue30,
		SUM(retain1) retain1, SUM(retain3) retain3, SUM(retain7) retain7, SUM(`convert`) AS `convert`,sum(revenue_hour_24) revenue_hour_24,sum(pay_hour_24) pay_hour_24,sum(pay_hour_24_user) pay_hour_24_user,
		SUM(payRevenue) AS payRevenue,SUM(payRevenue1) AS payRevenue1, SUM(payRevenue3) AS payRevenue3, SUM(payRevenue7) AS payRevenue7, SUM(payRevenue14) AS payRevenue14, SUM(payRevenue30) AS payRevenue30,
		SUM(totalRevenue) AS totalRevenue,SUM(register) AS register, SUM(game_addiction) AS game_addiction,SUM(download) AS download,
		SUM(reActive) AS reActive,SUM(uninstalls) AS uninstalls,SUM(cashSpend) AS cashSpend,SUM(virtualSpend) AS virtualSpend,
		SUM(addPayCount) AS addPayCount,SUM(payCount) AS payCount,SUM(gamePayCount) AS gamePayCount,SUM(payCount7) AS payCount7,SUM(payCountFirst7) AS payCountFirst7,
		SUM(b_install) AS b_install,SUM(b_active) AS b_active,SUM(b_addRevenue) AS b_addRevenue,SUM(b_activeRevenue) AS b_activeRevenue,
		SUM(b_retain1) AS b_retain1,SUM(b_addVideo) AS b_addVideo,SUM(b_addPlaque) AS b_addPlaque,
		SUM(beforeRevenue1) AS beforeRevenue1,SUM(beforePayRevenue1) AS beforePayRevenue1,
		SUM(recoveryRevenue) AS recoveryRevenue,SUM(recoveryPayRevenue) AS recoveryPayRevenue,sum(backActivateCount) as backActivateCount
		,sum(first_cash_user) first_cash_user ,sum(cash_user) cash_user,ifnull(convert(sum(revenue1)/sum(download),decimal(10,2)),0.00) down_ltv1
		,convert(sum(ticket),decimal(10,2)) AS ticket
		,ifnull(convert(sum(backActivateCount)/sum(installs)*100,decimal(10,2)),0.00) as backActivateRate
		,ifnull(convert(sum(backActiveJ)/sum(installs)*100,decimal(10,2)),0.00) as activeJRate
		,ifnull(sum(activeJ),0) activeJ
		,ifnull(sum(registerJ),0) registerJ
		,ifnull(sum(backActiveJ),0) backActiveJ
		,ifnull(sum(backRegisterJ),0) backRegisterJ
		,ifnull(convert(sum(spend)/sum(activeJ),decimal(10,2)),0.0) activeJSpend
		,ifnull(convert(sum(spend)/sum(backActivateCount),decimal(10,2)),0.0) activeZSpend
		,ifnull(convert(sum(spend)/sum(registerJ),decimal(10,2)),0.0) registerJSpend
		,ifnull(convert(sum(spend)/sum(backActiveJ),decimal(10,2)),0.0) backActiveJSpend
		,ifnull(convert(sum(spend)/sum(backRegisterJ),decimal(10,2)),0.0) backRegisterJSpend
		<if test="media != null and media.contains('oppo')">
		       ,ifnull(sum(realTimeRevenue),0) realTimeRevenue
		</if>
		FROM (
		SELECT SUM(spend) AS spend,SUM(reActiveSpend) AS reActiveSpend,SUM(uninstallsSpend) AS uninstallsSpend, SUM(rebateSpend) AS rebateSpend,SUM(dollarSpend) AS dollarSpend,SUM(dollarRebateSpend) AS dollarRebateSpend,
		SUM(installs) AS installs, SUM(impressions) AS impressions, SUM(clicks) AS clicks,
		SUM(totalRevenue1) AS revenue,SUM(revenue1) AS revenue1, SUM(revenue3) AS revenue3, SUM(revenue7) AS revenue7,SUM(revenue14) AS revenue14,SUM(revenue30) AS revenue30,
		SUM(retain1) retain1, SUM(retain3) retain3, SUM(retain7) retain7, SUM(`convert`) AS `convert`,sum(revenue_hour_24) revenue_hour_24,sum(pay_hour_24) pay_hour_24,sum(pay_hour_24_user) pay_hour_24_user,
		SUM(payRevenue) AS payRevenue,SUM(payRevenue1) AS payRevenue1, SUM(payRevenue3) AS payRevenue3, SUM(payRevenue7) AS payRevenue7, SUM(payRevenue14) AS payRevenue14, SUM(payRevenue30) AS payRevenue30,
		SUM(IFNULL(totalRevenue1,0.00)+IFNULL(payRevenue,0.00)) AS totalRevenue,SUM(register) AS register, SUM(game_addiction) AS game_addiction,SUM(download) AS download,
		SUM(reActive) AS reActive,SUM(uninstalls) AS uninstalls,SUM(cashSpend) AS cashSpend,SUM(virtualSpend) AS virtualSpend,
		SUM(addPayCount) AS addPayCount,SUM(payCount) AS payCount,SUM(gamePayCount) AS gamePayCount,SUM(payCount7) AS payCount7,SUM(payCountFirst7) AS payCountFirst7,
		SUM(b_install) AS b_install,SUM(b_active) AS b_active,SUM(b_addRevenue) AS b_addRevenue,SUM(b_activeRevenue) AS b_activeRevenue,
		SUM(b_retain1) AS b_retain1,SUM(b_addVideo) AS b_addVideo,SUM(b_addPlaque) AS b_addPlaque,
		SUM(beforeRevenue1) AS beforeRevenue1,SUM(beforePayRevenue1) AS beforePayRevenue1,
		SUM(recoveryRevenue) AS recoveryRevenue,SUM(recoveryPayRevenue) AS recoveryPayRevenue,
		IFNULL(SUM(installs)/SUM(impressions) * 100,0.00) AS impressionConvertRate,
		IFNULL(SUM(installs)/SUM(clicks) * 100,0.00) AS clickConvertRate,
		IFNULL(SUM(clicks)/SUM(impressions) * 100,0.00) AS clickRate,
		IFNULL(SUM(rebateSpend)/SUM(installs),0.00) AS cpi,
		IFNULL(SUM(revenue1)/SUM(clicks),0.00) AS clickLtv1,
		IFNULL(SUM(revenue1)/SUM(installs),0.00) AS installsLtv1,
		IFNULL(SUM(revenue3)/SUM(installs),0.00) AS installsLtv3,
		IFNULL(SUM(revenue7)/SUM(installs),0.00) AS installsLtv7,
		IFNULL(SUM(revenue14)/SUM(installs),0.00) AS installsLtv14,
		IFNULL(SUM(revenue30)/SUM(installs),0.00) AS installsLtv30,
		IFNULL(SUM(revenue1)/SUM(register),0.00) AS registerLtv1,
		IFNULL(SUM(revenue3)/SUM(register),0.00) AS registerLtv3,
		IFNULL(SUM(revenue7)/SUM(register),0.00) AS registerLtv7,
		IFNULL(SUM(revenue14)/SUM(register),0.00) AS registerLtv14,
		IFNULL(SUM(revenue30)/SUM(register),0.00) AS registerLtv30,
		IFNULL(SUM(payRevenue1)/SUM(installs),0.00) AS installsPayLtv1,
		IFNULL(SUM(payRevenue3)/SUM(installs),0.00) AS installsPayLtv3,
		IFNULL(SUM(payRevenue7)/SUM(installs),0.00) AS installsPayLtv7,
		IFNULL(SUM(payRevenue30)/SUM(installs),0.00) AS installsPayLtv30,
		IFNULL(SUM(payRevenue1)/SUM(register),0.00) AS registerPayLtv1,
		IFNULL(SUM(payRevenue3)/SUM(register),0.00) AS registerPayLtv3,
		IFNULL(SUM(payRevenue7)/SUM(register),0.00) AS registerPayLtv7,
		IFNULL(SUM(payRevenue30)/SUM(register),0.00) AS registerPayLtv30,
		IFNULL(SUM(revenue1+payRevenue1)/SUM(installs),0.00) AS installsTotalLtv1,
		IFNULL(SUM(revenue3+payRevenue3)/SUM(installs),0.00) AS installsTotalLtv3,
		IFNULL(SUM(revenue7+payRevenue7)/SUM(installs),0.00) AS installsTotalLtv7,
		IFNULL(SUM(revenue30+payRevenue30)/SUM(installs),0.00) AS installsTotalLtv30,
		IFNULL(SUM(revenue1+payRevenue1)/SUM(register),0.00) AS registerTotalLtv1,
		IFNULL(SUM(revenue3+payRevenue3)/SUM(register),0.00) AS registerTotalLtv3,
		IFNULL(SUM(revenue7+payRevenue7)/SUM(register),0.00) AS registerTotalLtv7,
		IFNULL(SUM(revenue30+payRevenue30)/SUM(register),0.00) AS registerTotalLtv30,
		IFNULL(SUM(revenue3)/SUM(revenue1),0.00) AS lt3,
		IFNULL(SUM(revenue7)/SUM(revenue1),0.00) AS lt7,
		IFNULL(SUM(revenue14)/SUM(revenue1),0.00) AS lt14,
		IFNULL(SUM(revenue30)/SUM(revenue1),0.00) AS lt30,
		IFNULL(SUM(revenue1)/SUM(spend) * 100,0.00) AS roi1,
		IFNULL(SUM(revenue3)/SUM(spend) * 100,0.00) AS roi3,
		IFNULL(SUM(revenue7)/SUM(spend) * 100,0.00) AS roi7,
		IFNULL(SUM(revenue14)/SUM(spend) * 100,0.00) AS roi14,
		IFNULL(SUM(revenue30)/SUM(spend) * 100,0.00) AS roi30,
		IFNULL(SUM(revenue_hour_24)/SUM(spend) * 100,0.00) AS revenue_hour_roi24,
		IFNULL(SUM(pay_hour_24)/SUM(spend) * 100,0.00) AS pay_hour_roi24,
		IFNULL(SUM(pay_hour_24)/SUM(pay_hour_24_user) * 100,0.00) AS pay_hour_24_arpu,
		IFNULL(SUM(payRevenue1)/SUM(spend) * 100,0.00) AS payRoi1,
		IFNULL(SUM(payRevenue3)/SUM(spend) * 100,0.00) AS payRoi3,
		IFNULL(SUM(payRevenue7)/SUM(spend) * 100,0.00) AS payRoi7,
		IFNULL(SUM(payRevenue30)/SUM(spend) * 100,0.00) AS payRoi30,
		IFNULL(SUM(revenue1+payRevenue1)/SUM(spend) * 100,0.00) AS totalRoi1,
		IFNULL(SUM(revenue3+payRevenue3)/SUM(spend) * 100,0.00) AS totalRoi3,
		IFNULL(SUM(revenue7+payRevenue7)/SUM(spend) * 100,0.00) AS totalRoi7,
		IFNULL(SUM(revenue14+payRevenue14)/SUM(spend) * 100,0.00) AS totalRoi14,
		IFNULL(SUM(revenue30+payRevenue30)/SUM(spend) * 100,0.00) AS totalRoi30,
		IFNULL((ifnull(SUM(revenue3),0.00)+ifnull(sum(payRevenue3),0.00))/(ifnull(SUM(revenue1),0.00)+ifnull(sum(payRevenue1),0.00)),0.00) AS totalLt3,
		IFNULL((ifnull(SUM(revenue7),0.00)+ifnull(sum(payRevenue7),0.00))/(ifnull(SUM(revenue1),0.00)+ifnull(sum(payRevenue1),0.00)),0.00) AS totalLt7,
		IFNULL((ifnull(SUM(revenue14),0.00)+ifnull(sum(payRevenue14),0.00))/(ifnull(SUM(revenue1),0.00)+ifnull(sum(payRevenue1),0.00)),0.00) AS totalLt14,
		IFNULL((ifnull(SUM(revenue30),0.00)+ifnull(sum(payRevenue30),0.00))/(ifnull(SUM(revenue1),0.00)+ifnull(sum(payRevenue1),0.00)),0.00) AS totalLt30,
		IFNULL(SUM(`convert`)/SUM(clicks) * 100,0.00) AS convertRate,
		IFNULL(SUM(register)/SUM(installs) * 100,0.00) AS registerRate,
		IFNULL(SUM(game_addiction)/SUM(installs) * 100,0.00) AS addictionRate,
		IFNULL(SUM(spend)/SUM(`convert`),0.00) AS convertSpend,
		IFNULL(SUM(spend)/SUM(register),0.00) AS registerSpend,
		IFNULL(SUM(spend)/SUM(game_addiction),0.00) AS addictionSpend,
		IFNULL(SUM(installs)/SUM(clicks) * 100,0.00) AS installRate,
		IFNULL(SUM(spend)/SUM(impressions)*1000,0.00) AS avgShowSpend,
		IFNULL(SUM(spend)/SUM(download),0.00) AS downloadcost,
		IFNULL(SUM(spend)/SUM(reActive),0.00) AS preReActive,
		IFNULL(SUM(spend)/SUM(uninstalls),0.00) AS preUninstalls,
		IFNULL(SUM(IFNULL(totalRevenue1,0.00)+IFNULL(payRevenue,0.00))/SUM(rebateSpend),0.00) AS buyROI,
		IFNULL(SUM(spend)/SUM(payCount),0.00) AS activePayCost,
		IFNULL(SUM(payCount)/SUM(installs),0.00) AS installsActivePayRate,
		IFNULL(SUM(payCount)/SUM(register),0.00) AS registerActivePayRate,
		IFNULL(SUM(spend)/SUM(addPayCount),0.00) AS addPayCost,
		IFNULL(SUM(addPayCount)/SUM(installs),0.00) AS installsAddPayRate,
		IFNULL(SUM(addPayCount)/SUM(register),0.00) AS registerAddPayRate,
		IFNULL(SUM(spend)/SUM(gamePayCount),0.00) AS gamePayCost,
		IFNULL(SUM(payCount7)/SUM(payCountFirst7),0.00) AS payCountPer7,
		IFNULL(SUM(retain1)/SUM(installs),0.00) AS installsRetainRate1,
		IFNULL(SUM(retain3)/SUM(installs),0.00) AS installsRetainRate3,
		IFNULL(SUM(retain7)/SUM(installs),0.00) AS installsRetainRate7,
		IFNULL(SUM(retain1)/SUM(register),0.00) AS registerRetainRate1,
		IFNULL(SUM(retain3)/SUM(register),0.00) AS registerRetainRate3,
		IFNULL(SUM(retain7)/SUM(register),0.00) AS registerRetainRate7,
		IFNULL(SUM(rebateSpend)/SUM(b_install),0.00) AS b_cpa,
		IFNULL(SUM(b_addRevenue)/SUM(rebateSpend),0.00) AS b_roi1,
		IFNULL(SUM(b_addRevenue)/SUM(b_install),0.00) AS b_addArpu,
		IFNULL(SUM(b_activeRevenue)/SUM(b_active),0.00) AS b_activeArpu,
		IFNULL(SUM(b_addPlaque)/SUM(b_install),0.00) AS b_pvPlaque,
		IFNULL(SUM(b_addVideo)/SUM(b_install),0.00) AS b_pvVideo,
		IFNULL(SUM(b_retain1)/SUM(b_install),0.00) AS b_retainRate1,
		IFNULL(SUM(beforeRevenue1)/SUM(spend),0.00) AS beforeRoi1,
		IFNULL(SUM(recoveryRevenue)/SUM(spend),0.00) AS recoveryRoi1,
		IFNULL(SUM(beforePayRevenue1)/SUM(spend),0.00) AS beforePayRoi1,
		IFNULL(SUM(recoveryPayRevenue)/SUM(spend),0.00) AS recoveryPayRoi1,
		IFNULL(SUM(recoveryRevenue)/SUM(recoveryRevenue),0.00) AS revenueRate1,
		IFNULL(SUM(recoveryPayRevenue)/SUM(beforePayRevenue1),0.00) AS payRevenueRate1
		,ifnull(convert(sum(backActivateCount)/sum(installs)*100,decimal(10,2)),0.00) as backActivateRate
		,ifnull(convert(sum(backActiveJ)/sum(installs)*100,decimal(10,2)),0.00) as activeJRate
		,ifnull(sum(activeJ),0) activeJ
		,ifnull(sum(registerJ),0) registerJ
		,ifnull(sum(backActiveJ),0) backActiveJ
		,ifnull(sum(backRegisterJ),0) backRegisterJ
		,ifnull(convert(sum(spend)/sum(activeJ),decimal(10,2)),0.0) activeJSpend
		,ifnull(convert(sum(spend)/sum(backActivateCount),decimal(10,2)),0.0) activeZSpend
		,ifnull(convert(sum(spend)/sum(registerJ),decimal(10,2)),0.0) registerJSpend
		,ifnull(convert(sum(spend)/sum(backActiveJ),decimal(10,2)),0.0) backActiveJSpend
		,ifnull(convert(sum(spend)/sum(backRegisterJ),decimal(10,2)),0.0) backRegisterJSpend
		,ifnull(sum(backActivateCount)) as backActivateCount
		,ifnull(sum(revenue1)/sum(register),0.00) as add_arpu_register
		,ifnull(sum(revenue1)/sum(first_cash_user),0.00) as add_arpu_cash
		,ifnull(sum(first_cash_user)/sum(register)*100,0.00) as adv_perm_rate
		,ifnull(sum(cash_user)/sum(register)*100,0.00) as register_cash_rate
		,ifnull(sum(first_cash_user),0) first_cash_user
		,ifnull(sum(cash_user),0) cash_user
		,ifnull(convert(sum(revenue1)/sum(download),decimal(10,2)),0.00) down_ltv1
		,ifnull(sum(ticket),0) ticket
		<if test="media != null and media.contains('oppo')">
		       ,ifnull(sum(oppoRealTimeRevenue)/100,0) realTimeRevenue
		</if>
		       <if test="group != null and group.size > 0">
			,
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'campaign'">
						campaignId, campaign, bid
					</when>
					<when test="item == 'groupId'">
						c.groupId as groupId, c.groupName as groupName
					</when>
					<when test="item == 'appCategory'">
						a.app_category as appCategory
					</when>
					<when test="item == 'agent'">
						first_agent, agent
					</when>
					<when test="item == 'week'">
						DATE_FORMAT(day, '%x-%v') as day,DATE_FORMAT(day, '%x-%v')
					</when>
					<when test="item == 'month'">
						DATE_FORMAT(day,'%Y-%m') as day,
						DATE_FORMAT(day,'%Y-%m')
					</when>
					<when test="item == 'beek'">
						CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS day
						,CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0")) as beek
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		<if test="custom_date != null and custom_date.size() > 0">
			,concat(#{start_date},'至',#{end_date}) as day
		</if>
		FROM dnwx_adt.dn_report_spend_china c
		LEFT JOIN dnwx_adt.app_info a on c.app = a.id
		<if test="media != null and media.contains('oppo')">
		LEFT JOIN dnwx_adt.dn_report_spend_oppo_realtime ort
		    on ort.`tdate` = c.`day` and ort.campaign_id = c.campaignId
		</if>
		left join dnwx_adt.oppo_group_info op on c.groupId = op.adGroupId
		<include refid="getSpendReportCondition1"/>
		<include refid="getSpendReportCondition2"/>
		) a
	</select>

	<select id="getDirectReport" resultType="com.wbgame.pojo.jettison.report.dto.DirectReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.DirectReportParam">
		SELECT SUM(umAddNum) AS umAddNum, SUM(umActiveNum) AS umActiveNum,
		SUM(channelAddNum) AS channelAddNum, SUM(channelActiveNum) AS channelActiveNum,SUM(revenue) AS revenue,
		SUM(channelSpend) AS channelSpend, SUM(channelBuyAdd) AS channelBuyAdd,
		SUM(ttDirectSpend) AS ttDirectSpend,SUM(ttDirectBuyAdd) AS ttDirectBuyAdd,
		SUM(ksDirectSpend) AS ksDirectSpend,SUM(ksDirectBuyAdd) AS ksDirectBuyAdd,
		SUM(gdtDirectSpend) AS gdtDirectSpend,SUM(gdtDirectBuyAdd) AS gdtDirectBuyAdd,
		SUM(directSpend) AS directSpend,SUM(directBuyAdd) AS directBuyAdd,SUM(afterPayRevenue) AS afterPayRevenue,SUM(payRevenue) AS payRevenue,
		SUM(ttRevenue1) AS ttRevenue1,SUM(ttPayRevenue1) AS ttPayRevenue1,SUM(ttAfterPayRevenue1) AS ttAfterPayRevenue1,SUM(ttAddPayCount) AS ttAddPayCount,
		SUM(ksRevenue1) AS ksRevenue1,SUM(ksPayRevenue1) AS ksPayRevenue1,SUM(ksAfterPayRevenue1) AS ksAfterPayRevenue1,SUM(ksAddPayCount) AS ksAddPayCount,
		SUM(gdtRevenue1) AS gdtRevenue1,SUM(gdtPayRevenue1) AS gdtPayRevenue1,SUM(gdtAfterPayRevenue1) AS gdtAfterPayRevenue1,SUM(gdtAddPayCount) AS gdtAddPayCount,
		SUM(IFNULL(channelSpend,0)+IFNULL(directSpend,0)) AS spend,
		SUM(IFNULL(afterPayRevenue,0)+IFNULL(revenue,0)) AS totalRevenue,
		IFNULL(SUM(IFNULL(afterPayRevenue,0)+IFNULL(revenue,0))-SUM(IFNULL(channelSpend,0)+IFNULL(directSpend,0)),0.00) AS profit,
		IFNULL((SUM(IFNULL(revenue,0)+IFNULL(afterPayRevenue,0)))/(SUM(IFNULL(channelSpend,0)+IFNULL(directSpend,0))),0.00) AS roi,
		IFNULL(SUM(revenue)/SUM(IFNULL(channelSpend,0)+IFNULL(directSpend,0)),0.00) AS revenueRoi,
		IFNULL(SUM(afterPayRevenue)/SUM(IFNULL(channelSpend,0)+IFNULL(directSpend,0)),0.00) AS payRoi,
		IFNULL(SUM(channelSpend)/SUM(channelBuyAdd),0.00) AS channelCost,
		IFNULL(SUM(ttDirectSpend)/SUM(ttDirectBuyAdd),0.00) AS ttDirectCost,
		IFNULL(SUM(ksDirectSpend)/SUM(ksDirectBuyAdd),0.00) AS ksDirectCost,
		IFNULL(SUM(gdtDirectSpend)/SUM(gdtDirectBuyAdd),0.00) AS gdtDirectCost,
		IFNULL(SUM(directSpend)/SUM(directBuyAdd),0.00) AS directCost,
		ifnull(sum(revenueChannel),0.00) revenueChannel,ifnull(sum(revenueSelf),0.00) revenueSelf,
		IFNULL(SUM(ttDirectSpend)/SUM(ttAddPayCount),0.00) AS ttPayCost,
		IFNULL(SUM(ttPayRevenue1)/SUM(ttDirectBuyAdd),0.00) AS ttPayLtv1,
		IFNULL(SUM(ttRevenue1)/SUM(ttDirectBuyAdd),0.00) AS ttRevenueLtv1,
		IFNULL(SUM(ttPayRevenue1+ttRevenue1)/SUM(ttDirectBuyAdd),0.00) AS ttLtv1,
		IFNULL(SUM(ttAddPayCount)/SUM(ttDirectBuyAdd),0.00) AS ttAddPayRate,
		IFNULL(SUM(IFNULL(ttAfterPayRevenue1,0)+IFNULL(ttRevenue1,0))/SUM(ttDirectSpend),0.00) AS ttRoi1,
		IFNULL(SUM(ttRevenue1)/SUM(ttDirectSpend),0.00) AS ttRevenueRoi1,
		IFNULL(SUM(ttPayRevenue1)/SUM(ttDirectSpend),0.00) AS ttPayRoi1,
		IFNULL(SUM(ttAfterPayRevenue1)/SUM(ttDirectSpend),0.00) AS ttAfterPayRoi1,

		IFNULL(SUM(ksDirectSpend)/SUM(ksAddPayCount),0.00) AS ksPayCost,
		IFNULL(SUM(ksPayRevenue1)/SUM(ksDirectBuyAdd),0.00) AS ksPayLtv1,
		IFNULL(SUM(ksRevenue1)/SUM(ksDirectBuyAdd),0.00) AS ksRevenueLtv1,
		IFNULL(SUM(ksPayRevenue1+ksRevenue1)/SUM(ksDirectBuyAdd),0.00) AS ksLtv1,
		IFNULL(SUM(ksAddPayCount)/SUM(ksDirectBuyAdd),0.00) AS ksAddPayRate,
		IFNULL(SUM(IFNULL(ksAfterPayRevenue1,0)+IFNULL(ksRevenue1,0))/SUM(ksDirectSpend),0.00) AS ksRoi1,
		IFNULL(SUM(ksRevenue1)/SUM(ksDirectSpend),0.00) AS ksRevenueRoi1,
		IFNULL(SUM(ksPayRevenue1)/SUM(ksDirectSpend),0.00) AS ksPayRoi1,
		IFNULL(SUM(ksAfterPayRevenue1)/SUM(ksDirectSpend),0.00) AS ksAfterPayRoi1,

		IFNULL(SUM(gdtDirectSpend)/SUM(gdtAddPayCount),0.00) AS gdtPayCost,
		IFNULL(SUM(gdtPayRevenue1)/SUM(gdtDirectBuyAdd),0.00) AS gdtPayLtv1,
		IFNULL(SUM(gdtRevenue1)/SUM(gdtDirectBuyAdd),0.00) AS gdtRevenueLtv1,
		IFNULL(SUM(gdtPayRevenue1+gdtRevenue1)/SUM(gdtDirectBuyAdd),0.00) AS gdtLtv1,
		IFNULL(SUM(gdtAddPayCount)/SUM(gdtDirectBuyAdd),0.00) AS gdtAddPayRate,
		IFNULL(SUM(IFNULL(gdtAfterPayRevenue1,0)+IFNULL(gdtRevenue1,0))/SUM(gdtDirectSpend),0.00) AS gdtRoi1,
		IFNULL(SUM(gdtRevenue1)/SUM(gdtDirectSpend),0.00) AS gdtRevenueRoi1,
		IFNULL(SUM(gdtPayRevenue1)/SUM(gdtDirectSpend),0.00) AS gdtPayRoi1,
		IFNULL(SUM(gdtAfterPayRevenue1)/SUM(gdtDirectSpend),0.00) AS gdtAfterPayRoi1,
		IFNULL(convert(SUM(directTotalRevenue1),decimal(10,2)),0.00) AS directTotalRevenue1,
		IFNULL(convert(SUM(channelBuyRevenue1),decimal(10,2)),0.00) AS channelBuyRevenue1,
		IFNULL(convert(SUM(channelBuyRevenue1)/SUM(channelSpend)*100,decimal(10,2)),0.00) AS channelRoi1,
		IFNULL(convert(SUM(directTotalRevenue1)/SUM(directSpend)*100,decimal(10,2)),0.00) AS directRoi1,
		convert(IFNULL(SUM(directTotalRevenue1),0.00)+IFNULL(SUM(channelBuyRevenue1),0.00),decimal(10,2)) AS totalBuyRevenue1,
		IFNULL(convert((IFNULL(SUM(directTotalRevenue1),0.00)+IFNULL(SUM(channelBuyRevenue1),0.00))/(IFNULL(SUM(channelSpend),0.00)+IFNULL(SUM(directSpend),0.00))*100,decimal(10,2)),0.00) AS roi1,
		IFNULL(convert(SUM(directBuyRevenueTotal),decimal(10,2)),0.00) AS directBuyRevenueTotal,
		IFNULL(convert(SUM(channelBuyRevenueTotal),decimal(10,2)),0.00) AS channelBuyRevenueTotal,
		SUM(IFNULL(directBuyRevenueTotal,0)+IFNULL(channelBuyRevenueTotal,0)) AS buyRevenueTotal,
		IFNULL((SUM(IFNULL(directBuyRevenueTotal,0)+IFNULL(channelBuyRevenueTotal,0)))/(SUM(IFNULL(channelSpend,0)+IFNULL(directSpend,0))),0.00) AS buyRoi,
		IFNULL(SUM(channelBuyRevenueTotal)/SUM(channelSpend),0.00) AS channelBuyRoi,
		IFNULL(SUM(directBuyRevenueTotal)/SUM(directSpend),0.00) AS directBuyRoi

		<if test="choose == 'channel'">
			,IFNULL(SUM(IFNULL(channelSpend,0)+IFNULL(directSpend,0))/SUM(channelAddNum),0.00) AS addCost,
			IFNULL(SUM(revenueChannel)/SUM(channelActiveNum),0.00) AS activeArpuChannel,
			IFNULL(SUM(revenueSelf)/SUM(channelActiveNum),0.00) AS activeArpuSelf,
			IFNULL(SUM(revenue)/SUM(channelActiveNum),0.00) AS activeArpu,
			IFNULL(SUM(IFNULL(channelAddNum,0)-IFNULL(channelBuyAdd,0)-IFNULL(directBuyAdd,0)),0) AS nativeAddNum,
			IFNULL(SUM(IFNULL(channelBuyAdd,0)+IFNULL(directBuyAdd,0))/SUM(channelAddNum),0.00) AS buyAddRate,
			IFNULL(SUM(channelAddNum)/SUM(channelActiveNum),0.00) AS addRate,
			IFNULL(SUM(afterPayRevenue)/SUM(channelActiveNum),0.00) AS payArpu,
			IFNULL(SUM(IFNULL(afterPayRevenue,0)+IFNULL(revenue,0))/SUM(channelActiveNum),0.00) AS totalArpu
		</if>
		<if test="choose == 'um'">
			,IFNULL(SUM(IFNULL(channelSpend,0)+IFNULL(directSpend,0))/SUM(umAddNum),0.00) AS addCost,
			IFNULL(SUM(revenueChannel)/SUM(umActiveNum),0.00) AS activeArpuChannel,
			IFNULL(SUM(revenueSelf)/SUM(umActiveNum),0.00) AS activeArpuSelf,
			IFNULL(SUM(revenue)/SUM(umActiveNum),0.00) AS activeArpu,
			IFNULL(SUM(IFNULL(umAddNum,0)-IFNULL(channelBuyAdd,0)-IFNULL(directBuyAdd,0)),0) AS nativeAddNum,
			IFNULL(SUM(IFNULL(channelBuyAdd,0)+IFNULL(directBuyAdd,0))/SUM(umAddNum),0.00) AS buyAddRate,
			IFNULL(SUM(umAddNum)/SUM(umActiveNum),0.00) AS addRate,
			IFNULL(SUM(afterPayRevenue)/SUM(umActiveNum),0.00) AS payArpu,
			IFNULL(SUM(IFNULL(afterPayRevenue,0)+IFNULL(revenue,0))/SUM(umActiveNum),0.00) AS totalArpu
		</if>
		<if test="group != null and group.size > 0">
			,
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'app'">
						d.app, d.app as appId
					</when>
					<when test="item == 'appCategory'">
						a.app_category as appCategory
					</when>
					<when test="item == 'week'">
						DATE_FORMAT(day, '%x-%v') as day,DATE_FORMAT(day, '%x-%v')
					</when>
					<when test="item == 'month'">
						DATE_FORMAT(day,'%Y-%m') as day,
						DATE_FORMAT(day,'%Y-%m')
					</when>
					<when test="item == 'beek'">
						CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS day
						,CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0")) as beek
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		<if test="custom_date != null and custom_date.size() > 0">
			,concat(#{start_date},'至',#{end_date}) as day
		</if>
		FROM dnwx_adt.dn_direct_spend_report d
		LEFT JOIN dnwx_adt.app_info a on d.app = a.id
		<include refid="getDirectReportCondition1"/>
		<include refid="getDirectReportCondition2"/>
		<if test="order_str != null and order_str != ''">
			ORDER BY ${order_str}
		</if>
	</select>

	<select id="getDirectReportSummary" resultType="com.wbgame.pojo.jettison.report.dto.DirectReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.DirectReportParam">
	    SELECT SUM(umAddNum) AS umAddNum, SUM(umActiveNum) AS umActiveNum,
		SUM(channelAddNum) AS channelAddNum, SUM(channelActiveNum) AS channelActiveNum,SUM(revenue) AS revenue,
		SUM(channelSpend) AS channelSpend, SUM(channelBuyAdd) AS channelBuyAdd,
		SUM(ttDirectSpend) AS ttDirectSpend,SUM(ttDirectBuyAdd) AS ttDirectBuyAdd,
		SUM(ksDirectSpend) AS ksDirectSpend,SUM(ksDirectBuyAdd) AS ksDirectBuyAdd,
		SUM(gdtDirectSpend) AS gdtDirectSpend,SUM(gdtDirectBuyAdd) AS gdtDirectBuyAdd,
		SUM(directSpend) AS directSpend,SUM(directBuyAdd) AS directBuyAdd,SUM(afterPayRevenue) AS afterPayRevenue,SUM(payRevenue) AS payRevenue,
		SUM(ttRevenue1) AS ttRevenue1,SUM(ttPayRevenue1) AS ttPayRevenue1,SUM(ttAfterPayRevenue1) AS ttAfterPayRevenue1,SUM(ttAddPayCount) AS ttAddPayCount,
		SUM(ksRevenue1) AS ksRevenue1,SUM(ksPayRevenue1) AS ksPayRevenue1,SUM(ksAfterPayRevenue1) AS ksAfterPayRevenue1,SUM(ksAddPayCount) AS ksAddPayCount,
		SUM(gdtRevenue1) AS gdtRevenue1,SUM(gdtPayRevenue1) AS gdtPayRevenue1,SUM(gdtAfterPayRevenue1) AS gdtAfterPayRevenue1,SUM(gdtAddPayCount) AS gdtAddPayCount
		,ifnull(sum(revenueChannel),0.00) revenueChannel,ifnull(sum(revenueSelf),0.00) revenueSelf,
		SUM(spend) AS spend,SUM(directTotalRevenue1) AS directTotalRevenue1,SUM(channelBuyRevenue1) AS channelBuyRevenue1,
		IFNULL(convert(SUM(channelBuyRevenue1)/SUM(channelSpend)*100,decimal(10,2)),0.00) AS channelRoi1,
		IFNULL(convert(SUM(directTotalRevenue1)/SUM(directSpend)*100,decimal(10,2)),0.00) AS directRoi1,
		SUM(totalBuyRevenue1) AS totalBuyRevenue1,
		IFNULL(convert(SUM(totalBuyRevenue1)/SUM(spend)*100,decimal(10,2)),0.00) AS roi1
		,SUM(directBuyRevenueTotal) AS directBuyRevenueTotal, SUM(channelBuyRevenueTotal) AS channelBuyRevenueTotal
		,SUM(IFNULL(directBuyRevenueTotal,0)+IFNULL(channelBuyRevenueTotal,0)) AS buyRevenueTotal
		,IFNULL((SUM(IFNULL(directBuyRevenueTotal,0)+IFNULL(channelBuyRevenueTotal,0)))/(SUM(IFNULL(channelSpend,0)+IFNULL(directSpend,0))),0.00) AS buyRoi
		,IFNULL(SUM(channelBuyRevenueTotal)/SUM(channelSpend),0.00) AS channelBuyRoi
		,IFNULL(SUM(directBuyRevenueTotal)/SUM(directSpend),0.00) AS directBuyRoi
		from
		(SELECT SUM(umAddNum) AS umAddNum, SUM(umActiveNum) AS umActiveNum,
		SUM(channelAddNum) AS channelAddNum, SUM(channelActiveNum) AS channelActiveNum,SUM(revenue) AS revenue,
		SUM(channelSpend) AS channelSpend, SUM(channelBuyAdd) AS channelBuyAdd,
		SUM(ttDirectSpend) AS ttDirectSpend,SUM(ttDirectBuyAdd) AS ttDirectBuyAdd,
		SUM(ksDirectSpend) AS ksDirectSpend,SUM(ksDirectBuyAdd) AS ksDirectBuyAdd,
		SUM(gdtDirectSpend) AS gdtDirectSpend,SUM(gdtDirectBuyAdd) AS gdtDirectBuyAdd,
		SUM(directSpend) AS directSpend,SUM(directBuyAdd) AS directBuyAdd,SUM(afterPayRevenue) AS afterPayRevenue,SUM(payRevenue) AS payRevenue,
		SUM(ttRevenue1) AS ttRevenue1,SUM(ttPayRevenue1) AS ttPayRevenue1,SUM(ttAfterPayRevenue1) AS ttAfterPayRevenue1,SUM(ttAddPayCount) AS ttAddPayCount,
		SUM(ksRevenue1) AS ksRevenue1,SUM(ksPayRevenue1) AS ksPayRevenue1,SUM(ksAfterPayRevenue1) AS ksAfterPayRevenue1,SUM(ksAddPayCount) AS ksAddPayCount,
		SUM(gdtRevenue1) AS gdtRevenue1,SUM(gdtPayRevenue1) AS gdtPayRevenue1,SUM(gdtAfterPayRevenue1) AS gdtAfterPayRevenue1,SUM(gdtAddPayCount) AS gdtAddPayCount
		,ifnull(sum(revenueChannel),0.00) revenueChannel,ifnull(sum(revenueSelf),0.00) revenueSelf
		,SUM(IFNULL(channelSpend,0)+IFNULL(directSpend,0)) AS spend
		,convert(SUM(directTotalRevenue1),decimal(10,2)) AS directTotalRevenue1
		,convert(SUM(channelBuyRevenue1),decimal(10,2)) AS channelBuyRevenue1
		,convert(IFNULL(SUM(directTotalRevenue1),0.00)+IFNULL(SUM(channelBuyRevenue1),0.00),decimal(10,2)) AS totalBuyRevenue1
		,IFNULL(convert(SUM(directBuyRevenueTotal),decimal(10,2)),0.00) AS directBuyRevenueTotal
		,IFNULL(convert(SUM(channelBuyRevenueTotal),decimal(10,2)),0.00) AS channelBuyRevenueTotal
		<if test="group != null and group.size > 0">
			,
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'app'">
						d.app, d.app as appId
					</when>
					<when test="item == 'appCategory'">
						a.app_category as appCategory
					</when>
					<when test="item == 'week'">
						DATE_FORMAT(day, '%x-%v') as day,DATE_FORMAT(day, '%x-%v')
					</when>
					<when test="item == 'month'">
						DATE_FORMAT(day,'%Y-%m') as day,
						DATE_FORMAT(day,'%Y-%m')
					</when>
					<when test="item == 'beek'">
						CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS day
						,CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0")) as beek
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		<if test="custom_date != null and custom_date.size() > 0">
			,concat(#{start_date},'至',#{end_date}) as day
		</if>
		FROM dnwx_adt.dn_direct_spend_report d
		LEFT JOIN dnwx_adt.app_info a on d.app = a.id
		<include refid="getDirectReportCondition1"/>
		<include refid="getDirectReportCondition2"/>) a
	</select>

	<select id="getMaterialReport" resultType="com.wbgame.pojo.jettison.report.dto.MaterialReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.MaterialReportParam">
		SELECT fileName, `type`, createDay, duration, label1, label2, label3,scriptNum,url,format, SUM(installs) AS installs, SUM(impressions) AS impressions,
		SUM(clicks) AS clicks, SUM(spend) AS spend,SUM(rebateSpend) AS rebateSpend, SUM(retention_day_1) AS retention_day_1,
		SUM(toutiao_times) AS toutiao_times, SUM(kuaishou_times) AS kuaishou_times, SUM(gdt_times) AS gdt_times,sum(creative_num) creative_num,
		SUM(total_play) AS total_play, SUM(valid_play) AS valid_play, SUM(valid_play_cost) AS valid_play_cost,
		SUM(play_25_feed_break) AS play_25_feed_break, SUM(play_50_feed_break) AS play_50_feed_break,
		SUM(play_75_feed_break) AS play_75_feed_break, SUM(play_100_feed_break) AS play_100_feed_break,
		SUM(play_time) AS play_time, SUM(wifi_play) AS wifi_play, SUM(card_show) AS card_show,
		SUM(add_user) AS add_user,ifnull(SUM(ifnull(add_revenue_1,0)+ifnull(add_purchase_revenue_1,0)),0) AS ltv1,SUM(video_ltv) AS video_ltv,SUM(plaque_ltv) AS plaque_ltv,
		SUM(video_show_times) AS video_show_times,SUM(plaque_show_times) AS plaque_show_times,
		SUM(payCount) AS payCount,SUM(gamePayCount) AS gamePayCount,SUM(payRevenue1) AS payRevenue1,
		SUM(add_revenue_1) AS add_revenue1,SUM(add_revenue_2) AS add_revenue_2,SUM(add_revenue_3) AS add_revenue_3,
		SUM(add_revenue_4) AS add_revenue_4,SUM(add_revenue_5) AS add_revenue_5,SUM(add_revenue_6) AS add_revenue_6,
		SUM(add_revenue_7) AS add_revenue_7,SUM(add_revenue_14) AS add_revenue_14,SUM(add_revenue_21) AS add_revenue_21,
		SUM(add_revenue_28) AS add_revenue_28,SUM(add_revenue_30) AS add_revenue_30,
		SUM(add_revenue_42) AS add_revenue_42,SUM(add_revenue_60) AS add_revenue_60,SUM(add_revenue_90) AS add_revenue_90,
		SUM(add_purchase_revenue_1) AS add_purchase_revenue_1,SUM(add_purchase_revenue_2) AS add_purchase_revenue_2,SUM(add_purchase_revenue_3) AS add_purchase_revenue_3,
		SUM(add_purchase_revenue_4) AS add_purchase_revenue_4,SUM(add_purchase_revenue_5) AS add_purchase_revenue_5,SUM(add_purchase_revenue_6) AS add_purchase_revenue_6,
		SUM(add_purchase_revenue_7) AS add_purchase_revenue_7,SUM(add_purchase_revenue_14) AS add_purchase_revenue_14,SUM(add_purchase_revenue_21) AS add_purchase_revenue_21,
		SUM(add_purchase_revenue_28) AS add_purchase_revenue_28,SUM(add_purchase_revenue_30) AS add_purchase_revenue_30,
		SUM(add_purchase_revenue_42) AS add_purchase_revenue_42,SUM(add_purchase_revenue_60) AS add_purchase_revenue_60,SUM(add_purchase_revenue_90) AS add_purchase_revenue_90,
		IFNULL(SUM(retention_day_1)/SUM(installs) * 100,0.00) AS retentionRate,
		IFNULL(SUM(installs)/SUM(clicks) * 100,0.00) AS installRate,
		IFNULL(SUM(clicks)/SUM(impressions) * 100,0.00) AS clickRate,
		IFNULL(SUM(spend)/SUM(clicks),0.00) AS click_cost,
		IFNULL(SUM(impressions)/SUM(spend),0.00) AS impression_cost,
		IFNULL(SUM(spend)/SUM(installs),0.00) AS install_cost,
		IFNULL(SUM(valid_play)/SUM(impressions) * 100,0.00) AS valid_play_rate,
		IFNULL(SUM(play_time)/SUM(total_play),0.00) AS average_play_time_per_play,
		IFNULL(SUM(play_100_feed_break)/SUM(total_play) * 100,0.00) AS play_over_rate,
		IFNULL(SUM(wifi_play)/SUM(total_play) * 100,0.00) AS wifi_play_rate,
		IFNULL(SUM(IFNULL(add_revenue_1,0)+IFNULL(add_purchase_revenue_1,0))/SUM(rebateSpend) * 100,0.00) AS roi1,
		IFNULL(SUM(IFNULL(add_revenue_2,0)+IFNULL(add_purchase_revenue_2,0))/SUM(rebateSpend),0.00) AS roi2,
		IFNULL(SUM(IFNULL(add_revenue_3,0)+IFNULL(add_purchase_revenue_3,0))/SUM(rebateSpend),0.00) AS roi3,
		IFNULL(SUM(IFNULL(add_revenue_4,0)+IFNULL(add_purchase_revenue_4,0))/SUM(rebateSpend),0.00) AS roi4,
		IFNULL(SUM(IFNULL(add_revenue_5,0)+IFNULL(add_purchase_revenue_5,0))/SUM(rebateSpend),0.00) AS roi5,
		IFNULL(SUM(IFNULL(add_revenue_6,0)+IFNULL(add_purchase_revenue_6,0))/SUM(rebateSpend),0.00) AS roi6,
		IFNULL(SUM(IFNULL(add_revenue_7,0)+IFNULL(add_purchase_revenue_7,0))/SUM(rebateSpend),0.00) AS roi7,
		IFNULL(SUM(IFNULL(add_revenue_14,0)+IFNULL(add_purchase_revenue_14,0))/SUM(rebateSpend),0.00) AS roi14,
		IFNULL(SUM(IFNULL(add_revenue_21,0)+IFNULL(add_purchase_revenue_21,0))/SUM(rebateSpend),0.00) AS roi21,
		IFNULL(SUM(IFNULL(add_revenue_28,0)+IFNULL(add_purchase_revenue_28,0))/SUM(rebateSpend),0.00) AS roi28,
		IFNULL(SUM(IFNULL(add_revenue_30,0)+IFNULL(add_purchase_revenue_30,0))/SUM(rebateSpend),0.00) AS roi30,
		IFNULL(SUM(IFNULL(add_revenue_42,0)+IFNULL(add_purchase_revenue_42,0))/SUM(rebateSpend),0.00) AS roi42,
		IFNULL(SUM(IFNULL(add_revenue_60,0)+IFNULL(add_purchase_revenue_60,0))/SUM(rebateSpend),0.00) AS roi60,
		IFNULL(SUM(IFNULL(add_revenue_90,0)+IFNULL(add_purchase_revenue_90,0))/SUM(rebateSpend),0.00) AS roi90,
		IFNULL(convert(SUM(add_purchase_revenue_1)/SUM(rebateSpend) * 100,decimal(10,2)),0.00) AS pay_roi1,
		IFNULL(convert(SUM(add_purchase_revenue_3)/SUM(rebateSpend) * 100,decimal(10,2)),0.00) AS pay_roi3,
		IFNULL(convert(SUM(add_purchase_revenue_7)/SUM(rebateSpend) * 100,decimal(10,2)),0.00) AS pay_roi7,
		IFNULL(convert(SUM(add_purchase_revenue_14)/SUM(rebateSpend) * 100,decimal(10,2)),0.00) AS pay_roi14,
		IFNULL(convert(SUM(add_purchase_revenue_30)/SUM(rebateSpend) * 100,decimal(10,2)),0.00) AS pay_roi30,
		IFNULL(SUM(IFNULL(add_revenue_1,0)+IFNULL(add_purchase_revenue_1,0))/SUM(add_user),0.00) AS arpu,
		IFNULL(SUM(video_ltv)/SUM(video_show_times) * 1000,0.00) AS videoEcpm,
		IFNULL(SUM(plaque_ltv)/SUM(plaque_Show_times) * 1000,0.00) AS plaqueEcpm,
		IFNULL(SUM(video_show_times)/SUM(add_user),0.00) AS avgVideoShowTimes,
		IFNULL(SUM(plaque_Show_times)/SUM(add_user),0.00) AS avgplaqueShowTimes,
		IFNULL(SUM(spend)/SUM(add_user),0.00) AS cpa,
		IFNULL(SUM(spend)/SUM(payCount),0.00) AS payCost,
		IFNULL(SUM(payCount)/SUM(installs),0.00) AS payRate,
		IFNULL(SUM(spend)/SUM(gamePayCount),0.00) AS gamePayCost,
		IFNULL(SUM(add_pay_users)/SUM(add_user),0.00) AS addUserPayRate,
		IFNULL(SUM(add_purchase_revenue_1)/SUM(add_pay_users),0.00) AS addUserPayArpu,
		IFNULL(SUM(rebateSpend)/SUM(add_pay_users),0.00) AS addUserPayCost,
		IFNULL(SUM(add_pay_users),0) add_pay_users,
		IFNULL(SUM(first_pay_num),0) first_pay_num
		,IFNULL(convert(sum(pay_retain_1)/SUM(add_pay_users) * 100,decimal(10,2)),0.00) pay_retain_1
		,IFNULL(convert(sum(pay_retain_2)/SUM(add_pay_users) * 100,decimal(10,2)),0.00) pay_retain_2
		,IFNULL(convert(sum(pay_retain_3)/SUM(add_pay_users) * 100,decimal(10,2)),0.00) pay_retain_3
		,IFNULL(convert(sum(pay_retain_7)/SUM(add_pay_users) * 100,decimal(10,2)),0.00) pay_retain_7
		,IFNULL(convert(sum(pay_retain_14)/SUM(add_pay_users) * 100,decimal(10,2)),0.00) pay_retain_14
		,IFNULL(convert(sum(pay_retain_30)/SUM(add_pay_users) * 100,decimal(10,2)),0.00) pay_retain_30
		,IFNULL(convert(sum(video_play_2)/SUM(kuaishou_play_num) * 100,decimal(10,2)),0.00) video_play_2
		,IFNULL(convert(sum(video_play_3)/SUM(kuaishou_play_num) * 100,decimal(10,2)),0.00) video_play_3
		,IFNULL(convert(sum(video_play_5)/SUM(kuaishou_play_num) * 100,decimal(10,2)),0.00) video_play_5
		,IFNULL(convert(sum(video_play_10)/SUM(kuaishou_play_num) * 100,decimal(10,2)),0.00) video_play_10
		,IFNULL(convert(sum(kuaishou_feed_play_75)/SUM(kuaishou_play_num) * 100,decimal(10,2)),0.00) kuaishou_feed_play_75
		,IFNULL(convert(sum(finish_video_play)/SUM(kuaishou_play_num) * 100,decimal(10,2)),0.00) finish_video_play
		,IFNULL(SUM(IFNULL(b.week_users,a.week_users)),0) week_users
		,IFNULL(SUM(IFNULL(b.month_users,a.month_users)),0) month_users
		,IFNULL(SUM(IFNULL(b.year_users,a.year_users)),0) year_users
		,IFNULL(SUM(IFNULL(b.renew_users_1,a.renew_users_1)),0) renew_users_1
		,IFNULL(SUM(IFNULL(b.renew_users_2,a.renew_users_2)),0) renew_users_2
		,IFNULL(SUM(IFNULL(b.renew_users_3,a.renew_users_3)),0) renew_users_3
		,IFNULL(SUM(IFNULL(b.renew_users_4,a.renew_users_4)),0) renew_users_4
		,IFNULL(SUM(IFNULL(b.renew_users_5,a.renew_users_5)),0) renew_users_5
		,IFNULL(SUM(IFNULL(b.renew_users_6,a.renew_users_6)),0) renew_users_6
		,IFNULL(convert(sum(IFNULL(b.renew_users_1,a.renew_users_1))/SUM(IFNULL(b.week_users,a.week_users)) * 100,decimal(10,2)),0.00) renew_user_rate_1
		,IFNULL(convert(sum(IFNULL(b.renew_users_2,a.renew_users_2))/SUM(IFNULL(b.week_users,a.week_users)) * 100,decimal(10,2)),0.00) renew_user_rate_2
		,IFNULL(convert(sum(IFNULL(b.renew_users_3,a.renew_users_3))/SUM(IFNULL(b.week_users,a.week_users)) * 100,decimal(10,2)),0.00) renew_user_rate_3
		,IFNULL(convert(sum(IFNULL(b.renew_users_4,a.renew_users_4))/SUM(IFNULL(b.week_users,a.week_users)) * 100,decimal(10,2)),0.00) renew_user_rate_4
		,IFNULL(convert(sum(IFNULL(b.renew_users_5,a.renew_users_5))/SUM(IFNULL(b.week_users,a.week_users)) * 100,decimal(10,2)),0.00) renew_user_rate_5
		,IFNULL(convert(sum(IFNULL(b.renew_users_6,a.renew_users_6))/SUM(IFNULL(b.week_users,a.week_users)) * 100,decimal(10,2)),0.00) renew_user_rate_6
		,IFNULL(convert(sum(IFNULL(b.agg_renew_users_1,a.agg_renew_users_1))/SUM(IFNULL(b.week_users,a.week_users)) ,decimal(10,2)),0.00) renew_lt1
		,IFNULL(convert(sum(IFNULL(b.agg_renew_users_2,a.agg_renew_users_2))/SUM(IFNULL(b.week_users,a.week_users)) ,decimal(10,2)),0.00) renew_lt2
		,IFNULL(convert(sum(IFNULL(b.agg_renew_users_3,a.agg_renew_users_3))/SUM(IFNULL(b.week_users,a.week_users)) ,decimal(10,2)),0.00) renew_lt3
		,IFNULL(convert(sum(IFNULL(b.agg_renew_users_4,a.agg_renew_users_4))/SUM(IFNULL(b.week_users,a.week_users)) ,decimal(10,2)),0.00) renew_lt4
		,IFNULL(convert(sum(IFNULL(b.agg_renew_users_5,a.agg_renew_users_5))/SUM(IFNULL(b.week_users,a.week_users)) ,decimal(10,2)),0.00) renew_lt5
		,IFNULL(convert(sum(IFNULL(b.agg_renew_users_6,a.agg_renew_users_6))/SUM(IFNULL(b.week_users,a.week_users)) ,decimal(10,2)),0.00) renew_lt6
		,IFNULL(convert(sum(IFNULL(b.agg_renew_revenue_1,a.agg_renew_revenue_1)+IFNULL(b.month_revenue,a.month_revenue)+IFNULL(b.year_revenue,a.year_revenue))/SUM(rebateSpend) * 100,decimal(10,2)),0.00) renew_roi1
		,IFNULL(convert(sum(IFNULL(b.agg_renew_revenue_2,a.agg_renew_revenue_2)+IFNULL(b.month_revenue,a.month_revenue)+IFNULL(b.year_revenue,a.year_revenue))/SUM(rebateSpend) * 100,decimal(10,2)),0.00) renew_roi2
		,IFNULL(convert(sum(IFNULL(b.agg_renew_revenue_3,a.agg_renew_revenue_3)+IFNULL(b.month_revenue,a.month_revenue)+IFNULL(b.year_revenue,a.year_revenue))/SUM(rebateSpend) * 100,decimal(10,2)),0.00) renew_roi3
		,IFNULL(convert(sum(IFNULL(b.agg_renew_revenue_4,a.agg_renew_revenue_4)+IFNULL(b.month_revenue,a.month_revenue)+IFNULL(b.year_revenue,a.year_revenue))/SUM(rebateSpend) * 100,decimal(10,2)),0.00) renew_roi4
		,IFNULL(convert(sum(IFNULL(b.agg_renew_revenue_5,a.agg_renew_revenue_5)+IFNULL(b.month_revenue,a.month_revenue)+IFNULL(b.year_revenue,a.year_revenue))/SUM(rebateSpend) * 100,decimal(10,2)),0.00) renew_roi5
		,IFNULL(convert(sum(IFNULL(b.agg_renew_revenue_6,a.agg_renew_revenue_6)+IFNULL(b.month_revenue,a.month_revenue)+IFNULL(b.year_revenue,a.year_revenue))/SUM(rebateSpend) * 100,decimal(10,2)),0.00) renew_roi6
		<if test='group != null and group.size > 0 and group.contains("component_id")'>
		,if(component_id is null,null,GROUP_CONCAT(DISTINCT signature SEPARATOR ',')) component_url
		</if>
		<if test="group != null and group.size > 0">
			,
			<foreach collection="group" index="index" item="item" separator=",">
				`${item}`
			</foreach>
		</if>
		FROM dnwx_adt.dn_report_material a
		left join dnwx_bi.ads_camera_apple_notice_material b
		on a.material_id = b.material_id and a.promotion_id = b.campaign_id and a.ad_platform = b.reyun_channel and a.day = b.tdate
		<include refid="getMaterialReportCondition1"/>
		<include refid="getMaterialReportCondition2"/>
	</select>

	<select id="getMaterialReportSummary" resultType="com.wbgame.pojo.jettison.report.dto.MaterialReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.MaterialReportParam">
		SELECT SUM(installs) AS installs, SUM(impressions) AS impressions,
		SUM(clicks) AS clicks, SUM(spend) AS spend,SUM(rebateSpend) AS rebateSpend,SUM(retention_day_1) AS retention_day_1,
		SUM(toutiao_times) AS toutiao_times, SUM(kuaishou_times) AS kuaishou_times, SUM(gdt_times) AS gdt_times,sum(creative_num) creative_num,
		SUM(add_user) AS add_user,ifnull(SUM(ifnull(add_revenue_1,0)+ifnull(add_purchase_revenue_1,0)),0) AS ltv1,SUM(video_ltv) AS video_ltv,SUM(plaque_ltv) AS plaque_ltv,
		SUM(video_show_times) AS video_show_times,SUM(plaque_show_times) AS plaque_show_times,
		SUM(payCount) AS payCount, SUM(payRevenue1) AS payRevenue1, SUM(gamePayCount) AS gamePayCount,
		SUM(add_revenue_1) AS add_revenue1,SUM(add_revenue_2) AS add_revenue_2,SUM(add_revenue_3) AS add_revenue_3,
		SUM(add_revenue_4) AS add_revenue_4,SUM(add_revenue_5) AS add_revenue_5,SUM(add_revenue_6) AS add_revenue_6,
		SUM(add_revenue_7) AS add_revenue_7,SUM(add_revenue_14) AS add_revenue_14,SUM(add_revenue_21) AS add_revenue_21,
		SUM(add_revenue_28) AS add_revenue_28,SUM(add_revenue_30) AS add_revenue_30,
		SUM(add_revenue_42) AS add_revenue_42,SUM(add_revenue_60) AS add_revenue_60,SUM(add_revenue_90) AS add_revenue_90,
		SUM(add_purchase_revenue_1) AS add_purchase_revenue_1,SUM(add_purchase_revenue_2) AS add_purchase_revenue_2,SUM(add_purchase_revenue_3) AS add_purchase_revenue_3,
		SUM(add_purchase_revenue_4) AS add_purchase_revenue_4,SUM(add_purchase_revenue_5) AS add_purchase_revenue_5,SUM(add_purchase_revenue_6) AS add_purchase_revenue_6,
		SUM(add_purchase_revenue_7) AS add_purchase_revenue_7,SUM(add_purchase_revenue_14) AS add_purchase_revenue_14,SUM(add_purchase_revenue_21) AS add_purchase_revenue_21,
		SUM(add_purchase_revenue_28) AS add_purchase_revenue_28,SUM(add_purchase_revenue_30) AS add_purchase_revenue_30,
		SUM(add_purchase_revenue_42) AS add_purchase_revenue_42,SUM(add_purchase_revenue_60) AS add_purchase_revenue_60,SUM(add_purchase_revenue_90) AS add_purchase_revenue_90,
		IFNULL(SUM(add_pay_users)/SUM(add_user),0.00) AS addUserPayRate,
		IFNULL(SUM(add_purchase_revenue_1)/SUM(add_pay_users),0.00) AS addUserPayArpu,
		IFNULL(SUM(rebateSpend)/SUM(add_pay_users),0.00) AS addUserPayCost,
		IFNULL(SUM(add_pay_users),0) add_pay_users,
		IFNULL(SUM(first_pay_num),0) first_pay_num
		,IFNULL(convert(sum(add_purchase_revenue_1)/SUM(rebateSpend) * 100,decimal(10,2)),0.00) pay_roi1
		,IFNULL(convert(sum(add_purchase_revenue_3)/SUM(rebateSpend) * 100,decimal(10,2)),0.00) pay_roi3
		,IFNULL(convert(sum(add_purchase_revenue_7)/SUM(rebateSpend) * 100,decimal(10,2)),0.00) pay_roi7
		,IFNULL(convert(sum(add_purchase_revenue_14)/SUM(rebateSpend) * 100,decimal(10,2)),0.00) pay_roi14
		,IFNULL(convert(sum(add_purchase_revenue_30)/SUM(rebateSpend) * 100,decimal(10,2)),0.00) pay_roi30
		,IFNULL(convert(sum(pay_retain_1)/SUM(add_pay_users) * 100,decimal(10,2)),0.00) pay_retain_1
		,IFNULL(convert(sum(pay_retain_2)/SUM(add_pay_users) * 100,decimal(10,2)),0.00) pay_retain_2
		,IFNULL(convert(sum(pay_retain_3)/SUM(add_pay_users) * 100,decimal(10,2)),0.00) pay_retain_3
		,IFNULL(convert(sum(pay_retain_7)/SUM(add_pay_users) * 100,decimal(10,2)),0.00) pay_retain_7
		,IFNULL(convert(sum(pay_retain_14)/SUM(add_pay_users) * 100,decimal(10,2)),0.00) pay_retain_14
		,IFNULL(convert(sum(pay_retain_30)/SUM(add_pay_users) * 100,decimal(10,2)),0.00) pay_retain_30
		,IFNULL(convert(sum(video_play_2)/SUM(kuaishou_play_num) * 100,decimal(10,2)),0.00) video_play_2
		,IFNULL(convert(sum(video_play_3)/SUM(kuaishou_play_num) * 100,decimal(10,2)),0.00) video_play_3
		,IFNULL(convert(sum(video_play_5)/SUM(kuaishou_play_num) * 100,decimal(10,2)),0.00) video_play_5
		,IFNULL(convert(sum(video_play_10)/SUM(kuaishou_play_num) * 100,decimal(10,2)),0.00) video_play_10
		,IFNULL(convert(sum(kuaishou_feed_play_75)/SUM(kuaishou_play_num) * 100,decimal(10,2)),0.00) kuaishou_feed_play_75
		,IFNULL(convert(sum(finish_video_play)/SUM(kuaishou_play_num) * 100,decimal(10,2)),0.00) finish_video_play
		,IFNULL(SUM(IFNULL(b.week_users,a.week_users)),0) week_users
		,IFNULL(SUM(IFNULL(b.month_users,a.month_users)),0) month_users
		,IFNULL(SUM(IFNULL(b.year_users,a.year_users)),0) year_users
		,IFNULL(SUM(IFNULL(b.renew_users_1,a.renew_users_1)),0) renew_users_1
		,IFNULL(SUM(IFNULL(b.renew_users_2,a.renew_users_2)),0) renew_users_2
		,IFNULL(SUM(IFNULL(b.renew_users_3,a.renew_users_3)),0) renew_users_3
		,IFNULL(SUM(IFNULL(b.renew_users_4,a.renew_users_4)),0) renew_users_4
		,IFNULL(SUM(IFNULL(b.renew_users_5,a.renew_users_5)),0) renew_users_5
		,IFNULL(SUM(IFNULL(b.renew_users_6,a.renew_users_6)),0) renew_users_6
		,IFNULL(convert(sum(IFNULL(b.renew_users_1,a.renew_users_1))/SUM(IFNULL(b.week_users,a.week_users)) * 100,decimal(10,2)),0.00) renew_user_rate_1
		,IFNULL(convert(sum(IFNULL(b.renew_users_2,a.renew_users_2))/SUM(IFNULL(b.week_users,a.week_users)) * 100,decimal(10,2)),0.00) renew_user_rate_2
		,IFNULL(convert(sum(IFNULL(b.renew_users_3,a.renew_users_3))/SUM(IFNULL(b.week_users,a.week_users)) * 100,decimal(10,2)),0.00) renew_user_rate_3
		,IFNULL(convert(sum(IFNULL(b.renew_users_4,a.renew_users_4))/SUM(IFNULL(b.week_users,a.week_users)) * 100,decimal(10,2)),0.00) renew_user_rate_4
		,IFNULL(convert(sum(IFNULL(b.renew_users_5,a.renew_users_5))/SUM(IFNULL(b.week_users,a.week_users)) * 100,decimal(10,2)),0.00) renew_user_rate_5
		,IFNULL(convert(sum(IFNULL(b.renew_users_6,a.renew_users_6))/SUM(IFNULL(b.week_users,a.week_users)) * 100,decimal(10,2)),0.00) renew_user_rate_6
		,IFNULL(convert(sum(IFNULL(b.agg_renew_users_1,a.agg_renew_users_1))/SUM(IFNULL(b.week_users,a.week_users)) ,decimal(10,2)),0.00) renew_lt1
		,IFNULL(convert(sum(IFNULL(b.agg_renew_users_2,a.agg_renew_users_2))/SUM(IFNULL(b.week_users,a.week_users)) ,decimal(10,2)),0.00) renew_lt2
		,IFNULL(convert(sum(IFNULL(b.agg_renew_users_3,a.agg_renew_users_3))/SUM(IFNULL(b.week_users,a.week_users)) ,decimal(10,2)),0.00) renew_lt3
		,IFNULL(convert(sum(IFNULL(b.agg_renew_users_4,a.agg_renew_users_4))/SUM(IFNULL(b.week_users,a.week_users)) ,decimal(10,2)),0.00) renew_lt4
		,IFNULL(convert(sum(IFNULL(b.agg_renew_users_5,a.agg_renew_users_5))/SUM(IFNULL(b.week_users,a.week_users)) ,decimal(10,2)),0.00) renew_lt5
		,IFNULL(convert(sum(IFNULL(b.agg_renew_users_6,a.agg_renew_users_6))/SUM(IFNULL(b.week_users,a.week_users)) ,decimal(10,2)),0.00) renew_lt6
		,IFNULL(convert(sum(IFNULL(b.agg_renew_revenue_1,a.agg_renew_revenue_1)+IFNULL(b.month_revenue,a.month_revenue)+IFNULL(b.year_revenue,a.year_revenue))/SUM(rebateSpend) * 100,decimal(10,2)),0.00) renew_roi1
		,IFNULL(convert(sum(IFNULL(b.agg_renew_revenue_2,a.agg_renew_revenue_2)+IFNULL(b.month_revenue,a.month_revenue)+IFNULL(b.year_revenue,a.year_revenue))/SUM(rebateSpend) * 100,decimal(10,2)),0.00) renew_roi2
		,IFNULL(convert(sum(IFNULL(b.agg_renew_revenue_3,a.agg_renew_revenue_3)+IFNULL(b.month_revenue,a.month_revenue)+IFNULL(b.year_revenue,a.year_revenue))/SUM(rebateSpend) * 100,decimal(10,2)),0.00) renew_roi3
		,IFNULL(convert(sum(IFNULL(b.agg_renew_revenue_4,a.agg_renew_revenue_4)+IFNULL(b.month_revenue,a.month_revenue)+IFNULL(b.year_revenue,a.year_revenue))/SUM(rebateSpend) * 100,decimal(10,2)),0.00) renew_roi4
		,IFNULL(convert(sum(IFNULL(b.agg_renew_revenue_5,a.agg_renew_revenue_5)+IFNULL(b.month_revenue,a.month_revenue)+IFNULL(b.year_revenue,a.year_revenue))/SUM(rebateSpend) * 100,decimal(10,2)),0.00) renew_roi5
		,IFNULL(convert(sum(IFNULL(b.agg_renew_revenue_6,a.agg_renew_revenue_6)+IFNULL(b.month_revenue,a.month_revenue)+IFNULL(b.year_revenue,a.year_revenue))/SUM(rebateSpend) * 100,decimal(10,2)),0.00) renew_roi6
		FROM dnwx_adt.dn_report_material a
		left join dnwx_bi.ads_camera_apple_notice_material b
		on a.material_id = b.material_id and a.promotion_id = b.campaign_id and a.ad_platform = b.reyun_channel and a.day = b.tdate
		<include refid="getMaterialReportCondition1"/>
	</select>

	<select id="getMaterialList" resultType="com.wbgame.pojo.jettison.report.dto.MaterialReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.MaterialReportParam">
		SELECT fileName, `type`, createDay, duration, label1, label2, label3,artist3d,creativist,artist,ad_platform,channel,day,IFNULL(url,signature) signature,
		 SUM(spend) AS spend,SUM(rebateSpend) AS rebateSpend
		FROM dnwx_adt.dn_report_material
		<include refid="getArtistSpendCondition"/>
		<include refid="getMaterialReportCondition2"/>
	</select>

	<select id="getMaterialListSummary" resultType="com.wbgame.pojo.jettison.report.dto.MaterialReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.MaterialReportParam">
		SELECT  SUM(spend) AS spend,SUM(rebateSpend) AS rebateSpend
		FROM dnwx_adt.dn_report_material
		<include refid="getArtistSpendCondition"/>
	</select>

	<select id="getCreativeReport" resultType="com.wbgame.pojo.jettison.report.dto.CreativeSpendReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.CreativeSpendReportParam">
		SELECT title, createDay,`type`,
		channelType, material, SUM(installs) AS installs, SUM(impressions) AS impressions,
		SUM(clicks) AS clicks, SUM(spend) AS spend,SUM(rebateSpend) AS rebateSpend, SUM(retention_day_1) AS retention_day_1,
		SUM(reActive) reActive,SUM(reActiveImpression) reActiveImpression,SUM(reActiveClick) reActiveClick,SUM(reActiveDownload) reActiveDownload,
		SUM(uninstalls) uninstalls,SUM(uninstallsdownload) uninstallsdownload,
		SUM(addiction) addiction,SUM(addictionClick) addictionClick,SUM(addictionDownload) addictionDownload,
		IFNULL(ROUND(SUM(retention_day_1)/SUM(installs) * 100,2),0.00) AS retentionRate,
		IFNULL(ROUND(SUM(installs)/SUM(clicks) * 100,2),0.00) AS installRate,
		IFNULL(ROUND(SUM(clicks)/SUM(impressions) * 100,2),0.00) AS clickRate,
		IFNULL(ROUND(SUM(spend)/SUM(clicks),2),0.00) AS click_cost,
		IFNULL(ROUND(SUM(impressions)/SUM(spend),2),0.00) AS impression_cost,
		IFNULL(ROUND(SUM(spend)/SUM(installs),2),0.00) AS install_cost,
		IFNULL(ROUND(SUM(reActiveImpression)/SUM(impressions),2),0.00) AS reActiveImpressionsRate,
		IFNULL(ROUND(SUM(reActiveClick)/SUM(clicks) * 100,2),0.00) AS reActiveClicksRate,
		IFNULL(ROUND(SUM(reActive)/SUM(reActiveDownload) * 100,2),0.00) AS reActiveInstallsRate,
		IFNULL(ROUND(SUM(addiction)/SUM(addictionClick),2),0.00) AS addictionClickRate,
		IFNULL(ROUND(SUM(addiction)/SUM(addictionDownload),2),0.00) AS addictionDownloadRate,
		IFNULL(ROUND(SUM(ltv1)/SUM(add_user),2),0.00) AS install_arpu,
		IFNULL(ROUND(SUM(video_show_times)/SUM(add_user),2),0.00) AS avgVideoShowTimes,
		IFNULL(ROUND(SUM(plaque_Show_times)/SUM(add_user),2),0.00) AS avgplaqueShowTimes
		<if test="group != null and group.size > 0">
			,
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'artist'">
						artist,artist3d,creativist
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		FROM dnwx_adt.dn_report_creative_summary
		<include refid="getCreativeReportCondition1"/>
		<include refid="getCreativeReportCondition2"/>
	</select>

	<select id="getCreativeReportSummary" resultType="com.wbgame.pojo.jettison.report.dto.CreativeSpendReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.CreativeSpendReportParam">
		SELECT SUM(installs) AS installs, SUM(impressions) AS impressions,
		SUM(clicks) AS clicks, SUM(spend) AS spend,SUM(rebateSpend) AS rebateSpend, SUM(retention_day_1) AS retention_day_1,
		SUM(reActive) reActive,SUM(reActiveImpression) reActiveImpression,SUM(reActiveClick) reActiveClick,SUM(reActiveDownload) reActiveDownload,
		SUM(uninstalls) uninstalls,SUM(uninstallsdownload) uninstallsdownload,
		SUM(addiction) addiction,SUM(addictionClick) addictionClick,SUM(addictionDownload) addictionDownload
		FROM dnwx_adt.dn_report_creative_summary
		<include refid="getCreativeReportCondition1"/>
	</select>

	<select id="getCreativeLineChart" resultType="java.util.Map"  parameterType="com.wbgame.pojo.jettison.report.dto.CreativeSpendReportDTO">
		SELECT `day`, COUNT(DISTINCT creative_id) AS `sum`
		<if test="meishu == false">
			, SUM(spend) AS spend, IFNULL(ROUND(SUM(spend)/SUM(installs),2),0.00) AS install_cost
		</if>
		FROM dnwx_adt.dn_report_creative_summary
		<include refid="getCreativeReportCondition1"/>
		GROUP BY `day`
		ORDER BY `day`
	</select>

	<select id="getArtistSpend" resultType="java.util.Map"  parameterType="com.wbgame.pojo.jettison.report.param.MaterialReportParam">
		SELECT `artist`,artist3d,creativist, sum(rebateSpend) AS `spend`
		FROM dnwx_adt.dn_report_material
		<include refid="getArtistSpendCondition"/>
		GROUP BY `artist`,artist3d,creativist
		ORDER BY `spend` desc
	</select>
	<select id="getChannelSpend" resultType="java.util.Map"  parameterType="com.wbgame.pojo.jettison.report.param.MaterialReportParam">
		SELECT
		<choose>
             <when test="meishu == true">
                 channel,
             </when>
             <otherwise>
                 ad_platform as channel,
             </otherwise>
        </choose>
		sum(rebateSpend) AS `spend` FROM dnwx_adt.dn_report_material
		<include refid="getArtistSpendCondition"/>
		<choose>
             <when test="meishu == true">
                 GROUP BY `channel`
             </when>
             <otherwise>
                GROUP BY `ad_platform`
             </otherwise>
        </choose>
		ORDER BY spend desc
	</select>

	<select id="getTag1Spend" resultType="java.util.Map"  parameterType="com.wbgame.pojo.jettison.report.param.MaterialReportParam">
		SELECT `label1`, sum(rebateSpend) AS `spend`
		FROM dnwx_adt.dn_report_material
		<include refid="getArtistSpendCondition"/>
		GROUP BY `label1`
		ORDER BY spend desc
	</select>

	<select id="getTag2Spend" resultType="java.util.Map"  parameterType="com.wbgame.pojo.jettison.report.param.MaterialReportParam">
		SELECT `label2`, sum(rebateSpend) AS `spend`
		FROM dnwx_adt.dn_report_material
		<include refid="getArtistSpendCondition"/>
		GROUP BY `label2`
		ORDER BY spend desc
	</select>

	<select id="getTag3Spend" resultType="java.util.Map"  parameterType="com.wbgame.pojo.jettison.report.param.MaterialReportParam">
		SELECT `label3`, sum(rebateSpend) AS `spend`
		FROM dnwx_adt.dn_report_material
		<include refid="getArtistSpendCondition"/>
		GROUP BY `label3`
		ORDER BY spend desc
	</select>


	<select id="getMaterialLineChart" resultType="java.util.Map"  parameterType="com.wbgame.pojo.jettison.report.param.MaterialReportParam">
		SELECT `day`, COUNT(DISTINCT signature) AS `sum`
		<if test="meishu == false">
			, SUM(spend) AS spend, IFNULL(ROUND(SUM(spend)/SUM(installs),2),0.00) AS install_cost
		</if>
		FROM dnwx_adt.dn_report_material
		<include refid="getMaterialReportCondition1"/>
		GROUP BY `day`
		ORDER BY `day`
	</select>

	<select id="getCreativeXiaomiReport" resultType="com.wbgame.pojo.jettison.report.dto.CreativeXiaomiReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.CreativeXiaomiParam">
		SELECT priceType,
		SUM(spend) spend,SUM(impressions) impressions,SUM(clicks) clicks,SUM(installs) installs,SUM(downloads) downloads,SUM(cashSpend) cashSpend,SUM(virtualSpend) virtualSpend,
		SUM(reActive) reActive,SUM(reActiveSpend) reActiveSpend,SUM(reActiveImpression) reActiveImpression,SUM(reActiveClick) reActiveClick,SUM(reActiveDownload) reActiveDownload,
		SUM(uninstalls) uninstalls,SUM(uninstallsSpend) uninstallsSpend,SUM(uninstallsdownload) uninstallsdownload,SUM(activeSpend) activeSpend,
		SUM(addiction) addiction,SUM(addictionSpend) addictionSpend,SUM(addictionClick) addictionClick,SUM(addictionDownload) addictionDownload,
		SUM(gameReaD1) gameReaD1,SUM(gameReaD3) gameReaD3,SUM(gameReaD7) gameReaD7,SUM(gameRegister) gameRegister,SUM(gameRegisterSpend) gameRegisterSpend,
		SUM(gameRerD1) gameRerD1,SUM(gameRerD3) gameRerD3,SUM(gameRerD7) gameRerD7,SUM(gamePayUser) gamePayUser,SUM(gamePuD1) gamePuD1,SUM(gamePuD7) gamePuD7,SUM(gamePuH24) gamePuH24,
		SUM(gamePayFee) gamePayFee,SUM(gamePfH24) gamePfH24,SUM(gamePfD1) gamePfD1,SUM(gamePfD3) gamePfD3,SUM(gamePfD7) gamePfD7,SUM(gamePfD30) gamePfD30,
		SUM(gameIncome) gameIncome,SUM(gameIncomeAll) gameIncomeAll,SUM(gameIncome1) gameIncome1,SUM(gameIncome3) gameIncome3,SUM(gameIncome7) gameIncome7,SUM(gameIncome30) gameIncome30,
		SUM(gameFD1) gameFD1,SUM(gameFD3) gameFD3,SUM(gameFD7) gameFD7,SUM(gameFD30) gameFD30,
		IFNULL(ROUND(SUM(spend)/SUM(clicks) * 100,2),0.00) AS clickPreSpend,
		IFNULL(ROUND(SUM(clicks)/SUM(impressions) * 100,4),0.00) AS clickRate,
		IFNULL(ROUND(SUM(spend)/SUM(impressions) * 100,2),0.00) AS ecpm,
		IFNULL(ROUND(SUM(spend)/SUM(downloads),2),0.00) AS installsPreSpend,
		IFNULL(ROUND(SUM(downloads)/SUM(impressions),4),0.00) AS installsRate,
		IFNULL(ROUND(SUM(spend)/SUM(reActive),2),0.00) AS reActivePreSpend,
		IFNULL(ROUND(SUM(reActiveImpression)/SUM(impressions),4),0.00) AS reActiveImpressionsRate,
		IFNULL(ROUND(SUM(reActiveClick)/SUM(clicks) * 100,4),0.00) AS reActiveClicksRate,
		IFNULL(ROUND(SUM(reActive)/SUM(reActiveDownload) * 100,4),0.00) AS reActiveInstallsRate,
		IFNULL(ROUND(SUM(uninstallsSpend)/SUM(uninstalls) * 100,2),0.00) AS uninstallsPreSpend,
		IFNULL(ROUND(SUM(uninstalls)/SUM(uninstallsDownload),4),0.00) AS uninstallsInstallsRate,
		IFNULL(ROUND(SUM(activeSpend)/SUM(installs),2),0.00) AS activePreSpend,
		IFNULL(ROUND(SUM(gameReaD1)/SUM(installs),4),0.00) AS gameReaRate,
		IFNULL(ROUND(SUM(gameReaD3)/SUM(installs),4),0.00) AS gameReaRate3,
		IFNULL(ROUND(SUM(gameReaD7)/SUM(installs),4),0.00) AS gameReaRate7,
		IFNULL(ROUND(SUM(gameRegisterSPend)/SUM(gameRegister) * 100,2),0.00) AS gameRegisterPreSpend,
		IFNULL(ROUND(SUM(gameRerD1)/SUM(gameRegister) * 100,4),0.00) AS gameRerRate,
		IFNULL(ROUND(SUM(gameRerD3)/SUM(gameRegister) * 100,4),0.00) AS gameRerRate3,
		IFNULL(ROUND(SUM(gameRerD7)/SUM(gameRegister) * 100,4),0.00) AS gameRerRate7,
		IFNULL(ROUND(SUM(spend)/SUM(gamePuD7) * 100,2),0.00) AS gamePuD7PreSpend,
		IFNULL(ROUND(SUM(gamePuD1)/SUM(gameRegister),4),0.00) AS addPayRate,
		IFNULL(ROUND(SUM(addiction)/SUM(addictionClick),4),0.00) AS addictionClickRate,
		IFNULL(ROUND(SUM(addiction)/SUM(addictionDownload),4),0.00) AS addictionDownloadRate,
		IFNULL(ROUND(SUM(addictionSpend)/SUM(addiction),2),0.00) AS addictionPreSpend,
		IFNULL(ROUND(SUM(gamePfD1)/SUM(gameRegister),2),0.00) AS ltv1,
		IFNULL(ROUND(SUM(gamePfD3)/SUM(gameRegister),2),0.00) AS ltv3,
		IFNULL(ROUND(SUM(gamePfD7)/SUM(gameRegister),2),0.00) AS ltv7,
		IFNULL(ROUND(SUM(gamePfD30)/SUM(gameRegister),2),0.00) AS ltv30,
		IFNULL(ROUND(SUM(gamePfH24)/SUM(spend),4),0.00) AS roi24h,
		IFNULL(ROUND(SUM(spend)/SUM(gamePuH24),4),0.00) AS average24h,
		IFNULL(ROUND(SUM(gamePfD1)/SUM(spend),4),0.00) AS roi1,
		IFNULL(ROUND(SUM(gamePfD3)/SUM(spend),4),0.00) AS roi3,
		IFNULL(ROUND(SUM(gamePfD7)/SUM(spend),4),0.00) AS roi7,
		IFNULL(ROUND(SUM(gamePfD30)/SUM(spend),4),0.00) AS roi30,
		IFNULL(ROUND(SUM(gameIncome1)/SUM(installs),2),0.00) AS incomeLtv1,
		IFNULL(ROUND(SUM(gameIncome3)/SUM(installs),2),0.00) AS incomeLtv3,
		IFNULL(ROUND(SUM(gameIncome7)/SUM(installs),2),0.00) AS incomeLtv7,
		IFNULL(ROUND(SUM(gameIncome30)/SUM(installs),2),0.00) AS incomeLtv30,
		IFNULL(ROUND(SUM(gameIncome)/SUM(spend),4),0.00) AS roi,
		IFNULL(ROUND(SUM(gameIncome1)/SUM(spend),4),0.00) AS incomeROI1,
		IFNULL(ROUND(SUM(gameIncome3)/SUM(spend),4),0.00) AS incomeROI3,
		IFNULL(ROUND(SUM(gameIncome7)/SUM(spend),4),0.00) AS incomeROI7,
		IFNULL(ROUND(SUM(gameIncome30)/SUM(spend),4),0.00) AS incomeROI30,
		IFNULL(ROUND(SUM(gameFD1)/SUM(spend),4),0.00) AS gameFDROI1,
		IFNULL(ROUND(SUM(gameFD3)/SUM(spend),4),0.00) AS gameFDROI3,
		IFNULL(ROUND(SUM(gameFD7)/SUM(spend),4),0.00) AS gameFDROI7,
		IFNULL(ROUND(SUM(gameFD30)/SUM(spend),4),0.00) AS gameFDROI30,
		IFNULL(SUM(spend),0.00) AS spendRate
		,ifnull(sum(gameIibpH24Format),0.00) gameIibpH24Format
		,ifnull(sum(gameIicpH24Format),0.00) gameIicpH24Format
		,ifnull(sum(gameIibpH24Format)/sum(spend)*100,0.00) gameIibpH24FormatRoi
		,ifnull(sum(gameIicpH24Format)/sum(spend)*100,0.00) gameIicpH24FormatRoi
		<if test="group != null and group.size > 0">
			,
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'appCategory'">
						a.app_category AS appCategory
					</when>
					<when test="item == 'week'">
						DATE_FORMAT(day, '%x-%v') as day,DATE_FORMAT(day, '%x-%v')
					</when>
					<when test="item == 'month'">
						DATE_FORMAT(day,'%Y-%m') as day,
						DATE_FORMAT(day,'%Y-%m')
					</when>
					<when test="item == 'beek'">
						CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS day
						,CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0")) as beek
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		<if test="custom_date != null and custom_date.size() > 0">
			,concat(#{start_date},'至',#{end_date}) as day
		</if>
		FROM dnwx_adt.dn_xiaomi_creative_report r left join dnwx_adt.app_info a on  r.app=a.id
		left join dnwx_adt.xiaomi_creative_realtime_info info on r.creative_id = info.creative
		<include refid="CreativeXiaomiReportsql1"/>
		<include refid="CreativeXiaomiReportsql2"/>

	</select>

	<select id="getCreativeXiaomiCreativeIdReport" resultType="com.wbgame.pojo.jettison.report.dto.CreativeXiaomiReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.CreativeXiaomiParam">
		SELECT creative_id, account
		FROM dnwx_adt.dn_xiaomi_creative_info_temp a
		left join dnwx_adt.xiaomi_creative_realtime_info b on a.creative_id = b.creative
		where 1=1
		<if test="gameName != null and gameName.size > 0">
			AND gameName IN
			<foreach collection="gameName" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		  <if test="putUser != null and putUser.size > 0">
			AND putUser IN
			<foreach collection="putUser" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		  <if test="account != null and account.size > 0">
			AND account IN
			<foreach collection="account" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		  <if test="transferType != null and transferType.size > 0">
			AND transferType IN
			<foreach collection="transferType" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		  <if test="midType != null and midType.size > 0">
			AND midType IN
			<foreach collection="midType" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		  <if test="lowType != null and lowType.size > 0">
			AND lowType IN
			<foreach collection="lowType" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>

		<if test="groupName != null and groupName !=''">
			AND groupName LIKE "%"#{groupName}"%"
		</if>
		<if test="creative_id != null and creative_id != '' ">
			and creative_id = #{creative_id}
		</if>
		<if test="inDelivery != null and inDelivery != ''">
			AND in_delivery = #{inDelivery}
		</if>

		group by creative_id, account

		
	</select>

	<select id="getCreativeXiaomiReportSummary" resultType="com.wbgame.pojo.jettison.report.dto.CreativeXiaomiReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.CreativeXiaomiParam">
		SELECT
		SUM(spend) spend,SUM(impressions) impressions,SUM(clicks) clicks,SUM(installs) installs,SUM(downloads) downloads,SUM(cashSpend) cashSpend,SUM(virtualSpend) virtualSpend,
		SUM(reActive) reActive,SUM(reActiveSpend) reActiveSpend,SUM(reActiveImpression) reActiveImpression,SUM(reActiveClick) reActiveClick,SUM(reActiveDownload) reActiveDownload,
		SUM(uninstalls) uninstalls,SUM(uninstallsSpend) uninstallsSpend,SUM(uninstallsdownload) uninstallsdownload,SUM(activeSpend) activeSpend,
		SUM(addiction) addiction,SUM(addictionSpend) addictionSpend,SUM(addictionClick) addictionClick,SUM(addictionDownload) addictionDownload,
		SUM(gameReaD1) gameReaD1,SUM(gameReaD3) gameReaD3,SUM(gameReaD7) gameReaD7,SUM(gameRegister) gameRegister,SUM(gameRegisterSpend) gameRegisterSpend,
		SUM(gameRerD1) gameRerD1,SUM(gameRerD3) gameRerD3,SUM(gameRerD7) gameRerD7,SUM(gamePayUser) gamePayUser,SUM(gamePuD1) gamePuD1,SUM(gamePuD7) gamePuD7,SUM(gamePuH24) gamePuH24,
		SUM(gamePayFee) gamePayFee,SUM(gamePfH24) gamePfH24,SUM(gamePfD1) gamePfD1,SUM(gamePfD3) gamePfD3,SUM(gamePfD7) gamePfD7,SUM(gamePfD30) gamePfD30,
		SUM(gameIncome) gameIncome,SUM(gameIncomeAll) gameIncomeAll,SUM(gameIncome1) gameIncome1,SUM(gameIncome3) gameIncome3,SUM(gameIncome7) gameIncome7,SUM(gameIncome30) gameIncome30,
		SUM(gameFD1) gameFD1,SUM(gameFD3) gameFD3,SUM(gameFD7) gameFD7,SUM(gameFD30) gameFD30
		,ifnull(sum(gameIibpH24Format),0.00) gameIibpH24Format
		,ifnull(sum(gameIicpH24Format),0.00) gameIicpH24Format
		FROM dnwx_adt.dn_xiaomi_creative_report
		<include refid="CreativeXiaomiReportsql1"/>
	</select>

	<select id="getSubscribeRoiReport" resultType="com.wbgame.pojo.jettison.report.dto.SubscribeRoiReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.SubscribeRoiReportParam">
		SELECT
		SUM(cost) cost,SUM(rebate_cost) rebate_cost,SUM(clicks) clicks,SUM(installs) installs,SUM(fee_user_cnt) fee_user_cnt,SUM(new_pay_user_cnt) new_pay_user_cnt,
		SUM(new_pay_money) new_pay_money,SUM(total_sub_user_cnt) total_sub_user_cnt,SUM(total_sub_money) total_sub_money,SUM(retention_1day_user_cnt) retention_1day_user_cnt,
		SUM(retention_3day_user_cnt) retention_3day_user_cnt,SUM(retention_5day_user_cnt) retention_5day_user_cnt,SUM(retention_7day_user_cnt) retention_7day_user_cnt,
		SUM(installs)/SUM(clicks) AS installsRate,
		SUM(cost)/SUM(installs) AS installsCost,
		SUM(fee_user_cnt)/SUM(installs) AS fee_user_rate,
		SUM(cost)/SUM(fee_user_cnt) AS fee_user_cost,
		SUM(new_pay_user_cnt)/SUM(installs) AS new_pay_user_rate,
		SUM(cost)/SUM(new_pay_user_cnt) AS new_pay_user_cost,
		SUM(new_pay_money)/SUM(installs) AS new_pay_arpu,
		SUM(new_pay_money)/SUM(new_pay_user_cnt) AS new_pay_arppu,
		SUM(new_pay_money)/SUM(cost) AS new_pay_roi,
		SUM(total_sub_user_cnt)/SUM(installs) AS total_sub_user_rate,
		SUM(cost)/SUM(total_sub_user_cnt) AS total_sub_user_cost,
		SUM(total_sub_money)/SUM(installs) AS total_sub_arpu,
		SUM(total_sub_money)/SUM(total_sub_user_cnt) AS total_sub_arppu,
		SUM(total_sub_money)/SUM(cost) AS total_sub_roi
		<if test="group != null and group.size > 0">
			,
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'appid'">
						appid, app_name
					</when>
					<when test="item == 'group_id'">
						group_id, group_name
					</when>
					<when test="item == 'campaign_id'">
						campaign_id, campaign_name
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		FROM dnwx_bi.ads_tool_ios_roi_daily
		<include refid="SubscribeRoiReportsql1"/>
		<include refid="SubscribeRoiReportsql2"/>

	</select>

	<select id="getSubscribeRoiReportSummary" resultType="com.wbgame.pojo.jettison.report.dto.SubscribeRoiReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.SubscribeRoiReportParam">
		SELECT
		SUM(cost/100) cost,SUM(rebate_cost/100) rebate_cost,SUM(installs) installs,SUM(fee_user_cnt) fee_user_cnt,SUM(new_pay_user_cnt) new_pay_user_cnt,
		SUM(new_pay_money) new_pay_money,SUM(total_sub_user_cnt) total_sub_user_cnt,SUM(total_sub_money) total_sub_money,SUM(retention_1day_user_cnt) retention_1day_user_cnt,
		SUM(retention_3day_user_cnt) retention_3day_user_cnt,SUM(retention_5day_user_cnt) retention_5day_user_cnt,SUM(retention_7day_user_cnt) retention_7day_user_cnt
		FROM dnwx_bi.ads_tool_ios_roi_daily
		<include refid="SubscribeRoiReportsql1"/>
	</select>

	<select id="getSubscribeReport" resultType="com.wbgame.pojo.product.SubscribeReportVO"
			parameterType="com.wbgame.pojo.product.SubscribeReportDTO">
		SELECT
		SUM(reg_user_cnt) reg_user_cnt,SUM(fee_user_cnt) fee_user_cnt,SUM(pay_user_cnt) pay_user_cnt,SUM(cancel_fee_user_cnt) cancel_fee_user_cnt,
		SUM(first_sub_user_cnt) first_sub_user_cnt,SUM(first_sub_revenue) first_sub_revenue,SUM(notice_1_user_cnt) notice_1_user_cnt,SUM(notice_2_user_cnt) notice_2_user_cnt,
		SUM(notice_3_user_cnt) notice_3_user_cnt,SUM(notice_4_user_cnt) notice_4_user_cnt,SUM(notice_5_user_cnt) notice_5_user_cnt,SUM(notice_6_user_cnt) notice_6_user_cnt,
		SUM(notice_7_user_cnt) notice_7_user_cnt,SUM(notice_8_user_cnt) notice_8_user_cnt,SUM(notice_9_user_cnt) notice_9_user_cnt,SUM(notice_10_user_cnt) notice_10_user_cnt,
		SUM(notice_11_user_cnt) notice_11_user_cnt,SUM(notice_12_user_cnt) notice_12_user_cnt,SUM(notice_13_user_cnt) notice_13_user_cnt,SUM(notice_14_user_cnt) notice_14_user_cnt,
		SUM(notice_15_user_cnt) notice_15_user_cnt,SUM(notice_16_user_cnt) notice_16_user_cnt,SUM(notice_17_user_cnt) notice_17_user_cnt,SUM(notice_18_user_cnt) notice_18_user_cnt,
		SUM(notice_19_user_cnt) notice_19_user_cnt,SUM(notice_20_user_cnt) notice_20_user_cnt,SUM(notice_21_user_cnt) notice_21_user_cnt,SUM(notice_22_user_cnt) notice_22_user_cnt,
		SUM(notice_23_user_cnt) notice_23_user_cnt,SUM(notice_24_user_cnt) notice_24_user_cnt,SUM(notice_25_user_cnt) notice_25_user_cnt,SUM(notice_26_user_cnt) notice_26_user_cnt,
		SUM(notice_27_user_cnt) notice_27_user_cnt,SUM(notice_28_user_cnt) notice_28_user_cnt,SUM(notice_29_user_cnt) notice_29_user_cnt,SUM(notice_30_user_cnt) notice_30_user_cnt,
		SUM(fee_user_cnt)/SUM(reg_user_cnt) AS fee_user_rate,
		SUM(pay_user_cnt)/SUM(fee_user_cnt) AS pay_user_rate,
		SUM(cancel_fee_user_cnt)/SUM(fee_user_cnt) AS cancel_fee_user_rate,
		SUM(first_sub_user_cnt)/SUM(reg_user_cnt) AS first_sub_user_rate,
		SUM(first_sub_revenue)/SUM(reg_user_cnt) AS first_sub_revenue_arpu,
		SUM(first_sub_revenue)/SUM(first_sub_user_cnt) AS first_sub_revenue_arppu,
		SUM(notice_1_user_cnt)/SUM(first_sub_user_cnt) AS notice_1_user_rate,
		SUM(notice_1_user_cnt)/SUM(first_sub_user_cnt) AS notice_1_user_ltv,
		SUM(notice_2_user_cnt)/SUM(first_sub_user_cnt) AS notice_2_user_rate,
		SUM(notice_2_user_cnt)/SUM(first_sub_user_cnt) AS notice_2_user_ltv,
		SUM(notice_3_user_cnt)/SUM(first_sub_user_cnt) AS notice_3_user_rate,
		SUM(notice_3_user_cnt)/SUM(first_sub_user_cnt) AS notice_3_user_ltv,
		SUM(notice_4_user_cnt)/SUM(first_sub_user_cnt) AS notice_4_user_rate,
		SUM(notice_4_user_cnt)/SUM(first_sub_user_cnt) AS notice_4_user_ltv,
		SUM(notice_5_user_cnt)/SUM(first_sub_user_cnt) AS notice_5_user_rate,
		SUM(notice_5_user_cnt)/SUM(first_sub_user_cnt) AS notice_5_user_ltv,
		SUM(notice_6_user_cnt)/SUM(first_sub_user_cnt) AS notice_6_user_rate,
	   	SUM(notice_6_user_cnt)/SUM(first_sub_user_cnt) AS notice_6_user_ltv,
		SUM(notice_7_user_cnt)/SUM(first_sub_user_cnt) AS notice_7_user_rate,
		SUM(notice_7_user_cnt)/SUM(first_sub_user_cnt) AS notice_7_user_ltv,
		SUM(notice_8_user_cnt)/SUM(first_sub_user_cnt) AS notice_8_user_rate,
	   	SUM(notice_8_user_cnt)/SUM(first_sub_user_cnt) AS notice_8_user_ltv,
	   	SUM(notice_9_user_cnt)/SUM(first_sub_user_cnt) AS notice_9_user_rate,
		SUM(notice_9_user_cnt)/SUM(first_sub_user_cnt) AS notice_9_user_ltv,
		SUM(notice_10_user_cnt)/SUM(first_sub_user_cnt) AS notice_10_user_rate,
		SUM(notice_10_user_cnt)/SUM(first_sub_user_cnt) AS notice_10_user_ltv,
		SUM(notice_11_user_cnt)/SUM(first_sub_user_cnt) AS notice_11_user_rate,
		SUM(notice_11_user_cnt)/SUM(first_sub_user_cnt) AS notice_11_user_ltv,
	   	SUM(notice_12_user_cnt)/SUM(first_sub_user_cnt) AS notice_12_user_rate,
		SUM(notice_12_user_cnt)/SUM(first_sub_user_cnt) AS notice_12_user_ltv,
		SUM(notice_13_user_cnt)/SUM(first_sub_user_cnt) AS notice_13_user_rate,
		SUM(notice_13_user_cnt)/SUM(first_sub_user_cnt) AS notice_13_user_ltv,
		SUM(notice_14_user_cnt)/SUM(first_sub_user_cnt) AS notice_14_user_rate,
		SUM(notice_14_user_cnt)/SUM(first_sub_user_cnt) AS notice_14_user_ltv,
		SUM(notice_15_user_cnt)/SUM(first_sub_user_cnt) AS notice_15_user_rate,
		SUM(notice_15_user_cnt)/SUM(first_sub_user_cnt) AS notice_15_user_ltv,
		SUM(notice_16_user_cnt)/SUM(first_sub_user_cnt) AS notice_16_user_rate,
		SUM(notice_16_user_cnt)/SUM(first_sub_user_cnt) AS notice_16_user_ltv,
		SUM(notice_17_user_cnt)/SUM(first_sub_user_cnt) AS notice_17_user_rate,
		SUM(notice_17_user_cnt)/SUM(first_sub_user_cnt) AS notice_17_user_ltv,
		SUM(notice_18_user_cnt)/SUM(first_sub_user_cnt) AS notice_18_user_rate,
		SUM(notice_18_user_cnt)/SUM(first_sub_user_cnt) AS notice_18_user_ltv,
		SUM(notice_19_user_cnt)/SUM(first_sub_user_cnt) AS notice_19_user_rate,
		SUM(notice_19_user_cnt)/SUM(first_sub_user_cnt) AS notice_19_user_ltv,
		SUM(notice_20_user_cnt)/SUM(first_sub_user_cnt) AS notice_20_user_rate,
		SUM(notice_20_user_cnt)/SUM(first_sub_user_cnt) AS notice_20_user_ltv,
		SUM(notice_21_user_cnt)/SUM(first_sub_user_cnt) AS notice_21_user_rate,
		SUM(notice_21_user_cnt)/SUM(first_sub_user_cnt) AS notice_21_user_ltv,
		SUM(notice_22_user_cnt)/SUM(first_sub_user_cnt) AS notice_22_user_rate,
		SUM(notice_22_user_cnt)/SUM(first_sub_user_cnt) AS notice_22_user_ltv,
		SUM(notice_23_user_cnt)/SUM(first_sub_user_cnt) AS notice_23_user_rate,
		SUM(notice_23_user_cnt)/SUM(first_sub_user_cnt) AS notice_23_user_ltv,
		SUM(notice_24_user_cnt)/SUM(first_sub_user_cnt) AS notice_24_user_rate,
		SUM(notice_24_user_cnt)/SUM(first_sub_user_cnt) AS notice_24_user_ltv,
		SUM(notice_25_user_cnt)/SUM(first_sub_user_cnt) AS notice_25_user_rate,
		SUM(notice_25_user_cnt)/SUM(first_sub_user_cnt) AS notice_25_user_ltv,
		SUM(notice_26_user_cnt)/SUM(first_sub_user_cnt) AS notice_26_user_rate,
		SUM(notice_26_user_cnt)/SUM(first_sub_user_cnt) AS notice_26_user_ltv,
		SUM(notice_27_user_cnt)/SUM(first_sub_user_cnt) AS notice_27_user_rate,
		SUM(notice_27_user_cnt)/SUM(first_sub_user_cnt) AS notice_27_user_ltv,
		SUM(notice_28_user_cnt)/SUM(first_sub_user_cnt) AS notice_28_user_rate,
		SUM(notice_28_user_cnt)/SUM(first_sub_user_cnt) AS notice_28_user_ltv,
		SUM(notice_29_user_cnt)/SUM(first_sub_user_cnt) AS notice_29_user_rate,
		SUM(notice_29_user_cnt)/SUM(first_sub_user_cnt) AS notice_29_user_ltv,
		SUM(notice_30_user_cnt)/SUM(first_sub_user_cnt) AS notice_30_user_rate,
		SUM(notice_30_user_cnt)/SUM(first_sub_user_cnt) AS notice_30_user_ltv
		<if test="group != null and group.size > 0">
			,
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'appid'">
						appid, app_name
					</when>
					<when test="item == 'group_id'">
						group_id, group_name
					</when>
					<when test="item == 'campaign_id'">
						campaign_id, campaign_name
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		FROM dnwx_bi.ads_tool_ios_goods_info_daily
		<include refid="SubscribeReportsql1"/>
		<include refid="SubscribeReportsql2"/>

	</select>

	<select id="getSubscribeReportSummary" resultType="com.wbgame.pojo.product.SubscribeReportVO"
			parameterType="com.wbgame.pojo.product.SubscribeReportDTO">
		SELECT
		SUM(reg_user_cnt) reg_user_cnt,SUM(fee_user_cnt) fee_user_cnt,SUM(pay_user_cnt) pay_user_cnt,SUM(cancel_fee_user_cnt) cancel_fee_user_cnt,
		SUM(first_sub_user_cnt) first_sub_user_cnt,SUM(first_sub_revenue) first_sub_revenue,SUM(notice_1_user_cnt) notice_1_user_cnt,SUM(notice_2_user_cnt) notice_2_user_cnt,
		SUM(notice_3_user_cnt) notice_3_user_cnt,SUM(notice_4_user_cnt) notice_4_user_cnt,SUM(notice_5_user_cnt) notice_5_user_cnt,SUM(notice_6_user_cnt) notice_6_user_cnt,
		SUM(notice_7_user_cnt) notice_7_user_cnt,SUM(notice_8_user_cnt) notice_8_user_cnt,SUM(notice_9_user_cnt) notice_9_user_cnt,SUM(notice_10_user_cnt) notice_10_user_cnt,
		SUM(notice_11_user_cnt) notice_11_user_cnt,SUM(notice_12_user_cnt) notice_12_user_cnt,SUM(notice_13_user_cnt) notice_13_user_cnt,SUM(notice_14_user_cnt) notice_14_user_cnt,
		SUM(notice_15_user_cnt) notice_15_user_cnt,SUM(notice_16_user_cnt) notice_16_user_cnt,SUM(notice_17_user_cnt) notice_17_user_cnt,SUM(notice_18_user_cnt) notice_18_user_cnt,
		SUM(notice_19_user_cnt) notice_19_user_cnt,SUM(notice_20_user_cnt) notice_20_user_cnt,SUM(notice_21_user_cnt) notice_21_user_cnt,SUM(notice_22_user_cnt) notice_22_user_cnt,
		SUM(notice_23_user_cnt) notice_23_user_cnt,SUM(notice_24_user_cnt) notice_24_user_cnt,SUM(notice_25_user_cnt) notice_25_user_cnt,SUM(notice_26_user_cnt) notice_26_user_cnt,
		SUM(notice_27_user_cnt) notice_27_user_cnt,SUM(notice_28_user_cnt) notice_28_user_cnt,SUM(notice_29_user_cnt) notice_29_user_cnt,SUM(notice_30_user_cnt) notice_30_user_cnt
		FROM dnwx_bi.ads_tool_ios_goods_info_daily
		<include refid="SubscribeReportsql1"/>
	</select>

	<sql id="dncha_cash_sql">
		select  d.cha_sub_launch, d.cha_type_name,a.app_name appname,d.`out`,
			IFNULL(TRUNCATE(sum(request_count),2), 0) request_count,
			IFNULL(TRUNCATE(sum(return_count),2), 0) return_count,
			IFNULL(TRUNCATE(sum(pv),2), 0) pv,
			IFNULL(TRUNCATE(sum(revenue),2), 0) revenue,
			IFNULL(TRUNCATE(sum(dollar_revenue),2), 0) dollar_revenue,
			IFNULL(TRUNCATE(sum(revenue)/sum(pv)*1000,2), 0) ecpm,
			IFNULL(TRUNCATE(sum(click),2), 0) click,
			IFNULL(TRUNCATE(sum(return_count)/sum(request_count)*100,2), 0) fill_rate,
			IFNULL(TRUNCATE(sum(pv)/sum(return_count)*100,2), 0) pv_rate,
			IFNULL(TRUNCATE(sum(click)/sum(pv)*100,2), 0) click_rate,
			IFNULL(CAST(SUM(revenue)/SUM(click) AS decimal(18,3)), 0) cpc


		<if test="groups != null and groups.size > 0">
		    ,
			<foreach collection="groups" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'sdk_appid'">
						d.app_id sdk_appid
					</when>
					<when test="item == 'sdk_code'">
						d.placement_id sdk_code
					</when>
					<otherwise>
						`${item}`
					</otherwise>

				</choose>
			</foreach>
		</if>
		FROM dn_cha_cash_total d
		LEFT JOIN dnwx_adt.app_info a on d.dnappid = a.id

		WHERE `date` BETWEEN #{start_date} AND #{end_date} and d.app_id != '0'
		<if test="appid != null and appid != ''">
			and dnappid in (${appid})
		</if>
		<if test="cha_type != null and cha_type != ''">
			and cha_type in (${cha_type})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media})
		</if>
		<if test="cha_sub_launch != null and cha_sub_launch != ''">
			and cha_sub_launch = #{cha_sub_launch}
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id})
		</if>

		<if test="agent != null and agent != ''">
			and agent = #{agent}
		</if>
		<if test="placement_type != null and placement_type != ''">
			and placement_type = #{placement_type}
		</if>
		<if test="ad_sid != null and ad_sid != ''">
			and ad_sid like concat('%',#{ad_sid},'%')
		</if>
		<if test="detail_ad_sid != null and detail_ad_sid != ''">
			and ad_sid = #{detail_ad_sid}
		</if>
		<if test="country != null and country != ''">
			and country = #{country}
		</if>
		<if test="open_type != null and open_type != ''">
			and open_type = #{open_type}
		</if>
		<if test="out != null and out != ''">
			and `out` = #{out}
		</if>
		<if test="sdk_code != null and sdk_code != ''">
			and d.placement_id = #{sdk_code}
		</if>
		<if test="sdk_appid != null and sdk_appid != ''">
			and d.app_id = #{sdk_appid}
		</if>
		<if test="appid_tag != null and appid_tag != ''">
			<choose>
				<when test="appid_tag_rev != null and appid_tag_rev != ''">
					AND CONCAT(dnappid,'#',cha_id) not in (${appid_tag})
				</when>
				<otherwise>
					AND CONCAT(dnappid,'#',cha_id) in (${appid_tag})
				</otherwise>
			</choose>
		</if>


		<if test="groups != null and groups.size > 0">
			GROUP BY
			<foreach collection="groups" index="index" item="item" separator=",">
				`${item}`
			</foreach>
		</if>
		<if test="ad_sid_group != null and ad_sid_group != ''">
			having (sum(pv)+sum(revenue)) > 0
		</if>
		<if test="order_str == null or order_str == ''">
			ORDER BY `date` ASC, revenue DESC
		</if>
		<if test="order_str != null and order_str != ''">
			ORDER BY
				<foreach collection="groups" index="index" item="item" >
					CASE WHEN ${item} is null or ${item}='' THEN 1 ELSE 0 END,
				</foreach>

			    ${order_str}
		</if>
	</sql>

	<select id="getHourSpendReport" resultType="com.wbgame.pojo.jettison.report.dto.SpendReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.SpendReportParam">
		SELECT SUM(spend) AS spend,SUM(rebateSpend) AS rebateSpend
		<if test="group != null and group.size > 0">
			,
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'appCategory'">
						a.app_category AS appCategory
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		FROM dnwx_adt.dn_report_spend_china_hour h
		LEFT JOIN dnwx_adt.app_info a on h.app = a.id
		<include refid="getHourSpendReportCondition1"/>
		<include refid="getHourSpendReportCondition2"/>
	</select>

	<select id="getHourSpendReportSummary" resultType="com.wbgame.pojo.jettison.report.dto.SpendReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.SpendReportParam">
		SELECT SUM(spend) AS spend,SUM(rebateSpend) AS rebateSpend FROM dnwx_adt.dn_report_spend_china_hour
		<include refid="getHourSpendReportCondition1"/>
	</select>

	<select id="getHourSpendReportChart" resultType="com.wbgame.pojo.jettison.report.dto.SpendReportDTO"
			parameterType="com.wbgame.pojo.jettison.report.param.SpendReportParam">
		SELECT `hour`,SUM(spend)/(datediff(#{end_date}, #{start_date})+1) AS spend,SUM(rebateSpend) AS rebateSpend
		FROM dnwx_adt.dn_report_spend_china_hour
		WHERE `day` BETWEEN #{start_date} AND #{end_date}
		<if test="app != null and app.size > 0">
			AND app IN
			<foreach collection="app" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="media != null and media.size > 0">
			AND media IN
			<foreach collection="media" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="putUser != null and putUser.size > 0">
			AND putUser IN
			<foreach collection="putUser" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="account != null and account.size > 0">
			AND account IN
			<foreach collection="account" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		GROUP BY `hour` ORDER BY `hour` ASC
	</select>

	<sql id="getOperationReportCondition1">
		WHERE 1 = 1
		<if test="app != null and app.size > 0">
			AND app IN
			<foreach collection="app" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="appCategory != null and appCategory.size > 0">
			AND app_category IN
			<foreach collection="appCategory" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="ad_platform != null and ad_platform.size > 0">
			AND ad_platform IN
			<foreach collection="ad_platform" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>

		<if test="channel != null and channel.size > 0">
			AND channel IN
			<foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="companyName != null and companyName.size > 0">
			AND companyName IN
			<foreach collection="companyName" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="gameName != null and gameName.size > 0">
			AND gameName IN
			<foreach collection="gameName" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="packageName != null and packageName != ''">
			and packageName like concat('%',#{packageName},'%')
		</if>
		<if test="app_type != null">
			and app_type = #{app_type}
		</if>
		<if test="start_date != null and end_date!= null">
			AND day <![CDATA[ >= ]]> #{start_date}
			AND day <![CDATA[ <= ]]> #{end_date}
		</if>
		<if test="appid_tag != null and appid_tag != ''">
			<choose>
				<when test="appid_tag_rev != null and appid_tag_rev != ''">
					AND CONCAT(app,'#',channel) not in (${appid_tag})
				</when>
				<otherwise>
					AND CONCAT(app,'#',channel) in (${appid_tag})
				</otherwise>
			</choose>
		</if>
	</sql>

	<sql id="getOperationReportCondition2">
		<if test="group != null and group.size > 0">
			GROUP BY
			<foreach collection="group" index="index" item="item" separator=",">
			    <choose>
					<when test="item == 'week'">
						DATE_FORMAT(day, '%x-%v')
					</when>
					<when test="item == 'month'">
						DATE_FORMAT(day,'%Y-%m')
					</when>
					<when test="item == 'beek'">
						CONCAT(DATE_FORMAT(day, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(day, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))
					</when>
					<when test="item == 'appCategory'">
						app_category
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		<if test="key != null and key != ''">
			<if test="index == 'spend'">
				HAVING IFNULL(SUM(spend),0.00) <![CDATA[ ${symbol} ]]> #{number}
			</if>
			<if test="index == 'rebateSpend'">
				HAVING IFNULL(SUM(rebateSpend),0.00) <![CDATA[ ${symbol} ]]> #{number}
			</if>
		</if>
	</sql>

	<sql id="getSpendReportCondition1">
		WHERE 1 = 1
		<if test="app != null and app.size > 0">
			AND app IN
			<foreach collection="app" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="appCategory != null and appCategory.size > 0">
			AND app_category IN
			<foreach collection="appCategory" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="creative_template != null and creative_template != ''">
			AND creative_template IN (${creative_template})
		</if>
		<if test="os_type != null and os_type != ''">
			AND os_type IN (${os_type})
		</if>
		<if test="accountSubject != null and accountSubject != ''">
			AND accountSubject IN (${accountSubject})
		</if>
		<if test="delivery_mode != null and delivery_mode != ''">
			AND delivery_mode = #{delivery_mode}
		</if>
		<if test="channelType != null and channelType.size > 0">
			AND channelType IN
			<foreach collection="channelType" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="media != null and media.size > 0">
			AND media IN
			<foreach collection="media" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="channel != null and channel.size > 0">
			AND channel IN
			<foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="type != null and type.size > 0">
			AND `type` IN
			<foreach collection="type" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="agent != null and agent.size > 0">
			AND agent IN
			<foreach collection="agent" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="first_agent != null and first_agent.size > 0">
			AND first_agent IN
			<foreach collection="first_agent" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="putUser != null and putUser.size > 0">
			AND putUser IN
			<foreach collection="putUser" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="artist != null and artist.size > 0">
			AND (
			artist IN
			<foreach collection="artist" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			OR
			<foreach collection="artist" index="index" item="item" separator="OR">
				artist LIKE "%"#{item}"%"
			</foreach>
			)
		</if>
		<if test="account != null and account.size > 0">
			AND account IN
			<foreach collection="account" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="adsenseType != null and adsenseType.size > 0">
			AND adsenseType IN
			<foreach collection="adsenseType" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="adsensePosition != null and adsensePosition.size > 0">
			AND (
			adsensePosition IN
			<foreach collection="adsensePosition" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
		<if test="company != null and company.size > 0">
			AND company IN
			<foreach collection="company" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="gameName != null and gameName.size > 0">
			AND gameName IN
			<foreach collection="gameName" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="package_name != null and package_name.size > 0">
			AND package_name IN
			<foreach collection="package_name" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="shop_id != null and shop_id.size > 0">
			AND shop_id IN
			<foreach collection="shop_id" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="phonePlatform != null and phonePlatform.size > 0">
			AND phonePlatform IN
			<foreach collection="phonePlatform" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="transferType != null and transferType.size > 0">
			AND transferType IN
			<foreach collection="transferType" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="start_date != null and end_date!= null">
			AND day BETWEEN #{start_date} AND #{end_date}
		</if>
		<if test="campaignName != null and campaignName != ''">
			AND campaign LIKE "%"#{campaignName}"%"
		</if>
		<if test="campaignId != null and campaignId !=''">
			AND campaignId LIKE CONCAT('%',#{campaignId},'%')
		</if>
		<if test="groupName != null and groupName != ''">
			AND groupName LIKE "%"#{groupName}"%"
		</if>
		<if test="groupId != null and groupId !=''">
			AND groupId LIKE CONCAT('%',#{groupId},'%')
		</if>
		<if test="bidType != null and bidType != ''">
			AND bidType = #{bidType}
		</if>
		<if test="appid_tag != null and appid_tag != ''">
			<choose>
				<when test="appid_tag_rev != null and appid_tag_rev != ''">
					AND CONCAT(app,'#',channel1) not in (${appid_tag})
				</when>
				<otherwise>
					AND CONCAT(app,'#',channel1) in (${appid_tag})
				</otherwise>
			</choose>
		</if>
		<if test="planId != null and planId != ''">
			AND planId = #{planId}
		</if>
		<if test="planName != null and planName != ''">
			AND planName like CONCAT('%',#{planName},'%')
		</if>
	</sql>

	<sql id="getSpendReportCondition2">
		<if test="group != null and group.size>0">
			GROUP BY
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'week'">
						DATE_FORMAT(day, '%x-%v')
					</when>
					<when test="item == 'month'">
						DATE_FORMAT(day,'%Y-%m')
					</when>
					<when test="item == 'agent'">
						first_agent, agent
					</when>
					<when test="item == 'appCategory'">
						a.app_category
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		<if test="key != null and key != ''">
			<if test="index == 'roi_1'">
				HAVING IFNULL(ROUND(SUM(revenue1)/SUM(spend) * 100,2),0.00) <![CDATA[ ${symbol} ]]> #{number}
			</if>
			<if test="index == 'spend'">
				HAVING IFNULL(SUM(spend),0.00) <![CDATA[ ${symbol} ]]> #{number}
			</if>
			<if test="index == 'rebateSpend'">
				HAVING IFNULL(SUM(rebateSpend),0.00) <![CDATA[ ${symbol} ]]> #{number}
			</if>
			<if test="index == 'installs'">
				HAVING IFNULL(SUM(installs),0) <![CDATA[ ${symbol} ]]> #{number}
			</if>
			<if test="index == 'revenue1'">
				HAVING IFNULL(ROUND(SUM(revenue1),0.00) <![CDATA[ ${symbol} ]]> #{number}
			</if>
		</if>
		ORDER BY ${order_str}
	</sql>

	<sql id="getHourSpendReportCondition1">
		WHERE 1 = 1
		<if test="app != null and app.size > 0">
			AND app IN
			<foreach collection="app" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="media != null and media.size > 0">
			AND media IN
			<foreach collection="media" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="putUser != null and putUser.size > 0">
			AND putUser IN
			<foreach collection="putUser" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="account != null and account.size > 0">
			AND account IN
			<foreach collection="account" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="start_date != null and end_date!= null">
			AND `day` BETWEEN #{start_date} AND #{end_date}
		</if>
		<if test="start_hour != null and start_hour != '' and end_hour!= null and end_hour != '' ">
			AND `hour` BETWEEN #{start_hour} AND #{end_hour}
		</if>
	</sql>

	<sql id="getHourSpendReportCondition2">
		<if test="group != null and group.size>0">
			GROUP BY
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'appCategory'">
						a.app_category
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		<if test="order_str == null or order_str == ''">
			ORDER BY `day` ASC, `hour` ASC, spend DESC
		</if>
		<if test="order_str != null and order_str != ''">
			ORDER BY ${order_str}
		</if>
	</sql>

	<sql id="getDirectReportCondition1">
		WHERE 1 = 1
		<if test="app != null and app.size > 0">
			AND app IN
			<foreach collection="app" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="media != null and media.size > 0">
			AND media IN
			<foreach collection="media" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="channel != null and channel.size > 0">
			AND channel IN
			<foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="start_date != null and end_date!= null">
			AND `day` BETWEEN #{start_date} AND #{end_date}
		</if>

		<if test="appid_tag != null and appid_tag != ''">
			<choose>
				<when test="appid_tag_rev != null and appid_tag_rev != ''">
					AND CONCAT(app,'#',channel) not in (${appid_tag})
				</when>
				<otherwise>
					AND CONCAT(app,'#',channel) in (${appid_tag})
				</otherwise>
			</choose>
		</if>
	</sql>

	<sql id="getDirectReportCondition2">
		<if test="group != null and group.size > 0">
			GROUP BY
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'week'">
						DATE_FORMAT(day, '%x-%v')
					</when>
					<when test="item == 'month'">
						DATE_FORMAT(day,'%Y-%m')
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		<if test="key != null and key != ''">
			<if test="index == 'spend'">
				HAVING IFNULL(SUM(spend),0.00) <![CDATA[ ${symbol} ]]> #{number}
			</if>
			<if test="index == 'directSpend'">
				HAVING IFNULL(SUM(directSpend),0.00) <![CDATA[ ${symbol} ]]> #{number}
			</if>
		</if>
		<if test="order_str == null or order_str == ''">
			ORDER BY `day` ASC, SUM( channelSpend)+SUM(directSpend) DESC
		</if>
	</sql>

	<sql id="getMaterialReportCondition1">
		WHERE 1 = 1
		<if test="ad_platform != null and ad_platform.size > 0">
			AND ad_platform IN
			<foreach collection="ad_platform" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="channel != null and channel.size > 0">
			AND channel IN
			<foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="putUser != null and putUser.size > 0">
			AND putUser IN
			<foreach collection="putUser" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="artist_real != null and artist_real.size > 0">
			AND (
			artist_real IN
			<foreach collection="artist_real" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			OR
			<foreach collection="artist_real" index="index" item="item" separator="OR">
				artist_real LIKE "%"#{item}"%"
			</foreach>
			)
		</if>
		<if test="product_part != null and product_part.size > 0">
			AND (
			product_part IN
			<foreach collection="product_part" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			OR
			<foreach collection="product_part" index="index" item="item" separator="OR">
				product_part LIKE "%"#{item}"%"
			</foreach>
			)
		</if>
		<if test="artist != null and artist.size > 0">
			AND (
			artist IN
			<foreach collection="artist" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			OR
			<foreach collection="artist" index="index" item="item" separator="OR">
				artist LIKE "%"#{item}"%"
			</foreach>
			)
		</if>
		<if test="artist3d != null and artist3d.size > 0">
			AND (
			artist3d IN
			<foreach collection="artist3d" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			OR
			<foreach collection="artist3d" index="index" item="item" separator="OR">
				artist3d LIKE "%"#{item}"%"
			</foreach>
			)
		</if>
		<if test="creativist != null and creativist.size > 0">
			AND (
			creativist IN
			<foreach collection="creativist" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			OR
			<foreach collection="creativist" index="index" item="item" separator="OR">
				creativist LIKE "%"#{item}"%"
			</foreach>
			)
		</if>
		<if test="artists != null and artists !=''">
			AND (
			artist LIKE "%"#{artists}"%" OR
			artist3d LIKE "%"#{artists}"%" OR
			creativist LIKE "%"#{artists}"%" OR
			product_part LIKE "%"#{artists}"%"
			)
		</if>
		<if test="app != null and app.size > 0">
			AND app IN
			<foreach collection="app" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="type != null and type.size > 0">
			AND `type` IN
			<foreach collection="type" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="fileName != null and fileName != ''">
			AND fileName LIKE "%"#{fileName}"%"
		</if>
		<if test="fileNames != null and fileNames.size > 0">
			AND (
			<foreach collection="fileNames" separator="or" item="item">
				fileName like "%"#{item}"%"
			</foreach>
			)
		</if>
		<if test="createDay != null and createDay.size > 0">
			AND createDay <![CDATA[ >= ]]> #{createDay[0]}
			AND createDay <![CDATA[ <= ]]> #{createDay[1]}
		</if>
		<if test="account != null and account.size > 0">
			AND account IN
			<foreach collection="account" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="label1 != null and label1.size > 0">
			AND label1 IN
			<foreach collection="label1" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="label2 != null and label2.size > 0">
			AND label2 IN
			<foreach collection="label2" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="label3 != null and label3.size !=''">
			AND
			<foreach collection="label3" index="index" item="item" open="(" separator=" OR " close=")">
				label3 LIKE "%"#{item}"%"
			</foreach>
		</if>
		<if test="scriptNum != null and scriptNum != ''">
			AND scriptNum LIKE "%"#{scriptNum}"%"
		</if>
		<if test="start_date != null and end_date!= null">
			AND `day` BETWEEN #{start_date} AND #{end_date}
		</if>
		<if test="specs != null and specs.size > 0">
			AND specs IN
			<foreach collection="specs" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</sql>

	<sql id="getArtistSpendCondition">
		WHERE 1 = 1
		<if test="ad_platform != null and ad_platform.size > 0">
			AND ad_platform IN
			<foreach collection="ad_platform" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="channel != null and channel.size > 0">
			AND channel IN
			<foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="putUser != null and putUser.size > 0">
			AND putUser IN
			<foreach collection="putUser" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="artist != null and artist.size > 0">
			AND (
			<foreach collection="artist" index="index" item="item" separator="OR">
				artist LIKE "%"#{item}"%" OR artist3d LIKE "%"#{item}"%" OR creativist LIKE "%"#{item}"%" OR product_part LIKE "%"#{item}"%"
			</foreach>
			)
		</if>
		<if test="type != null and type.size > 0">
			AND `type` IN
			<foreach collection="type" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="createDay != null and createDay.size > 0">
			AND createDay <![CDATA[ >= ]]> #{createDay[0]}
			AND createDay <![CDATA[ <= ]]> #{createDay[1]}
		</if>
		<if test="start_date != null and end_date!= null">
			AND `day` BETWEEN #{start_date} AND #{end_date}
		</if>
	</sql>
	<sql id="getMaterialReportCondition2">
		<if test="group != null and group.size > 0">
			GROUP BY
			<foreach collection="group" index="index" item="item" separator=",">
				`${item}`
			</foreach>
		</if>
		<if test="order_str == null or order_str == ''">
			ORDER BY `day` ASC, spend DESC
		</if>
		<if test="order_str != null and order_str != ''">
			ORDER BY ${order_str}
		</if>
	</sql>

	<sql id="getCreativeReportCondition1">
		WHERE signature != ''
		<if test="type != null and type != ''">
			AND `type` = #{type}
		</if>
		<if test="ad_platform != null and ad_platform.size > 0">
			AND ad_platform IN
			<foreach collection="ad_platform" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="channel != null and channel.size > 0">
			AND channel IN
			<foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="putUser != null and putUser.size > 0">
			AND putUser IN
			<foreach collection="putUser" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="artist != null and artist.size > 0">
			AND (
			artist IN
			<foreach collection="artist" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			OR
			<foreach collection="artist" index="index" item="item" separator="OR">
				artist LIKE "%"#{item}"%"
			</foreach>
			)
		</if>
		<if test="artist3d != null and artist3d.size > 0">
			AND (
			artist3d IN
			<foreach collection="artist3d" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			OR
			<foreach collection="artist3d" index="index" item="item" separator="OR">
				artist3d LIKE "%"#{item}"%"
			</foreach>
			)
		</if>
		<if test="creativist != null and creativist.size > 0">
			AND (
			creativist IN
			<foreach collection="creativist" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			OR
			<foreach collection="creativist" index="index" item="item" separator="OR">
				creativist LIKE "%"#{item}"%"
			</foreach>
			)
		</if>
		<if test="artists != null and artists !=''">
			AND (
			artist LIKE "%"#{artists}"%" OR
			artist3d LIKE "%"#{artists}"%" OR
			creativist LIKE "%"#{artists}"%"
			)
		</if>
		<if test="app != null and app.size > 0">
			AND app IN
			<foreach collection="app" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="createDay != null and createDay.size > 0">
			AND createDay <![CDATA[ >= ]]> #{createDay[0]}
			AND createDay <![CDATA[ <= ]]> #{createDay[1]}
		</if>
		<if test="start_date != null and end_date!= null">
			AND `day` BETWEEN #{start_date} AND #{end_date}
		</if>
	</sql>

	<sql id="getCreativeReportCondition2">
		<if test="group != null and group.size > 0">
			GROUP BY
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'artist'">
						artist,artist3d,creativist
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		<if test="order_str == null or order_str == ''">
			ORDER BY `day` ASC, installs DESC
		</if>
		<if test="order_str != null and order_str != ''">
			ORDER BY ${order_str}
		</if>
	</sql>

	<sql id="CreativeXiaomiReportsql1">
		WHERE 1=1
		<if test="roi_range_min != null">
			and (gameIncome1/spend) <![CDATA[ >= ]]> #{roi_range_min}
		</if>
		<if test="roi_range_max != null">
			and (gameIncome1/spend) <![CDATA[ <= ]]> #{roi_range_max}
		</if>
		<if test="spend_range_min != null">
		    and spend <![CDATA[ >= ]]> #{spend_range_min}
		</if>
		<if test="spend_range_max != null">
		    and spend <![CDATA[ <= ]]> #{spend_range_max}
		</if>
		<if test="account != null and account.size > 0">
			AND account IN
			<foreach collection="account" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="trackingTypes != null and trackingTypes.size > 0">
			AND tracking_type IN
			<foreach collection="trackingTypes" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="miType != null and miType.size > 0">
			AND miType IN
			<foreach collection="miType" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="highType != null and highType.size > 0">
			AND highType IN
			<foreach collection="highType" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="midType != null and midType.size > 0">
			AND midType IN
			<foreach collection="midType" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="lowType != null and lowType.size > 0">
			AND lowType IN
			<foreach collection="lowType" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="channel != null and channel.size > 0">
			AND channel IN
			<foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="app != null and app.size > 0">
			AND app IN
			<foreach collection="app" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="first_agent != null and first_agent.size > 0">
			AND first_agent IN
			<foreach collection="first_agent" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="agent != null and agent.size > 0">
			AND agent IN
			<foreach collection="agent" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="company != null and company.size > 0">
			AND company IN
			<foreach collection="company" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="putUser != null and putUser.size > 0">
			AND putUser IN
			<foreach collection="putUser" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="transferType != null and transferType.size > 0">
			AND transferType IN
			<foreach collection="transferType" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="priceType != null and priceType.size > 0">
			AND priceType IN
			<foreach collection="priceType" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="campaignName != null and campaignName !=''">
			AND campaignName LIKE "%"#{campaignName}"%"
		</if>
		<if test="campaignId != null and campaignId !=''">
			AND campaignId LIKE CONCAT('%',#{campaignId},'%')
		</if>
		<if test="groupName != null and groupName !=''">
			AND groupName LIKE "%"#{groupName}"%"
		</if>
		<if test="groupId != null and groupId !=''">
			AND groupId LIKE CONCAT('%',#{groupId},'%')
		</if>
		<if test="creativeName != null and creativeName !=''">
			AND creativeName LIKE "%"#{creativeName}"%"
		</if>
		<if test="creative_id != null and creative_id !=''">
			AND creative_id LIKE CONCAT('%',#{creative_id},'%')
		</if>
		<if test="start_date != null and end_date!= null">
			AND `day` BETWEEN #{start_date} AND #{end_date}
		</if>
		<if test="artist != null and artist.size > 0">
			AND (
			artist IN
			<foreach collection="artist" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			OR
			<foreach collection="artist" index="index" item="item" separator="OR">
				artist LIKE "%"#{item}"%"
			</foreach>
			)
		</if>
		<if test="gameName != null and gameName.size > 0">
			AND gameName IN
			<foreach collection="gameName" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="inDelivery != null and inDelivery != ''">
			AND in_delivery = #{inDelivery}
		</if>
	</sql>

	<sql id="CreativeXiaomiReportsql2">
		<if test="group != null and group.size > 0">
			GROUP BY
			<foreach collection="group" index="index" item="item" separator=",">
				<choose>
					<when test="item == 'week'">
						DATE_FORMAT(day, '%x-%v')
					</when>
					<when test="item == 'month'">
						DATE_FORMAT(day,'%Y-%m')
					</when>
					<when test="item == 'agent'">
						first_agent, agent
					</when>
					<when test="item == 'appCategory'">
						a.app_category
					</when>
					<otherwise>
						`${item}`
					</otherwise>
				</choose>
			</foreach>
		</if>
		<if test="order_str == null or order_str == ''">
			ORDER BY `day` ASC, spend DESC
		</if>
		<if test="order_str != null and order_str != ''">
			ORDER BY ${order_str}
		</if>
	</sql>

	<sql id="SubscribeRoiReportsql1">
		WHERE 1=1
		<if test="account != null and account.size > 0">
			AND account IN
			<foreach collection="account" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="ad_buy_media != null and ad_buy_media.size > 0">
			AND ad_buy_media IN
			<foreach collection="ad_buy_media" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="country != null and country.size > 0">
			AND country IN
			<foreach collection="country" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="channel != null and channel.size > 0">
			AND channel IN
			<foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="appid != null and appid.size > 0">
			AND appid IN
			<foreach collection="appid" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="group_name != null and group_name !=''">
			AND group_name LIKE "%"#{group_name}"%"
		</if>
		<if test="campaign_name != null and campaign_name !=''">
			AND campaign_name LIKE "%"#{campaign_name}"%"
		</if>
		<if test="start_date != null and end_date!= null">
			AND tdate BETWEEN #{start_date} AND #{end_date}
		</if>
	</sql>

	<sql id="SubscribeRoiReportsql2">
		<if test="group != null and group.size > 0">
			GROUP BY
			<foreach collection="group" index="index" item="item" separator=",">
				${item}
			</foreach>
		</if>
		<if test="order_str == null or order_str == ''">
			ORDER BY `tdate` ASC, cost DESC
		</if>
		<if test="order_str != null and order_str != ''">
			ORDER BY ${order_str}
		</if>
	</sql>

	<sql id="SubscribeReportsql1">
		WHERE 1=1
		<if test="account != null and account.size > 0">
			AND account IN
			<foreach collection="account" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="ad_buy_media != null and ad_buy_media.size > 0">
			AND ad_buy_media IN
			<foreach collection="ad_buy_media" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="country != null and country.size > 0">
			AND country IN
			<foreach collection="country" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="channel != null and channel.size > 0">
			AND channel IN
			<foreach collection="channel" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="appid != null and appid.size > 0">
			AND appid IN
			<foreach collection="appid" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="goods_type != null and goods_type !=''">
			AND goods_type LIKE "%"#{goods_type}"%"
		</if>
		<if test="goods_id != null and goods_id !=''">
			AND goods_id LIKE "%"#{goods_id}"%"
		</if>
		<if test="user_label != null and user_label !=''">
			AND user_label LIKE "%"#{user_label}"%"
		</if>
		<if test="group_name != null and group_name !=''">
			AND group_name LIKE "%"#{group_name}"%"
		</if>
		<if test="campaign_name != null and campaign_name !=''">
			AND campaign_name LIKE "%"#{campaign_name}"%"
		</if>
		<if test="start_date != null and end_date!= null">
			AND tdate BETWEEN #{start_date} AND #{end_date}
		</if>
	</sql>

	<sql id="SubscribeReportsql2">
		<if test="group != null and group.size > 0">
			GROUP BY
			<foreach collection="group" index="index" item="item" separator=",">
				${item}
			</foreach>
		</if>
		<if test="order_str == null or order_str == ''">
			ORDER BY `tdate` ASC, reg_user_cnt DESC
		</if>
		<if test="order_str != null and order_str != ''">
			ORDER BY ${order_str}
		</if>
	</sql>

	<select id="effectiveManage" resultType="com.wbgame.pojo.jettison.vo.EffectivenessManageVo"  parameterType="com.wbgame.pojo.jettison.param.EffectivenessManageParam">
		select t1.tdate tdate,t1.spend spend ,t1.rebateSpend rebateSpend ,t3.new_material_num new_material_num,t2.material_num material_num ,
		t1.gdt_times gdt_times ,t1.kuaishou_times kuaishou_times ,t1.toutiao_times toutiao_times ,
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
			t1.artist artist,t1.artist3d artist3d,t1.creatives creatives,t1.product_part
			</when>
			<otherwise>
				t1.artist
			</otherwise>
		</choose>
		from
		(
		SELECT
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
			`artist` artist ,artist3d,creativist creatives,product_part,
			</when>
			<otherwise>
				`artist` artist ,
			</otherwise>
		</choose>
		 round(IFNULL(sum(rebateSpend),0.00),2) AS `rebateSpend`,count(signature) as new_material_num,sum(gdt_times) gdt_times,round(IFNULL(sum(spend),0.00),2) AS `spend`,
		 sum(kuaishou_times) kuaishou_times,sum(toutiao_times) toutiao_times,
		<choose>
			<when test='group.contains("date")'>
				DATE_FORMAT(`day`,'%Y-%m-%d') tdate
			</when>
			<when test='group.contains("week")'>
				DATE_FORMAT(`day`,'%Y-%u') tdate
			</when>
			<when test='group.contains("month")'>
				DATE_FORMAT(`day`,'%Y-%m') tdate
			</when>
			<when test='group.contains("year")'>
				DATE_FORMAT(`day`,'%Y') tdate
			</when>
			<otherwise>
				`day` tdate
			</otherwise>
		</choose>
		FROM dnwx_adt.dn_report_material where 1=1
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
				<if test="artists != null and artists.size > 0">
					AND
					<foreach collection="artists" index="index" item="item" open="(" separator="or" close=")">
						artist LIKE "%"#{item}"%" OR artist3d LIKE "%"#{item}"%" OR creativist LIKE "%"#{item}"%" OR product_part LIKE "%"#{item}"%"
					</foreach>
				</if>
			</when>
			<otherwise>
				<if test="artists != null and artists.size > 0">
					AND (
					<foreach collection="artists" index="index" item="item"  separator="or">
						artist LIKE "%"#{item}"%"
					</foreach>
					)
				</if>
			</otherwise>
		</choose>
		<if test="material_begin_online_time != null and material_end_online_time!= null">
			AND createDay <![CDATA[ >= ]]> #{material_begin_online_time}
			AND createDay <![CDATA[ <= ]]> #{material_end_online_time}
		</if>
		<if test="begin_date != null and end_date!= null">
			AND `day` BETWEEN #{begin_date} AND #{end_date}
		</if>
		<choose>
			<when test='group.contains("date")'>
				GROUP BY DATE_FORMAT(`day`,'%Y-%m-%d'),
			</when>
			<when test='group.contains("week")'>
				GROUP BY DATE_FORMAT(`day`,'%Y-%u'),
			</when>
			<when test='group.contains("month")'>
				GROUP BY DATE_FORMAT(`day`,'%Y-%m'),
			</when>
			<when test='group.contains("year")'>
				GROUP BY DATE_FORMAT(`day`,'%Y'),
			</when>
			<otherwise>
				GROUP BY
			</otherwise>
		</choose>
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
				`artist`,artist3d,creativist,product_part
			</when>
			<otherwise>
				`artist`
			</otherwise>
		</choose>
		) t1 left join (
		SELECT
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
			`artist` artist ,artist3d,creativist creatives,product_part,
			</when>
			<otherwise>
				`artist` artist ,
			</otherwise>
		</choose>
		 count(signature) as material_num,
		<choose>
			<when test='group.contains("date")'>
				DATE_FORMAT(`day`,'%Y-%m-%d') tdate
			</when>
			<when test='group.contains("week")'>
				DATE_FORMAT(`day`,'%Y-%u') tdate
			</when>
			<when test='group.contains("month")'>
				DATE_FORMAT(`day`,'%Y-%m') tdate
			</when>
			<when test='group.contains("year")'>
				DATE_FORMAT(`day`,'%Y') tdate
			</when>
			<otherwise>
				`day` tdate
			</otherwise>
		</choose>
		FROM dnwx_adt.dn_report_material where impressions>0
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
				<if test="artists != null and artists.size > 0">
					AND
					<foreach collection="artists" index="index" item="item" open="(" separator="or" close=")">
						artist LIKE "%"#{item}"%" OR artist3d LIKE "%"#{item}"%" OR creativist LIKE "%"#{item}"%" OR product_part LIKE "%"#{item}"%"
					</foreach>
				</if>
			</when>
			<otherwise>
				<if test="artists != null and artists.size > 0">
					AND (
					<foreach collection="artists" index="index" item="item"  separator="or">
						artist LIKE "%"#{item}"%"
					</foreach>
					)
				</if>
			</otherwise>
		</choose>
		<choose>
			<when test="material_begin_online_time != null and material_end_online_time!= null">
				AND createDay <![CDATA[ >= ]]> #{material_begin_online_time}
				AND createDay <![CDATA[ <= ]]> #{material_end_online_time}
			</when>
			<otherwise>
				AND createDay <![CDATA[ >= ]]> #{begin_date}
				AND createDay <![CDATA[ <= ]]> #{end_date}
			</otherwise>
		</choose>
		<if test="begin_date != null and end_date!= null">
			AND `day` BETWEEN #{begin_date} AND #{end_date}
		</if>
		<choose>
			<when test='group.contains("date")'>
				GROUP BY DATE_FORMAT(`day`,'%Y-%m-%d'),
			</when>
			<when test='group.contains("week")'>
				GROUP BY DATE_FORMAT(`day`,'%Y-%u'),
			</when>
			<when test='group.contains("month")'>
				GROUP BY DATE_FORMAT(`day`,'%Y-%m'),
			</when>
			<when test='group.contains("year")'>
				GROUP BY DATE_FORMAT(`day`,'%Y'),
			</when>
			<otherwise>
				GROUP BY
			</otherwise>
		</choose>
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
				`artist`,artist3d,creativist,product_part
			</when>
			<otherwise>
				`artist`
			</otherwise>
		</choose>
		) t2 on
		<choose>
			<when test='group.contains("date")'>
				t1.tdate=t2.tdate and
			</when>
			<when test='group.contains("week")'>
				t1.tdate=t2.tdate and
			</when>
			<when test='group.contains("month")'>
				t1.tdate=t2.tdate and
			</when>
			<when test='group.contains("year")'>
				t1.tdate=t2.tdate and
			</when>
			<otherwise>

			</otherwise>
		</choose>
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
				t1.artist=t2.artist and t1.artist3d=t2.artist3d and t1.creatives=t2.creatives and t1.product_part = t2.product_part
			</when>
			<otherwise>
				t1.artist=t2.artist
			</otherwise>
		</choose>
		left join
		(
		SELECT
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
			`artist` artist ,artist3d,creativist creatives,product_part,
			</when>
			<otherwise>
				`artist` artist ,
			</otherwise>
		</choose>
		count(signature) as new_material_num,
		<choose>
			<when test='group.contains("date")'>
				DATE_FORMAT(`day`,'%Y-%m-%d') tdate
			</when>
			<when test='group.contains("week")'>
				DATE_FORMAT(`day`,'%Y-%u') tdate
			</when>
			<when test='group.contains("month")'>
				DATE_FORMAT(`day`,'%Y-%m') tdate
			</when>
			<when test='group.contains("year")'>
				DATE_FORMAT(`day`,'%Y') tdate
			</when>
			<otherwise>
				`day` tdate
			</otherwise>
		</choose>
		FROM dnwx_adt.dn_report_material where 1=1
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
				<if test="artists != null and artists.size > 0">
					AND
					<foreach collection="artists" index="index" item="item" open="(" separator="or" close=")">
						artist LIKE "%"#{item}"%" OR artist3d LIKE "%"#{item}"%" OR creativist LIKE "%"#{item}"%" OR product_part LIKE "%"#{item}"%"
					</foreach>
				</if>
			</when>
			<otherwise>
				<if test="artists != null and artists.size > 0">
					AND (
					<foreach collection="artists" index="index" item="item"  separator="or">
						artist LIKE "%"#{item}"%"
					</foreach>
					)
				</if>
			</otherwise>
		</choose>
		<choose>
			<when test="material_begin_online_time != null and material_end_online_time!= null">
				AND createDay <![CDATA[ >= ]]> #{material_begin_online_time}
				AND createDay <![CDATA[ <= ]]> #{material_end_online_time}
			</when>
			<otherwise>
				AND createDay <![CDATA[ >= ]]> #{begin_date}
				AND createDay <![CDATA[ <= ]]> #{end_date}
			</otherwise>
		</choose>
		<if test="begin_date != null and end_date!= null">
			AND `day` BETWEEN #{begin_date} AND #{end_date}
		</if>
		<choose>
			<when test='group.contains("date")'>
				GROUP BY DATE_FORMAT(`day`,'%Y-%m-%d'),
			</when>
			<when test='group.contains("week")'>
				GROUP BY DATE_FORMAT(`day`,'%Y-%u'),
			</when>
			<when test='group.contains("month")'>
				GROUP BY DATE_FORMAT(`day`,'%Y-%m'),
			</when>
			<when test='group.contains("year")'>
				GROUP BY DATE_FORMAT(`day`,'%Y'),
			</when>
			<otherwise>
				GROUP BY
			</otherwise>
		</choose>
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
				`artist`,artist3d,creativist,product_part
			</when>
			<otherwise>
				`artist`
			</otherwise>
		</choose>
		) t3 on
		<choose>
			<when test='group.contains("date")'>
				t1.tdate=t3.tdate and
			</when>
			<when test='group.contains("week")'>
				t1.tdate=t3.tdate and
			</when>
			<when test='group.contains("month")'>
				t1.tdate=t3.tdate and
			</when>
			<when test='group.contains("year")'>
				t1.tdate=t3.tdate and
			</when>
			<otherwise>

			</otherwise>
		</choose>
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
				t1.artist=t3.artist and t1.artist3d=t3.artist3d and t1.creatives=t3.creatives and t1.product_part = t3.product_part
			</when>
			<otherwise>
				t1.artist=t3.artist
			</otherwise>
		</choose>
	</select>
	<select id="getCumulativeOnlineNum" resultType="com.wbgame.pojo.jettison.vo.EffectivenessManageVo" parameterType="com.wbgame.pojo.jettison.param.EffectivenessManageParam">
    	SELECT
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
			artist ,artist3d,creatives ,
			</when>
			<otherwise>
				artist ,
			</otherwise>
		</choose>
		 sum(cumulative_online_num) as cumulative_online_num
		FROM dnwx_adt.artist_cumulative_num where 1=1
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
				<if test="artists != null and artists.size > 0">
					AND
					<foreach collection="artists" index="index" item="item" open="(" separator="or" close=")">
						artist LIKE "%"#{item}"%" OR artist3d LIKE "%"#{item}"%" OR creatives LIKE "%"#{item}"%"
					</foreach>
				</if>
			</when>
			<otherwise>
				<if test="artists != null and artists.size > 0">
					AND (
					<foreach collection="artists" index="index" item="item"  separator="or">
						artist LIKE "%"#{item}"%"
					</foreach>
					)
				</if>
			</otherwise>
		</choose>
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
				group by `artist`,artist3d,creatives
			</when>
			<otherwise>
				group by `artist`
			</otherwise>
		</choose>
	</select>

	<select id="selectSpendSum" resultType="java.lang.Double">
		select sum(rebateSpend) spend
		from dnwx_adt.dn_report_spend_china
		where
		    app = #{appid}
		and `day` ${dateCondition}
	</select>

	<select id="getCreativeReportByWeek" resultType="java.util.Map"  parameterType="com.wbgame.pojo.jettison.MaterialCondition">
		SELECT artist, signature, `type`, SUM( `installs`)  AS installs, SUM( `spend`) AS spend,
		SUM(impressions) AS impressions, SUM(clicks) AS clicks, SUM( `retention_day_1`) AS retention_day_1
		<if test="putWeekGroup == true">
			,DATE_FORMAT(`day`,'%Y%u') `putWeek`
		</if>
		FROM dnwx_adt.dn_report_creative_summary
		<include refid="materialCondition"/>
		GROUP BY artist, signature, `type`
		<if test="putWeekGroup == true">
			,`putWeek`
		</if>
	</select>

	<select id="getCreativeReportByWeek2" resultType="com.wbgame.pojo.jettison.MaterialByWeekVisualization"
			parameterType="com.wbgame.pojo.jettison.MaterialCondition">
		SELECT artist, signature, `type`, SUM( `installs`)  AS installs
		<if test="putWeekGroup == true">
			,DATE_FORMAT(`day`,'%Y%u') `putWeek`
		</if>
		<if test="weekGroup == true">
			,DATE_FORMAT(`createDay`,'%Y%u') `week`
		</if>
		<if test="appGroup == true">
			, `app`
		</if>
		FROM dnwx_adt.dn_report_creative_summary
		<include refid="materialCondition"/>
		GROUP BY artist, signature, `type`
		<if test="putWeekGroup == true">
			,`putWeek`
		</if>
		<if test="weekGroup == true">
			,`week`
		</if>
		<if test="appGroup == true">
			, `app`
		</if>
	</select>

	<select id="getCreativeReportByWeek3" resultType="java.util.Map" parameterType="com.wbgame.pojo.jettison.MaterialCondition">
		SELECT DATE_FORMAT(`day`,'%Y%u') putWeek, DATE_FORMAT(`createDay`,'%Y%u') `week`, SUM( `installs`)  AS installs
		FROM dnwx_adt.dn_report_creative_summary
		<include refid="materialCondition"/>
		GROUP BY putWeek, week
		ORDER BY putWeek, week DESC
	</select>
	<select id="selectSpendGroupByAccount" resultType="java.util.Map">
		SELECT account, sum(spend) spend, sum(cashSpend) cashSpend, sum(virtualSpend) virtualSpend, sum(installs) installs FROM `dnwx_adt`.`dn_report_spend_china`
		where `day` = #{date}
		  and media = #{media}
		group by account
	</select>

	<select id="getCreativeXiaomigameNames" resultType="java.lang.String">
		SELECT gameName FROM dnwx_adt.dn_xiaomi_creative_report
		WHERE `day` between #{start_date} and #{end_date} and spend > 0 and gameName is not null
		group by gameName
	</select>
	<select id="selectAppleSpendManual"
			resultType="com.wbgame.pojo.jettison.report.AppleManualSpendVo">
		select * from apple_manual_spend_data
		where tdate between #{start_date} and #{end_date}
		<if test="app != null and app.size > 0">
			AND appid IN (<foreach collection="app" separator="," item="it">
			#{it}
		</foreach>)
		</if>
		<if test="media != null and media.size > 0">
			AND media IN (<foreach collection="media" separator="," item="it">
			#{it}
		</foreach>)
		</if>
		order by tdate desc, appid desc

	</select>
	<select id="selectReyunIncome" resultType="com.wbgame.pojo.jettison.report.AppleManualSpendVo">
		select t1.tdate, t1.appid, t3.app_name appName, t1.num_payorder_all_user, t1.amt_income_all_user, t2.amendment_income, t2.putUser, t2.updateTime
		from camera_line_tracking_io_data t1
		left join camera_line_tracking_io_data_suppliment t2
		on t1.tdate = t2.tdate and t1.appid = t2.appid
		left join app_info t3 on t1.appid = t3.id
		where t1.tdate between #{start_date} and #{end_date}
		<if test="app != null and app.size > 0">
			AND t1.appid IN (<foreach collection="app" separator="," item="it">
			#{it}
		</foreach>)
		</if>
		order by t1.tdate desc, t1.appid desc
	</select>
	<select id="selectCamerasPayCount" resultType="com.wbgame.task.cameras.CameraCostCounter">
		select app appid, `day` tdate, sum(rebateSpend) rebatespend, sum(payCount) paycount from dnwx_adt.dn_report_spend_china a
		left join app_info b on a.app = b.id
		where `day` = #{date}
		  and b.app_category in (3,15,18,19,20,48)
		  and b.os_type = #{type}
		group by app, `day`
		having sum(rebateSpend) > 0 or  sum(payCount) > 0
	</select>
	<select id="selectSpecialCamerasPayCount" resultType="com.wbgame.task.cameras.CameraCostCounter">
		select app appid, `day` tdate, sum(rebateSpend) rebatespend, sum(payCount) paycount, c.num_payorder_all_user pay_users, c.amt_income_all_user pay_amount
		from dnwx_adt.dn_report_spend_china a
				 left join app_info b on a.app = b.id
				 left join camera_line_tracking_io_data c on a.`day` = c.tdate and a.app = c.appid
		where `day` = #{date}
		  and a.app in (38846,39027)
		group by app, `day`
		having sum(rebateSpend) > 0 or  sum(payCount) > 0
	</select>
	<select id="selectXiaomiCreativeHighType" resultType="java.lang.String">
		select DISTINCT b.type_format high_type from dnwx_adt.`xiaomi_creative_realtime_info` a left join dnwx_adt.xiaomi_campaign_info b on a.group_id = b.xcampaign_id
	</select>
	<select id="selectXiaomiCreativeMidType" resultType="java.lang.String">
		select DISTINCT c.display_type mid_type from dnwx_adt.`xiaomi_creative_realtime_info` a left join dnwx_adt.adt_channel_campaign_info c on a.campaign_id = c.campaign_id
	</select>
	<select id="selectXiaomiCreativeLowType" resultType="java.lang.String">
		SELECT distinct material_type low_type  from dnwx_adt.`xiaomi_creative_realtime_info`
	</select>
	<select id="selectUmengChannelTotal" resultType="com.wbgame.pojo.UmengChannelTotalVo">
         <include refid="umengChannelTotalSql"/>
	     <if test="group != null and group != ''">
			 group by ${group}
		 </if>
		 <if test="order_str!= null and order_str!= ''">
			 order by ${order_str}
		 </if>
	</select>
	<select id="selectUmengChannelTotalTotal" resultType="com.wbgame.pojo.UmengChannelTotalVo">
		<include refid="umengChannelTotalSql"/>
	</select>
	<select id="selectPlanIdBudget" resultType="com.wbgame.pojo.budgetWarning.CampaignBudget">
		select opaccount account, planId, planName, dayBudget budget from dnwx_adt.oppo_group_info
		<if test="list != null">
		where planId in
			<foreach collection="list" item="it" separator="," open="(" close=")">
				#{it}
			</foreach>
		</if>
		group by planId, planName, opaccount
	</select>

	<select id="selectPlanIdBudgetCondition" resultType="com.wbgame.pojo.budgetWarning.CampaignBudget">
		select opaccount account, planId, planName, dayBudget budget, status from dnwx_adt.oppo_group_info a
		       left join
				( SELECT groupId, sum(spend) spend from dnwx_adt.dn_report_spend_china
				 where `day` BETWEEN #{startDate}  and #{endDate}
				 and media = 'oppo'
				 GROUP BY groupId)
				 b on a.adGroupId = b.groupId
		       left join dnwx_adt.oppo_plan_open_status c on a.planId = c.adPlanId
		    where 1 = 1
		<if test="planId != null and planId.size()>0">
		 and planId in
			<foreach collection="planId" item="it" separator="," open="(" close=")">
				#{it}
			</foreach>
		</if>
		<if test="account != null and account.size()>0">
			and opaccount in
		    <foreach collection="account" item="it" separator="," open="(" close=")">
				#{it}
			</foreach>
		</if>
		<if test="planName != null and planName != ''">
			and planName like concat("%", #{planName}, "%")
		</if>
		group by planId, planName, opaccount
		HAVING sum(spend) > 0
	</select>

	<insert id="updatePlanStatus">
		replace into dnwx_adt.oppo_plan_open_status(adPlanId, status)
		values (#{planId}, #{status})
	</insert>

	<select id="selectCameraPayGap" resultType="java.util.Map">
		select appid, app_name, paycount, pay_users, gap from (
				select appid, sum(paycount) paycount, sum(pay_users) pay_users, ifnull((sum(pay_users) - sum(paycount))/sum(pay_users),0) gap from ads_android_camera_series_and_camera_pay_cost_hourly
				where tdate BETWEEN #{startDate} and #{endDate}
				GROUP BY appid
				union all
				select appid, sum(paycount) paycount, sum(pay_users) pay_users, ifnull((sum(pay_users) - sum(paycount))/sum(pay_users),0) gap from ads_leying_and_camera_pay_cost
				where tdate BETWEEN #{startDate} and #{endDate}
				GROUP BY appid
			)XX
				left join app_info a on xx.appid = a.id
		where appid in
		<foreach collection="list" item="it" separator="," open="(" close=")">
			#{it}
		</foreach>
	</select>

	<sql id="umengChannelTotalSql">
		SELECT a.a_day tdate, a.appid appid, a.install_channel channel, cha_media, app_category, d.app_name appname,
			   truncate(sum(keep1)/sum(add_num)*100,2) keep_num1,
			   truncate(sum(keep2)/sum(add_num)*100,2) keep_num2,
			   truncate(sum(keep3)/sum(add_num)*100,2) keep_num3,
			   truncate(sum(keep4)/sum(add_num)*100,2) keep_num4,
			   truncate(sum(keep5)/sum(add_num)*100,2) keep_num5,
			   truncate(sum(keep6)/sum(add_num)*100,2) keep_num6,
			   truncate(sum(keep7)/sum(add_num)*100,2) keep_num7,
			   truncate(sum(keep14)/sum(add_num)*100,2) keep_num14,
			   truncate(sum(keep30)/sum(add_num)*100,2) keep_num30,
			   sum(act_num) actnum,
			   sum(add_num) addnum,
			   sum(start_num) launch,
			   cast(avg(second_avg) as int) daily_duration,
			   cast(avg(single_avg) as int) daily_per_duration,
			   sum(revenue) adv_fee  FROM
			   (select a_day, appid, install_channel,  truncate(sum(keep1),2) keep1,truncate(sum(keep2),2) keep2,truncate(sum(keep3),2) keep3,truncate(sum(keep4),2) keep4,
					truncate(sum(keep5),2) keep5,truncate(sum(keep6),2) keep6,truncate(sum(keep7),2) keep7,truncate(sum(keep14),2) keep14,truncate(sum(keep30),2) keep30
					from ads_umeng_user_app_keep_daily where a_day between #{start_date} and #{end_date} GROUP BY a_day, appid, install_channel)
					a
				left join
				(select a_day, appid, install_channel, sum(act_num) act_num, sum(add_num) add_num, sum(start_num) start_num,
				    cast(avg(second_avg) as int) second_avg, cast(avg(single_avg) as int) single_avg from ads_umeng_custom_total_daily
				where a_day between #{start_date} and #{end_date} GROUP BY a_day, appid, install_channel)
				 b on a.a_day = b.a_day and a.appid = b.appid and a.install_channel = b.install_channel
				left join
				( select tdate, appid, cha_id, cha_media, ifnull(sum(revise_revenue),0) revenue from
				ads_dn_extend_revise_income_daily where tdate between #{start_date} and #{end_date}
				GROUP BY tdate, appid, cha_id) c on a.a_day = c.tdate and a.appid = c.appid and a.install_channel = c.cha_id
				left join app_info d on a.appid = d.id
		where a.a_day between #{start_date} and #{end_date}
		<if test="appid != null and appid != ''">
			AND a.appid in (${appid})
		</if>
		<if test="channel!= null and channel!= ''">
			AND a.install_channel in  (${channel})
		</if>
		<if test="app_category!=null and app_category!=''">
			AND app_category in (${app_category})
		</if>
		<if test="cha_media != null and cha_media!=''">
			AND cha_media in (${cha_media})
		</if>
	</sql>

	<sql id="materialCondition">
		WHERE artist IS NOT NULL AND `artist` != '' AND `createDay` IS NOT NULL
		<if test="putDays != null and putDays.size > 0">
			AND `day` BETWEEN #{putDays[0]} AND #{putDays[1]}
		</if>
		<if test="days != null and days.size > 0">
			AND `createDay` BETWEEN #{days[0]} AND #{days[1]}
		</if>
		<if test="artists != null and artists.size > 0">
			AND (
			artist IN
			<foreach collection="artists" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			OR
			<foreach collection="artists" index="index" item="item" separator="OR">
				<bind name="artistSearch" value="'%' + item + '%'"/>
				artist LIKE #{artistSearch}
			</foreach>
			)
		</if>
		<if test="types != null and types.size > 0">
			AND `type` IN
			<foreach collection="types" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="apps != null and apps.size > 0">
			AND `app` IN
			<foreach collection="apps" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</sql>


	<insert id="insertYinliConfig">
		INSERT INTO dnwx_adt.wx_yinli_appid_config
		(appid,enabled,create_user,gravity_appid)
		values (#{appid},#{enabled},#{create_user},#{gravity_appid})
		on duplicate key update
		 update_time = #{update_time},
		 update_user = #{update_user},
		 gravity_appid = values(gravity_appid)
	</insert>

	<delete id="deleteYinliConfig">
		DELETE FROM dnwx_adt.wx_yinli_appid_config
		WHERE appid IN
		<foreach collection="appidList" item="appid" open="(" separator="," close=")">
			#{appid,jdbcType=INTEGER}
		</foreach>
	</delete>
	<delete id="deleteAndroidCameraList">
		delete from ${tableName}
		where tdate = #{date}
	</delete>

	<insert id="batchInsertAndroidCameraList" parameterType="com.wbgame.task.cameras.CameraCostCounter">
		insert into ${tableName}
		    (tdate, appid, rebatespend, paycount, pay_users, income, sign_user, cancel_user, pay_user)
		values
		    <foreach collection="list" item="item" separator=",">
		    (#{item.tdate},#{item.appid},#{item.rebatespend},#{item.paycount},#{item.pay_users},
		     #{item.pay_amount},#{item.sign_user},#{item.cancel_user},#{item.pay_user})
		    </foreach>
	</insert>
	<insert id="insertEstimateRoiList">
		insert into ${tableName}
		(`key`, tag, `value`) values
		<foreach collection="list" item="item" separator=",">
			(#{item.key},#{tag},#{item.value})
		</foreach>
		on duplicate key update
		`value` = values(`value`)
	</insert>
	<insert id="insertPayUserDetail">
		insert into dnwx_adt.camera_order_user_detail
		(tdate, appid, androidid, payname, param3, price)
		values
		<foreach collection="list" item="item" separator=",">
			(#{date},#{item.appid},#{item.androidid},#{item.payname},#{item.param3},#{item.price})
		</foreach>
		on duplicate key update
		price = values(price),
		payname = values(payname),
		param3 = values(param3)
	</insert>
	<insert id="insertCancelUserDetail">
		insert into dnwx_adt.camera_order_cancel_user_detail
		(tdate, appid, androidid, payname, param3, price)
		values
		<foreach collection="list" item="item" separator=",">
			(#{date},#{item.appid},#{item.androidid},#{item.payname},#{item.param3},#{item.price})
		</foreach>
		on duplicate key update
		price = values(price),
		payname = values(payname),
		param3 = values(param3)
	</insert>
	<insert id="insertPayUserDetail2">
		insert into camera_order_user_detail
		(tdate, appid, androidid, payname, param3, price)
		values
		<foreach collection="list" item="item" separator=",">
			(#{date},#{item.appid},#{item.androidid},#{item.payname},#{item.param3},#{item.price})
		</foreach>
		on duplicate key update
		price = values(price),
		payname = values(payname),
		param3 = values(param3)
	</insert>
	<insert id="batchInsertCameraPayGap">
		replace into dnwx_adt.self_submit_gap_statistic
		    (tdate,appid,appName,todayGap,hisGap,bias,mediaPay,selfCountPay,historyPay,historyUser,firstPay,
		    firstPayUser,submitCount,submitCountUser,submitLossUser,active30DayPay,active30DayUser)
		    values
		    <foreach collection="list" item="item" separator=",">
				(#{item.tdate},#{item.appid},#{item.appName},#{item.todayGap},#{item.hisGap},#{item.bias},#{item.mediaPay},#{item.selfCountPay},#{item.historyPay},#{item.historyUser},#{item.firstPay},
				#{item.firstPayUser},#{item.submitCount},#{item.submitCountUser},#{item.submitLossUser},#{item.active30DayPay},#{item.active30DayUser})
		    </foreach>
	</insert>

	<select id="selectPayUserDetail" resultType="java.lang.String">
		select androidid from camera_order_user_detail
		where tdate = #{date} and appid = #{appid}
	</select>

	<select id="selectClickActuser" resultType="java.util.Map">
		SELECT tdate,appid,download_channel,ifnull(global_click_user,0) global_click_user,
			   ifnull(sum(splash_click_users)) splash_click_users,
			   ifnull(sum(video_click_users)) video_click_users,
			   ifnull(sum(plaque_click_users)) plaque_click_users,
			   ifnull(sum(banner_click_users)) banner_click_users,
			   ifnull(sum(msg_click_users)) msg_click_users,
			   ifnull(sum(icon_click_users)) icon_click_users
		FROM `ads_user_ad_click_penetration` WHERE tdate = #{tdate}
		GROUP BY tdate,appid,download_channel
	</select>

	<select id="selectAdshowActuser" resultType="java.util.Map">
		SELECT tdate,appid,download_channel,ad_type,ifnull(sum(ad_active_user),0) ad_active_user,ifnull(global_ad_active_user,0) global_ad_active_user FROM ads_active_user_revenue_ecpm_pv_analyze_daily WHERE tdate = #{tdate} AND active_type = '活跃'
		GROUP BY tdate,appid,download_channel,ad_type
	</select>


</mapper>