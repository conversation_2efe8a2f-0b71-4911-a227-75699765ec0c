<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsRevenueSummaryDailyMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.adv2.AdsRevenueSummaryDailyVO">
        <result column="tdate" property="tdate" jdbcType="VARCHAR"/>
        <result column="agent" property="agent" jdbcType="VARCHAR"/>
        <result column="app_category" property="appCategory" jdbcType="VARCHAR"/>
        <result column="app_category_name" property="appCategoryName" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
        <result column="ad_type" property="adType" jdbcType="VARCHAR"/>
        <result column="user_type" property="isNewInstall" jdbcType="VARCHAR"/>
        <result column="active_type_user" property="activeTypeUser" jdbcType="BIGINT"/>
        <result column="adjusted_request_cnt" property="adjustedRequestCnt" jdbcType="DECIMAL"/>
        <result column="adjusted_request_success_cnt" property="adjustedRequestSuccessCnt" jdbcType="DECIMAL"/>
        <result column="adjusted_ad_cnt" property="adjustedAdCnt" jdbcType="DECIMAL"/>
        <result column="adjusted_click_cnt" property="adjustedClickCnt" jdbcType="DECIMAL"/>
        <result column="adjusted_ad_revenue" property="adjustedAdRevenue" jdbcType="DECIMAL"/>
        <result column="adjusted_total_ad_revenue" property="adjustedTotalAdRevenue" jdbcType="DECIMAL"/>
        <result column="pv_per_capita" property="pvPerCapita" jdbcType="VARCHAR"/>
        <result column="ecpm" property="ecpm" jdbcType="VARCHAR"/>
        <result column="arpu" property="arpu" jdbcType="VARCHAR"/>
        <result column="income_ratio" property="incomeRatio" jdbcType="VARCHAR"/>
        <result column="fill_rate" property="fillRate" jdbcType="VARCHAR"/>
        <result column="impression_rate" property="impressionRate" jdbcType="VARCHAR"/>
        <result column="ctr" property="ctr" jdbcType="VARCHAR"/>
        <result column="fill_permeability" property="fillPermeability" jdbcType="VARCHAR"/>
        <result column="display_penetration" property="displayPenetration" jdbcType="VARCHAR"/>
        <result column="combination" property="combination" jdbcType="VARCHAR"/>
        <result column="is_new_install" property="isNewInstall" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectRevenueSummary" parameterType="com.wbgame.pojo.adv2.AdsRevenueSummaryDailyDTO" resultMap="BaseResultMap">


        select a.*, cast(ifnull(a.adjusted_ad_revenue / tmp.total, 0) * 100 as decimal(18, 2)) as income_ratio

        from (
            select

            <if test="group != null and group != ''">
                ${group},
            </if>
            sum(active_type_user) active_type_user,
            sum(cast(adjusted_request_cnt as decimal(18, 0) )) adjusted_request_cnt,
            sum(cast(adjusted_request_success_cnt as decimal(18, 0) )) adjusted_request_success_cnt,
            sum(cast(adjusted_ad_cnt as decimal(18, 0) )) adjusted_ad_cnt,
            sum(cast(adjusted_click_cnt as decimal(18, 0) )) adjusted_click_cnt,
            sum(cast(adjusted_ad_revenue as decimal(18, 2))) adjusted_ad_revenue,

            cast(ifnull(sum(adjusted_ad_cnt) / sum(active_type_user), 0) as decimal(18, 2) ) pv_per_capita,
            cast(ifnull(sum(adjusted_ad_revenue) / sum(adjusted_ad_cnt), 0) *1000 as decimal(18, 2)) ecpm,
            cast(ifnull(sum(adjusted_ad_revenue) / sum(active_type_user), 0) as decimal(18, 2)) arpu,
            cast(ifnull(sum(adjusted_request_success_cnt) / sum(adjusted_request_cnt), 0) * 100 as decimal(18, 2)) fill_rate,
            cast(ifnull(sum(adjusted_ad_cnt) / sum(adjusted_request_success_cnt), 0) * 100 as decimal(18, 2)) impression_rate,
            cast(ifnull(sum(adjusted_click_cnt) / sum(adjusted_ad_cnt), 0) * 100 as decimal(18, 2)) ctr,
            cast(ifnull(sum(adjusted_request_success_user_cnt) / sum(active_type_user), 0) * 100 as decimal(18, 2)) fill_permeability,
            cast(ifnull(sum(adjusted_ad_user_cnt) / sum(active_type_user), 0) * 100 as decimal(18, 2)) display_penetration
            from ads_revenue_summary_daily

            <where>
                <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                    tdate BETWEEN #{start_date}
                    AND #{end_date}
                </if>
                <if test="agent != null and agent != ''">
                    AND agent = #{agent}
                </if>

                <if test="adType != null and adType != ''">
                    AND ad_type = #{adType}
                </if>

                <if test="isNewInstall != null">
                    AND is_new_install = #{isNewInstall}
                </if>

                <if test="channelList != null and channelList.size > 0">
                    AND download_channel IN
                    <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                        #{channel}
                    </foreach>
                </if>

                <if test="appidList != null and appidList.size > 0">
                    AND appid IN
                    <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                        #{appid}
                    </foreach>
                </if>
            </where>

            <if test="group != null and group != ''">
                group by ${group}
            </if>

        ) a

        left join (
            select

                 <if test="secondaryGroup != null and secondaryGroup != ''">
                ${secondaryGroup},
            </if>
            sum(cast(adjusted_ad_revenue as decimal(18,2)) ) total
            from ads_revenue_summary_daily

            <where>
                <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                    tdate BETWEEN #{start_date}
                    AND #{end_date}
                </if>
                <if test="adType != null and adType != ''">
                    AND ad_type = #{adType}
                </if>

                <if test="isNewInstall != null">
                    AND is_new_install = #{isNewInstall}
                </if>

                <if test="channelList != null and channelList.size > 0">
                    AND download_channel IN
                    <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                        #{channel}
                    </foreach>
                </if>

                <if test="appidList != null and appidList.size > 0">
                    AND appid IN
                    <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                        #{appid}
                    </foreach>
                </if>
            </where>
            <if test="secondaryGroup != null and secondaryGroup != ''">
                group by ${secondaryGroup}
            </if>

        ) tmp
        on ${onGroup}

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by a.tdate
            </otherwise>
        </choose>


    </select>

    <select id="selectCoordinateMapRevenueSummary" parameterType="com.wbgame.pojo.adv2.AdsRevenueSummaryDailyDTO" resultMap="BaseResultMap">

        select
            a.tdate,
            cast(ifnull(sum(a.adjusted_ad_revenue)/ tmp.total, 0) * 100 as decimal(18, 2)) as income_ratio,
            sum( active_type_user ) active_type_user,
            sum( adjusted_request_cnt ) adjusted_request_cnt,
            sum( adjusted_request_success_cnt ) adjusted_request_success_cnt,
            sum( adjusted_ad_cnt ) adjusted_ad_cnt,
            sum( adjusted_click_cnt ) adjusted_click_cnt,
            sum( adjusted_ad_revenue ) adjusted_ad_revenue,
            sum( pv_per_capita ) pv_per_capita,
            cast(ifnull(sum(adjusted_ad_revenue) / sum(adjusted_ad_cnt), 0) * 1000  as decimal(18, 2)) ecpm,
            cast(ifnull(sum(adjusted_request_success_cnt) / sum(adjusted_request_cnt), 0) * 100 as decimal(18, 2)) fill_rate,
            cast(ifnull(sum(adjusted_ad_cnt) / sum(adjusted_request_success_cnt), 0) * 100 as decimal(18, 2)) impression_rate,
            cast(ifnull(sum(adjusted_click_cnt) / sum(adjusted_ad_cnt), 0) * 100 as decimal(18, 2)) ctr,
            cast(ifnull(sum(adjusted_request_success_user_cnt) / sum(active_type_user), 0) * 100 as decimal(18, 2)) fill_permeability,
            cast(ifnull(sum(adjusted_ad_user_cnt) / sum(active_type_user), 0) * 100 as decimal(18, 2)) display_penetration

        from (

            select
            <if test="group != null and group != ''">
                ${group},

            </if>

            sum(active_type_user) active_type_user,
            sum(cast(adjusted_request_cnt as decimal(18, 0) )) adjusted_request_cnt,
            sum(cast(adjusted_request_success_cnt as decimal(18, 0) )) adjusted_request_success_cnt,
            sum(cast(adjusted_ad_cnt as decimal(18, 0) )) adjusted_ad_cnt,
            sum(cast(adjusted_click_cnt as decimal(18, 0) )) adjusted_click_cnt,
            sum(cast(adjusted_ad_revenue as decimal(18, 2))) adjusted_ad_revenue,
            sum(adjusted_request_success_user_cnt) adjusted_request_success_user_cnt,
            sum(adjusted_ad_user_cnt) adjusted_ad_user_cnt,
            cast(ifnull(sum(adjusted_ad_cnt) / sum(active_type_user), 0) as decimal(18, 2) ) pv_per_capita,
            cast(ifnull(sum(adjusted_ad_revenue) / sum(active_type_user), 0) as decimal(18, 2)) arpu

        from ads_revenue_summary_daily

            <where>
                <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                    tdate BETWEEN #{start_date}
                    AND #{end_date}
                </if>

                <if test="agent != null and agent != ''">
                    AND agent = #{agent}
                </if>

                <if test="adType != null and adType != ''">
                    AND ad_type = #{adType}
                </if>

                <if test="isNewInstall != null">
                    AND is_new_install = #{isNewInstall}
                </if>

                <if test="channelList != null and channelList.size > 0">
                    AND download_channel IN
                    <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                        #{channel}
                    </foreach>
                </if>

                <if test="appidList != null and appidList.size > 0">
                    AND appid IN
                    <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                        #{appid}
                    </foreach>
                </if>

            </where>


            <if test="group != null and group != ''">
                group by ${group}
            </if>
            ) a
            LEFT JOIN (

            SELECT
            tdate,

            sum( adjusted_ad_revenue ) total
            FROM
            ads_revenue_summary_daily
            <where>
                <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                    tdate BETWEEN #{start_date}
                    AND #{end_date}
                </if>

                <if test="agent != null and agent != ''">
                    AND agent = #{agent}
                </if>

                <if test="adType != null and adType != ''">
                    AND ad_type = #{adType}
                </if>

                <if test="isNewInstall != null">
                    AND is_new_install = #{isNewInstall}
                </if>

                <if test="channelList != null and channelList.size > 0">
                    AND download_channel IN
                    <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                        #{channel}
                    </foreach>
                </if>

                <if test="appidList != null and appidList.size > 0">
                    AND appid IN
                    <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                        #{appid}
                    </foreach>
                </if>

            </where>
            GROUP BY tdate
                ) tmp on a.tdate =tmp.tdate
        group by a.tdate
        order by a.tdate

    </select>


    <select id="countRevenueSummary" parameterType="com.wbgame.pojo.adv2.AdsRevenueSummaryDailyDTO" resultMap="BaseResultMap">


        select sum(active_type_user) active_type_user,sum(adjusted_request_cnt) adjusted_request_cnt,
               sum(adjusted_request_success_cnt) adjusted_request_success_cnt,sum(adjusted_ad_cnt) adjusted_ad_cnt,sum(adjusted_click_cnt) adjusted_click_cnt,
               sum(adjusted_ad_revenue) adjusted_ad_revenue,sum(pv_per_capita) pv_per_capita,sum(ecpm) ecpm,sum(arpu) arpu
        from (select
        <if test="group != null and group != ''">
            ${group},
        </if>
        sum(active_type_user) active_type_user,
        sum(cast(adjusted_request_cnt as decimal(18, 0) )) adjusted_request_cnt,
        sum(cast(adjusted_request_success_cnt as decimal(18, 0) )) adjusted_request_success_cnt,
        sum(cast(adjusted_ad_cnt as decimal(18, 0) )) adjusted_ad_cnt,
        sum(cast(adjusted_click_cnt as decimal(18, 0) )) adjusted_click_cnt,
        sum(cast(adjusted_ad_revenue as decimal(18, 2))) adjusted_ad_revenue,
        cast(ifnull(sum(adjusted_ad_cnt) / sum(active_type_user), 0) as decimal(18, 2) ) pv_per_capita,
        cast(ifnull(sum(adjusted_ad_revenue) / sum(adjusted_ad_cnt), 0) * 1000  as decimal(18, 2)) ecpm,
        cast(ifnull(sum(adjusted_ad_revenue) / sum(active_type_user), 0) as decimal(18, 2)) arpu,
        cast(ifnull(sum(adjusted_request_success_cnt) / sum(adjusted_request_cnt), 0) * 100 as decimal(18, 2)) fill_rate,
        cast(ifnull(sum(adjusted_ad_cnt) / sum(adjusted_request_success_cnt), 0) * 100 as decimal(18, 2)) impression_rate,
        cast(ifnull(sum(adjusted_click_cnt) / sum(adjusted_ad_cnt), 0) * 100 as decimal(18, 2)) ctr,
        cast(ifnull(sum(adjusted_request_success_user_cnt) / sum(active_type_user), 0) * 100 as decimal(18, 2)) fill_permeability,
        cast(ifnull(sum(adjusted_ad_user_cnt) / sum(active_type_user), 0) * 100 as decimal(18, 2)) display_penetration


        from ads_revenue_summary_daily

        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                tdate BETWEEN #{start_date}
                AND #{end_date}
            </if>

            <if test="agent != null and agent != ''">
                AND agent = #{agent}
            </if>

            <if test="adType != null and adType != ''">
                AND ad_type = #{adType}
            </if>

            <choose>
                <when test="isNewInstall != null">
                    AND is_new_install = #{isNewInstall}
                </when>
                <otherwise>

                </otherwise>
            </choose>

            <if test="channelList != null and channelList.size > 0">
                AND download_channel IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

        </where>


        <if test="group != null and group != ''">
            group by ${group}
        </if>

        ) a
    </select>


    <select id="selectRevenueSummaryAll" parameterType="com.wbgame.pojo.adv2.AdsRevenueSummaryDailyDTO" resultMap="BaseResultMap">

        select

        <if test="group != null and group != ''">
            ${group},
        </if>
        sum(adjusted_ad_revenue) adjusted_ad_revenue

        from ads_revenue_summary_daily

        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                tdate BETWEEN #{start_date}
                AND #{end_date}
            </if>

            <if test="agent != null and agent != ''">
                AND agent = #{agent}
            </if>


            <choose>
                <when test="isNewInstall != null">
                    AND is_new_install = #{isNewInstall}
                </when>
                <otherwise>

                </otherwise>
            </choose>

            <if test="channelList != null and channelList.size > 0">
                AND download_channel IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

        </where>


        <if test="group != null and group != ''">
            group by ${group}
        </if>

    </select>

</mapper>