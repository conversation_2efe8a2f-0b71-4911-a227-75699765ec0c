<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.Adv2ShiftMapper">

	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>
	
	
	<!-- 游戏分渠道收支数据汇总  -->
  	<select id="selectGameChaRevenueTotal" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_gamecha_revenue_sql_New"/>
	</select>
	<select id="selectGameChaRevenueTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.actnum) actnum,
			SUM(xx.addnum) addnum,
			TRUNCATE(SUM(xx.rebate_consume),2) rebate_consume,
			TRUNCATE(SUM(xx.ad_revenue),2) ad_revenue,
			TRUNCATE(SUM(xx.bill_revenue),2) bill_revenue,
			TRUNCATE(SUM(xx.rebate_consume)/SUM(xx.addnum), 2) cpa,
			TRUNCATE(SUM(xx.ad_revenue)/SUM(xx.actnum), 2) dau_arpu,
			TRUNCATE(SUM(xx.bill_revenue)/SUM(xx.actnum), 2) bill_arpu,
			TRUNCATE((SUM(xx.ad_revenue)+SUM(xx.bill_revenue))/SUM(xx.actnum), 2) total_arpu,
			TRUNCATE((SUM(xx.ad_revenue)+SUM(xx.bill_revenue))/SUM(xx.rebate_consume)*100, 2) day_roi
			
		from (<include refid="dn_gamecha_revenue_sql_New"/>) xx
	</select>
	<sql id="dn_gamecha_revenue_sql_New">
		select aa.*,
			dd.all_expenses,
			dd.all_sum_revenue,
			dd.all_sum_profit_rate
		from
			(select 
				<if test="tdate_group != null and tdate_group != ''">tdate,</if>
				<if test="appid_group != null and appid_group != ''">appid,</if>
				<if test="cha_id_group != null and cha_id_group != ''">cha_id,</if>
				<if test="cha_type_name_group != null and cha_type_name_group != ''">cha_type_name,</if>
				<if test="cha_media_group != null and cha_media_group != ''">cha_media,</if>
				<if test="cha_sub_launch_group != null and cha_sub_launch_group != ''">cha_sub_launch,</if>
				SUM(actnum) actnum,
				SUM(addnum) addnum,
				TRUNCATE(SUM(rebate_consume),2) rebate_consume,
				TRUNCATE(SUM(ad_revenue),2) ad_revenue,
				TRUNCATE(SUM(bill_revenue),2) bill_revenue,
				TRUNCATE(SUM(rebate_consume)/SUM(addnum), 2) cpa,
				TRUNCATE(SUM(ad_revenue)/SUM(actnum), 2) dau_arpu,
				TRUNCATE(SUM(bill_revenue)/SUM(actnum), 2) bill_arpu,
				TRUNCATE((SUM(ad_revenue)+SUM(bill_revenue))/SUM(actnum), 2) total_arpu,
				TRUNCATE((SUM(ad_revenue)+SUM(bill_revenue))/SUM(rebate_consume)*100, 2) day_roi

			from dnwx_wjy.dn_game_cha_revenue_total 
			where tdate BETWEEN #{sdate} AND #{edate} 
			<include refid="dn_extend_where_sql"/> 
			 group by
			<trim suffixOverrides=",">
				<if test="tdate_group != null and tdate_group != ''">tdate,</if>
				<if test="appid_group != null and appid_group != ''">appid,</if>
				<if test="cha_id_group != null and cha_id_group != ''">cha_id,</if>
				<if test="cha_type_name_group != null and cha_type_name_group != ''">cha_type_name,</if>
				<if test="cha_media_group != null and cha_media_group != ''">cha_media,</if>
				<if test="cha_sub_launch_group != null and cha_sub_launch_group != ''">cha_sub_launch,</if>
			</trim>) aa
		
		LEFT JOIN 
			(select 
				<if test="appid_group != null and appid_group != ''">appid,</if>
				<if test="cha_id_group != null and cha_id_group != ''">cha_id,</if>
				<if test="cha_type_name_group != null and cha_type_name_group != ''">cha_type_name,</if>
				<if test="cha_media_group != null and cha_media_group != ''">cha_media,</if>
				<if test="cha_sub_launch_group != null and cha_sub_launch_group != ''">cha_sub_launch,</if>
				TRUNCATE(sum(rebate_consume),0) all_expenses,
				TRUNCATE(SUM(ad_revenue) + SUM(bill_revenue),0) all_sum_revenue,
				IFNULL(TRUNCATE((SUM(ad_revenue) + SUM(bill_revenue) - SUM(rebate_consume))/SUM(rebate_consume)*100, 1),0) all_sum_profit_rate
			
			from dnwx_wjy.dn_game_cha_revenue_total 
			<where>
				<if test="appid != null and appid != ''">
					and appid in (${appid}) 
				</if>
				<if test="cha_id != null and cha_id != ''">
					and cha_id in (${cha_id}) 
				</if>
				<if test="cha_type_name != null and cha_type_name != ''">
					and cha_type_name in (${cha_type_name}) 
				</if>
				<if test="cha_media != null and cha_media != ''">
					and cha_media in (${cha_media}) 
				</if>
				<if test="cha_sub_launch != null and cha_sub_launch != ''">
					and cha_sub_launch in (${cha_sub_launch}) 
				</if>
				<if test="apps != null and apps != ''">
					and appid in (${apps}) 
				</if>
			</where>
			<trim suffixOverrides="," prefix="group by ">
				<if test="appid_group != null and appid_group != ''">appid,</if>
				<if test="cha_id_group != null and cha_id_group != ''">cha_id,</if>
				<if test="cha_type_name_group != null and cha_type_name_group != ''">cha_type_name,</if>
				<if test="cha_media_group != null and cha_media_group != ''">cha_media,</if>
				<if test="cha_sub_launch_group != null and cha_sub_launch_group != ''">cha_sub_launch,</if>
			</trim>) dd 
			
		ON 1=1 
		<if test="appid_group != null and appid_group != ''"> and aa.appid=dd.appid</if>
		<if test="cha_id_group != null and cha_id_group != ''"> and aa.cha_id=dd.cha_id</if>
		<if test="cha_type_name_group != null and cha_type_name_group != ''">and aa.cha_type_name=dd.cha_type_name</if>
		<if test="cha_media_group != null and cha_media_group != ''">and aa.cha_media=dd.cha_media</if>
		<if test="cha_sub_launch_group != null and cha_sub_launch_group != ''">and aa.cha_sub_launch=dd.cha_sub_launch</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by
				<if test="tdate_group != null and tdate_group != ''">aa.tdate asc ,</if> addnum desc
			</otherwise>
		</choose>
	</sql>
	
	
	<sql id="dn_extend_where_sql">
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid in (${prjid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name}) 
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media}) 
		</if>
		<if test="cha_sub_launch != null and cha_sub_launch != ''">
			and cha_sub_launch in (${cha_sub_launch}) 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>
	</sql>
	
</mapper>