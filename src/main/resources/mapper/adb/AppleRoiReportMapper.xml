<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adb.AppleRoiReportMapper">

    <select id="selectAppleRoiReport" resultType="com.wbgame.pojo.jettison.AppleRoiReportVo">
        <include refid="apple_roi_report_select_sql"/>
        <if test="group != null and group != ''">
            GROUP BY ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>

    <select id="selectAppleGameRoiReport" resultType="com.wbgame.pojo.jettison.AppleRoiReportVo">
        <include refid="apple_roi_game_report_select_sql"/>
        <if test="group != null and group != ''">
            GROUP BY ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>

    <select id="countAppleRoiReport" resultType="com.wbgame.pojo.jettison.AppleRoiReportVo">
        <include refid="apple_roi_report_select_sql"/>
    </select>
    <select id="countAppleGameRoiReport" resultType="com.wbgame.pojo.jettison.AppleRoiReportVo">
        <include refid="apple_roi_game_report_select_sql"/>
    </select>

    <sql id="apple_roi_report_select_common_field_sql">
        tdate,appid,app_name,app_category,putUser,account,campaign_id,campaign_name,adgroupid,adgroupname,keywordid,keyword
        ,convert(sum(spend),decimal(10,2)) spend,sum(impressions) impressions,sum(click) click,sum(installs) installs
        ,sum(newdownloads) newdownloads,sum(redownloads) redownloads
        ,ifnull(convert(sum(spend)/sum(click),DECIMAL(10,2)),0) spend_click_avg
        ,ifnull(convert(sum(spend)/sum(installs),DECIMAL(10,2)),0) spend_installs_avg
        ,ifnull(convert(sum(click)/sum(impressions)*100,DECIMAL(10,2)),0) click_rate
        ,ifnull(convert(sum(installs)/sum(click)*100,DECIMAL(10,2)),0) installs_rate
        ,ifnull(convert(sum(distinctinstalls)/sum(newdownloads+redownloads)*100,DECIMAL(10,2)),0) active_rate
        ,ifnull(convert(sum(spend)/sum(distinctinstalls),DECIMAL(10,2)),0) active_cost
        ,sum(distinctinstalls) distinctinstalls
    </sql>

    <sql id="apple_roi_game_report_select_sql">
        select <include refid="apple_roi_report_select_common_field_sql"/>,
        sum(reg_users) reg_users,
        convert(sum(spend)/sum(add_pay_users),decimal(10,2)) add_pay_user_cost,
        convert(ifnull(sum(add_pay_users)/sum(reg_users),0)*100, decimal(10,2))  add_pay_user_rate,
        sum(add_pay_users) add_pay_users,
        sum(add_pay_revenue/100) add_pay_revenue
        <choose>
            <when test="game_revenue_index == 1">
                <foreach collection="game_days" item="i">
                    ,ifnull(convert((sum(add_revenue_${i}))/100/sum(spend)*100, decimal(10,2)),0) as roi_rate_${i}
                    ,ifnull(convert((sum(add_purchase_retention_${i})/sum(add_pay_users))*100, decimal(10,2)),0) pay_retain_rate_${i}
                    ,ifnull(convert((sum(add_revenue_${i}) )/(sum(add_revenue_1) ), decimal(10,2)),0) as increase_n_${i}
                </foreach>
            </when>
            <when test="game_revenue_index == 2">
                <foreach collection="game_days" item="i">
                    ,ifnull(convert((sum(add_purchase_revenue_${i}))/100/sum(spend)*100, decimal(10,2)),0) as roi_rate_${i}
                    ,ifnull(convert((sum(add_purchase_retention_${i})/sum(add_pay_users))*100, decimal(10,2)),0) pay_retain_rate_${i}
                    ,ifnull(convert((sum(add_purchase_revenue_${i}))/(sum(add_purchase_revenue_1)), decimal(10,2)),0) as increase_n_${i}
                </foreach>
            </when>
            <otherwise>
                <foreach collection="game_days" item="i">
                    ,ifnull(convert((sum(add_revenue_${i}) + sum(add_purchase_revenue_${i}))/100/sum(spend)*100, decimal(10,2)),0) as roi_rate_${i}
                    ,ifnull(convert((sum(add_purchase_retention_${i})/sum(add_pay_users))*100, decimal(10,2)),0) pay_retain_rate_${i}
                    ,ifnull(convert((sum(add_revenue_${i}) + sum(add_purchase_revenue_${i}))/(sum(add_revenue_1) + sum(add_purchase_revenue_1)), decimal(10,2)),0) as increase_n_${i}
                </foreach>
        </otherwise>
        </choose>

        from ads_apple_ads_game_ios a left join app_info b on a.appid = b.id
        where 1=1
        <include refid="apple_roi_report_where_sql"/>
    </sql>

    <sql id="apple_roi_report_select_sql">
        select <include refid="apple_roi_report_select_common_field_sql"/>
        ,sum(week_first_payment_users+month_first_payment_users+year_first_payment_users) first_pay_users
        ,ifnull(convert(sum(spend)/sum(week_first_payment_users+month_first_payment_users+year_first_payment_users),DECIMAL(10,2)),0) first_pay_cost
        ,convert(sum(week_first_payment_amount+month_first_payment_amount+year_first_payment_amount),decimal(10,2)) pay_income
        ,sum(week_first_payment_users) week_first_payment_users
        ,sum(month_first_payment_users) month_first_payment_users
        ,sum(year_first_payment_users) year_first_payment_users
        <foreach collection="renew_nums" item="i">
            ,sum(renew_users_${i}) renew_users_${i}
            ,convert(sum(renew_revenue_${i}),decimal(10,2)) renew_revenue_${i}
            ,convert(sum(agg_renew_revenue_${i}),decimal(10,2)) agg_renew_revenue_${i}
            ,ifnull(convert(sum(renew_users_${i})/sum(week_first_payment_users)*100,DECIMAL(10,2)),0) renew_users_${i}_rate
            ,ifnull(convert(sum(agg_renew_users_${i})/sum(week_first_payment_users),DECIMAL(10,2)),0) lt${i}
            ,ifnull(convert(sum(agg_renew_revenue_${i})/sum(spend)*100,DECIMAL(10,2)),0) agg_roi_${i}_rate
        </foreach>
        from ads_apple_ads_renew_detail_daily a left join app_info b on a.appid = b.id
        where 1=1
        <include refid="apple_roi_report_where_sql"/>
    </sql>


	<select id="selectAppleActualRoiReport" resultType="com.wbgame.pojo.jettison.AppleRoiReportVo">
        <include refid="actual_apple_ads_roi_report_sql"/>
        <if test="group != null and group != ''">
            GROUP BY ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>
	<select id="selectAppleActualGameRoiReport" resultType="com.wbgame.pojo.jettison.AppleRoiReportVo">
        <include refid="actual_apple_ads_game_roi_report_sql"/>
        <if test="group != null and group != ''">
            GROUP BY ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>

    <select id="countAppleActualRoiReport" resultType="com.wbgame.pojo.jettison.AppleRoiReportVo">
        <include refid="actual_apple_ads_roi_report_sql"/>
    </select>
    <select id="countAppleActualGameRoiReport" resultType="com.wbgame.pojo.jettison.AppleRoiReportVo">
        <include refid="actual_apple_ads_game_roi_report_sql"/>
    </select>
    <sql id="actual_apple_ads_roi_report_sql">
        select <include refid="actual_apple_ads_roi_report_common_select_field"/>
        from dnwx_adt.apple_ads_actual_roi_report a left join app_info b on a.appId = b.id
        where 1=1
        <include refid="actual_apple_ads_roi_where_sql"/>
    </sql>
    <sql id="actual_apple_ads_game_roi_report_sql">
        select <include refid="actual_apple_ads_roi_report_common_select_field"/>
            ,ifnull(convert(sum(pay_users)/sum(distinctinstalls)*100,decimal(10,2)), 0) add_pay_user_rate
            ,sum(ltv) revenue1
            ,sum(pay_income)+sum(ltv) totalIncome
            <choose>
                <when test="game_revenue_index == 1">
                    ,ifnull(convert(sum(ltv)/sum(spend)*100,decimal(10,2)),0) roi_rate_1
                </when>
                <when test="game_revenue_index == 2">
                    ,ifnull(convert(sum(pay_income)/sum(spend)*100,decimal(10,2)),0) roi_rate_1
                </when>
                <when test="game_revenue_index == 3">
                    ,ifnull(convert((sum(pay_income) + sum(ltv))/sum(spend)*100,decimal(10,2)),0) roi_rate_1
                </when>
            </choose>
        from dnwx_adt.apple_ads_actual_roi_report a left join app_info b on a.appId = b.id
        where 1=1
        <include refid="actual_apple_ads_roi_where_sql"/>
    </sql>

    <sql id="actual_apple_ads_roi_report_common_select_field">
        `day` tdate,appId appid,app_name,app_category,putUser,accountId account,campaignId campaign_id,campaignName campaign_name,groupName adgroupname,keyword keyword
        ,convert(sum(spend),decimal(10,2)) spend,sum(impressions) impressions,sum(click) click,sum(installs) installs
        ,sum(newdownloads) newdownloads,sum(redownloads) redownloads
        ,ifnull(convert(sum(spend)/sum(click),DECIMAL(10,2)),0) spend_click_avg
        ,ifnull(convert(sum(spend)/sum(installs),DECIMAL(10,2)),0) spend_installs_avg
        ,ifnull(convert(sum(click)/sum(impressions)*100,DECIMAL(10,2)),0) click_rate
        ,ifnull(convert(sum(installs)/sum(click)*100,DECIMAL(10,2)),0) installs_rate
        ,ifnull(convert(sum(distinctinstalls)/sum(newdownloads+redownloads)*100,DECIMAL(10,2)),0) active_rate
        ,ifnull(convert(sum(spend)/sum(distinctinstalls),DECIMAL(10,2)),0) active_cost

        ,convert(sum(spend)/sum(pay_users),decimal(10,2)) first_day_pay_cost
        ,convert(sum(pay_income),decimal(10,2)) pay_income
        ,sum(distinctinstalls) distinctinstalls,sum(pay_users) first_day_pay_users
        ,sum(week_sub_count) week_first_payment_users,sum(month_sub_count) month_first_payment_users,sum(year_sub_count) year_first_payment_users
    </sql>

    <sql id="apple_roi_report_where_sql">
        and tdate between #{start_date} and #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="app_category != null and app_category != ''">
            and app_category in (${app_category})
        </if>
        <if test="putUser != null and putUser != ''">
            and putUser in (${putUser})
        </if>
        <if test="account != null and account != ''">
            and account in (${account})
        </if>
        <if test="campaign_id != null and campaign_id != ''">
            and campaign_id in (${campaign_id})
        </if>
        <if test="adgroupid != null and adgroupid != ''">
            and adgroupid in (${adgroupid})
        </if>
        <if test="keywordid != null and keywordid != ''">
            and keywordid in (${keywordid})
        </if>
    </sql>

	<sql id="actual_apple_ads_roi_where_sql">
        and `day` between #{start_date} and #{end_date}
        <if test="appid != null and appid != ''">
            and appId in (${appid})
        </if>
        <if test="app_category != null and app_category != ''">
            and app_category in (${app_category})
        </if>
        <if test="putUser != null and putUser != ''">
            and putUser in (${putUser})
        </if>
        <if test="account != null and account != ''">
            and accountId in (${account})
        </if>
        <if test="campaign_id != null and campaign_id != ''">
            and campaignId in (${campaign_id})
        </if>
        <if test="adgroupname != null and adgroupname != ''">
            and groupName in (${adgroupname})
        </if>
        <if test="keyword != null and keyword != ''">
            and keyword in (${keyword})
        </if>
    </sql>
    <select id="selectAppleAdsName" resultType="java.util.Map">
        select account,campaignId,campaignName,adGroupId,adGroupName,keywordId,keyword from dnwx_adt.dnwx_apple_ads_spend_media_report
        <where>
            <if test="campaign != null and campaign != ''">
                and campaignName like concat('%',#{campaign},'%')
            </if>
            <if test="adgroup != null and adgroup != ''">
                and adGroupName like concat('%',#{adgroup},'%')
            </if>
            <if test="keyword != null and keyword != ''">
                and keyword like concat('%',#{keyword},'%')
            </if>
            <if test="account != null and account != ''">
                and account like concat('%',#{account},'%')
            </if>
        </where>
        group by account,campaignId,adGroupId,keywordId
    </select>
    <select id="selectMixAppleGameRoiReport" resultType="com.wbgame.pojo.jettison.AppleRoiReportVo">
        <include refid="mixAppleGameRoiSql"/>
    </select>
    <select id="selectMixAppleRoiReport" resultType="com.wbgame.pojo.jettison.AppleRoiReportVo">
        <include refid="mixAppleRoiSql"/>
    </select>
    <select id="countMixAppleGameRoiReport" resultType="com.wbgame.pojo.jettison.AppleRoiReportVo">
        select
            sum(spend) spend,
            sum(impressions) impressions,
sum(click) click,
sum(installs) installs,
sum(redownloads) redownloads,
sum(spend_click_avg) spend_click_avg,
sum(spend_installs_avg) spend_installs_avg,
sum(click_rate) click_rate,
sum(installs_rate) installs_rate,
sum(active_rate) active_rate,
sum(active_cost) active_cost,
sum(distinctinstalls) distinctinstalls,
sum(reg_users) reg_users,
sum(add_pay_user_cost) add_pay_user_cost,
sum(add_pay_user_rate) add_pay_user_rate,
sum(add_pay_users) add_pay_users,
sum(add_pay_revenue) add_pay_revenue
<choose>
    <when test="game_revenue_index == 1">
        <foreach collection="game_days" item="i">
            ,sum(roi_rate_${i}) roi_rate_${i}
            ,sum(pay_retain_rate_${i}) pay_retain_rate_${i}
            ,sum(increase_n_${i}) increase_n_${i}
        </foreach>
    </when>
    <when test="game_revenue_index == 2">
        <foreach collection="game_days" item="i">
            ,sum(roi_rate_${i}) roi_rate_${i}
            ,sum(pay_retain_rate_${i}) pay_retain_rate_${i}
            ,sum(increase_n_${i}) increase_n_${i}
        </foreach>
    </when>
    <otherwise>
        <foreach collection="game_days" item="i">
            ,sum(roi_rate_${i}) roi_rate_${i}
            ,sum(pay_retain_rate_${i}) pay_retain_rate_${i}
            ,sum(increase_n_${i}) increase_n_${i}
        </foreach>
    </otherwise>
</choose>
        from (<include refid="mixAppleGameRoiSql"/>) xx
    </select>
    <select id="countMixAppleRoiReport" resultType="com.wbgame.pojo.jettison.AppleRoiReportVo">
        select
            sum(spend) spend,
            sum(impressions) impressions,
            sum(click) click,
            sum(installs) installs,
            sum(newdownloads) newdownloads,
            sum(redownloads) redownloads,
            sum(spend_click_avg) spend_click_avg,
            sum(spend_installs_avg) spend_installs_avg,
            sum(click_rate) click_rate,
            sum(installs_rate) installs_rate,
            sum(active_rate) active_rate,
            sum(active_cost) active_cost,
            sum(distinctinstalls) distinctinstalls,
            sum(first_pay_users) first_pay_users,
            sum(first_pay_cost) first_pay_cost,
            sum(pay_income) pay_income,
            sum(week_first_payment_users) week_first_payment_users,
            sum(month_first_payment_users) month_first_payment_users,
            sum(year_first_payment_users) year_first_payment_users
        <foreach collection="renew_nums" item="i">
            ,sum(renew_users_${i}) renew_users_${i}
            ,sum(renew_revenue_${i}) renew_revenue_${i}
            ,sum(agg_renew_revenue_${i}) agg_renew_revenue_${i}
            ,sum(renew_users_${i}_rate) renew_users_${i}_rate
            ,sum(lt${i}) lt${i}
            ,sum(agg_roi_${i}_rate) agg_roi_${i}_rate
        </foreach>
            from (<include refid="mixAppleRoiSql"/>) xx
    </select>

    <sql id="mixAppleRoiSql">
        select
tdate,appid,app_name,app_category,putUser,account,campaign_id,campaign_name,adgroupid,adgroupname,keywordid,keyword
        ,convert(sum(spend),decimal(10,2)) spend,sum(impressions) impressions,sum(click) click,sum(installs) installs
        ,sum(newdownloads) newdownloads,sum(redownloads) redownloads
        ,ifnull(convert(sum(spend)/sum(click),DECIMAL(10,2)),0) spend_click_avg
        ,ifnull(convert(sum(spend)/sum(installs),DECIMAL(10,2)),0) spend_installs_avg
        ,ifnull(convert(sum(click)/sum(impressions)*100,DECIMAL(10,2)),0) click_rate
        ,ifnull(convert(sum(installs)/sum(click)*100,DECIMAL(10,2)),0) installs_rate
        ,ifnull(convert(sum(distinctinstalls)/sum(newdownloads+redownloads)*100,DECIMAL(10,2)),0) active_rate
        ,ifnull(convert(sum(spend)/sum(distinctinstalls),DECIMAL(10,2)),0) active_cost
        ,sum(distinctinstalls) distinctinstalls

        ,sum(week_first_payment_users+month_first_payment_users+year_first_payment_users) first_pay_users
        ,ifnull(convert(sum(spend)/sum(week_first_payment_users+month_first_payment_users+year_first_payment_users),DECIMAL(10,2)),0) first_pay_cost
        ,convert(sum(week_first_payment_amount+month_first_payment_amount+year_first_payment_amount),decimal(10,2)) pay_income
        ,sum(week_first_payment_users) week_first_payment_users
        ,sum(month_first_payment_users) month_first_payment_users
        ,sum(year_first_payment_users) year_first_payment_users
        <foreach collection="renew_nums" item="i">
            ,sum(renew_users_${i}) renew_users_${i}
            ,convert(sum(renew_revenue_${i}),decimal(10,2)) renew_revenue_${i}
            ,convert(sum(agg_renew_revenue_${i}),decimal(10,2)) agg_renew_revenue_${i}
            ,ifnull(convert(sum(renew_users_${i})/sum(week_first_payment_users)*100,DECIMAL(10,2)),0) renew_users_${i}_rate
            ,ifnull(convert(sum(agg_renew_users_${i})/sum(week_first_payment_users),DECIMAL(10,2)),0) lt${i}
            ,ifnull(convert(sum(agg_renew_revenue_${i})/sum(spend)*100,DECIMAL(10,2)),0) agg_roi_${i}_rate
        </foreach>
        from dnwx_bi.ads_apple_ads_renew_detail_daily a left join app_info b on a.appid = b.id
        where tdate between #{start_date} and #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="app_category != null and app_category != ''">
            and app_category in (${app_category})
        </if>
        <if test="putUser != null and putUser != ''">
            and putUser in (${putUser})
        </if>
        <if test="account != null and account != ''">
            and account in (${account})
        </if>
        <if test="campaign_id != null and campaign_id != ''">
            and campaign_id in (${campaign_id})
        </if>
        <if test="adgroupid != null and adgroupid != ''">
            and adgroupid in (${adgroupid})
        </if>
        <if test="keywordid != null and keywordid != ''">
            and keywordid in (${keywordid})
        </if>
        <if test="group != null and group != ''">
            GROUP BY ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
union all
select
`day` tdate,appId appid,app_name,app_category,putUser,accountId account,campaignId campaign_id,campaignName campaign_name,'' adgroupid,groupName adgroupname,'' keywordid, keyword keyword
        ,convert(sum(spend),decimal(10,2)) spend,sum(impressions) impressions,sum(click) click,sum(installs) installs
        ,sum(newdownloads) newdownloads,sum(redownloads) redownloads
        ,ifnull(convert(sum(spend)/sum(click),DECIMAL(10,2)),0) spend_click_avg
        ,ifnull(convert(sum(spend)/sum(installs),DECIMAL(10,2)),0) spend_installs_avg
        ,ifnull(convert(sum(click)/sum(impressions)*100,DECIMAL(10,2)),0) click_rate
        ,ifnull(convert(sum(installs)/sum(click)*100,DECIMAL(10,2)),0) installs_rate
        ,ifnull(convert(sum(distinctinstalls)/sum(newdownloads+redownloads)*100,DECIMAL(10,2)),0) active_rate
        ,ifnull(convert(sum(spend)/sum(distinctinstalls),DECIMAL(10,2)),0) active_cost
		,sum(distinctinstalls) distinctinstalls

        ,sum(pay_users) first_pay_users
        ,convert(sum(spend)/sum(pay_users),decimal(10,2)) first_pay_cost
        ,convert(sum(pay_income),decimal(10,2)) pay_income
        ,sum(week_sub_count) week_first_payment_users
        ,sum(month_sub_count) month_first_payment_users
        ,sum(year_sub_count) year_first_payment_users
        <foreach collection="renew_nums" item="i">
            ,0 renew_users_${i}
            ,0 renew_revenue_${i}
            ,0 agg_renew_revenue_${i}
            ,0 renew_users_${i}_rate
            ,0 lt${i}
            ,0 agg_roi_${i}_rate
        </foreach>
        from dnwx_adt.apple_ads_actual_roi_report a left join app_info b on a.appId = b.id
        where `day` = #{end_date}
<if test="appid != null and appid != ''">
            and appId in (${appid})
        </if>
        <if test="app_category != null and app_category != ''">
            and app_category in (${app_category})
        </if>
        <if test="putUser != null and putUser != ''">
            and putUser in (${putUser})
        </if>
        <if test="account != null and account != ''">
            and accountId in (${account})
        </if>
        <if test="campaign_id != null and campaign_id != ''">
            and campaignId in (${campaign_id})
        </if>
        <if test="adgroupname != null and adgroupname != ''">
            and groupName in (${adgroupname})
        </if>
        <if test="keyword != null and keyword != ''">
            and keyword in (${keyword})
        </if>
<if test="group != null and group != ''">
            GROUP BY ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </sql>
    <sql id="mixAppleGameRoiSql">
        select
tdate,appid,app_name,app_category,putUser,account,campaign_id,campaign_name,adgroupid,adgroupname,keywordid,keyword
        ,convert(sum(spend),decimal(10,2)) spend,sum(impressions) impressions,sum(click) click,sum(installs) installs
        ,sum(newdownloads) newdownloads,sum(redownloads) redownloads
        ,ifnull(convert(sum(spend)/sum(click),DECIMAL(10,2)),0) spend_click_avg
        ,ifnull(convert(sum(spend)/sum(installs),DECIMAL(10,2)),0) spend_installs_avg
        ,ifnull(convert(sum(click)/sum(impressions)*100,DECIMAL(10,2)),0) click_rate
        ,ifnull(convert(sum(installs)/sum(click)*100,DECIMAL(10,2)),0) installs_rate
        ,ifnull(convert(sum(distinctinstalls)/sum(newdownloads+redownloads)*100,DECIMAL(10,2)),0) active_rate
        ,ifnull(convert(sum(spend)/sum(distinctinstalls),DECIMAL(10,2)),0) active_cost
        ,sum(distinctinstalls) distinctinstalls

        ,sum(reg_users) reg_users
        ,convert(sum(spend)/sum(add_pay_users),decimal(10,2)) add_pay_user_cost
        ,convert(ifnull(sum(add_pay_users)/sum(reg_users),0)*100,decimal(10,2))  add_pay_user_rate
        ,sum(add_pay_users) add_pay_users
        ,sum(add_pay_revenue/100) add_pay_revenue
        <choose>
            <when test="game_revenue_index == 1">
                <foreach collection="game_days" item="i">
                    ,ifnull(convert((sum(add_revenue_${i}))/100/sum(spend)*100, decimal(10,2)),0) as roi_rate_${i}
                    ,ifnull(convert((sum(add_purchase_retention_${i})/sum(add_pay_users))*100, decimal(10,2)),0) pay_retain_rate_${i}
                    ,ifnull(convert((sum(add_revenue_${i}) )/(sum(add_revenue_1) ), decimal(10,2)),0) as increase_n_${i}
                </foreach>
            </when>
            <when test="game_revenue_index == 2">
                <foreach collection="game_days" item="i">
                    ,ifnull(convert((sum(add_purchase_revenue_${i}))/100/sum(spend)*100, decimal(10,2)),0) as roi_rate_${i}
                    ,ifnull(convert((sum(add_purchase_retention_${i})/sum(add_pay_users))*100, decimal(10,2)),0) pay_retain_rate_${i}
                    ,ifnull(convert((sum(add_purchase_revenue_${i}))/(sum(add_purchase_revenue_1)), decimal(10,2)),0) as increase_n_${i}
                </foreach>
            </when>
            <otherwise>
                <foreach collection="game_days" item="i">
                    ,ifnull(convert((sum(add_revenue_${i}) + sum(add_purchase_revenue_${i}))/100/sum(spend)*100, decimal(10,2)),0) as roi_rate_${i}
                    ,ifnull(convert((sum(add_purchase_retention_${i})/sum(add_pay_users))*100, decimal(10,2)),0) pay_retain_rate_${i}
                    ,ifnull(convert((sum(add_revenue_${i}) + sum(add_purchase_revenue_${i}))/(sum(add_revenue_1) + sum(add_purchase_revenue_1)), decimal(10,2)),0) as increase_n_${i}
                </foreach>
        </otherwise>
        </choose>
        from dnwx_bi.ads_apple_ads_game_ios a left join app_info b on a.appid = b.id
        where tdate between #{start_date} and #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="app_category != null and app_category != ''">
            and app_category in (${app_category})
        </if>
        <if test="putUser != null and putUser != ''">
            and putUser in (${putUser})
        </if>
        <if test="account != null and account != ''">
            and account in (${account})
        </if>
        <if test="campaign_id != null and campaign_id != ''">
            and campaign_id in (${campaign_id})
        </if>
        <if test="adgroupid != null and adgroupid != ''">
            and adgroupid in (${adgroupid})
        </if>
        <if test="keywordid != null and keywordid != ''">
            and keywordid in (${keywordid})
        </if>
        <if test="group != null and group != ''">
            GROUP BY ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
union all
select
`day` tdate,appId appid,app_name,app_category,putUser,accountId account,campaignId campaign_id,campaignName campaign_name,'' adgroupid,groupName adgroupname,'' keywordid, keyword keyword
        ,convert(sum(spend),decimal(10,2)) spend,sum(impressions) impressions,sum(click) click,sum(installs) installs
        ,sum(newdownloads) newdownloads,sum(redownloads) redownloads
        ,ifnull(convert(sum(spend)/sum(click),DECIMAL(10,2)),0) spend_click_avg
        ,ifnull(convert(sum(spend)/sum(installs),DECIMAL(10,2)),0) spend_installs_avg
        ,ifnull(convert(sum(click)/sum(impressions)*100,DECIMAL(10,2)),0) click_rate
        ,ifnull(convert(sum(installs)/sum(click)*100,DECIMAL(10,2)),0) installs_rate
        ,ifnull(convert(sum(distinctinstalls)/sum(newdownloads+redownloads)*100,DECIMAL(10,2)),0) active_rate
        ,ifnull(convert(sum(spend)/sum(distinctinstalls),DECIMAL(10,2)),0) active_cost
                ,sum(distinctinstalls) distinctinstalls

        ,0 reg_users
        ,convert(sum(spend)/sum(pay_users),decimal(10,2)) add_pay_user_cost
        ,ifnull(convert(sum(pay_users)/sum(distinctinstalls)*100,decimal(10,2)), 0) add_pay_user_rate
        ,sum(pay_users) add_pay_users
        ,sum(pay_income)+sum(ltv) add_pay_revenue
        <choose>
            <when test="game_revenue_index == 1">
                <foreach collection="game_days" item="i">
                    ,0 as roi_rate_${i}
                    ,0 pay_retain_rate_${i}
                    ,0 as increase_n_${i}
                </foreach>
            </when>
            <when test="game_revenue_index == 2">
                <foreach collection="game_days" item="i">
                    ,0 as roi_rate_${i}
                    ,0 pay_retain_rate_${i}
                    ,0 as increase_n_${i}
                </foreach>
            </when>
            <otherwise>
                <foreach collection="game_days" item="i">
                    ,0 as roi_rate_${i}
                    ,0 pay_retain_rate_${i}
                    ,0 as increase_n_${i}
                </foreach>
        </otherwise>
        </choose>
        from dnwx_adt.apple_ads_actual_roi_report a left join app_info b on a.appId = b.id
        where `day` = #{end_date}
        <if test="appid != null and appid != ''">
            and appId in (${appid})
        </if>
        <if test="app_category != null and app_category != ''">
            and app_category in (${app_category})
        </if>
        <if test="putUser != null and putUser != ''">
            and putUser in (${putUser})
        </if>
        <if test="account != null and account != ''">
            and accountId in (${account})
        </if>
        <if test="campaign_id != null and campaign_id != ''">
            and campaignId in (${campaign_id})
        </if>
        <if test="adgroupname != null and adgroupname != ''">
            and groupName in (${adgroupname})
        </if>
        <if test="keyword != null and keyword != ''">
            and keyword in (${keyword})
        </if>
<if test="group != null and group != ''">
            GROUP BY ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </sql>
</mapper>