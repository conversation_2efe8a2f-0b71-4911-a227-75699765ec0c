<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adb.ClientSDKMapper">

    <sql id="dn_extend_where_sql">
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="prjid != null and prjid != ''">
            and prjid in (${prjid})
        </if>
        <if test="cha_id != null and cha_id != ''">
            and cha_id in (${cha_id})
        </if>
        <if test="adpos_type != null and adpos_type != ''">
            and adpos_type = #{adpos_type}
        </if>
        <if test="cha_type_name != null and cha_type_name != ''">
            and cha_type_name in (${cha_type_name})
        </if>
        <if test="cha_media != null and cha_media != ''">
            and cha_media in (${cha_media})
        </if>
        <if test="cha_sub_launch != null and cha_sub_launch != ''">
            and cha_sub_launch in (${cha_sub_launch})
        </if>
        <if test="apps != null and apps != ''">
            and appid in (${apps})
        </if>
    </sql>

    <select id="selectSdkRelationReport" parameterType="java.util.Map" resultType="java.util.Map">
        select *
        from (
            select source.tdate, source.appid, source.cha_id, source.prjid, source.sdk_name, source.ver,
            source.local_ver, source.num, result_name.singleid, result_name.tempName
            from (
                select *
                from dn_extend_sdk_relation
                where tdate BETWEEN #{sdate} AND #{edate}
                <include refid="dn_extend_where_sql"/>
                <if test="sdk_name != null and sdk_name != ''">and sdk_name like concat('%',#{sdk_name},'%')
                </if>
                    <if test="ver != null and ver != ''">
                        and ver like concat('%',#{ver},'%')
                    </if>
                    <if test="local_ver != null and local_ver != ''">
                        and local_ver like concat('%',#{local_ver},'%')
                    </if>
            ) source
            left join (
                select project_id, sdk_name, sdk_version, version, temp_id
                from wbgui_project_sdk_relation
                <if test="temp_id != null and temp_id != ''">
                    <where>
                        temp_id in (${temp_id})
                    </where>
                </if>
            ) sdk_relation
            on source.prjid = sdk_relation.project_id
            and source.sdk_name = sdk_relation.sdk_name
            and source.ver = sdk_relation.sdk_version
            and source.local_ver = sdk_relation.version
            left join (
                select singleid, tempName
                from wbgui_tempmodule
                <if test="temp_id != null and temp_id != ''">
                    <where>
                        singleid in (${temp_id})
                    </where>
                </if>
            ) result_name
           on sdk_relation.temp_id = result_name.singleid
            <if test="temp_id != null and temp_id != ''">
                <where>
                    singleid in (${temp_id})
                </where>
            </if>

        ) result
        <choose>
            <when test="order_str != null and order_str != ''">
                order by
                    CASE WHEN appid is null or appid='' THEN 1 ELSE 0 END,
                    CASE WHEN ver is null or ver='' THEN 1 ELSE 0 END,
                    CASE WHEN sdk_name is null or sdk_name='' THEN 1 ELSE 0 END,
                    ${order_str}
            </when>
            <otherwise>
                order by tdate desc,num desc
            </otherwise>
        </choose>

    </select>

    <select id="selectSDKTemps" resultType="java.util.Map">
        select a.appid,a.cha_id,a.prjid
        ,GROUP_CONCAT(a.temp_id SEPARATOR ' | ') temp_id
        ,GROUP_CONCAT(a.tempName SEPARATOR ' | ') temp_name from
        (select a.appid,a.cha_id,a.prjid,b.temp_id,c.tempName
        from dn_extend_sdk_relation a
        join wbgui_project_sdk_relation b
        on a.prjid = b.project_id
        and a.sdk_name = b.sdk_name
        and a.ver = b.sdk_version
        and a.local_ver = b.version
        join wbgui_tempmodule c on b.temp_id = c.singleid
        where tdate = #{day}
        GROUP BY a.appid,a.cha_id,a.prjid,b.temp_id,c.tempName
        having sum(num) > 100) a
        join
        (select a.appid,a.cha_id,max(a.prjid) prjid from
        (select a.appid,a.cha_id,a.prjid,b.temp_id,c.tempName
        from dn_extend_sdk_relation a
        join wbgui_project_sdk_relation b
        on a.prjid = b.project_id
        and a.sdk_name = b.sdk_name
        and a.ver = b.sdk_version
        and a.local_ver = b.version
        join wbgui_tempmodule c on b.temp_id = c.singleid
        where tdate = #{day}
        GROUP BY a.appid,a.cha_id,a.prjid,b.temp_id,c.tempName
        having sum(num) > 100) a
        GROUP BY a.appid,a.cha_id) b
        on a.prjid = b.prjid and a.appid = b.appid and a.cha_id = b.cha_id
        GROUP BY a.appid,a.cha_id,a.prjid
    </select>

    <select id="selectSDKDateChannel" resultType="java.util.Map">
        <if test="singleId != null and singleId != ''">
            select singleid, ifnull(tempName, '') as tempName, date, ifnull(cha, '') as cha
            from wbgui_tempmodule
            where singleid = ${singleId}
        </if>
    </select>
</mapper>