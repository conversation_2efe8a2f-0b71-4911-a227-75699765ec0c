<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.UserCompositionAnalysisMapper">



    <select id="selectDataList" resultType="com.wbgame.pojo.operate.UserCompositionAnalysisVO"
            parameterType="com.wbgame.pojo.operate.UserCompositionAnalysisDTO">


        select * from (


            <include refid="selectData1"/>
                          ) a



        <if test="grpList != null and grpList.size > 0">
            where grp IN
            <foreach collection="grpList" item="grp" open="(" separator="," close=")">
                #{grp}
            </foreach>
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                 order by tdate,grp
            </otherwise>
        </choose>
    </select>

    <select id="selectDateScheme" resultType="com.wbgame.pojo.operate.UserCompositionAnalysisVO"
            parameterType="com.wbgame.pojo.operate.UserCompositionAnalysisDTO">


        select
        tdate,
        grp,
        sum(user_cnt)                                        total_user_cnt,
        cast(sum(iap_revenue) / sum(user_cnt) / 100 as decimal (18, 2))  arpu,
        cast(sum(iap_revenue) / 100 as decimal (18, 2))      total_income,
        truncate(sum(retention_1day_user_cnt) / sum(case when retention_1day_user_cnt>0 then user_cnt end ) * 100, 2)  stay2,
        truncate(sum(retention_3day_user_cnt) / sum(case when retention_3day_user_cnt>0 then user_cnt end ) * 100, 2)  stay3,
        truncate(sum(retention_7day_user_cnt) / sum(case when retention_7day_user_cnt>0 then user_cnt end ) * 100, 2)  stay7,
        truncate(sum(retention_14day_user_cnt) / sum(case when retention_14day_user_cnt>0 then user_cnt end ) * 100, 2) stay14,
        truncate(sum(retention_30day_user_cnt) / sum(case when retention_30day_user_cnt>0 then user_cnt end ) * 100, 2) stay30,
        truncate(sum(retention_60day_user_cnt) / sum(case when retention_60day_user_cnt>0 then user_cnt end ) * 100, 2) stay60,
        truncate(sum(iap_user_cnt) / sum(user_cnt) * 100, 2) pay_rate,
        cast(sum(iap_revenue) / sum(user_cnt) / 100 as decimal (18, 2))  arpu,
        cast(ifnull(sum(iap_revenue) / sum(iap_user_cnt), 0) / 100 as decimal (18, 2))    arrpu
        from (
            SELECT   
		<choose>
           <when test='null!=group and group.contains("week")'>
              DATE_FORMAT(tdate,'%x-%v') tdate,
           </when>
           <when test='null!=group and group.contains("month")'>
             DATE_FORMAT(tdate,'%Y-%m') tdate,
           </when>
            <when test='null!=group and group.contains("year")'>
                DATE_FORMAT(tdate,'%Y') tdate,
            </when>
           <when test='null!=group and group.contains("tdate")'>
             tdate,
           </when>
           <otherwise>
           </otherwise>
     	</choose>
            case when group_id > 30 then 31 else group_id end as grp,
            sum(user_cnt)                 user_cnt,
            <include refid="avg_users_sql"/>
            sum(retention_1day_user_cnt)  retention_1day_user_cnt,
            sum(retention_3day_user_cnt)  retention_3day_user_cnt,
            sum(retention_7day_user_cnt)  retention_7day_user_cnt,
            sum(retention_14day_user_cnt) retention_14day_user_cnt,
            sum(retention_30day_user_cnt) retention_30day_user_cnt,
            sum(iap_revenue) iap_revenue,
            sum(iap_user_cnt) iap_user_cnt,
            sum(retention_60day_user_cnt) retention_60day_user_cnt
            from ads_wechat_user_group_retention_iap_info_daily
            <include refid="condition"/>
            
        <choose>
           <when test='null!=group and group.contains("week")'>
              group by DATE_FORMAT(tdate,'%x-%v'), app_name, appid, grp
           </when>
           <when test='null!=group and group.contains("month")'>
            group by DATE_FORMAT(tdate,'%Y-%m'), app_name, appid, grp
           </when>
            <when test='null!=group and group.contains("year")'>
             group by  DATE_FORMAT(tdate,'%Y') , app_name, appid, grp
            </when>
           <when test='null!=group and group.contains("tdate")'>
            group by tdate, app_name, appid, grp
           </when>
           <otherwise>
           </otherwise>
     	</choose>

            union all
            SELECT   
		<choose>
           <when test='null!=group and group.contains("week")'>
              DATE_FORMAT(tdate,'%x-%v') tdate,
           </when>
           <when test='null!=group and group.contains("month")'>
             DATE_FORMAT(tdate,'%Y-%m') tdate,
           </when>
            <when test='null!=group and group.contains("year")'>
                DATE_FORMAT(tdate,'%Y') tdate,
            </when>
           <when test='null!=group and group.contains("tdate")'>
             tdate,
           </when>
           <otherwise>
           </otherwise>
     	</choose>
            case when group_id > 30 then 31 else group_id end as grp,
            sum(user_cnt)                 user_cnt,
            <include refid="avg_users_sql"/>
            sum(retention_1day_user_cnt)  retention_1day_user_cnt,
            sum(retention_3day_user_cnt)  retention_3day_user_cnt,
            sum(retention_7day_user_cnt)  retention_7day_user_cnt,
            sum(retention_14day_user_cnt) retention_14day_user_cnt,
            sum(retention_30day_user_cnt) retention_30day_user_cnt,
            sum(iap_revenue) iap_revenue,
            sum(iap_user_cnt) iap_user_cnt,
            sum(retention_60day_user_cnt) retention_60day_user_cnt
            from ads_user_group_retention_iap_info_daily
            <include refid="condition"/>
            
              <choose>
	           <when test='null!=group and group.contains("week")'>
	              group by DATE_FORMAT(tdate,'%x-%v'), app_name, appid, grp
	           </when>
	           <when test='null!=group and group.contains("month")'>
	            group by DATE_FORMAT(tdate,'%Y-%m'), app_name, appid, grp
	           </when>
                  <when test='null!=group and group.contains("year")'>
                      group by  DATE_FORMAT(tdate,'%Y') , app_name, appid, grp
                  </when>
	           <when test='null!=group and group.contains("tdate")'>
	            group by tdate, app_name, appid, grp
	           </when>
	           <otherwise>
	           </otherwise>
	     	</choose>


            union all
            SELECT   
		<choose>
           <when test='null!=group and group.contains("week")'>
              DATE_FORMAT(tdate,'%x-%v') tdate,
           </when>
           <when test='null!=group and group.contains("month")'>
             DATE_FORMAT(tdate,'%Y-%m') tdate,
           </when>
            <when test='null!=group and group.contains("year")'>
                DATE_FORMAT(tdate,'%Y') tdate,
            </when>
           <when test='null!=group and group.contains("tdate")'>
             tdate,
           </when>
           <otherwise>
           </otherwise>
     	</choose>
            case when group_id > 30 then 31 else group_id end as grp,
            sum(user_cnt)                 user_cnt,
            <include refid="avg_users_sql"/>
            sum(retention_1day_user_cnt)  retention_1day_user_cnt,
            sum(retention_3day_user_cnt)  retention_3day_user_cnt,
            sum(retention_7day_user_cnt)  retention_7day_user_cnt,
            sum(retention_14day_user_cnt) retention_14day_user_cnt,
            sum(retention_30day_user_cnt) retention_30day_user_cnt,
            sum(iap_revenue) iap_revenue,
            sum(iap_user_cnt) iap_user_cnt,
            sum(retention_60day_user_cnt) retention_60day_user_cnt
            from ads_user_group_retention_iap_info_ios_daily
            <include refid="condition"/>
            <choose>
	           <when test='null!=group and group.contains("week")'>
	              group by DATE_FORMAT(tdate,'%x-%v'), app_name, appid, grp
	           </when>
	           <when test='null!=group and group.contains("month")'>
	            group by DATE_FORMAT(tdate,'%Y-%m'), app_name, appid, grp
	           </when>
                <when test='null!=group and group.contains("year")'>
                    group by  DATE_FORMAT(tdate,'%Y') , app_name, appid, grp
                </when>
	           <when test='null!=group and group.contains("tdate")'>
	            group by tdate, app_name, appid, grp
	           </when>
	           <otherwise>
	           </otherwise>
	     	</choose>
                          ) a

        <if test="grpList != null and grpList.size > 0">
            where grp IN
            <foreach collection="grpList" item="grp" open="(" separator="," close=")">
                #{grp}
            </foreach>
        </if>
        group by tdate, grp
        order by tdate
        
    </select>

    <select id="selectDataCount" resultType="com.wbgame.pojo.operate.UserCompositionAnalysisVO"
            parameterType="com.wbgame.pojo.operate.UserCompositionAnalysisDTO">

        select

            sum(user_cnt)                                        total_user_cnt,
            sum(iap_user_cnt)                                    total_iap_user,
            cast(sum(iap_revenue) / 100 as decimal (18, 2))                  total_income,
            truncate(sum(iap_user_cnt) / sum(user_cnt) * 100, 2) pay_rate,
            cast(sum(iap_revenue) / sum(user_cnt) / 100 as decimal (18, 2))  arpu,
            cast(ifnull(sum(iap_revenue) / sum(iap_user_cnt), 0) / 100 as decimal (18, 2))    arrpu,
	        truncate(sum(retention_1day_user_cnt) / sum(case when retention_1day_user_cnt>0 then user_cnt end ) * 100, 2)  stay2,
	        truncate(sum(retention_3day_user_cnt) / sum(case when retention_3day_user_cnt>0 then user_cnt end ) * 100, 2)  stay3,
	        truncate(sum(retention_7day_user_cnt) / sum(case when retention_7day_user_cnt>0 then user_cnt end ) * 100, 2)  stay7,
	        truncate(sum(retention_14day_user_cnt) / sum(case when retention_14day_user_cnt>0 then user_cnt end ) * 100, 2) stay14,
	        truncate(sum(retention_30day_user_cnt) / sum(case when retention_30day_user_cnt>0 then user_cnt end ) * 100, 2) stay30,
	        truncate(sum(retention_60day_user_cnt) / sum(case when retention_60day_user_cnt>0 then user_cnt end ) * 100, 2) stay60
        from (

            SELECT tdate,
            app_name,
            appid,
            case when group_id > 30 then 31 else group_id end as grp,
            sum(user_cnt)                                        user_cnt,
            sum(iap_user_cnt)                                    iap_user_cnt,
            sum(iap_revenue)                                     iap_revenue,
	        sum(retention_1day_user_cnt)  retention_1day_user_cnt,
	        sum(retention_3day_user_cnt)  retention_3day_user_cnt,
	        sum(retention_7day_user_cnt)  retention_7day_user_cnt,
	        sum(retention_14day_user_cnt) retention_14day_user_cnt,
	        sum(retention_30day_user_cnt) retention_30day_user_cnt,
	        sum(retention_60day_user_cnt) retention_60day_user_cnt
	        from ads_wechat_user_group_retention_iap_info_daily

            <include refid="condition"/>
            and group_id != 0
            group by tdate, app_name, appid, grp

            union all
            SELECT tdate,
                app_name,
                appid,
                case when group_id > 30 then 31 else group_id end as grp,
                sum(user_cnt)                                        user_cnt,
                sum(iap_user_cnt)                                    iap_user_cnt,
                sum(iap_revenue)                                     iap_revenue,
	        sum(retention_1day_user_cnt)  retention_1day_user_cnt,
	        sum(retention_3day_user_cnt)  retention_3day_user_cnt,
	        sum(retention_7day_user_cnt)  retention_7day_user_cnt,
	        sum(retention_14day_user_cnt) retention_14day_user_cnt,
	        sum(retention_30day_user_cnt) retention_30day_user_cnt,
	        sum(retention_60day_user_cnt) retention_60day_user_cnt
            from ads_user_group_retention_iap_info_daily

            <include refid="condition"/>
        and group_id != 0
            group by tdate, app_name, appid, grp

            union all

            SELECT tdate,
                app_name,
                appid,
                case when group_id > 30 then 31 else group_id end as grp,
                sum(user_cnt)                                        user_cnt,
                sum(iap_user_cnt)                                    iap_user_cnt,
                sum(iap_revenue)                                     iap_revenue,
	        sum(retention_1day_user_cnt)  retention_1day_user_cnt,
	        sum(retention_3day_user_cnt)  retention_3day_user_cnt,
	        sum(retention_7day_user_cnt)  retention_7day_user_cnt,
	        sum(retention_14day_user_cnt) retention_14day_user_cnt,
	        sum(retention_30day_user_cnt) retention_30day_user_cnt,
	        sum(retention_60day_user_cnt) retention_60day_user_cnt
            from ads_user_group_retention_iap_info_ios_daily

            <include refid="condition"/>
        and group_id != 0
            group by tdate, app_name, appid, grp
        ) a

        <if test="grpList != null and grpList.size > 0">
            where grp IN
            <foreach collection="grpList" item="grp" open="(" separator="," close=")">
                #{grp}
            </foreach>
        </if>

    </select>

    <sql id="avg_users_sql">
        <choose>
            <when test='null!=group and group.contains("week")'>
                cast(sum(user_cnt)/count(distinct tdate) as decimal (10, 0)) day_avg_user,
            </when>
            <when test='null!=group and group.contains("month")'>
                cast(sum(user_cnt)/count(distinct tdate) as decimal (10, 0)) day_avg_user,
            </when>
            <when test='null!=group and group.contains("year")'>
                cast(sum(user_cnt)/count(distinct tdate) as decimal (10, 0)) day_avg_user,
            </when>
            <otherwise>
                sum(user_cnt) day_avg_user,
            </otherwise>
        </choose>
    </sql>

    <sql id="selectData1">

        SELECT  
		<choose>
           <when test='null!=group and group.contains("week")'>
              DATE_FORMAT(tdate,'%x-%v') tdate,
           </when>
           <when test='null!=group and group.contains("month")'>
             DATE_FORMAT(tdate,'%Y-%m') tdate,
           </when>
            <when test='null!=group and group.contains("year")'>
                DATE_FORMAT(tdate,'%Y') tdate,
            </when>
           <when test='null!=group and group.contains("tdate")'>
            tdate ,
           </when>
           <otherwise>
           </otherwise>
     	</choose>
        app_name, appid,
        case when group_id > 30 then 31 else group_id end as grp,
        sum(user_cnt)                                        total_user_cnt,
        <include refid="avg_users_sql"/>
        sum(iap_user_cnt)                                    total_iap_user,
        cast(sum(iap_revenue) / 100 as decimal (18, 2))                  total_income,
        truncate(sum(iap_user_cnt) / sum(user_cnt) * 100, 2) pay_rate,
        cast(sum(iap_revenue) / sum(user_cnt) / 100 as decimal (18, 2))  arpu,
        cast(ifnull(sum(iap_revenue) / sum(iap_user_cnt), 0) / 100 as decimal (18, 2))    arrpu,
        truncate(sum(retention_1day_user_cnt) / sum(case when retention_1day_user_cnt>0 then user_cnt end ) * 100, 2)  stay2,
        truncate(sum(retention_3day_user_cnt) / sum(case when retention_3day_user_cnt>0 then user_cnt end) * 100, 2)  stay3,
        truncate(sum(retention_7day_user_cnt) / sum(case when retention_7day_user_cnt>0 then user_cnt end) * 100, 2)  stay7,
        truncate(sum(retention_14day_user_cnt) / sum(case when retention_14day_user_cnt>0 then user_cnt end) * 100, 2) stay14,
        truncate(sum(retention_30day_user_cnt) / sum(case when retention_30day_user_cnt>0 then user_cnt end) * 100, 2) stay30,
        truncate(sum(retention_60day_user_cnt) / sum(case when retention_60day_user_cnt>0 then user_cnt end) * 100, 2) stay60
        from ads_wechat_user_group_retention_iap_info_daily

        <include refid="condition"/>
        <choose>
           <when test='null!=group and group.contains("week")'>
              group by DATE_FORMAT(tdate,'%x-%v'), app_name, appid, grp
           </when>
           <when test='null!=group and group.contains("month")'>
            group by DATE_FORMAT(tdate,'%Y-%m'), app_name, appid, grp
           </when>
            <when test='null!=group and group.contains("year")'>
            group by    DATE_FORMAT(tdate,'%Y') , app_name, appid, grp
            </when>
           <when test='null!=group and group.contains("tdate")'>
            group by tdate, app_name, appid, grp
           </when>
           <otherwise>
           </otherwise>
     	</choose>

        union all
        SELECT  
		<choose>
           <when test='null!=group and group.contains("week")'>
              DATE_FORMAT(tdate,'%x-%v') tdate,
           </when>
           <when test='null!=group and group.contains("month")'>
             DATE_FORMAT(tdate,'%Y-%m') tdate,
           </when>
            <when test='null!=group and group.contains("year")'>
                DATE_FORMAT(tdate,'%Y') tdate,
            </when>
           <when test='null!=group and group.contains("tdate")'>
            tdate ,
           </when>
           <otherwise>
           </otherwise>
     	</choose>
        app_name, appid,
        case when group_id > 30 then 31 else group_id end as grp,
        sum(user_cnt)                                        total_user_cnt,
        <include refid="avg_users_sql"/>
        sum(iap_user_cnt)                                    total_iap_user,
        cast(sum(iap_revenue) / 100 as decimal (18, 2))                  total_income,
        truncate(sum(iap_user_cnt) / sum(user_cnt) * 100, 2) pay_rate,
        cast(sum(iap_revenue) / sum(user_cnt) / 100 as decimal (18, 2))  arpu,
        cast(ifnull(sum(iap_revenue) / sum(iap_user_cnt), 0) / 100 as decimal (18, 2))    arrpu,
        truncate(sum(retention_1day_user_cnt) / sum(case when retention_1day_user_cnt>0 then user_cnt end ) * 100, 2)  stay2,
        truncate(sum(retention_3day_user_cnt) / sum(case when retention_3day_user_cnt>0 then user_cnt end) * 100, 2)  stay3,
        truncate(sum(retention_7day_user_cnt) / sum(case when retention_7day_user_cnt>0 then user_cnt end) * 100, 2)  stay7,
        truncate(sum(retention_14day_user_cnt) / sum(case when retention_14day_user_cnt>0 then user_cnt end) * 100, 2) stay14,
        truncate(sum(retention_30day_user_cnt) / sum(case when retention_30day_user_cnt>0 then user_cnt end) * 100, 2) stay30,
        truncate(sum(retention_60day_user_cnt) / sum(case when retention_60day_user_cnt>0 then user_cnt end) * 100, 2) stay60
        from ads_user_group_retention_iap_info_daily

        <include refid="condition"/>

        
        <choose>
           <when test='null!=group and group.contains("week")'>
              group by DATE_FORMAT(tdate,'%x-%v'), app_name, appid, grp
           </when>
           <when test='null!=group and group.contains("month")'>
            group by DATE_FORMAT(tdate,'%Y-%m'), app_name, appid, grp
           </when>
            <when test='null!=group and group.contains("year")'>
            group by DATE_FORMAT(tdate,'%Y') , app_name, appid, grp
            </when>
           <when test='null!=group and group.contains("tdate")'>
            group by tdate, app_name, appid, grp
           </when>
           <otherwise>
           </otherwise>
     	</choose>

        union all

        SELECT  
		<choose>
           <when test='null!=group and group.contains("week")'>
              DATE_FORMAT(tdate,'%x-%v') tdate,
           </when>
           <when test='null!=group and group.contains("month")'>
             DATE_FORMAT(tdate,'%Y-%m') tdate,
           </when>
            <when test='null!=group and group.contains("year")'>
                DATE_FORMAT(tdate,'%Y') tdate,
            </when>
           <when test='null!=group and group.contains("tdate")'>
            tdate ,
           </when>
           <otherwise>
           </otherwise>
     	</choose>
        app_name, appid,
        case when group_id > 30 then 31 else group_id end as grp,
        sum(user_cnt)                                        total_user_cnt,
        <include refid="avg_users_sql"/>
        sum(iap_user_cnt)                                    total_iap_user,
        cast(sum(iap_revenue) / 100 as decimal (18, 2))                  total_income,
        truncate(sum(iap_user_cnt) / sum(user_cnt) * 100, 2) pay_rate,
        cast(sum(iap_revenue) / sum(user_cnt) / 100 as decimal (18, 2))  arpu,
        cast(ifnull(sum(iap_revenue) / sum(iap_user_cnt), 0) / 100 as decimal (18, 2))    arrpu,
        truncate(sum(retention_1day_user_cnt) / sum(case when retention_1day_user_cnt>0 then user_cnt end ) * 100, 2)  stay2,
        truncate(sum(retention_3day_user_cnt) / sum(case when retention_3day_user_cnt>0 then user_cnt end) * 100, 2)  stay3,
        truncate(sum(retention_7day_user_cnt) / sum(case when retention_7day_user_cnt>0 then user_cnt end) * 100, 2)  stay7,
        truncate(sum(retention_14day_user_cnt) / sum(case when retention_14day_user_cnt>0 then user_cnt end) * 100, 2) stay14,
        truncate(sum(retention_30day_user_cnt) / sum(case when retention_30day_user_cnt>0 then user_cnt end) * 100, 2) stay30,
        truncate(sum(retention_60day_user_cnt) / sum(case when retention_60day_user_cnt>0 then user_cnt end) * 100, 2) stay60
        from ads_user_group_retention_iap_info_ios_daily

        <include refid="condition"/>

        <choose>
           <when test='null!=group and group.contains("week")'>
              group by DATE_FORMAT(tdate,'%x-%v'), app_name, appid, grp
           </when>
           <when test='null!=group and group.contains("month")'>
            group by DATE_FORMAT(tdate,'%Y-%m'), app_name, appid, grp
           </when>
            <when test='null!=group and group.contains("year")'>
                group by DATE_FORMAT(tdate,'%Y') , app_name, appid, grp
            </when>
           <when test='null!=group and group.contains("tdate")'>
            group by tdate, app_name, appid, grp
           </when>
           <otherwise>
           </otherwise>
     	</choose>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                 order by tdate
            </otherwise>
        </choose>
    </sql>
    
    <sql id="condition">

        <where>

            tdate between #{start_date} and #{end_date}
            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>
    </sql>

<!--    *******************用户活跃6组*********-->

    <select id="selectDataList6" resultType="com.wbgame.pojo.operate.UserCompositionAnalysisVO"
            parameterType="com.wbgame.pojo.operate.UserCompositionAnalysisDTO">
        select * from  (
            select a.*,b.active_user_cnt,b.avg_active_time,b.mid_active_time,b.min_active_time,b.max_active_time,b.active_day_avg_user
            from (
            <include refid="selectData2"/>
            ) a
            left join (<include refid="selectOnlineDurationList"/>) b
        on a.appid = b.appid and a.tdate = b.tdate and a.grp = b.grp) a
        <if test="grpList != null and grpList.size > 0">
            where a.grp IN
            <foreach collection="grpList" item="grp" open="(" separator="," close=")">
                #{grp}
            </foreach>
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                 order by a.tdate,a.grp
            </otherwise>
        </choose>

    </select>

    <select id="selectDataCount6" resultType="com.wbgame.pojo.operate.UserCompositionAnalysisVO"
            parameterType="com.wbgame.pojo.operate.UserCompositionAnalysisDTO">

        select

        sum(user_cnt)                                        total_user_cnt,
        sum(iap_user_cnt)                                    total_iap_user,
        cast(sum(iap_revenue) / 100 as decimal (18, 2))                  total_income,
        truncate(sum(iap_user_cnt) / sum(user_cnt) * 100, 2) pay_rate,
        cast(sum(iap_revenue) / sum(user_cnt) / 100 as decimal (18, 2))  arpu,
        cast(sum(iap_revenue) / sum(iap_user_cnt) / 100 as decimal (18, 2))    arrpu,
        truncate(sum(retention_1day_user_cnt) / sum(case when retention_1day_user_cnt>0 then user_cnt end ) * 100, 2)  stay2,
        truncate(sum(retention_3day_user_cnt) / sum(case when retention_3day_user_cnt>0 then user_cnt end ) * 100, 2)  stay3,
        truncate(sum(retention_7day_user_cnt) / sum(case when retention_7day_user_cnt>0 then user_cnt end ) * 100, 2)  stay7,
        truncate(sum(retention_14day_user_cnt) / sum(case when retention_14day_user_cnt>0 then user_cnt end ) * 100, 2) stay14,
        truncate(sum(retention_30day_user_cnt) / sum(case when retention_30day_user_cnt>0 then user_cnt end ) * 100, 2) stay30,
        truncate(sum(retention_60day_user_cnt) / sum(case when retention_60day_user_cnt>0 then user_cnt end ) * 100, 2) stay60
        from (

        SELECT tdate,
        app_name, appid,
        case when group_id=1 then '新用户'
		     when group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7 then '轻度用户'
		     when group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14 then '中度用户'
		     when group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30 then '重度用户'
		     when group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45 then '超重度用户'
		     when group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60 then '轻硬核用户'
		     when group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90 then '硬核用户'
		     when group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120 then '超硬核用户'
		     when group_id<![CDATA[>=]]>121 then '骨灰级用户'
        else group_id end as grp,
        sum(user_cnt)                                        user_cnt,
        sum(iap_user_cnt)                                    iap_user_cnt,
        sum(iap_revenue)                                     iap_revenue,
        sum(retention_1day_user_cnt)  retention_1day_user_cnt,
        sum(retention_3day_user_cnt)  retention_3day_user_cnt,
        sum(retention_7day_user_cnt)  retention_7day_user_cnt,
        sum(retention_14day_user_cnt) retention_14day_user_cnt,
        sum(retention_30day_user_cnt) retention_30day_user_cnt,
        sum(retention_60day_user_cnt) retention_60day_user_cnt
        from ads_wechat_user_group_retention_iap_info_daily

        <include refid="condition"/>
        and group_id != 0
        group by tdate, app_name, appid, grp

        union all
        SELECT tdate,
        app_name, appid,
        case when group_id=1 then '新用户'
		     when group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7 then '轻度用户'
		     when group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14 then '中度用户'
		     when group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30 then '重度用户'
		     when group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45 then '超重度用户'
		     when group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60 then '轻硬核用户'
		     when group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90 then '硬核用户'
		     when group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120 then '超硬核用户'
		     when group_id<![CDATA[>=]]>121 then '骨灰级用户'
        else group_id end as grp,
        sum(user_cnt)                                        user_cnt,
        sum(iap_user_cnt)                                    iap_user_cnt,
        sum(iap_revenue)                                     iap_revenue,
        sum(retention_1day_user_cnt)  retention_1day_user_cnt,
        sum(retention_3day_user_cnt)  retention_3day_user_cnt,
        sum(retention_7day_user_cnt)  retention_7day_user_cnt,
        sum(retention_14day_user_cnt) retention_14day_user_cnt,
        sum(retention_30day_user_cnt) retention_30day_user_cnt,
        sum(retention_60day_user_cnt) retention_60day_user_cnt
        from ads_user_group_retention_iap_info_daily

        <include refid="condition"/>
        and group_id != 0
        group by tdate, app_name, appid, grp

        union all

        SELECT tdate,
        app_name, appid,
        case when group_id=1 then '新用户'
		     when group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7 then '轻度用户'
		     when group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14 then '中度用户'
		     when group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30 then '重度用户'
		     when group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45 then '超重度用户'
		     when group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60 then '轻硬核用户'
		     when group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90 then '硬核用户'
		     when group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120 then '超硬核用户'
		     when group_id<![CDATA[>=]]>121 then '骨灰级用户'
        else group_id end as grp,
        sum(user_cnt)                                        user_cnt,
        sum(iap_user_cnt)                                    iap_user_cnt,
        sum(iap_revenue)                                     iap_revenue,
        sum(retention_1day_user_cnt)  retention_1day_user_cnt,
        sum(retention_3day_user_cnt)  retention_3day_user_cnt,
        sum(retention_7day_user_cnt)  retention_7day_user_cnt,
        sum(retention_14day_user_cnt) retention_14day_user_cnt,
        sum(retention_30day_user_cnt) retention_30day_user_cnt,
        sum(retention_60day_user_cnt) retention_60day_user_cnt
        from ads_user_group_retention_iap_info_ios_daily

        <include refid="condition"/>
        and group_id != 0
        group by tdate, app_name, appid, grp
        ) a

        <if test="grpList != null and grpList.size > 0">
            where grp IN
            <foreach collection="grpList" item="grp" open="(" separator="," close=")">
                #{grp}
            </foreach>
        </if>

    </select>

    <select id="selectDateScheme6" resultType="com.wbgame.pojo.operate.UserCompositionAnalysisVO"
            parameterType="com.wbgame.pojo.operate.UserCompositionAnalysisDTO">


        select 
		tdate,
        grp,
        sum(user_cnt)                                                                            total_user_cnt,
        <include refid="avg_users_sql"/>
        cast(sum(iap_revenue) / 100 as decimal (18, 2))                                          total_income,
        concat(stay2, "%") stay2,
        concat(stay3, "%") stay3,
        concat(stay7, "%") stay7,
        concat(stay14, "%") stay14,
        concat(stay30, "%") stay30,
        concat(stay60, "%") stay60,
        concat(truncate(sum(iap_user_cnt) / sum(user_cnt) * 100, 2), "%")                        pay_rate,
        cast(sum(iap_revenue) / sum(user_cnt) / 100 as decimal (18, 2))  arpu,
        cast(ifnull(sum(iap_revenue) / sum(iap_user_cnt), 0) / 100 as decimal(18, 2))            arrpu
        from (
        SELECT 
		<choose>
           <when test='null!=group and group.contains("week")'>
              DATE_FORMAT(tdate,'%x-%v') tdate,
           </when>
           <when test='null!=group and group.contains("month")'>
             DATE_FORMAT(tdate,'%Y-%m') tdate,
           </when>
            <when test='null!=group and group.contains("year")'>
                DATE_FORMAT(tdate,'%Y') tdate,
            </when>
           <when test='null!=group and group.contains("tdate")'>
             tdate,
           </when>
           <otherwise>
           </otherwise>
     	</choose>
        case when group_id=0 then '整体'
        when group_id=1 then '新用户'
        when group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7 then '轻度用户'
        when group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14 then '中度用户'
        when group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30 then '重度用户'
        when group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45 then '超重度用户'
        when group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60 then '轻硬核用户'
        when group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90 then '硬核用户'
        when group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120 then '超硬核用户'
        when group_id<![CDATA[>=]]>121 then '骨灰级用户'
        else group_id end as grp,
        sum(user_cnt)                 user_cnt,
        truncate(sum(retention_1day_user_cnt) / sum(case when retention_1day_user_cnt>0 then user_cnt end ) * 100, 2)  stay2,
        truncate(sum(retention_3day_user_cnt) / sum(case when retention_3day_user_cnt>0 then user_cnt end) * 100, 2)  stay3,
        truncate(sum(retention_7day_user_cnt) / sum(case when retention_7day_user_cnt>0 then user_cnt end) * 100, 2)  stay7,
        truncate(sum(retention_14day_user_cnt) / sum(case when retention_14day_user_cnt>0 then user_cnt end) * 100, 2) stay14,
        truncate(sum(retention_30day_user_cnt) / sum(case when retention_30day_user_cnt>0 then user_cnt end) * 100, 2) stay30,
        truncate(sum(retention_60day_user_cnt) / sum(case when retention_60day_user_cnt>0 then user_cnt end) * 100, 2) stay60,
        sum(iap_revenue)              iap_revenue,
        sum(iap_user_cnt)             iap_user_cnt
        from ads_wechat_user_group_retention_iap_info_daily
        <include refid="condition"/>
        <choose>
           <when test='null!=group and group.contains("week")'>
              group by DATE_FORMAT(tdate,'%x-%v'), grp
           </when>
           <when test='null!=group and group.contains("month")'>
            group by DATE_FORMAT(tdate,'%Y-%m'), grp
           </when>
            <when test='null!=group and group.contains("year")'>
                group by DATE_FORMAT(tdate,'%Y'), grp
            </when>
           <when test='null!=group and group.contains("tdate")'>
            group by tdate, grp
           </when>
           <otherwise>
           </otherwise>
     	</choose>
        union all
        SELECT  
		<choose>
           <when test='null!=group and group.contains("week")'>
              DATE_FORMAT(tdate,'%x-%v') tdate,
           </when>
           <when test='null!=group and group.contains("month")'>
             DATE_FORMAT(tdate,'%Y-%m') tdate,
           </when>
            <when test='null!=group and group.contains("year")'>
                DATE_FORMAT(tdate,'%Y') tdate,
            </when>
           <when test='null!=group and group.contains("tdate")'>
            tdate,
           </when>
           <otherwise>
           </otherwise>
     	</choose>
        case when group_id=0 then '整体'
        when group_id=1 then '新用户'
        when group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7 then '轻度用户'
        when group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14 then '中度用户'
        when group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30 then '重度用户'
        when group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45 then '超重度用户'
        when group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60 then '轻硬核用户'
        when group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90 then '硬核用户'
        when group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120 then '超硬核用户'
        when group_id<![CDATA[>=]]>121 then '骨灰级用户'
        else group_id end as grp,
        sum(user_cnt)                 user_cnt,
        truncate(sum(retention_1day_user_cnt) / sum(case when retention_1day_user_cnt>0 then user_cnt end ) * 100, 2)  stay2,
        truncate(sum(retention_3day_user_cnt) / sum(case when retention_3day_user_cnt>0 then user_cnt end) * 100, 2)  stay3,
        truncate(sum(retention_7day_user_cnt) / sum(case when retention_7day_user_cnt>0 then user_cnt end) * 100, 2)  stay7,
        truncate(sum(retention_14day_user_cnt) / sum(case when retention_14day_user_cnt>0 then user_cnt end) * 100, 2) stay14,
        truncate(sum(retention_30day_user_cnt) / sum(case when retention_30day_user_cnt>0 then user_cnt end) * 100, 2) stay30,
        truncate(sum(retention_60day_user_cnt) / sum(case when retention_60day_user_cnt>0 then user_cnt end) * 100, 2) stay60,
        sum(iap_revenue)              iap_revenue,
        sum(iap_user_cnt)             iap_user_cnt
        from ads_user_group_retention_iap_info_daily
        <include refid="condition"/>
        <choose>
           <when test='null!=group and group.contains("week")'>
              group by DATE_FORMAT(tdate,'%x-%v'), grp
           </when>
           <when test='null!=group and group.contains("month")'>
            group by DATE_FORMAT(tdate,'%Y-%m'), grp
           </when>
            <when test='null!=group and group.contains("year")'>
                group by DATE_FORMAT(tdate,'%Y'), grp
            </when>
           <when test='null!=group and group.contains("tdate")'>
            group by tdate, grp
           </when>
           <otherwise>
           </otherwise>
     	</choose>
        union all
        SELECT 
		<choose>
           <when test='null!=group and group.contains("week")'>
              DATE_FORMAT(tdate,'%x-%v') tdate,
           </when>
           <when test='null!=group and group.contains("month")'>
             DATE_FORMAT(tdate,'%Y-%m') tdate,
           </when>
            <when test='null!=group and group.contains("year")'>
                DATE_FORMAT(tdate,'%Y') tdate,
            </when>
           <when test='null!=group and group.contains("tdate")'>
            tdate,
           </when>
           <otherwise>
           </otherwise>
     	</choose>
        case when group_id=0 then '整体'
        when group_id=1 then '新用户'
        when group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7 then '轻度用户'
        when group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14 then '中度用户'
        when group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30 then '重度用户'
        when group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45 then '超重度用户'
        when group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60 then '轻硬核用户'
        when group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90 then '硬核用户'
        when group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120 then '超硬核用户'
        when group_id<![CDATA[>=]]>121 then '骨灰级用户'
        else group_id end as grp,
        sum(user_cnt)                 user_cnt,
        truncate(sum(retention_1day_user_cnt) / sum(case when retention_1day_user_cnt>0 then user_cnt end ) * 100, 2)  stay2,
        truncate(sum(retention_3day_user_cnt) / sum(case when retention_3day_user_cnt>0 then user_cnt end) * 100, 2)  stay3,
        truncate(sum(retention_7day_user_cnt) / sum(case when retention_7day_user_cnt>0 then user_cnt end) * 100, 2)  stay7,
        truncate(sum(retention_14day_user_cnt) / sum(case when retention_14day_user_cnt>0 then user_cnt end) * 100, 2) stay14,
        truncate(sum(retention_30day_user_cnt) / sum(case when retention_30day_user_cnt>0 then user_cnt end) * 100, 2) stay30,
        truncate(sum(retention_60day_user_cnt) / sum(case when retention_60day_user_cnt>0 then user_cnt end) * 100, 2) stay60,
        sum(iap_revenue)              iap_revenue,
        sum(iap_user_cnt)             iap_user_cnt
        from ads_user_group_retention_iap_info_ios_daily
        <include refid="condition"/>
        <choose>
           <when test='null!=group and group.contains("week")'>
              group by DATE_FORMAT(tdate,'%x-%v'), grp
           </when>
           <when test='null!=group and group.contains("month")'>
            group by DATE_FORMAT(tdate,'%Y-%m'), grp
           </when>
            <when test='null!=group and group.contains("year")'>
                group by DATE_FORMAT(tdate,'%Y'), grp
            </when>
           <when test='null!=group and group.contains("tdate")'>
            group by tdate, grp
           </when>
           <otherwise>
           </otherwise>
     	</choose>
        ) a

        <if test="grpList != null and grpList.size > 0">
            where grp IN
            <foreach collection="grpList" item="grp" open="(" separator="," close=")">
                #{grp}
            </foreach>
        </if>
        group by tdate, grp
         order by tdate
    </select>

    <sql id="selectData2">
        SELECT 
		    <include refid="sql_tdate"/>
            app_name, appid,
            case when group_id=0 then '整体'
             when group_id=1 then '新用户'
		     when group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7 then '轻度用户'
		     when group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14 then '中度用户'
		     when group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30 then '重度用户'
		     when group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45 then '超重度用户'
		     when group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60 then '轻硬核用户'
		     when group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90 then '硬核用户'
		     when group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120 then '超硬核用户'
		     when group_id<![CDATA[>=]]>121 then '骨灰级用户'
            else group_id end as grp,
            sum(user_cnt) total_user_cnt,
            <include refid="avg_users_sql"/>
            sum(iap_user_cnt) total_iap_user,
            cast(sum(iap_revenue) / 100 as decimal (18, 2))                  total_income,
            truncate(sum(iap_user_cnt) / sum(user_cnt) * 100, 2) pay_rate,
            cast(sum(iap_revenue) / sum(user_cnt) / 100 as decimal (18, 2))  arpu,
            cast(sum(iap_revenue) / sum(iap_user_cnt) / 100 as decimal (18, 2))    arrpu,
            truncate(sum(retention_1day_user_cnt) / sum(case when retention_1day_user_cnt>0 then user_cnt end ) * 100, 2)  stay2,
            truncate(sum(retention_3day_user_cnt) / sum(case when retention_3day_user_cnt>0 then user_cnt end) * 100, 2)  stay3,
            truncate(sum(retention_7day_user_cnt) / sum(case when retention_7day_user_cnt>0 then user_cnt end) * 100, 2)  stay7,
            truncate(sum(retention_14day_user_cnt) / sum(case when retention_14day_user_cnt>0 then user_cnt end) * 100, 2) stay14,
            truncate(sum(retention_30day_user_cnt) / sum(case when retention_30day_user_cnt>0 then user_cnt end) * 100, 2) stay30,
            truncate(sum(retention_60day_user_cnt) / sum(case when retention_60day_user_cnt>0 then user_cnt end) * 100, 2) stay60

        from ads_wechat_user_group_retention_iap_info_daily
        <include refid="condition"/>
        <choose>
           <when test='null!=group and group.contains("week")'>
              group by DATE_FORMAT(tdate,'%x-%v'), app_name, appid, grp
           </when>
           <when test='null!=group and group.contains("month")'>
            group by DATE_FORMAT(tdate,'%Y-%m'), app_name, appid, grp
           </when>
            <when test='null!=group and group.contains("year")'>
                group by DATE_FORMAT(tdate,'%Y'), app_name, appid, grp
            </when>
           <when test='null!=group and group.contains("tdate")'>
            group by tdate, app_name, appid, grp
           </when>
           <otherwise>
           </otherwise>
     	</choose>

        union all
        SELECT 
		    <include refid="sql_tdate"/>
            app_name, appid,
        case when group_id=0 then '整体'
        when group_id=1 then '新用户'
        when group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7 then '轻度用户'
        when group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14 then '中度用户'
        when group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30 then '重度用户'
        when group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45 then '超重度用户'
        when group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60 then '轻硬核用户'
        when group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90 then '硬核用户'
        when group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120 then '超硬核用户'
        when group_id<![CDATA[>=]]>121 then '骨灰级用户'
        else group_id end as grp,
            sum(user_cnt) total_user_cnt,
            <include refid="avg_users_sql"/>
            sum(iap_user_cnt) total_iap_user,
            cast(sum(iap_revenue) / 100 as decimal (18, 2))                  total_income,
            truncate(sum(iap_user_cnt) / sum(user_cnt) * 100, 2) pay_rate,
            cast(sum(iap_revenue) / sum(user_cnt) / 100 as decimal (18, 2))  arpu,
            cast(sum(iap_revenue) / sum(iap_user_cnt) / 100 as decimal (18, 2))    arrpu,
            truncate(sum(retention_1day_user_cnt) / sum(case when retention_1day_user_cnt>0 then user_cnt end ) * 100, 2)  stay2,
            truncate(sum(retention_3day_user_cnt) / sum(case when retention_3day_user_cnt>0 then user_cnt end) * 100, 2)  stay3,
            truncate(sum(retention_7day_user_cnt) / sum(case when retention_7day_user_cnt>0 then user_cnt end) * 100, 2)  stay7,
            truncate(sum(retention_14day_user_cnt) / sum(case when retention_14day_user_cnt>0 then user_cnt end) * 100, 2) stay14,
            truncate(sum(retention_30day_user_cnt) / sum(case when retention_30day_user_cnt>0 then user_cnt end) * 100, 2) stay30,
            truncate(sum(retention_60day_user_cnt) / sum(case when retention_60day_user_cnt>0 then user_cnt end) * 100, 2) stay60
        from ads_user_group_retention_iap_info_daily

       <include refid="condition"/>
        
        <choose>
           <when test='null!=group and group.contains("week")'>
              group by DATE_FORMAT(tdate,'%x-%v'), app_name, appid, grp
           </when>
           <when test='null!=group and group.contains("month")'>
            group by DATE_FORMAT(tdate,'%Y-%m'), app_name, appid, grp
           </when>
            <when test='null!=group and group.contains("year")'>
                group by DATE_FORMAT(tdate,'%Y'), app_name, appid, grp
            </when>
           <when test='null!=group and group.contains("tdate")'>
            group by tdate, app_name, appid, grp
           </when>
           <otherwise>
           </otherwise>
     	</choose>
        union all

        SELECT
		    <include refid="sql_tdate"/>
            app_name, appid,
        case when group_id=0 then '整体'
        when group_id=1 then '新用户'
        when group_id<![CDATA[>=]]>2 and group_id<![CDATA[<=]]>7 then '轻度用户'
        when group_id<![CDATA[>=]]>8 and group_id<![CDATA[<=]]>14 then '中度用户'
        when group_id<![CDATA[>=]]>15 and group_id<![CDATA[<=]]>30 then '重度用户'
        when group_id<![CDATA[>=]]>31 and group_id<![CDATA[<=]]>45 then '超重度用户'
        when group_id<![CDATA[>=]]>46 and group_id<![CDATA[<=]]>60 then '轻硬核用户'
        when group_id<![CDATA[>=]]>61 and group_id<![CDATA[<=]]>90 then '硬核用户'
        when group_id<![CDATA[>=]]>91 and group_id<![CDATA[<=]]>120 then '超硬核用户'
        when group_id<![CDATA[>=]]>121 then '骨灰级用户'
        else group_id end as grp,
            sum(user_cnt) total_user_cnt,
            <include refid="avg_users_sql"/>
            sum(iap_user_cnt) total_iap_user,
            cast(sum(iap_revenue) / 100 as decimal (18, 2))                  total_income,
            truncate(sum(iap_user_cnt) / sum(user_cnt) * 100, 2) pay_rate,
            cast(sum(iap_revenue) / sum(user_cnt) / 100 as decimal (18, 2))  arpu,
            cast(sum(iap_revenue) / sum(iap_user_cnt) / 100 as decimal (18, 2))    arrpu,
            truncate(sum(retention_1day_user_cnt) / sum(case when retention_1day_user_cnt>0 then user_cnt end ) * 100, 2)  stay2,
            truncate(sum(retention_3day_user_cnt) / sum(case when retention_3day_user_cnt>0 then user_cnt end) * 100, 2)  stay3,
            truncate(sum(retention_7day_user_cnt) / sum(case when retention_7day_user_cnt>0 then user_cnt end) * 100, 2)  stay7,
            truncate(sum(retention_14day_user_cnt) / sum(case when retention_14day_user_cnt>0 then user_cnt end) * 100, 2) stay14,
            truncate(sum(retention_30day_user_cnt) / sum(case when retention_30day_user_cnt>0 then user_cnt end) * 100, 2) stay30,
            truncate(sum(retention_60day_user_cnt) / sum(case when retention_60day_user_cnt>0 then user_cnt end) * 100, 2) stay60

        from ads_user_group_retention_iap_info_ios_daily

        <include refid="condition"/>
        <choose>
           <when test='null!=group and group.contains("week")'>
              group by DATE_FORMAT(tdate,'%x-%v'), app_name, appid, grp
           </when>
           <when test='null!=group and group.contains("month")'>
            group by DATE_FORMAT(tdate,'%Y-%m'), app_name, appid, grp
           </when>
            <when test='null!=group and group.contains("year")'>
                group by DATE_FORMAT(tdate,'%Y'), app_name, appid, grp
            </when>
           <when test='null!=group and group.contains("tdate")'>
            group by tdate, app_name, appid, grp
           </when>
           <otherwise>
           </otherwise>
     	</choose>
    </sql>



    <!-- 活跃度用户在线时长分析  -->
    <sql id="selectOnlineDurationList">
		select tdate,app_name,appid,grp,
		       sum(active_user_cnt) active_user_cnt,
		       sum(active_day_avg_user) active_day_avg_user,
               cast(sum(avg_active_time*active_user_cnt)/sum(active_user_cnt) as decimal (10, 2)) avg_active_time,
               cast(avg(mid_active_time) as decimal (10, 2)) mid_active_time,
               cast(min(min_active_time) as decimal (10, 2)) min_active_time,
               cast(max(max_active_time) as decimal (10, 2)) max_active_time
           from (
		    select app_name,appid,
                <include refid="sql_tdate"/>
                case  when group_id=1 then '新用户'
                    when group_id = 0 then '整体'
                    when group_id=2 then '轻度用户'
                    when group_id=3 then '中度用户'
                    when group_id=4 then '重度用户'
                    when group_id=5 then '超重度用户'
                    when group_id=6 then '轻硬核用户'
                    when group_id=7 then '硬核用户'
                    when group_id=8 then '超硬核用户'
                    when group_id=9 then '骨灰级用户'
                    else group_id end as grp,
                        sum(active_user_cnt) active_user_cnt,cast(sum(active_user_cnt)/count(distinct tdate) as decimal (10, 0)) active_day_avg_user,
                        cast(sum(avg_active_time*active_user_cnt)/sum(active_user_cnt) as decimal (10, 2)) avg_active_time ,
                        cast(avg(mid_active_time) as decimal (10, 2)) mid_active_time ,
                        cast(min(min_active_time) as decimal (10, 2)) min_active_time ,
                        cast(max(max_active_time) as decimal (10, 2)) max_active_time ,
                        group_id
                from ads_wechat_user_group_active_time_analysis_daily
                where  tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
                   <include refid="sql_online_duration_group"/>
                union all
                select app_name,appid,
                <include refid="sql_tdate"/>
                case  when group_id=1 then '新用户'
                when group_id = 0 then '整体'
                when group_id=2 then '轻度用户'
                when group_id=3 then '中度用户'
                when group_id=4 then '重度用户'
                when group_id=5 then '超重度用户'
                when group_id=6 then '轻硬核用户'
                when group_id=7 then '硬核用户'
                when group_id=8 then '超硬核用户'
                when group_id=9 then '骨灰级用户'
                else group_id end as grp,
                sum(active_user_cnt) active_user_cnt,cast(sum(active_user_cnt)/count(distinct tdate) as decimal (10, 0)) active_day_avg_user,
                cast(sum(avg_active_time*active_user_cnt)/sum(active_user_cnt) as decimal (10, 2)) avg_active_time ,
                cast(avg(mid_active_time) as decimal (10, 2)) mid_active_time ,
                cast(min(min_active_time) as decimal (10, 2)) min_active_time ,
                cast(max(max_active_time) as decimal (10, 2)) max_active_time ,
                group_id
                from ads_user_group_active_time_analysis_daily
                where  tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
                <include refid="sql_online_duration_group"/>
                union all
                select app_name,appid,
                <include refid="sql_tdate"/>
                case  when group_id=1 then '新用户'
                when group_id = 0 then '整体'
                when group_id=2 then '轻度用户'
                when group_id=3 then '中度用户'
                when group_id=4 then '重度用户'
                when group_id=5 then '超重度用户'
                when group_id=6 then '轻硬核用户'
                when group_id=7 then '硬核用户'
                when group_id=8 then '超硬核用户'
                when group_id=9 then '骨灰级用户'
                else group_id end as grp,
                sum(active_user_cnt) active_user_cnt,cast(sum(active_user_cnt)/count(distinct tdate) as decimal (10, 0)) active_day_avg_user,
                cast(sum(avg_active_time*active_user_cnt)/sum(active_user_cnt) as decimal (10, 2)) avg_active_time ,
                cast(avg(mid_active_time) as decimal (10, 2)) mid_active_time ,
                cast(min(min_active_time) as decimal (10, 2)) min_active_time ,
                cast(max(max_active_time) as decimal (10, 2)) max_active_time ,
                group_id
                from ads_user_group_active_time_analysis_ios_daily
                where  tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
                <include refid="sql_online_duration_group"/>
        ) t1 group by t1.tdate,t1.appid,t1.grp
    </sql>

    <sql id="sql_tdate">
        <choose>
            <when test='null!=group and group.contains("week")'>
                DATE_FORMAT(tdate,'%x-%v') tdate,
            </when>
            <when test='null!=group and group.contains("month")'>
                DATE_FORMAT(tdate,'%Y-%m') tdate,
            </when>
            <when test='null!=group and group.contains("year")'>
                DATE_FORMAT(tdate,'%Y') tdate,
            </when>
            <otherwise>
                tdate,
            </otherwise>
        </choose>
    </sql>

    <sql id="sql_online_duration_group">
        group by appid,grp
        <if test="group != null and group != ''">
            <choose>
                <when test='null!=group and group.contains("week")'>
                    ,DATE_FORMAT(tdate,'%x-%v')
                </when>
                <when test='null!=group and group.contains("month")'>
                    ,DATE_FORMAT(tdate,'%Y-%m')
                </when>
                <when test='null!=group and group.contains("year")'>
                    ,DATE_FORMAT(tdate,'%Y')
                </when>
                <otherwise>
                    ,tdate
                </otherwise>
            </choose>
        </if>
    </sql>

</mapper>