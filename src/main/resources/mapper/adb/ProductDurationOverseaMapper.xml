<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.ProductDurationOverseaMapper">


    <select id="queryList" resultType="com.wbgame.pojo.adv2.bigdata.ProductDurationOverseaVo">
        SELECT
            <if test="group != null and group != ''">
                ${group},
            </if>
            sum( regs ) regs,
            sum( dau ) dau,
            sum(total) stay_time_cnt,
            round(sum( user_0_30s )/ sum(total) * 100, 2 ) user_0_30s,
            round(sum( user_30s_1m )/ sum(total) * 100, 2 ) user_30s_1m,
            round(sum( user_1_2m )/ sum(total) * 100, 2 ) user_1_2m,
            round(sum( user_2_3m )/ sum(total) * 100, 2 ) user_2_3m,
            round(sum( user_3_5m )/ sum(total) * 100, 2 ) user_3_5m,
            round(sum( user_5_8m )/ sum(total) * 100, 2 ) user_5_8m,
            round(sum( user_8_10m )/ sum(total) * 100, 2 ) user_8_10m,
            round(sum( user_10m_plus )/ sum(total) * 100, 2 ) user_10m_plus
        FROM (
             SELECT
                 a.tdate,
                 a.appid,
                 a.channel,
                 b.regs,
                 b.dau,
                 a.user_0_30s,
                 a.user_30s_1m,
                 a.user_1_2m,
                 a.user_2_3m,
                 a.user_3_5m,
                 a.user_5_8m,
                 a.user_8_10m,
                 a.user_10m_plus,
                 a.total
             FROM
                 (
                 SELECT
                     tdate,
                     appid,
                     channel,
                     sum( user_0_30s ) user_0_30s,
                     sum( user_30s_1m ) user_30s_1m,
                     sum( user_1_2m ) user_1_2m,
                     sum( user_2_3m ) user_2_3m,
                     sum( user_3_5m ) user_3_5m,
                     sum( user_5_8m ) user_5_8m,
                     sum( user_8_10m ) user_8_10m,
                     sum( user_10m_plus ) user_10m_plus,
                     (sum( user_0_30s ) + sum( user_30s_1m ) + sum( user_1_2m ) + sum( user_2_3m ) + sum( user_3_5m ) + sum( user_5_8m ) + sum( user_8_10m ) + sum( user_10m_plus )) total
                 FROM
                     `ads_product_user_duration_distribution_daily_oversea`
                <where>
                    <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                        tdate between #{start_date} and #{end_date}
                    </if>
                    <if test="appid != null and appid != ''">
                        and appid in (${appid})
                    </if>
                    <if test="channel != null and channel != ''">
                        and channel in (${channel})
                    </if>
                    <if test="app_category != null and app_category != ''">
                        and appid in (SELECT id FROM app_info where app_category in (${app_category}))
                    </if>
                </where>
                 GROUP BY
                     tdate,
                     appid,
                     channel
                 ) a
                 LEFT JOIN (
                 SELECT
                     tdate,
                     appid,
                     download_channel,
                     sum( new_users ) regs,
                     sum( act_users ) dau
                 FROM
                     ads_dim_users_info_3d_hourly_oversea
                 <where>
                     <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                         tdate between #{start_date} and #{end_date}
                     </if>
                     <if test="appid != null and appid != ''">
                         and appid in (${appid})
                     </if>
                     <if test="channel != null and channel != ''">
                         and download_channel in (${channel})
                     </if>
                    <if test="app_category != null and app_category != ''">
                        and appid in (SELECT id FROM app_info where app_category in (${app_category}))
                    </if>
                 </where>
                 GROUP BY
                     tdate,
                     appid,
                     download_channel
             ) b ON a.tdate = b.tdate
                 AND a.appid = b.appid
                 AND a.channel = b.download_channel
             ) a
            <choose>
                <when test="group != null and group != ''">
                    group by ${group}
                </when>
                <otherwise>
                    HAVING sum(dau) IS NOT NULL
                </otherwise>
            </choose>
             <if test="order_str != null and order_str != ''">
                 order by ${order_str}
             </if>
    </select>
    <select id="queryTotal" resultType="com.wbgame.pojo.adv2.bigdata.ProductDurationOverseaVo">
        SELECT
            ifnull(sum(b.regs),0) regs,
            ifnull(sum(b.dau),0) dau,
            ifnull(sum(total),0) stay_time_cnt,
            ifnull(round(sum(a.user_0_30s)/sum(a.total) * 100,2),0.0) user_0_30s,
            ifnull(round(sum(a.user_30s_1m)/sum(a.total) * 100,2),0.0) user_30s_1m,
            ifnull(round(sum(a.user_1_2m)/sum(a.total) * 100,2),0.0) user_1_2m,
            ifnull(round(sum(a.user_2_3m)/sum(a.total) * 100,2),0.0) user_2_3m,
            ifnull(round(sum(a.user_3_5m)/sum(a.total) * 100,2),0.0) user_3_5m,
            ifnull(round(sum(a.user_5_8m)/sum(a.total) * 100,2),0.0) user_5_8m,
            ifnull(round(sum(a.user_8_10m)/sum(a.total) * 100,2),0.0) user_8_10m,
            ifnull(round(sum(a.user_10m_plus)/sum(a.total) * 100,2),0.0) user_10m_plus
        FROM
        (
            SELECT
            tdate,
            appid,
            channel,
            sum( user_0_30s ) user_0_30s,
            sum( user_30s_1m ) user_30s_1m,
            sum( user_1_2m ) user_1_2m,
            sum( user_2_3m ) user_2_3m,
            sum( user_3_5m ) user_3_5m,
            sum( user_5_8m ) user_5_8m,
            sum( user_8_10m ) user_8_10m,
            sum( user_10m_plus ) user_10m_plus,
            (sum( user_0_30s ) + sum( user_30s_1m ) + sum( user_1_2m ) + sum( user_2_3m ) + sum( user_3_5m ) + sum( user_5_8m ) + sum( user_8_10m ) + sum( user_10m_plus )) total
        FROM
            `ads_product_user_duration_distribution_daily_oversea`
        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                tdate between #{start_date} and #{end_date}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="app_category != null and app_category != ''">
                and appid in (SELECT id FROM app_info where app_category in (${app_category}))
            </if>
        </where>
        GROUP BY
            tdate,
            appid,
            channel
        ) a
        LEFT JOIN (
        SELECT
            tdate,
            appid,
            download_channel,
            sum( new_users ) regs,
            sum( act_users ) dau
        FROM
            ads_dim_users_info_3d_hourly_oversea
        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                tdate between #{start_date} and #{end_date}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and download_channel in (${channel})
            </if>
            <if test="app_category != null and app_category != ''">
                and appid in (SELECT id FROM app_info where app_category in (${app_category}))
            </if>
        </where>
        GROUP BY
            tdate,
            appid,
            download_channel
            ) b ON a.tdate = b.tdate
            AND a.appid = b.appid
            AND a.channel = b.download_channel
    </select>
</mapper>