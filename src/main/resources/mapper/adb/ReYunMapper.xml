<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.ReYunMapper">


	<select id="selectFtPayList" resultType="com.wbgame.pojo.ChannelSdExtensionPayVo">
		select #{year} year,#{month} month,#{tdate} day,app_id appid,media,ifnull(channel,'') channel,p_channel ztchannel,REPLACE(FORMAT(sum(rebateCost)/100,2),',','') rebateSpend from ads_campaign_reyun
		where day between #{startTime} and #{endTime}  and add_user > 0 and rebateCost > 0 and type = 0 and p_channel in
		(<foreach collection="list" separator="," item="it">
		#{it.chaId}
	</foreach>)
		GROUP BY app_id,channel,p_channel
	</select>

</mapper>
