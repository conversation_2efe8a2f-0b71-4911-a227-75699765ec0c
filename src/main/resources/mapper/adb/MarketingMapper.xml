<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.MarketingMapper">

	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>
	
	<select id="selectIncomeRatio" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_incomeRatio_sql"/>
	</select>
	<select id="selectIncomeRatioSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
			*
		from (<include refid="dn_incomeRatio_sql"/>) xx
	</select>
	<sql id="dn_incomeRatio_sql">
		select 
			${group_str},
			app_name,
			
			SUM(aa.dau) dau,
			SUM(aa.reg_user_cnt) reg_user_cnt,
			CONCAT(TRUNCATE(SUM(aa.reg_user_cnt)/SUM(aa.dau)*100, 2),'%') reg_user_rate,
			TRUNCATE(CONCAT(SUM(aa.in_ad_revenue)/100,'')+CONCAT(SUM(aa.out_ad_revenue)/100,''),2) sum_ad_revenue,
			TRUNCATE(CONCAT(SUM(aa.in_ad_revenue)/100,''),2) in_ad_revenue,
			TRUNCATE(CONCAT(SUM(aa.out_ad_revenue)/100,''),2) out_ad_revenue,
			
			CONCAT(TRUNCATE( CONCAT(SUM(aa.in_ad_revenue),'')/(SUM(aa.in_ad_revenue)+SUM(aa.out_ad_revenue))*100, 2),'%') in_ad_revenue_rate,
			CONCAT(TRUNCATE( CONCAT(SUM(aa.out_ad_revenue),'')/(SUM(aa.in_ad_revenue)+SUM(aa.out_ad_revenue))*100, 2),'%') out_ad_revenue_rate,
			TRUNCATE(CONCAT(SUM(aa.in_ad_revenue)/100,'')/SUM(aa.dau), 2) in_arpu,
			TRUNCATE(CONCAT(SUM(aa.out_ad_revenue)/100,'')/SUM(aa.dau), 2) out_arpu,
			TRUNCATE(CONCAT(SUM(aa.in_ad_revenue)/100+SUM(aa.out_ad_revenue)/100,'')/SUM(aa.dau), 2) all_arpu,
		
			TRUNCATE(CONCAT(SUM(aa.out_ad_revenue)/100,'')/SUM(aa.out_dau), 2) out_ad_revenue_arpu,
			CONCAT(TRUNCATE(SUM(aa.out_dau)/SUM(aa.dau)*100, 2),'%') out_dau_rate,
		
			IFNULL(TRUNCATE( CONCAT(SUM(aa.in_splash_revenue)/100,'') / SUM(aa.in_splash_cnt)*1000, 2),0) in_splash_ecpm,
			IFNULL(TRUNCATE( CONCAT(SUM(aa.in_plaque_revenue)/100,'') / SUM(aa.in_plaque_cnt)*1000, 2),0) in_plaque_ecpm,
			IFNULL(TRUNCATE( CONCAT(SUM(aa.in_banner_revenue)/100,'') / SUM(aa.in_banner_cnt)*1000, 2),0) in_banner_ecpm,
			IFNULL(TRUNCATE( CONCAT(SUM(aa.in_video_revenue)/100,'') / SUM(aa.in_video_cnt)*1000, 2),0) in_video_ecpm,
			IFNULL(TRUNCATE( CONCAT(SUM(aa.in_msg_revenue)/100,'') / SUM(aa.in_msg_cnt)*1000, 2),0) in_msg_ecpm,
		
			IFNULL(TRUNCATE( CONCAT(SUM(aa.out_splash_revenue)/100,'') / SUM(aa.out_splash_cnt)*1000, 2),0) out_splash_ecpm,
			IFNULL(TRUNCATE( CONCAT(SUM(aa.out_plaque_revenue)/100,'') / SUM(aa.out_plaque_cnt)*1000, 2),0) out_plaque_ecpm,
			IFNULL(TRUNCATE( CONCAT(SUM(aa.out_banner_revenue)/100,'') / SUM(aa.out_banner_cnt)*1000, 2),0) out_banner_ecpm,
			IFNULL(TRUNCATE( CONCAT(SUM(aa.out_video_revenue)/100,'') / SUM(aa.out_video_cnt)*1000, 2),0) out_video_ecpm,
			IFNULL(TRUNCATE( CONCAT(SUM(aa.out_msg_revenue)/100,'') / SUM(aa.out_msg_cnt)*1000, 2),0) out_msg_ecpm,
			
			IFNULL(TRUNCATE(SUM(aa.in_splash_cnt)/SUM(aa.dau), 2),0) in_splash_perpv,
			IFNULL(TRUNCATE(SUM(aa.in_plaque_cnt)/SUM(aa.dau), 2),0) in_plaque_perpv,
			IFNULL(TRUNCATE(SUM(aa.in_banner_cnt)/SUM(aa.dau), 2),0) in_banner_perpv,
			IFNULL(TRUNCATE(SUM(aa.in_video_cnt)/SUM(aa.dau), 2),0) in_video_perpv,
			IFNULL(TRUNCATE(SUM(aa.in_msg_cnt)/SUM(aa.dau), 2),0) in_msg_perpv,
			
			
			IFNULL(TRUNCATE(SUM(aa.out_splash_cnt)/SUM(aa.dau), 2),0) out_splash_perpv,
			IFNULL(TRUNCATE(SUM(aa.out_plaque_cnt)/SUM(aa.dau), 2),0) out_plaque_perpv,
			IFNULL(TRUNCATE(SUM(aa.out_banner_cnt)/SUM(aa.dau), 2),0) out_banner_perpv,
			IFNULL(TRUNCATE(SUM(aa.out_video_cnt)/SUM(aa.dau), 2),0) out_video_perpv,
			IFNULL(TRUNCATE(SUM(aa.out_msg_cnt)/SUM(aa.dau), 2),0) out_msg_perpv,
			
			IFNULL(TRUNCATE(SUM(aa.in_splash_cnt)/SUM(aa.in_dau), 2),0) in_splash_seeppv,
			IFNULL(TRUNCATE(SUM(aa.in_plaque_cnt)/SUM(aa.in_dau), 2),0) in_plaque_seeppv,
			IFNULL(TRUNCATE(SUM(aa.in_banner_cnt)/SUM(aa.in_dau), 2),0) in_banner_seeppv,
			IFNULL(TRUNCATE(SUM(aa.in_video_cnt)/SUM(aa.in_dau), 2),0) in_video_seeppv,
			IFNULL(TRUNCATE(SUM(aa.in_msg_cnt)/SUM(aa.in_dau), 2),0) in_msg_seeppv,
			
			
			IFNULL(TRUNCATE(SUM(aa.out_splash_cnt)/SUM(aa.out_dau), 2),0) out_splash_seeppv,
			IFNULL(TRUNCATE(SUM(aa.out_plaque_cnt)/SUM(aa.out_dau), 2),0) out_plaque_seeppv,
			IFNULL(TRUNCATE(SUM(aa.out_banner_cnt)/SUM(aa.out_dau), 2),0) out_banner_seeppv,
			IFNULL(TRUNCATE(SUM(aa.out_video_cnt)/SUM(aa.out_dau), 2),0) out_video_seeppv,
			IFNULL(TRUNCATE(SUM(aa.out_msg_cnt)/SUM(aa.out_dau), 2),0) out_msg_seeppv

		from dnwx_bi.ads_market_user_in_out_revenue_daily aa 
		where tdate BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="channel_type != null and channel_type != ''">
			and channel_type in (${channel_type})
		</if>
		<if test="ad_buy_media != null and ad_buy_media != ''">
			and ad_buy_media in (${ad_buy_media})
		</if>
		<if test="download_channel != null and download_channel != ''">
			and download_channel in (${download_channel})
		</if>
		<if test="pid != null and pid != ''">
			and pid in (${pid})
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>

		group by ${group_str} 
		<if test="order_str != null and order_str != ''">
			order by ${order_str} 
		</if>
		
	</sql>
	
	
</mapper>