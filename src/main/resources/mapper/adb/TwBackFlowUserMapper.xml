<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.TwBackFlowUserMapper">

    <select id="queryList" resultType="com.wbgame.pojo.adv2.bigdata.TwBackFlowUserVo">
        select
            a.tdate,
            a.appid,
            a.app,
            a.channel,
            a.cnt,
            a.user_cnt,
            a.total_user_cnt,
            ifnull(CAST(round(a.rate * 100, 2) AS DECIMAL(10,2)),0.00) AS rate
        from dnwx_bi.ads_tool_wangzhuan_tw_backflow_distribution_daily a
        left join dnwx_bi.app_info b on a.appid = b.id
        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                tdate between #{start_date} and #{end_date}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="two_app_category != null and two_app_category != ''">
                and b.two_app_category in ${two_app_category}
            </if>
        </where>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>
    <select id="queryTotal" resultType="com.wbgame.pojo.adv2.bigdata.TwBackFlowUserVo">
        select sum(a.cnt) cnt,
               sum(a.user_cnt) user_cnt,
               sum(a.total_user_cnt) total_user_cnt,
               CONCAT(ifnull(CAST(round(a.rate * 100, 2) AS DECIMAL(10,2)),0.00), '%') AS rate
        from dnwx_bi.ads_tool_wangzhuan_tw_backflow_distribution_daily a
        left join dnwx_bi.app_info b on a.appid = b.id
        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                tdate between #{start_date} and #{end_date}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="two_app_category != null and two_app_category != ''">
                and b.two_app_category in ${two_app_category}
            </if>
        </where>
    </select>


</mapper>