<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.UserRangeTwoMapper">

	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>

	
	<!-- 活跃用户展示频次、价值分析、ECPM分析 -->
	<select id="selectDnAdshowActRange" parameterType="java.lang.String" resultType="java.util.Map">
		
		SELECT tdate,appid, '0' prjid,IFNULL(download_channel,'') channel,active_type,ad_type adpos_type,
		       dau actnum,reg_user_cnt addnum,ad_active_user aduser,global_ad_active_user global_aduser,
			ad_pv_active_user_pv_0 pv0,
			ad_pv_active_user_pv_1 pv1,
			ad_pv_active_user_pv_2 pv2,
			ad_pv_active_user_pv_3 pv3,
			ad_pv_active_user_pv_4 pv4,
			ad_pv_active_user_pv_5 pv5,
			ad_pv_active_user_pv_6 pv6,
			ad_pv_active_user_pv_7 pv7,
			ad_pv_active_user_pv_8 pv8,
			ad_pv_active_user_pv_9 pv9,
			ad_pv_active_user_pv_10 pv10,
			ad_pv_active_user_pv_11_15 pv11,
			ad_pv_active_user_pv_16_20 pv12,
			ad_pv_active_user_pv_21_25 pv13,
			ad_pv_active_user_pv_gt_25 pv14
-- 		       ,
-- 			user_label
		FROM dnwx_bi.ads_active_user_adjusted_revenue_ecpm_pv_analyze_daily
		where tdate='${tdate}'
		
	</select>
	
	<select id="selectDnAdRevenueActRange" parameterType="java.lang.String" resultType="java.util.Map">
		
		SELECT tdate,appid,IFNULL(download_channel,'') cha_id,'0' adsid,dau actnum,reg_user_cnt addnum,
		       active_type,ad_type adpos_type,'0' ecpm,
			ad_active_user_revenue_pv_1/100 pv1,
			ad_active_user_revenue_pv_2/100 pv2,
			ad_active_user_revenue_pv_3/100 pv3,
			ad_active_user_revenue_pv_4/100 pv4,
			ad_active_user_revenue_pv_5/100 pv5,
			ad_active_user_revenue_pv_6/100 pv6,
			ad_active_user_revenue_pv_7/100 pv7,
			ad_active_user_revenue_pv_8/100 pv8,
			ad_active_user_revenue_pv_9/100 pv9,
			ad_active_user_revenue_pv_10/100 pv10,
			ad_active_user_revenue_pv_11_15/100 pv11,
			ad_active_user_revenue_pv_16_20/100 pv12,
			ad_active_user_revenue_pv_21_25/100 pv13,
			ad_active_user_revenue_pv_gt_25/100 pv14
-- 		       ,
-- 			pid prjid,
-- 			user_label
		FROM dnwx_bi.ads_active_user_adjusted_revenue_ecpm_pv_analyze_daily 
		where tdate='${tdate}' 
		
	</select>
	
	<select id="selectDnAdEcpmActRange" parameterType="java.lang.String" resultType="java.util.Map">
		
		SELECT tdate,appid,IFNULL(download_channel,'') cha_id,'0' adsid,dau actnum,reg_user_cnt addnum,active_type,ad_type adpos_type,'0' ecpm,
			ad_active_user_pv_1 pv1,
			ad_active_user_pv_2 pv2,
			ad_active_user_pv_3 pv3,
			ad_active_user_pv_4 pv4,
			ad_active_user_pv_5 pv5,
			ad_active_user_pv_6 pv6,
			ad_active_user_pv_7 pv7,
			ad_active_user_pv_8 pv8,
			ad_active_user_pv_9 pv9,
			ad_active_user_pv_10 pv10,
			ad_active_user_pv_11_15 pv11,
			ad_active_user_pv_16_20 pv12,
			ad_active_user_pv_21_25 pv13,
			ad_active_user_pv_gt_25 pv14,
		
			ad_active_user_revenue_pv_1/100 ar1,
			ad_active_user_revenue_pv_2/100 ar2,
			ad_active_user_revenue_pv_3/100 ar3,
			ad_active_user_revenue_pv_4/100 ar4,
			ad_active_user_revenue_pv_5/100 ar5,
			ad_active_user_revenue_pv_6/100 ar6,
			ad_active_user_revenue_pv_7/100 ar7,
			ad_active_user_revenue_pv_8/100 ar8,
			ad_active_user_revenue_pv_9/100 ar9,
			ad_active_user_revenue_pv_10/100 ar10,
			ad_active_user_revenue_pv_11_15/100 ar11,
			ad_active_user_revenue_pv_16_20/100 ar12,
			ad_active_user_revenue_pv_21_25/100 ar13,
			ad_active_user_revenue_pv_gt_25/100 ar14
-- 		       ,
-- 			pid prjid,
-- 			user_label
		
		FROM dnwx_bi.ads_active_user_adjusted_revenue_ecpm_pv_analyze_daily 
		where tdate='${tdate}' 
		
	</select>
	
	<!-- 新增用户展示频次、价值分析、ECPM分析 -->
	<select id="selectDnAdshowAddRange" parameterType="java.lang.String" resultType="java.util.Map">
		
		SELECT tdate,appid, '0' prjid,download_channel channel,reg_user_cnt addnum,'0' adsid,ad_type adpos_type,
			ad_pv_1 pv1,
			ad_pv_2 pv2,
			ad_pv_3 pv3,
			ad_pv_4 pv4,
			ad_pv_5 pv5,
			ad_pv_6 pv6,
			ad_pv_7 pv7
-- 				,
-- 			   user_label
		FROM dnwx_bi.ads_reg_user_adjusted_ltv_ecpm_pv_analyze_daily
		where tdate='${tdate}'
		
	</select>
	
	<select id="selectDnAdRevenueAddRange" parameterType="java.lang.String" resultType="java.util.Map">
		
		SELECT tdate,appid,download_channel cha_id,'0' adsid,ad_type adpos_type,reg_user_cnt addnum,'0' ecpm,
			add_revenue_arpu_1/100 ar1,
			add_revenue_arpu_2/100 ar2,
			add_revenue_arpu_3/100 ar3,
			add_revenue_arpu_4/100 ar4,
			add_revenue_arpu_5/100 ar5,
			add_revenue_arpu_6/100 ar6,
			add_revenue_arpu_7/100 ar7
-- 		       ,
-- 			pid prjid,
-- 			user_label
		
		FROM dnwx_bi.ads_reg_user_adjusted_ltv_ecpm_pv_analyze_daily 
		where tdate='${tdate}'
		
	</select>
	
	<select id="selectDnAdEcpmAddRange" parameterType="java.lang.String" resultType="java.util.Map">
		
		SELECT tdate,appid,download_channel cha_id,'0' adsid,ad_type adpos_type,reg_user_cnt addnum,'0' ecpm,
			ad_pv_1 pv1,
			ad_pv_2 pv2,
			ad_pv_3 pv3,
			ad_pv_4 pv4,
			ad_pv_5 pv5,
			ad_pv_6 pv6,
			ad_pv_7 pv7,
			add_revenue_arpu_1/100 ar1,
			add_revenue_arpu_2/100 ar2,
			add_revenue_arpu_3/100 ar3,
			add_revenue_arpu_4/100 ar4,
			add_revenue_arpu_5/100 ar5,
			add_revenue_arpu_6/100 ar6,
			add_revenue_arpu_7/100 ar7
-- 		       ,
-- 		    pid prjid,
-- 		    user_label

		FROM dnwx_bi.ads_reg_user_adjusted_ltv_ecpm_pv_analyze_daily 
		where tdate='${tdate}'
		
	</select>
	
</mapper>