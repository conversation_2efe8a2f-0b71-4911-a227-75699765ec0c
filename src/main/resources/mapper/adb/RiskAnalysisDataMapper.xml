<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.RiskAnalysisMapper">


	<select id="getRegRiskAnalysis" resultType="com.wbgame.pojo.advert.RiskAnalysisRegDataVo" parameterType="java.util.Map">
		select
		tdate,appid,app_name,download_channel,reg_user_cnt,
		high_risk_user_cnt,lower_risk_user_cnt,no_risk_user_cnt,
		unidentification_user_cnt,default_user_cnt,shop_user_cnt,league_user_cnt,other_user_cnt,

		concat(IFNULL(ROUND((high_risk_user_cnt/reg_user_cnt)*100,2),0.00),'%') high_risk_user_cnt_ratio,
		concat(IFNULL(ROUND((lower_risk_user_cnt/reg_user_cnt)*100,2),0.00),'%') lower_risk_user_cnt_ratio,
		concat(IFNULL(ROUND((no_risk_user_cnt/reg_user_cnt)*100,2),0.00),'%') no_risk_user_cnt_ratio,

		concat(IFNULL(ROUND((unidentification_user_cnt/reg_user_cnt)*100,2),0.00),'%') unidentification_user_cnt_ratio,
		concat(IFNULL(ROUND((default_user_cnt/reg_user_cnt)*100,2),0.00),'%') default_user_cnt_ratio,
		concat(IFNULL(ROUND((shop_user_cnt/reg_user_cnt)*100,2),0.00),'%') shop_user_cnt_ratio,
		concat(IFNULL(ROUND((league_user_cnt/reg_user_cnt)*100,2),0.00),'%') league_user_cnt_ratio,
		concat(IFNULL(ROUND((other_user_cnt/reg_user_cnt)*100,2),0.00),'%') other_user_cnt_ratio

		from dnwx_bi.ads_reg_user_risk_management_daily where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="download_channel != null and download_channel != ''">
			and `download_channel` in (${download_channel})
		</if>
		and `tdate` <![CDATA[>=]]> #{start_date} and `tdate` <![CDATA[<=]]> #{end_date}
		group by tdate,appid,download_channel
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate desc
			</otherwise>
		</choose>
	</select>

	<select id="getRegRiskAnalysisSum" resultType="com.wbgame.pojo.advert.RiskAnalysisRegDataVo" parameterType="java.util.Map">
		select
		sum(reg_user_cnt) reg_user_cnt,sum(high_risk_user_cnt) high_risk_user_cnt,
		sum(lower_risk_user_cnt) lower_risk_user_cnt,sum(no_risk_user_cnt) no_risk_user_cnt,
		sum(unidentification_user_cnt) unidentification_user_cnt,sum(default_user_cnt) default_user_cnt,
		sum(shop_user_cnt) shop_user_cnt,sum(league_user_cnt) league_user_cnt,
		sum(other_user_cnt) other_user_cnt,

		concat(IFNULL(ROUND((sum(high_risk_user_cnt)/sum(reg_user_cnt))*100,2),0.00),'%') high_risk_user_cnt_ratio,
		concat(IFNULL(ROUND((sum(lower_risk_user_cnt)/sum(reg_user_cnt))*100,2),0.00),'%') lower_risk_user_cnt_ratio,
		concat(IFNULL(ROUND((sum(no_risk_user_cnt)/sum(reg_user_cnt))*100,2),0.00),'%') no_risk_user_cnt_ratio,

		concat(IFNULL(ROUND((sum(unidentification_user_cnt)/sum(reg_user_cnt))*100,2),0.00),'%') unidentification_user_cnt_ratio,
		concat(IFNULL(ROUND((sum(default_user_cnt)/sum(reg_user_cnt))*100,2),0.00),'%') default_user_cnt_ratio,
		concat(IFNULL(ROUND((sum(shop_user_cnt)/sum(reg_user_cnt))*100,2),0.00),'%') shop_user_cnt_ratio,
		concat(IFNULL(ROUND((sum(league_user_cnt)/sum(reg_user_cnt))*100,2),0.00),'%') league_user_cnt_ratio,
		concat(IFNULL(ROUND((sum(other_user_cnt)/sum(reg_user_cnt))*100,2),0.00),'%') other_user_cnt_ratio

		from dnwx_bi.ads_reg_user_risk_management_daily where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="download_channel != null and download_channel != ''">
			and `download_channel` in (${download_channel})
		</if>
		and `tdate` <![CDATA[>=]]> #{start_date} and `tdate` <![CDATA[<=]]> #{end_date}
	</select>



	<select id="getActiveRiskAnalysis" resultType="com.wbgame.pojo.advert.RiskAnalysisActiveDataVo" parameterType="java.util.Map">
		select
		tdate,appid,app_name,download_channel,dau,
		high_risk_user_cnt,lower_risk_user_cnt,no_risk_user_cnt,
		unidentification_user_cnt,default_user_cnt,shop_user_cnt,league_user_cnt,other_user_cnt,

		concat(IFNULL(ROUND((high_risk_user_cnt/dau)*100,2),0.00),'%') high_risk_user_cnt_ratio,
		concat(IFNULL(ROUND((lower_risk_user_cnt/dau)*100,2),0.00),'%') lower_risk_user_cnt_ratio,
		concat(IFNULL(ROUND((no_risk_user_cnt/dau)*100,2),0.00),'%') no_risk_user_cnt_ratio,

		concat(IFNULL(ROUND((unidentification_user_cnt/dau)*100,2),0.00),'%') unidentification_user_cnt_ratio,
		concat(IFNULL(ROUND((default_user_cnt/dau)*100,2),0.00),'%') default_user_cnt_ratio,
		concat(IFNULL(ROUND((shop_user_cnt/dau)*100,2),0.00),'%') shop_user_cnt_ratio,
		concat(IFNULL(ROUND((league_user_cnt/dau)*100,2),0.00),'%') league_user_cnt_ratio,
		concat(IFNULL(ROUND((other_user_cnt/dau)*100,2),0.00),'%') other_user_cnt_ratio

		from dnwx_bi.ads_active_user_risk_management_daily where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="download_channel != null and download_channel != ''">
			and `download_channel` in (${download_channel})
		</if>
		and `tdate` <![CDATA[>=]]> #{start_date} and `tdate` <![CDATA[<=]]> #{end_date}
		group by tdate,appid,download_channel
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate desc
			</otherwise>
		</choose>
	</select>

	<select id="getActiveRiskAnalysisSum" resultType="com.wbgame.pojo.advert.RiskAnalysisActiveDataVo" parameterType="java.util.Map">
		select
		sum(dau) dau,sum(high_risk_user_cnt) high_risk_user_cnt,
		sum(lower_risk_user_cnt) lower_risk_user_cnt,sum(no_risk_user_cnt) no_risk_user_cnt,
		sum(unidentification_user_cnt) unidentification_user_cnt,sum(default_user_cnt) default_user_cnt,
		sum(shop_user_cnt) shop_user_cnt,sum(league_user_cnt) league_user_cnt,
		sum(other_user_cnt) other_user_cnt,

		concat(IFNULL(ROUND((sum(high_risk_user_cnt)/sum(dau))*100,2),0.00),'%') high_risk_user_cnt_ratio,
		concat(IFNULL(ROUND((sum(lower_risk_user_cnt)/sum(dau))*100,2),0.00),'%') lower_risk_user_cnt_ratio,
		concat(IFNULL(ROUND((sum(no_risk_user_cnt)/sum(dau))*100,2),0.00),'%') no_risk_user_cnt_ratio,

		concat(IFNULL(ROUND((sum(unidentification_user_cnt)/sum(dau))*100,2),0.00),'%') unidentification_user_cnt_ratio,
		concat(IFNULL(ROUND((sum(default_user_cnt)/sum(dau))*100,2),0.00),'%') default_user_cnt_ratio,
		concat(IFNULL(ROUND((sum(shop_user_cnt)/sum(dau))*100,2),0.00),'%') shop_user_cnt_ratio,
		concat(IFNULL(ROUND((sum(league_user_cnt)/sum(dau))*100,2),0.00),'%') league_user_cnt_ratio,
		concat(IFNULL(ROUND((sum(other_user_cnt)/sum(dau))*100,2),0.00),'%') other_user_cnt_ratio

		from dnwx_bi.ads_active_user_risk_management_daily where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="download_channel != null and download_channel != ''">
			and `download_channel` in (${download_channel})
		</if>
		and `tdate` <![CDATA[>=]]> #{start_date} and `tdate` <![CDATA[<=]]> #{end_date}
	</select>

	<select id="getRatioRiskAnalysis" resultType="com.wbgame.pojo.advert.RiskAnalysisRatioDataVo" parameterType="java.util.Map">
		select
		tdate,`appid`,app_name,`download_channel`,`user_source`,`user_active_type`,
		concat(IFNULL(ROUND(sum(high_risk_user_cnt)/(sum(high_risk_user_cnt)+sum(lower_risk_user_cnt)+sum(no_risk_user_cnt))*100,2),0.00),'%') high_risk_user_cnt_ratio,
		concat(IFNULL(ROUND(sum(lower_risk_user_cnt)/(sum(high_risk_user_cnt)+sum(lower_risk_user_cnt)+sum(no_risk_user_cnt))*100,2),0.00),'%') lower_risk_user_cnt_ratio,
		concat(IFNULL(ROUND(sum(no_risk_user_cnt)/(sum(high_risk_user_cnt)+sum(lower_risk_user_cnt)+sum(no_risk_user_cnt))*100,2),0.00),'%') no_risk_user_cnt_ratio
		from dnwx_bi.ads_risk_user_proportion_daily where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="download_channel != null and download_channel != ''">
			and `download_channel` in (${download_channel})
		</if>
		<if test="user_source != null and user_source != ''">
			and `user_source` =  #{user_source}
		</if>
		<if test="user_active_type != null and user_active_type != ''">
			and `user_active_type` =  #{user_active_type}
		</if>
		and `tdate` <![CDATA[>=]]> #{start_date} and `tdate` <![CDATA[<=]]> #{end_date}
		group by tdate,`appid`,`download_channel`,`user_source`,`user_active_type`
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate desc
			</otherwise>
		</choose>
	</select>

	<select id="getRatioRiskAnalysisSum" resultType="com.wbgame.pojo.advert.RiskAnalysisRatioDataVo" parameterType="java.util.Map">
		select
		concat(IFNULL(ROUND(sum(high_risk_user_cnt)/(sum(high_risk_user_cnt)+sum(lower_risk_user_cnt)+sum(no_risk_user_cnt))*100,2),0.00),'%') high_risk_user_cnt_ratio,
		concat(IFNULL(ROUND(sum(lower_risk_user_cnt)/(sum(high_risk_user_cnt)+sum(lower_risk_user_cnt)+sum(no_risk_user_cnt))*100,2),0.00),'%') lower_risk_user_cnt_ratio,
		concat(IFNULL(ROUND(sum(no_risk_user_cnt)/(sum(high_risk_user_cnt)+sum(lower_risk_user_cnt)+sum(no_risk_user_cnt))*100,2),0.00),'%') no_risk_user_cnt_ratio
		from dnwx_bi.ads_risk_user_proportion_daily where 1=1
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="download_channel != null and download_channel != ''">
			and `download_channel` in (${download_channel})
		</if>
		<if test="user_source != null and user_source != ''">
			and `user_source` =  #{user_source}
		</if>
		<if test="user_active_type != null and user_active_type != ''">
			and `user_active_type` =  #{user_active_type}
		</if>
		and `tdate` <![CDATA[>=]]> #{start_date} and `tdate` <![CDATA[<=]]> #{end_date}
	</select>

</mapper>
