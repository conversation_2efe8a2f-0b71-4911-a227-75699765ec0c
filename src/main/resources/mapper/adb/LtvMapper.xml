<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.LtvMapper">


    <select id="getLtvMediaList" resultType="com.wbgame.pojo.game.report.LtvMediaReportVo"
            parameterType="com.wbgame.pojo.game.report.LtvMediaReportVo">

            <include refid="selectMediaListDayOrWeekOrMonth"/>

    </select>

    <sql id="selectMediaList">
        SELECT
        <if test="group != null and group != ''">
            ${group},
        </if>
        sum(reg_users) reg_users,
        round(SUM(LTV1),2) revenue1,
        round(sum(LTV1)/sum(reg_users)/100,2) origin_ltv1,
        round(sum(LTV1)/sum(LTV1),2) ltv1,
        round(sum(LTV2)/sum(LTV1),2) ltv2,
        round(sum(LTV3)/sum(LTV1),2) ltv3,
        round(sum(LTV4)/sum(LTV1),2) ltv4,
        round(sum(LTV5)/sum(LTV1),2) ltv5,
        round(sum(LTV6)/sum(LTV1),2) ltv6,
        round(sum(LTV7)/sum(LTV1),2) ltv7,
        round(sum(LTV8)/sum(LTV1),2) ltv8,
        round(sum(LTV9)/sum(LTV1),2) ltv9,
        round(sum(LTV10)/sum(LTV1),2) ltv10,
        round(sum(LTV11)/sum(LTV1),2) ltv11,
        round(sum(LTV12)/sum(LTV1),2) ltv12,
        round(sum(LTV13)/sum(LTV1),2) ltv13,
        round(sum(LTV14)/sum(LTV1),2) ltv14,
        round(sum(LTV15)/sum(LTV1),2) ltv15,
        round(sum(LTV16)/sum(LTV1),2) ltv16,
        round(sum(LTV17)/sum(LTV1),2) ltv17,
        round(sum(LTV18)/sum(LTV1),2) ltv18,
        round(sum(LTV19)/sum(LTV1),2) ltv19,
        round(sum(LTV20)/sum(LTV1),2) ltv20,
        round(sum(LTV21)/sum(LTV1),2) ltv21,
        round(sum(LTV22)/sum(LTV1),2) ltv22,
        round(sum(LTV23)/sum(LTV1),2) ltv23,
        round(sum(LTV24)/sum(LTV1),2) ltv24,
        round(sum(LTV25)/sum(LTV1),2) ltv25,
        round(sum(LTV26)/sum(LTV1),2) ltv26,
        round(sum(LTV27)/sum(LTV1),2) ltv27,
        round(sum(LTV28)/sum(LTV1),2) ltv28,
        round(sum(LTV29)/sum(LTV1),2) ltv29,
        round(sum(LTV30)/sum(LTV1),2) ltv30,
        round(sum(LTV36)/sum(LTV1),2) ltv36,
        round(sum(LTV42)/sum(LTV1),2) ltv42,
        round(sum(LTV48)/sum(LTV1),2) ltv48,
        round(sum(LTV54)/sum(LTV1),2) ltv54,
        round(sum(LTV60)/sum(LTV1),2) ltv60,


        round(sum(LTV75)/sum(LTV1),2) ltv75,
        round(sum(LTV90)/sum(LTV1),2) ltv90,
        round(sum(LTV105)/sum(LTV1),2) ltv105,
        round(sum(LTV120)/sum(LTV1),2) ltv120,
        round(sum(LTV135)/sum(LTV1),2) ltv135,
        round(sum(LTV150)/sum(LTV1),2) ltv150,
        round(sum(LTV165)/sum(LTV1),2) ltv165,
        round(sum(LTV180)/sum(LTV1),2) ltv180,
        round(sum(LTV195)/sum(LTV1),2) ltv195,
        round(sum(LTV210)/sum(LTV1),2) ltv210,
        round(sum(LTV225)/sum(LTV1),2) ltv225,
        round(sum(LTV240)/sum(LTV1),2) ltv240,
        round(sum(LTV255)/sum(LTV1),2) ltv255,
        round(sum(LTV270)/sum(LTV1),2) ltv270,
        round(sum(LTV285)/sum(LTV1),2) ltv285,
        round(sum(LTV300)/sum(LTV1),2) ltv300,
        round(sum(LTV315)/sum(LTV1),2) ltv315,
        round(sum(LTV330)/sum(LTV1),2) ltv330,
        round(sum(LTV345)/sum(LTV1),2) ltv345,
        round(sum(LTV360)/sum(LTV1),2) ltv360,


        round((sum(LTV14)/sum(LTV1))/(sum(LTV7)/sum(LTV1)),2) ltv14_7,
        round((sum(LTV21)/sum(LTV1))/(sum(LTV14)/sum(LTV1)),2) ltv21_7,
        round((sum(LTV28)/sum(LTV1))/(sum(LTV21)/sum(LTV1)),2) ltv28_7,
        round((sum(LTV30)/sum(LTV1))/(sum(LTV28)/sum(LTV1)),2) ltv30_7,
        round((sum(LTV36)/sum(LTV1))/(sum(LTV30)/sum(LTV1)),2) ltv36_7,
        round((sum(LTV48)/sum(LTV1))/(sum(LTV36)/sum(LTV1)),2) ltv48_7,
        round((sum(LTV60)/sum(LTV1))/(sum(LTV48)/sum(LTV1)),2) ltv60_7,

        round((sum(LTV75)/sum(LTV1))/(sum(LTV60)/sum(LTV1)),2) ltv75_7,
        round((sum(LTV90)/sum(LTV1))/(sum(LTV75)/sum(LTV1)),2) ltv90_7,
        round((sum(LTV105)/sum(LTV1))/(sum(LTV90)/sum(LTV1)),2) ltv105_7,
        round((sum(LTV120)/sum(LTV1))/(sum(LTV105)/sum(LTV1)),2) ltv120_7,
        round((sum(LTV135)/sum(LTV1))/(sum(LTV120)/sum(LTV1)),2) ltv135_7,
        round((sum(LTV150)/sum(LTV1))/(sum(LTV135)/sum(LTV1)),2) ltv150_7,
        round((sum(LTV165)/sum(LTV1))/(sum(LTV150)/sum(LTV1)),2) ltv165_7,
        round((sum(LTV180)/sum(LTV1))/(sum(LTV165)/sum(LTV1)),2) ltv180_7,
        round((sum(LTV195)/sum(LTV1))/(sum(LTV180)/sum(LTV1)),2) ltv195_7,
        round((sum(LTV210)/sum(LTV1))/(sum(LTV195)/sum(LTV1)),2) ltv210_7,
        round((sum(LTV225)/sum(LTV1))/(sum(LTV210)/sum(LTV1)),2) ltv225_7,
        round((sum(LTV240)/sum(LTV1))/(sum(LTV225)/sum(LTV1)),2) ltv240_7,
        round((sum(LTV255)/sum(LTV1))/(sum(LTV240)/sum(LTV1)),2) ltv255_7,
        round((sum(LTV270)/sum(LTV1))/(sum(LTV255)/sum(LTV1)),2) ltv270_7,
        round((sum(LTV285)/sum(LTV1))/(sum(LTV270)/sum(LTV1)),2) ltv285_7,
        round((sum(LTV300)/sum(LTV1))/(sum(LTV285)/sum(LTV1)),2) ltv300_7,
        round((sum(LTV315)/sum(LTV1))/(sum(LTV300)/sum(LTV1)),2) ltv315_7,
        round((sum(LTV330)/sum(LTV1))/(sum(LTV315)/sum(LTV1)),2) ltv330_7,
        round((sum(LTV345)/sum(LTV1))/(sum(LTV330)/sum(LTV1)),2) ltv345_7,
        round((sum(LTV360)/sum(LTV1))/(sum(LTV345)/sum(LTV1)),2) ltv360_7

        FROM ( <include refid="selectMediaListDetail"/>) a where 1=1
        <if test="reg_users != null and reg_users != ''">
            and reg_users >= #{reg_users}
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc , origin_ltv1 desc
            </otherwise>
        </choose>
    </sql>

    <sql id="selectMediaListDayOrWeekOrMonth">
        SELECT
        <if test="group != null and group != ''">
            ${group},
        </if>
        sum(reg_users) reg_users,
        round(SUM(LTV1),2) revenue1,
        round(sum(LTV1)/sum(reg_users)/100,2) origin_ltv1,
        round(sum(LTV1/LTV1)/count(if(LTV1/LTV1>0,1,null)>0),2) ltv1,
        round(sum(LTV2/LTV1)/count(if(LTV2/LTV1>0,1,null)>0),2) ltv2,
        round(sum(LTV3/LTV1)/count(if(LTV3/LTV1>0,1,null)>0),2) ltv3,
        round(sum(LTV4/LTV1)/count(if(LTV4/LTV1>0,1,null)>0),2) ltv4,
        round(sum(LTV5/LTV1)/count(if(LTV5/LTV1>0,1,null)>0),2) ltv5,
        round(sum(LTV6/LTV1)/count(if(LTV6/LTV1>0,1,null)>0),2) ltv6,
        round(sum(LTV7/LTV1)/count(if(LTV7/LTV1>0,1,null)>0),2) ltv7,
        round(sum(LTV8/LTV1)/count(if(LTV8/LTV1>0,1,null)>0),2) ltv8,
        round(sum(LTV9/LTV1)/count(if(LTV9/LTV1>0,1,null)>0),2) ltv9,
        round(sum(LTV10/LTV1)/count(if(LTV10/LTV1>0,1,null)>0),2)ltv10,
        round(sum(LTV11/LTV1)/count(if(LTV11/LTV1>0,1,null)>0),2) ltv11,
        round(sum(LTV12/LTV1)/count(if(LTV12/LTV1>0,1,null)>0),2) ltv12,
        round(sum(LTV13/LTV1)/count(if(LTV13/LTV1>0,1,null)>0),2) ltv13,
        round(sum(LTV14/LTV1)/count(if(LTV14/LTV1>0,1,null)>0),2) ltv14,
        round(sum(LTV15/LTV1)/count(if(LTV15/LTV1>0,1,null)>0),2) ltv15,
        round(sum(LTV16/LTV1)/count(if(LTV16/LTV1>0,1,null)>0),2) ltv16,
        round(sum(LTV17/LTV1)/count(if(LTV17/LTV1>0,1,null)>0),2) ltv17,
        round(sum(LTV18/LTV1)/count(if(LTV18/LTV1>0,1,null)>0),2) ltv18,
        round(sum(LTV19/LTV1)/count(if(LTV19/LTV1>0,1,null)>0),2) ltv19,
        round(sum(LTV20/LTV1)/count(if(LTV20/LTV1>0,1,null)>0),2) ltv20,
        round(sum(LTV21/LTV1)/count(if(LTV21/LTV1>0,1,null)>0),2) ltv21,
        round(sum(LTV22/LTV1)/count(if(LTV22/LTV1>0,1,null)>0),2) ltv22,
        round(sum(LTV23/LTV1)/count(if(LTV23/LTV1>0,1,null)>0),2) ltv23,
        round(sum(LTV24/LTV1)/count(if(LTV24/LTV1>0,1,null)>0),2) ltv24,
        round(sum(LTV25/LTV1)/count(if(LTV25/LTV1>0,1,null)>0),2) ltv25,
        round(sum(LTV26/LTV1)/count(if(LTV26/LTV1>0,1,null)>0),2) ltv26,
        round(sum(LTV27/LTV1)/count(if(LTV27/LTV1>0,1,null)>0),2) ltv27,
        round(sum(LTV28/LTV1)/count(if(LTV28/LTV1>0,1,null)>0),2) ltv28,
        round(sum(LTV29/LTV1)/count(if(LTV29/LTV1>0,1,null)>0),2) ltv29,
        round(sum(LTV30/LTV1)/count(if(LTV30/LTV1>0,1,null)>0),2) ltv30,
        round(sum(LTV36/LTV1)/count(if(LTV36/LTV1>0,1,null)>0),2) ltv36,
        round(sum(LTV42/LTV1)/count(if(LTV42/LTV1>0,1,null)>0),2) ltv42,
        round(sum(LTV48/LTV1)/count(if(LTV48/LTV1>0,1,null)>0),2) ltv48,
        round(sum(LTV54/LTV1)/count(if(LTV54/LTV1>0,1,null)>0),2) ltv54,
        round(sum(LTV60/LTV1)/count(if(LTV60/LTV1>0,1,null)>0),2) ltv60,


        round(sum(LTV75/LTV1)/count(if(LTV75/LTV1>0,1,null)>0),2) ltv75,
        round(sum(LTV90/LTV1)/count(if(LTV90/LTV1>0,1,null)>0),2) ltv90,
        round(sum(LTV105/LTV1)/count(if(LTV105/LTV1>0,1,null)>0),2) ltv105,
        round(sum(LTV120/LTV1)/count(if(LTV120/LTV1>0,1,null)>0),2) ltv120,
        round(sum(LTV135/LTV1)/count(if(LTV135/LTV1>0,1,null)>0),2) ltv135,
        round(sum(LTV150/LTV1)/count(if(LTV150/LTV1>0,1,null)>0),2) ltv150,
        round(sum(LTV165/LTV1)/count(if(LTV165/LTV1>0,1,null)>0),2) ltv165,
        round(sum(LTV180/LTV1)/count(if(LTV180/LTV1>0,1,null)>0),2) ltv180,
        round(sum(LTV195/LTV1)/count(if(LTV195/LTV1>0,1,null)>0),2) ltv195,
        round(sum(LTV210/LTV1)/count(if(LTV210/LTV1>0,1,null)>0),2) ltv210,
        round(sum(LTV225/LTV1)/count(if(LTV225/LTV1>0,1,null)>0),2) ltv225,
        round(sum(LTV240/LTV1)/count(if(LTV240/LTV1>0,1,null)>0),2) ltv240,
        round(sum(LTV255/LTV1)/count(if(LTV255/LTV1>0,1,null)>0),2) ltv255,
        round(sum(LTV270/LTV1)/count(if(LTV270/LTV1>0,1,null)>0),2) ltv270,
        round(sum(LTV285/LTV1)/count(if(LTV285/LTV1>0,1,null)>0),2) ltv285,
        round(sum(LTV300/LTV1)/count(if(LTV300/LTV1>0,1,null)>0),2) ltv300,
        round(sum(LTV315/LTV1)/count(if(LTV315/LTV1>0,1,null)>0),2) ltv315,
        round(sum(LTV330/LTV1)/count(if(LTV330/LTV1>0,1,null)>0),2) ltv330,
        round(sum(LTV345/LTV1)/count(if(LTV345/LTV1>0,1,null)>0),2) ltv345,
        round(sum(LTV360/LTV1)/count(if(LTV360/LTV1>0,1,null)>0),2) ltv360,


        round(sum(LTV14/LTV1)/count(if(LTV14/LTV1>0,1,null)>0),2)
        /round(sum(LTV7/LTV1)/count(if(LTV7/LTV1>0,1,null)>0),2) ltv14_7,

        round(sum(LTV21/LTV1)/count(if(LTV21/LTV1>0,1,null)>0),2)
        /round(sum(LTV14/LTV1)/count(if(LTV14/LTV1>0,1,null)>0),2) ltv21_7,

        round(sum(LTV28/LTV1)/count(if(LTV28/LTV1>0,1,null)>0),2)
        /round(sum(LTV21/LTV1)/count(if(LTV21/LTV1>0,1,null)>0),2) ltv28_7,

        round(sum(LTV30/LTV1)/count(if(LTV30/LTV1>0,1,null)>0),2)
        /round(sum(LTV28/LTV1)/count(if(LTV28/LTV1>0,1,null)>0),2) ltv30_7,

        round(sum(LTV36/LTV1)/count(if(LTV36/LTV1>0,1,null)>0),2)
        /round(sum(LTV30/LTV1)/count(if(LTV30/LTV1>0,1,null)>0),2) ltv36_7,

        round(sum(LTV48/LTV1)/count(if(LTV48/LTV1>0,1,null)>0),2)
        /round(sum(LTV36/LTV1)/count(if(LTV36/LTV1>0,1,null)>0),2) ltv48_7,

        round(sum(LTV60/LTV1)/count(if(LTV60/LTV1>0,1,null)>0),2)
        /round(sum(LTV48/LTV1)/count(if(LTV48/LTV1>0,1,null)>0),2) ltv60_7,

        round(sum(LTV75/LTV1)/count(if(LTV75/LTV1>0,1,null)>0),2)
        /round(sum(LTV60/LTV1)/count(if(LTV60/LTV1>0,1,null)>0),2) ltv75_7,

        round(sum(LTV90/LTV1)/count(if(LTV90/LTV1>0,1,null)>0),2)
        /round(sum(LTV75/LTV1)/count(if(LTV75/LTV1>0,1,null)>0),2) ltv90_7,

        round(sum(LTV105/LTV1)/count(if(LTV105/LTV1>0,1,null)>0),2)
        /round(sum(LTV90/LTV1)/count(if(LTV90/LTV1>0,1,null)>0),2) ltv105_7,

        round(sum(LTV120/LTV1)/count(if(LTV120/LTV1>0,1,null)>0),2)
        /round(sum(LTV105/LTV1)/count(if(LTV105/LTV1>0,1,null)>0),2) ltv120_7,

        round(sum(LTV135/LTV1)/count(if(LTV135/LTV1>0,1,null)>0),2)
        /round(sum(LTV120/LTV1)/count(if(LTV120/LTV1>0,1,null)>0),2) ltv135_7,

        round(sum(LTV150/LTV1)/count(if(LTV150/LTV1>0,1,null)>0),2)
        /round(sum(LTV135/LTV1)/count(if(LTV135/LTV1>0,1,null)>0),2) ltv150_7,


        round(sum(LTV165/LTV1)/count(if(LTV165/LTV1>0,1,null)>0),2)
        /round(sum(LTV150/LTV1)/count(if(LTV150/LTV1>0,1,null)>0),2) ltv165_7,


        round(sum(LTV180/LTV1)/count(if(LTV180/LTV1>0,1,null)>0),2)
        /round(sum(LTV165/LTV1)/count(if(LTV165/LTV1>0,1,null)>0),2) ltv180_7,


        round(sum(LTV195/LTV1)/count(if(LTV195/LTV1>0,1,null)>0),2)
        /round(sum(LTV180/LTV1)/count(if(LTV180/LTV1>0,1,null)>0),2) ltv195_7,

        round(sum(LTV210/LTV1)/count(if(LTV210/LTV1>0,1,null)>0),2)
        /round(sum(LTV195/LTV1)/count(if(LTV195/LTV1>0,1,null)>0),2) ltv210_7,


        round(sum(LTV225/LTV1)/count(if(LTV225/LTV1>0,1,null)>0),2)
        /round(sum(LTV210/LTV1)/count(if(LTV210/LTV1>0,1,null)>0),2) ltv225_7,


        round(sum(LTV240/LTV1)/count(if(LTV240/LTV1>0,1,null)>0),2)
        /round(sum(LTV225/LTV1)/count(if(LTV225/LTV1>0,1,null)>0),2) ltv240_7,

        round(sum(LTV255/LTV1)/count(if(LTV255/LTV1>0,1,null)>0),2)
        /round(sum(LTV240/LTV1)/count(if(LTV240/LTV1>0,1,null)>0),2) ltv255_7,

        round(sum(LTV270/LTV1)/count(if(LTV270/LTV1>0,1,null)>0),2)
        /round(sum(LTV255/LTV1)/count(if(LTV255/LTV1>0,1,null)>0),2) ltv270_7,

        round(sum(LTV285/LTV1)/count(if(LTV285/LTV1>0,1,null)>0),2)
        /round(sum(LTV270/LTV1)/count(if(LTV270/LTV1>0,1,null)>0),2) ltv285_7,

        round(sum(LTV300/LTV1)/count(if(LTV300/LTV1>0,1,null)>0),2)
        /round(sum(LTV285/LTV1)/count(if(LTV285/LTV1>0,1,null)>0),2) ltv300_7,

        round(sum(LTV315/LTV1)/count(if(LTV315/LTV1>0,1,null)>0),2)
        /round(sum(LTV300/LTV1)/count(if(LTV300/LTV1>0,1,null)>0),2) ltv315_7,

        round(sum(LTV330/LTV1)/count(if(LTV330/LTV1>0,1,null)>0),2)
        /round(sum(LTV315/LTV1)/count(if(LTV315/LTV1>0,1,null)>0),2) ltv330_7,

        round(sum(LTV345/LTV1)/count(if(LTV345/LTV1>0,1,null)>0),2)
        /round(sum(LTV330/LTV1)/count(if(LTV330/LTV1>0,1,null)>0),2) ltv345_7,

        round(sum(LTV360/LTV1)/count(if(LTV360/LTV1>0,1,null)>0),2)
        /round(sum(LTV345/LTV1)/count(if(LTV345/LTV1>0,1,null)>0),2) ltv360_7

        FROM ( <include refid="selectMediaListDetail"/>) a where 1=1
        <if test="reg_users != null and reg_users != ''">
            and reg_users >= #{reg_users}
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc , origin_ltv1 desc
            </otherwise>
        </choose>
    </sql>



    <select id="getLtvMediaSum" resultType="com.wbgame.pojo.game.report.LtvMediaReportVo"
            parameterType="com.wbgame.pojo.game.report.LtvMediaReportVo">
        select
        SUM(reg_users) reg_users,
        round(avg(origin_ltv1),2) origin_ltv1,
        round(sum(ltv1)/count(ltv1>0), 2) ltv1,
        round(sum(ltv2)/count(ltv2>0), 2) ltv2,
        round(sum(ltv3)/count(ltv3>0), 2) ltv3,
        round(sum(ltv4)/count(ltv4>0), 2) ltv4,
        round(sum(ltv5)/count(ltv5>0), 2) ltv5,
        round(sum(ltv6)/count(ltv6>0), 2) ltv6,
        round(sum(ltv7)/count(ltv7>0), 2) ltv7,
        round(sum(ltv8)/count(ltv8>0), 2) ltv8,
        round(sum(ltv9)/count(ltv9>0), 2) ltv9,
        round(sum(ltv10)/count(ltv10>0), 2) ltv10,
        round(sum(ltv11)/count(ltv11>0), 2) ltv11,
        round(sum(ltv12)/count(ltv12>0), 2) ltv12,
        round(sum(ltv13)/count(ltv13>0), 2) ltv13,
        round(sum(ltv14)/count(ltv14>0), 2) ltv14,
        round(sum(ltv15)/count(ltv15>0), 2) ltv15,
        round(sum(ltv16)/count(ltv16>0), 2) ltv16,
        round(sum(ltv17)/count(ltv17>0), 2) ltv17,
        round(sum(ltv18)/count(ltv18>0), 2) ltv18,
        round(sum(ltv19)/count(ltv19>0), 2) ltv19,
        round(sum(ltv20)/count(ltv20>0), 2) ltv20,
        round(sum(ltv21)/count(ltv21>0), 2) ltv21,
        round(sum(ltv22)/count(ltv22>0), 2) ltv22,
        round(sum(ltv23)/count(ltv23>0), 2) ltv23,
        round(sum(ltv24)/count(ltv24>0), 2) ltv24,
        round(sum(ltv25)/count(ltv25>0), 2) ltv25,
        round(sum(ltv26)/count(ltv26>0), 2) ltv26,
        round(sum(ltv27)/count(ltv27>0), 2) ltv27,
        round(sum(ltv28)/count(ltv28>0), 2) ltv28,
        round(sum(ltv29)/count(ltv29>0), 2) ltv29,
        round(sum(ltv30)/count(ltv30>0), 2) ltv30,
        round(sum(ltv36)/count(ltv36>0), 2) ltv36,
        round(sum(ltv42)/count(ltv42>0), 2) ltv42,
        round(sum(ltv48)/count(ltv48>0), 2) ltv48,
        round(sum(ltv54)/count(ltv54>0), 2) ltv54,
        round(sum(ltv60)/count(ltv60>0), 2) ltv60,
        round(sum(ltv75)/count(ltv75>0), 2) ltv75,
        round(sum(ltv90)/count(ltv90>0), 2) ltv90,
        round(sum(ltv105)/count(ltv105>0), 2) ltv105,
        round(sum(ltv120)/count(ltv120>0), 2) ltv120,
        round(sum(ltv135)/count(ltv135>0), 2) ltv135,
        round(sum(ltv150)/count(ltv150>0), 2) ltv150,
        round(sum(ltv165)/count(ltv165>0), 2) ltv165,
        round(sum(ltv180)/count(ltv180>0), 2) ltv180,
        round(sum(ltv195)/count(ltv195>0), 2) ltv195,
        round(sum(ltv210)/count(ltv210>0), 2) ltv210,
        round(sum(ltv225)/count(ltv225>0), 2) ltv225,
        round(sum(ltv240)/count(ltv240>0), 2) ltv240,
        round(sum(ltv255)/count(ltv255>0), 2) ltv255,
        round(sum(ltv270)/count(ltv270>0), 2) ltv270,
        round(sum(ltv285)/count(ltv285>0), 2) ltv285,
        round(sum(ltv300)/count(ltv300>0), 2) ltv300,
        round(sum(ltv315)/count(ltv315>0), 2) ltv315,
        round(sum(ltv330)/count(ltv330>0), 2) ltv330,
        round(sum(ltv345)/count(ltv345>0), 2) ltv345,
        round(sum(ltv360)/count(ltv360>0), 2) ltv360




        from (
            <include refid="selectMediaListDayOrWeekOrMonth"/>
        ) a
    </select>

    <select id="getLtvMediaMax" resultType="com.wbgame.pojo.game.report.LtvMediaReportVo"
            parameterType="com.wbgame.pojo.game.report.LtvMediaReportVo">
        select
        max(reg_users) reg_users,
        round(max(origin_ltv1),2) origin_ltv1,
        round(max(ltv1), 2) ltv1,
        round(max(ltv2), 2) ltv2,
        round(max(ltv3), 2) ltv3,
        round(max(ltv4), 2) ltv4,
        round(max(ltv5), 2) ltv5,
        round(max(ltv6), 2) ltv6,
        round(max(ltv7), 2) ltv7,
        round(max(ltv8), 2) ltv8,
        round(max(ltv9), 2) ltv9,
        round(max(ltv10), 2) ltv10,
        round(max(ltv11), 2) ltv11,
        round(max(ltv12), 2) ltv12,
        round(max(ltv13), 2) ltv13,
        round(max(ltv14), 2) ltv14,
        round(max(ltv15), 2) ltv15,
        round(max(ltv16), 2) ltv16,
        round(max(ltv17), 2) ltv17,
        round(max(ltv18), 2) ltv18,
        round(max(ltv19), 2) ltv19,

        round(max(ltv20), 2) ltv20,
        round(max(ltv21), 2) ltv21,
        round(max(ltv22), 2) ltv22,
        round(max(ltv23), 2) ltv23,
        round(max(ltv24), 2) ltv24,
        round(max(ltv25), 2) ltv25,
        round(max(ltv26), 2) ltv26,
        round(max(ltv27), 2) ltv27,
        round(max(ltv28), 2) ltv28,
        round(max(ltv29), 2) ltv29,
        round(max(ltv30), 2) ltv30,
        round(max(ltv36), 2) ltv36,
        round(max(ltv42), 2) ltv42,
        round(max(ltv48), 2) ltv48,
        round(max(ltv54), 2) ltv54,
        round(max(ltv60), 2) ltv60,

        round(max(ltv75), 2) ltv75,
        round(max(ltv90), 2) ltv90,
        round(max(ltv105), 2) ltv105,

        round(max(ltv120), 2) ltv120,
        round(max(ltv135), 2) ltv135,
        round(max(ltv150), 2) ltv150,
        round(max(ltv165), 2) ltv165,
        round(max(ltv180), 2) ltv180,
        round(max(ltv195), 2) ltv195,
        round(max(ltv210), 2) ltv210,
        round(max(ltv225), 2) ltv225,
        round(max(ltv240), 2) ltv240,
        round(max(ltv255), 2) ltv255,
        round(max(ltv270), 2) ltv270,
        round(max(ltv285), 2) ltv285,
        round(max(ltv300), 2) ltv300,
        round(max(ltv315), 2) ltv315,
        round(max(ltv330), 2) ltv330,
        round(max(ltv345), 2) ltv345,
        round(max(ltv360), 2) ltv360,

        round(max(ltv14_7), 2) ltv14_7,
        round(max(ltv21_7), 2) ltv21_7,
        round(max(ltv28_7), 2) ltv28_7,
        round(max(ltv30_7), 2) ltv30_7,
        round(max(ltv36_7), 2) ltv36_7,
        round(max(ltv48_7), 2) ltv48_7,
        round(max(ltv60_7), 2) ltv60_7,
        round(max(ltv75_7), 2) ltv75_7,
        round(max(ltv90_7), 2) ltv90_7,
        round(max(ltv105_7), 2) ltv105_7,
        round(max(ltv120_7), 2) ltv120_7,
        round(max(ltv135_7), 2) ltv135_7,
        round(max(ltv150_7), 2) ltv150_7,
        round(max(ltv165_7), 2) ltv165_7,
        round(max(ltv180_7), 2) ltv180_7,
        round(max(ltv195_7), 2) ltv195_7,
        round(max(ltv210_7), 2) ltv210_7,
        round(max(ltv225_7), 2) ltv225_7,
        round(max(ltv240_7), 2) ltv240_7,
        round(max(ltv255_7), 2) ltv255_7,
        round(max(ltv270_7), 2) ltv270_7,
        round(max(ltv285_7), 2) ltv285_7,
        round(max(ltv300_7), 2) ltv300_7,
        round(max(ltv315_7), 2) ltv315_7,
        round(max(ltv330_7), 2) ltv330_7,
        round(max(ltv345_7), 2) ltv345_7,
        round(max(ltv360_7), 2) ltv360_7

        from (
            <include refid="selectMediaListDayOrWeekOrMonth"/>
        ) a
    </select>




    <sql id="selectMediaListDetail">
        select
        <choose>
            <when test="summary == 1">
                tdate,
            </when>
            <when test="summary == 2">
                date_format(tdate, '%Y-%u') tdate,
            </when>
            <when test="summary == 3">
                date_format(tdate, '%Y-%m') tdate,
            </when>
            <otherwise>
                tdate,
            </otherwise>
        </choose>
        appid,media,reg_users,
        LTV1,LTV2,LTV3,LTV4,LTV5,LTV6,LTV7,LTV8,LTV9,LTV10,
        LTV11,LTV12,LTV13,LTV14,LTV15,LTV16,LTV17,LTV18,LTV19,LTV20,
        LTV21,LTV22,LTV23,LTV24,LTV25,LTV26,LTV27,LTV28,LTV29,LTV30,
        LTV36,LTV42,LTV48,LTV54,LTV60,LTV75,LTV90,LTV105,LTV120,LTV135,LTV150,
        LTV165,LTV180,LTV195,LTV210,LTV225,LTV240,LTV255,LTV270,LTV285,LTV300,
        LTV315,LTV330,LTV345,LTV360
        FROM `ads_add_user_media_roi_multiples_daily`
        where 1=1 and LTV1 >0
        <if test="reg_users != null and reg_users != ''">
            and reg_users >= #{reg_users}
        </if>
        <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
            and tdate between #{start_date} and #{end_date}
        </if>
        <if test="appidList != null and appidList.size > 0">
            AND appid IN
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        <if test="mediaList != null and mediaList.size > 0">
            AND media IN
            <foreach collection="mediaList" item="media" open="(" separator="," close=")">
                #{media}
            </foreach>
        </if>
        <if test="exclusionDateList != null and exclusionDateList.size > 0">
            and tdate not in
            <foreach collection="exclusionDateList" item="td" open="(" separator="," close=")">
                #{td}
            </foreach>
        </if>
    </sql>


    <select id="getNewerAdGrowthList" parameterType="com.wbgame.pojo.game.report.AdGrowthReportVo" resultType="com.wbgame.pojo.game.report.AdGrowthReportVo">
        select
            a.app_name app_name,
            #{adTypeList} ad_type,
        <if test="group != null and group != ''">
            ${group_alias},
        </if>
            a.reg_user_cnt reg_user_cnt,
            a.permeability_1 permeability_1,
            a.permeability_2 permeability_2,
            a.permeability_3 permeability_3,
            a.permeability_4 permeability_4,
            a.permeability_5 permeability_5,
            a.permeability_6 permeability_6,
            a.permeability_7 permeability_7,
            b.avg_pv_1 avg_pv_1,
            b.avg_pv_2 avg_pv_2,
            b.avg_pv_3 avg_pv_3,
            b.avg_pv_4 avg_pv_4,
            b.avg_pv_5 avg_pv_5,
            b.avg_pv_6 avg_pv_6,
            b.avg_pv_7 avg_pv_7,
            round(b.pv_permeability_1,2) pv_permeability_1,
            round(b.pv_permeability_2,2) pv_permeability_2,
            round(b.pv_permeability_3,2) pv_permeability_3,
            round(b.pv_permeability_4,2) pv_permeability_4,
            round(b.pv_permeability_5,2) pv_permeability_5,
            round(b.pv_permeability_6,2) pv_permeability_6,
            round(b.pv_permeability_7,2) pv_permeability_7
        from (
        select
            app_name,
        <if test="group != null and group != ''">
            ${group},
        </if>
            sum(distinct reg_user_cnt) reg_user_cnt,
            sum(distinct retention_1_day_ad_user_cnt)/sum(distinct reg_user_cnt) permeability_1,
            sum(distinct retention_2_day_ad_user_cnt)/sum(distinct retention_1_day_user_cnt) permeability_2,
            sum(distinct retention_3_day_ad_user_cnt)/sum(distinct retention_2_day_user_cnt) permeability_3,
            sum(distinct retention_4_day_ad_user_cnt)/sum(distinct retention_3_day_user_cnt) permeability_4,
            sum(distinct retention_5_day_ad_user_cnt)/sum(distinct retention_4_day_user_cnt) permeability_5,
            sum(distinct retention_6_day_ad_user_cnt)/sum(distinct retention_5_day_user_cnt) permeability_6,
            sum(distinct retention_7_day_ad_user_cnt)/sum(distinct retention_6_day_user_cnt) permeability_7
        from
         #{tableName}  where 1=1
        <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
            and tdate between #{start_date} and #{end_date}
        </if>
        <if test="appidList != null and appidList.size > 0">
            AND appid IN
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        <if test="channelList != null and channelList.size > 0">
            AND download_channel IN
            <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                #{channel}
            </foreach>
        </if>
        <if test="adTypeList != null and adTypeList!= ''">
            AND ad_type =  #{adTypeList}
        </if>
        <if test="ad_pos != null and ad_pos!=''">
            AND ad_pos in(${ad_pos})
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        )  a left join (
        select
            app_name,
        <if test="group != null and group != ''">
                ${group},
        </if>
            sum(retention_1_day_ad_cnt)/sum(distinct reg_user_cnt) avg_pv_1,
            sum(retention_2_day_ad_cnt)/sum(retention_1_day_user_cnt) avg_pv_2,
            sum(retention_3_day_ad_cnt)/sum(retention_2_day_user_cnt) avg_pv_3,
            sum(retention_4_day_ad_cnt)/sum(retention_3_day_user_cnt) avg_pv_4,
            sum(retention_5_day_ad_cnt)/sum(retention_4_day_user_cnt) avg_pv_5,
            sum(retention_6_day_ad_cnt)/sum(retention_5_day_user_cnt) avg_pv_6,
            sum(retention_7_day_ad_cnt)/sum(retention_6_day_user_cnt) avg_pv_7,
            
            sum(retention_1_day_ad_cnt)/sum(retention_1_day_ad_user_cnt) pv_permeability_1,
            sum(retention_2_day_ad_cnt)/sum(retention_2_day_ad_user_cnt) pv_permeability_2,
            sum(retention_3_day_ad_cnt)/sum(retention_3_day_ad_user_cnt) pv_permeability_3,
            sum(retention_4_day_ad_cnt)/sum(retention_4_day_ad_user_cnt) pv_permeability_4,
            sum(retention_5_day_ad_cnt)/sum(retention_5_day_ad_user_cnt) pv_permeability_5,
            sum(retention_6_day_ad_cnt)/sum(retention_6_day_ad_user_cnt) pv_permeability_6,
            sum(retention_7_day_ad_cnt)/sum(retention_7_day_ad_user_cnt) pv_permeability_7
            
        from
        #{tableName} where 1=1
        <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
            and tdate between #{start_date} and #{end_date}
        </if>
        <if test="appidList != null and appidList.size > 0">
            AND appid IN
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        <if test="channelList != null and channelList.size > 0">
            AND download_channel IN
            <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                #{channel}
            </foreach>
        </if>
        <if test="adTypeList != null and adTypeList!= ''">
            AND ad_type =  #{adTypeList}
        </if>
        <if test="ad_pos != null and ad_pos!=''">
            AND ad_pos in(${ad_pos})
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        ) b on 1=1
        <if test='group != null and group.contains("appid")'>
            AND a.appid = b.appid
        </if>
        <if test='group != null and group.contains("tdate")'>
            AND a.tdate = b.tdate
        </if>

        <if test='group != null and group.contains("download_channel")'>
            AND a.download_channel = b.download_channel
        </if>

        <if test='group != null and group.contains("ad_type")'>
            AND a.ad_type = b.ad_type
        </if>

        <if test='group != null and group.contains("ad_pos")'>
            AND a.ad_pos = b.ad_pos
        </if>

        <if test="group != null and group != ''">
            group by ${group_alias}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                <if test='group != null and group.contains("tdate")'>
                    order by a.tdate asc ,reg_user_cnt desc
                </if>
                <if test='group != null and !group.contains("tdate")'>
                    order by reg_user_cnt desc
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="getNewerAdGrowthSum" parameterType="com.wbgame.pojo.game.report.AdGrowthReportVo" resultType="com.wbgame.pojo.game.report.AdGrowthReportVo">
        select
        sum(distinct reg_user_cnt) reg_user_cnt,
        sum(distinct retention_1_day_ad_user_cnt)/sum(distinct reg_user_cnt) permeability_1,
        sum(distinct retention_2_day_ad_user_cnt)/sum(distinct retention_1_day_user_cnt) permeability_2,
        sum(distinct retention_3_day_ad_user_cnt)/sum(distinct retention_2_day_user_cnt) permeability_3,
        sum(distinct retention_4_day_ad_user_cnt)/sum(distinct retention_3_day_user_cnt) permeability_4,
        sum(distinct retention_5_day_ad_user_cnt)/sum(distinct retention_4_day_user_cnt) permeability_5,
        sum(distinct retention_6_day_ad_user_cnt)/sum(distinct retention_5_day_user_cnt) permeability_6,
        sum(distinct retention_7_day_ad_user_cnt)/sum(distinct retention_6_day_user_cnt) permeability_7,

        sum(retention_1_day_ad_cnt)/sum(distinct reg_user_cnt) avg_pv_1,
        sum(retention_2_day_ad_cnt)/sum(retention_1_day_user_cnt) avg_pv_2,
        sum(retention_3_day_ad_cnt)/sum(retention_2_day_user_cnt) avg_pv_3,
        sum(retention_4_day_ad_cnt)/sum(retention_3_day_user_cnt) avg_pv_4,
        sum(retention_5_day_ad_cnt)/sum(retention_4_day_user_cnt) avg_pv_5,
        sum(retention_6_day_ad_cnt)/sum(retention_5_day_user_cnt) avg_pv_6,
        sum(retention_7_day_ad_cnt)/sum(retention_6_day_user_cnt) avg_pv_7,
        
        round(sum(retention_1_day_ad_cnt)/sum(retention_1_day_ad_user_cnt),2) pv_permeability_1,
        round(sum(retention_2_day_ad_cnt)/sum(retention_2_day_ad_user_cnt),2) pv_permeability_2,
        round(sum(retention_3_day_ad_cnt)/sum(retention_3_day_ad_user_cnt),2) pv_permeability_3,
        round(sum(retention_4_day_ad_cnt)/sum(retention_4_day_ad_user_cnt),2) pv_permeability_4,
        round(sum(retention_5_day_ad_cnt)/sum(retention_5_day_ad_user_cnt),2) pv_permeability_5,
        round(sum(retention_6_day_ad_cnt)/sum(retention_6_day_ad_user_cnt),2) pv_permeability_6,
        round(sum(retention_7_day_ad_cnt)/sum(retention_7_day_ad_user_cnt),2) pv_permeability_7

        from #{tableName}  where 1=1
        <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
            and tdate between #{start_date} and #{end_date}
        </if>
        <if test="appidList != null and appidList.size > 0">
            AND appid IN
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        <if test="channelList != null and channelList.size > 0">
            AND download_channel IN
            <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                #{channel}
            </foreach>
        </if>
        <if test="adTypeList != null and adTypeList!= '' ">
            AND ad_type =  #{adTypeList}
        </if>
        <if test="ad_pos != null and ad_pos!=''">
            AND ad_pos in(${ad_pos})
        </if>
    </select>
</mapper>