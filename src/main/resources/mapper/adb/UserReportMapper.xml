<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.UserReportMapper">

	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>
	
	<select id="selectUserReportTotal" parameterType="java.lang.String" resultType="java.util.Map">
		
		select ${group},sum(add_use) add_use,sum(active_use) active_use,sum(power_times) power_times 
		
		from dnwx_bi.ads_pid_crowd_daily where tdate BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid} 
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel})
		</if>
		<if test="userlabel != null and userlabel != ''">
			and userlabel like concat('%',#{userlabel},'%')
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>
		group by ${group}
		
		<if test="order != null and order != ''">
			order by ${order}
		</if>
	</select>
	
	<select id="selectAdmsgAppUserTotal" parameterType="java.util.Map" resultType="com.wbgame.pojo.product.AdmsgAppUserTotalVo">
	
		SELECT '${tdate}' tdate,xx.productid appid,yy.currStart,yy.currDau,xx.currNew 
		FROM 
			(select productid,COUNT(DISTINCT android_id) currNew 
			FROM dnwx_wjy.push_appuser_info_hash where createtime BETWEEN '${tdate} 00:00:00' and '${tdate} 23:59:59'
			group by productid ) xx 
		LEFT JOIN
			(select productid,COUNT(android_id) currStart,COUNT(DISTINCT android_id) currDau
			FROM dnwx_wjy.np_post_log_${today} where createtime BETWEEN '${tdate} 00:00:00' and '${tdate} 23:59:59'
			group by productid ) yy 
		ON xx.productid=yy.productid
		
	</select>
	
	<select id="selectAdmsgChaUserTotal" parameterType="java.util.Map" resultType="com.wbgame.pojo.product.AdmsgAppUserTotalVo">
	
		SELECT '${tdate}' tdate,xx.productid appid,xx.chaid cha_id,yy.currStart,yy.currDau,xx.currNew 
		FROM 
			(select SUBSTR(projectid FROM 1 FOR 5) productid,chaid,COUNT(DISTINCT android_id) currNew 
			FROM dnwx_wjy.push_chauser_info_hash where createtime BETWEEN '${tdate} 00:00:00' and '${tdate} 23:59:59'
			group by SUBSTR(projectid FROM 1 FOR 5),chaid ) xx 
		LEFT JOIN
			(select productid,mmid,COUNT(android_id) currStart,COUNT(DISTINCT android_id) currDau
			FROM dnwx_wjy.np_post_log_${today} 
			group by productid,mmid ) yy 
		ON xx.productid=yy.productid AND xx.chaid=yy.mmid
		
	</select>
	
	<select id="selectAdmsgChaUserTotalTwo" parameterType="java.util.Map" resultType="com.wbgame.pojo.product.AdmsgAppUserTotalVo">
	
		SELECT '${tdate}' tdate,xx.projectid prjid,xx.chaid cha_id,yy.currStart,yy.currDau,xx.currNew 
		FROM 
			(select projectid,chaid,COUNT(DISTINCT android_id) currNew 
			FROM dnwx_wjy.push_chauser_info_hash where createtime BETWEEN '${tdate} 00:00:00' and '${tdate} 23:59:59'
			group by projectid,chaid ) xx 
		LEFT JOIN
			(select projectid,mmid,COUNT(android_id) currStart,COUNT(DISTINCT android_id) currDau
			FROM dnwx_wjy.np_post_log_${today} 
			group by projectid,mmid ) yy 
		ON xx.projectid=yy.projectid AND xx.chaid=yy.mmid
		
	</select>
	
	<select id="selectAdmsgVerUserTotal" parameterType="java.util.Map" resultType="com.wbgame.pojo.product.AdmsgAppUserTotalVo">
	
		SELECT '${tdate}' tdate,xx.productid appid,xx.ver,yy.currStart,yy.currDau,xx.currNew,zz.currUpdate 
		FROM 
			(select productid,ver,COUNT(DISTINCT android_id) currNew 
			FROM dnwx_wjy.push_veruser_info_hash where createtime BETWEEN '${tdate} 00:00:00' and '${tdate} 23:59:59'
			group by productid,ver ) xx 
		LEFT JOIN
			(select productid,version,COUNT(android_id) currStart,COUNT(DISTINCT android_id) currDau
			FROM dnwx_wjy.np_post_log_${today} 
			group by productid,version ) yy 
		ON xx.productid=yy.productid AND xx.ver=yy.version
		LEFT JOIN
			(select productid,COUNT(DISTINCT android_id) currUpdate
			FROM dnwx_wjy.np_post_log_${today} 
			group by productid HAVING COUNT(DISTINCT version) > 1 ) zz  
		ON xx.productid=zz.productid 
		
	</select>
	
	<select id="selectAdmsgCustomEventInfo" parameterType="java.util.Map" resultType="java.util.Map">
	
		select *,channel cha_id,
				CONCAT(TRUNCATE(news_count/news_sum*100,2),'%') news_rate,
				CONCAT(TRUNCATE(user_num/user_sum*100,2),'%') user_rate
		from dnwx_bi.ads_custom_events_hourly 
		where tdate BETWEEN #{sdate} AND #{edate} 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="cha_id != null and cha_id != ''">
				and channel in (${cha_id}) 
			</if>
			<if test="ver != null and ver != ''">
				and ver in (${ver}) 
			</if>
			<if test="event_name != null and event_name != ''">
				and event_name = #{event_name} 
			</if>
			
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate desc,thour desc,news_count desc
			</otherwise>
		</choose>
	</select>


	<!-- 大数据-版本分布新增活跃  -->
	<select id="selectCurrUserVerReportTotal" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="curr_user_verreport_sql"/>
	</select>
	<select id="selectCurrUserVerReportTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
		SUM(xx.currUpdate) currUpdate,
		SUM(xx.currStart) currStart,
		SUM(xx.currDau) currDau,
		SUM(xx.currNew) currNew,
		SUM(xx.sumNew) sumNew

		from (<include refid="curr_user_verreport_sql"/>) xx
	</select>

	<sql id="curr_user_verreport_sql">

		SELECT tdate,appid,app,ver,currDau,currNew,currStart,currUpdate,sumnew as sumNew
		FROM dnwx_bi.ads_version_analysis_daily

		where tdate BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="ver != null and ver != ''">
			and ver in (${ver})
		</if>

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
		</choose>
	</sql>
	
</mapper>