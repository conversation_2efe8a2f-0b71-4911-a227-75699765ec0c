<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adb.AdvSpaceConfigMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.clean.AdvSpaceConfig">
    <!--@mbg.generated-->
    <!--@Table adv_space_config-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <id column="appid" jdbcType="INTEGER" property="appid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, appid, `name`, `owner`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from adv_space_config
    where id = #{id,jdbcType=VARCHAR}
      and appid = #{appid,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--@mbg.generated-->
    delete from adv_space_config
    where id = #{id,jdbcType=VARCHAR}
      and appid = #{appid,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.wbgame.pojo.clean.AdvSpaceConfig">
    <!--@mbg.generated-->
    insert into adv_space_config (id, appid, `name`, 
      `owner`, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{appid,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{owner,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wbgame.pojo.clean.AdvSpaceConfig">
    <!--@mbg.generated-->
    insert into adv_space_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="appid != null">
        appid,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="owner != null">
        `owner`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="appid != null">
        #{appid,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.clean.AdvSpaceConfig">
    <!--@mbg.generated-->
    update adv_space_config
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="owner != null">
        `owner` = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
      and appid = #{appid,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.clean.AdvSpaceConfig">
    <!--@mbg.generated-->
    update adv_space_config
    set `name` = #{name,jdbcType=VARCHAR},
      `owner` = #{owner,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
      and appid = #{appid,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-03-30-->
  <select id="selectAll" resultMap="BaseResultMap">
    select a.appid,b.app_name appName,a.id,a.name,a.owner,DATE_FORMAT(a.create_time,'%Y-%m-%d %H:%i:%s') createStr, DATE_FORMAT(a.update_time,'%Y-%m-%d %H:%i:%s') updateStr
    from adv_space_config a left join data_ym.app_info b on a.appid = b.id
    <where>
      <if test="id != null and id != ''">
        and a.id like concat('%',#{id,jdbcType=VARCHAR},'%')
      </if>
      <if test="appid != null and appid != ''">
        and a.appid=#{appid,jdbcType=INTEGER}
      </if>
      <if test="name != null and name !=''">
        and `a.name`=#{name,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

    <insert id="batchInsertAdvSpace">
      replace into adv_space_config(id,name,owner,create_time,update_time,appid) values
      <foreach collection="list" item="item" separator=",">
        (#{item.id},#{item.name},#{item.owner},#{item.createTime},#{item.updateTime},#{item.appid})
      </foreach>
    </insert>
</mapper>