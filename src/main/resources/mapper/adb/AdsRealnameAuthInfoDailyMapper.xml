<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsRealnameAuthInfoDailyMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.AdsRealnameAuthInfoDaily">
        <result column="t_date" property="tDate" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="download_channel" property="downloadChannel" jdbcType="VARCHAR"/>
        <result column="pid" property="pid" jdbcType="VARCHAR"/>
        <result column="user_reg_type" property="userRegType" jdbcType="VARCHAR"/>
        <result column="brand" property="brand" jdbcType="VARCHAR"/>
        <result column="model" property="model" jdbcType="VARCHAR"/>
        <result column="app_realname_pop_cnt" property="appRealnamePopCnt" jdbcType="INTEGER"/>
        <result column="app_realname_close_cnt" property="appRealnameCloseCnt" jdbcType="INTEGER"/>
        <result column="app_realname_auth_succ_cnt" property="appRealnameAuthSuccCnt" jdbcType="INTEGER"/>
        <result column="app_realname_auth_fail_cnt" property="appRealnameAuthFailCnt" jdbcType="INTEGER"/>
        <result column="passing_rate" property="passingRate" jdbcType="INTEGER"/>
        <result column="user_cnt_all" property="userCntAll" jdbcType="BIGINT"/>
        <result column="author_rate" property="authorRate" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        t_date
        , appid, download_channel, pid, user_reg_type, brand, model, app_realname_pop_cnt,
    app_realname_close_cnt, app_realname_auth_succ_cnt, app_realname_auth_fail_cnt
    </sql>
    <select id="selectAuthInfo" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.AdsRealnameAuthInfoDaily">

        <include refid="selectData"/>

    </select>

    <select id="countAuthInfo" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.AdsRealnameAuthInfoDaily">

        select

            sum(app_realname_pop_cnt)                                   app_realname_pop_cnt,
            sum(app_realname_close_cnt)                                 app_realname_close_cnt,
            sum(app_realname_auth_succ_cnt)                             app_realname_auth_succ_cnt,
            sum(app_realname_auth_fail_cnt)                             app_realname_auth_fail_cnt,
            sum(user_cnt_all)                             user_cnt_all
        from (

            <include refid="selectData"/>
                          ) a

    </select>

    <sql id="selectData">

        select

        <if test="group != null and group != ''">
            ${group},
        </if>
        ifnull(sum(app_realname_pop_cnt), 0)                                   app_realname_pop_cnt,
        ifnull(sum(app_realname_close_cnt), 0)                                 app_realname_close_cnt,
        ifnull(sum(app_realname_auth_succ_cnt), 0)                             app_realname_auth_succ_cnt,
        ifnull(sum(app_realname_auth_fail_cnt), 0)                             app_realname_auth_fail_cnt,
        ifnull(sum(user_cnt_all), 0)                             user_cnt_all,
        cast(ifnull(sum(app_realname_auth_succ_cnt) / sum(user_cnt_all), 0) * 100 as decimal(18, 2))  author_rate,
        truncate(ifnull(sum(app_realname_auth_succ_cnt) / sum(app_realname_pop_cnt), 0) * 100, 2)  passing_rate

        from ads_realname_auth_info_daily

        <where>

            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and t_date between #{start_date} and #{end_date}
            </if>


            <if test="userRegType != null and userRegType != ''">

                and user_reg_type = #{userRegType}
            </if>
            <if test="brand != null and brand != ''">

                and brand = #{brand}
            </if>
            <if test="model != null and model != ''">

                and model = #{model}
            </if>

            <if test="channelList != null and channelList.size > 0">
                AND download_channel IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

            <if test="prjidList != null and prjidList.size > 0">
                AND pid IN
                <foreach collection="prjidList" item="prj" open="(" separator="," close=")">
                    #{prj}
                </foreach>
            </if>

        </where>

        <if test="group != null and group != ''">
            group by ${group}
        </if>

        having sum(app_realname_pop_cnt) > 0
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by t_date
            </otherwise>
        </choose>

    </sql>

</mapper>