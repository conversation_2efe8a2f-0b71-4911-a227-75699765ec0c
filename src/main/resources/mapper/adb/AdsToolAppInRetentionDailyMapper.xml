<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsToolAppInRetentionDailyMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.AdsToolAppInRetentionDailyVO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="tdate" property="tdate" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
        <result column="download_channel" property="downloadChannel" jdbcType="VARCHAR"/>
        <result column="pid" property="pid" jdbcType="VARCHAR"/>
        <result column="ver" property="ver" jdbcType="VARCHAR"/>
        <result column="reg_user_cnt" property="regUserCnt" jdbcType="BIGINT"/>
        <result column="real_reg_user_cnt" property="realRegUserCnt" jdbcType="BIGINT"/>
        <result column="real_retention_1day_user_cnt" property="realRetention1dayUserCnt" jdbcType="VARCHAR"/>
        <result column="real_retention_2day_user_cnt" property="realRetention2dayUserCnt" jdbcType="VARCHAR"/>
        <result column="real_retention_3day_user_cnt" property="realRetention3dayUserCnt" jdbcType="VARCHAR"/>
        <result column="real_retention_4day_user_cnt" property="realRetention4dayUserCnt" jdbcType="VARCHAR"/>
        <result column="real_retention_5day_user_cnt" property="realRetention5dayUserCnt" jdbcType="BIGINT"/>
        <result column="real_retention_6day_user_cnt" property="realRetention6dayUserCnt" jdbcType="BIGINT"/>
        <result column="real_retention_7day_user_cnt" property="realRetention7dayUserCnt" jdbcType="BIGINT"/>
        <result column="real_retention_14day_user_cnt" property="realRetention14dayUserCnt" jdbcType="BIGINT"/>
        <result column="real_retention_30day_user_cnt" property="realRetention30dayUserCnt" jdbcType="BIGINT"/>
        <result column="proportion_of_users" property="proportionOfUsers" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, tdate, appid, app_name, download_channel, pid, ver, reg_user_cnt, real_reg_user_cnt,
    real_retention_1day_user_cnt, real_retention_2day_user_cnt, real_retention_3day_user_cnt,
    real_retention_4day_user_cnt, real_retention_5day_user_cnt, real_retention_6day_user_cnt,
    real_retention_7day_user_cnt, real_retention_14day_user_cnt, real_retention_30day_user_cnt
    </sql>

    <select id="selectByCondition" parameterType="com.wbgame.pojo.AdsToolAppInRetentionDailyDTO"
            resultMap="BaseResultMap">

        SELECT

        appid,
        app_name,
        <if test="group != null and group !=''">
            ${group},
        </if>
        sum( reg_user_cnt ) reg_user_cnt,
        sum( real_reg_user_cnt ) real_reg_user_cnt,
        round( sum( real_reg_user_cnt ) / sum( reg_user_cnt ) * 100, 2 ) proportion_of_users,
        concat(round(sum(real_retention_1day_user_cnt) / sum(real_reg_user_cnt) * 100, 2), "%") real_retention_1day_user_cnt,
        concat(round(sum(real_retention_2day_user_cnt) /sum(real_reg_user_cnt) * 100, 2), "%") real_retention_2day_user_cnt,
        concat(round(sum(real_retention_3day_user_cnt) /sum(real_reg_user_cnt) * 100, 2), "%") real_retention_3day_user_cnt,
        concat(round(sum(real_retention_4day_user_cnt) /sum(real_reg_user_cnt) * 100, 2), "%") real_retention_4day_user_cnt,
        concat(round(sum(real_retention_5day_user_cnt) /sum(real_reg_user_cnt) * 100, 2), "%") real_retention_5day_user_cnt,
        concat(round(sum(real_retention_6day_user_cnt) /sum(real_reg_user_cnt) * 100, 2), "%") real_retention_6day_user_cnt,
        concat(round(sum(real_retention_7day_user_cnt) /sum(real_reg_user_cnt) * 100, 2), "%") real_retention_7day_user_cnt,
        concat(round(sum(real_retention_14day_user_cnt) / sum(real_reg_user_cnt) * 100, 2), "%") real_retention_14day_user_cnt,
        concat(round(sum(real_retention_30day_user_cnt) / sum(real_reg_user_cnt) * 100, 2), "%") real_retention_30day_user_cnt
        FROM
        ads_tool_app_in_retention_daily

        <where>

            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and tdate between #{start_date} and #{end_date}
            </if>
            <if test="appid != null and appid.size > 0">
                and appid in
                <foreach collection="appid" item="aid" open="(" separator="," close=")">
                    #{aid}
                </foreach>

            </if>
            <if test="downloadChannel != null and downloadChannel.size > 0">
                and download_channel in
                <foreach collection="downloadChannel" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>
            <if test="pid != null and pid !=''">
                and pid = #{pid}
            </if>

            <if test="ver != null and ver !=''">
                and ver like #{ver} "%"
            </if>
        </where>
        <choose>
            <when test="group != null and group !=''">
                group by appid, app_name, ${group}
            </when>
            <otherwise>
                group by appid, app_name
            </otherwise>
        </choose>

        <choose>
            <when test="order_str != null and order_str !=''">
                ORDER BY ${order_str}
            </when>
            <otherwise>
                ORDER BY
                tdate DESC,
                id DESC
            </otherwise>
        </choose>

    </select>

</mapper>