<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wbgame.mapper.adb.AdsHotUpdateDataAnalyseDailyMapper">

    <select id="selectAdsHotUpdateDataAnalyseDailyList"
            resultType="com.wbgame.pojo.adv2.bigdata.MixedRequestAnalysisAggVo"
            parameterType="com.wbgame.pojo.adv2.bigdata.MixedRequestAnalysisVo">
    SELECT
        id,
        tdate,
        appid,
        download_channel,
        IFNULL(channel_type,'未知') channel_type,
        case when ad_buy_channel='-1' or ad_buy_channel='' then '未知' else ad_buy_channel end as ad_buy_channel,
        case when ad_buy_media='-1' or ad_buy_media='' then '未知' else ad_buy_media end as ad_buy_media,
        pid,
        agent,
        IFNULL(TRUNCATE(SUM(ad_revenue/100),2),0) ad_revenue,
        IFNULL(TRUNCATE(SUM(total_ad_revenue/100),2),0) total_ad_revenue,
        IFNULL(CONVERT(SUM(ad_revenue)/SUM(total_ad_revenue) * 100, Decimal(10,2)),0)  revenue_rate,
        SUM(active_user_cnt) active_user_cnt,
        SUM(reg_user_cnt) reg_user_cnt,
        IFNULL(TRUNCATE(SUM(ad_revenue)/SUM(active_user_cnt)/100, 3),0) dau_arpu,
        IFNULL(TRUNCATE(SUM(ad_revenue)/SUM(reg_user_cnt)/100, 3),0) add_arpu,
        IFNULL(CONVERT(SUM(reg_user_cnt)/SUM(active_user_cnt) * 100, Decimal(10,2)),0) add_rate,

        IFNULL(TRUNCATE(SUM(adjusted_splash_ad_cnt) / SUM(active_user_cnt), 2),0) avg_pv_splash,
        IFNULL(TRUNCATE(SUM(adjusted_plaque_ad_cnt) / SUM(active_user_cnt), 2),0) avg_pv_plaque,
        IFNULL(TRUNCATE(SUM(adjusted_banner_ad_cnt) / SUM(active_user_cnt), 2),0) avg_pv_banner,
        IFNULL(TRUNCATE(SUM(adjusted_video_ad_cnt) / SUM(active_user_cnt), 2),0) avg_pv_video,
        IFNULL(TRUNCATE(SUM(adjusted_msg_ad_cnt) / SUM(active_user_cnt), 2),0) avg_pv_msg,

        IFNULL(TRUNCATE(SUM(adjusted_splash_ad_revenue) / SUM(adjusted_splash_ad_cnt)*10, 2),0) ecpm_splash,
        IFNULL(TRUNCATE(SUM(adjusted_plaque_ad_revenue) / SUM(adjusted_plaque_ad_cnt)*10, 2),0) ecpm_plaque,
        IFNULL(TRUNCATE(SUM(adjusted_banner_ad_revenue) / SUM(adjusted_banner_ad_cnt)*10, 2),0) ecpm_banner,
        IFNULL(TRUNCATE(SUM(adjusted_video_ad_revenue) / SUM(adjusted_video_ad_cnt)*10, 2),0) ecpm_video,
        IFNULL(TRUNCATE(SUM(adjusted_msg_ad_revenue) / SUM(adjusted_msg_ad_cnt)*10, 2),0) ecpm_msg,

        IFNULL(TRUNCATE(SUM(adjusted_splash_ad_cnt), 0),0) pv_splash,
        IFNULL(TRUNCATE(SUM(adjusted_plaque_ad_cnt), 0),0) pv_plaque,
        IFNULL(TRUNCATE(SUM(adjusted_banner_ad_cnt), 0),0) pv_banner,
        IFNULL(TRUNCATE(SUM(adjusted_video_ad_cnt), 0),0) pv_video,
        IFNULL(TRUNCATE(SUM(adjusted_msg_ad_cnt), 0),0) pv_msg,

        IFNULL(TRUNCATE(SUM(adjusted_splash_ad_revenue)/100, 2),0) revenue_splash,
        IFNULL(TRUNCATE(SUM(adjusted_plaque_ad_revenue)/100, 2),0) revenue_plaque,
        IFNULL(TRUNCATE(SUM(adjusted_banner_ad_revenue)/100, 2),0) revenue_banner,
        IFNULL(TRUNCATE(SUM(adjusted_video_ad_revenue)/100, 2),0) revenue_video,
        IFNULL(TRUNCATE(SUM(adjusted_msg_ad_revenue)/100, 2),0) revenue_msg,

        IFNULL(TRUNCATE(SUM(adjusted_splash_ad_revenue) / SUM(active_user_cnt) / 100, 3),0) arpu_splash,
        IFNULL(TRUNCATE(SUM(adjusted_plaque_ad_revenue) / SUM(active_user_cnt) / 100, 3),0) arpu_plaque,
        IFNULL(TRUNCATE(SUM(adjusted_banner_ad_revenue) / SUM(active_user_cnt) / 100, 3),0) arpu_banner,
        IFNULL(TRUNCATE(SUM(adjusted_video_ad_revenue) / SUM(active_user_cnt) / 100, 3),0) arpu_video,
        IFNULL(TRUNCATE(SUM(adjusted_msg_ad_revenue) / SUM(active_user_cnt) / 100, 3),0) arpu_msg
    FROM ads_hot_update_data_analyse_daily
    WHERE tdate >= #{start_date} and tdate &lt;= #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="download_channel != null and download_channel != ''">
            and download_channel in (${download_channel})
        </if>
        <if test="channel_type != null and channel_type != ''">
            and channel_type in (${channel_type})
        </if>
        <if test="ad_buy_channel != null and ad_buy_channel != ''">
            and ad_buy_channel in (${ad_buy_channel})
        </if>
        <if test="ad_buy_media != null and ad_buy_media != ''">
            and ad_buy_media in (${ad_buy_media})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="agent != null and agent != ''">
            and agent = #{agent}
        </if>
        <if test="groupStr != null and groupStr != ''">
            group by ${groupStr}
        </if>
        <if test="orderStr != null and orderStr != ''">
            order by ${orderStr}
        </if>
    </select>
</mapper>