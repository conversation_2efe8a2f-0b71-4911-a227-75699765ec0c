<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsActivityRevenueDailyMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.operate.AdsActivityRevenueDailyVO">
        <result column="t_date" property="tDate" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="activity_id" property="activityId" jdbcType="VARCHAR"/>
        <result column="act_cnt" property="actCnt" jdbcType="BIGINT"/>
        <result column="pay_revenue_sum" property="payRevenueSum" jdbcType="DECIMAL"/>
        <result column="use_cnt" property="useCnt" jdbcType="BIGINT"/>
        <result column="use_rate" property="useRate" jdbcType="DECIMAL"/>
        <result column="pay_cnt" property="payCnt" jdbcType="BIGINT"/>
        <result column="pay_revenue" property="payRevenue" jdbcType="DECIMAL"/>
        <result column="pay_revenue_rate" property="payRevenueRate" jdbcType="DECIMAL"/>
        <result column="pay_user_rate" property="payUserRate" jdbcType="DECIMAL"/>
        <result column="pay_arpu" property="payArpu" jdbcType="DECIMAL"/>
        <result column="old_user_cnt" property="oldUserCnt" jdbcType="BIGINT"/>
        <result column="old_user_iap_revenue" property="oldUserIapRevenue" jdbcType="DECIMAL"/>
        <result column="old_arpu" property="oldArpu" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        t_date
        , appid, activity_id, act_cnt, pay_revenue_sum, use_cnt, use_rate, pay_cnt,
    pay_revenue, pay_revenue_rate, pay_user_rate, pay_arpu
    </sql>
    <select id="selectByExample" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.operate.AdsActivityRevenueDailyDTO">

        select t1.*
        <if test="group != null and group !='' and group.contains('t_date') and group.contains('appid')">
            ,t2.old_user_cnt,t2.old_user_iap_revenue,t2.old_arpu
        </if>
        from  (
            select activity_id,

            <if test="group != null and group !=''">
                ${group},
            </if>

            sum(act_cnt) act_cnt,
            cast(sum(pay_revenue_sum) as decimal(18, 2)) pay_revenue_sum,
            sum(use_cnt) use_cnt,
            cast(ifnull(sum(use_cnt) / sum(act_cnt), 0) * 100 as decimal(18, 2)) use_rate,
            sum(pay_cnt) pay_cnt,
            cast(sum(pay_revenue) as decimal(18, 2)) pay_revenue,
            cast(ifnull(sum(pay_revenue) / sum(pay_revenue_sum), 0) * 100 as decimal(18, 2)) pay_revenue_rate,
            cast(ifnull(sum(pay_cnt) / sum(use_cnt), 0) * 100 as decimal(18, 2)) pay_user_rate,
            cast(ifnull(sum(pay_revenue) / sum(pay_cnt), 0) as decimal(18, 2)) pay_arpu

            from ads_activity_revenue_daily

            <include refid="condition"/>

            group by activity_id
            <if test="group != null and group !=''">
                ,${group}
            </if>
        ) t1
        <if test="group != null and group !='' and group.contains('t_date') and group.contains('appid')">
            left join (
            select tdate,
            appid,
            sum(old_user_cnt) old_user_cnt,
            cast(sum(old_user_iap_revenue)/100 as decimal(18, 2)) old_user_iap_revenue,
            cast(sum(old_user_iap_revenue)/sum(old_user_cnt)/100 as decimal(18, 2)) old_arpu
            from ads_user_iap_revenue_info_daily
            where tdate between #{start_date} and #{end_date}
            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
            group by tdate,appid
            ) t2 on t1.t_date = t2.tdate and t1.appid = t2.appid
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by
                <if test="group != null and group !='' and group.contains('t_date')">
                    t_date,
                </if>
                use_cnt desc
            </otherwise>
        </choose>

    </select>


    <select id="selectByExampleCount" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.operate.AdsActivityRevenueDailyDTO">

        select

        sum(act_cnt) act_cnt,
        cast(sum(pay_revenue_sum) as decimal(18, 2)) pay_revenue_sum,
        sum(use_cnt)  use_cnt,
        sum(pay_cnt) pay_cnt,
        cast(sum(pay_revenue) as decimal(18, 2)) pay_revenue,
        cast(ifnull(sum(pay_cnt) / sum(use_cnt), 0) * 100 as decimal(18, 2)) pay_user_rate,
        cast(ifnull(sum(pay_revenue) / sum(pay_cnt), 0) as decimal(18, 2)) pay_arpu
        <if test="group != null and group !='' and group.contains('t_date') and group.contains('appid')">
            ,sum(old_user_cnt) old_user_cnt
            ,cast(sum(old_user_iap_revenue) as decimal(18, 2)) old_user_iap_revenue
            ,cast(sum(old_user_iap_revenue)/sum(old_user_cnt) as decimal(18, 2)) old_arpu
        </if>

        from (
            select t1.*
            <if test="group != null and group !='' and group.contains('t_date') and group.contains('appid')">
                ,t2.old_user_cnt,t2.old_user_iap_revenue
            </if>

            from  (
                select
                t_date,
                appid,
                max(act_cnt) act_cnt,
                max(pay_revenue_sum) pay_revenue_sum,
                sum(use_cnt) use_cnt,
                sum(pay_cnt) pay_cnt,
                sum(pay_revenue) pay_revenue,
                sum(pay_arpu) pay_arpu

                from ads_activity_revenue_daily

                <include refid="condition"/>
                group by t_date,appid
               ) t1
        <if test="group != null and group !='' and group.contains('t_date') and group.contains('appid')">
            left join (
            select tdate,
            appid,
            sum(old_user_cnt) old_user_cnt,
            sum(old_user_iap_revenue)/100 old_user_iap_revenue
            from ads_user_iap_revenue_info_daily
            where tdate between #{start_date} and #{end_date}
            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
            group by tdate,appid
            ) t2 on t1.t_date = t2.tdate and t1.appid = t2.appid
        </if>
        ) a

    </select>

    <sql id="condition">

        <where>

            t_date between #{start_date} and #{end_date}
            
            <if test="activityId != null and activityId != ''">
                and activity_id = #{activityId}
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>

    </sql>


    <select id="getAcivityPayTotalChart" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.operate.AdsActivityRevenueDailyDTO">
        select
        t_date,appid,
        cast(avg(pay_revenue_sum) as decimal(18, 2)) pay_revenue
        from ads_activity_revenue_daily where t_date between #{start_date} and #{end_date}
        <if test="appidList != null and appidList.size > 0">
            AND appid IN
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        group by t_date,appid order by t_date asc
    </select>

    <select id="getOldPayTotalChart" resultType="com.wbgame.pojo.operate.AdsActivityRevenueDailyVO">
        select tdate tDate,
        appid,
        cast(sum(old_user_iap_revenue)/100 AS DECIMAL(18, 2)) payRevenue
        from ads_user_iap_revenue_info_daily
        where tdate between #{start_date} and #{end_date}
        <if test="appidList != null and appidList.size > 0">
            AND appid IN
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        group by tdate,appid order by tdate asc
    </select>

    <select id="selectArpu" resultType="com.wbgame.pojo.operate.AdsActivityRevenueDailyVO">
        select t_date tDate,appid,
        ifnull(cast(sum(pay_revenue_sum) / sum(act_cnt) as decimal(18, 2)), 0) payArpu
        from ads_activity_revenue_daily
        <include refid="condition"/>
        group by t_date,appid
    </select>


</mapper>