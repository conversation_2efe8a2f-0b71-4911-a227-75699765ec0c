<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsBackflowUserRetentionAnalysisDailyMapper">
    <resultMap id="adsBackFlowMap" type="com.wbgame.pojo.operate.AdsBackflowUserRetentionAnalysisDailyVO">
        <result column="tdate" property="tdate" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="download_channel" property="download_channel" jdbcType="VARCHAR"/>
        <result column="backflow1_retention_1day_user_cnt" property="backflow1_retention_1day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow1_retention_2day_user_cnt" property="backflow1_retention_2day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow1_retention_3day_user_cnt" property="backflow1_retention_3day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow1_retention_7day_user_cnt" property="backflow1_retention_7day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow1_retention_14day_user_cnt" property="backflow1_retention_14day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow1_retention_30day_user_cnt" property="backflow1_retention_30day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow3_retention_1day_user_cnt" property="backflow3_retention_1day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow3_retention_2day_user_cnt" property="backflow3_retention_2day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow3_retention_3day_user_cnt" property="backflow3_retention_3day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow3_retention_7day_user_cnt" property="backflow3_retention_7day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow3_retention_14day_user_cnt" property="backflow3_retention_14day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow3_retention_30day_user_cnt" property="backflow3_retention_30day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow7_retention_1day_user_cnt" property="backflow7_retention_1day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow7_retention_2day_user_cnt" property="backflow7_retention_2day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow7_retention_3day_user_cnt" property="backflow7_retention_3day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow7_retention_7day_user_cnt" property="backflow7_retention_7day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow7_retention_14day_user_cnt" property="backflow7_retention_14day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow7_retention_30day_user_cnt" property="backflow7_retention_30day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow14_retention_1day_user_cnt" property="backflow14_retention_1day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow14_retention_2day_user_cnt" property="backflow14_retention_2day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow14_retention_3day_user_cnt" property="backflow14_retention_3day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow14_retention_7day_user_cnt" property="backflow14_retention_7day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow14_retention_14day_user_cnt" property="backflow14_retention_14day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow14_retention_30day_user_cnt" property="backflow14_retention_30day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow14up_retention_1day_user_cnt" property="backflow14up_retention_1day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow14up_retention_2day_user_cnt" property="backflow14up_retention_2day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow14up_retention_3day_user_cnt" property="backflow14up_retention_3day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow14up_retention_7day_user_cnt" property="backflow14up_retention_7day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow14up_retention_14day_user_cnt" property="backflow14up_retention_14day_user_cnt"
                jdbcType="INTEGER"/>
        <result column="backflow14up_retention_30day_user_cnt" property="backflow14up_retention_30day_user_cnt"
                jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        tdate
        , appid, download_channel, backflow1_retention_1day_user_cnt, backflow1_retention_2day_user_cnt,
    backflow1_retention_3day_user_cnt, backflow1_retention_7day_user_cnt, backflow1_retention_14day_user_cnt, 
    backflow1_retention_30day_user_cnt, backflow3_retention_1day_user_cnt, backflow3_retention_2day_user_cnt, 
    backflow3_retention_3day_user_cnt, backflow3_retention_7day_user_cnt, backflow3_retention_14day_user_cnt, 
    backflow3_retention_30day_user_cnt, backflow7_retention_1day_user_cnt, backflow7_retention_2day_user_cnt, 
    backflow7_retention_3day_user_cnt, backflow7_retention_7day_user_cnt, backflow7_retention_14day_user_cnt, 
    backflow7_retention_30day_user_cnt, backflow14_retention_1day_user_cnt, backflow14_retention_2day_user_cnt, 
    backflow14_retention_3day_user_cnt, backflow14_retention_7day_user_cnt, backflow14_retention_14day_user_cnt, 
    backflow14_retention_30day_user_cnt, backflow14up_retention_1day_user_cnt, backflow14up_retention_2day_user_cnt, 
    backflow14up_retention_3day_user_cnt, backflow14up_retention_7day_user_cnt, backflow14up_retention_14day_user_cnt, 
    backflow14up_retention_30day_user_cnt
    </sql>

    <select id="selectAdsBackflowUserRetentionAnalysisDaily" parameterType="com.wbgame.common.GeneralReportParam"
        resultMap= "adsBackFlowMap">


        SELECT
            <if test="group != null and group != ''">
                ${group},
            </if>

        sum(backflow_retention_paynum1) backflow_retention_paynum1,
        sum(backflow_retention_paysum1) / 100 backflow_retention_paysum1,
        truncate(ifnull(sum(backflow_retention_paynum1) / sum(backflow1_retention_1day_user_cnt), 0) * 100, 2) backflow_retention_paynum1_rate,
        truncate(ifnull(sum(backflow_retention_paysum1) / 100 / sum(backflow1_retention_1day_user_cnt), 0), 2) backflow_retention_paysum1_rate,

        sum(backflow_retention_paynum3) backflow_retention_paynum3,
        sum(backflow_retention_paysum3) / 100 backflow_retention_paysum3,
        truncate(ifnull(sum(backflow_retention_paynum3) / sum(backflow3_retention_1day_user_cnt), 0) * 100, 2) backflow_retention_paynum3_rate,
        truncate(ifnull(sum(backflow_retention_paysum3) / 100 / sum(backflow3_retention_1day_user_cnt), 0), 2) backflow_retention_paysum3_rate,

        sum(backflow_retention_paynum7) backflow_retention_paynum7,
        sum(backflow_retention_paysum7) / 100 backflow_retention_paysum7,
        truncate(ifnull(sum(backflow_retention_paynum7) / sum(backflow7_retention_1day_user_cnt), 0) * 100, 2) backflow_retention_paynum7_rate,
        truncate(ifnull(sum(backflow_retention_paysum7) / 100 / sum(backflow7_retention_1day_user_cnt), 0), 2) backflow_retention_paysum7_rate,

        sum(backflow_retention_paynum14) backflow_retention_paynum14,
        sum(backflow_retention_paysum14) / 100 backflow_retention_paysum14,
        truncate(ifnull(sum(backflow_retention_paynum14) / sum(backflow14_retention_1day_user_cnt), 0) * 100, 2) backflow_retention_paynum14_rate,
        truncate(ifnull(sum(backflow_retention_paysum14) / 100 / sum(backflow14_retention_1day_user_cnt)), 2) backflow_retention_paysum14_rate,

        sum(backflow_retention_paynum14up) backflow_retention_paynum14up,
        sum(backflow_retention_paysum14up) / 100 backflow_retention_paysum14up,
        truncate(ifnull(sum(backflow_retention_paynum14up) / sum(backflow14up_retention_1day_user_cnt), 0) * 100, 2) backflow_retention_paynum14up_rate,
        truncate(ifnull(sum(backflow_retention_paysum14up) / 100 / sum(backflow14up_retention_1day_user_cnt), 0), 2) backflow_retention_paysum14up_rate,

        TRUNCATE( sum( backflow1_retention_2day_user_cnt ) / sum( backflow1_retention_1day_user_cnt ) * 100, 2 ) secondRetentionRate1,
        TRUNCATE( sum( backflow3_retention_2day_user_cnt ) / sum( backflow3_retention_1day_user_cnt ) * 100, 2 ) secondRetentionRate3,
        TRUNCATE( sum( backflow7_retention_2day_user_cnt ) / sum( backflow7_retention_1day_user_cnt ) * 100, 2 ) secondRetentionRate7,
        TRUNCATE( sum( backflow14_retention_2day_user_cnt ) / sum( backflow14_retention_1day_user_cnt ) * 100, 2 ) secondRetentionRate14,
        TRUNCATE( sum( backflow14up_retention_2day_user_cnt ) / sum( backflow14up_retention_1day_user_cnt ) * 100, 2 ) secondRetentionRate14above,

        sum( backflow1_retention_1day_user_cnt ) backflow1_retention_1day_user_cnt,
        TRUNCATE(sum( backflow1_retention_3day_user_cnt ) / sum( backflow1_retention_1day_user_cnt ) * 100, 2) backflow1_retention_3day_user_cnt,
        TRUNCATE(sum( backflow1_retention_7day_user_cnt )  / sum( backflow1_retention_1day_user_cnt ) * 100, 2) backflow1_retention_7day_user_cnt,
        TRUNCATE(sum( backflow1_retention_14day_user_cnt )  / sum( backflow1_retention_1day_user_cnt ) * 100, 2) backflow1_retention_14day_user_cnt,
        TRUNCATE(sum( backflow1_retention_30day_user_cnt )  / sum( backflow1_retention_1day_user_cnt ) * 100, 2) backflow1_retention_30day_user_cnt,

        sum( backflow3_retention_1day_user_cnt ) backflow3_retention_1day_user_cnt,
        TRUNCATE( sum( backflow3_retention_3day_user_cnt ) / sum( backflow3_retention_1day_user_cnt ) * 100, 2 ) backflow3_retention_3day_user_cnt,
        TRUNCATE( sum( backflow3_retention_7day_user_cnt ) / sum( backflow3_retention_1day_user_cnt ) * 100, 2 ) backflow3_retention_7day_user_cnt,
        TRUNCATE( sum( backflow3_retention_14day_user_cnt ) / sum( backflow3_retention_1day_user_cnt ) * 100, 2 ) backflow3_retention_14day_user_cnt,
        TRUNCATE( sum( backflow3_retention_30day_user_cnt ) / sum( backflow3_retention_1day_user_cnt ) * 100, 2 ) backflow3_retention_30day_user_cnt,

        sum( backflow7_retention_1day_user_cnt ) backflow7_retention_1day_user_cnt,
        TRUNCATE( sum( backflow7_retention_3day_user_cnt ) / sum( backflow7_retention_1day_user_cnt ) * 100, 2 ) backflow7_retention_3day_user_cnt,
        TRUNCATE( sum( backflow7_retention_7day_user_cnt ) / sum( backflow7_retention_1day_user_cnt ) * 100, 2 ) backflow7_retention_7day_user_cnt,
        TRUNCATE( sum( backflow7_retention_14day_user_cnt ) / sum( backflow7_retention_1day_user_cnt ) * 100, 2 ) backflow7_retention_14day_user_cnt,
        TRUNCATE( sum( backflow7_retention_30day_user_cnt ) / sum( backflow7_retention_1day_user_cnt ) * 100, 2 ) backflow7_retention_30day_user_cnt,

        sum( backflow14_retention_1day_user_cnt ) backflow14_retention_1day_user_cnt,
        TRUNCATE( sum( backflow14_retention_3day_user_cnt ) / sum( backflow14_retention_1day_user_cnt ) * 100, 2 ) backflow14_retention_3day_user_cnt,
        TRUNCATE( sum( backflow14_retention_7day_user_cnt ) / sum( backflow14_retention_1day_user_cnt ) * 100, 2 ) backflow14_retention_7day_user_cnt,
        TRUNCATE( sum( backflow14_retention_14day_user_cnt ) / sum( backflow14_retention_1day_user_cnt ) * 100, 2 ) backflow14_retention_14day_user_cnt,
        TRUNCATE( sum( backflow14_retention_30day_user_cnt ) / sum( backflow14_retention_1day_user_cnt ) * 100, 2 ) backflow14_retention_30day_user_cnt,

        sum( backflow14up_retention_1day_user_cnt ) backflow14up_retention_1day_user_cnt,
        TRUNCATE( sum( backflow14up_retention_3day_user_cnt ) / sum( backflow14up_retention_1day_user_cnt ) * 100, 2 ) backflow14up_retention_3day_user_cnt,
        TRUNCATE( sum( backflow14up_retention_7day_user_cnt ) / sum( backflow14up_retention_1day_user_cnt ) * 100, 2 ) backflow14up_retention_7day_user_cnt,
        TRUNCATE( sum( backflow14up_retention_14day_user_cnt ) / sum( backflow14up_retention_1day_user_cnt ) * 100, 2 ) backflow14up_retention_14day_user_cnt,
        TRUNCATE( sum( backflow14up_retention_30day_user_cnt ) / sum( backflow14up_retention_1day_user_cnt ) * 100, 2 ) backflow14up_retention_30day_user_cnt
        FROM
            ads_backflow_user_retention_analysis_daily

        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                tdate BETWEEN #{start_date}
                AND #{end_date}
            </if>


            <if test="channelList != null and channelList.size > 0">
                AND download_channel IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

        </where>
        <if test="group != null and group != ''">
            GROUP BY ${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                ORDER BY ${order_str}
            </when>
            <otherwise>
                ORDER BY tdate, appid DESC
            </otherwise>
        </choose>

    </select>

    <select id="selectAdsBackflowUserRetentionAnalysisDailyCount" parameterType="com.wbgame.common.GeneralReportParam"
        resultMap= "adsBackFlowMap">


        select

        cast(sum(backflow_retention_paynum1) as decimal(18, 2)) backflow_retention_paynum1,
        cast(sum(backflow_retention_paysum1) as decimal(18, 2)) backflow_retention_paysum1,
        cast(sum(backflow_retention_paysum1_rate) as decimal(18, 2)) backflow_retention_paysum1_rate,
        cast(sum(backflow_retention_paynum3) as decimal(18, 2)) backflow_retention_paynum3,
        cast(sum(backflow_retention_paysum3) as decimal(18, 2)) backflow_retention_paysum3,
        cast(sum(backflow_retention_paysum3_rate) as decimal(18, 2)) backflow_retention_paysum3_rate,
        cast(sum(backflow_retention_paynum7) as decimal(18, 2)) backflow_retention_paynum7,
        cast(sum(backflow_retention_paysum7) as decimal(18, 2)) backflow_retention_paysum7,
        cast(sum(backflow_retention_paysum7_rate) as decimal(18, 2)) backflow_retention_paysum7_rate,
        cast(sum(backflow_retention_paynum14) as decimal(18, 2)) backflow_retention_paynum14,
        cast(sum(backflow_retention_paysum14) as decimal(18, 2)) backflow_retention_paysum14,
        cast(sum(backflow_retention_paysum14_rate) as decimal(18, 2)) backflow_retention_paysum14_rate,
        cast(sum(backflow_retention_paynum14up) as decimal(18, 2)) backflow_retention_paynum14up,
        cast(sum(backflow_retention_paysum14up) as decimal(18, 2)) backflow_retention_paysum14up,
        cast(sum(backflow_retention_paysum14up_rate) as decimal(18, 2)) backflow_retention_paysum14up_rate,
        cast(sum(backflow1_retention_1day_user_cnt) as decimal(18, 2)) backflow1_retention_1day_user_cnt,
        cast(sum(backflow3_retention_1day_user_cnt) as decimal(18, 2)) backflow3_retention_1day_user_cnt,
        cast(sum(backflow7_retention_1day_user_cnt) as decimal(18, 2)) backflow7_retention_1day_user_cnt,
        cast(sum(backflow14_retention_1day_user_cnt) as decimal(18, 2)) backflow14_retention_1day_user_cnt,
        cast(sum(backflow14up_retention_1day_user_cnt) as decimal(18, 2)) backflow14up_retention_1day_user_cnt
        from (

        SELECT
        <if test="group != null and group != ''">
            ${group},
        </if>

        sum(backflow_retention_paynum1) backflow_retention_paynum1,
        sum(backflow_retention_paysum1) / 100 backflow_retention_paysum1,
        truncate(sum(backflow_retention_paynum1) / sum(backflow1_retention_1day_user_cnt) * 100, 2) backflow_retention_paynum1_rate,
        truncate(sum(backflow_retention_paysum1) / 100 / sum(backflow1_retention_1day_user_cnt), 2) backflow_retention_paysum1_rate,

        sum(backflow_retention_paynum3) backflow_retention_paynum3,
        sum(backflow_retention_paysum3) / 100 backflow_retention_paysum3,
        truncate(sum(backflow_retention_paynum3) / sum(backflow3_retention_1day_user_cnt) * 100, 2) backflow_retention_paynum3_rate,
        truncate(sum(backflow_retention_paysum3) / 100 / sum(backflow3_retention_1day_user_cnt), 2) backflow_retention_paysum3_rate,

        sum(backflow_retention_paynum7) backflow_retention_paynum7,
        sum(backflow_retention_paysum7) / 100 backflow_retention_paysum7,
        truncate(sum(backflow_retention_paynum7) / sum(backflow7_retention_1day_user_cnt) * 100, 2) backflow_retention_paynum7_rate,
        truncate(sum(backflow_retention_paysum7) / 100 / sum(backflow7_retention_1day_user_cnt), 2) backflow_retention_paysum7_rate,

        sum(backflow_retention_paynum14) backflow_retention_paynum14,
        sum(backflow_retention_paysum14) / 100 backflow_retention_paysum14,
        truncate(sum(backflow_retention_paynum14) / sum(backflow14_retention_1day_user_cnt) * 100, 2) backflow_retention_paynum14_rate,
        truncate(sum(backflow_retention_paysum14) / 100 / sum(backflow14_retention_1day_user_cnt), 2) backflow_retention_paysum14_rate,

        sum(backflow_retention_paynum14up) backflow_retention_paynum14up,
        sum(backflow_retention_paysum14up) / 100 backflow_retention_paysum14up,
        truncate(sum(backflow_retention_paynum14up) / sum(backflow14up_retention_1day_user_cnt) * 100, 2) backflow_retention_paynum14up_rate,
        truncate(sum(backflow_retention_paysum14up) / 100 / sum(backflow14up_retention_1day_user_cnt), 2) backflow_retention_paysum14up_rate,

        TRUNCATE( sum( backflow1_retention_2day_user_cnt ) / sum( backflow1_retention_1day_user_cnt ) * 100, 2 ) secondRetentionRate1,
        TRUNCATE( sum( backflow3_retention_2day_user_cnt ) / sum( backflow3_retention_1day_user_cnt ) * 100, 2 ) secondRetentionRate3,
        TRUNCATE( sum( backflow7_retention_2day_user_cnt ) / sum( backflow7_retention_1day_user_cnt ) * 100, 2 ) secondRetentionRate7,
        TRUNCATE( sum( backflow14_retention_2day_user_cnt ) / sum( backflow14_retention_1day_user_cnt ) * 100, 2 ) secondRetentionRate14,
        TRUNCATE( sum( backflow14up_retention_2day_user_cnt ) / sum( backflow14up_retention_1day_user_cnt ) * 100, 2 ) secondRetentionRate14above,

        sum( backflow1_retention_1day_user_cnt ) backflow1_retention_1day_user_cnt,
        TRUNCATE(sum( backflow1_retention_3day_user_cnt ) / sum( backflow1_retention_1day_user_cnt ) * 100, 2) backflow1_retention_3day_user_cnt,
        TRUNCATE(sum( backflow1_retention_7day_user_cnt )  / sum( backflow1_retention_1day_user_cnt ) * 100, 2) backflow1_retention_7day_user_cnt,
        TRUNCATE(sum( backflow1_retention_14day_user_cnt )  / sum( backflow1_retention_1day_user_cnt ) * 100, 2) backflow1_retention_14day_user_cnt,
        TRUNCATE(sum( backflow1_retention_30day_user_cnt )  / sum( backflow1_retention_1day_user_cnt ) * 100, 2) backflow1_retention_30day_user_cnt,

        sum( backflow3_retention_1day_user_cnt ) backflow3_retention_1day_user_cnt,
        TRUNCATE( sum( backflow3_retention_3day_user_cnt ) / sum( backflow3_retention_1day_user_cnt ) * 100, 2 ) backflow3_retention_3day_user_cnt,
        TRUNCATE( sum( backflow3_retention_7day_user_cnt ) / sum( backflow3_retention_1day_user_cnt ) * 100, 2 ) backflow3_retention_7day_user_cnt,
        TRUNCATE( sum( backflow3_retention_14day_user_cnt ) / sum( backflow3_retention_1day_user_cnt ) * 100, 2 ) backflow3_retention_14day_user_cnt,
        TRUNCATE( sum( backflow3_retention_30day_user_cnt ) / sum( backflow3_retention_1day_user_cnt ) * 100, 2 ) backflow3_retention_30day_user_cnt,

        sum( backflow7_retention_1day_user_cnt ) backflow7_retention_1day_user_cnt,
        TRUNCATE( sum( backflow7_retention_3day_user_cnt ) / sum( backflow7_retention_1day_user_cnt ) * 100, 2 ) backflow7_retention_3day_user_cnt,
        TRUNCATE( sum( backflow7_retention_7day_user_cnt ) / sum( backflow7_retention_1day_user_cnt ) * 100, 2 ) backflow7_retention_7day_user_cnt,
        TRUNCATE( sum( backflow7_retention_14day_user_cnt ) / sum( backflow7_retention_1day_user_cnt ) * 100, 2 ) backflow7_retention_14day_user_cnt,
        TRUNCATE( sum( backflow7_retention_30day_user_cnt ) / sum( backflow7_retention_1day_user_cnt ) * 100, 2 ) backflow7_retention_30day_user_cnt,

        sum( backflow14_retention_1day_user_cnt ) backflow14_retention_1day_user_cnt,
        TRUNCATE( sum( backflow14_retention_3day_user_cnt ) / sum( backflow14_retention_1day_user_cnt ) * 100, 2 ) backflow14_retention_3day_user_cnt,
        TRUNCATE( sum( backflow14_retention_7day_user_cnt ) / sum( backflow14_retention_1day_user_cnt ) * 100, 2 ) backflow14_retention_7day_user_cnt,
        TRUNCATE( sum( backflow14_retention_14day_user_cnt ) / sum( backflow14_retention_1day_user_cnt ) * 100, 2 ) backflow14_retention_14day_user_cnt,
        TRUNCATE( sum( backflow14_retention_30day_user_cnt ) / sum( backflow14_retention_1day_user_cnt ) * 100, 2 ) backflow14_retention_30day_user_cnt,

        sum( backflow14up_retention_1day_user_cnt ) backflow14up_retention_1day_user_cnt,
        TRUNCATE( sum( backflow14up_retention_3day_user_cnt ) / sum( backflow14up_retention_1day_user_cnt ) * 100, 2 ) backflow14up_retention_3day_user_cnt,
        TRUNCATE( sum( backflow14up_retention_7day_user_cnt ) / sum( backflow14up_retention_1day_user_cnt ) * 100, 2 ) backflow14up_retention_7day_user_cnt,
        TRUNCATE( sum( backflow14up_retention_14day_user_cnt ) / sum( backflow14up_retention_1day_user_cnt ) * 100, 2 ) backflow14up_retention_14day_user_cnt,
        TRUNCATE( sum( backflow14up_retention_30day_user_cnt ) / sum( backflow14up_retention_1day_user_cnt ) * 100, 2 ) backflow14up_retention_30day_user_cnt
        FROM
        ads_backflow_user_retention_analysis_daily

        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                tdate BETWEEN #{start_date}
                AND #{end_date}
            </if>


            <if test="channelList != null and channelList.size > 0">
                AND download_channel IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

        </where>
        <if test="group != null and group != ''">
            GROUP BY ${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                ORDER BY ${order_str}
            </when>
            <otherwise>
                ORDER BY tdate, appid DESC
            </otherwise>
        </choose>

                          ) a

    </select>

    <select id="selectAdsBackflowUserRetentionAnalysisDailyMap" parameterType="com.wbgame.common.GeneralReportParam"
        resultMap= "adsBackFlowMap">


        SELECT
           tdate,

        truncate(sum(backflow_retention_paynum1) / sum(backflow1_retention_1day_user_cnt) * 100, 2) backflow_retention_paynum1_rate,
        truncate(sum(backflow_retention_paysum1) / sum(backflow1_retention_1day_user_cnt) / 100, 2) backflow_retention_paysum1_rate,

        truncate(sum(backflow_retention_paynum3) / sum(backflow3_retention_1day_user_cnt) * 100, 2) backflow_retention_paynum3_rate,
        truncate(sum(backflow_retention_paysum3) / sum(backflow3_retention_1day_user_cnt) / 100, 2) backflow_retention_paysum3_rate,

        truncate(sum(backflow_retention_paynum7) / sum(backflow7_retention_1day_user_cnt) * 100, 2) backflow_retention_paynum7_rate,
        truncate(sum(backflow_retention_paysum7) / sum(backflow7_retention_1day_user_cnt) / 100, 2) backflow_retention_paysum7_rate,

        truncate(sum(backflow_retention_paynum14) / sum(backflow14_retention_1day_user_cnt) * 100, 2) backflow_retention_paynum14_rate,
        truncate(sum(backflow_retention_paysum14) / sum(backflow14_retention_1day_user_cnt) / 100, 2) backflow_retention_paysum14_rate,

        truncate(sum(backflow_retention_paynum14up) / sum(backflow14up_retention_1day_user_cnt) * 100, 2) backflow_retention_paynum14up_rate,
        truncate(sum(backflow_retention_paysum14up) / sum(backflow14up_retention_1day_user_cnt) / 100, 2) backflow_retention_paysum14up_rate,
        sum(backflow1_retention_1day_user_cnt) backflow1_retention_1day_user_cnt,
        sum(backflow3_retention_1day_user_cnt) backflow3_retention_1day_user_cnt,
        sum(backflow7_retention_1day_user_cnt) backflow7_retention_1day_user_cnt,
        sum(backflow14_retention_1day_user_cnt) backflow14_retention_1day_user_cnt,
        sum(backflow14up_retention_1day_user_cnt) backflow14up_retention_1day_user_cnt
        FROM
            ads_backflow_user_retention_analysis_daily

        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                tdate BETWEEN #{start_date}
                AND #{end_date}
            </if>


            <if test="channelList != null and channelList.size > 0">
                AND download_channel IN
                <foreach collection="channelList" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>

            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

        </where>

        GROUP BY tdate

        <choose>
            <when test="order_str != null and order_str != ''">
                ORDER BY ${order_str}
            </when>
            <otherwise>
                ORDER BY tdate, appid DESC
            </otherwise>
        </choose>

    </select>

</mapper>