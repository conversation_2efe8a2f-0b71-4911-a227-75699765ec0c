<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.AdsWangzhuanDataMapper">

    <select id="queryList" resultType="com.wbgame.pojo.adv2.bigdata.AdsWangzhuanDataVo">
        SELECT
        <if test="group != null and group != ''">
            <choose>
                <when test="group.contains('appid')">
                    ${group},app_name,
                </when>
                <otherwise>
                    ${group},
                </otherwise>
            </choose>
        </if>
        sum(dau) dau,
        sum( reg_users ) reg_users,
        round(sum( reg_users ) / sum(dau) * 100,2) reg_rate,
        sum( total_launch_cnt ) total_launch_cnt,
        round(sum( total_launch_cnt ) / sum(dau) ,2) total_launch_cnt_rate,
        sum( pendding_cnt ) pendding_cnt,
        sum( wapper_cnt ) wapper_cnt,
        sum( jump_launch_cnt ) jump_launch_cnt,
        sum( widget_launch_cnt ) widget_launch_cnt,
        sum( direct_launch_cnt ) direct_launch_cnt,
        sum( behaivor_cnt ) behaivor_cnt,
        sum( tw_splash_ad_cnt ) tw_splash_ad_cnt,
        sum( game_ad_cnt ) game_ad_cnt,

        CONVERT(sum( backflow_pv ) / sum(dau),DECIMAL(10, 2)) backflow_pv_dau,
        CONVERT(sum( backflow_ad_revenue ) / sum(backflow_users),DECIMAL(10, 2)) backflow_arpu,
        CONVERT(sum( backflow_ad_revenue ) / sum(backflow_pv) * 1000,DECIMAL(10, 2)) backflow_ecpm,
        CONVERT(sum( backflow_pv )/sum(backflow_users),DECIMAL(10, 2)) backflow_pv,
        CONVERT(sum( backflow_ad_revenue ) / sum(dau) ,DECIMAL(10, 2)) backflow_ad_revenue_dau,

        round(sum( pendding_cnt ) / sum( dau ),2) pendding_avg_rate,
        round(sum( wapper_cnt ) / sum( dau ),2) wapper_avg_rate,
        round(sum( jump_launch_cnt ) / sum( dau ),2) jump_launch_avg_rate,
        round(sum( widget_launch_cnt ) / sum( dau ),2) widget_launch_avg_rate,
        round(sum( direct_launch_cnt ) / sum( dau ),2) direct_launch_avg_rate,
        round(sum( behaivor_cnt ) / sum( dau ),2) behaivor_avg_rate,
        round(sum( tw_splash_ad_cnt ) / sum( dau ),2) tw_splash_ad_avg_rate,
        round(sum( game_ad_cnt ) / sum( dau ),2) game_ad_avg_rate,

        round(sum( pendding_cnt ) / sum( total_launch_cnt ) * 100,2) pendding_rate,
        round(sum( wapper_cnt ) / sum( total_launch_cnt ) * 100,2) wapper_rate,
        round(sum( jump_launch_cnt ) / sum( total_launch_cnt ) * 100,2) jump_launch_rate,
        round(sum( widget_launch_cnt ) / sum( total_launch_cnt ) * 100,2) widget_launch_rate,
        round(sum( direct_launch_cnt ) / sum( total_launch_cnt ) * 100,2) direct_launch_rate
        FROM
        (
        SELECT
            a.tdate,
            a.appid,
            a.app_name,
            a.channel_type,
            a.channel,
            a.refluxVersion,
            a.pid,a.os_ver,
            <if test="group != null and group != '' and group.contains('action_type')">
                a.action_type,
            </if>
            b.dau,
            b.reg_users,
            a.total_launch_cnt,
            a.pendding_cnt,
            a.wapper_cnt,
            a.jump_launch_cnt,
            a.widget_launch_cnt,
            a.direct_launch_cnt,
            a.behaivor_cnt,
            a.tw_splash_ad_cnt,
            a.game_ad_cnt,
            a.backflow_users,
            a.backflow_pv,
            a.backflow_ad_revenue
        FROM (
            SELECT
                tdate,
                appid,
                app_name,
                channel_type,
                channel,
                refluxVersion,
                pid,os_ver,
                <if test="group != null and group != '' and group.contains('action_type')">
                    action_type,
                </if>
                sum( total_launch_cnt ) total_launch_cnt,
                sum( pendding_cnt ) pendding_cnt,
                sum( wapper_cnt ) wapper_cnt,
                sum( jump_launch_cnt ) jump_launch_cnt,
                sum( widget_launch_cnt ) widget_launch_cnt,
                sum( direct_launch_cnt ) direct_launch_cnt,
                sum( behaivor_cnt ) behaivor_cnt,
                sum( tw_splash_ad_cnt ) tw_splash_ad_cnt,
                sum( game_ad_cnt ) game_ad_cnt,
                sum(backflow_ad_revenue) backflow_ad_revenue,
                sum(backflow_pv) backflow_pv,
                sum(backflow_users) backflow_users
            FROM (
            SELECT tdate, appid,app_name, channel, channel_type ,pid,os_ver,user_type, tw_splash_ad_cnt,refluxVersion,
            sum( total_launch_cnt) total_launch_cnt, sum( pendding_cnt ) pendding_cnt, sum( wapper_cnt ) wapper_cnt,
            sum( jump_launch_cnt ) jump_launch_cnt, sum( widget_launch_cnt ) widget_launch_cnt,
            sum( direct_launch_cnt ) direct_launch_cnt, sum( behaivor_cnt ) behaivor_cnt,sum( game_ad_cnt ) game_ad_cnt,
            sum(backflow_ad_revenue) backflow_ad_revenue,sum(backflow_pv) backflow_pv,sum(backflow_users) backflow_users
            FROM ads_wangzhuan_product_out_data_summay_daily a
            left join (SELECT project_id,version refluxVersion FROM dnwx_bi.wbgui_project_sdk_relation where sdk_name='小组件模块') b on a.pid = b.project_id
            <where>
                <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                    tdate between REPLACE(#{startDate}, '-', '') and REPLACE(#{endDate}, '-', '')
                </if>
                <if test="appid != null and appid != ''">
                    and appid in (${appid})
                </if>
                <if test="channel_type != null and channel_type != ''">
                    and channel_type = #{channel_type}
                </if>
                <if test="channel != null and channel != ''">
                    and channel in (${channel})
                </if>
                <if test="pid != null and pid != ''">
                    and pid like concat('%',#{pid},'%')
                </if>
                <if test="os_ver != null and os_ver != ''">
                    and os_ver in (${os_ver})
                </if>
                <if test="action_type != null and action_type != ''">
                    and nvl(action_type, '') in (${action_type})
                </if>
                <if test="refluxVersion != null and refluxVersion != ''">
                    and refluxVersion in (${refluxVersion})
                </if>
            </where>
            GROUP BY tdate, appid, channel, channel_type,pid,os_ver,user_type,refluxVersion
            ) t1
            GROUP BY
                tdate,
                appid,
                channel,
                channel_type,
                refluxVersion
                ,pid,os_ver
                <if test="group != null and group != '' and group.contains('action_type')">
                    ,action_type
                </if>
            ) a
            LEFT JOIN (
            SELECT
            REPLACE(t_date, '-', '') tdate,
            appid,
            download_channel,
            pid,
            os_ver,
            sum( act_users ) dau,
            sum( new_users ) reg_users
            FROM
            ads_dim_users_info_5d_os_ver_hourly
            <where>
                <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                    t_date between #{startDate} and #{endDate}
                </if>
                <if test="appid != null and appid != ''">
                    and appid in (${appid})
                </if>
                <if test="channel != null and channel != ''">
                    and download_channel in (${channel})
                </if>
                <if test="pid != null and pid != ''">
                    and pid like concat('%',#{pid},'%')
                </if>
                <if test="os_ver != null and os_ver != ''">
                    and os_ver in (${os_ver})
                </if>
            </where>
            GROUP BY
            tdate,
            appid,
            download_channel,
            pid,
            os_ver
            ) b ON a.tdate = b.tdate AND a.appid = b.appid AND a.channel = b.download_channel AND a.pid = b.pid AND a.os_ver = b.os_ver
        ) t1
        <choose>
            <when test="group != null and group != ''">
                GROUP BY ${group}
            </when>
            <otherwise>
                HAVING sum(total_launch_cnt) IS NOT NULL
            </otherwise>
        </choose>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>
    <select id="queryTotal" resultType="com.wbgame.pojo.adv2.bigdata.AdsWangzhuanDataVo">
        SELECT
        ifnull(sum(dau),0) dau,
        ifnull(sum( reg_users ),0) reg_users,
        ifnull(round(sum( reg_users ) / sum(dau) * 100,2),0) reg_rate,
        ifnull(sum( total_launch_cnt ),0) total_launch_cnt,
        ifnull(round(sum( total_launch_cnt ) / sum(dau),2),0) total_launch_cnt_rate,
        ifnull(sum( pendding_cnt ),0) pendding_cnt,
        ifnull(sum( wapper_cnt ),0) wapper_cnt,
        ifnull(sum( jump_launch_cnt ),0) jump_launch_cnt,
        ifnull(sum( widget_launch_cnt ),0) widget_launch_cnt,
        ifnull(sum( direct_launch_cnt ),0) direct_launch_cnt,
        ifnull(sum( behaivor_cnt ),0) behaivor_cnt,
        ifnull(sum( tw_splash_ad_cnt ),0) tw_splash_ad_cnt,
        ifnull(sum( game_ad_cnt ),0) game_ad_cnt,

        ifnull(CONVERT(sum( backflow_ad_revenue ) / sum(backflow_users),DECIMAL(10, 2)),0) backflow_arpu,
        ifnull(CONVERT(sum( backflow_pv )/sum(backflow_users),DECIMAL(10, 2)),0) backflow_pv,
        ifnull(CONVERT(sum( backflow_ad_revenue ) / sum(backflow_pv) * 1000,DECIMAL(10, 2)),0) backflow_ecpm,

        ifnull(CONVERT(sum( backflow_pv ) / sum(dau),DECIMAL(10, 2)),0) backflow_pv_dau,
        ifnull(CONVERT(sum( backflow_ad_revenue ) / sum(dau) ,DECIMAL(10, 2)),0) backflow_ad_revenue_dau,

        ifnull(round(sum( pendding_cnt ) / sum( dau ),2),0) pendding_avg_rate,
        ifnull(round(sum( wapper_cnt ) / sum( dau ),2),0) wapper_avg_rate,
        ifnull(round(sum( jump_launch_cnt ) / sum( dau ),2),0) jump_launch_avg_rate,
        ifnull(round(sum( widget_launch_cnt ) / sum( dau ),2),0) widget_launch_avg_rate,
        ifnull(round(sum( direct_launch_cnt ) / sum( dau ),2),0) direct_launch_avg_rate,
        ifnull(round(sum( behaivor_cnt ) / sum( dau ),2),0) behaivor_avg_rate,
        ifnull(round(sum( tw_splash_ad_cnt ) / sum( dau ),2),0) tw_splash_ad_avg_rate,
        ifnull(round(sum( game_ad_cnt ) / sum( dau ),2),0) game_ad_avg_rate,

        ifnull(round(sum( pendding_cnt ) / sum( total_launch_cnt ) * 100,2),0) pendding_rate,
        ifnull(round(sum( wapper_cnt ) / sum( total_launch_cnt ) * 100,2),0) wapper_rate,
        ifnull(round(sum( jump_launch_cnt ) / sum( total_launch_cnt ) * 100,2),0) jump_launch_rate,
        ifnull(round(sum( widget_launch_cnt ) / sum( total_launch_cnt ) * 100,2),0) widget_launch_rate,
        ifnull(round(sum( direct_launch_cnt ) / sum( total_launch_cnt ) * 100,2),0) direct_launch_rate,
        ifnull(round(sum( behaivor_cnt ) / sum( total_launch_cnt ) * 100,2),0) behaivor_cnt_rate
        FROM
        (
        SELECT
            a.tdate,
            a.channel_type,
            a.channel,
            a.pid,
            a.os_ver,
            a.refluxVersion,
            b.dau,
            b.reg_users,
            a.total_launch_cnt,
            a.pendding_cnt,
            a.wapper_cnt,
            a.jump_launch_cnt,
            a.widget_launch_cnt,
            a.direct_launch_cnt,
            a.behaivor_cnt,
            a.tw_splash_ad_cnt,
            a.game_ad_cnt,
            a.backflow_ad_revenue,
            a.backflow_pv,
            a.backflow_users
        FROM(
        select
            tdate,
            appid,
            channel_type,
            channel,
            refluxVersion,
            pid,
            os_ver,
            sum( total_launch_cnt ) total_launch_cnt,
            sum( pendding_cnt ) pendding_cnt,
            sum( wapper_cnt ) wapper_cnt,
            sum( jump_launch_cnt ) jump_launch_cnt,
            sum( widget_launch_cnt ) widget_launch_cnt,
            sum( direct_launch_cnt ) direct_launch_cnt,
            sum( behaivor_cnt ) behaivor_cnt,
            sum(tw_splash_ad_cnt) tw_splash_ad_cnt,
            sum(game_ad_cnt) game_ad_cnt,
            sum(backflow_ad_revenue) backflow_ad_revenue,
            sum(backflow_pv) backflow_pv,
            sum(backflow_users) backflow_users
        from (
            SELECT tdate, appid, channel, channel_type ,pid,os_ver,user_type, tw_splash_ad_cnt,refluxVersion,
                   sum( total_launch_cnt) total_launch_cnt, sum( pendding_cnt ) pendding_cnt, sum( wapper_cnt ) wapper_cnt,
                   sum( jump_launch_cnt ) jump_launch_cnt, sum( widget_launch_cnt ) widget_launch_cnt,
                   sum( direct_launch_cnt ) direct_launch_cnt, sum( behaivor_cnt ) behaivor_cnt,sum( game_ad_cnt ) game_ad_cnt,
                   sum(backflow_ad_revenue) backflow_ad_revenue,sum(backflow_pv) backflow_pv,sum(backflow_users) backflow_users

            FROM ads_wangzhuan_product_out_data_summay_daily a
            left join (SELECT project_id,version refluxVersion FROM dnwx_bi.wbgui_project_sdk_relation where sdk_name='小组件模块') b on a.pid = b.project_id
            <where>
              <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                  tdate between REPLACE(#{startDate}, '-', '') and REPLACE(#{endDate}, '-', '')
              </if>
              <if test="appid != null and appid != ''">
                  and appid in (${appid})
              </if>
              <if test="channel_type != null and channel_type != ''">
                  and channel_type = #{channel_type}
              </if>
              <if test="channel != null and channel != ''">
                  and channel in (${channel})
              </if>
              <if test="pid != null and pid != ''">
                  and pid like concat('%',#{pid},'%')
              </if>
              <if test="os_ver != null and os_ver != ''">
                  and os_ver in (${os_ver})
              </if>
              <if test="refluxVersion != null and refluxVersion != ''">
                  and refluxVersion in (${refluxVersion})
              </if>
              <if test="action_type != null and action_type != ''">
                  and nvl(action_type, '') in (${action_type})
              </if>
            </where>
            GROUP BY tdate, appid, channel, channel_type,pid,os_ver,user_type,refluxVersion
              ) t1
        GROUP BY
            tdate,
            appid,
            channel,
            channel_type,
            refluxVersion
            ,pid,os_ver
        ) a
        LEFT JOIN (
            SELECT
            REPLACE(t_date, '-', '') tdate,
            appid,
            download_channel,
            pid,
            os_ver,
            sum( act_users ) dau,
            sum( new_users ) reg_users
            FROM
            ads_dim_users_info_5d_os_ver_hourly
            <where>
                <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                    t_date between #{startDate} and #{endDate}
                </if>
                <if test="appid != null and appid != ''">
                    and appid in (${appid})
                </if>
                <if test="channel != null and channel != ''">
                    and download_channel in (${channel})
                </if>
                <if test="pid != null and pid != ''">
                    and pid like concat('%',#{pid},'%')
                </if>
                <if test="os_ver != null and os_ver != ''">
                    and os_ver in (${os_ver})
                </if>
            </where>
            GROUP BY
            tdate,
            appid,
            download_channel,
            pid,
            os_ver
            ) b ON a.tdate = b.tdate AND a.appid = b.appid AND a.channel = b.download_channel AND a.pid = b.pid AND a.os_ver = b.os_ver
        ) t1
    </select>

    <select id="queryOsVers" resultType="java.lang.String">
        SELECT DISTINCT os_ver FROM ads_wangzhuan_product_out_data_summay_daily WHERE os_ver IS NOT NULL ORDER BY os_ver
    </select>
    <select id="queryActionTypes" resultType="java.lang.String">
        SELECT DISTINCT action_type FROM ads_wangzhuan_product_out_data_summay_daily WHERE action_type IS NOT NULL ORDER BY action_type DESC
    </select>

    <select id="queryContainsActionList" resultType="com.wbgame.pojo.adv2.bigdata.AdsWangzhuanDataVo">
        select
        <if test="dto.group != null and dto.group != ''">
        <choose>
            <when test="dto.group.contains('appid')">
                ${dto.group},app_name,
            </when>
            <otherwise>
                ${dto.group},
            </otherwise>
        </choose>
        </if>
        a.action_type,
        dau,
        reg_users,
        round(reg_users/ dau* 100,2) reg_rate,
        a.total_launch_cnt1 total_launch_cnt,
        round(a.total_launch_cnt1 / dau ,2) total_launch_cnt_rate,
        a.pendding_cnt1  pendding_cnt,
        a.wapper_cnt1 wapper_cnt,
        a.jump_launch_cnt1  jump_launch_cnt,
        a.widget_launch_cnt1  widget_launch_cnt,
        a.direct_launch_cnt1  direct_launch_cnt,
        a.behaivor_cnt1  behaivor_cnt,
        a.game_ad_cnt1  game_ad_cnt,

        CONVERT(a.backflow_pv / dau,DECIMAL(10, 2)) backflow_pv_dau,
        CONVERT(a.backflow_ad_revenue  / a.backflow_users,DECIMAL(10, 2)) backflow_arpu,
        CONVERT(a.backflow_ad_revenue / a.backflow_pv * 1000,DECIMAL(10, 2)) backflow_ecpm,
        CONVERT(a.backflow_pv/ a.backflow_users,DECIMAL(10, 2)) backflow_pv,
        CONVERT(a.backflow_ad_revenue / dau ,DECIMAL(10, 2)) backflow_ad_revenue_dau,

        b.tw_splash_ad_cnt2 tw_splash_ad_cnt,
        round( a.pendding_cnt1  /  dau ,2) pendding_avg_rate,
        round( a.wapper_cnt1  /  dau ,2) wapper_avg_rate,
        round( a.jump_launch_cnt1  /  dau ,2) jump_launch_avg_rate,
        round( a.widget_launch_cnt1  / dau ,2) widget_launch_avg_rate,
        round( a.direct_launch_cnt1  /  dau ,2) direct_launch_avg_rate,
        round( a.behaivor_cnt1  / dau ,2) behaivor_avg_rate,
        round( a.game_ad_cnt1  / dau ,2) game_ad_avg_rate,
        round( b.tw_splash_ad_cnt2  / dau ,2) tw_splash_ad_avg_rate,
        round( a.pendding_cnt1  /  a.total_launch_cnt1  * 100,2) pendding_rate,
        round( a.wapper_cnt1  / a.total_launch_cnt1  * 100,2) wapper_rate,
        round( a.jump_launch_cnt1  /  a.total_launch_cnt1  * 100,2) jump_launch_rate,
        round( a.widget_launch_cnt1  /  a.total_launch_cnt1  * 100,2) widget_launch_rate,
        round( a.direct_launch_cnt1  /  a.total_launch_cnt1  * 100,2) direct_launch_rate from
        (SELECT
            action_type,
            sum(total_launch_cnt) total_launch_cnt1,
            sum(pendding_cnt) pendding_cnt1,
            sum(wapper_cnt) wapper_cnt1,
            sum(jump_launch_cnt) jump_launch_cnt1,
            sum(widget_launch_cnt) widget_launch_cnt1,
            sum(direct_launch_cnt) direct_launch_cnt1,
            sum(behaivor_cnt) behaivor_cnt1,
            sum(game_ad_cnt) game_ad_cnt1,
            sum(backflow_ad_revenue) backflow_ad_revenue,
            sum(backflow_pv) backflow_pv,
            sum(backflow_users) backflow_users
            <if test="dto.group != null and dto.group != ''">
                <choose>
                    <when test="dto.group.contains('appid')">
                        ,${dto.group},app_name
                    </when>
                    <otherwise>
                        ,${dto.group}
                    </otherwise>
                </choose>
            </if>
        FROM ads_wangzhuan_product_out_data_summay_daily a
        left join (SELECT project_id,version refluxVersion FROM dnwx_bi.wbgui_project_sdk_relation where sdk_name='小组件模块') b on a.pid = b.project_id
        <where>
            <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                tdate between REPLACE(#{dto.startDate}, '-', '') and REPLACE(#{dto.endDate}, '-', '')
            </if>
            <if test="dto.appid != null and dto.appid != ''">
                and appid in (${dto.appid})
            </if>
            <if test="dto.channel_type != null and dto.channel_type != ''">
                and channel_type = #{dto.channel_type}
            </if>
            <if test="dto.channel != null and dto.channel != ''">
                and channel in (${dto.channel})
            </if>
            <if test="dto.pid != null and dto.pid != ''">
                and pid like concat('%',#{dto.pid},'%')
            </if>
            <if test="dto.os_ver != null and dto.os_ver != ''">
                and os_ver in (${dto.os_ver})
            </if>
            <if test="dto.refluxVersion != null and dto.refluxVersion != ''">
                and refluxVersion in (${dto.refluxVersion})
            </if>
            <if test="dto.action_type != null and dto.action_type != ''">
                and nvl(action_type, '') in (${dto.action_type})
            </if>
        </where>
        GROUP BY
        action_type
        <if test="dto.group != null and dto.group != ''">
            ,${dto.group}
        </if>
        ) a
        left join (
        SELECT
        <if test="dto.group != null and dto.group != '' and dto.group.contains('tdate')">
            tdate tdate_b,
        </if>
        <if test="dto.group != null and dto.group != '' and dto.group.contains('appid')">
            appid appid_b,
        </if>
        <if test="dto.group != null and dto.group != '' and dto.group.contains('channel')">
            channel channel_b,
        </if>
        <if test="dto.group != null and dto.group != '' and dto.group.contains('channel_type')">
            channel_type channel_type_b,
        </if>
        <if test="dto.joinFlag != null and dto.joinFlag == 1">
            pid pid_b,
        </if>
        <if test="dto.group != null and dto.group != '' and dto.group.contains('os_ver')">
            os_ver os_ver_b,
        </if>
        <if test="dto.group != null and dto.group != '' and dto.group.contains('refluxVersion')">
            refluxVersion refluxVersion_b,
        </if>
        sum(dau) dau,
        sum( reg_users ) reg_users,
        sum(tw_splash_ad_cnt) tw_splash_ad_cnt2
        FROM
        (
        SELECT
        a.tdate,
        a.appid,
        a.channel_type,
        a.channel,
        a.pid,
        a.os_ver,
        a.refluxVersion,
        b.dau,
        b.reg_users,
        a.tw_splash_ad_cnt
        FROM (
        SELECT
        tdate,
        appid,
        channel_type,
        channel,
        pid,
        os_ver,
        refluxVersion,
        sum(tw_splash_ad_cnt) tw_splash_ad_cnt
        FROM (
            SELECT tdate, appid,app_name, channel, channel_type ,pid,os_ver,user_type, tw_splash_ad_cnt,refluxVersion
            FROM ads_wangzhuan_product_out_data_summay_daily a
            left join (SELECT project_id,version refluxVersion FROM dnwx_bi.wbgui_project_sdk_relation where sdk_name='小组件模块') b on a.pid = b.project_id
            <where>
                <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                    tdate between REPLACE(#{dto.startDate}, '-', '') and REPLACE(#{dto.endDate}, '-', '')
                </if>
                <if test="dto.appid != null and dto.appid != ''">
                    and appid in (${dto.appid})
                </if>
                <if test="dto.channel_type != null and dto.channel_type != ''">
                    and channel_type = #{dto.channel_type}
                </if>
                <if test="dto.channel != null and dto.channel != ''">
                    and channel in (${dto.channel})
                </if>
                <if test="dto.pid != null and dto.pid != ''">
                    and pid like concat('%',#{dto.pid},'%')
                </if>
                <if test="dto.os_ver != null and dto.os_ver != ''">
                    and os_ver in (${dto.os_ver})
                </if>
                <if test="dto.refluxVersion != null and dto.refluxVersion != ''">
                    and refluxVersion in (${dto.refluxVersion})
                </if>
                <if test="dto.action_type != null and dto.action_type != ''">
                    and nvl(action_type, '') in (${dto.action_type})
                </if>
            </where>
            GROUP BY tdate, appid, channel, channel_type,pid,os_ver,user_type,refluxVersion
        ) t1
        GROUP BY
        tdate,
        appid,
        channel,
        channel_type
        ,pid,os_ver
        ,refluxVersion
        ) a
        LEFT JOIN (
            SELECT
            REPLACE(t_date, '-', '') tdate,
            appid,
            download_channel,
            pid,
            os_ver,
            sum( act_users ) dau,
            sum( new_users ) reg_users
            FROM
            ads_dim_users_info_5d_os_ver_hourly
            <where>
                <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                    t_date between #{dto.startDate} and #{dto.endDate}
                </if>
                <if test="dto.appid != null and dto.appid != ''">
                    and appid in (${dto.appid})
                </if>
                <if test="dto.channel != null and dto.channel != ''">
                    and download_channel in (${dto.channel})
                </if>
                <if test="dto.pid != null and dto.pid != ''">
                    and pid like concat('%',#{dto.pid},'%')
                </if>
                <if test="dto.os_ver != null and dto.os_ver != ''">
                    and os_ver in (${dto.os_ver})
                </if>
            </where>
            GROUP BY
            tdate,
            appid,
            download_channel,
            pid,
            os_ver
            ) b ON a.tdate = b.tdate AND a.appid = b.appid AND a.channel = b.download_channel AND a.pid = b.pid AND a.os_ver = b.os_ver
        ) t1
        <choose>
            <when test="dto.group != null and dto.group != ''">
                GROUP BY ${dto.group}
            </when>
            <otherwise>
                HAVING sum(total_launch_cnt) IS NOT NULL
            </otherwise>
        </choose>
        ) b
        <foreach collection="groupList" item="field" open="on" separator=" and ">
            <if test="field.equals('tdate')">
                a.tdate = b.tdate_b
            </if>
            <if test=" field.equals('appid')">
                a.appid = b.appid_b
            </if>
            <if test="field.equals('channel')">
                a.channel = b.channel_b
            </if>
            <if test="field.equals('channel_type')">
                a.channel_type = b.channel_type_b
            </if>
            <if test="field.equals('pid')">
                a.pid = b.pid_b
            </if>
            <if test="field.equals('os_ver')">
                a.os_ver = b.os_ver_b
            </if>
            <if test="field.equals('refluxVersion')">
                a.refluxVersion = b.refluxVersion_b
            </if>
        </foreach>
        <if test="dto.order_str != null and dto.order_str != ''">
            order by ${dto.order_str}
        </if>
    </select>


    <select id="queryActionTypeList" resultType="java.lang.String">
        SELECT DISTINCT action_type FROM ads_wangzhuan_product_out_data_summay_daily a
        left join (SELECT project_id,version refluxVersion FROM dnwx_bi.wbgui_project_sdk_relation where sdk_name='小组件模块') b on a.pid = b.project_id
        <where>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                tdate between REPLACE(#{startDate}, '-', '') and REPLACE(#{endDate}, '-', '')
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel_type != null and channel_type != ''">
                and channel_type = #{channel_type}
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="pid != null and pid != ''">
                and pid like concat('%',#{pid},'%')
            </if>
            <if test="os_ver != null and os_ver != ''">
                and os_ver in (${os_ver})
            </if>
            <if test="refluxVersion != null and refluxVersion != ''">
                and refluxVersion in (${refluxVersion})
            </if>
            <if test="action_type != null and action_type != ''">
                and nvl(action_type, '') in (${action_type})
            </if>
        </where>
    </select>


</mapper>