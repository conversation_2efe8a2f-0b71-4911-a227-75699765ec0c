<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wbgame.mapper.adb.RealCostReportMapper">


    <select id="selectRealCostReport" resultType="com.wbgame.pojo.jettison.vo.RealCostReportVo">
            select
            appid,accountid,advert_id,concat(app_name,'-',id) app_name,
            groupName,groupId,media,putUser,
           <include refid="realCostReportSql"/>
            group by ${group}
            <if test="order_str != null and order_str != ''">
                order by ${order_str}
            </if>
    </select>

    <select id="countRealCostReport" resultType="com.wbgame.pojo.jettison.vo.RealCostReportVo">
            select
            <include refid="realCostReportSql"/>
    </select>

    <sql id="realCostReportSql">
        ifnull(sum(total_spend),0) total_spend,
        ifnull(sum(reported_user),0) reported_user,
        ifnull(sum(none_reported_user),0) none_reported_user,
        ifnull(sum(total_user),0) total_user,
        ifnull(sum(sub_users),0) sub_users,
        ifnull(sum(cancel_users),0) cancel_users,
        ifnull(convert(sum(total_spend)/sum(reported_user),decimal(10,2)),0) after_pay_cost,
        ifnull(convert(sum(total_spend)/sum(total_user),decimal(10,2)),0)  view_pay_cost,
        ifnull(convert(sum(cancel_users)/sum(sub_users)*100,decimal(10,2)),0)  un_sub_rate
        from
        (
        SELECT
        COALESCE(xx0.tdate, xx1.day) AS tdate,
        COALESCE(xx0.appid, xx1.app) AS appid,
        COALESCE(xx0.accountid, xx1.account) AS accountid,
        COALESCE(xx0.advert_id, xx1.campaignId) AS advert_id,
        xx1.groupName,
        xx1.groupId,
        xx1.media,
        xx1.putUser,
        xx0.reported_user,
        xx0.none_reported_user,
        xx0.total_user,
        xx0.sub_users,
        xx0.cancel_users,
        xx1.total_spend
        FROM  <include refid="costPaySql"/> xx0
        FULL OUTER JOIN <include refid="costSpendSql"/> xx1
        ON xx0.tdate = xx1.day
        AND xx0.appid = xx1.app
        AND xx0.advert_id = xx1.campaignId
        AND xx0.accountid = xx1.account
        ) as join_result
        left join dnwx_adt.app_info as c on join_result.appid=c.id
        where media = #{media}
        <if test="accountid != null and accountid != ''">
            and accountid in (${accountid})
        </if>
        <if test="putUser != null and putUser != ''">
            and putUser in (${putUser})
        </if>
        <if test="advert_id != null and advert_id != ''">
            and advert_id in (${advert_id})
        </if>
        <if test="groupName != null and groupName != ''">
            and groupName in (${groupName})
        </if>
    </sql>

    <sql id="costSpendSql">
        (select day,app,campaignId,account,sum(rebateSpend) total_spend,groupName,groupId,media,putUser
        from dnwx_adt.dn_report_spend_china
        where day <![CDATA[>=]]> #{start_date} and day <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and app in (${appid})
        </if>
        group by day,app,campaignId
        )
    </sql>

    <sql id="costPaySql">
        (select tdate,appid,accountid,advert_id,
        count(distinct if(report_type='已上报',self_user,null)) reported_user,
        count(distinct if(report_type='未上报',self_user,null)) none_reported_user,
        (count(distinct if(report_type='已上报',self_user,null))+count(distinct if(report_type='未上报',self_user,null))) total_user,
        count(distinct if(sub_type='sub_user',self_user,null)) sub_users,
        count(distinct if(cancel_type='cancel_user',self_user,null)) cancel_users
        from (
        select tt0.*,tt1.advert_id,tt1.accountid
        from
        (select tx.tdate,tx.appid,tx.user_id self_user,t0.user_id report_user_id,
        t1.user_type as 上报状态,t2.user_type 是否激活超30天,if(t1.user_type is not null,'已上报','未上报') as report_type,
        t3.sub_type,
        t3.cancel_type
        from
        (select tdate,appid,user_id
        from
        (select tdate,appid,user_id
        from
        (select tdate,appid,firstPayUser_lst as user_id,'首次付费用户' as user_type
        from
        (select tdate,appid,firstPayUser
        from dnwx_adt.self_submit_gap_statistic
        where tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        ) a
        lateral view explode(split(a.firstPayUser,',')) ws as firstPayUser_lst
        ) m0

        union all

        select tdate,appid,user_id
        from
        (select tdate,appid,historyUser_lst as user_id,'历史有付费用户' as user_type
        from
        (select tdate,appid,historyUser
        from dnwx_adt.self_submit_gap_statistic
        where tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        ) a
        lateral view explode(split(a.historyUser,',')) ws as historyUser_lst
        ) m0

        union all

        select tdate,appid,androidid as user_id
        from camera_order_user_detail
        where tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        ) mm0

        group by tdate,appid,user_id
        ) tx

        left join

        (select tdate,appid,user_id,user_type
        from
        (select tdate,appid,firstPayUser_lst as user_id,'首次付费用户' as user_type
        from
        (select tdate,appid,firstPayUser
        from dnwx_adt.self_submit_gap_statistic
        where tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        ) a
        lateral view explode(split(a.firstPayUser,',')) ws as firstPayUser_lst
        ) m0

        union all

        select tdate,appid,user_id,user_type
        from
        (select tdate,appid,historyUser_lst as user_id,'历史有付费用户' as user_type
        from
        (select tdate,appid,historyUser
        from dnwx_adt.self_submit_gap_statistic
        where tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        ) a
        lateral view explode(split(a.historyUser,',')) ws as historyUser_lst
        ) m0

        ) t0

        on tx.tdate=t0.tdate and tx.appid=t0.appid and tx.user_id=t0.user_id

        left join

        (select tdate,appid,user_id,user_type
        from
        (select tdate,appid,submitCountUser_lst as user_id,'后台已上报用户' as user_type
        from
        (select tdate,appid,submitCountUser
        from dnwx_adt.self_submit_gap_statistic
        where tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        ) a
        lateral view explode(split(a.submitCountUser,',')) ws as submitCountUser_lst
        ) m0
        ) t1

        on t0.tdate=t1.tdate and t0.appid=t1.appid and t0.user_id=t1.user_id

        left join

        (select tdate,appid,user_id,user_type
        from
        (select tdate,appid,active30DayUser_lst as user_id,'激活超30天付费用户' as user_type
        from
        (select tdate,appid,active30DayUser
        from dnwx_adt.self_submit_gap_statistic
        where tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        ) a
        lateral view explode(split(a.active30DayUser,',')) ws as active30DayUser_lst
        ) m0
        ) t2

        on t0.tdate=t2.tdate and t0.appid=t2.appid and t0.user_id=t2.user_id

        left join

        (select a.tdate,a.appid,a.androidid sub_user_id,if(a.androidid is not null,'sub_user','other') sub_type,b.androidid cancel_user_id,if(b.androidid is not null,'cancel_user','other') cancel_type,b.price cancel_price
        from
        (select tdate,appid,androidid,payname,param3,price from dnwx_adt.camera_order_user_detail
        where tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        and (length(androidid) <![CDATA[>]]> 30 or (length(androidid) <![CDATA[<]]> 30 and param3='pay'))
        ) a

        left join

        (SELECT * FROM dnwx_adt.camera_order_cancel_user_detail
        ) b

        on a.tdate=b.tdate and a.appid=b.appid and a.androidid=b.androidid
        ) t3

        on tx.tdate=t3.tdate and tx.appid=t3.appid and tx.user_id=t3.sub_user_id

        ) tt0

        left join

        (select appid,androidid,advert_id,accountid
        from
        (select appid,androidid,advert_id,accountid,row_number() over(partition by appid,androidid order by active_time desc) rn
        from dnwx_adt.self_attribution_active_user
        where active_date<![CDATA[>=]]>date_sub(current_date(),90) and active_date<![CDATA[<=]]>current_date()
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        ) a
        where rn=1
        ) tt1

        on tt0.appid=tt1.appid and tt0.self_user=tt1.androidid

        order by report_type
        ) x0

        group by  tdate,appid,accountid,advert_id

        )
    </sql>
</mapper>