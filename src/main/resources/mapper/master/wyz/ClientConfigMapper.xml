<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--
        mapper为映射的根节点，用来管理DAO接口
        namespace指定DAO接口的完整类名，表示mapper配置文件管理哪个DAO接口(包.接口名)
        mybatis会依据这个接口动态创建一个实现类去实现这个接口，而这个实现类是一个Mapper对象
    -->
<mapper namespace="com.wbgame.mapper.master.wyz.ClientConfigMapper">
    <!--
        id = "接口中的方法名"
        parameterType = "接口中传入方法的参数类型"
        resultType = "返回实体类对象：包.类名"  处理结果集 自动封装
        注意:sql语句后不要出现";"号
        查询：select标签
        增加：insert标签
        修改：update标签
        删除：delete标签
      -->

    <insert id="insertClientConfig" parameterType="com.wbgame.pojo.wyz.ClientConfigVo">
        insert into client_config (ctype, appid, cha_id, pid, cdata, creator, create_date)
        VALUES (#{config.ctype}, #{config.appid}, #{config.chaId}, #{config.pid}, #{config.cdata}, #{config.creator}, #{config.createDate})
    </insert>

    <select id="selectClientConfig" parameterType="com.wbgame.pojo.wyz.ClientConfigVo" resultType="com.wbgame.pojo.wyz.ClientConfigVo">
        select id, ctype, appid, cha_id chaId, pid, cdata, creator, date_format(create_date, '%Y-%m-%d %H:%i:%s') createDate, updater, date_format(update_date, '%Y-%m-%d %H:%i:%s') updateDate
        from client_config
        <where>
            <if test="config.id != null">
                id = #{config.id}
            </if>
            <if test="config.ctype != null and config.ctype != ''">
                and ctype = #{config.ctype}
            </if>
            <if test="config.appid != null and config.appid != ''">
                and appid = #{config.appid}
            </if>
            <if test="config.chaId != null and config.chaId != ''">
                and cha_id = #{config.chaId}
            </if>
            <if test="config.pid != null and config.pid != ''">
                and pid = #{config.pid}
            </if>
            <if test="config.cdata != null and config.cdata != ''">
                and cdata = #{config.cdata}
            </if>
        </where>
    </select>

    <delete id="deleteClientConfig" parameterType="java.lang.Integer">
        delete from client_config
        where id = #{id}
    </delete>

    <update id="updateClientConfig" parameterType="com.wbgame.pojo.wyz.ClientConfigVo">
        update client_config
        set cdata = #{config.cdata}, updater = #{config.updater}, update_date = #{config.updateDate}
        where id = #{config.id};
    </update>
</mapper>