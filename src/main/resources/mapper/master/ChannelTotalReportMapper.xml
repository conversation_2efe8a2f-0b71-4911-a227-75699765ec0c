<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.ChannelTotaReportMapper">

    <delete id="deleteChannelTotalReport">
        delete from channel_total_report where tdate between #{startTime} and #{endTime}
        <if test="channel != null and channel != ''">
            and channel = #{channel}
        </if>
    </delete>

    <insert id="batchInsertChannelTotalReport1">
        <foreach collection="list" separator=";" item="it">
            insert into channel_total_report(tdate,appid,channel,add_num,act_num,keep1,all_add_pay_num)
            values
            (#{it.tdate},#{it.appid},#{it.cname},#{it.addnum},#{it.actnum},#{it.keep_num1},#{it.pay_reg_num})
            ON DUPLICATE KEY UPDATE
            add_num = VALUES(add_num),
            act_num = VALUES(act_num),
            keep1 = VALUES(keep1),
            all_add_pay_num = VALUES(all_add_pay_num)
        </foreach>
    </insert>

    <insert id="batchInsertChannelTotalReport2">
        <foreach collection="list" separator=";" item="it">
            insert into channel_total_report(tdate,appid,channel,spend,register,installs,pay_buy_revenue_share_fre,pay_revenue1,pay_revenue7,cash_revenue1,cash_revenue7,
            add_pay_num,pay_num_24,pay_money_24,pay_revenue_share_after,shop_spend,shop_impressions,shop_clicks,shop_download,shop_installs,shop_register
            ,shop_gamePayCount,shop_payRevenue,shop_payRevenue1,shop_payRevenue3,shop_payRevenue7,shop_payRevenue30,other_spend,other_impressions,other_clicks,other_download
            ,other_installs,other_register,other_gamePayCount,other_payRevenue,other_payRevenue1,other_payRevenue3,other_payRevenue7,other_payRevenue30)
            values
            (#{it.day},#{it.app},#{it.channel1},#{it.spend},#{it.register},#{it.installs},#{it.payRevenue},#{it.payRevenue1},#{it.payRevenue7}
            ,#{it.revenue1},#{it.revenue7},#{it.addPayCount},#{it.pay_num_24},#{it.pay_money_24},#{it.pay_revenue_share_after}
            ,#{it.shop_spend},#{it.shop_impressions},#{it.shop_clicks},#{it.shop_download},#{it.shop_installs},#{it.shop_register}
            ,#{it.shop_gamePayCount},#{it.shop_payRevenue},#{it.shop_payRevenue1},#{it.shop_payRevenue3},#{it.shop_payRevenue7},#{it.shop_payRevenue30},#{it.other_spend}
            ,#{it.other_impressions},#{it.other_clicks},#{it.other_download},#{it.other_installs},#{it.other_register},#{it.other_gamePayCount},#{it.other_payRevenue}
            ,#{it.other_payRevenue1},#{it.other_payRevenue3},#{it.other_payRevenue7},#{it.other_payRevenue30}
            )
            ON DUPLICATE KEY UPDATE
            spend = VALUES(spend),
            register = VALUES(register),
            installs = VALUES(installs),
            pay_buy_revenue_share_fre = VALUES(pay_buy_revenue_share_fre),
            pay_revenue1 = VALUES(pay_revenue1),
            pay_revenue7 = VALUES(pay_revenue7),
            cash_revenue1 = VALUES(cash_revenue1),
            cash_revenue7 = VALUES(cash_revenue7),
            add_pay_num = VALUES(add_pay_num),
            pay_num_24 = VALUES(pay_num_24),
            pay_money_24 = VALUES(pay_money_24),
            pay_revenue_share_after = VALUES(pay_revenue_share_after),
            shop_spend = VALUES(shop_spend),
            shop_impressions = VALUES(shop_impressions),
            shop_clicks = VALUES(shop_clicks),
            shop_download = VALUES(shop_download),
            shop_installs = VALUES(shop_installs),
            shop_register = VALUES(shop_register),
            shop_gamePayCount = VALUES(shop_gamePayCount),
            shop_payRevenue = VALUES(shop_payRevenue),
            shop_payRevenue1 = VALUES(shop_payRevenue1),
            shop_payRevenue3 = VALUES(shop_payRevenue3),
            shop_payRevenue7 = VALUES(shop_payRevenue7),
            shop_payRevenue30 = VALUES(shop_payRevenue30),
            other_spend = VALUES(other_spend),
            other_impressions = VALUES(other_impressions),
            other_clicks = VALUES(other_clicks),
            other_download = VALUES(other_download),
            other_installs = VALUES(other_installs),
            other_register = VALUES(other_register),
            other_gamePayCount = VALUES(other_gamePayCount),
            other_payRevenue = VALUES(other_payRevenue),
            other_payRevenue1 = VALUES(other_payRevenue1),
            other_payRevenue3 = VALUES(other_payRevenue3),
            other_payRevenue7 = VALUES(other_payRevenue7),
            other_payRevenue30 = VALUES(other_payRevenue30)
        </foreach>
    </insert>


    <insert id="batchInsertChannelTotalReport9">
        <foreach collection="list" separator=";" item="it">
            insert into channel_total_report(tdate,appid,channel,spend,register,installs,pay_buy_revenue_share_fre,pay_revenue1,pay_revenue7,cash_revenue1,cash_revenue7,
            add_pay_num,pay_num_24,pay_money_24,pay_revenue_share_after,shop_spend,shop_impressions,shop_clicks,shop_download,shop_installs,shop_register
            ,shop_gamePayCount,shop_payRevenue,shop_payRevenue1,shop_payRevenue3,shop_payRevenue7,shop_payRevenue30,other_spend,other_impressions,other_clicks,other_download
            ,other_installs,other_register,other_gamePayCount,other_payRevenue,other_payRevenue1,other_payRevenue3,other_payRevenue7,other_payRevenue30,kobe_coupon,shop_kobe_coupon,other_kobe_coupon)
            values
            (#{it.day},#{it.app},#{it.channel1},#{it.spend},#{it.register},#{it.installs},#{it.payRevenue},#{it.payRevenue1},#{it.payRevenue7}
            ,#{it.revenue1},#{it.revenue7},#{it.addPayCount},#{it.pay_num_24},#{it.pay_money_24},#{it.pay_revenue_share_after}
            ,#{it.shop_spend},#{it.shop_impressions},#{it.shop_clicks},#{it.shop_download},#{it.shop_installs},#{it.shop_register}
            ,#{it.shop_gamePayCount},#{it.shop_payRevenue},#{it.shop_payRevenue1},#{it.shop_payRevenue3},#{it.shop_payRevenue7},#{it.shop_payRevenue30},#{it.other_spend}
            ,#{it.other_impressions},#{it.other_clicks},#{it.other_download},#{it.other_installs},#{it.other_register},#{it.other_gamePayCount},#{it.other_payRevenue}
            ,#{it.other_payRevenue1},#{it.other_payRevenue3},#{it.other_payRevenue7},#{it.other_payRevenue30},#{it.ticket},#{it.shop_ticket},#{it.other_ticket}
            )
            ON DUPLICATE KEY UPDATE
            kobe_coupon = VALUES(kobe_coupon),
            shop_kobe_coupon = VALUES(shop_kobe_coupon),
            other_kobe_coupon = VALUES(other_kobe_coupon)
        </foreach>
    </insert>



    <insert id="batchInsertChannelTotalReport3">
        <foreach collection="list" separator=";" item="it">
            insert into channel_total_report(tdate,appid,channel,total_advert_show,video_show,video_income,cash_revenue_after)
            values
            (#{it.date},#{it.dnappid},#{it.cha_id},#{it.total_advert_show},#{it.video_show},#{it.video_income},#{it.sub_revenue})
            ON DUPLICATE KEY UPDATE
            total_advert_show = VALUES(total_advert_show),
            video_show = VALUES(video_show),
            video_income = VALUES(video_income),
            cash_revenue_after = VALUES(cash_revenue_after)
        </foreach>
    </insert>

    <select id="channelTotalReportList" resultType="com.wbgame.pojo.operate.ChannelTotalReportVo">
        select appid,tdate,app_category,app_name,cha_media,channel,spend,add_num,act_num,cash_revenue_after,pay_buy_revenue_share_fre
        ,pay_revenue_share_fre,pay_revenue_share_after,installs,register,all_add_pay_num,all_add_pay_num_bigdata,add_pay_num,pay_money_24,pay_num_24,pay_revenue1
        ,pay_revenue7,cash_revenue1,cash_revenue7,all_cost,all_arpu,ad_arpu,pay_arpu,concat(all_roi_share_after,'%') all_roi_share_after
        ,concat(add_rate,'%') add_rate,natural_pay_revenue_share_fre,add_cost,register_cost,concat(all_add_pay_rate,'%') all_add_pay_rate,concat(all_add_pay_rate_bigdata,'%') all_add_pay_rate_bigdata
        ,all_add_pay_cost,add_pay_arpu,concat(add_roi_24,'%') add_roi_24,add_pay_avg_24,pay_ltv_1,pay_ltv_7,concat(pay_roi_1,'%') pay_roi_1
        ,concat(pay_roi_7,'%') pay_roi_7,concat(cash_roi_1,'%') cash_roi_1,concat(cash_roi_7,'%') cash_roi_7
        ,concat(keep1,'%') keep1,concat(keep3,'%') keep3,concat(keep7,'%') keep7,concat(keep14,'%') keep14
        ,concat(add_pay_rate,'%') add_pay_rate,add_pay_cost,pay_user_num,total_advert_show,video_show,video_income,pay_arppu
        ,concat(act_pay_rate,'%') act_pay_rate,concat(add_ltv_7_rate,'%') add_ltv_7_rate
        ,avg_advert_pv,video_avg_ecpm,share_after_total_income,profit,concat(profit_rate,'%') profit_rate
        ,natural_add,natural_add_cost,natural_add_pay,natural_add_pay_cost,natural_pay_num,natural_pay_cost
        ,shop_spend,shop_impressions,shop_clicks,concat(shop_clicks_rate,'%') shop_clicks_rate,shop_download,concat(shop_download_rate,'%') shop_download_rate,shop_installs
        ,shop_installs_cost,shop_register,shop_register_cost,shop_gamePayCount,shop_gamePayCount_cost,shop_payRevenue
        ,concat(shop_pay_roi1,'%') shop_pay_roi1,concat(shop_pay_roi3,'%') shop_pay_roi3,concat(shop_pay_roi7,'%') shop_pay_roi7,concat(shop_pay_roi30,'%') shop_pay_roi30
        ,shop_payRevenue1,shop_payRevenue3,shop_payRevenue7,shop_payRevenue30,concat(shop_spend_rate,'%') shop_spend_rate,shop_download_cost,concat(shop_pay_roi,'%') shop_pay_roi
        ,other_spend,other_impressions,other_clicks,concat(other_clicks_rate,'%') other_clicks_rate,other_download,concat(other_download_rate,'%') other_download_rate,other_installs
        ,other_installs_cost,other_register,other_register_cost,other_gamePayCount,other_gamePayCount_cost,other_payRevenue
        ,concat(other_pay_roi1,'%') other_pay_roi1,concat(other_pay_roi3,'%') other_pay_roi3,concat(other_pay_roi7,'%') other_pay_roi7,concat(other_pay_roi30,'%') other_pay_roi30
        ,other_payRevenue1,other_payRevenue3,other_payRevenue7,other_payRevenue30,concat(other_spend_rate,'%') other_spend_rate,other_download_cost,concat(other_pay_roi,'%') other_pay_roi,kobe_coupon,shop_kobe_coupon,other_kobe_coupon
        from
        (select appid,tdate,app_category,app_name,cha_media,channel,ifnull(spend,0) spend,ifnull(add_num,0) add_num,ifnull(act_num,0) act_num
        ,ifnull(cash_revenue_after,0) cash_revenue_after,ifnull(pay_buy_revenue_share_fre,0) pay_buy_revenue_share_fre
        ,ifnull(pay_revenue_share_fre,0) pay_revenue_share_fre,round(ifnull(pay_revenue_share_after,0),2) pay_revenue_share_after
        ,ifnull(installs,0) installs,ifnull(register,0) register,ifnull(all_add_pay_num,0) all_add_pay_num,ifnull(all_add_pay_num_bigdata,0) all_add_pay_num_bigdata
        ,ifnull(add_pay_num,0) add_pay_num,ifnull(pay_money_24,0) pay_money_24,ifnull(pay_num_24,0) pay_num_24,ifnull(pay_revenue1,0) pay_revenue1
        ,ifnull(pay_revenue7,0) pay_revenue7,ifnull(cash_revenue1,0) cash_revenue1,ifnull(cash_revenue7,0) cash_revenue7
        ,ifnull(all_cost,0) all_cost,ifnull(ad_arpu,0) ad_arpu
        ,ifnull(add_rate,0) add_rate,ifnull(natural_pay_revenue_share_fre,0) natural_pay_revenue_share_fre
        ,ifnull(add_cost,0) add_cost,ifnull(register_cost,0) register_cost,ifnull(all_add_pay_rate,0) all_add_pay_rate,ifnull(all_add_pay_rate_bigdata,0) all_add_pay_rate_bigdata
        ,ifnull(all_add_pay_cost,0) all_add_pay_cost,ifnull(add_pay_arpu,0) add_pay_arpu,ifnull(add_roi_24,0) add_roi_24
        ,ifnull(add_pay_avg_24,0) add_pay_avg_24,ifnull(pay_ltv_1,0) pay_ltv_1,ifnull(pay_ltv_7,0) pay_ltv_7
        ,ifnull(pay_roi_1,0) pay_roi_1,ifnull(pay_roi_7,0) pay_roi_7,ifnull(cash_roi_1,0) cash_roi_1,ifnull(cash_roi_7,0) cash_roi_7
        ,ifnull(keep1,0) keep1,ifnull(keep3,0) keep3,ifnull(keep7,0) keep7,ifnull(keep14,0) keep14,ifnull(add_pay_rate,0) add_pay_rate
        ,ifnull(add_pay_cost,0) add_pay_cost
        ,ifnull(round((ifnull(cash_revenue_after,0)+ifnull(pay_revenue_share_after,0))/ifnull(act_num,0),2),0) all_arpu
        ,ifnull(round((ifnull(cash_revenue_after,0)+ifnull(pay_revenue_share_after,0))/ifnull(spend,0)*100,2),0) all_roi_share_after
        ,ifnull(round(ifnull(pay_revenue_share_after,0)/ifnull(act_num,0),2),0) pay_arpu
        ,pay_user_num,total_advert_show,video_show,video_income
        ,ifnull(round(pay_revenue_share_after/pay_user_num,2),0) pay_arppu
        ,ifnull(round(pay_user_num/act_num*100,2),0) act_pay_rate
        ,ifnull(round(pay_ltv_7/pay_ltv_1*100,2),0) add_ltv_7_rate
        ,ifnull(round(total_advert_show/act_num,2),0) avg_advert_pv
        ,ifnull(round(video_income/video_show*1000,2),0) video_avg_ecpm
        ,round(ifnull(cash_revenue_after,0)+ifnull(pay_revenue_share_after,0),2) share_after_total_income
        ,profit
        ,ifnull(round(profit/(ifnull(cash_revenue_after,0)+ifnull(pay_revenue_share_after,0))*100,2),0) profit_rate
        ,natural_add,natural_add_cost,natural_add_pay,natural_add_pay_cost,natural_pay_num,natural_pay_cost
        ,shop_spend,shop_impressions,shop_clicks,shop_clicks_rate,shop_download,shop_download_rate,shop_installs
        ,shop_installs_cost,shop_register,shop_register_cost,shop_gamePayCount,shop_gamePayCount_cost,shop_payRevenue
        ,shop_pay_roi1,shop_pay_roi3,shop_pay_roi7,shop_pay_roi30,shop_payRevenue1,shop_payRevenue3,shop_payRevenue7,shop_payRevenue30
        ,shop_spend_rate,shop_download_cost,shop_pay_roi
        ,other_spend,other_impressions,other_clicks,other_clicks_rate,other_download,other_download_rate,other_installs
        ,other_installs_cost,other_register,other_register_cost,other_gamePayCount,other_gamePayCount_cost,other_payRevenue
        ,other_pay_roi1,other_pay_roi3,other_pay_roi7,other_pay_roi30,other_payRevenue1,other_payRevenue3,other_payRevenue7,other_payRevenue30
        ,other_spend_rate,other_download_cost,other_pay_roi,kobe_coupon,shop_kobe_coupon,other_kobe_coupon
        from
        (select appid,app_category,app_name,cha_media,channel
        , <choose>
                <when test="custom_date != null and custom_date.size() > 0">
                    concat(#{start_date},'至',#{end_date}) as tdate
                </when>
                <when test="group != null and group != '' and group.contains('week')">
                    DATE_FORMAT(tdate, '%x-%v') as tdate,DATE_FORMAT(tdate, '%x-%v') week
                </when>
                <when test="group != null and group != '' and group.contains('month')">
                    DATE_FORMAT(tdate,'%Y-%m') as tdate,
                    DATE_FORMAT(tdate,'%Y-%m') as month
                </when>
                <when test="group == null or group == ''">
                    concat(#{start_date},'至',#{end_date}) as tdate
                </when>
                <when test="group != null and group != '' and group.contains('beek')">
                    CONCAT(DATE_FORMAT(tdate, '%x'),"-", LPAD(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 1,2,"0"),"-",LPAD(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 2,2,"0"))  AS tdate
                    ,CONCAT(DATE_FORMAT(tdate, '%x'),"-", LPAD(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 1,2,"0"),"-",LPAD(FLOOR((DATE_FORMAT(tdate, '%v') - 1) / 2) * 2 + 2,2,"0")) as beek
                </when>
                <when test="group != null and group != '' and group.contains('tdate')">
                    tdate
                </when>
                <otherwise>
                    concat(#{start_date},'至',#{end_date}) as tdate
                </otherwise>
          </choose>
        ,sum(spend) spend,sum(add_num) add_num,sum(act_num) act_num,sum(cash_revenue_after) cash_revenue_after,sum(pay_buy_revenue_share_fre) pay_buy_revenue_share_fre
        ,sum(pay_revenue_share_fre) pay_revenue_share_fre,sum(installs) installs,sum(register) register,sum(all_add_pay_num) all_add_pay_num,sum(all_add_pay_num_bigdata) all_add_pay_num_bigdata,sum(add_pay_num) add_pay_num
        ,sum(pay_money_24) pay_money_24,sum(pay_num_24) pay_num_24,sum(pay_revenue1) pay_revenue1,sum(pay_revenue7) pay_revenue7,sum(cash_revenue1) cash_revenue1,sum(cash_revenue7) cash_revenue7
        ,round(sum(spend)/sum(add_num),2) all_cost
        ,round(sum(cash_revenue_after)/sum(act_num),2) ad_arpu
        ,round(sum(add_num)/sum(act_num)*100,2) add_rate
        ,(ifnull(sum(pay_revenue_share_fre),0)-ifnull(sum(pay_buy_revenue_share_fre),0)) natural_pay_revenue_share_fre
        ,round(sum(spend)/sum(installs),2) add_cost
        ,round(sum(spend)/sum(register),2) register_cost
        ,round(sum(all_add_pay_num)/sum(add_num)*100,2) all_add_pay_rate
        ,round(sum(all_add_pay_num_bigdata)/sum(add_num)*100,2) all_add_pay_rate_bigdata
        ,round(sum(spend)/sum(all_add_pay_num)) all_add_pay_cost
        ,round(sum(pay_revenue1)/sum(add_pay_num)) add_pay_arpu
        ,round(sum(pay_money_24)/sum(spend)*100,2) add_roi_24
        ,round(sum(spend)/sum(pay_num_24),2) add_pay_avg_24
        ,round(sum(pay_revenue1)/sum(register),2) pay_ltv_1
        ,round(sum(pay_revenue7)/sum(register),2) pay_ltv_7
        ,round(sum(pay_revenue1)/sum(spend)*100,2) pay_roi_1
        ,round(sum(pay_revenue7)/sum(spend)*100,2) pay_roi_7
        ,round(sum(cash_revenue1)/sum(spend)*100,2) cash_roi_1
        ,round(sum(cash_revenue7)/sum(spend)*100,2) cash_roi_7
        ,ROUND(AVG(keep1),2) keep1
        ,ROUND(AVG(keep3),2) keep3
        ,ROUND(AVG(keep7),2) keep7
        ,ROUND(AVG(keep14),2) keep14
        ,round(sum(spend)/sum(add_pay_num),2) add_pay_cost
        ,ifnull(sum(pay_user_num),0) pay_user_num
        ,ifnull(sum(total_advert_show),0) total_advert_show
        ,ifnull(sum(video_show),0) video_show
        ,round(ifnull(sum(video_income),0),2) video_income
        ,(ifnull(sum(add_num),0)-ifnull(sum(installs),0)) natural_add
        ,round(sum(spend)/(ifnull(sum(add_num),0)-ifnull(sum(installs),0))) natural_add_cost
        ,(ifnull(sum(all_add_pay_num),0) - ifnull(sum(add_pay_num),0)) natural_add_pay
        ,round(sum(spend)/(ifnull(sum(all_add_pay_num),0) - ifnull(sum(add_pay_num),0))) natural_add_pay_cost
        ,(ifnull(sum(pay_user_num),0) - ifnull(sum(gamePayCount),0)) natural_pay_num
        ,round(sum(spend)/(ifnull(sum(pay_user_num),0) - ifnull(sum(gamePayCount),0))) natural_pay_cost
        ,sum(shop_spend) shop_spend,sum(shop_impressions) shop_impressions,sum(shop_clicks) shop_clicks
        ,round(sum(shop_clicks)/sum(shop_impressions)*100,2) shop_clicks_rate,sum(shop_download) shop_download
        ,round(sum(shop_download)/sum(shop_clicks)*100,2) shop_download_rate,sum(shop_installs) shop_installs
        ,round(sum(shop_spend)/sum(shop_installs),2) shop_installs_cost,sum(shop_register) shop_register
        ,round(sum(shop_spend)/sum(shop_register),2) shop_register_cost,sum(shop_gamePayCount) shop_gamePayCount
        ,round(sum(shop_spend)/sum(shop_gamePayCount),2) shop_gamePayCount_cost,sum(shop_payRevenue) shop_payRevenue
        ,round(sum(shop_payRevenue1)/sum(shop_spend)*100,2) shop_pay_roi1
        ,round(sum(shop_payRevenue3)/sum(shop_spend)*100,2) shop_pay_roi3
        ,round(sum(shop_payRevenue7)/sum(shop_spend)*100,2) shop_pay_roi7
        ,round(sum(shop_payRevenue30)/sum(shop_spend)*100,2) shop_pay_roi30
        ,sum(shop_payRevenue1) shop_payRevenue1,sum(shop_payRevenue3) shop_payRevenue3
        ,sum(shop_payRevenue7) shop_payRevenue7,sum(shop_payRevenue30) shop_payRevenue30
        ,round(sum(shop_spend)/sum(spend)*100,2) shop_spend_rate
        ,round(sum(shop_spend)/sum(shop_download),2) shop_download_cost
        ,round(sum(shop_payRevenue)/sum(shop_spend)*100,2) shop_pay_roi
        ,sum(other_spend) other_spend,sum(other_impressions) other_impressions,sum(other_clicks) other_clicks
        ,round(sum(other_clicks)/sum(other_impressions)*100,2) other_clicks_rate,sum(other_download) other_download
        ,round(sum(other_download)/sum(other_clicks)*100,2) other_download_rate,sum(other_installs) other_installs
        ,round(sum(other_spend)/sum(other_installs),2) other_installs_cost,sum(other_register) other_register
        ,round(sum(other_spend)/sum(other_register),2) other_register_cost,sum(other_gamePayCount) other_gamePayCount
        ,round(sum(other_spend)/sum(other_gamePayCount),2) other_gamePayCount_cost,sum(other_payRevenue) other_payRevenue
        ,round(sum(other_payRevenue1)/sum(other_spend)*100,2) other_pay_roi1
        ,round(sum(other_payRevenue3)/sum(other_spend)*100,2) other_pay_roi3
        ,round(sum(other_payRevenue7)/sum(other_spend)*100,2) other_pay_roi7
        ,round(sum(other_payRevenue30)/sum(other_spend)*100,2) other_pay_roi30
        ,sum(other_payRevenue1) other_payRevenue1,sum(other_payRevenue3) other_payRevenue3
        ,sum(other_payRevenue7) other_payRevenue7,sum(other_payRevenue30) other_payRevenue30
        ,round(sum(other_spend)/sum(spend)*100,2) other_spend_rate
        ,round(sum(other_spend)/sum(other_download),2) other_download_cost
        ,round(sum(other_payRevenue)/sum(other_spend)*100,2) other_pay_roi
        ,CASE channel
                        WHEN 'oppoml' THEN round(SUM(add_pay_num)/sum(register)*100,2)
                        WHEN 'vivoml' THEN round(SUM(add_pay_num)/sum(register)*100,2)
                        WHEN 'xiaomi' THEN round(SUM(add_pay_num)/sum(register)*100,2)
                        WHEN 'oppo' THEN round(SUM(add_pay_num)/sum(register)*100,2)
                        WHEN 'oppo2' THEN round(SUM(add_pay_num)/sum(register)*100,2)
                        WHEN 'vivo' THEN round(SUM(add_pay_num)/sum(register)*100,2)
                        WHEN 'xiaomiml' THEN round(SUM(add_pay_num)/sum(register)*100,2)
                        ELSE round(SUM(add_pay_num)/sum(installs)*100,2)
                    END add_pay_rate
        ,sum(pay_revenue_share_after) pay_revenue_share_after
        ,sum(kobe_coupon) kobe_coupon,sum(profit) profit
        ,sum(shop_kobe_coupon) shop_kobe_coupon,sum(other_kobe_coupon) other_kobe_coupon
         from
        (select appid,tdate,app_category,app_name,cha_media,channel,spend,add_num,act_num,cash_revenue_after,keep1,keep3,keep7,keep14,all_add_pay_num,pay_money_24,pay_num_24,pay_revenue1,pay_revenue7
        ,cash_revenue1,cash_revenue7,pay_revenue_share_fre,pay_buy_revenue_share_fre,register,installs,add_pay_num,pay_user_num,total_advert_show,video_show,video_income,all_add_pay_num_bigdata
        ,shop_spend,shop_impressions,shop_clicks,shop_download,shop_installs,shop_register,shop_gamePayCount,shop_payRevenue,shop_payRevenue1,shop_payRevenue3,shop_payRevenue7,shop_payRevenue30
        ,other_spend,other_impressions,other_clicks,other_download,other_installs,other_register,other_gamePayCount,other_payRevenue,other_payRevenue1,other_payRevenue3,other_payRevenue7,other_payRevenue30
        ,gamePayCount,kobe_coupon,pay_revenue_share_after,shop_kobe_coupon,other_kobe_coupon
        ,CASE
            WHEN channel = 'vivo' THEN round((ifnull(shop_payRevenue,0)-ifnull(shop_kobe_coupon,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))) * 0.95 * 0.5 + (ifnull(other_payRevenue,0)-ifnull(other_kobe_coupon,0)) * 0.95 * 0.9 +ifnull(cash_revenue_after,0)-ifnull(spend,0) ,2)
            ELSE round(ifnull(cash_revenue_after,0)+ifnull(pay_revenue_share_after,0)-ifnull(spend,0) - ifnull(kobe_coupon,0),2)
         END AS profit
        from
        (select appid,tdate,c.name app_category,app_name,d.cha_media,channel,spend,add_num,act_num,cash_revenue_after,keep1,keep3,keep7,keep14,all_add_pay_num,pay_money_24,pay_num_24,pay_revenue1,pay_revenue7,
        cash_revenue1,cash_revenue7,pay_revenue_share_fre,pay_buy_revenue_share_fre,register,installs,add_pay_num,pay_user_num,total_advert_show,video_show,video_income,all_add_pay_num_bigdata
        ,shop_spend,shop_impressions,shop_clicks,shop_download,shop_installs,shop_register,shop_gamePayCount,shop_payRevenue,shop_payRevenue1,shop_payRevenue3,shop_payRevenue7,shop_payRevenue30
        ,other_spend,other_impressions,other_clicks,other_download,other_installs,other_register,other_gamePayCount,other_payRevenue,other_payRevenue1,other_payRevenue3,other_payRevenue7,other_payRevenue30
        ,(ifnull(shop_gamePayCount,0) + ifnull(other_gamePayCount,0)) gamePayCount,a.kobe_coupon,a.shop_kobe_coupon,a.other_kobe_coupon
        ,CASE channel
        WHEN 'oppo' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'vivo' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'xiaomi' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'oppoml' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'oppomj' THEN ifnull(pay_revenue_share_fre*(1-0.05)*0.9,0)
        WHEN 'vivoml' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'xiaomimj' THEN ifnull(pay_revenue_share_fre*(1-0.05)*0.7,0)
        WHEN 'huawei' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'huaweiml' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'huawei2' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'huaweiml2' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'h5_huawei' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'honor' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.02)*0.5
        ELSE ifnull(pay_revenue_share_fre,0)
        END pay_revenue_share_after
        from channel_total_report a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        left join dn_channel_info d on a.channel = d.cha_id
        where tdate between #{start_date} and #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="cha_media != null and cha_media != ''">
            and cha_media in (${cha_media})
        </if>
        <if test="app_category != null and app_category != ''">
            and b.app_category in (${app_category})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        ) a
        ) a
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        ) a
        order by ${order_str} ) a
    </select>

    <select id="channelTotalReportCount" resultType="com.wbgame.pojo.operate.ChannelTotalReportVo">
        select spend,add_num,act_num,cash_revenue_after,pay_buy_revenue_share_fre
        ,pay_revenue_share_fre,round(ifnull(pay_revenue_share_after,0),2) pay_revenue_share_after,installs,register,all_add_pay_num,add_pay_num,pay_money_24,pay_num_24,pay_revenue1
        ,pay_revenue7,cash_revenue1,cash_revenue7,all_cost,ad_arpu,all_add_pay_num_bigdata,concat(all_add_pay_rate_bigdata,'%') all_add_pay_rate_bigdata
        ,concat(add_rate,'%') add_rate,natural_pay_revenue_share_fre,add_cost,register_cost,concat(all_add_pay_rate,'%') all_add_pay_rate
        ,all_add_pay_cost,add_pay_arpu,concat(add_roi_24,'%') add_roi_24,add_pay_avg_24,pay_ltv_1,pay_ltv_7,concat(pay_roi_1,'%') pay_roi_1
        ,concat(pay_roi_7,'%') pay_roi_7,concat(cash_roi_1,'%') cash_roi_1,concat(cash_roi_7,'%') cash_roi_7
        ,concat(keep1,'%') keep1,concat(keep3,'%') keep3,concat(keep7,'%') keep7,concat(keep14,'%') keep14,add_pay_cost
        ,round((cash_revenue_after+pay_revenue_share_after)/act_num,2) all_arpu
        ,round(pay_revenue_share_after/act_num,2) pay_arpu
        ,concat(round((cash_revenue_after+pay_revenue_share_after)/spend*100,2),'%') all_roi_share_after
        ,pay_user_num,total_advert_show,video_show,video_income
        ,ifnull(round(pay_revenue_share_after/pay_user_num,2),0) pay_arppu
        ,concat(ifnull(round(pay_user_num/act_num*100,2),0),'%') act_pay_rate
        ,concat(ifnull(round(pay_ltv_7/pay_ltv_1,2),0),'%') add_ltv_7_rate
        ,ifnull(round(total_advert_show/act_num,2),0) avg_advert_pv
        ,ifnull(round(video_income/video_show*1000,2),0) video_avg_ecpm
        ,round(ifnull(cash_revenue_after,0)+ifnull(pay_revenue_share_after,0),2) share_after_total_income
        ,profit
        ,concat(ifnull(round(profit/(ifnull(cash_revenue_after,0)+ifnull(pay_revenue_share_after,0))*100,2),0),'%') profit_rate
        ,natural_add,natural_add_cost,natural_add_pay,natural_add_pay_cost,natural_pay_num,natural_pay_cost
        ,shop_spend,shop_impressions,shop_clicks,concat(shop_clicks_rate,'%') shop_clicks_rate,shop_download,concat(shop_download_rate,'%') shop_download_rate,shop_installs
        ,shop_installs_cost,shop_register,shop_register_cost,shop_gamePayCount,shop_gamePayCount_cost,shop_payRevenue
        ,concat(shop_pay_roi1,'%') shop_pay_roi1,concat(shop_pay_roi3,'%') shop_pay_roi3,concat(shop_pay_roi7,'%') shop_pay_roi7,concat(shop_pay_roi30,'%') shop_pay_roi30
        ,shop_payRevenue1,shop_payRevenue3,shop_payRevenue7,shop_payRevenue30,concat(shop_spend_rate,'%') shop_spend_rate,shop_download_cost,concat(shop_pay_roi,'%') shop_pay_roi
        ,other_spend,other_impressions,other_clicks,concat(other_clicks_rate,'%') other_clicks_rate,other_download,concat(other_download_rate,'%') other_download_rate,other_installs
        ,other_installs_cost,other_register,other_register_cost,other_gamePayCount,other_gamePayCount_cost,other_payRevenue
        ,concat(other_pay_roi1,'%') other_pay_roi1,concat(other_pay_roi3,'%') other_pay_roi3,concat(other_pay_roi7,'%') other_pay_roi7,concat(other_pay_roi30,'%') other_pay_roi30
        ,other_payRevenue1,other_payRevenue3,other_payRevenue7,other_payRevenue30,concat(other_spend_rate,'%') other_spend_rate,other_download_cost,concat(other_pay_roi,'%') other_pay_roi,kobe_coupon total_kobe_coupon
        ,shop_kobe_coupon,other_kobe_coupon
        from
        (select sum(spend) spend,sum(add_num) add_num,sum(act_num) act_num,sum(cash_revenue_after) cash_revenue_after,sum(pay_buy_revenue_share_fre) pay_buy_revenue_share_fre
        ,sum(pay_revenue_share_fre) pay_revenue_share_fre,sum(installs) installs,sum(register) register,sum(all_add_pay_num) all_add_pay_num,sum(all_add_pay_num_bigdata) all_add_pay_num_bigdata,sum(add_pay_num) add_pay_num
        ,sum(pay_money_24) pay_money_24,sum(pay_num_24) pay_num_24,sum(pay_revenue1) pay_revenue1,sum(pay_revenue7) pay_revenue7,sum(cash_revenue1) cash_revenue1,sum(cash_revenue7) cash_revenue7
        ,round(sum(spend)/sum(add_num),2) all_cost
        ,round(sum(cash_revenue_after)/sum(act_num),2) ad_arpu
        ,round(sum(add_num)/sum(act_num)*100,2) add_rate
        ,round((ifnull(sum(pay_revenue_share_fre),0)-ifnull(sum(pay_buy_revenue_share_fre),0)),2) natural_pay_revenue_share_fre
        ,round(sum(spend)/sum(installs),2) add_cost
        ,round(sum(spend)/sum(register),2) register_cost
        ,round(sum(all_add_pay_num)/sum(add_num)*100,2) all_add_pay_rate
        ,round(sum(all_add_pay_num_bigdata)/sum(add_num)*100,2) all_add_pay_rate_bigdata
        ,round(sum(spend)/sum(all_add_pay_num)) all_add_pay_cost
        ,round(sum(pay_revenue1)/sum(add_pay_num)) add_pay_arpu
        ,round(sum(pay_money_24)/sum(spend)*100,2) add_roi_24
        ,round(sum(spend)/sum(pay_num_24),2) add_pay_avg_24
        ,round(sum(pay_revenue1)/sum(register),2) pay_ltv_1
        ,round(sum(pay_revenue7)/sum(register),2) pay_ltv_7
        ,round(sum(pay_revenue1)/sum(spend)*100,2) pay_roi_1
        ,round(sum(pay_revenue7)/sum(spend)*100,2) pay_roi_7
        ,round(sum(cash_revenue1)/sum(spend)*100,2) cash_roi_1
        ,round(sum(cash_revenue7)/sum(spend)*100,2) cash_roi_7
        ,ROUND(AVG(keep1),2) keep1
        ,ROUND(AVG(keep3),2) keep3
        ,ROUND(AVG(keep7),2) keep7
        ,ROUND(AVG(keep14),2) keep14
        ,round(sum(spend)/sum(add_pay_num),2) add_pay_cost
        ,ifnull(sum(pay_user_num),0) pay_user_num
        ,ifnull(sum(total_advert_show),0) total_advert_show
        ,ifnull(sum(video_show),0) video_show
        ,round(ifnull(sum(video_income),0),2) video_income
        ,sum(pay_revenue_share_after) pay_revenue_share_after
        ,(ifnull(sum(add_num),0)-ifnull(sum(installs),0)) natural_add
        ,round(sum(spend)/(ifnull(sum(add_num),0)-ifnull(sum(installs),0))) natural_add_cost
        ,(ifnull(sum(all_add_pay_num),0) - ifnull(sum(add_pay_num),0)) natural_add_pay
        ,round(sum(spend)/(ifnull(sum(all_add_pay_num),0) - ifnull(sum(add_pay_num),0))) natural_add_pay_cost
        ,(ifnull(sum(pay_user_num),0) - ifnull(sum(gamePayCount),0)) natural_pay_num
        ,round(sum(spend)/(ifnull(sum(pay_user_num),0) - ifnull(sum(gamePayCount),0))) natural_pay_cost
        ,sum(shop_spend) shop_spend,sum(shop_impressions) shop_impressions,sum(shop_clicks) shop_clicks
        ,round(sum(shop_clicks)/sum(shop_impressions)*100,2) shop_clicks_rate,sum(shop_download) shop_download
        ,round(sum(shop_download)/sum(shop_clicks)*100,2) shop_download_rate,sum(shop_installs) shop_installs
        ,round(sum(shop_spend)/sum(shop_installs),2) shop_installs_cost,sum(shop_register) shop_register
        ,round(sum(shop_spend)/sum(shop_register),2) shop_register_cost,sum(shop_gamePayCount) shop_gamePayCount
        ,round(sum(shop_spend)/sum(shop_gamePayCount),2) shop_gamePayCount_cost,sum(shop_payRevenue) shop_payRevenue
        ,round(sum(shop_payRevenue1)/sum(shop_spend)*100,2) shop_pay_roi1
        ,round(sum(shop_payRevenue3)/sum(shop_spend)*100,2) shop_pay_roi3
        ,round(sum(shop_payRevenue7)/sum(shop_spend)*100,2) shop_pay_roi7
        ,round(sum(shop_payRevenue30)/sum(shop_spend)*100,2) shop_pay_roi30
        ,sum(shop_payRevenue1) shop_payRevenue1,sum(shop_payRevenue3) shop_payRevenue3
        ,sum(shop_payRevenue7) shop_payRevenue7,sum(shop_payRevenue30) shop_payRevenue30
        ,round(sum(shop_spend)/sum(spend)*100,2) shop_spend_rate
        ,round(sum(shop_spend)/sum(shop_download),2) shop_download_cost
        ,round(sum(shop_payRevenue)/sum(shop_spend)*100,2) shop_pay_roi
        ,sum(other_spend) other_spend,sum(other_impressions) other_impressions,sum(other_clicks) other_clicks
        ,round(sum(other_clicks)/sum(other_impressions)*100,2) other_clicks_rate,sum(other_download) other_download
        ,round(sum(other_download)/sum(other_clicks)*100,2) other_download_rate,sum(other_installs) other_installs
        ,round(sum(other_spend)/sum(other_installs),2) other_installs_cost,sum(other_register) other_register
        ,round(sum(other_spend)/sum(other_register),2) other_register_cost,sum(other_gamePayCount) other_gamePayCount
        ,round(sum(other_spend)/sum(other_gamePayCount),2) other_gamePayCount_cost,sum(other_payRevenue) other_payRevenue
        ,round(sum(other_payRevenue1)/sum(other_spend)*100,2) other_pay_roi1
        ,round(sum(other_payRevenue3)/sum(other_spend)*100,2) other_pay_roi3
        ,round(sum(other_payRevenue7)/sum(other_spend)*100,2) other_pay_roi7
        ,round(sum(other_payRevenue30)/sum(other_spend)*100,2) other_pay_roi30
        ,sum(other_payRevenue1) other_payRevenue1,sum(other_payRevenue3) other_payRevenue3
        ,sum(other_payRevenue7) other_payRevenue7,sum(other_payRevenue30) other_payRevenue30
        ,round(sum(other_spend)/sum(spend)*100,2) other_spend_rate
        ,round(sum(other_spend)/sum(other_download),2) other_download_cost
        ,round(sum(other_payRevenue)/sum(other_spend)*100,2) other_pay_roi
        ,sum(kobe_coupon) kobe_coupon,sum(profit) profit
        ,sum(shop_kobe_coupon) shop_kobe_coupon,sum(other_kobe_coupon) other_kobe_coupon
        from
        (
        select spend,add_num,act_num,cash_revenue_after,keep1,keep3,keep7,keep14,all_add_pay_num,all_add_pay_num_bigdata,pay_money_24,pay_num_24,pay_revenue1,pay_revenue7
        ,cash_revenue1,cash_revenue7,pay_revenue_share_fre,pay_buy_revenue_share_fre,register,installs,add_pay_num,pay_user_num,total_advert_show,video_show,video_income
        ,shop_spend,shop_impressions,shop_clicks,shop_download,shop_installs,shop_register,shop_gamePayCount,shop_payRevenue,shop_payRevenue1,shop_payRevenue3,shop_payRevenue7,shop_payRevenue30
        ,other_spend,other_impressions,other_clicks,other_download,other_installs,other_register,other_gamePayCount,other_payRevenue,other_payRevenue1,other_payRevenue3,other_payRevenue7,other_payRevenue30
        ,gamePayCount,kobe_coupon,pay_revenue_share_after,channel,shop_kobe_coupon,other_kobe_coupon
        ,CASE
            WHEN channel = 'vivo' THEN round((ifnull(shop_payRevenue,0)-ifnull(shop_kobe_coupon,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))) * 0.95 * 0.5 + (ifnull(other_payRevenue,0)-ifnull(other_kobe_coupon,0)) * 0.95 * 0.9 +ifnull(cash_revenue_after,0)-ifnull(spend,0) ,2)
            ELSE round(ifnull(cash_revenue_after,0)+ifnull(pay_revenue_share_after,0)-ifnull(spend,0) - ifnull(kobe_coupon,0),2)
         END AS profit
        from
        (select spend,add_num,act_num,cash_revenue_after,keep1,keep3,keep7,keep14,all_add_pay_num,all_add_pay_num_bigdata,pay_money_24,pay_num_24,pay_revenue1,pay_revenue7,
        cash_revenue1,cash_revenue7,pay_revenue_share_fre,pay_buy_revenue_share_fre,register,installs,add_pay_num,pay_user_num,total_advert_show,video_show,video_income
        ,shop_spend,shop_impressions,shop_clicks,shop_download,shop_installs,shop_register,shop_gamePayCount,shop_payRevenue,shop_payRevenue1,shop_payRevenue3,shop_payRevenue7,shop_payRevenue30
        ,other_spend,other_impressions,other_clicks,other_download,other_installs,other_register,other_gamePayCount,other_payRevenue,other_payRevenue1,other_payRevenue3,other_payRevenue7,other_payRevenue30
        ,(ifnull(shop_gamePayCount,0) + ifnull(other_gamePayCount,0)) gamePayCount,a.kobe_coupon,channel,a.shop_kobe_coupon,a.other_kobe_coupon
        ,CASE channel
        WHEN 'oppo' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'vivo' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'xiaomi' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'oppoml' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'oppomj' THEN ifnull(pay_revenue_share_fre*(1-0.05)*0.9,0)
        WHEN 'vivoml' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'xiaomimj' THEN ifnull(pay_revenue_share_fre*(1-0.05)*0.7,0)
        WHEN 'huawei' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'huaweiml' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'huawei2' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'huaweiml2' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'h5_huawei' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'honor' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.02)*0.5
        ELSE ifnull(pay_revenue_share_fre,0)
        END pay_revenue_share_after
        from channel_total_report a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        left join dn_channel_info d on a.channel = d.cha_id
        where tdate between #{start_date} and #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="cha_media != null and cha_media != ''">
            and cha_media in (${cha_media})
        </if>
        <if test="app_category != null and app_category != ''">
            and b.app_category in (${app_category})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        ) a
        ) a
        ) a
    </select>

    <update id="batchUpdateChannelTotalReport4">
        <foreach collection="list" separator=";" item="it">
            update channel_total_report set
            all_add_pay_num_bigdata = #{it.pay_reg_num}
            where  tdate = #{it.tdate} and appid = #{it.appid} and channel = #{it.channel}
        </foreach>
    </update>

    <select id="selectChannelTotalReport5" resultType="com.alibaba.fastjson.JSONObject">
        select tdate,appid,cname,addnum,actnum,keep_num1,keep_num3,keep_num7,keep_num14
        from umeng_channel_total
        where tdate between #{startTime} and #{endTime}
        <if test="channel != null and channel != ''">
            and cname = #{channel}
        </if>
        GROUP BY tdate,appid,cname
    </select>

    <select id="selectChannelTotalReport6" resultType="com.alibaba.fastjson.JSONObject">
        select tdate,appid,cha_id cname,sum(ad_revenue) sub_revenue
        from dn_game_cha_revenue_total
        where tdate between #{startTime} and #{endTime}
        <if test="channel != null and channel != ''">
            and cha_id = #{channel}
        </if>
        GROUP BY tdate,appid,cha_id
    </select>

    <select id="selectChannelTotalReport8" resultType="com.alibaba.fastjson.JSONObject">
        select
        tdate,a.appid,b.channel cname,
        SUM(pay_total) pay_total,
        SUM(pay_reg_num) pay_reg_num,
        SUM(kobe_coupon) kobe_coupon
        from  adv_platform_pagedata_info a
        left join adv_platform_app_info b on a.appid = b.appid and a.platform =b.platform and a.tappid = b.tappid
        left join app_channel_config c on b.platform =c.channel and b.taccount = c.account
        where DATE(tdate) between #{startTime} and #{endTime}
        and b.channel in ('oppo','oppomj','oppoml','vivo','vivoml','xiaomi','xiaomiml','xiaomimj','huawei','huaweiml')
        <if test="channel != null and channel != ''">
            and b.channel = #{channel}
        </if>
        GROUP BY tdate,a.appid,b.channel
        having SUM(pay_total) > 0 or SUM(pay_reg_num) > 0
    </select>

    <insert id="batchInsertChannelTotalReport5">
        <foreach collection="list" separator=";" item="it">
            insert into channel_total_report(tdate,appid,channel,add_num,act_num,keep1,keep3,keep7,keep14)
            values
            (#{it.tdate},#{it.appid},#{it.cname},#{it.addnum},#{it.actnum},#{it.keep_num1},#{it.keep_num3},#{it.keep_num7},#{it.keep_num14})
            ON DUPLICATE KEY UPDATE
            add_num = VALUES(add_num),
            act_num = VALUES(act_num),
            keep1 = VALUES(keep1),
            keep3 = VALUES(keep3),
            keep7 = VALUES(keep7),
            keep14 = VALUES(keep14)
        </foreach>
    </insert>

    <insert id="batchInsertChannelTotalReport6">
        <foreach collection="list" separator=";" item="it">
            insert into channel_total_report(tdate,appid,channel,cash_revenue_after)
            values
            (#{it.tdate},#{it.appid},#{it.cname},#{it.sub_revenue})
            ON DUPLICATE KEY UPDATE
            cash_revenue_after = VALUES(cash_revenue_after)
        </foreach>
    </insert>

    <insert id="batchInsertChannelTotalReport7">
        <foreach collection="list" separator=";" item="it">
            insert into channel_total_report(tdate,appid,channel,pay_revenue_share_fre)
            values
            (#{it.tdate},#{it.appid},#{it.cname},#{it.pay_total})
            ON DUPLICATE KEY UPDATE
            pay_revenue_share_fre = VALUES(pay_revenue_share_fre)
        </foreach>
    </insert>

    <insert id="batchInsertChannelTotalReport8">
        <foreach collection="list" separator=";" item="it">
            insert into channel_total_report(tdate,appid,channel,pay_revenue_share_fre,all_add_pay_num,kobe_coupon)
            values
            (#{it.tdate},#{it.appid},#{it.cname},#{it.pay_total},#{it.pay_reg_num},#{it.kobe_coupon})
            ON DUPLICATE KEY UPDATE
            pay_revenue_share_fre = VALUES(pay_revenue_share_fre),
            all_add_pay_num = VALUES(all_add_pay_num),
            kobe_coupon = VALUES(kobe_coupon)
        </foreach>
    </insert>

    <insert id="batchInsertChannelTotalReport12">
        <foreach collection="list" separator=";" item="it">
            insert into channel_total_report(tdate,appid,channel,pay_user_num)
            values
            (#{it.tdate},#{it.appid},#{it.cname},#{it.pay_num})
            ON DUPLICATE KEY UPDATE
            pay_user_num = VALUES(pay_user_num)
        </foreach>
    </insert>

</mapper>