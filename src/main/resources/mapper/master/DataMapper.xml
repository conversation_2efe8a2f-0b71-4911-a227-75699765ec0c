<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.DataMapper">



    <delete id="deleteFilterListById" parameterType="java.lang.Long" >
        delete from dnwx_filter_list
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <resultMap id="dnwxFilterListMap" type="com.wbgame.pojo.clean.DnwxFilterListVO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="appid" property="appid" jdbcType="VARCHAR" />
        <result column="topic" property="topic" jdbcType="VARCHAR" />
        <result column="prjid" property="prjid" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    </resultMap>
    <insert id="insertFilterList" parameterType="com.wbgame.pojo.clean.DnwxFilterList" >
        insert into dnwx_filter_list
        <trim prefix="(" suffix=")" suffixOverrides="," >

            <if test="appid != null and appid != ''" >
                appid,
            </if>
            <if test="topic != null and topic != ''" >
                topic,
            </if>
            <if test="prjid != null and prjid != ''" >
                prjid,
            </if>

            <if test="createUser != null and createUser != ''" >
                create_user,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >

            <if test="appid != null and appid != ''" >
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="topic != null and topic != ''" >
                #{topic,jdbcType=VARCHAR},
            </if>
            <if test="prjid != null and prjid != ''" >
                #{prjid,jdbcType=VARCHAR},
            </if>

            <if test="createUser != null and createUser != ''" >
                #{createUser,jdbcType=VARCHAR},
            </if>

        </trim>
    </insert>

    <select id="selectFilterList" parameterType="com.wbgame.pojo.clean.DnwxFilterListQuery"
            resultMap="dnwxFilterListMap">

        select id, appid, topic, prjid, create_user, create_time from dnwx_filter_list
        <where>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>

            <if test="topic != null and topic != ''">
                and topic like #{topic} "%"
            </if>
            <if test="prjid != null and prjid != ''">
                and prjid like #{prjid} "%"
            </if>
        </where>
        order by  id desc
    </select>
</mapper>