<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.AuthMapper" >

	<insert id="batchInsertAuthInfo" parameterType="java.util.List">
		insert into dnwx_real_auth_info(pid,lsn,productid,cardid,imei,mac,name,createtime,lasttime) values
		<foreach collection="list" item="rai" separator=",">
			(#{rai.prjid},#{rai.lsn},#{rai.appid},#{rai.cardid},#{rai.imei},#{rai.mac},#{rai.name},
			#{rai.createtime},#{rai.lasttime})
		</foreach>
		ON DUPLICATE KEY UPDATE lasttime = values(lasttime)
	</insert>
	
	<insert id="batchInsertRedPackInfo" parameterType="java.util.List">
		insert into red_pack_audit_info(c_date,c_appid,c_pid,c_price,c_users,c_total,c_twoRate,c_dau) values
		<foreach collection="list" item="rai" separator=",">
			(#{rai.c_date},#{rai.c_appid},#{rai.c_pid},#{rai.c_price},#{rai.c_users},#{rai.c_total},#{rai.c_twoUsers},
			#{rai.c_dau})
		</foreach>
	</insert>
	
	<insert id="batchInsertRedPackThree" parameterType="java.util.List">
		insert into red_pack_audit_info(c_date,c_appid,c_pid,c_threeRate) values
		<foreach collection="list" item="rai" separator=",">
			(#{rai.c_date},#{rai.c_appid},#{rai.c_pid},#{rai.c_twoUsers})
		</foreach>
		ON DUPLICATE KEY UPDATE c_threeRate = values(c_threeRate)
	</insert>
	
	<insert id="batchInsertRedPackSeven" parameterType="java.util.List">
		insert into red_pack_audit_info(c_date,c_appid,c_pid,c_sevenRate) values
		<foreach collection="list" item="rai" separator=",">
			(#{rai.c_date},#{rai.c_appid},#{rai.c_pid},#{rai.c_twoUsers})
		</foreach>
		ON DUPLICATE KEY UPDATE c_sevenRate = values(c_sevenRate)
	</insert>
	
	<insert id="batchInsertRedPackInfoRealTime" parameterType="java.util.List">
		insert into ${tableName}(c_date,c_appid,c_pid,c_price,c_users,c_total,c_overUser,c_openUser,c_dau,wx_success,wx_fail,c_withdraw,c_cashUser,c_withdrawUser,c_withdrawRet) values
		<foreach collection="rPackList" item="rp" separator=",">
			(#{rp.c_date},#{rp.c_appid},#{rp.c_pid},#{rp.c_price},#{rp.c_users},#{rp.c_total},#{rp.c_overUser},#{rp.c_openUser},#{rp.c_dau},#{rp.wx_success},#{rp.wx_fail},#{rp.c_withdraw},#{rp.c_cashUser},#{rp.c_withdrawUser},#{rp.c_withdrawRet})
		</foreach>
		ON DUPLICATE KEY UPDATE c_users = values(c_users),c_total = values(c_total),c_overUser = values(c_overUser),c_openUser = values(c_openUser),c_dau = values(c_dau),wx_success = values(wx_success),wx_fail = values(wx_fail),c_withdraw = values(c_withdraw),c_cashUser = values(c_cashUser),c_withdrawUser = values(c_withdrawUser),c_withdrawRet = values(c_withdrawRet)
	</insert>
	
	<select id="selectRedPackInfoBalance" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.realAuth.RedPackInfoDto">
			select DATE_FORMAT(date,'%Y%m%d') as c_date,SUBSTR(userid,1,5) as c_appid,pid as c_pid,0.3 as c_price,
			count(distinct userid) as c_users,sum(amount) as c_total from withdraw 
			where `desc` = '已发放'
			and DATE_FORMAT(date,'%Y%m%d') = '${tdate}'
			and (amount = 1 or amount = 0.3)
			group by DATE_FORMAT(date,'%Y%m%d'),SUBSTR(userid,1,5),pid
	</select>
	
</mapper>