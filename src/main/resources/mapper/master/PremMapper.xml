<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.PremMapper">

<!--	<resultMap id="premResultMap" type="com.wbgame.pojo.WbPremConfigVo">-->
<!--		<id property="premId" column="prem_id"/>-->
<!--		<result property="premType" column="prem_type"/>-->
<!--		<result property="premMark" column="prem_mark"/>-->
<!--		<result property="menuPageDis" column="menu_page_dis"/>-->
<!--		<result property="premCon" column="prem_con"/>-->
<!--		<result property="premUser" column="prem_user"/>-->
<!--		<result property="premGroup" column="prem_group"/>-->
<!--		<result property="cuser" column="cuser"/>-->
<!--		<result property="cutime" column="cutime"/>-->
<!--		<result property="euser" column="euser"/>-->
<!--		<result property="endtime" column="endtime"/>-->
<!--	</resultMap>-->

	<select id="queryAllPrem" resultType="com.wbgame.pojo.WbPremConfigVo" parameterType="com.wbgame.pojo.WbPremConfigVo">
		SELECT * FROM main_sys_prem_v3 WHERE 1=1
		<if test="prem_id != null and prem_id != ''">
			and prem_id=#{prem_id}
		</if>
		<if test="prem_type != null and prem_type != ''">
			and prem_type=#{prem_type}
		</if>
		<if test="prem_mark != null and prem_mark != ''">
			and prem_mark=#{prem_mark}
		</if>
		<if test="prem_con != null and prem_con != ''">
			and prem_con=#{prem_con}
		</if>
		<if test="prem_user != null and prem_user != ''">
			and prem_user=#{prem_user}
		</if>
		<if test="prem_group != null and prem_group != ''">
			and prem_group=#{prem_group}
		</if>
		<if test="disable_prem_user != null and disable_prem_user != ''">
			and prem_user != #{disable_prem_user}
		</if>
		order by cutime desc
	</select>

	<select id="queryPremById" resultType="com.wbgame.pojo.WbPremConfigVo">
		SELECT * FROM main_sys_prem_v3 WHERE prem_id = #{prem_id}
	</select>

	<insert id="addPrem" parameterType="com.wbgame.pojo.WbPremConfigVo">
		INSERT INTO main_sys_prem_v3 (prem_type, prem_mark, menu_page_dis, prem_con, prem_user, prem_group, cuser, cutime)
		VALUES (#{prem_type}, #{prem_mark}, #{menu_page_dis}, #{prem_con}, #{prem_user}, #{prem_group}, #{cuser}, now())
	</insert>

	<update id="updatePrem" parameterType="com.wbgame.pojo.WbPremConfigVo">
		UPDATE main_sys_prem_v3
		SET prem_type = #{prem_type}, prem_mark = #{prem_mark}, menu_page_dis = #{menu_page_dis}, prem_con = #{prem_con}, prem_user = #{prem_user}, prem_group = #{prem_group}, euser = #{euser}, endtime = now()
		WHERE prem_id = #{prem_id}
	</update>

	<delete id="deletePrem" parameterType="com.wbgame.pojo.WbPremConfigVo">
		DELETE FROM main_sys_prem_v3 WHERE prem_id = #{prem_id}
	</delete>


</mapper>