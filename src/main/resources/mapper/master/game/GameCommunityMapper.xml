<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.game.GameDataMapper">

    <select id="selectAICustomList" parameterType="com.wbgame.pojo.game.report.query.AiCustomRequestParam"
            resultType="com.wbgame.pojo.game.report.response.AICustomResponse">
        select id,
        user_question,
        bot_answer,
        DATE(commit_date) AS tdate,
        commit_date,
        origin_qa,
        question_type,
        status,
        usercode,
        user_id,
        appid,
        app_name,
        ROUND(acc_iap_revenue/100) AS acc_iap_revenue ,
        ROUND(acc_iap_revenue_7/100) AS acc_iap_revenue_7,
        ROUND(acc_iap_revenue_30/100) AS acc_iap_revenue_30,
        day_of_30_active,
        day_of_7_active,
        round(active_time/60) AS active_time,
        round(avg_active_time_7/60) AS avg_active_time_7,
        tag_r,
        operator_status,
        update_time,
        update_user
        from yyhz_0308.game_user_question_info_hourly
        where DATE(commit_date) between #{start_date} and #{end_date}
        <if test="usercode != null and usercode != ''">
            and usercode = #{usercode}
        </if>
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="app_name != null and app_name != ''">
            and app_name like '%${app_name}%'
        </if>
        <if test="user_id != null and user_id != ''">
            and user_id = #{user_id}
        </if>
        <if test="user_question != null and user_question != ''">
            and user_question like concat('%',#{user_question},'%')
        </if>
        <if test="acc_iap_revenue != null and acc_iap_revenue != ''">
            and acc_iap_revenue >= ${acc_iap_revenue}*100
        </if>
        <if test="acc_iap_revenue_7 != null and acc_iap_revenue_7 != ''">
            and acc_iap_revenue_7 >= ${acc_iap_revenue_7}*100
        </if>
        <if test="acc_iap_revenue_30 != null and acc_iap_revenue_30 != ''">
            and acc_iap_revenue_30 >= ${acc_iap_revenue_30}*100
        </if>
        <if test="day_of_30_active != null and day_of_30_active != ''">
            and day_of_30_active >= ${day_of_30_active}
        </if>
        <if test="day_of_7_active != null and day_of_7_active != ''">
            and day_of_7_active >= ${day_of_7_active}
        </if>
        <if test="avg_active_time_7 != null and avg_active_time_7 != ''">
            and round(avg_active_time_7/60) >= ${avg_active_time_7}
        </if>
        <if test="active_time != null and active_time != ''">
            and active_time >= ${active_time}*60
        </if>

        <if test="operator_status != null and operator_status != ''">
            and operator_status = #{operator_status}
        </if>

        <if test="tag_r != null and tag_r.size() > 0">
            and tag_r in
            <foreach collection="tag_r" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>

        <!-- 告警配置时的查询条件-->
        <choose>
            <when test="config!=null and config.condition !=null and config.condition == 'and'">
                <if test="config.tag_r != null and config.tag_r != ''">
                    and tag_r in (${config.tag_r})
                </if>

                <if test="config.acc_iap_revenue_condition != null and config.acc_iap_revenue_condition != ''">
                    and acc_iap_revenue ${config.acc_iap_revenue_condition} ${config.acc_iap_revenue_value}*100
                </if>
                <if test="config.acc_iap_revenue_30_condition != null and config.acc_iap_revenue_30_condition != ''">
                    and acc_iap_revenue_30 ${config.acc_iap_revenue_30_condition} ${config.acc_iap_revenue_30_value}*100
                </if>
                <if test="config.acc_iap_revenue_7_condition != null and config.acc_iap_revenue_7_condition != ''">
                    and acc_iap_revenue_7 ${config.acc_iap_revenue_7_condition} ${config.acc_iap_revenue_7_value}*100
                </if>

                <if test="config.day_of_30_active_condition != null and config.day_of_30_active_condition != ''">
                    and day_of_30_active ${config.day_of_30_active_condition} #{config.day_of_30_active_value}
                </if>

                <if test="config.day_of_7_active_condition != null and config.day_of_7_active_condition != ''">
                    and day_of_7_active ${config.day_of_7_active_condition} #{config.day_of_7_active_value}
                </if>
                <if test="config.active_time_condition != null and config.active_time_condition != ''">
                    and active_time ${config.active_time_condition} ${config.active_time_value}*60
                </if>
                <if test="config.avg_active_time_7_condition != null and config.avg_active_time_7_condition != ''">
                    and avg_active_time_7 ${config.avg_active_time_7_condition} ${config.avg_active_time_7_value}*60
                </if>
            </when>
            <when test="config!=null and config.condition !=null and config.condition == 'or'">
                AND (
                1=0
                <if test="config.tag_r != null and config.tag_r != ''">
                    OR tag_r in (${config.tag_r})
                </if>

                <if test="config.acc_iap_revenue_condition != null and config.acc_iap_revenue_condition != ''">
                    OR acc_iap_revenue ${config.acc_iap_revenue_condition} ${config.acc_iap_revenue_value}*100
                </if>
                <if test="config.acc_iap_revenue_30_condition != null and config.acc_iap_revenue_30_condition != ''">
                    OR acc_iap_revenue_30 ${config.acc_iap_revenue_30_condition} ${config.acc_iap_revenue_30_value}*100
                </if>
                <if test="config.acc_iap_revenue_7_condition != null and config.acc_iap_revenue_7_condition != ''">
                    OR acc_iap_revenue_7 ${config.acc_iap_revenue_7_condition} ${config.acc_iap_revenue_7_value}*100
                </if>

                <if test="config.day_of_30_active_condition != null and config.day_of_30_active_condition != ''">
                    OR day_of_30_active ${config.day_of_30_active_condition} #{config.day_of_30_active_value}
                </if>

                <if test="config.day_of_7_active_condition != null and config.day_of_7_active_condition != ''">
                    OR day_of_7_active ${config.day_of_7_active_condition} #{config.day_of_7_active_value}
                </if>
                <if test="config.active_time_condition != null and config.active_time_condition != ''">
                    OR active_time ${config.active_time_condition} ${config.active_time_value}*60
                </if>
                <if test="config.avg_active_time_7_condition != null and config.avg_active_time_7_condition != ''">
                    OR avg_active_time_7 ${config.avg_active_time_7_condition} ${config.avg_active_time_7_value}*60
                </if>
                )
            </when>
        </choose>



        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>

    <select id="selectAICustomListNew" parameterType="com.wbgame.pojo.game.report.query.AiCustomRequestParam"
            resultType="com.wbgame.pojo.game.report.response.AICustomResponse">
        select id,
        user_question,
        bot_answer,
        DATE(commit_date) AS tdate,
        commit_date,
        origin_qa,
        question_type,
        status,
        usercode,
        user_id,
        ROUND(acc_iap_revenue/100) AS acc_iap_revenue ,
        ROUND(acc_iap_revenue_7/100) AS acc_iap_revenue_7,
        ROUND(acc_iap_revenue_30/100) AS acc_iap_revenue_30,
        day_of_30_active,
        day_of_7_active,
        round(active_time/60) AS active_time,
        round(avg_active_time_7/60) AS avg_active_time_7,
        tag_r,
        operator_status,
        update_time,
        update_user
        from yyhz_0308.game_user_question_info_hourly
        where commit_date between #{start_date} and #{end_date}
        <if test="usercode != null and usercode != ''">
            and usercode = #{usercode}
        </if>
        <if test="user_id != null and user_id != ''">
            and user_id = #{user_id}
        </if>
        <if test="usercode != null and usercode != ''">
            and usercode = #{usercode}
        </if>
        <if test="acc_iap_revenue != null and acc_iap_revenue != ''">
            and acc_iap_revenue >= ${acc_iap_revenue}*100
        </if>
        <if test="acc_iap_revenue_7 != null and acc_iap_revenue_7 != ''">
            and acc_iap_revenue_7 >= ${acc_iap_revenue_7}*100
        </if>
        <if test="acc_iap_revenue_30 != null and acc_iap_revenue_30 != ''">
            and acc_iap_revenue_30 >= ${acc_iap_revenue_30}*100
        </if>
        <if test="day_of_30_active != null and day_of_30_active != ''">
            and day_of_30_active >= ${day_of_30_active}
        </if>
        <if test="day_of_7_active != null and day_of_7_active != ''">
            and day_of_7_active >= ${day_of_7_active}
        </if>
        <if test="avg_active_time_7 != null and avg_active_time_7 != ''">
            and avg_active_time_7 >= ${avg_active_time_7}*60
        </if>
        <if test="active_time != null and active_time != ''">
            and active_time >= ${active_time}*60
        </if>

        <if test="operator_status != null and operator_status != ''">
            and operator_status = #{operator_status}
        </if>

        <if test="tag_r != null and tag_r.size() > 0">
            and tag_r in
            <foreach collection="tag_r" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        <if test="config != null and config.app_name != null and config.app_name != ''">
            and app_name like '%${config.app_name}%'
        </if>


        <!-- 告警配置时的查询条件-->
        <choose>
            <when test="config!=null and config.condition !=null and config.condition == 'and'">
                <if test="config.tag_r != null and config.tag_r != ''">
                    and tag_r in (${config.tag_r})
                </if>

                <if test="config.acc_iap_revenue_condition != null and config.acc_iap_revenue_condition != ''">
                    and acc_iap_revenue ${config.acc_iap_revenue_condition} ${config.acc_iap_revenue_value}*100
                </if>
                <if test="config.acc_iap_revenue_30_condition != null and config.acc_iap_revenue_30_condition != ''">
                    and acc_iap_revenue_30 ${config.acc_iap_revenue_30_condition} ${config.acc_iap_revenue_30_value}*100
                </if>
                <if test="config.acc_iap_revenue_7_condition != null and config.acc_iap_revenue_7_condition != ''">
                    and acc_iap_revenue_7 ${config.acc_iap_revenue_7_condition} ${config.acc_iap_revenue_7_value}*100
                </if>

                <if test="config.day_of_30_active_condition != null and config.day_of_30_active_condition != ''">
                    and day_of_30_active ${config.day_of_30_active_condition} #{config.day_of_30_active_value}
                </if>

                <if test="config.day_of_7_active_condition != null and config.day_of_7_active_condition != ''">
                    and day_of_7_active ${config.day_of_7_active_condition} #{config.day_of_7_active_value}
                </if>
                <if test="config.active_time_condition != null and config.active_time_condition != ''">
                    and active_time ${config.active_time_condition} ${config.active_time_value}*60
                </if>
                <if test="config.avg_active_time_7_condition != null and config.avg_active_time_7_condition != ''">
                    and avg_active_time_7 ${config.avg_active_time_7_condition} ${config.avg_active_time_7_value}*60
                </if>
            </when>
            <when test="config!=null and config.condition !=null and config.condition == 'or'">
                AND (
                1=0
                <if test="config.tag_r != null and config.tag_r != ''">
                    OR tag_r in (${config.tag_r})
                </if>

                <if test="config.acc_iap_revenue_condition != null and config.acc_iap_revenue_condition != ''">
                    OR acc_iap_revenue ${config.acc_iap_revenue_condition} ${config.acc_iap_revenue_value}*100
                </if>
                <if test="config.acc_iap_revenue_30_condition != null and config.acc_iap_revenue_30_condition != ''">
                    OR acc_iap_revenue_30 ${config.acc_iap_revenue_30_condition} ${config.acc_iap_revenue_30_value}*100
                </if>
                <if test="config.acc_iap_revenue_7_condition != null and config.acc_iap_revenue_7_condition != ''">
                    OR acc_iap_revenue_7 ${config.acc_iap_revenue_7_condition} ${config.acc_iap_revenue_7_value}*100
                </if>

                <if test="config.day_of_30_active_condition != null and config.day_of_30_active_condition != ''">
                    OR day_of_30_active ${config.day_of_30_active_condition} #{config.day_of_30_active_value}
                </if>

                <if test="config.day_of_7_active_condition != null and config.day_of_7_active_condition != ''">
                    OR day_of_7_active ${config.day_of_7_active_condition} #{config.day_of_7_active_value}
                </if>
                <if test="config.active_time_condition != null and config.active_time_condition != ''">
                    OR active_time ${config.active_time_condition} ${config.active_time_value}*60
                </if>
                <if test="config.avg_active_time_7_condition != null and config.avg_active_time_7_condition != ''">
                    OR avg_active_time_7 ${config.avg_active_time_7_condition} ${config.avg_active_time_7_value}*60
                </if>
                )
            </when>
        </choose>



        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>

    <select id="selectAICustomConfigList" parameterType="com.wbgame.pojo.game.config.query.AICustomConfigRequestParam"
            resultType="com.wbgame.pojo.game.config.response.AICustomConfigResponse">
        select * from  yyhz_0308.game_ai_custom_warn_config where 1=1

        <if test="app_name != null and app_name != ''">
            and app_name = #{app_name}
        </if>

    </select>

    <insert id="insertAICustomConfig" parameterType="com.wbgame.pojo.game.config.query.AICustomConfigRequestParam">
        insert into yyhz_0308.game_ai_custom_warn_config
        (
            app_name,chat_id,`condition`,tag_r,acc_iap_revenue_condition,acc_iap_revenue_value,acc_iap_revenue_30_condition,acc_iap_revenue_30_value,
            acc_iap_revenue_7_condition,acc_iap_revenue_7_value,day_of_30_active_condition,day_of_30_active_value,
            day_of_7_active_condition,day_of_7_active_value,active_time_condition,active_time_value,
            avg_active_time_7_condition,avg_active_time_7_value,state,create_time,create_user
        ) values (
            #{app_name},#{chat_id},#{condition},#{tag_r},#{acc_iap_revenue_condition},#{acc_iap_revenue_value},#{acc_iap_revenue_30_condition},#{acc_iap_revenue_30_value},
            #{acc_iap_revenue_7_condition},#{acc_iap_revenue_7_value},#{day_of_30_active_condition},#{day_of_30_active_value},
            #{day_of_7_active_condition},#{day_of_7_active_value},#{active_time_condition},#{active_time_value},
            #{avg_active_time_7_condition},#{avg_active_time_7_value},#{state},#{create_time},#{create_user}
        )
    </insert>

    <update id="updateAICustomConfig" parameterType="com.wbgame.pojo.game.config.query.AICustomConfigRequestParam">
        update yyhz_0308.game_ai_custom_warn_config
        set
        chat_id = #{chat_id},
        app_name = #{app_name},
        `condition` = #{condition},
        tag_r = #{tag_r},
        acc_iap_revenue_condition = #{acc_iap_revenue_condition},
        acc_iap_revenue_value = #{acc_iap_revenue_value},
        acc_iap_revenue_30_condition = #{acc_iap_revenue_30_condition},
        acc_iap_revenue_30_value = #{acc_iap_revenue_30_value},
        acc_iap_revenue_7_condition = #{acc_iap_revenue_7_condition},
        acc_iap_revenue_7_value = #{acc_iap_revenue_7_value},
        day_of_30_active_condition = #{day_of_30_active_condition},
        day_of_30_active_value = #{day_of_30_active_value},
        day_of_7_active_condition = #{day_of_7_active_condition},
        day_of_7_active_value = #{day_of_7_active_value},
        active_time_condition = #{active_time_condition},
        active_time_value = #{active_time_value},
        avg_active_time_7_condition = #{avg_active_time_7_condition},
        avg_active_time_7_value = #{avg_active_time_7_value},
        state = #{state},
        update_time = #{update_time},
        update_user = #{update_user}
        where id = #{id}
    </update>

    <delete id="deleteAICustomConfig" parameterType="java.lang.Long">
        delete from yyhz_0308.game_ai_custom_warn_config where id = #{id}
    </delete>

    <insert id="insertAICustomListBatch" parameterType="java.util.List">
        insert ignore into yyhz_0308.game_user_question_info_hourly
        (
            user_question,bot_answer,commit_date,origin_qa,question_type,status,usercode,
            user_id,acc_iap_revenue,acc_iap_revenue_7,acc_iap_revenue_30,day_of_30_active,
            day_of_7_active,active_time,avg_active_time_7,tag_r,operator_status,appid,app_name
        )
        values
        <foreach collection="list" item="item" separator=",">
        (
            #{item.user_question},#{item.bot_answer},#{item.commit_date},#{item.origin_qa},#{item.question_type},#{item.status},#{item.usercode},
            #{item.user_id},#{item.acc_iap_revenue},#{item.acc_iap_revenue_7},#{item.acc_iap_revenue_30},#{item.day_of_30_active},
            #{item.day_of_7_active},#{item.active_time},#{item.avg_active_time_7},#{item.tag_r},0,#{item.appid},#{item.app_name}
        )
        </foreach>

    </insert>

    <update id="updateAICustomState" parameterType="com.wbgame.pojo.game.report.query.AICustomHandleParam">
        update yyhz_0308.game_user_question_info_hourly
        set operator_status = #{operator_status},update_user = #{update_user},update_time = #{update_time}  where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="saveAliAccountConfig" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.wbgame.pojo.game.config.query.AliAccountConfigSaveRequestParam">
        insert into yyhz_0308.ali_account_quota_config (account, account_name, quota, sub_code, create_time, create_user, update_time, update_user, company, `type`)
        values (#{account,jdbcType=VARCHAR}, #{account_name,jdbcType=VARCHAR}, #{quota,jdbcType=BIGINT}, #{sub_code,jdbcType=VARCHAR}, #{create_time,jdbcType=VARCHAR}, #{create_user,jdbcType=VARCHAR}, #{update_time,jdbcType=VARCHAR}, #{update_user,jdbcType=VARCHAR}, #{company,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR})
    </insert>

    <delete id="deleteAliAccountConfig" parameterType="java.lang.Long">
        delete from yyhz_0308.ali_account_quota_config where id = #{id}
    </delete>

    <update id="updateAliAccountConfig" parameterType="com.wbgame.pojo.game.config.query.AliAccountConfigSaveRequestParam">
        update yyhz_0308.ali_account_quota_config
        <set>
            <if test="account_name != null and account_name != ''">
                account_name = #{account_name},
            </if>
            <if test="quota != null and quota != ''">
                quota = #{quota},
            </if>
            <if test="sub_code != null and sub_code != ''">
                sub_code = #{sub_code},
            </if>
            <if test="company != null and company != ''">
                company = #{company},
            </if>
            <if test="type != null and type != ''">
                `type` = #{type},
            </if>
            <if test="update_time != null and update_time != ''">
                update_time = #{update_time},
            </if>
            <if test="update_user != null and update_user != ''">
                update_user = #{update_user},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectAliAccountConfigListPage" parameterType="com.wbgame.pojo.game.config.query.AliAccountConfigRequestParam"
            resultType="com.wbgame.pojo.game.config.response.AliAccountConfigResponse">
        select id, account, account_name, quota, sub_code, create_time, create_user, update_time, update_user, company, `type`
        from yyhz_0308.ali_account_quota_config
        where 1=1
        <if test="account != null and account != ''">
            and account = #{account}
        </if>
        <if test="account_name != null and account_name != ''">
            and account_name like '%${account_name}%'
        </if>

        <if test="sub_code != null and sub_code != ''">
            and sub_code = #{sub_code}
        </if>

        <if test="company != null and company != ''">
            and company like '%${company}%'
        </if>

        <if test="type != null and type != ''">
            and `type` = #{type}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>

    <select id="getAliAccountConfig" parameterType="java.lang.String"
            resultType="com.wbgame.pojo.game.config.response.AliAccountConfigResponse">
        select * from yyhz_0308.ali_account_quota_config where account = #{account}
    </select>

</mapper>
