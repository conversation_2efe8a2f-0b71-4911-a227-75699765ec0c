<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.game.PayMapper">

    <select id="getPayTotalDataList" resultType="com.wbgame.pojo.game.pay.PayTotalVo">
        select DATE(createtime) tdate,appid,pid,payid,zone_id,
        ROUND(sum(money)/100,2) as price,count(distinct(imei)) as pnum from  wb_pay_info
        where createtime BETWEEN '${start_date} 00:00:00' and '${end_date} 23:59:59'
        and orderstatus='SUCCESS' and appid !='' group by DATE(createtime),appid,pid,payid,zone_id
    </select>

    <insert id="savePayTotalPidData" parameterType="com.wbgame.pojo.game.pay.PayTotalVo">
        insert ignore into pay_total_pid_info (tdate,appid,pid,payid,zone_id,price,pnum) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.tdate},#{item.appid},#{item.pid},#{item.payid},#{item.zone_id},#{item.price},#{item.pnum}
            )
        </foreach>
    </insert>

    <insert id="savePayTotalPidDauData" parameterType="com.wbgame.pojo.game.pay.PayTotalVo">
        insert ignore into pay_total_pid_dau_info (tdate,appid,pid,act_num) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.tdate},#{item.appid},#{item.pid},#{item.act_num}
            )
        </foreach>
    </insert>

    <select id="getPayTotalReportList" parameterType="com.wbgame.pojo.game.report.query.PayTotalQueryVo"
            resultType="com.wbgame.pojo.game.pay.PayTotalVo">
        select
            ${group},concat(${group}) mapkey,
            sum(price) price,sum(pnum) pnum,
            ROUND(SUM(price)/SUM(pnum),2) pay_arpu
        from (
            select a.*,c.name as app_category_name  from  pay_total_pid_info a
            left join app_info b on a.appid = b.id
            left join app_category c on b.app_category = c.id
            where tdate  <![CDATA[ >= ]]> #{start_date} and tdate <![CDATA[ <= ]]> #{end_date}
            <if test="appidList != null and appidList.size > 0">
                AND appid IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
            <if test="pid != null and pid != ''">
                and pid = #{pid}
            </if>
            <if test="zone_id != null and zone_id != ''">
                and zone_id = #{zone_id}
            </if>
            <if test="payidList != null and payidList.size > 0">
                AND payid IN
                <foreach collection="payidList" item="payid" open="(" separator="," close=")">
                    #{payid}
                </foreach>
            </if>
        ) a
        group by ${group}
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate desc
            </otherwise>
        </choose>
    </select>

    <select id="getPayTotalPidData" parameterType="com.wbgame.pojo.game.report.query.PayTotalQueryVo"
            resultType="com.wbgame.pojo.game.pay.PayTotalVo">
        select
        sum(price) price,sum(pnum) pnum,ROUND(SUM(price)/SUM(pnum),2) pay_arpu
        from (
        select a.*,c.name as app_category_name  from  pay_total_pid_info a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        where tdate  <![CDATA[ >= ]]> #{start_date} and tdate <![CDATA[ <= ]]> #{end_date}
        <if test="appidList != null and appidList.size > 0">
            AND appid IN
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="zone_id != null and zone_id != ''">
            and zone_id = #{zone_id}
        </if>
        <if test="payidList != null and payidList.size > 0">
            AND payid IN
            <foreach collection="payidList" item="payid" open="(" separator="," close=")">
                #{payid}
            </foreach>
        </if>
        ) a
    </select>

    <select id="getPayTotalPidDauList" parameterType="com.wbgame.pojo.game.report.query.PayTotalQueryVo"
            resultType="com.wbgame.pojo.game.pay.PayTotalVo">
        select ${group},concat(${group}) mapkey,sum(act_num) act_num from (
        select a.*,c.name as app_category_name from pay_total_pid_dau_info a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        where tdate  <![CDATA[ >= ]]> #{start_date} and tdate <![CDATA[ <= ]]> #{end_date}
        <if test="appidList != null and appidList.size > 0">
            AND appid IN
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        ) a group by ${group}
    </select>

    <select id="getPayTotalPidDauData" parameterType="com.wbgame.pojo.game.report.query.PayTotalQueryVo"
            resultType="com.wbgame.pojo.game.pay.PayTotalVo">
        select sum(act_num) act_num from (
        select a.*,c.name as app_category_name from pay_total_pid_dau_info a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        where tdate  <![CDATA[ >= ]]> #{start_date} and tdate <![CDATA[ <= ]]> #{end_date}
        <if test="appidList != null and appidList.size > 0">
            AND appid IN
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        ) a
    </select>

    <select id="getPayTotalAppReportList" parameterType="com.wbgame.pojo.game.report.query.PayTotalQueryVo"
            resultType="com.wbgame.pojo.game.pay.PayTotalAppVo">
        select sum(price) as price,appid from pay_total_pid_info
        where tdate  <![CDATA[ >= ]]> #{start_date} and tdate <![CDATA[ <= ]]> #{end_date}
        <if test="appidList != null and appidList.size > 0">
            AND appid IN
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        <if test="payidList != null and payidList.size > 0">
            AND payid IN
            <foreach collection="payidList" item="payid" open="(" separator="," close=")">
                #{payid}
            </foreach>
        </if>
        group by appid
    </select>

    <select id="getPayTotalPayTypeReportList" parameterType="com.wbgame.pojo.game.report.query.PayTotalQueryVo"
            resultType="com.wbgame.pojo.game.pay.PayTotalPaytypeVo">
        select sum(price) as price,payid from pay_total_pid_info
        where tdate  <![CDATA[ >= ]]> #{start_date} and tdate <![CDATA[ <= ]]> #{end_date}
        <if test="appidList != null and appidList.size > 0">
            AND appid IN
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>
        <if test="payidList != null and payidList.size > 0">
            AND payid IN
            <foreach collection="payidList" item="payid" open="(" separator="," close=")">
                #{payid}
            </foreach>
        </if>
        group by payid
    </select>

    <select id="getPayTotalDetailList" parameterType="com.wbgame.pojo.game.report.query.PayTotalInfoQueryVo"
            resultType="com.wbgame.pojo.game.pay.PayTotalInfoVo">
        select appid,pid,createtime,imei,payid,zone_id, ROUND(money/100,2) as price from wb_pay_info
        where createtime BETWEEN '${start_date} 00:00:00' and '${end_date} 23:59:59' and orderstatus='SUCCESS'
        <if test="appid != null and appid != ''">
            and appid = #{appid}
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="zone_id != null and zone_id != ''">
            and zone_id = #{zone_id}
        </if>
        <if test="payid != null and payid != ''">
            and payid = #{payid}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by createtime desc
            </otherwise>
        </choose>
    </select>

    <insert id="insertLeyingOrderToWbPayInfo">
        insert ignore into wb_pay_info (orderid,uid,money,paytype,imei,pid,appid,is_new,
        orderstatus,payname,paynote,createtime,param1,param2,param3,chaid,androidid,
        payid,userid,buy_id,pn,model,ip,version,updatetime,idfa)
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.orderid},#{item.uid},#{item.money},#{item.paytype},#{item.imei},
                #{item.pid},#{item.appid},#{item.is_new},#{item.orderstatus},#{item.payname},#{item.paynote},
                #{item.createtime},#{item.param1},#{item.param2},#{item.param3},#{item.chaid},
                #{item.androidid},#{item.payid},#{item.userid},#{item.buy_id},#{item.pn},
                #{item.model},#{item.ip},#{item.version},#{item.updatetime},#{item.idfa}
            )
        </foreach>
    </insert>

</mapper>