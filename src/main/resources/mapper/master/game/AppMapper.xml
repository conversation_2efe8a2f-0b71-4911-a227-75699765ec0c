<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.game.AppMapper">

    <select id="getAppIterationInfoList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.AppIterationInfoVo">
        SELECT DATE_FORMAT(a.date,'%Y-%m-%d %H:%i:%s') `date`,DATE_FORMAT(a.endDate,'%Y-%m-%d %H:%i:%s') endDate,
        a.pjId, a.versionName,a.infojson, b.id appid, b.app_name,c.name app_category from dnwx_client.wbgui_formconfig a
        left join yyhz_0308.app_info b on SUBSTR(a.pjId,1,5)=b.id left join yyhz_0308.app_category c on b.app_category = c.id where 1=1
        <if test="appid != null and appid != ''">
            and b.id in (${appid})
        </if>
        <if test="prjid != null and prjid != ''">
            and a.pjId =#{prjid}
        </if>
        <if test="app_name != null and app_name != ''">
            and b.app_name like '%${app_name}%'
        </if>
        <if test="versionName != null and versionName != ''">
            and a.versionName =#{versionName}
        </if>
        <if test="app_category != null and app_category != ''">
            and b.app_category =#{app_category}
        </if>
        <if test="sstart_date != null and sstart_date != ''">
            and DATE_FORMAT(a.date,'%Y-%m-%d') <![CDATA[ >= ]]> #{sstart_date} and  DATE_FORMAT(a.date,'%Y-%m-%d') <![CDATA[ <= ]]> #{send_date}
        </if>
        <if test="cstart_date != null and cstart_date != ''">
            and DATE_FORMAT(a.endDate,'%Y-%m-%d') <![CDATA[ >= ]]> #{cstart_date} and  DATE_FORMAT(a.endDate,'%Y-%m-%d') <![CDATA[ <= ]]> #{cend_date}
        </if>
        and channelTag in ('csj','zhubao')
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by DATE_FORMAT(a.date,'%Y-%m-%d %H:%i:%s') desc
            </otherwise>
        </choose>
    </select>

    <select id="getAppChannelIndexList" resultType="java.lang.String">
        select channel from channel_index_config order by `index` asc
    </select>

    <select id="getAppChannelOnlineInfoList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.AppChannelOnlineVo">

        SELECT appid,pjId prjid,channelTag channel,
        DATE_FORMAT(date, '%Y-%m-%d %H:%i:%s') create_time,
        DATE_FORMAT(endDate,'%Y-%m-%d %H:%i:%s') complete_time,
        DATE_FORMAT(OnlineDate, '%Y-%m-%d %H:%i:%s') online_time,
        f.name app_category,
        versionName version FROM (
            SELECT * from dnwx_client.wbgui_formconfig where pjId in (
                SELECT  max(pjId) from dnwx_client.wbgui_formconfig where channelTag !='' and channelTag not in ('csj','zhubao') and state !='暂停中' GROUP BY typeName,channelTag
            )
        ) a
        inner join dnwx_client.wbgui_gametype b on a.typeName =b.gameId
        left join yyhz_0308.app_info d on b.appid = d.id
        LEFT JOIN yyhz_0308.app_category f on d.app_category =f.id
        WHERE 1=1 and channelTag !=''
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="app_category != null and app_category != ''">
            and d.app_category = #{app_category}
        </if>
        GROUP BY appid,channelTag
    </select>

    <select id="getAppChannelCsjOnlineInfoList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.AppChannelOnlineVo">

        SELECT b.appid,pjId prjid,channelTag channel,
        DATE_FORMAT(date, '%Y-%m-%d %H:%i:%s') create_time,
        DATE_FORMAT(endDate,'%Y-%m-%d %H:%i:%s') complete_time,
        DATE_FORMAT(OnlineDate, '%Y-%m-%d %H:%i:%s') online_time,
        f.name app_category,
        versionName version FROM (
        SELECT * from dnwx_client.wbgui_formconfig where pjId in (
        SELECT  max(pjId) from dnwx_client.wbgui_formconfig where channelTag in ('zhubao','csj') and endDate !='' and state !='暂停中' GROUP BY typeName,channelTag
        )
        ) a
        inner join dnwx_client.wbgui_gametype b on a.typeName =b.gameId
        left join yyhz_0308.app_info d on b.appid = d.id
        LEFT JOIN yyhz_0308.app_category f on d.app_category =f.id
        WHERE 1=1 and channelTag !=''
        <if test="appid != null and appid != ''">
            and b.appid in (${appid})
        </if>
        <if test="app_category != null and app_category != ''">
            and d.app_category = #{app_category}
        </if>
        GROUP BY appid,channelTag
    </select>

    <select id="getNotArrivedOrderList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.PayNotArrivedOrderVo">
        select b.orderid,b.uid,a.consume_status,a.update_time,ROUND(b.money/100,2) money,b.orderstatus,
        b.imei as openid,b.appid,b.payname,b.createtime,b.param1,b.param2,b.chaid,c.back_time
        from consume_order_info a inner join wb_pay_info b on a.orderid =b.orderid
        left join callback_order_info c on a.orderid = c.orderid
        where pay_status = 'SUCCESS'
        <if test="appid != null and appid != ''">
            and b.appid in (${appid})
        </if>
        <if test="uid != null and uid != ''">
            and b.uid = #{uid}
        </if>
        <if test="consume_status != null and consume_status != ''">
            and a.consume_status = #{consume_status}
        </if>
        <if test="orderid != null and orderid != ''">
            and a.orderid = #{orderid}
        </if>
        <if test="openid != null and openid != ''">
            and b.imei = #{openid}
        </if>
        and b.createtime <![CDATA[ >= ]]> concat(#{start_date},' 00:00:00') and b.createtime <![CDATA[ <= ]]> concat(#{end_date},' 23:59:59')
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by DATE_FORMAT(b.createtime,'%Y-%m-%d %H:%i:%s') asc,b.money desc
            </otherwise>
        </choose>
    </select>

    <select id="getNotArrivedOrderSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.PayNotArrivedOrderVo">
        select ROUND(SUM(b.money/100),2) money
        from consume_order_info a inner join wb_pay_info b on a.orderid =b.orderid
        where pay_status = 'SUCCESS'
        <if test="appid != null and appid != ''">
            and b.appid in (${appid})
        </if>
        <if test="consume_status != null and consume_status != ''">
            and a.consume_status = #{consume_status}
        </if>
        <if test="orderid != null and orderid != ''">
            and a.orderid = #{orderid}
        </if>
        <if test="openid != null and openid != ''">
            and b.imei = #{openid}
        </if>
        <if test="uid != null and uid != ''">
            and b.uid = #{uid}
        </if>
        and b.createtime <![CDATA[ >= ]]> concat(#{start_date},' 00:00:00') and b.createtime <![CDATA[ <= ]]> concat(#{end_date},' 23:59:59')
    </select>

    <select id="getFeedbackList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.userinfo.FeedbackVo">
        select * from  user_feedback_info where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="loginid != null and loginid != ''">
            and loginid = #{loginid}
        </if>
        <if test="msgtype != null and msgtype != ''">
            and msgtype = #{msgtype}
        </if>
        <if test="state != null and state != ''">
            and state = #{state}
        </if>
        <if test="note != null and note != ''">
            and note like concat('%',#{note},'%')
        </if>
        <if test="device_id != null and device_id != ''">
            and (androidid = #{device_id} or lsn = #{device_id} or idfa = #{device_id})
        </if>
        and DATE(create_time) <![CDATA[ >= ]]> #{start_date} and DATE(create_time) <![CDATA[ <= ]]> #{end_date}
        order by create_time desc
    </select>

    <select id="getGameServerConfig" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.config.ServerVo">
        select * from game_server_config where 1=1
        <if test="appid != null and appid != ''">
            and appid = #{appid}
        </if>
        <if test="server_type != null and server_type != ''">
            and server_type = #{server_type}
        </if>
        <if test="channel != null and channel != ''">
            and channel = #{channel}
        </if>
    </select>

    <select id="getGameServerConfigList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.config.ServerVo">
        select * from game_server_config where 1=1
        <if test="appid != null and appid != ''">
            and appid = #{appid}
        </if>
    </select>

    <insert id="saveHandleUserInfoLog" parameterType="com.wbgame.pojo.game.userinfo.HandleUserInfoVo">
        insert into game_handle_userinfo_log (handle,appid,server_type,channel,loginId,userinfo,create_time,create_user) values
        (#{handle},#{appid},#{channel},#{server_type},#{loginId},#{userinfo},now(),#{create_user})
    </insert>

    <select id="getHandleUserInfoLog" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.userinfo.HandleUserInfoVo">
        select * from  game_handle_userinfo_log where  1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="server_type != null and server_type != ''">
            and server_type = #{server_type}
        </if>
        <if test="loginId != null and loginId != ''">
            and loginId = #{loginId}
        </if>
        and DATE(create_time) <![CDATA[ >= ]]> #{start_date} and DATE(create_time) <![CDATA[ <= ]]> #{end_date}
        order by create_time desc
    </select>

    <select id="getUserLevelConfigList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.config.UserLevelVo">
        select * from dnwx_cfg.dn_user_level where  1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and chlid in (${channel})
        </if>
        <if test="pid != null and pid != ''">
            and pid =#{pid}
        </if>
        <if test="status != null and status != ''">
            and status =#{status}
        </if>
        order by create_time desc
    </select>

    <insert id="saveUserLevelConfig" parameterType="com.wbgame.pojo.game.config.UserLevelVo">
        insert into dnwx_cfg.dn_user_level (appid,chlid,pid,levelA,levelB,levelC,adType,adTime,status,create_time,create_user)
        values (#{appid},#{chlid},#{pid},#{levelA},#{levelB},#{levelC},#{adType},#{adTime},#{status},now(),#{create_user})
    </insert>

    <update id="updateUserLevelConfig" parameterType="com.wbgame.pojo.game.config.UserLevelVo">
        update dnwx_cfg.dn_user_level set appid = #{appid},chlid = #{chlid},pid = #{pid},levelA = #{levelA},
        levelB = #{levelB},levelC = #{levelC},adType = #{adType},adTime = #{adTime},status = #{status},
        modify_time = now(),modify_user = #{modify_user}
        where id = #{id}
    </update>

    <delete id="delUserLevelConfig" parameterType="com.wbgame.pojo.game.config.UserLevelVo">
        delete from dnwx_cfg.dn_user_level where id in (${id})
    </delete>

    <update id="updateBatchUserLevelConfig" parameterType="com.wbgame.pojo.game.config.UserLevelVo">
        update dnwx_cfg.dn_user_level
        <set>
            <if test="levelA != null and levelA != ''">
                levelA = #{levelA},
            </if>
            <if test="levelB != null and levelB != ''">
                levelB = #{levelB},
            </if>
            <if test="levelC != null and levelC != ''">
                levelC = #{levelC},
            </if>
            <if test="adType != null and adType != ''">
                adType = #{adType},
            </if>
            <if test="adTime != null and adTime != ''">
                adTime = #{adTime},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="modify_user != null and modify_user != ''">
                modify_user = #{modify_user},
            </if>

            modify_time = now()
        </set>


        where id in (${id})
    </update>

    <select id="getFeishuExcelTaskList" resultType="com.wbgame.pojo.game.config.FeishuExcelTaskVo">
        select * from wb_fs_excel_task_config where state = 1
    </select>

    <select id="getFSExcelList" parameterType="com.wbgame.pojo.game.config.FeishuExcelTaskVo" resultType="com.wbgame.pojo.game.config.FeishuExcelTaskVo">
        select  * from wb_fs_excel_task_config where  1=1
        <if test="state != null and state != ''">
            and state = #{state}
        </if>
        <if test="send_type != null and send_type != ''">
            and send_type = #{send_type}
        </if>
        <if test="page_mark != null and page_mark != ''">
            and page_mark = #{page_mark}
        </if>
        <if test="create_user != null and create_user != ''">
            and create_user = #{create_user}
        </if>
        order by id desc
    </select>

    <insert id="saveFSExcel" parameterType="com.wbgame.pojo.game.config.FeishuExcelTaskVo" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into wb_fs_excel_task_config (title,send_type,page_mark,templete_id,time_type,
        header_param,query_param,member_type,member_id,dn_user_id,query_url,send_time,send_day,
        query_start_time,query_end_time,date_format,task_data_day,state,create_user,create_time)
        values (#{title},#{send_type},#{page_mark},#{templete_id},#{time_type},
        #{header_param},#{query_param},#{member_type},#{member_id},#{dn_user_id},#{query_url},#{send_time},#{send_day},
        #{query_start_time},#{query_end_time},#{date_format},#{task_data_day},#{state},#{create_user},now())
    </insert>


    <update id="updateFSExcel" parameterType="com.wbgame.pojo.game.config.FeishuExcelTaskVo">
        update wb_fs_excel_task_config set title =#{title},send_type =#{send_type},page_mark =#{page_mark},
        templete_id =#{templete_id},time_type =#{time_type},header_param =#{header_param},query_param =#{query_param},
        member_type =#{member_type},member_id =#{member_id},dn_user_id =#{dn_user_id},query_url =#{query_url},send_day =#{send_day},
        query_start_time =#{query_start_time},query_end_time =#{query_end_time},date_format =#{date_format},send_time =#{send_time},
        task_data_day =#{task_data_day},state =#{state},modify_time =now(),modify_user = #{modify_user} where id = #{id}
    </update>

    <delete id="delFSExcel" parameterType="java.lang.String">
        delete from wb_fs_excel_task_config where id in (${id})
    </delete>
    
    <update id="updateFeishuExcelTaskState" parameterType="com.wbgame.pojo.game.config.FeishuExcelTaskVo">
        update wb_fs_excel_task_config set state =#{state} where id = #{id}
    </update>

    <update id="updateFSExcelSendState" parameterType="com.wbgame.pojo.game.config.FeishuExcelTaskVo">
        update wb_fs_excel_task_config set send = 1 ,complete_time = now() where id = #{id}
    </update>
    
    <select id="getPayInfoList" parameterType="com.wbgame.pojo.game.pay.RefundBaseDataVo" resultType="com.wbgame.pojo.game.pay.PayInfoVo">
        select orderid,uid,money,pid,chaid,buy_id,appid from wb_pay_info where uid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.refund_order}
        </foreach>
    </select>

    <insert id="insertRefundData" parameterType="com.wbgame.pojo.game.pay.RefundBaseDataVo">
        insert ignore into refund_order_info (refund_order,pay_order,uid,refund_uid,total_fee,
        refund_fee,refund_status,success_time,refund_source,payid,pid,appid,imei,create_time,chaid,buy_id,success_date) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.refund_order},#{item.pay_order},#{item.uid},#{item.refund_uid},#{item.total_fee},
                #{item.refund_fee},#{item.refund_status},#{item.success_time},#{item.refund_source},#{item.payid},
                #{item.pid},#{item.appid},#{item.imei},now(),#{item.chaid},#{item.buy_id},#{item.success_date}
            )
        </foreach>
    </insert>
    
    <update id="batchUpdateFSExcelSendState" parameterType="com.wbgame.pojo.game.config.FeishuExcelTaskVo">
        update wb_fs_excel_task_config set send = 1 ,complete_time = now() where id in (
        <foreach collection="list" item="item" separator=",">
                #{item}
        </foreach>  )
    </update>
    
    <!--批量发送飞书模板  模块-->
    
    <resultMap id="batchFsTaskMap" type="com.wbgame.pojo.game.config.BatchFeishuExcelTaskVo">
		<id column="id" property="id" jdbcType="INTEGER"/>
        <result column="batch_send_name" property="batch_send_name" jdbcType="VARCHAR"/>
        <result column="state" property="state" jdbcType="INTEGER"/>
        <result column="templete_count" property="templete_count" jdbcType="INTEGER"/>
        <result column="member_type" property="member_type" jdbcType="VARCHAR"/>
        <result column="send_type" property="send_type" jdbcType="VARCHAR"/>
        <result column="member_id" property="member_id" jdbcType="VARCHAR"/>
        <result column="dn_user_id" property="dn_user_id" jdbcType="VARCHAR"/>
        <result column="task_data_day" property="task_data_day" jdbcType="VARCHAR"/>
        <result column="date_format" property="date_format" jdbcType="VARCHAR"/>
        <result column="page_mark" property="page_mark" jdbcType="VARCHAR"/>
        <result column="time_type" property="time_type" jdbcType="VARCHAR"/>
        <result column="send_time" property="send_time" jdbcType="VARCHAR"/>
        <result column="send_day" property="send_day" jdbcType="VARCHAR"/>
        <result column="create_user" property="create_user" jdbcType="VARCHAR"/>
        <result column="create_time" property="create_time" jdbcType="VARCHAR"/>
        <result column="modify_user" property="modify_user" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modify_time" jdbcType="VARCHAR"/>
        <result column="content_type" property="content_type" jdbcType="VARCHAR"/>
        <result column="covered" property="covered" jdbcType="INTEGER"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="date_group" property="date_group" jdbcType="VARCHAR"/>
        <result column="custom_date" property="custom_date" jdbcType="VARCHAR"/>
        <collection  property="list"  ofType="com.wbgame.pojo.game.config.FeishuExcelTaskVo">
			<result column="bid" property="bid" jdbcType="INTEGER"/>
	        <result column="title" property="title" jdbcType="VARCHAR"/>
	        <result column="query_param" property="query_param" jdbcType="VARCHAR"/>
	        <result column="header_param" property="header_param" jdbcType="VARCHAR"/>
	        <result column="query_url" property="query_url" jdbcType="VARCHAR"/>
	        <result column="query_start_time" property="query_start_time" jdbcType="VARCHAR"/>
	        <result column="query_end_time" property="query_end_time" jdbcType="VARCHAR"/>
	        <result column="templete_id" property="templete_id" jdbcType="VARCHAR"/>
		</collection>
	</resultMap>

    
   <insert id="saveBatchSFRecord" parameterType="com.wbgame.pojo.game.config.BatchFeishuExcelTaskVo" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into batch_fs_task_config (batch_send_name,templete_count,state,create_user,send_type,page_mark,time_type,
        member_type,member_id,dn_user_id,send_time,send_day,
        date_format,task_data_day,send_status,create_time,covered,url,date_group,custom_date)
        values (#{batch_send_name},#{templete_count},#{state},#{create_user},#{send_type},#{page_mark},#{time_type},
        #{member_type},#{member_id},#{dn_user_id},#{send_time},#{send_day},
        #{date_format},#{task_data_day},1,now(),#{covered},#{url},#{date_group},#{custom_date})
    </insert>
    
    <insert id="batchSaveFSExcelSendState" parameterType="com.wbgame.pojo.game.config.FeishuExcelTaskVo" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into batch_fs_excel_task_config (title,templete_id, header_param,query_param,query_url,query_start_time,query_end_time,bid)
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.title},#{item.templete_id},
		        #{item.header_param},#{item.query_param},#{item.query_url},
		        #{item.query_start_time},#{item.query_end_time},#{item.bid}
            )
        </foreach>
    </insert>
    <delete id="deleteBatch" parameterType="java.lang.String">
        delete from batch_fs_task_config where id in (${id})
    </delete>
    <select id="getBatchTemplete" parameterType="java.lang.String" resultMap="batchFsTaskMap">
        select  b.*,e.* from batch_fs_task_config b left join batch_fs_excel_task_config e on b.id=e.bid  where  b.create_user=#{create_user} and b.page_mark=#{page_mark} order by b.id desc
    </select>
    <select id="getBatchTempleteCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        select  count(id) from batch_fs_task_config b   where  b.create_user=#{create_user} 
    </select>
    
    <select id="getBatchTempleteTask"  resultMap="batchFsTaskMap">
        select  b.*,e.* from batch_fs_task_config b left join batch_fs_excel_task_config e on b.id=e.bid where b.send_type=2  and   b.state=1 
    </select>
    
    <select id="getBatchTepleteById" parameterType="java.lang.Integer" resultMap="batchFsTaskMap">
        select  b.*,e.* from batch_fs_task_config b left join batch_fs_excel_task_config e on b.id=e.bid  where  b.id=#{bid}
    </select>
    
    <update id="updateBatchTemplete" parameterType="com.wbgame.pojo.game.config.BatchFeishuExcelTaskVo">
        update batch_fs_task_config 
        <trim prefix="SET" suffixOverrides=",">
            modify_time = now(),
            <if test="batch_send_name != null">batch_send_name = #{batch_send_name},</if>
            <if test="templete_count != null">templete_count = #{templete_count},</if>
            <if test="member_type != null">member_type = #{member_type},</if>
            <if test="member_id != null">member_id = #{member_id},</if>
            <if test="task_data_day != null">task_data_day = #{task_data_day},</if>
            <if test="send_type != null">send_type = #{send_type},</if>
            <if test="page_mark != null">page_mark = #{page_mark},</if>
            <if test="time_type != null">time_type = #{time_type},</if>
            <if test="dn_user_id != null">dn_user_id = #{dn_user_id},</if>
            <if test="modify_user != null">modify_user = #{modify_user},</if>
            <if test="send_day != null">send_day = #{send_day},</if>
            <if test="send_time != null">send_time = #{send_time},</if>
            <if test="state != null">state = #{state},</if>
            <if test="date_format != null">date_format = #{date_format},</if>
            <if test="url != null">url = #{url},</if>
            <if test="covered != null">covered = #{covered},</if>
            <if test="content_type != null">state = #{content_type},</if>
            <if test="date_group != null">date_group = #{date_group},</if>
            <if test="custom_date != null">custom_date = #{custom_date}</if>

        </trim>
        where id = #{id}
    </update>
    <delete id="deleteBatchExcelByBid" parameterType="java.lang.Integer">
        delete from batch_fs_excel_task_config where bid=#{bid}
    </delete>
    
</mapper>