<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.TurnTableMapper">
    <insert id="insertTurnTableNum" parameterType="java.util.Map">
        INSERT into turntable
        (mmdate,appid,pid,turnNum)
        values
        <foreach collection="list" item="li" separator=",">
            (
            #{li.mmdate},
            #{li.appid},
            #{li.pid},
            #{li.turnNum}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        turnNum = VALUES (turnNum)
    </insert>

    <insert id="insertTurnTableTypeNum" parameterType="java.util.Map">
        INSERT into turntable
        (mmdate,appid,pid,type1,type2,type3,type4,type5)
        values
        <foreach collection="list" item="li" separator=",">
            (
            #{li.mmdate},
            #{li.appid},
            #{li.pid},
            #{li.type1},
            #{li.type2},
            #{li.type3},
            #{li.type4},
            #{li.type5}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        type1 = VALUES (type1),
        type2 = VALUES (type2),
        type3 = VALUES (type3),
        type4 = VALUES (type4),
        type5 = VALUES (type5)
    </insert>

    <insert id="insertTurnTablePrizeNum" parameterType="java.util.Map">
        INSERT into turntable
        (mmdate,appid,pid,prize1,prize2,prize3,prize4,prize5)
        values
        <foreach collection="list" item="li" separator=",">
            (
            #{li.mmdate},
            #{li.appid},
            #{li.pid},
            #{li.prize1},
            #{li.prize2},
            #{li.prize3},
            #{li.prize4},
            #{li.prize5}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        prize1 = VALUES (prize1),
        prize2 = VALUES (prize2),
        prize3 = VALUES (prize3),
        prize4 = VALUES (prize4),
        prize5 = VALUES (prize5)
    </insert>

    <select id="getTurnTableList" parameterType="java.util.Map" resultType="com.wbgame.pojo.nnjy.TurnTableVO">
        SELECT
        tt.mmdate AS mmdate,
        tt.pid AS pid,
        tt.appid AS appid,
        SUM( tt.turnNum ) AS turnNum,
        SUM( type1 ) AS type1,
        SUM( type2 ) AS type2,
        SUM( type3 ) AS type3,
        SUM( type4 ) AS type4,
        SUM( type5 ) AS type5,
        SUM( prize1 ) AS prize1,
        SUM( prize2 ) AS prize2,
        SUM( prize3 ) AS prize3,
        SUM( prize4 ) AS prize4,
        SUM( prize5 ) AS prize5,
        wf.gameName AS appName,
        wc.channel AS channel,
        wf.versionName AS ver,
        wf.platform AS os
        FROM
        turntable tt,
        dnwx_client.wbgui_formconfig wf,
        dnwx_client.wbgui_gametype wg,
        dnwx_client.wbgui_channel wc
        WHERE
        1 = 1
        <if test="pid != null and pid != ''">
            and tt.pid = #{pid}
        </if>
        <if test="appid != null and appid != ''">
            and tt.appid = #{appid}
        </if>
        AND wf.pjId = tt.pid
        AND wf.typeName = wg.gameId
        AND wf.channel = wc.id
        AND tt.mmdate >= #{beginDt} and tt.mmdate &lt;= #{endDt}
        GROUP BY
        tt.mmdate
        order by sum(tt.turnNum) desc
    </select>

</mapper>