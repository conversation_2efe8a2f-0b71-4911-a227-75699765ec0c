<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.TokenManageMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.operate.TokenManageVo">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="tokenid" property="tokenid" jdbcType="VARCHAR" />
        <result column="appid" property="appid" jdbcType="VARCHAR" />
        <result column="token_name" property="token_name" jdbcType="VARCHAR" />
        <result column="create_time" property="create_time" jdbcType="TIMESTAMP" />
        <result column="update_time" property="update_time" jdbcType="TIMESTAMP" />
        <result column="create_user" property="create_user" jdbcType="VARCHAR" />
        <result column="update_user" property="update_user" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, tokenid, appid, token_name, date_format(create_time, '%Y-%m-%d %H:%i:%s') create_time,
        date_format(update_time, '%Y-%m-%d %H:%i:%s') update_time, create_user, update_user,status
    </sql>

    <select id="tokenList" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.operate.TokenManageVo">
        select
        <include refid="Base_Column_List"/>
        from dn_token_manage
        <where>
            <if test="token_name != null and token_name != ''">
                and token_name like "%"#{token_name}"%"
            </if>
            <if test="tokenid != null and tokenid != ''">
                and tokenid = #{tokenid}
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="status != null">
                and status = #{status}
           </if>
        </where>
    </select>
    
    <select id="selectTokenNameByTokenid" resultType="java.lang.String"
            parameterType="java.lang.String">
        select
        token_name
        from dn_token_manage where appid = #{appid} and tokenid=#{tokenid} limit 1;
    </select>

    <delete id="deleteTokenById" parameterType="java.lang.Integer">
        delete from dn_token_manage
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <insert id="add" parameterType="com.wbgame.pojo.operate.TokenManageVo"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into dn_token_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tokenid != null and tokenid != ''">
                tokenid,
            </if>
            <if test="appid != null and appid != ''">
                appid,
            </if>
            <if test="token_name != null and token_name != ''">
                token_name,
            </if>
            <if test="create_user != null and create_user != ''">
                create_user,
            </if>
          	<if test="status != null">
                status,
            </if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tokenid != null and tokenid != ''">
                #{tokenid,jdbcType=VARCHAR},
            </if>
            <if test="appid != null and appid != ''">
                #{appid,jdbcType=INTEGER},
            </if>
            <if test="token_name != null and token_name != ''">
                #{token_name,jdbcType=VARCHAR},
            </if>
            <if test="create_user != null and create_user != ''">
                #{create_user,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        </trim>
    </insert>

    <update id="updateToken" parameterType="com.wbgame.pojo.operate.TokenManageVo">
        update dn_token_manage
        <set>
            <if test="tokenid != null and tokenid != ''">
                tokenid = #{tokenid,jdbcType=VARCHAR},
            </if>

            <if test="appid != null and appid != ''">
                appid = #{appid,jdbcType=INTEGER},
            </if>
			<if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="token_name != null and token_name != ''">
                token_name = #{token_name,jdbcType=VARCHAR},
            </if>
            <if test="update_user != null and update_user != ''">
                update_user = #{update_user,jdbcType=VARCHAR},
            </if>
            update_time = CURRENT_TIMESTAMP
        </set>
        where id = #{id}
    </update>

</mapper>