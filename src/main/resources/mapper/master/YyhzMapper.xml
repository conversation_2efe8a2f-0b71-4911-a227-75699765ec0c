<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.YyhzMapper">
	
	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>
	
	<update id="updateDnAdshowAdduserPv" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			update dn_adshow_adduser set 
	    		${li.pvnum} = #{li.pv}
	    	where tdate=#{li.tdate} and appid=#{li.appid} and channel=#{li.channel} and adpos_type=#{li.adpos_type} 
    	</foreach>
	</update>
	
	<insert id="insertDnGroupDataMonitor" parameterType="java.util.Map" >
		INSERT INTO dnwx_bi.dn_extend_group_monitor(tdate,appid,cha_id,prjid,income,plaque_pv,plaque_income,video_pv,video_income,msg_pv,msg_income)
		SELECT aa.tdate,aa.appid,aa.cha_id,aa.prjid,SUM(aa.income) income,
			SUM(CASE WHEN aa.adpos_type = 'plaque' THEN aa.show_num ELSE 0 END) plaque_pv,
			SUM(CASE WHEN aa.adpos_type = 'plaque' THEN aa.income ELSE 0 END) plaque_income,
			SUM(CASE WHEN aa.adpos_type = 'video' THEN aa.show_num ELSE 0 END) video_pv,
			SUM(CASE WHEN aa.adpos_type = 'video' THEN aa.income ELSE 0 END) video_income,
			SUM(CASE WHEN aa.adpos_type = 'msg' THEN aa.show_num ELSE 0 END) msg_pv,
			SUM(CASE WHEN aa.adpos_type = 'msg' THEN aa.income ELSE 0 END) msg_income
			
		from (
			select 
				tdate,prjid,cha_id,adsid,adpos_type,
				appid,
				ecpm,
				TRUNCATE(ecpm*sum(aa.show_num)/1000,2) income,
				SUM(aa.req_num) req_num,
				SUM(aa.fill_num) fill_num,
				SUM(aa.show_num) show_num,
				SUM(aa.click_num) click_num
			from dnwx_bi.dn_extend_group_data_two aa 
			where tdate BETWEEN #{tdate} AND #{tdate} 
			group by tdate,prjid,cha_id,adsid
		) aa
		WHERE aa.adpos_type in ('plaque','video','msg') AND aa.income is not null
		GROUP BY aa.prjid,aa.cha_id 
	</insert>
	
	<select id="selectAdshowAdduser" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			concat(tdate,'') tdate,appid,channel,adpos_type,
			IFNULL(addnum,0) addnum,
			IFNULL(TRUNCATE(sum(pv1)/addnum,2),0) pv1,
			IFNULL(TRUNCATE(sum(pv2)/addnum,2),0) pv2,
			IFNULL(TRUNCATE(sum(pv3)/addnum,2),0) pv3,
			IFNULL(TRUNCATE(sum(pv4)/addnum,2),0) pv4,
			IFNULL(TRUNCATE(sum(pv5)/addnum,2),0) pv5,
			IFNULL(TRUNCATE(sum(pv6)/addnum,2),0) pv6,
			IFNULL(TRUNCATE(sum(pv7)/addnum,2),0) pv7,
			IFNULL(TRUNCATE(sum(pv8)/addnum,2),0) pv8,
			IFNULL(TRUNCATE(sum(pv9)/addnum,2),0) pv9,
			IFNULL(TRUNCATE(sum(pv10)/addnum,2),0) pv10,
			IFNULL(TRUNCATE(sum(pv11)/addnum,2),0) pv11,
			IFNULL(TRUNCATE(sum(pv12)/addnum,2),0) pv12,
			IFNULL(TRUNCATE(sum(pv13)/addnum,2),0) pv13,
			IFNULL(TRUNCATE(sum(pv14)/addnum,2),0) pv14
		from dnwx_cfg.dn_adshow_adduser aa
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="channel != null and channel != ''">
			and channel = #{channel} 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid}
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		
		 group by tdate,appid,channel,adpos_type

		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY ${order_str}
			</when>

			<otherwise>
				order by tdate desc,addnum desc
			</otherwise>
		</choose>
	</select>

	<select id="selectAdshowActuser" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			concat(tdate,'') tdate,appid,channel,
			<if test="adpos_type_group != null and adpos_type_group != ''">
				adpos_type,
			</if>
			IFNULL(actnum,0) actnum,
			IFNULL(addnum,0) addnum,
			IFNULL(aduser,0) aduser,
			IFNULL(global_aduser,0) global_aduser,

			IFNULL(truncate(addnum/actnum*100,2),0) as add_rate,
			IFNULL(truncate(aduser/actnum*100,2),0) as seep_rate,
			IFNULL(truncate(aduser/addnum*100,2),0) as add_seep_rate,
			IFNULL(truncate(global_aduser/actnum*100,2),0) as global_seep_rate,
			IFNULL(truncate(global_aduser/addnum*100,2),0) as add_global_seep_rate,
			
			IFNULL(truncate(SUM(pv0)/${user}*100,2),0) as pv0,
			IFNULL(truncate(SUM(pv1)/${user}*100,2),0) as pv1,
			IFNULL(truncate(SUM(pv2)/${user}*100,2),0) as pv2,
			IFNULL(truncate(SUM(pv3)/${user}*100,2),0) as pv3,
			IFNULL(truncate(SUM(pv4)/${user}*100,2),0) as pv4,
			IFNULL(truncate(SUM(pv5)/${user}*100,2),0) as pv5,
			IFNULL(truncate(SUM(pv6)/${user}*100,2),0) as pv6,
			IFNULL(truncate(SUM(pv7)/${user}*100,2),0) as pv7,
			IFNULL(truncate(SUM(pv8)/${user}*100,2),0) as pv8,
			IFNULL(truncate(SUM(pv9)/${user}*100,2),0) as pv9,
			IFNULL(truncate(SUM(pv10)/${user}*100,2),0) as pv10,
			IFNULL(truncate(SUM(pv11)/${user}*100,2),0) as pv11,
			IFNULL(truncate(SUM(pv12)/${user}*100,2),0) as pv12,
			IFNULL(truncate(SUM(pv13)/${user}*100,2),0) as pv13,
			IFNULL(truncate(SUM(pv14)/${user}*100,2),0) as pv14
			
		from dnwx_cfg.dn_adshow_actuser aa
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid}
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="user_type != null and user_type != ''">
			and active_type = #{user_type} 
		</if>
		
		 group by tdate,appid,channel
		<!-- 增加广告位类型-->
		<if test="adpos_type_group != null and adpos_type_group != ''">
			,adpos_type
		</if>

		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY ${order_str}
			</when>

			<otherwise>
				order by tdate desc,actnum desc
			</otherwise>
		</choose>

	</select>
	
	
	<!-- 活跃用户价值分析  -->
  	<select id="selectDnRevenueActuser" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_revenue_actuser_sql"/>
	</select>
	<select id="selectDnRevenueActuserSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			TRUNCATE(SUM(xx.sum_revenue),2) sum_revenue
			
		from (<include refid="dn_revenue_actuser_sql"/>) xx
	</select>
	<sql id="dn_revenue_actuser_sql">
		select 
			tdate,appid,cha_id,adpos_type,

			IFNULL(actnum,0) actnum,
			IFNULL(addnum,0) addnum,
			IFNULL(sum_revenue,0) sum_revenue,
			IFNULL(TRUNCATE(addnum / actnum*100, 1),0) addrate,

			IFNULL(TRUNCATE(pv1 / sum_revenue*100, 1),0) pv1,
			IFNULL(TRUNCATE(pv2 / sum_revenue*100, 1),0) pv2,
			IFNULL(TRUNCATE(pv3 / sum_revenue*100, 1),0) pv3,
			IFNULL(TRUNCATE(pv4 / sum_revenue*100, 1),0) pv4,
			IFNULL(TRUNCATE(pv5 / sum_revenue*100, 1),0) pv5,
			IFNULL(TRUNCATE(pv6 / sum_revenue*100, 1),0) pv6,
			IFNULL(TRUNCATE(pv7 / sum_revenue*100, 1),0) pv7,
			IFNULL(TRUNCATE(pv8 / sum_revenue*100, 1),0) pv8,
			IFNULL(TRUNCATE(pv9 / sum_revenue*100, 1),0) pv9,
			IFNULL(TRUNCATE(pv10 / sum_revenue*100, 1),0) pv10,
			IFNULL(TRUNCATE(pv11 / sum_revenue*100, 1),0) pv11,
			IFNULL(TRUNCATE(pv12 / sum_revenue*100, 1),0) pv12,
			IFNULL(TRUNCATE(pv13 / sum_revenue*100, 1),0) pv13,
			IFNULL(TRUNCATE(pv14 / sum_revenue*100, 1),0) pv14
		from 
			(select 
				tdate,appid,cha_id,adpos_type,
				actnum,addnum,
				TRUNCATE(SUM(pv1+pv2+pv3+pv4+pv5+pv6+pv7+pv8+pv9+pv10+pv11+pv12+pv13+pv14),2) sum_revenue,
				SUM(pv1) pv1,SUM(pv2) pv2,SUM(pv3) pv3,SUM(pv4) pv4,SUM(pv5) pv5,SUM(pv6) pv6,SUM(pv7) pv7,
				SUM(pv8) pv8,SUM(pv9) pv9,SUM(pv10) pv10,SUM(pv11) pv11,SUM(pv12) pv12,SUM(pv13) pv13,SUM(pv14) pv14
				
			from dnwx_cfg.dn_extend_revenue_actuser
			where tdate BETWEEN #{sdate} AND #{edate} 
			<include refid="dn_extend_where_sql"/> 
			
			<if test="user_type != null and user_type != ''">
				and active_type = #{user_type} 
			</if>
			group by tdate,appid,cha_id,adpos_type) aa
		
		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY ${order_str}
			</when>

			<otherwise>
				order by tdate desc,sum_revenue desc
			</otherwise>
		</choose>
	</sql>
	
	<!-- 活跃用户ecpm分析  -->
  	<select id="selectDnEcpmActuser" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_ecpm_actuser_sql"/>
	</select>
	<select id="selectDnEcpmActuserSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			TRUNCATE(SUM(xx.sum_revenue),2) sum_revenue,
			TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.sum_pv)*1000, 2) ecpm
			
		from (<include refid="dn_ecpm_actuser_sql"/>) xx
	</select>
	<sql id="dn_ecpm_actuser_sql">
		select 
			tdate,appid,cha_id,adpos_type,
			IFNULL(actnum,0) actnum,
			IFNULL(addnum,0) addnum,
			IFNULL(sum_revenue,0) sum_revenue,
			IFNULL(sum_pv,0) sum_pv,
			IFNULL(TRUNCATE(addnum / actnum*100, 1),0) addrate,

			IFNULL(TRUNCATE(sum_revenue / sum_pv*1000,2),0) ecpm,
			IFNULL(TRUNCATE(ar1 / pv1*1000,2),0) pv1,
			IFNULL(TRUNCATE(ar2 / pv2*1000,2),0) pv2,
			IFNULL(TRUNCATE(ar3 / pv3*1000,2),0) pv3,
			IFNULL(TRUNCATE(ar4 / pv4*1000,2),0) pv4,
			IFNULL(TRUNCATE(ar5 / pv5*1000,2),0) pv5,
			IFNULL(TRUNCATE(ar6 / pv6*1000,2),0) pv6,
			IFNULL(TRUNCATE(ar7 / pv7*1000,2),0) pv7,
			IFNULL(TRUNCATE(ar8 / pv8*1000,2),0) pv8,
			IFNULL(TRUNCATE(ar9 / pv9*1000,2),0) pv9,
			IFNULL(TRUNCATE(ar10 / pv10*1000,2),0) pv10,
			IFNULL(TRUNCATE(ar11 / pv11*1000,2),0) pv11,
			IFNULL(TRUNCATE(ar12 / pv12*1000,2),0) pv12,
			IFNULL(TRUNCATE(ar13 / pv13*1000,2),0) pv13,
			IFNULL(TRUNCATE(ar14 / pv14*1000,2),0) pv14
		from 
			(select 
				tdate,appid,cha_id,adpos_type,
				actnum,addnum,
				TRUNCATE(SUM(pv1+pv2+pv3+pv4+pv5+pv6+pv7+pv8+pv9+pv10+pv11+pv12+pv13+pv14),2) sum_pv,
				TRUNCATE(SUM(ar1+ar2+ar3+ar4+ar5+ar6+ar7+ar8+ar9+ar10+ar11+ar12+ar13+ar14),2) sum_revenue,
				SUM(pv1) pv1,SUM(pv2) pv2,SUM(pv3) pv3,SUM(pv4) pv4,SUM(pv5) pv5,SUM(pv6) pv6,SUM(pv7) pv7,
				SUM(pv8) pv8,SUM(pv9) pv9,SUM(pv10) pv10,SUM(pv11) pv11,SUM(pv12) pv12,SUM(pv13) pv13,SUM(pv14) pv14,
				SUM(ar1) ar1,SUM(ar2) ar2,SUM(ar3) ar3,SUM(ar4) ar4,SUM(ar5) ar5,SUM(ar6) ar6,SUM(ar7) ar7,
				SUM(ar8) ar8,SUM(ar9) ar9,SUM(ar10) ar10,SUM(ar11) ar11,SUM(ar12) ar12,SUM(ar13) ar13,SUM(ar14) ar14
				
			from dnwx_cfg.dn_extend_ecpm_actuser
			where tdate BETWEEN #{sdate} AND #{edate} 
			<include refid="dn_extend_where_sql"/> 
			
			<if test="user_type != null and user_type != ''">
				and active_type = #{user_type} 
			</if>
			group by tdate,appid,cha_id,adpos_type) aa

		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY ${order_str}
			</when>

			<otherwise>
				order by tdate desc,sum_revenue desc
			</otherwise>
		</choose>
	</sql>
	
	<!-- 新用户ltv递进  -->
  	<select id="selectSumLtvAdduser" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			xx.*,
			(ltv1+ltv2+ltv3) sum_ltv3,
			(ltv1+ltv2+ltv3+ltv4+ltv5+ltv6+ltv7) sum_ltv7
			
		from (<include refid="dn_sumltv_adduser_sql"/>) xx
	</select>
	<select id="selectSumLtvAdduserSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			TRUNCATE(SUM(xx.ltv1), 2) ltv1,
			TRUNCATE(SUM(xx.ltv2), 2) ltv2,
			TRUNCATE(SUM(xx.ltv3), 2) ltv3,
			TRUNCATE(SUM(xx.ltv4), 2) ltv4,
			TRUNCATE(SUM(xx.ltv5), 2) ltv5,
			TRUNCATE(SUM(xx.ltv6), 2) ltv6,
			TRUNCATE(SUM(xx.ltv7), 2) ltv7,
			SUM(xx.ltv1)+SUM(xx.ltv2)+SUM(xx.ltv3) sum_ltv3,
			SUM(xx.ltv1)+SUM(xx.ltv2)+SUM(xx.ltv3)+SUM(xx.ltv4)+SUM(xx.ltv5)+SUM(xx.ltv6)+SUM(xx.ltv7) sum_ltv7 
			
		from (<include refid="dn_sumltv_adduser_sql"/>) xx
	</select>
	<sql id="dn_sumltv_adduser_sql">
		select 
			tdate,appid,cha_id
			<if test="adpos_type_group != null and adpos_type_group != ''">,adpos_type</if>
			,IFNULL(addnum,0) addnum,
			IFNULL(SUM(ar1),0) ar1,
			IFNULL(SUM(ar2),0) ar2,
			IFNULL(SUM(ar3),0) ar3,
			IFNULL(SUM(ar4),0) ar4,
			IFNULL(SUM(ar5),0) ar5,
			IFNULL(SUM(ar6),0) ar6,
			IFNULL(SUM(ar7),0) ar7,
			IFNULL(TRUNCATE(SUM(ar1)/addnum, 2),0) ltv1,
			IFNULL(TRUNCATE(SUM(ar2)/addnum, 2),0) ltv2,
			IFNULL(TRUNCATE(SUM(ar3)/addnum, 2),0) ltv3,
			IFNULL(TRUNCATE(SUM(ar4)/addnum, 2),0) ltv4,
			IFNULL(TRUNCATE(SUM(ar5)/addnum, 2),0) ltv5,
			IFNULL(TRUNCATE(SUM(ar6)/addnum, 2),0) ltv6,
			IFNULL(TRUNCATE(SUM(ar7)/addnum, 2),0) ltv7
			
		from dnwx_cfg.dn_extend_sumltv_adduser
		where tdate BETWEEN #{sdate} AND #{edate} 
		<include refid="dn_extend_where_sql"/> 
		group by tdate,appid,cha_id
		<if test="adpos_type_group != null and adpos_type_group != ''">,adpos_type</if>
		
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>

			<otherwise>
				order by tdate desc,addnum desc
			</otherwise>
		</choose>
	</sql>
	
	<!-- 新用户ecpm分析  -->
  	<select id="selectDnEcpmAdduser" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_ecpm_adduser_sql"/>
	</select>
	<select id="selectDnEcpmAdduserSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			*
			
		from (<include refid="dn_ecpm_adduser_sql"/>) xx
	</select>
	<sql id="dn_ecpm_adduser_sql">
		select 
			tdate,appid,cha_id,adpos_type,
			IFNULL(addnum,0) addnum,
			IFNULL(TRUNCATE(SUM(ar1) / SUM(pv1)*1000, 2),0) ecpm1,
			IFNULL(TRUNCATE(SUM(ar2) / SUM(pv2)*1000, 2),0) ecpm2,
			IFNULL(TRUNCATE(SUM(ar3) / SUM(pv3)*1000, 2),0) ecpm3,
			IFNULL(TRUNCATE(SUM(ar4) / SUM(pv4)*1000, 2),0) ecpm4,
			IFNULL(TRUNCATE(SUM(ar5) / SUM(pv5)*1000, 2),0) ecpm5,
			IFNULL(TRUNCATE(SUM(ar6) / SUM(pv6)*1000, 2),0) ecpm6,
			IFNULL(TRUNCATE(SUM(ar7) / SUM(pv7)*1000, 2),0) ecpm7
			
		from dnwx_cfg.dn_extend_ecpm_adduser
		where tdate BETWEEN #{sdate} AND #{edate} 
		<include refid="dn_extend_where_sql"/> 
		group by tdate,appid,cha_id,adpos_type

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>

			<otherwise>
				order by tdate desc,addnum desc
			</otherwise>
		</choose>
	</sql>
	
	<!-- 应用分渠道收支数据汇总  -->
  	<select id="selectAppChaRevenueTotal" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_appcha_revenue_sql"/>
	</select>
	<select id="selectAppChaRevenueTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.actnum) actnum,
			SUM(xx.addnum) addnum,
			TRUNCATE(SUM(xx.rebate_consume),2) rebate_consume,
			TRUNCATE(SUM(xx.ad_revenue),2) ad_revenue,
			TRUNCATE(SUM(xx.pay_revenue),2) pay_revenue,
			TRUNCATE(SUM(xx.rebate_consume)/SUM(xx.addnum), 2) cpa,
			TRUNCATE((SUM(xx.ad_revenue)+SUM(xx.pay_revenue))/SUM(xx.actnum), 2) dau_arpu,
			CONCAT(TRUNCATE((SUM(xx.ad_revenue)+SUM(xx.pay_revenue))/SUM(xx.rebate_consume)*100, 2),'%') day_roi
			
		from (<include refid="dn_appcha_revenue_sql"/>) xx
	</select>
	<sql id="dn_appcha_revenue_sql">
		select 
			tdate,appid,cha_type_name,cha_media,cha_sub_launch,cha_id,
			SUM(actnum) actnum,SUM(addnum) addnum,
			TRUNCATE(SUM(rebate_consume),2) rebate_consume,
			TRUNCATE(SUM(ad_revenue),2) ad_revenue,
			TRUNCATE(SUM(pay_revenue),2) pay_revenue,
			TRUNCATE(SUM(rebate_consume)/SUM(addnum), 2) cpa,
			TRUNCATE((SUM(ad_revenue)+SUM(pay_revenue))/SUM(actnum), 2) dau_arpu,
			CONCAT(TRUNCATE((SUM(ad_revenue)+SUM(pay_revenue))/SUM(rebate_consume)*100, 2),'%') day_roi
			
		from yyhz_0308.dn_app_cha_revenue_total 
		where tdate BETWEEN #{sdate} AND #{edate} 
		and appid in (select id from yyhz_0308.app_info where bus_category=3) 
		
		<include refid="dn_extend_where_sql"/> 
		group by tdate,appid,cha_id
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate asc,rebate_consume desc
			</otherwise>
		</choose>

	</sql>
	
	<!-- 游戏分渠道收支数据汇总  -->
  	<select id="selectGameChaRevenueTotal" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_gamecha_revenue_sql"/>
	</select>
	<select id="selectGameChaRevenueTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.actnum) actnum,
			SUM(xx.addnum) addnum,
			TRUNCATE(SUM(xx.rebate_consume),2) rebate_consume,
			TRUNCATE(SUM(xx.ad_revenue),2) ad_revenue,
			TRUNCATE(SUM(xx.bill_revenue),2) bill_revenue,
			TRUNCATE(SUM(xx.rebate_consume)/SUM(xx.addnum), 2) cpa,
			TRUNCATE(SUM(xx.ad_revenue)/SUM(xx.actnum), 2) dau_arpu,
			TRUNCATE(SUM(xx.bill_revenue)/SUM(xx.actnum), 2) bill_arpu,
			TRUNCATE((SUM(xx.ad_revenue)+SUM(xx.bill_revenue))/SUM(xx.actnum), 2) total_arpu,
			CONCAT(TRUNCATE((SUM(xx.ad_revenue)+SUM(xx.bill_revenue))/SUM(xx.rebate_consume)*100, 2),'%') day_roi
			
		from (<include refid="dn_gamecha_revenue_sql"/>) xx
	</select>
	<sql id="dn_gamecha_revenue_sql">
		select aa.*,
			dd.all_expenses,
			dd.all_sum_revenue,
			dd.all_sum_profit_rate
		from
			(select 
				<if test="tdate_group != null and tdate_group != ''">tdate,</if>
				<if test="appid_group != null and appid_group != ''">appid,</if>
				<if test="cha_id_group != null and cha_id_group != ''">cha_id,</if>
				<if test="cha_type_name_group != null and cha_type_name_group != ''">cha_type_name,</if>
				<if test="cha_media_group != null and cha_media_group != ''">cha_media,</if>
				<if test="cha_sub_launch_group != null and cha_sub_launch_group != ''">cha_sub_launch,</if>
				SUM(actnum) actnum,
				SUM(addnum) addnum,
				TRUNCATE(SUM(rebate_consume),2) rebate_consume,
				TRUNCATE(SUM(ad_revenue),2) ad_revenue,
				TRUNCATE(SUM(bill_revenue),2) bill_revenue,
				TRUNCATE(SUM(rebate_consume)/SUM(addnum), 2) cpa,
				TRUNCATE(SUM(ad_revenue)/SUM(actnum), 2) dau_arpu,
				TRUNCATE(SUM(bill_revenue)/SUM(actnum), 2) bill_arpu,
				TRUNCATE((SUM(ad_revenue)+SUM(bill_revenue))/SUM(actnum), 2) total_arpu,
				CONCAT(TRUNCATE((SUM(ad_revenue)+SUM(bill_revenue))/SUM(rebate_consume)*100, 2),'%') day_roi

			from yyhz_0308.dn_game_cha_revenue_total a inner join app_info b on a.appid=b.id
			where tdate BETWEEN #{sdate} AND #{edate} and b.app_category != 3
			<include refid="dn_extend_where_sql"/> 
			 group by
			<trim suffixOverrides=",">
				<if test="tdate_group != null and tdate_group != ''">tdate,</if>
				<if test="appid_group != null and appid_group != ''">appid,</if>
				<if test="cha_id_group != null and cha_id_group != ''">cha_id,</if>
				<if test="cha_type_name_group != null and cha_type_name_group != ''">cha_type_name,</if>
				<if test="cha_media_group != null and cha_media_group != ''">cha_media,</if>
				<if test="cha_sub_launch_group != null and cha_sub_launch_group != ''">cha_sub_launch,</if>
			</trim>) aa
		
		LEFT JOIN 
			(select 
				<if test="appid_group != null and appid_group != ''">appid,</if>
				<if test="cha_id_group != null and cha_id_group != ''">cname,</if>
				<if test="cha_type_name_group != null and cha_type_name_group != ''">type_name,</if>
				<if test="cha_media_group != null and cha_media_group != ''">cha_media,</if>
				<if test="cha_sub_launch_group != null and cha_sub_launch_group != ''">cha_sub_launch,</if>
				TRUNCATE(sum(invest_amount),0) all_expenses,
				TRUNCATE(SUM(adv_income) + SUM(billing_income),0) all_sum_revenue,
				CONCAT(IFNULL(TRUNCATE((SUM(adv_income) + SUM(billing_income) - SUM(invest_amount))/SUM(invest_amount)*100, 1),0),'%') all_sum_profit_rate
			
			from dn_app_income_total where 1=1 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="cha_id != null and cha_id != ''">
				and cname in (${cha_id}) 
			</if>
			<if test="cha_type_name != null and cha_type_name != ''">
				and type_name in (${cha_type_name}) 
			</if>
			<if test="cha_media != null and cha_media != ''">
				and cha_media in (${cha_media}) 
			</if>
			<if test="cha_sub_launch != null and cha_sub_launch != ''">
				and cha_sub_launch in (${cha_sub_launch}) 
			</if>
			<if test="apps != null and apps != ''">
				and appid in (${apps}) 
			</if>
			<trim suffixOverrides="," prefix="group by ">
				<if test="appid_group != null and appid_group != ''">appid,</if>
				<if test="cha_id_group != null and cha_id_group != ''">cname,</if>
				<if test="cha_type_name_group != null and cha_type_name_group != ''">type_name,</if>
				<if test="cha_media_group != null and cha_media_group != ''">cha_media,</if>
				<if test="cha_sub_launch_group != null and cha_sub_launch_group != ''">cha_sub_launch,</if>
			</trim>) dd 
			
		ON 1=1 
		<if test="appid_group != null and appid_group != ''"> and aa.appid=dd.appid</if>
		<if test="cha_id_group != null and cha_id_group != ''"> and aa.cha_id=dd.cname</if>
		<if test="cha_type_name_group != null and cha_type_name_group != ''">and aa.cha_type_name=dd.type_name</if>
		<if test="cha_media_group != null and cha_media_group != ''">and aa.cha_media=dd.cha_media</if>
		<if test="cha_sub_launch_group != null and cha_sub_launch_group != ''">and aa.cha_sub_launch=dd.cha_sub_launch</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by
				<if test="tdate_group != null and tdate_group != ''">aa.tdate asc ,</if>
				addnum desc
			</otherwise>
		</choose>
	</sql>
	
	
	<select id="selectAgentShowGap" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			*
		from yyhz_0308.dn_agent_show_gap 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<include refid="dn_extend_where_sql"/> 
	</select>
	
	<select id="selectSdkRelationReport" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			*
		from dnwx_bi.dn_extend_sdk_relation 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<include refid="dn_extend_where_sql"/> 
		
		<if test="sdk_name != null and sdk_name != ''">
			and sdk_name like concat('%',#{sdk_name},'%')
		</if>
		<if test="ver != null and ver != ''">
			and ver like concat('%',#{ver},'%')
		</if>
		<if test="local_ver != null and local_ver != ''">
			and local_ver like concat('%',#{local_ver},'%')
		</if>
		order by num desc
	</select>
	
	<select id="selectExtendClickWarn" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			*
		from yyhz_0308.dn_extend_click_warn 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<include refid="dn_extend_where_sql"/> 
		
		<if test="event != null and event != ''">
			and event like concat('%',#{event},'%')
		</if>
	</select>
	
	<sql id="dn_extend_where_sql">
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid in (${prjid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name}) 
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media}) 
		</if>
		<if test="cha_sub_launch != null and cha_sub_launch != ''">
			and cha_sub_launch in (${cha_sub_launch}) 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>
	</sql>

	<select id="getAdConfig" resultType="java.util.Map">
		SELECT appid,cha_id,sdk_code,sdk_adtype,open_type,adsid as ad_sid,sdk_appid as appid FROM dnwx_cfg.dn_extend_adsid_manage
	</select>
	<select id="getChannel" resultType="java.util.Map">
		SELECT dci.*, dct.type_name FROM dn_channel_info dci
		LEFT JOIN dn_channel_type dct ON dci.cha_type = dct.type_id
	</select>
	
	
	<select id="selectPushAssess" parameterType="java.util.Map" resultType="com.wbgame.pojo.product.ProductAssessVo">
		SELECT concat(appid,'') mapkey,appid,
			TRUNCATE(AVG(addnum),0) daily_addnum,
			TRUNCATE(AVG(ad_revenue),2) daily_revenue 
		FROM dn_app_revenue_total WHERE app_category=1 
		AND tdate BETWEEN #{sdate} AND #{edate} 
		
		<if test="two_app_category != null and two_app_category != ''">
			and appid in (select id from app_info where two_app_category in (${two_app_category})) 
		</if>
		<include refid="dn_extend_where_sql"/> 
		group by appid
		order by appid asc
	</select>

	<select id="selectAppid" resultType="java.lang.String">
            select id from app_info where umeng_key = #{umeng_key}
    </select>
    
    
    <!-- 自统计-版本分布新增活跃  -->
    <select id="selectCurrUserVerReportTotal" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="curr_user_verreport_sql"/>
	</select>
	<select id="selectCurrUserVerReportTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.currStart) currStart,
			SUM(xx.currDau) currDau,
			SUM(xx.currNew) currNew
			
		from (<include refid="curr_user_verreport_sql"/>) xx
	</select>

    <sql id="curr_user_verreport_sql">
		select xx.*,yy.sumNew from 
		
			(SELECT tdate,appid,ver,SUM(currDau) currDau,SUM(currStart) currStart,SUM(currNew) currNew,currUpdate 
			FROM yyhz_0308.admsg_ver_user_total
			 
			where tdate BETWEEN #{sdate} AND #{edate} 
				<if test="appid != null and appid != ''">
					and appid in (${appid}) 
				</if>
				<if test="ver != null and ver != ''">
					and ver in (${ver}) 
				</if>
			group by tdate,appid,ver ) xx
		
		LEFT JOIN 
			(SELECT appid,ver,SUM(currNew) sumNew FROM yyhz_0308.admsg_ver_user_total where 1=1
				<if test="appid != null and appid != ''">
					and appid in (${appid}) 
				</if>
				<if test="ver != null and ver != ''">
					and ver in (${ver}) 
				</if>
			group by appid,ver) yy
		ON xx.appid=yy.appid AND xx.ver=yy.ver
		
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			
		</choose>
	</sql>

	<select id="getChinaMonetizationSummaryReportByDay"
			resultType="com.wbgame.pojo.adv2.reportEntity.ChinaMonetizationReport">
		select  aa.*, bb.out_dau,bb.out_duration,bb.out_installs,bb.out_revenue
		from
			(SELECT a.date AS `day`, a.dnappid AS app, a.cha_id,b.duration, a.revenue, b.addnum AS installs, b.actnum AS dau,a.country AS country
			 FROM
				 (SELECT date, dnappid, cha_id, SUM(revenue) AS revenue,country,`out` FROM dn_cha_cash_total
				  WHERE date = #{day} AND dnappid IS NOT NULL
				  GROUP BY date, dnappid, cha_id,country) a
					 LEFT JOIN
				 (SELECT tdate, appid, install_channel,duration, SUM(add_num) AS addnum, SUM(act_num) AS actnum,'CN' as country FROM umeng_user_channel_total
				  WHERE tdate =#{day} AND appid IS NOT NULL
				  GROUP BY tdate, appid, install_channel,duration) b
				 ON a.date = b.tdate AND a.dnappid = b.appid AND a.cha_id = b.install_channel and a.country = b.country
			 WHERE revenue > 0.01 OR actnum > 10
			 UNION
			 SELECT b.tdate AS `day`, b.appid AS app, b.install_channel AS cha_id, b.duration, a.revenue, b.addnum AS installs, b.actnum AS dau,a.country AS country
			 FROM
				 (SELECT date, dnappid, cha_id, SUM(revenue) AS revenue,country, `out` FROM dn_cha_cash_total
				  WHERE date = #{day} AND dnappid IS NOT NULL
				  GROUP BY date, dnappid, cha_id, country) a
					 RIGHT JOIN
				 (SELECT tdate, appid, install_channel,duration, SUM(add_num) AS addnum, SUM(act_num) AS actnum,'CN' as country FROM umeng_user_channel_total
				  WHERE tdate = #{day} AND appid IS NOT NULL
				  GROUP BY tdate, appid, install_channel,duration) b
				 ON a.date = b.tdate AND a.dnappid = b.appid AND a.cha_id = b.install_channel and a.country = b.country
			 WHERE revenue > 0.01 OR actnum > 10
			) aa

				left join (
				SELECT a.date AS `day`, a.dnappid AS app, a.cha_id
						,case
							 when a.`out` = 1 then b.duration
							 else 0
						   end AS out_duration,
					   case
						   when a.`out` = 1 then b.addnum
						   else 0
						   end AS out_installs,
					   case
						   when a.`out` = 1 then b.actnum
						   else 0
						   end AS out_dau,
					   case
						   when a.`out` = 1 then a.revenue
						   else 0
						   end AS out_revenue

				FROM
					(SELECT date, dnappid, cha_id, SUM(revenue) AS revenue,country,`out` FROM dn_cha_cash_total
					 WHERE date = #{day} AND dnappid IS NOT NULL and `out` = 1
					 GROUP BY date, dnappid, cha_id,country, `out`) a
						LEFT JOIN
					(SELECT tdate, appid, install_channel,duration, SUM(add_num) AS addnum, SUM(act_num) AS actnum,'CN' as country FROM umeng_user_channel_total
					 WHERE tdate =#{day} AND appid IS NOT NULL
					 GROUP BY tdate, appid, install_channel,duration) b
					ON a.date = b.tdate AND a.dnappid = b.appid AND a.cha_id = b.install_channel and a.country = b.country
				WHERE revenue > 0.01 OR actnum > 10
			) bb
						  on aa.`day` = bb.`day` and aa.app = bb.app and aa.cha_id = bb.cha_id
	</select>


	<select id="selectWbguiChannelAdsid" parameterType="java.util.Map" resultType="java.util.Map">
		select SUBSTR(xx.pjId,1,5) appid,xx.gameName appname,xx.channelTag cha_id,xx.pjId prjid,xx.state,IF(yy.adsid IS NOT NULL,0,1) flag2 from
			(select * from dnwx_client.wbgui_formconfig
			 	where (DATE(date) >= #{tdate} or DATE(stateditdate) >= #{tdate})
			   	and state = '审核中'
				and channel in ('b6f5701f76c01881','373869478a18923f','3bf018db96370a4') ) xx

		LEFT JOIN (
				select * from dnwx_cfg.dn_extend_adconfig
					where (prjid is not null and prjid != '')
					and statu=1 and user_group='all' and adpos_type='video'
				group by appid,cha_id,prjid HAVING COUNT(1)=1
			) yy
		ON SUBSTR(xx.pjId,1,5)=yy.appid and xx.channelTag=yy.cha_id and xx.pjId=yy.prjid
		where IF(yy.adsid IS NOT NULL,0,1)=1
	</select>
	<select id="selectWbguiChannelAdsidTwo" parameterType="java.util.Map" resultType="java.util.Map">
		select SUBSTR(xx.pjId,1,5) appid,xx.gameName appname,xx.channelTag cha_id,xx.pjId prjid,xx.state,IF(yy.adsid IS NULL,0,1) flag2 from
			(select * from dnwx_client.wbgui_formconfig
			 	where (DATE(date) >= #{tdate} or DATE(stateditdate) >= #{tdate})
			   	and state != '审核中' and state != '被驳回'
				and channel in ('b6f5701f76c01881','373869478a18923f','3bf018db96370a4') ) xx

		LEFT JOIN (
				select * from dnwx_cfg.dn_extend_adconfig
					where (prjid is not null and prjid != '')
					and statu=1 and user_group='all'
				group by appid,cha_id,prjid HAVING COUNT(1)=1
			) yy
		ON SUBSTR(xx.pjId,1,5)=yy.appid and xx.channelTag=yy.cha_id and xx.pjId=yy.prjid
		where IF(yy.adsid IS NULL,0,1)=1 and yy.adpos_type='video'
	</select>


	<select id="selectPrjidGroupConfigConfig" resultType="java.util.Map" >
		select *
		from dnwx_cfg.dn_prjid_group
		where 1=1
		<if test="gname != null and gname != ''">
			and	gname like CONCAT('%',#{gname},'%')
		</if>
		<if test="note != null and note != ''">
			and	note like CONCAT('%',#{note},'%')
		</if>
		<if test="prjid != null and prjid != ''">
			and	prjid like CONCAT('%',#{prjid},'%')
		</if>
		<if test="chastr != null and chastr != ''">
			and	chastr like CONCAT('%',#{chastr},'%')
		</if>
		<if test="ctype != null and ctype != ''">
			and	ctype = #{ctype}
		</if>
		<if test="status != null and status != ''">
			and	status = #{status}
		</if>
		<if test="cuser != null and cuser != ''">
			and	cuser = #{cuser}
		</if>
		<if test="euser != null and euser != ''">
			and	euser = #{euser}
		</if>
		<if test="id != null and id != ''">
			and	id in (${id})
		</if>

		order by createtime desc
	</select>
	<select id="existPrjidGroupConfig" resultType="java.util.Map" >

		SELECT * from
		(
		<foreach collection="pidList" item="pid" separator=" union all ">
			select id,'${pid}' as prjid,gname
			from dnwx_cfg.dn_prjid_group
			where ctype=1 and FIND_IN_SET('${pid}', prjid) > 0
		</foreach>
		) xx
		<if test="id != null and id != ''">
			where id != #{id}
		</if>
		limit 1
	</select>

	<insert id="insertPrjidGroupConfig"  parameterType="java.util.Map">
		insert into dnwx_cfg.dn_prjid_group
			(gname,note,app_category,appid,tempid,append,prjid,chastr,ctype,status,cuser,createtime)
		values
			(#{gname},#{note},#{app_category},#{appid},#{tempid},#{append},#{prjid},#{chastr},#{ctype},#{status},#{cuser},now())
	</insert>

	<update id="updatePrjidGroupConfig" parameterType="java.util.Map">
		update dnwx_cfg.dn_prjid_group
		<set>
			<if test="gname != null and gname != ''">
				gname = #{gname},
			</if>
			<if test="note != null">
				note = #{note},
			</if>
			<if test="app_category != null">
				app_category = #{app_category},
			</if>
			<if test="appid != null">
				appid = #{appid},
			</if>
			<if test="tempid != null">
				tempid = #{tempid},
			</if>
			<if test="append != null">
				append = #{append},
			</if>
			<if test="prjid != null">
				prjid = #{prjid},
			</if>
			<if test="chastr != null">
				chastr = #{chastr},
			</if>
			<if test="ctype != null and ctype != ''">
				ctype = #{ctype},
			</if>
			<if test="status != null and status != ''">
				status = #{status},
			</if>
			<if test="euser != null and euser != ''">
				euser = #{euser},
			</if>
			endtime = now(),
		</set>
		where id = #{id}
	</update>
	<delete id="deletePrjidGroupConfig" parameterType="java.util.Map">
		delete from dnwx_cfg.dn_prjid_group where id in (${id})
	</delete>


	<insert id="insertGamePolicyConfig" parameterType="com.wbgame.pojo.product.DnGamePolicyConfigVo"
			useGeneratedKeys="true" keyColumn="id" keyProperty="id">
		INSERT INTO dn_game_policy_config (
			partner_sdk,
			company,
			purpose,
			demand,
			info,
			url,
			ctype,
			statu,
			cuser,
			createtime
		)
		VALUES
	  	(
			 #{partner_sdk},
			 #{company},
			 #{purpose},
			 #{demand},
			 #{info},
			 #{url},
			 #{ctype},
			 #{statu},
			 #{cuser},
			 now()
		)
	</insert>

	<update id="updateGamePolicyConfig" parameterType="com.wbgame.pojo.product.DnGamePolicyConfigVo">
		UPDATE dn_game_policy_config
		SET
			partner_sdk = #{partner_sdk},
			company = #{company},
			purpose = #{purpose},
			demand = #{demand},
			info = #{info},
			url = #{url},
			ctype = #{ctype},
			statu = #{statu},
			euser = #{euser},
			endtime = now()
		WHERE id = #{id}
	</update>

	<delete id="deleteGamePolicyConfig" parameterType="com.wbgame.pojo.product.DnGamePolicyConfigVo">
		DELETE FROM dn_game_policy_config
		WHERE id = #{id}
	</delete>


	<select id="selectAdposSyncConfig" resultType="java.util.Map" >
		select *
		from dnwx_cfg.dn_adpos_sync_config
		<where>
			<if test="name != null and name != ''">
				and	name like CONCAT('%',#{name},'%')
			</if>
			<if test="id != null and id != ''">
				and	id = #{id}
			</if>
			<if test="ctype != null and ctype != ''">
				and	ctype = #{ctype}
			</if>
			<if test="cuser != null and cuser != ''">
				and	cuser = #{cuser}
			</if>
		</where>

		order by createtime desc
	</select>


	<insert id="insertDnExtendAdposForSync"  parameterType="java.util.Map">

		INSERT INTO dnwx_cfg.dn_extend_adpos_manage
		    (appid,cha_id,prjid,user_group,adpos_type,adpos,strategy,adstyle,showrate,statu,note,delaySecond,startLv,endLv,lvInterval,xdelay,autoInterval,`out`,createtime,lasttime,cuser,buy_act,extraparam,custom_param,prjid_group_id)
		select
			${obj.re_appid},
			${obj.re_channel},
			${obj.re_prjid},
			${obj.re_user_group},
			adpos_type,adpos,strategy,adstyle,showrate,statu,note,delaySecond,startLv,endLv,lvInterval,xdelay,autoInterval,`out`,now(),now(),#{obj.cuser},buy_act,extraparam,custom_param,prjid_group_id
		from
			dnwx_cfg.dn_extend_adpos_manage
		where id in (${obj.makeid})

	</insert>


	<!-- 查询所有广告展示屏蔽配置 -->
	<select id="selectDnExtendAdinfoManageByCustomFilter" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT id, appid, params
		FROM dnwx_cfg.dn_extend_adinfo_manage_new WHERE IFNULL(params,'') != ''
			AND params like CONCAT('%',#{key1},'%')
			<!--
			<if test="ids != null and ids != ''">
				AND id in (${ids})
			</if>
			-->
	</select>

	<!-- 查询所有广告位管理配置 -->
	<select id="selectDnExtendAdposManageByCustomFilter" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT id, appid, custom_param
		FROM dnwx_cfg.dn_extend_adpos_manage WHERE IFNULL(custom_param,'') != ''
			AND custom_param like CONCAT('%',#{key1},'%')
	</select>

	<!-- 查询所有x3x4配置 -->
	<select id="selectDnExtendXConfigV2ByCustomFilter" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT concat(id,'') id, appid
			<if test="ctype != null and ctype == 'x3config'">
				,x3_content as xContent
			</if>
			<if test="ctype != null and ctype == 'x4config'">
				,x4_content as xContent
			</if>
		FROM dnwx_cfg.dn_extend_x_config_v2 WHERE 1=1
			<if test="ctype != null and ctype == 'x3config'">
				AND x3_content like CONCAT('%',#{key1},'%')
				<if test="key2 != null and key2 != ''">
					AND x3_content like CONCAT('%',#{key2},'%')
				</if>
			</if>
			<if test="ctype != null and ctype == 'x4config'">
				AND x4_content like CONCAT('%',#{key1},'%')
				<if test="key2 != null and key2 != ''">
					AND x4_content like CONCAT('%',#{key2},'%')
				</if>
			</if>
	</select>

	<!-- 更新广告展示屏蔽配置的参数 -->
	<update id="updateDnExtendAdinfoManageParams" parameterType="java.util.Map">
		UPDATE dnwx_cfg.dn_extend_adinfo_manage_new
		SET params = #{newParams},
			endtime=now(),euser=#{cuser}
		WHERE id = #{id}
	</update>

	<!-- 更新广告位管理配置的参数 -->
	<update id="updateDnExtendAdposManageParams" parameterType="java.util.Map">
		UPDATE dnwx_cfg.dn_extend_adpos_manage
		SET custom_param = #{newParams},
			lasttime=now(),euser=#{cuser}
		WHERE id = #{id}
	</update>

	<!-- 更新x3x4配置的参数 -->
	<update id="updateDnExtendXConfigV2Params" parameterType="java.util.Map">
		UPDATE dnwx_cfg.dn_extend_x_config_v2
		SET
			update_time=now(),
			update_user=#{cuser}
		<if test="contentType == 'x3'">
			,x3_content = #{newParams}
		</if>
		<if test="contentType == 'x4'">
			,x4_content = #{newParams}
		</if>
		WHERE id = #{id}
	</update>


	<!-- 根据ID查询广告展示屏蔽配置 -->
	<select id="selectDnExtendAdinfoManageById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT id, params
		FROM dnwx_cfg.dn_extend_adinfo_manage_new
		WHERE id = #{id}
	</select>

	<!-- 根据ID查询广告位管理配置 -->
	<select id="selectDnExtendAdposManageById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT id, custom_param
		FROM dnwx_cfg.dn_extend_adpos_manage
		WHERE id = #{id}
	</select>

	<!-- 根据ID查询x3x4配置 -->
	<select id="selectDnExtendXConfigV2ById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT id
			<if test="contentType == 'x3'">
				,x3_content as xContent
			</if>
			<if test="contentType == 'x4'">
				,x4_content as xContent
			</if>
		FROM dnwx_cfg.dn_extend_x_config_v2
		WHERE id = #{id}
	</select>

</mapper>