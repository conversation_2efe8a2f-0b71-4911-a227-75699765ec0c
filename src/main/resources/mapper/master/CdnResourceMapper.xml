<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.CdnResourceMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.CdnResource">
    <!--@mbg.generated-->
    <!--@Table cdn_resource-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="LONGVARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, url, `operator`, create_time, update_time, remark,domainName,company
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from cdn_resource
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from cdn_resource
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.wbgame.pojo.CdnResource">
    <!--@mbg.generated-->
    insert into cdn_resource (id, url, `operator`, 
      create_time, update_time, remark
      )
    values (#{id,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wbgame.pojo.CdnResource">
    <!--@mbg.generated-->
    insert into cdn_resource
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="domainName != null">
        domainName,
      </if>
      <if test="company != null">
        company,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=LONGVARCHAR},
      </if>
      <if test="domainName != null">
        #{domainName,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        #{company,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.CdnResource">
    <!--@mbg.generated-->
    update cdn_resource
    <set>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=LONGVARCHAR},
      </if>
      <if test="domainName != null">
        domainName = #{domainName,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        company = #{company,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.CdnResource">
    <!--@mbg.generated-->
    update cdn_resource
    set url = #{url,jdbcType=VARCHAR},
      `operator` = #{operator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

<!--auto generated by MybatisCodeHelper on 2020-12-25-->
  <select id="selectByAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from cdn_resource
        <where>1=1
          <if test="id != null and id != ''">
            and id = #{id,jdbcType=VARCHAR}
          </if>
        </where>
        <if test="order != null and order != ''">
          ${order}
        </if>

    </select>
</mapper>