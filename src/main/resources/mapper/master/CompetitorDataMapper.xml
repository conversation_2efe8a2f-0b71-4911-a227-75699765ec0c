<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.CompetitorDataMapper">

    <!-- 竞品数据分析表结果映射 -->
    <resultMap id="CompetitorDataResultMap" type="com.wbgame.pojo.custom.CompetitorDataVo">
        <result column="date" property="date" />
        <result column="time" property="time" />
        <result column="channel" property="channel" />
        <result column="company" property="company" />
        <result column="appName" property="appName" />
        <result column="device" property="device" />
        <result column="osVersion" property="osVersion" />
        <result column="sdkVersion" property="sdkVersion" />
        <result column="appVersion" property="appVersion" />
        <result column="duration" property="duration" />
        <result column="adType" property="adType" />
        <result column="sourceCounts" property="sourceCounts" />
        <result column="loadSuccCounts" property="loadSuccCounts" />
        <result column="loadFailCounts" property="loadFailCounts" />
        <result column="selfShow" property="selfShow" />
        <result column="apiShow" property="apiShow" />
        <result column="sdkShow" property="sdkShow" />
        <result column="secondShow" property="secondShow" />
        <result column="totalShow" property="totalShow" />
        <result column="selfClick" property="selfClick" />
        <result column="apiClick" property="apiClick" />
        <result column="sdkClick" property="sdkClick" />
        <result column="secondClick" property="secondClick" />
        <result column="totalClick" property="totalClick" />
        <result column="selfShowRate" property="selfShowRate" />
        <result column="apiShowRate" property="apiShowRate" />
        <result column="sdkShowRate" property="sdkShowRate" />
        <result column="totalShowRate" property="totalShowRate" />
        <result column="selfClickRate" property="selfClickRate" />
        <result column="apiClickRate" property="apiClickRate" />
        <result column="sdkClickRate" property="sdkClickRate" />
        <result column="totalClickRate" property="totalClickRate" />
    </resultMap>
    
    <!-- 查询竞品数据分析列表 -->
    <select id="selectCompetitorDataList" parameterType="java.util.Map" resultMap="CompetitorDataResultMap">
        SELECT 
            `date`, `time`, channel, company, md5, appName, device, osVersion, sdkVersion, appVersion,
            duration, adType, sourceCounts, loadSuccCounts, loadFailCounts, 
            selfShow, apiShow, sdkShow, secondShow, totalShow,
            selfClick, apiClick, sdkClick, secondClick, totalClick,
            selfShowRate, apiShowRate, sdkShowRate, totalShowRate,
            selfClickRate, apiClickRate, sdkClickRate, totalClickRate
        FROM dn_competitor_data
        where `date` BETWEEN #{sdate} AND #{edate}
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="company != null and company != ''">
                AND company like CONCAT('%',#{company},'%')
            </if>
            <if test="appName != null and appName != ''">
                AND appName like CONCAT('%',#{appName},'%')
            </if>
            <if test="adType != null and adType != ''">
                AND adType like CONCAT('%',#{adType},'%')
            </if>
            <if test="channel != null and channel != ''">
                AND channel in (${channel})
            </if>
            <if test="duration != null and duration != ''">
                AND `duration` in (${duration})
            </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by `date` DESC, `time` DESC
            </otherwise>
        </choose>

    </select>
    
    <!-- 批量插入竞品数据 -->
    <insert id="insertCompetitorDataBatch" parameterType="java.util.List">
        INSERT INTO dn_competitor_data (
            date, time, channel, company, appName, device, osVersion, sdkVersion, appVersion, md5,
            duration, adType, sourceCounts, loadSuccCounts, loadFailCounts, 
            selfShow, apiShow, sdkShow, secondShow, totalShow,
            selfClick, apiClick, sdkClick, secondClick, totalClick,
            selfShowRate, apiShowRate, sdkShowRate, totalShowRate,
            selfClickRate, apiClickRate, sdkClickRate, totalClickRate
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item._date}, #{item._time}, #{item._channel}, #{item._company}, #{item._appName},
                #{item._device}, #{item._osVersion}, #{item._sdkVersion}, #{item._appVersion},#{item._md5},
                #{item._duration}, #{item._adType}, #{item._sourceCounts}, #{item._loadSuccCounts}, 
                #{item._loadFailCounts}, #{item._selfShow}, #{item._apiShow}, #{item._sdkShow}, 
                #{item._secondShow}, #{item._totalShow}, #{item._selfClick}, #{item._apiClick}, 
                #{item._sdkClick}, #{item._secondClick}, #{item._totalClick}, #{item._selfShowRate}, 
                #{item._apiShowRate}, #{item._sdkShowRate}, #{item._totalShowRate}, #{item._selfClickRate}, 
                #{item._apiClickRate}, #{item._sdkClickRate}, #{item._totalClickRate}
            )
        </foreach>
    </insert>
    

</mapper>