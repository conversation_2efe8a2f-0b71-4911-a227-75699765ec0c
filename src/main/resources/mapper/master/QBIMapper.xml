<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.QBIMapper">
    <insert id="insertQbiType">
        insert into    qbi_permission_config
        (department_id, type)
        values
            (#{department_id},#{type})
    </insert>

    <update id="updateQbiType">
        update qbi_permission_config
        set
            type = #{type}
        where
            department_id = #{department_id}
    </update>

    <select id="selectQBIType" resultType="java.lang.String">
        select type from qbi_permission_config where department_id = #{department}
    </select>

	<select id="fieldList" resultType="java.util.HashMap">
        select field_name,type from report_field_definition where unique_identification = #{page}
    </select>
</mapper>