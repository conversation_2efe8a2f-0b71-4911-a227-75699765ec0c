<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.OASysMapper">

	<select id="selectSurveyInfo" parameterType="java.util.Map" resultType="java.util.Map">
		select *,CONCAT(`quarter`,survey) `owner`
		from oa_survey_info where 1=1
		<if test="quarter != null and quarter != ''"> and `quarter` = #{quarter} </if>
		<if test="survey != null and survey != ''"> and `survey` = #{survey} </if>
		<if test="secto != null and secto != ''"> and `secto` = #{secto} </if>
		<if test="state != null and state != ''"> and `state` = #{state} </if>
		<if test="rater != null and rater != ''"> 
			and CONCAT(`quarter`,survey) in (select `owner` from oa_owner_list where survey=#{rater}) 
		</if>
	</select>

	<insert id="insertSurveyInfo" parameterType="com.alibaba.fastjson.JSONObject">
		replace into oa_survey_info (
			survey,
			`quarter`,
			`name`,
			position,
			secto,
			state,
			target1_max,
			target2_max,
			target3_max

		)
		values (#{survey},
			#{quarter},
			#{name},
			#{position},
			#{secto},
			#{state},
			#{target1_max},
			#{target2_max},
			#{target3_max})
	</insert>

	<insert id="insertOwnerList" parameterType="com.alibaba.fastjson.JSONObject">
		replace into oa_owner_list (
			`owner`,
			survey,
			rater,
			target1,
			target2,
			target3,
			proportion,
			appraise,
			restrict_min,
			createtime
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.owner},
			#{li.survey},
			#{li.rater},
			#{li.target1},
			#{li.target2},
			#{li.target3},
			#{li.proportion},
			#{li.appraise},
			#{li.restrict_min},
			now())
		</foreach>
	</insert>
	
</mapper>