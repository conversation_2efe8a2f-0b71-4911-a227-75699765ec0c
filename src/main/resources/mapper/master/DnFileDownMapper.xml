<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.DnFileDownMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.DnFileDown">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="fileName" property="fileName" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="createTime" property="createTime" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , name, fileName, status,url,createTime
    </sql>
    <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.DnFileDown">
        select

        <include refid="Base_Column_List"/>
        from dn_file_down
        <where>
            <if test="name != null and name != ''">
                name = #{name}
            </if>
        </where>
        order by id desc
    </select>

    <insert id="insert" parameterType="com.wbgame.pojo.DnFileDown">

        <foreach collection="list" item="data" separator=";">

            insert into dn_file_down (name, fileName,
            status,url)
            values (#{data.name,jdbcType=VARCHAR}, #{data.fileName,jdbcType=VARCHAR},
            #{data.status,jdbcType=TINYINT}, #{data.url})
        </foreach>
    </insert>

</mapper>