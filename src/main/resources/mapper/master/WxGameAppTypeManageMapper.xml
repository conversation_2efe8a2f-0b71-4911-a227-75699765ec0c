<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.WxGameAppTypeManageMapper">

	<!--小游戏产品类别管理-->
    <select id="list" resultType="com.wbgame.pojo.operate.WxGameAppManageDto"
            parameterType="com.wbgame.pojo.operate.WxGameAppManageDto">
        select
        *
        from dn_wxapp_type_manage
        <where>
            <if test="category != null and category != ''">
                and category like "%"#{category}"%"
            </if>
            <if test="sub_category != null and sub_category != ''">
                and sub_category like "%"#{sub_category}"%"
            </if>
            <if test="status != null">
                and status = #{status}
           </if>
        </where>
    </select>
    
    <delete id="delete" parameterType="java.lang.Integer">
        delete from dn_wxapp_type_manage
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <insert id="add" parameterType="com.wbgame.pojo.operate.WxGameAppManageDto"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into dn_wxapp_type_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="category != null and category != ''">
                category,
            </if>
            <if test="sub_category != null and sub_category != ''">
                sub_category,
            </if>
            <if test="update_user != null and update_user != ''">
                update_user,
            </if>
             update_time,
          	<if test="status != null">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="category != null and category != ''">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="sub_category != null and sub_category != ''">
                #{sub_category,jdbcType=VARCHAR},
            </if>
            <if test="update_user != null and update_user != ''">
                #{update_user,jdbcType=VARCHAR},
            </if>
            CURRENT_TIMESTAMP,
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
     <insert id="batchAddAppType" parameterType="com.wbgame.pojo.operate.WxGameAppManageDto"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
          insert into dn_wxapp_type_manage(category,sub_category,update_user,update_time) values
        <foreach collection="list" item="li" separator=",">
            (#{li.category},#{li.sub_category},#{li.update_user},CURRENT_TIMESTAMP)
        </foreach>
    </insert>

    <update id="update" parameterType="com.wbgame.pojo.operate.WxGameAppManageDto">
        update dn_wxapp_type_manage
        <set>
                category = #{category,jdbcType=VARCHAR},
                sub_category = #{sub_category,jdbcType=INTEGER},
                status = #{status,jdbcType=TINYINT},
                update_user = #{update_user,jdbcType=VARCHAR},
            update_time = CURRENT_TIMESTAMP
        </set>
        where id = #{id}
    </update>
    
	<!--小游戏产品管理-->
	<select id="AppConfigList" resultType="com.wbgame.pojo.operate.WxGameAppManageDto"
            parameterType="com.wbgame.pojo.operate.WxGameAppManageDto">
        select
        *
        from dn_wxapp_manage
        <where>
             <if test="wxappid != null and wxappid != ''">
                and wxappid like "%"#{wxappid}"%"
            </if>
        	<if test="appname != null and appname != ''">
                and appname like "%"#{appname}"%"
            </if>
            <if test="category != null and category != ''">
                and category like "%"#{category}"%"
            </if>
            <if test="sub_category != null and sub_category != ''">
                and sub_category like "%"#{sub_category}"%"
            </if>
            <if test="status != null">
                and status = #{status}
           </if>
        </where>
    </select>
    
    <delete id="deleteAppConfig" parameterType="java.lang.Integer">
        delete from dn_wxapp_manage
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <insert id="addAppConfig" parameterType="com.wbgame.pojo.operate.WxGameAppManageDto"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into dn_wxapp_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">
        	<if test="wxappid != null and wxappid != ''">
                wxappid,
            </if>
        	<if test="appname != null and appname != ''">
                appname,
            </if>
            <if test="category != null and category != ''">
                category,
            </if>
            <if test="sub_category != null and sub_category != ''">
                sub_category,
            </if>
            <if test="update_user != null and update_user != ''">
                update_user,
            </if>
             update_time,
          	<if test="status != null">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="wxappid != null and wxappid != ''">
                #{wxappid,jdbcType=VARCHAR},
            </if>
            <if test="appname != null and appname != ''">
                #{appname,jdbcType=VARCHAR},
            </if>
            <if test="category != null and category != ''">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="sub_category != null and sub_category != ''">
                #{sub_category,jdbcType=VARCHAR},
            </if>
            <if test="update_user != null and update_user != ''">
                #{update_user,jdbcType=VARCHAR},
            </if>
            CURRENT_TIMESTAMP,
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
     <insert id="batchAddApp" parameterType="com.wbgame.pojo.operate.WxGameAppManageDto"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
          insert into dn_wxapp_manage(wxappid,appname,category,sub_category,update_user,update_time) values
        <foreach collection="list" item="li" separator=",">
            (#{li.wxappid},#{li.appname},#{li.category},#{li.sub_category},#{li.update_user},CURRENT_TIMESTAMP)
        </foreach>
    </insert>

    <update id="updateAppConfig" parameterType="com.wbgame.pojo.operate.WxGameAppManageDto">
        update dn_wxapp_manage
        <set>
                wxappid = #{wxappid,jdbcType=VARCHAR},
                appname = #{appname,jdbcType=VARCHAR},
                category = #{category,jdbcType=VARCHAR},
                sub_category = #{sub_category,jdbcType=INTEGER},
                status = #{status,jdbcType=TINYINT},
                update_user = #{update_user,jdbcType=VARCHAR},
            update_time = CURRENT_TIMESTAMP
        </set>
        where id = #{id}
    </update>
    
    
    <!--小游戏回传比例配置-->
	<select id="callBackConfigList" resultType="com.wbgame.pojo.jettison.vo.CallBackConfigVo"
            parameterType="com.wbgame.pojo.jettison.vo.CallBackConfigVo">
        select
        *
        from dnwx_adt.wx_game_callback_config
        <where>
             <if test="pay_id != null and pay_id != ''">
                and pay_id  in (${pay_id})
            </if>
             <if test="money != null and money != '0'">
                and money =#{money}
            </if>
            <if test="account_id != null and account_id != ''">
                and account_id in (${account_id})
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="status != null">
                and `status` = #{status}
           </if>
           <if test="type != null">
                and `type` = #{type}
           </if>
        </where>
        order by id desc
    </select>
    
    <delete id="deleteCallBackConfig" parameterType="java.lang.Integer">
        delete from dnwx_adt.wx_game_callback_config
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <insert id="addCallBackConfig" parameterType="com.wbgame.pojo.jettison.vo.CallBackConfigVo"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into dnwx_adt.wx_game_callback_config(appid,account_id,pay_id,money,type,down_money,dis_money,create_time,create_user,status,ratio) values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.appid},#{item.account_id},#{item.pay_id},#{item.money},#{item.type},#{item.down_money},#{item.dis_money},CURRENT_TIMESTAMP,#{item.create_user}
            ,#{item.status},#{item.ratio})
        </foreach>
    </insert>

    <update id="updateCallBackConfig" parameterType="com.wbgame.pojo.jettison.vo.CallBackConfigVo">
        update dnwx_adt.wx_game_callback_config
        <set>
                appid = #{appid,jdbcType=INTEGER},
                type = #{type,jdbcType=INTEGER},
                account_id = #{account_id,jdbcType=VARCHAR},
                campaign_id = #{campaign_id,jdbcType=VARCHAR},
                pay_id = #{pay_id,jdbcType=VARCHAR},
                money = #{money,jdbcType=INTEGER},
                down_money = #{down_money,jdbcType=INTEGER},
                `status` = #{status,jdbcType=TINYINT},
                update_user = #{update_user,jdbcType=VARCHAR},
            	update_time = CURRENT_TIMESTAMP,
            	ratio=#{ratio},
            	 dis_money = #{dis_money,jdbcType=VARCHAR}
        </set>
        where id = #{id}
    </update>
    
    
    <update id="updateCallBackConfigStatus" parameterType="com.wbgame.pojo.jettison.vo.CallBackConfigVo">
        update dnwx_adt.wx_game_callback_config
        <set>
                `status` = #{status,jdbcType=TINYINT},
                update_user = #{update_user,jdbcType=VARCHAR},
            	update_time = CURRENT_TIMESTAMP
        </set>
        where id = #{id}
    </update>
    
     <!--关键行为付费回传配置-->
	<select id="keybehaviorPayConfigList" resultType="com.wbgame.pojo.jettison.vo.KeyBehaiorPayReportConfigVo"
            parameterType="com.wbgame.pojo.jettison.vo.KeyBehaiorPayReportConfigVo">
        select
        *
        from dnwx_adt.key_behaior_pay_report_config
        <where>
            <if test="account_id != null and account_id != ''">
                and account_id in (${account_id})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="status != null">
                and `status` = #{status}
           </if>
            <if test="is_recharged != null">
                and `is_recharged` = #{is_recharged}
           </if>
        </where>
    </select>
    <delete id="deleteKeybehaviorPayConfigConfigById" parameterType="java.lang.Integer">
        delete from dnwx_adt.key_behaior_pay_report_config
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <insert id="addKeybehaviorPayConfig" parameterType="com.wbgame.pojo.jettison.vo.KeyBehaiorPayReportConfigVo"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into dnwx_adt.key_behaior_pay_report_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
        	<if test="appid != null and appid != ''">
                appid,
            </if>
        	<if test="account_id != null and account_id != ''">
                account_id,
            </if>
            <if test="channel != null and channel != ''">
                channel,
            </if>
           <if test="money != null and money != ''">
                money,
            </if>
            <if test="update_user != null and update_user != ''">
                update_user,
            </if>
            <if test="create_user != null and create_user != ''">
                create_user,
            </if>
             update_time,
             create_time,
             is_recharged,
            <if test="status != null">
                `status`,
            </if>
            <if test="ratio != null">
                `ratio`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appid != null and appid != ''">
                #{appid,jdbcType=INTEGER},
            </if>
            <if test="account_id != null and account_id != ''">
                #{account_id,jdbcType=VARCHAR},
            </if>
            <if test="channel != null and channel != ''">
                #{channel,jdbcType=VARCHAR},
            </if>
            <if test="money != null and money != ''">
                #{money,jdbcType=INTEGER},
            </if>
            <if test="update_user != null and update_user != ''">
                #{update_user,jdbcType=VARCHAR},
            </if>
            <if test="create_user != null and create_user != ''">
                #{create_user,jdbcType=VARCHAR},
            </if>
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP,
            #{is_recharged,jdbcType=TINYINT},
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="ratio != null">
               #{ratio,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateKeybehaviorPayConfig" parameterType="com.wbgame.pojo.jettison.vo.KeyBehaiorPayReportConfigVo">
        update dnwx_adt.key_behaior_pay_report_config
        <set>
                appid = #{appid,jdbcType=INTEGER},
                account_id = #{account_id,jdbcType=VARCHAR},
                channel = #{channel,jdbcType=VARCHAR},
                money = #{money,jdbcType=INTEGER},
                `status` = #{status,jdbcType=TINYINT},
                update_user = #{update_user,jdbcType=VARCHAR},
            	update_time = CURRENT_TIMESTAMP,
            	is_recharged=#{is_recharged,jdbcType=TINYINT},
            	ratio=#{ratio}
        </set>
        where id = #{id}
    </update>
    
    
    <update id="updateKeybehaviorPayConfigStatus" parameterType="com.wbgame.pojo.jettison.vo.KeyBehaiorPayReportConfigVo">
        update dnwx_adt.key_behaior_pay_report_config
        <set>
                `status` = #{status,jdbcType=TINYINT},
                update_user = #{update_user,jdbcType=VARCHAR},
            	update_time = CURRENT_TIMESTAMP
        </set>
        where id = #{id}
    </update>
    
    <!--小游戏数据汇总-->
    <select id="wechatGameDataSummaryList" resultType="com.wbgame.pojo.advert.WechatGameDataSummaryVo"
            parameterType="com.wbgame.pojo.param.ActivePaidUserRetentioParam">
        <include refid="wechatGameSummaryList"/>
    </select>

    <sql id="wechatGameSummaryList">
        select
        f.tdate tdate,
        concat(f.app_name,'-',f.id) app,
        round(sum(f.rebateSpend),2) rebateSpend,
        sum(f.installs) installs,
        round(sum(f.rebateSpend)/sum(f.installs),2) cpa,
        round((sum(f.rebateSpend)/sum(f.installs)/avg(f.rd1_rate)),2) rd1_spend,
        round((sum(f.rebateSpend)/sum(f.installs)/avg(f.rd7_rate)),2) rd7_spend,
        sum(f.new_purchase_external_app) new_purchase_external_app  ,
        sum(f.new_purchase_wechat_advertising) new_purchase_wechat_advertising ,
        round(sum(f.rebateSpend)/sum(f.add_num),2) t_cpa,
        sum(f.act_num) act_num,
        sum(f.add_num) add_num,
        CONCAT(round(sum(f.add_num)/sum(f.act_num)*100,2),'%') add_rate,
        (sum(f.add_num)-sum(f.new_purchase_external_app)-sum(f.new_purchase_wechat_advertising)) other_add_num,
        CONCAT(round((sum(f.add_num)-sum(f.new_purchase_external_app)-sum(f.new_purchase_wechat_advertising))/sum(f.add_num)*100,2),'%')  nature_rate,
        round(sum(f.visits_num_per),2) visits_num_per ,
        round(sum(f.average_stay),2) average_stay ,

        CONCAT(round(avg(f.rd1_rate)*100,2),'%') rd1_rate,
        CONCAT(round(avg(f.rd7_rate)*100,2),'%') rd7_rate,

        sum(f.pay_amount) pay_amount ,
        sum(f.apple_income) apple_income,
        round((sum(f.pay_amount)*0.6+sum(f.apple_income)),2) purchase_income,
        round((sum(f.pay_amount)*0.6+sum(f.apple_income))/sum(f.act_num),2) purchase_arpu,
        CONCAT(round((sum(f.pay_amount)+sum(f.apple_income))/(sum(f.pay_amount)+sum(f.apple_income)+sum(f.ad_revenue)+sum(f.advertising_money))*100,2),'%') purchase_rate,
        sum(f.share_new_advances) share_new_advances ,
        CONCAT(round(sum(f.share_new_advances)/sum(f.add_num)*100,2),'%') share_rate,
        round(sum(f.shares_num)/sum(f.share_users_number),2) avg_share,
        sum(f.ad_revenue) ad_revenue ,
        round(sum(f.advertising_money),2) advertising_money,
        CONCAT(round(sum(f.advertising_money)/sum(f.ad_revenue)*100,2),'%') pro_traffic_adv,
        round(sum(f.ad_revenue)/sum(f.act_num),2) ad_arpu,
        round(sum(f.video_revenue)/sum(f.act_num),2) arpu_video,
        round(sum(f.video_pv)/sum(f.act_num),2) avg_pv_video,
        round(sum(f.video_revenue)/sum(f.video_pv)*1000,2) ecpm_video ,
        round((sum(f.ad_revenue)+sum(f.pay_amount)+sum(f.apple_income)+sum(f.advertising_money)),2) total_revenue_divide_before,
        round((sum(f.ad_revenue)+sum(f.pay_amount)*0.6+sum(f.apple_income)+sum(f.advertising_money)),2) total_revenue_divide_after,
        round((sum(f.ad_revenue)+sum(f.pay_amount)+sum(f.apple_income)+sum(f.advertising_money))/f.act_num,2) total_arpu_divide_before,
        round((sum(f.ad_revenue)+sum(f.pay_amount)*0.6+sum(f.apple_income)+sum(f.advertising_money))/sum(f.act_num),2) total_arpu_divide_after,
        round((sum(f.ad_revenue)+sum(f.pay_amount)*0.6+sum(f.apple_income)+sum(f.advertising_money)-IFNULL(sum(f.rebateSpend),0)),2) profit_daily,

        CONCAT(round((sum(f.ad_revenue)+sum(f.pay_amount)*0.6+sum(f.apple_income)+sum(f.advertising_money)-sum(f.rebateSpend))
        /(sum(f.ad_revenue)+sum(f.pay_amount)*0.6+sum(f.apple_income)+sum(f.advertising_money))*100,2),'%') gross_margin_daily,

        CONCAT(round((sum(f.ad_revenue)+sum(f.pay_amount)*0.6+sum(f.apple_income)+sum(f.advertising_money))/sum(f.rebateSpend)*100,2),'%') roi,
        CONCAT(round(sum(f.acmul_revenue)/sum(f.acmul_spend)*100,2),'%') acmul_roi,
        CONCAT(round(avg(f.sharing_rate)*100,2),'%') sharing_rate ,

        sum(f.shares_num) shares_num,
        sum(f.share_users_number) share_users_number,
        sum(f.video_revenue) video_revenue,
        sum(f.video_pv) video_pv,
        sum(f.acmul_revenue) acmul_revenue,
        sum(f.acmul_spend) acmul_spend
        from
        (
        select
        <choose>
            <when test='null!=gp and gp.contains("week")'>
                DATE_FORMAT(a.tdate,'%x-%v') tdate,
            </when>
            <when test='null!=gp and gp.contains("month")'>
                DATE_FORMAT(a.tdate,'%Y-%m') tdate,
            </when>
            <when test='null!=gp and gp.contains("quarter")'>
                CONCAT(YEAR(a.tdate), '-Q', QUARTER(a.tdate)) tdate,
            </when>
            <when test='null!=gp and gp.contains("year")'>
                DATE_FORMAT(a.tdate,'%Y') tdate,
            </when>
            <otherwise>
                a.tdate tdate,
            </otherwise>
        </choose>
        a.appid appid,
        a.new_purchase_external_app new_purchase_external_app,
        a.new_purchase_wechat_advertising new_purchase_wechat_advertising,
        a.share_new_advances share_new_advances,
        a.act_num  act_num,
        a.add_num  add_num,
        a.cumulative_registere_users  cumulative_registere_users,
        a.act_visits_num  act_visits_num,
        a.visits_num_per visits_num_per,
        a.average_stay average_stay,
        a.rd1_rate rd1_rate,
        a.rd7_rate  rd7_rate,
        a.shares_num  shares_num,
        a.share_users_number  share_users_number,
        a.sharing_rate  sharing_rate,
        a.purchase_users  purchase_users,
        a.first_purchase_users  first_purchase_users,
        a.paid_permeability  paid_permeability,
        a.ad_revenue  ad_revenue,
        a.pay_amount  pay_amount,
        a.video_pv  video_pv,
        a.video_revenue video_revenue,
        a.acmul_revenue acmul_revenue,
        a.acmul_spend acmul_spend,
        b.installs installs,
        b.rebateSpend rebateSpend,
        c.advertising_money advertising_money,
        c.apple_income apple_income,
        d.id id,
        d.app_name app_name
        from wechat_game_operation_data a
        left join
        (
        SELECT day,app,sum(installs) installs,round(sum(rebateSpend),2) rebateSpend  FROM `dn_report_spend_china_summary`
        where  day BETWEEN #{start_date} AND #{end_date}
        <if test="appid != null and appid != ''">
            and app in (${appid})
        </if>
        group by day,app
        ) b on a.tdate=b.day and a.appid=b.app
        left join
        (
        select  tdate,appid,sum(ad_revenue) advertising_money,sum(wx_pay_income) apple_income
        from dn_micgame_revenue_total where tdate BETWEEN #{start_date} AND #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        group by tdate,appid
        ) c on a.tdate=c.tdate and a.appid=c.appid
        left join
        (
        select id,app_name from app_info where bus_category=2
        ) d on a.appid=d.id

        where a.tdate BETWEEN #{start_date} AND #{end_date}
        <if test="appid != null and appid != ''">
            and a.appid in (${appid})
        </if>
        ) f group by appid,tdate
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>

    </sql>

    <!--小游戏数据汇总汇总栏-->
    <select id="wechatGameDataSummaryTotal" resultType="com.wbgame.pojo.advert.WechatGameDataSummaryVo"
            parameterType="com.wbgame.pojo.param.ActivePaidUserRetentioParam">
			   select   
			        round(b.rebateSpend,2) rebateSpend,
			        b.installs installs,
			        round(b.rebateSpend/b.installs) cpa,
			        round((b.rebateSpend/b.installs/(a.rd1/a.add_num)),2) rd1_spend,
			        round((b.rebateSpend/b.installs/(a.rd7/a.add_num)),2) rd7_spend,
			        a.new_purchase_external_app new_purchase_external_app  ,
			        a.new_purchase_wechat_advertising new_purchase_wechat_advertising ,
			        round(b.rebateSpend/a.add_num,2) t_cpa,
			        a.act_num act_num,
			        a.add_num add_num,
			        CONCAT(round(a.add_num/a.act_num*100,2),'%') add_rate,
			        (a.add_num-a.new_purchase_external_app-a.new_purchase_wechat_advertising) other_add_num,
			        CONCAT(round((a.add_num-a.new_purchase_external_app-a.new_purchase_wechat_advertising)/a.add_num*100,2),'%')  nature_rate,
			        round(a.act_visits_num/a.act_num,2) visits_num_per ,
			        round(a.stay/a.act_num,2) average_stay ,
			        
			        round(a.rd1/a.add_num,2) rd1_rate ,
			        round(a.rd7/a.add_num,2) rd7_rate ,
			        a.pay_amount pay_amount ,
			        round(c.apple_income,2) apple_income,
			        round((a.pay_amount*0.6+c.apple_income),2) purchase_income,
			        round((a.pay_amount*0.6+c.apple_income)/a.act_num,2) purchase_arpu,
			        CONCAT(round((a.pay_amount+c.apple_income)/(a.pay_amount+c.apple_income+a.ad_revenue+c.advertising_money)*100,2),'%') purchase_rate,
			
			        a.share_new_advances share_new_advances ,
			        CONCAT(round(a.share_new_advances/a.add_num*100,2),'%') share_rate,
			        CONCAT(round(a.share_users_number/a.add_num*100,2),'%') sharing_rate ,
			        round(a.shares_num/a.share_users_number,2) avg_share,
			
			        a.ad_revenue ad_revenue ,
			        round(c.advertising_money,2) advertising_money,
			        CONCAT(round(c.advertising_money/a.ad_revenue*100,2),'%') pro_traffic_adv,
			        round(a.ad_revenue/a.act_num,2) ad_arpu,
			        round(a.video_revenue/a.act_num,2) arpu_video,
			        round(a.video_pv/a.act_num,2) avg_pv_video,
			        round(a.video_revenue/a.video_pv*1000,2) ecpm_video ,
			
			        round((a.ad_revenue+a.pay_amount+c.apple_income+c.advertising_money),2) total_revenue_divide_before,
			        round((a.ad_revenue+a.pay_amount*0.6+c.apple_income+c.advertising_money),2) total_revenue_divide_after,
			        round((a.ad_revenue+a.pay_amount+c.apple_income+c.advertising_money)/a.act_num,2) total_arpu_divide_before,
			        round((a.ad_revenue+a.pay_amount*0.6+c.apple_income+c.advertising_money)/a.act_num,2) total_arpu_divide_after,
			
			        round((a.ad_revenue+a.pay_amount*0.6+c.apple_income+c.advertising_money-b.rebateSpend),2) profit_daily,
			        CONCAT(round((a.ad_revenue+a.pay_amount*0.6+c.apple_income+c.advertising_money-b.rebateSpend)/(a.ad_revenue+a.pay_amount*0.6+c.apple_income+c.advertising_money)*100,2),'%') gross_margin_daily,
			        CONCAT(round((a.ad_revenue+a.pay_amount*0.6+c.apple_income+c.advertising_money)/b.rebateSpend*100,2),'%') roi,
			        CONCAT(round(d.acmul_revenue/d.acmul_spend*100,2),'%') acmul_roi
			       
			from
			(select
			        sum(new_purchase_external_app) new_purchase_external_app,
			        sum(new_purchase_wechat_advertising) new_purchase_wechat_advertising,
			        sum(share_new_advances) share_new_advances ,
			        sum(act_num)  act_num,
			        sum(add_num)  add_num,
			        sum(cumulative_registere_users)  cumulative_registere_users,
			        sum(act_visits_num)  act_visits_num,
			        sum(average_stay*act_num)  stay,
			        sum(rd1_rate*add_num) rd1  ,
			        sum(rd7_rate*add_num) rd7  ,
			        sum(shares_num)  shares_num,
			        sum(share_users_number) share_users_number ,
			        sum(purchase_users)  purchase_users,
			        sum(first_purchase_users) first_purchase_users ,
			        sum(ad_revenue) ad_revenue ,
			        sum(pay_amount) pay_amount ,
			        sum(video_pv) video_pv ,
			        sum(video_revenue) video_revenue
			from wechat_game_operation_data
			where 
			tdate BETWEEN #{start_date} AND #{end_date}
			<if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
			) a,
			(SELECT sum(installs) installs,round(sum(rebateSpend),2) rebateSpend  FROM `dn_report_spend_china_summary`
			where 
			day BETWEEN #{start_date} AND #{end_date}
			<if test="appid != null and appid != ''">
                and app in (${appid})
            </if>
			) b
			,
			(select  sum(ad_revenue) advertising_money,sum(wx_pay_income) apple_income
			from dn_micgame_revenue_total 
			where  
			tdate BETWEEN #{start_date} AND #{end_date}
			<if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
			) c,
			(
			select  sum(acmul_revenue) acmul_revenue , sum(acmul_spend) acmul_spend
			from wechat_game_operation_data
			where 
			tdate = #{end_date}
			<if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
			) d
    </select>
    <!--广告展示屏蔽策略模板管理-->
    <select id="adShowShieldTmpList" resultType="com.wbgame.pojo.adv2.AdShowShieldTmpManageVo"
            parameterType="com.wbgame.pojo.adv2.AdShowShieldTmpManageVo">
        select
        *
        from ad_show_shield_tmp
        <where>
            <if test="tmp_name != null and tmp_name != ''">
                and tmp_name=#{tmp_name}
            </if>
        </where>
    </select>
    <delete id="deleteAdShowShieldTmp" parameterType="java.lang.Integer">
        delete from ad_show_shield_tmp
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <insert id="addAdShowShieldTmp" parameterType="com.wbgame.pojo.adv2.AdShowShieldTmpManageVo"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into ad_show_shield_tmp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tmp_name != null and tmp_name != ''">
                tmp_name,
            </if>
            <if test="restrict_id != null">
                restrict_id,
            </if>
            <if test="restrict_name != null and restrict_name != ''">
                restrict_name,
            </if>
            <if test="value != null and value != ''">
                value,
            </if>
            <if test="tmp_name_info != null and tmp_name_info != ''">
                tmp_name_info,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tmp_name != null and tmp_name != ''">
                #{tmp_name,jdbcType=VARCHAR},
            </if>
            <if test="restrict_id != null">
                #{restrict_id,jdbcType=TINYINT},
            </if>
            <if test="restrict_name != null and restrict_name != ''">
                #{restrict_name,jdbcType=VARCHAR},
            </if>
            <if test="value != null and value != ''">
                #{value,jdbcType=VARCHAR},
            </if>
            <if test="tmp_name_info != null and tmp_name_info != ''">
                #{tmp_name_info},
            </if>
        </trim>
    </insert>

    <update id="updateAdShowShieldTmp" parameterType="com.wbgame.pojo.adv2.AdShowShieldTmpManageVo">
        update ad_show_shield_tmp
        <set>
                tmp_name = #{tmp_name,jdbcType=VARCHAR},
                restrict_id = #{restrict_id,jdbcType=INTEGER},
                restrict_name = #{restrict_name,jdbcType=VARCHAR},
           	 	value =#{value,jdbcType=VARCHAR},
                tmp_name_info =#{tmp_name_info}
        </set>
        where id = #{id}
    </update>
    <!--X模式配置V2版本-->
    <select id="xconfigList" resultType="com.wbgame.pojo.product.DnwxX3X4ConfigManageV2Dto"
            parameterType="com.wbgame.pojo.product.DnwxX3X4ConfigManageV2Dto">
            select id,
	        case when ISNULL(appid) OR ''=appid THEN match_appid_id ELSE  appid END AS appid,
			case when ISNULL(channel) OR ''=channel THEN match_cha_id ELSE  channel END AS channel,
	        prjid, x3_content,x4_content,x3_content_md5,x4_content_md5,create_user,create_time,update_user,update_time,status
	        from dnwx_cfg.dn_extend_x_config_v2
        <where>
            <if test="appid != null and appid != ''">
                and (appid in (${appid})  or match_appid_id in (${appid}))
            </if>
            <if test="appCategory != null and appCategory != ''">
                and appid in (select id from app_info where app_category in (${appCategory}))
            </if>
            <if test="prjid != null and prjid != ''">
                and prjid=#{prjid}
            </if>
            <if test="channel != null and channel != ''">
                and (channel  in (${channel}) or match_cha_id  in (${channel}))
            </if>
            <if test="x3_content_md5 != null and x3_content_md5 != ''">
                and x3_content_md5 like "%"#{x3_content_md5}"%"
            </if>
            <if test="x4_content_md5 != null and x4_content_md5 != ''">
                and x4_content_md5  like "%"#{x4_content_md5}"%"
            </if>
            <if test="status != null">
                and status=#{status}
            </if>
            <if test="filter_id != null and filter_id != ''">
                and id in (${filter_id})
            </if>
            <if test="appid_tag != null and appid_tag != ''">
                <choose>
                    <when test="appid_tag_rev != null and appid_tag_rev != ''">
                        AND (
                            CONCAT(appid,'#',channel) not in (${appid_tag})
                            and CONCAT(match_appid_id,'#',match_cha_id) not in (${appid_tag})
                        )
                    </when>
                    <otherwise>
                        AND (
	                        CONCAT(appid,'#',channel) in (${appid_tag})
                            or CONCAT(match_appid_id,'#',match_cha_id) in (${appid_tag})
                        )
                    </otherwise>
                </choose>
            </if>

        </where>
        order by create_time desc
    </select>
    <select id="checkConfig" resultType="com.wbgame.pojo.product.DnwxX3X4ConfigManageV2Dto"
            parameterType="com.wbgame.pojo.product.DnwxX3X4ConfigManageV2Dto">
        select
        *
        from dnwx_cfg.dn_extend_x_config_v2
        <where>
            <if test="appid != null and appid != ''">
                and appid=#{appid}
            </if>
            <if test="prjid != null and prjid != ''">
                and prjid=#{prjid}
            </if>
            <if test="channel != null and channel != ''">
                and channel=#{channel}
            </if>
        </where>
        limit 1
    </select>
        <select id="checkConfigByChannel" resultType="com.wbgame.pojo.product.DnwxX3X4ConfigManageV2Dto"
            parameterType="java.lang.String">
        select
        *
        from dnwx_cfg.dn_extend_x_config_v2 where channel=#{channel} and appid is null limit 1
    </select>
    
    <select id="selectXconfigByIds" resultType="com.wbgame.pojo.product.DnwxX3X4ConfigManageV2Dto"
            parameterType="java.lang.Integer">
        select
        *
        from dnwx_cfg.dn_extend_x_config_v2
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>
    
    <delete id="deleteXconfig" parameterType="java.lang.Integer">
        delete from dnwx_cfg.dn_extend_x_config_v2
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <insert id="addXconfig" parameterType="com.wbgame.pojo.product.DnwxX3X4ConfigManageV2Dto"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into dnwx_cfg.dn_extend_x_config_v2
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appid != null and appid != ''">
                appid,
            </if>
            <if test="prjid != null and prjid != ''">
                prjid,
            </if>
            <if test="channel != null and channel != ''">
                channel,
            </if>
            <if test="x3_content != null and x3_content != ''">
                x3_content,
            </if>
            <if test="x4_content != null and x4_content != ''">
                x4_content,
            </if>
            <if test="x3_content_md5 != null and x3_content_md5 != ''">
                x3_content_md5,
            </if>
            <if test="x4_content_md5 != null and x4_content_md5 != ''">
                x4_content_md5,
            </if>
            <if test="match_cha_id  != null and match_cha_id != ''">
                match_cha_id,
            </if>
            <if test="match_appid_id != null and match_cha_id != ''">
                match_appid_id,
            </if>
            <if test="create_user != null and create_user != ''">
                create_user,
            </if>
            <if test="create_time != null and create_time != ''">
                create_time,
            </if>
            <if test="update_user != null and update_user != ''">
                update_user,
            </if>
            <if test="update_time != null and update_time != ''">
                update_time,
            </if>
            <if test="status != null">
                status,
            </if>
           	<if test="x_type != null">
                x_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
        	<if test="appid != null and appid != ''">
               #{appid,jdbcType=VARCHAR},
            </if>
            <if test="prjid != null and prjid != ''">
                #{prjid,jdbcType=VARCHAR},
            </if>
            <if test="channel != null and channel != ''">
             	#{channel,jdbcType=VARCHAR},
            </if>
            <if test="x3_content != null and x3_content != ''">
            	#{x3_content,jdbcType=VARCHAR},
            </if>
            <if test="x4_content != null and x4_content != ''">
            	#{x4_content,jdbcType=VARCHAR},
            </if>
            <if test="x3_content_md5 != null and x3_content_md5 != ''">
            	#{x3_content_md5,jdbcType=VARCHAR},
            </if>
            <if test="x4_content_md5 != null and x4_content_md5 != ''">
            	#{x4_content_md5,jdbcType=VARCHAR},
            </if>
            <if test="match_cha_id != null and match_cha_id != ''">
            	#{match_cha_id,jdbcType=VARCHAR},
            </if>
            <if test="match_appid_id != null and match_appid_id != ''">
            	#{match_appid_id,jdbcType=VARCHAR},
            </if>
            <if test="create_user != null and create_user != ''">
            	#{create_user,jdbcType=VARCHAR},
            </if>
            <if test="create_time != null and create_time != ''">
            	#{create_time,jdbcType=VARCHAR},
            </if>
            <if test="update_user != null and update_user != ''">
            	#{update_user,jdbcType=VARCHAR},
            </if>
            <if test="update_time != null and update_time != ''">
            	#{update_time,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
            	#{status,jdbcType=TINYINT},
            </if>
            <if test="x_type != null">
            	#{x_type,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <update id="updateXconfig" parameterType="com.wbgame.pojo.product.DnwxX3X4ConfigManageV2Dto">
        update dnwx_cfg.dn_extend_x_config_v2
        
        <set>
        	 <trim  suffixOverrides=",">
	            <if test="appid != null and appid != ''">
	                appid = #{appid,jdbcType=VARCHAR},
	            </if>
	            <if test="prjid != null and prjid != ''">
	                 prjid = #{prjid,jdbcType=VARCHAR},
	            </if>
	            <if test="channel != null and channel != ''">
	                channel =#{channel,jdbcType=VARCHAR},
	            </if>
                x3_content = #{x3_content,jdbcType=VARCHAR},
                x4_content = #{x4_content,jdbcType=VARCHAR},
                x3_content_md5 = #{x3_content_md5,jdbcType=VARCHAR},
                x4_content_md5 = #{x4_content_md5,jdbcType=VARCHAR},
	            <if test="update_user != null and update_user != ''">
	                update_user = #{update_user,jdbcType=VARCHAR},
	            </if>
	            <if test="update_time != null and update_time != ''">
	                update_time = #{update_time,jdbcType=VARCHAR},
	            </if>
	            <if test="status != null">
	               status = #{status,jdbcType=INTEGER},
	            </if>
	        </trim>
        </set>
        where id = #{id}
    </update>
</mapper>