<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.ApkMapper">

	<sql id="foreachUserList"> <!-- 循环获取新增表中pid的新增用户lsn -->
		<foreach collection="array" item="it" separator=" union all ">
			select projectid,lsn from push_user_info_hash${it} 
			where createtime BETWEEN '${today} 00:00:00' AND '${today} 23:59:59'
		</foreach>
	</sql>
	<sql id="foreachKeepUser"> <!-- 循环获取新增表中pid的多日留存用户lsn -->
		<foreach collection="array" item="it" separator=" union all ">
			select projectid,lsn from push_user_info_hash${it} 
			where createtime BETWEEN '${startday} 00:00:00' AND '${startday} 23:59:59'
			and lasttime BETWEEN '${today} 00:00:00' AND '${today} 23:59:59' 
		</foreach>
	</sql>
	
	<insert id="insertKeepUserOne" parameterType="java.util.Map">
    	insert into product_keep_num_total
		
		SELECT '${today}' as mmdate, SUBSTR(projectid,1,5), projectid,
			COUNT(DISTINCT aa.lsn) as usernum, 
			<foreach collection="array" item="it" separator=",">0</foreach>
		FROM (<include refid="foreachUserList"/>) aa 
		GROUP BY projectid
    </insert>
	
	<select id="selectKeepUserTwo" parameterType="java.util.Map" resultType="com.wbgame.pojo.push.ApkProductKeepVo">
		
		SELECT '${startday}' as mmdate, projectid, 
			'keep${daynum}' as param1, COUNT(DISTINCT aa.lsn) as param2 
		FROM (<include refid="foreachKeepUser"/>) aa
		GROUP BY projectid
	</select>
	
	<update id="updateKeepUserThree" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			update product_keep_num_total set 
	    		${li.param1} = ROUND(#{li.param2}/usernum,2)
	    	where mmdate = #{li.mmdate} and projectid = #{li.projectid} 
    	</foreach>
	</update>
	
	<!-- 赋值项目分组的新增、活跃数据 -->
	<insert id="insertNewUserOne" parameterType="java.util.Map">
    	insert into buyu_newcount_total(by_date, by_priid, push_newcount)
		
		SELECT '${today}' as mmdate, projectid, COUNT(DISTINCT aa.lsn) as addnum 
		FROM (<include refid="foreachUserList"/>) aa 
		GROUP BY projectid
    </insert>
	<insert id="insertDauUserOne" parameterType="java.util.Map">
    	insert into alone_dau_total(by_date, by_priid, by_newcount)
		
		SELECT '${date}' as mmdate, projectid, COUNT(DISTINCT aa.lsn) as actnum 
		FROM np_post_log_${date} aa 
		GROUP BY projectid
    </insert>
	
	<!-- 查询产品分组的新增用户 -->
	<!-- <insert id="insertDnChaDauAppOne" parameterType="java.util.Map" >
		INSERT INTO dn_cha_dau_total(tdate, pid, product_add)
	
		SELECT '${today}' as tdate, projectid, COUNT(DISTINCT aa.lsn) as num 
		FROM (
			<foreach collection="array" item="it" separator=" union all ">
				select projectid,lsn from push_appuser_info_hash${it} 
				where createtime BETWEEN '${today} 00:00:00' AND '${today} 23:59:59' 
			</foreach>
		) aa 
		GROUP BY projectid
		
		ON DUPLICATE KEY UPDATE 
		product_add=VALUES(product_add)
	</insert>
	<insert id="insertDnChaDauAppTwo" parameterType="java.util.Map" >
		REPLACE INTO product_active_temp(tdate, pid, product_active)
	
		SELECT '${today}' as tdate, projectid, COUNT(DISTINCT aa.lsn) as active 
		FROM (
			<foreach collection="array" item="it" separator=" union all ">
				select projectid,lsn from push_appuser_info_hash${it} 
				where lasttime BETWEEN '${today} 00:00:00' AND '${today} 23:59:59' 
			</foreach>
		) aa 
		GROUP BY projectid
		
	</insert>
	<insert id="insertDnChaDauAppThree" parameterType="java.util.Map" >
		INSERT INTO dn_cha_dau_total(tdate, pid, product_active)
	
		SELECT tdate, pid, product_active from product_active_temp where tdate = #{today}
		
		ON DUPLICATE KEY UPDATE 
		product_active=VALUES(product_active)
	</insert> -->
	
	
    <!-- 项目ID+子渠道分组的新增 活跃 -->
    <insert id="insertProductChaAddUser" parameterType="java.util.Map">
    	insert into product_chaid_total(tdate, projectid, chaid, add_num)
		
		SELECT '${today}' as mmdate, projectid, chaid, COUNT(DISTINCT aa.lsn) as num 
		FROM (
			<foreach collection="array" item="it" separator=" union all ">
				select projectid,chaid,lsn from push_chauser_info_hash${it} 
				where createtime BETWEEN '${today} 00:00:00' AND '${today} 23:59:59'
			</foreach>
		) aa 
		GROUP BY projectid,chaid
		
		ON DUPLICATE KEY UPDATE 
		add_num=VALUES(add_num)
    </insert>
	<insert id="insertProductChaActUser" parameterType="java.util.Map">
    	insert into product_chaid_total(tdate, projectid, chaid, act_num)
		
		SELECT '${today}' as mmdate, projectid, chaid, COUNT(DISTINCT aa.lsn) as num 
		FROM (
			<foreach collection="array" item="it" separator=" union all ">
				select projectid,chaid,lsn from push_chauser_info_hash${it} 
				where lasttime BETWEEN '${today} 00:00:00' AND '${today} 23:59:59'
			</foreach>
		) aa 
		GROUP BY projectid,chaid
		
		ON DUPLICATE KEY UPDATE 
		act_num=VALUES(act_num)
    </insert>
    
    <!-- 项目ID+子渠道+二级子渠道分组的新增 活跃 -->
    <insert id="insertPushSubChaAddUser" parameterType="java.util.Map">
    	insert into dn_extend_subcha_total(tdate, appid, cha_id, sub_cha, addnum)
		
		SELECT '${today}' as mmdate, SUBSTR(projectid,1,5), chaid, sub_channel, COUNT(DISTINCT aa.lsn) as addnum 
		FROM (
			<foreach collection="array" item="it" separator=" union all ">
				select projectid,chaid,sub_channel,lsn from push_subchauser_info_hash${it} 
				where createtime BETWEEN '${today} 00:00:00' AND '${today} 23:59:59'
			</foreach>
		) aa 
		GROUP BY SUBSTR(projectid,1,5),chaid,sub_channel
		
		ON DUPLICATE KEY UPDATE 
		addnum=VALUES(addnum)
    </insert>
	<insert id="insertPushSubChaActUser" parameterType="java.util.Map">
    	insert into dn_extend_subcha_total(tdate, appid, cha_id, sub_cha, actnum)
		
		SELECT '${today}' as mmdate, SUBSTR(projectid,1,5), chaid, sub_channel, COUNT(DISTINCT aa.lsn) as actnum 
		FROM (
			<foreach collection="array" item="it" separator=" union all ">
				select projectid,chaid,sub_channel,lsn from push_subchauser_info_hash${it} 
				where lasttime BETWEEN '${today} 00:00:00' AND '${today} 23:59:59'
			</foreach>
		) aa 
		GROUP BY SUBSTR(projectid,1,5),chaid,sub_channel
		
		ON DUPLICATE KEY UPDATE 
		actnum=VALUES(actnum)
    </insert>
    
    
    <!-- 项目ID+子渠道+策略ID分组的新增 活跃 -->
    <insert id="insertPushGroupAddUser" parameterType="java.util.Map">
    	insert into dnwx_bi.product_group_total(tdate, projectid, chaid, sid, addnum)
		
		SELECT '${today}' as tdate, projectid, chaid, sid, COUNT(DISTINCT aa.lsn) as addnum 
		FROM (
			<foreach collection="array" item="it" separator=" union all ">
				select projectid,chaid,sid,lsn from dnwx_bi.push_groupuser_info_hash${it} 
				where createtime BETWEEN '${today} 00:00:00' AND '${today} 23:59:59'
			</foreach>
		) aa 
		GROUP BY projectid,chaid,sid
		
		ON DUPLICATE KEY UPDATE 
		addnum=VALUES(addnum)
    </insert>
	<insert id="insertPushGroupActUser" parameterType="java.util.Map">
		insert into dnwx_bi.product_group_total(tdate, projectid, chaid, sid, actnum)
		
		SELECT '${today}' as tdate, projectid, chaid, sid, COUNT(DISTINCT aa.lsn) as actnum 
		FROM (
			<foreach collection="array" item="it" separator=" union all ">
				select projectid,chaid,sid,lsn from dnwx_bi.push_groupuser_info_hash${it} 
				where lasttime BETWEEN '${today} 00:00:00' AND '${today} 23:59:59'
			</foreach>
		) aa 
		GROUP BY projectid,chaid,sid
		
		ON DUPLICATE KEY UPDATE 
		actnum=VALUES(actnum)
    </insert>
    
</mapper>