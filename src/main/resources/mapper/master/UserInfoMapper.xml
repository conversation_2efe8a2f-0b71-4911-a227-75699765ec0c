<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.UserInfoMapper">

	<select id="selectUserInfoVo" parameterType="java.lang.String"
		resultType="com.wbgame.pojo.UserInfoVo">
		select <include refid="userinfo"/> 
		from ssds_user_info
		where wx_id = #{wx_id} limit 1
	</select>
	<select id="selectUserInfoVoAllByAttr" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.UserInfoVo">
		select wx_id, pro_id, u_name, u_icon, top_score, top_high, play_times,
		phone, reward_num,
		create_date, last_date
		from ssds_user_info
	</select>

	<insert id="insertUserInfoVo" parameterType="com.wbgame.pojo.UserInfoVo">
		insert into ssds_user_info (<include refid="userinfo"/> ) 
		values (
			#{wx_id},
			#{pro_id},
			#{u_name},
			#{u_icon},
			#{top_score},
			#{top_high},
			#{play_times},
			#{reward_num},
			#{phone},
			#{create_date},
			#{last_date}) 
		ON DUPLICATE KEY UPDATE 
		u_name=VALUES(u_name),
		u_icon=VALUES(u_icon),
		top_score=VALUES(top_score),
		top_high=VALUES(top_high),
		play_times=VALUES(play_times),
		reward_num=VALUES(reward_num),
		phone=VALUES(phone),
		last_date=VALUES(last_date)
	</insert>
	<insert id="insertRewardInfoVo" parameterType="com.wbgame.pojo.RewardInfoVo">
		insert into ssds_reward_info (
			cdate,
			wx_id,
			pro_id,
			phone
		) 
		values (
			#{cdate},
			#{wx_id},
			#{pro_id},
			#{phone}
		)	
	</insert>
	<insert id="insertUserInfoVoByAttr" parameterType="java.util.Map">
		insert into ssds_user_info (<include refid="userinfo"/> ) 
		values 
		<foreach collection="list" item="li" separator=",">
			(#{li.wx_id},
			#{li.pro_id},
			#{li.u_name},
			#{li.u_icon},
			#{li.top_score},
			#{li.top_high},
			#{li.play_times},
			#{li.reward_num},
			#{li.phone},
			#{li.create_date},
			#{li.last_date}) 
		</foreach>	
		ON DUPLICATE KEY UPDATE 
		u_name=VALUES(u_name),
		u_icon=VALUES(u_icon),
		top_score=VALUES(top_score),
		top_high=VALUES(top_high),
		play_times=VALUES(play_times),
		reward_num=VALUES(reward_num),
		phone=VALUES(phone),
		last_date=VALUES(last_date)
	</insert>
	<update id="updateInitUserInfoVo">
		update ssds_user_info set top_score = 0,top_high = 0,reward_num = 0
	</update>
	<sql id="userinfo">
		wx_id,
		pro_id,
		u_name,
      	u_icon,
      	top_score,
      	top_high,
      	play_times,
      	reward_num,
      	phone,
      	create_date,
      	last_date
	</sql>

	<!-- 账号系统与飞书user_id关联关系-->
	<select id="selectSysFsInfo" resultType="com.wbgame.pojo.SysUfsVo" parameterType="com.wbgame.pojo.SysUfsVo">
        select * from wb_fs_account_info where 1=1
		<if test="login_name != null and login_name != ''">
			and login_name = #{login_name}
		</if>
		<if test="statu != null and statu != ''">
			and statu = #{statu}
		</if>

		<if test="department != null and department != ''">
			and department like "%" #{department} "%"
		</if>

		<if test="post != null and post != ''">
			and post like "%" #{post} "%"
		</if>
		order by createTime desc
    </select>

	<insert id="insertSysFsInfo" parameterType="com.wbgame.pojo.SysUfsVo">
		insert into wb_fs_account_info (login_name,username,fsuser_id,statu,createUser,createTime,department,post) values
		 (#{login_name},#{username},#{fsuser_id},#{statu},#{createUser},NOW(),#{department},#{post});
	</insert>

	<update id="updateSysFsInfo" parameterType="com.wbgame.pojo.SysUfsVo">
		update wb_fs_account_info set  login_name =#{login_name},username =#{username},fsuser_id =#{fsuser_id},
		statu =#{statu},modifyUser =#{modifyUser},modifyTime =NOW(), department = #{department}, post = #{post}
		where  id =#{id}
	</update>

	<delete id="delSysFsInfo" parameterType="com.wbgame.pojo.SysUfsVo">
		delete from wb_fs_account_info where  id =#{id}
	</delete>

	<!-- CDN缓存刷新记录-->
	<insert id="saveRefreshCDNUrl">
		replace into ad_refresh_cdn_url_log (username,url,createtime) values (#{username},#{url},NOW());
	</insert>

	<!-- 获取CDN缓存刷新记录-->
	<select id="selectRefreshCDNUrlList" resultType="com.wbgame.pojo.RefreshCDNVo" parameterType="com.wbgame.pojo.RefreshCDNVo">
		SELECT  * FROM  ad_refresh_cdn_url_log where username =#{username} order by createtime desc limit 10
	</select>
</mapper>