<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.WindControlLevelMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.operate.WindControlLevelVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <id column="appid" property="appid" jdbcType="INTEGER"/>
        <result column="prjid" property="prjid" jdbcType="VARCHAR"/>
        <result column="event_group" property="eventGroup" jdbcType="VARCHAR"/>
        <result column="event_name" property="eventName" jdbcType="VARCHAR"/>
        <result column="hclass" property="hclass" jdbcType="VARCHAR"/>
        <result column="hkey" property="hkey" jdbcType="VARCHAR"/>
        <result column="hvalue" property="hvalue" jdbcType="VARCHAR"/>
        <result column="note" property="note" jdbcType="VARCHAR"/>
        <result column="score" property="score" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,appid, prjid,
         event_group, event_name, hclass, hkey, hvalue, note, score,
        create_user,
        update_user,
        date_format(create_time, '%Y-%m-%d %H:%i:%s') create_time,
        date_format(update_time, '%Y-%m-%d %H:%i:%s') update_time
    </sql>

    <select id="selectWindControlLevel" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.operate.WindControlLevelDTO">
        select
        <include refid="Base_Column_List"/>
        from wind_control_level
        <where>

            <if test="eventName != null and eventName != ''">

                and event_name like "%" #{eventName} "%"

            </if>
            <if test="eventGroup != null and eventGroup != ''">

                and event_group like "%" #{eventGroup} "%"
            </if>

        </where>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by id desc
            </otherwise>
        </choose>
    </select>

    <delete id="deleteByIdList" parameterType="java.lang.Integer">
        delete
        from wind_control_level
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertWindControlLevel" >

        <choose>
            <when test="version == 1">

                INSERT into wind_control_level(appid,prjid,event_group,event_name,hclass,hkey,hvalue,note,score, create_user)

                SELECT #{appid} as appid,#{prjid} as prjid,event_group,event_name,hclass,hkey,hvalue,note,score, #{userName} as create_user
                FROM wind_control_level where prjid is null
            </when>
            <otherwise>

                INSERT into wind_control_level(appid,prjid,event_group,event_name,hclass,hkey,hvalue,note,score, create_user)

                SELECT #{appid} as appid,#{prjid} as prjid,event_group,event_name,hclass,hkey,hvalue,note,score, #{userName} as create_user
                FROM wind_control_level where prjid = 38801022

            </otherwise>
        </choose>

    </insert>

    <update id="updateWindControlLevel" parameterType="com.wbgame.pojo.operate.WindControlLevel">
        update wind_control_level
        <set>
            <if test="eventGroup != null and eventGroup != ''">
                event_group = #{eventGroup,jdbcType=VARCHAR},
            </if>
            <if test="eventName != null and eventName != ''">
                event_name = #{eventName,jdbcType=VARCHAR},
            </if>
            <if test="hclass != null and hclass != ''">
                hclass = #{hclass,jdbcType=VARCHAR},
            </if>
            <if test="hkey != null and hkey != ''">
                hkey = #{hkey,jdbcType=VARCHAR},
            </if>
            <if test="hvalue != null and hvalue != ''">
                hvalue = #{hvalue,jdbcType=VARCHAR},
            </if>
            <if test="note != null and note != ''">
                note = #{note,jdbcType=VARCHAR},
            </if>
            <if test="score != null">
                score = #{score,jdbcType=INTEGER},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser},
            </if>
            update_time = current_timestamp
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <insert id="copyWindControlLevel" parameterType="com.wbgame.pojo.operate.WindControlLevel"
            keyColumn="id" useGeneratedKeys="true" keyProperty="id">
        insert into wind_control_level
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="appid != null and appid != ''">
                appid,
            </if>
            <if test="prjid != null and prjid != ''">
                prjid,
            </if>

            <if test="eventGroup != null and eventGroup != ''">
                event_group,
            </if>
            <if test="eventName != null and eventName != ''">
                event_name,
            </if>
            <if test="hclass != null and hclass != ''">
                hclass,
            </if>
            <if test="hkey != null and hkey != ''">
                hkey,
            </if>
            <if test="hvalue != null and hvalue != ''">
                hvalue,
            </if>
            <if test="note != null and note != ''">
                note,
            </if>
            <if test="score != null">
                score,
            </if>

            <if test="createUser != null and createUser != ''">
                create_user,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="appid != null and appid != ''">
                #{appid},
            </if>
            <if test="prjid != null and prjid != ''">
                #{prjid},
            </if>

            <if test="eventGroup != null and eventGroup != ''">
                #{eventGroup,jdbcType=VARCHAR},
            </if>
            <if test="eventName != null and eventName != ''">
                #{eventName,jdbcType=VARCHAR},
            </if>
            <if test="hclass != null and hclass != ''">
                #{hclass,jdbcType=VARCHAR},
            </if>
            <if test="hkey != null and hkey != ''">
                #{hkey,jdbcType=VARCHAR},
            </if>
            <if test="hvalue != null and hvalue != ''">
                #{hvalue,jdbcType=VARCHAR},
            </if>
            <if test="note != null and note != ''">
                #{note,jdbcType=VARCHAR},
            </if>
            <if test="score != null">
                #{score,jdbcType=INTEGER},
            </if>

            <if test="createUser != null and createUser != ''">
                #{createUser},
            </if>
        </trim>
    </insert>
</mapper>