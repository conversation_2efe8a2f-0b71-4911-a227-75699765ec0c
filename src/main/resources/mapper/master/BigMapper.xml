<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.BigMapper">
	
	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>
	
	<insert id="insertExtendAdtypeRevise" parameterType="java.lang.String">
		REPLACE INTO dnwx_bi.dn_extend_revise_adtype(tdate,appid,cha_id,cha_type_name,cha_media,sum_revenue,
			pv_splash,pv_plaque,pv_banner,pv_video,pv_msg,revenue_splash,revenue_plaque,revenue_banner,revenue_video,revenue_msg)

		select '${tdate}' tdate,aa.appid,aa.cha_id,cc.type_name,bb.cha_media,SUM(aa.revise_revenue) revenue,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_show ELSE 0 END) pv_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_show ELSE 0 END) pv_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_show ELSE 0 END) pv_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_show ELSE 0 END) pv_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_show ELSE 0 END) pv_msg,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_revenue ELSE 0 END) revenue_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_revenue ELSE 0 END) revenue_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_revenue ELSE 0 END) revenue_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_revenue ELSE 0 END) revenue_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_revenue ELSE 0 END) revenue_msg
		from dnwx_bi.dn_extend_revise_income aa 
		LEFT JOIN dn_channel_info bb ON aa.cha_id=bb.cha_id
		LEFT JOIN dn_channel_type cc ON bb.cha_type=cc.type_id
		where aa.tdate = '${tdate}' and aa.revise_show is not null 
		and SUBSTRING_INDEX(aa.adsid,'_',1) not in ('Mjuhe','bxm')
		group by aa.appid,aa.cha_id
	</insert>
	<insert id="insertExtendAdtypeReviseTwo" parameterType="java.lang.String">
		REPLACE INTO dnwx_bi.dn_extend_revise_adtype_two(tdate,appid,cha_id,cha_type_name,cha_media,sum_revenue,
			pv_splash,pv_plaque,pv_banner,pv_video,pv_msg,revenue_splash,revenue_plaque,revenue_banner,revenue_video,revenue_msg)

		select '${tdate}' tdate,aa.appid,aa.cha_id,cc.type_name,bb.cha_media,SUM(aa.revise_revenue) revenue,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_show ELSE 0 END) pv_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_show ELSE 0 END) pv_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_show ELSE 0 END) pv_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_show ELSE 0 END) pv_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_show ELSE 0 END) pv_msg,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_revenue ELSE 0 END) revenue_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_revenue ELSE 0 END) revenue_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_revenue ELSE 0 END) revenue_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_revenue ELSE 0 END) revenue_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_revenue ELSE 0 END) revenue_msg
		from dnwx_bi.dn_extend_revise_income aa 
		LEFT JOIN dn_channel_info bb ON aa.cha_id=bb.cha_id
		LEFT JOIN dn_channel_type cc ON bb.cha_type=cc.type_id
		where aa.tdate = '${tdate}' and aa.revise_show is not null 
		and SUBSTRING_INDEX(aa.adsid,'_',1) not in ('Mjuhe','bxm')
		group by aa.appid,aa.cha_id
	</insert>
	
	<insert id="insertExtendAdtypeGroup" parameterType="java.lang.String">
		REPLACE INTO dnwx_bi.dn_extend_group_adtype(tdate,appid,cha_id,prjid,user_group,actnum,addnum,sum_revenue,
			pv_splash,pv_plaque,pv_banner,pv_video,pv_msg,revenue_splash,revenue_plaque,revenue_banner,revenue_video,revenue_msg)

		select '${tdate}' tdate,aa.appid,aa.cha_id,aa.prjid,aa.user_group,aa.actnum,aa.addnum,SUM(aa.revise_revenue) revenue,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_show ELSE 0 END) pv_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_show ELSE 0 END) pv_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_show ELSE 0 END) pv_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_show ELSE 0 END) pv_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_show ELSE 0 END) pv_msg,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_revenue ELSE 0 END) revenue_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_revenue ELSE 0 END) revenue_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_revenue ELSE 0 END) revenue_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_revenue ELSE 0 END) revenue_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_revenue ELSE 0 END) revenue_msg
		from dnwx_bi.dn_extend_revise_income aa 
		where aa.tdate = '${tdate}' and aa.revise_show is not null 
		and SUBSTRING_INDEX(aa.adsid,'_',1) not in ('Mjuhe','bxm')
		group by aa.appid,aa.cha_id,aa.prjid,aa.user_group
	</insert>
	
	<insert id="insertExtendPrjidIncome" parameterType="java.lang.String">
		INSERT INTO dnwx_bi.dn_extend_prjid_income(tdate,appid,prjid,cha_id,cha_type_name,cha_media,sum_revenue,actnum,addnum,pv_splash,pv_plaque,pv_banner,pv_video,pv_msg,revenue_splash,revenue_plaque,revenue_banner,revenue_video,revenue_msg) 
		
		select '${tdate}' tdate,aa.appid,aa.prjid,aa.cha_id,cc.type_name,bb.cha_media,SUM(aa.revise_revenue) revenue,aa.actnum,aa.addnum,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_show ELSE 0 END) pv_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_show ELSE 0 END) pv_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_show ELSE 0 END) pv_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_show ELSE 0 END) pv_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_show ELSE 0 END) pv_msg,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_revenue ELSE 0 END) revenue_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_revenue ELSE 0 END) revenue_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_revenue ELSE 0 END) revenue_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_revenue ELSE 0 END) revenue_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_revenue ELSE 0 END) revenue_msg
		from dnwx_bi.dn_extend_revise_income aa 
		LEFT JOIN dn_channel_info bb ON aa.cha_id=bb.cha_id
		LEFT JOIN dn_channel_type cc ON bb.cha_type=cc.type_id
		where aa.tdate = '${tdate}' 
		group by aa.prjid,aa.cha_id
	</insert>
	
	<insert id="insertExtendSubchaTotal" parameterType="java.lang.String">
		REPLACE INTO dnwx_bi.dn_extend_subcha_total(tdate,appid,cha_id,sub_cha,cha_type_name,cha_media,addnum,actnum,sum_revenue,
			pv_splash,pv_plaque,pv_banner,pv_video,pv_msg,revenue_splash,revenue_plaque,revenue_banner,revenue_video,revenue_msg) 
			
		select '${tdate}' tdate,aa.appid,aa.cha_id,aa.sub_cha,cc.type_name,bb.cha_media,aa.addnum,aa.actnum,SUM(aa.revenue) revenue,
			SUM(CASE WHEN adpos_type = 'splash' THEN self_show ELSE 0 END) pv_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN self_show ELSE 0 END) pv_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN self_show ELSE 0 END) pv_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN self_show ELSE 0 END) pv_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN self_show ELSE 0 END) pv_msg,
			SUM(CASE WHEN adpos_type = 'splash' THEN revenue ELSE 0 END) revenue_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revenue ELSE 0 END) revenue_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revenue ELSE 0 END) revenue_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revenue ELSE 0 END) revenue_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revenue ELSE 0 END) revenue_msg
		from dnwx_bi.dn_extend_subcha_income aa 
		LEFT JOIN dn_channel_info bb ON aa.cha_id=bb.cha_id
		LEFT JOIN dn_channel_type cc ON bb.cha_type=cc.type_id
		where aa.tdate = '${tdate}' 
		group by aa.appid,aa.cha_id,aa.sub_cha
	</insert>
	<select id="selectSubchaTotalOfBanner" parameterType="java.lang.String" resultType="java.util.Map">
		SELECT '${tdate}' tdate,xx.appid,xx.cha_id,xx.sub_cha,
			truncate(xx.actnum/yy.actSum*zz.pv,0) pv,
			IFNULL(truncate(xx.actnum/yy.actSum*zz.income,2),0) income FROM

		(SELECT appid,cha_id,sub_cha,actnum FROM dnwx_bi.dn_extend_subcha_total where tdate=#{tdate}
		GROUP BY appid,cha_id,sub_cha) xx

		LEFT JOIN
		(SELECT appid,cha_id,SUM(actnum) actSum FROM dnwx_bi.dn_extend_subcha_total where tdate=#{tdate}
		GROUP BY appid,cha_id) yy
		ON xx.appid=yy.appid AND xx.cha_id=yy.cha_id

		LEFT JOIN
		(select dnappid appid,cha_id,
			TRUNCATE(sum(aa.revenue),2) income,
			TRUNCATE(sum(aa.pv),0) pv
		from dn_cha_cash_total aa where date = #{tdate}
		and placement_type in ('banner') and app_id != '0' and dnappid != '0' and cha_id is not null
		group by dnappid,cha_id having (sum(pv)+sum(revenue)) > 0 ) zz
		ON xx.appid=zz.appid AND xx.cha_id=zz.cha_id
	</select>
	
	<!-- 项目ID+子渠道+二级子渠道分组的新增 活跃 -->
    <insert id="insertPushSubChaAddUser" parameterType="java.util.Map">
    	insert into dnwx_bi.dn_extend_subcha_total(tdate, appid, cha_id, sub_cha, addnum)
		
		SELECT '${today}' as mmdate, SUBSTR(projectid,1,5), chaid, sub_channel, COUNT(DISTINCT aa.lsn) as addnum 
		FROM (
			<foreach collection="array" item="it" separator=" union all ">
				select projectid,chaid,sub_channel,lsn from push_subchauser_info_hash${it} 
				where createtime BETWEEN '${today} 00:00:00' AND '${today} 23:59:59'
			</foreach>
		) aa 
		GROUP BY SUBSTR(projectid,1,5),chaid,sub_channel
		
		ON DUPLICATE KEY UPDATE 
		addnum=VALUES(addnum)
    </insert>
	<insert id="insertPushSubChaActUser" parameterType="java.util.Map">
    	insert into dnwx_bi.dn_extend_subcha_total(tdate, appid, cha_id, sub_cha, actnum)
		
		SELECT '${today}' as mmdate, SUBSTR(projectid,1,5), chaid, sub_channel, COUNT(DISTINCT aa.lsn) as actnum 
		FROM (
			<foreach collection="array" item="it" separator=" union all ">
				select projectid,chaid,sub_channel,lsn from push_subchauser_info_hash${it} 
				where lasttime BETWEEN '${today} 00:00:00' AND '${today} 23:59:59'
			</foreach>
		) aa 
		GROUP BY SUBSTR(projectid,1,5),chaid,sub_channel
		
		ON DUPLICATE KEY UPDATE 
		actnum=VALUES(actnum)
    </insert>
    
    
    
    <!-- 变现-数据gap统计  -->
  	<select id="selectDnShowGapTotal" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_showgap_sql"/>
	</select>
	<select id="selectDnShowGapTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.request_count) request_count,
			SUM(xx.req_num) req_num,
			CONCAT(TRUNCATE((sum(xx.req_num)-sum(xx.request_count))/sum(xx.req_num)*100, 1),'%') req_gap,
			
			SUM(xx.return_count) return_count,
			SUM(xx.fill_num) fill_num,
			CONCAT(TRUNCATE((sum(xx.fill_num)-sum(xx.return_count))/sum(xx.fill_num)*100, 1),'%') return_gap,
			
			SUM(xx.pv) pv,
			SUM(xx.selfshow_num) selfshow_num,
			CONCAT(TRUNCATE((sum(xx.selfshow_num)-sum(xx.pv))/sum(xx.selfshow_num)*100, 1),'%') pv_gap,
			
			SUM(xx.click) click,
			SUM(xx.click_num) click_num,
			CONCAT(TRUNCATE((sum(xx.click_num)-sum(xx.click))/sum(xx.click_num)*100, 1),'%') click_gap
		from (<include refid="dn_showgap_sql"/>) xx
	</select>
	<sql id="dn_showgap_sql">
		select 
			${group},
			SUM(request_count) request_count,
			SUM(return_count) return_count,
			SUM(pv) pv,
			SUM(click) click,
			
			SUM(req_num) req_num,
			SUM(fill_num) fill_num,
			SUM(selfshow_num) selfshow_num,
			SUM(click_num) click_num,
			
			CONCAT(TRUNCATE((sum(req_num)-sum(request_count))/sum(req_num)*100, 1),'%') req_gap,
			CONCAT(TRUNCATE((sum(fill_num)-sum(return_count))/sum(fill_num)*100, 1),'%') return_gap,
			CONCAT(TRUNCATE((sum(selfshow_num)-sum(pv))/sum(selfshow_num)*100, 1),'%') pv_gap,
			CONCAT(TRUNCATE((sum(click_num)-sum(click))/sum(click_num)*100, 1),'%') click_gap
		from dnwx_bi.dn_show_gap 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid in (${adsid}) 
		</if>
		<if test="agent != null and agent != ''">
			and agent = #{agent} 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>
		<!-- AND adpos_type not in ('banner') -->
		
		group by ${group}
		order by tdate desc,pv desc
	</sql>
	
    <!-- 变现收入校准  -->
  	<select id="selectExtendIncomeRevise" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_revise_income_sql"/>
	</select>
	<select id="selectExtendIncomeReviseSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.self_show) self_show,
			SUM(xx.revise_show) revise_show,
			SUM(xx.revise_revenue) revise_revenue
			<!-- CONCAT(TRUNCATE((sum(xx.selfshow_num)-sum(xx.pv))/sum(xx.selfshow_num)*100, 1),'%') pv_gap -->
		from (<include refid="dn_revise_income_sql"/>) xx
	</select>
	<sql id="dn_revise_income_sql">
		select 
			tdate,appid,cha_id,prjid,adsid,adpos_type,user_group,
			sdk_adtype,
			ecpm,
			gap_val,
			self_show,
			TRUNCATE(self_show*(1-gap_val*0.01),0) revise_show,
			TRUNCATE(ecpm*(self_show*(1-gap_val*0.01))/1000, 2) revise_revenue,
			actnum,
			addnum
		from dnwx_bi.dn_extend_revise_income 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid}
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid in (${adsid}) 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="user_group != null and user_group != ''">
			and user_group like concat('%',#{user_group},'%') 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>
		
		group by tdate,appid,cha_id,prjid,adsid,adpos_type,user_group
		order by tdate desc,self_show desc
	</sql>
	
	<!-- 汇总数据校准  -->
  	<select id="selectAdtypeTotalRevise" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_revise_adtype_sql"/>
	</select>
	<select id="selectAdtypeTotalReviseSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.actnum) actnum,
			SUM(xx.addnum) addnum,
			CONCAT(TRUNCATE(SUM(xx.addnum) / SUM(xx.actnum)*100, 1),'%') addrate,
			SUM(xx.sum_revenue) sum_revenue,
			
			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.addnum), 2),0) add_arpu,
			
			
			IFNULL(TRUNCATE(SUM(xx.pv_splash) / SUM(xx.actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque) / SUM(xx.actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner) / SUM(xx.actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video) / SUM(xx.actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg) / SUM(xx.actnum), 1),0) avg_pv_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.pv_msg)*1000, 1),0) ecpm_msg,
			
			IFNULL(TRUNCATE(SUM(xx.pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg), 0),0) pv_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg), 2),0) revenue_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.actnum), 2),0) arpu_msg
			
		from (<include refid="dn_revise_adtype_sql"/>) xx
	</select>
	<sql id="dn_revise_adtype_sql">
		select
		<if test="group != null and group != ''">
			${group},
		</if>
			SUM(actnum) actnum,
			SUM(addnum) addnum,
			CONCAT(TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1),'%') addrate,
			SUM(sum_revenue) sum_revenue,
			IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(addnum), 2),0) add_arpu,
			
			
			IFNULL(TRUNCATE(SUM(pv_splash) / SUM(actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque) / SUM(actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner) / SUM(actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video) / SUM(actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg) / SUM(actnum), 1),0) avg_pv_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(pv_msg)*1000, 1),0) ecpm_msg,
			
			IFNULL(TRUNCATE(SUM(pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg), 0),0) pv_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(revenue_msg), 2),0) revenue_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(actnum), 2),0) arpu_msg
			
		from ${tableName} 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name}) 
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media}) 
		</if>
		<if test="user_group != null and user_group != ''">
			and user_group = #{user_group} 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>

		<if test="group != null and group != ''">
			  group by ${group}
		</if>

		order by tdate desc,actnum desc
	</sql>

	<!-- 二级子渠道展示收入  -->
  	<select id="selectSubchaTotal" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_subcha_total_sql"/>
	</select>
	<select id="selectSubchaTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
			SUM(xx.actnum) actnum,
			SUM(xx.addnum) addnum,
			CONCAT(TRUNCATE(SUM(xx.addnum) / SUM(xx.actnum)*100, 1),'%') addrate,
			SUM(xx.sum_revenue) sum_revenue,

			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.addnum), 2),0) add_arpu,


			IFNULL(TRUNCATE(SUM(xx.pv_splash) / SUM(xx.actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque) / SUM(xx.actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner) / SUM(xx.actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video) / SUM(xx.actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg) / SUM(xx.actnum), 1),0) avg_pv_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.pv_msg)*1000, 1),0) ecpm_msg,

			IFNULL(TRUNCATE(SUM(xx.pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg), 0),0) pv_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg), 2),0) revenue_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.actnum), 2),0) arpu_msg

		from (<include refid="dn_subcha_total_sql"/>) xx
	</select>
	<sql id="dn_subcha_total_sql">
		select
			${group},
			SUM(actnum) actnum,
			SUM(addnum) addnum,
			CONCAT(TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1),'%') addrate,
			SUM(sum_revenue) sum_revenue,
			IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(addnum), 2),0) add_arpu,


			IFNULL(TRUNCATE(SUM(pv_splash) / SUM(actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque) / SUM(actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner) / SUM(actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video) / SUM(actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg) / SUM(actnum), 1),0) avg_pv_msg,

			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(pv_msg)*1000, 1),0) ecpm_msg,

			IFNULL(TRUNCATE(SUM(pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg), 0),0) pv_msg,

			IFNULL(TRUNCATE(SUM(revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(revenue_msg), 2),0) revenue_msg,

			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(actnum), 2),0) arpu_msg

		from dnwx_bi.dn_extend_subcha_total
		where tdate BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id})
		</if>
		<if test="sub_cha != null and sub_cha != ''">
			and sub_cha in (${sub_cha})
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media})
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>

		group by ${group}
		order by tdate desc,actnum desc
	</sql>

	<!-- 项目ID收入预估  -->
  	<select id="selectPrjidTotalIncome" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_prjid_income_sql"/>
	</select>
	<select id="selectPrjidTotalIncomeSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.actnum) actnum,
			SUM(xx.addnum) addnum,
			CONCAT(TRUNCATE(SUM(xx.addnum) / SUM(xx.actnum)*100, 1),'%') addrate,
			SUM(xx.sum_revenue) sum_revenue,
			
			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.addnum), 2),0) add_arpu,
			
			
			IFNULL(TRUNCATE(SUM(xx.pv_splash) / SUM(xx.actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque) / SUM(xx.actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner) / SUM(xx.actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video) / SUM(xx.actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg) / SUM(xx.actnum), 1),0) avg_pv_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.pv_msg)*1000, 1),0) ecpm_msg,
			
			IFNULL(TRUNCATE(SUM(xx.pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg), 0),0) pv_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg), 2),0) revenue_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.actnum), 2),0) arpu_msg
			
		from (<include refid="dn_prjid_income_sql"/>) xx
	</select>
	<sql id="dn_prjid_income_sql">
		select 
			${group},
			SUM(actnum) actnum,
			SUM(addnum) addnum,
			CONCAT(TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1),'%') addrate,
			SUM(sum_revenue) sum_revenue,
			IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(addnum), 2),0) add_arpu,
			
			
			IFNULL(TRUNCATE(SUM(pv_splash) / SUM(actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque) / SUM(actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner) / SUM(actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video) / SUM(actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg) / SUM(actnum), 1),0) avg_pv_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(pv_msg)*1000, 1),0) ecpm_msg,
			
			IFNULL(TRUNCATE(SUM(pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg), 0),0) pv_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(revenue_msg), 2),0) revenue_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(actnum), 2),0) arpu_msg
			
		from dnwx_bi.dn_extend_prjid_income 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid in (${prjid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name}) 
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media}) 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>
		
		group by ${group} 
		order by tdate desc,actnum desc
	</sql>
	
	<!-- 聚合综合查询  -->
  	<select id="selectDnGroupCom" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_group_com_sql"/>
	</select>
	<select id="selectDnGroupComSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.income) income,
			SUM(xx.req_num) req_num,
			SUM(xx.fill_num) fill_num,
			SUM(xx.show_num) show_num,
			SUM(xx.click_num) click_num,
			CONCAT(TRUNCATE(sum(xx.fill_num)/sum(xx.req_num)*100, 1),'%') fill_rate,
			CONCAT(TRUNCATE(sum(xx.show_num)/sum(xx.fill_num)*100, 1),'%') show_rate,
			CONCAT(TRUNCATE(sum(xx.click_num)/sum(xx.show_num)*100, 1),'%') click_rate,
			
			SUM(xx.platform_show) platform_show,
			SUM(xx.platform_fill) platform_fill,
			SUM(xx.platform_req) platform_req,
			CONCAT(TRUNCATE((SUM(xx.platform_show)-SUM(xx.show_num)) / SUM(xx.platform_show) * 100, 1),'%') show_gap,
			CONCAT(TRUNCATE((SUM(xx.platform_fill)-SUM(xx.fill_num)) / SUM(xx.platform_fill) * 100, 1),'%') fill_gap,
			CONCAT(TRUNCATE((SUM(xx.platform_req)-SUM(xx.req_num)) / SUM(xx.platform_req) * 100, 1),'%') req_gap
		from (<include refid="dn_group_com_sql"/>) xx
	</select>
	<sql id="dn_group_com_sql">
		select 
			concat(tdate,'') date,
			appid,
			cha_id,
			adsid,
			strategy,
			adpos_type,
			agent,
			sdk_adtype,
			ecpm,
			is_newuser,
			SUM(aa.income) income,
			SUM(aa.req_num) req_num,
			SUM(aa.fill_num) fill_num,
			SUM(aa.show_num) show_num,
			SUM(aa.click_num) click_num,
			aa.ad_load_duration,
			CONCAT(TRUNCATE(sum(aa.fill_num)/sum(aa.req_num)*100, 1),'%') fill_rate,
			CONCAT(TRUNCATE(sum(aa.show_num)/sum(aa.fill_num)*100, 1),'%') show_rate,
			CONCAT(TRUNCATE(sum(aa.click_num)/sum(aa.show_num)*100, 1),'%') click_rate,
			
			platform_ecpm,
			CONCAT(TRUNCATE((platform_ecpm-ecpm) / ecpm * 100, 1),'%') ecpm_gap,
			SUM(aa.platform_show) platform_show,
			CONCAT(TRUNCATE((SUM(aa.platform_show)-SUM(aa.show_num)) / SUM(aa.platform_show) * 100, 1),'%') show_gap,
			SUM(aa.platform_fill) platform_fill,
			CONCAT(TRUNCATE((SUM(aa.platform_fill)-SUM(aa.fill_num)) / SUM(aa.platform_fill) * 100, 1),'%') fill_gap,
			SUM(aa.platform_req) platform_req,
			CONCAT(TRUNCATE((SUM(aa.platform_req)-SUM(aa.req_num)) / SUM(aa.platform_req) * 100, 1),'%') req_gap
			
		from dnwx_bi.dn_extend_group_sum aa 
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id = #{cha_id} 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid = #{adsid} 
		</if>
		<if test="agent != null and agent != ''">
			and agent = #{agent} 
		</if>
		<if test="sdk_adtype != null and sdk_adtype != ''">
			and sdk_adtype = #{sdk_adtype} 
		</if>
		<if test="is_newuser != null and is_newuser != ''">
			and is_newuser in (${is_newuser}) 
		</if>
		<if test="strategy != null and strategy != ''">
			and strategy like concat('%',#{strategy},'%') 
		</if>
		<if test="income_beyond != null and income_beyond != ''">
			and income > 0
		</if>
		group by tdate,adsid
		order by tdate asc,strategy asc,ecpm desc
	</sql>
	<!-- 变现-广告位数据查询  -->
  	<select id="selectAdposData" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_adpos_sql"/>
	</select>
	<select id="selectAdposDataSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.income) income,
			SUM(xx.show_num) show_num,
			SUM(xx.click_num) click_num,
			CONCAT(ROUND(sum(xx.click_num)/sum(xx.show_num)*100, 2),'%') click_rate
			<!-- CONCAT(ROUND(sum(xx.device_num)/xx.dau*100,2),'%') as seep_rate -->
		from (<include refid="dn_adpos_sql"/>) xx
	</select>
	<sql id="dn_adpos_sql">
		select 
			concat(appid,cha_id,adpos) mapkey,
			concat(tdate,'') tdate,
			appid,cha_id ${group},
			dau,
			TRUNCATE(sum(income),2) income,
			SUM(show_num) show_num,
			SUM(click_num) click_num,
			SUM(device_num) device_num,
			CONCAT(ROUND(sum(click_num)/sum(show_num)*100, 2),'%') click_rate,
			CONCAT(ROUND(sum(device_num)/dau*100,2),'%') as seep_rate,
			TRUNCATE(sum(show_num)/dau, 1) as per_pv,
			TRUNCATE(sum(show_num)/sum(device_num), 1) as seep_per_pv
		from dnwx_bi.dn_extend_adpos_data 
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid}
		</if>
		<if test="adpos != null and adpos != ''">
			and adpos = #{adpos} 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid = #{adsid} 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>
		
		group by tdate,appid,cha_id ${group}
		order by tdate asc,adpos_type asc,income desc 
	</sql>
	
	<!-- 汇总数据-用户群  -->
  	<select id="selectAdtypeTotalGroup" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_group_adtype_sql"/>
	</select>
	<select id="selectAdtypeTotalGroupSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.actnum) actnum,
			SUM(xx.addnum) addnum,
			CONCAT(TRUNCATE(SUM(xx.addnum) / SUM(xx.actnum)*100, 1),'%') addrate,
			SUM(xx.sum_revenue) sum_revenue,
			
			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.addnum), 2),0) add_arpu,
			
			
			IFNULL(TRUNCATE(SUM(xx.pv_splash) / SUM(xx.actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque) / SUM(xx.actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner) / SUM(xx.actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video) / SUM(xx.actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg) / SUM(xx.actnum), 1),0) avg_pv_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.pv_msg)*1000, 1),0) ecpm_msg,
			
			IFNULL(TRUNCATE(SUM(xx.pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg), 0),0) pv_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg), 2),0) revenue_msg,
			
			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.actnum), 2),0) arpu_msg
			
		from (<include refid="dn_group_adtype_sql"/>) xx
	</select>
	<sql id="dn_group_adtype_sql">
		select
			<if test="group != null and group != ''">
				${group},
			</if>
			SUM(actnum) actnum,
			SUM(addnum) addnum,
			CONCAT(TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1),'%') addrate,
			SUM(sum_revenue) sum_revenue,
			IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(addnum), 2),0) add_arpu,
			
			
			IFNULL(TRUNCATE(SUM(pv_splash) / SUM(actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque) / SUM(actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner) / SUM(actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video) / SUM(actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg) / SUM(actnum), 1),0) avg_pv_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(pv_msg)*1000, 1),0) ecpm_msg,
			
			IFNULL(TRUNCATE(SUM(pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg), 0),0) pv_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(revenue_msg), 2),0) revenue_msg,
			
			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(actnum), 2),0) arpu_msg
			
		from dnwx_bi.dn_extend_group_adtype 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid} 
		</if>
		<if test="user_group != null and user_group != ''">
			and user_group = #{user_group} 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>

		<if test="group != null and group != ''">
			 group by ${group}
		</if>

		order by tdate desc,actnum desc
	</sql>
	
</mapper>