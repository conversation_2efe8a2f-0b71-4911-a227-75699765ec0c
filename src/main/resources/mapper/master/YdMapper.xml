<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.YdMapper">

    <!-- 用户信息维护查询 -->
    <select id="selectListByParam" resultType="com.wbgame.pojo.UserMaintain" parameterType="java.lang.Integer" >
        select
        loginId, level, quest_stat_list AS questStatList,
        finish_quest_list AS finishQuestList, sysVersion, create_time AS createTime,
        creator
        from yd_user_maintain
        where 1=1
        <if test="loginId != null ">
            and loginId = #{loginId}
        </if>
    </select>

    <!-- 用户信息维护添加 -->
    <insert id="insert" parameterType="com.wbgame.pojo.UserMaintain" >
    insert into yd_user_maintain (loginId, level, quest_stat_list, 
      finish_quest_list, sysVersion, create_time, 
      creator)
    values (#{loginId,jdbcType=INTEGER}, #{level,jdbcType=VARCHAR}, #{questStatList,jdbcType=VARCHAR}, 
      #{finishQuestList,jdbcType=VARCHAR}, #{sysversion,jdbcType=INTEGER}, NOW(), 
      #{creator,jdbcType=VARCHAR})
  </insert>

    <!-- 用户信息维护修改 -->
    <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.UserMaintain" >
    update yd_user_maintain
    set level = #{level,jdbcType=VARCHAR},
      quest_stat_list = #{questStatList,jdbcType=VARCHAR},
      finish_quest_list = #{finishQuestList,jdbcType=VARCHAR},
      sysVersion = #{sysversion,jdbcType=INTEGER}
    where loginId = #{loginId,jdbcType=INTEGER}
  </update>

    <!-- 红包产品数据查询 -->
    <select id="selectRedPack" resultType="com.wbgame.pojo.RedPackConfigVo" >
        SELECT
        a.c_date AS cDate,
        b.gameName AS gameName ,
        b.platform AS platform,
        <if test="hide != 1">
            b.versionName AS versionName,
            a.c_pid AS cPid,
            a.c_price AS cPrice,
            a.c_users AS cUsers,
            a.c_total AS cTotal,
            concat(round(c_twoRate / a.c_users*100, 0),'%') AS cTwoRate,
            concat(round(c_threeRate / a.c_users*100, 0),'%') AS cThreeRate,
            concat(round(c_sevenRate / a.c_users*100, 0),'%') AS cSevenRate,
            a.c_dau AS cDau,
            concat(round(a.c_users / a.c_dau*100, 0),'%') AS rate
        </if >
        <if test="hide == 1">
            sum(a.c_users) AS cUsers,
            sum(a.c_total) AS cTotal,
            concat(round(sum(c_twoRate) / sum(a.c_users)*100, 0),'%') AS cTwoRate,
            concat(round(sum(c_threeRate) / sum(a.c_users)*100, 0),'%') AS cThreeRate,
            concat(round(sum(c_sevenRate) / sum(a.c_users)*100, 0),'%') AS cSevenRate,
            sum(a.c_dau) AS cDau,
            concat(round(sum(a.c_users) / sum(a.c_dau)*100, 0),'%') AS rate
        </if>
        FROM
        red_pack_audit_info a,
        dnwx_client.wbgui_formconfig b
        WHERE
        a.c_pid = b.pjId and a.c_price=0.30
        <if test="cPid != null and cPid != ''">
            and a.c_pid = #{cPid}
        </if>
        <if test="gameName != null and gameName != ''">
            and a.c_appid in (${gameName})
        </if>
        <if test = "startTime != null and endTime != null">
            and a.c_date BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="hide == 1">
            GROUP BY a.c_appid
        </if>
        <choose>
            <when test="order != null and order != ''">
               order by ${order}
            </when>
            <otherwise>
               order by a.c_date asc,cUsers desc
            </otherwise>
        </choose>
    </select>

    <select id="selectNowRedPack" resultType="com.wbgame.pojo.RedPackConfig"  >
        SELECT  a.c_date AS cDate,b.gameName AS gameName ,f.app_name, b.platform AS platform,
        a.c_appid AS cAppid,
        <if test="hide != 1">
            wx_success AS wxSuccess,
            c_cashUser AS cCashUser,
            c_withdrawUser AS cWithdrawUser,
            c_withdrawRet AS cWithdrawRet,
            wx_fail AS wxFail,
            ROUND(e.keep1*100) AS keep1,
            round(CASE WHEN ( d.c_twoRate / d.c_users * 100 )>= 100 THEN 100 ELSE ( d.c_twoRate / d.c_users * 100 ) END,0 ) AS cTwoRate,
            ROUND(a.wx_success /(wx_success+wx_fail),2)*100 AS  wxSuccessRate,
            c_withdraw AS cWithdraw,
            ROUND(CASE WHEN (a.c_users /a.c_withdraw*100 )>= 100 THEN 100 ELSE ( a.c_users /a.c_withdraw*100 ) END,0 ) AS withdrawSuccessRate,
            b.versionName AS versionName,a.c_pid AS cPid,a.c_price AS cPrice,
            a.c_users AS cUsers,
            a.c_total AS cTotal,
            a.c_overUser AS cOverUser,
            a.c_dau AS cDau,
            ROUND(a.c_users /a.c_overUser,2)*100 AS withdrawRate ,
            c.new_count AS newCount
        </if>
        <if test="hide == 1">
            sum(wx_success) AS wxSuccess,
            sum(c_cashUser) AS cCashUser,
            sum(c_withdrawUser) AS cWithdrawUser,
            sum(c_withdrawRet) AS cWithdrawRet,
            sum(wx_fail) AS wxFail ,
            sum(e.keep1) AS keep1,
            round(CASE WHEN (sum(d.c_twoRate) / sum(d.c_users)*100)>= 100 THEN 100 ELSE (sum(d.c_twoRate) / sum(d.c_users)*100) END,0 ) AS cTwoRate,
            ROUND(sum(a.wx_success) /(sum(wx_success)+sum(wx_fail)),2)*100 AS  wxSuccessRate,
            sum(c_withdraw) AS cWithdraw,
            ROUND(CASE WHEN ( sum(a.c_users) /sum(a.c_withdraw)*100 )>= 100 THEN 100 ELSE ( sum(a.c_users) /sum(a.c_withdraw)*100 ) END,0 ) AS withdrawSuccessRate,
            sum(a.c_users) AS cUsers,
            sum(a.c_total) AS cTotal,
            sum(a.c_overUser) AS cOverUser,
            sum(a.c_dau) AS cDau,
            ROUND(sum(a.c_users)/sum(a.c_overUser),2)*100 AS withdrawRate ,
            sum(c.new_count) AS newCount
        </if>
        FROM
        ${databasestr} AS a
        left JOIN  dnwx_client.wbgui_formconfig AS b on  a.c_pid = b.pjId
        left JOIN  user_newcount_total AS c on   a.c_date = c.tdate and a.c_pid = c.projectid
        left JOIN  red_pack_audit_info AS d on a.c_pid = d.c_pid and a.c_date = d.c_date and d.c_price=0.30
        left JOIN  red_pack_product_keep_info AS e  on a.c_date = e.c_date and a.c_pid = e.c_pid
        left join app_info f on a.c_appid = f.id
        WHERE
        1=1
        <if test="cPid != null and cPid != ''">
            and a.c_pid = ${cPid}
        </if>
        <if test="gameName != null and gameName != ''">
            and a.c_appid in (${gameName})
        </if>
        <if test = "startTime != null and endTime != null">
            and Date(a.c_date) BETWEEN '${startTime}' AND '${endTime}'
        </if>
        <if test="hide == 1">
            GROUP BY a.c_appid
        </if>
        <choose>
            <when test="order != null and order != ''">
                order by ${order}
            </when>
            <otherwise>
                order by cTotal desc
            </otherwise>
        </choose>
    </select>

    <!-- 新版系统参数配置查询 -->
    <select id="selectNOParam" resultType="com.wbgame.pojo.NOParam">
    select IS_ASSETS, AUDIT_PRJIDS,LOTTERYPRJIDS,NOFEE_PROJECT,NOFEE_CITY,FIRST_IMSI_PROJECTID,CYCLE_FLAG_NO_PROJECTID,MMNOTIFY_PROJECTIDS from np_param where ID =1
  </select>

    <!-- 新版系统参数配置修改 -->
    <update id="updateNOParam" parameterType="com.wbgame.pojo.NOParam">
	 update np_param set AUDIT_PRJIDS = #{AUDIT_PRJIDS,jdbcType=VARCHAR},
	 LOTTERYPRJIDS = #{LOTTERYPRJIDS,jdbcType=VARCHAR},
	 NOFEE_PROJECT = #{NOFEE_PROJECT,jdbcType=VARCHAR},
	 IS_ASSETS = #{IS_ASSETS,jdbcType=VARCHAR},
	 NOFEE_CITY = #{NOFEE_CITY,jdbcType=VARCHAR},
	 FIRST_IMSI_PROJECTID = #{FIRST_IMSI_PROJECTID,jdbcType=VARCHAR},
	 CYCLE_FLAG_NO_PROJECTID = #{CYCLE_FLAG_NO_PROJECTID,jdbcType=VARCHAR},
	 MMNOTIFY_PROJECTIDS = #{MMNOTIFY_PROJECTIDS,jdbcType=VARCHAR}
	 where ID =1
 </update>

    <update id="updateNofee" parameterType="com.wbgame.pojo.NOParam">
	 update np_param set 
	 NOFEE_PROJECT = #{NOFEE_PROJECT,jdbcType=VARCHAR}
	 where ID =1
 </update>

    <!-- 红包登录参数配置 查询 -->
    <select id="selectHongBaoInfo"  resultType="com.wbgame.pojo.HongBaoInfo" >
        select
        mch_id AS mchId,wxappid,logo_imgurl AS logoImgurl,appname,packname,appsecret,call_url AS callUrl,`key`,
        nick_name AS nickName,send_name AS sendName,wishing ,act_name AS actName,remark,pid
        ,create_owner,update_owner,create_time,update_time
        from wx_hongbao_info
        where 1=1
        <if test="packname != null and packname != ''">
            and packname = #{packname}
        </if>
        <if test="logoImgurl != null and logoImgurl != ''">
            and logo_imgurl = #{logoImgurl}
        </if>
        <if test="mchId != null and mchId != ''">
            and mch_id = #{mchId}
        </if>
        <if test="pid != null and pid != ''">
            and pid like concat('%',#{pid},'%')
        </if>
    </select>

    <!-- 红包登录参数配置 删除-->
    <delete id="deleteHongBaoInfo" parameterType="com.wbgame.pojo.HongBaoInfo" >
    delete from wx_hongbao_info
    where packname = #{packname,jdbcType=VARCHAR}
      and logo_imgurl = #{logoImgurl,jdbcType=VARCHAR}
      and pid = #{pid,jdbcType=VARCHAR}
      limit 1
  </delete>

    <!-- 红包登录参数配置 添加 -->
    <insert id="insertHongBaoInfo" parameterType="com.wbgame.pojo.HongBaoInfo" >
    insert into wx_hongbao_info (packname, logo_imgurl, mch_id, 
      wxappid, nick_name, send_name, 
      wishing, act_name, remark, appname, 
      appsecret, call_url,`key`,pid,create_owner)
    values (#{packname,jdbcType=VARCHAR}, #{logoImgurl,jdbcType=VARCHAR}, #{mchId,jdbcType=VARCHAR}, 
      #{wxappid,jdbcType=VARCHAR}, #{nickName,jdbcType=VARCHAR}, #{sendName,jdbcType=VARCHAR}, 
      #{wishing,jdbcType=VARCHAR}, #{actName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
        #{appname,jdbcType=VARCHAR}, 
      #{appsecret,jdbcType=VARCHAR}, #{callUrl,jdbcType=VARCHAR},#{key,jdbcType=VARCHAR},#{pid},#{create_owner})
  </insert>
    <!-- 红包登录参数配置 修改 -->
    <update id="updateHongBaoInfo" parameterType="com.wbgame.pojo.HongBaoInfo" >
        update wx_hongbao_info
        <set >
            <if test="mchId != null" >
                mch_id = #{mchId,jdbcType=VARCHAR},
            </if>
            <if test="wxappid != null" >
                wxappid = #{wxappid,jdbcType=VARCHAR},
            </if>
            <if test="nickName != null" >
                nick_name = #{nickName,jdbcType=VARCHAR},
            </if>
            <if test="sendName != null" >
                send_name = #{sendName,jdbcType=VARCHAR},
            </if>
            <if test="wishing != null" >
                wishing = #{wishing,jdbcType=VARCHAR},
            </if>
            <if test="actName != null" >
                act_name = #{actName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="appname != null" >
                appname = #{appname,jdbcType=VARCHAR},
            </if>
            <if test="appsecret != null" >
                appsecret = #{appsecret,jdbcType=VARCHAR},
            </if>
            <if test="callUrl != null" >
                call_url = #{callUrl,jdbcType=VARCHAR},
            </if>
            <if test="key != null" >
                `key` = #{key,jdbcType=VARCHAR},
            </if>
            <if test="update_owner != null" >
                update_owner = #{update_owner,jdbcType=VARCHAR},
            </if>
        </set>
        where packname = #{packname,jdbcType=VARCHAR}
        and logo_imgurl = #{logoImgurl,jdbcType=VARCHAR}
        and pid = #{pid,jdbcType=VARCHAR}
    </update>

    <select id="selectRusConfigInfo" resultType="com.wbgame.pojo.RusConfigInfo" parameterType="com.wbgame.pojo.RusConfigInfo" >
        select
        pid,type,amount,`desc`
        from rus_config
        where 1=1
        <if test="pid != null and pid != ''" >
            and pid = #{pid,jdbcType=VARCHAR}
        </if>
        <if test="type != null and type != ''" >
            and type = #{type,jdbcType=INTEGER}
        </if>
    </select>
    <delete id="deleteRusConfigInfo" parameterType="com.wbgame.pojo.RusConfigInfo" >
    delete from rus_config
    where pid = #{pid,jdbcType=VARCHAR}
      and type = #{type,jdbcType=INTEGER}
  </delete>
    <insert id="insertRusConfigInfo" parameterType="com.wbgame.pojo.RusConfigInfo" >
    insert into rus_config (pid, type, amount, 
      `desc`)
    values (#{pid,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, 
      #{desc,jdbcType=VARCHAR})
  </insert>

    <update id="updateRusConfigInfo" parameterType="com.wbgame.pojo.RusConfigInfo" >
    update rus_config
    set 
    	amount = #{amount},
      `desc` = #{desc,jdbcType=VARCHAR}
    where pid = #{pid,jdbcType=VARCHAR}
      and type = #{type,jdbcType=INTEGER}
  </update>

    <insert id="addRusConfigList" parameterType="java.util.List">
        insert into rus_config (pid, type, amount, `desc`)
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.pid},
            #{lpc.type},
            #{lpc.amount},
            #{lpc.desc})
        </foreach>

    </insert>

    <select id="selectRedbalanceVo" parameterType="com.wbgame.pojo.RedbalanceVo"  resultType="com.wbgame.pojo.RedbalanceVo">
        select
        userid,redbalance AS redBalance,imei,prjid, create_time AS createTime,modify_time AS modifyTime
        from rus_redbalance
        where 1=1
        <if test="userid != null and userid != ''" >
            and userid = #{userid}
        </if>
        <if test="imei != null and imei != ''" >
            and imei = #{imei}
        </if>
        <if test="prjid != null and prjid != ''" >
            and prjid = #{prjid}
        </if>
    </select>

    <select id="selectWithdrawDetailsNew" resultType="com.wbgame.pojo.RedPackConfig"  >
	 SELECT
		DATE_FORMAT( date, '%Y%m%d' ) AS cDate,
		SUBSTR( userid, 1, 5 ) AS cAppid,
		pid AS cPid,
		amount AS cPrice,
		count( DISTINCT userid ) AS cUsers,
		sum( amount ) AS cTotal 
	FROM
		withdraw 
	WHERE
		`desc` = '已发放' 
		and DATE_FORMAT( date, '%Y%m%d' ) = #{startTime}
		and pid = #{cPid} 
	GROUP BY
		DATE_FORMAT( date, '%Y%m%d' ),
		amount,
		SUBSTR( userid, 1, 5 ),
		pid
  </select>

    <!-- 查询前一天的红包产品明细数据 -->
    <select id="selectWithdrawDetailsMaster" resultType="com.wbgame.pojo.RedPackConfig"  >
		SELECT
			DATE( date )AS cDate,
			SUBSTR( userid, 1, 5 ) AS cAppid,
			pid AS cPid,
			amount AS cPrice,
			count( DISTINCT userid ) AS cUsers,
			sum( amount ) AS cTotal 
		FROM
			withdraw 
		WHERE
			`desc` = '已发放' 
			AND DATE( date ) = date_sub(curdate(),interval 1 day)
		GROUP BY
			DATE( date ),
			amount,
			SUBSTR( userid, 1, 5 ),
			pid
  </select>

    <!-- 添加前一天的红包产品明细数据 -->
    <insert id="insertRedPackAuditDetail" parameterType="java.util.List">
        insert into red_pack_audit_detail (c_date, c_appid, c_pid,
        c_price, c_users, c_total)
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.cDate},
            #{lpc.cAppid},
            #{lpc.cPid},
            #{lpc.cPrice},
            #{lpc.cUsers},
            #{lpc.cTotal}
            )
        </foreach>
    </insert>
    <insert id="insertRedPackAuditDetailTwo" parameterType="java.util.List">
        insert into red_pack_qq_audit_detail (c_date, c_appid, c_pid,
        c_price, c_users, c_total)
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.cDate},
            #{lpc.cAppid},
            #{lpc.cPid},
            #{lpc.cPrice},
            #{lpc.cUsers},
            #{lpc.cTotal}
            )
        </foreach>
    </insert>
    <insert id="insertWithdrawRealTimeForQQ" parameterType="java.util.Map">
        insert into red_pack_qq_audit_info_${month}
        (c_date,c_appid,c_pid,c_price,c_users,c_total,c_overUser,c_withdraw)

        values
        <foreach collection="list" item="li" separator=",">
            (#{li.c_date},
            #{li.c_appid},
            #{li.c_pid},
            #{li.c_price},
            #{li.c_users},
            #{li.c_total},
            #{li.c_overUser},
            #{li.c_withdraw}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        c_price=VALUES(c_price),
        c_users=VALUES(c_users),
        c_total=VALUES(c_total),
        c_overUser=VALUES(c_overUser),
        c_withdraw=VALUES(c_withdraw)
    </insert>
    <update id="updateWithdrawRealTimeForQQ" parameterType="java.util.Map">
        <foreach collection="list" item="li" separator=";">
            update red_pack_qq_audit_info_${month}
            set c_dau = #{li.c_dau},c_show = #{li.c_show},c_hold = #{li.c_hold}
            where c_date = #{tdate} and c_appid = #{li.appid} and c_pid = #{li.pid}
        </foreach>
    </update>

    <!-- 移动-红包产品提现明细查询 -->
    <select id="selectRedPackAuditDetail"  resultType="com.wbgame.pojo.RedPackConfig" >
        select
        Date(c_date) as cDate, b.app_name AS gameName,
        c_appid as cAppid,
        <if test="hide != 1">
            c_pid AS cPid, c_price AS cPrice, c_users AS cUsers, c_total AS cTotal
        </if>
        <if test="hide == 1">
            sum(a.c_users) AS cUsers,
            sum(a.c_total) AS cTotal
        </if>
        from red_pack_audit_detail a
        left join app_info b on  a.c_appid = b.id and b.channel_id = 10118
        where  1=1
        <if test="cPid != null and cPid != ''">
            and a.c_pid = #{cPid}
        </if>
        <if test="cAppid != null and cAppid != ''">
            and a.c_appid in (${cAppid})
        </if>
        <if test = "startTime != null and endTime != null">
            and Date(a.c_date) BETWEEN '${startTime}' AND '${endTime}'
        </if>
        <if test="hide == 1">
            GROUP BY a.c_appid
        </if>
        <choose>
            <when test="order != null and order != ''">
                order by ${order}
            </when>
            <otherwise>
                order by Date(c_date)
            </otherwise>
        </choose>

    </select>

    <!-- 移动-红包产品提现明细查询 -->
    <select id="selectQQRedPackAuditDetail"  resultType="com.wbgame.pojo.RedPackConfig" >
        select
        Date(c_date) as cDate, c_appid AS cAppid,
        <if test="hide != 1">
            c_pid AS cPid, c_price AS cPrice, c_users AS cUsers, c_total AS cTotal
        </if>
        <if test="hide == 1">
            sum(a.c_users) AS cUsers,
            sum(a.c_total) AS cTotal
        </if>
        from red_pack_qq_audit_detail a
        where  1=1
        <if test="cPid != null and cPid != ''">
            and a.c_pid = #{cPid}
        </if>
        <if test="cAppid != null and cAppid != ''">
            and a.c_appid = #{cAppid}
        </if>
        <if test = "startTime != null and endTime != null">
            and Date(a.c_date) BETWEEN '${startTime}' AND '${endTime}'
        </if>
        <if test="hide == 1">
            GROUP BY a.c_appid
        </if>
        order by Date(c_date)
    </select>

    <!--小游戏实时红包产品数据查询 -->
    <select id="selectWxNowRedPack" resultType="com.wbgame.pojo.RedPackConfig"  >
        SELECT  a.c_date AS cDate,b.app_name AS gameName,
        a.c_appid AS cAppid,
        <if test="hide != 1">
            c_withdraw AS cWithdraw,
            CONCAT(ROUND(CASE WHEN (a.c_users /a.c_withdraw*100 )>= 100 THEN 100 ELSE ( a.c_users /a.c_withdraw*100 ) END,0 ),'%' ) AS withdrawSuccessRate,
            a.c_pid AS cPid,a.c_price AS cPrice,
            a.c_users AS cUsers,
            a.c_total AS cTotal,
            a.c_overUser AS cOverUser,
            a.c_dau AS cDau,
            a.c_show AS cShow,
            a.c_hold AS cHold,
            CONCAT(ROUND(a.c_users /a.c_overUser,2)*100,'%') AS withdrawRate
        </if>
        <if test="hide == 1">
            sum(c_withdraw) AS cWithdraw,
            CONCAT(ROUND(CASE WHEN ( sum(a.c_users) /sum(a.c_withdraw)*100 )>= 100 THEN 100 ELSE ( sum(a.c_users) /sum(a.c_withdraw)*100 ) END,0 ),'%' ) AS withdrawSuccessRate,
            sum(a.c_users) AS cUsers,
            sum(a.c_total) AS cTotal,
            sum(a.c_overUser) AS cOverUser,
            sum(a.c_dau) AS cDau,
            sum(a.c_show) AS cShow,
            sum(a.c_hold) AS cHold,
            CONCAT(ROUND(sum(a.c_users)/sum(a.c_overUser),2)*100,'%') AS withdrawRate
        </if>
        FROM
        ${databasestr} AS a
        left join app_info b on  a.c_appid = b.id
        WHERE
        1=1
        <if test="cPid != null and cPid != ''">
            and a.c_pid = ${cPid}
        </if>
        <if test="gameName != null and gameName != ''">
            and a.c_appid = ${gameName}
        </if>
        <if test = "startTime != null and endTime != null">
            and Date(a.c_date) BETWEEN '${startTime}' AND '${endTime}'
        </if>
        <if test="hide == 1">
            GROUP BY DATE( c_date ), a.c_appid
        </if>
        order by cTotal desc
    </select>

    <select id="selectAloneProjectInfo" resultType="com.wbgame.pojo.AloneProjectInfo"  parameterType="com.wbgame.pojo.AloneProjectInfo" >
        select
        prjid, login_name AS loginName, cha_name AS chaName
        from alone_project_info
        where 1=1
        <if test="prjid != null and prjid != ''">
            and prjid = #{prjid,jdbcType=VARCHAR}
        </if>
        <if test="loginName != null and loginName != ''">
            and login_name = #{loginName,jdbcType=VARCHAR}
        </if>

    </select>

    <insert id="insertAloneProjectInfo" parameterType="com.wbgame.pojo.AloneProjectInfo" >
    insert into alone_project_info (prjid, login_name, cha_name
      )
    values (#{prjid,jdbcType=VARCHAR}, #{loginName,jdbcType=VARCHAR}, #{chaName,jdbcType=VARCHAR}
      )
  </insert>

    <update id="updateAloneProjectInfo" parameterType="com.wbgame.pojo.AloneProjectInfo" >
    update alone_project_info
    set 
      cha_name = #{chaName,jdbcType=VARCHAR}
    where prjid = #{prjid,jdbcType=VARCHAR}
  </update>

    <update id="updateDnChannelInfo" parameterType="com.wbgame.pojo.DnChannelInfo" >
    update dn_channel_info
    set cha_type = #{chaType,jdbcType=INTEGER},
      cha_media = #{chaMedia,jdbcType=VARCHAR},
      cha_sub_launch = #{chaSubLaunch,jdbcType=VARCHAR},
      cha_ratio = #{chaRatio,jdbcType=VARCHAR},
      cha_remark = #{chaRemark,jdbcType=VARCHAR},
      contract = #{contract,jdbcType=VARCHAR},
      taxes = #{taxes,jdbcType=VARCHAR},
      dn_bus_model_id = #{dn_bus_model_id},
      update_owner = #{update_owner},
      ry_cha = #{ry_cha},
      update_time = now()
    where cha_id = #{chaId,jdbcType=VARCHAR}
  </update>

    <insert id="insertDnChannelInfo" parameterType="com.wbgame.pojo.DnChannelInfo" >
    insert into dn_channel_info (cha_id, cha_type, cha_media, 
      cha_sub_launch, cha_remark,cha_ratio,contract,taxes,shop_examine,shop_up,cha_statu,dn_bus_model_id
      ,create_time,update_time,create_owner,update_owner,ry_cha
      )
    values (#{chaId,jdbcType=VARCHAR}, #{chaType,jdbcType=INTEGER}, #{chaMedia,jdbcType=VARCHAR}, 
      #{chaSubLaunch,jdbcType=VARCHAR} ,#{chaRemark,jdbcType=VARCHAR}, #{chaRatio,jdbcType=VARCHAR}
      , #{contract,jdbcType=VARCHAR}, #{taxes,jdbcType=VARCHAR},#{shop_examine},#{shop_up},#{chaStatu}
      ,#{dn_bus_model_id},now(),now(),#{create_owner},#{update_owner},#{ry_cha}
      )
  </insert>

    <select id="selectDnChannelInfo" resultType="com.wbgame.pojo.DnChannelInfo" parameterType="com.wbgame.pojo.DnChannelInfo" >
        select
        cha_id chaId, cha_type chaType, cha_media chaMedia , cha_sub_launch chaSubLaunch, cha_id_byte chaIdByte,shop_examine,shop_up,dn_bus_model_id,b.bus_model dn_bus_model_name,
        cha_name_byte chaNameByte, cha_sub_byte chaSubByte, cha_sub_name_byte chaSubNameByte, cha_remark chaRemark,cha_ratio chaRatio,contract,taxes,cha_statu chaStatu
        ,date_format(a.create_time, '%Y-%m-%d %H:%i:%S') create_time,date_format(a.update_time, '%Y-%m-%d %H:%i:%S') update_time,a.create_owner,a.update_owner,ry_cha
        from dn_channel_info a left join dn_bus_model_config b on a.dn_bus_model_id = b.id
        where 1=1
        <if test="chaId != null and chaId != ''">
            and  cha_id = #{chaId,jdbcType=VARCHAR}
        </if>
        <if test="chaType != null and chaType != ''">
            and  cha_type = #{chaType,jdbcType=VARCHAR}
        </if>
        <if test="chaMedia != null and chaMedia != ''">
            and  cha_media = #{chaMedia,jdbcType=VARCHAR}
        </if>
        <if test="chaStatu != null and chaStatu != ''">
            and  cha_statu = #{chaStatu,jdbcType=VARCHAR}
        </if>
        <if test="dn_bus_model_id != null and dn_bus_model_id != ''">
            and  dn_bus_model_id in (${dn_bus_model_id})
        </if>
    </select>

    <select id="selectDnChannelType"  resultType="com.wbgame.pojo.DnChannelType" >
    select 
    type_id typeId, type_name typeName
    from dn_channel_type
  </select>

    <!-- 卖量收入报表 -->
    <update id="updateAppExchangeVolume" parameterType="com.wbgame.pojo.AppExchangeVolume" >
    update app_exchange_volume_sell
    set tdate = #{tdate,jdbcType=VARCHAR},
      channel_name = #{channelName,jdbcType=VARCHAR},
      game_name = #{gameName,jdbcType=VARCHAR},
      appid = #{appid,jdbcType=VARCHAR},
      org_channel = #{orgChannel,jdbcType=VARCHAR},
      org_game_name = #{orgGameName,jdbcType=VARCHAR},
      org_appid = #{orgAppid,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      channel = #{channel,jdbcType=INTEGER},
      derived_revenue = #{derivedRevenue,jdbcType=DECIMAL},
      pricing_type = #{pricingType,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      platform_url = #{platformUrl,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <!-- 卖量收入报表 -->
    <insert id="insertAppExchangeVolume" parameterType="java.util.List">
        insert into app_exchange_volume_sell  ( tdate,
        channel_name,
        game_name,
        appid,
        org_channel,
        org_game_name,
        org_appid,
        price,
        channel,
        derived_revenue,
        pricing_type,
        platform,
        platform_url)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.tdate},
            #{li.channelName},
            #{li.gameName},
            #{li.appid},
            #{li.orgChannel},
            #{li.orgGameName},
            #{li.orgAppid},
            #{li.price},
            #{li.channel},
            #{li.derivedRevenue},
            #{li.pricingType},
            #{li.platform},
            #{li.platformUrl})
        </foreach>

    </insert>

    <!-- 卖量收入报表 -->
    <select id="selectAppExchangeVolume" resultType="com.wbgame.pojo.AppExchangeVolume" >
        select
        id, tdate, channel_name AS channelName, c.appname AS gameName, a.appid, org_channel AS orgChannel, org_game_name AS orgGameName , org_appid AS orgAppid, price,
        channel, derived_revenue AS derivedRevenue, pricing_type AS pricingType,  platform, platform_url AS  platformUrl
        from app_exchange_volume_sell  a left join app_exchange_name_config c on a.appid = c.appid
        where 1=1
        <if test ="startTime != null and endTime != null">
            and a.tdate BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="channelName != null and channelName != ''">
            and a.channel_name = #{channelName}
        </if>
        <if test="gameName != null and gameName != ''">
            and c.appname = #{gameName}
        </if>
        <if test="appid != null and appid != ''">
            and a.appid = #{appid}
        </if>
        <if test="orgChannel != null and orgChannel != ''">
            and a.org_channel = #{orgChannel}
        </if>
        <if test="orgGameName != null and orgGameName != ''">
            and a.org_game_name = #{orgGameName}
        </if>
        <if test="pricingType != null and pricingType != ''">
            and a.pricing_type = #{pricingType}
        </if>
    </select>

    <!-- 卖量收入报表 -->
    <select id="selectAppExchangeSell" resultType="com.wbgame.pojo.AppExchangeVolume" >
        select round(avg(price),2) price , sum(channel) AS channel, sum(derived_revenue) AS derivedRevenue
        from app_exchange_volume_sell  a left join app_exchange_name_config c on a.appid = c.appid
        where 1=1
        <if test ="startTime != null and endTime != null">
            and a.tdate BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="channelName != null and channelName != ''">
            and a.channel_name = #{channelName}
        </if>
        <if test="gameName != null and gameName != ''">
            and c.appname = #{gameName}
        </if>
        <if test="appid != null and appid != ''">
            and a.appid = #{appid}
        </if>
        <if test="orgChannel != null and orgChannel != ''">
            and a.org_channel = #{orgChannel}
        </if>
        <if test="orgGameName != null and orgGameName != ''">
            and a.org_game_name = #{orgGameName}
        </if>
        <if test="pricingType != null and pricingType != ''">
            and a.pricing_type = #{pricingType}
        </if>
    </select>

    <!-- 卖量收入报表 -->
    <select id="selectGameNameSell" resultType="String" >
     select 
    DISTINCT appname AS gameName
    from app_exchange_name_config  a  where appname !=""
  </select>

    <!-- 卖量收入报表 -->
    <select id="selectAppidSell" resultType="String" >
    select 
    DISTINCT  appid 
    from app_exchange_name_config  a  where appid !=""
  </select>

    <!-- 卖量收入报表 -->
    <select id="selectOrgChannelSell" resultType="String" >
    select 
    DISTINCT org_channel AS orgChannel
    from app_exchange_volume_sell  a where org_channel !=""
  </select>

    <!-- 卖量收入报表 -->
    <select id="selectOrgGameNameSell" resultType="String" >
    select 
    DISTINCT org_game_name AS orgGameName
    from app_exchange_volume_sell  a where org_game_name !=""
  </select>

    <select id="selectChannelNameSell" resultType="String" >
    select 
    DISTINCT channel_name AS channelName
    from app_exchange_volume_sell  a where channel_name !=""
  </select>

    <select id="selectPricingTypeSell" resultType="String" >
    select 
    DISTINCT pricing_type AS pricingType
    from app_exchange_volume_sell  a  where pricing_type !=""
  </select>

    <delete id="deleteAppExchangeVolumeSell" parameterType="String" >
  delete from app_exchange_volume_sell where id in (${ids})
  </delete>

    <insert id="insertAppChannelAppidInfo" parameterType="java.util.List" >
        insert into app_channel_appid (tdate, channel, appid, appid_key,
        add_num, act_num, income, times,
        `two_rate`, `ban_income`, `screen_income`, `traffic_income`,
        `video_income`, `ban_pv`, `screen_pv`, `traffic_pv`, `video_pv`,`open_income`, `open_pv`
        )
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.tdate}, #{li.channel}, #{li.appid},#{li.appidKey},
            #{li.addNum}, #{li.actNum}, #{li.income},#{li.times},
            #{li.twoRate}, #{li.banIncome},#{li.screenIncome}, #{li.trafficIncome},
            #{li.videoIncome},#{li.banPv}, #{li.screenPv}, #{li.trafficPv},#{li.videoPv}, #{li.openIncome},#{li.openPv}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        add_num = VALUES(add_num),
        act_num = VALUES(act_num),
        income = VALUES(income),
        times = VALUES(times),
        two_rate = VALUES(two_rate),
        ban_income = VALUES(ban_income),
        screen_income = VALUES(screen_income),
        traffic_income = VALUES(traffic_income),
        video_income = VALUES(video_income),
        open_income = VALUES(open_income),
        ban_pv = VALUES(ban_pv),
        screen_pv = VALUES(screen_pv),
        open_pv = VALUES(open_pv),
        traffic_pv = VALUES(traffic_pv),
        video_pv = VALUES(video_pv)

    </insert>

    <select id="selectAppChannelAppidInfo" resultType="com.wbgame.pojo.AppChannelAppidInfo" >
        SELECT
            tdate,channel, appidKey,appid,channelAppid,appIncome,addNum,actNum,concat( addPer, '%' ) as addPer ,income,
            dauArpu,times,twoRate,banDauArpu,screenDauArpu,openDauArpu,trafficDauArpu,videoDauArpu,brickDauArpu,avgBanpv,
            avgScreenpv,avgOpenpv,avgTrafficpv,avgVideopv,avgBrickpv,banEcpm,screenEcpm,openEcpm,trafficEcpm,videoEcpm,
            brickEcpm,banIncome,screenIncome,trafficIncome,orimsgIncome,videoIncome,openIncome,brickIncome,banPv,screenPv,
            trafficPv,videoPv,openPv,brickPv
        FROM
                (<include refid="appChannelAppidInfoSql"/>) a
    </select>

    <sql id="appChannelAppidInfoSql" >
        select
                a.tdate, a.channel,d.appid AS appidKey,d.appname AS appid,
                a.appid_key AS channelAppid,
				ifnull((IFNULL(e.costs,0)+IFNULL(e.derived_revenue,0)),0) AS appIncome,
                ifnull(add_num,0) AS addNum,
                ifnull(act_num,0) AS actNum,
                ifnull(round(add_num/act_num*100,2),0) as addPer,
                ifnull(income,0) as income,
                ifnull(round(income/act_num,3),0) as dauArpu,
                ifnull(a.times,0) times,
                ifnull(a.two_rate,'0.00%') AS twoRate,
                ifnull(round(ban_income/act_num,3),0) as banDauArpu,
                ifnull(round(screen_income/act_num,3),0) as screenDauArpu,
                ifnull(round(open_income/act_num,3),0) as openDauArpu,
                ifnull(round(traffic_income/act_num,3),0) as trafficDauArpu,
                ifnull(round(video_income/act_num,3),0) as videoDauArpu,
                ifnull(round(brick_income/act_num,3),0) as brickDauArpu,
                ifnull(round(ban_pv/act_num,2),0) as avgBanpv,
                ifnull(round(screen_pv/act_num,2),0) as avgScreenpv,
                ifnull(round(open_pv/act_num,2),0) as avgOpenpv,
                ifnull(round(traffic_pv/act_num,2),0) as avgTrafficpv,
                ifnull(round(video_pv/act_num,2),0) as avgVideopv,
                ifnull(round(brick_pv/act_num,2),0) as avgBrickpv,
                ifnull(round(ban_income/ban_pv*1000,2),0) as banEcpm,
                ifnull(round(screen_income/screen_pv*1000,2),0) as screenEcpm,
                ifnull(round(open_income/open_pv*1000,2),0) as  openEcpm,
                ifnull(round(traffic_income/traffic_pv*1000,2),0) as trafficEcpm,
                ifnull(round(video_income/video_pv*1000,2),0) as videoEcpm,
                ifnull(round(brick_income/brick_pv*1000,2),0) as brickEcpm,
                ifnull(`ban_income`,0) AS banIncome,
                ifnull(`screen_income`,0) AS screenIncome,
                ifnull(`traffic_income`,0) AS trafficIncome,
                ifnull(`orimsg_income`,0) AS orimsgIncome,
                ifnull(`video_income`,0) AS videoIncome,
                ifnull(`open_income`,0) AS openIncome,
                ifnull(`brick_income`,0) AS brickIncome,
                ifnull(`ban_pv`,0) AS banPv,
                ifnull(`screen_pv`,0) AS screenPv,
                ifnull(`traffic_pv`,0) AS trafficPv,
                ifnull(`video_pv`,0) AS videoPv,
                ifnull(`open_pv`,0) AS openPv,
                ifnull(`brick_pv`,0) AS brickPv
        from app_channel_appid a  left join app_exchange_name_config c on a.appid_key = c.appid LEFT JOIN  wx_channel_manage d on a.appid_key = d.ttappid
        LEFT JOIN (SELECT tdate,channel_name,game_name ,sum(derived_revenue) derived_revenue,sum(costs) costs  from app_exchange_volume_buy a
        GROUP BY tdate,channel_name ,game_name ) e on e.tdate = a.tdate and e.channel_name = a.channel and e.game_name = a.appid
        where 1=1
        <if test ="startTime != null and endTime != null">
            and a.tdate BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="channelName != null and channelName != ''">
            and a.channel_name = #{channelName}
        </if>
        <if test="channel != null and channel != ''">
            and a.channel = #{channel}
        </if>
        <if test="gameName != null and gameName != ''">
            and d.appname = #{gameName}
        </if>
        <if test="appid != null and appid != ''">
            and  d.appid = #{appid}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </sql>

    <select id="selectSumAppChannelAppidInfo" resultType="com.wbgame.pojo.AppChannelAppidInfo" >
        select
        sum(add_num) AS addNum, sum(act_num) AS actNum,
        sum(income) AS income
        from app_channel_appid a  left join app_exchange_name_config c on a.appid_key = c.appid LEFT JOIN  wx_channel_manage d on a.appid_key = d.ttappid
        LEFT JOIN (SELECT tdate,channel_name,game_name ,sum(derived_revenue) derived_revenue,sum(costs) costs  from app_exchange_volume_buy a
        GROUP BY tdate,channel_name ,game_name ) e on e.tdate = a.tdate and e.channel_name = a.channel and e.game_name = a.appid
        where 1=1
        <if test ="startTime != null and endTime != null">
            and a.tdate BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="channelName != null and channelName != ''">
            and a.channel_name = #{channelName}
        </if>
        <if test="channel != null and channel != ''">
            and a.channel = #{channel}
        </if>
        <if test="gameName != null and gameName != ''">
            and d.appname = #{gameName}
        </if>
        <if test="appid != null and appid != ''">
            and  d.appid = #{appid}
        </if>
    </select>

    <update id="updateAppIncome" parameterType="com.wbgame.pojo.AppChannelAppidInfo">
	UPDATE
	 `app_channel_appid` 
	SET 
	`app_income` = #{appIncome} 
	WHERE
		`tdate` = #{tdate} 
		AND `channel` = #{channel} 
		AND `appid_key` = #{channelAppid} 
	</update>

    <select id="selectChannelName" resultType="String" >
    select 
    DISTINCT channel 
    from app_channel_appid  a
   </select>

    <!-- 买量收入报表 -->
    <update id="updateAppExchangeVolumeBuy" parameterType="com.wbgame.pojo.AppExchangeVolume" >
    update app_exchange_volume_buy
    set tdate = #{tdate,jdbcType=VARCHAR},
      channel_name = #{channelName,jdbcType=VARCHAR},
      game_name = #{gameName,jdbcType=VARCHAR},
      appid = #{appid,jdbcType=VARCHAR},
      org_channel = #{orgChannel,jdbcType=VARCHAR},
      org_game_name = #{orgGameName,jdbcType=VARCHAR},
      org_appid = #{orgAppid,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      channel = #{channel,jdbcType=INTEGER},
      derived_revenue = #{derivedRevenue,jdbcType=DECIMAL},
      pricing_type = #{pricingType,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      platform_url = #{platformUrl,jdbcType=VARCHAR},
      times = #{times,jdbcType=VARCHAR},
      volumes = #{volumes,jdbcType=VARCHAR},
      costs = #{costs,jdbcType=VARCHAR},
      two_rate = #{twoRate,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <!-- 买量收入报表 -->
    <insert id="insertAppExchangeVolumeBuy" parameterType="java.util.List">
        insert into app_exchange_volume_buy ( tdate,
        channel_name,
        game_name,
        appid,
        org_channel,
        org_game_name,
        org_appid,
        price,
        channel,
        derived_revenue,
        pricing_type,
        platform,
        platform_url,
        times,
        volumes,
        costs,
        two_rate
        )
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.tdate},
            #{li.channelName},
            #{li.gameName},
            #{li.appid},
            #{li.orgChannel},
            #{li.orgGameName},
            #{li.orgAppid},
            #{li.price},
            #{li.channel},
            #{li.derivedRevenue},
            #{li.pricingType},
            #{li.platform},
            #{li.platformUrl},
            #{li.times},
            #{li.volumes},
            #{li.costs},
            #{li.twoRate}
            )
        </foreach>

    </insert>

    <!-- 买量收入报表 -->
    <select id="selectAppExchangeVolumeBuy" resultType="com.wbgame.pojo.AppExchangeVolume" >
        select
        id, volumes , costs, tdate, channel_name AS channelName, a.game_name as gameName, appid, org_channel AS orgChannel, org_game_name AS orgGameName , org_appid AS orgAppid, price,
        channel, derived_revenue AS derivedRevenue, pricing_type AS pricingType,  platform, platform_url AS  platformUrl,times,two_rate AS twoRate
        from app_exchange_volume_buy a
        where 1=1
        <if test ="startTime != null and endTime != null">
            and a.tdate BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="channelName != null and channelName != ''">
            and a.channel_name = #{channelName}
        </if>
        <if test="gameName != null and gameName != ''">
            and a.game_name = #{gameName}
        </if>
        <if test="appid != null and appid != ''">
            and a.appid = #{appid}
        </if>
        <if test="orgChannel != null and orgChannel != ''">
            and a.org_channel = #{orgChannel}
        </if>
        <if test="orgGameName != null and orgGameName != ''">
            and a.org_game_name = #{orgGameName}
        </if>
        <if test="pricingType != null and pricingType != ''">
            and a.pricing_type = #{pricingType}
        </if>

    </select>

    <delete id="deleteAppExchangeVolumeBuy"  parameterType="String" >
  delete from app_exchange_volume_buy where id in (${ids})
  </delete>



    <!-- 买量汇总 -->
    <select id="selectAppExchangeBuy" resultType="com.wbgame.pojo.AppExchangeVolume" >
        select round(avg(price),2) price , sum(channel) AS channel, sum(derived_revenue) AS derivedRevenue,
        sum(volumes) AS volumes,sum(costs) AS costs
        from app_exchange_volume_buy a
        where 1=1
        <if test ="startTime != null and endTime != null">
            and a.tdate BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="channelName != null and channelName != ''">
            and a.channel_name = #{channelName}
        </if>
        <if test="gameName != null and gameName != ''">
            and a.game_name = #{gameName}
        </if>
        <if test="appid != null and appid != ''">
            and a.appid = #{appid}
        </if>
        <if test="orgChannel != null and orgChannel != ''">
            and a.org_channel = #{orgChannel}
        </if>
        <if test="orgGameName != null and orgGameName != ''">
            and a.org_game_name = #{orgGameName}
        </if>
        <if test="pricingType != null and pricingType != ''">
            and a.pricing_type = #{pricingType}
        </if>
    </select>

    <!-- 买量收入报表 -->
    <select id="selectGameNameBuy" resultType="String" >
    select 
    DISTINCT game_name AS gameName
    from app_exchange_volume_buy a where game_name != ""
  </select>

    <select id="getAppidByChaManage" resultType="String" >
  select DISTINCT appid from wx_channel_manage where appid != 0 and appid != ""
  </select>
    <select id="getNameByChaManage" resultType="String" >
  select DISTINCT appname AS gameName from wx_channel_manage where appid != 0 and appid != ""
  </select>

    <!-- 买量收入报表 -->
    <select id="selectAppidBuy" resultType="String" >
    select 
    DISTINCT  appid 
    from app_exchange_volume_buy  a where appid !=""
  </select>

    <!-- 买量收入报表 -->
    <select id="selectOrgChannelBuy" resultType="String" >
    select 
    DISTINCT org_channel AS orgChannel
    from app_exchange_volume_buy  a where org_channel !=""
  </select>

    <select id="selectOrgGameNameBuy" resultType="String" >
    select 
    DISTINCT org_game_name AS orgGameName
    from app_exchange_volume_buy  a where org_game_name !=""
  </select>

    <select id="selectChannelNameBuy" resultType="String" >
    select 
    DISTINCT channel_name AS channelName
    from app_exchange_volume_buy  a where channel_name !=""
  </select>

    <select id="selectPricingTypeBuy" resultType="String" >
    select 
    DISTINCT pricing_type AS pricingType
    from app_exchange_volume_buy  a  where pricing_type !=""
  </select>


    <select id="selectAppExchangeVolumeVo"  resultType="com.wbgame.pojo.AppExchangeVolumeVo">
        select aa.tdate,
        <if test="hide != 1">
            aa.channel_name as channelName,c.appname as gameName,aa.appid,
        </if>
        b.add_num as addNum ,b.act_num AS actNum
        ,CONCAT(round(b.add_num/b.act_num*100,2),'%') as perNew
        ,b.income
        ,aa.totalSell
        ,round((b.income+aa.totalSell)/b.act_num,3) as dauArpu
        ,aa.totalNew
        ,CONCAT(round(aa.totalNew/b.add_num*100,2),'%') as newSellPer
        ,CONCAT(round(aa.totalNew/b.act_num*100,2),'%') as dauSellPer
        FROM
        (
        SELECT
        a.tdate,
        <if test="hide != 1">
            a.channel_name,
            a.game_name,
            a.appid,
        </if>
        sum( channel ) totalNew,
        sum( derived_revenue ) totalSell
        from
        app_exchange_volume_sell a group by a.tdate
        <if test="hide != 1">
            ,a.channel_name,a.appid
        </if>
        ) aa,
        <if test="hide == 1">
            (SELECT tdate,income ,SUM(add_num) add_num,SUM(act_num) act_num FROM app_channel_appid GROUP BY tdate) b
        </if>
        <if test="hide != 1">
            app_channel_appid b,app_exchange_name_config c
        </if>
        where aa.tdate = b.tdate
        <if test="hide != 1">
            and aa.channel_name = b.channel and aa.appid = b.appid_key and aa.appid = c.appid
        </if>
        <if test = "startTime != null and endTime != null ">
            and aa.tdate BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="channelName != null and channelName != '' and hide != 1 ">
            and aa.channel_name = #{channelName}
        </if>
        <if test="gameName != null and gameName != '' and hide != 1 ">
            and c.appname = #{gameName}
        </if>
        <if test="appid != null and appid != '' and hide != 1 ">
            and aa.appid = #{appid}
        </if>
    </select>



    <update id="updateAppChannelAppidInfo" parameterType="com.wbgame.pojo.AppChannelAppidInfo" >
    update app_channel_appid
    set add_num = #{addNum},
      act_num = #{actNum},
      income = #{income}
    where  tdate = #{tdate}
    	and channel = #{channel}
      and appid = #{appid}
  </update>

    <select id="selectAppExchangeVolumeTotal"  resultType="com.wbgame.pojo.AppExchangeVolumeTotalVo" >
        select aa.tdate,aa.channel
        ,aa.app_income appIncome
        ,volumes
        ,costs
        ,aa.totalNew
        ,(CASE WHEN aa.channel="手Q" THEN aa.totalNew-IFNULL(bb.totalNewBuy,0) ELSE bb.totalNewBuy END) AS totalNewBuy
        ,(CASE WHEN aa.channel="手Q" THEN bb.totalNewBuy ELSE IFNULL(volumes,0) END) AS salesVolume
        ,aa.totalDau
        ,CONCAT(round(aa.totalNew/aa.totalDau*100,2),'%') as perNew
        ,cc.totalNewSell
        ,CONCAT(round(IFNULL(cc.totalNewSell,0)/aa.totalDau*100,2),'%') as dauSellPer
        ,(CASE WHEN aa.channel="手Q" THEN 0 ELSE bb.totalBuy END) AS totalBuy
        ,(CASE WHEN aa.channel="手Q" THEN bb.totalBuy ELSE IFNULL(bb.costs,0) END) AS expenditure
        ,(IFNULL(bb.totalBuy,0)+IFNULL(bb.costs,0)) totalExpenditure
        ,(CASE WHEN aa.channel="手Q" THEN ROUND(bb.totalBuy/(IFNULL(cc.totalNewSell,0)+IFNULL(bb.totalNewBuy,0)),2) ELSE
        round((IFNULL(bb.totalBuy,0)+IFNULL(bb.costs,0))/(IFNULL(bb.totalNewBuy,0)+IFNULL(volumes,0)),2) END) AS unitPrice
        ,(CASE WHEN aa.channel="手Q" THEN ROUND(aa.totalIncome/aa.totalNew*(aa.totalNew-bb.totalNewBuy)*0.5,2) ELSE aa.totalIncome END) AS totalIncome
        ,cc.totalSell
        ,round((IFNULL(cc.totalSell,0))/aa.totalDau,2) as sellDauArpu
        ,(CASE WHEN aa.channel="手Q" THEN aa.totalIncome ELSE aa.totalIncome+IFNULL(cc.totalSell,0) END) AS allIncome
        ,round((aa.totalIncome+IFNULL(cc.totalSell,0))/aa.totalDau,3) as dauArpu
        ,(CASE WHEN aa.channel="手Q" THEN ROUND((aa.totalIncome*0.7/aa.totalNew-IFNULL(bb.costs,0)),2)
        ELSE (IFNULL(cc.totalSell,0)-IFNULL(bb.totalBuy,0)) END) AS  diffIncome
        ,(CASE WHEN aa.channel="手Q" THEN ROUND(aa.totalIncome/aa.totalNew*(aa.totalNew-bb.totalNewBuy)*0.5,2) + ROUND(aa.totalIncome/aa.totalNew*bb.totalNewBuy*0.7,2) -bb.totalBuy
        ELSE (aa.totalIncome+IFNULL(cc.totalSell,0)-IFNULL(bb.totalBuy,0)-IFNULL(bb.costs,0)) END) AS profit
        from
        (select a.tdate,a.channel,appid_key
        ,sum(app_income) app_income
        ,sum(a.add_num) as totalNew
        ,sum(a.act_num) as totalDau
        ,sum(a.income) as totalIncome
        from app_channel_appid a group by a.tdate,a.channel) aa LEFT JOIN app_volume_total dd on  aa.tdate = dd.tdate and aa.channel = dd.channel
        LEFT JOIN
        (select b.tdate,b.channel_name
        ,sum(b.volumes) as volumes
        ,sum(b.costs) as costs
        ,sum(b.channel) as totalNewBuy
        ,sum(b.derived_revenue) as totalBuy
        from app_exchange_volume_buy b
        where b.pricing_type != '我司互导' group by b.tdate,b.channel_name) bb on aa.tdate = bb.tdate and aa.channel = bb.channel_name
        LEFT JOIN
        (select c.tdate,c.channel_name
        ,IFNULL(sum(c.channel),0) as totalNewSell
        ,(CASE WHEN c.channel_name="手Q" THEN 0 ELSE sum(c.derived_revenue)END) AS totalSell
        from app_exchange_volume_sell c where c.pricing_type != '我司互导' group by c.tdate,c.channel_name) cc
        on aa.tdate = cc.tdate and aa.channel = cc.channel_name
        where 1=1
        <if test ="startTime != null and endTime != null ">
            and aa.tdate BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="channel != null and channel != ''  ">
            and aa.channel  in (${channel})
        </if>
    </select>

    <insert id="updateAppExchangeVolumeTemInfo" parameterType="com.wbgame.pojo.AppExchangeVolumeTemInfo" >
    INSERT INTO `app_volume_total`(tdate,channel,`sales_volume`, `expenditure`) 
    VALUES (#{tdate}, #{channel},#{salesVolume}, #{expenditure})
    ON DUPLICATE KEY UPDATE
	sales_volume = VALUES(sales_volume),
	expenditure = VALUES(expenditure)
  </insert>

    <insert id="editAppExchangeName" parameterType="java.util.Map">
        insert into `app_exchange_name_config`(appid,appname)
        VALUES
        <foreach collection="map.entrySet()" open="(" separator="),(" close=")" index="key" item="val">
            #{key}, #{val}
        </foreach>
        ON DUPLICATE KEY UPDATE
        appname = VALUES(appname)
    </insert>

    <update id="updateAppExchangePermissions" parameterType="com.wbgame.pojo.AppExchangePermissions" >
    update app_exchange_permissions
    set org_channel = #{orgChannel,jdbcType=VARCHAR}
    where user_name = #{userName,jdbcType=VARCHAR}
      and page_name = #{pageName,jdbcType=VARCHAR}
  </update>

    <delete id="deleteAppExchangePermissions" parameterType="com.wbgame.pojo.AppExchangePermissions" >
    delete from app_exchange_permissions
    where user_name = #{userName,jdbcType=VARCHAR}
      and page_name = #{pageName,jdbcType=VARCHAR}
  </delete>

    <insert id="insertAppExchangePermissions" parameterType="com.wbgame.pojo.AppExchangePermissions" >
    insert into app_exchange_permissions (user_name, page_name, org_channel
      )
    values (#{userName,jdbcType=VARCHAR}, #{pageName,jdbcType=VARCHAR}, #{orgChannel,jdbcType=VARCHAR}
      )
  </insert>

    <select id="selectAppExchangePermissions"  parameterType="com.wbgame.pojo.AppExchangePermissions" resultType="com.wbgame.pojo.AppExchangePermissions" >
        select
        user_name AS userName, page_name AS pageName, org_channel AS orgChannel
        from app_exchange_permissions
        where 1=1
        <if test="userName != null and userName != ''  ">
            and user_name = #{userName,jdbcType=VARCHAR}
        </if>
        <if test="pageName != null and pageName != ''  ">
            and page_name = #{pageName,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectAdmsgPlatform" resultType="String">
		select DISTINCT platform from dn_ad_cash_paltform
  </select>
    <select id="selectAdmsgAgent" resultType="String" >
        select agent from dn_ad_cash_paltform where 1>2 OR
        <foreach collection="array" item="item" index="index"  separator="OR">
            `platform` LIKE CONCAT('%',#{item},'%')
        </foreach>

    </select>

    <select id="selectDnAdConfigVo"  parameterType="com.wbgame.pojo.DnAdConfigVo" resultType="com.wbgame.pojo.DnAdConfigVo" >
        SELECT b.platform platForm , a.appid,type,a.`code`,app_name appName,
        a.ad_sid adSid, dn_appid dnAppid,dn_pid dnPid,channel_tag channelTag
        from extend_adinfo a LEFT JOIN dn_ad_cash_paltform b ON a.agent = b.agent
        LEFT JOIN dn_adinfo_config c ON a.appid = c.appid and  a.`code` = c.`code`
        LEFT JOIN app_info d ON d.id = c.dn_appid
        where 1 = 1
        <if test ="adSid != null and adSid != '' ">
            and a.ad_sid  like concat('%',#{adSid},'%')
        </if>
        <if test ="code != null and code != '' ">
            and a.`code`  like concat('%',#{code},'%')
        </if>
        <if test ="platForm != null and platForm != '' ">
            and b.platform  like concat('%',#{platForm},'%')
        </if>
        <if test ="appid != null and appid != '' ">
            and  a.appid  like concat('%',#{appid},'%')
        </if>
        <if test="type != null and type != ''  ">
            and a.type = #{type}
        </if>
        <if test="dnAppid != null and dnAppid != ''  ">
            and dn_appid in (${dnAppid})
        </if>
    </select>

    <insert id="updateDnAdConfigVo" parameterType="com.wbgame.pojo.DnAdConfigVo">

        insert into dn_adinfo_config (appid, code,
        <if test="dnAppid != null">
            dn_appid
        </if>
        <if test="dnPid != null">
            dn_pid
        </if>
        <if test="channelTag != null">
            channel_tag
        </if>
        )
        values
        (#{appid,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR},
        <if test="dnAppid != null">
            #{dnAppid,jdbcType=VARCHAR}
        </if>
        <if test="dnPid != null">
            #{dnPid,jdbcType=VARCHAR}
        </if>
        <if test="channelTag != null">
            #{channelTag,jdbcType=VARCHAR}
        </if>
        )

        ON DUPLICATE KEY UPDATE
        <if test="dnAppid != null">
            dn_appid = VALUES(dn_appid)
        </if>
        <if test="dnPid != null">
            dn_pid = VALUES(dn_pid)
        </if>
        <if test="channelTag != null">
            channel_tag = VALUES(channel_tag)
        </if>
    </insert>

    <insert id="insertDnAdConfigVo" parameterType="com.wbgame.pojo.DnAdConfigVo">
        insert into dn_adinfo_config (appid, code,
        dn_appid, dn_pid, channel_tag
        )

        values
        <foreach collection="list" item="li" separator=",">
            (#{li.appid,jdbcType=VARCHAR}, #{li.code,jdbcType=VARCHAR},
            #{li.dnAppid,jdbcType=VARCHAR}, #{li.dnPid,jdbcType=VARCHAR}, #{li.channelTag,jdbcType=VARCHAR}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        dn_appid = VALUES(dn_appid),
        dn_pid = VALUES(dn_pid),
        channel_tag = VALUES(channel_tag)
    </insert>

    <insert id="updateAdinfoKeys" parameterType="com.wbgame.pojo.DnAdConfigVo">
        insert into dn_adinfo_config (appid, code, ad_sid)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.appid,jdbcType=VARCHAR}, #{li.code,jdbcType=VARCHAR},#{li.adSid,jdbcType=VARCHAR}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        ad_sid = VALUES(ad_sid)
    </insert>

    <select id="selectChaId" resultType="String">
    select DISTINCT cha_id chaId from dn_channel_info
   </select>

    <select id="selectPlatForm" resultType="String">
     select DISTINCT platform platForm
 		from dn_ad_cash_paltform
   </select>

    <!-- 定制化页面过滤接口_查询 selectFind ID_200707184944 -->
    <select id="selectFind" parameterType="java.util.Map" resultType="com.wbgame.pojo.WaibaoUserVo">
        SELECT xx.*,yy.typelinks FROM (
        SELECT * FROM `market_sys_find` WHERE 1=1
        <if test="page_url != null and page_url != ''">
            AND page_url = #{page_url}
        </if>
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
        <if test="find_val != null and find_val != ''">
            AND find_val = #{find_val}
        </if>
        ) xx

        LEFT JOIN
        (SELECT
        GROUP_CONCAT(
        aa.`key` SEPARATOR ','
        ) `key`,
        GROUP_CONCAT(
        CONCAT_WS('=', aa.parnet_name, aa.children_name) SEPARATOR ','
        ) typelinks

        FROM
        (SELECT * FROM market_media_channel_find WHERE `key` IN (
        SELECT substring_index(substring_index(a.`media_channel_list_find_table`,',',b.help_topic_id+1),',',-1) `media_channel_list_find_table`
        FROM market_sys_find a JOIN
        help_topic b
        <!--help_topic b-->
        ON b.help_topic_id &lt; (length(a.`media_channel_list_find_table`) - length(REPLACE(a.`media_channel_list_find_table`,',',''))+1)
        WHERE 1=1
        <if test="page_url != null and page_url != ''">
            AND a.page_url = #{page_url}
        </if>
        <if test="type != null and type != ''">
            AND a.type = #{type}
        </if>
        <if test="find_val != null and find_val != ''">
            AND a.find_val = #{find_val}
        </if>
        ORDER BY a.`key`
        )
        ) aa
        ) yy
        ON xx.`media_channel_list_find_table` = yy.`key`
    </select>

    <select id="selectFindV2" parameterType="java.util.Map" resultType="com.wbgame.pojo.WaibaoUserNewVo">
        SELECT xx.key,xx.up_data_time,xx.page_url,xx.type,xx.find_val,xx.app_list_find,xx.channel_type_list_find,
        xx.media_channel_list_find_table,xx.agent_list_find,xx.put_user_list_find,xx.title_list_find,xx.cha_type_find,
        IFNULL(if(length(trim(xx.ad_art_group_id))>0,xx.ad_art_group_id,null),'all') as ad_art_group_id,xx.account_group,
        vv.artGroupConfig as artist_list_find,yy.typelinks,xx.tf_sub_channel_list FROM (
        SELECT * FROM `market_sys_find` WHERE 1=1
        <if test="page_url != null and page_url != ''">
            AND page_url = #{page_url}
        </if>
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
        <if test="find_val != null and find_val != ''">
            AND find_val = #{find_val}
        </if>
        ) xx LEFT JOIN adv_art_group_config vv on xx.ad_art_group_id =vv.artGroupId

        LEFT JOIN
        (SELECT
        GROUP_CONCAT(
        aa.`key` SEPARATOR ','
        ) `key`,
        GROUP_CONCAT(
        CONCAT_WS('=', aa.parnet_name, aa.children_name) SEPARATOR ','
        ) typelinks

        FROM
        (SELECT * FROM market_media_channel_find WHERE `key` IN (
        SELECT substring_index(substring_index(a.`media_channel_list_find_table`,',',b.help_topic_id+1),',',-1) `media_channel_list_find_table`
        FROM market_sys_find a JOIN
        help_topic b
        <!--help_topic b-->
        ON b.help_topic_id &lt; (length(a.`media_channel_list_find_table`) - length(REPLACE(a.`media_channel_list_find_table`,',',''))+1)
        WHERE 1=1
        <if test="page_url != null and page_url != ''">
            AND a.page_url = #{page_url}
        </if>
        <if test="type != null and type != ''">
            AND a.type = #{type}
        </if>
        <if test="find_val != null and find_val != ''">
            AND a.find_val = #{find_val}
        </if>
        ORDER BY a.`key`
        )
        ) aa
        ) yy
        ON xx.`media_channel_list_find_table` = yy.`key`
    </select>

    <insert id="updateBatchDnAdConfigVo" parameterType="com.wbgame.pojo.DnAdConfigVo">
        insert into dn_adinfo_config (appid, code,
        <if test="dnAppid != null">
            dn_appid
        </if>
        <if test="dnPid != null">
            dn_pid
        </if>
        <if test="channelTag != null">
            channel_tag
        </if>
        )
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.appid,jdbcType=VARCHAR}, #{li.code,jdbcType=VARCHAR},
            <if test="dnAppid != null">
                #{dnAppid,jdbcType=VARCHAR}
            </if>
            <if test="dnPid != null">
                #{dnPid,jdbcType=VARCHAR}
            </if>
            <if test="channelTag != null">
                #{channelTag,jdbcType=VARCHAR}
            </if>
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        <!-- 	dn_appid = VALUES(dn_appid),
            dn_pid = VALUES(dn_pid),
            channel_tag = VALUES(channel_tag) -->
        <if test="dnAppid != null">
            dn_appid = VALUES(dn_appid)
        </if>
        <if test="dnPid != null">
            dn_pid = VALUES(dn_pid)
        </if>
        <if test="channelTag != null">
            channel_tag = VALUES(channel_tag)
        </if>
    </insert>

    <select id="selectDnAdConfigVoPid"  parameterType="com.wbgame.pojo.DnAdConfigVo" resultType="com.wbgame.pojo.DnAdConfigVo" >
        select 	aaaa.ad_sid adSid,
        aaaa.dn_pid dnPid,
        aaaa.`code` ,
        aaaa.appid from (select aaa.ad_sid,aaa.adsize,aaa.agent,aaa.appid,aaa.appkey,aaa.`code`,aaa.creatdate,aaa.dn_appid,aaa.dn_pid,aaa.limitname,aaa.refreshinterval,aaa.textinfo,aaa.type,bbb.channelTag from (select ad_sid,adsize,agent,appid,appkey,`code`, concat('',creatdate) creatdate,limitname,refreshinterval,textinfo,type,bb.dn_appid,dn_pid
        from extend_adinfo aa,(select ad_sid_str,IF(LENGTH(max(prjmid))>=8,SUBSTR(prjmid,1,5),'') dn_appid,max(prjmid) dn_pid
        from extend_adconfig where statu = 1
        and ad_sid_str in
        <foreach collection="adSid" item="adSid" open="(" separator="," close=")">
            #{adSid}
        </foreach>
        GROUP BY ad_sid_str) bb where aa.ad_sid = bb.ad_sid_str) aaa LEFT JOIN dnwx_client.wbgui_formconfig bbb on aaa.dn_pid = bbb.pjId) aaaa
        LEFT JOIN dn_ad_cash_paltform bbbb ON aaaa.agent = bbbb.agent
    </select>

    <insert id="updateDnAdConfigVoPid" parameterType="com.wbgame.pojo.DnAdConfigVo">
        insert into dn_adinfo_config (appid, code,  dn_pid)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.appid,jdbcType=VARCHAR}, #{li.code,jdbcType=VARCHAR}, #{li.dnPid,jdbcType=VARCHAR})
        </foreach>
        ON DUPLICATE KEY UPDATE
        dn_pid = VALUES(dn_pid)
    </insert>


    <insert id="updateBatchPid" parameterType="com.wbgame.pojo.DnAdConfigVo">
        insert into dn_adinfo_config (appid, code, dn_pid)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.appid,jdbcType=VARCHAR}, #{li.code,jdbcType=VARCHAR}, #{li.dnPid,jdbcType=VARCHAR})
        </foreach>
        ON DUPLICATE KEY UPDATE
        dn_pid = VALUES(dn_pid)
    </insert>

    <insert id="updateBatchAppid" parameterType="com.wbgame.pojo.DnAdConfigVo">
        insert into dn_adinfo_config (appid, code,dn_appid)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.appid,jdbcType=VARCHAR}, #{li.code,jdbcType=VARCHAR},#{li.dnAppid,jdbcType=VARCHAR} )
        </foreach>
        ON DUPLICATE KEY UPDATE
        dn_appid = VALUES(dn_appid)
    </insert>

    <insert id="updateBatchCha" parameterType="com.wbgame.pojo.DnAdConfigVo">
        insert into dn_adinfo_config (appid, code,channel_tag)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.appid,jdbcType=VARCHAR}, #{li.code,jdbcType=VARCHAR},#{li.channelTag,jdbcType=VARCHAR} )
        </foreach>
        ON DUPLICATE KEY UPDATE
        channel_tag = VALUES(channel_tag)

    </insert>

    <select id="selectDateGameAd" resultType="com.wbgame.pojo.GameAdInfo" parameterType="String">
 select	a.tdate cDate, a.channel,d.appid cGame,add_num AS addNum, act_num AS dau,(IFNULL(e.costs,0)+IFNULL(e.derived_revenue,0)) AS amount,
  (CASE WHEN a.channel="手Q" THEN 	round(income *0.3,2) ELSE income END) AS income
    from app_channel_appid a  left join app_exchange_name_config c on a.appid_key = c.appid LEFT JOIN wx_channel_manage d on a.appid_key = d.ttappid 
    left join (SELECT tdate,channel_name,game_name ,sum(derived_revenue) derived_revenue,sum(costs) costs  from app_exchange_volume_buy a
 	GROUP BY tdate,channel_name ,game_name ) e on e.tdate = a.tdate and e.channel_name = a.channel and e.game_name = a.appid   
    where  a.tdate =  #{startTime} and d.appid IS NOT NULL  and a.tdate IS NOT NULL and a.channel IS NOT NULL
    </select>


    <select id="selectDnAvgPrice" resultType="com.wbgame.pojo.DnAvgPriceVo" parameterType="String">
		select bb.tdate,bb.appid,channel_logo cname
	 		,IFNULL(ROUND(aa.adv_income/bb.actNum,4),0) avgPrice 
		from 
			(select a.*,sum(a.channel_active) actNum FROM dn_cha_dau_total a 
			where cha_type = 8 and tdate = #{startTime}
			GROUP BY appid, channel_logo) bb 
		LEFT JOIN 
			dn_channel_cost aa 
		on aa.appid = bb.appid and bb.channel_logo = aa.cha_id 
			where aa.tdate = #{startTime}
  </select>
  
  <select id="selectDnAvgPriceNew" resultType="com.wbgame.pojo.DnAvgPriceVo" parameterType="String">
		select bb.tdate,bb.appid,channel_logo cname
	 		,IFNULL(ROUND(aa.ad_revenue/bb.actNum,4),0) avgPrice 
		from 
			(select a.*,sum(a.channel_active) actNum FROM dn_cha_dau_total_two a 
			where cha_type = 8 and tdate = #{startTime}
			GROUP BY appid, channel_logo) bb 
		LEFT JOIN 
			dn_game_cha_revenue_total aa 
		on aa.appid = bb.appid and bb.channel_logo = aa.cha_id 
			where aa.tdate = #{startTime}
  	</select>
  	
    <update id="updateDnChaDauTotalPrice"  parameterType="com.wbgame.pojo.DnAvgPriceVo">
        <foreach collection="list" item="item" index="index">
            UPDATE `dn_cha_dau_total_two`
            SET avg_price = #{item.avgPrice}
            WHERE `tdate`= #{item.tdate} AND `appid`= #{item.appid} and channel_logo = #{item.cname};
        </foreach>
    </update>

    <select id="selectDnChaDauTotalPrice" resultType="com.wbgame.pojo.DnAvgPriceVo" parameterType="String">
	select bb.tdate,bb.pid,bb.appid, bb.gname appname, bb.channel_active dau ,channel_add addnum,channel_logo cname
	 ,truncate(avg_price*channel_active*(IFNULL(cc.ratio,100)/100),2) adv_income ,cha_media,type_name typeName
	from  dn_cha_dau_total_two bb LEFT JOIN dn_cps_report_ratio cc on bb.pid =cc.pid 
	where cha_type = 8 and tdate = #{startTime}
  </select>

    <select id="selectDnCpsReportRatio" resultType="com.wbgame.pojo.DnCpsReportRatio" parameterType="com.wbgame.pojo.DnCpsReportRatio" >
        select
        *
        from dn_cps_report_ratio
        where 1=1
        <if test="pid != null and pid != ''">
            and	 pid = #{pid,jdbcType=VARCHAR}
        </if>
    </select>

    <delete id="deleteDnCpsReportRatio" parameterType="com.wbgame.pojo.DnCpsReportRatio" >
    delete from dn_cps_report_ratio
    where pid = #{pid,jdbcType=VARCHAR}
  </delete>

    <insert id="insertDnCpsReportRatio" parameterType="com.wbgame.pojo.DnCpsReportRatio" >
    insert into dn_cps_report_ratio (pid, ratio)
    values (#{pid,jdbcType=VARCHAR}, #{ratio,jdbcType=VARCHAR})
  </insert>
    <update id="updateDnCpsReportRatio" parameterType="com.wbgame.pojo.DnCpsReportRatio" >
    update dn_cps_report_ratio
      set  ratio = #{ratio,jdbcType=VARCHAR}
    where pid = #{pid,jdbcType=VARCHAR}
  </update>

    <insert id="insertDnCpsReport" parameterType="com.wbgame.pojo.DnAvgPriceVo">
        INSERT INTO `dn_cps_report` (
        `tdate`,
        `appid`,
        `pid`,
        `appname`,
        `dau`,
        `addnum`,
        `cname`,
        `adv_income`,
        `type_name`
        )
        VALUES
        <foreach collection="list" item="li" separator=",">
            (#{li.tdate,jdbcType=VARCHAR}, #{li.appid,jdbcType=VARCHAR},
            #{li.pid,jdbcType=VARCHAR}, #{li.appname,jdbcType=VARCHAR},
            #{li.dau,jdbcType=VARCHAR}, #{li.addnum,jdbcType=VARCHAR},
            #{li.cname,jdbcType=VARCHAR}, #{li.adv_income,jdbcType=VARCHAR},
            #{li.typeName,jdbcType=VARCHAR}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        appid = VALUES(appid),
        appname = VALUES(appname),
        dau = VALUES(dau),
        addnum = VALUES(addnum),
        cname = VALUES(cname),
        adv_income = VALUES(adv_income),
        type_name = VALUES(type_name)
    </insert>

    <insert id="editDnChaDauTotal" parameterType="com.wbgame.pojo.DnAvgPriceVo">
        INSERT INTO `dn_cps_report` (
        `tdate`,
        `appid`,
        `pid`,
        `appname`,
        `dau`,
        `addnum`,
        `cname`,
        `adv_income`,
        `type_name`,
        `status`

        )
        VALUES
        <foreach collection="list" item="li" separator=",">
            (#{li.tdate,jdbcType=VARCHAR}, #{li.appid,jdbcType=VARCHAR},
            #{li.pid,jdbcType=VARCHAR}, #{li.appname,jdbcType=VARCHAR},
            #{li.dau,jdbcType=VARCHAR}, #{li.addnum,jdbcType=VARCHAR},
            #{li.cname,jdbcType=VARCHAR}, #{li.adv_income,jdbcType=VARCHAR},
            #{li.typeName,jdbcType=VARCHAR}, "1"
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        adv_income = VALUES(adv_income)
    </insert>


	<insert id="insertDnwxRuleAllConfig" parameterType="com.wbgame.pojo.mobile.DnwxAllparamConfig" >
	    insert into dnwx_rule_all_config (
	    	mid,mname,area,standard_type,tjTestModel,agreementFlag,agreementUrl,policyUrl,reportFlag,
	    	`update`,hw_login,sim_filter,redeem,clogin,sl,slMsg,app_audit,healthFlag,standard,
	    	audit,lottery,nofee,assets,city,advert,antiAddiction,mmnotify,note,`lock`,vlogo,atLaunch,atBack,atClick,
	    	safeCustomer,nosafeCustomer,auth_type,cuser,euser,createtime
	    )
	    values (
	    	#{mid},#{mname},#{area},#{standard_type},#{tjTestModel},#{agreementFlag},#{agreementUrl},#{policyUrl},#{reportFlag},
	    	#{update},#{hw_login},#{sim_filter},#{redeem},#{clogin},#{sl},#{slMsg},#{app_audit},#{healthFlag},#{standard},
	    	#{audit},#{lottery},#{nofee},#{assets},#{city},#{advert},#{antiAddiction},#{mmnotify},#{note},#{lock},#{vlogo},#{atLaunch},#{atBack},#{atClick},
	    	#{safeCustomer},#{nosafeCustomer},#{auth_type},#{euser},#{euser},now()
	    )
  </insert>

    <update id="updateDnwxRuleAllConfig" parameterType="com.wbgame.pojo.mobile.DnwxAllparamConfig" >
	    update dnwx_rule_all_config 
	    set 
			mname=#{mname},
			area=#{area},
			tjTestModel=#{tjTestModel},
			agreementFlag=#{agreementFlag},
			agreementUrl=#{agreementUrl},
			policyUrl=#{policyUrl},
			reportFlag=#{reportFlag},
			`update`=#{update},
			hw_login=#{hw_login},
			sim_filter=#{sim_filter},
			redeem=#{redeem},
			clogin=#{clogin},
			sl=#{sl},
			slMsg=#{slMsg},
			app_audit=#{app_audit},
			healthFlag=#{healthFlag},
			standard=#{standard},
			audit=#{audit},
			lottery=#{lottery},
			nofee=#{nofee},
			assets=#{assets},
			city=#{city},
			advert=#{advert},
			antiAddiction=#{antiAddiction},
			mmnotify=#{mmnotify},
			note=#{note},
			`lock`=#{lock},
			vlogo=#{vlogo},
			atLaunch=#{atLaunch},
			atBack=#{atBack},
			atClick=#{atClick},
			safeCustomer=#{safeCustomer},
			nosafeCustomer=#{nosafeCustomer},
			auth_type=#{auth_type},
			euser=#{euser} 
	    where mid = #{mid} and standard_type = #{standard_type}
  </update>

  <delete id="deleteDnwxRuleAllConfig" parameterType="com.wbgame.pojo.mobile.DnwxAllparamConfig" >
    	delete from dnwx_rule_all_config where mid = #{mid} 
  </delete>
  

  <insert id="insertDnwxAllparamConfig" parameterType="com.wbgame.pojo.mobile.DnwxAllparamConfig" >
    insert into dnwx_mmparam_all_config (
    	appid,cha_id,pid,area,tjTestModel,agreementFlag,agreementUrl,policyUrl,reportFlag,
    	`update`,hw_login,sim_filter,redeem,clogin,sl,slMsg,app_audit,healthFlag,standard,standard_type,
    	audit,lottery,nofee,assets,city,advert,antiAddiction,mmnotify,note,`lock`,vlogo,atLaunch,atBack,atClick,
    	safeCustomer,nosafeCustomer,auth_type, cuser,euser 
    )
    values (
    	#{appid},#{cha_id},#{pid},#{area},#{tjTestModel},#{agreementFlag},#{agreementUrl},#{policyUrl},#{reportFlag},
    	#{update},#{hw_login},#{sim_filter},#{redeem},#{clogin},#{sl},#{slMsg},#{app_audit},#{healthFlag},#{standard},#{standard_type},
    	#{audit},#{lottery},#{nofee},#{assets},#{city},#{advert},#{antiAddiction},#{mmnotify},#{note},#{lock},#{vlogo},#{atLaunch},#{atBack},#{atClick},
    	#{safeCustomer},#{nosafeCustomer},#{auth_type}, #{cuser},#{euser} 
    )
  </insert>

    <update id="updateDnwxAllparamConfig" parameterType="com.wbgame.pojo.mobile.DnwxAllparamConfig" >
    update dnwx_mmparam_all_config 
    set 
		appid=#{appid},
		cha_id=#{cha_id},
		pid=#{pid},
		area=#{area},
		tjTestModel=#{tjTestModel},
		agreementFlag=#{agreementFlag},
		agreementUrl=#{agreementUrl},
		policyUrl=#{policyUrl},
		reportFlag=#{reportFlag},
		`update`=#{update},
		hw_login=#{hw_login},
		sim_filter=#{sim_filter},
		redeem=#{redeem},
		clogin=#{clogin},
		sl=#{sl},
		slMsg=#{slMsg},
		app_audit=#{app_audit},
		healthFlag=#{healthFlag},
		standard=#{standard},
		standard_type=#{standard_type},
		audit=#{audit},
		lottery=#{lottery},
		nofee=#{nofee},
		assets=#{assets},
		city=#{city},
		advert=#{advert},
		antiAddiction=#{antiAddiction},
		mmnotify=#{mmnotify},
		note=#{note},
		`lock`=#{lock},
		vlogo=#{vlogo},
		atLaunch=#{atLaunch},
		atBack=#{atBack},
		atClick=#{atClick},
		safeCustomer=#{safeCustomer},
		nosafeCustomer=#{nosafeCustomer},
		auth_type=#{auth_type},
		euser=#{euser},
        endtime = now()
    where id = #{id} 
  </update>

    <delete id="deleteDnwxAllparamConfig" parameterType="com.wbgame.pojo.mobile.DnwxAllparamConfig" >
    	delete from dnwx_mmparam_all_config where id = #{id} 
  </delete>


    <update id="updateDnwxMmparamConfig" parameterType="com.wbgame.pojo.DnwxMmparamConfig" >
    update dnwx_mmparam_config
    set tjTestModel = #{tjtestmodel,jdbcType=VARCHAR},
      agreementFlag = #{agreementflag,jdbcType=VARCHAR},
      agreementUrl = #{agreementurl,jdbcType=VARCHAR},
      policyUrl = #{policyurl,jdbcType=VARCHAR},
      `update` = #{update},
      hw_login = #{hw_login},
      sim_filter = #{sim_filter},
      reportFlag = #{reportFlag},
      clogin = #{clogin},
      app_audit = #{app_audit},
      modifyUser = #{modifyUser},
      modifyTime = now()
    where pid = #{pid,jdbcType=INTEGER}
  </update>

    <insert id="insertDnwxMmparamConfig" parameterType="com.wbgame.pojo.DnwxMmparamConfig" >
    insert into dnwx_mmparam_config (pid, tjTestModel, agreementFlag, 
      agreementUrl, policyUrl, `update`, hw_login, sim_filter, reportFlag, clogin,
      app_audit, createUser, createTime)
    values (#{pid,jdbcType=INTEGER}, #{tjtestmodel,jdbcType=VARCHAR}, #{agreementflag,jdbcType=VARCHAR}, 
      #{agreementurl}, #{policyurl}, #{update}, #{hw_login}, #{sim_filter}, #{reportFlag}, #{clogin},
      #{app_audit}, #{createUser}, now())
  </insert>

    <delete id="deleteDnwxMmparamConfig" parameterType="com.wbgame.pojo.DnwxMmparamConfig" >
    delete from dnwx_mmparam_config
    where pid = #{pid,jdbcType=INTEGER}
  </delete>

    <select id="selectDnwxMmparamConfig" resultType="com.wbgame.pojo.DnwxMmparamConfig" parameterType="com.wbgame.pojo.DnwxMmparamConfig" >
        select * from dnwx_mmparam_config
        where 1=1
        <if test="pid != null and pid != ''">
            and	 pid = #{pid,jdbcType=INTEGER}
        </if>
    </select>

    <update id="updateDnwxMmparamConfigTwo" parameterType="com.wbgame.pojo.DnwxMmparamConfig" >
    update dnwx_mmparam_config_two
    set 
    	appid = #{appid},
    	cha_id = #{cha_id},
    	pid = #{pid},
    	area = #{area,jdbcType=VARCHAR},
    	tjTestModel = #{tjtestmodel,jdbcType=VARCHAR},
      	agreementFlag = #{agreementflag,jdbcType=VARCHAR},
      	agreementUrl = #{agreementurl,jdbcType=VARCHAR},
      	policyUrl = #{policyurl,jdbcType=VARCHAR},
      	`update` = #{update},
      	hw_login = #{hw_login},
      	sim_filter = #{sim_filter},
     	reportFlag = #{reportFlag},
     	clogin = #{clogin},
     	sl = #{sl},
     	slMsg = #{slMsg},
     	healthFlag = #{healthFlag}
    where id = #{id} 
  </update>

    <insert id="insertDnwxMmparamConfigTwo" parameterType="com.wbgame.pojo.DnwxMmparamConfig" >
    insert into dnwx_mmparam_config_two (appid, cha_id, pid, area, tjTestModel, agreementFlag, 
      agreementUrl, policyUrl, `update`, hw_login, sim_filter, reportFlag, clogin, sl, slMsg, healthFlag)
    values (#{appid}, #{cha_id}, #{pid}, #{area}, #{tjtestmodel}, #{agreementflag}, 
      #{agreementurl}, #{policyurl}, #{update}, #{hw_login}, #{sim_filter}, #{reportFlag}, #{clogin}, #{sl}, #{slMsg}, #{healthFlag})
  </insert>

    <delete id="deleteDnwxMmparamConfigTwo" parameterType="com.wbgame.pojo.DnwxMmparamConfig" >
    delete from dnwx_mmparam_config_two
    where id in (${id})
  </delete>

    <select id="selectDnwxMmparamConfigTwo" resultType="com.wbgame.pojo.DnwxMmparamConfig" parameterType="com.wbgame.pojo.DnwxMmparamConfig" >
        select *
        from dnwx_mmparam_config_two
        where 1=1
        <if test="appid != null and appid != ''">
            and appid = #{appid}
        </if>
        <if test="cha_id != null and cha_id != ''">
            and cha_id = #{cha_id}
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
    </select>

    <select id="selectWbguiFormconfig" resultType="com.wbgame.pojo.WbguiFormconfig" parameterType="com.wbgame.pojo.WbguiFormconfig" >
    select a.appointVer,a.company,a.crNum, a.ad,a.channel,a.channelTag,a.dataEye,a.date,a.email,a.endDate,a.flag,a.gameName,a.iconSize,a.initiator,a.instructions,a.`language`,a.moduleData,a.originator
	,a.packageName,a.parameter,a.pjId,a.planners,a.platform,a.priority,a.singleid,a.state,a.statistics,a.typeName,a.umengId,a.versionCode,a.versionName,a.icon,b.icon icons,c.appkey,c.appid
	,(CASE WHEN c.apptype = 'game' THEN 0 ELSE 1 END) appType,d.fileName,d.filePath,d.fileContent
	FROM
      dnwx_client.wbgui_formconfig a 
      
    LEFT JOIN dnwx_client.wbgui_iconconfig b on a.icon = b.singleid
	LEFT JOIN dnwx_client.wbgui_gametype c on a.typeName = c.gameId 
	LEFT JOIN (	SELECT T1.singleId,T2.fileName,T2.filePath,T1.file fileContent 
				FROM dnwx_client.wbgui_from_file T1
				LEFT JOIN dnwx_client.wbgui_module_file T2 
				ON T1.modulesingleId = T2.singleId and T2.isEnable = "1" 
				WHERE IFNULL(T1.file,'') != '') d on a.singleid=d.singleId
	WHERE
	a.singleid=#{singleid} or a.pjId=#{singleid} limit 1
  </select>

    <update id="updateAssetMaintenance" parameterType="com.wbgame.pojo.AssetMaintenance" >
    update asset_maintenance
    set parts_name = #{partsName,jdbcType=VARCHAR},
      parts_type = #{partsType,jdbcType=VARCHAR},
      price = #{price,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR},
      department = #{department,jdbcType=VARCHAR},
      date = #{date,jdbcType=VARCHAR},
      warranty = #{warranty,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      purchaser = #{purchaser,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR}
    where sn = #{sn,jdbcType=VARCHAR}
  </update>

    <select id="selectAssetMaintenance" resultType="com.wbgame.pojo.AssetMaintenance" >
        select
        sn, parts_name partsName, parts_type partsType, price, source, department, date, warranty, order_id orderId,
        purchaser,status
        from asset_maintenance
        where 1=1
        <if test="sn != null and sn != ''">
            and sn = #{sn,jdbcType=VARCHAR}
        </if>
    </select>
    <delete id="deleteAssetMaintenance" parameterType="com.wbgame.pojo.AssetMaintenance" >
    delete from asset_maintenance
    where sn = #{sn,jdbcType=VARCHAR}
  </delete>

    <insert id="insertAssetMaintenance" parameterType="com.wbgame.pojo.AssetMaintenance" >
    insert into asset_maintenance (sn, parts_name, parts_type, 
      price, source, department, 
      date, warranty, order_id, 
      purchaser, status)
    values (#{sn,jdbcType=VARCHAR}, #{partsName,jdbcType=VARCHAR}, #{partsType,jdbcType=VARCHAR}, 
      #{price,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, #{department,jdbcType=VARCHAR}, 
      #{date,jdbcType=VARCHAR}, #{warranty,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, 
      #{purchaser,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR})
  </insert>

    <select id="selectWechatAddChannel" resultType="com.wbgame.pojo.WechatAddChannel"  >
    select 
    *
    from wechat_add_channel 
    where  tdate BETWEEN #{startTime} AND #{endTime}
  </select>

    <select id="selectFinanceCpsCost" resultType="com.wbgame.pojo.FinanceCpsCost" >
        SELECT
        year(tdate) year,
        month(tdate) month,
        appid,
        cname cps,
        cc.cha_media company,
        cc.contract contract,
        truncate(sum(adv_income),2) income,
        (100-IFNULL(cc.cha_ratio,'0')) ratio,
        IFNULL(cc.taxes,'0') taxes,
        truncate((sum(adv_income)*(100-IFNULL(cc.cha_ratio,'0'))/100*(100-IFNULL(cc.taxes,'0'))/100),2) cpsratio
        <!-- cps分成公式修改为:收入*（1-我方分成比例）*（1-税率扣除率）=cps分成收入 -->
        FROM
        `dn_cps_report` bb
        LEFT JOIN dn_channel_info cc ON bb.cname = cc.cha_id
        where year(tdate) = #{year,jdbcType=VARCHAR}
        and month(tdate) = #{month,jdbcType=VARCHAR}
        GROUP BY
        month(tdate),
        year(tdate),
        appid,
        cname
    </select>

    <select id="selectCpsStatus" resultType="String" >
	SELECT
		tdate
	FROM
		`dn_cps_report` bb
	where year(tdate) = #{year,jdbcType=VARCHAR}
     and month(tdate) = #{month,jdbcType=VARCHAR}
     and `status` = 0
	 LIMIT 1 ;
  </select>

    <insert id="insertFinanceCpsCost" parameterType="com.wbgame.pojo.FinanceCpsCost" >
        insert into finance_cps_cost (year, month, appid,
        cps, company, contract,
        income, ratio, taxes,
        cpsratio)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.year,jdbcType=VARCHAR}, #{li.month,jdbcType=VARCHAR}, #{li.appid,jdbcType=VARCHAR},
            #{li.cps,jdbcType=VARCHAR}, #{li.company,jdbcType=VARCHAR}, #{li.contract,jdbcType=VARCHAR},
            #{li.income,jdbcType=VARCHAR}, #{li.ratio,jdbcType=VARCHAR}, #{li.taxes,jdbcType=VARCHAR},
            #{li.cpsratio,jdbcType=VARCHAR})
        </foreach>
        ON DUPLICATE KEY UPDATE
        company = VALUES(company),
        contract = VALUES(contract),
        income = VALUES(income),
        ratio = VALUES(ratio),
        taxes = VALUES(taxes),
        cpsratio = VALUES(cpsratio)
    </insert>

    <update id="updateAppChannelForIncomeSum" >
		update app_channel_appid set 
			income=IFNULL(ban_income,0)+IFNULL(screen_income,0)+IFNULL(video_income,0)+IFNULL(open_income,0)+IFNULL(brick_income,0)+IFNULL(traffic_income,0)
		where tdate=#{tdate} and channel="手Q"
	</update>

    <select id="selectDnwxInterfaceConfig" resultType="com.wbgame.pojo.DnwxInterfaceConfig" parameterType="com.wbgame.pojo.DnwxInterfaceConfig" >
        select
        i_key iKey , i_url iUrl, i_ver iVer
        from dnwx_interface_config
        where 1=1
        <if test="iKey != null and iKey != ''">
            and	 i_key = #{iKey,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectDnwxInterfaceConfigiKey" resultType="String"  >
    select 
    i_key iKey 
    from dnwx_interface_config
  </select>
    <delete id="deleteDnwxInterfaceConfig" parameterType="com.wbgame.pojo.DnwxInterfaceConfig" >
    delete from dnwx_interface_config
    where i_key = #{iKey,jdbcType=VARCHAR}
  </delete>
    <insert id="insertDnwxInterfaceConfig" parameterType="com.wbgame.pojo.DnwxInterfaceConfig" >
    insert into dnwx_interface_config (i_key, i_url, i_ver
      )
    values (#{iKey,jdbcType=VARCHAR}, #{iUrl,jdbcType=VARCHAR}, #{iVer,jdbcType=VARCHAR}
      )
  </insert>
    <update id="updateDnwxInterfaceConfig" parameterType="com.wbgame.pojo.DnwxInterfaceConfig" >
    update dnwx_interface_config
    set i_url = #{iUrl,jdbcType=VARCHAR},
      i_ver = #{iVer,jdbcType=VARCHAR}
    where i_key = #{iKey,jdbcType=VARCHAR}
  </update>


    <select id="selectAppCategory" resultType="com.wbgame.pojo.AppCategoryVo">
        select * from app_category
        where 1=1
        <if test="name != null and name != ''">
            and	 `name` like  '%${name}%'
        </if>
    </select>
    <select id="selectAppCategoryByName" resultType="com.wbgame.pojo.AppCategoryVo">
        select * from app_category
        where 1=1
        <if test="name != null and name != ''">
            and	 `name` =  #{name}
        </if>
    </select>
    <delete id="deleteAppCategory" parameterType="com.wbgame.pojo.AppCategoryVo" >
    delete from app_category
    where id = #{id,jdbcType=VARCHAR}
  </delete>
    <insert id="insertAppCategory" parameterType="com.wbgame.pojo.AppCategoryVo" >
    insert into app_category (`name`, remark)
    values  (#{name,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR})
  </insert>
    <update id="updateAppCategory" parameterType="com.wbgame.pojo.AppCategoryVo" >
    update app_category
    set `name` = #{name,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

    <delete id="deleteDnChannelInfo">
        delete from dn_channel_info where cha_id = #{cha_id} limit 1
    </delete>
    <update id="updateChaStatu">
        update dn_channel_info set
        <if test="chaStatu != null">
            cha_statu = #{chaStatu},
        </if>
        <if test="shop_examine != null">
            shop_examine = #{shop_examine},
        </if>
        <if test="shop_up != null">
            shop_up = #{shop_up},
        </if>
        update_time = now(),
        update_owner = #{update_owner}
        where cha_id = #{chaId}
    </update>

    <select id="selectSuperBaseInfo" resultType="com.wbgame.pojo.AppBaseParam">
        SELECT a.*,b.channelTag cha ,SUBSTR( a.prjid, 1, 5 ) appid,c.`name`  FROM `super_base_info` a LEFT JOIN dnwx_client.wbgui_formconfig b on a.prjid = b.pjId   LEFT JOIN np_city c ON a.cityId =c.id
        where  1=1
        <if test="prjid != null and prjid != ''">
            and  a.prjid = #{prjid}
        </if>
        ORDER BY currentTime DESC
    </select>

    <select id="selectExamineDevice" resultType="java.util.Map">
        <foreach collection="list" item="it" separator="union all">
            select DATE_FORMAT(a.createtime,'%Y-%m-%d %H:%i:%s') createtime,d.app_name,a.mmid,a.projectid,from_base64(a.wifissid) as wifiname,c.`name` as city
            ,a.buy_id,a.from_ip,a.wifissid,a.brand,b.pkgList,a.bs,a.gametimes,a.lsn,a.imei,a.macaddr,a.mobilemodel
            ,if(a.net = 0,'无网络',if(a.net = 1,'移动网络',if(a.net = 2,'wifi网络',if(a.net = 3,'以太网络','蓝牙网络')))) net,a.oaid,a.version,
            if(a.version = 1,'手机','pad') versionName,
            if(a.wx = 0,'未安装','已安装') wx,a.android_id
            from ${it.table} a,app_install_pkg b,np_city c,app_info d
            where a.wifissid != '' and b.tdate = #{it.tdate}
            <if test="apps != null and apps != ''">
                and a.productid in (${apps})
            </if>
            <if test="chas != null and chas != ''">
                and a.mmid in ${chas}
            </if>
            <if test="appid != null and appid != ''">
                and a.productid in (${appid})
            </if>
            <if test="pid != null and pid != ''">
                and a.projectid = #{pid}
            </if>
            <if test="channel != null and channel != ''">
                and a.mmid in (${channel})
            </if>
            <if test="buy_id != null and buy_id != ''">
                and a.buy_id like concat('%',#{buy_id},'%')
            </if>
            and a.android_id = b.lsn
            and a.cityid = c.id
            and a.productid = d.id
        </foreach>
        order by
        <choose>
            <when test="order != null and order != ''">
                ${order}
            </when>
            <otherwise>
                createtime desc
            </otherwise>
        </choose>

    </select>

    <select id="selectAppInfoDate" resultType="java.util.Map">
        select id,app_id,app_name,umeng_key,app_category,os_type from app_info
    </select>

    <select id="selectDnChannelInfoAndType" resultType="com.wbgame.pojo.DnChannelInfo">
         select cha_id chaId, cha_type chaType, cha_media chaMedia , cha_sub_launch chaSubLaunch,type_name typeName
         from dn_channel_info a left join dn_channel_type b on a.cha_type =  b.type_id
    </select>

    <select id="selectChannelListByChaTypeAndMedia" resultType="com.wbgame.pojo.DnChannelInfo">
        select cha_id chaId from dn_channel_info where 1=1
        <if test="cha_type != null and cha_type != ''">
            and cha_type in ${cha_type}
        </if>
        <if test="media != null and media != ''">
            and cha_media in ${media}
        </if>
        <if test="channel != null and channel != ''">
            and cha_id in ${channel}
        </if>
    </select>

    <select id="selectPayTypes" resultType="java.util.Map">
        select paytype,payname from box_gamepay_type
    </select>

    <insert id="recordSysRequestLog">
        insert into sys_request_log(create_time,tdate,sys_paltform,page_index,login_account)
        values
        (now(),now(),#{sys_paltform},#{page_index},#{login_account})
    </insert>

    <select id="querySysRequestLogList" resultType="com.wbgame.pojo.mobile.SysRequestLogDTO">
        SELECT * FROM (
        SELECT
        mv3.title AS sys_name,
        msmv.title AS page_name,
        msmv.`index` AS page_index,
        COALESCE(srl.request_count, 0) AS request_count
        <if test="group != null and group != ''">
            ,${group}
        </if>
        FROM
        main_sys_menu_v3 msmv
        LEFT JOIN
        main_sys_menu_v3 mv3
        ON msmv.sys_mark = mv3.`index`
        LEFT JOIN
        (
        SELECT sys_paltform,page_index,count(1) AS request_count
        <if test="group != null and group != ''">
            ,${group}
        </if>
        FROM sys_request_log
        WHERE tdate BETWEEN #{start_date} AND #{end_date}
        GROUP BY sys_paltform,page_index
        <if test="group != null and group != ''">
            ,${group}
        </if>
        ) srl
        ON srl.page_index = msmv.`index` AND srl.sys_paltform = msmv.sys_mark
        where
        msmv.sys_mark NOT IN ('v3', 'main') AND (msmv.slot = '' OR msmv.slot IS NULL)
        ) a
        <where>
            <if test="key != null and key != ''">
                <if test="index == 'request'">
                    request_count <![CDATA[ ${symbol} ]]> #{number}
                </if>
            </if>
            <if test="page_index != null and page_index != ''">
                AND page_index IN (${page_index})
            </if>
        </where>
        <if test="order_str != null and order_str != ''">
            ORDER BY ${order_str}
        </if>
    </select>


    <insert id="batchInsertXyxAdvData">
        insert into dn_cha_cash_total(member_id,app_id,placement_id,date,return_count,pv,click,revenue,dnappid,agent,ad_sid,cha_type
        ,cha_media,cha_sub_launch,cha_id,cha_type_name,country,open_type,placement_type) values
        <foreach collection="list" item="it" separator=",">
        (#{it.sdk_appid},#{it.sdk_appid},#{it.ad_unit_id},#{it.date},#{it.req_succ_count},#{it.exposure_count},#{it.click_count},#{it.income},#{it.appid}
        ,#{it.agent},#{it.adsid},#{it.cha_type},#{it.cha_media},#{it.cha_sub_launch},#{it.cha_id},#{it.type_name},'CN',#{it.open_type},#{it.sdk_adtype})
        </foreach>
    </insert>

    <update id="batchUpateHongBaoInfoMch">
        update wx_hongbao_info
        <set >
            <if test="mchId != null" >
                mch_id = #{mchId,jdbcType=VARCHAR},
            </if>
            <if test="key != null" >
                `key` = #{key,jdbcType=VARCHAR},
            </if>
            <if test="update_owner != null" >
                update_owner = #{update_owner,jdbcType=VARCHAR},
            </if>
        </set>
        where packname = #{packname,jdbcType=VARCHAR}
        and logo_imgurl = #{logoImgurl,jdbcType=VARCHAR}
        and pid = #{pid,jdbcType=VARCHAR}
    </update>


</mapper>