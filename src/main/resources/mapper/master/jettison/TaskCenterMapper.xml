<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.jettison.TaskCenterMapper">

    <select id="taskList"  resultType="com.wbgame.pojo.jettison.TaskCenterDto" parameterType="com.wbgame.pojo.jettison.TaskCenterDto">
        SELECT s.ruleName taskName,s.account accountId,s.media,s.secondNumber adNum,b.successCmapaign succsessCount,(s.secondNumber-b.successCmapaign) failCount,
        date_format(b.createTime, '%Y-%m-%d %H:%i:%s') createTime, s.createUser createUser, b.status status, b.id batchId 
        FROM dnwx_adt.dn_put_batch b left join dnwx_adt.dn_put_schedule s  on s.id=b.scheduleId
        WHERE  1=1
		<if test="taskName != null and taskName != ''">
			and s.ruleName LIKE "%"#{taskName}"%"
		</if>
		<if test="media != null and media != ''">
			and s.media in (${media}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and s.account in (${accountId}) 
		</if>
		<if test="createUser != null and createUser != ''">
			and s.createUser LIKE "%"#{createUser}"%"
		</if>
        <if test="beginDate != null and beginDate != ''">
			and date_format(b.createTime, '%Y-%m-%d') BETWEEN #{beginDate} AND #{endDate}
		</if>
		order by b.createTime desc
    </select>
	<select id="getDetail"  resultType="com.wbgame.pojo.jettison.vo.TaskDetailVo" >
        SELECT account accountId,groupId,groupName,campaignId,campaignName ,status,errMsg  from dnwx_adt.dn_put_batch_campaign
        WHERE  batchId=#{batchId}
    </select>
    <insert id="saveOfflineFileTask" parameterType="com.wbgame.pojo.common.param.CreateOfflineFileParam" useGeneratedKeys="true" keyProperty="id">
		insert into off_line_file_task
		(page_mark,param,page_query_url,query_url,content_type,page_content_type,create_user,create_time)
		values (#{page_mark,jdbcType=VARCHAR}, #{jsonStrParam,jdbcType=VARCHAR},#{page_query_url,jdbcType=VARCHAR},
		#{query_url,jdbcType=VARCHAR},#{content_type,jdbcType=VARCHAR},#{page_content_type,jdbcType=VARCHAR},#{create_user,jdbcType=VARCHAR},now())
	</insert>
	<update id="updateOfflineFileTaskStatus" parameterType="com.wbgame.pojo.common.param.CreateOfflineFileParam" >
		update  off_line_file_task set task_status=#{task_status},file_url=#{file_url} where id=#{id}
	</update>
	 <select id="offlineFileTaskList"  resultType="com.wbgame.pojo.common.vo.OfflineFileTaskVo" parameterType="com.wbgame.pojo.common.vo.OfflineFileTaskVo">
        SELECT * FROM off_line_file_task  WHERE  1=1
		<if test="create_user != null and create_user != ''">
			and create_user=#{create_user}
		</if>
		<if test="page_mark != null and page_mark != ''">
			and page_mark=#{page_mark}
		</if>
		<if test="task_status != null and task_status != ''">
			and task_status=#{task_status}
		</if>
		order by create_time desc
    </select>
    <delete id="deleteOfflineFileTask" parameterType="com.wbgame.pojo.common.vo.OfflineFileTaskVo">
    	delete FROM off_line_file_task  WHERE  id=#{id} and create_user=#{create_user}
    </delete>
</mapper>