<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.jettison.MaterialPathMapper">

    <select id="pathList" resultType="com.wbgame.pojo.jettison.vo.MaterialPathVo"  >
        SELECT id,groupId,name,level,parentId,clGroupId FROM dnwx_adt.dn_jettison_material_path  where 1=1
         <if test="groupId != null and groupId != ''">
			and groupId =#{groupId}
		</if>
		order by createTime desc
    </select>
	 <select id="materialList" resultType="com.wbgame.pojo.jettison.report.MaterialDTO"  parameterType="com.wbgame.pojo.jettison.report.MaterialDTO" >
        SELECT m.id id,m.appid appid,m.fileName fileName,m.filePath filePath,m.pathName pathName, m.signature signature,m.artist artist,m.creatives creatives,m.producer3d producer3d,
        m.label1 label1, m.label2 label2, m.label3 label3,m.height high,m.wide wide,m.size size,m.`type` type,m.`day` day, m.signature2 signature2 ,m.script_num scriptNum,
        m.updateUser updateUser,m.updateTime updateTime,m.createUser createUser,m.createTime createTime,a.app_name appName,m.tag_app_name tagAppName,m.product_part product_part,
        s.spend7 spend7,s.spend30 spend30,s.spend spend,s.installs7 installs7,s.installs30 installs30,s.installs installs,m.status status,m.immortal3d immortal3d
        FROM
        <choose>
			<when test="filePath != null and filePath != '' and filePath != 10000 and filePath != 0">
				(SELECT * from dnwx_adt.dn_jettison_material l,(
				SELECT
				rd.groupId
				FROM
				( SELECT * FROM dnwx_adt.dn_jettison_material_path WHERE parentId IS NOT NULL ) rd,
				( SELECT @pid := #{filePath}) pd
				WHERE
				FIND_IN_SET( parentId, @pid ) > 0
				AND @pid := concat( @pid, ',', groupId )

				UNION
				SELECT
				groupId
				FROM
				dnwx_adt.dn_jettison_material_path
				WHERE
				FIND_IN_SET( groupId, @pid ) > 0
				UNION
				SELECT #{filePath} FROM DUAL
				) t1 where l.filePath =t1.groupid  )
			</when>
			<otherwise>
				dnwx_adt.dn_jettison_material
			</otherwise>
		</choose>
        m left  join app_info  a on m.appid=a.id left  join dnwx_adt.dn_jettison_material_spend s on m.signature2=s.signature  where 1=1
		<if test="artistList != null and artistList.size > 0">
			AND (
            <foreach collection="artistList" index="index" item="item" separator="OR">
                m.artist LIKE "%"#{item}"%"
            </foreach>
            )
		</if>
		<if test="productPartList != null and productPartList.size  > 0">
			AND (
            <foreach collection="productPartList" index="index" item="item" separator="OR">
                m.product_part LIKE "%"#{item}"%"
            </foreach>
            )
		</if>
		<if test="type != null and type != ''">
			and m.type=#{type}
		</if>
		<if test="label1 != null and label1 != ''">
			and m.label1 in (${label1})
		</if>
		<if test="label2 != null and label2 != ''">
			and m.label2 in (${label2})
		</if>
		<if test="fileName != null and fileName != ''">
			and m.fileName LIKE "%"#{fileName}"%"
		</if>
		<if test="tagAppName != null and tagAppName != ''">
			and m.tag_app_name LIKE "%"#{tagAppName}"%"
		</if>
		<if test="scriptNum != null and scriptNum != ''">
			and m.script_num LIKE "%"#{scriptNum}"%"
		</if>
		<if test="immortal3d != null and immortal3d != ''">
			and m.immortal3d in (${immortal3d})
		</if>
		<if test="label3List != null and label3List.size > 0">
	        AND (
            <foreach collection="label3List" index="index" item="item" separator="OR">
                m.label3 LIKE "%"#{item}"%"
            </foreach>
            )
		</if>
		<if test="length != null and length.size > 0">
         	and  	m.height in
	        <foreach collection="length" separator="," item="it" open="(" close=")">
	                #{it}
	        </foreach>
        </if>
		<if test="wides != null and wides.size > 0">
          and 	m.wide in
	        <foreach collection="wides" separator="," item="it" open="(" close=")">
	                #{it}
	        </foreach>
        </if>
		<if test="beginDate != null and beginDate != ''">
			and m.day BETWEEN #{beginDate} AND #{endDate}
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by m.createTime desc
			</otherwise>
		</choose>
    </select>
	
	<select id="getMaterialList" resultType="com.wbgame.pojo.jettison.report.MaterialDTO"  parameterType="com.wbgame.pojo.jettison.report.MaterialDTO" >
        SELECT m.id id,m.appid appid,m.fileName fileName,m.filePath filePath,m.pathName pathName, m.signature signature,m.artist artist,m.creatives creatives,m.producer3d producer3d,
        m.label1 label1, m.label2 label2, m.label3 label3,m.height high,m.wide wide,m.size size,m.`type` type,m.`day` day, m.signature2 signature2 ,m.product_part product_part ,
        m.updateUser updateUser,m.updateTime updateTime,m.createUser createUser,m.createTime createTime,a.app_name appName,m.tag_app_name tagAppName,m.script_num scriptNum,
        s.spend7 spend7,s.spend30 spend30,s.spend spend,s.installs7 installs7,s.installs30 installs30,s.installs installs,m.status status,m.immortal3d immortal3d
        FROM dnwx_adt.dn_jettison_material m left  join yyhz_0308.app_info  a on m.appid=a.id left  join dnwx_adt.dn_jettison_material_spend s on m.signature2=s.signature  where m.filePath=#{filePath}
		<if test="artistList != null and artistList.size > 0">
			AND (
            <foreach collection="artistList" index="index" item="item" separator="OR">
                m.artist LIKE "%"#{item}"%"
            </foreach>
            )
		</if>
		<if test="productPartList != null and productPartList.size  > 0">
			AND (
            <foreach collection="productPartList" index="index" item="item" separator="OR">
                m.product_part LIKE "%"#{item}"%"
            </foreach>
            )
		</if>
		<if test="type != null and type != ''">
			and m.type=#{type}
		</if>
		<if test="label1 != null and label1 != ''">
			and m.label1 in (${label1})
		</if>
		<if test="label2 != null and label2 != ''">
			and m.label2 in (${label2})
		</if>
		<if test="fileName != null and fileName != ''">
			and m.fileName LIKE "%"#{fileName}"%"
		</if>
		<if test="scriptNum != null and scriptNum != ''">
			and m.script_num LIKE "%"#{scriptNum}"%"
		</if>
		<if test="tagAppName != null and tagAppName != ''">
			and m.tag_app_name LIKE "%"#{tagAppName}"%"
		</if>
		<if test="immortal3d != null and immortal3d != ''">
			and m.immortal3d in (${immortal3d})
		</if>
		<if test="label3List != null and label3List.size > 0">
	        AND (
            <foreach collection="label3List" index="index" item="item" separator="OR">
                m.label3 LIKE "%"#{item}"%"
            </foreach>
            )
		</if>
		<if test="length != null and length.size > 0">
         	and  	m.height in
	        <foreach collection="length" separator="," item="it" open="(" close=")">
	                #{it}
	        </foreach>
        </if>
		<if test="wides != null and wides.size > 0">
          and 	m.wide in
	        <foreach collection="wides" separator="," item="it" open="(" close=")">
	                #{it}
	        </foreach>
        </if>
		<if test="beginDate != null and beginDate != ''">
			and m.day BETWEEN #{beginDate} AND #{endDate}
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by m.createTime desc
			</otherwise>
		</choose>
    </select>
    
    <select id="getMaterialListByFolderId" resultType="com.wbgame.pojo.jettison.report.MaterialDTO"  >
        SELECT * from dnwx_adt.dn_jettison_material where 
           	filePath in
	        <foreach collection="folderList" separator="," item="it" open="(" close=")">
	                #{it.groupId}
	        </foreach>
    </select>
    
    <select id="getChildFolder" resultType="com.wbgame.pojo.jettison.vo.MaterialPathVo"  >
    	SELECT groupId,name FROM dnwx_adt.dn_jettison_material_path where parentId=#{filePath}
    </select>
    
    <insert id="addMaterialPath" parameterType="com.wbgame.pojo.jettison.vo.MaterialPathVo" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO dnwx_adt.dn_jettison_material_path
        (groupId,name,level,parentId,createTime,updateTime,updateUser)
        VALUES
        (#{groupId},#{name},#{level}, #{parentId},  #{createTime}, #{createTime}, #{lastUpdateUser})
    </insert>

 	<select id="getGroupNum" resultType="java.lang.Integer" >
        SELECT num from dnwx_adt.dn_jettison_number where level=#{level} order by id desc 
    </select>

	<select id="exitMaterialPath" resultType="java.lang.Integer"  parameterType="com.wbgame.pojo.jettison.vo.MaterialPathVo">
        SELECT count(id) from dnwx_adt.dn_jettison_material_path where   name=#{name} and parentId=#{parentId}
    </select>

	<update id="updateGroupNum" >
       UPDATE dnwx_adt.dn_jettison_number set num=num+1  where level=#{level}
   	</update>
   	
   	
	<select id="getMaterialInfo" resultType="com.wbgame.pojo.jettison.report.MaterialDTO"  >
         SELECT fileName,filePath,pathName, signature,artist,creatives,producer3d,product_part, label1, label2, label3,height,wide,size,`type`,`day` FROM dnwx_adt.dn_jettison_material  where id=#{mid} 
    </select>
    
    
   	<update id="updateMaterial" parameterType="com.wbgame.pojo.jettison.report.MaterialDTO">
       UPDATE dnwx_adt.dn_jettison_material set label1=#{label1},label2=#{label2},label3=#{label3},artist=#{artist},updateUser=#{updateUser},script_num=#{scriptNum},
       producer3d=#{producer3d},creatives=#{creatives},updateTime=#{updateTime},tag_app_name=#{tagAppName},immortal3d=#{immortal3d},product_part=#{product_part} where id=#{id}
   	</update>

	<update id="batchUpdateMaterial">
		update dnwx_adt.dn_jettison_material set
		<if test="is_artist == 1">
			artist=#{artist},producer3d=#{producer3d},creatives=#{creatives},immortal3d=#{immortal3d},product_part=#{product_part},
		</if>
		<if test="is_label == 1">
			label1=#{label1},label2=#{label2},label3=#{label3},tag_app_name=#{tagAppName},
		</if>
		updateTime=#{updateTime},updateUser=#{updateUser}
		where id in (${ids})
	</update>

    <update id="updateMaterialPath"   parameterType="com.wbgame.pojo.jettison.vo.MaterialPathVo" >
       UPDATE dnwx_adt.dn_jettison_material_path set name=#{name}  where id=#{id}
   	</update>
   	
   	 <update id="updateMaterialPathClFolder"    >
       UPDATE dnwx_adt.dn_jettison_material_path set clGroupId=#{clGroupId}  where id=#{id}
   	</update>
   	
   	<select id="getMaterialCountByGroupId" resultType="java.lang.Integer"  >
        SELECT count(id) from dnwx_adt.dn_jettison_material where   filePath=#{groupId} 
    </select>
   	
   	<select id="getChildCountByGroupId" resultType="java.lang.Integer"  >
        SELECT count(id) from dnwx_adt.dn_jettison_material_path where   parentId=#{groupId} 
    </select>
    
    <delete id="deleteFilePathByGroupId" >
	    delete from dnwx_adt.dn_jettison_material_path where groupId = #{groupId}
	</delete>
    
   <update id="batchUpdateMaterialPath" >
       UPDATE dnwx_adt.dn_jettison_material t set t.filePath=#{groupId},t.updateUser=#{username},t.updateTime=#{time},t.pathName=#{pathName} where t.id in
        <foreach collection="materialIds" separator="," item="it" open="(" close=")">
                #{it}
        </foreach>
   	</update>
    
    <update id="moveMaterialPath"    >
        UPDATE dnwx_adt.dn_jettison_material_path t1,(SELECT id FROM dnwx_adt.dn_jettison_material_path ORDER  BY id DESC LIMIT 1) t2 set t1.id=t2.id+1,
       t1.parentId=#{desGroupId},t1.level=#{desLevel}+1,t1.updateUser=#{username},t1.updateTime=#{time}  where t1.groupId=#{groupId}
   	</update>
   	
   	
   	<!-- 删除素材 -->
	<delete id="deleteMaterial" >
		DELETE FROM dnwx_adt.dn_jettison_material WHERE id in
		<foreach collection="materialIds" separator="," item="it" open="(" close=")">
                #{it}
        </foreach>
	</delete>
	
	<select id="getChildGroupId" resultType="java.lang.Integer"  >
       SELECT
				rd.groupId
			FROM
				( SELECT * FROM dnwx_adt.dn_jettison_material_path WHERE parentId IS NOT NULL ) rd,
				( SELECT @pid := #{groupId} ) pd 
			WHERE
				FIND_IN_SET( parentId, @pid ) > 0 
				AND @pid := concat( @pid, ',', groupId ) 
			
			UNION
			SELECT
			groupId
			FROM
				dnwx_adt.dn_jettison_material_path 
			WHERE
				FIND_IN_SET( groupId, @pid ) > 0
    </select>
    <update id="updateChildGroupId" >
       UPDATE dnwx_adt.dn_jettison_material_path t1,(SELECT id FROM dnwx_adt.dn_jettison_material_path ORDER  BY id DESC LIMIT 1) t2 set t1.id=t2.id+1 where t1.groupId in
        <foreach collection="groupId" separator="," item="it" open="(" close=")">
                #{it}
        </foreach>
   	</update>
    <select id="tasklList" resultType="com.wbgame.pojo.jettison.UploadMaterialTaskDTO"  parameterType="com.wbgame.pojo.jettison.UploadMaterialTaskDTO" >
        SELECT   * from dnwx_adt.dn_jettison_material_upload_task  where 1=1
		<if test="task_name != null and task_name != ''">
			and task_name like "%"#{task_name}"%"
		</if>
		<if test="create_user != null and create_user != ''">
			and create_user like "%"#{create_user}"%"
		</if>
		<if test="beginDate != null and beginDate != ''">
			and DATE_FORMAT(time,'%Y-%m-%d') BETWEEN #{beginDate} AND #{endDate}
		</if>
		order by time desc
    </select>
    <select id="taskDetaillList" resultType="com.wbgame.pojo.jettison.UploadMaterialRecordDTO" >
        SELECT   * from dnwx_adt.dn_jettison_material_upload_record  where task_id=#{taskId}
    </select>

       	<!-- 删除文件夹-->
	<delete id="deleteFolder" >
		DELETE FROM dnwx_adt.dn_jettison_material_path WHERE id=#{id}
	</delete>

	<select id="selectParentPathList" resultType="com.wbgame.pojo.jettison.vo.MaterialPathVo">
        select id,groupId,name,level,a.parentId,clGroupId,if(num > 0,0,1) is_last from
		(select * from dnwx_adt.dn_jettison_material_path where parentId = #{parentId}
		<if test="name != null and name != ''">
			and name like concat('%',#{name},'%')
		</if>
		) a left join
		(select parentId,count(1) num from dnwx_adt.dn_jettison_material_path where parentId in (
		select groupId from dnwx_adt.dn_jettison_material_path where parentId = #{parentId}
		<if test="name != null and name != ''">
			and name like concat('%',#{name},'%')
		</if>
		)
		GROUP BY parentId) b
		on a.groupId = b.parentId
		order by createTime desc
	</select>

    <select id="likeMaterialNamePathList" resultType="com.wbgame.pojo.jettison.vo.MaterialPathVo">
		select id,groupId,name,level,a.parentId,clGroupId,if(num > 0,0,1) is_last from
		(select * from dnwx_adt.dn_jettison_material_path where name like concat('%',#{name},'%')) a left join
		(select parentId,count(1) num from dnwx_adt.dn_jettison_material_path where parentId in (
		select groupId from dnwx_adt.dn_jettison_material_path where name like concat('%',#{name},'%'))
		GROUP BY parentId) b
		on a.groupId = b.parentId
		order by createTime desc
	</select>

	<select id="selectMaterialPathLists" resultType="com.wbgame.pojo.jettison.vo.MaterialPathVo">
		select id,groupId,name,level,a.parentId,clGroupId,if(num > 0,0,1) is_last from
		(select * from dnwx_adt.dn_jettison_material_path where groupId in (
		<foreach collection="list" item="item" separator=",">
			#{item}
		</foreach>
		)) a left join
		(select parentId,count(1) num from dnwx_adt.dn_jettison_material_path where parentId in (
		select groupId from dnwx_adt.dn_jettison_material_path where  groupId in (
		<foreach collection="list" item="item" separator=",">
			#{item}
		</foreach>
		))
		GROUP BY parentId) b
		on a.groupId = b.parentId
		order by createTime desc
	</select>

</mapper>