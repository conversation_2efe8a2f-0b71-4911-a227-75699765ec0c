<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.jettison.CallbackShortcutMapper">

	<select id="queryMediaService" resultType="com.wbgame.pojo.attribution.MediaSubmitService">
		SELECT * FROM dnwx_adt.common_click_callback_media_submit_service ORDER BY media
	</select>


	<select id="queryList" resultType="com.wbgame.pojo.attribution.MixClickCallbackShortcut">
		SELECT * FROM dnwx_adt.common_mix_click_callback_shortcut
		<where>
			<if test="param.shortcut != null and param.shortcut != ''">
				shortcut = #{param.shortcut}
			</if>
			<if test="param.special_name != null and param.special_name != ''">
				AND special_name LIKE CONCAT('%',#{param.special_name},'%')
			</if>
			<if test="param.appid != null and param.appid != ''">
				AND appid IN (${param.appid})
			</if>
			<if test="param.app_category != null and param.app_category != ''">
				AND app_category IN (${param.app_category})
			</if>
			<if test="param.media != null and param.media != ''">
				AND media = #{param.media}
			</if>
			<if test="param.pid != null and param.pid != ''">
				AND pid LIKE CONCAT('%',#{param.pid},'%')
			</if>
			<if test="param.pn != null and param.pn != ''">
				AND pn LIKE CONCAT('%',#{param.pn},'%')
			</if>
			<if test="param.channel != null and param.channel != ''">
				AND channel REGEXP ${param.channel}
			</if>
			<if test="param.enabled != null">
				AND enabled = #{param.enabled}
			</if>
		</where>
		<choose>
			<when test="param.order_str != null and param.order_str != ''">
				order by ${param.order_str}
			</when>
			<otherwise>
				order by create_time desc
			</otherwise>
		</choose>
	</select>
	<select id="queryShortcut" resultType="com.wbgame.pojo.attribution.MixClickCallbackShortcut">
		SELECT * FROM dnwx_adt.common_mix_click_callback_shortcut WHERE shortcut = #{shortcut}
	</select>

	<insert id="insertShortcut">
		insert into dnwx_adt.common_mix_click_callback_shortcut
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="param.shortcut != null and param.shortcut != ''">
				shortcut,
			</if>
			<if test="param.special_name != null and param.special_name != ''">
				special_name,
			</if>
			<if test="param.appid != null and param.appid != ''">
				appid,
			</if>
			<if test="param.app_category != null and param.app_category != ''">
				app_category,
			</if>
			<if test="param.media != null and param.media != ''">
				media,
			</if>
			<if test="param.pid != null and param.pid != ''">
				pid,
			</if>
			<if test="param.pn != null and param.pn != ''">
				pn,
			</if>
			<if test="param.channel != null and param.channel != ''">
				channel,
			</if>
			<if test="param.monitor_link != null and param.monitor_link != ''">
				monitor_link,
			</if>
			<if test="param.enabled != null">
				enabled,
			</if>
			<if test="param.create_user != null and param.create_user != ''">
				create_user,
			</if>
			create_time
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="param.shortcut != null and param.shortcut != ''">
				#{param.shortcut},
			</if>
			<if test="param.special_name != null and param.special_name != ''">
				#{param.special_name},
			</if>
			<if test="param.appid != null and param.appid != ''">
				#{param.appid},
			</if>
			<if test="param.app_category != null and param.app_category != ''">
				#{param.app_category},
			</if>
			<if test="param.media != null and param.media != ''">
				#{param.media},
			</if>
			<if test="param.pid != null and param.pid != ''">
				#{param.pid},
			</if>
			<if test="param.pn != null and param.pn != ''">
				#{param.pn},
			</if>
			<if test="param.channel != null and param.channel != ''">
				#{param.channel},
			</if>
			<if test="param.monitor_link != null and param.monitor_link != ''">
				#{param.monitor_link},
			</if>
			<if test="param.enabled != null">
				#{param.enabled},
			</if>
			<if test="param.create_user != null and param.create_user != ''">
				#{param.create_user},
			</if>
			CURRENT_TIMESTAMP
		</trim>
	</insert>

	<delete id="deleteShortcut">
		DELETE FROM dnwx_adt.common_mix_click_callback_shortcut WHERE shortcut IN (${shortcut})
	</delete>

	<update id="updateShortcut">
		update dnwx_adt.common_mix_click_callback_shortcut
		<set>
			<if test="param.pid != null">
				pid = #{param.pid},
			</if>
			<if test="param.pn != null">
				pn = #{param.pn},
			</if>
			<if test="param.channel != null">
				channel = #{param.channel},
			</if>
			<if test="param.monitor_link != null">
				monitor_link = #{param.monitor_link},
			</if>
			<if test="param.enabled != null">
				enabled = #{param.enabled},
			</if>
			<if test="param.update_user != null and param.update_user != ''">
				update_user = #{param.update_user},
			</if>
			update_time = current_timestamp
		</set>
		where shortcut = #{param.shortcut}
	</update>

	<select id="isShortcutDefined" resultType="java.lang.Integer">
		select count(1) from dnwx_adt.common_mix_click_callback_shortcut where shortcut = #{shortcut}
	</select>

</mapper>