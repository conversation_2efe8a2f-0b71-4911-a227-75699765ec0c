<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.jettison.DocumentMapper">

    <select id="documentList" resultType="com.wbgame.pojo.jettison.vo.DocumentVo"  parameterType="com.wbgame.pojo.jettison.vo.DocumentVo">
        SELECT id,document_type documentType,document_content documentContent,create_user createUser,last_update_user lastUpdateUser,create_time createTime,last_update_time lastUpdateTime
         FROM dnwx_adt.dn_jettison_document  where 1=1
         <if test="documentType != null and documentType != ''">
			and document_type in (${documentType} )
		</if>
		 <if test="createUser != null and createUser != ''">
			and create_user like concat('%',#{createUser},'%') 
		</if>
		 <if test="documentContent != null and documentContent != ''">
			and document_content like concat('%',#{documentContent},'%') 
		</if>
		order by create_time desc
    </select>

    <insert id="insertDocument" parameterType="com.wbgame.pojo.jettison.vo.DocumentVo">
        INSERT INTO dnwx_adt.dn_jettison_document
        (document_type,document_content,create_user,last_update_user,create_time,last_update_time)
        VALUES
        (#{documentType},#{documentContent},#{createUser}, #{lastUpdateUser},  #{createTime}, #{lastUpdateTime})
    </insert>

	<update id="editDocument" parameterType="com.wbgame.pojo.jettison.vo.DocumentVo">
       UPDATE dnwx_adt.dn_jettison_document set document_type=#{documentType},document_content=#{documentContent},last_update_user=#{lastUpdateUser},last_update_time=#{lastUpdateTime} where id=#{id}
   	</update>

   <delete id="deleteDocument" parameterType="java.util.List">
   		delete from dnwx_adt.dn_jettison_document where id in  
   		 <foreach collection="ids" index="index" item="item" separator=","  open="(" close=")">
               #{item}
         </foreach>
   </delete>
    <select id="documentTypeList" resultType="java.lang.String"  >
        SELECT type_name from dnwx_adt.dn_jettison_document_type
    </select>
   
	<insert id="insertDocumentType" >
        INSERT INTO dnwx_adt.dn_jettison_document_type
        (type_name,create_user,last_update_user,create_time,last_update_time)
        VALUES
        (#{typeName},#{createUser}, #{createUser},#{createTime},#{createTime})
    </insert>
    
    <select id="tagList" resultType="com.wbgame.pojo.jettison.vo.TagVo" parameterType="com.wbgame.pojo.jettison.vo.TagVo"  >
        SELECT id,app_name appName,tag_type tagType,tag_name tagName,create_user createUser,last_update_user lastUpdateUser,create_time createTime,last_update_time lastUpdateTime
         FROM dnwx_adt.dn_jettison_tag  where 1=1
         <if test="tagType != null and tagType != ''">
			and tag_type in (${tagType} )
		</if>
		 <if test="tagName != null and tagName != ''">
			and tag_name like concat('%',#{tagName},'%') 
		</if>
		<if test="appName != null and appName != ''">
				and app_name=#{appName}
		</if>
		order by create_time desc
    </select>
	<insert id="addTag" parameterType="com.wbgame.pojo.jettison.vo.TagVo">
        INSERT INTO dnwx_adt.dn_jettison_tag
        (tag_type,tag_name,create_user,last_update_user,create_time,last_update_time,app_name)
        VALUES
        (#{tagType},#{tagName},#{createUser}, #{lastUpdateUser},#{createTime},#{lastUpdateTime},#{appName})
    </insert>
    
    <select id="exitDocument" resultType="java.lang.Integer" >
        SELECT count(id) FROM dnwx_adt.dn_jettison_document where  document_type=#{typeName} and document_content=#{content}
    </select>
    
    <select id="exitDocumentType" resultType="java.lang.Integer" >
        SELECT count(id) FROM dnwx_adt.dn_jettison_document_type where  type_name=#{typeName}
    </select>
    
    <select id="exitTag" resultType="java.lang.Integer"  >
         SELECT id FROM dnwx_adt.dn_jettison_tag where tag_name=#{tagName} and app_name=#{appName} limit 1
    </select>
    
    <update id="editTag" parameterType="com.wbgame.pojo.jettison.vo.TagVo">
       UPDATE dnwx_adt.dn_jettison_tag set tag_name=#{tagName},last_update_user=#{lastUpdateUser},last_update_time=#{lastUpdateTime},app_name=#{appName} where id=#{id}
   	</update>

   <delete id="deleteTag" >
   		delete from dnwx_adt.dn_jettison_tag where id=#{id} 
   </delete>
</mapper>