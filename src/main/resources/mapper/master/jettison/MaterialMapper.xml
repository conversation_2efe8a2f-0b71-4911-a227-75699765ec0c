<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.jettison.MaterialMapper">

    <select id="isExistMaterial" resultType="java.lang.String">
        SELECT fileName FROM dnwx_adt.dn_jettison_material WHERE  signature = #{signature} limit 1
    </select>

	<select id="getMaterialById" resultType="java.lang.String">
        select CONCAT('https://v.vzhifu.net/',type,'/',signature) as url from dnwx_adt.dn_jettison_material where id in (${idList})  
    </select>
    
    <select id="getMaterial" resultType="java.util.Map">
        select CONCAT('https://v.vzhifu.net/',type,'/',signature) as filePath,fileName,type,signature from dnwx_adt.dn_jettison_material where id in (${idList})  
    </select>
    
    <insert id="uploadMaterial" parameterType="com.wbgame.pojo.jettison.report.MaterialDTO" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO dnwx_adt.dn_jettison_material(fileName,pathName,filePath, signature,artist,creatives,producer3d, label1, 
        label2, label3,height,wide,size,`type`, format, `day`,createUser,updateUser,updateTime,createTime,signature2,script_num,tag_app_name,immortal3d,product_part,is_syn_cl)
        VALUES
        (#{fileName},#{pathName},#{filePath},#{signature}, #{artist}, #{creatives}, #{producer3d},  #{label1},#{label2},#{label3},#{high},#{wide},
         #{size},#{type}, #{format}, #{day}, #{createUser}, #{updateUser}, #{updateTime}, #{createTime},#{signature2},#{scriptNum},#{tagAppName},#{immortal3d},#{product_part},#{is_syn_cl})
    </insert>


	<update id="updateMaterialStatus"  parameterType="com.wbgame.pojo.jettison.report.MaterialDTO" >
       UPDATE dnwx_adt.dn_jettison_material set status=-1  where id=#{id}
   	</update>

    <insert id="saveUploadMaterialTask" parameterType="com.wbgame.pojo.jettison.UploadMaterialTaskDTO" keyColumn="id" keyProperty="id" useGeneratedKeys="true" >
       INSERT INTO dnwx_adt.dn_jettison_material_upload_task
       (task_name,time,create_user, status,total_count)
       VALUES
       (#{task_name},#{time},#{create_user},#{status}, #{total_count})
    </insert>
	
	<update id="updateUploadMaterialTask"  parameterType="com.wbgame.pojo.jettison.UploadMaterialTaskDTO" >
       UPDATE dnwx_adt.dn_jettison_material_upload_task set status=1,success_count=#{success_count},fail_count=#{fail_count}  where id=#{id}
   	</update>
   	
   	<insert id="bacthInsertUploadRecord" parameterType="com.wbgame.pojo.jettison.UploadMaterialRecordDTO"  >
       INSERT INTO dnwx_adt.dn_jettison_material_upload_record (task_id,material_name,path_name, material_type,upload_status,sync_status,error_msg) VALUES
       <foreach collection="recordList" item="li" separator=",">
			(#{li.task_id},#{li.material_name},#{li.path_name},#{li.material_type}, #{li.upload_status}, #{li.sync_status},#{li.error_msg})
		</foreach>	
       
    </insert>

    <select id="getMaterialCountGroupByArtist" resultType="java.util.Map" parameterType="com.wbgame.pojo.jettison.report.param.MaterialReportParam">
        select artist,count(id) as counts from dnwx_adt.dn_jettison_material 
		WHERE 1 = 1
		<if test="artist != null and artist.size > 0">
			AND (
			artist IN
			<foreach collection="artist" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			OR
			<foreach collection="artist" index="index" item="item" separator="OR">
				artist LIKE "%"#{item}"%"
			</foreach>
			)
		</if>
		<if test="type != null and type.size > 0">
			AND 
			type IN
			<foreach collection="type" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="createDay != null and createDay.size > 0">
			AND day <![CDATA[ >= ]]> #{createDay[0]}
			AND day <![CDATA[ <= ]]> #{createDay[1]}
		</if>
		group by artist 
		order by counts
    </select>
    <select id="getMaterialCount" resultType="com.wbgame.pojo.jettison.vo.EffectivenessManageVo" parameterType="com.wbgame.pojo.jettison.param.EffectivenessManageParam">
		select
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
			artist,producer3d artist3d,creatives creatives,
			</when>
			<otherwise>
				artist,
			</otherwise>
		</choose>
		count(id) as new_material_num,
		<choose>
			<when test='group.contains("date")'>
				DATE_FORMAT(`day`,'%Y-%m-%d') tdate
			</when>
			<when test='group.contains("week")'>
				DATE_FORMAT(`day`,'%Y-%u') tdate
			</when>
			<when test='group.contains("month")'>
				DATE_FORMAT(`day`,'%Y-%m') tdate
			</when>
			<otherwise>
				DATE_FORMAT(`day`,'%Y') tdate
			</otherwise>
		</choose>
		from dnwx_adt.dn_jettison_material  WHERE 1 = 1
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
				<if test="artists != null and artists.size > 0">
					AND 
					<foreach collection="artists" index="index" item="item" open="(" separator="or" close=")">
						artist LIKE "%"#{item}"%" OR producer3d LIKE "%"#{item}"%" OR creatives LIKE "%"#{item}"%"
					</foreach>
				</if>
			</when>
			<otherwise>
				<if test="artists != null and artists.size > 0">
					AND (
					<foreach collection="artists" index="index" item="item"  separator="or">
						artist LIKE "%"#{item}"%" 
					</foreach>
					)
				</if>
			</otherwise>
		</choose>
		<choose>
			<when test="material_begin_online_time != null and material_end_online_time!= null">
				AND `day` <![CDATA[ >= ]]> #{material_begin_online_time}
				AND `day` <![CDATA[ <= ]]> #{material_end_online_time}
			</when>
			<otherwise>
				AND `day` <![CDATA[ >= ]]> #{begin_date}
				AND `day` <![CDATA[ <= ]]> #{end_date}
			</otherwise>
		</choose>
		<choose>
			<when test='group.contains("date")'>
				GROUP BY DATE_FORMAT(`day`,'%Y-%m-%d'),
			</when>
			<when test='group.contains("week")'>
				GROUP BY DATE_FORMAT(`day`,'%Y-%u'),
			</when>
			<when test='group.contains("month")'>
				GROUP BY DATE_FORMAT(`day`,'%Y-%m'),
			</when>
			<otherwise>
				GROUP BY DATE_FORMAT(`day`,'%Y'),
			</otherwise>
		</choose>
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
				`artist`,artist3d,creatives
			</when>
			<otherwise>
				`artist`
			</otherwise>
		</choose>
    </select>
    <select id="getCumulativeOnlineNum" resultType="com.wbgame.pojo.jettison.vo.EffectivenessManageVo" parameterType="com.wbgame.pojo.jettison.param.EffectivenessManageParam">
    	select
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
			artist,producer3d artist3d,creatives,
			</when>
			<otherwise>
				artist,
			</otherwise>
		</choose>
		count(id) as cumulative_online_num
		from dnwx_adt.dn_jettison_material where 
		<choose>
			<when test='group.contains("multiRole") or group.contains("artGroup")'>
				<if test="artists != null and artists.size > 0">
					<foreach collection="artists" index="index" item="item" separator="or" >
						artist LIKE "%"#{item}"%" OR producer3d LIKE "%"#{item}"%" OR creatives LIKE "%"#{item}"%"
					</foreach>
					group  by `artist`,artist3d,creatives
				</if>
			</when>
			<otherwise>
				<if test="artists != null and artists.size > 0">
					<foreach collection="artists" index="index" item="item"  separator="or">
						artist LIKE "%"#{item}"%" 
					</foreach>
				</if>
				group  by `artist`
			</otherwise>
		</choose>
    </select>

    <insert id="insertMaterialMD5Matchs">
        replace into dnwx_adt.dn_video_md5_match(video_id,md5,ad_platform,account,create_owner,update_owner,update_time,create_time)
        values
        <foreach item="item" collection="list" separator=",">
			(#{item.video_id},#{item.md5},#{item.ad_platform},#{item.account},#{item.create_owner},#{item.update_owner},now(),now())
		</foreach>
	</insert>

	<select id="selectArtistByName" resultType="java.util.Map">
		select * from dnwx_adt.dn_artist where name = #{artist} limit 1
	</select>

	<select id="selectMaterialByids" resultType="com.wbgame.pojo.jettison.report.MaterialDTO">
		select * from dnwx_adt.dn_jettison_material where id in (${ids})
	</select>
</mapper>