<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.jettison.MaterialTagAppManageMapper">

    
	<!--标签产品管理-->
    <select id="list" resultType="com.wbgame.pojo.jettison.MaterialTagAppDto"
            parameterType="com.wbgame.pojo.jettison.MaterialTagAppDto">
        select
        *
        from dnwx_adt.dn_jettison_tag_app_manage
        <where>
            <if test="appname != null and appname != ''">
                and appname like "%"#{appname}"%"
            </if>
            <if test="status != null">
                and status = #{status}
           </if>
        </where>
    </select>
    
    <delete id="delete" parameterType="java.lang.Integer">
        delete from dnwx_adt.dn_jettison_tag_app_manage
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <insert id="add" parameterType="com.wbgame.pojo.jettison.MaterialTagAppDto"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into dnwx_adt.dn_jettison_tag_app_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">
                appname,
                update_user,
                create_user,
             create_time,
             update_time,
          	<if test="status != null">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                #{appname,jdbcType=VARCHAR},
                #{update_user,jdbcType=VARCHAR},
                #{create_user,jdbcType=VARCHAR},
                 CURRENT_TIMESTAMP,
            	CURRENT_TIMESTAMP,
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.wbgame.pojo.operate.WxGameAppManageDto">
        update dnwx_adt.dn_jettison_tag_app_manage
        <set>
             appname = #{appname,jdbcType=VARCHAR},
             <if test="status != null">
              status = #{status,jdbcType=TINYINT},
        	 </if>
             update_user = #{update_user,jdbcType=VARCHAR},
        	 update_time = CURRENT_TIMESTAMP
        </set>
        where id = #{id}
    </update>
</mapper>