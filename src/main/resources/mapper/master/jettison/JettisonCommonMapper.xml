<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.jettison.JettisonCommonMapper">

	<select id="getChannel" resultType="com.wbgame.pojo.jettison.report.dto.ChannelDTO">
		SELECT dci.*, dct.type_name FROM dn_channel_info dci
		LEFT JOIN dn_channel_type dct ON dci.cha_type = dct.type_id
	</select>

	<select id="getChannelTypeName" resultType="java.lang.String">
		SELECT type_name FROM dn_channel_type
	</select>

	<select id="getArtist" resultType="java.util.Map">
		SELECT * FROM dnwx_adt.dn_artist
	</select>

	<select id ="getPutUser" resultType="java.lang.String" >
		SELECT putUser FROM dnwx_adt.dn_putUser ORDER BY id ASC
	</select>

	<select id ="getAgent" resultType="com.wbgame.pojo.jettison.report.dto.AgentDTO" >
		SELECT * FROM dnwx_adt.dn_agent ORDER BY id DESC
	</select>

	<select id ="getAgentByName" resultType="java.lang.String" >
		SELECT agent FROM dnwx_adt.dn_agent ORDER BY id DESC
	</select>

	<select id="getGameName" resultType="java.lang.String">
		SELECT tappname FROM adv_platform_app_info
	</select>

	<select id="getCompanyName" resultType="java.lang.String">
		SELECT company FROM dnwx_cfg.dn_email_info where length(company)>20
	</select>

	<select id="getAdsenseType" resultType="java.lang.String">
		SELECT DISTINCT adsenseType FROM dnwx_adt.dn_adsense ORDER BY id ASC
	</select>

	<select id ="getAdsensePosition" resultType="java.lang.String" >
		SELECT DISTINCT adsensePosition FROM dnwx_adt.dn_adsense ORDER BY id ASC
	</select>

	<select id="getAccountType" resultType="java.util.Map">
		SELECT id,`type` AS name FROM dnwx_adt.dn_account_type
	</select>
	
	<select id ="getArtistName" resultType="java.lang.String" >
		SELECT DISTINCT name FROM dnwx_adt.dn_artist
	</select>

	<select id="getStrategys" resultType="java.util.Map">
		select id,strategyName name from dnwx_adt
		.dn_strategy
	</select>

	<select id="getArtistByType" resultType="java.util.Map">
		SELECT * FROM dnwx_adt.dn_artist where type = #{type}
	</select>

	<select id="getPkgName" resultType="java.lang.String">
	    SELECT packagename FROM adv_platform_app_info group by packagename
	</select>

	<select id="getShopId" resultType="java.lang.String">
	    SELECT
	    case when platform = 'vivo' then shopId
		     else tappid
		     end shopId
	    FROM adv_platform_app_info
	</select>

</mapper>