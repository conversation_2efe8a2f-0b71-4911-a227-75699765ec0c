<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.jettison.DnwxAdtMapper">

	<select id="getBatchList" parameterType="com.wbgame.pojo.jettison.report.param.BatchReportParam"
            resultType="com.wbgame.pojo.jettison.report.dto.BatchReportDTO">
        SELECT * FROM dnwx_adt.dn_put_schedule WHERE 1 = 1
        <if test="media != null and media != ''">
            AND media  = #{media}
        </if>
        <if test="account != null and account != ''">
            AND account  = #{account}
        </if>
        <if test="ruleStatus != null and ruleStatus != ''">
            AND ruleStatus  = #{ruleStatus}
        </if>
        <if test="createUser != null and createUser != ''">
            AND createUser LIKE concat('%',#{createUser},'%')
        </if>
        <if test="ruleName != null and ruleName != ''">
            AND ruleName LIKE concat('%',#{ruleName},'%')
        </if>
    </select>

    <select id="getBatchCount" parameterType="java.lang.Integer"
            resultType="java.lang.Integer">
        SELECT count(*) FROM dnwx_adt.dn_put_schedule WHERE id = #{id} AND ruleStatus = 1
    </select>

    <insert id="addBatch" parameterType="com.wbgame.pojo.jettison.report.dto.BatchReportDTO">
        INSERT INTO dnwx_adt.dn_put_schedule
        (
         ruleName, media, account,rule,ruleRate,ruleTime, ruleCron,createUser,
         templateId,templateType,firstName,firstBudget,secondName,secondBudget,secondNumber,thirdName,thirdNumber,singleMaterialNumber,singleTitleNumber,
         bid,deepBid,titlePath,materialPath,materialType,materialSpec,materialChoose,materialNumber,startTime,endTime,status
         )
        VALUES
        (
         #{ruleName},#{media},#{account}, #{rule}, #{ruleRate}, #{ruleTime}, #{ruleCron}, #{createUser},
         #{templateId},#{templateType},#{firstName},#{firstBudget}, #{secondName}, #{secondBudget}, #{secondNumber}, #{thirdName},
         #{thirdNumber},#{singleMaterialNumber},#{singleTitleNumber}, #{bid},#{deepBid}, #{titlePath}, #{materialPath},
         #{materialType},#{materialSpec}, #{materialChoose},#{materialNumber}, #{startTime}, #{endTime},#{status}
         )
    </insert>

    <insert id="deleteBatch">
        DELETE FROM dnwx_adt.dn_put_schedule WHERE id = #{id}
    </insert>

    <update id="updateBatch" parameterType="com.wbgame.pojo.jettison.report.dto.BatchReportDTO">
        UPDATE dnwx_adt.dn_put_schedule SET
            ruleName = #{ruleName},
            media = #{media},
            account = #{account},
            rule = #{rule},
            ruleRate = #{ruleRate},
            ruleTime = #{ruleTime},
            ruleCron = #{ruleCron},
            templateId = #{templateId},
            templateType = #{templateType},
            firstName = #{firstName},
            firstBudget = #{firstBudget},
            secondName = #{secondName},
            secondBudget  = #{secondBudget},
            secondNumber = #{secondNumber},
            thirdName = #{thirdName},
            thirdNumber = #{thirdNumber},
            singleMaterialNumber = #{singleMaterialNumber},
            singleTitleNumber = #{singleTitleNumber},
            bid = #{bid},
            deepBid = #{deepBid},
            titlePath = #{titlePath},
            materialPath = #{materialPath},
            materialType = #{materialType},
            materialSpec = #{materialSpec},
            materialChoose = #{materialChoose},
            materialNumber = #{materialNumber},
            startTime = #{startTime},
            endTime = #{endTime},
            status  = #{status},
            updateUser = #{createUser}
        WHERE id = #{id}
    </update>

    <update id="enableBatch">
        UPDATE dnwx_adt.dn_put_schedule SET ruleStatus = #{status}
        WHERE id = #{id}
    </update>


    <select id="getTransferType" resultType="java.util.Map">
        SELECT id,strategyName FROM dnwx_adt.dn_strategy
    </select>

    <select id="getArtist" resultType="com.wbgame.pojo.jettison.report.dto.ArtistDTO">
        SELECT * FROM dnwx_adt.dn_artist WHERE 1=1
        <if test="artistName !=null and artistName !=''">
            AND `name` LIKE "%"#{artistName}"%"
        </if>
        ORDER BY createTime DESC
    </select>

    <select id="getArtistName" resultType="com.wbgame.pojo.jettison.report.dto.ArtistDTO">
        SELECT * FROM dnwx_adt.dn_artist WHERE 1=1
        <if test="artistName !=null and artistName !=''">
            AND `name` = #{artistName}
        </if>
        <if test="id !=null and id !=''">
            AND `id` != #{id}
        </if>
    </select>

    <insert id="addArtist" parameterType="com.wbgame.pojo.jettison.report.dto.ArtistDTO">
        INSERT IGNORE INTO dnwx_adt.dn_artist
        ( mark, `name`, clid, createUser ,is_job) VALUES ( #{mark}, #{name}, #{clid}, #{createUser} ,1)
    </insert>

    <update id="updateArtist" parameterType="com.wbgame.pojo.jettison.report.dto.ArtistDTO">
        UPDATE dnwx_adt.dn_artist SET
          mark = #{mark},
          `name` = #{name},
          clid = #{clid},
          is_job = #{is_job},
          updateUser = #{updateUser}
        WHERE id = #{id}
    </update>

    <select id="getPutUser" resultType="com.wbgame.pojo.jettison.report.dto.PutUserDTO">
        SELECT * FROM dnwx_adt.dn_putuser WHERE 1=1
        <if test="putUserName !=null and putUserName !=''">
            AND putUser LIKE "%"#{putUserName}"%"
        </if>
        ORDER BY createTime DESC
    </select>

    <select id="getPutUserName" resultType="com.wbgame.pojo.jettison.report.dto.PutUserDTO">
        SELECT * FROM dnwx_adt.dn_putuser WHERE 1=1
        <if test="putUserName !=null and putUserName !=''">
            AND putUser = #{putUserName}
        </if>
        <if test="id !=null and id !=''">
            AND `id` != #{id}
        </if>
    </select>

    <insert id="addPutUser" parameterType="com.wbgame.pojo.jettison.report.dto.PutUserDTO">
        INSERT IGNORE INTO dnwx_adt.dn_putuser
        ( putUser, uname,createUser ) VALUES ( #{putUser}, #{uname}, #{createUser})
    </insert>

    <update id="updatePutUser" parameterType="com.wbgame.pojo.jettison.report.dto.PutUserDTO">
        UPDATE dnwx_adt.dn_putuser SET
          putUser = #{putUser},
          uname = #{uname},
          updateUser = #{updateUser}
        WHERE id = #{id}
    </update>

    <insert id="addTTTAccount">
        INSERT IGNORE INTO dnwx_adt.dn_233_account_spend
        (
        parentAccount, account, accountName, access_key, access_secret, `type`, first_agent, agent,
        putUser, rebate, remark, groupId, createUser, business, strategy , appid , media,createTime,artist
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.parentAccount}, #{item.account}, #{item.accountName},#{item.clientId}, #{item.clientSecret}, #{item.type}, #{item.first_agent}, #{item.agent},
            #{item.putUser}, #{item.rebate}, #{item.remark}, #{item.groupId}, #{item.createUser}, #{item.business}, #{item.strategy} ,#{item.appId} ,#{item.media},now()
            ,#{item.artist}
            )
        </foreach>
    </insert>

    <select id="getStrategy" resultType="java.util.Map">
        SELECT *  FROM dnwx_adt.dn_strategy
    </select>

    <select id="get233ParentAccount" resultType="java.lang.String">
        select account from dnwx_adt.dn_233_account_spend where parentAccount is null
    </select>

    <select id="get233Accounts" resultType="com.wbgame.pojo.jettison.report.dto.AccountDTO">
        select parentAccount,account,accountName,access_key clientId,access_secret clientSecret,appid appId,media,type,first_agent,agent,putUser,rebate,remark,groupId,enabled
        ,business,strategy,createTime,updateTime,createUser,updateUser,artist from dnwx_adt.dn_233_account_spend
        where 1=1
        <if test="account != null and account != ''">
            AND account = #{account}
        </if>
        <if test="enabled != null">
            and enabled = #{enabled}
        </if>
        <if test="parentAccount != null and parentAccount != ''">
            AND parentAccount = #{parentAccount}
        </if>
        <if test="accountName != null and accountName != ''">
            <bind name="accountNameSearch" value="'%' + accountName + '%'"/>
            AND (accountName LIKE #{accountNameSearch} OR account LIKE #{accountNameSearch})
        </if>
        <if test="first_agent != null and first_agent != ''">
            <bind name="firstAgentSearch" value="'%' + first_agent + '%'"/>
            AND first_agent LIKE #{firstAgentSearch}
        </if>
        <if test="agent != null and agent != ''">
            <bind name="agentSearch" value="'%' + agent + '%'"/>
            AND agent LIKE #{agentSearch}
        </if>
        <if test="putUsers != null and putUsers.size > 0">
            AND putUser IN
            <foreach collection="putUsers" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="artist != null and artist.size > 0">
            AND artist IN
            <foreach collection="artist" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="groupIds != null and groupIds.size > 0">
            AND groupId IN
            <foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="business != null and business.size > 0">
            AND business IN
            <foreach collection="business" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="remark != null and remark != ''">
            <bind name="remarkSearch" value="'%' + remark + '%'"/>
            AND remark LIKE #{remarkSearch}
        </if>
        ORDER BY createTime DESC
    </select>

    <insert id="add233ParentAccount">
        insert into dnwx_adt.dn_233_account_spend(account,accountName,access_key,access_secret,createTime,createUser) values
        (#{account},#{accountName},#{clientId},#{clientSecret},now(),#{createUser})
    </insert>

    <update id="update233Account">
        UPDATE dnwx_adt.dn_233_account_spend SET
			accountName = #{accountName},
			parentAccount = #{parentAccount},
			media = #{media},
			`type` = #{type},
			first_agent = #{first_agent},
			agent = #{agent},
			putUser = #{putUser},
			rebate = #{rebate},
			remark = #{remark},
			groupId = #{groupId},
			updateUser = #{updateUser},
			business = #{business},
			strategy = #{strategy},
			appid = #{appId},
			artist = #{artist},
			updateTime = now()
		WHERE account = #{account}
    </update>

    <update id="updateEnabled">
        UPDATE dnwx_adt.${table} SET enabled = #{enabled},updateUser = #{updateUser},updateTime = now()
        WHERE account IN
        <foreach collection="accountList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateGroupAccount">
        UPDATE dnwx_adt.${table} SET groupId = #{groupId},updateUser = #{updateUser},updateTime = now()
        WHERE account IN
        <foreach collection="accountList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectGuanBaoReport1" resultType="com.wbgame.pojo.jettison.report.dto.GuanBaoReport">
        select day,appid,media,channel,transfer_type,app_name,strategyName,account,putUser
        ,sum(spend) spend,sum(rebate_spend) rebate_spend,sum(installs) installs,sum(predict_spend) predict_spend
        ,sum(predict_rebate_spend) predict_rebate_spend,sum(game_addiction) game_addiction
        ,ifnull(convert(sum(rebate_spend)/sum(installs),decimal(10,2)),0.00) installs_cost
        ,ifnull(convert(sum(predict_spend)/if(transfer_type = 5,sum(game_addiction),sum(installs)),decimal(10,2)),0.00) real_bid
        ,ifnull(convert(sum(rebate_spend)/sum(game_addiction),decimal(10,2)),0.00) addiction_cost
        from (select *
         ,case media
         when '头条' then convert(predict_spend/1.045,decimal(10,2))
         when '快手' then convert(predict_spend/1.025,decimal(10,2))
         when '广点通' then convert(predict_spend/1.075,decimal(10,2))
         when '百度' then convert(predict_spend/1.43,decimal(10,2))
         else predict_spend
         end predict_rebate_spend
        from dnwx_adt.dn_guanbao_report
        <include refid="getGuanBaoReportCondition1"/>
        ) a
        left join app_info f on a.appid = f.id
        left join dnwx_adt.dn_strategy c on a.transfer_type = c.id
        <if test="group != null and group != ''">
            GROUP BY ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </select>

    <select id="selectGuanBaoReport2" resultType="com.wbgame.pojo.jettison.report.dto.GuanBaoReport">
        select day,appid,channel,media,app_name,spend,rebate_spend,installs,predict_spend,predict_rebate_spend,income,add_num,act_num
        ,installs_cost,real_bid,concat(all_roi,'%') all_roi,concat(predict_real_roi,'%') predict_real_roi,all_profit
        ,concat(all_profit_rate,'%') all_profit_rate,predict_profit,concat(predict_profit_rate,'%') predict_profit_rate
        ,all_arpu,game_addiction,addiction_cost
        from
        (select day,appid,channel,media,app_name
        ,sum(spend) spend,sum(rebate_spend) rebate_spend,sum(installs) installs,sum(predict_spend) predict_spend,sum(game_addiction) game_addiction
        ,sum(predict_rebate_spend) predict_rebate_spend,sum(income) income,sum(add_num) add_num,sum(act_num) act_num
        ,ifnull(convert(sum(rebate_spend)/sum(installs),decimal(10,2)),0.00) installs_cost
        ,ifnull(convert(sum(rebate_spend)/sum(game_addiction),decimal(10,2)),0.00) addiction_cost
        ,ifnull(convert(sum(predict_spend)/sum(installs),decimal(10,2)),0.00) real_bid
        ,ifnull(convert(sum(income)/sum(rebate_spend)*100,decimal(10,2)),0.00) all_roi
        ,ifnull(convert(sum(income)/sum(predict_rebate_spend)*100,decimal(10,2)),0.00) predict_real_roi
        ,ifnull(sum(income)-sum(spend),0.00) all_profit
        ,ifnull(convert((sum(income)-sum(spend))/sum(spend)*100,decimal(10,2)),0.00) all_profit_rate
        ,ifnull(sum(income)-sum(predict_rebate_spend),0.00) predict_profit
        ,ifnull(convert((sum(income)-sum(predict_rebate_spend))/sum(predict_rebate_spend)*100,decimal(10,2)),0.00) predict_profit_rate
        ,ifnull(convert(sum(income)/sum(act_num),decimal(10,2)),0.00) all_arpu
        from
        (select day,appid,channel,media,sum(spend) spend,sum(rebate_spend) rebate_spend,sum(installs) installs
        ,sum(predict_spend) predict_spend,sum(game_addiction) game_addiction
        ,case media
        when '头条' then convert(sum(predict_spend)/1.045,decimal(10,2))
        when '快手' then convert(sum(predict_spend)/1.025,decimal(10,2))
        when '广点通' then convert(sum(predict_spend)/1.075,decimal(10,2))
        when '百度' then convert(sum(predict_spend)/1.43,decimal(10,2))
        else sum(predict_spend)
        end predict_rebate_spend
        from dnwx_adt.dn_guanbao_report
        <include refid="getGuanBaoReportCondition1"/>
        group by day,appid,channel,media) a

        left join

        (select day b_day,appid b_appid,channel b_channel,media b_media,income,add_num,act_num
        from dnwx_adt.dn_guanbao_report
        <include refid="getGuanBaoReportCondition1"/>
        group by day,appid,channel,media) b
        on a.appid = b_appid and a.day = b_day and a.channel = b_channel and a.media = b_media

        left join app_info f on a.appid = f.id
        <if test="group != null and group != ''">
            GROUP BY ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>) a
    </select>

    <select id="selectGuanBaoReport3" resultType="com.wbgame.pojo.jettison.report.dto.GuanBaoReport">
        select day,appid,media,app_name,spend,rebate_spend,installs,predict_spend,predict_rebate_spend,income,add_num,act_num
        ,installs_cost,real_bid,concat(all_roi,'%') all_roi,concat(predict_real_roi,'%') predict_real_roi,all_profit
        ,concat(all_profit_rate,'%') all_profit_rate,predict_profit,concat(predict_profit_rate,'%') predict_profit_rate
        ,all_arpu,pay_rec_amount,real_spend,real_rebate_spend,concat(real_roi,'%') real_roi,d_real_profit
        ,concat(d_real_profit_rate,'%') d_real_profit_rate,game_addiction,addiction_cost
        from
        (select day,appid,media,app_name
        ,sum(spend) spend,sum(rebate_spend) rebate_spend,sum(installs) installs,sum(predict_spend) predict_spend,sum(game_addiction) game_addiction
        ,sum(predict_rebate_spend) predict_rebate_spend,sum(income) income,sum(add_num) add_num,sum(act_num) act_num
        ,ifnull(convert(sum(rebate_spend)/sum(installs),decimal(10,2)),0.00) installs_cost
        ,ifnull(convert(sum(rebate_spend)/sum(game_addiction),decimal(10,2)),0.00) addiction_cost
        ,ifnull(convert(sum(predict_spend)/sum(installs),decimal(10,2)),0.00) real_bid
        ,ifnull(convert(sum(income)/sum(rebate_spend)*100,decimal(10,2)),0.00) all_roi
        ,ifnull(convert(sum(income)/sum(predict_rebate_spend)*100,decimal(10,2)),0.00) predict_real_roi
        ,ifnull(sum(income)-sum(spend),0.00) all_profit
        ,ifnull(convert((sum(income)-sum(spend))/sum(spend)*100,decimal(10,2)),0.00) all_profit_rate
        ,ifnull(sum(income)-sum(predict_rebate_spend),0.00) predict_profit
        ,ifnull(convert((sum(income)-sum(predict_rebate_spend))/sum(predict_rebate_spend)*100,decimal(10,2)),0.00) predict_profit_rate
        ,ifnull(convert(sum(income)/sum(act_num),decimal(10,2)),0.00) all_arpu
				,sum(pay_rec_amount) pay_rec_amount
				,sum(spend) - sum(pay_rec_amount) real_spend
				,sum(real_rebate_spend) - sum(pay_rec_rebate_amount) real_rebate_spend
				,ifnull(convert(sum(income)/(sum(real_rebate_spend) - sum(pay_rec_rebate_amount))*100,decimal(10,2)),0.00) real_roi
				,ifnull(sum(income)-sum(real_rebate_spend) + sum(pay_rec_rebate_amount),0.00) d_real_profit
        ,ifnull(convert((sum(income)-sum(real_rebate_spend) + sum(pay_rec_rebate_amount))
				/(sum(real_rebate_spend) - sum(pay_rec_rebate_amount))*100,decimal(10,2)),0.00) d_real_profit_rate
        from
        (select day,appid,media,sum(spend) spend,sum(rebate_spend) rebate_spend,sum(installs) installs
        ,sum(predict_spend) predict_spend,sum(game_addiction) game_addiction
        ,case media
        when '头条' then convert(sum(predict_spend)/1.045,decimal(10,2))
        when '快手' then convert(sum(predict_spend)/1.025,decimal(10,2))
        when '广点通' then convert(sum(predict_spend)/1.075,decimal(10,2))
        when '百度' then convert(sum(predict_spend)/1.43,decimal(10,2))
        else sum(predict_spend)
        end predict_rebate_spend
				,case media
        when '头条' then convert(sum(spend)/1.045,decimal(10,2))
        when '快手' then convert(sum(spend)/1.025,decimal(10,2))
        when '广点通' then convert(sum(spend)/1.075,decimal(10,2))
        when '百度' then convert(sum(spend)/1.43,decimal(10,2))
        else sum(spend)
				end real_rebate_spend
        from dnwx_adt.dn_guanbao_report
        <include refid="getGuanBaoReportCondition1"/>
        group by day,appid,media) a

        left join

        (select day b_day,appid b_appid,media b_media,sum(income) income,sum(add_num) add_num,sum(act_num) act_num
				from (select day,appid,channel,media,income,add_num,act_num
				from dnwx_adt.dn_guanbao_report
        <include refid="getGuanBaoReportCondition1"/>
        group by day,appid,channel,media) a
				GROUP BY day,appid,media) b
        on a.appid = b_appid and a.day = b_day and a.media = b_media

				left join

				(select day c_day,appid c_appid,media c_media,pay_rec_amount
				,case media
        when '头条' then convert(pay_rec_amount/1.045,decimal(10,2))
        when '快手' then convert(pay_rec_amount/1.025,decimal(10,2))
        when '广点通' then convert(pay_rec_amount/1.075,decimal(10,2))
        when '百度' then convert(pay_rec_amount/1.43,decimal(10,2))
        else pay_rec_amount
				end pay_rec_rebate_amount
				from dnwx_adt.dn_guanbao_report
        <include refid="getGuanBaoReportCondition1"/>
        group by day,appid,media) c
				on a.appid = c_appid and a.day = c_day and a.media = c_media

        left join app_info f on a.appid = f.id
        <if test="group != null and group != ''">
            GROUP BY ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>) a
    </select>

    <select id="countGuanBaoReport1" resultType="com.wbgame.pojo.jettison.report.dto.GuanBaoReport">
        select sum(spend) spend,sum(rebate_spend) rebate_spend,sum(installs) installs,sum(predict_spend) predict_spend
        ,sum(predict_rebate_spend) predict_rebate_spend,sum(game_addiction) game_addiction
        ,ifnull(convert(sum(rebate_spend)/sum(installs),decimal(10,2)),0.00) installs_cost
        ,ifnull(convert(sum(rebate_spend)/sum(game_addiction),decimal(10,2)),0.00) addiction_cost
        ,ifnull(convert(sum(predict_spend)/if(transfer_type = 5,sum(game_addiction),sum(installs)),decimal(10,2)),0.00) real_bid
        from (select *
        ,case media
        when '头条' then convert(predict_spend/1.045,decimal(10,2))
        when '快手' then convert(predict_spend/1.025,decimal(10,2))
        when '广点通' then convert(predict_spend/1.075,decimal(10,2))
        when '百度' then convert(predict_spend/1.43,decimal(10,2))
        else predict_spend
        end predict_rebate_spend
        from dnwx_adt.dn_guanbao_report
        <include refid="getGuanBaoReportCondition1"/>
        ) a
    </select>

    <select id="countGuanBaoReport2" resultType="com.wbgame.pojo.jettison.report.dto.GuanBaoReport">
        select sum(spend) spend,sum(rebate_spend) rebate_spend,sum(installs) installs,sum(predict_spend) predict_spend,sum(game_addiction) game_addiction
        ,sum(predict_rebate_spend) predict_rebate_spend,sum(income) income,sum(add_num) add_num,sum(act_num) act_num
        ,ifnull(convert(sum(rebate_spend)/sum(installs),decimal(10,2)),0.00) installs_cost
        ,ifnull(convert(sum(rebate_spend)/sum(game_addiction),decimal(10,2)),0.00) addiction_cost
        ,ifnull(convert(sum(predict_spend)/sum(installs),decimal(10,2)),0.00) real_bid
        ,concat(ifnull(convert(sum(income)/sum(rebate_spend)*100,decimal(10,2)),0.00),'%') all_roi
        ,concat(ifnull(convert(sum(income)/sum(predict_rebate_spend)*100,decimal(10,2)),0.00),'%') predict_real_roi
        ,ifnull(sum(income)-sum(spend),0.00) all_profit
        ,concat(ifnull(convert((sum(income)-sum(spend))/sum(spend)*100,decimal(10,2)),0.00),'%') all_profit_rate
        ,ifnull(sum(income)-sum(predict_rebate_spend),0.00) predict_profit
        ,concat(ifnull(convert((sum(income)-sum(predict_rebate_spend))/sum(predict_rebate_spend)*100,decimal(10,2)),0.00),'%') predict_profit_rate
        ,ifnull(convert(sum(income)/sum(act_num),decimal(10,2)),0.00) all_arpu
        from
        (select day,appid,channel,media,sum(spend) spend,sum(rebate_spend) rebate_spend,sum(installs) installs
        ,sum(predict_spend) predict_spend,sum(game_addiction) game_addiction
        ,case media
        when '头条' then convert(sum(predict_spend)/1.045,decimal(10,2))
        when '快手' then convert(sum(predict_spend)/1.025,decimal(10,2))
        when '广点通' then convert(sum(predict_spend)/1.075,decimal(10,2))
        when '百度' then convert(sum(predict_spend)/1.43,decimal(10,2))
        else sum(predict_spend)
        end predict_rebate_spend
        from dnwx_adt.dn_guanbao_report
        <include refid="getGuanBaoReportCondition1"/>
        group by day,appid,channel,media) a

        left join

        (select day b_day,appid b_appid,channel b_channel,media b_media,income,add_num,act_num
        from dnwx_adt.dn_guanbao_report
        <include refid="getGuanBaoReportCondition1"/>
        group by day,appid,channel,media) b
        on a.appid = b_appid and a.day = b_day and a.channel = b_channel and a.media = b_media
    </select>

    <select id="countGuanBaoReport3" resultType="com.wbgame.pojo.jettison.report.dto.GuanBaoReport">
        select sum(spend) spend,sum(rebate_spend) rebate_spend,sum(installs) installs,sum(predict_spend) predict_spend,sum(game_addiction) game_addiction
        ,sum(predict_rebate_spend) predict_rebate_spend,sum(income) income,sum(add_num) add_num,sum(act_num) act_num
        ,ifnull(convert(sum(rebate_spend)/sum(installs),decimal(10,2)),0.00) installs_cost
        ,ifnull(convert(sum(rebate_spend)/sum(game_addiction),decimal(10,2)),0.00) addiction_cost
        ,ifnull(convert(sum(predict_spend)/sum(installs),decimal(10,2)),0.00) real_bid
        ,concat(ifnull(convert(sum(income)/sum(rebate_spend)*100,decimal(10,2)),0.00),'%') all_roi
        ,concat(ifnull(convert(sum(income)/sum(predict_rebate_spend)*100,decimal(10,2)),0.00),'%') predict_real_roi
        ,ifnull(sum(income)-sum(spend),0.00) all_profit
        ,concat(ifnull(convert((sum(income)-sum(spend))/sum(spend)*100,decimal(10,2)),0.00),'%') all_profit_rate
        ,ifnull(sum(income)-sum(predict_rebate_spend),0.00) predict_profit
        ,concat(ifnull(convert((sum(income)-sum(predict_rebate_spend))/sum(predict_rebate_spend)*100,decimal(10,2)),0.00),'%') predict_profit_rate
        ,ifnull(convert(sum(income)/sum(act_num),decimal(10,2)),0.00) all_arpu
        ,sum(pay_rec_amount) pay_rec_amount
        ,sum(spend) - sum(pay_rec_amount) real_spend
        ,sum(real_rebate_spend) - sum(pay_rec_rebate_amount) real_rebate_spend
        ,concat(ifnull(convert(sum(income)/(sum(real_rebate_spend) - sum(pay_rec_rebate_amount))*100,decimal(10,2)),0.00),'%') real_roi
        ,ifnull(sum(income)-sum(real_rebate_spend) + sum(pay_rec_rebate_amount),0.00) d_real_profit
        ,concat(ifnull(convert((sum(income)-sum(real_rebate_spend) + sum(pay_rec_rebate_amount))
        /(sum(real_rebate_spend) - sum(pay_rec_rebate_amount))*100,decimal(10,2)),0.00),'%') d_real_profit_rate
        from
        (select day,appid,media,sum(spend) spend,sum(rebate_spend) rebate_spend,sum(installs) installs
        ,sum(predict_spend) predict_spend,sum(game_addiction) game_addiction
        ,case media
        when '头条' then convert(sum(predict_spend)/1.045,decimal(10,2))
        when '快手' then convert(sum(predict_spend)/1.025,decimal(10,2))
        when '广点通' then convert(sum(predict_spend)/1.075,decimal(10,2))
        when '百度' then convert(sum(predict_spend)/1.43,decimal(10,2))
        else sum(predict_spend)
        end predict_rebate_spend
        ,case media
        when '头条' then convert(sum(spend)/1.045,decimal(10,2))
        when '快手' then convert(sum(spend)/1.025,decimal(10,2))
        when '广点通' then convert(sum(spend)/1.075,decimal(10,2))
        when '百度' then convert(sum(spend)/1.43,decimal(10,2))
        else sum(spend)
        end real_rebate_spend
        from dnwx_adt.dn_guanbao_report
        <include refid="getGuanBaoReportCondition1"/>
        group by day,appid,media) a

        left join

        (select day b_day,appid b_appid,media b_media,sum(income) income,sum(add_num) add_num,sum(act_num) act_num
        from (select day,appid,channel,media,income,add_num,act_num
        from dnwx_adt.dn_guanbao_report
        <include refid="getGuanBaoReportCondition1"/>
        group by day,appid,channel,media) a
        GROUP BY day,appid,media) b
        on a.appid = b_appid and a.day = b_day and a.media = b_media

        left join

        (select day c_day,appid c_appid,media c_media,pay_rec_amount
        ,case media
        when '头条' then convert(pay_rec_amount/1.045,decimal(10,2))
        when '快手' then convert(pay_rec_amount/1.025,decimal(10,2))
        when '广点通' then convert(pay_rec_amount/1.075,decimal(10,2))
        when '百度' then convert(pay_rec_amount/1.43,decimal(10,2))
        else pay_rec_amount
        end pay_rec_rebate_amount
        from dnwx_adt.dn_guanbao_report
        <include refid="getGuanBaoReportCondition1"/>
        group by day,appid,media) c
        on a.appid = c_appid and a.day = c_day and a.media = c_media
    </select>

    <select id="selectTfAccount1" resultType="java.util.Map">
        SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_233_account_spend
        where account like concat('%',#{account},'%')
    </select>

    <select id="getTfChannels" resultType="com.wbgame.pojo.jettison.vo.TfChannelVo">
        SELECT dci.*, dct.type_name FROM yyhz_0308.dn_channel_info dci
        LEFT JOIN yyhz_0308.dn_channel_type dct ON dci.cha_type = dct.type_id
    </select>

    <select id="getMaterialCountByArtistAndWeek" resultType="java.util.Map">
        SELECT DATE_FORMAT(`day`,'%Y%u') `week`, artist, `type`, COUNT(1) AS `count`
        FROM dnwx_adt.dn_material
        <include refid="materialCondition"/>
        GROUP BY `week`, artist, `type`
    </select>

    <select id="getMaterialSign" resultType="java.util.Map">
        SELECT DISTINCT DATE_FORMAT(`day`,'%Y%u') `week`, signature FROM dnwx_adt.dn_material
        <include refid="materialCondition"/>
    </select>

    <sql id="getGuanBaoReportCondition1">
        WHERE `day` BETWEEN #{start_date} AND #{end_date}
        <if test="media != null and media != ''">
            AND media IN (${media})
        </if>
        <if test="spend_limit == 1">
            AND spend > 0
        </if>
        <if test="appid != null and appid != ''">
            AND appid IN (${appid})
        </if>
        <if test="channel != null and channel != ''">
            AND channel IN (${channel})
        </if>
        <if test="transfer_type != null and transfer_type != ''">
            AND transfer_type IN (${transfer_type})
        </if>
        <if test="account != null and account != ''">
            AND account IN (${account})
        </if>
        <if test="putUser != null and putUser != ''">
            AND putUser IN (${putUser})
        </if>
    </sql>

    <sql id="materialCondition">
        WHERE artist IS NOT NULL AND `artist` != ''
        <if test="days != null and days.size > 0">
            AND `day` BETWEEN #{days[0]} AND #{days[1]}
        </if>
        <if test="artists != null and artists.size > 0">
            AND (
            artist IN
            <foreach collection="artists" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            <foreach collection="artists" index="index" item="item" separator="OR">
                <bind name="artistSearch" value="'%' + item + '%'"/>
                artist LIKE #{artistSearch}
            </foreach>
            )
        </if>
        <if test="types != null and types.size > 0">
            AND `type` IN
            <foreach collection="types" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="getTfAccountListByMedia" resultType="java.util.Map">
        <choose>
            <when test="media == '头条'">
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_toutiao_account_spend
                WHERE parentAccount IS NOT NULL
                <include refid="getTfAccountListByMediaWhere"/>
            </when>
            <when test="media == '快手'">
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_kuaishou_account_spend
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
            </when>
            <when test="media == '广点通'">
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_tx_account_spend
                WHERE parentAccount IS NOT NULL
                <include refid="getTfAccountListByMediaWhere"/>
            </when>
            <when test="media == 'oppo'">
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_oppo_account_spend
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
            </when>
            <when test="media == '华为'">
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_huawei_account_spend
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
            </when>
            <when test="media == '小米'">
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_xiaomi_account_spend
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
            </when>
            <when test="media == 'vivo'">
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_vivo_account_spend
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
            </when>
            <otherwise>
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_233_account_spend
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
                union all
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_toutiao_account_spend
                WHERE parentAccount IS NOT NULL
                <include refid="getTfAccountListByMediaWhere"/>
                union all
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_kuaishou_account_spend
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
                union all
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_tx_account_spend
                WHERE parentAccount IS NOT NULL
                <include refid="getTfAccountListByMediaWhere"/>
                union all
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_mintegral_account
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
                union all
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_oppo_account_spend
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
                union all
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_baidu_account_spend
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
                union all
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_aqy_account_spend
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
                union all
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_huawei_account_spend
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
                union all
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_xiaomi_account_spend
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
                union all
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_vivo_account_spend
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
                union all
                SELECT account, IFNULL(remark, '备注') AS remark, 1 AS groupId FROM dnwx_adt.dn_facebook_spend_account
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
                union all
                SELECT account, IFNULL(remark, '备注') AS remark, 1 AS groupId FROM dnwx_adt.dn_google_spend_account
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
                union all
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_bzhan_account_spend
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
                union all
                SELECT account, IFNULL(remark, '备注') AS remark, groupId FROM dnwx_adt.dn_spend_account
                where 1=1
                <include refid="getTfAccountListByMediaWhere"/>
            </otherwise>
        </choose>
    </select>

    <sql id="getTfAccountListByMediaWhere">
        and enabled = 1
        <if test="putUser != null and putUser != ''">
            and putUser in (${putUser})
        </if>
        <if test="groupId != null and groupId != ''">
            and groupId in (${groupId})
        </if>
    </sql>
</mapper>