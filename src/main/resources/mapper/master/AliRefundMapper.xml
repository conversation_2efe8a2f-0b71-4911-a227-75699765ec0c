<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.PayRefundMapper">


    <select id="selectPayOrder" resultType="com.wbgame.pojo.WbPayInfoVo">
        select * from wb_pay_info where 1=1
        <if test="dn_order_id != null and dn_order_id != ''">
            and orderid = #{dn_order_id}
        </if>
        <if test="ali_order_id != null and ali_order_id != ''">
            and uid = #{ali_order_id}
        </if>
        limit 1;
    </select>

    <insert id="recordRefundLog">
        insert into ali_refund_log(request_id,dn_order_id,ali_order_id,refund_reason,appid,refund_money,merchant_id,code,msg,sub_code,sub_msg,paytype,create_owner,create_time)
        values
        (#{refund_out},#{dn_order_id},#{ali_order_id},#{refund_reason},#{appid},#{refund_money},#{merchant_id},#{code},#{msg},#{sub_code},#{sub_msg},#{paytype},#{create_owner},now())
    </insert>

    <select id="selectRecordRefundLogs" resultType="com.wbgame.pojo.pay.RecordRefundLog">
        select request_id,dn_order_id,ali_order_id,refund_reason,appid,ROUND(refund_money/100,2) refund_money,merchant_id,code,msg,sub_code
        ,sub_msg,paytype,create_owner,create_time from ali_refund_log
        where 1=1
        <if test="dn_order_id != null and dn_order_id != ''">
            and dn_order_id = #{dn_order_id}
        </if>
        <if test="ali_order_id != null and ali_order_id != ''">
            and ali_order_id = #{ali_order_id}
        </if>
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="merchant_id != null and merchant_id != ''">
            and merchant_id = #{merchant_id}
        </if>
        <if test="create_owner != null and create_owner != ''">
            and create_owner = #{create_owner}
        </if>
        <if test="start_time != null and start_time != ''">
            and create_time between concat(#{start_time},' 00:00:00') and concat(#{end_time},' 23:59:59')
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by create_time desc
            </otherwise>
        </choose>

    </select>

    <select id="selectRecordRefundOrder" resultType="com.wbgame.pojo.pay.PayRefundOrder">
        select a.appid,c.app_name,updatetime,ROUND(money/100,2) money,orderid,uid,b.create_owner refund_owner,b.create_time refund_time,IF(dn_order_id is null,"正常","已退款") orderStatus,a.paytype
        from wb_pay_info a
        left join (select dn_order_id,create_time,create_owner from ali_refund_log where code = 10000) b on a.orderid = b.dn_order_id
        left join app_info c on a.appid = c.id
        where 1=1
        <if test="orderid != null and orderid != ''">
            and a.orderid = #{orderid}
        </if>
        <if test="uid != null and uid != ''">
            and a.uid = #{uid}
        </if>
        limit 1
    </select>

    <select id="selectMachConfig" resultType="com.alibaba.fastjson.JSONObject">
        select * from wx_mach_config where mach_id = #{mach_id}
    </select>

</mapper>