<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.SomeMapper" >
  
  <select id="selectUserInfoByAttr" parameterType="java.util.Map" resultType="com.wbgame.pojo.RespUserInfo">
    select 
    <choose>
    	<when test="type != null and type == 'detail'">
    		<include refid="user_detail"/>
    	</when>
    	<otherwise>
    		<include refid="user_simple"/>
    	</otherwise>
    </choose> 
    from ${tableName} 
    where 1=1 
   	<if test="id != null and id != ''">
   		and id = #{id}
   	</if>
   	<if test="wxid != null and wxid != ''">
   		and wxid = #{wxid} 
   	</if>
   	<if test="imei != null and imei != ''">
   		and imei = #{imei} 
   	</if>
   	<if test="ob != null and ob == 'desc'">
   		order by id desc
   	</if>
    limit 1
  </select>
  
  <insert id="insertUserInfo" parameterType="java.util.Map">
    insert into ${tableName}(<include refid="user_detail"/>)
    values
    <foreach collection="list" item="li" separator=",">
    	(#{li.id},
		#{li.wxid},
		#{li.name},
		#{li.wxname},
		#{li.wxhead},
		#{li.issign},
		#{li.lv},
		#{li.phone_num},
		#{li.gold},
		#{li.diamond},
		#{li.sex},
		#{li.exp},
		#{li.tools},
		#{li.create_date},
		#{li.login_time},
		#{li.lsn},
		#{li.imsi},
		#{li.imei},
		#{li.mmid},
		#{li.pid})
    </foreach>
    ON DUPLICATE KEY UPDATE 
	wxid = VALUES(wxid),
	name = VALUES(name),
	wxname = VALUES(wxname),
	wxhead = VALUES(wxhead),
	issign = VALUES(issign),
	lv = VALUES(lv),
	phone_num = VALUES(phone_num),
	gold = VALUES(gold),
	diamond = VALUES(diamond),
	sex = VALUES(sex),
	exp = VALUES(exp),
	tools = VALUES(tools),
	pid = VALUES(pid)

  </insert>
  
  <select id="selectGiftConfig" resultType="com.wbgame.pojo.ConfigVo">
       select * from hls_gift_config order by priority
  </select>
  
  <sql id="user_detail">
  	id,
	wxid,
	name,
	wxname,
	wxhead,
	issign,
	lv,
	phone_num,
	gold,
	diamond,
	sex,
	exp,
	tools,
	create_date,
	login_time,
	lsn,
	imsi,
	imei,
	mmid,
	pid
  </sql>
  <sql id="user_simple">
  	id,
	wxid,
	name,
	wxname,
	wxhead,
	issign,
	lv,
	phone_num,
	gold,
	diamond,
	sex,
	exp,
	tools
  </sql>

	<!--抽奖配置-->
	<select id="selectDrawConfig" resultType="com.wbgame.pojo.DrawConfigVo">
		select * from wx_draw_config
	</select>

	<select id = "selectShortUrl" resultType="com.wbgame.pojo.ShortUrlVo">
		select * from short_url_config
	</select>

	<select id="selectShortUrlAll" resultType="com.wbgame.pojo.ShortUrlVo">
		select * from short_url_config order by id desc
	</select>

	<insert id="insertShortUrl" parameterType="com.wbgame.pojo.ShortUrlVo">
		insert into short_url_config(shortkey,urlvalue)values(#{shortkey},#{urlvalue})
	</insert>

	<select id="selectAppInfo" resultType="com.wbgame.pojo.ApkAppInfoVo">
		select * from dnwx_client.wbgui_gametype
	</select>

	<select id="selectAppInfoData" parameterType="String" resultType="com.wbgame.pojo.ApkAppInfoVo">
		select wg.*,wg.*,aut.todayUser,aut.currDau,aut.currNew,aut.currStart,aut.utimes
		from dnwx_client.wbgui_gametype wg
		LEFT JOIN apk_user_total aut ON aut.appid = wg.appid
		where aut.createtime = #{date}
	</select>

	<select id="selectApkUserTotal" parameterType="java.lang.String" resultType="com.wbgame.pojo.ApkUserTotalVo">
		select * from apk_user_total where createtime = DATE_FORMAT(now(),"%Y-%m-%d") 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		order by yesterdayNew DESC
	</select>
	
	<select id="selectApkUserTotalTwo" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.ApkUserTotalVo">
		select aa.*,bb.* from 
		(select appid,appname, 
			SUM(actnum) as currDau,
			SUM(startnum) as currStart,
			SUM(addnum) as currNew,
			SUM(alladdnum) as todayUser,
			ROUND(AVG(duration),2) as utimes
		from umeng_apk_user_total 
		where tdate = #{end_date}
			<if test="appid != null and appid != ''">
				and appid in (${appid})
			</if>
		group by appid) aa 
		
		join 
		
		(select appid,appname, 
			SUM(actnum) as yesterdayDau,
			SUM(startnum) as yesterdayStart,
			SUM(addnum) as yesterdayNew,
			SUM(alladdnum) as yesterdayUser,
			ROUND(AVG(duration),2) as yesterdayUtimes
		from umeng_apk_user_total 
		where tdate = #{start_date}
			<if test="appid != null and appid != ''">
				and appid in (${appid})
			</if>
		group by appid) bb
		
		on aa.appid = bb.appid
	</select>
	<select id="selectApkUserTotalThree" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.ApkUserTotalVo">
		
		select tdate, 
			SUM(actnum) as currDau,
			SUM(startnum) as currStart,
			SUM(addnum) as currNew,
			SUM(alladdnum) as todayUser
		from umeng_allapk_user_total 
		where tdate BETWEEN #{start_date} AND #{end_date}
		group by tdate
		order by tdate asc
	</select>

	<select id="selectApkUserTotalTwoV2" parameterType="java.util.Map"
			resultType="com.wbgame.pojo.ApkUserTotalVo">
		select
		appid,appname appName,
		currDau,currStart,currNew,todayUser,utimes,
		yesterdayDau,yesterdayStart,yesterdayNew,yesterdayUser,yesterdayUtimes
		from
		(select aa.appid,aa.appname,currDau,currStart,currNew,todayUser,utimes,
		yesterdayDau,yesterdayStart,yesterdayNew,yesterdayUser,yesterdayUtimes from
		(select appid,appname,
		SUM(actnum) as currDau,
		SUM(startnum) as currStart,
		SUM(addnum) as currNew,
		SUM(alladdnum) as todayUser,
		ROUND(AVG(duration),2) as utimes
		from umeng_apk_user_total
		where tdate = #{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		group by appid) aa

		left join

		(select appid appid,appname appname,
		SUM(actnum) as yesterdayDau,
		SUM(startnum) as yesterdayStart,
		SUM(addnum) as yesterdayNew,
		SUM(alladdnum) as yesterdayUser,
		ROUND(AVG(duration),2) as yesterdayUtimes
		from umeng_app_user_total
		where tdate = #{start_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		group by appid) bb

		on aa.appid = bb.appid

		union

		select bb.appid,bb.appname,currDau,currStart,currNew,todayUser,utimes,
		yesterdayDau,yesterdayStart,yesterdayNew,yesterdayUser,yesterdayUtimes from
		(select appid,appname,
		SUM(actnum) as currDau,
		SUM(startnum) as currStart,
		SUM(addnum) as currNew,
		SUM(alladdnum) as todayUser,
		ROUND(AVG(duration),2) as utimes
		from umeng_apk_user_total
		where tdate = #{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		group by appid) aa

		right join

		(select appid,appname,
		SUM(actnum) as yesterdayDau,
		SUM(startnum) as yesterdayStart,
		SUM(addnum) as yesterdayNew,
		SUM(alladdnum) as yesterdayUser,
		ROUND(AVG(duration),2) as yesterdayUtimes
		from umeng_app_user_total
		where tdate = #{start_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		group by appid) bb

		on aa.appid = bb.appid) a

		<if test="order != null and order != ''">
			order by ${order}
		</if>
	</select>
	<select id="selectApkUserTotalThreeV2" parameterType="java.util.Map"
			resultType="com.wbgame.pojo.ApkUserTotalVo">
		select tdate,
		SUM(actnum) as currDau,
		SUM(startnum) as currStart,
		SUM(addnum) as currNew,
		SUM(alladdnum) as todayUser
		from umeng_all_user_total
		where tdate = #{start_date}
	</select>

	<select id="selectTodayApkUserTotalThreeV2" parameterType="java.util.Map"
			resultType="com.wbgame.pojo.ApkUserTotalVo">
		select tdate,
		SUM(actnum) as currDau,
		SUM(startnum) as currStart,
		SUM(addnum) as currNew,
		SUM(alladdnum) as todayUser
		from umeng_apk_user_total
		where tdate = #{end_date}
	</select>

	<insert id="insertDauUser" useGeneratedKeys="true" parameterType="java.util.List">
		insert into apk_user_total(appid,appName,currNew,yesterdayNew,currDau,yesterdayDau,currStart,
		yesterdayStart,yesterdayUser,todayUser,utimes,sumtimes,yesterdayUtimes,createtime,
		sevenDau,thirthDau,sevenTimes) values
		<foreach collection="list"  index="index" item="item" separator=",">
			(#{item.appid}, #{item.appName},#{item.currNew},#{item.yesterdayNew},#{item.currDau},#{item.yesterdayDau},
			#{item.currStart},#{item.yesterdayStart},#{item.yesterdayUser},#{item.todayUser},#{item.utimes},#{item.sumtimes},
			#{item.yesterdayUtimes},#{item.createtime},#{item.sevenDau},#{item.thirthDau},#{item.sevenTimes})
		</foreach>
		ON DUPLICATE KEY UPDATE
		appName=VALUES(appName)
		,currNew=VALUES(currNew)
		,yesterdayNew=VALUES(yesterdayNew)
		,currDau=VALUES(currDau)
		,yesterdayDau=VALUES(yesterdayDau)
		,currStart=VALUES(currStart)
		,yesterdayStart=VALUES(yesterdayStart)
		,yesterdayUser=VALUES(yesterdayUser)
		,todayUser=VALUES(todayUser)
		,utimes=VALUES(utimes)
		,sumtimes=VALUES(sumtimes)
		,yesterdayUtimes=VALUES(yesterdayUtimes)
		,sevenDau=VALUES(sevenDau)
		,thirthDau=VALUES(thirthDau)
		,sevenTimes=VALUES(sevenTimes)
	</insert>

	<insert id="insertDauUserDetail" useGeneratedKeys="true" parameterType="java.util.List">
		insert into apk_user_total_detail(appid,appName,currNew,yesterdayNew,currDau,yesterdayDau,currStart,
		yesterdayStart,yesterdayUser,todayUser,utimes,sumtimes,yesterdayUtimes,createtime) values
		<foreach collection="list"  index="index" item="item" separator=",">
			(#{item.appid}, #{item.appName},#{item.currNew},#{item.yesterdayNew},#{item.currDau},#{item.yesterdayDau},
			#{item.currStart},#{item.yesterdayStart}
			,#{item.yesterdayUser},#{item.todayUser},#{item.utimes},#{item.sumtimes},#{item.yesterdayUtimes},#{item.createtime})
		</foreach>
		ON DUPLICATE KEY UPDATE
		appName=VALUES(appName)
		,currNew=VALUES(currNew)
		,yesterdayNew=VALUES(yesterdayNew)
		,currDau=VALUES(currDau)
		,yesterdayDau=VALUES(yesterdayDau)
		,currStart=VALUES(currStart)
		,yesterdayStart=VALUES(yesterdayStart)
		,yesterdayUser=VALUES(yesterdayUser)
		,todayUser=VALUES(todayUser)
		,utimes=VALUES(utimes)
		,sumtimes=VALUES(sumtimes)
		,yesterdayUtimes=VALUES(yesterdayUtimes)
	</insert>

	<select id="selectApkUserNew" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkSelectNewVo">
		select wf.gameName as appName,bnt.by_date as dt,wc.channel as channel,wc.channelBuyFlag as channelType,bnt.by_priid as pid,
		wf.versionName as ver,sum(bnt.push_newcount) as newNum,wg.gameName as typeName,wf.platform as os
		from buyu_newcount_total bnt,dnwx_client.wbgui_formconfig wf,dnwx_client.wbgui_gametype wg,dnwx_client.wbgui_channel wc
		where 1=1
		<if test="appName != null and appName != ''">
			and wf.gameName like concat('%',#{appName},'%')
		</if>
		<if test="pid != null and pid != ''">
			and bnt.by_priid = #{pid}
		</if>
		<if test="appid != null and appid != ''">
			and wg.appid = #{appid}
		</if>
		<if test="channel != null and channel != ''">
			and wc.channel like concat('%',#{channel},'%')
		</if>
		<if test="os != null and os != ''">
			and wf.platform = #{os}
		</if>
		<if test="channelType != null and channelType != ''">
			and wc.channelBuyFlag = #{channelType}
		</if>
		and wf.pjId = bnt.by_priid and wf.typeName = wg.gameId and wf.channel = wc.id
		and bnt.by_date &gt;= #{beginDt} and bnt.by_date &lt;= #{endDt}
		group by bnt.by_date
		<if test="osgroup == '' and channelgroup == '' and productgroup == '' and channelTypegroup == '' and groupStr == ''">
			,bnt.by_priid
		</if>
		<if test="osgroup != null and osgroup != ''">
			,wf.platform
		</if>
		<if test="channelgroup != null and channelgroup != ''">
			,wc.channel
		</if>
		<if test="productgroup != null and productgroup != ''">
			,wg.gameName
		</if>
		<if test="channelTypegroup != null and channelTypegroup != ''">
			,wc.channelBuyFlag
		</if>
		order by sum(bnt.push_newcount) desc
	</select>

	<select id="selectApkUserNewGroup" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkSelectNewVo">
		select wf.gameName as appName,date_format(str_to_date(bnt.by_date, '%Y-%m'),"%Y-%m") as dt,wc.channel as channel,
		wc.channelBuyFlag as channelType,bnt.by_priid as pid,
		wf.versionName as ver,sum(bnt.push_newcount) as newNum,wg.gameName as typeName,wf.platform as os
		from buyu_newcount_total bnt, dnwx_client.wbgui_formconfig wf, dnwx_client.wbgui_gametype wg, dnwx_client.wbgui_channel wc
		where 1=1
		<if test="appName != null and appName != ''">
			and wf.gameName like concat('%',#{appName},'%')
		</if>
		<if test="pid != null and pid != ''">
			and bnt.by_priid = #{pid}
		</if>
		<if test="appid != null and appid != ''">
			and wg.appid = #{appid}
		</if>
		<if test="channel != null and channel != ''">
			and wc.channel like concat('%',#{channel},'%')
		</if>
		<if test="os != null and os != ''">
			and wf.platform = #{os}
		</if>
		<if test="channelType != null and channelType != ''">
			and wc.channelBuyFlag = #{channelType}
		</if>
		and wf.pjId = bnt.by_priid and wf.typeName = wg.gameId and wf.channel = wc.id
		and bnt.by_date &gt;= #{beginDt} and bnt.by_date &lt;= #{endDt}
		group by date_format(str_to_date(bnt.by_date, '%Y-%m'),"%Y-%m")
		<if test="osgroup != null and osgroup != ''">
			,wf.platform
		</if>
		<if test="channelgroup != null and channelgroup != ''">
			,wc.channel
		</if>
		<if test="productgroup != null and productgroup != ''">
			,wg.gameName
		</if>
		<if test="channelTypegroup != null and channelTypegroup != ''">
			,wc.channelBuyFlag
		</if>
		order by sum(bnt.push_newcount) desc
	</select>

	<select id="selectApkUserDauSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkSelectNewVo">
		select sum(aa.newNum) actTotal,sum(aa.actNum) addTotal,
			sum(aa.shieldNum) shieldTotal,CONCAT(truncate(sum(aa.shieldNum) / sum(aa.newNum) * 100,2),'%') shieldTotalRate
		from (<include refid="selectApkUserDau"/>) aa
		
	</select>
	
	<sql id="selectApkUserDau">
		select wf.gameName as appName,bnt.by_date as dt,wc.channel as channel,wc.channelBuyFlag as channelType,bnt.by_priid as pid,
			wf.versionName as ver,sum(bnt.by_newcount) as newNum,
			sum(bnt.unknow_count) as shieldNum,CONCAT(truncate(sum(bnt.unknow_count) / sum(bnt.by_newcount) * 100,2),'%') shieldRate,
			wg.gameName as typeName,wf.platform as os,sum(bynt.push_newcount) as actNum
		from alone_dau_total bnt
		LEFT JOIN dnwx_client.wbgui_formconfig wf ON wf.pjId = bnt.by_priid
		LEFT JOIN dnwx_client.wbgui_gametype wg ON wf.typeName = wg.gameId
		LEFT JOIN dnwx_client.wbgui_channel wc ON wf.channel = wc.id
		LEFT JOIN (select by_date,by_priid,SUM(push_newcount) push_newcount 
			from buyu_newcount_total where by_date BETWEEN #{beginDt} AND #{endDt} GROUP BY by_date,by_priid) bynt 
			ON bynt.by_priid = bnt.by_priid and bynt.by_date = bnt.by_date 
		where bnt.by_date BETWEEN #{beginDt} AND #{endDt} 
		<if test="appName != null and appName != ''">
			and wf.gameName like concat('%',#{appName},'%')
		</if>
		<if test="pid != null and pid != ''">
			and bnt.by_priid = #{pid}
		</if>
		<if test="appid != null and appid != ''">
			and wg.appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and wc.channel like concat('%',#{channel},'%')
		</if>
		<if test="os != null and os != ''">
			and wf.platform = #{os}
		</if>
		<if test="channelType != null and channelType != ''">
			and wc.channelBuyFlag = #{channelType}
		</if>
		
		group by bnt.by_date
		<if test="osgroup == '' and channelgroup == '' and productgroup == '' and channelTypegroup == '' and groupStr==''">
			,bnt.by_priid
		</if>
		<if test="osgroup != null and osgroup != ''">
			,wf.platform
		</if>
		<if test="channelgroup != null and channelgroup != ''">
			,wc.channel
		</if>
		<if test="productgroup != null and productgroup != ''">
			,wg.gameName
		</if>
		<if test="channelTypegroup != null and channelTypegroup != ''">
			,wc.channelBuyFlag
		</if>
		order by sum(bnt.by_newcount) desc
	</sql>
	<select id="selectApkUserDau" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkSelectNewVo">
		<include refid="selectApkUserDau"/>
	</select>
	

	<select id="selectApkUserDauGroupSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkSelectNewVo">
		select sum(aa.newNum) actTotal,sum(aa.actNum) addTotal,
			sum(aa.shieldNum) shieldTotal,CONCAT(truncate(sum(aa.shieldNum) / sum(aa.newNum) * 100,2),'%') shieldTotalRate
		from (<include refid="selectApkUserDauGroup"/>) aa
		
	</select>
	<sql id="selectApkUserDauGroup">
		select wf.gameName as appName,date_format(str_to_date(bnt.by_date, '%Y-%m'),"%Y-%m") as dt,wc.channel as channel,
			wc.channelBuyFlag as channelType,bnt.by_priid as pid,
			wf.versionName as ver,sum(bnt.by_newcount) as newNum,
			sum(bnt.unknow_count) as shieldNum,CONCAT(truncate(sum(bnt.unknow_count) / sum(bnt.by_newcount) * 100,2),'%') shieldRate,
			wg.gameName as typeName,wf.platform as os,sum(bynt.push_newcount) as actNum
		from alone_dau_total bnt
		LEFT JOIN dnwx_client.wbgui_formconfig wf ON wf.pjId = bnt.by_priid
		LEFT JOIN dnwx_client.wbgui_gametype wg ON wf.typeName = wg.gameId
		LEFT JOIN dnwx_client.wbgui_channel wc ON wf.channel = wc.id
		LEFT JOIN (select by_date,by_priid,SUM(push_newcount) push_newcount 
			from buyu_newcount_total where by_date BETWEEN #{beginDt} AND #{endDt} GROUP BY by_date,by_priid) bynt 
			ON bynt.by_priid = bnt.by_priid and bynt.by_date = bnt.by_date 
		where 1=1
		<if test="appName != null and appName != ''">
			and wf.gameName like concat('%',#{appName},'%')
		</if>
		<if test="pid != null and pid != ''">
			and bnt.by_priid = #{pid}
		</if>
		<if test="appid != null and appid != ''">
			and wg.appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and wc.channel like concat('%',#{channel},'%')
		</if>
		<if test="os != null and os != ''">
			and wf.platform = #{os}
		</if>
		<if test="channelType != null and channelType != ''">
			and wc.channelBuyFlag = #{channelType}
		</if>
		and bnt.by_date BETWEEN #{beginDt} AND #{endDt}
		group by date_format(str_to_date(bnt.by_date, '%Y-%m'),"%Y-%m")
		<if test="osgroup != null and osgroup != ''">
			,wf.platform
		</if>
		<if test="channelgroup != null and channelgroup != ''">
			,wc.channel
		</if>
		<if test="productgroup != null and productgroup != ''">
			,wg.gameName
		</if>
		<if test="channelTypegroup != null and channelTypegroup != ''">
			,wc.channelBuyFlag
		</if>
		order by sum(bnt.by_newcount) desc
	</sql>
	<select id="selectApkUserDauGroup" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkSelectNewVo">
		<include refid="selectApkUserDauGroup"/>
	</select>


	<select id="selectApkUserKeep" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkKeepUserVo">
		select pkt.mmdate as dt,wg.gameName as appName,sum(pkt.usernum) as newNum,sum(pkt.keep1) as one,
		sum(pkt.keep2) as two,sum(pkt.keep3) as three,sum(pkt.keep4) as four,sum(pkt.keep5) as five,sum(pkt.keep6) as six,
		sum(pkt.keep7) as seven,sum(pkt.keep14) as fourteen,sum(pkt.keep30) as thirty,pkt.projectid,
		
		(1+pp.keep1) keepnum1,
		(1+pp.keep1+pp.keep2) keepnum2,
		(1+pp.keep1+pp.keep2+pp.keep3) keepnum3,
		(1+pp.keep1+pp.keep2+pp.keep3+pp.keep4) keepnum4,
		(1+pp.keep1+pp.keep2+pp.keep3+pp.keep4+pp.keep5) keepnum5,
		(1+pp.keep1+pp.keep2+pp.keep3+pp.keep4+pp.keep5+pp.keep6) keepnum6,
		(1+pp.keep1+pp.keep2+pp.keep3+pp.keep4+pp.keep5+pp.keep6+pp.keep7) keepnum7,
		
		<foreach collection="array1" item="item" open="(1+" close=")" separator=" + ">
			pp.keep${item}
		</foreach> as keepnum14,
		<foreach collection="array2" item="item" open="(1+" close=")" separator=" + ">
			pp.keep${item}
		</foreach> as keepnum30
		
		from product_keep_total pkt
		LEFT JOIN dnwx_client.wbgui_formconfig wf ON pkt.projectid=wf.pjId
		LEFT JOIN dnwx_client.wbgui_gametype wg ON wf.typeName = wg.gameId
		LEFT JOIN product_keep_num_total pp ON pkt.projectid=pp.projectid and pkt.mmdate=pp.mmdate
		
		WHERE 1=1
		<if test="appName != null and appName != ''">
			and wg.gameName like concat('%',#{appName},'%')
		</if>
		<if test="appid != null and appid != ''">
			and wg.appid in (${appid})
		</if>
		<if test="pid != null and pid != ''">
			and pkt.projectid = #{pid}
		</if>
		and pkt.mmdate &gt;= #{beginDt} and pkt.mmdate &lt;= #{endDt}
		group by pkt.mmdate,wg.gameName
		order by pkt.mmdate desc
	</select>

	<select id="selectApkUserDetail" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkUserTotalVo">
		select appid,currNew,yesterdayNew,currDau,yesterdayDau,
		currStart,yesterdayStart,createtime
		from apk_user_total_detail
		where appid = #{appid}
		and date_format(createtime,"%Y-%m-%d") = #{time}
		GROUP BY createtime
		order by createtime
	</select>
	
	<select id="selectApkUserTrend" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkUserTrendVo">
		select appid,todayUser,sevenDau,thirthDau,sevenTimes,createtime
		from apk_user_total where appid = #{appid} and createtime = #{createtime}
	</select>

	<select id="selectApkUserTrendLine" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkUserTotalVo">
		select appid,appName,currDau,yesterdayDau,currNew,currStart,utimes,sumtimes,
		todayUser,createtime
		from apk_user_total
		where appid = #{appid}
		and createtime  &gt;= #{beginDt} and createtime &lt;= #{endDt}
		order by createtime
	</select>

	<select id="selectApkUserNewTrend" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkUserTotalVo">
		select aut.appid as appid,aut.currNew as currNew,aut.todayUser as todayUser,aut.createtime as createtime,akt.keep1 as keep1,
		akt.keep7 as keep7,akt.keep30 as keep30,aut.yesterdayNew as yesterdayNew
		from apk_user_total aut
		LEFT JOIN apk_keep_total akt ON akt.appid = aut.appid
		and aut.createtime = date_format(str_to_date(akt.createtime,'%Y%m%d'),'%Y-%m-%d')
		where aut.appid = #{appid}
		and aut.createtime  &gt;= #{beginDt} and aut.createtime &lt;= #{endDt}
		GROUP BY aut.createtime
	</select>

	<select id="selectApkUserVer" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkUserVerVo">
		select product_id as productid,ver,SUM(act_num) as actNum,SUM(add_num)
		as addNum from apk_product_stats
		where tdate = #{beginDt}
		and ver is not null
		and product_id = #{appid}
		GROUP BY ver
	</select>

	<select id="selectApkUserChannel" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkUserVerVo">
		select product_id as productid,channel,SUM(act_num) as actNum,SUM(add_num)
		as addNum from apk_product_stats
		where tdate = #{beginDt}
		and channel is not null
		and product_id = #{appid}
		GROUP BY channel
	</select>

	<select id="selectApkTotalChannel" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkUserVerVo">
		select product_id as productid,channel,sum(add_num)
		as todayUser from apk_product_stats
		where  channel is not null
		and product_id = #{appid}
		GROUP BY channel
	</select>

	<select id="selectApkTotalVer" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkUserVerVo">
		select product_id as productid,ver,sum(add_num)
		as todayUser from apk_product_stats
		where  ver is not null
		and product_id = #{appid}
		GROUP BY ver
	</select>

	<insert id="insertKeepNum" useGeneratedKeys="true" parameterType="java.util.List">
		insert into apk_keep_total(appid,keep1,keep7,keep30,totalnum,newnum,createtime)values
		<foreach collection="list"  index="index" item="item" separator=",">
			(#{item.appid},#{item.keep1},#{item.keep7},#{item.keep30},#{item.totalnum},#{item.newnum},#{item.createtime})
		</foreach>
	</insert>

	<update id="updateKeep7Num" useGeneratedKeys="true" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" open="" close="" separator=";">
		update apk_keep_total set
		keep7 = #{item.keep7}
		where appid = #{item.appid} and createtime = #{item.createtime}
		</foreach>
	</update>

	<update id="updateKeep30Num" useGeneratedKeys="true" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" open="" close="" separator=";">
			update apk_keep_total set
			keep30 = #{item.keep30}
			where appid = #{item.appid} and createtime = #{item.createtime}
		</foreach>
	</update>

	<insert id="insertChannelFeeAdv" parameterType="java.util.List">
		insert into adv_channel_fee_client(adv_dt,cha_name,cha_game,ad_pos,adv_fee,show_count,click_count,bus_nm)values
		<foreach collection="list"  index="index" item="item" separator=",">
		(#{item.adv_dt},#{item.cha_name},#{item.cha_game},#{item.ad_pos},#{item.adv_fee},
		 #{item.show_count},#{item.click_count},#{item.bus_nm})
		</foreach>
	</insert>

	<insert id="insertChannelFee"  parameterType="java.util.List">
		insert into adv_channel_fee(push_cha,pri_id,mmid,adv_dt,give_fee,new_count,two_retention,dau,get_fee,
		get_fee_expect,adv_fee,show_count,click_count,adv_type,os_type,cha_type,adv_fee_type)values
		<foreach collection="list"  index="index" item="item" separator=",">
		(#{item.push_cha},#{item.pri_id},#{item.mmid},#{item.adv_dt},#{item.give_fee},#{item.new_count},#{item.two_retention},#{item.dau},
		#{item.get_fee},#{item.get_fee_expect},#{item.adv_fee},#{item.show_count},#{item.click_count},#{item.adv_type},#{item.os_type},#{item.cha_type},#{item.adv_fee_type})
		</foreach>
	</insert>

	<select id="selectFeedBackConfig" parameterType="Map" resultType="com.wbgame.pojo.TouSuVo">
		select a.ts_id,a.ts_username,a.ts_phone,a.ts_answer,a.ts_reply,a.ts_stauts,
		date_format(a.datetime, '%Y-%m-%d %H:%i:%s') as datetime,a.product_type,
		a.ts_pmoney,a.ts_province,a.ts_qq from zjh_tousu_info a where 1=1
		<if test="timerequire != null and timerequire != ''">
			and a.datetime &gt;= #{beginDt} and a.datetime &lt;= #{endDt}
		</if>
		<if test="operator != null and operator != ''">
			and a.ts_username = #{operator}
		</if>
		<if test="apptype != null and apptype != ''">
			and a.product_type = #{apptype}
		</if>
		<if test="userid != null and userid != ''">
			and a.oper_userid = #{userid}
		</if>
		<if test="phone != null and phone != ''">
			and a.ts_phone = #{phone}
		</if>
		<if test="remarks != null and remarks != ''">
			and a.ts_answer like concat('%',#{remarks},'%')
		</if>
		<if test="qq != null and qq != ''">
			and a.ts_qq = #{qq}
		</if>
		order by a.datetime desc
	</select>

	<insert id="insertFeedBackConfig" parameterType="com.wbgame.pojo.TouSuVo">
		insert into zjh_tousu_info(ts_username,ts_phone,ts_answer,ts_reply,
		ts_stauts,datetime,oper_userid,product_type,ts_pmoney,ts_province,ts_qq) values
		(#{ts_username},#{ts_phone},#{ts_answer},#{ts_reply},#{ts_stauts},#{datetime},
		#{oper_userid},#{product_type},#{ts_pmoney},#{ts_province},#{ts_qq})
	</insert>

	<update id="updateFeedBackConfig" parameterType="com.wbgame.pojo.TouSuVo">
		update zjh_tousu_info set ts_username=#{ts_username},ts_phone=#{ts_phone},ts_answer=#{ts_answer},
		ts_reply=#{ts_reply},ts_stauts=#{ts_stauts},datetime=#{datetime},product_type=#{product_type},
		ts_pmoney=#{ts_pmoney},ts_province=#{ts_province},ts_qq=#{ts_qq} where ts_id = #{ts_id}
	</update>

	<delete id="removeFeedBackConfig" parameterType="String">
		delete from zjh_tousu_info where ts_id = #{ts_id}
	</delete>

	<select id="selectButtonInfo" parameterType="Map" resultType="com.wbgame.pojo.ApkButtonVo">
		select * from ttxfk_button_config where 1=1
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		order by id desc
	</select>

	<update id="updateButtonInfo" parameterType="com.wbgame.pojo.ApkButtonVo">
		update ttxfk_button_config set pid = #{pid},button_order = #{button_order},
		button_lock = #{button_lock} where id = #{id}
	</update>

	<insert id="insertButtonInfo" parameterType="com.wbgame.pojo.ApkButtonVo">
		insert into ttxfk_button_config(pid,button_order,button_lock)values(#{pid},#{button_order},#{button_lock})
	</insert>
	
	 <insert id="insertUserTalkBack" parameterType="com.wbgame.pojo.UserTalkBack">
        insert into user_talk_back(msg,contant,imgurl,appid,pid,lsn,creattime)
        values(#{msg},#{contant},#{imgurl},#{appid},#{pid},#{lsn},now())
    </insert>

	<select id="selectSystemConfig" resultType="com.wbgame.pojo.ApkSystemConfigVo">
		select * from np_param
	</select>

	<update id="updateSystemConfig" parameterType="com.wbgame.pojo.ApkSystemConfigVo">
		update np_param set
		<if test="LOTTERYPRJIDS != null and LOTTERYPRJIDS != ''">
			LOTTERYPRJIDS = #{LOTTERYPRJIDS}
		</if>
		<if test="CHECKCODE != null and CHECKCODE != ''">
			CHECKCODE = #{CHECKCODE}
		</if>
		<if test="AUDIT_PRJIDS != null and AUDIT_PRJIDS != ''">
			AUDIT_PRJIDS = #{AUDIT_PRJIDS}
		</if>
		<if test="IS_ASSETS != null and IS_ASSETS != ''">
			IS_ASSETS = #{IS_ASSETS}
		</if>
		<if test="SHARE_URL != null and SHARE_URL != ''">
			SHARE_URL = #{SHARE_URL}
		</if>
		<if test="NO_SZCALLS_PROJECTIDS != null and NO_SZCALLS_PROJECTIDS != ''">
			NO_SZCALLS_PROJECTIDS = #{NO_SZCALLS_PROJECTIDS}
		</if>
		<if test="NOFEE_PROJECT != null and NOFEE_PROJECT != ''">
			NOFEE_PROJECT = #{NOFEE_PROJECT}
		</if>
		where ID = #{ID}
	</update>

	<select id="selectDhmInfo" parameterType="Map" resultType="com.wbgame.pojo.ProjectVo">
		select dhmid,dvalue,statu,createtime, create_user createUser from GAME_DHM_INDEX where 1=1
		<if test="dhmid != null and dhmid != ''">
			and DHMID = #{dhmid}
		</if>
		<if test="dhvalue != null and dhvalue != ''">
			and DVALUE = #{dhvalue}
		</if>
		<if test="statu != null and statu != ''">
			and STATU = #{statu}
		</if>
		and ISFP = '1'
		order by createtime desc
	</select>

	<insert id="insertDhmInfo" parameterType="Map">
		insert into GAME_DHM_INDEX(DHMID,DVALUE,STATU,ISFP,CREATETIME,create_user)
		values(#{dhm,jdbcType=VARCHAR},#{addvalue,jdbcType=VARCHAR},'0','1',#{dateString,jdbcType=VARCHAR},#{createUser})
	</insert>
	<insert id="insertDhmInfoList" parameterType="java.util.List">
		insert into GAME_DHM_INDEX(DHMID,DVALUE,STATU,ISFP,CREATETIME, create_user)
		
		values
		<foreach collection="list" item="item" separator=",">
			(#{item.dhm},#{item.addvalue},'0','1',now(), #{item.createUser})
		</foreach>
		
	</insert>

	<select id="selectAppUpdateInfo" parameterType="Map" resultType="com.wbgame.pojo.NpPacUpdateVo">
		select
		id,
		pacver,
		pacname,
		verinfo,
		pac_url,
		prjid,
		flag,
		statu,
		nocity,
		seqnum,
		imei,
		tips,
		progresstype,
		uptype,
		upvalue,
		appName,
		cha,
		appid,
		downType,
		update_owner,
		DATE_FORMAT(updateTime,'%Y-%m-%d %H:%i:%s') updateStr,
		create_user,
		DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time
		from np_pac_update_new where 1=1
		<if test="pid != null and pid != ''">
			and prjid = #{pid}
		</if>
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="pacver != null and pacver != ''">
			and pacver = #{pacver}
		</if>
		<if test="verinfo != null and verinfo != ''">
			and verinfo = #{verinfo}
		</if>
		<if test="cha != null and cha != ''">
			and cha in (${cha})
		</if>
		order by id desc
	</select>

	<insert id="insertAppUpdateInfo" parameterType="com.wbgame.pojo.NpPacUpdateVo">
		insert into np_pac_update_new(pacver,pacname,verinfo,pac_url,prjid,flag,statu,nocity,seqnum,
		imei,tips,progresstype,uptype,upvalue,appid,appName,cha,downType,create_user,create_time)
		values(#{pacver},#{pacname},#{verinfo},#{pac_url},#{prjid},#{flag},#{statu},#{nocity},#{seqnum},
		#{imei},#{tips},#{progresstype},#{uptype},#{upvalue},#{appid},#{appName},#{cha},#{downType},#{create_user},now())
	</insert>

	<update id="updateAppUpdateInfo" parameterType="com.wbgame.pojo.NpPacUpdateVo">
		update np_pac_update_new set pacname=#{pacname},verinfo=#{verinfo},pac_url=#{pac_url},flag=#{flag},
			statu=#{statu},nocity=#{nocity},seqnum=#{seqnum},imei=#{imei},tips=#{tips},
			progresstype=#{progresstype},uptype=#{uptype},upvalue=#{upvalue},appid=#{appid},appName=#{appName},cha=#{cha},
			update_owner=#{update_owner},updateTime=#{updateTime,jdbcType=TIMESTAMP},downType=#{downType}
		where id = #{id}
	</update>

	<delete id="deleteAppUpdateInfo" parameterType="Map">
		delete from np_pac_update_new where id = #{id}
	</delete>

	<select id="selectAppListInfo" parameterType="Map" resultType="com.wbgame.pojo.AppInfoVo">
		select a.id,a.channel_id,a.app_id,a.app_name,date_format(a.create_time, '%Y-%m-%d %H:%i:%s') create_time,
		a.sync_umeng,umeng_key,b.channel_name,a.app_category,a.umeng_account,a.find_vals,a.os_type,a.xyx_id,a.reyun_key,a.bus_category,a.two_app_category,a.cp
		from app_info a,app_channel b where a.channel_id = b.channel_id
		<if test="appName != null and appName != ''">
			and a.app_name like concat('%',#{appName},'%')
		</if>
		<if test="channelId != null and channelId != ''">
			and a.channel_id = #{channelId}
		</if>
		<if test="appid != null and appid != ''">
			and a.id = #{appid}
		</if>
		<if test="appCategory != null and appCategory != ''">
			and a.app_category in (${appCategory})
		</if>
		<if test="findVal != null and findVal != ''">
			and a.find_vals like concat('%',#{findVal},'%')
		</if>
		<if test="osType != null and osType != ''">
			and a.os_type = #{osType}
		</if>
		<if test="apps != null and apps != ''">
			and a.id in ${apps}
		</if>
		<if test="reyun_key != null and reyun_key != ''">
			and a.reyun_key = #{reyun_key}
		</if>
		<if test="umeng_account != null and umeng_account != ''">
			and a.umeng_account = #{umeng_account}
		</if>
		<if test="bus_category != null and bus_category != ''">
			and a.bus_category = #{bus_category}
		</if>
		<if test="two_app_category != null and two_app_category != ''">
			and a.two_app_category = #{two_app_category} 
		</if>
		order by a.create_time desc
	</select>

	<select id="exportAppListInfo" parameterType="Map" resultType="Map">
		select a.id,a.app_name,date_format(a.create_time, '%Y-%m-%d %H:%i:%s') create_time,
		c.name as app_category,(case a.os_type when 2 then 'IOS' when 1 then '安卓' when 3 then 'google' when 4 then '小游戏' end) os_type
		from app_info a left join app_category c on a.app_category = c.id,
		     app_channel b
		where a.channel_id = b.channel_id
		<if test="appName != null and appName != ''">
			and a.app_name like concat('%',#{appName},'%')
		</if>
		<if test="channelId != null and channelId != ''">
			and a.channel_id = #{channelId}
		</if>
		<if test="appid != null and appid != ''">
			and a.id = #{appid}
		</if>
		<if test="appCategory != null and appCategory != ''">
			and a.app_category = #{appCategory}
		</if>
		<if test="findVal != null and findVal != ''">
			and a.find_vals like concat('%',#{findVal},'%')
		</if>
		<if test="osType != null and osType != ''">
			and a.os_type = #{osType}
		</if>
		<if test="apps != null and apps != ''">
			and a.id in ${apps}
		</if>
		order by a.create_time desc
	</select>

	<insert id="insertAppListInfo" parameterType="Map" 
				useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into app_info(channel_id,app_id,app_name,create_time,sync_umeng,umeng_key,app_category,umeng_account,find_vals,os_type,xyx_id,reyun_key,bus_category,two_app_category,cp)
		values(#{channel_id},#{app_id},#{app_name},now(),#{sync_umeng},#{umeng_key},#{app_category},#{umeng_account},#{find_vals},#{os_type},#{xyx_id},#{reyun_key},#{bus_category},#{two_app_category},#{cp})
	</insert>

	<update id="updateAppListInfo" parameterType="Map">
		update app_info set channel_id = #{channel_id}, app_name = #{app_name},
			sync_umeng = #{sync_umeng}, umeng_key = #{umeng_key} ,app_category = #{app_category},
			umeng_account = #{umeng_account},find_vals = #{find_vals},os_type = #{os_type},xyx_id = #{xyx_id},reyun_key = #{reyun_key},
			bus_category = #{bus_category},two_app_category = #{two_app_category},cp = #{cp}
		where id = #{id}
	</update>

	<delete id="deleteAppListInfo" parameterType="String">
		delete from app_info where id in (#{ids})
	</delete>

	<select id="selectChannelInfo" resultType="com.wbgame.pojo.SelectStortVo">
		select channel_id op_value,channel_name op_text from app_channel
	</select>

	<select id="selectUmengEnter" parameterType="Map" resultType="com.wbgame.pojo.UengIncomeShowVo">
		select * from umeng_income_show where 1=1
		<if test="appname != null and appname != ''">
			and appname like concat('%',#{appname},'%')
		</if>
		<if test="cname != null and cname != ''">
			and cname like concat('%',#{cname},'%')
		</if>
		<if test="push_cha != null and push_cha != ''">
			and push_cha like concat('%',#{push_cha},'%')
		</if>
		<if test="ctype != null and ctype != ''">
			and ctype = #{ctype}
		</if>
		order by appname asc
	</select>

	<insert id="insertUmengEnter" parameterType="com.wbgame.pojo.UengIncomeShowVo">
		insert into umeng_income_show(appname,appkey,cname,cid,push_cha,ctype) 
		values(#{appname},#{appkey},#{cname},#{cid},#{push_cha},#{ctype})
	</insert>
	
	<update id="updateUmengEnter" parameterType="com.wbgame.pojo.UengIncomeShowVo">
		update umeng_income_show set push_cha=#{push_cha} 
		where appkey=#{appkey} and cid=#{cid} and ctype=#{ctype}
	</update>

	<delete id="deleteUmengEnter" parameterType="com.wbgame.pojo.UengIncomeShowVo">
		delete from umeng_income_show 
		where appkey=#{appkey} and cid=#{cid} and push_cha=#{push_cha} and ctype=#{ctype}
	</delete>

	<select id="selectUmengList" resultType="com.wbgame.pojo.UmengTreeListVo">
		select * from umeng_tree_list where parent_id = 0
	</select>

	<select id="selectUmengListTwo" resultType="com.wbgame.pojo.UmengTreeListVo">
		select * from umeng_tree_list where parent_id != 0
	</select>

	<select id="selectUmengChannelTotal" parameterType="Map" resultType="com.wbgame.pojo.UmengChannelTotalVo">
		select aa.*,bb.ad_revenue adv_fee from
		(select * from  umeng_channel_total where
		tdate BETWEEN #{start_date} AND #{end_date}
		<if test="all == 1">
			and appkey in (select DISTINCT appkey from umeng_income_show where ctype = 2)
		</if>
		) aa
		left join app_info zz on aa.appkey = zz.umeng_key
		
		LEFT JOIN dn_game_cha_revenue_total bb
		ON aa.tdate = bb.tdate AND aa.appid = bb.appid AND aa.cname = bb.cha_id

		where 1=1
		<if test="appkey != null and appkey != ''">
			and aa.appkey in (${appkey})
		</if>
		<if test="channel != null and channel != ''">
			and aa.cname in (${channel})
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by aa.tdate asc ,addnum desc
			</otherwise>
		</choose>
	</select>
	
	<select id="selectUmengkeys" resultType="Map"> 
	select  appkey,appname from umeng_income_show where ctype = 2 GROUP BY appkey
	</select>

	<select id="selectUmengChannelTotalGroup" parameterType="Map" resultType="com.wbgame.pojo.UmengChannelTotalVo">
		select
			aa.tdate,
			aa.appid,
			aa.cname channel,
			bb.cha_media,
			ww.`name` as app_category,
			zz.app_name appname,
			SUM(aa.actnum) actnum,SUM(aa.addnum) addnum,
			SUM(aa.launch) launch,
			ROUND(SUM(bb.ad_revenue),2) adv_fee,
			ROUND(AVG(time_to_sec(daily_duration)),0) daily_duration,
			ROUND(AVG(time_to_sec(daily_per_duration)),0) daily_per_duration,
			ROUND(AVG(aa.keep_num1),2) keep_num1,
			ROUND(AVG(aa.keep_num2),2) keep_num2,
			ROUND(AVG(aa.keep_num3),2) keep_num3,
			ROUND(AVG(aa.keep_num4),2) keep_num4,
			ROUND(AVG(aa.keep_num5),2) keep_num5,
			ROUND(AVG(aa.keep_num6),2) keep_num6,
			ROUND(AVG(aa.keep_num7),2) keep_num7,
			ROUND(AVG(aa.keep_num14),2) keep_num14,
			ROUND(AVG(aa.keep_num30),2) keep_num30
		from 
			(select * from  umeng_channel_total where
			tdate BETWEEN #{start_date} AND #{end_date}
			<if test="all == 1">
				and appkey in (select DISTINCT appkey from umeng_income_show where ctype = 2)
			</if>
			) aa
		left join app_info zz on aa.appkey = zz.umeng_key
		left join app_category ww on ww.id = zz.`app_category`
		
		LEFT JOIN dn_game_cha_revenue_total bb
		ON aa.tdate = bb.tdate AND aa.appid = bb.appid AND aa.cname = bb.cha_id
		
		where 1=1

		<if test="appkey != null and appkey != ''">
			and aa.appkey in (${appkey})
		</if>
		<if test="channel != null and channel != ''">
			and aa.cname in (${channel})
		</if>
		<if test="app_category != null and app_category != ''">
			and ww.id in (${app_category})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and bb.cha_media in (${cha_media})
		</if>

		<if test="group != null and group != ''">
			group by ${group}
		</if>

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by aa.tdate asc ,addnum desc
			</otherwise>
		</choose>

	</select>
	<select id="selectUmengChannelTotalGroup2" parameterType="Map" resultType="com.wbgame.pojo.UmengChannelTotalVo">
		select
			aa.tdate,
			aa.appid,
			aa.install_channel channel,
			bb.cha_media,
			ww.`name` as app_category,
			zz.app_name appname,
			SUM(aa.act_num) actnum,SUM(aa.add_num) addnum,
			SUM(aa.start_num) launch,
			ROUND(SUM(bb.ad_revenue),2) adv_fee,
			ROUND(AVG(time_to_sec(duration)),0) daily_duration,
			ROUND(AVG(time_to_sec(daily_per_duration)),0) daily_per_duration,
			ROUND(AVG(aa.product_keep1),2) keep_num1,
			ROUND(AVG(aa.product_keep2),2) keep_num2,
			ROUND(AVG(aa.product_keep3),2) keep_num3,
			ROUND(AVG(aa.product_keep4),2) keep_num4,
			ROUND(AVG(aa.product_keep5),2) keep_num5,
			ROUND(AVG(aa.product_keep6),2) keep_num6,
			ROUND(AVG(aa.product_keep7),2) keep_num7,
			ROUND(AVG(aa.product_keep14),2) keep_num14,
			ROUND(AVG(aa.product_keep30),2) keep_num30
		from 
			(select * from  umeng_user_channel_total where
			tdate BETWEEN #{start_date} AND #{end_date}
			<if test="all == 1">
				and app_key in (select DISTINCT appkey from umeng_income_show where ctype = 2)
			</if>
			) aa
		left join app_info zz on aa.app_key = zz.umeng_key
		left join app_category ww on ww.id = zz.`app_category`
		
		LEFT JOIN dn_game_cha_revenue_total bb
		ON aa.tdate = bb.tdate AND aa.appid = bb.appid AND aa.install_channel = bb.cha_id
		
		where 1=1

		<if test="appkey != null and appkey != ''">
			and aa.app_key in (${appkey})
		</if>
		<if test="channel != null and channel != ''">
			and aa.install_channel in (${channel})
		</if>
		<if test="app_category != null and app_category != ''">
			and ww.id in (${app_category})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and bb.cha_media in (${cha_media})
		</if>

		<if test="group != null and group != ''">
			group by ${group}
		</if>

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by aa.tdate asc ,addnum desc
			</otherwise>
		</choose>
	</select>

	<select id="selectUmengChannelTotalNew" parameterType="Map" resultType="com.wbgame.pojo.UmengChannelTotalVo">
		select
		aa.tdate,
		aa.appid,
		aa.install_channel channel,
		bb.cha_media,
		ww.`name` as app_category,
		zz.app_name appname,
		SUM(aa.act_num) actnum,SUM(aa.add_num) addnum,
		SUM(aa.start_num) launch,
		ROUND(SUM(bb.ad_revenue),2) adv_fee,
		ROUND(AVG(time_to_sec(aa.duration)),0) daily_duration,
		ROUND(AVG(time_to_sec(aa.daily_per_duration)),0) daily_per_duration,
		ROUND(AVG(aa.product_keep1),2) keep_num1,
		ROUND(AVG(aa.product_keep2),2) keep_num2,
		ROUND(AVG(aa.product_keep3),2) keep_num3,
		ROUND(AVG(aa.product_keep4),2) keep_num4,
		ROUND(AVG(aa.product_keep5),2) keep_num5,
		ROUND(AVG(aa.product_keep6),2) keep_num6,
		ROUND(AVG(aa.product_keep7),2) keep_num7,
		ROUND(AVG(aa.product_keep14),2) keep_num14,
		ROUND(AVG(aa.product_keep30),2) keep_num30
		from
		(
		select * from umeng_user_channel_total where tdate BETWEEN #{start_date} AND #{end_date}
		) aa
		left join app_info zz on aa.app_key = zz.umeng_key
		left join app_category ww on ww.id = zz.`app_category`
		LEFT JOIN dn_game_cha_revenue_total bb
		ON aa.tdate = bb.tdate AND aa.appid = bb.appid AND aa.install_channel = bb.cha_id
		where 1=1
		<if test="appid != null and appid != ''">
			and aa.appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and aa.install_channel in (${channel})
		</if>
		<if test="app_category != null and app_category != ''">
			and ww.id in (${app_category})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and bb.cha_media in (${cha_media})
		</if>

		<if test="group != null and group != ''">
			group by ${group}
		</if>

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by aa.tdate asc ,addnum desc
			</otherwise>
		</choose>

	</select>

	<select id="selectUmengChannelReport" parameterType="Map" resultType="com.wbgame.pojo.UmengChannelTotalVo">
		select aa.appname,aa.appkey,aa.cname,aa.cid,
		truncate(SUM(aa.addnum),2) as addnum,
		truncate(SUM(cc.adv_fee),2) as adv_fee,
		truncate(SUM(aa.actnum)/7,2) as actnum,
		truncate(SUM(aa.keep_num1)/7,2) as keep_num1
		from (select * from  umeng_channel_total where
		tdate BETWEEN #{date1} AND #{date2} ) aa
		join (select * from umeng_income_show where ctype = 1) bb
		on aa.appkey = bb.appkey and aa.cid = bb.cid
		left join umeng_push_cha cc
		on aa.tdate = cc.tdate and bb.push_cha = cc.push_cha where 1=1
		<if test="app != null and app != ''">
			and aa.appkey in (#{app})
		</if>
		<if test="cname != null and cname != ''">
			and aa.cname in (${cname})
		</if>
		group by aa.appkey,aa.cid
		order by aa.cname,aa.appname
	</select>

	<select id="selectUmengChannelReportNew" parameterType="Map" resultType="com.wbgame.pojo.UmengChannelTotalVo">
		select aa.appname,aa.appkey,aa.cname,aa.cid,
		truncate(SUM(aa.addnum),2) as addnum,
		truncate(SUM(cc.adv_fee),2) as adv_fee,
		truncate(SUM(aa.actnum)/7,2) as actnum,
		truncate(SUM(aa.keep_num1)/7,2) as keep_num1
		from (select * from  umeng_channel_total where
		tdate BETWEEN #{date1} AND #{date2} ) aa
		join (select * from umeng_income_show where ctype = 1) bb
		on aa.appkey = bb.appkey and aa.cid = bb.cid
		left join umeng_push_cha cc
		on aa.tdate = cc.tdate and bb.push_cha = cc.push_cha where 1=1
		<if test="appid != null and appid != ''">
			and aa.appid in (#{appid})
		</if>
		<if test="cname != null and cname != ''">
			and aa.cname in (${cname})
		</if>
		group by aa.appkey,aa.cid
		order by aa.cname,aa.appname
	</select>

	<select id="selectQpayInfo" parameterType="Map" resultType="com.wbgame.pojo.QPayVo">
		select * from NP_PARAM_RULE_GIFT where 1=1
		<if test="prjid != null and prjid != ''">
			and PRJMID = #{prjid}
		</if>
	</select>

	<insert id="insertQpayInfo" parameterType="com.wbgame.pojo.QPayVo">
		insert into NP_PARAM_RULE_GIFT(PRJMID,GIFT_STR,PRODUCTID,CARRIER_TYPE,SEQ_ID,REMARK_INFO,SFCITYIDS,MMID)values
		(#{PRJMID},#{GIFT_STR},#{PRODUCTID},#{CARRIER_TYPE},#{SEQ_ID},#{REMARK_INFO},#{SFCITYIDS},#{MMID})
	</insert>

	<update id="updateQpayInfo" parameterType="com.wbgame.pojo.QPayVo">
		update NP_PARAM_RULE_GIFT set GIFT_STR = #{GIFT_STR},PRODUCTID = #{PRODUCTID},CARRIER_TYPE = #{CARRIER_TYPE},
		SEQ_ID = #{SEQ_ID},REMARK_INFO = #{REMARK_INFO},SFCITYIDS = #{SFCITYIDS},MMID = #{MMID} where PRJMID = #{PRJMID}
	</update>

	<delete id="deleteQpayInfo" parameterType="String">
		delete from  NP_PARAM_RULE_GIFT where PRJMID = #{id}
	</delete>

	<select id="selectQpayRuleInfo" parameterType="Map" resultType="com.wbgame.pojo.QpayRuleVo">
		select * from NP_PARAM_RULE_FULL where 1=1
		<if test="prjid != null and prjid != ''">
			and PRJMID = #{prjid}
		</if>
		<if test="productid != null and productid != ''">
			and PRODUCTID = #{productid}
		</if>

	</select>

	<insert id="insertQpayRuleInfo" parameterType="com.wbgame.pojo.QpayRuleVo">
		insert into NP_PARAM_RULE_FULL(PRJMID,MMID,PAY_TYPE,PRODUCTID,
		CARRIER_TYPE,SFCITYIDS,SEQ_ID,REMARK_INFO,NOPAY_PRJIDS,FEE_DATA_URL)
		values(#{PRJMID},#{MMID},#{PAY_TYPE},#{PRODUCTID},
		#{CARRIER_TYPE},#{SFCITYIDS},#{SEQ_ID},#{REMARK_INFO},#{NOPAY_PRJIDS},#{FEE_DATA_URL})
	</insert>

	<update id="updateQpayRuleInfo" parameterType="com.wbgame.pojo.QpayRuleVo">
		update NP_PARAM_RULE_FULL set MMID = #{MMID},PAY_TYPE = #{PAY_TYPE},PRODUCTID = #{PRODUCTID},
		CARRIER_TYPE = #{CARRIER_TYPE},SFCITYIDS = #{SFCITYIDS},SEQ_ID = #{SEQ_ID},REMARK_INFO = #{REMARK_INFO},
		NOPAY_PRJIDS = #{NOPAY_PRJIDS},FEE_DATA_URL = #{FEE_DATA_URL} where PRJMID = #{PRJMID}
	</update>

	<delete id="deleteQpayRuleInfo" parameterType="String">
		delete from  NP_PARAM_RULE_FULL where PRJMID = #{id}
	</delete>

	<select id="selectFilterDeviceBlack" parameterType="Map" resultType="com.wbgame.pojo.NpPostVo">
		select imei as param1,'${type}' param2
		,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createStr,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') updateStr,owner
		from np_post_black where 1=1
		<if test="key != null and key != ''">
			and imei = #{key}
		</if>
	</select>

	<select id="selectFilterDeviceWhite" parameterType="Map" resultType="com.wbgame.pojo.NpPostVo">
		select imei as param1,
		`c_num` cNum,
		`holder`,
		`c_type` cType,
		`sponsor` ,
		'${type}' param2
		,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createStr,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') updateStr,owner
		from np_post_white where 1=1
		<if test="key != null and key != ''">
			and imei = #{key}
		</if>
	</select>

	<select id="selectFilterDeviceIp" parameterType="Map" resultType="com.wbgame.pojo.NpPostVo">
		select ip as param1,'${type}' param2,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createStr,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') updateStr,owner
		from np_post_ip_black where 1=1
		<if test="key != null and key != ''">
			and ip = #{key}
		</if>
	</select>

	<insert id="insertFilterDeviceBlack" parameterType="Map">
		replace into np_post_black(imei,create_time,update_time,owner) values(#{key},#{create_time},#{update_time},#{owner})
	</insert>

	<insert id="insertFilterDeviceWhite" parameterType="Map">
		INSERT INTO `np_post_white` (`imei`, `c_num`, `holder`, `c_type`, `sponsor`,create_time,update_time,owner)
		VALUES (#{key}, #{cNum}, #{holder},#{cType} ,#{sponsor},#{create_time},#{update_time},#{owner} )
	</insert>
	
	<update id="updeteFilterDeviceWhite" parameterType="Map">
		UPDATE `np_post_white` 
		SET 
		 `c_num`=#{cNum}, `holder`= #{holder}, `c_type`= #{cType},`sponsor`= #{sponsor} ,update_time = #{update_time},owner = #{owner}
		 WHERE `imei`=#{key}
	</update>

	<insert id="insertFilterDeviceIp" parameterType="Map">
		insert into np_post_ip_black(ip,create_time,update_time,owner) values(#{key},#{create_time},#{update_time},#{owner})
	</insert>

	<delete id="deleteFilterDeviceBlack" parameterType="Map">
		delete from np_post_black where imei = #{key}
	</delete>

	<delete id="deleteFilterDeviceWhite" parameterType="Map">
		delete from np_post_white where imei = #{key}
	</delete>

	<delete id="deleteFilterDeviceIp" parameterType="Map">
		delete from np_post_ip_black where ip = #{key}
	</delete>

	<select id="selectApiAdConfig" parameterType="Map" resultType="com.wbgame.pojo.AdPrjVo">
		select prjmid,ad_sid,ad_pos,occur_min,occur_max,statu,
		DATE_FORMAT(createtime,'%Y-%m-%d %H:%i:%s') createtime,
		(occur_max-occur_min+1)||'%' per,cityids,qz_lelve,scale_value from AD_CONFIGURE t where 1=1
		<if test="ad_sid != null and ad_sid != ''">
			and ad_sid like concat('%',#{ad_sid},'%')
		</if>
		<if test="prjid != null and prjid != ''">
			and prjmid like concat('%',#{prjid},'%')
		</if>
		<if test="status != null and status != ''">
			and statu = #{status}
		</if>
	</select>

	<insert id="insertApiAdConfig" parameterType="com.wbgame.pojo.AdPrjVo">
		insert into AD_CONFIGURE(prjmid,ad_sid,ad_pos,occur_min,occur_max,statu,createtime,cityids,qz_lelve,scale_value) values
		(#{prjmid},#{ad_sid},#{ad_pos},#{occur_min},#{occur_max},#{statu},now(),#{cityids},#{qz_lelve},#{scale_value})
	</insert>

	<update id="updateApiAdConfig" parameterType="com.wbgame.pojo.AdPrjVo">
		update AD_CONFIGURE set ad_pos=#{ad_pos},occur_min=#{occur_min},occur_max=#{occur_max},
		statu=#{statu},qz_lelve=#{qz_lelve},cityids=#{cityids},scale_value=#{scale_value} where prjmid=#{prjmid} and ad_sid=#{ad_sid}
	</update>

	<delete id="deleteApiAdConfig" parameterType="Map">
		delete from AD_CONFIGURE where PRJMID = #{prijmid} and AD_SID = #{ad_sid}
	</delete>

	<update id="openApiAdConfig" parameterType="Map">
		update AD_CONFIGURE set statu = #{statu} where PRJMID = #{prijmid} and AD_SID = #{ad_sid}
	</update>

	<select id="selectNewAdConfigBySid" parameterType="List" resultType="com.wbgame.pojo.ExtendVo">
		select agent,appid,appkey,type,code,limitname,refreshinterval,ad_sid,textinfo,adsize,
		DATE_FORMAT(creatdate,'%Y-%m-%d %H:%i:%s') creatdate from EXTEND_ADINFO t  where 1=1 and ad_sid in (${str})
	</select>

	<select id="selectNewAdConfig" parameterType="Map" resultType="com.wbgame.pojo.ExtendVo">
		select agent,appid,appkey,type,code,limitname,refreshinterval,ad_sid,textinfo,adsize,
		DATE_FORMAT(creatdate,'%Y-%m-%d %H:%i:%s') creatdate from EXTEND_ADINFO t  where 1=1
		<if test="ad_sid != null and ad_sid != ''">
			and ad_sid like concat('%',#{ad_sid},'%')
		</if>
		<if test="code != null and code != ''">
			and code like concat('%',#{code},'%')
		</if>
		<if test="app_id != null and app_id != ''">
			and appid like concat('%',#{app_id},'%')
		</if>
		<if test="tj_agent != null and tj_agent != ''">
			and lower(agent) like concat('%',#{tj_agent},'%')
		</if>
	</select>

	<insert id="insertNewAdConfig" parameterType="com.wbgame.pojo.ExtendVo">
		insert into EXTEND_ADINFO(agent,appid,appkey,type,code,limitname,refreshinterval,ad_sid,textinfo,adsize,creatdate)
		values(#{agent},#{appid},#{appkey},#{type},#{code},#{limitname},#{refreshinterval},#{ad_sid},'${textinfo}-bc',#{adsize},now())
	</insert>

	<insert id="batchInsertNewAdConfig" parameterType="java.util.List">
		insert into EXTEND_ADINFO(agent,appid,appkey,type,code,limitname,refreshinterval,ad_sid,textinfo,adsize,creatdate)values
		<foreach collection="list"  index="index" item="item" separator=",">
		(#{item.agent},#{item.appid},#{item.appkey},#{item.type},#{item.code},#{item.limitname},#{item.refreshinterval},#{item.ad_sid},
			'${item.textinfo}-bc',#{item.adsize},now())
		</foreach>
	</insert>

	<update id="updateNewAdConfig" parameterType="com.wbgame.pojo.ExtendVo">
		update EXTEND_ADINFO set code=#{code},appid=#{appid},textinfo='${textinfo}-bc',
		refreshinterval=#{refreshinterval},limitname=#{limitname},adsize=#{adsize},creatdate=now(),appkey=#{appkey}
		where ad_sid=#{ad_sid}
	</update>

	<delete id="deleteNewAdConfig" parameterType="String">
		delete from EXTEND_ADINFO where ad_sid in (#{ids})
	</delete>

	<select id="selectNewAdOpenConfigForSql" parameterType="String" resultType="com.wbgame.pojo.push.ConfigVo">
		${sql}
	</select>

	<select id="selectNewAdOpenConfig" parameterType="Map" resultType="com.wbgame.pojo.push.ConfigVo">
		select t.limit_num,t.seqid, t.prjmid,t.name,t.type,t.rate,t.ad_sid_str,t.agentpecent,t.round,t.statu,
		t.delaytime,t.activ_cityid,t.activ_telecom,t.activ_statu,DATE_FORMAT(t.createdate,'%Y-%m-%d %H:%i:%s') createdate,
		t.delaydays as limit_date,t.delaysecond as limit_second,t.showmodel
		from EXTEND_ADCONFIG t where 1=1
		<if test="ad_sid != null and ad_sid != ''">
			and t.ad_sid_str like concat('%',#{ad_sid},'%')
		</if>
		<if test="prjid != null and prjid != ''">
			and t.prjmid like concat('%',#{prjid},'%')
		</if>
		<if test="status != null and status != ''">
			and t.statu = #{status}
		</if>
	</select>

	<insert id="insertNewAdOpenConfig" parameterType="com.wbgame.pojo.push.ConfigVo">
		insert into EXTEND_ADCONFIG(prjmid,name,type,rate,limit_num,ad_sid_str,agentpecent,round,statu,
		delaytime,activ_cityid,activ_telecom,activ_statu,createdate,delaydays,delaysecond,showmodel) values
		(#{prjmid},#{name},#{type},#{rate},#{limit_num},#{ad_sid_str},#{agentpecent},#{round},#{statu},
		#{delaytime},#{activ_cityid},#{activ_telecom},#{activ_statu},now(),#{limit_date},#{limit_second},#{showmodel})
	</insert>

	<insert id="batchInsertNewAdOpenConfig" parameterType="java.util.List">
		insert into EXTEND_ADCONFIG(prjmid,name,type,rate,limit_num,ad_sid_str,agentpecent,round,statu,
		delaytime,activ_cityid,activ_telecom,activ_statu,createdate,delaydays,delaysecond,showmodel) values
		<foreach collection="list"  index="index" item="item" separator=",">
			(#{item.prjmid},#{item.name},#{item.type},#{item.rate},#{item.limit_num},#{item.ad_sid_str},#{item.agentpecent},
			#{item.round},#{item.statu},
			#{item.delaytime},#{item.activ_cityid},#{item.activ_telecom},#{item.activ_statu},now(),#{item.limit_date},
			#{item.limit_second},#{item.showmodel})
		</foreach>
	</insert>

	<update id="updateNewAdOpenConfig" parameterType="com.wbgame.pojo.push.ConfigVo">
		update EXTEND_ADCONFIG set prjmid=#{prjmid},type=#{type},ad_sid_str=#{ad_sid_str},name=#{name},
		rate=#{rate},limit_num=#{limit_num},agentpecent=#{agentpecent},statu=#{statu},round=#{round},
		delaytime=#{delaytime},activ_telecom=#{activ_telecom},activ_cityid=#{activ_cityid},
		activ_statu=#{activ_statu},delaydays=#{limit_date},delaysecond=#{limit_second},
		showmodel=#{showmodel} where seqid=#{seqid}
	</update>

	<update id="batchupdateNewAdOpenConfig" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" open="" close="" separator=";">
			update EXTEND_ADCONFIG
			<set>
				prjmid=#{item.prjmid},type=#{item.type},ad_sid_str=#{item.ad_sid_str},name=#{item.name},
				rate=#{item.rate},limit_num=#{item.limit_num},agentpecent=#{item.agentpecent},statu=#{item.statu},round=#{item.round},
				delaytime=#{item.delaytime},activ_telecom=#{item.activ_telecom},
				<if test="item.activ_cityid != null and item.activ_cityid != ''">
					activ_cityid=#{item.activ_cityid},
				</if>
				<if test="item.showmodel != null and item.showmodel != ''">
					showmodel=#{item.showmodel},
				</if>
				
				activ_statu=#{item.activ_statu},delaydays=#{item.limit_date},delaysecond=#{item.limit_second}
			</set>
			where seqid=#{item.seqid}
		</foreach>
	</update>

	<delete id="deleteNewAdOpenConfig" parameterType="Map">
		delete from EXTEND_ADCONFIG where SEQID in (${seqids})
	</delete>

	<update id="openNewAdOpenConfig" parameterType="Map">
		update EXTEND_ADCONFIG set statu = #{statu} where seqid in (${ids})
	</update>

	 <select id="selectNewAdOpenConfigByPrj" parameterType="String" resultType="com.wbgame.pojo.push.ConfigVo">
		 select limit_num,seqid,prjmid,name,type,rate,ad_sid_str,agentpecent,round,statu,delaytime,
		 activ_cityid,activ_telecom,activ_statu,DATE_FORMAT(createdate,'%Y-%m-%d %H:%i:%s') createdate,
		 delaydays as limit_date,delaysecond as limit_second,showmodel
		 from EXTEND_ADCONFIG t where 1=1 and prjmid=#{id}
	 </select>
	 
	 <select id="selectNewAdNameOpenConfig" parameterType="String" resultType="com.wbgame.pojo.push.ConfigVo">
		 select limit_num,seqid,prjmid,name,type,rate,ad_sid_str,agentpecent,round,statu,delaytime,
		 activ_cityid,activ_telecom,activ_statu,DATE_FORMAT(createdate,'%Y-%m-%d %H:%i:%s') createdate,
		 delaydays as limit_date,delaysecond as limit_second,showmodel
		 from EXTEND_ADCONFIG t where 1=1 and seqid in (${seqids})
	 </select>

	<select id="selectNewAdOpenConfigInfoById" parameterType="List" resultType="com.wbgame.pojo.push.ConfigVo">
		select limit_num,seqid,prjmid,name,type,rate,ad_sid_str,agentpecent,round,statu,delaytime,
		activ_cityid,activ_telecom,activ_statu,DATE_FORMAT(createdate,'%Y-%m-%d %H:%i:%s') createdate,
		delaydays as limit_date,delaysecond as limit_second,showmodel
		from EXTEND_ADCONFIG t where seqid in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="selectNewAdOpenConfigInfo" parameterType="Map" resultType="com.wbgame.pojo.push.ConfigVo">
		select limit_num,seqid,prjmid,name,type,rate,ad_sid_str,agentpecent,round,statu,delaytime,
		activ_cityid,activ_telecom,activ_statu,DATE_FORMAT(createdate,'%Y-%m-%d %H:%i:%s') createdate,
		delaydays as limit_date,delaysecond as limit_second,showmodel
		from EXTEND_ADCONFIG t where 1=1
		<if test="opid != null and opid != ''">
			and prjmid = #{opid}
		</if>
		<if test="ad_sid != null and ad_sid != ''">
			and ad_sid_str = #{ad_sid}
		</if>
		<if test="ad_name != null and ad_name != ''">
			and name = #{ad_name}
		</if>
	</select>

	<select id="selectProduct" parameterType="Map" resultType="com.wbgame.pojo.UmengTotalVo">
		select id,tdate,product,SUM(addnum) as addnum,SUM(actnum) as actnum,SUM(income) as income,SUM(expend) as expend,channel
		from umeng_total_info where tdate BETWEEN #{start_date} AND #{end_date}
		<if test="product != null and product != ''">
			and product = #{product}
		</if>
		group by tdate,product,channel order by tdate desc,addnum desc,channel asc
	</select>

	<update id="updateUmengProduct" parameterType="Map">
		update umeng_total_info set income = #{income},expend = #{expend} where id = #{id}
	</update>

	<select id="selectProductData" parameterType="Map" resultType="com.wbgame.pojo.ProdutChannelDataVo">
		<!-- select a.cha_name,b.by_date,d.gameName,b.push_newcount as newCount,c.by_newcount as dauCount
		from alone_project_info a,buyu_newcount_total b
		,alone_dau_total c
		,wbgui_formconfig d
		where a.prjid = d.pjId and a.prjid = b.by_priid and a.prjid = c.by_priid and c.by_date = b.by_date
		and b.by_date &gt;= #{begin} and b.by_date &lt;= #{end}  -->
		
		select by_date,gameName,newCount,dauCount,cha_name,ee.totalFee from 
			(select b.by_date,d.gameName,b.push_newcount as newCount,c.by_newcount as dauCount,a.cha_name,a.prjid
                from alone_project_info a
                ,buyu_newcount_total b
                ,alone_dau_total c
                ,dnwx_client.wbgui_formconfig d
                where a.prjid = d.pjId and a.prjid = b.by_priid and a.prjid = c.by_priid and c.by_date = b.by_date
                and b.by_date BETWEEN '${begin} 00:00:00' AND '${end} 23:59:59' 
                <if test="currNm != 'admin'">
					and a.login_name = #{currNm}
				</if>
				<if test="gameName != null and gameName != ''">
					and d.gameName like concat('%',#{gameName},'%')
				</if>
				<if test="cha_name != null and cha_name != ''">
					and a.cha_name like concat('%',#{cha_name},'%')
				</if>
                ) aa 
            LEFT JOIN 
              (select c_date,c_pid,ROUND(sum(c_total),2) totalFee from red_pack_audit_detail 
				where c_date BETWEEN '${begin} 00:00:00' AND '${end} 23:59:59' 
				group by c_date,c_pid
				) ee ON aa.by_date = ee.c_date and aa.prjid = ee.c_pid
		
	</select>

	<select id="selectIncomeData" parameterType="Map" resultType="com.wbgame.pojo.UmengTotalVo">
		select tdate,product,ctype,SUM(addnum) as addnum,SUM(actnum) as actnum,SUM(income) as income,SUM(expend) as expend
		from umeng_total_info where tdate BETWEEN #{start_date} AND #{end_date}
		<if test="ctype != null and ctype != ''">
			and ctype = #{ctype}
		</if>
		<if test="product != null and product != ''">
			and product = #{product}
		</if>
		group by tdate,product,ctype order by tdate desc,ctype asc,addnum desc
	</select>

	<select id="selectPageAutoConfig" parameterType="Map" resultType="com.wbgame.pojo.NpPostVo">
		SELECT pname as param1,page_url as param2,json_value as param3 FROM page_info where 1=1 
		<if test="pname != null and pname != ''">
			and pname like concat('%',#{pname},'%')
		</if>
	</select>

	<insert id="insertPageAuto" parameterType="Map">
		insert into page_info(pname,page_url,json_value)values(#{pname},#{page_url},#{value})
	</insert>

	<select id="selectAdTableConfig" parameterType="Map" resultType="com.wbgame.pojo.NpActiveFree">
		select * from np_param_active_free where 1=1
		<if test="prjmid != null and prjmid != ''">
			and PRJMID = #{prjmid}
		</if>
		<if test="testids != null and testids != ''">
			and (prjmid in (${testids}) or prjmid like '3333%')
		</if>
	</select>

	<insert id="insertAdTableConfig" parameterType="com.wbgame.pojo.NpActiveFree">
		insert into np_param_active_free(prjmid,free_num,plaquelimitinterval,productid,carrier_type,seq_id,remark_info,sfcityids,
		mmid,clicklimitnum,update_time,splashlimitinterval,bannerupdateinterval,adopenlimitinterval,reloadinterval,expires)
		values(#{prjmid},#{free_num},#{plaquelimitinterval},#{productid},#{carrier_type},#{seq_id},
		#{remark_info},#{sfcityids},#{mmid},#{clicklimitnum},#{update_time},#{splashlimitinterval},
		#{bannerupdateinterval},#{adopenlimitinterval},#{reloadinterval},#{expires})
	</insert>

	<update id="updateAdTableConfig" parameterType="com.wbgame.pojo.NpActiveFree">
		update np_param_active_free set free_num = #{free_num},plaquelimitinterval = #{plaquelimitinterval},
		productid = #{productid},carrier_type = #{carrier_type},seq_id = #{seq_id},remark_info = #{remark_info},
		sfcityids = #{sfcityids},mmid = #{mmid},clicklimitnum = #{clicklimitnum},update_time = #{update_time},
		splashlimitinterval = #{splashlimitinterval},bannerupdateinterval = #{bannerupdateinterval},
		adopenlimitinterval=#{adopenlimitinterval},reloadinterval=#{reloadinterval},expires=#{expires}
		where prjmid = #{prjmid}
	</update>

	<select id="selectNpMap" parameterType="Map" resultType="com.wbgame.pojo.NpPostVo">
		${sql}
	</select>

	<select id="selectUserNewCount" parameterType="String" resultType="com.wbgame.pojo.UserNewCountVo">
		${sql}
	</select>

	<select id="queryOppoPay" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryOppoPayNew" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryAliPay" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryAliPayNew" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryViewPay" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryViewPayNew" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryWxPay" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryWxPayNew" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryHWPay" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryHWPayNew" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryBDPay" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryBDPayNew" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryXMPay" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryXMPayNew" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryJLPay" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryJLPayNew" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryMZPay" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryMZPayNew" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="querySXPay" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="querySXPayNew" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryLXPay" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryLXPayNew" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryTXPay" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<select id="queryTXPayNew" parameterType="String" resultType="com.wbgame.pojo.PayInfoVo">
		${sql}
	</select>

	<insert id="insertApkGameTime" parameterType="java.util.List">
		insert into apk_gametime (blockgame,productid,projectid,nsingle,osingle,nday,oday,startcount,createtime) values
		<foreach collection="list"  index="index" item="item" separator=",">
			(#{item.blockgame},#{item.productid},#{item.projectid},#{item.nsingle},#{item.osingle},
			#{item.nday},#{item.oday},#{item.startcount},#{item.createtime})
		</foreach>
		ON DUPLICATE KEY UPDATE
		nday=VALUES(nday),
		oday=VALUES(oday),
		startcount=VALUES(startcount)
	</insert>

	<select id="selectApkGameTime" parameterType="String" resultType="com.wbgame.pojo.ApkGameTimeVo">
		${sql}
	</select>

	<select id="selectAdTotalHour" parameterType="String" resultType="com.wbgame.pojo.AdTotalHourVo">
		${sql}
	</select>

	<select id="selectNpMaps" parameterType="String" resultType="Map">
		${sql}
	</select>

	<select id="selectPathInfo" resultType="com.wbgame.pojo.SysPathVo">
		select * from sys_path_config
	</select>

	<insert id="insertLogInfo" parameterType="com.wbgame.pojo.wbsys.WxActionRecord">
		insert into wx_action_record(cdatetime,cuser,cpage,ctype,params,respBody,client_ip) 
		values(now(),#{cuser},#{cpage},#{ctype},#{params},#{respBody},#{client_ip})
	</insert>

	<select id="selectPkLog" parameterType="String" resultType="com.wbgame.pojo.PkLogVo">
		${sql}
	</select>

	<select id="selectNewUserByPid" parameterType="Map" resultType="com.wbgame.pojo.ProdutChannelDataVo">
		select by_date,sum(push_newcount) newCount,by_priid
		from buyu_newcount_total where by_priid = #{pid} and by_priid != "0"
		and by_date &gt;= #{createtime} and by_date &lt;= #{endtime}
		group by by_priid
	</select>

	<select id="selectDauUserByPid" parameterType="Map" resultType="com.wbgame.pojo.ProdutChannelDataVo">
		select by_date,by_newcount as dauCount,by_priid
		from alone_dau_total where by_priid = #{pid}
		and by_date &gt;= #{createtime} and by_date &lt;= #{endtime}
	</select>

	<select id="selectStartCountByPid" parameterType="Map" resultType="com.wbgame.pojo.ProdutChannelDataVo">
		select by_date,by_newcount as startCount,by_priid
		from alone_dau_total where by_priid = #{pid}
		and by_date &gt;= #{createtime} and by_date &lt;= #{endtime}
	</select>

	<select id="selectAsserts" parameterType="Map" resultType="com.wbgame.pojo.AssetsInfo">
		select oldassetsid,assetsid,assetsname,assetsnote,assetsuser,assetsstatus,assetsusage,company,buytime,assetstype,
		assetssum,userdepartment,addtime,
		date_format(createtime,"%Y-%m-%d %H:%i:%S") as createtime,atype,amoney,aunit from assets_info where 1=1
		<if test="starttime != null and starttime != ''">
            and date_format(createtime,"%Y-%m-%d") &gt;= #{starttime}
		</if>
		<if test="endtime != null and endtime != ''">
			and date_format(createtime,"%Y-%m-%d") &lt;= #{endtime}
		</if>
		<if test="assetsname != null and assetsname != ''">
			and assetsname = #{assetsname}
		</if>
		<if test="assetsstatus != null and assetsstatus != ''">
			and assetsstatus = #{assetsstatus}
		</if>
		<if test="assetsusage != null and assetsusage != ''">
			and assetsusage = #{assetsusage}
		</if>
		<if test="userdepartment != null and userdepartment != ''">
			and userdepartment = #{userdepartment}
		</if>
		<if test="assetsuser != null and assetsuser != ''">
			and assetsuser = #{assetsuser}
		</if>
		<if test="company != null and company != ''">
			and company = #{company}
		</if>
		<if test="atype != null and atype != ''">
			and atype = #{atype}
		</if>
		<if test="assetsid != null and assetsid != ''">
			and assetsid = #{assetsid}
		</if>
		order by createtime desc
	</select>

	<insert id="addAsserts" parameterType="Map">
		insert into assets_info(oldassetsid,assetsid,assetsname,assetsnote,
		assetsstatus,company,buytime,assetstype,assetssum,atype,createtime,aunit,amoney)
		values(#{oldassetsid},#{assetsid},#{assetsname},#{assetsnote},
		#{assetsstatus},#{company},#{buytime},#{assetstype},#{assetssum},#{atype},now(),#{aunit},#{amoney})
	</insert>

	<update id="updateAsserts" parameterType="Map">
		update assets_info set assetsname = #{assetsname},assetsnote=#{assetsnote},assetsstatus=#{assetsstatus},
		company = #{company},buytime=#{buytime},assetstype=#{assetstype},assetssum=#{assetssum},atype=#{atype},amoney=#{amoney},aunit=#{aunit}
		where oldassetsid = #{oldassetsid} and assetsid = #{assetsid}
	</update>

	<update id="handerAsserts" parameterType="Map">
		update assets_info set assetsuser=#{assetsuser},userdepartment=#{userdepartment},assetsusage=#{assetsusage}
		where oldassetsid = #{oldassetsid} and assetsid = #{assetsid}
	</update>

	<insert id="batchAddAsserts" parameterType="java.util.List">
		insert into assets_info (oldassetsid,assetsid,assetsname,assetsnote,assetsuser,assetsstatus,assetsusage,company,buytime,
		assetstype,assetssum,userdepartment,addtime,atype,createtime,aunit,amoney) values
		<foreach collection="list"  index="index" item="item" separator=",">
			(#{item.oldassetsid},#{item.assetsid},#{item.assetsname},#{item.assetsnote},#{item.assetsuser},
			#{item.assetsstatus},#{item.assetsusage},#{item.company},#{item.buytime},#{item.assetstype},#{item.assetssum},#{item.userdepartment},
			#{item.addtime},#{item.atype},now(),#{item.aunit},#{item.amoney})
		</foreach>
	</insert>

	<select id="selectAssertsLog" parameterType="Map" resultType="com.wbgame.pojo.AssetsInfoLog">
		select aid,assetsid,operator,operatortype,operatorcontent,date_format(createtime,"%Y-%m-%d %H:%i:%S") as createtime from assets_info_log
		where 1=1
		<if test="assetsid != null and assetsid != ''">
			and assetsid = #{assetsid}
		</if>
		<if test="starttime != null and starttime != ''">
			and date_format(createtime,"%Y-%m-%d") &gt;= #{starttime}
		</if>
		<if test="endtime != null and endtime != ''">
			and date_format(createtime,"%Y-%m-%d") &lt;= #{endtime}
		</if>
		order by createtime desc
	</select>

	<insert id="insertAssertsLog" parameterType="com.wbgame.pojo.AssetsInfoLog">
		insert into assets_info_log(assetsid,operator,operatortype,operatorcontent,createtime)
		values(#{assetsid},#{operator},#{operatortype},#{operatorcontent},now())
	</insert>

	<insert id="batchAssertsLog" parameterType="java.util.List">
		insert into assets_info_log(assetsid,operator,operatortype,operatorcontent,createtime)values
		<foreach collection="list"  index="index" item="item" separator=",">
			(#{item.assetsid},#{item.operator},#{item.operatortype},#{item.operatorcontent},now())
		</foreach>
	</insert>

	<select id="selectAssertsTable" resultType="com.wbgame.pojo.AssetsInfoTable">
		select count(*) as num,assetsstatus
		from assets_info GROUP BY assetsstatus
	</select>

	<select id="selectAssertsDepTable" resultType="com.wbgame.pojo.AssetsInfoTable">
		select count(*) as num,userdepartment
		from assets_info GROUP BY userdepartment
	</select>

	<select id="selectAssertsDateTable" resultType="com.wbgame.pojo.AssetsInfoTable">
		select count(*) as num,operatortype,
		DATE_FORMAT(createtime,"%Y-%m") as createtime
		from assets_info_log
		GROUP BY operatortype,DATE_FORMAT(createtime,"%Y-%m")
	</select>

	<insert id="insertLoginTotal" parameterType="java.util.List">
		insert into apk_login_total_${table}(appid,imei,projectid,lsn,loginday)values
		<foreach collection="list"  index="index" item="item" separator=",">
			(#{item.appid},#{item.imei},#{item.projectid},#{item.lsn},1)
		</foreach>
		ON DUPLICATE KEY UPDATE
		loginday=loginday+1
	</insert>

	<select id="selectLoginTable" parameterType="Map" resultType="com.wbgame.pojo.LoginTotalVo">
		select count(*) as psum,loginday from apk_login_total_${table}
		where appid = #{appid}
		GROUP BY loginday
		order by psum desc
	</select>

	<select id="selectLoginDetail" parameterType="Map" resultType="com.wbgame.pojo.LoginTotalVo">
		select * from apk_login_total_${table} where loginday &gt;= #{loginday} and appid = #{appid}
	</select>
	
	<update id="updateSDKConfigInfo" parameterType="com.wbgame.pojo.SDKConfigInfo" >
    update sdk_config
    set sdk_txt = #{sdkTxt,jdbcType=VARCHAR},
      sdk_val = #{sdkVal,jdbcType=VARCHAR},
      sdk_status = #{sdkStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <insert id="insertSDKConfigInfo" parameterType="com.wbgame.pojo.SDKConfigInfo" >
    insert into sdk_config (sdk_txt, sdk_val, sdk_status)
    values (#{sdkTxt}, #{sdkVal}, #{sdkStatus})
  </insert>
  
  <select id="selectSDKConfigInfo" parameterType="com.wbgame.pojo.SDKConfigInfo" resultType="com.wbgame.pojo.SDKConfigInfo">
    select 
     id, sdk_txt AS sdkTxt , sdk_val AS sdkVal, sdk_status AS sdkStatus
    from sdk_config
    where 1=1 
    <if test="id != null and id != ''" >
        and id = #{id,jdbcType=VARCHAR}
    </if>
    <if test="sdkTxt != null and sdkTxt != ''" >
        and sdk_txt  like concat('%',#{sdkTxt},'%')
     </if>
     <if test="sdkVal != null and sdkVal != ''" >
        and sdk_val  like concat('%',#{sdkVal},'%')
    </if>
    <if test="sdkStatus != null and sdkStatus != ''" >
        and sdk_status = #{sdkStatus,jdbcType=INTEGER}
     </if>
  </select>

	<select id="selectUmengChannelConfig" resultType="com.wbgame.pojo.UmengChannelConfig" parameterType="com.wbgame.pojo.UmengChannelConfig" >
    select 
    id, channel
    from umeng_channel_config
    where 1=1
    <if test="id != null and id != ''" >
        and id = #{id,jdbcType=INTEGER}
     </if>
  </select>
	<insert id="insertUmengChannelConfig" parameterType="com.wbgame.pojo.UmengChannelConfig" >
    insert into umeng_channel_config (id, channel)
    values (#{id,jdbcType=INTEGER}, #{channel,jdbcType=VARCHAR})
  </insert>
	<update id="updateUmengChannelConfig" parameterType="com.wbgame.pojo.UmengChannelConfig" >
    update umeng_channel_config
    set channel = #{channel,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
   <delete id="deleteUmengChannelConfig" parameterType="com.wbgame.pojo.UmengChannelConfig" >
    delete from umeng_channel_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
 
	<select id="selectUmengChangeInfo" parameterType="Map" resultType="com.wbgame.pojo.UmengChannelTotalVo">
	 select aa.tdate,aa.cname,aa.appKey,aa.addnum,aa.actnum,zz.app_name as appname,
	 	cha_media chaMedia,cha_sub_launch chaSubLaunch,zz.id ,dd.crashnum,
	 	CONCAT(truncate(dd.crashnum / aa.actnum * 100,2),'%') crashrate
	 from
		(select * from  umeng_channel_total where
		tdate BETWEEN #{start_date} AND #{end_date} 
			<if test="appid != null and appid != ''" >
       			and appid in (${appid})
     		</if>
		) aa
		join app_info zz on aa.appkey = zz.umeng_key
		join (select * from umeng_income_show where ctype = 1) bb
		on aa.appkey = bb.appkey and aa.cid = bb.cid
		left join dn_channel_info cc on aa.cname = cc.cha_id
		left join umeng_channel_crash dd
		on aa.tdate=dd.ds and aa.appkey=dd.app_key and aa.cname=dd.channel
		
		<if test="order != null and order != ''" >
       		order by ${order}
     	</if>
	</select>
	
	<select id="selectUmengChangeSummary" parameterType="Map" resultType="com.wbgame.pojo.UmengChannelTotalVo">
	 	select sum(aa.addnum) addnum,sum(aa.actnum) actnum,sum(dd.crashnum) crashnum,
	 		CONCAT(truncate(sum(dd.crashnum) / sum(aa.actnum) * 100,2),'%') crashrate
		FROM
		(select * from  umeng_channel_total where
		tdate BETWEEN #{start_date} AND #{end_date} 
			<if test="appid != null and appid != ''" >
       			and appid = #{appid} 
     		</if>
		) aa
		join app_info zz on aa.appkey = zz.umeng_key
		join (select * from umeng_income_show where ctype = 1) bb
		on aa.appkey = bb.appkey and aa.cid = bb.cid
		left join dn_channel_info cc on aa.cname = cc.cha_id
		left join umeng_channel_crash dd
		on aa.tdate=dd.ds and aa.appkey=dd.app_key and aa.cname=dd.channel
		
	</select>
	
	<select id="selectDnGroup" resultType="com.wbgame.pojo.DnChannelVo" >
	 select CAST(id as char(20)) id , groupName from dn_group a where a.enabled = 1
	</select>
	
	<select id="selectDnGroupApp" resultType="com.wbgame.pojo.DnChannelVo" parameterType="String" >
	 SELECT CAST(a.appId as char(20)) appId  ,b.app_name appName from dn_group_app a left JOIN app_info b on a.appId =b.id  
	 <if test="groupId != null and groupId != ''" >
     where a.groupId in (${groupId})
     </if> 
	  
	</select>
	<select id="selectDnChannelType" resultType="com.wbgame.pojo.DnChannelVo" >
	 SELECT CAST(type_id as char(20)) typeId, type_name typeName from dn_channel_type
	</select>
	
	<select id="selectDnChaMedia" resultType="String" >
	 SELECT DISTINCT cha_media chaMedia from dn_channel_info where cha_media  != '' 
	</select>
	
	<select id="selectDnChaSubLaunch" resultType="String" >
	 SELECT DISTINCT cha_sub_launch chaSubLaunch  from dn_channel_info  where cha_media  != '' 
	</select>
  
    <insert id="insertDnChannelCost" parameterType="com.wbgame.pojo.DnChannelCost" >
    insert into dn_channel_cost (tdate, appid, cha_id, invest_amount, adv_income, billing_income)
    values
     <foreach collection="list"  index="index" item="item" separator=",">
    (#{item.tdate,jdbcType=VARCHAR}, #{item.appid,jdbcType=VARCHAR}, #{item.chaId,jdbcType=VARCHAR}, 
      #{item.investAmount,jdbcType=DECIMAL}, #{item.advIncome,jdbcType=DECIMAL}, #{item.billingIncome,jdbcType=DECIMAL})
     </foreach>
      ON DUPLICATE KEY UPDATE
		invest_amount=VALUES(invest_amount),
		adv_income=VALUES(adv_income),
		billing_income=VALUES(billing_income)  
  	</insert>
    <insert id="insertDnChannelCostTwo" parameterType="com.wbgame.pojo.DnChannelCost" >
    insert into umeng_channel_cost (tdate, appid, cha_id, invest_amount, adv_income, billing_income)
    values
     <foreach collection="list"  index="index" item="item" separator=",">
    (#{item.tdate,jdbcType=VARCHAR}, #{item.appid,jdbcType=VARCHAR}, #{item.chaId,jdbcType=VARCHAR}, 
      #{item.investAmount,jdbcType=DECIMAL}, #{item.advIncome,jdbcType=DECIMAL}, #{item.billingIncome,jdbcType=DECIMAL})
     </foreach>
      ON DUPLICATE KEY UPDATE
		invest_amount=VALUES(invest_amount),
		adv_income=VALUES(adv_income),
		billing_income=VALUES(billing_income)  
  	</insert>
  	<insert id="insertDnChannelCostTwoBilling" parameterType="java.lang.String">
  		insert into umeng_channel_cost(tdate,appid,cha_id,billing_income) 
		select '${tdate}' tdate,a.appid,b.channelTag,TRUNCATE(sum(a.money)/100,2)
		from wb_pay_info a,dnwx_client.wbgui_formconfig b
			where a.pid = b.pjId
			and a.createtime BETWEEN '${tdate} 00:00:00' and '${tdate} 23:59:59' 
			and a.orderstatus = 'SUCCESS'
			and a.pid is not null
			and b.channelTag != ''
		group by a.appid,b.channelTag 
		
		ON DUPLICATE KEY UPDATE
		billing_income=VALUES(billing_income)
  	</insert>
  	
  	<select id="selectDnChannelCost" parameterType="Map" resultType="com.wbgame.pojo.DnChannelCost" >
    select 
     aa.tdate, aa.appid, cha_id chaId, invest_amount investAmount, adv_income advIncome, billing_income billingIncome,b.app_name appidName
    from dn_channel_cost aa left JOIN app_info b on aa.appId =b.id 
    where 1 = 1
    <if test ="startTime != null and endTime != null ">
		and aa.tdate BETWEEN #{startTime} AND #{endTime}
	</if>	
	<if test="appid != null and appid != ''  ">
		and aa.appid = #{appid}
	</if>
	<if test="chaId != null and chaId != ''  ">
		and aa.cha_id = #{chaId}
	</if>
  	</select>
  
  	<select id="selectDnChannelTotal" parameterType="Map" resultType="com.wbgame.pojo.DnChannelTotal" >
	 select aa.tdate,aa.appid,aa.appname,aa.cname,aa.cid ,sum(aa.addnum) addnum,sum(aa.actnum) actnum,aa.totalKeep,bb.cha_media chaMedia,
	 bb.cha_sub_launch chaSubLaunch,bb.cha_sub_name_byte,bb.cha_type chaType,sum(cc.adv_income) advIncome
	,sum(cc.billing_income) billingIncome,sum(cc.invest_amount) investAmount
	,round(cc.invest_amount  / aa.addnum, 2) CPA
	,round(
			(cc.adv_income + cc.billing_income) / aa.actnum,
			2
		) totalDAUARPU,
		round(cc.adv_income / aa.actnum, 2) advDAUARPU,
		round(cc.billing_income / aa.actnum, 2) billDAUARPU,
		CONCAT(
			round(
				(cc.adv_income + cc.billing_income) * 100 / invest_amount,
				2
			),
			'%'
		) dayROI
	,dd.sumAdv
	,dd.sumBill
	,dd.sumInvest
	,round(
				(dd.sumAdv + dd.sumBill),
				2
			) totalIncome
	,CONCAT(
			round(
				(dd.sumAdv + dd.sumBill) / dd.sumInvest * 100,
				2
			),
			'%'
		)totalROI
	from (select a.tdate,a.appid,a.appname,a.cid,a.cname,sum(a.addnum) addnum,sum(a.actnum) actnum,round(
			(
				100 + IFNULL(avg(keep_num1), 0) + IFNULL(avg(keep_num2), 0) + IFNULL(avg(keep_num3), 0) + IFNULL(avg(keep_num4), 0) + IFNULL(avg(keep_num5), 0) + IFNULL(avg(keep_num6), 0) + IFNULL(avg(keep_num7), 0)
			) / 100,
			2
		) totalKeep from umeng_channel_total a 
	where  a.tdate BETWEEN #{startTime} AND #{endTime}  group by a.tdate,a.appid,a.appname,a.cname) aa LEFT JOIN (select dci.cha_media,dci.cha_sub_launch,dci.cha_sub_name_byte,dct.type_name,dci.cha_id,dci.cha_type from dn_channel_info dci LEFT JOIN dn_channel_type dct on dci.cha_type = dct.type_id) bb on aa.cname = bb.cha_id 
	LEFT JOIN dn_channel_cost cc ON aa.tdate = cc.tdate and aa.appid = cc.appid and aa.cname = cc.cha_id LEFT JOIN (select appid,cha_id,sum(invest_amount) as sumInvest,sum(adv_income) as sumAdv,sum(billing_income) as sumBill 
	from dn_channel_cost where tdate &lt;=  #{endTime} group by appid,cha_id) dd on aa.appid = dd.appid and aa.cname = dd.cha_id
	where 1=1
	<if test="appid != null and appid != ''  ">
		and aa.appid in (${appid})
	</if>
	<if test="chaType != null and chaType != ''  ">
		and bb.cha_type = #{chaType}
	</if>
	<if test="chaMedia != null and chaMedia != ''  ">
		and bb.cha_media = #{chaMedia}
	</if>
	<if test="chaSubLaunch != null and chaSubLaunch != ''  ">
		and bb.cha_sub_launch = #{chaSubLaunch}
	</if>
	<if test="channelList != null and channelList != ''" >
		 and aa.cname in 
	    <foreach collection="channelList" item="channelList" open="(" separator="," close=")">
               #{channelList}
         </foreach>
	</if>
	<if test="group != null and group != ''" >
	     group by ${group}
	</if>
	<if test="order != null and order != ''" >
	     order by ${order}
	</if>  	
	</select>
  	
  	<select id="selectDnChannelSum" parameterType="Map" resultType="com.wbgame.pojo.DnChannelTotal" >
	select   sum(addnum) addnum, sum(actnum) `actnum`,
	sum(adv_income)  advIncome,
	sum(invest_amount)  investAmount,
	sum(billing_income)  billingIncome
	from umeng_channel_total a left join
	dn_channel_cost  c ON a.cname = c.cha_id and a.tdate = c.tdate and a.appid =c.appid
	left join dn_channel_info b ON a.cname = b.cha_id
	WHERE a.tdate BETWEEN #{startTime} AND #{endTime}
	<if test="appid != null and appid != ''  ">
		and a.appid in (${appid})
	</if>
	<if test="chaType != null and chaType != ''  ">
		and b.cha_type = #{chaType}
	</if>
	<if test="chaMedia != null and chaMedia != ''  ">
		and b.cha_media = #{chaMedia}
	</if>
	<if test="chaSubLaunch != null and chaSubLaunch != ''  ">
		and b.cha_sub_launch = #{chaSubLaunch}
	</if>
  	</select>
  	
  	
  	
  	<insert id="insertDnAppIncomeTotalBilling" parameterType="java.lang.String">
  		insert into dn_app_income_total(tdate,appid,cname,billing_income) 
		select '${tdate}' tdate,a.appid,b.channelTag,TRUNCATE(sum(a.money)/100,2)
		from wb_pay_info a,dnwx_client.wbgui_formconfig b
			where a.pid = b.pjId
			and a.createtime BETWEEN '${tdate} 00:00:00' and '${tdate} 23:59:59' 
			and a.orderstatus = 'SUCCESS'
			and a.pid is not null
			and b.channelTag != ''
		group by a.appid,b.channelTag 
		
		ON DUPLICATE KEY UPDATE
		billing_income=VALUES(billing_income)
  	</insert>
  	<insert id="insertDnAppIncomeTotal" parameterType="java.lang.String">
  		insert into dn_app_income_total
  			(tdate,appid,appname,cha_type,type_name,cha_media,cha_sub_launch,cname,addnum,actnum,totalKeep,cha_sub_name_byte,adv_income,billing_income,invest_amount)
		 SELECT
			cc.tdate,
			cc.appid,
			aa.appname,
		  	bb.cha_type,
			bb.type_name,
			bb.cha_media,
			bb.cha_sub_launch,
			cc.cha_id,
			aa.addnum,
			aa.actnum,
			aa.totalKeep,
			bb.cha_sub_name_byte,
			cc.adv_income,
			cc.billing_income,
			cc.invest_amount
		FROM
			(
			SELECT
				a.tdate,
				a.appid,
				a.appname,
				a.cname,
				sum(a.addnum) addnum,
				sum(a.actnum) actnum,
				round(
					(100 + IFNULL(avg(keep_num1), 0) + IFNULL(avg(keep_num2), 0) + IFNULL(avg(keep_num3), 0) + IFNULL(avg(keep_num4), 0) + IFNULL(avg(keep_num5), 0) + IFNULL(avg(keep_num6), 0) + IFNULL(avg(keep_num7), 0)) / 100,2
				) totalKeep
			FROM
				umeng_channel_total a WHERE a.tdate BETWEEN #{tdate} AND #{tdate} 
			GROUP BY
				a.tdate,
				a.appid,
				a.appname,
				a.cname
			) aa
			
		RIGHT JOIN (SELECT * from dn_channel_cost WHERE tdate BETWEEN #{tdate} AND #{tdate} ) cc 
		ON aa.tdate = cc.tdate AND aa.appid = cc.appid AND aa.cname = cc.cha_id
		
		LEFT JOIN (
			SELECT
				dci.cha_media,
				dci.cha_sub_launch,
				dci.cha_sub_name_byte,
				dct.type_name,
				dci.cha_id,
		    	dci.cha_type
			FROM dn_channel_info dci
			LEFT JOIN dn_channel_type dct ON dci.cha_type = dct.type_id
		) bb ON cc.cha_id = bb.cha_id
		
		ON DUPLICATE KEY UPDATE
		appname=VALUES(appname),
		adv_income=VALUES(adv_income),
		invest_amount=VALUES(invest_amount)
  	</insert>
  	
  	<!-- 游戏产品收支汇总表2021  -->
  	<select id="selectDnChannelTotal2021" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_channel_total_2021_sql"/>
	</select>
	<select id="selectDnChannelTotal2021Sum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			sum(xx.addnum) addnum,
			sum(xx.actnum) actnum,
			sum(xx.advIncome) advIncome,
			sum(xx.investAmount) investAmount,
			sum(xx.billingIncome) billingIncome
		from (<include refid="dn_channel_total_2021_sql"/>) xx
	</select>
	<sql id="dn_channel_total_2021_sql">
		select aa.*,dd.sumAdvIncome,dd.sumBillingIncome,dd.sumInvestAmount,
			round((dd.sumAdvIncome + dd.sumBillingIncome),2) sumAmount,
			CONCAT(round((dd.sumAdvIncome + dd.sumBillingIncome) / dd.sumInvestAmount * 100,2),'%') totalROI
		from 
			(SELECT
				appname,
				type_name,
				${group},
				round(sum(addnum)/20, 0) addnum,
				round(sum(actnum)/20, 0) actnum,
				round(sum(adv_income)/20, 0) advIncome,
				round(sum(invest_amount)/20, 0) investAmount,
				round(sum(billing_income)/20, 0) billingIncome,
			
			  	round(sum(invest_amount) / sum(addnum), 2) CPA,
				round(avg(totalKeep), 2) totalKeep,
			  	round((sum(adv_income) + sum(billing_income)) / sum(actnum),2) totalDAUARPU,
				round(sum(adv_income) / sum(actnum), 2) advDAUARPU,
			  	round(sum(billing_income) / sum(actnum), 2) billDAUARPU,
				CONCAT(round((sum(adv_income) + sum(billing_income)) * 100 / sum(invest_amount),2),'%') dayROI
			FROM
				dn_app_income_total where tdate BETWEEN #{startTime} AND #{endTime} 
				<include refid="dn_channel_where"/> 
			group by ${group}) aa
			 
		LEFT JOIN 
			(select 
				${group2},
				sum(adv_income) sumAdvIncome,
				sum(invest_amount) sumInvestAmount,
				sum(billing_income) sumBillingIncome
			from dn_app_income_total where 1=1 
			<include refid="dn_channel_where"/>
			group by ${group2}) dd 
		
		ON ${group_match}
		
		<if test="order_str != null and order_str != ''">
		 	order by ${order_str} 
		</if>
	</sql>
	
	<!-- 游戏产品收支汇总表 -->
  	<select id="selectDnChannelTotalNew" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_channel_total_sql"/>
	</select>
	<select id="selectDnChannelTotalNewSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			sum(xx.addnum) addnum,
			sum(xx.actnum) actnum,
			sum(xx.advIncome) advIncome,
			sum(xx.investAmount) investAmount,
			sum(xx.billingIncome) billingIncome
		from (<include refid="dn_channel_total_sql"/>) xx
	</select>
	<sql id="dn_channel_total_sql">
		select aa.*,dd.sumAdvIncome,dd.sumBillingIncome,dd.sumInvestAmount,
			round((dd.sumAdvIncome + dd.sumBillingIncome),2) sumAmount,
			CONCAT(round((dd.sumAdvIncome + dd.sumBillingIncome) / dd.sumInvestAmount * 100,2),'%') totalROI
		from 
			(SELECT
				appname,
				type_name,
				${group},
				sum(addnum) addnum,
				sum(actnum) actnum,
				sum(adv_income) advIncome,
				sum(invest_amount) investAmount,
				sum(billing_income) billingIncome,
			
			  	round(sum(invest_amount) / sum(addnum), 2) CPA,
				round(avg(totalKeep), 2) totalKeep,
			  	round((sum(adv_income) + sum(billing_income)) / sum(actnum),2) totalDAUARPU,
				round(sum(adv_income) / sum(actnum), 2) advDAUARPU,
			  	round(sum(billing_income) / sum(actnum), 2) billDAUARPU,
				CONCAT(round((sum(adv_income) + sum(billing_income)) * 100 / sum(invest_amount),2),'%') dayROI
			FROM
				dn_app_income_total where tdate BETWEEN #{startTime} AND #{endTime} 
				<include refid="dn_channel_where_new"/>
			group by ${group}) aa
			 
		LEFT JOIN 
			(select 
				${group2},
				sum(adv_income) sumAdvIncome,
				sum(invest_amount) sumInvestAmount,
				sum(billing_income) sumBillingIncome
			from dn_app_income_total where 1=1 
			<include refid="dn_channel_where_new"/>
			group by ${group2}) dd 
		
		ON ${group_match}
		
		<if test="order_str != null and order_str != ''">
		 	order by ${order_str} 
		</if>
	</sql>
	<sql id="dn_channel_where">
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="cha_type != null and cha_type != ''">
			and cha_type in (${cha_type}) 
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media = #{cha_media} 
		</if>
		<if test="cha_sub_launch != null and cha_sub_launch != ''">
			and cha_sub_launch = #{cha_sub_launch} 
		</if>
		<if test="cname != null and cname != ''">
			and cname = #{cname}
		</if>
		<if test="channelList != null and channelList != ''">
		 	and cname in ('${channelList}') 
		</if>
		
	</sql>
	<sql id="dn_channel_where_new">
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="cha_type != null and cha_type != ''">
			and cha_type in (${cha_type})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media})
		</if>
		<if test="cha_sub_launch != null and cha_sub_launch != ''">
			and cha_sub_launch in (${cha_sub_launch})
		</if>
		<if test="cname != null and cname != ''">
			and cname in (${cname})
		</if>
		<if test="channelList != null and channelList != ''">
			and cname in (${channelList})
		</if>

	</sql>
  	
  	<update id="updateMarketSysFind" parameterType="com.wbgame.pojo.MarketSysFind" >
	    update market_sys_find
	    set up_data_time = now(),
	      page_url = #{pageUrl,jdbcType=VARCHAR},
	      type = #{type,jdbcType=VARCHAR},
	      find_val = #{findVal,jdbcType=VARCHAR},
	      app_list_find = #{appListFind,jdbcType=VARCHAR},
	      channel_type_list_find = #{channelTypeListFind,jdbcType=VARCHAR},
	      media_channel_list_find_table = #{mediaChannelListFindTable,jdbcType=VARCHAR},
	      agent_list_find = #{agentListFind,jdbcType=VARCHAR},
	      put_user_list_find = #{putUserListFind,jdbcType=VARCHAR},
	      artist_list_find = #{artistListFind,jdbcType=VARCHAR},
	      title_list_find = #{titleListFind,jdbcType=VARCHAR},
	      cha_type_find = #{chaTypeFind,jdbcType=VARCHAR},
	      ad_art_group_id = #{artGroupId,jdbcType=VARCHAR},
	      account_group = #{account_group},
	      tf_sub_channel_list =#{tf_sub_channel_list,jdbcType=VARCHAR}
	    where `key` = #{key,jdbcType=INTEGER}
	</update>
  
  <insert id="insertMarketSysFind" parameterType="com.wbgame.pojo.MarketSysFind" >
    insert into market_sys_find (up_data_time, page_url, 
      type, find_val, app_list_find, 
      channel_type_list_find, media_channel_list_find_table, 
      agent_list_find, put_user_list_find, artist_list_find, 
      title_list_find,cha_type_find,ad_art_group_id,
      account_group,tf_sub_channel_list)
    values (now(), #{pageUrl,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{findVal,jdbcType=VARCHAR}, #{appListFind,jdbcType=VARCHAR}, 
      #{channelTypeListFind,jdbcType=VARCHAR}, #{mediaChannelListFindTable,jdbcType=VARCHAR}, 
      #{agentListFind,jdbcType=VARCHAR}, #{putUserListFind,jdbcType=VARCHAR}, #{artistListFind,jdbcType=VARCHAR}, 
      #{titleListFind,jdbcType=VARCHAR},#{chaTypeFind,jdbcType=VARCHAR},#{artGroupId,jdbcType=VARCHAR},
      #{account_group},#{tf_sub_channel_list})
  </insert>
  
    <delete id="deleteMarketSysFind" parameterType="com.wbgame.pojo.MarketSysFind" >
    delete from market_sys_find
    where `key` = #{key,jdbcType=INTEGER}
  </delete>
  
   <select id="selectMarketSysFind" resultType="com.wbgame.pojo.MarketSysFind" parameterType="com.wbgame.pojo.MarketSysFind" >
    select 
    `key`, up_data_time upDataTime, page_url pageUrl, type, find_val findVal, app_list_find appListFind, channel_type_list_find channelTypeListFind, 
    media_channel_list_find_table mediaChannelListFindTable, agent_list_find agentListFind, put_user_list_find putUserListFind, artist_list_find artistListFind, 
    title_list_find titleListFind,cha_type_find chaTypeFind,ad_art_group_id artGroupId, account_group,tf_sub_channel_list
    from market_sys_find
    where 1=1
    <if test="type != null and type != ''  ">
		and type  = #{type}
	</if>
	<if test="findVal != null and findVal != ''  ">
		and find_val  = #{findVal}
	</if>
	<if test="pageUrl != null and pageUrl != ''  ">
		and page_url  = #{pageUrl}
	</if>
			
	</select>
	
	<select id="selectfindVal" resultType="String" parameterType="String" >
    select 
     find_val findVal
    from market_sys_find
    where  type  = #{type}
  </select>

	<insert id="batchCopyMarketSysFindByPageUrl" parameterType="java.util.List">
		insert into market_sys_find (up_data_time, page_url,
      	`type`, find_val, app_list_find,
      	channel_type_list_find, media_channel_list_find_table,
      	agent_list_find, put_user_list_find, artist_list_find,
      	title_list_find,cha_type_find,ad_art_group_id,account_group,tf_sub_channel_list) VALUES
		<foreach collection="list" index="index" item="item" separator=",">
			(
			NOW(),#{item.pageUrl},#{item.type},#{item.findVal},
			#{item.appListFind},#{item.channelTypeListFind},#{item.mediaChannelListFindTable},
			#{item.agentListFind},#{item.putUserListFind},#{item.artistListFind},#{item.titleListFind},#{item.chaTypeFind},
			#{item.artGroupId},#{item.account_group},#{item.tf_sub_channel_list}
			)
		</foreach>
	</insert>

	<update id="batchEditMarketSysFindByAppIdOrPid" parameterType="com.wbgame.pojo.MarketSysFind">
		update market_sys_find set app_list_find =#{appListFind} ,channel_type_list_find =#{channelTypeListFind} where `key` in (${keys})
	</update>
  
 	<select id="selectDnChannelRat" resultType="com.wbgame.pojo.DnChannelInfo" >
	 	SELECT cha_id chaId,(100-cha_ratio)/100 chaRatio from dn_channel_info where cha_ratio != 100 and cha_ratio &gt;= 0 and cha_ratio &lt; 100
	</select>
 	<select id="selectDnChannelRatMap" resultType="com.wbgame.pojo.DnChannelInfo" >
	 	SELECT cha_id mapkey,cha_id chaId,(100-cha_ratio)/100 chaRatio from dn_channel_info where cha_ratio != 100 and cha_ratio &gt;= 0 and cha_ratio &lt; 100
	</select>

	<select id="countUmengChannelTotal" parameterType="Map" resultType="com.wbgame.pojo.UmengChannelTotalVo">
		select
		sum(aa.addnum) as addnum,
		sum(aa.actnum) as actnum,
		sum(bb.ad_revenue) as adv_fee,
		sum(aa.launch) launch,
		sum(bb.ad_revenue) ad_revenue,
		AVG(time_to_sec(daily_duration)) daily_duration,
		AVG(time_to_sec(daily_per_duration)) daily_per_duration,
		ROUND(AVG(aa.keep_num1),2) keep_num1,
		ROUND(AVG(aa.keep_num2),2) keep_num2,
		ROUND(AVG(aa.keep_num3),2) keep_num3,
		ROUND(AVG(aa.keep_num4),2) keep_num4,
		ROUND(AVG(aa.keep_num5),2) keep_num5,
		ROUND(AVG(aa.keep_num6),2) keep_num6,
		ROUND(AVG(aa.keep_num7),2) keep_num7,
		ROUND(AVG(aa.keep_num14),2) keep_num14,
		ROUND(AVG(aa.keep_num30),2) keep_num30
		from
		(select * from  umeng_channel_total where
		tdate BETWEEN #{start_date} AND #{end_date}
		<if test="all == 1">
			and appkey in (select DISTINCT appkey from umeng_income_show where ctype = 2)
		</if>
		) aa
		join app_info zz on aa.appkey = zz.umeng_key

		LEFT JOIN dn_game_cha_revenue_total bb
		ON aa.tdate = bb.tdate AND aa.appid = bb.appid AND aa.cname = bb.cha_id
		left join app_category ww on ww.id = zz.`app_category`
		where 1=1
		<if test="push_cha != null and push_cha != ''">
			and bb.push_cha like concat('%',#{push_cha},'%')
		</if>

		<if test="appkey != null and appkey != ''">
			and aa.appkey in (${appkey})
		</if>
		<if test="channel != null and channel != ''">
			and aa.cname in (${channel})
		</if>
		<if test="app_category != null and app_category != ''">
			and ww.id in (${app_category})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and bb.cha_media in (${cha_media})
		</if>
		order by aa.tdate asc
	</select>

	<select id="countUmengChannelTotalNew" parameterType="Map" resultType="com.wbgame.pojo.UmengChannelTotalVo">
		select
		sum(aa.add_num) as addnum,
		sum(aa.act_num) as actnum,
		sum(bb.ad_revenue) as adv_fee,
		sum(aa.start_num) launch,
		sum(bb.ad_revenue) ad_revenue,
		AVG(time_to_sec(aa.duration)) daily_duration,
		AVG(time_to_sec(aa.daily_per_duration)) daily_per_duration,
		ROUND(AVG(aa.product_keep1),2) keep_num1,
		ROUND(AVG(aa.product_keep2),2) keep_num2,
		ROUND(AVG(aa.product_keep3),2) keep_num3,
		ROUND(AVG(aa.product_keep4),2) keep_num4,
		ROUND(AVG(aa.product_keep5),2) keep_num5,
		ROUND(AVG(aa.product_keep6),2) keep_num6,
		ROUND(AVG(aa.product_keep7),2) keep_num7,
		ROUND(AVG(aa.product_keep14),2) keep_num14,
		ROUND(AVG(aa.product_keep30),2) keep_num30
		from
		(
			select * from umeng_user_channel_total where tdate BETWEEN #{start_date} AND #{end_date}
		) aa
		join app_info zz on aa.app_key = zz.umeng_key

		LEFT JOIN dn_game_cha_revenue_total bb
		ON aa.tdate = bb.tdate AND aa.appid = bb.appid AND aa.install_channel = bb.cha_id
		left join app_category ww on ww.id = zz.`app_category`
		where 1=1
		<if test="push_cha != null and push_cha != ''">
			and bb.push_cha like concat('%',#{push_cha},'%')
		</if>

		<if test="appid != null and appid != ''">
			and aa.appid in (${appid})
		</if>
		<if test="channel != null and channel != ''">
			and aa.install_channel in (${channel})
		</if>
		<if test="app_category != null and app_category != ''">
			and ww.id in (${app_category})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and bb.cha_media in (${cha_media})
		</if>
	</select>

	<insert id="updateDnChannelTotalOne" parameterType="com.wbgame.pojo.DnChannelTotal" >
	 insert into dn_channel_cost (tdate, appid, cha_id, invest_amount, adv_income)
    values
    (#{tdate,jdbcType=VARCHAR}, #{appid,jdbcType=VARCHAR}, #{chaId,jdbcType=VARCHAR}, 
      #{investAmount,jdbcType=DECIMAL}, #{advIncome,jdbcType=DECIMAL})
      ON DUPLICATE KEY UPDATE
		invest_amount=VALUES(invest_amount),
		
		adv_income=VALUES(adv_income)
  </insert>
  
  <insert id="updateDnChannelTotalThree" parameterType="com.wbgame.pojo.DnChannelTotal" >
	 insert into dn_app_income_total (tdate, appid, cname, invest_amount, adv_income, addnum, actnum)
    values
    (#{tdate,jdbcType=VARCHAR}, #{appid,jdbcType=VARCHAR}, #{chaId,jdbcType=VARCHAR}, 
      #{investAmount,jdbcType=DECIMAL}, #{advIncome,jdbcType=DECIMAL},#{addnum,jdbcType=DECIMAL}, #{actnum,jdbcType=DECIMAL})
      ON DUPLICATE KEY UPDATE
		invest_amount=VALUES(invest_amount),
		addnum=VALUES(addnum),
		actnum=VALUES(actnum),
		adv_income=VALUES(adv_income)
  </insert>
  
  <update id="updateDnChannelTotalTwo" parameterType="com.wbgame.pojo.DnChannelTotal" >
		 UPDATE `umeng_channel_total`
	SET `addnum` = #{addnum},
	 	`actnum` = #{actnum}
	WHERE
		`tdate` = #{tdate}
	and 
		`appid` = #{appid}
	and
		`cid` = #{cid}
  </update>

	  <insert id="insertApitjList" parameterType="com.wbgame.pojo.ApiInfoVo" >
	    insert into dnwx_cfg.api_tj_click_show (`id`, `t`, `pid`, `source`, `clicks`, `shows`)
	    values 
	     <foreach collection="list" item="li" separator=","> 
	    (#{li.id,jdbcType=VARCHAR}, #{li.t,jdbcType=VARCHAR}, #{li.pid,jdbcType=VARCHAR}, 
	      #{li.source,jdbcType=VARCHAR}, #{li.clicks,jdbcType=VARCHAR}, #{li.shows,jdbcType=VARCHAR})
	       </foreach>
	       ON DUPLICATE KEY UPDATE
			clicks = VALUES(clicks),
			shows = VALUES(shows)
	  </insert>

	<select id="selectApkUserDetailV2" resultType="java.util.HashMap">
		select * from umeng_apk_hour where date_format(tdate,"%Y-%m-%d") = #{tdate} and appid = #{appid}
	</select>

	<select id="getAppCategorys" resultType="com.wbgame.pojo.AppCategory">
		select id,name from app_category
	</select>

    <delete id="deleteDnGroupApp">
		delete from dn_group_app;
	</delete>

	<insert id="batchInsertDnGroupApp">
		<foreach collection="list" item="li" separator=";">
			insert into dn_group_app(id,groupId,appId,appname)
			select #{li.id},#{li.groupId},#{li.appId},app_name from app_info where id = #{li.appId}
		</foreach>
	</insert>

    <select id="countUmengKey" resultType="java.lang.Long">
		select count(1) from app_info where umeng_key = #{umeng_key}
	</select>

	<select id="selectAppInfoId" resultType="com.wbgame.pojo.AppInfoVo">
		select * from app_info where id = #{hiddenId}
	</select>
	
	<select id="selectShielduser" parameterType="java.util.Map" resultType="java.util.Map">
		select * 
		from yyhz_0308.dn_shielduser_info 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<if test="pid !=null and pid != ''">
			and pid = #{pid} 
		</if>
		order by createtime desc 
	</select>

    <select id="selectAppByCategory" resultType="java.util.Map">
		select id,app_name name from app_info where app_category = #{category}
	</select>

	<select id="selectAppByCategorys" resultType="java.util.Map" parameterType="java.util.Map">
		select id,app_name name from app_info where  1=1
		<if test="category !=null and category != ''">
			and app_category in (${category})
		</if>
	</select>

	<select id="selectAppCategoryByRelation" resultType="java.util.Map">
		SELECT
			ac.id AS app_category_id,
			ac.name AS app_category_name,
			tac.id AS two_app_category_id,
			tac.name AS two_app_category_name
		FROM
			app_category AS ac
				LEFT JOIN
			app_category_relation AS acr ON ac.id = acr.app_category_id
				LEFT JOIN
			two_app_category AS tac ON acr.two_app_category_id = tac.id

		WHERE 1=1
		<if test="category != null and category != ''">
			and ac.id in (${category})
		</if>
	</select>

	<delete id="deleteDnGroup">
		delete from  dn_group
	</delete>

	<insert id="batchInsertDnGroup">
		<foreach collection="list" item="li" separator=";">
			insert into dn_group(id,groupName,enabled,createUser,createTime)
			values (#{li.id},#{li.groupName},#{li.enabled},#{li.createUser},#{li.createTime})
		</foreach>
	</insert>

	<select id="getAppGroupList" resultType="com.wbgame.pojo.DnChannelVo" >
	 select id AS groupId, groupName from dn_group a where a.enabled = 1
	</select>

	<select id="getAppGroupAppIdList" resultType="com.wbgame.pojo.DnChannelVo">
		select groupId , appId from  dn_group_app
	</select>

	<select id="getAppRetentionsList" resultType="com.wbgame.pojo.mobile.AppRetentionsVo">
		select tdate,appid,appkey,add_num,
		IFNULL(keep1,0.0) keep1, IFNULL(keep2,0.0) keep2,IFNULL(keep3,0.0) keep3,
		IFNULL(keep4,0.0) keep4, IFNULL(keep5,0.0) keep5,IFNULL(keep6,0.0) keep6,
		IFNULL(keep7,0.0) keep7, IFNULL(keep14,0.0) keep14,IFNULL(keep30,0.0) keep30,
		ROUND(1+IFNULL(keep1,0.0)/100,2) keepnum1,
		ROUND(1+(IFNULL(keep1,0.0)+IFNULL(keep2,0.0))/100,2) keepnum2,
		ROUND(1+(IFNULL(keep1,0.0)+IFNULL(keep2,0.0)+IFNULL(keep3,0.0))/100,2) keepnum3,
		ROUND(1+(IFNULL(keep1,0.0)+IFNULL(keep2,0.0)+IFNULL(keep3,0.0)+IFNULL(keep4,0.0))/100,2) keepnum4,
		ROUND(1+(IFNULL(keep1,0.0)+IFNULL(keep2,0.0)+IFNULL(keep3,0.0)+IFNULL(keep4,0.0)+IFNULL(keep5,0.0))/100,2) keepnum5,
		ROUND(1+(IFNULL(keep1,0.0)+IFNULL(keep2,0.0)+IFNULL(keep3,0.0)+IFNULL(keep4,0.0)+IFNULL(keep5,0.0)+IFNULL(keep6,0.0))/100,2) keepnum6,
		ROUND(1+(IFNULL(keep1,0.0)+IFNULL(keep2,0.0)+IFNULL(keep3,0.0)+IFNULL(keep4,0.0)+IFNULL(keep5,0.0)+IFNULL(keep6,0.0)+IFNULL(keep7,0.0))/100,2) keepnum7,
		ROUND(1+(IFNULL(keep1,0.0)+IFNULL(keep2,0.0)+IFNULL(keep3,0.0)+IFNULL(keep4,0.0)+IFNULL(keep5,0.0)+IFNULL(keep6,0.0)+IFNULL(keep7,0.0)+IFNULL(keep14,0.0))/100,2) keepnum14,
		ROUND(1+(IFNULL(keep1,0.0)+IFNULL(keep2,0.0)+IFNULL(keep3,0.0)+IFNULL(keep4,0.0)+IFNULL(keep5,0.0)+IFNULL(keep6,0.0)+IFNULL(keep7,0.0)+IFNULL(keep14,0.0)+IFNULL(keep30,0.0))/100,2) keepnum30
		from umeng_user_app_keep_three where 1=1
		and tdate <![CDATA[>=]]> #{startTime} and  tdate <![CDATA[<=]]> #{endTime}
		<if test="appid !=null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="order !=null and order != ''">
			order by ${order}
		</if>
		<if test="order ==null or order == ''">
			order by add_num desc
		</if>
	</select>

    <select id="getDepartments" resultType="java.util.Map">
		select * from main_sys_department_v3
		where 1=1
		<if test="parentId != null and parentId != ''">
			and parent_id = #{parentId}
		</if>
	</select>

    <update id="updateAdvertKeeps">
        <foreach collection="list" item="it" separator=";">
			update umeng_channel_total set
			keep_num1 = #{it.keep_num1},
			keep_num2 = #{it.keep_num2},
			keep_num3 = #{it.keep_num3},
			keep_num4 = #{it.keep_num4},
			keep_num5 = #{it.keep_num5},
			keep_num6 = #{it.keep_num6},
			keep_num7 = #{it.keep_num7},
			keep_num14 = #{it.keep_num14},
			keep_num30 = #{it.keep_num30}
			where tdate = #{it.tdate} and appid = #{it.appid} and cname = #{it.cname}
		</foreach>
	</update>

	<select id="getChannelPlatforms" resultType="java.lang.String">
		select channel from app_channel_config GROUP BY channel
	</select>

	<update id="updateXyxId">
		update app_info set xyx_id = #{xyx_id}
			<if test="find_vals !=null">
				,find_vals=#{find_vals}
			</if>
		where id = #{appid}
	</update>
</mapper>