<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.DnwxInterfaceConfigMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.DnwxInterfaceConfig">
    <!--@mbg.generated-->
    <!--@Table dnwx_interface_config-->
    <id column="i_key" jdbcType="VARCHAR" property="iKey" />
    <result column="i_url" jdbcType="VARCHAR" property="iUrl" />
    <result column="i_ver" jdbcType="VARCHAR" property="iVer" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    i_key, i_url, i_ver
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from dnwx_interface_config
    where i_key = #{iKey,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from dnwx_interface_config
    where i_key = #{iKey,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.wbgame.pojo.DnwxInterfaceConfig">
    <!--@mbg.generated-->
    insert into dnwx_interface_config (i_key, i_url, i_ver
      )
    values (#{iKey,jdbcType=VARCHAR}, #{iUrl,jdbcType=VARCHAR}, #{iVer,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wbgame.pojo.DnwxInterfaceConfig">
    <!--@mbg.generated-->
    insert into dnwx_interface_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="iKey != null">
        i_key,
      </if>
      <if test="iUrl != null">
        i_url,
      </if>
      <if test="iVer != null">
        i_ver,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="iKey != null">
        #{iKey,jdbcType=VARCHAR},
      </if>
      <if test="iUrl != null">
        #{iUrl,jdbcType=VARCHAR},
      </if>
      <if test="iVer != null">
        #{iVer,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.DnwxInterfaceConfig">
    <!--@mbg.generated-->
    update dnwx_interface_config
    <set>
      <if test="iUrl != null">
        i_url = #{iUrl,jdbcType=VARCHAR},
      </if>
      <if test="iVer != null">
        i_ver = #{iVer,jdbcType=VARCHAR},
      </if>
    </set>
    where i_key = #{iKey,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.DnwxInterfaceConfig">
    <!--@mbg.generated-->
    update dnwx_interface_config
    set i_url = #{iUrl,jdbcType=VARCHAR},
      i_ver = #{iVer,jdbcType=VARCHAR}
    where i_key = #{iKey,jdbcType=VARCHAR}
  </update>
</mapper>