<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.ApiTransferKeyConfigMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.mobile.ApiTransferKeyConfig">
    <!--@mbg.generated-->
    <!--@Table api_transfer_key_config-->
    <id column="account" jdbcType="VARCHAR" property="account" />
    <id column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="access_key" jdbcType="VARCHAR" property="accessKey" />
    <result column="access_secret" jdbcType="VARCHAR" property="accessSecret" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="tappid" jdbcType="VARCHAR" property="tappid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_owner" jdbcType="VARCHAR" property="createOwner" />
    <result column="update_owner" jdbcType="VARCHAR" property="updateOwner" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    account, platform, `name`, access_key, access_secret, create_time, update_time, create_owner, 
    update_owner
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    account, platform, `name`, access_key, access_secret, create_time, update_time, create_owner,
    update_owner,token
    from api_transfer_key_config
    where account = #{account,jdbcType=VARCHAR}
      and platform = #{platform,jdbcType=VARCHAR}
  </select>
  <select id="selectByPrimaryKeyTwo" parameterType="map" resultMap="BaseResultMap">
    select
        taccount account, tappid, cname `name`, access_key, access_secret, token
    from adv_platform_app_info xx
    LEFT JOIN
        (select dn_account,channel,cname,hw_report_api_client_id as access_key,hw_report_api_client_secret as access_secret,token
                from api_packet_platform_account) yy
    ON xx.taccount=yy.dn_account AND xx.platform=yy.channel

    where platform = #{platform}
          and appid=#{appid}
          and packagename=#{packageName}

          and bindEndTime > CURRENT_DATE()
    ORDER BY createTime desc limit 1

  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--@mbg.generated-->
    delete from api_transfer_key_config
    where account = #{account,jdbcType=VARCHAR}
      and platform = #{platform,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.wbgame.pojo.mobile.ApiTransferKeyConfig">
    <!--@mbg.generated-->
    insert into api_transfer_key_config (account, platform, `name`,
      access_key, access_secret, create_time, 
      update_time, create_owner, update_owner
      )
    values (#{account,jdbcType=VARCHAR}, #{platform,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{accessKey,jdbcType=VARCHAR}, #{accessSecret,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createOwner,jdbcType=VARCHAR}, #{updateOwner,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wbgame.pojo.mobile.ApiTransferKeyConfig">
    <!--@mbg.generated-->
    insert into api_transfer_key_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="account != null">
        account,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="accessKey != null">
        access_key,
      </if>
      <if test="accessSecret != null">
        access_secret,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createOwner != null">
        create_owner,
      </if>
      <if test="updateOwner != null">
        update_owner,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="accessKey != null">
        #{accessKey,jdbcType=VARCHAR},
      </if>
      <if test="accessSecret != null">
        #{accessSecret,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwner != null">
        #{createOwner,jdbcType=VARCHAR},
      </if>
      <if test="updateOwner != null">
        #{updateOwner,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.mobile.ApiTransferKeyConfig">
    <!--@mbg.generated-->
    update api_transfer_key_config
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="accessKey != null">
        access_key = #{accessKey,jdbcType=VARCHAR},
      </if>
      <if test="accessSecret != null">
        access_secret = #{accessSecret,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwner != null">
        create_owner = #{createOwner,jdbcType=VARCHAR},
      </if>
      <if test="updateOwner != null">
        update_owner = #{updateOwner,jdbcType=VARCHAR},
      </if>
    </set>
    where account = #{account,jdbcType=VARCHAR}
      and platform = #{platform,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.mobile.ApiTransferKeyConfig">
    <!--@mbg.generated-->
    update api_transfer_key_config
    set `name` = #{name,jdbcType=VARCHAR},
      access_key = #{accessKey,jdbcType=VARCHAR},
      access_secret = #{accessSecret,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_owner = #{createOwner,jdbcType=VARCHAR},
      update_owner = #{updateOwner,jdbcType=VARCHAR}
    where account = #{account,jdbcType=VARCHAR}
      and platform = #{platform,jdbcType=VARCHAR}
  </update>

  <select id="selectApiTransferRecord" resultType="java.util.Map">
    select apk,version versionCode,fileMd5,package,title,account,channel,type,DATE_FORMAT(NOW(),'%Y-%m-%d %H:%i:%s') createStr,DATE_FORMAT(NOW(),'%Y-%m-%d %H:%i:%s') updateStr,create_owner,update_owner
    from api_transfer_record
    <where>
      <if test="platform != null and platform != ''">
        and channel = #{platform}
      </if>
      <if test="type != null ">
        and type = #{type}
      </if>
      <if test="title != null and title != ''">
        and title = #{title}
      </if>
      <if test="account != null and account != ''">
        and account = #{account}
      </if>
      <if test="package != null and package != ''">
        and package = #{package}
      </if>
    </where>
  </select>

  <insert id="createApiTransferRecord">
    insert into api_transfer_record(appid,package,title,account,type,channel,create_time,update_time,create_owner,update_owner,apk,version,fileMd5)
    values
    (#{appid},#{packageName},#{title},#{account},#{type},#{channel},#{createTime},#{updateTime},#{createOwner},#{updateOwner},#{apk},#{version},#{md5})
  </insert>

  <update id="updateApiTransferRecord">
    update api_transfer_record
    set title = #{title,jdbcType=VARCHAR},
    update_time = #{updateTime},
    update_owner = #{updateOwner,jdbcType=VARCHAR},
    apk = #{apk},
    version = #{version},
    fileMd5 = #{md5}
    where account = #{account,jdbcType=VARCHAR}
    and package = #{packageName,jdbcType=VARCHAR}
    and channel = #{channel,jdbcType=VARCHAR}
    and type = #{type,jdbcType=INTEGER}

  </update>

  <select id="selectTypes" resultType="java.util.Map">
    select id,`name` from api_transfer_type
    <where>
      <if test="channel != null and channel != ''">
        and channel = #{channel}
      </if>
      <if test="subId != null and subId != ''">
        and sub_id = #{subId}
      </if>
      <if test="type != null and type != ''">
        and `type` = #{type}
      </if>
    </where>
  </select>

    <insert id="batchInsertApiTransferRecords">
      insert into api_transfer_record(package,title,account,type,channel,create_time,update_time,create_owner,update_owner,version)
      values
      <foreach collection="list" item="it" separator=",">
        (#{it.packageName},#{it.title},#{it.account},#{it.type},#{it.channel},#{it.createTime},#{it.updateTime},#{it.createOwner},#{it.updateOwner},#{it.version,jdbcType=VARCHAR})
      </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2021-10-25-->
  <select id="selectByAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from api_transfer_key_config
    <where>
      <if test="account != null">
        and account=#{account,jdbcType=VARCHAR}
      </if>
      <if test="platform != null">
        and platform=#{platform,jdbcType=VARCHAR}
      </if>
      <if test="name != null">
        and `name`=#{name,jdbcType=VARCHAR}
      </if>
      <if test="accessKey != null">
        and access_key=#{accessKey,jdbcType=VARCHAR}
      </if>
      <if test="accessSecret != null">
        and access_secret=#{accessSecret,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        and create_time=#{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        and update_time=#{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="createOwner != null">
        and create_owner=#{createOwner,jdbcType=VARCHAR}
      </if>
      <if test="updateOwner != null">
        and update_owner=#{updateOwner,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

    <select id="selectAllOppoAccountsTwo" resultType="java.util.Map">
      SELECT dn_account,channel,cname,hw_report_api_client_id as access_key,hw_report_api_client_secret as access_secret
        FROM api_packet_platform_account where channel = 'oppo' GROUP BY hw_report_api_client_id
    </select>

  <update id="updateOppoAccountToken">
    update api_transfer_key_config
    set
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    update_owner = #{updateOwner,jdbcType=VARCHAR},
    token = #{token}
    where account = #{account,jdbcType=VARCHAR}
    and platform = #{platform,jdbcType=VARCHAR}
  </update>

  <select id="selectOneClassify" resultType="java.lang.String">
    select sub_id from api_transfer_type where id = #{id} and type = #{type} and channel = #{channel}
  </select>
</mapper>