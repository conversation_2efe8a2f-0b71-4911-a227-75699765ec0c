<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.SysconfigMapper">

	<select id="selectSysConfigVo" parameterType="java.util.Map" resultType="com.wbgame.pojo.SysConfigVo">
		select * from ssds_sysconfig limit 1
	</select>

	<insert id="insertSysConfigVo" parameterType="com.wbgame.pojo.SysConfigVo">
		insert into ssds_sysconfig (reward_title, reward_img, challenge_num,
		share_num, reward_num, reward_total
		)
		values (#{reward_title,jdbcType=VARCHAR}, #{reward_img,jdbcType=VARCHAR},
		#{challenge_num,jdbcType=INTEGER},
		#{share_num,jdbcType=INTEGER}, #{reward_num,jdbcType=INTEGER}, #{reward_total,jdbcType=INTEGER}
		)
	</insert>

	<select id="selectWxIconPushByAppid" parameterType="int" resultType="com.wbgame.pojo.WxIconPushVo">
		select * from wx_icon_push where appid = #{appid} and status = 1 order by level desc
	</select>
	<select id="selectXyxIconPushByAttr" parameterType="java.util.Map" resultType="com.wbgame.pojo.XyxIconPushVo">
		select * from xyx_icon_push where status = 1 
			
		<if test="appid != null and appid != ''">
			and appid = #{appid} 
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid} 
		</if>
		<if test="channel != null and channel != ''">
			and channel = #{channel} 
		</if>
		
		order by level desc
	</select>
	<select id="selectWxCustomMessageAll" resultType="com.wbgame.pojo.WxCustomMessageVo">
		select * from wx_custom_message
	</select>
	<select id="selectWxAppConfigMap" resultType="java.util.Map">
		select * from wx_app_config
	</select>
	
	<select id="selectWxIconGnameAll" resultType="java.util.Map">
		SELECT 
			CAST(id as char(20)) as id,
			imgtype,game_name
		FROM wx_icon_push where status != 2
	</select>
	
	<select id="selectWxVideoPushAll" parameterType="java.util.Map" resultType="com.wbgame.pojo.WxVideoPushVo">
		select * from wx_video_push where 1=1
		<if test="video_type != null and video_type != ''">
			and video_type = #{video_type}
		</if>
	</select>
	
	<select id="selectWxBonusConfig" resultType="java.util.Map">
		select * from wx_bonus_config
	</select>
	<select id="selectWxHelpConfig" resultType="java.util.Map">
		select * from wx_help_config
	</select>
	<select id="selectWxFdsConfig" resultType="java.util.Map">
		select * from wx_fds_config
	</select>
	
	<select id="selectWxSharePushAll" resultType="com.wbgame.pojo.WxSharePushVo">
		select * from wx_share_push where status = 1
	</select>
	<select id="selectWxPowerSwitch" resultType="java.util.Map">
		select * from wx_power_switch
	</select>
	<select id="selectWxPageSwitch" resultType="java.util.Map">
		select * from wx_page_switch
	</select>
	<select id="selectWxCustomTemplate" resultType="java.util.Map">
		select * from wx_custom_template
	</select>
	<select id="selectWxRedPackConfig" resultType="java.util.Map">
		select * from wx_redpack_config
	</select>
	<select id="selectWxMpPayConfig" resultType="java.util.Map">
		select * from wx_mppay_config
	</select>
	<select id="selectAdCodeConfig" resultType="java.util.Map">
		select * from toutiao_ad_code
	</select>
	
	<select id="selectWxMoneyMsgList" resultType="com.wbgame.pojo.MoneyMsgVo">
		select * from wx_money_log
	</select>
	
	<select id="selectCustomChannelList" parameterType="java.util.Map" resultType="java.lang.String">
		select cid from wx_channel_manage
	</select>
	
	<select id="selectPartnerByCreater" parameterType="java.lang.String" resultType="java.util.Map">
		
		SELECT pid,show_rate FROM wx_project_manage 
		where 1=1 
		<if test="creater != null and creater != ''">
			and (creater = #{creater} or partner = #{creater})
		</if>
	</select>
	
	<select id="selectAdMsgPer" resultType="string">
		SELECT zzz FROM aa_test
	</select>
	
</mapper>