<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.AdMapper">

	<insert id="insertTouTiaoAdTotal" parameterType="java.util.List">
		insert into toutiao_ad_total (
			appid,
			ad_slot_id,
			`show`,
			click,
			click_rate,
			cost,
			ecpm,
			region,
			stat_datetime,
			app_name,
			slot_name

		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.appid},
			#{li.ad_slot_id},
			#{li.show},
			#{li.click},
			#{li.click_rate},
			#{li.cost},
			#{li.ecpm},
			#{li.region},
			#{li.stat_datetime},
			#{li.app_name},
			#{li.slot_name})
		</foreach>	
	</insert>
	
	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>
	
	<insert id="insertUmengAdIncomeList" parameterType="java.util.List">
		replace into umeng_ad_income (
			tdate,appid,appname,appkey,channel,media,actnum,total_income,dau_arpu,gameName,
			banner_pv,plaque_pv,splash_pv,video_pv,banner_arpu,plaque_arpu,splash_arpu,
			video_arpu,banner_ecpm,plaque_ecpm,splash_ecpm,
			native_banner_ecpm,native_plaque_ecpm,native_splash_ecpm,
			video_ecpm,banner_show,plaque_show,splash_show,native_banner_show,
			native_plaque_show,native_splash_show,video_show,banner_income,
			plaque_income,splash_income,native_banner_income,native_plaque_income,
			native_splash_income,video_income,
			plaque_video_show,plaque_video_ecpm,plaque_video_income,
			msg_pv, msg_arpu, native_msg_ecpm, native_msg_show, native_msg_income,
			plaque_video_pv, plaque_video_arpu,addnum,avgnum,
			banner_click,plaque_click,splash_click,native_banner_click,native_plaque_click,
			native_splash_click,video_click,plaque_video_click,native_msg_click,
			system_splash_show,native_new_plaque_show,native_new_banner_show,suspend_icon_show,
			system_splash_pv,native_new_plaque_pv,native_new_banner_pv,suspend_icon_pv,
			system_splash_arpu,native_new_plaque_arpu,native_new_banner_arpu,suspend_icon_arpu,
			system_splash_ecpm,native_new_plaque_ecpm,native_new_banner_ecpm,suspend_icon_ecpm,
			system_splash_income,native_new_plaque_income,native_new_banner_income,suspend_icon_income,
			system_splash_click,native_new_plaque_click,native_new_banner_click,suspend_icon_click,
			native_plaque_pv,native_banner_pv,temp_id,temp_name,source,active_temp_id,active_temp_name,
			banner_request,plaque_request,splash_request,video_request,native_banner_request,native_plaque_request,
		    native_splash_request,plaque_video_request,native_msg_request,system_splash_request,
		    native_new_plaque_request,native_new_banner_request,suspend_icon_request,banner_fill,plaque_fill,
		    splash_fill,video_fill,native_banner_fill,native_plaque_fill,native_splash_fill,plaque_video_fill,
		    native_msg_fill,system_splash_fill,native_new_plaque_fill,native_new_banner_fill,suspend_icon_fill,
			show_splash_ad_active_cnt,show_plaque_ad_active_cnt,show_banner_ad_active_cnt,show_video_ad_active_cnt,
			show_msg_ad_active_cnt,show_icon_ad_active_cnt,click_splash_ad_active_cnt,click_plaque_ad_active_cnt,
			click_banner_ad_active_cnt,click_video_ad_active_cnt,click_msg_ad_active_cnt,click_icon_ad_active_cnt,
			show_total_ad_active_cnt,click_total_ad_active_cnt,ad_violation_type,large_ver
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},#{li.appid},#{li.appname},#{li.appkey},#{li.channel},#{li.media},#{li.actnum},
			#{li.total_income},#{li.dau_arpu},#{li.gameName},#{li.banner_pv},#{li.plaque_pv},
			#{li.splash_pv},#{li.video_pv},#{li.banner_arpu},#{li.plaque_arpu},
			#{li.splash_arpu},#{li.video_arpu},#{li.banner_ecpm},#{li.plaque_ecpm},
			#{li.splash_ecpm},#{li.native_banner_ecpm},#{li.native_plaque_ecpm},
			#{li.native_splash_ecpm},#{li.video_ecpm},#{li.banner_show},#{li.plaque_show},
			#{li.splash_show},#{li.native_banner_show},#{li.native_plaque_show},
			#{li.native_splash_show},#{li.video_show},#{li.banner_income},#{li.plaque_income},
			#{li.splash_income},#{li.native_banner_income},#{li.native_plaque_income},
			#{li.native_splash_income},#{li.video_income},
			#{li.plaque_video_show},#{li.plaque_video_ecpm},#{li.plaque_video_income},
			#{li.msg_pv},#{li.msg_arpu},#{li.native_msg_ecpm},#{li.native_msg_show},#{li.native_msg_income},
			#{li.plaque_video_pv},#{li.plaque_video_arpu},#{li.addnum},#{li.avgnum},
			#{li.banner_click},#{li.plaque_click},#{li.splash_click},#{li.native_banner_click},#{li.native_plaque_click},
			#{li.native_splash_click},#{li.video_click},#{li.plaque_video_click},#{li.native_msg_click},
			#{li.system_splash_show},#{li.native_new_plaque_show},#{li.native_new_banner_show},#{li.suspend_icon_show},
			#{li.system_splash_pv},#{li.native_new_plaque_pv},#{li.native_new_banner_pv},#{li.suspend_icon_pv},
			#{li.system_splash_arpu},#{li.native_new_plaque_arpu},#{li.native_new_banner_arpu},#{li.suspend_icon_arpu},
			#{li.system_splash_ecpm},#{li.native_new_plaque_ecpm},#{li.native_new_banner_ecpm},#{li.suspend_icon_ecpm},
			#{li.system_splash_income},#{li.native_new_plaque_income},#{li.native_new_banner_income},#{li.suspend_icon_income},
			#{li.system_splash_click},#{li.native_new_plaque_click},#{li.native_new_banner_click},#{li.suspend_icon_click},
			#{li.native_plaque_pv},#{li.native_banner_pv},#{li.temp_id},#{li.temp_name}
            <if test="li.source != null and li.source != ''">
                ,#{li.source}
            </if>
            <if test="li.source == null or li.source == ''">
                ,1
            </if>
			,#{li.active_temp_id},#{li.active_temp_name}
			,#{li.banner_request},#{li.plaque_request},#{li.splash_request},#{li.video_request},#{li.native_banner_request}
			,#{li.native_plaque_request},#{li.native_splash_request},#{li.plaque_video_request},#{li.native_msg_request}
			,#{li.system_splash_request},#{li.native_new_plaque_request},#{li.native_new_banner_request},#{li.suspend_icon_request}
			,#{li.banner_fill},#{li.plaque_fill},#{li.splash_fill},#{li.video_fill},#{li.native_banner_fill}
			,#{li.native_plaque_fill},#{li.native_splash_fill},#{li.plaque_video_fill},#{li.native_msg_fill}
			,#{li.system_splash_fill},#{li.native_new_plaque_fill},#{li.native_new_banner_fill},#{li.suspend_icon_fill}
			,#{li.show_splash_ad_active_cnt},#{li.show_plaque_ad_active_cnt},#{li.show_banner_ad_active_cnt},#{li.show_video_ad_active_cnt}
			,#{li.show_msg_ad_active_cnt},#{li.show_icon_ad_active_cnt},#{li.click_splash_ad_active_cnt},#{li.click_plaque_ad_active_cnt}
			,#{li.click_banner_ad_active_cnt},#{li.click_video_ad_active_cnt},#{li.click_msg_ad_active_cnt},#{li.click_icon_ad_active_cnt}
			,#{li.show_total_ad_active_cnt},#{li.click_total_ad_active_cnt},#{li.ad_violation_type},#{li.large_ver}
			)
		</foreach>	
	</insert>

	<insert id="insertOppoAdIncomeList" parameterType="java.util.List">
		replace into oppo_ad_income (
		tdate,appid,appname,appkey,channel,media,actnum,total_income,dau_arpu,
		banner_pv,plaque_pv,splash_pv,video_pv,banner_arpu,plaque_arpu,splash_arpu,
		video_arpu,banner_ecpm,plaque_ecpm,splash_ecpm,
		native_banner_ecpm,native_plaque_ecpm,native_splash_ecpm,
		video_ecpm,banner_show,plaque_show,splash_show,native_banner_show,
		native_plaque_show,native_splash_show,video_show,banner_income,
		plaque_income,splash_income,native_banner_income,native_plaque_income,
		native_splash_income,video_income,
		plaque_video_show,plaque_video_ecpm,plaque_video_income,
		msg_pv, msg_arpu, native_msg_ecpm, native_msg_show, native_msg_income,
		plaque_video_pv, plaque_video_arpu,addnum,avgnum,
		banner_click,plaque_click,splash_click,native_banner_click,native_plaque_click,
		native_splash_click,video_click,plaque_video_click,native_msg_click,
		system_splash_show,native_new_plaque_show,native_new_banner_show,suspend_icon_show,
		system_splash_pv,native_new_plaque_pv,native_new_banner_pv,suspend_icon_pv,
		system_splash_arpu,native_new_plaque_arpu,native_new_banner_arpu,suspend_icon_arpu,
		system_splash_ecpm,native_new_plaque_ecpm,native_new_banner_ecpm,suspend_icon_ecpm,
		system_splash_income,native_new_plaque_income,native_new_banner_income,suspend_icon_income,
		system_splash_click,native_new_plaque_click,native_new_banner_click,suspend_icon_click,
		native_plaque_pv,native_banner_pv,source,banner_request,plaque_request,
		splash_request,video_request,native_banner_request,native_plaque_request,native_splash_request,
		plaque_video_request,native_msg_request,system_splash_request,native_new_plaque_request,
		native_new_banner_request,suspend_icon_request,banner_fill,plaque_fill,splash_fill,video_fill,
		native_banner_fill,native_plaque_fill,native_splash_fill,plaque_video_fill,native_msg_fill,
		system_splash_fill,native_new_plaque_fill,native_new_banner_fill,suspend_icon_fill,
		show_splash_ad_active_cnt,show_plaque_ad_active_cnt,show_banner_ad_active_cnt,show_video_ad_active_cnt,
		show_msg_ad_active_cnt,show_icon_ad_active_cnt,click_splash_ad_active_cnt,click_plaque_ad_active_cnt,
		click_banner_ad_active_cnt,click_video_ad_active_cnt,click_msg_ad_active_cnt,click_icon_ad_active_cnt,
		show_total_ad_active_cnt,click_total_ad_active_cnt
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},#{li.appid},#{li.appname},#{li.appkey},#{li.channel},#{li.media},#{li.actnum},
			#{li.total_income},#{li.dau_arpu},#{li.banner_pv},#{li.plaque_pv},
			#{li.splash_pv},#{li.video_pv},#{li.banner_arpu},#{li.plaque_arpu},
			#{li.splash_arpu},#{li.video_arpu},#{li.banner_ecpm},#{li.plaque_ecpm},
			#{li.splash_ecpm},#{li.native_banner_ecpm},#{li.native_plaque_ecpm},
			#{li.native_splash_ecpm},#{li.video_ecpm},#{li.banner_show},#{li.plaque_show},
			#{li.splash_show},#{li.native_banner_show},#{li.native_plaque_show},
			#{li.native_splash_show},#{li.video_show},#{li.banner_income},#{li.plaque_income},
			#{li.splash_income},#{li.native_banner_income},#{li.native_plaque_income},
			#{li.native_splash_income},#{li.video_income},
			#{li.plaque_video_show},#{li.plaque_video_ecpm},#{li.plaque_video_income},
			#{li.msg_pv},#{li.msg_arpu},#{li.native_msg_ecpm},#{li.native_msg_show},#{li.native_msg_income},
			#{li.plaque_video_pv},#{li.plaque_video_arpu},#{li.addnum},#{li.avgnum},
			#{li.banner_click},#{li.plaque_click},#{li.splash_click},#{li.native_banner_click},#{li.native_plaque_click},
			#{li.native_splash_click},#{li.video_click},#{li.plaque_video_click},#{li.native_msg_click},
			#{li.system_splash_show},#{li.native_new_plaque_show},#{li.native_new_banner_show},#{li.suspend_icon_show},
			#{li.system_splash_pv},#{li.native_new_plaque_pv},#{li.native_new_banner_pv},#{li.suspend_icon_pv},
			#{li.system_splash_arpu},#{li.native_new_plaque_arpu},#{li.native_new_banner_arpu},#{li.suspend_icon_arpu},
			#{li.system_splash_ecpm},#{li.native_new_plaque_ecpm},#{li.native_new_banner_ecpm},#{li.suspend_icon_ecpm},
			#{li.system_splash_income},#{li.native_new_plaque_income},#{li.native_new_banner_income},#{li.suspend_icon_income},
			#{li.system_splash_click},#{li.native_new_plaque_click},#{li.native_new_banner_click},#{li.suspend_icon_click},
			#{li.native_plaque_pv},#{li.native_banner_pv}
			<if test="li.source != null and li.source != ''">
                ,#{li.source}
            </if>
            <if test="li.source == null or li.source == ''">
                ,1
            </if>
			,#{li.banner_request},#{li.plaque_request},#{li.splash_request},#{li.video_request},#{li.native_banner_request}
			,#{li.native_plaque_request},#{li.native_splash_request},#{li.plaque_video_request},#{li.native_msg_request}
			,#{li.system_splash_request},#{li.native_new_plaque_request},#{li.native_new_banner_request},#{li.suspend_icon_request}
			,#{li.banner_fill},#{li.plaque_fill},#{li.splash_fill},#{li.video_fill},#{li.native_banner_fill}
			,#{li.native_plaque_fill},#{li.native_splash_fill},#{li.plaque_video_fill},#{li.native_msg_fill}
			,#{li.system_splash_fill},#{li.native_new_plaque_fill},#{li.native_new_banner_fill},#{li.suspend_icon_fill}
			,#{li.show_splash_ad_active_cnt},#{li.show_plaque_ad_active_cnt},#{li.show_banner_ad_active_cnt},#{li.show_video_ad_active_cnt}
			,#{li.show_msg_ad_active_cnt},#{li.show_icon_ad_active_cnt},#{li.click_splash_ad_active_cnt},#{li.click_plaque_ad_active_cnt}
			,#{li.click_banner_ad_active_cnt},#{li.click_video_ad_active_cnt},#{li.click_msg_ad_active_cnt},#{li.click_icon_ad_active_cnt}
			,#{li.show_total_ad_active_cnt},#{li.click_total_ad_active_cnt}
			)
		</foreach>
	</insert>
	
	<select id="selectUmengAdcode" parameterType="java.util.Map" resultType="com.wbgame.pojo.UmengAdcodeVo">
		select * from umeng_adcode_list where 1=1 
		<if test="ad_code != null and ad_code != ''">
			and ad_code = #{ad_code} 
		</if>
		<if test="appkey != null and appkey != ''">
			and appkey in (${appkey})
		</if>
		<if test="adtype != null and adtype != ''">
			and adtype in (${adtype})
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel})
		</if>
		<if test="status != null and status != ''">
			and status = #{status}
		</if>
		
		order by ad_code asc
	</select>

	<select id="selectUmengAdcodeByAdcode" parameterType="java.util.Map" resultType="com.wbgame.pojo.UmengAdcodeVo">
		select * from umeng_adcode_list where 1=1 and ad_code in (${ad_code})
	</select>
	
	
	<select id="selectDnBillRevenue" resultType="java.util.Map">
		select '${tdate}' tdate,a.appid,b.channelTag cha_id,TRUNCATE(sum(a.money)/100,2) bill_revenue
		from wb_pay_info a,dnwx_client.wbgui_formconfig b
			where a.pid = b.pjId
			and a.createtime BETWEEN '${tdate} 00:00:00' and '${tdate} 23:59:59' 
			and a.orderstatus = 'SUCCESS'
			and a.pid is not null
			and b.channelTag != ''
		group by a.appid,b.channelTag 
	</select>
	<select id="selectDnBillRevenueAppRatio" resultType="java.util.Map">
		select '${tdate}' as tdate,appid,COUNT(DISTINCT imei) paynum,
			IFNULL(ROUND(SUM(
					CASE WHEN paytype='苹果支付' THEN money*0.7 
							 WHEN paytype='字节支付' and chaid='dyly' THEN money*0.2 
							 WHEN paytype='小米支付' and chaid='xiaomi' THEN money*0.45 
							 WHEN paytype='小米支付' and chaid='xiaomimj' THEN money*0.65 
							 WHEN paytype='oppo支付' and chaid='oppo' THEN money*0.45 
							 WHEN paytype='oppo支付' and chaid='oppomj' THEN money*0.85 
							 WHEN paytype='oppo支付' and chaid='oppoml' THEN money*0.85 
							 WHEN paytype='vivo支付' and chaid='vivo' THEN money*0.45 
							 WHEN paytype='vivo支付' and chaid='vivoml' THEN money*0.85 
							 WHEN paytype='华为支付' and chaid='huawei' THEN money*0.45 
							 WHEN paytype='微信支付' THEN money*0.98 
							 WHEN paytype='支付宝' THEN money*0.98 
					ELSE money END)/100, 2), 0) pay_revenue 
		from wb_pay_info where createtime BETWEEN '${tdate} 00:00:00' and '${tdate} 23:59:59' AND orderstatus='SUCCESS'
			and appid not in (select id from app_info where app_category=17) 
		group by tdate,appid
				
	</select>
	
	<select id="selectDnBillRevenueAppChannelRatio" resultType="java.util.Map">
		select '${tdate}' as tdate,appid,chaid channel,COUNT(DISTINCT imei) paynum,
			IFNULL(ROUND(SUM(
					CASE WHEN paytype='苹果支付' THEN money*0.7 
							 WHEN paytype='字节支付' and chaid='dyly' THEN money*0.2 
							 WHEN paytype='小米支付' and chaid='xiaomi' THEN money*0.45 
							 WHEN paytype='小米支付' and chaid='xiaomimj' THEN money*0.65 
							 WHEN paytype='oppo支付' and chaid='oppo' THEN money*0.45 
							 WHEN paytype='oppo支付' and chaid='oppomj' THEN money*0.85 
							 WHEN paytype='oppo支付' and chaid='oppoml' THEN money*0.85 
							 WHEN paytype='vivo支付' and chaid='vivo' THEN money*0.45 
							 WHEN paytype='vivo支付' and chaid='vivoml' THEN money*0.85 
							 WHEN paytype='华为支付' and chaid='huawei' THEN money*0.45 
							 WHEN paytype='微信支付' THEN money*0.98 
							 WHEN paytype='支付宝' THEN money*0.98 
					ELSE money END)/100, 2), 0) pay_revenue 
		from wb_pay_info where createtime BETWEEN '${tdate} 00:00:00' and '${tdate} 23:59:59' AND orderstatus='SUCCESS'
			and appid not in (select id from app_info where app_category=17) 
		group by tdate,appid,chaid
				
	</select>
	
	<select id="selectProjectidChannelMap" resultType="java.util.Map">
		SELECT 
			aa.gameName as gname,aa.pjId as projectid,aa.channelTag as cha_id,
			aa.versionName as ver,aa.channel as cid,bb.channel,
			cc.appid,dd.cha_id channel_logo,ee.type_name cha_type_name
		FROM dnwx_client.wbgui_formconfig aa
		JOIN dnwx_client.wbgui_channel bb on aa.channel = bb.id
		JOIN dnwx_client.wbgui_gametype cc on aa.typeName = cc.gameId
		LEFT JOIN dn_channel_info dd on aa.channelTag = dd.cha_id
		LEFT JOIN dn_channel_type ee on dd.cha_type = ee.type_id
	</select>
	
	<select id="selectDauAndProjectMap" parameterType="java.util.Map" resultType="java.util.Map">
		select concat(${cont}) mapkey,zzz.*,
			SUM(by_newcount) act_num,SUM(push_newcount) add_num
		from 
			(SELECT 
				yy.by_date tdate,yy.by_priid pid,yy.by_newcount,zz.push_newcount,
				aa.gameName gname,aa.versionName ver,aa.channel cid,bb.channel,
				cc.appid,cc.gameName typeName,cc.platform os
			FROM alone_dau_total yy 
			JOIN buyu_newcount_total zz on yy.by_date=zz.by_date and yy.by_priid=zz.by_priid
			JOIN dnwx_client.wbgui_formconfig aa on yy.by_priid = aa.pjId
			JOIN dnwx_client.wbgui_channel bb on aa.channel = bb.id
			JOIN dnwx_client.wbgui_gametype cc on aa.typeName = cc.gameId
			WHERE yy.by_date BETWEEN #{start_date} AND #{end_date}  
			<if test="appid != null and appid != ''">
				and appid = #{appid} 
			</if>
			<if test="pid != null and pid != ''">
				and yy.by_priid = #{pid} 
			</if>
			) zzz
		GROUP BY ${group}
	</select>
 	
	<insert id="insertApkProductStatsList" parameterType="java.util.List">
		insert into apk_product_stats(
			tdate,
			product_id,
			projectid,
			ver,
			channel,
			act_num,
			add_num
		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.product_id},
			#{li.projectid},
			#{li.ver},
			#{li.channel},
			#{li.act_num},
			#{li.add_num})
		</foreach>	
	</insert>
	
	<update id="updateApkProductUserTotal" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update apk_user_total set 
	    		<choose>
	    			<when test="li.sevenDau != null and li.sevenDau != 0">
	    				sevenDau = #{li.sevenDau}
	    			</when>
	    			<when test="li.sevenTimes != null and li.sevenTimes != 0">
	    				sevenTimes = #{li.sevenTimes}
	    			</when>
	    			<otherwise>
	    				thirthDau = #{li.thirthDau}
	    			</otherwise>
	    		</choose>
	    	where appid = #{li.appid} and createtime = #{li.createtime}
    	</foreach>
	</update>
	
	
	<insert id="insertLandingPageContent" parameterType="java.util.List" >
		insert into landing_page_content(
			file_name,
			file_catalog,
			template_id,
			down_url,
			down_text,
			title,
			file_host,
			user_id,
			created
		) values
		<foreach collection="list" item="lpc" separator=",">
			(#{lpc.file_name},
			#{lpc.file_catalog},
			#{lpc.template_id},
			#{lpc.down_url},
			#{lpc.down_text},
			#{lpc.title},
			#{lpc.file_host},
			#{lpc.user_id},
			NOW())
		</foreach>
		
	</insert>
	
	<update id="updateLandingPageContent" parameterType="com.wbgame.pojo.LandingPageContentVo" >
		update landing_page_content set 
			template_id = #{template_id},
			down_url = #{down_url},
			down_text = #{down_text},
			title = #{title} 
		where file_name = #{file_name} and file_catalog = #{file_catalog}
	</update>
	
	<delete id="deleteLandingPageContent" parameterType="com.wbgame.pojo.LandingPageContentVo" >
    	delete from landing_page_content where file_name = #{file_name} and file_catalog = #{file_catalog}
    </delete>
    
    
    <insert id="insertGDTAdwordReport" parameterType="java.util.List" >
    	insert into gdt_adwork_report (
			AppId,
			Date,
			MemberId,
			MediumName,
			PlacementId,
			PlacementName,
			PlacementType,
			RequestCount,
			Pv,
			Click,
			ClickRate,
			Revenue,
			ECPM,
			FillRate,
			isSummary
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.AppId},
			#{li.Date},
			#{li.MemberId},
			#{li.MediumName},
			#{li.PlacementId},
			#{li.PlacementName},
			#{li.PlacementType},
			#{li.RequestCount},
			#{li.Pv},
			#{li.Click},
			#{li.ClickRate},
			#{li.Revenue},
			#{li.ECPM},
			#{li.FillRate},
			#{li.isSummary})
		</foreach>
		
	</insert>
	<insert id="insertGDTMedium" parameterType="com.wbgame.pojo.custom.GDTWorkMediumVo">
		insert into gdt_adwork_medium(
			member_id,
			app_id,
			medium_id,
			medium_name,
			industry_id,
			os,
			keywords,
			description,
			package_name,
			market_status,
			market_url,
			jail_break,
			createtime
		) values(
			#{member_id},
			#{app_id},
			#{medium_id},
			#{medium_name},
			#{industry_id},
			#{os},
			#{keywords},
			#{description},
			#{package_name},
			#{market_status},
			#{market_url},
			#{jail_break},
			now()
		)
	</insert>
	<update id="updateGDTMedium" parameterType="com.wbgame.pojo.custom.GDTWorkMediumVo">
		update gdt_adwork_medium set 
			medium_name = #{medium_name},
			industry_id = #{industry_id},
			os = #{os},
			keywords = #{keywords},
			description = #{description},
			package_name = #{package_name},
			market_status = #{market_status},
			market_url = #{market_url},
			jail_break = #{jail_break}
		where member_id = #{member_id} and medium_id = #{medium_id}
	</update>
	<insert id="insertGDTPlacement" parameterType="com.wbgame.pojo.custom.GDTWorkMediumVo">
		insert into gdt_adwork_placement(
			member_id,
			app_id,
			medium_id,
			placement_id,
			placement_name,
			placement_type,
			placement_sub_type,
			ad_pull_mode,
			createtime
		) values(
			#{member_id},
			#{app_id},
			#{medium_id},
			#{placement_id},
			#{placement_name},
			#{placement_type},
			#{placement_sub_type},
			#{ad_pull_mode},
			now()
		)
	</insert>
	<update id="updateGDTPlacement" parameterType="com.wbgame.pojo.custom.GDTWorkMediumVo">
		update gdt_adwork_placement set 
			placement_name = #{placement_name}
		where member_id = #{member_id} and medium_id = #{medium_id} and placement_id = #{placement_id}
	</update>
	
    <insert id="insertPostBlackList" parameterType="java.util.List" >
    	insert into black_device_info (
			tdate,
			productid,
			projectid,
			imei,
			channel,
			from_ip,
			gametimes,
			logincount
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.TDATE},
			#{li.PRODUCTID},
			#{li.PROJECTID},
			#{li.IMEI},
			#{li.CHANNEL},
			#{li.FROM_IP},
			#{li.GAMETIMES},
			#{li.LOGINCOUNT})
			
		</foreach>
	</insert>
	
	<insert id="insertApkProductOutflow" parameterType="java.util.List" >
    	insert into apk_product_outflow (
			tdate,
			product_id,
			projectid,
			act_num,
			outflow_num
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.product_id},
			#{li.projectid},
			#{li.act_num},
			#{li.outflow_num})
			
		</foreach>
	</insert>
	<insert id="insertApkProductSilent" parameterType="java.util.List" >
    	insert into apk_product_silent (
			tdate,
			tname,
			product_id,
			projectid,
			add_num,
			silent_num
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.tname},
			#{li.product_id},
			#{li.projectid},
			#{li.add_num},
			#{li.silent_num})
			
		</foreach>
	</insert>
	
	<insert id="insertHongBaoConfig" parameterType="com.wbgame.pojo.HongBaoInfoVo" >
		insert into wx_hongbao_info(
			mch_id,
			wxappid,
			nick_name,
			send_name,
			wishing,
			act_name,
			remark,
			`key`,
			zspath,
			logo_imgurl,
			share_content,
			share_url,
			share_imgurl,
			appname,
			packname,
			appsecret,
			call_url
		) values(
			#{mch_id},
			#{wxappid},
			#{nick_name},
			#{send_name},
			#{wishing},
			#{act_name},
			#{remark},
			#{key},
			#{zspath},
			#{logo_imgurl},
			#{share_content},
			#{share_url},
			#{share_imgurl},
			#{appname},
			#{packname},
			#{appsecret},
			#{call_url}
		)
	</insert>
	<update id="updateHongBaoConfig" parameterType="com.wbgame.pojo.HongBaoInfoVo" >
		update wx_hongbao_info set 
			mch_id = #{mch_id},
			wxappid = #{wxappid},
			nick_name = #{nick_name},
			send_name = #{send_name},
			wishing = #{wishing},
			act_name = #{act_name},
			remark = #{remark},
			`key` = #{key},
			zspath = #{zspath},
			logo_imgurl = #{logo_imgurl},
			share_content = #{share_content},
			share_url = #{share_url},
			share_imgurl = #{share_imgurl},
			appname = #{appname},
			appsecret = #{appsecret},
			call_url = #{call_url}
		where packname = #{packname}
		
	</update>
	
	<insert id="updateUmengPushCha" parameterType="java.util.List" >
		insert into umeng_push_cha(
			tdate,
			push_cha,
			adv_fee
		) values
		<foreach collection="list" item="lpc" separator=",">
			(#{lpc.tdate},
			#{lpc.push_cha},
			#{lpc.adv_fee})
		</foreach>
		ON DUPLICATE KEY UPDATE 
		adv_fee=VALUES(adv_fee)
	</insert>
	
	<!-- google活跃数据  -->
	<update id="updateGoogleFeishuAct" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update google_feishu_result set islogin = 1
	    	where pid = #{li.PRODUCTID} and imei = #{li.IMEI}
    	</foreach>
	</update>
	<insert id="insertGoogleFeishuTotal" parameterType="java.util.List">
		insert into ad_google_feishu_total(
			tdate,
			pid,
			act_num,
			add_num,
			msg_count,
			banner_count,
			plaque_count,
			splash_count,
			video_count
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.mapkey},
			#{li.act_num},
			#{li.add_num},
			#{li.msg_count},
			#{li.banner_count},
			#{li.plaque_count},
			#{li.splash_count},
			#{li.video_count})
		</foreach>
	</insert>

	<!-- maoyou活跃用户  -->
	<update id="updateMaoyouAct" parameterType="java.util.List">
		update click_track_push set islogin = 1
	    where idfa in (
			<foreach collection="list" item="li" separator=",">
				 '${li}'
	    	</foreach>
    	)
	</update>
	<insert id="insertMaoyouTotal" parameterType="java.util.List">
		insert into ad_maoyou_total(
			tdate,
			pid,
			channel,
			act_num,
			add_num,
			video_count

		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.pid},
			#{li.mapkey},
			#{li.act_num},
			#{li.add_num},
			#{li.video_count})
		</foreach>
	</insert>
	<update id="updateMaoyouTotalKeepBatch" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update ad_maoyou_total set 
				<choose>
	    			<when test="li.keepnum0 != null and li.keepnum0 != ''">
	    				keep2 = #{li.keepnum0}
	    			</when>
	    			<when test="li.keepnum1 != null and li.keepnum1 != ''">
	    				keep3 = #{li.keepnum1}
	    			</when>
	    			<when test="li.keepnum2 != null and li.keepnum2 != ''">
	    				keep4 = #{li.keepnum2}
	    			</when>
	    			<when test="li.keepnum3 != null and li.keepnum3 != ''">
	    				keep7 = #{li.keepnum3}
	    			</when>
	    			<otherwise>
	    				keep2 = #{li.keepnum0}
	    			</otherwise>
	    		</choose>
	    	where tdate = #{li.tdate} and pid = #{li.pid} and channel = #{li.channel}
    	</foreach>
	</update>
	
	<!-- 广告数据信息统计报表 -->
	<select id="selectAdvChannelFeeTotal" parameterType="java.util.Map" 
					resultType="com.wbgame.pojo.custom.AdvFeeVo">
					
		SELECT 
			${group},
			sum(give_fee) give_fee,
			sum(adv_fee) adv_fee,
			sum(dau) dau,
			sum(new_count) show_count
		FROM ADV_CHANNEL_FEE_NEW_REPORT
		WHERE adv_dt BETWEEN #{start_date} AND #{end_date} 
			<include refid="advChannelFeeWhere"/>
		GROUP BY ${group}
		ORDER BY ${group}
	</select>
	<select id="selectAdvChannelFeeTotalTwo" parameterType="java.util.Map" 
					resultType="com.wbgame.pojo.custom.AdvFeeVo">
					
		SELECT
			bb.${group} as mapkey,
			bb.${group},
			bb.give_fee,
			bb.new_count,
			bb.cpa,
			bb.dau,
			bb.get_fee,
			bb.arpu,
			bb.adv_fee,
			bb.adv_dau,
			aa.give_fee_total,
			aa.get_fee_total,
			aa.adv_total,
			round(aa.get_fee_total + aa.adv_total,2) total_fee,
			(CASE
				WHEN aa.give_fee_total = 0 
				THEN 0 
				ELSE round((aa.get_fee_total + aa.adv_total)/ aa.give_fee_total * 100,2) 
			 END
			) input_output_ratio
		FROM
			(	SELECT
					${group},
					sum(give_fee) give_fee,
					sum(new_count) new_count,
					(
						CASE
						WHEN sum(new_count) = 0 
						THEN 0 
						ELSE
							round(sum(give_fee) / sum(new_count),2)
						END
					) cpa,
					sum(dau) dau,
					sum(adv_fee) adv_fee,
					(
						CASE
						WHEN sum(dau) = 0 
						THEN 0 
						ELSE
							round(sum(adv_fee) / sum(dau),2)
						END
					) adv_dau,
					sum(get_fee) get_fee,
					(
						CASE
						WHEN sum(dau) = 0 
						THEN 0
						ELSE
							round(sum(get_fee) / sum(dau),2)
						END
					) arpu
				FROM 
					ADV_CHANNEL_FEE_NEW_REPORT
				WHERE adv_dt BETWEEN #{start_date} AND #{end_date} 
					<include refid="advChannelFeeWhere"/>
				GROUP BY ${group}
			) bb
			JOIN
			(	SELECT
					${group},
					sum(give_fee) give_fee_total,
					sum(get_fee) get_fee_total,
					sum(adv_fee) adv_total
				FROM
					ADV_CHANNEL_FEE_NEW_REPORT
				WHERE adv_dt BETWEEN #{start_date} AND #{end_date} 
					<include refid="advChannelFeeWhere"/>
				GROUP BY ${group}
			) aa
		ON bb.${group} = aa.${group} 
		ORDER BY 
			<if test="group != null and group == 'adv_dt'">
				bb.adv_dt ASC,
			</if>
			bb.adv_fee DESC
	</select>
	<select id="selectAdvChannelFeeTotalThree" parameterType="java.util.Map" 
					resultType="com.wbgame.pojo.custom.AdvFeeVo">
					
		SELECT
			'展示汇总' as mapkey,
			'展示汇总' as ${group},
			sum(bb.give_fee) give_fee,
			sum(bb.new_count) new_count,
			round(sum(bb.give_fee)/sum(bb.new_count),3) cpa,
			sum(bb.dau) dau,
			sum(bb.get_fee) get_fee,
			round(sum(bb.get_fee)/sum(bb.dau),3) arpu,
			sum(bb.adv_fee) adv_fee,
			round(sum(bb.adv_fee)/sum(bb.dau),3) adv_dau,
			sum(aa.give_fee_total) give_fee_total,
			sum(aa.get_fee_total) get_fee_total,
			sum(aa.adv_total) adv_total,
			round(sum(aa.get_fee_total) + sum(aa.adv_total),2) total_fee,
			(CASE
				WHEN sum(aa.give_fee_total) = 0 
				THEN 0 
				ELSE round((sum(aa.get_fee_total) + sum(aa.adv_total))/ sum(aa.give_fee_total) * 100,2) 
			 END
			) input_output_ratio
		FROM
			(	SELECT
					${group},
					sum(give_fee) give_fee,
					sum(new_count) new_count,
					(
						CASE
						WHEN sum(new_count) = 0 
						THEN 0 
						ELSE
							round(sum(give_fee) / sum(new_count),2)
						END
					) cpa,
					sum(dau) dau,
					sum(adv_fee) adv_fee,
					(
						CASE
						WHEN sum(dau) = 0 
						THEN 0 
						ELSE
							round(sum(adv_fee) / sum(dau),2)
						END
					) adv_dau,
					sum(get_fee) get_fee,
					(
						CASE
						WHEN sum(dau) = 0 
						THEN 0
						ELSE
							round(sum(get_fee) / sum(dau),2)
						END
					) arpu
				FROM 
					ADV_CHANNEL_FEE_NEW_REPORT
				WHERE adv_dt BETWEEN #{start_date} AND #{end_date} 
					<include refid="advChannelFeeWhere"/>
				GROUP BY ${group}
			) bb
			JOIN
			(	SELECT
					${group},
					sum(give_fee) give_fee_total,
					sum(get_fee) get_fee_total,
					sum(adv_fee) adv_total
				FROM
					ADV_CHANNEL_FEE_NEW_REPORT
				WHERE adv_dt BETWEEN #{start_date} AND #{end_date} 
					<include refid="advChannelFeeWhere"/>
				GROUP BY ${group}
			) aa
		ON bb.${group} = aa.${group} 
	</select>
	<sql id="advChannelFeeWhere">
		<if test="appid != null and appid != ''">
			and appid = #{appid}
		</if>
		<if test="push_cha != null and push_cha != ''">
			and push_cha like concat('%',#{push_cha},'%')
		</if>
		<if test="pri_id != null and pri_id != ''">
			and pri_id = #{pri_id}
		</if>
		<if test="cha_type != null and cha_type != ''">
			and cha_type = #{cha_type}
		</if>
		<if test="os_type != null and os_type != ''">
			and os_type = #{os_type}
		</if>
		<if test="appids != null and appids != ''">
			and appid in (${appids}) 
		</if>
	</sql>
	
	<insert id="insertAdvChannelFeeTotalNewList" parameterType="java.util.List">
		insert into adv_channel_fee_new_report (
			adv_dt,
			push_cha,
			appid,
			pri_id,
			cha_type,
			os_type,
			give_fee,
			new_count,
			dau,
			get_fee,
			adv_fee

		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.adv_dt},
			#{li.push_cha},
			#{li.appid},
			#{li.pri_id},
			#{li.cha_type},
			#{li.os_type},
			#{li.give_fee},
			#{li.new_count},
			#{li.dau},
			#{li.get_fee},
			#{li.adv_fee})
		</foreach>	
	</insert>
	<select id="selectAdvChannelFeeTotalNew" parameterType="java.util.Map" 
					resultType="com.wbgame.pojo.custom.AdvFeeVo">
					
		SELECT
			bb.*,
			aa.give_fee_total,
			aa.get_fee_total,
			aa.adv_total,
			round(aa.get_fee_total + aa.adv_total,2) total_fee,
			(CASE
				WHEN aa.give_fee_total = 0 
				THEN 0 
				ELSE round((aa.get_fee_total + aa.adv_total) / aa.give_fee_total * 100,2) 
			 END
			) input_output_ratio
		FROM
			(	SELECT
					<if test="date_group != null and date_group != ''">adv_dt,</if>
					${group},
					sum(give_fee) give_fee,
					sum(new_count) new_count,
					(
						CASE
						WHEN sum(new_count) = 0 
						THEN 0 
						ELSE
							round(sum(give_fee) / sum(new_count),2)
						END
					) cpa,
					sum(dau) dau,
					sum(adv_fee) adv_fee,
					(
						CASE
						WHEN sum(dau) = 0 
						THEN 0 
						ELSE
							round(sum(adv_fee) / sum(dau),2)
						END
					) adv_dau,
					sum(get_fee) get_fee,
					(
						CASE
						WHEN sum(dau) = 0 
						THEN 0
						ELSE
							round(sum(get_fee) / sum(dau),2)
						END
					) arpu,
					(
						CASE
						WHEN sum(dau) = 0 
						THEN 0 
						ELSE
							round((sum(get_fee) + sum(adv_fee)) / sum(dau),2)
						END
					) total_arpu
				FROM 
					ADV_CHANNEL_FEE_NEW
				WHERE adv_dt BETWEEN #{start_date} AND #{end_date} 
					<include refid="advChannelFeeWhere"/>
				GROUP BY <if test="date_group != null and date_group != ''">adv_dt,</if>${group}
			) bb
			JOIN
			(	SELECT
					${group},
					sum(give_fee) give_fee_total,
					sum(get_fee) get_fee_total,
					sum(adv_fee) adv_total
				FROM
					ADV_CHANNEL_FEE_NEW
				WHERE adv_dt BETWEEN '2018-01-01' AND #{end_date} 
					<include refid="advChannelFeeWhere"/>
				GROUP BY ${group}
			) aa
		ON ${group_match}
		ORDER BY bb.${group} ASC,bb.adv_fee DESC
	</select>
	<select id="selectAdvChannelFeeTotalHaiwai" parameterType="java.util.Map" 
					resultType="com.wbgame.pojo.custom.AdvFeeVo">
					
		SELECT
			bb.*,
			aa.give_fee_total,
			aa.get_fee_total,
			aa.adv_total,
			round(aa.get_fee_total + aa.adv_total,2) total_fee,
			(CASE
				WHEN aa.give_fee_total = 0 
				THEN 0 
				ELSE round((aa.get_fee_total + aa.adv_total) / aa.give_fee_total * 100,2) 
			 END
			) input_output_ratio
		FROM
			(	SELECT
					adv_dt,
					appid,
					${group},
					sum(give_fee) give_fee,
					sum(new_count) new_count,
					(
						CASE
						WHEN sum(new_count) = 0 
						THEN 0 
						ELSE
							round(sum(give_fee) / sum(new_count),2)
						END
					) cpa,
					sum(dau) dau,
					sum(adv_fee) adv_fee,
					(
						CASE
						WHEN sum(dau) = 0 
						THEN 0 
						ELSE
							round(sum(adv_fee) / sum(dau),2)
						END
					) adv_dau,
					sum(get_fee) get_fee,
					(
						CASE
						WHEN sum(dau) = 0 
						THEN 0
						ELSE
							round(sum(get_fee) / sum(dau),2)
						END
					) arpu,
					(
						CASE
						WHEN sum(dau) = 0 
						THEN 0 
						ELSE
							round((sum(get_fee) + sum(adv_fee)) / sum(dau),2)
						END
					) total_arpu
				FROM 
					ADV_CHANNEL_FEE_NEW
				WHERE adv_dt BETWEEN #{start_date} AND #{end_date} 
					<include refid="advChannelFeeWhere"/>
				GROUP BY adv_dt,appid,${group}
			) bb
			JOIN
			(	SELECT
					${group},
					sum(give_fee) give_fee_total,
					sum(get_fee) get_fee_total,
					sum(adv_fee) adv_total
				FROM
					ADV_CHANNEL_FEE_NEW
				WHERE adv_dt BETWEEN '2018-01-01' AND #{end_date} 
					<include refid="advChannelFeeWhere"/>
				GROUP BY ${group}
			) aa
		ON ${group_match}
		ORDER BY bb.adv_dt ASC,bb.adv_fee DESC
	</select>
	
	<insert id="insertBoxGamepayMonthList" parameterType="java.util.Map">
		insert into ${tableName}(
			paycode,
			lsn,
			imsi,
			pid,
			mmid,
			appid,
			paytype,
			price,
			payresult,
			renturncode,
			paytime,
			createtime,
			mac,
			imei,
			sign,
			ran
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.paycode},
			#{li.lsn},
			#{li.imsi},
			#{li.pid},
			#{li.mmid},
			#{li.appid},
			#{li.paytype},
			#{li.price},
			#{li.payresult},
			#{li.renturncode},
			#{li.paytime},
			#{li.createtime},
			#{li.mac},
			#{li.imei},
			#{li.sign},
			#{li.ran})
		</foreach>
	</insert>
	
	
	<insert id="insertProductModelList" parameterType="java.util.List">
		insert into alone_dau_model(
			tdate,
			productid,
			num,
			model
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.param1},
			#{li.param2},
			#{li.param3})
		</foreach>
	</insert>
	
	<insert id="insertRedPackAuditList" parameterType="java.util.Map">
		insert into red_pack_audit_info (
			c_date,
			c_appid,
			c_pid,
			c_price,
			${daynum}
		) values
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.param1},
			#{li.param2},
			'0.1',
			#{li.param3})
		</foreach>
		ON DUPLICATE KEY UPDATE
		${daynum} = VALUES(${daynum})
	</insert>
	
	<update id="updateAppChannelForKeepList" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update app_channel_appid set two_rate=#{li.two_rate} 
				where tdate=#{li.tdate} and channel=#{li.channel} and appid_key=#{li.appid_key}
    	</foreach>
	</update>
	<update id="updateAppChannelForIncomeList" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update app_channel_appid set 
				income=#{li.income},
				ban_income=#{li.ban_income},
				screen_income=#{li.screen_income},
				traffic_income=#{li.traffic_income},
				video_income=#{li.video_income},
				ban_pv=#{li.ban_pv},
				screen_pv=#{li.screen_pv},
				traffic_pv=#{li.traffic_pv},
				video_pv=#{li.video_pv},
				open_income=#{li.open_income},
				open_pv=#{li.open_pv},
				brick_income=#{li.brick_income},
				brick_pv=#{li.brick_pv},
				orimsg_income=#{li.orimsg_income},
				orimsg_pv=#{li.orimsg_pv}
			where tdate=#{li.tdate} and channel=#{li.channel} and appid=#{li.appid} 
    	</foreach>
	</update>
	
	<!-- 合作方产品收支查询  -->
	<select id="selectPartnerApp" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="partner_app_sql"/>
	</select>
	<select id="selectPartnerAppSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			sum(xx.addnum) addnum,sum(xx.dau) dau,
			sum(xx.invest_amount) invest_amount,
			sum(xx.adv_income) adv_income,
			sum(xx.billing_income) billing_income,
			truncate(SUM(xx.invest_amount)/SUM(xx.addnum),2) cpa,
			truncate(SUM(xx.adv_income)/SUM(xx.dau),2) adv_arpu,
			truncate((SUM(xx.adv_income)+SUM(xx.billing_income))-SUM(xx.invest_amount),2) day_profit,
			truncate((SUM(xx.adv_income)+SUM(xx.billing_income))/SUM(xx.invest_amount)*100,2) day_roi 
		from (<include refid="partner_app_sql"/>) xx
	</select>
	
	<sql id="partner_app_sql">
		select aa.*,bb.invest_amount,bb.adv_income,bb.billing_income,
			truncate(bb.invest_amount/aa.addnum,2) cpa,
			truncate(bb.adv_income/aa.dau,2) adv_arpu,
			truncate((bb.adv_income+bb.billing_income)-bb.invest_amount,2) day_profit,
			truncate((bb.adv_income+bb.billing_income)/bb.invest_amount*100,2) day_roi,
			cc.sumInvestAmount,cc.sumAdvIncome,cc.sumBillingIncome,
			cc.sumAmount,cc.totalROI
		from 
		(SELECT ${group1},SUM(addnum) addnum,SUM(actnum) dau 
			FROM umeng_channel_total t1 
			<!-- 处理未审核日期过滤  -->
			<if test="check != null and check != ''">
			 	JOIN
				(SELECT tdate date,ischeck FROM umeng_channel_check where ischeck = 1 GROUP BY tdate) t2
				ON t1.tdate = t2.date 
			</if>
			
			where tdate BETWEEN #{start_date} AND #{end_date} 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="cname != null and cname != ''">
				and cname like concat('%',#{cname},'%') 
			</if>
			<if test="1==1">
				and (appid != 37862 or tdate &lt; '2020-07-01') 
				and (appid != 37930 or tdate &lt; '2020-09-01') 
				and (appid != 37892 or tdate &lt; '2020-09-01') 
				and (appid != 37944 or tdate &lt; '2020-10-01') 
				and (appid != 37986 or tdate &lt; '2020-11-01') 
				and (appid != 37949 or tdate &lt; '2020-11-01') 
				and (appid != 37976 or tdate &lt; '2021-01-01') 
				and (appid != 38055 or tdate &lt; '2021-02-21') 
				and (appid != 37820 or tdate &lt; '2021-03-01') 
				and (appid != 38016 or tdate >= '2021-06-01') 
				
				and (appid != 37822 or tdate &lt; '2021-08-01') 
				and (appid != 37821 or tdate &lt; '2021-08-01') 
				and (appid != 37844 or tdate &lt; '2021-08-01') 
				and (appid != 37880 or tdate &lt; '2021-08-01') 
				and (appid != 37899 or tdate &lt; '2021-08-01') 
				and (appid != 37900 or tdate &lt; '2021-08-01') 
				and (appid != 37954 or tdate &lt; '2021-08-01') 
				and (appid != 37938 or tdate &lt; '2021-08-01') 
				and (appid != 37955 or tdate &lt; '2021-08-01') 
				
				and (appid != 38016 or tdate &lt; '2021-08-01') 
				and (appid != 38097 or tdate &lt; '2021-08-01') 
				and (appid != 38106 or tdate &lt; '2021-08-01') 
				and (appid != 38109 or tdate &lt; '2021-08-01') 
				
				and (appid != 38160 or tdate &lt; '2021-09-25') 
				and (appid != 38291 or tdate &lt; '2021-09-25') 
				and (appid != 37959 or tdate &lt; '2021-09-07') 
				and (appid != 37965 or tdate &lt; '2021-09-07') 
				and (appid != 37969 or tdate &lt; '2021-09-07') 
				and (appid != 37972 or tdate &lt; '2021-09-07') 
				
				and (appid != 37886 or tdate >= '2021-09-19') 
			</if>
			GROUP BY ${group1}
		) aa
			
		LEFT JOIN
		(SELECT ${group2},SUM(invest_amount) invest_amount,
				SUM(adv_income) adv_income,SUM(billing_income) billing_income 
			FROM umeng_channel_cost t1 
			<if test="check != null and check != ''">
			 	JOIN
				(SELECT tdate date,ischeck FROM umeng_channel_check where ischeck = 1 GROUP BY tdate) t2
				ON t1.tdate = t2.date 
			</if>
			
			where tdate BETWEEN #{start_date} AND #{end_date} 
			<include refid="partner_sql"/> 
			GROUP BY ${group2}
		) bb
		${group_match2}
		
		LEFT JOIN
		(SELECT ${group3},SUM(invest_amount) sumInvestAmount,
			SUM(adv_income) sumAdvIncome,SUM(billing_income) sumBillingIncome,
			truncate(SUM(adv_income)+SUM(billing_income),2) sumAmount,
			CONCAT(truncate((SUM(adv_income)+SUM(billing_income))/SUM(invest_amount)*100,2),'%') totalROI
			FROM umeng_channel_cost t1 
			<if test="check != null and check != ''">
			 	JOIN
				(SELECT tdate date,ischeck FROM umeng_channel_check where ischeck = 1 GROUP BY tdate) t2
				ON t1.tdate = t2.date 
			</if>
			
			where 1=1 <include refid="partner_sql"/> 
			<if test="1==1">
				and (appid != 37862 or tdate &lt; '2020-07-01') 
				and (appid != 37930 or tdate &lt; '2020-09-01') 
				and (appid != 37892 or tdate &lt; '2020-09-01') 
				and (appid != 37944 or tdate &lt; '2020-10-01') 
				and (appid != 37986 or tdate &lt; '2020-11-01') 
				and (appid != 37949 or tdate &lt; '2020-11-01') 
				and (appid != 37976 or tdate &lt; '2021-01-01') 
				and (appid != 38055 or tdate &lt; '2021-02-21') 
				and (appid != 37820 or tdate &lt; '2021-03-01') 
				and (appid != 38016 or tdate >= '2021-06-01') 
				
				and (appid != 37822 or tdate &lt; '2021-08-01') 
				and (appid != 37821 or tdate &lt; '2021-08-01') 
				and (appid != 37844 or tdate &lt; '2021-08-01') 
				and (appid != 37880 or tdate &lt; '2021-08-01') 
				and (appid != 37899 or tdate &lt; '2021-08-01') 
				and (appid != 37900 or tdate &lt; '2021-08-01') 
				and (appid != 37954 or tdate &lt; '2021-08-01') 
				and (appid != 37938 or tdate &lt; '2021-08-01') 
				and (appid != 37955 or tdate &lt; '2021-08-01') 
				
				and (appid != 38016 or tdate &lt; '2021-08-01') 
				and (appid != 38097 or tdate &lt; '2021-08-01') 
				and (appid != 38106 or tdate &lt; '2021-08-01') 
				and (appid != 38109 or tdate &lt; '2021-08-01') 
				
				and (appid != 38160 or tdate &lt; '2021-09-25') 
				and (appid != 38291 or tdate &lt; '2021-09-25') 
				and (appid != 37959 or tdate &lt; '2021-09-07') 
				and (appid != 37965 or tdate &lt; '2021-09-07') 
				and (appid != 37969 or tdate &lt; '2021-09-07') 
				and (appid != 37972 or tdate &lt; '2021-09-07') 
				
				and (appid != 37886 or tdate >= '2021-09-19') 
			</if>
			GROUP BY ${group3}
		) cc
		${group_match3}
		
		<if test="order_str != null and order_str != ''">
		 	order by ${order_str} 
		</if>
	</sql>
	<sql id="partner_sql">
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
	</sql>
	
	<!-- 合作方产品收支查询.haiwai2  -->
	<select id="selectPartnerAppHaiwai2" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="partner_app_haiwai2_sql"/>
	</select>
	<select id="selectPartnerAppSumHaiwai2" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			sum(xx.addnum) addnum,sum(xx.dau) dau,
			sum(xx.invest_amount) invest_amount,
			sum(xx.adv_income) adv_income,
			sum(xx.billing_income) billing_income,
			truncate(SUM(xx.invest_amount)/SUM(xx.addnum),2) cpa,
			truncate(SUM(xx.adv_income)/SUM(xx.dau),2) adv_arpu,
			truncate((SUM(xx.adv_income)+SUM(xx.billing_income))-SUM(xx.invest_amount),2) day_profit,
			truncate((SUM(xx.adv_income)+SUM(xx.billing_income))/SUM(xx.invest_amount)*100,2) day_roi 
		from (<include refid="partner_app_haiwai2_sql"/>) xx
	</select>
	
	<sql id="partner_app_haiwai2_sql">
		select aa.*,bb.invest_amount,bb.adv_income,bb.billing_income,
			truncate(bb.invest_amount/aa.addnum,2) cpa,
			truncate(bb.adv_income/aa.dau,2) adv_arpu,
			truncate((bb.adv_income+bb.billing_income)-bb.invest_amount,2) day_profit,
			truncate((bb.adv_income+bb.billing_income)/bb.invest_amount*100,2) day_roi,
			cc.sumInvestAmount,cc.sumAdvIncome,cc.sumBillingIncome,
			cc.sumAmount,cc.totalROI
		from 
		(SELECT ${group1},SUM(addnum) addnum,SUM(actnum) dau 
			FROM umeng_channel_total t1 
			<!-- 处理未审核日期过滤  -->
			<if test="check != null and check != ''">
			 	JOIN
				(SELECT tdate date,ischeck FROM umeng_channel_check_haiwai2 where ischeck = 1 GROUP BY tdate) t2
				ON t1.tdate = t2.date 
			</if>
			
			where tdate BETWEEN #{start_date} AND #{end_date} 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="cname != null and cname != ''">
				and cname like concat('%',#{cname},'%') 
			</if>
			<if test="1==1">
				and (appid != 37862 or tdate &lt; '2020-07-01') 
				and (appid != 37930 or tdate &lt; '2020-09-01') 
				and (appid != 37892 or tdate &lt; '2020-09-01') 
				and (appid != 37944 or tdate &lt; '2020-10-01') 
				and (appid != 37986 or tdate &lt; '2020-11-01') 
				and (appid != 37949 or tdate &lt; '2020-11-01') 
				and (appid != 37976 or tdate &lt; '2021-01-01') 
				and (appid != 38055 or tdate &lt; '2021-02-21') 
				and (appid != 37820 or tdate &lt; '2021-03-01') 
				and (appid != 38016 or tdate >= '2021-06-01') 
				
				and (appid != 37822 or tdate &lt; '2021-08-01') 
				and (appid != 37821 or tdate &lt; '2021-08-01') 
				and (appid != 37844 or tdate &lt; '2021-08-01') 
				and (appid != 37880 or tdate &lt; '2021-08-01') 
				and (appid != 37899 or tdate &lt; '2021-08-01') 
				and (appid != 37900 or tdate &lt; '2021-08-01') 
				and (appid != 37954 or tdate &lt; '2021-08-01') 
				and (appid != 37938 or tdate &lt; '2021-08-01') 
				and (appid != 37955 or tdate &lt; '2021-08-01') 
				
				and (appid != 38016 or tdate &lt; '2021-08-01') 
				and (appid != 38097 or tdate &lt; '2021-08-01') 
				and (appid != 38106 or tdate &lt; '2021-08-01') 
				and (appid != 38109 or tdate &lt; '2021-08-01') 
				
				and (appid != 38160 or tdate &lt; '2021-09-25') 
				and (appid != 38291 or tdate &lt; '2021-09-25') 
				and (appid != 37959 or tdate &lt; '2021-09-07') 
				and (appid != 37965 or tdate &lt; '2021-09-07') 
				and (appid != 37969 or tdate &lt; '2021-09-07') 
				and (appid != 37972 or tdate &lt; '2021-09-07') 
				
				and (appid != 37886 or tdate >= '2021-09-19') 
			</if>
			GROUP BY ${group1}
		) aa
			
		LEFT JOIN
		(SELECT ${group2},SUM(invest_amount) invest_amount,
				SUM(adv_income) adv_income,SUM(billing_income) billing_income 
			FROM umeng_channel_cost t1 
			<if test="check != null and check != ''">
			 	JOIN
				(SELECT tdate date,ischeck FROM umeng_channel_check_haiwai2 where ischeck = 1 GROUP BY tdate) t2
				ON t1.tdate = t2.date 
			</if>
			
			where tdate BETWEEN #{start_date} AND #{end_date} 
			<include refid="partner_sql"/> 
			GROUP BY ${group2}
		) bb 
		${group_match2}
		
		LEFT JOIN
		(SELECT ${group3},SUM(invest_amount) sumInvestAmount,
			SUM(adv_income) sumAdvIncome,SUM(billing_income) sumBillingIncome,
			truncate(SUM(adv_income)+SUM(billing_income),2) sumAmount,
			CONCAT(truncate((SUM(adv_income)+SUM(billing_income))/SUM(invest_amount)*100,2),'%') totalROI
			FROM umeng_channel_cost t1 
			<if test="check != null and check != ''">
			 	JOIN
				(SELECT tdate date,ischeck FROM umeng_channel_check_haiwai2 where ischeck = 1 GROUP BY tdate) t2
				ON t1.tdate = t2.date 
			</if>
			
			where 1=1 <include refid="partner_sql"/> 
			<if test="1==1">
				and (appid != 37862 or tdate &lt; '2020-07-01') 
				and (appid != 37930 or tdate &lt; '2020-09-01') 
				and (appid != 37892 or tdate &lt; '2020-09-01') 
				and (appid != 37944 or tdate &lt; '2020-10-01') 
				and (appid != 37986 or tdate &lt; '2020-11-01') 
				and (appid != 37949 or tdate &lt; '2020-11-01') 
				and (appid != 37976 or tdate &lt; '2021-01-01') 
				and (appid != 38055 or tdate &lt; '2021-02-21') 
				and (appid != 37820 or tdate &lt; '2021-03-01') 
				and (appid != 38016 or tdate >= '2021-06-01') 
				
				and (appid != 37822 or tdate &lt; '2021-08-01') 
				and (appid != 37821 or tdate &lt; '2021-08-01') 
				and (appid != 37844 or tdate &lt; '2021-08-01') 
				and (appid != 37880 or tdate &lt; '2021-08-01') 
				and (appid != 37899 or tdate &lt; '2021-08-01') 
				and (appid != 37900 or tdate &lt; '2021-08-01') 
				and (appid != 37954 or tdate &lt; '2021-08-01') 
				and (appid != 37938 or tdate &lt; '2021-08-01') 
				and (appid != 37955 or tdate &lt; '2021-08-01') 
				
				and (appid != 38016 or tdate &lt; '2021-08-01') 
				and (appid != 38097 or tdate &lt; '2021-08-01') 
				and (appid != 38106 or tdate &lt; '2021-08-01') 
				and (appid != 38109 or tdate &lt; '2021-08-01') 
				
				and (appid != 38160 or tdate &lt; '2021-09-25') 
				and (appid != 38291 or tdate &lt; '2021-09-25') 
				and (appid != 37959 or tdate &lt; '2021-09-07') 
				and (appid != 37965 or tdate &lt; '2021-09-07') 
				and (appid != 37969 or tdate &lt; '2021-09-07') 
				and (appid != 37972 or tdate &lt; '2021-09-07') 
				
				and (appid != 37886 or tdate >= '2021-09-19') 
			</if>
			GROUP BY ${group3}
		) cc 
		${group_match3}
		
		<if test="order_str != null and order_str != ''">
		 	order by ${order_str} 
		</if>
	</sql>
	
	<!-- 合作方投放明细查询 -->
	<select id="selectPartnerInvestInfo" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="partner_invest_sql"/>
	</select>
	<select id="selectPartnerInvestInfoSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			sum(xx.investAmount) investAmount 
		from (<include refid="partner_invest_sql"/>) xx
	</select>
	<sql id="partner_invest_sql">
		
		SELECT t1.* FROM
			(SELECT
				${group},
				IFNULL(sum(invest_amount),0) investAmount 
			FROM umeng_channel_cost c1,dn_channel_info c2 
			
			WHERE c1.cha_id=c2.cha_id and tdate BETWEEN #{sdate} AND #{edate} 
				<if test="appid != null and appid != ''">
					and appid in (${appid}) 
				</if>
				<if test="cha_media != null and cha_media != ''">
					and cha_media in (${cha_media}) 
				</if>
			<!-- voodoo账户特殊处理 -->
			and appid not in (37820,37821,37822,37844,37880,37886,37899,37900,37938,37954,37955,37959,37965,37969,37972) 
			group by ${group}) t1
		
		<!-- 处理未审核日期过滤  -->
		<if test="check != null and check != ''">
		 	JOIN
			(SELECT tdate date,ischeck FROM partner_invest_check where ischeck = 1 GROUP BY tdate) t2
			ON t1.tdate = t2.date 
		</if>
	</sql>
	
	<!-- 合作方变现明细查询 -->
	<select id="selectPartnerRevenueInfo" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="partner_revenue_sql"/>
	</select>
	<select id="selectPartnerRevenueInfoSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			TRUNCATE(sum(xx.revenue),2) revenue,
			TRUNCATE(sum(xx.revenue)/sum(xx.pv)*1000,2) ecpm 
		from (<include refid="partner_revenue_sql"/>) xx
	</select>
	<sql id="partner_revenue_sql">
		
		SELECT
			${group},
			TRUNCATE(sum(t1.revenue),2) revenue,
			TRUNCATE(sum(t1.pv),0) pv,
			TRUNCATE(sum(t1.revenue)/sum(t1.pv)*1000,2) ecpm
		FROM dn_cha_cash_total t1 
			<!-- 处理未审核日期过滤  -->
			<if test="check != null and check != ''">
			 	JOIN
				(SELECT tdate,ischeck FROM partner_revenue_check where ischeck = 1 GROUP BY tdate) t2
				ON t1.date = t2.tdate 
			</if>
		
		WHERE date BETWEEN #{sdate} AND #{edate} 
			<if test="appid != null and appid != ''">
				and dnappid in (${appid}) 
			</if>
			<if test="agent != null and agent != ''">
				and agent in (${agent})
			</if>
		and app_id != '0' 
		and dnappid is not null 
		<!-- voodoo账户特殊处理 -->
		and dnappid not in (37820,37821,37822,37844,37880,37886,37899,37900,37938,37954,37955,37959,37965,37969,37972) 
		
		group by ${group} 
	</sql>
	
	
	<!-- 新增活跃汇总查询数据  -->
	<insert id="insertDnChaDauTotal" parameterType="java.lang.String">
		INSERT INTO dn_cha_dau_total
			(tdate,pid,channel_active,channel_add,gname,version,appid,type_name,sys,cha_type,cha_media,cha_sub_launch,channel_logo,cha_type_name)
		SELECT 
			yy.by_date tdate,yy.by_priid pid,
			SUM(yy.by_newcount) channel_active,IFNULL(SUM(zz.push_newcount),0) channel_add,
			aa.gameName gname,aa.versionName version,
			cc.appid,cc.gameName type_name,cc.platform sys,
			dd.cha_type,dd.cha_media,dd.cha_sub_launch,dd.cha_id channel_logo,
			ee.type_name cha_type_name
		FROM alone_dau_total yy 
		LEFT JOIN buyu_newcount_total zz on yy.by_date=zz.by_date and yy.by_priid=zz.by_priid
		LEFT JOIN dnwx_client.wbgui_formconfig aa on yy.by_priid = aa.pjId
		LEFT JOIN dnwx_client.wbgui_channel bb on aa.channel = bb.id
		LEFT JOIN dnwx_client.wbgui_gametype cc on aa.typeName = cc.gameId
		LEFT JOIN dn_channel_info dd on aa.channelTag = dd.cha_id
		LEFT JOIN dn_channel_type ee on dd.cha_type = ee.type_id
		WHERE yy.by_date BETWEEN #{tdate} AND #{tdate}    
		GROUP BY pid
	</insert>
	<!-- 新增活跃汇总查询数据.v2  -->
	<insert id="insertDnChaDauTotalTwo" parameterType="java.lang.String">
		REPLACE INTO dn_cha_dau_total_two
			(tdate,pid,channel_active,channel_add,gname,version,appid,type_name,sys,cha_type,cha_media,cha_sub_launch,channel_logo,cha_type_name)
		SELECT 
			yy.tdate,yy.projectid,
			SUM(yy.act_num) channel_active,IFNULL(SUM(yy.add_num),0) channel_add,
			aa.gameName gname,aa.versionName version,
			cc.appid,cc.gameName type_name,cc.platform sys,
			dd.cha_type,dd.cha_media,dd.cha_sub_launch,yy.chaid channel_logo,
			ee.type_name cha_type_name
		FROM product_chaid_total yy 
		LEFT JOIN dnwx_client.wbgui_formconfig aa on yy.projectid = aa.pjId
		LEFT JOIN dnwx_client.wbgui_gametype cc on aa.typeName = cc.gameId
		LEFT JOIN dn_channel_info dd on yy.chaid = dd.cha_id
		LEFT JOIN dn_channel_type ee on dd.cha_type = ee.type_id
		WHERE yy.tdate BETWEEN #{tdate} AND #{tdate}    
		GROUP BY projectid,chaid
	</insert>
	
	<select id="selectDnChaDauTotal" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dncha_dau_sql"/>
	</select>
	<select id="selectDnChaDauTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			sum(xx.channel_add) channel_add,
			sum(xx.channel_active) channel_active,
			sum(xx.product_add) product_add,
			sum(xx.product_active) product_active
		from (<include refid="dncha_dau_sql"/>) xx
	</select>
	<sql id="dncha_dau_sql">
		select a.*,(avg_price * a.channel_active) price from ${tableName} a
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid} 
		</if>
		<if test="cha_type != null and cha_type != ''">
			and cha_type in (${cha_type})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media})
		</if>
		<if test="cha_sub_launch != null and cha_sub_launch != ''">
			and channel_logo in (${cha_sub_launch}) 
		</if>
		and channel_active >= 10 
		
		<if test="order_str != null and order_str != ''">
		 	order by ${order_str} 
		</if>
	</sql>


	
	<!-- cps合作方报表查看  -->
	<select id="selectCpsPartnerReport" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="cps_partner_sql_1"/>
	</select>
	<select id="selectCpsPartnerReportSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			sum(xx.addnum) addnum,sum(xx.dau) dau,
			sum(xx.pus_income) pus_income,sum(xx.adv_income) adv_income,
			truncate(SUM(xx.pus_income)/SUM(xx.dau),2) pus_dau_arpu,
			truncate(SUM(xx.adv_income)/SUM(xx.dau),2) adv_dau_arpu,
			truncate((SUM(xx.pus_income)+SUM(xx.adv_income)),2) sum_income,
			truncate((SUM(xx.pus_income)+SUM(xx.adv_income))/SUM(xx.dau),2) dau_arpu
		from (<include refid="cps_partner_sql_1"/>) xx
	</select>
	
	<sql id="cps_partner_sql_1">
		select`tdate`, `appid`, `pid`, `appname`,  type_name typeName,cname,cc.cha_media,
		sum(`dau`) dau, sum(`addnum`) addnum, sum(`adv_income`) adv_income, sum(`billing_income`) pus_income,
		`status`,
		truncate(sum(bb.billing_income)/sum(bb.dau),2) pus_dau_arpu,
		truncate(sum(bb.adv_income)/sum(bb.dau),2) adv_dau_arpu,
		truncate((sum(bb.billing_income)+sum(bb.adv_income)),2) sum_income,
		truncate((sum(bb.billing_income)+sum(bb.adv_income))/sum(bb.dau),2) dau_arpu
		from 
		dn_cps_report bb  left join dn_channel_info cc on bb.cname = cc.cha_id 
		where tdate BETWEEN #{start_date} AND #{end_date} 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="cname != null and cname != ''">
				and cname in ('${cname}')
			</if>
			<if test="pid != null and pid != ''">
				and pid = #{pid}
			</if>
			<if test="status != null and status != ''">
				and `status` = #{status}
			</if>
		GROUP BY ${group}
		
		<if test="order_str != null and order_str != ''">
		 	order by ${order_str} 
		</if>
	</sql>
	
	
	<sql id="cps_partner_sql">
		select aa.*,bb.billing_income pus_income,bb.adv_income,
			truncate(bb.billing_income/aa.dau,2) pus_dau_arpu,
			truncate(bb.adv_income/aa.dau,2) adv_dau_arpu,
			truncate((bb.billing_income+bb.adv_income),2) sum_income,
			truncate((bb.billing_income+bb.adv_income)/aa.dau,2) dau_arpu
		from 
		(SELECT tdate,appid ${group1},appname,SUM(addnum) addnum,SUM(actnum) dau 
			FROM umeng_channel_total where tdate BETWEEN #{start_date} AND #{end_date} 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="cname != null and cname != ''">
				and cname in ('${cname}')
			</if>
			GROUP BY tdate,appid ${group1}) aa
			
		LEFT JOIN
		(SELECT tdate,appid ${group2},SUM(billing_income) billing_income,SUM(adv_income) adv_income 
			FROM umeng_channel_cost where tdate BETWEEN #{start_date} AND #{end_date} 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			GROUP BY tdate,appid ${group2}) bb
		ON aa.tdate = bb.tdate AND aa.appid = bb.appid ${group_match2}
		
		<if test="order_str != null and order_str != ''">
		 	order by ${order_str} 
		</if>
	</sql>
	
	<update id="updateCpsReportStatus" parameterType="java.util.Map" >
		UPDATE dn_cps_report 
		SET 
		 `status`= 1
		WHERE `tdate`= #{tdate}
	</update>
	
	<update id="updateCpsReportDauIncome" parameterType="java.util.Map" >
		UPDATE dn_cps_report 
		SET  `dau`= #{dau},
		 `adv_income`= #{adv_income}
		WHERE `tdate`= #{tdate} and `pid` = #{pid}
	</update>
	
	<insert id="insertAdvChannelFeeNew" parameterType="java.util.Map" >
		INSERT INTO adv_channel_fee_new_report(adv_dt,push_cha,appid,pri_id,cha_type,os_type,give_fee,new_count,dau,get_fee,adv_fee)
		SELECT
			aa.tdate,
			bb.cha_sub_launch,
			aa.appid,
			null,
			null,
			( CASE WHEN cha_type = 1 THEN 3 WHEN cha_type = 9 THEN 2 ELSE 1 END ),
			IFNULL(sum(cc.invest_amount),0) investAmount,
			IFNULL(sum(aa.addnum),0) addnum,
			IFNULL(sum(aa.actnum),0) actnum,
			IFNULL(sum(cc.billing_income),0) billingIncome,
			IFNULL(sum(cc.adv_income),0) advIncome
			
		FROM
			(
				SELECT
					a.tdate,
					a.appid,
					a.appname,
					a.cname,
					a.addnum,
					a.actnum
				FROM umeng_channel_total a
				WHERE a.tdate BETWEEN #{start_date} AND #{end_date} 
			) aa
		LEFT JOIN (
			SELECT
				dci.cha_media,
				dci.cha_sub_launch,
				dci.cha_sub_name_byte,
				dct.type_name,
				dci.cha_id,
				dci.cha_type
			FROM
				dn_channel_info dci
			LEFT JOIN dn_channel_type dct ON dci.cha_type = dct.type_id
		) bb ON aa.cname = bb.cha_id
		
		LEFT JOIN dn_channel_cost cc 
		ON aa.tdate = cc.tdate AND aa.appid = cc.appid AND aa.cname = cc.cha_id
		GROUP BY appid,tdate,cha_type,cha_media,cha_sub_launch
		
		<!-- ON DUPLICATE KEY UPDATE 
		give_fee=VALUES(give_fee),
		new_count=VALUES(new_count),
		dau=VALUES(dau),
		get_fee=VALUES(get_fee),
		adv_fee=VALUES(adv_fee) -->
	</insert>
	
	<!-- <insert id="insertQimaiDate" parameterType="java.util.List" >
		insert into dn_qimai_data(
		tdate,
		market_name,
		app_version_time,
		app_download_num,
		sub_name,
		app_id,
		app_bundleid
	) values
	<foreach collection="list" item="li" separator=",">
		(#{li.tdate},
		#{li.market_name},
		#{li.app_version_time},
		#{li.app_download_num},
		#{li.sub_name},
		#{li.app_id},
		#{li.app_bundleid}
		)
	</foreach>	
	</insert>
	
	<select id="selectDnQIMaiData" resultType="com.wbgame.pojo.DnQIMaiData">
		SELECT 
			*
		FROM dn_qimai_config 
	</select> -->
	
	<delete id="batchDelUmengAdcode" parameterType="Map">
		delete from umeng_ad_income where 
		`tdate` = #{tdate} AND `appkey` = #{appkey} AND `channel` = #{channel};
	</delete>
	<delete id="delUmengAdcode" >
		delete from umeng_adcode_list  
	</delete>
	<delete id="deleteXiaomiErrorDetail">
		delete from xiaomi_error_detail
		where tdate = #{tdate}
		and account = #{account}
	</delete>
	<insert id="insertXiaomiErrorDetail" parameterType="com.wbgame.service.impl.platform.xiaomi.XiaomiErrorDetail">
		insert into xiaomi_error_detail
		    (tdate, account, appid, channel, game_name, suggestion, publisher_id, placement_id,
		     placement_name, style_name, error_code, error_code_message,
		     request_fail_num, request_fail_ratio, origin_request_num, filling_num, filling_rate, effect_request_num,
		     ecpm, gain, account_subject)
		VALUES
		    <foreach collection="list" item="item" separator=",">
		    (#{item.tdate}, #{item.account}, #{item.appid},#{item.channel}, #{item.gameName}, #{item.suggestion}, #{item.publisherId},
		     #{item.placementId}, #{item.placementName}, #{item.styleName},
		     #{item.errorCode}, #{item.errorCodeMessage}, #{item.requestFailNum}, #{item.requestFailRatio},
		     #{item.originRequestNum}, #{item.fillingNum}, #{item.fillingRate}, #{item.effectRequestNum},
		     #{item.ecpm}, #{item.gain}, #{item.accountSubject})
		</foreach>
	</insert>
	<select id="selectXiaomiErrorDetail"
				resultType="com.wbgame.service.impl.platform.xiaomi.XiaomiErrorDetail">
		select tdate, account, appid, channel, app_name appName, game_name gameName, suggestion, publisher_id publisherId, placement_id placementId,
		     placement_name placementName, style_name styleName, error_code errorCode, error_code_message errorCodeMessage,
		     request_fail_num requestFailNum, concat(request_fail_ratio,'%') requestFailRatio, origin_request_num originRequestNum,
		     filling_num fillingNum, concat(filling_rate,'%') fillingRate, effect_request_num effectRequestNum,
		     ecpm, gain, account_subject accountSubject
		from xiaomi_error_detail a
		left join app_info b on a.appid = b.id
		where tdate between #{startDate} and #{endDate}
		<if test="account != null and account != ''">
		 	and account in (${account})
		</if>
		<if test="appid != null and appid != ''">
		 	and appid in (${appid})
		</if>
		<if test="appCategory != null and appCategory != ''">
		 	and app_category in (${appCategory})
		</if>
		<if test="channel != null and channel != ''">
		 	and channel in (${channel})
		</if>
		<if test="errorCode != null and errorCode != ''">
		 	and error_code in (${errorCode})
		</if>
		<if test="gameName != null and gameName != ''">
			and game_name like concat('%', #{gameName}, '%')
		</if>
		<if test="accountSubject != null and accountSubject != ''">
			and account_subject in (${accountSubject})
		</if>
		<if test="styleName != null and styleName != ''">
			and style_name like concat('%', #{styleName}, '%')
		</if>
		<if test="placementName != null and placementName != ''">
			and placement_name like concat('%', #{placementName}, '%')
		</if>
		<if test="errorCodeMessage != null and errorCodeMessage != ''">
			and error_code_message like concat('%', #{errorCodeMessage}, '%')
		</if>
		<if test="publisherId != null and publisherId != ''">
			and publisher_id = #{publisherId}
		</if>
		<if test="placementId != null and placementId != ''">
			and placement_id = #{placementId}
		</if>
		<if test="orderStr != null and orderStr != ''">
			order by ${orderStr}
		</if>
	</select>

	<select id="selectXiaomiErrorCodes" resultType="java.lang.String">
		select distinct error_code errorCode from xiaomi_error_detail
	</select>

	<select id="getUseAppInfo" resultType="com.wbgame.pojo.wx.WxAppInfo">
		select id,name,appid,secret,create_time as createTime,is_use as isUse from wx_app_info
		where is_use = 1
	</select>
	
	<select id="selectUmengAddAct" parameterType="java.util.Map" resultType="java.util.Map">
	SELECT
		a.by_date tdate,
		a.by_priid pid,
		b.push_newcount add_num,
		a.by_newcount act_num
	FROM
		alone_dau_total a
	LEFT JOIN buyu_newcount_total b ON a.by_priid = b.by_priid
	WHERE
		a.by_date = #{tdate}
	AND b.by_date = #{tdate}
	AND a.by_priid in  
	<foreach collection="pids" item="pid" index="index" open="(" close=")" separator=",">
      ${pid}
    </foreach>
	</select>
	
	<insert id="insertExtendRevenueReport" parameterType="java.lang.String">
		INSERT INTO dnwx_cfg.dn_extend_revenue_report(date,dnappid,agent,placement_type,request_count,return_count,pv,revenue)
		select 
				date,dnappid,agent,open_type,
				sum(aa.request_count) request_count,
				sum(aa.return_count) return_count,
				sum(aa.pv) pv,
				TRUNCATE(sum(aa.revenue),2) revenue
			from dn_cha_cash_total aa 
			where date = #{tdate} and app_id != '0' and dnappid != '0' 
		group by date,dnappid,agent,open_type 
	</insert>
	
	<!-- 应用分渠道收支汇总表-平台 -->
	<insert id="insertDnAppChannelRevenueTotal" parameterType="java.lang.String">
		INSERT IGNORE dn_app_channel_revenue_total(tdate,appid,channel,ad_revenue,app_category)
		select date,dnappid appid,cha_id,SUM(revenue) revenue,'3' 
		from dn_cha_cash_total where date = #{tdate} and app_id != '0'
			and cha_id != 'baidu' and dnappid is not null and dnappid in (SELECT id FROM app_info where bus_category = 3)
		GROUP BY dnappid,cha_id
		<!-- ON DUPLICATE KEY UPDATE ad_revenue=VALUES(ad_revenue) -->
	</insert>
	<insert id="insertDnAppChannelRevenueTotalTwo" parameterType="java.lang.String">
		INSERT IGNORE dn_app_channel_revenue_total(tdate,appid,channel,ad_revenue,app_category)
		select date,dnappid appid,cha_id,SUM(revenue) revenue,'1' 
		from dn_cha_cash_total where date = #{tdate} and app_id != '0' 
			and cha_id != 'baidu' and dnappid is not null and dnappid in (SELECT id FROM app_info where bus_category = 1)
		GROUP BY dnappid,cha_id
		<!-- ON DUPLICATE KEY UPDATE ad_revenue=VALUES(ad_revenue) -->
	</insert>
	
	<select id="selectDnAppChannelRevenueTotal" parameterType="java.util.Map" resultType="java.util.Map">
		select
		    tdate
<if test="appid_group != null or channel_group != ''">
	,${group2}
</if>
,rebate_consume,
addnum,
actnum,
paynum,
ad_revenue,
bd_revenue,
pay_revenue,
total_revenue,
total_arpu,

iap_reg_user_cnt,
concat(iap_reg_user_rate,'%') iap_reg_user_rate,
android_order_cnt,
ios_order_cnt,

concat(addrate,'%') addrate,
concat(payrate,'%') payrate,
cpa,
ad_arpu,
bd_arpu,
pay_arpu,
paynum_arpu,
profit,
sum_profit,
concat(profit_rate,'%') profit_rate,
concat(sum_profit_rate,'%') sum_profit_rate,

all_expenses,
all_revenue,
all_sum_revenue,
concat(all_profit_rate,'%') all_profit_rate,
concat(all_sum_profit_rate,'%') all_sum_profit_rate
		from (<include refid="dn_appchannel_revenue_sql"/>) temp
	</select>
	<select id="selectDnAppChannelRevenueTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			TRUNCATE(SUM(xx.rebate_consume),0) rebate_consume,
			SUM(xx.addnum) addnum,
			SUM(xx.actnum) actnum,
			SUM(xx.paynum) paynum,
			TRUNCATE(SUM(xx.ad_revenue),0) ad_revenue,
			TRUNCATE(SUM(xx.bd_revenue),0) bd_revenue,
			TRUNCATE(SUM(xx.pay_revenue),0) pay_revenue,
			TRUNCATE(SUM(xx.ad_revenue)+SUM(xx.bd_revenue)+SUM(xx.pay_revenue),0) total_revenue,
			TRUNCATE((SUM(xx.ad_revenue)+SUM(xx.bd_revenue)+SUM(xx.pay_revenue)) / SUM(xx.actnum), 2) total_arpu,
			
			SUM(xx.iap_reg_user_cnt) iap_reg_user_cnt,
			CONCAT(TRUNCATE(SUM(xx.iap_reg_user_cnt) / SUM(xx.addnum)*100, 2),'%') iap_reg_user_rate,
			SUM(xx.android_order_cnt) android_order_cnt,
			SUM(xx.ios_order_cnt) ios_order_cnt,
			
			CONCAT(TRUNCATE(SUM(xx.addnum) / SUM(xx.actnum)*100, 1),'%') addrate,
			CONCAT(TRUNCATE(SUM(xx.paynum) / SUM(xx.actnum)*100, 1),'%') payrate,
			TRUNCATE(SUM(xx.rebate_consume) / SUM(xx.addnum), 2) cpa,
			TRUNCATE(SUM(xx.ad_revenue) / SUM(xx.actnum), 2) ad_arpu,
			TRUNCATE(SUM(xx.bd_revenue) / SUM(xx.actnum), 2) bd_arpu,
			TRUNCATE(SUM(xx.pay_revenue) / SUM(xx.actnum), 2) pay_arpu,
			TRUNCATE(SUM(xx.pay_revenue) / SUM(xx.paynum), 2) paynum_arpu,
			TRUNCATE(SUM(xx.ad_revenue) - SUM(xx.rebate_consume), 0) profit,
			TRUNCATE(SUM(xx.ad_revenue) + SUM(xx.bd_revenue) + SUM(xx.pay_revenue) - SUM(xx.rebate_consume), 0) sum_profit,
			CONCAT(IFNULL(TRUNCATE((SUM(xx.ad_revenue) - SUM(xx.rebate_consume))/SUM(xx.rebate_consume)*100, 1),0),'%') profit_rate,
			CONCAT(IFNULL(TRUNCATE((SUM(xx.ad_revenue) + SUM(xx.bd_revenue) + SUM(xx.pay_revenue) - SUM(xx.rebate_consume))/SUM(xx.rebate_consume)*100, 1),0),'%') sum_profit_rate
				
		from (<include refid="dn_appchannel_revenue_sql"/>) xx
	</select>
	<sql id="dn_appchannel_revenue_sql">
		select aa.*,
			dd.all_expenses,
			dd.all_revenue,
			dd.all_sum_revenue,
			dd.all_profit_rate,
			dd.all_sum_profit_rate
		from
			(select 
				${group} tdate
				<if test="appid_group != null or channel_group != ''">
					,${group2}
				</if>
				,TRUNCATE(SUM(rebate_consume),0) rebate_consume,
				SUM(addnum) addnum,
				SUM(actnum) actnum,
				SUM(paynum) paynum,
				TRUNCATE(SUM(ad_revenue),0) ad_revenue,
				TRUNCATE(SUM(bd_revenue),0) bd_revenue,
				TRUNCATE(SUM(pay_revenue),0) pay_revenue,
				TRUNCATE(SUM(ad_revenue)+SUM(bd_revenue)+SUM(pay_revenue),0) total_revenue,
				TRUNCATE((SUM(ad_revenue)+SUM(bd_revenue)+SUM(pay_revenue)) / SUM(actnum), 2) total_arpu,
				
				SUM(iap_reg_user_cnt) iap_reg_user_cnt,
				TRUNCATE(SUM(iap_reg_user_cnt) / SUM(addnum)*100, 2) iap_reg_user_rate,
				SUM(android_order_cnt) android_order_cnt,
				SUM(ios_order_cnt) ios_order_cnt,
				
				TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1) addrate,
				TRUNCATE(SUM(paynum) / SUM(actnum)*100, 1) payrate,
				IFNULL(TRUNCATE(SUM(rebate_consume) / SUM(addnum), 2),0) cpa,
				TRUNCATE(SUM(ad_revenue) / SUM(actnum), 2) ad_arpu,
				TRUNCATE(SUM(bd_revenue) / SUM(actnum), 2) bd_arpu,
				TRUNCATE(SUM(pay_revenue) / SUM(actnum), 2) pay_arpu,
				TRUNCATE(SUM(pay_revenue) / SUM(paynum), 2) paynum_arpu,
				TRUNCATE(SUM(ad_revenue) - SUM(rebate_consume), 0) profit,
				TRUNCATE(SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) - SUM(rebate_consume), 0) sum_profit,
				IFNULL(TRUNCATE((SUM(ad_revenue) - SUM(rebate_consume))/TRUNCATE(SUM(rebate_consume),0)*100, 1),0) profit_rate,
				IFNULL(TRUNCATE((SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) - SUM(rebate_consume))/TRUNCATE(SUM(rebate_consume),0)*100, 1),0) sum_profit_rate
			from dn_app_channel_revenue_total aa
			where tdate BETWEEN #{start_date} AND #{end_date} 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="channel != null and channel != ''">
				and channel in (${channel}) 
			</if>
			<if test="app_category != null and app_category != ''">
				and app_category = #{app_category} 
			</if>
			 group by ${group}
			 	<if test="appid_group != null or channel_group != ''">
					,${group2}
				</if>
			) aa
		 
		 LEFT JOIN 
			(select 
				appid,
				channel,
				TRUNCATE(SUM(rebate_consume),0) all_expenses,
				TRUNCATE(SUM(ad_revenue),0) all_revenue,
				TRUNCATE(SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue),0) all_sum_revenue,
				IFNULL(TRUNCATE((SUM(ad_revenue) - SUM(rebate_consume))/SUM(rebate_consume)*100, 1),0) all_profit_rate,
				IFNULL(TRUNCATE((SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) - SUM(rebate_consume))/SUM(rebate_consume)*100, 1),0) all_sum_profit_rate
			from dn_app_channel_revenue_total where 1=1
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="channel != null and channel != ''">
				and channel in (${channel}) 
			</if>
			<if test="app_category != null and app_category != ''">
				and app_category = #{app_category} 
			</if>
			
			<choose>
				<when test="appid_group != null or channel_group != ''">
					group by ${group2}) dd ${group2_match}
				</when>
				<otherwise>
					) dd ON 1=1
				</otherwise>
			</choose>
		
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate asc, addnum desc
			</otherwise>
		</choose>
	</sql>
	
	<!-- 应用收支汇总表 -->
	<insert id="insertDnAppRevenueTotal" parameterType="java.lang.String">
		INSERT IGNORE dn_app_revenue_total(tdate,appid,ad_revenue,app_category)
		select date,dnappid appid,SUM(revenue) revenue,'3' 
		from dn_cha_cash_total where date = #{tdate} and app_id != '0'
			and cha_id != 'baidu' and dnappid is not null and dnappid in (SELECT id FROM app_info where bus_category = 3)
		GROUP BY dnappid
		<!-- ON DUPLICATE KEY UPDATE ad_revenue=VALUES(ad_revenue) -->
	</insert>
	<insert id="insertDnAppRevenueTotalTwo" parameterType="java.lang.String">
		INSERT IGNORE dn_app_revenue_total(tdate,appid,ad_revenue,app_category)
		select date,dnappid appid,SUM(revenue) revenue,'1' 
		from dn_cha_cash_total where date = #{tdate} and app_id != '0' 
			and cha_id != 'baidu' and dnappid is not null and dnappid in (SELECT id FROM app_info where bus_category = 1)
		GROUP BY dnappid
		<!-- ON DUPLICATE KEY UPDATE ad_revenue=VALUES(ad_revenue) -->
	</insert>
	
	<select id="selectDnAppRevenueTotal" parameterType="java.util.Map" resultType="java.util.Map">
		select
			tdate
			${group2},
			rebate_consume,
			addnum,
			actnum,
			paynum,
			ad_revenue,
			bd_revenue,
			pay_revenue,
			total_revenue,
			total_arpu,

			iap_reg_user_cnt,
			concat(iap_reg_user_rate,'%') iap_reg_user_rate,
			android_order_cnt,
			ios_order_cnt,

			concat(addrate,'%') addrate,
			concat(payrate,'%') payrate,
			cpa,
			ad_arpu,
			bd_arpu,
			pay_arpu,
			paynum_arpu,
			profit,
			sum_profit,
			concat(profit_rate,'%') profit_rate,
			concat(sum_profit_rate,'%') sum_profit_rate,
			all_expenses,
			all_revenue,
			all_sum_revenue,
			concat(all_profit_rate,'%') all_profit_rate,
			concat(all_sum_profit_rate,'%') all_sum_profit_rate

		from  (<include refid="dn_app_revenue_sql"/>) temp
	</select>
	<select id="selectDnAppRevenueTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			TRUNCATE(SUM(xx.rebate_consume),0) rebate_consume,
			SUM(xx.addnum) addnum,
			SUM(xx.actnum) actnum,
			SUM(xx.paynum) paynum,
			TRUNCATE(SUM(xx.ad_revenue),0) ad_revenue,
			TRUNCATE(SUM(xx.bd_revenue),0) bd_revenue,
			TRUNCATE(SUM(xx.pay_revenue),0) pay_revenue,
			TRUNCATE(SUM(xx.ad_revenue)+SUM(xx.bd_revenue)+SUM(xx.pay_revenue),0) total_revenue,
			TRUNCATE((SUM(xx.ad_revenue)+SUM(xx.bd_revenue)+SUM(xx.pay_revenue)) / SUM(xx.actnum), 2) total_arpu,
			
			SUM(xx.iap_reg_user_cnt) iap_reg_user_cnt,
			CONCAT(TRUNCATE(SUM(xx.iap_reg_user_cnt) / SUM(xx.addnum)*100, 2),'%') iap_reg_user_rate,
			SUM(xx.android_order_cnt) android_order_cnt,
			SUM(xx.ios_order_cnt) ios_order_cnt,
			
			CONCAT(TRUNCATE(SUM(xx.addnum) / SUM(xx.actnum)*100, 1),'%') addrate,
			CONCAT(TRUNCATE(SUM(xx.paynum) / SUM(xx.actnum)*100, 1),'%') payrate,
			TRUNCATE(SUM(xx.rebate_consume) / SUM(xx.addnum), 2) cpa,
			TRUNCATE(SUM(xx.ad_revenue) / SUM(xx.actnum), 2) ad_arpu,
			TRUNCATE(SUM(xx.bd_revenue) / SUM(xx.actnum), 2) bd_arpu,
			TRUNCATE(SUM(xx.pay_revenue) / SUM(xx.actnum), 2) pay_arpu,
			TRUNCATE(SUM(xx.pay_revenue) / SUM(xx.paynum), 2) paynum_arpu,
			TRUNCATE(SUM(xx.ad_revenue) - SUM(xx.rebate_consume), 0) profit,
			TRUNCATE(SUM(xx.ad_revenue) + SUM(xx.bd_revenue) + SUM(xx.pay_revenue) - SUM(xx.rebate_consume), 0) sum_profit,
			CONCAT(IFNULL(TRUNCATE((SUM(xx.ad_revenue) - SUM(xx.rebate_consume))/SUM(xx.rebate_consume)*100, 1),0),'%') profit_rate,
			CONCAT(IFNULL(TRUNCATE((SUM(xx.ad_revenue) + SUM(xx.bd_revenue) + SUM(xx.pay_revenue) - SUM(xx.rebate_consume))/SUM(xx.rebate_consume)*100, 1),0),'%') sum_profit_rate
				
		from (<include refid="dn_app_revenue_sql"/>) xx
	</select>
	<sql id="dn_app_revenue_sql">
		select aa.*,
			dd.all_expenses,
			dd.all_revenue,
			dd.all_sum_revenue,
			dd.all_profit_rate,
			dd.all_sum_profit_rate
		from
			(select 
				${group} tdate
				${group2},
				TRUNCATE(SUM(rebate_consume),0) rebate_consume,
				SUM(addnum) addnum,
				SUM(actnum) actnum,
				SUM(paynum) paynum,
				TRUNCATE(SUM(ad_revenue),0) ad_revenue,
				TRUNCATE(SUM(bd_revenue),0) bd_revenue,
				TRUNCATE(SUM(pay_revenue),0) pay_revenue,
				TRUNCATE(SUM(ad_revenue)+SUM(bd_revenue)+SUM(pay_revenue),0) total_revenue,
				TRUNCATE((SUM(ad_revenue)+SUM(bd_revenue)+SUM(pay_revenue)) / SUM(actnum), 2) total_arpu,
				
				SUM(iap_reg_user_cnt) iap_reg_user_cnt,
				TRUNCATE(SUM(iap_reg_user_cnt) / SUM(addnum)*100, 2) iap_reg_user_rate,
				SUM(android_order_cnt) android_order_cnt,
				SUM(ios_order_cnt) ios_order_cnt,
				
				TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1) addrate,
				TRUNCATE(SUM(paynum) / SUM(actnum)*100, 1) payrate,
				IFNULL(TRUNCATE(SUM(rebate_consume) / SUM(addnum), 2),0) cpa,
				TRUNCATE(SUM(ad_revenue) / SUM(actnum), 2) ad_arpu,
				TRUNCATE(SUM(bd_revenue) / SUM(actnum), 2) bd_arpu,
				TRUNCATE(SUM(pay_revenue) / SUM(actnum), 2) pay_arpu,
				TRUNCATE(SUM(pay_revenue) / SUM(paynum), 2) paynum_arpu,
				TRUNCATE(SUM(ad_revenue) - SUM(rebate_consume), 0) profit,
				TRUNCATE(SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) - SUM(rebate_consume), 0) sum_profit,
				IFNULL(TRUNCATE((SUM(ad_revenue) - SUM(rebate_consume))/TRUNCATE(SUM(rebate_consume),0)*100, 1),0) profit_rate,
				IFNULL(TRUNCATE((SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) - SUM(rebate_consume))/TRUNCATE(SUM(rebate_consume),0)*100, 1),0) sum_profit_rate
			from dn_app_revenue_total aa
			where tdate BETWEEN #{start_date} AND #{end_date} 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="app_category != null and app_category != ''">
				and app_category = #{app_category} 
			</if>
			<if test="two_app_category != null and two_app_category != ''">
				and appid in (select id from app_info where two_app_category in (${two_app_category})) 
			</if>
			 group by ${group}${group2}) aa
		 
		 LEFT JOIN 
			(select 
				appid,
				TRUNCATE(SUM(rebate_consume),0) all_expenses,
				TRUNCATE(SUM(ad_revenue),0) all_revenue,
				TRUNCATE(SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue),0) all_sum_revenue,
				IFNULL(TRUNCATE((SUM(ad_revenue) - SUM(rebate_consume))/SUM(rebate_consume)*100, 1),0) all_profit_rate,
				IFNULL(TRUNCATE((SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) - SUM(rebate_consume))/SUM(rebate_consume)*100, 1),0) all_sum_profit_rate
			from dn_app_revenue_total where 1=1
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="app_category != null and app_category != ''">
				and app_category = #{app_category} 
			</if>
			
			<choose>
				<when test="appid_group != null and appid_group != ''">
					group by appid) dd ON aa.appid = dd.appid
				</when>
				<otherwise>
					) dd ON 1=1
				</otherwise>
			</choose>
		
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate asc, addnum desc
			</otherwise>
		</choose>
	</sql>
	
	<select id="selectDnPaysAppRevenueTotal" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_paysapp_revenue_sql"/>
	</select>
	<select id="selectDnPaysAppRevenueTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			SUM(xx.addnum) addnum,
			SUM(xx.actnum) actnum,
			SUM(xx.paynum) paynum,
			TRUNCATE(SUM(xx.ad_revenue),0) ad_revenue,
			TRUNCATE(SUM(xx.bd_revenue),0) bd_revenue,
			TRUNCATE(SUM(xx.pay_revenue),0) pay_revenue
			
		from (<include refid="dn_paysapp_revenue_sql"/>) xx
	</select>
	<sql id="dn_paysapp_revenue_sql">
		select aa.*,
			dd.all_expenses,
			dd.all_revenue,
			dd.all_sum_revenue,
			dd.all_profit_rate,
			dd.all_sum_profit_rate
		from
			(select 
				${group} tdate,
				appid,
				ROUND(SUM(addnum)*0.01,0) addnum,
				ROUND(SUM(actnum)*0.01,0) actnum,
				ROUND(SUM(paynum)*0.01,0) paynum,
				
				TRUNCATE(SUM(ad_revenue)*0.01,0) ad_revenue,
				TRUNCATE(SUM(bd_revenue)*0.01,0) bd_revenue,
				TRUNCATE(SUM(pay_revenue)*0.01,0) pay_revenue
				
			from dn_app_revenue_total aa
			where tdate BETWEEN #{start_date} AND #{end_date} 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="app_category != null and app_category != ''">
				and app_category = #{app_category} 
			</if>
			
			group by ${group},appid) aa
		 
		 LEFT JOIN 
			(select 
				appid,
				TRUNCATE(SUM(rebate_consume),0) all_expenses,
				TRUNCATE(SUM(ad_revenue),0) all_revenue,
				TRUNCATE(SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue),0) all_sum_revenue,
				CONCAT(IFNULL(TRUNCATE((SUM(ad_revenue) - SUM(rebate_consume))/SUM(rebate_consume)*100, 1),0),'%') all_profit_rate,
				CONCAT(IFNULL(TRUNCATE((SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) - SUM(rebate_consume))/SUM(rebate_consume)*100, 1),0),'%') all_sum_profit_rate
			from dn_app_revenue_total where 1=1
			and pay_revenue > 0 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="app_category != null and app_category != ''">
				and app_category = #{app_category} 
			</if>
			group by appid) dd 
			
		ON aa.appid = dd.appid 
		where aa.pay_revenue > 0 
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate asc, addnum desc
			</otherwise>
		</choose>
	</sql>
	
	<insert id="insertSelfPromotionIncome" parameterType="java.lang.String">
		REPLACE INTO dnwx_cfg.dn_selfpromotion_income(tdate,appid,adpos_type,open_type,revenue,pv,ecpm,headline_pv,gdt_pv,kuaishou_pv,mobvista_pv,admob_pv,sigmob_pv,headline_revenue,gdt_revenue,kuaishou_revenue,mobvista_revenue,admob_revenue,sigmob_revenue)
		select '${tdate}' tdate,dnappid,placement_type,bb.open_type,SUM(revenue) revenue,SUM(pv) pv,TRUNCATE(SUM(revenue)/IF(SUM(pv)=0,NULL,SUM(pv))*1000,2) ecpm,
			SUM(CASE WHEN agent = 'headline' THEN pv ELSE 0 END) headline_pv,
			SUM(CASE WHEN agent = 'gdt' THEN pv ELSE 0 END) gdt_pv,
			SUM(CASE WHEN agent = 'kuaishou' THEN pv ELSE 0 END) kuaishou_pv,
			SUM(CASE WHEN agent = 'mobvista' THEN pv ELSE 0 END) mobvista_pv,
			SUM(CASE WHEN agent = 'admob' THEN pv ELSE 0 END) admob_pv,
			SUM(CASE WHEN agent = 'sigmob' THEN pv ELSE 0 END) sigmob_pv,
			SUM(CASE WHEN agent = 'headline' THEN revenue ELSE 0 END) headline_revenue,
			SUM(CASE WHEN agent = 'gdt' THEN revenue ELSE 0 END) gdt_revenue,
			SUM(CASE WHEN agent = 'kuaishou' THEN revenue ELSE 0 END) kuaishou_revenue,
			SUM(CASE WHEN agent = 'mobvista' THEN revenue ELSE 0 END) mobvista_revenue,
			SUM(CASE WHEN agent = 'admob' THEN revenue ELSE 0 END) admob_revenue,
			SUM(CASE WHEN agent = 'sigmob' THEN revenue ELSE 0 END) sigmob_revenue
		from dn_cha_cash_total aa JOIN (select adsid,open_type from dnwx_cfg.dn_extend_adsid_manage group by adsid) bb ON aa.ad_sid=bb.adsid
			where date = '${tdate}' and app_id != '0' and dnappid != '0' and ad_sid is not null and bb.open_type is not null
			and agent in ('headline','gdt','kuaishou','mobvista','admob','sigmob')
		group by dnappid,placement_type,bb.open_type
	</insert>
	
	<insert id="insertExtendAdtypeRevise" parameterType="java.lang.String">
		REPLACE INTO dn_extend_revise_adtype(tdate,appid,cha_id,cha_type_name,cha_media,sum_revenue,
			pv_splash,pv_plaque,pv_banner,pv_video,pv_msg,revenue_splash,revenue_plaque,revenue_banner,revenue_video,revenue_msg)

		select '${tdate}' tdate,aa.appid,aa.cha_id,cc.type_name,bb.cha_media,SUM(aa.revise_revenue) revenue,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_show ELSE 0 END) pv_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_show ELSE 0 END) pv_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_show ELSE 0 END) pv_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_show ELSE 0 END) pv_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_show ELSE 0 END) pv_msg,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_revenue ELSE 0 END) revenue_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_revenue ELSE 0 END) revenue_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_revenue ELSE 0 END) revenue_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_revenue ELSE 0 END) revenue_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_revenue ELSE 0 END) revenue_msg
		from dn_extend_revise_income aa 
		LEFT JOIN dn_channel_info bb ON aa.cha_id=bb.cha_id
		LEFT JOIN dn_channel_type cc ON bb.cha_type=cc.type_id
		where aa.tdate = '${tdate}' and aa.revise_show is not null 
		and SUBSTRING_INDEX(aa.adsid,'_',1) not in ('Mjuhe','bxm')
		group by aa.appid,aa.cha_id
	</insert>
	
	<insert id="insertExtendSubchaTotal" parameterType="java.lang.String">
		REPLACE INTO dn_extend_subcha_total(tdate,appid,cha_id,sub_cha,cha_type_name,cha_media,sum_revenue,
			pv_splash,pv_plaque,pv_banner,pv_video,pv_msg,revenue_splash,revenue_plaque,revenue_banner,revenue_video,revenue_msg) 
			
		select '${tdate}' tdate,aa.appid,aa.cha_id,aa.sub_cha,cc.type_name,bb.cha_media,SUM(aa.revenue) revenue,
			SUM(CASE WHEN adpos_type = 'splash' THEN self_show ELSE 0 END) pv_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN self_show ELSE 0 END) pv_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN self_show ELSE 0 END) pv_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN self_show ELSE 0 END) pv_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN self_show ELSE 0 END) pv_msg,
			SUM(CASE WHEN adpos_type = 'splash' THEN revenue ELSE 0 END) revenue_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revenue ELSE 0 END) revenue_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revenue ELSE 0 END) revenue_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revenue ELSE 0 END) revenue_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revenue ELSE 0 END) revenue_msg
		from dn_extend_subcha_income aa 
		LEFT JOIN dn_channel_info bb ON aa.cha_id=bb.cha_id
		LEFT JOIN dn_channel_type cc ON bb.cha_type=cc.type_id
		where aa.tdate = '${tdate}' 
		group by aa.appid,aa.cha_id,aa.sub_cha
	</insert>
	<select id="selectSubchaTotalOfBanner" parameterType="java.lang.String" resultType="java.util.Map">
		SELECT '${tdate}' tdate,xx.appid,xx.cha_id,xx.sub_cha,
			truncate(xx.actnum/yy.actSum*zz.pv,0) pv,
			IFNULL(truncate(xx.actnum/yy.actSum*zz.income,2),0) income FROM

		(SELECT appid,cha_id,sub_cha,actnum FROM `dn_extend_subcha_total` where tdate=#{tdate}
		GROUP BY appid,cha_id,sub_cha) xx

		LEFT JOIN
		(SELECT appid,cha_id,SUM(actnum) actSum FROM `dn_extend_subcha_total` where tdate=#{tdate}
		GROUP BY appid,cha_id) yy
		ON xx.appid=yy.appid AND xx.cha_id=yy.cha_id

		LEFT JOIN
		(select dnappid appid,cha_id,
			TRUNCATE(sum(aa.revenue),2) income,
			TRUNCATE(sum(aa.pv),0) pv
		from dn_cha_cash_total aa where date = #{tdate}
		and placement_type in ('banner') and app_id != '0' and dnappid != '0' and cha_id is not null
		group by dnappid,cha_id having (sum(pv)+sum(revenue)) > 0 ) zz
		ON xx.appid=zz.appid AND xx.cha_id=zz.cha_id
	</select>

	<insert id="insertExtendPrjidIncome" parameterType="java.lang.String">
		INSERT INTO dn_extend_prjid_income(tdate,appid,prjid,cha_id,cha_type_name,cha_media,sum_revenue,actnum,addnum,pv_splash,pv_plaque,pv_banner,pv_video,pv_msg,revenue_splash,revenue_plaque,revenue_banner,revenue_video,revenue_msg) 
		select '${tdate}' tdate,aa.appid,aa.prjid,aa.cha_id,cc.type_name,bb.cha_media,SUM(aa.revise_revenue) revenue,aa.actnum,aa.addnum,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_show ELSE 0 END) pv_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_show ELSE 0 END) pv_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_show ELSE 0 END) pv_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_show ELSE 0 END) pv_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_show ELSE 0 END) pv_msg,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_revenue ELSE 0 END) revenue_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_revenue ELSE 0 END) revenue_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_revenue ELSE 0 END) revenue_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_revenue ELSE 0 END) revenue_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_revenue ELSE 0 END) revenue_msg
		from dn_extend_revise_income aa 
		LEFT JOIN dn_channel_info bb ON aa.cha_id=bb.cha_id
		LEFT JOIN dn_channel_type cc ON bb.cha_type=cc.type_id
		where aa.tdate = '${tdate}' 
		group by aa.prjid,aa.cha_id
	</insert>

	<insert id="batchInsertUmengAdcode">
		insert ignore umeng_adcode_list(ad_code,appkey,adtype,note,channel,status) values
		<foreach collection="list" separator="," item="it">
			(#{it.ad_code},#{it.appkey},#{it.adtype},#{it.note},#{it.channel},#{it.status})
		</foreach>
 	</insert>
	<insert id="batchInsertUmengAdcode2">
		replace into umeng_adcode_list(ad_code,appkey,adtype,note,channel,status) values
		<foreach collection="list" separator="," item="it">
			(#{it.ad_code},#{it.appkey},#{it.adtype},#{it.note},#{it.channel},#{it.status})
		</foreach>
 	</insert>
	<insert id="insertDnAppChaRevenue" parameterType="java.lang.String">
		INSERT IGNORE dn_app_cha_revenue_total(tdate,appid,cha_id,cha_type_name,cha_media,cha_sub_launch,ad_revenue) 
		
		select '${tdate}' tdate,aa.appid,aa.cha_id,cc.type_name,bb.cha_media,bb.cha_sub_launch,SUM(aa.revise_revenue) revenue
		from dnwx_bi.dn_extend_revise_income aa 
		LEFT JOIN dn_channel_info bb ON aa.cha_id=bb.cha_id
		LEFT JOIN dn_channel_type cc ON bb.cha_type=cc.type_id
		where aa.tdate = '${tdate}' and aa.revise_show is not null
		group by aa.appid,aa.cha_id
	</insert>
	<!-- 兼容拉取老版本产品渠道 -->
	<insert id="insertDnAppChaRevenueTwo" parameterType="java.lang.String">
		INSERT IGNORE dn_app_cha_revenue_total(tdate,appid,cha_id,cha_type_name,cha_media,cha_sub_launch,ad_revenue) 
		
		select '${tdate}' tdate,aa.dnappid appid,aa.cha_id,cc.type_name,bb.cha_media,bb.cha_sub_launch,IFNULL(TRUNCATE(SUM(aa.revenue),2),0) revenue
		from dn_cha_cash_total aa 
		LEFT JOIN dn_channel_info bb ON aa.cha_id=bb.cha_id
		LEFT JOIN dn_channel_type cc ON bb.cha_type=cc.type_id
		where aa.date = '${tdate}' and aa.app_id != '0' and aa.dnappid != '0' and aa.ad_sid != '' and aa.cha_id is not null
		<!-- 只有部分渠道 用变现明细数据填充 .20220315 -->
		and (aa.cha_id in ('oppo','vivo','xiaomi','apple','xiaomimj','oppoml','jinli','oppo2','huawei','huawei2','huaweiml') or aa.cha_id in (select cha_id from dn_channel_info where cha_type=8 and cha_id != 'ylyq') )
		group by aa.dnappid,aa.cha_id
	</insert>
	
	<update id="updateAdduserForPv" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			update dn_adshow_adduser set 
	    		${li.mapkey} = #{li.pv} 
	    	where tdate = #{li.tdate} and adsid = #{li.adsid} and channel = #{li.cha_id} 
    	</foreach>
	</update>
	<update id="updateAdduserForLtv" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			update dn_extend_sumltv_adduser set 
	    		${li.mapkey} = ROUND(#{li.pv}*ecpm/1000,2) 
	    	where tdate = #{li.tdate} and adsid = #{li.adsid} and cha_id = #{li.cha_id} 
    	</foreach>
	</update>
	<update id="updateAdduserForArPv" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			update dn_extend_ecpm_adduser set 
	    		${li.mapkey} = #{li.pv},
	    		${li.mapkey2} = ROUND(#{li.pv}*ecpm/1000,2) 
	    	where tdate = #{li.tdate} and adsid = #{li.adsid} and cha_id = #{li.cha_id} 
    	</foreach>
	</update>
	
	<insert id="insertAgentShowGap" parameterType="java.lang.String">
		REPLACE INTO dn_agent_show_gap(tdate,appid,headline_msg,gdt_msg,kuaishou_msg,mobvista_msg,admob_msg,sigmob_msg,headline_plaque,gdt_plaque,kuaishou_plaque,mobvista_plaque,admob_plaque,sigmob_plaque,headline_video,gdt_video,kuaishou_video,mobvista_video,admob_video,sigmob_video) 
		
		SELECT '${tdate}' tdate,appid, 
			CONCAT(truncate((headline_msg_show-headline_msg_pv)/headline_msg_show*100,1),'%') headline_msg,
			CONCAT(truncate((gdt_msg_show-gdt_msg_pv)/gdt_msg_show*100,1),'%') gdt_msg,
			CONCAT(truncate((kuaishou_msg_show-kuaishou_msg_pv)/kuaishou_msg_show*100,1),'%') kuaishou_msg,
			CONCAT(truncate((mobvista_msg_show-mobvista_msg_pv)/mobvista_msg_show*100,1),'%') mobvista_msg,
			CONCAT(truncate((admob_msg_show-admob_msg_pv)/admob_msg_show*100,1),'%') admob_msg,
			CONCAT(truncate((sigmob_msg_show-sigmob_msg_pv)/sigmob_msg_show*100,1),'%') sigmob_msg,
			CONCAT(truncate((headline_plaque_show-headline_plaque_pv)/headline_plaque_show*100,1),'%') headline_plaque,
			CONCAT(truncate((gdt_plaque_show-gdt_plaque_pv)/gdt_plaque_show*100,1),'%') gdt_plaque,
			CONCAT(truncate((kuaishou_plaque_show-kuaishou_plaque_pv)/kuaishou_plaque_show*100,1),'%') kuaishou_plaque,
			CONCAT(truncate((mobvista_plaque_show-mobvista_plaque_pv)/mobvista_plaque_show*100,1),'%') mobvista_plaque,
			CONCAT(truncate((admob_plaque_show-admob_plaque_pv)/admob_plaque_show*100,1),'%') admob_plaque,
			CONCAT(truncate((sigmob_plaque_show-sigmob_plaque_pv)/sigmob_plaque_show*100,1),'%') sigmob_plaque,
			CONCAT(truncate((headline_video_show-headline_video_pv)/headline_video_show*100,1),'%') headline_video,
			CONCAT(truncate((gdt_video_show-gdt_video_pv)/gdt_video_show*100,1),'%') gdt_video,
			CONCAT(truncate((kuaishou_video_show-kuaishou_video_pv)/kuaishou_video_show*100,1),'%') kuaishou_video,
			CONCAT(truncate((mobvista_video_show-mobvista_video_pv)/mobvista_video_show*100,1),'%') mobvista_video,
			CONCAT(truncate((admob_video_show-admob_video_pv)/admob_video_show*100,1),'%') admob_video,
			CONCAT(truncate((sigmob_video_show-sigmob_video_pv)/sigmob_video_show*100,1),'%') sigmob_video
		FROM 
		(SELECT appid,
			SUM(CASE WHEN adpos_type='msg' AND agent='headline' THEN pv ELSE 0 END) as headline_msg_pv,
			SUM(CASE WHEN adpos_type='msg' AND agent='headline' THEN selfshow_num ELSE 0 END) as headline_msg_show,
			SUM(CASE WHEN adpos_type='plaque' AND agent='headline' THEN pv ELSE 0 END) as headline_plaque_pv,
			SUM(CASE WHEN adpos_type='plaque' AND agent='headline' THEN selfshow_num ELSE 0 END) as headline_plaque_show,
			SUM(CASE WHEN adpos_type='video' AND agent='headline' THEN pv ELSE 0 END) as headline_video_pv,
			SUM(CASE WHEN adpos_type='video' AND agent='headline' THEN selfshow_num ELSE 0 END) as headline_video_show,
			SUM(CASE WHEN adpos_type='msg' AND agent='gdt' THEN pv ELSE 0 END) as gdt_msg_pv,
			SUM(CASE WHEN adpos_type='msg' AND agent='gdt' THEN selfshow_num ELSE 0 END) as gdt_msg_show,
			SUM(CASE WHEN adpos_type='plaque' AND agent='gdt' THEN pv ELSE 0 END) as gdt_plaque_pv,
			SUM(CASE WHEN adpos_type='plaque' AND agent='gdt' THEN selfshow_num ELSE 0 END) as gdt_plaque_show,
			SUM(CASE WHEN adpos_type='video' AND agent='gdt' THEN pv ELSE 0 END) as gdt_video_pv,
			SUM(CASE WHEN adpos_type='video' AND agent='gdt' THEN selfshow_num ELSE 0 END) as gdt_video_show,
			SUM(CASE WHEN adpos_type='msg' AND agent='kuaishou' THEN pv ELSE 0 END) as kuaishou_msg_pv,
			SUM(CASE WHEN adpos_type='msg' AND agent='kuaishou' THEN selfshow_num ELSE 0 END) as kuaishou_msg_show,
			SUM(CASE WHEN adpos_type='plaque' AND agent='kuaishou' THEN pv ELSE 0 END) as kuaishou_plaque_pv,
			SUM(CASE WHEN adpos_type='plaque' AND agent='kuaishou' THEN selfshow_num ELSE 0 END) as kuaishou_plaque_show,
			SUM(CASE WHEN adpos_type='video' AND agent='kuaishou' THEN pv ELSE 0 END) as kuaishou_video_pv,
			SUM(CASE WHEN adpos_type='video' AND agent='kuaishou' THEN selfshow_num ELSE 0 END) as kuaishou_video_show,
			SUM(CASE WHEN adpos_type='msg' AND agent='mobvista' THEN pv ELSE 0 END) as mobvista_msg_pv,
			SUM(CASE WHEN adpos_type='msg' AND agent='mobvista' THEN selfshow_num ELSE 0 END) as mobvista_msg_show,
			SUM(CASE WHEN adpos_type='plaque' AND agent='mobvista' THEN pv ELSE 0 END) as mobvista_plaque_pv,
			SUM(CASE WHEN adpos_type='plaque' AND agent='mobvista' THEN selfshow_num ELSE 0 END) as mobvista_plaque_show,
			SUM(CASE WHEN adpos_type='video' AND agent='mobvista' THEN pv ELSE 0 END) as mobvista_video_pv,
			SUM(CASE WHEN adpos_type='video' AND agent='mobvista' THEN selfshow_num ELSE 0 END) as mobvista_video_show,
			SUM(CASE WHEN adpos_type='msg' AND agent='admob' THEN pv ELSE 0 END) as admob_msg_pv,
			SUM(CASE WHEN adpos_type='msg' AND agent='admob' THEN selfshow_num ELSE 0 END) as admob_msg_show,
			SUM(CASE WHEN adpos_type='plaque' AND agent='admob' THEN pv ELSE 0 END) as admob_plaque_pv,
			SUM(CASE WHEN adpos_type='plaque' AND agent='admob' THEN selfshow_num ELSE 0 END) as admob_plaque_show,
			SUM(CASE WHEN adpos_type='video' AND agent='admob' THEN pv ELSE 0 END) as admob_video_pv,
			SUM(CASE WHEN adpos_type='video' AND agent='admob' THEN selfshow_num ELSE 0 END) as admob_video_show,
			SUM(CASE WHEN adpos_type='msg' AND agent='sigmob' THEN pv ELSE 0 END) as sigmob_msg_pv,
			SUM(CASE WHEN adpos_type='msg' AND agent='sigmob' THEN selfshow_num ELSE 0 END) as sigmob_msg_show,
			SUM(CASE WHEN adpos_type='plaque' AND agent='sigmob' THEN pv ELSE 0 END) as sigmob_plaque_pv,
			SUM(CASE WHEN adpos_type='plaque' AND agent='sigmob' THEN selfshow_num ELSE 0 END) as sigmob_plaque_show,
			SUM(CASE WHEN adpos_type='video' AND agent='sigmob' THEN pv ELSE 0 END) as sigmob_video_pv,
			SUM(CASE WHEN adpos_type='video' AND agent='sigmob' THEN selfshow_num ELSE 0 END) as sigmob_video_show
		FROM dn_show_gap where tdate = '${tdate}'
		GROUP BY appid) aa
	</insert>
	
	<select id="queryChaByPid" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT channelTag FROM dnwx_client.`wbgui_formconfig` where  pjId = #{pid}
	</select>
	
	

	<select id="selectNewPayInfo" resultType="java.util.Map">
		 select truncate(sum(money),2) as amount,
		 count(distinct ifnull(imei,'')) as payNumber,count(*) as payCount,
		 truncate(sum(money)/count(distinct ifnull(imei,'')),2) as arpu,
		 date(createtime) creattime,tdate
		 ,pid AS prjid,paytype AS payWay,appid,c.name appCategory
		 from
		(select *,
		<choose>
			<when test="custom_date != null and custom_date != ''">
				concat(#{begin},'至',#{end}) as tdate
			</when>
			<when test="group != null and group.contains('tdate')">
				date(createtime) as tdate
			</when>
			<when test="group != null and group.contains('week')">
				DATE_FORMAT(createtime, '%x-%v') as tdate,
				DATE_FORMAT(createtime, '%x-%v') as week
			</when>
			<when test="group != null and group.contains('month')">
				DATE_FORMAT(createtime,'%Y-%m') as tdate,
				DATE_FORMAT(createtime,'%Y-%m') as `month`
			</when>
			<when test="group != null and group != '' and group.contains('beek')">
				CONCAT(DATE_FORMAT(createtime, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 1,CHAR),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 2,CHAR),2,"0"))  AS tdate,
				CONCAT(DATE_FORMAT(createtime, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 1,CHAR),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 2,CHAR),2,"0"))  AS beek
			</when>
			<otherwise>
				concat(#{begin},'至',#{end}) as tdate
			</otherwise>
		</choose>
		from wb_pay_info_hw
		 where createtime BETWEEN concat(#{begin},' 00:00:00') AND concat(#{end},' 23:59:59') and orderstatus = 'SUCCESS'
		<if test="app != null and app !=''">
			and appid in (${app})
		</if>
		<if test="prjId != null and prjId !=''">
			and pid = #{prjId}
		</if>
		<if test="payWays != null and payWays !=''">
			and paytype in (${payWays})
		</if>
		<choose>
			<when test="type.contains('test')">
				and oaid = 'test'
			</when>
			<otherwise>
				and (oaid != 'test' or oaid is null)
			</otherwise>
		</choose>
		) a
		left join app_info b on a.appid = b.id
		left join app_category c on b.app_category = c.id
		<if test="appCategory != null and appCategory !=''">
		   where app_category = #{appCategory}
		</if>
		group by ${group}
		order by ${order}
	</select>

	<select id="countNewPayInfo" resultType="java.util.Map">

		select
			truncate(sum(xx.amount),2) amount,
			sum(xx.payNumber) payNumber,
			sum(xx.payCount) payCount,
			truncate(sum(xx.amount)/sum(xx.payNumber), 2) arpu
		from
			(select truncate(sum(money),2) as amount,
			count(distinct ifnull(imei,'')) as payNumber,count(*) as payCount,
			truncate(sum(money)/count(distinct ifnull(imei,'')),2) as arpu,
			date(createtime) creattime
			,pid AS prjid,paytype AS payWay,appid,c.name appCategory
			from
			(select *,
			<choose>
				<when test="custom_date != null and custom_date != ''">
					concat(#{begin},'至',#{end}) as tdate
				</when>
				<when test="group != null and group.contains('tdate')">
					date(createtime) as tdate
				</when>
				<when test="group != null and group.contains('week')">
					DATE_FORMAT(createtime, '%x-%v') as tdate,
					DATE_FORMAT(createtime, '%x-%v') as week
				</when>
				<when test="group != null and group.contains('month')">
					DATE_FORMAT(createtime,'%Y-%m') as tdate,
					DATE_FORMAT(createtime,'%Y-%m') as `month`
				</when>
				<when test="group != null and group != '' and group.contains('beek')">
					CONCAT(DATE_FORMAT(createtime, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 1,CHAR),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 2,CHAR),2,"0"))  AS tdate,
					CONCAT(DATE_FORMAT(createtime, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 1,CHAR),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(createtime, '%v') - 1) / 2) * 2 + 2,CHAR),2,"0"))  AS beek
				</when>
				<otherwise>
					concat(#{begin},'至',#{end}) as tdate
				</otherwise>
			</choose>
			from wb_pay_info_hw
			where createtime BETWEEN concat(#{begin},' 00:00:00') AND concat(#{end},' 23:59:59') and orderstatus = 'SUCCESS'
			<if test="app != null and app !=''">
				and appid in (${app})
			</if>
			<if test="prjId != null and prjId !=''">
				and pid = #{prjId}
			</if>
			<if test="payWays != null and payWays !=''">
				and paytype in (${payWays})
			</if>
			<choose>
				<when test="type.contains('test')">
					and oaid = 'test'
				</when>
				<otherwise>
					and (oaid != 'test' or oaid is null)
				</otherwise>
			</choose>
			) a
			left join app_info b on a.appid = b.id
			left join app_category c on b.app_category = c.id
			<if test="appCategory != null and appCategory !=''">
				where app_category = #{appCategory}
			</if>
			group by ${group}
			order by ${order}) xx
	</select>

	<insert id="insertDnAppPayRevenueSpecical" parameterType="java.util.Map">
		INSERT into dn_app_revenue_total(tdate,appid,pay_revenue,paynum,app_category)
			select '${tdate}' as tdate,appid,IFNULL(ROUND(sum(money)/100,2),0) pay_revenue,COUNT(DISTINCT imei) paynum,'1'
					from wb_pay_info where createtime BETWEEN '${tdate} 00:00:00' and '${tdate} 23:59:59'

			AND appid in (select id from app_info where app_category=5)
			AND orderstatus='SUCCESS' group by tdate,appid
		ON DUPLICATE KEY UPDATE pay_revenue=VALUES(pay_revenue),paynum=VALUES(paynum)
	</insert>

	<insert id="insertDnChaAppPayRevenueSpecical" parameterType="java.util.Map">
		INSERT INTO dn_app_cha_revenue_total(tdate,appid,cha_id,cha_type_name,cha_media,cha_sub_launch,pay_revenue)
		select
		'${tdate}' as tdate,
		ff.appid,
		aa.channelTag as cha_id,
		ee.type_name cha_type_name,
		dd.cha_media cha_media,
		dd.cha_sub_launch cha_sub_launch,
		IFNULL(ROUND(sum(money)/100,2),0) pay_revenue
		from wb_pay_info ff
		LEFT JOIN dnwx_client.wbgui_formconfig aa on ff.pid = aa.pjId
		LEFT JOIN dnwx_client.wbgui_channel bb on aa.channel = bb.id
		LEFT JOIN dnwx_client.wbgui_gametype cc on aa.typeName = cc.gameId
		LEFT JOIN dn_channel_info dd on aa.channelTag = dd.cha_id
		LEFT JOIN dn_channel_type ee on dd.cha_type = ee.type_id
		where DATE_FORMAT(ff.createtime,'%Y-%m-%d') = #{tdate} AND ff.appid in
		<foreach collection="appid" item="aid" open="(" separator="," close=")">
			#{aid}
		</foreach>
		AND orderstatus='SUCCESS' group by tdate,appid,pid
		ON DUPLICATE KEY UPDATE pay_revenue=VALUES(pay_revenue)
	</insert>

	<select id="getOppoReport" resultType="com.wbgame.pojo.adv2.OppoReportVO"
			parameterType="com.wbgame.pojo.adv2.OppoReprtDTO">
		SELECT
		IFNULL(SUM(requests),0) AS requests,
		IFNULL(SUM(fills),0) AS fills,
		IFNULL(SUM(impressions),0) AS impressions,
		IFNULL(SUM(clicks),0) AS clicks,
		IFNULL(ROUND(SUM(revenue),0),0) AS revenue,
		IFNULL(ROUND(SUM(buyRevenue),0),0) AS buyRevenue,
		IFNULL(ROUND(IFNULL(SUM(clicks),0)/IFNULL(SUM(impressions),0) * 100,2),0) AS clickRate,
		IFNULL(ROUND(IFNULL(SUM(revenue),0)/IFNULL(SUM(impressions),0) * 1000,2),0) AS ecpm
		<if test="group != null and group.size > 0">
			,
			<foreach collection="group" index="index" item="item" separator=",">
				`${item}`
			</foreach>
		</if>
		FROM dnwx_cfg.dn_oppo_report a left join dnwx_adt.dn_oppo_account b on a.account = b.account
		<include refid="getOppoReportCondition1"/>
		<include refid="getOppoReportCondition2"/>
	</select>

	<select id="getOppoReportSummary" resultType="com.wbgame.pojo.adv2.OppoReportVO"
			parameterType="com.wbgame.pojo.adv2.OppoReprtDTO">
		SELECT SUM(requests) AS requests, SUM(fills) AS fills, SUM(impressions) AS impressions, SUM(clicks) AS clicks,
		SUM(revenue) AS revenue, SUM(buyRevenue) AS buyRevenue,
		ROUND(IFNULL(SUM(clicks),0)/IFNULL(SUM(impressions),0) * 100,2) AS clickRate,
		ROUND(IFNULL(SUM(revenue),0)/IFNULL(SUM(impressions),0) * 1000,2) AS ecpm
		FROM dnwx_cfg.dn_oppo_report a left join dnwx_adt.dn_oppo_account b on a.account = b.account
		<include refid="getOppoReportCondition1"/>
	</select>

	<select id="selectUnmatchAds" resultType="com.wbgame.pojo.advert.CashUnmatchVo">
             select placement_id,app_id,member_id from dn_cha_cash_total_hourly where dnappid is null and hours = 24
             and date between #{startDate} and #{endDate}
             <if test="agent != null and agent != ''">
				 and agent = #{agent}
			 </if>
	</select>
	<select id="selectCamerasPayCount" resultType="com.wbgame.task.cameras.WbPayInfoUserInfo">
		select a.appid, androidid, payname,
		       case when param3 = 'pay' and sign_type='1' and is_sign='1' then 'pay'
			   else 'v5' end as param3,
		    sum(money/100) price
		from wb_pay_info a
				 left join app_info b on a.appid = b.id
				 left join ( select * from pay_cycle_sign_log where sign_type='1' and is_sign='1'
				            and create_time BETWEEN #{start} and #{end} ) c on a.orderid = c.original_order
		where createtime BETWEEN #{start} and #{end}
		  and b.app_category in  (3,15,18,19,20,48)
		  and b.os_type = 1
		  and money > (select pay_money_baseline from camera_pay_count_money_config limit 1)
		  and orderstatus = 'SUCCESS'
		  and param3 != 'notice'
		GROUP BY appid,androidid, payname, param3

	</select>
	<select id="selectCamerasSubscribeCount" resultType="com.wbgame.task.cameras.WbPayInfoUserInfo">
		select appid, androidid, payname
from wb_pay_info a
left join app_info b
on a.appid = b.id
where createtime BETWEEN '2024-11-11 00:00:00' and '2024-11-11 23:59:59'
-- and appid = 40536
and b.app_category in  (3,15,18,19,20,48)
and b.os_type = 1
and money > (select pay_money_baseline from camera_pay_count_money_config limit 1)
and orderstatus = 'SUCCESS'
and param3 in ('pay','notice')
GROUP BY appid,androidid, payname

	</select>
	<select id="selectIOSCamerasPayCount" resultType="com.wbgame.task.cameras.WbPayInfoUserInfo">
       <choose>
		   <when test="type == 2">
		select appid, appAccountToken androidid, productId payname, sum(price/ 1000 * rate) price, currency, rate
			from (
					 SELECT appid, appAccountToken, json_extract_c(noticedata, 'price') price, json_extract_c(noticedata, 'productId') productId, json_extract_c(noticedata, 'currency') currency, left(create_time, 10) atime FROM `apple_notice_total`
					 where notificationtype in ('SUBSCRIBED','DID_RENEW', 'ONE_TIME_CHARGE')
					   and appaccounttoken rlike '^[A-Z0-9][A-Z0-9-]*[A-Z0-9]$'
				 and create_time between #{start} and #{end}
				and json_extract_c(noticedata, 'environment') != 'Sandbox'
				and json_extract_c(noticedata, 'storefront') != 'CHN'
					 and appid is not null
				 ) a
		 left join ods_currency_rate b on a.atime=left(b.tdate, 10) and a.currency=b.country
		GROUP BY appid, appAccountToken, productId

			</when>
			<otherwise>
			select appid, appAccountToken androidid, productId payname, sum(price/ 1000) price
			from (
					 SELECT appid, appAccountToken, json_extract_c(noticedata, 'price') price, json_extract_c(noticedata, 'productId') productId FROM `apple_notice_total`
					 where notificationtype in ('SUBSCRIBED','DID_RENEW', 'ONE_TIME_CHARGE')
					   and appaccounttoken rlike '^[A-Z0-9][A-Z0-9-]*[A-Z0-9]$'
				 and create_time between #{start} and #{end}
				and json_extract_c(noticedata, 'environment') != 'Sandbox'
				and json_extract_c(noticedata, 'storefront') = 'CHN'
					 and appid is not null
				 ) a
			GROUP BY appid, appAccountToken, productId
			</otherwise>
	   </choose>

	</select>
	<select id="selectAndroidCamerasSignCount" resultType="com.wbgame.task.cameras.CameraCostCounter">
-- 		安卓相机用户锚定签约用户
select m0.*,m1.week_users pay_user
from
(
select a.day tdate,a.appid,count(distinct a.android_id) sign_user,count(distinct b.android_id) cancel_user
from
(select day,appid,android_id
from pay_cycle_sign_log
where day between #{start} and #{end}
and sign_type='1'
and is_sign='1'
and original_order not in (select orderid from wb_pay_info where  date(createtime) between #{start} and #{end} and money &lt; 10 group by orderid) -- ********过滤低于0.1元的订单，此为测试订单
) a
left join
(select day,appid,android_id
from pay_cycle_sign_log
where day between #{start} and #{end}
and sign_type='2'
and is_sign='0'
and day=create_date  -- 当天解约的条件
) b
on a.day=b.day and a.appid=b.appid and a.android_id=b.android_id
group by a.day,a.appid
) m0
left join
(select day,appid,count(distinct android_id) week_users
from pay_cycle_sign_log
where day between #{start} and #{end}
and sign_type='1'
-- and is_sign='1' -- 20241026 增加条件
and original_order is not null
and original_order not in (select orderid from wb_pay_info where date(createtime) between #{start} and #{end} and money &lt; 10 group by orderid) -- ********过滤低于0.1元的订单，此为测试订单
group by day,appid
) m1
on m0.tdate=m1.day and m0.appid=m1.appid
	</select>
	<select id="selectIOSCamerasSubscribeCount"
			resultType="com.wbgame.task.cameras.CameraCostCounter">
		<choose>
			<when test="type == 2">
				SELECT appid,
					   sum(case when notificationType='SUBSCRIBED' and subtype='INITIAL_BUY' then 1 else 0 end) sign_user,
					   sum(case when notificationType='DID_CHANGE_RENEWAL_STATUS' and subtype='AUTO_RENEW_DISABLED' and appAccountToken in (
						   SELECT distinct appAccountToken FROM `apple_notice_total`
						   where notificationType='SUBSCRIBED'
							 and subtype='INITIAL_BUY'
							 and appaccounttoken rlike '^[A-Z0-9][A-Z0-9-]*[A-Z0-9]$'
						   and create_time between  #{start} and #{end}
						   and json_extract_c(noticedata, 'environment') != 'Sandbox'
							and json_extract_c(noticedata, 'storefront') != 'CHN'
							) then 1 else 0 end) cancel_user
							FROM `apple_notice_total`
						where
					appaccounttoken rlike '^[A-Z0-9][A-Z0-9-]*[A-Z0-9]$'
					and create_time between #{start} and #{end}
				  and json_extract_c(noticedata, 'environment') != 'Sandbox'
				  and json_extract_c(noticedata, 'storefront') != 'CHN'
				  and appid is not null
				GROUP BY appid
			</when>
			<otherwise>
				SELECT appid,
					   sum(case when notificationType='SUBSCRIBED' and subtype='INITIAL_BUY' then 1 else 0 end) sign_user,
					   sum(case when notificationType='DID_CHANGE_RENEWAL_STATUS' and subtype='AUTO_RENEW_DISABLED' and appAccountToken in (
						   SELECT distinct appAccountToken FROM `apple_notice_total`
						   where notificationType='SUBSCRIBED'
							 and subtype='INITIAL_BUY'
							 and appaccounttoken rlike '^[A-Z0-9][A-Z0-9-]*[A-Z0-9]$'
						   and create_time between  #{start} and #{end}
						   and json_extract_c(noticedata, 'environment') != 'Sandbox'
							and json_extract_c(noticedata, 'storefront') = 'CHN'
							) then 1 else 0 end) cancel_user
							FROM `apple_notice_total`
						where
					appaccounttoken rlike '^[A-Z0-9][A-Z0-9-]*[A-Z0-9]$'
					and create_time between #{start} and #{end}
				  and json_extract_c(noticedata, 'environment') != 'Sandbox'
				  and json_extract_c(noticedata, 'storefront') = 'CHN'
				  and appid is not null
				GROUP BY appid
			</otherwise>
		</choose>
	</select>
	<select id="selectCameraCancelCount" resultType="com.wbgame.task.cameras.WbPayInfoUserInfo">
		select appid, cancel_user androidid, payname, '' param3, price  from (
			 SELECT appid, create_time, json_extract_c(noticedata, 'productId') payname, json_extract_c(noticedata, 'price')/10 price,
					case when notificationType='DID_CHANGE_RENEWAL_STATUS' and subtype='AUTO_RENEW_DISABLED' and appAccountToken in (
						SELECT distinct appAccountToken FROM `apple_notice_total`
						where notificationType='SUBSCRIBED'
						  and subtype='INITIAL_BUY'
						  and appaccounttoken rlike '^[A-Z0-9][A-Z0-9-]*[A-Z0-9]$'
						and left(create_time,10) = #{tdate}
				 and json_extract_c(noticedata, 'environment') != 'Sandbox'
				 and json_extract_c(noticedata, 'storefront') = 'CHN'
		 ) then appAccountToken else null end cancel_user
		FROM `apple_notice_total`
		where
			appaccounttoken rlike '^[A-Z0-9][A-Z0-9-]*[A-Z0-9]$'
		  and left(create_time,10)  = #{tdate}
		  and json_extract_c(noticedata, 'environment') != 'Sandbox'
		  and json_extract_c(noticedata, 'storefront') = 'CHN'
		  and appid is not null
			) a
		where cancel_user is not null
-- 		GROUP BY appid
		union all
		select a.appid,android_id androidid,b.payname,b.param3,b.money price
		from pay_cycle_sign_log a
				 left join wb_pay_info b on a.original_order = b.orderid
		where day = #{tdate}
		  and sign_type='2'
		  and is_sign='0'
		  and day=create_date  -- 当天解约的条件
		  and money > 100
	</select>

    <sql id="getOppoReportCondition1">
		WHERE 1 = 1
		<if test="campany != null and campany != ''">
			AND campany in (${campany})
		</if>
		<if test="productName != null and productName != ''">
			AND productName LIKE "%"#{productName}"%"
		</if>
		<if test="productId != null and productId != ''">
			AND productId LIKE "%"#{productId}"%"
		</if>
		<if test="platform != null and platform != ''">
			AND platform LIKE "%"#{platform}"%"
		</if>
		<if test="start_date != null and end_date!= null">
			AND `day` <![CDATA[ >= ]]> #{start_date}
			AND `day` <![CDATA[ <= ]]> #{end_date}
		</if>
	</sql>

	<sql id="getOppoReportCondition2">
		<if test="group != null and group.size > 0">
			GROUP BY
			<foreach collection="group" index="index" item="item" separator=",">
				`${item}`
			</foreach>
		</if>
		<if test="order_str == null or order_str == ''">
			ORDER BY `day` ASC, SUM(revenue) DESC
		</if>
		<if test="order_str != null and order_str != ''">
			ORDER BY ${order_str}
		</if>
	</sql>


	<insert id="insertUmengAdSummary">
		replace into umeng_ad_summary (
		tdate,appname,appid,channel,media,source,temp_id,temp_name,addnum,actnum,avgnum,total_income,dau_arpu,daily_duration,
		all_total_pv,total_splash_pv,total_plaque_pv,total_plaque_video_pv,total_banner_pv,total_video_pv,total_native_msg_pv,
		total_suspend_icon_pv,all_total_click,total_splash_click,total_plaque_click,total_plaque_video_click,total_banner_click,
		total_video_click,total_native_msg_click,total_suspend_icon_click,system_splash_ecpm,splash_ecpm,native_splash_ecpm,
		plaque_ecpm,native_new_plaque_ecpm,plaque_video_ecpm,banner_ecpm,native_new_banner_ecpm,video_ecpm,native_msg_ecpm,
		suspend_icon_ecpm,native_plaque_ecpm,native_banner_ecpm,splash_ctr,native_splash_ctr,plaque_ctr,native_new_plaque_ctr,
		plaque_video_ctr,banner_ctr,native_new_banner_ctr,video_ctr,native_msg_ctr,suspend_icon_ctr,native_plaque_ctr,native_banner_ctr,
		splash_cpc,native_splash_cpc,plaque_cpc,native_new_plaque_cpc,plaque_video_cpc,banner_cpc,native_new_banner_cpc,video_cpc,
		native_msg_cpc,suspend_icon_cpc,native_plaque_cpc,native_banner_cpc
		) values
		<foreach collection="fList" item="li" separator=",">
			(#{li.tdate},#{li.appname},#{li.appid},#{li.channel},#{li.media},#{li.source},#{li.temp_id},#{li.temp_name},
			#{li.addnum},#{li.actnum},#{li.avgnum},#{li.total_income},#{li.dau_arpu},#{li.daily_duration},
			#{li.all_total_pv},#{li.total_splash_pv},#{li.total_plaque_pv},#{li.total_plaque_video_pv},
			#{li.total_banner_pv},#{li.total_video_pv},#{li.total_native_msg_pv},#{li.total_suspend_icon_pv},
			#{li.all_total_click},#{li.total_splash_click},#{li.total_plaque_click},#{li.total_plaque_video_click},
			#{li.total_banner_click},#{li.total_video_click},#{li.total_native_msg_click},#{li.total_suspend_icon_click},
			#{li.system_splash_ecpm},#{li.splash_ecpm},#{li.native_splash_ecpm},#{li.plaque_ecpm},
			#{li.native_new_plaque_ecpm},#{li.plaque_video_ecpm},#{li.banner_ecpm},#{li.native_new_banner_ecpm},
			#{li.video_ecpm},#{li.native_msg_ecpm},#{li.suspend_icon_ecpm},#{li.native_plaque_ecpm},#{li.native_banner_ecpm},
			#{li.splash_ctr},#{li.native_splash_ctr},#{li.plaque_ctr},
			#{li.native_new_plaque_ctr},#{li.plaque_video_ctr},#{li.banner_ctr},#{li.native_new_banner_ctr},
			#{li.video_ctr},#{li.native_msg_ctr},#{li.suspend_icon_ctr},#{li.native_plaque_ctr},#{li.native_banner_ctr},
			#{li.splash_cpc},#{li.native_splash_cpc},#{li.plaque_cpc},#{li.native_new_plaque_cpc},
			#{li.plaque_video_cpc},#{li.banner_cpc},#{li.native_new_banner_cpc},#{li.video_cpc},
			#{li.native_msg_cpc},#{li.suspend_icon_cpc},#{li.native_plaque_cpc},#{li.native_banner_cpc}
			)
		</foreach>
	</insert>


	<insert id="insertReportOperationDaily">
		replace into report_operation_summary_daily (
		`day`,spend,app,channel,appid,ad_platform,buy_installs,buy_cost,activeLtv1,add_roi,lt3,lt7,avg_duration,f_roi_total,
		buy_spendRoi,t_roi_channel,roi,buy_spendRevenue,buy_profit,natural_install,natural_revenue,natural_spendRevenue,
		buy_rate,add_num,active_num,um_add_num,um_active_num,revenue,revenue_profit,revenue_profit_rate,cost,add_revenue,
		buy_revenue,revenue_channel,arpu,install_arpu
		) values
		<foreach collection="dataList" item="li" separator=",">
			(#{li.day},#{li.spend},#{li.app},#{li.channel},#{li.appId},#{li.ad_platform},#{li.buy_installs},#{li.buy_cost},
			#{li.activeLtv1},#{li.add_roi},#{li.lt3},#{li.lt7},#{li.avg_duration},#{li.f_roi_total},#{li.buy_spendRoi},
			#{li.t_roi_channel},#{li.roi},#{li.buy_spendRevenue},#{li.buy_profit},#{li.natural_install},#{li.natural_revenue},
			#{li.natural_spendRevenue},#{li.buy_rate},#{li.add_num},#{li.active_num},#{li.um_add_num},
			#{li.um_active_num},#{li.revenue},#{li.revenue_profit},#{li.revenue_profit_rate},
			#{li.cost},#{li.add_revenue},#{li.buy_revenue},#{li.revenueChannel},#{li.arpu},#{li.install_arpu}
			)
		</foreach>
	</insert>
	<insert id="replaceIntoUserChannelTotal">
		replace into umeng_user_channel_total (tdate, appid, app_key,
		add_num, act_num,start_num, install_channel,duration,daily_per_duration) values
		<foreach collection="channels" index="index" item="item" separator=",">
			(#{item.tdate},#{item.appid}, #{item.appKey,jdbcType=VARCHAR},
			#{item.addNum,jdbcType=INTEGER}, #{item.actNum,jdbcType=INTEGER},
			#{item.startNum,jdbcType=INTEGER}, #{item.installChannel,jdbcType=VARCHAR},
			#{item.duration,jdbcType=VARCHAR}, #{item.dailyPerDuration,jdbcType=VARCHAR})
		</foreach>
	</insert>


</mapper>