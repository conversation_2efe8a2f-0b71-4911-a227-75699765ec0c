<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.HelpMapper">

	<insert id="insertWxHelpUserInfoList" parameterType="java.util.List">
		insert into wx_help_user_info (
			wbappid,
			openid,
			hold_money,
			hold_msg,
			sub_list,
			wxname,
			wxicon,
			create_time,
			last_time

		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.wbappid},
			#{li.openid},
			#{li.hold_money},
			#{li.hold_msg},
			#{li.sub_list},
			#{li.wxname},
			#{li.wxicon},
			now(),
			now())
		</foreach>	
		ON DUPLICATE KEY UPDATE 
		hold_money=VALUES(hold_money),
		hold_msg=VALUES(hold_msg),
		sub_list=VALUES(sub_list),
		wxname=VALUES(wxname),
		wxicon=VALUES(wxicon),
		last_time=VALUES(last_time)
	</insert>
	
	<insert id="insertWxFdsUserInfoList" parameterType="java.util.List">
		insert into wx_fds_user_info (
			wbappid,
			openid,
			hold_money,
			hold_coins,
			hold_msg,
			sub_list,
			wxname,
			wxicon,
			create_time,
			last_time

		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.wbappid},
			#{li.openid},
			#{li.hold_money},
			#{li.hold_coins},
			#{li.hold_msg},
			#{li.sub_list},
			#{li.wxname},
			#{li.wxicon},
			now(),
			now())
		</foreach>	
		ON DUPLICATE KEY UPDATE 
		hold_money=VALUES(hold_money),
		hold_coins=VALUES(hold_coins),
		hold_msg=VALUES(hold_msg),
		sub_list=VALUES(sub_list),
		wxname=VALUES(wxname),
		wxicon=VALUES(wxicon),
		last_time=VALUES(last_time)
	</insert>
	
	<insert id="insertWxGameBoxUserInfoList" parameterType="java.util.List">
		insert into wx_gamebox_user_info (
			wbappid,
			openid,
			hold_money,
			hold_coins,
			wxname,
			wxicon,
			create_time,
			last_time

		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.wbappid},
			#{li.openid},
			#{li.hold_money},
			#{li.hold_coins},
			#{li.wxname},
			#{li.wxicon},
			now(),
			now())
		</foreach>	
		ON DUPLICATE KEY UPDATE 
		hold_money=VALUES(hold_money),
		hold_coins=VALUES(hold_coins),
		wxname=VALUES(wxname),
		wxicon=VALUES(wxicon),
		last_time=VALUES(last_time)
	</insert>
	
	<insert id="insertWxBox2333UserInfoList" parameterType="java.util.List">
		insert into wx_box2333_user_info (
			wbappid,
			openid,
			hold_money,
			is_over,
			sub_list,
			box_openid,
			wxname,
			wxicon,
			create_time,
			last_time

		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.wbappid},
			#{li.openid},
			#{li.hold_money},
			#{li.is_over},
			#{li.sub_list},
			#{li.box_openid},
			#{li.wxname},
			#{li.wxicon},
			now(),
			now())
		</foreach>	
		ON DUPLICATE KEY UPDATE 
		hold_money=VALUES(hold_money),
		is_over=VALUES(is_over),
		sub_list=VALUES(sub_list),
		box_openid=VALUES(box_openid),
		wxname=VALUES(wxname),
		wxicon=VALUES(wxicon),
		last_time=VALUES(last_time)
	</insert>
	<!-- <insert id="insertWxHelpPutcashList" parameterType="java.util.List">
		insert into wx_money_putcash (
			wbappid,
			openid,
			amount,
			type,
			create_time
		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.wbappid},
			#{li.openid},
			#{li.amount},
			2,
			now())
		</foreach>	
	</insert> -->
	
	<insert id="insertWxSimpleHelpUserInfoList" parameterType="java.util.List">
		insert into wx_help_user_info (
			wbappid,
			openid,
			sub_list,
			wxname,
			wxicon,
			create_time,
			last_time

		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.wbappid},
			#{li.openid},
			#{li.sub_list},
			#{li.wxname},
			#{li.wxicon},
			now(),
			now())
		</foreach>	
		ON DUPLICATE KEY UPDATE 
		sub_list=VALUES(sub_list),
		wxname=VALUES(wxname),
		wxicon=VALUES(wxicon),
		last_time=VALUES(last_time)
	</insert>
	
	
	<select id="selectWxHelpUserInfo" parameterType="java.lang.String"
		resultType="com.wbgame.pojo.HelpInfoVo">
		
		select * from wx_help_user_info
		where wbappid = #{wbappid} and openid = #{openid} limit 1
	</select>
	
	<select id="selectWxSimpleHelpUserInfo" parameterType="java.lang.String"
		resultType="com.wbgame.pojo.SimpleHelpInfoVo">
		
		select * from wx_help_user_info
		where wbappid = #{wbappid} and openid = #{openid} limit 1
	</select>

	<select id="selectWxFdsUserInfo" parameterType="java.lang.String"
		resultType="com.wbgame.pojo.FdsInfoVo">
		
		select * from wx_fds_user_info
		where wbappid = #{wbappid} and openid = #{openid} limit 1
	</select>
	
	<select id="selectWxGameBoxUserInfo" parameterType="java.lang.String"
		resultType="com.wbgame.pojo.GameBoxInfoVo">
		
		select * from wx_gamebox_user_info
		where wbappid = #{wbappid} and openid = #{openid} limit 1
	</select>
	
	<select id="selectWxBox2333UserInfo" parameterType="java.lang.String"
		resultType="com.wbgame.pojo.Box2333InfoVo">
		
		select * from wx_box2333_user_info
		where wbappid = #{wbappid} and openid = #{openid} limit 1
	</select>
	
	
</mapper>