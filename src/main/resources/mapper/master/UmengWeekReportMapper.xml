<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.UmengWeekReportMapper">

    <select id="getUmengWeekReportBaseDataList" parameterType="java.util.Map" resultType="com.wbgame.pojo.advert.UmChWeekReportVo">
        select
        cc.appid,
        cc.cname channel,
        ROUND(IFNULL(SUM(cc.ad_revenue),0) ,2) advFee,
        IFNULL(SUM(actnum),0) actNum,
        IFNULL(SUM(addnum),0) addNum,
        IFNULL(ROUND((SUM(cc.ad_revenue)/SUM(cc.actnum)),2),0) arpu,
        IFNULL(ROUND((SUM(launch)/SUM(cc.actnum)),2),0) avgInit,
        IFNULL(ROUND(SUM(SUBSTRING_INDEX(daily_duration,':',1)*3600+SUBSTRING_INDEX (SUBSTRING_INDEX(daily_duration,':',2),':',-1)*60+SUBSTRING_INDEX(daily_duration,':',-1))/count(if(daily_duration!='',true,null)),0),0) liveTime,
        IFNULL(ROUND(AVG(keep_num1),2),0) retention1,
        IFNULL(ROUND(AVG(keep_num7),2),0) retention7,
        IFNULL(ROUND(1+AVG(keep_num1+keep_num2+keep_num3+keep_num4+keep_num5+keep_num6+keep_num7)/100,2),0) sevenlt
        from
        (
        SELECT  aa.*,ad_revenue FROM
        umeng_channel_total aa
        INNER JOIN dn_app_cha_revenue_total bb ON aa.tdate = bb.tdate
        AND aa.appid = bb.appid
        AND aa.cname = bb.cha_id where
        aa.tdate BETWEEN #{date1} AND #{date2}
        <if test="appid != null and appid != ''">
            and aa.appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and aa.cname in (${channel})
        </if>
        ) cc
        group by cc.appid,cc.cname
        order by addNum desc
    </select>


</mapper>