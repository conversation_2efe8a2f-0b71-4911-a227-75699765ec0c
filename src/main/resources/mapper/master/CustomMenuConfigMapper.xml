<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.CustomMenuConfigMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.advert.CustomMenuConfig">
    <!--@mbg.generated-->
    <!--@Table custom_menu_config-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_owner" jdbcType="VARCHAR" property="createOwner" />
    <result column="update_owner" jdbcType="VARCHAR" property="updateOwner" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, create_time, update_time, create_owner, update_owner, `status`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from custom_menu_config
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from custom_menu_config
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.wbgame.pojo.advert.CustomMenuConfig">
    <!--@mbg.generated-->
    insert into custom_menu_config (id, `name`, create_time, 
      update_time, create_owner, update_owner, 
      `status`)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createOwner,jdbcType=VARCHAR}, #{updateOwner,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.wbgame.pojo.advert.CustomMenuConfig">
    <!--@mbg.generated-->
    insert into custom_menu_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createOwner != null">
        create_owner,
      </if>
      <if test="updateOwner != null">
        update_owner,
      </if>
      <if test="status != null">
        `status`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwner != null">
        #{createOwner,jdbcType=VARCHAR},
      </if>
      <if test="updateOwner != null">
        #{updateOwner,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.advert.CustomMenuConfig">
    <!--@mbg.generated-->
    update custom_menu_config
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOwner != null">
        create_owner = #{createOwner,jdbcType=VARCHAR},
      </if>
      <if test="updateOwner != null">
        update_owner = #{updateOwner,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.advert.CustomMenuConfig">
    <!--@mbg.generated-->
    update custom_menu_config
    set `name` = #{name,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_owner = #{createOwner,jdbcType=VARCHAR},
      update_owner = #{updateOwner,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-05-18-->
  <select id="selectByAll" resultMap="BaseResultMap">
    select
    id, `name`, DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createStr, DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') updateStr, create_owner, update_owner, `status`
    from custom_menu_config
    <where>
      <if test="id != null and id != ''">
        and id=#{id,jdbcType=VARCHAR}
      </if>
      <if test="name != null and name != ''">
        and `name`=#{name,jdbcType=VARCHAR}
      </if>
      <if test="status != null">
        and `status`=#{status,jdbcType=INTEGER}
      </if>
    </where>
  </select>

  <select id="getPermissionMenus" resultType="java.util.Map">
          select id,name from custom_menu_config
  </select>
</mapper>