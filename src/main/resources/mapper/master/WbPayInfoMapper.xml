<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.WbPayInfoMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.custom.WbPayInfo">
        <id column="orderid" property="orderid" jdbcType="VARCHAR"/>
        <result column="uid" property="uid" jdbcType="VARCHAR"/>
        <result column="money" property="money" jdbcType="INTEGER"/>
        <result column="paytype" property="paytype" jdbcType="VARCHAR"/>
        <result column="imei" property="imei" jdbcType="VARCHAR"/>
        <result column="pid" property="pid" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="orderstatus" property="orderstatus" jdbcType="VARCHAR"/>
        <result column="payname" property="payname" jdbcType="VARCHAR"/>
        <result column="paynote" property="paynote" jdbcType="VARCHAR"/>
        <result column="createtime" property="createtime" jdbcType="TIMESTAMP"/>
        <result column="param1" property="param1" jdbcType="VARCHAR"/>
        <result column="param2" property="param2" jdbcType="VARCHAR"/>
        <result column="param3" property="param3" jdbcType="VARCHAR"/>
        <result column="lsn" property="lsn" jdbcType="VARCHAR"/>
        <result column="chaid" property="chaid" jdbcType="VARCHAR"/>
        <result column="androidid" property="androidid" jdbcType="VARCHAR"/>
        <result column="oaid" property="oaid" jdbcType="VARCHAR"/>
        <result column="idfa" property="idfa" jdbcType="VARCHAR"/>
        <result column="lsn_new" property="lsnNew" jdbcType="VARCHAR"/>
        <result column="payid" property="payid" jdbcType="VARCHAR"/>
        <result column="userid" property="userid" jdbcType="VARCHAR"/>
        <result column="custom" property="custom" jdbcType="VARCHAR"/>
        <result column="buy_id" property="buyId" jdbcType="VARCHAR"/>
        <result column="pn" property="pn" jdbcType="VARCHAR"/>
        <result column="model" property="model" jdbcType="VARCHAR"/>
        <result column="is_new" property="isNew" jdbcType="INTEGER"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        orderid
        , uid, money, paytype, imei, pid, appid, orderstatus, payname, paynote, createtime,
    param1, param2, param3, lsn, chaid, androidid, oaid, idfa, lsn_new, payid, userid, 
    custom, buy_id, pn, model, is_new
    </sql>

    <select id="selectWbPayInfo" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
            orderid,
            round( money / 100, 2 ) money,
            paytype,
            imei,
            app_name,
            appid,
            pid,
            orderstatus,
            payname,
            paynote,
            createtime,
            param1,
            param2,
            param3,
            userid,
            custom,
            pn,
            model ,
            IF( is_new = 0,"老用户", IF ( is_new = 1,"新用户","未知") ) is_new
        FROM
            wb_pay_info a
                LEFT JOIN app_info b ON a.appid = b.id
        WHERE
            createtime BETWEEN CURRENT_DATE
                AND now()
        <if test="orderid != null and orderid != ''">
            and orderId = #{orderid}
        </if>

        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>

        <if test="appidList != null and appidList.size> 0">
            and appid in
            <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                #{appid}
            </foreach>
        </if>

        <if test="paytypeList != null and paytypeList.size > 0">
            and paytype in
            <foreach collection="paytypeList" item="paytype" open="(" separator="," close=")">
                #{paytype}
            </foreach>
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by createtime desc
            </otherwise>
        </choose>

    </select>

    <update id="updateOrderMark">
        update wb_pay_info set param3 = #{mark} where orderid = #{orderid}
    </update>
</mapper>