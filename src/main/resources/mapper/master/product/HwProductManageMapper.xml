<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.product.HwProductManageMapper">

    <insert id="batchInsert">
        insert into hw_product_manage (appid,channel,productNo,productName,productDesc,price,currency,purchaseType,status,create_time,create_owner)
        values
        <foreach collection="productList" item="product" separator=",">
            (#{product.appid},#{product.channel},#{product.productNo},#{product.productName},#{product.productDesc},#{product.price},#{product.currency},#{product.purchaseType},#{product.status},now(),#{product.create_owner})
        </foreach>
    </insert>



    <update id="updateProduct">
        update hw_product_manage
        <set>
            <if test="manage.channel != null and manage.channel != ''">
                channel = #{manage.channel},
            </if>
            <if test="manage.productName != null and manage.productName != ''">
                productName = #{manage.productName},
            </if>
            <if test="manage.productDesc != null and manage.productDesc != ''">
                productDesc = #{manage.productDesc},
            </if>
            <if test="manage.price != null and manage.price != ''">
                price = #{manage.price},
            </if>
            <if test="manage.status != null and manage.status != ''">
                status = #{manage.status},
            </if>
            <if test="manage.update_owner != null and manage.update_owner != ''">
                update_owner = #{manage.update_owner},
            </if>
            update_time = now()
        </set>
        where id = #{manage.id}
    </update>


    <select id="queryList" resultType="com.wbgame.pojo.product.HwProductManage">
        select id,appid,channel,productNo,productName,productDesc,round(price/100,2) price,currency,purchaseType,status,create_time,update_time,create_owner,update_owner from hw_product_manage
        <where>
            <if test="dto.appid != null and dto.appid != ''">
                and appid in (${dto.appid})
            </if>
            <if test="dto.channel != null and dto.channel != ''">
                and channel in (${dto.channel})
            </if>
            <if test="dto.productNo != null and dto.productNo != ''">
                and productNo like concat('%',#{dto.productNo},'%')
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and productName like concat('%',#{dto.productName},'%')
            </if>
            <if test="dto.currency != null and dto.currency != ''">
                and currency = #{dto.currency}
            </if>
            <if test="dto.purchaseType != null and dto.purchaseType != ''">
                and purchaseType = #{dto.purchaseType}
            </if>
            <if test="dto.status != null and dto.status != ''">
                and status = #{dto.status}
            </if>
        </where>
        <choose>
            <when test="dto.order_str != null and dto.order_str != ''">
                order by ${dto.order_str}
            </when>
            <otherwise>
                order by create_time desc
            </otherwise>
        </choose>
    </select>
</mapper>