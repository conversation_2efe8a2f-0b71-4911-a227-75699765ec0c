<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.YyhzTwoMapper">
	
	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>
	
	
	<select id="selectAdshowAdduser" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			concat(tdate,'') tdate,appid,channel,adpos_type, -- prjid,user_label,
		addnum,
			TRUNCATE(sum(pv1)/addnum,2) pv1,
			TRUNCATE(sum(pv2)/addnum,2) pv2,
			TRUNCATE(sum(pv3)/addnum,2) pv3,
			TRUNCATE(sum(pv4)/addnum,2) pv4,
			TRUNCATE(sum(pv5)/addnum,2) pv5,
			TRUNCATE(sum(pv6)/addnum,2) pv6,
			TRUNCATE(sum(pv7)/addnum,2) pv7,
			TRUNCATE(sum(pv8)/addnum,2) pv8,
			TRUNCATE(sum(pv9)/addnum,2) pv9,
			TRUNCATE(sum(pv10)/addnum,2) pv10,
			TRUNCATE(sum(pv11)/addnum,2) pv11,
			TRUNCATE(sum(pv12)/addnum,2) pv12,
			TRUNCATE(sum(pv13)/addnum,2) pv13,
			TRUNCATE(sum(pv14)/addnum,2) pv14
		from yyhz_0308.dn_adshow_adduser2 aa 
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="channel != null and channel != ''">
			and channel = #{channel} 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid}
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		
		 group by tdate,appid,channel,adpos_type -- ,prjid,user_label
		 order by tdate desc,addnum desc
	</select>
	<select id="selectAdshowActuser" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			concat(tdate,'') tdate,appid,channel,
			<if test="adpos_type_group != null and adpos_type_group != ''">
				adpos_type,
			</if>
			actnum,
			addnum,
			CONCAT(truncate(addnum/actnum*100,2),'%') as add_rate,
			aduser,
			global_aduser,
			CONCAT(truncate(aduser/actnum*100,2),'%') as seep_rate,
			CONCAT(truncate(aduser/addnum*100,2),'%') as add_seep_rate,
			CONCAT(truncate(global_aduser/actnum*100,2),'%') as global_seep_rate,
			CONCAT(truncate(global_aduser/addnum*100,2),'%') as add_global_seep_rate,
			
			CONCAT(truncate(SUM(pv0)/${user}*100,2),'%') as pv0,
			CONCAT(truncate(SUM(pv1)/${user}*100,2),'%') as pv1,
			CONCAT(truncate(SUM(pv2)/${user}*100,2),'%') as pv2,
			CONCAT(truncate(SUM(pv3)/${user}*100,2),'%') as pv3,
			CONCAT(truncate(SUM(pv4)/${user}*100,2),'%') as pv4,
			CONCAT(truncate(SUM(pv5)/${user}*100,2),'%') as pv5,
			CONCAT(truncate(SUM(pv6)/${user}*100,2),'%') as pv6,
			CONCAT(truncate(SUM(pv7)/${user}*100,2),'%') as pv7,
			CONCAT(truncate(SUM(pv8)/${user}*100,2),'%') as pv8,
			CONCAT(truncate(SUM(pv9)/${user}*100,2),'%') as pv9,
			CONCAT(truncate(SUM(pv10)/${user}*100,2),'%') as pv10,
			CONCAT(truncate(SUM(pv11)/${user}*100,2),'%') as pv11,
			CONCAT(truncate(SUM(pv12)/${user}*100,2),'%') as pv12,
			CONCAT(truncate(SUM(pv13)/${user}*100,2),'%') as pv13,
			CONCAT(truncate(SUM(pv14)/${user}*100,2),'%') as pv14
			
		from yyhz_0308.dn_adshow_actuser2 aa 
		where tdate BETWEEN #{start_date} AND #{end_date} 
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid}
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="user_type != null and user_type != ''">
			and active_type = #{user_type} 
		</if>
		
		 group by tdate,appid,channel
		<if test="adpos_type_group != null and adpos_type_group != ''">
			,adpos_type
		</if>
		 order by tdate desc,actnum desc
	</select>
	
	
	<!-- 活跃用户价值分析  -->
  	<select id="selectDnRevenueActuser" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_revenue_actuser_sql"/>
	</select>
	<select id="selectDnRevenueActuserSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			TRUNCATE(SUM(xx.sum_revenue),2) sum_revenue
			
		from (<include refid="dn_revenue_actuser_sql"/>) xx
	</select>
	<sql id="dn_revenue_actuser_sql">
		select 
			tdate,appid,cha_id,adpos_type, -- prjid,user_label,
			actnum,addnum,sum_revenue,CONCAT(TRUNCATE(addnum / actnum*100, 1),'%') addrate,
			CONCAT(TRUNCATE(pv1 / sum_revenue*100, 1),'%') pv1,
			CONCAT(TRUNCATE(pv2 / sum_revenue*100, 1),'%') pv2,
			CONCAT(TRUNCATE(pv3 / sum_revenue*100, 1),'%') pv3,
			CONCAT(TRUNCATE(pv4 / sum_revenue*100, 1),'%') pv4,
			CONCAT(TRUNCATE(pv5 / sum_revenue*100, 1),'%') pv5,
			CONCAT(TRUNCATE(pv6 / sum_revenue*100, 1),'%') pv6,
			CONCAT(TRUNCATE(pv7 / sum_revenue*100, 1),'%') pv7,
			CONCAT(TRUNCATE(pv8 / sum_revenue*100, 1),'%') pv8,
			CONCAT(TRUNCATE(pv9 / sum_revenue*100, 1),'%') pv9,
			CONCAT(TRUNCATE(pv10 / sum_revenue*100, 1),'%') pv10,
			CONCAT(TRUNCATE(pv11 / sum_revenue*100, 1),'%') pv11,
			CONCAT(TRUNCATE(pv12 / sum_revenue*100, 1),'%') pv12,
			CONCAT(TRUNCATE(pv13 / sum_revenue*100, 1),'%') pv13,
			CONCAT(TRUNCATE(pv14 / sum_revenue*100, 1),'%') pv14
		from 
			(select 
				tdate,appid,cha_id,adpos_type,
				actnum,addnum,prjid,user_label,
				TRUNCATE(SUM(pv1+pv2+pv3+pv4+pv5+pv6+pv7+pv8+pv9+pv10+pv11+pv12+pv13+pv14),2) sum_revenue,
				SUM(pv1) pv1,SUM(pv2) pv2,SUM(pv3) pv3,SUM(pv4) pv4,SUM(pv5) pv5,SUM(pv6) pv6,SUM(pv7) pv7,
				SUM(pv8) pv8,SUM(pv9) pv9,SUM(pv10) pv10,SUM(pv11) pv11,SUM(pv12) pv12,SUM(pv13) pv13,SUM(pv14) pv14
				
			from yyhz_0308.dn_extend_revenue_actuser2 
			where tdate BETWEEN #{sdate} AND #{edate} 
			<include refid="dn_extend_where_sql"/> 
			
			<if test="user_type != null and user_type != ''">
				and active_type = #{user_type} 
			</if>
-- 			group by tdate,appid,cha_id,adpos_type,prjid,user_label) aa
			group by tdate,appid,cha_id,adpos_type) aa

		order by tdate desc,sum_revenue desc
	</sql>
	
	<!-- 活跃用户ecpm分析  -->
  	<select id="selectDnEcpmActuser" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_ecpm_actuser_sql"/>
	</select>
	<select id="selectDnEcpmActuserSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			TRUNCATE(SUM(xx.sum_revenue),2) sum_revenue,
			TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.sum_pv)*1000, 2) ecpm
			
		from (<include refid="dn_ecpm_actuser_sql"/>) xx
	</select>
	<sql id="dn_ecpm_actuser_sql">
		select 
			tdate,appid,cha_id,adpos_type, -- prjid,user_label,
			actnum,addnum,sum_revenue,sum_pv,CONCAT(TRUNCATE(addnum / actnum*100, 1),'%') addrate,
			TRUNCATE(sum_revenue / sum_pv*1000, 2) ecpm,
			TRUNCATE(ar1 / pv1*1000, 2) pv1,
			TRUNCATE(ar2 / pv2*1000, 2) pv2,
			TRUNCATE(ar3 / pv3*1000, 2) pv3,
			TRUNCATE(ar4 / pv4*1000, 2) pv4,
			TRUNCATE(ar5 / pv5*1000, 2) pv5,
			TRUNCATE(ar6 / pv6*1000, 2) pv6,
			TRUNCATE(ar7 / pv7*1000, 2) pv7,
			TRUNCATE(ar8 / pv8*1000, 2) pv8,
			TRUNCATE(ar9 / pv9*1000, 2) pv9,
			TRUNCATE(ar10 / pv10*1000, 2) pv10,
			TRUNCATE(ar11 / pv11*1000, 2) pv11,
			TRUNCATE(ar12 / pv12*1000, 2) pv12,
			TRUNCATE(ar13 / pv13*1000, 2) pv13,
			TRUNCATE(ar14 / pv14*1000, 2) pv14
		from 
			(select 
				tdate,appid,cha_id,adpos_type,
				actnum,addnum,prjid,user_label,
				TRUNCATE(SUM(pv1+pv2+pv3+pv4+pv5+pv6+pv7+pv8+pv9+pv10+pv11+pv12+pv13+pv14),2) sum_pv,
				TRUNCATE(SUM(ar1+ar2+ar3+ar4+ar5+ar6+ar7+ar8+ar9+ar10+ar11+ar12+ar13+ar14),2) sum_revenue,
				SUM(pv1) pv1,SUM(pv2) pv2,SUM(pv3) pv3,SUM(pv4) pv4,SUM(pv5) pv5,SUM(pv6) pv6,SUM(pv7) pv7,
				SUM(pv8) pv8,SUM(pv9) pv9,SUM(pv10) pv10,SUM(pv11) pv11,SUM(pv12) pv12,SUM(pv13) pv13,SUM(pv14) pv14,
				SUM(ar1) ar1,SUM(ar2) ar2,SUM(ar3) ar3,SUM(ar4) ar4,SUM(ar5) ar5,SUM(ar6) ar6,SUM(ar7) ar7,
				SUM(ar8) ar8,SUM(ar9) ar9,SUM(ar10) ar10,SUM(ar11) ar11,SUM(ar12) ar12,SUM(ar13) ar13,SUM(ar14) ar14
				
			from yyhz_0308.dn_extend_ecpm_actuser2 
			where tdate BETWEEN #{sdate} AND #{edate} 
			<include refid="dn_extend_where_sql"/> 
			
			<if test="user_type != null and user_type != ''">
				and active_type = #{user_type} 
			</if>
-- 			group by tdate,appid,cha_id,adpos_type,prjid,user_label) aa
			group by tdate,appid,cha_id,adpos_type) aa

		order by tdate desc,sum_revenue desc
	</sql>
	
	<!-- 新用户ltv递进  -->
  	<select id="selectSumLtvAdduser" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			xx.*,
			(ltv1+ltv2+ltv3) sum_ltv3,
			(ltv1+ltv2+ltv3+ltv4+ltv5+ltv6+ltv7) sum_ltv7
			
		from (<include refid="dn_sumltv_adduser_sql"/>) xx
	</select>
	<select id="selectSumLtvAdduserSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			TRUNCATE(SUM(xx.ltv1), 2) ltv1,
			TRUNCATE(SUM(xx.ltv2), 2) ltv2,
			TRUNCATE(SUM(xx.ltv3), 2) ltv3,
			TRUNCATE(SUM(xx.ltv4), 2) ltv4,
			TRUNCATE(SUM(xx.ltv5), 2) ltv5,
			TRUNCATE(SUM(xx.ltv6), 2) ltv6,
			TRUNCATE(SUM(xx.ltv7), 2) ltv7,
			SUM(xx.ltv1)+SUM(xx.ltv2)+SUM(xx.ltv3) sum_ltv3,
			SUM(xx.ltv1)+SUM(xx.ltv2)+SUM(xx.ltv3)+SUM(xx.ltv4)+SUM(xx.ltv5)+SUM(xx.ltv6)+SUM(xx.ltv7) sum_ltv7 
			
		from (<include refid="dn_sumltv_adduser_sql"/>) xx
	</select>
	<sql id="dn_sumltv_adduser_sql">
		select 
			tdate,appid,cha_id -- ,prjid,user_label
			<if test="adpos_type_group != null and adpos_type_group != ''">,adpos_type</if>
			,addnum,
			SUM(ar1) ar1,
			SUM(ar2) ar2,
			SUM(ar3) ar3,
			SUM(ar4) ar4,
			SUM(ar5) ar5,
			SUM(ar6) ar6,
			SUM(ar7) ar7,
			TRUNCATE(SUM(ar1)/addnum, 2) ltv1,
			TRUNCATE(SUM(ar2)/addnum, 2) ltv2,
			TRUNCATE(SUM(ar3)/addnum, 2) ltv3,
			TRUNCATE(SUM(ar4)/addnum, 2) ltv4,
			TRUNCATE(SUM(ar5)/addnum, 2) ltv5,
			TRUNCATE(SUM(ar6)/addnum, 2) ltv6,
			TRUNCATE(SUM(ar7)/addnum, 2) ltv7
			
		from yyhz_0308.dn_extend_sumltv_adduser2 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<include refid="dn_extend_where_sql"/> 
		group by tdate,appid,cha_id -- ,prjid,user_label
		<if test="adpos_type_group != null and adpos_type_group != ''">,adpos_type</if>
		
		order by tdate desc,addnum desc
	</sql>
	
	<!-- 新用户ecpm分析  -->
  	<select id="selectDnEcpmAdduser" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_ecpm_adduser_sql"/>
	</select>
	<select id="selectDnEcpmAdduserSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			*
		from (<include refid="dn_ecpm_adduser_sql"/>) xx
	</select>
	<sql id="dn_ecpm_adduser_sql">
		select 
			tdate,appid,cha_id,adpos_type, -- prjid,user_label,
			addnum,
			TRUNCATE(SUM(ar1) / SUM(pv1)*1000, 2) ecpm1,
			TRUNCATE(SUM(ar2) / SUM(pv2)*1000, 2) ecpm2,
			TRUNCATE(SUM(ar3) / SUM(pv3)*1000, 2) ecpm3,
			TRUNCATE(SUM(ar4) / SUM(pv4)*1000, 2) ecpm4,
			TRUNCATE(SUM(ar5) / SUM(pv5)*1000, 2) ecpm5,
			TRUNCATE(SUM(ar6) / SUM(pv6)*1000, 2) ecpm6,
			TRUNCATE(SUM(ar7) / SUM(pv7)*1000, 2) ecpm7
			
		from yyhz_0308.dn_extend_ecpm_adduser2 
		where tdate BETWEEN #{sdate} AND #{edate} 
		<include refid="dn_extend_where_sql"/> 
		group by tdate,appid,cha_id,adpos_type -- ,prjid,user_label
		
		order by tdate desc,addnum desc
	</sql>
	
	
	<sql id="dn_extend_where_sql">
		<if test="appid != null and appid != ''">
			and appid in (${appid}) 
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid in (${prjid}) 
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id}) 
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type} 
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name}) 
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media}) 
		</if>
		<if test="cha_sub_launch != null and cha_sub_launch != ''">
			and cha_sub_launch in (${cha_sub_launch}) 
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps}) 
		</if>
	</sql>

</mapper>