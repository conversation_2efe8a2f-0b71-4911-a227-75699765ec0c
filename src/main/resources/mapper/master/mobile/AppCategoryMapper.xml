<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.mobile.AppCategoryMapper">

    <insert id="addTwoAppCategory">
        insert into two_app_category(name,remark,create_time,update_time,create_owner,update_owner) values
        (#{name},#{remark},now(),now(),#{create_owner},#{update_owner})
    </insert>

    <update id="updateTwoAppCategory">
        update two_app_category set
        name = #{name},
        remark = #{remark},
        update_owner = #{update_owner},
        update_time = now()
        where id = #{id}
    </update>

    <delete id="deleteTwoAppCategory">
        delete from two_app_category where id = #{id} limit 1
    </delete>

    <select id="selectTwoAppCategory" resultType="com.wbgame.pojo.view.TwoAppCategoryVo">
        select id,name,remark,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') update_time,create_owner,update_owner
        from two_app_category where 1=1
        <if test="name != null and name != ''">
            and name = #{name}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by id
            </otherwise>
        </choose>
    </select>

</mapper>