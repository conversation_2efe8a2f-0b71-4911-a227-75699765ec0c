<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.mobile.ProductSysConfigMapper">

	<!--产品子系统配置-->
    <select id="list" resultType="com.wbgame.pojo.clean.response.ProductSysConfigResponseParam"
            parameterType="com.wbgame.pojo.clean.request.ProductSysConfigRequestParam">
        select
        *
        from product_sys_config
        <where>
            <if test="product_name != null and product_name != ''">
                and product_name like "%"#{product_name}"%"
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="platform != null and platform != 0">
                and platform = #{platform}
           </if>
            <if test="status != null">
                and status = #{status}
           </if>
            <if test="sys_web_url != null and sys_web_url != ''">
                and sys_web_url like CONCAT('%',#{sys_web_url},'%')
            </if>
            <if test="server_category != null">
                and server_category = #{server_category}
            </if>
        </where>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by update_time desc ,create_time desc
			</otherwise>
		</choose>
    </select>
    
    <delete id="delete" parameterType="java.lang.Integer">
        delete from product_sys_config
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <insert id="add" parameterType="com.wbgame.pojo.clean.request.ProductSysConfigSaveRequestParam"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into product_sys_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="product_name != null and product_name != ''">
                product_name,
            </if>
            <if test="icon_url != null and icon_url != ''">
                icon_url,
            </if>
            <if test="appid != null and appid != ''">
                appid,
            </if>
            <if test="pkg != null and pkg != ''">
                pkg,
            </if>
            <if test="domain_name != null and domain_name != ''">
                domain_name,
            </if>
            <if test="platform != null and platform != 0">
                platform,
            </if>
            <if test="sys_web_url != null and sys_web_url != ''">
                sys_web_url,
            </if>
            <if test="inter_dir != null and inter_dir != ''">
                inter_dir,
            </if>
            <if test="test_inter_dir != null and test_inter_dir != ''">
                test_inter_dir,
            </if>
            <if test="test_domain != null and test_domain != ''">
                test_domain,
            </if>
            create_user,
            update_user,
          	status,
            server_category
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
        	<if test="product_name != null and product_name != ''">
        		#{product_name,jdbcType=VARCHAR},
            </if>
            <if test="icon_url != null and icon_url != ''">
                #{icon_url,jdbcType=VARCHAR},
            </if>
            <if test="appid != null and appid != ''">
            	#{appid,jdbcType=VARCHAR},
            </if>
            <if test="pkg != null and pkg != ''">
            	#{pkg,jdbcType=VARCHAR},
            </if>
             <if test="domain_name != null and domain_name != ''">
            	#{domain_name,jdbcType=VARCHAR},
            </if>
            <if test="platform != null and platform != 0">
                #{platform,jdbcType=TINYINT},
            </if>
            <if test="sys_web_url != null and sys_web_url != ''">
                #{sys_web_url},
            </if>
            <if test="inter_dir != null and inter_dir != ''">
                #{inter_dir},
            </if>
            <if test="test_inter_dir != null and test_inter_dir != ''">
                #{test_inter_dir},
            </if>
            <if test="test_domain != null and test_domain != ''">
                #{test_domain},
            </if>
            #{create_user,jdbcType=VARCHAR},
            #{update_user,jdbcType=VARCHAR},
          	#{status,jdbcType=TINYINT},
          	#{server_category,jdbcType=TINYINT}
        </trim>
    </insert>

    <update id="update" parameterType="com.wbgame.pojo.clean.request.ProductSysConfigSaveRequestParam">
        update product_sys_config
        <set>
                product_name = #{product_name,jdbcType=VARCHAR},
                icon_url = #{icon_url,jdbcType=VARCHAR},
                pkg = #{pkg,jdbcType=VARCHAR},
                 appid = #{appid,jdbcType=VARCHAR},
                domain_name = #{domain_name,jdbcType=VARCHAR},
                status = #{status,jdbcType=TINYINT},
                platform = #{platform,jdbcType=TINYINT},
                <if test="sys_web_url != null and sys_web_url != ''">
                    sys_web_url = #{sys_web_url},
                </if>
                inter_dir = #{inter_dir},
                test_inter_dir = #{test_inter_dir},
                test_domain = #{test_domain},
                server_category = #{server_category,jdbcType=TINYINT},

                update_user = #{update_user,jdbcType=VARCHAR},
                update_time = CURRENT_TIMESTAMP
        </set>
        where id = #{id}
    </update>
    
</mapper>