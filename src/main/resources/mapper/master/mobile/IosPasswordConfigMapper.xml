<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.mobile.IosPasswordConfigMapper" >
  <resultMap id="resultDataMap" type="com.wbgame.pojo.mobile.IosPasswordConfig" >
    <result column="appid" property="appid" jdbcType="INTEGER" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="create_owner" property="createOwner" jdbcType="VARCHAR" />
    <result column="update_owner" property="updateOwner" jdbcType="VARCHAR" />
    <result column="appkey" property="appkey" jdbcType="VARCHAR" />
    <result column="secret_key" property="secretKey" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="resultDataMapVO" type="com.wbgame.pojo.mobile.IosPasswordConfigVO" >
    <result column="appid" property="appid" jdbcType="INTEGER" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="create_owner" property="createOwner" jdbcType="VARCHAR" />
    <result column="update_owner" property="updateOwner" jdbcType="VARCHAR" />
    <result column="appkey" property="appkey" jdbcType="VARCHAR" />
    <result column="secret_key" property="secretKey" jdbcType="VARCHAR" />
    <result column="app_name" property="appName" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="com.wbgame.pojo.mobile.IosPasswordConfig" >
    insert into ios_password_config (appid, password, create_time, 
      update_time, create_owner, update_owner, 
      appkey, secret_key)
    values (#{appid,jdbcType=INTEGER}, #{password,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createOwner,jdbcType=VARCHAR}, #{updateOwner,jdbcType=VARCHAR}, 
      #{appkey,jdbcType=VARCHAR}, #{secretKey,jdbcType=VARCHAR})
  </insert>

  <insert id="insertSelective" parameterType="com.wbgame.pojo.DnwxInterfaceConfig">
    insert into ios_password_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appid != null">
        appid,
      </if>
      <if test="password != null and password != ''">
        password,
      </if>
      <if test="createOwner != null and createOwner != ''">
        create_owner,
      </if>
      <if test="updateOwner != null and updateOwner != ''">
        update_owner,
      </if>
      <if test="appkey != null and appkey != ''">
        appkey,
      </if>
      <if test="secretKey != null and secretKey != ''">
        secret_key,
      </if>
        create_time
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appid != null">
        #{appid},
      </if>
      <if test="password != null and password != '' ">
        #{password},
      </if>
      <if test="createOwner != null and createOwner != ''">
        #{createOwner},
      </if>
      <if test="updateOwner != null and updateOwner != ''">
        #{updateOwner},
      </if>
      <if test="appkey != null and appkey != ''">
        #{appkey},
      </if>
      <if test="secretKey != null and secretKey != ''">
        #{secretKey},
      </if>
        now()
    </trim>
  </insert>
  <select id="selectByCondition" resultMap="resultDataMapVO"
          parameterType="com.wbgame.pojo.mobile.IosPasswordConfigQuery">
      select i.appid, i.password, i.create_time, i.update_time, i.create_owner, i.update_owner, i.appkey,
        i.secret_key, a.app_name
      from ios_password_config i, app_info a

      <where>

        i.appid = a.id

        <if test="appid != null and appid != '' ">
          and appid in (${appid})
        </if>

        <if test="password != null and password != ''">
          and password like #{password} "%"
        </if>

        <if test="appkey != null and appkey != ''">
          and appkey like #{appkey} "%"
        </if>
        <if test="secretKey != null and secretKey != ''">
          and secret_key like #{secretKey} "%"
        </if>

      </where>

        order by create_time
  </select>

  <update id="batchUpdate" parameterType="com.wbgame.pojo.mobile.IosPasswordConfig">

    <foreach collection="list" item="config" separator=";" >

        update ios_password_config
      <set>

        <if test="config.password != null and config.password != ''">
          password = #{config.password},
        </if>
        <if test="config.createOwner != null and config.createOwner != ''">
          create_owner = #{config.createOwner},
        </if>
        <if test="config.updateOwner != null and config.updateOwner != ''">
          update_owner = #{config.updateOwner},
        </if>
        <if test="config.appkey != null and config.appkey != ''">
          appkey = #{config.appkey},
        </if>
        <if test="config.secretKey != null and config.secretKey != ''">
          secret_key = #{config.secretKey},
        </if>
        update_time = now()

      </set>
        where appid = #{config.appid}
    </foreach>

  </update>

  <delete id="batchDelete" parameterType="java.lang.Integer">

        delete from ios_password_config where appid in
        <foreach collection="list" item="appid" open="(" separator="," close=")">

            #{appid}
        </foreach>
  </delete>


  <select id="countAppByById" parameterType="java.lang.Integer" resultType="java.lang.Integer">

    select count(*) from app_info where id in
    <foreach collection="list" item="appid" open="(" separator="," close=")">
      #{appid}
    </foreach>
  </select>
</mapper>