<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.mobile.HwApiMapper">

    <select id="getHwAppInfoList" parameterType="com.wbgame.pojo.mobile.hw.HwAppInfo"
            resultType="com.wbgame.pojo.mobile.hw.HwAppInfo">
        select * from hw_app_info  where  1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="taccount != null and taccount != ''">
            and taccount = #{taccount}
        </if>
        <if test="state != null and state != ''">
            and state = #{state}
        </if>
        <if test="tappid != null and tappid != ''">
            and tappid = #{tappid}
        </if>
        <if test="tappname != null and tappname != ''">
            and tappname like concat('%',#{tappname},'%')
        </if>

    </select>
    
    <insert id="saveHwAppBaseInfo" parameterType="com.wbgame.pojo.mobile.hw.HwAppInfo">
        insert into hw_app_info (appid,tappid,tappname,taccount,taccountName,packageName,createUser,createTime)
        value
        (#{appid},#{tappid},#{tappname},#{taccount},#{taccountName},#{packageName},#{createUser},now())
    </insert>

    <update id="updateHwAppBaseInfo" parameterType="com.wbgame.pojo.mobile.hw.HwAppInfo">
        update hw_app_info set appid = #{appid},tappid = #{tappid},taccount=#{taccount},taccountName=#{taccountName},tappname = #{tappname},
        packageName = #{packageName},modifyUser = #{modifyUser},modifyTime = now()
        where id = #{id}
    </update>

    <delete id="deleteHwAppInfo" parameterType="com.wbgame.pojo.mobile.hw.HwAppInfo">
        delete from hw_app_info where  id =#{id}
    </delete>

    <update id="updateHwAppDetailInfo" parameterType="com.wbgame.pojo.mobile.hw.HwAppInfo">
        update hw_app_info set state =#{state} ,`languages` = #{languages} ,auditInfo =#{auditInfo} ,appInfo =#{appInfo}
        where tappid =#{tappid}
    </update>

    <update id="updateHwAppTimeInfo" parameterType="com.wbgame.pojo.mobile.hw.HwAppInfo">
        update hw_app_info set modifyUser = #{modifyUser},modifyTime = now()
        where tappid = #{tappid}
    </update>

    <select id="getHwAppInfoListByIds" parameterType="java.lang.String" resultType="com.wbgame.pojo.mobile.hw.HwAppInfo">
        select * from hw_app_info where  id in (${ids})
    </select>
</mapper>