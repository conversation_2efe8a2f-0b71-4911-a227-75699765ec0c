<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.mobile.ApiPacketParaConfigMapper">

	<!--分包参数配置-->
    <select id="list" resultType="com.wbgame.pojo.mobile.response.ApiPacketParaConfigResponseParam"
            parameterType="com.wbgame.pojo.mobile.request.ApiPacketParaConfigParam">
        select
            id,
            dn_account,
            hw_report_api_client_id,
            hw_report_api_client_secret,
            cid,
            cname,
            channel,
            token AS platform_token,
            create_time,
            create_user,
            update_time,
            update_user,
            `status`
        from api_packet_platform_account
        <where>
            <if test="dn_account != null and dn_account != ''">
                and dn_account like CONCAT('%',#{dn_account},'%')
            </if>
            <if test="cid != null and cid != ''">
                and cid in (${cid})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="status != null">
                and status = #{status}
           </if>
        </where>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by update_time desc ,create_time desc
			</otherwise>
		</choose>
    </select>
    
    <delete id="delete" parameterType="java.lang.Integer">
        delete from api_packet_platform_account
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <insert id="add" parameterType="com.wbgame.pojo.mobile.request.ApiPacketParaConfigParam"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into api_packet_platform_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dn_account != null and dn_account != ''">
                dn_account,
            </if>
            <if test="hw_report_api_client_id != null and hw_report_api_client_id != ''">
                hw_report_api_client_id,
            </if>
            <if test="hw_report_api_client_secret != null and hw_report_api_client_secret != ''">
                hw_report_api_client_secret,
            </if>
            <if test="channel != null and channel != ''">
                channel,
            </if>
            <if test="cid != null and cid != ''">
                cid,
            </if>
            <if test="cname != null and cname != ''">
                cname,
            </if>
             create_user,
             update_user,
          	<if test="status != null">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
        	<if test="dn_account != null and dn_account != ''">
        		#{dn_account,jdbcType=VARCHAR},
            </if>
            <if test="hw_report_api_client_id != null and hw_report_api_client_id != ''">
                #{hw_report_api_client_id,jdbcType=VARCHAR},
            </if>
            <if test="hw_report_api_client_secret != null and hw_report_api_client_secret != ''">
            	#{hw_report_api_client_secret,jdbcType=VARCHAR},
            </if>
            <if test="channel != null and channel != ''">
            	#{channel,jdbcType=VARCHAR},
            </if>
             <if test="cid != null and cid != ''">
            	#{cid,jdbcType=TINYINT},
            </if>
            <if test="cname != null and cname != ''">
            	#{cname,jdbcType=VARCHAR},
            </if>
             #{create_user,jdbcType=VARCHAR},
             #{update_user,jdbcType=VARCHAR},
          	<if test="status != null">
          		#{status,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.wbgame.pojo.mobile.request.ApiPacketParaConfigParam">
        update api_packet_platform_account
        <set>
                hw_report_api_client_id = #{hw_report_api_client_id,jdbcType=VARCHAR},
                hw_report_api_client_secret = #{hw_report_api_client_secret,jdbcType=VARCHAR},
                cid = #{cid,jdbcType=INTEGER},
                cname = #{cname,jdbcType=VARCHAR},
                status = #{status,jdbcType=TINYINT},
                update_user = #{update_user,jdbcType=VARCHAR},
                update_time = CURRENT_TIMESTAMP
        </set>
        	where id = #{id}
    </update>
     <insert id="addHuaWeiPacketAuditRecord" parameterType="com.wbgame.pojo.mobile.request.HuaWeiPacketAuditRecordParam"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into hw_packet_audit_ecord(hw_report_api_client_id,hwappid,token,transfer_time) values(#{hw_report_api_client_id},#{hwappid},#{token},#{transfer_time})
    </insert>
    
        <select id="queryHuaWeiPacketAuditRecordNeeded" resultType="com.wbgame.pojo.mobile.request.HuaWeiPacketAuditRecordParam">
        select
        *
        from hw_packet_audit_ecord where audit_status=0
    </select>
        <update id="updateHuaWeiPacketAuditRecord" parameterType="com.wbgame.pojo.mobile.request.HuaWeiPacketAuditRecordParam">
        update hw_packet_audit_ecord
        <set>
                audit_time = #{audit_time,jdbcType=VARCHAR},
                audit_status = #{audit_status,jdbcType=TINYINT},
                audit_msg = #{audit_msg,jdbcType=VARCHAR}
        </set>
        	where id = #{id}
    </update>


    <update id="updateOppoAccountToken" parameterType="java.util.Map">
        update api_packet_platform_account
        set
            update_user = #{update_user},
            update_time = CURRENT_TIMESTAMP,
            token = #{token}
        where dn_account = #{account,jdbcType=VARCHAR} and channel = #{platform,jdbcType=VARCHAR}
    </update>

</mapper>