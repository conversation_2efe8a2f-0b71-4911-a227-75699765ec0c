<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.mobile.CertificateMapper">


    <insert id="insert">
        INSERT INTO certificate_repository (url, dns_url, dns_type, ssl_des, email, test_dns, ssl_exp, dow_ssl, bt_send, bt_file, ap_time, url_st, create_user, create_time,name,msg)
        VALUES (#{url}, #{dns_url}, #{dns_type}, #{ssl_des}, #{email}, #{test_dns}, #{ssl_exp}, #{dow_ssl}, #{bt_send}, #{bt_file}, #{ap_time}, #{url_st}, #{create_user}, NOW(),#{name},#{msg})
    </insert>

    <update id="update">
        UPDATE certificate_repository
        SET url = #{url}, dns_url = #{dns_url}, dns_type = #{dns_type}, ssl_des = #{ssl_des}, email = #{email}, test_dns = #{test_dns}, ssl_exp = #{ssl_exp}, dow_ssl = #{dow_ssl}, bt_send = #{bt_send}, bt_file = #{bt_file}, ap_time = #{ap_time}, url_st = #{url_st}, update_user = #{update_user}, update_time = NOW(),
            name = #{name},msg = #{msg}
        WHERE id = #{id}
    </update>

    <delete id="delete">
        DELETE FROM certificate_repository WHERE id = #{id}
    </delete>

    <select id="queryList" resultType="com.wbgame.pojo.mobile.CertificateRepository">
        SELECT * FROM certificate_repository
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="url != null and url != ''">
                 AND url LIKE CONCAT('%', #{url}, '%')
            </if>
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="dns_url != null and dns_url != ''">
                 AND dns_url LIKE CONCAT('%', #{dns_url}, '%')
            </if>
            <if test="dns_type != null and dns_type != ''">
                 AND dns_type = #{dns_type}
            </if>
            <if test="ssl_des != null and ssl_des != ''">
                AND ssl_des = #{ssl_des}
            </if>
            <if test="email != null and email != ''">
                AND email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="bt_send != null and bt_send != ''">
                AND bt_send LIKE CONCAT('%', #{bt_send}, '%')
            </if>
            <if test="test_dns != null">
                AND test_dns = #{test_dns}
            </if>
            <if test="url_st != null and url_st != ''">
                AND url_st = #{url_st}
            </if>
        </where>
        <choose>
            <when test="order_str != null and order_str != ''">
                ORDER BY ${order_str}
            </when>
            <otherwise>
                ORDER BY create_time DESC
            </otherwise>
        </choose>
    </select>

</mapper>