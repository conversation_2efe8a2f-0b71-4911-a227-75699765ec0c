<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.AccountSubscribeConfigMapper">

    <insert id="insertConfig">
        INSERT INTO account_subscribe_config (app_id,account_id, subscribe_page_id, enabled,create_user
                                             ,create_time,update_user,update_time)
        value (#{entity.appId}, #{entity.accountId}, #{entity.subscribePageId}, #{entity.enabled},#{entity.createUser},
               #{entity.createTime},#{entity.updateUser},#{entity.updateTime}   )
    </insert>
    <update id="updateById">
        UPDATE account_subscribe_config
        set enabled = #{entity.enabled},
            app_id = #{entity.appId},
            account_id = #{entity.accountId},
            subscribe_page_id = #{entity.subscribePageId},
            update_user = #{entity.updateUser},
            update_time = #{entity.updateTime}
        where id = #{entity.id}
    </update>
    <update id="batchUpdate">
        update account_subscribe_config
        <set>
            <if test="subscribePageId != null and subscribePageId !='' ">
                subscribe_page_id = #{subscribePageId},
            </if>
            <if test="enabled != null ">
                enabled = #{enabled},
            </if>
            <if test="updateUser != null and updateUser !='' ">
                update_user = #{updateUser},
            </if>
            <if test="updateTime != null ">
                update_time = #{updateTime},
            </if>
        </set>
        where id IN
        <if test="ids != null and ids.size > 0">
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>
    <delete id="deleteByIdList">
        DELETE FROM account_subscribe_config
        WHERE id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>
    <select id="selectByCondition" resultType="com.wbgame.pojo.redpack.AccountSubscribePageConfig">
        select t1.id as id,
        t1.app_id as appId,
        t1.account_id as accountId,
        t1.subscribe_page_id as subscribePageId,
        t1.enabled as enabled,
        t1.create_user as createUser,
        t1.create_time as createTime,
        t1.update_user as updateUser,
        t1.update_time as updateTime
        from account_subscribe_config t1
        <where>
            1=1
            <if test="query.appidList != null and query.appidList.size > 0">
                and t1.app_id in
                <foreach collection="query.appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>

            <if test="query.accounts != null and query.accounts.size > 0">
                and t1.account_id in
                <foreach collection="query.accounts" item="account" open="(" separator="," close=")">
                    #{account}
                </foreach>
            </if>

            <if test="query.subscribePageId != null and query.subscribePageId !='' ">
                and t1.subscribe_page_id = #{query.subscribePageId}
            </if>

            <if test="query.ids != null and query.ids.size > 0 ">
                and t1.id in
                <foreach collection="query.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="count" resultType="java.lang.Integer">
        select count(*)
        from account_subscribe_config t1
        where t1.app_id = #{entity.appId}
        and t1.account_id = #{entity.accountId}
        <if test="entity.id != null">
            and t1.id != #{entity.id}
        </if>
    </select>
    <select id="selectByIdList" resultType="com.wbgame.pojo.redpack.AccountSubscribePageConfig">
        select t1.id as id,
               t1.app_id as appId,
               t1.account_id as accountId,
               t1.subscribe_page_id as subscribePageId,
               t1.enabled as enabled,
               t1.create_user as createUser,
               t1.create_time as createTime,
               t1.update_user as updateUser,
               t1.update_time as updateTime
        from account_subscribe_config t1
        where t1.id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>
    <select id="selectSubscribePages" resultType="com.wbgame.pojo.redpack.SubscribeConfig">
        select t1.subscription_id as subscriptionId,t1.subscription_name as subscriptionName,t1.enabled as enabled,t1.remark as remark,t1.author as author
        from middle_subscribe_page t1
        <where >
            <if test="appidList != null and appidList.size > 0">
                and  t1.app_id in
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectLink" resultType="java.lang.String">
        select url
        from dnwx_adt.common_material_appid_url_config
        where appid = #{appid}
    </select>
</mapper>