<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.GameRemoteResourceConfigMapper">

    <select id="selectGameRemoteResourceList" resultType="com.wbgame.pojo.mobile.GameRemoteResourceVo" parameterType="com.wbgame.pojo.mobile.GameRemoteResourceVo">
        select  * from mobile_gameremoteres_config where 1=1
        <if test="appid != null and appid != ''">
            and appid = #{appid,jdbcType=VARCHAR}
        </if>
        order by createtime desc
    </select>

    <insert id="insertGameRemoteResource" parameterType="com.wbgame.pojo.mobile.GameRemoteResourceVo">
        insert  into mobile_gameremoteres_config (appid,md5,downloadUrl,createtime,cuser)
        values (#{appid},#{md5},#{downloadUrl},NOW(),#{cuser});
    </insert>

    <update id="updateGameRemoteResource" parameterType="com.wbgame.pojo.mobile.GameRemoteResourceVo">
        update  mobile_gameremoteres_config set
        appid =#{appid}, md5 =#{md5},downloadUrl =#{downloadUrl},euser =#{euser}, endtime =NOW()
        where id =#{id}
    </update>

    <delete id="delGameRemoteResource" parameterType="com.wbgame.pojo.mobile.GameRemoteResourceVo">
        delete  from mobile_gameremoteres_config where  id =#{id}
    </delete>
</mapper>