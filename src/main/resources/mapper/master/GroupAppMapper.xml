<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.GroupAppMapper" >

	<delete id="deleteAppByGroup">
		DELETE FROM dn_group_app WHERE groupId = #{groupId}
	</delete>

	<insert id="batchAddAppByGroup">
		INSERT INTO dn_group_app(groupId, appId) VALUES
		<foreach collection="appIdList" item="appId" separator=",">
			(#{groupId}, #{appId})
		</foreach>
	</insert>


	<select id="getAppByGroupList" resultType="com.wbgame.pojo.GroupAppVo">
		SELECT xx.id,xx.id groupId, xx.groupName,xx.enabled, GROUP_CONCAT(yy.appId ORDER BY yy.appId ASC) AS appIdStr,
			xx.createUser,xx.updateUser,
			CONCAT(xx.createTime,'') createTime,CONCAT(xx.updateTime,'') updateTime
		FROM dn_group xx
		LEFT JOIN dn_group_app yy ON xx.id=yy.groupId

		WHERE 1=1
		<if test="groupName != null and groupName != ''">
			and xx.groupName = #{groupName}
		</if>
		<if test="enabled != null and enabled != ''">
			and xx.enabled = #{enabled}
		</if>
		GROUP BY xx.id
		ORDER BY xx.id DESC
	</select>
	<select id="getGroupCountByName" resultType="java.lang.Integer">
		SELECT COUNT(id) FROM dn_group WHERE groupName = #{groupName}
	</select>

	<insert id="addGroup" useGeneratedKeys="true" keyProperty="id" parameterType="com.wbgame.pojo.GroupAppVo">
		INSERT INTO dn_group(groupName, enabled, createUser, createTime)
		VALUES (#{groupName}, #{enabled}, #{createUser}, now())
	</insert>

	<update id="updateGroup" parameterType="com.wbgame.pojo.GroupAppVo">
		UPDATE dn_group SET groupName=#{groupName}, enabled=#{enabled}, updateUser = #{updateUser}, updateTime=now()
		WHERE id = #{groupId}
	</update>

</mapper>