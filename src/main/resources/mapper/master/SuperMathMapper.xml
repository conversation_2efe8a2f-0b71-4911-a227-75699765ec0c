<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.SuperMathMapper">

	<select id="selectSuperMathVo" parameterType="java.lang.String"
		resultType="com.wbgame.pojo.SuperMathVo">
		select <include refid="userinfo"/> 
		from super_user_info
		where project_id = #{project_id} and user_id = #{user_id} limit 1
	</select>
	
	<select id="selectSuperMathIdList" parameterType="java.lang.String"
		resultType="java.lang.String">
		select user_id 
		from super_user_info
		where project_id = #{project_id}
	</select>
	<select id="selectSuperMathVoByAttr" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.SuperMathVo">
		
	</select>

	<insert id="insertSuperMathVo" parameterType="com.wbgame.pojo.SuperMathVo">
		insert into super_user_info (<include refid="userinfo"/> ) 
		values (
			#{user_id},
			#{user_name},
			#{user_icon},
			#{user_score},
			#{user_rank},
			#{project_id},
			now(),
			now())
		ON DUPLICATE KEY UPDATE 
		user_name=VALUES(user_name),
		user_icon=VALUES(user_icon),
		user_score=VALUES(user_score),
		user_rank=VALUES(user_rank)
	</insert>
	<insert id="insertSuperMathVoByAttr" parameterType="java.util.Map">
		insert into super_user_info (<include refid="userinfo"/> ) 
		values 
		<foreach collection="list" item="li" separator=",">
			(#{li.user_id},
			#{li.user_name},
			#{li.user_icon},
			#{li.user_score},
			#{li.user_rank},
			#{li.project_id},
			now(),
			now())
		</foreach>	
		ON DUPLICATE KEY UPDATE 
		user_name=VALUES(user_name),
		user_icon=VALUES(user_icon),
		user_score=VALUES(user_score),
		user_rank=VALUES(user_rank),
		last_time=VALUES(last_time)
	</insert>
	
	<update id="updateSuperMathVoByAttr" parameterType="java.util.Map">
		update super_user_info set 
			user_score = #{user_score},
			user_rank = #{user_rank} 
		where project_id = #{project_id}
	</update>
	
	<sql id="userinfo">
		user_id,
		user_name,
		user_icon,
		user_score,
		user_rank,
		project_id,
		create_time,
		last_time
	</sql>

</mapper>