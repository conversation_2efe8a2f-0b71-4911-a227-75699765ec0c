<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.ApkEventMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.mobile.ApkEvent">
    <!--@mbg.generated-->
    <!--@Table apk_event-->
    <id column="app_key" jdbcType="VARCHAR" property="appKey" />
    <id column="event" jdbcType="VARCHAR" property="event" />
    <result column="is_syn" jdbcType="INTEGER" property="isSyn" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    app_key, event, is_syn
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from apk_event
    where app_key = #{appKey,jdbcType=VARCHAR}
      and event = #{event,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--@mbg.generated-->
    delete from apk_event
    where app_key = #{appKey,jdbcType=VARCHAR}
      and event = #{event,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.wbgame.pojo.mobile.ApkEvent">
    <!--@mbg.generated-->
    insert into apk_event (app_key, event, is_syn
      )
    values (#{appKey,jdbcType=VARCHAR}, #{event,jdbcType=VARCHAR}, #{isSyn,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wbgame.pojo.mobile.ApkEvent">
    <!--@mbg.generated-->
    insert into apk_event
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appKey != null">
        app_key,
      </if>
      <if test="event != null">
        event,
      </if>
      <if test="isSyn != null">
        is_syn,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appKey != null">
        #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="event != null">
        #{event,jdbcType=VARCHAR},
      </if>
      <if test="isSyn != null">
        #{isSyn,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.mobile.ApkEvent">
    <!--@mbg.generated-->
    update apk_event
    <set>
      <if test="isSyn != null">
        is_syn = #{isSyn,jdbcType=INTEGER},
      </if>
    </set>
    where app_key = #{appKey,jdbcType=VARCHAR}
      and event = #{event,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.mobile.ApkEvent">
    <!--@mbg.generated-->
    update apk_event
    set is_syn = #{isSyn,jdbcType=INTEGER}
    where app_key = #{appKey,jdbcType=VARCHAR}
      and event = #{event,jdbcType=VARCHAR}
  </update>

    <select id="selectEventsByAppid" resultType="java.lang.String">
      select a.event from apk_event a left join app_info b on a.app_key = b.umeng_key where b.id = #{appid}
    </select>

  <insert id="batchInsertApkEvents">
    insert ignore into apk_event (app_key, event, is_syn) values
    <foreach collection="list" index="index" item="item" separator=",">
    (#{item.appKey,jdbcType=VARCHAR}, #{item.event,jdbcType=VARCHAR}, #{item.isSyn,jdbcType=INTEGER} )
    </foreach>

  </insert>

  <sql id="wheres">
    <if test="item.whereParams != null and item.whereParams.size() != 0">
      <choose>
        <when test="item.whereType == 'and'">
          <foreach collection="item.whereParams" item="params" open="and (" close=")" separator="and">
            <foreach collection="params" item="it" open="(" close=")" separator="or">
              <choose>
                <when test="it.type == 1">
                  ${it.column} in (${it.value})
                </when>
                <when test="it.type == 2">
                  ${it.column} not in (${it.value})
                </when>
                <when test="it.type == 3">
                  ${it.column} like concat('%',#{it.value},'%')
                </when>
                <when test="it.type == 4">
                  ${it.column} not like concat('%',#{it.value},'%')
                </when>
                <when test="it.type == 5">
                  ${it.column} is null
                </when>
                <when test="it.type == 6">
                  ${it.column} is not null
                </when>
              </choose>
            </foreach>
          </foreach>
        </when>

        <otherwise>
          <foreach collection="item.whereParams" item="params" open="and (" close=")" separator="or">
            <foreach collection="params" item="it" open="(" close=")" separator="and">
              <choose>
                <when test="it.type == 1">
                  ${it.column} in (${it.value})
                </when>
                <when test="it.type == 2">
                  ${it.column} not in (${it.value})
                </when>
                <when test="it.type == 3">
                  ${it.column} like concat('%',#{it.value},'%')
                </when>
                <when test="it.type == 4">
                  ${it.column} not like concat('%',#{it.value},'%')
                </when>
                <when test="it.type == 5">
                  ${it.column} is null
                </when>
                <when test="it.type == 6">
                  ${it.column} is not null
                </when>
              </choose>
            </foreach>
          </foreach>
        </otherwise>
      </choose>
    </if>
  </sql>

  <select id="selectEventAnalysis" resultType="java.util.Map">
    select
    <if test="group_str != null and group_str != ''">
      ${group_str},
    </if>
    <foreach collection="apkEvents" item="item" separator=",">
      <choose>
        <when test="item.type == 1">
          <choose>
            <when test="item.field == 'avg_num'">
              ROUND(sum(CASE WHEN event_name = #{item.event,jdbcType=VARCHAR}
              <include refid="wheres"></include>
              THEN time_num END)/sum(CASE WHEN event_name = #{item.event,jdbcType=VARCHAR} THEN person_num END),2)
              as ${item.event}__${item.field}__${item.mode}
            </when>
            <otherwise>
              sum(CASE WHEN event_name = #{item.event,jdbcType=VARCHAR}
              <include refid="wheres"></include>
              THEN ${item.field} END) as ${item.event}__${item.field}__${item.mode}
            </otherwise>
          </choose>
        </when>

        <otherwise>
         <choose>
           <when test="item.mode == 'count'">
             count(DISTINCT CASE WHEN event_name = #{item.event,jdbcType=VARCHAR}
             <include refid="wheres"></include>
             THEN ${item.field} END) as ${item.event}__${item.field}__${item.mode}
           </when>
           <when test="item.mode == 'sum'">
             ${item.mode}(CASE WHEN event_name = #{item.event,jdbcType=VARCHAR}
             <include refid="wheres"></include>
             THEN ${item.field}_sum END) as ${item.event}__${item.field}__${item.mode}
           </when>
           <otherwise>
             ${item.mode}(CASE WHEN event_name = #{item.event,jdbcType=VARCHAR}
             <include refid="wheres"></include>
             THEN ${item.field} END) as ${item.event}__${item.field}__${item.mode}
           </otherwise>
         </choose>
        </otherwise>
      </choose>
    </foreach>
    from umeng_user_event_${appid}
    where tdate between #{startTime} and #{endTime}
    and event_name in
    <foreach collection="apkEvents" index="index" item="item" separator="," open="(" close=")">
        #{item.event}
    </foreach>
    <if test="whereParams != null and whereParams.size() != 0">
      <choose>
        <when test="whereType == 'and'">
          <foreach collection="whereParams" item="params" open="and (" close=")" separator="and">
            <foreach collection="params" item="item" open="(" close=")" separator="or">
              <choose>
                <when test="item.type == 1">
                  ${item.column} in (${item.value})
                </when>
                <when test="item.type == 2">
                  ${item.column} not in (${item.value})
                </when>
                <when test="item.type == 3">
                  ${item.column} like concat('%',#{item.value},'%')
                </when>
                <when test="item.type == 4">
                  ${item.column} not like concat('%',#{item.value},'%')
                </when>
                <when test="item.type == 5">
                  ${item.column} is null
                </when>
                <when test="item.type == 6">
                  ${item.column} is not null
                </when>
              </choose>
            </foreach>
          </foreach>
        </when>

        <otherwise>
          <foreach collection="whereParams" item="params" open="and (" close=")" separator="or">
            <foreach collection="params" item="item" open="(" close=")" separator="and">
              <choose>
                <when test="item.type == 1">
                  ${item.column} in (${item.value})
                </when>
                <when test="item.type == 2">
                  ${item.column} not in (${item.value})
                </when>
                <when test="item.type == 3">
                  ${item.column} like concat('%',#{item.value},'%')
                </when>
                <when test="item.type == 4">
                  ${item.column} not like concat('%',#{item.value},'%')
                </when>
                <when test="item.type == 5">
                  ${item.column} is null
                </when>
                <when test="item.type == 6">
                  ${item.column} is not null
                </when>
              </choose>
            </foreach>
          </foreach>
        </otherwise>
      </choose>
    </if>
    GROUP BY ${group_str}
    order by ${order_str}

  </select>

  <select id="selectNotSynEvents" resultMap="BaseResultMap">
    select * from apk_event where is_syn = 0 and app_key = #{appKey,jdbcType=VARCHAR} and event in
    <foreach collection="apkEvents" open="(" close=")" separator="," item="item" >
      #{item.event}
    </foreach>
  </select>

  <insert id="batchInsertUmengUserEvents">
    insert into umeng_user_event_${appid} ( app_version,install_channel,device_brand,device_model,tdate,event_name,is_new_install,time_num,person_num,sdk_version,app_channel,pre_app_version,screen_height,screen_width,
    os_version,country,province,city,is_wifi,screen_height_sum,screen_width_sum )
      values
    <foreach collection="data" index="index" item="item" separator=",">
    (#{item.app_version},#{item.install_channel},#{item.device_brand},#{item.device_model},#{item.tdate},#{item.event_name},#{item.is_new_install},
      #{item.time_num},#{item.person_num},#{item.sdk_version},#{item.app_channel},#{item.pre_app_version},#{item.screen_height},#{item.screen_width},
      #{item.os_version},#{item.country},#{item.province},#{item.city},#{item.is_wifi},#{item.screenHeightSum},#{item.screenWidthSum})
    </foreach>
  </insert>

  <select id="selectByAll" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from apk_event
    <where>1=1
      <if test="appKey != null and appKey != ''">
        and app_key = #{appKey,jdbcType=VARCHAR}
      </if>
      <if test="event != null and event != ''">
        and event = #{event,jdbcType=VARCHAR}
      </if>
      <if test="isSyn != null">
        and is_syn = #{isSyn,jdbcType=INTEGER}
      </if>
    </where>
  </select>

  <select id="selectAppKeyIsSyn" resultType="java.lang.String">
    select app_key from apk_event where is_syn = 1
    <if test="appKey != null and appKey != ''">
      and app_key = #{appKey}
    </if>
    group by app_key
  </select>
  <update id="updateEventSynStatus">

    update apk_event set is_syn = 0 where app_key = #{appKey} and event in
    <foreach collection="list" item="item" separator="," open="(" close=")">
      #{item.event}
    </foreach>

  </update>

  <select id="hasEventTable" resultType="java.lang.Long">
    SELECT count(1) FROM information_schema.TABLES WHERE table_name = concat('umeng_user_event_',#{appid})
  </select>

  <select id="selectAppid" resultType="java.lang.String">
    select id from app_info where umeng_key = #{umeng_key}
  </select>

  <select id="createEventTable">
    CREATE TABLE umeng_user_event_${appid}  (
    `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `app_version` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'app版本号',
    `install_channel` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '安装渠道',
    `tdate` date NULL DEFAULT NULL COMMENT '日期',
    `event_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '事件',
    `is_new_install` int(11) NULL DEFAULT NULL COMMENT '是否新安装  0  1',
    `time_num` bigint(11) NULL DEFAULT NULL COMMENT '次数',
    `person_num` bigint(11) NULL DEFAULT NULL COMMENT '人数',
    `device_brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备品牌',
    `device_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备型号',
    `app_channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '当前渠道',
    `sdk_version` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'sdk版本',
    `pre_app_version` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上个版本号',
    `screen_height` int(10) NULL DEFAULT NULL COMMENT '分辨率高度',
    `screen_width` int(10) NULL DEFAULT NULL COMMENT '分辨率宽度',
    `os_version` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作系统版本',
    `country` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '国家',
    `province` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省份',
    `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市',
    `is_wifi` int(2) NULL DEFAULT NULL COMMENT '是否wifi  0  1',
    `screen_height_sum` bigint(11) NULL DEFAULT NULL COMMENT '分辨率高度累加值',
    `screen_width_sum` bigint(11) NULL DEFAULT NULL COMMENT '分辨率宽度累加值',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `app_version_index`(`app_version`) USING BTREE,
    INDEX `install_channel_index`(`install_channel`) USING BTREE,
    INDEX `event_name_index`(`event_name`) USING BTREE,
    INDEX `device_brand_index`(`device_brand`) USING BTREE,
    INDEX `device_model_index`(`device_model`) USING BTREE,
    INDEX `app_channel_index`(`app_channel`) USING BTREE,
    INDEX `sdk_version_index`(`sdk_version`) USING BTREE,
    INDEX `pre_app_version_index`(`pre_app_version`) USING BTREE,
    INDEX `screen_height_index`(`screen_height`) USING BTREE,
    INDEX `screen_width_index`(`screen_width`) USING BTREE,
    INDEX `os_version_index`(`os_version`) USING BTREE,
    INDEX `country_index`(`country`) USING BTREE,
    INDEX `province_index`(`province`) USING BTREE,
    INDEX `city_index`(`city`) USING BTREE,
    INDEX `tdate_index`(`tdate`, `event_name`) USING BTREE
    ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
  </select>

  <select id="selectAppInfoByAppKey" resultType="com.wbgame.pojo.AppInfoVo">
     select id app_id,app_category from
     app_info where umeng_key = #{umeng_key}
  </select>
  <delete id="deleteAllExpireData">
    delete from umeng_user_event_${appid} where tdate = #{expire}
  </delete>

    <insert id="batchInsertEventDataResource">
      insert ignore into event_data_resource (brand,model,sdk,os,appid)
      values
      <foreach collection="data" index="index" item="item" separator=",">
        (#{item.brand},#{item.model},#{item.sdk},#{item.os},#{item.appid})
      </foreach>
    </insert>

  <select id="selectAppKeyByAppid" resultType="String">
    select umeng_key from app_info where id = #{appid}
  </select>

    <select id="selectAllAppInfo" resultType="java.util.Map">
      select * from app_info
    </select>

</mapper>