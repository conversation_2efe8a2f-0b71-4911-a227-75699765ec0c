<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.DnwxInterfaceCountMapper">


    <select id="queryList" resultType="com.wbgame.pojo.mobile.ApiAccessStatsVo">
        SELECT
        <if test="group != null and group != ''">
            ${group},
        </if>
        sum(num) num
        FROM
        (SELECT LEFT(prjid,5) appid,num, CASE WHEN LENGTH(prjid) > 5 THEN prjid ELSE '' END AS prjid,uri,`date` FROM
        (SELECT tdate `date`,num,SUBSTRING_INDEX(interface, '?', 1) uri, CASE WHEN INSTR(interface, 'pid=') > 0 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(interface, 'pid=', -1), '&amp;', 1) ELSE '' END AS prjid FROM yyhz_0308.dnwx_interface_count where interface LIKE '%check:%'
        <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
            and `tdate` between #{start_date} and #{end_date}
        </if>) t1) t2
        <where>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="prjid != null and prjid != ''">
                and prjid like concat('%',#{prjid},'%')
            </if>
        </where>
        <choose>
            <when test="group != null and group != ''">
                GROUP BY ${group}
            </when>
            <otherwise>having sum(num) is not null</otherwise>
        </choose>
        <choose>
            <when test="order_str != null and order_str != ''">
                ORDER BY ${order_str}
            </when>
            <otherwise>
                ORDER BY date desc
            </otherwise>
        </choose>
    </select>



</mapper>