<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.AppInfoByCategoryMapper">

    <select id="getAppidList" parameterType="java.util.List" resultType="java.lang.String">
        <if test="app_category != null and app_category.size() > 0 ">
            select id
            from app_info
            where app_category in
            <foreach collection="app_category" item="category" open="(" separator="," close=")">
                #{category}
            </foreach>
        </if>
    </select>

    <select id="getAppidsByCategory" resultType="java.lang.String">
        select id
        from app_info
        <where>
            <if test="appid != null and appid != ''">
                and id in (${appid})
            </if>
            <if test="app_category != null and app_category != ''">
                and app_category in (${app_category})
            </if>
            <if test="two_app_category != null and two_app_category != ''">
                and two_app_category in (${two_app_category})
            </if>
        </where>
    </select>
</mapper>