<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.yyhzCashReport.CashReportMapper">

    <select id="getReportCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM yyhz_0308.dn_cha_cash_total WHERE `date` = #{date} AND agent = #{agent} AND member_id = #{member_id}
    </select>

    <delete id="delReport">
        DELETE FROM yyhz_0308.dn_cha_cash_total WHERE `date` = #{date} AND agent = #{agent} AND member_id = #{member_id}
    </delete>


    <insert id="batchAddReport" parameterType="com.wbgame.pojo.adv2.reportEntity.ReportChina">
        INSERT INTO yyhz_0308.dn_cha_cash_total
        (
         dnappid, app_id, agent, placement_type, placement_id,open_type, country, `date`, pv, click, revenue, dollar_revenue, buy_revenue,
         member_id, ad_sid, cha_type_name, cha_media, cha_sub_launch, cha_id, cha_type, request_count, return_count,`source`, `out`
         )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.dnappid}, #{item.app_id}, #{item.agent}, #{item.placement_type}, #{item.placement_id}, #{item.open_type}, #{item.country},
            #{item.date},#{item.pv}, #{item.click}, #{item.revenue}, #{item.dollar_revenue}, #{item.buy_revenue}, #{item.member_id},
            #{item.ad_sid}, #{item.cha_type_name}, #{item.cha_media}, #{item.cha_sub_launch}, #{item.cha_id}, #{item.cha_type}, #{item.request_count},
            #{item.return_count},#{item.source},#{item.out}
            )
        </foreach>
    </insert>

    <delete id="delHourReport">
        DELETE FROM yyhz_0308.dn_cha_cash_total_hourly WHERE `date` = #{date} AND agent = #{agent} AND member_id = #{member_id}
    </delete>

    <insert id="batchAddHourReport" parameterType="com.wbgame.pojo.adv2.reportEntity.ReportChina">
        INSERT INTO yyhz_0308.dn_cha_cash_total_hourly
        (
        dnappid, app_id, agent, placement_type, placement_id,open_type, country, `date`, hours, pv, click, revenue, dollar_revenue, buy_revenue,
        member_id, ad_sid, cha_type_name, cha_media, cha_sub_launch, cha_id, cha_type, request_count, return_count,`source`
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.dnappid}, #{item.app_id}, #{item.agent}, #{item.placement_type}, #{item.placement_id}, #{item.open_type}, #{item.country},
            #{item.date},#{item.hours},#{item.pv}, #{item.click}, #{item.revenue}, #{item.dollar_revenue}, #{item.buy_revenue}, #{item.member_id},
            #{item.ad_sid}, #{item.cha_type_name}, #{item.cha_media}, #{item.cha_sub_launch}, #{item.cha_id}, #{item.cha_type}, #{item.request_count},
            #{item.return_count},#{item.source}
            )
        </foreach>
    </insert>

    <select id="getAdConfig" resultType="com.wbgame.pojo.adv2.reportEntity.AdConfigEntity">
        SELECT appid AS dn_appid, TRIM(cha_id) AS channel_tag, TRIM(sdk_code) AS `code`, TRIM(sdk_adtype) AS `type`,TRIM(open_type) AS `open_type`,
               TRIM(adsid) AS ad_sid, TRIM(sdk_appid) AS appid, `out` FROM dnwx_cfg.dn_extend_adsid_manage
    </select>

    <select id="getChannel" resultType="com.wbgame.pojo.adv2.reportEntity.Channel">
        SELECT dci.*, dct.type_name FROM yyhz_0308.dn_channel_info dci
                                             LEFT JOIN yyhz_0308.dn_channel_type dct ON dci.cha_type = dct.type_id
    </select>

    <select id="getValidCashVivoAccountList" resultType="com.wbgame.pojo.adv2.businessAccountEntity.CashVivoAccount">
        SELECT v.account as account,v.accountName as accountName,a.tttoken AS cookie,v.secretKey as secretKey FROM yyhz_0308.dn_vivo_account v
                    LEFT join yyhz_0308.app_channel_config a on v.account = a.account and a.channel = 'vivo'
        where v.enabled = 1
    </select>

    <select id="getCashOhayooAccountList" resultType="com.wbgame.pojo.adv2.businessAccountEntity.CashOhayooAccount">
        select * from dnwx_adt.dn_cash_account where enabled = 1 and platform = 'ohayoo'
    </select>

    <insert id="batchInsertXyxAdvData">
        insert into yyhz_0308.dn_cha_cash_total(member_id,app_id,placement_id,date,return_count,pv,click,revenue,dnappid,agent,ad_sid,cha_type
        ,cha_media,cha_sub_launch,cha_id,cha_type_name,country,open_type,placement_type, `out`) values
        <foreach collection="list" item="it" separator=",">
            (#{it.sdk_appid},#{it.sdk_appid},#{it.ad_unit_id},#{it.date},#{it.req_succ_count},#{it.exposure_count},#{it.click_count},#{it.income},#{it.appid}
            ,#{it.agent},#{it.adsid},#{it.cha_type},#{it.cha_media},#{it.cha_sub_launch},#{it.cha_id},#{it.type_name},'CN',#{it.open_type},#{it.sdk_adtype},#{it.out})
        </foreach>
    </insert>

    <select id="selectXyxAppkey" resultType="java.util.Map">
        select appid,sdk_appid,sdk_appkey from dnwx_cfg.dn_extend_adsid_manage
        where agent = 'weixin' and LENGTH(trim(sdk_appid)) > 0 and LENGTH(trim(sdk_appkey)) > 0
        GROUP BY appid
        order by createtime desc
    </select>

    <select id="selectXyxAdsidReport" resultType="com.alibaba.fastjson.JSONObject">
        select adsid,agent,sdk_code,sdk_appid,sdk_appkey,sdk_adtype,adpos_type,sdk_adtype,open_type,a.cha_id,b.cha_media,b.cha_sub_launch,cha_type,type_name,appid,`out` from dnwx_cfg.dn_extend_adsid_manage a
                                                                                                                                                                            left join yyhz_0308.dn_channel_info b on a.cha_id = b.cha_id
                                                                                                                                                                            left join yyhz_0308.dn_channel_type c on b.cha_type = c.type_id
        where agent = 'weixin'
    </select>


    <select id="selectNewCashReport" resultType="com.wbgame.pojo.adv2.reportEntity.ReportChina">
        select

        dnappid, app_id, agent, placement_type, placement_id,open_type, country, `date`, pv, click, revenue, dollar_revenue, buy_revenue,
        member_id, ad_sid, cha_type_name, cha_media, cha_sub_launch, cha_id, cha_type, request_count, return_count,`source`
        from dnwx_cfg.dn_cha_cash_total
        where
            `date` = #{selectDate}
    </select>

    <select id="selectOldCashReport" resultType="com.wbgame.pojo.adv2.reportEntity.ReportChina">
        select

            dnappid, app_id, agent, placement_type, placement_id,open_type, country, `date`, pv, click, revenue, dollar_revenue, buy_revenue,
            member_id, ad_sid, cha_type_name, cha_media, cha_sub_launch, cha_id, cha_type, request_count, return_count,`source`
        from yyhz_0308.dn_cha_cash_total
        where
            `date` = #{selectDate}
        and agent in ('ohayoo','weixin','oppo','GDT','kuaishou','sigmob','vivo','Huawei','Mi','headline')
    </select>

    <insert id="insertStatisticReportList" parameterType="com.wbgame.report.analysis.StatisticsResult">
        INSERT INTO dnwx_cfg.statistic_report
        (
        count,pvSum,clickSum,revenueSum,buySum,reqCount,retCount,type,repDate,agent
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.count},#{item.pvSum},#{item.clickSum},#{item.revenueSum},
             #{item.buySum},#{item.reqCount},#{item.retCount},#{item.type},#{item.repDate}, #{item.agent}
            )
        </foreach>
    </insert>

    <delete id="deleteStatisticReport">
        delete from dnwx_cfg.statistic_report
        where repDate = #{repDate}
    </delete>

    <delete id="batchDeleteXyxAdvData">
        delete from yyhz_0308.dn_cha_cash_total
               where date BETWEEN #{startTime} AND  #{endTime} and agent = 'weixin'
        AND member_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
        '${item}'
        </foreach>
    </delete>

    <delete id="batchDeleteXyxAdbData">
        delete from yyhz_0308.dn_cha_cash_total
        where date BETWEEN #{startTime} AND  #{endTime} and agent = 'weixin'
        AND member_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            '${item}'
        </foreach>
    </delete>

    <!-- dn_adt.dn_xiaomi_app 迁移至 dnwx_cfg.dn_xiaomi_app
         取status为1的appid，当线上拉取的appid 返回："应用不存在!" 时，将status置为0
     -->
    <select id="getXiaomiApp" resultType="java.util.HashMap">
        SELECT account, appid FROM dnwx_cfg.dn_xiaomi_app where status = 1
    </select>
    <select id="getCashXiaomiAccountList"
            resultType="com.wbgame.pojo.adv2.businessAccountEntity.CashXiaomiAccount">

            SELECT * FROM dnwx_cfg.dn_xiaomi_account
            WHERE enabled = 1
    </select>

    <select id="getDouyinAccount" resultType="java.lang.String">
        select tttoken from douyin_account_cookie_for_task where enable = 1
    </select>

    <update id="updateXiaomiAppStatus">
        update dnwx_cfg.dn_xiaomi_app
        set status = 0
        where
            account = #{account}
            and appid = #{appid}
    </update>

    <delete id="delOppoReport">
        DELETE FROM dnwx_cfg.dn_oppo_report WHERE `day` = #{day} AND account = #{account}
    </delete>
    <delete id="deleteDouyinApps">
        DELETE FROM yyhz_0308.dn_cha_cash_total
        where date = #{date} and agent = #{platform}
        AND app_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="AddOppoReport" parameterType="com.wbgame.pojo.adv2.reportEntity.ReportChina">
        INSERT INTO dnwx_cfg.dn_oppo_report
        (
        account, platform, productName, productId, `day`, requests, clicks, fills, impressions, revenue, buyRevenue
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.member_id},#{item.platform}, #{item.appName}, #{item.app_id}, #{item.date}, #{item.request_count}, #{item.click},
            #{item.return_count}, #{item.pv}, #{item.revenue}, #{item.buy_revenue}
            )
        </foreach>
    </insert>
    <select id="aggregateHour" resultType="com.wbgame.pojo.adv2.reportEntity.ReportChina">
        select member_id,app_id,placement_id,`date`,'24' as hours,sum(request_count),sum(return_count) as return_count,sum(pv) as pv,sum(click) as click,sum(revenue) as revenue,dnappid,agent,placement_type,ad_sid,cha_type,cha_media,cha_sub_launch,cha_id,cha_type_name,buy_revenue,country,dollar_revenue,open_type,createTime,source,type
        from yyhz_0308.dn_cha_cash_total_hourly
        where date = #{date} and member_id = #{account}
        GROUP BY placement_id
    </select>
    <insert id="insertHourReport" parameterType="java.util.List">
        insert into yyhz_0308.dn_cha_cash_total_hourly
        (member_id,app_id,placement_id,date,hours,request_count,return_count,pv,click,revenue,dnappid,agent,placement_type,ad_sid,cha_type,cha_media,cha_sub_launch,cha_id,cha_type_name,buy_revenue,country,dollar_revenue,open_type,createTime,source,type)
        values
            <foreach collection="list" item="it" separator=",">
                (#{member_id},#{app_id},#{placement_id},#{date},#{hours},#{request_count},#{return_count},#{pv},#{click},#{revenue},#{dnappid},#{agent},#{placement_type},#{ad_sid},#{cha_type},#{cha_media},#{cha_sub_launch},#{cha_id},#{cha_type_name},#{buy_revenue},#{country},#{dollar_revenue},#{open_type},now(),#{source},#{type})
            </foreach>
    </insert>

    <select id="selectOhayooConfig" resultType="com.wbgame.pojo.AppInfoVo">
        select *
        from yyhz_0308.app_info
        where app_category = 38
    </select>

    <select id="selectOhayooPackages" resultType="java.lang.String">
        select distinct packageName
        from dnwx_client.wbgui_formconfig
        where LEFT (pjId, 5) in (
            select id
            from yyhz_0308.app_info
            where app_category = 38
            )
    </select>

    <select id="getCashOhayooExtraPackageAccountList"
            resultType="com.wbgame.pojo.adv2.businessAccountEntity.CashOhayooAccount">
        select * from yyhz_0308.dn_cash_ohayoo_extra_package_account where enabled = 1
    </select>
    <select id="getDouyinAccountAndCookie"
            resultType="com.wbgame.pojo.adv2.businessAccountEntity.DouyinAccount">
        select account, tttoken cookie from douyin_account_cookie_for_task where enable = 1
    </select>
    <select id="getAdConfigByPlatform" resultType="com.wbgame.pojo.adv2.reportEntity.AdConfigEntity">
        SELECT appid AS dn_appid, TRIM(cha_id) AS channel_tag, TRIM(sdk_code) AS `code`, TRIM(sdk_adtype) AS `type`,TRIM(open_type) AS `open_type`,
               TRIM(adsid) AS ad_sid, TRIM(sdk_appid) AS appid FROM dnwx_cfg.dn_extend_adsid_manage
        WHERE agent = #{platform}
    </select>
    <select id="getUnuseCashOhayooExtraPackageAccountList"
            resultType="com.wbgame.pojo.adv2.businessAccountEntity.CashOhayooAccount">
        select * from yyhz_0308.dn_cash_ohayoo_extra_package_account where enabled = 0
    </select>
    <select id="aggregateKsRevenue" resultType="com.wbgame.pojo.adv2.reportEntity.ReportChina">
        SELECT appid dnappid, sum(cost)/1000 revenue, count(1) pv FROM dnwx_cfg.${table_name}
        where tdate = #{date}
        GROUP BY appid
    </select>
    <select id="selectAppidInfo" resultType="java.util.Map">
        select appid, tappid from yyhz_0308.kuaishou_xyx_appid_info
        where enabled = 1
    </select>
	<select id="selectAppidByPkg" resultType="java.util.Map">
        select appid, packagename from yyhz_0308.adv_platform_app_info
        where platform = 'vivo'
    </select>
    <select id="getAdCodeStatus" resultType="java.lang.Integer">
        select statu from dnwx_cfg.dn_online_adpos_status
        where  placement_id=#{adCode}
    </select>
    <select id="selectCleanAppids" resultType="java.lang.String">
        select appid from yyhz_0308.clean_appid
    </select>
</mapper>