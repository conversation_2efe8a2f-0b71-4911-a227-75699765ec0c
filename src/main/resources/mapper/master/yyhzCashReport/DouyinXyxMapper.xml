<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.yyhzCashReport.DouyinXyxMapper">
    <insert id="insertBatchReport">
        insert into yyhz_0308.dn_cash_douyin_xyx_report
        (
            id,tdate,appid,tappid,host_name,version_type,income,
         active_user_num,new_user_num,total_user_num,share_time,open_time,per_user_open_time,per_user_stay_time,avg_stay_time,
         aactive_user_num,aoneday,atwoday,athreeday,afourday,afiveday,asixday,asivenday,afourteenday,amonth,
         bactive_user_num,boneday,btwoday,bthreeday,bfourday,bfiveday,bsixday,bsivenday,bfourteenday,bmonth
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.id},#{item.tdate},#{item.appid},#{item.tappid},#{item.host_name},#{item.version_type},#{item.income},
         #{item.active_user_num},#{item.new_user_num},#{item.total_user_num},#{item.share_time},#{item.open_time},#{item.per_user_open_time},#{item.per_user_stay_time},#{item.avg_stay_time},
         #{item.aactive_user_num},#{item.aoneday},#{item.atwoday},#{item.athreeday},#{item.afourday},#{item.afiveday},#{item.asixday},#{item.asivenday},#{item.afourteenday},#{item.amonth},
         #{item.bactive_user_num},#{item.boneday},#{item.btwoday},#{item.bthreeday},#{item.bfourday},#{item.bfiveday},#{item.bsixday},#{item.bsivenday},#{item.bfourteenday},#{item.bmonth}
        )
        </foreach>

    </insert>

    <update id="updateIncome" parameterType="java.util.List">
        <foreach collection="list" item="it" separator=";">
            update yyhz_0308.dn_douyin_xyx_flow_data
            <set>
                total_income = #{it.total_income},
                real_total_income = #{it.real_total_income},
            </set>
            where appid = #{it.appid}
            and tdate = #{it.tdate}
        </foreach>
    </update>
    <update id="updateUserBehavior">
        insert into dn_douyin_xyx_statistic_data
        (tdate, appid, tappid, active_user_num, new_user_num, total_user_num, share_time, open_time, per_user_open_time, per_user_stay_time, avg_stay_time)
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.tdate},
            #{lpc.appid},
            #{lpc.app_id},
            #{lpc.active_user_num},
            #{lpc.new_user_num},
            #{lpc.total_user_num},
            #{lpc.share_time},
            #{lpc.open_time},
            #{lpc.per_user_open_time},
            #{lpc.per_user_stay_time},
            #{lpc.avg_stay_time}
            )
        </foreach>ON DUPLICATE KEY UPDATE
        appid = VALUES(appid),
        active_user_num = VALUES(active_user_num),
        new_user_num = VALUES(new_user_num),
        total_user_num = VALUES(total_user_num),
        share_time = VALUES(share_time),
        open_time = VALUES(open_time),
        per_user_open_time = VALUES(per_user_open_time),
        per_user_stay_time = VALUES(per_user_stay_time),
        avg_stay_time = VALUES(avg_stay_time)
    </update>
    <update id="updateRetention">
        insert into dn_douyin_xyx_statistic_data
        (tdate, appid, tappid,aactive_user_num,aoneday,atwoday,athreeday,afourday,afiveday,asixday,asivenday,afourteenday,amonth,
         bactive_user_num,boneday,btwoday,bthreeday,bfourday,bfiveday,bsixday,bsivenday,bfourteenday,bmonth)
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.tdate},#{lpc.appid},#{lpc.app_id},
             #{lpc.aactive_user_num},#{lpc.aoneday},#{lpc.atwoday},#{lpc.athreeday},#{lpc.afourday},#{lpc.afiveday},#{lpc.asixday},#{lpc.asivenday},#{lpc.afourteenday},#{lpc.amonth},
             #{lpc.bactive_user_num},#{lpc.boneday},#{lpc.btwoday},#{lpc.bthreeday},#{lpc.bfourday},#{lpc.bfiveday},#{lpc.bsixday},#{lpc.bsivenday},#{lpc.bfourteenday},#{lpc.bmonth}
            )
        </foreach>ON DUPLICATE KEY UPDATE
        aactive_user_num = VALUES(aactive_user_num),aoneday = VALUES(aoneday),atwoday = VALUES(atwoday),
        athreeday = VALUES(athreeday),afourday = VALUES(afourday),afiveday = VALUES(afiveday),
        asixday = VALUES(asixday),asivenday = VALUES(asivenday),afourteenday = VALUES(afourteenday),
        amonth = VALUES(amonth),bactive_user_num = VALUES(bactive_user_num),boneday = VALUES(boneday),
        btwoday = VALUES(btwoday),bthreeday = VALUES(bthreeday),bfourday = VALUES(bfourday),bfiveday = VALUES(bfiveday),
        bsixday = VALUES(bsixday),bsivenday = VALUES(bsivenday),bfourteenday = VALUES(bfourteenday),bmonth = VALUES(bmonth)
    </update>

    <delete id="deleteReport" >
        delete from yyhz_0308.dn_cash_douyin_xyx_report
        where tdate = #{tdate} and appid = #{appid}
    </delete>

    <select id="selectAllByDateRange" resultType="com.wbgame.pojo.adv2.reportEntity.DouyinTotalDto">
        select appid,app_id,appName,tdate,click,request,`show`,clickRate,ecpm,realCost,jumpUvIncome, total_income, real_total_income
        from yyhz_0308.dn_douyin_xyx_flow_data
        where tdate between #{start} and #{end} and app_id = #{tappid}
    </select>


    <insert id="insertTotalReport" parameterType="com.wbgame.pojo.adv2.reportEntity.DouyinTotalDto">
        insert into yyhz_0308.dn_douyin_xyx_flow_data
        ( appid,app_id,appName,tdate,click,request,`show`,clickRate,ecpm,realCost,real_income,jumpUvIncome, refundFee, payMoney, payPerson)
        values
        <foreach collection="list" item="item" separator=",">
        (
            #{item.appid},#{item.app_id},#{item.appName},#{item.tdate},#{item.click},#{item.request},#{item.show},#{item.clickRate},#{item.ecpm},#{item.realCost},#{item.real_income},#{item.jumpUvIncome}, #{item.refundFee}, #{item.payMoney}, #{item.payPerson}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        click = VALUES(click),
        request = VALUES(request),
        `show` = VALUES(`show`),
        clickRate = VALUES(clickRate),
        ecpm = VALUES(ecpm),
        realCost = VALUES(realCost),
        real_income = VALUES(real_income),
        jumpUvIncome = VALUES(jumpUvIncome),
        refundFee = VALUES(refundFee),
        payMoney = VALUES(payMoney),
        payPerson = VALUES(payPerson),
        modifyTime = now()
    </insert>
    <insert id="insertBatchSource">
        insert into dn_douyin_xyx_source_data
        (tdate,appid,app_id,scene_id,scene_name,uv,newUv,openTime,avgStayTime, hostname)
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.tdate},#{lpc.appid},#{lpc.app_id},#{lpc.scene_id},#{lpc.scene_name},#{lpc.uv},#{lpc.newUv},#{lpc.openTime},#{lpc.avgStayTime},#{lpc.hostname}
            )
        </foreach>ON DUPLICATE KEY UPDATE
            scene_name = VALUES(scene_name),
        uv = VALUES(uv), newUv = VALUES(newUv), openTime = VALUES(openTime), avgStayTime = VALUES(avgStayTime), hostname = VALUES(hostname)
    </insert>
    <insert id="insertBatchSpend">
        insert into dn_douyin_xyx_spend_data
        (appid,app_id,app_name,tdate,rebate_income,spend,total_income,act_ad_income,act_pay_income,buy_ad_income,
         buy_pay_income,free_ad_income,free_pay_income,subsidy_10p,last_total_income,revenue,pay_prefer_income)
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.appid},#{lpc.app_id},#{lpc.app_name},#{lpc.tdate},#{lpc.rebate_income},#{lpc.spend},#{lpc.total_income},#{lpc.act_ad_income},
             #{lpc.act_pay_income},#{lpc.buy_ad_income},#{lpc.buy_pay_income},#{lpc.free_ad_income},#{lpc.free_pay_income},
             #{lpc.subsidy_10p}, #{lpc.last_total_income}, #{lpc.revenue},#{lpc.pay_prefer_income}
            )
        </foreach>ON DUPLICATE KEY UPDATE
            spend = VALUES(spend),
            total_income = VALUES(total_income),
            act_ad_income = VALUES(act_ad_income),
            act_pay_income = VALUES(act_pay_income),
            buy_ad_income = VALUES(buy_ad_income),
            buy_pay_income = VALUES(buy_pay_income),
            free_ad_income = VALUES(free_ad_income),
            free_pay_income = VALUES(free_pay_income),
            subsidy_10p = VALUES(subsidy_10p),
            last_total_income = VALUES(last_total_income),
            rebate_income = VALUES(rebate_income),
            revenue = VALUES(revenue),
            pay_prefer_income = VALUES(pay_prefer_income)
    </insert>
    <insert id="insertBatchFeedSpend">
            insert into dn_douyin_xyx_feed_spend_data
        (tdate,appid,app_id,hostApp,activeUser,adClickPv,adConsume,adIncome,adShowPv,cashAdPlayPv,clickRate,
         costPerAction,costPerClick,ecpm,loadSuccessPv,newAdIncome,newOrderIncome,newUser,orderIncome,stayTimePerUser,totalIncome,
         activeVidoePer,totalArpu,adArpu,orderArpu,totalRoi,adRoi,orderRoi
         )
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.tdate},#{lpc.appid},#{lpc.app_id},#{lpc.hostApp},#{lpc.activeUser},#{lpc.adClickPv},#{lpc.adConsume},#{lpc.adIncome},#{lpc.adShowPv},#{lpc.cashAdPlayPv},#{lpc.clickRate},
             #{lpc.costPerAction},#{lpc.costPerClick},#{lpc.ecpm},#{lpc.loadSuccessPv},#{lpc.newAdIncome},#{lpc.newOrderIncome},#{lpc.newUser},#{lpc.orderIncome},#{lpc.stayTimePerUser},#{lpc.totalIncome},
             #{lpc.activeVidoePer},#{lpc.totalArpu},#{lpc.adArpu},#{lpc.orderArpu},#{lpc.totalRoi},#{lpc.adRoi},#{lpc.orderRoi}
            )
        </foreach>ON DUPLICATE KEY UPDATE
            activeUser = VALUES(activeUser),
            adClickPv = VALUES(adClickPv),
            adConsume = VALUES(adConsume),
            adIncome = VALUES(adIncome),
            adShowPv = VALUES(adShowPv),
            cashAdPlayPv = VALUES(cashAdPlayPv),
            clickRate = VALUES(clickRate),
            costPerAction = VALUES(costPerAction),
            costPerClick = VALUES(costPerClick),
            ecpm = VALUES(ecpm),
            loadSuccessPv = VALUES(loadSuccessPv),
            newAdIncome = VALUES(newAdIncome),
            newOrderIncome = VALUES(newOrderIncome),
            newUser = VALUES(newUser),
            orderIncome = VALUES(orderIncome),
            stayTimePerUser = VALUES(stayTimePerUser),
            totalIncome = VALUES(totalIncome),
            activeVidoePer = VALUES(activeVidoePer),
            totalArpu = VALUES(totalArpu),
            adArpu = VALUES(adArpu),
            orderArpu = VALUES(orderArpu),
            totalRoi = VALUES(totalRoi),
            adRoi = VALUES(adRoi),
            orderRoi = VALUES(orderRoi)
    </insert>
    <insert id="insertBatchLTV">
        insert into dn_douyin_xyx_ltv_data
        (tdate,appid,app_id,hostApp,adLtv1,adLtv2,adLtv3,adLtv4,adLtv5,adLtv6,adLtv7,adLtv15,adLtv30,adLtv60,adLtv150,
         ltv1,ltv2,ltv3,ltv4,ltv5,ltv6,ltv7,ltv15,ltv30,ltv60,ltv150,orderLtv1,orderLtv2,orderLtv3,orderLtv4,orderLtv5,
         orderLtv6,orderLtv7,orderLtv15,orderLtv30,orderLtv60,orderLtv150)
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.tdate},#{lpc.appid},#{lpc.app_id},#{lpc.hostApp},#{lpc.adLtv1},#{lpc.adLtv2},#{lpc.adLtv3},#{lpc.adLtv4},#{lpc.adLtv5},#{lpc.adLtv6},#{lpc.adLtv7},#{lpc.adLtv15},#{lpc.adLtv30},#{lpc.adLtv60},#{lpc.adLtv150},
             #{lpc.ltv1},#{lpc.ltv2},#{lpc.ltv3},#{lpc.ltv4},#{lpc.ltv5},#{lpc.ltv6},#{lpc.ltv7},#{lpc.ltv15},#{lpc.ltv30},#{lpc.ltv60},#{lpc.ltv150},#{lpc.orderLtv1},#{lpc.orderLtv2},#{lpc.orderLtv3},#{lpc.orderLtv4},#{lpc.orderLtv5},
             #{lpc.orderLtv6},#{lpc.orderLtv7},#{lpc.orderLtv15},#{lpc.orderLtv30},#{lpc.orderLtv60},#{lpc.orderLtv150})
        </foreach>ON DUPLICATE KEY UPDATE
            adLtv1 = VALUES(adLtv1),
            adLtv2 = VALUES(adLtv2),
            adLtv3 = VALUES(adLtv3),
            adLtv4 = VALUES(adLtv4),
            adLtv5 = VALUES(adLtv5),
            adLtv6 = VALUES(adLtv6),
            adLtv7 = VALUES(adLtv7),
            adLtv15 = VALUES(adLtv15),
            adLtv30 = VALUES(adLtv30),
            adLtv60 = VALUES(adLtv60),
            adLtv150 = VALUES(adLtv150),
            ltv1 = VALUES(ltv1),
            ltv2 = VALUES(ltv2),
            ltv3 = VALUES(ltv3),
            ltv4 = VALUES(ltv4),
            ltv5 = VALUES(ltv5),
            ltv6 = VALUES(ltv6),
            ltv7 = VALUES(ltv7),
            ltv15 = VALUES(ltv15),
            ltv30 = VALUES(ltv30),
            ltv60 = VALUES(ltv60),
            ltv150 = VALUES(ltv150),
            orderLtv1 = VALUES(orderLtv1),
            orderLtv2 = VALUES(orderLtv2),
            orderLtv3 = VALUES(orderLtv3),
            orderLtv4 = VALUES(orderLtv4),
            orderLtv5 = VALUES(orderLtv5),
            orderLtv6 = VALUES(orderLtv6),
            orderLtv7 = VALUES(orderLtv7),
            orderLtv15 = VALUES(orderLtv15),
            orderLtv30 = VALUES(orderLtv30),
            orderLtv60 = VALUES(orderLtv60),
            orderLtv150 = VALUES(orderLtv150)
    </insert>
    <insert id="insertBatchROI">
        insert into dn_douyin_xyx_roi_data
        (tdate,appid,app_id,hostApp,adRoi1,adRoi2,adRoi3,adRoi4,adRoi5,adRoi6,adRoi7,adRoi15,adRoi30,adRoi60,adRoi150,
         roi1,roi2,roi3,roi4,roi5,roi6,roi7,roi15,roi30,roi60,roi150,orderRoi1,orderRoi2,orderRoi3,orderRoi4,orderRoi5,
         orderRoi6,orderRoi7,orderRoi15,orderRoi30,orderRoi60,orderRoi150)
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.tdate},#{lpc.appid},#{lpc.app_id},#{lpc.hostApp},#{lpc.adRoi1},#{lpc.adRoi2},#{lpc.adRoi3},#{lpc.adRoi4},#{lpc.adRoi5},#{lpc.adRoi6},#{lpc.adRoi7},#{lpc.adRoi15},#{lpc.adRoi30},#{lpc.adRoi60},#{lpc.adRoi150},
             #{lpc.roi1},#{lpc.roi2},#{lpc.roi3},#{lpc.roi4},#{lpc.roi5},#{lpc.roi6},#{lpc.roi7},#{lpc.roi15},#{lpc.roi30},#{lpc.roi60},#{lpc.roi150},#{lpc.orderRoi1},#{lpc.orderRoi2},#{lpc.orderRoi3},#{lpc.orderRoi4},#{lpc.orderRoi5},
             #{lpc.orderRoi6},#{lpc.orderRoi7},#{lpc.orderRoi15},#{lpc.orderRoi30},#{lpc.orderRoi60},#{lpc.orderRoi150})
        </foreach>ON DUPLICATE KEY UPDATE
            adRoi1 = VALUES(adRoi1),
            adRoi2 = VALUES(adRoi2),
            adRoi3 = VALUES(adRoi3),
            adRoi4 = VALUES(adRoi4),
            adRoi5 = VALUES(adRoi5),
            adRoi6 = VALUES(adRoi6),
            adRoi7 = VALUES(adRoi7),
            adRoi15 = VALUES(adRoi15),
            adRoi30 = VALUES(adRoi30),
            adRoi60 = VALUES(adRoi60),
            adRoi150 = VALUES(adRoi150),
            roi1 = VALUES(roi1),
            roi2 = VALUES(roi2),
            roi3 = VALUES(roi3),
            roi4 = VALUES(roi4),
            roi5 = VALUES(roi5),
            roi6 = VALUES(roi6),
            roi7 = VALUES(roi7),
            roi15 = VALUES(roi15),
            roi30 = VALUES(roi30),
            roi60 = VALUES(roi60),
            roi150 = VALUES(roi150),
            orderRoi1 = VALUES(orderRoi1),
            orderRoi2 = VALUES(orderRoi2),
            orderRoi3 = VALUES(orderRoi3),
            orderRoi4 = VALUES(orderRoi4),
            orderRoi5 = VALUES(orderRoi5),
            orderRoi6 = VALUES(orderRoi6),
            orderRoi7 = VALUES(orderRoi7),
            orderRoi15 = VALUES(orderRoi15),
            orderRoi30 = VALUES(orderRoi30),
            orderRoi60 = VALUES(orderRoi60),
            orderRoi150 = VALUES(orderRoi150)
    </insert>
    <insert id="insertBatchCampaign">
insert into dn_douyin_xyx_campaign_data
        (appid,app_id,app_name,tdate,campaign_id,campaign_name,spend,buy_ad_income,
         buy_pay_income,newRegisterCnt,videoPlaySettle,videoPost,dauV2Settle)
        values
        <foreach collection="list" item="lpc" separator=",">
            (#{lpc.appid},#{lpc.app_id},#{lpc.app_name},#{lpc.tdate},#{lpc.campaign_id},#{lpc.campaign_name},#{lpc.spend},#{lpc.buy_ad_income},
             #{lpc.buy_pay_income},#{lpc.newRegisterCnt},#{lpc.videoPlaySettle},#{lpc.videoPost},#{lpc.dauV2Settle}
            )
        </foreach>ON DUPLICATE KEY UPDATE
            spend = VALUES(spend),
            buy_ad_income = VALUES(buy_ad_income),
            buy_pay_income = VALUES(buy_pay_income),
            newRegisterCnt = VALUES(newRegisterCnt),
            videoPlaySettle = VALUES(videoPlaySettle),
            videoPost = VALUES(videoPost),
            dauV2Settle = VALUES(dauV2Settle)
    </insert>
    <insert id="insertBatchVideo">
        insert into dn_douyin_xyx_video_data
        (appid,app_id,campaign_id,campaign_name,app_name,playCnt,income,isPublic,likes,
         anchorClick,anchorShow,shareCnt,cover,title,author,publishTime,itemId,itemUrl)
        values <foreach collection="list" item="it" separator=",">
        (#{appid},#{app_id},#{campaign_id},#{it.taskName},#{appName},#{it.playCnt},#{it.income},#{it.isPublic},
        #{it.likes},#{it.anchorClick},#{it.anchorShow},#{it.shareCnt},#{it.cover},#{it.title},#{it.author},#{it.publishTime},#{it.itemId},#{it.itemUrl})
    </foreach>  ON DUPLICATE KEY UPDATE
                    playCnt = VAlUES(playCnt),
                    income = VAlUES(income),
                    isPublic = VAlUES(isPublic),
                    likes = VAlUES(likes),
                    anchorClick = VAlUES(anchorClick),
                    anchorShow = VAlUES(anchorShow),
                    shareCnt = VAlUES(shareCnt)
    </insert>

    <delete id="deleteTotalReport">
        delete from yyhz_0308.dn_douyin_xyx_flow_data
        where tdate = #{date} and appid in (
        <foreach collection="list" item="it" separator=",">
            ${it}
        </foreach>
        )
    </delete>
    <delete id="deleteWechatGame">
        delete from wechat_game_operation_data
        where
            tdate = #{date} and appid in (
                <foreach collection="list" item="item" separator=",">
                    ${item}
                </foreach>
            )
    </delete>

    <update id="updateWechatGame">
        replace into `wechat_game_operation_data`
        (tdate, appid, act_num, add_num, cumulative_registere_users, act_visits_num, visits_num_per,
         average_stay, rd1_rate, rd7_rate, shares_num, sharing_rate, purchase_users, ad_revenue,
         pay_amount,video_pv,video_revenue,acmul_revenue)
        select a.tdate, a.appid, active_user_num, new_user_num, total_user_num, open_time, per_user_open_time,
               per_user_stay_time/1000, aoneday, asivenday, share_time, IF(active_user_num=0, 0, share_time/active_user_num), b.payPerson, b.realCost,
               b.payMoney, b.`show`,b.realCost,b.total_income
        from dn_douyin_xyx_flow_data b
            left join dn_douyin_xyx_statistic_data a
                on a.tdate = b.tdate and a.tappid = b.app_id
        where a.tdate = #{date}
    </update>

    <update id="updateAcmulSpend">
        update    wechat_game_operation_data
        set
            acmul_spend = #{acmulSpend}
        where appid = #{appid}
        and tdate = #{tdate}
    </update>

    <select id="selectAcmulSpend" resultType="java.lang.Double">
        select acmul_spend from wechat_game_operation_data
        where
            tdate = #{tdate} and
            appid = #{appid}
    </select>
    <select id="selectAllFlow" resultType="com.wbgame.pojo.adv2.reportEntity.DouyinTotalDto">
        select appid,app_id,appName,tdate,click,request,`show`,
        convert(clickRate*100, decimal(10,2)) clickRate,
        convert(ecpm, decimal(10,2)) ecpm,
        convert(realCost, decimal(10,2)) realCost,jumpUvIncome,
        convert(real_income, decimal(10,2)) real_income,
        convert(total_income, decimal(10,2)) total_income,
        convert(real_total_income, decimal(10,2)) real_total_income,
        convert(refundFee, decimal(10,2)) refundFee,
        convert(payMoney, decimal(10,2)) payMoney,payPerson,createTime,modifyTime from dn_douyin_xyx_flow_data
        where
            tdate between #{startDate} and #{endDate}
        <if test="app_id != null and app_id != ''">
            and app_id = #{app_id}
        </if>
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        order by tdate desc
    </select>
    <select id="selectAllStatistic" resultType="com.wbgame.pojo.adv2.reportEntity.DouyinStatisticDto">
        select *, tappid app_id from dn_douyin_xyx_statistic_data
        where
        tdate between #{startDate} and #{endDate}
        <if test="app_id != null and app_id != ''">
            and tappid = #{app_id}
        </if>
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        order by tdate desc
    </select>
</mapper>