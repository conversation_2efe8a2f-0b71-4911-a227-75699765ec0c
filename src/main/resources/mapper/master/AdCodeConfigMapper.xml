<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.AdCodeConfigMapper">

    <select id="getAdCodeAccountList" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo" resultType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        select  * from  adv_adcode_account_info where  1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="tappid != null and tappid != ''">
            and tappid = #{tappid}
        </if>
        <if test="channel != null and channel != ''">
            and channel = #{channel}
        </if>
        <if test="note != null and note != ''">
            and note like '%{note}%'
        </if>
        <if test="platform != null and platform != ''">
            and platform in (${platform})
        </if>

    </select>

    <select id="getCSJAdCodeAccountList" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo" resultType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        select  * from  adv_adcode_account_info where  (channel = 'csj' or channel = 'zhubao')
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="tappid != null and tappid != ''">
            and tappid = #{tappid}
        </if>
        <if test="note != null and note != ''">
            and note like '%{note}%'
        </if>
        <if test="platform != null and platform != ''">
            and platform = (${platform})
        </if>
    </select>

    <select id="getAdCodeAccountByMix" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo" resultType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        select  * from  adv_adcode_account_info where  1=1
        and appid = #{appid} and tappid =#{tappid} and taccountid =#{taccountid} and platform =#{platform}
    </select>

    <insert id="addAdCodeAccount" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        insert into adv_adcode_account_info (appid,tappid,taccountid,tsecrect,platform,platformName,note,createUser,createTime, channel)
        values (#{appid},#{tappid},#{taccountid},#{tsecrect},#{platform},#{platformName},#{note},#{createUser},now(), #{channel})
        ON DUPLICATE KEY UPDATE
        modifyUser=VALUES(modifyUser),
        modifyTime=now()
    </insert>

    <update id="updateAdCodeAccount" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        update adv_adcode_account_info set appid = #{appid},tappid = #{tappid},
        taccountid = #{taccountid},tsecrect = #{tsecrect},platform = #{platform},
        platformName = #{platformName},note = #{note},modifyUser =#{modifyUser},modifyTime =now(), channel = #{channel}
        where id = #{id}
    </update>

    <delete id="delAdCodeAccount" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo" >
        delete from adv_adcode_account_info where id =#{id}
    </delete>


    <select id="getHeadlineAdCodeList" parameterType="com.wbgame.pojo.adv2.CSJAdcodeVo" resultType="com.wbgame.pojo.adv2.CSJAdcodeVo">
        select * from  adv_adcode_headline_info where  1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="ad_slot_type != null and ad_slot_type != ''">
            and ad_slot_type =#{ad_slot_type}
        </if>
        <if test="ad_slot_id != null and ad_slot_id != ''">
            and ad_slot_id = #{ad_slot_id}
        </if>
        <if test="ad_slot_name != null and ad_slot_name != ''">
            and ad_slot_name like  concat('%',#{ad_slot_name},'%')
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        order by createTime desc
    </select>

    <select id="getYLHAdCodeList" parameterType="com.wbgame.pojo.adv2.YLHAdcodeVo" resultType="com.wbgame.pojo.adv2.YLHAdcodeVo">
        select * from  adv_adcode_ylh_info where  1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="scene != null and scene != ''">
            and scene = #{scene}
        </if>
        <if test="placement_id != null and placement_id != ''">
            and placement_id = #{placement_id}
        </if>
        <if test="placement_name != null and placement_name != ''">
            and placement_name like  concat('%',#{placement_name},'%')
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        order by createTime desc
    </select>

    <select id="getKSAdCodeList" parameterType="com.wbgame.pojo.adv2.KSAdcodeVo" resultType="com.wbgame.pojo.adv2.KSAdcodeVo">
        select * from  adv_adcode_ks_info where  1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="adStyle != null and adStyle != ''">
            and adStyle = #{adStyle}
        </if>
        <if test="positionId != null and positionId != ''">
            and positionId = #{positionId}
        </if>
        <if test="name != null and name != ''">
            and `name` like  concat('%',#{name},'%')
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="priceStrategy != null and priceStrategy != ''">
            and priceStrategy = #{priceStrategy} 
        </if>
        order by createTime desc
    </select>

    <insert id="saveHeadlineAdCode" parameterType="com.wbgame.pojo.adv2.CSJAdcodeVo" >
        insert into adv_adcode_headline_info (
        ad_slot_id,appid,channel,adExtensionName,remark,user_id,role_id,app_id,ad_slot_type,sdk_ad_type,open_type,
        ad_slot_name,use_mediation,cpm,render_type,bidding_type,video_voice_control,
        ad_categories,tpl_list,accept_material_type,slide_banner,
        width,height,use_icon,orientation,reward_name,reward_count,
        reward_is_callback,reward_callback_url,ad_rollout_size,skip_duration,use_endcard,strategy,createUser,createTime)
        values (
        #{ad_slot_id},#{appid},#{channel},#{adExtensionName},#{remark},#{user_id},#{role_id},#{app_id},#{ad_slot_type},#{sdk_ad_type},#{open_type},
        #{ad_slot_name},#{use_mediation},#{cpm},#{render_type},#{bidding_type},#{video_voice_control},
        #{ad_categories},#{tpl_list},#{accept_material_type},#{slide_banner},
        #{width},#{height},#{use_icon},#{orientation},#{reward_name},#{reward_count},
        #{reward_is_callback},#{reward_callback_url},#{ad_rollout_size},#{skip_duration},#{use_endcard},#{strategy},#{createUser},now())
    </insert>

    <insert id="saveKSAdCode" parameterType="com.wbgame.pojo.adv2.KSAdcodeVo">
        insert into adv_adcode_ks_info (positionId,appid,channel,adExtensionName,remark,app_id,`name`,adStyle,renderType,materialTypeList,
        templateId,rewardedType,rewardedNum,callbackStatus,callbackUrl,sdk_ad_type,cpm,open_type,bidding_type,
        skipAdMode,countdownShow,voice,strategy,priceStrategy,createUser,createTime)
        values (
        #{positionId},#{appid},#{channel},#{adExtensionName},#{remark},#{app_id},#{name},#{adStyle},#{renderType},#{materialTypeList},
        #{templateId},#{rewardedType},#{rewardedNum},#{callbackStatus},#{callbackUrl},#{sdk_ad_type},#{cpm},#{open_type},#{bidding_type},
        #{skipAdMode},#{countdownShow},#{voice},#{strategy},#{priceStrategy},#{createUser},now()
        )
    </insert>

    <insert id="saveYLHAdCode" parameterType="com.wbgame.pojo.adv2.YLHAdcodeVo">
        insert into adv_adcode_ylh_info(placement_id,member_id,appid,channel,adExtensionName,remark,app_id,placement_name,
        scene,rewarded_video_scene,rewarded_video_description,ad_pull_mode,render_type,sdk_ad_type,open_type,
        ad_crt_type_list,ad_crt_template_type,ad_crt_normal_type,flash_crt_type,is_open_rewarded,
        rewarded_video_crt_type,ad_feedback_element,need_server_verify,transfer_url,secret,price_strategy_type,real_time_bidding_type,
        ecpm_price,placement_test_status,strategy,createUser,createTime)
        values (
        #{placement_id},#{member_id},#{appid},#{channel},#{adExtensionName},#{remark},#{app_id},#{placement_name},
        #{scene},#{rewarded_video_scene},#{rewarded_video_description},#{ad_pull_mode},#{render_type},#{sdk_ad_type},#{open_type},
        #{ad_crt_type_list},#{ad_crt_template_type},#{ad_crt_normal_type},#{flash_crt_type},#{is_open_rewarded},
        #{rewarded_video_crt_type},#{ad_feedback_element},#{need_server_verify},#{transfer_url},#{secret},#{price_strategy_type},#{real_time_bidding_type},
        #{ecpm_price},#{placement_test_status},#{strategy},#{createUser},now()
        )
    </insert>

    <update id="updateKSCpm" parameterType="com.wbgame.pojo.adv2.KSAdcodeVo">
        update adv_adcode_ks_info set cpm = #{cpm},modifyUser=#{modifyUser},modifyTime = now()  where positionId =#{positionId}
    </update>

    <update id="updateYLHCpm" parameterType="com.wbgame.pojo.adv2.YLHAdcodeVo">
        update adv_adcode_ylh_info set ecpm_price =#{ecpm_price},modifyUser=#{modifyUser},modifyTime = now() where placement_id =#{placement_id}
    </update>

    <update id="updateCSJCpm" parameterType="com.wbgame.pojo.adv2.CSJAdcodeVo">
        update adv_adcode_headline_info set cpm =#{cpm},modifyUser=#{modifyUser},modifyTime =now() where ad_slot_id =#{ad_slot_id}
    </update>

    <insert id="saveYLHMedium" parameterType="com.wbgame.pojo.adv2.YLHMediumVo">
        insert into adv_medium_ylh_info (appid,app_id,member_id,medium_name,industry_id,
        industry_id_v2,os,detail_url,affiliation,package_name,full_package_name,sha1,wechat_app_id,
        wechat_universal_link,icp_picture_img_id,soft_right_img_id,company_ship_img_id,
        biz_right_img_id,game_isbn_img_id,medium_test_status,profit_mode,convert_to_formal,create_user,create_time)
        values (#{appid},#{app_id},#{member_id},#{medium_name},#{industry_id},
        #{industry_id_v2},#{os},#{detail_url},#{affiliation},#{package_name},#{full_package_name},#{sha1},#{wechat_app_id},
        #{wechat_universal_link},#{icp_picture_img_id},#{soft_right_img_id},#{company_ship_img_id},
        #{biz_right_img_id},#{game_isbn_img_id},#{medium_test_status},#{profit_mode},#{convert_to_formal},#{create_user},now())
        ON DUPLICATE KEY UPDATE appid =values(appid),medium_name =values(medium_name),industry_id =values(industry_id),
        industry_id_v2 =values(industry_id_v2),os =values(os),detail_url =values(detail_url),
        affiliation =values(affiliation),package_name =values(package_name),full_package_name =values(full_package_name),
        sha1 =values(sha1),wechat_app_id =values(wechat_app_id),wechat_universal_link =values(wechat_universal_link),
        icp_picture_img_id =values(icp_picture_img_id),soft_right_img_id =values(soft_right_img_id),company_ship_img_id =values(company_ship_img_id),
        biz_right_img_id =values(biz_right_img_id),game_isbn_img_id =values(game_isbn_img_id),medium_test_status =values(medium_test_status),
        profit_mode =values(profit_mode),convert_to_formal =values(convert_to_formal),modify_user =values(modify_user),modify_time = now()
    </insert>

    <insert id="batchSaveYLHMedium" parameterType="java.util.List">
        insert into adv_medium_ylh_info (appid,app_id,member_id,medium_name,industry_id,
        industry_id_v2,os,detail_url,affiliation,package_name,full_package_name,sha1,wechat_app_id,
        wechat_universal_link,icp_picture_img_id,soft_right_img_id,company_ship_img_id,
        biz_right_img_id,game_isbn_img_id,medium_test_status,profit_mode,status) values
        <foreach collection="list" item="item" separator=",">
            (#{item.appid},#{item.app_id},#{item.member_id},#{item.medium_name},#{item.industry_id},
            #{item.industry_id_v2},#{item.os},#{item.detail_url},#{item.affiliation},#{item.package_name},#{item.full_package_name},#{item.sha1},#{item.wechat_app_id},
            #{item.wechat_universal_link},#{item.icp_picture_img_id},#{item.soft_right_img_id},#{item.company_ship_img_id},
            #{item.biz_right_img_id},#{item.game_isbn_img_id},#{item.medium_test_status},#{item.profit_mode},#{item.status})
        </foreach>
        ON DUPLICATE KEY UPDATE member_id =values(member_id),medium_name = values(medium_name),industry_id = values(industry_id),
        industry_id_v2 = values(industry_id_v2),os = values(os),detail_url = values(detail_url),
        affiliation = values(affiliation),package_name = values(package_name),full_package_name = values(full_package_name),
        sha1 = values(sha1),wechat_app_id = values(wechat_app_id),wechat_universal_link = values(wechat_universal_link),
        icp_picture_img_id = values(icp_picture_img_id),soft_right_img_id = values(soft_right_img_id),
        company_ship_img_id = values(company_ship_img_id),biz_right_img_id = values(biz_right_img_id),
        game_isbn_img_id = values(game_isbn_img_id),medium_test_status = values(medium_test_status),
        profit_mode = values(profit_mode),status = values(status)
    </insert>

    <update id="updateYLHMediumBaseInfo" parameterType="com.wbgame.pojo.adv2.YLHMediumVo">
        update adv_medium_ylh_info set appid = #{appid},modify_user =#{modify_user},modify_time = now()
        where app_id = #{app_id}
    </update>

    <select id="getYLHMediumList" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.YLHMediumVo">
        select * from  adv_medium_ylh_info where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="member_id != null and member_id != ''">
            and member_id = #{member_id}
        </if>
        <if test="medium_name != null and medium_name != ''">
            and medium_name like '%${medium_name}%'
        </if>
    </select>

    <select id="getMobvistaAdCodeList" parameterType="com.wbgame.pojo.adv2.MobvistaAdVo" resultType="com.wbgame.pojo.adv2.MobvistaAdVo">
        select * from  adv_adcode_mobvista_info where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="ad_type != null and ad_type != ''">
            and ad_type = #{ad_type}
        </if>
        <if test="unit_id != null and unit_id != ''">
            and unit_id = #{unit_id}
        </if>
        <if test="unit_name != null and unit_name != ''">
            and (unit_name like '%${unit_name}%' or hb_unit_name like '%${unit_name}%' )
        </if>
    </select>

    <select id="selectTappidList" resultType="java.lang.String">
        select distinct tappid
        from adv_adcode_account_info
        where
            platformName = #{platformName}
        and channel = #{channel}
    </select>

    <insert id="saveMobvistaAdCode" parameterType="com.wbgame.pojo.adv2.MobvistaAdVo">
        insert into adv_adcode_mobvista_info (appid,app_id,placement_name,ad_type,integrate_type,
        content_type,video_orientation,show_close_button,auto_fresh,hb_unit_name,placement_id,
        bidding_type,unit_name,ecpm_floor,target_ecpm,unit_id,channel,adExtensionName,remark,
        sdk_ad_type,open_type,createTime,createUser,ecpm,strategy)
        values (#{appid},#{app_id},#{placement_name},#{ad_type},#{integrate_type},
        #{content_type},#{video_orientation},#{show_close_button},#{auto_fresh},#{hb_unit_name},#{placement_id},
        #{bidding_type},#{unit_name},#{ecpm_floor},#{target_ecpm},#{unit_id},#{channel},#{adExtensionName},#{remark},
        #{sdk_ad_type},#{open_type},now(),#{createUser},#{ecpm_price},#{strategy})
    </insert>

    <insert id="insertSigmobAdCode" parameterType="com.wbgame.pojo.adv2.SigmobAdVo">
        insert into adv_adcode_sigmob_info (
            appid,app_id,adtype,name,scenario,frequencyd,addirect,reward_setting,reward_name,reward_sum,reward_url,
            render_type,material,direction,min_duration,max_duration,is_mute,is_auto_play,becpm_state,becpm,internal_bidding,
            channel,adExtensionName,remark,sdk_ad_type,open_type,strategy,placementid,createTime,createUser,modifyTime,modifyUser
            )
        values
            (
            #{appid},#{app_id},#{adtype},#{name},#{scenario},#{frequencyd},#{addirect},#{reward_setting},#{reward_name},#{reward_sum},#{reward_url},
            #{render_type},#{material},#{direction},#{min_duration},#{max_duration},#{is_mute},#{is_auto_play},#{becpm_state},#{becpm},#{internal_bidding},
            #{channel},#{adExtensionName},#{remark},#{sdk_ad_type},#{open_type},#{strategy},#{placementid},now(),#{createUser},now(),#{modifyUser}
            )
    </insert>

    <select id="selectSigmobAdCodeList" resultType="com.wbgame.pojo.adv2.SigmobAdVo" parameterType="com.wbgame.pojo.adv2.SigmobAdVo">
        select
            appid,app_id,adtype,`name`,scenario,frequencyd,addirect,reward_setting,reward_name,reward_sum,reward_url,
               render_type,material,direction,min_duration,max_duration,is_mute,is_auto_play,becpm_state,becpm,internal_bidding,
               channel,adExtensionName,remark,sdk_ad_type,open_type,strategy,placementid,createTime,createUser,modifyTime,modifyUser
        from
            adv_adcode_sigmob_info
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="adtype != null and adtype != ''">
            and adtype = #{adtype}
        </if>
        <if test="placementid != null and placementid != ''">
            and placementid = #{placementid}
        </if>
        <if test="name != null and name != ''">
            and (`name` like '%${name}%')
        </if>
    </select>
    <select id="getAdCodeAccountListByTappid" parameterType="com.wbgame.pojo.adv2.AdCodeAccountVo" resultType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        select  * from  adv_adcode_account_info where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="tappid != null and tappid != ''">
            and tappid = #{tappid}
        </if>
        <if test="note != null and note != ''">
            and note like '%{note}%'
        </if>
        <if test="platform != null and platform != ''">
            and platform = (${platform})
        </if>
    </select>

    <insert id="insertCSJModule" parameterType="com.wbgame.pojo.adv2.adcodeModule.CSJAdcodeModuleVo">
        insert into adv_adcode_module_headline_info
        (
            module_name,platform,sdk_ad_type,open_type,strategy,ad_slot_name,bidding_type,cpmFormula,cpmStr,
            use_mediation,ad_slot_type,render_type,ad_categories,tpl_list,accept_material_type,video_voice_control,
            slide_banner,width,height,use_icon,orientation,reward_name,reward_count,reward_is_callback,reward_callback_url,
            ad_rollout_size,skip_duration,use_endcard,createUser,createTime,modifyUser,modifyTime
        ) values (
                     #{module_name},#{platform},#{sdk_ad_type},#{open_type},#{strategy},#{ad_slot_name},#{bidding_type},#{cpmFormula},#{cpmStr},
                     #{use_mediation},#{ad_slot_type},#{render_type},#{ad_categories},#{tpl_list},#{accept_material_type},#{video_voice_control},
                     #{slide_banner},#{width},#{height},#{use_icon},#{orientation},#{reward_name},#{reward_count},#{reward_is_callback},#{reward_callback_url},
                     #{ad_rollout_size},#{skip_duration},#{use_endcard},#{createUser},now(),#{modifyUser},now()
                 )
    </insert>
    <insert id="insertAdsidDtos">
        insert into dnwx_cfg.dn_online_adpos_status
            (platform,app_id,appid,placement_id,placement_name,statu,audit_status,adsid,createtime, gdt_status,status_update_time)
        values
        <foreach collection="list" item="it" separator=",">
        (#{it.platform},#{it.app_id},#{it.appid},#{it.placement_id},#{it.placement_name},#{it.statu},#{it.audit_status},#{it.adsid},now(),#{it.gdt_status},#{it.status_update_time})
        </foreach>
    </insert>

    <select id="selectCSJModule" resultType="com.wbgame.pojo.adv2.adcodeModule.CSJAdcodeModuleVo">
        select id,module_name,platform,sdk_ad_type,open_type,strategy,ad_slot_name,bidding_type,cpmFormula,cpmStr,
               use_mediation,ad_slot_type,render_type,ad_categories,tpl_list,accept_material_type,video_voice_control,
               slide_banner,width,height,use_icon,orientation,reward_name,reward_count,reward_is_callback,reward_callback_url,
               ad_rollout_size,skip_duration,use_endcard,createUser,createTime,modifyUser,modifyTime
        from adv_adcode_module_headline_info
        where 1 = 1
        <if test="module_name != null and module_name != ''">
            and module_name = #{module_name}
        </if>
        <if test="platform != null and platform != ''">
            and platform = #{platform}
        </if>
        <if test="bidding_type != null and bidding_type != ''">
            and bidding_type = #{bidding_type}
        </if>
        <if test="createUser != null and createUser != ''">
            and createUser = #{createUser}
        </if>
    </select>
    <select id="selectOppoAccount" resultType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        select platform, account taccountid, secret tsecrect from dnwx_cfg.api_ad_pos_list_account
        where platform = #{platform}
    </select>
    <select id="selectAdsidDtos" resultType="com.wbgame.pojo.adv2.OnlineAdsidDto">
        select o.*,m.createtime adsid_createtime
        from dnwx_cfg.dn_online_adpos_status o left join  dnwx_cfg.dn_extend_adsid_manage m on o.adsid=m.adsid
        where o.adsid is not null
        <if test="platform != null and platform != ''">
            and o.platform = #{platform}
        </if>
        <if test="appid != null and appid != ''">
            and o.appid in (${appid})
        </if>
        <if test="statu != null and statu != ''">
            and o.statu = #{statu}
        </if>
        <if test="placement_id != null and placement_id != ''">
            and o.placement_id = #{placement_id}
        </if>
        <if test="adsid != null and adsid != ''">
            and o.adsid = #{adsid}
        </if>
        <if test="gdt_status != null and gdt_status != ''">
            and o.gdt_status in (${gdt_status})
        </if>
    </select>
    <select id="getUnioniAccountList" resultType="com.wbgame.pojo.adv2.AdCodeAccountVo">
        SELECT a.*,b.accessid taccountid,b.secret tsecrect,b.accountName FROM yyhz_0308.`adv_platform_app_info` a
                    LEFT JOIN dnwx_cfg.dn_api_platform_account b on a.taccount = b.account
        where channel = #{channel}
          and appid = #{appid}
          <if test="tappid != null and tappid != ''">
            and tappid = #{tappid}
          </if>
          and b.platform = #{platform}
        ORDER BY createTime desc
    </select>

    <delete id="deleteCSJModule">
        delete from dnwx_cfg.adv_adcode_module_headline_info
        where id = #{id}
    </delete>
    <delete id="deleteAdsidDtos">
        delete from dnwx_cfg.dn_online_adpos_status
        where platform = #{platform}
    </delete>
</mapper>