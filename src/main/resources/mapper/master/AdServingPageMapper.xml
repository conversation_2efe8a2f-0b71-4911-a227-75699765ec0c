<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.AdServingPageMapper">
    <!-- 投放页配置相关-->
    <select id="selectAdServingPage" resultType="com.wbgame.pojo.AdServingPageConfigVo" parameterType="com.wbgame.pojo.AdServingPageConfigVo">
        select  * from adv_adserving_config where 1=1
        <if test="productName != null and productName != ''">
            and  productName like '%${productName}%'
        </if>
        <if test="domain != null and domain != ''">
            and  `domain` = #{domain}
        </if>
        order by createtime desc
    </select>

    <insert id="saveAdServingPage" parameterType="com.wbgame.pojo.AdServingPageConfigVo">
        insert into adv_adserving_config (`domain`,icon,productName,dominant,productDesc,downloadUrl,version,packageSize,pageUrl,cuser,createtime)
        values (#{domain},#{icon},#{productName},#{dominant},#{productDesc},#{downloadUrl},#{version},#{packageSize},#{pageUrl},#{cuser},NOW())
    </insert>

    <update id="updateAdServingPage" parameterType="com.wbgame.pojo.AdServingPageConfigVo">
        update adv_adserving_config set `domain`= #{domain},icon = #{icon},productName = #{productName},dominant = #{dominant},productDesc =#{productDesc},
        downloadUrl = #{downloadUrl},version = #{version},packageSize = #{packageSize},pageUrl = #{pageUrl},euser = #{euser},endtime = NOW()
        where id = #{id}
    </update>

    <!-- 投放页域名下拉配置相关-->
    <select id="selectAdServingCompanyPage" resultType="com.wbgame.pojo.AdSeringCompanyPageVo" parameterType="com.wbgame.pojo.AdSeringCompanyPageVo">
        select  * from  adv_adservingdomain_config where state =1
    </select>

    <insert id="saveAdServingCompany" parameterType="com.wbgame.pojo.AdSeringCompanyPageVo">
        insert adv_adservingdomain_config (`domain`,url,companyName,templePage,ftpDir,cuser,createtime,state,ftpAddress,ftpAccount,ftpPwd)
        values (#{domain},#{url},#{companyName},#{templePage},#{ftpDir},#{cuser},NOW(),1,#{ftpAddress},#{ftpAccount},#{ftpPwd})
    </insert>


    <update id="updateAdServingCompany" parameterType="com.wbgame.pojo.AdSeringCompanyPageVo">
        update adv_adservingdomain_config set `domain` = #{domain},companyName = #{companyName},url = #{url},
            templePage = #{templePage},ftpDir=#{ftpDir},euser = #{euser},endtime = NOW()
            <if test="ftpAddress != null">
                ,ftpAddress = #{ftpAddress}
            </if>
            <if test="ftpAccount != null">
                ,ftpAccount = #{ftpAccount}
            </if>
            <if test="ftpPwd != null">
                ,ftpPwd = #{ftpPwd}
            </if>
        where id = #{id}
    </update>



    <!--官网产品配置相关-->
    <select id="selectAdServingProductList" parameterType="com.wbgame.pojo.AdServingProductConfigVo" resultType="com.wbgame.pojo.AdServingProductConfigVo">
        select * from adv_adserving_product_config where 1=1
        <if test="productName != null and productName != ''">
            and  productName like '%${productName}%'
        </if>
        <if test="domain != null and domain != ''">
            and  `domain` = #{domain}
        </if>
        <if test="id != null and id != ''">
            and id in (${id})
        </if>
        order by createtime desc
    </select>

    <insert id="saveAdServingProduct" parameterType="com.wbgame.pojo.AdServingProductConfigVo">
        insert into adv_adserving_product_config (sort,`domain`,icon,productName,productDesc,downloadUrl,productImgs,mark,statu,cuser,
        createtime,iosPkgName,androidPkgName,iosDownloadUrl,appid,updateDate,lastVersion,company)
        values (#{sort},#{domain},#{icon},#{productName},#{productDesc},#{downloadUrl},#{productImgs},#{mark},#{statu},#{cuser},
        NOW(),#{iosPkgName},#{androidPkgName},#{iosDownloadUrl},#{appid},#{updateDate},#{lastVersion},#{company})
    </insert>

    <update id="updateAdServingProduct" parameterType="com.wbgame.pojo.AdServingProductConfigVo">
        update adv_adserving_product_config set sort =#{sort}, `domain`= #{domain},icon = #{icon},productName = #{productName},productDesc =#{productDesc},
        downloadUrl = #{downloadUrl},productImgs = #{productImgs},mark=#{mark},euser = #{euser},statu=#{statu},company = #{company},
        iosPkgName=#{iosPkgName},androidPkgName=#{androidPkgName},iosDownloadUrl=#{iosDownloadUrl},appid=#{appid},updateDate=#{updateDate},lastVersion=#{lastVersion},
        endtime = NOW()
        where id = #{id}
    </update>

    <update id="batchUpdateServingProductStatus" parameterType="com.wbgame.pojo.AdServingProductConfigVo">
        update adv_adserving_product_config set statu =#{statu},euser = #{euser},endtime = NOW() where  id in (${id})
    </update>

    <insert id="batchSaveAdServingProduct" parameterType="java.util.List">
        insert into adv_adserving_product_config (sort,`domain`,icon,productName,productDesc,downloadUrl,productImgs,mark,
        statu,cuser,createtime,euser,endtime,iosPkgName,androidPkgName,iosDownloadUrl,appid,updateDate,lastVersion,company)
        values
        <foreach collection="list" item="li" separator=",">
            (
            #{li.sort},
            #{li.domain},
            #{li.icon},
            #{li.productName},
            #{li.productDesc},
            #{li.downloadUrl},
            #{li.productImgs},
            #{li.mark},
            #{li.statu},
            #{li.cuser},
            #{li.createtime},
            #{li.euser},
            #{li.endtime},
            #{li.iosPkgName},
            #{li.androidPkgName},
            #{li.iosDownloadUrl},
            #{li.appid},
            #{li.updateDate},
            #{li.lastVersion},
            #{li.company}
            )
        </foreach>
    </insert>

    <delete id="delAdServingProduct" parameterType="com.wbgame.pojo.AdServingProductConfigVo">
        delete from  adv_adserving_product_config where id in (${id})
    </delete>


</mapper>