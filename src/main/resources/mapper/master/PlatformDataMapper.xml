<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.PlatformDataMapper">

    <select id="getPlatformAppInfoList" parameterType="com.wbgame.pojo.adv2.PlatformAppInfoVo" resultType="com.wbgame.pojo.adv2.PlatformAppInfoVo">
        select
        a.id id,
        a.appid,
        c.name app_category,
        a.tappid,
        a.tappname,
        a.taccount,
        a.platform,
        a.channel,
        a.shopId,
        a.packagename,
        a.bindEndTime,
        a.version,
        a.state,
        a.syncState,
        a.syncTime,
        a.createTime,
        a.createUser,
        a.modifyTime,
        a.modifyUser,
        a.spend_channel,
        a.stateTime,
        a.refuse_reason,
        b.cp,
        a.vivo_media_id,
        a.sign_date,
        a.appid_tag,
        b.app_name appname,
        a.putUser,
        a.app_store_sale
        from  adv_platform_app_info a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        where  1=1
        <if test="appid != null and appid != ''">
            and a.appid in (${appid})
        </if>
        <if test="putUser != null and putUser != ''">
            and a.putUser in (${putUser})
        </if>
        <if test="app_category != null and app_category != ''">
            and b.app_category in (${app_category})
        </if>
        <if test="platform != null and platform != ''">
            and a.platform =#{platform}
        </if>
        <if test="channel != null and channel != ''">
            and a.channel in (${channel})
        </if>
        <if test="taccount != null and taccount != ''">
            and a.taccount =#{taccount}
        </if>
        <if test="tappname != null and tappname != ''">
            and a.tappname like '%${tappname}%'
        </if>
        <if test="state != null and state != ''">
            and a.state in (${state})
        </if>
        <if test="shopId != null and shopId != ''">
            and a.shopId =#{shopId}
        </if>
        <if test="tappid != null and tappid != ''">
            and a.tappid =#{tappid}
        </if>
        <if test="syncState != null and syncState != ''">
            and a.syncState =#{syncState}
        </if>
        <if test="packagename != null and packagename != ''">
            and a.packagename =#{packagename}
        </if>
        <if test="cp != null and cp != ''">
            and b.cp like '%${cp}%'
        </if>
        <if test="start_state_time != null and start_state_time != ''">
            and Date(stateTime) between #{start_state_time} and #{end_state_time}
        </if>
        <if test="vivo_media_id != null and vivo_media_id != ''">
            and a.vivo_media_id like '%${vivo_media_id}%'
        </if>
        <if test="app_store_sale != null and app_store_sale != ''">
            and a.app_store_sale in (${app_store_sale})
        </if>

        <if test="appid_tag != null and appid_tag != ''">
            and (
                <if test="appid_tag_rev != null and appid_tag_rev != ''">
                    (appid_tag is null) or not
                </if>
                <choose>
                    <when test="appid_tag.contains('all') ">
                        (IFNULL(appid_tag,'') != '')
                    </when>
                    <otherwise>
                        <foreach collection="appid_tag.split(',')" item="it" open="(" separator=" or " close=")">
                            (FIND_IN_SET(#{it},appid_tag) > 0)
                        </foreach>
                    </otherwise>
                </choose>
            )
        </if>
        order by a.createTime desc
    </select>

    <select id="getPlatformAppInfoListById" resultType="com.wbgame.pojo.adv2.PlatformAppInfoVo">
        select  * from  adv_platform_app_info where id = #{id}
    </select>

    <insert id="savePlatformAppInfo" parameterType="com.wbgame.pojo.adv2.PlatformAppInfoVo">
        insert into adv_platform_app_info (appid,tappid,taccount,tappname,packagename,platform,channel,shopId,bindEndTime,createUser,createTime,spend_channel,vivo_media_id,sign_date,putUser) values
        (#{appid},#{tappid},#{taccount},#{tappname},#{packagename},#{platform},#{channel},#{shopId},#{bindEndTime},#{createUser},now(),#{spend_channel},#{vivo_media_id},#{sign_date},#{putUser})
    </insert>

    <update id="updatePlatformAppInfo" parameterType="com.wbgame.pojo.adv2.PlatformAppInfoVo">
        update adv_platform_app_info set appid =#{appid},tappid =#{tappid},taccount =#{taccount},
            channel =#{channel},tappname =#{tappname},packagename=#{packagename},platform =#{platform},
            shopId=#{shopId},bindEndTime=#{bindEndTime},modifyUser =#{modifyUser},modifyTime =now(),spend_channel=#{spend_channel},
            vivo_media_id = #{vivo_media_id},sign_date = #{sign_date},putUser = #{putUser}
        where id =#{id}
    </update>

    <delete id="delPlatformAppInfo" parameterType="com.wbgame.pojo.adv2.PlatformAppInfoVo">
        delete from adv_platform_app_info where id =#{id}
    </delete>

    <update id="batchDelAppidTag">
        update adv_platform_app_info
            set appid_tag=TRIM(BOTH ',' FROM REPLACE(CONCAT(',', appid_tag, ','), (',${appid_tag},'), ','))
        where id in (${id});
    </update>

    <update id="batchUpdateAppidTag">
        update adv_platform_app_info set appid_tag = TRIM(BOTH ',' FROM REPLACE(CONCAT(',', appid_tag, ','), CONCAT(',', #{old_appid_tag}, ','), CONCAT(',', #{appid_tag}, ',')))
        where id in (${id});
    </update>


    <update id="batchAddAppidTag" parameterType="com.wbgame.pojo.adv2.PlatformAppInfoVo">
        update adv_platform_app_info set appid_tag=IF(appid_tag is null or appid_tag='',#{appid_tag},CONCAT(appid_tag,',',#{appid_tag}))
        where id in (${id}) and (FIND_IN_SET(#{appid_tag},IFNULL(appid_tag,'')) &lt;= 0)
    </update>
    <update id="batchUpdatePlatformAppInfoAsAppidTag" parameterType="com.wbgame.pojo.adv2.PlatformAppInfoVo">
        <!-- 用于批量更新appid_tag -->
        <foreach collection="list" item="li" separator=";" >
            update adv_platform_app_info set appid_tag=IF(appid_tag is null or appid_tag='',#{li.appid_tag},CONCAT(appid_tag,',',#{li.appid_tag}))
            where packagename = #{li.packagename}
        </foreach>
    </update>

    <select id="selectPlatformAppInfoAsAppidTagList" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.PlatformAppInfoVo">
        select * from
        (
            <foreach collection="array" item="it" separator=" union all ">
                SELECT #{it} appid_tag,appid,channel,packagename FROM adv_platform_app_info
                where (FIND_IN_SET(#{it},appid_tag) > 0)
            </foreach>
        ) xx
        where 1=1
        <if test="appid_tag != null and appid_tag != ''">
            and (FIND_IN_SET(#{appid_tag},appid_tag) > 0)
        </if>
    </select>

    <insert id="savePlatformPageData" parameterType="java.util.List">
        replace into adv_platform_pagedata_info (tdate,appid,tappid,platform,net_download,update_download,xiliang,
        detail_netdownload,detail_transform,download_rate,install_rate,icon_click_rate,exposure_downCnt_ratio,icon_download_ratio,
        addnum,dau,avg_time,avg_duration,exposure,detail_view,exposure_click_ratio,total_download,
        pay_num,pay_total,pay_reg_num,pay_reg_total,pay_succ_ratio,pay_times,pay_first_total,keep1,keep3,keep7,keep14,keep30,kobe_coupon,first_pay_user_num,
        avg_expose_trans,avg_detail_trans,top_expose_trans,top_detail_trans,avg_icon_click,avg_detail_personal,new_user_avg_duration)
        values
        <foreach collection="list"  index="index" item="item" separator=",">
            (#{item.tdate},#{item.appid},#{item.tappid},#{item.platform},#{item.net_download},#{item.update_download},#{item.xiliang},
            #{item.detail_netdownload},#{item.detail_transform},#{item.download_rate},
            #{item.install_rate},#{item.icon_click_rate},#{item.exposure_downCnt_ratio},#{item.icon_download_ratio},#{item.addnum},#{item.dau},#{item.avg_time},
            #{item.avg_duration},#{item.exposure},#{item.detail_view},#{item.exposure_click_ratio},#{item.total_download},
            #{item.pay_num},#{item.pay_total},#{item.pay_reg_num},#{item.pay_reg_total},#{item.pay_succ_ratio},#{item.pay_times},#{item.pay_first_total},#{item.keep1},
            #{item.keep3},#{item.keep7},#{item.keep14},#{item.keep30},#{item.kobe_coupon},#{item.first_pay_user_num},
            #{item.avg_expose_trans},#{item.avg_detail_trans},#{item.top_expose_trans},#{item.top_detail_trans},#{item.avg_icon_click},#{item.avg_detail_personal},#{item.new_user_avg_duration})
        </foreach>
    </insert>

    <update id="updatePlatformAppOhterInfo" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update adv_platform_app_info
            <set>
                <if test="item.state != null and item.state != ''">state = #{item.state},</if>
                <if test="item.refuse_reason != null and item.refuse_reason != ''">refuse_reason = #{item.refuse_reason},</if>
                <if test="item.version != null and item.version != ''">version = #{item.version},</if>
                <if test="item.syncState != null and item.syncState != ''">syncState = #{item.syncState},</if>
                <if test="item.stateTime != null and item.stateTime != ''">stateTime = #{item.stateTime},</if>
                <if test="item.app_store_sale != null and item.app_store_sale != ''">app_store_sale = #{item.app_store_sale},</if>
                syncTime = now()
            </set>
            where tappid = #{item.tappid} and platform = #{item.platform} and taccount = #{item.taccount}
        </foreach>
    </update>

    <update id="updatePlatformAppInfoNotSuccessState" parameterType="java.lang.String">
        update adv_platform_app_info set syncState = #{syncState},syncTime = now()
        where taccount = #{taccount} and platform = #{platform}
    </update>

    <update id="updateChannelAppInfoNotSuccessState" parameterType="java.lang.String">
        update adv_platform_app_info set syncState = #{syncState},syncTime = now()
        where taccount = #{taccount} and channel = #{channel}
    </update>


    <update id="batchUpdateHuaweiChannel" parameterType="com.wbgame.pojo.adv2.PlatformAppInfoVo">
        <foreach collection="list" item="it">
            update adv_platform_app_info set spend_channel = #{it.spend_channel}
            where tappid = #{it.tappid};
        </foreach>
    </update>

    <insert id="savePlatformGradeData" parameterType="java.util.List">
        replace into adv_platform_gradedata_info (tdate,appid,tappid,platform,total_grade,total_grade_times,star1,star2,star3,star4,star5)
        values
        <foreach collection="list"  index="index" item="item" separator=",">
            (#{item.tdate},#{item.appid},#{item.tappid},#{item.platform},#{item.total_grade},#{item.total_grade_times},#{item.star1},
            #{item.star2},#{item.star3},#{item.star4},#{item.star5})
        </foreach>
    </insert>


    <select id="getPlatformPageDataList" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.PlatformPageDataVo">
        select
        tdate,a.appid,a.tappid,a.platform,
        avg(avg_time) avg_time,
        SEC_TO_TIME(round(avg(IFNULL(new_user_avg_duration,0)),0)) new_user_avg_duration,
        IFNULL(
            ROUND(SUM(SUBSTRING_INDEX(avg_duration,':',1)*3600
            +SUBSTRING_INDEX (SUBSTRING_INDEX(avg_duration,':',2),':',-1)*60
            +SUBSTRING_INDEX(avg_duration,':',-1))/count(if(avg_duration!='',true,null)),0),0) avg_duration,
        SUM(addnum) addnum,
        SUM(dau) dau,
        SUM(net_download) net_download,
        SUM(detail_netdownload) detail_netdownload,
        AVG(detail_transform) detail_transform,
        IF(a.platform='oppo' or a.platform='xiaomi',AVG(a.download_rate),(IFNULL(SUM(download_rate*total_download)/SUM(total_download),0))) download_rate,
        AVG(icon_click_rate) icon_click_rate,
        IF(a.platform='oppo' or a.platform='xiaomi',AVG(a.install_rate),IFNULL(SUM(install_rate*net_download)/SUM(net_download),0)) install_rate,
        SUM(update_download) update_download,
        AVG(xiliang) xiliang,
        SUM(exposure) exposure,
        SUM(detail_view) detail_view,
        AVG(exposure_click_ratio) exposure_click_ratio,
        AVG(exposure_downCnt_ratio) exposure_downCnt_ratio,
        AVG(icon_download_ratio) icon_download_ratio,
        SUM(IF(a.platform='xiaomi',a.total_download,net_download+update_download)) total_download,

        SUM(pay_num) pay_num,
        ROUND(SUM(pay_total),2) pay_total,
        SUM(pay_reg_num) pay_reg_num,
        SUM(pay_reg_total) pay_reg_total,
        SUM(pay_times) pay_times,
        SUM(first_pay_user_num) first_pay_user_num,
        SUM(kobe_coupon) kobe_coupon,
        ROUND(SUM(pay_first_total),2) pay_first_total,
        AVG (pay_succ_ratio) pay_succ_ratio,
        SUM(pay_num)/SUM(dau) pay_dau_permeate_ratio,
        SUM(pay_total)/SUM(dau) arpu,
        SUM(pay_total)/SUM(pay_num) arppu,
        AVG(keep1) keep1,
        AVG(keep3) keep3,
        AVG(keep7) keep7,
        AVG(keep14) keep14,
        AVG(keep30) keep30,
        AVG(avg_expose_trans) avg_expose_trans,
        AVG(avg_detail_trans) avg_detail_trans,
        AVG(top_expose_trans) top_expose_trans,
        AVG(top_detail_trans) top_detail_trans,
        AVG(avg_icon_click) avg_icon_click,
        AVG(avg_detail_personal) avg_detail_personal,

        b.channel,b.tappname,b.taccount from  adv_platform_pagedata_info a
        left join adv_platform_app_info b on a.appid = b.appid and a.platform =b.platform and a.tappid = b.tappid and a.tdate &lt;= b.bindEndTime
        left join app_channel_config c on b.platform =c.channel and b.taccount = c.account
        where  1=1
        <if test="appid != null and appid != ''">
            and a.appid in (${appid})
        </if>
        <if test="tappid != null and tappid != ''">
            and a.tappid = #{tappid}
        </if>
        <if test="platform != null and platform != ''">
            and a.platform =#{platform}
        </if>
        <if test="channel != null and channel != ''">
            and b.channel in (${channel})
        </if>
        <if test="tappname != null and tappname != ''">
            and b.tappname like concat('%',#{tappname},'%')
        </if>

        <if test="taccount != null and taccount != ''">
            and b.taccount = #{taccount}
        </if>
        and tdate BETWEEN #{startTime} AND #{endTime}
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc ,addnum+0 desc
            </otherwise>
        </choose>

    </select>


    <select id="getPlatformPageDataSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.PlatformPageDataVo">
        select
        avg(avg_time) avg_time,
        SEC_TO_TIME(round(avg(IFNULL(new_user_avg_duration,0)),0)) new_user_avg_duration,
        IFNULL(
        ROUND(SUM(SUBSTRING_INDEX(avg_duration,':',1)*3600
        +SUBSTRING_INDEX (SUBSTRING_INDEX(avg_duration,':',2),':',-1)*60
        +SUBSTRING_INDEX(avg_duration,':',-1))/count(if(avg_duration!='',true,null)),0),0) avg_duration,
        SUM(addnum) addnum,
        SUM(dau) dau,
        SUM(net_download) net_download,
        SUM(detail_netdownload) detail_netdownload,
        AVG(detail_transform) detail_transform,
        IF(a.platform='oppo' or a.platform='xiaomi',AVG(a.download_rate),(IFNULL(SUM(download_rate*total_download)/SUM(total_download),0))) download_rate,
        AVG(icon_click_rate) icon_click_rate,
        IF(a.platform='oppo' or a.platform='xiaomi',AVG(a.install_rate),IFNULL(SUM(install_rate*net_download)/SUM(net_download),0)) install_rate,
        SUM(update_download) update_download,
        AVG(xiliang) xiliang,
        SUM(exposure) exposure,
        SUM(detail_view) detail_view,
        AVG(exposure_click_ratio) exposure_click_ratio,
        AVG(exposure_downCnt_ratio) exposure_downCnt_ratio,
        AVG(icon_download_ratio) icon_download_ratio,
        SUM(IF(a.platform='xiaomi',a.total_download,net_download+update_download)) total_download,

        SUM(pay_num) pay_num,
        ROUND(SUM(pay_total),2) pay_total,
        SUM(pay_reg_num) pay_reg_num,
        SUM(pay_reg_total) pay_reg_total,
        SUM(pay_times) pay_times,
        SUM(first_pay_user_num) first_pay_user_num,
        SUM(kobe_coupon) kobe_coupon,
        ROUND(SUM(pay_first_total),2) pay_first_total,
        AVG (pay_succ_ratio) pay_succ_ratio,
        SUM(pay_num)/SUM(dau) pay_dau_permeate_ratio,
        SUM(pay_total)/SUM(dau) arpu,
        SUM(pay_total)/SUM(pay_num) arppu,
        AVG(keep1) keep1,
        AVG(keep3) keep3,
        AVG(keep7) keep7,
        AVG(keep14) keep14,
        AVG(keep30) keep30,
        AVG(avg_expose_trans) avg_expose_trans,
        AVG(avg_detail_trans) avg_detail_trans,
        AVG(top_expose_trans) top_expose_trans,
        AVG(top_detail_trans) top_detail_trans,
        AVG(avg_icon_click) avg_icon_click,
        AVG(avg_detail_personal) avg_detail_personal
        from  adv_platform_pagedata_info a
        left join adv_platform_app_info b on a.appid = b.appid and a.platform =b.platform and a.tappid = b.tappid
        left join app_channel_config c on b.platform =c.channel and b.taccount = c.account
        where  1=1
        <if test="appid != null and appid != ''">
            and a.appid in (${appid})
        </if>
        <if test="tappid != null and tappid != ''">
            and a.tappid = #{tappid}
        </if>
        <if test="platform != null and platform != ''">
            and a.platform =#{platform}
        </if>
        <if test="channel != null and channel != ''">
            and b.channel in (${channel})
        </if>
        <if test="tappname != null and tappname != ''">
            and b.tappname like concat('%',#{tappname},'%')
        </if>
        <if test="taccount != null and taccount != ''">
            and b.taccount = #{taccount}
        </if>
        and tdate BETWEEN #{startTime} AND #{endTime}
    </select>




    <select id="getPlatformGradeDataList" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.PlatformGradeDataVo">
        select a.*,b.channel,b.tappname,b.taccount from adv_platform_gradedata_info a
        left join adv_platform_app_info b on a.appid = b.appid and a.platform =b.platform and a.tappid = b.tappid
        left join app_channel_config c on b.platform =c.channel and b.taccount = c.account
        where 1=1
        <if test="appid != null and appid != ''">
            and a.appid in (${appid})
        </if>
        <if test="tappid != null and tappid != ''">
            and a.tappid = #{tappid}
        </if>
        <if test="platform != null and platform != ''">
            and a.platform =#{platform}
        </if>
        <if test="channel != null and channel != ''">
            and b.channel in (${channel})
        </if>
        <if test="tappname != null and tappname != ''">
            and b.tappname like concat('%',#{tappname},'%')
        </if>
        <if test="taccount != null and taccount != ''">
            and b.taccount = #{taccount}
        </if>
        and DATE(tdate) BETWEEN #{startTime} AND #{endTime}
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc
            </otherwise>
        </choose>
    </select>

    <select id="getPlatformHwConfigList"  resultType="com.wbgame.pojo.adv2.PlatformHwConfigVo">
        select  * from  adv_hw_platform_account
    </select>

    <select id="queryPlatformHwConfig" resultType="com.wbgame.pojo.adv2.PlatformHwConfigVo">
        select  * from  adv_hw_platform_account where dn_account = #{account}
    </select>

    <insert id="savePlatformReservationData" parameterType="java.util.List">
        replace into adv_platform_reservationdata_info (tdate,appid,tappid,platform,details_pv,details_uv,details_view,
        preorder_users_today,preorder_users_total,preorder_users_cancel,preorder_success_rate,preorder_hot,
        preorder_exposure_rate,exposure_rate_middle_value,preorder_rate_middle_value,icon_click_rate)
        values
        <foreach collection="list"  index="index" item="item" separator=",">
            (
                #{item.tdate},#{item.appid},#{item.tappid},#{item.platform},
                #{item.details_pv},#{item.details_uv},#{item.details_view},
                #{item.preorder_users_today},#{item.preorder_users_total},#{item.preorder_users_cancel},
                #{item.preorder_success_rate},#{item.preorder_hot},#{item.preorder_exposure_rate},
                #{item.exposure_rate_middle_value},#{item.preorder_rate_middle_value},#{item.icon_click_rate}
            )
        </foreach>
    </insert>

    <select id="getPlatformReservationDataList" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.platform.PlatformReservationDataVo">
        select
        IFNULL(SUM(details_pv),0) details_pv,
        IFNULL(SUM(details_uv),0) details_uv,
        IFNULL(SUM(details_view),0) details_view,
        IFNULL(SUM(preorder_users_today),0) preorder_users_today,
        IFNULL(SUM(preorder_users_total),0) preorder_users_total,
        IFNULL(SUM(preorder_users_cancel),0) preorder_users_cancel,
        IFNULL(SUM(preorder_hot),0) preorder_hot,
        IFNULL(AVG(exposure_rate_middle_value),0) exposure_rate_middle_value,
        IFNULL(AVG(preorder_rate_middle_value),0) preorder_rate_middle_value,
        IFNULL(AVG(preorder_success_rate),0) preorder_success_rate,
        IFNULL(AVG(preorder_exposure_rate),0) preorder_exposure_rate,
        IFNULL(AVG(icon_click_rate),0) icon_click_rate,
        tdate,a.appid,a.tappid,a.platform,b.channel,b.tappname,b.taccount
        from  adv_platform_reservationdata_info a
        left join adv_platform_app_info b on a.appid = b.appid and a.platform =b.platform and a.tappid = b.tappid
        left join app_channel_config c on b.platform =c.channel and b.taccount = c.account
        where  1=1
        <if test="appid != null and appid != ''">
            and a.appid in (${appid})
        </if>
        <if test="tappid != null and tappid != ''">
            and a.tappid = #{tappid}
        </if>
        <if test="platform != null and platform != ''">
            and a.platform =#{platform}
        </if>
        <if test="channel != null and channel != ''">
            and b.channel in (${channel})
        </if>
        <if test="tappname != null and tappname != ''">
            and b.tappname like concat('%',#{tappname},'%')
        </if>

        <if test="taccount != null and taccount != ''">
            and b.taccount = #{taccount}
        </if>
        and DATE(tdate) BETWEEN #{startTime} AND #{endTime}
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc ,preorder_users_total+0 desc
            </otherwise>
        </choose>

    </select>

    <select id="getPlatformReservationDataSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.platform.PlatformReservationDataVo">
        select
        IFNULL(SUM(details_pv),0) details_pv,
        IFNULL(SUM(details_uv),0) details_uv,
        IFNULL(SUM(details_view),0) details_view,
        IFNULL(SUM(preorder_users_today),0) preorder_users_today,
        IFNULL(SUM(preorder_users_total),0) preorder_users_total,
        IFNULL(SUM(preorder_users_cancel),0) preorder_users_cancel,
        IFNULL(SUM(preorder_hot),0) preorder_hot,
        IFNULL(AVG(exposure_rate_middle_value),0) exposure_rate_middle_value,
        IFNULL(AVG(preorder_rate_middle_value),0) preorder_rate_middle_value,
        IFNULL(round(AVG(preorder_success_rate),2),0) preorder_success_rate,
        IFNULL(AVG(preorder_exposure_rate),0) preorder_exposure_rate,
        IFNULL(AVG(icon_click_rate),0) icon_click_rate,
        tdate,a.appid,a.tappid,a.platform,b.channel,b.tappname,b.taccount
        from  adv_platform_reservationdata_info a
        left join adv_platform_app_info b on a.appid = b.appid and a.platform =b.platform and a.tappid = b.tappid
        left join app_channel_config c on b.platform =c.channel and b.taccount = c.account
        where  1=1
        <if test="appid != null and appid != ''">
            and a.appid in (${appid})
        </if>
        <if test="tappid != null and tappid != ''">
            and a.tappid = #{tappid}
        </if>
        <if test="platform != null and platform != ''">
            and a.platform =#{platform}
        </if>
        <if test="channel != null and channel != ''">
            and b.channel in (${channel})
        </if>
        <if test="tappname != null and tappname != ''">
            and b.tappname like concat('%',#{tappname},'%')
        </if>

        <if test="taccount != null and taccount != ''">
            and b.taccount = #{taccount}
        </if>
        and DATE(tdate) BETWEEN #{startTime} AND #{endTime}
    </select>

    <insert id="insertHwPlatformDataConfig" parameterType="com.wbgame.pojo.mobile.hw.HwCommonInfoVo">
        insert into adv_platform_app_info (appid,tappid,taccount,tappname,packagename,platform,channel,shopId,bindEndTime,createUser,createTime,spend_channel) values
        (#{appid},#{tappid},#{taccount},#{tappname},#{packagename},#{platform},#{channel},#{shopId},#{bindEndTime},#{createUser},now(),#{spend_channel})
    </insert>

    <insert id="insertHwLoginConfig" parameterType="com.wbgame.pojo.mobile.hw.HwCommonInfoVo">
        insert into hw_pay_param(appid,pkg,hwappid,flag,createtime)
        values (#{appid},#{packagename},#{tappid},#{flag},now())
    </insert>

    <insert id="insertHwAppConfig" parameterType="com.wbgame.pojo.mobile.hw.HwCommonInfoVo">
        insert into hw_app_info (appid,tappid,tappname,taccount,taccountName,packageName,createUser,createTime)
        value
        (#{appid},#{tappid},#{tappname},#{hwTaccount},#{hwTaccountName},#{packagename},#{createUser},now())
    </insert>


    <insert id="savePlatformMsgList" parameterType="java.util.List">
        INSERT IGNORE adv_platform_msg_info(
            platform,
            account,
            company,
            contentId,
            title,
            content,
            tdate,
            createtime,
            tappname,
            tappid
        ) values
        <foreach collection="list"  index="index" item="li" separator=",">
            (#{li.platform},
            #{li.account},
            #{li.company},
            #{li.contentId},
            #{li.title},
            #{li.content},
            #{li.tdate},
            #{li.createtime},
            #{li.tappname},
            #{li.tappid})
        </foreach>

    </insert>

    <select id="selectPlatformMsgInfoList" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.platform.PlatformMsgInfoVo">
        SELECT
            platform,
            account,
            company,
            contentId,
            content,
            title,
            tdate,
            createtime,
            tappname,
            tappid,
            counts wg_count
        FROM adv_platform_msg_info a left join adv_platform_cumul_counts c  on a.tappid=c.pappid
        where tdate BETWEEN #{sdate} AND #{edate}
        and title != '【封禁通知】违规封禁通知'
        and title not like '%广告位作弊%'
        <if test="platform != null and platform != ''">
            and platform = #{platform}
        </if>
        <if test="company != null and company != ''">
            and company = #{company}
        </if>
        <if test="account != null and account != ''">
            and account = #{account}
        </if>
        <if test="tappname != null and tappname != ''">
            and tappname = #{tappname}
        </if>
        <if test="tappid != null and tappid != ''">
            and tappid = #{tappid}
        </if>
        <if test="wg_count != null and wg_count != ''">
            and counts = #{wg_count}
        </if>
        <if test="title != null and title != ''">
            and title like concat('%',#{title},'%')
        </if>
        <if test="content != null and content != ''">
            and content like concat('%',#{content},'%')
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                ORDER BY ${order_str}
            </when>

            <otherwise>
                ORDER BY platform asc,account asc,createtime desc
            </otherwise>
        </choose>
    </select>


    <insert id="saveVivoQuickGameList" parameterType="java.util.List">
        REPLACE INTO vivo_quick_game_info(
            tdate,
            account,
            company,
            tappname,
            tpackage,
            tappid,
            new_user,
            day_active_user,
            act_arpu,
            revenue,
            new_uv_avg,
            act_uv_avg,
            pay_user,
            pay_income,
            pay_arpu,
            pay_arppu,
            `view`,
            click,
            click_ratio,
            ecpm,
            cpc,
            ad_income,
            ad_arpu,
            createtime,
            cuser
            ,start_up_number
,boot_uv_avg
,user_duration
,durat_avg
,exposure_rate
,ctr_avg
,collapse_rate
,black_screen_rate
,network_request_success_rate
,download_network_request_success_rate
,average_max_ram
,card_frame_rate
,stuck_rate
,download_success_rate
,start_up_success_rate
,appid
,app_name
        ) values
        <foreach collection="list"  index="index" item="li" separator=",">
            (#{li.tdate},
            #{li.account},
            #{li.company},
            #{li.tappname},
            #{li.tpackage},
            #{li.tappid},
            #{li.new_user},
            #{li.day_active_user},
            #{li.act_arpu},
            #{li.revenue},
            #{li.new_uv_avg},
            #{li.act_uv_avg},
            #{li.pay_user},
            #{li.pay_income},
            #{li.pay_arpu},
            #{li.pay_arppu},
            #{li.view},
            #{li.click},
            #{li.click_ratio},
            #{li.ecpm},
            #{li.cpc},
            #{li.ad_income},
            #{li.ad_arpu},
            now(),
            #{li.cuser}
            ,#{li.start_up_number}
,#{li.boot_uv_avg}
,#{li.user_duration}
,#{li.durat_avg}
,#{li.exposure_rate}
,#{li.ctr_avg}
,#{li.collapse_rate}
,#{li.black_screen_rate}
,#{li.network_request_success_rate}
,#{li.download_network_request_success_rate}
,#{li.average_max_ram}
,#{li.card_frame_rate}
,#{li.stuck_rate}
,#{li.download_success_rate}
,#{li.start_up_success_rate}
,#{li.appid}
,#{li.app_name})
        </foreach>
    </insert>

    <select id="selectVivoQuickGameList" parameterType="java.util.Map" resultType="java.util.Map">
        <include refid="selectVivoQuickGameListSql"/>
    </select>

    <select id="selectVivoQuickGameSum" parameterType="java.util.Map" resultType="java.util.Map">
        select
        sum(new_user) new_user
        ,sum(day_active_user) day_active_user
        ,concat(ifnull(round(sum(revenue)/sum(day_active_user),3),0), '') as act_arpu
        ,sum(revenue) revenue
        ,TRUNCATE(AVG(new_uv_avg),2) new_uv_avg
        ,TRUNCATE(AVG(act_uv_avg),2) act_uv_avg
        ,TRUNCATE(AVG(start_up_number),2) start_up_number
        ,TRUNCATE(AVG(boot_uv_avg),2) boot_uv_avg
        ,TRUNCATE(AVG(user_duration),2) user_duration
        ,TRUNCATE(AVG(durat_avg),2) durat_avg
        ,concat(TRUNCATE(AVG(exposure_rate),2),'%') exposure_rate
        ,concat(TRUNCATE(AVG(ctr_avg_rate),2),'%') ctr_avg_rate
        ,concat(TRUNCATE(AVG(next_day_left_rate),2),'%') next_day_left_rate
        ,concat(TRUNCATE(AVG(act_rtn_2_avg_rate),2),'%') act_rtn_2_avg_rate
        ,concat(TRUNCATE(AVG(three_day_left_rate),2),'%') three_day_left_rate
        ,concat(TRUNCATE(AVG(act_rtn_3_avg_rate),2),'%') act_rtn_3_avg_rate
        ,concat(TRUNCATE(AVG(seven_day_left_rate),2),'%') seven_day_left_rate
        ,concat(TRUNCATE(AVG(act_rtn_7_avg_rate),2),'%') act_rtn_7_avg_rate
        ,sum(pay_user) pay_user
        ,sum(pay_income) pay_income
        ,ifnull(TRUNCATE(sum(pay_income)/sum(day_active_user),2),0) pay_arpu
        ,ifnull(TRUNCATE(sum(pay_income)/sum(pay_user),2),0) pay_arppu
        ,sum(view) view
        ,sum(click) click
        ,concat(ifnull(TRUNCATE(sum(click)/sum(view),2),0),'%') click_rate
        ,ifnull(TRUNCATE(sum(ad_income)/sum(view)*1000,2),0) ecpm
        ,ifnull(TRUNCATE(sum(ad_income)/sum(click),2),0) cpc
        ,sum(ad_income) ad_income
        ,concat(ifnull(round(sum(ad_income)/sum(day_active_user),3),0), '') as ad_arpu
        ,concat(TRUNCATE(AVG(download_success_rate),2),'%') download_success_rate
        ,concat(TRUNCATE(AVG(start_up_success_rate),2),'%') start_up_success_rate
        ,concat(TRUNCATE(AVG(collapse_rate),2),'%') collapse_rate
        ,concat(TRUNCATE(AVG(black_screen_rate),2),'%') black_screen_rate
        ,concat(TRUNCATE(AVG(network_request_success_rate),2),'%') network_request_success_rate
        ,concat(TRUNCATE(AVG(download_network_request_success_rate),2),'%') download_network_request_success_rate
        ,TRUNCATE(AVG(average_max_ram),2) average_max_ram
        ,concat(TRUNCATE(AVG(card_frame_rate),2),'%') card_frame_rate
        ,concat(TRUNCATE(AVG(stuck_rate),2),'%') stuck_rate
        from (<include refid="selectVivoQuickGameListSql"/>) t
    </select>

    <sql id="selectVivoQuickGameListSql">
        SELECT tdate,account,company,tappname,tpackage,tappid,appid,app_name
        ,sum(new_user) new_user
        ,sum(day_active_user) day_active_user
        ,concat(ifnull(round(sum(revenue)/sum(day_active_user),3),0), '') as act_arpu
        ,sum(revenue) revenue
        ,TRUNCATE(AVG(new_uv_avg),2) new_uv_avg
        ,TRUNCATE(AVG(act_uv_avg),2) act_uv_avg
        ,TRUNCATE(AVG(start_up_number),2) start_up_number
        ,TRUNCATE(AVG(boot_uv_avg),2) boot_uv_avg
        ,TRUNCATE(AVG(user_duration),2) user_duration
        ,TRUNCATE(AVG(durat_avg),2) durat_avg
        ,TRUNCATE(AVG(exposure_rate),2) exposure_rate
        ,TRUNCATE(AVG(ctr_avg),2) ctr_avg_rate
        ,TRUNCATE(AVG(next_day_left_rate),2) next_day_left_rate
        ,TRUNCATE(AVG(act_rtn_2_avg),2) act_rtn_2_avg_rate
        ,TRUNCATE(AVG(three_day_left_rate),2) three_day_left_rate
        ,TRUNCATE(AVG(act_rtn_3_avg),2) act_rtn_3_avg_rate
        ,TRUNCATE(AVG(seven_day_left_rate),2) seven_day_left_rate
        ,TRUNCATE(AVG(act_rtn_7_avg),2) act_rtn_7_avg_rate
        ,sum(pay_user) pay_user
        ,sum(pay_income) pay_income
        ,ifnull(TRUNCATE(sum(pay_income)/sum(day_active_user),2),0) pay_arpu
        ,ifnull(TRUNCATE(sum(pay_income)/sum(pay_user),2),0) pay_arppu
        ,sum(view) view
        ,sum(click) click
        ,ifnull(TRUNCATE(sum(click)/sum(view),2),0) click_rate
        ,ifnull(TRUNCATE(sum(ad_income)/sum(view)*1000,2),0) ecpm
        ,ifnull(TRUNCATE(sum(ad_income)/sum(click),2),0) cpc
        ,sum(ad_income) ad_income
        ,concat(ifnull(round(sum(ad_income)/sum(day_active_user),3),0), '') as ad_arpu
        ,TRUNCATE(AVG(download_success_rate),2) download_success_rate
        ,TRUNCATE(AVG(start_up_success_rate),2) start_up_success_rate
        ,TRUNCATE(AVG(collapse_rate),2) collapse_rate
        ,TRUNCATE(AVG(black_screen_rate),2) black_screen_rate
        ,TRUNCATE(AVG(network_request_success_rate),2) network_request_success_rate
        ,TRUNCATE(AVG(download_network_request_success_rate),2) download_network_request_success_rate
        ,TRUNCATE(AVG(average_max_ram),2) average_max_ram
        ,TRUNCATE(AVG(card_frame_rate),2) card_frame_rate
        ,TRUNCATE(AVG(stuck_rate),2) stuck_rate

        FROM vivo_quick_game_info
        where tdate BETWEEN #{sdate} AND #{edate}
          and (day_active_user > 0 and new_user > 0)
        <if test="account != null and account != ''">
            and account = #{account}
        </if>
        <if test="tappid != null and tappid != ''">
            and tappid = #{tappid}
        </if>
        <if test="tpackage != null and tpackage != ''">
            and tpackage = #{tpackage}
        </if>
        <if test="tappname != null and tappname != ''">
            and tappname like concat('%',#{tappname},'%')
        </if>
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>

        <if test="group != null and group != ''">
            group by ${group}
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                ORDER BY ${order_str}
            </when>

            <otherwise>
                ORDER BY tdate asc,account asc,createtime desc
            </otherwise>
        </choose>
    </sql>

    <select id="selectPlatformBusinessInfo" parameterType="java.util.Map" resultType="java.util.Map">

        SELECT xx.* FROM
        (
            SELECT bb.taccount,cc.cname,bb.appid,bb.tappname,bb.platform,bb.tappid,bb.packagename,bb.channel,bb.version,
                IFNULL(dd.`status`,0) patch_status,dd.endtime,IFNULL(ee.counts,0) counts,ee.yg_date,IFNULL(gg.wudian,0) wudian
            FROM adv_platform_app_info bb
            LEFT JOIN app_channel_config cc ON bb.platform=cc.channel AND bb.taccount=cc.account
            LEFT JOIN (select pkg,appver,`status`,endtime from dn_game_patch_config where `status` != 0) dd
                ON bb.packagename=dd.pkg and bb.version=dd.appver
            LEFT JOIN (select platform,tappname,COUNT(1) counts,tdate yg_date from adv_platform_msg_info
                        where (tappname is not null and tappname != '')
                        group by platform,tappname order by tdate desc) ee
                ON bb.platform=ee.platform and bb.tappname=ee.tappname
            /*LEFT JOIN dnwx_client.wbgui_formconfig ff
                ON bb.packagename=ff.packageName and bb.version=ff.versionName*/

            LEFT JOIN
                 (select DISTINCT appid,'oppo' channel,'1' wudian from dnwx_cfg.dn_extend_adpos_manage
                  where adpos_type='plaque' and statu=1 and adstyle in (19,20,21)
                    and cha_id in ('oppo','oppo2','oppoml','opposd','opyy','oppomj','oppoyy')

                  union all

                  select DISTINCT appid,'vivo' channel,'1' wudian from dnwx_cfg.dn_extend_adpos_manage
                  where adpos_type='plaque' and statu=1 and adstyle in (10,11,12,13,14,15,16,17,18,19,20)
                    and cha_id in ('vivo','vivoml','vivosd','viyy','vivo2','vivoml2','vivomj')

                  union all

                  select DISTINCT appid,'xiaomi' channel,'1' wudian from dnwx_cfg.dn_extend_adpos_manage
                  where adpos_type='plaque' and statu=1 and adstyle in (10,11,12,13,14,15,16,17,18,19,20)
                    and cha_id in ('xiaomi','xiaomisd','xmyy','xiaomiml','xiaomimj','xiaomiwt')) gg
            ON bb.appid=gg.appid AND bb.channel=gg.channel

            LEFT JOIN app_info hh ON bb.appid = hh.id

           <where>
                <if test="platform != null and platform != ''">
                    and bb.platform = #{platform}
                </if>
                <if test="account != null and account != ''">
                    and bb.taccount = #{account}
                </if>
               <if test="appid != null and appid != ''">
                   and bb.appid in (${appid})
               </if>
               <if test="channel != null and channel != ''">
                   and bb.channel in (${channel})
               </if>
               <if test="app_category != null and app_category != ''">
                   and hh.app_category in (${app_category})
               </if>
               <if test="packagename != null and packagename != ''">
                   and bb.packagename = #{packagename}
               </if>
               <if test="version != null and version != ''">
                   and bb.version = #{version}
               </if>
           </where>) xx
        <where>
            <if test="patch_status != null and patch_status != ''">
                and xx.patch_status = #{patch_status}
            </if>
            <if test="wudian != null and wudian != ''">
                and xx.wudian = #{wudian}
            </if>
            <if test="counts != null and counts != ''">
                and xx.counts <![CDATA[ >= ]]> ${counts}
            </if>
        </where>

        <choose>
            <when test="order_str != null and order_str != ''">
                ORDER BY ${order_str}
            </when>

            <otherwise>
                ORDER BY xx.platform asc,xx.appid asc
            </otherwise>
        </choose>
    </select>

    <select id="getPlatformWarnSwitch" parameterType="java.lang.String" resultType="java.lang.String">
        select platform_sdk_switch from adv_platform_special_config where platform = #{platform}
    </select>
	<insert id="saveMsgCumulCounts" parameterType="java.util.Map">
            insert into adv_platform_cumul_counts(pappid)
        	values(#{pappid}) ON DUPLICATE KEY UPDATE counts=counts+1
    </insert>
     <select id="getMsgByMsgId" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(contentId) from adv_platform_msg_info where platform = #{platform}  and contentId=#{contentId}
    </select>
    <select id="getPlatformHonorConfigList" resultType="com.wbgame.pojo.adv2.PlatformHonorConfigVo">
        SELECT a.account, a.ttappid cash_account, b.client_id, b.client_secret
        FROM `app_channel_config` a
                 left join dnwx_adt.dn_honor_account  b
                           on a.ttappid = b.account
        where channel = 'honor'
    </select>
    <select id="channelTotalPlatformList" resultType="com.wbgame.pojo.adv2.platform.PlatformChannelVo">
        SELECT
            apai.channel,
            round(ifnull(sum(appi.kobe_coupon),0),2) kobeCoupon,
            appi.appid,
            <choose>
                <when test="param.custom_date != null and param.custom_date.size() > 0">
                    concat(#{param.start_date},'至',#{param.end_date}) as tdate
                </when>
                <when test="param.group != null and param.group != '' and param.group.contains('week')">
                    DATE_FORMAT(appi.tdate, '%x-%v') as tdate,DATE_FORMAT(appi.tdate, '%x-%v') week
                </when>
                <when test="param.group != null and param.group != '' and param.group.contains('month')">
                    DATE_FORMAT(appi.tdate,'%Y-%m') as tdate,
                    DATE_FORMAT(appi.tdate,'%Y-%m') as month
                </when>
                <when test="param.group == null or param.group == ''">
                    concat(#{param.start_date},'至',#{param.end_date}) as tdate
                </when>
                <when test="param.group != null and param.group != '' and param.group.contains('beek')">
                    CONCAT(DATE_FORMAT(appi.tdate, '%x'),"-", LPAD(FLOOR((DATE_FORMAT(appi.tdate, '%v') - 1) / 2) * 2 + 1,2,"0"),"-",LPAD(FLOOR((DATE_FORMAT(appi.tdate, '%v') - 1) / 2) * 2 + 2,2,"0"))  AS tdate
                    ,CONCAT(DATE_FORMAT(appi.tdate, '%x'),"-", LPAD(FLOOR((DATE_FORMAT(appi.tdate, '%v') - 1) / 2) * 2 + 1,2,"0"),"-",LPAD(FLOOR((DATE_FORMAT(appi.tdate, '%v') - 1) / 2) * 2 + 2,2,"0")) as beek
                </when>
                <when test="param.group != null and param.group != '' and param.group.contains('tdate')">
                    appi.tdate
                </when>
                <otherwise>
                    concat(#{param.start_date},'至',#{param.end_date}) as tdate
                </otherwise>
            </choose>
        FROM
            adv_platform_pagedata_info appi
                INNER JOIN adv_platform_app_info apai ON appi.appid = apai.appid
                AND appi.platform = apai.platform
                AND appi.tappid = apai.tappid
        WHERE
            appi.tdate BETWEEN  #{param.start_date} and #{param.end_date}
        <if test="param.appid != null and param.appid != ''">
            and appi.appid in (${param.appid})
        </if>
        <if test="param.channel != null and param.channel != ''">
            and apai.channel in (${param.channel})
        </if>
        GROUP BY
            apai.channel,
            appi.appid
        <if test="param.group != null and param.group != '' and param.group.contains('tdate')">
            ,appi.tdate
        </if>
        <if test="param.group != null and param.group != '' and param.group.contains('week')">
            ,DATE_FORMAT(appi.tdate, '%x-%v')
        </if>
        <if test="param.group != null and param.group != '' and param.group.contains('beek')">
            ,CONCAT(DATE_FORMAT(appi.tdate, '%x'),"-", LPAD(FLOOR((DATE_FORMAT(appi.tdate, '%v') - 1) / 2) * 2 + 1,2,"0"),"-",LPAD(FLOOR((DATE_FORMAT(appi.tdate, '%v') - 1) / 2) * 2 + 2,2,"0"))
        </if>
        <if test="param.group != null and param.group != '' and param.group.contains('month')">
            ,DATE_FORMAT(appi.tdate,'%Y-%m')
        </if>
        <if test="param.group == null or param.group == ''">
            ,concat(#{param.start_date},'至',#{param.end_date})
        </if>
        ORDER BY appi.tdate ASC
    </select>
    <select id="channelTotalPlatformCount" resultType="java.lang.String">
        SELECT
        round(ifnull(sum(appi.kobe_coupon),0),2) totalKobeCoupon
        FROM
        adv_platform_pagedata_info appi
        INNER JOIN adv_platform_app_info apai ON appi.appid = apai.appid
        AND appi.platform = apai.platform
        AND appi.tappid = apai.tappid
        WHERE
        appi.tdate BETWEEN  #{param.start_date} and #{param.end_date}
        <if test="param.appid != null and param.appid != ''">
            and appi.appid in (${param.appid})
        </if>
        <if test="param.channel != null and param.channel != ''">
            and apai.channel in (${param.channel})
        </if>
    </select>

    <update id="updatePlatformAppInfoBindEndTime">
        update adv_platform_app_info set bindEndTime = #{bindEndTime} where id = #{id}
    </update>

    <select id="queryPlatformAppInfoList" resultType="com.wbgame.pojo.adv2.PlatformAppInfoVo">
        select
        a.id id,
        a.appid,
        c.name app_category,
        a.tappid,
        a.tappname,
        a.taccount,
        d.cname,
        a.platform,
        a.channel,
        a.shopId,
        a.packagename,
        a.bindEndTime,
        a.version,
        a.state,
        a.syncState,
        a.syncTime,
        a.createTime,
        a.createUser,
        a.modifyTime,
        a.modifyUser,
        a.spend_channel,
        a.stateTime,
        a.refuse_reason,
        b.cp,
        a.vivo_media_id,
        a.sign_date,
        a.appid_tag,
        b.app_name appname,
        a.putUser
        from  adv_platform_app_info a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        left join app_channel_config d on a.taccount = d.account
        where bindEndTime > CURRENT_DATE()
        <if test="platform != null and platform != ''">
            and a.platform =#{platform}
        </if>
        <if test="tappidList != null and tappidList.size()>0">
            and a.tappid in
            <foreach collection="tappidList" separator="," item="tappid" open="(" close=")">
                #{tappid}
            </foreach>
        </if>
        order by a.createTime desc
    </select>

    <delete id="deleteAppPartnerList">
        <foreach collection="appidTags" separator=";" item="appid_tag">
            DELETE FROM adv_platform_appid_tag WHERE appid_tag = #{appid_tag}
        </foreach>
    </delete>

    <update id="updateAppidTag">
        <foreach collection="appidTags" separator=";" item="appid_tag">
        UPDATE adv_platform_app_info
            SET appid_tag = TRIM(BOTH ',' FROM REPLACE(REPLACE(CONCAT(',', appid_tag, ','), CONCAT(',',#{appid_tag},','), ','), ',,', ','))
        WHERE FIND_IN_SET(#{appid_tag}, appid_tag)
        </foreach>
    </update>

</mapper>