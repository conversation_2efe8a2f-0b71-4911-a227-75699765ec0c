<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.AdvertMapper">

    <select id="getMicGameRebateCostData" parameterType="java.lang.String" resultType="com.wbgame.pojo.advert.MicGameIncomeVo">
        select `day` tdate,app appid , TRUNCATE(SUM(rebateSpend),2) rebate_cost
        from  dn_report_spend_china_summary a inner join app_info b on a.app=b.id and b.app_category in (17,39,43,44,45,46,47)
        where  1=1 and `day` = #{tdate} group by tdate,app
    </select>

    <select id="getMicGamePayData" parameterType="java.lang.String" resultType="com.wbgame.pojo.advert.MicGameIncomeVo">
        select  a.appid appid,'${tdate}' tdate,
        TRUNCATE(sum(if(paytype='腾讯米大师支付',money,0)/100),2) mds_pay_income,
        TRUNCATE(sum(if(paytype='微信支付',money,0)/100),2) + TRUNCATE(sum(if(paytype='聚合支付',money,0)/100),2) wx_pay_income,
        TRUNCATE(sum(if(paytype='字节支付',money,0)/100),2) tt_pay_income,
        count(distinct ifnull(imei,'')) as pay_num,
        count(distinct if(paytype='腾讯米大师支付',ifnull(imei,''),null)) mds_pay_num,
        count(distinct if(paytype='微信支付',ifnull(imei,''),null)) + count(distinct if(paytype='聚合支付',ifnull(imei,''),null)) wx_pay_num,
        count(distinct if(paytype='字节支付',ifnull(imei,''),null)) tt_pay_num
        from wb_pay_info a inner join app_info b on a.appid=b.id and b.app_category in (17,39,43,44,45,46,47)
        where a.createtime BETWEEN '${tdate} 00:00:00' and '${tdate} 23:59:59' and orderstatus='SUCCESS' group by DATE(a.createtime), a.appid
    </select>

    <select id="getMicGameAdIncomeData" parameterType="java.lang.String" resultType="com.wbgame.pojo.advert.MicGameIncomeVo">
       select a.date tdate,a.dnappid appid,TRUNCATE(sum(revenue),2) ad_income
       from  dn_cha_cash_total a inner join  app_info b on a.dnappid = b.id and b.app_category in (17,39,43,44,45,46,47)
       where 1=1 and `date` =#{tdate} group by a.dnappid,a.date
    </select>

    <insert id="saveMicGameRegAndDauData" parameterType="java.util.List">
        insert into dn_micgame_revenue_total (tdate,appid,add_num,dau,pay_reg_user_num,android_order_cnt,ios_order_cnt) values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.tdate},#{item.appid},#{item.add_num},#{item.dau},#{item.pay_reg_user_num},#{item.android_order_cnt},#{item.ios_order_cnt}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        add_num = VALUES(add_num),
        dau = VALUES(dau),
        pay_reg_user_num = VALUES(pay_reg_user_num),
        android_order_cnt = VALUES(android_order_cnt),
        ios_order_cnt = VALUES(ios_order_cnt)
    </insert>

    <insert id="updateMicGameRebateCostData" parameterType="java.util.List">
        insert into dn_micgame_revenue_total (tdate,appid,rebate_cost) values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.tdate},#{item.appid},#{item.rebate_cost}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE rebate_cost = VALUES(rebate_cost)
    </insert>

    <insert id="updateMicGamePayData" parameterType="java.util.List">
        insert into dn_micgame_revenue_total (tdate,appid,mds_pay_income,wx_pay_income,tt_pay_income,pay_num,mds_pay_num,wx_pay_num,tt_pay_num) values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.tdate},#{item.appid},#{item.mds_pay_income},#{item.wx_pay_income},#{item.tt_pay_income},#{item.pay_num},#{item.mds_pay_num},#{item.wx_pay_num},#{item.tt_pay_num}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE mds_pay_income = VALUES(mds_pay_income),wx_pay_income = VALUES(wx_pay_income),tt_pay_income = VALUES(tt_pay_income),pay_num =values(pay_num),mds_pay_num =values(mds_pay_num),wx_pay_num =values(wx_pay_num),tt_pay_num =values(tt_pay_num)
    </insert>

    <update id="updateMicGameAdIncomeData" parameterType="java.util.List">
        insert into dn_micgame_revenue_total (tdate,appid,ad_income) values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.tdate},#{item.appid},#{item.ad_income}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE ad_income = VALUES(ad_income)
    </update>


    <select id="getMicGameIncomeDataList" parameterType="java.util.Map" resultType="com.wbgame.pojo.advert.MicGameIncomeVo">
        <include refid="dn_micgame_revenue_sql"/>
    </select>

    <select id="getMicGameIncomeDataListSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.advert.MicGameIncomeVo">
        select
        TRUNCATE(SUM(xx.rebate_cost),0) rebate_cost,
        SUM(xx.add_num)  add_num,
        TRUNCATE(SUM(xx.rebate_cost)/SUM(xx.add_num),2) cpa,
        SUM(xx.dau) dau,
        CONCAT(TRUNCATE(SUM(xx.add_num) / SUM(xx.dau)*100, 2),'%') reg_ratio,
        SUM(xx.pay_num) pay_num,
        SUM(xx.mds_pay_num) mds_pay_num,
        SUM(xx.wx_pay_num) wx_pay_num,
        SUM(xx.android_order_cnt) android_order_cnt,
        SUM(xx.ios_order_cnt) ios_order_cnt,
        SUM(xx.mds_pay_income) mds_pay_income,
        SUM(xx.wx_pay_income) wx_pay_income,
        SUM(xx.tt_pay_income) tt_pay_income,
        TRUNCATE(SUM(xx.mds_pay_income)+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income),0) pay_income_total,
        TRUNCATE(SUM(xx.mds_pay_income)+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income)+SUM(xx.ad_income),0) pre_profit,
        CONCAT(TRUNCATE(SUM(xx.pay_num) / SUM(xx.dau)*100, 2),'%') pay_ratio,
        TRUNCATE((SUM(xx.mds_pay_income)+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income))/SUM(xx.pay_num),2) pay_user_arpu,
        TRUNCATE(SUM(xx.ad_income)/SUM(xx.dau),2) ad_arpu,
        TRUNCATE(SUM(xx.ad_income),0) ad_income,
        TRUNCATE(SUM(xx.ad_revenue),0) ad_revenue,
        TRUNCATE(SUM(xx.pay_ad_revenue),0) pay_ad_revenue,
        TRUNCATE(SUM(xx.refund_revenue),0) refund_revenue,
        <choose>
            <when test="end_date != null and end_date &gt;= '2024-04-01'">
                TRUNCATE((SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income)*0.9)/SUM(xx.dau),2) pay_arpu,
                TRUNCATE((SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income)*0.9+SUM(xx.ad_income))/SUM(xx.dau),2) all_arpu,
                TRUNCATE(SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income)*0.9,0) pay_income,
                TRUNCATE(SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income)*0.9+SUM(xx.ad_income),0) all_income,
                TRUNCATE(SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income)*0.9+SUM(xx.ad_income)-SUM(xx.rebate_cost),0) profit,
                TRUNCATE(SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income)*0.9+SUM(xx.ad_income)+SUM(xx.ad_revenue)-SUM(xx.rebate_cost),0) net_profit,
            </when>
            <otherwise>
                TRUNCATE((SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income)*0.6)/SUM(xx.dau),2) pay_arpu,
                TRUNCATE((SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income)*0.6+SUM(xx.ad_income))/SUM(xx.dau),2) all_arpu,
                TRUNCATE(SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income)*0.6,0) pay_income,
                TRUNCATE(SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income)*0.6+SUM(xx.ad_income),0) all_income,
                TRUNCATE(SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income)*0.6+SUM(xx.ad_income)-SUM(xx.rebate_cost),0) profit,
                TRUNCATE(SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.tt_pay_income)*0.6+SUM(xx.ad_income)+SUM(xx.ad_revenue)-SUM(xx.rebate_cost),0) net_profit,
            </otherwise>
        </choose>

        SUM(xx.pay_reg_user_num) pay_reg_user_num,
        CONCAT(TRUNCATE(SUM(xx.pay_reg_user_num) / SUM(xx.add_num)*100, 2),'%') pay_reg_user_num_ratio
        from (<include refid="dn_micgame_revenue_sql"/>) xx
    </select>


    <sql id="dn_micgame_revenue_sql">
        select aa.*,
        dd.total_rebate_cost,
        dd.total_income,
        dd.total_ad_revenue,
        dd.total_profit,
        dd.total_gross_profit_ratio
        from
        (select
        ${group} tdate,
        appid,
        TRUNCATE(SUM(rebate_cost),0) rebate_cost,
        SUM(add_num) add_num,
        TRUNCATE(SUM(rebate_cost)/SUM(add_num),2) cpa,
        SUM(dau) dau,
        CONCAT(TRUNCATE(SUM(add_num) / SUM(dau)*100, 2),'%') reg_ratio,
        SUM(pay_num) pay_num,
        TRUNCATE(SUM(mds_pay_income)+SUM(wx_pay_income)+SUM(tt_pay_income),0) pay_income_total,
        TRUNCATE(SUM(mds_pay_income)+SUM(wx_pay_income)+SUM(tt_pay_income)+SUM(ad_income),0) pre_profit,
        CONCAT(TRUNCATE(SUM(pay_num) / SUM(dau)*100, 2),'%') pay_ratio,
        TRUNCATE((SUM(mds_pay_income)+SUM(wx_pay_income)+SUM(tt_pay_income))/SUM(pay_num),2) pay_user_arpu,
        TRUNCATE(SUM(ad_income)/SUM(dau),2) ad_arpu,
        TRUNCATE(SUM(ad_income),0) ad_income,
        TRUNCATE(SUM(ad_revenue),0) ad_revenue,
        TRUNCATE(SUM(pay_ad_revenue),0) pay_ad_revenue,
        TRUNCATE(SUM(refund_revenue),0) refund_revenue,
        /* 判断结束日期大于2024-04-01的，按照新汇率*0.9，否则按照旧汇率*0.6 */
        <choose>
            <when test="end_date != null and end_date &gt;= '2024-04-01'">
                TRUNCATE((SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.9)/SUM(dau),2) pay_arpu,
                TRUNCATE((SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.9+SUM(ad_income))/SUM(dau),2) all_arpu,
                TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.9,0) pay_income,
                TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.9+SUM(ad_income),0) all_income,
                TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.9+SUM(ad_income)-SUM(rebate_cost),0) profit,
                TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.9+SUM(ad_income)+SUM(ad_revenue)-SUM(rebate_cost),0) net_profit,
            </when>
            <otherwise>
                TRUNCATE((SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.6)/SUM(dau),2) pay_arpu,
                TRUNCATE((SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.6+SUM(ad_income))/SUM(dau),2) all_arpu,
                TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.6,0) pay_income,
                TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.6+SUM(ad_income),0) all_income,
                TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.6+SUM(ad_income)-SUM(rebate_cost),0) profit,
                TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.6+SUM(ad_income)+SUM(ad_revenue)-SUM(rebate_cost),0) net_profit,
            </otherwise>
        </choose>

        SUM(mds_pay_income) mds_pay_income,
        SUM(wx_pay_income) wx_pay_income,
        SUM(tt_pay_income) tt_pay_income,
        SUM(mds_pay_num) mds_pay_num,
        SUM(wx_pay_num) wx_pay_num,
        SUM(tt_pay_num) tt_pay_num,
        SUM(android_order_cnt) android_order_cnt,
        SUM(ios_order_cnt) ios_order_cnt,
        SUM(pay_reg_user_num) pay_reg_user_num,
        CONCAT(TRUNCATE(SUM(pay_reg_user_num) / SUM(add_num)*100, 2),'%') pay_reg_user_num_ratio
        from dn_micgame_revenue_total aa
        where tdate BETWEEN #{start_date} AND #{end_date}
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        group by ${group}
        <if test="new_group != null and new_group != ''">
            ,appid
        </if>
        ) aa
        <choose>
            <when test="new_group != null and new_group != ''">
                LEFT JOIN
                (select
                appid,
                TRUNCATE(SUM(rebate_cost),0) total_rebate_cost,
                TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.9+SUM(ad_income)-SUM(refund_revenue),0) total_income,
                TRUNCATE(SUM(ad_revenue)+SUM(pay_ad_revenue),0) total_ad_revenue,

                TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.9+
                SUM(ad_income)+SUM(ad_revenue)+SUM(pay_ad_revenue)-SUM(refund_revenue)-SUM(rebate_cost),0) total_profit,

                CONCAT(TRUNCATE((((SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.9
                +SUM(ad_income)+SUM(ad_revenue)+SUM(pay_ad_revenue)-SUM(refund_revenue))/SUM(rebate_cost))-1)*100,1),'%') total_gross_profit_ratio
                from dn_micgame_revenue_total where 1=1
                <if test="appid != null and appid != ''">
                    and appid in (${appid})
                </if>
                group by appid) dd ON aa.appid = dd.appid
            </when>
            <otherwise>
                ,
                (select
                appid,
                TRUNCATE(SUM(rebate_cost),0) total_rebate_cost,
                TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.9+SUM(ad_income)-SUM(refund_revenue),0) total_income,
                TRUNCATE(SUM(ad_revenue)+SUM(pay_ad_revenue),0) total_ad_revenue,
                TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.9
                +SUM(ad_income)+SUM(ad_revenue)+SUM(pay_ad_revenue)-SUM(refund_revenue)-SUM(rebate_cost),0) total_profit,

                CONCAT(TRUNCATE((((SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.9
                +SUM(ad_income)+SUM(ad_revenue)+SUM(pay_ad_revenue)-SUM(refund_revenue))/SUM(rebate_cost))-1)*100,1),'%') total_gross_profit_ratio

                from dn_micgame_revenue_total where 1=1
                <if test="appid != null and appid != ''">
                    and appid in (${appid})
                </if>
                ) dd
            </otherwise>
        </choose>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc, add_num desc
            </otherwise>
        </choose>
    </sql>
    
    
    <select id="getPaysMicGameIncomeDataList" parameterType="java.util.Map" resultType="com.wbgame.pojo.advert.MicGameIncomeVo">
        <include refid="dn_paysmicgame_revenue_sql"/>
    </select>

    <select id="getPaysMicGameIncomeDataListSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.advert.MicGameIncomeVo">
        select
        TRUNCATE(SUM(xx.rebate_cost),0) rebate_cost,
        TRUNCATE(SUM(xx.add_num),0) add_num,
        TRUNCATE(SUM(xx.dau),0) dau,
        TRUNCATE(SUM(xx.rebate_cost)/SUM(xx.add_num),2) cpa,
        CONCAT(TRUNCATE(SUM(xx.add_num) / SUM(xx.dau)*100, 2),'%') reg_ratio,
        SUM(xx.pay_num) pay_num,
        SUM(xx.mds_pay_num) mds_pay_num,
        SUM(xx.wx_pay_num) wx_pay_num,
        SUM(xx.mds_pay_income) mds_pay_income,
        SUM(xx.wx_pay_income) wx_pay_income,
        TRUNCATE(SUM(xx.mds_pay_income)+SUM(xx.wx_pay_income),0) pay_income_total,
        CONCAT(TRUNCATE(SUM(xx.pay_num) / SUM(xx.dau)*100, 2),'%') pay_ratio,
        TRUNCATE((SUM(xx.mds_pay_income)+SUM(xx.wx_pay_income))/SUM(xx.pay_num),2) pay_user_arpu,
        TRUNCATE((SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income))/SUM(xx.dau),2) pay_arpu,
        TRUNCATE(SUM(xx.ad_income)/SUM(xx.dau),2) ad_arpu,
        TRUNCATE((SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.ad_income))/SUM(xx.dau),2) all_arpu,
        TRUNCATE(SUM(xx.ad_income),0) ad_income,
        TRUNCATE(SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income),0) pay_income,
        TRUNCATE(SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.ad_income),0) all_income,
        TRUNCATE(SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.ad_income)-SUM(xx.rebate_cost),0) profit,
        TRUNCATE(SUM(xx.ad_revenue),0) ad_revenue,
        TRUNCATE(SUM(xx.mds_pay_income)*0.6+SUM(xx.wx_pay_income)+SUM(xx.ad_income)+SUM(xx.ad_revenue)-SUM(xx.rebate_cost),0) net_profit,
        SUM(xx.pay_reg_user_num) pay_reg_user_num,
        CONCAT(TRUNCATE(SUM(xx.pay_reg_user_num) / SUM(xx.add_num)*100, 2),'%') pay_reg_user_num_ratio
        from (<include refid="dn_paysmicgame_revenue_sql"/>) xx
    </select>


    <sql id="dn_paysmicgame_revenue_sql">
        select aa.*,
        dd.total_rebate_cost,
        dd.total_income,
        dd.total_ad_revenue,
        dd.total_profit,
        dd.total_gross_profit_ratio
        from
        (select
        ${group} tdate,
        appid,
        TRUNCATE(SUM(rebate_cost),0) rebate_cost,
        ROUND(SUM(add_num)*0.01,0) add_num,
        ROUND(SUM(dau)*0.01,0) dau,
        
        TRUNCATE(SUM(rebate_cost)/SUM(add_num),2) cpa,
        CONCAT(TRUNCATE(SUM(add_num) / SUM(dau)*100, 2),'%') reg_ratio,
        SUM(pay_num) pay_num,
        TRUNCATE((SUM(mds_pay_income)+SUM(wx_pay_income))*0.01,0) pay_income_total,
        CONCAT(TRUNCATE(SUM(pay_num) / SUM(dau)*100, 2),'%') pay_ratio,
        TRUNCATE((SUM(mds_pay_income)+SUM(wx_pay_income))/SUM(pay_num),2) pay_user_arpu,
        TRUNCATE((SUM(mds_pay_income)*0.6+SUM(wx_pay_income))/SUM(dau),2) pay_arpu,
        TRUNCATE(SUM(ad_income)/SUM(dau),2) ad_arpu,
        TRUNCATE((SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(ad_income))/SUM(dau),2) all_arpu,
        TRUNCATE(SUM(ad_income),0) ad_income,
        TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income),0) pay_income,
        TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(ad_income),0) all_income,
        TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(ad_income)-SUM(rebate_cost),0) profit,
        TRUNCATE(SUM(ad_revenue),0) ad_revenue,
        TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(ad_income)+SUM(ad_revenue)-SUM(rebate_cost),0) net_profit,
        SUM(mds_pay_income) mds_pay_income,
        SUM(wx_pay_income) wx_pay_income,
        SUM(mds_pay_num) mds_pay_num,
        SUM(wx_pay_num) wx_pay_num,
        SUM(pay_reg_user_num) pay_reg_user_num,
        CONCAT(TRUNCATE(SUM(pay_reg_user_num) / SUM(add_num)*100, 2),'%') pay_reg_user_num_ratio
        from dn_micgame_revenue_total aa
        where tdate BETWEEN #{start_date} AND #{end_date}
        
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        group by ${group},appid) aa
        LEFT JOIN
        (select
        appid,
        TRUNCATE(SUM(rebate_cost),0) total_rebate_cost,
        TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(ad_income),0) total_income,
        TRUNCATE(SUM(ad_revenue),0) total_ad_revenue,
        TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(ad_income)+SUM(ad_revenue)-SUM(rebate_cost),0) total_profit,
        CONCAT(TRUNCATE((((SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(ad_income)+SUM(ad_revenue))/SUM(rebate_cost))-1)*100,1),'%') total_gross_profit_ratio
        from dn_micgame_revenue_total where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        group by appid) dd
        ON aa.appid = dd.appid
        where aa.pay_income_total > 0 
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate asc, add_num desc
            </otherwise>
        </choose>
    </sql>

    <update id="updateMicGameIncomeAdRevenue" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" close=";" open="" separator=";">
            update dn_micgame_revenue_total
            <trim prefix="set" suffixOverrides=",">
                <if test="item.ad_revenue != null and item.ad_revenue != ''">
                    ad_revenue = #{item.ad_revenue},
                </if>
                <if test="item.pay_ad_revenue != null and item.pay_ad_revenue != ''">
                    pay_ad_revenue = #{item.pay_ad_revenue},
                </if>
                <if test="item.refund_revenue != null and item.refund_revenue != ''">
                    refund_revenue = #{item.refund_revenue},
                </if>
                modify_user=#{item.modify_user},
                modify_time =now()
            </trim>
            where 1=1 and  tdate =#{item.tdate} and appid = #{item.appid}
        </foreach>
    </update>

    <select id="getCityListMap" resultType="java.util.Map">
        SELECT a.id cityId,CONCAT(b.`name`,a.`name`) cityName from np_city a INNER JOIN np_province b on a.provice_id= b.id
    </select>

    <insert id="saveFeedbackInfo" parameterType="com.wbgame.pojo.game.userinfo.FeedbackVo">
        insert into user_feedback_info (msg,contact,imgs,msgtype,appid,loginid,androidid,lsn,pid,channel,version,idfa,create_time) value
        (#{msg},#{contact},#{imgs},#{msgtype},#{appid},#{loginid},#{androidid},#{lsn},#{pid},#{channel},#{version},idfa,now())
    </insert>

    <delete id="delFeedback" parameterType="java.lang.String">
        delete from user_feedback_info where id in (${id})
    </delete>

    <update id="updateFeedback" parameterType="java.util.Map">
        update user_feedback_info set state=#{state},
            <if test="note != null">
                note=#{note},
            </if>
           modify_user=#{modify_user},modify_time =now() where id in(${id})
    </update>

    <sql id="getUmengAdIncomeSql">
        select
        b.tdate
        <if test="group != null and group != '' and group.contains('appkey') ">
            ,b.appid,b.appkey
        </if>
        <if test="group != null and group != '' and group.contains('channel') ">
            ,b.channel
        </if>
        <if test="group != null and group != '' and group.contains('media')">
            ,b.media
        </if>
        <if test="group != null and group != '' and group.contains('source')">
            ,case b.source
                when '2' then '自统计'
                else '媒体'
            end source
        </if>
        ,b.appname, b.gameName,b.large_ver,
        b.temp_id, b.temp_name,b.active_temp_id, b.active_temp_name,b.packagename,b.appid_tag,b.ad_violation_type,
        sum(b.addnum) addnum,sum(b.actnum) actnum,
        round(sum(b.addnum)/sum(b.actnum),4) avgnum,
        round(sum(b.native_msg_show)/sum(b.actnum),2) msg_pv,
        round(sum(b.banner_show)/sum(b.actnum),2) banner_pv,
        round(sum(b.plaque_show)/sum(b.actnum),2) plaque_pv,
        round(sum(b.native_msg_show)/sum(b.actnum),2) native_msg_pv,
        round((sum(b.native_new_banner_show)+sum(b.native_banner_show))/sum(b.actnum),2) native_new_banner_pv,
        round(sum(b.native_splash_show)/sum(b.actnum),2) native_splash_pv,
        round(sum(b.plaque_video_show)/sum(b.actnum),2) plaque_video_pv,
        round(sum(b.native_plaque_show)/sum(b.actnum),2) native_plaque_pv,
        round(sum(b.native_banner_show)/sum(b.actnum),2) native_banner_pv,
        round(sum(b.system_splash_show)/sum(b.actnum),2) system_splash_pv,
        round(sum(b.splash_show)/sum(b.actnum),2) splash_pv,
        round(sum(b.suspend_icon_show)/sum(b.actnum),2) suspend_icon_pv,
        round(sum(b.video_show)/sum(b.actnum),2) video_pv,
        round((sum(b.native_new_plaque_show)+sum(b.native_plaque_show))/sum(b.actnum),2) native_new_plaque_pv,
        round((sum(b.system_splash_show)+sum(b.native_splash_show)+sum(splash_show))/sum(b.actnum),2) total_splash_pv,
        round((sum(b.native_new_plaque_show)+sum(b.native_plaque_show)+sum(b.plaque_show))/sum(b.actnum),2) total_plaque_pv,
        round(sum(b.plaque_video_show)/sum(b.actnum),2) total_plaque_video_pv,
        round((sum(b.native_new_banner_show)+sum(b.native_banner_show)+sum(b.banner_show))/sum(b.actnum),2) total_banner_pv,
        round(sum(b.video_show)/sum(b.actnum),2) total_video_pv,
        round(sum(b.native_msg_show)/sum(b.actnum),2) total_native_msg_pv,
        round(sum(b.suspend_icon_show)/sum(b.actnum),2) total_suspend_icon_pv,

        sum(banner_click) banner_click,
        sum(native_splash_click) native_splash_click,
        sum(native_plaque_click) native_plaque_click,
        sum(native_new_plaque_click) + sum(native_plaque_click) native_new_plaque_click,
        sum(native_msg_click) native_msg_click,
        sum(video_click) video_click,
        sum(splash_click) splash_click,
        sum(plaque_video_click) plaque_video_click,
        sum(system_splash_click) system_splash_click,
        sum(suspend_icon_click) suspend_icon_click,
        sum(plaque_click) plaque_click,
        sum(native_banner_click) native_banner_click,
        sum(native_new_banner_click) + sum(native_banner_click) native_new_banner_click,

        round((sum(b.system_splash_click)+sum(b.native_splash_click)+sum(splash_click))/sum(b.actnum),2) total_splash_click,
        round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click)+sum(b.plaque_click))/sum(b.actnum),2) total_plaque_click,
        round(sum(b.plaque_video_click)/sum(b.actnum),2) total_plaque_video_click,
        round((sum(b.native_new_banner_click)+sum(b.native_banner_click)+sum(b.banner_click))/sum(b.actnum),2) total_banner_click,
        round(sum(b.video_click)/sum(b.actnum),2) total_video_click,
        round(sum(b.native_msg_click)/sum(b.actnum),2) total_native_msg_click,
        round(sum(b.suspend_icon_click)/sum(b.actnum),2) total_suspend_icon_click,

        round(sum(b.banner_click)/sum(b.actnum),2) banner_avg_click,
        round(sum(b.plaque_click)/sum(b.actnum),2) plaque_avg_click,
        round(sum(b.native_msg_click)/sum(b.actnum),2) native_msg_avg_click,
        round((sum(b.native_new_banner_click)+sum(b.native_banner_click))/sum(b.actnum),2) native_new_banner_avg_click,
        round(sum(b.native_splash_click)/sum(b.actnum),2) native_splash_avg_click,
        round(sum(b.plaque_video_click)/sum(b.actnum),2) plaque_video_avg_click,
        round(sum(b.native_plaque_click)/sum(b.actnum),2) native_plaque_avg_click,
        round(sum(b.native_banner_click)/sum(b.actnum),2) native_banner_avg_click,
        round(sum(b.system_splash_click)/sum(b.actnum),2) system_splash_avg_click,
        round(sum(b.splash_click)/sum(b.actnum),2) splash_avg_click,
        round(sum(b.suspend_icon_click)/sum(b.actnum),2) suspend_icon_avg_click,
        round(sum(b.video_click)/sum(b.actnum),2) video_avg_click,
        round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click))/sum(b.actnum),2) native_new_plaque_avg_click,

        round(sum(b.video_income)/sum(b.actnum),3) video_arpu,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income))/sum(b.actnum),3) native_new_plaque_arpu,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income))/sum(b.actnum),3) native_new_banner_arpu,
        round(sum(b.banner_income)/sum(b.actnum),3) banner_arpu,
        round(sum(b.total_income)/sum(b.actnum),2) dau_arpu,
        round(sum(b.native_msg_income)/sum(b.actnum),3) msg_arpu,
        round(sum(b.splash_income)/sum(b.actnum),3) splash_arpu,
        round(sum(b.plaque_income)/sum(b.actnum),3) plaque_arpu,
        round(sum(b.plaque_video_income)/sum(b.actnum),3) plaque_video_arpu,
        round(sum(b.system_splash_income)/sum(b.actnum),3) system_splash_arpu,
        round(sum(b.suspend_icon_income)/sum(b.actnum),3) suspend_icon_arpu,
        round((sum(b.system_splash_income)+sum(b.native_splash_income)+sum(b.splash_income))/sum(b.actnum),3) total_splash_arpu,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income)+sum(b.plaque_income))/sum(b.actnum),3) total_plaque_arpu,
        round(sum(b.plaque_video_income)/sum(b.actnum),3) total_plaque_video_arpu,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income)+sum(b.banner_income))/sum(b.actnum),3) total_banner_arpu,
        round(sum(b.video_income)/sum(b.actnum),3) total_video_arpu,
        round(sum(b.native_msg_income)/sum(b.actnum),3) total_native_msg_arpu,
        round(sum(b.suspend_icon_income)/sum(b.actnum),3) total_suspend_icon_arpu,

        sum(native_splash_show) native_splash_show,
        sum(splash_show) splash_show,
        sum(native_new_banner_show) + sum(native_banner_show) native_new_banner_show,
        sum(native_plaque_show) native_plaque_show,
        sum(native_new_plaque_show) + sum(native_plaque_show) native_new_plaque_show,
        sum(native_banner_show) native_banner_show,
        sum(banner_show) banner_show,
        sum(suspend_icon_show) suspend_icon_show,
        sum(system_splash_show) system_splash_show,
        sum(video_show) video_show,
        sum(plaque_video_show) plaque_video_show,
        sum(native_msg_show) native_msg_show,
        sum(plaque_show) plaque_show,

        IFNULL(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show),0) sum_total_banner,
        IFNULL(round(sum(banner_show)/(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show))*100,2),0) sum_pv_banner,
        IFNULL(round((sum(native_new_banner_show)+sum(native_banner_show))/(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show))*100,2),0) sum_pv_new_banner,

        IFNULL(round((sum(banner_show)+sum(plaque_show)+sum(splash_show)+sum(native_banner_show)+sum(native_plaque_show)+sum(native_splash_show)+sum(video_show)+sum(plaque_video_show)
        +sum(native_msg_show)+sum(system_splash_show)+sum(native_new_plaque_show)+sum(native_new_banner_show)+sum(suspend_icon_show))/sum(b.actnum), 2), 0) all_total_pv,
        IFNULL(round((sum(banner_click)+sum(plaque_click)+sum(splash_click)+sum(native_banner_click)+sum(native_plaque_click)+sum(native_splash_click)+sum(video_click)+sum(plaque_video_click)
        +sum(native_msg_click)+sum(system_splash_click)+sum(native_new_plaque_click)+sum(native_new_banner_click)+sum(suspend_icon_click))/sum(b.actnum),2), 0) all_total_click,

        IFNULL(round((sum(banner_click)+sum(plaque_click)+sum(splash_click)+sum(native_banner_click)+sum(native_plaque_click)+sum(native_splash_click)+sum(video_click)+sum(plaque_video_click)
        +sum(native_msg_click)+sum(system_splash_click)+sum(native_new_plaque_click)+sum(native_new_banner_click)+sum(suspend_icon_click))/(sum(banner_show)+sum(plaque_show)+sum(splash_show)+sum(native_banner_show)+sum(native_plaque_show)+sum(native_splash_show)+sum(video_show)+sum(plaque_video_show)
        +sum(native_msg_show)+sum(system_splash_show)+sum(native_new_plaque_show)+sum(native_new_banner_show)+sum(suspend_icon_show))*100, 2), 0) all_total_ctr,

        round((sum(banner_income)*1000/sum(banner_show)),2) banner_ecpm,
        round((sum(native_banner_income)*1000/sum(native_banner_show)),2) native_banner_ecpm,
        round((sum(plaque_video_income)*1000/sum(plaque_video_show)),2) plaque_video_ecpm,
        round(((sum(native_new_plaque_income)+sum(native_plaque_income))*1000/(sum(native_new_plaque_show)+sum(native_plaque_show))),2) native_new_plaque_ecpm,
        round((sum(native_msg_income)*1000/sum(native_msg_show)),2) native_msg_ecpm,
        round((sum(native_splash_income)*1000/sum(native_splash_show)),2) native_splash_ecpm,
        round((sum(suspend_icon_income)*1000/sum(suspend_icon_show)),2) suspend_icon_ecpm,
        round((sum(splash_income)*1000/sum(splash_show)),2) splash_ecpm,
        round((sum(system_splash_income)*1000/sum(system_splash_show)),2) system_splash_ecpm,
        round((sum(native_plaque_income)*1000/sum(native_plaque_show)),2) native_plaque_ecpm,
        round((sum(video_income)*1000/sum(video_show)),2) video_ecpm,
        round((sum(plaque_income)*1000/sum(plaque_show)),2) plaque_ecpm,
        round(((sum(native_new_banner_income)+sum(native_banner_income))*1000/(sum(native_new_banner_show)+sum(native_banner_show))),2) native_new_banner_ecpm,

        ifnull(round(sum(total_income),2),0) total_income,
        round(sum(native_plaque_income),2) native_plaque_income,
        round(sum(video_income),2) video_income,
        round(sum(native_msg_income),2) native_msg_income,
        round(sum(suspend_icon_income),2) suspend_icon_income,
        round(sum(splash_income),2) splash_income,
        round(sum(banner_income),2) banner_income,
        round(sum(plaque_video_income),2) plaque_video_income,
        round(sum(system_splash_income),2) system_splash_income,
        round(sum(native_banner_income),2) native_banner_income,
        round(sum(native_new_banner_income)+sum(native_banner_income),2) native_new_banner_income,
        round(sum(native_splash_income),2) native_splash_income,
        round(sum(plaque_income),2) plaque_income,
        round(sum(native_new_plaque_income)+sum(native_plaque_income),2) native_new_plaque_income,

        ifnull(round(sum(b.splash_click)/sum(b.splash_show)*100,2),0) splash_ctr,
        ifnull(round(sum(b.native_splash_click)/sum(b.native_splash_show)*100,2),0) native_splash_ctr,
        ifnull(round(sum(b.plaque_click)/sum(b.plaque_show)*100,2),0) plaque_ctr,
        ifnull(round(sum(b.native_plaque_click)/sum(b.native_plaque_show)*100,2),0) native_plaque_ctr,
        ifnull(round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click))/(sum(b.native_new_plaque_show)+sum(b.native_plaque_show))*100,2),0) native_new_plaque_ctr,
        ifnull(round(sum(b.plaque_video_click)/sum(b.plaque_video_show)*100,2),0) plaque_video_ctr,
        ifnull(round(sum(b.banner_click)/sum(b.banner_show)*100,2),0) banner_ctr,
        ifnull(round(sum(b.native_banner_click)/sum(b.native_banner_show)*100,2),0) native_banner_ctr,
        ifnull(round((sum(b.native_new_banner_click)+sum(b.native_banner_click))/(sum(b.native_new_banner_show)+sum(b.native_banner_show))*100,2),0) native_new_banner_ctr,
        ifnull(round(sum(b.video_click)/sum(b.video_show)*100,2),0) video_ctr,
        ifnull(round(sum(b.native_msg_click)/sum(b.native_msg_show)*100,2),0) native_msg_ctr,
        ifnull(round(sum(b.suspend_icon_click)/sum(b.suspend_icon_show)*100,2),0) suspend_icon_ctr,

        round(sum(b.splash_income)/sum(b.splash_click),3) splash_cpc,
        round(sum(b.native_splash_income)/sum(b.native_splash_click),3) native_splash_cpc,
        round(sum(b.plaque_income)/sum(b.plaque_click),3) plaque_cpc,
        round(sum(b.native_plaque_income)/sum(b.native_plaque_click),3) native_plaque_cpc,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income))/(sum(b.native_new_plaque_click)+sum(b.native_plaque_click)),3) native_new_plaque_cpc,
        round(sum(b.plaque_video_income)/sum(b.plaque_video_click),3) plaque_video_cpc,
        round(sum(b.banner_income)/sum(b.banner_click),3) banner_cpc,
        round(sum(b.native_banner_income)/sum(b.native_banner_click),3) native_banner_cpc,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income))/(sum(b.native_new_banner_click)+sum(b.native_banner_click)),3) native_new_banner_cpc,
        round(sum(b.video_income)/sum(b.video_click),3) video_cpc,
        round(sum(b.native_msg_income)/sum(b.native_msg_click),3) native_msg_cpc,
        round(sum(b.suspend_icon_income)/sum(b.suspend_icon_click),3) suspend_icon_cpc,
        round(sum(b.t_duration)/sum(b.actnum),0)  daily_duration,

        sum(banner_request) banner_request,
        sum(native_splash_request) native_splash_request,
        sum(native_new_plaque_request) + sum(native_plaque_request) native_new_plaque_request,
        sum(native_msg_request) native_msg_request,
        sum(video_request) video_request,
        sum(splash_request) splash_request,
        sum(plaque_video_request) plaque_video_request,
        sum(system_splash_request) system_splash_request,
        sum(suspend_icon_request) suspend_icon_request,
        sum(plaque_request) plaque_request,
        sum(native_new_banner_request) + sum(native_banner_request) native_new_banner_request,

        IFNULL(round((sum(banner_request)+sum(native_splash_request)+sum(native_new_plaque_request)+sum(native_plaque_request)+sum(native_msg_request)+sum(video_request)+sum(splash_request)
        +sum(plaque_video_request)+sum(system_splash_request)+sum(suspend_icon_request)+sum(plaque_request)+sum(native_new_banner_request)+sum(native_banner_request))/sum(b.actnum),2), 0) all_total_request,
        round((sum(b.system_splash_request)+sum(b.native_splash_request)+sum(splash_request))/sum(b.actnum),2) total_splash_request,
        round((sum(b.native_new_plaque_request)+sum(b.native_plaque_request)+sum(b.plaque_request))/sum(b.actnum),2) total_plaque_request,
        round(sum(b.plaque_video_request)/sum(b.actnum),2) total_plaque_video_request,
        round((sum(b.native_new_banner_request)+sum(b.native_banner_request)+sum(b.banner_request))/sum(b.actnum),2) total_banner_request,
        round(sum(b.video_request)/sum(b.actnum),2) total_video_request,
        round(sum(b.native_msg_request)/sum(b.actnum),2) total_native_msg_request,
        round(sum(b.suspend_icon_request)/sum(b.actnum),2) total_suspend_icon_request,

        round(sum(b.system_splash_request)/sum(b.actnum),2) system_splash_avg_request,
        round(sum(b.splash_request)/sum(b.actnum),2) splash_avg_request,
        round(sum(b.native_splash_request)/sum(b.actnum),2) native_splash_avg_request,
        round(sum(b.plaque_request)/sum(b.actnum),2) plaque_avg_request,
        round((sum(b.native_new_plaque_request)+sum(b.native_plaque_request))/sum(b.actnum),2) native_new_plaque_avg_request,
        round(sum(b.plaque_video_request)/sum(b.actnum),2) plaque_video_avg_request,
        round(sum(b.banner_request)/sum(b.actnum),2) banner_avg_request,
        round((sum(b.native_new_banner_request) + sum(b.native_banner_request))/sum(b.actnum),2) native_new_banner_avg_request,
        round(sum(b.video_request)/sum(b.actnum),2) video_avg_request,
        round(sum(b.native_msg_request)/sum(b.actnum),2) native_msg_avg_request,
        round(sum(b.suspend_icon_request)/sum(b.actnum),2) suspend_icon_avg_request,

        sum(banner_fill) banner_fill,
        sum(native_splash_fill) native_splash_fill,
        sum(native_new_plaque_fill) + sum(native_plaque_fill) native_new_plaque_fill,
        sum(native_msg_fill) native_msg_fill,
        sum(video_fill) video_fill,
        sum(splash_fill) splash_fill,
        sum(plaque_video_fill) plaque_video_fill,
        sum(system_splash_fill) system_splash_fill,
        sum(suspend_icon_fill) suspend_icon_fill,
        sum(plaque_fill) plaque_fill,
        sum(native_new_banner_fill) + sum(native_banner_fill) native_new_banner_fill,

        IFNULL(round((sum(banner_fill)+sum(plaque_fill)+sum(splash_fill)+sum(native_banner_fill)+sum(native_plaque_fill)+sum(native_splash_fill)+sum(video_fill)+sum(plaque_video_fill)
        +sum(native_msg_fill)+sum(system_splash_fill)+sum(native_new_plaque_fill)+sum(native_new_banner_fill)+sum(suspend_icon_fill))/sum(b.actnum),2), 0) all_total_avg_fill,
        round((sum(b.system_splash_fill)+sum(b.native_splash_fill)+sum(splash_fill))/sum(b.actnum),2) total_splash_avg_fill,
        round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill)+sum(b.plaque_fill))/sum(b.actnum),2) total_plaque_avg_fill,
        round(sum(b.plaque_video_fill)/sum(b.actnum),2) total_plaque_video_avg_fill,
        round((sum(b.native_new_banner_fill)+sum(b.native_banner_fill)+sum(b.banner_fill))/sum(b.actnum),2) total_banner_avg_fill,
        round(sum(b.video_fill)/sum(b.actnum),2) total_video_avg_fill,
        round(sum(b.native_msg_fill)/sum(b.actnum),2) total_native_msg_avg_fill,
        round(sum(b.suspend_icon_fill)/sum(b.actnum),2) total_suspend_icon_avg_fill,

        round(sum(b.system_splash_fill)/sum(b.actnum),2) system_splash_avg_fill,
        round(sum(b.splash_fill)/sum(b.actnum),2) splash_avg_fill,
        round(sum(b.native_splash_fill)/sum(b.actnum),2) native_splash_avg_fill,
        round(sum(b.plaque_fill)/sum(b.actnum),2) plaque_avg_fill,
        round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill))/sum(b.actnum),2) native_new_plaque_avg_fill,
        round(sum(b.plaque_video_fill)/sum(b.actnum),2) plaque_video_avg_fill,
        round(sum(b.banner_fill)/sum(b.actnum),2) banner_avg_fill,
        round((sum(b.native_new_banner_fill) + sum(b.native_banner_fill))/sum(b.actnum),2) native_new_banner_avg_fill,
        round(sum(b.video_fill)/sum(b.actnum),2) video_avg_fill,
        round(sum(b.native_msg_fill)/sum(b.actnum),2) native_msg_avg_fill,
        round(sum(b.suspend_icon_fill)/sum(b.actnum),2) suspend_icon_avg_fill,

        IFNULL(round((sum(banner_fill)+sum(plaque_fill)+sum(splash_fill)+sum(native_banner_fill)+sum(native_plaque_fill)+sum(native_splash_fill)+sum(video_fill)+sum(plaque_video_fill)
        +sum(native_msg_fill)+sum(system_splash_fill)+sum(native_new_plaque_fill)+sum(native_new_banner_fill)+sum(suspend_icon_fill))/
        (sum(banner_request)+sum(native_splash_request)+sum(native_new_plaque_request)+sum(native_plaque_request)+sum(native_msg_request)+sum(video_request)+sum(splash_request)
        +sum(plaque_video_request)+sum(system_splash_request)+sum(suspend_icon_request)+sum(plaque_request)+sum(native_new_banner_request)+sum(native_banner_request))*100,2), 0) all_total_fill,
        IFNULL(round(sum(b.system_splash_fill)/sum(b.system_splash_request)*100,2), 0) total_system_splash_fill,
        IFNULL(round(sum(b.splash_fill)/sum(b.splash_request)*100,2), 0) total_splash_fill,
        IFNULL(round(sum(b.native_splash_fill)/sum(b.native_splash_request)*100,2), 0) total_native_splash_fill,
        IFNULL(round(sum(b.plaque_fill)/sum(b.plaque_request)*100,2), 0) total_plaque_fill,
        IFNULL(round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill))/(sum(b.native_new_plaque_request)+sum(b.native_plaque_request))*100,2), 0) total_native_new_plaque_fill,
        IFNULL(round(sum(b.plaque_video_fill)/sum(b.plaque_video_request)*100,2), 0) total_plaque_video_fill,
        IFNULL(round(sum(b.banner_fill)/sum(b.banner_request)*100,2), 0) total_banner_fill,
        IFNULL(round((sum(b.native_new_banner_fill)+sum(b.native_banner_fill))/(sum(b.native_new_banner_request)+sum(b.native_banner_request))*100,2), 0) total_native_new_banner_fill,
        IFNULL(round(sum(b.video_fill)/sum(b.video_request)*100,2), 0) total_video_fill,
        IFNULL(round(sum(b.native_msg_fill)/sum(b.native_msg_request)*100,2), 0) total_native_msg_fill,
        IFNULL(round(sum(b.suspend_icon_fill)/sum(b.suspend_icon_request)*100,2), 0) total_suspend_icon_fill,

        IFNULL(round(sum(b.show_splash_ad_active_cnt)/sum(b.actnum)*100,2), 0) show_splash_ad_active_cnt,
        IFNULL(round(sum(b.show_plaque_ad_active_cnt)/sum(b.actnum)*100,2), 0) show_plaque_ad_active_cnt,
        IFNULL(round(sum(b.show_banner_ad_active_cnt)/sum(b.actnum)*100,2), 0) show_banner_ad_active_cnt,
        IFNULL(round(sum(b.show_video_ad_active_cnt)/sum(b.actnum)*100,2), 0) show_video_ad_active_cnt,
        IFNULL(round(sum(b.show_msg_ad_active_cnt)/sum(b.actnum)*100,2), 0) show_msg_ad_active_cnt,
        IFNULL(round(sum(b.show_icon_ad_active_cnt)/sum(b.actnum)*100,2), 0) show_icon_ad_active_cnt,

        IFNULL(round(sum(b.click_splash_ad_active_cnt)/sum(b.actnum)*100,2), 0) click_splash_ad_active_cnt,
        IFNULL(round(sum(b.click_plaque_ad_active_cnt)/sum(b.actnum)*100,2), 0) click_plaque_ad_active_cnt,
        IFNULL(round(sum(b.click_banner_ad_active_cnt)/sum(b.actnum)*100,2), 0) click_banner_ad_active_cnt,
        IFNULL(round(sum(b.click_video_ad_active_cnt)/sum(b.actnum)*100,2), 0) click_video_ad_active_cnt,
        IFNULL(round(sum(b.click_msg_ad_active_cnt)/sum(b.actnum)*100,2), 0) click_msg_ad_active_cnt,
        IFNULL(round(sum(b.click_icon_ad_active_cnt)/sum(b.actnum)*100,2), 0) click_icon_ad_active_cnt,
        IFNULL(round(sum(b.show_total_ad_active_cnt)/sum(b.actnum)*100,2), 0) show_total_ad_active_cnt,
        IFNULL(round(sum(b.click_total_ad_active_cnt)/sum(b.actnum)*100,2), 0) click_total_ad_active_cnt

        from
        (
        SELECT
        REPLACE(c.appkey,'_2','') appkey,c.channel,c.media,c.gameName,c.temp_id,c.temp_name,c.large_ver,c.actnum,c.total_income,c.dau_arpu,c.banner_pv,c.plaque_pv,c.splash_pv,c.video_pv,
        c.banner_arpu,c.plaque_arpu,c.splash_arpu,c.video_arpu,c.banner_ecpm,c.plaque_ecpm,c.splash_ecpm,c.native_banner_ecpm,
        c.native_plaque_ecpm,c.native_splash_ecpm,c.video_ecpm,c.plaque_video_ecpm,c.banner_show,c.plaque_show,c.splash_show,
        c.native_banner_show,c.native_plaque_show,c.native_splash_show,c.video_show,c.plaque_video_show,c.banner_income,c.plaque_income,
        c.splash_income,c.native_banner_income,c.native_plaque_income,c.native_splash_income,c.video_income,c.plaque_video_income,c.msg_pv,
        c.msg_arpu,c.native_msg_ecpm,c.native_msg_show,c.native_msg_income,c.plaque_video_pv,c.plaque_video_arpu,c.addnum,c.avgnum,c.banner_click,
        c.plaque_click,c.splash_click,c.native_banner_click,c.native_plaque_click,c.native_splash_click,c.video_click,c.plaque_video_click,
        c.system_splash_show,c.native_new_plaque_show,c.native_new_banner_show,c.suspend_icon_show,c.system_splash_pv,c.native_new_plaque_pv,
        c.native_new_banner_pv,c.suspend_icon_pv,c.system_splash_arpu,c.native_new_plaque_arpu,c.native_new_banner_arpu,c.suspend_icon_arpu,
        c.system_splash_ecpm,c.native_new_plaque_ecpm,c.native_new_banner_ecpm,c.suspend_icon_ecpm,c.system_splash_income,c.native_new_plaque_income,
        c.native_new_banner_income,c.suspend_icon_income,c.system_splash_click,c.native_new_plaque_click,c.native_new_banner_click,c.suspend_icon_click,
        c.native_plaque_pv,c.native_banner_pv,c.native_msg_click,
        d.daily_duration,f.id appid,f.app_name appname,c.source,c.active_temp_id, c.active_temp_name,
        TIME_TO_SEC(d.daily_duration) * d.actnum AS t_duration,
        c.banner_request,c.plaque_request,c.splash_request,c.video_request,c.native_banner_request,c.native_plaque_request,c.native_splash_request,
        c.plaque_video_request,c.native_msg_request,c.system_splash_request,c.native_new_plaque_request,
        c.native_new_banner_request,c.suspend_icon_request,c.banner_fill,c.plaque_fill,c.splash_fill,c.video_fill,
        c.native_banner_fill,c.native_plaque_fill,c.native_splash_fill,c.plaque_video_fill,c.native_msg_fill,
        c.system_splash_fill,c.native_new_plaque_fill,c.native_new_banner_fill,c.suspend_icon_fill,
        c.show_splash_ad_active_cnt, c.show_plaque_ad_active_cnt, c.show_banner_ad_active_cnt, c.show_video_ad_active_cnt,
        c.show_msg_ad_active_cnt, c.show_icon_ad_active_cnt, c.click_splash_ad_active_cnt, c.click_plaque_ad_active_cnt,
        c.click_banner_ad_active_cnt, c.click_video_ad_active_cnt, c.click_msg_ad_active_cnt, c.click_icon_ad_active_cnt,
        c.show_total_ad_active_cnt, c.click_total_ad_active_cnt,h.packagename,h.appid_tag,c.ad_violation_type,
        <choose>
            <when test="custom_date != null and custom_date != ''">
                concat(#{start_date},'至',#{end_date}) as tdate
            </when>
            <when test="group != null and group != '' and group.contains('tdate')">
                c.tdate
            </when>
            <when test="group != null and group != '' and group.contains('week')">
                DATE_FORMAT(c.tdate, '%x-%v') as tdate,
                DATE_FORMAT(c.tdate, '%x-%v') as week
            </when>
            <when test="group != null and group != '' and group.contains('month')">
                DATE_FORMAT(c.tdate,'%Y-%m') as tdate,
                DATE_FORMAT(c.tdate,'%Y-%m') as `month`
            </when>
            <when test="group != null and group != '' and group.contains('beek')">
                CONCAT(DATE_FORMAT(c.tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(c.tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(c.tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS tdate,
                CONCAT(DATE_FORMAT(c.tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(c.tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(c.tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS beek
            </when>
            <otherwise>
                concat(#{start_date},'至',#{end_date}) as tdate
            </otherwise>
        </choose>

		FROM ${tableName} c
        left join app_info f on REPLACE(c.appkey,'_2','') = f.umeng_key
        LEFT JOIN umeng_channel_total d ON c.tdate = d.tdate
        AND REPLACE(c.appkey,'_2','') = d.appkey
        AND c.channel = d.cname
        left join (SELECT a.appid, a.channel, a.packagename,a.appid_tag FROM( SELECT appid, channel, MAX(createTime) AS max_createTime FROM adv_platform_app_info WHERE bindEndTime >= CURRENT_DATE GROUP BY appid, channel) AS latest_app INNER JOIN adv_platform_app_info a ON latest_app.appid = a.appid AND latest_app.channel = a.channel AND latest_app.max_createTime = a.createTime) h
        on c.appid = h.appid and c.channel = h.channel
        WHERE c.tdate <![CDATA[>=]]> #{start_date} and c.tdate <![CDATA[<=]]> #{end_date}
        and concat(c.appid, c.channel) in
        (SELECT con_ac FROM ads_appid_channel_info
        <where>
            <if test="state != null and state != ''">
                state in (${state})
            </if>
        </where>)
        <if test="appid != null and appid != '' ">
            and c.appid in (${appid})
        </if>
        <if test="channel != null and channel != '' ">
            and c.channel in (${channel})
        </if>
        <if test="media != null and media != '' ">
            and c.media in (${media})
        </if>
        <if test="gameName != null and gameName != '' ">
            and c.gameName in (${gameName})
        </if>
        <if test="temp_id != null and temp_id != '' ">
            and c.temp_id like concat('%',#{temp_id},'%')
        </if>
        <if test="temp_name != null and temp_name != '' ">
            and c.temp_name like concat('%',#{temp_name},'%')
        </if>
        <if test="large_ver != null and large_ver != '' ">
            and c.large_ver like concat('%',#{large_ver},'%')
        </if>
        <if test="active_temp_id != null and active_temp_id != '' ">
            and c.active_temp_id like concat('%',#{active_temp_id},'%')
        </if>
        <if test="active_temp_name != null and active_temp_name != '' ">
            and c.active_temp_name like concat('%',#{active_temp_name},'%')
        </if>
        <if test="source != null and source != ''">
            and c.source = #{source}
        </if>
        <if test="appid_tag != null and appid_tag != ''">
            <choose>
                <when test="appid_tag_rev != null and appid_tag_rev != ''">
                    AND CONCAT(c.appid,'#',c.channel) not in (${appid_tag})
                </when>
                <otherwise>
                    AND CONCAT(c.appid,'#',c.channel) in (${appid_tag})
                </otherwise>
            </choose>
        </if>
        <if test="packagename != null and packagename != ''">
            and h.packagename like concat('%',#{packagename},'%')
        </if>
        <if test="ad_violation_type != null and ad_violation_type != ''">
            <foreach collection="ad_violation_type.split(',')" item="type" open="and (" close=")" separator=" or ">
                FIND_IN_SET(${type},REPLACE(ad_violation_type, '|', ','))
            </foreach>
        </if>
        ) b

        <if test="group != null and group != '' ">
            group by ${group}
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by b.tdate asc,b.addnum+0 desc
            </otherwise>
        </choose>
    </sql>

    <select id="getAppChannelList" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT distinct concat(app_info.appid, app_info.channel) as con_ac,
                        app_info.state
        FROM adv_platform_app_info app_info
        join  (
            SELECT appid, channel, MAX(bindEndTime) as max_bindEndTime
            FROM adv_platform_app_info
            group by appid, channel
        ) dim_info
       on app_info.appid = dim_info.appid
       and app_info.channel = dim_info.channel
       and app_info.bindEndTime = dim_info.max_bindEndTime
      <if test="state != null and state != ''">
         and  app_info.state in (${state})
      </if>
    </select>

    <select id="getStateByAppidChannel" resultType="java.util.Map">
        <if test="appid != null and appid != '' and channel != null and channel != ''">
            SELECT distinct concat(app_info.appid, app_info.channel) as con_ac,
            app_info.state
            FROM adv_platform_app_info app_info
            join (
                SELECT appid, channel, max(bindEndTime) as max_bindEndTime
                FROM adv_platform_app_info
                where appid = ${appid}
                and channel = ${channel}
                group by appid, channel
            ) dim_app_info
            on app_info.appid = dim_app_info.appid
            and app_info.channel = dim_app_info.channel
            and app_info.bindEndTime = dim_app_info.max_bindEndTime
            <if test="state != null and state != ''">
                where  app_info.state in (${state})
            </if>
            order by app_info.createTime desc limit 1
        </if>
    </select>

    <select id="getUmengAdIncomeList" parameterType="java.util.Map" resultType="com.wbgame.pojo.advert.UmengAdIncomeReportVo">
        <include refid="getUmengAdIncomeSql"/>
    </select>

    <select id="getUmengAdIncomeSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.advert.UmengAdIncomeReportVo">
        select
        sum(b.addnum) addnum,sum(b.actnum) actnum,
        round(sum(b.addnum)/sum(b.actnum),4) avgnum,

        round(sum(b.native_msg_show)/sum(b.actnum),2) msg_pv,
        round(sum(b.banner_show)/sum(b.actnum),2) banner_pv,
        round(sum(b.plaque_show)/sum(b.actnum),2) plaque_pv,
        round(sum(b.native_msg_show)/sum(b.actnum),2) native_msg_pv,
        round((sum(b.native_new_banner_show)+sum(b.native_banner_show))/sum(b.actnum),2) native_new_banner_pv,
        round(sum(b.native_splash_show)/sum(b.actnum),2) native_splash_pv,
        round(sum(b.plaque_video_show)/sum(b.actnum),2) plaque_video_pv,
        round(sum(b.native_plaque_show)/sum(b.actnum),2) native_plaque_pv,
        round(sum(b.native_banner_show)/sum(b.actnum),2) native_banner_pv,
        round(sum(b.system_splash_show)/sum(b.actnum),2) system_splash_pv,
        round(sum(b.splash_show)/sum(b.actnum),2) splash_pv,
        round(sum(b.suspend_icon_show)/sum(b.actnum),2) suspend_icon_pv,
        round(sum(b.video_show)/sum(b.actnum),2) video_pv,
        round((sum(b.native_new_plaque_show)+sum(b.native_plaque_show))/sum(b.actnum),2) native_new_plaque_pv,
        round((sum(b.system_splash_show)+sum(b.native_splash_show)+sum(splash_show))/sum(b.actnum),2) total_splash_pv,
        round((sum(b.native_new_plaque_show)+sum(b.native_plaque_show)+sum(b.plaque_show))/sum(b.actnum),2) total_plaque_pv,
        round(sum(b.plaque_video_show)/sum(b.actnum),2) total_plaque_video_pv,
        round((sum(b.native_new_banner_show)+sum(b.native_banner_show)+sum(b.banner_show))/sum(b.actnum),2) total_banner_pv,
        round(sum(b.video_show)/sum(b.actnum),2) total_video_pv,
        round(sum(b.native_msg_show)/sum(b.actnum),2) total_native_msg_pv,
        round(sum(b.suspend_icon_show)/sum(b.actnum),2) total_suspend_icon_pv,

        sum(banner_click) banner_click,
        sum(native_splash_click) native_splash_click,
        sum(native_plaque_click) native_plaque_click,
        sum(native_new_plaque_click) + sum(native_plaque_click) native_new_plaque_click,
        sum(native_msg_click) native_msg_click,
        sum(video_click) video_click,
        sum(splash_click) splash_click,
        sum(plaque_video_click) plaque_video_click,
        sum(system_splash_click) system_splash_click,
        sum(suspend_icon_click) suspend_icon_click,
        sum(plaque_click) plaque_click,
        sum(native_banner_click) native_banner_click,
        sum(native_new_banner_click) + sum(native_banner_click) native_new_banner_click,

        round((sum(b.system_splash_click)+sum(b.native_splash_click)+sum(splash_click))/sum(b.actnum),2) total_splash_click,
        round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click)+sum(b.plaque_click))/sum(b.actnum),2) total_plaque_click,
        round(sum(b.plaque_video_click)/sum(b.actnum),2) total_plaque_video_click,
        round((sum(b.native_new_banner_click)+sum(b.native_banner_click)+sum(b.banner_click))/sum(b.actnum),2) total_banner_click,
        round(sum(b.video_click)/sum(b.actnum),2) total_video_click,
        round(sum(b.native_msg_click)/sum(b.actnum),2) total_native_msg_click,
        round(sum(b.suspend_icon_click)/sum(b.actnum),2) total_suspend_icon_click,

        round(sum(b.banner_click)/sum(b.actnum),2) banner_avg_click,
        round(sum(b.plaque_click)/sum(b.actnum),2) plaque_avg_click,
        round(sum(b.native_msg_click)/sum(b.actnum),2) native_msg_avg_click,
        round((sum(b.native_new_banner_click)+sum(b.native_banner_click))/sum(b.actnum),2) native_new_banner_avg_click,
        round(sum(b.native_splash_click)/sum(b.actnum),2) native_splash_avg_click,
        round(sum(b.plaque_video_click)/sum(b.actnum),2) plaque_video_avg_click,
        round(sum(b.native_plaque_click)/sum(b.actnum),2) native_plaque_avg_click,
        round(sum(b.native_banner_click)/sum(b.actnum),2) native_banner_avg_click,
        round(sum(b.system_splash_click)/sum(b.actnum),2) system_splash_avg_click,
        round(sum(b.splash_click)/sum(b.actnum),2) splash_avg_click,
        round(sum(b.suspend_icon_click)/sum(b.actnum),2) suspend_icon_avg_click,
        round(sum(b.video_click)/sum(b.actnum),2) video_avg_click,
        round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click))/sum(b.actnum),2) native_new_plaque_avg_click,

        round(sum(b.video_income)/sum(b.actnum),3) video_arpu,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income))/sum(b.actnum),3) native_new_plaque_arpu,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income))/sum(b.actnum),3) native_new_banner_arpu,
        round(sum(b.banner_income)/sum(b.actnum),3) banner_arpu,
        round(sum(b.total_income)/sum(b.actnum),2) dau_arpu,
        round(sum(b.native_msg_income)/sum(b.actnum),3) msg_arpu,
        round(sum(b.splash_income)/sum(b.actnum),3) splash_arpu,
        round(sum(b.plaque_income)/sum(b.actnum),3) plaque_arpu,
        round(sum(b.plaque_video_income)/sum(b.actnum),3) plaque_video_arpu,
        round(sum(b.system_splash_income)/sum(b.actnum),3) system_splash_arpu,
        round(sum(b.suspend_icon_income)/sum(b.actnum),3) suspend_icon_arpu,
        round((sum(b.system_splash_income)+sum(b.native_splash_income)+sum(b.splash_income))/sum(b.actnum),3) total_splash_arpu,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income)+sum(b.plaque_income))/sum(b.actnum),3) total_plaque_arpu,
        round(sum(b.plaque_video_income)/sum(b.actnum),3) total_plaque_video_arpu,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income)+sum(b.banner_income))/sum(b.actnum),3) total_banner_arpu,
        round(sum(b.video_income)/sum(b.actnum),3) total_video_arpu,
        round(sum(b.native_msg_income)/sum(b.actnum),3) total_native_msg_arpu,
        round(sum(b.suspend_icon_income)/sum(b.actnum),3) total_suspend_icon_arpu,

        sum(native_splash_show) native_splash_show,
        sum(splash_show) splash_show,
        sum(native_new_banner_show) + sum(native_banner_show) native_new_banner_show,
        sum(native_plaque_show) native_plaque_show,
        sum(native_new_plaque_show) + sum(native_plaque_show) native_new_plaque_show,
        sum(native_banner_show) native_banner_show,
        sum(banner_show) banner_show,
        sum(suspend_icon_show) suspend_icon_show,
        sum(system_splash_show) system_splash_show,
        sum(video_show) video_show,
        sum(plaque_video_show) plaque_video_show,
        sum(native_msg_show) native_msg_show,
        sum(plaque_show) plaque_show,

        IFNULL(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show),0) sum_total_banner,
        IFNULL(round(sum(banner_show)/(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show))*100,2),0) sum_pv_banner,
        IFNULL(round((sum(native_new_banner_show)+sum(native_banner_show))/(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show))*100,2),0) sum_pv_new_banner,



        round((sum(banner_income)*1000/sum(banner_show)),2) banner_ecpm,
        round((sum(native_banner_income)*1000/sum(native_banner_show)),2) native_banner_ecpm,
        round((sum(plaque_video_income)*1000/sum(plaque_video_show)),2) plaque_video_ecpm,
        round(((sum(native_new_plaque_income)+sum(native_plaque_income))*1000/(sum(native_new_plaque_show)+sum(native_plaque_show))),2) native_new_plaque_ecpm,
        round((sum(native_msg_income)*1000/sum(native_msg_show)),2) native_msg_ecpm,
        round((sum(native_splash_income)*1000/sum(native_splash_show)),2) native_splash_ecpm,
        round((sum(suspend_icon_income)*1000/sum(suspend_icon_show)),2) suspend_icon_ecpm,
        round((sum(splash_income)*1000/sum(splash_show)),2) splash_ecpm,
        round((sum(system_splash_income)*1000/sum(system_splash_show)),2) system_splash_ecpm,
        round((sum(native_plaque_income)*1000/sum(native_plaque_show)),2) native_plaque_ecpm,
        round((sum(video_income)*1000/sum(video_show)),2) video_ecpm,
        round((sum(plaque_income)*1000/sum(plaque_show)),2) plaque_ecpm,
        round(((sum(native_new_banner_income)+sum(native_banner_income))*1000/(sum(native_new_banner_show)+sum(native_banner_show))),2) native_new_banner_ecpm,

        round(sum(total_income),2) total_income,
        round(sum(native_plaque_income),2) native_plaque_income,
        round(sum(video_income),2) video_income,
        round(sum(native_msg_income),2) native_msg_income,
        round(sum(suspend_icon_income),2) suspend_icon_income,
        round(sum(splash_income),2) splash_income,
        round(sum(banner_income),2) banner_income,
        round(sum(plaque_video_income),2) plaque_video_income,
        round(sum(system_splash_income),2) system_splash_income,
        round(sum(native_banner_income),2) native_banner_income,
        round(sum(native_new_banner_income)+sum(native_banner_income),2) native_new_banner_income,
        round(sum(native_splash_income),2) native_splash_income,
        round(sum(plaque_income),2) plaque_income,
        round(sum(native_new_plaque_income)+sum(native_plaque_income),2) native_new_plaque_income,

        concat(ifnull(round(sum(b.splash_click)/sum(b.splash_show)*100,2),0),'%') splash_ctr,
        concat(ifnull(round(sum(b.native_splash_click)/sum(b.native_splash_show)*100,2),0),'%') native_splash_ctr,
        concat(ifnull(round(sum(b.plaque_click)/sum(b.plaque_show)*100,2),0),'%') plaque_ctr,
        concat(ifnull(round(sum(b.native_plaque_click)/sum(b.native_plaque_show)*100,2),0),'%') native_plaque_ctr,
        concat(ifnull(round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click))/(sum(b.native_new_plaque_show)+sum(b.native_plaque_show))*100,2),0),'%') native_new_plaque_ctr,
        concat(ifnull(round(sum(b.plaque_video_click)/sum(b.plaque_video_show)*100,2),0),'%') plaque_video_ctr,
        concat(ifnull(round(sum(b.banner_click)/sum(b.banner_show)*100,2),0),'%') banner_ctr,
        concat(ifnull(round(sum(b.native_banner_click)/sum(b.native_banner_show)*100,2),0),'%') native_banner_ctr,
        concat(ifnull(round((sum(b.native_new_banner_click)+sum(b.native_banner_click))/(sum(b.native_new_banner_show)+sum(b.native_banner_show))*100,2),0),'%') native_new_banner_ctr,
        concat(ifnull(round(sum(b.video_click)/sum(b.video_show)*100,2),0),'%') video_ctr,
        concat(ifnull(round(sum(b.native_msg_click)/sum(b.native_msg_show)*100,2),0),'%') native_msg_ctr,
        concat(ifnull(round(sum(b.suspend_icon_click)/sum(b.suspend_icon_show)*100,2),0),'%') suspend_icon_ctr,
        concat(IFNULL(round((sum(banner_click)+sum(plaque_click)+sum(splash_click)+sum(native_banner_click)+sum(native_plaque_click)+sum(native_splash_click)+sum(video_click)+sum(plaque_video_click)
        +sum(native_msg_click)+sum(system_splash_click)+sum(native_new_plaque_click)+sum(native_new_banner_click)+sum(suspend_icon_click))/(sum(banner_show)+sum(plaque_show)+sum(splash_show)+sum(native_banner_show)+sum(native_plaque_show)+sum(native_splash_show)+sum(video_show)+sum(plaque_video_show)
        +sum(native_msg_show)+sum(system_splash_show)+sum(native_new_plaque_show)+sum(native_new_banner_show)+sum(suspend_icon_show))*100, 2), 0),'%') all_total_ctr,

        round(sum(b.splash_income)/sum(b.splash_click),3) splash_cpc,
        round(sum(b.native_splash_income)/sum(b.native_splash_click),3) native_splash_cpc,
        round(sum(b.plaque_income)/sum(b.plaque_click),3) plaque_cpc,
        round(sum(b.native_plaque_income)/sum(b.native_plaque_click),3) native_plaque_cpc,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income))/(sum(b.native_new_plaque_click)+sum(b.native_plaque_click)),3) native_new_plaque_cpc,
        round(sum(b.plaque_video_income)/sum(b.plaque_video_click),3) plaque_video_cpc,
        round(sum(b.banner_income)/sum(b.banner_click),3) banner_cpc,
        round(sum(b.native_banner_income)/sum(b.native_banner_click),3) native_banner_cpc,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income))/(sum(b.native_new_banner_click)+sum(b.native_banner_click)),3) native_new_banner_cpc,
        round(sum(b.video_income)/sum(b.video_click),3) video_cpc,
        round(sum(b.native_msg_income)/sum(b.native_msg_click),3) native_msg_cpc,
        round(sum(b.suspend_icon_income)/sum(b.suspend_icon_click),3) suspend_icon_cpc,
        round(sum(b.t_duration)/sum(b.actnum),0)  daily_duration,

        sum(banner_request) banner_request,
        sum(native_splash_request) native_splash_request,
        sum(native_new_plaque_request) + sum(native_plaque_request) native_new_plaque_request,
        sum(native_msg_request) native_msg_request,
        sum(video_request) video_request,
        sum(splash_request) splash_request,
        sum(plaque_video_request) plaque_video_request,
        sum(system_splash_request) system_splash_request,
        sum(suspend_icon_request) suspend_icon_request,
        sum(plaque_request) plaque_request,
        sum(native_new_banner_request) + sum(native_banner_request) native_new_banner_request,

        IFNULL(round((sum(banner_request)+sum(native_splash_request)+sum(native_new_plaque_request)+sum(native_plaque_request)+sum(native_msg_request)+sum(video_request)+sum(splash_request)
        +sum(plaque_video_request)+sum(system_splash_request)+sum(suspend_icon_request)+sum(plaque_request)+sum(native_new_banner_request)+sum(native_banner_request))/sum(b.actnum),2), 0) all_total_request,
        round((sum(b.system_splash_request)+sum(b.native_splash_request)+sum(splash_request))/sum(b.actnum),2) total_splash_request,
        round((sum(b.native_new_plaque_request)+sum(b.native_plaque_request)+sum(b.plaque_request))/sum(b.actnum),2) total_plaque_request,
        round(sum(b.plaque_video_request)/sum(b.actnum),2) total_plaque_video_request,
        round((sum(b.native_new_banner_request)+sum(b.native_banner_request)+sum(b.banner_request))/sum(b.actnum),2) total_banner_request,
        round(sum(b.video_request)/sum(b.actnum),2) total_video_request,
        round(sum(b.native_msg_request)/sum(b.actnum),2) total_native_msg_request,
        round(sum(b.suspend_icon_request)/sum(b.actnum),2) total_suspend_icon_request,

        round(sum(b.system_splash_request)/sum(b.actnum),2) system_splash_avg_request,
        round(sum(b.splash_request)/sum(b.actnum),2) splash_avg_request,
        round(sum(b.native_splash_request)/sum(b.actnum),2) native_splash_avg_request,
        round(sum(b.plaque_request)/sum(b.actnum),2) plaque_avg_request,
        round((sum(b.native_new_plaque_request)+sum(b.native_plaque_request))/sum(b.actnum),2) native_new_plaque_avg_request,
        round(sum(b.plaque_video_request)/sum(b.actnum),2) plaque_video_avg_request,
        round(sum(b.banner_request)/sum(b.actnum),2) banner_avg_request,
        round((sum(b.native_new_banner_request) + sum(b.native_banner_request))/sum(b.actnum),2) native_new_banner_avg_request,
        round(sum(b.video_request)/sum(b.actnum),2) video_avg_request,
        round(sum(b.native_msg_request)/sum(b.actnum),2) native_msg_avg_request,
        round(sum(b.suspend_icon_request)/sum(b.actnum),2) suspend_icon_avg_request,

        sum(banner_fill) banner_fill,
        sum(native_splash_fill) native_splash_fill,
        sum(native_new_plaque_fill) + sum(native_plaque_fill) native_new_plaque_fill,
        sum(native_msg_fill) native_msg_fill,
        sum(video_fill) video_fill,
        sum(splash_fill) splash_fill,
        sum(plaque_video_fill) plaque_video_fill,
        sum(system_splash_fill) system_splash_fill,
        sum(suspend_icon_fill) suspend_icon_fill,
        sum(plaque_fill) plaque_fill,
        sum(native_new_banner_fill) + sum(native_banner_fill) native_new_banner_fill,

        IFNULL(round((sum(banner_fill)+sum(plaque_fill)+sum(splash_fill)+sum(native_banner_fill)+sum(native_plaque_fill)+sum(native_splash_fill)+sum(video_fill)+sum(plaque_video_fill)
        +sum(native_msg_fill)+sum(system_splash_fill)+sum(native_new_plaque_fill)+sum(native_new_banner_fill)+sum(suspend_icon_fill))/sum(b.actnum),2), 0) all_total_avg_fill,
        round((sum(b.system_splash_fill)+sum(b.native_splash_fill)+sum(splash_fill))/sum(b.actnum),2) total_splash_avg_fill,
        round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill)+sum(b.plaque_fill))/sum(b.actnum),2) total_plaque_avg_fill,
        round(sum(b.plaque_video_fill)/sum(b.actnum),2) total_plaque_video_avg_fill,
        round((sum(b.native_new_banner_fill)+sum(b.native_banner_fill)+sum(b.banner_fill))/sum(b.actnum),2) total_banner_avg_fill,
        round(sum(b.video_fill)/sum(b.actnum),2) total_video_avg_fill,
        round(sum(b.native_msg_fill)/sum(b.actnum),2) total_native_msg_avg_fill,
        round(sum(b.suspend_icon_fill)/sum(b.actnum),2) total_suspend_icon_avg_fill,

        round(sum(b.system_splash_fill)/sum(b.actnum),2) system_splash_avg_fill,
        round(sum(b.splash_fill)/sum(b.actnum),2) splash_avg_fill,
        round(sum(b.native_splash_fill)/sum(b.actnum),2) native_splash_avg_fill,
        round(sum(b.plaque_fill)/sum(b.actnum),2) plaque_avg_fill,
        round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill))/sum(b.actnum),2) native_new_plaque_avg_fill,
        round(sum(b.plaque_video_fill)/sum(b.actnum),2) plaque_video_avg_fill,
        round(sum(b.banner_fill)/sum(b.actnum),2) banner_avg_fill,
        round((sum(b.native_new_banner_fill) + sum(b.native_banner_fill))/sum(b.actnum),2) native_new_banner_avg_fill,
        round(sum(b.video_fill)/sum(b.actnum),2) video_avg_fill,
        round(sum(b.native_msg_fill)/sum(b.actnum),2) native_msg_avg_fill,
        round(sum(b.suspend_icon_fill)/sum(b.actnum),2) suspend_icon_avg_fill,

        CONCAT(IFNULL(round((sum(banner_fill)+sum(plaque_fill)+sum(splash_fill)+sum(native_banner_fill)+sum(native_plaque_fill)+sum(native_splash_fill)+sum(video_fill)+sum(plaque_video_fill)
        +sum(native_msg_fill)+sum(system_splash_fill)+sum(native_new_plaque_fill)+sum(native_new_banner_fill)+sum(suspend_icon_fill))/
        (sum(banner_request)+sum(native_splash_request)+sum(native_new_plaque_request)+sum(native_plaque_request)+sum(native_msg_request)+sum(video_request)+sum(splash_request)
        +sum(plaque_video_request)+sum(system_splash_request)+sum(suspend_icon_request)+sum(plaque_request)+sum(native_new_banner_request)+sum(native_banner_request))*100,2), 0),'%') all_total_fill,
        CONCAT(IFNULL(round(sum(b.system_splash_fill)/sum(b.system_splash_request)*100,2),0),'%') total_system_splash_fill,
        CONCAT(IFNULL(round(sum(b.splash_fill)/sum(b.splash_request)*100,2),0),'%') total_splash_fill,
        CONCAT(IFNULL(round(sum(b.native_splash_fill)/sum(b.native_splash_request)*100,2),0),'%') total_native_splash_fill,
        CONCAT(IFNULL(round(sum(b.plaque_fill)/sum(b.plaque_request)*100,2),0),'%') total_plaque_fill,
        CONCAT(IFNULL(round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill))/(sum(b.native_new_plaque_request)+sum(b.native_plaque_request))*100,2),0),'%') total_native_new_plaque_fill,
        CONCAT(IFNULL(round(sum(b.plaque_video_fill)/sum(b.plaque_video_request)*100,2),0),'%') total_plaque_video_fill,
        CONCAT(IFNULL(round(sum(b.banner_fill)/sum(b.banner_request)*100,2),0),'%') total_banner_fill,
        CONCAT(IFNULL(round((sum(b.native_new_banner_fill)+sum(b.native_banner_fill))/(sum(b.native_new_banner_request)+sum(b.native_banner_request))*100,2),0),'%') total_native_new_banner_fill,
        CONCAT(IFNULL(round(sum(b.video_fill)/sum(b.video_request)*100,2),0),'%') total_video_fill,
        CONCAT(IFNULL(round(sum(b.native_msg_fill)/sum(b.native_msg_request)*100,2),0),'%') total_native_msg_fill,
        CONCAT(IFNULL(round(sum(b.suspend_icon_fill)/sum(b.suspend_icon_request)*100,2),0),'%') total_suspend_icon_fill,

        concat(IFNULL(round(sum(b.show_splash_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') show_splash_ad_active_cnt,
        concat(IFNULL(round(sum(b.show_plaque_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') show_plaque_ad_active_cnt,
        concat(IFNULL(round(sum(b.show_banner_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') show_banner_ad_active_cnt,
        concat(IFNULL(round(sum(b.show_video_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') show_video_ad_active_cnt,
        concat(IFNULL(round(sum(b.show_msg_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') show_msg_ad_active_cnt,
        concat(IFNULL(round(sum(b.show_icon_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') show_icon_ad_active_cnt,

        concat(IFNULL(round(sum(b.click_splash_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') click_splash_ad_active_cnt,
        concat(IFNULL(round(sum(b.click_plaque_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') click_plaque_ad_active_cnt,
        concat(IFNULL(round(sum(b.click_banner_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') click_banner_ad_active_cnt,
        concat(IFNULL(round(sum(b.click_video_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') click_video_ad_active_cnt,
        concat(IFNULL(round(sum(b.click_msg_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') click_msg_ad_active_cnt,
        concat(IFNULL(round(sum(b.click_icon_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') click_icon_ad_active_cnt,
        concat(IFNULL(round(sum(b.show_total_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') show_total_ad_active_cnt,
        concat(IFNULL(round(sum(b.click_total_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') click_total_ad_active_cnt

        from
        (
        SELECT c.*, d.daily_duration,
            TIME_TO_SEC(d.daily_duration) * d.actnum AS t_duration
		FROM ${tableName} c
        LEFT JOIN umeng_channel_total d ON c.tdate = d.tdate
        AND REPLACE(c.appkey,'_2','') = d.appkey
        AND c.channel = d.cname
        left join (SELECT a.appid, a.channel, a.packagename FROM( SELECT appid, channel, MAX(createTime) AS max_createTime FROM adv_platform_app_info WHERE bindEndTime >= CURRENT_DATE GROUP BY appid, channel) AS latest_app INNER JOIN adv_platform_app_info a ON latest_app.appid = a.appid AND latest_app.channel = a.channel AND latest_app.max_createTime = a.createTime) h
        on c.appid = h.appid and c.channel = h.channel
        WHERE c.tdate <![CDATA[>=]]> #{start_date} and c.tdate <![CDATA[<=]]> #{end_date}
        and concat(c.appid, c.channel) in
        (SELECT con_ac FROM ads_appid_channel_info
        <where>
            <if test="state != null and state != ''">
                state in (${state})
            </if>
        </where>)
        <if test="appid != null and appid != '' ">
            and c.appid in (${appid})
        </if>
        <if test="channel != null and channel != '' ">
            and c.channel in (${channel})
        </if>
        <if test="media != null and media != '' ">
            and c.media in (${media})
        </if>
        <if test="gameName != null and gameName != '' ">
            and c.gameName in (${gameName})
        </if>
        <if test="temp_id != null and temp_id != '' ">
            and c.temp_id like concat('%',#{temp_id},'%')
        </if>
        <if test="temp_name != null and temp_name != '' ">
            and c.temp_name like concat('%',#{temp_name},'%')
        </if>
        <if test="large_ver != null and large_ver != '' ">
            and c.large_ver like concat('%',#{large_ver},'%')
        </if>
        <if test="active_temp_id != null and active_temp_id != '' ">
            and c.active_temp_id like concat('%',#{active_temp_id},'%')
        </if>
        <if test="active_temp_name != null and active_temp_name != '' ">
            and c.active_temp_name like concat('%',#{active_temp_name},'%')
        </if>
        <if test="source != null and source != ''">
            and c.source = #{source}
        </if>
        <if test="packagename != null and packagename != ''">
            and h.packagename like concat('%',#{packagename},'%')
        </if>
        <if test="ad_violation_type != null and ad_violation_type != ''">
            <foreach collection="ad_violation_type.split(',')" item="type" open="and (" close=")" separator=" or ">
                FIND_IN_SET(${type},REPLACE(ad_violation_type, '|', ','))
            </foreach>
        </if>
        ) b
    </select>

    <select id="getAppChannelConfigList" parameterType="com.wbgame.pojo.custom.AppChannelConfigVo"
            resultType="com.wbgame.pojo.custom.AppChannelConfigVo">
        select  `account`, `channel`, `tttoken`, `ttappid`,`ttparam`,
                `token`, `dnappid`, `cname`, `createtime`,token atoken,company_type,note,create_time,create_user,
				modify_time,modify_user,status from app_channel_config where 1=1

        <if test="account != null and account != '' ">
            and account = #{account}
        </if>
        <if test="channel != null and channel != '' ">
            and channel in (${channel})
        </if>
        <if test="dnappid != null and dnappid != '' ">
            and dnappid like '%${dnappid}%'
        </if>
        <if test="company_type != null and company_type != '' ">
            and company_type in (${company_type})
        </if>
        <if test="note != null and note != '' ">
            and note like '%${note}%'
        </if>
       	<if test="status != null and status != ''">
            and  status= #{status}
        </if>
        <if test="cname != null and cname != '' ">
            and cname like '%${cname}%'
        </if>
        	order by create_time desc
    </select>

    <select id="selectUmengAdIncomeWarningList" parameterType="com.wbgame.pojo.UmengAdIncomeWarningVo"
            resultType="com.wbgame.pojo.advert.UmengAdIncomeReportVo">
        select
        b.tdate
        <if test="group != null and group != '' and group.contains('appkey') ">
            ,b.appid,b.appkey
        </if>
        <if test="group != null and group != '' and group.contains('channel') ">
            ,b.channel
        </if>
        <if test="group != null and group != '' and group.contains('packagename') ">
            ,packagename
        </if>
        ,b.appname, b.temp_id, b.temp_name,
        sum(b.addnum) addnum,sum(b.actnum) actnum,
        round(sum(b.addnum)/sum(b.actnum),4) avgnum,
        round(sum(b.native_msg_show)/sum(b.actnum),2) msg_pv,
        round(sum(b.banner_show)/sum(b.actnum),2) banner_pv,
        round(sum(b.plaque_show)/sum(b.actnum),2) plaque_pv,
        round(sum(b.native_msg_show)/sum(b.actnum),2) native_msg_pv,
        round((sum(b.native_new_banner_show) + sum(b.native_banner_show))/sum(b.actnum),2) native_new_banner_pv,
        round(sum(b.native_splash_show)/sum(b.actnum),2) native_splash_pv,
        round(sum(b.plaque_video_show)/sum(b.actnum),2) plaque_video_pv,
        round(sum(b.native_plaque_show)/sum(b.actnum),2) native_plaque_pv,
        round(sum(b.native_banner_show)/sum(b.actnum),2) native_banner_pv,
        round(sum(b.system_splash_show)/sum(b.actnum),2) system_splash_pv,
        round(sum(b.splash_show)/sum(b.actnum),2) splash_pv,
        round(sum(b.suspend_icon_show)/sum(b.actnum),2) suspend_icon_pv,
        round(sum(b.video_show)/sum(b.actnum),2) video_pv,
        round((sum(b.native_new_plaque_show)+sum(b.native_plaque_show))/sum(b.actnum),2) native_new_plaque_pv,
        round((sum(b.system_splash_show)+sum(b.native_splash_show)+sum(splash_show))/sum(b.actnum),2) total_splash_pv,
        round((sum(b.native_new_plaque_show)+sum(b.native_plaque_show)+sum(b.plaque_show))/sum(b.actnum),2) total_plaque_pv,
        round(sum(b.plaque_video_show)/sum(b.actnum),2) total_plaque_video_pv,
        round((sum(b.native_new_banner_show)+sum(b.native_banner_show)+sum(b.banner_show))/sum(b.actnum),2) total_banner_pv,
        round(sum(b.video_show)/sum(b.actnum),2) total_video_pv,
        round(sum(b.native_msg_show)/sum(b.actnum),2) total_native_msg_pv,
        round(sum(b.suspend_icon_show)/sum(b.actnum),2) total_suspend_icon_pv,
        IFNULL(round((sum(banner_show)+sum(plaque_show)+sum(splash_show)+sum(native_banner_show)+sum(native_plaque_show)+sum(native_splash_show)+sum(video_show)+sum(plaque_video_show)
        +sum(native_msg_show)+sum(system_splash_show)+sum(native_new_plaque_show)+sum(native_new_banner_show)+sum(suspend_icon_show))/sum(b.actnum), 2), 0) all_total_pv,

        sum(banner_click) banner_click,
        sum(native_splash_click) native_splash_click,
        sum(native_plaque_click) native_plaque_click,
        sum(native_new_plaque_click) native_new_plaque_click,
        sum(native_msg_click) native_msg_click,
        sum(video_click) video_click,
        sum(splash_click) splash_click,
        sum(plaque_video_click) plaque_video_click,
        sum(system_splash_click) system_splash_click,
        sum(suspend_icon_click) suspend_icon_click,
        sum(plaque_click) plaque_click,
        sum(native_banner_click) native_banner_click,
        sum(native_new_banner_click) native_new_banner_click,

        round(sum(b.video_income)/sum(b.actnum),3) video_arpu,
        round(sum(b.native_new_plaque_income)/sum(b.actnum),3) native_new_plaque_arpu,
        round(sum(b.native_new_banner_income)/sum(b.actnum),3) native_new_banner_arpu,
        round(sum(b.banner_income)/sum(b.actnum),3) banner_arpu,
        round(sum(b.total_income)/sum(b.actnum),2) dau_arpu,
        round(sum(b.native_msg_income)/sum(b.actnum),3) msg_arpu,
        round(sum(b.splash_income)/sum(b.actnum),3) splash_arpu,
        round(sum(b.plaque_income)/sum(b.actnum),3) plaque_arpu,
        round(sum(b.plaque_video_income)/sum(b.actnum),3) plaque_video_arpu,
        round(sum(b.system_splash_income)/sum(b.actnum),3) system_splash_arpu,
        round(sum(b.suspend_icon_income)/sum(b.actnum),3) suspend_icon_arpu,
        round((sum(b.system_splash_income)+sum(b.native_splash_income)+sum(b.splash_income))/sum(b.actnum),3) total_splash_arpu,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income)+sum(b.plaque_income))/sum(b.actnum),3) total_plaque_arpu,
        round(sum(b.plaque_video_income)/sum(b.actnum),3) total_plaque_video_arpu,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income)+sum(b.banner_income))/sum(b.actnum),3) total_banner_arpu,
        round(sum(b.video_income)/sum(b.actnum),3) total_video_arpu,
        round(sum(b.native_msg_income)/sum(b.actnum),3) total_native_msg_arpu,
        round(sum(b.suspend_icon_income)/sum(b.actnum),3) total_suspend_icon_arpu,

        sum(native_splash_show) native_splash_show,
        sum(splash_show) splash_show,
        sum(native_new_banner_show) native_new_banner_show,
        sum(native_plaque_show) native_plaque_show,
        sum(native_new_plaque_show) native_new_plaque_show,
        sum(native_banner_show) native_banner_show,
        sum(banner_show) banner_show,
        sum(suspend_icon_show) suspend_icon_show,
        sum(system_splash_show) system_splash_show,
        sum(video_show) video_show,
        sum(plaque_video_show) plaque_video_show,
        sum(native_msg_show) native_msg_show,
        sum(plaque_show) plaque_show,

        IFNULL(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show),0) sum_total_banner,
        IFNULL(round(sum(banner_show)/(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show))*100,2),0) sum_pv_banner,
        IFNULL(round((sum(native_new_banner_show)+sum(native_banner_show))/(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show))*100,2),0) sum_pv_new_banner,

        IFNULL(round((sum(banner_click)+sum(plaque_click)+sum(splash_click)+sum(native_banner_click)+sum(native_plaque_click)+sum(native_splash_click)+sum(video_click)+sum(plaque_video_click)
        +sum(native_msg_click)+sum(system_splash_click)+sum(native_new_plaque_click)+sum(native_new_banner_click)+sum(suspend_icon_click))/(sum(banner_show)+sum(plaque_show)+sum(splash_show)+sum(native_banner_show)+sum(native_plaque_show)+sum(native_splash_show)+sum(video_show)+sum(plaque_video_show)
        +sum(native_msg_show)+sum(system_splash_show)+sum(native_new_plaque_show)+sum(native_new_banner_show)+sum(suspend_icon_show))*100, 2), 0) all_total_ctr,

        sum(native_splash_show)+sum(splash_show)+sum(native_new_banner_show)+sum(native_plaque_show)+sum(native_new_plaque_show)+sum(native_banner_show)+sum(banner_show)+sum(suspend_icon_show)+sum(system_splash_show)+sum(video_show)+sum(plaque_video_show)+sum(native_msg_show) +sum(plaque_show) all_total_show,

        round((sum(banner_income)*1000/sum(banner_show)),2) banner_ecpm,
        round((sum(native_banner_income)*1000/sum(native_banner_show)),2) native_banner_ecpm,
        round((sum(plaque_video_income)*1000/sum(plaque_video_show)),2) plaque_video_ecpm,
        round(((sum(native_new_plaque_income)+sum(native_plaque_income))*1000/(sum(native_new_plaque_show)+sum(native_plaque_show))),2) native_new_plaque_ecpm,
        round((sum(native_msg_income)*1000/sum(native_msg_show)),2) native_msg_ecpm,
        round((sum(native_splash_income)*1000/sum(native_splash_show)),2) native_splash_ecpm,
        round((sum(suspend_icon_income)*1000/sum(suspend_icon_show)),2) suspend_icon_ecpm,
        round((sum(splash_income)*1000/sum(splash_show)),2) splash_ecpm,
        round((sum(system_splash_income)*1000/sum(system_splash_show)),2) system_splash_ecpm,
        round((sum(native_plaque_income)*1000/sum(native_plaque_show)),2) native_plaque_ecpm,
        round((sum(video_income)*1000/sum(video_show)),2) video_ecpm,
        round((sum(plaque_income)*1000/sum(plaque_show)),2) plaque_ecpm,
        round(((sum(native_new_banner_income)+sum(native_banner_income))*1000/(sum(native_new_banner_show)+sum(native_banner_show))),2) native_new_banner_ecpm,

        round(sum(total_income),2) total_income,
        round(sum(native_plaque_income),2) native_plaque_income,
        round(sum(video_income),2) video_income,
        round(sum(native_msg_income),2) native_msg_income,
        round(sum(suspend_icon_income),2) suspend_icon_income,
        round(sum(splash_income),2) splash_income,
        round(sum(banner_income),2) banner_income,
        round(sum(plaque_video_income),2) plaque_video_income,
        round(sum(system_splash_income),2) system_splash_income,
        round(sum(native_banner_income),2) native_banner_income,
        round(sum(native_new_banner_income),2) native_new_banner_income,
        round(sum(native_splash_income),2) native_splash_income,
        round(sum(plaque_income),2) plaque_income,
        round(sum(native_new_plaque_income),2) native_new_plaque_income,

        ifnull(round(sum(b.splash_click)/sum(b.splash_show)*100,2),0) splash_ctr,
        ifnull(round(sum(b.native_splash_click)/sum(b.native_splash_show)*100,2),0) native_splash_ctr,
        ifnull(round(sum(b.plaque_click)/sum(b.plaque_show)*100,2),0) plaque_ctr,
        ifnull(round(sum(b.native_plaque_click)/sum(b.native_plaque_show)*100,2),0) native_plaque_ctr,
        ifnull(round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click))/(sum(b.native_new_plaque_show)+sum(b.native_plaque_show))*100,2),0) native_new_plaque_ctr,
        ifnull(round(sum(b.plaque_video_click)/sum(b.plaque_video_show)*100,2),0) plaque_video_ctr,
        ifnull(round(sum(b.banner_click)/sum(b.banner_show)*100,2),0) banner_ctr,
        ifnull(round(sum(b.native_banner_click)/sum(b.native_banner_show)*100,2),0) native_banner_ctr,
        ifnull(round((sum(b.native_new_banner_click)+sum(b.native_banner_click))/(sum(b.native_new_banner_show)+sum(b.native_banner_show))*100,2),0) native_new_banner_ctr,
        ifnull(round(sum(b.video_click)/sum(b.video_show)*100,2),0) video_ctr,
        ifnull(round(sum(b.native_msg_click)/sum(b.native_msg_show)*100,2),0) native_msg_ctr,
        ifnull(round(sum(b.suspend_icon_click)/sum(b.suspend_icon_show)*100,2),0) suspend_icon_ctr,
        ifnull(round(sum(b.splash_click)/sum(b.splash_show)*100,2),0) + ifnull(round(sum(b.native_splash_click)/sum(b.native_splash_show)*100,2),0) as total_splash_ctr,

        round(sum(b.splash_income)/sum(b.splash_click),3) splash_cpc,
        round(sum(b.native_splash_income)/sum(b.native_splash_click),3) native_splash_cpc,
        round(sum(b.plaque_income)/sum(b.plaque_click),3) plaque_cpc,
        round(sum(b.native_plaque_income)/sum(b.native_plaque_click),3) native_plaque_cpc,
        round(sum(b.native_new_plaque_income)/sum(b.native_new_plaque_click),3) native_new_plaque_cpc,
        round(sum(b.plaque_video_income)/sum(b.plaque_video_click),3) plaque_video_cpc,
        round(sum(b.banner_income)/sum(b.banner_click),3) banner_cpc,
        round(sum(b.native_banner_income)/sum(b.native_banner_click),3) native_banner_cpc,
        round(sum(b.native_new_banner_income)/sum(b.native_new_banner_click),3) native_new_banner_cpc,
        round(sum(b.video_income)/sum(b.video_click),3) video_cpc,
        round(sum(b.native_msg_income)/sum(b.native_msg_click),3) native_msg_cpc,
        round(sum(b.suspend_icon_income)/sum(b.suspend_icon_click),3) suspend_icon_cpc,
        ROUND(SUM(SUBSTRING_INDEX(b.daily_duration,':',1)*3600+SUBSTRING_INDEX (SUBSTRING_INDEX(b.daily_duration,':',2),':',-1)*60+SUBSTRING_INDEX(b.daily_duration,':',-1))/count(if(b.daily_duration!='',true,null)),0) daily_duration
        from
        (
        SELECT
        c.tdate,c.appkey,c.temp_id, c.temp_name,c.channel,e.packagename,c.actnum,c.total_income,c.dau_arpu,c.banner_pv,c.plaque_pv,c.splash_pv,c.video_pv,
        c.banner_arpu,c.plaque_arpu,c.splash_arpu,c.video_arpu,c.banner_ecpm,c.plaque_ecpm,c.splash_ecpm,c.native_banner_ecpm,
        c.native_plaque_ecpm,c.native_splash_ecpm,c.video_ecpm,c.plaque_video_ecpm,c.banner_show,c.plaque_show,c.splash_show,
        c.native_banner_show,c.native_plaque_show,c.native_splash_show,c.video_show,c.plaque_video_show,c.banner_income,c.plaque_income,
        c.splash_income,c.native_banner_income,c.native_plaque_income,c.native_splash_income,c.video_income,c.plaque_video_income,c.msg_pv,
        c.msg_arpu,c.native_msg_ecpm,c.native_msg_show,c.native_msg_income,c.plaque_video_pv,c.plaque_video_arpu,c.addnum,c.avgnum,c.banner_click,
        c.plaque_click,c.splash_click,c.native_banner_click,c.native_plaque_click,c.native_splash_click,c.video_click,c.plaque_video_click,
        c.system_splash_show,c.native_new_plaque_show,c.native_new_banner_show,c.suspend_icon_show,c.system_splash_pv,c.native_new_plaque_pv,
        c.native_new_banner_pv,c.suspend_icon_pv,c.system_splash_arpu,c.native_new_plaque_arpu,c.native_new_banner_arpu,c.suspend_icon_arpu,
        c.system_splash_ecpm,c.native_new_plaque_ecpm,c.native_new_banner_ecpm,c.suspend_icon_ecpm,c.system_splash_income,c.native_new_plaque_income,
        c.native_new_banner_income,c.suspend_icon_income,c.system_splash_click,c.native_new_plaque_click,c.native_new_banner_click,c.suspend_icon_click,
        c.native_plaque_pv,c.native_banner_pv,c.native_msg_click,
        d.daily_duration,f.id appid,f.app_name appname FROM ${tableName} c
        left join app_info f on c.appkey = f.umeng_key
        LEFT JOIN umeng_channel_total d ON c.tdate = d.tdate
        AND c.appkey = d.appkey
        AND c.channel = d.cname
        left join (SELECT a.appid, a.channel, a.packagename FROM(SELECT appid, channel, MAX(createTime) AS max_createTime FROM adv_platform_app_info WHERE bindEndTime >= CURRENT_DATE GROUP BY appid, channel) AS latest_app INNER JOIN adv_platform_app_info a ON latest_app.appid = a.appid AND latest_app.channel = a.channel AND latest_app.max_createTime = a.createTime) e
        on c.appid = e.appid and c.channel = e.channel
        WHERE ${addNumCondition}
        and c.source = 1
        and  c.tdate <![CDATA[>=]]> #{start_date}
        and c.tdate <![CDATA[<=]]> #{end_date}
        and c.tdate <![CDATA[>=]]> DATE_SUB(CURRENT_DATE(), INTERVAL 31 DAY)
        and c.tdate <![CDATA[<=]]> curdate()
        ) b
        where 1=1
        and b.tdate <![CDATA[>=]]> #{start_date}
        and b.tdate <![CDATA[<=]]> #{end_date}
        and b.tdate <![CDATA[>=]]> DATE_SUB(CURRENT_DATE(), INTERVAL 31 DAY)
        and b.tdate <![CDATA[<=]]> curdate()
        <if test="appid != null and appid != '' ">
            and b.appid in (${appid})
        </if>
        <if test="channel != null and channel != '' ">
            and b.channel in (${channel})
        </if>
        <if test="packagename != null and packagename != '' ">
            and packagename like concat('%',#{packagename},'%')
        </if>
        <if test="temp_id != null and temp_id != '' ">
            and b.temp_id like concat('%',#{temp_id},'%')
        </if>
        <if test="temp_name != null and temp_name != '' ">
            and b.temp_name like concat('%',#{temp_name},'%')
        </if>
        <if test="con_ac != null and con_ac != ''">
            and concat(b.appid, b.channel) in (${con_ac})
        </if>

        <if test="appid_tag != null and appid_tag != ''">
            <choose>
                <when test="appid_tag_rev != null and appid_tag_rev != ''">
                    AND CONCAT(b.appid,'#',b.channel) not in (${appid_tag})
                </when>
                <otherwise>
                    AND CONCAT(b.appid,'#',b.channel) in (${appid_tag})
                </otherwise>
            </choose>
        </if>

        <if test="group != null and group != '' ">
            group by ${group}
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by b.tdate asc,b.addnum+0 desc
            </otherwise>
        </choose>
    </select>
</mapper>