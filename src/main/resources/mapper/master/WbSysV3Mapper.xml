<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.WbSysV3Mapper">

	<select id="selectSysmarkMenu" parameterType="com.wbgame.pojo.CurrMenuVo" resultType="com.wbgame.pojo.CurrMenuVo">
		SELECT * FROM main_sys_menu_v3 where 1=1
		<if test="sys_mark != null and sys_mark != ''"> and sys_mark = #{sys_mark} </if>
		<if test="index != null and index != ''"> and `index` = #{index} </if>
		<if test="title != null and title != ''"> and `title` like concat('%',#{title},'%') </if>
		<if test="slot != null and slot != ''"> and `slot` like concat('%',#{slot},'%') </if>
		<!-- ORDER BY list_number ASC -->
	</select>
	
	<insert id="insertSysmarkMenu" parameterType="com.wbgame.pojo.CurrMenuVo">
		INSERT INTO main_sys_menu_v3 (  
			sys_mark,`index`,title,off,icon,slot,custom,list_number,fm_config,cuser,createtime
		) VALUES (
			#{sys_mark},#{index},#{title},#{off},#{icon},#{slot},#{custom},#{list_number},#{fm_config},#{cuser},now()
				)
	</insert>
	
	<update id="updateSysmarkMenu" parameterType="com.wbgame.pojo.CurrMenuVo">
		UPDATE main_sys_menu_v3 SET
			euser = #{euser},endtime = now(),
			sys_mark = #{sys_mark},
			`index`= #{index},
			`title`=#{title} 
		<if test="off != null and off != ''">,`off`=#{off} </if>
		<if test="icon != null and icon != ''">,`icon`=#{icon} </if>
		<if test="slot != null ">,`slot`=#{slot} </if>
		<if test="custom != null and custom != ''">,`custom`=#{custom} </if>
		<if test="list_number != null and list_number != ''">,`list_number`=#{list_number} </if>
		<if test="fm_config != null and fm_config != ''">,`fm_config`=#{fm_config} </if>
		WHERE id = #{id}
	</update>
	
	<delete id="deleteSysmarkMenu" parameterType="com.wbgame.pojo.CurrMenuVo">
		DELETE FROM main_sys_menu_v3 WHERE id = #{id}
	</delete>
	
	<!-- 获取权限列表  -->
	<select id="selectSysmarkOrg" parameterType="com.wbgame.pojo.CurrOrgVo" resultType="com.wbgame.pojo.CurrOrgVo">
		SELECT * FROM main_sys_org_v3 where 1=1 
		<if test="cuser != null and cuser != ''"> and cuser = #{cuser} </if>
		<if test="org_id != null and org_id != ''"> and org_id = #{org_id} </if>
		<if test="org_name != null and org_name != ''"> and org_name like concat('%',#{org_name},'%') </if>
		<if test="hidden_menu_list != null and hidden_menu_list != ''"> and `hidden_menu_list` like concat('%',#{hidden_menu_list},'%') </if>
		<if test="page_list != null and page_list != ''"> and `page_list` like concat('%',#{page_list},'%') </if>
		ORDER BY orderTime ASC
	</select>
	
	<insert id="insertSysmarkOrg" parameterType="com.wbgame.pojo.CurrOrgVo">
		INSERT INTO main_sys_org_v3 ( 
			org_id,org_name,hidden_menu_list,page_list,orderTime,cuser,euser,createtime,endtime
		) VALUES (
			#{org_id},#{org_name},#{hidden_menu_list},#{page_list},unix_timestamp(),#{cuser},#{euser},now(),now() 
		)
	</insert>
	
	<update id="updateSysmarkOrg" parameterType="com.wbgame.pojo.CurrOrgVo">
		UPDATE main_sys_org_v3 SET 
			org_name = #{org_name},
			endtime = now()
		<if test="euser != null and euser != ''">,euser=#{euser} </if>
		<if test="hidden_menu_list != null and hidden_menu_list != ''">,`hidden_menu_list`=#{hidden_menu_list} </if>
		<if test="page_list != null and page_list != ''">,`page_list`=#{page_list} </if>
		<if test="cuser != null and cuser != ''">,cuser=#{cuser} </if>
		WHERE org_id = #{org_id}
	</update>
	
	<delete id="deleteSysmarkOrg" parameterType="com.wbgame.pojo.CurrOrgVo">
		DELETE FROM main_sys_org_v3 WHERE org_id = #{org_id} 
	</delete>
	
	<!-- 获取角色列表  -->
	<select id="selectSysmarkRole" parameterType="com.wbgame.pojo.CurrRoleVo" resultType="com.wbgame.pojo.CurrRoleVo">
		SELECT * FROM main_sys_role_v3 where 1=1 
		<if test="cuser != null and cuser != ''"> and cuser = #{cuser} </if>
		<if test="role_id != null and role_id != ''"> and role_id in (${role_id}) </if>
		<if test="role_name != null and role_name != ''"> and role_name like concat('%',#{role_name},'%') </if>
		<if test="appid != null and appid != ''"> and appid like concat('%',#{appid},'%') </if>
		<if test="cha_id != null and cha_id != ''"> and cha_id like concat('%',#{cha_id},'%') </if>
		<if test="cha_type != null and cha_type != ''"> and cha_type like concat('%',#{cha_type},'%') </if>
		<if test="cha_media != null and cha_media != ''"> and cha_media like concat('%',#{cha_media},'%') </if>
		<if test="account_group != null and account_group != ''"> and account_group like concat('%',#{account_group},'%') </if>
	</select>
	
	<insert id="insertSysmarkRole" parameterType="com.wbgame.pojo.CurrRoleVo">
		INSERT INTO main_sys_role_v3 ( 
			role_id, role_name, little_appid, appid, cha_id, custom, groupapp, cha_type, cha_media, account_group, app_category, cuser,euser,createtime,endtime
		) VALUES (
			#{role_id},#{role_name},#{little_appid},#{appid},#{cha_id},#{custom},#{groupapp},#{cha_type},#{cha_media},#{account_group},#{app_category}, #{cuser},#{euser},now(),now() 
		)
	</insert>
	
	<update id="updateSysmarkRole" parameterType="com.wbgame.pojo.CurrRoleVo">
		UPDATE main_sys_role_v3 SET 
			role_name = #{role_name},
			groupapp = #{groupapp},
			cha_type = #{cha_type},
			cha_media = #{cha_media},
			endtime = now()
		<if test="euser != null and euser != ''">,euser=#{euser} </if>
		<if test="little_appid != null ">,little_appid=#{little_appid} </if>
		<if test="appid != null ">,appid=#{appid} </if>
		<if test="cha_id != null ">,cha_id=#{cha_id} </if>
		<if test="account_group != null ">,account_group=#{account_group} </if>
		<if test="app_category != null ">,app_category=#{app_category} </if>
		<if test="custom != null ">,custom=#{custom} </if>
		<if test="cuser != null and cuser != ''">,cuser=#{cuser} </if>
		WHERE role_id = #{role_id}
	</update>
	
	<delete id="deleteSysmarkRole" parameterType="com.wbgame.pojo.CurrRoleVo">
		DELETE FROM main_sys_role_v3 WHERE role_id = #{role_id} 
	</delete>
	
	<!-- 获取用户列表  -->
	<select id="selectSysmarkUser" parameterType="com.wbgame.pojo.CurrUserVo" resultType="com.wbgame.pojo.CurrUserVo">
		SELECT uu.*,cc.org_name,cc.hidden_menu_list,cc.page_list,dd.* 
		FROM main_sys_user_v3 uu,main_sys_org_v3 cc,main_sys_department_v3 dd
		WHERE uu.org_id=cc.org_id and uu.department=dd.id
		<if test="login_name != null and login_name != ''"> and login_name = #{login_name} </if>
		<if test="org_id != null and org_id != ''"> and uu.org_id = #{org_id} </if>
		<if test="role_id != null and role_id != ''"> and role_id like concat('%',#{role_id},'%') </if>
		<if test="nick_name != null and nick_name != ''"> and nick_name like concat('%',#{nick_name},'%') </if>
		<if test="department != null and department != ''"> and (department=#{department} or dd.parent_id=#{department}) </if>
		<if test="company != null and company != ''"> and company = #{company} </if>
	</select>
	
	<insert id="insertSysmarkUser" parameterType="com.wbgame.pojo.CurrUserVo">
		INSERT INTO main_sys_user_v3 ( 
			login_name,`password`,nick_name,org_id,role_id,company,mach_code,email,department 
		) VALUES (
			#{login_name},#{password},#{nick_name},#{org_id},#{role_id},#{company},#{mach_code},#{email},#{department}    
		)
	</insert>
	
	<update id="updateSysmarkUser" parameterType="com.wbgame.pojo.CurrUserVo">
		UPDATE main_sys_user_v3 SET 
			nick_name = #{nick_name},
			org_id = #{org_id},
			role_id = #{role_id},
			company = #{company},
			mach_code = #{mach_code},
			email = #{email},
			euser = #{euser}
		<if test="department != null and department != ''">,department=#{department} </if>
		<if test="password != null and password != ''">,password=#{password} </if>
		WHERE login_name = #{login_name} 
	</update>
	
	<delete id="deleteSysmarkUser" parameterType="com.wbgame.pojo.CurrUserVo">
		DELETE FROM main_sys_user_v3 WHERE login_name = #{login_name}
	</delete>
	
	
	<!-- 获取前端错误日志列表  -->
	<select id="selectClientErrorLog" parameterType="com.wbgame.pojo.ClientErrorLogVo" resultType="com.wbgame.pojo.ClientErrorLogVo">
		SELECT * FROM client_sys_error_log where 1=1
		<if test="type != null and type != ''"> and `type` = #{type} </if>
		<if test="select != null and select != ''"> and `select` like concat('%',#{select},'%') </if>
		<if test="new_use != null and new_use != ''"> and new_use = #{new_use} </if>
		<if test="div_use != null and div_use != ''"> and div_use = #{div_use} </if>
		<if test="new_time != null and new_time != ''"> and new_time = #{new_time} </if>
		<if test="div_time != null and div_time != ''"> and div_time = #{div_time} </if>
	</select>

	<insert id="insertClientErrorLog" parameterType="com.wbgame.pojo.ClientErrorLogVo">
		INSERT INTO client_sys_error_log (
			`type`, `select`, `data`, new_use, div_use, new_time, div_time
		) VALUES (
			#{type},#{select},#{data},#{new_use},#{div_use},#{new_time},#{div_time}
		)
	</insert>

	<update id="updateClientErrorLog" parameterType="com.wbgame.pojo.ClientErrorLogVo">
		UPDATE client_sys_error_log SET
			`type` = #{type},
			`select` = #{select}
		<if test="data != null and data != ''">,`data`=#{data} </if>
		<if test="new_use != null and new_use != ''">,new_use=#{new_use} </if>
		<if test="div_use != null and div_use != ''">,div_use=#{div_use} </if>
		<if test="new_time != null and new_time != ''">,new_time=#{new_time} </if>
		<if test="div_time != null and div_time != ''">,div_time=#{div_time} </if>
		WHERE id = #{id}
	</update>

	<delete id="deleteClientErrorLog" parameterType="com.wbgame.pojo.ClientErrorLogVo">
		DELETE FROM client_sys_error_log WHERE id = #{id}
	</delete>

	<select id="selectArtOrgUserInfo" parameterType="com.wbgame.pojo.CurrUserVo" resultType="com.wbgame.pojo.CurrUserVo">
		select * from main_sys_user_v3 where org_id in ('art','org_tfms')
	</select>

	<select id="selectSysmarkMenuConfig" parameterType="com.wbgame.pojo.WbMenuConfigVo" resultType="com.wbgame.pojo.WbMenuConfigVo">
		SELECT bb.*,cc.ai_number FROM main_sys_menu_v3 bb
		LEFT JOIN
		(select ctype,COUNT(1) ai_number FROM wx_action_record where cdatetime BETWEEN '${sdate} 00:00:00' AND '${edate} 23:59:59'
		group by ctype) cc
		ON bb.`index`=cc.ctype
		<where>
			<if test="sys_mark != null and sys_mark != ''"> and sys_mark = #{sys_mark} </if>
			<if test="index != null and index != ''"> and `index` like concat('%',#{index},'%') </if>
			<if test="title != null and title != ''"> and `title` like concat('%',#{title},'%') </if>
			<if test="slot != null and slot != ''"> and `slot` like concat('%',#{slot},'%') </if>
			<if test="config_list != null and config_list.length > 0">
				and (
				<foreach collection="config_list" item="item" separator=" and ">
					fm_config like concat('%',#{item},'%')
				</foreach>
				)
			</if>
		</where>

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by id desc
			</otherwise>
		</choose>
	</select>


</mapper>