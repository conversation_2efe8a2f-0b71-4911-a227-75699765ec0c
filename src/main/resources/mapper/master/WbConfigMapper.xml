<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.WbConfigMapper">

	<select id="selectWxAppConfig" parameterType="com.wbgame.pojo.wbsys.WxAppConfigVo" 
					resultType="com.wbgame.pojo.wbsys.WxAppConfigVo">
					
		select * from wx_app_config where 1=1
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="pid_name != null and pid_name != ''">
			and pid_name = #{pid_name}
		</if>
	</select>

	<insert id="insertWxAppConfig" parameterType="com.wbgame.pojo.wbsys.WxAppConfigVo">
		insert into wx_app_config(
			pid,
			appid,
			app_secret,
			app_name,
			pid_name,
			project_status,
			project_ver,
			plaque_rate,
			ad_flag,
			cash_flag,
			game_config,
			late_config
		) values(
			#{pid},
			#{appid},
			#{app_secret},
			#{app_name},
			#{pid_name},
			#{project_status},
			#{project_ver},
			#{plaque_rate},
			#{ad_flag},
			#{cash_flag},
			#{game_config},
			#{late_config}
		)
		
	</insert>
	<update id="updateWxAppConfig" parameterType="com.wbgame.pojo.wbsys.WxAppConfigVo">
		update wx_app_config set 
			appid = #{appid},
			app_secret = #{app_secret},
			app_name = #{app_name},
			pid_name = #{pid_name},
			project_status = #{project_status},
			project_ver = #{project_ver},
			plaque_rate = #{plaque_rate},
			ad_flag = #{ad_flag},
			cash_flag = #{cash_flag},
			game_config = #{game_config},
			late_config = #{late_config}
		where pid = #{pid}
	</update>
	
	
	<select id="selectWxShareConfig" parameterType="com.wbgame.pojo.WxSharePushVo" 
					resultType="com.wbgame.pojo.WxSharePushVo">
					
		select * from wx_share_push where 1=1 
		<if test="appid != null and appid != ''">
			and appid = #{appid}
		</if>
		<if test="channel != null and channel != ''">
			and channel = #{channel}
		</if>
		<if test="status != null and status != ''">
			and status = #{status}
		</if>
		order by id desc
	</select>

	<insert id="insertWxShareConfig" parameterType="com.wbgame.pojo.WxSharePushVo">
		insert into wx_share_push(
			appid,
			channel,
			placement,
			title,
			image_url,
			query,
			limit_num,
			param,
			status
		) values(
			#{appid},
			#{channel},
			#{placement},
			#{title},
			#{image_url},
			#{query},
			#{limit_num},
			#{param},
			#{status}
		)
	</insert>
	<update id="updateWxShareConfig" parameterType="com.wbgame.pojo.WxSharePushVo">
		update wx_share_push set 
			appid = #{appid},
			channel = #{channel},
			placement = #{placement},
			title = #{title},
			image_url = #{image_url},
			query = #{query},
			limit_num = #{limit_num},
			param = #{param},
			status = #{status}
		where id = #{id}
	</update>
	<delete id="deleteWxShareConfig" parameterType="com.wbgame.pojo.WxSharePushVo">
		delete from wx_share_push where id in (${id})
	</delete>
	
	<select id="selectWxChannelManage" parameterType="com.wbgame.pojo.wbsys.WxChannelMaganeVo" 
					resultType="com.wbgame.pojo.wbsys.WxChannelMaganeVo">
					
		select * from wx_channel_manage where 1=1 
		<if test="cname != null and cname != ''">
			and cname like concat('%',#{cname},'%') 
		</if>
		<if test="appid != null and appid != ''">
			and appid = #{appid}
		</if>
		<if test="cid != null and cid != ''">
			and cid = #{cid}
		</if>
		order by createtime desc
	</select>
	<select id="selectWxProjectManage" parameterType="com.wbgame.pojo.wbsys.WxProjectMaganeVo" 
					resultType="com.wbgame.pojo.wbsys.WxProjectMaganeVo">
					
		select * from wx_project_manage where 1=1 
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="creater != null and creater != ''">
			and creater = #{creater}
		</if>
		<if test="pname != null and pname != ''">
			and pname like concat('%',#{pname},'%') 
		</if>
		<if test="appname != null and appname != ''">
			and appname like concat('%',#{appname},'%') 
		</if>
		order by createtime desc
	</select>
	
	
	<select id="selectWxIconConfig" parameterType="java.util.Map" 
					resultType="com.wbgame.pojo.WxIconPushVo">
					
		select * from wx_icon_push where status != 2
		<if test="id != null and id != ''">
			and id = #{id}
		</if>
		<if test="appid != null and appid != ''">
			and appid = #{appid}
		</if>
		<if test="channel != null and channel != ''">
			and channel = #{channel}
		</if>
		<if test="imgtype != null and imgtype != ''">
			and imgtype = #{imgtype}
		</if>
		<if test="game_name != null and game_name != ''">
			and game_name like '%${game_name}%'
		</if>
		<if test="status != null and status != ''">
			and status = #{status}
		</if>
		<if test="push_appid != null and push_appid != ''">
			and push_appid = #{push_appid}
		</if>
		<if test="param != null and param != ''">
			<if test="param == 'general'">
				and param != 'android' and param != 'ios'
			</if>
			<if test="param != 'general'">
				and param = #{param}
			</if>
		</if>
		order by id desc
	</select>

	<insert id="insertWxIconConfig" parameterType="com.wbgame.pojo.WxIconPushVo"
			useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into wx_icon_push(
			id,
			appid,
			channel,
			imgtype,
			icon,
			plist,
			push_image,
			push_appid,
			open,
			open_path,
			level,
			status,
			game_name,
			game_introduce,
			game_star,
			extra,
			placement,
			placement_name,
			param,
			extraData
		) values(
			#{id},
			#{appid},
			#{channel},
			#{imgtype},
			#{icon},
			#{plist},
			#{push_image},
			#{push_appid},
			#{open},
			#{open_path},
			#{level},
			#{status},
			#{game_name},
			#{game_introduce},
			#{game_star},
			#{extra},
			#{placement},
			#{placement_name},
			#{param},
			#{extraData}
		)
	</insert>
	<insert id="insertWxIconConfigList" parameterType="com.wbgame.pojo.WxIconPushVo">
		insert into wx_icon_push(
			appid,
			channel,
			imgtype,
			icon,
			plist,
			push_image,
			push_appid,
			open,
			open_path,
			`level`,
			status,
			game_name,
			game_introduce,
			game_star,
			extra,
			placement,
			placement_name,
			param,
			extraData
		) values
			<foreach collection="list" item="li" separator=",">
				(#{li.appid},
				#{li.channel},
				#{li.imgtype},
				#{li.icon},
				#{li.plist},
				#{li.push_image},
				#{li.push_appid},
				#{li.open},
				#{li.open_path},
				#{li.level},
				#{li.status},
				#{li.game_name},
				#{li.game_introduce},
				#{li.game_star},
				#{li.extra},
				#{li.placement},
				#{li.placement_name},
				#{li.param},
				#{li.extraData})
			</foreach>
	</insert>
	
	<update id="updateWxIconConfig" parameterType="com.wbgame.pojo.WxIconPushVo">
		update wx_icon_push set 
			appid = #{appid},
			channel = #{channel},
			imgtype = #{imgtype},
			icon = #{icon},
			plist = #{plist},
			push_image = #{push_image},
			push_appid = #{push_appid},
			open = #{open},
			open_path = #{open_path},
			level = #{level},
			status = #{status},
			game_name = #{game_name},
			game_introduce = #{game_introduce},
			game_star = #{game_star},
			extra = #{extra},
			placement = #{placement},
			placement_name = #{placement_name},
			param = #{param},
			extraData = #{extraData} 
		where id = #{id}
	</update>
	
	
	<select id="selectWxLinkConfig" parameterType="com.wbgame.pojo.WxLinkPushVo" 
					resultType="com.wbgame.pojo.WxLinkPushVo">
					
		select * from wx_link_push where 1=1 
		<if test="appid != null and appid != ''">
			and appid = #{appid}
		</if>
		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		<if test="channel != null and channel != ''">
			and channel = #{channel}
		</if>
		<if test="status != null and status != ''">
			and status = #{status}
		</if>
		order by id desc
	</select>

	<insert id="insertWxLinkConfig" parameterType="com.wbgame.pojo.WxLinkPushVo"
		useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into wx_link_push(
			appid,
			pid,
			channel,
			link,
			ver,
			status
		) values(
			#{appid},
			#{pid},
			#{channel},
			#{link},
			#{ver},
			#{status}
		)
	</insert>
	<update id="updateWxLinkConfig" parameterType="com.wbgame.pojo.WxLinkPushVo">
		update wx_link_push set 
			appid = #{appid},
			pid = #{pid},
			channel = #{channel},
			link = #{link},
			ver = #{ver},
			status = #{status}
		where id = #{id}
	</update>
	<delete id="deleteWxLinkConfig" parameterType="com.wbgame.pojo.WxLinkPushVo">
		delete from wx_link_push where id in (${id})
	</delete>
	
	
	<select id="selectWxActionRecord" parameterType="java.util.Map" 
		resultType="com.wbgame.pojo.wbsys.WxActionRecord">
					
		select xx.*,yy.title from wx_action_record xx left join (select sys_mark,`index`,title from main_sys_menu_v3) yy
		ON xx.ctype=yy.`index` 
			where cdatetime BETWEEN '${sdate} 00:00:00' AND '${edate} 23:59:59'
			<if test="cuser != null and cuser != ''">
				and cuser = #{cuser} 
			</if>
			<if test="ctype != null and ctype != ''">
				and ctype in (${ctype})  
			</if>
			<if test="cpage != null and cpage != ''">
				and cpage like concat('%',#{cpage},'%') 
			</if>
			<if test="title != null and title != ''">
				and yy.title like concat('%',#{title},'%') 
			</if>
			<if test="params != null and params != ''">
				and params like concat('%',#{params},'%') 
			</if>
			<if test="respBody != null and respBody != ''">
				and respBody like concat('%',#{respBody},'%') 
			</if>
			<if test="check != null and check != ''">
				and (${check}) 
			</if>
			
			and (cpage != '/wb/pageTemplateHandle' or params not like '%handle:check%') 
			and  (cpage not like '/adv2/%' or cpage not like '%/select%') 
			and  (cpage not like '/adv2/%' or cpage not like '%/get%') 
		order by cdatetime desc
	</select>

	<update id="updateAppInfo" parameterType="com.wbgame.pojo.AppInfoVo">
		update app_info 
		<set>
			umeng_key = #{umeng_key},sync_umeng = #{sync_umeng},
			<if test="app_name != null and app_name != ''">
				app_name = #{app_name},
			</if>
			<if test="os_type != null and os_type != ''">
				os_type = #{os_type},
			</if>
			<if test="app_category != null and app_category != ''">
				app_category = #{app_category},
			</if>
			<if test="xyx_id != null and xyx_id != ''">
				xyx_id = #{xyx_id},
			</if>
			<if test="reyun_key != null and reyun_key != ''">
				reyun_key = #{reyun_key},
			</if>
			<if test="bus_category != null and bus_category != ''">
				bus_category = #{bus_category},
			</if>
			<if test="two_app_category != null and two_app_category != ''">
				two_app_category = #{two_app_category},
			</if>
			<if test="cp != null and cp != ''">
				cp = #{cp},
			</if>
		</set>
		
		where id = #{id}
	</update>

	<select id="getCategoryByBus" parameterType="java.lang.String" resultType="com.wbgame.pojo.wbsys.CategoryVo">
		select a.app_category,b.name as category_name
		from app_info a inner join app_category b on a.app_category = b.id
		where 1=1
		<if test="bus_category != null and bus_category != ''">
			and bus_category = #{bus_category}
		</if>
		group by a.app_category
	</select>
	
</mapper>