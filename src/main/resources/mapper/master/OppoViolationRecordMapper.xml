<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.OppoViolationRecordMapper">


    <insert id="batchInsert" parameterType="java.util.List">
        replace into oppo_violation_record (
            message_id,tdate,company,platform,appid,appname,channel,online_name,tappid,package_name,adsense_id,
            sdk_adtype,open_type,bidding_mode,last_month_revenue,last_month_show,last_month_click,score,amount,
            `type`,dimension,violation_content,handle_time,subject,content,violation_date,adsid,reduced_amount
        ) values
        <foreach collection="storeList" item="li" separator=",">
            (#{li.message_id},#{li.tdate},#{li.company},#{li.platform},#{li.appid},#{li.appname},#{li.channel},#{li.online_name},#{li.tappid},#{li.package_name},#{li.adsense_id},
            #{li.sdk_adtype},#{li.open_type},#{li.bidding_mode},#{li.last_month_revenue},#{li.last_month_show},#{li.last_month_click},#{li.score},#{li.amount},
            #{li.type},#{li.dimension},#{li.violation_content},#{li.handle_time},#{li.subject},#{li.content},#{li.violation_date},#{li.adsid},#{li.reduced_amount})
        </foreach>
    </insert>


    <select id="queryList" resultType="com.wbgame.pojo.OppoViolationRecord">
        select * from oppo_violation_record
        <where>
            <if test="company != null and company != ''">
                and company in (${company})
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="online_name != null and online_name != ''">
                and online_name in (${online_name})
            </if>
            <if test="tappid != null and tappid != ''">
                and tappid like concat('%',#{tappid},'%')
            </if>
            <if test="package_name != null and package_name != ''">
                and package_name like concat('%',#{package_name},'%')
            </if>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                and tdate between concat(#{startTime},' 00:00:00') and concat(#{endTime},' 23:59:59')
            </if>
            <if test="sdk_adtype != null and sdk_adtype != ''">
                and sdk_adtype in (${sdk_adtype})
            </if>

        </where>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate desc
            </otherwise>
        </choose>
    </select>


    <select id="queryListV2" resultType="com.wbgame.pojo.OppoViolationRecordVo">
        select *,TRUNCATE(last_month_revenue/last_month_show*1000,2) ecpm,round(last_month_click/last_month_show*100,2) ctr from oppo_violation_record
        <where>
            <if test="company != null and company != ''">
                and company in (${company})
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="online_name != null and online_name != ''">
                and online_name = #{online_name}
            </if>
            <if test="tappid != null and tappid != ''">
                and tappid like concat('%',#{tappid},'%')
            </if>
            <if test="package_name != null and package_name != ''">
                and package_name like concat('%',#{package_name},'%')
            </if>
            <if test="startTime != null and endTime != null">
                and tdate between concat(#{startTime},' 00:00:00') and concat(#{endTime},' 23:59:59')
            </if>
            <if test="sdk_adtype != null and sdk_adtype != ''">
                and sdk_adtype = #{sdk_adtype}
            </if>
            <if test="open_type != null and open_type != ''">
                and open_type = #{open_type}
            </if>
            <if test="bidding_mode != null and bidding_mode != ''">
                and bidding_mode = #{bidding_mode}
            </if>
            <if test="type != null and type != ''">
                and `type` = #{type}
            </if>    
            <if test="dimension != null and dimension != ''">
                and dimension = #{dimension}
            </if>
            <if test="adsense_id != null and adsense_id != ''">
                and adsense_id like concat('%',#{adsense_id},'%')
            </if>
            <if test="subject != null and subject != ''">
                and subject like concat('%',#{subject},'%')
            </if>
        </where>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate desc
            </otherwise>
        </choose>
    </select>
</mapper>