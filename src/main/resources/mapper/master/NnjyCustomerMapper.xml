<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.NnjyCustomerMapper">

	 <select id="selectByHotQuestions" resultType="com.wbgame.pojo.nnjy.HotQuestionsInfo"  >
    select 
    id,question_hot AS questionHot
    from
    customer_hot_questions
    order by question_hit 
  </select>
 
  <update id="updateHit" >
    update customer_hot_questions
    set question_hit = question_hit + 1
    where id = #{id,jdbcType=INTEGER}
  </update>
  
   <update id="updateHotQuestions" parameterType="com.wbgame.pojo.nnjy.HotQuestionsInfo" >
    update customer_hot_questions
    set 
      question_hot = #{questionHot,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  
   <delete id="deleteHotQuestions" parameterType="java.lang.Integer" >
    delete from customer_hot_questions
    where id = #{id,jdbcType=INTEGER}
  </delete>
  
   <insert id="insertHotQuestions" parameterType="com.wbgame.pojo.nnjy.HotQuestionsInfo" >
    insert into customer_hot_questions (question_hot
      )
    values (#{questionHot,jdbcType=LONGVARCHAR}
      )
  </insert>
  
  <select id="selectByQType" resultType="com.wbgame.pojo.nnjy.QuestionsInfo" parameterType="java.lang.Integer" >
    select 
    a.id ,a.question_answer AS questionAnswer ,a.question_type AS questionType ,a.question_desc AS questionDesc
    from customer_question a ,customer_hot_questions b
    where question_type = #{questionType,jdbcType=INTEGER} and a.question_type = b.id
  </select>
  
  <select id="selectByQuestionId" resultType="com.wbgame.pojo.nnjy.QuestionsInfo" parameterType="com.wbgame.pojo.nnjy.QuestionsInfo" >
    select 
    id ,question_desc AS questionDesc,question_answer AS questionAnswer,question_type AS questionType
    from customer_question
    where 1=1
    <if test="id != null  and id !=''">
		and id = #{id}
	</if>
  </select>
  
  <select id="selectByKeywords" resultType="com.wbgame.pojo.nnjy.QuestionsInfo" parameterType="java.lang.String" >
    select 
    id ,question_desc AS questionDesc
    from customer_question
    where question_desc like concat('%',#{keywords},'%') or question_answer like concat('%',#{keywords},'%')
  </select>
  
  <update id="updateQuestions" parameterType="com.wbgame.pojo.nnjy.QuestionsInfo" >
    update customer_question
    set question_type = #{questionType,jdbcType=INTEGER},
      question_desc = #{questionDesc,jdbcType=LONGVARCHAR},
      question_answer = #{questionAnswer,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <delete id="deleteByQuestionId" parameterType="java.lang.Integer" >
    delete from customer_question
    where id = #{id,jdbcType=INTEGER}
  </delete>
  
  <delete id="deleteByQuestionType" parameterType="java.lang.Integer" >
    delete from customer_question
    where question_type = #{questionType,jdbcType=INTEGER}
  </delete>
  
   <insert id="insertQuestion" parameterType="com.wbgame.pojo.nnjy.QuestionsInfo" >
    insert into customer_question (question_type, question_desc, 
      question_answer)
    values (#{questionType,jdbcType=INTEGER}, #{questionDesc,jdbcType=LONGVARCHAR}, 
      #{questionAnswer,jdbcType=LONGVARCHAR})
  </insert>
</mapper>