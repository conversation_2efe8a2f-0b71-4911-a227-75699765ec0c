<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.ReyunAppDebugMapper" >

  <select id="selectDebugRecord" resultType="com.wbgame.pojo.advert.ReyunAppDebugDTO"
          parameterType="com.wbgame.pojo.advert.ReyunAppDebugDTO">
    select
    *
    from reyun_debug_record
    <where>
      <if test="appkey != null and appkey != ''">
        and appkey like "%" #{appkey} "%"
      </if>
      <if test="os != null">
        and os = #{os}
      </if>
      <if test="event_name != null">
        and event_name = #{event_name}
      </if>
      <if test="muid != null and muid != ''">
        and muid = #{muid}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
    <if test="attribution_tool != null">
        and attribution_tool = #{attribution_tool}
      </if>
    </where>
    order by id desc
  </select>



  <insert id="addDebugRecord" parameterType="com.wbgame.pojo.advert.ReyunAppDebugDTO"
          useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into reyun_debug_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appkey != null and appkey != ''">
        appkey,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="os != null and os != ''">
        os,
      </if>
      <if test="event_name != null and event_name != ''">
        event_name,
      </if>
      <if test="muid != null and muid != ''">
        muid,
      </if>
      <if test="value != null and value != ''">
        value,
      </if>
		<if test="attribution_tool != null and attribution_tool != ''">
        attribution_tool,
      </if>
      create_user,
      create_time
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
       <if test="appkey != null and appkey != ''">
        #{appkey,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="os != null and os != ''">
        #{os,jdbcType=VARCHAR},
      </if>
      <if test="event_name != null and event_name != ''">
      	#{event_name,jdbcType=VARCHAR},
      </if>
      <if test="muid != null and muid != ''">
        #{muid,jdbcType=VARCHAR},
      </if>
      <if test="value != null and value != ''">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="attribution_tool != null and attribution_tool != ''">
        #{attribution_tool},
      </if>
      #{create_user,jdbcType=VARCHAR},
      CURRENT_TIMESTAMP
    </trim>
  </insert>

  <update id="updateRecordStatus" >
    update reyun_debug_record
    set status   = #{status,jdbcType=TINYINT} where id = #{id,jdbcType=INTEGER}
  </update>
  
   <select id="selectOhayooAppData" resultType="java.lang.String">
    select id  from app_info where app_category =#{category,jdbcType=INTEGER} and id not in (38732,38734,38777,38806,38816,38818,38824,38825,38826,38827,
    38828,38829,38844,38845,38854,38855,38856,38885,38886,38887,38893,38903,38924,38925,38926,38939,38943,38951,38952,38953,
    38954,38955,38956,39073,39369,39413,39419,39477,39517,39533,39537,39545)
  </select>
  
  
  <select id="selectOhayooProductAccumulatRevenue" resultType="com.wbgame.pojo.jettison.OhayooProductAccumulatRevenue" >
   SELECT appid,round(SUM(ad_revenue),2) allRevenue,round(SUM(spend),2) allSpend from dnwx_adt.ohayoo_product_essential_data  WHERE tdate <![CDATA[<=]]>#{date} and  appid in (
	 <foreach collection="appidList"  index="index" item="item" separator=",">
		#{item}
	</foreach>
	)
   GROUP BY appid;
  </select>
  <insert id="insertOhayooProductEssentialData" parameterType="com.wbgame.pojo.jettison.OhayooProductEssentialDataDTO">
    insert into dnwx_adt.ohayoo_product_essential_data(tdate,appid,appName,appCategory,channelType,spend,add_user,allRevenue,addNum,actNum,keep_num1,keep_num2,keep_num3,keep_num4,
    keep_num5,keep_num6,keep_num7,keep_num14,keep_num30,ad_revenue,daily_duration,daily_per_duration,launch) values
    <foreach collection="list" item="li" separator=",">
			(#{li.tdate},#{li.appid},#{li.appName},#{li.appCategory},#{li.channelType},#{li.spend},#{li.add_user},#{li.allRevenue},#{li.addNum},#{li.actNum},
			#{li.keep_num1},#{li.keep_num2},#{li.keep_num3},#{li.keep_num4},#{li.keep_num5},#{li.keep_num6},#{li.keep_num7},#{li.keep_num14},#{li.keep_num30},
			#{li.ad_revenue},#{li.daily_duration},#{li.daily_per_duration}	,#{li.launch}			
			)
	</foreach>
  </insert>
  
   <insert id="insertOrUpdateOhayooProductEssentialData" parameterType="com.wbgame.pojo.jettison.OhayooProductEssentialDataDTO">
	    insert into dnwx_adt.ohayoo_product_essential_data(tdate,appid,appName,appCategory,channelType,spend,add_user,allRevenue,addNum,actNum,keep_num1,keep_num2,keep_num3,keep_num4,
	    keep_num5,keep_num6,keep_num7,keep_num14,keep_num30,ad_revenue,daily_duration,daily_per_duration,launch,ad_pv,allSpend) values
	    <foreach collection="list" item="li" separator="," >
				(#{li.tdate},#{li.appid},#{li.appName},#{li.appCategory},#{li.channelType},#{li.spend},#{li.add_user},#{li.allRevenue},#{li.addNum},#{li.actNum},
				#{li.keep_num1},#{li.keep_num2},#{li.keep_num3},#{li.keep_num4},#{li.keep_num5},#{li.keep_num6},#{li.keep_num7},#{li.keep_num14},#{li.keep_num30},
				#{li.ad_revenue},#{li.daily_duration},#{li.daily_per_duration}	,#{li.launch},#{li.ad_pv},#{li.allSpend}			
				)
		</foreach>
	    on duplicate key update
	    spend=values(spend),add_user=values(add_user),allRevenue=values(allRevenue),addNum=values(addNum),actNum=values(actNum),keep_num1=values(keep_num1),keep_num2=values(keep_num2),
	    keep_num3=values(keep_num3),keep_num4=values(keep_num4),keep_num5=values(keep_num5),keep_num6=values(keep_num6),keep_num7=values(keep_num7),keep_num14=values(keep_num14),
	    keep_num30=values(keep_num30),ad_revenue=values(ad_revenue),daily_duration=values(daily_duration),daily_per_duration=values(daily_per_duration),launch=values(launch),
	    ad_pv=values(ad_pv), allSpend=values(allSpend)
	</insert>
	
	<insert id="insertOrUpdateWechatGameOperationData" parameterType="com.wbgame.pojo.jettison.WechatGameOperationDataDTO">
	    insert into wechat_game_operation_data(tdate,appid,new_purchase_external_app,new_purchase_wechat_advertising,share_new_advances,act_num,add_num,
	   		cumulative_registere_users, act_visits_num,visits_num_per,average_stay,rd1_rate,rd7_rate,shares_num,share_users_number,sharing_rate,purchase_users,
	    	first_purchase_users,paid_permeability,ad_revenue,video_pv,video_revenue,pay_amount,acmul_revenue,acmul_spend) values
	    <foreach collection="list" item="li" separator="," >
				(#{li.tdate},#{li.appid},#{li.new_purchase_external_app},#{li.new_purchase_wechat_advertising},#{li.share_new_advances},#{li.act_num},#{li.add_num},
				#{li.cumulative_registere_users},#{li.act_visits_num},#{li.visits_num_per},#{li.average_stay},#{li.rd1_rate},#{li.rd7_rate},#{li.shares_num},#{li.share_users_number},
				#{li.sharing_rate},#{li.purchase_users},#{li.first_purchase_users},#{li.paid_permeability},#{li.ad_revenue},#{li.video_pv},#{li.video_revenue},#{li.pay_amount}	,
				#{li.acmul_revenue},#{li.acmul_spend}
				)
		</foreach>
	    on duplicate key update
	    new_purchase_external_app=values(new_purchase_external_app),new_purchase_wechat_advertising=values(new_purchase_wechat_advertising),share_new_advances=values(share_new_advances),
	    act_num=values(act_num),add_num=values(add_num),cumulative_registere_users=values(cumulative_registere_users),act_visits_num=values(act_visits_num),
	    visits_num_per=values(visits_num_per),average_stay=values(average_stay),rd1_rate=values(rd1_rate),rd7_rate=values(rd7_rate),shares_num=values(shares_num),
	    share_users_number=values(share_users_number),sharing_rate=values(sharing_rate),purchase_users=values(purchase_users),first_purchase_users=values(first_purchase_users),
	    paid_permeability=values(paid_permeability), ad_revenue=values(ad_revenue), video_pv=values(video_pv),video_revenue=values(video_revenue),pay_amount=values(pay_amount),
	    acmul_revenue=values(acmul_revenue),acmul_spend=values(acmul_spend)
	</insert>
	
 	<select id="selectOhayooEssentialData" resultType="com.wbgame.pojo.jettison.vo.OhayooProductEssentialDataVo"
            parameterType="com.wbgame.pojo.jettison.param.OhayooProductDataParam" >
        select 
		<if test="group != null and group != ''">
         	${group},
        </if>
    	<include refid="ohayoo_essential_data_sql"/>
		<if test="appid != null and appid != ''">
         	and appid in (${appid})
        </if>
        <if test="appCategory != null and appCategory != ''">
         	and appCategory  in (${appCategory})
        </if>
        <if test="channelType != null and channelType != ''">
         	and channelType in (${channelType})
        </if>
        <if test="group != null and group != ''">
         	group by ${group}
        </if>
        <choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate asc
			</otherwise>
		</choose>
    </select>
    <select id="selectOhayooEssentialDataTotal" resultType="com.wbgame.pojo.jettison.vo.OhayooProductEssentialDataVo"
            parameterType="com.wbgame.pojo.jettison.param.OhayooProductDataParam" >
          select 
    	<include refid="ohayoo_essential_data_sql"/>
		<if test="appid != null and appid != ''">
         	and appid in (${appid})
        </if>
        <if test="appCategory != null and appCategory != ''">
         	and appCategory  in (${appCategory})
        </if>
        <if test="channelType != null and channelType != ''">
         	and channelType in (${channelType})
        </if>
    </select>
    
    <sql id="ohayoo_essential_data_sql">
		round(sum(spend),2) spend,sum(add_user) add_user,round(sum(spend)/sum(add_user),2) as purchaseCpa,ifnull(round(sum(ad_revenue)/sum(spend)*100,2),0.00) as roi,
    	ifnull(round(sum(allRevenue)/sum(allSpend)*100,2),0.00) as acmulRoi,sum(addNum) addNum,CONCAT(round((sum(addNum)-sum(add_user))/sum(addNum)*100,2),'%') naturalPro,
    	round(sum(spend)/sum(addNum),2) as cpa,CONCAT(round(sum(addNum)/sum(actNum)*100,2),'%') newPro,sum(actNum) actNum,CONCAT(round(avg(keep_num1),2),'%') keep_num1,
    	CONCAT(round(avg(keep_num2),2),'%') keep_num2,CONCAT(round(avg(keep_num3),2),'%') keep_num3,CONCAT(round(avg(keep_num4),2),'%') keep_num4,
    	CONCAT(round(avg(keep_num5),2),'%') keep_num5,CONCAT(round(avg(keep_num6),2),'%') keep_num6,CONCAT(round(avg(keep_num7),2),'%') keep_num7,
    	CONCAT(round(avg(keep_num14),2),'%') keep_num14,CONCAT(round(avg(keep_num30),2),'%') keep_num30,sum(ad_revenue) ad_revenue,round(avg(daily_duration),0) daily_duration,
    	round(avg(daily_per_duration),0) daily_per_duration,round(sum(launch)/sum(actNum),2) launch,
    	sum(ad_pv) ad_pv,round(sum(ad_revenue)/sum(actNum),2) arpu,round(sum(ad_pv)/sum(actNum),2) avgPv,round(sum(ad_revenue)/sum(ad_pv)*1000,2) ecpm
		FROM    dnwx_adt.ohayoo_product_essential_data
		where   tdate<![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
    </sql>
    
    <select id="selectAcmulRoi" resultType="java.lang.String"
            parameterType="com.wbgame.pojo.jettison.param.OhayooProductDataParam" >
          select CONCAT(round(allRevenue/allSpend*100,2),'%') acmulRoi from dnwx_adt.ohayoo_product_essential_data where 1=1
          <if test="appid != null and appid != ''">
         	and appid= #{appid}
	        </if>
	        <if test="appCategory != null and appCategory != ''">
	         	and appCategory= #{appCategory}
	        </if>
	        <if test="channelType != null and channelType != ''">
	         	and channelType= #{channelType}
	        </if>
	        and tdate BETWEEN #{start_date} and #{end_date}
	        order by tdate desc limit 1
    </select>
    
      <select id="selectPastOneDayAccumulatRevenue" resultType="com.wbgame.pojo.jettison.OhayooProductAccumulatRevenue" >
	   	SELECT acmul_revenue allRevenue,acmul_spend allSpend from wechat_game_operation_data  WHERE tdate =#{date} and  appid=#{appId} limit 1
	  </select>
	      
      <select id="selectAdvRevenue" resultType="com.wbgame.pojo.jettison.OhayooProductAccumulatRevenue" >
	   	 select  tdate,appid,sum(ad_revenue) allRevenue,sum(wx_pay_income) allSpend ,sum(rebate_cost) spend ,sum(pay_ad_revenue) pay_ad_revenue ,sum(refund_revenue) refund_revenue  from dn_micgame_revenue_total 
			where  tdate<![CDATA[>=]]>#{beginDate} and tdate<![CDATA[<=]]>#{endDate} and appid in 
		    <foreach collection="appidList" item="li" separator="," open="("  close=")" >
					#{li}
			</foreach>
			 group by tdate,appid
	  </select>
	  
	  <select id="dyXyxIssuerReportList" resultType="com.wbgame.pojo.jettison.vo.DyXyxIssuerReporttVo">
		select * from dn_douyin_xyx_campaign_data 
		where tdate <![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
			 order by tdate
			</otherwise>
		</choose>
	</select>
	<select id="dyXyxIssuerReportTotal" resultType="com.wbgame.pojo.jettison.vo.DyXyxIssuerReporttVo">
		select round(sum(spend),2) spend, round(sum(buy_ad_income),2) buy_ad_income, round(sum(buy_pay_income),2) buy_pay_income,sum(newRegisterCnt) newRegisterCnt,
	 	sum(videoPlaySettle) videoPlaySettle,sum(videoPost) videoPost,sum(dauV2Settle) dauV2Settle from dn_douyin_xyx_campaign_data
		where tdate <![CDATA[>=]]>#{start_date} and tdate<![CDATA[<=]]>#{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
	</select>
</mapper>