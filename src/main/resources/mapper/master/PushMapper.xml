<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.PushMapper">

	<select id="selectPushUserInfo" parameterType="java.util.Map"
		resultType="java.lang.String">
		select count(1) from zyzhfee.PUSH_USER_INFO
	</select>

	<select id="selectDauUser" parameterType="String" resultType="com.wbgame.pojo.ApkDauUserVo">
		select productid as appid,count(distinct lsn) as dau,
		count(lsn) as startdau,round(sum(gametimes)/count(lsn),0) as utimes,round(sum(gametimes)/count(distinct lsn),0) as sumtimes
		from zyzhoa.NP_POST_LOG_${_parameter} GROUP BY PRODUCTID
	</select>

	<select id="selectNewUser" parameterType="String" resultType="com.wbgame.pojo.ApkNewUserVo">
		select PRODUCT_ID as appid,COUNT(distinct lsn) as currNew,
		COUNT(lsn) as newUser,to_char(createtime,'yyyy-mm-dd') as createtime
		from zyzhfee.PUSH_USER_INFO_TWO where to_char(createtime,'yyyy-mm-dd') = #{_parameter}
		GROUP BY PRODUCT_ID,to_char(createtime,'yyyy-mm-dd')
	</select>

	<select id="selectKeepData" parameterType="java.util.Map" resultType="com.wbgame.pojo.ApkKeepReturnVo">
		select count(DISTINCT lsn) as KEEPNUM,PRODUCT_ID as PRODUCTID
		from zyzhfee.push_user_info yy
		where to_char(yy.createtime,'yyyymmdd') = #{date1}
		and to_char(yy.lastaction, 'yyyymmdd') = #{nowdate}
		GROUP BY PRODUCT_ID
	</select>

	<insert id="insertChannelFee"  parameterType="java.util.List">
		insert into zyzhoa.ADV_CHANNEL_FEE(PUSH_CHA,PRI_ID,MMID,ADV_DT,GIVE_FEE,NEW_COUNT,TWO_RETENTION,DAU,GET_FEE,
		GET_FEE_EXPECT,ADV_FEE,SHOW_COUNT,CLICK_COUNT,ADV_TYPE,OS_TYPE,CHA_TYPE,ADV_FEE_TYPE)
		<foreach collection="list"  index="index" item="item" separator="union all">
		(
			select
			#{item.push_cha},#{item.pri_id},#{item.mmid},#{item.adv_dt},#{item.give_fee},#{item.new_count},#{item.two_retention},#{item.dau},
		#{item.get_fee},#{item.get_fee_expect},#{item.adv_fee},#{item.show_count},#{item.click_count},#{item.adv_type},#{item.os_type},#{item.cha_type},#{item.adv_fee_type}
			from dual
			)
		</foreach>
	</insert>

	<!--<insert id="insertChannelFee"  parameterType="java.util.List">
		insert into zyzhoa.ADV_CHANNEL_FEE(PUSH_CHA,PRI_ID,MMID,ADV_DT,GIVE_FEE,NEW_COUNT,TWO_RETENTION,DAU,GET_FEE,
		GET_FEE_EXPECT,ADV_FEE,SHOW_COUNT,CLICK_COUNT,ADV_TYPE,OS_TYPE,CHA_TYPE,ADV_FEE_TYPE)values
		<foreach collection="list"  index="index" item="item" separator=",">
			(#{item.push_cha},#{item.pri_id},#{item.mmid},#{item.adv_dt},#{item.give_fee},#{item.new_count},#{item.two_retention},#{item.dau},
			#{item.get_fee},#{item.get_fee_expect},#{item.adv_fee},#{item.show_count},#{item.click_count},#{item.adv_type},#{item.os_type},
			#{item.cha_type},#{item.adv_fee_type})
		</foreach>
	</insert>-->

	
	<!-- 抽取产品数据  -->
	<select id="selectProjectInfoByLog" parameterType="java.util.Map" 
		resultType="com.wbgame.pojo.push.ApkProductStatsVo">
		
		select '${tdate}' as tdate,aa.*,bb.add_num from 
			(select projectid,COUNT(DISTINCT lsn) as act_num 
			from ZYZHOA.NP_POST_LOG_${tdate}
			group by projectid) aa 
		
		left join 
			(select projectid,COUNT(DISTINCT lsn) as add_num 
			from ZYZHFEE.PUSH_USER_INFO
			where to_char(createtime,'yyyymmdd') = '${tdate}'
			group by projectid) bb
		
		on aa.projectid = bb.projectid
	</select>
	<select id="selectApkProductInfoByLog" parameterType="java.util.Map" 
		resultType="com.wbgame.pojo.push.ApkProductStatsVo">
		
		select '${tdate}' as tdate,aa.*,bb.add_num from 
			(select PRODUCTID as product_id,projectid,COUNT(DISTINCT lsn) as act_num 
			from ZYZHOA.NP_POST_LOG_${tdate}
			group by PRODUCTID,PROJECTID) aa 
		
		left join 
			(select product_id,projectid,COUNT(DISTINCT lsn) as add_num 
			from ZYZHFEE.PUSH_USER_INFO_TWO
			where to_char(createtime,'yyyymmdd') = '${tdate}'
			group by PRODUCT_ID,PROJECTID) bb
		
		on aa.product_id = bb.product_id and aa.projectid = bb.projectid
	</select>
	
	<select id="selectApkProductSumByLog" parameterType="java.util.Map" 
		resultType="com.wbgame.pojo.push.ApkProductUserTotalVo">
		
		select '${today}' as createtime,aa.productid as appid,count(DISTINCT aa.lsn) as ${mark} from(
			<foreach collection="list" item="it" separator=" union ">
				select productid,lsn from ZYZHOA.NP_POST_LOG_TWO_${it}
			</foreach>
		) aa
		group by aa.productid
	</select>
	<select id="selectApkProductDurationByLog" parameterType="java.util.Map" 
		resultType="com.wbgame.pojo.push.ApkProductUserTotalVo">
		
		SELECT '${today}' as createtime,aa.PRODUCTID as appid,TRUNC(SUM(duration)/7,2) as sevenTimes FROM (
			<foreach collection="list" item="it" separator=" union all ">
				SELECT PRODUCTID,TRUNC(SUM(GAMETIMES)/COUNT(DISTINCT LSN),2) as duration
				FROM ZYZHOA.NP_POST_LOG_${it} GROUP BY PRODUCTID
			</foreach>
		) aa
		GROUP BY aa.PRODUCTID
	</select>
	
	<select id="selectApkProductOutflowByLog" parameterType="java.util.Map"
			resultType="com.wbgame.pojo.push.ApkProductNumVo">
		select aa.product_id,'all' as projectid,aa.outflow_num,bb.act_num from 
			(SELECT PRODUCT_ID,COUNT(DISTINCT lsn) as outflow_num 
			FROM "ZYZHFEE"."PUSH_USER_INFO_TWO"
			where to_char(LASTACTION,'yyyymmdd') = '${tdate}'
			GROUP BY PRODUCT_ID) aa
		join
			(SELECT PRODUCTID,COUNT(DISTINCT lsn) as act_num
			from ZYZHOA.NP_POST_LOG_${tdate} GROUP BY PRODUCTID) bb
		on aa.PRODUCT_ID = bb.PRODUCTID
		
		union all
		
		select cc.*,dd.act_num from 
			(select product_id,projectid,COUNT(DISTINCT lsn) as outflow_num 
			from ZYZHFEE.PUSH_USER_INFO_TWO
			where to_char(LASTACTION,'yyyymmdd') = '${tdate}'
			group by PRODUCT_ID,PROJECTID) cc 
		join 
			(select PRODUCTID as product_id,projectid,COUNT(DISTINCT lsn) as act_num 
			from ZYZHOA.NP_POST_LOG_${tdate}
			group by PRODUCTID,PROJECTID) dd
		on cc.product_id = dd.product_id and cc.projectid = dd.projectid
	</select>
	<select id="selectApkProductSilentByLog" parameterType="java.util.Map"
			resultType="com.wbgame.pojo.push.ApkProductNumVo">
		select aa.product_id,'all' as projectid,aa.silent_num,bb.add_num from 
			(SELECT product_id,COUNT(DISTINCT lsn) as silent_num
			FROM "ZYZHFEE"."PUSH_USER_INFO_TWO"
			where CREATETIME >= TO_DATE('${sdate} 00:00:00', 'yyyymmdd hh24:mi:ss')
			and CREATETIME &lt;= TO_DATE('${edate} 23:59:59', 'yyyymmdd hh24:mi:ss')
			and (to_char(LASTACTION,'yyyymmdd') = to_char(createtime,'yyyymmdd')
			or to_char(LASTACTION,'yyyymmdd') = to_char(createtime+1,'yyyymmdd'))
			GROUP BY PRODUCT_ID) aa
		join
			(SELECT product_id,COUNT(DISTINCT lsn) as add_num
			FROM "ZYZHFEE"."PUSH_USER_INFO_TWO"
			where CREATETIME >= TO_DATE('${sdate} 00:00:00', 'yyyymmdd hh24:mi:ss')
			and CREATETIME &lt;= TO_DATE('${edate} 23:59:59', 'yyyymmdd hh24:mi:ss')
			GROUP BY PRODUCT_ID) bb
		on aa.PRODUCT_ID = bb.PRODUCT_ID
		
		union all
		
		select cc.*,dd.add_num from 
			(SELECT product_id,projectid,COUNT(DISTINCT lsn) as silent_num
			FROM "ZYZHFEE"."PUSH_USER_INFO_TWO"
			where CREATETIME >= TO_DATE('${sdate} 00:00:00', 'yyyymmdd hh24:mi:ss')
			and CREATETIME &lt;= TO_DATE('${edate} 23:59:59', 'yyyymmdd hh24:mi:ss')
			and (to_char(LASTACTION,'yyyymmdd') = to_char(createtime,'yyyymmdd')
			or to_char(LASTACTION,'yyyymmdd') = to_char(createtime+1,'yyyymmdd'))
			GROUP BY PRODUCT_ID,PROJECTID) cc
		JOIN
			(SELECT product_id,projectid,COUNT(DISTINCT lsn) as add_num
			FROM "ZYZHFEE"."PUSH_USER_INFO_TWO"
			where CREATETIME >= TO_DATE('${sdate} 00:00:00', 'yyyymmdd hh24:mi:ss')
			and CREATETIME &lt;= TO_DATE('${edate} 23:59:59', 'yyyymmdd hh24:mi:ss')
			GROUP BY PRODUCT_ID,PROJECTID) dd
		on cc.product_id = dd.product_id and cc.projectid = dd.projectid	
	</select>

	<select id="queryClientPost" parameterType="String" resultType="com.wbgame.pojo.ClientPostParamVo">
		${sql}
	</select>

	<select id="selectGameTimeSingle" parameterType="Map" resultType="com.wbgame.pojo.ApkGameTimeVo">
		select d.blockgame as blockgame,d.productid as productid,d.projectid as projectid,
		d.sumgame as nsingle,e.sumgame as osingle from (
		select
		CASE
		when  c.GAMETIME BETWEEN  1 and 3 then 1
		when  c.GAMETIME BETWEEN  4 and 10 then 2
		when c.GAMETIME BETWEEN  11 and 30 then 3
		when c.GAMETIME BETWEEN  31 and 60 then 4
		when c.GAMETIME BETWEEN  61 and 180 then 5
		when c.GAMETIME BETWEEN  181 and 600 then 6
		when c.GAMETIME BETWEEN  601 and 1800 then 7
		else 8
		end as blockgame,count(*) as sumgame,c.projectid,c.productid
		from (
		select a.productid as productid,a.projectid as projectid,a.lsn,a.gametimes as gametime
		from np_post_log_${table} a,zyzhfee.push_user_info b
		where
		b.createtime &gt;= to_date(#{beginDate},'yyyy-MM-dd hh24:mi:ss')
		and b.createtime &lt;= to_date(#{endDate},'yyyy-MM-dd hh24:mi:ss')
		and a.lsn = b.lsn
		and a.productid = b.product_id
		and a.gametimes != 0) c
		group by
		CASE
		when  c.GAMETIME BETWEEN  1 and 3 then 1
		when  c.GAMETIME BETWEEN  4 and 10 then 2
		when c.GAMETIME BETWEEN  11 and 30 then 3
		when c.GAMETIME BETWEEN  31 and 60 then 4
		when c.GAMETIME BETWEEN  61 and 180 then 5
		when c.GAMETIME BETWEEN  181 and 600 then 6
		when c.GAMETIME BETWEEN  601 and 1800 then 7
		else 8
		end,c.projectid,c.productid
		) d,(
		select
		CASE
		when  c.GAMETIME BETWEEN  1 and 3 then 1
		when  c.GAMETIME BETWEEN  4 and 10 then 2
		when c.GAMETIME BETWEEN  11 and 30 then 3
		when c.GAMETIME BETWEEN  31 and 60 then 4
		when c.GAMETIME BETWEEN  61 and 180 then 5
		when c.GAMETIME BETWEEN  181 and 600 then 6
		when c.GAMETIME BETWEEN  601 and 1800 then 7
		else 8
		end as blockgame,count(*) as sumgame,c.projectid,c.productid
		from (
		select a.productid as productid,a.projectid as projectid,
		a.lsn,a.gametimes as gametime
		from np_post_log_${table} a,zyzhfee.push_user_info b
		where
		b.createtime &lt; to_date(#{beginDate},'yyyy-MM-dd hh24:mi:ss')
		and a.lsn = b.lsn
		and a.productid = b.product_id
		and a.gametimes != 0) c
		group by
		CASE
		when  c.GAMETIME BETWEEN  1 and 3 then 1
		when  c.GAMETIME BETWEEN  4 and 10 then 2
		when c.GAMETIME BETWEEN  11 and 30 then 3
		when c.GAMETIME BETWEEN  31 and 60 then 4
		when c.GAMETIME BETWEEN  61 and 180 then 5
		when c.GAMETIME BETWEEN  181 and 600 then 6
		when c.GAMETIME BETWEEN  601 and 1800 then 7
		else 8
		end,c.projectid,c.productid
		) e
		WHERE d.projectid = e.projectid and d.blockgame = e.blockgame and d.productid = e.productid
	</select>

	<select id="selectGameTimeDay" parameterType="Map" resultType="com.wbgame.pojo.ApkGameTimeVo">
		select d.blockgame as blockgame,d.productid as productid,d.projectid as projectid,
		d.sumgame as nday,e.sumgame as oday from (
		select
		CASE
		when  c.GAMETIME BETWEEN  1 and 3 then 1
		when  c.GAMETIME BETWEEN  4 and 10 then 2
		when c.GAMETIME BETWEEN  11 and 30 then 3
		when c.GAMETIME BETWEEN  31 and 60 then 4
		when c.GAMETIME BETWEEN  61 and 180 then 5
		when c.GAMETIME BETWEEN  181 and 600 then 6
		when c.GAMETIME BETWEEN  601 and 1800 then 7
		else 8
		end as blockgame,count(*) as sumgame,c.projectid,c.productid
		from (
		select a.productid as productid,a.projectid as projectid,a.lsn,sum(a.gametimes) as gametime
		from np_post_log_${table} a,zyzhfee.push_user_info b
		where
		b.createtime &gt;= to_date(#{beginDate},'yyyy-MM-dd hh24:mi:ss')
		and b.createtime &lt;= to_date(#{endDate},'yyyy-MM-dd hh24:mi:ss')
		and a.lsn = b.lsn
		and a.productid = b.product_id
		and a.gametimes != 0
		GROUP BY a.lsn,a.productid,a.projectid) c
		group by
		CASE
		when  c.GAMETIME BETWEEN  1 and 3 then 1
		when  c.GAMETIME BETWEEN  4 and 10 then 2
		when c.GAMETIME BETWEEN  11 and 30 then 3
		when c.GAMETIME BETWEEN  31 and 60 then 4
		when c.GAMETIME BETWEEN  61 and 180 then 5
		when c.GAMETIME BETWEEN  181 and 600 then 6
		when c.GAMETIME BETWEEN  601 and 1800 then 7
		else 8
		end,c.projectid,c.productid
		) d,(
		select
		CASE
		when  c.GAMETIME BETWEEN  1 and 3 then 1
		when  c.GAMETIME BETWEEN  4 and 10 then 2
		when c.GAMETIME BETWEEN  11 and 30 then 3
		when c.GAMETIME BETWEEN  31 and 60 then 4
		when c.GAMETIME BETWEEN  61 and 180 then 5
		when c.GAMETIME BETWEEN  181 and 600 then 6
		when c.GAMETIME BETWEEN  601 and 1800 then 7
		else 8
		end as blockgame,count(*) as sumgame,c.projectid,c.productid
		from (
		select a.productid as productid,a.projectid as projectid,
		a.lsn,sum(a.gametimes) as gametime
		from np_post_log_${table} a,zyzhfee.push_user_info b
		where
		b.createtime &lt; to_date(#{beginDate},'yyyy-MM-dd hh24:mi:ss')
		and a.lsn = b.lsn
		and a.productid = b.product_id
		and a.gametimes != 0
		GROUP BY a.lsn,a.productid,a.projectid) c
		group by
		CASE
		when  c.GAMETIME BETWEEN  1 and 3 then 1
		when  c.GAMETIME BETWEEN  4 and 10 then 2
		when c.GAMETIME BETWEEN  11 and 30 then 3
		when c.GAMETIME BETWEEN  31 and 60 then 4
		when c.GAMETIME BETWEEN  61 and 180 then 5
		when c.GAMETIME BETWEEN  181 and 600 then 6
		when c.GAMETIME BETWEEN  601 and 1800 then 7
		else 8
		end,c.projectid,c.productid
		) e
		WHERE d.projectid = e.projectid and d.blockgame = e.blockgame and d.productid = e.productid
	</select>

	<select id="selectStartCount" parameterType="String" resultType="com.wbgame.pojo.ApkGameTimeVo">
		SELECT count(*) as startcount,PROJECTID,PRODUCTID
		FROM np_post_log_${table} GROUP BY PROJECTID,PRODUCTID
	</select>

    <select id="selectLoginTotal" parameterType="String" resultType="com.wbgame.pojo.LoginTotalVo">
		select DISTINCT lsn as lsn,imei as imei,projectid as projectid,productid as appid from np_post_log_${table}
	</select>
	
    <select id="selectProductModel" parameterType="String" resultType="com.wbgame.pojo.NpPostVo">
		select '${tdate}' as tdate,productid as param1,un as param2,mobilemodel as param3 from (

		SELECT productid,un,mobilemodel,row_number() OVER(PARTITION BY productid ORDER BY un desc) e FROM 
		
			(select productid,mobilemodel,un from 
				(SELECT a.productid,a.mobilemodel,count(distinct a.lsn) un 
				FROM np_post_log_${tdate} a 
				where a.productid != 0
				and a.mobilemodel != '0'
				group by a.productid,a.mobilemodel
				
				order by un desc)
			)
		) t where e &lt;= 50
	</select>
	
</mapper>