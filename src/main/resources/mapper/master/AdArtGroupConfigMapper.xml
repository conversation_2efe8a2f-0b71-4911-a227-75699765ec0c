<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.AdArtGroupConfigMapper">
    <!-- 美术组配置相关-->
    <select id="selectAdArtGroupConfig" resultType="com.wbgame.pojo.adv2.AdArtGroupVo" parameterType="com.wbgame.pojo.adv2.AdArtGroupVo">
        select  * from  adv_art_group_config where 1=1
        <if test="artGroupName != null and artGroupName != ''">
            and  artGroupName like '%${artGroupName}%'
        </if>
        <if test="artGroupId != null and artGroupId != '' and artGroupId != 'all'">
            and  artGroupId = #{artGroupId}
        </if>
    </select>


    <insert id="insertAdArtGroupConfig" parameterType="com.wbgame.pojo.adv2.AdArtGroupVo">
        insert into adv_art_group_config (artGroupName,artGroupConfig,remark,createTime,modifyUser,modifyTime)
        values (#{artGroupName},#{artGroupConfig},#{remark},NOW(),#{modifyUser},NOW())
    </insert>

    <delete id="delAdArtGroupConfig" parameterType="com.wbgame.pojo.adv2.AdArtGroupVo">
        delete from adv_art_group_config where artGroupId in (${artGroupId})
    </delete>

    <update id="updateAdArtGroupConfig" parameterType="com.wbgame.pojo.adv2.AdArtGroupVo">
        update adv_art_group_config set artGroupName = #{artGroupName},artGroupConfig = #{artGroupConfig},remark = #{remark},
        modifyUser = #{modifyUser},modifyTime = NOW()
        where artGroupId = #{artGroupId}
    </update>

    <update id="updateMarketFindAdArtGroupId" parameterType="com.wbgame.pojo.adv2.AdArtGroupVo">
        update market_sys_find set ad_art_group_id ='' where ad_art_group_id =#{artGroupId}
    </update>

</mapper>