<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.finance.master.MediumFreeMapper">
	
	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>
	

	
	<insert id="insertMediumFreeProfitInfo" parameterType="java.util.Map" >
		replace into finance_medium_profit_info(
			tdate,
			bus_port,
			bus_model,
			business,
			app_category,
			appid,
			android_billing,
			android_refund,
			pay_billing,
			pay_revenue,
			ad_revenue,
			all_revenue,
			h5_ad_rate,
			h5_ad_bonus,
			h5_get_bonus,
			h5_gift_bonus,
			h5_award_bonus
		) values
		<foreach collection="list" item="li" separator=",">
		  (
			#{li.tdate},
			#{li.bus_port},
			#{li.bus_model},
			#{li.business},
			#{li.app_category},
			#{li.appid},
			#{li.android_billing},
			#{li.android_refund},
			#{li.pay_billing},
			#{li.pay_revenue},
			#{li.ad_revenue},
			#{li.all_revenue},
			#{li.h5_ad_rate},
			#{li.h5_ad_bonus},
			#{li.h5_get_bonus},
			#{li.h5_gift_bonus},
			#{li.h5_award_bonus}
		 )
		</foreach>

	</insert>
	<select id="selectMediumFreeProfitInfo" parameterType="java.util.Map" resultType="com.wbgame.pojo.finance.MediumFreeIProfitVo">
		SELECT xx.*,IFNULL(yy.ad_revenue,0) h5_ad_bonus FROM
			(select aa.tdate,aa.appid,ff.app_category,ff.app_name,aa.dn_bus_model_id business,dd.bus_model,dd.bus_port,
				TRUNCATE(sum(adv_income),2) ad_revenue,
				TRUNCATE(sum(pay_income),2) pay_revenue,
				TRUNCATE(sum(adv_income)+sum(pay_income)-IFNULL((ee.refund_amount*0.6),0), 2) all_revenue,
				IFNULL(ee.refund_amount,0) android_refund,
				IFNULL(pp.android_billing,0) android_billing,
				IFNULL(pp.pay_billing,0) pay_billing,
				/*ee.ad_rate h5_ad_rate,
				IFNULL(TRUNCATE(sum(adv_income)*ee.ad_rate/100, 2),0) h5_ad_bonus,*/

				if(os_type = 1,'安卓',if(os_type = 2,'ios',if(os_type = 3,'google',if(os_type = 4,'小游戏','')))) platform
			from finance_model_income_detail aa

			left join app_info ff on aa.appid=ff.id

			left join dn_bus_model_config dd on aa.dn_bus_model_id = dd.id

			LEFT JOIN finance_medium_import_config ee on aa.tdate=ee.tdate and aa.appid=ee.appid and dd.bus_port=ee.bus_port

			LEFT JOIN
				(select cc.dn_bus_model_id,DATE_FORMAT(createtime,'%Y-%m-%d') date,appid,IF(aa.paytype='腾讯米大师支付' and aa.chaid='wx','h5_wechat',aa.chaid) chaid,

					IFNULL(TRUNCATE(SUM(CASE WHEN paytype='腾讯米大师支付' THEN money ELSE 0 END)/100,2),0) android_billing,
					IFNULL(TRUNCATE(SUM(money)/100,2),0) pay_billing

					from wb_pay_info aa left join dn_channel_info cc on IF(aa.paytype='腾讯米大师支付' and aa.chaid='wx','h5_wechat',aa.chaid) = cc.cha_id
					where createtime BETWEEN '${sdate} 00:00:00' AND '${edate} 23:59:59'
					and orderstatus='SUCCESS' and chaid != ''
					GROUP BY DATE_FORMAT(createtime,'%Y-%m-%d'),appid,cc.dn_bus_model_id ) pp
			ON aa.tdate=pp.date and aa.appid=pp.appid and aa.dn_bus_model_id=pp.dn_bus_model_id

			where aa.tdate between '${sdate}' and '${edate}' and (ff.app_category in (5,17,43,44,45))

			group by aa.tdate,aa.appid,dd.bus_port) xx

		LEFT JOIN
			(
				select tdate,appid,ad_revenue from dn_micgame_revenue_total where tdate BETWEEN '${sdate}' AND '${edate}'
				group by tdate,appid
			) yy
			ON xx.tdate=yy.tdate and xx.appid=yy.appid and xx.bus_port='微信小游戏'
	</select>

	<select id="selectPayBillingMap" parameterType="java.util.Map" resultType="java.util.Map">
		select appid,
			CONCAT(DATE_FORMAT(createtime,'%Y-%m'),appid) mapkey,
			IFNULL(TRUNCATE(SUM(CASE WHEN paytype='腾讯米大师支付' THEN money ELSE 0 END)/100,2),0) pay_billing
		from wb_pay_info aa RIGHT JOIN finance_window_config bb
		ON aa.appid=bb.dnappid
			where orderstatus='SUCCESS'
			and createtime > '2022-01-01 00:00:00'

		<if test="where != null and where != ''">
			${where}
		</if>

		group by DATE_FORMAT(createtime,'%Y-%m'),appid
	</select>


	<select id="selectMediumFreeProfitList" parameterType="java.util.Map" resultType="com.wbgame.pojo.finance.MediumFreeIProfitVo">
		select
			tdate,
			bus_port,
			bus_model,
			business,
			app_category,
			appid,
			android_billing,
			android_refund,
			pay_billing,
			pay_revenue,
			ad_revenue,
			all_revenue,
			concat(h5_ad_rate,'%') h5_ad_rate,
			h5_ad_bonus,
			h5_get_bonus,
			h5_gift_bonus,
			h5_award_bonus
		from
		(<include refid="dn_mediumfree_profit_sql"/>) a
	</select>
	<select id="selectMediumFreeProfitListSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.finance.MediumFreeIProfitVo">
		select

			TRUNCATE(SUM(xx.android_billing),2) android_billing,
			TRUNCATE(SUM(xx.android_refund),2) android_refund,
			TRUNCATE(SUM(xx.pay_billing),2) pay_billing,
			TRUNCATE(SUM(xx.pay_revenue),2) pay_revenue,
			TRUNCATE(SUM(xx.ad_revenue),2) ad_revenue,
			TRUNCATE(SUM(xx.all_revenue),2) all_revenue,
			TRUNCATE(SUM(xx.h5_ad_bonus),2) h5_ad_bonus,
			TRUNCATE(SUM(xx.h5_get_bonus),2) h5_get_bonus,
			TRUNCATE(SUM(xx.h5_gift_bonus),2) h5_gift_bonus,
			TRUNCATE(SUM(xx.h5_award_bonus),2) h5_award_bonus

		from (<include refid="dn_mediumfree_profit_sql"/>) xx
	</select>

	<sql id="dn_mediumfree_profit_sql">
		select
			tdate,
			bus_port,
			bus_model,
			business,
			app_category,
			appid,
			ifnull(convert(android_billing,decimal(10,2)),0) android_billing,
			ifnull(convert(android_refund,decimal(10,2)),0) android_refund,
			ifnull(convert(pay_billing,decimal(10,2)),0) pay_billing,
			ifnull(convert(pay_revenue,decimal(10,2)),0) pay_revenue,
			ifnull(convert(ad_revenue,decimal(10,2)),0) ad_revenue,
			ifnull(convert(all_revenue,decimal(10,2)),0) all_revenue,
			ifnull(convert(h5_ad_rate,decimal(10,2)),0) h5_ad_rate,
			ifnull(convert(h5_ad_bonus,decimal(10,2)),0) h5_ad_bonus,
			ifnull(convert(h5_get_bonus,decimal(10,2)),0) h5_get_bonus,
			ifnull(convert(h5_gift_bonus,decimal(10,2)),0) h5_gift_bonus,
			ifnull(convert(h5_award_bonus,decimal(10,2)),0) h5_award_bonus
		from finance_medium_profit_info aa

		where tdate between '${sdate}' and '${edate}'

		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="app_category != null and app_category != ''">
			and app_category in (${app_category})
		</if>
		<if test="bus_port != null and bus_port != ''">
			and bus_port = #{bus_port}
		</if>

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate,appid,bus_port
			</otherwise>
		</choose>
	</sql>


	<select id="selectMediumFreeProfitTotalList" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_mediumfree_profittotal_sql"/>

	</select>
	<select id="selectMediumFreeProfitTotalListSum" parameterType="java.util.Map" resultType="java.util.Map">
		select

			TRUNCATE(SUM(xx.rebateSpend),2) rebateSpend,
			TRUNCATE(SUM(xx.yearlyRateSpend),2) yearlyRateSpend,
			TRUNCATE(SUM(xx.pay_billing),2) pay_billing,
			TRUNCATE(SUM(xx.pay_revenue),2) pay_revenue,
			TRUNCATE(SUM(xx.ad_revenue),2) ad_revenue,
			TRUNCATE(SUM(xx.all_revenue),2) all_revenue,
			TRUNCATE(SUM(xx.h5_award_bonus),2) h5_award_bonus,

			TRUNCATE(IFNULL(sum(xx.all_revenue),0)-IFNULL(SUM(xx.rebateSpend),0)+IFNULL(SUM(xx.yearlyRateSpend),0)+IFNULL(sum(xx.h5_award_bonus),0), 2) profit,
			CONCAT(TRUNCATE((IFNULL(sum(xx.all_revenue),0)-IFNULL(SUM(xx.rebateSpend),0)+IFNULL(SUM(xx.yearlyRateSpend),0)+IFNULL(sum(xx.h5_award_bonus),0))/IFNULL(sum(xx.all_revenue),0)*100, 2),'%') profit_rate

		from (<include refid="dn_mediumfree_profittotal_sql"/>) xx
	</select>

	<sql id="dn_mediumfree_profittotal_sql">
		select
			<choose>
				<when test="group != null and group != ''">
					${group} as tdate,
				</when>
				<otherwise>
					bb.tdate,
				</otherwise>
			</choose>
		    bb.business,bb.bus_port,bb.appid ,ff.app_name,ff.app_category,gg.`name` app_category_name,
			IFNULL(TRUNCATE(SUM(aa.rebateSpend), 2),0) rebateSpend,
			IFNULL(TRUNCATE(SUM(aa.yearlyRateSpend), 2),0) yearlyRateSpend,

			TRUNCATE(sum(bb.pay_billing),2) pay_billing,
			TRUNCATE(sum(bb.pay_revenue),2) pay_revenue,
			TRUNCATE(sum(bb.ad_revenue),2) ad_revenue,
			TRUNCATE(sum(bb.all_revenue),2) all_revenue,
			TRUNCATE(sum(bb.h5_award_bonus),2) h5_award_bonus,

			TRUNCATE(IFNULL(sum(bb.all_revenue),0)-IFNULL(SUM(aa.rebateSpend),0)+IFNULL(SUM(aa.yearlyRateSpend),0)+IFNULL(sum(bb.h5_award_bonus),0), 2) profit,
			CONCAT(TRUNCATE((IFNULL(sum(bb.all_revenue),0)-IFNULL(SUM(aa.rebateSpend),0)+IFNULL(SUM(aa.yearlyRateSpend),0)+IFNULL(sum(bb.h5_award_bonus),0))/IFNULL(sum(bb.all_revenue),0)*100, 2),'%') profit_rate

		from (
			select `day`,app,business,
				TRUNCATE(SUM(rebateSpend), 2) rebateSpend,
				TRUNCATE(SUM(yearlyRateSpend), 2) yearlyRateSpend
			from dn_report_spend_finance
			where `day` BETWEEN #{sdate} AND #{edate}
			group by `day`,app,business

		) aa

		right join finance_medium_profit_info bb on aa.`day`=bb.tdate and aa.app=bb.appid and aa.business=bb.business
		left join app_info ff on bb.appid=ff.id
		left join app_category gg on ff.app_category=gg.id

		where bb.tdate BETWEEN #{sdate} AND #{edate} and (ff.app_category in (5) or ff.bus_category = 2)
		<if test="appid != null and appid != ''">
			and bb.appid in (${appid})
		</if>
		<if test="app_category != null and app_category != ''">
			and bb.app_category in (${app_category})
		</if>
		<if test="bus_port != null and bus_port != ''">
			and bb.bus_port = #{bus_port}
		</if>


		<choose>
			<when test="group != null and group != ''">
				group by ${group},bb.appid
			</when>
			<otherwise>
				group by bb.tdate,bb.appid,bb.bus_port
			</otherwise>
		</choose>

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by bb.tdate,bb.appid,bb.bus_port
			</otherwise>
		</choose>

	</sql>


	<delete id="deleteMediumFreeImportConfig" parameterType="com.wbgame.pojo.finance.MediumFreeImportVo" >
		delete from finance_medium_import_config
		where
		<foreach collection="list" item="li"  separator=" or " >
			(tdate=#{li.tdate} and bus_port=#{li.bus_port} and appid=#{li.appid})
		</foreach>

	</delete>

</mapper>