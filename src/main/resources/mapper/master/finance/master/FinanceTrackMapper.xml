<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.finance.master.FinanceTrackMapper">

    <select id="getFinanceTrackList" parameterType="java.util.Map" resultType="com.wbgame.pojo.finance.FinanceTrackVo">
        select * from finance_track_report where 1=1
        <if test="tdate != null and tdate != ''">
            and tdate like '%${tdate}%'
        </if>
        <if test="company != null and company != ''">
            and company LIKE '%${company}%'
        </if>
        <if test="customer != null and customer != ''">
            and customer LIKE '%${customer}%'
        </if>
        <if test="invoice_time != null and invoice_time != ''">
            and invoice_time like '%${invoice_time}%'
        </if>
        <if test="invoice_number != null and invoice_number != ''">
            and invoice_number LIKE '%${invoice_number}%'
        </if>
        <if test="invoice_text != null and invoice_text != ''">
            and invoice_text LIKE '%${invoice_text}%'
        </if>
        <if test="receipt_time != null and receipt_time != ''">
            and receipt_time LIKE '%${receipt_time}%'
        </if>
        <if test="type != null and type != ''">
            and `type` LIKE '%${type}%'
        </if>
        order by tdate desc,company,customer
    </select>

    <select id="getFinanceTrackListSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.finance.FinanceTrackVo">
        select ROUND(IFNULL(sum(estimated),0),2) estimated,ROUND(IFNULL(sum(invoice_amount),0),2) invoice_amount,
        ROUND(IFNULL(sum(difference_amount),0),2) difference_amount,ROUND(IFNULL(sum(left_amount),0),2) left_amount,
        ROUND(IFNULL(sum(receipt_amount),0),2) receipt_amount ,ROUND(IFNULL(sum(ticket_not_receipt),0),2) ticket_not_receipt,
        ROUND(IFNULL(sum(need_receipt_total),0),2) need_receipt_total
        from finance_track_report where 1=1
        <if test="tdate != null and tdate != ''">
            and tdate like '%${tdate}%'
        </if>
        <if test="company != null and company != ''">
            and company LIKE '%${company}%'
        </if>
        <if test="customer != null and customer != ''">
            and customer LIKE '%${customer}%'
        </if>
        <if test="invoice_time != null and invoice_time != ''">
            and invoice_time like '%${invoice_time}%'
        </if>
        <if test="invoice_number != null and invoice_number != ''">
            and invoice_number LIKE '%${invoice_number}%'
        </if>
        <if test="invoice_text != null and invoice_text != ''">
            and invoice_text LIKE '%${invoice_text}%'
        </if>
        <if test="receipt_time != null and receipt_time != ''">
            and receipt_time LIKE '%${receipt_time}%'
        </if>
        <if test="type != null and type != ''">
            and `type` LIKE '%${type}%'
        </if>
    </select>


    <insert id="addFinanceTrack" parameterType="com.wbgame.pojo.finance.FinanceTrackVo">
        insert into finance_track_report (tdate,company,customer,estimated,invoice_time,
        invoice_number,invoice_ticket_num,invoice_text,invoice_amount,difference_amount,reason,left_amount,
        receipt_time,receipt_amount,ticket_not_receipt,need_receipt_total,`type`,send_time,
        tracking_number,mark,create_time,create_user) values (#{tdate},#{company},#{customer},
        #{estimated},#{invoice_time},#{invoice_number},#{invoice_ticket_num},#{invoice_text},#{invoice_amount},#{difference_amount},
        #{reason},#{left_amount},#{receipt_time},#{receipt_amount},#{ticket_not_receipt},#{need_receipt_total},
        #{type},#{send_time},#{tracking_number},#{mark},now(),#{create_user})
    </insert>

    <update id="updateFinanceTrack" parameterType="com.wbgame.pojo.finance.FinanceTrackVo">
        update finance_track_report set tdate = #{tdate},company = #{company},customer = #{customer},estimated = #{estimated},
        invoice_time = #{invoice_time},invoice_number = #{invoice_number},invoice_text = #{invoice_text},invoice_ticket_num =#{invoice_ticket_num},
        invoice_amount = #{invoice_amount},difference_amount = #{difference_amount},reason = #{reason},left_amount = #{left_amount},
        receipt_time = #{receipt_time},receipt_amount = #{receipt_amount},ticket_not_receipt = #{ticket_not_receipt},
        need_receipt_total = #{need_receipt_total},`type` = #{type},send_time = #{send_time},tracking_number = #{tracking_number},
        mark = #{mark},modify_time = now(),modify_user = #{modify_user}
        where id = #{id}
    </update>

    <delete id="delFinanceTrack" parameterType="com.wbgame.pojo.finance.FinanceTrackVo">
        delete from finance_track_report where  id in (${id})
    </delete>

    <insert id="batchAddFinanceTrack" parameterType="java.util.List">
        insert into finance_track_report (tdate,company,customer,estimated,invoice_time,
        invoice_number,invoice_ticket_num,invoice_text,invoice_amount,difference_amount,reason,left_amount,
        receipt_time,receipt_amount,ticket_not_receipt,need_receipt_total,`type`,send_time,
        tracking_number,mark,create_time,create_user)
        values
        <foreach collection="list" item="li" separator=",">
            (
            #{li.tdate}, #{li.company}, #{li.customer},#{li.estimated}, #{li.invoice_time}, #{li.invoice_number},#{li.invoice_ticket_num},
            #{li.invoice_text}, #{li.invoice_amount}, #{li.difference_amount},#{li.reason}, #{li.left_amount},
            #{li.receipt_time}, #{li.receipt_amount}, #{li.ticket_not_receipt},#{li.need_receipt_total}, #{li.type}, #{li.send_time},
            #{li.tracking_number}, #{li.mark},now(), #{li.create_user}
            )
        </foreach>
    </insert>
</mapper>