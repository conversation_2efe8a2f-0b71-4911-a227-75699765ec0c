<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.finance.master.DnYearlyRateConfigMapper">

    <delete id="deleteDnYearlyRateConfigById" parameterType="com.wbgame.pojo.finance.DnYearlyRateConfigVo">

        <if test="list != null and list.size > 0">

            delete from dn_yearly_rate_config
            <where>
                <foreach collection="list" item="key">
                    or app_category =#{key.app_category} and media = #{key.media}
                </foreach>
            </where>
        </if>

    </delete>

    <insert id="insertDnYearlyRateConfig" parameterType="com.wbgame.pojo.finance.DnYearlyRateConfigVo">
        insert into dn_yearly_rate_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="app_category != null and app_category != ''">
                app_category,
            </if>
            <if test="media != null and media != ''">
                media,
            </if>
            <if test="rebate_rate != null and rebate_rate != ''">
                rebate_rate,
            </if>

            <if test="create_user != null and create_user != ''">
                create_user,
            </if>

            create_time,
            update_time
        </trim>

        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="app_category != null and app_category != ''">
                #{app_category},
            </if>
            <if test="media != null and media != ''">
                #{media},
            </if>
            <if test="rebate_rate != null and rebate_rate != ''">
                #{rebate_rate,jdbcType=VARCHAR},
            </if>

            <if test="create_user != null and create_user != ''">
                #{create_user,jdbcType=VARCHAR},
            </if>
            current_timestamp,
            current_timestamp
        </trim>
    </insert>

    <update id="updateDnYearlyRateConfig" parameterType="com.wbgame.pojo.finance.DnYearlyRateConfigVo">
        update dn_yearly_rate_config
        set
            rebate_rate = #{rebate_rate,jdbcType=VARCHAR},
            update_time = current_timestamp,
            update_user = #{update_user,jdbcType=VARCHAR}
        where
            app_category = #{app_category,jdbcType=VARCHAR}
            and media = #{media,jdbcType=VARCHAR}
    </update>

    <sql id="resColumn">

        app_category, media, concat(rebate_rate, "%") rebate_rate, create_user, update_user,
        date_format(create_time, '%Y-%m-%d %H:%i:%s') create_time,
        date_format(update_time, '%Y-%m-%d %H:%i:%s') update_time
    </sql>

    <select id="selectDnYearlyRateConfig" resultType="com.wbgame.pojo.finance.DnYearlyRateConfigVo">

        select yy.*,zz.name as app_category_name from dn_yearly_rate_config yy left join app_category zz on yy.app_category = zz.id
        <where>

            <if test="app_category != null and app_category.size > 0">
                and app_category in
                <foreach collection="app_category" item="index" open="(" separator="," close=")">
                    #{index}
                </foreach>
            </if>
            <if test="media != null and media.size > 0">
                and media in
                <foreach collection="media" item="index" open="(" separator="," close=")">
                    #{index}
                </foreach>

            </if>

        </where>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by create_time desc
            </otherwise>
        </choose>
    </select>

</mapper>