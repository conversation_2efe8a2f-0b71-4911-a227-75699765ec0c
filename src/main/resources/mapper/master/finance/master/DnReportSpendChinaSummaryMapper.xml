<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.finance.master.DnReportSpendChinaSummaryMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.finance.IncentiveIntroductionVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="app_category_name" property="appCategoryName" jdbcType="VARCHAR"/>
        <result column="platform" property="platform" jdbcType="VARCHAR"/>
        <result column="bus_model" property="busModel" jdbcType="VARCHAR"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
        <result column="proportion" property="proportion" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="INTEGER"/>
        <result column="app" property="appId" jdbcType="INTEGER"/>
        <result column="app_category" property="appCategory" jdbcType="INTEGER"/>
        <result column="spend" property="spend" jdbcType="DECIMAL"/>
        <result column="day" property="day" jdbcType="VARCHAR"/>
        <result column="media" property="media" jdbcType="VARCHAR"/>
        <result column="model_id" property="modelId" jdbcType="INTEGER"/>
        <result column="business" property="modelId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="selectTmp">
        b.id
        , b.app, b.installs, b.impressions, b.clicks, b.convert, b.register, b.game_addiction,
        cast(b.spend as decimal(20, 2)) spend,
        cast(b.rebateSpend as decimal(20, 2)) rebateSpend, b.revenue1, b.revenue3, b.revenue7,
        b.revenue30, b.totalRevenue1, b.day, b.account,
        b.media, b.channel, b.channel1, b.channelType, b.type, b.agent, b.putUser, b.artist, b.material,
        DATE_FORMAT(b.createTime, "%Y-%m-%d %H:%i:%s")createTime,
        b.download, b.adsenseType, b.adsensePosition, b.business
    </sql>

    <select id="selectIncentiveIncomeByCondition" parameterType="com.wbgame.pojo.finance.IncentiveIntroductionDTO"
            resultMap="BaseResultMap">

        SELECT
        di.*,
        tmp.app_name,
        tmp.app_category_name,
        (case when tmp.platform = 1 then "安卓" when tmp.platform = 2 then "IOS" when tmp.platform = 3 then "Google" else "小游戏" end ) platform,
        tmp.app_category,
        db.bus_model
        FROM
        dn_inncentive_income di
        LEFT JOIN (
        SELECT
        ai.id aid,
        ai.app_name,
        ai.os_type platform,
        ac.id app_category,
        ac.NAME app_category_name
        FROM
        app_info ai
        LEFT JOIN app_category ac ON ai.app_category = ac.id
        ) tmp ON di.app_id = tmp.aid
        LEFT JOIN dn_bus_model_config db ON di.model_id = db.id
        <where>

            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and day between #{start_date} and #{end_date}
            </if>

            <if test="appId != null and appId.size > 0">
                and app_id in
                <foreach collection="appId" item="a" open="(" separator="," close=")">
                    #{a}
                </foreach>
            </if>

            <if test="modelId != null and modelId.size > 0">

                and model_id in
                <foreach collection="modelId" item="b" open="(" separator="," close=")">
                    #{b}
                </foreach>
            </if>
        </where>

        <choose>
            <when test="order_str != null and order_str !=''">
                order by ${order_str}, id desc
            </when>
            <otherwise>
                order by day desc, id desc
            </otherwise>
        </choose>

    </select>

    <select id="countIncentiveIncomeByCondition" parameterType="com.wbgame.pojo.finance.IncentiveIntroductionDTO"
            resultMap="BaseResultMap">

        SELECT
        sum(spend) spend
        FROM
        dn_inncentive_income

        <where>

            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and day between #{start_date} and #{end_date}
            </if>

            <if test="appId != null and appId.size > 0">
                and app_id in
                <foreach collection="appId" item="a" open="(" separator="," close=")">
                    #{a}
                </foreach>
            </if>

            <if test="modelId != null and modelId.size > 0">

                and model_id in
                <foreach collection="modelId" item="b" open="(" separator="," close=")">
                    #{b}
                </foreach>
            </if>
        </where>

    </select>

    <select id="percentage" parameterType="com.wbgame.pojo.finance.IncentiveIntroductionDTO" resultMap="BaseResultMap">

        SELECT a.tday day,
               a.app,
               a.business,
               a.media,
               app_category_name,
               db.bus_model,
               0 spend,
               TRUNCATE(spend_a / spend_b, 10) proportion
        FROM (
                 SELECT date_format( DAY, "%Y-%m" ) tday,
                     app,
                     business,
                     media,
                     sum( cast( spend AS DECIMAL( 18, 2 ) ) ) spend_a
                 FROM
                      dn_report_spend_finance
                 <where>
                    <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                        and day between #{start_date} and #{end_date}
                    </if>
                   AND business IS NOT NULL
                   AND spend > 0
                 </where>
                 GROUP BY
                     tday,
                     app,
                     business,
                     media
             ) a
                 LEFT JOIN (
            SELECT date_format( DAY, "%Y-%m" ) tday
                    ,
                app,
                business,
                sum( cast( spend AS DECIMAL( 18, 2 ) ) ) spend_b
            FROM
                 dn_report_spend_finance

            <where>
                <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                    and day between #{start_date} and #{end_date}
                </if>
                AND business IS NOT NULL
                AND spend > 0
            </where>
            GROUP BY
                tday,
                app,
                business
        ) b ON a.app = b.app
            AND a.tday = b.tday
            AND a.business = b.business

                 LEFT JOIN
             (select ai.id aid, ac.id app_category, ac.name app_category_name
              from app_info ai
                       left join app_category ac on ai.app_category = ac.id) tmp on b.app = tmp.aid
            LEFT JOIN dn_bus_model_config db
            ON a.business = db.id
        ORDER BY day DESC, a.app desc

    </select>

    <select id="percentageToDayLinModel" parameterType="com.wbgame.pojo.finance.IncentiveIntroductionDTO" resultMap="BaseResultMap">

        <!-- 针对只有 业务模式和年月的-->
        SELECT
            a.tday day,
            a.app,
            a.business,
            a.media,
            app_category_name,
            db.bus_model,
            0 spend,
            TRUNCATE( spend_a / spend_b, 10 ) proportion
        FROM
            (
                SELECT
                    date_format( DAY, "%Y-%m" ) tday,
                    app,
                    business,
                    media,
                    sum( cast( spend AS DECIMAL ( 18, 2 ) ) ) spend_a
                FROM
                     dn_report_spend_finance x LEFT JOIN app_info y ON x.app = y.id

                /* 判断去掉中度的产品分类 -张亮.20230509 */
                where y.app_category != '5'

                    <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                        and day between #{start_date} and #{end_date}
                    </if>
                    AND business IS NOT NULL
                    AND spend > 0

                GROUP BY
                    tday,
                    app,
                    business,
                    media
            ) a
                LEFT JOIN (
                SELECT
                    date_format( DAY, "%Y-%m" ) tday,
                    business,
                    sum( cast( spend AS DECIMAL ( 18, 2 ) ) ) spend_b
                FROM
                    dn_report_spend_finance x LEFT JOIN app_info y ON x.app = y.id

                /* 判断去掉中度的产品分类 -张亮.20230509 */
                where y.app_category != '5'

                    <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                        and day between #{start_date} and #{end_date}
                    </if>
                    AND business IS NOT NULL
                    AND spend > 0
                GROUP BY
                    tday,
                    business
            ) b ON
                    a.tday = b.tday
                    AND a.business = b.business
                LEFT JOIN (
                SELECT
                    ai.id aid,
                    ac.id app_category,
                    ac.NAME app_category_name
                FROM
                    app_info ai
                        LEFT JOIN app_category ac ON ai.app_category = ac.id
            ) tmp ON a.app = tmp.aid
                LEFT JOIN dn_bus_model_config db ON a.business = db.id
        where bus_model in ("xiaomiiaa", "vivoiaa","oppoiaa")
        ORDER BY
            DAY DESC,
            a.app DESC
    </select>

    <select id="getIncome" resultMap="BaseResultMap">

        SELECT dr.day,
        dr.app,
        dr.business,
        dr.media,
        db.bus_model,
        sum( cast( dr.spend AS DECIMAL( 18, 2 ) ) ) spend
        FROM
         dn_report_spend_finance  dr LEFT JOIN dn_bus_model_config db
        ON dr.business = db.id
        <where>
            <if test="start_date != null and start_date != '' and end_date != null and end_date != ''">
                and dr.day between #{start_date} and #{end_date}
            </if>
            AND dr.business IS NOT NULL
            AND dr.spend > 0
        </where>
        GROUP BY
        dr.day,
        dr.app,
        dr.business,
        dr.media,
        db.bus_model
    </select>


    <delete id="deleteByDay" parameterType="com.wbgame.pojo.finance.IncentiveIntroductionVO" >

        <if test="list != null and list.size > 0">
            delete from dn_inncentive_income
            <where>
                <foreach collection="list" item="introduction">
                    or (day = #{introduction.day} and model_id = #{introduction.modelId} and app_id = #{introduction.appId})
                </foreach>
            </where>
        </if>

    </delete>

    <insert id="insertIncentiveIntroduction" parameterType="com.wbgame.pojo.finance.IncentiveIntroductionVO"
            useGeneratedKeys="true" keyColumn="id" keyProperty="id">

        insert into dn_inncentive_income(app_id, model_id, day, media, spend)
        values
        <foreach collection="list" item="incent" separator=",">
            (#{incent.appId}, #{incent.modelId}, #{incent.day}, #{incent.media}, #{incent.spend})
        </foreach>
    </insert>

    <update id="updateIncentiveIntroduction" parameterType="com.wbgame.pojo.finance.IncentiveIntroductionVO">

        update dn_inncentive_income
        <set>

            <if test="spend != null and spend != ''">

                spend = #{spend},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteIncentive" parameterType="java.lang.Integer" >

        <if test="list != null and list.size > 0">

            delete from dn_inncentive_income
            <where>
                id in
                <foreach collection="list" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </where>
        </if>
    </delete>
</mapper>