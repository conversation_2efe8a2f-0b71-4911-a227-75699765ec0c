<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.finance.master.FinanceMapper">

    <insert id="batchExecSql" parameterType="java.util.Map">
        ${sql1}
        <foreach collection="list" item="li" separator=",">
            ${sql2}
        </foreach>
        ${sql3}
    </insert>

    <update id="updateFinanceAppidMoney" parameterType="com.wbgame.pojo.finance.FinanceAppidMoney">
    update finance_appid_money
    set company = #{company,jdbcType=VARCHAR},
      cname = #{cname,jdbcType=VARCHAR},
      account = #{account,jdbcType=VARCHAR},
      platform_recharge = #{platformRecharge,jdbcType=VARCHAR},
      grant_income = #{grantIncome,jdbcType=VARCHAR},
      cash_consume = #{cashConsume,jdbcType=VARCHAR},
      grant_consume = #{grantConsume,jdbcType=VARCHAR},
      platform_consume = #{platformConsume,jdbcType=VARCHAR},
      dollar = #{dollar,jdbcType=VARCHAR},
      agent = #{agent,jdbcType=VARCHAR},
      rebate = #{rebate,jdbcType=VARCHAR},
      contract_rebate_real_charged = #{contractRebateRealCharged,jdbcType=VARCHAR},
      contract_rebate_real_recharged = #{contractRebateRealRecharged,jdbcType=VARCHAR}
    where appid = #{appid,jdbcType=VARCHAR}
      and year = #{year,jdbcType=VARCHAR}
      and month = #{month,jdbcType=VARCHAR}
      and account_id = #{accountId,jdbcType=VARCHAR}
  </update>

    <update id="syncFinanceAppid" parameterType="com.wbgame.pojo.finance.FinanceAppidMoney">
    update finance_appid_money
    set
      appid = #{appid,jdbcType=VARCHAR}
    where appid = 0
      and year = #{year,jdbcType=VARCHAR}
      and month = #{month,jdbcType=VARCHAR}
      and account_id = #{accountId,jdbcType=VARCHAR}
  </update>

    <insert id="insertFinanceAppidMoney" parameterType="com.wbgame.pojo.finance.FinanceAppidMoney">
        insert into finance_appid_money (year, month,
        account_id, company, cname,
        account, platform_recharge, grant_income,
        cash_consume, grant_consume, platform_consume,
        contract_rebate_real_charged,
        contract_rebate_real_recharged,
        dollar,agent,rebate)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.year,jdbcType=VARCHAR}, #{li.month,jdbcType=VARCHAR},
            #{li.accountId,jdbcType=VARCHAR}, #{li.company,jdbcType=VARCHAR}, #{li.cname,jdbcType=VARCHAR},
            #{li.account,jdbcType=VARCHAR}, #{li.platformRecharge,jdbcType=VARCHAR}, #{li.grantIncome,jdbcType=VARCHAR},
            #{li.cashConsume,jdbcType=VARCHAR}, #{li.grantConsume,jdbcType=VARCHAR},
            #{li.platformConsume,jdbcType=VARCHAR},
            #{li.contractRebateRealCharged,jdbcType=VARCHAR},
            #{li.contractRebateRealRecharged,jdbcType=VARCHAR},
            #{li.dollar,jdbcType=VARCHAR},#{li.agent,jdbcType=VARCHAR}, #{li.rebate,jdbcType=VARCHAR})
        </foreach>
        ON DUPLICATE KEY UPDATE
        company = VALUES(company),
        cname = VALUES(cname),
        account = VALUES(account),
        platform_recharge = VALUES(platform_recharge),
        grant_income = VALUES(grant_income),
        cash_consume = VALUES(cash_consume),
        grant_consume = VALUES(grant_consume),
        platform_consume = VALUES(platform_consume),
        contract_rebate_real_charged = VALUES(contract_rebate_real_charged),
        contract_rebate_real_recharged = VALUES(contract_rebate_real_recharged),
        dollar = VALUES(dollar),
        agent = VALUES(agent),
        rebate = VALUES(rebate)
    </insert>

    <insert id="insertFinanceAppidMoney2" parameterType="com.wbgame.pojo.finance.FinanceAppidMoney">
        insert into finance_appid_money (year, month,
        account_id, company, cname,
        account, platform_recharge, grant_income,
        cash_consume, grant_consume, platform_consume,
        contract_rebate_real_charged,
        contract_rebate_real_recharged,
        dollar,agent,rebate,appid)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.year,jdbcType=VARCHAR}, #{li.month,jdbcType=VARCHAR},
            #{li.accountId,jdbcType=VARCHAR}, #{li.company,jdbcType=VARCHAR}, #{li.cname,jdbcType=VARCHAR},
            #{li.account,jdbcType=VARCHAR}, #{li.platformRecharge,jdbcType=VARCHAR}, #{li.grantIncome,jdbcType=VARCHAR},
            #{li.cashConsume,jdbcType=VARCHAR}, #{li.grantConsume,jdbcType=VARCHAR},
            #{li.platformConsume,jdbcType=VARCHAR},
            #{li.contractRebateRealCharged,jdbcType=VARCHAR},
            #{li.contractRebateRealRecharged,jdbcType=VARCHAR},
            #{li.dollar,jdbcType=VARCHAR},#{li.agent,jdbcType=VARCHAR},
            #{li.rebate,jdbcType=VARCHAR},#{li.appid,jdbcType=VARCHAR})
        </foreach>
        ON DUPLICATE KEY UPDATE
        company = VALUES(company),
        cname = VALUES(cname),
        account = VALUES(account),
        platform_recharge = VALUES(platform_recharge),
        grant_income = VALUES(grant_income),
        cash_consume = VALUES(cash_consume),
        grant_consume = VALUES(grant_consume),
        platform_consume = VALUES(platform_consume),
        contract_rebate_real_charged = VALUES(contract_rebate_real_charged),
        contract_rebate_real_recharged = VALUES(contract_rebate_real_recharged),
        dollar = VALUES(dollar),
        agent = VALUES(agent),
        rebate = VALUES(rebate)
    </insert>

    <delete id="deleteFinanceAppidMoney" parameterType="com.wbgame.pojo.finance.FinanceAppidMoney">
    delete from finance_appid_money
    where appid = #{appid,jdbcType=VARCHAR}
      and year = #{year,jdbcType=VARCHAR}
      and month = #{month,jdbcType=VARCHAR}
      and account_id = #{accountId,jdbcType=VARCHAR}
  </delete>

    <select id="selectFinanceAppidMoney" resultType="com.wbgame.pojo.finance.FinanceAppidMoney">
        select
        <choose>
            <when test="group != null and group != ''">
                ifnull(app_name,'未关联') appname,a.appid,year, month, account_id accountId, company, cname, a.account, sum(platform_recharge)
                platformRecharge, sum(grant_income) grantIncome,
                sum(cash_consume) cashConsume, sum(grant_consume) grantConsume,
                sum(contract_rebate_real_charged) contractRebateRealCharged,
                sum(contract_rebate_real_recharged) contractRebateRealRecharged,
                sum((IFNULL(grant_consume,0)+IFNULL(cash_consume,0))) platformConsume,
                dollar,agent,round(avg(rebate),2) rebate
            </when>

            <otherwise>
                ifnull(app_name,'未关联') appname,a.appid,year, month, account_id accountId, company, cname, a.account, platform_recharge platformRecharge,
                grant_income grantIncome,
                cash_consume cashConsume, grant_consume grantConsume,
                contract_rebate_real_charged contractRebateRealCharged,
                contract_rebate_real_recharged contractRebateRealRecharged,
                (IFNULL(grant_consume,0)+IFNULL(cash_consume,0))
                platformConsume, dollar,agent,rebate
            </otherwise>
        </choose>
        from finance_appid_money a left join app_info b on a.appid = b.id
        where 1=1
        and !(platform_recharge = 0 and grant_income = 0 and cash_consume = 0 and grant_consume = 0 and
        (IFNULL(grant_consume,0)+IFNULL(cash_consume,0)) = 0)
        <if test="year != null and year != ''">
            and year = #{year}
        </if>
        <if test="month != null and month != ''">
            and month in ${month}
        </if>
        <if test="appid != null and appid != ''">
            and a.appid = #{appid}
        </if>
        <if test="company != null and company != ''">
            and company like concat('%',#{company},'%')
        </if>
        <if test="account != null and account != ''">
            and a.account like concat('%',#{account},'%')
        </if>
        <if test="accountId != null and accountId != ''">
            and account_id like concat('%',#{accountId},'%')
        </if>
        <if test="cname != null and cname != ''">
            and cname like concat('%',#{cname},'%')
        </if>
        <if test="agent != null and agent != ''">
            and agent like concat('%',#{agent},'%')
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

    <select id="syscFinanceAppidMoney" parameterType="com.wbgame.pojo.finance.FinanceAppidMoney"
            resultType="com.wbgame.pojo.finance.FinanceAppidMoney">
	    SELECT
		YEAR (DAY) year,
		MONTH (DAY) month,
		b.company company,
		ad_platform cname,
		b.accNum account,
		a.account accountId,
		sum(cash_cost) cashConsume,
		sum(cost) platformConsume,
		sum(reward_cost) grantConsume,
		sum(income) grantIncome,
		sum(transfer_in) platformRecharge,
        sum(contract_rebate_real_charged) contractRebateRealCharged,
        sum(contract_rebate_real_recharged) contractRebateRealRecharged,
		b.agent,
		b.rebate,
		IFNULL(a.app,0) appid
	FROM
		dn_report_finance a LEFT JOIN
		dn_config_finance b on a.account = b.account
	where MONTH (DAY) = #{month,jdbcType=VARCHAR}
		and YEAR (DAY) = #{year,jdbcType=VARCHAR}
		and a.account = #{account,jdbcType=VARCHAR}
	GROUP BY
		MONTH (DAY),
		account,
		ad_platform,
        appid
  </select>
    <select id="syscFinanceAppidMoney2" parameterType="com.wbgame.pojo.finance.FinanceAppidMoney"
            resultType="com.wbgame.pojo.finance.FinanceAppidMoney">
	  select YEAR (DAY) year,
			MONTH (DAY) month,
			x.company company,
			ad_platform cname,
			x.accNum account,
			x.account accountId,
	  	    sum(cash_cost) cashConsume,
			sum(cost) platformConsume,
			sum(reward_cost) grantConsume,
			sum(income) grantIncome,
			sum(transfer_in) platformRecharge,
            sum(contract_rebate_real_charged) contractRebateRealCharged,
            sum(contract_rebate_real_recharged) contractRebateRealRecharged,
			x.agent,
			x.rebate,
			x.app appid
	from
	(select day,
			b.company company,
			ad_platform ,
			b.accNum ,
			a.account ,
			cash_cost,
			cost ,
			reward_cost,
			income,
            contract_rebate_real_charged,
            contract_rebate_real_recharged,
			transfer_in,
			b.agent,
			b.rebate,
			IFNULL(a.app,0) app   FROM
			dn_report_finance a LEFT JOIN
			dn_config_finance b on a.account = b.account
	where
                    MONTH (DAY) = #{month,jdbcType=VARCHAR}
                and YEAR (DAY) = #{year,jdbcType=VARCHAR}	) x
	GROUP BY
		account,
		ad_platform,
        appid
    </select>

    <select id="selectSumFinanceAppidMoney" resultType="com.wbgame.pojo.finance.FinanceAppidMoney">
        select
        SUM(platform_recharge) platformRecharge, SUM(grant_income) grantIncome,
        SUM(cash_consume) cashConsume, SUM(grant_consume) grantConsume,
        SUM((IFNULL(grant_consume,0)+IFNULL(cash_consume,0))) platformConsume,
        SUM(contract_rebate_real_charged) contractRebateRealCharged,
        SUM(contract_rebate_real_recharged) contractRebateRealRecharged,
        SUM(dollar) dollar from finance_appid_money
        where 1=1
        <if test="year != null and year != ''">
            and year = #{year}
        </if>
        <if test="month != null and month != ''">
            and month in ${month}
        </if>
        <if test="appid != null and appid != ''">
            and appid = #{appid}
        </if>
        <if test="company != null and company != ''">
            and company like concat('%',#{company},'%')
        </if>
        <if test="account != null and account != ''">
            and account like concat('%',#{account},'%')
        </if>
        <if test="accountId != null and accountId != ''">
            and account_id like concat('%',#{accountId},'%')
        </if>
        <if test="cname != null and cname != ''">
            and cname like concat('%',#{cname},'%')
        </if>
        <if test="agent != null and agent != ''">
            and agent like concat('%',#{agent},'%')
        </if>
    </select>

    <insert id="insertFinanceCpsCost" parameterType="com.wbgame.pojo.finance.FinanceCpsCost">
        insert into finance_cps_cost (year, month, appid,
        cps, company, contract,
        income, ratio, taxes,
        cpsratio)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.year,jdbcType=VARCHAR}, #{li.month,jdbcType=VARCHAR}, #{li.appid,jdbcType=VARCHAR},
            #{li.cps,jdbcType=VARCHAR}, #{li.company,jdbcType=VARCHAR}, #{li.contract,jdbcType=VARCHAR},
            #{li.income,jdbcType=VARCHAR}, #{li.ratio,jdbcType=VARCHAR}, #{li.taxes,jdbcType=VARCHAR},
            #{li.cpsratio,jdbcType=VARCHAR})
        </foreach>
        ON DUPLICATE KEY UPDATE
        company = VALUES(company),
        contract = VALUES(contract),
        income = VALUES(income),
        ratio = VALUES(ratio),
        taxes = VALUES(taxes),
        cpsratio = VALUES(cpsratio)
    </insert>

    <update id="updateFinanceCpsCost" parameterType="com.wbgame.pojo.finance.FinanceCpsCost">
    update finance_cps_cost
    set company = #{company,jdbcType=VARCHAR},
      contract = #{contract,jdbcType=VARCHAR},
      income = #{income,jdbcType=VARCHAR},
      ratio = #{ratio,jdbcType=VARCHAR},
      taxes = #{taxes,jdbcType=VARCHAR},
      cpsratio = #{cpsratio,jdbcType=VARCHAR}
    where year = #{year,jdbcType=VARCHAR}
      and month = #{month,jdbcType=VARCHAR}
      and appid = #{appid,jdbcType=VARCHAR}
      and cps = #{cps,jdbcType=VARCHAR}
  </update>

    <select id="selectFinanceCpsCost" resultType="com.wbgame.pojo.finance.FinanceCpsCost">
        select
        <choose>
            <when test="group != null and group != ''">
                year, month, appid, cps, company, contract, sum(income) as income, round(avg(ratio),2) as ratio,
                round(avg(taxes),2) as taxes, sum(cpsratio) as cpsratio
            </when>

            <otherwise>
                year, month, appid, cps, company, contract, income, ratio, taxes, cpsratio
            </otherwise>
        </choose>
        from finance_cps_cost
        where 1=1
        <if test="year != null and year != ''">
            and year = #{year}
        </if>
        <if test="company != null and company != ''">
            and company like concat('%',#{company},'%')
        </if>
        <if test="month != null and month != ''">
            and month = #{month}
        </if>
        <if test="appid != null and appid != ''">
            and appid like concat('%',#{appid},'%')
        </if>
        <if test="cps != null and cps != ''">
            and cps like concat('%',#{cps},'%')
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

    <select id="selectSumFinanceCpsCost" resultType="com.wbgame.pojo.finance.FinanceCpsCost">
        select
        sum(income) income,sum(cpsratio) cpsratio
        from finance_cps_cost
        where 1=1
        <if test="year != null and year != ''">
            and year = #{year}
        </if>
        <if test="month != null and month != ''">
            and month = #{month}
        </if>
        <if test="appid != null and appid != ''">
            and appid like concat('%',#{appid},'%')
        </if>
        <if test="cps != null and cps != ''">
            and cps like concat('%',#{cps},'%')
        </if>
        <if test="company != null and company != ''">
            and company like concat('%',#{company},'%')
        </if>
    </select>

    <delete id="deleteFinanceCpsCost" parameterType="com.wbgame.pojo.finance.FinanceCpsCost">
    delete from finance_cps_cost
    where year = #{year,jdbcType=VARCHAR}
      and month = #{month,jdbcType=VARCHAR}
      and appid = #{appid,jdbcType=VARCHAR}
      and cps = #{cps,jdbcType=VARCHAR}
  </delete>

    <update id="updateFinanceDevCost" parameterType="com.wbgame.pojo.finance.FinanceDevCost">
    update finance_dev_cost
    set development = #{development,jdbcType=VARCHAR},
      contract = #{contract,jdbcType=VARCHAR},
      income = #{income,jdbcType=VARCHAR},
      ratio = #{ratio,jdbcType=VARCHAR},
      taxes = #{taxes,jdbcType=VARCHAR},
      devratio = #{devratio,jdbcType=VARCHAR},
      license = #{license,jdbcType=VARCHAR},
      advances = #{advances,jdbcType=VARCHAR}
    where appid = #{appid,jdbcType=VARCHAR}
      and month = #{month,jdbcType=VARCHAR}
      and year = #{year,jdbcType=VARCHAR}
  </update>

    <insert id="insertFinanceDevCost" parameterType="com.wbgame.pojo.finance.FinanceDevCost">
        insert into finance_dev_cost (appid, month, year,
        development, contract, income,
        ratio, taxes, devratio,
        license, advances)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.appid,jdbcType=VARCHAR}, #{li.month,jdbcType=VARCHAR}, #{li.year,jdbcType=VARCHAR},
            #{li.development,jdbcType=VARCHAR}, #{li.contract,jdbcType=VARCHAR}, #{li.income,jdbcType=VARCHAR},
            #{li.ratio,jdbcType=VARCHAR}, #{li.taxes,jdbcType=VARCHAR}, #{li.devratio,jdbcType=VARCHAR},
            #{li.license,jdbcType=VARCHAR}, #{li.advances,jdbcType=VARCHAR})
        </foreach>
        ON DUPLICATE KEY UPDATE
        development = VALUES(development),
        contract = VALUES(contract),
        income = VALUES(income),
        ratio = VALUES(ratio),
        taxes = VALUES(taxes),
        license = VALUES(license),
        advances = VALUES(advances),
        devratio = VALUES(devratio)
    </insert>

    <delete id="deleteFinanceDevCost" parameterType="com.wbgame.pojo.finance.FinanceDevCost">
    delete from finance_dev_cost
    where appid = #{appid,jdbcType=VARCHAR}
      and month = #{month,jdbcType=VARCHAR}
      and year = #{year,jdbcType=VARCHAR}
  </delete>

    <select id="selectFinanceDevCost" resultType="com.wbgame.pojo.finance.FinanceDevCost">
        select
        appid, month, year, development, contract, income, ratio, taxes, devratio, license,advances
        from finance_dev_cost
        where 1=1
        and month = #{month,jdbcType=VARCHAR}
        and year = #{year,jdbcType=VARCHAR}
        <if test="appid != null and appid != ''">
            and appid like concat('%',#{appid},'%')
        </if>
        <if test="development != null and development != ''">
            and development = #{development,jdbcType=VARCHAR}
        </if>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

    <select id="selectSumFinanceDevCost" resultType="com.wbgame.pojo.finance.FinanceDevCost">
        select
        sum(income) income, sum(devratio) devratio, sum(license) license, sum(advances) advances
        from finance_dev_cost
        where 1=1
        and month = #{month,jdbcType=VARCHAR}
        and year = #{year,jdbcType=VARCHAR}
        <if test="appid != null and appid != ''">
            and appid like concat('%',#{appid},'%')
        </if>
        <if test="development != null and development != ''">
            and development = #{development,jdbcType=VARCHAR}
        </if>

    </select>

    <insert id="insertFinanceOtherCost" parameterType="com.wbgame.pojo.finance.FinanceOtherCost">
        insert into finance_other_cost (`date`, `year`,`month`, media, appId, appCategory, payType,osType,business,spend,rebateSpend,serviceSpend)
        values
        <foreach collection="list" item="li" separator=",">
            (
            #{li.date},#{li.year},#{li.month}, #{li.media}, #{li.appId}, #{li.appCategory}, #{li.payType},
             #{li.osType}, #{li.business}, #{li.spend}, #{li.rebateSpend},#{li.serviceSpend}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        `date` = VALUES(`date`),
        `year` = VALUES(`year`),
        `month` = VALUES(`month`),
        media = VALUES(media),
        appId = VALUES(appId),
        appCategory = VALUES(appCategory),
        payType = VALUES(payType),
        osType = VALUES(osType),
        business = VALUES(business),
        spend = VALUES(spend),
        rebateSpend = VALUES(rebateSpend),
        serviceSpend = VALUES(serviceSpend)
    </insert>

    <update id="updateFinancePlfIncome" parameterType="com.wbgame.pojo.finance.FinancePlfIncome">
    update finance_plf_income
    set outcompany = #{outcompany,jdbcType=VARCHAR},
      incompany = #{incompany,jdbcType=VARCHAR},
      income = #{income,jdbcType=VARCHAR},
      appid = #{appid,jdbcType=VARCHAR},
      dollar_rate = #{dollarRate,jdbcType=VARCHAR},
      dollar = #{dollar,jdbcType=VARCHAR},
      dollar_settlement = #{dollarSettlement,jdbcType=VARCHAR},
      back_data = #{backData,jdbcType=VARCHAR},
      predicting_income = #{predictingIncome,jdbcType=VARCHAR},
      settlement_amount = #{settlementAmount,jdbcType=VARCHAR},
      dollar_data = #{dollarData,jdbcType=VARCHAR},
      predicting_data = #{predictingData,jdbcType=VARCHAR},
      differences = #{differences,jdbcType=VARCHAR},
      difference_note = #{differenceNote,jdbcType=VARCHAR},
      proportion = #{proportion,jdbcType=VARCHAR},
      increase = #{increase,jdbcType=VARCHAR}
    where year = #{year,jdbcType=VARCHAR}
      and month = #{month,jdbcType=VARCHAR}
      and project = #{project,jdbcType=VARCHAR}
      and cname = #{cname,jdbcType=VARCHAR}
  </update>

    <insert id="insertFinancePlfIncome" parameterType="com.wbgame.pojo.finance.FinancePlfIncome">
        insert into finance_plf_income (year, month, project,
        cname, outcompany, incompany,
        income, appid, dollar_rate,
        dollar, dollar_settlement, back_data,
        predicting_income, settlement_amount, dollar_data,
        predicting_data, differences, difference_note,
        proportion, increase)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.year,jdbcType=VARCHAR}, #{li.month,jdbcType=VARCHAR}, #{li.project,jdbcType=VARCHAR},
            #{li.cname,jdbcType=VARCHAR}, #{li.outcompany,jdbcType=VARCHAR}, #{li.incompany,jdbcType=VARCHAR},
            #{li.income,jdbcType=VARCHAR}, #{li.appid,jdbcType=VARCHAR}, #{li.dollarRate,jdbcType=VARCHAR},
            #{li.dollar,jdbcType=VARCHAR}, #{li.dollarSettlement,jdbcType=VARCHAR}, #{li.backData,jdbcType=VARCHAR},
            #{li.predictingIncome,jdbcType=VARCHAR}, #{li.settlementAmount,jdbcType=VARCHAR},
            #{li.dollarData,jdbcType=VARCHAR},
            #{li.predictingData,jdbcType=VARCHAR}, #{li.differences,jdbcType=VARCHAR},
            #{li.differenceNote,jdbcType=VARCHAR},
            #{li.proportion,jdbcType=VARCHAR}, #{li.increase,jdbcType=VARCHAR})
        </foreach>
        ON DUPLICATE KEY UPDATE
        outcompany = VALUES(outcompany),
        incompany = VALUES(incompany),
        income = VALUES(income),
        appid = VALUES(appid),
        dollar_rate = VALUES(dollar_rate),
        dollar = VALUES(dollar),
        dollar_settlement = VALUES(dollar_settlement),
        back_data = VALUES(back_data),
        predicting_income = VALUES(predicting_income),
        settlement_amount = VALUES(settlement_amount),
        dollar_data = VALUES(dollar_data),
        predicting_data = VALUES(predicting_data),
        differences = VALUES(differences),
        difference_note = VALUES(difference_note),
        proportion = VALUES(proportion),
        increase = VALUES(increase)
    </insert>

    <delete id="deleteFinancePlfIncome" parameterType="com.wbgame.pojo.finance.FinancePlfIncome">
    delete from finance_plf_income
    where year = #{year,jdbcType=VARCHAR}
      and month = #{month,jdbcType=VARCHAR}
      and project = #{project,jdbcType=VARCHAR}
      and cname = #{cname,jdbcType=VARCHAR}
  </delete>

    <select id="selectFinancePlfIncome" resultType="com.wbgame.pojo.finance.FinancePlfIncome">
        select
        year, month, project, cname, outcompany, incompany, income, appid, dollar_rate dollarRate, dollar,
        dollar_settlement dollarSettlement, back_data backData, predicting_income predictingIncome, settlement_amount
        settlementAmount,
        dollar_data dollarData,
        predicting_data predictingData, differences, difference_note differenceNote, proportion, increase
        from finance_plf_income
        where year = #{year,jdbcType=VARCHAR}
        and month = #{month,jdbcType=VARCHAR}
        <if test="appid != null and appid != ''">
            and appid like concat('%',#{appid},'%')
        </if>
        <if test="cname != null and cname != ''">
            and cname like concat('%',#{cname},'%')
        </if>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

    <select id="selectSumFinancePlfIncome" resultType="com.wbgame.pojo.finance.FinancePlfIncome">
        select
        sum(dollar) dollar ,
        sum(dollar_settlement) dollarSettlement, sum(back_data) backData, sum(predicting_income) predictingIncome,
        sum(settlement_amount) settlementAmount,
        sum(dollar_data) dollarData,
        sum(predicting_data) predictingData, sum(differences)
        from finance_plf_income
        where year = #{year,jdbcType=VARCHAR}
        and month = #{month,jdbcType=VARCHAR}
        <if test="appid != null and appid != ''">
            and appid like concat('%',#{appid},'%')
        </if>
        <if test="cname != null and cname != ''">
            and cname like concat('%',#{cname},'%')
        </if>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

    <select id="selectDnConfigFinance" resultType="com.wbgame.pojo.finance.DnConfigFinance">
        select
        account,channel,accNum, company,agent,rebate
        from dn_config_finance
        where 1=1
        <if test="account != null and account != ''">
            and account = #{account,jdbcType=VARCHAR}
        </if>
        <if test="accNum != null and accNum != ''">
            and accNum = #{accNum,jdbcType=VARCHAR}
        </if>
    </select>
    <delete id="deleteDnConfigFinance" parameterType="com.wbgame.pojo.finance.DnConfigFinance">
    delete from dn_config_finance
    where account = #{account,jdbcType=VARCHAR}
  </delete>
    <insert id="insertDnConfigFinance" parameterType="com.wbgame.pojo.finance.DnConfigFinance">
    insert into dn_config_finance (account, accNum, company,agent,rebate,channel)
    values (#{account,jdbcType=VARCHAR}, #{accNum,jdbcType=VARCHAR}, #{company,jdbcType=VARCHAR},
     #{agent,jdbcType=VARCHAR}, #{rebate,jdbcType=VARCHAR},#{channel,jdbcType=VARCHAR})
  </insert>
    <insert id="batchDnConfigFinance" parameterType="com.wbgame.pojo.finance.DnConfigFinance">
        insert into dn_config_finance (account, accNum, company,agent,rebate,channel)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.account,jdbcType=VARCHAR}, #{li.accNum,jdbcType=VARCHAR}, #{li.company,jdbcType=VARCHAR},
            #{li.agent,jdbcType=VARCHAR}, #{li.rebate,jdbcType=VARCHAR},#{li.channel,jdbcType=VARCHAR})
        </foreach>
        ON DUPLICATE KEY UPDATE
        accNum = VALUES(accNum),
        company = VALUES(company),
        agent = VALUES(agent),
        rebate = VALUES(rebate),
        channel = VALUES(channel)

    </insert>

    <update id="updateDnConfigFinance" parameterType="com.wbgame.pojo.finance.DnConfigFinance">
    update dn_config_finance
    set accNum = #{accNum,jdbcType=VARCHAR},
      company = #{company,jdbcType=VARCHAR},
      agent = #{agent,jdbcType=VARCHAR},
      rebate = #{rebate,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=INTEGER}
    where account = #{account,jdbcType=VARCHAR}
  </update>


    <select id="selectDnReportFinance" parameterType="com.wbgame.pojo.finance.DnReportFinance"
            resultType="com.wbgame.pojo.finance.DnReportFinance">
        select
        id,
        <if test="groups != null and groups != ''">
            ad_platform, app,
            sum(cash_cost) cash_cost, sum(cost) cost,
            sum(reward_cost) reward_cost, sum(income) income, sum(transfer_in) transfer_in,
            sum(contract_rebate_real_charged) contractRebateRealCharged,
            sum(contract_rebate_real_recharged) contractRebateRealRecharged,
            day,
        </if>
        <if test="groups == null or groups == ''">
            cash_cost, cost, ad_platform, app,
            reward_cost, income, transfer_in,
            contract_rebate_real_charged contractRebateRealCharged,
            contract_rebate_real_recharged contractRebateRealRecharged,
            day,
        </if>
        account, createTime
        from dn_report_finance
        where day between #{startTime} and #{endTime}
        <if test="account != null and account != ''">
            and account = #{account,jdbcType=VARCHAR}
        </if>
        <if test="groups != null and groups != ''">
            GROUP BY ${groups}
        </if>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

    <select id="selectSumDnReportFinance" parameterType="com.wbgame.pojo.finance.DnReportFinance"
            resultType="com.wbgame.pojo.finance.DnReportFinance">
        select ad_platform, sum(cash_cost) cash_cost, sum(cost) cost,
        sum(reward_cost) reward_cost, sum(income) income, sum(transfer_in) transfer_in,
        sum(contract_rebate_real_charged) contract_rebate_real_charged,
        sum(contract_rebate_real_recharged) contract_rebate_real_recharged
        from dn_report_finance
        where day between #{startTime} and #{endTime}
        <if test="account != null and account != ''">
            and account = #{account,jdbcType=VARCHAR}
        </if>
    </select>

    <update id="updateFinancePreIncome" parameterType="com.wbgame.pojo.finance.FinancePreIncome">
    update finance_pre_income
    set project = #{project,jdbcType=VARCHAR},
      outcompany = #{outcompany,jdbcType=VARCHAR},
      incompany = #{incompany,jdbcType=VARCHAR},
      origin = #{origin,jdbcType=VARCHAR},
      dollar_rate = #{dollarRate,jdbcType=DECIMAL},
      dollar = #{dollar,jdbcType=DECIMAL},
      back_data = #{backData,jdbcType=DECIMAL},
      predicting_income = #{predictingIncome,jdbcType=DECIMAL}
    where  year = #{year,jdbcType=VARCHAR}
      and month = #{month,jdbcType=VARCHAR}
      and cname = #{cname,jdbcType=VARCHAR}
      and appid = #{appid,jdbcType=VARCHAR}
  </update>
    <delete id="deleteFinancePreIncome" parameterType="com.wbgame.pojo.finance.FinancePreIncome">
    delete from finance_pre_income
    where year = #{year,jdbcType=VARCHAR}
      and month = #{month,jdbcType=VARCHAR}
      and cname = #{cname,jdbcType=VARCHAR}
      and appid = #{appid,jdbcType=VARCHAR}
  </delete>
    <select id="selectFinancePreIncome" resultType="com.wbgame.pojo.finance.FinancePreIncome">
        select
        year, month, cname, appid, project, outcompany, incompany, origin, dollar_rate dollarRate, dollar, back_data
        backData, predicting_income predictingIncome
        from finance_pre_income
        where year = #{year,jdbcType=VARCHAR}
        and month = #{month,jdbcType=VARCHAR}
        <if test="appid != null and appid != ''">
            and appid like concat('%',#{appid},'%')
        </if>
        <if test="cname != null and cname != ''">
            and cname like concat('%',#{cname},'%')
        </if>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>
    <select id="selectSumFinancePreIncome" resultType="com.wbgame.pojo.finance.FinancePreIncome">
        select
        sum(dollar) dollar, sum(back_data) backData, sum(predicting_income) predictingIncome
        from finance_pre_income
        where year = #{year,jdbcType=VARCHAR}
        and month = #{month,jdbcType=VARCHAR}
        <if test="appid != null and appid != ''">
            and appid like concat('%',#{appid},'%')
        </if>
        <if test="cname != null and cname != ''">
            and cname like concat('%',#{cname},'%')
        </if>
    </select>
    <insert id="insertFinancePreIncome" parameterType="com.wbgame.pojo.finance.FinancePreIncome">
        insert into finance_pre_income (year, month, cname,
        appid, project, outcompany,
        incompany, origin, dollar_rate,
        dollar, back_data, predicting_income
        )
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.year,jdbcType=VARCHAR}, #{li.month,jdbcType=VARCHAR}, #{li.cname,jdbcType=VARCHAR},
            #{li.appid,jdbcType=VARCHAR}, #{li.project,jdbcType=VARCHAR}, #{li.outcompany,jdbcType=VARCHAR},
            #{li.incompany,jdbcType=VARCHAR}, #{li.origin,jdbcType=VARCHAR}, #{li.dollarRate,jdbcType=DECIMAL},
            #{li.dollar,jdbcType=DECIMAL}, #{li.backData,jdbcType=DECIMAL}, #{li.predictingIncome,jdbcType=DECIMAL}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        project = VALUES(project),
        outcompany = VALUES(outcompany),
        incompany = VALUES(incompany),
        origin = VALUES(origin),
        dollar_rate = VALUES(dollar_rate),
        dollar = VALUES(dollar),
        back_data = VALUES(back_data),
        predicting_income = VALUES(predicting_income)
    </insert>

    <update id="updateFinanceSltIncome" parameterType="com.wbgame.pojo.finance.FinanceSltIncome">
    update finance_slt_income
    set year = #{year,jdbcType=VARCHAR},
      month = #{month,jdbcType=VARCHAR},
      project = #{project,jdbcType=VARCHAR},
      outcompany = #{outcompany,jdbcType=VARCHAR},
      cname = #{cname,jdbcType=VARCHAR},
      incompany = #{incompany,jdbcType=VARCHAR},
      origin = #{origin,jdbcType=VARCHAR},
      dollar_settlement = #{dollarSettlement,jdbcType=DECIMAL},
      settlement = #{settlement,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <insert id="insertFinanceSltIncome" parameterType="com.wbgame.pojo.finance.FinanceSltIncome">
        insert into finance_slt_income (year, month,
        project, outcompany, cname,
        incompany, origin, dollar_settlement,
        settlement)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.year,jdbcType=VARCHAR}, #{li.month,jdbcType=VARCHAR},
            #{li.project,jdbcType=VARCHAR}, #{li.outcompany,jdbcType=VARCHAR}, #{li.cname,jdbcType=VARCHAR},
            #{li.incompany,jdbcType=VARCHAR}, #{li.origin,jdbcType=VARCHAR}, #{li.dollarSettlement,jdbcType=DECIMAL},
            #{li.settlement,jdbcType=DECIMAL})
        </foreach>
    </insert>
    <select id="selectFinanceSltIncome" resultType="com.wbgame.pojo.finance.FinanceSltIncome">
        select
        id, year, month, project, outcompany, cname, incompany, origin, dollar_settlement dollarSettlement,
        settlement
        from finance_slt_income
        where year = #{year,jdbcType=VARCHAR}
        and month = #{month,jdbcType=VARCHAR}
        <if test="cname != null and cname != ''">
            and cname like concat('%',#{cname},'%')
        </if>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>
    <select id="selectSumFinanceSltIncome" resultType="com.wbgame.pojo.finance.FinanceSltIncome">
        select
        sum(dollar_settlement) dollarSettlement,
        sum(settlement) settlement
        from finance_slt_income
        where year = #{year,jdbcType=VARCHAR}
        and month = #{month,jdbcType=VARCHAR}
        <if test="cname != null and cname != ''">
            and cname like concat('%',#{cname},'%')
        </if>
    </select>
    <delete id="deleteFinanceSltIncome" parameterType="com.wbgame.pojo.finance.FinanceSltIncome">
    delete from finance_slt_income
    where id = #{id,jdbcType=INTEGER}
  </delete>

    <select id="selectAccountList" resultType="String">
  select DISTINCT account from dn_report_finance
  </select>

    <select id="selectFinancePreIncomeNew" resultType="java.util.Map">
        <choose>
            <when test="group != null and group != ''">
                select app_name,date,incompany,paycompany,agent,login_account,dnappid,sum(revenue_1) as
                revenue_1,sum(revenue_2) as revenue_2 from
                (select date,incompany,paycompany,a.agent,login_account,dnappid,if(type = 1 or type is null,revenue,0)
                as revenue_1,if(type = 2,revenue,0) as revenue_2,app_name
                FROM finance_agent_income a LEFT JOIN finance_account_config b ON a.member_id = b.account_id and a.agent
                = b.agent
                left join app_info c on a.dnappid = c.id) a
            </when>
            <otherwise>
                select app_name,date,incompany,paycompany,a.agent,login_account,dnappid,ifnull(if(type = 1 or type is
                null,revenue,0),0) revenue_1,ifnull(if(type = 2,revenue,0),0) revenue_2
                FROM finance_agent_income a LEFT JOIN finance_account_config b ON a.member_id = b.account_id and a.agent
                = b.agent
                left join app_info c on a.dnappid = c.id
            </otherwise>
        </choose>
        <where>date between #{startTime} and #{endTime}
            <if test="agent != null and agent != ''">
                and a.agent = #{agent}
            </if>
            <if test="dnappid != null and dnappid != ''">
                and app_name = #{dnappid}
            </if>
            <if test="incompany != null and incompany != ''">
                and incompany = #{incompany}
            </if>
        </where>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        order by date

    </select>

    <select id="countFinancePreIncomeNew" resultType="java.util.Map">
        select sum(revenue) as revenue,ifnull(type,1) type
        FROM finance_agent_income a LEFT JOIN finance_account_config b ON a.member_id = b.account_id and a.agent =
        b.agent
        left join app_info c on a.dnappid = c.id
        <where>date between #{startTime} and #{endTime}
            <if test="agent != null and agent != ''">
                and a.agent = #{agent}
            </if>
            <if test="dnappid != null and dnappid != ''">
                and app_name = #{dnappid}
            </if>
            <if test="incompany != null and incompany != ''">
                and incompany = #{incompany}
            </if>
        </where>
        group by ifnull(type,1)
    </select>

    <select id="selectFinancePreIncomeSum" resultType="com.wbgame.pojo.finance.FinancePreIncomeSum">
        <choose>
            <when test="group != null and group != ''">
                select year,month,incompany,paycompany,agent,login_account as loginAccount,appid,app_name as appName,
                ifnull(sum(dollar_money),0) as dollarMoney,ifnull(sum(rmb_money),0) as rmbMoney,income_type as
                incomeType
            </when>

            <otherwise>
                select a.id,year,month,incompany,paycompany,agent,login_account as loginAccount,appid,app_name as appName,
                dollar_money as dollarMoney,rmb_money as rmbMoney,income_type as incomeType,if(is_examine = 1,'已审核','未审核') examine
            </otherwise>
        </choose>
        from finance_pre_income_summary a left join app_info b on a.appid = b.id
        where 1=1
        <if test="year != null and year != ''">
            and year = #{year}
        </if>
        <if test="month != null and month != ''">
            and month in ${month}
        </if>
        <if test="appid != null and appid != ''">
            and appid = #{appid}
        </if>
        <if test="incompany != null and incompany != ''">
            and incompany in (${incompany})
        </if>
        <if test="loginAccount != null and loginAccount != ''">
            and login_account = #{loginAccount}
        </if>
        <if test="agent != null and agent != ''">
            and agent like concat('%',#{agent},'%')
        </if>
        <if test="incomeType != null and incomeType != ''">
            and income_type in (${incomeType})
        </if>
        <if test="examine != null and examine != ''">
            and is_examine = #{examine}
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <if test="order != null and order != ''">
            order by ${order}
        </if>

    </select>

    <select id="countFinancePreIncomeSum" resultType="java.util.Map">
        select ifnull(sum(dollar_money),0) as dollarMoney,ifnull(sum(rmb_money),0) as rmbMoney
        from finance_pre_income_summary
        where 1=1
        <if test="year != null and year != ''">
            and year = #{year}
        </if>
        <if test="month != null and month != ''">
            and month in ${month}
        </if>
        <if test="appid != null and appid != ''">
            and appid = #{appid}
        </if>
        <if test="incompany != null and incompany != ''">
            and incompany in (${incompany})
        </if>
        <if test="loginAccount != null and loginAccount != ''">
            and login_account = #{loginAccount}
        </if>
        <if test="agent != null and agent != ''">
            and agent like concat('%',#{agent},'%')
        </if>
        <if test="incomeType != null and incomeType != ''">
            and income_type in (${incomeType})
        </if>
        <if test="examine != null and examine != ''">
            and is_examine = #{examine}
        </if>
    </select>

    <select id="exportFinancePreIncomeSum" resultType="java.util.Map">
        <choose>
            <when test="group != null and group != ''">
                select year,month,incompany,paycompany,agent,login_account as loginAccount,appid,app_name as appName,
                ifnull(sum(dollar_money),0) as dollarMoney,ifnull(sum(rmb_money),0) as rmbMoney,
                case income_type when 1 then 'app广告收入' when 2 then '计费收入' when 3 then '小游戏广告收入' when 4 then '百度收入' else '其他收入' end as incomeTypeName
            </when>

            <otherwise>
                select a.id,year,month,incompany,paycompany,agent,login_account as loginAccount,appid,app_name as appName,
                dollar_money as dollarMoney,rmb_money as rmbMoney,if(is_examine = 1,'已审核','未审核') examine,
                case income_type when 1 then 'app广告收入' when 2 then '计费收入' when 3 then '小游戏广告收入' when 4 then '百度收入' else '其他收入' end as incomeTypeName
            </otherwise>
        </choose>
        from finance_pre_income_summary a left join app_info b on a.appid = b.id
        where 1=1
        <if test="year != null and year != ''">
            and year = #{year}
        </if>
        <if test="month != null and month != ''">
            and month in ${month}
        </if>
        <if test="appid != null and appid != ''">
            and appid = #{appid}
        </if>
        <if test="incompany != null and incompany != ''">
            and incompany in (${incompany})
        </if>
        <if test="loginAccount != null and loginAccount != ''">
            and login_account = #{loginAccount}
        </if>
        <if test="agent != null and agent != ''">
            and agent like concat('%',#{agent},'%')
        </if>
        <if test="incomeType != null and incomeType != ''">
            and income_type in (${incomeType})
        </if>
        <if test="examine != null and examine != ''">
            and is_examine = #{examine}
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

    <select id="selectSyncFinancePreIncomeSum1" resultType="com.wbgame.pojo.finance.FinancePreIncomeSum">
        select #{year} as year,#{month} as month,date,incompany,paycompany,a.agent,
        login_account as loginAccount,dnappid as appid,
        ifnull(if(type = 1 or type is null,(sum(revenue)*ifnull(b.share,100)/100),0),0) rmbMoney,ifnull(if(type = 2,(sum(revenue)*ifnull(b.share,100)/100),0),0) dollarMoney,1 as incomeType
        FROM finance_agent_income a LEFT JOIN finance_account_config b ON a.member_id = b.account_id and a.agent = b.agent
        left join app_info c on a.dnappid = c.id
        where date between #{startTime} and #{endTime}
        and a.agent is not null and dnappid is not null and login_account is not null
        group by a.agent,dnappid,login_account
    </select>

    <select id="selectSyncFinancePreIncomeSum2" resultType="com.wbgame.pojo.finance.FinancePreIncomeSum">
       select #{year} as year,#{month} as month,a.paytype as paycompany,a.paytype as agent,ifnull(c.id,15265) as appid
       ,CONCAT(a.paytype,a.appid) as loginAccount,(sum(a.money)*0.01*ifnull(b.share,100)/100) as rmbMoney,2 as incomeType,0 as dollarMoney,
        b.incompany
       from wb_pay_info a left join finance_account_config b on CONCAT(a.paytype,a.appid) = b.login_account
       left join app_info c on a.appid = c.id
       where a.orderstatus = 'SUCCESS' and a.appid is not null and date(a.createtime) between #{startTime} and #{endTime}
       group by a.paytype,a.appid
    </select>

    <select id="selectAllAgent" resultType="java.util.Map">
        select agent,agentName from finance_agent_config
    </select>

    <select id="existfinanceAccountConfig" resultType="java.lang.Long"
            parameterType="com.wbgame.pojo.finance.FinanceAccountConfig">
         select count(1) from finance_account_config where account_id = #{account_id} and agent = #{agent} limit 1
    </select>

    <insert id="insertFinancePreIncomeSum" parameterType="com.wbgame.pojo.finance.FinancePreIncomeSum">
        insert ignore into finance_pre_income_summary (year,month,incompany,paycompany,agent,
        login_account,appid,dollar_money,rmb_money,income_type)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.year}, #{li.month},
            #{li.incompany,jdbcType=VARCHAR}, #{li.paycompany,jdbcType=VARCHAR}, #{li.agent,jdbcType=VARCHAR},
            #{li.loginAccount,jdbcType=VARCHAR}, #{li.appid,jdbcType=VARCHAR}, #{li.dollarMoney,jdbcType=VARCHAR},
            #{li.rmbMoney,jdbcType=VARCHAR}, #{li.incomeType,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="insertExamineFinanceAppidMoney">
          insert ignore finance_appid_money_examine(year,month,`table`) values (#{year},#{month},#{table});
    </insert>

    <delete id="deleteExamineFinanceAppidMoney">
           delete from finance_appid_money_examine where year = #{year} and month  = #{month} and `table` = #{table} limit 1
    </delete>

    <select id="countExamineFinanceAppidMoney" resultType="java.lang.Long">
        select count(1) from finance_appid_money_examine where year = #{year} and month  = #{month} and `table` = #{table}
    </select>

    <select id="selectDnReportFinaceGroup" resultType="java.util.Map">
         select account,app from dn_report_finance where app is not null GROUP BY account,app
    </select>

    <insert id="batchInsertAccountAppid">
        insert ignore dn_account_appid (account,appid) values
        <foreach collection="list" item="it" separator=",">
            (#{it.account},#{it.app})
        </foreach>
    </insert>

    <select id="selectSyncFinancePreIncomeSum3" resultType="com.wbgame.pojo.finance.FinancePreIncomeSum">
         select #{year} as year,#{month} as month,a.channel agent,ifnull(d.appid,15265) appid,(sum(ifnull(a.income,0))*ifnull(c.share,100)/100)  rmbMoney,
        ifnull(b.account,a.channel) loginAccount,c.incompany,c.paycompany,3 as incomeType ,0 as dollarMoney
        from app_channel_appid a LEFT JOIN  wx_channel_manage d on a.appid_key = d.ttappid
        left join app_channel_collect b on a.channel = b.cname and instr(b.appids,d.appid) > 0
        left join finance_account_config c on b.account = c.login_account and b.channel  = c.agent
        where tdate between #{startTime} and #{endTime}
        GROUP BY a.channel,b.account,d.appid
    </select>

    <insert id="batchInsertFinancePreIncomeSum">
        replace into finance_pre_income_summary (`year`, `month`, `agent`, `incompany`, `paycompany`, `login_account`, `appid`, `dollar_money`, `rmb_money`, `income_type`,is_examine)
        values
        <foreach collection="list" item="obj" separator=",">
            (#{obj.year}, #{obj.month}, #{obj.agent}, #{obj.incompany}, #{obj.paycompany}, #{obj.loginAccount}, #{obj.appid}, #{obj.dollarMoney}, #{obj.rmbMoney}, #{obj.incomeType},#{obj.examine})
        </foreach>
    </insert>

    <select id="selectPredictIncomeOverview" resultType="com.wbgame.pojo.PredictIncomeOverview">
        select tdate,incompany,paycompany,type,amount,bill_date,bill_amount,different_reason,collect_date,collect_amount
        from finance_pre_income_overview
        <where>1=1
            <choose>
                <when test="month != null and month != ''">
                    and tdate = concat(#{year},#{month})
                </when>
                <otherwise>
                    and tdate like concat(#{year},'%')
                </otherwise>
            </choose>
            <if test="incompany != null and incompany != ''">
                and incompany in (${incompany})
            </if>
            <if test="paycompany != null and paycompany != ''">
                and paycompany = #{paycompany}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
        </where>
        order by ${order}
    </select>

    <update id="updatePredictIncomeOverview">
        update finance_pre_income_overview
        <trim prefix="set" suffixOverrides=",">
            <if test="bill_date != null and bill_date != ''">
                bill_date = #{bill_date},
            </if>
            <if test="bill_amount != null and bill_amount != ''">
                bill_amount = #{bill_amount},
            </if>
            <if test="different_reason != null and different_reason != ''">
                different_reason = #{different_reason},
            </if>
            <if test="collect_date != null and collect_date != ''">
                collect_date = #{collect_date},
            </if>
            <if test="collect_amount != null and collect_amount != ''">
                collect_amount = #{collect_amount},
            </if>
        </trim>
        where tdate = #{tdate}
            and incompany = #{incompany}
            and paycompany = #{paycompany}
            and type = #{type}


    </update>

    <insert id="batchInsertPredictIncomeOverview">
        replace into finance_pre_income_overview(tdate,incompany,paycompany,type,amount)
        values
        <foreach collection="list" item="it" separator=",">
            (#{it.tdate},#{it.incompany},#{it.paycompany},#{it.type},#{it.amount})
        </foreach>
    </insert>

    <select id="exportNameRelation" resultType="java.util.Map">
        select a.gameName as agameName,b.gameName as bgameName,channelTag,b.appid
        from dnwx_client.wbgui_formconfig a,dnwx_client.wbgui_gametype b where a.typeName = b.gameId
        group by a.gameName,b.gameName,channelTag,b.appid
    </select>
    <select id="selectSyncProIncomeOverview" resultType="com.wbgame.pojo.PredictIncomeOverview">
        select concat(#{year},'年',#{month},'月') as tdate,a.channel as paycompany,sum(ifnull(a.income,0)) amount,
        ifnull(c.incompany,'') incompany,'小游戏广告收入' as type
        from app_channel_appid a LEFT JOIN  wx_channel_manage d on a.appid_key = d.ttappid
        left join app_channel_collect b on a.channel = b.cname and instr(b.appids,d.appid) > 0
        left join finance_account_config c on b.account = c.login_account and b.channel  = c.agent
        where tdate between #{startTime} and #{endTime}
        GROUP BY a.channel,incompany
        union all
		select concat(#{year},'年',#{month},'月') as tdate,a.paytype as paycompany,
        (sum(a.money)*0.01*ifnull(b.share,100)/100) as amount,ifnull(b.incompany,'') incompany ,'计费收入' as type
       from wb_pay_info a left join finance_account_config b on CONCAT(a.paytype,a.appid) = b.login_account
       left join app_info c on a.appid = c.id
       where a.orderstatus = 'SUCCESS' and a.appid is not null and a.createtime between #{startTime} and #{endTime}
       group by a.paytype,b.incompany
		union all
		select concat(#{year},'年',#{month},'月') as tdate,a.agent paycompany,
        ifnull(if(type = 1 or type is null,sum(revenue),0),0) amount,ifnull(incompany,'') incompany,'app广告收入' as type
        FROM finance_agent_income a LEFT JOIN finance_account_config b ON a.member_id = b.account_id and a.agent = b.agent
        left join app_info c on a.dnappid = c.id
        where date between #{startTime} and #{endTime}
        and a.agent is not null and dnappid is not null and login_account is not null
        group by a.agent,incompany
    </select>

    <select id="countPredictIncomeOverview" resultType="com.wbgame.pojo.PredictIncomeOverview">
         select sum(amount) amount,sum(bill_amount) bill_amount,sum(collect_amount) collect_amount
        from finance_pre_income_overview
        <where>1=1
            <choose>
                <when test="month != null and month != ''">
                    and tdate = concat(#{year},#{month})
                </when>
                <otherwise>
                    and tdate like concat(#{year},'%')
                </otherwise>
            </choose>
            <if test="incompany != null and incompany != ''">
                and incompany in (${incompany})
            </if>
            <if test="paycompany != null and paycompany != ''">
                and paycompany = #{paycompany}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
        </where>
    </select>

    <select id="selectFinanceAppidMoneyImport" resultType="com.wbgame.pojo.finance.FinanceAppidMoneyImport">
        select
        <choose>
            <when test="group != null and group != ''">
                ifnull(b.appname,'未关联') appname,ifnull(b.app,'未关联') appid,year, month, account_id accountId, company, cname,
                sum(grant_income) grantIncome,sum(cash_consume) cashConsume, sum(grant_consume) grantConsume,
                sum(platform_consume) platformConsume,sum(real_amount) realAmount,sum(rebate_amount) rebateAmount,
                agent,round(avg(rebate),2) rebate
            </when>

            <otherwise>
                ifnull(b.appname,'未关联') appname,ifnull(b.app,'未关联') appid,year, month, account_id accountId, company, cname,
                grant_income grantIncome,cash_consume cashConsume, grant_consume grantConsume,platform_consume platformConsume,
                real_amount realAmount,rebate_amount rebateAmount,agent,rebate
            </otherwise>
        </choose>
        from finance_appid_money_import a left join finance_account_sum b on a.account_id = b.account and a.date = b.date
        where 1=1
        <if test="year != null and year != ''">
            and year = #{year}
        </if>
        <if test="month != null and month != ''">
            and month in ${month}
        </if>
        <if test="appid != null and appid != ''">
            and b.app like concat('%',#{appid},'%')
        </if>
        <if test="company != null and company != ''">
            and company like concat('%',#{company},'%')
        </if>
        <if test="accountId != null and accountId != ''">
            and account_id like concat('%',#{accountId},'%')
        </if>
        <if test="cname != null and cname != ''">
            and cname like concat('%',#{cname},'%')
        </if>
        <if test="agent != null and agent != ''">
            and agent like concat('%',#{agent},'%')
        </if>
        <if test="group != null and group != ''">
            group by ${group}
        </if>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

    <select id="selectSumFinanceAppidMoneyImport" resultType="com.wbgame.pojo.finance.FinanceAppidMoneyImport">
        select
        sum(grant_income) grantIncome,sum(cash_consume) cashConsume, sum(grant_consume) grantConsume,
        sum(platform_consume) platformConsume,sum(real_amount) realAmount,sum(rebate_amount) rebateAmount
        from finance_appid_money_import a left join finance_account_sum b on a.account_id = b.account and a.date = b.date
        where 1=1
        <if test="year != null and year != ''">
            and year = #{year}
        </if>
        <if test="month != null and month != ''">
            and month in ${month}
        </if>
        <if test="appid != null and appid != ''">
            and b.app like concat('%',#{appid},'%')
        </if>
        <if test="company != null and company != ''">
            and company like concat('%',#{company},'%')
        </if>
        <if test="accountId != null and accountId != ''">
            and account_id like concat('%',#{accountId},'%')
        </if>
        <if test="cname != null and cname != ''">
            and cname like concat('%',#{cname},'%')
        </if>
        <if test="agent != null and agent != ''">
            and agent like concat('%',#{agent},'%')
        </if>
    </select>

    <insert id="batchInsertFinanceAppidMoneyImport">
        insert into finance_appid_money_import (year,month,date,account_id,company,agent,rebate,cname,grant_income,cash_consume,grant_consume,platform_consume,real_amount,rebate_amount)
        values
        <foreach collection="list" item="it" separator=",">
            (#{it.year},#{it.month},#{it.date},#{it.accountId},#{it.company},#{it.agent},#{it.rebate},#{it.cname},
            #{it.grantIncome},#{it.cashConsume},#{it.grantConsume},#{it.platformConsume},#{it.realAmount},
            #{it.rebateAmount})
        </foreach>
    </insert>

    <select id="selectBaiduAccountConfigs" resultType="java.util.Map">
        select account,appid,create_owner,update_owner,app_name appname,bdappid,remark
        ,DATE_FORMAT(a.create_time,'%Y-%m-%d %H:%i:%s') createStr,DATE_FORMAT(a.update_time,'%Y-%m-%d %H:%i:%s') updateStr
        from finance_baidu_account_config a left join app_info b on a.appid = b.id
        where 1=1
        <if test="account != null and account != ''">
            and account = #{account}
        </if>
        <if test="appid != null and appid != ''" >
            and appid = #{appid}
        </if>
        <if test="bdappid != null and bdappid != ''">
            and bdappid = #{bdappid}
        </if>
    </select>

    <insert id="batchInsertBaiduAccountConfigImport">
        insert ignore into finance_baidu_account_config(account,appid,bdappid,create_owner,update_owner,create_time,update_time,remark) values
        <foreach collection="list" separator="," item="it">
            (#{it.account},#{it.appid},#{it.bdappid},#{it.create_owner},#{it.update_owner},now(),now(),#{it.remark})
        </foreach>
        ON DUPLICATE KEY UPDATE
        update_owner=VALUES(update_owner),
        update_time=now(),
        bdappid = VALUES(bdappid)
    </insert>

    <insert id="insertBaiduAccountConfig">
        insert into finance_baidu_account_config(account,appid,bdappid,create_owner,update_owner,create_time,update_time,remark) values
         (#{account},#{appid},#{bdappid},#{create_owner},#{update_owner},now(),now(),#{remark})
    </insert>

    <insert id="updateBaiduAccountConfig">
        update finance_baidu_account_config
        set bdappid = #{bdappid},
        remark = #{remark},
        update_owner = #{update_owner},
        update_time = now()
        where account = #{account} and appid = #{appid}
    </insert>

    <delete id="deleteBaiduAccountConfig">
        delete from finance_baidu_account_config where account=#{account} and appid = #{appid} limit 1
    </delete>

    <select id="selectFinancePreIncomeDetail" resultType="java.util.Map">
        select * from
        (<include refid="dn_finance_preIncome_detail"/> ) xx

        order by ${order}
    </select>
    <sql id="dn_finance_preIncome_detail">

        select
            <choose>
                <when test="group != null and group != '' and group.contains('month')">
                    DATE_FORMAT(date,'%Y-%m') as date,
                    DATE_FORMAT(date,'%Y-%m') as month,
                </when>
                <otherwise>
                    date,
                </otherwise>
            </choose>
            a.appid,b.app_name,ca.name app_category,agent,sum(income) income,income_type,media_id,
            dc.media_name media_name,CONCAT(a.appid,a.cha_id) mapkey,
            if(income_type = 1,'广告收入',if(income_type = 3,'小游戏收入',if(income_type = 2,'计费收入','百度收入'))) income_name,
            login_account,
            incompany,
            paycompany,cha_id,if(os_type = 1,'安卓',if(os_type = 2,'ios',if(os_type = 3,'google',if(os_type = 4,'小游戏','')))) os_type
        from finance_pre_income_detail a
        left join app_info b on a.appid = b.id
        left join app_category ca on b.app_category = ca.id
        left join (
        select xx.app_id,xx.media_name from
        (   select app_id,medium_name as media_name from dnwx_cfg.adv_medium_ylh_info
            union
            select tappid as app_id,tappname as media_name from adv_platform_app_info) xx group by xx.app_id
        ) dc on a.media_id=dc.app_id

        <where> date between #{startTime} and #{endTime}
            <if test="appid != null and appid.length != 0">
                and a.appid in (${appid})
            </if>
            <if test="agent != null and agent != ''">
                and agent in (${agent})
            </if>
            <if test="incomeType != null and incomeType != ''">
                and income_type in (${incomeType})
            </if>
            <if test="app_category != null and app_category != ''">
                and app_category in (${app_category})
            </if>
            <if test="cha_id != null and cha_id != ''">
                and cha_id in (${cha_id})
            </if>
            <if test="os_type != null and os_type != ''">
                and os_type in (${os_type})
            </if>
            <if test="than_income != null and than_income != ''">
                and IFNULL(income,0) > 0
            </if>

            <if test="incompany != null and incompany != ''">
                and incompany in (${incompany})
            </if>
            <if test="loginAccount != null and loginAccount != ''">
                and login_account = #{loginAccount}
            </if>
        </where>

        <choose>
            <when test="group != null and group != ''">
                group by ${group}
            </when>
            <otherwise>
                group by date,login_account,agent,a.appid,cha_id
            </otherwise>
        </choose>
    </sql>

    <select id="countFinancePreIncomeDetail" resultType="java.util.Map">
        select sum(income) income from
        (<include refid="dn_finance_preIncome_detail"/> ) xx

    </select>
    <!--<select id="countFinancePreIncomeDetail" resultType="java.util.Map">
        select sum(income) income from finance_pre_income_detail a
        left join app_info b on a.appid = b.id
        <where> date between #{startTime} and #{endTime}
            <if test="appid != null and appid.length != 0">
                and a.appid in (${appid})
            </if>
            <if test="agent != null and agent != ''">
                and agent in (${agent})
            </if>
            <if test="incompany != null and incompany != ''">
                and incompany in (${incompany})
            </if>
            <if test="loginAccount != null and loginAccount != ''">
                and login_account = #{loginAccount}
            </if>
            <if test="incomeType != null and incomeType != ''">
                and income_type in (${incomeType})
            </if>
            <if test="category != null and category != ''">
                and app_category in (${category})
            </if>
            <if test="cha_id != null and cha_id != ''">
                and cha_id in (${cha_id})
            </if>
            <if test="os_type != null and os_type != ''">
                and os_type in (${os_type})
            </if>
        </where>
    </select>-->

    <select id="selectSynFinancePreIncome" resultType="java.util.Map">
        select date,a.login_account,appid,a.agent,(a.money*0.01*ifnull(b.share,100)/100) as income
        ,income_type,incompany,paycompany,null media_id,cha_id from
        (select date(createtime) date
        ,case paytype
         when '支付宝' then ifnull(param2,CONCAT(paytype,appid))
         when '微信支付' then ifnull(param2,CONCAT(paytype,appid))
         when '聚合支付' then ifnull(param2,CONCAT(paytype,appid))
         when '字节支付' then if(uid LIKE '%TC%',CONCAT(paytype,appid,'钻石'),CONCAT(paytype,appid,'现金'))
         else CONCAT(paytype,appid)
         end login_account
        ,ifnull(appid,0) as appid,paytype as agent
        ,sum(a.money) money,2 as income_type,ifnull(chaid,'') cha_id
        from wb_pay_info a
        where orderstatus = 'SUCCESS' and appid is not null and createtime between concat(#{startTime},' 00:00:00') and concat(#{endTime},' 23:59:59') and paytype not in ('oppo支付','小米支付','华为支付','vivo支付','荣耀支付')
        and (oaid != 'test' or oaid is null) and (param3 != 'test' or param3 is null)
        GROUP BY appid,paytype,date(createtime),chaid,login_account) a
		left join finance_account_config b on a.login_account = b.login_account

        union all

        select tdate,CONCAT(paytype,appid,channel) login_account,appid,paytype,if(c.app_category = 5,round(income-kobe_coupon,2),round(income,2)) income,2 as income_type,incompany,paycompany,null media_id,channel
        from
        (select tdate,appid,channel
        ,CASE channel
        WHEN 'oppo' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'vivo' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'xiaomi' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'oppoml' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'oppomj' THEN ifnull(pay_revenue_share_fre*(1-0.05)*0.9,0)
        WHEN 'vivoml' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'xiaomimj' THEN ifnull(pay_revenue_share_fre*(1-0.05)*0.7,0)
        WHEN 'huawei' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'huaweiml' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'huawei2' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'huaweiml2' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'h5_huawei' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'honor' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.02)*0.5
        ELSE ifnull(pay_revenue_share_fre,0)
        END income
        ,CASE channel
        WHEN 'oppo' THEN 'oppo支付'
        WHEN 'vivo' THEN 'vivo支付'
        WHEN 'xiaomi' THEN '小米支付'
        WHEN 'oppoml' THEN 'oppo支付'
        WHEN 'oppomj' THEN 'oppo支付'
        WHEN 'vivoml' THEN 'vivo支付'
        WHEN 'xiaomimj' THEN '小米支付'
        WHEN 'huawei' THEN '华为支付'
        WHEN 'huaweiml' THEN '华为支付'
        WHEN 'huawei2' THEN '华为支付'
        WHEN 'huaweiml2' THEN '华为支付'
        WHEN 'h5_huawei' THEN '华为支付'
        WHEN 'honor' THEN '荣耀支付'
        ELSE '其他'
        END paytype,
        ifnull(kobe_coupon*0.9,0) kobe_coupon
        from channel_total_report
        where appid is not null and tdate between #{startTime} and #{endTime} and channel in ('oppo','vivo','xiaomi','oppoml','oppomj','vivoml','xiaomimj','huawei','honor')
        ) a
        left join finance_account_config b on CONCAT(paytype,appid,channel) = b.login_account
        left join app_info c on a.appid = c.id
		where income > 0

        union all

        select date(createtime) date,CONCAT(paytype,appid) login_account,ifnull(appid,0) as appid,paytype as agent
        ,round((sum(a.money)*c.rate*ifnull(b.share,100)/100),2) as income,2 as income_type,incompany,paycompany,null media_id
        ,ifnull(chaid,'') cha_id
        from wb_pay_info_hw a
        left join finance_account_config b on CONCAT(paytype,appid) = b.login_account
        left join currency_exchange_rate c on date(createtime) = tdate
        where orderstatus = 'SUCCESS' and appid is not null and date(createtime) between concat(#{startTime},' 00:00:00') and concat(#{endTime},' 23:59:59') and ifnull(oaid,'')!= 'test'
        and change_type = 'USD,CNY'
        GROUP BY appid,paytype,date(createtime),chaid

        union all

        select date,ifnull(b.login_account,a.member_id),
                IF(dnappid is null,IF(a.agent='applovin' or a.agent='pangle' or a.agent='mobvista',39597,0),dnappid) appid,
            a.agent,if(a.agent = 'kuaishouh5',round((sum(revenue)*ifnull(b.share,100)/100),2),SUM(revenue)) income
            ,if(a.agent = 'Baidu',4,1) as income_type,incompany,paycompany,app_id media_id,ifnull(cha_id,'') cha_id
        from dn_cha_cash_total a
        left join finance_account_config b on a.member_id = b.account_id and a.agent = b.agent
        where date between #{startTime} and #{endTime}
        and ((a.agent = 'GDT' and app_id !=0) or (a.agent != 'GDT'))
        and (a.agent != 'vivo' or dnappid is not null)  and a.agent != 'ohayoo'
        GROUP BY date,member_id,dnappid,a.agent,cha_id

        union all

        select date,ifnull(b.login_account,a.member_id),IFNULL(dnappid,0) appid,a.agent,round((sum(revenue)*ifnull(b.share,100)/100),2) income,if(a.agent = 'Baidu',4,1) as income_type,incompany,paycompany,app_id media_id
        ,ifnull(cha_id,'') cha_id
        from dn_cha_cash_total a
        left join finance_account_config b on a.member_id = b.account_id and a.agent = b.agent
        where date between #{startTime} and #{endTime}
        and ((a.agent = 'GDT' and app_id !=0) or (a.agent != 'GDT'))
        and (a.agent != 'vivo' or dnappid is not null)  and a.agent = 'ohayoo'
        GROUP BY date,member_id,dnappid,a.agent,cha_id

        union all


        select tdate,ifnull(b.account,a.channel) login_account,ifnull(d.appid,0) appid,a.channel agent,round(sum(ifnull(a.income,0))*ifnull(c.share,100)/100,2) income,
        3 as income_type,incompany,paycompany,null media_id,
               case
                   when a.channel = '字节' then 'h5_tt'
                   when a.channel = '233' then '信息流投放'
                   else 'wx'
                   end as cha_id
        from app_channel_appid a LEFT JOIN  wx_channel_manage d on a.appid_key = d.ttappid
        left join app_channel_collect b on a.channel = b.cname and instr(b.appids,ifnull(d.appid,0)) > 0
        left join finance_account_config c on b.account = c.login_account and c.agent = b.channel
        where tdate between #{startTime} and #{endTime}
        GROUP BY a.channel,b.account,d.appid,tdate
    </select>

    <delete id="deleteFinancePreIncomeDetail">
         delete from finance_pre_income_detail where date between #{startTime} and #{endTime}
    </delete>

    <insert id="batchInsertFinancePreIncomeDetail">
        replace into finance_pre_income_detail(date,income_type,paycompany,incompany,agent,login_account,appid,income,media_id,cha_id) values
        <foreach collection="list" separator="," item="it">
            (#{it.date},#{it.income_type},#{it.paycompany},(#{it.incompany}),#{it.agent},#{it.login_account},#{it.appid},#{it.income},#{it.media_id},#{it.cha_id})
        </foreach>
     </insert>

    <select id="selectDnReportFinance1"  resultType="com.wbgame.pojo.finance.DnReportFinance1">
        select
        d.id,d.account,d.createTime,d.day as date,year(d.day) as year,month(d.day) as month,day(d.day) as day,
        d.ad_platform, d.app as appid,c.company,c.agent,c.rebate,a.app_name as appName,a.app_category as appCategory,
        <if test="groups != null and groups != ''">
            sum(round((c.rebate-1)*round((cash_cost/rebate),2),2)) as rebateNum,
            sum(round((cash_cost/rebate),2)) as realcost,
            sum(d.cash_cost) cashcost,
            sum(ifnull(d.cash_cost,0)+ifnull(d.reward_cost,0)+ifnull(d.contract_rebate_real_charged,0)) cost,
            sum(d.reward_cost) rewardcost, sum(d.income) income, sum(d.transfer_in) transfer_in,
            sum(d.contract_rebate_real_charged) contractRebateRealCharged,
            sum(d.contract_rebate_real_recharged) contractRebateRealRecharged,
            round(s.spend,2) as spendcost,
            round(s.spend,2) - sum(round(ifnull(d.cash_cost,0)+ifnull(d.reward_cost,0)+ifnull(d.contract_rebate_real_charged,0),2)) as diffcost
        </if>
        <if test="groups == null or groups == ''">
            round((c.rebate-1)*round((cash_cost/rebate),2),2) as rebateNum,
            round((cash_cost/rebate),2) as realcost,
            cash_cost as cashcost, (ifnull(d.cash_cost,0)+ifnull(d.reward_cost,0)+ifnull(d.contract_rebate_real_charged,0)) cost,
            d.reward_cost as rewardcost, d.income, d.transfer_in,
            d.contract_rebate_real_charged contractRebateRealCharged,
            d.contract_rebate_real_recharged contractRebateRealRecharged,
            round(s.spend,2) as spendcost,
            round(s.spend-ifnull(d.cash_cost,0)-ifnull(d.reward_cost,0)-ifnull(d.contract_rebate_real_charged,0),2) as diffcost
        </if>
        from dn_report_finance d
        LEFT JOIN dn_config_finance c on c.account = d.account
        LEFT JOIN app_info a on a.id = d.app
        LEFT JOIN (select day,account,sum(spend) spend from dn_report_spend_china_summary where day between #{startTime} and #{endTime} group by account,day ) s on (s.account = d.account and s.day = d.day)
        where d.day between #{startTime} and #{endTime}
        <if test="account != null and account != ''">
            and d.account = #{account,jdbcType=VARCHAR}
        </if>
        <if test="appid != null and appid != ''">
            and d.app = #{appid,jdbcType=VARCHAR}
        </if>
        <if test="company != null and company != ''">
            and c.company = #{company,jdbcType=VARCHAR}
        </if>
        <if test="agent != null and agent != ''">
            and c.agent = #{agent,jdbcType=VARCHAR}
        </if>
        <if test="ad_platform != null and ad_platform != ''">
            and d.ad_platform = #{ad_platform,jdbcType=VARCHAR}
        </if>
        <if test="groups != null and groups != ''">
            GROUP BY ${groups}
        </if>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

    <select id="selectSumDnReportFinance1" parameterType="com.wbgame.pojo.finance.DnReportFinance1"
            resultType="com.wbgame.pojo.finance.DnReportFinance1">
        select ad_platform, sum(cash_cost) cashcost,
        sum(ifnull(d.cash_cost,0)+ifnull(d.reward_cost,0)+ifnull(d.contract_rebate_real_charged,0)) cost,
        sum(reward_cost) rewardcost, sum(income) income, sum(transfer_in) transfer_in,
        sum(contract_rebate_real_charged) contractRebateRealCharged,
        sum(contract_rebate_real_recharged) contractRebateRealRecharged,
        sum(round((c.rebate-1)*round((cash_cost/rebate),2),2)) as rebateNum,
        sum(round((cash_cost/rebate),2)) as realcost,
        sum(round(s.spend,2)) as spendcost,
        sum(round(s.spend-ifnull(d.cash_cost,0)-ifnull(d.reward_cost,0)-ifnull(d.contract_rebate_real_charged,0),2)) as diffcost
        from dn_report_finance d
        LEFT JOIN dn_config_finance c on c.account = d.account
        LEFT JOIN (select day,account,sum(spend) spend from dn_report_spend_china_summary where day between #{startTime} and #{endTime} group by account,day ) s on (s.account = d.account and s.day = d.day)
        where d.day between #{startTime} and #{endTime}
        <if test="account != null and account != ''">
            and d.account = #{account,jdbcType=VARCHAR}
        </if>
        <if test="appid != null and appid != ''">
            and d.app = #{appid,jdbcType=VARCHAR}
        </if>
        <if test="company != null and company != ''">
            and c.company = #{company,jdbcType=VARCHAR}
        </if>
        <if test="agent != null and agent != ''">
            and c.agent = #{agent,jdbcType=VARCHAR}
        </if>
        <if test="ad_platform != null and ad_platform != ''">
            and ad_platform = #{ad_platform,jdbcType=VARCHAR}
        </if>
    </select>

<!--    <select id="selectSyncFinancePreIncomeSum4" resultType="com.wbgame.pojo.finance.FinancePreIncomeSum">-->
<!--        select #{year} as year,#{month} as month,account as loginAccount,appid,'baidu' agent,sum(income) as rmbMoney,-->
<!--        4 as income_type,incompany,paycompany,4 as incomeType ,0 as dollarMoney from baidu_cash_total a-->
<!--        left join finance_account_config b on a.account = b.login_account-->
<!--        where tdate between #{startTime} and #{endTime} and appid is not null-->
<!--        group by agent,account,appid-->
<!--    </select>-->

    <update id="updateDnReportFinance" parameterType="com.wbgame.pojo.finance.DnReportFinance1">
        update dn_report_finance
        set ad_platform = #{ad_platform},
            cash_cost =  #{cashcost},
            cost =  #{cost},
            reward_cost =  #{rewardcost},
            income =  #{income},
            transfer_in =  #{transfer_in},
            contract_rebate_real_charged =  #{contractRebateRealCharged},
            contract_rebate_real_recharged =  #{contractRebateRealRecharged},
            account =  #{account},
            app =  #{appid},
            day =  #{date}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <insert id="insertDnReportFinance" parameterType="com.wbgame.pojo.finance.DnReportFinance1">
        insert into dn_report_finance (ad_platform,cash_cost,cost,reward_cost,income,transfer_in,
               contract_rebate_real_charged,contract_rebate_real_recharged,account,app,day)
        values
        <foreach collection="list" item="li" separator=",">
            (#{li.ad_platform,jdbcType=VARCHAR},#{li.cashcost,jdbcType=VARCHAR},
            #{li.cost,jdbcType=VARCHAR}, #{li.rewardcost,jdbcType=VARCHAR}, #{li.income,jdbcType=VARCHAR},
            #{li.transfer_in,jdbcType=VARCHAR}, #{li.contractRebateRealCharged,jdbcType=VARCHAR}, #{li.contractRebateRealRecharged,jdbcType=VARCHAR},
            #{li.account,jdbcType=VARCHAR}, #{li.appid,jdbcType=VARCHAR},
            #{li.date,jdbcType=VARCHAR})
        </foreach>
    </insert>
     <insert id="batchAddFinanceReport" parameterType="com.wbgame.pojo.finance.FinanceReport">
        INSERT INTO dnwx_adt.dn_channel_report_finance
        (media,spend,cash_cost,cost,reward_cost,income,transfer_in,account,day,createTime,
         contract_rebate_real_charged,contract_rebate_real_recharged,cash_balance,not_cash_balance,exchange_cost,
         star_cost,rebate_cost,gift_cost,agent,putUser,installs,virtualSpend,rebate, type,appstore_cost,feed_cost
         ,league_cost,general_cost)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.ad_platform},#{item.spend},#{item.cash_cost},#{item.cost},#{item.reward_cost},#{item.income},#{item.transfer_in},#{item.account},#{item.day},#{item.createTime},
             #{item.contract_rebate_real_charged},#{item.contract_rebate_real_recharged},#{item.cash_balance},#{item.not_cash_balance},#{item.exchange_cost},
             #{item.star_cost},#{item.rebate_cost},#{item.gift_cost},#{item.agent},#{item.putUser},#{item.installs},#{item.virtualSpend},#{item.rebate},#{item.type}
             ,#{item.appstore_cost},#{item.feed_cost},#{item.league_cost},#{item.general_cost}
            )
        </foreach>
    </insert>
    <insert id="batchAddFinanceReportDuplicate">
        INSERT INTO dnwx_adt.dn_channel_report_finance
        (media,spend,cash_cost,cost,reward_cost,income,transfer_in,account,day,createTime,
         contract_rebate_real_charged,contract_rebate_real_recharged,cash_balance,not_cash_balance,exchange_cost,
         star_cost,rebate_cost,gift_cost,agent,putUser,installs,virtualSpend,rebate, type)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.ad_platform},#{item.spend},#{item.cash_cost},#{item.cost},#{item.reward_cost},#{item.income},#{item.transfer_in},#{item.account},#{item.day},#{item.createTime},
             #{item.contract_rebate_real_charged},#{item.contract_rebate_real_recharged},#{item.cash_balance},#{item.not_cash_balance},#{item.exchange_cost},
             #{item.star_cost},#{item.rebate_cost},#{item.gift_cost},#{item.agent},#{item.putUser},#{item.installs},#{item.virtualSpend},#{item.rebate},#{item.type}
            )
        </foreach> ON DUPLICATE KEY UPDATE
                        cash_cost = VALUES(cash_cost),
                        cost = VALUES(cost),
                        reward_cost = VALUES(reward_cost),
                        income = VALUES(income),
                        transfer_in = VALUES(transfer_in),
                        contract_rebate_real_charged = VALUES(contract_rebate_real_charged),
                        contract_rebate_real_recharged = VALUES(contract_rebate_real_recharged),
                        spend = VALUES(spend),
                        installs = VALUES(installs),
                        virtualSpend = VALUES(virtualSpend),
                        exchange_cost = VALUES(exchange_cost),
                        star_cost = VALUES(star_cost),
                        rebate_cost = VALUES(rebate_cost),
                        gift_cost = VALUES(gift_cost)
    </insert>

    <update id="examineFinacePreIncomeSummary">
        update finance_pre_income_summary set
        is_examine = #{examine}
        where id in (${ids})
    </update>
    <update id="updateBalance">
        <foreach collection="list" item="it" separator=" ">
            update dnwx_adt.dn_channel_report_finance set
            cash_balance = #{it.cash_balance},
            not_cash_balance = #{it.not_cash_balance}
            where `day` = #{date}
            and account = #{it.account}
            and media = #{media};
        </foreach>
    </update>

    <select id="selectFinancePreIncomeSumById" resultType="com.wbgame.pojo.finance.FinancePreIncomeSum">
        select id,year,month,agent,incompany,paycompany,login_account loginAccount,appid,income_type incomeType,dollar_money dollarMoney,rmb_money rmbMoney,is_examine examine
        from finance_pre_income_summary where id = #{id}
    </select>

    <delete id="deleteFinancePreIncomeSumNotExamine">
        delete from finance_pre_income_summary where year = #{year} and month = #{month} and is_examine = 0
    </delete>
    <delete id="delFinanceReport">
        DELETE FROM dnwx_adt.dn_channel_report_finance WHERE media = #{ad_platform}
        AND `day` IN
        <foreach collection="dayList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectFinancePreIncomeSumOne" resultType="com.wbgame.pojo.finance.FinancePreIncomeSum">
        select id,year,month,agent,incompany,paycompany,login_account loginAccount,appid,income_type incomeType,dollar_money dollarMoney,rmb_money rmbMoney,is_examine examine
        from finance_pre_income_summary where year = #{year} and month = #{month} and appid = #{appid} and login_account = #{loginAccount} and agent = #{agent}
    </select>

    <select id="getFinanceSpendReport" parameterType="com.wbgame.pojo.finance.FinanceSpendReportParam"
            resultType="com.wbgame.pojo.finance.FinanceSpendReportDTO">
        SELECT SUM(spend) AS spend,SUM(rebateSpend) AS rebateSpend,SUM(serviceSpend) AS serviceSpend,SUM(yearlyRateSpend) AS yearlyRateSpend, rebate,serviceRate,yearlyRate
        <if test="group != null and group.size > 0">
            ,
            <foreach collection="group" index="index" item="item" separator=",">
                <choose>
                    <when test="item == 'appCategory'">
                        a.app_category AS appCategory
                    </when>
                    <when test="item == 'agent'">
                        first_agent AS firstAgent, agent
                    </when>
                    <when test="item == 'rebate'">

                    </when>
                    <when test="item == 'osType'">
                        a.os_type AS osType
                    </when>
                    <when test="item == 'app'">
                        app,a.app_name AS appName
                    </when>
                    <otherwise>
                        `${item}`
                    </otherwise>
                </choose>
            </foreach>
        </if>
        FROM dn_report_spend_finance d LEFT JOIN app_info a on a.id = d.app
        <include refid="getSpendReportCondition1"/>
        <include refid="getSpendReportCondition2"/>
    </select>

    <select id="getFinanceSpendReportSummary" parameterType="com.wbgame.pojo.finance.FinanceSpendReportParam"
            resultType="com.wbgame.pojo.finance.FinanceSpendReportDTO">
        SELECT SUM(spend) AS spend,SUM(rebateSpend) AS rebateSpend,SUM(serviceSpend) AS serviceSpend
        FROM dn_report_spend_finance d LEFT JOIN app_info a on a.id = d.app
        <include refid="getSpendReportCondition1"/>
    </select>

    <select id="getFinanceOtherCostList" parameterType="com.wbgame.pojo.finance.FinanceOtherCost"
            resultType="com.wbgame.pojo.finance.FinanceOtherCost">
        SELECT SUM(spend) AS spend, LEFT(`date`,4) AS `year`,RIGHT(`date`,2) AS `month`
        <if test="group != null and group != ''">
            , ${group}
        </if>
        FROM finance_other_cost
        <include refid="getFinanceOtherCostCondition1"/>
        <include refid="getFinanceOtherCostCondition2"/>
    </select>

    <select id="getFinanceOtherCostListSummary" parameterType="com.wbgame.pojo.finance.FinanceOtherCost"
            resultType="com.wbgame.pojo.finance.FinanceOtherCost">
        SELECT SUM(spend) AS spend
        FROM finance_other_cost
        <include refid="getFinanceOtherCostCondition1"/>
    </select>

    <select id="getWithoutAppReport" parameterType="com.wbgame.pojo.finance.FinanceOtherCost"
        resultType="com.wbgame.pojo.finance.FinanceOtherCost">
        SELECT CONCAT(#{year},"-",#{month}) AS `date`,#{year} AS `year`,#{month1} AS `month`,#{payType} AS payType, ${spend} * sum(spend)/allSpend as spend,SUM(serviceSpend) AS serviceSpend,SUM(rebateSpend) AS rebateSpend, app AS appId,media,business,app_category AS appCategory,os_type AS osType from dn_report_spend_finance d
        LEFT JOIN app_info a on a.id = d.app
        LEFT JOIN (
            SELECT sum(spend) allSpend,app_category AS appCategory,os_type as osType,business AS business1,media as media1 from dn_report_spend_finance d
            LEFT JOIN app_info a ON a.id = d.app
            WHERE `day` BETWEEN #{start_date} AND #{end_date} AND media = #{media} AND business = #{business} AND app_category = #{appCategory} AND os_type = #{osType}
            GROUP BY app_category,os_type,business,media
        ) c
        ON c.media1 = d.media  and c.business1 = d.business AND c.appCategory = a.app_category AND c.osType = a.os_type
        WHERE `day` BETWEEN #{start_date}  AND #{end_date} AND media = #{media} AND business = #{business} AND app_category = #{appCategory} AND os_type = #{osType}
        GROUP BY app,business,media,app_category,os_type
    </select>

    <select id="getWithoutMediaReport" parameterType="com.wbgame.pojo.finance.FinanceOtherCost"
            resultType="com.wbgame.pojo.finance.FinanceOtherCost">
        SELECT CONCAT(#{year},"-",#{month}) AS `date`,#{year} AS `year`,#{month1} AS `month`,#{payType} AS payType, ${spend} * sum(spend)/allSpend as spend,SUM(serviceSpend) AS serviceSpend,SUM(rebateSpend) AS rebateSpend, app AS appId,media,business,app_category AS appCategory,os_type AS osType from dn_report_spend_finance d
        LEFT JOIN app_info a on a.id = d.app
        LEFT JOIN (
            SELECT sum(spend) allSpend,app_category AS appCategory,os_type as osType,business AS business1,app as app1 from dn_report_spend_finance d
            LEFT JOIN app_info a ON a.id = d.app
            WHERE `day` BETWEEN #{start_date} AND #{end_date} AND app = #{appId} AND business = #{business} AND app_category = #{appCategory} AND os_type = #{osType}
            GROUP BY app_category,os_type,business,app
        ) c
        ON c.app1 = d.app and c.business1 = d.business AND c.appCategory = a.app_category AND c.osType = a.os_type
        WHERE `day` BETWEEN #{start_date}  AND #{end_date} AND app = #{appId} AND business = #{business} AND app_category = #{appCategory} AND os_type = #{osType}
        GROUP BY app,business,media,app_category,os_type
    </select>

    <select id="getSameReport" parameterType="com.wbgame.pojo.finance.FinanceOtherCost"
            resultType="com.wbgame.pojo.finance.FinanceOtherCost">
        SELECT CONCAT(#{year},"-",#{month}) AS `date`,#{year} AS `year`,#{month1} AS `month`,#{payType} AS payType, ${spend} AS spend,SUM(serviceSpend) AS serviceSpend,SUM(rebateSpend) AS rebateSpend, app AS appId,media,business,app_category AS appCategory,os_type AS osType from dn_report_spend_finance d
        LEFT JOIN app_info a on a.id = d.app
        LEFT JOIN (
            SELECT sum(spend) allSpend,app_category AS appCategory,app as app1 from dn_report_spend_finance d
            LEFT JOIN app_info a ON a.id = d.app
            WHERE `day` BETWEEN #{start_date} AND #{end_date} AND app = #{appId}
            GROUP BY app
        ) c
        ON c.app1 = d.app AND c.appCategory = a.app_category
        WHERE `day` BETWEEN #{start_date}  AND #{end_date} AND app = #{appId}
        GROUP BY app,business,media
    </select>

    <select id="getBusiness" resultType="java.util.Map">
        SELECT id,bus_model,bus_port FROM dn_bus_model_config
    </select>

    <select id="getAppCategory" resultType="java.util.Map">
        SELECT id,`name` FROM app_category
        <include refid="getSpendReportCondition1"/>
    </select>

    <select id="getAppName" resultType="java.util.Map">
        SELECT id,app_name FROM app_info
        <include refid="getSpendReportCondition1"/>
    </select>
    <select id="selectOldFinance" resultType="com.wbgame.pojo.finance.FinanceReport">
        select * from dn_report_finance
        where `day` = #{day}
        and ad_platform = #{ad_platform}
    </select>

    <select id="selectFinanceOhayooConfig" resultType="com.wbgame.pojo.finance.FinanceOhayooVo">
        select appid,incompany,paycompany,login_account,create_owner,update_owner,create_time,update_time
        from finance_ohayoo_app_config where 1=1
        <if test="login_account != null and login_account != ''">
            and login_account like concat('%',#{login_account},'%')
        </if>
        <if test="paycompany != null and paycompany != ''">
            and paycompany like concat('%',#{paycompany},'%')
        </if>
        <if test="appid != null">
            and appid in (${appid})
        </if>
        <if test="incompany != null and incompany != ''">
            and incompany in (${incompany})
        </if>
        <if test="order_str != null and order_str != ''">
            ORDER BY ${order_str}
        </if>
    </select>

    <select id="selectFinanceOhayooConfigById" resultType="com.wbgame.pojo.finance.FinanceOhayooVo">
        select appid,incompany,paycompany,login_account,create_owner,update_owner,create_time,update_time
        from finance_ohayoo_app_config where appid = #{appid}
    </select>

    <insert id="addFinanceOhayooConfig">
         insert into finance_ohayoo_app_config(appid,login_account,create_owner,update_owner,incompany,paycompany) values
         (#{appid},#{login_account},#{create_owner},#{update_owner},#{incompany},#{paycompany})
    </insert>

    <update id="updateFinanceOhayooConfig">
        update finance_ohayoo_app_config
        set login_account = #{login_account},
        incompany = #{incompany},
        paycompany = #{paycompany},
        update_owner = #{update_owner}
        where appid = #{appid}
    </update>

    <delete id="deleteFinanceOhayooConfig">
        delete from finance_ohayoo_app_config where appid = #{appid}
    </delete>

    <sql id="getSpendReportCondition1">
        WHERE 1 = 1
        <if test="account != null and account.size > 0">
            AND account IN
            <foreach collection="account" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="media != null and media.size > 0">
            AND media IN
            <foreach collection="media" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="business != null and business.size > 0">
            AND business IN
            <foreach collection="business" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="osType != null and osType.size > 0">
            AND os_type IN
            <foreach collection="osType" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="appCategory != null and appCategory.size > 0">
            AND app_category IN
            <foreach collection="appCategory" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="app != null and app.size > 0">
            AND `app` IN
            <foreach collection="app" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="firstAgent != null and firstAgent.size > 0">
            AND first_agent IN
            <foreach collection="firstAgent" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="agent != null and agent.size > 0">
            AND agent IN
            <foreach collection="agent" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="start_date != null and end_date!= null">
            AND day BETWEEN #{start_date} AND #{end_date}
        </if>
    </sql>

    <sql id="getSpendReportCondition2">
        <if test="group != null and group.size>0">
            GROUP BY
            <foreach collection="group" index="index" item="item" separator=",">
                <choose>
                    <when test="item == 'agent'">
                        first_agent, agent
                    </when>
                    <when test="item == 'appCategory'">
                        a.app_category
                    </when>
                    <when test="item == 'rebate'">
                        rebate, serviceRate
                    </when>
                    <when test="item == 'osType'">
                        a.os_type
                    </when>
                    <otherwise>
                        `${item}`
                    </otherwise>
                </choose>
            </foreach>
        </if>
        <if test="order_str != null and order_str != ''">
            ORDER BY ${order_str}
        </if>
        <if test="order_str == null or order_str == ''">
            ORDER BY spend desc,`day` asc
        </if>
    </sql>

    <sql id="getFinanceOtherCostCondition1">
        WHERE 1 = 1
        <if test="appId != null and appId !='' ">
            AND appId IN (${appId})
        </if>
        <if test="media != null and media !=''">
            AND media IN (${media})
        </if>
        <if test="appCategory != null  and appCategory !=''">
            AND appCategory IN (${appCategory})
        </if>
        <if test="payType != null and payType !='' ">
            AND payType IN (${payType})
        </if>
        <if test="osType != null and osType !=''">
            AND osType IN (${osType})
        </if>
        <if test="business != null and business !=''">
            AND business IN (${business})
        </if>
        <if test="year != null and year != ''">
            AND `year` = #{year}
        </if>
        <if test="month != null and month!= ''">
            AND `month` = #{month}
        </if>
            AND `date` BETWEEN #{start_date} AND #{end_date}
    </sql>

    <sql id="getFinanceOtherCostCondition2">
        <if test="group != null and group != '' ">
            GROUP BY ${group}
        </if>
        <if test="order_str != null and order_str != ''">
            ORDER BY ${order_str}
        </if>
        <if test="order_str == null or order_str == ''">
            ORDER BY spend desc,`year` asc,`month` asc
        </if>
    </sql>



    <update id="batchUpdateFinanceAccountConfig" >
        update finance_account_config
        <set>
            <if test="app.share != null and app.share != '' ">
                `share` = #{app.share},
            </if>
            <if test="app.incompany != null and app.incompany != '' ">
                incompany = #{app.incompany},
            </if>
            <if test="app.paycompany != null and app.paycompany != '' ">
                paycompany = #{app.paycompany},
            </if>
            <if test="app.login_account != null and app.login_account != '' ">
                login_account = #{app.login_account},
            </if>
            <if test="app.type != null and app.type != '' ">
                `type` = #{app.type},
            </if>
        </set>
        where
            <foreach collection="list" item="li" separator=" or ">
                (CONCAT(account_id,agent) = #{li})
            </foreach>
    </update>

</mapper>