<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.finance.master.DnRebateRateConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.finance.DnRebateRateConfigVO">
        <id column="media" property="media" jdbcType="VARCHAR"/>
        <id column="primary_agent" property="primaryAgent" jdbcType="VARCHAR"/>
        <id column="month" property="month" jdbcType="VARCHAR"/>
        <result column="rebate_rate" property="rebateRate" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
    </resultMap>

    <delete id="deleteDnRebateRateConfigById" parameterType="com.wbgame.pojo.finance.DnRebateRateConfigKey">

        <if test="list != null and list.size > 0">

            delete from dn_rebate_rate_config

            <where>

                <foreach collection="list" item="key">

                    or (media = #{key.media,jdbcType=VARCHAR} and primary_agent =#{key.primaryAgent,jdbcType=VARCHAR}
                    and month =#{key.month,jdbcType=VARCHAR})
                </foreach>

            </where>
        </if>

    </delete>

    <insert id="insertDnRebateRateConfig" parameterType="com.wbgame.pojo.finance.DnRebateRateConfig">
        insert into dn_rebate_rate_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="media != null and media != ''">
                media,
            </if>
            <if test="primaryAgent != null and primaryAgent != ''">
                primary_agent,
            </if>
            <if test="month != null and month != ''">
                month,
            </if>
            <if test="app_category != null">
                app_category,
            </if>
            <if test="rebateRate != null and rebateRate != ''">
                rebate_rate,
            </if>

            <if test="createUser != null and createUser != ''">
                create_user,
            </if>

            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="media != null and media != ''">
                #{media,jdbcType=VARCHAR},
            </if>
            <if test="primaryAgent != null and primaryAgent != ''">
                #{primaryAgent,jdbcType=VARCHAR},
            </if>
            <if test="month != null and month != ''">
                #{month},
            </if>
            <if test="app_category != null">
                #{app_category},
            </if>
            <if test="rebateRate != null and rebateRate != ''">
                #{rebateRate,jdbcType=VARCHAR},
            </if>

            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>
            current_timestamp,
            current_timestamp
        </trim>
    </insert>

    <update id="updateDnRebateRateConfigBatch">
        update dn_rebate_rate_config
        <set>
            <if test="rebateRate != null">
                rebate_rate = #{rebateRate,jdbcType=VARCHAR},
            </if>

            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            update_time = current_timestamp
        </set>
        where media = #{media,jdbcType=VARCHAR} and primary_agent = #{primaryAgent,jdbcType=VARCHAR} and month = #{month} and app_category = #{app_category}

    </update>
    <update id="updateDnRebateRateConfig" parameterType="com.wbgame.pojo.finance.DnRebateRateConfig">
        update dn_rebate_rate_config
        set rebate_rate = #{rebateRate,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            create_user = #{createUser,jdbcType=VARCHAR},
            update_user = #{updateUser,jdbcType=VARCHAR}
        where media = #{media,jdbcType=VARCHAR}
          and primary_agent = #{primaryAgent,jdbcType=VARCHAR} and month = #{month} and app_category = #{app_category}
    </update>

    <select id="selectRebateRateByCondition" resultMap="BaseResultMap">

        select
        media
        , primary_agent, concat(rebate_rate, "%") rebate_rate, date_format(create_time, '%Y-%m-%d %H:%i:%s') create_time,
        date_format(update_time, '%Y-%m-%d %H:%i:%s') update_time, create_user, update_user, month,app_category,b.name category_name
        from dn_rebate_rate_config a left join app_category b on a.app_category = b.id
        <where>

            <if test="media != null and media.size > 0">
                and media in
                <foreach collection="media" item="m" open="(" separator="," close=")">

                    #{m}
                </foreach>

            </if>
            <if test="primaryAgent != null and primaryAgent.size > 0">
                and primary_agent in
                <foreach collection="primaryAgent" item="p" open="(" separator="," close=")">

                    #{p}
                </foreach>
            </if>

            <if test="app_category != null and app_category.size > 0">
                and app_category in
                <foreach collection="app_category" item="i" open="(" separator="," close=")">
                    #{i}
                </foreach>
            </if>

            <if test="start_date != null and start_date != ''and start_date != null and start_date != ''">
                and month between #{start_date} and #{end_date}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>