<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.finance.master.FinMicGameRevenueMapper">
	
	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>
	


	<select id="selectDouyinGameRevenueList" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="douyin_game_revenue_sql"/>
	</select>

	<select id="selectDouyinGameRevenueListSum" parameterType="java.util.Map" resultType="java.util.Map">
		select

			TRUNCATE(SUM(xx.rebate_cost),2) rebate_cost,
			TRUNCATE(SUM(xx.pay_income_total),2) pay_income_total,
			TRUNCATE(SUM(xx.mds_pay_income),2) mds_pay_income,
			TRUNCATE(SUM(xx.wx_pay_income),2) wx_pay_income,
			TRUNCATE(SUM(xx.tt_pay_income),2) tt_pay_income,
			TRUNCATE(SUM(xx.ad_income),2) ad_income,
			TRUNCATE(SUM(xx.pre_profit),2) pre_profit,
			TRUNCATE(SUM(xx.pay_income),2) pay_income,
			TRUNCATE(SUM(xx.all_income),2) all_income,
			TRUNCATE(SUM(xx.profit),2) profit,
			TRUNCATE(SUM(xx.ad_revenue),2) ad_revenue,
			TRUNCATE(SUM(xx.net_profit),2) net_profit,
			TRUNCATE(SUM(xx.refund_revenue),2) refund_revenue,
			TRUNCATE(SUM(xx.rebate_income),2) rebate_income,
			TRUNCATE(SUM(xx.spend),2) spend,
			TRUNCATE(SUM(xx.activity_spend),2) activity_spend,
			TRUNCATE(SUM(xx.buy_ad_income),2) buy_ad_income,
			TRUNCATE(SUM(xx.buy_pay_income),2) buy_pay_income,
			TRUNCATE(SUM(xx.subsidy_10p),2) subsidy_10p,
			TRUNCATE(SUM(xx.free_ad_income),2) free_ad_income,
			TRUNCATE(SUM(xx.free_pay_income),2) free_pay_income,
			TRUNCATE(SUM(xx.last_total_spend),2) last_total_spend,
			TRUNCATE(SUM(xx.last_total_income),2) last_total_income,
			TRUNCATE(SUM(xx.revise_spend),2) revise_spend,
			TRUNCATE(SUM(xx.revise_rebate_income),2) revise_rebate_income,
			TRUNCATE(SUM(xx.revise_profit),2) revise_profit,
			CONCAT(TRUNCATE(SUM(xx.revise_profit)/SUM(xx.revise_rebate_income)*100, 2),'%') revise_profit_rate,
			TRUNCATE(SUM(xx.pay_prefer_income),2) pay_prefer_income

		from (<include refid="douyin_game_revenue_sql"/>) xx
	</select>

	<sql id="douyin_game_revenue_sql">

		select zz.*,
			IFNULL(all_income+rebate_income-refund_revenue,0) revise_rebate_income,
			IFNULL(all_income+rebate_income-refund_revenue-revise_spend,0) revise_profit,
			IFNULL(TRUNCATE((all_income+rebate_income-refund_revenue-revise_spend)/(all_income+rebate_income-refund_revenue)*100, 2),0) revise_profit_rate

		from (
			select xx.*,
				TRUNCATE(xx.pay_income_total+yy.ad_income,2) pre_profit,
				TRUNCATE(xx.tt_pay_income * 0.9+yy.ad_income,2) all_income,
				TRUNCATE(xx.tt_pay_income * 0.9+yy.ad_income-xx.rebate_cost,2) profit,
				TRUNCATE(xx.tt_pay_income * 0.9+yy.ad_income-xx.rebate_cost-xx.refund_revenue,2) net_profit,
				yy.euser,
				yy.endtime,
				yy.ad_income,
				yy.rebate_income,
				yy.spend,
				yy.activity_spend,
				yy.buy_ad_income,
				yy.buy_pay_income,
				yy.subsidy_10p,
				yy.free_ad_income,
				yy.free_pay_income,
				yy.last_total_income,
				yy.pay_prefer_income,
				(yy.spend+yy.activity_spend-yy.subsidy_10p) last_total_spend,
				(xx.rebate_cost+yy.spend+yy.activity_spend-yy.subsidy_10p-xx.ad_revenue) revise_spend,
				TRUNCATE(xx.tt_pay_income * 0.9, 2) pay_income

			from
				(select
					${group},
					TRUNCATE(SUM(rebate_cost),2) rebate_cost,
					TRUNCATE(SUM(mds_pay_income)+SUM(wx_pay_income)+SUM(tt_pay_income),2) pay_income_total,
					TRUNCATE(SUM(mds_pay_income),2) mds_pay_income,
					TRUNCATE(SUM(wx_pay_income),2) wx_pay_income,
					TRUNCATE(SUM(tt_pay_income),2) tt_pay_income,
					/*TRUNCATE(SUM(mds_pay_income)+SUM(wx_pay_income)+SUM(tt_pay_income)+SUM(ad_income),2) pre_profit,*/

					TRUNCATE(SUM(tt_pay_income)*IFNULL(bb.rate, 0.57),2) pay_income_pre,
					/*TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*IFNULL(bb.rate, 0.57)+SUM(ad_income),2) all_income,
					TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*IFNULL(bb.rate, 0.57)+SUM(ad_income)-SUM(rebate_cost),2) profit,
					TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*IFNULL(bb.rate, 0.57)+SUM(ad_income)-SUM(rebate_cost)-SUM(refund_revenue),2) net_profit,*/
					TRUNCATE(SUM(ad_revenue),2) ad_revenue,
					IFNULL(TRUNCATE(SUM(cc.amount),2),0) refund_revenue

				from dn_micgame_revenue_total aa

				LEFT JOIN finance_douyin_window_config bb
				ON aa.tdate=bb.date AND aa.appid=bb.dnappid

				LEFT JOIN (SELECT success_date,appid app,TRUNCATE(SUM(refund_fee/100),2) amount FROM refund_order_info
							where success_date BETWEEN #{sdate} AND #{edate}
							and refund_status = 'SUCCESS'
							group by success_date,appid) cc
				ON aa.tdate=cc.success_date AND aa.appid=cc.app

				where tdate BETWEEN #{sdate} AND #{edate}
				and appid in (select id from app_info where app_category in (43))
				<if test="appid != null and appid != ''">
					and appid in (${appid})
				</if>
				group by ${group}) xx
			LEFT JOIN

				(select
					${group},
					euser,
					CONCAT(endtime,'') endtime,
					TRUNCATE(SUM(revenue),2) ad_income,
					TRUNCATE(SUM(rebate_income),2) rebate_income,
					TRUNCATE(SUM(spend),2) spend,
					TRUNCATE(SUM(activity_spend),2) activity_spend,
					TRUNCATE(SUM(buy_ad_income),2) buy_ad_income,
					TRUNCATE(SUM(buy_pay_income),2) buy_pay_income,
					TRUNCATE(SUM(subsidy_10p),2) subsidy_10p,
					TRUNCATE(SUM(free_ad_income),2) free_ad_income,
					TRUNCATE(SUM(free_pay_income),2) free_pay_income,
					TRUNCATE(SUM(last_total_income),2) last_total_income,
					ifnull(TRUNCATE(SUM(pay_prefer_income),2),0) pay_prefer_income
		from dn_douyin_xyx_spend_data
				where tdate BETWEEN #{sdate} AND #{edate}
				<if test="appid != null and appid != ''">
					and appid in (${appid})
				</if>
				group by ${group}) yy
			${group_match}
		) zz

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by rebate_cost desc
			</otherwise>
		</choose>

	</sql>

</mapper>