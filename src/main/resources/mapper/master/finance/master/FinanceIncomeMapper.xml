<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.finance.master.FinanceIncomeMapper">

    <select id="selectFinanceModelIncome" resultType="com.wbgame.pojo.FinanceModelIncomeVo">
        select tdate,appid,app_name,bus_model dn_bus_model_name,c.name app_category,media,
        sum(adv_income) adv_income,sum(pay_income) pay_income,sum(baidu_income) baidu_income
        ,if(os_type = 1,'安卓',if(os_type = 2,'ios',if(os_type = 3,'google',if(os_type = 4,'小游戏','')))) platform
        from finance_model_income_detail a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        left join dn_bus_model_config d on a.dn_bus_model_id = d.id
        where tdate between #{start_date} and #{end_date}
        <if test="platform != null and platform != ''">
            and os_type in (${platform})
        </if>
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="dn_bus_model_id != null and dn_bus_model_id != ''">
            and dn_bus_model_id in (${dn_bus_model_id})
        </if>
        <if test="app_category != null and app_category != ''">
            and app_category in (${app_category})
        </if>
        <choose>
            <when test="group != null and group != ''">
                group by ${group}
            </when>
            <otherwise>
                group by tdate,os_type,app_category,appid,dn_bus_model_id,media
            </otherwise>
        </choose>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate,adv_income desc
            </otherwise>
        </choose>
    </select>

    <select id="countFinanceModelIncome" resultType="com.wbgame.pojo.FinanceModelIncomeVo">
        select sum(adv_income) adv_income,sum(pay_income) pay_income,sum(baidu_income) baidu_income
        from finance_model_income_detail a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        left join dn_bus_model_config d on a.dn_bus_model_id = d.id
        where tdate between #{start_date} and #{end_date}
        <if test="platform != null and platform != ''">
            and os_type in (${platform})
        </if>
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="dn_bus_model_id != null and dn_bus_model_id != ''">
            and dn_bus_model_id in (${dn_bus_model_id})
        </if>
        <if test="app_category != null and app_category != ''">
            and app_category in (${app_category})
        </if>
    </select>

    <select id="selectAllFinanceIncome" resultType="com.wbgame.pojo.FinanceModelIncomeVo">
         select date tdate,appid,dn_bus_model_id,'' media
        ,round(ifnull(sum(case when income_type in (1,3) then income end),0),2) adv_income
        ,round(ifnull(sum(case when income_type = 2 then income end),0),2) pay_income
        ,round(ifnull(sum(case when income_type = 4 then income end),0),2) baidu_income
        from
        (select date(createtime) date,ifnull(appid,15265) as appid,sum(income) income,2 as income_type,
        case d.bus_category
        when 3 then 14
        when 2 then if(payid in (111,11),13,if(payid = 118,12,20))
        else ifnull(dn_bus_model_id,20)
        end dn_bus_model_id from
        (select createtime,appid,round((a.money*0.01*ifnull(b.share,100)/100),2) as income,chaid,payid from wb_pay_info a
        left join finance_account_config b on CONCAT(paytype,appid) = b.login_account
        where orderstatus = 'SUCCESS' and appid is not null and createtime between concat(#{start_date},' 00:00:00') and concat(#{end_date},' 23:59:59') and paytype not in ('oppo支付','小米支付','华为支付','vivo支付','荣耀支付')
        and (oaid != 'test' or oaid is null) and (param3 != 'test' or param3 is null)) a
        left join dn_channel_info c on a.chaid = c.cha_id
        left join app_info d on a.appid = d.id
        GROUP BY appid,date(createtime),dn_bus_model_id

        union all

        select tdate date,appid,if(d.app_category = 5,round(sum(income)-sum(kobe_coupon),2),round(sum(income),2)) income,2 as income_type
        ,case d.bus_category
        when 3 then 14
        when 2 then if(payid in (111,11),13,if(payid = 118,12,20))
        else ifnull(dn_bus_model_id,20)
        end dn_bus_model_id from
        (select tdate,appid,channel,payid,income,kobe_coupon from
        (select tdate,appid,channel
        ,CASE channel
        WHEN 'oppo' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'vivo' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'xiaomi' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'oppoml' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'oppomj' THEN ifnull(pay_revenue_share_fre*(1-0.05)*0.9,0)
        WHEN 'vivoml' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.05)*0.5
        WHEN 'xiaomimj' THEN ifnull(pay_revenue_share_fre*(1-0.05)*0.7,0)
        WHEN 'huawei' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'huaweiml' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'huawei2' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'huaweiml2' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'h5_huawei' THEN ifnull(pay_revenue_share_fre,0)*(1-0.02)*0.5
        WHEN 'honor' THEN ifnull(pay_revenue_share_after,0)+(ifnull(pay_revenue_share_fre,0)-ifnull(pay_buy_revenue_share_fre,0))*(1-0.02)*0.5
        ELSE ifnull(pay_revenue_share_fre,0)
        END income
        ,CASE channel
        WHEN 'oppo' THEN 104
        WHEN 'vivo' THEN 107
        WHEN 'xiaomi' THEN 106
        WHEN 'oppoml' THEN 104
        WHEN 'oppomj' THEN 104
        WHEN 'vivoml' THEN 107
        WHEN 'xiaomimj' THEN 106
        WHEN 'huawei' THEN 113
        WHEN 'huaweiml' THEN 113
        WHEN 'huawei2' THEN 113
        WHEN 'huaweiml2' THEN 113
        WHEN 'h5_huawei' THEN 113
        WHEN 'honor' THEN 121
        ELSE 20
        END payid,
        ifnull(kobe_coupon*0.9,0) kobe_coupon
        from channel_total_report
        where appid is not null and tdate between #{start_date} and #{end_date} and channel in ('oppo','vivo','xiaomi','oppoml','oppomj','vivoml','xiaomimj','huawei','honor')
        ) a
		where income > 0 ) a
		left join dn_channel_info c on a.channel = c.cha_id
        left join app_info d on a.appid = d.id
        GROUP BY appid,tdate,dn_bus_model_id

        union all

        select date(createtime) date,ifnull(appid,15265) as appid,sum(income) income,2 as income_type,if(d.bus_category = 3,14,ifnull(dn_bus_model_id,20)) dn_bus_model_id from
        (select createtime,appid,round((a.money*c.rate*ifnull(b.share,100)/100),2) as income,chaid from wb_pay_info_hw a
        left join finance_account_config b on CONCAT(paytype,appid) = b.login_account
        left join currency_exchange_rate c on date(createtime) = tdate
        where orderstatus = 'SUCCESS' and ifnull(oaid,'')!= 'test' and appid is not null and createtime between concat(#{start_date},' 00:00:00') and concat(#{end_date},' 23:59:59') and c.change_type = 'USD,CNY') a
        left join dn_channel_info c on a.chaid = c.cha_id
        left join app_info d on a.appid = d.id
        GROUP BY appid,date(createtime),dn_bus_model_id

        union all

        select date,appid,SUM(income) income,income_type,dn_bus_model_id from
        (select date,IFNULL(dnappid,15265) appid,if(a.agent = 'kuaishouh5',round((revenue*ifnull(b.share,100)/100),2),revenue) income
        ,1 as income_type,if(d.bus_category = 3,14,ifnull(dn_bus_model_id,20)) dn_bus_model_id
        from dn_cha_cash_total a
        left join dn_channel_info c on a.cha_id = c.cha_id
        left join app_info d on a.dnappid = d.id
        left join finance_account_config b on a.member_id = b.account_id and a.agent = b.agent
        where date between #{start_date} and #{end_date}
        and ((a.agent = 'GDT' and a.app_id !=0) or (a.agent != 'GDT'))
        and (a.agent != 'vivo' or dnappid is not null)
        and a.agent not in ('applovin','pangle','mobvista','ohayoo','Baidu')) a
        GROUP BY date,appid,dn_bus_model_id


        union all

        select date,IFNULL(dnappid,39597) appid,SUM(revenue) income,1 as income_type,if(d.bus_category = 3,14,ifnull(dn_bus_model_id,20)) dn_bus_model_id
        from dn_cha_cash_total a
        left join dn_channel_info c on a.cha_id = c.cha_id
        left join app_info d on a.dnappid = d.id
        where date between #{start_date} and #{end_date}
        and ((a.agent = 'GDT' and a.app_id !=0) or (a.agent != 'GDT'))
        and (a.agent != 'vivo' or dnappid is not null)
        and a.agent in ('applovin','pangle','mobvista')
        GROUP BY date,dnappid,dn_bus_model_id

        union all

        select date,IFNULL(dnappid,15265) appid,SUM(revenue) income,4 as income_type,if(d.bus_category = 3,14,ifnull(dn_bus_model_id,20)) dn_bus_model_id
        from dn_cha_cash_total a
        left join dn_channel_info c on a.cha_id = c.cha_id
        left join app_info d on a.dnappid = d.id
        where date between #{start_date} and #{end_date}
        and ((a.agent = 'GDT' and a.app_id !=0) or (a.agent != 'GDT'))
        and (a.agent != 'vivo' or dnappid is not null)
        and a.agent = 'Baidu'
        GROUP BY date,dnappid,dn_bus_model_id

        union all

        select date,IFNULL(dnappid,15265) appid,round((sum(revenue)*ifnull(e.share,100)/100),2) income,1 as income_type,if(d.bus_category = 3,14,ifnull(dn_bus_model_id,20)) dn_bus_model_id
        from dn_cha_cash_total a
        left join dn_channel_info c on a.cha_id = c.cha_id
        left join app_info d on a.dnappid = d.id
        left join finance_account_config e on a.member_id = e.login_account
        where date between #{start_date} and #{end_date}
        and ((a.agent = 'GDT' and a.app_id !=0) or (a.agent != 'GDT'))
        and (a.agent != 'vivo' or dnappid is not null)
        and a.agent = 'ohayoo'
        GROUP BY date,dnappid,dn_bus_model_id

        union all

        select tdate,appid,sum(income) income,income_type,if(d.bus_category = 3,14,ifnull(dn_bus_model_id,20)) dn_bus_model_id from
		(select tdate,ifnull(d.appid,15265) appid,round(ifnull(a.income,0)*ifnull(c.share,100)/100,2) income,3 as income_type
        from app_channel_appid a LEFT JOIN  wx_channel_manage d on a.appid_key = d.ttappid
        left join app_channel_collect b on a.channel = b.cname and instr(b.appids,ifnull(d.appid,15265)) > 0
        left join finance_account_config c on b.account = c.login_account and c.agent = b.channel
        where tdate between #{start_date} and #{end_date}) a
        left join dn_channel_info e on e.cha_id = 'wx'
        left join app_info d on a.appid = d.id
        GROUP BY appid,tdate,dn_bus_model_id) a
        where income > 0
        GROUP BY date,appid,dn_bus_model_id
    </select>

    <select id="selectReleaseMediaRate" resultType="com.alibaba.fastjson.JSONObject">
        select a.day,a.app,a.business,a.media,round(media_spend/spend,2) rate from
        (select day,app,business,media,sum(spend) media_spend from dn_report_spend_finance
        where day between #{start_date} and #{end_date} and business is not null and spend > 0  and app is not null GROUP BY day,app,business,media) a
        left JOIN
        (select day,app,business,sum(spend) spend from dn_report_spend_finance
        where day between #{start_date} and #{end_date} and business is not null and spend > 0  and app is not null GROUP BY day,app,business) b
        on a.day = b.day and a.app = b.app and a.business = b.business
    </select>

    <delete id="deleteFinanceModelIncome">
        delete from finance_model_income_detail where tdate between #{start_date} and #{end_date}
    </delete>

    <insert id="batchInsertFinanceModelIncome">
        insert into finance_model_income_detail(tdate,appid,dn_bus_model_id,media,adv_income,pay_income,baidu_income)
        values
        <foreach collection="list" separator="," item="it">
            (#{it.tdate},#{it.appid},#{it.dn_bus_model_id},#{it.media},#{it.adv_income},#{it.pay_income},#{it.baidu_income})
        </foreach>
    </insert>

    <select id="selectFinanceMonthIncomeSpend" resultType="com.wbgame.pojo.finance.MonthIncomeSpendVo">
        select year,month,dn_bus_model_name,media,category_name,app_name,appid
        ,if(platform = 1,'安卓',if(platform = 2,'ios',if(platform = 3,'google',if(platform = 4,'小游戏','')))) platform
        ,sum(rebate_consume) rebate_consume,sum(pay_consume) pay_consume
        ,sum(frame_pre_amount) frame_pre_amount,sum(activity_pre_amount) activity_pre_amount,sum(agent_operate_amount) agent_operate_amount
        ,sum(release_spend) release_spend,sum(share_spend) share_spend,sum(redpack_spend) redpack_spend
        ,(sum(release_spend) + sum(share_spend) + sum(redpack_spend)) total_spend
        ,sum(ad_income) ad_income,sum(pay_income) pay_income,sum(baidu_income) baidu_income,sum(inspire_income) inspire_income
        ,(sum(ad_income)+sum(pay_income)+sum(baidu_income)+sum(inspire_income)) total_income
        ,(sum(ad_income)+sum(pay_income)+sum(baidu_income)+sum(inspire_income))-(sum(release_spend) + sum(share_spend) + sum(redpack_spend)) profit
        ,(sum(ad_income)+sum(pay_income)+sum(baidu_income)+sum(inspire_income)-sum(release_spend)) pre_profit
        from finance_month_income_spend
        where tdate between #{start_date} and #{end_date}
        <if test="dn_bus_model_id != null and dn_bus_model_id != ''">
            and dn_bus_model_id in (${dn_bus_model_id})
        </if>
        <if test="media != null and media != ''">
            and media in (${media})
        </if>
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="category_id != null and category_id != ''">
            and category_id in (${category_id})
        </if>
        <if test="platform != null and platform != ''">
            and platform in (${platform})
        </if>
        <choose>
            <when test="group != null and group != ''">
                group by ${group}
            </when>
            <otherwise>
                group by year,month,platform,category_id,appid,dn_bus_model_id,media
            </otherwise>
        </choose>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by year desc,month desc
            </otherwise>
        </choose>
    </select>

    <select id="countFinanceMonthIncomeSpend" resultType="com.wbgame.pojo.finance.MonthIncomeSpendVo">
        select
        sum(rebate_consume) rebate_consume,sum(pay_consume) pay_consume
        ,sum(frame_pre_amount) frame_pre_amount,sum(activity_pre_amount) activity_pre_amount,sum(agent_operate_amount) agent_operate_amount
        ,sum(release_spend) release_spend,sum(share_spend) share_spend,sum(redpack_spend) redpack_spend
        ,(sum(release_spend) + sum(share_spend) + sum(redpack_spend)) total_spend
        ,sum(ad_income) ad_income,sum(pay_income) pay_income,sum(baidu_income) baidu_income,sum(inspire_income) inspire_income
        ,(sum(ad_income)+sum(pay_income)+sum(baidu_income)+sum(inspire_income)) total_income
        ,(sum(ad_income)+sum(pay_income)+sum(baidu_income)+sum(inspire_income))-(sum(release_spend) + sum(share_spend) + sum(redpack_spend)) profit
        ,(sum(ad_income)+sum(pay_income)+sum(baidu_income)+sum(inspire_income)-sum(release_spend)-sum(redpack_spend)) pre_profit
        from finance_month_income_spend
        where tdate between #{start_date} and #{end_date}
        <if test="dn_bus_model_id != null and dn_bus_model_id != ''">
            and dn_bus_model_id in (${dn_bus_model_id})
        </if>
        <if test="media != null and media != ''">
            and media in (${media})
        </if>
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="category_id != null and category_id != ''">
            and category_id in (${category_id})
        </if>
    </select>

    <delete id="deleteMonthIncomeSpend">
         delete from finance_month_income_spend where tdate = #{tdate}
    </delete>

    <select id="selectMonthIncome" resultType="com.alibaba.fastjson.JSONObject">
        select #{year} year,#{month} month,#{tdate} tdate,c.id category_id,c.name category_name,bus_model dn_bus_model_name,os_type platform
        ,dn_bus_model_id,a.media,appid,b.app_name,sum(adv_income) ad_income,sum(pay_income) pay_income,sum(baidu_income) baidu_income
        from finance_model_income_detail a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        left join dn_bus_model_config d on a.dn_bus_model_id = d.id
        where tdate BETWEEN #{startTime} and #{endTime} GROUP BY dn_bus_model_id,media,appid
    </select>

    <insert id="batchInsertMonthIncome">
        replace into finance_month_income_spend(year,month,tdate,platform,dn_bus_model_id,dn_bus_model_name,media,category_id,category_name,
        appid,app_name,ad_income,pay_income,baidu_income)
        values
        <foreach collection="list" separator="," item="it">
            (#{it.year},#{it.month},#{it.tdate},#{it.platform},#{it.dn_bus_model_id},#{it.dn_bus_model_name},#{it.media},#{it.category_id}
            ,#{it.category_name},#{it.appid},#{it.app_name},#{it.ad_income},#{it.pay_income},#{it.baidu_income})
        </foreach>
    </insert>

    <select id="selectMonthConsume" resultType="com.alibaba.fastjson.JSONObject">
        select year,month,tdate,media,appid,app_name,dn_bus_model_id,category_id,category_name,dn_bus_model_name,platform
        ,frame_pre_amount,(ifnull(xyx_amount,0)+ifnull(activity_pre_amount,0)) activity_pre_amount,pay_consume,share_spend,redpack_spend
        ,(rebate_consume-pay_consume-xyx_amount-activity_pre_amount-frame_pre_amount+agent_operate_amount) release_spend
        ,rebate_consume,agent_operate_amount
        from
        (select #{year} year,#{month} month,#{tdate} tdate,media,appid,app_name,business dn_bus_model_id
        ,c.id category_id,c.name category_name,bus_model dn_bus_model_name,os_type platform
        ,ifnull(sum(case when payType = '预计年框' then spend end),0) frame_pre_amount
        ,ifnull(sum(case when payType = '预计小游戏激励金' then spend end),0) xyx_amount
        ,ifnull(sum(case when payType = '预计活动返货' then spend end),0) activity_pre_amount
        ,ifnull(sum(case when payType = '赔付消耗' then spend end),0) pay_consume
        ,0 share_spend
        ,ifnull(sum(case when payType = '红包支出' then spend end),0) redpack_spend
        ,0 rebate_consume,0 agent_operate_amount
        from finance_other_cost a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        left join dn_bus_model_config d on a.business = d.id
        where year = #{year} and month = #{month}
        GROUP BY media,appid,business) a
    </select>

    <insert id="batchInsertMonthConsume">
        <foreach collection="list" separator=";" item="it">
        insert into finance_month_income_spend(year,month,tdate,platform,dn_bus_model_id,dn_bus_model_name,media,category_id,category_name,appid,app_name,
        frame_pre_amount,activity_pre_amount,pay_consume,share_spend,redpack_spend,rebate_consume,agent_operate_amount,release_spend)
        values
            (#{it.year},#{it.month},#{it.tdate},#{it.platform},#{it.dn_bus_model_id},#{it.dn_bus_model_name},#{it.media},#{it.category_id}
            ,#{it.category_name},#{it.appid},#{it.app_name},#{it.frame_pre_amount},#{it.activity_pre_amount},#{it.pay_consume},#{it.share_spend}
            ,#{it.redpack_spend},#{it.rebate_consume},#{it.agent_operate_amount},#{it.release_spend})
            ON DUPLICATE KEY UPDATE
            frame_pre_amount = VALUES(frame_pre_amount),
            activity_pre_amount = VALUES(activity_pre_amount),
            pay_consume = VALUES(pay_consume),
            share_spend = VALUES(share_spend),
            redpack_spend = VALUES(redpack_spend),
            rebate_consume = VALUES(rebate_consume),
            agent_operate_amount = VALUES(agent_operate_amount),
            release_spend = VALUES(release_spend)
        </foreach>
    </insert>

    <select id="selectInspireIncome" resultType="com.alibaba.fastjson.JSONObject">
        select #{year} year,#{month} month,#{tdate} tdate,c.id category_id,c.name category_name,bus_model dn_bus_model_name,os_type platform
        ,model_id dn_bus_model_id,a.media,a.app_id appid,b.app_name,spend inspire_income from dn_inncentive_income a
        left join app_info b on a.app_id = b.id
        left join app_category c on b.app_category = c.id
        left join dn_bus_model_config d on a.model_id = d.id
        where day = #{tdate}
    </select>

    <insert id="batchInsertInspireIncome">
        <foreach collection="list" separator=";" item="it">
            insert into finance_month_income_spend(year,month,tdate,platform,dn_bus_model_id,dn_bus_model_name,media,
            category_id,category_name,appid,app_name,inspire_income)
            values
            (#{it.year},#{it.month},#{it.tdate},#{it.platform},#{it.dn_bus_model_id},#{it.dn_bus_model_name},#{it.media}
            ,#{it.category_id},#{it.category_name},#{it.appid},#{it.app_name},#{it.inspire_income})
            ON DUPLICATE KEY UPDATE
            inspire_income = VALUES(inspire_income)
        </foreach>
    </insert>

    <select id="selectMonthReleaseMediaRate" resultType="com.alibaba.fastjson.JSONObject">
        select #{tdate} tdate,a.app,a.business,a.media,round(media_spend/spend,2) rate from
        (select app,business,media,sum(spend) media_spend from dn_report_spend_finance
        where day between #{start_date} and #{end_date} and business is not null and spend > 0 and app is not null GROUP BY app,business,media) a
        left JOIN
        (select app,business,sum(spend) spend from dn_report_spend_finance
        where day between #{start_date} and #{end_date} and business is not null and spend > 0 and app is not null GROUP BY app,business) b
        on a.app = b.app and a.business = b.business
    </select>

    <select id="selectFinanceBusinessSplitRate" resultType="com.alibaba.fastjson.JSONObject">
        select * from finance_business_split_rate
    </select>

    <insert id="batchInsertFinanceBusinessSplitRate">
        insert into finance_business_split_rate(appid,business,media,rate,tdate,update_time) values
        <foreach collection="list" item="it" separator=",">
            (#{it.app},#{it.business},#{it.media},#{it.rate},#{it.tdate},now())
        </foreach>
    </insert>

    <select id="selectMonthrebateSpend" resultType="com.alibaba.fastjson.JSONObject">
        select #{year} year,#{month} month,#{tdate} tdate,ifnull(app,15265) appid,media,business
        ,ifnull(sum(rebateSpend),0) rebate_consume,ifnull(sum(serviceSpend),0) agent_operate_amount
        ,app_name,a.business dn_bus_model_id,c.id category_id,c.`name` category_name,bus_model dn_bus_model_name,os_type platform
        from dn_report_spend_finance a
        left join app_info b on a.app = b.id
        left join app_category c on b.app_category = c.id
        left join dn_bus_model_config d on a.business = d.id
        where date_format(day, '%Y-%m') = #{tdate}
        GROUP BY date_format(day,'%Y-%m'),app,media,business
    </select>

    <insert id="batchInsertMonthrebateSpend">
        <foreach collection="list" separator=";" item="it">
            insert into finance_month_income_spend(year,month,tdate,platform,dn_bus_model_id,dn_bus_model_name,media,category_id,category_name,appid,app_name,
            rebate_consume,agent_operate_amount,release_spend)
            values
            (#{it.year},#{it.month},#{it.tdate},#{it.platform},#{it.dn_bus_model_id},#{it.dn_bus_model_name},#{it.media},#{it.category_id}
            ,#{it.category_name},#{it.appid},#{it.app_name},#{it.rebate_consume},#{it.agent_operate_amount},#{it.rebate_consume}+#{it.agent_operate_amount})
            ON DUPLICATE KEY UPDATE
            rebate_consume = VALUES(rebate_consume),
            agent_operate_amount = VALUES(agent_operate_amount),
            release_spend = release_spend+VALUES(rebate_consume)+VALUES(agent_operate_amount)
        </foreach>
    </insert>

    <select id="selectPreProfitRateListByAppid" resultType="java.util.Map">
        select a.year,a.month,a.appid,dn_bus_model_id,media,round(a.pre_profit/b.pre_profit*#{amount},2) share_spend
        from
        (select year,month,appid,dn_bus_model_id,media
        ,(sum(ad_income)+sum(pay_income)+sum(baidu_income)+sum(inspire_income)-sum(release_spend)) pre_profit
        from finance_month_income_spend where year = #{year} and month = #{month} and appid = #{appid}
        GROUP BY year,month,appid,dn_bus_model_id,media) a
        left join
        (select year,month,appid
        ,(sum(ad_income)+sum(pay_income)+sum(baidu_income)+sum(inspire_income)-sum(release_spend)) pre_profit
        from finance_month_income_spend where year = #{year} and month = #{month} and appid = #{appid}
        GROUP BY year,month,appid) b
        on a.year = b.year and a.month = b.month and a.appid = b.appid
    </select>

    <update id="updateShareSpends">
        <foreach collection="list" separator=";" item="it">
            update finance_month_income_spend set
            share_spend = #{it.share_spend}
            where year = #{it.year} and month = #{it.month} and appid = #{it.appid}
            and dn_bus_model_id= #{it.dn_bus_model_id} and media = #{it.media}
        </foreach>
    </update>

    <delete id="deleteFinanceBusinessSplitRate">
        delete from finance_business_split_rate where appid = #{app} and business = #{business}
    </delete>

</mapper>