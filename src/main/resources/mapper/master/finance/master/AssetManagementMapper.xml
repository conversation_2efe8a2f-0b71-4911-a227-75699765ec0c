<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.finance.master.AssetManagementMapper">

    <select id="selectAssertsTable" resultType="com.wbgame.pojo.finance.AssetsInfoTable">
		select count(*) as num,assetsstatus
		from assets_info GROUP BY assetsstatus
	</select>

    <select id="selectAssertsDepTable" resultType="com.wbgame.pojo.finance.AssetsInfoTable">
		select count(*) as num,userdepartment
		from assets_info GROUP BY userdepartment
	</select>

    <select id="selectAssertsDateTable" resultType="com.wbgame.pojo.finance.AssetsInfoTable">
		select count(*) as num,operatortype,
		DATE_FORMAT(createtime,"%Y-%m") as createtime
		from assets_info_log
		GROUP BY operatortype,DATE_FORMAT(createtime,"%Y-%m")
	</select>

    <select id="selectAssertsLog" parameterType="Map" resultType="com.wbgame.pojo.finance.AssetsInfoLog">
        select aid,assetsid,operator,operatortype,operatorcontent,date_format(createtime,"%Y-%m-%d %H:%i:%S") as
        createtime from assets_info_log
        where 1=1
        <if test="assetsid != null and assetsid != ''">
            and assetsid = #{assetsid}
        </if>
        <if test="starttime != null and starttime != ''">
            and date_format(createtime,"%Y-%m-%d") &gt;= #{starttime}
        </if>
        <if test="endtime != null and endtime != ''">
            and date_format(createtime,"%Y-%m-%d") &lt;= #{endtime}
        </if>
        <if test="operatorcontent != null and operatorcontent != ''">
            and operatorcontent like concat('%',#{operatorcontent},'%')
        </if>

        order by createtime desc
    </select>

    <select id="existAssertsByAssetsid" parameterType="java.lang.String" resultType="java.lang.Long">
		select count(1) from assets_info where assetsid = #{assetsid} limit 1
	</select>

    <select id="selectAsserts" parameterType="Map" resultType="com.wbgame.pojo.finance.AssetsInfo">
        select
        oldassetsid,assetsid,assetsname,assetsnote,assetsuser,assetsstatus,assetsusage,company,buytime,assetstype,
        assetssum,userdepartment,addtime,
        date_format(createtime,"%Y-%m-%d %H:%i:%S") as createtime,atype,amoney,aunit from assets_info where 1=1
        <if test="starttime != null and starttime != ''">
            and date_format(createtime,"%Y-%m-%d") &gt;= #{starttime}
        </if>
        <if test="endtime != null and endtime != ''">
            and date_format(createtime,"%Y-%m-%d") &lt;= #{endtime}
        </if>
        <if test="assetsname != null and assetsname != ''">
            and assetsname like  concat('%',#{assetsname},'%')
        </if>
        <if test="assetsstatus != null and assetsstatus != ''">
            and assetsstatus = #{assetsstatus}
        </if>
        <if test="assetsusage != null and assetsusage != ''">
            and assetsusage = #{assetsusage}
        </if>
        <if test="userdepartment != null and userdepartment != ''">
            and userdepartment = #{userdepartment}
        </if>
        <if test="assetsuser != null and assetsuser != ''">
            and assetsuser like concat('%',#{assetsuser},'%')
        </if>
        <if test="company != null and company != ''">
            and company = #{company}
        </if>
        <if test="atype != null and atype != ''">
            and atype = #{atype}
        </if>
        <if test="assetsid != null and assetsid != ''">
            and assetsid like  concat('%',#{assetsid},'%')
        </if>
        order by createtime desc
    </select>

    <insert id="addAsserts" parameterType="Map">
		insert into assets_info(oldassetsid,assetsid,assetsname,assetsnote,
		assetsstatus,company,buytime,assetstype,assetssum,atype,createtime,aunit,amoney)
		values(#{oldassetsid},#{assetsid},#{assetsname},#{assetsnote},
		#{assetsstatus},#{company},#{buytime},#{assetstype},#{assetssum},#{atype},now(),#{aunit},#{amoney})
	</insert>

    <insert id="insertAssertsLog" parameterType="com.wbgame.pojo.finance.AssetsInfoLog">
		insert into assets_info_log(assetsid,operator,operatortype,operatorcontent,createtime)
		values(#{assetsid},#{operator},#{operatortype},#{operatorcontent},now())
	</insert>

    <update id="updateAsserts" parameterType="Map">
		update assets_info set assetsname = #{assetsname},assetsnote=#{assetsnote},assetsstatus=#{assetsstatus},
		company = #{company},buytime=#{buytime},assetstype=#{assetstype},assetssum=#{assetssum},atype=#{atype},amoney=#{amoney},aunit=#{aunit}
		where oldassetsid = #{oldassetsid} and assetsid = #{assetsid}
	</update>

    <update id="handerAsserts" parameterType="Map">
		update assets_info set assetsuser=#{assetsuser},userdepartment=#{userdepartment},assetsstatus=#{assetsstatus},assetsnote=#{assetsnote}
		where oldassetsid = #{oldassetsid} and assetsid = #{assetsid}
	</update>

    <insert id="batchAddAsserts" parameterType="java.util.List">
        insert into assets_info
        (oldassetsid,assetsid,assetsname,assetsnote,assetsuser,assetsstatus,assetsusage,company,buytime,
        assetstype,assetssum,userdepartment,addtime,atype,createtime,aunit,amoney) values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.oldassetsid},#{item.assetsid},#{item.assetsname},#{item.assetsnote},#{item.assetsuser},
            #{item.assetsstatus},#{item.assetsusage},#{item.company},#{item.buytime},#{item.assetstype},#{item.assetssum},#{item.userdepartment},
            #{item.addtime},#{item.atype},now(),#{item.aunit},#{item.amoney})
        </foreach>
    </insert>

    <insert id="batchAssertsLog" parameterType="java.util.List">
        insert into assets_info_log(assetsid,operator,operatortype,operatorcontent,createtime)values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.assetsid},#{item.operator},#{item.operatortype},#{item.operatorcontent},now())
        </foreach>
    </insert>

    <select id="selectAssetMaintenance" resultType="com.wbgame.pojo.finance.AssetMaintenance">
        select
        sn, parts_name partsName, parts_type partsType, price, source, department, date, warranty, order_id orderId,
        purchaser,status
        from asset_maintenance
        where 1=1
        <if test="sn != null and sn != ''">
            and sn = #{sn,jdbcType=VARCHAR}
        </if>
    </select>

    <insert id="insertAssetMaintenance" parameterType="com.wbgame.pojo.finance.AssetMaintenance">
    insert into asset_maintenance (sn, parts_name, parts_type,
      price, source, department,
      date, warranty, order_id,
      purchaser, status)
    values (#{sn,jdbcType=VARCHAR}, #{partsName,jdbcType=VARCHAR}, #{partsType,jdbcType=VARCHAR},
      #{price,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, #{department,jdbcType=VARCHAR},
      #{date,jdbcType=VARCHAR}, #{warranty,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR},
      #{purchaser,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR})
  </insert>

    <update id="updateAssetMaintenance" parameterType="com.wbgame.pojo.finance.AssetMaintenance">
    update asset_maintenance
    set parts_name = #{partsName,jdbcType=VARCHAR},
      parts_type = #{partsType,jdbcType=VARCHAR},
      price = #{price,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR},
      department = #{department,jdbcType=VARCHAR},
      date = #{date,jdbcType=VARCHAR},
      warranty = #{warranty,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      purchaser = #{purchaser,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR}
    where sn = #{sn,jdbcType=VARCHAR}
  </update>

    <delete id="deleteAssetMaintenance" parameterType="com.wbgame.pojo.finance.AssetMaintenance">
    delete from asset_maintenance
    where sn = #{sn,jdbcType=VARCHAR}
  </delete>

    <delete id="deleteAssert">
        delete from assets_info where assetsid = #{id} limit 1
    </delete>

    <update id="batchupdateAsserts">


        <foreach collection="ids" separator=";" item="it">
            update assets_info set userdepartment = #{userdepartment}
            where assetsid = #{it}
        </foreach>
    </update>

</mapper>
