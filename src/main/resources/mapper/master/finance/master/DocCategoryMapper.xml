<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.finance.master.DocCategoryMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.finance.DocCategory">
    <!--@mbg.generated-->
    <!--@Table doc_category-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from doc_category
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from doc_category
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wbgame.pojo.finance.DocCategory">
    <!--@mbg.generated-->
    insert into doc_category (id, `name`)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wbgame.pojo.finance.DocCategory">
    <!--@mbg.generated-->
    insert into doc_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.finance.DocCategory">
    <!--@mbg.generated-->
    update doc_category
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.finance.DocCategory">
    <!--@mbg.generated-->
    update doc_category
    set `name` = #{name,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

<!--auto generated by MybatisCodeHelper on 2020-11-20-->
  <select id="selectAll" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.finance.DocCategory">
    select
    <include refid="Base_Column_List"/>
    from doc_category
    <where>1=1
      <if test="name != null and name != ''">
        and `name` = #{name,jdbcType=VARCHAR}
      </if>
      <if test="id != null">
        and id = #{id,jdbcType=BIGINT}
      </if>
    </where>
  </select>
  <select id="exist" resultType="java.lang.Long">
    select count(1) from doc_category
    <where>1=1
      <if test="name != null and name != ''">
        and `name` = #{name,jdbcType=VARCHAR}
      </if>
      <if test="id != null">
        and id != #{id,jdbcType=BIGINT}
      </if>
    </where>
  </select>
</mapper>