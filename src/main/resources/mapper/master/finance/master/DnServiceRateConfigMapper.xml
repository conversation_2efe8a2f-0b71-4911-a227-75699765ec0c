<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.finance.master.DnServiceRateConfigMapper" >
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.finance.DnServiceRateConfigVO" >
    <id column="media" property="media" jdbcType="VARCHAR" />
    <id column="second_agent" property="secondAgent" jdbcType="VARCHAR" />
    <id column="month" property="month" jdbcType="VARCHAR"/>
    <result column="rebate_rate" property="rebateRate" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
  </resultMap>
  <delete id="deleteDnServiceRateConfigById" parameterType="com.wbgame.pojo.finance.DnRebateRateConfigKey">

    <if test="list != null and list.size > 0">

      delete from dn_service_rate_config

      <where>

        <foreach collection="list" item="key">

          or (media = #{key.media,jdbcType=VARCHAR} and second_agent =#{key.secondAgent,jdbcType=VARCHAR}
                and month =#{key.month,jdbcType=VARCHAR})
        </foreach>

      </where>
    </if>

  </delete>

  <insert id="insertDnRebateRateConfig" parameterType="com.wbgame.pojo.finance.DnServiceRateConfig">
    insert into dn_service_rate_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="media != null and media != ''">
        media,
      </if>
      <if test="secondAgent != null and secondAgent != ''">
        second_agent,
      </if>
      <if test="rebateRate != null and rebateRate != ''">
        rebate_rate,
      </if>

      <if test="createUser != null and createUser != ''">
        create_user,
      </if>
      <if test="month != null and month != ''">
        month,
      </if>

      create_time,
      update_time
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="media != null and media != ''">
        #{media,jdbcType=VARCHAR},
      </if>
      <if test="secondAgent != null and secondAgent != ''">
        #{secondAgent,jdbcType=VARCHAR},
      </if>
      <if test="rebateRate != null">
        #{rebateRate,jdbcType=VARCHAR},
      </if>

      <if test="createUser != null and createUser != ''">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="month != null and month != ''">
        #{month,jdbcType=VARCHAR},
      </if>
      current_timestamp,
      current_timestamp
    </trim>
  </insert>

  <update id="updateDnDnServiceRateConfigBatch">
    update dn_service_rate_config
    <set>
      <if test="rebateRate != null">
        rebate_rate = #{rebateRate,jdbcType=VARCHAR},
      </if>

      <if test="updateUser != null and updateUser != ''">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      update_time = current_timestamp
    </set>
    where media = #{media,jdbcType=VARCHAR} and second_agent = #{secondAgent,jdbcType=VARCHAR}
    and month = #{month}

  </update>
  <update id="updateDnDnServiceRateConfig" parameterType="com.wbgame.pojo.finance.DnServiceRateConfig">
    update dn_service_rate_config
    set rebate_rate = #{rebateRate,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_user = #{createUser,jdbcType=VARCHAR},
        update_user = #{updateUser,jdbcType=VARCHAR}
    where media = #{media,jdbcType=VARCHAR}
      and second_agent = #{secondAgent,jdbcType=VARCHAR}
      and month =#{month,jdbcType=VARCHAR}
  </update>

  <sql id="resColumn">
    media
    , second_agent, concat(rebate_rate, "%") rebate_rate, date_format(create_time, '%Y-%m-%d %H:%i:%s') create_time,
        date_format(update_time, '%Y-%m-%d %H:%i:%s') update_time, create_user, update_user, month
  </sql>

  <select id="selectDnServiceRateByCondition" resultMap="BaseResultMap">

    select
    <include refid="resColumn"/>
    from dn_service_rate_config
    <where>

      <if test="media != null and media.size > 0">
        and media in
        <foreach collection="media" item="m" open="(" separator="," close=")">

          #{m}
        </foreach>

      </if>
      <if test="secondAgent != null and secondAgent.size > 0">
        and second_agent in
        <foreach collection="secondAgent" item="p" open="(" separator="," close=")">

          #{p}
        </foreach>
      </if>

      <if test="start_date != null and start_date != ''and start_date != null and start_date != ''">
        and month between #{start_date} and #{end_date}
      </if>
    </where>
    order by create_time desc
  </select>
</mapper>