<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.finance.master.FinanceAdMapper">

    <insert id="batchExecSql" parameterType="java.util.Map">
        ${sql1}
        <foreach collection="list" item="li" separator=",">
            ${sql2}
        </foreach>
        ${sql3}
    </insert>


    <select id="selectAlipayComplaintSum" parameterType="java.util.Map" resultType="java.util.Map" >
            select
                sum(xx.order_count) order_count,sum(xx.complaint_count) complaint_count,
                CONCAT(IFNULL(truncate(sum(xx.complaint_count)/sum(xx.order_count)*100,2),0),'%') as complaint_rate,
                IFNULL(truncate(sum(IF((xx.conform_count &lt; 0),0,xx.conform_count)),0),0) as conform_count
            from (<include refid="dn_AlipayComplaintList"/>) xx
    </select>
    <select id="selectAlipayComplaintList" parameterType="java.util.Map" resultType="java.util.Map" >
        <include refid="dn_AlipayComplaintList"/>
    </select>

    <sql id="dn_AlipayComplaintList">
        select tdate,alipay_account_id,sum(order_count) order_count,sum(complaint_count) complaint_count,
            IFNULL(truncate(sum(complaint_count)/sum(order_count)*100,2),0) as complaint_rate,
            IFNULL(truncate(sum(complaint_count)/0.005-sum(order_count),0),0) as conform_count
        from alipay_complaint_info
        where tdate BETWEEN #{sdate} and #{edate}
        <if test="alipay_account_id != null and alipay_account_id != ''">
            and alipay_account_id in (${alipay_account_id})
        </if>

        <choose>
            <when test="group != null and group != ''">
                group by ${group}
            </when>
            <otherwise>

            </otherwise>
        </choose>
        <if test="order_str != null and order_str != ''">
            order by ${order_str}
        </if>
    </sql>


    <insert id="insertAlipayComplaintInfo" parameterType="java.util.Map">
        REPLACE INTO alipay_complaint_info (
            tdate,
            alipay_account_id,
            complaint_count,
            createtime
        )
        VALUES
        (
             #{tdate},
             #{alipay_account_id},
             #{complaint_count},
             now()
        )
    </insert>
    <update id="updateAlipayComplaintInfoAsOrderCount" parameterType="java.util.Map">
        <foreach collection="list" item="li" separator=";">
            UPDATE alipay_complaint_info
            SET
                order_count = #{li.order_count},endtime=now()
            WHERE tdate = #{li.tdate} AND alipay_account_id = #{li.alipay_account_id}
        </foreach>
    </update>

</mapper>