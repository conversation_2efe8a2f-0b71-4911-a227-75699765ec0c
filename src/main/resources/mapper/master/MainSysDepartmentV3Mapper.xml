<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.MainSysDepartmentV3Mapper" >
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.finance.MainSysDepartmentV3VO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="department_name" property="departmentName" jdbcType="VARCHAR" />
    <result column="parent_id" property="parentId" jdbcType="INTEGER" />
    <result column="create_user" property="createUser" jdbcType="INTEGER" />
    <result column="update_user" property="updateUser" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="INTEGER" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, department_name, parent_id
  </sql>

  <select id="selectDepartment" resultType="com.wbgame.pojo.finance.MainSysDepartmentV3" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from main_sys_department_v3
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteDepartmentById" parameterType="java.lang.Integer" >
    delete from main_sys_department_v3
    where id in

    <foreach collection="list" item="id" open="(" separator="," close=")">

          #{id,jdbcType=INTEGER}
    </foreach>

  </delete>

  <insert id="insertDepartment" parameterType="com.wbgame.pojo.finance.MainSysDepartmentV3" >
    insert into main_sys_department_v3
    <trim prefix="(" suffix=")" suffixOverrides="," >

      <if test="departmentName != null" >
        department_name,
      </if>
      <if test="parentId != null" >
        parent_id,
      </if>
      <if test="createUser != null" >
        create_user,
      </if>
      create_time,
      update_time
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >

      <if test="departmentName != null" >
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="createUser != null" >
        #{createUser},
      </if>
      CURRENT_TIMESTAMP,
      CURRENT_TIMESTAMP
    </trim>
  </insert>


  <update id="updateDepartmentById" parameterType="com.wbgame.pojo.finance.MainSysDepartmentV3" >
    update main_sys_department_v3
    <set >
      <if test="departmentName != null and departmentName != ''" >
        department_name = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null and updateUser != ''" >
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      parent_id = #{parentId,jdbcType=INTEGER},
      update_time = CURRENT_TIMESTAMP
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.finance.MainSysDepartmentV3" >
    update main_sys_department_v3
    set department_name = #{departmentName,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectDepartmentByCondition" parameterType="com.wbgame.pojo.finance.MainSysDepartmentV3DTO"
          resultMap="BaseResultMap">

          SELECT
          c.id,
          c.department_name,
          c.parent_id,
          DATE_FORMAT( c.create_time, '%Y-%m-%d %H:%i:%s' ) create_time,
          c.create_user,
          DATE_FORMAT( c.update_time, '%Y-%m-%d %H:%i:%s' ) update_time,
          c.update_user,
          p.department_name parentName,
          qbi.type type
          FROM
          main_sys_department_v3 c
          LEFT JOIN main_sys_department_v3 p ON c.parent_id = p.id
          LEFT JOIN yyhz_0308.qbi_permission_config qbi ON c.id = qbi.department_id
          <if test="departmentName != null and departmentName !=''">
                where c.department_name like "%" #{departmentName} "%"
          </if>
        order by id desc
  </select>

  <update id="updateDepartmentPidIsNullByPid" parameterType="java.lang.Integer" >
        update main_sys_department_v3 set parent_id = null where parent_id in
        <foreach collection="list" item="pId" open="(" separator="," close=")">

            #{pId}
        </foreach>
  </update>
</mapper>