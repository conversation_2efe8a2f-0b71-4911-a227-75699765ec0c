<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--
        mapper为映射的根节点，用来管理DAO接口
        namespace指定DAO接口的完整类名，表示mapper配置文件管理哪个DAO接口(包.接口名)
        mybatis会依据这个接口动态创建一个实现类去实现这个接口，而这个实现类是一个Mapper对象
    -->
<mapper namespace="com.wbgame.mapper.master.ClientRefundMapper">
    <!--
        id = "接口中的方法名"
        parameterType = "接口中传入方法的参数类型"
        resultType = "返回实体类对象：包.类名"  处理结果集 自动封装
        注意:sql语句后不要出现";"号
        查询：select标签
        增加：insert标签
        修改：update标签
        删除：delete标签
      -->
    <insert id="insertClientRefund" parameterType="com.wbgame.pojo.clientRefund.ClientRefundVo">
        insert into wb_pay_refund_total (tdate, appid, chaid, paytype, pay_revenue, refund_revenue, paynum, cuser, createtime)
        VALUES (#{refund.tdate}, #{refund.appid}, #{refund.chaid}, #{refund.paytype}, #{refund.pay_revenue}, #{refund.refund_revenue}, #{refund.paynum}, #{refund.cuser}, #{refund.createtime})
    </insert>

    <select id="selectClientRefund" resultType="com.wbgame.pojo.clientRefund.ClientRefundVo">
        select id, tdate, appid, chaid, paytype, pay_revenue, refund_revenue, paynum,
               cuser, date_format(createtime, '%Y-%m-%d %H:%i:%s') as createtime,
               euser, date_format(endtime, '%Y-%m-%d %H:%i:%s') as endtime
        from wb_pay_refund_total
        <where>
            <if test="start_date != null">
                tdate <![CDATA[>=]]> #{start_date}
            </if>
            <if test="end_date != null">
                and tdate <![CDATA[<=]]> #{end_date}
            </if>
            <if test="appid != null">
                and appid in
                <foreach collection="appid" item="aid" index="index" open="(" close=")" separator=",">
                    #{aid}
                </foreach>
            </if>
            <if test="chaid != null">
                and chaid in
                <foreach collection="chaid" item="cid" index="index" open="(" close=")" separator=",">
                    #{cid}
                </foreach>
            </if>
            <if test="paytype != null">
                and paytype in
                <foreach collection="paytype" item="ptype" index="index" open="(" close=")" separator=",">
                    #{ptype}
                </foreach>
            </if>
        </where>
        order by
        <if test="order_str != null and order_str != ''">${order_str},</if>
        tdate
    </select>

    <select id="selectClientRefundTotal" resultType="hashmap">
        select sum(refund_revenue) as refund_revenue, sum(pay_revenue) as pay_revenue, sum(paynum) as paynum from wb_pay_refund_total
        <where>
            <if test="start_date != null">
                tdate <![CDATA[>=]]> #{start_date}
            </if>
            <if test="end_date != null">
                and tdate <![CDATA[<=]]> #{end_date}
            </if>
            <if test="appid != null">
                and appid in
                <foreach collection="appid" item="aid" index="index" open="(" close=")" separator=",">
                    #{aid}
                </foreach>
            </if>
            <if test="chaid != null">
                and chaid in
                <foreach collection="chaid" item="cid" index="index" open="(" close=")" separator=",">
                    #{cid}
                </foreach>
            </if>
            <if test="paytype != null">
                and paytype in
                <foreach collection="paytype" item="ptype" index="index" open="(" close=")" separator=",">
                    #{ptype}
                </foreach>
            </if>
        </where>
    </select>

    <delete id="deleteClientRefund" parameterType="java.lang.Integer">
        delete from wb_pay_refund_total
        where id = #{id}
    </delete>

    <update id="updateClientRefund" parameterType="com.wbgame.pojo.clientRefund.ClientRefundVo">
        update wb_pay_refund_total
        <set>
            euser = #{refund.euser}, endtime = #{refund.endtime},
            <if test="refund.paynum != null and refund.paynum != ''">
                paynum = #{refund.paynum},
            </if>
            <if test="refund.refund_revenue != null and refund.refund_revenue != ''">
                refund_revenue = #{refund.refund_revenue},
            </if>
            <if test="refund.pay_revenue != null and refund.pay_revenue != ''">
                pay_revenue = #{refund.pay_revenue},
            </if>
        </set>
        where id = #{refund.id}
    </update>
</mapper>