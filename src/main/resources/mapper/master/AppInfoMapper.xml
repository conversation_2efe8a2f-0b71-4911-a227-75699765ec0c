<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.AppInfoMapper">


    <select id="selectAppNameByIdList" parameterType="java.lang.Integer" resultType="com.wbgame.pojo.AppInfoVo">

        SELECT
        id,
        app_name
        FROM
        app_info
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">

            #{id}
        </foreach>
    </select>


    <select id="selectTypeNameByChaIdList" parameterType="java.lang.Integer" resultType="java.util.Map">

        SELECT
            ci.cha_id,
            ct.type_name,
            ct.type_id
        FROM
            dn_channel_info ci
                LEFT JOIN dn_channel_type ct ON ci.cha_type = ct.type_id
        <if test="idList !=null and idList.size > 0">
            where ci.cha_type in
            <foreach collection="idList" item="cha_type" open="(" separator="," close=")">

                #{cha_type}
            </foreach>
        </if>

    </select>

    <select id="getAppSignArr" resultType="com.wbgame.pojo.test.AppSignInfo">
        select id,app_name appName,app_id appId from app_info
        where 1=1
        <if test="app_category != null and app_category != ''">
            and app_category in (${app_category})
        </if>
        <if test="bus_category != null and bus_category != ''">
            and bus_category = #{bus_category}
        </if>
    </select>
</mapper>