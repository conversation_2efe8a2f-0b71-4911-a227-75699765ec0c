<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.DnBusAppLineMapper" >
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.mobile.DnBusAppLineVO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="bus_line" property="busLine" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
  </resultMap>
  
  <sql id="Base_Column_List" >
      id, bus_line, if(status = 1, "开启", "关闭") status, description, date_format(create_time, '%Y-%m-%d %H:%i:%s') create_time,
      date_format(update_time, '%Y-%m-%d %H:%i:%s') update_time, create_user, update_user
  </sql>
  <select id="selectDnBusAppLineByCondition" resultMap="BaseResultMap"
          parameterType="com.wbgame.pojo.mobile.DnBusAppLineDTO">
    select
    <include refid="Base_Column_List"/>
    from dn_bus_app_line
    <where>

      <if test="busLine != null and busLine != ''">
        and bus_line like "%" #{busLine} "%"
      </if>

      <if test="status != null">
        and status = #{status}
      </if>
    </where>
    order by id desc
  </select>



  <delete id="deleteDnBusAppLineById" parameterType="java.lang.Integer">
    delete from dn_bus_app_line
    where id in
    <foreach collection="list" item="id" open="(" separator="," close=")">

      #{id,jdbcType=INTEGER}
    </foreach>

  </delete>

  <insert id="insertDnBusAppLine" parameterType="com.wbgame.pojo.mobile.DnBusAppLine"
          useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into dn_bus_app_line
    <trim prefix="(" suffix=")" suffixOverrides=",">

      <if test="busLine != null and busLine != ''">
        bus_line,
      </if>
      <if test="status != null">
        status,
      </if>

      <if test="description != null and description != ''">
        description,
      </if>

      <if test="createUser != null and createUser != ''">
        create_user,
      </if>

      create_time,
      update_time
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">

      <if test="busLine != null and busLine != ''">
        #{busLine,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>

      <if test="description != null and description != ''">
        #{description,jdbcType=VARCHAR},
      </if>

      <if test="createUser != null and createUser != ''">
        #{createUser,jdbcType=VARCHAR},
      </if>

      CURRENT_TIMESTAMP,
      CURRENT_TIMESTAMP
    </trim>
  </insert>

  <update id="updateDnBusAppLineById" parameterType="com.wbgame.pojo.mobile.DnBusAppLine">
    update dn_bus_app_line
    <set>
      <if test="busLine != null and busLine != ''">
        bus_line = #{busLine,jdbcType=VARCHAR},
      </if>

      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>

      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>

      <if test="updateUser != null and updateUser != ''">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      update_time = CURRENT_TIMESTAMP
    </set>
    where id = #{id}

  </update>

  <update id="updateById" parameterType="com.wbgame.pojo.mobile.DnBusAppLine">
    update dn_bus_app_line
    set bus_line   = #{busLine,jdbcType=VARCHAR},
        status       = #{prjid,jdbcType=TINYINT},
        description = #{description,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_user = #{createUser,jdbcType=VARCHAR},
        update_user = #{updateUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="export" resultMap="BaseResultMap"
          parameterType="com.wbgame.pojo.mobile.DnBusAppLineDTO">
    select
    <include refid="Base_Column_List"/>
    from dn_bus_app_line
    <where>

      <if test="busLine != null and busLine != ''">
        and bus_line like #{busLine,jdbcType=VARCHAR} "%"
      </if>

      <if test="status != null">
        and status = #{status}
      </if>
    </where>
  </select>
</mapper>