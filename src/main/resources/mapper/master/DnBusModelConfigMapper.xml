<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.DnBusModelConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.DnBusModelConfigVO">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="bus_model" property="busModel" jdbcType="VARCHAR" />
        <result column="bus_port" property="busPort" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="description" property="description" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, bus_model, bus_port, status, description, date_format(create_time, '%Y-%m-%d %H:%i:%s') create_time,
        date_format(update_time, '%Y-%m-%d %H:%i:%s') update_time, create_user, update_user
    </sql>

    <select id="selectDnBusModelConfigByCondition" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.DnBusModelConfigDTO">
        select
        <include refid="Base_Column_List"/>
        from dn_bus_model_config
        <where>

            <if test="busModel != null and busModel != ''">
                and bus_model like CONCAT('%',#{busModel},'%')
            </if>
            <if test="busPort != null and busPort != ''">
                and bus_port like CONCAT('%',#{busPort},'%')
            </if>

            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>

    <select id="export" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.DnBusModelConfigDTO">
        select
        id, bus_model, bus_port, if(status = 1, "开启", "关闭") status, description, date_format(create_time, '%Y-%m-%d %H:%i:%s') create_time,
        date_format(update_time, '%Y-%m-%d %H:%i:%s') update_time, create_user, update_user
        from dn_bus_model_config
        <where>

            <if test="busModel != null and busModel != ''">
                and bus_model like CONCAT('%',#{busModel},'%')
            </if>
            <if test="busPort != null and busPort != ''">
                and bus_port like CONCAT('%',#{busPort},'%')
            </if>

            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>

    <delete id="deleteDnBusModelConfigById" parameterType="java.lang.Integer">
        delete from dn_bus_model_config
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">

            #{id,jdbcType=INTEGER}
        </foreach>

    </delete>

    <insert id="insertDnBusModelConfig" parameterType="com.wbgame.pojo.DnBusModelConfig"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into dn_bus_model_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="busModel != null and busModel != ''">
                bus_model,
            </if>
            <if test="busPort != null and busPort != ''">
                bus_port,
            </if>
            <if test="status != null">
                status,
            </if>

            <if test="description != null and description != ''">
                description,
            </if>

            <if test="createUser != null and createUser != ''">
                create_user,
            </if>

            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="busModel != null and busModel != ''">
                #{busModel,jdbcType=VARCHAR},
            </if>
            <if test="busPort != null and busPort != ''">
                #{busPort,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>

            <if test="description != null and description != ''">
                #{description,jdbcType=VARCHAR},
            </if>

            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>

            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        </trim>
    </insert>

    <update id="updateDnBusModelConfigById" parameterType="com.wbgame.pojo.DnBusModelConfig">
        update dn_bus_model_config
        <set>
            <if test="busModel != null and busModel != ''">
                bus_model = #{busModel,jdbcType=VARCHAR},
            </if>

            <if test="busPort != null and busPort != ''">
                bus_port = #{busPort,jdbcType=VARCHAR},
            </if>

            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>

            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>

            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            update_time = CURRENT_TIMESTAMP
        </set>
        where id = #{id}
    
    </update>

</mapper>