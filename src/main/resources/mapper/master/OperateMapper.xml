<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.OperateMapper">

    <select id="selectOverTrend" resultType="com.alibaba.fastjson.JSONObject">
        select tdate,sum(addnum) addnum,sum(actnum) actnum,sum(ad_revenue) ad_revenue,sum(rebate_consume) rebate_consume,
        SUM(bd_revenue) bd_revenue,SUM(pay_revenue) pay_revenue,(sum(ad_revenue)+SUM(bd_revenue)+sum(pay_revenue)) all_revenue,
        if(SUM(rebate_consume) != 0,
        IFNULL(TRUNCATE(((SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) + sum(ad_money) - SUM(rebate_consume))/SUM(rebate_consume))*100, 1),0),100) roi,
        IFNULL(TRUNCATE(SUM(rebate_consume) / SUM(addnum), 2),0) cpa,
        TRUNCATE((SUM(ad_revenue)+SUM(bd_revenue)+SUM(pay_revenue)) / SUM(actnum), 2) total_arpu,ad_money
        from
        (select tdate,sum(addnum) addnum,sum(actnum) actnum,sum(ad_revenue) ad_revenue,sum(rebate_consume) rebate_consume,
        SUM(bd_revenue) bd_revenue,SUM(pay_revenue) pay_revenue,0 ad_money
        from dn_app_revenue_total a left join app_info b on a.appid = b.id
        where tdate between #{startTime} and #{endTime} and b.app_category != 17
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        GROUP BY tdate
        union all
        select tdate,SUM(add_num) add_num,SUM(dau) act_num,sum(ad_income) ad_revenue,sum(rebate_cost) rebate_consume,0 bd_revenue,
        TRUNCATE(SUM(mds_pay_income)*0.6+SUM(wx_pay_income)+SUM(tt_pay_income)*0.6,2) pay_revenue,ad_revenue ad_money from dn_micgame_revenue_total
        where tdate between #{startTime} and #{endTime}
        <if test="appid != null and appid != ''">
           and appid in (${appid})
        </if>
        GROUP BY tdate) a
        GROUP BY tdate order by tdate asc
    </select>

    <select id="selectNewProcessConfig" resultType="com.wbgame.pojo.operate.NewProcessConfigVo">
        select appid,json,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time,
        DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') update_time,create_owner,update_owner
        from new_process_config
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <choose>
            <when test="order != null and order != ''">
                order by ${order}
            </when>
            <otherwise>
                order by update_time desc
            </otherwise>
        </choose>
    </select>

    <insert id="insertNewProcessConfig">
        insert into new_process_config(appid,json,create_time,update_time,create_owner,update_owner)
        values
        (#{appid},#{json},now(),now(),#{create_owner},#{update_owner})
    </insert>

    <update id="updateNewProcessConfig">
        update new_process_config set
        json = #{json},
        update_owner = #{update_owner},
        update_time = now()
        where appid = #{appid}
    </update>

    <delete id="deleteNewProcessConfig">
        delete from new_process_config where appid = #{appid}
    </delete>

    <select id="selectShareContentConfig" resultType="com.wbgame.pojo.operate.ShareContentConfigVo">
        select id,remark,title,image_url,image_number,date_format(create_time,'%Y-%m-%d %H:%i:%s') create_time,
        date_format(update_time,'%Y-%m-%d %H:%i:%s') update_time,create_owner,update_owner
        from wx_share_content_config
        where 1=1
        <if test="remark != null and remark != ''">
            and remark like concat("%",#{remark},"%")
        </if>
        <if test="id != null and id != ''">
            and id = #{id}
        </if>
        <choose>
            <when test="order != null and order != ''">
                order by ${order}
            </when>
            <otherwise>
                order by update_time desc
            </otherwise>
        </choose>
    </select>

    <insert id="addShareContentConfig">
        insert into wx_share_content_config(remark,title,image_url,image_number,create_time,update_time,create_owner,update_owner)
        values
        (#{remark},#{title},#{image_url},#{image_number},now(),now(),#{create_owner},#{update_owner})
    </insert>

    <update id="updateShareContentConfig">
        update wx_share_content_config set remark = #{remark},title = #{title},image_url = #{image_url},image_number = #{image_number},
        update_time = now(),update_owner = #{update_owner}
        where id = #{id}
    </update>

    <delete id="deleteShareContentConfig">
        delete from wx_share_content_config where id = #{id} limit 1
    </delete>

    <select id="selectShareSwitchConfig" resultType="com.wbgame.pojo.operate.ShareSwitchConfigVo">
        select a.id,appid,app_name,channel,param1,param2,limit_num,placement,contents,status,
        date_format(a.create_time,'%Y-%m-%d %H:%i:%s') create_time,
        date_format(a.update_time,'%Y-%m-%d %H:%i:%s') update_time,
        create_owner,update_owner
        from wx_share_switch_config a left join app_info b on a.appid = b.id
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <choose>
            <when test="order != null and order != ''">
                order by ${order}
            </when>
            <otherwise>
                order by update_time desc
            </otherwise>
        </choose>
    </select>

    <insert id="addShareSwitchConfig">
        insert into wx_share_switch_config(appid,channel,param1,contents,status,create_time,update_time,create_owner,update_owner,param2,placement,limit_num)
        values
        (#{appid},#{channel},#{param1},#{contents},#{status},now(),now(),#{create_owner},#{update_owner},#{param2},#{placement},#{limit_num})
    </insert>

    <update id="updateShareSwitchConfig">
        update wx_share_switch_config set
        appid = #{appid},
        channel = #{channel},
        param1 = #{param1},
        param2 = #{param2},
        limit_num = #{limit_num},
        placement = #{placement},
        contents = #{contents},
        status = #{status},
        update_time = now(),
        update_owner = #{update_owner}
        where id = #{id}
    </update>

    <delete id="deleteShareSwitchConfig">
        delete from wx_share_switch_config where id = #{id} limit 1
    </delete>

    <select id="getShareContents" resultType="java.util.Map">
        select id,remark from wx_share_content_config
    </select>

    <select id="selectAdjustConfig" resultType="com.wbgame.pojo.operate.AdjustConfigVo">
        select appid,app_name,event,event_token,appid_token,create_owner,update_owner,
        DATE_FORMAT(a.create_time,'%Y-%m-%d %H:%i:%s') create_time,DATE_FORMAT(a.update_time,'%Y-%m-%d %H:%i:%s') update_time
        from adjust_point_config a left join app_info b on a.appid = b.id
        where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <choose>
            <when test="order != null and order != ''">
                order by ${order}
            </when>
            <otherwise>
                order by create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="existAdjustConfig" resultType="java.lang.Integer">
          select count(1) from adjust_point_config where appid = #{appid} and event = #{event}
    </select>

    <insert id="addAdjustConfig">
        insert into adjust_point_config(appid,event,appid_token,event_token,create_time,update_time,create_owner,update_owner)
        values
        (#{appid},#{event},#{appid_token},#{event_token},now(),now(),#{create_owner},#{update_owner})
    </insert>

    <update id="updateAdjustConfig">
        update adjust_point_config set
        appid = #{appid},
        event = #{event},
        appid_token = #{appid_token},
        event_token = #{event_token},
        update_owner = #{update_owner},
        update_time = now()
        where event = #{event} and appid = #{appid}
    </update>

    <delete id="deleteAdjustConfig">
        delete from adjust_point_config where event = #{event} and appid = #{appid} limit 1
    </delete>

    <select id="selectAdjustEventTypes" resultType="java.util.Map">
        select event_id id,event_name name from adjust_point_type
    </select>
</mapper>