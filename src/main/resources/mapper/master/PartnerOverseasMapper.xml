<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.PartnerOverseasMapper">

    <insert id="batchExecSql" parameterType="java.util.Map">
        ${sql1}
        <foreach collection="list" item="li" separator=",">
            ${sql2}
        </foreach>
        ${sql3}
    </insert>
    <update id="batchExecSqlTwo" parameterType="java.util.Map">
        <foreach collection="list" item="li" separator=";">
            ${sql1}
        </foreach>
    </update>




    <!-- 同步拉取 投放、变现、新增活跃 -->
    <insert id="insertPartnerInvestNewOfList" parameterType="java.util.List">
        INSERT INTO partner_invest_info_overseas(tdate,appid,country_code,cha_media,invest_amount,ischeck)

        VALUES
        <foreach collection="list" item="li" separator=",">
            (#{li.tdate},
            #{li.appid},
            #{li.country_code},
            #{li.cha_media},
            #{li.invest_amount},
            '0')
        </foreach>

    </insert>
    <insert id="insertPartnerRevenueNewOfList" parameterType="java.util.List">
        INSERT INTO partner_revenue_info_overseas(tdate,appid,country_code,agent,revenue,pv,ischeck)

        VALUES
        <foreach collection="list" item="li" separator=",">
            (#{li.tdate},
            #{li.appid},
            #{li.country_code},
            #{li.agent},
            #{li.revenue},
            #{li.pv},
            '0')
        </foreach>

    </insert>


    <insert id="insertPartnerAppRevenueNewBillingOfList" parameterType="java.util.List">
        REPLACE INTO partner_app_revenue_total_overseas(tdate,appid,actnum,addnum,pay_revenue,refund_revenue,paynum,ischeck)

        VALUES
        <foreach collection="list" item="li" separator=",">
            (#{li.tdate},
            #{li.appid},
            #{li.actnum},
            #{li.addnum},
            #{li.pay_revenue},
            #{li.refund_revenue},
            #{li.paynum},
            '0')
        </foreach>

    </insert>


</mapper>