<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.BonusMapper">

	<insert id="insertWxBonusUserInfoList" parameterType="java.util.List">
		insert into wx_bonus_user_info (
			wbappid,
			openid,
			unionid,
			max_score,
			hold_money,
			hold_msg,
			create_time,
			last_time
		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.wbappid},
			#{li.openid},
			#{li.unionid},
			#{li.max_score},
			#{li.hold_money},
			#{li.hold_msg},
			now(),
			now())
		</foreach>	
		ON DUPLICATE KEY UPDATE 
		max_score=VALUES(max_score),
		hold_money=VALUES(hold_money),
		hold_msg=VALUES(hold_msg),
		last_time=VALUES(last_time)
	</insert>
	
	<insert id="insertWxMoneyPutcashList" parameterType="java.util.List">
		insert into wx_money_putcash (
			wbappid,
			openid,
			<!-- unionid, -->
			amount,
			type,
			create_time
		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.wbappid},
			#{li.openid},
			<!-- #{li.unionid}, -->
			#{li.amount},
			#{li.type},
			now())
		</foreach>	
	</insert>
	<select id="selectWxBonusUserInfo" parameterType="java.lang.String"
		resultType="com.wbgame.pojo.BonusInfoVo">
		
		select * from wx_bonus_user_info
		where wbappid = #{wbappid} and openid = #{openid} limit 1
	</select>

</mapper>