<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.PartnerMapper">

	
	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>
	
	
	<!-- 合作方投放明细查询-新 -->
	<select id="selectPartnerInvestInfoNew" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="partner_invest_new_sql"/>
	</select>
	<select id="selectPartnerInvestInfoNewSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			sum(xx.investAmount) investAmount 
		from (<include refid="partner_invest_new_sql"/>) xx
	</select>
	<sql id="partner_invest_new_sql">
		
		SELECT
			${group},
			id,
			ischeck,
			IFNULL(sum(invest_amount),0) investAmount 
		FROM
		<choose>
			<when test="app_category_filter != null and app_category_filter != ''">
				partner_invest_info_overseas
			</when>
			<otherwise>
				partner_invest_info
			</otherwise>
		</choose>
		
		WHERE tdate BETWEEN #{sdate} AND #{edate} 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="cha_media != null and cha_media != ''">
				and cha_media in (${cha_media}) 
			</if>
			<if test="ischeck != null and ischeck != ''">
				and ischeck = #{ischeck} 
			</if>
			<if test="apps != null and apps != ''">
				and appid in (${apps}) 
			</if>
			<if test="country_code != null and country_code != ''">
				and country_code in (${country_code})
			</if>
			<if test="app_category != null and app_category != ''">
				and appid in (select id from app_info where app_category in (${app_category}))
			</if>
		group by ${group}
		<choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate desc
            </otherwise>
        </choose>
	</sql>
	
	<!-- 合作方变现明细查询-新 -->
	<select id="selectPartnerRevenueInfoNew" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="partner_revenue_new_sql"/>
	</select>
	<select id="selectPartnerRevenueInfoNewSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			TRUNCATE(sum(xx.revenue),2) revenue,
			TRUNCATE(sum(xx.revenue)/sum(xx.pv)*1000,2) ecpm 
		from (<include refid="partner_revenue_new_sql"/>) xx
	</select>
	<sql id="partner_revenue_new_sql">
		
		SELECT
			${group},
			ischeck,
			ifnull(TRUNCATE(sum(t1.revenue),2),0) revenue,
			ifnull(TRUNCATE(sum(t1.pv),0),0) pv,
			ifnull(TRUNCATE(sum(t1.revenue)/sum(t1.pv)*1000,2),0) ecpm
		FROM
		<choose>
			<when test="app_category_filter != null and app_category_filter != ''">
				partner_revenue_info_overseas t1
			</when>
			<otherwise>
				partner_revenue_info t1
			</otherwise>
		</choose>

		
		WHERE tdate BETWEEN #{sdate} AND #{edate} 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="agent != null and agent != ''">
				and agent in (${agent})
			</if>
			<if test="ischeck != null and ischeck != ''">
				and ischeck = #{ischeck} 
			</if>
			<if test="apps != null and apps != ''">
				and appid in (${apps}) 
			</if>
			<if test="country_code != null and country_code != ''">
				and country_code in (${country_code})
			</if>
			<if test="app_category != null and app_category != ''">
				and appid in (select id from app_info where app_category in (${app_category}))
			</if>
		group by ${group}
		<choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by tdate desc
            </otherwise>
        </choose>
	</sql>
	
	<!-- 合作产品收支稽核查询 -->
	<select id="selectPartnerAppRevenueNew" parameterType="java.util.Map" resultType="java.util.Map">
		select
		    tdate,
		    appid,
			ischeck,
			rebate_consume,
			addnum,
			actnum,
			paynum,
			ad_revenue,
			bd_revenue,
			pay_revenue,
			refund_revenue,
			total_revenue,
			total_arpu,

			concat(addrate,'%') as addrate,
			concat(payrate,'%') as payrate,
			cpa,
			ad_arpu,
			bd_arpu,
			pay_arpu,
			paynum_arpu,
			profit,
			sum_profit,
			concat(profit_rate,'%') as profit_rate,
			concat(sum_profit_rate,'%') as sum_profit_rate,
			all_expenses,
			all_revenue,
			all_pay_revenue,
			all_refund_revenue,
			all_sum_revenue,
			concat(all_profit_rate,'%') as all_profit_rate,
			concat(all_sum_profit_rate,'%') as all_sum_profit_rate
		from (<include refid="partner_app_revenue_new_sql"/>) as temp
	</select>
	<select id="selectPartnerAppRevenueNewSum" parameterType="java.util.Map" resultType="java.util.Map">
		select 
			TRUNCATE(SUM(xx.rebate_consume),2) rebate_consume,
			SUM(xx.addnum) addnum,
			SUM(xx.actnum) actnum,
			SUM(xx.paynum) paynum,
			TRUNCATE(SUM(xx.ad_revenue),2) ad_revenue,
			TRUNCATE(SUM(xx.bd_revenue),2) bd_revenue,
			TRUNCATE(SUM(xx.pay_revenue),2) pay_revenue,
			TRUNCATE(SUM(xx.refund_revenue),0) refund_revenue,
			TRUNCATE(SUM(xx.ad_revenue)+SUM(xx.bd_revenue)+SUM(xx.pay_revenue)-TRUNCATE(SUM(xx.refund_revenue),0),2) total_revenue,
			TRUNCATE((SUM(xx.ad_revenue)+SUM(xx.bd_revenue)+SUM(xx.pay_revenue)-TRUNCATE(SUM(xx.refund_revenue),0)) / SUM(xx.actnum), 2) total_arpu,
			
			CONCAT(TRUNCATE(SUM(xx.addnum) / SUM(xx.actnum)*100, 1),'%') addrate,
			CONCAT(TRUNCATE(SUM(xx.paynum) / SUM(xx.actnum)*100, 1),'%') payrate,
			TRUNCATE(SUM(xx.rebate_consume) / SUM(xx.addnum), 1) cpa,
			TRUNCATE(SUM(xx.ad_revenue) / SUM(xx.actnum), 2) ad_arpu,
			TRUNCATE(SUM(xx.bd_revenue) / SUM(xx.actnum), 2) bd_arpu,
			TRUNCATE((SUM(xx.pay_revenue)-TRUNCATE(SUM(xx.refund_revenue),0)) / SUM(xx.actnum), 2) pay_arpu,
			TRUNCATE((SUM(xx.pay_revenue)-TRUNCATE(SUM(xx.refund_revenue),0)) / SUM(xx.paynum), 2) paynum_arpu,
			TRUNCATE(SUM(xx.ad_revenue) - SUM(xx.rebate_consume), 0) profit,
			TRUNCATE(SUM(xx.ad_revenue) + SUM(xx.bd_revenue) + SUM(xx.pay_revenue) - TRUNCATE(SUM(xx.refund_revenue),0) - SUM(xx.rebate_consume), 0) sum_profit,
			CONCAT(IFNULL(TRUNCATE((SUM(xx.ad_revenue) - SUM(xx.rebate_consume))/(SUM(xx.ad_revenue) + SUM(xx.bd_revenue) + SUM(xx.pay_revenue) - TRUNCATE(SUM(xx.refund_revenue),0))*100, 1),0),'%') profit_rate,
			CONCAT(IFNULL(TRUNCATE((SUM(xx.ad_revenue) + SUM(xx.bd_revenue) + SUM(xx.pay_revenue) - TRUNCATE(SUM(xx.refund_revenue),0) - SUM(xx.rebate_consume))/(SUM(xx.ad_revenue) + SUM(xx.bd_revenue) + SUM(xx.pay_revenue) - TRUNCATE(SUM(xx.refund_revenue),0))*100, 1),0),'%') sum_profit_rate
				
		from (<include refid="partner_app_revenue_new_sql"/>) xx
	</select>
    <sql id="partner_app_revenue_new_sql">
		select aa.*,
			dd.all_expenses,
			dd.all_revenue,
			dd.all_pay_revenue,
			dd.all_refund_revenue,
			dd.all_sum_revenue,
			dd.all_profit_rate,
			dd.all_sum_profit_rate
		from
			(select 
				${group} tdate,
				appid,
				ischeck,
				TRUNCATE(SUM(rebate_consume),2) rebate_consume,
				SUM(addnum) addnum,
				SUM(actnum) actnum,
				SUM(paynum) paynum,
				TRUNCATE(SUM(ad_revenue),2) ad_revenue,
				TRUNCATE(SUM(bd_revenue),2) bd_revenue,
				TRUNCATE(SUM(pay_revenue),2) pay_revenue,
				TRUNCATE(SUM(refund_revenue),0) refund_revenue,
				TRUNCATE(SUM(ad_revenue)+SUM(bd_revenue)+SUM(pay_revenue)-TRUNCATE(SUM(refund_revenue),0),2) total_revenue,
				TRUNCATE((SUM(ad_revenue)+SUM(bd_revenue)+SUM(pay_revenue)-TRUNCATE(SUM(refund_revenue),0)) / SUM(actnum), 2) total_arpu,
				
				TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1) addrate,
				TRUNCATE(SUM(paynum) / SUM(actnum)*100, 1) payrate,
				IFNULL(TRUNCATE(SUM(rebate_consume) / SUM(addnum), 1),0) cpa,
				TRUNCATE(SUM(ad_revenue) / SUM(actnum), 2) ad_arpu,
				TRUNCATE(SUM(bd_revenue) / SUM(actnum), 2) bd_arpu,
				TRUNCATE((SUM(pay_revenue)-TRUNCATE(SUM(refund_revenue),0)) / SUM(actnum), 2) pay_arpu,
				TRUNCATE((SUM(pay_revenue)-TRUNCATE(SUM(refund_revenue),0)) / SUM(paynum), 2) paynum_arpu,
				TRUNCATE(SUM(ad_revenue) - SUM(rebate_consume), 0) profit,
				TRUNCATE(SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) - TRUNCATE(SUM(refund_revenue),0) - SUM(rebate_consume), 0) sum_profit,
				IFNULL(TRUNCATE((SUM(ad_revenue) - SUM(rebate_consume))/(SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) - TRUNCATE(SUM(refund_revenue),0))*100, 1),0) profit_rate,
				IFNULL(TRUNCATE((SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) - TRUNCATE(SUM(refund_revenue),0) - SUM(rebate_consume))/(SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) - TRUNCATE(SUM(refund_revenue),0))*100, 1),0) sum_profit_rate
			from (

				<choose>
					<when test="app_category_filter != null and app_category_filter != ''">
						<include refid="app_revenue_table_overseas"/>
					</when>
					<otherwise>
						<include refid="app_revenue_table"/>
					</otherwise>
				</choose>
			) xx
			where tdate BETWEEN #{sdate} AND #{edate} 
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="country_code != null and country_code != ''">
				and country_code in (${country_code})
			</if>
			<if test="app_category != null and app_category != ''">
				and appid in (select id from app_info where app_category in (${app_category}))
			</if>
			
			group by ${group},appid) aa
		 
		 LEFT JOIN 
			(select 
				appid,
				TRUNCATE(SUM(rebate_consume),2) all_expenses,
				TRUNCATE(SUM(ad_revenue),2) all_revenue,
				TRUNCATE(SUM(pay_revenue),2) all_pay_revenue,
				TRUNCATE(SUM(refund_revenue),0) all_refund_revenue,
				TRUNCATE(IFNULL(SUM(ad_revenue),0) + IFNULL(SUM(bd_revenue),0) + IFNULL(SUM(pay_revenue),0) - IFNULL(TRUNCATE (SUM(refund_revenue), 0),0), 2) all_sum_revenue,
				IFNULL(TRUNCATE((SUM(ad_revenue) - SUM(rebate_consume))/(SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) - TRUNCATE(SUM(refund_revenue),0))*100, 1),0) all_profit_rate,
				IFNULL(TRUNCATE((SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) - TRUNCATE(SUM(refund_revenue),0) - SUM(rebate_consume))/(SUM(ad_revenue) + SUM(bd_revenue) + SUM(pay_revenue) - TRUNCATE(SUM(refund_revenue),0))*100, 1),0) all_sum_profit_rate
			from (
				<choose>
					<when test="app_category_filter != null and app_category_filter != ''">
						<include refid="all_app_revenue_table_overseas"/>
					</when>
					<otherwise>
						<include refid="all_app_revenue_table"/>
					</otherwise>
				</choose>
			) xx where 1=1
			<if test="appid != null and appid != ''">
				and appid in (${appid}) 
			</if>
			<if test="country_code != null and country_code != ''">
				and country_code in (${country_code})
			</if>
			<if test="app_category != null and app_category != ''">
				and appid in (select id from app_info where app_category in (${app_category}))
			</if>
			
			group by appid) dd 
			
		ON aa.appid = dd.appid
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate asc, addnum desc
			</otherwise>
		</choose>
	</sql>
	
	<!--  以投放表为主，关联投放、变现、新增活跃表
		通过full join方式，完整显示出投放消耗或变现收入有值的记录
		修改数据关联依据，兼容超休产品只有内购收入的情况
	 -->
	<sql id="app_revenue_table">
		SELECT aa.tdate,aa.appid,IFNULL(aa.rebate_consume,0) rebate_consume,IFNULL(aa.ad_revenue,0) ad_revenue,'0' bd_revenue,
				zz.actnum,zz.addnum,IFNULL(zz.pay_revenue,0) pay_revenue,IFNULL(zz.refund_revenue,0) refund_revenue,zz.paynum,zz.ischeck  

		FROM 
			(
				SELECT xx.tdate,xx.appid,xx.rebate_consume,yy.ad_revenue from
					(select tdate,appid,SUM(invest_amount) rebate_consume from partner_invest_info where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) xx

				LEFT JOIN
					(select tdate,appid,SUM(revenue) ad_revenue from partner_revenue_info where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) yy
				ON xx.tdate=yy.tdate AND xx.appid=yy.appid

				union all

				SELECT yy.tdate,yy.appid,xx.rebate_consume,yy.ad_revenue from
					(select tdate,appid,SUM(invest_amount) rebate_consume from partner_invest_info where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) xx

				RIGHT JOIN
					(select tdate,appid,SUM(revenue) ad_revenue from partner_revenue_info where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) yy
				ON xx.tdate=yy.tdate AND xx.appid=yy.appid WHERE xx.rebate_consume is null
			) aa

		LEFT JOIN
			(select tdate,appid,actnum,addnum,pay_revenue,refund_revenue,paynum,ischeck from partner_app_revenue_total where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) zz
		ON aa.tdate=zz.tdate AND aa.appid=zz.appid


		union all

		SELECT zz.tdate,zz.appid,IFNULL(aa.rebate_consume,0) rebate_consume,IFNULL(aa.ad_revenue,0) ad_revenue,'0' bd_revenue,
			   zz.actnum,zz.addnum,IFNULL(zz.pay_revenue,0) pay_revenue,IFNULL(zz.refund_revenue,0) refund_revenue,zz.paynum,zz.ischeck

		FROM
			(
				SELECT xx.tdate,xx.appid,xx.rebate_consume,yy.ad_revenue from
					(select tdate,appid,SUM(invest_amount) rebate_consume from partner_invest_info where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) xx

						LEFT JOIN
					(select tdate,appid,SUM(revenue) ad_revenue from partner_revenue_info where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) yy
					ON xx.tdate=yy.tdate AND xx.appid=yy.appid

				union all

				SELECT yy.tdate,yy.appid,xx.rebate_consume,yy.ad_revenue from
					(select tdate,appid,SUM(invest_amount) rebate_consume from partner_invest_info where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) xx

						RIGHT JOIN
					(select tdate,appid,SUM(revenue) ad_revenue from partner_revenue_info where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) yy
					ON xx.tdate=yy.tdate AND xx.appid=yy.appid WHERE xx.rebate_consume is null
			) aa

		RIGHT JOIN
			(select tdate,appid,actnum,addnum,pay_revenue,refund_revenue,paynum,ischeck from partner_app_revenue_total where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) zz
			ON aa.tdate=zz.tdate AND aa.appid=zz.appid WHERE aa.appid is null


	</sql>
	<sql id="all_app_revenue_table">
		SELECT xx.appid,xx.rebate_consume,yy.ad_revenue,'0' bd_revenue,zz.pay_revenue,zz.refund_revenue

		FROM (select appid,SUM(invest_amount) rebate_consume from partner_invest_info group by appid) xx

		LEFT JOIN (select appid,SUM(revenue) ad_revenue from partner_revenue_info group by appid) yy
		ON xx.appid=yy.appid

		LEFT JOIN (select appid,SUM(pay_revenue) pay_revenue,SUM(refund_revenue) refund_revenue from partner_app_revenue_total group by appid) zz
		ON xx.appid=zz.appid
	</sql>

	<!-- 海外版本 -->
	<sql id="app_revenue_table_overseas">
		SELECT aa.tdate,aa.appid,IFNULL(aa.rebate_consume,0) rebate_consume,IFNULL(aa.ad_revenue,0) ad_revenue,'0' bd_revenue,
				zz.actnum,zz.addnum,IFNULL(zz.pay_revenue,0) pay_revenue,IFNULL(zz.refund_revenue,0) refund_revenue,zz.paynum,zz.ischeck

		FROM
			(
				SELECT xx.tdate,xx.appid,xx.rebate_consume,yy.ad_revenue from
					(select tdate,appid,SUM(invest_amount) rebate_consume from partner_invest_info_overseas where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) xx

				LEFT JOIN
					(select tdate,appid,SUM(revenue) ad_revenue from partner_revenue_info_overseas where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) yy
				ON xx.tdate=yy.tdate AND xx.appid=yy.appid

				union all

				SELECT yy.tdate,yy.appid,xx.rebate_consume,yy.ad_revenue from
					(select tdate,appid,SUM(invest_amount) rebate_consume from partner_invest_info_overseas where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) xx

				RIGHT JOIN
					(select tdate,appid,SUM(revenue) ad_revenue from partner_revenue_info_overseas where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) yy
				ON xx.tdate=yy.tdate AND xx.appid=yy.appid WHERE xx.rebate_consume is null
			) aa

		LEFT JOIN
			(select tdate,appid,SUM(actnum) actnum,SUM(addnum) addnum,SUM(pay_revenue) pay_revenue,sum(refund_revenue) refund_revenue,SUM(paynum) paynum,ischeck from partner_app_revenue_total_overseas where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) zz
		ON aa.tdate=zz.tdate AND aa.appid=zz.appid


		union all

		SELECT zz.tdate,zz.appid,IFNULL(aa.rebate_consume,0) rebate_consume,IFNULL(aa.ad_revenue,0) ad_revenue,'0' bd_revenue,
			   zz.actnum,zz.addnum,IFNULL(zz.pay_revenue,0) pay_revenue,IFNULL(zz.refund_revenue,0) refund_revenue,zz.paynum,zz.ischeck

		FROM
			(
				SELECT xx.tdate,xx.appid,xx.rebate_consume,yy.ad_revenue from
					(select tdate,appid,SUM(invest_amount) rebate_consume from partner_invest_info_overseas where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) xx

						LEFT JOIN
					(select tdate,appid,SUM(revenue) ad_revenue from partner_revenue_info_overseas where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) yy
					ON xx.tdate=yy.tdate AND xx.appid=yy.appid

				union all

				SELECT yy.tdate,yy.appid,xx.rebate_consume,yy.ad_revenue from
					(select tdate,appid,SUM(invest_amount) rebate_consume from partner_invest_info_overseas where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) xx

						RIGHT JOIN
					(select tdate,appid,SUM(revenue) ad_revenue from partner_revenue_info_overseas where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) yy
					ON xx.tdate=yy.tdate AND xx.appid=yy.appid WHERE xx.rebate_consume is null
			) aa

		RIGHT JOIN
			(select tdate,appid,SUM(actnum) actnum,SUM(addnum) addnum,SUM(pay_revenue) pay_revenue,sum(refund_revenue) refund_revenue,SUM(paynum) paynum,ischeck from partner_app_revenue_total_overseas where tdate BETWEEN #{sdate} AND #{edate} group by tdate,appid) zz
			ON aa.tdate=zz.tdate AND aa.appid=zz.appid WHERE aa.appid is null


	</sql>
	<sql id="all_app_revenue_table_overseas">
		SELECT xx.appid,xx.rebate_consume,yy.ad_revenue,'0' bd_revenue,zz.pay_revenue,zz.refund_revenue

		FROM (select appid,SUM(invest_amount) rebate_consume from partner_invest_info_overseas group by appid) xx

		LEFT JOIN (select appid,SUM(revenue) ad_revenue from partner_revenue_info_overseas group by appid) yy
		ON xx.appid=yy.appid

		LEFT JOIN (select appid,SUM(pay_revenue) pay_revenue,SUM(refund_revenue) refund_revenue from partner_app_revenue_total_overseas group by appid) zz
		ON xx.appid=zz.appid
	</sql>

	<!-- 同步拉取 投放、变现、新增活跃 -->
	<insert id="insertPartnerInvestNew" parameterType="java.lang.String">
		INSERT INTO partner_invest_info(tdate,appid,cha_media,invest_amount,ischeck) 
		
		
		SELECT
			`day` tdate,app appid,media cha_media,
			IFNULL(TRUNCATE(SUM(rebateSpend), 2),0) invest_amount,
			'0' as 'ischeck'
		FROM dn_report_spend_china_summary 
		WHERE `day` = '${tdate}' and LENGTH(app) = 5
		GROUP BY app,media
	
	</insert>
	<insert id="insertPartnerInvestNewOfCps" parameterType="java.lang.String">
		INSERT INTO partner_invest_info(tdate,appid,cha_media,invest_amount,ischeck) 
		
		VALUES
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.appid},
			#{li.cha_media},
			#{li.rebate_consume},
			'0') 
		</foreach>
	
	</insert>
	<insert id="insertPartnerRevenueNew" parameterType="java.lang.String">
		INSERT INTO partner_revenue_info(tdate,appid,agent,revenue,pv,ischeck) 
		
		
		SELECT
			date,dnappid,agent,
			IFNULL(TRUNCATE(sum(t1.revenue),2),0) revenue,
			IFNULL(TRUNCATE(sum(t1.pv),0),0) pv,
			'0' as 'ischeck'
		FROM dn_cha_cash_total t1 
			
		WHERE date='${tdate}' and app_id != '0' and dnappid != '0'
		GROUP BY date,dnappid,agent
	</insert>
	
	
	<insert id="insertPartnerAppRevenueNewBilling" parameterType="java.lang.String">
		REPLACE INTO partner_app_revenue_total(tdate,appid,actnum,addnum,pay_revenue,refund_revenue,paynum,ischeck)

		SELECT xx.*,IFNULL(yy.pay_revenue,0) pay_revenue,IFNULL(yy.refund_revenue,0) refund_revenue,IFNULL(yy.paynum,0) paynum,'0' 
		FROM 
			(select tdate,appid,SUM(act_num) actnum,SUM(add_num) addnum from umeng_user_channel_total where tdate='${tdate}'
				and appid not in (select id from app_info where bus_category=2) group by tdate,appid ) xx
		LEFT JOIN 
			(select '${tdate}' as tdate,appid,paynum,
				IFNULL(TRUNCATE(SUM(
						CASE WHEN rate is not NULL THEN pay_revenue*rate/100
							 WHEN paytype='苹果支付' THEN pay_revenue*0.69
							 WHEN paytype='字节支付' and chaid='dyly' THEN pay_revenue*0.2 
							 WHEN paytype='小米支付' and chaid='xiaomi' THEN pay_revenue*0.40
							 WHEN paytype='小米支付' and chaid='xiaomimj' THEN pay_revenue*0.40
							 WHEN paytype='oppo支付' and chaid='oppo' THEN pay_revenue*0.40
							 WHEN paytype='oppo支付' and chaid='oppomj' THEN pay_revenue*0.40
							 WHEN paytype='oppo支付' and chaid='oppoml' THEN pay_revenue*0.40
							 WHEN paytype='vivo支付' and chaid='vivo' THEN pay_revenue*0.40
							 WHEN paytype='vivo支付' and chaid='vivoml' THEN pay_revenue*0.40
							 WHEN paytype='华为支付' and chaid='huawei' THEN pay_revenue*0.40
							 WHEN paytype='微信支付' THEN pay_revenue*0.98
							 WHEN paytype='支付宝' THEN pay_revenue*0.98
							 WHEN paytype='聚合支付' THEN pay_revenue*0.98
							 ELSE pay_revenue END) /100, 2), 0) pay_revenue,
				IFNULL(TRUNCATE(SUM(
						CASE WHEN rate is not NULL THEN refund_revenue*rate/100
							 WHEN paytype='苹果支付' THEN refund_revenue*0.69
							 WHEN paytype='字节支付' and chaid='dyly' THEN refund_revenue*0.2
							 WHEN paytype='小米支付' and chaid='xiaomi' THEN refund_revenue*0.40
							 WHEN paytype='小米支付' and chaid='xiaomimj' THEN refund_revenue*0.40
							 WHEN paytype='oppo支付' and chaid='oppo' THEN refund_revenue*0.40
							 WHEN paytype='oppo支付' and chaid='oppomj' THEN refund_revenue*0.40
							 WHEN paytype='oppo支付' and chaid='oppoml' THEN refund_revenue*0.40
							 WHEN paytype='vivo支付' and chaid='vivo' THEN refund_revenue*0.40
							 WHEN paytype='vivo支付' and chaid='vivoml' THEN refund_revenue*0.40
							 WHEN paytype='华为支付' and chaid='huawei' THEN refund_revenue*0.40
							 WHEN paytype='微信支付' THEN refund_revenue*0.98
							 WHEN paytype='支付宝' THEN refund_revenue*0.98
							 WHEN paytype='聚合支付' THEN refund_revenue*0.98
							 ELSE refund_revenue END) /100, 2), 0) refund_revenue
			from wb_pay_refund_total where tdate='${tdate}' 
			and appid not in (select id from app_info where bus_category=2) group by tdate,appid ) yy
		
		ON xx.tdate=yy.tdate and xx.appid=yy.appid
  	</insert>
  	
	<insert id="insertPartnerAppCostTotal" parameterType="java.util.Map">
		INSERT IGNORE partner_channel_cost(tdate,appid,invest_amount,adv_income,billing_income,refund_revenue,addnum,actnum,paynum)

		SELECT xx.tdate,xx.appid,xx.rebate_consume,xx.ad_revenue,xx.pay_revenue,xx.refund_revenue,xx.addnum,xx.actnum,xx.paynum 
		FROM (<include refid="app_revenue_table"/>) xx
  	</insert>

	<!-- 拉取用户退款金额，付费用户数使用产品维度 -->
	<insert id="insertPartnerAppPayRefund" parameterType="java.util.Map">
		INSERT IGNORE wb_pay_refund_total(tdate,appid,chaid,paytype,pay_revenue,refund_revenue,money,paynum)
		
		select aa.*,(pay_revenue-refund_revenue) money,IFNULL(cc.paynum, 0) paynum from
			(select DATE_FORMAT(createtime,'%Y-%m-%d') as tdate,appid,IFNULL(chaid,'') chaid,IFNULL(paytype,'') paytype,IFNULL(SUM(money), 0) pay_revenue,'0' refund_revenue
			from wb_pay_info  
			where createtime BETWEEN '${tdate} 00:00:00' AND '${tdate} 23:59:59'
				and orderstatus='SUCCESS'
			  	and (oaid != 'test' or oaid is null) and (param3 != 'test' or param3 is null)
			  	and appid is not null and paytype is not null
			group by DATE_FORMAT(createtime,'%Y-%m-%d'),appid,chaid,paytype) aa
			
		LEFT JOIN
			
			(select DATE_FORMAT(createtime,'%Y-%m-%d') as tdate,appid,COUNT(DISTINCT imei) paynum
			from wb_pay_info  
			where createtime BETWEEN '${tdate} 00:00:00' AND '${tdate} 23:59:59'
				and orderstatus='SUCCESS'
				and (oaid != 'test' or oaid is null) and (param3 != 'test' or param3 is null)
				and appid is not null and paytype is not null
			group by DATE_FORMAT(createtime,'%Y-%m-%d'),appid) cc
			
		ON aa.tdate=cc.tdate and aa.appid=cc.appid
		
	</insert>

	<update id="updatePartnerAppPayRefundRate" parameterType="java.util.Map">
		UPDATE wb_pay_refund_total xx
			INNER JOIN partner_app_paytype_rate yy
			ON xx.tdate='${tdate}' AND xx.appid=yy.app AND xx.paytype=yy.app_paytype
		SET xx.rate = yy.rate
	</update>



	<!-- 查询合作产品收入与核减信息 -->
	<select id="selectPartnerAppRevenueWithReduce" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT * FROM
			(SELECT
				${group} tdate,t1.appid,
				IFNULL(TRUNCATE(SUM(t1.pay_revenue), 2),0) as pay_revenue,
				IFNULL(TRUNCATE(SUM(t2.reduce_amount), 2),0) as reduce_amount,
				IFNULL(TRUNCATE(SUM(t2.reduce_pay_revenue), 2),0) as reduce_pay_revenue,
				IFNULL(t2.ischeck, '无改动') as ischeck,t2.createtime
			FROM partner_app_revenue_total t1
			LEFT JOIN partner_pay_reduce t2 ON t1.tdate = t2.tdate AND t1.appid = t2.appid
			WHERE t1.tdate BETWEEN #{sdate} and #{edate}
				<if test="appid != null and appid != ''">
					AND t1.appid in (${appid})
				</if>
				<if test="app_category != null and app_category != ''">
					AND t1.appid in (SELECT id FROM app_info where app_category in (${app_category}))
				</if>
				<if test="ischeck != null and ischeck != ''">
				  <choose>
					<when test="ischeck == '0'">
						AND t2.ischeck not in (1,2)
					</when>
					<otherwise>
						AND t2.ischeck = #{ischeck}
					</otherwise>
				  </choose>
				</if>
			GROUP BY ${group},appid
			) xx
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate asc, pay_revenue desc
			</otherwise>
		</choose>
	</select>

	<!-- 查询合作产品收入与核减信息汇总 -->
	<select id="selectPartnerAppRevenueWithReduceSum" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			SUM(IFNULL(t1.pay_revenue, 0)) as pay_revenue,
			SUM(IFNULL(t1.refund_revenue, 0)) as refund_revenue,
			SUM(IFNULL(t2.reduce_amount, 0)) as reduce_amount,
			SUM(IFNULL(t2.reduce_pay_revenue, 0)) as reduce_pay_revenue
		FROM partner_app_revenue_total t1
		LEFT JOIN partner_pay_reduce t2 ON t1.tdate = t2.tdate AND t1.appid = t2.appid
		WHERE t1.tdate BETWEEN #{sdate} and #{edate}
			<if test="appid != null and appid != ''">
				AND t1.appid in (${appid})
			</if>
			<if test="app_category != null and app_category != ''">
				AND t1.appid in (SELECT id FROM app_info where app_category in (${app_category}))
			</if>
			<if test="ischeck != null and ischeck != ''">
				<choose>
					<when test="ischeck == '0'">
						AND t2.ischeck not in (1,2)
					</when>
					<otherwise>
						AND t2.ischeck = #{ischeck}
					</otherwise>
				</choose>
			</if>
	</select>


</mapper>