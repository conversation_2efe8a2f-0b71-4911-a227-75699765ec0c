<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.ActivityInfoMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.operate.ActivityInfo">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="app_id" property="appId" jdbcType="INTEGER"/>
        <result column="activity_id" property="activityId" jdbcType="VARCHAR"/>
        <result column="activity_name" property="activityName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , app_id, activity_id, activity_name, status, create_user, update_user,
        date_format(create_time, '%Y-%m-%d %H:%i:%s') create_time,
        date_format(update_time, '%Y-%m-%d %H:%i:%s') update_time

    </sql>
    <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.operate.ActivityInfo">
        select

        <include refid="Base_Column_List"/>
        from activity_info
        <where>
            
            <if test="activityId != null and activityId != ''">
                and activity_id = #{activityId}
            </if>
            <if test="activityName != null and activityName != ''">
                and activity_name = #{activityName}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="appidList != null and appidList.size > 0">
                AND app_id IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>

        order by id desc
    </select>

    <delete id="deleteActivity" parameterType="java.lang.Integer">
        delete
        from activity_info
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertActivity" parameterType="com.wbgame.pojo.operate.ActivityInfo">
        insert into activity_info
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="appId != null">
                app_id,
            </if>
            <if test="activityId != null and activityId != ''">
                activity_id,
            </if>
            <if test="activityName != null and activityName != ''">
                activity_name,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createUser != null">
                create_user,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="appId != null">
                #{appId,jdbcType=INTEGER},
            </if>
            <if test="activityId != null and activityId != ''">
                #{activityId,jdbcType=VARCHAR},
            </if>
            <if test="activityName != null and activityName != ''">
                #{activityName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>


        </trim>
    </insert>

    <update id="updateActivity" parameterType="com.wbgame.pojo.operate.ActivityInfo">
        update activity_info
        <set>
            <if test="appId != null">
                app_id = #{appId,jdbcType=INTEGER},
            </if>
            <if test="activityId != null">
                activity_id = #{activityId,jdbcType=VARCHAR},
            </if>
            <if test="activityName != null">
                activity_name = #{activityName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>

            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>

            update_time = now()
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <insert id="batchImport" parameterType="com.wbgame.pojo.operate.ActivityInfo">

        <foreach collection="list" item="data" separator=";">

            insert into activity_info

            <trim prefix="(" suffix=")" suffixOverrides=",">

                <if test="data.appId != null">
                    app_id,
                </if>
                <if test="data.activityId != null and data.activityId != ''">
                    activity_id,
                </if>
                <if test="data.activityName != null and data.activityName != ''">
                    activity_name,
                </if>
                <if test="data.status != null">
                    status,
                </if>
                <if test="data.createUser != null">
                    create_user,
                </if>

            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">

                <if test="data.appId != null">
                    #{data.appId,jdbcType=INTEGER},
                </if>
                <if test="data.activityId != null and data.activityId != ''">
                    #{data.activityId,jdbcType=VARCHAR},
                </if>
                <if test="data.activityName != null and data.activityName != ''">
                    #{data.activityName,jdbcType=VARCHAR},
                </if>
                <if test="data.status != null">
                    #{data.status,jdbcType=TINYINT},
                </if>
                <if test="data.createUser != null">
                    #{data.createUser,jdbcType=VARCHAR},
                </if>


            </trim>
        </foreach>
    </insert>

    <select id="getActivityConfig" resultMap="BaseResultMap" parameterType="com.wbgame.pojo.operate.ActivityInfo">
        select
        <include refid="Base_Column_List"/>
        from activity_info
        <where>
            <if test="activityId != null and activityId != ''">
                and activity_id like '%${activityId}%'
            </if>
            <if test="appidList != null and appidList.size > 0">
                AND app_id IN
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

</mapper>