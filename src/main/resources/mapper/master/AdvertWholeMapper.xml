<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.AdvertWholeMapper">

    <select id="selectPayInfoDetailInside" resultType="com.wbgame.pojo.view.PayInfoDetailVo">
        select tdate,pid,paytype,app_category,appid,is_new,zone_id,sum(amount) amount,sum(payNumber) payNumber,sum(payCount) payCount,truncate(sum(amount)/sum(payNumber),2) arpu
        from
        (select date(a.createtime) tdate,zone_id,pid,paytype,c.name app_category,app_name appid,if(ifnull(is_new,0)=1,'新用戶',if(ifnull(is_new,0)=0,'老用戶','未知')) is_new
        ,truncate(sum(money)/100,2) as amount
        ,count(distinct ifnull(imei,'')) as payNumber
        ,count(*) as payCount
        from wb_pay_info a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        where a.createtime BETWEEN concat(#{start_date},' 00:00:00') AND concat(#{end_date},' 23:59:59')
        and orderstatus = 'SUCCESS'
        <if test="appid != null and appid !=''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid !=''">
            and pid = #{pid}
        </if>
        <if test="zone_id != null and zone_id !=''">
            and zone_id = #{zone_id}
        </if>
        <if test="paytype != null and paytype !=''">
            and paytype = #{paytype}
        </if>
        <if test="app_category != null and app_category !=''">
            and app_category in (${app_category})
        </if>
        <if test="is_new != null and is_new != ''">
            and ifnull(is_new,0) = #{is_new}
        </if>
        <choose>
            <when test="type.contains('test')">
                and oaid = 'test'
            </when>
            <otherwise>
                and (oaid != 'test' or oaid is null)
            </otherwise>
        </choose>
        group by date(createtime),a.appid,pid,paytype,app_category,ifnull(is_new,0),zone_id
        union all
        select date(createtime) tdate,zone_id,pid,paytype,c.name app_category,app_name appid,if(is_new=1,'新用戶',if(is_new=0,'老用戶','未知')) is_new
        ,convert(SUM(money),decimal(20,2)) as amount
        ,count(distinct ifnull(imei,'')) as payNumber
        ,count(*) as payCount
        from wb_pay_info_hw a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        where createtime BETWEEN concat(#{start_date},' 00:00:00') AND concat(#{end_date},' 23:59:59')
        and orderstatus = 'SUCCESS'
        <if test="appid != null and appid !=''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid !=''">
            and pid = #{pid}
        </if>
        <if test="zone_id != null and zone_id !=''">
            and zone_id = #{zone_id}
        </if>
        <if test="paytype != null and paytype !=''">
            and paytype = #{paytype}
        </if>
        <if test="app_category != null and app_category !=''">
            and app_category in (${app_category})
        </if>
        <if test="is_new != null and is_new != ''">
            and is_new = #{is_new}
        </if>
        <choose>
            <when test="type.contains('test')">
                and oaid = 'test'
            </when>
            <otherwise>
                and (oaid != 'test' or oaid is null)
            </otherwise>
        </choose>
        group by date(createtime),a.appid,pid,paytype,app_category,is_new,zone_id
        ) a
        group by ${group}
        order by ${order_str}
    </select>

    <select id="countPayInfoDetailInside" resultType="com.wbgame.pojo.view.PayInfoDetailVo">
        select sum(xx.amount) amount,
        sum(xx.payCount) as payCount,
        sum(xx.payNumber) as payNumber,
        truncate(sum(xx.amount)/sum(xx.payNumber),2) as arpu
        from
        (select tdate,pid,paytype,app_category,appid,is_new,zone_id,sum(amount) amount,sum(payNumber) payNumber,sum(payCount) payCount,truncate(sum(amount)/sum(payNumber),2) arpu
        from
        (select date(a.createtime) tdate,zone_id,pid,paytype,c.name app_category,app_name appid,if(ifnull(is_new,0)=1,'新用戶',if(ifnull(is_new,0)=0,'老用戶','未知')) is_new
        ,truncate(sum(money)/100,2) as amount
        ,count(distinct ifnull(imei,'')) as payNumber
        ,count(*) as payCount
        from wb_pay_info a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        where a.createtime BETWEEN concat(#{start_date},' 00:00:00') AND concat(#{end_date},' 23:59:59')
        and orderstatus = 'SUCCESS'
        <if test="appid != null and appid !=''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid !=''">
            and pid = #{pid}
        </if>
        <if test="zone_id != null and zone_id !=''">
            and zone_id = #{zone_id}
        </if>
        <if test="paytype != null and paytype !=''">
            and paytype = #{paytype}
        </if>
        <if test="app_category != null and app_category !=''">
            and app_category in (${app_category})
        </if>
        <if test="is_new != null and is_new != ''">
            and ifnull(is_new,0) = #{is_new}
        </if>
        <choose>
            <when test="type.contains('test')">
                and oaid = 'test'
            </when>
            <otherwise>
                and (oaid != 'test' or oaid is null)
            </otherwise>
        </choose>
        group by date(createtime),a.appid,pid,paytype,app_category,ifnull(is_new,0),zone_id
        union all
        select date(createtime) tdate,zone_id,pid,paytype,c.name app_category,app_name appid,if(is_new=1,'新用戶',if(is_new=0,'老用戶','未知')) is_new
        ,convert(SUM(money),decimal(20,2)) as amount
        ,count(distinct ifnull(imei,'')) as payNumber
        ,count(*) as payCount
        from wb_pay_info_hw a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        where createtime BETWEEN concat(#{start_date},' 00:00:00') AND concat(#{end_date},' 23:59:59')
        and orderstatus = 'SUCCESS'
        <if test="appid != null and appid !=''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid !=''">
            and pid = #{pid}
        </if>
        <if test="zone_id != null and zone_id !=''">
            and zone_id = #{zone_id}
        </if>
        <if test="paytype != null and paytype !=''">
            and paytype = #{paytype}
        </if>
        <if test="app_category != null and app_category !=''">
            and app_category in (${app_category})
        </if>
        <if test="is_new != null and is_new != ''">
            and is_new = #{is_new}
        </if>
        <choose>
            <when test="type.contains('test')">
                and oaid = 'test'
            </when>
            <otherwise>
                and (oaid != 'test' or oaid is null)
            </otherwise>
        </choose>
        group by date(createtime),a.appid,pid,paytype,app_category,is_new,zone_id
        ) a
        group by ${group}
        ) xx
    </select>

    <select id="selectPayInfoDetailOutside" resultType="com.wbgame.pojo.view.PayInfoDetailVo">
        select date(createtime) tdate,zone_id,pid,paytype,c.name app_category,app_name appid,if(is_new=1,'新用戶',if(is_new=0,'老用戶','未知')) is_new
        ,truncate(sum(money),2) as amount
        ,count(distinct ifnull(imei,'')) as payNumber
        ,count(*) as payCount
        ,truncate(sum(money)/count(distinct ifnull(imei,'')),2) as arpu
        from wb_pay_info_hw a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        where createtime BETWEEN concat(#{start_date},' 00:00:00') AND concat(#{end_date},' 23:59:59')
        and orderstatus = 'SUCCESS'
        <if test="appid != null and appid !=''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid !=''">
            and pid = #{pid}
        </if>
        <if test="zone_id != null and zone_id !=''">
            and zone_id = #{zone_id}
        </if>
        <if test="paytype != null and paytype !=''">
            and paytype = #{paytype}
        </if>
        <if test="app_category != null and app_category !=''">
            and app_category in (${app_category})
        </if>
        <if test="is_new != null and is_new != ''">
            and is_new = #{is_new}
        </if>
        <choose>
            <when test="type.contains('test')">
                and oaid = 'test'
            </when>
            <otherwise>
                and (oaid != 'test' or oaid is null)
            </otherwise>
        </choose>
        group by ${group}
        order by ${order_str}
    </select>

    <select id="countPayInfoDetailOutside" resultType="com.wbgame.pojo.view.PayInfoDetailVo">
        select truncate(sum(money),2) as amount,
        count(distinct ifnull(imei,'')) as payNumber,count(*) as payCount,
        truncate(sum(money)/count(distinct ifnull(imei,'')),2) as arpu
        from wb_pay_info_hw a
        left join app_info b on a.appid = b.id
        left join app_category c on b.app_category = c.id
        where createtime BETWEEN concat(#{start_date},' 00:00:00') AND concat(#{end_date},' 23:59:59') and orderstatus = 'SUCCESS'
        <if test="appid != null and appid !=''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid !=''">
            and pid = #{pid}
        </if>
        <if test="zone_id != null and zone_id !=''">
            and zone_id = #{zone_id}
        </if>
        <if test="paytype != null and paytype !=''">
            and paytype = #{paytype}
        </if>
        <if test="app_category != null and app_category !=''">
            and app_category in (${app_category})
        </if>
        <if test="is_new != null and is_new != ''">
            and is_new = #{is_new}
        </if>
        <choose>
            <when test="type.contains('test')">
                and oaid = 'test'
            </when>
            <otherwise>
                and (oaid != 'test' or oaid is null)
            </otherwise>
        </choose>
    </select>

    <select id="selectPayRefundInfos" resultType="com.wbgame.pojo.view.PayRefundInfoVo">
        select success_date,b.appid,app_name,name app_category,param2 mchid,b.chaid,b.paytype,b.pid
        ,round(sum(refund_fee/100),2) amount,count(1) number,count(distinct b.imei) users
        from refund_order_info a
        left join wb_pay_info b on a.pay_order = b.orderid
        left join app_info c on b.appid = c.id
        left join app_category d on c.app_category = d.id
        where success_date between #{start_date} and #{end_date} and refund_status = 'SUCCESS'
        <if test="appid != null and appid != ''">
            and b.appid in (${appid})
        </if>
        <if test="app_category != null and app_category != ''">
            and c.app_category in (${app_category})
        </if>
        <if test="paytype != null and paytype != ''">
            and b.paytype in (${paytype})
        </if>
        <if test="pid != null and pid != ''">
            and b.pid = #{pid}
        </if>
        group by ${group}
        order by ${order_str}
    </select>

    <select id="countPayRefundInfos" resultType="com.wbgame.pojo.view.PayRefundInfoVo">
        select round(sum(refund_fee/100),2) amount,count(1) number,count(distinct b.imei) users
        from refund_order_info a
        left join wb_pay_info b on a.pay_order = b.orderid
        left join app_info c on b.appid = c.id
        left join app_category d on c.app_category = d.id
        where success_date between #{start_date} and #{end_date} and refund_status = 'SUCCESS'
        <if test="appid != null and appid != ''">
            and b.appid in (${appid})
        </if>
        <if test="app_category != null and app_category != ''">
            and c.app_category in (${app_category})
        </if>
        <if test="paytype != null and paytype != ''">
            and b.paytype in (${paytype})
        </if>
        <if test="pid != null and pid != ''">
            and b.pid = #{pid}
        </if>
    </select>

</mapper>