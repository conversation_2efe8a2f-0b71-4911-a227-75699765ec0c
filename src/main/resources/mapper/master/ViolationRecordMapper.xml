<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.ViolationRecordMapper">


    <insert id="batchInsert" parameterType="java.util.List">
        replace into xiaomi_violation_record (
            company,punish_type,appid,appname,channel,online_name,tappid,package_name,adsense_name,adsense_id,
            adsense_type,violation_type,violation_time,violation_reason,deadline,status,last_modified_time,
            platform,sdk_adtype,ad_usage_type,is_out_app,bidding_mode,ad_strategy,temp_id,temp_name,last_week_revenue,last_week_show,last_week_click
        ) values
        <foreach collection="storeList" item="li" separator=",">
            (#{li.company},#{li.punish_type},#{li.appid},#{li.appname},#{li.channel},#{li.online_name},#{li.tappid},#{li.package_name},#{li.adsense_name},
            #{li.adsense_id},#{li.adsense_type},#{li.violation_type},#{li.violation_time},#{li.violation_reason},#{li.deadline},#{li.status},#{li.last_modified_time},
            #{li.platform},#{li.sdk_adtype},#{li.ad_usage_type},#{li.is_out_app},#{li.bidding_mode},#{li.ad_strategy},#{li.temp_id},#{li.temp_name},
            #{li.last_week_revenue},#{li.last_week_show},#{li.last_week_click})
        </foreach>
    </insert>

    <insert id="batchDuplicateUpdate" parameterType="java.util.List">
        <foreach collection="storeList" separator=";" item="li">
            insert into xiaomi_violation_record (
            company,punish_type,appid,appname,channel,online_name,tappid,package_name,adsense_name,adsense_id,
            adsense_type,violation_type,violation_time,violation_reason,deadline,status,last_modified_time
            ) values
            (
            #{li.company},#{li.punish_type},#{li.appid},#{li.appname},#{li.channel},#{li.online_name},#{li.tappid},#{li.package_name},#{li.adsense_name},
            #{li.adsense_id},#{li.adsense_type},#{li.violation_type},#{li.violation_time},#{li.violation_reason},#{li.deadline},#{li.status},#{li.last_modified_time}
            )
            ON DUPLICATE KEY UPDATE
            appname = VALUES(appname),
            online_name = VALUES(online_name),
            tappid = VALUES(tappid),
            package_name = VALUES(package_name),
            adsense_name = VALUES(adsense_name),
            adsense_id = VALUES(adsense_id),
            adsense_type = VALUES(adsense_type),
            violation_type = VALUES(violation_type),
            violation_reason = VALUES(violation_reason),
            deadline = VALUES(deadline),
            status = VALUES(status),
            last_modified_time = VALUES(last_modified_time)
        </foreach>
    </insert>


    <select id="queryList" resultType="com.wbgame.pojo.XiaomiViolationRecord">
        select * from xiaomi_violation_record
        <where>
            <if test="punish_type != null and punish_type != ''">
                punish_type = #{punish_type}
            </if>
            <if test="company != null and company != ''">
                and company in (${company})
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="online_name != null and online_name != ''">
                and online_name in (${online_name})
            </if>
            <if test="tappid != null and tappid != ''">
                and tappid like concat('%',#{tappid},'%')
            </if>
            <if test="package_name != null and package_name != ''">
                and package_name like concat('%',#{package_name},'%')
            </if>
            <if test="adsense_type != null and adsense_type != ''">
                and adsense_type like concat('%',#{adsense_type},'%')
            </if>
            <if test="violation_type != null and violation_type != ''">
                and violation_type in (${violation_type})
            </if>
            <if test="startTime != null and endTime != null">
                and violation_time between concat(#{startTime},' 00:00:00') and concat(#{endTime},' 23:59:59')
            </if>
            <if test="deadStartTime != null and deadEndTime != null">
                and deadline between concat(#{deadStartTime},' 00:00:00') and concat(#{deadEndTime},' 23:59:59')
            </if>
            <if test="modifiedStartTime != null and modifiedEndTime != null">
                and last_modified_time between concat(#{modifiedStartTime},' 00:00:00') and concat(#{modifiedEndTime},' 23:59:59')
            </if>
            <if test="status != null and status != ''">
                and status in (${status})
            </if>
            <if test="sdk_adtype != null and sdk_adtype != ''">
                and sdk_adtype in (${sdk_adtype})
            </if>
            <if test="ad_usage_type != null and ad_usage_type != ''">
                and ad_usage_type in (${ad_usage_type})
            </if>
            <if test="ad_strategy != null and ad_strategy != ''">
                and ad_strategy in (${ad_strategy})
            </if>
            <if test="temp_id != null and temp_id != ''">
                and temp_id like concat('%',#{temp_id},'%')
            </if>
            <if test="temp_name != null and temp_name != ''">
                and temp_name like concat('%',#{temp_name},'%')
            </if>
        </where>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by violation_time desc
            </otherwise>
        </choose>
    </select>


    <select id="queryStatus" resultType="java.lang.String">
        SELECT DISTINCT `status` FROM xiaomi_violation_record ORDER BY `status` DESC
    </select>


    <select id="queryGroupList" resultType="com.wbgame.pojo.XiaomiViolationRecordVo">
        select
        <choose>
            <when test="groupList != null and groupList.size() > 0">
                ROUND(sum(last_week_revenue),2) last_week_revenue,
                ROUND(sum(last_week_show),0) last_week_show,
                ROUND(sum(last_week_click),0) last_week_click,
                ROUND(sum(last_week_revenue)/sum(last_week_show)*1000,2) ecpm,
                ROUND(sum(last_week_click)/sum(last_week_show)*100,2) ctr,
                ROUND(sum(last_week_revenue)/sum(last_week_click),3) cpc,
                <foreach collection="groupList" item="field" separator=",">
                    COALESCE(${field}, '') AS ${field}
                </foreach>
            </when>
            <otherwise>
                *
            </otherwise>
        </choose>
        from (select a.*,b.appid_tag from xiaomi_violation_record a left join adv_platform_app_info b on a.appid = b.appid and a.channel = b.channel and a.package_name = b.packagename) a
        <where>
            <if test="punish_type != null and punish_type != ''">
                punish_type = #{punish_type}
            </if>
            <if test="company != null and company != ''">
                and company in (${company})
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="online_name != null and online_name != ''">
                and online_name in (${online_name})
            </if>
            <if test="tappid != null and tappid != ''">
                and tappid like concat('%',#{tappid},'%')
            </if>
            <if test="package_name != null and package_name != ''">
                and package_name like concat('%',#{package_name},'%')
            </if>
            <if test="adsense_type != null and adsense_type != ''">
                and adsense_type like concat('%',#{adsense_type},'%')
            </if>
            <if test="violation_type != null and violation_type != ''">
                and violation_type in (${violation_type})
            </if>
            <if test="startTime != null and endTime != null">
                and violation_time between concat(#{startTime},' 00:00:00') and concat(#{endTime},' 23:59:59')
            </if>
            <if test="deadStartTime != null and deadEndTime != null">
                and deadline between concat(#{deadStartTime},' 00:00:00') and concat(#{deadEndTime},' 23:59:59')
            </if>
            <if test="modifiedStartTime != null and modifiedEndTime != null">
                and last_modified_time between concat(#{modifiedStartTime},' 00:00:00') and concat(#{modifiedEndTime},' 23:59:59')
            </if>
            <if test="status != null and status != ''">
                and status in (${status})
            </if>
            <if test="sdk_adtype != null and sdk_adtype != ''">
                and sdk_adtype in (${sdk_adtype})
            </if>
            <if test="ad_usage_type != null and ad_usage_type != ''">
                and ad_usage_type in (${ad_usage_type})
            </if>
            <if test="ad_strategy != null and ad_strategy != ''">
                and ad_strategy in (${ad_strategy})
            </if>
            <if test="temp_id != null and temp_id != ''">
                and temp_id like concat('%',#{temp_id},'%')
            </if>
            <if test="temp_name != null and temp_name != ''">
                and temp_name like concat('%',#{temp_name},'%')
            </if>
            <if test="appid_tag != null and appid_tag.size() > 0">
                <choose>
                    <when test="appid_tag_rev != null and appid_tag_rev != ''">
                        <foreach collection="appid_tag" item="tag" open=" and " close="" separator=" and ">
                            (FIND_IN_SET(#{tag},appid_tag) = 0 or appid_tag is null or appid_tag = '')
                        </foreach>
                    </when>
                    <otherwise>
                        <foreach collection="appid_tag" item="tag" open="and (" close=")" separator=" or ">
                            FIND_IN_SET(#{tag},appid_tag)
                        </foreach>
                    </otherwise>
                </choose>
            </if>
        </where>
        <if test="groupList != null and groupList.size() > 0">
            GROUP BY
            <foreach collection="groupList" item="field" separator=",">
                ${field}
            </foreach>
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by violation_time desc
            </otherwise>
        </choose>
    </select>

    <select id="queryGroupTotal" resultType="com.wbgame.pojo.XiaomiViolationRecordVo">
        select
            ifnull(ROUND(sum(last_week_revenue),2),0.00) last_week_revenue,
            ifnull(ROUND(sum(last_week_show),0),0) last_week_show,
            ifnull(ROUND(sum(last_week_click),0),0) last_week_click,
            ifnull(ROUND(sum(last_week_revenue)/sum(last_week_show)*1000,2),0.00) ecpm,
            ifnull(ROUND(sum(last_week_click)/sum(last_week_show)*100,2),0.00) ctr,
            ifnull(ROUND(sum(last_week_revenue)/sum(last_week_click),3),0.000) cpc
        from (select a.*,b.appid_tag from xiaomi_violation_record a left join adv_platform_app_info b on a.appid = b.appid and a.channel = b.channel and a.package_name = b.packagename) a
        <where>
            <if test="punish_type != null and punish_type != ''">
                punish_type = #{punish_type}
            </if>
            <if test="company != null and company != ''">
                and company in (${company})
            </if>
            <if test="appid != null and appid != ''">
                and appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and channel in (${channel})
            </if>
            <if test="online_name != null and online_name != ''">
                and online_name in (${online_name})
            </if>
            <if test="tappid != null and tappid != ''">
                and tappid like concat('%',#{tappid},'%')
            </if>
            <if test="package_name != null and package_name != ''">
                and package_name like concat('%',#{package_name},'%')
            </if>
            <if test="adsense_type != null and adsense_type != ''">
                and adsense_type like concat('%',#{adsense_type},'%')
            </if>
            <if test="violation_type != null and violation_type != ''">
                and violation_type in (${violation_type})
            </if>
            <if test="startTime != null and endTime != null">
                and violation_time between concat(#{startTime},' 00:00:00') and concat(#{endTime},' 23:59:59')
            </if>
            <if test="deadStartTime != null and deadEndTime != null">
                and deadline between concat(#{deadStartTime},' 00:00:00') and concat(#{deadEndTime},' 23:59:59')
            </if>
            <if test="modifiedStartTime != null and modifiedEndTime != null">
                and last_modified_time between concat(#{modifiedStartTime},' 00:00:00') and concat(#{modifiedEndTime},' 23:59:59')
            </if>
            <if test="status != null and status != ''">
                and status in (${status})
            </if>
            <if test="sdk_adtype != null and sdk_adtype != ''">
                and sdk_adtype in (${sdk_adtype})
            </if>
            <if test="ad_usage_type != null and ad_usage_type != ''">
                and ad_usage_type in (${ad_usage_type})
            </if>
            <if test="ad_strategy != null and ad_strategy != ''">
                and ad_strategy in (${ad_strategy})
            </if>
            <if test="temp_id != null and temp_id != ''">
                and temp_id like concat('%',#{temp_id},'%')
            </if>
            <if test="temp_name != null and temp_name != ''">
                and temp_name like concat('%',#{temp_name},'%')
            </if>
            <if test="appid_tag != null and appid_tag.size() > 0">
                <choose>
                    <when test="appid_tag_rev != null and appid_tag_rev != ''">
                        <foreach collection="appid_tag" item="tag" open=" and " close="" separator=" and ">
                            (FIND_IN_SET(#{tag},appid_tag) = 0 or appid_tag is null or appid_tag = '')
                        </foreach>
                    </when>
                    <otherwise>
                        <foreach collection="appid_tag" item="tag" open="and (" close=")" separator=" or ">
                            FIND_IN_SET(#{tag},appid_tag)
                        </foreach>
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>


    <select id="queryViolationCounts" resultType="com.wbgame.pojo.XiaomiViolationRecordVo">
        SELECT
            COALESCE(a.company, '') AS company,
            COALESCE(a.punish_type, '') AS punish_type,
            COALESCE(a.appid, '') AS appid,
            COALESCE(a.appname, '') AS appname,
            COALESCE(b.appid_tag, '') AS appid_tag,
            COALESCE(a.channel, '') AS channel,
            COALESCE(a.online_name, '') AS online_name,
            COALESCE(a.tappid, '') AS tappid,
            COALESCE(a.package_name, '') AS package_name,
            COALESCE(a.adsense_name, '') AS adsense_name,
            COALESCE(a.adsense_id, '') AS adsense_id,
            COALESCE(a.temp_id, '') AS temp_id,
            COALESCE(a.temp_name, '') AS temp_name,
            COALESCE(a.violation_type, '') AS violation_type,
            COALESCE(date(a.`violation_time`), '') AS violation_time,
            COALESCE(a.violation_reason, '') AS violation_reason,
            COALESCE(a.deadline, '') AS deadline,
            COALESCE(a.last_modified_time, '') AS last_modified_time,
            COALESCE(a.status, '') AS status,
            COALESCE(a.ad_usage_type, '') AS ad_usage_type,
            COALESCE(a.ad_strategy, '') AS ad_strategy,
            COALESCE(a.platform, '') AS platform,
            COALESCE(a.is_out_app, '') AS is_out_app,
            COALESCE(a.bidding_mode, '') AS bidding_mode,
            COUNT(DISTINCT CASE WHEN a.punish_type = '广告位类型违规' AND a.adsense_type = '横幅'
            OR a.punish_type = '广告位违规' AND a.sdk_adtype = 'banner' THEN '横幅' END) AS banner_violation_count,
            COUNT(DISTINCT CASE WHEN a.punish_type = '广告位类型违规' AND a.adsense_type = '原生模板'
            OR a.punish_type = '广告位违规' AND a.sdk_adtype = 'yuans' THEN '原生' END) AS yuans_violation_count,
            COUNT(DISTINCT CASE WHEN a.punish_type = '广告位类型违规' AND a.adsense_type = '插屏'
            OR a.punish_type = '广告位违规' AND a.sdk_adtype = 'plaque' THEN '插屏' END) AS plaque_violation_count,
            COUNT(DISTINCT CASE WHEN a.punish_type = '广告位类型违规' AND a.adsense_type = '激励视频'
            OR a.punish_type = '广告位违规' AND a.sdk_adtype = 'video' THEN '视频' END) AS video_violation_count,
            COUNT(DISTINCT CASE WHEN a.punish_type = '应用违规' THEN '应用违规' END) AS app_violation_count
        FROM xiaomi_violation_record a
        left join adv_platform_app_info b on a.appid = b.appid and a.channel = b.channel and a.package_name = b.packagename
        <where>
            <if test="punish_type != null and punish_type != ''">
                a.punish_type = #{punish_type}
            </if>
            <if test="company != null and company != ''">
                and a.company in (${company})
            </if>
            <if test="appid != null and appid != ''">
                and a.appid in (${appid})
            </if>
            <if test="channel != null and channel != ''">
                and a.channel in (${channel})
            </if>
            <if test="online_name != null and online_name != ''">
                and a.online_name in (${online_name})
            </if>
            <if test="tappid != null and tappid != ''">
                and a.tappid like concat('%',#{tappid},'%')
            </if>
            <if test="package_name != null and package_name != ''">
                and a.package_name like concat('%',#{package_name},'%')
            </if>
            <if test="adsense_type != null and adsense_type != ''">
                and a.adsense_type like concat('%',#{adsense_type},'%')
            </if>
            <if test="violation_type != null and violation_type != ''">
                and a.violation_type in (${violation_type})
            </if>
            <if test="startTime != null and endTime != null">
                and a.violation_time between concat(#{startTime},' 00:00:00') and concat(#{endTime},' 23:59:59')
            </if>
            <if test="deadStartTime != null and deadEndTime != null">
                and a.deadline between concat(#{deadStartTime},' 00:00:00') and concat(#{deadEndTime},' 23:59:59')
            </if>
            <if test="modifiedStartTime != null and modifiedEndTime != null">
                and a.last_modified_time between concat(#{modifiedStartTime},' 00:00:00') and concat(#{modifiedEndTime},' 23:59:59')
            </if>
            <if test="status != null and status != ''">
                and a.status in (${status})
            </if>
            <if test="sdk_adtype != null and sdk_adtype != ''">
                and a.sdk_adtype in (${sdk_adtype})
            </if>
            <if test="ad_usage_type != null and ad_usage_type != ''">
                and a.ad_usage_type in (${ad_usage_type})
            </if>
            <if test="ad_strategy != null and ad_strategy != ''">
                and a.ad_strategy in (${ad_strategy})
            </if>
            <if test="temp_id != null and temp_id != ''">
                and a.temp_id like concat('%',#{temp_id},'%')
            </if>
            <if test="temp_name != null and temp_name != ''">
                and a.temp_name like concat('%',#{temp_name},'%')
            </if>
            <if test="appid_tag != null and appid_tag.size() > 0">
                <choose>
                    <when test="appid_tag_rev != null and appid_tag_rev != ''">
                        <foreach collection="appid_tag" item="tag" open=" and " close="" separator=" and ">
                            (FIND_IN_SET(#{tag},b.appid_tag) = 0 or b.appid_tag is null or b.appid_tag = '')
                        </foreach>
                    </when>
                    <otherwise>
                        <foreach collection="appid_tag" item="tag" open="and (" close=")" separator=" or ">
                            FIND_IN_SET(#{tag},b.appid_tag)
                        </foreach>
                    </otherwise>
                </choose>
            </if>
        </where>
        GROUP BY a.company, a.punish_type, a.appid, a.appname, a.channel, a.online_name, a.tappid, a.package_name,
            a.temp_id, a.violation_type, date(a.`violation_time`), a.status, a.ad_usage_type, a.ad_strategy, a.platform, a.is_out_app, a.bidding_mode
    </select>

    <select id="queryAdsenseTypes" resultType="com.wbgame.pojo.XiaomiViolationRecordVo">
        SELECT
            *
        FROM
            (
            SELECT
                appid,
                channel,
                adsense_type,
                DATE_FORMAT( violation_time, '%Y-%m-%d' ) violation_date,
                CASE
                    `status`
                WHEN '已解封' THEN
                    DATE_FORMAT( last_modified_time, '%Y-%m-%d' ) ELSE DATE_FORMAT( now( ), '%Y-%m-%d' )
                END AS last_modified_date
            FROM
                xiaomi_violation_record
            WHERE
                punish_type = '广告位类型违规'
                AND `status` IN ( '已封禁', '已解封' )
            ) t1
        WHERE
            violation_date &lt;= #{tdate} AND last_modified_date &gt; #{tdate}
    </select>

</mapper>