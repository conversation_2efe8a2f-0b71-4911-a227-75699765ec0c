<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.DnwxFilterListNewMapper">
    <resultMap id="dnwxfilterMap" type="com.wbgame.pojo.clean.DnwxFilterListNewVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="topic" property="topic" jdbcType="VARCHAR"/>
        <result column="appid" property="appid" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="app_group" property="appGroup" jdbcType="INTEGER"/>
        <result column="product_id" property="productId" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="app_name" property="productName" jdbcType="VARCHAR"/>
        <result column="item_id" property="itemId" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="selectFilter">
        d.id, d.topic, d.appid, d.title, d.app_group,d.prjid,
        d.product_id, DATE_FORMAT(d.create_time, "%Y-%m-%d %H:%i:%s") create_time, d.create_user,
        DATE_FORMAT(d.update_time, "%Y-%m-%d %H:%i:%s") update_time, d.update_user, d.item_id
    </sql>
    

    <delete id="deleteByIdList" parameterType="java.lang.Long">
        delete
        from dnwx_filter_list_new
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </delete>

    <delete id="deleteByProductId" parameterType="java.lang.Integer">
        delete
        from dnwx_filter_list_new
        where product_id in
        <foreach collection="list" item="proid" open="(" separator="," close=")">
            #{proid}
        </foreach>

    </delete>


    <insert id="insertDnwxFilterListNew" parameterType="com.wbgame.pojo.clean.DnwxFilterListNew"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into dnwx_filter_list_new
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="topic != null and topic != ''">
                topic,
            </if>
            <if test="appid != null and appid != ''">
                appid,
            </if>
            <if test="title != null and title != ''">
                title,
            </if>
            <if test="appGroup != null and appGroup != ''">
                app_group,
            </if>
            <choose>

                <when test="productId != null and productId != ''">
                    product_id,
                </when>
                <otherwise>
                    item_id,
                </otherwise>
            </choose>

            <if test="createUser != null and createUser != ''">
                create_user,
            </if>

            <if test="prjid != null and prjid != ''">
                prjid,
            </if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="topic != null and topic != ''">
                #{topic,jdbcType=VARCHAR},
            </if>
            <if test="appid != null and appid != ''">
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != ''">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="appGroup != null and appGroup != ''">
                #{appGroup,jdbcType=INTEGER},
            </if>

            <choose>
                <when test="productId != null and productId != ''">
                    #{productId},
                </when>
                <otherwise>
                    #{itemId},
                </otherwise>
            </choose>

            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>

            <if test="prjid != null and prjid != ''">
                #{prjid,jdbcType=LONGVARCHAR},
            </if>
            current_timestamp,
            current_timestamp
        </trim>
    </insert>

    <select id="selectDnwxFilterListNew" parameterType="com.wbgame.pojo.clean.DnwxFilterListNewDTO" resultMap="dnwxfilterMap">

        select <include refid="selectFilter"/>, a.app_name  from dnwx_filter_list_new d
            LEFT JOIN app_info a
            ON d.product_id = a.id
        <where>

            <if test="topic != null and topic != ''">
                and topic = #{topic}
            </if>

            <if test="title != null and title != ''">
                and title like "%" #{title} "%"
            </if>
            <if test="createUser != null and createUser != ''">
                and create_user like #{createUser} "%"
            </if>

            <if test="itemId != null and itemId != ''">
                and item_id like #{itemId} "%"
            </if>

            <if test="prjid != null and prjid != ''">
                and prjid like "%" #{prjid} "%"
            </if>

            <if test="appidList != null and appidList.size > 0">
                and product_id in
                <foreach collection="appidList" item="productId" open="(" separator="," close=")">
                    #{productId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <insert id="insertBatch" parameterType="com.wbgame.pojo.clean.DnwxFilterListNew" >

        insert into dnwx_filter_list_new(topic,title,app_group,product_id,create_user,prjid,item_id,create_time,update_time)
        values
        <foreach collection="list" item="data" separator=",">

                (#{data.topic,jdbcType=VARCHAR},

                #{data.title,jdbcType=VARCHAR},

                #{data.appGroup,jdbcType=VARCHAR},

                #{data.productId,jdbcType=INTEGER},

                #{data.createUser,jdbcType=VARCHAR},

                #{data.prjid,jdbcType=LONGVARCHAR},

                #{data.itemId,jdbcType=VARCHAR},

                current_timestamp,
                current_timestamp)
        </foreach>
    </insert>

    <select id="selectDnwxFilterListNewByTitleAndProduceId" resultMap="dnwxfilterMap">

        select id, topic, appid, title, app_group, product_id from dnwx_filter_list_new

        <where>
            prjid = #{prjid} and topic = #{topic}
            <choose>
                <when test="productId != null">

                    and product_id = #{productId}
                </when>
                <otherwise>
                    and item_id = #{itemId}
                </otherwise>
            </choose>

        </where>

        limit 1

    </select>


    <update id="updateDnwxFilterListNew" parameterType="com.wbgame.pojo.clean.DnwxFilterListNew" >

        update dnwx_filter_list_new
        <set>

            <if test="title != null and title != ''">
                title = #{title},
            </if>

            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser},
            </if>
            update_time = current_timestamp

        </set>

        where id = #{id}

    </update>

    <select id="selectEvent" resultMap="dnwxfilterMap">

        select title, product_id, topic, prjid
        from dnwx_filter_list_new

        <where>
            topic = #{topic}
            <choose>
                <when test="productId != null">

                    and product_id = #{productId}
                </when>
                <otherwise>
                    and item_id = #{itemId}
                </otherwise>
            </choose>

        </where>

    </select>

    <select id="batchSelectEvent" resultMap="dnwxfilterMap" parameterType="java.util.List">
        select title, item_id, product_id, topic, prjid
        from dnwx_filter_list_new

        where
            <foreach collection="list" item="item" separator="or">
                (
                topic = #{item.topic}
                <choose>
                    <when test="item.productId != null">
                        and product_id = #{item.productId}
                    </when>
                    <otherwise>
                        and item_id = #{item.itemId}
                    </otherwise>
                </choose>

                )
            </foreach>
    </select>
</mapper>