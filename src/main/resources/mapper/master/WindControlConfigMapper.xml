<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.master.WindControlConfigMapper">
    <resultMap id="BaseResultMap" type="com.wbgame.pojo.operate.WindControlConfig">
        <result column="appid" property="appid" jdbcType="INTEGER"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
        <result column="cha_id" property="cha_id" jdbcType="VARCHAR"/>
        <result column="prjid" property="prjid" jdbcType="VARCHAR"/>
        <result column="queryInvTime" property="queryInvTime" jdbcType="INTEGER"/>
        <result column="appNumMax" property="appNumMax" jdbcType="INTEGER"/>
        <result column="chargeLastValue" property="chargeLastValue" jdbcType="INTEGER"/>
        <result column="gyroLastValue" property="gyroLastValue" jdbcType="INTEGER"/>
        <result column="stationLastValue" property="stationLastValue" jdbcType="INTEGER"/>
        <result column="similarAppNum" property="similarAppNum" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        appid
        , cha_id, prjid, queryInvTime, appNumMax, chargeLastValue, gyroLastValue, stationLastValue,
    similarAppNum, wifiKeyList, pkgKeyList, pkgSelfList, brandList
    </sql>


    <insert id="insertSWindControlConfig" parameterType="com.wbgame.pojo.operate.WindControlConfig">
        insert into wind_control_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appid != null">
                appid,
            </if>
            <if test="cha_id != null and cha_id != ''">
                cha_id,
            </if>
            <if test="prjid != null and prjid != ''">
                prjid,
            </if>
            <if test="queryInvTime != null">
                queryInvTime,
            </if>
            <if test="appNumMax != null">
                appNumMax,
            </if>
            <if test="chargeLastValue != null">
                chargeLastValue,
            </if>
            <if test="gyroLastValue != null">
                gyroLastValue,
            </if>
            <if test="stationLastValue != null">
                stationLastValue,
            </if>
            <if test="similarAppNum != null">
                similarAppNum,
            </if>
            <if test="wifiKeyList != null and wifiKeyList != ''">
                wifiKeyList,
            </if>
            <if test="pkgKeyList != null and pkgKeyList != ''">
                pkgKeyList,
            </if>
            <if test="pkgSelfList != null and pkgSelfList != ''">
                pkgSelfList,
            </if>
            <if test="brandList != null and brandList != ''">
                brandList,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appid != null">
                #{appid,jdbcType=INTEGER},
            </if>
            <if test="cha_id != null and cha_id != ''">
                #{cha_id,jdbcType=VARCHAR},
            </if>
            <if test="prjid != null and prjid != ''">
                #{prjid,jdbcType=VARCHAR},
            </if>
            <if test="queryInvTime != null">
                #{queryInvTime,jdbcType=INTEGER},
            </if>
            <if test="appNumMax != null">
                #{appNumMax,jdbcType=INTEGER},
            </if>
            <if test="chargeLastValue != null">
                #{chargeLastValue,jdbcType=INTEGER},
            </if>
            <if test="gyroLastValue != null">
                #{gyroLastValue,jdbcType=INTEGER},
            </if>
            <if test="stationLastValue != null">
                #{stationLastValue,jdbcType=INTEGER},
            </if>
            <if test="similarAppNum != null">
                #{similarAppNum,jdbcType=INTEGER},
            </if>
            <if test="wifiKeyList != null and wifiKeyList != ''">
                #{wifiKeyList,jdbcType=LONGVARCHAR},
            </if>
            <if test="pkgKeyList != null and pkgKeyList != ''">
                #{pkgKeyList,jdbcType=LONGVARCHAR},
            </if>
            <if test="pkgSelfList != null and pkgSelfList != ''">
                #{pkgSelfList,jdbcType=LONGVARCHAR},
            </if>
            <if test="brandList != null and brandList != ''">
                #{brandList,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

    <select id="selectWindControlConfig" parameterType="com.wbgame.pojo.operate.WindControlConfigDTO" resultMap="BaseResultMap">

        select w.*, a.app_name from wind_control_config w left join app_info a on w.appid = a.id

        <where>
            <if test="appidList != null and appidList.size > 0">
                and appid in
                <foreach collection="appidList" item="appid" open="(" separator="," close=")">
                    #{appid}
                </foreach>
            </if>
            <if test="channelList != null and channelList.size > 0">
                and cha_id in
                <foreach collection="channelList" item="cha" open="(" separator="," close=")">
                    #{cha}
                </foreach>
            </if>
            <if test="prjid != null and prjid != ''">

                and prjid = #{prjid}
            </if>

        </where>
    </select>

    <select id="selectAppIdOrChaExist" resultMap="BaseResultMap"
            parameterType="com.wbgame.pojo.operate.WindControlConfigDTO">


        select <include refid="Base_Column_List"/>
        from wind_control_config
        <where>

            <if test="appid != null and appid != ''">
                and appid = #{appid}
            </if>

            <choose>
                <when test="cha_id != null and cha_id != ''">
                    and cha_id = #{cha_id}
                </when>
                <when test="prjid != null and prjid != ''">
                    and prjid = #{prjid}
                </when>
            </choose>
        </where>

    </select>
</mapper>