<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.usertag.UsertagMapper">

	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>

	<!-- <select id="selectDnAdshowActRange" parameterType="java.lang.String" resultType="java.util.Map">
		select aa.*,bb.actnum from 
		
		(SELECT appid,channel,prjid,'video' adpos_type,COUNT(DISTINCT muid) aduser,
			COUNT(DISTINCT CASE WHEN video_show_times = 0 THEN muid END) pv0,
			COUNT(DISTINCT CASE WHEN video_show_times = 1 THEN muid END) pv1,
			COUNT(DISTINCT CASE WHEN video_show_times = 2 THEN muid END) pv2,
			COUNT(DISTINCT CASE WHEN video_show_times = 3 THEN muid END) pv3,
			COUNT(DISTINCT CASE WHEN video_show_times = 4 THEN muid END) pv4,
			COUNT(DISTINCT CASE WHEN video_show_times = 5 THEN muid END) pv5,
			COUNT(DISTINCT CASE WHEN video_show_times = 6 THEN muid END) pv6,
			COUNT(DISTINCT CASE WHEN video_show_times = 7 THEN muid END) pv7,
			COUNT(DISTINCT CASE WHEN video_show_times = 8 THEN muid END) pv8,
			COUNT(DISTINCT CASE WHEN video_show_times = 9 THEN muid END) pv9,
			COUNT(DISTINCT CASE WHEN video_show_times = 10 THEN muid END) pv10,
			COUNT(DISTINCT CASE WHEN video_show_times >= 11 and video_show_times &lt;= 15 THEN muid END) pv11,
			COUNT(DISTINCT CASE WHEN video_show_times >= 16 and video_show_times &lt;= 20 THEN muid END) pv12,
			COUNT(DISTINCT CASE WHEN video_show_times >= 21 and video_show_times &lt;= 25 THEN muid END) pv13,
			COUNT(DISTINCT CASE WHEN video_show_times > 25 THEN muid END) pv14
		FROM `daily_ad_record${tdate}` GROUP BY appid,channel
		
		UNION ALL
		
		SELECT appid,channel,prjid,'plaque' adpos_type,COUNT(DISTINCT muid) aduser,
			COUNT(DISTINCT CASE WHEN plaque_show_times = 0 THEN muid END) pv0,
			COUNT(DISTINCT CASE WHEN plaque_show_times = 1 THEN muid END) pv1,
			COUNT(DISTINCT CASE WHEN plaque_show_times = 2 THEN muid END) pv2,
			COUNT(DISTINCT CASE WHEN plaque_show_times = 3 THEN muid END) pv3,
			COUNT(DISTINCT CASE WHEN plaque_show_times = 4 THEN muid END) pv4,
			COUNT(DISTINCT CASE WHEN plaque_show_times = 5 THEN muid END) pv5,
			COUNT(DISTINCT CASE WHEN plaque_show_times = 6 THEN muid END) pv6,
			COUNT(DISTINCT CASE WHEN plaque_show_times = 7 THEN muid END) pv7,
			COUNT(DISTINCT CASE WHEN plaque_show_times = 8 THEN muid END) pv8,
			COUNT(DISTINCT CASE WHEN plaque_show_times = 9 THEN muid END) pv9,
			COUNT(DISTINCT CASE WHEN plaque_show_times = 10 THEN muid END) pv10,
			COUNT(DISTINCT CASE WHEN plaque_show_times >= 11 and plaque_show_times &lt;= 15 THEN muid END) pv11,
			COUNT(DISTINCT CASE WHEN plaque_show_times >= 16 and plaque_show_times &lt;= 20 THEN muid END) pv12,
			COUNT(DISTINCT CASE WHEN plaque_show_times >= 21 and plaque_show_times &lt;= 25 THEN muid END) pv13,
			COUNT(DISTINCT CASE WHEN plaque_show_times > 25 THEN muid END) pv14
		FROM `daily_ad_record${tdate}` GROUP BY appid,channel
		
		UNION ALL
		
		SELECT appid,channel,prjid,'plaqueVideo' adpos_type,COUNT(DISTINCT muid) aduser,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times = 0 THEN muid END) pv0,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times = 1 THEN muid END) pv1,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times = 2 THEN muid END) pv2,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times = 3 THEN muid END) pv3,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times = 4 THEN muid END) pv4,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times = 5 THEN muid END) pv5,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times = 6 THEN muid END) pv6,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times = 7 THEN muid END) pv7,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times = 8 THEN muid END) pv8,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times = 9 THEN muid END) pv9,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times = 10 THEN muid END) pv10,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times >= 11 and plaque_video_show_times &lt;= 15 THEN muid END) pv11,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times >= 16 and plaque_video_show_times &lt;= 20 THEN muid END) pv12,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times >= 21 and plaque_video_show_times &lt;= 25 THEN muid END) pv13,
			COUNT(DISTINCT CASE WHEN plaque_video_show_times > 25 THEN muid END) pv14
		FROM `daily_ad_record${tdate}` GROUP BY appid,channel
		
		UNION ALL
		
		SELECT appid,channel,prjid,'msg' adpos_type,COUNT(DISTINCT muid) aduser,
			COUNT(DISTINCT CASE WHEN info_flow_show_times = 0 THEN muid END) pv0,
			COUNT(DISTINCT CASE WHEN info_flow_show_times = 1 THEN muid END) pv1,
			COUNT(DISTINCT CASE WHEN info_flow_show_times = 2 THEN muid END) pv2,
			COUNT(DISTINCT CASE WHEN info_flow_show_times = 3 THEN muid END) pv3,
			COUNT(DISTINCT CASE WHEN info_flow_show_times = 4 THEN muid END) pv4,
			COUNT(DISTINCT CASE WHEN info_flow_show_times = 5 THEN muid END) pv5,
			COUNT(DISTINCT CASE WHEN info_flow_show_times = 6 THEN muid END) pv6,
			COUNT(DISTINCT CASE WHEN info_flow_show_times = 7 THEN muid END) pv7,
			COUNT(DISTINCT CASE WHEN info_flow_show_times = 8 THEN muid END) pv8,
			COUNT(DISTINCT CASE WHEN info_flow_show_times = 9 THEN muid END) pv9,
			COUNT(DISTINCT CASE WHEN info_flow_show_times = 10 THEN muid END) pv10,
			COUNT(DISTINCT CASE WHEN info_flow_show_times >= 11 and info_flow_show_times &lt;= 15 THEN muid END) pv11,
			COUNT(DISTINCT CASE WHEN info_flow_show_times >= 16 and info_flow_show_times &lt;= 20 THEN muid END) pv12,
			COUNT(DISTINCT CASE WHEN info_flow_show_times >= 21 and info_flow_show_times &lt;= 25 THEN muid END) pv13,
			COUNT(DISTINCT CASE WHEN info_flow_show_times > 25 THEN muid END) pv14
		FROM `daily_ad_record${tdate}` GROUP BY appid,channel

		UNION ALL

		SELECT appid,channel,prjid,'splash' adpos_type,COUNT(DISTINCT muid) aduser,
			COUNT(DISTINCT CASE WHEN splash_show_times = 0 THEN muid END) pv0,
			COUNT(DISTINCT CASE WHEN splash_show_times = 1 THEN muid END) pv1,
			COUNT(DISTINCT CASE WHEN splash_show_times = 2 THEN muid END) pv2,
			COUNT(DISTINCT CASE WHEN splash_show_times = 3 THEN muid END) pv3,
			COUNT(DISTINCT CASE WHEN splash_show_times = 4 THEN muid END) pv4,
			COUNT(DISTINCT CASE WHEN splash_show_times = 5 THEN muid END) pv5,
			COUNT(DISTINCT CASE WHEN splash_show_times = 6 THEN muid END) pv6,
			COUNT(DISTINCT CASE WHEN splash_show_times = 7 THEN muid END) pv7,
			COUNT(DISTINCT CASE WHEN splash_show_times = 8 THEN muid END) pv8,
			COUNT(DISTINCT CASE WHEN splash_show_times = 9 THEN muid END) pv9,
			COUNT(DISTINCT CASE WHEN splash_show_times = 10 THEN muid END) pv10,
			COUNT(DISTINCT CASE WHEN splash_show_times >= 11 and splash_show_times &lt;= 15 THEN muid END) pv11,
			COUNT(DISTINCT CASE WHEN splash_show_times >= 16 and splash_show_times &lt;= 20 THEN muid END) pv12,
			COUNT(DISTINCT CASE WHEN splash_show_times >= 21 and splash_show_times &lt;= 25 THEN muid END) pv13,
			COUNT(DISTINCT CASE WHEN splash_show_times > 25 THEN muid END) pv14
		FROM `daily_ad_record${tdate}` GROUP BY appid,channel

		UNION ALL

		SELECT appid,channel,prjid,'banner' adpos_type,COUNT(DISTINCT muid) aduser,
			COUNT(DISTINCT CASE WHEN banner_show_times = 0 THEN muid END) pv0,
			COUNT(DISTINCT CASE WHEN banner_show_times = 1 THEN muid END) pv1,
			COUNT(DISTINCT CASE WHEN banner_show_times = 2 THEN muid END) pv2,
			COUNT(DISTINCT CASE WHEN banner_show_times = 3 THEN muid END) pv3,
			COUNT(DISTINCT CASE WHEN banner_show_times = 4 THEN muid END) pv4,
			COUNT(DISTINCT CASE WHEN banner_show_times = 5 THEN muid END) pv5,
			COUNT(DISTINCT CASE WHEN banner_show_times = 6 THEN muid END) pv6,
			COUNT(DISTINCT CASE WHEN banner_show_times = 7 THEN muid END) pv7,
			COUNT(DISTINCT CASE WHEN banner_show_times = 8 THEN muid END) pv8,
			COUNT(DISTINCT CASE WHEN banner_show_times = 9 THEN muid END) pv9,
			COUNT(DISTINCT CASE WHEN banner_show_times = 10 THEN muid END) pv10,
			COUNT(DISTINCT CASE WHEN banner_show_times >= 11 and banner_show_times &lt;= 15 THEN muid END) pv11,
			COUNT(DISTINCT CASE WHEN banner_show_times >= 16 and banner_show_times &lt;= 20 THEN muid END) pv12,
			COUNT(DISTINCT CASE WHEN banner_show_times >= 21 and banner_show_times &lt;= 25 THEN muid END) pv13,
			COUNT(DISTINCT CASE WHEN banner_show_times > 25 THEN muid END) pv14
		FROM `daily_ad_record${tdate}` GROUP BY appid,channel

		) aa

		LEFT JOIN 
		
		(SELECT appid,channel,prjid,COUNT(DISTINCT muid) actnum 
			FROM `daily_user_portrait${tdate}` GROUP BY appid,channel) bb
		
		ON aa.appid=bb.appid AND aa.channel=bb.channel
	</select> -->
	
	<!-- 活跃用户展示频次、价值分析、ECPM分析 -->
	<select id="selectDnAdshowActRange" parameterType="java.lang.String" resultType="java.util.Map">
		
		SELECT tdate,appid,'0' prjid,download_channel channel,ad_type adpos_type,dau actnum,ad_active_user aduser, 
			ad_pv_active_user_pv_0 pv0,
			ad_pv_active_user_pv_1 pv1,
			ad_pv_active_user_pv_2 pv2,
			ad_pv_active_user_pv_3 pv3,
			ad_pv_active_user_pv_4 pv4,
			ad_pv_active_user_pv_5 pv5,
			ad_pv_active_user_pv_6 pv6,
			ad_pv_active_user_pv_7 pv7,
			ad_pv_active_user_pv_8 pv8,
			ad_pv_active_user_pv_9 pv9,
			ad_pv_active_user_pv_10 pv10,
			ad_pv_active_user_pv_11_15 pv11,
			ad_pv_active_user_pv_16_20 pv12,
			ad_pv_active_user_pv_21_25 pv13,
			ad_pv_active_user_pv_gt_25 pv14
		FROM dnwx_bi.ads_active_user_revenue_ecpm_pv_analyze_daily 
		where tdate='${tdate}' and active_type='活跃' 
		
	</select>
	
	<select id="selectDnAdRevenueActRange" parameterType="java.lang.String" resultType="java.util.Map">
		
		SELECT tdate,appid,download_channel cha_id,'0' adsid,dau actnum,reg_user_cnt addnum,ad_type adpos_type,'0' ecpm,
			ad_active_user_revenue_pv_1/100 pv1,
			ad_active_user_revenue_pv_2/100 pv2,
			ad_active_user_revenue_pv_3/100 pv3,
			ad_active_user_revenue_pv_4/100 pv4,
			ad_active_user_revenue_pv_5/100 pv5,
			ad_active_user_revenue_pv_6/100 pv6,
			ad_active_user_revenue_pv_7/100 pv7,
			ad_active_user_revenue_pv_8/100 pv8,
			ad_active_user_revenue_pv_9/100 pv9,
			ad_active_user_revenue_pv_10/100 pv10,
			ad_active_user_revenue_pv_11_15/100 pv11,
			ad_active_user_revenue_pv_16_20/100 pv12,
			ad_active_user_revenue_pv_21_25/100 pv13,
			ad_active_user_revenue_pv_gt_25/100 pv14
		FROM dnwx_bi.ads_active_user_revenue_ecpm_pv_analyze_daily 
		where tdate='${tdate}' and active_type='活跃' 
		
	</select>
	
	<select id="selectDnAdEcpmActRange" parameterType="java.lang.String" resultType="java.util.Map">
		
		SELECT tdate,appid,download_channel cha_id,'0' adsid,dau actnum,reg_user_cnt addnum,ad_type adpos_type,'0' ecpm,
			ad_active_user_pv_1 pv1,
			ad_active_user_pv_2 pv2,
			ad_active_user_pv_3 pv3,
			ad_active_user_pv_4 pv4,
			ad_active_user_pv_5 pv5,
			ad_active_user_pv_6 pv6,
			ad_active_user_pv_7 pv7,
			ad_active_user_pv_8 pv8,
			ad_active_user_pv_9 pv9,
			ad_active_user_pv_10 pv10,
			ad_active_user_pv_11_15 pv11,
			ad_active_user_pv_16_20 pv12,
			ad_active_user_pv_21_25 pv13,
			ad_active_user_pv_gt_25 pv14,
		
			ad_active_user_revenue_pv_1/100 ar1,
			ad_active_user_revenue_pv_2/100 ar2,
			ad_active_user_revenue_pv_3/100 ar3,
			ad_active_user_revenue_pv_4/100 ar4,
			ad_active_user_revenue_pv_5/100 ar5,
			ad_active_user_revenue_pv_6/100 ar6,
			ad_active_user_revenue_pv_7/100 ar7,
			ad_active_user_revenue_pv_8/100 ar8,
			ad_active_user_revenue_pv_9/100 ar9,
			ad_active_user_revenue_pv_10/100 ar10,
			ad_active_user_revenue_pv_11_15/100 ar11,
			ad_active_user_revenue_pv_16_20/100 ar12,
			ad_active_user_revenue_pv_21_25/100 ar13,
			ad_active_user_revenue_pv_gt_25/100 ar14
		
		FROM dnwx_bi.ads_active_user_revenue_ecpm_pv_analyze_daily 
		where tdate='${tdate}' and active_type='活跃' 
		
	</select>
	
	<!-- 新增用户展示频次、价值分析、ECPM分析 -->
	<select id="selectDnAdshowAddRange" parameterType="java.lang.String" resultType="java.util.Map">
		
		SELECT tdate,appid,'0' prjid,download_channel channel,reg_user_cnt addnum,'0' adsid,ad_type adpos_type,
			ad_pv_1 pv1,
			ad_pv_2 pv2,
			ad_pv_3 pv3,
			ad_pv_4 pv4,
			ad_pv_5 pv5,
			ad_pv_6 pv6,
			ad_pv_7 pv7
		
		FROM dnwx_bi.ads_reg_user_ltv_ecpm_pv_analyze_daily 
		where tdate='${tdate}' 
		
	</select>
	
	<select id="selectDnAdRevenueAddRange" parameterType="java.lang.String" resultType="java.util.Map">
		
		SELECT tdate,appid,download_channel cha_id,'0' adsid,ad_type adpos_type,reg_user_cnt addnum,'0' ecpm,
			add_revenue_arpu_1/100 ar1,
			add_revenue_arpu_2/100 ar2,
			add_revenue_arpu_3/100 ar3,
			add_revenue_arpu_4/100 ar4,
			add_revenue_arpu_5/100 ar5,
			add_revenue_arpu_6/100 ar6,
			add_revenue_arpu_7/100 ar7
		
		FROM dnwx_bi.ads_reg_user_ltv_ecpm_pv_analyze_daily 
		where tdate='${tdate}' 
		
	</select>
	
	<select id="selectDnAdEcpmAddRange" parameterType="java.lang.String" resultType="java.util.Map">
		
		SELECT tdate,appid,download_channel cha_id,'0' adsid,ad_type adpos_type,reg_user_cnt addnum,'0' ecpm,
			ad_pv_1 pv1,
			ad_pv_2 pv2,
			ad_pv_3 pv3,
			ad_pv_4 pv4,
			ad_pv_5 pv5,
			ad_pv_6 pv6,
			ad_pv_7 pv7,
			add_revenue_arpu_1/100 ar1,
			add_revenue_arpu_2/100 ar2,
			add_revenue_arpu_3/100 ar3,
			add_revenue_arpu_4/100 ar4,
			add_revenue_arpu_5/100 ar5,
			add_revenue_arpu_6/100 ar6,
			add_revenue_arpu_7/100 ar7

		FROM dnwx_bi.ads_reg_user_ltv_ecpm_pv_analyze_daily 
		where tdate='${tdate}' 
		
	</select>
	
		
	<!-- 查询快手重报数据 -->
	<select id="selectKsReportData" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam" resultType="com.wbgame.pojo.jettison.KsReportDataDto">
		SELECT
		appid,
		<if test='group.contains("day")'>
			tdate day,
		</if>
		<if test='group.contains("accountId")'>
			accountid accountId,
		</if>
		  sum(REPLACE(REPLACE(SUBSTR(url ,-5),'=',''),'t','')) money FROM ks_pay_request_record  where tdate BETWEEN #{beginDate} AND #{endDate} 
		<if test="appId != null and appId != ''">
			and appid in (${appId}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and accountid in (${accountId}) 
		</if>
		group by appid
		<if test='group.contains("day")'>
			,day
		</if>
		<if test='group.contains("accountId")'>
			,accountId
		</if>
	</select>
		<!-- 微信小游戏ROI报表列表查询  -->
  	<select id="wechatGameRoiReport" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam" resultType="com.wbgame.pojo.jettison.AdRoiReportDTO">
		<include refid="wx_game_roi_sql"/>
	</select>
	<!-- 微信小游戏ROI报表汇总  -->
	<select id="wechatGameRoiReportTotal" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam"  resultType="com.wbgame.pojo.jettison.AdRoiReportDTO">
		SELECT tdate as day,appid as appId ,
			sum(reg_user_cnt) as addUser,sum(active_user_cnt) as activeUser,round(sum(reg_user_iap_revenue),2) as  ipaIncome,
			round(sum(iap_revenue_2),2) as ltv2,round(sum(iap_revenue_3),2) as ltv3,round(sum(iap_revenue_4),2) as ltv4,round(sum(iap_revenue_5),2) as ltv5,
			round(sum(iap_revenue_6),2) as ltv6,round(sum(iap_revenue_7),2) as ltv7,round(sum(iap_revenue_14),2) as ltv14,round(sum(iap_revenue_30),2) as ltv30,
			round(sum(iap_revenue_8),2) as ltv8,round(sum(iap_revenue_9),2) as ltv9,round(sum(iap_revenue_10),2) as ltv10,round(sum(iap_revenue_11),2) as ltv11,
			round(sum(iap_revenue_12),2) as ltv12,round(sum(iap_revenue_13),2) as ltv13,round(sum(iap_revenue_15),2) as ltv15,round(sum(iap_revenue_16),2) as ltv16,
			round(sum(iap_revenue_17),2) as ltv17,round(sum(iap_revenue_18),2) as ltv18,round(sum(iap_revenue_19),2) as ltv19,round(sum(iap_revenue_20),2) as ltv20,
			round(sum(iap_revenue_21),2) as ltv21,round(sum(iap_revenue_22),2) as ltv22,round(sum(iap_revenue_23),2) as ltv23,round(sum(iap_revenue_24),2) as ltv24,
			round(sum(iap_revenue_25),2) as ltv25,round(sum(iap_revenue_26),2) as ltv26,round(sum(iap_revenue_27),2) as ltv27,round(sum(iap_revenue_28),2) as ltv28,
			round(sum(iap_revenue_29),2) as ltv29,round(sum(iap_revenue_36),2) as ltv36,round(sum(iap_revenue_42),2) as ltv42,round(sum(iap_revenue_48),2) as ltv48,round(sum(iap_revenue_54),2) as ltv54,
			round(sum(iap_revenue_60),2) as ltv60,round(sum(rebate_cost),2) as spend,CONCAT(round(sum(reg_user_iap_revenue)/sum(rebate_cost)*100,2),'%') as aRoi,
			CONCAT(round(sum(iap_revenue_2)/sum(rebate_cost)*100,2),'%') as roi2,CONCAT(round(sum(iap_revenue_3)/sum(rebate_cost)*100,2),'%') as roi3,CONCAT(round(sum(iap_revenue_4)/sum(rebate_cost)*100,2),'%') as roi4,CONCAT(round(sum(iap_revenue_5)/sum(rebate_cost)*100,2),'%') as roi5,
			CONCAT(round(sum(iap_revenue_6)/sum(rebate_cost)*100,2),'%') as roi6,CONCAT(round(sum(iap_revenue_7)/sum(rebate_cost)*100,2),'%') as roi7,CONCAT(round(sum(iap_revenue_14)/sum(rebate_cost)*100,2),'%') as roi14,CONCAT(round(sum(iap_revenue_30)/sum(rebate_cost)*100,2),'%') as roi30,
			CONCAT(round(sum(iap_revenue_8)/sum(rebate_cost)*100,2),'%') as roi8,CONCAT(round(sum(iap_revenue_9)/sum(rebate_cost)*100,2),'%') as roi9,CONCAT(round(sum(iap_revenue_10)/sum(rebate_cost)*100,2),'%') as roi10,CONCAT(round(sum(iap_revenue_11)/sum(rebate_cost)*100,2),'%') as roi11,
			CONCAT(round(sum(iap_revenue_12)/sum(rebate_cost)*100,2),'%') as roi12,CONCAT(round(sum(iap_revenue_13)/sum(rebate_cost)*100,2),'%') as roi13,CONCAT(round(sum(iap_revenue_15)/sum(rebate_cost)*100,2),'%') as roi15,CONCAT(round(sum(iap_revenue_16)/sum(rebate_cost)*100,2),'%') as roi16,
			CONCAT(round(sum(iap_revenue_17)/sum(rebate_cost)*100,2),'%') as roi17,CONCAT(round(sum(iap_revenue_18)/sum(rebate_cost)*100,2),'%') as roi18,CONCAT(round(sum(iap_revenue_19)/sum(rebate_cost)*100,2),'%') as roi19,CONCAT(round(sum(iap_revenue_20)/sum(rebate_cost)*100,2),'%') as roi20,
			CONCAT(round(sum(iap_revenue_21)/sum(rebate_cost)*100,2),'%') as roi21,CONCAT(round(sum(iap_revenue_22)/sum(rebate_cost)*100,2),'%') as roi22,CONCAT(round(sum(iap_revenue_23)/sum(rebate_cost)*100,2),'%') as roi23,CONCAT(round(sum(iap_revenue_24)/sum(rebate_cost)*100,2),'%') as roi24,
			CONCAT(round(sum(iap_revenue_25)/sum(rebate_cost)*100,2),'%') as roi25,CONCAT(round(sum(iap_revenue_26)/sum(rebate_cost)*100,2),'%') as roi26,CONCAT(round(sum(iap_revenue_27)/sum(rebate_cost)*100,2),'%') as roi27,CONCAT(round(sum(iap_revenue_28)/sum(rebate_cost)*100,2),'%') as roi28,
			CONCAT(round(sum(iap_revenue_29)/sum(rebate_cost)*100,2),'%') as roi29,CONCAT(round(sum(iap_revenue_36)/sum(rebate_cost)*100,2),'%') as roi36,CONCAT(round(sum(iap_revenue_42)/sum(rebate_cost)*100,2),'%') as roi42,CONCAT(round(sum(iap_revenue_48)/sum(rebate_cost)*100,2),'%') as roi48,
			CONCAT(round(sum(iap_revenue_54)/sum(rebate_cost)*100,2),'%') as roi54,CONCAT(round(sum(iap_revenue_60)/sum(rebate_cost)*100,2),'%') as roi60,
			sum(iap_reg_user_cnt) as addPurchaseUsers,sum(iap_active_user_cnt) as activePurchaseUsers
			from ads_wechat_game_roi
		where tdate BETWEEN #{beginDate} AND #{endDate} 
		<if test="appId != null and appId != ''">
			and appid in (${appId}) 
		</if>
	</select>
		<sql id="wx_game_roi_sql">
		SELECT tdate as day,appid as appId ,
			sum(reg_user_cnt) as addUser,sum(active_user_cnt) as activeUser,round(sum(reg_user_iap_revenue),2) as  ipaIncome,
			round(sum(iap_revenue_2),2) as ltv2,round(sum(iap_revenue_3),2) as ltv3,round(sum(iap_revenue_4),2) as ltv4,round(sum(iap_revenue_5),2) as ltv5,
			round(sum(iap_revenue_6),2) as ltv6,round(sum(iap_revenue_7),2) as ltv7,round(sum(iap_revenue_14),2) as ltv14,round(sum(iap_revenue_30),2) as ltv30,
			round(sum(iap_revenue_8),2) as ltv8,round(sum(iap_revenue_9),2) as ltv9,round(sum(iap_revenue_10),2) as ltv10,round(sum(iap_revenue_11),2) as ltv11,
			round(sum(iap_revenue_12),2) as ltv12,round(sum(iap_revenue_13),2) as ltv13,round(sum(iap_revenue_15),2) as ltv15,round(sum(iap_revenue_16),2) as ltv16,
			round(sum(iap_revenue_17),2) as ltv17,round(sum(iap_revenue_18),2) as ltv18,round(sum(iap_revenue_19),2) as ltv19,round(sum(iap_revenue_20),2) as ltv20,
			round(sum(iap_revenue_21),2) as ltv21,round(sum(iap_revenue_22),2) as ltv22,round(sum(iap_revenue_23),2) as ltv23,round(sum(iap_revenue_24),2) as ltv24,
			round(sum(iap_revenue_25),2) as ltv25,round(sum(iap_revenue_26),2) as ltv26,round(sum(iap_revenue_27),2) as ltv27,round(sum(iap_revenue_28),2) as ltv28,
			round(sum(iap_revenue_29),2) as ltv29,round(sum(iap_revenue_36),2) as ltv36,round(sum(iap_revenue_42),2) as ltv42,round(sum(iap_revenue_48),2) as ltv48,round(sum(iap_revenue_54),2) as ltv54,
			round(sum(iap_revenue_60),2) as ltv60,round(sum(rebate_cost),2) as spend,CONCAT(round(sum(reg_user_iap_revenue)/sum(rebate_cost)*100,2),'%') as aRoi,
			CONCAT(round(sum(iap_revenue_2)/sum(rebate_cost)*100,2),'%') as roi2,CONCAT(round(sum(iap_revenue_3)/sum(rebate_cost)*100,2),'%') as roi3,CONCAT(round(sum(iap_revenue_4)/sum(rebate_cost)*100,2),'%') as roi4,CONCAT(round(sum(iap_revenue_5)/sum(rebate_cost)*100,2),'%') as roi5,
			CONCAT(round(sum(iap_revenue_6)/sum(rebate_cost)*100,2),'%') as roi6,CONCAT(round(sum(iap_revenue_7)/sum(rebate_cost)*100,2),'%') as roi7,CONCAT(round(sum(iap_revenue_14)/sum(rebate_cost)*100,2),'%') as roi14,CONCAT(round(sum(iap_revenue_30)/sum(rebate_cost)*100,2),'%') as roi30,
			CONCAT(round(sum(iap_revenue_8)/sum(rebate_cost)*100,2),'%') as roi8,CONCAT(round(sum(iap_revenue_9)/sum(rebate_cost)*100,2),'%') as roi9,CONCAT(round(sum(iap_revenue_10)/sum(rebate_cost)*100,2),'%') as roi10,CONCAT(round(sum(iap_revenue_11)/sum(rebate_cost)*100,2),'%') as roi11,
			CONCAT(round(sum(iap_revenue_12)/sum(rebate_cost)*100,2),'%') as roi12,CONCAT(round(sum(iap_revenue_13)/sum(rebate_cost)*100,2),'%') as roi13,CONCAT(round(sum(iap_revenue_15)/sum(rebate_cost)*100,2),'%') as roi15,CONCAT(round(sum(iap_revenue_16)/sum(rebate_cost)*100,2),'%') as roi16,
			CONCAT(round(sum(iap_revenue_17)/sum(rebate_cost)*100,2),'%') as roi17,CONCAT(round(sum(iap_revenue_18)/sum(rebate_cost)*100,2),'%') as roi18,CONCAT(round(sum(iap_revenue_19)/sum(rebate_cost)*100,2),'%') as roi19,CONCAT(round(sum(iap_revenue_20)/sum(rebate_cost)*100,2),'%') as roi20,
			CONCAT(round(sum(iap_revenue_21)/sum(rebate_cost)*100,2),'%') as roi21,CONCAT(round(sum(iap_revenue_22)/sum(rebate_cost)*100,2),'%') as roi22,CONCAT(round(sum(iap_revenue_23)/sum(rebate_cost)*100,2),'%') as roi23,CONCAT(round(sum(iap_revenue_24)/sum(rebate_cost)*100,2),'%') as roi24,
			CONCAT(round(sum(iap_revenue_25)/sum(rebate_cost)*100,2),'%') as roi25,CONCAT(round(sum(iap_revenue_26)/sum(rebate_cost)*100,2),'%') as roi26,CONCAT(round(sum(iap_revenue_27)/sum(rebate_cost)*100,2),'%') as roi27,CONCAT(round(sum(iap_revenue_28)/sum(rebate_cost)*100,2),'%') as roi28,
			CONCAT(round(sum(iap_revenue_29)/sum(rebate_cost)*100,2),'%') as roi29,CONCAT(round(sum(iap_revenue_36)/sum(rebate_cost)*100,2),'%') as roi36,CONCAT(round(sum(iap_revenue_42)/sum(rebate_cost)*100,2),'%') as roi42,CONCAT(round(sum(iap_revenue_48)/sum(rebate_cost)*100,2),'%') as roi48,
			CONCAT(round(sum(iap_revenue_54)/sum(rebate_cost)*100,2),'%') as roi54,CONCAT(round(sum(iap_revenue_60)/sum(rebate_cost)*100,2),'%') as roi60,
			sum(iap_reg_user_cnt) as addPurchaseUsers,sum(iap_active_user_cnt) as activePurchaseUsers
			from ads_wechat_game_roi
		where tdate BETWEEN #{beginDate} AND #{endDate} 
		<if test="appId != null and appId != ''">
			and appid in (${appId}) 
		</if>
		group by appid,tdate order by day asc
	</sql>
</mapper>