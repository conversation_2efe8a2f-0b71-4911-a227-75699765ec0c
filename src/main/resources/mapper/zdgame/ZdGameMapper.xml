<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.zdgame.ZdGameMapper">

    <select id="getMailList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.config.MailVo">
        select * from zhongdu.mail_detail_info where  1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="pid != null and pid != ''">
            and pid = #{pid}
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        <if test="server_type != null and server_type != ''">
            and server_type = #{server_type}
        </if>
        <if test="loginId != null and loginId != ''">
            and loginId = #{loginId}
        </if>
        <if test="mailType != null and mailType != ''">
            and mailType = #{mailType}
        </if>
        <if test="title != null and title != ''">
            and title like '%${title}%'
        </if>
        <if test="msg != null and msg != ''">
            and msg like '%${msg}%'
        </if>
        <if test="id != null and id != ''">
            and id = #{id}
        </if>
        <if test="state != null and state != ''">
            and state = #{state}
        </if>
        <if test="create_user != null and create_user != ''">
            and create_user like '%${create_user}%'
        </if>
        and DATE(create_time) <![CDATA[>=]]> #{start_date} and DATE(create_time) <![CDATA[<=]]> #{end_date}
    </select>

    <select id="getMailById" parameterType="java.lang.String" resultType="com.wbgame.pojo.game.config.MailVo">
        select * from zhongdu.mail_detail_info where id = #{id}
    </select>


    <insert id="saveMail" parameterType="com.wbgame.pojo.game.config.MailVo">
        insert into zhongdu.mail_detail_info (mailId,mailType,title,msg,img,appid,pid,server_type,channel,loginId,start_date,end_date,
        item,state,new_effect,version_effect,receive_type,create_user,create_time) value
        (#{mailId},#{mailType},#{title},#{msg},#{img},#{appid},#{pid},#{server_type},#{channel},#{loginId},#{start_date},#{end_date},
        #{item},#{state},#{new_effect},#{version_effect},#{receive_type},#{create_user},now())
    </insert>

    <update id="updateMail" parameterType="com.wbgame.pojo.game.config.MailVo">
        update zhongdu.mail_detail_info
        set mailType =#{mailType},title =#{title},msg =#{msg},img =#{img},appid =#{appid},
        loginId =#{loginId},start_date =#{start_date},pid = #{pid},new_effect = #{new_effect},
        version_effect =#{version_effect},server_type = #{server_type},channel =#{channel},
        end_date =#{end_date},item =#{item},receive_type = #{receive_type},modify_user =#{modify_user},modify_time =now()
        where id = #{id}
    </update>

    <update id="updateMailSendState" parameterType="com.wbgame.pojo.game.config.MailVo">
         update zhongdu.mail_detail_info  set state =#{state},modify_user =#{modify_user},modify_time =now()
         where id = #{id}
    </update>

    <delete id="delMail" parameterType="com.wbgame.pojo.game.config.MailVo">
        delete from zhongdu.mail_detail_info where  id in (${id})
    </delete>

    <select id="getGameTableConfigList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.config.GameTableVo">
        select * from zhongdu.dnwx_medium_game where 1=1
        <if test="appid != null and appid != ''">
            and appid = #{appid}
        </if>
    </select>

    <select id="getLoginLogList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.userinfo.LoginVo">
        select a.*,b.create_time
        <if test=" device_type =='android' ">
            ,a.aid as device_id
        </if>

        <if test=" device_type =='ios' ">
            ,a.idfa as device_id
        </if>
        from  zhongdu.${login_log_table_name} a inner join zhongdu.${user_info_table_name} b on a.loginId = b.loginId
        where a.loginId = #{loginId}

        <if test="device_id != null and device_id != ''">
            and (a.aid = #{device_id} or a.idfa = #{device_id})
        </if>

        <if test=" device_type =='android' ">
            and a.aid !='' and a.aid is not null
        </if>

        <if test=" device_type =='ios' ">
            and a.idfa !='' and a.idfa is not null
        </if>
        order by last_time desc
    </select>

    <select id="getForbiddenList" parameterType="java.util.Map" resultType="com.wbgame.pojo.game.config.ForbiddenVo">
        SELECT * from zhongdu.game_forbidden_info where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="loginId != null and loginId != ''">
            and loginId = #{loginId}
        </if>
        <if test="openid != null and openid != ''">
            and openid = #{openid}
        </if>
        <if test="androidid != null and androidid != ''">
            and androidid = #{androidid}
        </if>
        <if test="forbiddenType != null and forbiddenType != ''">
            and forbiddenType = #{forbiddenType}
        </if>
        <if test="server_type != null and server_type != ''">
            and server_type = #{server_type}
        </if>
        <if test="channel != null and channel != ''">
            and channel in (${channel})
        </if>
        order by create_time desc
    </select>

    <insert id="saveForbidden" parameterType="com.wbgame.pojo.game.config.ForbiddenVo">
        insert into zhongdu.game_forbidden_info (appid,accountType,server_type,channel,loginId,openid,androidid,forbiddenType,forbiddenTime,reason,create_time,create_user)
        values (#{appid},#{accountType},#{server_type},#{channel},#{loginId},#{openid},#{androidid},#{forbiddenType},#{forbiddenTime},#{reason},now(),#{create_user})
    </insert>

    <update id="updateForbidden" parameterType="com.wbgame.pojo.game.config.ForbiddenVo">
        update zhongdu.game_forbidden_info set appid = #{appid},server_type = #{server_type},
        loginId = #{loginId},forbiddenType =#{forbiddenType},channel = #{channel},
        forbiddenTime=#{forbiddenTime},reason = #{reason},modify_time = now(),modify_user = #{modify_user}
        where id =#{id}
    </update>

    <delete id="delForbidden" parameterType="com.wbgame.pojo.game.config.ForbiddenVo">
        delete from zhongdu.game_forbidden_info where id in (${id})
    </delete>

    <select id="getGameUserLoginId" parameterType="com.wbgame.pojo.game.report.GameUserInfoVo" resultType="com.wbgame.pojo.game.report.GameUserInfoVo">
        select * from ${database} where 1=1
        <if test="wxid != null and wxid != ''">
            and wxid = #{wxid}
        </if>
        <if test="aid != null and aid != ''">
            and aid = #{aid}
        </if>
        order by create_time desc limit 1
    </select>

    <!--白名单相关    -->
    <select id="getWhiteConfigList" parameterType="com.wbgame.pojo.game.config.WhiteListVo" resultType="com.wbgame.pojo.game.config.WhiteListVo">
        select * from  zhongdu.game_white_info where 1=1
        <if test="appid != null and appid != ''">
            and appid in (${appid})
        </if>
        <if test="server_type != null and server_type != ''">
            and server_type = #{server_type}
        </if>
        <if test="account_type != null and account_type != ''">
            and account_type = #{account_type}
        </if>
        <if test="account != null and account != ''">
            and account = #{account}
        </if>
        <if test="start_date != null and start_date != ''">
            and DATE(create_time) <![CDATA[ >= ]]> #{start_date} and DATE(create_time) <![CDATA[ <= ]]> #{end_date}
        </if>
        order by create_time desc
    </select>

    <insert id="saveWhiteConfig" parameterType="com.wbgame.pojo.game.config.WhiteListVo">
        replace into zhongdu.game_white_info (appid,server_type,account_type,account,create_user,create_time)
        values (#{appid},#{server_type},#{account_type},#{account},#{create_user},now())
    </insert>

    <update id="updateWhiteConfig" parameterType="com.wbgame.pojo.game.config.WhiteListVo">
        update zhongdu.game_white_info
            set appid = #{appid},server_type = #{server_type},account_type = #{account_type},account = #{account},
            modify_time = now(),modify_user = #{modify_user}
        where id = #{id}
    </update>

    <delete id="delWhiteConfig" parameterType="com.wbgame.pojo.game.config.WhiteListVo">
        delete from zhongdu.game_white_info where id in (${id})
    </delete>

    <insert id="batchSaveWhiteConfig" parameterType="java.util.List">
        replace into zhongdu.game_white_info (appid,server_type,account_type,account,create_user,create_time) values
        <foreach collection="list"  index="index" item="item" separator=",">
            (#{item.appid}, #{item.server_type},#{item.account_type},#{item.account},#{item.create_user},now())
        </foreach>
    </insert>
</mapper>