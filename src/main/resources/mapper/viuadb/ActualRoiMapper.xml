<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.viuadb.ActualRoiMapper">

	
	<!-- 代理商广告roi报表(当天数据)查询  -->
  	<select id="actualAdRoiReport" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam" resultType="com.wbgame.pojo.jettison.AdRoiDTO">
		<include refid="actula_roi_sql"/>
	</select>
	<!-- 代理商广告roi报表(当天数据)汇总查询  -->
	<select id="actualAdRoiReportTotal" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam"  resultType="com.wbgame.pojo.jettison.AdRoiDTO">
		select 
		day, appId,media , accountId, groupName,campaignName, campaignId,sum(addUser) as addUser,sum(dupAddUser) as dupAddUser,sum(addUserLtv0) as addUserLtv0,
		sum(spend) as spend,sum(impressions) as impressions,sum(clicks) as clicks,sum(installs) as installs,sum(`convert`) as 'convert',sum(register) as register,
		sum(videoLtv) as videoLtv,sum(plaqueLtv) AS plaqueLtv,sum(addIpa) as addIpa,sum(payCount) as payCount ,sum(gameAddiction) as gameAddiction
		from (<include refid="actula_roi_sql"/>) 
	</select>
	<sql id="actula_roi_sql">
		SELECT day,appid appId,media ,account_id accountId,group_name groupName,campaign_name campaignName,campaign_id campaignId,
		sum(add_user) as addUser,sum(dup_add_user) as dupAddUser,sum(add_user_ltv0)*100 as addUserLtv0,sum(spend)*100 as spend,
		sum(impressions) as impressions,sum(clicks) as clicks,sum(installs) as installs,sum(`convert`) as 'convert',sum(register) as register,sum(game_addiction) as gameAddiction,
		sum(video_ltv)*100 as videoLtv,sum(plaque_ltv)*100 AS plaqueLtv,sum(add_ipa)*100 as addIpa,sum(pay_count) as payCount from channel_realize_report_data_works
		where day BETWEEN #{beginDate} AND #{endDate} AND   (spend>0 or add_user>0) 
		<if test="appId != null and appId != ''">
			and appid in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and media in (${media}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account_id in (${accountId}) 
		</if>
		<if test="groupName != null and groupName != ''">
			and group_name = #{groupName} 
		</if>
		<if test="campaignId != null and campaignId != ''">
			and campaign_id in (${campaignId})
		</if>
		<if test="campaignName != null and campaignName != ''">
			and campaign_name = #{campaignName} 
		</if>
		group by appid
		<if test="group != null and group != ''">
			,${group}
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by day asc ,spend desc
			</otherwise>
		</choose>
	</sql>
	
	<!-- 微信小游戏实时ROI/实时ROI报表查询-->
  	<select id="roiReport" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam" resultType="com.wbgame.pojo.jettison.AdRoiDTO">
		<include refid="wx_game_actula_roi_sql"/>
	</select>
	<!-- 微信小游戏实时ROI/实时ROI报表汇总查询  -->
	<select id="roiReportTotal" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam"  resultType="com.wbgame.pojo.jettison.AdRoiDTO">
		select 
		day, appId,media,channel, childChannel, accountId, groupName, campaignName,
			 campaignId,sum(asplashShowTimes) as asplashShowTimes,sum(aplaqueVideoShowTimes) as aplaqueVideoShowTimes,
			sum(abannerShowTimes) as abannerShowTimes,sum(ainfoFlowShowTimes) as ainfoFlowShowTimes,sum(aplaqueShowTimes) as aplaqueShowTimes,
			sum(avideoShowTimes) as avideoShowTimes ,sum(impressions) as impressions,sum(clicks) as clicks,sum(installs) as installs,
			sum(`convert`) as 'convert',sum(register) as register,sum(gameAddiction) as gameAddiction,sum(addIpa) as addIpa,strategy,company,
			sum(spend) as spend,sum(videoLtv) as videoLtv,sum(plaqueLtv) AS plaqueLtv,bid, adsensePosition, sum(payCount) payCount, sum(gamePayCount) gamePayCount, sum(payCount7) payCount7, sum(payCountFirst7) payCountFirst7,
			sum(dupAddUser) as dupAddUser,sum(addUser) as addUser,sum(addUserLtv0) as addUserLtv0,putUser,sum(addPurchaseUsers) addPurchaseUsers
		from (<include refid="wx_game_actula_roi_sql"/>) 
	</select>
	<sql id="wx_game_actula_roi_sql">
		SELECT day,appid appId,media,channel,child_channel childChannel,account_id accountId,group_name groupName,campaign_name campaignName,
			campaign_id campaignId,adsense_position adsensePosition,strategy,company,anchor_related_type,delivery_mode,
			sum(asplash_show_times) as asplashShowTimes,sum(aplaque_video_show_times) as aplaqueVideoShowTimes,
			sum(abanner_show_times) as abannerShowTimes,sum(ainfo_flow_show_times) as ainfoFlowShowTimes,sum(aplaque_show_times) as aplaqueShowTimes,
			sum(avideo_show_times) as avideoShowTimes ,sum(impressions) as impressions,sum(clicks) as clicks,sum(installs) as installs,
			sum(`convert`) as 'convert',sum(register) as register,sum(game_addiction) as gameAddiction,sum(add_ipa) as addIpa,
			sum(spend) as spend,sum(video_ltv) as videoLtv,sum(plaque_ltv) AS plaqueLtv,bid,
			sum(pay_count) payCount,sum(game_pay_count) gamePayCount,sum(pay_count7) payCount7,sum(pay_count_first7) payCountFirst7,
			sum(dup_add_user) as dupAddUser,sum(add_user) as addUser,sum(add_user_ltv0) as addUserLtv0,put_user putUser,sum(add_user_pay_count) addPurchaseUsers from #{tableName}
			where day BETWEEN #{beginDate} AND #{endDate} AND   (spend>0 or add_user>0)
		<if test="appId != null and appId != ''">
			and appid in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and media in (${media}) 
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account_id in (${accountId}) 
		</if>
		<if test="putUser != null and putUser != ''">
			and put_user in (${putUser}) 
		</if>
		<if test="campaignId != null and campaignId != ''">
			and campaign_id = #{campaignId} 
		</if>
		<if test="campaignName != null and campaignName != ''">
			and campaign_name = #{campaignName} 
		</if>
		<if test="childChannel != null and childChannel != ''">
			and child_channel in (${childChannel}) 
		</if>
		<if test="groupName != null and groupName != ''">
			and group_name = #{groupName} 
		</if>
		<if test="company != null and company != ''">
			and company in (${company}) 
		</if>
		<if test="adsensePosition != null and adsensePosition != ''">
			and adsense_position in (${adsensePosition}) 
		</if>
		<if test="strategy != null and strategy != ''">
			and strategy in (${strategy}) 
		</if>
		<if test="accountType != null and accountType != ''">
			and account_type in (${accountType}) 
		</if>
		<if test="anchor_related_type != null and anchor_related_type != ''">
			and anchor_related_type = #{anchor_related_type} 
		</if>
		<if test="scope != null and scope != ''">
          <if test="index == 'spend'">
            and  spend <![CDATA[ ${symbol} ]]> #{number}
          </if>
        </if>
		group by appid
		<if test="group != null and group != ''">
			,${group}
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by day asc,appId asc ,spend desc
			</otherwise>
		</choose>
	</sql>
	
	
	<!-- 代理商微信小游戏实时ROI报表查询-->
  	<select id="agentRoiReport" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam" resultType="com.wbgame.pojo.jettison.AdRoiDTO">
		<include refid="wx_game_agent_roi_sql"/>
	</select>
	<!-- 代理商微信小游戏实时ROI报表查询汇总查询  -->
	<select id="agentRoiReportTotal" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam"  resultType="com.wbgame.pojo.jettison.AdRoiDTO">
		select 
		day, appId,media,channel, childChannel, accountId, groupName, campaignName,
			 campaignId,sum(asplashShowTimes) as asplashShowTimes,sum(aplaqueVideoShowTimes) as aplaqueVideoShowTimes,
			sum(abannerShowTimes) as abannerShowTimes,sum(ainfoFlowShowTimes) as ainfoFlowShowTimes,sum(aplaqueShowTimes) as aplaqueShowTimes,
			sum(avideoShowTimes) as avideoShowTimes ,sum(impressions) as impressions,sum(clicks) as clicks,sum(installs) as installs,
			sum(`convert`) as 'convert',sum(register) as register,sum(gameAddiction) as gameAddiction,sum(addIpa) as addIpa,strategy,company,
			sum(spend) as spend,sum(videoLtv) as videoLtv,sum(plaqueLtv) AS plaqueLtv,bid, adsensePosition, sum(payCount) payCount, sum(gamePayCount) gamePayCount, sum(payCount7) payCount7, sum(payCountFirst7) payCountFirst7,
			sum(dupAddUser) as dupAddUser,sum(addUser) as addUser,sum(addUserLtv0) as addUserLtv0,putUser
		from (<include refid="wx_game_actula_roi_sql"/>) 
	</select>
	<sql id="wx_game_agent_roi_sql">
		SELECT day,appid appId,media,channel,child_channel childChannel,account_id accountId,group_name groupName,campaign_name campaignName,
			campaign_id campaignId,sum(asplash_show_times) as asplashShowTimes,sum(aplaque_video_show_times) as aplaqueVideoShowTimes,
			sum(abanner_show_times) as abannerShowTimes,sum(ainfo_flow_show_times) as ainfoFlowShowTimes,sum(aplaque_show_times) as aplaqueShowTimes,
			sum(avideo_show_times) as avideoShowTimes ,sum(impressions) as impressions,sum(clicks) as clicks,sum(installs) as installs,
			sum(`convert`) as 'convert',sum(register) as register,sum(game_addiction) as gameAddiction,sum(add_ipa)*100 as addIpa,strategy,company,
			sum(spend)*100 as spend,sum(video_ltv)*100 as videoLtv,sum(plaque_ltv)*100 AS plaqueLtv,bid,
			adsense_position adsensePosition,sum(pay_count) payCount,sum(game_pay_count) gamePayCount,sum(pay_count7) payCount7,sum(pay_count_first7) payCountFirst7,
			sum(dup_add_user) as dupAddUser,sum(add_user) as addUser,sum(add_user_ltv0)*100 as addUserLtv0,put_user putUser from wx_game_roi_report
			where day BETWEEN #{beginDate} AND #{endDate} AND   (spend>0 or add_user>0)
		<if test="appId != null and appId != ''">
			and appid in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and media in (${media}) 
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account_id in (${accountId}) 
		</if>
		<if test="putUser != null and putUser != ''">
			and put_user in (${putUser}) 
		</if>
		<if test="campaignId != null and campaignId != ''">
			and campaign_id = #{campaignId} 
		</if>
		<if test="campaignName != null and campaignName != ''">
			and campaign_name = #{campaignName} 
		</if>
		<if test="childChannel != null and childChannel != ''">
			and child_channel = #{childChannel} 
		</if>
		<if test="groupName != null and groupName != ''">
			and group_name = #{groupName} 
		</if>
		<if test="company != null and company != ''">
			and company in (${company}) 
		</if>
		<if test="adsensePosition != null and adsensePosition != ''">
			and adsense_position in (${adsensePosition}) 
		</if>
		<if test="strategy != null and strategy != ''">
			and strategy in (${strategy}) 
		</if>
		<if test="accountType != null and accountType != ''">
			and account_type in (${accountType}) 
		</if>
		<if test="scope != null and scope != ''">
          <if test="index == 'spend'">
            and  spend <![CDATA[ ${symbol} ]]> #{number}
          </if>
        </if>
		group by appid
		<if test="group != null and group != ''">
			,${group}
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by day asc ,spend desc
			</otherwise>
		</choose>
	</sql>
	
	<!-- 查询实时报表最近更新时间  -->
  	<select id="queryActualCreateTime" parameterType="java.lang.String" resultType="java.util.Map">
  	SELECT blocking_time blockingTime,end_time endTime ,update_time updateTime,tdate FROM report_update_time WHERE tdate =#{dateSuff}
				order by id desc limit 1
	</select>
	
	<!--  OPPO代理商广告实时ROI报表查询 -->
  	<select id="oppoActualAdRoiReport" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam" resultType="com.wbgame.pojo.jettison.AdRoiDTO">
		<include refid="oppo_actula_roi_sql"/>
	</select>
	<!--  OPPO代理商广告实时ROI报表汇总查询  -->
	<select id="oppoActualAdRoiReportTotal" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam"  resultType="com.wbgame.pojo.jettison.AdRoiDTO">
		SELECT day, appId,media, childChannel, accountId, groupName, campaignName,campaignId,sum(aplaqueShowTimes) as aplaqueShowTimes,sum(installs) installs,
			sum(addIpa) as addIpa,strategy,sum(spend) as spend,adsensePosition ,sum(addUser) as addUser,sum(addUserLtv0) as addUserLtv0,sum(ltv2) ltv2
		from (<include refid="oppo_actula_roi_sql"/>) 
	</select>
	<sql id="oppo_actula_roi_sql">
		SELECT day,appid appId,media,child_channel childChannel,account_id accountId,group_name groupName,campaign_name campaignName,
		campaign_id campaignId,sum(aplaque_show_times) as aplaqueShowTimes,sum(add_ipa) as addIpa,strategy,sum(spend) as spend,adsense_position adsensePosition,
		sum(add_user) as addUser,sum(add_user_ltv0) as addUserLtv0,sum(revenue1) ltv2,sum(installs) installs from channel_realize_report_data_works
		where day BETWEEN #{beginDate} AND #{endDate} AND   (spend>0 or add_user>0) 
		<if test="appId != null and appId != ''">
			and appid in (${appId}) 
		</if>
		<if test="media != null and media != ''">
			and media in (${media}) 
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel}) 
		</if>
		<if test="accountId != null and accountId != ''">
			and account_id in (${accountId}) 
		</if>
		<if test="putUser != null and putUser != ''">
			and put_user in (${putUser}) 
		</if>
		<if test="campaignId != null and campaignId != ''">
			and campaign_id = #{campaignId} 
		</if>
		<if test="campaignName != null and campaignName != ''">
			and campaign_name = #{campaignName} 
		</if>
		<if test="childChannel != null and childChannel != ''">
			and child_channel = #{childChannel} 
		</if>
		<if test="groupName != null and groupName != ''">
			and group_name = #{groupName} 
		</if>
		<if test="company != null and company != ''">
			and company in (${company}) 
		</if>
		<if test="adsensePosition != null and adsensePosition != ''">
			and adsense_position in (${adsensePosition}) 
		</if>
		<if test="strategy != null and strategy != ''">
			and strategy in (${strategy}) 
		</if>
		<if test="accountType != null and accountType != ''">
			and account_type in (${accountType}) 
		</if>
		<if test="scope != null and scope != ''">
          <if test="index == 'spend'">
             and spend <![CDATA[ ${symbol} ]]> #{number}
          </if>
        </if>
		group by appid
		<if test="group != null and group != ''">
			,${group}
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by day asc ,spend desc
			</otherwise>
		</choose>
	</sql>
	
	
	<!-- 实时ROI报表查询-->
	<select id="actualRoiReport" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam" resultType="com.wbgame.pojo.jettison.vo.AdRoiVo">
		SELECT
			<if test='group.contains("day")'>
				day,
			</if>
			<if test='group.contains("hourly")'>
				hourly,
			</if>
			<if test='group.contains("appId")'>
				appid appId ,
				app_name appName,
			</if>
			<if test='group.contains("media")'>
				case when media='' or media='-1' then '自然量'
					 else media
				end as	  media,
			</if>
			<if test='group.contains("channel")'>
				case when channel='' or channel='-1' then '自然量'
					 else channel
				end as	  channel,
			</if>
			<if test='group.contains("accountId")'>
				case when account_id='' or account_id='-1' then '自然量'
					 else account_id
				end as	   accountId,
			</if>
			<if test='group.contains("putUser")'>
				put_user putUser,
			</if>
			<if test='group.contains("groupName")'>
				group_name groupName,
			</if>
			<if test='group.contains("campaignName")'>
				campaign_name campaignName,
			</if>
			<if test='group.contains("campaignId")'>
				case when campaign_id='' or campaign_id='-1' then '自然量'
					 else campaign_id
				end as	   campaignId,
			</if>
			<if test='group.contains("accountType")'>
				account_type accountType,
			</if>
			<if test='group.contains("strategy")'>
-- 				case when strategy='1' then '激活'
-- 					 when strategy='2' then '注册'
-- 					 when strategy='3' then '付费'
-- 					 when strategy='4' then '付费ROI'
-- 					 when strategy='5' then '关键行为'
-- 					 when strategy='6' then '次留'
-- 					 when strategy='7' then '激活+次留'
-- 					 when strategy='8' then '激活+付费'
-- 					 when strategy='9' then '激活+关键行为'
-- 					 when strategy='10' then '每次付费'
-- 					 when strategy='11' then 'nobid-每次付费'
-- 					 when strategy='12' then 'nobid-目标付费'
-- 					 when strategy='13' then 'nobid-付费ROI'
-- 					 when strategy='14' then '注册-首日付费ROI'
-- 					 when strategy='15' then '广告变现-首日变现ROI'
-- 					 when strategy='16' then '首次付费-首日付费ROI'
-- 					 when strategy='17' then '激活-注册'
-- 					 when strategy='18' then '激活-首日付费ROI'
-- 					 when strategy='19' then '目标付费'
-- 					 when strategy='21' then '广告变现'
-- 					 when strategy='22' then '下载'
-- 					 when strategy='23' then '自定义注册'
-- 					 when strategy='24' then '自定义付费'
-- 					 when strategy='25' then '自定义次留'
-- 					 when strategy='26' then '自定义激活'
-- 					 when strategy='27' then '广告变现-7日变现ROI'
-- 					 when strategy='999' then '其他'
-- 					 else '未识别'
-- 				end as	strategy,
				    strategy strategy,
			</if>
			<if test='group.contains("appCategory")'>
				app_category appCategory,
			</if>
			<if test='group.contains("company")'>
				company company,
			</if>
			<if test='group.contains("adsensePosition")'>
				adsense_position adsensePosition,
			</if>
			<if test='group.contains("anchor_related_type")'>
				case when anchor_related_type='1' then '是'
					 when anchor_related_type='0' then '否'
					 else '未识别'
				end as	 anchor_related_type,
			</if>
			<if test='group.contains("delivery_mode")'>
				case when delivery_mode='1' then '是'
					 when delivery_mode='0' then '否'
					 when delivery_mode is null then '巨量1.0'
					 else '未识别'
				end as	 delivery_mode,
			</if>
			<if test='group.contains("adsenseType")'>
				adsensetype,
			</if>
			coalesce(round(sum(add_user_ltv0),2),0) realizeIncome,
			coalesce(round(sum(add_ipa),2),0) ipaIncome,
			coalesce(round(sum(add_ipa)/sum(spend),4),0) ipaRoi,
			coalesce(round(sum(add_user_ltv0)/sum(spend),4),0.00) realizeRoi,
			coalesce(round((coalesce(sum(add_ipa),0)+coalesce(sum(add_user_ltv0),0))/sum(spend),4),0) firstDayRoi,
			<choose>
            	<when test="revenueSource ==1">
            		coalesce(round(sum(add_user_ltv0),2),0) addUserLtv0,
            		coalesce(round(sum(add_user_ltv0)/sum(add_user),2),0) addArpu,
            		coalesce(round(sum(add_user_ltv0)/sum(spend),4),0.00) aRoi,
            	</when>
            	<when test="revenueSource ==2">
            		coalesce(round(sum(add_ipa),2),0) addUserLtv0,
            		coalesce(round(sum(add_ipa)/sum(add_user),2),0) addArpu,
            		coalesce(round(sum(add_ipa)/sum(spend),4),0.00) aRoi,
            	</when>
            	<otherwise>
					round(COALESCE(sum(add_ipa),0)+COALESCE(sum(add_user_ltv0),0),2) addUserLtv0,
					COALESCE(round((COALESCE(sum(add_ipa),0)+COALESCE(sum(add_user_ltv0),0))/sum(add_user),2),0) addArpu,
					coalesce(round((coalesce(sum(add_ipa),0)+coalesce(sum(add_user_ltv0),0))/sum(spend),4),0.00) aRoi,
				</otherwise>
			</choose>
			coalesce(sum(add_user),0) as addUser,coalesce(round(sum(spend),2),0) spend,
			coalesce(round(sum(spend)/sum(add_user),2),0) cpa,
			round(sum(avideo_show_times)/sum(add_user),2) avgVideo,
			round(sum(aplaque_show_times)/sum(add_user),2) avgPlaque,
			round(sum(video_ltv)/sum(avideo_show_times)*1000,2) videoEcpm,
			round(sum(plaque_ltv)/sum(aplaque_show_times)*1000,2) plaqueEcpm,
			coalesce(sum(pay_count),0) firstPayCount,
			coalesce(sum(game_pay_count),0) paymentTimes,
			round(sum(spend)/sum(pay_count),2) firstChargeCost,
			coalesce(round(sum(pay_count)/sum(installs),4),0.00) firstPaymentRate,
			sum(pay_count7) paymentTimes7,
			round(sum(pay_count7)/sum(pay_count_first7),2) avgPayCount7,
			round(sum(spend)/sum(game_pay_count),2)  payCost,
			sum(add_user_pay_count) addPurchaseUsers,
			round(sum(spend)/sum(add_user_pay_count),2) addUserPayCost,
			round(sum(add_ipa)/sum(add_user_pay_count),2) addUserPayArpu,
			coalesce(round(sum(add_user_pay_count)/sum(add_user),4),0.00) addUserPayRate,
			round(sum(spend)/sum(convert),2) convertSpend,
			round(sum(spend)/sum(register),2) registerSpend,
			round(sum(spend)/sum(game_addiction),2) addictionSpend,
			round(sum(spend)/sum(impressions)*1000,2) avgShowSpend,
			coalesce(round(sum(convert)/sum(clicks),4),0.00) convertRate,
			coalesce(round(sum(installs)/sum(clicks),4),0.00) installRate,
			coalesce(round(sum(register)/sum(installs),4),0.00) registerRate,
			coalesce(round(sum(game_addiction)/sum(installs),4),0.00) addictionRate,
			coalesce(round(sum(clicks)/sum(impressions),4),0.00) clickRate,
			coalesce(sum(game_addiction),0) as gameAddiction
		from
		(<include refid="jrtt_actula_roi_sql"/>)
		<if test="group != null and group != ''">
			group by ${group}
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by day asc,appId asc ,spend desc
			</otherwise>
		</choose>
	</select>
	<!-- 实时ROI报表汇总查询  -->
	<select id="actualRoiReportTotal" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam"  resultType="com.wbgame.pojo.jettison.vo.AdRoiVo">
		SELECT
			coalesce(round(sum(add_user_ltv0),2),0) realizeIncome,
			coalesce(round(sum(add_ipa),2),0) ipaIncome,
			coalesce(round(sum(add_ipa)/sum(spend),4),0) ipaRoi,
			coalesce(round(sum(add_user_ltv0)/sum(spend),4),0.00) realizeRoi,
			coalesce(round((coalesce(sum(add_ipa),0)+coalesce(sum(add_user_ltv0),0))/sum(spend),4),0) firstDayRoi,
			<choose>
            	<when test="revenueSource ==1">
            		coalesce(round(sum(add_user_ltv0),2),0) addUserLtv0,
            		coalesce(round(sum(add_user_ltv0)/sum(add_user),2),0) addArpu,
            		coalesce(round(sum(add_user_ltv0)/sum(spend),4),0.00) aRoi,
            	</when>
            	<when test="revenueSource ==2">
            		coalesce(round(sum(add_ipa),2),0) addUserLtv0,
            		coalesce(round(sum(add_ipa)/sum(add_user),2),0) addArpu,
            		coalesce(round(sum(add_ipa)/sum(spend),4),0.00) aRoi,
            	</when>
            	<otherwise>
					round(COALESCE(sum(add_ipa),0)+COALESCE(sum(add_user_ltv0),0),2) addUserLtv0,
					COALESCE(round((COALESCE(sum(add_ipa),0)+COALESCE(sum(add_user_ltv0),0))/sum(add_user),2),0) addArpu,
					coalesce(round((coalesce(sum(add_ipa),0)+coalesce(sum(add_user_ltv0),0))/sum(spend),4),0.00) aRoi,
				</otherwise>
			</choose>
			coalesce(sum(add_user),0) as addUser,coalesce(round(sum(spend),2),0) spend,
			coalesce(round(sum(spend)/sum(add_user),2),0) cpa,
			round(sum(avideo_show_times)/sum(add_user),2) avgVideo,
			round(sum(aplaque_show_times)/sum(add_user),2) avgPlaque,
			round(sum(video_ltv)/sum(avideo_show_times)*1000,2) videoEcpm,
			round(sum(plaque_ltv)/sum(aplaque_show_times)*1000,2) plaqueEcpm,
			coalesce(sum(pay_count),0) firstPayCount,
			coalesce(sum(game_pay_count),0) paymentTimes,
			round(sum(spend)/sum(pay_count),2) firstChargeCost,
			coalesce(round(sum(pay_count)/sum(installs),4),0.00) firstPaymentRate,
			sum(pay_count7) paymentTimes7,
			round(sum(pay_count7)/sum(pay_count_first7),2) avgPayCount7,
			round(sum(spend)/sum(game_pay_count),2)  payCost,
			sum(add_user_pay_count) addPurchaseUsers,
			round(sum(spend)/sum(add_user_pay_count),2) addUserPayCost,
			round(sum(add_ipa)/sum(add_user_pay_count),2) addUserPayArpu,
			coalesce(round(sum(add_user_pay_count)/sum(add_user),4),0.00) addUserPayRate,
			round(sum(spend)/sum(convert),2) convertSpend,
			round(sum(spend)/sum(register),2) registerSpend,
			round(sum(spend)/sum(game_addiction),2) addictionSpend,
			round(sum(spend)/sum(impressions)*1000,2) avgShowSpend,
			coalesce(round(sum(convert)/sum(clicks),4),0.00) convertRate,
			coalesce(round(sum(installs)/sum(clicks),4),0.00) installRate,
			coalesce(round(sum(register)/sum(installs),4),0.00) registerRate,
			coalesce(round(sum(game_addiction)/sum(installs),4),0.00) addictionRate,
			coalesce(round(sum(clicks)/sum(impressions),4),0.00) clickRate,
			coalesce(sum(game_addiction),0) as gameAddiction
		from (<include refid="jrtt_actula_roi_sql"/>)
	</select>
	<sql id="jrtt_actula_roi_sql">
			SELECT c.*,a.app_category app_category,a.app_name app_name from channel_realize_report_data_works c left join dnwx_adt.app_info a on c.appid=a.id where day BETWEEN #{beginDate} AND #{endDate} 
		<if test="appId != null and appId != ''">
			and appid in (${appId})
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel})
		</if>
		<if test="media != null and media != ''">
			and media in (${media})
		</if>
		<if test="accountId != null and accountId != ''">
			and account_id in (${accountId})
		</if>
		<if test="putUser != null and putUser != ''">
			and put_user in (${putUser})
		</if>
		<if test="campaignId != null and campaignId != ''">
			and campaign_id = #{campaignId}
		</if>
		<if test="campaignName != null and campaignName != ''">
			and campaign_name = #{campaignName}
		</if>
		<if test="childChannel != null and childChannel != ''">
			and child_channel in (${childChannel})
		</if>
		<if test="groupName != null and groupName != ''">
			and group_name = #{groupName}
		</if>
		<if test="company != null and company != ''">
			and company in (${company})
		</if>
		<if test="adsensePositions != null and adsensePositions.size > 0">
			AND (
			adsense_position IN
			<foreach collection="adsensePositions" index="index" item="item" open="(" separator="," close=")">
				${item}
			</foreach>
			OR
			<foreach collection="adsensePositions" index="index" item="item" separator="OR">
				adsense_position LIKE  concat('%',${item},'%')
			</foreach>
			)
		</if>
		<if test="strategy != null and strategy != ''">
			and strategy in (${strategy})
		</if>
		<if test="accountType != null and accountType != ''">
			and account_type in (${accountType})
		</if>
		<if test="adsenseTypes != null and adsenseTypes.size > 0">
			AND (
			adsenseType IN
			<foreach collection="adsenseTypes" index="index" item="item" open="(" separator="," close=")">
				${item}
			</foreach>
			OR
			<foreach collection="adsenseTypes" index="index" item="item" separator="OR">
				adsenseType LIKE  concat('%',${item},'%')
			</foreach>
			)
		</if>
		<if test="delivery_mode != null and delivery_mode != ''">
			and delivery_mode = #{delivery_mode}
		</if>
		<if test="anchor_related_type != null and anchor_related_type != ''">
			and anchor_related_type = #{anchor_related_type}
		</if>
		<if test="appCategory != null and appCategory != ''">
			and a.app_category in  (${appCategory})
		</if>
		<if test="scope != null and scope != ''">
			<if test="index == 'spend'">
				and ifnull(spend,0) <![CDATA[ ${symbol} ]]> #{number}
			</if>
			<if test="index == 'spendNature'">
			and (
        		media = '自然量' OR
        		(media != '自然量' AND ifnull(spend,0) <![CDATA[ ${symbol} ]]> #{number})
    		)
          		</if>
		</if>
		union all
		SELECT c.*,a.app_category app_category,a.app_name app_name from wx_game_roi_report c left join dnwx_adt.app_info a on c.appid=a.id where day BETWEEN #{beginDate} AND #{endDate} 
		<if test="appId != null and appId != ''">
			and appid in (${appId})
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel})
		</if>
		<if test="media != null and media != ''">
			and media in (${media})
		</if>
		<if test="accountId != null and accountId != ''">
			and account_id in (${accountId})
		</if>
		<if test="putUser != null and putUser != ''">
			and put_user in (${putUser})
		</if>
		<if test="campaignId != null and campaignId != ''">
			and campaign_id = #{campaignId}
		</if>
		<if test="campaignName != null and campaignName != ''">
			and campaign_name = #{campaignName}
		</if>
		<if test="childChannel != null and childChannel != ''">
			and child_channel in (${childChannel})
		</if>
		<if test="groupName != null and groupName != ''">
			and group_name = #{groupName}
		</if>
		<if test="company != null and company != ''">
			and company in (${company})
		</if>
		<if test="adsensePositions != null and adsensePositions.size > 0">
			AND (
			adsense_position IN
			<foreach collection="adsensePositions" index="index" item="item" open="(" separator="," close=")">
				${item}
			</foreach>
			OR
			<foreach collection="adsensePositions" index="index" item="item" separator="OR">
				adsense_position LIKE  concat('%',${item},'%')
			</foreach>
			)
		</if>
		<if test="strategy != null and strategy != ''">
			and strategy in (${strategy})
		</if>
		<if test="accountType != null and accountType != ''">
			and account_type in (${accountType})
		</if>
		<if test="adsenseTypes != null and adsenseTypes.size > 0">
			AND (
			adsenseType IN
			<foreach collection="adsenseTypes" index="index" item="item" open="(" separator="," close=")">
				${item}
			</foreach>
			OR
			<foreach collection="adsenseTypes" index="index" item="item" separator="OR">
				adsenseType LIKE  concat('%',${item},'%')
			</foreach>
			)
		</if>
		<if test="delivery_mode != null and delivery_mode != ''">
			and delivery_mode = #{delivery_mode}
		</if>
		<if test="anchor_related_type != null and anchor_related_type != ''">
			and anchor_related_type = #{anchor_related_type}
		</if>
		<if test="appCategory != null and appCategory != ''">
			and a.app_category in  (${appCategory})
		</if>
		<if test="scope != null and scope != ''">
			<if test="index == 'spend'">
				and ifnull(spend,0) <![CDATA[ ${symbol} ]]> #{number}
			</if>
		<if test="index == 'spendNature'">
			and (
        		media = '自然量' OR
        		(media != '自然量' AND COALESCE(spend,0) <![CDATA[ ${symbol} ]]> #{number})
    		)
          		</if>
		</if>
	</sql>

	<!-- 实时ROI小时报表查询-->
	<select id="roiHourReport" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam" resultType="com.wbgame.pojo.jettison.vo.AdRoiVo">
		SELECT
			<if test='group.contains("day")'>
				day,
			</if>
			<if test='group.contains("hourly")'>
				hourly,
			</if>
			<if test='group.contains("appId")'>
				appid appId ,
				app_name appName,
			</if>
			<if test='group.contains("media")'>
				case when media='' or media='-1' then '自然量'
					 else media
				end as	  media,
			</if>
			<if test='group.contains("channel")'>
				case when channel='' or channel='-1' then '自然量'
					 else channel
				end as	  channel,
			</if>
			<if test='group.contains("accountId")'>
				case when account_id='' or account_id='-1' then '自然量'
					 else account_id
				end as	   accountId,
			</if>
			<if test='group.contains("putUser")'>
				put_user putUser,
			</if>
			<if test='group.contains("groupName")'>
				group_name groupName,
			</if>
			<if test='group.contains("campaignName")'>
				campaign_name campaignName,
			</if>
			<if test='group.contains("campaignId")'>
				case when campaign_id='' or campaign_id='-1' then '自然量'
					 else campaign_id
				end as	   campaignId,
			</if>
			<if test='group.contains("accountType")'>
				account_type accountType,
			</if>
			<if test='group.contains("strategy")'>
				case when strategy='1' then '激活'
					 when strategy='2' then '注册'
					 when strategy='3' then '付费'
					 when strategy='4' then '付费ROI'
					 when strategy='5' then '关键行为'
					 when strategy='6' then '次留'
					 when strategy='7' then '激活+次留'
					 when strategy='8' then '激活+付费'
					 when strategy='9' then '激活+关键行为'
					 when strategy='10' then '每次付费'
					 when strategy='11' then 'nobid-每次付费'
					 when strategy='12' then 'nobid-目标付费'
					 when strategy='13' then 'nobid-付费ROI'
					 when strategy='14' then '注册-首日付费ROI'
					 when strategy='15' then '广告变现-首日变现ROI'
					 when strategy='16' then '首次付费-首日付费ROI'
					 when strategy='17' then '激活-注册'
					 when strategy='18' then '激活-首日付费ROI'
					 when strategy='19' then '目标付费'
					 when strategy='21' then '广告变现'
					 when strategy='22' then '下载'
					 when strategy='23' then '自定义注册'
					 when strategy='24' then '自定义付费'
					 when strategy='25' then '自定义次留'
					 when strategy='26' then '自定义激活'
					 when strategy='27' then '广告变现-7日变现ROI'
					 when strategy='999' then '其他'
					 else '未识别'
				end as	strategy,
			</if>
			<if test='group.contains("appCategory")'>
				app_category appCategory,
			</if>
			<if test='group.contains("company")'>
				company company,
			</if>
			<if test='group.contains("adsensePosition")'>
				adsense_position adsensePosition,
			</if>
			<if test='group.contains("anchor_related_type")'>
				case when anchor_related_type='1' then '是'
					 when anchor_related_type='0' then '否'
					 else '未识别'
				end as	 anchor_related_type,
			</if>
			<if test='group.contains("delivery_mode")'>
				case when delivery_mode='1' then '是'
					 when delivery_mode='0' then '否'
					 when delivery_mode is null then '巨量1.0'
					 else '未识别'
				end as	 delivery_mode,
			</if>
			<if test='group.contains("adsenseType")'>
				adsensetype,
			</if>
			coalesce(round(sum(add_user_ltv0),2),0) realizeIncome,
			coalesce(round(sum(add_ipa),2),0) ipaIncome,
			coalesce(round(sum(add_ipa)/sum(spend),4),0) ipaRoi,
			coalesce(round(sum(add_user_ltv0)/sum(spend),4),0.00) realizeRoi,
			coalesce(round((coalesce(sum(add_ipa),0)+coalesce(sum(add_user_ltv0),0))/sum(spend),4),0) firstDayRoi,
			<choose>
            	<when test="revenueSource ==1">
            		coalesce(round(sum(add_user_ltv0),2),0) addUserLtv0,
            		coalesce(round(sum(add_user_ltv0)/sum(add_user),2),0) addArpu,
            		coalesce(round(sum(add_user_ltv0)/sum(spend),4),0.00) aRoi,
            	</when>
            	<when test="revenueSource ==2">
            		coalesce(round(sum(add_ipa),2),0) addUserLtv0,
            		coalesce(round(sum(add_ipa)/sum(add_user),2),0) addArpu,
            		coalesce(round(sum(add_ipa)/sum(spend),4),0.00) aRoi,
            	</when>
            	<otherwise>
					round(COALESCE(sum(add_ipa),0)/100.00+COALESCE(sum(add_user_ltv0),0)/100.00,2) addUserLtv0,
					COALESCE(round((COALESCE(sum(add_ipa),0)+COALESCE(sum(add_user_ltv0),0))/sum(add_user),2),0) addArpu,
					coalesce(round((coalesce(sum(add_ipa),0)+coalesce(sum(add_user_ltv0),0))/sum(spend),4),0.00) aRoi,
				</otherwise>
			</choose>
			coalesce(sum(add_user),0) as addUser,coalesce(round(sum(spend),2),0) spend,
			coalesce(round(sum(spend)/sum(add_user),2),0) cpa,
			round(sum(avideo_show_times)/sum(add_user),2) avgVideo,
			round(sum(aplaque_show_times)/sum(add_user),2) avgPlaque,
			round(sum(video_ltv)/sum(avideo_show_times)*1000,2) videoEcpm,
			round(sum(plaque_ltv)/sum(aplaque_show_times)*1000,2) plaqueEcpm,
			coalesce(sum(pay_count),0) firstPayCount,
			coalesce(sum(game_pay_count),0) paymentTimes,
			round(sum(spend)/sum(pay_count),2) firstChargeCost,
			coalesce(round(sum(pay_count)/sum(installs),4),0.00) firstPaymentRate,
			sum(pay_count7) paymentTimes7,
			round(sum(pay_count7)/sum(pay_count_first7),2) avgPayCount7,
			round(sum(spend)/sum(game_pay_count),2)  payCost,
			sum(add_user_pay_count) addPurchaseUsers,
			round(sum(spend)/sum(add_user_pay_count),2) addUserPayCost,
			round(sum(add_ipa)/sum(add_user_pay_count),2) addUserPayArpu,
			coalesce(round(sum(add_user_pay_count)/sum(add_user),4),0.00) addUserPayRate,
			round(sum(spend)/sum(convert),2) convertSpend,
			round(sum(spend)/sum(register),2) registerSpend,
			round(sum(spend)/sum(game_addiction),2) addictionSpend,
			round(sum(spend)/sum(impressions)*1000,2) avgShowSpend,
			coalesce(round(sum(convert)/sum(clicks),4),0.00) convertRate,
			coalesce(round(sum(installs)/sum(clicks),4),0.00) installRate,
			coalesce(round(sum(register)/sum(installs),4),0.00) registerRate,
			coalesce(round(sum(game_addiction)/sum(installs),4),0.00) addictionRate,
			coalesce(round(sum(clicks)/sum(impressions),4),0.00) clickRate,
			coalesce(sum(game_addiction),0) as gameAddiction
		from
		(<include refid="actula_hour_roi_sql"/>)
		<if test="group != null and group != ''">
			group by ${group}
		</if>
		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by day asc,appId asc ,spend desc
			</otherwise>
		</choose>
	</select>
	<!-- 实时ROI小时报表汇总查询  -->
	<select id="roiHourReportTotal" parameterType="com.wbgame.pojo.jettison.param.AdRoiParam"  resultType="com.wbgame.pojo.jettison.vo.AdRoiVo">
		SELECT
			coalesce(round(sum(add_user_ltv0),2),0) realizeIncome,
			coalesce(round(sum(add_ipa),2),0) ipaIncome,
			coalesce(round(sum(add_ipa)/sum(spend),4),0) ipaRoi,
			coalesce(round(sum(add_user_ltv0)/sum(spend),4),0.00) realizeRoi,
			coalesce(round((coalesce(sum(add_ipa),0)+coalesce(sum(add_user_ltv0),0))/sum(spend),4),0) firstDayRoi,
			<choose>
            	<when test="revenueSource ==1">
            		coalesce(round(sum(add_user_ltv0),2),0) addUserLtv0,
            		coalesce(round(sum(add_user_ltv0)/sum(add_user),2),0) addArpu,
            		coalesce(round(sum(add_user_ltv0)/sum(spend),4),0.00) aRoi,
            	</when>
            	<when test="revenueSource ==2">
            		coalesce(round(sum(add_ipa),2),0) addUserLtv0,
            		coalesce(round(sum(add_ipa)/sum(add_user),2),0) addArpu,
            		coalesce(round(sum(add_ipa)/sum(spend),4),0.00) aRoi,
            	</when>
            	<otherwise>
					round(COALESCE(sum(add_ipa),0)/100.00+COALESCE(sum(add_user_ltv0),0)/100.00,2) addUserLtv0,
					COALESCE(round((COALESCE(sum(add_ipa),0)+COALESCE(sum(add_user_ltv0),0))/sum(add_user),2),0) addArpu,
					coalesce(round((coalesce(sum(add_ipa),0)+coalesce(sum(add_user_ltv0),0))/sum(spend),4),0.00) aRoi,
				</otherwise>
			</choose>
			coalesce(sum(add_user),0) as addUser,coalesce(round(sum(spend),2),0) spend,
			coalesce(round(sum(spend)/sum(add_user),2),0) cpa,
			round(sum(avideo_show_times)/sum(add_user),2) avgVideo,
			round(sum(aplaque_show_times)/sum(add_user),2) avgPlaque,
			round(sum(video_ltv)/sum(avideo_show_times)*1000,2) videoEcpm,
			round(sum(plaque_ltv)/sum(aplaque_show_times)*1000,2) plaqueEcpm,
			coalesce(sum(pay_count),0) firstPayCount,
			coalesce(sum(game_pay_count),0) paymentTimes,
			round(sum(spend)/sum(pay_count),2) firstChargeCost,
			coalesce(round(sum(pay_count)/sum(installs),4),0.00) firstPaymentRate,
			sum(pay_count7) paymentTimes7,
			round(sum(pay_count7)/sum(pay_count_first7),2) avgPayCount7,
			round(sum(spend)/sum(game_pay_count),2)  payCost,
			sum(add_user_pay_count) addPurchaseUsers,
			round(sum(spend)/sum(add_user_pay_count),2) addUserPayCost,
			round(sum(add_ipa)/sum(add_user_pay_count),2) addUserPayArpu,
			coalesce(round(sum(add_user_pay_count)/sum(add_user),4),0.00) addUserPayRate,
			round(sum(spend)/sum(convert),2) convertSpend,
			round(sum(spend)/sum(register),2) registerSpend,
			round(sum(spend)/sum(game_addiction),2) addictionSpend,
			round(sum(spend)/sum(impressions)*1000,2) avgShowSpend,
			coalesce(round(sum(convert)/sum(clicks),4),0.00) convertRate,
			coalesce(round(sum(installs)/sum(clicks),4),0.00) installRate,
			coalesce(round(sum(register)/sum(installs),4),0.00) registerRate,
			coalesce(round(sum(game_addiction)/sum(installs),4),0.00) addictionRate,
			coalesce(round(sum(clicks)/sum(impressions),4),0.00) clickRate,
			coalesce(sum(game_addiction),0) as gameAddiction
		from (<include refid="actula_hour_roi_sql"/>)
	</select>

	<sql id="proxyActualRoiReportBaseSql">
		SELECT c.*,a.app_category app_category,a.app_name app_name from dnwx_adt.sdk_union_app_roi_proxy c left join dnwx_adt.app_info a on c.appid=a.id where day BETWEEN #{beginDate} AND #{endDate}
		<if test="appId != null and appId != ''">
			and appid in (${appId})
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel})
		</if>
		<if test="media != null and media != ''">
			and media in (${media})
		</if>
		<if test="accountId != null and accountId != ''">
			and account_id in (${accountId})
		</if>
		<if test="putUser != null and putUser != ''">
			and put_user in (${putUser})
		</if>
		<if test="campaignId != null and campaignId != ''">
			and campaign_id = #{campaignId}
		</if>
		<if test="campaignName != null and campaignName != ''">
			and campaign_name = #{campaignName}
		</if>
		<if test="childChannel != null and childChannel != ''">
			and child_channel in (${childChannel})
		</if>
		<if test="groupName != null and groupName != ''">
			and group_name = #{groupName}
		</if>
		<if test="company != null and company != ''">
			and company in (${company})
		</if>
		<if test="adsensePositions != null and adsensePositions.size > 0">
			AND (
			adsensePosition IN
			<foreach collection="adsensePositions" index="index" item="item" open="(" separator="," close=")">
				${item}
			</foreach>
			OR
			<foreach collection="adsensePositions" index="index" item="item" separator="OR">
				adsensePosition LIKE  concat('%',${item},'%')
			</foreach>
			)
		</if>
		<if test="transferType != null and transferType != ''">
			and transferType in (${transferType})
		</if>
		<if test="accountType != null and accountType != ''">
			and account_type in (${accountType})
		</if>
		<if test="adsenseTypes != null and adsenseTypes.size > 0">
			AND (
			adsenseType IN
			<foreach collection="adsenseTypes" index="index" item="item" open="(" separator="," close=")">
				${item}
			</foreach>
<!--			OR-->
<!--			<foreach collection="adsenseTypes" index="index" item="item" separator="OR">-->
<!--				adsenseType LIKE  concat('%',${item},'%')-->
<!--			</foreach>-->
			)
		</if>
		<if test="delivery_mode != null and delivery_mode != ''">
			and delivery_mode = #{delivery_mode}
		</if>
		<if test="anchor_related_type != null and anchor_related_type != ''">
			and anchor_related_type = #{anchor_related_type}
		</if>
		<if test="appCategory != null and appCategory != ''">
			and a.app_category in  (${appCategory})
		</if>
		<if test="firstAgent != null and firstAgent != ''">
			and first_agent in  (${firstAgent})
		</if>

	</sql>

	<sql id="proxyAcutalRoiReportSql">
		SELECT
			<if test='group.contains("day")'>
				`day`,
			</if>
			<if test='group.contains("appId")'>
				appid appId ,
				app_name appName,
			</if>
			<if test='group.contains("accountId")'>
				case when account_id='' or account_id='-1' then '自然量'
					 else account_id
				end as	   accountId,
			</if>
			<if test='group.contains("groupName")'>
				group_name groupName,
			</if>
			<if test='group.contains("campaignName")'>
				campaign_name campaignName,
			</if>
			<if test='group.contains("campaignId")'>
				case when campaign_id='' or campaign_id='-1' then '自然量'
					 else campaign_id
				end as	   campaignId,
			</if>
			<if test='group.contains("appCategory")'>
				app_category appCategory,
			</if>
			<if test='group.contains("adsensePosition")'>
				adsensePosition adsensePosition,
			</if>
			<if test='group.contains("adsenseType")'>
				adsensetype,
			</if>
			<if test='group.contains("media")'>
				media,
			</if>
			<if test='group.contains("first_agent")'>
				first_agent firstAgent,
			</if>
			<if test='group.contains("transferType")'>
				transferType,
			</if>

			coalesce(round(sum(add_ipa),2),0) ipaIncome,
			coalesce(round(sum(add_ipa)/sum(spend),4),0) ipaRoi,

            coalesce(round(sum(add_ipa),2),0) addUserLtv0,
            coalesce(round(sum(add_ipa)/sum(add_user),2),0) addArpu,
            coalesce(round(sum(add_ipa)/sum(spend),4),0.00) aRoi,

			coalesce(sum(add_user),0) as addUser,coalesce(round(sum(spend),2),0) spend,
			coalesce(round(sum(spend)/sum(add_user),2),0) cpa,
			sum(add_user_pay_count) addPurchaseUsers,
			round(sum(spend)/sum(add_user_pay_count),2) addUserPayCost,
			round(sum(add_ipa)/sum(add_user_pay_count),2) addUserPayArpu,
			coalesce(round(sum(add_user_pay_count)/sum(add_user),4),0.00) addUserPayRate
		from
		(<include refid="proxyActualRoiReportBaseSql"/>)
		<if test="group != null and group != ''">
			group by ${group}
		</if>

		<if test="scope != null and scope != ''">
			<if test="index == 'spend'">
				having ifnull(spend,0) <![CDATA[ ${symbol} ]]> #{number}
			</if>
			<if test="index == 'spendNature'">
			having (
        		media = '自然量' OR
        		(media != '自然量' AND ifnull(spend,0) <![CDATA[ ${symbol} ]]> #{number})
    		)
          		</if>
		</if>

		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by day asc,appId asc ,spend desc
			</otherwise>
		</choose>
	</sql>

	<sql id="proxyOfflineRoiReportBaseSql">
		SELECT c.*,a.app_category app_category,a.app_name app_name from dnwx_adt.sdk_union_app_roi_proxy_offline c
		    left join dnwx_adt.app_info a on c.appid=a.id where `day` BETWEEN #{beginDate} AND #{endDate}
		<if test="appId != null and appId != ''">
			and appid in (${appId})
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel})
		</if>
		<if test="media != null and media != ''">
			and media in (${media})
		</if>
		<if test="accountId != null and accountId != ''">
			and account_id in (${accountId})
		</if>
		<if test="putUser != null and putUser != ''">
			and put_user in (${putUser})
		</if>
		<if test="campaignId != null and campaignId != ''">
			and campaign_id = #{campaignId}
		</if>
		<if test="campaignName != null and campaignName != ''">
			and campaign_name = #{campaignName}
		</if>
		<if test="childChannel != null and childChannel != ''">
			and child_channel in (${childChannel})
		</if>
		<if test="groupName != null and groupName != ''">
			and group_name = #{groupName}
		</if>
		<if test="company != null and company != ''">
			and company in (${company})
		</if>
		<if test="adsensePositions != null and adsensePositions.size > 0">
			AND (
			adsensePosition IN
			<foreach collection="adsensePositions" index="index" item="item" open="(" separator="," close=")">
				${item}
			</foreach>
			OR
			<foreach collection="adsensePositions" index="index" item="item" separator="OR">
				adsensePosition LIKE  concat('%',${item},'%')
			</foreach>
			)
		</if>
		<if test="transferType != null and transferType != ''">
			and transferType in (${transferType})
		</if>
		<if test="accountType != null and accountType != ''">
			and account_type in (${accountType})
		</if>
		<if test="adsenseTypes != null and adsenseTypes.size > 0">
			AND (
			adsenseType IN
			<foreach collection="adsenseTypes" index="index" item="item" open="(" separator="," close=")">
				${item}
			</foreach>
<!--			OR-->
<!--			<foreach collection="adsenseTypes" index="index" item="item" separator="OR">-->
<!--				adsenseType LIKE  concat('%',${item},'%')-->
<!--			</foreach>-->
			)
		</if>
		<if test="delivery_mode != null and delivery_mode != ''">
			and delivery_mode = #{delivery_mode}
		</if>
		<if test="anchor_related_type != null and anchor_related_type != ''">
			and anchor_related_type = #{anchor_related_type}
		</if>
		<if test="appCategory != null and appCategory != ''">
			and a.app_category in  (${appCategory})
		</if>
		<if test="firstAgent != null and firstAgent != ''">
			and first_agent in  (${firstAgent})
		</if>

	</sql>

	<sql id="proxyOfflineRoiReportSql">
		SELECT
			<if test='group.contains("day")'>
				`day`,
			</if>
			<if test='group.contains("appId")'>
				appid appId ,
				app_name appName,
			</if>
			<if test='group.contains("accountId")'>
				case when account_id='' or account_id='-1' then '自然量'
					 else account_id
				end as	   accountId,
			</if>
			<if test='group.contains("groupName")'>
				group_name groupName,
			</if>
			<if test='group.contains("campaignName")'>
				campaign_name campaignName,
			</if>
			<if test='group.contains("campaignId")'>
				case when campaign_id='' or campaign_id='-1' then '自然量'
					 else campaign_id
				end as	   campaignId,
			</if>
			<if test='group.contains("appCategory")'>
				app_category appCategory,
			</if>
			<if test='group.contains("adsensePosition")'>
				adsensePosition adsensePosition,
			</if>
			<if test='group.contains("adsenseType")'>
				adsensetype,
			</if>
			<if test='group.contains("media")'>
				media,
			</if>
			<if test='group.contains("first_agent")'>
				first_agent firstAgent,
			</if>
			<if test='group.contains("transferType")'>
				transferType,
			</if>

			coalesce(round(sum(add_ipa),2),0) ipaIncome,
			coalesce(round(sum(add_ipa_7),2),0) ipaIncome7,
			coalesce(round(sum(add_ipa)/sum(spend),4),0) ipaRoi,
			coalesce(round(sum(add_ipa_7)/sum(spend),4),0) ipaRoi7,

            coalesce(round(sum(add_ipa),2),0) addUserLtv0,
            coalesce(round(sum(add_ipa)/sum(add_user),2),0) addArpu,
            coalesce(round(sum(add_ipa)/sum(spend),4),0.00) aRoi,

			coalesce(sum(add_user),0) as addUser,coalesce(round(sum(spend),2),0) spend,
			coalesce(round(sum(spend)/sum(add_user),2),0) cpa,
			sum(add_user_pay_count) addPurchaseUsers,
			round(sum(spend)/sum(add_user_pay_count),2) addUserPayCost,
			round(sum(add_ipa)/sum(add_user_pay_count),2) addUserPayArpu,
			coalesce(round(sum(add_user_pay_count)/sum(add_user),4),0.00) addUserPayRate
		from
		(<include refid="proxyOfflineRoiReportBaseSql"/>)
		<if test="group != null and group != ''">
			group by ${group}
		</if>

		<if test="scope != null and scope != ''">
			<if test="index == 'spend'">
				having ifnull(spend,0) <![CDATA[ ${symbol} ]]> #{number}
			</if>
			<if test="index == 'spendNature'">
			having (
        		media = '自然量' OR
        		(media != '自然量' AND ifnull(spend,0) <![CDATA[ ${symbol} ]]> #{number})
    		)
          		</if>
		</if>

		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by day asc,appId asc ,spend desc
			</otherwise>
		</choose>
	</sql>

	<select id="proxyActualRoiReport" resultType="com.wbgame.pojo.jettison.vo.AdRoiVo">
		<include refid="proxyAcutalRoiReportSql"/>
	</select>

	<select id="proxyActualRoiReportTotal" resultType="com.wbgame.pojo.jettison.vo.AdRoiVo">
	select
	COALESCE( round( sum( ipaIncome ), 2 ), 0 ) ipaIncome,
	COALESCE ( round( sum( ipaIncome )/ sum( spend ), 4 ), 0 ) ipaRoi,
	COALESCE ( round( sum( ipaIncome ), 2 ), 0 ) addUserLtv0,
	COALESCE ( round( sum( ipaIncome )/ sum( addUser ), 2 ), 0 ) addArpu,
	COALESCE ( round( sum( ipaIncome )/ sum( spend ), 4 ), 0.00 ) aRoi,
	COALESCE ( sum( addUser ), 0 ) AS addUser,
	COALESCE ( round( sum( spend ), 2 ), 0 ) spend,
	COALESCE ( round( sum( spend )/ sum( addUser ), 2 ), 0 ) cpa,
	sum( addPurchaseUsers ) addPurchaseUsers,
	round( sum( spend )/ sum( addPurchaseUsers ), 2 ) addUserPayCost,
	round( sum( ipaIncome )/ sum( addPurchaseUsers ), 2 ) addUserPayArpu,
	COALESCE ( round( sum( addPurchaseUsers )/ sum( addUser ), 4 ), 0.00 ) addUserPayRate
			from (<include refid="proxyAcutalRoiReportSql"/>) xx
	</select>
	<select id="proxyOfflineRoiReport" resultType="com.wbgame.pojo.jettison.vo.AdRoiVo">
		<include refid="proxyOfflineRoiReportSql"/>
	</select>
	<select id="proxyOfflineRoiReportTotal" resultType="com.wbgame.pojo.jettison.vo.AdRoiVo">
select
	COALESCE( round( sum( ipaIncome ), 2 ), 0 ) ipaIncome,
	COALESCE ( round( sum( ipaIncome )/ sum( spend ), 4 ), 0 ) ipaRoi,
	COALESCE ( round( sum( ipaIncome7 )/ sum( spend ), 4 ), 0 ) ipaRoi7,
	COALESCE ( round( sum( ipaIncome ), 2 ), 0 ) addUserLtv0,
	COALESCE ( round( sum( ipaIncome )/ sum( addUser ), 2 ), 0 ) addArpu,
	COALESCE ( round( sum( ipaIncome )/ sum( spend ), 4 ), 0.00 ) aRoi,
	COALESCE ( sum( addUser ), 0 ) AS addUser,
	COALESCE ( round( sum( spend ), 2 ), 0 ) spend,
	COALESCE ( round( sum( spend )/ sum( addUser ), 2 ), 0 ) cpa,
	sum( addPurchaseUsers ) addPurchaseUsers,
	round( sum( spend )/ sum( addPurchaseUsers ), 2 ) addUserPayCost,
	round( sum( ipaIncome )/ sum( addPurchaseUsers ), 2 ) addUserPayArpu,
	COALESCE ( round( sum( addPurchaseUsers )/ sum( addUser ), 4 ), 0.00 ) addUserPayRate
			from (<include refid="proxyOfflineRoiReportSql"/>) xx
	</select>

	<sql id="actula_hour_roi_sql">
		SELECT c.*,a.app_category app_category,a.app_name app_name from hourly_realtime_roi_report c left join dnwx_adt.app_info a on c.appid=a.id where day BETWEEN #{beginDate} AND #{endDate} 
		<if test="hourly != null and hourly != ''">
			and hourly in (${hourly})
		</if>
		<if test="appId != null and appId != ''">
			and appid in (${appId})
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel})
		</if>
		<if test="media != null and media != ''">
			and media in (${media})
		</if>
		<if test="accountId != null and accountId != ''">
			and account_id in (${accountId})
		</if>
		<if test="putUser != null and putUser != ''">
			and put_user in (${putUser})
		</if>
		<if test="campaignId != null and campaignId != ''">
			and campaign_id = #{campaignId}
		</if>
		<if test="campaignName != null and campaignName != ''">
			and campaign_name = #{campaignName}
		</if>
		<if test="childChannel != null and childChannel != ''">
			and child_channel in (${childChannel})
		</if>
		<if test="groupName != null and groupName != ''">
			and group_name = #{groupName}
		</if>
		<if test="company != null and company != ''">
			and company in (${company})
		</if>
		<if test="adsensePositions != null and adsensePositions.size > 0">
			AND (
			adsense_position IN
			<foreach collection="adsensePositions" index="index" item="item" open="(" separator="," close=")">
				${item}
			</foreach>
			OR
			<foreach collection="adsensePositions" index="index" item="item" separator="OR">
				adsense_position LIKE  concat('%',${item},'%')
			</foreach>
			)
		</if>
		<if test="strategy != null and strategy != ''">
			and strategy in (${strategy})
		</if>
		<if test="accountType != null and accountType != ''">
			and account_type in (${accountType})
		</if>
		<if test="adsenseTypes != null and adsenseTypes.size > 0">
			AND (
			adsenseType IN
			<foreach collection="adsenseTypes" index="index" item="item" open="(" separator="," close=")">
				${item}
			</foreach>
			OR
			<foreach collection="adsenseTypes" index="index" item="item" separator="OR">
				adsenseType LIKE  concat('%',${item},'%')
			</foreach>
			)
		</if>
		<if test="delivery_mode != null and delivery_mode != ''">
			and delivery_mode = #{delivery_mode}
		</if>
		<if test="anchor_related_type != null and anchor_related_type != ''">
			and anchor_related_type = #{anchor_related_type}
		</if>
		<if test="appCategory != null and appCategory != ''">
			and a.app_category in  (${appCategory})
		</if>
		<if test="scope != null and scope != ''">
			<if test="index == 'spend'">
				and spend <![CDATA[ ${symbol} ]]> #{number}
			</if>
		</if>
	</sql>
</mapper>