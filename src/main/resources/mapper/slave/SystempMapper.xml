<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave.SysTempMapper">
    <!-- ``` 

        系统服务设置-查询 serviceSetCkRun ID_201116112604
        -系统服务设置-新增 serviceSetAddRun ID_201116112616
        -系统服务设置-编辑 serviceSetEditRun ID_201116112641
        -系统服务设置-删除 serviceSetDelRun ID_201116112639

        ===已完成===
        权限管理-查询 orgGrepSetCkRun ID_201109121437
        -权限管理-新增 orgGrepSetAddRun ID_201109121456
        -权限管理-编辑 orgGrepSetEditRun ID_201109121457
        -权限管理-删除 orgGrepSetDelRun ID_201109121459
        菜单管理-查询 logInMenuCkRun ID_201026114507
        菜单管理-新增 logInMenuAddRun ID_201026114510
        菜单管理-编辑 logInMenuEditRun ID_201026114511
        菜单管理-删除 logInMenuDelRun ID_201026114513
        获取服务中心数据 getService ID_201021160415
        用户登陆查询接口 selectSysUser ID_201020152124
        获取权限数据 selectSysOrg ID_201020160507
        获取菜单数据 selectSysMenu ID_201020160509
     -->

     <!-- 用户登陆查询接口 selectSysUser ID_201020152124 -->
    <select id="selectSysUser" parameterType="java.util.Map" resultType="com.wbgame.pojo.SysTempVo">
        SELECT 
        `login_name`, `nick_name`, `org_id`, `org_name`, `role_id`, `role_name`, `platform_id`, `company`,password
        FROM wb_sys_user 
		WHERE login_name = #{login_name} AND password = #{password}
    </select>
    
     <!-- 获取权限数据 selectSysOrg ID_201020160507 -->
    <select id="selectSysOrg" parameterType="java.util.Map" resultType="com.wbgame.pojo.SysTempVo">
        SELECT `org_id`, `org_name`, `sys_type`, `hidden_menu_list`, `page_list`, `orderTime` FROM `wb_sys_org_v2` WHERE org_id = #{org_id}
    </select>
    
     <!-- 获取菜单数据 selectSysMenu ID_201020160509 -->
    <select id="selectSysMenu" parameterType="java.util.Map" resultType="com.wbgame.pojo.SysTempVo">
        SELECT `index`, `title`, `off`, `icon`, `slot`, `list_number` FROM wb_sys_menu_v2 where `index` IN 
		<foreach item="page_list" collection="array" open="(" separator="," close=")">
			#{page_list}
		</foreach>
		ORDER BY `list_number` DESC
    </select>
    
     <!-- 获取服务中心数据 getService ID_201021160415 -->
    <select id="getService" parameterType="java.util.Map" resultType="com.wbgame.pojo.SysTempVo">
        SELECT 
        `sys_mark`,
        `title`,
        `name`,
        `text`,
        `tip`,
        `version`,
        `icon`,
        `verify_link`,
        `link`,
        `dep_verify_link`,
        `dep_link`,
        `test_verify_link`,
        `test_link`,
        `user`,
        `list_number`
        FROM `wb_sys_service_page` 
        ORDER BY `list_number` ASC
    </select>
    
     <!-- 菜单管理-查询 logInMenuCkRun ID_201026114507 -->
    <select id="logInMenuCkRun" parameterType="java.util.Map" resultType="com.wbgame.pojo.SysTempVo">
        SELECT 
        `index`, `title`, `off`, `icon`, `slot`, `list_number`
        FROM `wb_sys_menu_v2` WHERE 1=1
        <if test="index != null and index !=''" >AND `index` = #{index}</if>
        <if test="title != null and title !=''" >AND `title` LIKE CONCAT('%',#{title},'%')</if>
        <if test="off != null and off !=''" >AND `off` = #{off}</if>
        <if test="icon != null and icon !=''" >AND `icon` LIKE CONCAT('%',#{icon},'%')</if>
        <if test="slot != null and slot !=''" >AND `slot` LIKE CONCAT('%',#{slot},'%')</if>
        ORDER BY `list_number` ASC
    </select>
    
    <!-- 菜单管理-新增 logInMenuAddRun ID_201026114510 -->
    <insert id="logInMenuAddRun" parameterType="java.util.Map">
        INSERT INTO `wb_sys_menu_v2` (
            `index`
            ,`title`
            ,`off`
            <if test="icon != null and icon != ''">,`icon`</if>
            <if test="slot != null and slot != ''">,`slot`</if>
            ,`list_number`
        ) VALUES (
            '${index}'
            ,'${title}'
            ,'${off}'
            <if test="icon != null and icon != ''">,'${icon}'</if>
            <if test="slot != null and slot != ''">,'${slot}'</if>
            ,'${list_number}'
        )
    </insert>
    
    <!-- 菜单管理-编辑 logInMenuEditRun ID_201026114511 -->
    <update id="logInMenuEditRun" parameterType="java.util.Map">
		UPDATE `wb_sys_menu_v2` 
		<set>
			<if test="title != null and title != ''" >title = #{title},</if>
			<if test="off != null and off != ''" >off = #{off},</if>
			<if test="icon != null and icon != ''" >icon = #{icon},</if>
			<if test="slot != null and slot != ''" >slot = #{slot},</if>
			<if test="list_number != null and list_number != ''" >list_number = #{list_number},</if>
		</set>
		<where>
            `index` = #{index}
		</where>
    </update>
    
    <!-- 菜单管理-删除 logInMenuDelRun ID_201026114513 -->
    <delete id="logInMenuDelRun" parameterType="com.wbgame.pojo.SysTempVo">
		DELETE FROM `wb_sys_menu_v2` WHERE (`index`='${index}')
    </delete>
    
    <!-- 权限管理-查询 orgGrepSetCkRun ID_201109121437 -->
    <select id="orgGrepSetCkRun" parameterType="java.util.Map" resultType="com.wbgame.pojo.SysTempVo">
        SELECT 
        `org_id`, `org_name`, `sys_type`, `hidden_menu_list`, `page_list`, `orderTime`
        FROM `wb_sys_org_v2` WHERE 1=1
        <if test="org_id != null and org_id !=''" >AND `org_id` = #{org_id}</if>
        <if test="org_name != null and org_name !=''" >AND `org_name` LIKE CONCAT('%',#{org_name},'%')</if>
        <if test="sys_type != null and sys_type !=''" >AND `sys_type` = #{sys_type}</if>
        <if test="hidden_menu_list != null and hidden_menu_list !=''" >AND `hidden_menu_list` LIKE CONCAT('%',#{hidden_menu_list},'%')</if>
        <if test="page_list != null and page_list !=''" >AND `page_list` LIKE CONCAT('%',#{page_list},'%')</if>
        ORDER BY `orderTime` ASC
    </select>

    <!-- -权限管理-新增 orgGrepSetAddRun ID_201109121456 -->
    <insert id="orgGrepSetAddRun" parameterType="java.util.Map">
        INSERT INTO `wb_sys_org_v2` (
            `org_id`
            ,`org_name`
            <if test="sys_type != null and sys_type != ''">,`sys_type`</if>
            <if test="hidden_menu_list != null and hidden_menu_list != ''">,`hidden_menu_list`</if>
            <if test="page_list != null and page_list != ''">,`page_list`</if>
            ,`orderTime`
        ) VALUES (
            '${org_id}'
            ,'${org_name}'
            <if test="sys_type != null and sys_type != ''">,'${sys_type}'</if>
            <if test="hidden_menu_list != null and hidden_menu_list != ''">,'${hidden_menu_list}'</if>
            <if test="page_list != null and page_list != ''">,'${page_list}'</if>
            ,unix_timestamp()
        )
    </insert>

    <!-- -权限管理-编辑 orgGrepSetEditRun ID_201109121457 -->
    <update id="orgGrepSetEditRun" parameterType="java.util.Map">
		UPDATE `wb_sys_org_v2` 
		<set>
			<if test="org_name != null and org_name != ''" >org_name = #{org_name},</if>
			<if test="sys_type != null and sys_type != ''" >sys_type = #{sys_type},</if>
			<if test="hidden_menu_list != null and hidden_menu_list != ''" >hidden_menu_list = #{hidden_menu_list},</if>
			<if test="page_list != null and page_list != ''" >page_list = #{page_list},</if>
		</set>
		<where>
            `org_id` = #{org_id}
		</where>
    </update>

    <!-- -权限管理-删除 orgGrepSetDelRun ID_201109121459 -->
    <delete id="orgGrepSetDelRun" parameterType="com.wbgame.pojo.SysTempVo">
		DELETE FROM `wb_sys_org_v2` WHERE (`org_id`='${org_id}')
    </delete>

    <!-- 系统服务设置-查询 serviceSetCkRun ID_201116112604 -->
    <select id="serviceSetCkRun" parameterType="java.util.Map" resultType="com.wbgame.pojo.SysTempVo">
        SELECT 
            `sys_mark`,
            `title`,
            `name`,
            `text`,
            `version`,
            `icon`,
            `link`,
            `verify_link`,
            `dep_link`,
            `dep_verify_link`,
            `test_link`,
            `test_verify_link`,
            `list_number`,
            `tip`,
            `user`
        FROM `wb_sys_service_page` WHERE 1=1
        <if test="sys_mark != null and sys_mark !=''" >AND `sys_mark` = #{sys_mark}</if>
        <if test="title != null and title !=''" >AND `title` LIKE CONCAT('%',#{title},'%')</if>
        <if test="name != null and name !=''" >AND `name` LIKE CONCAT('%',#{name},'%')</if>
        <if test="text != null and text !=''" >AND `text` LIKE CONCAT('%',#{text},'%')</if>
        <if test="version != null and version !=''" >AND `version` = #{version}</if>
        <if test="icon != null and icon !=''" >AND `icon` = #{icon}</if>
        <if test="link != null and link !=''" >AND `link` LIKE CONCAT('%',#{link},'%')</if>
        <if test="verify_link != null and verify_link !=''" >AND `verify_link` LIKE CONCAT('%',#{verify_link},'%')</if>
        <if test="dep_link != null and dep_link !=''" >AND `dep_link` LIKE CONCAT('%',#{dep_link},'%')</if>
        <if test="dep_verify_link != null and dep_verify_link !=''" >AND `dep_verify_link` LIKE CONCAT('%',#{dep_verify_link},'%')</if>
        <if test="test_link != null and test_link !=''" >AND `test_link` LIKE CONCAT('%',#{test_link},'%')</if>
        <if test="test_verify_link != null and test_verify_link !=''" >AND `test_verify_link` = #{test_verify_link}</if>
        <if test="list_number != null and list_number !=''" >AND `list_number` = #{list_number}</if>
        <if test="tip != null and tip !=''" >AND `tip` = #{tip}</if>
        <if test="user != null and user !=''" >AND `user` = #{user}</if>
        ORDER BY `list_number` DESC
    </select>

    <!-- -系统服务设置-新增 serviceSetAddRun ID_201116112616 -->
    <insert id="serviceSetAddRun" parameterType="java.util.Map">
        INSERT INTO `wb_sys_service_page` (
            `sys_mark`
            <if test="title != null and title != ''">,`title`</if>
            <if test="name != null and name != ''">,`name`</if>
            <if test="text != null and text != ''">,`text`</if>
            <if test="version != null and version != ''">,`version`</if>
            <if test="icon != null and icon != ''">,`icon`</if>
            <if test="verify_link != null and verify_link != ''">,`verify_link`</if>
            <if test="link != null and link != ''">,`link`</if>
            <if test="dep_verify_link != null and dep_verify_link != ''">,`dep_verify_link`</if>
            <if test="dep_link != null and dep_link != ''">,`dep_link`</if>
            <if test="test_verify_link != null and test_verify_link != ''">,`test_verify_link`</if>
            <if test="test_link != null and test_link != ''">,`test_link`</if>
            <if test="list_number != null and list_number != ''">,`list_number`</if>
            <if test="tip != null and tip != ''">,`tip`</if>
            <if test="user != null and user != ''">,`user`</if>
            <!-- ,`orderTime` -->
        ) VALUES (
            '${sys_mark}'
            <if test="title != null and title != ''">,'${title}'</if>
            <if test="name != null and name != ''">,'${name}'</if>
            <if test="text != null and text != ''">,'${text}'</if>
            <if test="version != null and version != ''">,'${version}'</if>
            <if test="icon != null and icon != ''">,'${icon}'</if>
            <if test="verify_link != null and verify_link != ''">,'${verify_link}'</if>
            <if test="link != null and link != ''">,'${link}'</if>
            <if test="dep_verify_link != null and dep_verify_link != ''">,'${dep_verify_link}'</if>
            <if test="dep_link != null and dep_link != ''">,'${dep_link}'</if>
            <if test="test_verify_link != null and test_verify_link != ''">,'${test_verify_link}'</if>
            <if test="test_link != null and test_link != ''">,'${test_link}'</if>
            <if test="list_number != null and list_number != ''">,'${list_number}'</if>
            <if test="tip != null and tip != ''">,'${tip}'</if>
            <if test="user != null and user != ''">,'${user}'</if>
            <!-- ,unix_timestamp() -->
        )
    </insert>

    <!-- -系统服务设置-编辑 serviceSetEditRun ID_201116112641-->
    <update id="serviceSetEditRun" parameterType="java.util.Map">
		UPDATE `wb_sys_service_page` 
		<set>
			<if test="title != null and title != ''" >title = #{title},</if>
			<if test="name != null and name != ''" >name = #{name},</if>
			<if test="text != null and text != ''" >text = #{text},</if>
			<if test="version != null and version != ''" >version = #{version},</if>
			<if test="icon != null and icon != ''" >icon = #{icon},</if>
			<if test="verify_link != null" >verify_link = #{verify_link},</if>
			<if test="link != null" >link = #{link},</if>
			<if test="dep_verify_link != null" >dep_verify_link = #{dep_verify_link},</if>
			<if test="dep_link != null" >dep_link = #{dep_link},</if>
			<if test="test_verify_link != null" >test_verify_link = #{test_verify_link},</if>
			<if test="test_link != null" >test_link = #{test_link},</if>
			<if test="list_number != null and list_number != ''" >list_number = #{list_number},</if>
			<if test="tip != null and tip != ''" >tip = #{tip},</if>
			<if test="user != null and user != ''" >user = #{user},</if>
		</set>
		<where>
            `sys_mark` = #{sys_mark}
		</where>
    </update>

    <!-- -系统服务设置-删除 serviceSetDelRun ID_201116112639-->
    <delete id="serviceSetDelRun" parameterType="com.wbgame.pojo.SysTempVo">
		DELETE FROM `wb_sys_service_page` WHERE (`sys_mark`='${sys_mark}')
    </delete>

    
</mapper>