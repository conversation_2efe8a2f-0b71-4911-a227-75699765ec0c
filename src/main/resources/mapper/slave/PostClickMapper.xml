<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave.PostClickMapper">

	<insert id="insertPostClickTotal" parameterType="com.wbgame.pojo.PostClickTotalVo">
		insert into post_click_total(
			appid,
			lsn,
			clickid,
			event_type,
			create_time,
			last_time
		) values
		
		<foreach collection="list" item="li" separator=",">
			(
				#{li.appid},
				#{li.lsn},
				#{li.clickid},
				#{li.event_type},
				now(),
				now()
			)
		</foreach>	
		ON DUPLICATE KEY UPDATE
		last_time = VALUES(last_time)
	</insert>
	
	<insert id="insertPostClickToUserList" parameterType="java.util.Map">
		insert into post_user_list(appid,lsn,clickid,event_type,iscommit,create_time)

		select aa.appid,aa.lsn,aa.clickid,aa.event_type,'0',now() from
		(SELECT appid,lsn,clickid,event_type FROM post_click_total 
			where DATE_FORMAT(create_time,'%Y%m%d') = '${today}') aa
		
		join (select appid,lsn from app_login_${today} group by appid,lsn) bb
		on aa.appid = bb.appid and aa.lsn = bb.lsn
		
		ON DUPLICATE KEY UPDATE
		lsn = VALUES(lsn)
	</insert>
	
	<update id="updatePostUserListForCommit" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update post_user_list set iscommit = 1 
	    	where appid = #{li.appid} and lsn = #{li.lsn}
    	</foreach>
	</update>
	
</mapper>