<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave.WaiBaoSysMapper">

	<select id="selectWbSysUser" parameterType="java.util.Map" resultType="com.wbgame.pojo.WaibaoUserVo">
		select * from waibao_sys_user 
		where login_name = #{login_name} and password = #{password}
	</select>
	<select id="selectWbSysUserInfoList" parameterType="com.wbgame.pojo.WaibaoUserVo" 
					resultType="com.wbgame.pojo.WaibaoUserVo">
					
		select * from waibao_sys_user where 1=1
		<if test="login_name != null and login_name != ''">
			and login_name = #{login_name}
		</if>
		<if test="nick_name != null and nick_name != ''">
			and nick_name like concat('%',#{nick_name},'%')
		</if>
	</select>
	<select id="selectWbSysRoleInfoList" parameterType="com.wbgame.pojo.WaibaoUserVo" 
					resultType="com.wbgame.pojo.WaibaoUserVo">
					
		select * from waibao_sys_role where 1=1
		<if test="role_name != null and role_name != ''">
			and role_name like '%${role_name}%'
		</if>
	</select>

	<insert id="insertWbSysUser" parameterType="com.wbgame.pojo.WaibaoUserVo">
		insert into waibao_sys_user(
				login_name,
				password,
				nick_name,
				role_id,
				role_name,
				app_group,
				email,
				phone,
				status,
				date

		) values(
				#{login_name},
				#{password},
				#{nick_name},
				#{role_id},
				#{role_name},
				#{app_group},
				#{email},
				#{phone},
				#{status},
				now()
		)
	</insert>
	<update id="updateWbSysUser" parameterType="com.wbgame.pojo.WaibaoUserVo">
		update waibao_sys_user set 
			<if test="password != null and password != ''">
				password = #{password},
			</if>
			nick_name = #{nick_name},
			role_id = #{role_id},
			role_name = #{role_name},
			email = #{email},
			phone = #{phone},
			status = #{status}
		where login_name = #{login_name}
	</update>
	<delete id="deleteWbSysUser" parameterType="com.wbgame.pojo.WaibaoUserVo">
		delete from waibao_sys_user where login_name = #{login_name}
	</delete>
	
	
	<insert id="insertWbSysRole" parameterType="com.wbgame.pojo.WaibaoUserVo">
		insert into waibao_sys_role(
				role_name,
				app_group
		) values(
				#{role_name},
				#{app_group}
		)
	</insert>
	<update id="updateWbSysRole" parameterType="com.wbgame.pojo.WaibaoUserVo">
		update waibao_sys_role set 
			role_name = #{role_name},
			app_group = #{app_group}
		where role_id = #{role_id}
	</update>
	<delete id="deleteWbSysRole" parameterType="com.wbgame.pojo.WaibaoUserVo">
		delete from waibao_sys_role where role_id = #{role_id}
	</delete>
	
</mapper>