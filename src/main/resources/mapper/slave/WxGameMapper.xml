<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave.WxGameMapper" >
  <insert id="insertGameAdInfo" parameterType="java.util.List" >
    insert into game_advertisement (c_date, c_game, channel, 
      amount, add_num, dau, 
      income, distribution)
    values 
      <foreach collection="list" item="lpc" separator=",">
			(#{lpc.cDate}, 
			 #{lpc.cGame},
			 #{lpc.channel}, 
      		 #{lpc.amount},
      		 #{lpc.addNum}, 
      		 #{lpc.dau}, 
      		 #{lpc.income}, 
      		 #{lpc.distribution}   		 
      		 )
		</foreach>ON DUPLICATE KEY UPDATE 
			<!-- c_date = VALUES(c_date),
			c_game = VALUES(c_game),
			channel = VALUES(channel) -->
			amount = VALUES(amount),
			add_num = VALUES(add_num),
			dau = VALUES(dau),
			income = VALUES(income),
			distribution = VALUES(distribution)
			
  </insert>
  
  
  <!-- 合作方查询界面 -->
   <select id="selectGameAdInfo" resultType="com.wbgame.pojo.GameAdInfo" >
    select 
    c_date AS cDate,
	c_game AS cGame,
	channel,
	sum( amount ) amount,
	sum( add_num ) AS addNum,
	sum( dau ) dau,
	sum( income ) income,
	sum( distribution ) distribution,
	round( sum( amount ) / sum( add_num ) , 2 ) AS cpa,
	round( sum( income ) / sum( dau ), 2 ) AS dauARpu1,
	round( sum( distribution ) / sum( dau ), 2 ) AS dauARpu2,
	round(( sum( amount ) + sum( distribution )) / sum( dau ), 2 ) AS dauARpu3 
    from game_advertisement a
    where 1=1
    <if test="cGame != null and cGame != ''">
		and a.c_game = #{cGame}
	</if>
	<if test = "startTime != null and endTime != null">
		and a.c_date BETWEEN #{startTime} AND #{endTime}
	</if>
	<if test="channel != null and channel != ''">
		and a.channel = #{channel}
	</if>
	<if test="appid != null and appid != ''">
		and a.c_game in (${appid})
	</if>
	
	<!-- 屏蔽11月1日以后数据  -->
	<if test="1==1">
		and (a.c_game != 37948 or a.c_date &lt; '2020-11-01')
		and (a.c_game != 37829 or a.c_date &lt; '2021-03-01')
		
		and (a.c_game != 37832 or a.c_date &lt; '2021-08-01') 
		and (a.c_game != 37830 or a.c_date &lt; '2021-08-01') 
		and (a.c_game != 37840 or a.c_date &lt; '2021-08-01') 
		and (a.c_game != 37890 or a.c_date &lt; '2021-08-01') 
		and (a.c_game != 37909 or a.c_date &lt; '2021-08-01') 
		
		and (a.c_game != 38005 or a.c_date &lt; '2021-08-01') 
		
		and (a.c_game != 38044 or DATE_FORMAT(a.c_date,'%Y-%m') = '2021-09') 
		and (a.c_game != 37953 or a.c_date &lt; '2021-09-07') 
	</if>
	GROUP BY
		DATE( c_date ),
		c_game
  </select>
  
    <!-- 合作方查询界面 -->
   <select id="selectSumGameAdInfo" resultType="com.wbgame.pojo.GameAdInfo" >
    select 
	sum( amount ) amount,
	sum( add_num ) AS addNum,
	sum( dau ) dau,
	sum( income ) income
    from game_advertisement a
    where 1=1
    <if test="cGame != null and cGame != ''">
		and a.c_game = #{cGame}
	</if>
	<if test = "startTime != null and endTime != null">
		and a.c_date BETWEEN #{startTime} AND #{endTime}
	</if>
	<if test="channel != null and channel != ''">
		and a.channel = #{channel}
	</if>
	<if test="appid != null and appid != ''">
		and a.c_game in (${appid})
	</if>
	<if test="1==1">
		and (a.c_game != 37948 or a.c_date &lt; '2020-11-01')
		and (a.c_game != 37829 or a.c_date &lt; '2021-03-01')
		
		and (a.c_game != 37832 or a.c_date &lt; '2021-08-01') 
		and (a.c_game != 37830 or a.c_date &lt; '2021-08-01') 
		and (a.c_game != 37840 or a.c_date &lt; '2021-08-01') 
		and (a.c_game != 37890 or a.c_date &lt; '2021-08-01') 
		and (a.c_game != 37909 or a.c_date &lt; '2021-08-01') 
		
		and (a.c_game != 38005 or a.c_date &lt; '2021-08-01') 
		
		and (a.c_game != 38044 or DATE_FORMAT(a.c_date,'%Y-%m') = '2021-09') 
		and (a.c_game != 37953 or a.c_date &lt; '2021-09-07') 
	</if>
  </select>
  
  
  <update id="updateGameAdInfo" parameterType="com.wbgame.pojo.GameAdInfo" >
    update game_advertisement
    set 
      amount = #{amount},
      add_num = #{addNum},
      dau = #{dau},
      income = #{income},
      distribution = #{distribution}
    where c_date = #{cDate} and channel = #{channel} and c_game = #{cGame}
  </update>
  
  <delete id="deleteGameAdInfo" parameterType="com.wbgame.pojo.GameAdInfo"  >
    delete from game_advertisement
    where c_date = #{cDate}
  </delete>
  
  <!-- 小游戏广告数据信息查询 -->
   <select id="selectGameAdInfoList" resultType="com.wbgame.pojo.GameAdInfo" >
    select 
    <if test="hide == 1">
    c_date AS cDate,
	c_game AS cGame,
	channel,
	amount,
	add_num  AS addNum,
	dau,
	income,
	distribution,
	round(amount / add_num  , 2 ) AS cpa,
	round(income  / dau , 2 ) AS dauARpu1,
	round(distribution / dau , 2 ) AS dauARpu2,
	round(( amount  + distribution ) /  dau , 2 ) AS dauARpu3 
	 </if >
	<if test="hide != 1">
	c_date AS cDate,
	c_game AS cGame,
	sum( amount ) amount,
	sum( add_num ) AS addNum,
	sum( dau ) dau,
	sum( income ) income,
	sum( distribution ) distribution,
	round( sum( amount ) / sum( add_num ) , 2 ) AS cpa,
	round( sum( income ) / sum( dau ), 2 ) AS dauARpu1,
	round( sum( distribution ) / sum( dau ), 2 ) AS dauARpu2,
	round(( sum( amount ) + sum( distribution )) / sum( dau ), 2 ) AS dauARpu3 
	</if >
    from game_advertisement a
    where 1=1
    <if test="cGame != null and cGame != ''">
		and a.c_game = #{cGame}
	</if>
	<if test = "startTime != null and endTime != null">
		and a.c_date BETWEEN #{startTime} AND #{endTime}
	</if>
	<if test="channel != null and channel != ''">
		and a.channel = #{channel}
	</if>
	<if test="appid != null and appid != ''">
		and a.c_game in (${appid})
	</if>
	<if test="hide != 1">
	GROUP BY DATE( c_date ),
		c_game
	</if >
  </select>
  
  
  
   <insert id="sycGameAdInfo" parameterType="java.util.List" >
    insert into game_advertisement (c_date, c_game, channel, 
      amount, add_num, dau,income)
    values 
      <foreach collection="list" item="lpc" separator=",">
			(#{lpc.cDate}, 
			 #{lpc.cGame},
			 #{lpc.channel}, 
      		 #{lpc.amount},
      		 #{lpc.addNum}, 
      		 #{lpc.dau}, 
      		 #{lpc.income}	 
      		 )
		</foreach>ON DUPLICATE KEY UPDATE 
			amount = VALUES(amount),
			add_num = VALUES(add_num),
			dau = VALUES(dau),
			income = VALUES(income)
  </insert>
  
  
   <!-- 卖量收入报表 -->
  <update id="updateAppExchangeVolume" parameterType="com.wbgame.pojo.AppExchangeVolume" >
    update app_exchange_volume_sell
    set tdate = #{tdate,jdbcType=VARCHAR},
      channel_name = #{channelName,jdbcType=VARCHAR},
      game_name = #{gameName,jdbcType=VARCHAR},
      appid = #{appid,jdbcType=VARCHAR},
      org_channel = #{orgChannel,jdbcType=VARCHAR},
      org_game_name = #{orgGameName,jdbcType=VARCHAR},
      org_appid = #{orgAppid,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      channel = #{channel,jdbcType=INTEGER},
      derived_revenue = #{derivedRevenue,jdbcType=DECIMAL},
      pricing_type = #{pricingType,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      platform_url = #{platformUrl,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <!-- 卖量收入报表 -->
  <insert id="insertAppExchangeVolume" parameterType="java.util.List">
    insert into app_exchange_volume_sell  ( tdate,
    							  channel_name, 
							      game_name, 
							      appid,
							      org_channel, 
							      org_game_name, 
							      org_appid,
							       price, 
							      channel, 
							      derived_revenue, 
							      pricing_type, 
							      platform, 
							      platform_url)
      values
      <foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.channelName},
			#{li.gameName},
			#{li.appid},
			#{li.orgChannel},
			#{li.orgGameName},
			#{li.orgAppid},
			#{li.price},
			#{li.channel},
			#{li.derivedRevenue},
			#{li.pricingType},
			#{li.platform},
			#{li.platformUrl})
		</foreach>
    
  </insert>
  
  <!-- 卖量收入报表 -->
  <select id="selectAppExchangeVolume" resultType="com.wbgame.pojo.AppExchangeVolume" >
    select 
   	id, tdate, channel_name AS channelName, c.appname AS gameName, a.appid, org_channel AS orgChannel, org_game_name AS orgGameName , org_appid AS orgAppid, price, 
   	 channel, derived_revenue AS derivedRevenue, pricing_type AS pricingType,  platform, platform_url AS  platformUrl
    from app_exchange_volume_sell  a left join app_exchange_name_config c on a.appid = c.appid
    where 1=1
	<if test ="startTime != null and endTime != null">
		and a.tdate BETWEEN #{startTime} AND #{endTime}
	</if>
	<if test="channelName != null and channelName != ''">
		and a.channel_name = #{channelName}
	</if>
	<if test="gameName != null and gameName != ''">
		and c.appname = #{gameName}
	</if>
	<if test="appid != null and appid != ''">
		and a.appid = #{appid}
	</if>
	<if test="orgChannel != null and orgChannel != ''">
		and a.org_channel = #{orgChannel}
	</if>
	<if test="orgGameName != null and orgGameName != ''">
		and a.org_game_name = #{orgGameName}
	</if>
	<if test="pricingType != null and pricingType != ''">
		and a.pricing_type = #{pricingType}
	</if>
  </select>
  
  <!-- 卖量收入报表 -->
  <select id="selectAppExchangeSell" resultType="com.wbgame.pojo.AppExchangeVolume" >
    select round(avg(price),2) price , sum(channel) AS channel, sum(derived_revenue) AS derivedRevenue
    from app_exchange_volume_sell  a left join app_exchange_name_config c on a.appid = c.appid
    where 1=1
	<if test ="startTime != null and endTime != null">
		and a.tdate BETWEEN #{startTime} AND #{endTime}
	</if>
	<if test="channelName != null and channelName != ''">
		and a.channel_name = #{channelName}
	</if>
	<if test="gameName != null and gameName != ''">
		and c.appname = #{gameName}
	</if>
	<if test="appid != null and appid != ''">
		and a.appid = #{appid}
	</if>
	<if test="orgChannel != null and orgChannel != ''">
		and a.org_channel = #{orgChannel}
	</if>
	<if test="orgGameName != null and orgGameName != ''">
		and a.org_game_name = #{orgGameName}
	</if>
	<if test="pricingType != null and pricingType != ''">
		and a.pricing_type = #{pricingType}
	</if>
  </select>
  
  <!-- 卖量收入报表 -->
  <select id="selectGameNameSell" resultType="String" >
     select 
    DISTINCT appname AS gameName
    from app_exchange_name_config  a  where appname !=""
  </select>
  
   <!-- 卖量收入报表 -->
  <select id="selectAppidSell" resultType="String" >
    select 
    DISTINCT  appid 
    from app_exchange_name_config  a  where appid !=""
  </select>
  
   <!-- 卖量收入报表 -->
  <select id="selectOrgChannelSell" resultType="String" >
    select 
    DISTINCT org_channel AS orgChannel
    from app_exchange_volume_sell  a where org_channel !=""
  </select>
  
   <!-- 卖量收入报表 -->
  <select id="selectOrgGameNameSell" resultType="String" >
    select 
    DISTINCT org_game_name AS orgGameName
    from app_exchange_volume_sell  a where org_game_name !=""
  </select>
  
  <select id="selectChannelNameSell" resultType="String" >
    select 
    DISTINCT channel_name AS channelName
    from app_exchange_volume_sell  a where channel_name !=""
  </select>
  
  <select id="selectPricingTypeSell" resultType="String" >
    select 
    DISTINCT pricing_type AS pricingType
    from app_exchange_volume_sell  a  where pricing_type !=""
  </select>
  
   <delete id="deleteAppExchangeVolumeSell" parameterType="String" >
  delete from app_exchange_volume_sell where id in (${ids})
  </delete>
  
  <insert id="insertAppChannelAppidInfo" parameterType="java.util.List" >
    insert into app_channel_appid (tdate, channel, appid, appid_key,
      add_num, act_num, income, times,
      `two_rate`, `ban_income`, `screen_income`, `traffic_income`, 
      `video_income`, `ban_pv`, `screen_pv`, `traffic_pv`, `video_pv`,`open_income`, `open_pv`
      )
    values 
    <foreach collection="list" item="li" separator=",">
	(#{li.tdate}, #{li.channel}, #{li.appid},#{li.appidKey},  
      #{li.addNum}, #{li.actNum}, #{li.income},#{li.times},
       #{li.twoRate}, #{li.banIncome},#{li.screenIncome}, #{li.trafficIncome},
        #{li.videoIncome},#{li.banPv}, #{li.screenPv}, #{li.trafficPv},#{li.videoPv}, #{li.openIncome},#{li.openPv}
      )
	</foreach>
   	ON DUPLICATE KEY UPDATE
	add_num = VALUES(add_num),
	act_num = VALUES(act_num),
	income = VALUES(income),
	times = VALUES(times),
	two_rate = VALUES(two_rate),
	ban_income = VALUES(ban_income),
	screen_income = VALUES(screen_income),
	traffic_income = VALUES(traffic_income),
	video_income = VALUES(video_income),
	open_income = VALUES(open_income),
	ban_pv = VALUES(ban_pv),
	screen_pv = VALUES(screen_pv),
	open_pv = VALUES(open_pv),
	traffic_pv = VALUES(traffic_pv),
	video_pv = VALUES(video_pv)
		
  </insert>
  
   <select id="selectAppChannelAppidInfo" resultType="com.wbgame.pojo.AppChannelAppidInfo" >
    select 
   	tdate, channel,appid_key AS appidKey,c.appname AS appid,  add_num AS addNum, act_num AS actNum,
   	CONCAT(round(add_num/act_num*100,2),'%') as addPer,
	income,
   	round(income/act_num,3) as dauArpu,
   	times,
   	two_rate AS twoRate,
   	round(ban_income/act_num,3) as banDauArpu,
   	round(screen_income/act_num,3) as screenDauArpu,
   	round(open_income/act_num,3) as openDauArpu,
   	round(traffic_income/act_num,3) as trafficDauArpu,
   	round(video_income/act_num,3) as videoDauArpu,
	round(ban_pv/act_num,2) as avgBanpv,
   	round(screen_pv/act_num,2) as avgScreenpv,
   	round(open_pv/act_num,2) as avgOpenpv,
   	round(traffic_pv/act_num,2) as avgTrafficpv,
   	round(video_pv/act_num,2) as avgVideopv,
	round(ban_income/ban_pv*1000,2) as banEcpm,
   	round(screen_income/screen_pv*1000,2) as screenEcpm,
   	round(open_income/open_pv*1000,2) as  openEcpm,
   	round(traffic_income/traffic_pv*1000,2) as trafficEcpm,
   	round(video_income/video_pv*1000,2) as videoEcpm,
	`ban_income` AS banIncome, `screen_income` AS screenIncome, `traffic_income` AS trafficIncome, `video_income` AS videoIncome,`open_income` AS openIncome,
	`ban_pv` AS banPv, `screen_pv` AS screenPv, `traffic_pv` AS trafficPv, `video_pv` AS videoPv,`open_pv` AS openPv
    from app_channel_appid a  left join app_exchange_name_config c on a.appid_key = c.appid
    where 1=1
    <if test ="startTime != null and endTime != null">
		and a.tdate BETWEEN #{startTime} AND #{endTime}
	</if>
	<if test="channelName != null and channelName != ''">
		and a.channel_name = #{channelName}
	</if>
	<if test="channel != null and channel != ''">
		and a.channel = #{channel}
	</if>
	<if test="gameName != null and gameName != ''">
		and c.appname = #{gameName}
	</if>
	<if test="appid != null and appid != ''">
		and a.appid_key = #{appid}
	</if>
	</select>
	
	<select id="selectChannelName" resultType="String" >
    select 
    DISTINCT channel 
    from app_channel_appid  a
   </select>
	
	<!-- 买量收入报表 -->
  <update id="updateAppExchangeVolumeBuy" parameterType="com.wbgame.pojo.AppExchangeVolume" >
    update app_exchange_volume_buy
    set tdate = #{tdate,jdbcType=VARCHAR},
      channel_name = #{channelName,jdbcType=VARCHAR},
      game_name = #{gameName,jdbcType=VARCHAR},
      appid = #{appid,jdbcType=VARCHAR},
      org_channel = #{orgChannel,jdbcType=VARCHAR},
      org_game_name = #{orgGameName,jdbcType=VARCHAR},
      org_appid = #{orgAppid,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      channel = #{channel,jdbcType=INTEGER},
      derived_revenue = #{derivedRevenue,jdbcType=DECIMAL},
      pricing_type = #{pricingType,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      platform_url = #{platformUrl,jdbcType=VARCHAR},
      times = #{times,jdbcType=VARCHAR},
      two_rate = #{twoRate,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <!-- 买量收入报表 -->
  <insert id="insertAppExchangeVolumeBuy" parameterType="java.util.List">
    insert into app_exchange_volume_buy ( tdate,
    							  channel_name, 
							      game_name, 
							      appid,
							      org_channel, 
							      org_game_name, 
							      org_appid,
							       price, 
							      channel, 
							      derived_revenue, 
							      pricing_type, 
							      platform, 
							      platform_url,
							      times,
							      two_rate
							      )
      values
      <foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.channelName},
			#{li.gameName},
			#{li.appid},
			#{li.orgChannel},
			#{li.orgGameName},
			#{li.orgAppid},
			#{li.price},
			#{li.channel},
			#{li.derivedRevenue},
			#{li.pricingType},
			#{li.platform},
			#{li.platformUrl},
			#{li.times},
			#{li.twoRate}
			)
		</foreach>
    
  </insert>
  
  <!-- 买量收入报表 -->
  <select id="selectAppExchangeVolumeBuy" resultType="com.wbgame.pojo.AppExchangeVolume" >
    select 
   	id, tdate, channel_name AS channelName, a.game_name as gameName, appid, org_channel AS orgChannel, org_game_name AS orgGameName , org_appid AS orgAppid, price, 
   	 channel, derived_revenue AS derivedRevenue, pricing_type AS pricingType,  platform, platform_url AS  platformUrl,times,two_rate AS twoRate
    from app_exchange_volume_buy a 
    where 1=1
	<if test ="startTime != null and endTime != null">
		and a.tdate BETWEEN #{startTime} AND #{endTime}
	</if>
	<if test="channelName != null and channelName != ''">
		and a.channel_name = #{channelName}
	</if>
	<if test="gameName != null and gameName != ''">
		and a.game_name = #{gameName}
	</if>
	<if test="appid != null and appid != ''">
		and a.appid = #{appid}
	</if>
	<if test="orgChannel != null and orgChannel != ''">
		and a.org_channel = #{orgChannel}
	</if>
	<if test="orgGameName != null and orgGameName != ''">
		and a.org_game_name = #{orgGameName}
	</if>
	
	
  </select>
  
  <delete id="deleteAppExchangeVolumeBuy"  parameterType="String" >
  delete from app_exchange_volume_buy where id in (${ids})
  </delete>
  

 	
 	<!-- 买量汇总 -->
    <select id="selectAppExchangeBuy" resultType="com.wbgame.pojo.AppExchangeVolume" >
    select round(avg(price),2) price , sum(channel) AS channel, sum(derived_revenue) AS derivedRevenue
    from app_exchange_volume_buy a 
    where 1=1
	<if test ="startTime != null and endTime != null">
		and a.tdate BETWEEN #{startTime} AND #{endTime}
	</if>
	<if test="channelName != null and channelName != ''">
		and a.channel_name = #{channelName}
	</if>
	<if test="gameName != null and gameName != ''">
		and a.game_name = #{gameName}
	</if>
	<if test="appid != null and appid != ''">
		and a.appid = #{appid}
	</if>
	<if test="orgChannel != null and orgChannel != ''">
		and a.org_channel = #{orgChannel}
	</if>
	<if test="orgGameName != null and orgGameName != ''">
		and a.org_game_name = #{orgGameName}
	</if>
	<if test="pricingType != null and pricingType != ''">
		and a.pricing_type = #{pricingType}
	</if>
  </select>
  
  <!-- 买量收入报表 -->
  <select id="selectGameNameBuy" resultType="String" >
    select 
    DISTINCT game_name AS gameName
    from app_exchange_volume_buy a where game_name != ""
  </select>
  
   <!-- 买量收入报表 -->
  <select id="selectAppidBuy" resultType="String" >
    select 
    DISTINCT  appid 
    from app_exchange_volume_buy  a where appid !=""
  </select>
  
   <!-- 买量收入报表 -->
  <select id="selectOrgChannelBuy" resultType="String" >
    select 
    DISTINCT org_channel AS orgChannel
    from app_exchange_volume_buy  a where org_channel !=""
  </select>
  
   <select id="selectOrgGameNameBuy" resultType="String" >
    select 
    DISTINCT org_game_name AS orgGameName
    from app_exchange_volume_buy  a where org_game_name !=""
  </select>
  
  <select id="selectChannelNameBuy" resultType="String" >
    select 
    DISTINCT channel_name AS channelName
    from app_exchange_volume_buy  a where channel_name !=""
  </select>
  
  <select id="selectPricingTypeBuy" resultType="String" >
    select 
    DISTINCT pricing_type AS pricingType
    from app_exchange_volume_buy  a  where pricing_type !=""
  </select>
   
  
  <select id="selectAppExchangeVolumeVo"  resultType="com.wbgame.pojo.AppExchangeVolumeVo">
	  select aa.tdate,
	   <if test="hide != 1">
	  aa.channel_name as channelName,c.appname as gameName,aa.appid,
	  </if>
	  b.add_num as addNum ,b.act_num AS actNum
	 ,CONCAT(round(b.add_num/b.act_num*100,2),'%') as perNew
	 ,b.income
	 ,aa.totalSell 
	 ,round((b.income+aa.totalSell)/b.act_num,3) as dauArpu
	 ,aa.totalNew
	 ,CONCAT(round(aa.totalNew/b.add_num*100,2),'%') as newSellPer
	 ,CONCAT(round(aa.totalNew/b.act_num*100,2),'%') as dauSellPer
	 FROM
		(
		SELECT
			a.tdate,
			<if test="hide != 1">
			a.channel_name,
			a.game_name,
			a.appid,
			</if>
			sum( channel ) totalNew,
			sum( derived_revenue ) totalSell
		from
	 		app_exchange_volume_sell a group by a.tdate
	 		<if test="hide != 1">
	 		,a.channel_name,a.appid
	 		</if>
	  ) aa,
	 <if test="hide == 1">
	(SELECT tdate,income ,SUM(add_num) add_num,SUM(act_num) act_num FROM app_channel_appid GROUP BY tdate) b
	</if>
	 <if test="hide != 1">
	app_channel_appid b,app_exchange_name_config c
	</if>
	where aa.tdate = b.tdate 
	<if test="hide != 1">
	and aa.channel_name = b.channel and aa.appid = b.appid_key and aa.appid = c.appid
	</if>
	<if test = "startTime != null and endTime != null ">
		and aa.tdate BETWEEN #{startTime} AND #{endTime}
	</if>	
	<if test="channelName != null and channelName != '' and hide != 1 ">
		and aa.channel_name = #{channelName}
	</if>
	<if test="gameName != null and gameName != '' and hide != 1 ">
		and c.appname = #{gameName}
	</if>
	<if test="appid != null and appid != '' and hide != 1 ">
		and aa.appid = #{appid}
	</if>
  </select>
  
  <update id="updateAppChannelAppidInfo" parameterType="com.wbgame.pojo.AppChannelAppidInfo" >
    update app_channel_appid
    set add_num = #{addNum},
      act_num = #{actNum},
      income = #{income}
    where  tdate = #{tdate}
    	and channel = #{channel}
      and appid = #{appid}
  </update>
  
  <select id="selectAppExchangeVolumeTotal"  resultType="com.wbgame.pojo.AppExchangeVolumeTotalVo" >
	  select aa.tdate,aa.channel
	,aa.totalNew
	,(CASE WHEN aa.channel="手Q" THEN aa.totalNew-IFNULL(bb.totalNewBuy,0) ELSE bb.totalNewBuy END) AS totalNewBuy
	,(CASE WHEN aa.channel="手Q" THEN bb.totalNewBuy ELSE IFNULL(dd.sales_volume,0) END) AS salesVolume
	,aa.totalDau
	,CONCAT(round(aa.totalNew/aa.totalDau*100,2),'%') as perNew
	,cc.totalNewSell 
	,CONCAT(round(IFNULL(cc.totalNewSell,0)/aa.totalDau*100,2),'%') as dauSellPer 
	,(CASE WHEN aa.channel="手Q" THEN 0 ELSE bb.totalBuy END) AS totalBuy
	,(CASE WHEN aa.channel="手Q" THEN bb.totalBuy ELSE IFNULL(dd.expenditure,0) END) AS expenditure
	,(IFNULL(bb.totalBuy,0)+IFNULL(dd.expenditure,0)) totalExpenditure
	,(CASE WHEN aa.channel="手Q" THEN ROUND(bb.totalBuy/(IFNULL(cc.totalNewSell,0)+IFNULL(bb.totalNewBuy,0)),2) ELSE
	round((IFNULL(bb.totalBuy,0)+IFNULL(dd.expenditure,0))/(IFNULL(bb.totalNewBuy,0)+IFNULL(dd.sales_volume,0)),2) END) AS unitPrice
	,(CASE WHEN aa.channel="手Q" THEN ROUND(aa.totalIncome/aa.totalNew*(aa.totalNew-bb.totalNewBuy)*0.5,2) ELSE aa.totalIncome END) AS totalIncome
	,cc.totalSell  
	,round((IFNULL(cc.totalSell,0))/aa.totalDau,2) as sellDauArpu 
	,(CASE WHEN aa.channel="手Q" THEN aa.totalIncome ELSE aa.totalIncome+IFNULL(cc.totalSell,0) END) AS allIncome
	,round((aa.totalIncome+IFNULL(cc.totalSell,0))/aa.totalDau,3) as dauArpu  
	,(CASE WHEN aa.channel="手Q" THEN ROUND((aa.totalIncome*0.7/aa.totalNew-IFNULL(dd.expenditure,0)),2) 
	ELSE (IFNULL(cc.totalSell,0)-IFNULL(bb.totalBuy,0)) END) AS  diffIncome 
	,(aa.totalIncome+IFNULL(cc.totalSell,0)-IFNULL(bb.totalBuy,0)-IFNULL(dd.expenditure,0)) profit 
	from 
	 (select a.tdate,a.channel,appid_key
	 ,sum(a.add_num) as totalNew
	 ,sum(a.act_num) as totalDau
	 ,sum(a.income) as totalIncome 
	from app_channel_appid a group by a.tdate,a.channel) aa LEFT JOIN app_volume_total dd on  aa.tdate = dd.tdate and aa.channel = dd.channel
	LEFT JOIN 
	(select b.tdate,b.channel_name
	,sum(b.channel) as totalNewBuy
	,sum(b.derived_revenue) as totalBuy
	from app_exchange_volume_buy b 
	where b.pricing_type != '我司互导' group by b.tdate,b.channel_name) bb on aa.tdate = bb.tdate and aa.channel = bb.channel_name
		LEFT JOIN 
	(select c.tdate,c.channel_name
	,IFNULL(sum(c.channel),0) as totalNewSell 
	,(CASE WHEN c.channel="手Q" THEN 0 ELSE sum(c.derived_revenue)END) AS totalSell
	 from app_exchange_volume_sell c where c.pricing_type != '我司互导' group by c.tdate,c.channel_name) cc  
	 on aa.tdate = cc.tdate and aa.channel = cc.channel_name
	 where 1=1
	<if test ="startTime != null and endTime != null ">
		and aa.tdate BETWEEN #{startTime} AND #{endTime}
	</if>	
	<if test="channel != null and channel != ''  ">
		and aa.channel = #{channel}
	</if>
  </select>
  
  <insert id="updateAppExchangeVolumeTemInfo" parameterType="com.wbgame.pojo.AppExchangeVolumeTemInfo" >
    INSERT INTO `app_volume_total`(tdate,channel,`sales_volume`, `expenditure`) 
    VALUES (#{tdate}, #{channel},#{salesVolume}, #{expenditure})
    ON DUPLICATE KEY UPDATE
	sales_volume = VALUES(sales_volume),
	expenditure = VALUES(expenditure)
  </insert>
  
  <insert id="editAppExchangeName" parameterType="java.util.Map">
    insert into `app_exchange_name_config`(appid,appname) 
    VALUES 
         <foreach collection="map.entrySet()" open="(" separator="),(" close=")" index="key" item="val">
        #{key}, #{val}
    </foreach>
    ON DUPLICATE KEY UPDATE
	appname = VALUES(appname)
 </insert> 
 
 <update id="updateAppExchangePermissions" parameterType="com.wbgame.pojo.AppExchangePermissions" >
    update app_exchange_permissions
    set org_channel = #{orgChannel,jdbcType=VARCHAR}
    where user_name = #{userName,jdbcType=VARCHAR}
      and page_name = #{pageName,jdbcType=VARCHAR}
  </update>
  
  <delete id="deleteAppExchangePermissions" parameterType="com.wbgame.pojo.AppExchangePermissions" >
    delete from app_exchange_permissions
    where user_name = #{userName,jdbcType=VARCHAR}
      and page_name = #{pageName,jdbcType=VARCHAR}
  </delete>
  
   <insert id="insertAppExchangePermissions" parameterType="com.wbgame.pojo.AppExchangePermissions" >
    insert into app_exchange_permissions (user_name, page_name, org_channel
      )
    values (#{userName,jdbcType=VARCHAR}, #{pageName,jdbcType=VARCHAR}, #{orgChannel,jdbcType=VARCHAR}
      )
  </insert>
  
  <select id="selectAppExchangePermissions"  parameterType="com.wbgame.pojo.AppExchangePermissions" resultType="com.wbgame.pojo.AppExchangePermissions" >
    select 
      user_name AS userName, page_name AS pageName, org_channel AS orgChannel
    from app_exchange_permissions
    where 1=1 
    <if test="userName != null and userName != ''  ">
		and user_name = #{userName,jdbcType=VARCHAR}
	</if>
    <if test="pageName != null and pageName != ''  ">
		and page_name = #{pageName,jdbcType=VARCHAR}
	</if> 
  </select>
</mapper>