<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave.WbSysMapper">

	<select id="selectWbSysUser" parameterType="java.util.Map" resultType="com.wbgame.pojo.CurrUserVo">
		select * from wb_sys_user 
		where login_name = #{login_name} and password = #{password}
	</select>
	<select id="selectWbSysUserInfoList" parameterType="com.wbgame.pojo.CurrUserVo" 
					resultType="com.wbgame.pojo.CurrUserVo">
					
		select * from wb_sys_user where 1=1
		<if test="login_name != null and login_name != ''">
			and login_name = #{login_name}
		</if>
		<if test="nick_name != null and nick_name != ''">
			and nick_name like concat('%',#{nick_name},'%')
		</if>
		<if test="org_name != null and org_name != ''">
			and org_name like concat('%',#{org_name},'%')
		</if>
		<if test="org_id != null and org_id != ''">
			and org_id = #{org_id}
		</if>
		<if test="department != null and department != ''">
			and department = #{department}
		</if>
	</select>
	<select id="selectWbSysRoleInfoList" parameterType="com.wbgame.pojo.CurrUserVo" 
					resultType="com.wbgame.pojo.CurrUserVo">
					
		select * from wb_sys_role where 1=1
		<if test="role_name != null and role_name != ''">
			and role_name like '%${role_name}%'
		</if>
	</select>


	<resultMap id="treeMenu" type="com.wbgame.pojo.WbSysMenuVo">
        <result property="mid" column="mid" />
        <result property="title" column="title" />
        <result property="link" column="link" />
        <result property="parent_id" column="parent_id" />
        <collection property="subMenu" javaType="ArrayList" column="mid" ofType="com.wbgame.pojo.WbSysMenuVo"  select="selectTreeMenu"/>
    </resultMap>
    
	<select id="selectTreeMenu" parameterType="java.lang.String" resultMap="treeMenu">
		select * from wb_sys_menu where parent_id=#{mid,jdbcType=INTEGER}
	</select>
	
	
	<insert id="insertWbSysUser" parameterType="com.wbgame.pojo.CurrUserVo">
		insert into wb_sys_user(
		        <if test="mach_code != null and mach_code != ''">
					mach_code,
				</if>
				login_name,
				password,
				nick_name,
				org_id,
				org_name,
				role_id,
				role_name,
				platform_id,
				company,
				email,
				department 
		) values(
				<if test="mach_code != null and mach_code != ''">
					#{mach_code},
				</if>
				#{login_name},
				#{password},
				#{nick_name},
				#{org_id},
				#{org_name},
				#{role_id},
				#{role_name},
				#{platform_id},
				#{company},
				#{email},
				#{department} 
		)
	</insert>
	<update id="updateWbSysUser" parameterType="com.wbgame.pojo.CurrUserVo">
		update wb_sys_user set 
			<if test="password != null and password != ''">
				password = #{password},
			</if>
			<if test="mach_code != null and mach_code != ''">
				mach_code = #{mach_code},
			</if>
			nick_name = #{nick_name},
			org_id = #{org_id},
			org_name = #{org_name},
			role_id = #{role_id},
			role_name = #{role_name},
			platform_id = #{platform_id},
			company = #{company},
			email = #{email},
			department = #{department} 
		where login_name = #{login_name}
	</update>
	<delete id="deleteWbSysUser" parameterType="com.wbgame.pojo.CurrUserVo">
		delete from wb_sys_user where login_name = #{login_name}
	</delete>
	
	
	<insert id="insertWbSysRole" parameterType="com.wbgame.pojo.CurrUserVo">
		insert into wb_sys_role(
				role_name,
				app_group,
				app_group_b,
				app_group_c
		) values(
				#{role_name},
				#{app_group},
				#{app_group_b},
				#{app_group_c}
		)
	</insert>
	<update id="updateWbSysRole" parameterType="com.wbgame.pojo.CurrUserVo">
		update wb_sys_role set 
			role_name = #{role_name},
			app_group = #{app_group},
			app_group_b = #{app_group_b},
			app_group_c = #{app_group_c}
		where role_id = #{role_id}
	</update>
	<delete id="deleteWbSysRole" parameterType="com.wbgame.pojo.CurrUserVo">
		delete from wb_sys_role where role_id = #{role_id}
	</delete>
	
	<select id="selectWbSysUserloginNameList" resultType="com.wbgame.pojo.CurrUserVo">
					
		SELECT  a.login_name, nick_name FROM `wb_sys_user`a where a.org_name in ("外部渠道","渠道组")
		
	</select>
	<select id="selectAllUserName"  resultType="com.wbgame.pojo.CurrUserVo">
		select  login_name ,nick_name from wb_sys_user 
	</select>

	<select id="selectArtOrgUserInfo" parameterType="com.wbgame.pojo.CurrUserVo"
			resultType="com.wbgame.pojo.CurrUserVo">
        select * from wb_sys_user where 1=1 and org_id in ('art','org_art')
    </select>
</mapper>