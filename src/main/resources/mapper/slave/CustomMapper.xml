<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.slave.CustomMapper">

	<select id="selectCustomStatsLog" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.CustomStatsVo">
		
		select 
			'${start_date}' as tdate,appid,pid,eventid,label,
			COUNT(DISTINCT lsn) as click_user_num,
			SUM(num) as click_num 
		from ${table_name} 
		where 1=1 
			<if test="appid != null and appid != ''">
				and appid = #{appid}
			</if>
			<if test="pid != null and pid != ''">
				and pid = #{pid}
			</if>
			<if test="eventid != null and eventid != ''">
				and eventid = #{eventid}
			</if>
		group by appid,pid,eventid,label

	</select>
	
	<select id="selectCustomStatsInfo" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.CustomStatsVo">
		
		select * from app_custom_info_total
		where 1=1  
			<if test="start_date != null and end_date != null">
				and tdate BETWEEN #{start_date} AND #{end_date} 
			</if>
			<if test="appid != null and appid != ''">
				and appid = #{appid} 
			</if>
			<if test="pid != null and pid != ''">
				and pid = #{pid} 
			</if>
			<if test="eventid != null and eventid != ''">
				and eventid like concat('%',#{eventid},'%') 
			</if>
			<if test="label_name != null and label_name != ''">
				and label_name like concat('%',#{label_name},'%') 
			</if>
			
			<if test="label != null and label != ''">
				and label like concat('%',#{label},'%') 
			</if>
			<if test="ids != null and ids != ''">
				and label in (${ids}) 
			</if>
	</select>
	
	<insert id="insertCustomStatsInfo" parameterType="java.util.List">
		insert into app_custom_info_total (
			tdate,
			appid,
			pid,
			eventid,
			label,
			label_name,
			click_num,
			click_user_num,
			ave_click_num
			
		) values 
		
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.appid},
			#{li.pid},
			#{li.eventid},
			#{li.label},
			#{li.label_name},
			#{li.click_num},
			#{li.click_user_num},
			#{li.ave_click_num})
			
		</foreach>	
	</insert>
	<delete id="deleteCustomStatsInfo" parameterType="java.util.Map">
		delete from app_custom_info_total where tdate = #{start_date}
	</delete>
	
	<select id="selectShowStatsInfo" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.CustomStatsVo">
		
		select tdate,appid,pid,eventid,label,num as show_num 
		from app_show_all
		where 1=1 
			<if test="start_date != null and end_date != null">
				and tdate BETWEEN #{start_date} AND #{end_date} 
			</if>
			<if test="appid != null and appid != ''">
				and appid = #{appid} 
			</if>
			<if test="pid != null and pid != ''">
				and pid = #{pid} 
			</if>
			<if test="eventid != null and eventid != ''">
				and eventid like concat('%',#{eventid},'%') 
			</if>
			<if test="label != null and label != ''">
				and label like concat('%',#{label},'%') 
			</if>
	</select>
	
	<update id="updateCustomStatsInfoForShow" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update app_custom_info_total set show_num = #{li.show_num} 
	    	where tdate = #{li.tdate} and appid = #{li.appid} 
	    		and pid = #{li.pid} and eventid = #{li.eventid} 
	    		and label = #{li.label} and label_name = #{li.label_name}
    	</foreach>
	</update>
	
	
	<insert id="insertKeepUserOne" parameterType="java.util.Map">
    	insert into app_custom_retained_user
    		(tdate,appid,pid,total_num,add_num,keep_num1,keep_num2,keep_num3,keep_num4,keep_num5,keep_num6,keep_num7,keep_num14,keep_num30)
		
		SELECT '${today}' as tdate, appid, pid,
			COUNT(DISTINCT lsn) as total_num, 0,0,0,0,0,0,0,0,0,0
		FROM app_login_${today}
		GROUP BY appid,pid
    	
    	ON DUPLICATE KEY UPDATE
		total_num = VALUES(total_num)
    </insert>
    <insert id="insertKeepUserOneB" parameterType="java.util.Map">
    	insert into app_custom_retained_user_two
    		(tdate,appid,total_num,add_num,keep_num1,keep_num2,keep_num3,keep_num4,keep_num5,keep_num6,keep_num7,keep_num14,keep_num30)
		
		SELECT '${today}' as tdate, appid,
			COUNT(DISTINCT lsn) as total_num, 0,0,0,0,0,0,0,0,0,0
		FROM app_login_two_${today} WHERE eventid = 'start' 
		GROUP BY appid
    	
    	ON DUPLICATE KEY UPDATE
		total_num = VALUES(total_num)
    </insert>
	
	<select id="selectKeepUserTwo" parameterType="java.util.Map" resultType="com.wbgame.pojo.custom.CustomKeepUserVo">
		select 
			'${startday}' as tdate,aa.appid,aa.pid,count(aa.lsn) as keep_num${daynum}
		from app_login_${today} aa
		join (<include refid="foreachUserList"/>) bb
		
		on bb.appid=aa.appid and bb.pid=aa.pid and bb.lsn=aa.lsn
		group by aa.appid,aa.pid
	</select>
	<sql id="foreachUserList"> <!-- 循环获取新增表中appid和pid的新增用户lsn -->
		<foreach collection="array" item="it" separator=" union all ">
			select appid,pid,lsn from app_custom_userlist_hash${it} where create_date = '${startday}'
		</foreach>
	</sql>
	
	
	<select id="selectKeepUserTwoB" parameterType="java.util.Map" resultType="com.wbgame.pojo.custom.CustomKeepUserVo">
		select 
			'${startday}' as tdate,aa.appid,count(aa.lsn) as keep_num${daynum}
		from app_login_two_${today} aa 
		join (<include refid="foreachUserListB"/>) bb
		on bb.appid=aa.appid and bb.lsn=aa.lsn
		
		where aa.eventid = 'start' 
		group by aa.appid
	</select>
	
	<sql id="foreachUserListB">
		<foreach collection="array" item="it" separator=" union all ">
			select appid,lsn from app_custom_userlist_hash_two${it} where create_date = '${startday}'
		</foreach>
	</sql>
	
	<update id="updateKeepUserFour" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update app_custom_retained_user set 
	    		<choose>
	    			<when test="li.keep_num1 != null and li.keep_num1 != ''">
	    				keep_num1 = #{li.keep_num1}
	    			</when>
	    			<when test="li.keep_num2 != null and li.keep_num2 != ''">
	    				keep_num2 = #{li.keep_num2}
	    			</when>
	    			<when test="li.keep_num3 != null and li.keep_num3 != ''">
	    				keep_num3 = #{li.keep_num3}
	    			</when>
	    			<when test="li.keep_num4 != null and li.keep_num4 != ''">
	    				keep_num4 = #{li.keep_num4}
	    			</when>
	    			<when test="li.keep_num5 != null and li.keep_num5 != ''">
	    				keep_num5 = #{li.keep_num5}
	    			</when>
	    			<when test="li.keep_num6 != null and li.keep_num6 != ''">
	    				keep_num6 = #{li.keep_num6}
	    			</when>
	    			<when test="li.keep_num7 != null and li.keep_num7 != ''">
	    				keep_num7 = #{li.keep_num7}
	    			</when>
	    			<when test="li.keep_num14 != null and li.keep_num14 != ''">
	    				keep_num14 = #{li.keep_num14}
	    			</when>
	    			<when test="li.keep_num30 != null and li.keep_num30 != ''">
	    				keep_num30 = #{li.keep_num30}
	    			</when>
	    			<when test="li.add_num != null and li.add_num != ''">
	    				add_num = #{li.add_num}
	    			</when>
	    		</choose>
	    	where tdate = #{li.tdate} and appid = #{li.appid} and pid = #{li.pid}
    	</foreach>
	</update>
	
	<update id="updateKeepUserFourB" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update app_custom_retained_user_two set 
	    		<choose>
	    			<when test="li.keep_num1 != null and li.keep_num1 != ''">
	    				keep_num1 = #{li.keep_num1}
	    			</when>
	    			<when test="li.keep_num2 != null and li.keep_num2 != ''">
	    				keep_num2 = #{li.keep_num2}
	    			</when>
	    			<when test="li.keep_num3 != null and li.keep_num3 != ''">
	    				keep_num3 = #{li.keep_num3}
	    			</when>
	    			<when test="li.keep_num4 != null and li.keep_num4 != ''">
	    				keep_num4 = #{li.keep_num4}
	    			</when>
	    			<when test="li.keep_num5 != null and li.keep_num5 != ''">
	    				keep_num5 = #{li.keep_num5}
	    			</when>
	    			<when test="li.keep_num6 != null and li.keep_num6 != ''">
	    				keep_num6 = #{li.keep_num6}
	    			</when>
	    			<when test="li.keep_num7 != null and li.keep_num7 != ''">
	    				keep_num7 = #{li.keep_num7}
	    			</when>
	    			<when test="li.keep_num14 != null and li.keep_num14 != ''">
	    				keep_num14 = #{li.keep_num14}
	    			</when>
	    			<when test="li.keep_num30 != null and li.keep_num30 != ''">
	    				keep_num30 = #{li.keep_num30}
	    			</when>
	    			<when test="li.add_num != null and li.add_num != ''">
	    				add_num = #{li.add_num}
	    			</when>
	    		</choose>
	    	where tdate = #{li.tdate} and appid = #{li.appid}
    	</foreach>
	</update>
	
	<insert id="insertCustomUserList" parameterType="java.util.Map">
		insert into ${table_name} (
			appid,
			pid,
			lsn,
			create_date
			
		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.appid},
			#{li.pid},
			#{li.lsn},
			#{li.create_date})
			
		</foreach> 
		ON DUPLICATE KEY UPDATE
		pid = VALUES(pid)
	</insert>
	<select id="selectCustomUserList" parameterType="java.util.Map" resultType="com.wbgame.pojo.custom.CustomKeepUserVo">
		select '${today}' as tdate,appid,pid,COUNT(lsn) as add_num 
		from ${table_name} 
		where create_date = #{today} 
		group by appid,pid
	</select>
	
	<insert id="insertCustomUserListB" parameterType="java.util.Map">
		insert into ${table_name} (
			appid,
			lsn,
			create_date,
			last_date
			
		) values 
		<foreach collection="list" item="li" separator=",">
			(#{li.appid},
			#{li.lsn},
			#{li.create_date},
			#{li.last_date})
			
		</foreach> 
		ON DUPLICATE KEY UPDATE
		last_date = VALUES(last_date)
	</insert>
	<select id="selectCustomUserListB" parameterType="java.util.Map" resultType="com.wbgame.pojo.custom.CustomKeepUserVo">
		select '${today}' as tdate,appid,COUNT(lsn) as add_num 
		from ${table_name} 
		where create_date = #{today} 
		group by appid
	</select>
	
	<select id="selectCustomRetained" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.CustomKeepUserVo">
		
		select * from app_custom_retained_user
		where 1=1  
			<if test="start_date != null and end_date != null">
				and tdate BETWEEN #{start_date} AND #{end_date} 
			</if>
			<if test="appid != null and appid != ''">
				and appid = #{appid} 
			</if>
			<if test="pid != null and pid != ''">
				and pid = #{pid} 
			</if>
		order by tdate asc,add_num desc
	</select>
	
	<select id="selectCustomRetainedTwo" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.CustomKeepUserVo">
		
		select 
			tdate,appid,pid,total_num,add_num,
			TRUNCATE(keep_num1*100/add_num,2) as keep_num1,
			TRUNCATE(keep_num2*100/add_num,2) as keep_num2,
			TRUNCATE(keep_num3*100/add_num,2) as keep_num3,
			TRUNCATE(keep_num4*100/add_num,2) as keep_num4,
			TRUNCATE(keep_num5*100/add_num,2) as keep_num5,
			TRUNCATE(keep_num6*100/add_num,2) as keep_num6,
			TRUNCATE(keep_num7*100/add_num,2) as keep_num7,
			TRUNCATE(keep_num14*100/add_num,2) as keep_num14,
			TRUNCATE(keep_num30*100/add_num,2) as keep_num30
 		from app_custom_retained_user
		where 1=1  
			<if test="start_date != null and end_date != null">
				and tdate BETWEEN #{start_date} AND #{end_date} 
			</if>
			<if test="appid != null and appid != ''">
				and appid = #{appid} 
			</if>
			<if test="pid != null and pid != ''">
				and pid = #{pid} 
			</if>
		order by tdate asc,add_num asc
	</select>
	
	<select id="selectAppDurationInfo" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AppDurationVo">
		
		select * from app_duration_all
		where 1=1  
			<if test="start_date != null and end_date != null">
				and tdate BETWEEN #{start_date} AND #{end_date} 
			</if>
			<if test="appid != null and appid != ''">
				and appid = #{appid} 
			</if>
			<if test="pid != null and pid != ''">
				and pid = #{pid} 
			</if>
			<if test="channel != null and channel != ''">
				and channel = #{channel} 
			</if>
		order by tdate asc,duration desc
	</select>
	
	
	<!-- partner数据  -->
	<select id="selectPartnerUserTables" parameterType="java.util.Map"
				resultType="java.lang.String">
		
		SHOW TABLES LIKE '${table_name}'
	</select>
	
	<select id="selectPartnerStatsLog" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.PartnerStatsVo">
		
		<foreach collection="list" item="li" separator=" UNION ">
			select 
				'${start_date}' as tdate,
				'${li}' as appid,
				pid,channel,
				COUNT(lsn) as add_num 
			from app_partner_user_${li}
			where create_date = #{start_date}
			group by pid,channel
		</foreach>

	</select>
	
	<select id="selectPartnerStatsInfo" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.PartnerStatsVo">
		
		select * from ${table_name}
		where 1=1  
			<if test="start_date != null and end_date != null">
				and tdate BETWEEN #{start_date} AND #{end_date} 
			</if>
			<if test="appid != null and appid != ''">
				and appid = #{appid} 
			</if>
			<if test="pid != null and pid != ''">
				and pid = #{pid} 
			</if>
			<if test="allpid != null and allpid != ''">
				and pid in (${allpid}) 
			</if>
			<if test="channel != null and channel != ''">
				and channel like concat('%',#{channel},'%') 
			</if>
	</select>
	<select id="selectPartnerKeepInfo" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.PartnerStatsVo">
		
			select aa.*,bb.add_num 
			from app_partner_info_keep aa
			left join app_partner_info_total bb
			on aa.tdate = bb.tdate and aa.appid = bb.appid and aa.pid = bb.pid and aa.channel = bb.channel
			where 1=1
			<if test="start_date != null and end_date != null">
				and aa.tdate BETWEEN #{start_date} AND #{end_date} 
			</if>
			<if test="appid != null and appid != ''">
				and aa.appid = #{appid} 
			</if>
			<if test="pid != null and pid != ''">
				and aa.pid = #{pid} 
			</if>
			<if test="channel != null and channel != ''">
				and aa.channel like concat('%',#{channel},'%') 
			</if>
	</select>
	
	<insert id="insertPartnerStatsInfo" parameterType="java.util.List">
		insert into app_partner_info_total (
			tdate,
			appid,
			pid,
			channel,
			add_num,
			bus_num
		) values 
		
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.appid},
			#{li.pid},
			#{li.channel},
			#{li.add_num},
			#{li.bus_num})
			
		</foreach>	
	</insert>
	
	<delete id="deletePartnerStatsInfo" parameterType="java.util.Map">
		delete from ${table_name} where tdate = #{start_date}
	</delete>
	
	<select id="selectPartnerKeepLog" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.PartnerStatsVo">
		
		<foreach collection="list" item="li" separator=" UNION ">
			select 
				create_date as tdate,'${li}' as appid,
				pid,channel,COUNT(lsn) as add_num <!-- 临时存放在add_num中 -->
			
			from app_partner_user_${li}
			where create_date in
				(
					DATE_SUB(#{start_date},INTERVAL 6 DAY),
					DATE_SUB(#{start_date},INTERVAL 2 DAY),
					DATE_SUB(#{start_date},INTERVAL 1 DAY)
				)
			and islogin = 1 
			group by create_date,pid,channel
		</foreach>
		
		<!-- <foreach collection="list" item="li" separator=" UNION ">
			select '${start_date}' as tdate,'${li}' as appid,aa.pid,aa.channel,
				bb.keep2_num as keep2_num,
				cc.keep3_num as keep3_num,
				dd.keep7_num as keep7_num
			from 
				(select 
					pid,channel
				from app_partner_user_${li}
				where create_date = #{start_date}
				group by pid,channel) aa
			left join 
				(select 
					pid,channel,COUNT(lsn) as keep2_num 
				from app_partner_user_${li}
				where create_date = DATE_SUB(#{start_date},INTERVAL 1 DAY)
				and islogin = 1
				group by pid,channel) bb
			on aa.pid = bb.pid and aa.channel = bb.channel
			
			left join 
				(select 
					pid,channel,COUNT(lsn) as keep3_num 
				from app_partner_user_${li}
				where create_date = DATE_SUB(#{start_date},INTERVAL 2 DAY)
				and islogin = 1
				group by pid,channel) cc
			on aa.pid = cc.pid and aa.channel = cc.channel
			
			left join 
				(select 
					pid,channel,COUNT(lsn) as keep7_num 
				from app_partner_user_${li}
				where create_date = DATE_SUB(#{start_date},INTERVAL 6 DAY)
				and islogin = 1
				group by pid,channel) dd
			on aa.pid = dd.pid and aa.channel = dd.channel

		</foreach> -->

	</select>
	<insert id="insertPartnerStatsInfoKeep" parameterType="java.util.List">
		insert into app_partner_info_keep (
			tdate,
			appid,
			pid,
			channel,
			keep2_num,
			keep3_num,
			keep7_num
		) values 
		
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.appid},
			#{li.pid},
			#{li.channel},
			#{li.keep2_num},
			#{li.keep3_num},
			#{li.keep7_num})
			
		</foreach>	
	</insert>
	<update id="updatePartnerStatsInfoKeep" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update app_partner_info_keep set 
	    		<choose>
	    			<when test="li.keep2_num != null and li.keep2_num != 0">
	    				keep2_num = #{li.keep2_num}
	    			</when>
	    			<when test="li.keep3_num != null and li.keep3_num != 0">
	    				keep3_num = #{li.keep3_num}
	    			</when>
	    			<when test="li.keep7_num != null and li.keep7_num != 0">
	    				keep7_num = #{li.keep7_num}
	    			</when>
	    			
	    		</choose>
	    	where tdate = #{li.tdate} and appid = #{li.appid} and pid = #{li.pid} and channel = #{li.channel}
    	</foreach>
	</update>
	
	<select id="selectCustomTotalStatsLogTwo" parameterType="java.lang.String"
		resultType="com.wbgame.pojo.custom.CustomTotalStatsVo">
		select '${tdate}' as tdate,xx.*,cc.per_rate,dd.add_num,
				(ee.all_add_num + IFNULL(dd.add_num, 0)) as all_add_num  
		from (
		
			select aa.appid,aa.click_num,aa.click_user_num,bb.open_num,bb.open_user_num from 
			(SELECT appid,SUM(num) as click_num,count(DISTINCT lsn) as click_user_num 
			FROM app_login_two_${tdate}
			WHERE eventid = 'start' GROUP BY appid) aa 
			left join 
			(SELECT appid,SUM(num) as open_num,count(DISTINCT lsn) as open_user_num 
			FROM app_login_two_${tdate}
			WHERE eventid = 'login' GROUP BY appid) bb
			on aa.appid = bb.appid
			
			union
			
			select bb.appid,aa.click_num,aa.click_user_num,bb.open_num,bb.open_user_num from 
			(SELECT appid,SUM(num) as click_num,count(DISTINCT lsn) as click_user_num 
			FROM app_login_two_${tdate}
			WHERE eventid = 'start' GROUP BY appid) aa 
			right join 
			(SELECT appid,SUM(num) as open_num,count(DISTINCT lsn) as open_user_num 
			FROM app_login_two_${tdate}
			WHERE eventid = 'login' GROUP BY appid) bb
			on aa.appid = bb.appid
		) xx
		
		left join 
		(SELECT appid,truncate(SUM(duration)/SUM(number),2) as per_rate FROM app_duration_all
		where tdate = #{tdate}
		group by appid) cc
		on xx.appid = cc.appid
		
		left join 
		(SELECT appid,SUM(add_num) as add_num FROM app_custom_retained_user_two
		where tdate = #{tdate} 
		group by appid) dd
		on xx.appid = dd.appid
		
		left join 
		(SELECT appid,all_add_num FROM app_custom_stats_total_two
		where tdate = DATE_SUB(#{tdate},INTERVAL 1 DAY)
		group by appid) ee
		on xx.appid = ee.appid
	</select>
	<insert id="insertCustomTotalStatsListTwo" parameterType="java.util.List">
		insert into app_custom_stats_total_two (
			tdate,
			appid,
			appname,
			click_num,
			click_user_num,
			open_num,
			open_user_num,
			per_rate,
			add_num,
			all_add_num

		) values 
		
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.appid},
			#{li.appname},
			#{li.click_num},
			#{li.click_user_num},
			#{li.open_num},
			#{li.open_user_num},
			#{li.per_rate},
			#{li.add_num},
			#{li.all_add_num})
			
		</foreach>
		ON DUPLICATE KEY UPDATE
		click_num = VALUES(click_num),
		click_user_num = VALUES(click_user_num),
		open_num = VALUES(open_num),
		open_user_num = VALUES(open_user_num),
		per_rate = VALUES(per_rate),
		add_num = VALUES(add_num),
		all_add_num = VALUES(all_add_num)
	</insert>
	
	<select id="selectCustomTotalStatsLog" parameterType="java.lang.String"
		resultType="com.wbgame.pojo.custom.CustomTotalStatsVo">
		select '${tdate}' as tdate,xx.*,cc.per_rate,dd.add_num,ee.all_add_num 
		from (
		
			select aa.appid,aa.pid,aa.click_num,aa.click_user_num,bb.open_num,bb.open_user_num from 
			(SELECT appid,pid,SUM(click_num) as click_num,SUM(click_user_num) as click_user_num 
			FROM app_custom_info_total
			where tdate = #{tdate} and eventid = 'start'
			group by appid,pid) aa 
			left join 
			(SELECT appid,pid,SUM(click_num) as open_num,SUM(click_user_num) as open_user_num 
			FROM app_custom_info_total
			where tdate = #{tdate} and eventid = 'login'
			group by appid,pid) bb
			on aa.appid = bb.appid and aa.pid = bb.pid
			
			union
			
			select bb.appid,bb.pid,aa.click_num,aa.click_user_num,bb.open_num,bb.open_user_num from 
			(SELECT appid,pid,SUM(click_num) as click_num,SUM(click_user_num) as click_user_num 
			FROM app_custom_info_total
			where tdate = #{tdate} and eventid = 'start'
			group by appid,pid) aa 
			right join 
			(SELECT appid,pid,SUM(click_num) as open_num,SUM(click_user_num) as open_user_num 
			FROM app_custom_info_total
			where tdate = #{tdate} and eventid = 'login'
			group by appid,pid) bb
			on aa.appid = bb.appid and aa.pid = bb.pid
		) xx
		
		left join 
		(SELECT appid,pid,truncate(SUM(duration)/SUM(number),2) as per_rate FROM app_duration_all
		where tdate = #{tdate}
		group by appid,pid) cc
		on xx.appid = cc.appid and xx.pid = cc.pid
		
		left join 
		(SELECT appid,pid,SUM(add_num) as add_num FROM app_custom_retained_user
		where tdate = #{tdate} 
		group by appid,pid) dd
		on xx.appid = dd.appid and xx.pid = dd.pid
		
		left join 
		(SELECT appid,pid,SUM(add_num) as all_add_num FROM app_custom_retained_user
		group by appid,pid) ee
		on xx.appid = ee.appid and xx.pid = ee.pid
	</select>
	<insert id="insertCustomTotalStatsList" parameterType="java.util.List">
		insert into app_custom_stats_total (
			tdate,
			appid,
			pid,
			appname,
			click_num,
			click_user_num,
			open_num,
			open_user_num,
			per_rate,
			add_num,
			all_add_num

		) values 
		
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.appid},
			#{li.pid},
			#{li.appname},
			#{li.click_num},
			#{li.click_user_num},
			#{li.open_num},
			#{li.open_user_num},
			#{li.per_rate},
			#{li.add_num},
			#{li.all_add_num})
			
		</foreach>
		ON DUPLICATE KEY UPDATE
		click_num = VALUES(click_num),
		click_user_num = VALUES(click_user_num),
		open_num = VALUES(open_num),
		open_user_num = VALUES(open_user_num),
		per_rate = VALUES(per_rate),
		add_num = VALUES(add_num),
		all_add_num = VALUES(all_add_num)
	</insert>
	
	<select id="selectCustomStatsTotalInfo" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.CustomTotalStatsVo">
		select *
		from app_custom_stats_total
		where 1=1 
			<if test="tdate != null">
				and tdate in ('${tdate}')
			</if>
			<if test="appid != null and appid != ''">
				and appid = #{appid} 
			</if>
			<if test="pid != null and pid != ''">
				and pid = #{pid} 
			</if>
		order by tdate asc,appid asc,add_num desc
	</select>
	<select id="selectCustomStatsTotalInfoTwo" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.CustomTotalStatsVo">
		
		select tdate,appid,appname, 
			SUM(click_num) as click_num,
			SUM(click_user_num) as click_user_num,
			SUM(open_num) as open_num,
			SUM(open_user_num) as open_user_num,
			per_rate,
			SUM(add_num) as add_num,
			SUM(all_add_num) as all_add_num
		from app_custom_stats_total 
		where 1=1 
			<if test="start_date != null and end_date != null">
				and tdate BETWEEN #{start_date} AND #{end_date} 
			</if>
			<if test="appid != null and appid != ''">
				and appid in (${appid})
			</if>
		group by tdate,appid
		<if test="appid == null or appid == ''">
			order by add_num desc
		</if>
	</select>
	<select id="selectCustomStatsTotalInfoThree" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.CustomTotalStatsVo">
		
		select tdate, 
			SUM(click_num) as click_num,
			SUM(click_user_num) as click_user_num,
			SUM(open_num) as open_num,
			SUM(open_user_num) as open_user_num,
			SUM(add_num) as add_num,
			SUM(all_add_num) as all_add_num
		from app_custom_stats_total 
		where tdate BETWEEN #{start_date} AND #{end_date}
			<if test="appid != null and appid != ''">
				and appid in (${appid})
			</if>
		group by tdate
		order by tdate asc
	</select>
	
	<select id="selectCustomStatsKeepInfo" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.CustomKeepUserVo">
		select tdate,appid,
			SUM(total_num) as total_num,
			SUM(add_num) as add_num,
			TRUNCATE(SUM(keep_num1)*100/SUM(add_num),2) as keep_num1,
			TRUNCATE(SUM(keep_num2)*100/SUM(add_num),2) as keep_num2,
			TRUNCATE(SUM(keep_num3)*100/SUM(add_num),2) as keep_num3,
			TRUNCATE(SUM(keep_num4)*100/SUM(add_num),2) as keep_num4,
			TRUNCATE(SUM(keep_num5)*100/SUM(add_num),2) as keep_num5,
			TRUNCATE(SUM(keep_num6)*100/SUM(add_num),2) as keep_num6,
			TRUNCATE(SUM(keep_num7)*100/SUM(add_num),2) as keep_num7,
			TRUNCATE(SUM(keep_num14)*100/SUM(add_num),2) as keep_num14,
			TRUNCATE(SUM(keep_num30)*100/SUM(add_num),2) as keep_num30
		FROM app_custom_retained_user
		where 1=1 
			<if test="start_date != null and end_date != null">
				and tdate BETWEEN #{start_date} AND #{end_date} 
			</if>
			<if test="appid != null and appid != ''">
				and appid = #{appid} 
			</if>
		group by tdate,appid
	</select>
	
	
	<select id="selectAppDurationErrorLog" parameterType="java.util.Map"
		resultType="com.wbgame.pojo.custom.AppDurationErrorVo">
		select * from app_duration_error_log
		where 1=1 
			<if test="tdate != null and tdate != null">
				and tdate = #{tdate} 
			</if>
			<if test="appid != null and appid != ''">
				and appid = #{appid} 
			</if>
			<if test="lsn != null and lsn != ''">
				and lsn = #{lsn} 
			</if>
		order by duration desc
	</select>
	
	
	<update id="updateTest" parameterType="java.util.List">
		<foreach collection="list" item="li" separator=";">
			update app_custom_stats_total_two 
				set all_add_num = #{li.all_add_num} 
			where tdate = #{li.tdate} and appid = #{li.appid}
    	</foreach>
	</update>
	
	<insert id="insertAppTotalHour" parameterType="java.util.List">
		insert into app_total_hour (
			tdate,
			adtype,
			appid,
			provider,
			${hournum}
		) values 
		
		<foreach collection="list" item="li" separator=",">
			(#{li.tdate},
			#{li.param1},
			#{li.param2},
			#{li.param3},
			#{li.param4})
			
		</foreach>
		ON DUPLICATE KEY UPDATE
		${hournum} = VALUES(${hournum})
	</insert>
	
	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>	
		${sql3}
	</insert>
	
	
</mapper>