server.port=${port:6115}
server.tomcat.uri-encoding=UTF-8
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
#server.tomcat.max-connections=15000
#logging.level.com.wbgame.mapper=debug
#logging.level.*=INFO
#logging.level.org.apache.http=WARN

cityUtil.locations=/cfg/17monipdb.datx
tempPath=/home/<USER>/src
savePath=/home/<USER>
commonPath=/home/<USER>

album_id=12000010319

#是否开启定时任务?true:false
schedule.enabled=false

#禁止生产环境打开swagger接口
knife4j.production=true

# dubbo配置nacos注册服务
#dubbo.application.name=dubbo-provider-wbparent
#dubbo.registry.address=nacos://mse-ae111306-nacos-ans.mse.aliyuncs.com:8848
#
#dubbo.protocol.name=dubbo
#dubbo.protocol.port=-1
#dubbo.provider.threads=300
#dubbo.consumer.timeout=20000

S3.region=us-west-1
S3.accessKeyId=test
S3.accessKeySecret=test
S3.bucketName=tik-game-com-web


#ADB云原生数据库
spring.datasource.adb.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.adb.url=******************************************************************************************************************************************************************************
spring.datasource.adb.username=pork
spring.datasource.adb.password=open_123!@#


#ADB 用户画像bi库
spring.datasource.viuadb.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.viuadb.url=******************************************************************************************************************************************************************************************
spring.datasource.viuadb.username=db_viu
spring.datasource.viuadb.password=Dnwx@user!@#123

#红包独立数据库
spring.datasource.redpack.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.redpack.url=*******************************************************************************************************************************************************************************
spring.datasource.redpack.username=red_pack
spring.datasource.redpack.password=red_pack@0302

#变现系统数据源
spring.datasource.adv2.driver-class-name=com.mysql.jdbc.Driver
#外网地址
#spring.datasource.adv2.url=*******************************************************************************************************************************************************************************
spring.datasource.adv2.url=*****************************************************************************************************************************************************************************
spring.datasource.adv2.username=oppoh5
spring.datasource.adv2.password=Dnwx@1602


#清理王数据库
spring.datasource.clean.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.clean.url=*******************************************************************************************************************************************************************************
#spring.datasource.clean.url=*****************************************************************************************************************************************************************************
spring.datasource.clean.username=adv_data
spring.datasource.clean.password=adv_902902

#yyhz_0308数据库
spring.datasource.master.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.master.url=**********************************************************************************************************************************************************************************
spring.datasource.master.username=dnwx_main
spring.datasource.master.password=dnwx@0607


#xyxtj数据库
spring.datasource.slave.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.slave.url=******************************************************************************************************************************************************************************
spring.datasource.slave.username=dnwx_main
spring.datasource.slave.password=dnwx@0607

#admsg数据库
spring.datasource.slave2.driver-class-name=com.mysql.jdbc.Driver
# 外网
spring.datasource.slave2.url=******************************************************************************************************************************************************************************
#spring.datasource.slave2.url=****************************************************************************************************************************************************************************
spring.datasource.slave2.username=adv_data
spring.datasource.slave2.password=adv_902902


#nnjy海外数据库
spring.datasource.haiwai2.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.haiwai2.url=*******************************************************************************************************************************************************************************
spring.datasource.haiwai2.username=nnjy_dba
spring.datasource.haiwai2.password=Dnwx1602

#nnjy国内数据库
spring.datasource.nnjy.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.nnjy.url=******************************************************************************************************************************************************************************
spring.datasource.nnjy.username=nn_oa
spring.datasource.nnjy.password=nn_oa0301

#投放系统数据库
spring.datasource.tfxt.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.tfxt.url=*****************************************************************************************************************************************************************************************
spring.datasource.tfxt.username=dnwx
spring.datasource.tfxt.password=4C3Z238cbF3QR627F8

#用户画像数据库
spring.datasource.usertag.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.usertag.url=************************************************************************************************************************************************************************************
spring.datasource.usertag.username=db_viu
spring.datasource.usertag.password=Dnwx@user!@#123

# 游戏城
spring.datasource.game.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.game.url=***********************************************************************************************************************************
spring.datasource.game.username=red_pack
spring.datasource.game.password=red_pack@0302

# 中度游戏数据库
spring.datasource.zdgame.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.zdgame.url=***************************************************************************************************************************************
spring.datasource.zdgame.username=hhjj
spring.datasource.zdgame.password=hhjj@0307

# 游戏game库
spring.datasource.gameother.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.gameother.url=**********************************************************************************************************************************************************************
spring.datasource.gameother.username=mmxw_dba
spring.datasource.gameother.password=dnwx@1227

#redis
spring.datasource.initialSize=20
spring.datasource.minIdle=20
spring.datasource.maxActive=50
spring.datasource.maxWait=60000
spring.datasource.validationQuery=SELECT 1 FROM DUAL
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=true
spring.datasource.testOnReturn=false
spring.datasource.timeBetweenEvictionRunsMillis=60000
spring.datasource.minEvictableIdleTimeMillis=300000
spring.datasource.removeAbandoned=true
spring.datasource.removeAbandonedTimeout=1800
spring.datasource.poolPreparedStatements=false
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20

#pagehelper 使用beanconfig的配置
#pagehelper.helperDialect=mysql
#pagehelper.reasonable=true
#pagehelper.supportMethodsArguments=true
#pagehelper.params=count=countSql

#redis
spring.redis.pool.maxTotal=300
spring.redis.pool.maxIdle=50
spring.redis.pool.minIdle=30
spring.redis.pool.maxWaitMillis=0
spring.redis.timeout=5000
spring.redis.host=r-j6cb6s5834vpbzptyl.redis.rds.aliyuncs.com
spring.redis.port=6379
spring.redis.password=r-j6cb6s5834vpbzptyl:Dnwx@1602

# 检查无效连接
spring.redis.pool.testOnCreate=false
spring.redis.pool.testOnReturn=false
spring.redis.pool.testOnBorrow=true
spring.redis.pool.testWhileIdle=true
spring.redis.pool.minEvictableIdleTimeMills=60000
spring.redis.pool.timeBetweenEvictionRunsMillis=30000


jetcache.local.default.type=linkedhashmap
jetcache.local.default.keyConvertor=fastjson
#jetcache.local.default.expireAfterWriteInMillis=6000000

# 最大支持文件大小
spring.http.multipart.max-file-size=300Mb
# 最大支持请求大小
spring.http.multipart.max-request-size=400Mb
# 指定自定义命名的配置文件
#logging.config= classpath:logback-custom.xml

