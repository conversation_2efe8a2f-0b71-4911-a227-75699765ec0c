server.port=${port:6106}
server.tomcat.uri-encoding=UTF-8
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
#server.tomcat.max-connections=15000
logging.level.com.wbgame.mapper=debug
logging.level.com.wbgame.*=debug

logging.level.com.wbgame.service.impl.UmengServiceImpl=debug

#logging.level.org.apache.http=WARN

#是否开启定时任务?true:false
schedule.enabled=false

cityUtil.locations=/cfg/17monipdb.datx
tempPath=/home/<USER>/src
savePath=/home/<USER>
#cityUtil.locations=D:\\17monipdb.datx
#tempPath=D:\\

album_id=12000010319
#缓存刷新域名
cash.refresh.track=http://*************:6510

# actuator使用所需配置
#management.security.enabled=false
#management.port=6106

#指定前端映射目录
htmlPath=/home/<USER>/v3/


S3.region=us-west-1
S3.accessKeyId=test
S3.accessKeySecret=test
S3.bucketName=tik-game-com-web


#ADB云原生数据库
spring.datasource.adb.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.adb.url=******************************************************************************************************************************************************************************
spring.datasource.adb.username=caow
spring.datasource.adb.password=caow@1227

#ADB-mysql账号和密码
#spring.datasource.adb.username=root
#spring.datasource.adb.password=Dnwx1602

#ADB-mysql账号和密码2
#spring.datasource.adb.username=dnwx_bi
#spring.datasource.adb.password=dnwx_bi1602


#ADB 用户画像bi库
spring.datasource.viuadb.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.viuadb.url=******************************************************************************************************************************************************************************************
spring.datasource.viuadb.username=caow
spring.datasource.viuadb.password=caow@1227


#yyhz_0308数据库
spring.datasource.master.driver-class-name=com.mysql.jdbc.Driver
# 外网
spring.datasource.master.url=**********************************************************************************************************************************************************************************
#spring.datasource.master.url=*************************************************************************************************************************************************************************************
spring.datasource.master.username=dnwx_pork
spring.datasource.master.password=dnwx@pork#1602

#spring.datasource.master.url=*************************************************************************************************************************************
#spring.datasource.master.username=root
#spring.datasource.master.password=123456


#xyxtj数据库
spring.datasource.slave.driver-class-name=com.mysql.jdbc.Driver
# 外网
spring.datasource.slave.url=******************************************************************************************************************************************************************************
#spring.datasource.slave.url=*********************************************************************************************************************************************************************************
spring.datasource.slave.username=dnwx_pork
spring.datasource.slave.password=dnwx@pork#1602




#nnjy海外数据库
spring.datasource.haiwai2.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.haiwai2.url=*******************************************************************************************************************************************************************************
spring.datasource.haiwai2.username=nnjy_dba
spring.datasource.haiwai2.password=Dnwx1602


#投放系统数据库
spring.datasource.tfxt.driver-class-name=com.mysql.jdbc.Driver
#spring.datasource.tfxt.url=*****************************************************************************************************************************************************************************************
#spring.datasource.tfxt.username=dnwx
#spring.datasource.tfxt.password=4C3Z238cbF3QR627F8
spring.datasource.tfxt.url=********************************************************************************************************************************************************************
spring.datasource.tfxt.username=dnwx_pork
spring.datasource.tfxt.password=dnwx@pork#1602


#清理王数据库
spring.datasource.clean.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.clean.url=*******************************************************************************************************************************************************************************
#spring.datasource.clean.url=*****************************************************************************************************************************************************************************
spring.datasource.clean.username=adv_data
spring.datasource.clean.password=adv_902902


#admsg数据库
spring.datasource.slave2.driver-class-name=com.mysql.jdbc.Driver
# 外网
spring.datasource.slave2.url=******************************************************************************************************************************************************************************
#spring.datasource.slave2.url=****************************************************************************************************************************************************************************
spring.datasource.slave2.username=adv_data
spring.datasource.slave2.password=adv_902902


#变现系统数据源
spring.datasource.adv2.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.adv2.url=*********************************************************************************************************************************************************************************
#spring.datasource.adv2.url=************************************************************************************************************************************************************************************
spring.datasource.adv2.username=dnwx_main
spring.datasource.adv2.password=dnwx@0607

#用户画像数据库
spring.datasource.usertag.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.usertag.url=************************************************************************************************************************************************************************************
spring.datasource.usertag.username=red_pack
spring.datasource.usertag.password=redpack@0607

#红包独立数据库
spring.datasource.redpack.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.redpack.url=*********************************************************************************************************************************************************************************
spring.datasource.redpack.username=dnwx_redpack
spring.datasource.redpack.password=dnwx@redpack#1602

# 中度游戏数据库
spring.datasource.zdgame.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.zdgame.url=***************************************************************************************************************************************
spring.datasource.zdgame.username=hhjj
spring.datasource.zdgame.password=hhjj@0307

# 游戏城 GameDataSourceConfig
spring.datasource.game.driver-class-name=com.mysql.jdbc.Driver
# 外网
spring.datasource.game.url=***********************************************************************************************************************************
spring.datasource.game.username=dnwx_redpack
spring.datasource.game.password=dnwx@redpack#1602

#游戏game库
spring.datasource.gameother.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.gameother.url=jdbc:mysql://************:3306/yyhz_0308?useUnicode=true&characterEncoding=utf8&rewriteBatchedStatements=true&allowMultiQueries=true&useSSL=false
spring.datasource.gameother.username=db_fin
spring.datasource.gameother.password=Dnwx@user!@#1021


# 海外广告配置
spring.datasource.haiwaiad.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.haiwaiad.url=*********************************************************************************************************************************************************************************
spring.datasource.haiwaiad.username=cfg_adb
spring.datasource.haiwaiad.password=cfg_adb@0411

# 海外adb库
spring.datasource.haiwai2adb.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.haiwai2adb.url=*************************************************************************************************************************************************************************************************************************************
spring.datasource.haiwai2adb.username=chensq
spring.datasource.haiwai2adb.password=Dnwx@1602

# 乐影数据库
spring.datasource.leying.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.leying.url=********************************************************************************************************************************************************************************************************************
spring.datasource.leying.username=ly_dba
spring.datasource.leying.password=Dnwx@1602

spring.datasource.track.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.track.url=****************************************************************************************************************************************************************************************************************
spring.datasource.track.username=dnwx_bi_en_adb
spring.datasource.track.password=bi_en_adb@0526


#连接池的配置信息
spring.datasource.initialSize=20
spring.datasource.minIdle=20
spring.datasource.maxActive=50
spring.datasource.maxWait=60000
spring.datasource.validationQuery=SELECT 1 FROM DUAL
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=true
spring.datasource.testOnReturn=false
spring.datasource.timeBetweenEvictionRunsMillis=60000
spring.datasource.minEvictableIdleTimeMillis=300000
spring.datasource.removeAbandoned=true
spring.datasource.removeAbandonedTimeout=1800
spring.datasource.poolPreparedStatements=false
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20

#pagehelper 使用beanconfig的配置
#pagehelper.helperDialect=mysql
#pagehelper.reasonable=true
#pagehelper.supportMethodsArguments=true
#pagehelper.params=count=countSql

#redis
spring.redis.pool.maxIdle=30
spring.redis.pool.minIdle=10
spring.redis.pool.maxTotal=200
spring.redis.pool.maxWaitMillis=3000
spring.redis.timeout=5000

## 运营系统
spring.redis.host=************
spring.redis.port=6379
spring.redis.password=dnwx.123
# 红包redis
spring.redis.redPack.host=************
spring.redis.redPack.port=6379
spring.redis.redPack.password=dnwx.123


#spring.redis.host=r-wz97nfbxwnytg37c5f.redis.rds.aliyuncs.com
#spring.redis.port=6379
#spring.redis.password=Dnwx@0209
### 红包redis
#spring.redis.redPack.host=r-wz9u5it5rjhsgmbm80pd.redis.rds.aliyuncs.com
#spring.redis.redPack.port=6379
#spring.redis.redPack.password=redPack0109



# 检查无效连接
spring.redis.pool.testOnCreate=false
spring.redis.pool.testOnReturn=false
spring.redis.pool.testOnBorrow=true
spring.redis.pool.testWhileIdle=true
spring.redis.pool.minEvictableIdleTimeMills=60000
spring.redis.pool.timeBetweenEvictionRunsMillis=30000


jetcache.local.default.type=linkedhashmap
jetcache.local.default.keyConvertor=fastjson
#jetcache.local.default.expireAfterWriteInMillis=6000000

 # 最大支持文件大小
spring.http.multipart.max-file-size=200Mb
 # 最大支持请求大小
spring.http.multipart.max-request-size=200Mb
