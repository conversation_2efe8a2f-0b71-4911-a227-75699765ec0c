server.port=${port:6115}
server.tomcat.uri-encoding=UTF-8
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
server.max-http-header-size=20971520
#server.tomcat.max-connections=15000
#logging.level.com.wbgame.mapper=debug
#logging.level.*=INFO
#logging.level.org.apache.http=WARN

cityUtil.locations=/cfg/17monipdb.datx
tempPath=/home/<USER>/src
savePath=/home/<USER>
commonPath=/home/<USER>

album_id=12000010319
cash.refresh.track=https://track.vzhifu.net
#\u662F\u5426\u5F00\u542F\u5B9A\u65F6\u4EFB\u52A1?true:false
schedule.enabled=false

#\u7981\u6B62\u751F\u4EA7\u73AF\u5883\u6253\u5F00swagger\u63A5\u53E3
knife4j.production=true

S3.region=us-west-1
S3.accessKeyId=test
S3.accessKeySecret=test
S3.bucketName=tik-game-com-web


#ADB\u4E91\u539F\u751F\u6570\u636E\u5E93
spring.datasource.adb.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.adb.url=*****************************************************************************************************************************************************************************
spring.datasource.adb.username=dnwx_pork
spring.datasource.adb.password=dnwx@pork#1602



#ADB \u7528\u6237\u753B\u50CFbi\u5E93
spring.datasource.viuadb.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.viuadb.url=******************************************************************************************************************************************************************************************
spring.datasource.viuadb.username=dnwx_pork
spring.datasource.viuadb.password=dnwx@pork#1602


#\u53D8\u73B0\u7CFB\u7EDF\u6570\u636E\u6E90
spring.datasource.adv2.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.adv2.url=************************************************************************************************************************************************************************************
spring.datasource.adv2.username=dnwx_pork
spring.datasource.adv2.password=dnwx@pork#1602


#yyhz_0308\u6570\u636E\u5E93
spring.datasource.master.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.master.url=*************************************************************************************************************************************************************************************
spring.datasource.master.username=dnwx_pork
spring.datasource.master.password=dnwx@pork#1602

#xyxtj\u6570\u636E\u5E93
spring.datasource.slave.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.slave.url=*********************************************************************************************************************************************************************************
spring.datasource.slave.username=dnwx_pork
spring.datasource.slave.password=dnwx@pork#1602


#\u6E05\u7406\u738B\u6570\u636E\u5E93
spring.datasource.clean.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.clean.url=*******************************************************************************************************************************************************************************
#spring.datasource.clean.url=*****************************************************************************************************************************************************************************
spring.datasource.clean.username=adv_data
spring.datasource.clean.password=adv_902902

#admsg\u6570\u636E\u5E93
spring.datasource.slave2.driver-class-name=com.mysql.jdbc.Driver
# \u5916\u7F51
spring.datasource.slave2.url=******************************************************************************************************************************************************************************
#spring.datasource.slave2.url=****************************************************************************************************************************************************************************
spring.datasource.slave2.username=adv_data
spring.datasource.slave2.password=adv_902902


#nnjy\u6D77\u5916\u6570\u636E\u5E93
spring.datasource.haiwai2.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.haiwai2.url=*******************************************************************************************************************************************************************************
spring.datasource.haiwai2.username=nnjy_dba
spring.datasource.haiwai2.password=Dnwx1602


#\u6295\u653E\u7CFB\u7EDF\u6570\u636E\u5E93
spring.datasource.tfxt.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.tfxt.url=************************************************************************************************************************************************************************************
spring.datasource.tfxt.username=dnwx_pork
spring.datasource.tfxt.password=dnwx@pork#1602

#\u7528\u6237\u753B\u50CF\u6570\u636E\u5E93
spring.datasource.usertag.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.usertag.url=********************************************************************************************************************************************************************
spring.datasource.usertag.username=dnwx_pork
spring.datasource.usertag.password=dnwx@pork#1602

# \u6E38\u620F\u57CE
spring.datasource.game.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.game.url=***********************************************************************************************************************************
spring.datasource.game.username=dnwx_redpack
spring.datasource.game.password=dnwx@redpack#1602

#\u7EA2\u5305\u72EC\u7ACB\u6570\u636E\u5E93
spring.datasource.redpack.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.redpack.url=*******************************************************************************************************************************************************************************
spring.datasource.redpack.username=dnwx_redpack
spring.datasource.redpack.password=dnwx@redpack#1602


# \u4E2D\u5EA6\u6E38\u620F\u6570\u636E\u5E93
spring.datasource.zdgame.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.zdgame.url=***************************************************************************************************************************************
spring.datasource.zdgame.username=hhjj
spring.datasource.zdgame.password=hhjj@0307

# \u6E38\u620Fgame\u5E93
spring.datasource.gameother.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.gameother.url=**********************************************************************************************************************************************************************
spring.datasource.gameother.username=mmxw_dba
spring.datasource.gameother.password=dnwx@1227

# \u6D77\u5916\u5E7F\u544A\u914D\u7F6E
spring.datasource.haiwaiad.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.haiwaiad.url=*********************************************************************************************************************************************************************************
spring.datasource.haiwaiad.username=cfg_adb
spring.datasource.haiwaiad.password=cfg_adb@0411

spring.datasource.haiwai2adb.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.haiwai2adb.url=**********************************************************************************************************************************************************************************
spring.datasource.haiwai2adb.username=chensq
spring.datasource.haiwai2adb.password=Dnwx@1602

# \u4E50\u5F71\u6570\u636E\u5E93
spring.datasource.leying.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.leying.url=********************************************************************************************************************************************************************************************************************
spring.datasource.leying.username=ly_dba
spring.datasource.leying.password=Dnwx@1602


# \u5B9A\u65F6\u4EFB\u52A1\u6295\u653E\u5E93
#redis
spring.datasource.initialSize=20
spring.datasource.minIdle=20
spring.datasource.maxActive=50
spring.datasource.maxWait=60000
spring.datasource.validationQuery=SELECT 1 FROM DUAL
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=true
spring.datasource.testOnReturn=false
spring.datasource.timeBetweenEvictionRunsMillis=60000
spring.datasource.minEvictableIdleTimeMillis=300000
spring.datasource.removeAbandoned=true
spring.datasource.removeAbandonedTimeout=1800
spring.datasource.poolPreparedStatements=false
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20

#pagehelper \u4F7F\u7528beanconfig\u7684\u914D\u7F6E
#pagehelper.helperDialect=mysql
#pagehelper.reasonable=true
#pagehelper.supportMethodsArguments=true
#pagehelper.params=count=countSql

#redis
spring.redis.pool.maxTotal=300
spring.redis.pool.maxIdle=50
spring.redis.pool.minIdle=30
spring.redis.pool.maxWaitMillis=0
spring.redis.timeout=5000
spring.redis.host=r-wz97nfbxwnytg37c5f.redis.rds.aliyuncs.com
spring.redis.port=6379
spring.redis.password=Dnwx@0209

# \u68C0\u67E5\u65E0\u6548\u8FDE\u63A5
spring.redis.pool.testOnCreate=false
spring.redis.pool.testOnReturn=false
spring.redis.pool.testOnBorrow=true
spring.redis.pool.testWhileIdle=true
spring.redis.pool.minEvictableIdleTimeMills=60000
spring.redis.pool.timeBetweenEvictionRunsMillis=30000

# \u7EA2\u5305redis
spring.redis.redPack.host=r-wz9u5it5rjhsgmbm80.redis.rds.aliyuncs.com
spring.redis.redPack.port=6379
spring.redis.redPack.password=redPack0109


jetcache.local.default.type=linkedhashmap
jetcache.local.default.keyConvertor=fastjson
#jetcache.local.default.expireAfterWriteInMillis=6000000

# \u6700\u5927\u652F\u6301\u6587\u4EF6\u5927\u5C0F
spring.http.multipart.max-file-size=300Mb
# \u6700\u5927\u652F\u6301\u8BF7\u6C42\u5927\u5C0F
spring.http.multipart.max-request-size=400Mb
## \u6307\u5B9A\u81EA\u5B9A\u4E49\u547D\u540D\u7684\u914D\u7F6E\u6587\u4EF6
#logging.config= classpath:logback-custom.xml

