39657,
38718,
39320,
40451,
39833,
40524,
40512,
40195,
38320,
39649,
40303,
37822,
38161,
40935,
40308,
40743,
38490,
40798,
40976,
39497,
38709,
40597,
38389,
39348,
37871,
38751,
40573,
40958,
41293,
40655,
40081,
37967,
40235,
39608,
40993,
40625,
40187,
40644,
40504,
39457,
40377,
40139,
40651,
40343,
38261,
40350,
40549,
40938,
40366,
38042,
38565,
37985,
39885,
40775,
40933,
40921,
39781,


==> /var/log/gitlab/nginx/gitlab_access.log <==
************ - - [21/Apr/2025:08:37:21 +0000] "GET /assets/webpack/9.3ace988d.chunk.js HTTP/1.1" 200 188 "http://*************/zcx_androidproject/tanchisheduijue/-/project_members" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" -

==> /var/log/gitlab/gitlab-rails/production.log <==
Started GET "/uploads/-/system/user/avatar/85/avatar.png?width=40" for ************ at 2025-04-21 08:37:21 +0000
Processing by UploadsController#show as HTML
  Parameters: {"width"=>"40", "model"=>"user", "mounted_as"=>"avatar", "id"=>"85", "filename"=>"avatar.png"}

==> /var/log/gitlab/gitlab-workhorse/current <==
{"correlation_id":"01JSBQXAK5W4YEHQ6Z4ZVT2TZ3","encoding":"gzip","file":"/opt/gitlab/embedded/service/gitlab-rails/public/assets/lazy_bundles/select2-718700608a42cbeb8139cf2277773fe17c47e05624e5a773f3509fca1408a56d.css","level":"info","method":"GET","msg":"Send static file","time":"2025-04-21T08:37:21Z","uri":"/assets/lazy_bundles/select2-718700608a42cbeb8139cf2277773fe17c47e05624e5a773f3509fca1408a56d.css"}
{"content_type":"text/css; charset=utf-8","correlation_id":"01JSBQXAK5W4YEHQ6Z4ZVT2TZ3","duration_ms":0,"host":"*************","level":"info","method":"GET","msg":"access","proto":"HTTP/1.1","referrer":"http://*************/zcx_androidproject/tanchisheduijue/-/project_members","remote_addr":"127.0.0.1:0","remote_ip":"127.0.0.1","route":"^/assets/","status":200,"system":"http","time":"2025-04-21T08:37:21Z","ttfb_ms":0,"uri":"/assets/lazy_bundles/select2-718700608a42cbeb8139cf2277773fe17c47e05624e5a773f3509fca1408a56d.css","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","written_bytes":3739}

==> /var/log/gitlab/nginx/gitlab_access.log <==
************ - - [21/Apr/2025:08:37:21 +0000] "GET /assets/lazy_bundles/select2-718700608a42cbeb8139cf2277773fe17c47e05624e5a773f3509fca1408a56d.css HTTP/1.1" 200 3758 "http://*************/zcx_androidproject/tanchisheduijue/-/project_members" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" -

==> /var/log/gitlab/gitlab-rails/production.log <==
Completed 200 OK in 24ms (ActiveRecord: 4.1ms | Elasticsearch: 0.0ms | Allocations: 3355)

==> /var/log/gitlab/gitlab-rails/production_json.log <==
{"method":"GET","path":"/uploads/-/system/user/avatar/85/avatar.png","format":"html","controller":"UploadsController","action":"show","status":200,"time":"2025-04-21T08:37:21.142Z","params":[{"key":"width","value":"40"},{"key":"model","value":"user"},{"key":"mounted_as","value":"avatar"},{"key":"id","value":"85"},{"key":"filename","value":"avatar.png"}],"remote_ip":"************","user_id":98,"username":"e.zhang","ua":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","correlation_id":"01JSBQXAJ4RYWNH52820SHHK3T","meta.user":"e.zhang","meta.caller_id":"UploadsController#show","meta.remote_ip":"************","meta.feature_category":"not_owned","meta.client_id":"user/98","redis_calls":1,"redis_duration_s":0.000493,"redis_read_bytes":181,"redis_write_bytes":702,"redis_shared_state_calls":1,"redis_shared_state_duration_s":0.000493,"redis_shared_state_read_bytes":181,"redis_shared_state_write_bytes":702,"db_count":3,"db_write_count":0,"db_cached_count":0,"cpu_s":0.043393,"mem_objects":9440,"mem_bytes":491976,"mem_mallocs":1318,"queue_duration_s":0.011679,"db_duration_s":0.00405,"view_duration_s":0.0,"duration_s":0.02416}

==> /var/log/gitlab/gitlab-workhorse/current <==
{"correlation_id":"01JSBQXAJ4RYWNH52820SHHK3T","duration_s":0.071149107,"imageresizer.content_type":"image/png","imageresizer.original_filesize":56638,"imageresizer.status":"success","imageresizer.target_width":"40","level":"info","method":"GET","msg":"success","subsystem":"imageresizer","time":"2025-04-21T08:37:21Z","uri":"/uploads/-/system/user/avatar/85/avatar.png?width=40","written_bytes":3482}
{"content_type":"image/png","correlation_id":"01JSBQXAJ4RYWNH52820SHHK3T","duration_ms":123,"host":"*************","level":"info","method":"GET","msg":"access","proto":"HTTP/1.1","referrer":"http://*************/zcx_androidproject/tanchisheduijue/-/project_members","remote_addr":"127.0.0.1:0","remote_ip":"127.0.0.1","route":"","status":200,"system":"http","time":"2025-04-21T08:37:21Z","ttfb_ms":123,"uri":"/uploads/-/system/user/avatar/85/avatar.png?width=40","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","written_bytes":3482}

==> /var/log/gitlab/nginx/gitlab_access.log <==
************ - - [21/Apr/2025:08:37:21 +0000] "GET /uploads/-/system/user/avatar/85/avatar.png?width=40 HTTP/1.1" 200 3494 "http://*************/zcx_androidproject/tanchisheduijue/-/project_members" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" -

==> /var/log/gitlab/gitlab-rails/production.log <==
Started GET "/g2/diy-project.git/info/refs/?service=git-upload-pack" for ************ at 2025-04-21 08:37:21 +0000
Processing by Repositories::GitHttpController#info_refs as */*
  Parameters: {"service"=>"git-upload-pack", "repository_path"=>"g2/diy-project.git"}
Filter chain halted as :authenticate_user rendered or redirected
Completed 401 Unauthorized in 15ms (Views: 0.7ms | ActiveRecord: 3.0ms | Elasticsearch: 0.0ms | Allocations: 2701)

==> /var/log/gitlab/gitlab-rails/production_json.log <==
{"method":"GET","path":"/g2/diy-project.git/info/refs","format":"*/*","controller":"Repositories::GitHttpController","action":"info_refs","status":401,"time":"2025-04-21T08:37:21.897Z","params":[{"key":"service","value":"git-upload-pack"},{"key":"repository_path","value":"g2/diy-project.git"}],"remote_ip":"************","user_id":null,"username":null,"ua":"git/2.39.1.windows.1","db_count":1,"db_write_count":0,"db_cached_count":0,"cpu_s":0.041087,"mem_objects":5695,"mem_bytes":463744,"mem_mallocs":1190,"correlation_id":"01JSBQXB9T8P7TK6E6EYT99C32","db_duration_s":0.00303,"view_duration_s":0.00073,"duration_s":0.01535}

==> /var/log/gitlab/gitlab-workhorse/current <==
{"content_type":"text/plain; charset=utf-8","correlation_id":"01JSBQXB9T8P7TK6E6EYT99C32","duration_ms":48,"host":"*************","level":"info","method":"GET","msg":"access","proto":"HTTP/1.1","referrer":"","remote_addr":"127.0.0.1:0","remote_ip":"127.0.0.1","route":"^/.+\\.git/info/refs\\z","status":401,"system":"http","time":"2025-04-21T08:37:21Z","ttfb_ms":48,"uri":"/g2/diy-project.git/info/refs?service=git-upload-pack","user_agent":"git/2.39.1.windows.1","written_bytes":26}

==> /var/log/gitlab/nginx/gitlab_access.log <==
************ - - [21/Apr/2025:08:37:21 +0000] "GET /g2/diy-project.git/info/refs?service=git-upload-pack HTTP/1.1" 401 26 "" "git/2.39.1.windows.1" -

==> /var/log/gitlab/sshd/current <==
2025-04-21_08:37:21.96154 @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
2025-04-21_08:37:21.96157 @         WARNING: UNPROTECTED PRIVATE KEY FILE!          @
2025-04-21_08:37:21.96158 @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
2025-04-21_08:37:21.96158 Permissions 0755 for '/etc/gitlab/ssh_host_rsa_key' are too open.
2025-04-21_08:37:21.96160 It is required that your private key files are NOT accessible by others.
2025-04-21_08:37:21.96161 This private key will be ignored.
2025-04-21_08:37:21.96161 Unable to load host key "/etc/gitlab/ssh_host_rsa_key": bad permissions
2025-04-21_08:37:21.96168 Unable to load host key: /etc/gitlab/ssh_host_rsa_key
2025-04-21_08:37:21.96171 @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
2025-04-21_08:37:21.96173 @         WARNING: UNPROTECTED PRIVATE KEY FILE!          @
2025-04-21_08:37:21.96174 @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
2025-04-21_08:37:21.96175 Permissions 0755 for '/etc/gitlab/ssh_host_ecdsa_key' are too open.
2025-04-21_08:37:21.96175 It is required that your private key files are NOT accessible by others.
2025-04-21_08:37:21.96175 This private key will be ignored.
2025-04-21_08:37:21.96176 Unable to load host key "/etc/gitlab/ssh_host_ecdsa_key": bad permissions
2025-04-21_08:37:21.96214 Unable to load host key: /etc/gitlab/ssh_host_ecdsa_key
2025-04-21_08:37:21.96216 @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
2025-04-21_08:37:21.96217 @         WARNING: UNPROTECTED PRIVATE KEY FILE!          @
2025-04-21_08:37:21.96217 @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
2025-04-21_08:37:21.96218 Permissions 0755 for '/etc/gitlab/ssh_host_ed25519_key' are too open.
2025-04-21_08:37:21.96218 It is required that your private key files are NOT accessible by others.
2025-04-21_08:37:21.96219 This private key will be ignored.
2025-04-21_08:37:21.96221 Unable to load host key "/etc/gitlab/ssh_host_ed25519_key": bad permissions
2025-04-21_08:37:21.96221 Unable to load host key: /etc/gitlab/ssh_host_ed25519_key
2025-04-21_08:37:21.96226 sshd: no hostkeys available -- exiting.

==> /var/log/gitlab/gitlab-rails/production.log <==
Started GET "/g2/diy-project.git/info/refs/?service=git-upload-pack" for ************ at 2025-04-21 08:37:21 +0000
Processing by Repositories::GitHttpController#info_refs as */*
  Parameters: {"service"=>"git-upload-pack", "repository_path"=>"g2/diy-project.git"}
Completed 200 OK in 186ms (Views: 0.5ms | ActiveRecord: 9.1ms | Elasticsearch: 0.0ms | Allocations: 10618)

==> /var/log/gitlab/gitlab-rails/production_json.log <==
{"method":"GET","path":"/g2/diy-project.git/info/refs","format":"*/*","controller":"Repositories::GitHttpController","action":"info_refs","status":200,"time":"2025-04-21T08:37:22.201Z","params":[{"key":"service","value":"git-upload-pack"},{"key":"repository_path","value":"g2/diy-project.git"}],"remote_ip":"************","user_id":170,"username":"jiangcomcn","ua":"git/2.39.1.windows.1","correlation_id":"01JSBQXBEBMHCTAYXC7NP34G4G","meta.user":"jiangcomcn","meta.project":"g2/diy-project","meta.root_namespace":"g2","meta.caller_id":"Repositories::GitHttpController#info_refs","meta.remote_ip":"************","meta.feature_category":"source_code_management","meta.client_id":"user/170","redis_calls":2,"redis_duration_s":0.000731,"redis_read_bytes":312,"redis_write_bytes":86,"redis_cache_calls":2,"redis_cache_duration_s":0.000731,"redis_cache_read_bytes":312,"redis_cache_write_bytes":86,"db_count":10,"db_write_count":0,"db_cached_count":1,"cpu_s":0.197622,"mem_objects":13626,"mem_bytes":860936,"mem_mallocs":2919,"db_duration_s":0.00909,"view_duration_s":0.00051,"duration_s":0.18635}

==> /var/log/gitlab/gitlab-workhorse/current <==
{"content_type":"application/x-git-upload-pack-advertisement","correlation_id":"01JSBQXBEBMHCTAYXC7NP34G4G","duration_ms":213,"host":"*************","level":"info","method":"GET","msg":"access","proto":"HTTP/1.1","referrer":"","remote_addr":"127.0.0.1:0","remote_ip":"127.0.0.1","route":"^/.+\\.git/info/refs\\z","status":200,"system":"http","time":"2025-04-21T08:37:22Z","ttfb_ms":210,"uri":"/g2/diy-project.git/info/refs?service=git-upload-pack","user_agent":"git/2.39.1.windows.1","written_bytes":145}

==> /var/log/gitlab/gitaly/current <==
{"command.count":1,"command.inblock":0,"command.majflt":0,"command.maxrss":235264,"command.minflt":399,"command.oublock":0,"command.real_time_ms":3,"command.system_time_ms":1,"command.user_time_ms":1,"correlation_id":"01JSBQXBEBMHCTAYXC7NP34G4G","grpc.code":"OK","grpc.meta.auth_version":"v2","grpc.meta.client_name":"gitlab-workhorse","grpc.meta.deadline_type":"none","grpc.method":"InfoRefsUploadPack","grpc.request.fullMethod":"/gitaly.SmartHTTPService/InfoRefsUploadPack","grpc.request.glProjectPath":"g2/diy-project","grpc.request.glRepository":"project-2258","grpc.request.repoPath":"@hashed/ea/21/ea215720034a4c3073d7a7886b27431b89805c01b18329b8af22bc4113a668a4.git","grpc.request.repoStorage":"default","grpc.request.topLevelGroup":"@hashed","grpc.service":"gitaly.SmartHTTPService","grpc.start_time":"2025-04-21T08:37:22.204Z","grpc.time_ms":3.803,"level":"info","msg":"finished streaming call with code OK","peer.address":"@","pid":356,"span.kind":"server","system":"grpc","time":"2025-04-21T08:37:22.207Z"}

==> /var/log/gitlab/nginx/gitlab_access.log <==
************ - jiangcomcn [21/Apr/2025:08:37:22 +0000] "GET /g2/diy-project.git/info/refs?service=git-upload-pack HTTP/1.1" 200 145 "" "git/2.39.1.windows.1" -

==> /var/log/gitlab/gitlab-rails/production.log <==
Started POST "/g2/diy-project.git/git-upload-pack/" for ************ at 2025-04-21 08:37:22 +0000
Processing by Repositories::GitHttpController#git_upload_pack as
  Parameters: {"repository_path"=>"g2/diy-project.git"}
Completed 200 OK in 200ms (Views: 0.6ms | ActiveRecord: 18.2ms | Elasticsearch: 0.0ms | Allocations: 10793)

==> /var/log/gitlab/gitlab-rails/production_json.log <==
{"method":"POST","path":"/g2/diy-project.git/git-upload-pack","format":null,"controller":"Repositories::GitHttpController","action":"git_upload_pack","status":200,"time":"2025-04-21T08:37:22.428Z","params":[{"key":"repository_path","value":"g2/diy-project.git"}],"remote_ip":"************","user_id":170,"username":"jiangcomcn","ua":"git/2.39.1.windows.1","correlation_id":"01JSBQXBN3DAZQFCZTDBF9YF1Y","meta.user":"jiangcomcn","meta.project":"g2/diy-project","meta.root_namespace":"g2","meta.caller_id":"Repositories::GitHttpController#git_upload_pack","meta.remote_ip":"************","meta.feature_category":"source_code_management","meta.client_id":"user/170","redis_calls":1,"redis_duration_s":0.000366,"redis_read_bytes":109,"redis_write_bytes":43,"redis_cache_calls":1,"redis_cache_duration_s":0.000366,"redis_cache_read_bytes":109,"redis_cache_write_bytes":43,"db_count":12,"db_write_count":1,"db_cached_count":1,"cpu_s":0.196301,"mem_objects":13877,"mem_bytes":857344,"mem_mallocs":2972,"db_duration_s":0.01819,"view_duration_s":0.00063,"duration_s":0.20059}

==> /var/log/gitlab/gitaly/current <==
{"correlation_id":"01JSBQXBN3DAZQFCZTDBF9YF1Y","grpc.meta.auth_version":"v2","grpc.meta.client_name":"gitlab-workhorse","grpc.meta.deadline_type":"none","grpc.method":"PostUploadPack","grpc.request.fullMethod":"/gitaly.SmartHTTPService/PostUploadPack","grpc.request.glProjectPath":"g2/diy-project","grpc.request.glRepository":"project-2258","grpc.request.repoPath":"@hashed/ea/21/ea215720034a4c3073d7a7886b27431b89805c01b18329b8af22bc4113a668a4.git","grpc.request.repoStorage":"default","grpc.request.topLevelGroup":"@hashed","grpc.service":"gitaly.SmartHTTPService","grpc.start_time":"2025-04-21T08:37:22.431Z","level":"info","msg":"request details","peer.address":"@","pid":356,"request_sha":"7ad7e2eb5d78f68c7f086320ce226bbe2754b43a","response_bytes":4227,"span.kind":"server","system":"grpc","time":"2025-04-21T08:37:22.438Z"}
{"command.count":1,"command.inblock":0,"command.majflt":0,"command.maxrss":235264,"command.minflt":704,"command.oublock":0,"command.real_time_ms":6,"command.system_time_ms":3,"command.user_time_ms":1,"correlation_id":"01JSBQXBN3DAZQFCZTDBF9YF1Y","grpc.code":"OK","grpc.meta.auth_version":"v2","grpc.meta.client_name":"gitlab-workhorse","grpc.meta.deadline_type":"none","grpc.method":"PostUploadPack","grpc.request.fullMethod":"/gitaly.SmartHTTPService/PostUploadPack","grpc.request.glProjectPath":"g2/diy-project","grpc.request.glRepository":"project-2258","grpc.request.repoPath":"@hashed/ea/21/ea215720034a4c3073d7a7886b27431b89805c01b18329b8af22bc4113a668a4.git","grpc.request.repoStorage":"default","grpc.request.topLevelGroup":"@hashed","grpc.service":"gitaly.SmartHTTPService","grpc.start_time":"2025-04-21T08:37:22.431Z","grpc.time_ms":7.599,"level":"info","msg":"finished streaming call with code OK","peer.address":"@","pid":356,"span.kind":"server","system":"grpc","time":"2025-04-21T08:37:22.438Z"}

==> /var/log/gitlab/gitlab-workhorse/current <==
{"content_type":"application/x-git-upload-pack-result","correlation_id":"01JSBQXBN3DAZQFCZTDBF9YF1Y","duration_ms":227,"host":"*************","level":"info","method":"POST","msg":"access","proto":"HTTP/1.1","referrer":"","remote_addr":"127.0.0.1:0","remote_ip":"127.0.0.1","route":"^/.+\\.git/git-upload-pack\\z","status":200,"system":"http","time":"2025-04-21T08:37:22Z","ttfb_ms":225,"uri":"/g2/diy-project.git/git-upload-pack","user_agent":"git/2.39.1.windows.1","written_bytes":4227}
