-- 竞品数据分析表
CREATE TABLE `dn_competitor_data` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `date` varchar(20) DEFAULT NULL COMMENT '日期',
  `time` varchar(20) DEFAULT NULL COMMENT '时间',
  `company` varchar(100) DEFAULT NULL COMMENT '公司主体',
  `appName` varchar(100) DEFAULT NULL COMMENT '产品名称',
  `device` varchar(100) DEFAULT NULL COMMENT '设备型号',
  `osVersion` varchar(50) DEFAULT NULL COMMENT '安卓版本',
  `sdkVersion` varchar(50) DEFAULT NULL COMMENT 'sdk版本',
  `appVersion` varchar(50) DEFAULT NULL COMMENT '包体版本',
  `duration` int(11) DEFAULT '0' COMMENT '时长',
  `adType` varchar(50) DEFAULT NULL COMMENT '广告类型',
  `sourceCounts` int(11) DEFAULT '0' COMMENT '广告源数量',
  `loadSuccCounts` int(11) DEFAULT '0' COMMENT '加载成功数',
  `loadFailCounts` int(11) DEFAULT '0' COMMENT '加载失败数',
  `selfShow` int(11) DEFAULT '0' COMMENT '自有广告展示数',
  `apiShow` int(11) DEFAULT '0' COMMENT '三方api广告展示数',
  `sdkShow` int(11) DEFAULT '0' COMMENT '三方sdk广告展示数',
  `secondShow` int(11) DEFAULT '0' COMMENT '分钟展示数',
  `totalShow` int(11) DEFAULT '0' COMMENT '总展示数',
  `selfClick` int(11) DEFAULT '0' COMMENT '自有广告点击数',
  `apiClick` int(11) DEFAULT '0' COMMENT '三方api广告点击数',
  `sdkClick` int(11) DEFAULT '0' COMMENT '三方sdk广告点击数',
  `secondClick` int(11) DEFAULT '0' COMMENT '分钟点击数',
  `totalClick` int(11) DEFAULT '0' COMMENT '总点击数',
  `selfShowRate` varchar(255) DEFAULT NULL COMMENT '自有广告展示率',
  `apiShowRate` varchar(255) DEFAULT NULL COMMENT '三方api广告展示率',
  `sdkShowRate` varchar(255) DEFAULT NULL COMMENT '三方sdk广告展示率',
  `totalShowRate` varchar(255) DEFAULT NULL COMMENT '总展示率',
  `selfClickRate` varchar(255) DEFAULT NULL COMMENT '自有广告点击率',
  `apiClickRate` varchar(255) DEFAULT NULL COMMENT '三方api广告点击率',
  `sdkClickRate` varchar(255) DEFAULT NULL COMMENT '三方sdk广告点击率',
  `totalClickRate` varchar(255) DEFAULT NULL COMMENT '总点击率',
  `createtime` datetime NOT NULL DEFAULT NOW() COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT NOW() COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idxdate` (`date`),
  KEY `idxcompany` (`company`),
  KEY `idxappName` (`appName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='竞品数据分析表';