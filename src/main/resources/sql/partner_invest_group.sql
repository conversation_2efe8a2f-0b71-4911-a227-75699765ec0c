/*
数据库表创建和数据同步SQL

表名：partner_invest_group
描述：合作方投放明细聚合数据表
作者：系统生成
日期：2023-07-10
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for partner_invest_group
-- ----------------------------
DROP TABLE IF EXISTS `partner_invest_group`;
CREATE TABLE `partner_invest_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tdate` varchar(10) NOT NULL COMMENT '日期',
  `app_category` varchar(50) DEFAULT NULL COMMENT '应用分类',
  `appname` varchar(100) DEFAULT NULL COMMENT '应用名称',
  `appid` varchar(50) NOT NULL COMMENT '应用ID',
  `channelType` varchar(100) DEFAULT NULL COMMENT '渠道类型',
  `media` varchar(100) DEFAULT NULL COMMENT '媒体',
  `spend` decimal(20,2) DEFAULT '0.0000000000' COMMENT '消耗(N)',
  `rebateSpend` decimal(20,2) DEFAULT '0.0000000000' COMMENT '返点消耗',
  `reduceAmount` decimal(20,2) DEFAULT '0.0000000000' COMMENT '核减金额',
  `consume` decimal(20,10) GENERATED ALWAYS AS (spend - reduceAmount) STORED COMMENT '消耗',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `ischeck` char(1) DEFAULT '0' COMMENT '审核状态：0-未审核，1-已审核，2-已调整',
  PRIMARY KEY (`id`),
  KEY `idx_tdate` (`tdate`),
  KEY `idx_appid` (`appid`),
  KEY `idx_media` (`media`),
  KEY `idx_channel_type` (`channelType`),
  KEY `idx_app_category` (`app_category`),
  KEY `idx_composite` (`tdate`, `appid`, `media`, `channelType`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合作方投放明细聚合数据表';

-- ----------------------------
-- 同步数据的SQL语句
-- ----------------------------

-- 从dn_report_spend_china_2020表同步数据到partner_invest_group表
INSERT INTO partner_invest_group (tdate, app_category, appname, appid, channelType, media, spend, rebateSpend, reduceAmount)
SELECT 
    d.day AS tdate,
    a.app_category AS app_category,
    a.app_name AS appname,
    d.app AS appid,
    d.channelType AS channelType,
    d.media AS media,
    IFNULL(d.spend, 0) AS spend,
    IFNULL(d.rebateSpend, 0) AS rebateSpend,
    0 AS reduceAmount  -- 核减金额默认为0，需要手动填写
FROM 
    dn_report_spend_china_2020 d
LEFT JOIN 
    app_info a ON d.app = a.id
WHERE 
    d.day BETWEEN :startDate AND :endDate
GROUP BY 
    d.day, a.app_category, a.app_name, d.app, d.channelType, d.media;

-- ----------------------------
-- 查询示例
-- ----------------------------

-- 按照维度查询数据
SELECT 
    tdate,
    app_category,
    appname,
    appid,
    channelType,
    media,
    spend,
    rebateSpend,
    reduceAmount,
    consume
FROM 
    partner_invest_group
WHERE 
    tdate BETWEEN :startDate AND :endDate
    -- 可选条件
    -- AND app_category IN (:appCategories)
    -- AND appid IN (:appIds)
    -- AND channelType IN (:channelTypes)
    -- AND media IN (:medias)
GROUP BY 
    -- 根据需要选择分组维度
    tdate, app_category, appname, appid, channelType, media
ORDER BY 
    tdate DESC;

-- 汇总查询
SELECT 
    SUM(spend) AS total_spend,
    SUM(rebateSpend) AS total_rebate_spend,
    SUM(reduceAmount) AS total_reduce_amount,
    SUM(consume) AS total_consume
FROM 
    partner_invest_group
WHERE 
    tdate BETWEEN :startDate AND :endDate;