/*
Navicat MySQL Data Transfer

Source Server         : 38测试
Source Server Version : 50731
Source Host           : ************:3306
Source Database       : yyhz_0308

Target Server Type    : MYSQL
Target Server Version : 50731
File Encoding         : 65001

Date: 2021-05-27 16:35:40
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for kb_product_price_config
-- ----------------------------
DROP TABLE IF EXISTS `kb_product_price_config`;
CREATE TABLE `kb_product_price_config` (
  `product_id` varchar(32) NOT NULL COMMENT '商品编号',
  `product_name` varchar(64) DEFAULT NULL COMMENT '商品名称',
  `product_short_name` varchar(32) DEFAULT NULL COMMENT '商品简称',
  `price` double(10,2) DEFAULT NULL COMMENT '商品价值 单位（分）',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `statues` tinyint(2) DEFAULT '1' COMMENT '状态  1：开启  0：关闭',
  `cuser` varchar(64) DEFAULT NULL COMMENT '创建人',
  `createtime` date DEFAULT NULL COMMENT '创建时间',
  `euser` varchar(64) DEFAULT NULL COMMENT '最后修改人',
  `endtime` date DEFAULT NULL COMMENT '最后修改时间',
  PRIMARY KEY (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for kb_product_revenue
-- ----------------------------
DROP TABLE IF EXISTS `kb_product_revenue`;
CREATE TABLE `kb_product_revenue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `appid` varchar(10) DEFAULT NULL COMMENT '应用id',
  `cha` varchar(32) DEFAULT NULL COMMENT '渠道',
  `app_ver` varchar(32) DEFAULT NULL COMMENT '版本',
  `product_id` varchar(32) DEFAULT NULL COMMENT '产品id',
  `product_name` varchar(64) DEFAULT NULL COMMENT '产品名称',
  `date` date DEFAULT NULL COMMENT '日期',
  `revenue` double(10,1) DEFAULT NULL COMMENT '总收入',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4;
