/*
Navicat MySQL Data Transfer

Source Server         : xyxtj
Source Server Version : 50616
Source Host           : rm-wz9v23lm6wlar701ido.mysql.rds.aliyuncs.com:3306
Source Database       : xyxtj

Target Server Type    : MYSQL
Target Server Version : 50616
File Encoding         : 65001

Date: 2021-05-27 16:36:38
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for kb_coupons_record
-- ----------------------------
DROP TABLE IF EXISTS `kb_coupons_record`;
CREATE TABLE `kb_coupons_record` (
  `order_id` varchar(32) NOT NULL COMMENT '公司自己的订单ID',
  `kb_order_id` varchar(32) DEFAULT NULL COMMENT '坤彪网络交易订单号',
  `kb_order_time` varchar(32) DEFAULT NULL COMMENT '坤彪订单yyyyMMddHHmmss格式时间戳',
  `user_flag` tinyint(2) DEFAULT NULL COMMENT '0：outTokenId 和手机号都 必 传 ；\r\n 1：outTokenId 不传，手机 号 必 传 ；\r\n2：outTokenId 必传，手机号不传',
  `mobile` varchar(255) DEFAULT NULL COMMENT '兑换手机号，userFlag 为2 时选填',
  `out_token_id` varchar(64) DEFAULT NULL,
  `is_sued` tinyint(2) DEFAULT '1' COMMENT ' 1：初始化状态 2：成功  3：失败',
  `lss_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '下发时间',
  `send_no` varchar(32) DEFAULT NULL COMMENT '下发编号',
  `partner_id` varchar(64) DEFAULT NULL,
  `store_id` varchar(64) DEFAULT NULL,
  `request_id` varchar(32) DEFAULT NULL,
  `appid` varchar(32) DEFAULT NULL,
  `pid` varchar(32) DEFAULT NULL,
  `cha` varchar(128) DEFAULT NULL,
  `app_ver` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `INDEX_ORDERID` (`order_id`) USING BTREE,
  UNIQUE KEY `INDEX_KB_ORDERID` (`kb_order_id`) USING BTREE,
  KEY `INDEX_TOKEN` (`out_token_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='坤彪网络发券记录';

-- ----------------------------
-- Table structure for kb_product_exchange_record
-- ----------------------------
DROP TABLE IF EXISTS `kb_product_exchange_record`;
CREATE TABLE `kb_product_exchange_record` (
  `kb_order_id` varchar(32) NOT NULL,
  `product_id` varchar(32) DEFAULT NULL COMMENT '商品编号',
  `quantity` int(4) DEFAULT NULL COMMENT '兑换数量',
  `points` int(255) DEFAULT NULL COMMENT '单个商品积分',
  KEY `INDEX_KB_ORDERID` (`kb_order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- ----------------------------
-- Table structure for kb_ticket_record
-- ----------------------------
DROP TABLE IF EXISTS `kb_ticket_record`;
CREATE TABLE `kb_ticket_record` (
  `ticket_no` varchar(32) NOT NULL,
  `order_id` varchar(32) DEFAULT NULL COMMENT '自己的订单id',
  `kb_order_id` varchar(32) DEFAULT NULL COMMENT '坤彪网络交易订单号',
  `product_id` varchar(32) DEFAULT NULL COMMENT '商品id',
  `ticket_secret` varchar(255) DEFAULT NULL COMMENT '券密',
  PRIMARY KEY (`ticket_no`),
  KEY `INDEX_KB_ORDERID` (`kb_order_id`) USING BTREE,
  KEY `INDEX_ORDER_ID` (`order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
