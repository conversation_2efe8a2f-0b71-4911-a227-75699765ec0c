server.port=${port:6105}
server.tomcat.uri-encoding=UTF-8
server.max-http-header-size=20971520
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
#server.tomcat.max-connections=15000
logging.level.com.wbgame.mapper=debug
#logging.level.com.wbgame.service.*=DEBUG
logging.level.org.apache.http=INFO

## 基础文件目录
cityUtil.locations=/cfg/17monipdb.datx
tempPath=/home/<USER>/src
savePath=/home/<USER>
commonPath=/home/<USER>

album_id=12000010319
cash.refresh.track=https://track.vzhifu.net
#是否开启定时任务?true:false
schedule.enabled=false

#禁止生产环境打开swagger接口
knife4j.production=false


# 亚马逊s3配置
S3.region=us-west-1
S3.accessKeyId=test
S3.accessKeySecret=test
S3.bucketName=tik-game-com-web


#ADB 云原生数据库
spring.datasource.adb.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.adb.url=******************************************************************************************************************************************************************************
spring.datasource.adb.username=caow
spring.datasource.adb.password=caow@1227

#ADB 用户画像bi库
spring.datasource.viuadb.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.viuadb.url=******************************************************************************************************************************************************************************************
spring.datasource.viuadb.username=adb_test
spring.datasource.viuadb.password=adb_test0511



#变现系统数据源
spring.datasource.adv2.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.adv2.url=************************************************************************************************************************************************
spring.datasource.adv2.username=db_fin
spring.datasource.adv2.password=Dnwx@user!@#1021


#清理王数据库
spring.datasource.clean.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.clean.url=*************************************************************************************************************************************************
spring.datasource.clean.username=db_fin
spring.datasource.clean.password=Dnwx@user!@#1021

#yyhz_0308数据库
spring.datasource.master.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.master.url=*************************************************************************************************************************************************
spring.datasource.master.username=db_fin
spring.datasource.master.password=Dnwx@user!@#1021


#xyxtj数据库
spring.datasource.slave.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.slave.url=*********************************************************************************************************************************************
spring.datasource.slave.username=db_fin
spring.datasource.slave.password=Dnwx@user!@#1021


#admsg数据库
spring.datasource.slave2.driver-class-name=com.mysql.jdbc.Driver
# 外网
spring.datasource.slave2.url=*************************************************************************************************************************************************
spring.datasource.slave2.username=db_fin
spring.datasource.slave2.password=Dnwx@user!@#1021


#nnjy海外数据库
spring.datasource.haiwai2.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.haiwai2.url=*************************************************************************************************************************************************
spring.datasource.haiwai2.username=db_fin
spring.datasource.haiwai2.password=Dnwx@user!@#1021


#投放系统数据库
spring.datasource.tfxt.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.tfxt.url=************************************************************************************************************************************************
spring.datasource.tfxt.username=db_fin
spring.datasource.tfxt.password=Dnwx@user!@#1021

#用户画像数据库
spring.datasource.usertag.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.usertag.url=*************************************************************************************************************************************************
spring.datasource.usertag.username=db_fin
spring.datasource.usertag.password=Dnwx@user!@#1021

# 游戏城
spring.datasource.game.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.game.url=**************************************************************************************************************************************************
spring.datasource.game.username=db_fin
spring.datasource.game.password=Dnwx@user!@#1021

#红包独立数据库
spring.datasource.redpack.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.redpack.url=*************************************************************************************************************************************************
spring.datasource.redpack.username=db_fin
spring.datasource.redpack.password=Dnwx@user!@#1021


# 中度游戏数据库
spring.datasource.zdgame.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.zdgame.url=*************************************************************************************************************************************************
spring.datasource.zdgame.username=db_fin
spring.datasource.zdgame.password=Dnwx@user!@#1021

#游戏game库
spring.datasource.gameother.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.gameother.url=*************************************************************************************************************************************************
spring.datasource.gameother.username=db_fin
spring.datasource.gameother.password=Dnwx@user!@#1021

# 海外广告配置
#spring.datasource.haiwaiad.driver-class-name=com.mysql.jdbc.Driver
#spring.datasource.haiwaiad.url=*************************************************************************************************************************************************
#spring.datasource.haiwaiad.username=db_fin
#spring.datasource.haiwaiad.password=Dnwx@user!@#1021
spring.datasource.haiwaiad.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.haiwaiad.url=*********************************************************************************************************************************************************************************
spring.datasource.haiwaiad.username=cfg_adb
spring.datasource.haiwaiad.password=cfg_adb@0411

# 海外adb库
spring.datasource.haiwai2adb.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.haiwai2adb.url=*************************************************************************************************************************************************
spring.datasource.haiwai2adb.username=db_fin
spring.datasource.haiwai2adb.password=Dnwx@user!@#1021

# 乐影数据库
spring.datasource.leying.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.leying.url=************************************************************************************************************************************************************************************
spring.datasource.leying.username=db_fin
spring.datasource.leying.password=Dnwx@user!@#1021

spring.datasource.track.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.track.url=****************************************************************************************************************************************************************************************************************
spring.datasource.track.username=dnwx_bi_en_adb
spring.datasource.track.password=bi_en_adb@0526


#数据连接池
spring.datasource.initialSize=20
spring.datasource.minIdle=20
spring.datasource.maxActive=50
spring.datasource.maxWait=60000
spring.datasource.validationQuery=SELECT 1 FROM DUAL
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=true
spring.datasource.testOnReturn=false
spring.datasource.timeBetweenEvictionRunsMillis=60000
spring.datasource.minEvictableIdleTimeMillis=300000
spring.datasource.removeAbandoned=true
spring.datasource.removeAbandonedTimeout=1800
spring.datasource.poolPreparedStatements=false
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20

#redis
spring.redis.host=************
spring.redis.port=6379
spring.redis.password=dnwx.123
spring.redis.pool.maxIdle=30
spring.redis.pool.minIdle=10
spring.redis.pool.maxTotal=200
spring.redis.pool.maxWaitMillis=3000
spring.redis.timeout=3000

# 检查无效连接
spring.redis.pool.testOnCreate=false
spring.redis.pool.testOnReturn=false
spring.redis.pool.testOnBorrow=true
spring.redis.pool.testWhileIdle=true
spring.redis.pool.minEvictableIdleTimeMills=60000
spring.redis.pool.timeBetweenEvictionRunsMillis=30000

# 红包redis
spring.redis.redPack.host=************
spring.redis.redPack.port=6379
spring.redis.redPack.password=dnwx.123


jetcache.local.default.type=linkedhashmap
jetcache.local.default.keyConvertor=fastjson
#jetcache.local.default.expireAfterWriteInMillis=6000000

# 最大支持文件大小
spring.http.multipart.max-file-size=800Mb
# 最大支持请求大小
spring.http.multipart.max-request-size=800Mb