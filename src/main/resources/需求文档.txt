表字段如下：
日期(_date),时间(_time),公司主体(_company),产品名称(_appName),设备型号(_device),安卓版本(_osVersion),sdk版本(_sdkVersion),包体版本(_appVersion),时长(_duration),广告类型(_adType),广告源数量(_sourceCounts),加载成功数(_loadSuccCounts),加载失败数(_loadFailCounts),自有广告展示数(_selfShow),三方api广告展示数(_apiShow),三方sdk广告展示数(_sdkShow),分钟展示数(_secondShow),总展示数(_totalShow),自有广告点击数(_selfClick),三方api广告点击数(_apiClick),三方sdk广告点击数(_sdkClick),分钟点击数(_secondClick),总点击数(_totalClick),自有广告展示率(_selfShowRate),三方api广告展示率(_apiShowRate),三方sdk广告展示率(_sdkShowRate),总展示率(_totalShowRate),自有广告点击率(_selfClickRate),三方api广告点击率(_apiClickRate),三方sdk广告点击率(_sdkClickRate),总点击率(_totalClickRate)
1.页面路径（综合运营系统 -> 渠道联运 -> 竞品数据分析表）
2.需提供日志上传入口，上传的日志内容格式如下（公司主体无法从日志中获取，需在上传文件的时候进行关联）
3.日志上传后自动进行解析
4.报表中显示的内容为从日志中解析出的内容