package com.wbgame.mapper.adb;

import com.wbgame.pojo.advert.partnerNew.PartnerInvestGroupDTO;

import java.util.List;
import java.util.Map;

/**
 * 合作方投放明细聚合数据Mapper接口
 * <AUTHOR>
 */
public interface PartnerInvestGroupMapper {

    /**
     * 合作方投放明细聚合查询
     * @param dto 查询参数
     * @return 查询结果列表
     */
    List<Map<String, String>> selectPartnerInvestGroup(PartnerInvestGroupDTO dto);
    
    /**
     * 合作方投放明细聚合汇总查询
     * @param dto 查询参数
     * @return 汇总结果
     */
    Map<String, String> selectPartnerInvestGroupSum(PartnerInvestGroupDTO dto);
    
    /**
     * 从dn_report_spend_china表同步数据
     * @ sdate 起始日期
     * @ edate 结束日期
     * @return 影响行数
     */
    int syncDataFromDnReport(PartnerInvestGroupDTO dto);
    
    /**
     * 更新核减金额
     * @param dto 参数，包含id和reduceAmount
     * @return 影响行数
     */
    int updateReduceAmount(PartnerInvestGroupDTO dto);
    
    /**
     * 审核数据
     * @param tdate 日期
     * @return 影响行数
     */
    int checkData(String tdate);
}