package com.wbgame.mapper.adb;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.wbgame.pojo.UmengChannelTotalVo;
import com.wbgame.pojo.adv2.reportEntity.ReportChina;
import com.wbgame.pojo.advert.UmengChannelTotalBigDataParam;
import com.wbgame.pojo.budgetWarning.CampaignBudget;
import com.wbgame.pojo.jettison.MaterialByWeekVisualization;
import com.wbgame.pojo.jettison.MaterialCondition;
import com.wbgame.pojo.jettison.param.EffectivenessManageParam;
import com.wbgame.pojo.jettison.report.AppleManualSpendVo;
import com.wbgame.pojo.jettison.report.dto.*;
import com.wbgame.pojo.jettison.report.param.*;
import com.wbgame.pojo.jettison.vo.EffectivenessManageVo;
import com.wbgame.pojo.product.SubscribeReportDTO;
import com.wbgame.pojo.product.SubscribeReportVO;
import com.wbgame.task.cameras.CameraCostCounter;
import com.wbgame.task.cameras.CameraStatisticPo;
import com.wbgame.task.cameras.WbPayInfoUserInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface DnwxBiAdtMapper {

    // 通用执行语句和查询语句
    @Update(" ${sql} ")
    int execSql(@Param("sql")String sql); // 直接执行DML sql语句
    @Select(" ${sql} ")
    List<Map<String, Object>> queryListMap(@Param("sql")String sql);

    void deleteAppInfo();

    void batchInsertAppInfo(List<Map<String, Object>> queryMap3);

    void deleteDnwxBiAppInfo();

    void batchInsertDnwxBiAppInfo(List<Map<String, Object>> queryMap3);

    List<Map<String,Object>> selectDnChaCashTotal(Map<String, Object> paramMap);
    List<ReportChina> selectCashTotalHuaweiOversea(@Param("date") String date);
    Map<String,Object> selectDnChaCashTotalSum(Map<String, Object> paramMap);

    int batchInsertXyxAdvData(List<JSONObject> data);

    Page<OperationReportDTO> getOperationReport(OperationReportParam param);
    OperationReportDTO getOperationSummary(OperationReportParam param);

    Page<OperationReportDTO> getOperationReport_v2(OperationReportParam param);
    OperationReportDTO getOperationSummary_v2(OperationReportParam param);

    //投放报表
    Page<SpendReportDTO> getSpendReport(SpendReportParam param);
    SpendReportDTO getSpendReportSummary(SpendReportParam param);
    //投放小时级别报表
    Page<SpendReportDTO> getHourSpendReport(SpendReportParam param);
    SpendReportDTO getHourSpendReportSummary(SpendReportParam param);
    List<SpendReportDTO> getHourSpendReportChart(SpendReportParam param);
    //直投报表
    Page<DirectReportDTO> getDirectReport(DirectReportParam param);
    DirectReportDTO getDirectReportSummary(DirectReportParam param);
    //素材报表
    Page<MaterialReportDTO> getMaterialReport(MaterialReportParam param);
    MaterialReportDTO getMaterialReportSummary(MaterialReportParam param);
    List<Map> getMaterialLineChart(MaterialReportParam param);

    //创意报表
    Page<CreativeSpendReportDTO> getCreativeReport(CreativeSpendReportParam parm);
    CreativeSpendReportDTO getCreativeReportSummary(CreativeSpendReportParam parm);
    List<Map> getCreativeLineChart(CreativeSpendReportParam parm);

    //小米创意层级投放报表
    Page<CreativeXiaomiReportDTO> getCreativeXiaomiReport(CreativeXiaomiParam parm);
    Page<CreativeXiaomiReportDTO> getCreativeXiaomiCreativeIdReport(CreativeXiaomiParam parm);
    int insertCreativeXiaomiCreativeIdReport(@Param("date") String date);
    List<Map<String,Object>> selectCreativeXiaomiCreativeIdList(@Param("date") String date);
    CreativeXiaomiReportDTO getCreativeXiaomiReportSummary(CreativeXiaomiParam parm);

    //订阅ROI报表
    Page<SubscribeRoiReportDTO> getSubscribeRoiReport(SubscribeRoiReportParam parm);
    SubscribeRoiReportDTO getSubscribeRoiReportSummary(SubscribeRoiReportParam parm);

    //订阅ROI报表
    Page<SubscribeReportVO> getSubscribeReport(SubscribeReportDTO parm);
    SubscribeReportVO getSubscribeReportSummary(SubscribeReportDTO parm);
    
    //人效管理 素材列表
    Page<MaterialReportDTO> getMaterialList(MaterialReportParam param);
    MaterialReportDTO getMaterialListSummary(MaterialReportParam param);
    
    /*设计师消耗分布*/
    List<Map> getArtistSpend(MaterialReportParam param);
    /*渠道消耗分布*/
    List<Map> getChannelSpend(MaterialReportParam param);
    /*标签消耗分布-制作方式*/
    List<Map> getTag1Spend(MaterialReportParam param);
    /*标签消耗分布-手段*/
    List<Map> getTag2Spend(MaterialReportParam param);
    /*标签消耗分布-内容*/
    List<Map> getTag3Spend(MaterialReportParam param);
    
    List<EffectivenessManageVo> effectiveManage(EffectivenessManageParam param);
    
    List<EffectivenessManageVo> getCumulativeOnlineNum(EffectivenessManageParam param);

    Double selectSpendSum(@Param("appid") Integer appid, @Param("dateCondition") String dateCondition);

    List<Map> getCreativeReportByWeek(MaterialCondition materialCondition);

    List<MaterialByWeekVisualization> getCreativeReportByWeek2(MaterialCondition materialCondition);

    List<Map> getCreativeReportByWeek3(MaterialCondition materialCondition);

    List<Map<String, Object>> selectSpendGroupByAccount(@Param("date") String date, @Param("media") String media);

    List<String> getCreativeXiaomigameNames(CreativeXiaomiParam param);

    List<AppleManualSpendVo> selectAppleSpendManual(SpendReportParam param);

    void insertAppleSpendManual(List<AppleManualSpendVo> list);

    List<AppleManualSpendVo> selectReyunIncome(SpendReportParam param);

    void insertReyunIncome(List<AppleManualSpendVo> list);

    void deleteAppleSpendManual(List<AppleManualSpendVo> list);

    void deleteReyunIncome(List<AppleManualSpendVo> list);

    void insertYinliConfig(Map<String,Object> insertParamMap);

    void deleteYinliConfig(@Param("appidList") List<String> appidList);

    List<CameraCostCounter> selectCamerasPayCount(@Param("date") String date, @Param("type") int osType);

    List<CameraCostCounter> selectSpecialCamerasPayCount(@Param("date") String date);

    void deleteAndroidCameraList(@Param("date") String date, @Param("tableName") String tableName, @Param("tag") String tag);

    void batchInsertAndroidCameraList(@Param("list") List<CameraCostCounter> list, @Param("tableName") String tableName, @Param("tag") String tag);

    List<String> selectXiaomiCreativeLowType();
    List<String> selectXiaomiCreativeMidType();
    List<String> selectXiaomiCreativeHighType();

    List<UmengChannelTotalVo> selectUmengChannelTotal(UmengChannelTotalBigDataParam param);
    UmengChannelTotalVo selectUmengChannelTotalTotal(UmengChannelTotalBigDataParam param);

    void insertEstimateRoiList(@Param("list") ArrayList<HashMap<String, String>> list, @Param("tableName") String tableName, @Param("tag") String tag);

    List<CampaignBudget> selectPlanIdBudget(@Param("list") List<String> list,@Param("media") String media);
    List<CampaignBudget> selectPlanIdBudgetCondition(OppoPlanIdParam param);

    void updatePlanStatus(@Param("planId") String planId, @Param("status") String status);

    void updatePlanIdBudget(BudgetUpdateRecord record);

    void insertPayUserDetail(@Param("list") List<WbPayInfoUserInfo> validUser, @Param("date") String date);
    void insertCancelUserDetail(@Param("list") List<WbPayInfoUserInfo> validUser, @Param("date") String date);
    void insertPayUserDetail2(@Param("list") List<WbPayInfoUserInfo> validUser, @Param("date") String date);

    List<String> selectPayUserDetail(@Param("appid") String appid, @Param("date") String date);

    List<Map<String,Object>> selectCameraPayGap(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                @Param("list") List<String> appList);

    List<Map<String,Object>> selectClickActuser(String tdate);

    List<Map<String,Object>> selectAdshowActuser(String tdate);

    void batchInsertCameraPayGap(@Param("list") List<CameraStatisticPo> list);
}
