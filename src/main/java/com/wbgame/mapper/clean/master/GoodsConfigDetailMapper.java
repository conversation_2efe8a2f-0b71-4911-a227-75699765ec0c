package com.wbgame.mapper.clean.master;


import com.wbgame.pojo.clean.GoodsConfigDetail;

import java.util.List;

public interface GoodsConfigDetailMapper {

    int deleteGoodsConfigDetail(List<Long> configId);

    int insertGoodsConfigDetail(List<GoodsConfigDetail> record);

    List<GoodsConfigDetail> selectGoodsConfigDetail(GoodsConfigDetail example);

    List<GoodsConfigDetail> selectGoodsConfigDetailById(Long configId);

    int updateGoodsConfigDetail(GoodsConfigDetail record);

}