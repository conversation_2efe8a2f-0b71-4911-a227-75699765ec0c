package com.wbgame.mapper.haiwaiad;

import com.wbgame.pojo.param.PayInfoDetailParam;
import com.wbgame.pojo.view.PayInfoDetailVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/8/28 12:30
 */
public interface HwWbPayInfoMapper {


    // 新版支付信息查询--海外
    List<Map<String, Object>> selectNewPayInfo(Map<String, Object> param);

    // 新版支付信息统计汇总--海外
    Map<String, Object> countNewPayInfo(Map<String, Object> param);

    //实时支付信息查询--海外
    List<PayInfoDetailVo> selectPayInfoDetailOutside(PayInfoDetailParam param);
    //实时支付信息查询汇总--海外
    PayInfoDetailVo countPayInfoDetailOutside(PayInfoDetailParam param);
}
