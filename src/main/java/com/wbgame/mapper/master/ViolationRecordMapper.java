package com.wbgame.mapper.master;

import com.wbgame.pojo.XiaomiViolationRecord;
import com.wbgame.pojo.XiaomiViolationRecordDTO;
import com.wbgame.pojo.XiaomiViolationRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 小米违规记录mapper
 * @Date 2024/11/19 15:06
 */
public interface ViolationRecordMapper {


    int batchInsert(@Param("storeList") List<XiaomiViolationRecord> storeList);
    int batchDuplicateUpdate(@Param("storeList") List<XiaomiViolationRecord> storeList);

    List<XiaomiViolationRecord> queryList(XiaomiViolationRecordDTO dto);

    List<XiaomiViolationRecordVo> queryGroupList(XiaomiViolationRecordDTO dto);

    List<String> queryStatus();

    XiaomiViolationRecordVo queryGroupTotal(XiaomiViolationRecordDTO dto);

    List<XiaomiViolationRecordVo> queryViolationCounts(XiaomiViolationRecordDTO dto);

    /**
     * 根据传入的时间查询符合在这个时间的已封禁的广告位类型数据
     * @param tdate 时间
     * @return 符合条件的数据
     */
    List<XiaomiViolationRecordVo> queryAdsenseTypes(@Param("tdate") String tdate);

}
