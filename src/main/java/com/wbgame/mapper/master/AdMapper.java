package com.wbgame.mapper.master;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.Page;
import com.wbgame.pojo.*;
import com.wbgame.pojo.adv2.*;
import com.wbgame.pojo.advert.CashUnmatchParam;
import com.wbgame.pojo.advert.CashUnmatchVo;
import com.wbgame.pojo.advert.DnwxReyunConfigVo;
import com.wbgame.pojo.advert.UmengAdIncomeReportVo;
import com.wbgame.pojo.jettison.report.dto.OperationReportDTO;
import com.wbgame.service.impl.platform.xiaomi.XiaomiErrorDetail;
import com.wbgame.service.impl.platform.xiaomi.XiaomiErrorDetailParam;
import com.wbgame.task.cameras.CameraCostCounter;
import com.wbgame.task.cameras.WbPayInfoUserInfo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.wbgame.pojo.custom.AdMsgStatsTotalVo;
import com.wbgame.pojo.custom.AdMsgTotalVo;
import com.wbgame.pojo.custom.AdTotalHourTwoVo;
import com.wbgame.pojo.custom.AdvFeeVo;
import com.wbgame.pojo.custom.AdvertInferHourVo;
import com.wbgame.pojo.custom.AppchannelAppidVo;
import com.wbgame.pojo.custom.BoxPayVo;
import com.wbgame.pojo.custom.GDTWorkMediumVo;
import com.wbgame.pojo.custom.GDTWorkReportVo;
import com.wbgame.pojo.custom.JdAppPayVo;
import com.wbgame.pojo.push.ApkProductNumVo;
import com.wbgame.pojo.push.ApkProductStatsVo;
import com.wbgame.pojo.push.ApkProductUserTotalVo;
import com.wbgame.pojo.wx.WxAppInfo;

public interface AdMapper {
	
	// 头条数据
	int insertTouTiaoAdTotal(List<TouTiaoAdVo> list);
	
	int insertApkProductStatsList(List<ApkProductStatsVo> list);
	int updateApkProductUserTotal(List<ApkProductUserTotalVo> list);
	
	// 获取活跃数
	@Select("SELECT '${tdate}' as tdate,by_priid as pid,by_newcount as act_num FROM alone_dau_total where by_date = #{tdate}")
 	List<AdMsgTotalVo> selectAloneAduAct(@Param("tdate") String tdate);
	
	/** 查询项目ID对应的产品版本渠道标识 */
   	@MapKey("projectid")
 	Map<String, Map<String, Object>> selectProjectidChannelMap();
   	/** 获取活跃数以及匹配的日期和项目渠道等 */
	@MapKey("mapkey")
	Map<String, Map<String, Object>> selectDauAndProjectMap(Map<String, Object> paramMap);
	
	/**
  	 * 获取广告源对应平台明细数据 ecpm、收入 
  	 * @param date 日期
  	 * @return
  	 */
	@MapKey("mapkey")
	@Select(" ${sql} ")
  	public Map<String, DnAdsidCashTotalVo> getAdsidCashTotal(@Param("sql")String sql);
	/**
  	 * 获取广告源管理对应sdk_code、open_type 
  	 * @return
  	 */
	@MapKey("mapkey")
	@Select(" ${sql} ")
	public Map<String, ExtendAdsidVo> getAdsidManageMap(@Param("sql")String sql);
	
	/**
  	 * 获取子渠道对应cha_type_name、cha_media 
  	 * @return
  	 */
	@MapKey("mapkey")
	@Select(" ${sql} ")
	public Map<String, DnChaRevenueTotal> getChaidManageMap(@Param("sql")String sql);
	
	
	/** 按应用+子渠道 查询计费收入 */
	public List<Map<String, Object>> selectDnBillRevenue(Map<String, String> paramMap);
	/** 按应用 查询分成后计费收入 */
	public List<Map<String, Object>> selectDnBillRevenueAppRatio(Map<String, String> paramMap);
	/** 按应用、子渠道 查询分成后计费收入 */
	public List<Map<String, Object>> selectDnBillRevenueAppChannelRatio(Map<String, String> paramMap);
	
	
	// 广告数据信息查询
	@Select(" ${sql} ")
	public List<AdvFeeVo> browsAdv(@Param("sql")String sql);
	@Update("update adv_channel_fee set give_fee=#{give_fee},get_fee=#{get_fee},adv_fee=#{adv_fee},dau=#{dau} where adv_dt=#{adv_dt} and push_cha=#{push_cha} and pri_id=#{pri_id} and cha_type=#{cha_type}")
	public int updateAdvFee(AdvFeeVo afv);
	
	@Select("select CAST(id as char(20)) as id, app_name, app_category, bus_category,two_app_category,umeng_key,os_type from app_info ${where}")
  	public List<Map<String, Object>> selectAppInfoList(@Param("where") String where);

	// 增加app_category等条件查询，有值时加入where
	@Select({
		"<script>",
			"select * from app_info where 1=1",
			"<if test='app_category != null and app_category != \"\"'>",
			"  AND app_category in (${app_category})",
			"</if>",
			"<if test='bus_category != null and bus_category != \"\"'>",
			"  AND bus_category in (${bus_category})",
			"</if>",
		"</script>"
	})
  	public List<Map<String, Object>> selectAppInfoListTwo(Map<String, String> paramMap);

	@Select(" ${sql} ")
	public List<JdAppPayVo> browsAdTotal(@Param("sql")String sql);
	
	// 通用执行语句和查询语句
	@Update(" ${sql} ")
	public int execSql(@Param("sql")String sql); // 直接执行DML sql语句
	@Select(" ${sql} ")
	public List<String> queryListString(@Param("sql")String sql);
	@Select(" ${sql} ")
	public List<Map<String, Object>> queryListMap(@Param("sql")String sql);
	@Select(" ${sql} ")
	public List<Map<String, String>> queryListMapOne(@Param("sql")String sql);
	@MapKey("mapkey")
	@Select(" ${sql} ")
	public Map<String, Map<String, Object>> queryListMapOfKey(@Param("sql")String sql);
	@Update(" ${sql} ")
	public int execSqlHandle(@Param("sql")String sql, @Param("obj")Object obj);
	@Select(" ${sql} ")
	public List<NpPostVo> queryNpPost(@Param("sql")String sql);
	@Select(" ${sql} ")
	public List<Map<String, Object>> queryListMapTwo(@Param("sql")String sql, @Param("obj")Object obj);
	public int batchExecSql(Map<String, Object> paramMap);
	public int batchExecSqlTwo(Map<String, Object> paramMap);
	
	
	// 渠道产品广告数据
	public int insertUmengAdIncomeList(List<UmengAdIncomeVo> list);
	public List<UmengAdcodeVo> selectUmengAdcode(Map<String, String> paramMap);
	@Insert("insert into umeng_adcode_list(ad_code,appkey,adtype,note,channel,status) values(#{ad_code},#{appkey},#{adtype},#{note},#{channel},#{status})")
	public int insertUmengAdcode(UmengAdcodeVo np);
	@Update("update umeng_adcode_list set appkey=#{appkey},adtype=#{adtype},note=#{note},status=#{status},channel=#{channel} where ad_code=#{ad_code}")
	public int updateUmengAdcode(UmengAdcodeVo np);
	@Delete("delete from umeng_adcode_list where ad_code=#{ad_code} and channel=#{channel}")
	public int deleteUmengAdcode(UmengAdcodeVo np);
	public List<UmengAdcodeVo> selectUmengAdcodeByAdcode(Map<String, String> paramMap);
	@Update("update umeng_adcode_list set appkey=#{appkey},channel=#{channel}, status= #{status} where ad_code in (${ad_code})")
	int batchUpdateUmengAdcode(UmengAdcodeVo np);
	
	// 渠道广告收入管理
	@Select(" ${sql} ")
	public List<AdvChaFeeVo> selectAdvChaFee(@Param("sql")String sql);
	@Insert("insert into adv_channel_fee_client(cha_name,cha_game,ad_pos,adv_fee,show_count,click_count,adv_dt) values(#{cha_name},#{cha_game},#{ad_pos},#{adv_fee},#{show_count},#{click_count},#{adv_dt})")
	public int insertAdvChaFee(AdvChaFeeVo np);
	@Update("update adv_channel_fee_client set adv_fee=#{adv_fee} where cha_name=#{cha_name} and cha_game=#{cha_game} and ad_pos=#{ad_pos} and adv_dt=#{adv_dt}")
	public int updateAdvChaFee(AdvChaFeeVo np);
	@Delete("delete from adv_channel_fee_client where cha_name=#{cha_name} and cha_game=#{cha_game} and ad_pos=#{ad_pos} and adv_dt=#{adv_dt}")
	public int deleteAdvChaFee(AdvChaFeeVo np);
	
	// 广告产品互推配置
	@Select(" ${sql} ")
	public List<ActConfigVo> selectActConfig(@Param("sql")String sql);
	@Insert("insert into alone_act_config(appid,cha_id,pid,icon,image,linkUrl,autoDay,gamelist,status,level,open,param,plist,citys,showTimes,pushtype,adpos,pushclass,spinetxt,out_gamelist,note,title) values(#{appid},#{cha_id},#{pid},#{icon},#{image},#{linkUrl},#{autoDay},#{gamelist},#{status},#{level},#{open},#{param},#{plist},#{citys},#{showTimes},#{pushtype},#{adpos},#{pushclass},#{spinetxt},#{out_gamelist},#{note},#{title})")
	public int insertActConfig(ActConfigVo act);
	@Update("update alone_act_config set appid=#{appid},cha_id=#{cha_id},pid=#{pid},icon=#{icon},image=#{image},linkUrl=#{linkUrl},autoDay=#{autoDay},gamelist=#{gamelist},status=#{status},level=#{level},open=#{open},param=#{param},plist=#{plist},citys=#{citys},showTimes=#{showTimes},pushtype=#{pushtype},adpos=#{adpos},pushclass=#{pushclass},spinetxt=#{spinetxt},out_gamelist=#{out_gamelist},note=#{note},title=#{title} where aid=#{aid}")
	public int updateActConfig(ActConfigVo act);
	
	// 投放新增查询，活跃查询
	@Select(" ${sql} ")
	public List<BuYuNewCountVo> queryBuYuNew(@Param("sql")String sql);
	
	// 落地页内容管理
	public int insertLandingPageContent(List<LandingPageContentVo> list);
	public int updateLandingPageContent(LandingPageContentVo lpc);
	public int deleteLandingPageContent(LandingPageContentVo lpc);
	
	// 实时新增监控报表、活跃用户监控报表、广告请求实时图表
	@Select(" ${sql} ")
	public List<AdTotalHourTwoVo> queryHourReport(@Param("sql")String sql);
	
	// 广点通数据信息
	@Select(" ${sql} ")
	public List<GDTWorkReportVo> selectGDTAdwordReport(@Param("sql")String sql);
	public int insertGDTAdwordReport(List<GDTWorkReportVo> list);
	
	public int insertGDTMedium(GDTWorkMediumVo np);
	public int updateGDTMedium(GDTWorkMediumVo np);
	public int insertGDTPlacement(GDTWorkMediumVo np);
	public int updateGDTPlacement(GDTWorkMediumVo np);
	
	
	// 黑名单设备
	public int insertPostBlackList(List<Map<String, Object>> list);
	
	// 流失用户、沉默用户
	public int insertApkProductOutflow(List<ApkProductNumVo> list);
	public int insertApkProductSilent(List<ApkProductNumVo> list);
	
	// 微信红包参数配置
	public int insertHongBaoConfig(HongBaoInfoVo hb);
	public int updateHongBaoConfig(HongBaoInfoVo hb);
		
	public int updateUmengPushCha(List<UmengChannelTotalVo> keepList);
	// google活跃用户
	public int updateGoogleFeishuAct(List<Map<String, Object>> list);
	public int insertGoogleFeishuTotal(List<Map<String, Object>> list);
	// maoyou活跃用户
	public int updateMaoyouAct(List<String> list);
	public int insertMaoyouTotal(List<Map<String, Object>> list);
	public int updateMaoyouTotalKeepBatch(List<Map<String, Object>> list);
	
	// 广告数据信息统计报表
	public List<AdvFeeVo> selectAdvChannelFeeTotal(Map<String, Object> paramMap);
	public List<AdvFeeVo> selectAdvChannelFeeTotalTwo(Map<String, Object> paramMap);
	public AdvFeeVo selectAdvChannelFeeTotalThree(Map<String, Object> paramMap);
	public int insertAdvChannelFeeTotalNewList(List<AdvFeeVo> list);
	public List<AdvFeeVo> selectAdvChannelFeeTotalNew(Map<String, Object> paramMap);
	public List<AdvFeeVo> selectAdvChannelFeeTotalHaiwai(Map<String, Object> paramMap);
	
	// apk产品新增、活跃数据
	@MapKey("appid")
	@Select("select appid,currNew,yesterdayNew,currDau,yesterdayDau from apk_user_total where createtime = #{tdate}")
	Map<String, Map<String, Object>> selectApkUserNewDauMap(@Param("tdate") String tdate);
	@Select("select '${tdate}' as tdate,appid,currNew as add_num,currDau as act_num from apk_user_total where createtime = #{tdate}")
	List<AdMsgStatsTotalVo> selectApkUserNewDauList(@Param("tdate") String tdate);
		
	@Select(" ${sql} ")
	public List<BoxPayVo> selectBoxGamepayMonthList(@Param("sql")String sql);
	public int insertBoxGamepayMonthList(Map<String, Object> paramMap);
	
	// 按产品查询活跃用户机型数据入库
	public int insertProductModelList(List<NpPostVo> list);
	
	// 余额提现版本提现留存数据适配
	public int insertRedPackAuditList(Map<String, Object> paramMap);

	@MapKey("id")
	@Select("SELECT  id AS id,id AS appid,app_name AS gname from app_info where channel_id = 51808 ")
	Map<String, Map<String, Object>> selectAppinfoMap();
	
	
	// 抽取各渠道新增活跃、PV收入数据存储
	int updateAppChannelForKeepList(List<AppchannelAppidVo> list);
	int updateAppChannelForIncomeList(List<AppchannelAppidVo> list);
	
	// 合作方产品收支查询
	public List<Map<String,Object>> selectPartnerApp(Map<String, Object> paramMap);
	public Map<String,Object> selectPartnerAppSum(Map<String, Object> paramMap);
	
	// 合作方产品收支查询.haiwai2
	public List<Map<String,Object>> selectPartnerAppHaiwai2(Map<String, Object> paramMap);
	public Map<String,Object> selectPartnerAppSumHaiwai2(Map<String, Object> paramMap);
	
	// cps合作方报表查看
	public List<Map<String,Object>> selectCpsPartnerReport(Map<String, Object> paramMap);
	public Map<String,Object> selectCpsPartnerReportSum(Map<String, Object> paramMap);
	
	// 合作方投放明细查询
	public List<Map<String,Object>> selectPartnerInvestInfo(Map<String, String> paramMap);
	public Map<String,Object> selectPartnerInvestInfoSum(Map<String, String> paramMap);
	
	// 合作方变现明细查询
	public List<Map<String,Object>> selectPartnerRevenueInfo(Map<String, String> paramMap);
	public Map<String,Object> selectPartnerRevenueInfoSum(Map<String, String> paramMap);
	
	// 国内活跃新增汇总查询
	public int insertDnChaDauTotal(@Param("tdate") String tdate);
	public int insertDnChaDauTotalTwo(@Param("tdate") String tdate);
	public List<Map<String,Object>> selectDnChaDauTotal(Map<String, Object> paramMap);
	public Map<String,Object> selectDnChaDauTotalSum(Map<String, Object> paramMap);
	
	// 新版广告数据报表内容注入
	int insertAdvChannelFeeNew(Map<String, Object> paramMap);
	
	/** 热云参数配置V3 新增 */
	@Insert("insert into dnwx_reyun_config_three(appid,cha_id,pid,buy_id,buy_act,`event`,eventType,times,`loop`,rate,adType,ecpmValue,ecpmType,action,timeType,checkTimes,levelType,`level`,statu,sdk_type,logic,`out`,action_params,rule_rate,cuser,createtime,euser,endtime) value(#{appid},#{cha_id},#{pid},#{buy_id},#{buy_act},#{event},#{eventType},#{times},#{loop},#{rate},#{adType},#{ecpmValue},#{ecpmType},#{action},#{timeType},#{checkTimes},#{levelType},#{level},#{statu},#{sdk_type},#{logic},#{out},#{action_params},#{rule_rate},#{cuser},now(),#{euser},now()) ")
	@Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
	public int insertDnwxReyunConfigThree(DnwxReyunConfigVo record);

	@Insert("insert into dnwx_reyun_config_three_record(record_id,id,appid,cha_id,pid,buy_id,buy_act,`event`,eventType,times,`loop`,rate,adType,ecpmValue,ecpmType,action,timeType,checkTimes,levelType,`level`,statu,sdk_type,logic,`out`,action_params,rule_rate,cuser,createtime,euser,endtime) value(#{record_id},#{id},#{appid},#{cha_id},#{pid},#{buy_id},#{buy_act},#{event},#{eventType},#{times},#{loop},#{rate},#{adType},#{ecpmValue},#{ecpmType},#{action},#{timeType},#{checkTimes},#{levelType},#{level},#{statu},#{sdk_type},#{logic},#{out},#{action_params},#{rule_rate},#{cuser},now(),#{euser},now()) ")
	public int insertDnwxReyunConfigThreeRecord(DnwxReyunConfigVo record);

	/** 应用收支汇总表查询 */
	public List<Map<String,Object>> selectDnAppRevenueTotal(Map<String, Object> paramMap);
	/** 应用收支汇总表查询汇总 */
	public Map<String,Object> selectDnAppRevenueTotalSum(Map<String, Object> paramMap);
	public int insertDnAppRevenueTotal(@Param("tdate")String tdate);
	public int insertDnAppRevenueTotalTwo(@Param("tdate")String tdate);
	
	
	/** 应用分渠道收支汇总表查询 */
	public List<Map<String,Object>> selectDnAppChannelRevenueTotal(Map<String, Object> paramMap);
	/** 应用分渠道收支汇总表查询汇总 */
	public Map<String,Object> selectDnAppChannelRevenueTotalSum(Map<String, Object> paramMap);
	public int insertDnAppChannelRevenueTotal(@Param("tdate")String tdate);
	public int insertDnAppChannelRevenueTotalTwo(@Param("tdate")String tdate);
	
	/** 同步变现-自推广收入分析 */
	public int insertSelfPromotionIncome(@Param("tdate")String tdate);
	/** 同步变现-汇总数据校准 */
	public int insertExtendAdtypeRevise(@Param("tdate")String tdate);
	/** 同步变现-项目ID收入预估 */
	public int insertExtendPrjidIncome(@Param("tdate")String tdate);
	/** 同步变现-二级子渠道展示收入 */
	public int insertExtendSubchaTotal(@Param("tdate")String tdate);
	public List<Map<String, Object>> selectSubchaTotalOfBanner(@Param("tdate")String tdate);
	
	/** 同步应用分渠道收支数据汇总 */
	public int insertDnAppChaRevenue(@Param("tdate")String tdate);
	public int insertDnAppChaRevenueTwo(@Param("tdate")String tdate);
	/** 同步数据gap汇总分析 */
	public int insertAgentShowGap(@Param("tdate")String tdate);
	
	/** 更新新增用户的多日PV */
	public int updateAdduserForPv(List<Map<String, Object>> list);
	public int updateAdduserForLtv(List<Map<String, Object>> list);
	public int updateAdduserForArPv(List<Map<String, Object>> list);
	
	//广告-cps合作方报表  审核
	int updateCpsReportStatus(Map<String, Object> paramMap);
	//广告-cps合作方报表 根据主键字段修改活跃、广告收入.  
	int updateCpsReportDauIncome (Map<String, Object> paramMap);

	int batchDelUmengAdcode(Map<String, Object> contentList);
	
	public int insertExtendRevenueReport(@Param("tdate")String tdate);

	//获取小游戏助手的游戏
	List<WxAppInfo> getUseAppInfo();

	@Select(" ${sql} ")
	List<AdvertInferHourVo> queryAdvertHourReport(@Param("sql")String sql);

	List<Map<String,Object>> selectUmengAddAct(Map<String, Object> paramMap);

    void batchInsertUmengAdcode(List<UmengAdcodeVo> list);
    void batchInsertUmengAdcode2(List<UmengAdcodeVo> list); 
	public int  delUmengAdcode();
    @Select(" select * from adv_csj_template ")
    List<CSJXxlTemplateVo> getCSJXxlTemplateList();

	String queryChaByPid(String pid);

	// oppo渠道产品广告数据
	public int insertOppoAdIncomeList(List<UmengAdIncomeVo> list);
	

	// 新版支付信息查询
	List<Map<String, Object>> selectNewPayInfo(Map<String, Object> param);

	Map<String, Object> countNewPayInfo(Map<String, Object> param);

	// 获取处理特殊应用
	@Select("select appid from dn_revenue_special_appid")
	List<String> getDnAppPayRevenueSpecicalAppids();

	// 应用收支汇总表 处理特殊应用只有付费收入但是无广告收入
	int insertDnAppPayRevenueSpecical(Map<String,Object> map);

	// 应用分渠道收支数据 处理特殊应用只有付费收入但是无广告收入
	int insertDnChaAppPayRevenueSpecical(Map<String,Object> map);
	
	
	/** 付费应用收支汇总表查询 */
	public List<Map<String,Object>> selectDnPaysAppRevenueTotal(Map<String, Object> paramMap);
	/** 付费应用收支汇总表查询汇总 */
	public Map<String,Object> selectDnPaysAppRevenueTotalSum(Map<String, Object> paramMap);

	Page<OppoReportVO> getOppoReport(OppoReprtDTO param);
	OppoReportVO getOppoReportSummary(OppoReprtDTO param);


    List<CashUnmatchVo> selectUnmatchAds(CashUnmatchParam param);

	int insertUmengAdSummary(@Param("fList") List<UmengAdIncomeReportVo> fList);

	void insertReportOperationDaily(@Param("dataList") List<OperationReportDTO> dataList);

	void replaceIntoUserChannelTotal(@Param("channels") List<UmengUserChannelTotal> channels);

	/**
	 * 单次付费查询
	 * @param start
	 * @param end
	 * @return
	 */
	List<WbPayInfoUserInfo> selectCamerasPayCount(@Param("start") String start, @Param("end") String end);
	/**
	 * 订阅付费查询
	 * @param start
	 * @param end
	 * @return
	 */
	List<WbPayInfoUserInfo> selectCamerasSubscribeCount(@Param("start") String start, @Param("end") String end);

	/**
	 * ios订阅付费查询
	 * @param start
	 * @param end
	 * @return
	 */
	List<WbPayInfoUserInfo> selectIOSCamerasPayCount(@Param("start") String start, @Param("end") String end, @Param("type") int type);

	List<CameraCostCounter> selectAndroidCamerasSignCount(@Param("start") String start, @Param("end") String end);

	List<CameraCostCounter> selectIOSCamerasSubscribeCount(@Param("start") String start, @Param("end") String end, @Param("type") int type);

	List<WbPayInfoUserInfo> selectCameraCancelCount(@Param("tdate") String tdate);

	void deleteXiaomiErrorDetail(@Param("tdate") String tdate, @Param("account") String account);

	void insertXiaomiErrorDetail(@Param("list") List<XiaomiErrorDetail> list);

	List<XiaomiErrorDetail> selectXiaomiErrorDetail(XiaomiErrorDetailParam param);

	List<String> selectXiaomiErrorCodes();
}