package com.wbgame.pojo.operate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "月度ROI")
public class ROIMonthlyVo {
    @ApiModelProperty(value = "年月",dataType = "String")
	private String month;
    @ApiModelProperty(value = "appid",dataType = "String")
	private String app_id;
    @ApiModelProperty(value = "应用名称",dataType = "String")
	private String appName;
    @ApiModelProperty(value = "计算方式",dataType = "Integer")
	private Integer calculation_method;
    @ApiModelProperty(value = "月返点消耗",dataType = "String")
	private String rebatespend;
    @ApiModelProperty(value = "cpa",dataType = "String")
	private String cpa;
    @ApiModelProperty(value = "月新增",dataType = "Integer")
	private Integer reg_users;
    @ApiModelProperty(value = "月活跃",dataType = "Integer")
	private Integer active_users;
    @ApiModelProperty(value = "本月累计收入(广告+计费)",dataType = "String")
	private String add_revenue_1;
    @ApiModelProperty(value = "2月累计收入(广告+计费)",dataType = "String")
	private String add_revenue_2;
    @ApiModelProperty(value = "3月累计收入(广告+计费)",dataType = "String")
	private String add_revenue_3;
    @ApiModelProperty(value = "4月累计收入(广告+计费)",dataType = "String")
	private String add_revenue_4;
    @ApiModelProperty(value = "5月累计收入(广告+计费)",dataType = "String")
	private String add_revenue_5;
    @ApiModelProperty(value = "6月累计收入(广告+计费)",dataType = "String")
	private String add_revenue_6;
    @ApiModelProperty(value = "7月累计收入(广告+计费)",dataType = "String")
	private String add_revenue_7;
    @ApiModelProperty(value = "8月累计收入(广告+计费)",dataType = "String")
	private String add_revenue_8;
    @ApiModelProperty(value = "9月累计收入(广告+计费)",dataType = "String")
	private String add_revenue_9;
    @ApiModelProperty(value = "10月累计收入(广告+计费)",dataType = "String")
	private String add_revenue_10;
    @ApiModelProperty(value = "11月累计收入(广告+计费)",dataType = "String")
	private String add_revenue_11;
    @ApiModelProperty(value = "12月累计收入(广告+计费)",dataType = "String")
	private String add_revenue_12;
    @ApiModelProperty(value = "本月累计付费人数",dataType = "String")
	private Integer cumulative_users_1;
    @ApiModelProperty(value = "2月累计付费人数",dataType = "String")
	private Integer cumulative_users_2;
    @ApiModelProperty(value = "3月累计付费人数",dataType = "String")
	private Integer cumulative_users_3;
    @ApiModelProperty(value = "4月累计付费人数",dataType = "String")
	private Integer cumulative_users_4;
    @ApiModelProperty(value = "5月累计付费人数",dataType = "String")
	private Integer cumulative_users_5;
    @ApiModelProperty(value = "6月累计付费人数",dataType = "String")
	private Integer cumulative_users_6;
    @ApiModelProperty(value = "7月累计付费人数",dataType = "String")
	private Integer cumulative_users_7;
    @ApiModelProperty(value = "8月累计付费人数",dataType = "String")
	private Integer cumulative_users_8;
    @ApiModelProperty(value = "9月累计付费人数",dataType = "String")
	private Integer cumulative_users_9;
    @ApiModelProperty(value = "10月累计付费人数",dataType = "String")
	private Integer cumulative_users_10;
    @ApiModelProperty(value = "11月累计付费人数",dataType = "String")
	private Integer cumulative_users_11;
    @ApiModelProperty(value = "12月累计付费人数",dataType = "String")
	private Integer cumulative_users_12;
    @ApiModelProperty(value = "本月付费成本",dataType = "String")
	private String cost_payment_1;
    @ApiModelProperty(value = "2月累计付费成本",dataType = "String")
	private String cost_payment_2;
    @ApiModelProperty(value = "3月累计付费成本",dataType = "String")
	private String cost_payment_3;
    @ApiModelProperty(value = "4月累计付费成本",dataType = "String")
	private String cost_payment_4;
    @ApiModelProperty(value = "5月累计付费成本",dataType = "String")
	private String cost_payment_5;
    @ApiModelProperty(value = "6月累计付费成本",dataType = "String")
	private String cost_payment_6;
    @ApiModelProperty(value = "7月累计付费成本",dataType = "String")
	private String cost_payment_7;
	@ApiModelProperty(value = "8月累计付费成本",dataType = "String")
	private String cost_payment_8;
	@ApiModelProperty(value = "9月累计付费成本",dataType = "String")
	private String cost_payment_9;
	@ApiModelProperty(value = "10月累计付费成本",dataType = "String")
	private String cost_payment_10;
	@ApiModelProperty(value = "11月累计付费成本",dataType = "String")
	private String cost_payment_11;
	@ApiModelProperty(value = "12月累计付费成本",dataType = "String")
	private String cost_payment_12;
	@ApiModelProperty(value = "roi_1",dataType = "String")
	private String roi_1;
	@ApiModelProperty(value = "roi_2",dataType = "String")
	private String roi_2;
	@ApiModelProperty(value = "roi_3",dataType = "String")
	private String roi_3;
	@ApiModelProperty(value = "roi_4",dataType = "String")
	private String roi_4;
	@ApiModelProperty(value = "roi_5",dataType = "String")
	private String roi_5;
	@ApiModelProperty(value = "roi_6",dataType = "String")
	private String roi_6;
	@ApiModelProperty(value = "roi_7",dataType = "String")
	private String roi_7;
	@ApiModelProperty(value = "roi_8",dataType = "String")
	private String roi_8;
	@ApiModelProperty(value = "roi_9",dataType = "String")
	private String roi_9;
	@ApiModelProperty(value = "roi_10",dataType = "String")
	private String roi_10;
	@ApiModelProperty(value = "roi_11",dataType = "String")
	private String roi_11;
	@ApiModelProperty(value = "roi_12",dataType = "String")
	private String roi_12;
	public String getMonth() {
		return month;
	}
	public void setMonth(String month) {
		this.month = month;
	}
	public String getApp_id() {
		return app_id;
	}
	public void setApp_id(String app_id) {
		this.app_id = app_id;
	}
	public Integer getCalculation_method() {
		return calculation_method;
	}
	public void setCalculation_method(Integer calculation_method) {
		this.calculation_method = calculation_method;
	}
	public String getRebatespend() {
		return rebatespend;
	}
	public void setRebatespend(String rebatespend) {
		this.rebatespend = rebatespend;
	}
	public Integer getReg_users() {
		return reg_users;
	}
	public void setReg_users(Integer reg_users) {
		this.reg_users = reg_users;
	}
	public Integer getActive_users() {
		return active_users;
	}
	public void setActive_users(Integer active_users) {
		this.active_users = active_users;
	}
	public String getAdd_revenue_1() {
		return add_revenue_1;
	}
	public void setAdd_revenue_1(String add_revenue_1) {
		this.add_revenue_1 = add_revenue_1;
	}
	public String getAdd_revenue_2() {
		return add_revenue_2;
	}
	public void setAdd_revenue_2(String add_revenue_2) {
		this.add_revenue_2 = add_revenue_2;
	}
	public String getAdd_revenue_3() {
		return add_revenue_3;
	}
	public void setAdd_revenue_3(String add_revenue_3) {
		this.add_revenue_3 = add_revenue_3;
	}
	public String getAdd_revenue_4() {
		return add_revenue_4;
	}
	public void setAdd_revenue_4(String add_revenue_4) {
		this.add_revenue_4 = add_revenue_4;
	}
	public String getAdd_revenue_5() {
		return add_revenue_5;
	}
	public void setAdd_revenue_5(String add_revenue_5) {
		this.add_revenue_5 = add_revenue_5;
	}
	public String getAdd_revenue_6() {
		return add_revenue_6;
	}
	public void setAdd_revenue_6(String add_revenue_6) {
		this.add_revenue_6 = add_revenue_6;
	}
	public String getAdd_revenue_7() {
		return add_revenue_7;
	}
	public void setAdd_revenue_7(String add_revenue_7) {
		this.add_revenue_7 = add_revenue_7;
	}
	public String getAdd_revenue_8() {
		return add_revenue_8;
	}
	public void setAdd_revenue_8(String add_revenue_8) {
		this.add_revenue_8 = add_revenue_8;
	}
	public String getAdd_revenue_9() {
		return add_revenue_9;
	}
	public void setAdd_revenue_9(String add_revenue_9) {
		this.add_revenue_9 = add_revenue_9;
	}
	public String getAdd_revenue_10() {
		return add_revenue_10;
	}
	public void setAdd_revenue_10(String add_revenue_10) {
		this.add_revenue_10 = add_revenue_10;
	}
	public String getAdd_revenue_11() {
		return add_revenue_11;
	}
	public void setAdd_revenue_11(String add_revenue_11) {
		this.add_revenue_11 = add_revenue_11;
	}
	public String getAdd_revenue_12() {
		return add_revenue_12;
	}
	public void setAdd_revenue_12(String add_revenue_12) {
		this.add_revenue_12 = add_revenue_12;
	}
	public Integer getCumulative_users_1() {
		return cumulative_users_1;
	}
	public void setCumulative_users_1(Integer cumulative_users_1) {
		this.cumulative_users_1 = cumulative_users_1;
	}
	public Integer getCumulative_users_2() {
		return cumulative_users_2;
	}
	public void setCumulative_users_2(Integer cumulative_users_2) {
		this.cumulative_users_2 = cumulative_users_2;
	}
	public Integer getCumulative_users_3() {
		return cumulative_users_3;
	}
	public void setCumulative_users_3(Integer cumulative_users_3) {
		this.cumulative_users_3 = cumulative_users_3;
	}
	public Integer getCumulative_users_4() {
		return cumulative_users_4;
	}
	public void setCumulative_users_4(Integer cumulative_users_4) {
		this.cumulative_users_4 = cumulative_users_4;
	}
	public Integer getCumulative_users_5() {
		return cumulative_users_5;
	}
	public void setCumulative_users_5(Integer cumulative_users_5) {
		this.cumulative_users_5 = cumulative_users_5;
	}
	public Integer getCumulative_users_6() {
		return cumulative_users_6;
	}
	public void setCumulative_users_6(Integer cumulative_users_6) {
		this.cumulative_users_6 = cumulative_users_6;
	}
	public Integer getCumulative_users_7() {
		return cumulative_users_7;
	}
	public void setCumulative_users_7(Integer cumulative_users_7) {
		this.cumulative_users_7 = cumulative_users_7;
	}
	public Integer getCumulative_users_8() {
		return cumulative_users_8;
	}
	public void setCumulative_users_8(Integer cumulative_users_8) {
		this.cumulative_users_8 = cumulative_users_8;
	}
	public Integer getCumulative_users_9() {
		return cumulative_users_9;
	}
	public void setCumulative_users_9(Integer cumulative_users_9) {
		this.cumulative_users_9 = cumulative_users_9;
	}
	public Integer getCumulative_users_10() {
		return cumulative_users_10;
	}
	public void setCumulative_users_10(Integer cumulative_users_10) {
		this.cumulative_users_10 = cumulative_users_10;
	}
	public Integer getCumulative_users_11() {
		return cumulative_users_11;
	}
	public void setCumulative_users_11(Integer cumulative_users_11) {
		this.cumulative_users_11 = cumulative_users_11;
	}
	public Integer getCumulative_users_12() {
		return cumulative_users_12;
	}
	public void setCumulative_users_12(Integer cumulative_users_12) {
		this.cumulative_users_12 = cumulative_users_12;
	}
	public String getCost_payment_1() {
		return cost_payment_1;
	}
	public void setCost_payment_1(String cost_payment_1) {
		this.cost_payment_1 = cost_payment_1;
	}
	public String getCost_payment_2() {
		return cost_payment_2;
	}
	public void setCost_payment_2(String cost_payment_2) {
		this.cost_payment_2 = cost_payment_2;
	}
	public String getCost_payment_3() {
		return cost_payment_3;
	}
	public void setCost_payment_3(String cost_payment_3) {
		this.cost_payment_3 = cost_payment_3;
	}
	public String getCost_payment_4() {
		return cost_payment_4;
	}
	public void setCost_payment_4(String cost_payment_4) {
		this.cost_payment_4 = cost_payment_4;
	}
	public String getCost_payment_5() {
		return cost_payment_5;
	}
	public void setCost_payment_5(String cost_payment_5) {
		this.cost_payment_5 = cost_payment_5;
	}
	public String getCost_payment_6() {
		return cost_payment_6;
	}
	public void setCost_payment_6(String cost_payment_6) {
		this.cost_payment_6 = cost_payment_6;
	}
	public String getCost_payment_7() {
		return cost_payment_7;
	}
	public void setCost_payment_7(String cost_payment_7) {
		this.cost_payment_7 = cost_payment_7;
	}
	public String getCost_payment_8() {
		return cost_payment_8;
	}
	public void setCost_payment_8(String cost_payment_8) {
		this.cost_payment_8 = cost_payment_8;
	}
	public String getCost_payment_9() {
		return cost_payment_9;
	}
	public void setCost_payment_9(String cost_payment_9) {
		this.cost_payment_9 = cost_payment_9;
	}
	public String getCost_payment_10() {
		return cost_payment_10;
	}
	public void setCost_payment_10(String cost_payment_10) {
		this.cost_payment_10 = cost_payment_10;
	}
	public String getCost_payment_11() {
		return cost_payment_11;
	}
	public void setCost_payment_11(String cost_payment_11) {
		this.cost_payment_11 = cost_payment_11;
	}
	public String getCost_payment_12() {
		return cost_payment_12;
	}
	public void setCost_payment_12(String cost_payment_12) {
		this.cost_payment_12 = cost_payment_12;
	}
	public String getRoi_1() {
		return roi_1;
	}
	public void setRoi_1(String roi_1) {
		this.roi_1 = roi_1;
	}
	public String getRoi_2() {
		return roi_2;
	}
	public void setRoi_2(String roi_2) {
		this.roi_2 = roi_2;
	}
	public String getRoi_3() {
		return roi_3;
	}
	public void setRoi_3(String roi_3) {
		this.roi_3 = roi_3;
	}
	public String getRoi_4() {
		return roi_4;
	}
	public void setRoi_4(String roi_4) {
		this.roi_4 = roi_4;
	}
	public String getRoi_5() {
		return roi_5;
	}
	public void setRoi_5(String roi_5) {
		this.roi_5 = roi_5;
	}
	public String getRoi_6() {
		return roi_6;
	}
	public void setRoi_6(String roi_6) {
		this.roi_6 = roi_6;
	}
	public String getRoi_7() {
		return roi_7;
	}
	public void setRoi_7(String roi_7) {
		this.roi_7 = roi_7;
	}
	public String getRoi_8() {
		return roi_8;
	}
	public void setRoi_8(String roi_8) {
		this.roi_8 = roi_8;
	}
	public String getRoi_9() {
		return roi_9;
	}
	public void setRoi_9(String roi_9) {
		this.roi_9 = roi_9;
	}
	public String getRoi_10() {
		return roi_10;
	}
	public void setRoi_10(String roi_10) {
		this.roi_10 = roi_10;
	}
	public String getRoi_11() {
		return roi_11;
	}
	public void setRoi_11(String roi_11) {
		this.roi_11 = roi_11;
	}
	public String getRoi_12() {
		return roi_12;
	}
	public void setRoi_12(String roi_12) {
		this.roi_12 = roi_12;
	}
	public String getCpa() {
		return cpa;
	}
	public void setCpa(String cpa) {
		this.cpa = cpa;
	}
	public String getAppName() {
		return appName;
	}
	public void setAppName(String appName) {
		this.appName = appName;
	}
}
