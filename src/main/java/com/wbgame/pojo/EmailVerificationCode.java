package com.wbgame.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR> Assistant
 * @Description 邮件验证码实体类
 * @Date 2024-12-24
 */
@Data
public class EmailVerificationCode {

    /** 主键ID */
    private Long id;

    /** 验证邮箱 */
    private String email;

    /** 邮件标题 */
    private String subject;

    /** 邮件内容 */
    private String content;

    /** 解析出的验证码 */
    private String verificationCode;

    /** 验证码来源平台（华为、小米等） */
    private String platform;

    /** 邮件唯一标识 */
    private String messageId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 状态：0-无效，1-有效 */
    private Integer status;

    /** 备注信息 */
    private String remark;
}
