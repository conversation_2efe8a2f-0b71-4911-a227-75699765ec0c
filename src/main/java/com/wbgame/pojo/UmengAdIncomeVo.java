package com.wbgame.pojo;

public class UmengAdIncomeVo {

	private String appid; //应用id
	private String tdate;
	private String adcode; // 广告ID码
	private String appname; // 应用名
	private String appkey; // 友盟应用key
	private String channel; // 渠道，oppo、vivo
	private String media; // 媒体
	private int addnum; // 新增用户数
	private int actnum; // 活跃用户数
	private String avgnum; //新增占比
	private String total_income; // 所有位置总收入
	private String dau_arpu; // 总用户平均收入（总收入/总活跃，保留两位小数）

	private String country; // 区分数据的国家地区

	// 各类型 人均pv （(banner展示+原生banner展示)/活跃用户数）
	private String banner_pv;
	private String plaque_pv;
	private String splash_pv;
	private String video_pv;
	private String msg_pv;
	private String plaque_video_pv;

	private String system_splash_pv;   //人均系统开屏 pv
	private String native_new_plaque_pv; //原生新样式插屏 pv
	private String native_new_banner_pv; //原生新样式banner pv
	private String suspend_icon_pv;  //悬浮icon pv
	private String native_plaque_pv; //原生插屏人均pv
	private String native_banner_pv; //原生banner人均pv


	// 各类型 每用户平均收入 arpu （(banner收入+原生banner收入)/活跃用户数）
	private String banner_arpu;
	private String plaque_arpu;
	private String splash_arpu;
	private String video_arpu;
	private String msg_arpu;
	private String plaque_video_arpu;

	private String ad_violation_type;



	private String system_splash_arpu;   //系统开屏 arpu
	private String native_new_plaque_arpu; //原生新样式插屏 arpu
	private String native_new_banner_arpu; //原生新样式banner arpu
	private String suspend_icon_arpu;  //悬浮icon arpu



	// 各位置 千次展示广告收入 ecpm （收入/展示*1000）
	private String banner_ecpm;
	private String plaque_ecpm;
	private String splash_ecpm;
	private String native_banner_ecpm;
	private String native_plaque_ecpm;
	private String native_splash_ecpm;
	private String video_ecpm;

	private String system_splash_ecpm;   //系统开屏 ecpm
	private String native_new_plaque_ecpm; //原生新样式插屏 ecpm
	private String native_new_banner_ecpm; //原生新样式banner ecpm
	private String suspend_icon_ecpm;  //悬浮icon ecpm


	
	private String banner_show; // banner展示pv
	private String plaque_show; // 插屏展示pv
	private String splash_show; // 开屏展示pv
	private String native_banner_show; // 原生banner展示pv
	private String native_plaque_show; // 原生插屏展示pv
	private String native_splash_show; // 原生开屏展示pv
	private String video_show; // 视频展示pv

	private String system_splash_show;   //系统开屏 pv
	private String native_new_plaque_show; //原生新样式插屏 pv
	private String native_new_banner_show; //原生新样式banner pv
	private String suspend_icon_show;  //悬浮icon pv



	private String banner_income; // banner收入
	private String plaque_income; // 插屏收入
	private String splash_income; // 开屏收入
	private String native_banner_income; // 原生banner收入
	private String native_plaque_income; // 原生插屏收入
	private String native_splash_income; // 原生开屏收入
	private String video_income; // 视频收入

	private String system_splash_income;   //系统开屏 收入
	private String native_new_plaque_income; //原生新样式插屏 收入
	private String native_new_banner_income; //原生新样式banner 收入
	private String suspend_icon_income;  //悬浮icon 收入

	
	
	private String plaque_video_ecpm; // 插屏视频 千次展示广告收入ecpm
	private String plaque_video_show; // 插屏视频展示pv
	private String plaque_video_income; // 插屏视频收入
	
	private String native_msg_ecpm; // 原生msg 千次展示广告收入ecpm
	private String native_msg_show; // 原生msg 展示pv
	private String native_msg_income; // 原生msg 收入
	
	private String banner_click; // banner点击数
	private String plaque_click; // 插屏点击数
	private String splash_click; // 开屏点击数
	private String native_banner_click; // 原生banner点击数
	private String native_plaque_click; // 原生插屏点击数
	private String native_splash_click; // 原生开屏点击数
	private String video_click; // 视频点击数
	private String plaque_video_click; // 插屏视频点击数
	private String native_msg_click; // 原生msg 点击数

	private String system_splash_click;   //系统开屏 点击数
	private String native_new_plaque_click; //原生新样式插屏 点击数
	private String native_new_banner_click; //原生新样式banner 点击数
	private String suspend_icon_click;  //悬浮icon 点击数

	private String gameName; // 渠道游戏名称

	private String temp_id; //功能标识

	private String temp_name; //功能名称

	private String large_ver;	//基于大版本

	private String source; //来源:media-媒体/self-自统计

	private String active_temp_id;	//活跃模块 功能标识id

	private String active_temp_name;	//活跃模块 功能名称

	private String banner_request;//请求-banner
	private String plaque_request; //请求-插屏
	private String splash_request; // 开屏请求数
	private String video_request; // 视频请求
	private String native_banner_request; // 原生banner请求
	private String native_plaque_request; // 原生插屏请求
	private String native_splash_request; // 原生开屏请求
	private String plaque_video_request; // 插屏视频请求
	private String native_msg_request; // 原生msg 请求
	private String system_splash_request;   //系统开屏 请求
	private String native_new_plaque_request; //原生新样式插屏 请求
	private String native_new_banner_request; //原生新样式banner 请求
	private String suspend_icon_request;  //悬浮icon 请求

	private String banner_fill;//填充-banner
	private String plaque_fill;//填充-插屏
	private String splash_fill;//开屏填充数
	private String video_fill; // 视频填充
	private String native_banner_fill; // 原生banner填充
	private String native_plaque_fill; // 原生插屏填充
	private String native_splash_fill; // 原生开屏填充
	private String plaque_video_fill; // 插屏视频填充
	private String native_msg_fill; // 原生msg 填充
	private String system_splash_fill;   //系统开屏 填充
	private String native_new_plaque_fill; //原生新样式插屏 填充
	private String native_new_banner_fill; //原生新样式banner 填充
	private String suspend_icon_fill;  //悬浮icon 填充


	//活跃用户展示点击-开屏
	private String show_total_ad_active_cnt;
	private String show_total_ad_active_cnt_rate;
	private String click_total_ad_active_cnt;
	private String click_total_ad_active_cnt_rate;



	private String show_splash_ad_active_cnt;
	private String show_splash_ad_active_cnt_rate;
	private String click_splash_ad_active_cnt;
	private String click_splash_ad_active_cnt_rate;

	//插屏
	private String show_plaque_ad_active_cnt;
	private String show_plaque_ad_active_cnt_rate;
	private String click_plaque_ad_active_cnt;
	private String click_plaque_ad_active_cnt_rate;

	//banner
	private String show_banner_ad_active_cnt;
	private String show_banner_ad_active_cnt_rate;
	private String click_banner_ad_active_cnt;
	private String click_banner_ad_active_cnt_rate;

	private String show_video_ad_active_cnt;
	private String show_video_ad_active_cnt_rate;

	private String click_video_ad_active_cnt;
	private String click_video_ad_active_cnt_rate;


	private String show_msg_ad_active_cnt;
	private String show_msg_ad_active_cnt_rate;

	private String click_msg_ad_active_cnt;
	private String click_msg_ad_active_cnt_rate;


	private String show_icon_ad_active_cnt;
	private String show_icon_ad_active_cnt_rate;
	private String click_icon_ad_active_cnt;
	private String click_icon_ad_active_cnt_rate;



	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getGameName() {
		return gameName;
	}

	public void setGameName(String gameName) {
		this.gameName = gameName;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getNative_plaque_pv() {
		return native_plaque_pv;
	}

	public void setNative_plaque_pv(String native_plaque_pv) {
		this.native_plaque_pv = native_plaque_pv;
	}

	public String getNative_banner_pv() {
		return native_banner_pv;
	}

	public void setNative_banner_pv(String native_banner_pv) {
		this.native_banner_pv = native_banner_pv;
	}

	public String getSystem_splash_pv() {
		return system_splash_pv;
	}

	public void setSystem_splash_pv(String system_splash_pv) {
		this.system_splash_pv = system_splash_pv;
	}

	public String getNative_new_plaque_pv() {
		return native_new_plaque_pv;
	}

	public void setNative_new_plaque_pv(String native_new_plaque_pv) {
		this.native_new_plaque_pv = native_new_plaque_pv;
	}

	public String getNative_new_banner_pv() {
		return native_new_banner_pv;
	}

	public void setNative_new_banner_pv(String native_new_banner_pv) {
		this.native_new_banner_pv = native_new_banner_pv;
	}

	public String getSuspend_icon_pv() {
		return suspend_icon_pv;
	}

	public void setSuspend_icon_pv(String suspend_icon_pv) {
		this.suspend_icon_pv = suspend_icon_pv;
	}

	public String getSystem_splash_arpu() {
		return system_splash_arpu;
	}

	public void setSystem_splash_arpu(String system_splash_arpu) {
		this.system_splash_arpu = system_splash_arpu;
	}

	public String getNative_new_plaque_arpu() {
		return native_new_plaque_arpu;
	}

	public void setNative_new_plaque_arpu(String native_new_plaque_arpu) {
		this.native_new_plaque_arpu = native_new_plaque_arpu;
	}

	public String getNative_new_banner_arpu() {
		return native_new_banner_arpu;
	}

	public void setNative_new_banner_arpu(String native_new_banner_arpu) {
		this.native_new_banner_arpu = native_new_banner_arpu;
	}

	public String getSuspend_icon_arpu() {
		return suspend_icon_arpu;
	}

	public void setSuspend_icon_arpu(String suspend_icon_arpu) {
		this.suspend_icon_arpu = suspend_icon_arpu;
	}

	public String getSystem_splash_ecpm() {
		return system_splash_ecpm;
	}

	public void setSystem_splash_ecpm(String system_splash_ecpm) {
		this.system_splash_ecpm = system_splash_ecpm;
	}

	public String getNative_new_plaque_ecpm() {
		return native_new_plaque_ecpm;
	}

	public void setNative_new_plaque_ecpm(String native_new_plaque_ecpm) {
		this.native_new_plaque_ecpm = native_new_plaque_ecpm;
	}

	public String getNative_new_banner_ecpm() {
		return native_new_banner_ecpm;
	}

	public void setNative_new_banner_ecpm(String native_new_banner_ecpm) {
		this.native_new_banner_ecpm = native_new_banner_ecpm;
	}

	public String getSuspend_icon_ecpm() {
		return suspend_icon_ecpm;
	}

	public void setSuspend_icon_ecpm(String suspend_icon_ecpm) {
		this.suspend_icon_ecpm = suspend_icon_ecpm;
	}

	public String getSystem_splash_show() {
		return system_splash_show;
	}

	public void setSystem_splash_show(String system_splash_show) {
		this.system_splash_show = system_splash_show;
	}

	public String getNative_new_plaque_show() {
		return native_new_plaque_show;
	}

	public void setNative_new_plaque_show(String native_new_plaque_show) {
		this.native_new_plaque_show = native_new_plaque_show;
	}

	public String getNative_new_banner_show() {
		return native_new_banner_show;
	}

	public void setNative_new_banner_show(String native_new_banner_show) {
		this.native_new_banner_show = native_new_banner_show;
	}

	public String getSuspend_icon_show() {
		return suspend_icon_show;
	}

	public void setSuspend_icon_show(String suspend_icon_show) {
		this.suspend_icon_show = suspend_icon_show;
	}

	public String getSystem_splash_income() {
		return system_splash_income;
	}

	public void setSystem_splash_income(String system_splash_income) {
		this.system_splash_income = system_splash_income;
	}

	public String getNative_new_plaque_income() {
		return native_new_plaque_income;
	}

	public void setNative_new_plaque_income(String native_new_plaque_income) {
		this.native_new_plaque_income = native_new_plaque_income;
	}

	public String getNative_new_banner_income() {
		return native_new_banner_income;
	}

	public void setNative_new_banner_income(String native_new_banner_income) {
		this.native_new_banner_income = native_new_banner_income;
	}

	public String getSuspend_icon_income() {
		return suspend_icon_income;
	}

	public void setSuspend_icon_income(String suspend_icon_income) {
		this.suspend_icon_income = suspend_icon_income;
	}

	public String getSystem_splash_click() {
		return system_splash_click;
	}

	public void setSystem_splash_click(String system_splash_click) {
		this.system_splash_click = system_splash_click;
	}

	public String getNative_new_plaque_click() {
		return native_new_plaque_click;
	}

	public void setNative_new_plaque_click(String native_new_plaque_click) {
		this.native_new_plaque_click = native_new_plaque_click;
	}

	public String getNative_new_banner_click() {
		return native_new_banner_click;
	}

	public void setNative_new_banner_click(String native_new_banner_click) {
		this.native_new_banner_click = native_new_banner_click;
	}

	public String getSuspend_icon_click() {
		return suspend_icon_click;
	}

	public void setSuspend_icon_click(String suspend_icon_click) {
		this.suspend_icon_click = suspend_icon_click;
	}

	public String getTdate() {
		return tdate;
	}
	public void setTdate(String tdate) {
		this.tdate = tdate;
	}
	public String getAdcode() {
		return adcode;
	}
	public void setAdcode(String adcode) {
		this.adcode = adcode;
	}
	public String getAppname() {
		return appname;
	}
	public void setAppname(String appname) {
		this.appname = appname;
	}
	public String getAppkey() {
		return appkey;
	}
	public void setAppkey(String appkey) {
		this.appkey = appkey;
	}
	
	public String getChannel() {
		return channel;
	}
	public void setChannel(String channel) {
		this.channel = channel;
	}
	public int getActnum() {
		return actnum;
	}
	public void setActnum(int actnum) {
		this.actnum = actnum;
	}
	public String getTotal_income() {
		return total_income;
	}
	public void setTotal_income(String total_income) {
		this.total_income = total_income;
	}
	public String getDau_arpu() {
		return dau_arpu;
	}
	public void setDau_arpu(String dau_arpu) {
		this.dau_arpu = dau_arpu;
	}
	public String getBanner_pv() {
		return banner_pv;
	}
	public void setBanner_pv(String banner_pv) {
		this.banner_pv = banner_pv;
	}
	public String getPlaque_pv() {
		return plaque_pv;
	}
	public void setPlaque_pv(String plaque_pv) {
		this.plaque_pv = plaque_pv;
	}
	public String getSplash_pv() {
		return splash_pv;
	}
	public void setSplash_pv(String splash_pv) {
		this.splash_pv = splash_pv;
	}
	public String getVideo_pv() {
		return video_pv;
	}
	public void setVideo_pv(String video_pv) {
		this.video_pv = video_pv;
	}
	public String getBanner_arpu() {
		return banner_arpu;
	}
	public void setBanner_arpu(String banner_arpu) {
		this.banner_arpu = banner_arpu;
	}
	public String getPlaque_arpu() {
		return plaque_arpu;
	}
	public void setPlaque_arpu(String plaque_arpu) {
		this.plaque_arpu = plaque_arpu;
	}
	public String getSplash_arpu() {
		return splash_arpu;
	}
	public void setSplash_arpu(String splash_arpu) {
		this.splash_arpu = splash_arpu;
	}
	public String getVideo_arpu() {
		return video_arpu;
	}
	public void setVideo_arpu(String video_arpu) {
		this.video_arpu = video_arpu;
	}
	public String getBanner_ecpm() {
		return banner_ecpm;
	}
	public void setBanner_ecpm(String banner_ecpm) {
		this.banner_ecpm = banner_ecpm;
	}
	public String getPlaque_ecpm() {
		return plaque_ecpm;
	}
	public void setPlaque_ecpm(String plaque_ecpm) {
		this.plaque_ecpm = plaque_ecpm;
	}
	public String getSplash_ecpm() {
		return splash_ecpm;
	}
	public void setSplash_ecpm(String splash_ecpm) {
		this.splash_ecpm = splash_ecpm;
	}
	public String getNative_banner_ecpm() {
		return native_banner_ecpm;
	}
	public void setNative_banner_ecpm(String native_banner_ecpm) {
		this.native_banner_ecpm = native_banner_ecpm;
	}
	public String getNative_plaque_ecpm() {
		return native_plaque_ecpm;
	}
	public void setNative_plaque_ecpm(String native_plaque_ecpm) {
		this.native_plaque_ecpm = native_plaque_ecpm;
	}
	public String getNative_splash_ecpm() {
		return native_splash_ecpm;
	}
	public void setNative_splash_ecpm(String native_splash_ecpm) {
		this.native_splash_ecpm = native_splash_ecpm;
	}
	public String getVideo_ecpm() {
		return video_ecpm;
	}
	public void setVideo_ecpm(String video_ecpm) {
		this.video_ecpm = video_ecpm;
	}
	public String getBanner_show() {
		return banner_show;
	}
	public void setBanner_show(String banner_show) {
		this.banner_show = banner_show;
	}
	public String getPlaque_show() {
		return plaque_show;
	}
	public void setPlaque_show(String plaque_show) {
		this.plaque_show = plaque_show;
	}
	public String getSplash_show() {
		return splash_show;
	}
	public void setSplash_show(String splash_show) {
		this.splash_show = splash_show;
	}
	public String getNative_banner_show() {
		return native_banner_show;
	}
	public void setNative_banner_show(String native_banner_show) {
		this.native_banner_show = native_banner_show;
	}
	public String getNative_plaque_show() {
		return native_plaque_show;
	}
	public void setNative_plaque_show(String native_plaque_show) {
		this.native_plaque_show = native_plaque_show;
	}
	public String getNative_splash_show() {
		return native_splash_show;
	}
	public void setNative_splash_show(String native_splash_show) {
		this.native_splash_show = native_splash_show;
	}
	public String getVideo_show() {
		return video_show;
	}
	public void setVideo_show(String video_show) {
		this.video_show = video_show;
	}
	public String getBanner_income() {
		return banner_income;
	}
	public void setBanner_income(String banner_income) {
		this.banner_income = banner_income;
	}
	public String getPlaque_income() {
		return plaque_income;
	}
	public void setPlaque_income(String plaque_income) {
		this.plaque_income = plaque_income;
	}
	public String getSplash_income() {
		return splash_income;
	}
	public void setSplash_income(String splash_income) {
		this.splash_income = splash_income;
	}
	public String getNative_banner_income() {
		return native_banner_income;
	}
	public void setNative_banner_income(String native_banner_income) {
		this.native_banner_income = native_banner_income;
	}
	public String getNative_plaque_income() {
		return native_plaque_income;
	}
	public void setNative_plaque_income(String native_plaque_income) {
		this.native_plaque_income = native_plaque_income;
	}
	public String getNative_splash_income() {
		return native_splash_income;
	}
	public void setNative_splash_income(String native_splash_income) {
		this.native_splash_income = native_splash_income;
	}
	public String getVideo_income() {
		return video_income;
	}
	public void setVideo_income(String video_income) {
		this.video_income = video_income;
	}
	public String getPlaque_video_ecpm() {
		return plaque_video_ecpm;
	}
	public void setPlaque_video_ecpm(String plaque_video_ecpm) {
		this.plaque_video_ecpm = plaque_video_ecpm;
	}
	public String getPlaque_video_show() {
		return plaque_video_show;
	}
	public void setPlaque_video_show(String plaque_video_show) {
		this.plaque_video_show = plaque_video_show;
	}
	public String getPlaque_video_income() {
		return plaque_video_income;
	}
	public void setPlaque_video_income(String plaque_video_income) {
		this.plaque_video_income = plaque_video_income;
	}
	public String getMsg_pv() {
		return msg_pv;
	}
	public void setMsg_pv(String msg_pv) {
		this.msg_pv = msg_pv;
	}
	public String getMsg_arpu() {
		return msg_arpu;
	}
	public void setMsg_arpu(String msg_arpu) {
		this.msg_arpu = msg_arpu;
	}
	public String getNative_msg_ecpm() {
		return native_msg_ecpm;
	}
	public void setNative_msg_ecpm(String native_msg_ecpm) {
		this.native_msg_ecpm = native_msg_ecpm;
	}
	public String getNative_msg_show() {
		return native_msg_show;
	}
	public void setNative_msg_show(String native_msg_show) {
		this.native_msg_show = native_msg_show;
	}
	public String getNative_msg_income() {
		return native_msg_income;
	}
	public void setNative_msg_income(String native_msg_income) {
		this.native_msg_income = native_msg_income;
	}
	public String getPlaque_video_pv() {
		return plaque_video_pv;
	}
	public void setPlaque_video_pv(String plaque_video_pv) {
		this.plaque_video_pv = plaque_video_pv;
	}
	public String getPlaque_video_arpu() {
		return plaque_video_arpu;
	}
	public void setPlaque_video_arpu(String plaque_video_arpu) {
		this.plaque_video_arpu = plaque_video_arpu;
	}

	public int getAddnum() {
		return addnum;
	}

	public void setAddnum(int addnum) {
		this.addnum = addnum;
	}

	public String getAvgnum() {
		return avgnum;
	}

	public void setAvgnum(String avgnum) {
		this.avgnum = avgnum;
	}
	public String getBanner_click() {
		return banner_click;
	}
	public void setBanner_click(String banner_click) {
		this.banner_click = banner_click;
	}
	public String getPlaque_click() {
		return plaque_click;
	}
	public void setPlaque_click(String plaque_click) {
		this.plaque_click = plaque_click;
	}
	public String getSplash_click() {
		return splash_click;
	}
	public void setSplash_click(String splash_click) {
		this.splash_click = splash_click;
	}
	public String getNative_banner_click() {
		return native_banner_click;
	}
	public void setNative_banner_click(String native_banner_click) {
		this.native_banner_click = native_banner_click;
	}
	public String getNative_plaque_click() {
		return native_plaque_click;
	}
	public void setNative_plaque_click(String native_plaque_click) {
		this.native_plaque_click = native_plaque_click;
	}
	public String getNative_splash_click() {
		return native_splash_click;
	}
	public void setNative_splash_click(String native_splash_click) {
		this.native_splash_click = native_splash_click;
	}
	public String getVideo_click() {
		return video_click;
	}
	public void setVideo_click(String video_click) {
		this.video_click = video_click;
	}
	public String getPlaque_video_click() {
		return plaque_video_click;
	}
	public void setPlaque_video_click(String plaque_video_click) {
		this.plaque_video_click = plaque_video_click;
	}
	public String getNative_msg_click() {
		return native_msg_click;
	}
	public void setNative_msg_click(String native_msg_click) {
		this.native_msg_click = native_msg_click;
	}

	public String getMedia() {
		return media;
	}

	public void setMedia(String media) {
		this.media = media;
	}

	public String getTemp_id() {
		return temp_id;
	}

	public void setTemp_id(String temp_id) {
		this.temp_id = temp_id;
	}

	public String getTemp_name() {
		return temp_name;
	}

	public void setTemp_name(String temp_name) {
		this.temp_name = temp_name;
	}

	public String getLarge_ver() {
		return large_ver;
	}

	public void setLarge_ver(String large_ver) {
		this.large_ver = large_ver;
	}

	public String getActive_temp_id() {
		return active_temp_id;
	}

	public void setActive_temp_id(String active_temp_id) {
		this.active_temp_id = active_temp_id;
	}

	public String getActive_temp_name() {
		return active_temp_name;
	}

	public void setActive_temp_name(String active_temp_name) {
		this.active_temp_name = active_temp_name;
	}

	public String getBanner_request() {
		return banner_request;
	}

	public void setBanner_request(String banner_request) {
		this.banner_request = banner_request;
	}

	public String getPlaque_request() {
		return plaque_request;
	}

	public void setPlaque_request(String plaque_request) {
		this.plaque_request = plaque_request;
	}

	public String getSplash_request() {
		return splash_request;
	}

	public void setSplash_request(String splash_request) {
		this.splash_request = splash_request;
	}

	public String getVideo_request() {
		return video_request;
	}

	public void setVideo_request(String video_request) {
		this.video_request = video_request;
	}

	public String getNative_banner_request() {
		return native_banner_request;
	}

	public void setNative_banner_request(String native_banner_request) {
		this.native_banner_request = native_banner_request;
	}

	public String getNative_plaque_request() {
		return native_plaque_request;
	}

	public void setNative_plaque_request(String native_plaque_request) {
		this.native_plaque_request = native_plaque_request;
	}

	public String getNative_splash_request() {
		return native_splash_request;
	}

	public void setNative_splash_request(String native_splash_request) {
		this.native_splash_request = native_splash_request;
	}

	public String getPlaque_video_request() {
		return plaque_video_request;
	}

	public void setPlaque_video_request(String plaque_video_request) {
		this.plaque_video_request = plaque_video_request;
	}

	public String getNative_msg_request() {
		return native_msg_request;
	}

	public void setNative_msg_request(String native_msg_request) {
		this.native_msg_request = native_msg_request;
	}

	public String getSystem_splash_request() {
		return system_splash_request;
	}

	public void setSystem_splash_request(String system_splash_request) {
		this.system_splash_request = system_splash_request;
	}

	public String getNative_new_plaque_request() {
		return native_new_plaque_request;
	}

	public void setNative_new_plaque_request(String native_new_plaque_request) {
		this.native_new_plaque_request = native_new_plaque_request;
	}

	public String getNative_new_banner_request() {
		return native_new_banner_request;
	}

	public void setNative_new_banner_request(String native_new_banner_request) {
		this.native_new_banner_request = native_new_banner_request;
	}

	public String getSuspend_icon_request() {
		return suspend_icon_request;
	}

	public void setSuspend_icon_request(String suspend_icon_request) {
		this.suspend_icon_request = suspend_icon_request;
	}

	public String getBanner_fill() {
		return banner_fill;
	}

	public void setBanner_fill(String banner_fill) {
		this.banner_fill = banner_fill;
	}

	public String getPlaque_fill() {
		return plaque_fill;
	}

	public void setPlaque_fill(String plaque_fill) {
		this.plaque_fill = plaque_fill;
	}

	public String getSplash_fill() {
		return splash_fill;
	}

	public void setSplash_fill(String splash_fill) {
		this.splash_fill = splash_fill;
	}

	public String getVideo_fill() {
		return video_fill;
	}

	public void setVideo_fill(String video_fill) {
		this.video_fill = video_fill;
	}

	public String getNative_banner_fill() {
		return native_banner_fill;
	}

	public void setNative_banner_fill(String native_banner_fill) {
		this.native_banner_fill = native_banner_fill;
	}

	public String getNative_plaque_fill() {
		return native_plaque_fill;
	}

	public void setNative_plaque_fill(String native_plaque_fill) {
		this.native_plaque_fill = native_plaque_fill;
	}

	public String getNative_splash_fill() {
		return native_splash_fill;
	}

	public void setNative_splash_fill(String native_splash_fill) {
		this.native_splash_fill = native_splash_fill;
	}

	public String getPlaque_video_fill() {
		return plaque_video_fill;
	}

	public void setPlaque_video_fill(String plaque_video_fill) {
		this.plaque_video_fill = plaque_video_fill;
	}

	public String getNative_msg_fill() {
		return native_msg_fill;
	}

	public void setNative_msg_fill(String native_msg_fill) {
		this.native_msg_fill = native_msg_fill;
	}

	public String getSystem_splash_fill() {
		return system_splash_fill;
	}

	public void setSystem_splash_fill(String system_splash_fill) {
		this.system_splash_fill = system_splash_fill;
	}

	public String getNative_new_plaque_fill() {
		return native_new_plaque_fill;
	}

	public void setNative_new_plaque_fill(String native_new_plaque_fill) {
		this.native_new_plaque_fill = native_new_plaque_fill;
	}

	public String getNative_new_banner_fill() {
		return native_new_banner_fill;
	}

	public void setNative_new_banner_fill(String native_new_banner_fill) {
		this.native_new_banner_fill = native_new_banner_fill;
	}

	public String getSuspend_icon_fill() {
		return suspend_icon_fill;
	}

	public void setSuspend_icon_fill(String suspend_icon_fill) {
		this.suspend_icon_fill = suspend_icon_fill;
	}


	public String getShow_splash_ad_active_cnt() {
		return show_splash_ad_active_cnt;
	}

	public void setShow_splash_ad_active_cnt(String show_splash_ad_active_cnt) {
		this.show_splash_ad_active_cnt = show_splash_ad_active_cnt;
	}

	public String getClick_splash_ad_active_cnt() {
		return click_splash_ad_active_cnt;
	}

	public void setClick_splash_ad_active_cnt(String click_splash_ad_active_cnt) {
		this.click_splash_ad_active_cnt = click_splash_ad_active_cnt;
	}

	public String getShow_plaque_ad_active_cnt() {
		return show_plaque_ad_active_cnt;
	}

	public void setShow_plaque_ad_active_cnt(String show_plaque_ad_active_cnt) {
		this.show_plaque_ad_active_cnt = show_plaque_ad_active_cnt;
	}

	public String getClick_plaque_ad_active_cnt() {
		return click_plaque_ad_active_cnt;
	}

	public void setClick_plaque_ad_active_cnt(String click_plaque_ad_active_cnt) {
		this.click_plaque_ad_active_cnt = click_plaque_ad_active_cnt;
	}

	public String getShow_banner_ad_active_cnt() {
		return show_banner_ad_active_cnt;
	}

	public void setShow_banner_ad_active_cnt(String show_banner_ad_active_cnt) {
		this.show_banner_ad_active_cnt = show_banner_ad_active_cnt;
	}

	public String getClick_banner_ad_active_cnt() {
		return click_banner_ad_active_cnt;
	}

	public void setClick_banner_ad_active_cnt(String click_banner_ad_active_cnt) {
		this.click_banner_ad_active_cnt = click_banner_ad_active_cnt;
	}

	public String getShow_video_ad_active_cnt() {
		return show_video_ad_active_cnt;
	}

	public void setShow_video_ad_active_cnt(String show_video_ad_active_cnt) {
		this.show_video_ad_active_cnt = show_video_ad_active_cnt;
	}

	public String getClick_video_ad_active_cnt() {
		return click_video_ad_active_cnt;
	}

	public void setClick_video_ad_active_cnt(String click_video_ad_active_cnt) {
		this.click_video_ad_active_cnt = click_video_ad_active_cnt;
	}

	public String getShow_msg_ad_active_cnt() {
		return show_msg_ad_active_cnt;
	}

	public void setShow_msg_ad_active_cnt(String show_msg_ad_active_cnt) {
		this.show_msg_ad_active_cnt = show_msg_ad_active_cnt;
	}

	public String getClick_msg_ad_active_cnt() {
		return click_msg_ad_active_cnt;
	}

	public void setClick_msg_ad_active_cnt(String click_msg_ad_active_cnt) {
		this.click_msg_ad_active_cnt = click_msg_ad_active_cnt;
	}

	public String getShow_icon_ad_active_cnt() {
		return show_icon_ad_active_cnt;
	}

	public void setShow_icon_ad_active_cnt(String show_icon_ad_active_cnt) {
		this.show_icon_ad_active_cnt = show_icon_ad_active_cnt;
	}

	public String getClick_icon_ad_active_cnt() {
		return click_icon_ad_active_cnt;
	}

	public void setClick_icon_ad_active_cnt(String click_icon_ad_active_cnt) {
		this.click_icon_ad_active_cnt = click_icon_ad_active_cnt;
	}

	public String getShow_total_ad_active_cnt() {
		return show_total_ad_active_cnt;
	}

	public void setShow_total_ad_active_cnt(String show_total_ad_active_cnt) {
		this.show_total_ad_active_cnt = show_total_ad_active_cnt;
	}

	public String getShow_total_ad_active_cnt_rate() {
		return show_total_ad_active_cnt_rate;
	}

	public void setShow_total_ad_active_cnt_rate(String show_total_ad_active_cnt_rate) {
		this.show_total_ad_active_cnt_rate = show_total_ad_active_cnt_rate;
	}

	public String getClick_total_ad_active_cnt() {
		return click_total_ad_active_cnt;
	}

	public void setClick_total_ad_active_cnt(String click_total_ad_active_cnt) {
		this.click_total_ad_active_cnt = click_total_ad_active_cnt;
	}

	public String getClick_total_ad_active_cnt_rate() {
		return click_total_ad_active_cnt_rate;
	}

	public void setClick_total_ad_active_cnt_rate(String click_total_ad_active_cnt_rate) {
		this.click_total_ad_active_cnt_rate = click_total_ad_active_cnt_rate;
	}

	public String getShow_splash_ad_active_cnt_rate() {
		return show_splash_ad_active_cnt_rate;
	}

	public void setShow_splash_ad_active_cnt_rate(String show_splash_ad_active_cnt_rate) {
		this.show_splash_ad_active_cnt_rate = show_splash_ad_active_cnt_rate;
	}

	public String getClick_splash_ad_active_cnt_rate() {
		return click_splash_ad_active_cnt_rate;
	}

	public void setClick_splash_ad_active_cnt_rate(String click_splash_ad_active_cnt_rate) {
		this.click_splash_ad_active_cnt_rate = click_splash_ad_active_cnt_rate;
	}

	public String getShow_plaque_ad_active_cnt_rate() {
		return show_plaque_ad_active_cnt_rate;
	}

	public void setShow_plaque_ad_active_cnt_rate(String show_plaque_ad_active_cnt_rate) {
		this.show_plaque_ad_active_cnt_rate = show_plaque_ad_active_cnt_rate;
	}

	public String getClick_plaque_ad_active_cnt_rate() {
		return click_plaque_ad_active_cnt_rate;
	}

	public void setClick_plaque_ad_active_cnt_rate(String click_plaque_ad_active_cnt_rate) {
		this.click_plaque_ad_active_cnt_rate = click_plaque_ad_active_cnt_rate;
	}

	public String getShow_banner_ad_active_cnt_rate() {
		return show_banner_ad_active_cnt_rate;
	}

	public void setShow_banner_ad_active_cnt_rate(String show_banner_ad_active_cnt_rate) {
		this.show_banner_ad_active_cnt_rate = show_banner_ad_active_cnt_rate;
	}

	public String getClick_banner_ad_active_cnt_rate() {
		return click_banner_ad_active_cnt_rate;
	}

	public void setClick_banner_ad_active_cnt_rate(String click_banner_ad_active_cnt_rate) {
		this.click_banner_ad_active_cnt_rate = click_banner_ad_active_cnt_rate;
	}

	public String getShow_video_ad_active_cnt_rate() {
		return show_video_ad_active_cnt_rate;
	}

	public void setShow_video_ad_active_cnt_rate(String show_video_ad_active_cnt_rate) {
		this.show_video_ad_active_cnt_rate = show_video_ad_active_cnt_rate;
	}

	public String getClick_video_ad_active_cnt_rate() {
		return click_video_ad_active_cnt_rate;
	}

	public void setClick_video_ad_active_cnt_rate(String click_video_ad_active_cnt_rate) {
		this.click_video_ad_active_cnt_rate = click_video_ad_active_cnt_rate;
	}

	public String getShow_msg_ad_active_cnt_rate() {
		return show_msg_ad_active_cnt_rate;
	}

	public void setShow_msg_ad_active_cnt_rate(String show_msg_ad_active_cnt_rate) {
		this.show_msg_ad_active_cnt_rate = show_msg_ad_active_cnt_rate;
	}

	public String getClick_msg_ad_active_cnt_rate() {
		return click_msg_ad_active_cnt_rate;
	}

	public void setClick_msg_ad_active_cnt_rate(String click_msg_ad_active_cnt_rate) {
		this.click_msg_ad_active_cnt_rate = click_msg_ad_active_cnt_rate;
	}

	public String getShow_icon_ad_active_cnt_rate() {
		return show_icon_ad_active_cnt_rate;
	}

	public void setShow_icon_ad_active_cnt_rate(String show_icon_ad_active_cnt_rate) {
		this.show_icon_ad_active_cnt_rate = show_icon_ad_active_cnt_rate;
	}

	public String getClick_icon_ad_active_cnt_rate() {
		return click_icon_ad_active_cnt_rate;
	}

	public void setClick_icon_ad_active_cnt_rate(String click_icon_ad_active_cnt_rate) {
		this.click_icon_ad_active_cnt_rate = click_icon_ad_active_cnt_rate;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getAd_violation_type() {
		return ad_violation_type;
	}

	public void setAd_violation_type(String ad_violation_type) {
		this.ad_violation_type = ad_violation_type;
	}
}
