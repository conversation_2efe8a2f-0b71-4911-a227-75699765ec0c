package com.wbgame.pojo.custom;

import lombok.Data;


/**
 * @description: 竞品数据分析表实体类
 **/
@Data
public class CompetitorDataVo {
    

    private String date;        // 日期
    private String time;        // 时间
    private String channel;     // 渠道
    private String company;     // 公司主体
    private String appName;     // 产品名称
    private String device;      // 设备型号
    private String osVersion;   // 安卓版本
    private String sdkVersion;  // sdk版本
    private String appVersion;  // 包体版本
    private String duration;   // 时长
    private String adType;      // 广告类型
    private Integer sourceCounts;      // 广告源数量
    private Integer loadSuccCounts;    // 加载成功数
    private Integer loadFailCounts;    // 加载失败数
    private Integer selfShow;          // 自有广告展示数
    private Integer apiShow;           // 三方api广告展示数
    private Integer sdkShow;           // 三方sdk广告展示数
    private String secondShow;        // 分钟展示数
    private String totalShow;         // 总展示数
    private Integer selfClick;         // 自有广告点击数
    private Integer apiClick;          // 三方api广告点击数
    private Integer sdkClick;          // 三方sdk广告点击数
    private String secondClick;       // 分钟点击数
    private String totalClick;        // 总点击数
    private String selfShowRate;       // 自有广告展示率
    private String apiShowRate;        // 三方api广告展示率
    private String sdkShowRate;        // 三方sdk广告展示率
    private String totalShowRate;      // 总展示率
    private String selfClickRate;      // 自有广告点击率
    private String apiClickRate;       // 三方api广告点击率
    private String sdkClickRate;       // 三方sdk广告点击率
    private String totalClickRate;     // 总点击率
    private String createtime;     // 创建时间
    private String createtime;     // 创建时间


}