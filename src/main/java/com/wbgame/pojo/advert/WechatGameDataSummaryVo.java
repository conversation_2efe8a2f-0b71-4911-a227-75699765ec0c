package com.wbgame.pojo.advert;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @description: 小游戏数据综合汇总Vo
 * @author: xugx
 * @date: 2023/06/19
 **/
@ApiModel(value = "小游戏数据综合汇总")
public class WechatGameDataSummaryVo {
	@ApiModelProperty("日期")
    private String tdate;
	@ApiModelProperty("产品")
    private String app;
	@ApiModelProperty("返点消耗")
    private double rebateSpend;
    
	@ApiModelProperty("媒体后台新增")
    private int installs;
    
    
	@ApiModelProperty("媒体买量CPA")
    private double cpa;
    
	@ApiModelProperty("整体CPA")
    private double t_cpa;
    
	@ApiModelProperty("次留成本")
    private double rd1_spend;
	
	@ApiModelProperty("流量广告金")
    private double advertising_money;
    
	@ApiModelProperty("流量广告金占广告收入比例")
    private String pro_traffic_adv;
    @ApiModelProperty("7留成本")
    private double rd7_spend;
    @ApiModelProperty("广告arpu")
    private double ad_arpu;
    @ApiModelProperty("arpu_视频")
    private double arpu_video;
    @ApiModelProperty("人均pv_视频")
    private double avg_pv_video;
    @ApiModelProperty("ecpm_视频")
    private double ecpm_video; 
    @ApiModelProperty("总流水_分成前加广告金")
    private double total_revenue_divide_before; 
    @ApiModelProperty("总收入_分成后加广告金")
    private double total_revenue_divide_after; 
    @ApiModelProperty("分成前总arpu_分成前加广告金")
    private double total_arpu_divide_before; 
    @ApiModelProperty("分成后总arpu_分成后加广告金")
    private double total_arpu_divide_after; 
    @ApiModelProperty("当天利润")
    private double profit_daily; 
    @ApiModelProperty("当天毛利润率")
    private String  gross_margin_daily; 
    @ApiModelProperty("收入ROI_当日")
    private String roi; 
    @ApiModelProperty("买量新增_外部应用")
    private int new_purchase_external_app;
    @ApiModelProperty("买量新增_微信广告")
    private int new_purchase_wechat_advertising;
    @ApiModelProperty("分享新进")
    private int share_new_advances;
    @ApiModelProperty("注册用户")
    private int add_num;
    @ApiModelProperty("活跃用户")
    private int act_num;
    @ApiModelProperty("其他新增")
    private int other_add_num;
    @ApiModelProperty("新增占比")
    private String add_rate;
    @ApiModelProperty("自然量占比")
    private String nature_rate;
    @ApiModelProperty("分享拉新占比")
    private String share_rate;
    @ApiModelProperty("累计注册用户数")
    private long cumulative_registere_users;
    @ApiModelProperty("访问次数")
    private long act_visits_num;
    @ApiModelProperty("人均访问次数")
    private double visits_num_per;
    @ApiModelProperty("人均停留时长_秒")
    private double average_stay;
    @ApiModelProperty("次日留存率")
    private String rd1_rate;
    @ApiModelProperty("7日留存率")
    private String rd7_rate;
    @ApiModelProperty("分享次数")
    private int shares_num;
    @ApiModelProperty("分享用户数")
    private int share_users_number;
    @ApiModelProperty("分享率")
    private String sharing_rate;
    @ApiModelProperty("付费用户数")
    private int purchase_users;
    @ApiModelProperty("首次付费用户数")
    private int first_purchase_users;
    @ApiModelProperty("付费渗透率")
    private double paid_permeability;
    @ApiModelProperty("场景渠道分析-活跃用户   广告收入")
    private double ad_revenue;
    @ApiModelProperty("安卓内购流水_分成前")
    private double pay_amount;
    @ApiModelProperty("iOS收入")
    private double apple_income;
    @ApiModelProperty("总内购收入_分成后")
    private double purchase_income;
    @ApiModelProperty("内购arpu_分成后")
    private double purchase_arpu;
    @ApiModelProperty("内购收入占比")
    private String purchase_rate;
    
    @ApiModelProperty("人均分享")
    private double avg_share;
    
    @ApiModelProperty("累计ROI")
    private String acmul_roi; 
    
	public String getTdate() {
		return tdate;
	}
	public void setTdate(String tdate) {
		this.tdate = tdate;
	}
	public int getNew_purchase_external_app() {
		return new_purchase_external_app;
	}
	public void setNew_purchase_external_app(int new_purchase_external_app) {
		this.new_purchase_external_app = new_purchase_external_app;
	}
	public int getNew_purchase_wechat_advertising() {
		return new_purchase_wechat_advertising;
	}
	public void setNew_purchase_wechat_advertising(int new_purchase_wechat_advertising) {
		this.new_purchase_wechat_advertising = new_purchase_wechat_advertising;
	}
	public int getShare_new_advances() {
		return share_new_advances;
	}
	public void setShare_new_advances(int share_new_advances) {
		this.share_new_advances = share_new_advances;
	}
	public int getAdd_num() {
		return add_num;
	}
	public void setAdd_num(int add_num) {
		this.add_num = add_num;
	}
	public int getAct_num() {
		return act_num;
	}
	public void setAct_num(int act_num) {
		this.act_num = act_num;
	}
	public long getCumulative_registere_users() {
		return cumulative_registere_users;
	}
	public void setCumulative_registere_users(long cumulative_registere_users) {
		this.cumulative_registere_users = cumulative_registere_users;
	}
	public long getAct_visits_num() {
		return act_visits_num;
	}
	public void setAct_visits_num(long act_visits_num) {
		this.act_visits_num = act_visits_num;
	}
	public double getVisits_num_per() {
		return visits_num_per;
	}
	public void setVisits_num_per(double visits_num_per) {
		this.visits_num_per = visits_num_per;
	}
	public double getAverage_stay() {
		return average_stay;
	}
	public void setAverage_stay(double average_stay) {
		this.average_stay = average_stay;
	}
	public String getRd1_rate() {
		return rd1_rate;
	}
	public void setRd1_rate(String rd1_rate) {
		this.rd1_rate = rd1_rate;
	}
	public String getRd7_rate() {
		return rd7_rate;
	}
	public void setRd7_rate(String rd7_rate) {
		this.rd7_rate = rd7_rate;
	}
	public int getShares_num() {
		return shares_num;
	}
	public void setShares_num(int shares_num) {
		this.shares_num = shares_num;
	}
	public int getShare_users_number() {
		return share_users_number;
	}
	public void setShare_users_number(int share_users_number) {
		this.share_users_number = share_users_number;
	}
	public String getSharing_rate() {
		return sharing_rate;
	}
	public void setSharing_rate(String sharing_rate) {
		this.sharing_rate = sharing_rate;
	}
	public int getPurchase_users() {
		return purchase_users;
	}
	public void setPurchase_users(int purchase_users) {
		this.purchase_users = purchase_users;
	}
	public int getFirst_purchase_users() {
		return first_purchase_users;
	}
	public void setFirst_purchase_users(int first_purchase_users) {
		this.first_purchase_users = first_purchase_users;
	}
	public double getPaid_permeability() {
		return paid_permeability;
	}
	public void setPaid_permeability(double paid_permeability) {
		this.paid_permeability = paid_permeability;
	}
	public double getAd_revenue() {
		return ad_revenue;
	}
	public void setAd_revenue(double ad_revenue) {
		this.ad_revenue = ad_revenue;
	}
	public double getPay_amount() {
		return pay_amount;
	}
	public void setPay_amount(double pay_amount) {
		this.pay_amount = pay_amount;
	}
	public String getApp() {
		return app;
	}
	public void setApp(String app) {
		this.app = app;
	}
	public double getRebateSpend() {
		return rebateSpend;
	}
	public void setRebateSpend(double rebateSpend) {
		this.rebateSpend = rebateSpend;
	}
	public int getInstalls() {
		return installs;
	}
	public void setInstalls(int installs) {
		this.installs = installs;
	}
	public double getCpa() {
		return cpa;
	}
	public void setCpa(double cpa) {
		this.cpa = cpa;
	}
	public double getT_cpa() {
		return t_cpa;
	}
	public void setT_cpa(double t_cpa) {
		this.t_cpa = t_cpa;
	}
	public double getRd1_spend() {
		return rd1_spend;
	}
	public void setRd1_spend(double rd1_spend) {
		this.rd1_spend = rd1_spend;
	}
	public double getAdvertising_money() {
		return advertising_money;
	}
	public void setAdvertising_money(double advertising_money) {
		this.advertising_money = advertising_money;
	}
	public String getPro_traffic_adv() {
		return pro_traffic_adv;
	}
	public void setPro_traffic_adv(String pro_traffic_adv) {
		this.pro_traffic_adv = pro_traffic_adv;
	}
	public double getRd7_spend() {
		return rd7_spend;
	}
	public void setRd7_spend(double rd7_spend) {
		this.rd7_spend = rd7_spend;
	}
	public double getAd_arpu() {
		return ad_arpu;
	}
	public void setAd_arpu(double ad_arpu) {
		this.ad_arpu = ad_arpu;
	}
	public double getArpu_video() {
		return arpu_video;
	}
	public void setArpu_video(double arpu_video) {
		this.arpu_video = arpu_video;
	}
	public double getAvg_pv_video() {
		return avg_pv_video;
	}
	public void setAvg_pv_video(double avg_pv_video) {
		this.avg_pv_video = avg_pv_video;
	}
	public double getEcpm_video() {
		return ecpm_video;
	}
	public void setEcpm_video(double ecpm_video) {
		this.ecpm_video = ecpm_video;
	}
	public double getTotal_revenue_divide_before() {
		return total_revenue_divide_before;
	}
	public void setTotal_revenue_divide_before(double total_revenue_divide_before) {
		this.total_revenue_divide_before = total_revenue_divide_before;
	}
	public double getTotal_revenue_divide_after() {
		return total_revenue_divide_after;
	}
	public void setTotal_revenue_divide_after(double total_revenue_divide_after) {
		this.total_revenue_divide_after = total_revenue_divide_after;
	}
	public double getTotal_arpu_divide_before() {
		return total_arpu_divide_before;
	}
	public void setTotal_arpu_divide_before(double total_arpu_divide_before) {
		this.total_arpu_divide_before = total_arpu_divide_before;
	}
	public double getTotal_arpu_divide_after() {
		return total_arpu_divide_after;
	}
	public void setTotal_arpu_divide_after(double total_arpu_divide_after) {
		this.total_arpu_divide_after = total_arpu_divide_after;
	}
	public double getProfit_daily() {
		return profit_daily;
	}
	public void setProfit_daily(double profit_daily) {
		this.profit_daily = profit_daily;
	}
	public String getGross_margin_daily() {
		return gross_margin_daily;
	}
	public void setGross_margin_daily(String gross_margin_daily) {
		this.gross_margin_daily = gross_margin_daily;
	}
	public String getRoi() {
		return roi;
	}
	public void setRoi(String roi) {
		this.roi = roi;
	}
	public int getOther_add_num() {
		return other_add_num;
	}
	public void setOther_add_num(int other_add_num) {
		this.other_add_num = other_add_num;
	}
	public String getAdd_rate() {
		return add_rate;
	}
	public void setAdd_rate(String add_rate) {
		this.add_rate = add_rate;
	}
	public String getNature_rate() {
		return nature_rate;
	}
	public void setNature_rate(String nature_rate) {
		this.nature_rate = nature_rate;
	}
	public String getShare_rate() {
		return share_rate;
	}
	public void setShare_rate(String share_rate) {
		this.share_rate = share_rate;
	}
	public double getApple_income() {
		return apple_income;
	}
	public void setApple_income(double apple_income) {
		this.apple_income = apple_income;
	}
	public double getPurchase_income() {
		return purchase_income;
	}
	public void setPurchase_income(double purchase_income) {
		this.purchase_income = purchase_income;
	}
	public double getPurchase_arpu() {
		return purchase_arpu;
	}
	public void setPurchase_arpu(double purchase_arpu) {
		this.purchase_arpu = purchase_arpu;
	}
	public String getPurchase_rate() {
		return purchase_rate;
	}
	public void setPurchase_rate(String purchase_rate) {
		this.purchase_rate = purchase_rate;
	}
	public double getAvg_share() {
		return avg_share;
	}
	public void setAvg_share(double avg_share) {
		this.avg_share = avg_share;
	}
	public String getAcmul_roi() {
		return acmul_roi;
	}
	public void setAcmul_roi(String acmul_roi) {
		this.acmul_roi = acmul_roi;
	}
}
