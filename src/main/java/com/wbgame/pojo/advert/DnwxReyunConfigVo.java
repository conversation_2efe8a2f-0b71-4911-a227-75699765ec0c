package com.wbgame.pojo.advert;

public class DnwxReyunConfigVo {
	
	private String id; // 唯一标识
	
	private int appid; // 应用ID
	private String cha_id; // 子渠道
	private String pid; // 项目ID
	private String event; // 上报给热云的事件名称 String类型，取值范围：launch/register/event_(1-12)
	private int eventType; // 事件类型 int类型，取值范围：1/2/3/4/5/6/7  time/ecpm/event/customer/level/arpu/click
	private int times; // 时长或触发次数（时长单位为秒） int类型 
	private int loop; // 事件上报类型 int类型，取值范围：1/2/3/4 首日首次/首次/首日每次/每次 
	private int rate; // 触发事件后上报给热云的比例，取值范围（1-100）
	private String adType; // 广告类型 String类型(支持多选)，取值范围 1: video 2：plaque 3: splash 4: banner 5: msg 6: icon
	private double ecpmValue; // ecpm的值（达到该值才会触发上报）int类型  单位为分
	private Integer ecpmType; // ecpm事件触发上报的类型   0: 平均值    1: 次数+平均值
	private String action; // 自定义事件 String类型
	private String timeType; // 时长统计的 int类型，取值范围：1/2/3 累计在线时长/激活当日累计在线时长/单次启动在线时长
	private String statu; // 状态，1-开启、0-关闭
	private String sdk_type; // SDK类型，热云-reyun 头条-headline
	
	private String cuser; // 创建人
	private String createtime; // 创建时间
	private String euser; // 最后操作人
	private String endtime; // 最后操作时间
	
	private String checkTimes; // ecpm判断次数 int类型
	private String levelType; // 关卡类型 int类型，取值范围：1/2/3/4 进入关卡/关卡胜利/关卡失败/关卡结束
	private String level; // 关卡数 String类型
	
	private String logic; // 逻辑，1-与、0-或，默认0
	private String out; // out-应用内外 (0-应用内、1-应用外、all-全部)
	private String action_params; // 自定义事件参数，通过json数组格式传递[{"k1":"level1","k2":"1","k3":"200"}]其中k2作为选项：1-等于，2-大于，3-小于
	
	/** 投放渠道 */
	private String buy_id;
	/** 投放活动计划 */
	private String buy_act;

	/** 操作记录id */
	private String record_id;

	/** 条件达成比例 */
	private int rule_rate;


	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public int getAppid() {
		return appid;
	}
	public void setAppid(int appid) {
		this.appid = appid;
	}
	public String getCha_id() {
		return cha_id;
	}
	public void setCha_id(String cha_id) {
		this.cha_id = cha_id;
	}
	public String getPid() {
		return pid;
	}
	public void setPid(String pid) {
		this.pid = pid;
	}
	public String getEvent() {
		return event;
	}
	public void setEvent(String event) {
		this.event = event;
	}
	public int getEventType() {
		return eventType;
	}
	public void setEventType(int eventType) {
		this.eventType = eventType;
	}
	public int getTimes() {
		return times;
	}
	public void setTimes(int times) {
		this.times = times;
	}
	public int getLoop() {
		return loop;
	}
	public void setLoop(int loop) {
		this.loop = loop;
	}
	public int getRate() {
		return rate;
	}
	public void setRate(int rate) {
		this.rate = rate;
	}
	public String getAdType() {
		return adType;
	}
	public void setAdType(String adType) {
		this.adType = adType;
	}

	public double getEcpmValue() {
		return ecpmValue;
	}

	public void setEcpmValue(double ecpmValue) {
		this.ecpmValue = ecpmValue;
	}

	public String getAction() {
		return action;
	}
	public void setAction(String action) {
		this.action = action;
	}
	public String getTimeType() {
		return timeType;
	}
	public void setTimeType(String timeType) {
		this.timeType = timeType;
	}
	public String getCuser() {
		return cuser;
	}
	public void setCuser(String cuser) {
		this.cuser = cuser;
	}
	public String getCreatetime() {
		return createtime;
	}
	public void setCreatetime(String createtime) {
		this.createtime = createtime;
	}
	public String getEuser() {
		return euser;
	}
	public void setEuser(String euser) {
		this.euser = euser;
	}
	public String getEndtime() {
		return endtime;
	}
	public void setEndtime(String endtime) {
		this.endtime = endtime;
	}
	public String getStatu() {
		return statu;
	}
	public void setStatu(String statu) {
		this.statu = statu;
	}
	public Integer getEcpmType() {
		return ecpmType;
	}
	public void setEcpmType(Integer ecpmType) {
		this.ecpmType = ecpmType;
	}
	public String getBuy_id() {
		return buy_id;
	}
	public void setBuy_id(String buy_id) {
		this.buy_id = buy_id;
	}
	public String getBuy_act() {
		return buy_act;
	}
	public void setBuy_act(String buy_act) {
		this.buy_act = buy_act;
	}
	public String getCheckTimes() {
		return checkTimes;
	}
	public void setCheckTimes(String checkTimes) {
		this.checkTimes = checkTimes;
	}
	public String getLevelType() {
		return levelType;
	}
	public void setLevelType(String levelType) {
		this.levelType = levelType;
	}
	public String getLevel() {
		return level;
	}
	public void setLevel(String level) {
		this.level = level;
	}
	public String getSdk_type() {
		return sdk_type;
	}
	public void setSdk_type(String sdk_type) {
		this.sdk_type = sdk_type;
	}
	public String getLogic() {
		return logic;
	}
	public void setLogic(String logic) {
		this.logic = logic;
	}
	public String getOut() {
		return out;
	}
	public void setOut(String out) {
		this.out = out;
	}
	public String getAction_params() {
		return action_params;
	}
	public void setAction_params(String action_params) {
		this.action_params = action_params;
	}

	public String getRecord_id() {
		return record_id;
	}

	public void setRecord_id(String record_id) {
		this.record_id = record_id;
	}

	public int getRule_rate() {
		return rule_rate;
	}

	public void setRule_rate(int rule_rate) {
		this.rule_rate = rule_rate;
	}
}
