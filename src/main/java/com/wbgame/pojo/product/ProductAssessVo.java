package com.wbgame.pojo.product;

public class ProductAssessVo {
	
	private String week; // 周期
	private String appid; // 产品
	private String operates; // 运营人员
	private int daily_addnum; // 日均新增
	private int daily_revenue; // 日均收入
	private String merge_assess; // 综合评级,
	private String compare_addnum; // 新增环比值,
	private String compare_revenue; // 收入环比值,
	private String compare_assess; // 评级环比值
	
	private String mapkey;
	
	
	public String getWeek() {
		return week;
	}
	public void setWeek(String week) {
		this.week = week;
	}
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getOperates() {
		return operates;
	}
	public void setOperates(String operates) {
		this.operates = operates;
	}
	public int getDaily_addnum() {
		return daily_addnum;
	}
	public void setDaily_addnum(int daily_addnum) {
		this.daily_addnum = daily_addnum;
	}
	public int getDaily_revenue() {
		return daily_revenue;
	}
	public void setDaily_revenue(int daily_revenue) {
		this.daily_revenue = daily_revenue;
	}
	public String getMerge_assess() {
		return merge_assess;
	}
	public void setMerge_assess(String merge_assess) {
		this.merge_assess = merge_assess;
	}
	public String getCompare_addnum() {
		return compare_addnum;
	}
	public void setCompare_addnum(String compare_addnum) {
		this.compare_addnum = compare_addnum;
	}
	public String getCompare_revenue() {
		return compare_revenue;
	}
	public void setCompare_revenue(String compare_revenue) {
		this.compare_revenue = compare_revenue;
	}
	public String getCompare_assess() {
		return compare_assess;
	}
	public void setCompare_assess(String compare_assess) {
		this.compare_assess = compare_assess;
	}
	public String getMapkey() {
		return mapkey;
	}
	public void setMapkey(String mapkey) {
		this.mapkey = mapkey;
	}
	
}
