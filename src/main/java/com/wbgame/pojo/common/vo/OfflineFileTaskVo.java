package com.wbgame.pojo.common.vo;


import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "离线文件任务")
@Data
public class  OfflineFileTaskVo implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -1602601901679348654L;

	private int id;
    
    @ApiModelProperty(value = "创建人",required = true)
    private String create_user;
    
    @ApiModelProperty(value = "页面标识（中文名称，如：广告ROI报表（新））")
    private String page_mark;
    
    @ApiModelProperty(value = "文件地址")
    private String file_url;
    
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date create_time;
    
    @ApiModelProperty(value = "任务状态：1-创建中  2-完成状态  3-重试状态  4-失败状态")
	private int task_status;
    
    @ApiModelProperty(value = "偏移量")
	private int limit;
    
    @ApiModelProperty(value = "页码")
	private int start;
}