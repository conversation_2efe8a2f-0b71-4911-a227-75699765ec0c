package com.wbgame.pojo.game.report;

/**
 * <AUTHOR>
 * @Classname ToolEventVo
 * @Description 工具自定义事件漏斗
 * @Date 2022/6/6 11:36
 */
public class ToolEventVo {

    private String ds;                  //日期
    private String appid;               //应用id
    private String appname;             //应用名称
    private String pid;                 //项目id
    private String channel;             //子渠道
    private String name_k_v;            //事件参数组合
    private String is_auto;             //区分是否是自动激活用户， 0主动激活、1自动激活、2不区分用户、-1该用户无法判断
    private String active_cat;          //区分当天用户是否是新用户， 0老用户、1新用户、2不区分用户、-1该用户无法判断
    private String uv;                  //用户数
    private String pv;                  //次数
    private String brand;               // 设备品牌
    private String os_ver;              // 系统版本

    public String getAppname() {
        return appname;
    }

    public void setAppname(String appname) {
        this.appname = appname;
    }

    public String getDs() {
        return ds;
    }

    public void setDs(String ds) {
        this.ds = ds;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getName_k_v() {
        return name_k_v;
    }

    public void setName_k_v(String name_k_v) {
        this.name_k_v = name_k_v;
    }

    public String getIs_auto() {
        return is_auto;
    }

    public void setIs_auto(String is_auto) {
        this.is_auto = is_auto;
    }

    public String getActive_cat() {
        return active_cat;
    }

    public void setActive_cat(String active_cat) {
        this.active_cat = active_cat;
    }

    public String getUv() {
        return uv;
    }

    public void setUv(String uv) {
        this.uv = uv;
    }

    public String getPv() {
        return pv;
    }

    public void setPv(String pv) {
        this.pv = pv;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getOs_ver() {
        return os_ver;
    }

    public void setOs_ver(String os_ver) {
        this.os_ver = os_ver;
    }
}
