package com.wbgame.pojo.game;

/**
 * <AUTHOR>
 * @Classname TimingTrendVo
 * @Description TODO
 * @Date 2022/4/27 17:53
 */
public class TimingTrendVo {

    private String tdate;               //日期
    private String appid;               //appid
    private String pid;                 //项目id
    private String channel;             //子渠道
    private String hourly;              //时间段
    private String add_user;            //新增人数
    private String active_user;         //活跃人数
    private String pay_num;             //付费人数
    private String ad_revenue;          //广告收入
    private String iap_revenue;         //计费收入
    private String total_revenue;       //整体收入
    private String ad_arpu;             //广告arpu
    private String iap_arpu;            //内购arpu
    private String total_arpu;          //整体arpu

    public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getHourly() {
        return hourly;
    }

    public void setHourly(String hourly) {
        this.hourly = hourly;
    }

    public String getAdd_user() {
        return add_user;
    }

    public void setAdd_user(String add_user) {
        this.add_user = add_user;
    }

    public String getActive_user() {
        return active_user;
    }

    public void setActive_user(String active_user) {
        this.active_user = active_user;
    }

    public String getPay_num() {
        return pay_num;
    }

    public void setPay_num(String pay_num) {
        this.pay_num = pay_num;
    }

    public String getAd_revenue() {
        return ad_revenue;
    }

    public void setAd_revenue(String ad_revenue) {
        this.ad_revenue = ad_revenue;
    }

    public String getIap_revenue() {
        return iap_revenue;
    }

    public void setIap_revenue(String iap_revenue) {
        this.iap_revenue = iap_revenue;
    }

    public String getTotal_revenue() {
        return total_revenue;
    }

    public void setTotal_revenue(String total_revenue) {
        this.total_revenue = total_revenue;
    }

    public String getAd_arpu() {
        return ad_arpu;
    }

    public void setAd_arpu(String ad_arpu) {
        this.ad_arpu = ad_arpu;
    }

    public String getIap_arpu() {
        return iap_arpu;
    }

    public void setIap_arpu(String iap_arpu) {
        this.iap_arpu = iap_arpu;
    }

    public String getTotal_arpu() {
        return total_arpu;
    }

    public void setTotal_arpu(String total_arpu) {
        this.total_arpu = total_arpu;
    }
}
