package com.wbgame.pojo.finance;

import com.wbgame.common.QueryGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

@ApiModel("部门接收参数")
public class MainSysDepartmentV3DTO {
    private Integer id;

    @ApiModelProperty("部门名")
    @NotBlank(message = "部门名称不能为空")
    private String departmentName;

    @ApiModelProperty("上级部门id")
    private Integer parentId;

    @ApiModelProperty("页码")
    @NotNull(message = "页码错误", groups = QueryGroup.class)
    private Integer start;

    @ApiModelProperty("条数")
    @NotNull(message = "条数错误", groups = QueryGroup.class)
    private Integer limit;

    private String createUser;
    private String updateUser;

    private String value;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName == null ? null : departmentName.trim();
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }
}