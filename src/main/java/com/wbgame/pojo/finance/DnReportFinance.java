package com.wbgame.pojo.finance;


/**
 * 财务-广告投放支出明细对象
 *
 * <AUTHOR>
 * @date 2020/10/16
 */
public class DnReportFinance {
    private Integer id;

    /**
     * 投放平台
     */
    private String ad_platform;

    /**
     * 现金支出
     */
    private String cash_cost;

    /**
     * 总支出
     */
    private String cost;

    /**
     * 赠款消耗
     */
    private String reward_cost;

    /**
     * 赠款存入
     */
    private String income;

    /**
     * 总转入
     */
    private String transfer_in;

    /**
     * 账号
     */
    private String account;

    /**
     * 日期
     */
    private String day;

    private String createTime;

    private String app;

    /**
     * 年返季返消耗
     */
    private String contractRebateRealCharged;

    /**
     * 年返季返存入
     */
    private String contractRebateRealRecharged;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAd_platform() {
        return ad_platform;
    }

    public void setAd_platform(String ad_platform) {
        this.ad_platform = ad_platform;
    }

    public String getCash_cost() {
        return cash_cost;
    }

    public void setCash_cost(String cash_cost) {
        this.cash_cost = cash_cost;
    }

    public String getCost() {
        return cost;
    }

    public void setCost(String cost) {
        this.cost = cost;
    }

    public String getReward_cost() {
        return reward_cost;
    }

    public void setReward_cost(String reward_cost) {
        this.reward_cost = reward_cost;
    }

    public String getIncome() {
        return income;
    }

    public void setIncome(String income) {
        this.income = income;
    }

    public String getTransfer_in() {
        return transfer_in;
    }

    public void setTransfer_in(String transfer_in) {
        this.transfer_in = transfer_in;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getApp() {
        return app;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public String getContractRebateRealCharged() {
        return contractRebateRealCharged;
    }

    public void setContractRebateRealCharged(String contractRebateRealCharged) {
        this.contractRebateRealCharged = contractRebateRealCharged;
    }

    public String getContractRebateRealRecharged() {
        return contractRebateRealRecharged;
    }

    public void setContractRebateRealRecharged(String contractRebateRealRecharged) {
        this.contractRebateRealRecharged = contractRebateRealRecharged;
    }
}