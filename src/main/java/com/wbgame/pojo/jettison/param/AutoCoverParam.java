package com.wbgame.pojo.jettison.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/31 16:36
 */
@Data
@ApiModel("自动封面生成参数")
public class AutoCoverParam {

    @ApiModelProperty(value = "推广目的")
    private Integer ad_type;

    @ApiModelProperty(value = "计划类型")
    private Integer media_type;

    @ApiModelProperty(value = "展现形式")
    private Integer place_type;

    @ApiModelProperty(value = "创意类型")
    private Integer display_mode;

    @ApiModelProperty(value = "创意规格id")
    private Integer material_norm_id;

    @ApiModelProperty(value = "视频地址")
    private String video_url;

    @ApiModelProperty(value = "自动类型")
    private Integer auto_type;

}
