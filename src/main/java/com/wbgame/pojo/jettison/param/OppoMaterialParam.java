package com.wbgame.pojo.jettison.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/31 16:36
 */
@Data
@ApiModel("自动封面生成参数")
public class OppoMaterialParam {

    @ApiModelProperty(value = "推广类型")
    private Integer extension_type;

    @ApiModelProperty(value = "推广流量")
    private Integer extension_flow;

    @ApiModelProperty(value = "流量场景")
    private Integer flow_scene;

    @ApiModelProperty(value = "统一规格ID")
    private Integer global_spec_id;

    @ApiModelProperty(value = "视频地址")
    private String video_url;

    @ApiModelProperty(value = "自动类型")
    private Integer auto_type;

}
