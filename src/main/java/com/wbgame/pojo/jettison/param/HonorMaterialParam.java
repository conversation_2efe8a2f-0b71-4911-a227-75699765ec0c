package com.wbgame.pojo.jettison.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/31 16:36
 */
@Data
@ApiModel("自动封面生成参数")
public class HonorMaterialParam {

    @ApiModelProperty(value = "推广类型")
    private Integer promotion_purpose;

    @ApiModelProperty(value = "流量场景")
    private Integer traffic_type;

    @ApiModelProperty(value = "投放位置")
    private Long ad_placement_id;

    @ApiModelProperty(value = "创意规格id")
    private Integer ad_creative_spec_id;

    @ApiModelProperty(value = "视频地址")
    private String video_url;

    @ApiModelProperty(value = "自动类型")
    private Integer auto_type;

}
