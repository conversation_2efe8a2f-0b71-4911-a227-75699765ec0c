package com.wbgame.pojo.jettison.report.dto;


public class SpendReportDTO extends BaseReportDTO {

    private Long id;

    private Integer hour;

    private String campaignId;

    private String campaign;

    private String groupId;//广告组（上一层）

    private String groupName;//广告组名称（上一层）

    private Long installs;

    private Long impressions;

    private Long clicks;

    private Long convert;

    private Long register;

    private Long game_addiction;

    private Double spend;

    private Double rebateSpend;

    private Double dollarSpend;

    private Double dollarRebateSpend;

    private Double serviceSpend;

    private String day;

    private String media;

    private String channel;

    private String channel1;

    private String channelType;

    private String type;

    private String first_agent;

    private String agent;

    private String putUser;

    private String artist;

    private String material;

    private String createTime;

    private String clickRate;

    private String clickConvertRate;

    private String impressionConvertRate;

    private Double cpi;

    private String creative_template;

    private String xingqi;

    private String package_name;

    private String shop_id;

    private Double revenue;         //变现金额
    private Double revenue1;        //首日变现金额
    private Double revenue3;        //3日变现金额
    private Double revenue7;        //7日变现金额
    private Double revenue14;       //14日变现金额
    private Double revenue30;       //30日变现金额
    private String payRevenue;      //付费金额
    private Double payRevenue1;     //首日付费金额
    private Double payRevenue3;     //3日付费金额
    private Double payRevenue7;     //7日付费金额
    private Double payRevenue14;    //14日付费金额
    private Double payRevenue30;    //30日付费金额

    private Double clickLtv1;   //广告点击LTV1
    private Double installsLtv1;            //广告激活LTV1
    private Double installsLtv3;            //广告激活LTV3
    private Double installsLtv7;            //广告激活LTV7
    private Double installsLtv14;            //广告激活LTV14
    private Double installsLtv30;           //广告激活LTV30
    private Double registerLtv1;            //广告注册LTV1
    private Double registerLtv3;            //广告注册LTV3
    private Double registerLtv7;            //广告注册LTV7
    private Double registerLtv14;            //广告注册LTV14
    private Double registerLtv30;           //广告注册LTV30
    private Double installsPayLtv1;         //付费激活LTV1
    private Double installsPayLtv3;         //付费激活LTV3
    private Double installsPayLtv7;         //付费激活LTV7
    private Double installsPayLtv30;        //付费激活LTV30
    private Double registerPayLtv1;         //付费注册LTV1
    private Double registerPayLtv3;         //付费注册LTV3
    private Double registerPayLtv7;         //付费注册LTV7
    private Double registerPayLtv30;        //付费注册LTV30
    private Double installsTotalLtv1;       //总LTV1
    private Double installsTotalLtv3;       //总LTV3
    private Double installsTotalLtv7;       //总LTV7
    private Double installsTotalLtv30;      //总LTV30
    private Double registerTotalLtv1;       //总LTV1
    private Double registerTotalLtv3;       //总LTV3
    private Double registerTotalLtv7;       //总LTV7
    private Double registerTotalLtv30;      //总LTV30

    private String roi1;            //广告ROI1
    private String roi3;            //广告ROI3
    private String roi7;            //广告ROI7
    private String roi14;            //广告ROI14
    private String roi30;           //广告ROI30
    private String payRoi1;         //付费ROI1
    private String payRoi3;         //付费ROI3
    private String payRoi7;         //付费ROI7
    private String payRoi30;        //付费ROI30
    private String totalRoi1;       //总ROI1
    private String totalRoi3;       //总ROI3
    private String totalRoi7;       //总ROI7
    private String totalRoi14;       //总ROI14
    private String totalRoi30;      //总ROI30
    private String revenue_hour_roi24; //激活24小时变现ROI
    private Double revenue_hour_24;  //激活24小时变现金额
    private String pay_hour_roi24; //激活24小时付费ROI
    private Double pay_hour_24;  //激活24小时付费金额
    private Long pay_hour_24_user;  //激活24小时付费人数
    private Double pay_hour_24_arpu;  //激活24小时付费arpu
    private Double totalLt3;          //总lt3
    private Double totalLt7;          //总lt7
    private Double totalLt14;          //总lt14
    private Double totalLt30;          //总lt30

    private Double down_ltv1;  //下载ltv1

    private String lt3;

    private String lt7;

    private String lt14;

    private String lt30;

    private Double totalRevenue;

    private String convertRate;

    private String registerRate;

    private String addictionRate;

    private Double convertSpend;

    private Double registerSpend;

    private Double addictionSpend;

    private String installRate;

    private Double avgShowSpend;

    private Integer status;

    private Long download;

    private Double downloadcost;

    private String adsenseType;

    private String adsensePosition;

    private Long reActive;          // 首日拉活量

    private Double reActiveSpend;   // 拉活消耗

    private Double preReActive;     // 首日拉活均价

    private Long uninstalls;        // APP召回量（卸载量）

    private Double uninstallsSpend; // 召回消耗

    private Double preUninstalls;   // APP召回均价

    private String payRoi;
    private String revenueRoi;
    private String buyRoi;

    private Integer gamePayCount;   //付费次数
    private Double gamePayCost;     //付费成本（spend/gamePayCount)
    private String installsGamePayRate;     //激活付费率 (gamePayCount/installs)
    private String registerGamePayRate;     //注册付费率 (gamePayCount/register)
    private Integer payCount;       //首次付费次数
    private Double activePayCost;   //首次付费成本（spend/payCount)
    private String installsActivePayRate;   //首次激活付费率 (payCount/installs)
    private String registerActivePayRate;   //首次注册付费率 (payCount/register)
    private Integer payCount7;      //7日付费次数
    private Double payCountPer7;    //7日人均付费（7日付费次数/7日首次付费次数）
    private Double payCountFirst7;  //7日首次付费次数
    private Integer addPayCount;    //新增付费数
    private Double addPayCost;      //新增付费成本
    private String installsAddPayRate;      //新增激活付费率
    private String registerAddPayRate;      //新增注册付费率

    private String company;

    private Integer source;

    private String phonePlatform;

    private Double bid;             //出价

    private Double bp;              //计费比

    private Double retain1;         //次日留存数

    private Double retain3;         //3日留存数

    private Double retain7;         //7日留存数

    private String installsRetainRate1;     //激活次留率
    private String registerRetainRate1;     //注册次留率

    private String installsRetainRate3;     //激活3留率
    private String registerRetainRate3;     //注册3留率

    private String installsRetainRate7;     //激活7留率
    private String registerRetainRate7;     //注册7留率

    private String transferType;    //转化类型（出价策略）

    private Integer b_install;      //大数据新增

    private Integer b_active;       //大数据活跃

    private Double b_addRevenue;    //大数据新增总收入

    private Double b_activeRevenue; //大数据活跃总收入

    private Integer b_retain1;      //大数据次留数

    private Integer b_addVideo;     //大数据当日新增视频数

    private Integer b_addPlaque;    //大数据当日新增插屏数

    private Double b_cpa;           //大数据当日新增成本

    private Double b_addArpu;       //大数据新增人均收益(总收入/新增人数)

    private Double b_activeArpu;    //大数据活跃人均收益 (总收入/活跃人数)

    private Double b_pvVideo;       //大数据首日人均视频

    private Double b_pvPlaque;      //大数据首日人均插屏

    private String b_roi1;          //大数据当日ROI

    private String b_retainRate1;   //大数据次日留存率

    private String gameName;        //渠道游戏名称

    private Double cashSpend;       //现金消耗

    private Double virtualSpend;    //虚拟金消耗

    private Double cashRebateSpend; //现金返点消耗

    private String business;        //业务模式

    private String rebate;          //返点率

    private String serviceRate;     //服务费率

    private Double beforeRevenue1;  //首日回收金额-广告变现(按计费时间)---分成后

    private Double beforePayRevenue1;//首日回收金额-充值付费(按计费时间)---分成后

    private Double recoveryRevenue;     //首日回收金额-广告变现(按计费时间)---分成后

    private Double recoveryPayRevenue;  //首日回收金额-充值付费(按计费时间)---分成后

    private String beforeRoi1;
    private String recoveryRoi1;

    private String beforePayRoi1;
    private String recoveryPayRoi1;

    private String revenueRate1;

    private String payRevenueRate1;

    private String spendRate;

    private String bidType;

    private String app;//导出用
    private String appCategory;//导出用
    private String appId;//导出用
    private String country;
    private String account; //账户
    private String accountRemark;//账号备注
    private String os_type;//产品系统类型
    private String delivery_mode;//投放模式
    private Integer backActivateCount;//vivo自定义激活数
    private String backActivateRate;//vivo自定义激活达成率
    private Double add_arpu_register;//新增arp注册
    private Double add_arpu_cash;//新增arp变现
    private String adv_perm_rate;//广告渗透率
    private String register_cash_rate;//注册变现率
    private Integer first_cash_user;//首日变现人数
    private Integer cash_user;//变现人数
    private String accountSubject;//投放账号主体

    private String ticket;//vivo共担券

    private Double realTimeRevenue; // oppo实时变现收入
    private String realTimeRoi; // oppo 实时变现roi

    private Integer activeJ;
    private Integer registerJ;
    private Integer backActiveJ;
    private Integer backRegisterJ;

    private Double activeJSpend;
    private String activeJRate;
    private Double activeZSpend;
    private Double registerJSpend;
    private Double backActiveJSpend;
    private Double backRegisterJSpend;

    private String planId;
    private String planName;

    // 华为赔付指标
    // 深度转化数
    private int deep_conversion_nums;
    // 转化数
    private int conversion_nums;
    // 保障后深层转化成本
    private String compensated_deep_conversion_cost;
    // 保障开始时间
    private String compensation_begin_time;
    // 保障结束时间
    private String compensation_end_time;
    // 保障状态
    private String compensation_status;
    // 花费
    private String compensation_real_cost;
    // 保障周期，1-周期一，2-周期二
    private Integer compensation_period;
    // 深层目标转化成本
    private String deep_expect_conversion_cost;
    // 深层转化成本
    private String deep_conversion_cost;
    // 目标转化成本
    private String expect_conversion_cost;
    //保障后广告变现ROI1
    private String compensated_ad_income_one_day_roi;
    // 期望消耗
    private String compensation_expect_cost;
    // 保障后浅层转化成本
    private String compensated_conversion_cost;
    // 保障金额
    private String compensation_amount;
    // 转化成本
    private String conversion_cost;
    // 保障后roi
    private String total_compensation_roi;

    public String getTotal_compensation_roi() {
        return total_compensation_roi;
    }

    public void setTotal_compensation_roi(String total_compensation_roi) {
        this.total_compensation_roi = total_compensation_roi;
    }

    public int getDeep_conversion_nums() {
        return deep_conversion_nums;
    }

    public void setDeep_conversion_nums(int deep_conversion_nums) {
        this.deep_conversion_nums = deep_conversion_nums;
    }

    public int getConversion_nums() {
        return conversion_nums;
    }

    public void setConversion_nums(int conversion_nums) {
        this.conversion_nums = conversion_nums;
    }

    public String getCompensated_deep_conversion_cost() {
        return compensated_deep_conversion_cost;
    }

    public void setCompensated_deep_conversion_cost(String compensated_deep_conversion_cost) {
        this.compensated_deep_conversion_cost = compensated_deep_conversion_cost;
    }

    public String getCompensation_begin_time() {
        return compensation_begin_time;
    }

    public void setCompensation_begin_time(String compensation_begin_time) {
        this.compensation_begin_time = compensation_begin_time;
    }

    public String getCompensation_end_time() {
        return compensation_end_time;
    }

    public void setCompensation_end_time(String compensation_end_time) {
        this.compensation_end_time = compensation_end_time;
    }

    public String getCompensation_status() {
        return compensation_status;
    }

    public void setCompensation_status(String compensation_status) {
        this.compensation_status = compensation_status;
    }

    public String getCompensation_real_cost() {
        return compensation_real_cost;
    }

    public void setCompensation_real_cost(String compensation_real_cost) {
        this.compensation_real_cost = compensation_real_cost;
    }

    public Integer getCompensation_period() {
        return compensation_period;
    }

    public void setCompensation_period(Integer compensation_period) {
        this.compensation_period = compensation_period;
    }

    public String getDeep_expect_conversion_cost() {
        return deep_expect_conversion_cost;
    }

    public void setDeep_expect_conversion_cost(String deep_expect_conversion_cost) {
        this.deep_expect_conversion_cost = deep_expect_conversion_cost;
    }

    public String getDeep_conversion_cost() {
        return deep_conversion_cost;
    }

    public void setDeep_conversion_cost(String deep_conversion_cost) {
        this.deep_conversion_cost = deep_conversion_cost;
    }

    public String getExpect_conversion_cost() {
        return expect_conversion_cost;
    }

    public void setExpect_conversion_cost(String expect_conversion_cost) {
        this.expect_conversion_cost = expect_conversion_cost;
    }

    public String getCompensated_ad_income_one_day_roi() {
        return compensated_ad_income_one_day_roi;
    }

    public void setCompensated_ad_income_one_day_roi(String compensated_ad_income_one_day_roi) {
        this.compensated_ad_income_one_day_roi = compensated_ad_income_one_day_roi;
    }

    public String getCompensation_expect_cost() {
        return compensation_expect_cost;
    }

    public void setCompensation_expect_cost(String compensation_expect_cost) {
        this.compensation_expect_cost = compensation_expect_cost;
    }

    public String getCompensated_conversion_cost() {
        return compensated_conversion_cost;
    }

    public void setCompensated_conversion_cost(String compensated_conversion_cost) {
        this.compensated_conversion_cost = compensated_conversion_cost;
    }

    public String getCompensation_amount() {
        return compensation_amount;
    }

    public void setCompensation_amount(String compensation_amount) {
        this.compensation_amount = compensation_amount;
    }

    public String getConversion_cost() {
        return conversion_cost;
    }

    public void setConversion_cost(String conversion_cost) {
        this.conversion_cost = conversion_cost;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getActiveJRate() {
        return activeJRate;
    }

    public void setActiveJRate(String activeJRate) {
        this.activeJRate = activeJRate;
    }

    public Double getActiveZSpend() {
        return activeZSpend;
    }

    public void setActiveZSpend(Double activeZSpend) {
        this.activeZSpend = activeZSpend;
    }

    public Double getActiveJSpend() {
        return activeJSpend;
    }

    public void setActiveJSpend(Double activeJSpend) {
        this.activeJSpend = activeJSpend;
    }

    public Double getRegisterJSpend() {
        return registerJSpend;
    }

    public void setRegisterJSpend(Double registerJSpend) {
        this.registerJSpend = registerJSpend;
    }

    public Double getBackActiveJSpend() {
        return backActiveJSpend;
    }

    public void setBackActiveJSpend(Double backActiveJSpend) {
        this.backActiveJSpend = backActiveJSpend;
    }

    public Double getBackRegisterJSpend() {
        return backRegisterJSpend;
    }

    public void setBackRegisterJSpend(Double backRegisterJSpend) {
        this.backRegisterJSpend = backRegisterJSpend;
    }

    public Integer getActiveJ() {
        return activeJ;
    }

    public void setActiveJ(Integer activeJ) {
        this.activeJ = activeJ;
    }

    public Integer getRegisterJ() {
        return registerJ;
    }

    public void setRegisterJ(Integer registerJ) {
        this.registerJ = registerJ;
    }

    public Integer getBackActiveJ() {
        return backActiveJ;
    }

    public void setBackActiveJ(Integer backActiveJ) {
        this.backActiveJ = backActiveJ;
    }

    public Integer getBackRegisterJ() {
        return backRegisterJ;
    }

    public void setBackRegisterJ(Integer backRegisterJ) {
        this.backRegisterJ = backRegisterJ;
    }

    public String getRealTimeRoi() {
        return realTimeRoi;
    }

    public void setRealTimeRoi(String realTimeRoi) {
        this.realTimeRoi = realTimeRoi;
    }

    public Double getRealTimeRevenue() {
        return realTimeRevenue;
    }

    public void setRealTimeRevenue(Double realTimeRevenue) {
        this.realTimeRevenue = realTimeRevenue;
    }

    public String getAccountSubject() {
		return accountSubject;
	}

	public void setAccountSubject(String accountSubject) {
		this.accountSubject = accountSubject;
	}

	public String getRecoveryRoi1() {
        return recoveryRoi1;
    }

    public void setRecoveryRoi1(String recoveryRoi1) {
        this.recoveryRoi1 = recoveryRoi1;
    }

    public String getRecoveryPayRoi1() {
        return recoveryPayRoi1;
    }

    public void setRecoveryPayRoi1(String recoveryPayRoi1) {
        this.recoveryPayRoi1 = recoveryPayRoi1;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getHour() {
        return hour;
    }

    public void setHour(Integer hour) {
        this.hour = hour;
    }

    public String getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(String campaignId) {
        this.campaignId = campaignId;
    }

    public String getCampaign() {
        return campaign;
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Long getInstalls() {
        return installs;
    }

    public void setInstalls(Long installs) {
        this.installs = installs;
    }

    public Long getImpressions() {
        return impressions;
    }

    public void setImpressions(Long impressions) {
        this.impressions = impressions;
    }

    public Long getClicks() {
        return clicks;
    }

    public void setClicks(Long clicks) {
        this.clicks = clicks;
    }

    public Long getConvert() {
        return convert;
    }

    public void setConvert(Long convert) {
        this.convert = convert;
    }

    public Long getRegister() {
        return register;
    }

    public void setRegister(Long register) {
        this.register = register;
    }

    public Long getGame_addiction() {
        return game_addiction;
    }

    public void setGame_addiction(Long game_addiction) {
        this.game_addiction = game_addiction;
    }

    public Double getSpend() {
        return spend;
    }

    public void setSpend(Double spend) {
        this.spend = spend;
    }

    public Double getRebateSpend() {
        return rebateSpend;
    }

    public void setRebateSpend(Double rebateSpend) {
        this.rebateSpend = rebateSpend;
    }

    public Double getDollarSpend() {
        return dollarSpend;
    }

    public void setDollarSpend(Double dollarSpend) {
        this.dollarSpend = dollarSpend;
    }

    public Double getDollarRebateSpend() {
        return dollarRebateSpend;
    }

    public void setDollarRebateSpend(Double dollarRebateSpend) {
        this.dollarRebateSpend = dollarRebateSpend;
    }

    public Double getServiceSpend() {
        return serviceSpend;
    }

    public void setServiceSpend(Double serviceSpend) {
        this.serviceSpend = serviceSpend;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getChannel1() {
        return channel1;
    }

    public void setChannel1(String channel1) {
        this.channel1 = channel1;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFirst_agent() {
        return first_agent;
    }

    public void setFirst_agent(String first_agent) {
        this.first_agent = first_agent;
    }

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    public String getPutUser() {
        return putUser;
    }

    public void setPutUser(String putUser) {
        this.putUser = putUser;
    }

    public String getArtist() {
        return artist;
    }

    public void setArtist(String artist) {
        this.artist = artist;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getClickRate() {
        return clickRate;
    }

    public void setClickRate(String clickRate) {
        this.clickRate = clickRate;
    }

    public String getClickConvertRate() {
        return clickConvertRate;
    }

    public void setClickConvertRate(String clickConvertRate) {
        this.clickConvertRate = clickConvertRate;
    }

    public String getImpressionConvertRate() {
        return impressionConvertRate;
    }

    public void setImpressionConvertRate(String impressionConvertRate) {
        this.impressionConvertRate = impressionConvertRate;
    }

    public Double getCpi() {
        return cpi;
    }

    public void setCpi(Double cpi) {
        this.cpi = cpi;
    }

    public Double getRevenue1() {
        return revenue1;
    }

    public void setRevenue1(Double revenue1) {
        this.revenue1 = revenue1;
    }

    public Double getRevenue3() {
        return revenue3;
    }

    public void setRevenue3(Double revenue3) {
        this.revenue3 = revenue3;
    }

    public Double getRevenue7() {
        return revenue7;
    }

    public void setRevenue7(Double revenue7) {
        this.revenue7 = revenue7;
    }

    public Double getRevenue14() {
        return revenue14;
    }

    public void setRevenue14(Double revenue14) {
        this.revenue14 = revenue14;
    }

    public Double getRevenue30() {
        return revenue30;
    }

    public void setRevenue30(Double revenue30) {
        this.revenue30 = revenue30;
    }

    public Double getPayRevenue1() {
        return payRevenue1;
    }

    public void setPayRevenue1(Double payRevenue1) {
        this.payRevenue1 = payRevenue1;
    }

    public Double getPayRevenue3() {
        return payRevenue3;
    }

    public void setPayRevenue3(Double payRevenue3) {
        this.payRevenue3 = payRevenue3;
    }

    public Double getPayRevenue7() {
        return payRevenue7;
    }

    public void setPayRevenue7(Double payRevenue7) {
        this.payRevenue7 = payRevenue7;
    }

    public Double getPayRevenue14() {
        return payRevenue14;
    }

    public void setPayRevenue14(Double payRevenue14) {
        this.payRevenue14 = payRevenue14;
    }

    public Double getPayRevenue30() {
        return payRevenue30;
    }

    public void setPayRevenue30(Double payRevenue30) {
        this.payRevenue30 = payRevenue30;
    }

    public String getRoi1() {
        return roi1;
    }

    public void setRoi1(String roi1) {
        this.roi1 = roi1;
    }

    public String getRoi3() {
        return roi3;
    }

    public void setRoi3(String roi3) {
        this.roi3 = roi3;
    }

    public String getRoi7() {
        return roi7;
    }

    public void setRoi7(String roi7) {
        this.roi7 = roi7;
    }

    public String getRoi30() {
        return roi30;
    }

    public void setRoi30(String roi30) {
        this.roi30 = roi30;
    }

    public String getPayRoi1() {
        return payRoi1;
    }

    public void setPayRoi1(String payRoi1) {
        this.payRoi1 = payRoi1;
    }

    public String getLt3() {
        return lt3;
    }

    public void setLt3(String lt3) {
        this.lt3 = lt3;
    }

    public String getLt7() {
        return lt7;
    }

    public void setLt7(String lt7) {
        this.lt7 = lt7;
    }

    public Double getTotalRevenue() {
        return totalRevenue;
    }

    public void setTotalRevenue(Double totalRevenue) {
        this.totalRevenue = totalRevenue;
    }

    public String getConvertRate() {
        return convertRate;
    }

    public void setConvertRate(String convertRate) {
        this.convertRate = convertRate;
    }

    public String getRegisterRate() {
        return registerRate;
    }

    public void setRegisterRate(String registerRate) {
        this.registerRate = registerRate;
    }

    public String getAddictionRate() {
        return addictionRate;
    }

    public void setAddictionRate(String addictionRate) {
        this.addictionRate = addictionRate;
    }

    public Double getConvertSpend() {
        return convertSpend;
    }

    public void setConvertSpend(Double convertSpend) {
        this.convertSpend = convertSpend;
    }

    public Double getRegisterSpend() {
        return registerSpend;
    }

    public void setRegisterSpend(Double registerSpend) {
        this.registerSpend = registerSpend;
    }

    public Double getAddictionSpend() {
        return addictionSpend;
    }

    public void setAddictionSpend(Double addictionSpend) {
        this.addictionSpend = addictionSpend;
    }

    public String getInstallRate() {
        return installRate;
    }

    public void setInstallRate(String installRate) {
        this.installRate = installRate;
    }

    public Double getAvgShowSpend() {
        return avgShowSpend;
    }

    public void setAvgShowSpend(Double avgShowSpend) {
        this.avgShowSpend = avgShowSpend;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getDownload() {
        return download;
    }

    public void setDownload(Long download) {
        this.download = download;
    }

    public Double getDownloadcost() {
        return downloadcost;
    }

    public void setDownloadcost(Double downloadcost) {
        this.downloadcost = downloadcost;
    }

    public String getAdsenseType() {
        return adsenseType;
    }

    public void setAdsenseType(String adsenseType) {
        this.adsenseType = adsenseType;
    }

    public String getAdsensePosition() {
        return adsensePosition;
    }

    public void setAdsensePosition(String adsensePosition) {
        this.adsensePosition = adsensePosition;
    }

    public Long getReActive() {
        return reActive;
    }

    public void setReActive(Long reActive) {
        this.reActive = reActive;
    }

    public Double getReActiveSpend() {
        return reActiveSpend;
    }

    public void setReActiveSpend(Double reActiveSpend) {
        this.reActiveSpend = reActiveSpend;
    }

    public Double getPreReActive() {
        return preReActive;
    }

    public void setPreReActive(Double preReActive) {
        this.preReActive = preReActive;
    }

    public Long getUninstalls() {
        return uninstalls;
    }

    public void setUninstalls(Long uninstalls) {
        this.uninstalls = uninstalls;
    }

    public Double getUninstallsSpend() {
        return uninstallsSpend;
    }

    public void setUninstallsSpend(Double uninstallsSpend) {
        this.uninstallsSpend = uninstallsSpend;
    }

    public Double getPreUninstalls() {
        return preUninstalls;
    }

    public void setPreUninstalls(Double preUninstalls) {
        this.preUninstalls = preUninstalls;
    }

    public String getPayRoi() {
        return payRoi;
    }

    public void setPayRoi(String payRoi) {
        this.payRoi = payRoi;
    }

    public String getRevenueRoi() {
        return revenueRoi;
    }

    public void setRevenueRoi(String revenueRoi) {
        this.revenueRoi = revenueRoi;
    }

    public String getBuyRoi() {
        return buyRoi;
    }

    public void setBuyRoi(String buyRoi) {
        this.buyRoi = buyRoi;
    }

    public Integer getGamePayCount() {
        return gamePayCount;
    }

    public void setGamePayCount(Integer gamePayCount) {
        this.gamePayCount = gamePayCount;
    }

    public Double getGamePayCost() {
        return gamePayCost;
    }

    public void setGamePayCost(Double gamePayCost) {
        this.gamePayCost = gamePayCost;
    }


    public Integer getPayCount() {
        return payCount;
    }

    public void setPayCount(Integer payCount) {
        this.payCount = payCount;
    }

    public Double getActivePayCost() {
        return activePayCost;
    }

    public void setActivePayCost(Double activePayCost) {
        this.activePayCost = activePayCost;
    }


    public Integer getPayCount7() {
        return payCount7;
    }

    public void setPayCount7(Integer payCount7) {
        this.payCount7 = payCount7;
    }

    public Double getPayCountPer7() {
        return payCountPer7;
    }

    public void setPayCountPer7(Double payCountPer7) {
        this.payCountPer7 = payCountPer7;
    }

    public Double getPayCountFirst7() {
        return payCountFirst7;
    }

    public void setPayCountFirst7(Double payCountFirst7) {
        this.payCountFirst7 = payCountFirst7;
    }

    public Integer getAddPayCount() {
        return addPayCount;
    }

    public void setAddPayCount(Integer addPayCount) {
        this.addPayCount = addPayCount;
    }

    public Double getAddPayCost() {
        return addPayCost;
    }

    public void setAddPayCost(Double addPayCost) {
        this.addPayCost = addPayCost;
    }


    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    @Override
    public String getCountry() {
        return country;
    }

    @Override
    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getPhonePlatform() {
        return phonePlatform;
    }

    public void setPhonePlatform(String phonePlatform) {
        this.phonePlatform = phonePlatform;
    }

    public Double getBid() {
        return bid;
    }

    public void setBid(Double bid) {
        this.bid = bid;
    }

    public Double getBp() {
        return bp;
    }

    public void setBp(Double bp) {
        this.bp = bp;
    }

    public Double getRetain1() {
        return retain1;
    }

    public void setRetain1(Double retain1) {
        this.retain1 = retain1;
    }

    public Double getRetain3() {
        return retain3;
    }

    public void setRetain3(Double retain3) {
        this.retain3 = retain3;
    }

    public Double getRetain7() {
        return retain7;
    }

    public void setRetain7(Double retain7) {
        this.retain7 = retain7;
    }

    public String getInstallsRetainRate1() {
        return installsRetainRate1;
    }

    public void setInstallsRetainRate1(String installsRetainRate1) {
        this.installsRetainRate1 = installsRetainRate1;
    }

    public String getRegisterRetainRate1() {
        return registerRetainRate1;
    }

    public void setRegisterRetainRate1(String registerRetainRate1) {
        this.registerRetainRate1 = registerRetainRate1;
    }

    public String getInstallsRetainRate3() {
        return installsRetainRate3;
    }

    public void setInstallsRetainRate3(String installsRetainRate3) {
        this.installsRetainRate3 = installsRetainRate3;
    }

    public String getRegisterRetainRate3() {
        return registerRetainRate3;
    }

    public void setRegisterRetainRate3(String registerRetainRate3) {
        this.registerRetainRate3 = registerRetainRate3;
    }

    public String getInstallsRetainRate7() {
        return installsRetainRate7;
    }

    public void setInstallsRetainRate7(String installsRetainRate7) {
        this.installsRetainRate7 = installsRetainRate7;
    }

    public String getRegisterRetainRate7() {
        return registerRetainRate7;
    }

    public void setRegisterRetainRate7(String registerRetainRate7) {
        this.registerRetainRate7 = registerRetainRate7;
    }

    public String getTransferType() {
        return transferType;
    }

    public void setTransferType(String transferType) {
        this.transferType = transferType;
    }

    public Integer getB_install() {
        return b_install;
    }

    public void setB_install(Integer b_install) {
        this.b_install = b_install;
    }

    public Integer getB_active() {
        return b_active;
    }

    public void setB_active(Integer b_active) {
        this.b_active = b_active;
    }

    public Double getB_addRevenue() {
        return b_addRevenue;
    }

    public void setB_addRevenue(Double b_addRevenue) {
        this.b_addRevenue = b_addRevenue;
    }

    public Double getB_activeRevenue() {
        return b_activeRevenue;
    }

    public void setB_activeRevenue(Double b_activeRevenue) {
        this.b_activeRevenue = b_activeRevenue;
    }

    public Integer getB_retain1() {
        return b_retain1;
    }

    public void setB_retain1(Integer b_retain1) {
        this.b_retain1 = b_retain1;
    }

    public Integer getB_addVideo() {
        return b_addVideo;
    }

    public void setB_addVideo(Integer b_addVideo) {
        this.b_addVideo = b_addVideo;
    }

    public Integer getB_addPlaque() {
        return b_addPlaque;
    }

    public void setB_addPlaque(Integer b_addPlaque) {
        this.b_addPlaque = b_addPlaque;
    }

    public Double getB_cpa() {
        return b_cpa;
    }

    public void setB_cpa(Double b_cpa) {
        this.b_cpa = b_cpa;
    }

    public Double getB_addArpu() {
        return b_addArpu;
    }

    public void setB_addArpu(Double b_addArpu) {
        this.b_addArpu = b_addArpu;
    }

    public Double getB_activeArpu() {
        return b_activeArpu;
    }

    public void setB_activeArpu(Double b_activeArpu) {
        this.b_activeArpu = b_activeArpu;
    }

    public Double getB_pvVideo() {
        return b_pvVideo;
    }

    public void setB_pvVideo(Double b_pvVideo) {
        this.b_pvVideo = b_pvVideo;
    }

    public Double getB_pvPlaque() {
        return b_pvPlaque;
    }

    public void setB_pvPlaque(Double b_pvPlaque) {
        this.b_pvPlaque = b_pvPlaque;
    }

    public String getB_roi1() {
        return b_roi1;
    }

    public void setB_roi1(String b_roi1) {
        this.b_roi1 = b_roi1;
    }

    public String getB_retainRate1() {
        return b_retainRate1;
    }

    public void setB_retainRate1(String b_retainRate1) {
        this.b_retainRate1 = b_retainRate1;
    }

    public String getGameName() {
        return gameName;
    }

    public void setGameName(String gameName) {
        this.gameName = gameName;
    }

    public Double getCashSpend() {
        return cashSpend;
    }

    public void setCashSpend(Double cashSpend) {
        this.cashSpend = cashSpend;
    }

    public Double getVirtualSpend() {
        return virtualSpend;
    }

    public void setVirtualSpend(Double virtualSpend) {
        this.virtualSpend = virtualSpend;
    }

    public Double getCashRebateSpend() {
        return cashRebateSpend;
    }

    public void setCashRebateSpend(Double cashRebateSpend) {
        this.cashRebateSpend = cashRebateSpend;
    }

    public String getBusiness() {
        return business;
    }

    public void setBusiness(String business) {
        this.business = business;
    }

    public String getRebate() {
        return rebate;
    }

    public void setRebate(String rebate) {
        this.rebate = rebate;
    }

    public String getServiceRate() {
        return serviceRate;
    }

    public void setServiceRate(String serviceRate) {
        this.serviceRate = serviceRate;
    }

    public Double getBeforeRevenue1() {
        return beforeRevenue1;
    }

    public void setBeforeRevenue1(Double beforeRevenue1) {
        this.beforeRevenue1 = beforeRevenue1;
    }

    public Double getBeforePayRevenue1() {
        return beforePayRevenue1;
    }

    public void setBeforePayRevenue1(Double beforePayRevenue1) {
        this.beforePayRevenue1 = beforePayRevenue1;
    }

    public String getBeforeRoi1() {
        return beforeRoi1;
    }

    public void setBeforeRoi1(String beforeRoi1) {
        this.beforeRoi1 = beforeRoi1;
    }

    public String getBeforePayRoi1() {
        return beforePayRoi1;
    }

    public void setBeforePayRoi1(String beforePayRoi1) {
        this.beforePayRoi1 = beforePayRoi1;
    }

    public String getRevenueRate1() {
        return revenueRate1;
    }

    public void setRevenueRate1(String revenueRate1) {
        this.revenueRate1 = revenueRate1;
    }

    public String getPayRevenueRate1() {
        return payRevenueRate1;
    }

    public void setPayRevenueRate1(String payRevenueRate1) {
        this.payRevenueRate1 = payRevenueRate1;
    }

    public Double getRecoveryRevenue() {
        return recoveryRevenue;
    }

    public void setRecoveryRevenue(Double recoveryRevenue) {
        this.recoveryRevenue = recoveryRevenue;
    }

    public Double getRecoveryPayRevenue() {
        return recoveryPayRevenue;
    }

    public void setRecoveryPayRevenue(Double recoveryPayRevenue) {
        this.recoveryPayRevenue = recoveryPayRevenue;
    }

    public Double getRevenue() {
        return revenue;
    }

    public void setRevenue(Double revenue) {
        this.revenue = revenue;
    }

    public String getPayRevenue() {
        return payRevenue;
    }

    public void setPayRevenue(String payRevenue) {
        this.payRevenue = payRevenue;
    }

    public String getPayRoi3() {
        return payRoi3;
    }

    public void setPayRoi3(String payRoi3) {
        this.payRoi3 = payRoi3;
    }

    public String getPayRoi7() {
        return payRoi7;
    }

    public void setPayRoi7(String payRoi7) {
        this.payRoi7 = payRoi7;
    }

    public String getPayRoi30() {
        return payRoi30;
    }

    public void setPayRoi30(String payRoi30) {
        this.payRoi30 = payRoi30;
    }

    public String getTotalRoi1() {
        return totalRoi1;
    }

    public void setTotalRoi1(String totalRoi1) {
        this.totalRoi1 = totalRoi1;
    }

    public String getTotalRoi3() {
        return totalRoi3;
    }

    public void setTotalRoi3(String totalRoi3) {
        this.totalRoi3 = totalRoi3;
    }

    public String getTotalRoi7() {
        return totalRoi7;
    }

    public void setTotalRoi7(String totalRoi7) {
        this.totalRoi7 = totalRoi7;
    }

    public String getTotalRoi30() {
        return totalRoi30;
    }

    public void setTotalRoi30(String totalRoi30) {
        this.totalRoi30 = totalRoi30;
    }

    @Override
    public String getApp() {
        return app;
    }

    @Override
    public void setApp(String app) {
        this.app = app;
    }

    @Override
    public String getAppCategory() {
        return appCategory;
    }

    @Override
    public void setAppCategory(String appCategory) {
        this.appCategory = appCategory;
    }

    @Override
    public String getAppId() {
        return appId;
    }

    @Override
    public void setAppId(String appId) {
        this.appId = appId;
    }

    @Override
    public String getAccount() {
        return account;
    }

    @Override
    public void setAccount(String account) {
        this.account = account;
    }

    @Override
    public String getAccountRemark() {
        return accountRemark;
    }

    @Override
    public void setAccountRemark(String accountRemark) {
        this.accountRemark = accountRemark;
    }

    public Double getInstallsLtv1() {
        return installsLtv1;
    }

    public void setInstallsLtv1(Double installsLtv1) {
        this.installsLtv1 = installsLtv1;
    }

    public Double getInstallsLtv3() {
        return installsLtv3;
    }

    public void setInstallsLtv3(Double installsLtv3) {
        this.installsLtv3 = installsLtv3;
    }

    public Double getInstallsLtv7() {
        return installsLtv7;
    }

    public void setInstallsLtv7(Double installsLtv7) {
        this.installsLtv7 = installsLtv7;
    }

    public Double getInstallsLtv30() {
        return installsLtv30;
    }

    public void setInstallsLtv30(Double installsLtv30) {
        this.installsLtv30 = installsLtv30;
    }

    public Double getRegisterLtv1() {
        return registerLtv1;
    }

    public void setRegisterLtv1(Double registerLtv1) {
        this.registerLtv1 = registerLtv1;
    }

    public Double getRegisterLtv3() {
        return registerLtv3;
    }

    public void setRegisterLtv3(Double registerLtv3) {
        this.registerLtv3 = registerLtv3;
    }

    public Double getRegisterLtv7() {
        return registerLtv7;
    }

    public void setRegisterLtv7(Double registerLtv7) {
        this.registerLtv7 = registerLtv7;
    }

    public Double getRegisterLtv30() {
        return registerLtv30;
    }

    public void setRegisterLtv30(Double registerLtv30) {
        this.registerLtv30 = registerLtv30;
    }

    public Double getInstallsPayLtv1() {
        return installsPayLtv1;
    }

    public void setInstallsPayLtv1(Double installsPayLtv1) {
        this.installsPayLtv1 = installsPayLtv1;
    }

    public Double getInstallsPayLtv3() {
        return installsPayLtv3;
    }

    public void setInstallsPayLtv3(Double installsPayLtv3) {
        this.installsPayLtv3 = installsPayLtv3;
    }

    public Double getInstallsPayLtv7() {
        return installsPayLtv7;
    }

    public void setInstallsPayLtv7(Double installsPayLtv7) {
        this.installsPayLtv7 = installsPayLtv7;
    }

    public Double getInstallsPayLtv30() {
        return installsPayLtv30;
    }

    public void setInstallsPayLtv30(Double installsPayLtv30) {
        this.installsPayLtv30 = installsPayLtv30;
    }

    public Double getRegisterPayLtv1() {
        return registerPayLtv1;
    }

    public void setRegisterPayLtv1(Double registerPayLtv1) {
        this.registerPayLtv1 = registerPayLtv1;
    }

    public Double getRegisterPayLtv3() {
        return registerPayLtv3;
    }

    public void setRegisterPayLtv3(Double registerPayLtv3) {
        this.registerPayLtv3 = registerPayLtv3;
    }

    public Double getRegisterPayLtv7() {
        return registerPayLtv7;
    }

    public void setRegisterPayLtv7(Double registerPayLtv7) {
        this.registerPayLtv7 = registerPayLtv7;
    }

    public Double getRegisterPayLtv30() {
        return registerPayLtv30;
    }

    public void setRegisterPayLtv30(Double registerPayLtv30) {
        this.registerPayLtv30 = registerPayLtv30;
    }

    public Double getInstallsTotalLtv1() {
        return installsTotalLtv1;
    }

    public void setInstallsTotalLtv1(Double installsTotalLtv1) {
        this.installsTotalLtv1 = installsTotalLtv1;
    }

    public Double getInstallsTotalLtv3() {
        return installsTotalLtv3;
    }

    public void setInstallsTotalLtv3(Double installsTotalLtv3) {
        this.installsTotalLtv3 = installsTotalLtv3;
    }

    public Double getInstallsTotalLtv7() {
        return installsTotalLtv7;
    }

    public void setInstallsTotalLtv7(Double installsTotalLtv7) {
        this.installsTotalLtv7 = installsTotalLtv7;
    }

    public Double getInstallsTotalLtv30() {
        return installsTotalLtv30;
    }

    public void setInstallsTotalLtv30(Double installsTotalLtv30) {
        this.installsTotalLtv30 = installsTotalLtv30;
    }

    public Double getRegisterTotalLtv1() {
        return registerTotalLtv1;
    }

    public void setRegisterTotalLtv1(Double registerTotalLtv1) {
        this.registerTotalLtv1 = registerTotalLtv1;
    }

    public Double getRegisterTotalLtv3() {
        return registerTotalLtv3;
    }

    public void setRegisterTotalLtv3(Double registerTotalLtv3) {
        this.registerTotalLtv3 = registerTotalLtv3;
    }

    public Double getRegisterTotalLtv7() {
        return registerTotalLtv7;
    }

    public void setRegisterTotalLtv7(Double registerTotalLtv7) {
        this.registerTotalLtv7 = registerTotalLtv7;
    }

    public Double getRegisterTotalLtv30() {
        return registerTotalLtv30;
    }

    public void setRegisterTotalLtv30(Double registerTotalLtv30) {
        this.registerTotalLtv30 = registerTotalLtv30;
    }

    public String getInstallsGamePayRate() {
        return installsGamePayRate;
    }

    public void setInstallsGamePayRate(String installsGamePayRate) {
        this.installsGamePayRate = installsGamePayRate;
    }

    public String getRegisterGamePayRate() {
        return registerGamePayRate;
    }

    public void setRegisterGamePayRate(String registerGamePayRate) {
        this.registerGamePayRate = registerGamePayRate;
    }

    public String getInstallsActivePayRate() {
        return installsActivePayRate;
    }

    public void setInstallsActivePayRate(String installsActivePayRate) {
        this.installsActivePayRate = installsActivePayRate;
    }

    public String getRegisterActivePayRate() {
        return registerActivePayRate;
    }

    public void setRegisterActivePayRate(String registerActivePayRate) {
        this.registerActivePayRate = registerActivePayRate;
    }

    public String getInstallsAddPayRate() {
        return installsAddPayRate;
    }

    public void setInstallsAddPayRate(String installsAddPayRate) {
        this.installsAddPayRate = installsAddPayRate;
    }

    public String getRegisterAddPayRate() {
        return registerAddPayRate;
    }

    public void setRegisterAddPayRate(String registerAddPayRate) {
        this.registerAddPayRate = registerAddPayRate;
    }

    public String getBidType() {
        return bidType;
    }

    public void setBidType(String bidType) {
        this.bidType = bidType;
    }

    public String getSpendRate() {
        return spendRate;
    }

    public void setSpendRate(String spendRate) {
        this.spendRate = spendRate;
    }

    public String getOs_type() {
        return os_type;
    }

    public void setOs_type(String os_type) {
        this.os_type = os_type;
    }

    public String getDelivery_mode() {
        return delivery_mode;
    }

    public void setDelivery_mode(String delivery_mode) {
        this.delivery_mode = delivery_mode;
    }

    public String getBackActivateRate() {
        return backActivateRate;
    }

    public void setBackActivateRate(String backActivateRate) {
        this.backActivateRate = backActivateRate;
    }

    public Integer getBackActivateCount() {
        return backActivateCount;
    }

    public void setBackActivateCount(Integer backActivateCount) {
        this.backActivateCount = backActivateCount;
    }

    public Double getInstallsLtv14() {
        return installsLtv14;
    }

    public void setInstallsLtv14(Double installsLtv14) {
        this.installsLtv14 = installsLtv14;
    }

    public Double getRegisterLtv14() {
        return registerLtv14;
    }

    public void setRegisterLtv14(Double registerLtv14) {
        this.registerLtv14 = registerLtv14;
    }

    public String getRoi14() {
        return roi14;
    }

    public void setRoi14(String roi14) {
        this.roi14 = roi14;
    }

    public String getLt14() {
        return lt14;
    }

    public void setLt14(String lt14) {
        this.lt14 = lt14;
    }

    public Double getAdd_arpu_register() {
        return add_arpu_register;
    }

    public void setAdd_arpu_register(Double add_arpu_register) {
        this.add_arpu_register = add_arpu_register;
    }

    public Double getAdd_arpu_cash() {
        return add_arpu_cash;
    }

    public void setAdd_arpu_cash(Double add_arpu_cash) {
        this.add_arpu_cash = add_arpu_cash;
    }

    public String getAdv_perm_rate() {
        return adv_perm_rate;
    }

    public void setAdv_perm_rate(String adv_perm_rate) {
        this.adv_perm_rate = adv_perm_rate;
    }

    public String getRegister_cash_rate() {
        return register_cash_rate;
    }

    public void setRegister_cash_rate(String register_cash_rate) {
        this.register_cash_rate = register_cash_rate;
    }

    public Integer getFirst_cash_user() {
        return first_cash_user;
    }

    public void setFirst_cash_user(Integer first_cash_user) {
        this.first_cash_user = first_cash_user;
    }

    public Integer getCash_user() {
        return cash_user;
    }

    public void setCash_user(Integer cash_user) {
        this.cash_user = cash_user;
    }

    public String getRevenue_hour_roi24() {
        return revenue_hour_roi24;
    }

    public void setRevenue_hour_roi24(String revenue_hour_roi24) {
        this.revenue_hour_roi24 = revenue_hour_roi24;
    }

    public Double getRevenue_hour_24() {
        return revenue_hour_24;
    }

    public void setRevenue_hour_24(Double revenue_hour_24) {
        this.revenue_hour_24 = revenue_hour_24;
    }

    public String getPay_hour_roi24() {
        return pay_hour_roi24;
    }

    public void setPay_hour_roi24(String pay_hour_roi24) {
        this.pay_hour_roi24 = pay_hour_roi24;
    }

    public Double getPay_hour_24() {
        return pay_hour_24;
    }

    public void setPay_hour_24(Double pay_hour_24) {
        this.pay_hour_24 = pay_hour_24;
    }

    public Long getPay_hour_24_user() {
        return pay_hour_24_user;
    }

    public void setPay_hour_24_user(Long pay_hour_24_user) {
        this.pay_hour_24_user = pay_hour_24_user;
    }

    public Double getPay_hour_24_arpu() {
        return pay_hour_24_arpu;
    }

    public void setPay_hour_24_arpu(Double pay_hour_24_arpu) {
        this.pay_hour_24_arpu = pay_hour_24_arpu;
    }

    public String getCreative_template() {
        return creative_template;
    }

    public void setCreative_template(String creative_template) {
        this.creative_template = creative_template;
    }

    public String getTotalRoi14() {
        return totalRoi14;
    }

    public void setTotalRoi14(String totalRoi14) {
        this.totalRoi14 = totalRoi14;
    }

    public Double getTotalLt3() {
        return totalLt3;
    }

    public void setTotalLt3(Double totalLt3) {
        this.totalLt3 = totalLt3;
    }

    public Double getTotalLt7() {
        return totalLt7;
    }

    public void setTotalLt7(Double totalLt7) {
        this.totalLt7 = totalLt7;
    }

    public Double getTotalLt14() {
        return totalLt14;
    }

    public void setTotalLt14(Double totalLt14) {
        this.totalLt14 = totalLt14;
    }

    public Double getTotalLt30() {
        return totalLt30;
    }

    public void setTotalLt30(Double totalLt30) {
        this.totalLt30 = totalLt30;
    }

    public String getXingqi() {
        return xingqi;
    }

    public void setXingqi(String xingqi) {
        this.xingqi = xingqi;
    }

    public String getPackage_name() {
        return package_name;
    }

    public void setPackage_name(String package_name) {
        this.package_name = package_name;
    }

    public String getShop_id() {
        return shop_id;
    }

    public void setShop_id(String shop_id) {
        this.shop_id = shop_id;
    }

    public Double getDown_ltv1() {
        return down_ltv1;
    }

    public void setDown_ltv1(Double down_ltv1) {
        this.down_ltv1 = down_ltv1;
    }

    public String getLt30() {
        return lt30;
    }

    public void setLt30(String lt30) {
        this.lt30 = lt30;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public Double getClickLtv1() {
        return clickLtv1;
    }

    public void setClickLtv1(Double clickLtv1) {
        this.clickLtv1 = clickLtv1;
    }
}
