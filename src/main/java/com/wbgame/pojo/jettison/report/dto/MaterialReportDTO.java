package com.wbgame.pojo.jettison.report.dto;

import com.wbgame.aop.AppendPercentage;

public class MaterialReportDTO extends BaseReportDTO{

    private Integer id;

    private String day;             //查询日期

    private String fileName;        //文件名称

    private String format;          //文件格式

    private String type;            //文件类型

    private String createDay;       //素材生成日期

    private String signature;       //MD5

    private String ad_platform;     //媒体平台

    private String putUser;         //投手

    private String media;           //媒体

    private String channel;         //子渠道

    private String channelType;     //渠道类型

    private String artist;          //设计师

    private String artist3d;        //3d设计师

    private String creativist;      //创意设计师

    private String artist_real;     //真人

    private String product_part;     //制作参与人

    private String cover;

    private Long installs;          //买量新增

    private Long impressions;       //展示

    private Long clicks;            //点击

    private Long retention_day_1;   //次留数

    private Double spend;           //消耗

    private Double rebateSpend;     //返点消耗

    private Double click_cost;      //点击成本

    private Double impression_cost; //展示成本

    private Double install_cost;    //新增成本

    private String clickRate;       //点击率

    private String installRate;     //新增率

    private String retentionRate;   //次留率

    private String url;             //素材地址

    private Long active_user;       //热云活跃人数

    private Double revenue;         //收入

    private Double revenue1;        //首日收入

    private Double revenue3;        //7日收入

    private Double revenue7;        //7日收入

    private Double revenue14;       //14日收入

    private Long aplaque_video_show_times;//插屏展示次数

    private Double installs_arpu;   //新增arpu(和收入有关系)

    private Integer creative_num;   //创意次数

    private Integer first_pay_num;  //首日付费次数


    private Double ltv7;

    private Double ltv14;

    private Integer creative_times; //创意时长(视频专用)

    private Integer toutiao_times;  //头条展示次数

    private Integer kuaishou_times; //快手展示次数

    private Integer gdt_times;      //广点通展示次数

    private Long total_play;        //今天播放次数

    private Long valid_play;        //有效播放次数

    private Double valid_play_cost;//播放成本

    private String valid_play_rate;//播放率

    private Long play_25_feed_break;//25%进度播放数

    private Long play_50_feed_break;//50%进度播放数

    private Long play_75_feed_break;//75%进度播放数

    private Long play_100_feed_break;//100%进度播放数

    private Double average_play_time_per_play;//平均单次播放时长

    private String play_over_rate;//播完率

    private String wifi_play_rate;//WiFi播放占比

    private Long wifi_play;//WiFi播放量

    private Double play_time;//播放时长

    private Long card_show;//3秒卡片展现

    private Double duration;//视频时长

    private String play_25_feed_break_rate;//25%进度播放率

    private String play_50_feed_break_rate;//50%进度播放率

    private String play_75_feed_break_rate;//75%进度播放率

    private String play_100_feed_break_rate;//100%进度播放率

    private Double play_25_time;

    private Double play_50_time;

    private Double play_75_time;

    private String label1;

    private String label2;

    private String label3;

    private String scriptNum;

    private String specs;

    //大数据
    private Long add_user;//新增人数
    private Double ltv1;//收入
    private Double add_revenue_1;
    private Double add_revenue_2;
    private Double add_revenue_3;
    private Double add_revenue_4;
    private Double add_revenue_5;
    private Double add_revenue_6;
    private Double add_revenue_7;
    private Double add_revenue_14;
    private Double add_revenue_21;
    private Double add_revenue_28;
    private Double add_revenue_30;
    private Double add_revenue_42;
    private Double add_revenue_60;
    private Double add_revenue_90;
    private Double add_purchase_revenue_1;
    private Double add_purchase_revenue_2;
    private Double add_purchase_revenue_3;
    private Double add_purchase_revenue_4;
    private Double add_purchase_revenue_5;
    private Double add_purchase_revenue_6;
    private Double add_purchase_revenue_7;
    private Double add_purchase_revenue_14;
    private Double add_purchase_revenue_21;
    private Double add_purchase_revenue_28;
    private Double add_purchase_revenue_30;
    private Double add_purchase_revenue_42;
    private Double add_purchase_revenue_60;
    private Double add_purchase_revenue_90;
    private String roi1;
    private String roi2;
    private String roi3;
    private String roi4;
    private String roi5;
    private String roi6;
    private String roi7;
    private String roi14;
    private String roi21;
    private String roi28;
    private String roi30;
    private String roi42;
    private String roi60;
    private String roi90;
    @AppendPercentage
    private String pay_roi1;
    @AppendPercentage
    private String pay_roi3;
    @AppendPercentage
    private String pay_roi7;
    @AppendPercentage
    private String pay_roi14;
    @AppendPercentage
    private String pay_roi30;
    private Double video_ltv;//首日视频收入
    private Double plaque_ltv;//首日插屏收入
    private Long video_show_times;//视频展示次数
    private Long plaque_show_times;//插屏展示次数
    private String arpu;//人均收入
    private String videoEcpm;//视频ecpm
    private String plaqueEcpm;//插屏ecpm
    private String avgVideoShowTimes;//首日人均视频
    private String avgPlaqueShowTimes;//首日人均插屏
    private String cpa;//人均返点消耗
    private Long add_pay_users;//新增付费数
    private String addUserPayRate;//新增付费率
    private Double addUserPayArpu;//新增付费arpu
    private Double addUserPayCost;//新增付费成本
    //付费用户留存率1、2、3、7、14日
    private String pay_retain_1;
    private String pay_retain_2;
    private String pay_retain_3;
    private String pay_retain_7;
    private String pay_retain_14;
    private String pay_retain_30;

    //广点通
    private String gdt_roi1;
    private String gdt_roi3;
    private String gdt_roi7;
    private String gdt_roi14;
    private String gdt_roi30;
    private Long gdt_retention_day_1;
    private Long gdt_add_user;
    private Double gdt_ltv1;
    private String gdt_retentionRate;
    private Double gdt_arpu;
    private Double gdt_cpa;

    //快手
    private String video_play_2;//2s播放数

    private String video_play_3;//3s播放数

    private String video_play_5;//5s播放数

    private String video_play_10;//10s播放数

    private String kuaishou_feed_play_75;//75%进度播放数

    private String finish_video_play;//完播数

    private String kuaishou_play_num;//快手播放数

    //工具续订
    private Integer week_users;
    private Integer month_users;
    private Integer year_users;
    private Integer renew_users_1;
    private Integer renew_users_2;
    private Integer renew_users_3;
    private Integer renew_users_4;
    private Integer renew_users_5;
    private Integer renew_users_6;
    private String renew_user_rate_1;
    private String renew_user_rate_2;
    private String renew_user_rate_3;
    private String renew_user_rate_4;
    private String renew_user_rate_5;
    private String renew_user_rate_6;
    private String renew_lt1;
    private String renew_lt2;
    private String renew_lt3;
    private String renew_lt4;
    private String renew_lt5;
    private String renew_lt6;
    private String renew_roi1;
    private String renew_roi2;
    private String renew_roi3;
    private String renew_roi4;
    private String renew_roi5;
    private String renew_roi6;





    //付费信息
    private Double payRevenue1;   //首次付费金额
    private Integer gamePayCount; //付费次数
    private Integer payCount;     //首次付费次数
    private Double gamePayCost;   //付费成本（spend/gamePayCount)
    private Double payCost; //首次付费成本（spend/payCount)
    private String payRate; //首次付费率 (payCount/installs)
    //导出用
    private String app;
    private String appCategory;
    private String appId;
    private String account;

    private String component_url;

    private String component_id;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCreateDay() {
        return createDay;
    }

    public void setCreateDay(String createDay) {
        this.createDay = createDay;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getAd_platform() {
        return ad_platform;
    }

    public void setAd_platform(String ad_platform) {
        this.ad_platform = ad_platform;
    }

    public String getPutUser() {
        return putUser;
    }

    public void setPutUser(String putUser) {
        this.putUser = putUser;
    }

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getArtist() {
        return artist;
    }

    public void setArtist(String artist) {
        this.artist = artist;
    }

    public String getArtist3d() {
        return artist3d;
    }

    public void setArtist3d(String artist3d) {
        this.artist3d = artist3d;
    }

    public String getCreativist() {
        return creativist;
    }

    public void setCreativist(String creativist) {
        this.creativist = creativist;
    }

    public Long getInstalls() {
        return installs;
    }

    public void setInstalls(Long installs) {
        this.installs = installs;
    }

    public Long getImpressions() {
        return impressions;
    }

    public void setImpressions(Long impressions) {
        this.impressions = impressions;
    }

    public Long getClicks() {
        return clicks;
    }

    public void setClicks(Long clicks) {
        this.clicks = clicks;
    }

    public Long getRetention_day_1() {
        return retention_day_1;
    }

    public void setRetention_day_1(Long retention_day_1) {
        this.retention_day_1 = retention_day_1;
    }

    public Double getSpend() {
        return spend;
    }

    public void setSpend(Double spend) {
        this.spend = spend;
    }

    public Double getRebateSpend() {
        return rebateSpend;
    }

    public void setRebateSpend(Double rebateSpend) {
        this.rebateSpend = rebateSpend;
    }

    public Double getClick_cost() {
        return click_cost;
    }

    public void setClick_cost(Double click_cost) {
        this.click_cost = click_cost;
    }

    public Double getImpression_cost() {
        return impression_cost;
    }

    public void setImpression_cost(Double impression_cost) {
        this.impression_cost = impression_cost;
    }

    public Double getInstall_cost() {
        return install_cost;
    }

    public void setInstall_cost(Double install_cost) {
        this.install_cost = install_cost;
    }

    public String getClickRate() {
        return clickRate;
    }

    public void setClickRate(String clickRate) {
        this.clickRate = clickRate;
    }

    public String getInstallRate() {
        return installRate;
    }

    public void setInstallRate(String installRate) {
        this.installRate = installRate;
    }

    public String getRetentionRate() {
        return retentionRate;
    }

    public void setRetentionRate(String retentionRate) {
        this.retentionRate = retentionRate;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Long getActive_user() {
        return active_user;
    }

    public void setActive_user(Long active_user) {
        this.active_user = active_user;
    }

    public Double getRevenue() {
        return revenue;
    }

    public void setRevenue(Double revenue) {
        this.revenue = revenue;
    }

    public Double getRevenue1() {
        return revenue1;
    }

    public void setRevenue1(Double revenue1) {
        this.revenue1 = revenue1;
    }

    public Double getRevenue3() {
        return revenue3;
    }

    public void setRevenue3(Double revenue3) {
        this.revenue3 = revenue3;
    }

    public Double getRevenue7() {
        return revenue7;
    }

    public void setRevenue7(Double revenue7) {
        this.revenue7 = revenue7;
    }

    public Double getRevenue14() {
        return revenue14;
    }

    public void setRevenue14(Double revenue14) {
        this.revenue14 = revenue14;
    }

    public Long getAplaque_video_show_times() {
        return aplaque_video_show_times;
    }

    public void setAplaque_video_show_times(Long aplaque_video_show_times) {
        this.aplaque_video_show_times = aplaque_video_show_times;
    }

    public Double getInstalls_arpu() {
        return installs_arpu;
    }

    public void setInstalls_arpu(Double installs_arpu) {
        this.installs_arpu = installs_arpu;
    }


    public Double getLtv7() {
        return ltv7;
    }

    public void setLtv7(Double ltv7) {
        this.ltv7 = ltv7;
    }

    public Double getLtv14() {
        return ltv14;
    }

    public void setLtv14(Double ltv14) {
        this.ltv14 = ltv14;
    }

    public Integer getCreative_times() {
        return creative_times;
    }

    public void setCreative_times(Integer creative_times) {
        this.creative_times = creative_times;
    }

    public Integer getToutiao_times() {
        return toutiao_times;
    }

    public void setToutiao_times(Integer toutiao_times) {
        this.toutiao_times = toutiao_times;
    }

    public Integer getKuaishou_times() {
        return kuaishou_times;
    }

    public void setKuaishou_times(Integer kuaishou_times) {
        this.kuaishou_times = kuaishou_times;
    }

    public Integer getGdt_times() {
        return gdt_times;
    }

    public void setGdt_times(Integer gdt_times) {
        this.gdt_times = gdt_times;
    }

    public Long getTotal_play() {
        return total_play;
    }

    public void setTotal_play(Long total_play) {
        this.total_play = total_play;
    }

    public Long getValid_play() {
        return valid_play;
    }

    public void setValid_play(Long valid_play) {
        this.valid_play = valid_play;
    }

    public Double getValid_play_cost() {
        return valid_play_cost;
    }

    public void setValid_play_cost(Double valid_play_cost) {
        this.valid_play_cost = valid_play_cost;
    }

    public String getValid_play_rate() {
        return valid_play_rate;
    }

    public void setValid_play_rate(String valid_play_rate) {
        this.valid_play_rate = valid_play_rate;
    }

    public Long getPlay_25_feed_break() {
        return play_25_feed_break;
    }

    public void setPlay_25_feed_break(Long play_25_feed_break) {
        this.play_25_feed_break = play_25_feed_break;
    }

    public Long getPlay_50_feed_break() {
        return play_50_feed_break;
    }

    public void setPlay_50_feed_break(Long play_50_feed_break) {
        this.play_50_feed_break = play_50_feed_break;
    }

    public Long getPlay_75_feed_break() {
        return play_75_feed_break;
    }

    public void setPlay_75_feed_break(Long play_75_feed_break) {
        this.play_75_feed_break = play_75_feed_break;
    }

    public Long getPlay_100_feed_break() {
        return play_100_feed_break;
    }

    public void setPlay_100_feed_break(Long play_100_feed_break) {
        this.play_100_feed_break = play_100_feed_break;
    }

    public Double getAverage_play_time_per_play() {
        return average_play_time_per_play;
    }

    public void setAverage_play_time_per_play(Double average_play_time_per_play) {
        this.average_play_time_per_play = average_play_time_per_play;
    }

    public String getPlay_over_rate() {
        return play_over_rate;
    }

    public void setPlay_over_rate(String play_over_rate) {
        this.play_over_rate = play_over_rate;
    }

    public String getWifi_play_rate() {
        return wifi_play_rate;
    }

    public void setWifi_play_rate(String wifi_play_rate) {
        this.wifi_play_rate = wifi_play_rate;
    }

    public Long getWifi_play() {
        return wifi_play;
    }

    public void setWifi_play(Long wifi_play) {
        this.wifi_play = wifi_play;
    }

    public Double getPlay_time() {
        return play_time;
    }

    public void setPlay_time(Double play_time) {
        this.play_time = play_time;
    }

    public Long getCard_show() {
        return card_show;
    }

    public void setCard_show(Long card_show) {
        this.card_show = card_show;
    }

    public Double getDuration() {
        return duration;
    }

    public void setDuration(Double duration) {
        this.duration = duration;
    }

    public String getPlay_25_feed_break_rate() {
        return play_25_feed_break_rate;
    }

    public void setPlay_25_feed_break_rate(String play_25_feed_break_rate) {
        this.play_25_feed_break_rate = play_25_feed_break_rate;
    }

    public String getPlay_50_feed_break_rate() {
        return play_50_feed_break_rate;
    }

    public void setPlay_50_feed_break_rate(String play_50_feed_break_rate) {
        this.play_50_feed_break_rate = play_50_feed_break_rate;
    }

    public String getPlay_75_feed_break_rate() {
        return play_75_feed_break_rate;
    }

    public void setPlay_75_feed_break_rate(String play_75_feed_break_rate) {
        this.play_75_feed_break_rate = play_75_feed_break_rate;
    }

    public String getPlay_100_feed_break_rate() {
        return play_100_feed_break_rate;
    }

    public void setPlay_100_feed_break_rate(String play_100_feed_break_rate) {
        this.play_100_feed_break_rate = play_100_feed_break_rate;
    }

    public Double getPlay_25_time() {
        return play_25_time;
    }

    public void setPlay_25_time(Double play_25_time) {
        this.play_25_time = play_25_time;
    }

    public Double getPlay_50_time() {
        return play_50_time;
    }

    public void setPlay_50_time(Double play_50_time) {
        this.play_50_time = play_50_time;
    }

    public Double getPlay_75_time() {
        return play_75_time;
    }

    public void setPlay_75_time(Double play_75_time) {
        this.play_75_time = play_75_time;
    }

    public Long getAdd_user() {
        return add_user;
    }

    public void setAdd_user(Long add_user) {
        this.add_user = add_user;
    }

    public Double getLtv1() {
        return ltv1;
    }

    public void setLtv1(Double ltv1) {
        this.ltv1 = ltv1;
    }

    public Double getVideo_ltv() {
        return video_ltv;
    }

    public void setVideo_ltv(Double video_ltv) {
        this.video_ltv = video_ltv;
    }

    public Double getPlaque_ltv() {
        return plaque_ltv;
    }

    public void setPlaque_ltv(Double plaque_ltv) {
        this.plaque_ltv = plaque_ltv;
    }

    public Long getVideo_show_times() {
        return video_show_times;
    }

    public void setVideo_show_times(Long video_show_times) {
        this.video_show_times = video_show_times;
    }

    public Long getPlaque_show_times() {
        return plaque_show_times;
    }

    public void setPlaque_show_times(Long plaque_show_times) {
        this.plaque_show_times = plaque_show_times;
    }

    public String getArpu() {
        return arpu;
    }

    public void setArpu(String arpu) {
        this.arpu = arpu;
    }

    public String getVideoEcpm() {
        return videoEcpm;
    }

    public void setVideoEcpm(String videoEcpm) {
        this.videoEcpm = videoEcpm;
    }

    public String getPlaqueEcpm() {
        return plaqueEcpm;
    }

    public void setPlaqueEcpm(String plaqueEcpm) {
        this.plaqueEcpm = plaqueEcpm;
    }

    public String getAvgVideoShowTimes() {
        return avgVideoShowTimes;
    }

    public void setAvgVideoShowTimes(String avgVideoShowTimes) {
        this.avgVideoShowTimes = avgVideoShowTimes;
    }

    public String getAvgPlaqueShowTimes() {
        return avgPlaqueShowTimes;
    }

    public void setAvgPlaqueShowTimes(String avgPlaqueShowTimes) {
        this.avgPlaqueShowTimes = avgPlaqueShowTimes;
    }

    public String getCpa() {
        return cpa;
    }

    public void setCpa(String cpa) {
        this.cpa = cpa;
    }

    public Double getPayRevenue1() {
        return payRevenue1;
    }

    public void setPayRevenue1(Double payRevenue1) {
        this.payRevenue1 = payRevenue1;
    }

    public Integer getGamePayCount() {
        return gamePayCount;
    }

    public void setGamePayCount(Integer gamePayCount) {
        this.gamePayCount = gamePayCount;
    }

    public Integer getPayCount() {
        return payCount;
    }

    public void setPayCount(Integer payCount) {
        this.payCount = payCount;
    }

    public Double getGamePayCost() {
        return gamePayCost;
    }

    public void setGamePayCost(Double gamePayCost) {
        this.gamePayCost = gamePayCost;
    }

    public Double getPayCost() {
        return payCost;
    }

    public void setPayCost(Double payCost) {
        this.payCost = payCost;
    }

    public String getPayRate() {
        return payRate;
    }

    public void setPayRate(String payRate) {
        this.payRate = payRate;
    }

    @Override
    public String getApp() {
        return app;
    }

    @Override
    public void setApp(String app) {
        this.app = app;
    }

    @Override
    public String getAppCategory() {
        return appCategory;
    }

    @Override
    public void setAppCategory(String appCategory) {
        this.appCategory = appCategory;
    }

    @Override
    public String getAppId() {
        return appId;
    }

    @Override
    public void setAppId(String appId) {
        this.appId = appId;
    }

    @Override
    public String getAccount() {
        return account;
    }

    @Override
    public void setAccount(String account) {
        this.account = account;
    }

    public String getLabel1() {
        return label1;
    }

    public void setLabel1(String label1) {
        this.label1 = label1;
    }

    public String getLabel2() {
        return label2;
    }

    public void setLabel2(String label2) {
        this.label2 = label2;
    }

    public String getLabel3() {
        return label3;
    }

    public void setLabel3(String label3) {
        this.label3 = label3;
    }

    public Double getAdd_revenue_1() {
        return add_revenue_1;
    }

    public void setAdd_revenue_1(Double add_revenue_1) {
        this.add_revenue_1 = add_revenue_1;
    }

    public Double getAdd_revenue_2() {
        return add_revenue_2;
    }

    public void setAdd_revenue_2(Double add_revenue_2) {
        this.add_revenue_2 = add_revenue_2;
    }

    public Double getAdd_revenue_3() {
        return add_revenue_3;
    }

    public void setAdd_revenue_3(Double add_revenue_3) {
        this.add_revenue_3 = add_revenue_3;
    }

    public Double getAdd_revenue_4() {
        return add_revenue_4;
    }

    public void setAdd_revenue_4(Double add_revenue_4) {
        this.add_revenue_4 = add_revenue_4;
    }

    public Double getAdd_revenue_5() {
        return add_revenue_5;
    }

    public void setAdd_revenue_5(Double add_revenue_5) {
        this.add_revenue_5 = add_revenue_5;
    }

    public Double getAdd_revenue_6() {
        return add_revenue_6;
    }

    public void setAdd_revenue_6(Double add_revenue_6) {
        this.add_revenue_6 = add_revenue_6;
    }

    public Double getAdd_revenue_7() {
        return add_revenue_7;
    }

    public void setAdd_revenue_7(Double add_revenue_7) {
        this.add_revenue_7 = add_revenue_7;
    }

    public Double getAdd_revenue_14() {
        return add_revenue_14;
    }

    public void setAdd_revenue_14(Double add_revenue_14) {
        this.add_revenue_14 = add_revenue_14;
    }

    public Double getAdd_revenue_21() {
        return add_revenue_21;
    }

    public void setAdd_revenue_21(Double add_revenue_21) {
        this.add_revenue_21 = add_revenue_21;
    }

    public Double getAdd_revenue_28() {
        return add_revenue_28;
    }

    public void setAdd_revenue_28(Double add_revenue_28) {
        this.add_revenue_28 = add_revenue_28;
    }

    public Double getAdd_revenue_30() {
        return add_revenue_30;
    }

    public void setAdd_revenue_30(Double add_revenue_30) {
        this.add_revenue_30 = add_revenue_30;
    }

    public Double getAdd_purchase_revenue_1() {
        return add_purchase_revenue_1;
    }

    public void setAdd_purchase_revenue_1(Double add_purchase_revenue_1) {
        this.add_purchase_revenue_1 = add_purchase_revenue_1;
    }

    public Double getAdd_purchase_revenue_2() {
        return add_purchase_revenue_2;
    }

    public void setAdd_purchase_revenue_2(Double add_purchase_revenue_2) {
        this.add_purchase_revenue_2 = add_purchase_revenue_2;
    }

    public Double getAdd_purchase_revenue_3() {
        return add_purchase_revenue_3;
    }

    public void setAdd_purchase_revenue_3(Double add_purchase_revenue_3) {
        this.add_purchase_revenue_3 = add_purchase_revenue_3;
    }

    public Double getAdd_purchase_revenue_4() {
        return add_purchase_revenue_4;
    }

    public void setAdd_purchase_revenue_4(Double add_purchase_revenue_4) {
        this.add_purchase_revenue_4 = add_purchase_revenue_4;
    }

    public Double getAdd_purchase_revenue_5() {
        return add_purchase_revenue_5;
    }

    public void setAdd_purchase_revenue_5(Double add_purchase_revenue_5) {
        this.add_purchase_revenue_5 = add_purchase_revenue_5;
    }

    public Double getAdd_purchase_revenue_6() {
        return add_purchase_revenue_6;
    }

    public void setAdd_purchase_revenue_6(Double add_purchase_revenue_6) {
        this.add_purchase_revenue_6 = add_purchase_revenue_6;
    }

    public Double getAdd_purchase_revenue_7() {
        return add_purchase_revenue_7;
    }

    public void setAdd_purchase_revenue_7(Double add_purchase_revenue_7) {
        this.add_purchase_revenue_7 = add_purchase_revenue_7;
    }

    public Double getAdd_purchase_revenue_14() {
        return add_purchase_revenue_14;
    }

    public void setAdd_purchase_revenue_14(Double add_purchase_revenue_14) {
        this.add_purchase_revenue_14 = add_purchase_revenue_14;
    }

    public Double getAdd_purchase_revenue_21() {
        return add_purchase_revenue_21;
    }

    public void setAdd_purchase_revenue_21(Double add_purchase_revenue_21) {
        this.add_purchase_revenue_21 = add_purchase_revenue_21;
    }

    public Double getAdd_purchase_revenue_28() {
        return add_purchase_revenue_28;
    }

    public void setAdd_purchase_revenue_28(Double add_purchase_revenue_28) {
        this.add_purchase_revenue_28 = add_purchase_revenue_28;
    }

    public Double getAdd_purchase_revenue_30() {
        return add_purchase_revenue_30;
    }

    public void setAdd_purchase_revenue_30(Double add_purchase_revenue_30) {
        this.add_purchase_revenue_30 = add_purchase_revenue_30;
    }

    public String getRoi1() {
        return roi1;
    }

    public void setRoi1(String roi1) {
        this.roi1 = roi1;
    }

    public String getRoi2() {
        return roi2;
    }

    public void setRoi2(String roi2) {
        this.roi2 = roi2;
    }

    public String getRoi3() {
        return roi3;
    }

    public void setRoi3(String roi3) {
        this.roi3 = roi3;
    }

    public String getRoi4() {
        return roi4;
    }

    public void setRoi4(String roi4) {
        this.roi4 = roi4;
    }

    public String getRoi5() {
        return roi5;
    }

    public void setRoi5(String roi5) {
        this.roi5 = roi5;
    }

    public String getRoi6() {
        return roi6;
    }

    public void setRoi6(String roi6) {
        this.roi6 = roi6;
    }

    public String getRoi7() {
        return roi7;
    }

    public void setRoi7(String roi7) {
        this.roi7 = roi7;
    }

    public String getRoi14() {
        return roi14;
    }

    public void setRoi14(String roi14) {
        this.roi14 = roi14;
    }

    public String getRoi21() {
        return roi21;
    }

    public void setRoi21(String roi21) {
        this.roi21 = roi21;
    }

    public String getRoi28() {
        return roi28;
    }

    public void setRoi28(String roi28) {
        this.roi28 = roi28;
    }

    public String getRoi30() {
        return roi30;
    }

    public void setRoi30(String roi30) {
        this.roi30 = roi30;
    }

    public String getScriptNum() {
        return scriptNum;
    }

    public void setScriptNum(String scriptNum) {
        this.scriptNum = scriptNum;
    }

    public String getArtist_real() {
        return artist_real;
    }

    public void setArtist_real(String artist_real) {
        this.artist_real = artist_real;
    }

    public String getProduct_part() {
        return product_part;
    }

    public void setProduct_part(String product_part) {
        this.product_part = product_part;
    }

    public Long getAdd_pay_users() {
        return add_pay_users;
    }

    public void setAdd_pay_users(Long add_pay_users) {
        this.add_pay_users = add_pay_users;
    }

    public String getAddUserPayRate() {
        return addUserPayRate;
    }

    public void setAddUserPayRate(String addUserPayRate) {
        this.addUserPayRate = addUserPayRate;
    }

    public Double getAddUserPayArpu() {
        return addUserPayArpu;
    }

    public void setAddUserPayArpu(Double addUserPayArpu) {
        this.addUserPayArpu = addUserPayArpu;
    }

    public Double getAddUserPayCost() {
        return addUserPayCost;
    }

    public void setAddUserPayCost(Double addUserPayCost) {
        this.addUserPayCost = addUserPayCost;
    }

    public String getGdt_roi1() {
        return gdt_roi1;
    }

    public void setGdt_roi1(String gdt_roi1) {
        this.gdt_roi1 = gdt_roi1;
    }

    public String getGdt_roi3() {
        return gdt_roi3;
    }

    public void setGdt_roi3(String gdt_roi3) {
        this.gdt_roi3 = gdt_roi3;
    }

    public String getGdt_roi7() {
        return gdt_roi7;
    }

    public void setGdt_roi7(String gdt_roi7) {
        this.gdt_roi7 = gdt_roi7;
    }

    public String getGdt_roi14() {
        return gdt_roi14;
    }

    public void setGdt_roi14(String gdt_roi14) {
        this.gdt_roi14 = gdt_roi14;
    }

    public String getGdt_roi30() {
        return gdt_roi30;
    }

    public void setGdt_roi30(String gdt_roi30) {
        this.gdt_roi30 = gdt_roi30;
    }

    public Long getGdt_retention_day_1() {
        return gdt_retention_day_1;
    }

    public void setGdt_retention_day_1(Long gdt_retention_day_1) {
        this.gdt_retention_day_1 = gdt_retention_day_1;
    }

    public Long getGdt_add_user() {
        return gdt_add_user;
    }

    public void setGdt_add_user(Long gdt_add_user) {
        this.gdt_add_user = gdt_add_user;
    }

    public Double getGdt_ltv1() {
        return gdt_ltv1;
    }

    public void setGdt_ltv1(Double gdt_ltv1) {
        this.gdt_ltv1 = gdt_ltv1;
    }

    public String getGdt_retentionRate() {
        return gdt_retentionRate;
    }

    public void setGdt_retentionRate(String gdt_retentionRate) {
        this.gdt_retentionRate = gdt_retentionRate;
    }

    public Double getGdt_arpu() {
        return gdt_arpu;
    }

    public void setGdt_arpu(Double gdt_arpu) {
        this.gdt_arpu = gdt_arpu;
    }

    public Double getGdt_cpa() {
        return gdt_cpa;
    }

    public void setGdt_cpa(Double gdt_cpa) {
        this.gdt_cpa = gdt_cpa;
    }

    public String getPay_retain_1() {
        return pay_retain_1;
    }

    public void setPay_retain_1(String pay_retain_1) {
        this.pay_retain_1 = pay_retain_1;
    }

    public String getPay_retain_2() {
        return pay_retain_2;
    }

    public void setPay_retain_2(String pay_retain_2) {
        this.pay_retain_2 = pay_retain_2;
    }

    public String getPay_retain_3() {
        return pay_retain_3;
    }

    public void setPay_retain_3(String pay_retain_3) {
        this.pay_retain_3 = pay_retain_3;
    }

    public String getPay_retain_7() {
        return pay_retain_7;
    }

    public void setPay_retain_7(String pay_retain_7) {
        this.pay_retain_7 = pay_retain_7;
    }

    public String getPay_retain_14() {
        return pay_retain_14;
    }

    public void setPay_retain_14(String pay_retain_14) {
        this.pay_retain_14 = pay_retain_14;
    }

    public String getVideo_play_2() {
        return video_play_2;
    }

    public void setVideo_play_2(String video_play_2) {
        this.video_play_2 = video_play_2;
    }

    public String getVideo_play_3() {
        return video_play_3;
    }

    public void setVideo_play_3(String video_play_3) {
        this.video_play_3 = video_play_3;
    }

    public String getVideo_play_5() {
        return video_play_5;
    }

    public void setVideo_play_5(String video_play_5) {
        this.video_play_5 = video_play_5;
    }

    public String getVideo_play_10() {
        return video_play_10;
    }

    public void setVideo_play_10(String video_play_10) {
        this.video_play_10 = video_play_10;
    }

    public String getKuaishou_feed_play_75() {
        return kuaishou_feed_play_75;
    }

    public void setKuaishou_feed_play_75(String kuaishou_feed_play_75) {
        this.kuaishou_feed_play_75 = kuaishou_feed_play_75;
    }

    public String getFinish_video_play() {
        return finish_video_play;
    }

    public void setFinish_video_play(String finish_video_play) {
        this.finish_video_play = finish_video_play;
    }

    public String getKuaishou_play_num() {
        return kuaishou_play_num;
    }

    public void setKuaishou_play_num(String kuaishou_play_num) {
        this.kuaishou_play_num = kuaishou_play_num;
    }

    public Integer getCreative_num() {
        return creative_num;
    }

    public void setCreative_num(Integer creative_num) {
        this.creative_num = creative_num;
    }

    public Integer getFirst_pay_num() {
        return first_pay_num;
    }

    public void setFirst_pay_num(Integer first_pay_num) {
        this.first_pay_num = first_pay_num;
    }

    public Double getAdd_revenue_42() {
        return add_revenue_42;
    }

    public void setAdd_revenue_42(Double add_revenue_42) {
        this.add_revenue_42 = add_revenue_42;
    }

    public Double getAdd_revenue_60() {
        return add_revenue_60;
    }

    public void setAdd_revenue_60(Double add_revenue_60) {
        this.add_revenue_60 = add_revenue_60;
    }

    public Double getAdd_revenue_90() {
        return add_revenue_90;
    }

    public void setAdd_revenue_90(Double add_revenue_90) {
        this.add_revenue_90 = add_revenue_90;
    }

    public Double getAdd_purchase_revenue_42() {
        return add_purchase_revenue_42;
    }

    public void setAdd_purchase_revenue_42(Double add_purchase_revenue_42) {
        this.add_purchase_revenue_42 = add_purchase_revenue_42;
    }

    public Double getAdd_purchase_revenue_60() {
        return add_purchase_revenue_60;
    }

    public void setAdd_purchase_revenue_60(Double add_purchase_revenue_60) {
        this.add_purchase_revenue_60 = add_purchase_revenue_60;
    }

    public Double getAdd_purchase_revenue_90() {
        return add_purchase_revenue_90;
    }

    public void setAdd_purchase_revenue_90(Double add_purchase_revenue_90) {
        this.add_purchase_revenue_90 = add_purchase_revenue_90;
    }

    public String getRoi42() {
        return roi42;
    }

    public void setRoi42(String roi42) {
        this.roi42 = roi42;
    }

    public String getRoi60() {
        return roi60;
    }

    public void setRoi60(String roi60) {
        this.roi60 = roi60;
    }

    public String getRoi90() {
        return roi90;
    }

    public void setRoi90(String roi90) {
        this.roi90 = roi90;
    }

    public String getPay_retain_30() {
        return pay_retain_30;
    }

    public void setPay_retain_30(String pay_retain_30) {
        this.pay_retain_30 = pay_retain_30;
    }

    public String getSpecs() {
        return specs;
    }

    public void setSpecs(String specs) {
        this.specs = specs;
    }

    public Integer getWeek_users() {
        return week_users;
    }

    public void setWeek_users(Integer week_users) {
        this.week_users = week_users;
    }

    public Integer getMonth_users() {
        return month_users;
    }

    public void setMonth_users(Integer month_users) {
        this.month_users = month_users;
    }

    public Integer getYear_users() {
        return year_users;
    }

    public void setYear_users(Integer year_users) {
        this.year_users = year_users;
    }

    public Integer getRenew_users_1() {
        return renew_users_1;
    }

    public void setRenew_users_1(Integer renew_users_1) {
        this.renew_users_1 = renew_users_1;
    }

    public Integer getRenew_users_2() {
        return renew_users_2;
    }

    public void setRenew_users_2(Integer renew_users_2) {
        this.renew_users_2 = renew_users_2;
    }

    public Integer getRenew_users_3() {
        return renew_users_3;
    }

    public void setRenew_users_3(Integer renew_users_3) {
        this.renew_users_3 = renew_users_3;
    }

    public Integer getRenew_users_4() {
        return renew_users_4;
    }

    public void setRenew_users_4(Integer renew_users_4) {
        this.renew_users_4 = renew_users_4;
    }

    public Integer getRenew_users_5() {
        return renew_users_5;
    }

    public void setRenew_users_5(Integer renew_users_5) {
        this.renew_users_5 = renew_users_5;
    }

    public Integer getRenew_users_6() {
        return renew_users_6;
    }

    public void setRenew_users_6(Integer renew_users_6) {
        this.renew_users_6 = renew_users_6;
    }

    public String getRenew_user_rate_1() {
        return renew_user_rate_1;
    }

    public void setRenew_user_rate_1(String renew_user_rate_1) {
        this.renew_user_rate_1 = renew_user_rate_1;
    }

    public String getRenew_user_rate_2() {
        return renew_user_rate_2;
    }

    public void setRenew_user_rate_2(String renew_user_rate_2) {
        this.renew_user_rate_2 = renew_user_rate_2;
    }

    public String getRenew_user_rate_3() {
        return renew_user_rate_3;
    }

    public void setRenew_user_rate_3(String renew_user_rate_3) {
        this.renew_user_rate_3 = renew_user_rate_3;
    }

    public String getRenew_user_rate_4() {
        return renew_user_rate_4;
    }

    public void setRenew_user_rate_4(String renew_user_rate_4) {
        this.renew_user_rate_4 = renew_user_rate_4;
    }

    public String getRenew_user_rate_5() {
        return renew_user_rate_5;
    }

    public void setRenew_user_rate_5(String renew_user_rate_5) {
        this.renew_user_rate_5 = renew_user_rate_5;
    }

    public String getRenew_user_rate_6() {
        return renew_user_rate_6;
    }

    public void setRenew_user_rate_6(String renew_user_rate_6) {
        this.renew_user_rate_6 = renew_user_rate_6;
    }

    public String getRenew_lt1() {
        return renew_lt1;
    }

    public void setRenew_lt1(String renew_lt1) {
        this.renew_lt1 = renew_lt1;
    }

    public String getRenew_lt2() {
        return renew_lt2;
    }

    public void setRenew_lt2(String renew_lt2) {
        this.renew_lt2 = renew_lt2;
    }

    public String getRenew_lt3() {
        return renew_lt3;
    }

    public void setRenew_lt3(String renew_lt3) {
        this.renew_lt3 = renew_lt3;
    }

    public String getRenew_lt4() {
        return renew_lt4;
    }

    public void setRenew_lt4(String renew_lt4) {
        this.renew_lt4 = renew_lt4;
    }

    public String getRenew_lt5() {
        return renew_lt5;
    }

    public void setRenew_lt5(String renew_lt5) {
        this.renew_lt5 = renew_lt5;
    }

    public String getRenew_lt6() {
        return renew_lt6;
    }

    public void setRenew_lt6(String renew_lt6) {
        this.renew_lt6 = renew_lt6;
    }

    public String getRenew_roi1() {
        return renew_roi1;
    }

    public void setRenew_roi1(String renew_roi1) {
        this.renew_roi1 = renew_roi1;
    }

    public String getRenew_roi2() {
        return renew_roi2;
    }

    public void setRenew_roi2(String renew_roi2) {
        this.renew_roi2 = renew_roi2;
    }

    public String getRenew_roi3() {
        return renew_roi3;
    }

    public void setRenew_roi3(String renew_roi3) {
        this.renew_roi3 = renew_roi3;
    }

    public String getRenew_roi4() {
        return renew_roi4;
    }

    public void setRenew_roi4(String renew_roi4) {
        this.renew_roi4 = renew_roi4;
    }

    public String getRenew_roi5() {
        return renew_roi5;
    }

    public void setRenew_roi5(String renew_roi5) {
        this.renew_roi5 = renew_roi5;
    }

    public String getRenew_roi6() {
        return renew_roi6;
    }

    public void setRenew_roi6(String renew_roi6) {
        this.renew_roi6 = renew_roi6;
    }

    public String getComponent_url() {
        return component_url;
    }

    public void setComponent_url(String component_url) {
        this.component_url = component_url;
    }

    public String getComponent_id() {
        return component_id;
    }

    public void setComponent_id(String component_id) {
        this.component_id = component_id;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getPay_roi1() {
        return pay_roi1;
    }

    public void setPay_roi1(String pay_roi1) {
        this.pay_roi1 = pay_roi1;
    }

    public String getPay_roi3() {
        return pay_roi3;
    }

    public void setPay_roi3(String pay_roi3) {
        this.pay_roi3 = pay_roi3;
    }

    public String getPay_roi7() {
        return pay_roi7;
    }

    public void setPay_roi7(String pay_roi7) {
        this.pay_roi7 = pay_roi7;
    }

    public String getPay_roi30() {
        return pay_roi30;
    }

    public void setPay_roi30(String pay_roi30) {
        this.pay_roi30 = pay_roi30;
    }

    public String getPay_roi14() {
        return pay_roi14;
    }

    public void setPay_roi14(String pay_roi14) {
        this.pay_roi14 = pay_roi14;
    }
}
