package com.wbgame.pojo.jettison.vo;

import java.io.Serializable;


/**
 * @Author: xugx
 * @Description: 媒体创意Vo
 * @Date: created in 2021/11/30/15:34
 */
public class MediaCreativeVo  implements  Serializable {
	  
	/**
	 * 
	 */
	private static final long serialVersionUID = -4394581970904304388L;
	/**
	 * 日期
	 */
	private String day;
	/**
	 * 应用ID
	 */
	private String appId;
	/**
	 * 应用名称
	 */
	private String appName;
	/**
	 * 媒体
	 */
	private String media; 
	/**
	 *  投放子渠道
	 */
	private String channel; 
	/**
	 * 投放人员
	 */
	private String putUser;
	/**
	 * 美术人员
	 */
	private String artist;
	/**
	 * 媒体账号ID
	 */
	private String accountId;
	/**
	 *创意ID
	 */
	private String creativeId;
	/**
	 *渠道类型
	 */
	private String channelType;
	/**
	 * 消耗
	 */
	private Double spend;
	/**
	 * 新增用户数
	 */
	private Integer addUser;
	/**
	 *cpa
	 */
	private String cpa;
	/**
	 *首日roi
	 */
	private String roi;
	/**
	 * 首日收入
	 */
	private double ltv1;
	/**
	 * 1日留存率
	 */
	private String retentionOneDayRate;
	/**
	 *新增arpu
	 */
	private String arpu;
	/**
	 * 视频ecpm
	 */
	private String videoEcpm;
	/**
	 * 插屏ecpm
	 */
	private String plaqueEcpm;
	/**
	 * 首日-人均视频
	 */
	private double avgVideoShowTimes;
	/**
	 * 首日-人均插屏
	 */
	private double avgPlaqueShowTimes;
	/**
	 * 转化率
	 */
	private String installRate;
	/**
	 * 点击率
	 */
	private String clickRate;
	/**
	 *千次展示成本
	 */
	private String avgShowSpend;
	
	/**
	 *首日计费收入
	 */
	private double ipaIncome;
	/**
	 *首日计费+变现收入
	 */
	private double firstDayIncome;
	/**
	 * 首日ROI（计费）
	 */
	private String ipaRoi;
	
	/**
	 * 首日ROI（变现+计费）
	 */
	private String firstDayRoi;
	/**
	 * 新增用户付费数
	 */
	private int  add_purchase_users;
	/**
	 *活跃用户付费数
	 */
	private int  active_purchase_users;
	
	/**
	 * 2-30日留存率
	 */
	private String rd2;
	private String rd3;
	private String rd4;
	private String rd5;
	private String rd6;
	private String rd7;
	private String rd8;
	private String rd9;
	private String rd10;
	private String rd11;
	private String rd12;
	private String rd13;
	private String rd14;
	private String rd15;
	private String rd16;
	private String rd17;
	private String rd18;
	private String rd19;
	private String rd20;
	private String rd21;
	private String rd22;
	private String rd23;
	private String rd24;
	private String rd25;
	private String rd26;
	private String rd27;
	private String rd28;
	private String rd29;
	private String rd30;
	/**
	 * 付费用户2-30日留存率
	 */
	private String ipaRd1;
	private String ipaRd2;
	private String ipaRd3;
	private String ipaRd4;
	private String ipaRd5;
	private String ipaRd6;
	private String ipaRd7;
	private String ipaRd8;
	private String ipaRd9;
	private String ipaRd10;
	private String ipaRd11;
	private String ipaRd12;
	private String ipaRd13;
	private String ipaRd14;
	private String ipaRd15;
	private String ipaRd16;
	private String ipaRd17;
	private String ipaRd18;
	private String ipaRd19;
	private String ipaRd20;
	private String ipaRd21;
	private String ipaRd22;
	private String ipaRd23;
	private String ipaRd24;
	private String ipaRd25;
	private String ipaRd26;
	private String ipaRd27;
	private String ipaRd28;
	private String ipaRd29;
	private String ipaRd30;
	/**
	 *roi2-30
	 */
	private String roi2;
	private String roi3;
	private String roi4;
	private String roi5;
	private String roi6;
	private String roi7;
	private String roi8;
	private String roi9;
	private String roi10;
	private String roi11;
	private String roi12;
	private String roi13;
	private String roi14;
	private String roi15;
	private String roi16;
	private String roi17;
	private String roi18;
	private String roi19;
	private String roi20;
	private String roi21;
	private String roi22;
	private String roi23;
	private String roi24;
	private String roi25;
	private String roi26;
	private String roi27;
	private String roi28;
	private String roi29;
	private String roi30;
	/**
	 *ltv2-30
	 */
	private String ltv2;
	private String ltv3;
	private String ltv4;
	private String ltv5;
	private String ltv6;
	private String ltv7;
	private String ltv8;
	private String ltv9;
	private String ltv10;
	private String ltv11;
	private String ltv12;
	private String ltv13;
	private String ltv14;
	private String ltv15;
	private String ltv16;
	private String ltv17;
	private String ltv18;
	private String ltv19;
	private String ltv20;
	private String ltv21;
	private String ltv22;
	private String ltv23;
	private String ltv24;
	private String ltv25;
	private String ltv26;
	private String ltv27;
	private String ltv28;
	private String ltv29;
	private String ltv30;
	/**
	 *首次付费成本
	 */
	private String firstChargeCost;
	/**
	 *首次付费数
	 */
	private String firstPayCount;
	/**
	 *首次付费率
	 */
	private String firstPaymentRate;
	/**
	 *付费成本
	 */
	private String payCost;
	/**
	 *付费次数 
	 */
	private String paymentTimes;
	
	public String getLtv2() {
		return ltv2;
	}

	public void setLtv2(String ltv2) {
		this.ltv2 = ltv2;
	}

	public String getLtv3() {
		return ltv3;
	}

	public void setLtv3(String ltv3) {
		this.ltv3 = ltv3;
	}

	public String getLtv4() {
		return ltv4;
	}

	public void setLtv4(String ltv4) {
		this.ltv4 = ltv4;
	}

	public String getLtv5() {
		return ltv5;
	}

	public void setLtv5(String ltv5) {
		this.ltv5 = ltv5;
	}

	public String getLtv6() {
		return ltv6;
	}

	public void setLtv6(String ltv6) {
		this.ltv6 = ltv6;
	}

	public String getLtv7() {
		return ltv7;
	}

	public void setLtv7(String ltv7) {
		this.ltv7 = ltv7;
	}

	public String getLtv8() {
		return ltv8;
	}

	public void setLtv8(String ltv8) {
		this.ltv8 = ltv8;
	}

	public String getLtv9() {
		return ltv9;
	}

	public void setLtv9(String ltv9) {
		this.ltv9 = ltv9;
	}

	public String getLtv10() {
		return ltv10;
	}

	public void setLtv10(String ltv10) {
		this.ltv10 = ltv10;
	}

	public String getLtv11() {
		return ltv11;
	}

	public void setLtv11(String ltv11) {
		this.ltv11 = ltv11;
	}

	public String getLtv12() {
		return ltv12;
	}

	public void setLtv12(String ltv12) {
		this.ltv12 = ltv12;
	}

	public String getLtv13() {
		return ltv13;
	}

	public void setLtv13(String ltv13) {
		this.ltv13 = ltv13;
	}

	public String getLtv14() {
		return ltv14;
	}

	public void setLtv14(String ltv14) {
		this.ltv14 = ltv14;
	}

	public String getLtv15() {
		return ltv15;
	}

	public void setLtv15(String ltv15) {
		this.ltv15 = ltv15;
	}

	public String getLtv16() {
		return ltv16;
	}

	public void setLtv16(String ltv16) {
		this.ltv16 = ltv16;
	}

	public String getLtv17() {
		return ltv17;
	}

	public void setLtv17(String ltv17) {
		this.ltv17 = ltv17;
	}

	public String getLtv18() {
		return ltv18;
	}

	public void setLtv18(String ltv18) {
		this.ltv18 = ltv18;
	}

	public String getLtv19() {
		return ltv19;
	}

	public void setLtv19(String ltv19) {
		this.ltv19 = ltv19;
	}

	public String getLtv20() {
		return ltv20;
	}

	public void setLtv20(String ltv20) {
		this.ltv20 = ltv20;
	}

	public String getLtv21() {
		return ltv21;
	}

	public void setLtv21(String ltv21) {
		this.ltv21 = ltv21;
	}

	public String getLtv22() {
		return ltv22;
	}

	public void setLtv22(String ltv22) {
		this.ltv22 = ltv22;
	}

	public String getLtv23() {
		return ltv23;
	}

	public void setLtv23(String ltv23) {
		this.ltv23 = ltv23;
	}

	public String getLtv24() {
		return ltv24;
	}

	public void setLtv24(String ltv24) {
		this.ltv24 = ltv24;
	}

	public String getLtv25() {
		return ltv25;
	}

	public void setLtv25(String ltv25) {
		this.ltv25 = ltv25;
	}

	public String getLtv26() {
		return ltv26;
	}

	public void setLtv26(String ltv26) {
		this.ltv26 = ltv26;
	}

	public String getLtv27() {
		return ltv27;
	}

	public void setLtv27(String ltv27) {
		this.ltv27 = ltv27;
	}

	public String getLtv28() {
		return ltv28;
	}

	public void setLtv28(String ltv28) {
		this.ltv28 = ltv28;
	}

	public String getLtv29() {
		return ltv29;
	}

	public void setLtv29(String ltv29) {
		this.ltv29 = ltv29;
	}

	public String getLtv30() {
		return ltv30;
	}

	public void setLtv30(String ltv30) {
		this.ltv30 = ltv30;
	}

	public String getIpaRd1() {
		return ipaRd1;
	}

	public void setIpaRd1(String ipaRd1) {
		this.ipaRd1 = ipaRd1;
	}

	public String getFirstChargeCost() {
		return firstChargeCost;
	}

	public void setFirstChargeCost(String firstChargeCost) {
		this.firstChargeCost = firstChargeCost;
	}

	public String getFirstPayCount() {
		return firstPayCount;
	}

	public void setFirstPayCount(String firstPayCount) {
		this.firstPayCount = firstPayCount;
	}

	public String getFirstPaymentRate() {
		return firstPaymentRate;
	}

	public void setFirstPaymentRate(String firstPaymentRate) {
		this.firstPaymentRate = firstPaymentRate;
	}

	public String getPayCost() {
		return payCost;
	}

	public void setPayCost(String payCost) {
		this.payCost = payCost;
	}

	public String getPaymentTimes() {
		return paymentTimes;
	}

	public void setPaymentTimes(String paymentTimes) {
		this.paymentTimes = paymentTimes;
	}

	public double getIpaIncome() {
		return ipaIncome;
	}

	public void setIpaIncome(double ipaIncome) {
		this.ipaIncome = ipaIncome;
	}

	public double getFirstDayIncome() {
		return firstDayIncome;
	}

	public void setFirstDayIncome(double firstDayIncome) {
		this.firstDayIncome = firstDayIncome;
	}

	public String getIpaRoi() {
		return ipaRoi;
	}

	public void setIpaRoi(String ipaRoi) {
		this.ipaRoi = ipaRoi;
	}

	public String getFirstDayRoi() {
		return firstDayRoi;
	}

	public void setFirstDayRoi(String firstDayRoi) {
		this.firstDayRoi = firstDayRoi;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	public MediaCreativeVo(String appId, String media, String channel, String day, String accountId, String putUser,
			String artist, String creativeId,Double spend) {
		super();
		this.appId = appId;
		this.media = media;
		this.channel = channel;
		this.day = day;
		this.accountId = accountId;
		this.putUser = putUser;
		this.artist = artist;
		this.creativeId = creativeId;
		this.spend = spend;
	}

	public MediaCreativeVo() {
		super();
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getMedia() {
		return media;
	}

	public void setMedia(String media) {
		this.media = media;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getDay() {
		return day;
	}

	public void setDay(String day) {
		this.day = day;
	}

	public String getAccountId() {
		return accountId;
	}

	public String getArpu() {
		return arpu;
	}

	public void setArpu(String arpu) {
		this.arpu = arpu;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public String getPutUser() {
		return putUser;
	}

	public void setPutUser(String putUser) {
		this.putUser = putUser;
	}

	public String getArtist() {
		return artist;
	}

	public void setArtist(String artist) {
		this.artist = artist;
	}

	public String getCreativeId() {
		return creativeId;
	}

	public void setCreativeId(String creativeId) {
		this.creativeId = creativeId;
	}

	public Integer getAddUser() {
		return addUser;
	}

	public void setAddUser(Integer addUser) {
		this.addUser = addUser;
	}

	public String getRetentionOneDayRate() {
		return retentionOneDayRate;
	}

	public void setRetentionOneDayRate(String retentionOneDayRate) {
		this.retentionOneDayRate = retentionOneDayRate;
	}

	public Double getSpend() {
		return spend;
	}

	public void setSpend(Double spend) {
		this.spend = spend;
	}

	public String getInstallRate() {
		return installRate;
	}

	public void setInstallRate(String installRate) {
		this.installRate = installRate;
	}

	public String getClickRate() {
		return clickRate;
	}

	public void setClickRate(String clickRate) {
		this.clickRate = clickRate;
	}

	public double getLtv1() {
		return ltv1;
	}

	public void setLtv1(double ltv1) {
		this.ltv1 = ltv1;
	}

	public String getVideoEcpm() {
		return videoEcpm;
	}

	public void setVideoEcpm(String videoEcpm) {
		this.videoEcpm = videoEcpm;
	}

	public String getPlaqueEcpm() {
		return plaqueEcpm;
	}

	public void setPlaqueEcpm(String plaqueEcpm) {
		this.plaqueEcpm = plaqueEcpm;
	}

	public double getAvgVideoShowTimes() {
		return avgVideoShowTimes;
	}

	public void setAvgVideoShowTimes(double avgVideoShowTimes) {
		this.avgVideoShowTimes = avgVideoShowTimes;
	}

	public double getAvgPlaqueShowTimes() {
		return avgPlaqueShowTimes;
	}

	public void setAvgPlaqueShowTimes(double avgPlaqueShowTimes) {
		this.avgPlaqueShowTimes = avgPlaqueShowTimes;
	}

	public String getRoi() {
		return roi;
	}

	public void setRoi(String roi) {
		this.roi = roi;
	}

	public String getCpa() {
		return cpa;
	}

	public void setCpa(String cpa) {
		this.cpa = cpa;
	}

	public String getAvgShowSpend() {
		return avgShowSpend;
	}

	public void setAvgShowSpend(String avgShowSpend) {
		this.avgShowSpend = avgShowSpend;
	}

	public String getRd2() {
		return rd2;
	}

	public void setRd2(String rd2) {
		this.rd2 = rd2;
	}

	public String getRd3() {
		return rd3;
	}

	public void setRd3(String rd3) {
		this.rd3 = rd3;
	}

	public String getRd4() {
		return rd4;
	}

	public void setRd4(String rd4) {
		this.rd4 = rd4;
	}

	public String getRd5() {
		return rd5;
	}

	public void setRd5(String rd5) {
		this.rd5 = rd5;
	}

	public String getRd6() {
		return rd6;
	}

	public void setRd6(String rd6) {
		this.rd6 = rd6;
	}

	public String getRd7() {
		return rd7;
	}

	public void setRd7(String rd7) {
		this.rd7 = rd7;
	}

	public String getRd8() {
		return rd8;
	}

	public void setRd8(String rd8) {
		this.rd8 = rd8;
	}

	public String getRd9() {
		return rd9;
	}

	public void setRd9(String rd9) {
		this.rd9 = rd9;
	}

	public String getRd10() {
		return rd10;
	}

	public void setRd10(String rd10) {
		this.rd10 = rd10;
	}

	public String getRd11() {
		return rd11;
	}

	public void setRd11(String rd11) {
		this.rd11 = rd11;
	}

	public String getRd12() {
		return rd12;
	}

	public void setRd12(String rd12) {
		this.rd12 = rd12;
	}

	public String getRd13() {
		return rd13;
	}

	public void setRd13(String rd13) {
		this.rd13 = rd13;
	}

	public String getRd14() {
		return rd14;
	}

	public void setRd14(String rd14) {
		this.rd14 = rd14;
	}

	public String getRd15() {
		return rd15;
	}

	public void setRd15(String rd15) {
		this.rd15 = rd15;
	}

	public String getRd16() {
		return rd16;
	}

	public void setRd16(String rd16) {
		this.rd16 = rd16;
	}

	public String getRd17() {
		return rd17;
	}

	public void setRd17(String rd17) {
		this.rd17 = rd17;
	}

	public String getRd18() {
		return rd18;
	}

	public void setRd18(String rd18) {
		this.rd18 = rd18;
	}

	public String getRd19() {
		return rd19;
	}

	public void setRd19(String rd19) {
		this.rd19 = rd19;
	}

	public String getRd20() {
		return rd20;
	}

	public void setRd20(String rd20) {
		this.rd20 = rd20;
	}

	public String getRd21() {
		return rd21;
	}

	public void setRd21(String rd21) {
		this.rd21 = rd21;
	}

	public String getRd22() {
		return rd22;
	}

	public void setRd22(String rd22) {
		this.rd22 = rd22;
	}

	public String getRd23() {
		return rd23;
	}

	public void setRd23(String rd23) {
		this.rd23 = rd23;
	}

	public String getRd24() {
		return rd24;
	}

	public void setRd24(String rd24) {
		this.rd24 = rd24;
	}

	public String getRd25() {
		return rd25;
	}

	public void setRd25(String rd25) {
		this.rd25 = rd25;
	}

	public String getRd26() {
		return rd26;
	}

	public void setRd26(String rd26) {
		this.rd26 = rd26;
	}

	public String getRd27() {
		return rd27;
	}

	public void setRd27(String rd27) {
		this.rd27 = rd27;
	}

	public String getRd28() {
		return rd28;
	}

	public void setRd28(String rd28) {
		this.rd28 = rd28;
	}

	public String getRd29() {
		return rd29;
	}

	public void setRd29(String rd29) {
		this.rd29 = rd29;
	}

	public String getRd30() {
		return rd30;
	}

	public void setRd30(String rd30) {
		this.rd30 = rd30;
	}

	public String getIpaRd2() {
		return ipaRd2;
	}

	public void setIpaRd2(String ipaRd2) {
		this.ipaRd2 = ipaRd2;
	}

	public String getIpaRd3() {
		return ipaRd3;
	}

	public void setIpaRd3(String ipaRd3) {
		this.ipaRd3 = ipaRd3;
	}

	public String getIpaRd4() {
		return ipaRd4;
	}

	public void setIpaRd4(String ipaRd4) {
		this.ipaRd4 = ipaRd4;
	}

	public String getIpaRd5() {
		return ipaRd5;
	}

	public void setIpaRd5(String ipaRd5) {
		this.ipaRd5 = ipaRd5;
	}

	public String getIpaRd6() {
		return ipaRd6;
	}

	public void setIpaRd6(String ipaRd6) {
		this.ipaRd6 = ipaRd6;
	}

	public String getIpaRd7() {
		return ipaRd7;
	}

	public void setIpaRd7(String ipaRd7) {
		this.ipaRd7 = ipaRd7;
	}

	public String getIpaRd8() {
		return ipaRd8;
	}

	public void setIpaRd8(String ipaRd8) {
		this.ipaRd8 = ipaRd8;
	}

	public String getIpaRd9() {
		return ipaRd9;
	}

	public void setIpaRd9(String ipaRd9) {
		this.ipaRd9 = ipaRd9;
	}

	public String getIpaRd10() {
		return ipaRd10;
	}

	public void setIpaRd10(String ipaRd10) {
		this.ipaRd10 = ipaRd10;
	}

	public String getIpaRd11() {
		return ipaRd11;
	}

	public void setIpaRd11(String ipaRd11) {
		this.ipaRd11 = ipaRd11;
	}

	public String getIpaRd12() {
		return ipaRd12;
	}

	public void setIpaRd12(String ipaRd12) {
		this.ipaRd12 = ipaRd12;
	}

	public String getIpaRd13() {
		return ipaRd13;
	}

	public void setIpaRd13(String ipaRd13) {
		this.ipaRd13 = ipaRd13;
	}

	public String getIpaRd14() {
		return ipaRd14;
	}

	public void setIpaRd14(String ipaRd14) {
		this.ipaRd14 = ipaRd14;
	}

	public String getIpaRd15() {
		return ipaRd15;
	}

	public void setIpaRd15(String ipaRd15) {
		this.ipaRd15 = ipaRd15;
	}

	public String getIpaRd16() {
		return ipaRd16;
	}

	public void setIpaRd16(String ipaRd16) {
		this.ipaRd16 = ipaRd16;
	}

	public String getIpaRd17() {
		return ipaRd17;
	}

	public void setIpaRd17(String ipaRd17) {
		this.ipaRd17 = ipaRd17;
	}

	public String getIpaRd18() {
		return ipaRd18;
	}

	public void setIpaRd18(String ipaRd18) {
		this.ipaRd18 = ipaRd18;
	}

	public String getIpaRd19() {
		return ipaRd19;
	}

	public void setIpaRd19(String ipaRd19) {
		this.ipaRd19 = ipaRd19;
	}

	public String getIpaRd20() {
		return ipaRd20;
	}

	public void setIpaRd20(String ipaRd20) {
		this.ipaRd20 = ipaRd20;
	}

	public String getIpaRd21() {
		return ipaRd21;
	}

	public void setIpaRd21(String ipaRd21) {
		this.ipaRd21 = ipaRd21;
	}

	public String getIpaRd22() {
		return ipaRd22;
	}

	public void setIpaRd22(String ipaRd22) {
		this.ipaRd22 = ipaRd22;
	}

	public String getIpaRd23() {
		return ipaRd23;
	}

	public void setIpaRd23(String ipaRd23) {
		this.ipaRd23 = ipaRd23;
	}

	public String getIpaRd24() {
		return ipaRd24;
	}

	public void setIpaRd24(String ipaRd24) {
		this.ipaRd24 = ipaRd24;
	}

	public String getIpaRd25() {
		return ipaRd25;
	}

	public void setIpaRd25(String ipaRd25) {
		this.ipaRd25 = ipaRd25;
	}

	public String getIpaRd26() {
		return ipaRd26;
	}

	public void setIpaRd26(String ipaRd26) {
		this.ipaRd26 = ipaRd26;
	}

	public String getIpaRd27() {
		return ipaRd27;
	}

	public void setIpaRd27(String ipaRd27) {
		this.ipaRd27 = ipaRd27;
	}

	public String getIpaRd28() {
		return ipaRd28;
	}

	public void setIpaRd28(String ipaRd28) {
		this.ipaRd28 = ipaRd28;
	}

	public String getIpaRd29() {
		return ipaRd29;
	}

	public void setIpaRd29(String ipaRd29) {
		this.ipaRd29 = ipaRd29;
	}

	public String getIpaRd30() {
		return ipaRd30;
	}

	public void setIpaRd30(String ipaRd30) {
		this.ipaRd30 = ipaRd30;
	}

	public String getRoi2() {
		return roi2;
	}

	public void setRoi2(String roi2) {
		this.roi2 = roi2;
	}

	public String getRoi3() {
		return roi3;
	}

	public void setRoi3(String roi3) {
		this.roi3 = roi3;
	}

	public String getRoi4() {
		return roi4;
	}

	public void setRoi4(String roi4) {
		this.roi4 = roi4;
	}

	public String getRoi5() {
		return roi5;
	}

	public void setRoi5(String roi5) {
		this.roi5 = roi5;
	}

	public String getRoi6() {
		return roi6;
	}

	public void setRoi6(String roi6) {
		this.roi6 = roi6;
	}

	public String getRoi7() {
		return roi7;
	}

	public void setRoi7(String roi7) {
		this.roi7 = roi7;
	}

	public String getRoi8() {
		return roi8;
	}

	public void setRoi8(String roi8) {
		this.roi8 = roi8;
	}

	public String getRoi9() {
		return roi9;
	}

	public void setRoi9(String roi9) {
		this.roi9 = roi9;
	}

	public String getRoi10() {
		return roi10;
	}

	public void setRoi10(String roi10) {
		this.roi10 = roi10;
	}

	public String getRoi11() {
		return roi11;
	}

	public void setRoi11(String roi11) {
		this.roi11 = roi11;
	}

	public String getRoi12() {
		return roi12;
	}

	public void setRoi12(String roi12) {
		this.roi12 = roi12;
	}

	public String getRoi13() {
		return roi13;
	}

	public void setRoi13(String roi13) {
		this.roi13 = roi13;
	}

	public String getRoi14() {
		return roi14;
	}

	public void setRoi14(String roi14) {
		this.roi14 = roi14;
	}

	public String getRoi15() {
		return roi15;
	}

	public void setRoi15(String roi15) {
		this.roi15 = roi15;
	}

	public String getRoi16() {
		return roi16;
	}

	public void setRoi16(String roi16) {
		this.roi16 = roi16;
	}

	public String getRoi17() {
		return roi17;
	}

	public void setRoi17(String roi17) {
		this.roi17 = roi17;
	}

	public String getRoi18() {
		return roi18;
	}

	public void setRoi18(String roi18) {
		this.roi18 = roi18;
	}

	public String getRoi19() {
		return roi19;
	}

	public void setRoi19(String roi19) {
		this.roi19 = roi19;
	}

	public String getRoi20() {
		return roi20;
	}

	public void setRoi20(String roi20) {
		this.roi20 = roi20;
	}

	public String getRoi21() {
		return roi21;
	}

	public void setRoi21(String roi21) {
		this.roi21 = roi21;
	}

	public String getRoi22() {
		return roi22;
	}

	public void setRoi22(String roi22) {
		this.roi22 = roi22;
	}

	public String getRoi23() {
		return roi23;
	}

	public void setRoi23(String roi23) {
		this.roi23 = roi23;
	}

	public String getRoi24() {
		return roi24;
	}

	public void setRoi24(String roi24) {
		this.roi24 = roi24;
	}

	public String getRoi25() {
		return roi25;
	}

	public void setRoi25(String roi25) {
		this.roi25 = roi25;
	}

	public String getRoi26() {
		return roi26;
	}

	public void setRoi26(String roi26) {
		this.roi26 = roi26;
	}

	public String getRoi27() {
		return roi27;
	}

	public void setRoi27(String roi27) {
		this.roi27 = roi27;
	}

	public String getRoi28() {
		return roi28;
	}

	public void setRoi28(String roi28) {
		this.roi28 = roi28;
	}

	public String getRoi29() {
		return roi29;
	}

	public void setRoi29(String roi29) {
		this.roi29 = roi29;
	}

	public String getRoi30() {
		return roi30;
	}

	public void setRoi30(String roi30) {
		this.roi30 = roi30;
	}

	public int getAdd_purchase_users() {
		return add_purchase_users;
	}

	public void setAdd_purchase_users(int add_purchase_users) {
		this.add_purchase_users = add_purchase_users;
	}

	public int getActive_purchase_users() {
		return active_purchase_users;
	}

	public void setActive_purchase_users(int active_purchase_users) {
		this.active_purchase_users = active_purchase_users;
	}
}
