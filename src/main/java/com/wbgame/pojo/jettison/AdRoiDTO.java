package com.wbgame.pojo.jettison;

import java.io.Serializable;


/**
 * @Author: xugx
 * @Description: 大数据广告ROI报表Dto
 * @Date: created in 2020/10/20/12:23
 */
public class AdRoiDTO  implements  Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 6914139485495896620L;
	/**
	 *计划ID
	 */
	private String campaignId;
	/**
	 * 媒体
	 */
	private String media; 
	/**
	 *  投放子渠道
	 */
	private String channel; 
	/**
	 *  广告组名称
	 */
	private String groupName; 
	/**
	 * 计划名称
	 */
	private String campaignName; 
	/**
	 * 日期
	 */
	private String day;

	/**
	 * 小时
	 */
	private String hourly;
	
	/**
	 * 应用ID
	 */
	private String appId;
	/**
	 * 用户激活渠道(子渠道)
	 */
	private String childChannel;
	/**
	 *应用分类
	 */
	private String appCategory;
	
	/**
	 * 媒体账号ID
	 */
	private String accountId;
	/**
	 *uba模式 1 是  0 否
	 */
	private String delivery_mode;
	/**
	 *anchor_related_type  原生广告  1 是  0 否
	 */
	private String anchor_related_type;
	
	/**
	 *投放类型
	 */
	private String adsenseType;
	/**
	 * 新增用户数
	 */
	private Integer addUser;
	
	/**
	 * 排重新增用户数
	 */
	private Integer dupAddUser;
	
	/**
	 * 活跃用户数
	 */
	private Integer activeUser;
	/**
	 * 总收入
	 */
	private Double totalIncome ;
	/**
	 * 消耗
	 */
	private Double cost ;
	/**
	 * 新增用户当日总收入
	 */
	private Double addUserLtv0 ;
	/**
	 * cpa  消耗/新增人数
	 */
	private Double cpa ;
	/**
	 * 活跃用户的当日的ltv平均值
	 */
	private Double dauArpu;
	/**
	 * 当日新增用户的ltv平均值
	 */
	private Double addArpu;
	/**
	 * 新增用户1日留存数
	 */
	private Long rdOne;
	/**
	 * 新增用户2日留存数
	 */
	private Long rdTwo;
	/**
	 * 新增用户3日留存数
	 */
	private Long rdThree;
	/**
	 * 新增用户4日留存数
	 */
	private Long rdFour;
	/**
	 * 新增用户5日留存数
	 */
	private Long rdFive;
	/**
	 * 新增用户6日留存数
	 */
	private Long rdSix;
	/**
	 * 新增用户7日留存数
	 */
	private Long rdSeven;
	/**
	 * 新增用户14日留存数
	 */
	private Long rdFourteen;
	/**
	 * 新增用户8-13   15-29日留存数
	 */
	private Long rd8;
	private Long rd9;
	private Long rd10;
	private Long rd11;
	private Long rd12;
	private Long rd13;
	private Long rd15;
	private Long rd16;
	private Long rd17;
	private Long rd18;
	private Long rd19;
	private Long rd20;
	private Long rd21;
	private Long rd22;
	private Long rd23;
	private Long rd24;
	private Long rd25;
	private Long rd26;
	private Long rd27;
	private Long rd28;
	private Long rd29;
	private Long rd75;
	private Long rd90;
    private Long rd105;
    private Long rd120;
    private Long rd135;
    private Long rd150;
    private Long rd165;
    private Long rd180;
    private Long rd195;
    private Long rd210;
    private Long rd225;
    private Long rd240;
    private Long rd255;
    private Long rd270;
    private Long rd285;
    private Long rd300;
    private Long rd315;
    private Long rd330;
    private Long rd345;
    private Long rd360;
	
	/**
	 * ltv8-13   15-29
	 */
	private Double ltv8;
	private Double ltv9;
	private Double ltv10;
	private Double ltv11;
	private Double ltv12;
	private Double ltv13;
	private Double ltv15;
	private Double ltv16;
	private Double ltv17;
	private Double ltv18;
	private Double ltv19;
	private Double ltv20;
	private Double ltv21;
	private Double ltv22;
	private Double ltv23;
	private Double ltv24;
	private Double ltv25;
	private Double ltv26;
	private Double ltv27;
	private Double ltv28;
	private Double ltv29;
	
    private Double ltv105;
    private Double ltv135;
    private Double ltv150;
    private Double ltv165;
    private Double ltv180;
    private Double ltv195;
    private Double ltv210;
    private Double ltv225;
    private Double ltv240;
    private Double ltv255;
    private Double ltv270;
    private Double ltv285;
    private Double ltv300;
    private Double ltv315;
    private Double ltv330;
    private Double ltv345;
    private Double ltv360;
	/**
	 * 新增用户30日留存数
	 */
	private Long rdThrity;
	/**
	 * 新增用户36日留存数
	 */
	private Long rdThritySix;
	
	/**
	 * 新增用户42日留存数
	 */
	private Long rdFortyTwo;
	/**
	 * 新增用户48日留存数
	 */
	private Long rdFortyEight;
	/**
	 * 新增用户54日留存数
	 */
	private Long rdFiftyFour;
	/**
	 * 新增用户60日留存数
	 */
	private Long rdSixty;
	
	/**
	 * ltv1-120
	 */
	private Double ltv1;
	private Double ltv2;
	private Double ltv3;
	private Double ltv4;
	private Double ltv5;
	private Double ltv6;
	private Double ltv7;
	private Double ltv14;
	private Double ltv30;
	private Double ltv36;
	private Double ltv42;
	private Double ltv48;
	private Double ltv54;
	private Double ltv60;
	private Double ltv75;
	private Double ltv90;
	private Double ltv120;
	
	/**
	 * 当日总的开屏展示次数
	 */
	private Long splashShowTimes;
	/**
	 * 当日总的插屏视频展示次数
	 */
	private Long plaqueVideoShowTimes;
	/**
	 * 当日总的banner展示次数
	 */
	private Long bannerShowTimes;
	/**
	 * 当日总的信息流展示次数
	 */
	private Long infoFlowShowTimes;
	/**
	 * 当日总的插屏展示次数
	 */
	private Long plaqueShowTimes;
	/**
	 * 当日总的视频展示次数
	 */
	private Long videoShowTimes;
	
	/**
	 * 当日新增用户总的开屏展示次数
	 */
	private Long asplashShowTimes ;
	/**
	 * 当日新增用户总的插屏视频展示次数
	 */
	private Long aplaqueVideoShowTimes;
	/**
	 * 当日新增用户总的banner展示次数
	 */
	private Long abannerShowTimes;
	/**
	 * 当日新增用户总的信息流展示次数
	 */
	private Long ainfoFlowShowTimes;
	/**
	 * 当日新增用户总的插屏展示次数
	 */
	private Long aplaqueShowTimes;
	/**
	 * 当日新增用户总的视频展示次数
	 */
	private Long avideoShowTimes;
	/**
	 *消耗
	 */
	private Double spend;
	/**
	 *投放人员
	 */
	private String putUser;
	
	/**
	 *视频收入
	 */
	private Double videoLtv;
	/**
	 *plaque 收入
	 */
	private Double plaqueLtv;
	/**
	*展现数
	*/
	private Long impressions;
	/**
	*点击数
	*/
	private Long clicks;
	/**
	*激活数
	*/
	private Long installs;
	/**
	*转化数
	*/
	private Long convert;
	/**
	*注册数
	*/
	private Long register;
	/**
	*关键行为数
	*/
	private Long gameAddiction;
	
	/**
	 *活跃用户内购收入
	 */
	private Double activeIpa;
	
	/**
	 *新增用户内购收入
	 */
	private Double addIpa;
	/**
	 *新增用户后N天内购收入
	 */
	private Double ipaLtv2;
	private Double ipaLtv3;
	private Double ipaLtv4;
	private Double ipaLtv5;
	private Double ipaLtv6;
	private Double ipaLtv7;
	private Double ipaLtv14;
	private Double ipaLtv30;
	private Double ipaLtv36;
	private Double ipaLtv42;
	private Double ipaLtv48;
	private Double ipaLtv54;
	private Double ipaLtv60;
	private Double ipaLtv75;
	private Double ipaLtv90;
	private Double ipaLtv120;
	
	private Double ipaLtv8;
	private Double ipaLtv9;
	private Double ipaLtv10;
	private Double ipaLtv11;
	private Double ipaLtv12;
	private Double ipaLtv13;
	private Double ipaLtv15;
	private Double ipaLtv16;
	private Double ipaLtv17;
	private Double ipaLtv18;
	private Double ipaLtv19;
	private Double ipaLtv20;
	private Double ipaLtv21;
	private Double ipaLtv22;
	private Double ipaLtv23;
	private Double ipaLtv24;
	private Double ipaLtv25;
	private Double ipaLtv26;
	private Double ipaLtv27;
	private Double ipaLtv28;
	private Double ipaLtv29;
    private Double ipaLtv105;
    private Double ipaLtv135;
    private Double ipaLtv150;
    private Double ipaLtv165;
    private Double ipaLtv180;
    private Double ipaLtv195;
    private Double ipaLtv210;
    private Double ipaLtv225;
    private Double ipaLtv240;
    private Double ipaLtv255;
    private Double ipaLtv270;
    private Double ipaLtv285;
    private Double ipaLtv300;
    private Double ipaLtv315;
    private Double ipaLtv330;
    private Double ipaLtv345;
    private Double ipaLtv360;
	/**
	 *开发主体
	 */
	private String company;
	/**
	 *账号归属   '1:自运营-分包 2:代运营-分包 3:自运营-直投 4:代运营-直投'
	 */
	private Integer accountType;
	/**
	 *出价策略 1.激活，2.注册，3.付费，4.ROI，5.关键行为，6.次留，7.激活+次留，8.激活+付费，9.激活+关键行为'
	 */
	private Integer strategy;
	
	/**
	 *首次付费数
	 */
	private Integer payCount;
	
	/**
	 *7日付费数
	 */
	private Integer payCount7;
	
	/**
	 *付费次数
	 */
	private Integer gamePayCount;
	
	/**
	 *7日首次付费数
	 */
	private Integer payCountFirst7;
	
	/**
	 *出价
	 */
	private Double bid;
	
	/**
	 *投放位置
	 */
	private String adsensePosition;
	
	/**
	 *活跃用户付费数
	 */
	private Integer activePurchaseUsers;
	
	/**
	 *新增付费用户数
	 */
	private Integer addPurchaseUsers;
	
	/**
	 *付费用户N日留存用户数
	 */
	private Integer ipaRd1;
	private Integer ipaRd2;
	private Integer ipaRd3;
	private Integer ipaRd4;
	private Integer ipaRd5;
	private Integer ipaRd6;
	private Integer ipaRd7;
	private Integer ipaRd14;
	private Integer ipaRd30;
	private Integer ipaRd36;
	private Integer ipaRd42;
	private Integer ipaRd48;
	private Integer ipaRd54;
	private Integer ipaRd60;
	private Integer ipaRd75;
	private Integer ipaRd90;
	private Integer ipaRd120;

	private Integer ipaRd8;
	private Integer ipaRd9;
	private Integer ipaRd10;
	private Integer ipaRd11;
	private Integer ipaRd12;
	private Integer ipaRd13;
	private Integer ipaRd15;
	private Integer ipaRd16;
	private Integer ipaRd17;
	private Integer ipaRd18;
	private Integer ipaRd19;
	private Integer ipaRd20;
	private Integer ipaRd21;
	private Integer ipaRd22;
	private Integer ipaRd23;
	private Integer ipaRd24;
	private Integer ipaRd25;
	private Integer ipaRd26;
	private Integer ipaRd27;
	private Integer ipaRd28;
	private Integer ipaRd29;
    private Integer ipaRd105;
    private Integer ipaRd135;
    private Integer ipaRd150;
    private Integer ipaRd165;
    private Integer ipaRd180;
    private Integer ipaRd195;
    private Integer ipaRd210;
    private Integer ipaRd225;
    private Integer ipaRd240;
    private Integer ipaRd255;
    private Integer ipaRd270;
    private Integer ipaRd285;
    private Integer ipaRd300;
    private Integer ipaRd315;
    private Integer ipaRd330;
    private Integer ipaRd345;
    private Integer ipaRd360;
	
	public Long getRd105() {
		return rd105;
	}
	public void setRd105(Long rd105) {
		this.rd105 = rd105;
	}
	public Long getRd135() {
		return rd135;
	}
	public void setRd135(Long rd135) {
		this.rd135 = rd135;
	}
	public Long getRd150() {
		return rd150;
	}
	public void setRd150(Long rd150) {
		this.rd150 = rd150;
	}
	public Long getRd165() {
		return rd165;
	}
	public void setRd165(Long rd165) {
		this.rd165 = rd165;
	}
	public Long getRd180() {
		return rd180;
	}
	public void setRd180(Long rd180) {
		this.rd180 = rd180;
	}
	public Long getRd195() {
		return rd195;
	}
	public void setRd195(Long rd195) {
		this.rd195 = rd195;
	}
	public Long getRd210() {
		return rd210;
	}
	public void setRd210(Long rd210) {
		this.rd210 = rd210;
	}
	public Long getRd225() {
		return rd225;
	}
	public void setRd225(Long rd225) {
		this.rd225 = rd225;
	}
	public Long getRd240() {
		return rd240;
	}
	public void setRd240(Long rd240) {
		this.rd240 = rd240;
	}
	public Long getRd255() {
		return rd255;
	}
	public void setRd255(Long rd255) {
		this.rd255 = rd255;
	}
	public Long getRd270() {
		return rd270;
	}
	public void setRd270(Long rd270) {
		this.rd270 = rd270;
	}
	public Long getRd285() {
		return rd285;
	}
	public void setRd285(Long rd285) {
		this.rd285 = rd285;
	}
	public Long getRd300() {
		return rd300;
	}
	public void setRd300(Long rd300) {
		this.rd300 = rd300;
	}
	public Long getRd315() {
		return rd315;
	}
	public void setRd315(Long rd315) {
		this.rd315 = rd315;
	}
	public Long getRd330() {
		return rd330;
	}
	public void setRd330(Long rd330) {
		this.rd330 = rd330;
	}
	public Long getRd345() {
		return rd345;
	}
	public void setRd345(Long rd345) {
		this.rd345 = rd345;
	}
	public Long getRd360() {
		return rd360;
	}
	public void setRd360(Long rd360) {
		this.rd360 = rd360;
	}
	public Double getLtv105() {
		return ltv105;
	}
	public void setLtv105(Double ltv105) {
		this.ltv105 = ltv105;
	}
	public Double getLtv135() {
		return ltv135;
	}
	public void setLtv135(Double ltv135) {
		this.ltv135 = ltv135;
	}
	public Double getLtv150() {
		return ltv150;
	}
	public void setLtv150(Double ltv150) {
		this.ltv150 = ltv150;
	}
	public Double getLtv165() {
		return ltv165;
	}
	public void setLtv165(Double ltv165) {
		this.ltv165 = ltv165;
	}
	public Double getLtv180() {
		return ltv180;
	}
	public void setLtv180(Double ltv180) {
		this.ltv180 = ltv180;
	}
	public Double getLtv195() {
		return ltv195;
	}
	public void setLtv195(Double ltv195) {
		this.ltv195 = ltv195;
	}
	public Double getLtv210() {
		return ltv210;
	}
	public void setLtv210(Double ltv210) {
		this.ltv210 = ltv210;
	}
	public Double getLtv225() {
		return ltv225;
	}
	public void setLtv225(Double ltv225) {
		this.ltv225 = ltv225;
	}
	public Double getLtv240() {
		return ltv240;
	}
	public void setLtv240(Double ltv240) {
		this.ltv240 = ltv240;
	}
	public Double getLtv255() {
		return ltv255;
	}
	public void setLtv255(Double ltv255) {
		this.ltv255 = ltv255;
	}
	public Double getLtv270() {
		return ltv270;
	}
	public void setLtv270(Double ltv270) {
		this.ltv270 = ltv270;
	}
	public Double getLtv285() {
		return ltv285;
	}
	public void setLtv285(Double ltv285) {
		this.ltv285 = ltv285;
	}
	public Double getLtv300() {
		return ltv300;
	}
	public void setLtv300(Double ltv300) {
		this.ltv300 = ltv300;
	}
	public Double getLtv315() {
		return ltv315;
	}
	public void setLtv315(Double ltv315) {
		this.ltv315 = ltv315;
	}
	public Double getLtv330() {
		return ltv330;
	}
	public void setLtv330(Double ltv330) {
		this.ltv330 = ltv330;
	}
	public Double getLtv345() {
		return ltv345;
	}
	public void setLtv345(Double ltv345) {
		this.ltv345 = ltv345;
	}
	public Double getLtv360() {
		return ltv360;
	}
	public void setLtv360(Double ltv360) {
		this.ltv360 = ltv360;
	}
	public Long getRd75() {
		return rd75;
	}
	public void setRd75(Long rd75) {
		this.rd75 = rd75;
	}
	public Long getRd90() {
		return rd90;
	}
	public void setRd90(Long rd90) {
		this.rd90 = rd90;
	}
	public Long getRd120() {
		return rd120;
	}
	public void setRd120(Long rd120) {
		this.rd120 = rd120;
	}
	public Double getLtv75() {
		return ltv75;
	}
	public void setLtv75(Double ltv75) {
		this.ltv75 = ltv75;
	}
	public Double getLtv90() {
		return ltv90;
	}
	public void setLtv90(Double ltv90) {
		this.ltv90 = ltv90;
	}
	public Double getLtv120() {
		return ltv120;
	}
	public void setLtv120(Double ltv120) {
		this.ltv120 = ltv120;
	}
	public Double getIpaLtv75() {
		return ipaLtv75;
	}
	public void setIpaLtv75(Double ipaLtv75) {
		this.ipaLtv75 = ipaLtv75;
	}
	public Double getIpaLtv90() {
		return ipaLtv90;
	}
	public void setIpaLtv90(Double ipaLtv90) {
		this.ipaLtv90 = ipaLtv90;
	}
	public Double getIpaLtv120() {
		return ipaLtv120;
	}
	public void setIpaLtv120(Double ipaLtv120) {
		this.ipaLtv120 = ipaLtv120;
	}
	public Integer getIpaRd36() {
		return ipaRd36;
	}
	public void setIpaRd36(Integer ipaRd36) {
		this.ipaRd36 = ipaRd36;
	}
	public Integer getIpaRd42() {
		return ipaRd42;
	}
	public void setIpaRd42(Integer ipaRd42) {
		this.ipaRd42 = ipaRd42;
	}
	public Integer getIpaRd48() {
		return ipaRd48;
	}
	public void setIpaRd48(Integer ipaRd48) {
		this.ipaRd48 = ipaRd48;
	}
	public Integer getIpaRd54() {
		return ipaRd54;
	}
	public void setIpaRd54(Integer ipaRd54) {
		this.ipaRd54 = ipaRd54;
	}
	public Integer getIpaRd75() {
		return ipaRd75;
	}
	public void setIpaRd75(Integer ipaRd75) {
		this.ipaRd75 = ipaRd75;
	}
	public Integer getIpaRd90() {
		return ipaRd90;
	}
	public void setIpaRd90(Integer ipaRd90) {
		this.ipaRd90 = ipaRd90;
	}
	public Integer getIpaRd120() {
		return ipaRd120;
	}
	public void setIpaRd120(Integer ipaRd120) {
		this.ipaRd120 = ipaRd120;
	}
	public Integer getIpaRd8() {
		return ipaRd8;
	}
	public void setIpaRd8(Integer ipaRd8) {
		this.ipaRd8 = ipaRd8;
	}
	public Integer getIpaRd9() {
		return ipaRd9;
	}
	public void setIpaRd9(Integer ipaRd9) {
		this.ipaRd9 = ipaRd9;
	}
	public Integer getIpaRd10() {
		return ipaRd10;
	}
	public void setIpaRd10(Integer ipaRd10) {
		this.ipaRd10 = ipaRd10;
	}
	public Integer getIpaRd11() {
		return ipaRd11;
	}
	public void setIpaRd11(Integer ipaRd11) {
		this.ipaRd11 = ipaRd11;
	}
	public Integer getIpaRd12() {
		return ipaRd12;
	}
	public void setIpaRd12(Integer ipaRd12) {
		this.ipaRd12 = ipaRd12;
	}
	public Integer getIpaRd13() {
		return ipaRd13;
	}
	public void setIpaRd13(Integer ipaRd13) {
		this.ipaRd13 = ipaRd13;
	}
	public Integer getIpaRd15() {
		return ipaRd15;
	}
	public void setIpaRd15(Integer ipaRd15) {
		this.ipaRd15 = ipaRd15;
	}
	public Integer getIpaRd16() {
		return ipaRd16;
	}
	public void setIpaRd16(Integer ipaRd16) {
		this.ipaRd16 = ipaRd16;
	}
	public Integer getIpaRd17() {
		return ipaRd17;
	}
	public void setIpaRd17(Integer ipaRd17) {
		this.ipaRd17 = ipaRd17;
	}
	public Integer getIpaRd18() {
		return ipaRd18;
	}
	public void setIpaRd18(Integer ipaRd18) {
		this.ipaRd18 = ipaRd18;
	}
	public Integer getIpaRd19() {
		return ipaRd19;
	}
	public void setIpaRd19(Integer ipaRd19) {
		this.ipaRd19 = ipaRd19;
	}
	public Integer getIpaRd20() {
		return ipaRd20;
	}
	public void setIpaRd20(Integer ipaRd20) {
		this.ipaRd20 = ipaRd20;
	}
	public Integer getIpaRd21() {
		return ipaRd21;
	}
	public void setIpaRd21(Integer ipaRd21) {
		this.ipaRd21 = ipaRd21;
	}
	public Integer getIpaRd22() {
		return ipaRd22;
	}
	public void setIpaRd22(Integer ipaRd22) {
		this.ipaRd22 = ipaRd22;
	}
	public Integer getIpaRd23() {
		return ipaRd23;
	}
	public void setIpaRd23(Integer ipaRd23) {
		this.ipaRd23 = ipaRd23;
	}
	public Integer getIpaRd24() {
		return ipaRd24;
	}
	public void setIpaRd24(Integer ipaRd24) {
		this.ipaRd24 = ipaRd24;
	}
	public Integer getIpaRd25() {
		return ipaRd25;
	}
	public void setIpaRd25(Integer ipaRd25) {
		this.ipaRd25 = ipaRd25;
	}
	public Integer getIpaRd26() {
		return ipaRd26;
	}
	public void setIpaRd26(Integer ipaRd26) {
		this.ipaRd26 = ipaRd26;
	}
	public Integer getIpaRd27() {
		return ipaRd27;
	}
	public void setIpaRd27(Integer ipaRd27) {
		this.ipaRd27 = ipaRd27;
	}
	public Integer getIpaRd28() {
		return ipaRd28;
	}
	public void setIpaRd28(Integer ipaRd28) {
		this.ipaRd28 = ipaRd28;
	}
	public Integer getIpaRd29() {
		return ipaRd29;
	}
	public void setIpaRd29(Integer ipaRd29) {
		this.ipaRd29 = ipaRd29;
	}
	public Integer getIpaRd1() {
		return ipaRd1;
	}
	public void setIpaRd1(Integer ipaRd1) {
		this.ipaRd1 = ipaRd1;
	}
	public Integer getIpaRd2() {
		return ipaRd2;
	}
	public void setIpaRd2(Integer ipaRd2) {
		this.ipaRd2 = ipaRd2;
	}
	public Integer getIpaRd3() {
		return ipaRd3;
	}
	public void setIpaRd3(Integer ipaRd3) {
		this.ipaRd3 = ipaRd3;
	}
	public Integer getIpaRd4() {
		return ipaRd4;
	}
	public void setIpaRd4(Integer ipaRd4) {
		this.ipaRd4 = ipaRd4;
	}
	public Integer getIpaRd5() {
		return ipaRd5;
	}
	public void setIpaRd5(Integer ipaRd5) {
		this.ipaRd5 = ipaRd5;
	}
	public Integer getIpaRd6() {
		return ipaRd6;
	}
	public void setIpaRd6(Integer ipaRd6) {
		this.ipaRd6 = ipaRd6;
	}
	public Integer getIpaRd7() {
		return ipaRd7;
	}
	public void setIpaRd7(Integer ipaRd7) {
		this.ipaRd7 = ipaRd7;
	}
	public Integer getIpaRd14() {
		return ipaRd14;
	}
	public void setIpaRd14(Integer ipaRd14) {
		this.ipaRd14 = ipaRd14;
	}
	public Integer getIpaRd30() {
		return ipaRd30;
	}
	public void setIpaRd30(Integer ipaRd30) {
		this.ipaRd30 = ipaRd30;
	}
	public Integer getIpaRd60() {
		return ipaRd60;
	}
	public void setIpaRd60(Integer ipaRd60) {
		this.ipaRd60 = ipaRd60;
	}
	public Integer getActivePurchaseUsers() {
		return activePurchaseUsers;
	}
	public void setActivePurchaseUsers(Integer activePurchaseUsers) {
		this.activePurchaseUsers = activePurchaseUsers;
	}
	public Integer getAddPurchaseUsers() {
		return addPurchaseUsers;
	}
	public void setAddPurchaseUsers(Integer addPurchaseUsers) {
		this.addPurchaseUsers = addPurchaseUsers;
	}
	public String getAdsensePosition() {
		return adsensePosition;
	}
	public void setAdsensePosition(String adsensePosition) {
		this.adsensePosition = adsensePosition;
	}
	public Double getBid() {
		return bid;
	}
	public void setBid(Double bid) {
		this.bid = bid;
	}
	public Integer getPayCount() {
		return payCount;
	}
	public void setPayCount(Integer payCount) {
		this.payCount = payCount;
	}
	public Integer getPayCount7() {
		return payCount7;
	}
	public void setPayCount7(Integer payCount7) {
		this.payCount7 = payCount7;
	}
	public Integer getGamePayCount() {
		return gamePayCount;
	}
	public void setGamePayCount(Integer gamePayCount) {
		this.gamePayCount = gamePayCount;
	}
	public Integer getPayCountFirst7() {
		return payCountFirst7;
	}
	public void setPayCountFirst7(Integer payCountFirst7) {
		this.payCountFirst7 = payCountFirst7;
	}
	public String getCompany() {
		return company;
	}
	public void setCompany(String company) {
		this.company = company;
	}
	public Integer getAccountType() {
		return accountType;
	}
	public void setAccountType(Integer accountType) {
		this.accountType = accountType;
	}
	public Integer getStrategy() {
		return strategy;
	}
	public void setStrategy(Integer strategy) {
		this.strategy = strategy;
	}
	public Double getIpaLtv36() {
		return ipaLtv36;
	}
	public void setIpaLtv36(Double ipaLtv36) {
		this.ipaLtv36 = ipaLtv36;
	}
	public Double getIpaLtv42() {
		return ipaLtv42;
	}
	public void setIpaLtv42(Double ipaLtv42) {
		this.ipaLtv42 = ipaLtv42;
	}
	public Double getIpaLtv48() {
		return ipaLtv48;
	}
	public void setIpaLtv48(Double ipaLtv48) {
		this.ipaLtv48 = ipaLtv48;
	}
	public Double getIpaLtv54() {
		return ipaLtv54;
	}
	public void setIpaLtv54(Double ipaLtv54) {
		this.ipaLtv54 = ipaLtv54;
	}
	public Double getIpaLtv60() {
		return ipaLtv60;
	}
	public void setIpaLtv60(Double ipaLtv60) {
		this.ipaLtv60 = ipaLtv60;
	}
	public String getCampaignId() {
		return campaignId;
	}
	public void setCampaignId(String campaignId) {
		this.campaignId = campaignId;
	}
	public String getMedia() {
		return media;
	}
	public void setMedia(String media) {
		this.media = media;
	}
	public String getChannel() {
		return channel;
	}
	public void setChannel(String channel) {
		this.channel = channel;
	}
	public String getGroupName() {
		return groupName;
	}
	public Long getRdThritySix() {
		return rdThritySix;
	}
	public void setRdThritySix(Long rdThritySix) {
		this.rdThritySix = rdThritySix;
	}
	
	public Long getRdFortyTwo() {
		return rdFortyTwo;
	}
	public void setRdFortyTwo(Long rdFortyTwo) {
		this.rdFortyTwo = rdFortyTwo;
	}
	public Long getRdFortyEight() {
		return rdFortyEight;
	}
	public void setRdFortyEight(Long rdFortyEight) {
		this.rdFortyEight = rdFortyEight;
	}
	public Long getRdFiftyFour() {
		return rdFiftyFour;
	}
	public void setRdFiftyFour(Long rdFiftyFour) {
		this.rdFiftyFour = rdFiftyFour;
	}
	public Long getRdSixty() {
		return rdSixty;
	}
	public void setRdSixty(Long rdSixty) {
		this.rdSixty = rdSixty;
	}
	public Integer getAddUser() {
		return addUser;
	}
	public void setAddUser(Integer addUser) {
		this.addUser = addUser;
	}
	public Integer getDupAddUser() {
		return dupAddUser;
	}
	public void setDupAddUser(Integer dupAddUser) {
		this.dupAddUser = dupAddUser;
	}
	public Integer getActiveUser() {
		return activeUser;
	}
	public void setActiveUser(Integer activeUser) {
		this.activeUser = activeUser;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public String getCampaignName() {
		return campaignName;
	}
	public void setCampaignName(String campaignName) {
		this.campaignName = campaignName;
	}
	public String getDay() {
		return day;
	}
	public void setDay(String day) {
		this.day = day;
	}
	public String getAppId() {
		return appId;
	}
	public void setAppId(String appId) {
		this.appId = appId;
	}
	public String getChildChannel() {
		return childChannel;
	}
	public void setChildChannel(String childChannel) {
		this.childChannel = childChannel;
	}
	public String getAccountId() {
		return accountId;
	}
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}
	public Double getTotalIncome() {
		return totalIncome;
	}
	public void setTotalIncome(Double totalIncome) {
		this.totalIncome = totalIncome;
	}
	public Double getAddUserLtv0() {
		return addUserLtv0;
	}
	public void setAddUserLtv0(Double addUserLtv0) {
		this.addUserLtv0 = addUserLtv0;
	}
	public Double getCpa() {
		return cpa;
	}
	public void setCpa(Double cpa) {
		this.cpa = cpa;
	}
	public Double getDauArpu() {
		return dauArpu;
	}
	public void setDauArpu(Double dauArpu) {
		this.dauArpu = dauArpu;
	}
	public Double getAddArpu() {
		return addArpu;
	}
	public void setAddArpu(Double addArpu) {
		this.addArpu = addArpu;
	}
	public Long getRdOne() {
		return rdOne;
	}
	public void setRdOne(Long rdOne) {
		this.rdOne = rdOne;
	}
	public Long getRdTwo() {
		return rdTwo;
	}
	public void setRdTwo(Long rdTwo) {
		this.rdTwo = rdTwo;
	}
	public Long getRdThree() {
		return rdThree;
	}
	public void setRdThree(Long rdThree) {
		this.rdThree = rdThree;
	}
	public Long getRdFour() {
		return rdFour;
	}
	public void setRdFour(Long rdFour) {
		this.rdFour = rdFour;
	}
	public Long getRdFive() {
		return rdFive;
	}
	public void setRdFive(Long rdFive) {
		this.rdFive = rdFive;
	}
	public Long getRdSix() {
		return rdSix;
	}
	public void setRdSix(Long rdSix) {
		this.rdSix = rdSix;
	}
	public Long getRdSeven() {
		return rdSeven;
	}
	public void setRdSeven(Long rdSeven) {
		this.rdSeven = rdSeven;
	}
	public Long getRdFourteen() {
		return rdFourteen;
	}
	public void setRdFourteen(Long rdFourteen) {
		this.rdFourteen = rdFourteen;
	}
	public Long getRdThrity() {
		return rdThrity;
	}
	public void setRdThrity(Long rdThrity) {
		this.rdThrity = rdThrity;
	}
	public Double getLtv1() {
		return ltv1;
	}
	public void setLtv1(Double ltv1) {
		this.ltv1 = ltv1;
	}
	public Double getLtv2() {
		return ltv2;
	}
	public void setLtv2(Double ltv2) {
		this.ltv2 = ltv2;
	}
	public Double getLtv3() {
		return ltv3;
	}
	public void setLtv3(Double ltv3) {
		this.ltv3 = ltv3;
	}
	public Double getLtv4() {
		return ltv4;
	}
	public void setLtv4(Double ltv4) {
		this.ltv4 = ltv4;
	}
	public Double getLtv5() {
		return ltv5;
	}
	public void setLtv5(Double ltv5) {
		this.ltv5 = ltv5;
	}
	public Double getLtv6() {
		return ltv6;
	}
	public void setLtv6(Double ltv6) {
		this.ltv6 = ltv6;
	}
	public Double getLtv7() {
		return ltv7;
	}
	public void setLtv7(Double ltv7) {
		this.ltv7 = ltv7;
	}
	public Double getLtv14() {
		return ltv14;
	}
	public void setLtv14(Double ltv14) {
		this.ltv14 = ltv14;
	}
	public Double getLtv30() {
		return ltv30;
	}
	public void setLtv30(Double ltv30) {
		this.ltv30 = ltv30;
	}
	public Long getSplashShowTimes() {
		return splashShowTimes;
	}
	public void setSplashShowTimes(Long splashShowTimes) {
		this.splashShowTimes = splashShowTimes;
	}
	public Long getPlaqueVideoShowTimes() {
		return plaqueVideoShowTimes;
	}
	public void setPlaqueVideoShowTimes(Long plaqueVideoShowTimes) {
		this.plaqueVideoShowTimes = plaqueVideoShowTimes;
	}
	public Long getBannerShowTimes() {
		return bannerShowTimes;
	}
	public void setBannerShowTimes(Long bannerShowTimes) {
		this.bannerShowTimes = bannerShowTimes;
	}
	public Long getInfoFlowShowTimes() {
		return infoFlowShowTimes;
	}
	public void setInfoFlowShowTimes(Long infoFlowShowTimes) {
		this.infoFlowShowTimes = infoFlowShowTimes;
	}
	public Long getPlaqueShowTimes() {
		return plaqueShowTimes;
	}
	public void setPlaqueShowTimes(Long plaqueShowTimes) {
		this.plaqueShowTimes = plaqueShowTimes;
	}
	public Long getVideoShowTimes() {
		return videoShowTimes;
	}
	public void setVideoShowTimes(Long videoShowTimes) {
		this.videoShowTimes = videoShowTimes;
	}
	public Long getAsplashShowTimes() {
		return asplashShowTimes;
	}
	public void setAsplashShowTimes(Long asplashShowTimes) {
		this.asplashShowTimes = asplashShowTimes;
	}
	public Long getAplaqueVideoShowTimes() {
		return aplaqueVideoShowTimes;
	}
	public void setAplaqueVideoShowTimes(Long aplaqueVideoShowTimes) {
		this.aplaqueVideoShowTimes = aplaqueVideoShowTimes;
	}
	public Long getAbannerShowTimes() {
		return abannerShowTimes;
	}
	public void setAbannerShowTimes(Long abannerShowTimes) {
		this.abannerShowTimes = abannerShowTimes;
	}
	public Long getAinfoFlowShowTimes() {
		return ainfoFlowShowTimes;
	}
	public void setAinfoFlowShowTimes(Long ainfoFlowShowTimes) {
		this.ainfoFlowShowTimes = ainfoFlowShowTimes;
	}
	public Long getAplaqueShowTimes() {
		return aplaqueShowTimes;
	}
	public void setAplaqueShowTimes(Long aplaqueShowTimes) {
		this.aplaqueShowTimes = aplaqueShowTimes;
	}
	public Long getAvideoShowTimes() {
		return avideoShowTimes;
	}
	public void setAvideoShowTimes(Long avideoShowTimes) {
		this.avideoShowTimes = avideoShowTimes;
	}
	public Double getSpend() {
		return spend;
	}
	public void setSpend(Double spend) {
		this.spend = spend;
	}
	public String getPutUser() {
		return putUser;
	}
	public void setPutUser(String putUser) {
		this.putUser = putUser;
	}
	public Double getVideoLtv() {
		return videoLtv;
	}
	public void setVideoLtv(Double videoLtv) {
		this.videoLtv = videoLtv;
	}
	public Double getPlaqueLtv() {
		return plaqueLtv;
	}
	public void setPlaqueLtv(Double plaqueLtv) {
		this.plaqueLtv = plaqueLtv;
	}
	public Long getImpressions() {
		return impressions;
	}
	public void setImpressions(Long impressions) {
		this.impressions = impressions;
	}
	public Long getClicks() {
		return clicks;
	}
	public void setClicks(Long clicks) {
		this.clicks = clicks;
	}
	public Long getInstalls() {
		return installs;
	}
	public void setInstalls(Long installs) {
		this.installs = installs;
	}
	public Long getConvert() {
		return convert;
	}
	public void setConvert(Long convert) {
		this.convert = convert;
	}
	public Long getRegister() {
		return register;
	}
	public void setRegister(Long register) {
		this.register = register;
	}
	public Long getGameAddiction() {
		return gameAddiction;
	}
	public void setGameAddiction(Long gameAddiction) {
		this.gameAddiction = gameAddiction;
	}
	public Double getActiveIpa() {
		return activeIpa;
	}
	public void setActiveIpa(Double activeIpa) {
		this.activeIpa = activeIpa;
	}
	public Double getAddIpa() {
		return addIpa;
	}
	public void setAddIpa(Double addIpa) {
		this.addIpa = addIpa;
	}
	public Double getIpaLtv2() {
		return ipaLtv2;
	}
	public void setIpaLtv2(Double ipaLtv2) {
		this.ipaLtv2 = ipaLtv2;
	}
	public Double getIpaLtv3() {
		return ipaLtv3;
	}
	public void setIpaLtv3(Double ipaLtv3) {
		this.ipaLtv3 = ipaLtv3;
	}
	public Double getIpaLtv4() {
		return ipaLtv4;
	}
	public void setIpaLtv4(Double ipaLtv4) {
		this.ipaLtv4 = ipaLtv4;
	}
	public Double getIpaLtv5() {
		return ipaLtv5;
	}
	public void setIpaLtv5(Double ipaLtv5) {
		this.ipaLtv5 = ipaLtv5;
	}
	public Double getIpaLtv6() {
		return ipaLtv6;
	}
	public void setIpaLtv6(Double ipaLtv6) {
		this.ipaLtv6 = ipaLtv6;
	}
	public Double getIpaLtv7() {
		return ipaLtv7;
	}
	public void setIpaLtv7(Double ipaLtv7) {
		this.ipaLtv7 = ipaLtv7;
	}
	public Double getIpaLtv14() {
		return ipaLtv14;
	}
	public void setIpaLtv14(Double ipaLtv14) {
		this.ipaLtv14 = ipaLtv14;
	}
	public Double getIpaLtv30() {
		return ipaLtv30;
	}
	public void setIpaLtv30(Double ipaLtv30) {
		this.ipaLtv30 = ipaLtv30;
	}
	public Double getLtv36() {
		return ltv36;
	}
	public void setLtv36(Double ltv36) {
		this.ltv36 = ltv36;
	}
	public Double getLtv42() {
		return ltv42;
	}
	public void setLtv42(Double ltv42) {
		this.ltv42 = ltv42;
	}
	public Double getLtv48() {
		return ltv48;
	}
	public void setLtv48(Double ltv48) {
		this.ltv48 = ltv48;
	}
	public Double getLtv54() {
		return ltv54;
	}
	public void setLtv54(Double ltv54) {
		this.ltv54 = ltv54;
	}
	public Double getLtv60() {
		return ltv60;
	}
	public void setLtv60(Double ltv60) {
		this.ltv60 = ltv60;
	}
	public Long getRd8() {
		return rd8;
	}
	public void setRd8(Long rd8) {
		this.rd8 = rd8;
	}
	public Long getRd9() {
		return rd9;
	}
	public void setRd9(Long rd9) {
		this.rd9 = rd9;
	}
	public Long getRd10() {
		return rd10;
	}
	public void setRd10(Long rd10) {
		this.rd10 = rd10;
	}
	public Long getRd11() {
		return rd11;
	}
	public void setRd11(Long rd11) {
		this.rd11 = rd11;
	}
	public Long getRd12() {
		return rd12;
	}
	public void setRd12(Long rd12) {
		this.rd12 = rd12;
	}
	public Long getRd13() {
		return rd13;
	}
	public void setRd13(Long rd13) {
		this.rd13 = rd13;
	}
	public Long getRd15() {
		return rd15;
	}
	public void setRd15(Long rd15) {
		this.rd15 = rd15;
	}
	public Long getRd16() {
		return rd16;
	}
	public void setRd16(Long rd16) {
		this.rd16 = rd16;
	}
	public Long getRd17() {
		return rd17;
	}
	public void setRd17(Long rd17) {
		this.rd17 = rd17;
	}
	public Long getRd18() {
		return rd18;
	}
	public void setRd18(Long rd18) {
		this.rd18 = rd18;
	}
	public Long getRd19() {
		return rd19;
	}
	public void setRd19(Long rd19) {
		this.rd19 = rd19;
	}
	public Long getRd20() {
		return rd20;
	}
	public void setRd20(Long rd20) {
		this.rd20 = rd20;
	}
	public Long getRd21() {
		return rd21;
	}
	public void setRd21(Long rd21) {
		this.rd21 = rd21;
	}
	public Long getRd22() {
		return rd22;
	}
	public void setRd22(Long rd22) {
		this.rd22 = rd22;
	}
	public Long getRd23() {
		return rd23;
	}
	public void setRd23(Long rd23) {
		this.rd23 = rd23;
	}
	public Long getRd24() {
		return rd24;
	}
	public void setRd24(Long rd24) {
		this.rd24 = rd24;
	}
	public Long getRd25() {
		return rd25;
	}
	public void setRd25(Long rd25) {
		this.rd25 = rd25;
	}
	public Long getRd26() {
		return rd26;
	}
	public void setRd26(Long rd26) {
		this.rd26 = rd26;
	}
	public Long getRd27() {
		return rd27;
	}
	public void setRd27(Long rd27) {
		this.rd27 = rd27;
	}
	public Long getRd28() {
		return rd28;
	}
	public void setRd28(Long rd28) {
		this.rd28 = rd28;
	}
	public Long getRd29() {
		return rd29;
	}
	public void setRd29(Long rd29) {
		this.rd29 = rd29;
	}
	public Double getLtv8() {
		return ltv8;
	}
	public void setLtv8(Double ltv8) {
		this.ltv8 = ltv8;
	}
	public Double getLtv9() {
		return ltv9;
	}
	public void setLtv9(Double ltv9) {
		this.ltv9 = ltv9;
	}
	public Double getLtv10() {
		return ltv10;
	}
	public void setLtv10(Double ltv10) {
		this.ltv10 = ltv10;
	}
	public Double getLtv11() {
		return ltv11;
	}
	public void setLtv11(Double ltv11) {
		this.ltv11 = ltv11;
	}
	public Double getLtv12() {
		return ltv12;
	}
	public void setLtv12(Double ltv12) {
		this.ltv12 = ltv12;
	}
	public Double getLtv13() {
		return ltv13;
	}
	public void setLtv13(Double ltv13) {
		this.ltv13 = ltv13;
	}
	public Double getLtv15() {
		return ltv15;
	}
	public void setLtv15(Double ltv15) {
		this.ltv15 = ltv15;
	}
	public Double getLtv16() {
		return ltv16;
	}
	public void setLtv16(Double ltv16) {
		this.ltv16 = ltv16;
	}
	public Double getLtv17() {
		return ltv17;
	}
	public void setLtv17(Double ltv17) {
		this.ltv17 = ltv17;
	}
	public Double getLtv18() {
		return ltv18;
	}
	public void setLtv18(Double ltv18) {
		this.ltv18 = ltv18;
	}
	public Double getLtv19() {
		return ltv19;
	}
	public void setLtv19(Double ltv19) {
		this.ltv19 = ltv19;
	}
	public Double getLtv20() {
		return ltv20;
	}
	public void setLtv20(Double ltv20) {
		this.ltv20 = ltv20;
	}
	public Double getLtv21() {
		return ltv21;
	}
	public void setLtv21(Double ltv21) {
		this.ltv21 = ltv21;
	}
	public Double getLtv22() {
		return ltv22;
	}
	public void setLtv22(Double ltv22) {
		this.ltv22 = ltv22;
	}
	public Double getLtv23() {
		return ltv23;
	}
	public void setLtv23(Double ltv23) {
		this.ltv23 = ltv23;
	}
	public Double getLtv24() {
		return ltv24;
	}
	public void setLtv24(Double ltv24) {
		this.ltv24 = ltv24;
	}
	public Double getLtv25() {
		return ltv25;
	}
	public void setLtv25(Double ltv25) {
		this.ltv25 = ltv25;
	}
	public Double getLtv26() {
		return ltv26;
	}
	public void setLtv26(Double ltv26) {
		this.ltv26 = ltv26;
	}
	public Double getLtv27() {
		return ltv27;
	}
	public void setLtv27(Double ltv27) {
		this.ltv27 = ltv27;
	}
	public Double getLtv28() {
		return ltv28;
	}
	public void setLtv28(Double ltv28) {
		this.ltv28 = ltv28;
	}
	public Double getLtv29() {
		return ltv29;
	}
	public void setLtv29(Double ltv29) {
		this.ltv29 = ltv29;
	}
	public Double getIpaLtv8() {
		return ipaLtv8;
	}
	public void setIpaLtv8(Double ipaLtv8) {
		this.ipaLtv8 = ipaLtv8;
	}
	public Double getIpaLtv9() {
		return ipaLtv9;
	}
	public void setIpaLtv9(Double ipaLtv9) {
		this.ipaLtv9 = ipaLtv9;
	}
	public Double getIpaLtv10() {
		return ipaLtv10;
	}
	public void setIpaLtv10(Double ipaLtv10) {
		this.ipaLtv10 = ipaLtv10;
	}
	public Double getIpaLtv11() {
		return ipaLtv11;
	}
	public void setIpaLtv11(Double ipaLtv11) {
		this.ipaLtv11 = ipaLtv11;
	}
	public Double getIpaLtv12() {
		return ipaLtv12;
	}
	public void setIpaLtv12(Double ipaLtv12) {
		this.ipaLtv12 = ipaLtv12;
	}
	public Double getIpaLtv13() {
		return ipaLtv13;
	}
	public void setIpaLtv13(Double ipaLtv13) {
		this.ipaLtv13 = ipaLtv13;
	}
	public Double getIpaLtv15() {
		return ipaLtv15;
	}
	public void setIpaLtv15(Double ipaLtv15) {
		this.ipaLtv15 = ipaLtv15;
	}
	public Double getIpaLtv16() {
		return ipaLtv16;
	}
	public void setIpaLtv16(Double ipaLtv16) {
		this.ipaLtv16 = ipaLtv16;
	}
	public Double getIpaLtv17() {
		return ipaLtv17;
	}
	public void setIpaLtv17(Double ipaLtv17) {
		this.ipaLtv17 = ipaLtv17;
	}
	public Double getIpaLtv18() {
		return ipaLtv18;
	}
	public void setIpaLtv18(Double ipaLtv18) {
		this.ipaLtv18 = ipaLtv18;
	}
	public Double getIpaLtv19() {
		return ipaLtv19;
	}
	public void setIpaLtv19(Double ipaLtv19) {
		this.ipaLtv19 = ipaLtv19;
	}
	public Double getIpaLtv20() {
		return ipaLtv20;
	}
	public void setIpaLtv20(Double ipaLtv20) {
		this.ipaLtv20 = ipaLtv20;
	}
	public Double getIpaLtv21() {
		return ipaLtv21;
	}
	public void setIpaLtv21(Double ipaLtv21) {
		this.ipaLtv21 = ipaLtv21;
	}
	public Double getIpaLtv22() {
		return ipaLtv22;
	}
	public void setIpaLtv22(Double ipaLtv22) {
		this.ipaLtv22 = ipaLtv22;
	}
	public Double getIpaLtv23() {
		return ipaLtv23;
	}
	public void setIpaLtv23(Double ipaLtv23) {
		this.ipaLtv23 = ipaLtv23;
	}
	public Double getIpaLtv24() {
		return ipaLtv24;
	}
	public void setIpaLtv24(Double ipaLtv24) {
		this.ipaLtv24 = ipaLtv24;
	}
	public Double getIpaLtv25() {
		return ipaLtv25;
	}
	public void setIpaLtv25(Double ipaLtv25) {
		this.ipaLtv25 = ipaLtv25;
	}
	public Double getIpaLtv26() {
		return ipaLtv26;
	}
	public void setIpaLtv26(Double ipaLtv26) {
		this.ipaLtv26 = ipaLtv26;
	}
	public Double getIpaLtv27() {
		return ipaLtv27;
	}
	public void setIpaLtv27(Double ipaLtv27) {
		this.ipaLtv27 = ipaLtv27;
	}
	public Double getIpaLtv28() {
		return ipaLtv28;
	}
	public void setIpaLtv28(Double ipaLtv28) {
		this.ipaLtv28 = ipaLtv28;
	}
	public Double getIpaLtv29() {
		return ipaLtv29;
	}
	public void setIpaLtv29(Double ipaLtv29) {
		this.ipaLtv29 = ipaLtv29;
	}
	public Double getCost() {
		return cost;
	}
	public void setCost(Double cost) {
		this.cost = cost;
	}
	public Double getIpaLtv105() {
		return ipaLtv105;
	}
	public void setIpaLtv105(Double ipaLtv105) {
		this.ipaLtv105 = ipaLtv105;
	}
	public Double getIpaLtv135() {
		return ipaLtv135;
	}
	public void setIpaLtv135(Double ipaLtv135) {
		this.ipaLtv135 = ipaLtv135;
	}
	public Double getIpaLtv150() {
		return ipaLtv150;
	}
	public void setIpaLtv150(Double ipaLtv150) {
		this.ipaLtv150 = ipaLtv150;
	}
	public Double getIpaLtv165() {
		return ipaLtv165;
	}
	public void setIpaLtv165(Double ipaLtv165) {
		this.ipaLtv165 = ipaLtv165;
	}
	public Double getIpaLtv180() {
		return ipaLtv180;
	}
	public void setIpaLtv180(Double ipaLtv180) {
		this.ipaLtv180 = ipaLtv180;
	}
	public Double getIpaLtv195() {
		return ipaLtv195;
	}
	public void setIpaLtv195(Double ipaLtv195) {
		this.ipaLtv195 = ipaLtv195;
	}
	public Double getIpaLtv210() {
		return ipaLtv210;
	}
	public void setIpaLtv210(Double ipaLtv210) {
		this.ipaLtv210 = ipaLtv210;
	}
	public Double getIpaLtv225() {
		return ipaLtv225;
	}
	public void setIpaLtv225(Double ipaLtv225) {
		this.ipaLtv225 = ipaLtv225;
	}
	public Double getIpaLtv240() {
		return ipaLtv240;
	}
	public void setIpaLtv240(Double ipaLtv240) {
		this.ipaLtv240 = ipaLtv240;
	}
	public Double getIpaLtv255() {
		return ipaLtv255;
	}
	public void setIpaLtv255(Double ipaLtv255) {
		this.ipaLtv255 = ipaLtv255;
	}
	public Double getIpaLtv270() {
		return ipaLtv270;
	}
	public void setIpaLtv270(Double ipaLtv270) {
		this.ipaLtv270 = ipaLtv270;
	}
	public Double getIpaLtv285() {
		return ipaLtv285;
	}
	public void setIpaLtv285(Double ipaLtv285) {
		this.ipaLtv285 = ipaLtv285;
	}
	public Double getIpaLtv300() {
		return ipaLtv300;
	}
	public void setIpaLtv300(Double ipaLtv300) {
		this.ipaLtv300 = ipaLtv300;
	}
	public Double getIpaLtv315() {
		return ipaLtv315;
	}
	public void setIpaLtv315(Double ipaLtv315) {
		this.ipaLtv315 = ipaLtv315;
	}
	public Double getIpaLtv330() {
		return ipaLtv330;
	}
	public void setIpaLtv330(Double ipaLtv330) {
		this.ipaLtv330 = ipaLtv330;
	}
	public Double getIpaLtv345() {
		return ipaLtv345;
	}
	public void setIpaLtv345(Double ipaLtv345) {
		this.ipaLtv345 = ipaLtv345;
	}
	public Double getIpaLtv360() {
		return ipaLtv360;
	}
	public void setIpaLtv360(Double ipaLtv360) {
		this.ipaLtv360 = ipaLtv360;
	}
	public Integer getIpaRd105() {
		return ipaRd105;
	}
	public void setIpaRd105(Integer ipaRd105) {
		this.ipaRd105 = ipaRd105;
	}
	public Integer getIpaRd135() {
		return ipaRd135;
	}
	public void setIpaRd135(Integer ipaRd135) {
		this.ipaRd135 = ipaRd135;
	}
	public Integer getIpaRd150() {
		return ipaRd150;
	}
	public void setIpaRd150(Integer ipaRd150) {
		this.ipaRd150 = ipaRd150;
	}
	public Integer getIpaRd165() {
		return ipaRd165;
	}
	public void setIpaRd165(Integer ipaRd165) {
		this.ipaRd165 = ipaRd165;
	}
	public Integer getIpaRd180() {
		return ipaRd180;
	}
	public void setIpaRd180(Integer ipaRd180) {
		this.ipaRd180 = ipaRd180;
	}
	public Integer getIpaRd195() {
		return ipaRd195;
	}
	public void setIpaRd195(Integer ipaRd195) {
		this.ipaRd195 = ipaRd195;
	}
	public Integer getIpaRd210() {
		return ipaRd210;
	}
	public void setIpaRd210(Integer ipaRd210) {
		this.ipaRd210 = ipaRd210;
	}
	public Integer getIpaRd225() {
		return ipaRd225;
	}
	public void setIpaRd225(Integer ipaRd225) {
		this.ipaRd225 = ipaRd225;
	}
	public Integer getIpaRd240() {
		return ipaRd240;
	}
	public void setIpaRd240(Integer ipaRd240) {
		this.ipaRd240 = ipaRd240;
	}
	public Integer getIpaRd255() {
		return ipaRd255;
	}
	public void setIpaRd255(Integer ipaRd255) {
		this.ipaRd255 = ipaRd255;
	}
	public Integer getIpaRd270() {
		return ipaRd270;
	}
	public void setIpaRd270(Integer ipaRd270) {
		this.ipaRd270 = ipaRd270;
	}
	public Integer getIpaRd285() {
		return ipaRd285;
	}
	public void setIpaRd285(Integer ipaRd285) {
		this.ipaRd285 = ipaRd285;
	}
	public Integer getIpaRd300() {
		return ipaRd300;
	}
	public void setIpaRd300(Integer ipaRd300) {
		this.ipaRd300 = ipaRd300;
	}
	public Integer getIpaRd315() {
		return ipaRd315;
	}
	public void setIpaRd315(Integer ipaRd315) {
		this.ipaRd315 = ipaRd315;
	}
	public Integer getIpaRd330() {
		return ipaRd330;
	}
	public void setIpaRd330(Integer ipaRd330) {
		this.ipaRd330 = ipaRd330;
	}
	public Integer getIpaRd345() {
		return ipaRd345;
	}
	public void setIpaRd345(Integer ipaRd345) {
		this.ipaRd345 = ipaRd345;
	}
	public Integer getIpaRd360() {
		return ipaRd360;
	}
	public void setIpaRd360(Integer ipaRd360) {
		this.ipaRd360 = ipaRd360;
	}
	public String getDelivery_mode() {
		return delivery_mode;
	}
	public void setDelivery_mode(String delivery_mode) {
		this.delivery_mode = delivery_mode;
	}
	public String getAnchor_related_type() {
		return anchor_related_type;
	}
	public void setAnchor_related_type(String anchor_related_type) {
		this.anchor_related_type = anchor_related_type;
	}
	public String getAdsenseType() {
		return adsenseType;
	}
	public void setAdsenseType(String adsenseType) {
		this.adsenseType = adsenseType;
	}
	public String getAppCategory() {
		return appCategory;
	}
	public void setAppCategory(String appCategory) {
		this.appCategory = appCategory;
	}

	public String getHourly() {
		return hourly;
	}

	public void setHourly(String hourly) {
		this.hourly = hourly;
	}
}
