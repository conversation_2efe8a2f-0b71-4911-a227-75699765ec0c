package com.wbgame.pojo.mobile;

import com.wbgame.pojo.CommonConfigInfo;

/**
 * <AUTHOR>
 * @Classname InviteCheckConfigVo
 * @Description TODO
 * @Date 2021/7/7 10:59
 */
public class InviteCheckConfigVo extends CommonConfigInfo {

    private String id;
    private String prjid;
    private String bindCheck3;       //绑定规则 0,关闭  1,新用户  2,老用户 3,所有
    private String bindLimitCheck;   //绑定上限开关
    private String bindLimitNum;    //绑定人数上限
    private String devoteCheck1;     //收益开关 视频广告
    private String devoteCheck2;     //收益开关 插屏广告
    private String devoteCheck4;     //收益开关 邀请与被邀请收益
    private String withdrawType;     //提现方式
    private String popCheck;         //每次登录弹窗开关
    private String hbLimit;         //好友邀请红包总限额
    private String tips;             //赚钱攻略

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPrjid() {
        return prjid;
    }

    public void setPrjid(String prjid) {
        this.prjid = prjid;
    }

    public String getBindCheck3() {
        return bindCheck3;
    }

    public void setBindCheck3(String bindCheck3) {
        this.bindCheck3 = bindCheck3;
    }

    public String getBindLimitCheck() {
        return bindLimitCheck;
    }

    public void setBindLimitCheck(String bindLimitCheck) {
        this.bindLimitCheck = bindLimitCheck;
    }

    public String getBindLimitNum() {
        return bindLimitNum;
    }

    public void setBindLimitNum(String bindLimitNum) {
        this.bindLimitNum = bindLimitNum;
    }

    public String getDevoteCheck1() {
        return devoteCheck1;
    }

    public void setDevoteCheck1(String devoteCheck1) {
        this.devoteCheck1 = devoteCheck1;
    }

    public String getDevoteCheck2() {
        return devoteCheck2;
    }

    public void setDevoteCheck2(String devoteCheck2) {
        this.devoteCheck2 = devoteCheck2;
    }

    public String getDevoteCheck4() {
        return devoteCheck4;
    }

    public void setDevoteCheck4(String devoteCheck4) {
        this.devoteCheck4 = devoteCheck4;
    }

    public String getWithdrawType() {
        return withdrawType;
    }

    public void setWithdrawType(String withdrawType) {
        this.withdrawType = withdrawType;
    }

    public String getPopCheck() {
        return popCheck;
    }

    public void setPopCheck(String popCheck) {
        this.popCheck = popCheck;
    }

    public String getHbLimit() {
        return hbLimit;
    }

    public void setHbLimit(String hbLimit) {
        this.hbLimit = hbLimit;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }
}
