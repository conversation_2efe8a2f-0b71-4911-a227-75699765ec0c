package com.wbgame.pojo.mobile;

/**
 * 分渠道全部参数配置
 * <AUTHOR>
 *
 */
public class DnwxAllparamConfig {
	
	/** 唯一标识 */
    private String id;
    private String pid;
    private String appid;
    private String cha_id;
    
    /** 策略名 */
    private String mid;
    /** 策略名称 */
    private String mname;
    private String stime;
    private String etime;
    private String weeks;
    private String wtime;
    private String status;
    
    
    /** 地区， 省级加广深  */
    private String area;
    
    /**统计测试模式(tjTestModel) -下拉框,{0:正式模式}、{1,测试模式}，默认为正式模式*/
    private String tjTestModel;
    /**用户协议开关（agreementFlag）-下拉框,{0:不弹出}、{1:弹出-关闭进入}、{2:弹出-同意进入},默认不弹出*/
    private String agreementFlag;
    /**用户协议(agreementUrl) -输入框，默认值https://pro.77pin.net/pro/agreement.html*/
    private String agreementUrl;
    /**隐私政策(policyUrl) -输入框,默认值https://pro.77pin.net/pro/statement.htmls*/
    private String policyUrl;
    /** 自定义事件上报开关 */
    private String reportFlag;
    /**刷新间隔，默认值10800  */
    private String update;
    /** 强制登录：0-关闭 1-开启 */
    private String hw_login;
    /** 无卡屏蔽开关，默认关闭：0-关闭 1-开启，开启状态，针对客户端上报无sim卡的设备屏蔽广告链接 */
    private String sim_filter;
    /** 移动积分开关：0-关闭 1-开启 */
    private String redeem;
    /** 渠道登录开关：0-关闭 1-开启 */
    private String clogin;
    /** 应用审核开关，0:正常状态，1:审核状态-全屏蔽，2:审核状态-仅收集设备，3:审核状态-仅屏蔽广告，4:审核状态-仅屏蔽锁屏 */
    private String app_audit;
    
    /** 适龄开关，默认关闭， 0：关闭；1：8+；2：12+；3：16+ */
	private String sl;
	/** 适龄描述 */
	private String slMsg;
	/** 虚拟已实名认证开关：0-关闭 1-开启 */
	private String healthFlag;
	/** 广告合规开关：0-不合规 1-合规 */
	private String standard;
	/** 合规模块：全量-all，投放买量-1，渠道自然量-2，自推广自然量-3 */
	private String standard_type;
	
	
	/** 审核开关 */
	private int audit = 0;
	/** 大转盘 */
	private int lottery  = 0;
	/** 提现开关 */
	private int nofee  = 0;
	/** 热更开关 */
	private int assets  = 0;
	/** 自然量提现开关 */
	private int city  = 0;
	/** 广告开关 */
	private int advert  = 0;
	/** 防沉迷开关 */
	private int antiAddiction  = 0;
	/** 实名认证开关：0-关闭，1-开启，2-强制认证，3-强制认证-可关闭  */
	private int mmnotify = 0;
	/** 营销模块开关：0-关闭 1-开启 */
	private int lock = 0;
	/** 用户画像上报开关：0-关闭 1-开启，默认逗号隔开三个开启 1,1,1 */
	private String persona;
	/** 视频logo开关：0-关闭 1-开启，默认开启 */
	private String vlogo;
	/** 自动拉起功能（0：关闭   1：开启 ，默认关闭 ） */
	private String atLaunch;
	/** 自动拉回功能（0：关闭   1：开启，默认关闭） */
	private String atBack;
	/** x4功能开关，（0：关闭   1：开启） */
	private String atClick;
	/** 合规自定义开关，输入json字符 */
	private String safeCustomer;
	/** 非合规自定义开关，输入json字符 */
	private String nosafeCustomer;
	/** 认证类型，1-按设备关联、2-按设备&产品关联 默认2 */
	private String auth_type;
	
	
	private String note;
	/**
	 * 最后修改人
	 */
	private String euser;

	/**
	 * 最后修改时间
	 */
	private String endtime;
	/**
	 * 创建时间
	 */
	private String createtime;

	/**
	 * 创建人
	 */
	private String cuser;

	

	public String getEndtime() {
		return endtime;
	}

	public void setEndtime(String endtime) {
		this.endtime = endtime;
	}

	public String getCreatetime() {
		return createtime;
	}

	public void setCreatetime(String createtime) {
		this.createtime = createtime;
	}

	public String getTjTestModel() {
		return tjTestModel;
	}
	public void setTjTestModel(String tjTestModel) {
		this.tjTestModel = tjTestModel;
	}
	public String getAgreementFlag() {
		return agreementFlag;
	}
	public void setAgreementFlag(String agreementFlag) {
		this.agreementFlag = agreementFlag;
	}
	public String getAgreementUrl() {
		return agreementUrl;
	}
	public void setAgreementUrl(String agreementUrl) {
		this.agreementUrl = agreementUrl;
	}
	public String getPolicyUrl() {
		return policyUrl;
	}
	public void setPolicyUrl(String policyUrl) {
		this.policyUrl = policyUrl;
	}
	public String getUpdate() {
		return update;
	}
	public void setUpdate(String update) {
		this.update = update;
	}
	public String getHw_login() {
		return hw_login;
	}
	public void setHw_login(String hw_login) {
		this.hw_login = hw_login;
	}
	public String getSim_filter() {
		return sim_filter;
	}
	public void setSim_filter(String sim_filter) {
		this.sim_filter = sim_filter;
	}
	public String getRedeem() {
		return redeem;
	}
	public void setRedeem(String redeem) {
		this.redeem = redeem;
	}
	public String getClogin() {
		return clogin;
	}
	public void setClogin(String clogin) {
		this.clogin = clogin;
	}
	public String getApp_audit() {
		return app_audit;
	}
	public void setApp_audit(String app_audit) {
		this.app_audit = app_audit;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getPid() {
		return pid;
	}
	public void setPid(String pid) {
		this.pid = pid;
	}
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getCha_id() {
		return cha_id;
	}
	public void setCha_id(String cha_id) {
		this.cha_id = cha_id;
	}
	public String getArea() {
		return area;
	}
	public void setArea(String area) {
		this.area = area;
	}
	public String getReportFlag() {
		return reportFlag;
	}
	public void setReportFlag(String reportFlag) {
		this.reportFlag = reportFlag;
	}
	public String getSl() {
		return sl;
	}
	public void setSl(String sl) {
		this.sl = sl;
	}
	public String getSlMsg() {
		return slMsg;
	}
	public void setSlMsg(String slMsg) {
		this.slMsg = slMsg;
	}
	public String getHealthFlag() {
		return healthFlag;
	}
	public void setHealthFlag(String healthFlag) {
		this.healthFlag = healthFlag;
	}
	public int getAudit() {
		return audit;
	}
	public void setAudit(int audit) {
		this.audit = audit;
	}
	public int getLottery() {
		return lottery;
	}
	public void setLottery(int lottery) {
		this.lottery = lottery;
	}
	public int getNofee() {
		return nofee;
	}
	public void setNofee(int nofee) {
		this.nofee = nofee;
	}
	public int getAssets() {
		return assets;
	}
	public void setAssets(int assets) {
		this.assets = assets;
	}
	public int getCity() {
		return city;
	}
	public void setCity(int city) {
		this.city = city;
	}
	public int getAdvert() {
		return advert;
	}
	public void setAdvert(int advert) {
		this.advert = advert;
	}
	public int getAntiAddiction() {
		return antiAddiction;
	}
	public void setAntiAddiction(int antiAddiction) {
		this.antiAddiction = antiAddiction;
	}
	public int getMmnotify() {
		return mmnotify;
	}
	public void setMmnotify(int mmnotify) {
		this.mmnotify = mmnotify;
	}
	public String getStandard() {
		return standard;
	}
	public void setStandard(String standard) {
		this.standard = standard;
	}
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}
	
	public String getCuser() {
		return cuser;
	}
	public void setCuser(String cuser) {
		this.cuser = cuser;
	}
	public String getEuser() {
		return euser;
	}
	public void setEuser(String euser) {
		this.euser = euser;
	}
	public String getStandard_type() {
		return standard_type;
	}
	public void setStandard_type(String standard_type) {
		this.standard_type = standard_type;
	}
	public String getMid() {
		return mid;
	}
	public void setMid(String mid) {
		this.mid = mid;
	}
	public String getMname() {
		return mname;
	}
	public void setMname(String mname) {
		this.mname = mname;
	}
	public String getStime() {
		return stime;
	}
	public void setStime(String stime) {
		this.stime = stime;
	}
	public String getEtime() {
		return etime;
	}
	public void setEtime(String etime) {
		this.etime = etime;
	}
	public String getWeeks() {
		return weeks;
	}
	public void setWeeks(String weeks) {
		this.weeks = weeks;
	}
	public String getWtime() {
		return wtime;
	}
	public void setWtime(String wtime) {
		this.wtime = wtime;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public int getLock() {
		return lock;
	}
	public void setLock(int lock) {
		this.lock = lock;
	}
	public String getPersona() {
		return persona;
	}
	public void setPersona(String persona) {
		this.persona = persona;
	}
	public String getVlogo() {
		return vlogo;
	}
	public void setVlogo(String vlogo) {
		this.vlogo = vlogo;
	}
	public String getAtLaunch() {
		return atLaunch;
	}
	public void setAtLaunch(String atLaunch) {
		this.atLaunch = atLaunch;
	}
	public String getAtBack() {
		return atBack;
	}
	public void setAtBack(String atBack) {
		this.atBack = atBack;
	}
	public String getAtClick() {
		return atClick;
	}
	public void setAtClick(String atClick) {
		this.atClick = atClick;
	}
	public String getSafeCustomer() {
		return safeCustomer;
	}
	public void setSafeCustomer(String safeCustomer) {
		this.safeCustomer = safeCustomer;
	}
	public String getNosafeCustomer() {
		return nosafeCustomer;
	}
	public void setNosafeCustomer(String nosafeCustomer) {
		this.nosafeCustomer = nosafeCustomer;
	}
	public String getAuth_type() {
		return auth_type;
	}
	public void setAuth_type(String auth_type) {
		this.auth_type = auth_type;
	}

}