package com.wbgame.pojo.mobile;

import java.util.List;
import java.util.Objects;

/**
    * 事件数据源
    */
public class ApkEvent {
    /**
    * 事件
    */
    private String event;

    /**
     * 1 预置指标 2 预置参数
     */
    private String type;

    /**
     * 字段
     */
    private String field;

    /**
     * 方式：例如去重数
     */
    private String mode;

    //条件集合
    private List<List<WhereParam>> whereParams;

    /**
     * 条件类型   且 and  或 or
     */
    private String whereType;

    /**
    * 是否需要同步事件 1 定时抽取  0  不做处理
    */
    private Integer isSyn;

    /**
     * appkey
     */
    private String appKey;


    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public Integer getIsSyn() {
        return isSyn;
    }

    public void setIsSyn(Integer isSyn) {
        this.isSyn = isSyn;
    }

    /**
     * 重写equal和hashcode方法
     *
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ApkEvent apkEvent = (ApkEvent) o;
        return Objects.equals(event, apkEvent.event) &&
                Objects.equals(type, apkEvent.type) &&
                Objects.equals(field,apkEvent.field) &&
                Objects.equals(mode,apkEvent.mode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(event, type);
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getWhereType() {
        return whereType;
    }

    public void setWhereType(String whereType) {
        this.whereType = whereType;
    }

    public List<List<WhereParam>> getWhereParams() {
        return whereParams;
    }

    public void setWhereParams(List<List<WhereParam>> whereParams) {
        this.whereParams = whereParams;
    }
}