package com.wbgame.pojo;

public class SysTempVo {

    private String user_id;// 用户账号
    private String login_name;
    private String password;
    private String nick_name; // 昵称
    private String org_id; // 所属组ID
    private String org_name; // 组名称
    private String role_id; // 角色ID
    private String role_name; // 角色名称
    private String company; // 公司
    private String menu_group;
    private String token;
    private String app_group; // 小游戏运营后台
    private String app_group_b; // 移动运营后台
    private String app_group_c; // 广告统计后台

    private String platform_id; // 平台ID
    private String client_ip;
    private String sys_type;// 菜单类型
    private String hidden_menu_list;// 隐藏菜单
    private String page_list;// 授权菜单列表
    private String orderTime;// 更新时间
    private String index;// 菜单主键
    private String title;// 菜单标题
    private String off;// 菜单开关
    private String icon;// 菜单图标
    private String slot;// 菜单插槽
    private String upTime;// 菜单更新时间
    private String list_number;// 菜单排序

    private String sys_mark;// 系统标识
    private String name;// 附标题
    private String text;// 描述文字
    private String tip;// 提示
    private String version;// 版本号
    private String verify_link;// 主连接
    private String link;// 主连接
    private String dep_verify_link;// 测试连接
    private String dep_link;// 测试连接
    private String test_verify_link;// 开发连接
    private String test_link;// 开发连接
    private String user;// 使用
    private String hidden;// 隐藏

    public String getLogin_name() {
        return login_name;
    }

    public String getTest_verify_link() {
		return test_verify_link;
	}

	public void setTest_verify_link(String test_verify_link) {
		this.test_verify_link = test_verify_link;
	}

	public String getDep_verify_link() {
		return dep_verify_link;
	}

	public void setDep_verify_link(String dep_verify_link) {
		this.dep_verify_link = dep_verify_link;
	}

	public String getVerify_link() {
		return verify_link;
	}

	public void setVerify_link(String verify_link) {
		this.verify_link = verify_link;
	}

	public String getSys_mark() {
		return sys_mark;
	}

	public void setSys_mark(String sys_mark) {
		this.sys_mark = sys_mark;
	}

	public String getHidden() {
        return hidden;
    }

    public void setHidden(String hidden) {
        this.hidden = hidden;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getTest_link() {
        return test_link;
    }

    public void setTest_link(String test_link) {
        this.test_link = test_link;
    }

    public String getDep_link() {
        return dep_link;
    }

    public void setDep_link(String dep_link) {
        this.dep_link = dep_link;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getTip() {
        return tip;
    }

    public void setTip(String tip) {
        this.tip = tip;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getList_number() {
        return list_number;
    }

    public void setList_number(String list_number) {
        this.list_number = list_number;
    }

    public String getUpTime() {
        return upTime;
    }

    public void setUpTime(String upTime) {
        this.upTime = upTime;
    }

    public String getSlot() {
        return slot;
    }

    public void setSlot(String slot) {
        this.slot = slot;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getOff() {
        return off;
    }

    public void setOff(String off) {
        this.off = off;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(String orderTime) {
        this.orderTime = orderTime;
    }

    public String getPage_list() {
        return page_list;
    }

    public void setPage_list(String page_list) {
        this.page_list = page_list;
    }

    public String getHidden_menu_list() {
        return hidden_menu_list;
    }

    public void setHidden_menu_list(String hidden_menu_list) {
        this.hidden_menu_list = hidden_menu_list;
    }

    public String getSys_type() {
        return sys_type;
    }

    public void setSys_type(String sys_type) {
        this.sys_type = sys_type;
    }

    public void setLogin_name(String login_name) {
		this.login_name = login_name;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public String getNick_name() {
		return nick_name;
	}
	public void setNick_name(String nick_name) {
		this.nick_name = nick_name;
	}
	public String getOrg_id() {
		return org_id;
	}
	public void setOrg_id(String org_id) {
		this.org_id = org_id;
	}
	public String getOrg_name() {
		return org_name;
	}
	public void setOrg_name(String org_name) {
		this.org_name = org_name;
	}
	public String getRole_id() {
		return role_id;
	}
	public void setRole_id(String role_id) {
		this.role_id = role_id;
	}
	public String getRole_name() {
		return role_name;
	}
	public void setRole_name(String role_name) {
		this.role_name = role_name;
	}
	public String getCompany() {
		return company;
	}
	public void setCompany(String company) {
		this.company = company;
	}
	public String getMenu_group() {
		return menu_group;
	}
	public void setMenu_group(String menu_group) {
		this.menu_group = menu_group;
	}
	public String getApp_group() {
		return app_group;
	}
	public void setApp_group(String app_group) {
		this.app_group = app_group;
	}
	public String getToken() {
		return token;
	}
	public void setToken(String token) {
		this.token = token;
	}
	public String getPlatform_id() {
		return platform_id;
	}
	public void setPlatform_id(String platform_id) {
		this.platform_id = platform_id;
	}
	public String getClient_ip() {
		return client_ip;
	}
	public void setClient_ip(String client_ip) {
		this.client_ip = client_ip;
	}
	public String getApp_group_b() {
		return app_group_b;
	}
	public void setApp_group_b(String app_group_b) {
		this.app_group_b = app_group_b;
	}
	public String getApp_group_c() {
		return app_group_c;
	}
	public void setApp_group_c(String app_group_c) {
		this.app_group_c = app_group_c;
	}

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }
}