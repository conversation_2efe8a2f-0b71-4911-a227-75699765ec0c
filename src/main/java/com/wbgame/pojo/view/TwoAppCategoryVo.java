package com.wbgame.pojo.view;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "产品二级分类视图")
public class TwoAppCategoryVo {

    @ApiModelProperty(value = "二级分类id",dataType = "String")
    private String id;

    @ApiModelProperty(value = "二级分类名称",dataType = "String")
    private String name;

    @ApiModelProperty(value = "备注",dataType = "String")
    private String remark;

    @ApiModelProperty(value = "创建时间",dataType = "String")
    private String create_time;

    @ApiModelProperty(value = "更新时间",dataType = "String")
    private String update_time;

    @ApiModelProperty(value = "创建人",dataType = "String")
    private String create_owner;

    @ApiModelProperty(value = "更新人",dataType = "String")
    private String update_owner;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public String getCreate_owner() {
        return create_owner;
    }

    public void setCreate_owner(String create_owner) {
        this.create_owner = create_owner;
    }

    public String getUpdate_owner() {
        return update_owner;
    }

    public void setUpdate_owner(String update_owner) {
        this.update_owner = update_owner;
    }
}
