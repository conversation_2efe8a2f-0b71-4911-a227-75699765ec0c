package com.wbgame.pojo.view;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "支付退款视图")
public class PayRefundInfoVo {

    @ApiModelProperty(value = "日期",dataType = "String")
    private String success_date;

    @ApiModelProperty(value = "应用",dataType = "String")
    private String appid;

    @ApiModelProperty(value = "应用名称",dataType = "String")
    private String app_name;

    @ApiModelProperty(value = "项目ID",dataType = "String")
    private String pid;

    @ApiModelProperty(value = "支付方式",dataType = "String")
    private String paytype;

    @ApiModelProperty(value = "应用分类",dataType = "String")
    private String app_category;

    @ApiModelProperty(value = "付费金额",dataType = "String")
    private String amount;

    @ApiModelProperty(value = "付费人数",dataType = "String")
    private String users;

    @ApiModelProperty(value = "付费次数",dataType = "String")
    private String number;

    @ApiModelProperty(value = "子渠道",dataType = "String")
    private String chaid;

    @ApiModelProperty(value = "商户号",dataType = "String")
    private String mchid;

    public String getSuccess_date() {
        return success_date;
    }

    public void setSuccess_date(String success_date) {
        this.success_date = success_date;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getPaytype() {
        return paytype;
    }

    public void setPaytype(String paytype) {
        this.paytype = paytype;
    }

    public String getApp_category() {
        return app_category;
    }

    public void setApp_category(String app_category) {
        this.app_category = app_category;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getUsers() {
        return users;
    }

    public void setUsers(String users) {
        this.users = users;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getChaid() {
        return chaid;
    }

    public void setChaid(String chaid) {
        this.chaid = chaid;
    }

    public String getMchid() {
        return mchid;
    }

    public void setMchid(String mchid) {
        this.mchid = mchid;
    }

    public String getApp_name() {
        return app_name;
    }

    public void setApp_name(String app_name) {
        this.app_name = app_name;
    }
}
