package com.wbgame.pojo.adv2.adcodeModule;

import com.wbgame.pojo.adv2.CSJAdcodeVo;
import com.wbgame.pojo.adv2.YLHAdcodeVo;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/10
 * @description
 **/
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel("ylh 模板类")
public class YLHAdcodeModuleVo extends CommonAdcodeModuleVo{

    @ApiModelProperty("sdk广告类型")
    private String sdk_ad_type;     // sdk广告类型
    @ApiModelProperty("广告使用类型")
    private String open_type;     // 广告使用类型
    @ApiModelProperty("广告策略")
    private String strategy;     // 广告策略
    @ApiModelProperty("代码位类型")
    private String scene;     // 代码位类型
    @ApiModelProperty("接入方式")
    private String ad_pull_mode;     // 接入方式
    @ApiModelProperty("渲染⽅式")
    private String render_type;     // 渲染⽅式
    @ApiModelProperty("自渲染样式")
    private String ad_crt_normal_type;
    @ApiModelProperty("自渲染样式 替换原有的 ad_crt_normal_type 字段")
    private String ad_crt_normal_types;     // 自渲染样式
    @ApiModelProperty("模版样式")
    private String ad_crt_template_type;     // 模版样式
    @ApiModelProperty("素材类型")
    private String ad_crt_type_list;     // 素材类型
    @ApiModelProperty("开屏广告样式")
    private String flash_crt_type;     // 开屏广告样式
    @ApiModelProperty("回调设置")
    private String need_server_verify;     // 回调设置
    @ApiModelProperty("激励场景")
    private String rewarded_video_scene;     // 激励场景
    @ApiModelProperty("激励样式")
    private String rewarded_video_crt_type;     // 激励样式
    @ApiModelProperty("回调url")
    private String transfer_url;     // 回调url
    @ApiModelProperty("校验密钥")
    private String secret;     // 校验密钥
    @ApiModelProperty("是否开启激励")
    private String is_open_rewarded;     // 是否开启激励
    @ApiModelProperty("广告位id")
    private String placement_id;     // 广告位id
    @ApiModelProperty("代码位名称")
    private String placement_name;     // 代码位名称
    @ApiModelProperty("价格策略")
    private String price_strategy_type;     // 价格策略
    @ApiModelProperty("实时竞价类型")
    private String real_time_bidding_type;     // 实时竞价类型
//    private String ecpm_price;     // cpm设置

    @ApiModelProperty("创建人")
    private String createUser; //  '创建人';
    @ApiModelProperty("创建时间")
    private String createTime; //  '创建时间';
    @ApiModelProperty("最后操作人")
    private String modifyUser; // '最后操作人';
    @ApiModelProperty("最后操作时间")
    private String modifyTime; // '最后操作时间';
    @ApiModelProperty("自定义参数")
    private String params; // '自定义参数';
    @ApiModelProperty("模块名称")
    private String module_name;
    @ApiModelProperty("平台")
    private String platform;

    public List<YLHAdcodeVo> transferToAdcodeVo(String appName, String appid, String channel, String adExtensionName,String groupName) {
        Integer adCodeNum = this.getAdCodeNum();
        String cpmFormula = this.getCpmFormula();
        String cpmStr = this.getCpmStr();
        appName = appName.replace("-", "").replace(" ", "");
        List<Integer> cpmList = new ArrayList<>(adCodeNum);
        if (BlankUtils.isNotBlank(cpmFormula)) {
            String[] split = cpmFormula.split(",");
            int start = Integer.parseInt(split[0]);
            int step = Integer.parseInt(split[1]);
            for (int i = 0; i < adCodeNum; i++) {
                cpmList.add(start + step * i);
            }
        } else if (BlankUtils.isNotBlank(cpmStr)) {
            cpmList = Arrays.stream(cpmStr.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        }

        List<YLHAdcodeVo> vos = new ArrayList<>(adCodeNum);
        this.placement_name = appName + "-" + this.placement_name + "-";
        if ("Client_Bidding".equals(this.real_time_bidding_type)) {
            adExtensionName += "bidding";
        }

        for (int i = 0; i < adCodeNum; i++) {
            YLHAdcodeVo vo = new YLHAdcodeVo();
            vo.setAppid(appid);
            vo.setChannel(channel);
            vo.setSdk_ad_type(this.sdk_ad_type);
            vo.setOpen_type(this.open_type);
            vo.setStrategy(this.strategy);
            vo.setReal_time_bidding_type(this.real_time_bidding_type);
            if (!CollectionUtils.isEmpty(cpmList)) {
                vo.setEcpm_price(cpmList.get(i).toString());
                vo.setAdExtensionName(Optional.ofNullable(this.getExName()).orElse("") + adExtensionName + cpmList.get(i));
                vo.setPlacement_name(this.placement_name + vo.getAdExtensionName());
            } else {
                vo.setAdExtensionName(Optional.ofNullable(this.getExName()).orElse("") + adExtensionName + i);
                vo.setPlacement_name(this.placement_name + vo.getAdExtensionName());
            }
            vo.setScene(this.scene);
            vo.setAd_pull_mode(this.ad_pull_mode);
            vo.setRender_type(this.render_type);
            vo.setAd_crt_normal_type(this.ad_crt_normal_type);
            vo.setAd_crt_normal_types(this.ad_crt_normal_types);
            vo.setAd_crt_template_type(this.ad_crt_template_type);
            vo.setAd_crt_type_list(this.ad_crt_type_list);
//            vo.setVideo_voice_control(this.video_voice_control);
            vo.setFlash_crt_type(this.flash_crt_type);
            vo.setNeed_server_verify(this.need_server_verify);
            vo.setRewarded_video_scene(this.rewarded_video_scene);
            vo.setRewarded_video_crt_type(this.rewarded_video_crt_type);
            vo.setTransfer_url(this.transfer_url);
            vo.setSecret(this.secret);
            vo.setIs_open_rewarded(this.is_open_rewarded);
            vo.setPrice_strategy_type(this.price_strategy_type);
//            vo.setReal_time_bidding_type(this.real_time_bidding_type);

//            // csj 的规则 不能接收0，所以，填0进来的时候把cpm设置为   ""
            if ("0".equals(vo.getEcpm_price())) {
                vo.setEcpm_price("");
                vo.setPrice_strategy_type("");
            }
            vo.setParams(this.params);
            //封装创建记录 格式： 模板组 + 模板
            String create_record = "";
            if (!StringUtils.isEmpty(groupName)) {
                create_record = "模板组" + groupName;
            }
            if (!StringUtils.isEmpty(this.platform) && !StringUtils.isEmpty(this.module_name)) {
                create_record += (StringUtils.isEmpty(create_record) ? "模板" : "--模板") + this.platform + "-" + this.module_name;
            }
            vo.setCreate_record(create_record);

            vos.add(vo);
        }
        return vos;
    }
}
