package com.wbgame.pojo.adv2.bigdata;

import com.wbgame.pojo.PageSizeParam;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/4/22 17:22
 */
@Data
public class UserRevenueOverseaParam extends PageSizeParam {


    /**
     * 开始时间
     */
    private String start_date;


    /**
     * 结束时间
     */
    private String end_date;

    private String appGroup;

    /**
     * 动能appid
     */
    private String appid;

    /**
     * 应用分类
     */
    private String app_category;


    private String prjid;

    /**
     * 子渠道
     */
    private String channel;

    /**
     * 国家
     */
    private String country;

    /**
     * 用户类型(输入add)
     */
    private String user_type;


    private String user;

    /**
     * 广告位类型
     */
    private String adpos_type;

    /**
     * 排序
     */
    private String order_str;


    /**
     * 渠道产品自定义分组
     */
    private String appid_tag;

    /**
     * 是否反选
     */
    private String appid_tag_rev;

    private String adpos_type_group;

    /**
     * 分组维度
     */
    private String group;


    /**
     * 是否请求抖音数据
     */
    private String isDouyin;

    /**
     * 导出字段
     */
    private String value;

    /**
     * 导出文件名
     */
    private String export_file_name;

}
