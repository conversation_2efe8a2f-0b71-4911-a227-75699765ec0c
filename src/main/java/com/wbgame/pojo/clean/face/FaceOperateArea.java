package com.wbgame.pojo.clean.face;

import com.wbgame.common.GenericQueryParameters;
import com.wbgame.common.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.groups.Default;
import java.util.List;

@ApiModel("商品:运营地区")
public class FaceOperateArea extends GenericQueryParameters {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("运营地区名")
    private String operateAreaName;

    @ApiModelProperty("地区")
    @NotBlank(message = "地区不能为空", groups = {Default.class, UpdateGroup.class})
    private String operateArea;

    @ApiModelProperty("描述")
    private String operateAreaDesc;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("修改人")
    private String updateUser;

    @ApiModelProperty("修改时间")
    private String updateTime;

    @ApiModelProperty("地区详情")
    private List<FaceOperateArea> areaList;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOperateAreaName() {
        return operateAreaName;
    }

    public void setOperateAreaName(String operateAreaName) {
        this.operateAreaName = operateAreaName == null ? null : operateAreaName.trim();
    }

    public String getOperateArea() {
        return operateArea;
    }

    public void setOperateArea(String operateArea) {
        this.operateArea = operateArea == null ? null : operateArea.trim();
    }

    public String getOperateAreaDesc() {
        return operateAreaDesc;
    }

    public void setOperateAreaDesc(String operateAreaDesc) {
        this.operateAreaDesc = operateAreaDesc == null ? null : operateAreaDesc.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public List<FaceOperateArea> getAreaList() {
        return areaList;
    }

    public void setAreaList(List<FaceOperateArea> areaList) {
        this.areaList = areaList;
    }
}