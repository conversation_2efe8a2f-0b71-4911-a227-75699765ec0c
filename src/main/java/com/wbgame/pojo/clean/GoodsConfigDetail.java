package com.wbgame.pojo.clean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("商品明细")
public class GoodsConfigDetail {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("'商品配置表-主键ID'")
    private Long configId;

    @ApiModelProperty("'分组名称'")
    private String groupName;

    @ApiModelProperty("'计费点名称'")
    private String chargeName;

    @ApiModelProperty("'商品id'")
    private String goodId;

    @ApiModelProperty("'创建人'")
    private String createUser;

    @ApiModelProperty("'创建时间(毫秒时间戳)'")
    private Long createTime;

    @ApiModelProperty("'修改人'")
    private String modifyUser;

    @ApiModelProperty("'修改时间(毫秒时间戳)'")
    private Long modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getConfigId() {
        return configId;
    }

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName == null ? null : groupName.trim();
    }

    public String getChargeName() {
        return chargeName;
    }

    public void setChargeName(String chargeName) {
        this.chargeName = chargeName == null ? null : chargeName.trim();
    }

    public String getGoodId() {
        return goodId;
    }

    public void setGoodId(String goodId) {
        this.goodId = goodId == null ? null : goodId.trim();
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser == null ? null : modifyUser.trim();
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }
}