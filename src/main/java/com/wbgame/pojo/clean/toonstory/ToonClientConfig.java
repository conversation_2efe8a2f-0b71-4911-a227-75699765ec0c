package com.wbgame.pojo.clean.toonstory;

import com.wbgame.common.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

@ApiModel("客户端资源配置")
public class ToonClientConfig {

    @ApiModelProperty("主键ID")
    @NotNull(message = "资源id错误", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty("渠道 1 海外安卓, 2 海外IOS, 3 国内安卓 , 4 国内IOS")
    private Byte configType;

    @ApiModelProperty("订阅页视频")
    @NotNull(message = "订阅页视频不能为空", groups = {UpdateGroup.class})
    private String subscriptionPage;

    @ApiModelProperty("模板ID")
    @NotNull(message = "模板ID错误", groups = {UpdateGroup.class})
    private Long modelId;

    private String modifyUser;

    private Long modifyTime;

    @ApiModelProperty("买量用户展示开关 0 关 1 开")
    private Byte bynouOffOn;

    @ApiModelProperty("买量用户展示开关 0 关 1 开")
    private Byte subGuide;

    public Byte getSubGuide() {
        return subGuide;
    }

    public void setSubGuide(Byte subGuide) {
        this.subGuide = subGuide;
    }

    public Byte getBynouOffOn() {
        return bynouOffOn;
    }

    public void setBynouOffOn(Byte bynouOffOn) {
        this.bynouOffOn = bynouOffOn;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Byte getConfigType() {
        return configType;
    }

    public void setConfigType(Byte configType) {
        this.configType = configType;
    }

    public String getSubscriptionPage() {
        return subscriptionPage;
    }

    public void setSubscriptionPage(String subscriptionPage) {
        this.subscriptionPage = subscriptionPage == null ? null : subscriptionPage.trim();
    }

    public Long getModelId() {
        return modelId;
    }

    public void setModelId(Long modelId) {
        this.modelId = modelId;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser == null ? null : modifyUser.trim();
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }
}