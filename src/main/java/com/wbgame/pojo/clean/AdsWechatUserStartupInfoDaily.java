package com.wbgame.pojo.clean;

import com.wbgame.common.GeneralReportParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel("小游戏在线时长和启动次数")
public class AdsWechatUserStartupInfoDaily extends GeneralReportParam {


    @ApiModelProperty("")
    private Long id;

    @ApiModelProperty("日期")
    private String tdate;

    @ApiModelProperty("应用id")
    private String appid;

    @ApiModelProperty("媒体")
    private String ad_buy_media;

    @ApiModelProperty("应用名称")
    private String appName;

    @ApiModelProperty("在线时长")
    private BigDecimal onlineTimeAvgNew;

    @ApiModelProperty("在线时长")
    private BigDecimal onlineStartupMedianNew;

    @ApiModelProperty("启动次数")
    private BigDecimal onlineStartupAvgNew;

    @ApiModelProperty("在线时长中位数")
    private BigDecimal onlineTimeMedianNew;

    @ApiModelProperty("在线时长")
    private BigDecimal onlineTimeAvgOld;

    @ApiModelProperty("启动次数中位数")
    private BigDecimal onlineStartupMedianOld;

    @ApiModelProperty("启动次数")
    private BigDecimal onlineStartupAvgOld;

    @ApiModelProperty("在线时长中位数")
    private BigDecimal onlineTimeMedianOld;

    @ApiModelProperty("投放媒体")
    private String adBuyMedia;

    @ApiModelProperty("单次在线时长new")
    private String onlineTimeNew;

    @ApiModelProperty("单次在线时长old")
    private String onlineTimeOld;

    @ApiModelProperty("新用户人数")
    private BigDecimal regUserCnt;

    @ApiModelProperty("老用户人数")
    private BigDecimal oldUserCnt;

    public BigDecimal getRegUserCnt() {
        return regUserCnt;
    }

    public void setRegUserCnt(BigDecimal regUserCnt) {
        this.regUserCnt = regUserCnt;
    }

    public BigDecimal getOldUserCnt() {
        return oldUserCnt;
    }

    public void setOldUserCnt(BigDecimal oldUserCnt) {
        this.oldUserCnt = oldUserCnt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public BigDecimal getOnlineTimeAvgNew() {
        return onlineTimeAvgNew;
    }

    public void setOnlineTimeAvgNew(BigDecimal onlineTimeAvgNew) {
        this.onlineTimeAvgNew = onlineTimeAvgNew;
    }

    public BigDecimal getOnlineStartupMedianNew() {
        return onlineStartupMedianNew;
    }

    public void setOnlineStartupMedianNew(BigDecimal onlineStartupMedianNew) {
        this.onlineStartupMedianNew = onlineStartupMedianNew;
    }

    public BigDecimal getOnlineStartupAvgNew() {
        return onlineStartupAvgNew;
    }

    public void setOnlineStartupAvgNew(BigDecimal onlineStartupAvgNew) {
        this.onlineStartupAvgNew = onlineStartupAvgNew;
    }

    public BigDecimal getOnlineTimeAvgOld() {
        return onlineTimeAvgOld;
    }

    public void setOnlineTimeAvgOld(BigDecimal onlineTimeAvgOld) {
        this.onlineTimeAvgOld = onlineTimeAvgOld;
    }

    public BigDecimal getOnlineStartupMedianOld() {
        return onlineStartupMedianOld;
    }

    public void setOnlineStartupMedianOld(BigDecimal onlineStartupMedianOld) {
        this.onlineStartupMedianOld = onlineStartupMedianOld;
    }

    public BigDecimal getOnlineStartupAvgOld() {
        return onlineStartupAvgOld;
    }

    public void setOnlineStartupAvgOld(BigDecimal onlineStartupAvgOld) {
        this.onlineStartupAvgOld = onlineStartupAvgOld;
    }

    public String getAdBuyMedia() {
        return adBuyMedia;
    }

    public void setAdBuyMedia(String adBuyMedia) {
        this.adBuyMedia = adBuyMedia;
    }

    public String getOnlineTimeNew() {
        return onlineTimeNew;
    }

    public void setOnlineTimeNew(String onlineTimeNew) {
        this.onlineTimeNew = onlineTimeNew;
    }

    public String getOnlineTimeOld() {
        return onlineTimeOld;
    }

    public void setOnlineTimeOld(String onlineTimeOld) {
        this.onlineTimeOld = onlineTimeOld;
    }

    public String getAd_buy_media() {
        return ad_buy_media;
    }

    public void setAd_buy_media(String ad_buy_media) {
        this.ad_buy_media = ad_buy_media;
    }

    public BigDecimal getOnlineTimeMedianNew() {
        return onlineTimeMedianNew;
    }

    public void setOnlineTimeMedianNew(BigDecimal onlineTimeMedianNew) {
        this.onlineTimeMedianNew = onlineTimeMedianNew;
    }

    public BigDecimal getOnlineTimeMedianOld() {
        return onlineTimeMedianOld;
    }

    public void setOnlineTimeMedianOld(BigDecimal onlineTimeMedianOld) {
        this.onlineTimeMedianOld = onlineTimeMedianOld;
    }
}