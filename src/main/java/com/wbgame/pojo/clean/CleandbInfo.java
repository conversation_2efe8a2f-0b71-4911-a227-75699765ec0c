package com.wbgame.pojo.clean;

/**
 * 清理数据库配置页面
 * <AUTHOR>
 * @date: 2021年7月27日
 */
public class CleandbInfo {

    private String id;//唯一id
    private String pkg_name;//应用包名
    private String app_name;//应用名
    private String junk_type;//垃圾类型(0其它, 1缓存，2广告，3下载..)
    private String file_type;//文件类型(0其它, 1图片，2音频，3视频，4日志，5压缩包, 6安装包)
    private String desc;//文件描述(临时文件，垃圾文件，图片文件，短视频文件, 广告缓存文件...)
    private String file_path;//文件路径（文件绝对路径=文件根路径+文件路径）
    private String root_path;//文件根路径，默认为空
    private String strategy;//策略(0确定删除，1自动删除)
    private String update;//更新时间
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getPkg_name() {
		return pkg_name;
	}
	public void setPkg_name(String pkg_name) {
		this.pkg_name = pkg_name;
	}
	public String getApp_name() {
		return app_name;
	}
	public void setApp_name(String app_name) {
		this.app_name = app_name;
	}
	public String getJunk_type() {
		return junk_type;
	}
	public void setJunk_type(String junk_type) {
		this.junk_type = junk_type;
	}
	public String getFile_type() {
		return file_type;
	}
	public void setFile_type(String file_type) {
		this.file_type = file_type;
	}
	public String getDesc() {
		return desc;
	}
	public void setDesc(String desc) {
		this.desc = desc;
	}
	public String getFile_path() {
		return file_path;
	}
	public void setFile_path(String file_path) {
		this.file_path = file_path;
	}
	public String getRoot_path() {
		return root_path;
	}
	public void setRoot_path(String root_path) {
		this.root_path = root_path;
	}
	public String getStrategy() {
		return strategy;
	}
	public void setStrategy(String strategy) {
		this.strategy = strategy;
	}
	public String getUpdate() {
		return update;
	}
	public void setUpdate(String update) {
		this.update = update;
	}
    
}