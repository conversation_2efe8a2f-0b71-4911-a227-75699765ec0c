package com.wbgame.pojo.clean.request;



import com.wbgame.pojo.mobile.request.BaseRequestParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Classname ApiPacketParaConfigVo
 * @Date 2024/03/11 14:55
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("产品子系统配置")
public class ProductSysConfigSaveRequestParam extends BaseRequestParam  {
	    @ApiModelProperty("id")
	    private int id;
	 	@ApiModelProperty("应用名称")
	    private String product_name;
	    @ApiModelProperty("图标地址")
	    private String icon_url;
	    @ApiModelProperty("应用id")
	    private String appid;
	    @ApiModelProperty("应用包名")
	    private String pkg;
	    @ApiModelProperty("应用域名")
	    private String domain_name;
	    @ApiModelProperty("应用平台支持 1：安卓  2：IOS")
	    private int platform;
	    @ApiModelProperty("url")
	    private int sys_web_url;

		@ApiModelProperty("接口目录")
		private String inter_dir;

		@ApiModelProperty("测试接口目录")
		private String test_inter_dir;

		@ApiModelProperty("测试域名")
		private String test_domain;

		@ApiModelProperty("所属服务器资源分类")
		private Integer server_category;

}
