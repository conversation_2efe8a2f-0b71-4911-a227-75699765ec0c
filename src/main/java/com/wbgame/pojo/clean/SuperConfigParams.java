package com.wbgame.pojo.clean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 清理王-定义参数
 * <AUTHOR>
 * @date: 2020年8月25日
 */
@ApiModel("自定义参数配置")
public class SuperConfigParams {
	@ApiModelProperty("id")
	private Integer id;
    @ApiModelProperty("产品id")
	private String appid;
    @ApiModelProperty("渠道")
	private String cha;
    @ApiModelProperty("项目id")
	private String prjid;
    @ApiModelProperty("params json格式字符串")
	private String params;
    @ApiModelProperty("创建时间")
	private String creatTime;
    @ApiModelProperty("修改时间")
	private String modifyTime;
    @ApiModelProperty("修改人")
	private String modifyUser;
    @ApiModelProperty("状态")
	private String status;
	@ApiModelProperty("规则类型 1:")
    private String ruleType;
	@ApiModelProperty("国家码")
	private String country;
	@ApiModelProperty(value = "国家码列表",hidden = true)
	private List<String> countries;

	public List<String> getCountries() {
		return countries;
	}

	public void setCountries(List<String> countries) {
		this.countries = countries;
	}

	public String getRuleType() {
		return ruleType;
	}

	public void setRuleType(String ruleType) {
		this.ruleType = ruleType;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	@ApiModelProperty("创建人")
	private String createUser;

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getCha() {
		return cha;
	}
	public void setCha(String cha) {
		this.cha = cha;
	}
	public String getPrjid() {
		return prjid;
	}
	public void setPrjid(String prjid) {
		this.prjid = prjid;
	}
	public String getParams() {
		return params;
	}
	public void setParams(String params) {
		this.params = params;
	}
	public String getCreatTime() {
		return creatTime;
	}
	public void setCreatTime(String creatTime) {
		this.creatTime = creatTime;
	}
	public String getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}
	public String getModifyUser() {
		return modifyUser;
	}
	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}

}