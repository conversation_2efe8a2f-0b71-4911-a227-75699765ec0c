package com.wbgame;

import com.wbgame.common.AuthenticationInterceptor;
import com.wbgame.config.LogInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

/**
 * 配置拦截器
 */
@Configuration
public class WebMvcConfig extends WebMvcConfigurerAdapter {

    /* 前端静态文件目录 */
    @Value("${htmlPath:/home/<USER>/v3/}")
    private String htmlPath;

    /**
     * 路由路径拦截处理
     * @param registry
     */
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(getLogInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("/wb/login")
                .excludePathPatterns("/fin/**")
                .excludePathPatterns("/asset/**")
                .excludePathPatterns("/advert/cashPlatformDetail/overseaSync");

        registry.addInterceptor(getAuthInterceptor())
                .addPathPatterns("/mobile/**")
                .addPathPatterns("/advert/**")
                .addPathPatterns("/operate/**")
                .addPathPatterns("/game/set/**")
                .addPathPatterns("/feishu/**")

                .addPathPatterns("/clean/selectSuperLockNew")
                .addPathPatterns("/clean/selectSuperConfigNews")
                .addPathPatterns("/clean/selectSuperConfigNewV2/list")
                .addPathPatterns("/clean/selectApplicationAutoActivation")
                .addPathPatterns("/clean/selectSuperConfigParams")
                .addPathPatterns("/clean/selectSuperAdvanceConfigByCondition")
                .excludePathPatterns("/advert/cashPlatformDetail/overseaSync");

        super.addInterceptors(registry);
    }

    /**
     * 映射静态资源路径
     * @param registry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        //  /images/** 映射到哪里去     file:/home/<USER>/ 表示需要访问linux系统文件所在的文件夹路径名称
        registry.addResourceHandler("/**").addResourceLocations("file:"+htmlPath);
        registry.addResourceHandler("doc.html").addResourceLocations("classpath:/META-INF/resources/");
    }

    @Bean
    public LogInterceptor getLogInterceptor(){
        return new LogInterceptor();
    }

    @Bean
    public AuthenticationInterceptor getAuthInterceptor(){
        return new AuthenticationInterceptor();
    }

}