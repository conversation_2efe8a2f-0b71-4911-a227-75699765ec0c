package com.wbgame;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.task.HaiwaiAdTask;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpClientUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTime;

import javax.xml.ws.WebServiceClient;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

public class Test2 {


	public static void main(String[] args) throws IOException {

//		String url2 = "https://restapi.amap.com/v3/ip?output=json&key=6ebfcd9f83735d8b0a9ae5468b113d97&ip=" + "**************";
//		String httpGet2 = HttpClientUtils.getInstance().httpGet(url2);
//		System.out.println("httpGet2 = " +

//		WebServiceClient client = new WebServiceClient.Builder("lKDY3gR6LQjFCLchWdehBtfSy1D6R5wT8fA7qSALiUzK9IFEv6nfodlcPYF6v8fY").build();
//		RespLocationCity city = client.city("您要查询的IP");
//		System.out.println(city.getCode());
//		System.out.println(city.getMsg());
//		System.out.println(city.getData().getCountry());



		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost("https://edc.vigame.cn:6115" + "/wb/insertAppInfo");

		// 设置请求参数
		List<NameValuePair> params = new ArrayList<>();
		params.add(new BasicNameValuePair("appname", "测试应用" + System.currentTimeMillis())); // 动态生成应用名，避免重复
		params.add(new BasicNameValuePair("apptype", "app")); // game 或 app
		params.add(new BasicNameValuePair("ostype", "1")); // 1-安卓 2-IOS
		params.add(new BasicNameValuePair("app_category", "15")); // 15-海外工具
		params.add(new BasicNameValuePair("umeng_key", "测试umeng_key"));
		params.add(new BasicNameValuePair("sync_umeng", "0")); // 0-不同步 1-同步
		params.add(new BasicNameValuePair("xyx_id", "测试xyx_id"));
		params.add(new BasicNameValuePair("reyun_key", "测试reyun_key"));
		params.add(new BasicNameValuePair("bus_category", "1")); // 1-游戏、2-小游戏、3-工具
		params.add(new BasicNameValuePair("two_app_category", "10")); // 10-其它
		params.add(new BasicNameValuePair("cp", "测试CP"));
		params.add(new BasicNameValuePair("cuser", "testuser"));

//		httpPost = new HttpPost("https://edc.vigame.cn:6115" + "/wb/appInfoHandle");
//		params.add(new BasicNameValuePair("handle", "edit")); // 操作类型：edit
//		params.add(new BasicNameValuePair("appid", "41289")); // 需要替换为实际存在的应用ID

		httpPost.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));

		// 执行请求
		try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				String result = EntityUtils.toString(entity);
				System.out.println("插入应用信息响应结果: " + result);
			}
		}




		if(true){return;}

		List<String> list = new ArrayList<>();
		AtomicInteger num= new AtomicInteger();
		Files.readAllLines(Paths.get("C:\\Users\\<USER>\\Desktop\\ips.txt")).forEach(act -> {
			if (act != null && act.length() > 0) {
				String reqip = act;
				System.out.println("测试ip："+reqip+"\t");

				num.addAndGet(1);
				if (num.get() % 50 == 0) {
					try {
						Thread.sleep(1000l);
					} catch (InterruptedException e) {
						throw new RuntimeException(e);
					}
				}

				try {
//					String url = "https://restapi.amap.com/v3/ip?output=json&key=6ebfcd9f83735d8b0a9ae5468b113d97&ip=" + reqip;
					String url = "https://api.ipplus360.com/ip/geo/v1/city/?key=lKDY3gR6LQjFCLchWdehBtfSy1D6R5wT8fA7qSALiUzK9IFEv6nfodlcPYF6v8fY&coordsys=WGS84&ip=" + reqip;
					String httpGet = HttpClientUtils.getInstance().httpGet(url);
					System.out.println("httpGet = " + httpGet);
					JSONObject object = JSONObject.parseObject(httpGet);
					if ("Success".equalsIgnoreCase(object.getString("code"))) {

						String city = object.getJSONObject("data").getString("city");
						if(BlankUtils.checkBlank(city)){
							// 未获取到城市，获取省份
							city = object.getJSONObject("data").getString("prov");
							if(BlankUtils.checkBlank(city)){
								city = "未获取";
							}
						}
						list.add(city);
					} else {
						list.add("未获取");
					}

				} catch (Exception e) {
					e.printStackTrace();
					list.add("异常");
				}
			}

		});

		System.out.println("***************************************************");
		list.forEach(System.out::println);

		Files.write(Paths.get("C:\\Users\\<USER>\\Desktop\\zzz.txt"), list, StandardOpenOption.CREATE, StandardOpenOption.APPEND);
	}
}