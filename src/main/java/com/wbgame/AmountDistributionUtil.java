package com.wbgame;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

public class AmountDistributionUtil {
    
    /**
     * 将指定金额分摊到列表中，保证总和相等且数值接近平均
     * @param originalList 原始金额列表
     * @param amountToDistribute 需要分摊的金额
     * @return 分摊后的金额列表
     */
    public static List<Double> distributeAmount(List<Double> originalList, double amountToDistribute) {
        List<Double> result = new ArrayList<>(originalList.size());
        int size = originalList.size();
        
        // 计算平均值
        double average = amountToDistribute / size;
        double remaining = amountToDistribute;
        Random random = new Random();
        
        // 为除最后一个元素外的所有位置分配金额
        for (int i = 0; i < size - 1; i++) {
            // 在平均值的基础上随机上下浮动
            double variation = random.nextDouble() * (average * 0.2) - (average * 0.1);
            double amount = Math.floor(average + variation);
            
            // 确保剩余金额足够分配给后续元素
            if (remaining - amount < (size - i - 1) * (average * 0.5)) {
                amount = Math.floor(average * 0.9);
            }
            
            result.add(amount);
            remaining -= amount;
        }
        
        // 最后一个元素获得剩余金额，确保总和正确
        result.add(remaining);
        
        return result;
    }

    public static void main(String[] args) {
        List<Double> originalList = Arrays.asList(
                22642.63,
                13508.77,
                9700.35,
                13840.7,
                12838.79,
                12473.51,
                15039.9,
                14391.6,
                10729.0,
                10267.92,
                11938.97,
                11072.07,
                18022.29,
                12187.4,
                11305.69,
                12481.18,
                9749.66,
                11062.66,
                10383.1,
                16450.8,
                16212.61,
                17511.87,
                13896.82,
                13382.89,
                11996.94,
                13695.8,
                11053.63,
                12702.8

        );
        double amountToDistribute = 40632;
        List<Double> distributedAmount = AmountDistributionUtil.distributeAmount(originalList, amountToDistribute);
        System.out.println("分配结果：" + distributedAmount);
        distributedAmount.forEach(System.out::println);
    }

    
}