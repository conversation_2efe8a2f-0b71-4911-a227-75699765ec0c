package com.wbgame.controller.operate;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.ApiNeed;
import com.wbgame.aop.NewPageLimit;
import com.wbgame.aop.PageLimit;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.redpack.HbMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.operate.CommonReportVo;
import com.wbgame.pojo.operate.MdsConfigVo;
import com.wbgame.pojo.operate.PayWarnConfigVo;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@RestController
@CrossOrigin
@RequestMapping(value = "/operate/payWarn")
@Api(tags = "支付告警白名单")
@ApiSupport(author = "huangmb")
public class PayWarnConfigController {

    @Resource
    public HbMapper hbMapper;

    @Autowired
    HttpServletRequest request;

    @RequestMapping(value = "/list")
    @NewPageLimit
    @ApiOperation(value = "查询", notes = "查询支付告警白名单", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = PayWarnConfigVo.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public String list(@ApiNeed({"order","start","limit","appid"}) CommonReportVo param){
        List<PayWarnConfigVo> list = hbMapper.selectPayWarnConfig(param);
        JSONObject jsonObject = new JSONObject();
        long size = ((Page) list).getTotal();
        jsonObject.put("total",size);
        jsonObject.put("list",list);
        return ReturnJson.success(jsonObject);
    }


    @RequestMapping(value = "/add")
    @ApiOperation(value = "新增", notes = "新增支付告警白名单", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = Constants.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public String add(@ApiNeed({"appid"}) PayWarnConfigVo param) {
        if (BlankUtils.checkBlank(param.getAppid())) {
            return ReturnJson.error(Constants.ParamError);
        }
        CommonReportVo p = new CommonReportVo();
        p.setAppid(param.getAppid());
        List<PayWarnConfigVo> payWarnConfigVos = hbMapper.selectPayWarnConfig(p);
        if(payWarnConfigVos != null && payWarnConfigVos.size() > 0) {
            return ReturnJson.toErrorJson("该产品配置已存在");
        }
        CurrUserVo loginUser = (CurrUserVo)request.getAttribute("LoginUser");
        param.setCreate_owner(loginUser.getLogin_name());
        param.setUpdate_owner(loginUser.getLogin_name());
        param.setCreatetime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
        param.setUpdatetime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
        hbMapper.addPayWarnConfig(param);
        return ReturnJson.success();
    }

    @RequestMapping(value = "/delete")
    @ApiOperation(value = "删除", notes = "删除支付告警白名单", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = Constants.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public String delete(@ApiNeed({"appid"}) PayWarnConfigVo param) {
        hbMapper.deletePayWarnConfig(param);
        return ReturnJson.success();
    }
    
    
}
