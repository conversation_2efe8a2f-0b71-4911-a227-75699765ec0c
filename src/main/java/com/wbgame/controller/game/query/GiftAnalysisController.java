package com.wbgame.controller.game.query;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.game.GiftAnalysisVo;
import com.wbgame.service.AdService;
import com.wbgame.service.game.GiftService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.jettison.CommonUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Classname GiftAnalysisController
 * @Description TODO
 * @Date 2022/3/9 14:28
 */
@RequestMapping("/game/giftAnalysis")
@RestController
@CrossOrigin
public class GiftAnalysisController {

    private static final Map<String,String> GIFT_TYPE_DESC_MAP = new HashMap<String,String>(){{
        put("1","新手礼包");
        put("2","限时礼包");
        put("3","活动礼包");
        put("4","常驻礼包");
    }};

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    GiftService giftService;

    @Autowired
    private AdService adService;

    @RequestMapping("getList")
    public Object getList(HttpServletRequest request){
        JSONObject ret = new JSONObject();
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageNo = "".equals(start) == true ? 1 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        Map<String,String> paramMap = new HashMap<>();
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");
        String pid = request.getParameter("pid");
        String download_channel = request.getParameter("download_channel");
        String gift_item_type = request.getParameter("gift_item_type");
        String gift_item_id = request.getParameter("gift_item_id");
        String gift_item_name = request.getParameter("gift_item_name");
        if (!BlankUtils.checkBlank(gift_item_name)){
            gift_item_name = gift_item_name.replace(",","|");
        }
        String currency_type = request.getParameter("currency_type");
        String gift_buy_type = request.getParameter("gift_buy_type");
        String order_str = request.getParameter("order_str");
        String group = request.getParameter("group");
        String appid_group = "";
        String download_channel_group ="";
        String pid_group = "";
        String buy_date_group = "";
        String gift_item_id_group ="";
        String gift_item_type_group ="";
        String tag_r_group = "";
        String group_id_group = "";

        if (!BlankUtils.checkBlank(group)){
            String[] groups = group.split(",");
            for (String str:groups){
                if ("buy_date".equals(str)){
                    buy_date_group ="buy_date";
                }
                if ("pid".equals(str)){
                    pid_group ="a.pid";
                }
                if ("appid".equals(str)){
                    appid_group = "a.appid";
                }
                if ("download_channel".equals(str)){
                    download_channel_group = "a.download_channel";
                }
                if ("gift_item_type".equals(str)){
                    gift_item_type_group  ="gift_item_type";
                }
                if ("gift_item_id".equals(str)){
                    gift_item_id_group = "a.gift_item_id";
                }
                if ("tag_r".equals(str)) {
                    tag_r_group = "a.tag_r";
                }
                if ("group_id".equals(str)) {
                    group_id_group = "a.group_id";
                }
            }
        }
        String[] replaceGroup ={appid_group,download_channel_group,pid_group,buy_date_group,gift_item_id_group,gift_item_type_group,tag_r_group,group_id_group};
        //移除空的
        List<String> grouplist = new ArrayList<>();
        for (String str:replaceGroup){
            if (!BlankUtils.checkBlank(str)){
                grouplist.add(str);
            }
        }
        StringBuffer newGroup = new StringBuffer();
        if (grouplist.size()>0){
            for (int i = 0; i < grouplist.size(); i++){
                if(i != grouplist.size() - 1){
                    newGroup.append(grouplist.get(i)).append(",");
                }else {
                    newGroup.append(grouplist.get(i));
                }
            }
        }

        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("appid",appid);
        paramMap.put("pid",pid);
        paramMap.put("download_channel",download_channel);
        paramMap.put("gift_item_type",gift_item_type);
        paramMap.put("gift_item_id",gift_item_id);
        paramMap.put("gift_item_name",gift_item_name);
        paramMap.put("currency_type",currency_type);
        paramMap.put("tag_r",request.getParameter("tag_r"));
        paramMap.put("group_id",request.getParameter("group_id"));

        paramMap.put("order_str",order_str);
        //转换处理别名问题
        paramMap.put("group",newGroup.toString());
        paramMap.put("buy_date_group",buy_date_group);
        paramMap.put("appid_group",appid_group);
        paramMap.put("pid_group",pid_group);
        paramMap.put("download_channel_group",download_channel_group);
        paramMap.put("gift_item_type_group",gift_item_type_group);
        paramMap.put("gift_item_id_group",gift_item_id_group);
        paramMap.put("gift_buy_type",gift_buy_type);
        paramMap.put("tag_r_group",tag_r_group);
        paramMap.put("group_id_group",group_id_group);

        List<GiftAnalysisVo> totalList = giftService.getGiftAnalysisList(paramMap);
        if(null==totalList||totalList.size()==0){
        	ret.put("ret",1);
            ret.put("msg","ok");
            ret.put("data",new ArrayList<>());
            ret.put("total",null);
            ret.put("totalSize",0);
            ret.put("totalList",new ArrayList<>());
            return ret;
        }
        
        GiftAnalysisVo total = giftService.getGiftAnalysisSum(paramMap);
        if (total!=null){
            if (!BlankUtils.checkBlank(total.getSale_sum())){
                total.setSale_sum(new BigDecimal(total.getSale_sum()).setScale(2, RoundingMode.HALF_UP).toString());
            }
            if (!BlankUtils.checkBlank(total.getSale_sum_new())){
                total.setSale_sum_new(new BigDecimal(total.getSale_sum_new()).setScale(2, RoundingMode.HALF_UP).toString());
            }
            if (!BlankUtils.checkBlank(total.getSale_sum_old())){
                total.setSale_sum_old(new BigDecimal(total.getSale_sum_old()).setScale(2, RoundingMode.HALF_UP).toString());
            }
        }
        // 数据内容
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";;
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

        //所有结果集
        for (GiftAnalysisVo po:totalList){
        	//处理sum(聚合函数)返回null 条目问题
            if (po==null){
                continue;
            }
            //处理销售额保留两位小数问题
            if (!BlankUtils.checkBlank(po.getSale_sum())){
                po.setSale_sum(new BigDecimal(po.getSale_sum()).setScale(0, RoundingMode.HALF_UP).toString());
            }
            if (!BlankUtils.checkBlank(po.getSale_sum_new())){
                po.setSale_sum_new(new BigDecimal(po.getSale_sum_new()).setScale(0, RoundingMode.HALF_UP).toString());
            }
            if (!BlankUtils.checkBlank(po.getSale_sum_old())){
                po.setSale_sum_old(new BigDecimal(po.getSale_sum_old()).setScale(0, RoundingMode.HALF_UP).toString());
            }
            po.setAppname(appMap.get(po.getAppid())!=null?appMap.get(po.getAppid()).get("app_name")+"":"");
        }
        List<GiftAnalysisVo> subList = totalList.stream().skip((pageNo-1)*pageSize).limit(pageSize).
				collect(Collectors.toList());
        ret.put("ret",1);
        ret.put("msg","ok");
        ret.put("data",subList);
        ret.put("total",total);
        ret.put("totalSize",totalList.size());
        ret.put("totalList",totalList);
        return ret;
    }
    @RequestMapping("getLineChart")
    public Object getLineChart(HttpServletRequest request){
        JSONObject ret = new JSONObject();
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        Map<String,String> paramMap = new HashMap<>();
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");
        String pid = request.getParameter("pid");
        String download_channel = request.getParameter("download_channel");
        String gift_item_type = request.getParameter("gift_item_type");
        String gift_item_id = request.getParameter("gift_item_id");
        String gift_item_name = request.getParameter("gift_item_name");
        String gift_buy_type = request.getParameter("gift_buy_type");

        if (!BlankUtils.checkBlank(gift_item_name)){
            gift_item_name = gift_item_name.replace(",","|");
        }
        String currency_type = request.getParameter("currency_type");



        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("appid",appid);
        paramMap.put("pid",pid);
        paramMap.put("download_channel",download_channel);
        paramMap.put("gift_item_type",gift_item_type);
        paramMap.put("gift_item_id",gift_item_id);
        paramMap.put("gift_item_name",gift_item_name);
        paramMap.put("currency_type",currency_type);
        paramMap.put("gift_buy_type",gift_buy_type);
        paramMap.put("tag_r",request.getParameter("tag_r"));
        paramMap.put("group_id",request.getParameter("group_id"));


        List<Map<String,Object>>   list = giftService.getLineChart(paramMap);
        ret.put("ret",1);
        ret.put("msg","ok");
        ret.put("data",list);
        return ret;
    }
    @RequestMapping("export")
    public void export(HttpServletRequest request, HttpServletResponse response){
        Map<String,String> paramMap = new HashMap<>();
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");
        String pid = request.getParameter("pid");
        String download_channel = request.getParameter("download_channel");
        String gift_item_type = request.getParameter("gift_item_type");
        String gift_item_id = request.getParameter("gift_item_id");
        String gift_item_name = request.getParameter("gift_item_name");
        if (!BlankUtils.checkBlank(gift_item_name)){
            gift_item_name = gift_item_name.replace(",","|");
        }
        String currency_type = request.getParameter("currency_type");
        String gift_buy_type = request.getParameter("gift_buy_type");


        String order_str = request.getParameter("order_str");
        String group = request.getParameter("group");
        String export_file_name = request.getParameter("export_file_name");
        String appid_group = "";
        String download_channel_group ="";
        String pid_group = "";
        String buy_date_group = "";
        String gift_item_id_group ="";
        String gift_item_type_group ="";
        String tag_r_group = "";
        String group_id_group = "";

        if (!BlankUtils.checkBlank(group)){
            String[] groups = group.split(",");
            for (String str:groups){
                if ("buy_date".equals(str)){
                    buy_date_group ="buy_date";
                }
                if ("pid".equals(str)){
                    pid_group ="a.pid";
                }
                if ("appid".equals(str)){
                    appid_group = "a.appid";
                }
                if ("download_channel".equals(str)){
                    download_channel_group = "a.download_channel";
                }
                if ("gift_item_type".equals(str)){
                    gift_item_type_group  ="gift_item_type";
                }
                if ("gift_item_id".equals(str)){
                    gift_item_id_group = "a.gift_item_id";
                }
                if ("tag_r".equals(str)) {
                    tag_r_group = "a.tag_r";
                }
                if ("group_id".equals(str)) {
                    group_id_group = "a.group_id";
                }
            }
        }
        String[] replaceGroup ={appid_group,download_channel_group,pid_group,buy_date_group,gift_item_id_group,gift_item_type_group,tag_r_group,group_id_group};
        //移除空的
        List<String> grouplist = new ArrayList<>();
        for (String str:replaceGroup){
            if (!BlankUtils.checkBlank(str)){
                grouplist.add(str);
            }
        }
        StringBuffer newGroup = new StringBuffer();
        if (grouplist.size()>0){
            for (int i = 0; i < grouplist.size(); i++){
                if(i != grouplist.size() - 1){
                    newGroup.append(grouplist.get(i)).append(",");
                }else {
                    newGroup.append(grouplist.get(i));
                }
            }
        }

        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("appid",appid);
        paramMap.put("pid",pid);
        paramMap.put("download_channel",download_channel);
        paramMap.put("gift_item_type",gift_item_type);
        paramMap.put("gift_item_id",gift_item_id);
        paramMap.put("gift_item_name",gift_item_name);
        paramMap.put("currency_type",currency_type);

        paramMap.put("order_str",order_str);
        //转换处理别名问题
        paramMap.put("group",newGroup.toString());
        paramMap.put("buy_date_group",buy_date_group);
        paramMap.put("appid_group",appid_group);
        paramMap.put("pid_group",pid_group);
        paramMap.put("download_channel_group",download_channel_group);
        paramMap.put("gift_item_type_group",gift_item_type_group);
        paramMap.put("gift_item_id_group",gift_item_id_group);
        paramMap.put("tag_r_group",tag_r_group);
        paramMap.put("group_id_group",group_id_group);
        paramMap.put("gift_buy_type",gift_buy_type);
        paramMap.put("tag_r",request.getParameter("tag_r"));
        paramMap.put("group_id",request.getParameter("group_id"));

        // 数据内容
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";;
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

        List<GiftAnalysisVo> list = giftService.getGiftAnalysisList(paramMap);
        for (GiftAnalysisVo vo:list){
            if(vo!=null){
                if(!BlankUtils.checkBlank(vo.getAppid())){
                    vo.setAppname(appMap.get(vo.getAppid())!=null?appMap.get(vo.getAppid()).get("app_name")+"":"");
                }
                //处理销售额保留两位小数问题
                if (!BlankUtils.checkBlank(vo.getSale_sum())){
                    vo.setSale_sum(new BigDecimal(vo.getSale_sum()).setScale(0, RoundingMode.HALF_UP).toString());
                }
                if (!BlankUtils.checkBlank(vo.getSale_sum_new())){
                    vo.setSale_sum_new(new BigDecimal(vo.getSale_sum_new()).setScale(0, RoundingMode.HALF_UP).toString());
                }
                if (!BlankUtils.checkBlank(vo.getSale_sum_old())){
                    vo.setSale_sum_old(new BigDecimal(vo.getSale_sum_old()).setScale(0, RoundingMode.HALF_UP).toString());
                }
                String avtiveUsers=vo.getActive_user_count();
                String buyCount=vo.getBuy_count_new();
                if(!BlankUtils.checkBlank(avtiveUsers)&&!BlankUtils.checkBlank(buyCount)){
                	vo.setGift_penetration_rate(CommonUtil.bs(Integer.valueOf(buyCount), Integer.valueOf(avtiveUsers)));
                }
                vo.setGift_item_type(GIFT_TYPE_DESC_MAP.get(vo.getGift_item_type())!=null?GIFT_TYPE_DESC_MAP.get(vo.getGift_item_type()):vo.getGift_item_type());
            }else {
                list = new ArrayList<>();
                break;
            }
        }
        Map<String, String> headerMap = new LinkedHashMap<String, String>();

        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        ExportExcelUtil.exportXLSX2(response,list,headerMap, export_file_name + DateTime.now().toString("yyyyMMdd")+".xlsx");
    }


    @RequestMapping("/selectPackageAnalysisSales")
    public Result<Map<String, Map<String, List<Map<String, Object>>>>> selectPackageAnalysisSales(HttpServletRequest request){
        JSONObject ret = new JSONObject();
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ResultUtils.failure(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        Map<String,String> paramMap = new HashMap<>();
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");
        String pid = request.getParameter("pid");
        String download_channel = request.getParameter("download_channel");
        String gift_item_type = request.getParameter("gift_item_type");
        String gift_item_id = request.getParameter("gift_item_id");
        String gift_item_name = request.getParameter("gift_item_name");
        if (!BlankUtils.checkBlank(gift_item_name)){
            gift_item_name = gift_item_name.replace(",","|");
        }
        String currency_type = request.getParameter("currency_type");

        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("appid",appid);
        paramMap.put("pid",pid);
        paramMap.put("download_channel",download_channel);
        paramMap.put("gift_item_type",gift_item_type);
        paramMap.put("gift_item_id",gift_item_id);
        paramMap.put("gift_item_name",gift_item_name);
        paramMap.put("currency_type",currency_type);

        return giftService.selectPackageAnalysisSales(paramMap);
    }

}
