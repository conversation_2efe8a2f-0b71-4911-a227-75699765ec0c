package com.wbgame.controller.game.query;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.Asserts;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.game.report.AdGrowthReportVo;
import com.wbgame.service.game.AppService;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname AdGrowthController
 * @Description 广告增长
 * @Date 2023/5/4 12:28
 */
@RequestMapping("/game/adGrowth")
@RestController
@CrossOrigin
@Api(tags ="新用户广告增长")
public class AdGrowthController {

    @Autowired
    AppService appService;

    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping("list")
    public Result<PageResult<AdGrowthReportVo>> getNewerAdGrowthList(@ApiNeed({
            "start_date",
            "end_date",
            "appidList",
            "channelList",
            "adTypeList",
            "start",
            "limit",
            "group",
            "order_str",


    }) @Validated(QueryGroup.class) AdGrowthReportVo query) {
        return appService.getNewerAdGrowthPageList(query);
    }


    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping(value = "/export")
    public void exportNewerAdGrowthList(HttpServletResponse response, @ApiNeed({
            "start_date",
            "end_date",
            "appidList",
            "channelList",
            "adTypeList",
            "start",
            "limit",
            "group",
            "order_str",

    }) @Validated(QueryGroup.class) AdGrowthReportVo query) {
        List<AdGrowthReportVo> list = appService.getNewerAdGrowthList(query);
        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = query.getValue().split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = query.getExport_file_name() + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx";
        ExportExcelUtil.exportXLSX2(response, list, head, fileName);
    }

}
