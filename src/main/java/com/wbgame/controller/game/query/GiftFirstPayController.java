package com.wbgame.controller.game.query;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.QueryRemoveDateGroup;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.game.GiftFirstPayDTO;
import com.wbgame.pojo.game.GiftFirstPayVo;
import com.wbgame.service.AdService;
import com.wbgame.service.game.GiftService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname GiftFirstPayController
 * @Description TODO
 * @Date 2022/4/24 15:53
 */
@CrossOrigin
@RestController
@RequestMapping("/game/giftFirstPay")
public class GiftFirstPayController {

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    GiftService giftService;

    @Autowired
    private AdService adService;

    @RequestMapping("getList")
    public Object getList(HttpServletRequest request){
        JSONObject ret = new JSONObject();
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize);
        Map<String,String> paramMap = new HashMap<>();
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");
        String pid = request.getParameter("pid");
        String download_channel = request.getParameter("download_channel");
        String gift_id = request.getParameter("gift_id");
        String level_id = request.getParameter("level_id");

        String order_str = request.getParameter("order_str");
        String group = request.getParameter("group");
        String appid_group = "";
        String download_channel_group ="";
        String pid_group = "";
        String create_date_group = "";
        String gift_id_group ="";
        String level_id_group ="";

        if (!BlankUtils.checkBlank(group)){
            String[] groups = group.split(",");
            for (String str:groups){
                if ("create_date".equals(str)){
                    create_date_group ="a.create_date";
                }
                if ("pid".equals(str)){
                    pid_group ="a.pid";
                }
                if ("appid".equals(str)){
                    appid_group = "a.appid";
                }
                if ("download_channel".equals(str)){
                    download_channel_group = "a.download_channel";
                }
                if ("level_id".equals(str)){
                    level_id_group  ="a.level_id";
                }
                if ("gift_id".equals(str)){
                    gift_id_group = "a.gift_id";
                }
            }
        }
        if (!BlankUtils.checkBlank(order_str)){
            if (order_str.contains("level_id ")){
                order_str = order_str.replace("level_id ","a.level_id ");
            }
        }

        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("appid",appid);
        paramMap.put("pid",pid);
        paramMap.put("download_channel",download_channel);
        paramMap.put("gift_id",gift_id);
        paramMap.put("level_id",level_id);
        paramMap.put("order_str",order_str);


        String[] replaceGroup ={create_date_group,appid_group,download_channel_group,pid_group,gift_id_group,level_id_group};
        //移除空的
        List<String> grouplist = new ArrayList<>();
        for (String str:replaceGroup){
            if (!BlankUtils.checkBlank(str)){
                grouplist.add(str);
            }
        }
        StringBuffer newGroup = new StringBuffer();
        if (grouplist.size()>0){
            for (int i = 0; i < grouplist.size(); i++){
                if(i != grouplist.size() - 1){
                    newGroup.append(grouplist.get(i)).append(",");
                }else {
                    newGroup.append(grouplist.get(i));
                }
            }
        }


        //转换处理别名问题
        paramMap.put("group",newGroup.toString());
        paramMap.put("create_date_group",create_date_group);
        paramMap.put("appid_group",appid_group);
        paramMap.put("pid_group",pid_group);
        paramMap.put("download_channel_group",download_channel_group);
        paramMap.put("level_id_group",level_id_group);
        paramMap.put("gift_id_group",gift_id_group);

        List<GiftFirstPayVo> list = giftService.getGiftFirstPayList(paramMap);
        GiftFirstPayVo total = giftService.getGiftFirstPaySum(paramMap);
        long size = ((Page) list).getTotal();

        ret.put("ret",1);
        ret.put("msg","ok");
        ret.put("data",list);
        ret.put("total",total);
        ret.put("totalSize",size);
        return ret;
    }


    @RequestMapping("export")
    public void export(HttpServletRequest request, HttpServletResponse response){
        Map<String,String> paramMap = new HashMap<>();
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");
        String pid = request.getParameter("pid");
        String download_channel = request.getParameter("download_channel");
        String gift_id = request.getParameter("gift_id");
        String level_id = request.getParameter("level_id");

        String order_str = request.getParameter("order_str");
        String group = request.getParameter("group");
        String appid_group = "";
        String download_channel_group ="";
        String pid_group = "";
        String create_date_group = "";
        String gift_id_group ="";
        String level_id_group ="";

        if (!BlankUtils.checkBlank(group)){
            String[] groups = group.split(",");
            for (String str:groups){
                if ("create_date".equals(str)){
                    create_date_group ="a.create_date";
                }
                if ("pid".equals(str)){
                    pid_group ="a.pid";
                }
                if ("appid".equals(str)){
                    appid_group = "a.appid";
                }
                if ("download_channel".equals(str)){
                    download_channel_group = "a.download_channel";
                }
                if ("level_id".equals(str)){
                    level_id_group  ="a.level_id";
                }
                if ("gift_id".equals(str)){
                    gift_id_group = "a.gift_id";
                }
            }
        }
        if (!BlankUtils.checkBlank(order_str)){
            if (order_str.contains("level_id ")){
                order_str = order_str.replace("level_id ","a.level_id ");
            }
        }

        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("appid",appid);
        paramMap.put("pid",pid);
        paramMap.put("download_channel",download_channel);
        paramMap.put("gift_id",gift_id);
        paramMap.put("level_id",level_id);
        paramMap.put("order_str",order_str);


        String[] replaceGroup ={create_date_group,appid_group,download_channel_group,pid_group,gift_id_group,level_id_group};
        //移除空的
        List<String> grouplist = new ArrayList<>();
        for (String str:replaceGroup){
            if (!BlankUtils.checkBlank(str)){
                grouplist.add(str);
            }
        }
        StringBuffer newGroup = new StringBuffer();
        if (grouplist.size()>0){
            for (int i = 0; i < grouplist.size(); i++){
                if(i != grouplist.size() - 1){
                    newGroup.append(grouplist.get(i)).append(",");
                }else {
                    newGroup.append(grouplist.get(i));
                }
            }
        }


        //转换处理别名问题
        paramMap.put("group",newGroup.toString());
        paramMap.put("create_date_group",create_date_group);
        paramMap.put("appid_group",appid_group);
        paramMap.put("pid_group",pid_group);
        paramMap.put("download_channel_group",download_channel_group);
        paramMap.put("level_id_group",level_id_group);
        paramMap.put("gift_id_group",gift_id_group);

        // 数据内容
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";;
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

        List<GiftFirstPayVo> list = giftService.getGiftFirstPayList(paramMap);
        for (GiftFirstPayVo vo:list){
            if(vo!=null){
                if(!BlankUtils.checkBlank(vo.getAppid())){
                    vo.setAppid(appMap.get(vo.getAppid())!=null?appMap.get(vo.getAppid()).get("app_name")+"-"+vo.getAppid():"");
                }
            }
        }
        Map<String, String> headerMap = new LinkedHashMap<String, String>();

        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        String export_file_name = request.getParameter("export_file_name");
        if (BlankUtils.checkBlank(export_file_name)){
            export_file_name = "首充分析-大数据";
        }
        ExportExcelUtil.exportXLSX2(response,list,headerMap, export_file_name + "_"+ DateTime.now().toString("yyyyMMdd")+".xlsx");
    }

    /**
     * 首充分析-大数据：新老用户首充level_id堆叠柱状图
     */
    @PostMapping("/selectFirstPayToNewOldUser")
    public Result<Map<String,List<GiftFirstPayVo>>> selectFirstPayToNewOldUser(@Validated(QueryRemoveDateGroup.class) GiftFirstPayDTO giftFirstPayDTO){

        return giftService.selectFirstPayToNewOldUser(giftFirstPayDTO);
    }
}
