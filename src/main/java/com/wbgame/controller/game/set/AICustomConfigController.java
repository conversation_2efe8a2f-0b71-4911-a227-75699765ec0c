package com.wbgame.controller.game.set;

import com.wbgame.aop.LoginCheck;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.game.config.query.AICustomConfigRequestParam;
import com.wbgame.pojo.game.config.response.AICustomConfigResponse;
import com.wbgame.service.game.GameCommunityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname AICustomConfigController
 * @Description TODO
 * @Date 2025/3/5 18:13
 */
@CrossOrigin
@RestController
@RequestMapping("/game/AICustom")
@Api(tags ="AI客服告警设置")
public class AICustomConfigController {

    @Autowired
    private GameCommunityService communityService;

    @PostMapping("selectAICustomConfig")
    @ApiOperation(value = "AI客服告警设置-查询", notes = "AI客服告警设置-查询", httpMethod = "POST")
    public Result<List<AICustomConfigResponse>> selectAICustomConfig(){
        return ResultUtils.success(communityService.selectAICustomConfigList(new AICustomConfigRequestParam()));
    }

    @LoginCheck
    @PostMapping("saveAICustomConfig")
    @ApiOperation(value = "AI客服告警设置-新增", notes = "新增", httpMethod = "POST")
    public Result<Long> saveAICustomConfig(@RequestBody AICustomConfigRequestParam param, HttpServletRequest request){
        // 判断是否存在
        List<AICustomConfigResponse> response = communityService.selectAICustomConfigList(param);
        if (!response.isEmpty()) {
           return ResultUtils.failure("已经存在配置");
        }
        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        param.setCreate_user(loginUser.getLogin_name());
        param.setCreate_time(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
        return ResultUtils.success(communityService.insertAICustomConfig(param));
    }

    @LoginCheck
    @PostMapping("updateAICustomConfig")
    @ApiOperation(value = "AI客服告警设置-更新", notes = "更新", httpMethod = "POST")
    public Result<Long> updateICustomConfig(@RequestBody AICustomConfigRequestParam param, HttpServletRequest request){
        // 判断是否存在
        List<AICustomConfigResponse> response = communityService.selectAICustomConfigList(param);
        if (!response.isEmpty()) {
            boolean match = false;
            for (AICustomConfigResponse config:response){
                if (config.getId().compareTo(param.getId())==0){
                    match = true;
                }
            }
            if (!match){
                return ResultUtils.failure("已经存在配置");
            }
        }
        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        param.setUpdate_user(loginUser.getLogin_name());
        param.setUpdate_time(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
        return ResultUtils.success(communityService.updateAICustomConfig(param));
    }

    @LoginCheck
    @PostMapping("deleteAICustomConfig")
    @ApiOperation(value = "AI客服告警设置-删除", notes = "删除", httpMethod = "POST")
    @ApiImplicitParams(@ApiImplicitParam(name = "id",value = "资源id",required = true))
    public Result deleteAICustomConfig(@RequestBody AICustomConfigRequestParam param, HttpServletRequest request){
        communityService.deleteAICustomConfig(param.getId());
        return ResultUtils.success();
    }

}
