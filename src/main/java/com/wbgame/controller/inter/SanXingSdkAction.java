package com.wbgame.controller.inter;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.controller.inter.pojo.IAppPaySDKConfig;
import com.wbgame.controller.inter.pojo.NotifyPayResultCallback;
import com.wbgame.controller.inter.pojo.Order;
import com.wbgame.controller.inter.pojo.OrderReq;
import com.wbgame.controller.inter.pojo.OrderRes;
import com.wbgame.controller.inter.pojo.SanXingPayVo;
import com.wbgame.controller.inter.pojo.SignHelper;
import com.wbgame.controller.inter.pojo.WbPayInfo;
import com.wbgame.service.AdService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 
 * <AUTHOR>
 * 
 */
@Controller
public class SanXingSdkAction {

	private static Logger log = LoggerFactory.getLogger(SanXingSdkAction.class);
	private static final String APPID = "500085761";
	private static final Integer WARES_ID = 1;//1:按次;2:开放价格
	private static final String CURRENCY = "RMB";
	//private static final String NOTIFY_URL = "http://zf.vigame.cn:8998/notifySx";
	private static final String NOTIFY_URL = "http://zf.vigame.cn:6998/notifySx";
	private static final String NOTIFY_URL_LX = "http://zf.vigame.cn:6998/notifyLx";
	private static final String NOTIFY_URL_LX_LJ = "http://zf.vigame.cn:6998/notifyLxlj";
	private static final String NOTIFY_URL_LX_TTXFK = "http://zf.vigame.cn:6998/notifyLxttxfk";
	private static final String NOTIFY_URL_LX_XMTG = "http://zf.vigame.cn:6998/notifyLxxmtg";
	
	@Autowired
	private AdService adService;
	
	/**
	 * 支付处理
	 * 
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping(value = "/notifyLxxmtg", produces = "text/html; charset=utf-8")
	public void notifyLxxmtg(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		StringBuffer sb = new StringBuffer() ; 
	      try{
		      InputStream is = request.getInputStream(); 
		      InputStreamReader isr = new InputStreamReader(is);   
		      BufferedReader br = new BufferedReader(isr); 
		      String s = "" ; 
		      while((s=br.readLine())!=null){ 
		      sb.append(s) ; 
		      } 
	        }catch(Exception ex)
	      	{ 
	        	log.error("客户端上传数据格式错误-----"+ex);
	      	}
	     String queryString =sb.toString(); 
	     
	     log.debug("lxttxfk notify param=>"+queryString);
	     
		String resultCode = "FAILURE";
		
		try {
			if(!StringUtils.isEmpty(queryString)){
				
				Map<String, String> signParams = getSignParams(queryString);
				
				String transdata = signParams.get("transdata");
				String sign = signParams.get("sign");
				String signtype = signParams.get("signtype");
				
				if(signtype!=null){
					if(SignHelper.verify(transdata, sign, IAppPaySDKConfig.PLATP_KEY_XMTG)){
						
						JSONObject resJs = JSONObject.parseObject(transdata);
						
						SanXingPayVo svo = new SanXingPayVo();
						svo.setOrderid(resJs.getString("transid"));
						svo.setStatus(resJs.getIntValue("result")==0?"success":"failed");
						
//						boolean res = XiaoMiSdkServiceFactory.getInstance().notifyLx(svo);
						boolean res = notifyLx(svo);
						if(res){
							log.info("lxttxfk notify save success!");
						}else{
							log.info("lxttxfk notify save fail!");
						}
						resultCode = "SUCCESS";
					}else{
						log.error("notifyLxttxfk=> verify is error#"+transdata+"#"+sign+"#"+IAppPaySDKConfig.PLATP_KEY_XMTG);
					}
				}else{
					log.error("notifyLxttxfk=> signtype is null.");
				}
				
				
				
			}else{
				resultCode = "FAILURE";
			}
		} catch (Exception e) {
			resultCode = "FAILURE";
			e.printStackTrace();
		}

		response.setContentType("application/json");
		response.getOutputStream().write(resultCode.getBytes());
	}

    /**
	 * 联想新支付回调
	 * @param request
	 * @param response
	 * @throws Exception
     */
	@RequestMapping(value = "/notifyLxxmtg/v1", produces = "text/html; charset=utf-8")
	public void notifyLxxmtgNew(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		StringBuffer sb = new StringBuffer() ;
		try{
			InputStream is = request.getInputStream();
			InputStreamReader isr = new InputStreamReader(is);
			BufferedReader br = new BufferedReader(isr);
			String s ;
			while((s=br.readLine())!=null){
				sb.append(s) ;
			}
		}catch(Exception ex)
		{
			log.error("客户端上传数据格式错误-----"+ex);
		}
		String queryString =sb.toString();

		log.debug("lxttxfk notify param=>"+queryString);

		String resultCode = "FAILURE";

		try {
			if(!StringUtils.isEmpty(queryString)){

				Map<String, String> signParams = getSignParams(queryString);

				String transdata = signParams.get("transdata");
				String sign = signParams.get("sign");
				String signtype = signParams.get("signtype");

				if(signtype!=null){
					if(SignHelper.verify(transdata, sign, IAppPaySDKConfig.PLATP_KEY_XMTG)){

						JSONObject resJs = JSONObject.parseObject(transdata);

						SanXingPayVo svo = new SanXingPayVo();
						svo.setOrderid(resJs.getString("transid"));
						svo.setStatus(resJs.getIntValue("result")==0?"success":"failed");
						/**
                         * 支付回调，状态更新
						 */
						WbPayInfo wbPayInfo = new WbPayInfo();
						wbPayInfo.setOrderid(resJs.getString("transid"));
						wbPayInfo.setOrderstatus(resJs.getIntValue("result")==0?"success":"failed");
//						String result = CommonServiceFactory.getInstance().updateWbPayInfoStatus(wbPayInfo);
						String result = updateWbPayInfoStatus(wbPayInfo);
						
						if(result.equals("success")){
							log.info("lxttxfk notify save success!");
						}else{
							log.info("lxttxfk notify save fail!");
						}
						resultCode = "SUCCESS";
					}else{
						log.error("notifyLxttxfk=> verify is error#"+transdata+"#"+sign+"#"+IAppPaySDKConfig.PLATP_KEY_XMTG);
					}
				}else{
					log.error("notifyLxttxfk=> signtype is null.");
				}



			}else{
				resultCode = "FAILURE";
			}
		} catch (Exception e) {
			resultCode = "FAILURE";
			e.printStackTrace();
		}

		response.setContentType("application/json");
		response.getOutputStream().write(resultCode.getBytes());
	}
	
	/**
	 * 支付处理
	 * 
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping(value = "/notifyLxttxfk", produces = "text/html; charset=utf-8")
	public void notifyLxttxfk(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		StringBuffer sb = new StringBuffer() ; 
	      try{
		      InputStream is = request.getInputStream(); 
		      InputStreamReader isr = new InputStreamReader(is);   
		      BufferedReader br = new BufferedReader(isr); 
		      String s = "" ; 
		      while((s=br.readLine())!=null){ 
		      sb.append(s) ; 
		      } 
	        }catch(Exception ex)
	      	{ 
	        	log.error("客户端上传数据格式错误-----"+ex);
	      	}
	     String queryString =sb.toString(); 
	     
	     log.debug("lxttxfk notify param=>"+queryString);
	     
		String resultCode = "FAILURE";
		
		try {
			if(!StringUtils.isEmpty(queryString)){
				
				Map<String, String> signParams = getSignParams(queryString);
				
				String transdata = signParams.get("transdata");
				String sign = signParams.get("sign");
				String signtype = signParams.get("signtype");
				
				if(signtype!=null){
					if(SignHelper.verify(transdata, sign, IAppPaySDKConfig.PLATP_KEY_TTXFK)){
						
						JSONObject resJs = JSONObject.parseObject(transdata);
						
						SanXingPayVo svo = new SanXingPayVo();
						svo.setOrderid(resJs.getString("transid"));
						svo.setStatus(resJs.getIntValue("result")==0?"success":"failed");
						
//						boolean res = XiaoMiSdkServiceFactory.getInstance().notifyLx(svo);
						boolean res = notifyLx(svo);
						if(res){
							log.info("lxttxfk notify save success!");
						}else{
							log.info("lxttxfk notify save fail!");
						}
						resultCode = "SUCCESS";
					}else{
						log.error("notifyLxttxfk=> verify is error#"+transdata+"#"+sign+"#"+IAppPaySDKConfig.PLATP_KEY_TTXFK);
					}
				}else{
					log.error("notifyLxttxfk=> signtype is null.");
				}
				
				
				
			}else{
				resultCode = "FAILURE";
			}
		} catch (Exception e) {
			resultCode = "FAILURE";
			e.printStackTrace();
		}

		response.setContentType("application/json");
		response.getOutputStream().write(resultCode.getBytes());
	}

    /**
	 * 新版支付回调
	 * @param request
	 * @param response
	 * @throws Exception
     */
	@RequestMapping(value = "/notifyLxttxfk/v1", produces = "text/html; charset=utf-8")
	public void notifyLxttxfknew(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		StringBuffer sb = new StringBuffer() ;
		try{
			InputStream is = request.getInputStream();
			InputStreamReader isr = new InputStreamReader(is);
			BufferedReader br = new BufferedReader(isr);
			String s = "" ;
			while((s=br.readLine())!=null){
				sb.append(s) ;
			}
		}catch(Exception ex)
		{
			log.error("客户端上传数据格式错误-----"+ex);
		}
		String queryString =sb.toString();

		log.debug("lxttxfk notify param=>"+queryString);

		String resultCode = "FAILURE";

		try {
			if(!StringUtils.isEmpty(queryString)){

				Map<String, String> signParams = getSignParams(queryString);

				String transdata = signParams.get("transdata");
				String sign = signParams.get("sign");
				String signtype = signParams.get("signtype");

				if(signtype!=null){
					if(SignHelper.verify(transdata, sign, IAppPaySDKConfig.PLATP_KEY_TTXFK)){

						JSONObject resJs = JSONObject.parseObject(transdata);

						SanXingPayVo svo = new SanXingPayVo();
						svo.setOrderid(resJs.getString("transid"));
						svo.setStatus(resJs.getIntValue("result")==0?"success":"failed");
						/**
						 * 支付回调，状态更新
						 */
						WbPayInfo wbPayInfo = new WbPayInfo();
						wbPayInfo.setOrderid(resJs.getString("transid"));
						wbPayInfo.setOrderstatus(resJs.getIntValue("result")==0?"success":"failed");
//						String result = CommonServiceFactory.getInstance().updateWbPayInfoStatus(wbPayInfo);
						String result = updateWbPayInfoStatus(wbPayInfo);
						
						if(result.equals("success")){
							log.info("lxttxfk notify save success!");
						}else{
							log.info("lxttxfk notify save fail!");
						}
						resultCode = "SUCCESS";
					}else{
						log.error("notifyLxttxfk=> verify is error#"+transdata+"#"+sign+"#"+IAppPaySDKConfig.PLATP_KEY_TTXFK);
					}
				}else{
					log.error("notifyLxttxfk=> signtype is null.");
				}



			}else{
				resultCode = "FAILURE";
			}
		} catch (Exception e) {
			resultCode = "FAILURE";
			e.printStackTrace();
		}

		response.setContentType("application/json");
		response.getOutputStream().write(resultCode.getBytes());
	}
	
	/**
	 * 支付处理
	 * 
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping(value = "/notifyLxlj", produces = "text/html; charset=utf-8")
	public void notifyLxlj(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		StringBuffer sb = new StringBuffer() ; 
	      try{
		      InputStream is = request.getInputStream(); 
		      InputStreamReader isr = new InputStreamReader(is);   
		      BufferedReader br = new BufferedReader(isr); 
		      String s = "" ; 
		      while((s=br.readLine())!=null){ 
		      sb.append(s) ; 
		      } 
	        }catch(Exception ex)
	      	{ 
	        	log.error("客户端上传数据格式错误-----"+ex);
	      	}
	     String queryString =sb.toString(); 
	     
	     log.debug("lxlj notify param=>"+queryString);
	     
		String resultCode = "FAILURE";
		
		try {
			if(!StringUtils.isEmpty(queryString)){
				
				Map<String, String> signParams = getSignParams(queryString);
				
				String transdata = signParams.get("transdata");
				String sign = signParams.get("sign");
				String signtype = signParams.get("signtype");
				
				if(signtype!=null){
					if(SignHelper.verify(transdata, sign, IAppPaySDKConfig.PLATP_KEY_LJ)){
						
						JSONObject resJs = JSONObject.parseObject(transdata);
						
						SanXingPayVo svo = new SanXingPayVo();
						svo.setOrderid(resJs.getString("transid"));
						svo.setStatus(resJs.getIntValue("result")==0?"success":"failed");
						
//						boolean res = XiaoMiSdkServiceFactory.getInstance().notifyLx(svo);
						boolean res = notifyLx(svo);
						if(res){
							log.info("lxlj notify save success!");
						}else{
							log.info("lxlj notify save fail!");
						}
						resultCode = "SUCCESS";
					}else{
						log.error("notifyLxlj=> verify is error#"+transdata+"#"+sign+"#"+IAppPaySDKConfig.PLATP_KEY_LJ);
					}
				}else{
					log.error("notifyLxlj=> signtype is null.");
				}
				
				
				
			}else{
				resultCode = "FAILURE";
			}
		} catch (Exception e) {
			resultCode = "FAILURE";
			e.printStackTrace();
		}

		response.setContentType("application/json");
		response.getOutputStream().write(resultCode.getBytes());
	}

    /**
	 * 新版联想回调
	 * @param request
	 * @param response
	 * @throws Exception
     */
	@RequestMapping(value = "/notifyLxlj/v1", produces = "text/html; charset=utf-8")
	public void notifyLxljNew(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		StringBuffer sb = new StringBuffer() ;
		try{
			InputStream is = request.getInputStream();
			InputStreamReader isr = new InputStreamReader(is);
			BufferedReader br = new BufferedReader(isr);
			String s ;
			while((s=br.readLine())!=null){
				sb.append(s) ;
			}
		}catch(Exception ex)
		{
			log.error("客户端上传数据格式错误-----"+ex);
		}
		String queryString =sb.toString();

		log.debug("lxlj notify param=>"+queryString);

		String resultCode = "FAILURE";

		try {
			if(!StringUtils.isEmpty(queryString)){

				Map<String, String> signParams = getSignParams(queryString);

				String transdata = signParams.get("transdata");
				String sign = signParams.get("sign");
				String signtype = signParams.get("signtype");

				if(signtype!=null){
					if(SignHelper.verify(transdata, sign, IAppPaySDKConfig.PLATP_KEY_LJ)){

						JSONObject resJs = JSONObject.parseObject(transdata);

						SanXingPayVo svo = new SanXingPayVo();
						svo.setOrderid(resJs.getString("transid"));
						svo.setStatus(resJs.getIntValue("result")==0?"success":"failed");

						WbPayInfo wbPayInfo = new WbPayInfo();
						wbPayInfo.setOrderid(resJs.getString("transid"));
						wbPayInfo.setOrderstatus(resJs.getIntValue("result")==0?"success":"failed");
//						String result = CommonServiceFactory.getInstance().updateWbPayInfoStatus(wbPayInfo);
						String result = updateWbPayInfoStatus(wbPayInfo);
						
						if(result.equals("success")){
							log.info("lxlj notify save success!");
						}else{
							log.info("lxlj notify save fail!");
						}
						resultCode = "SUCCESS";
					}else{
						log.error("notifyLxlj=> verify is error#"+transdata+"#"+sign+"#"+IAppPaySDKConfig.PLATP_KEY_LJ);
					}
				}else{
					log.error("notifyLxlj=> signtype is null.");
				}



			}else{
				resultCode = "FAILURE";
			}
		} catch (Exception e) {
			resultCode = "FAILURE";
			e.printStackTrace();
		}

		response.setContentType("application/json");
		response.getOutputStream().write(resultCode.getBytes());
	}
	/**
	 * 支付处理
	 * 
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping(value = "/getOrderLx", produces = "text/html; charset=utf-8")
	public void getOrderLx(HttpServletRequest request, HttpServletResponse response)
			throws Exception {

		String queryString = request.getQueryString();
		JSONObject jres = new JSONObject();
		String errCode = "200";
		String errMsg = "ok";
		log.debug("lx param value=>"+queryString);
		try {
			if(!StringUtils.isEmpty(queryString)){
				SanXingPayVo svo = new SanXingPayVo();
				Map<String, String> signParams = getSignParams(queryString);
				System.out.println("三星订单参数============"+queryString);
				String wbappid = signParams.get("appid");
				String out_trade_no=getCharAndNumr(20);//生成商户ID
				String transid = null;
				int payid = Integer.parseInt(signParams.get("payid"));
				if("16067".equals(wbappid)){//俄罗斯六角
					svo.setAppid(IAppPaySDKConfig.APP_ID_LJ);
					transid = Order.CheckSign(IAppPaySDKConfig.APP_ID_LJ, payid, signParams.get("waresname"), out_trade_no, Float.parseFloat (signParams.get("price")), signParams.get("appuserid"), signParams.get("pid"), NOTIFY_URL_LX_LJ,IAppPaySDKConfig.APPV_KEY_LJ,IAppPaySDKConfig.PLATP_KEY_LJ);
				}else if("14665".equals(wbappid)){//天天消方块
					svo.setAppid(IAppPaySDKConfig.APP_ID_TTXFK);
					transid = Order.CheckSign(IAppPaySDKConfig.APP_ID_TTXFK, payid, signParams.get("waresname"), out_trade_no, Float.parseFloat (signParams.get("price")), signParams.get("appuserid"), signParams.get("pid"), NOTIFY_URL_LX_TTXFK,IAppPaySDKConfig.APPV_KEY_TTXFK,IAppPaySDKConfig.PLATP_KEY_TTXFK);
				}else if("13065".equals(wbappid)
						|| "10266".equals(wbappid)
						|| "13665".equals(wbappid)){//消灭糖果
					svo.setAppid(IAppPaySDKConfig.APP_ID_XMTG);
					transid = Order.CheckSign(IAppPaySDKConfig.APP_ID_XMTG, payid, signParams.get("waresname"), out_trade_no, Float.parseFloat (signParams.get("price")), signParams.get("appuserid"), signParams.get("pid"), NOTIFY_URL_LX_XMTG,IAppPaySDKConfig.APPV_KEY_XMTG,IAppPaySDKConfig.PLATP_KEY_XMTG);
				}else{
					svo.setAppid(IAppPaySDKConfig.APP_ID);
					transid = Order.CheckSign(IAppPaySDKConfig.APP_ID, payid, signParams.get("waresname"), out_trade_no, Float.parseFloat (signParams.get("price")), signParams.get("appuserid"), signParams.get("pid"), NOTIFY_URL_LX,IAppPaySDKConfig.APPV_KEY,IAppPaySDKConfig.PLATP_KEY);
				}
				log.debug("ls get order res=>"+transid);
				
				if(!StringUtils.isEmpty(transid)){
					jres.put("transid", transid);
					svo.setCporderid(out_trade_no);
					svo.setOrderid(transid);
					svo.setPrice(signParams.get("price"));
					svo.setCurrency(CURRENCY);
					svo.setWaresid(String.valueOf(payid));
					svo.setWaresname(signParams.get("waresname"));
					svo.setPid(signParams.get("pid"));
					svo.setImei(signParams.get("appuserid"));
					
//					boolean res = XiaoMiSdkServiceFactory.getInstance().notifyLx(svo);
					boolean res = notifyLx(svo);
					if(res){
						log.info("lx pay save success!");
					}else{
						log.info("lx pay save fail!");
					}
				}else{
					jres.put("code", "1003");
					jres.put("errmsg", "transid is null.");
				}
				
			}else{
				jres.put("code", "1001");
				jres.put("errmsg", "param is null.");
			}
		} catch (Exception e) {
			jres.put("code", "1002");
			jres.put("errmsg", e.getMessage());
			e.printStackTrace();
		}
		jres.put("code", errCode);
		jres.put("errmsg", errMsg);
		response.setContentType("application/json");
		response.getOutputStream().write(jres.toString().getBytes());
	
	}

    /**
	 * 新版联想发起支付
	 * @param request
	 * @param response
	 * @throws Exception
     */
	@RequestMapping(value = "/getOrderLx/v1", produces = "text/html; charset=utf-8")
	public void getOrderLxNew(HttpServletRequest request, HttpServletResponse response)
			throws Exception {

		String queryString = request.getQueryString();
		JSONObject jres = new JSONObject();
		String errCode = "200";
		String errMsg = "ok";
		log.debug("lx param value=>"+queryString);
		try {
			if(!StringUtils.isEmpty(queryString)){
				SanXingPayVo svo = new SanXingPayVo();
				Map<String, String> signParams = getSignParams(queryString);
				System.out.println("三星订单参数============"+queryString);
				String wbappid = signParams.get("appid");
				String out_trade_no=getCharAndNumr(20);//生成商户ID
				String transid ;
				int payid = Integer.parseInt(signParams.get("payid"));
				if("16067".equals(wbappid)){//俄罗斯六角
					svo.setAppid(IAppPaySDKConfig.APP_ID_LJ);
					transid = Order.CheckSign(IAppPaySDKConfig.APP_ID_LJ, payid, signParams.get("waresname"), out_trade_no, Float.parseFloat (signParams.get("price")), signParams.get("appuserid"), signParams.get("pid"), NOTIFY_URL_LX_LJ,IAppPaySDKConfig.APPV_KEY_LJ,IAppPaySDKConfig.PLATP_KEY_LJ);
				}else if("14665".equals(wbappid)){//天天消方块
					svo.setAppid(IAppPaySDKConfig.APP_ID_TTXFK);
					transid = Order.CheckSign(IAppPaySDKConfig.APP_ID_TTXFK, payid, signParams.get("waresname"), out_trade_no, Float.parseFloat (signParams.get("price")), signParams.get("appuserid"), signParams.get("pid"), NOTIFY_URL_LX_TTXFK,IAppPaySDKConfig.APPV_KEY_TTXFK,IAppPaySDKConfig.PLATP_KEY_TTXFK);
				}else if("13065".equals(wbappid)
						|| "10266".equals(wbappid)
						|| "13665".equals(wbappid)){//消灭糖果
					svo.setAppid(IAppPaySDKConfig.APP_ID_XMTG);
					transid = Order.CheckSign(IAppPaySDKConfig.APP_ID_XMTG, payid, signParams.get("waresname"), out_trade_no, Float.parseFloat (signParams.get("price")), signParams.get("appuserid"), signParams.get("pid"), NOTIFY_URL_LX_XMTG,IAppPaySDKConfig.APPV_KEY_XMTG,IAppPaySDKConfig.PLATP_KEY_XMTG);
				}else{
					svo.setAppid(IAppPaySDKConfig.APP_ID);
					transid = Order.CheckSign(IAppPaySDKConfig.APP_ID, payid, signParams.get("waresname"), out_trade_no, Float.parseFloat (signParams.get("price")), signParams.get("appuserid"), signParams.get("pid"), NOTIFY_URL_LX,IAppPaySDKConfig.APPV_KEY,IAppPaySDKConfig.PLATP_KEY);
				}
				log.debug("ls get order res=>"+transid);

				if(!StringUtils.isEmpty(transid)){
					jres.put("transid", transid);
					svo.setCporderid(out_trade_no);
					svo.setOrderid(transid);
					svo.setPrice(signParams.get("price"));
					svo.setCurrency(CURRENCY);
					svo.setWaresid(String.valueOf(payid));
					svo.setWaresname(signParams.get("waresname"));
					svo.setPid(signParams.get("pid"));
					svo.setImei(signParams.get("appuserid"));

					WbPayInfo wbPayInfo = new WbPayInfo();
					wbPayInfo.setOrderid(transid);
					wbPayInfo.setUid(out_trade_no);
					DecimalFormat df = new DecimalFormat("0");
					wbPayInfo.setMoney(df.format(Double.valueOf(signParams.get("price"))*100));
					wbPayInfo.setPid(signParams.get("pid"));
					wbPayInfo.setImei(signParams.get("appuserid"));
					wbPayInfo.setPayname(signParams.get("waresname"));
					wbPayInfo.setAppid(signParams.get("appid"));
					wbPayInfo.setPaytype("lx");
//					String result = CommonServiceFactory.getInstance().updateWbPayInfo(wbPayInfo);
					String ins = "insert into wb_pay_info(orderid,uid,money,paytype,imei,pid,appid,orderstatus, "+
					        "payname,paynote,createtime,param1,param2,param3) "+
							"values(#{obj.orderid},#{obj.uid},#{obj.money},#{obj.paytype},#{obj.imei},#{obj.pid},#{obj.appid}, "+
					        "#{obj.orderstatus},#{obj.payname},#{obj.paynote},now(),#{obj.param1},#{obj.param2},#{obj.param3})";
					int code = adService.execSqlHandle(ins, wbPayInfo);
					
					if(code > 0){
						log.info("lx pay save success!");
					}else{
						log.info("lx pay save fail!");
					}
				}else{
					jres.put("code", "1003");
					jres.put("errmsg", "transid is null.");
				}

			}else{
				jres.put("code", "1001");
				jres.put("errmsg", "param is null.");
			}
		} catch (Exception e) {
			jres.put("code", "1002");
			jres.put("errmsg", e.getMessage());
			e.printStackTrace();
		}
		jres.put("code", errCode);
		jres.put("errmsg", errMsg);
		response.setContentType("application/json");
		response.getOutputStream().write(jres.toString().getBytes());

	}
	
	/**
	 * 支付处理
	 * 
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping(value = "/getOrder", produces = "text/html; charset=utf-8")
	public void getOrder(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		String queryString = request.getQueryString();
		JSONObject jres = new JSONObject();
		log.debug("param value=>"+queryString);
		try {
			if(!StringUtils.isEmpty(queryString)){
				Map<String, String> signParams = getSignParams(queryString);
				String out_trade_no=getCharAndNumr(20);//生成商户ID
				OrderReq orderReq = new OrderReq();
				orderReq.setAppId (APPID);
				orderReq.setWaresId (WARES_ID);
				orderReq.setWarasName (signParams.get("waresname"));
				orderReq.setAppUserId (signParams.get("appuserid"));
				orderReq.setCpOrderId (out_trade_no);
				orderReq.setCpPrivateInfo (signParams.get("pid"));
				orderReq.setCurrency (CURRENCY);
				orderReq.setNotifyUrl (NOTIFY_URL);
				orderReq.setPrice (Float.parseFloat (signParams.get("price")));
				OrderRes orderRes = orderReq.send();
				String transid = orderRes.getTransid();
				String errCode = orderRes.getErrorCode();
				String errMsg = orderRes.getErrMsg();
				
				log.debug("get order res=>"+transid+"#"+errCode+"#"+errMsg);
				
				if(!StringUtils.isEmpty(transid)){
					jres.put("transid", transid);
					SanXingPayVo svo = new SanXingPayVo();
					svo.setAppid(APPID);
					svo.setCporderid(out_trade_no);
					svo.setOrderid(transid);
					svo.setPrice(signParams.get("price"));
					svo.setCurrency(CURRENCY);
					svo.setWaresid(String.valueOf(WARES_ID));
					svo.setWaresname(signParams.get("waresname"));
					svo.setPid(signParams.get("pid"));
					svo.setImei(signParams.get("appuserid"));
					
//					boolean res = XiaoMiSdkServiceFactory.getInstance().notifySanXing(svo);
					boolean res = notifySanXing(svo);
					if(res){
						log.info("pay save success!");
					}else{
						log.info("pay save fail!");
					}
				}else{
					jres.put("code", errCode);
					jres.put("errmsg", errMsg);
				}
				
			}else{
				jres.put("code", "1001");
				jres.put("errmsg", "param is null.");
			}
		} catch (Exception e) {
			jres.put("code", "1002");
			jres.put("errmsg", e.getMessage());
			e.printStackTrace();
		}

		response.setContentType("application/json");
		response.getOutputStream().write(jres.toString().getBytes());
	}

    /**
	 * 新版三星发起支付
	 * @param request
	 * @param response
	 * @throws Exception
     */
	@RequestMapping(value = "/getOrder/v1", produces = "text/html; charset=utf-8")
	public void getOrdernew(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		String queryString = request.getQueryString();
		JSONObject jres = new JSONObject();
		log.debug("param value=>"+queryString);
		try {
			if(!StringUtils.isEmpty(queryString)){
				Map<String, String> signParams = getSignParams(queryString);
				String out_trade_no=getCharAndNumr(20);//生成商户ID
				OrderReq orderReq = new OrderReq();
				orderReq.setAppId (APPID);
				orderReq.setWaresId (WARES_ID);
				orderReq.setWarasName (signParams.get("waresname"));
				orderReq.setAppUserId (signParams.get("appuserid"));
				orderReq.setCpOrderId (out_trade_no);
				orderReq.setCpPrivateInfo (signParams.get("pid"));
				orderReq.setCurrency (CURRENCY);
				orderReq.setNotifyUrl (NOTIFY_URL);
				orderReq.setPrice (Float.parseFloat (signParams.get("price")));
				OrderRes orderRes = orderReq.send();
				String transid = orderRes.getTransid();
				String errCode = orderRes.getErrorCode();
				String errMsg = orderRes.getErrMsg();

				log.debug("get order res=>"+transid+"#"+errCode+"#"+errMsg);

				if(!StringUtils.isEmpty(transid)){
					jres.put("transid", transid);
					SanXingPayVo svo = new SanXingPayVo();
					svo.setAppid(APPID);
					svo.setCporderid(out_trade_no);
					svo.setOrderid(transid);
					svo.setPrice(signParams.get("price"));
					svo.setCurrency(CURRENCY);
					svo.setWaresid(String.valueOf(WARES_ID));
					svo.setWaresname(signParams.get("waresname"));
					svo.setPid(signParams.get("pid"));
					svo.setImei(signParams.get("appuserid"));

					WbPayInfo wbPayInfo = new WbPayInfo();
					wbPayInfo.setOrderid(transid);
					wbPayInfo.setUid(out_trade_no);
					DecimalFormat df = new DecimalFormat("0");
					wbPayInfo.setMoney(df.format(Double.valueOf(signParams.get("price"))*100));
					wbPayInfo.setPid(signParams.get("pid"));
					wbPayInfo.setImei(signParams.get("appuserid"));
					wbPayInfo.setPayname(signParams.get("waresname"));
					wbPayInfo.setAppid(signParams.get("appid"));
					wbPayInfo.setPaytype("sx");
					//boolean res = XiaoMiSdkServiceFactory.getInstance().notifyLx(svo);
//					String result = CommonServiceFactory.getInstance().updateWbPayInfo(wbPayInfo);
					String ins = "insert into wb_pay_info(orderid,uid,money,paytype,imei,pid,appid,orderstatus, "+
					        "payname,paynote,createtime,param1,param2,param3) "+
							"values(#{obj.orderid},#{obj.uid},#{obj.money},#{obj.paytype},#{obj.imei},#{obj.pid},#{obj.appid}, "+
					        "#{obj.orderstatus},#{obj.payname},#{obj.paynote},now(),#{obj.param1},#{obj.param2},#{obj.param3})";
					int code = adService.execSqlHandle(ins, wbPayInfo);
					
					if(code > 0){

					//boolean res = XiaoMiSdkServiceFactory.getInstance().notifySanXing(svo);
					//if(res){
						log.info("pay save success!");
					}else{
						log.info("pay save fail!");
					}
				}else{
					jres.put("code", errCode);
					jres.put("errmsg", errMsg);
				}

			}else{
				jres.put("code", "1001");
				jres.put("errmsg", "param is null.");
			}
		} catch (Exception e) {
			jres.put("code", "1002");
			jres.put("errmsg", e.getMessage());
			e.printStackTrace();
		}

		response.setContentType("application/json");
		response.getOutputStream().write(jres.toString().getBytes());
	}
	
	/**
	 * 支付处理
	 * 
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping(value = "/notifyLx", produces = "text/html; charset=utf-8")
	public void notifyLx(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		StringBuffer sb = new StringBuffer() ; 
	      try{
		      InputStream is = request.getInputStream(); 
		      InputStreamReader isr = new InputStreamReader(is);   
		      BufferedReader br = new BufferedReader(isr); 
		      String s = "" ; 
		      while((s=br.readLine())!=null){ 
		      sb.append(s) ; 
		      } 
	        }catch(Exception ex)
	      	{ 
	        	log.error("客户端上传数据格式错误-----"+ex);
	      	}
	     String queryString =sb.toString(); 
	     
	     log.debug("lx notify param=>"+queryString);
	     
		String resultCode = "FAILURE";
		
		try {
			if(!StringUtils.isEmpty(queryString)){
				
				Map<String, String> signParams = getSignParams(queryString);
				
				String transdata = signParams.get("transdata");
				String sign = signParams.get("sign");
				String signtype = signParams.get("signtype");
				
				if(signtype!=null){
					if(SignHelper.verify(transdata, sign, IAppPaySDKConfig.PLATP_KEY)){
						
						JSONObject resJs = JSONObject.parseObject(transdata);
						
						SanXingPayVo svo = new SanXingPayVo();
						svo.setOrderid(resJs.getString("transid"));
						svo.setStatus(resJs.getIntValue("result")==0?"success":"failed");
						
//						boolean res = XiaoMiSdkServiceFactory.getInstance().notifyLx(svo);
						boolean res = notifyLx(svo);
						if(res){
							log.info("lx notify save success!");
						}else{
							log.info("lx notify save fail!");
						}
						resultCode = "SUCCESS";
					}else{
						log.error("notifyLx=> verify is error#"+transdata+"#"+sign+"#"+IAppPaySDKConfig.PLATP_KEY);
					}
				}else{
					log.error("notifyLx=> signtype is null.");
				}
				
				
				
			}else{
				resultCode = "FAILURE";
			}
		} catch (Exception e) {
			resultCode = "FAILURE";
			e.printStackTrace();
		}

		response.setContentType("application/json");
		response.getOutputStream().write(resultCode.getBytes());
	}

    /**
	 * 新版联想回调
	 * @param request
	 * @param response
	 * @throws Exception
     */
	@RequestMapping(value = "/notifyLx/v1", produces = "text/html; charset=utf-8")
	public void notifyLxnew(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		StringBuffer sb = new StringBuffer() ;
		try{
			InputStream is = request.getInputStream();
			InputStreamReader isr = new InputStreamReader(is);
			BufferedReader br = new BufferedReader(isr);
			String s = "" ;
			while((s=br.readLine())!=null){
				sb.append(s) ;
			}
		}catch(Exception ex)
		{
			log.error("客户端上传数据格式错误-----"+ex);
		}
		String queryString =sb.toString();

		log.debug("lx notify param=>"+queryString);

		String resultCode = "FAILURE";

		try {
			if(!StringUtils.isEmpty(queryString)){

				Map<String, String> signParams = getSignParams(queryString);

				String transdata = signParams.get("transdata");
				String sign = signParams.get("sign");
				String signtype = signParams.get("signtype");

				if(signtype!=null){
					if(SignHelper.verify(transdata, sign, IAppPaySDKConfig.PLATP_KEY)){

						JSONObject resJs = JSONObject.parseObject(transdata);

						SanXingPayVo svo = new SanXingPayVo();
						svo.setOrderid(resJs.getString("transid"));
						svo.setStatus(resJs.getIntValue("result")==0?"success":"failed");


						WbPayInfo wbPayInfo = new WbPayInfo();
						wbPayInfo.setOrderid(resJs.getString("transid"));
						wbPayInfo.setOrderstatus(resJs.getIntValue("result")==0?"success":"failed");
//						String result = CommonServiceFactory.getInstance().updateWbPayInfoStatus(wbPayInfo);
						String result = updateWbPayInfoStatus(wbPayInfo);
						
						if(result.equals("success")){
							log.info("lx notify save success!");
						}else{
							log.info("lx notify save fail!");
						}
						resultCode = "SUCCESS";
					}else{
						log.error("notifyLx=> verify is error#"+transdata+"#"+sign+"#"+IAppPaySDKConfig.PLATP_KEY);
					}
				}else{
					log.error("notifyLx=> signtype is null.");
				}



			}else{
				resultCode = "FAILURE";
			}
		} catch (Exception e) {
			resultCode = "FAILURE";
			e.printStackTrace();
		}

		response.setContentType("application/json");
		response.getOutputStream().write(resultCode.getBytes());
	}
	
	/**
	 * 支付处理
	 * 
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping(value = "/notifySx", produces = "text/html; charset=utf-8")
	public void notify(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		StringBuffer sb = new StringBuffer() ; 
	      try{
		      InputStream is = request.getInputStream(); 
		      InputStreamReader isr = new InputStreamReader(is);   
		      BufferedReader br = new BufferedReader(isr); 
		      String s = "" ; 
		      while((s=br.readLine())!=null){ 
		      sb.append(s) ; 
		      } 
	        }catch(Exception ex)
	      	{ 
	        	log.error("客户端上传数据格式错误-----"+ex);
	      	}
	     String queryString =sb.toString(); 
	     
	     log.debug("notify param=>"+queryString);
	     
		String resultCode = "SUCCESS";
		try {
			if(!StringUtils.isEmpty(queryString)){
				
				NotifyPayResultCallback nrc = new NotifyPayResultCallback(queryString);
				SanXingPayVo svo = new SanXingPayVo();
				svo.setOrderid(nrc.getTransId());
				svo.setStatus(nrc.getResult()==0?"success":"failed");
				
//				boolean res = XiaoMiSdkServiceFactory.getInstance().notifySanXing(svo);
				boolean res = notifySanXing(svo);
				
				if(res){
					log.info("notify save success!");
				}else{
					log.info("notify save fail!");
				}
			}else{
				resultCode = "FAILURE";
			}
		} catch (Exception e) {
			resultCode = "FAILURE";
			e.printStackTrace();
		}

		response.setContentType("application/json");
		response.getOutputStream().write(resultCode.getBytes());
	}

    /**
	 * 新版三星回调
	 * @param request
	 * @param response
	 * @throws Exception
     */
	@RequestMapping(value = "/notifySx/v1", produces = "text/html; charset=utf-8")
	public void notifynew(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		StringBuffer sb = new StringBuffer() ;
		try{
			InputStream is = request.getInputStream();
			InputStreamReader isr = new InputStreamReader(is);
			BufferedReader br = new BufferedReader(isr);
			String s = "" ;
			while((s=br.readLine())!=null){
				sb.append(s) ;
			}
		}catch(Exception ex)
		{
			log.error("客户端上传数据格式错误-----"+ex);
		}
		String queryString =sb.toString();

		log.debug("notify param=>"+queryString);

		String resultCode = "SUCCESS";
		try {
			if(!StringUtils.isEmpty(queryString)){

				NotifyPayResultCallback nrc = new NotifyPayResultCallback(queryString);
				SanXingPayVo svo = new SanXingPayVo();
				svo.setOrderid(nrc.getTransId());
				svo.setStatus(nrc.getResult()==0?"success":"failed");

				WbPayInfo wbPayInfo = new WbPayInfo();
				wbPayInfo.setOrderid(nrc.getTransId());
				wbPayInfo.setOrderstatus(nrc.getResult()==0?"success":"failed");
//				String result = CommonServiceFactory.getInstance().updateWbPayInfoStatus(wbPayInfo);
				String result = updateWbPayInfoStatus(wbPayInfo);
				
				if(result.equals("success")){
					log.info("notify save success!");
				}else{
					log.info("notify save fail!");
				}
			}else{
				resultCode = "FAILURE";
			}
		} catch (Exception e) {
			resultCode = "FAILURE";
			e.printStackTrace();
		}

		response.setContentType("application/json");
		response.getOutputStream().write(resultCode.getBytes());
	}

	/**
	 * 随机字符串
	 * @param length
	 * @return
	 */
	 public static String getCharAndNumr(int length) {
	  String val = "";
	  Random random = new Random();
	  for (int i = 0; i < length; i++) {
	   // 输出字母还是数字
	   String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num"; 
	   // 字符串
	   if ("char".equalsIgnoreCase(charOrNum)) {
	    // 取得大写字母还是小写字母
	    int choice = random.nextInt(2) % 2 == 0 ? 65 : 97; 
	    val += (char) (choice + random.nextInt(26));
	   } else if ("num".equalsIgnoreCase(charOrNum)) { // 数字
	    val += String.valueOf(random.nextInt(10));
	   }
	  }
	  return val;
	 }

	private String isNull(String s){
		if(s!=null){
			return s;
		}else{
			return "";
		}
	}
	
	private Map<String, String> getSignParams(String queryString)
			throws UnsupportedEncodingException {

		Map<String, String> result = new HashMap<String, String>();
		String[] params = queryString.split("&");
		for (String param : params) {
			String[] tmp = param.split("=");
			if(tmp!=null && tmp.length>1){
				String key = tmp[0];
				result.put(key, URLDecoder.decode(tmp[1], "UTF-8"));
			}
		}
		return result;
	}
	
	public boolean notifyLx(SanXingPayVo svo){
		String sql = "insert into lianxiang_pay_info(appid,orderid,price,currency,waresid,waresname,pid,imei,cporderid,ordertime) "+
				"values(#{obj.appid,jdbcType=VARCHAR},#{obj.orderid,jdbcType=VARCHAR},#{obj.price,jdbcType=VARCHAR},#{obj.currency,jdbcType=VARCHAR},#{obj.waresid,jdbcType=VARCHAR}, "+
		        "#{obj.waresname,jdbcType=VARCHAR},#{obj.pid,jdbcType=VARCHAR},#{obj.imei,jdbcType=VARCHAR},#{obj.cporderid,jdbcType=VARCHAR},now()) "+
		        "ON DUPLICATE KEY UPDATE status = #{obj.status,jdbcType=VARCHAR},ordertime = now()";
		int code = adService.execSqlHandle(sql, svo);
		if(code > 0)
			return true;
		else
			return false;
	}
	public boolean notifySanXing(SanXingPayVo svo){
		String sql = "insert into sanxing_pay_info(appid,orderid,price,currency,waresid,waresname,pid,imei,cporderid,ordertime) "+
				"values(#{obj.appid,jdbcType=VARCHAR},#{obj.orderid,jdbcType=VARCHAR},#{obj.price,jdbcType=VARCHAR},#{obj.currency,jdbcType=VARCHAR},#{obj.waresid,jdbcType=VARCHAR}, "+
		        "#{obj.waresname,jdbcType=VARCHAR},#{obj.pid,jdbcType=VARCHAR},#{obj.imei,jdbcType=VARCHAR},#{obj.cporderid,jdbcType=VARCHAR},now()) "+
		        "ON DUPLICATE KEY UPDATE status = #{obj.status,jdbcType=VARCHAR},ordertime = now()";
		int code = adService.execSqlHandle(sql, svo);
		if(code > 0)
			return true;
		else
			return false;
	}
	public String updateWbPayInfoStatus(WbPayInfo wbPayInfo){
		String up = "update wb_pay_info set orderstatus = #{obj.orderstatus} where orderid = #{obj.orderid}";
		int code = adService.execSqlHandle(up, wbPayInfo);
		
		if(code > 0)
			return "success";
		else
			return "fail";
	}
	
}
