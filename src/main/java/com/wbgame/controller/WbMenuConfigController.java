package com.wbgame.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.mapper.master.WbSysV3Mapper;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.CurrMenuVo;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.WbMenuConfigVo;
import com.wbgame.pojo.adv2.DnExtendGroupMonitorVo;
import com.wbgame.pojo.product.DnwxX4dataVo;
import com.wbgame.service.product.ProductReportService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import org.apache.commons.codec.binary.Base64;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description: 界面详情配_新
 * @author: caow
 * @date: 2023/09/19
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/main")
public class WbMenuConfigController {

    @Autowired
    private WbSysV3Mapper wbSysV3Mapper;
    @Autowired
    private YyhzMapper yyhzMapper;


    @Autowired
    private RedisTemplate redisTemplate;


    /**
     * 查询菜单
     * @param cur 当前菜单对象
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/menuConfig/list", method = { RequestMethod.GET, RequestMethod.POST })
    public String v3Menu(WbMenuConfigVo cur, HttpServletRequest request, HttpServletResponse response) {

        Map<String, String> paramMap = BlankUtils.getParameter(request, "fm_config_list");

        /* 进行token验证 */
//        String token = request.getParameter("token");
//        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//            return "{\"ret\":2,\"msg\":\"token is error!\"}";
//        else
//            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        JSONObject result = new JSONObject();
        try {
            if(!BlankUtils.checkBlank(paramMap.get("fm_config_list"))){
                cur.setConfig_list(paramMap.get("fm_config_list").split(","));
            }

            /* 库检索菜单数据 */
            PageHelper.startPage(paramMap);
            List<WbMenuConfigVo> list = wbSysV3Mapper.selectSysmarkMenuConfig(cur);
            long size = ((Page) list).getTotal();

            // custom自定义字段 转换为json对象返回
            List<WbMenuConfigVo> collect = list.stream().map(act -> {
                if(!BlankUtils.checkBlank(act.getCustom())){
                    String json = new String(Base64.decodeBase64(act.getCustom()));
                    if(BlankUtils.isJSONObject(json))
                        act.setCustomJson(JSONObject.parseObject(json));
                    else
                        act.setCustomJson(new JSONObject());
                    act.setCustom(null);
                }
                return act;
            }).collect(Collectors.toList());

            /* 格式化输出 */
            result.put("ret", 1);
            result.put("msg", "success");
            result.put("data", collect);
            result.put("totalCount", size);
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }

    /**
     * 导出菜单
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/menuConfig/export", method={RequestMethod.GET, RequestMethod.POST})
    public void menuConfig_export(WbMenuConfigVo cur,HttpServletRequest request,HttpServletResponse response) {

        Map<String, String> paramMap = BlankUtils.getParameter(request, "fm_config_list");
        if(!BlankUtils.checkBlank(paramMap.get("fm_config_list"))){
            cur.setConfig_list(paramMap.get("fm_config_list").split(","));
        }

        List<WbMenuConfigVo> list = wbSysV3Mapper.selectSysmarkMenuConfig(cur);
        // custom自定义字段 转换为json对象返回
        List<WbMenuConfigVo> collect = list.stream().map(act -> {
            if(!BlankUtils.checkBlank(act.getCustom())){
                String json = new String(Base64.decodeBase64(act.getCustom()));
                if(BlankUtils.isJSONObject(json))
                    act.setCustomJson(JSONObject.parseObject(json));
                else
                    act.setCustomJson(new JSONObject());
                act.setCustom(null);
            }
            return act;
        }).collect(Collectors.toList());

        List<Map<String, Object>> contentList = new ArrayList<Map<String,Object>>();
        for (WbMenuConfigVo act : collect) {

            Map<String, Object> obj = (Map<String, Object>)JSONObject.parseObject(JSONObject.toJSONString(act), Map.class);
            contentList.add(obj);
        }


        Map<String, String> headerMap = new LinkedHashMap<String, String>();

        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "界面详情配置_新_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX(response,contentList,headerMap,fileName);
    }


    /**
     * 操作菜单配置 新增修改删除
     * @param cur
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/menuConfig/handle", method = { RequestMethod.GET, RequestMethod.POST })
    public String adv2MenuSet(CurrMenuVo cur, HttpServletRequest request, HttpServletResponse response) {

        JSONObject result = new JSONObject();
        try {
            /* 进行token验证 */
            String token = request.getParameter("token");
            if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            else
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);

            // 对custom自定义json进行编码存储
            if(!BlankUtils.checkBlank(cur.getCustom())){
                cur.setCustom(Base64.encodeBase64String(cur.getCustom().getBytes()));
            }

            /* 根据操作标识符确定操作方式 */
            String handle = request.getParameter("handle");
            int res = 0;
            if ("add".equals(handle)) {
                cur.setCuser(cuser.getLogin_name());
                res = wbSysV3Mapper.insertSysmarkMenu(cur);
            } else if ("edit".equals(handle)) {
                cur.setEuser(cuser.getLogin_name());
                res = wbSysV3Mapper.updateSysmarkMenu(cur);
            } else if ("del".equals(handle)) {
                res = wbSysV3Mapper.deleteSysmarkMenu(cur);
            } else {
                return "{\"ret\":0,\"msg\":\"handle is error!\"}";
            }

            /* 格式化输出 */
            result.put("ret", res);
            result.put("msg", "success");
            return result.toJSONString();

        } catch (Exception e) {
            e.printStackTrace();

            if(e.toString().contains("Duplicate entry")) {
                result.put("ret", 0);
                result.put("msg", "错误信息 : 标识已存在!");
            }else{
                result.put("ret", 0);
                result.put("msg", "错误信息 : " + e.getMessage());
            }
            return result.toJSONString();
        }
    }

}
