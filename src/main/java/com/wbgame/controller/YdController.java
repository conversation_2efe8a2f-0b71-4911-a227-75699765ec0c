package com.wbgame.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.aop.LoginCheck;
import com.wbgame.common.Asserts;
import com.wbgame.common.ProtocolConstant;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.master.SysconfigMapper;
import com.wbgame.mapper.master.YdMapper;
import com.wbgame.mapper.slave.WbSysMapper;
import com.wbgame.mapper.slave2.YdSlave2Mapper;
import com.wbgame.pojo.*;
import com.wbgame.pojo.custom.GDTWorkReportVo;
import com.wbgame.service.*;
import com.wbgame.utils.*;
import jxl.Sheet;
import jxl.Workbook;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 移动
 *
 * <AUTHOR>
 * @date 2019年12月23日
 */

@CrossOrigin
@RestController
public class YdController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private YdService ydService;
    @Autowired
    private YdMapper ydMapper;
    @Autowired
    private WxGameService wxService;
    @Autowired
    private AdmsgService admsgService;
    @Autowired
    private AdService adService;
	@Autowired
	AdMapper adMapper;
    @Autowired
    private WbSysMapper wbSysMapper;
    @Autowired
    private SysconfigMapper sysconfigMapper;
    @Autowired
    private YdSlave2Mapper xyxMapper;


    /**
     * 新版系统参数配置 协议测试
     *
     * @param ver 版本号
     * @return
     */
    @RequestMapping(value = "/yd/testNOParam", method = RequestMethod.GET)
    public void testNOParam(String ver,HttpServletRequest request,HttpServletResponse response) throws Exception {

        String mmStr = "";
        int v = 0;
        if (!BlankUtils.checkBlank(ver)) {
            v = Integer.parseInt(ver.substring(1, ver.length()));
        }

        String pid = request.getParameter("pid");
        pid = BlankUtils.checkBlank(pid)?"333351":pid;

        String appid = request.getParameter("appid");
        appid = BlankUtils.checkBlank(appid)?"13665":appid;

        String cha_id = request.getParameter("cha_id");
        cha_id = BlankUtils.checkBlank(cha_id)?"csj":cha_id;

        String buy_id = request.getParameter("buy_id");

        String param = "pid="+ pid +"&net=2&lsn=243212452&imsi=460079596154889&imei=866378033846356"+
        		"&appid="+appid+"&chlid="+cha_id+"&buy_id="+buy_id+"&mmappid=&cyclepays=0&wx=1&adid=&os_version=7.1.1&gametimes=883&deviceType=1&locale=";

        String value = Base64.encodeBase64String(param.getBytes());

        if (v >= 4) {
            value = Base64.encodeBase64String(AESUtil.encrypt(
                    param.getBytes(), AESUtil.KEY.getBytes(), AESUtil.KEY.getBytes()));
        }

        String getUrl = ProtocolConstant.mm_cfg_cn_url + ver + "?value=" + value;

        mmStr = HttpClientUtils.getInstance().httpGet(getUrl);

        //如果版本号大于等于4需要解密加密
        if (v >= 4) {
            mmStr = new String(AESUtil.decrypt(Base64.decodeBase64(mmStr), AESUtil.KEY.getBytes(), AESUtil.KEY.getBytes()), "UTF-8");
        }

        //输出xml格式
        response.setContentType("text/xml; charset=utf-8");
        ServletOutputStream outputStream = response.getOutputStream();
        outputStream.write(mmStr.getBytes("utf-8"));

    }

    @RequestMapping(value = "/yd/hbBoundsList", method = RequestMethod.POST)
    public String hbBoundsList(HttpServletRequest request, HttpServletResponse response) {

	    JSONObject result = new JSONObject();
		String type = " select type_id,type_name from hb_bounds_type_info ";
		String money = " select money_id, left(money_name,char_length(money_name)-1) money_name  from hb_bounds_money_info ";

		result.put("ret", 1);
		result.put("type", adService.queryListMap(type));
		result.put("money", adService.queryListMap(money));
		return  result.toJSONString();
    }

    /**
     * 红包登录参数配置 查询
     *
     * @param redpackWxconfigInfo
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/yd/selectRedpackWxconfigInfo", method = RequestMethod.POST)
    public String selectRedpackWxconfigInfo(RedpackWxconfigInfo redpackWxconfigInfo,
                                            HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<RedpackWxconfigInfo> list = ydService.selectRedpackWxconfig(redpackWxconfigInfo);
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 红包登录参数配置 操作
     *
     * @param request
     * @param redpackWxconfigInfo
     * @param response
     * @return
     */
    @RequestMapping(value = "/yd/redpackWxconfigInfoHandle", method = RequestMethod.POST)
    public String RedpackWxconfigInfoHandle(HttpServletRequest request, RedpackWxconfigInfo redpackWxconfigInfo, HttpServletResponse response) {

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        int result = 0;
        try {

            if ("add".equals(request.getParameter("handle"))) {
                try {
                    result = ydService.insertRedpackWxconfig(redpackWxconfigInfo);
                } catch (DataAccessException e) {
                    return "{\"ret\":0,\"msg\":\"存在相同的内部应用和包名的数据!\"}";
                }

            } else if ("edit".equals(request.getParameter("handle"))) {
                result = ydService.updateRedpackWxconfig(redpackWxconfigInfo);
            } else if ("del".equals(request.getParameter("handle"))) {
                result = ydService.deleteRedpackWxconfig(redpackWxconfigInfo);
            }

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    @CrossOrigin
    @RequestMapping(value = "/wx/importGameAdList", method = {RequestMethod.POST})
    public @ResponseBody
    String importGameAdList(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {

        JSONObject obj = new JSONObject();
        int error = 0;

        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<GameAdInfo> list = new ArrayList<GameAdInfo>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    error = r + 1;
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;

                    String[] vals = new String[8];
                    for (int c = 0; c < 8; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }

                    GameAdInfo gad = new GameAdInfo();
                    gad.setcDate(vals[0]);
                    if (vals[0] != null && !vals[0].isEmpty()) {
                        // 上传过来的日期格式
                        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy/MM/dd");
                        boolean flag = false;
                        try {
                            if (vals[0].split("/").length != 3
                                    || vals[0].split("/")[0].length() != 4) {
                                flag = true;
                            }

                            String adv_dt = DateTime.parse(vals[0], format).toString("yyyy-MM-dd");
                            gad.setcDate(adv_dt);
                        } catch (Exception e) {
                            flag = true;
                        }

                        if (flag) {
                            obj.put("ret", 0);
                            obj.put("msg", "日期格式不正确，需要 yyyy/MM/dd 的格式！");
                            return obj.toString();
                        }
                    }

                    Map<String, Map<String, Object>> appInfoMap = sysconfigMapper.selectAppInfoMaps();

                    if (vals[1] == null || "".equals(vals[1]) || vals[1].length() == 0 || appInfoMap.get(vals[1]) == null) {
                        obj.put("ret", 0);
                        obj.put("msg", vals[1] + "该名称匹配不到对象appid,请查看");
                        return obj.toJSONString();
                    } else {
                        gad.setcGame(Integer.parseInt((String) (appInfoMap.get(vals[1]).get("id"))));
                    }

                    gad.setChannel(vals[2].trim());

                    if (vals[3] == null || "".equals(vals[3]) || vals[3].length() == 0) {
                        gad.setAmount(BigDecimal.ZERO);
                    } else {
                        gad.setAmount(new BigDecimal(vals[3].trim()));
                    }

                    if (vals[4] == null || "".equals(vals[4]) || vals[4].length() == 0) {
                        gad.setAddNum(0);
                    } else {
                        try {
                            gad.setAddNum(Integer.valueOf(vals[4].trim()));
                        } catch (Exception e) {
                            obj.put("ret", 0);
                            obj.put("msg", "第" + error + "行：'" + vals[4] + "' 可能格式有误");
                            return obj.toJSONString();
                        }
                    }

                    if (vals[5] == null || "".equals(vals[5]) || vals[5].length() == 0) {
                        gad.setDau(0);
                    } else {
                        try {
                            gad.setDau(Integer.valueOf(vals[5].trim()));
                        } catch (Exception e) {
                            obj.put("ret", 0);
                            obj.put("msg", "第" + error + "行： '" + vals[5] + "' 可能格式有误");
                            return obj.toJSONString();
                        }
                    }

                    if (vals[6] == null || "".equals(vals[6]) || vals[6].length() == 0) {
                        gad.setIncome(BigDecimal.ZERO);
                    } else {
                        gad.setIncome(new BigDecimal(vals[6].trim()));
                    }

                    if (vals[7] == null || "".equals(vals[7]) || vals[7].length() == 0) {
                        gad.setDistribution(BigDecimal.ZERO);
                    } else {
                        gad.setDistribution(new BigDecimal(vals[7].trim()));
                    }

                    list.add(gad);
                }
                wxService.insertGameAdInfo(list);

                obj.put("ret", 1);
                obj.put("msg", "上传文件成功");
            } else {
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls文件!");
            }

        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            obj.put("ret", 0);
            obj.put("msg", "导入失败 第" + error + "行有误，请查看");
        }
        return obj.toJSONString();
    }

    /**
     * 小游戏-合作方广告数据查询
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/selectGameAdInfo", method = RequestMethod.POST)
    public String selectselectGameAdInfo(HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("channel", request.getParameter("channel"));
        map.put("cGame", request.getParameter("cGame"));
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));

        if (!"root".equals(CommonUtil.userMap.get(cuser.getLogin_name())!=null?CommonUtil.userMap.get(cuser.getLogin_name()).getOrg_id():"")) {
            map.put("appid", CommonUtil.userMap.get(cuser.getLogin_name())!=null?CommonUtil.userMap.get(cuser.getLogin_name()).getApp_group():"");
        }

        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<GameAdInfo> list = wxService.selectGameAdInfo(map);
            long size = ((Page) list).getTotal();

            // 导出的excel数据存储
//            redisTemplate.opsForValue().set("wxGameAdInfoList-" + token,
//                    list, 20 * 60, TimeUnit.SECONDS);

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
            result.put("sum", wxService.selectSumGameAdInfo(map));
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 小游戏-合作方广告数据 操作
     *
     * @param request
     * @param gameAdInfo
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/gameAdInfoHandle", method = RequestMethod.POST)
    public String gameAdInfoHandle(HttpServletRequest request, GameAdInfo gameAdInfo, HttpServletResponse response) {

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        int result = 0;
        try {
            if ("edit".equals(request.getParameter("handle"))) {
                result = wxService.updateGameAdInfo(gameAdInfo);
            } else if ("del".equals(request.getParameter("handle"))) {
                result = wxService.deleteGameAdInfo(gameAdInfo);
            }

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    /**
     * 小游戏-合作方广告数据导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/exportGameAdInfoList", method = RequestMethod.GET)
    public String exportGameAdInfoList(HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("channel", request.getParameter("channel"));
        map.put("cGame", request.getParameter("cGame"));
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));

        if (!"root".equals(CommonUtil.userMap.get(cuser.getLogin_name()).getOrg_id())) {
            map.put("appid", CommonUtil.userMap.get(cuser.getLogin_name()).getApp_group());
        }

        List<GameAdInfo> list = wxService.selectGameAdInfo(map);

        // 数据内容
//      List<GameAdInfo> list = (List<GameAdInfo>) redisTemplate.opsForValue().get("wxGameAdInfoList-" + token);

        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";;
		Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;

        for (GameAdInfo temp : list) {
        	Integer appid = temp.getcGame();
            headerMap.put("cDate", "日期");
            headerMap.put("cGame", "游戏");
            headerMap.put("amount", "投放金额");
            headerMap.put("addNum", "新增用户");
            headerMap.put("cpa", "cpa");
            headerMap.put("dau", "dau");
            headerMap.put("income", "当日广告收入");
            headerMap.put("dauARpu1", "广告dauarpu");

            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("cDate", temp.getcDate());
            contentMap.put("cGame", appMap.get(appid.toString()).get("app_name"));
            contentMap.put("amount", temp.getAmount());
            contentMap.put("addNum", temp.getAddNum());
            contentMap.put("dau", temp.getDau());
            contentMap.put("income", temp.getIncome());
            contentMap.put("dauARpu1", temp.getDauARpu1());
            contentMap.put("cpa", temp.getCpa());


            contentList.add(contentMap);
        }


        String fileName = "游戏广告数据_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);

        return null;
    }

    /**
     * 微信红包大额活动配置 查询
     *
     * @param redpackdrawConfigInfo
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/selectRedpackdrawConfig", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectWxRedpackdrawConfig(RedpackdrawConfigInfo redpackdrawConfigInfo,
                                            HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        String pid = BlankUtils.checkNull(request, "pid");

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("pid", redpackdrawConfigInfo.getPid());
        map.put("type", redpackdrawConfigInfo.getType());

        if (!"root".equals(CommonUtil.userMap.get(cuser.getLogin_name()).getOrg_id())) {
            map.put("appid", CommonUtil.userMap.get(cuser.getLogin_name()).getApp_group());
        }


        JSONObject result = new JSONObject();
        try {
            String sql = "select * from home_redpack_qq_cfg where 1=1";
            if (!BlankUtils.checkBlank(pid))
                sql += " and pid = '" + pid + "'";


            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<Map<String, Object>> list = admsgService.queryListMap(sql);
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    @CrossOrigin
    @RequestMapping(value = "/wx/redpackdrawConfigHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String redpackdrawConfigHandle(RedpackdrawConfigInfo wc, HttpServletRequest request) {
        String token = request.getParameter("token");
        String handle = request.getParameter("handle");

        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();
        try {
            int resp = admsgService.qqRedpackdrawConfigHandle(handle, wc);

            if (resp > 0) {
                result.put("ret", 1);
                result.put("msg", "success");
            } else {
                result.put("ret", 0);
                result.put("msg", "无效操作");
            }
            return result.toJSONString();

        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }

    /**
     * 微信-红包产品参数配置 查询
     *
     * @param value
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/selectRedpackConfigInfo")
    public String selectWxRedpackConfigInfo(RedpackConfigInfo redpackConfigInfo,
                                            HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("appid", redpackConfigInfo.getAppid());
        map.put("appName", redpackConfigInfo.getAppName());


        if (!"root".equals(CommonUtil.userMap.get(cuser.getLogin_name())!=null?CommonUtil.userMap.get(cuser.getLogin_name()).getOrg_id():"")) {
            map.put("appids", CommonUtil.userMap.get(cuser.getLogin_name())!=null?CommonUtil.userMap.get(cuser.getLogin_name()).getApp_group():"");
        }

        JSONObject result = new JSONObject();
        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<RedpackConfigInfo> list = ydService.selectRedpackConfig(map);
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 小游戏-红包产品提现明细查询
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/selectRedPackAuditDetail", method = RequestMethod.POST)
    public String selectQQRedPackAuditDetail(HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("cPid", request.getParameter("cPid"));
        map.put("cAppid", request.getParameter("cAppid"));
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("hide", request.getParameter("hide"));

        JSONObject result = new JSONObject();
        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<RedPackConfig> list = ydService.selectQQRedPackAuditDetail(map);
            long size = ((Page) list).getTotal();

            // 导出的excel数据存储
            redisTemplate.opsForValue().set("qqRedPackAuditDetail-" + token,
                    list, 20 * 60, TimeUnit.SECONDS);

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 小游戏-红包产品提现明细导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/exportRedPackAuditDetail", method = RequestMethod.GET)
    public String exportQQRedPackAuditDetail(HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // 数据内容
        List<RedPackConfig> list = (List<RedPackConfig>) redisTemplate.opsForValue().get("qqRedPackAuditDetail-" + token);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;
        if (list.size() > 0 && null != list.get(0).getcPrice()) {
            for (RedPackConfig temp : list) {
                headerMap.put("cDate", "日期");
                headerMap.put("cAppid", "产品名称");
                headerMap.put("cPid", "项目id");
                headerMap.put("cPrice", "提现单价");
                headerMap.put("cUsers", "提现人数");
                headerMap.put("cTotal", "提现总额");

                contentMap = new LinkedHashMap<String, Object>();
                contentMap.put("cDate", temp.getcDate());
                contentMap.put("cAppid", temp.getcAppid());
                contentMap.put("cPid", temp.getcPid());
                contentMap.put("cPrice", temp.getcPrice());
                contentMap.put("cUsers", temp.getcUsers());
                contentMap.put("cTotal", temp.getcTotal());

                contentList.add(contentMap);
            }
        } else if (list.size() > 0 && null == list.get(0).getcPrice()) {
            for (RedPackConfig temp : list) {
                headerMap.put("cDate", "日期");
                headerMap.put("cAppid", "产品名称");
                headerMap.put("cUsers", "提现人数");
                headerMap.put("cTotal", "提现总额");

                contentMap = new LinkedHashMap<String, Object>();
                contentMap.put("cDate", temp.getcDate());
                contentMap.put("cAppid", temp.getcAppid());
                contentMap.put("cUsers", temp.getcUsers());
                contentMap.put("cTotal", temp.getcTotal());

                contentList.add(contentMap);
            }
        }
        String fileName = "小游戏红包产品提现明细_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);

        return null;
    }

    /**
     * 小游戏-数据查询-单机查询 实时查询红包
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/selectNowRedPackConfig", method = RequestMethod.POST)
    public String selectQQNowRedPack(HttpServletRequest request,
                                     HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;


        Map<String, Object> map = new HashMap<String, Object>();
        map.put("hide", request.getParameter("hide"));
        map.put("cPid", request.getParameter("cPid"));
        map.put("gameName", request.getParameter("gameName"));
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        String[] str = request.getParameter("startTime").split("-");
        String databasestr = "red_pack_qq_audit_info_" + str[0] + str[1];
        map.put("databasestr", databasestr);

        JSONObject result = new JSONObject();
        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<RedPackConfig> list = ydService.selectWxNowRedPack(map);
            long size = ((Page) list).getTotal();

            // 导出的excel数据存储
            redisTemplate.opsForValue().set("qqNowRedPackExcelList-" + token,
                    list, 20 * 60, TimeUnit.SECONDS);

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    @RequestMapping(value = "/wx/exportNowRedPackConfig", method = {
            RequestMethod.GET, RequestMethod.POST})
    public String exportQQNowRedPackConfig(ModelMap map,
                                           HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");

        // 数据内容
        List<RedPackConfig> list = (List<RedPackConfig>) redisTemplate.opsForValue().get("qqNowRedPackExcelList-" + token);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;
        if (null != list.get(0).getcPid()) {
            for (RedPackConfig temp : list) {
                headerMap.put("cDate", "日期");
                headerMap.put("gameName", "产品名称");//
                headerMap.put("cPid", "渠道标识");
                headerMap.put("cDau", "活跃用户数");
                headerMap.put("cOverUser", "达成用户数");
                headerMap.put("cUsers", "提现人数");
                headerMap.put("withdrawRate", "达成提现率");
                headerMap.put("cWithdraw", "提现请求人数");
                headerMap.put("withdrawSuccessRate", "提现成功率");
                headerMap.put("cTotal", "提现总额");
                headerMap.put("cShow", "视屏展示数");
                headerMap.put("cHold", "19元人数");

                contentMap = new LinkedHashMap<String, Object>();

                contentMap.put("cDate", temp.getcDate());
                contentMap.put("gameName", temp.getGameName());
                contentMap.put("cPid", temp.getcPid());
                contentMap.put("cUsers", temp.getcUsers());
                contentMap.put("cTotal", temp.getcTotal());
                contentMap.put("cOverUser", temp.getcOverUser());
                contentMap.put("cDau", temp.getcDau());
                contentMap.put("withdrawRate", temp.getWithdrawRate());
                contentMap.put("cWithdraw", temp.getcWithdraw());
                contentMap.put("withdrawSuccessRate", temp.getWithdrawSuccessRate());
                contentMap.put("cShow", temp.getcShow());
                contentMap.put("cHold", temp.getcHold());

                contentList.add(contentMap);
            }
        } else {
            for (RedPackConfig temp : list) {
                headerMap.put("cDate", "日期");
                headerMap.put("gameName", "产品名称");//
                headerMap.put("cDau", "活跃用户数");
                headerMap.put("cOverUser", "达成用户数");
                headerMap.put("cUsers", "提现人数");
                headerMap.put("cWithdraw", "提现请求人数");
                headerMap.put("cTotal", "提现总额");
                headerMap.put("cShow", "视屏展示数");
                headerMap.put("cHold", "19元人数");


                contentMap = new LinkedHashMap<String, Object>();

                contentMap.put("cDate", temp.getcDate());
                contentMap.put("gameName", temp.getGameName());
                contentMap.put("cUsers", temp.getcUsers());
                contentMap.put("cTotal", temp.getcTotal());
                contentMap.put("cOverUser", temp.getcOverUser());
                contentMap.put("cDau", temp.getcDau());
                contentMap.put("cWithdraw", temp.getcWithdraw());
                contentMap.put("cShow", temp.getcShow());
                contentMap.put("cHold", temp.getcHold());

                contentList.add(contentMap);
            }

        }
        String fileName = "小游戏红包产品实时数据_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);
        return null;
    }

    /**
     * 小游戏广告数据查询
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/selectGameAdList", method = RequestMethod.POST)
    public String selectGameAdList(HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("channel", request.getParameter("channel"));
        map.put("cGame", request.getParameter("cGame"));
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("hide", request.getParameter("hide"));

        if (!"root".equals(CommonUtil.userMap.get(cuser.getLogin_name()).getOrg_id())) {
            map.put("appid", CommonUtil.userMap.get(cuser.getLogin_name()).getApp_group());
        }

        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<GameAdInfo> list = wxService.selectGameAdInfoList(map);
            long size = ((Page) list).getTotal();

            // 导出的excel数据存储
            redisTemplate.opsForValue().set("selectGameAdList-" + token,
                    list, 20 * 60, TimeUnit.SECONDS);

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 小游戏广告数据导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/exportGameAdList", method = RequestMethod.GET)
    public String exportGameAdList(HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // 数据内容
        List<GameAdInfo> list = (List<GameAdInfo>) redisTemplate.opsForValue().get("selectGameAdList-" + token);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;
        if (null != list.get(0).getChannel()) {
            for (GameAdInfo temp : list) {
                headerMap.put("cDate", "日期");
                headerMap.put("cGame", "游戏");
                headerMap.put("channel", "推广渠道");
                headerMap.put("amount", "投放金额");
                headerMap.put("addNum", "新增用户");
                headerMap.put("cpa", "cpa");
                headerMap.put("dau", "dau");
                headerMap.put("income", "当日广告收入");
                headerMap.put("dauARpu1", "广告dauarpu");
                headerMap.put("distribution", "分发收入");
                headerMap.put("dauARpu2", "分发dauarpu");
                headerMap.put("dauARpu3", "总dauarpu");

                contentMap = new LinkedHashMap<String, Object>();
                contentMap.put("cDate", temp.getcDate());
                contentMap.put("cGame", temp.getcGame());
                contentMap.put("channel", temp.getChannel());
                contentMap.put("amount", temp.getAmount());
                contentMap.put("addNum", temp.getAddNum());
                contentMap.put("dau", temp.getDau());
                contentMap.put("income", temp.getIncome());
                contentMap.put("dauARpu1", temp.getDauARpu1());
                contentMap.put("cpa", temp.getCpa());
                contentMap.put("distribution", temp.getDistribution());
                contentMap.put("dauARpu2", temp.getDauARpu2());
                contentMap.put("dauARpu3", temp.getDauARpu3());

                contentList.add(contentMap);
            }
        } else {
            for (GameAdInfo temp : list) {
                headerMap.put("cDate", "日期");
                headerMap.put("cGame", "游戏");
                headerMap.put("amount", "投放金额");
                headerMap.put("addNum", "新增用户");
                headerMap.put("cpa", "cpa");
                headerMap.put("dau", "dau");
                headerMap.put("income", "当日广告收入");
                headerMap.put("dauARpu1", "广告dauarpu");
                headerMap.put("distribution", "分发收入");
                headerMap.put("dauARpu2", "分发dauarpu");
                headerMap.put("dauARpu3", "总dauarpu");

                contentMap = new LinkedHashMap<String, Object>();
                contentMap.put("cDate", temp.getcDate());
                contentMap.put("cGame", temp.getcGame());
                contentMap.put("amount", temp.getAmount());
                contentMap.put("addNum", temp.getAddNum());
                contentMap.put("dau", temp.getDau());
                contentMap.put("income", temp.getIncome());
                contentMap.put("dauARpu1", temp.getDauARpu1());
                contentMap.put("cpa", temp.getCpa());
                contentMap.put("distribution", temp.getDistribution());
                contentMap.put("dauARpu2", temp.getDauARpu2());
                contentMap.put("dauARpu3", temp.getDauARpu3());

                contentList.add(contentMap);

            }
        }

        String fileName = "小游戏广告数据_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);

        return null;
    }

    /**
     * 小游戏红包道具监控
     *
     * @param request
     * @param response
     */
    @CrossOrigin
    @RequestMapping(value = "/wx/selectRedPackRangeTotal", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectRedPackRangeTotal(GDTWorkReportVo gdt, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        String tdate = request.getParameter("tdate");
        String appid = request.getParameter("appid");
        String pid = request.getParameter("pid");
        String group_pid = request.getParameter("group_pid");

//		// token验证
//		String token = request.getParameter("token");
//		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//			return "{\"ret\":2,\"msg\":\"token is error!\"}";
//		else
//			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {
            // 筛除sql敏感字符注入
            if (BlankUtils.sqlValidate(tdate)
                    || BlankUtils.sqlValidate(pid)
                    || BlankUtils.sqlValidate(appid)) {
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }
            if (BlankUtils.checkBlank(tdate)) {
                tdate = DateTime.now().toString("yyyy-MM-dd");
            }

            String sql = "select concat(tdate,'') tdate,appid,pid,tooltype,"
                    + "SUM(range1) range1,SUM(range2) range2,SUM(range3) range3,SUM(range4) range4,"
                    + "SUM(range5) range5,SUM(range6) range6,SUM(range7) range7,SUM(range8) range8"
                    + " from xyx_redBalance_range"
                    + " where tdate BETWEEN '" + tdate + "' AND '" + tdate + "' ";
            if (!BlankUtils.checkBlank(pid))
                sql += " and pid = " + pid;
            if (!BlankUtils.checkBlank(appid))
                sql += " and appid = " + appid;

            if (!BlankUtils.checkBlank(group_pid))
                sql += " group by pid,tooltype";
            else
                sql += " group by appid,tooltype";

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<Map<String, Object>> list = admsgService.queryListMap(sql);
            long size = new PageInfo(list).getTotal();

            Map<String, Map<String, Object>> pidMap = adService.selectAppinfoMap();
            List<Map<String, Object>> collect = list.stream().map(map -> {
                pidMap.values().forEach(pMap -> {
                    if ((map.get("appid")).equals(pMap.get("appid"))) {
//						map.put("ver", pMap.get("ver")+"");
                        map.put("appname", pMap.get("gname") + "");
                    }
                });

                String ty = map.get("tooltype") + "";
                map.put("tooltype", "1".equals(ty) ? "现金余额" : "2".equals(ty) ? "iphone碎片" : "华为碎片");

                if (BlankUtils.checkBlank(group_pid)) {
                    map.remove("ver");
                    map.remove("pid");
                }
                return map;
            }).collect(Collectors.toList());

            result.put("ret", 1);
            result.put("data", collect);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    /**
     * 小游戏红包道具监控导出
     *
     * @param request
     * @param response
     */
    @CrossOrigin
    @RequestMapping(value = "/wx/exportRedPackRangeTotal", method = {RequestMethod.GET, RequestMethod.POST})
    public void exportRedPackRangeTotal(HttpServletRequest request, HttpServletResponse response) {
        String tdate = request.getParameter("tdate");
        String appid = request.getParameter("appid");
        String pid = request.getParameter("pid");
        String group_pid = request.getParameter("group_pid");

        List<Map<String, Object>> contentList = null;
        if (BlankUtils.sqlValidate(tdate)
                || BlankUtils.sqlValidate(pid)
                || BlankUtils.sqlValidate(appid)) {
            return;
        }

        if (BlankUtils.checkBlank(tdate)) {
            tdate = DateTime.now().toString("yyyy-MM-dd");
        }
        String sql = "select concat(tdate,'') tdate,appid,pid,tooltype,"
                + "SUM(range1) range1,SUM(range2) range2,SUM(range3) range3,SUM(range4) range4,"
                + "SUM(range5) range5,SUM(range6) range6,SUM(range7) range7,SUM(range8) range8"
                + " from xyx_redBalance_range"
                + " where tdate BETWEEN '" + tdate + "' AND '" + tdate + "' ";
        if (!BlankUtils.checkBlank(pid))
            sql += " and pid = " + pid;
        if (!BlankUtils.checkBlank(appid))
            sql += " and appid = " + appid;

        if (!BlankUtils.checkBlank(group_pid))
            sql += " group by pid,tooltype";
        else
            sql += " group by appid,tooltype";

        List<Map<String, Object>> list = admsgService.queryListMap(sql);
        Map<String, Map<String, Object>> pidMap = adService.selectAppinfoMap();
        contentList = list.stream().map(map -> {
            pidMap.values().forEach(pMap -> {
                if ((map.get("appid")).equals(pMap.get("appid"))) {
//					map.put("ver", pMap.get("ver")+"");
                    map.put("appname", pMap.get("gname") + "");
                }
            });

            String ty = map.get("tooltype") + "";
            map.put("tooltype", "1".equals(ty) ? "现金余额" : "2".equals(ty) ? "iphone碎片" : "华为碎片");

            if (BlankUtils.checkBlank(group_pid)) {
                map.remove("ver");
                map.remove("pid");
            }
            return map;
        }).collect(Collectors.toList());

        //数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        headerMap.put("appname", "产品名称");
        if (!BlankUtils.checkBlank(group_pid)) {
            headerMap.put("pid", "项目id");
            headerMap.put("ver", "版本号");
        }
        headerMap.put("tooltype", "道具类型");
        headerMap.put("range1", "0-20%");
        headerMap.put("range2", "20-40%");
        headerMap.put("range3", "40-60%");
        headerMap.put("range4", "60-80%");
        headerMap.put("range5", "80-90%");
        headerMap.put("range6", "90-95%");
        headerMap.put("range7", "95-100%");
        headerMap.put("range8", "100%及以上");

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "小游戏红包道具监控_" + DateTime.now().toString("yyyyMMddHHmmss") + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
    }

    /**
     * 合作方关联项目数据的配置查询
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/yd/selectAloneProjectInfo", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectAloneProjectInfo(HttpServletRequest request, HttpServletResponse response, AloneProjectInfo aloneProjectInfo) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();

        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<AloneProjectInfo> list = ydService.selectAloneProjectInfo(aloneProjectInfo);
            long size = ((Page) list).getTotal();


            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 修改渠道标识
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/yd/AloneProjectInfoHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public String AloneProjectInfoHandle(HttpServletRequest request, HttpServletResponse response, AloneProjectInfo aloneProjectInfo) {
        String token = request.getParameter("token");
        String handle = request.getParameter("handle");

        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();
        try {
            int resp = ydService.updateAloneProjectInfo(aloneProjectInfo);

            if (resp > 0) {
                result.put("ret", 1);
                result.put("msg", "success");
            } else {
                result.put("ret", 0);
                result.put("msg", "无效操作");
            }
            return result.toJSONString();

        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }

    /**
     * 合作方关联项目数据的配置查询
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/yd/insertAloneProjectInfo", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String insertAloneProjectInfo(AloneProjectInfo aloneProjectInfo, HttpServletRequest request) {
        String token = request.getParameter("token");

        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();
        try {

            int resp = ydService.insertAloneProjectInfo(aloneProjectInfo);

            if (resp > 0) {
                result.put("ret", 1);
                result.put("msg", "success");
            } else {
                result.put("ret", 0);
                result.put("msg", "操作失败");
            }
            return result.toJSONString();

        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "可能存在相同的项目id,请确认!");
            return result.toJSONString();
        }
    }


    @RequestMapping(value = "/yd/selectWbSysUserloginNameList", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String selectWbSysUserloginNameList(HttpServletRequest request) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();

        try {

            result.put("data", wbSysMapper.selectWbSysUserloginNameList());
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }


    @CrossOrigin
    @RequestMapping(value = "/wx/importAppExchangeVolume", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String importAppExchangeVolumeList(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {

        JSONObject obj = new JSONObject();
        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<AppExchangeVolume> list = new ArrayList<AppExchangeVolume>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;

                    String[] vals = new String[13];
                    for (int c = 0; c < 13; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }

                    AppExchangeVolume gad = new AppExchangeVolume();
                    gad.setTdate(vals[0]);
                    if (vals[0] != null && !vals[0].isEmpty()) {
                        // 上传过来的日期格式
                        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
                        boolean flag = false;
                        try {
                            if (vals[0].split("-").length != 3
                                    || vals[0].split("-")[0].length() != 4) {
                                flag = true;
                            }

                            String adv_dt = DateTime.parse(vals[0], format).toString("yyyy-MM-dd");
                            gad.setTdate(adv_dt);
                        } catch (Exception e) {
                            flag = true;
                        }

                        if (flag) {
                            obj.put("ret", 0);
                            obj.put("msg", "日期格式不正确，需要 yyyy-MM-dd 的格式！");
                            return obj.toString();
                        }
                    }
                    gad.setChannelName(vals[1]);
                    gad.setGameName(vals[2]);
                    gad.setAppid(vals[3]);
                    gad.setOrgChannel(vals[4]);
                    gad.setOrgGameName(vals[5]);
                    gad.setOrgAppid(vals[6]);
                    if (vals[7] == null || "".equals(vals[7]) || vals[7].length() == 0) {
                        gad.setPrice(BigDecimal.ZERO);
                    } else {
                        gad.setPrice(new BigDecimal(vals[7].trim()));
                    }
                    if (vals[8] == null || "".equals(vals[8]) || vals[8].length() == 0) {
                        gad.setChannel(0);
                    } else {
                        gad.setChannel(Integer.valueOf(vals[8].trim()));
                    }

                    if (vals[9] == null || "".equals(vals[9]) || vals[9].length() == 0) {
                        gad.setDerivedRevenue(BigDecimal.ZERO);
                    } else {
                        gad.setDerivedRevenue(new BigDecimal(vals[9].trim()));
                    }
                    gad.setPricingType(vals[10]);
                    gad.setPlatform(vals[11]);
                    gad.setPlatformUrl(vals[12]);

                    list.add(gad);
                }
                wxService.insertAppExchangeVolume(list);

                obj.put("ret", 1);
                obj.put("msg", "上传文件成功");
            } else {
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls文件!");
            }

        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            obj.put("ret", 0);
            obj.put("msg", "导入失败");
        }
        return obj.toJSONString();
    }


    @RequestMapping(value = "/wx/selectAppExchangeVolume", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String selectAppExchangeVolume(HttpServletRequest request) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("channelName", request.getParameter("channelName"));
        map.put("gameName", request.getParameter("gameName"));
        map.put("appid", request.getParameter("appid"));
        map.put("orgChannel", request.getParameter("orgChannel"));
        map.put("orgGameName", request.getParameter("orgGameName"));
        map.put("pricingType", request.getParameter("pricingType"));

        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<AppExchangeVolume> list = wxService.selectAppExchangeVolume(map);
            long size = ((Page) list).getTotal();

            // 导出的excel数据存储
            redisTemplate.opsForValue().set("selectAppExchangeVolume-" + token,
                    wxService.selectAppExchangeVolume(map), 20 * 60, TimeUnit.SECONDS);

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
            result.put("Summary", ydMapper.selectAppExchangeSell(map));
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 卖量收入报表
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/exportAppExchangeVolume", method = RequestMethod.GET)
    public String exportAppExchangeVolume(HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // 数据内容
        List<AppExchangeVolume> list = (List<AppExchangeVolume>) redisTemplate.opsForValue().get("selectAppExchangeVolume-" + token);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;

        for (AppExchangeVolume temp : list) {
            headerMap.put("tdate", "日期");
            headerMap.put("channelName", "渠道");
            headerMap.put("gameName", "我方导出产品");
            headerMap.put("appid", "产品appid");
            headerMap.put("orgChannel", "合作渠道");
            headerMap.put("orgGameName", "渠道买量产品");
            headerMap.put("orgAppid", "买量产品appid");
            headerMap.put("price", "单价");
            headerMap.put("channel", "渠道新增");
            headerMap.put("derivedRevenue", "导出收入");
            headerMap.put("pricingType", "计价方式");
            headerMap.put("platform", "后台");
            headerMap.put("platformUrl", "后台地址");


            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("tdate", temp.getTdate());
            contentMap.put("channelName", temp.getChannelName());
            contentMap.put("gameName", temp.getGameName());
            contentMap.put("appid", temp.getAppid());
            contentMap.put("orgChannel", temp.getOrgChannel());
            contentMap.put("orgGameName", temp.getOrgGameName());
            contentMap.put("orgAppid", temp.getOrgAppid());
            contentMap.put("price", temp.getPrice());
            contentMap.put("channel", temp.getChannel());
            contentMap.put("derivedRevenue", temp.getDerivedRevenue());
            contentMap.put("pricingType", temp.getPricingType());
            contentMap.put("platform", temp.getPlatform());
            contentMap.put("platformUrl", temp.getPlatformUrl());

            contentList.add(contentMap);
        }


        String fileName = "卖量收入报表_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);

        return null;
    }


    @CrossOrigin
    @RequestMapping(value = "/wx/appExchangeVolumeHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public String appExchangeVolumeHandle(AppExchangeVolume appExchangeVolume, HttpServletRequest request) {
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        int result = 0;
        try {

            if ("edit".equals(request.getParameter("handle"))) {
                result = wxService.updateAppExchangeVolume(appExchangeVolume);
            }

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    @CrossOrigin
    @RequestMapping(value = "/wx/importAppChannelAppidInfo", method = {RequestMethod.GET, RequestMethod.POST})
    public String importAppChannelAppidInfo(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {

        JSONObject obj = new JSONObject();
        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<AppChannelAppidInfo> list = new ArrayList<AppChannelAppidInfo>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();

                Map<String, String> map = new HashMap<String, String>();

                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;

                    String[] vals = new String[19];
                    for (int c = 0; c < 19; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }

                    AppChannelAppidInfo gad = new AppChannelAppidInfo();
                    gad.setTdate(vals[0]);
                    if (vals[0] != null && !vals[0].isEmpty()) {
                        // 上传过来的日期格式
                        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
                        boolean flag = false;
                        try {
                            if (vals[0].split("-").length != 3
                                    || vals[0].split("-")[0].length() != 4) {
                                flag = true;
                            }

                            String adv_dt = DateTime.parse(vals[0], format).toString("yyyy-MM-dd");
                            gad.setTdate(adv_dt);
                        } catch (Exception e) {
                            flag = true;
                        }

                        if (flag) {
                            obj.put("ret", 0);
                            obj.put("msg", "日期格式不正确，需要 yyyy-MM-dd 的格式！");
                            return obj.toString();
                        }
                    }
                    gad.setChannel(vals[1]);
                    gad.setAppid(vals[2]);
                    gad.setAppidKey(vals[3]);

                    if (vals[4] == null || "".equals(vals[4]) || vals[4].length() == 0) {
                        gad.setAddNum(0);
                    } else {
                        gad.setAddNum(Integer.valueOf(vals[4].trim()));
                    }
                    if (vals[5] == null || "".equals(vals[5]) || vals[5].length() == 0) {
                        gad.setActNum(0);
                    } else {
                        gad.setActNum(Integer.valueOf(vals[5].trim()));
                    }

                    if (vals[6] == null || "".equals(vals[6]) || vals[6].length() == 0) {
                        gad.setIncome(BigDecimal.ZERO);
                    } else {
                        gad.setIncome(new BigDecimal(vals[6].trim()));
                    }
                    map.put(gad.getAppidKey(), gad.getAppid());

                    gad.setTimes(vals[7]);

					/*if(vals[8] != null && !("").equals(vals[8]) && vals[8].contains(".")){
						vals[8] = vals[8].replace("%","00").substring(0,vals[8].indexOf(".")+3)+"%";
					}else{
						vals[8] = "0.0%";
					}*/
                    gad.setTwoRate(vals[8]);

                    if (vals[9] == null || "".equals(vals[9]) || vals[9].length() == 0) {
                        gad.setBanIncome(BigDecimal.ZERO);
                    } else {
                        gad.setBanIncome(new BigDecimal(vals[9].trim()));
                    }

                    if (vals[10] == null || "".equals(vals[10]) || vals[10].length() == 0) {
                        gad.setScreenIncome(BigDecimal.ZERO);
                    } else {
                        gad.setScreenIncome(new BigDecimal(vals[10].trim()));
                    }
                    //Open

                    if (vals[11] == null || "".equals(vals[11]) || vals[11].length() == 0) {
                        gad.setOpenIncome(BigDecimal.ZERO);
                    } else {
                        gad.setOpenIncome(new BigDecimal(vals[11].trim()));
                    }

                    if (vals[12] == null || "".equals(vals[12]) || vals[12].length() == 0) {
                        gad.setTrafficIncome(BigDecimal.ZERO);
                    } else {
                        gad.setTrafficIncome(new BigDecimal(vals[12].trim()));
                    }

                    if (vals[13] == null || "".equals(vals[13]) || vals[13].length() == 0) {
                        gad.setVideoIncome(BigDecimal.ZERO);
                    } else {
                        gad.setVideoIncome(new BigDecimal(vals[13].trim()));
                    }

                    if (vals[14] == null || "".equals(vals[14]) || vals[14].length() == 0) {
                        gad.setBanPv(BigDecimal.ZERO);
                    } else {
                        gad.setBanPv(new BigDecimal(vals[14].trim()));
                    }

                    if (vals[15] == null || "".equals(vals[15]) || vals[15].length() == 0) {
                        gad.setScreenPv(BigDecimal.ZERO);
                    } else {
                        gad.setScreenPv(new BigDecimal(vals[15].trim()));
                    }

                    if (vals[16] == null || "".equals(vals[16]) || vals[16].length() == 0) {
                        gad.setOpenPv(BigDecimal.ZERO);
                    } else {
                        gad.setOpenPv(new BigDecimal(vals[16].trim()));
                    }

                    if (vals[17] == null || "".equals(vals[17]) || vals[17].length() == 0) {
                        gad.setTrafficPv(BigDecimal.ZERO);
                    } else {
                        gad.setTrafficPv(new BigDecimal(vals[17].trim()));
                    }

                    if (vals[18] == null || "".equals(vals[18]) || vals[18].length() == 0) {
                        gad.setVideoPv(BigDecimal.ZERO);
                    } else {
                        gad.setVideoPv(new BigDecimal(vals[18].trim()));
                    }

                    list.add(gad);
                }
                wxService.insertAppChannelAppidInfo(list);

                ydMapper.editAppExchangeName(map);

                obj.put("ret", 1);
                obj.put("msg", "上传文件成功");
            } else {
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls文件!");
            }

        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            obj.put("ret", 0);
            obj.put("msg", "导入失败");
        }
        return obj.toJSONString();
    }

    @CrossOrigin
    @RequestMapping(value = "/wx/importAppExchangeVolumeBuy", method = {RequestMethod.GET, RequestMethod.POST})
    public String importAppExchangeVolumeBuyList(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {

        JSONObject obj = new JSONObject();
        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<AppExchangeVolume> list = new ArrayList<AppExchangeVolume>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;

                    String[] vals = new String[17];
                    for (int c = 0; c < 17; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }

                    AppExchangeVolume gad = new AppExchangeVolume();
                    gad.setTdate(vals[0]);
                    if (vals[0] != null && !vals[0].isEmpty()) {
                        // 上传过来的日期格式
                        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
                        boolean flag = false;
                        try {
                            if (vals[0].split("-").length != 3
                                    || vals[0].split("-")[0].length() != 4) {
                                flag = true;
                            }

                            String adv_dt = DateTime.parse(vals[0], format).toString("yyyy-MM-dd");
                            gad.setTdate(adv_dt);
                        } catch (Exception e) {
                            flag = true;
                        }

                        if (flag) {
                            obj.put("ret", 0);
                            obj.put("msg", "日期格式不正确，需要 yyyy-MM-dd 的格式！");
                            return obj.toString();
                        }
                    }
                    gad.setChannelName(vals[1]);
                    gad.setGameName(vals[2]);
                    gad.setAppid(vals[3]);
                    gad.setOrgChannel(vals[4]);
                    gad.setOrgGameName(vals[5]);
                    gad.setOrgAppid(vals[6]);
                    if (vals[7] == null || "".equals(vals[7]) || vals[7].length() == 0) {
                        gad.setPrice(BigDecimal.ZERO);
                    } else {
                        gad.setPrice(new BigDecimal(vals[7].trim()));
                    }
                    if (vals[8] == null || "".equals(vals[8]) || vals[8].length() == 0) {
                        gad.setChannel(0);
                    } else {
                        gad.setChannel(Integer.valueOf(vals[8].trim()));
                    }

                    if (vals[9] == null || "".equals(vals[9]) || vals[9].length() == 0) {
                        gad.setDerivedRevenue(BigDecimal.ZERO);
                    } else {
                        gad.setDerivedRevenue(new BigDecimal(vals[9].trim()));
                    }
                    gad.setPricingType(vals[10]);
                    gad.setPlatform(vals[11]);
                    gad.setPlatformUrl(vals[12]);
                    gad.setTimes(vals[13]);
                    gad.setTwoRate(vals[14]);
                    if (vals[15] == null || "".equals(vals[15]) || vals[15].length() == 0) {
                        gad.setVolumes("0");
                    } else {
                        gad.setVolumes(vals[15]);
                    }
                    if (vals[16] == null || "".equals(vals[16]) || vals[16].length() == 0) {
                        gad.setCosts("0");
                    } else {
                        gad.setCosts(vals[16]);
                    }

                    list.add(gad);
                }
                wxService.insertAppExchangeVolumeBuy(list);

                obj.put("ret", 1);
                obj.put("msg", "上传文件成功");
            } else {
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls文件!");
            }

        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            obj.put("ret", 0);
            obj.put("msg", "导入失败");
        }
        return obj.toJSONString();
    }


    @RequestMapping(value = "/wx/selectAppExchangeVolumeBuy", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectAppExchangeVolumeBuy(HttpServletRequest request) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("channelName", request.getParameter("channelName"));
        map.put("gameName", request.getParameter("gameName"));
        map.put("appid", request.getParameter("appid"));
        map.put("orgChannel", request.getParameter("orgChannel"));
        map.put("orgGameName", request.getParameter("orgGameName"));
        map.put("pricingType", request.getParameter("pricingType"));

        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<AppExchangeVolume> list = wxService.selectAppExchangeVolumeBuy(map);
            long size = ((Page) list).getTotal();

            // 导出的excel数据存储
            redisTemplate.opsForValue().set("selectAppExchangeVolumeBuy-" + token,
                    wxService.selectAppExchangeVolumeBuy(map), 20 * 60, TimeUnit.SECONDS);

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
            result.put("Summary", ydMapper.selectAppExchangeBuy(map));
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 卖量收入报表
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/exportAppExchangeVolumeBuy", method = RequestMethod.GET)
    public String exportAppExchangeVolumeBuy(HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // 数据内容
        List<AppExchangeVolume> list = (List<AppExchangeVolume>) redisTemplate.opsForValue().get("selectAppExchangeVolumeBuy-" + token);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;

        for (AppExchangeVolume temp : list) {
            headerMap.put("tdate", "日期");
            headerMap.put("channelName", "渠道");
            headerMap.put("gameName", "我方导入产品");
            headerMap.put("appid", "产品appid");
            headerMap.put("orgChannel", "合作渠道");
            headerMap.put("orgGameName", "渠道卖量产品");
            headerMap.put("orgAppid", "卖量产品appid");
            headerMap.put("price", "单价");
            headerMap.put("channel", "渠道新增");
            headerMap.put("derivedRevenue", "导入支出");
            headerMap.put("volumes", "投放买量");
            headerMap.put("costs", "投放支出");
            headerMap.put("pricingType", "计价方式");
            headerMap.put("platform", "后台");
            headerMap.put("platformUrl", "后台地址");
            headerMap.put("times", "时长");
            headerMap.put("twoRate", "留存");


            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("tdate", temp.getTdate());
            contentMap.put("channelName", temp.getChannelName());
            contentMap.put("gameName", temp.getGameName());
            contentMap.put("appid", temp.getAppid());
            contentMap.put("orgChannel", temp.getOrgChannel());
            contentMap.put("orgGameName", temp.getOrgGameName());
            contentMap.put("orgAppid", temp.getOrgAppid());
            contentMap.put("price", temp.getPrice());
            contentMap.put("channel", temp.getChannel());
            contentMap.put("derivedRevenue", temp.getDerivedRevenue());
            contentMap.put("volumes", temp.getVolumes());
            contentMap.put("costs", temp.getCosts());
            contentMap.put("pricingType", temp.getPricingType());
            contentMap.put("platform", temp.getPlatform());
            contentMap.put("platformUrl", temp.getPlatformUrl());
            contentMap.put("times", temp.getTimes());
            contentMap.put("twoRate", temp.getTwoRate());

            contentList.add(contentMap);
        }


        String fileName = "买量收入报表_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);

        return null;
    }


    @CrossOrigin
    @RequestMapping(value = "/wx/appExchangeVolumeBuyHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public String appExchangeVolumeBuyHandle(AppExchangeVolume appExchangeVolume, HttpServletRequest request) {
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        int result = 0;
        try {

            if ("edit".equals(request.getParameter("handle"))) {
                result = wxService.updateAppExchangeVolumeBuy(appExchangeVolume);
            }

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    @RequestMapping(value = "/wx/selectGameNameSell", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectGameNameSell(HttpServletRequest request) {
        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        String[] list = ydMapper.selectGameNameSell();
        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", list);

        return result.toJSONString();
    }

    @RequestMapping(value = "/wx/selectAppidSell", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectAppidSell(HttpServletRequest request) {

        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        String[] list = ydMapper.selectAppidSell();
        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", list);

        return result.toJSONString();
    }

    @RequestMapping(value = "/wx/selectOrgChannelSell", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectOrgChannelSell(HttpServletRequest request) {
        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        String[] list = ydMapper.selectOrgChannelSell();
        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", list);

        return result.toJSONString();
    }

    @RequestMapping(value = "/wx/selectOrgGameNameSell", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectOrgGameNameSell(HttpServletRequest request) {
        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", ydMapper.selectOrgGameNameSell());

        return result.toJSONString();
    }

    @RequestMapping(value = "/wx/selectChannelNameSell", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectChannelNameSell(HttpServletRequest request) {
        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", ydMapper.selectChannelNameSell());

        return result.toJSONString();
    }

    @RequestMapping(value = "/wx/selectPricingTypeSell", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectPricingTypeSell(HttpServletRequest request) {
        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", ydMapper.selectPricingTypeSell());

        return result.toJSONString();
    }

    @RequestMapping(value = "/wx/selectGameNameBuy", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectGameNameBuy(HttpServletRequest request) {

        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        String[] list = ydMapper.selectGameNameBuy();
        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", list);

        return result.toJSONString();
    }

    @RequestMapping(value = "/wx/selectAppidBuy", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectAppidBuy(HttpServletRequest request) {

        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        String[] list = ydMapper.selectAppidBuy();
        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", list);

        return result.toJSONString();
    }


    @RequestMapping(value = "/wx/getAppidByChaManage", method = {RequestMethod.GET, RequestMethod.POST})
    public String getAppidByChaManage(HttpServletRequest request) {

        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", ydMapper.getAppidByChaManage());

        return result.toJSONString();
    }

    @RequestMapping(value = "/wx/getNameByChaManage", method = {RequestMethod.GET, RequestMethod.POST})
    public String getNameByChaManage(HttpServletRequest request) {

        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", ydMapper.getNameByChaManage());

        return result.toJSONString();
    }

    @RequestMapping(value = "/wx/selectOrgChannelBuy", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectOrgChannelBuy(HttpServletRequest request) {

        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        String[] list = ydMapper.selectOrgChannelBuy();
        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", list);

        return result.toJSONString();
    }

    @RequestMapping(value = "/wx/selectOrgGameNameBuy", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectOrgGameNameBuy(HttpServletRequest request) {

        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", ydMapper.selectOrgGameNameBuy());

        return result.toJSONString();
    }

    @RequestMapping(value = "/wx/selectChannelNameBuy", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectChannelNameBuy(HttpServletRequest request) {

        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", ydMapper.selectChannelNameBuy());

        return result.toJSONString();
    }

    @RequestMapping(value = "/wx/selectPricingTypeBuy", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectPricingTypeBuy(HttpServletRequest request) {

        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", ydMapper.selectPricingTypeBuy());

        return result.toJSONString();
    }

    /**
     * 小游戏-换量汇总查询
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/wx/selectAppExchangeVolumeVo", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectAppExchangeVolumeVo(HttpServletRequest request) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("hide", request.getParameter("hide"));
        if (!"1".equals(request.getParameter("hide").toString())) {
            map.put("channelName", request.getParameter("channelName"));
            map.put("gameName", request.getParameter("gameName"));
            map.put("appid", request.getParameter("appid"));
        }

        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<AppExchangeVolumeVo> list = wxService.selectAppExchangeVolumeVo(map);
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();

    }

    /**
     * 卖量收入游戏报表修改
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/updateAppChannelAppidInfo", method = {RequestMethod.GET, RequestMethod.POST})
    public String updateAppChannelAppidInfo(HttpServletRequest request, HttpServletResponse response, AppChannelAppidInfo appChannelAppidInfo) {
        String token = request.getParameter("token");

        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();
        try {
            int resp = wxService.updateAppChannelAppidInfo(appChannelAppidInfo);

            if (resp > 0) {
                result.put("ret", 1);
                result.put("msg", "success");
            } else {
                result.put("ret", 0);
                result.put("msg", "无效操作");
            }
            return result.toJSONString();

        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }

    /**
     * 小游戏-卖量收入游戏报表
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/exportAppExchangeVolumeVo", method = RequestMethod.GET)
    public String exportAppExchangeVolumeVo(HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("hide", request.getParameter("hide"));
        if (!"1".equals(request.getParameter("hide").toString())) {
            map.put("channelName", request.getParameter("channelName"));
            map.put("gameName", request.getParameter("gameName"));
            map.put("appid", request.getParameter("appid"));
        }
        List<AppExchangeVolumeVo> list = wxService.selectAppExchangeVolumeVo(map);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;

        if (!"1".equals(request.getParameter("hide").toString())) {
            for (AppExchangeVolumeVo temp : list) {
                headerMap.put("tdate", "日期");
                headerMap.put("channelName", "渠道");
                headerMap.put("gameName", "我方导出产品");
                headerMap.put("appid", "产品appid");
                headerMap.put("addNum", "产品新增");
                headerMap.put("actNum", "产品活跃");
                headerMap.put("perNew", "新增占比");
                headerMap.put("income", "广告收入");
                headerMap.put("totalSell", "总导出收入");
                headerMap.put("dauArpu", "dauArpu");
                headerMap.put("totalNew", "总导出");
                headerMap.put("newSellPer", "总新增导出率");
                headerMap.put("dauSellPer", "总活跃导出率");


                contentMap = new LinkedHashMap<String, Object>();
                contentMap.put("tdate", temp.getTdate());
                contentMap.put("channelName", temp.getChannelName());
                contentMap.put("gameName", temp.getGameName());
                contentMap.put("appid", temp.getAppid());
                contentMap.put("addNum", temp.getAddNum());
                contentMap.put("actNum", temp.getActNum());
                contentMap.put("perNew", temp.getPerNew());
                contentMap.put("income", temp.getIncome());
                contentMap.put("totalSell", temp.getTotalSell());
                contentMap.put("dauArpu", temp.getDauArpu());
                contentMap.put("totalNew", temp.getTotalNew());
                contentMap.put("newSellPer", temp.getNewSellPer());
                contentMap.put("dauSellPer", temp.getDauSellPer());

                contentList.add(contentMap);
            }
        } else {
            for (AppExchangeVolumeVo temp : list) {
                headerMap.put("tdate", "日期");
                headerMap.put("addNum", "产品新增");
                headerMap.put("actNum", "产品活跃");
                headerMap.put("perNew", "新增占比");
                headerMap.put("income", "广告收入");
                headerMap.put("totalSell", "总导出收入");
                headerMap.put("dauArpu", "dauArpu");
                headerMap.put("totalNew", "总导出");
                headerMap.put("newSellPer", "总新增导出率");
                headerMap.put("dauSellPer", "总活跃导出率");

                contentMap = new LinkedHashMap<String, Object>();
                contentMap.put("tdate", temp.getTdate());
                contentMap.put("addNum", temp.getAddNum());
                contentMap.put("actNum", temp.getActNum());
                contentMap.put("perNew", temp.getPerNew());
                contentMap.put("income", temp.getIncome());
                contentMap.put("totalSell", temp.getTotalSell());
                contentMap.put("dauArpu", temp.getDauArpu());
                contentMap.put("totalNew", temp.getTotalNew());
                contentMap.put("newSellPer", temp.getNewSellPer());
                contentMap.put("dauSellPer", temp.getDauSellPer());

                contentList.add(contentMap);
            }
        }
        String fileName = "卖量收入游戏报表_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);

        return null;
    }

    /**
     * 小游戏-换量汇总查询
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/wx/selectAppExchangeVolumeTotalVo", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectAppExchangeVolumeTotal(HttpServletRequest request) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("channel", request.getParameter("channel"));

        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<AppExchangeVolumeTotalVo> list = wxService.selectAppExchangeVolumeTotal(map);
            long size = ((Page) list).getTotal();
            // 导出的excel数据存储
            redisTemplate.opsForValue().set("selectAppExchangeVolumeTotalVo-" + token,
                    list, 20 * 60, TimeUnit.SECONDS);

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();

    }

    /**
     * 换量汇总导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/exportAppExchangeVolumeTotalVo", method = RequestMethod.GET)
    @LoginCheck
    public void exportAppExchangeVolumeTotalVo(HttpServletRequest request, HttpServletResponse response) {
        String token = request.getParameter("token");
        // 数据内容
        List<AppExchangeVolumeTotalVo> list = (List<AppExchangeVolumeTotalVo>) redisTemplate.opsForValue().get("selectAppExchangeVolumeTotalVo-" + token);

        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = request.getParameter("value").split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = request.getParameter("export_file_name")+"_" + DateTime.now().toString("yyyyMMdd")+ ".xls";
        ExportExcelUtil.export2(response,list,head,fileName);
    }

    /**
     * 换量汇总修改
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/updateAppExchangeVolumeTemInfo", method = {RequestMethod.GET, RequestMethod.POST})
    public String updateAppExchangeVolumeTemInfo(HttpServletRequest request, HttpServletResponse response, AppExchangeVolumeTemInfo appExchangeVolumeTemInfo) {
        String token = request.getParameter("token");

        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();
        try {
            int resp = wxService.updateAppExchangeVolumeTemInfo(appExchangeVolumeTemInfo);

            if (resp > 0) {
                result.put("ret", 1);
                result.put("msg", "success");
            } else {
                result.put("ret", 0);
                result.put("msg", "无效操作");
            }
            return result.toJSONString();

        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }

    /**
     * 小游戏-渠道游戏广告数据分析
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/wx/selectAppChannelAppidInfo", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectAppChannelAppidInfo(HttpServletRequest request) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("channel", request.getParameter("channel"));
        map.put("gameName", request.getParameter("gameName"));
        map.put("appid", request.getParameter("appid"));
        map.put("order_str", request.getParameter("order_str"));

        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<AppChannelAppidInfo> list = wxService.selectAppChannelAppidInfo(map);
            long size = ((Page) list).getTotal();
            // 导出的excel数据存储
            redisTemplate.opsForValue().set("selectAppChannelAppidInfo-" + token,
                    wxService.selectAppChannelAppidInfo(map), 20 * 60, TimeUnit.SECONDS);

            result.put("ret", 1);
            result.put("data", list);
            result.put("sum", ydMapper.selectSumAppChannelAppidInfo(map));
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();

    }

    @RequestMapping(value = "/wx/selectChannelName", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectChannelName(HttpServletRequest request) {

        // token验证
        String token = request.getParameter("token");
        // token验证
        if (BlankUtils.checkBlank(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", ydMapper.selectChannelName());

        return result.toJSONString();
    }

    /**
     * 渠道游戏广告数据分析
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/exportAppChannelAppidInfo", method = RequestMethod.GET)
    public String exportAppChannelAppidInfo(HttpServletRequest request, HttpServletResponse response,
                                            String value, String export_file_name) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // 数据内容
        List<AppChannelAppidInfo> list = (List<AppChannelAppidInfo>) redisTemplate.opsForValue().get("selectAppChannelAppidInfo-" + token);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;

        for (AppChannelAppidInfo temp : list) {
            headerMap.put("tdate", "日期");
            headerMap.put("channel", "渠道");
            headerMap.put("appid", "产品");
            headerMap.put("appidKey", "产品appid");
            headerMap.put("addNum", "新增");
            headerMap.put("actNum", "活跃");
            headerMap.put("addPer", "新增占比");
            headerMap.put("income", "广告收入");
            headerMap.put("appIncome", "产品支出");
            headerMap.put("dauArpu", "广告dauarpu");
            headerMap.put("times", "时长");
            headerMap.put("twoRate", "次日留存");
            headerMap.put("banDauArpu", "dauarpuban");
            headerMap.put("screenDauArpu", "dauarpu插屏");
            headerMap.put("openDauArpu", "dauarpu开屏");
            headerMap.put("trafficDauArpu", "dauarpu信息流");
            headerMap.put("videoDauArpu", "dauarpu视频");
            headerMap.put("brickDauArpu", "dauarpu积木广告");
            headerMap.put("avgBanpv", "人均pvban");
            headerMap.put("avgScreenpv", "人均pv插屏");
            headerMap.put("avgOpenpv", "人均pv开屏");
            headerMap.put("avgTrafficpv", "人均pv信息流");
            headerMap.put("avgVideopv", "人均pv视频");
            headerMap.put("avgBrickpv", "人均pv积木广告");
            headerMap.put("banEcpm", "ecpmban");
            headerMap.put("screenEcpm", "ecpm插屏");
            headerMap.put("openEcpm", "ecpm开屏");
            headerMap.put("trafficEcpm", "ecpm信息流");
            headerMap.put("videoEcpm", "ecpm视频");
            headerMap.put("brickEcpm", "ecpm积木广告");
            headerMap.put("banIncome", "收入ban");
            headerMap.put("screenIncome", "收入插屏");
            headerMap.put("openIncome", "收入开屏");
            headerMap.put("trafficIncome", "收入信息流");
            headerMap.put("videoIncome", "收入视频");
            headerMap.put("brickIncome", "收入积木广告");
            headerMap.put("orimsgIncome", "原生msg");
            headerMap.put("banPv", "PVban");
            headerMap.put("screenPv", "PV插屏");
            headerMap.put("openPv", "PV开屏");
            headerMap.put("trafficPv", "PV信息流");
            headerMap.put("videoPv", "PV视频");
            headerMap.put("brickPv", "PV积木广告");

            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("tdate", temp.getTdate());
            contentMap.put("channel", temp.getChannel());
            contentMap.put("appid", temp.getAppid());
            contentMap.put("appidKey", temp.getAppidKey());
            contentMap.put("addNum", temp.getAddNum());
            contentMap.put("actNum", temp.getActNum());
            contentMap.put("addPer", temp.getAddPer());
            contentMap.put("income", temp.getIncome());
            contentMap.put("appIncome", temp.getAppIncome());
            contentMap.put("dauArpu", temp.getDauArpu());
            contentMap.put("times", temp.getTimes());
            contentMap.put("twoRate", temp.getTwoRate());
            contentMap.put("banDauArpu", temp.getBanDauArpu());
            contentMap.put("screenDauArpu", temp.getScreenDauArpu());
            contentMap.put("openDauArpu", temp.getOpenDauArpu());
            contentMap.put("trafficDauArpu", temp.getTrafficDauArpu());
            contentMap.put("videoDauArpu", temp.getVideoDauArpu());
            contentMap.put("avgBanpv", temp.getAvgBanpv());
            contentMap.put("avgScreenpv", temp.getAvgScreenpv());
            contentMap.put("avgOpenpv", temp.getAvgOpenpv());
            contentMap.put("avgTrafficpv", temp.getAvgTrafficpv());
            contentMap.put("avgVideopv", temp.getAvgVideopv());
            contentMap.put("banEcpm", temp.getBanEcpm());
            contentMap.put("screenEcpm", temp.getScreenEcpm());
            contentMap.put("openEcpm", temp.getOpenEcpm());
            contentMap.put("trafficEcpm", temp.getTrafficEcpm());
            contentMap.put("videoEcpm", temp.getVideoEcpm());
            contentMap.put("banIncome", temp.getBanIncome());
            contentMap.put("screenIncome", temp.getScreenIncome());
            contentMap.put("openIncome", temp.getOpenIncome());
            contentMap.put("trafficIncome", temp.getTrafficIncome());
            contentMap.put("videoIncome", temp.getVideoIncome());
            contentMap.put("orimsgIncome", temp.getOrimsgIncome());
            contentMap.put("banPv", temp.getBanPv());
            contentMap.put("screenPv", temp.getScreenPv());
            contentMap.put("openPv", temp.getOpenPv());
            contentMap.put("trafficPv", temp.getTrafficPv());
            contentMap.put("videoPv", temp.getVideoPv());

            contentMap.put("brickPv", temp.getBrickPv());
            contentMap.put("brickIncome", temp.getBrickIncome());
            contentMap.put("brickEcpm", temp.getBrickEcpm());
            contentMap.put("avgBrickpv", temp.getAvgBrickpv());
            contentMap.put("brickDauArpu", temp.getBrickDauArpu());

            contentList.add(contentMap);
        }

        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = export_file_name+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX(response,contentList,head,fileName);

        return null;
    }

    /**
     * 换量权限查询
     *
     * @param value
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "wx/selectAppExchangePermissions", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectAppExchangePermissions(AppExchangePermissions appExchangePermissions,
                                               HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<AppExchangePermissions> list = wxService.selectAppExchangePermissions(appExchangePermissions);
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 换量权限 操作
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "wx/appExchangePermissionsHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public String appExchangePermissionsHandle(HttpServletRequest request, AppExchangePermissions appExchangePermissions, HttpServletResponse response) {
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        int result = 0;
        try {

            if ("add".equals(request.getParameter("handle"))) {
                result = wxService.insertAppExchangePermissions(appExchangePermissions);
            } else if ("edit".equals(request.getParameter("handle"))) {
                result = wxService.updateAppExchangePermissions(appExchangePermissions);
            } else if ("del".equals(request.getParameter("handle"))) {
                result = wxService.deleteAppExchangePermissions(appExchangePermissions);
            }

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    /**
     * 批量删除 操作
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "wx/deleteAppExchangeVolumeBuy", method = {RequestMethod.GET, RequestMethod.POST})
    public String deleteAppExchangeVolumeBuy(String ids, HttpServletRequest request, AppExchangePermissions appExchangePermissions, HttpServletResponse response) {
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        int result = 0;
        try {

            result = ydMapper.deleteAppExchangeVolumeBuy(ids);
            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    /**
     * 批量删除 操作
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "wx/deleteAppExchangeVolumeSell", method = {RequestMethod.GET, RequestMethod.POST})
    public String deleteAppExchangeVolumeSell(String ids, HttpServletRequest request, AppExchangePermissions appExchangePermissions, HttpServletResponse response) {
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        int result = 0;
        try {

            result = ydMapper.deleteAppExchangeVolumeSell(ids);
            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    /**
     * 移动-渠道信息维护 操作
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "wx/updateAppIncome", method = {RequestMethod.GET, RequestMethod.POST})
    public String updateAppIncome(AppChannelAppidInfo appChannelAppidInfo, HttpServletRequest request, HttpServletResponse response) {
        // token验证
        String token = request.getParameter("token");
		/*if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);*/

        int result = 0;
        try {

            if ("edit".equals(request.getParameter("handle"))) {
                result = ydService.updateAppIncome(appChannelAppidInfo);
            }

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    @RequestMapping(value = "ad/updateDnAdConfigVo", method = {RequestMethod.GET, RequestMethod.POST})
    public String updateDnAdConfigVo(DnAdConfigVo dnAdConfigVo, HttpServletRequest request, HttpServletResponse response) {
        // token验证
        String token = request.getParameter("token");
//		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//			return "{\"ret\":2,\"msg\":\"token is error!\"}";
//		else
//			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        int result = 0;
        try {

            if ("edit".equals(request.getParameter("handle"))) {
                result = ydService.updateDnAdConfigVo(dnAdConfigVo);
            }/*else if ("change".equals(request.getParameter("handle"))) {
				result = ydService.updateAdinfoKeys(dnAdConfigVo);
			}*/

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    @RequestMapping(value = "ad/selectChaId", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectChaId(HttpServletRequest request, HttpServletResponse response) {
        JSONObject result = new JSONObject();
        result.put("ret", 1);
        result.put("data", ydMapper.selectChaId());
        result.put("platFormData", ydMapper.selectPlatForm());

        return result.toJSONString();
    }


    /* 定制化页面过滤接口 /market/pageFind ID_200707164749 */
    @RequestMapping(value = "/market/pageFind", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String pageFind(WaibaoUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 进行token验证 */
            // if (BlankUtils.checkBlank(cu.getToken())) {
            // 	return "{\"ret\":2,\"msg\":\"token is error!\"}";
            // };
            // if (redisTemplate.opsForValue().get(cu.getToken()) == null) {
            // 	return "{\"ret\":2,\"msg\":\"token is error!\"}";
            // } else {
            // 	redisTemplate.opsForValue().set(cu.getToken(), redisTemplate.opsForValue().get(cu.getToken()), 20 * 60,
            // 		TimeUnit.SECONDS);
            // };
            /* 设定翻页 */
            // String start = BlankUtils.checkNull(request, "start");
            // String limit = BlankUtils.checkNull(request, "limit");
            // int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            // int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // int pageNo = (pageStart / pageSize) + 1;
            // PageHelper.startPage(pageNo, pageSize); // 进行分页
            /* 设定检索参数 */
            Map<String, Object> findMap = new HashMap<String, Object>();
            findMap.put("type", request.getParameter("type"));
            findMap.put("page_url", request.getParameter("page"));
            findMap.put("find_val", request.getParameter("find"));
            /* 库检索菜单数据 */
            List<WaibaoUserVo> find = ydMapper.selectFind(findMap);
            // BlankUtils.classForeachEcho(find.get(0));
            /* 获取总条数 */
            // long total = ((Page) find).getTotal();
            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", 1);
            out.put("msg", "success");
            out.put("data", find);
            // out.put("totalCount", total);
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }


    //同步小游戏广告数据
    @RequestMapping(value = "/wx/sycGameAdInfo", method = {RequestMethod.GET, RequestMethod.POST})
    public String sycGameAdInfo(String startTime, HttpServletRequest request, HttpServletResponse response) {
        int r = 0;
        try {
            //渠道游戏广告数据分析数据获取
            List<GameAdInfo> list = ydMapper.selectDateGameAd(startTime);

            GameAdInfo gameAdInfo = new GameAdInfo();

            //先根据日期删除当天已存在数据后执行同步操作,避免一些数据无法被覆盖的问题
            gameAdInfo.setcDate(startTime);
            wxService.deleteGameAdInfo(gameAdInfo);

            //同步小游戏广告数据查询表
            r = wxService.sycGameAdInfo(list);

            if (r > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }


    /**
     *
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/selectWbguiFormconfig", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectWbguiFormconfig(WbguiFormconfig WbguiFormconfig,
                                        HttpServletRequest request, HttpServletResponse response) throws IOException {
        JSONObject result = new JSONObject();

        if (BlankUtils.checkBlank(WbguiFormconfig.getSingleid())) {
            return "{\"ret\":error,\"msg\":\"singleid!不能为空\"}";
        }

        WbguiFormconfig temp = ydService.selectWbguiFormconfig(WbguiFormconfig);
        //String icon = Base64.encodeBase64String(temp.getIcon());

        if(temp.getAd() == null){
        	return "{\"ret\":error,\"msg\":\"ad模块为空\"}";
        }

		String[] split2 = temp.getAd().split(";");
		String ad = "";
		for (int i = 0; i < split2.length; i++) {
			ad += "'"+split2[i]+"',";
		}
		String sql = "SELECT CONCAT_WS(':',moduleType,moduleName) FROM dnwx_client.wbgui_module a where  (moduleState = '0' and isMustbring  = '1' and moduleIsNew = '1' ) or a.moduleSingleid  in ("+ad.substring(0, ad.length() -1)+")";
		List<String> list = adMapper.queryListString(sql);
		String adInfo = StringUtils.join(list,",");

        Map<String, Object> contentMap = new LinkedHashMap<String, Object>();
        contentMap.put("adInfo", adInfo);
        contentMap.put("singleid", temp.getSingleid());
        contentMap.put("icon", temp.getIcon());
        contentMap.put("typeName", temp.getTypeName());
        contentMap.put("gameName", temp.getGameName());
        contentMap.put("channel", temp.getChannel());
        contentMap.put("iconSize", temp.getIconSize());
        contentMap.put("date", temp.getDate());
        contentMap.put("state", temp.getState());
        contentMap.put("priority", temp.getPriority());
        contentMap.put("packageName", temp.getPackageName());
        contentMap.put("pjId", temp.getPjId());
        contentMap.put("versionName", temp.getVersionName());
        contentMap.put("versionCode", temp.getVersionCode());
        contentMap.put("umengId", temp.getUmengId());
        contentMap.put("dataEye", temp.getDataEye());
        contentMap.put("ad", temp.getAd());
        contentMap.put("initiator", temp.getInitiator());
        contentMap.put("originator", temp.getOriginator());
        contentMap.put("endDate", temp.getEndDate());
        contentMap.put("language", temp.getLanguage());
        contentMap.put("platform", temp.getPlatform());
        contentMap.put("email", temp.getEmail());
        contentMap.put("planners", temp.getPlanners());
        contentMap.put("statistics", temp.getStatistics());
        contentMap.put("channelTag", temp.getChannelTag());
        contentMap.put("flag", temp.getFlag());
        contentMap.put("instructions", temp.getInstructions());
        contentMap.put("parameter", temp.getParameter());
        contentMap.put("appkey", temp.getAppkey());
        contentMap.put("appid", temp.getAppid());
        contentMap.put("moduleData", temp.getModuleData());
        contentMap.put("moduleVersion", temp.getModuleVersion());
        contentMap.put("company", temp.getCompany());
        contentMap.put("crNum", temp.getCrNum());

        contentMap.put("appType", temp.getAppType());

        // 华为渠道sdk参数
        JSONArray array = new JSONArray();

        JSONObject huawei = new JSONObject();
        huawei.put("fileName", temp.getFileName());
        huawei.put("filePath", temp.getFilePath());
        huawei.put("fileContent", temp.getFileContent());
        array.add(huawei);
        contentMap.put("sdkFiles", array);

        String placeholders = "";
        //pay;mi;AppId;2882303761519855362#pay;mi;AppKey;5671985587362#tj;umeng;UMENG_APPKEY;60614246b8c8d45c13b419c5#
        String moduleData = temp.getModuleData();

        String ssql = "SELECT moduleType,moduleName,moduleSdkKey FROM dnwx_client.wbgui_module a where  a.moduleSingleid  in ("+ad.substring(0, ad.length() -1)+")  ";
        List<WbguiModule> queryListBean = adService.queryListBean(ssql,WbguiModule.class);

        for (String  module: moduleData.split("#")) {
        	String[] modArr = module.split(";");
        	if(modArr.length > 2 ){
        		String moduleType = modArr[0];
            	String moduleName = modArr[1];
            	String old = modArr[2];
            	String place = "" ;

            	WbguiModule WbguiModules = queryListBean.stream().filter(e -> moduleName.equals(e.getModuleName()) && moduleType.equals(e.getModuleType())).findFirst().orElse(null);
            	if(null == WbguiModules ){
            		placeholders += module+"#";
            		continue;
            	}

            	String moduleSdkKey = WbguiModules.getModuleSdkKey();

            	if(!BlankUtils.checkBlank(moduleSdkKey)){
            		String[] SdkKey = moduleSdkKey.split("#");
            		for (int i = 0; i < SdkKey.length; i++) {
            			if(old.equals(SdkKey[i].split(";")[0])){
            				if(!SdkKey[i].endsWith(";")){
            					place = SdkKey[i].split(";")[SdkKey[i].split(";").length-1];
                				break;
            				}

            			}
					}
            	}
            	if(!BlankUtils.checkBlank(old) && !BlankUtils.checkBlank(place)){
            		module = module.replace(old, place);
            	}
        	}
        	placeholders += module+"#";
		}

        contentMap.put("moduleData", placeholders);

        result.put("data", contentMap);

        return result.toJSONString();
    }

    /**
    * 客户端出包参数接口v2.做去重处理
    * @param request
    * @param response
    * @return
    * @throws IOException
    */
   @RequestMapping(value = "/selectWbguiFormconfigV2", method = {RequestMethod.GET, RequestMethod.POST})
   public String selectWbguiFormconfigV2(WbguiFormconfig WbguiFormconfig,
                                       HttpServletRequest request, HttpServletResponse response) throws IOException {
       JSONObject result = new JSONObject();

       if (BlankUtils.checkBlank(WbguiFormconfig.getSingleid())) {
           return "{\"ret\":error,\"msg\":\"singleid!不能为空\"}";
       }

       WbguiFormconfig temp = ydService.selectWbguiFormconfig(WbguiFormconfig);
       //String icon = Base64.encodeBase64String(temp.getIcon());

       if(temp == null || temp.getAd() == null){
       		return "{\"ret\":error,\"msg\":\"ad模块为空\"}";
       }

       /** 模块区分游戏和应用的必带属性  -张炳杰.2022.09.13 */
       String where = " and gamebring='1' ";
       if("1".equals(temp.getAppType())){
       		where = " and appbring='1' ";
       }

       /** 模块区分安卓和ios的属性  -张炳杰.2022.11.04 */
       if("ios".equalsIgnoreCase(temp.getPlatform())){
    	   where += " and moduleIsIos = 'ios' ";
       }else{
    	   where += " and moduleIsIos != 'ios' ";
       }
       
		String[] split2 = temp.getAd().split(";");
		String ad = "";
		for (int i = 0; i < split2.length; i++) {
			ad += "'"+split2[i]+"',";
		}
		String sql = "SELECT CONCAT_WS(':',CONCAT(modulePrefix,moduleType),moduleName,moduleVersion) FROM dnwx_client.wbgui_module a where  (1=1 "+where+" and moduleIsNew = '1' ) or a.moduleSingleid  in ("+ad.substring(0, ad.length() -1)+")";
		List<String> list = adMapper.queryListString(sql);
		/** 特殊处理，判断带有appointVer-指向版本参数时，使用指向版本值  -张炳杰.2022.08.08 */
		if(!BlankUtils.checkBlank(temp.getAppointVer())){
			String[] split = temp.getAppointVer().split("#");

			list = list.stream().map(act2 -> {

				for (String act : split) {
					String[] modules = act.split(":");
					// 判断如果前缀的包名相同的话，使用AppointVer替换掉查询的moduleVersion
					if(modules.length == 3){
						String prefix = modules[0]+":"+modules[1];

						if(act2.startsWith(prefix))
							return act;
					}
				}
				return act2;
			}).collect(Collectors.toList());
		}


		String adInfo = StringUtils.join(list,",");

       Map<String, Object> contentMap = new LinkedHashMap<String, Object>();
       contentMap.put("adInfo", adInfo);
       contentMap.put("singleid", temp.getSingleid());
       contentMap.put("icon", temp.getIcon());
       contentMap.put("typeName", temp.getTypeName());
       contentMap.put("gameName", temp.getGameName());
       contentMap.put("channel", temp.getChannel());
       contentMap.put("iconSize", temp.getIconSize());
       contentMap.put("date", temp.getDate());
       contentMap.put("state", temp.getState());
       contentMap.put("priority", temp.getPriority());
       contentMap.put("packageName", temp.getPackageName());
       contentMap.put("pjId", temp.getPjId());
       contentMap.put("versionName", temp.getVersionName());
       contentMap.put("versionCode", temp.getVersionCode());
       contentMap.put("umengId", temp.getUmengId());
       contentMap.put("dataEye", temp.getDataEye());
       contentMap.put("ad", temp.getAd());
       contentMap.put("initiator", temp.getInitiator());
       contentMap.put("originator", temp.getOriginator());
       contentMap.put("endDate", temp.getEndDate());
       contentMap.put("language", temp.getLanguage());
       contentMap.put("platform", temp.getPlatform());
       contentMap.put("email", temp.getEmail());
       contentMap.put("planners", temp.getPlanners());
       contentMap.put("statistics", temp.getStatistics());
       contentMap.put("channelTag", temp.getChannelTag());
       contentMap.put("flag", temp.getFlag());
       contentMap.put("instructions", temp.getInstructions());
       contentMap.put("parameter", temp.getParameter());
       contentMap.put("appkey", temp.getAppkey());
       contentMap.put("appid", temp.getAppid());
       contentMap.put("moduleData", temp.getModuleData());
       contentMap.put("moduleVersion", temp.getModuleVersion());
       contentMap.put("company", temp.getCompany());
       contentMap.put("crNum", temp.getCrNum());

       contentMap.put("appType", temp.getAppType());
       contentMap.put("icons", Base64.encodeBase64String(temp.getIcons()));

       // 华为渠道sdk参数
       JSONArray array = new JSONArray();

       JSONObject huawei = new JSONObject();
       huawei.put("fileName", temp.getFileName());
       huawei.put("filePath", temp.getFilePath());
       huawei.put("fileContent", temp.getFileContent());
       array.add(huawei);
       contentMap.put("sdkFiles", array);

       String placeholders = "";
       //pay;mi;AppId;2882303761519855362#pay;mi;AppKey;5671985587362#tj;umeng;UMENG_APPKEY;60614246b8c8d45c13b419c5#
       String moduleData = temp.getModuleData();

       String ssql = "SELECT moduleType,moduleName,moduleSdkKey FROM dnwx_client.wbgui_module a where  a.moduleSingleid  in ("+ad.substring(0, ad.length() -1)+")  ";
       List<WbguiModule> queryListBean = adService.queryListBean(ssql,WbguiModule.class);

       for (String  module: moduleData.split("#")) {
       	String[] modArr = module.split(";");
       	if(modArr.length > 2 ){
       		String moduleType = modArr[0];
           	String moduleName = modArr[1];
           	String old = modArr[2];
           	String place = "" ;

           	WbguiModule WbguiModules = queryListBean.stream().filter(e -> moduleName.equals(e.getModuleName()) && moduleType.equals(e.getModuleType())).findFirst().orElse(null);
           	if(null == WbguiModules ){
           		placeholders += module+"#";
           		continue;
           	}

           	String moduleSdkKey = WbguiModules.getModuleSdkKey();

           	if(!BlankUtils.checkBlank(moduleSdkKey)){
           		String[] SdkKey = moduleSdkKey.split("#");
           		for (int i = 0; i < SdkKey.length; i++) {
           			if(old.equals(SdkKey[i].split(";")[0])){
           				if(!SdkKey[i].endsWith(";")){
           					place = SdkKey[i].split(";")[SdkKey[i].split(";").length-1];
               				break;
           				}

           			}
					}
           	}
           	if(!BlankUtils.checkBlank(old) && !BlankUtils.checkBlank(place)){
           		module = module.replace(old, place);
           	}
       	}
       	placeholders += module+"#";
		}

       contentMap.put("moduleData", placeholders);

       result.put("data", contentMap);

       return result.toJSONString();
   }

   /**
    * 客户端出包参数接口v3.ios包特殊使用
    * @param request
    * @param response
    * @return
    * @throws IOException
    */
   @RequestMapping(value = "/selectWbguiFormconfigV3", method = {RequestMethod.GET, RequestMethod.POST})
   public String selectWbguiFormconfigV3(WbguiFormconfig WbguiFormconfig,
                                       HttpServletRequest request, HttpServletResponse response) throws IOException {
       JSONObject result = new JSONObject();

       if (BlankUtils.checkBlank(WbguiFormconfig.getSingleid())) {
           return "{\"ret\":error,\"msg\":\"singleid!不能为空\"}";
       }

       WbguiFormconfig temp = ydService.selectWbguiFormconfig(WbguiFormconfig);
       //String icon = Base64.encodeBase64String(temp.getIcon());

       if(temp.getAd() == null){
       		return "{\"ret\":error,\"msg\":\"ad模块为空\"}";
       }

       /** 模块区分游戏和应用的必带属性  -张炳杰.2022.09.13 */
       String where = " and gamebring='1' ";
       if("1".equals(temp.getAppType())){
       		where = " and appbring='1' ";
       }

       /** 模块区分安卓和ios的属性  -张炳杰.2022.11.04 */
       if("ios".equalsIgnoreCase(temp.getPlatform())){
    	   where += " and moduleIsIos = 'ios' ";
       }else{
    	   where += " and moduleIsIos != 'ios' and moduleIsNew = '1' ";
       }

		String[] split2 = temp.getAd().split(";");
		String ad = "";
		for (int i = 0; i < split2.length; i++) {
			ad += "'"+split2[i]+"',";
		}
		String sql = "SELECT CONCAT_WS(':',CONCAT(modulePrefix,moduleType),moduleName) FROM dnwx_client.wbgui_module a where  (1=1 "+where+" ) or a.moduleSingleid  in ("+ad.substring(0, ad.length() -1)+")";

		List<String> list = adMapper.queryListString(sql);
		/** 特殊处理，判断带有appointVer-指向版本参数时，使用指向版本值  -张炳杰.2022.08.08 */
		if(!BlankUtils.checkBlank(temp.getAppointVer())){
			String[] split = temp.getAppointVer().split("#");

			list = list.stream().map(act2 -> {

				for (String act : split) {
					String[] modules = act.split(":");
					// 判断如果前缀的包名相同的话，使用AppointVer替换掉查询的moduleVersion
					if(modules.length == 3){
						String prefix = modules[0]+":"+modules[1];

						if(act2.startsWith(prefix))
							return act;
					}
				}
				return act2;
			}).collect(Collectors.toList());
		}


		String adInfo = StringUtils.join(list,",");

       Map<String, Object> contentMap = new LinkedHashMap<String, Object>();
       contentMap.put("adInfo", adInfo);
       contentMap.put("singleid", temp.getSingleid());
       contentMap.put("icon", temp.getIcon());
       contentMap.put("typeName", temp.getTypeName());
       contentMap.put("gameName", temp.getGameName());
       contentMap.put("channel", temp.getChannel());
       contentMap.put("iconSize", temp.getIconSize());
       contentMap.put("date", temp.getDate());
       contentMap.put("state", temp.getState());
       contentMap.put("priority", temp.getPriority());
       contentMap.put("packageName", temp.getPackageName());
       contentMap.put("pjId", temp.getPjId());
       contentMap.put("versionName", temp.getVersionName());
       contentMap.put("versionCode", temp.getVersionCode());
       contentMap.put("umengId", temp.getUmengId());
       contentMap.put("dataEye", temp.getDataEye());
       contentMap.put("ad", temp.getAd());
       contentMap.put("initiator", temp.getInitiator());
       contentMap.put("originator", temp.getOriginator());
       contentMap.put("endDate", temp.getEndDate());
       contentMap.put("language", temp.getLanguage());
       contentMap.put("platform", temp.getPlatform());
       contentMap.put("email", temp.getEmail());
       contentMap.put("planners", temp.getPlanners());
       contentMap.put("statistics", temp.getStatistics());
       contentMap.put("channelTag", temp.getChannelTag());
       contentMap.put("flag", temp.getFlag());
       contentMap.put("instructions", temp.getInstructions());
       contentMap.put("parameter", temp.getParameter());
       contentMap.put("appkey", temp.getAppkey());
       contentMap.put("appid", temp.getAppid());
       contentMap.put("moduleData", temp.getModuleData());
       contentMap.put("moduleVersion", temp.getModuleVersion());
       contentMap.put("company", temp.getCompany());
       contentMap.put("crNum", temp.getCrNum());

       contentMap.put("appType", temp.getAppType());
       contentMap.put("icons", Base64.encodeBase64String(temp.getIcons()));

       // 华为渠道sdk参数
       JSONArray array = new JSONArray();

       JSONObject huawei = new JSONObject();
       huawei.put("fileName", temp.getFileName());
       huawei.put("filePath", temp.getFilePath());
       huawei.put("fileContent", temp.getFileContent());
       array.add(huawei);
       contentMap.put("sdkFiles", array);

       String placeholders = "";
       //pay;mi;AppId;2882303761519855362#pay;mi;AppKey;5671985587362#tj;umeng;UMENG_APPKEY;60614246b8c8d45c13b419c5#
       String moduleData = temp.getModuleData();

       String ssql = "SELECT moduleType,moduleName,moduleSdkKey FROM dnwx_client.wbgui_module a where  a.moduleSingleid  in ("+ad.substring(0, ad.length() -1)+")  ";
       List<WbguiModule> queryListBean = adService.queryListBean(ssql,WbguiModule.class);

       for (String  module: moduleData.split("#")) {
       	String[] modArr = module.split(";");
       	if(modArr.length > 2 ){
       		String moduleType = modArr[0];
           	String moduleName = modArr[1];
           	String old = modArr[2];
           	String place = "" ;

           	WbguiModule WbguiModules = queryListBean.stream().filter(e -> moduleName.equals(e.getModuleName()) && moduleType.equals(e.getModuleType())).findFirst().orElse(null);
           	if(null == WbguiModules ){
           		placeholders += module+"#";
           		continue;
           	}

           	String moduleSdkKey = WbguiModules.getModuleSdkKey();

           	if(!BlankUtils.checkBlank(moduleSdkKey)){
           		String[] SdkKey = moduleSdkKey.split("#");
           		for (int i = 0; i < SdkKey.length; i++) {
           			if(old.equals(SdkKey[i].split(";")[0])){
           				if(!SdkKey[i].endsWith(";")){
           					place = SdkKey[i].split(";")[SdkKey[i].split(";").length-1];
               				break;
           				}

           			}
					}
           	}
           	if(!BlankUtils.checkBlank(old) && !BlankUtils.checkBlank(place)){
           		module = module.replace(old, place);
           	}
       	}
       	placeholders += module+"#";
		}

       contentMap.put("moduleData", placeholders);

       result.put("data", contentMap);

       return result.toJSONString();
   }


    /**
     * 小游戏数据导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/exportWechatAddChannel", method = RequestMethod.GET)
    public String exportWechatAddChannel(HttpServletRequest request, HttpServletResponse response, String startTime, String endTime) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");

        // 数据内容
        List<WechatAddChannel> list = ydMapper.selectWechatAddChannel(startTime, endTime);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;

        for (WechatAddChannel temp : list) {
            headerMap.put("tdate", "日期");
            headerMap.put("add_name", "新增用户");
            headerMap.put("add_appid", "新增用户appid");
            headerMap.put("channel", "合作渠道");
            headerMap.put("wxname", "抓取游戏名");
            headerMap.put("wxappid", "抓取游戏appid");
            headerMap.put("addnum", "渠道新增");

            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("tdate", temp.getTdate());
            contentMap.put("add_name", temp.getAdd_name());
            contentMap.put("add_appid", temp.getAdd_appid());
            contentMap.put("channel", temp.getChannel());
            contentMap.put("wxname", temp.getWxname());
            contentMap.put("wxappid", temp.getWxappid());
            contentMap.put("addnum", temp.getAddnum());

            contentList.add(contentMap);
        }


        String fileName = "小游戏数据导出_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);

        return null;
    }

    /**
     * 广告收入同步
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wx/updateAppChannelForIncomeSum", method = {RequestMethod.GET, RequestMethod.POST})
    public String updateAppChannelForIncomeSum(String tdate, HttpServletRequest request, HttpServletResponse response) {
        try {
            if (BlankUtils.checkBlank(tdate)) {
                return "{\"ret\":0,\"msg\":\"同步失败,选择日期！\"}";
            }

            int r = ydMapper.updateAppChannelForIncomeSum(tdate);

            if (r > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    @RequestMapping(value = "/yd/selectDnwxInterfaceConfigiKey", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectDnwxInterfaceConfigiKey(HttpServletRequest request) {

        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", ydMapper.selectDnwxInterfaceConfigiKey());

        return result.toJSONString();
    }

	@RequestMapping(value = "/updateWbguiModulehistory", method = {RequestMethod.GET, RequestMethod.POST})
    public String updateWbguiModulehistory(WbguiModulehistory m, HttpServletRequest request, HttpServletResponse response) throws IOException {

		if(BlankUtils.checkBlank(m.getVersion())||BlankUtils.checkBlank(m.getARTIFACT_ID())
				||BlankUtils.checkBlank(m.getVersion())||BlankUtils.checkBlank(m.getGROUP_ID()) ){
			return "{\"ret\":0,\"msg\":\"参数不能空!\"}";
		}
		try {

//			select modulePrefix,moduleType,moduleName,CONCAT(modulePrefix,moduleType) as GROUP_ID,moduleName AS ARTIFACT_ID from dnwx_client.wbgui_module WHERE moduleIsIos <>'ios'
			String singleList = " select moduleSingleid  from  dnwx_client.wbgui_module WHERE moduleIsIos <>'ios'   "
					+ " and moduleName  = '"+m.getARTIFACT_ID()+"' and CONCAT(modulePrefix,moduleType) = '"+m.getGROUP_ID() +"'";
			List<String> list = adService.queryListString(singleList);

			/** 增加list的空判断，避免异常 */
			if(list == null || list.isEmpty() || BlankUtils.checkBlank(list.get(0))){
				return "{\"ret\":0,\"msg\":\"Singleid匹配不到!\"}";
			}else{
				m.setSingleid(list.get(0));
			}

			String query = " SELECT * FROM dnwx_client.wbgui_modulehistory a where a.singleid = '"+m.getSingleid()+"' and a.version = '"+ m.getVersion()+ "'" ;
			List<WbguiModulehistory> queryListBean = adService.queryListBean(query, WbguiModulehistory.class);

			if(queryListBean != null && queryListBean.size()>0){
				 return "{\"ret\":1,\"msg\":\"操作成功! 该数据已存在\"}";
			}

			String sql = "insert into  dnwx_client.wbgui_modulehistory(`version`, `changeDate`, `singleid`, `remarks`, `ModifiName`, `Fouce`, `sdkver`, `sdkdate`,`largeVer`) "+
				"values(#{obj.version},DATE_FORMAT(NOW(),'%Y/%m/%d %H:%i:%s'),#{obj.singleid},#{obj.remarks},#{obj.modifiName},#{obj.fouce},#{obj.sdkver},#{obj.sdkdate},#{obj.largeVer}) ";

			int result = adService.execSqlHandle(sql, m);

			if(result > 0)
				 return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				 return "{\"ret\":0,\"msg\":\"无效操作!\"}";


		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}

	}

	 /**
     *
     *
     * @param value
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/selectWbguiFormconfigIcon", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectWbguiFormconfigIcon(WbguiFormconfig WbguiFormconfig,
                                        HttpServletRequest request, HttpServletResponse response) throws IOException {
        JSONObject result = new JSONObject();

        if (BlankUtils.checkBlank(WbguiFormconfig.getSingleid())) {
            return "{\"ret\":error,\"msg\":\"singleid!不能为空\"}";
        }

        WbguiFormconfig temp = ydService.selectWbguiFormconfig(WbguiFormconfig);
        Map<String, Object> contentMap = new LinkedHashMap<String, Object>();

        String icon = Base64.encodeBase64String(temp.getIcons());

        contentMap.put("icon", icon);

        result.put("data", contentMap);

        return result.toJSONString();
    }


    /**
     * 新版本广告源配置
     *
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/wx/selectAppChannelCollect", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectAppChannelCollect(AppChannelCollect app, HttpServletRequest request) throws IOException {

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        String sql = "select *  from app_channel_collect where account = '"+app.getAccount()+"' and channel = '"+app.getChannel()+"'";

        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<Map<String, Object>> list = adService.queryListMapTwo(sql, app);
        long size = ((Page) list).getTotal();

        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    @RequestMapping(value = "/wx/appChannelCollectHandle", method = {RequestMethod.POST})
    public String appChannelCollectHandle(String handle, AppChannelCollect app,
                                    HttpServletRequest request, HttpServletResponse response) throws IOException, Exception {

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        int result = 0;
        String sql = null;

        sql = "insert into app_channel_collect(account,channel,appids,cname) " +
                "values(#{obj.account},#{obj.channel},#{obj.appids},#{obj.cname}) "
                + "ON DUPLICATE KEY UPDATE appids = VALUES(appids),cname =VALUES(cname) ";

        result = adService.execSqlHandle(sql, app);
        if (result > 0)
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        else
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";

    }
    /**
     * 移动-特殊设备监控 查询
     *
     * @param redpackWxconfigInfo
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/yd/selectSuperBaseInfo", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectSuperBaseInfo(AppBaseParam record,
                                            HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<AppBaseParam> list = ydMapper.selectSuperBaseInfo(record);
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 特殊设备监控分析
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/yd/exportSuperBaseInfo", method = {RequestMethod.GET, RequestMethod.POST})
    public String exportSuperBaseInfo(AppBaseParam record,HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // 数据内容
        List<AppBaseParam> list = ydMapper.selectSuperBaseInfo(record);
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";;
		Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;

        for (AppBaseParam temp : list) {
            headerMap.put("currentTime", "服务端日期");
            headerMap.put("lsn", "lsn");
            headerMap.put("prjid", "项目id");
            headerMap.put("cha", "子渠道名称");
            headerMap.put("g", "wifi名称");
            headerMap.put("ip", "ip");
            headerMap.put("name", "地区");
            headerMap.put("a", "adb调试(1:是0:否)");
            headerMap.put("b", "是否在hook环境(1:是0:否)");
            headerMap.put("c", "是否root(1:是0:否)");
            headerMap.put("d", "设备型号");
            headerMap.put("e", "是否wifi(1:是0:否)");
            headerMap.put("f", "操作系统版本");
            headerMap.put("appid", "产品名称");
            headerMap.put("timestamp", "客户端日期");
            headerMap.put("pkgList", "安装包");
            headerMap.put("imei", "imei");
            headerMap.put("oaid", "oaid");
            headerMap.put("buy_id", "用户来源");

            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("currentTime", temp.getCurrentTime());
            contentMap.put("lsn", temp.getLsn());
            contentMap.put("prjid", temp.getPrjid());
            contentMap.put("cha", temp.getCha());
            contentMap.put("g", temp.getG());
            contentMap.put("ip", temp.getIp());
            contentMap.put("name", temp.getName());
            contentMap.put("a", temp.getA());
            contentMap.put("b", temp.getB());
            contentMap.put("c", temp.getC());
            contentMap.put("d", temp.getD());
            contentMap.put("e", temp.getE());
            contentMap.put("f", temp.getF());
            contentMap.put("appid", appMap.get(temp.getAppid()).get("app_name"));
            contentMap.put("timestamp", temp.getTimestamp());
            contentMap.put("pkgList", temp.getPkgList());
            contentMap.put("imei",  temp.getImei());
            contentMap.put("oaid", temp.getOaid());
            contentMap.put("buy_id", temp.getBuy_id());

            contentList.add(contentMap);
        }


        String fileName = "特殊设备监控分析_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);

        return null;
    }


    /**
     * 客户端出包参数接口v4.做去重处理,新增
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/selectWbguiFormconfigV4", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectWbguiFormconfigV4(WbguiFormconfig WbguiFormconfig,
                                          HttpServletRequest request, HttpServletResponse response) throws IOException {
        JSONObject result = new JSONObject();

        if (BlankUtils.checkBlank(WbguiFormconfig.getSingleid())) {
            return "{\"ret\":error,\"msg\":\"singleid!不能为空\"}";
        }

        WbguiFormconfig temp = ydService.selectWbguiFormconfig(WbguiFormconfig);
        //String icon = Base64.encodeBase64String(temp.getIcon());

        if(temp.getAd() == null){
            return "{\"ret\":error,\"msg\":\"ad模块为空\"}";
        }

        /** 模块区分游戏和应用的必带属性  -张炳杰.2022.09.13 */
        String where = " and gamebring='1' ";
        if("1".equals(temp.getAppType())){
            where = " and appbring='1' ";
        }

        /** 模块区分安卓和ios的属性  -张炳杰.2022.11.04 */
        if("ios".equalsIgnoreCase(temp.getPlatform())){
            where += " and moduleIsIos = 'ios' ";
        }else{
            where += " and moduleIsIos != 'ios' ";
        }

        String[] split2 = temp.getAd().split(";");
        String ad = "";
        for (int i = 0; i < split2.length; i++) {
            ad += "'"+split2[i]+"',";
        }
        String sql = "SELECT CONCAT_WS(':',CONCAT(modulePrefix,moduleType),moduleName,moduleVersion) FROM dnwx_client.wbgui_module a where  (1=1 "+where+" and moduleIsNew = '1' ) or a.moduleSingleid  in ("+ad.substring(0, ad.length() -1)+")";
        List<String> list = adMapper.queryListString(sql);
        /** 特殊处理，判断带有appointVer-指向版本参数时，使用指向版本值  -张炳杰.2022.08.08 */
        if(!BlankUtils.checkBlank(temp.getAppointVer())){
            String[] split = temp.getAppointVer().split("#");

            list = list.stream().map(act2 -> {

                for (String act : split) {
                    String[] modules = act.split(":");
                    // 判断如果前缀的包名相同的话，使用AppointVer替换掉查询的moduleVersion
                    if(modules.length == 3){
                        String prefix = modules[0]+":"+modules[1];

                        if(act2.startsWith(prefix))
                            return act;
                    }
                }
                return act2;
            }).collect(Collectors.toList());
        }


        String adInfo = StringUtils.join(list,",");

        Map<String, Object> contentMap = new LinkedHashMap<String, Object>();
        contentMap.put("adInfo", adInfo);
        contentMap.put("singleid", temp.getSingleid());
        contentMap.put("icon", temp.getIcon());
        contentMap.put("typeName", temp.getTypeName());
        contentMap.put("gameName", temp.getGameName());
        contentMap.put("channel", temp.getChannel());
        contentMap.put("iconSize", temp.getIconSize());
        contentMap.put("date", temp.getDate());
        contentMap.put("state", temp.getState());
        contentMap.put("priority", temp.getPriority());
        contentMap.put("packageName", temp.getPackageName());
        contentMap.put("pjId", temp.getPjId());
        contentMap.put("versionName", temp.getVersionName());
        contentMap.put("versionCode", temp.getVersionCode());
        contentMap.put("umengId", temp.getUmengId());
        contentMap.put("dataEye", temp.getDataEye());
        contentMap.put("ad", temp.getAd());
        contentMap.put("initiator", temp.getInitiator());
        contentMap.put("originator", temp.getOriginator());
        contentMap.put("endDate", temp.getEndDate());
        contentMap.put("language", temp.getLanguage());
        contentMap.put("platform", temp.getPlatform());
        contentMap.put("email", temp.getEmail());
        contentMap.put("planners", temp.getPlanners());
        contentMap.put("statistics", temp.getStatistics());
        contentMap.put("channelTag", temp.getChannelTag());
        contentMap.put("flag", temp.getFlag());
        contentMap.put("instructions", temp.getInstructions());
        contentMap.put("parameter", temp.getParameter());
        contentMap.put("appkey", temp.getAppkey());
        contentMap.put("appid", temp.getAppid());
        contentMap.put("moduleData", temp.getModuleData());
        contentMap.put("moduleVersion", temp.getModuleVersion());
        contentMap.put("company", temp.getCompany());
        contentMap.put("crNum", temp.getCrNum());

        contentMap.put("appType", temp.getAppType());
        contentMap.put("icons", Base64.encodeBase64String(temp.getIcons()));

        // 华为渠道sdk参数
        JSONArray array = new JSONArray();

        JSONObject huawei = new JSONObject();
        huawei.put("fileName", temp.getFileName());
        huawei.put("filePath", temp.getFilePath());
        huawei.put("fileContent", temp.getFileContent());
        array.add(huawei);
        contentMap.put("sdkFiles", array);

        String placeholders = "";
        //pay;mi;AppId;2882303761519855362#pay;mi;AppKey;5671985587362#tj;umeng;UMENG_APPKEY;60614246b8c8d45c13b419c5#
        String moduleData = temp.getModuleData();

        String ssql = "SELECT moduleType,moduleName,moduleSdkKey FROM dnwx_client.wbgui_module a where  a.moduleSingleid  in ("+ad.substring(0, ad.length() -1)+")  ";
        List<WbguiModule> queryListBean = adService.queryListBean(ssql,WbguiModule.class);

        for (String  module: moduleData.split("#")) {
            String[] modArr = module.split(";");
            if(modArr.length > 2 ){
                String moduleType = modArr[0];
                String moduleName = modArr[1];
                String old = modArr[2];
                String place = "" ;

                WbguiModule WbguiModules = queryListBean.stream().filter(e -> moduleName.equals(e.getModuleName()) && moduleType.equals(e.getModuleType())).findFirst().orElse(null);
                if(null == WbguiModules ){
                    placeholders += module+"#";
                    continue;
                }

                String moduleSdkKey = WbguiModules.getModuleSdkKey();

                if(!BlankUtils.checkBlank(moduleSdkKey)){
                    String[] SdkKey = moduleSdkKey.split("#");
                    for (int i = 0; i < SdkKey.length; i++) {
                        if(old.equals(SdkKey[i].split(";")[0])){
                            if(!SdkKey[i].endsWith(";")){
                                place = SdkKey[i].split(";")[SdkKey[i].split(";").length-1];
                                break;
                            }

                        }
                    }
                }
                if(!BlankUtils.checkBlank(old) && !BlankUtils.checkBlank(place)){
                    module = module.replace(old, place);
                }
            }
            placeholders += module+"#";
        }

        contentMap.put("moduleData", placeholders);
        //新增临时模块可用与不可用状态 默认为可用状态
        boolean canPack = true;
        String packReason = "";
        String tempModIdsql = "SELECT tmp_mod_id from dnwx_client.wbgui_formconfig  where IFNULL(tmp_mod_id,'') <>'' and (singleid = '"+WbguiFormconfig.getSingleid()+"' or pjId = '"+WbguiFormconfig.getSingleid()+"')";
        List<String> tempModIdList = adMapper.queryListString(tempModIdsql);
        if (tempModIdList.size()>0){
            String temp_mods = "";
            StringBuffer stringBuffer = new StringBuffer();
            for (String str:tempModIdList){
                //切割多个
                String[] temp_mod_strs = str.split(";");
                for (String s:temp_mod_strs){
                    if (!BlankUtils.checkBlank(s)){
                        stringBuffer.append("'").append(s).append("'").append(",");
                    }
                }
            }
            temp_mods = stringBuffer.toString();
            if (temp_mods.length()>0){
                temp_mods = temp_mods.substring(0,temp_mods.length()-1);
                String tempModSql = "select state from dnwx_client.wbgui_tempmodule where  state = '1' and singleid in ("+ temp_mods +")";
                List<String> tempModResult = adMapper.queryListString(tempModSql);
                if (tempModResult.size()>0){
                    canPack = false;
                    packReason = "当前需求单使用了已废弃的模块";
                }
            }
        }
        contentMap.put("canPack",canPack);
        contentMap.put("packReason",packReason);
        result.put("data", contentMap);

        return result.toJSONString();
    }
}
