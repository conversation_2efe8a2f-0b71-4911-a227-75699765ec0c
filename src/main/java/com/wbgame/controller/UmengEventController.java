package com.wbgame.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.ocean.rawsdk.ApiExecutor;
import com.alibaba.ocean.rawsdk.client.exception.OceanException;
import com.umeng.uapp.param.UmengUappEventCreateParam;
import com.umeng.uapp.param.UmengUappEventCreateResult;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.AppInfoVo;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.service.SomeService;
import com.wbgame.task.UsertagTask;
import com.wbgame.utils.BlankUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Classname UmengEventController
 * @Description TODO
 * @Date 2021/11/9 10:52
 */
@RequestMapping("umeng")
@RestController
@CrossOrigin
public class UmengEventController {

    private static final Logger logger = LoggerFactory.getLogger(UmengEventController.class);

    private static final String[] FORBIDEN_LIST = {
            "B_popup_page_trigger",
            "B_ta_fail",
            "B_popup_function_electricity",
            "B_popup_home_sense_perform_show",
            "B_battery_change_scene_fail",
            "B_popup_home_sense_trigger",
            "B_home_scene_fail",
            "B_lock_screen_onaction_screenoff",
            "B_popup_home_sense_content_attach",
            "B_popup_home_sense_created",
            "B_power_charge_scene_fail"
    };
    private static final String[] FORBIDEN_MATCH_LIST ={
            "default_load",
    };

    @Autowired
    SomeService someService;

    @Autowired
    RedisTemplate redisTemplate;

    /**
     * 创建自定义事件
     *
     * @param request
     * @return
     */
    @RequestMapping("event/create")
    public Object createEvent(HttpServletRequest request) throws UnsupportedEncodingException {

        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token)) {
            return ReturnJson.toErrorJson("登录token过期,请重新登录");
        }
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return ReturnJson.toErrorJson("登录token过期,请重新登录");
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        List<AppInfoVo> list = someService.selectAppListInfo(new HashMap<>());
        String appid = request.getParameter("appid");
        String eventName = request.getParameter("eventName");
        String eventDisplayName = request.getParameter("eventDisplayName");
        // eventType true 计算事件 false 计数事件
        Boolean eventType = "1".equals(request.getParameter("eventType"));
        Pattern first = Pattern.compile("[a-zA-Z0-9_,\\-()=+!*;@#:%\\[\\]‘\\${}^|~\\n\\r\\t ]{1,255}");
        Pattern  second= Pattern.compile("[a-zA-Z0-9_\u4e00-\u9fa5,\\-()=+!*;@#:%\\[\\]‘\\${}^|~\\n\\r\\t ]{1,255}");

        if (!first.matcher(eventName).matches() || !second.matcher(eventDisplayName).matches() || !StringUtils.isNumeric(request.getParameter("eventType"))) {
            return ReturnJson.toErrorJson("填写的参数不合法,请检查后重试");
        }
        //禁止操作事件
        if(Arrays.asList(FORBIDEN_LIST).contains(eventName)){
            return ReturnJson.toErrorJson("该事件名为禁止创建事件");
        }
        for (String s:FORBIDEN_MATCH_LIST){
            if (eventName.contains(s)){
                return ReturnJson.toErrorJson("该事件名为禁止创建事件");
            }
        }

        UmengUappEventCreateParam param = new UmengUappEventCreateParam();
        AppInfoVo appInfo = list.stream()
                .filter(t -> t.getId().equals(appid) && !BlankUtils.checkBlank(t.getUmeng_key()) && !BlankUtils.checkBlank(t.getUmeng_account()))
                .findFirst()
                .orElse(null);
        if (appInfo == null) {
            return ReturnJson.toErrorJson("选择的产品未配置友盟key或者友盟账号");
        }

        param.setAppkey(appInfo.getUmeng_key());
        param.setEventName(eventName);
        param.setEventDisplayName(URLEncoder.encode(eventDisplayName, "UTF-8"));
        param.setEventType(eventType);

        // 获取友盟账户的执行器
        ApiExecutor apiExecutor = UsertagTask.getUmengAccountExecutor(appInfo.getUmeng_account());
        String result = umengUappEventCreate(apiExecutor, param);
        if (!BlankUtils.checkBlank(result)) {
            JSONObject retJson = JSONObject.parseObject(result);
            if ("0".equals(retJson.getString("status"))) {
                return ReturnJson.success();
            } else {
                return ReturnJson.toErrorJson(retJson.getString("msg"));
            }
        } else {
            return ReturnJson.toErrorJson("创建自定义事件失败,请联系管理员");
        }
    }

    /**
     * 请求友盟创建自定义事件接口
     * @param apiExecutor
     * @param param
     * @return
     */
    public String umengUappEventCreate(ApiExecutor apiExecutor, UmengUappEventCreateParam param) {
        String retString = "";
        try {
            apiExecutor.setServerHost("gateway.open.umeng.com");
            UmengUappEventCreateResult result = apiExecutor.execute(param);
            retString = JSONObject.toJSONString(result);
        } catch (OceanException e) {
            logger.error("umengUappEventCreate:::::errorCode=" + e.getErrorCode() + ", errorMessage=" + e.getErrorMessage());
        }
        return retString;
    }

    /**
     * 批量导入友盟自定义事件
     *
     * @param file
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping("event/createBatch")
    public Object createEventBatch(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {

        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token)) {
            return ReturnJson.toErrorJson("登录token过期,请重新登录");
        }
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (cuser == null) {
            return ReturnJson.toErrorJson("登录token过期,请重新登录");
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String email = request.getParameter("email");
        if (BlankUtils.checkBlank(email)){
            return ReturnJson.toErrorJson("请填写结果提醒人邮箱地址");
        }
        String umeng_account = request.getParameter("umeng_account");
        String operater = cuser.getLogin_name();

        List<String> configList = getEventConfigStringList(file);
        if (configList.size() == 0) {
            return ReturnJson.toErrorJson("上传的配置文件为空");
        }
        someService.asyncCreateUmengEvent(configList,operater,email,umeng_account);
        return ReturnJson.success("请求处理中,稍后在邮件中获取结果,如十分钟以上未收到邮件请联系后台管理员!");
    }


    /**
     * 读取上传友盟自定义事件配置
     *
     * @param file
     * @return
     */
    public static List<String> getEventConfigStringList(MultipartFile file) {
        List<String> list = new ArrayList<>();
        try {
            InputStream ins = null;
            ins = file.getInputStream();
            File toFile = new File(file.getOriginalFilename());

            OutputStream os = new FileOutputStream(toFile);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            FileReader fr = new FileReader(toFile);
            BufferedReader br = new BufferedReader(fr);
            String line = "";
            while ((line = br.readLine()) != null) {
                list.add(line);
            }
            try {
                br.close();
                fr.close();
                os.close();
                ins.close();
                toFile.delete();
            } catch (Exception e) {
                logger.error("getEventConfigStringList close error:", e);
            }
        } catch (Exception e) {
            logger.error("getEventConfigStringList error:", e);
        }
        return list;
    }

}
