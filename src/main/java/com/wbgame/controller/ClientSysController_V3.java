package com.wbgame.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.wbgame.mapper.master.WbSysV3Mapper;
import com.wbgame.pojo.ClientErrorLogVo;
import com.wbgame.pojo.CurrMenuVo;
import com.wbgame.pojo.CurrOrgVo;
import com.wbgame.pojo.CurrRoleVo;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.WbguiUserInfoVo;
import com.wbgame.utils.BlankUtils;

/**
 * 前端系统相关 v3版本
 * <AUTHOR>
 */
@CrossOrigin
@RestController
public class ClientSysController_V3 {
	
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private WbSysV3Mapper wbSysV3Mapper;
	
	
	/**
	 * 查询角色列表
	 * @param cur 当前角色对象
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/wb/clientLogCheck_v3", method = { RequestMethod.GET, RequestMethod.POST })
	public String clientLogCheck_v3(ClientErrorLogVo client,HttpServletRequest request,HttpServletResponse response) {
		
		JSONObject result = new JSONObject();
		try {
			/* 进行token验证 */
//			String token = request.getParameter("token");
//			if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//				return "{\"ret\":2,\"msg\":\"token is error!\"}";
//			else
//				redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
//			CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);
			
			/* 检索角色数据 */
			List<ClientErrorLogVo> list = wbSysV3Mapper.selectClientErrorLog(client);
			// custom自定义字段 转换为json对象返回
			List<ClientErrorLogVo> collect = list.stream().map(act -> {
				if(!BlankUtils.checkBlank(act.getData())){
					String json = new String(Base64.decodeBase64(act.getData()));
					if(BlankUtils.isJSONObject(json))
						act.setDataJson(JSONObject.parseObject(json));
					else
						act.setDataJson(new JSONObject());
					act.setData(null);
				}
				return act;
			}).collect(Collectors.toList());
			
			/* 格式化输出 */
			result.put("ret", 1);
			result.put("msg", "success");
			result.put("data", collect);
			result.put("totalCount", collect.size());
			return result.toJSONString();
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息 : " + e.getMessage());
			return result.toJSONString();
		}
	}
	
	/**
	 * 操作角色配置 新增修改删除
	 * @param cur
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/wb/clientLogSet_v3", method = { RequestMethod.GET, RequestMethod.POST })
	public String clientLogSet_v3(String handle,ClientErrorLogVo client,HttpServletRequest request,HttpServletResponse response) {
		
		JSONObject result = new JSONObject();
		try {
			/* 进行token验证 */
//			String token = request.getParameter("token");
//			if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//				return "{\"ret\":2,\"msg\":\"token is error!\"}";
//			else
//				redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
//			CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);
			
			// 对data 自定义json进行编码存储
			if(!BlankUtils.checkBlank(client.getData())){
				client.setData(Base64.encodeBase64String(client.getData().getBytes()));
			}
			
			/* 根据操作标识符确定操作方式 */
			int res = 0;
			if ("add".equals(handle)) {
//				client.setCuser(cuser.getLogin_name());
//				client.setEuser(cuser.getLogin_name());
				res = wbSysV3Mapper.insertClientErrorLog(client);
				
			} else if ("edit".equals(handle)) {
				res = wbSysV3Mapper.updateClientErrorLog(client);
				
			} else if ("del".equals(handle)) {
				res = wbSysV3Mapper.deleteClientErrorLog(client);
			} else {
				return "{\"ret\":0,\"msg\":\"handle is error!\"}";
			}
			
			/* 格式化输出 */
			result.put("ret", res);
			result.put("msg", "success");
			return result.toJSONString();
			
		} catch (Exception e) {
			e.printStackTrace();
			
			if(e.toString().contains("Duplicate entry")) {
				result.put("ret", 0);
				result.put("msg", "错误信息 : 标识已存在!");
			}else{
				result.put("ret", 0);
				result.put("msg", "错误信息 : " + e.getMessage());
			}
			return result.toJSONString();
		}
	}
	
}
