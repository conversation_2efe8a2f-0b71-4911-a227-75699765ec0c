package com.wbgame.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.pojo.AdTotalHourVo;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.custom.AppDurationErrorVo;
import com.wbgame.pojo.custom.CustomKeepUserVo;
import com.wbgame.pojo.custom.CustomStatsVo;
import com.wbgame.pojo.custom.CustomTotalStatsVo;
import com.wbgame.pojo.custom.GDTWorkReportVo;
import com.wbgame.pojo.custom.PartnerStatsVo;
import com.wbgame.service.AdService;
import com.wbgame.service.CustomService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;
import com.wbgame.utils.JxlUtil;

import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

/**
 * 微信小游戏统计相关查询
 * <AUTHOR>
 */
@Controller
public class CustomAppController {
	
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private CustomService customService;
	@Autowired
	private AdService adService;
	
	
	@CrossOrigin
	@RequestMapping(value="/custom/getAppList", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getAppList(HttpServletRequest request,HttpServletResponse response){
		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String token = request.getParameter("token");
		
		// token验证
		if(BlankUtils.checkBlank(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);
		if(cuser == null){
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		}else{
			redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
		}
		
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			end_date = DateTime.now().toString("yyyy-MM-dd");
		}
		
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		// 不为管理员用户 则添加应用控制
		if(!"root".equals(CommonUtil.userMap.get(cuser.getLogin_name()).getOrg_id())){
			paramMap.put("appid", CommonUtil.userMap.get(cuser.getLogin_name()).getApp_group());
		}
		
		JSONObject result = new JSONObject();
		try {
			List<CustomTotalStatsVo> totalList = customService.selectCustomStatsTotalInfoThree(paramMap);
			if(totalList != null && totalList.size() == 2){
				result.put("yesterday", totalList.get(0));
				result.put("today", totalList.get(1));
			}else{
				JSONObject def = new JSONObject();
				def.put("click_num", 0);
				def.put("click_user_num", 0);
				def.put("open_num", 0);
				def.put("open_user_num", 0);
				def.put("add_num", 0);
				def.put("all_add_num", 0);
				JSONObject def2 = (JSONObject)def.clone();
				// json序列化后，相同的对象只是做了个指向内存地址，需要新开的内存对象
				
				result.put("yesterday", def);
				result.put("today", def2);
			}
			
			JSONArray array = new JSONArray();
			// 今天的数据
			paramMap.put("start_date", end_date);
			paramMap.put("end_date", end_date);
			List<CustomTotalStatsVo> toList = customService.selectCustomStatsTotalInfoTwo(paramMap);
			
			// 昨天的数据
			paramMap.put("start_date", start_date);
			paramMap.put("end_date", start_date);
			List<CustomTotalStatsVo> yesterList = customService.selectCustomStatsTotalInfoTwo(paramMap);
			for (CustomTotalStatsVo cust : toList) {
				JSONObject object = JSONObject.parseObject(JSONObject.toJSONString(cust));
				object.put("yes_click_num", 0);
				object.put("yes_click_user_num", 0);
				object.put("yes_open_num", 0);
				object.put("yes_open_user_num", 0);
				object.put("yes_add_num", 0);
				object.put("yes_all_add_num", 0);
				object.put("yes_per_rate", "0");
				
				// 将昨天的数据拼入今天相同的appid中
				for (CustomTotalStatsVo yester : yesterList) {
					if(cust.getAppid().equals(yester.getAppid())){
						object.put("yes_click_num", yester.getClick_num());
						object.put("yes_click_user_num", yester.getClick_user_num());
						object.put("yes_open_num", yester.getOpen_num());
						object.put("yes_open_user_num", yester.getOpen_user_num());
						object.put("yes_add_num", yester.getAdd_num());
						object.put("yes_all_add_num", yester.getAll_add_num());
						object.put("yes_per_rate", yester.getPer_rate());
						break;
					}
				}
				array.add(object);
			}
			
			result.put("ret", 1);
			result.put("data", array);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		return result.toJSONString();
	}

	@CrossOrigin
	@RequestMapping(value="/custom/getAppList/v3", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getAppListV3(HttpServletRequest request,HttpServletResponse response){
		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String token = request.getParameter("token");

		// token验证
		CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);
		if (cuser == null){
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		}else{
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			end_date = DateTime.now().toString("yyyy-MM-dd");
		}

		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		// 不为管理员用户 则添加应用控制
		if (!"root".equals(cuser.getRole_id())){
			paramMap.put("appid",cuser.getLittle_group());
		}

		JSONObject result = new JSONObject();
		try {
			List<CustomTotalStatsVo> totalList = customService.selectCustomStatsTotalInfoThree(paramMap);
			if(totalList != null && totalList.size() == 2){
				result.put("yesterday", totalList.get(0));
				result.put("today", totalList.get(1));
			}else{
				JSONObject def = new JSONObject();
				def.put("click_num", 0);
				def.put("click_user_num", 0);
				def.put("open_num", 0);
				def.put("open_user_num", 0);
				def.put("add_num", 0);
				def.put("all_add_num", 0);
				JSONObject def2 = (JSONObject)def.clone();
				// json序列化后，相同的对象只是做了个指向内存地址，需要新开的内存对象

				result.put("yesterday", def);
				result.put("today", def2);
			}

			JSONArray array = new JSONArray();
			// 今天的数据
			paramMap.put("start_date", end_date);
			paramMap.put("end_date", end_date);
			List<CustomTotalStatsVo> toList = customService.selectCustomStatsTotalInfoTwo(paramMap);

			// 昨天的数据
			paramMap.put("start_date", start_date);
			paramMap.put("end_date", start_date);
			List<CustomTotalStatsVo> yesterList = customService.selectCustomStatsTotalInfoTwo(paramMap);
			for (CustomTotalStatsVo cust : toList) {
				JSONObject object = JSONObject.parseObject(JSONObject.toJSONString(cust));
				object.put("yes_click_num", 0);
				object.put("yes_click_user_num", 0);
				object.put("yes_open_num", 0);
				object.put("yes_open_user_num", 0);
				object.put("yes_add_num", 0);
				object.put("yes_all_add_num", 0);
				object.put("yes_per_rate", "0");

				// 将昨天的数据拼入今天相同的appid中
				for (CustomTotalStatsVo yester : yesterList) {
					if(cust.getAppid().equals(yester.getAppid())){
						object.put("yes_click_num", yester.getClick_num());
						object.put("yes_click_user_num", yester.getClick_user_num());
						object.put("yes_open_num", yester.getOpen_num());
						object.put("yes_open_user_num", yester.getOpen_user_num());
						object.put("yes_add_num", yester.getAdd_num());
						object.put("yes_all_add_num", yester.getAll_add_num());
						object.put("yes_per_rate", yester.getPer_rate());
						break;
					}
				}
				array.add(object);
			}

			result.put("ret", 1);
			result.put("data", array);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		return result.toJSONString();
	}

	
	@CrossOrigin
	@RequestMapping(value="/wb/getPartnerNameList", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getPartnerNameList(CurrUserVo cu, HttpServletRequest request,HttpServletResponse response){
		
		JSONObject result = new JSONObject();
		try {
			List<Map<String, Object>> list = customService.selectPartnerNameList();
			result.put("ret", 1);
			result.put("data", list);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		return result.toJSONString();
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/getAppTrend", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getAppTrend(HttpServletRequest request,HttpServletResponse response){
		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appid = request.getParameter("appid");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		
		if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
			sdate = DateTime.now().minusDays(7).toString("yyyy-MM-dd");
			edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
		
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("appid", appid);
		paramMap.put("start_date", sdate);
		paramMap.put("end_date", edate);
		
		JSONObject result = new JSONObject();
		try {
			List<CustomTotalStatsVo> ctList = customService.selectCustomStatsTotalInfoTwo(paramMap);
			result.put("ret", 1);
			result.put("data", ctList);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		return result.toJSONString();
	}
	@CrossOrigin
	@RequestMapping(value="/custom/getAppKeep", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getAppKeep(HttpServletRequest request,HttpServletResponse response){
		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appid = request.getParameter("appid");

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
			sdate = DateTime.now().minusDays(7).toString("yyyy-MM-dd");
			edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
		
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("appid", appid);
		paramMap.put("start_date", sdate);
		paramMap.put("end_date", edate);
		
		JSONObject result = new JSONObject();
		try {
			List<CustomKeepUserVo> ctList = customService.selectCustomStatsKeepInfo(paramMap);
			result.put("ret", 1);
			result.put("data", ctList);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		return result.toJSONString();
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/getAppChannelTrend", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getAppChannelTrend(HttpServletRequest request,HttpServletResponse response){
		String tdate = request.getParameter("tdate");
		String appid = request.getParameter("appid");
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		if(BlankUtils.checkBlank(tdate))
			tdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		if(BlankUtils.checkBlank(appid))
			appid = "20065";
		
		if(tdate.split(",").length > 1){ // 多个日期
			tdate = String.join("','", tdate.split(","));
		}
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("appid", appid);
		paramMap.put("tdate", tdate);
		
		JSONObject result = new JSONObject();
		try {
			// 以日期tdate为key，集合数据为value，集合只获取前5个
			List<CustomTotalStatsVo> custList = customService.selectCustomStatsTotalInfo(paramMap);
			for (CustomTotalStatsVo cust : custList) {
				List<CustomTotalStatsVo> list = (List<CustomTotalStatsVo>)result.get(cust.getTdate());
				if(list == null || list.size() == 0){
					list = new ArrayList<CustomTotalStatsVo>();
				}else if(list.size() == 5){
					continue;
				}
				
				list.add(cust);
				result.put(cust.getTdate(), list);
			}
			
			result.put("ret", 1);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		return result.toJSONString();
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/getDurationTimes", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getDurationTimes(HttpServletRequest request,HttpServletResponse response){
		String tdate = request.getParameter("tdate");
		String appid = request.getParameter("appid");
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		if(BlankUtils.checkBlank(tdate))
			tdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		if(BlankUtils.checkBlank(appid))
			appid = "20065";
		
		if(tdate.split(",").length > 1){ // 多个日期
			tdate = String.join("','", tdate.split(","));
		}
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("appid", appid);
		paramMap.put("tdate", tdate);
		
		JSONObject result = new JSONObject();
		try {
			List<Map<String, Object>> mapList = customService.selectAppDurationTimes(paramMap);
			for (Map<String, Object> map : mapList) {
				List<Map<String, Object>> list = (List<Map<String, Object>>)result.get(map.get("tdate").toString());
				if(list == null || list.size() == 0)
					list = new ArrayList<Map<String, Object>>();
				
				list.add(map);
				result.put(map.get("tdate").toString(), list);
			}
			
			result.put("ret", 1);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		return result.toJSONString();
	}
	@CrossOrigin
	@RequestMapping(value="/custom/getDurationGameTimes", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getDurationGameTimes(HttpServletRequest request,HttpServletResponse response){
		String tdate = request.getParameter("tdate");
		String appid = request.getParameter("appid");
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		if(BlankUtils.checkBlank(tdate))
			tdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		if(BlankUtils.checkBlank(appid))
			appid = "20065";
		
		
		if(tdate.split(",").length > 1){ // 多个日期
			tdate = String.join("','", tdate.split(","));
		}
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("appid", appid);
		paramMap.put("tdate", tdate);
		
		JSONObject result = new JSONObject();
		try {
			List<Map<String, Object>> mapList = customService.selectAppDurationGameTimes(paramMap);
			for (Map<String, Object> map : mapList) {
				List<Map<String, Object>> list = (List<Map<String, Object>>)result.get(map.get("tdate").toString());
				if(list == null || list.size() == 0)
					list = new ArrayList<Map<String, Object>>();
				
				list.add(map);
				result.put(map.get("tdate").toString(), list);
			}
			
			result.put("ret", 1);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		return result.toJSONString();
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/getDurationErrorLog", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getDurationErrorLog(HttpServletRequest request,HttpServletResponse response){
		String tdate = request.getParameter("tdate");
		String appid = request.getParameter("appid");
		String lsn = request.getParameter("lsn");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// pageStart 和 limit设置值，当前页
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		if(BlankUtils.checkBlank(tdate))
			tdate = DateTime.now().toString("yyyy-MM-dd");
		
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("tdate", tdate);
		paramMap.put("appid", appid);
		paramMap.put("lsn", lsn);
		
		JSONObject result = new JSONObject();
		try {
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<AppDurationErrorVo> errorList = customService.selectAppDurationErrorLog(paramMap);
			long size = ((Page) errorList).getTotal();
			
			result.put("ret", 1);
			result.put("data", errorList);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		return result.toJSONString();
	}
	
	
	@CrossOrigin
	@RequestMapping(value="/custom/getCustomStats", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getCustomStats(HttpServletRequest request,HttpServletResponse response){
		String pStart = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String appid = request.getParameter("tj_appid");
		String pid = request.getParameter("tj_pid");
		String eventid = request.getParameter("tj_eventid");
		String label = request.getParameter("tj_label");
		String label_name = request.getParameter("tj_label_name");
		String start_date = request.getParameter("tj_start_date");
		String end_date = request.getParameter("tj_end_date");
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		
		// pageStart 和 limit设置值
		int pageStart = "".equals(pStart) == true ? 0 : Integer.parseInt(pStart);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		// 当前页
		int pageNo = (pageStart / pageSize) + 1;
		
		Map<String, Object> paramMap = new HashMap<String, Object>();
		String today = DateTime.now().toString("yyyyMMdd");
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = today;
			end_date = today;
		}
		
		String ids = "'888888'";
		String regex = "^[A-Za-z0-9_-]+$";
		if(!BlankUtils.checkBlank(label) && !label.matches(regex)){ // 即为中文时替换
			Iterator<Entry<String, Map<String, Object>>> iterator = CommonUtil.gMap.entrySet().iterator();
			while (iterator.hasNext()) {
				Entry<String, Map<String, Object>> next = iterator.next();
				if(label.equals(next.getValue().get("game_name"))){
					// 将中文名称替换成ID
					ids = ids +",'"+ next.getValue().get("id")+"'";
				}
			}
			paramMap.put("ids", ids);
		}else{
			paramMap.put("label", label);
		}
		
		paramMap.put("appid", appid);
		paramMap.put("pid", pid);
		paramMap.put("eventid", eventid);
		paramMap.put("label_name", label_name);
		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		
		JSONObject result = new JSONObject();
		try {
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<CustomStatsVo> list = customService.selectCustomStatsInfo(paramMap);
			long size = ((Page) list).getTotal();
			for (CustomStatsVo cust : list) {
				cust.setTdate(cust.getTdate().replace("-", ""));
				
				Map<String, Object> map = CommonUtil.gMap.get(cust.getLabel());
				// label为ID，替换成名称
				if(map != null && map.size() > 0){
					cust.setLabel(map.get("game_name")+"");
				}
			}
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		
		return result.toJSONString();
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/getCustomRetained", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getCustomRetained(HttpServletRequest request,HttpServletResponse response){
		String pStart = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String appid = request.getParameter("tj_appid");
		String pid = request.getParameter("tj_pid");
		String start_date = request.getParameter("tj_start_date");
		String end_date = request.getParameter("tj_end_date");
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		
		// pageStart 和 limit设置值
		int pageStart = "".equals(pStart) == true ? 0 : Integer.parseInt(pStart);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		String day = DateTime.now().minusDays(2).toString("yyyyMMdd");
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = day;
			end_date = day;
		}
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("appid", appid);
		paramMap.put("pid", pid);
		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		
		JSONObject result = new JSONObject();
		try {
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<CustomKeepUserVo> list = customService.selectCustomRetainedTwo(paramMap);
			long size = ((Page) list).getTotal();
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		
		return result.toJSONString();
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/getPartnerStatsKeep", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getPartnerStatsKeep(HttpServletRequest request,HttpServletResponse response){
		String pStart = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String appid = request.getParameter("tj_appid");
		String pid = request.getParameter("tj_pid");
		String channel = request.getParameter("tj_channel");
		String start_date = request.getParameter("tj_start_date");
		String end_date = request.getParameter("tj_end_date");
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		
		// pageStart 和 limit设置值
		int pageStart = "".equals(pStart) == true ? 0 : Integer.parseInt(pStart);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		String today = DateTime.now().minusDays(2).toString("yyyyMMdd");
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = today;
			end_date = today;
		}
		
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("appid", appid);
		paramMap.put("channel", channel);
		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		paramMap.put("pid", pid);
		
		JSONObject result = new JSONObject();
		try {
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<PartnerStatsVo> list = customService.selectPartnerKeepInfo(paramMap);
			long size = ((Page) list).getTotal();
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		
		return result.toJSONString();
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/getPartnerStatsInfo", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getPartnerStats(HttpServletRequest request,HttpServletResponse response){
		String pStart = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String appid = request.getParameter("tj_appid");
		String pid = request.getParameter("tj_pid");
		String channel = request.getParameter("tj_channel");
		String start_date = request.getParameter("tj_start_date");
		String end_date = request.getParameter("tj_end_date");
		String token = request.getParameter("token");
		
		// token验证
		if(BlankUtils.checkBlank(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);
		if(cuser == null){
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		}else{
			redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
		}
		// 当前缓存中该用户的实时权限
		CurrUserVo currUserVo = CommonUtil.userMap.get(cuser.getLogin_name());
		
		// pageStart 和 limit设置值
		int pageStart = "".equals(pStart) == true ? 0 : Integer.parseInt(pStart);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		String today = DateTime.now().toString("yyyyMMdd");
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = today;
			end_date = today;
		}
		
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("appid", appid);
		paramMap.put("channel", channel);
		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		paramMap.put("pid", pid);
		
		paramMap.put("table_name", "app_partner_info_total");
		
		Map<String, Map<String, Object>> createrMap = null;
		if(!"root".equals(currUserVo.getOrg_id())){ // 非管理员组
			// 获取到该用户的所有pid信息，如没有则直接返回
			createrMap = customService.selectPartnerByCreater(currUserVo.getLogin_name());
			if(createrMap != null && createrMap.size() != 0){
				// 只查询本人创建的pid
				List<String> pidList = new ArrayList<String>(createrMap.keySet());
				if(BlankUtils.checkBlank(pid) || !pidList.contains(pid)){
					String allpid = "'"+String.join("','", pidList)+"'";
					paramMap.put("allpid", allpid);
					paramMap.remove("pid");
				}
			}else{
				return "{\"ret\":0,\"totalCount\":0,\"data\":[]}";
			}
		}
		
		JSONObject result = new JSONObject();
		try {
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<PartnerStatsVo> list = customService.selectPartnerStatsInfo(paramMap);
			long size = ((Page) list).getTotal();
			
			if(!"root".equals(currUserVo.getOrg_id()) && list != null){
				for (PartnerStatsVo ps : list) {
					ps.setAdd_num(ps.getBus_num());
				}
			}
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		return result.toJSONString();
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/exportPartnerStatsInfo", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody void exportPartnerStats(HttpServletRequest request, HttpServletResponse response){

		String fileName = DateTime.now().toString("yyyyMMddHHmmssSS")+".xls";
		File file = new File(request.getRealPath("/"), fileName);
		try {
			String appid = request.getParameter("tj_appid");
			String pid = request.getParameter("tj_pid");
			String channel = request.getParameter("tj_channel");
			String start_date = request.getParameter("tj_start_date");
			String end_date = request.getParameter("tj_end_date");
			String token = request.getParameter("token");
			
			// token验证
			if(BlankUtils.checkBlank(token))
				return ;
			CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);
			if(cuser == null){
				return ;
			}else{
				redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
			}
			// 当前缓存中该用户的实时权限
			CurrUserVo currUserVo = CommonUtil.userMap.get(cuser.getLogin_name());
			
			String today = DateTime.now().toString("yyyyMMdd");
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = today;
				end_date = today;
			}
			
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("appid", appid);
			paramMap.put("pid", pid);
			paramMap.put("channel", channel);
			paramMap.put("start_date", start_date);
			paramMap.put("end_date", end_date);
			
			paramMap.put("table_name", "app_partner_info_total");
			
			Map<String, Map<String, Object>> createrMap = null;
			if(!"root".equals(currUserVo.getOrg_id())){ // 非管理员组
				// 获取到该用户的所有pid信息，如没有则直接返回
				createrMap = customService.selectPartnerByCreater(currUserVo.getLogin_name());
				if(createrMap != null && createrMap.size() != 0){
					List<String> pidList = new ArrayList<String>(createrMap.keySet());
					if(BlankUtils.checkBlank(pid) || !pidList.contains(pid)){
						String allpid = "'"+String.join("','", pidList)+"'";
						paramMap.put("allpid", allpid);
						paramMap.remove("pid");
					}
				}
			}
			
			List<PartnerStatsVo> list = customService.selectPartnerStatsInfo(paramMap);
			if(!"root".equals(currUserVo.getOrg_id()) && list != null){
				for (PartnerStatsVo ps : list) {
					ps.setAdd_num(ps.getBus_num());
				}
			}

			WritableWorkbook wwb = Workbook.createWorkbook(file);
        	//创建Excel工作表               创建Excel表的左下角表名(名称，第几个)
	        WritableSheet sheet = wwb.createSheet("渠道新增详情", 0);//创建sheet
            //ws.mergeCells(0, 0, 2, 1);//合并单元格(左列，左行，右列，右行)从第1行第1列到第2行第3列
            
	        String[] title={"日期","APPID","渠道","PID","新增数"};
	        
            for(int i = 0; i < title.length; i++){//添加标题
            	Label l = new Label(i, 0, title[i], JxlUtil.getTitle());
            	sheet.setColumnView(i, 20);
            	sheet.addCell(l);
            }
            	
            for(int k = 0; k < list.size(); k++){ // 每一行的内容
            	String[] vals = {
            			list.get(k).getTdate(),
            			list.get(k).getAppid(),
            			list.get(k).getChannel(),
            			list.get(k).getPid(),
            			list.get(k).getAdd_num()+""
            	};

            	for(int m = 0; m < title.length; m++){ // 每一列的内容，从第二行开始插入
            		Label l = new Label(m, k+1, vals[m], JxlUtil.getNormolCell());
            		sheet.addCell(l);
            	}
            }
	     /*ws.setColumnView(0, 20);//设置列宽
	       ws.setRowView(0, 400);//设置行高 */
           wwb.write();
           wwb.close();

		} catch (Exception e){
			e.printStackTrace();
			fileName = "无数据.xls";
		}
		
		response.addHeader("Cache-Control","no-cache");
		response.addDateHeader("Expries",0);
		response.setContentType("application/vnd.ms-excel;charset=utf-8");
		response.addHeader("Content-Disposition","attachment;filename=" + fileName);
		OutputStream pw = null;
		FileInputStream input = null;
		try {
			pw = response.getOutputStream();
			input = new FileInputStream(file);
			int length = 0;
			byte buffer[] = new byte[2048];
			while((length = input.read(buffer)) != -1){
				pw.write(buffer, 0, length);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			try {
				if(pw != null) {
					pw.close();
				}
				if(input != null) {
					input.close();
				}
				file.delete();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 微信小游戏 新增汇总
	 * @param gdt
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@CrossOrigin
	@RequestMapping(value="/custom/selectAppNewTotal", method={RequestMethod.GET, RequestMethod.POST})
	public @ResponseBody String selectAppNewTotal(GDTWorkReportVo gdt,HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		
		String start_date = BlankUtils.checkNull(request, "beginDt");
        String end_date = BlankUtils.checkNull(request, "endDt");
        String pid = BlankUtils.checkNull(request, "pid"); // 渠道标识
        String channel = BlankUtils.checkNull(request, "channel"); // 渠道名称
        String appid = BlankUtils.checkNull(request, "appid"); // 产品类型
        String moreflag = BlankUtils.checkNull(request, "moreflag"); // 传1显示历史数据，默认传0不显示
		
		// token验证
//		String token = request.getParameter("token");
//		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//			return "{\"ret\":2,\"msg\":\"token is error!\"}";
//		else
//			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		JSONObject result = new JSONObject();
		try {
			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(start_date) 
					|| BlankUtils.sqlValidate(end_date)
					|| BlankUtils.sqlValidate(appid)
					|| BlankUtils.sqlValidate(pid)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}
			String yesterday = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = yesterday;
				end_date = yesterday;
			}
			
			String where = "";
			if(!BlankUtils.checkBlank(appid))
				where += " and appid="+appid;
			if(!BlankUtils.checkBlank(pid))
				where += " and pid='"+pid+"'";
			if(!BlankUtils.checkBlank(channel))
				where += " and channel='"+channel+"'";
			
			if("0".equals(moreflag)) { // 剔除掉历史数据
				String pro = "select concat(appid,cid) mapkey from wx_channel_manage";
				List<String> providers = adService.queryListString(pro);
				
				where += " and CONCAT(appid,pid) in ('"+String.join("','", providers)+"')";
			}
			
			String sql = "select concat(tdate,'') dt,appname appName,appid,pid,channel,"
							+"open_user_num actNum,add_num newNum"
					+ " from app_custom_stats_total"
					+ " where tdate BETWEEN '"+start_date+"' AND '"+end_date+"' "+ where
					+ " order by dt desc,newNum desc";
			
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<Map<String, Object>> list = customService.queryListMap(sql);
			long size = ((Page) list).getTotal();
			
			
			String query = "select concat(appid,cid) mapkey,cname,appname from wx_channel_manage";
			Map<String, Map<String, Object>> queryMap = adService.queryListMapOfKey(query);
			String query2 = "select concat(tdate,b.appid,channel) mapkey,actnum,addnum from toutiao_app_info a,wx_channel_manage b "+
					 "where a.tdate BETWEEN '"+start_date+"' AND '"+end_date+"' and a.ttappid=b.ttappid";
			Map<String, Map<String, Object>> ttMap = adService.queryListMapOfKey(query2);
			
			list.forEach(act -> {
				Map<String, Object> chMap = queryMap.get(act.get("appid")+""+act.get("pid"));
				if(chMap != null) {
					act.put("channel", chMap.get("cname")+"");
					act.put("typeName", chMap.get("appname")+"");
				}
				
				// 根据 日期-应用-渠道 匹配，给头条、vivo等赋值活跃新增
				Map<String, Object> tMap = ttMap.get(act.get("dt")+""+act.get("appid")+act.get("pid"));
				if(tMap != null) {
					act.put("ttAct", tMap.get("actnum")+"");
					act.put("ttAdd", tMap.get("addnum")+"");
				}
			});
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}
	@CrossOrigin
	@RequestMapping(value="/custom/exportAppNewTotal", method = RequestMethod.POST)
	public void exportProductModel(HttpServletRequest request,HttpServletResponse response) {
		String start_date = BlankUtils.checkNull(request, "beginDt");
        String end_date = BlankUtils.checkNull(request, "endDt");
        String pid = BlankUtils.checkNull(request, "pid"); // 项目ID
        String channel = BlankUtils.checkNull(request, "channel"); // 渠道名称
        String appid = BlankUtils.checkNull(request, "appid"); // 产品类型
        String moreflag = BlankUtils.checkNull(request, "moreflag"); // 传1显示历史数据，默认传0不显示
		
        if(BlankUtils.sqlValidate(start_date) 
				|| BlankUtils.sqlValidate(end_date)
				|| BlankUtils.sqlValidate(appid)
				|| BlankUtils.sqlValidate(pid)){
			return ;
		}
		String yesterday = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = yesterday;
			end_date = yesterday;
		}
		
		String where = "";
		if(!BlankUtils.checkBlank(appid))
			where += " and appid="+appid;
		if(!BlankUtils.checkBlank(pid))
			where += " and pid='"+pid+"'";
		if(!BlankUtils.checkBlank(channel))
			where += " and channel='"+channel+"'";
		
		if("0".equals(moreflag)) { // 剔除掉历史数据
			String pro = "select concat(appid,cid) mapkey from wx_channel_manage";
			List<String> providers = adService.queryListString(pro);
			
			where += " and CONCAT(appid,pid) in ('"+String.join("','", providers)+"')";
		}
		
		String sql = "select concat(tdate,'') dt,appname appName,appid,pid,"
						+"open_user_num actNum,add_num newNum"
				+ " from app_custom_stats_total"
				+ " where tdate BETWEEN '"+start_date+"' AND '"+end_date+"' "+ where
				+ " order by dt desc,newNum desc";
		
		List<Map<String, Object>> contentList = customService.queryListMap(sql);
		
		String query = "select concat(appid,cid) mapkey,cname,appname from wx_channel_manage";
		Map<String, Map<String, Object>> queryMap = adService.queryListMapOfKey(query);
		String query2 = "select concat(tdate,ttappid,'tt') mapkey,actnum,addnum from toutiao_app_info";
		Map<String, Map<String, Object>> ttMap = adService.queryListMapOfKey(query2);
		
		contentList.forEach(act -> {
			Map<String, Object> chMap = queryMap.get(act.get("appid")+""+act.get("pid"));
			if(chMap != null) {
				act.put("channel", chMap.get("cname")+"");
				act.put("typeName", chMap.get("appname")+"");
			}
			
			// 头条活跃新增赋值
			Map<String, Object> tMap = ttMap.get(act.get("dt")+""+act.get("appid")+act.get("pid"));
			if(tMap != null) {
				act.put("ttAct", tMap.get("actnum")+"");
				act.put("ttAdd", tMap.get("addnum")+"");
			}
		});
		
		//数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		headerMap.put("dt","日期");
		headerMap.put("channel","渠道名称");
		headerMap.put("pid","项目标识");
		headerMap.put("appName","产品类型");
		headerMap.put("newNum","渠道新增");
		headerMap.put("ttAdd","平台新增");
		headerMap.put("actNum","渠道活跃");
		headerMap.put("ttAct","平台活跃");
		headerMap.put("typeName","应用名称");
		
		Map<String, String> inMap = new LinkedHashMap<String, String>();  
		inMap.put("strDt", "yyyy-MM-dd");
		
		String fileName = "新增汇总查询_"+DateTime.now().toString("yyyyMMddHHmmss")+".xls";
		JxlUtil.doSave(fileName,headerMap,contentList,inMap,null,null,request,response); 
	}
	
	/**
	 * 实时数据报表
	 * @param request
	 * @param response
	 * @return
	 */
	@CrossOrigin
	@RequestMapping(value="/custom/selectAppTotalHour", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String selectAppTotalHour(HttpServletRequest request,HttpServletResponse response){
		String tdate = request.getParameter("tdate");
		String adtype = request.getParameter("adtype");
		String appid = request.getParameter("appid");
		String provider = request.getParameter("provider");
		
		JSONObject result = new JSONObject();
		try {
			/*if(BlankUtils.checkBlank(tdate) || BlankUtils.checkBlank(adtype)
					|| BlankUtils.checkBlank(ad_pos_type)){
				
				tdate = DateTime.now().toString("yyyyMMdd");
				adtype = "1";
				ad_pos_type = "banner";
			}*/
			
			List<String> array = new ArrayList<String>();
			for (int i = 0; i < 24; i++) {
				if(i < 10)
					array.add("sum(hour0"+i+") as hour0"+i);
				else
					array.add("sum(hour"+i+") as hour"+i);
			}
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("array", array);
			paramMap.put("tdate", tdate);
			paramMap.put("adtype", adtype);
			paramMap.put("appid", appid);
			paramMap.put("provider", provider);
			
			String sql = "select appid,provider,"+String.join(",", array)+" from app_total_hour"
					+" where tdate = '"+tdate+"' and adtype = "+adtype;
			if(!BlankUtils.checkBlank(appid))
				sql += " and appid = "+appid+" and provider = '"+provider+"'";
			
			List<Map<String, Object>> list = customService.queryListMap(sql);
			
			result.put("ret", 1);
			result.put("data", list);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}
	
}
