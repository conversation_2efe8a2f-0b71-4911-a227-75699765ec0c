package com.wbgame.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Iterables;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.clientRefund.ClientRefundVo;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.service.ClientRefundService;
import com.wbgame.utils.BlankUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import com.wbgame.utils.excel.POIExcelUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户退款金额查询修改接口
 * <AUTHOR>
 * @createDate 2023/2/15
 * @class ClientRefundController
 */
@CrossOrigin
@RestController
@RequestMapping("/advert/clientRefund")
public class ClientRefundController {
    @Autowired
    ClientRefundService clientRefundService;

    /**
     * 查询用户退款金额
     *
     * @param request
     * @return
     */
    @PostMapping("/get")
    public String getClientRefund(HttpServletRequest request) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        // pageStart 和 limit设置值
        int pageSize = "".equals(limit) ? 100 : Integer.parseInt(limit);
        int pageStart = (("".equals(start) ? 0 : Integer.parseInt(start)) - 1) * pageSize + 1;
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date startDate = (request.getParameter("start_date") != null && !"".equals(request.getParameter("start_date"))) ? simpleDateFormat.parse(request.getParameter("start_date")) : null;
            Date endDate = (request.getParameter("end_date") != null && !"".equals(request.getParameter("end_date"))) ? simpleDateFormat.parse(request.getParameter("end_date")) : null;
            String appids = request.getParameter("appid");
            String order_str = "tdate".equals(request.getParameter("order_str")) ? "" : request.getParameter("order_str");
            String[] appid = null, chaId = null, payType = null;
            if (appids != null && !"".equals(appids)) {
                appid = appids.split(",");
            }
            String chaIds = request.getParameter("chaid");
            if (chaIds != null && !"".equals(chaIds)) {
                chaId = chaIds.split(",");
            }
            String payTypes = request.getParameter("paytype");
            if (payTypes != null && !"".equals(payTypes)) {
                payType = payTypes.split(",");
            }

            PageHelper.startPage(pageNo, pageSize);
            List<ClientRefundVo> clientRefundVos = (List<ClientRefundVo>) clientRefundService.selectClientRefund(startDate, endDate, appid, chaId, payType, order_str);
            long size = ((Page) clientRefundVos).getTotal();

            HashMap<String, Integer> totals = clientRefundService.selectClientRefundTotal(startDate, endDate, appid, chaId, payType);
            result.put("ret", 1);
            JSONObject data = new JSONObject();
            data.put("list", clientRefundVos);
            data.put("total", totals);
            result.put("data", data);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误信息： " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 接收客户端上报新增用户退款金额信息
     *
     * @param request
     * @return
     */
    @PostMapping("/insert")
    public String insertClientRefund(HttpServletRequest request) {
        int result = 0;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date tDate = (request.getParameter("tdate") != null && !"".equals(request.getParameter("tdate"))) ? simpleDateFormat.parse(request.getParameter("tdate")) : null;
            String appid = request.getParameter("appid");
            String chaId = request.getParameter("chaid");
            String payType = request.getParameter("paytype");
            //Integer payRevenue = request.getParameter("pay_revenue") != null ? Integer.valueOf(request.getParameter("pay_revenue")) : null;
            Integer payRevenue = null;
            Integer refundRevenue = request.getParameter("refund_revenue") != null ? Integer.valueOf(request.getParameter("refund_revenue")) : null;
            //Integer payNum = request.getParameter("paynum") != null ? Integer.valueOf(request.getParameter("paynum")) : null;
            Integer payNum = null;
            CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
            String user = loginUser == null ? "Default" : loginUser.getLogin_name();

            if (tDate == null || BlankUtils.checkBlank(appid) || BlankUtils.checkBlank(chaId) || BlankUtils.checkBlank(payType)) {
                return ReturnJson.toErrorJson("参数信息为空");
            }

            // 判断联合主键唯一性
            String[] appids = {appid};
            String[] chaids = {chaId};
            String[] paytypes = {payType};
            List<ClientRefundVo> clientRefundVos = clientRefundService.selectClientRefund(tDate, tDate, appids, chaids, paytypes, "");
            if (clientRefundVos.size() > 0) {   // 若数据已存在则调用Update方法
                result = clientRefundService.updateClientRefund(clientRefundVos.get(0).getId(), payRevenue, refundRevenue, payNum, user);
            } else {    // 若数据不存在则调用Insert方法
                result = clientRefundService.insertClientRefund(tDate, appid, chaId, payType, payRevenue, refundRevenue, payNum, user);
            }

        } catch (IllegalArgumentException e) {
            return ReturnJson.toErrorJson("参数数值错误");
        } catch (ParseException e) {
            return ReturnJson.toErrorJson("日期格式错误");
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息： " + e.getMessage());
        }
        if (result > 0) {
            return ReturnJson.success();
        } else {
            return ReturnJson.error();
        }
    }

    /**
     * 接收客户端上报删除用户退款金额信息
     *
     * @param ids
     * @return
     */
    @PostMapping("/delete")
    public String deleteClientRefund(@RequestParam("id") String[] ids) {
        int result = 0;
        try {
            if (ids.length == 0) {
                return ReturnJson.toErrorJson("id数组为空");
            }
            for (String id : ids) {
                if (!BlankUtils.checkBlank(id)) {
                    result += clientRefundService.deleteClientRefund(Integer.parseInt(id));
                }
            }
        } catch (Exception e) {
            return ReturnJson.toErrorJson("错误信息： " + e.getMessage());
        }
        if (result > 0) {
            return ReturnJson.success();
        } else {
            return ReturnJson.error();
        }
    }

    /**
     * 接收客户端上报修改用户退款金额信息
     *
     * @param request
     * @return
     */
    @PostMapping("/update")
    public String updateClientRefund(HttpServletRequest request) {
        int result = 0;
        try {
            int id = BlankUtils.checkBlank(request.getParameter("id")) ? null : Integer.parseInt(request.getParameter("id"));
            //Integer payRevenue = request.getParameter("pay_revenue") != null ? Integer.valueOf(request.getParameter("pay_revenue")) : null;
            Integer payRevenue = null;
            Integer refundRevenue = request.getParameter("refund_revenue") != null ? Integer.valueOf(request.getParameter("refund_revenue")) : null;
            //Integer payNum = request.getParameter("paynum") != null ? Integer.valueOf(request.getParameter("paynum")) : null;
            Integer payNum = null;
            CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
            String euser = loginUser == null ? "Default" : loginUser.getLogin_name();
            result = clientRefundService.updateClientRefund(id, payRevenue, refundRevenue, payNum, euser);
        } catch (NullPointerException e) {
            return ReturnJson.toErrorJson("id为空");
        } catch (NumberFormatException e) {
            return ReturnJson.toErrorJson("数值类型错误");
        } catch (Exception e) {
            return ReturnJson.toErrorJson("错误信息： " + e.getMessage());
        }

        if (result > 0) {
            return ReturnJson.success();
        } else {
            return ReturnJson.error();
        }
    }

    /**
     * 接收客户端上传excel文件修改用户退款金额信息
     *
     * @param file, request
     */
    @PostMapping("/import")
    public String importClientRefundExcel(@RequestParam(value = "file") MultipartFile file, HttpServletRequest request) {
        JSONObject result = new JSONObject();
        try {
            int failCount = 0;
            int rowCount = 1;
            HashMap<Integer, String> failedRows = new HashMap<>();
            List<String[]> fileContent = POIExcelUtils.readExcel(file);
            // 跳过第一行数据
            for (String[] row : Iterables.limit(fileContent, fileContent.size() - 1)) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
                Date tDate = (row[0] != null && !"".equals(row[0])) ? simpleDateFormat.parse(row[0]) : null;
                String appid = row[1];
                String chaId = row[2];
                String payType = row[3];
                Integer payRevenue = null;
                Integer payNum = null;
                Integer refundRevenue = (row[4] != null && !"".equals(row[4])) ? Integer.valueOf(row[4]) : null;
                CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
                String user = loginUser == null ? "Default" : loginUser.getLogin_name();
                // 判断联合主键是否存在
                if (tDate == null) {
                    failCount++;
                    rowCount++;
                    failedRows.put(rowCount, "日期参数不存在");
                    continue;
                } else if (BlankUtils.checkBlank(appid)) {
                    failCount++;
                    rowCount++;
                    failedRows.put(rowCount, "产品ID参数不存在");
                    continue;
                } else if (BlankUtils.checkBlank(chaId)) {
                    failCount++;
                    rowCount++;
                    failedRows.put(rowCount, "子渠道ID参数不存在");
                    continue;
                } else if (BlankUtils.checkBlank(payType)) {
                    failCount++;
                    rowCount++;
                    failedRows.put(rowCount, "支付类型参数不存在");
                    continue;
                }
                // 判断联合主键唯一性
                String[] appids = {appid};
                String[] chaids = {chaId};
                String[] paytypes = {payType};
                List<ClientRefundVo> clientRefundVos = clientRefundService.selectClientRefund(tDate, tDate, appids, chaids, paytypes, "");
                if (clientRefundVos.size() > 0) {   // 若数据已存在则调用Update方法
                    clientRefundService.updateClientRefund(clientRefundVos.get(0).getId(), payRevenue, refundRevenue, payNum, user);
                } else {    // 若数据不存在则调用Insert方法
                    clientRefundService.insertClientRefund(tDate, appid, chaId, payType, payRevenue, refundRevenue, payNum, user);
                }
                rowCount++;
            }
            result.put("ret", 1);
            result.put("fail_count", failCount);
            result.put("fail_info", StringUtils.strip(failedRows.toString(), "{}"));
        } catch (IllegalStateException e) {
            result.put("ret", 0);
            result.put("msg", "日期单元格格式错误");
        } catch (IOException | ParseException e) {
            result.put("ret", 0);
            result.put("msg", "错误信息： " + e.getMessage());
        }
        return result.toJSONString();
    }
}
