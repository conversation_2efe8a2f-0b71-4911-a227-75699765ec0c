package com.wbgame.controller.finance;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.aop.ApiIgp;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.master.finance.master.FinanceMapper;
import com.wbgame.pojo.PredictIncomeOverview;
import com.wbgame.pojo.finance.*;
import com.wbgame.pojo.jettison.report.InfoResult;
import com.wbgame.pojo.param.ActivityTakeParam;
import com.wbgame.service.finance.FinanceAdService;
import com.wbgame.service.finance.FinanceService;
import com.wbgame.service.jettison.report.SpendReportService;
import com.wbgame.utils.*;
import io.swagger.annotations.ApiOperation;
import jxl.Sheet;
import jxl.Workbook;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.alicp.jetcache.Cache.logger;

/**
 * 财务系统
 *
 * <AUTHOR>
 * @date 2020/10/16
 */
@CrossOrigin
@RestController
@RequestMapping(value = "/fin")
public class FinanceController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private FinanceMapper financeMapper;

    @Autowired
    private FinanceAdService adService;

    @Autowired
    private FinanceService financeService;

    /**
     * 财务系统-预估收入-查询条件-收款公司下拉内容
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/fd/getInCompanyList", method = {RequestMethod.GET, RequestMethod.POST})
    public String getInCompanyList(HttpServletRequest request, HttpServletResponse response) {

        JSONObject result = new JSONObject();
        List<Map<String, Object>> list = financeMapper.selectInComepanyList();
        result.put("ret", 1);
        result.put("data", list);
        return result.toJSONString();

    }

    @RequestMapping(value = "/wb/getAppNameList", method = {RequestMethod.GET, RequestMethod.POST})
    public String getAppNameList(String platform, HttpServletRequest request, HttpServletResponse response) {

        JSONObject result = new JSONObject();
        // 小游戏平台，移动平台，广告统计平台
        String where = " where channel_id = 51808";
        if (platform != null && ("b".equals(platform) || "c".equals(platform))) {
            where = " where channel_id = 10118";
        }
        List<Map<String, Object>> list = financeMapper.selectAppInfoList(where);
        result.put("ret", 1);
        result.put("data", list);
        return result.toJSONString();
    }

    /**
     * 广告投放支出-审核(要废除）
     * @param request
     * @return
     */
    @RequestMapping(value = "/fd/examineFinanceAppidMoney", method = {RequestMethod.GET, RequestMethod.POST})
    public String examineFinanceAppidMoney(HttpServletRequest request){
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String examine = request.getParameter("examnine");
        String year = request.getParameter("year");
        String month = request.getParameter("month");
        if (BlankUtils.checkBlank(year) || BlankUtils.checkBlank(month)) {
            return ReturnJson.error(Constants.ParamError);
        }
        if ("0".equals(examine)) {
            financeService.deleteExamineFinanceAppidMoney(year,month,"finance_appid_money");
        }else{
            financeService.insertExamineFinanceAppidMoney(year,month,"finance_appid_money");
        }
        return ReturnJson.success("审核完成");
    }

    /**
     * 财务系统-支出子系统-广告投放支出 查询(要废除）
     *
     * @return
     */
    @RequestMapping(value = "/fd/selectFinanceAppidMoney", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectFinanceAppidMoney(FinanceAppidMoney financeAppidMoney,
                                          HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("order", request.getParameter("order_str"));
        map.put("appid", request.getParameter("appid"));
        map.put("company", request.getParameter("company"));
        map.put("agent", request.getParameter("agent"));
        map.put("account", request.getParameter("account"));
        map.put("accountId", request.getParameter("accountId"));
        map.put("cname", request.getParameter("cname"));
        map.put("year", request.getParameter("year"));
        if (!BlankUtils.checkBlank(request.getParameter("month"))) {
            map.put("month", "(" + request.getParameter("month") + ")");
        }
        map.put("group", request.getParameter("groups"));

        JSONObject result = new JSONObject();
        // 进行分页
        PageHelper.startPage(pageNo, pageSize);
        List<FinanceAppidMoney> list = financeService.selectFinanceAppidMoney(map);
        long size = ((Page) list).getTotal();

        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        result.put("sum", financeMapper.selectSumFinanceAppidMoney(map));

        return result.toJSONString();
    }

    //(要废除）
    @RequestMapping(value = "/fd/importFinanceAppidMoney", method = {RequestMethod.GET, RequestMethod.POST})
    public String importFinanceAppidMoney(@RequestParam(value = "fileName") MultipartFile file,
                                          HttpServletRequest request, HttpServletResponse response) throws IOException {
        JSONObject obj = new JSONObject();
        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<FinanceAppidMoney> list = new ArrayList<FinanceAppidMoney>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                // 从第2行开始，第1行为标题，第1列内容为空则不执行
                for (int r = 1; r < row; r++) {
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                        continue;
                    }

                    String[] vals = new String[17];
                    for (int c = 0; c < 17; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }

                    FinanceAppidMoney gad = new FinanceAppidMoney();
                    for (int i = 0; i < 2; i++) {
                        if (!BlankUtils.isNumeric(vals[i])) {
                            obj.put("ret", 0);
                            obj.put("msg", vals[i] + "不是数字");
                            return obj.toJSONString();
                        }
                    }
                    Integer y = Integer.parseInt(vals[0].trim());
                    if (y < 1 || y > 9999 || vals[0].trim().length() != 4) {
                        obj.put("ret", 0);
                        obj.put("msg", vals[0] + " 不符合正常年份");
                        return obj.toJSONString();
                    }

                    gad.setYear(vals[0].trim());
                    //防止传08 这样的数据 主键不唯一
                    String month = vals[1].trim();
                    gad.setMonth(month);
                    Integer m = Integer.parseInt(month);
                    if (m < 1 || m > 13) {
                        obj.put("ret", 0);
                        obj.put("msg", vals[1] + " 不符合正常月份");
                        return obj.toJSONString();
                    }
                    if (month.length() > 1 && '0' == month.charAt(0)) {
                        gad.setMonth(month.substring(1, month.length()));
                    }

                    gad.setCompany(vals[2].trim());
                    gad.setAgent(vals[3].trim());
                    gad.setRebate(vals[4].trim());


                    gad.setCname(vals[5].trim());
                    gad.setAccount(vals[6].trim());
                    gad.setAccountId(vals[7].trim());
                    for (int i = 8; i < 16; i++) {
                        if (!BlankUtils.checkBlank(vals[i])) {
                            vals[i] = vals[i].replaceAll(",", "").trim();
                            if (!BlankUtils.isNumeric(vals[i])) {
                                obj.put("ret", 0);
                                obj.put("msg", vals[i] + "不是数字");
                                return obj.toJSONString();
                            }
                        }
                    }
                    gad.setPlatformRecharge(vals[8] == null ? "0" : ("".equals(vals[8].trim()) ? "0" : vals[8].trim()));
                    gad.setGrantIncome(vals[9] == null ? "0" : ("".equals(vals[9].trim()) ? "0" : vals[9].trim()));
                    gad.setCashConsume(vals[10] == null ? "0" : ("".equals(vals[10].trim()) ? "0" : vals[10].trim()));
                    gad.setGrantConsume(vals[11] == null ? "0" : ("".equals(vals[11].trim()) ? "0" : vals[11].trim()));
                    gad.setPlatformConsume(vals[12] == null ? "0" : ("".equals(vals[12].trim()) ? "0" : vals[12].trim()));
                    gad.setContractRebateRealCharged(vals[13] == null ? "0" : ("".equals(vals[13].trim()) ? "0" : vals[13].trim()));
                    gad.setContractRebateRealRecharged(vals[14] == null ? "0" : ("".equals(vals[14].trim()) ? "0" : vals[14].trim()));
                    gad.setDollar(vals[15] == null ? "0" : ("".equals(vals[15].trim()) ? "0" : vals[15].trim()));

                    list.add(gad);

                    obj.put("ret", 1);
                    obj.put("msg", "上传文件成功");
                }
                Map<String, List<FinanceAppidMoney>> map = list.stream().collect(Collectors.groupingBy(FinanceAppidMoney::getYear));
                for (Map.Entry<String, List<FinanceAppidMoney>> entry : map.entrySet()) {
                    List<FinanceAppidMoney> list1 = entry.getValue();
                    Map<String, List<FinanceAppidMoney>> map1 = list1.stream().collect(Collectors.groupingBy(FinanceAppidMoney::getMonth));
                    for (Map.Entry<String, List<FinanceAppidMoney>> entry1 : map1.entrySet()) {
                        if (financeService.selectExamineFinanceAppidMoney(entry.getKey(),entry1.getKey(),"finance_appid_money")){
                            return ReturnJson.toErrorJson("年份:"+entry.getKey()+",月份:"+entry1.getKey()+"已审核,禁止操作");
                        }
                    }
                }
                financeService.insertFinanceAppidMoney(list);
            } else {
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls文件!");
            }

        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            obj.put("ret", 0);
            obj.put("msg", "导入失败");
        }

        return obj.toJSONString();
    }


    //(要废除）
    @RequestMapping(value = "/fd/syncFinanceAppid", method = {RequestMethod.GET, RequestMethod.POST})
    public String sycDnAvgPrice(FinanceAppidMoney financeAppidMoney, HttpServletRequest request, HttpServletResponse response) {
        int r = 0;

        if (BlankUtils.checkBlank(financeAppidMoney.getYear()) || BlankUtils.checkBlank(financeAppidMoney.getMonth())) {
            return ReturnJson.error(Constants.ParamError);
        }
        if (financeService.selectExamineFinanceAppidMoney(financeAppidMoney.getYear(),financeAppidMoney.getMonth(),"finance_appid_money")) {
            return ReturnJson.toErrorJson("该月份已审核,禁止操作");
        }

        try {
            r = financeService.syncFinanceAppid(financeAppidMoney);
        } catch (DataAccessException e) {
            return "{\"ret\":0,\"msg\":\"存在相同的年月,账号和产品数据无法修改! 或者删除一条\"}";
        }

        if (r > 0) {
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } else {
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        }
    }

    //(要废除）
    @RequestMapping(value = "/fd/financeAppidMoneyHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public String updateFinanceAppidMoney(FinanceAppidMoney financeAppidMoney, HttpServletRequest request, HttpServletResponse response) {
        int result = 0;
        //验证审核
        if (BlankUtils.checkBlank(financeAppidMoney.getYear()) || BlankUtils.checkBlank(financeAppidMoney.getMonth())) {
            return ReturnJson.error(Constants.ParamError);
        }
        if (financeService.selectExamineFinanceAppidMoney(financeAppidMoney.getYear(),financeAppidMoney.getMonth(),"finance_appid_money")) {
            return ReturnJson.toErrorJson("该月份已审核,禁止操作");
        }

        if ("edit".equals(request.getParameter("handle"))) {
            result = financeService.updateFinanceAppidMoney(financeAppidMoney);
        } else if ("del".equals(request.getParameter("handle"))) {
            result = financeService.deleteFinanceAppidMoney(financeAppidMoney);
        } else if ("add".equals(request.getParameter("handle"))) {
            List<FinanceAppidMoney> list = new ArrayList<FinanceAppidMoney>();
            list.add(financeAppidMoney);
            result = financeService.insertFinanceAppidMoney(list);
        }

        if (result > 0) {
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } else {
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        }

    }

    /**
     * 导出(要废除）
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/fd/exportFinanceAppidMoney", method = {RequestMethod.GET, RequestMethod.POST})
    public String exportGameAdInfoList(HttpServletRequest request, HttpServletResponse response,
                                       @ApiIgp({"value","version"}) ActivityTakeParam param) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // 数据内容
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("order", request.getParameter("order_str"));
        map.put("appid", request.getParameter("appid"));
        map.put("company", request.getParameter("company"));
        map.put("agent", request.getParameter("agent"));
        map.put("account", request.getParameter("account"));
        map.put("accountId", request.getParameter("accountId"));
        map.put("cname", request.getParameter("cname"));
        map.put("year", request.getParameter("year"));
        if (!BlankUtils.checkBlank(request.getParameter("month"))) {
            map.put("month", "(" + request.getParameter("month") + ")");
        }
        map.put("group", request.getParameter("groups"));
        List<FinanceAppidMoney> list = financeService.selectFinanceAppidMoney(map);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;

        for (FinanceAppidMoney temp : list) {
            headerMap.put("year", "年");
            headerMap.put("month", "月");
            headerMap.put("company", "公司全称");
            headerMap.put("cname", "渠道");
            headerMap.put("account", "账号");
            headerMap.put("accountId", "账号id");
            headerMap.put("platformRecharge", "后台充值");
            headerMap.put("grantIncome", "赠款存入");
            headerMap.put("cashConsume", "现金消耗");
            headerMap.put("grantConsume", "赠款消耗");
            headerMap.put("platformConsume", "后台消耗");
            headerMap.put("contractRebateRealCharged","年返季返消耗");
            headerMap.put("contractRebateRealRecharged","年返季返存入");
            headerMap.put("dollar", "美金");
            headerMap.put("appid","appid");
            headerMap.put("appname", "产品名称");
            headerMap.put("agent", "代理商名称");
            headerMap.put("rebate", "返点率");

            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("year", temp.getYear());
            contentMap.put("month", temp.getMonth());
            contentMap.put("company", temp.getCompany());
            contentMap.put("cname", temp.getCname());
            contentMap.put("account", temp.getAccount());
            contentMap.put("accountId", temp.getAccountId());
            contentMap.put("platformRecharge", temp.getPlatformRecharge());
            contentMap.put("grantIncome", temp.getGrantIncome());
            contentMap.put("cashConsume", temp.getCashConsume());
            contentMap.put("grantConsume", temp.getGrantConsume());
            contentMap.put("platformConsume", temp.getPlatformConsume());
            contentMap.put("contractRebateRealCharged",temp.getContractRebateRealCharged());
            contentMap.put("contractRebateRealRecharged",temp.getContractRebateRealRecharged());
            contentMap.put("dollar", temp.getDollar());
            contentMap.put("appid",temp.getAppid());
            contentMap.put("appname", temp.getAppname());
            contentMap.put("agent", temp.getAgent());
            contentMap.put("rebate", temp.getRebate());
            contentList.add(contentMap);
        }

        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = param.getValue().split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = param.getReport()+"_" + DateTime.now().toString("yyyyMMdd")+ ".xls";
        ExportExcelUtil.export(response,contentList,head,fileName);
//        String fileName = "广告推广支出_" + DateTime.now().toString("yyyyMMdd")
//                + ".xls";
//        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
//                request, response);

        return null;
    }

    /**
     * 财务系统-支出系统-联运CPS支出 查询
     *
     * @return
     */
    @RequestMapping(value = "/fd/selectFinanceCpsCost", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectFinanceCpsCost(FinanceCpsCost financeCpsCost,
                                       HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("order", request.getParameter("order_str"));
        map.put("appid", request.getParameter("appid"));
        map.put("year", request.getParameter("year"));
        map.put("month", request.getParameter("month"));
        map.put("cps", request.getParameter("cps"));
        map.put("company", request.getParameter("company"));
        map.put("group", request.getParameter("groups"));

        JSONObject result = new JSONObject();

        // 进行分页
        PageHelper.startPage(pageNo, pageSize);
        List<FinanceCpsCost> list = financeService.selectFinanceCpsCost(map);
        long size = ((Page) list).getTotal();

        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        result.put("sum", financeMapper.selectSumFinanceCpsCost(map));

        return result.toJSONString();
    }

    @RequestMapping(value = "/fd/importFinanceCpsCost", method = {RequestMethod.GET, RequestMethod.POST})
    public String importFinanceCpsCost(@RequestParam(value = "fileName") MultipartFile file,
                                       HttpServletRequest request, HttpServletResponse response) throws IOException {
        JSONObject obj = new JSONObject();
        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<FinanceCpsCost> list = new ArrayList<FinanceCpsCost>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                // 从第2行开始，第1行为标题，第1列内容为空则不执行
                for (int r = 1; r < row; r++) {
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                        continue;
                    }

                    String[] vals = new String[10];
                    for (int c = 0; c < 10; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }

                    FinanceCpsCost gad = new FinanceCpsCost();
                    for (int i = 0; i < 2; i++) {
                        if (!BlankUtils.isNumeric(vals[i])) {
                            obj.put("ret", 0);
                            obj.put("msg", vals[i] + "不是数字");
                            return obj.toJSONString();
                        }
                    }
                    Integer y = Integer.parseInt(vals[0].trim());
                    if (y < 1 || y > 9999 || vals[0].trim().length() != 4) {
                        obj.put("ret", 0);
                        obj.put("msg", vals[0] + " 不符合正常年份");
                        return obj.toJSONString();
                    }


                    gad.setYear(vals[0].trim());
                    //防止传08 这样的数据 主键不唯一
                    String month = vals[1].trim();
                    Integer m = Integer.parseInt(month);
                    if (m < 1 || m > 12) {
                        obj.put("ret", 0);
                        obj.put("msg", vals[1] + " 不符合正常月份");
                        return obj.toJSONString();
                    }
                    gad.setMonth(month);
                    if (month.length() > 1 && '0' == month.charAt(0)) {
                        gad.setMonth(month.substring(1, month.length()));
                    }
                    gad.setAppid(vals[2].trim());
                    gad.setCps(vals[3].trim());
                    gad.setCompany(vals[4].trim());
                    gad.setContract(vals[5].trim());
                    for (int i = 6; i < 10; i++) {
                        if (!BlankUtils.isNumeric(vals[i])) {
                            obj.put("ret", 0);
                            obj.put("msg", vals[i] + "不是数字");
                            return obj.toJSONString();
                        }
                    }
                    gad.setIncome(vals[6].trim());
                    gad.setRatio(vals[7].trim());
                    gad.setTaxes(vals[8].trim());
                    gad.setCpsratio(vals[9].trim());

                    list.add(gad);

                    obj.put("ret", 1);
                    obj.put("msg", "上传文件成功");
                }
                financeService.insertFinanceCpsCost(list);
            } else {
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls文件!");
            }

        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            obj.put("ret", 0);
            obj.put("msg", "导入失败");
        }

        return obj.toJSONString();
    }

    @RequestMapping(value = "/fd/financeCpsCostHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public String financeCpsCostHandle(FinanceCpsCost financeCpsCost, HttpServletRequest request, HttpServletResponse response) {
        int result = 0;
        if ("edit".equals(request.getParameter("handle"))) {
            result = financeService.updateFinanceCpsCost(financeCpsCost);
        } else if ("del".equals(request.getParameter("handle"))) {
            result = financeService.deleteFinanceCpsCost(financeCpsCost);
        } else if ("add".equals(request.getParameter("handle"))) {
            List<FinanceCpsCost> list = new ArrayList<FinanceCpsCost>();
            list.add(financeCpsCost);
            result = financeService.insertFinanceCpsCost(list);
        }

        if (result > 0) {
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } else {
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        }

    }

    /**
     * 导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/fd/exportFinanceCpsCost", method = {RequestMethod.GET, RequestMethod.POST})
    public String exportFinanceCpsCost(HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // 数据内容
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("order", request.getParameter("order_str"));
        map.put("appid", request.getParameter("appid"));
        map.put("year", request.getParameter("year"));
        map.put("month", request.getParameter("month"));
        map.put("cps", request.getParameter("cps"));
        map.put("company", request.getParameter("company"));
        map.put("group", request.getParameter("groups"));
        List<FinanceCpsCost> list = financeService.selectFinanceCpsCost(map);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;

        for (FinanceCpsCost temp : list) {
            headerMap.put("year", "年");
            headerMap.put("month", "月");
            headerMap.put("appid", "appid");
            headerMap.put("cps", "cps渠道");
            headerMap.put("company", "渠道公司名称");
            headerMap.put("contract", "我方合同主体");
            headerMap.put("income", "收入");
            headerMap.put("ratio", "分成比例");
            headerMap.put("taxes", "税费扣除率");
            headerMap.put("cpsratio", "cps分成");

            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("year", temp.getYear());
            contentMap.put("month", temp.getMonth());
            contentMap.put("appid", temp.getAppid());
            contentMap.put("cps", temp.getCps());
            contentMap.put("company", temp.getCompany());
            contentMap.put("contract", temp.getContract());
            contentMap.put("income", temp.getIncome());
            contentMap.put("ratio", temp.getRatio());
            contentMap.put("taxes", temp.getTaxes());
            contentMap.put("cpsratio", temp.getCpsratio());
            contentList.add(contentMap);
        }

        String fileName = "联运CPS支出_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);

        return null;
    }

    /**
     * 财务系统-支出系统-产品研发分成 查询
     *
     * @return
     */
    @RequestMapping(value = "/fd/selectFinanceDevCost", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectFinanceDevCost(FinanceDevCost financeDevCost,
                                       HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("order", request.getParameter("order_str"));
        map.put("appid", request.getParameter("appid"));
        map.put("year", request.getParameter("year"));
        map.put("month", request.getParameter("month"));
        map.put("development", request.getParameter("development"));

        JSONObject result = new JSONObject();

        // 进行分页
        PageHelper.startPage(pageNo, pageSize);
        List<FinanceDevCost> list = financeService.selectFinanceDevCost(map);
        long size = ((Page) list).getTotal();

        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        result.put("sum", financeMapper.selectSumFinanceDevCost(map));

        return result.toJSONString();
    }

    @RequestMapping(value = "/fd/importFinanceDevCost", method = {RequestMethod.GET, RequestMethod.POST})
    public String importFinanceDevCost(@RequestParam(value = "fileName") MultipartFile file,
                                       HttpServletRequest request, HttpServletResponse response) throws IOException {
        JSONObject obj = new JSONObject();
        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<FinanceDevCost> list = new ArrayList<FinanceDevCost>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                // 从第2行开始，第1行为标题，第1列内容为空则不执行
                for (int r = 1; r < row; r++) {
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                        continue;
                    }

                    String[] vals = new String[11];
                    for (int c = 0; c < 11; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }
                    for (int i = 0; i < 2; i++) {
                        if (!BlankUtils.isNumeric(vals[i])) {
                            obj.put("ret", 0);
                            obj.put("msg", vals[i] + "不是数字");
                            return obj.toJSONString();
                        }
                    }
                    Integer y = Integer.parseInt(vals[0].trim());
                    if (y < 1 || y > 9999 || vals[0].trim().length() != 4) {
                        obj.put("ret", 0);
                        obj.put("msg", vals[0] + " 不符合正常年份");
                        return obj.toJSONString();
                    }

                    FinanceDevCost gad = new FinanceDevCost();
                    gad.setYear(vals[0].trim());
                    //防止传08 这样的数据 主键不唯一
                    String month = vals[1].trim();
                    Integer m = Integer.parseInt(month);
                    if (m < 1 || m > 12) {
                        obj.put("ret", 0);
                        obj.put("msg", vals[1] + " 不符合正常月份");
                        return obj.toJSONString();
                    }

                    gad.setMonth(month);
                    if (month.length() > 1 && '0' == month.charAt(0)) {
                        gad.setMonth(month.substring(1, month.length()));
                    }
                    gad.setAppid(vals[2].trim());
                    gad.setDevelopment(vals[3].trim());
                    gad.setContract(vals[4].trim());
                    for (int i = 5; i < 11; i++) {
                        if (!BlankUtils.isNumeric(vals[i])) {
                            obj.put("ret", 0);
                            obj.put("msg", vals[i] + "不是数字");
                            return obj.toJSONString();
                        }
                    }
                    gad.setIncome(vals[5].trim());
                    gad.setRatio(vals[6].trim());
                    gad.setTaxes(vals[7].trim());
                    gad.setDevratio(vals[8].trim());
                    gad.setLicense(vals[9].trim());
                    gad.setAdvances(vals[10].trim());

                    list.add(gad);

                    obj.put("ret", 1);
                    obj.put("msg", "上传文件成功");
                }
                financeService.insertFinanceDevCost(list);
            } else {
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls文件!");
            }

        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            obj.put("ret", 0);
            obj.put("msg", "导入失败");
        }

        return obj.toJSONString();
    }

    @RequestMapping(value = "/fd/financeDevCostHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public String financeDevCostHandle(FinanceDevCost financeDevCost, HttpServletRequest request, HttpServletResponse response) {
        int result = 0;
        if ("edit".equals(request.getParameter("handle"))) {
            result = financeService.updateFinanceDevCost(financeDevCost);
        } else if ("del".equals(request.getParameter("handle"))) {
            result = financeService.deleteFinanceDevCost(financeDevCost);
        } else if ("add".equals(request.getParameter("handle"))) {
            List<FinanceDevCost> list = new ArrayList<FinanceDevCost>();
            list.add(financeDevCost);
            result = financeService.insertFinanceDevCost(list);
        }

        if (result > 0) {
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } else {
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        }

    }

    /**
     * 导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/fd/exportFinanceDevCost", method = {RequestMethod.GET, RequestMethod.POST})
    public String exportFinanceDevCost(HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // 数据内容
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("order", request.getParameter("order_str"));
        map.put("appid", request.getParameter("appid"));
        map.put("year", request.getParameter("year"));
        map.put("month", request.getParameter("month"));
        map.put("development", request.getParameter("development"));
        List<FinanceDevCost> list = financeService.selectFinanceDevCost(map);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;

        for (FinanceDevCost temp : list) {
            headerMap.put("year", "年");
            headerMap.put("month", "月");
            headerMap.put("appid", "appid");
            headerMap.put("development", "研发主体");
            headerMap.put("contract", "合同");
            headerMap.put("income", "收入");
            headerMap.put("ratio", "分成比例");
            headerMap.put("taxes", "税费扣税");
            headerMap.put("devratio", "研发分成");
            headerMap.put("license", "授权金");
            headerMap.put("advances", "预付款");

            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("year", temp.getYear());
            contentMap.put("month", temp.getMonth());
            contentMap.put("appid", temp.getAppid());
            contentMap.put("development", temp.getDevelopment());
            contentMap.put("contract", temp.getContract());
            contentMap.put("income", temp.getIncome());
            contentMap.put("ratio", temp.getRatio());
            contentMap.put("taxes", temp.getTaxes());
            contentMap.put("devratio", temp.getDevratio());
            contentMap.put("license", temp.getLicense());
            contentMap.put("advances", temp.getAdvances());
            contentList.add(contentMap);
        }

        String fileName = "产品研发分成_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);

        return null;
    }




    /**
     * 财务系统-平台收入汇总 查询
     *
     * @return
     */
    @RequestMapping(value = "/fd/selectFinancePlfIncome", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectFinancePlfIncome(FinancePlfIncome financePlfIncome,
                                         HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("order", request.getParameter("order_str"));
        map.put("appid", request.getParameter("appid"));
        map.put("year", request.getParameter("year"));
        map.put("month", request.getParameter("month"));
        map.put("cname", request.getParameter("cname"));

        JSONObject result = new JSONObject();
        // 进行分页
        PageHelper.startPage(pageNo, pageSize);
        List<FinancePlfIncome> list = financeService.selectFinancePlfIncome(map);
        long size = ((Page) list).getTotal();

        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        result.put("sum", financeMapper.selectSumFinancePlfIncome(map));

        return result.toJSONString();
    }

    @RequestMapping(value = "/fd/importFinancePlfIncome", method = {RequestMethod.GET, RequestMethod.POST})
    public String importFinancePlfIncome(@RequestParam(value = "fileName") MultipartFile file,
                                         HttpServletRequest request, HttpServletResponse response) throws IOException {
        JSONObject obj = new JSONObject();
        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<FinancePlfIncome> list = new ArrayList<FinancePlfIncome>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                // 从第2行开始，第1行为标题，第1列内容为空则不执行
                for (int r = 1; r < row; r++) {
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                        continue;
                    }

                    String[] vals = new String[20];
                    for (int c = 0; c < 20; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }
                    for (int i = 0; i < 2; i++) {
                        if (!BlankUtils.isNumeric(vals[i])) {
                            obj.put("ret", 0);
                            obj.put("msg", vals[i] + "不是数字");
                            return obj.toJSONString();
                        }
                    }
                    Integer y = Integer.parseInt(vals[0].trim());
                    if (y < 1 || y > 9999 || vals[0].trim().length() != 4) {
                        obj.put("ret", 0);
                        obj.put("msg", vals[0] + " 不符合正常年份");
                        return obj.toJSONString();
                    }

                    FinancePlfIncome gad = new FinancePlfIncome();
                    gad.setYear(vals[0].trim());
                    //防止传08 这样的数据 主键不唯一
                    String month = vals[1].trim();
                    Integer m = Integer.parseInt(month);
                    if (m < 1 || m > 12) {
                        obj.put("ret", 0);
                        obj.put("msg", vals[1] + " 不符合正常月份");
                        return obj.toJSONString();
                    }

                    gad.setMonth(month);
                    if (month.length() > 1 && '0' == month.charAt(0)) {
                        gad.setMonth(month.substring(1, month.length()));
                    }
                    gad.setProject(vals[2].trim());
                    gad.setOutcompany(vals[3].trim());
                    gad.setCname(vals[4].trim());
                    gad.setIncompany(vals[5].trim());
                    gad.setIncome(vals[6].trim());
                    gad.setAppid(vals[7].trim());
                    for (int i = 8; i < 20; i++) {
                        if (!BlankUtils.isNumeric(vals[i])) {
                            obj.put("ret", 0);
                            obj.put("msg", vals[i] + "不是数字");
                            return obj.toJSONString();
                        }
                    }
                    gad.setDollarRate(vals[8].trim());
                    gad.setDollar(vals[9].trim());
                    gad.setDollarSettlement(vals[10].trim());
                    gad.setBackData(vals[11].trim());
                    gad.setPredictingIncome(vals[12].trim());
                    gad.setSettlementAmount(vals[13].trim());
                    gad.setDollarData(vals[14].trim());
                    gad.setPredictingData(vals[15].trim());
                    gad.setDifferences(vals[16].trim());
                    gad.setDifferenceNote(vals[17].trim());
                    gad.setProportion(vals[18].trim());
                    gad.setIncrease(vals[19].trim());

                    list.add(gad);

                    obj.put("ret", 1);
                    obj.put("msg", "上传文件成功");
                }
                financeService.insertFinancePlfIncome(list);
            } else {
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls文件!");
            }

        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            obj.put("ret", 0);
            obj.put("msg", "导入失败");
        }

        return obj.toJSONString();
    }

    @RequestMapping(value = "/fd/financePlfIncomeHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public String financePlfIncomeHandle(FinancePlfIncome financePlfIncome, HttpServletRequest request, HttpServletResponse response) {
        int result = 0;
        if ("edit".equals(request.getParameter("handle"))) {
            result = financeService.updateFinancePlfIncome(financePlfIncome);
        } else if ("del".equals(request.getParameter("handle"))) {
            result = financeService.deleteFinancePlfIncome(financePlfIncome);
        } else if ("add".equals(request.getParameter("handle"))) {
            List<FinancePlfIncome> list = new ArrayList<FinancePlfIncome>();
            list.add(financePlfIncome);
            result = financeService.insertFinancePlfIncome(list);
        }

        if (result > 0) {
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } else {
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        }

    }

    /**
     * 导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/fd/exportFinancePlfIncome", method = {RequestMethod.GET, RequestMethod.POST})
    public String exportFinancePlfIncome(HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // 数据内容
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("order", request.getParameter("order_str"));
        map.put("appid", request.getParameter("appid"));
        map.put("year", request.getParameter("year"));
        map.put("month", request.getParameter("month"));
        map.put("cname", request.getParameter("cname"));

        List<FinancePlfIncome> list = financeService.selectFinancePlfIncome(map);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;

        for (FinancePlfIncome temp : list) {
            headerMap.put("year", "年");
            headerMap.put("month", "月");
            headerMap.put("project", "项目");
            headerMap.put("outcompany", "开票公司全称");
            headerMap.put("cname", "渠道名称");
            headerMap.put("incompany", "收款公司");
            headerMap.put("income", "收入来源");
            headerMap.put("appid", "游戏名称");
            headerMap.put("dollarRate", "美金汇率");
            headerMap.put("dollar", "美金后台数据");
            headerMap.put("dollarSettlement", "美金结算金额");
            headerMap.put("backData", "后台数据");
            headerMap.put("predictingIncome", "预计收入");
            headerMap.put("settlementAmount", "结算计算");
            headerMap.put("dollarData", "业务提供美金数据");
            headerMap.put("predictingData", "业务提供预估数据");
            headerMap.put("differences", "差额");
            headerMap.put("differenceNote", "差额情况说明");
            headerMap.put("proportion", "占比");
            headerMap.put("increase", "同比涨幅");

            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("year", temp.getYear());
            contentMap.put("month", temp.getMonth());
            contentMap.put("project", temp.getProject());
            contentMap.put("outcompany", temp.getOutcompany());
            contentMap.put("cname", temp.getCname());
            contentMap.put("incompany", temp.getIncompany());
            contentMap.put("income", temp.getIncome());
            contentMap.put("appid", temp.getAppid());
            contentMap.put("dollarRate", temp.getDollarRate());
            contentMap.put("dollar", temp.getDollar());
            contentMap.put("dollarSettlement", temp.getDollarSettlement());
            contentMap.put("backData", temp.getBackData());
            contentMap.put("predictingIncome", temp.getPredictingIncome());
            contentMap.put("settlementAmount", temp.getSettlementAmount());
            contentMap.put("dollarData", temp.getDollarData());
            contentMap.put("predictingData", temp.getPredictingData());
            contentMap.put("differences", temp.getDifferences());
            contentMap.put("differenceNote", temp.getDifferenceNote());
            contentMap.put("proportion", temp.getProportion());
            contentMap.put("increase", temp.getIncrease());
            contentList.add(contentMap);
        }

        String fileName = "平台收入汇总_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);

        return null;
    }


    /**
     * 参数配置-投放账号配置 查询
     *
     * @return
     */
    @RequestMapping(value = "/fd/selectDnConfigFinance", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectDnConfigFinance(DnConfigFinance dnConfigFinance,
                                        HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();

        // 进行分页
        PageHelper.startPage(pageNo, pageSize);
        List<DnConfigFinance> list = financeService.selectDnConfigFinance(dnConfigFinance);
        long size = ((Page) list).getTotal();

        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        return result.toJSONString();
    }

    @RequestMapping(value = "/fd/dnConfigFinanceHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public String dnConfigFinanceHandle(DnConfigFinance dnConfigFinance, HttpServletRequest request, HttpServletResponse response) {
        int result = 0;
        if ("edit".equals(request.getParameter("handle"))) {
            result = financeService.updateDnConfigFinance(dnConfigFinance);
        } else if ("del".equals(request.getParameter("handle"))) {
            result = financeService.deleteDnConfigFinance(dnConfigFinance);
        } else if ("add".equals(request.getParameter("handle"))) {
            try {
                result = financeService.insertDnConfigFinance(dnConfigFinance);
            }catch (DuplicateKeyException e) {
                return ReturnJson.toErrorJson("该账号已存在");
            }

        }

        if (result > 0) {
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } else {
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        }

    }

    @RequestMapping(value = "fd/batchDnConfigFinance", method = {RequestMethod.GET, RequestMethod.POST})
    public String batchDnConfigFinance(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (null == file || file.isEmpty() || !(file.getOriginalFilename().endsWith(".xls") || file.getOriginalFilename().endsWith(".xlsx"))) {
            Asserts.fail("上传文件有误，需要excel文件!");
        }
        List<DnConfigFinance> list = new ArrayList<DnConfigFinance>();
        List<ArrayList<String>> arrayLists = ExcelUtils.readExcel(file);
        for (int i = 0; i < arrayLists.size(); i++) {
            if (i == 0) {
                //第一行为标题，不能为空
                for (int k = 0;k<6;k++) {
                    if (BlankUtils.checkBlank(arrayLists.get(i).get(k))) {
                        Asserts.fail("第一行标题不能出现空的情况");
                    }
                }
            } else {
                //第二行为内容
                DnConfigFinance gad = new DnConfigFinance();
                if (BlankUtils.checkBlank(arrayLists.get(i).get(3))) {
                    Asserts.fail("账号ID不能为空");
                }
                gad.setAccount(arrayLists.get(i).get(3));
                gad.setAccNum(arrayLists.get(i).get(2));
                gad.setCompany(arrayLists.get(i).get(0));
                gad.setAgent(arrayLists.get(i).get(4));
                gad.setRebate(arrayLists.get(i).get(5));
                gad.setChannel(arrayLists.get(i).get(1));
                list.add(gad);
            }
        }
        financeMapper.batchDnConfigFinance(list);

        return ReturnJson.success("导入成功");
    }

    /**
     * 财务系统-平台收入汇总 查询
     *
     * @return
     */
    @RequestMapping(value = "/fd/selectFinancePreIncome", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectFinancePreIncome(FinancePreIncome financePreIncome,
                                         HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("order", request.getParameter("order_str"));
        map.put("appid", request.getParameter("appid"));
        map.put("year", request.getParameter("year"));
        map.put("month", request.getParameter("month"));
        map.put("cname", request.getParameter("cname"));

        JSONObject result = new JSONObject();

        // 进行分页
        PageHelper.startPage(pageNo, pageSize);
        List<FinancePreIncome> list = financeService.selectFinancePreIncome(map);
        long size = ((Page) list).getTotal();

        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        result.put("sum", financeMapper.selectSumFinancePreIncome(map));

        return result.toJSONString();
    }

    @RequestMapping(value = "/fd/importFinancePreIncome", method = {RequestMethod.GET, RequestMethod.POST})
    public String importFinancePreIncome(@RequestParam(value = "fileName") MultipartFile file,
                                         HttpServletRequest request, HttpServletResponse response) throws IOException {
        JSONObject obj = new JSONObject();
        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<FinancePreIncome> list = new ArrayList<FinancePreIncome>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                // 从第2行开始，第1行为标题，第1列内容为空则不执行
                for (int r = 1; r < row; r++) {
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                        continue;
                    }

                    String[] vals = new String[12];
                    for (int c = 0; c < 12; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }
                    for (int i = 0; i < 2; i++) {
                        if (!BlankUtils.isNumeric(vals[i])) {
                            obj.put("ret", 0);
                            obj.put("msg", vals[i] + "不是数字");
                            return obj.toJSONString();
                        }
                    }
                    Integer y = Integer.parseInt(vals[0].trim());
                    if (y < 1 || y > 9999 || vals[0].trim().length() != 4) {
                        obj.put("ret", 0);
                        obj.put("msg", vals[0] + " 不符合正常年份");
                        return obj.toJSONString();
                    }

                    FinancePreIncome gad = new FinancePreIncome();
                    gad.setYear(vals[0].trim());
                    //防止传08 这样的数据 主键不唯一
                    String month = vals[1].trim();
                    Integer m = Integer.parseInt(month);
                    if (m < 1 || m > 12) {
                        obj.put("ret", 0);
                        obj.put("msg", vals[1] + " 不符合正常月份");
                        return obj.toJSONString();
                    }

                    gad.setMonth(month);
                    if (month.length() > 1 && '0' == month.charAt(0)) {
                        gad.setMonth(month.substring(1, month.length()));
                    }
                    gad.setProject(vals[2].trim());
                    gad.setOutcompany(vals[3].trim());
                    gad.setCname(vals[4].trim());
                    gad.setIncompany(vals[5].trim());
                    gad.setOrigin(vals[6].trim());
                    gad.setAppid(vals[7].trim());
                    for (int i = 8; i < 12; i++) {
                        if (!BlankUtils.isNumeric(vals[i])) {
                            obj.put("ret", 0);
                            obj.put("msg", vals[i] + "不是数字");
                            return obj.toJSONString();
                        }
                    }
                    gad.setDollarRate(vals[8].trim());
                    gad.setDollar(vals[9].trim());
                    gad.setBackData(vals[10].trim());
                    gad.setPredictingIncome(vals[11].trim());

                    list.add(gad);

                    obj.put("ret", 1);
                    obj.put("msg", "上传文件成功");
                }

                financeService.insertFinancePreIncome(list);
            } else {
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls文件!");
            }

        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            obj.put("ret", 0);
            obj.put("msg", "导入失败");
        }

        return obj.toJSONString();
    }

    @RequestMapping(value = "/fd/financePreIncomeHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public String financePreIncomeHandle(FinancePreIncome financePreIncome, HttpServletRequest request, HttpServletResponse response) {
        int result = 0;
        if ("edit".equals(request.getParameter("handle"))) {
            result = financeService.updateFinancePreIncome(financePreIncome);
        } else if ("del".equals(request.getParameter("handle"))) {
            result = financeService.deleteFinancePreIncome(financePreIncome);
        } else if ("add".equals(request.getParameter("handle"))) {
            List<FinancePreIncome> list = new ArrayList<FinancePreIncome>();
            list.add(financePreIncome);
            result = financeService.insertFinancePreIncome(list);
        }

        if (result > 0) {
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } else {
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        }
    }

    /**
     * 导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/fd/exportFinancePreIncome", method = {RequestMethod.GET, RequestMethod.POST})
    public String exportFinancePreIncome(HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // 数据内容
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("appid", request.getParameter("appid"));
        map.put("year", request.getParameter("year"));
        map.put("month", request.getParameter("month"));
        map.put("cname", request.getParameter("cname"));
        map.put("order", request.getParameter("order_str"));

        List<FinancePreIncome> list = financeService.selectFinancePreIncome(map);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;

        for (FinancePreIncome temp : list) {
            headerMap.put("year", "年");
            headerMap.put("month", "月");
            headerMap.put("project", "项目");
            headerMap.put("outcompany", "开票公司全称");
            headerMap.put("cname", "渠道名称");
            headerMap.put("incompany", "收款公司");
            headerMap.put("origin", "收入来源");
            headerMap.put("appid", "游戏名称");
            headerMap.put("dollarRate", "美金汇率");
            headerMap.put("dollar", "美金后台数据");
            headerMap.put("backData", "后台数据");
            headerMap.put("predictingIncome", "预计收入");

            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("year", temp.getYear());
            contentMap.put("month", temp.getMonth());
            contentMap.put("project", temp.getProject());
            contentMap.put("outcompany", temp.getOutcompany());
            contentMap.put("cname", temp.getCname());
            contentMap.put("incompany", temp.getIncompany());
            contentMap.put("origin", temp.getOrigin());
            contentMap.put("appid", temp.getAppid());
            contentMap.put("dollarRate", temp.getDollarRate());
            contentMap.put("dollar", temp.getDollar());
            contentMap.put("backData", temp.getBackData());
            contentMap.put("predictingIncome", temp.getPredictingIncome());
            contentList.add(contentMap);
        }

        String fileName = "预估收入_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);

        return null;
    }

    /**
     * 财务系统-结算收入  查询
     *
     * @return
     */
    @RequestMapping(value = "/fd/selectFinanceSltIncome", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectFinanceSltIncome(FinanceSltIncome financeSltIncome,
                                         HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("order", request.getParameter("order_str"));
        map.put("year", request.getParameter("year"));
        map.put("month", request.getParameter("month"));
        map.put("cname", request.getParameter("cname"));

        JSONObject result = new JSONObject();

        // 进行分页
        PageHelper.startPage(pageNo, pageSize);
        List<FinanceSltIncome> list = financeService.selectFinanceSltIncome(map);
        long size = ((Page) list).getTotal();

        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        result.put("sum", financeMapper.selectSumFinanceSltIncome(map));

        return result.toJSONString();
    }

    @RequestMapping(value = "/fd/importFinanceSltIncome", method = {RequestMethod.GET, RequestMethod.POST})
    public String importFinanceSltIncome(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {
        JSONObject obj = new JSONObject();
        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<FinanceSltIncome> list = new ArrayList<FinanceSltIncome>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                // 从第2行开始，第1行为标题，第1列内容为空则不执行
                for (int r = 1; r < row; r++) {
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                        continue;
                    }

                    String[] vals = new String[9];
                    for (int c = 0; c < 9; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }
                    for (int i = 0; i < 2; i++) {
                        if (!BlankUtils.isNumeric(vals[i])) {
                            obj.put("ret", 0);
                            obj.put("msg", vals[i] + "不是数字");
                            return obj.toJSONString();
                        }
                    }
                    Integer y = Integer.parseInt(vals[0].trim());
                    if (y < 1 || y > 9999 || vals[0].trim().length() != 4) {
                        obj.put("ret", 0);
                        obj.put("msg", vals[0] + " 不符合正常年份");
                        return obj.toJSONString();
                    }

                    FinanceSltIncome gad = new FinanceSltIncome();
                    gad.setYear(vals[0].trim());
                    //防止传08 这样的数据 主键不唯一
                    String month = vals[1].trim();
                    Integer m = Integer.parseInt(month);
                    if (m < 1 || m > 12) {
                        obj.put("ret", 0);
                        obj.put("msg", vals[1] + " 不符合正常月份");
                        return obj.toJSONString();
                    }

                    gad.setMonth(month);
                    if (month.length() > 1 && '0' == month.charAt(0)) {
                        gad.setMonth(month.substring(1, month.length()));
                    }
                    gad.setProject(vals[2].trim());
                    gad.setOutcompany(vals[3].trim());
                    gad.setCname(vals[4].trim());
                    gad.setIncompany(vals[5].trim());
                    gad.setOrigin(vals[6].trim());
                    for (int i = 7; i < 9; i++) {
                        if (!BlankUtils.isNumeric(vals[i])) {
                            obj.put("ret", 0);
                            obj.put("msg", vals[i] + "不是数字");
                            return obj.toJSONString();
                        }
                    }
                    gad.setDollarSettlement(vals[7].trim());
                    gad.setSettlement(vals[8].trim());

                    list.add(gad);

                    obj.put("ret", 1);
                    obj.put("msg", "上传文件成功");
                }
                financeService.insertFinanceSltIncome(list);
            } else {
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls文件!");
            }

        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            obj.put("ret", 0);
            obj.put("msg", "导入失败");
        }

        return obj.toJSONString();
    }

    @RequestMapping(value = "/fd/financeSltIncomeHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public String financeSltIncomeHandle(FinanceSltIncome financeSltIncome, HttpServletRequest request, HttpServletResponse response) {
        int result = 0;
        if ("edit".equals(request.getParameter("handle"))) {
            result = financeService.updateFinanceSltIncome(financeSltIncome);
        } else if ("del".equals(request.getParameter("handle"))) {
            result = financeService.deleteFinanceSltIncome(financeSltIncome);
        } else if ("add".equals(request.getParameter("handle"))) {
            List<FinanceSltIncome> list = new ArrayList<FinanceSltIncome>();
            list.add(financeSltIncome);
            result = financeService.insertFinanceSltIncome(list);
        }

        if (result > 0) {
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } else {
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        }

    }

    /**
     * 导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/fd/exportFinanceSltIncome", method = {RequestMethod.GET, RequestMethod.POST})
    public String exportFinanceSltIncome(HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // 数据内容
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("year", request.getParameter("year"));
        map.put("month", request.getParameter("month"));
        map.put("cname", request.getParameter("cname"));
        map.put("order", request.getParameter("order_str"));

        List<FinanceSltIncome> list = financeService.selectFinanceSltIncome(map);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;

        for (FinanceSltIncome temp : list) {
            headerMap.put("year", "年");
            headerMap.put("month", "月");
            headerMap.put("project", "项目");
            headerMap.put("outcompany", "开票公司全称");
            headerMap.put("cname", "渠道名称");
            headerMap.put("incompany", "收款公司");
            headerMap.put("origin", "收入来源");
            headerMap.put("dollarSettlement", "美金结算");
            headerMap.put("settlement", "结算金额");

            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("year", temp.getYear());
            contentMap.put("month", temp.getMonth());
            contentMap.put("project", temp.getProject());
            contentMap.put("outcompany", temp.getOutcompany());
            contentMap.put("cname", temp.getCname());
            contentMap.put("incompany", temp.getIncompany());
            contentMap.put("origin", temp.getOrigin());
            contentMap.put("dollarSettlement", temp.getDollarSettlement());
            contentMap.put("settlement", temp.getSettlement());
            contentList.add(contentMap);
        }

        String fileName = "结算收入_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);

        return null;
    }

    /**
     * 广告投放支出 数据同步按钮
     *
     * @param financeAppidMoney
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/fd/syscFinanceAppidMoney", method = {RequestMethod.GET, RequestMethod.POST})
    public String syscFinanceAppidMoney(FinanceAppidMoney financeAppidMoney, HttpServletRequest request, HttpServletResponse response) {
        int res = 0;

        //验证审核
        if (BlankUtils.checkBlank(financeAppidMoney.getYear()) || BlankUtils.checkBlank(financeAppidMoney.getMonth())) {
            return ReturnJson.error(Constants.ParamError);
        }
        if (financeService.selectExamineFinanceAppidMoney(financeAppidMoney.getYear(),financeAppidMoney.getMonth(),"finance_appid_money")) {
            return ReturnJson.toErrorJson("该月份已审核,禁止操作");
        }

        List<FinanceAppidMoney> list = new ArrayList<FinanceAppidMoney>();
        if (!("").equals(financeAppidMoney.getAccount()) && null != financeAppidMoney.getAccount()) {
            list = financeMapper.syscFinanceAppidMoney(financeAppidMoney);
        } else {
            list = financeMapper.syscFinanceAppidMoney2(financeAppidMoney);
        }

        if (list != null && list.size() > 0) {
            res = financeMapper.insertFinanceAppidMoney2(list);
        } else {
            return "{\"ret\":0,\"msg\":\"该账号当月没有数据,请在广告投放明细支出页面确认!\"}";
        }

        if (res > 0) {
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } else {
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        }
    }

    /**
     * 新版本广告源配置
     *
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/fd/selectFinanceAccount", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectFinanceAccount(FinanceAccountConfig app, HttpServletRequest request) throws IOException {

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        String sql = "select * from finance_account_config where 1=1";
        if (!BlankUtils.checkBlank(app.getAccount_id())) {
            sql += " and account_id = #{obj.account_id}";
        }
        if (!BlankUtils.checkBlank(app.getIncompany())) {
            sql += " and incompany = #{obj.incompany}";
        }
        if (!BlankUtils.checkBlank(app.getPaycompany())) {
            sql += " and paycompany like concat('%',#{obj.paycompany},'%')";
        }
        if (!BlankUtils.checkBlank(app.getLogin_account())) {
            sql += " and login_account = #{obj.login_account}";
        }
        if (app.getType() != null) {
            sql += " and type = #{obj.type}";
        }
        if (!BlankUtils.checkBlank(app.getAgent())) {
            sql += " and agent = #{obj.agent}";
        }

        // 进行分页
        PageHelper.startPage(pageNo, pageSize);
        List<Map<String, Object>> list = adService.queryListMapTwo(sql, app);
        long size = ((Page) list).getTotal();

        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    @RequestMapping(value = "/fd/FinanceAccountHandle", method = {RequestMethod.POST})
    public String extendAdsidHandle(String handle, FinanceAccountConfig app,String mapkey,
                                    HttpServletRequest request, HttpServletResponse response) throws IOException, Exception {

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        //判断联合主键是否存在
        Long count = financeService.existfinanceAccountConfig(app);

        String sql = null;
        if ("add".equals(handle)) {
            if (count > 0) {
                return ReturnJson.error(Constants.ExistFinanceAccountConfig);
            }
            //插入sql
            sql = "insert into finance_account_config (account_id, incompany, paycompany, login_account, type, agent, share) " +
                    "values(#{obj.account_id},#{obj.incompany},#{obj.paycompany},#{obj.login_account},#{obj.type},#{obj.agent},#{obj.share}) ";

        } else if ("edit".equals(handle)) {
            if (count == 0) {
                return ReturnJson.error(Constants.NotExistFinanceAccountConfig);
            }
            //更新sql
            sql = "update finance_account_config set incompany=#{obj.incompany},paycompany = #{obj.paycompany} " +
                    ", login_account = #{obj.login_account}, type = #{obj.type}, agent = #{obj.agent} ,share = #{obj.share} " +
                    "where account_id = #{obj.account_id} and agent = #{obj.agent}";

        } else if ("del".equals(handle)) {
            if (count == 0) {
                return ReturnJson.error(Constants.NotExistFinanceAccountConfig);
            }
            //删除sql
            sql = "delete from finance_account_config where  account_id = #{obj.account_id} and agent = #{obj.agent}";

        } else if ("batchEdit".equals(handle)) {
            System.out.println("mapkey:" + mapkey);
            // 批量更新sql
            List<String> list = Arrays.asList(mapkey.split(","));
            int num = financeMapper.batchUpdateFinanceAccountConfig(app, list);
            if (num > 0) {
                return ReturnJson.success();
            } else {
                return ReturnJson.error();
            }
        }

        int result = adService.execSqlHandle(sql, app);
        if (result > 0) {
            return ReturnJson.success();
        } else {
            return ReturnJson.error();
        }

    }

    @RequestMapping(value = "/fd/selectAccountList", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectAccountList(HttpServletRequest request, HttpServletResponse response) {
        JSONObject result = new JSONObject();

        result.put("ret", 1);
        result.put("data", financeMapper.selectAccountList());

        return result.toJSONString();
    }

    /**
     * 预估收入明细查询
     *
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/fd/selectFinancePreIncomeNew", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectFinancePreIncomeNew(FinancePreNew app, HttpServletRequest request) throws IOException {

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // 分页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;


        // 查询条件
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("agent", request.getParameter("agent"));
        map.put("dnappid", request.getParameter("dnappid"));
        map.put("incompany", request.getParameter("incompany"));
        map.put("group", request.getParameter("groups"));

        JSONObject result = new JSONObject();

        // 进行分页
        PageHelper.startPage(pageNo, pageSize);
        List<Map<String, Object>> list = financeService.selectFinancePreIncomeNew(map);

        long size = ((Page) list).getTotal();

        //汇总数据
        Map<String, Object> sum = new HashMap<>();
        List<Map<String, Object>> FinancePreNewcount = financeService.countFinancePreIncomeNew(map);
        for (Map o : FinancePreNewcount) {
            if ("1".equals(o.get("type").toString())) {
                sum.put("revenue_1", o.get("revenue"));
            } else {
                sum.put("revenue_2", o.get("revenue"));
            }
        }

        result.put("ret", 1);
        result.put("data", list);
        result.put("sum", sum);
        result.put("totalCount", size);

        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    /**
     * 预估收入明细导出
     *
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/fd/exportFinancePreIncomeNew", method = {RequestMethod.GET, RequestMethod.POST})
    public String exportFinancePreIncomeNew(FinancePreNew app, HttpServletRequest request, HttpServletResponse response) {

        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();

        // 查询条件
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("agent", request.getParameter("agent"));
        map.put("dnappid", request.getParameter("dnappid"));
        map.put("incompany", request.getParameter("incompany"));
        map.put("group", request.getParameter("groups"));

        List<Map<String, Object>> list = financeService.selectFinancePreIncomeNew(map);

        headerMap.put("date", "日期");
        headerMap.put("login_account", "登录账号");
        headerMap.put("dnappid","appid");
        headerMap.put("app_name", "产品名称");
        headerMap.put("agent", "渠道");
        headerMap.put("revenue_1", "人民币金额");
        headerMap.put("revenue_2", "美金金额");
        headerMap.put("incompany", "收款公司 ");
        headerMap.put("paycompany", "付款公司");

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "预估收入_" + DateTime.now().toString("yyyyMMddHHmmss") + ".xls";
        JxlUtil.doSave(fileName, headerMap, list, inMap, null, null, request, response);

        return null;
    }

    /**
     * 预估收入汇总查询
     *
     * @param request
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/fd/importFinancePreIncomeSum", method = {RequestMethod.GET, RequestMethod.POST})
    public String importFinancePreIncomeSum(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        if (null == file || file.isEmpty() || !(file.getOriginalFilename().endsWith(".xls") || file.getOriginalFilename().endsWith(".xlsx"))) {
            Asserts.fail("上传文件有误，需要excel文件!");
        }
        List<FinancePreIncomeSum> list = new ArrayList<>();
        List<ArrayList<String>> arrayLists = ExcelUtils.readExcel(file);
        for (int i = 0; i < arrayLists.size(); i++) {
            if (i == 0) {
                //第一行为标题，不能为空
                for (int k = 0; k < 2; k++) {
                    if (BlankUtils.checkBlank(arrayLists.get(i).get(k))) {
                        Asserts.fail("第一行标题不能出现空的情况");
                    }
                }
            } else {
                //第二行为内容
                FinancePreIncomeSum financePreIncomeSum = new FinancePreIncomeSum();
                if (BlankUtils.checkBlank(arrayLists.get(i).get(0)) || BlankUtils.checkBlank(arrayLists.get(i).get(1))
                        || BlankUtils.checkBlank(arrayLists.get(i).get(4)) || BlankUtils.checkBlank(arrayLists.get(i).get(5))
                        || BlankUtils.checkBlank(arrayLists.get(i).get(6)) || BlankUtils.checkBlank(arrayLists.get(i).get(8)) || BlankUtils.checkBlank(arrayLists.get(i).get(9))) {
                    continue;
                }
                //校验参数是否正确
                if (!BlankUtils.isNumeric(arrayLists.get(i).get(0)) || !BlankUtils.isNumeric(arrayLists.get(i).get(1))) {
                    Asserts.fail("日期错误");
                }
                if (!BlankUtils.isNumeric(arrayLists.get(i).get(6))) {
                    Asserts.fail("appid有误");
                }
                if (!BlankUtils.isNumeric(arrayLists.get(i).get(8).replace(",",""))) {
                    Asserts.fail("人民币金额有误");
                }
                if ("app广告收入".equals(arrayLists.get(i).get(9))) {
                    financePreIncomeSum.setIncomeType(1);
                }else if ("小游戏广告收入".equals(arrayLists.get(i).get(9))) {
                    financePreIncomeSum.setIncomeType(3);
                }else if ("计费收入".equals(arrayLists.get(i).get(9))) {
                    financePreIncomeSum.setIncomeType(2);
                }else if ("其他收入".equals(arrayLists.get(i).get(9))) {
                    financePreIncomeSum.setIncomeType(4);
                }else{
                    return ReturnJson.toErrorJson("收入类型有误");
                }
                financePreIncomeSum.setAgent(arrayLists.get(i).get(4));
                financePreIncomeSum.setAppid(Integer.parseInt(arrayLists.get(i).get(6)));
                financePreIncomeSum.setDollarMoney(new BigDecimal(0));
                financePreIncomeSum.setIncompany(arrayLists.get(i).get(2));
                financePreIncomeSum.setLoginAccount(arrayLists.get(i).get(5));
                financePreIncomeSum.setMonth(arrayLists.get(i).get(1));
                financePreIncomeSum.setYear(arrayLists.get(i).get(0));
                financePreIncomeSum.setPaycompany(arrayLists.get(i).get(3));
                financePreIncomeSum.setRmbMoney(new BigDecimal(arrayLists.get(i).get(8)));
                financePreIncomeSum.setExamine("0");
                FinancePreIncomeSum fin = financeService.selectFinancePreIncomeSumOne(financePreIncomeSum);
                if (fin == null || "0".equals(fin.getExamine())) {
                    list.add(financePreIncomeSum);
                }
            }
        }
        if (list != null && list.size() > 0) {
            financeService.batchInsertFinancePreIncomeSum(list);
        }
        return ReturnJson.success("导入成功");

    }


    /**
     * 预估收入汇总查询
     *
     * @param request
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/fd/selectFinancePreIncomeSum", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectFinancePreIncomeSum(FinancePreIncomeSum financePreIncomeSum, HttpServletRequest request) throws IOException {

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // 分页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;


        // 查询条件
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("year", request.getParameter("year"));
        if (!BlankUtils.checkBlank(request.getParameter("month"))) {
            map.put("month", "(" + request.getParameter("month") + ")");
        }
        map.put("agent", financePreIncomeSum.getAgent());
        map.put("appid", financePreIncomeSum.getAppid());
        map.put("incompany", financePreIncomeSum.getIncompany());
        map.put("loginAccount", financePreIncomeSum.getLoginAccount());
        map.put("group", request.getParameter("groups"));
        map.put("order", request.getParameter("order_str"));
        map.put("incomeType", financePreIncomeSum.getIncomeTypes());
        map.put("examine",financePreIncomeSum.getExamine());
        if (BlankUtils.checkBlank(request.getParameter("order_str"))) {
            map.put("order", "rmbMoney desc");
        }

        JSONObject result = new JSONObject();

        // 进行分页
        PageHelper.startPage(pageNo, pageSize);
        List<FinancePreIncomeSum> list = financeService.selectFinancePreIncomeSum(map);

        long size = ((Page) list).getTotal();

        //汇总数据
        List<Map<String, Object>> sum = financeService.countFinancePreIncomeSum(map);

        result.put("ret", 1);
        result.put("data", list);
        result.put("sum", sum);
        result.put("totalCount", size);

        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    /**
     * 预估收入汇总导出
     *
     * @param request
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/fd/exportFinancePreIncomeSum", method = {RequestMethod.GET, RequestMethod.POST})
    public String exportFinancePreIncomeSum(FinancePreIncomeSum financePreIncomeSum, HttpServletRequest request, HttpServletResponse response) {

        // 查询条件
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("year", request.getParameter("year"));
        if (!BlankUtils.checkBlank(request.getParameter("month"))) {
            map.put("month", "(" + request.getParameter("month") + ")");
        }
        map.put("agent", financePreIncomeSum.getAgent());
        map.put("appid", financePreIncomeSum.getAppid());
        map.put("incompany", financePreIncomeSum.getIncompany());
        map.put("loginAccount", financePreIncomeSum.getLoginAccount());
        map.put("group", request.getParameter("groups"));
        map.put("order", request.getParameter("order_str"));
        map.put("incomeType", financePreIncomeSum.getIncomeTypes());
        map.put("examine",financePreIncomeSum.getExamine());
        if (BlankUtils.checkBlank(request.getParameter("order_str"))) {
            map.put("order", "rmbMoney desc");
        }
        List<Map<String, Object>> list = financeService.exportFinancePreIncomeSum(map);

        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String value = request.getParameter("value");
        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                if ("incomeType".equals(s[0])){
                    s[0] = "incomeTypeName";
                }
                headerMap.put(s[0],s[1]);
            }
        }catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }


        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "预估收入_" + DateTime.now().toString("yyyyMMddHHmmss") + ".xls";
        JxlUtil.doSave(fileName, headerMap, list, inMap, null, null, request, response);

        return null;
    }

    /**
     * 预估收入汇总 数据同步按钮
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/fd/syncFinancePreIncomeSum", method = {RequestMethod.GET, RequestMethod.POST})
    public String syncFinancePreIncomeSum(HttpServletRequest request, HttpServletResponse response) {

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        Integer wait = (Integer)redisTemplate.opsForValue().get("syncFinancePreIncomeSum");
        if(wait!=null && wait ==1){
            return "{\"ret\":0,\"msg\":\"数据未同步完毕，请稍后再点击同步\"}";
        }

        redisTemplate.opsForValue().set("syncFinancePreIncomeSum",1);

        // 获取年、月参数
        String year = request.getParameter("year");
        String month = request.getParameter("month");

        // 查询条件：获取当月第一天和最后一天
        Map<String, Object> map = new HashMap<>();
        LocalDate startTime = LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 1);
        LocalDate endTime = startTime.with(TemporalAdjusters.lastDayOfMonth());
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("year", year);
        map.put("month", month);

        int res = 0;

        // 汇总两个查询结果的数据
        List<FinancePreIncomeSum> list = new ArrayList<>();
        try{
            //查询小游戏广告收入
            list.addAll(financeService.selectSyncFinancePreIncomeSum3(map));
            // 查询计费收入
            list.addAll(financeService.selectSyncFinancePreIncomeSum2(map));
            // 查询app广告收入
            list.addAll(financeService.selectSyncFinancePreIncomeSum1(map));

            if (list != null && list.size() > 0) {
                //删除同步的年月份未审核数据
                financeService.deleteFinancePreIncomeSumNotExamine(map);
                res = financeService.insertFinancePreIncomeSum(list);
            } else {
                return "{\"ret\":0,\"msg\":\"当月没有数据,请确认预估收入明细和计费收入!\"}";
            }

            //预估收入总览
            List<PredictIncomeOverview> views =financeService.selectSyncProIncomeOverview(map);
            financeService.batchInsertPredictIncomeOverview(views);
            if (res > 0) {
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            } else {
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";
            }
        } catch (Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        } finally {
            redisTemplate.delete("syncFinancePreIncomeSum");
        }

    }

    /**
     * 获取渠道数据源
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/fd/getAgentList", method = {RequestMethod.GET, RequestMethod.POST})
    public String getAgentList(HttpServletRequest request) {

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        JSONObject result = new JSONObject();
        List<Map<String, Object>> list = financeService.selectAllAgent();
        result.put("ret", 1);
        result.put("data", list);

        return result.toJSONString();

    }

    
    @RequestMapping(value = "/fd/FinancePreIncomeSumHandle", method = {RequestMethod.POST})
    public String FinancePreIncomeSumHandle(String handle, FinancePreIncomeSum app,
                                    HttpServletRequest request, HttpServletResponse response) throws IOException, Exception {

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        //验证审核
        if (!"add".equals(handle)) {
            FinancePreIncomeSum fin = financeService.selectFinancePreIncomeSumById(app.getId());
            if (fin == null) {
                return ReturnJson.toErrorJson("该数据不存在,禁止操作");
            }
            if ("1".equals(fin.getExamine())) {
                return ReturnJson.toErrorJson("审核数据禁止操作");
            }
        }
        String sql = null;
        if ("add".equals(handle)) {
            //插入sql
            sql = "insert into finance_pre_income_summary (`year`, `month`, `agent`, `incompany`, `paycompany`, `login_account`, `appid`, `dollar_money`, `rmb_money`, `income_type`,is_examine) " +
                    "values(#{obj.year}, #{obj.month}, #{obj.agent}, #{obj.incompany}, #{obj.paycompany}, #{obj.loginAccount}, #{obj.appid}, #{obj.dollarMoney}, #{obj.rmbMoney}, #{obj.incomeType},0) ";

        } else if ("edit".equals(handle)) {
            //更新sql
            sql = "update finance_pre_income_summary set incompany=#{obj.incompany},paycompany = #{obj.paycompany} " +
                    ", dollar_money = #{obj.dollarMoney}, rmb_money = #{obj.rmbMoney}, income_type = #{obj.incomeType}" +
                    " where login_account = #{obj.loginAccount} and year = #{obj.year} and month = #{obj.month} and agent = #{obj.agent} and appid = #{obj.appid} ";

        } else if ("del".equals(handle)) {
            //删除sql
            sql = "delete from finance_pre_income_summary where year = #{obj.year} and month = #{obj.month} and agent = #{obj.agent} and login_account = #{obj.loginAccount} and appid = #{obj.appid}";
        }

        int result = adService.execSqlHandle(sql, app);
        if (result > 0) {
            return ReturnJson.success();
        } else {
            return ReturnJson.error();
        }

    }

    /**
     * 广告投放支出-审核
     * @param request
     * @return
     */
    @RequestMapping(value = "/fd/examineFinancePreIncomeSum", method = {RequestMethod.GET, RequestMethod.POST})
    public String examineFinancePreIncomeSum(HttpServletRequest request){
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String examine = request.getParameter("examine");
        String ids = request.getParameter("ids");
        if (BlankUtils.checkBlank(ids) || BlankUtils.checkBlank(examine)) {
            return ReturnJson.error(Constants.ParamError);
        }
        Map<String,Object> param = new HashMap<>();
        param.put("ids",ids);
        param.put("examine",examine);
        financeService.examineFinacePreIncomeSummary(param);
        return ReturnJson.success("审核完成");
    }

    /**
     *  新增广告投放支出改进页面（查询）
     *
     * @return
     */
    @RequestMapping(value = "/fd/selectDnReportFinance", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectDnReportFinance1(HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("account", request.getParameter("account"));
        map.put("appid", request.getParameter("appid"));
        map.put("company", request.getParameter("company"));
        map.put("ad_platform", request.getParameter("ad_platform"));
        map.put("agent", request.getParameter("agent"));
        map.put("order", request.getParameter("order_str"));
        map.put("groups", request.getParameter("groups"));
        // 进行分页
        PageHelper.startPage(pageNo, pageSize);
        List<DnReportFinance1> list = financeMapper.selectDnReportFinance1(map);
        long size = ((Page) list).getTotal();

        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        result.put("sum", financeMapper.selectSumDnReportFinance1(map));
        return result.toJSONString();
    }

    /**
     *  新增广告投放支出改进页面（删除和新增）
     *
     * @return
     */
    @RequestMapping(value = "/fd/updateDnReportFinance", method = {RequestMethod.GET, RequestMethod.POST})
    public String updateDnReportFinance(DnReportFinance1 dnReportFinance, HttpServletRequest request, HttpServletResponse response) {
        int result = 0;

        if (BlankUtils.checkBlank(dnReportFinance.getDate())) {
            return ReturnJson.error(Constants.ParamError);
        }
        //验证审核
        String[] date = dnReportFinance.getDate().split("-");
        if (financeService.selectExamineFinanceAppidMoney(date[0],date[1],"dn_report_finance")) {
            return ReturnJson.toErrorJson("该月份已审核,禁止操作");
        }

        if ("edit".equals(request.getParameter("handle"))) {
            result = financeService.updateDnReportFinance(dnReportFinance);
        } else if ("add".equals(request.getParameter("handle"))) {
            List<DnReportFinance1> list = new ArrayList<DnReportFinance1>();
            list.add(dnReportFinance);
            result = financeService.insertDnReportFinance(list);
        }

        if (result > 0) {
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } else {
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        }

    }

    /**
     * 广告投放支出改进页面（导出）
     *
     * @param request
     * @param response
     * @return@
     */
    @RequestMapping(value = "/fd/exportDnReportFinance", method = {RequestMethod.GET, RequestMethod.POST})
    public String exportDnReportFinance1(HttpServletRequest request, HttpServletResponse response) {
        String token = request.getParameter("token");

        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        String value = request.getParameter("value");

        // 数据内容
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("account", request.getParameter("account"));
        map.put("appid", request.getParameter("appid"));
        map.put("company", request.getParameter("company"));
        map.put("ad_platform", request.getParameter("ad_platform"));
        map.put("agent", request.getParameter("agent"));
        map.put("order", request.getParameter("order_str"));
        map.put("groups", request.getParameter("groups"));

        List<DnReportFinance1> list = financeMapper.selectDnReportFinance1(map);

        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;
        Map<String, String> headerMap = new LinkedHashMap<String, String>();

        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                headerMap.put(s[0],s[1]);
            }
        }catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        for (DnReportFinance1 temp : list) {
            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("year", temp.getYear());
            contentMap.put("month", temp.getMonth());
            contentMap.put("day", temp.getDay());
            contentMap.put("company", temp.getCompany());
            contentMap.put("agent", temp.getAgent());
            contentMap.put("ad_platform", temp.getAd_platform());
            contentMap.put("account", temp.getAccount());
            contentMap.put("rebate", temp.getRebate());
            contentMap.put("income", temp.getIncome());
            contentMap.put("contractRebateRealCharged", temp.getContractRebateRealCharged());
            contentMap.put("rewardcost", temp.getRewardcost());
            contentMap.put("contractRebateRealRecharged", temp.getContractRebateRealRecharged());
            contentMap.put("cashcost", temp.getCashcost());
            contentMap.put("cost",temp.getCost());
            contentMap.put("incomeNum",temp.getIncomeNum());
            contentMap.put("realcost",temp.getRealcost());
            contentMap.put("rebateNum", temp.getRebateNum());
            contentMap.put("spendcost",temp.getSpendcost());
            contentMap.put("diffcost",temp.getDiffcost());
            contentMap.put("transfer_in",temp.getTransfer_in());
            contentMap.put("appid",temp.getAppid());
            contentMap.put("appName", temp.getAppName());
            contentMap.put("appCategory", temp.getAppCategory());
            contentList.add(contentMap);
        }
        String fileName = "广告投放支出_" + DateTime.now().toString("yyyyMMdd") + ".xlsx";
        ExportExcelUtil.exportXLSX(response,contentList,headerMap,fileName);

        return null;
    }

    /**
     * 广告投放支出改进页面（导入）
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/fd/importDnReportFinance", method = {RequestMethod.GET, RequestMethod.POST})
    public String importDnReportFinance(@RequestParam(value = "fileName") MultipartFile file,
                                          HttpServletRequest request, HttpServletResponse response) throws IOException {
        JSONObject obj = new JSONObject();
        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<DnReportFinance1> list = new ArrayList<DnReportFinance1>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                // 从第2行开始，第1行为标题，第1列内容为空则不执行
                for (int r = 1; r < row; r++) {
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                        continue;
                    }

                    String[] vals = new String[11];
                    for (int c = 0; c < vals.length; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }

                    DnReportFinance1 gad = new DnReportFinance1();

                    gad.setAccount(vals[0].trim());
                    gad.setAppid(vals[1].trim());
                    gad.setAd_platform(vals[2].trim());
                    for (int i = 3; i < 10; i++) {
                        if (!BlankUtils.checkBlank(vals[i])) {
                            vals[i] = vals[i].replaceAll(",", "").trim();
                            if (!BlankUtils.isNumeric(vals[i])) {
                                obj.put("ret", 0);
                                obj.put("msg", vals[i] + "不是数字");
                                return obj.toJSONString();
                            }
                        }
                    }
                    gad.setIncome(vals[3].trim());
                    gad.setContractRebateRealCharged(vals[4].trim());
                    gad.setRewardcost(vals[5].trim());
                    gad.setContractRebateRealRecharged(vals[6].trim());
                    gad.setCashcost(vals[7].trim());
                    gad.setCost(vals[8].trim());
                    gad.setTransfer_in(vals[9].trim());
                    gad.setDate(vals[10].trim());
                    String[] date = vals[10].trim().split("-");
                    gad.setYear(date[0]);
                    gad.setMonth(date[1]);
                    list.add(gad);
                    obj.put("ret", 1);
                    obj.put("msg", "上传文件成功");
                }

                Map<String, List<DnReportFinance1>> map = list.stream().collect(Collectors.groupingBy(DnReportFinance1::getYear));
                for (Map.Entry<String, List<DnReportFinance1>> entry : map.entrySet()) {
                    List<DnReportFinance1> list1 = entry.getValue();
                    Map<String, List<DnReportFinance1>> map1 = list1.stream().collect(Collectors.groupingBy(DnReportFinance1::getMonth));
                    for (Map.Entry<String, List<DnReportFinance1>> entry1 : map1.entrySet()) {
                        if (financeService.selectExamineFinanceAppidMoney(entry.getKey(),entry1.getKey(),"dn_report_finance")){
                            return ReturnJson.toErrorJson("年份:"+entry.getKey()+",月份:"+entry1.getKey()+"已审核,禁止操作");
                        }
                    }
                }
                financeService.insertDnReportFinance(list);
            } else {
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls文件!");
            }

        } catch (Exception e) {
            e.printStackTrace();
            obj.put("ret", 0);
            obj.put("msg", "导入失败");
        }

        return obj.toJSONString();
    }

    /**
     * 广告投放支出-审核
     * @param request
     * @return
     */
    @RequestMapping(value = "/fd/examineDnReportFinance", method = {RequestMethod.GET, RequestMethod.POST})
    public String examineDnReportFinance(HttpServletRequest request){
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String examine = request.getParameter("examnine");
        String year = request.getParameter("year");
        String month = request.getParameter("month");
        if (BlankUtils.checkBlank(year) || BlankUtils.checkBlank(month)) {
            return ReturnJson.error(Constants.ParamError);
        }
        if ("0".equals(examine)) {
            financeService.deleteExamineFinanceAppidMoney(year,month,"dn_report_finance");
        }else{
            financeService.insertExamineFinanceAppidMoney(year,month,"dn_report_finance");
        }
        return ReturnJson.success("审核完成");
    }



}
