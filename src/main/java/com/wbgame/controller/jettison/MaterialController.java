package com.wbgame.controller.jettison;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.LoginCheck;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.jettison.UploadMaterialRecordDTO;
import com.wbgame.pojo.jettison.UploadMaterialTaskDTO;
import com.wbgame.pojo.jettison.param.MaterialParam;
import com.wbgame.pojo.jettison.param.MoveMaterialParam;
import com.wbgame.pojo.jettison.report.MaterialBatchParam;
import com.wbgame.pojo.jettison.report.MaterialDTO;
import com.wbgame.pojo.jettison.vo.MaterialChildFolderVo;
import com.wbgame.pojo.jettison.vo.MaterialPathVo;
import com.wbgame.pojo.jettison.vo.TagVo;
import com.wbgame.service.jettison.DocumentService;
import com.wbgame.service.jettison.MaterialPathService;
import com.wbgame.service.jettison.report.MaterialService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.jettison.JSONResultModel;
import com.wbgame.utils.jettison.JSONResultString;
import com.github.pagehelper.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.alicp.jetcache.Cache.logger;

@CrossOrigin
@RestController
@RequestMapping("/material")
@Api(tags = "素材库")
@ApiSupport(author = "xugx")
public class MaterialController {

    @Autowired
    private HttpServletRequest request;
    @Autowired
    MaterialService materialService;
    
    @Autowired
    MaterialPathService materialPathService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
	@Autowired
	private DocumentService documentService;
	
	@Value("${album_id}")
	private  String album_id;
    
    @PostMapping("/uploadMaterial")
    @ApiOperation(value = "素材上传")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "上传失败",response = JSONResultString.class),
            @ApiResponse(code =1, message = "操作成功",response = JSONResultString.class),
            @ApiResponse(code =0, message = "身份验证不通过",response = JSONResultString.class)
    })
    public JSONResultString uploadMaterial(MaterialParam param){
    	JSONResultString infoResult = new JSONResultString();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
            infoResult.setRet(0);
            infoResult.setMsg("身份验证不通过");
            return infoResult;
        }else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        try {
            String username;
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
            infoResult = materialService.uploadMaterial(param,username,album_id);
        } catch (Exception e) {
            infoResult.setRet(-1);
            infoResult.setMsg("上传失败");
            logger.error("uploadMaterial error msg: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "下载素材", notes = "下载素材")
    @GetMapping(value = "/downloadMaterial")
    public JSONResultString downloadMaterial(HttpServletResponse response, @RequestParam(value = "idList") String idList,@RequestParam(value = "folder") String folderName) {
    	JSONResultString js=new JSONResultString();
    	try {
    		String token = request.getParameter("token");
	        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
	        	js.setRet(0);
	        	js.setMsg("身份验证不通过");
	            return js;
	        }else {
	            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
	        }
			materialService.downMaterial(idList,folderName, response);
		} catch (Exception e) {
			logger.error("downloadMaterial error msg: ", e);
			e.printStackTrace();
		}
		return js;
    }
    
    @ApiOperation(value = "下载素材链接", notes = "下载素材链接")
    @GetMapping(value = "/downloadMaterialLink")
    public JSONResultString downloadMaterialLink(HttpServletResponse response, @RequestParam(value = "idList") String idList) {
    	JSONResultString js=new JSONResultString();
    	try {
    		String token = request.getParameter("token");
	        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
	        	js.setRet(0);
	        	js.setMsg("身份验证不通过");
	            return js;
	        }else {
	            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
	        }
    		materialService.downloadMaterialLink(response,"素材路径_" + System.currentTimeMillis()+ ".xls",idList);
		} catch (Exception e) {
			logger.error("downloadMaterialLink error msg: ", e);
			e.printStackTrace();
		}
		return js;
    }
    
    @PostMapping("/getMaterialList")
    @ApiOperation(value = "素材列表(矩阵版)")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "获取数据异常",response = JSONResultModel.class),
            @ApiResponse(code = 0, message = "成功",response = JSONResultModel.class)
    })
    public JSONResultModel getMaterialList(MaterialDTO dto){
    	JSONResultModel infoResult = new JSONResultModel();
        try {
        	String token = request.getParameter("token");
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(false);
                infoResult.setMsg("身份验证不通过");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
			int pageNum=null==dto.getPage()?1:dto.getPage();
			int size=null==dto.getPageSize()?50:dto.getPageSize();
			PageHelper.startPage(pageNum,size);
			List<MaterialDTO>	materiaList=materialPathService.getMaterialList(dto);
			infoResult.setRows(materiaList);
			long totalSize = ((Page<MaterialDTO>) materiaList).getTotal();
			infoResult.setTotal((int)totalSize);
        } catch (Exception e) {
        	infoResult.setErrcode(-1);
            infoResult.setRet(false);;
            infoResult.setMsg("获取数据异常");
            logger.error("getMaterialList error msg: ", e);
        }
        return infoResult;
    }

    @PostMapping("/getParentPath")
    @ApiOperation(value = "获取当前文件夹素材的子文件夹")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "获取数据异常",response = JSONResultModel.class),
            @ApiResponse(code = 0, message = "成功",response = JSONResultModel.class)
    })
    @LoginCheck
    public JSONResultModel getParentPath(String parentId,String name){
        JSONResultModel infoResult = new JSONResultModel();
        try {
            if (BlankUtils.checkBlank(parentId)) {
                parentId = "0";
            }
            List<MaterialPathVo> materiaPathList=materialPathService.selectParentPathList(parentId,name);
            infoResult.setRows(materiaPathList);
            infoResult.setTotal(materiaPathList.size());
        } catch (Exception e) {
            infoResult.setErrcode(-1);
            infoResult.setRet(false);;
            infoResult.setMsg("获取数据异常");
            logger.error("getMaterialPath error msg: ", e);
        }
        return infoResult;
    }

    @PostMapping("/likeParentPath")
    @ApiOperation(value = "搜索文件夹")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "获取数据异常",response = JSONResultModel.class),
            @ApiResponse(code = 0, message = "成功",response = JSONResultModel.class)
    })
    @LoginCheck
    public JSONResultModel likeParentPath(String name){
        JSONResultModel infoResult = new JSONResultModel();
        try {
            if (BlankUtils.checkBlank(name)) {
                infoResult.setErrcode(-1);
                infoResult.setRet(false);;
                infoResult.setMsg("请输入关键字");
            }
            List<MaterialPathVo> materiaPathList=materialPathService.likeParentPathList(name);
            if (materiaPathList != null && materiaPathList.size() > 0) {
                infoResult.setRows(materiaPathList);
            }else {
                infoResult.setRows(new ArrayList<>());
            }
            if (materiaPathList != null && materiaPathList.size() > 0) {
                infoResult.setTotal(materiaPathList.size());
            }else {
                infoResult.setTotal(0);
            }
        } catch (Exception e) {
            infoResult.setErrcode(-1);
            infoResult.setRet(false);;
            infoResult.setMsg("获取数据异常");
            logger.error("getMaterialPath error msg: ", e);
        }
        return infoResult;
    }
    
    
    @PostMapping("/getChildFolder")
    @ApiOperation(value = "文件夹列表(矩阵版)")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "获取数据异常",response = JSONResultModel.class),
            @ApiResponse(code = 0, message = "成功",response = JSONResultModel.class)
    })
    public JSONResultModel getChildFolder(MaterialDTO dto){
    	JSONResultModel infoResult = new JSONResultModel();
        try {
        	String token = request.getParameter("token");
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(false);
                infoResult.setMsg("身份验证不通过");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
			int pageNum=null==dto.getPage()?1:dto.getPage();
			int size=null==dto.getPageSize()?50:dto.getPageSize();
			PageHelper.startPage(pageNum,size);
			//获取子文件夹
			List<MaterialPathVo>	folderList=materialPathService.getChildFolder(Integer.valueOf(dto.getFilePath()));
			if(null==folderList||folderList.size()==0){
				infoResult.setRows(null);
				infoResult.setTotal(0);
				return infoResult;
			}
			List<MaterialChildFolderVo> returnList=new ArrayList<MaterialChildFolderVo>();
			for (int i = 0; i <folderList.size(); i++) {
				MaterialChildFolderVo returnJson=new MaterialChildFolderVo();
				MaterialPathVo folder=folderList.get(i);
				String folderName=folder.getName();
				String groupId=folder.getGroupId();
				dto.setFilePath(groupId);
				//分页查询所有子文件夹(包含当期文件夹下的所有子文件)下的素材
				PageHelper.startPage(1,10);
				List<MaterialDTO> list=materialPathService.materialList(dto);
				returnJson.setFolderName(folderName);
				returnJson.setGroupId(groupId);
				returnJson.setList(list);
				returnJson.setParentId(dto.getFilePath());
				returnJson.setMaterialCount(((Page<MaterialDTO>) list).getTotal());
				returnList.add(returnJson);
			}
			infoResult.setRows(returnList);
			long totalSize = ((Page<MaterialPathVo>) folderList).getTotal();
			infoResult.setTotal((int)totalSize);
        } catch (Exception e) {
        	infoResult.setErrcode(-1);
            infoResult.setRet(false);;
            infoResult.setMsg("获取数据异常");
            logger.error("getChildFolder error msg: ", e);
        }
        return infoResult;
    }
    @PostMapping("/getMaterialPath")
    @ApiOperation(value = "获取素材文件夹列表")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "获取数据异常",response = JSONResultModel.class),
            @ApiResponse(code = 0, message = "成功",response = JSONResultModel.class)
    })
    public JSONResultModel getMaterialPath(MaterialDTO dto){
    	JSONResultModel infoResult = new JSONResultModel();
        try {
        	String token = request.getParameter("token");
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(false);
                infoResult.setMsg("身份验证不通过");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            int pageNum=null==dto.getPage()?1:dto.getPage();
            int size=null==dto.getPageSize()?50:dto.getPageSize();
            PageHelper.startPage(pageNum,size);
            List<MaterialDTO>	materiaList=materialPathService.materialList(dto);
            infoResult.setRows(materiaList);
            long totalSize = ((Page<MaterialDTO>) materiaList).getTotal();
            infoResult.setTotal((int)totalSize);
        } catch (Exception e) {
        	infoResult.setErrcode(-1);
            infoResult.setRet(false);;
            infoResult.setMsg("获取数据异常");
            logger.error("getMaterialPath error msg: ", e);
        }
        return infoResult;
    }
    
    
    @PostMapping("/addMaterialPath")
    @ApiOperation(value = "新增素材文件夹")
    @ApiResponses(value = {
    		@ApiResponse(code = -2, message = "已存在相同名称文件夹",response = JSONResultString.class),
            @ApiResponse(code = -1, message = "操作失败",response = JSONResultString.class),
            @ApiResponse(code =1, message = "操作成功",response = JSONResultString.class),
            @ApiResponse(code =0, message = "身份验证不通过",response = JSONResultString.class)
    })
    public JSONResultString addMaterialPath(MaterialPathVo vo){
    	JSONResultString infoResult = new JSONResultString();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
            infoResult.setRet(0);
            infoResult.setMsg("身份验证不通过");
            return infoResult;
        }else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        try {
        	int exitCount=materialPathService.exitMaterialPath(vo);
        	if(exitCount>0){
        		infoResult.setRet(-1);
        		infoResult.setMsg("当前文件夹下已存在"+vo.getName()+"文件夹");
				return infoResult;
        	}
            String username;
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
            vo.setCreateTime(DateUtil.getTimes());
            vo.setLastUpdateUser(username);
            vo.setLastUpdateTime(DateUtil.getTimes());
            vo.setGroupId(materialPathService.getGroupNum(vo.getLevel()));
            //入库
            materialPathService.addMaterialPath(vo);
            //修改文件夹序列号
            materialPathService.updateGroupNum(vo.getLevel());
            String clFolder=null;
            //调用创量API接口创建文件夹 并修改当前文件夹对应创量后台的文件夹ID
            if(StringUtil.isNotEmpty(vo.getClGroupId())){
            	clFolder=materialService.createClFolder(album_id, vo.getName(),vo.getClGroupId() ); 
            }else{
            	clFolder=materialService.createClFolder(album_id, vo.getName(),null); 
            }
            if(null!=clFolder){
            	materialPathService.updateMaterialPathClFolder(vo.getId(),clFolder);
            	vo.setClGroupId(clFolder);
            	infoResult.setMsg(JSONObject.toJSONString(vo));
            }else{
            	//同步创量失败  删除已创建的文件夹
            	materialPathService.deleteFolder(vo.getId());
            	infoResult.setRet(0);
                infoResult.setMsg("同步创量文件夹失败");
            }
        } catch (Exception e) {
            infoResult.setRet(-1);
            infoResult.setMsg("操作失败");
            logger.error("addMaterialPath error msg: ", e);
        }
        return infoResult;
    }
    
    @PostMapping("/getMaterialInfo")
    @ApiOperation(value = "获取素材详细信息")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "获取数据异常",response = JSONResultModel.class),
            @ApiResponse(code = 0, message = "成功",response = JSONResultModel.class)
    })
    public JSONResultModel getMaterialInfo(@RequestParam(required = true)Integer mid ){
    	JSONResultModel infoResult = new JSONResultModel();
        try {
        	String token = request.getParameter("token");
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(false);
                infoResult.setMsg("身份验证不通过");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
        	MaterialDTO	materiaInfo=materialPathService.getMaterialInfo(mid);
        	infoResult.setRows(materiaInfo);
        } catch (Exception e) {
        	infoResult.setErrcode(-1);
            infoResult.setRet(false);;
            infoResult.setMsg("获取数据异常");
            logger.error("getMaterialInfo error msg: ", e);
        }
        return infoResult;
    }
    
    
    @PostMapping("/updateMaterial")
    @ApiOperation(value = "修改素材信息")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "操作失败",response = JSONResultString.class),
            @ApiResponse(code =1, message = "操作成功",response = JSONResultString.class),
            @ApiResponse(code =0, message = "身份验证不通过",response = JSONResultString.class)
    })
    public JSONResultString updateMaterial(MaterialDTO dto){
    	JSONResultString infoResult = new JSONResultString();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
            infoResult.setRet(0);
            infoResult.setMsg("身份验证不通过");
            return infoResult;
        }else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        //检查设计师是否已离职
        List<MaterialDTO> materialDTOS = materialService.selectMaterialByids(dto.getId().toString());
        if (materialDTOS == null || materialDTOS.size() == 0) {
            infoResult.setRet(0);
            infoResult.setMsg("未找到修改素材");
            return infoResult;
        }
        MaterialDTO materialDTO = materialDTOS.get(0);
        //是否修改了设计师
        if (!dto.getArtist().equals(materialDTO.getArtist())) {
            //设计师是否在职
            Boolean isJob = materialService.selectArtistIsJob(materialDTO.getArtist());
            if (!isJob) {
                infoResult.setRet(0);
                infoResult.setMsg("禁止修改素材离职设计师");
                return infoResult;
            }
        }

        try {
            String username;
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
            dto.setUpdateUser(username);
            dto.setUpdateTime(DateUtil.getTimes());
            materialPathService.updateMaterial(dto);
        } catch (Exception e) {
            infoResult.setRet(-1);
            infoResult.setMsg("操作失败");
            logger.error("addMaterialPath error msg: ", e);
        }
        return infoResult;
    }

    @PostMapping("/batchUpdateMaterial")
    @ApiOperation(value = "批量修改素材")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "操作失败",response = JSONResultString.class),
            @ApiResponse(code =1, message = "操作成功",response = JSONResultString.class),
            @ApiResponse(code =0, message = "身份验证不通过",response = JSONResultString.class)
    })
    public JSONResultString batchUpdateMaterial(MaterialBatchParam param){
        JSONResultString infoResult = new JSONResultString();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
            infoResult.setRet(0);
            infoResult.setMsg("身份验证不通过");
            return infoResult;
        }else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        try {
            if (param.getIs_artist() != 1 && param.getIs_label() != 1) {
                infoResult.setRet(0);
                infoResult.setMsg("必须勾选一个修改项批量修改");
                return infoResult;
            }

            //查询修改的素材是否存在离职设计师
            if (param.getIs_artist() == 1) {
                List<MaterialDTO> materialDTOS = materialService.selectMaterialByids(param.getIds());
                if (materialDTOS == null || materialDTOS.size() == 0) {
                    infoResult.setRet(0);
                    infoResult.setMsg("未找到修改素材");
                    return infoResult;
                }
                //检查是否有离职设计师
                Set<String> artist = new HashSet<>(materialDTOS.stream().map(MaterialDTO::getArtist).collect(Collectors.toList()));
                for (String m : artist) {
                    if (m.equals(param.getArtist())) {
                        continue;
                    }
                    Boolean isJob = materialService.selectArtistIsJob(m);
                    if (!isJob) {
                        infoResult.setRet(0);
                        infoResult.setMsg("禁止修改素材离职设计师");
                        return infoResult;
                    }
                }
            }

            String username;
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
            param.setUpdateUser(username);
            param.setUpdateTime(DateUtil.getTimes());
            materialPathService.batchUpdateMaterial(param);
        } catch (Exception e) {
            infoResult.setRet(-1);
            infoResult.setMsg("操作失败");
            logger.error("addMaterialPath error msg: ", e);
        }
        return infoResult;
    }
    
    @PostMapping("/moveMaterial")
    @ApiOperation(value = "移动素材")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "操作失败",response = JSONResultString.class),
            @ApiResponse(code =1, message = "操作成功",response = JSONResultString.class),
            @ApiResponse(code =0, message = "身份验证不通过",response = JSONResultString.class)
    })
    public JSONResultString moveMaterial(@RequestBody MoveMaterialParam param){
    	JSONResultString infoResult = new JSONResultString();
    	List<Integer> materialIds =param.getMaterialIds();
    	if(null==materialIds||materialIds.size()==0){
            infoResult.setRet(-1);
            infoResult.setMsg("操作失败");
            return infoResult;
    	}
        String token = param.getToken();
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
            infoResult.setRet(0);
            infoResult.setMsg("身份验证不通过");
            return infoResult;
        }else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        try {
            String username;
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
            materialPathService.batchUpdateMaterialPath(materialIds, param.getGroupId(), username, DateUtil.getTimes(),param.getPathName());
        } catch (Exception e) {
            infoResult.setRet(-1);
            infoResult.setMsg("操作失败");
            logger.error("moveMaterial error msg: ", e);
        }
        return infoResult;
    }
    
    @PostMapping("/deleteMaterial")
    @ApiOperation(value = "删除素材")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "操作失败",response = JSONResultString.class),
            @ApiResponse(code =1, message = "操作成功",response = JSONResultString.class),
            @ApiResponse(code =0, message = "身份验证不通过",response = JSONResultString.class)
    })
    public JSONResultString deleteMaterial(@RequestBody MoveMaterialParam param){
    	JSONResultString infoResult = new JSONResultString();
    	List<Integer> materialIds =param.getMaterialIds();
    	if(null==materialIds||materialIds.size()==0){
            infoResult.setRet(-1);
            infoResult.setMsg("操作失败");
            return infoResult;
    	}
        String token = param.getToken();
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
            infoResult.setRet(0);
            infoResult.setMsg("身份验证不通过");
            return infoResult;
        }else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        try {
            List<MaterialDTO> materialDTOS = materialService.selectMaterialByids(StringUtils.join(param.getMaterialIds(), ","));
            if (materialDTOS == null || materialDTOS.size() == 0) {
                infoResult.setRet(0);
                infoResult.setMsg("未找到删除素材");
                return infoResult;
            }
            //检查是否有离职设计师
            Set<String> artist = new HashSet<>(materialDTOS.stream().map(MaterialDTO::getArtist).collect(Collectors.toList()));
            for (String m : artist) {
                Boolean isJob = materialService.selectArtistIsJob(m);
                if (!isJob) {
                    infoResult.setRet(0);
                    infoResult.setMsg("禁止删除包含离职设计师的素材,设计师="+m);
                    return infoResult;
                }
            }
            materialPathService.deleteMaterial(param.getMaterialIds());
        } catch (Exception e) {
            infoResult.setRet(-1);
            infoResult.setMsg("操作失败");
            logger.error("deleteMaterial error msg: ", e);
        }
        return infoResult;
    }
    
    @PostMapping("/editMaterialPath")
    @ApiOperation(value = "修改文件夹名称")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "操作失败",response = JSONResultString.class),
            @ApiResponse(code =1, message = "操作成功",response = JSONResultString.class),
            @ApiResponse(code =0, message = "身份验证不通过",response = JSONResultString.class)
    })
    public JSONResultString editMaterialPath(MaterialPathVo vo){
    	JSONResultString infoResult = new JSONResultString();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
            infoResult.setRet(1);
            infoResult.setMsg("身份验证不通过");
            return infoResult;
        }else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        try {
        	int exitCount=materialPathService.exitMaterialPath(vo);
        	if(exitCount>0){
        		infoResult.setRet(-1);
        		infoResult.setMsg("当前文件夹下已存在"+vo.getName()+"文件夹");
				return infoResult;
        	}
        	materialPathService.updateMaterialPath(vo);
        } catch (Exception e) {
            infoResult.setRet(-1);
            infoResult.setMsg("操作失败");
            logger.error("editMaterialPath error msg: ", e);
        }
        return infoResult;
    }
    
    @PostMapping("/deleteMaterialPath")
    @ApiOperation(value = "删除文件夹")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "操作失败",response = JSONResultString.class),
            @ApiResponse(code =1, message = "操作成功",response = JSONResultString.class),
            @ApiResponse(code =0, message = "身份验证不通过",response = JSONResultString.class)
    })
    public JSONResultString deleteMaterialPath(String groupId){
    	JSONResultString infoResult = new JSONResultString();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
            infoResult.setRet(1);
            infoResult.setMsg("身份验证不通过");
            return infoResult;
        }else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        try {
        	int exitCount=materialPathService.getMaterialCountByGroupId(groupId);
        	if(exitCount>0){
        		infoResult.setRet(-1);
        		infoResult.setMsg("当前文件夹下存在"+exitCount+"个素材,请先删除或移动素材后重试");
				return infoResult;
        	}
        	int exitChildCount=materialPathService.getChildCountByGroupId(groupId);
        	if(exitChildCount>0){
        		infoResult.setRet(-1);
        		infoResult.setMsg("当前文件夹下存在"+exitChildCount+"个子文件夹,请先删除子文件夹后重试");
				return infoResult;
        	}
        	materialPathService.deleteFilePathByGroupId(groupId);
        } catch (Exception e) {
            infoResult.setRet(-1);
            infoResult.setMsg("操作失败");
            logger.error("deleteMaterialPath error msg: ", e);
        }
        return infoResult;
    }
    
    @PostMapping("/moveMaterialPath")
    @ApiOperation(value = "移动文件夹")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "操作失败",response = JSONResultString.class),
            @ApiResponse(code =1, message = "操作成功",response = JSONResultString.class),
            @ApiResponse(code =0, message = "身份验证不通过",response = JSONResultString.class)
    })
    public JSONResultString moveMaterialPath(String desGroupId,String desPathName,String groupId,int desLevel){
    	JSONResultString infoResult = new JSONResultString();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
            infoResult.setRet(0);
            infoResult.setMsg("身份验证不通过");
            return infoResult;
        }else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        try {
            String username;
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
            //查询所有子文件夹
            List<Integer> childGroupIdList=materialPathService.getChildGroupId(groupId);
            //修改当前文件夹路径
            materialPathService.moveMaterialPath(desGroupId, groupId,username,DateUtil.getTimes(),desLevel);
            //修改子文件夹id值 将子文件夹放在父文件夹后面 递归才能查询到
            materialPathService.updateChildGroupId(childGroupIdList);
            //获取所有素材ID
            MaterialDTO dto=new MaterialDTO();
            dto.setFilePath(groupId);
            List<MaterialDTO> list=materialPathService.materialList(dto);
            List<Integer> materialIds=list.stream().map(MaterialDTO::getId).collect(Collectors.toList());
            //修改所有素材的路径名称
            materialPathService.batchUpdateMaterialPath(materialIds, groupId, username,DateUtil.getTimes(),desPathName);
        } catch (Exception e) {
            infoResult.setRet(-1);
            infoResult.setMsg("操作失败");
            logger.error("moveMaterial error msg: ", e);
        }
        return infoResult;
    }
    
    @PostMapping("/getTagList")
    @ApiOperation(value = "获取标签")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "获取数据异常",response = JSONResultModel.class),
            @ApiResponse(code = 0, message = "成功",response = JSONResultModel.class)
    })
    public JSONResultModel tagList(String appName){
    	JSONResultModel infoResult = new JSONResultModel();
        try {
        	JSONObject returnJson=new JSONObject();
        	TagVo vo=new TagVo();
        	if(StringUtil.isNotEmpty(appName)){
        		vo.setAppName(appName);
        	}
        	List<TagVo> list=documentService.tagList(vo);
        	Map<String,List<TagVo>> tagList=list.stream().collect(Collectors.groupingBy(TagVo::getTagType));
        	for (Entry<String, List<TagVo>> entry : tagList.entrySet()) {
				String type=entry.getKey();
				List<TagVo> value=entry.getValue();
				if("1".equals(type)){
					returnJson.put("tagList1", value);
				}else if("2".equals(type)){
					returnJson.put("tagList2", value);
				}else{
					returnJson.put("tagList3", value);
				}
			}
        	infoResult.setRows(returnJson);
        } catch (Exception e) {
        	infoResult.setErrcode(-1);
            infoResult.setRet(false);;
            infoResult.setMsg("获取数据异常");
            logger.error("getTagList error msg: ", e);
        }
        return infoResult;
    }
    @PostMapping("/getTaskList")
    @ApiOperation(value = "获取任务列表")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "获取数据异常",response = JSONResultModel.class),
            @ApiResponse(code = 0, message = "成功",response = JSONResultModel.class)
    })
    public JSONResultModel getTaskList(UploadMaterialTaskDTO dto){
    	JSONResultModel infoResult = new JSONResultModel();
        try {
        	String token = request.getParameter("token");
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(false);
                infoResult.setMsg("身份验证不通过");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
			int pageNum=null==dto.getPage()?1:dto.getPage();
			int size=null==dto.getPageSize()?50:dto.getPageSize();
			PageHelper.startPage(pageNum,size);
			List<UploadMaterialTaskDTO>	materiaList=materialPathService.tasklList(dto);
			infoResult.setRows(materiaList);
			long totalSize = ((Page<UploadMaterialTaskDTO>) materiaList).getTotal();
			infoResult.setTotal((int)totalSize);
        } catch (Exception e) {
        	infoResult.setErrcode(-1);
            infoResult.setRet(false);;
            infoResult.setMsg("获取数据异常");
            logger.error("getTaskList error msg: ", e);
        }
        return infoResult;
    }
    @PostMapping("/getTaskDetails")
    @ApiOperation(value = "任务详情")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "获取数据异常",response = JSONResultModel.class),
            @ApiResponse(code = 0, message = "成功",response = JSONResultModel.class)
    })
    public JSONResultModel getTaskDetails(Integer taskId,Integer page,Integer pageSize){
    	JSONResultModel infoResult = new JSONResultModel();
        try {
        	String token = request.getParameter("token");
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(false);
                infoResult.setMsg("身份验证不通过");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
			int pageNum=null==page?1:page;
			int size=null==pageSize?50:pageSize;
			PageHelper.startPage(pageNum,size);
			List<UploadMaterialRecordDTO>	materiaList=materialPathService.taskDetaillList(taskId);
			infoResult.setRows(materiaList);
			long totalSize = ((Page<UploadMaterialRecordDTO>) materiaList).getTotal();
			infoResult.setTotal((int)totalSize);
        } catch (Exception e) {
        	infoResult.setErrcode(-1);
            infoResult.setRet(false);;
            infoResult.setMsg("获取数据异常");
            logger.error("getTaskDetails error msg: ", e);
        }
        return infoResult;
    }
    @ApiOperation(value = "获取创量后台美术人员ID", notes = "获取创量后台美术人员ID")
    @GetMapping(value = "/getClArtistUserId")
    public String getClArtistUserId() {
    	try {
    		return materialService.getClArtistUserId();
		} catch (Exception e) {
			logger.error("getClArtistUserId error msg: ", e);
			e.printStackTrace();
			return null;
		}
    }

    @ApiOperation(value = "获取创量后台美术人员ID通过名称", notes = "获取创量后台美术人员ID通过名称")
    @GetMapping(value = "/getClArtistUserIdByName")
    public String getClArtistUserIdByName(String name) {
        try {
            if (BlankUtils.checkBlank(name)) {
                return ReturnJson.toErrorJson("请输入名称再查询创量id");
            }
            String result = materialService.getClArtistUserId();
            if (BlankUtils.checkBlank(result)) {
                return ReturnJson.toErrorJson("请求创量数据失败");
            }
            JSONObject jsonObject = JSONObject.parseObject(result);
            Integer code = jsonObject.getInteger("code");
            if (code != 0) {
                return ReturnJson.toErrorJson("获取创量id失败:"+jsonObject.getString("message"));
            }
            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null) {
                return ReturnJson.toErrorJson("获取创量数据失败");
            }
            JSONArray list = data.getJSONArray("list");
            if (list == null || list.size() == 0) {
                return ReturnJson.toErrorJson("获取创量列表失败");
            }
            JSONObject o = (JSONObject) list.stream().filter(t -> name.equals(((JSONObject) t).getString("name")))
                    .distinct().findFirst().orElse(null);
            if (o == null) {
                return ReturnJson.toErrorJson("未找到该名称的创量id");
            }
            return ReturnJson.success(o);
        } catch (Exception e) {
            logger.error("getClArtistUserIdByName error msg: ", e);
            return ReturnJson.toErrorJson("获取创量id失败");
        }
    }
}
