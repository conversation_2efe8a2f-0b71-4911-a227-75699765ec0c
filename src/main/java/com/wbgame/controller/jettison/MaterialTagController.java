package com.wbgame.controller.jettison;



import java.util.List;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.common.Constant;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.jettison.vo.TagVo;
import com.wbgame.service.jettison.DocumentService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.jettison.JSONResultModel;
import com.wbgame.utils.jettison.JSONResultString;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * desc:素材库-标签管理
 * createBy:xugx
 * date：2022-08-11
 */
@CrossOrigin(maxAge =300)
@RestController
@RequestMapping(value = "/material/tag")
@Api(tags = "标签管理")
@ApiSupport(author = "xugx")
public class MaterialTagController extends BaseController {
	Logger logger = LoggerFactory.getLogger(MaterialTagController.class);
	@Autowired
	private DocumentService documentService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
	/**
	 * 标签列表
	 * @param param  
	 * @return
	 */
	@RequestMapping(value="/list", method={RequestMethod.POST})
	@ApiOperation(value = "查询", notes = "标签列表", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1001, message = "查询异常",response = JSONResultModel.class),
            @ApiResponse(code = 0, message = "成功",response = JSONResultModel.class)
    })
	public JSONResultModel list(@RequestBody TagVo vo ){
		JSONResultModel result = new JSONResultModel();
		try {
			String token = vo.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
            	result.setRet(false);
            	result.setMsg("身份验证不通过");
                return result;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
			PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
			List<TagVo> list=documentService.tagList(vo);
			long size = ((Page<TagVo>) list).getTotal();
			result.setRows(list);
			result.setTotal((int)size);
		} catch (Exception e) {
			result.setErrcode(Constant.Realize.REPORT_ERROR_CODE);
			result.setMsg(e.getMessage());
			result.setRet(false);
			logger.error("标签列表查询异常:"+e.getMessage());
			e.printStackTrace();
		}
		return result;
	}
	/**
	 * 新增标签
	 * @return js
	 */
	@RequestMapping(value="/add", method={RequestMethod.POST})
	@ApiOperation(value = "新增", notes = "新增标签", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "操作失败",response = JSONResultString.class),
            @ApiResponse(code = 1, message = "操作成功",response = JSONResultString.class)
    })
	public JSONResultString add(@RequestBody TagVo vo){
		JSONResultString js=new JSONResultString();
		try {
			String token = vo.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
            	js.setRet(1);
            	js.setMsg("身份验证不通过");
                return js;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
			//查询当前的标签名称是否已经存在
			Integer exitCount=documentService.exitTag(vo.getTagName(),vo.getAppName());
			if(null!=exitCount&&exitCount.intValue()>0){
				js.setRet(-1);
				js.setMsg("已存在"+vo.getTagName()+"标签名称");
				return js;
			}
			String username;
	        CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(vo.getToken());
	        username = currUserVo.getLogin_name();
	        vo.setCreateUser(username);
	        vo.setLastUpdateUser(username);
	        vo.setCreateTime(DateUtil.getTimes());
	        vo.setLastUpdateTime(DateUtil.getTimes());
			documentService.addTag(vo);
		} catch (Exception e) {
			js.setRet(-1);
			js.setMsg("操作失败");
			logger.error("新增标签异常:"+e.getMessage());
			e.printStackTrace();
		}
		return js;
	}
	/**
	 * 编辑标签
	 * @return js
	 */
	@RequestMapping(value="/edit", method={RequestMethod.POST})
	@ApiOperation(value = "编辑", notes = "编辑标签", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "操作失败",response = JSONResultString.class),
            @ApiResponse(code = 1, message = "操作成功",response = JSONResultString.class)
    })
	public JSONResultString edit(@RequestBody TagVo vo){
		JSONResultString js=new JSONResultString();
		try {
			String token = vo.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
            	js.setRet(1);
            	js.setMsg("身份验证不通过");
                return js;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
			//查询当前的标签名称是否已经存在
			Integer exitCount=documentService.exitTag(vo.getTagName(),vo.getAppName());
			if(null!=exitCount&&exitCount.intValue()!=vo.getId()){
				js.setRet(-1);
				js.setMsg("已存在"+vo.getTagName()+"标签名称");
				return js;
			}
			String username;
	        CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(vo.getToken());
	        username = currUserVo.getLogin_name();
	        vo.setLastUpdateUser(username);
	        vo.setLastUpdateTime(DateUtil.getTimes());
			documentService.editTag(vo);
		} catch (Exception e) {
			js.setRet(-1);
			js.setMsg("操作失败");
			logger.error("编辑标签异常:"+e.getMessage());
			e.printStackTrace();
		}
		return js;
	}
	/**
	 * 删除标签
	 * @return js
	 */
	@RequestMapping(value="/delete", method={RequestMethod.POST})
	@ApiOperation(value = "删除", notes = "删除标签", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "操作失败",response = JSONResultString.class),
            @ApiResponse(code = 1, message = "操作成功",response = JSONResultString.class)
    })
	public JSONResultString delete(@RequestBody TagVo vo){
		JSONResultString js=new JSONResultString();
		try {
			String token = vo.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
            	js.setRet(1);
            	js.setMsg("身份验证不通过");
                return js;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
			documentService.deleteTag(vo.getId());
		} catch (Exception e) {
			js.setRet(-1);
			js.setMsg("操作失败");
			logger.error("删除标签异常:"+e.getMessage());
			e.printStackTrace();
		}
		return js;
	}
}
