package com.wbgame.controller.jettison.report;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.LoginCheck;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.jettison.report.InfoResult;
import com.wbgame.service.jettison.report.FileService;
import com.wbgame.utils.BlankUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

@CrossOrigin
@RestController
@RequestMapping("/file")
@Api(tags = "数据手动录入")
@ApiSupport(author = "xujy")
public class FileController {

    @Autowired
    FileService fileService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    Logger logger = LoggerFactory.getLogger(FileController.class);

    @ApiOperation(value = "b站账号录入")
    @PostMapping("/bzhan")
    public InfoResult bzhanAccount(@RequestParam("file") MultipartFile multipartFile,@RequestParam("token") String token) {
        InfoResult infoResult = new InfoResult();
        try {
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(0);
                infoResult.setMsg("token不存在");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            String username = currUserVo.getLogin_name();
            infoResult = fileService.bzhanAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error(e.getMessage());
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "微博账号录入")
    @PostMapping("/weibo")
    public InfoResult weiboAccount(@RequestParam("file") MultipartFile multipartFile,@RequestParam("token") String token) {
        InfoResult infoResult = new InfoResult();
        try {
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(0);
                infoResult.setMsg("token不存在");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            String username = currUserVo.getLogin_name();
            infoResult = fileService.weiboAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error(e.getMessage());
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "233账号录入")
    @PostMapping("/ttt")
    @LoginCheck
    public InfoResult tttAccount(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            String username = currUserVo.getLogin_name();
            infoResult = fileService.tttAccount(multipartFile,username);
        } catch (Exception e) {
            e.printStackTrace();
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "头条账号录入")
    @PostMapping("/toutiao")
    @LoginCheck
    public InfoResult toutiaoAccount(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            String username = currUserVo.getLogin_name();
            infoResult = fileService.toutiaoAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error("toutiaoAccount: ", e);
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "快手账号录入")
    @PostMapping("/kuaishou")
    @LoginCheck
    public InfoResult kuaishouAccount(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            String username = currUserVo.getLogin_name();
            infoResult = fileService.kuaishouAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error("kuaishouAccount: ", e);
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "广点通账号录入")
    @PostMapping("/gdt")
    @LoginCheck
    public InfoResult gdtAccount(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            String username = currUserVo.getLogin_name();
            infoResult = fileService.gdtAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error("gdtAccount: ", e);
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "微信账号录入")
    @PostMapping("/wx")
    @LoginCheck
    public InfoResult wxAccount(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            String username = currUserVo.getLogin_name();
            infoResult = fileService.wxAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error("wxAccount: ", e);
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "爱奇艺账号录入")
    @PostMapping("/aqy")
    @LoginCheck
    public InfoResult aqyAccount(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            String username = currUserVo.getLogin_name();
            infoResult = fileService.aqyAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error("aqyAccount: ", e);
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "百度账号录入")
    @PostMapping("/baidu")
    @LoginCheck
    public InfoResult baiduAccount(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            String username = currUserVo.getLogin_name();
            infoResult = fileService.baiduAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error("baiduAccount: ", e);
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "oppo账号录入")
    @PostMapping("/oppo")
    @LoginCheck
    public InfoResult oppoAccount(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            String username = currUserVo.getLogin_name();
            infoResult = fileService.oppoAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error("oppoAccount: ", e);
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "qtt账号录入")
    @PostMapping("/qtt")
    @LoginCheck
    public InfoResult qttAccount(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            String username = currUserVo.getLogin_name();
            infoResult = fileService.qttAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error("qttAccount: ", e);
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "华为账号录入")
    @PostMapping("/huawei")
    @LoginCheck
    public InfoResult huaweiAccount(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            String username = currUserVo.getLogin_name();
            infoResult = fileService.huaweiAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error("huaweiAccount: ", e);
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "vivo账号录入")
    @PostMapping("/vivo")
    @LoginCheck
    public InfoResult vivoAccount(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            String username = currUserVo.getLogin_name();
            infoResult = fileService.vivoAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error("huaweiAccount: ", e);
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "xiaomi账号录入")
    @PostMapping("/xiaomi")
    @LoginCheck
    public InfoResult xiaomiAccount(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            String username = currUserVo.getLogin_name();
            infoResult = fileService.xiaomiAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error("huaweiAccount: ", e);
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "uc账号录入")
    @PostMapping("/uc")
    @LoginCheck
    public InfoResult ucAccount(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            String username = currUserVo.getLogin_name();
            infoResult = fileService.ucAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error("oppoAccount: ", e);
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

    @ApiOperation(value = "荣耀账号录入")
    @PostMapping("/honor")
    @LoginCheck
    public InfoResult honorAccount(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
            String username = currUserVo.getLogin_name();
            infoResult = fileService.honorAccount(multipartFile,username);
        } catch (Exception e) {
            logger.error("honorAccount: ", e);
            infoResult.setRet(0);
            infoResult.setMsg("上传有误");
            return infoResult;
        }
        return infoResult;
    }

}
