package com.wbgame.controller.jettison.batch;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.LoginCheck;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.jettison.param.HonorMaterialParam;
import com.wbgame.service.jettison.CreativeMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description Honor创意素材库选择查询
 * <AUTHOR>
 * @Date 2025/01/24 17:10
 */
@CrossOrigin
@RestController
@RequestMapping("/batch/material/upload/honor")
@Api(tags = "Honor素材封面")
@ApiSupport(author = "huangmb")
@LoginCheck
@Slf4j
public class HonorMaterialCoverController {

    @Resource
    private CreativeMaterialService creativeMaterialService;

    @RequestMapping(value = "/autoCover",method = {RequestMethod.POST })
    @ApiOperation(value = "自动生成封面", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    public String autoCover(HonorMaterialParam param){

        if (param.getAuto_type() == 1) {
            return creativeMaterialService.getHonorAutoCoverOss(param);
        }else {
            return creativeMaterialService.getHonorAutoCover(param);
        }

    }

    @RequestMapping(value = "/loadCover",method = {RequestMethod.POST })
    @ApiOperation(value = "导入生成封面", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    public String loadCover(MultipartFile file,HonorMaterialParam param){

        return creativeMaterialService.getHonorLoadCover(file,param);

    }

    @RequestMapping(value = "/loadLogo",method = {RequestMethod.POST })
    @ApiOperation(value = "导入生成Logo", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    public String loadLogo(MultipartFile file,HonorMaterialParam param){

        return creativeMaterialService.getHonorLoadLogo(file,param);

    }


}
