package com.wbgame.controller.mobile.set.hbSet;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.slave2.YdSlave2Mapper;
import com.wbgame.pojo.WbPayConfig;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @description: 支付宝登录配置
 * @author: huangmb
 * @date: 2021/02/24
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/alipayLoginConfig")
public class AlipayLoginConfigController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private YdSlave2Mapper xyxMapper;

    /**
     * 支付宝登录配置
     * @param np
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/list", method = { RequestMethod.GET, RequestMethod.POST })
    public String list(WbPayConfig np, HttpServletRequest request, HttpServletResponse response) {

        JSONObject result = new JSONObject();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        try {
            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<WbPayConfig> list = xyxMapper.selectWbPayConfig(np);
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 支付宝登录配置
     * @param np
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/handle", method = { RequestMethod.GET, RequestMethod.POST })
    public String handle(String handle,WbPayConfig np,HttpServletRequest request,HttpServletResponse response) {

        JSONObject result = new JSONObject();
        try {
            /* 进行token验证 */
            String token = request.getParameter("token");
            if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            else
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

            /* 根据操作标识符确定操作方式 */
            int res = 0;
            if ("add".equals(handle)) {
                try {
                    res = xyxMapper.insertWbPayConfig(np);
                } catch (DataAccessException e) {
                    return "{\"ret\":0,\"msg\":\"存在相同配置无法新增!\"}";
                }

            } else if ("edit".equals(handle)) {
                res = xyxMapper.updateWbPayConfig(np);
            } else if ("del".equals(handle)) {
                res = xyxMapper.deleteWbPayConfig(np);
            } else {
                return "{\"ret\":0,\"msg\":\"handle is error!\"}";
            }

            /* 格式化输出 */
            result.put("ret", res);
            result.put("msg", "success");
            return result.toJSONString();

        } catch (Exception e) {
            e.printStackTrace();

            return result.toJSONString();
        }

    }

}
