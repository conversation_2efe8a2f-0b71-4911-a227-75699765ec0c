package com.wbgame.controller.mobile.set.apiTransfer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.common.constants.PlatformConstants;
import com.wbgame.mapper.clean.master.CleanYdMapper;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.AppInfo;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.WaibaoUserVo;
import com.wbgame.pojo.mobile.hw.*;
import com.wbgame.service.mobile.HwApiService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import jxl.Sheet;
import jxl.Workbook;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname HwApkTransferController
 * @Description TODO
 * @Date 2022/1/22 10:06
 */
@CrossOrigin
@RestController
@RequestMapping("/mobile/hwApi")
public class HwApkTransferController {

    private static final Logger logger = LoggerFactory.getLogger(HwApkTransferController.class);


    private Map<String, String> HW_APPKEY_SECRECT_MAP = new HashMap<String, String>() {
        {
            put("823812453912562880", "6442AACFEA33A61E5C3412DEC36A5F3D4578F5A71D6D46884C6C78D4C51E0C9F"); //动能无线
            put("823813246929618112", "A0EDED45278E90D66D3EF77903A7A509E020CF6238DFE98CD82C998D48ED1977"); //飞鸟科技
            put("823813646915208256", "6E2ABBE436AABA0CFDFE24EEF622CD858D5B0D53E9C552109AFBA5CFEB51E2FF"); //唯变科技
            put("809878798630669824", "357F77FBEAAFA9212A327E486983F1B68B6E74EAFAF2A848148C9D7A5EADE0D7"); //统掌科技
            put("823814479627177344", "02B249BC569E05E5FFD9B4AC207DE57700A638DEAD6944B2EB7905C4070806B8"); //全支付
            put("823814927771781696", "0AF267A04D52FC52E1DCFF0C356ADF6FC0E519010520D2EBCF800CA013539FE2"); //漫城科技
            put("823815449115381632", "85E963ED9632C58D2024D7AAC2CC67135A8F905020BD22BB7E294AB284137B86"); //雪道科技
            put("823815922534861696", "04B1AC7AD207522F0EFBBDCF040CA9F452280F09DD9C09D8200703780F2E5C9A"); //源码科技
            put("823816378875135040", "71FC7E4F2353C05ADABF92599AF3294F8DAAF9B74555C2EAAC4CC335248C18AA"); //国数
            put("823816873391964864", "4543D8EA44A2EFF622CDA1A82E2E5DBF5FAC2F069D6CE0E8A199E4BCEF093674"); //青木瓜
            put("841994802177264384", "3D5691F0FCD963B6F56668DFC9C57E7DA3E4AC8FF56E07CC8006CE6CD135E7D0"); //源流
            put("933814260419546432", "0DABC5DFA57273406678E1FF2C3E32833DADECCFD9BB0414843266313B44A753"); //二象
            put("933871194581520128", "210BB5E69C43A0C87609EA220DDCC3B4953586E15D9E346D3E9C19FA6D64F21C"); //神兽
            put("986253646641516288", "1A6B7FD235BD9879E50BC3B3AC91F6E584804279285ACFEDBC6FE09A998282BF"); //湫漻网络
            put("981227595246882048", "5C21E6FDFD4260ACBC3CAA09FF13DCCAC359EAC09A5DD30D2740759A158A3CA3"); //愔嫕
            put("1030956389658283840", "6FEE3D29A7D9528D381F2F4FDDCC9E46ADDB76B420CFF048523E12C26EB975E2"); //深圳沄晗网络科技有限公司
            put("1030962313861024768", "AF9AC0141D72E51E5EF0AF4F8277DDE7A718F3512A4168D31FAF9809410B8FD6"); //深圳佳翮网络科技有限公司
            put("1122255130939379520", "F1395FDEC48B55D2B3D3F138A7ABA69D585D3C0929BC2B8DD5EA506C4DCEF669"); //惠州市贝管家实业有限公司
            put("1158534687619042304", "9EC53FF8B2DDA182DC600A4BCBC16CAD16B7B42379E83FB4C29F71C738F2636C"); //惠州市阳光筑家房地产有限公司
            put("1194714853168707136", "4C4F8C2CE46D582A52F14C31F92DB5F082744E0DFAAFF8BDC85ACB2B871656A4"); //深圳市悠闲互娱科技有限公司
        }
    };

    @Autowired
    HwApiService hwApiService;

    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    private YyhzMapper yyhzMapper;


    @Value("${tempPath}")
    public String tempPath;


    @PostConstruct
    public void initHwAppKeySecretMap() {
        // 初始化加载华为应用的 access_key 和 access_secret
        // 假设 service 查询返回 List<Map<String, String>>，key 分别为 access_key 和 access_secret
        String query = "select hw_report_api_client_id as access_key,hw_report_api_client_secret as access_secret from `api_packet_platform_account` where channel='huawei' and `status`=1";
        List<Map<String, String>> list = yyhzMapper.queryListMapOne(query);
        if(!CollectionUtils.isEmpty(list)) {
            HW_APPKEY_SECRECT_MAP.clear();
            for (Map<String, String> item : list) {
                HW_APPKEY_SECRECT_MAP.put(item.get("access_key"), item.get("access_secret"));
            }
        }
    }


    @RequestMapping("getCompanyConfigList")
    public Object getCompanyConfigList(){
        JSONObject ret = new JSONObject();
        return ret;
    }


    /**
     * 获取应用列表信息
     * @param request
     * @return
     */
    @RequestMapping("syncAppInfo")
    public Object getAppList(HttpServletRequest request){
        JSONObject ret = new JSONObject();
        ret.put("msg","拉取应用信息失败!");
        String tappid = request.getParameter("tappid");
        String taccount = request.getParameter("taccount");
        String clientSecrect = HW_APPKEY_SECRECT_MAP.get(taccount);
        String token = HwApiManager.getToken(PlatformConstants.HW.HW_API_URL,taccount,clientSecrect);
        HwAppInfo appInfo = HwApiManager.getAppInfo(PlatformConstants.HW.HW_API_URL,taccount,token,tappid,"");
        if (null!=appInfo){
            hwApiService.updateHwAppDetailInfo(appInfo);
            ret.put("ret",1);
            ret.put("msg","拉取应用信息成功!");
        }
        return ret;
    }


    /**
     * 获取应用列表信息
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("getAppList")
    public Object getAppList(HttpServletRequest request,HwAppInfo vo){
        String token = request.getParameter("token");
        JSONObject result = new JSONObject();
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<HwAppInfo> list  = hwApiService.getHwAppInfoList(vo);
        JSONArray jsonArray = new JSONArray();
        for (HwAppInfo appInfo:list){
            JSONObject each = new JSONObject();
            each.put("id",appInfo.getId());
            each.put("appid",appInfo.getAppid());
            each.put("tappid",appInfo.getTappid());
            each.put("taccount",appInfo.getTaccount());
            each.put("taccountName",appInfo.getTaccountName());
            each.put("packageName",appInfo.getPackageName());
            each.put("tappname",appInfo.getTappname());
            each.put("state",appInfo.getState());
            each.put("createTime",appInfo.getCreateTime());
            each.put("createUser",appInfo.getCreateUser());
            each.put("modifyTime",appInfo.getModifyTime());
            each.put("modifyUser",appInfo.getModifyUser());


            JSONObject auditInfoJson = appInfo.getAuditInfo()!=null?JSONObject.parseObject(appInfo.getAuditInfo()):new JSONObject();

            JSONObject appInfoJson = appInfo.getAppInfo()!=null?JSONObject.parseObject(appInfo.getAppInfo()):new JSONObject();
            each.put("releaseState",appInfoJson.getString("releaseState"));
            each.put("versionNumber",appInfoJson.getString("versionNumber"));

            each.put("appInfo",appInfoJson);
            each.put("auditInfo",auditInfoJson);
            each.put("languages",appInfo.getLanguages()!=null?JSONArray.parseArray(appInfo.getLanguages()):"");
            jsonArray.add(each);
        }
        long size = ((Page) list).getTotal();

        result.put("ret", 1);
        result.put("data", jsonArray);
        result.put("totalCount", size);
        return result;
    }

    @RequestMapping("/export")
    public void export(HttpServletRequest request, HttpServletResponse response, HwAppInfo vo,
                       String value, String export_file_name){
        String token = request.getParameter("token");
        JSONObject result = new JSONObject();
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return;
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        List<HwAppInfo> list  = hwApiService.getHwAppInfoList(vo);

        // 应用名称赋值
        Map<String, Map<String, Object>> appMap = yyhzMapper.getAppCategoryMap();

        List<Map<String, Object>> jsonArray = new ArrayList<>();
        for (HwAppInfo appInfo:list){
            Map<String, Object> each = new HashMap<>();
            each.put("id",appInfo.getId());

            String appid = appInfo.getAppid();
            each.put("appid", appid);
            if (!StringUtils.isBlank(appid)) {

                Map<String, Object> app = appMap.get(appid);
                if(app != null) {
                    each.put("appname", app.get("app_name")+"");
                }
            }

            each.put("tappid",appInfo.getTappid());
            each.put("taccount",appInfo.getTaccount());
            each.put("taccountName",appInfo.getTaccountName());
            each.put("packageName",appInfo.getPackageName());
            each.put("tappname",appInfo.getTappname());
            each.put("state",getState(appInfo.getState()));
            each.put("createTime",appInfo.getCreateTime());
            each.put("createUser",appInfo.getCreateUser());
            each.put("modifyTime",appInfo.getModifyTime());
            each.put("modifyUser",appInfo.getModifyUser());


            JSONObject auditInfoJson = appInfo.getAuditInfo()!=null?JSONObject.parseObject(appInfo.getAuditInfo()):new JSONObject();

            JSONObject appInfoJson = appInfo.getAppInfo()!=null?JSONObject.parseObject(appInfo.getAppInfo()):new JSONObject();
            each.put("releaseState",appInfoJson.getString("releaseState"));
            each.put("versionNumber",appInfoJson.getString("versionNumber"));

            each.put("appNetType", getAppNetType(appInfoJson.getString("appNetType")));
            each.put("updateTime", appInfoJson.getString("updateTime"));

            each.put("appInfo",appInfoJson);
            each.put("auditInfo",auditInfoJson);
            each.put("languages",appInfo.getLanguages()!=null?JSONArray.parseArray(appInfo.getLanguages()):"");
            jsonArray.add(each);
        }



        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = export_file_name+"_" + DateTime.now().toString("yyyyMMdd")+ ".xls";
        ExportExcelUtil.export(response,jsonArray,head,fileName);
    }

    /**
     * 获取 应用状态
     * @param appNetType
     * @return
     */
    private String getAppNetType(String appNetType) {

        String appNetT;
        if (StringUtils.isBlank(appNetType)) {

            return "";
        }
        switch (appNetType) {

            case "1" :

                appNetT = "单机";
                break;
            case "2":
                appNetT = "网游";
                break;
            default:
                appNetT = "";
        }

        return appNetT;
    }

    /**
     * 获取 应用联网类型
     * @param state
     * @return
     */
    private String getState(String state) {

        if (StringUtils.isBlank(state)) {

            return "";
        }

        String stat;
        switch (state) {

            case "0" :

                stat = "已上架";
                break;

            case "1" :

                stat = "上架审核不通过";
                break;
            case "2":
                stat = "已下架";
                break;

            case "3":
                stat = "待上架，预约上架";
                break;

            case "4":
                stat = "审核中";
                break;

              case "5":
                  stat = "升级中";
                break;

              case "6":
                  stat = "申请下架";
                break;

              case "7":
                  stat = "草稿";
                break;

              case "8":
                  stat = "升级审核不通过";
                break;

              case "9":
                  stat = "下架审核不通过";
                break;

              case "10":
                  stat = "应用被开发者下架";
                break;

            case "11":
                stat = "撤销上架";
                break;


            default:
                stat = "";
        }

        return stat;
    }

    /**
     * 更新应用基础信息
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("handleAppBaseInfo")
    public Object handleAppBaseInfo(HttpServletRequest request,HwAppInfo vo){
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token)&&!redisTemplate.hasKey(token)) {
            return ReturnJson.toErrorJson("登录token过期,请重新登录");
        }else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        //创建人
        String username = "";
        if (token.startsWith("wbtoken")) {
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        }else if(token.startsWith("waibaotoken")){
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_id();
        }
        vo.setCreateUser(username);
        vo.setModifyUser(username);

        String handle = request.getParameter("handle");
        int result = 0;
        try {
            if ("add".equals(handle)){
                List<HwAppInfo> list = hwApiService.getHwAppInfoList(new HwAppInfo());
                HwAppInfo appInfo = list.stream().
                        filter(t->(t.getAppid()+"_"+t.getTappid()+"_"+t.getTaccount()).equals(vo.getAppid()+"_"+vo.getTappid()+"_"+vo.getTaccount())).findFirst().orElse(null);
                if (appInfo!=null){
                    return ReturnJson.toErrorJson("已经存在同样动能产品id+华为应用id的信息");
                }
                result = hwApiService.saveHwAppBaseInfo(vo);
                try {
                    String hwToken = HwApiManager.getToken(PlatformConstants.HW.HW_API_URL,vo.getTappid(),vo.getTaccount());
                    HwAppInfo hwAppInfo=HwApiManager.getAppInfo(PlatformConstants.HW.HW_API_URL, vo.getTaccount(),vo.getTappid(),hwToken,"");
                    if(null!=hwAppInfo){
                    	hwApiService.updateHwAppDetailInfo(hwAppInfo);
                    }
                }catch (Exception e){
                    logger.error("handleAppBaseInfo add",e);
                }
            }else if ("edit".equals(handle)){
                List<HwAppInfo> list = hwApiService.getHwAppInfoList(new HwAppInfo());
                HwAppInfo appInfo = list.stream().
                        filter(t->(t.getAppid()+"_"+t.getTappid()+"_"+t.getTaccount()).equals(vo.getAppid()+"_"+vo.getTappid()+"_"+vo.getTaccount())&&!t.getId().equals(vo.getId())).findFirst().orElse(null);
                if (appInfo!=null){
                    return ReturnJson.toErrorJson("已经存在同样动能产品id+华为应用id的信息");
                }
                result = hwApiService.updateHwAppBaseInfo(vo);
                try {
                    String hwToken = HwApiManager.getToken(PlatformConstants.HW.HW_API_URL,vo.getTappid(),vo.getTaccount());
                    HwAppInfo hwAppInfo= HwApiManager.getAppInfo(PlatformConstants.HW.HW_API_URL, vo.getTaccount(),vo.getTappid(),hwToken,"");
                    if(null!=hwAppInfo){
                    	hwApiService.updateHwAppDetailInfo(hwAppInfo);
                    }
                }catch (Exception e){
                    logger.error("handleAppBaseInfo edit",e);
                }

            }else if ("del".equals(handle)){
                result = hwApiService.deleteHwAppInfo(vo);
            }
        }catch (Exception e){
            logger.error("handleAppBaseInfo error",e);
        }

        if (result>0){
            return ReturnJson.success();
        }else {
            return ReturnJson.toErrorJson("操作失败,请联系管理员");
        }
    }



    /**
     * 操作华为平台应用详细信息
     * @param request
     * @return
     */
    @RequestMapping("handleAppDetailInfo")
    public Object handleAppDetailInfo(HttpServletRequest request){
        JSONObject handleRet = new JSONObject();
        JSONObject ret = new JSONObject();

        //创建人
        HwAppInfo vo = new HwAppInfo();
        String dntoken = request.getParameter("token");
        String username = "";
        if (dntoken.startsWith("wbtoken")) {
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(dntoken);
            username = currUserVo.getLogin_name();
        }else if(dntoken.startsWith("waibaotoken")){
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(dntoken);
            username = WaibaoUser.getUser_id();
        }
        if (BlankUtils.checkBlank(username)){
            return ReturnJson.toErrorJson("token过期,请重新登录");
        }

        String handle = request.getParameter("handle");
        String tappid = request.getParameter("tappid");
        String clientId = request.getParameter("taccount");

        vo.setCreateUser(username);
        vo.setModifyUser(username);
        vo.setTappid(tappid);

        //记录操作人员信息
        if (!"syncAppInfo".equals(handle)){
            hwApiService.updateHwAppTimeInfo(vo);
        }

        String clientSecret = HW_APPKEY_SECRECT_MAP.get(clientId);
        String token = HwApiManager.getToken(PlatformConstants.HW.HW_API_URL,clientId,clientSecret);
        String ids = request.getParameter("ids");

        if ("syncAppInfo".equals(handle)){
            String lang = "";
            HwAppInfo   appInfo = HwApiManager.getAppInfo(PlatformConstants.HW.HW_API_URL,clientId,token,tappid,lang);
            if(null!=appInfo){
            	hwApiService.updateHwAppDetailInfo(appInfo);
            	ret.put("ret",1);
                ret.put("msg","拉取应用信息成功!");
            }else{
            	ret.put("ret",1);
                ret.put("msg","拉取应用信息失败!");
            }
            return ret;
        }else if ("batchSyncAppInfo".equals(handle)){
            List<HwAppInfo> list = hwApiService.getHwAppInfoListIds(ids);
            int num = 0;
            List<String> failList = new ArrayList<>();
            for (HwAppInfo app:list){
                String lang = "";
                String eachClientId = app.getTaccount();
                String eachClientSecret = HW_APPKEY_SECRECT_MAP.get(eachClientId);
                String eachTappid = app.getTappid();
                String eachToken = HwApiManager.getToken(PlatformConstants.HW.HW_API_URL,eachClientId,eachClientSecret);
                HwAppInfo   appInfo = HwApiManager.getAppInfo(PlatformConstants.HW.HW_API_URL,eachClientId,eachToken,eachTappid,lang);
                if(null==appInfo){
                	 String failMsg = "动能产品id:"+app.getAppid()+",华为产品id:"+app.getTappid()+"--拉取信息失败;";
                     failList.add(failMsg);
                }else{
                	 hwApiService.updateHwAppDetailInfo(appInfo);	
                	 num++;
                }
            }
            if (num==list.size()){
                return ReturnJson.success();
            }else {
                String finalMsg = "";
                for (String str:failList){
                    finalMsg +=str;
                }
                return ReturnJson.toErrorJson(finalMsg);
            }
        }
        else if ("submit".equals(handle)){
            handleRet = HwApiManager.submit(PlatformConstants.HW.HW_API_URL,clientId,token,tappid);
            if (handleRet!=null){
                if ("0".equals(handleRet.getJSONObject("ret").getString("code"))){
                    //刷新信息
                	HwAppInfo appInfo= HwApiManager.getAppInfo(PlatformConstants.HW.HW_API_URL,clientId,tappid,token,"");
                    if(null!=appInfo){
                    	hwApiService.updateHwAppDetailInfo(appInfo);
                    }
                    return ReturnJson.success();
                }else{
                    return ReturnJson.toErrorJson(handleRet.getJSONObject("ret").getString("msg"));
                }
            }
        }else if ("updateLangInfo".equals(handle)){
            HwAppBaseInfo app = new HwAppBaseInfo();
            String appName = request.getParameter("appName");
            String appDesc = request.getParameter("appDesc");
            String briefInfo = request.getParameter("briefInfo");        //对应语言的一句话简介
            String newFeatures = request.getParameter("newFeatures");      //对应语言的新版本简介
            if (BlankUtils.checkBlank(appName)||BlankUtils.checkBlank(appDesc)
                    ||BlankUtils.checkBlank(briefInfo)){
                return ReturnJson.toErrorJson("必填参数为空!");
            }
            app.setAppName(appName);
            app.setAppDesc(appDesc);
            app.setBriefInfo(briefInfo);
            app.setNewFeatures(newFeatures);
            handleRet = updateLangInfo(PlatformConstants.HW.HW_API_URL,clientId,token,tappid,app);
            if (handleRet!=null){
                if ("0".equals(handleRet.getJSONObject("ret").getString("code"))){
                    //刷新信息
                	HwAppInfo appInfo=HwApiManager.getAppInfo(PlatformConstants.HW.HW_API_URL,clientId,tappid,token,"");
                    if(null!=appInfo){
                    	hwApiService.updateHwAppDetailInfo(appInfo);
                    }
                    return ReturnJson.success();
                }else{
                    return ReturnJson.toErrorJson(handleRet.getJSONObject("ret").getString("msg"));
                }
            }
        }else if ("updateAppInfo".equals(handle)){
            HwAppBaseInfo app = new HwAppBaseInfo();
            String childType = request.getParameter("childType");       //应用二级分类
            String grandChildType = request.getParameter("grandChildType");  //应用三级分类
            String privacyPolicy = request.getParameter("privacyPolicy");    //隐私政策地址
            String deviceTypes = request.getParameter("deviceTypes");      //设备类型
            String publicationNumber = request.getParameter("publicationNumber");   //版号信息
            String contentRate = request.getParameter("contentRate");       //应用分级
            if (BlankUtils.checkBlank(childType)||BlankUtils.checkBlank(grandChildType)
                    ||BlankUtils.checkBlank(privacyPolicy)||BlankUtils.checkBlank(deviceTypes)
                    ||BlankUtils.checkBlank(publicationNumber)||BlankUtils.checkBlank(contentRate)){
                return ReturnJson.toErrorJson("必填参数为空!");
            }
            app.setChildType(Integer.parseInt(childType));
            app.setGrandChildType(Integer.parseInt(grandChildType));
            app.setPrivacyPolicy(privacyPolicy);
            app.setDeviceTypes(deviceTypes);
            app.setPublicationNumber(publicationNumber);
            app.setPublishCountry("CN");
            app.setContentRate(contentRate);
            app.setIsFree("1");
            handleRet = updateAppInfo(PlatformConstants.HW.HW_API_URL,clientId,token,tappid,app);
            if (handleRet!=null){
                if ("0".equals(handleRet.getJSONObject("ret").getString("code"))){
                    //刷新信息
                	HwAppInfo appInfo=HwApiManager.getAppInfo(PlatformConstants.HW.HW_API_URL,clientId,tappid,token,"");
                    if(null!=appInfo){
                    	hwApiService.updateHwAppDetailInfo(appInfo);
                    }
                    return ReturnJson.success();
                }else{
                    return ReturnJson.toErrorJson(handleRet.getJSONObject("ret").getString("msg"));
                }
            }
        }else if ("updateAppFileInfo".equals(handle)){
            List<FileInfo> filesInfo = new ArrayList<>();
            String fileType = request.getParameter("fileType");
            String fileInfos = request.getParameter("fileInfos");
            String imgShowType = request.getParameter("imgShowType");
            String videoShowType = request.getParameter("videoShowType");
            String sensitivePermissionDesc = request.getParameter("sensitivePermissionDesc");
            HwAppBaseInfo app = new HwAppBaseInfo();
            app.setImgShowType(imgShowType);
            app.setVideoShowType(videoShowType);
            app.setSensitivePermissionDesc(sensitivePermissionDesc);
            if (BlankUtils.checkBlank(fileType)||BlankUtils.checkBlank(fileInfos)){
                return ReturnJson.toErrorJson("必填参数为空!");
            }
            //当fileType为2时该参数必选
            if ("2".equals(fileType)){
                if (BlankUtils.checkBlank(imgShowType)){
                    return ReturnJson.toErrorJson("当文件类型为应用介绍截图时,必填参数为空!");
                }
            }
            //当fileType为1时该参数必选
            if ("1".equals(fileType)){
                if (BlankUtils.checkBlank(videoShowType)){
                    return ReturnJson.toErrorJson("当文件类型为应用介绍视频时,必填参数为空!");
                }
            }
            JSONArray fileInfoArray = null;
            try {
                fileInfoArray = JSONArray.parseArray(fileInfos);
            }catch (Exception e){
                logger.error("parse fileinfo error:",e);
            }
            if (fileInfoArray==null){
                return ReturnJson.toErrorJson("请求参数fileInfos数据格式不正确");
            }
            for (Object obj:fileInfoArray){
                JSONObject json = (JSONObject) obj;
                String fileName = json.getString("fileName");
                String fileDestUrl = json.getString("fileDestUrl");
                String size = json.getString("size");
                String imageResolution = json.getString("imageResolution");
                String imageResolutionSingature = json.getString("imageResolutionSingature");

                FileInfo file = new FileInfo();
                file.setFileDestUlr(fileDestUrl);
                file.setFileName(fileName);
                if (!BlankUtils.checkBlank(size)){
                    file.setSize(Integer.parseInt(size));
                }
                file.setImageResolution(imageResolution);
                file.setImageResolutionSingature(imageResolutionSingature);
                filesInfo.add(file);
            }
            handleRet = updateAppFileInfo(PlatformConstants.HW.HW_API_URL,clientId,token,tappid,Integer.parseInt(fileType),filesInfo,app);
            if (handleRet!=null){
                if ("0".equals(handleRet.getJSONObject("ret").getString("code"))){
                    //刷新信息
                	HwAppInfo appInfo=HwApiManager.getAppInfo(PlatformConstants.HW.HW_API_URL,clientId,tappid,token,"");
                    if(null!=appInfo){
                    	hwApiService.updateHwAppDetailInfo(appInfo);
                    }
                    return ReturnJson.success();
                }else{
                    return ReturnJson.toErrorJson(handleRet.getJSONObject("ret").getString("msg"));
                }
            }
        }
        return ReturnJson.toErrorJson("操作失败!");
    }






    /**
     * 更新华为平台应用语言信息 默认为 zh-CN
     * @param domain
     * @param clientId
     * @param token
     * @param appId
     */
    public static JSONObject updateLangInfo(String domain, String clientId, String token, String appId,HwAppBaseInfo app) {
        JSONObject object = null;
        HttpPut put = new HttpPut(domain + "/publish/v2/app-language-info?appId=" + appId);
        put.setHeader("Authorization", "Bearer " + token);
        put.setHeader("client_id", clientId);
        JSONObject keyString = new JSONObject();
        //Request Body
        keyString.put("lang", "zh-CN");
        keyString.put("appName", app.getAppName());
        keyString.put("appDesc", app.getAppDesc());
        keyString.put("briefInfo",app.getBriefInfo());
        if (!BlankUtils.checkBlank(app.getNewFeatures())){
            keyString.put("newFeatures",app.getNewFeatures());
        }
        //...
        StringEntity entity = new StringEntity(keyString.toString(), Charset.forName("UTF-8"));
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        put.setEntity(entity);
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            CloseableHttpResponse httpResponse = httpClient.execute(put);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader br =
                        new BufferedReader(new InputStreamReader(httpResponse.getEntity().getContent(), Consts.UTF_8));
                String result = br.readLine();

                object = JSON.parseObject(result);
            }
        } catch (Exception e) {
            logger.error("updateLangInfo error:",e);
        }
        return object;
    }


    /**
     * 更新华为平台应用基本信息 默认zh-CN
     * @param domain
     * @param clientId
     * @param token
     * @param appId
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    public static JSONObject updateAppInfo(String domain, String clientId, String token, String appId, HwAppBaseInfo app) {
        JSONObject object = null;
        HttpPut put = new HttpPut(domain + "/publish/v2/app-info?appId=" + appId);
        put.setHeader("Authorization", "Bearer " + token);
        put.setHeader("client_id", clientId);
        JSONObject keyString = new JSONObject();
        // Set the language.
        keyString.put("defaultLang", "zh-CN");
        //设置是否免费
        keyString.put("isFree",Integer.parseInt(app.getIsFree()));
        // 设置 应用二级分类
        keyString.put("childType",app.getChildType());
        // 设置 应用三家分类
        keyString.put("grandChildType",app.getGrandChildType());
        // 设置 隐私地址
        keyString.put("privacyPolicy",app.getPrivacyPolicy());
        // 设置 版号信息
        keyString.put("publicationNumber",app.getPublicationNumber());
        // 设置发布国家
        keyString.put("publishCountry",app.getPublishCountry());
        // 设置 应用分级
        keyString.put("contentRate",app.getContentRate());
        // 设置 设备类型
        JSONArray deviceTypes = new JSONArray();
        deviceTypes = JSONArray.parseArray(app.getDeviceTypes());
        keyString.put("deviceTypes",deviceTypes);

        StringEntity entity = new StringEntity(keyString.toString(), Charset.forName("UTF-8"));
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        put.setEntity(entity);
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            CloseableHttpResponse httpResponse = httpClient.execute(put);
            int statusCode = httpResponse.getStatusLine().getStatusCode();

            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader br =
                        new BufferedReader(new InputStreamReader(httpResponse.getEntity().getContent(), Consts.UTF_8));
                String result = br.readLine();
                object = JSON.parseObject(result);
            }
        } catch (Exception e) {
            logger.error("updateAppInfo error:",e);
        }
        return object;
    }


    /**
     * 华为上传文件,获取文件地址
     * @param request
     * @param file
     * @return
     * @throws IOException
     */
    @RequestMapping("uploadFile")
    public Object uploadFile(HttpServletRequest request,@RequestParam(value = "fileName") MultipartFile file) throws IOException {
        JSONObject ret = new JSONObject();
        ret.put("ret",0);
        ret.put("msg","上传失败!");

        String clientId = request.getParameter("taccount");
        String tappid = request.getParameter("tappid");
        String clientSecret = HW_APPKEY_SECRECT_MAP.get(clientId);
        //通过获取上传文件地址接口获取上传地址以及authcode
        String token = HwApiManager.getToken(PlatformConstants.HW.HW_API_URL,clientId,clientSecret);

        String fileName = file.getOriginalFilename();
        // 使用文件MD5值来作为文件名，其中.为特殊字符需要\\转义
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (suffix.length()>4){
            suffix = "";
        }
        InputStream in = file.getInputStream();
        FileOutputStream out = new FileOutputStream(tempPath + "/" + fileName);
        byte buffer[] = new byte[2048];
        int len = 0;
        while((len = in.read(buffer)) > 0){
            out.write(buffer, 0, len);
        }

        //写入文件
        File upFile = new File(tempPath + "/" + fileName);
        JSONObject object = HwApiManager.getUploadUrl(PlatformConstants.HW.HW_API_URL, clientId, token, tappid, suffix, 1);

        String authCode = String.valueOf(object.get("authCode"));

        String uploadUrl = String.valueOf(object.get("uploadUrl"));

        // HttpPost post = new HttpPost(uploadParams.get("uploadUrl") + uploadFile);
        HttpPost post = new HttpPost(uploadUrl);

        // File to upload.
        FileBody bin = new FileBody(upFile);
        // Construct a POST request.
        HttpEntity reqEntity = MultipartEntityBuilder.create()
                .addPart("file", bin)
                .addTextBody("authCode", authCode) // Obtain the authentication code.
                .addTextBody("fileCount", "1")
                .addTextBody("parseType", "1")
                .build();

        post.setEntity(reqEntity);
        post.addHeader("accept", "application/json");

        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            CloseableHttpResponse httpResponse = httpClient.execute(post);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == HttpStatus.SC_OK) {
                FileServerOriResult fileServerResult =
                        JSON.parseObject(EntityUtils.toString(httpResponse.getEntity()), FileServerOriResult.class);
                // Obtain the result code.
                if (!"0".equals(fileServerResult.getResult().getResultCode())) {
                    return null;
                }
                List<FileInfo> fileInfoList = fileServerResult.getResult().getUploadFileRsp().getFileInfoList();
                if (fileInfoList!=null&&fileInfoList.size()>0){
                    FileInfo fileInfo = fileInfoList.get(0);
                    JSONObject data =new JSONObject();
                    data.put("fileName",fileName);
                    //图片/视频返回对应数据
                    data.put("fileDestUrl",fileInfo.getFileDestUlr());
                    data.put("size",fileInfo.getSize());
                    data.put("imageResolution",fileInfo.getImageResolution());
                    data.put("imageResolutionSingature",fileInfo.getImageResolutionSingature());
                    ret.put("data",data);
                    ret.put("ret",1);
                    ret.put("msg","上传成功!");
                }
            }
        } catch (Exception e) {
            logger.error("uploadFile error:",e);
        }
        upFile.delete();
        return ret;
    }

    /**
     * 华为平台应用传包-导入
     * @param request
     * @param file
     * @return
     */
    @RequestMapping("batchImport")
    public Object batchImport(HttpServletRequest request, @RequestParam(value = "fileName") MultipartFile file) {
        TreeMap<Integer,String> retMap = new TreeMap<>();
        String token = request.getParameter("token");
        CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
        String username = "";
        if (currUserVo!=null){
            username = currUserVo.getLogin_name();
        }
        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<HwAppInfo> exitslist = hwApiService.getHwAppInfoList(new HwAppInfo());
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int column = sheet.getColumns();
                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;
                    try {
                        String[] vals = new String[column];
                        for (int c = 0; c < column; c++) {
                            vals[c] = sheet.getCell(c, r).getContents();
                        }
                        try {

                            // 0:产品id 1:华为产品id 2:华为产品名称 4包名 8AppConnect账号
                            HwAppInfo vo = new HwAppInfo();
                            vo.setAppid(vals[0]);
                            vo.setTappid(vals[1]);
                            vo.setTappname(vals[2]);
                            vo.setPackageName(vals[4]);
                            vo.setTaccount(vals[8]);
                            vo.setCreateUser(username);
                            HwAppInfo appInfo = exitslist.stream().
                                    filter(t->(t.getAppid()+"_"+t.getTappid()+"_"+t.getTaccount()).equals(vo.getAppid()+"_"+vo.getTappid()+"_"+vo.getTaccount())).findFirst().orElse(null);
                            if(appInfo!=null){
                                retMap.put(r,"导入华为平台应用传包表失败,已经存在重复数据");
                            }else {
                                int result = hwApiService.saveHwAppBaseInfo(vo);
                                if (result==0){
                                    retMap.put(r,"导入华为平台应用传包表失败");
                                }
                            }
                        }catch (Exception e){
                            logger.error("华为平台应用传包导入失败1:",e);
                            retMap.put(r,"写入华为平台应用传包表失败");
                        }
                    }catch (Exception e){
                        logger.error("华为平台应用传包导入失败2:",e);
                        retMap.put(r,"写入华为平台应用传包表失败");
                    }
                }
            } else {
                return ReturnJson.toErrorJson("上传文件有误，需要.xls格式文件");
            }
        } catch (Exception e) {
            logger.error("华为平台应用传包 batchImport error:",e);
            return ReturnJson.toErrorJson("上传文件失败,请联系管理员!错误信息:"+e.getMessage());
        }
        StringBuffer sb = new StringBuffer();
        if (retMap.size()>0){
            for (Map.Entry<Integer,String> ret:retMap.entrySet()){
                sb.append("第"+ret.getKey()+"行,"+ret.getValue()).append("\t");
            }
            return ReturnJson.toErrorJson(sb.toString());
        }else {
            return ReturnJson.success("全部导入成功");
        }
    }
    /**
     * 更新华为平台应用文件信息
     * @param domain
     * @param clientId
     * @param token
     * @param appId
     * @param files
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    public static JSONObject updateAppFileInfo(String domain, String clientId, String token, String appId,Integer fileType,
                                               List<FileInfo> files,HwAppBaseInfo app){

        JSONObject object = null;
        HttpPut put = new HttpPut(domain + "/publish/v2/app-file-info?appId=" + appId);
        put.setHeader("Authorization", "Bearer " + token);
        put.setHeader("client_id", clientId);
        JSONObject keyString = new JSONObject();
        // Set the language.
        keyString.put("lang", "zh-CN");
        List<PublishFileInfo> fileInfos = new ArrayList<>();
        for (FileInfo fileInfo : files) {
            PublishFileInfo publishFileInfo = new PublishFileInfo();
            publishFileInfo.setImageResolution(fileInfo.getImageResolution());
            publishFileInfo.setImageResolutionSingature(fileInfo.getImageResolutionSingature());
            publishFileInfo.setSize(fileInfo.getSize());
            publishFileInfo.setFileDestUrl(fileInfo.getFileDestUlr());
            publishFileInfo.setFileName(fileInfo.getFileName());
            fileInfos.add(publishFileInfo);
        }
        keyString.put("files", fileInfos);
        keyString.put("fileType", fileType);
        // 设置 截图展现方式
        if ("2".equals(fileType.toString())){
            keyString.put("imgShowType",Integer.parseInt(app.getImgShowType()));
        }
        // 设置 视频展现方式
        if ("1".equals(fileType.toString())){
            keyString.put("videoShowType",Integer.parseInt(app.getVideoShowType()));
        }

        // 设置 敏感权限描述
        if (!BlankUtils.checkBlank(app.getSensitivePermissionDesc())){
            keyString.put("sensitivePermissionDesc",app.getSensitivePermissionDesc());
        }
        StringEntity entity = new StringEntity(keyString.toString(), Charset.forName("UTF-8"));
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        put.setEntity(entity);
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            CloseableHttpResponse httpResponse = httpClient.execute(put);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader br =
                        new BufferedReader(new InputStreamReader(httpResponse.getEntity().getContent(), Consts.UTF_8));
                String result = br.readLine();
                object = JSON.parseObject(result);
            }
        } catch (Exception e) {
            logger.error("updateAppFileInfo error:",e);
        }
        return object;
    }
}
