package com.wbgame.controller.mobile.set.gameSet;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.mobile.QuestionAnswerConfigVo;
import com.wbgame.pojo.mobile.QuestionPopCofigVo;
import com.wbgame.service.mobile.QuestionConfigService;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname QuestionConfigController
 * @Description TODO
 * @Date 2021/8/27 18:11
 */
@RequestMapping("/mobile/question")
@RestController
@CrossOrigin
public class QuestionConfigController {

    @Autowired
    QuestionConfigService questionConfigService;

    @Autowired
    RedisTemplate redisTemplate;


    /**
     * 弹窗查询接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("getPopList")
    public Object popList(HttpServletRequest request, QuestionPopCofigVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        //分页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        //查询
        PageHelper.startPage(pageNo, pageSize);
        List<QuestionPopCofigVo> list = questionConfigService.getQuestionPopList(vo);
        long size = ((Page) list).getTotal();

        JSONObject result = new JSONObject();
        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        return result;
    }

    /**
     * 弹窗操作接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("popHandle")
    public Object popHandle(HttpServletRequest request, QuestionPopCofigVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        String handle = request.getParameter("handle");
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (vo==null){
            return ReturnJson.toErrorJson("必要参数为空");
        }
        vo.setCreateUser(cuser.getLogin_name());
        vo.setModifyUser(cuser.getLogin_name());
        int succ = 0;
        if ("add".equals(handle)){
            succ = questionConfigService.addQuestionPopConfig(vo);
        }else if("update".equals(handle)){
            succ = questionConfigService.updateQuestionPopConfig(vo);
        }else if("del".equals(handle)){
            succ = questionConfigService.delQuestionPopConfig(vo);
        }
        if (succ>0){
            return ReturnJson.success();
        }else {
            return ReturnJson.toErrorJson("操作失败,请联系管理员");
        }
    }


    /**
     * 问卷问题查询接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("getQuestionList")
    public Object questionList(HttpServletRequest request, QuestionAnswerConfigVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        //分页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        //查询
        PageHelper.startPage(pageNo, pageSize);
        List<QuestionAnswerConfigVo> list = questionConfigService.getQuestionConfigList(vo);
        long size = ((Page) list).getTotal();

        JSONObject result = new JSONObject();
        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        return result;
    }

    /**
     * 问卷问题操作接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("questionHandle")
    public Object questionHandle(HttpServletRequest request, QuestionAnswerConfigVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        String handle = request.getParameter("handle");
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (vo==null){
            return ReturnJson.toErrorJson("必要参数为空");
        }
        vo.setCreateUser(cuser.getLogin_name());
        vo.setModifyUser(cuser.getLogin_name());
        int succ = 0;
        if ("add".equals(handle)){
            succ = questionConfigService.addQuestionConfig(vo);
        }else if("update".equals(handle)){
            succ = questionConfigService.updateQuestionConfig(vo);
        }else if("del".equals(handle)){
            succ = questionConfigService.delQuestionConfig(vo);
        }
        if (succ>0){
            return ReturnJson.success();
        }else {
            return ReturnJson.toErrorJson("操作失败,请联系管理员");
        }
    }

}
