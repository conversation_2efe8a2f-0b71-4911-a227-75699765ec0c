package com.wbgame.controller.mobile.query;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.service.YdService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @description: 审核设备查询
 * @author: huangmb
 * @date: 2021/08/30
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/examineDevice")
public class ExamineDeviceController {

    @Autowired
    private YdService ydService;

    @RequestMapping(value = "/list")
    public String list(HttpServletRequest request){
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        CurrUserVo user = (CurrUserVo) request.getAttribute("LoginUser");
        String pid = request.getParameter("pid");
        String channel = request.getParameter("channel");
        String appid = request.getParameter("appid");
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        String order = request.getParameter("order");
        String buy_id = request.getParameter("buy_id");
        Map<String,Object> param = new HashMap<>();
        param.put("appid",appid);
        param.put("pid",pid);
        param.put("channel",channel);
        param.put("order",order);
        param.put("apps",user.getApp_group());
        param.put("chas", StringUtils.getWhereParam(user.getCha_group()));
        param.put("buy_id",buy_id);
        //计算开始时间和结束时间差
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
        DateTime begin = DateTime.parse(startTime, format);
        DateTime end = DateTime.parse(endTime, format);
        List<Map<String,String>> tables = new ArrayList<>();
        while (begin.compareTo(end) <= 0) {
            Map<String,String> map = new HashMap<>();
            map.put("table","np_post2_log_"+begin.toString("yyyyMMdd"));
            map.put("tdate",begin.toString("yyyy-MM-dd"));
            tables.add(map);
            begin = begin.plusDays(1);
        }
        param.put("list",tables);
        PageHelper.startPage(pageNo, pageSize);
        List<Map<String,Object>> list =  ydService.selectExamineDevice(param);
        for (Map<String,Object> map : list) {
            if (map.get("wifiname") != null) {
                map.put("wifiname", new String((byte[]) map.get("wifiname")));
            }
        }
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("data", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }

    @RequestMapping(value = "/export")
    public void export(HttpServletRequest request, HttpServletResponse response){
        CurrUserVo user = (CurrUserVo) request.getAttribute("LoginUser");
        String pid = request.getParameter("pid");
        String channel = request.getParameter("channel");
        String appid = request.getParameter("appid");
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        String order = request.getParameter("order");
        String buy_id = request.getParameter("buy_id");
        Map<String,Object> param = new HashMap<>();
        param.put("appid",appid);
        param.put("pid",pid);
        param.put("channel",channel);
        param.put("order",order);
        param.put("apps",user.getApp_group());
        param.put("chas", StringUtils.getWhereParam(user.getCha_group()));
        param.put("buy_id",buy_id);
        //计算开始时间和结束时间差
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
        DateTime begin = DateTime.parse(startTime, format);
        DateTime end = DateTime.parse(endTime, format);
        List<Map<String,String>> tables = new ArrayList<>();
        while (begin.compareTo(end) <= 0) {
            Map<String,String> map = new HashMap<>();
            map.put("table","np_post2_log_"+begin.toString("yyyyMMdd"));
            map.put("tdate",begin.toString("yyyy-MM-dd"));
            tables.add(map);
            begin = begin.plusDays(1);
        }
        param.put("list",tables);
        List<Map<String,Object>> list =  ydService.selectExamineDevice(param);
        for (Map<String,Object> map : list) {
            if (map.get("wifiname") != null) {
                map.put("wifiname", new String((byte[]) map.get("wifiname")));
            }
        }
        Map head = new LinkedHashMap();
        head.put("createtime","访问时间");
        head.put("app_name","产品名称");
        head.put("mmid","子渠道");
        head.put("projectid","项目id");
        head.put("wifiname","wifi名称");
        head.put("city","地市");
        head.put("buy_id","投放账号");
        head.put("from_ip","访问ip");
        head.put("wifissid","原始wifi名称");
        head.put("brand","手机品牌");
        head.put("pkgList","已安装包名");
        head.put("bs","基站信息");
        head.put("gametimes","游戏时长");
        head.put("lsn","lsn");
        head.put("imei","imei");
        head.put("macaddr","mac");
        head.put("mobilemodel","手机型号");
        head.put("net","网络标识");
        head.put("oaid","oaid");
        head.put("versionName","设备类型");
        head.put("wx","微信标识");
        head.put("android_id","安卓id");
        String export_file_name = request.getParameter("export_file_name");
        if (BlankUtils.checkBlank(export_file_name)){
            export_file_name = "审核设备查询";
        }
        ExportExcelUtil.exportXLSX(response,list,head,export_file_name+"_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }



}
