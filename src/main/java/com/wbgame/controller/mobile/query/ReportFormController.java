package com.wbgame.controller.mobile.query;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.pojo.PkLogVo;
import com.wbgame.pojo.custom.AdTotalHourTwoVo;
import com.wbgame.service.AdService;
import com.wbgame.service.PayService;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description: 监控报表
 * @author: huangmb
 * @date: 2021/02/24
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/reportForm")
public class ReportFormController {

    @Autowired
    SomeService someService;

    @Autowired
    AdService adService;

    @Autowired
    PayService payService;

    /**
     * 游戏时长分布图
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/selectNpGameTimes", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectNpGameTimes(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String tdate = request.getParameter("tdate");
        String pid = request.getParameter("pid");

        JSONObject result = new JSONObject();
        try {
            // 筛除sql敏感字符注入
            if(BlankUtils.sqlValidate(tdate) || BlankUtils.sqlValidate(pid)){
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }

            if(tdate == null || tdate.isEmpty())
                tdate = DateTime.now().toString("yyyy-MM-dd");
            String sql = "SELECT '"+tdate+"' as tdate,projectid,SUM(type1) as type1,SUM(type2) as type2,SUM(type3) as type3,SUM(type4) as type4,SUM(type5) as type5,SUM(type6) as type6 "
                    + " FROM np_gametimes_report where tdate = '"+tdate+"' ";
            if(pid != null && !pid.isEmpty())
                sql = sql + " and projectid = "+pid;

            List<Map<String, Object>> list = adService.queryListMap(sql);

            result.put("ret", 1);
            result.put("data", list);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return result.toJSONString();
    }

    // 游戏次数时长分布图
    @RequestMapping(value="/selectNpTimes", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectNpTimes(HttpServletRequest request,HttpServletResponse response) throws IOException {
        String tdate = request.getParameter("tdate");
        String pid = request.getParameter("pid");

        JSONObject result = new JSONObject();
        try {
            // 筛除sql敏感字符注入
            if(BlankUtils.sqlValidate(tdate) || BlankUtils.sqlValidate(pid)){
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }

            if(tdate == null || tdate.isEmpty())
                tdate = DateTime.now().toString("yyyy-MM-dd");
            String sql = "SELECT '"+tdate+"' as tdate,times,SUM(counts) as counts FROM np_times_report where tdate = '"+tdate+"' ";
            if(pid != null && !pid.isEmpty())
                sql = sql + " and projectid = "+pid;

            sql = sql + " group by times order by times";

            List<Map<String, Object>> list = adService.queryListMap(sql);
            List<Integer> tList = new ArrayList<Integer>();
            for (Map<String, Object> map : list) {
                tList.add(Integer.valueOf(map.get("times")+""));
            }

            // 补全times缺少的数据，使用完整列表删除已有列表
            List<Integer> asList = new ArrayList(Arrays.asList(1,2,3,4,5,6,7,8,9,10,15,100));
            asList.removeAll(tList);
            for (Integer in : asList) {
                Map<String, Object> ob = new HashMap<String, Object>();
                ob.put("times", in);
                ob.put("counts", 0);
                list.add(ob);
            }
            // 按map里times正序排列
            Collections.sort(list, new Comparator<Map<String, Object>>() {
                @Override
                public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                    return (int)o1.get("times") - (int)o2.get("times");
                }
            });

            result.put("ret", 1);
            result.put("data", list);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 上报数据次数分布图
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/selectNpHours", method = {RequestMethod.POST})
    public String selectNpHours(HttpServletRequest request, HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        String tdate = BlankUtils.checkNull(request, "tdate");

        try {
            if (tdate == null || tdate.isEmpty())
                tdate = DateTime.now().toString("yyyy-MM-dd");

            String sql = "SELECT * FROM np_post_hour where tdate = '" + tdate + "';";
            List<Map<String, Object>> list = someService.selectNpMap(sql);

            JSONObject result = new JSONObject();
            result.put("ret", 1);
            result.put("data", list);
            return result.toString();
        } catch (Exception e) {
            e.printStackTrace();

            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
            return result.toString();
        }
    }

    /**
     * 地域分布图
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/selectNpProvince", method = {RequestMethod.POST})
    public String selectNpProvince(HttpServletRequest request, HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        String tdate = BlankUtils.checkNull(request, "tdate");
        String pid = BlankUtils.checkNull(request, "pid");

        try {
            if (tdate == null || tdate.isEmpty())
                tdate = DateTime.now().toString("yyyy-MM-dd");

            String sql = "SELECT tdate,province,SUM(nums) as nums FROM np_province_report where tdate = '" + tdate + "' ";
            if (pid != null && !pid.isEmpty())
                sql = sql + " and projectid = " + pid;

            sql = sql + " group by province order by nums desc";
            List<Map<String, Object>> list = someService.selectNpMap(sql);

            JSONObject result = new JSONObject();
            result.put("ret", 1);
            result.put("data", list);
            return result.toString();
        } catch (Exception e) {
            e.printStackTrace();

            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
            return result.toString();
        }
    }

    /**
     * 网路状态分布
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/selectNpNet", method = {RequestMethod.POST})
    public String selectNpNet(HttpServletRequest request, HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        String tdate = BlankUtils.checkNull(request, "tdate");

        try {
            if (tdate == null || tdate.isEmpty())
                tdate = DateTime.now().toString("yyyy-MM-dd");

            String sql = "SELECT * FROM np_net_report where tdate = '" + tdate + "'";
            sql = sql + " order by nums desc;";
            List<Map<String, Object>> list = someService.selectNpMap(sql);

            JSONObject result = new JSONObject();
            result.put("ret", 1);
            result.put("data", list);
            return result.toString();
        } catch (Exception e) {
            e.printStackTrace();

            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
            return result.toString();
        }
    }

    /**
     * api广告展示/点击实时图表、广告请求实时图表
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/selectAdTotalReport", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectAdTotalReport(HttpServletRequest request,HttpServletResponse response) throws IOException {
        String sdate = request.getParameter("sdate");
        String edate = request.getParameter("edate");
        String adtype = request.getParameter("adtype"); // 广告展示---1，广告点击---2，实时请求---3
        if(BlankUtils.checkBlank(adtype)){ adtype = "1"; }

        JSONObject result = new JSONObject();
        try {
            // 筛除sql敏感字符注入
            if(BlankUtils.sqlValidate(sdate) || BlankUtils.sqlValidate(edate)
                    || BlankUtils.sqlValidate(adtype)){
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }

            if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
                sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
                edate = DateTime.now().toString("yyyy-MM-dd");
            }
            String sql = "select * from ad_total_hour where adtype = "+adtype+" and tdate in ('"+sdate+"','"+edate+"')";

            List<AdTotalHourTwoVo> list = adService.queryHourReport(sql);

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", list.size());
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 对战服务器在线监控
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/selectPkTable", method = {RequestMethod.POST})
    public String selectPkTable(HttpServletRequest request, HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        String datestr = BlankUtils.checkNull(request, "datestr");

        try {
            List<PkLogVo> list = someService.SelectPkLog(datestr);
            JSONObject result = new JSONObject();
            if (list != null && list.size() > 0) {
                Map<String, List<Map<String, Integer>>> resultMap = new HashMap<>();
                for (PkLogVo pkLogVo : list) {
                    List<Map<String, Integer>> hourMap = resultMap.get(pkLogVo.getGamename());
                    if (hourMap != null && hourMap.size() > 0) {
                        Map<String, Integer> map = new HashMap<>();
                        map.put("hour" + pkLogVo.getDatestr(), pkLogVo.getGamenum() / 2);
                        hourMap.add(map);
                    } else {
                        Map<String, Integer> oneMap = new HashMap<>();
                        oneMap.put("hour" + pkLogVo.getDatestr(), pkLogVo.getGamenum() / 2);
                        List<Map<String, Integer>> listMap = new ArrayList<>();
                        listMap.add(oneMap);
                        resultMap.put(pkLogVo.getGamename(), listMap);
                    }
                }
                result.put("ret", 1);
                result.put("totalCount", resultMap.size());
                result.put("data", resultMap);
            }
            return result.toString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
            return result.toString();
        }
    }

}
