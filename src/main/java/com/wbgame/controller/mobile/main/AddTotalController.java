package com.wbgame.controller.mobile.main;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.pojo.ApkSelectNewVo;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.JxlUtil;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @description: 新增汇总查询
 * @author: huangmb
 * @date: 2021/02/09
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/addTotal")
public class AddTotalController {

    @Autowired
    SomeService someService;

    /**
     * 新增用户统计接口
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public String list(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            String start = BlankUtils.checkNull(request, "start");
            String limit = BlankUtils.checkNull(request, "limit");

           /* String replace = request.getParameter("value").replace(" ", "+");
            String value = new String(Base64.decodeBase64(replace),"UTF-8");
            JSONObject obj = JSONObject.parseObject(value);
            if(!BlankUtils.checkBlank(obj.getString("beginDt"))
                    || BlankUtils.checkBlank(obj.getString("endDt"))){
                return "{\"ret\":0,\"msg\":\"request value is error!\"}";
            }*/
            String beginDt = BlankUtils.checkNull(request, "beginDt");
            String endDt = BlankUtils.checkNull(request, "endDt");
            String appName = BlankUtils.checkNull(request, "appName");
            String pid = BlankUtils.checkNull(request, "pid");
            String channel = BlankUtils.checkNull(request, "channel");
            String appid = BlankUtils.checkNull(request, "appid");
            String os = BlankUtils.checkNull(request, "os");
            //按照时间分组
            String groupStr = BlankUtils.checkNull(request, "groupStr");
            //按照产品分组
            String productgroup = BlankUtils.checkNull(request, "productgroup");
            //按照渠道分组
            String channelgroup = BlankUtils.checkNull(request, "channelgroup");
            //按照操作系统分组
            String osgroup = BlankUtils.checkNull(request, "osgroup");
            //按照渠道类型分组
            String channelTypegroup = BlankUtils.checkNull(request, "channelTypegroup");
            String channelType = BlankUtils.checkNull(request, "channelType");

            // pageStart 和 limit设置值
            int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // 当前页
            int pageNo = (pageStart / pageSize) + 1;
            PageHelper.startPage(pageNo, pageSize); // 进行分页
            Map<String, Object> map = new HashMap<>();
            map.put("beginDt", beginDt);
            map.put("endDt", endDt);
            map.put("appName", appName);
            map.put("channel", channel);
            map.put("os", os);
            map.put("pid", pid);
            map.put("appid", appid);
            map.put("productgroup", productgroup);
            map.put("channelgroup", channelgroup);
            map.put("osgroup", osgroup);
            map.put("channelTypegroup", channelTypegroup);
            map.put("channelType", channelType);
            map.put("groupStr", groupStr);

            List<ApkSelectNewVo> list;
            List<ApkSelectNewVo> actList;
            boolean flag;
            boolean hasPid = false;
            if (endDt.substring(0, endDt.lastIndexOf("-")).equals(beginDt.substring(0, beginDt.lastIndexOf("-")))) {
                flag = true;
            } else {
                flag = false;
            }

            if (flag) {
                list = someService.selectApkUserNew(map);
                actList = someService.selectApkUserDau(map);
                if (groupStr.length() > 0 || productgroup.length() > 0 || channelgroup.length() > 0 || osgroup.length() > 0 || channelTypegroup.length() > 0) {
                    hasPid = false;
                } else {
                    hasPid = true;
                }
            } else {
                list = someService.selectApkUserNewGroup(map);
                actList = someService.selectApkUserDauGroup(map);
            }
            for (ApkSelectNewVo add : list) {
                for (ApkSelectNewVo act : actList) {
                    boolean over = true;
                    if (!add.getDt().equals(act.getDt())) {
                        over = false;
                    }

                    if (hasPid) {
                        if (!add.getPid().equals(act.getPid())) {
                            over = false;
                        }
                    }

                    // 匹配	日期、应用名称、渠道名称、渠道类型、系统
                    if (productgroup.length() > 0) {
                        if (!add.getTypeName().equals(act.getTypeName())) {
                            over = false;
                        }
                    }
                    if (channelgroup.length() > 0) {
                        if (!add.getChannel().equals(act.getChannel())) {
                            over = false;
                        }
                    }
                    if (channelTypegroup.length() > 0) {
                        if (!add.getChannelType().equals(act.getChannelType())) {
                            over = false;
                        }
                    }
                    if (osgroup.length() > 0) {
                        if (!add.getOs().equals(act.getOs())) {
                            over = false;
                        }
                    }

                    if (over) {
                        add.setActNum(act.getNewNum());
                        break;
                    }
                }
            }


            long size = ((Page) list).getTotal();
            JSONObject result = new JSONObject();
            result.put("data", list);
            result.put("ret", 1);
            result.put("totalCount", size);
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }


    /**
     * 导出
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void export(HttpServletRequest request, HttpServletResponse response) {

        String fileName = DateTime.now().toString("yyyyMMddHHmmssSS") + ".xls";
        File file = new File(request.getRealPath("/"), fileName);
        try {
          /*  String beginDt = BlankUtils.checkNull(request, "beginDt");
            String endDt = BlankUtils.checkNull(request, "endDt");
            System.out.println("beginDt=="+beginDt+"||"+"endDt===="+endDt);
            String appName = BlankUtils.checkNull(request, "appName");
            String pid = BlankUtils.checkNull(request, "pid");
            String channel = BlankUtils.checkNull(request, "channel");
            String appid = BlankUtils.checkNull(request, "appid");
            String os = BlankUtils.checkNull(request, "os");


            Map<String,Object> map = new HashMap<>();
            map.put("beginDt",beginDt);
            map.put("endDt",endDt);
            map.put("appName",appName);
            map.put("channel",channel);
            map.put("os",os);
            map.put("pid",pid);
            map.put("appid",appid);

            List<ApkSelectNewVo> list = someService.selectApkUserNew(map);*/
            String beginDt = BlankUtils.checkNull(request, "beginDt");
            String endDt = BlankUtils.checkNull(request, "endDt");
            String appName = BlankUtils.checkNull(request, "appName");
            String pid = BlankUtils.checkNull(request, "pid");
            String channel = BlankUtils.checkNull(request, "channel");
            String appid = BlankUtils.checkNull(request, "appid");
            String os = BlankUtils.checkNull(request, "os");
            //按照时间分组
            String groupStr = BlankUtils.checkNull(request, "groupStr");
            //按照产品分组
            String productgroup = BlankUtils.checkNull(request, "productgroup");
            //按照渠道分组
            String channelgroup = BlankUtils.checkNull(request, "channelgroup");
            //按照操作系统分组
            String osgroup = BlankUtils.checkNull(request, "osgroup");
            //按照渠道类型分组
            String channelTypegroup = BlankUtils.checkNull(request, "channelTypegroup");
            String channelType = BlankUtils.checkNull(request, "channelType");

            Map<String, Object> map = new HashMap<>();
            map.put("beginDt", beginDt);
            map.put("endDt", endDt);
            map.put("appName", appName);
            map.put("channel", channel);
            map.put("os", os);
            map.put("pid", pid);
            map.put("appid", appid);
            map.put("productgroup", productgroup);
            map.put("channelgroup", channelgroup);
            map.put("osgroup", osgroup);
            map.put("channelTypegroup", channelTypegroup);
            map.put("channelType", channelType);
            map.put("groupStr", groupStr);

            List<ApkSelectNewVo> list;
            List<ApkSelectNewVo> actList;
            boolean flag;
            boolean hasPid = false;
            if (endDt.substring(0, endDt.lastIndexOf("-")).equals(beginDt.substring(0, beginDt.lastIndexOf("-")))) {
                flag = true;
            } else {
                flag = false;
            }
            if (flag) {
                list = someService.selectApkUserNew(map);
                actList = someService.selectApkUserDau(map);
                if (groupStr.length() > 0 || productgroup.length() > 0 || channelgroup.length() > 0 || osgroup.length() > 0 || channelTypegroup.length() > 0) {
                    hasPid = false;
                } else {
                    hasPid = true;
                }
            } else {
                list = someService.selectApkUserNewGroup(map);
                actList = someService.selectApkUserDauGroup(map);
            }
            for (ApkSelectNewVo add : list) {
                for (ApkSelectNewVo act : actList) {
                    boolean over = true;
                    if (!add.getDt().equals(act.getDt())) {
                        over = false;
                    }

                    if (hasPid) { // 按月分组统计
                        if (!add.getPid().equals(act.getPid())) {
                            over = false;
                        }
                    }

                    // 匹配	应用名称、渠道名称、渠道类型、系统
                    if (productgroup.length() > 0) {
                        if (!add.getTypeName().equals(act.getTypeName())) {
                            over = false;
                        }
                    }
                    if (channelgroup.length() > 0) {
                        if (!add.getChannel().equals(act.getChannel())) {
                            over = false;
                        }
                    }
                    if (channelTypegroup.length() > 0) {
                        if (!add.getChannelType().equals(act.getChannelType())) {
                            over = false;
                        }
                    }
                    if (osgroup.length() > 0) {
                        if (!add.getOs().equals(act.getOs())) {
                            over = false;
                        }
                    }

                    if (over) {
                        add.setActNum(act.getNewNum());
                        break;
                    }
                }
            }


            WritableWorkbook wwb = Workbook.createWorkbook(file);
            //创建Excel工作表               创建Excel表的左下角表名(名称，第几个)
            WritableSheet sheet = wwb.createSheet("apk新增用户报表", 0);//创建sheet
            //ws.mergeCells(0, 0, 2, 1);//合并单元格(左列，左行，右列，右行)从第1行第1列到第2行第3列

            String[] title = {"日期", "渠道名称", "渠道类型", "渠道", "版本号", "应用名称", "渠道新增", "渠道活跃", "产品类型", "操作系统"};

            for (int i = 0; i < title.length; i++) {//添加标题
                Label l = new Label(i, 0, title[i], JxlUtil.getTitle());
                sheet.setColumnView(i, 20);
                sheet.addCell(l);
            }

            for (int k = 0; k < list.size(); k++) { // 每一行的内容
                String[] vals;
                if (hasPid) {
                    String[] temp = {
                            list.get(k).getDt(),
                            list.get(k).getChannel(),
                            list.get(k).getChannelType().equals("1") ? "自推广渠道" : "市场渠道",
                            list.get(k).getPid(),
                            list.get(k).getVer(),
                            list.get(k).getAppName(),
                            list.get(k).getNewNum() + "",
                            list.get(k).getActNum() + "",
                            list.get(k).getTypeName(),
                            list.get(k).getOs() + ""
                    };
                    vals = temp;
                } else {
                    String[] temp = {
                            (!flag || groupStr.length() > 0) ? list.get(k).getDt() : "",
                            channelgroup.length() > 0 ? list.get(k).getChannel() : "",
                            channelTypegroup.length() > 0 ? list.get(k).getChannelType().equals("1") ? "自推广渠道" : "市场渠道" : "",
                            "",
                            "",
                            "",
                            list.get(k).getNewNum() + "",
                            list.get(k).getActNum() + "",
                            productgroup.length() > 0 ? list.get(k).getTypeName() : "",
                            osgroup.length() > 0 ? list.get(k).getOs() + "" : ""
                    };
                    vals = temp;
                }


                for (int m = 0; m < title.length; m++) { // 每一列的内容，从第二行开始插入
                    Label l = new Label(m, k + 1, vals[m], JxlUtil.getNormolCell());
                    sheet.addCell(l);
                }
            }
	     /*ws.setColumnView(0, 20);//设置列宽
	       ws.setRowView(0, 400);//设置行高 */
            wwb.write();
            wwb.close();

        } catch (Exception e) {
            e.printStackTrace();
            fileName = "无数据.xls";
        }

        response.addHeader("Cache-Control", "no-cache");
        response.addDateHeader("Expries", 0);
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
        OutputStream pw = null;
        try {
            pw = response.getOutputStream();
            FileInputStream input = new FileInputStream(file);
            int length = 0;
            byte buffer[] = new byte[2048];
            while ((length = input.read(buffer)) != -1) {
                pw.write(buffer, 0, length);
            }
            input.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                pw.close();
                file.delete();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }



}
