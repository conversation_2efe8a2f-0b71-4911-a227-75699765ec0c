package com.wbgame.controller.product;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.cloudfront.AmazonCloudFrontClient;
import com.amazonaws.services.cloudfront.model.CreateInvalidationRequest;
import com.amazonaws.services.cloudfront.model.CreateInvalidationResult;
import com.amazonaws.services.cloudfront.model.InvalidationBatch;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.service.AdService;
import com.wbgame.utils.BlankUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

@CrossOrigin
@RestController
public class TikgameController {

	Logger logger = LoggerFactory.getLogger(TikgameController.class);

	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private AdService adService;

	@Value("${S3.accessKeyId}")
	private String accessKeyId;
	@Value("${S3.accessKeySecret}")
	private String accessKeySecret;
	@Value("${S3.bucketName}")
	private String bucketName;
	@Value("${S3.region}")
	private String regionName;

	/**
	 * 谷歌门户网站游戏配置.查询
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/tikgame/config", method={RequestMethod.GET, RequestMethod.POST})
	public String list(HttpServletRequest request,HttpServletResponse response) throws IOException {

		String[] args = {"appkey"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);

		JSONObject result = new JSONObject();
		try {
			/** 查询条件 */
			String name = request.getParameter("name");
			String appkey = request.getParameter("appkey");
			String aclass = request.getParameter("class");
			String mark = request.getParameter("mark");
			String status = request.getParameter("status");


			String query = "select * from tikgame_data_config where 1=1 ";
			if(!BlankUtils.checkBlank(name)){
				query += " and `name` like CONCAT('%','"+name+"','%') ";
			}
			if(!BlankUtils.checkBlank(appkey)){
				query += " and `appKey` = '"+appkey+"' ";
			}
			if(!BlankUtils.checkBlank(status)){
				query += " and `status` = '"+status+"' ";
			}
			if(!BlankUtils.checkBlank(aclass) && aclass.split(",").length > 0){
				List<String> where = new ArrayList<String>();
				for (String key : aclass.split(",")) {
					where.add(" `class` like CONCAT('%','"+key+"','%') ");
				}
				String join = String.join(" or ", where);

				query += " and ( "+join+" ) ";
			}
			if(!BlankUtils.checkBlank(mark) && mark.split(",").length > 0){
				List<String> where = new ArrayList<String>();
				for (String key : mark.split(",")) {
					where.add(" `mark` like CONCAT('%','"+key+"','%') ");
				}
				String join = String.join(" or ", where);
				
				query += " and ( "+join+" ) ";
			}
			query += " order by createtime desc";

			PageHelper.startPage(paramMap);
			List<Map<String, Object>> list = adService.queryListMap(query);
			long size = ((Page) list).getTotal();

			list.forEach(act -> {
				act.put("createtime", act.get("createtime")+"");
				act.put("endtime", act.get("endtime")+"");

			});

			result.put("errCode", "200");
			result.put("errMsg", "success");
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("errCode", "500");
			result.put("errMsg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}

	/**
	 * 谷歌门户网站游戏配置.操作
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value = "/tikgame/handle", method={RequestMethod.GET,RequestMethod.POST})
	public String handle(HttpServletRequest request, HttpServletResponse response) {

		// token验证
		String login_name = "zz";
		try {
			String token = request.getParameter("token");
			Object object = redisTemplate.opsForValue().get(token);
			if(object != null){
				JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(object));
				login_name = (json.getString("login_name")==null?"zz":json.getString("login_name"));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}


		// 获取到所有请求参数的Map
		Map<String, String> valMap = new HashMap<String, String>();

		Map<String, String[]> parameterMap = request.getParameterMap();
		for (Entry<String, String[]> next : parameterMap.entrySet()) {
			valMap.put(next.getKey(), next.getValue()[0]);
		}

		int result = 0;
		try {
			valMap.put("cuser", login_name);
			valMap.put("euser", login_name);


			if ("add".equals(request.getParameter("handle"))) {
				String sql = "insert into tikgame_data_config(appkey,tip,`name`,hot,Votes,`like`,`class`,mark,dis,img,runSrcL,posAdj,`status`,cuser,createtime,euser,endtime) "+
						"value(#{obj.appkey},#{obj.tip},#{obj.name},#{obj.hot},#{obj.Votes},#{obj.like},#{obj.class},#{obj.mark},#{obj.dis},#{obj.img},#{obj.runSrcL},#{obj.posAdj},#{obj.status},#{obj.cuser},now(),#{obj.euser},now())";
				result = adService.execSqlHandle(sql, valMap);


			} else if ("edit".equals(request.getParameter("handle"))) {
				String sql = "update tikgame_data_config set tip=#{obj.tip},`name`=#{obj.name},hot=#{obj.hot},Votes=#{obj.Votes},`like`=#{obj.like},`class`=#{obj.class},mark=#{obj.mark},dis=#{obj.dis},img=#{obj.img},runSrcL=#{obj.runSrcL},posAdj=#{obj.posAdj},status=#{obj.status},euser=#{obj.euser},endtime=now() "+
						"where appkey=#{obj.appkey} ";
				result = adService.execSqlHandle(sql, valMap);

			} else if ("del".equals(request.getParameter("handle"))) {
				String sql = "delete from tikgame_data_config where appkey in ("+valMap.get("appkey")+") ";
				result = adService.execSqlHandle(sql, valMap);
			}

			if (result > 0)
				return "{\"errCode\":200,\"errMsg\":\"操作成功!\"}";
			else
				return "{\"errCode\":500,\"errMsg\":\"无效操作!\"}";

		} catch (Exception e) {
			e.printStackTrace();
			return "{\"errCode\":500,\"errMsg\":\"操作失败!\"}";
		}
	}


	@RequestMapping(value="/tikgame/upload", method={RequestMethod.POST})
	public String batchImport(@RequestParam(value="file_name") MultipartFile file, String dir, HttpServletRequest request, HttpServletResponse response) throws IOException{

		JSONObject obj = new JSONObject();
		try{
			if(null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".zip")){

				String ranDir = BlankUtils.getRandomStr(4);
				if(!BlankUtils.checkBlank(dir)){
					/** 使用指定的目录 */
					ranDir = dir;
				}
				Path ranPath = Paths.get("/home/<USER>/"+ranDir);
				if(Files.notExists(ranPath)){
					Files.createDirectory(ranPath);
				}

				/** 
				 * 先将压缩文件写入地址
				 * 解压后写入随机目录
				 * 将该随机目录传入aws的S3
				 * 刷新cdn缓存
				 */
				String fileName = file.getOriginalFilename();
				file.transferTo(Paths.get("/home/<USER>/"+fileName).toFile());

				String[] cmd = new String[] { "/bin/sh", "-c", "unzip /home/<USER>/'"+fileName+"' -d /home/<USER>/"+ranDir+" "};
				Runtime.getRuntime().exec(cmd);


				AmazonS3 amazonS3Client =  amazonS3();
				DirectoryStream<Path> stream = Files.newDirectoryStream(ranPath);
        		for (Path path2 : stream) { // 多集目录

        			Files.walkFileTree(path2, new SimpleFileVisitor<Path>() { // 递归遍历单集目录下所有内容
        				@Override
        		        public FileVisitResult visitFile(Path path3, BasicFileAttributes attrs) throws IOException{
        					String replace = path3.toString().replace("\\", "/").replace("/home/<USER>/", "");
        					String contentType = Files.probeContentType(path3);
        					System.out.println(contentType+"\t"+path3.getFileName()+"\t"+replace);

        					File file3 = path3.toFile();
        					// 通过元配置，允许公开访问
        					ObjectMetadata objectMetadata = new ObjectMetadata();
        			        objectMetadata.setHeader("x-amz-acl", "public-read");
        			        if(contentType != null){
        			        	objectMetadata.setContentType(contentType);
        			        	objectMetadata.setContentLength(file3.length());
        			        }
        			        
        			        PutObjectResult putObjectResult = amazonS3Client.putObject(
        			        		new PutObjectRequest(bucketName, "game/"+replace, file3).withMetadata(objectMetadata));

        		            return FileVisitResult.CONTINUE;
        		        }
        			});
        		}
				refreshCloudFront("/game/"+ranDir+"/*");


				obj.put("errCode", "200");
				obj.put("errMsg", "上传文件成功");
				obj.put("img", "./game/"+ranDir+"/"+ranDir+".png");
				obj.put("runSrcL", "./game/"+ranDir);
			}else{
				obj.put("errCode", "500");
				obj.put("errMsg", "上传文件有误，需要.zip文件!");
			}

		}catch (Exception e) {
			//上传异常
			e.printStackTrace();
			obj.put("errCode", "500");
			obj.put("errMsg", "导入异常");
		}
		return obj.toJSONString();
	}

	/**
	 * 刷新cdn指定目录缓存，/game/mwgf/test11/*
	 * 
	 * @return
	 */
	public String refreshCloudFront(String... spath) {

		//		AWSCredentials awsCredentials = new DefaultAWSCredentialsProviderChain().getCredentials();

		AWSCredentials awsCredentials = new BasicAWSCredentials(accessKeyId, accessKeySecret);
		AmazonCloudFrontClient client = new AmazonCloudFrontClient(awsCredentials);

		com.amazonaws.services.cloudfront.model.Paths invalidation_paths = 
				new com.amazonaws.services.cloudfront.model.Paths().withItems(spath).withQuantity(spath.length);
		InvalidationBatch invalidation_batch = new InvalidationBatch(invalidation_paths, (DateTime.now().getMillis()/1000)+"");
		CreateInvalidationRequest invalidation = new CreateInvalidationRequest("E3KZBXCVVD9NFI", invalidation_batch);
		CreateInvalidationResult ret = client.createInvalidation(invalidation);

		System.out.println("Invalidation result: " + ret.toString());

		return null;
	}

	/**
	 * 获取S3操作的client对象
	 * @return
	 */
	public AmazonS3 amazonS3() {
		AWSCredentials awsCredentials = new BasicAWSCredentials(accessKeyId, accessKeySecret);
		AmazonS3ClientBuilder builder = AmazonS3ClientBuilder.standard().withCredentials(new AWSStaticCredentialsProvider(awsCredentials));
		//设置S3的地区
		builder.setRegion(regionName);
		AmazonS3 s3Client = builder.build();

		return s3Client;
	}

	public InputStream download(String key){
		S3Object object = amazonS3().getObject(new GetObjectRequest(bucketName, key));
		return object.getObjectContent();  
	}

}
