package com.wbgame.controller;

import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.XiaomiViolationRecordDTO;
import com.wbgame.pojo.XiaomiViolationRecordVo;
import com.wbgame.service.ViolationRecordService;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description xiaomi违规记录表
 * @Date 2024/11/18 15:59
 */
@CrossOrigin
@RestController
@RequestMapping("/violation/xiaomi")
public class ViolationRecordController {

    @Autowired
    private ViolationRecordService violationRecordService;

    /**
     * 手动触发拉取小米违规记录数据
     *
     * @param dto 条件：开始时间(必填)，结束时间(必填)，账号(非必填)
     * @return 结果
     */
    @PostMapping("/fetch")
    public Result<String> fetch(@RequestBody XiaomiViolationRecordDTO dto) {
        violationRecordService.pullViolationRecords(dto.getStartTime(), dto.getEndTime(), dto.getAccount(),false,false);
        return ResultUtils.success(Constants.OK);
    }

    /**
     * 拉取的小米违规数据，最大活跃模块等数据
     *
     * @param dto 拉取时间段参数
     * @return 拉取结果
     */
    @PostMapping("/fetchTransFields")
    public Result<String> fetchTransFields(@RequestBody XiaomiViolationRecordDTO dto) {
        violationRecordService.pullTransformFields(dto.getStartTime(), dto.getEndTime());
        return ResultUtils.success(Constants.OK);
    }

    /**
     * 查询小米违规数据所有的状态类型
     *
     * @return 查询结果
     */
    @GetMapping("/queryStatus")
    public Result<List<String>> queryStatus() {
        return violationRecordService.queryStatus();
    }

    /**
     * 根据条件分页查询小米违规数据
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    @ControllerLoggingEnhancer
    @PostMapping("/list")
    public Result<List<XiaomiViolationRecordVo>> queryList(@RequestBody XiaomiViolationRecordDTO dto) {
        //分页页码参数校验
        dto.checkPageParams();
        //赋值分页标记
        dto.setPageFlag(true);
        return violationRecordService.queryList(dto);
    }

    /**
     * 根据条件查询小米违规数据
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    @ControllerLoggingEnhancer
    @PostMapping("/common")
    public Result<List<XiaomiViolationRecordVo>> queryListNotLimit(XiaomiViolationRecordDTO dto) {
        //赋值不分页标记
        dto.setPageFlag(false);
        return violationRecordService.queryList(dto);
    }


    /**
     * 小米违规数据导出接口
     *
     * @param dto      导出条件参数
     * @param response HttpServletResponse
     */
    @PostMapping("/export")
    @ControllerLoggingEnhancer
    public void export(XiaomiViolationRecordDTO dto, HttpServletResponse response) {
        //不分页查询违规数据
        dto.setPageFlag(false);
        Result<List<XiaomiViolationRecordVo>> queryResult = violationRecordService.queryList(dto);
        //封装标题列
        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = dto.getValue().split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        ExportExcelUtil.exportXLSX2(response, queryResult.getData(), head, dto.getFile_name() + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }


}
