package com.wbgame.controller;

import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.OppoViolationRecordDTO;
import com.wbgame.pojo.OppoViolationRecordVo;
import com.wbgame.service.OppoViolationRecordService;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;

/**
 * <AUTHOR>
 * @Description oppo违规记录表
 * @Date 2025/03/06 16:00
 */
@CrossOrigin
@RestController
@RequestMapping("/violation/oppo")
public class OppoViolationRecordController {

    @Autowired
    private OppoViolationRecordService violationRecordService;

    /**
     * 手动触发拉取 oppo 违规记录数据
     */
    @ControllerLoggingEnhancer
    @GetMapping("/fetch")
    public Object fetch(int day) {
        violationRecordService.fetchEmails(day, "<EMAIL>", LOGIN_USER_NAME.get());
        return ResultUtils.success();
    }

    /**
     * 拉取的小米违规数据，最大活跃模块等数据
     *
     * @param dto 拉取时间段参数
     * @return 拉取结果
     */
    @PostMapping("/fetchTransFields")
    public Result<String> fetchTransFields(@RequestBody OppoViolationRecordDTO dto) {
        violationRecordService.fetchPlatformData(dto.getStartTime(), dto.getEndTime());
        return ResultUtils.success(Constants.OK);
    }


    /**
     * 根据条件分页查询小米违规数据
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    @ControllerLoggingEnhancer
    @PostMapping("/list")
    public Object queryList(@RequestBody OppoViolationRecordDTO dto) {
        //分页页码参数校验
        dto.checkPageParams();
        //赋值分页标记
        dto.setPageFlag(true);
        return violationRecordService.queryList(dto);
    }

    /**
     * 小米违规数据导出接口
     *
     * @param dto      导出条件参数
     * @param response HttpServletResponse
     */
    @PostMapping("/export")
    @ControllerLoggingEnhancer
    public void export(OppoViolationRecordDTO dto, HttpServletResponse response) {
        //不分页查询违规数据
        dto.setPageFlag(false);
        Result<List<OppoViolationRecordVo>> queryResult = violationRecordService.queryList(dto);
        //封装标题列
        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = dto.getValue().split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        ExportExcelUtil.exportXLSX2(response, queryResult.getData(), head, dto.getExport_file_name() + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }
}
