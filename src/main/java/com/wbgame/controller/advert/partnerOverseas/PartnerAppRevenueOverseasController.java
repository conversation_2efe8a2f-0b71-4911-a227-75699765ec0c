package com.wbgame.controller.advert.partnerOverseas;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.service.AdService;
import com.wbgame.service.PartnerService;
import com.wbgame.pojo.CurrUserVo;

import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 合作产品收支稽核查询 (海外版)
 * @author: caow
 * @date: 2025/05/21
 **/
@CrossOrigin
@RestController
@RequestMapping("/advert/partnerAppRevenueNewOverseas")
public class PartnerAppRevenueOverseasController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private PartnerService partnerService;
    @Autowired
    private AdService adService;

    Logger logger = LoggerFactory.getLogger(PartnerAppRevenueOverseasController.class); // Updated Logger


    /**
     * 查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="list", method={RequestMethod.GET, RequestMethod.POST})
    public String list(HttpServletRequest request, HttpServletResponse response) throws IOException {
        
        String[] args = {"sdate","edate","appid","app_category","order_str"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);
        paramMap.put("app_category_filter", "15,16");

        // 按照单日，月度，年度汇总
        String group = "DATE_FORMAT(tdate,'%Y-%m-%d')";
        String month_group = request.getParameter("month_group");
        String year_group = request.getParameter("year_group");
        if(!BlankUtils.checkBlank(month_group))
            group = "DATE_FORMAT(tdate,'%Y-%m')";
        else if(!BlankUtils.checkBlank(year_group))
            group = "DATE_FORMAT(tdate,'%Y')";

        // 国家维度
//        String country_group = request.getParameter("country_group");
//        if(!BlankUtils.checkBlank(country_group)) {
//            group = "country_code,"+group;
//        }

        paramMap.put("group", group);
        
        JSONObject result = new JSONObject();
        try {
            PageHelper.startPage(paramMap);
            List<Map<String, Object>> list = partnerService.selectPartnerAppRevenueNewOverseas(paramMap);
            long size = ((Page) list).getTotal();
            
            // 汇总数据
            result.put("total", partnerService.selectPartnerAppRevenueNewSumOverseas(paramMap));

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 导出
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="export", method={RequestMethod.GET, RequestMethod.POST})
    public void export(HttpServletRequest request,HttpServletResponse response) {

        String[] args = {"sdate","edate","appid","app_category","order_str"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);
        paramMap.put("app_category_filter", "15,16"); // Added app_category_filter for overseas

        // 按照单日，月度，年度汇总
        String group = "DATE_FORMAT(tdate,'%Y-%m-%d')";
        String month_group = request.getParameter("month_group");
        String year_group = request.getParameter("year_group");
        if(!BlankUtils.checkBlank(month_group))
            group = "DATE_FORMAT(tdate,'%Y-%m')";
        else if(!BlankUtils.checkBlank(year_group))
            group = "DATE_FORMAT(tdate,'%Y')";

        paramMap.put("group", group);

        List<Map<String, Object>> contentList = partnerService.selectPartnerAppRevenueNewOverseas(paramMap);

        Map<String, Map<String, Object>> appMap = adService.getAppInfoMap();
        contentList.forEach(act -> {
            Map<String, Object> app = appMap.get(act.get("appid")+"");
            if(app != null)
                act.put("appname", app.get("app_name"));
            
            if(act.get("ischeck") != null){
                String ischeck = act.get("ischeck")+"";
                act.put("ischeck", "2".equals(ischeck)?"已调整":"1".equals(ischeck)?"已核查":"未审核");
            }
        });

        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String value = BlankUtils.checkNull(request,"value");
        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                headerMap.put(s[0],s[1]);
            }
        }catch (Exception e) {
            e.printStackTrace();
        }

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "海外-合作产品收支稽核查询_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx"; // Updated filename

        ExportExcelUtil.exportXLSX(response, contentList, headerMap, fileName);
    }

    
    /**
     * 合作方产品审核
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/check", method={RequestMethod.POST})
    public String check(HttpServletRequest request,HttpServletResponse response) throws IOException {

        String tdate = request.getParameter("tdate");
        String token = request.getParameter("token");
        CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);

        if(BlankUtils.sqlValidate(tdate) || BlankUtils.checkBlank(tdate))
            return "{\"ret\":0,\"msg\":\"审核日期不能为空！\"}";

        /** 检验已审核通过的日期数据，已审核过则不允许覆盖 */
        String query = "select ischeck from partner_app_revenue_total_overseas where tdate = '"+tdate+"' and (ischeck='1')";
        List<String> check = adService.queryListString(query);
        if(check != null && check.size() > 0){
            return "{\"ret\":0,\"msg\":\"该日期数据已审核，无需重复操作！\"}";
        }
        
        
        try {
            // 操作行为记录
            String sql = "insert into partner_channel_check_log(tdate,ischeck,flag,cuser,date) values('"+tdate+"', 1, 'appRevenueOverseas', '"+cuser.getLogin_name()+"', now())"; // Updated flag
            adService.execSql(sql);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            // 将投放和收入数据，写入到合作方产品收支 umeng_channel_cost
            Map<String, String> paramMap = new HashMap<String, String>();
            paramMap.put("sdate", tdate);
            paramMap.put("edate", tdate);
            paramMap.put("app_category_filter", "15,16");
            partnerService.checkPartnerAppCostTotalOverseas(paramMap);

            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败，请联系管理员排查！\"}";
        }
    }

    /**
     * 修改付费收入退款金额，审核前后均可
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/handle", method = {RequestMethod.POST})
    public String handle(String handle, HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

        String[] args = {"tdate","appid","pay_revenue","refund_revenue"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);
        paramMap.put("app_category_filter", "15,16"); // Added app_category_filter for overseas
        
        if(BlankUtils.checkBlank(paramMap.get("tdate")) 
                || BlankUtils.checkBlank(paramMap.get("appid"))
                || BlankUtils.checkBlank(paramMap.get("pay_revenue"))
                || BlankUtils.checkBlank(paramMap.get("refund_revenue"))){
            return "{\"ret\":0,\"msg\":\"请求参数缺少必要参数！\"}";
        }
        
        try {
            if("edit".equals(handle)){
                partnerService.updatePartnerAppCostBillingOverseas(paramMap); // Service method now handles overseas logic
            }

            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败，请联系管理员排查！\"}";
        }
    }
    
    
    /**
     * 同步付费收入和退款金额到稽核页面
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/syncBill", method={RequestMethod.POST})
    public String syncBill(HttpServletRequest request, HttpServletResponse response) {

        String tdate = request.getParameter("tdate");
        String token = request.getParameter("token");
        CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);

        if(BlankUtils.sqlValidate(tdate) || BlankUtils.checkBlank(tdate))
            return "{\"ret\":0,\"msg\":\"同步日期不能为空！\"}";
        
        // TODO: [海外版] 此查询是否也需要考虑 app_category? 
        String query = "select ischeck from partner_app_revenue_total_overseas where tdate = '"+tdate+"' and (ischeck='1' or ischeck='2')";
        List<String> check = adService.queryListString(query);
        if(check != null && check.size() > 0){
            return "{\"ret\":0,\"msg\":\"该日期有支付数据已调整或已审核，请联系管理员确认是否要覆盖！\"}";
        }

        try {
            // 操作行为记录
            String sql = "insert into partner_channel_check_log(tdate,ischeck,flag,cuser,date) values('"+tdate+"', 1, 'billSyncOverseas', '"+cuser.getLogin_name()+"', now())";
            adService.execSql(sql);
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        try{
            Map<String, String> contextParams = new HashMap<>();
            contextParams.put("tdate", tdate);
            contextParams.put("app_category_filter", "15,16"); // Pass filter to service method

            boolean resp = partnerService.syncPartnerAppBillingNewOverseas(tdate,contextParams); // Service method now handles overseas logic
            if(resp)
                return "{\"ret\":1,\"msg\":\"同步成功！\"}";
            else
                return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
        }
    }
    
}