package com.wbgame.controller.advert.set.paramSet;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.mapper.master.YdMapper;
import com.wbgame.mapper.tfxt.TfxtMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.DnAdConfigVo;
import com.wbgame.pojo.ExtendVo;
import com.wbgame.service.AdService;
import com.wbgame.service.SomeService;
import com.wbgame.service.YdService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpClientUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 国内广告源配置
 * @author: huangmb
 * @date: 2021/06/25
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/advert/advertSourceConfig")
public class AdvertSourceConfigController {

    @Autowired
    private SomeService someService;

    @Autowired
    private YdService ydService;

    @Autowired
    private TfxtMapper tfxtMapper;

    @Autowired
    private AdService adService;


    /**
     * 新版广告源配置
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/list", method={RequestMethod.POST})
    public String list(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try{
            String start = BlankUtils.checkNull(request, "start");
            String limit = BlankUtils.checkNull(request, "limit");

            int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // 当前页
            int pageNo = (pageStart / pageSize) + 1;
            PageHelper.startPage(pageNo, pageSize); // 进行分页
            String ad_sid = BlankUtils.checkNull(request, "ad_sid");
            String code = BlankUtils.checkNull(request, "code");
            String app_id = BlankUtils.checkNull(request, "appid");
            String tj_agent = BlankUtils.checkNull(request, "tj_agent");
            Map<Object, Object> parmMap = new HashMap<>();
            parmMap.put("ad_sid", ad_sid);
            parmMap.put("code", code);
            parmMap.put("app_id", app_id);
            parmMap.put("tj_agent", tj_agent.toLowerCase());
            List<ExtendVo> list = someService.selectNewAdConfig(parmMap);
            long size = ((Page) list).getTotal();
            JSONObject result = new JSONObject();
            result.put("data", list);
            result.put("ret",1);
            result.put("totalCount", size);
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 新版广告源配置
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/handle", method={RequestMethod.POST})
    public String handle(ExtendVo np,String handle,CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
            int ret = 0;
            if("add".equals(handle)){
                String agent = np.getAgent();
                //qpay单独处理
                if(agent != null && agent.contains("Qpay"))
                {
                    np.setAgent("Qpay");
                }
                //ad_sid小写处理
                np.setAd_sid(np.getAd_sid().toLowerCase());
                ret = someService.insertNewAdConfig(np);

            }
            if("update".equals(handle)){
                String agent = np.getAgent();
                //qpay单独处理
                if(agent != null && agent.contains("Qpay"))
                {
                    np.setAgent("Qpay");
                }
                np.setAd_sid(np.getAd_sid().toLowerCase());
                ret = someService.updateNewAdConfig(np);
            }
            JSONObject result = new JSONObject();
            if(ret > 0){

                DnAdConfigVo dnAdConfigVo = new DnAdConfigVo();
                dnAdConfigVo.setAdSid(np.getAd_sid());
                dnAdConfigVo.setAppid(np.getAppid());
                dnAdConfigVo.setCode(np.getCode());

                List <DnAdConfigVo> list = new ArrayList<>();
                list.add(dnAdConfigVo);
                int rs = ydService.updateAdinfoKeys(list);
                if(rs > 0){
                    result.put("ret",1);
                    result.put("msg","操作成功");
                }

            }else {
                result.put("ret",0);
                result.put("msg","操作失败");
            }
            return result.toJSONString();
    }

    /**
     * 删除新版广告源配置
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/delete", method={RequestMethod.POST})
    public String delete(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
            String ids = BlankUtils.checkNull(request, "ids");
            JSONObject result = new JSONObject();
            int res = someService.deleteNewAdConfig(ids);
            if(res > 0){
                result.put("ret",1);
                result.put("msg","删除成功");
            }else {
                result.put("ret",0);
                result.put("msg","删除失败");
            }
            return result.toJSONString();
    }

    /**
     *新版广告源批量复制
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/copy", method={RequestMethod.POST})
    public String copy(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {

            String ad_sids = BlankUtils.checkNull(request,"ad_sids");
           /* String[] arr = ad_sids.split(",");
            List<String> slist = new ArrayList<>();
            for(String str : arr){
                slist.add(str);
            }*/
            String old_key = BlankUtils.checkNull(request,"old_key");
            String new_key = BlankUtils.checkNull(request,"new_key");
            //String old_appid = BlankUtils.checkNull(request,"old_appid");
            String new_appid = BlankUtils.checkNull(request,"new_appid");
            JSONObject result = new JSONObject();
            List<ExtendVo> list = someService.selectNewAdConfigBySid(ad_sids);

            List <DnAdConfigVo> dnAdConfiglist = new ArrayList<>();

            for(ExtendVo extendVo : list){
                if(!StringUtils.isEmpty(old_key) && !StringUtils.isEmpty(new_key)){
                    extendVo.setAd_sid(extendVo.getAd_sid().replace(old_key, new_key));
                }
                if(!StringUtils.isEmpty(new_appid)){
                    extendVo.setAppid(new_appid);
                }

                DnAdConfigVo dnAdConfigVo = new DnAdConfigVo();
                dnAdConfigVo.setAdSid(extendVo.getAd_sid());
                dnAdConfigVo.setAppid(extendVo.getAppid());
                dnAdConfigVo.setCode(extendVo.getCode());
                dnAdConfiglist.add(dnAdConfigVo);
            }
            int res = 0;
            int rs = 0;
            if(list != null && list.size() > 0){
                res = someService.batchInsertNewAdConfig(list);
                rs= ydService.updateAdinfoKeys(dnAdConfiglist);
            }
            if(res > 0 && rs > 0){
                result.put("ret",1);
                result.put("msg","操作成功");
            }else {
                result.put("ret",0);
                result.put("msg","操作失败");
            }
            return result.toJSONString();
    }

    /**
     * 同步新版广告源配置至投放系统
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/syn", method={RequestMethod.POST})
    public String syn(HttpServletRequest request, HttpServletResponse response) {

            String httpPost = HttpClientUtils.getInstance().httpPost("https://cfg.vigame.cn:6122/app/syncNewAdConfig", null);
            System.out.println("syncNewAdConfig 同步结果："+httpPost);

            // 批量导入广告源数据
            String sql2 = "select ad_sid mapkey,adsize,ad_sid,agent,appid,appkey,`code`, "+
                    "concat('',creatdate) creatdate,limitname,refreshinterval,textinfo,type,bb.dn_appid "+
                    "from extend_adinfo aa,(select ad_sid_str,IF(LENGTH(max(prjmid))>=8,SUBSTR(prjmid,1,5),'') dn_appid "+
                    "from extend_adconfig where statu = 1 GROUP BY ad_sid_str) bb where aa.ad_sid = bb.ad_sid_str";
            Map<String, Map<String, Object>> queryMap2 = adService.queryListMapOfKey(sql2);

            List<Map<String, Object>> list = new ArrayList<>(queryMap2.values());
            tfxtMapper.execSql("delete from dn_placement where 1=1");
            int resp = tfxtMapper.insertDnplacement(list);

            JSONObject result = new JSONObject();
            if(resp > 0) {
                result.put("ret",1);
                result.put("msg","操作成功");
            }else {
                result.put("ret",0);
                result.put("msg","同步失败，请重试！");
            }
            return result.toJSONString();

    }

}
