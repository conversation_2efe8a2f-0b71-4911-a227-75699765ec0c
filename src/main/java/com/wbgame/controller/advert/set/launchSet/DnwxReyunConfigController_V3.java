package com.wbgame.controller.advert.set.launchSet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.advert.DnwxReyunConfigVo;
import com.wbgame.service.AdService;
import com.wbgame.service.advert.DnwxReyunConfigService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description: 热云参数配置V3
 * @author: caow
 * @date: 2022/06/15
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/advert/dnwxReyunConfigV3")
public class DnwxReyunConfigController_V3 {

    @Autowired
    private AdService adService;
	@Autowired
    private DnwxReyunConfigService dnwxReyunConfigService;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 广告-热云参数配置.查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/list", method={RequestMethod.GET, RequestMethod.POST})
    public String list(HttpServletRequest request,HttpServletResponse response) throws IOException {

        String[] args = {"appid","cha_id","pid","adType","statu","cuser","event","eventType","buy_id","buy_act","sdk_type","logic"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);

        JSONObject result = new JSONObject();
        try {
            String sql = "select * from dnwx_reyun_config_three where pid not in (333351,13665999) ";
            if(!BlankUtils.checkBlank(paramMap.get("appid")))
                sql += " and appid in ("+paramMap.get("appid")+")";
            if(!BlankUtils.checkBlank(paramMap.get("cha_id")))
                sql += " and cha_id = #{obj.cha_id}";
            if(!BlankUtils.checkBlank(paramMap.get("pid")))
                sql += " and pid = #{obj.pid}";
            if(!BlankUtils.checkBlank(paramMap.get("adType")))
                sql += " and adType in ("+paramMap.get("adType")+")";
            if(!BlankUtils.checkBlank(paramMap.get("event")))
                sql += " and event = #{obj.event}";
            if(!BlankUtils.checkBlank(paramMap.get("eventType")))
                sql += " and eventType = #{obj.eventType}";
            if(!BlankUtils.checkBlank(paramMap.get("buy_id")))
                sql += " and buy_id = #{obj.buy_id}";
            if(!BlankUtils.checkBlank(paramMap.get("buy_act")))
                sql += " and buy_act = #{obj.buy_act}";
            if(!BlankUtils.checkBlank(paramMap.get("statu")))
            	sql += " and statu = #{obj.statu}";
            if(!BlankUtils.checkBlank(paramMap.get("sdk_type")))
            	sql += " and sdk_type = #{obj.sdk_type}";
            if(!BlankUtils.checkBlank(paramMap.get("logic")))
            	sql += " and logic = #{obj.logic}";
            if(!BlankUtils.checkBlank(paramMap.get("cuser")))
                sql += " and cuser = #{obj.cuser}";
            
            sql += " order by createtime desc ";

            List<Map<String, Object>> list = adService.queryListMapTwo(sql, paramMap);


            List<Map<String, Object>> collect = new ArrayList<Map<String,Object>>();
            Map<String, List<Map<String, Object>>> midMap = list.stream().collect(Collectors.groupingBy(act -> {
            	return act.get("appid")+",#"+act.get("cha_id")+",#"+act.get("pid")+",#"+act.get("buy_id")+",#"+act.get("buy_act")+",#"+act.get("event")+",#"+act.get("sdk_type");
            }));

            midMap.entrySet().forEach(act -> {
            	String[] split = act.getKey().split(",");
            	
            	if(split != null && split.length == 7){
	            	JSONObject info = new JSONObject();
	            	info.put("appid", split[0].replace("#", ""));
	            	info.put("cha_id", split[1].replace("#", ""));
	            	info.put("pid", split[2].replace("#", ""));
	            	info.put("buy_id", split[3].replace("#", ""));
	            	info.put("buy_act", split[4].replace("#", ""));
	            	info.put("event", split[5].replace("#", ""));
	            	info.put("sdk_type", split[6].replace("#", ""));

	            	if(act.getValue() != null && !act.getValue().isEmpty()){
	            		Map<String, Object> startMap = act.getValue().get(act.getValue().size() - 1);
	            		info.put("createtime", startMap.get("createtime")+"");
						info.put("cuser", startMap.get("cuser")+"");

						Map<String, Object> lastMap = act.getValue().get(0);
						info.put("endtime", lastMap.get("endtime")+"");
	            		info.put("euser", lastMap.get("euser")+"");
	            	}
	            	
	            	info.put("data", act.getValue());
					collect.add(info);
            	}
            });

			/* 做分页处理 */
			List<Map<String, Object>> resultList = collect.stream()
					.skip((Integer.valueOf(paramMap.get("pageNum")) - 1) * Integer.valueOf(paramMap.get("pageSize"))) // 跳过前面的元素
					.limit(Integer.valueOf(paramMap.get("pageSize"))) // 获取指定数量的元素
					.collect(Collectors.toList());

			result.put("ret", 1);
            result.put("data", resultList);
            result.put("totalCount", collect.size());
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 广告-热云参数配置.操作
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/handle", method={RequestMethod.GET,RequestMethod.POST})
    public String handle(DnwxReyunConfigVo record ,HttpServletRequest request, HttpServletResponse response) {

        // token验证
        String token = request.getParameter("token");
        Object object = redisTemplate.opsForValue().get(token);
        JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(object));

        int result = 0;
        try {
            record.setCuser(json.getString("login_name"));
            record.setEuser(json.getString("login_name"));
            
            /** 当广告类型为null时手动赋值，保证绝对有值 -caow.2022.08.15 */
            if(BlankUtils.checkBlank(record.getAdType())){
            	record.setAdType("0");
            }

			/** 针对ecpm-2 乘100 和arpu-6 乘100000，来计算单位 */
			if(record.getEventType() == 2){
				if(record.getEcpmValue() != 0){
					record.setEcpmValue(record.getEcpmValue()*100);
				}
			}else if(record.getEventType() == 6){
				if(record.getEcpmValue() != 0){
					record.setEcpmValue(record.getEcpmValue()*100000);
				}
			}

            if ("add".equals(request.getParameter("handle"))) {
				result = adService.insertDnwxReyunConfigThree(record);
				return "{\"ret\":1,\"msg\":\"操作成功!\",\"reyun_id\":"+record.getId()+"}";
				
			} else if ("edit".equals(request.getParameter("handle"))) {
				// 记录操作前配置
				try {
					String sql = "select * from dnwx_reyun_config_three where id="+record.getId();
					List<DnwxReyunConfigVo> list = adService.queryListBean(sql, DnwxReyunConfigVo.class);
					if(list != null && list.size() == 1){
						DnwxReyunConfigVo configVo = list.get(0);
						configVo.setRecord_id(record.getRecord_id());
						adService.insertDnwxReyunConfigThreeRecord(configVo);
					}
				}catch (Exception e){
					e.printStackTrace();
				}

				String sql = "update dnwx_reyun_config_three set appid=#{obj.appid},cha_id=#{obj.cha_id},pid=#{obj.pid},buy_id=#{obj.buy_id},buy_act=#{obj.buy_act},`event`=#{obj.event},eventType=#{obj.eventType},times=#{obj.times},`loop`=#{obj.loop},rate=#{obj.rate},adType=#{obj.adType},ecpmValue=#{obj.ecpmValue},ecpmType=#{obj.ecpmType},action=#{obj.action},timeType=#{obj.timeType},checkTimes=#{obj.checkTimes},levelType=#{obj.levelType},`level`=#{obj.level},statu=#{obj.statu},sdk_type=#{obj.sdk_type},logic=#{obj.logic},`out`=#{obj.out},action_params=#{obj.action_params},rule_rate=#{obj.rule_rate},euser=#{obj.euser},endtime=now() "+
						"where id=#{obj.id}";
				result = adService.execSqlHandle(sql, record);

			} else if ("del".equals(request.getParameter("handle"))) {
				// 记录操作前配置
				try {
					String sql = "select * from dnwx_reyun_config_three where id="+record.getId();
					List<DnwxReyunConfigVo> list = adService.queryListBean(sql, DnwxReyunConfigVo.class);
					if(list != null && list.size() == 1){
						DnwxReyunConfigVo configVo = list.get(0);
						configVo.setRecord_id(record.getRecord_id());
						adService.insertDnwxReyunConfigThreeRecord(configVo);
					}
				}catch (Exception e){
					e.printStackTrace();
				}

				String sql = "delete from dnwx_reyun_config_three where id in ("+record.getId()+") ";
				result = adService.execSqlHandle(sql, record);
			}

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

	/**
	 * 广告-热云参数配置.查询操作记录
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/recordList", method={RequestMethod.GET, RequestMethod.POST})
	public String recordList(HttpServletRequest request,HttpServletResponse response) throws IOException {

		String[] args = {"appid","cha_id","pid","adType","statu","cuser","event","eventType","buy_id","buy_act","sdk_type","logic"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);

		JSONObject result = new JSONObject();
		try {
			String sql = "select * from dnwx_reyun_config_three_record where 1=1 ";
			if(!BlankUtils.checkBlank(paramMap.get("appid")))
				sql += " and appid = #{obj.appid}";
			if(!BlankUtils.checkBlank(paramMap.get("cha_id")))
				sql += " and cha_id = #{obj.cha_id}";
			if(!BlankUtils.checkBlank(paramMap.get("pid")))
				sql += " and pid = #{obj.pid}";
			if(!BlankUtils.checkBlank(paramMap.get("buy_id")))
				sql += " and buy_id = #{obj.buy_id}";
			if(!BlankUtils.checkBlank(paramMap.get("buy_act")))
				sql += " and buy_act = #{obj.buy_act}";
			if(!BlankUtils.checkBlank(paramMap.get("event")))
				sql += " and `event` = #{obj.event}";
			if(!BlankUtils.checkBlank(paramMap.get("sdk_type")))
				sql += " and sdk_type = #{obj.sdk_type}";

			sql += " order by createtime desc ";

			// 组装后list
			List<Map<String, Object>> collect = new ArrayList<Map<String, Object>>();

			List<Map<String, Object>> recordList = adService.queryListMapTwo(sql, paramMap);
			if(recordList != null) {
				Map<String, List<Map<String, Object>>> recordMap = recordList.stream().collect(Collectors.groupingBy(act -> act.get("record_id") + ""));
				// 按照key排序使用
				TreeSet<String> treeSet = new TreeSet<String>(recordMap.keySet());

				// 将recordMap里的value按照下面list的处理
				for (String recordid : treeSet) {

					// 秒级时间戳转换为 LocalDateTime（使用UTC+8时区）
					Long timestampSeconds = Long.valueOf(recordid);
					LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timestampSeconds, 0, ZoneOffset.ofHours(8));
					// 格式化输出
					DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
					String format = dateTime.format(formatter) + "版本：";

					// 按照主键格式重新拼接数据格式
					List<Map<String, Object>> list = recordMap.get(recordid);
					Map<String, List<Map<String, Object>>> midMap = list.stream().collect(Collectors.groupingBy(act -> {
						return act.get("appid") + ",#" + act.get("cha_id") + ",#" + act.get("pid") + ",#" + act.get("buy_id") + ",#" + act.get("buy_act") + ",#" + act.get("event") + ",#" + act.get("sdk_type");
					}));

					midMap.entrySet().forEach(act -> {
						String[] split = act.getKey().split(",");

						if (split != null && split.length == 7) {
							JSONObject info = new JSONObject();
							info.put("record_id", format);
							info.put("appid", split[0].replace("#", ""));
							info.put("cha_id", split[1].replace("#", ""));
							info.put("pid", split[2].replace("#", ""));
							info.put("buy_id", split[3].replace("#", ""));
							info.put("buy_act", split[4].replace("#", ""));
							info.put("event", split[5].replace("#", ""));
							info.put("sdk_type", split[6].replace("#", ""));

							if (act.getValue() != null && !act.getValue().isEmpty()) {
								Map<String, Object> startMap = act.getValue().get(act.getValue().size() - 1);
								info.put("createtime", startMap.get("createtime")+"");
								info.put("cuser", startMap.get("cuser")+"");

								Map<String, Object> lastMap = act.getValue().get(0);
								info.put("endtime", lastMap.get("endtime")+"");
								info.put("euser", lastMap.get("euser")+"");
							}

							info.put("data", act.getValue());
							collect.add(info);
						}
					});

				}
			}

			result.put("ret", 1);
			result.put("data", collect);
			result.put("totalCount", collect.size());
		} catch (Exception e) {
			e.printStackTrace();
			return ReturnJson.toErrorJson( "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}

	

	/**
	 * 广告-热云参数配置.查询达成效果报表
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
    @RequestMapping(value="/reportList", method={RequestMethod.GET, RequestMethod.POST})
    public String reportList(HttpServletRequest request,HttpServletResponse response) throws IOException {
        String[] args = {"start_date","end_date","appid","cha_id","pid","event","eventType","buy_id","buy_act","sdk_type"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);

		JSONObject result = new JSONObject();
        try {
            List<Map<String, Object>> reportList = dnwxReyunConfigService.queryBehaviorAchievement(paramMap);
			// 接收到的列表，将reg_users、act_users、user_cnt、event_cnt字段累加汇总单独存储为totalMap
            Map<String, String> totalMap = new HashMap<>();
            for (Map<String, Object> record : reportList) {
				// 大数据新增、大数据活跃、深度行为达成人数、深度行为达成次数
                totalMap.put("reg_users", String.valueOf(Integer.parseInt(totalMap.getOrDefault("reg_users", "0")) + Integer.parseInt(record.getOrDefault("reg_users", "0").toString())));
                totalMap.put("act_users", String.valueOf(Integer.parseInt(totalMap.getOrDefault("act_users", "0")) + Integer.parseInt(record.getOrDefault("act_users", "0").toString())));
                totalMap.put("user_cnt", String.valueOf(Integer.parseInt(totalMap.getOrDefault("user_cnt", "0")) + Integer.parseInt(record.getOrDefault("user_cnt", "0").toString())));
                totalMap.put("event_cnt", String.valueOf(Integer.parseInt(totalMap.getOrDefault("event_cnt", "0")) + Integer.parseInt(record.getOrDefault("event_cnt", "0").toString())));
				// 深度行为达成率
                if(Integer.parseInt(totalMap.getOrDefault("reg_users", "0")) > 0){
					totalMap.put("user_rate", String.format("%.2f", Double.parseDouble(totalMap.getOrDefault("user_cnt", "0")) / Double.parseDouble(totalMap.getOrDefault("reg_users", "0")) * 100)+"%");
				}else{
					totalMap.put("user_rate", "0.00%");
				}
            }
			result.put("ret", 1);
			result.put("data", reportList);
			result.put("total", totalMap);
			result.put("totalCount", reportList.size());

            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息: " + e.getMessage());
        }
    }
	/**
	 * 广告-热云参数配置.导出达成效果报表
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/reportExport", method={RequestMethod.GET, RequestMethod.POST})
	public void reportExport(HttpServletRequest request,HttpServletResponse response) {

		String[] args = {"start_date","end_date","appid","cha_id","pid","event","eventType","buy_id","buy_act","sdk_type"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);

		List<Map<String, Object>> contentList = dnwxReyunConfigService.queryBehaviorAchievement(paramMap);

		// 应用名称进行匹配赋值
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap();
		for (Map<String, Object> act : contentList) {
			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name")+"");


			// 热云-reyun、广点通-gdt、adjust-adjust、自统计-self_attribution、热力引擎-solar、大数据事件-dnstatistics 的支持
			String sdk_type = act.get("sdk_type")+"";
			act.put("sdk_type", ("reyun".equals(sdk_type)?"热云":("gdt".equals(sdk_type)?"广点通":("adjust".equals(sdk_type)?"adjust":("self_attribution".equals(sdk_type)?"自统计":("solar".equals(sdk_type)?"热力引擎":("dnstatistics".equals(sdk_type)?"大数据事件":"未知")))))));
		}

		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		String value = request.getParameter("value");
		if (!BlankUtils.checkBlank(value)){
			//自定义列数据
			try {
				String[] split = value.split(";");
				for (int i = 0;i<split.length;i++) {
					String[] s = split[i].split(",");
					headerMap.put(s[0],s[1]);
				}
			}catch (Exception e) {
				e.printStackTrace();
				Asserts.fail("自定义列导出异常");
			}
		}

		String fileName = "深度事件-达成效果报表_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
		ExportExcelUtil.exportXLSX(response,contentList,headerMap,fileName);
	}

}
