package com.wbgame.controller.advert.partnerNew;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.advert.partnerNew.PartnerInvestGroupDTO;
import com.wbgame.service.advert.PartnerInvestGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * @description: 合作方投放明细聚合查询
 * @author: caow
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/advert/partnerInvestGroup")
public class PartnerInvestGroupController {

    @Autowired
    private PartnerInvestGroupService partnerInvestGroupService;

    /**
     * 合作方投放明细查询
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 查询结果
     * @throws IOException IO异常
     */
    @RequestMapping(value="list", method={RequestMethod.GET, RequestMethod.POST})
    public String list(@RequestBody PartnerInvestGroupDTO dto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        
        try {
            // 调用Service层方法
            return partnerInvestGroupService.list(dto, request);
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
            return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
        }
    }

    /**
     * 合作方产品收支导出
     * @param request HTTP请求
     * @param response HTTP响应
     */
    @RequestMapping(value = "/export", method={RequestMethod.GET, RequestMethod.POST})
    public void export(@RequestBody PartnerInvestGroupDTO dto, HttpServletRequest request, HttpServletResponse response) {
        // 调用Service层方法
        partnerInvestGroupService.export(dto, request, response);
    }


    /**
     * 处理投放数据
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 操作结果
     * @throws IOException IO异常
     * @throws Exception 异常
     */
    @RequestMapping(value="/handle", method = {RequestMethod.POST})
    public String handle(@RequestBody PartnerInvestGroupDTO dto, HttpServletRequest request, HttpServletResponse response) throws IOException, Exception {

        try {
            // 调用Service层方法
            return partnerInvestGroupService.handle(dto.getHandle(), dto);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 同步投放金额，从投放数据运营系统-国内投放细分数据查询
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 操作结果
     */
    @RequestMapping(value="/sync", method={RequestMethod.POST})
    public String sync(@RequestBody PartnerInvestGroupDTO dto, HttpServletRequest request, HttpServletResponse response) {

        try {
            // 调用Service层方法
            return partnerInvestGroupService.sync(dto);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("操作失败: " + e.getMessage());
        }
    }
    
}
