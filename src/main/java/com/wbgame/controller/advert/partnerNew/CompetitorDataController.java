package com.wbgame.controller.advert.partnerNew;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.custom.CompetitorDataVo;
import com.wbgame.service.CustomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 竞品数据分析表
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/advert/competitorData")
public class CompetitorDataController {

    @Autowired
    private CustomService customService;

    /**
     * 竞品数据分析表查询
     * @param paramMap 查询参数
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 查询结果
     * @throws IOException IO异常
     */
    @RequestMapping(value="list", method={RequestMethod.GET, RequestMethod.POST})
    public String list(@RequestBody Map<String, String> paramMap, HttpServletRequest request, HttpServletResponse response) throws IOException {
        
        try {
            // 调用Service层方法
            PageHelper.startPage(Integer.parseInt(paramMap.get("start")), Integer.parseInt(paramMap.get("limit")));
            List<CompetitorDataVo> list = customService.selectCompetitorDataList(paramMap);

            return ReturnJson.successPage(list);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息: " + e.getMessage());
        }
    }

    /**
     * 竞品数据分析表导出
     * @param paramMap 查询参数
     * @param request HTTP请求
     * @param response HTTP响应
     */
    @RequestMapping(value = "/export", method={RequestMethod.GET, RequestMethod.POST})
    public void export(@RequestBody Map<String, String> paramMap, HttpServletRequest request, HttpServletResponse response) {
        try {
            // 调用Service层方法
            customService.exportCompetitorData(paramMap, request, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 竞品数据日志上传
     * @param file 上传的文件
     * @param company 公司主体
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 操作结果
     * @throws IOException IO异常
     */
    @RequestMapping(value="/importLog", method = {RequestMethod.POST})
    public String importLog(@RequestParam(value = "fileName") MultipartFile[] files, String company, HttpServletRequest request, HttpServletResponse response) {
        try {
            if (files == null || files.length == 0) {
                return ReturnJson.toErrorJson("未上传任何文件");
            }

            List<Map<String, String>> resultList = new java.util.ArrayList<>();
            for (MultipartFile file : files) {
                Map<String, String> result = new java.util.HashMap<>();
                result.put("fileName", file.getOriginalFilename());
                try {
                    Map<String, Object> paramMap = new java.util.HashMap<>();
                    paramMap.put("file", file);
                    paramMap.put("company", company);
                    String importResult = customService.importCompetitorDataLog(paramMap);
                    if (importResult.contains("成功")) {
                        result.put("status", "success");
                        result.put("msg", "导入成功");
                    } else {
                        result.put("status", "fail");
                        result.put("msg", importResult);
                    }
                } catch (Exception ex) {
                    result.put("status", "fail");
                    result.put("msg", ex.getMessage());
                }
                resultList.add(result);
            }
            return ReturnJson.success(resultList);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("操作失败: " + e.getMessage());
        }
    }

    @RequestMapping(value="/deleteByMd5", method = {RequestMethod.POST})
    public String deleteByMd5(@RequestParam("md5") String md5) {
        try {
            boolean result = customService.deleteCompetitorDataByMd5(md5);
            if (result) {
                return ReturnJson.success("删除成功");
            } else {
                return ReturnJson.toErrorJson("删除失败，md5不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("操作失败: " + e.getMessage());
        }
    }

}