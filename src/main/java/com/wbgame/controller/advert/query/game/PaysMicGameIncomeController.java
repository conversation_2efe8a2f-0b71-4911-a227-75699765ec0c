package com.wbgame.controller.advert.query.game;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.advert.MicGameIncomeVo;
import com.wbgame.service.AdService;
import com.wbgame.service.advert.MicGameIncomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import jxl.Sheet;
import jxl.Workbook;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname 付费小游戏收支汇总表
 * @Description TODO
 * @Date 2022/08/24
 */

@CrossOrigin
@RestController
@RequestMapping("/advert/paysMicGameIncome")
public class PaysMicGameIncomeController {

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    private AdService adService;

    @Autowired
    MicGameIncomeService micGameIncomeService;

    /**
     * 小游戏收支汇总表-查询
     * @param request
     * @return
     */
    @RequestMapping("getList")
    public Object getList(HttpServletRequest request) {
        JSONObject ret = new JSONObject();
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize);
        Map<String, String> paramMap = new HashMap<>();
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");


        String group = "DATE_FORMAT(tdate,'%Y-%m-%d')";
        String week_group = request.getParameter("week_group");
        String month_group = request.getParameter("month_group");
        String year_group = request.getParameter("year_group");

        if (!BlankUtils.checkBlank(month_group)){
            group = "DATE_FORMAT(tdate,'%Y-%m')";
        } else if (!BlankUtils.checkBlank(year_group)) {
            group = "DATE_FORMAT(tdate,'%Y')";
        } else if (!BlankUtils.checkBlank(week_group)){
            group = "DATE_FORMAT(tdate,'%Y-%u')";
        }

        if (BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)) {
            start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        }
        paramMap.put("start_date", start_date);
        paramMap.put("end_date", end_date);

        String order_str = request.getParameter("order_str");
        paramMap.put("start_date", start_date);
        paramMap.put("end_date", end_date);
        paramMap.put("appid", appid);
        paramMap.put("group", group);
        paramMap.put("order_str", order_str);

        List<MicGameIncomeVo> list = micGameIncomeService.getPaysMicGameIncomeDataList(paramMap);
        MicGameIncomeVo total = micGameIncomeService.getPaysMicGameIncomeDataListSum(paramMap);

        long size = ((Page) list).getTotal();
        for (MicGameIncomeVo po : list) {
            //处理sum(聚合函数)返回null 条目问题
            if (po == null) {
                list = new ArrayList<>();
                size = 0;
                break;
            }
        }
        ret.put("ret", 1);
        ret.put("msg", "ok");
        ret.put("data", list);
        ret.put("total", total);
        ret.put("totalSize", size);
        return ret;
    }

    /**
     * 小游戏收支汇总表-导出
     * @param request
     * @param response
     */
    @RequestMapping("export")
    public void export(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> paramMap = new HashMap<>();
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");


        String group = "DATE_FORMAT(tdate,'%Y-%m-%d')";
        String week_group = request.getParameter("week_group");
        String month_group = request.getParameter("month_group");
        String year_group = request.getParameter("year_group");

        if (!BlankUtils.checkBlank(month_group)){
            group = "DATE_FORMAT(tdate,'%Y-%m')";
        } else if (!BlankUtils.checkBlank(year_group)) {
            group = "DATE_FORMAT(tdate,'%Y')";
        } else if (!BlankUtils.checkBlank(week_group)){
            group = "DATE_FORMAT(tdate,'%Y-%u')";
        }

        if (BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)) {
            start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        }
        paramMap.put("start_date", start_date);
        paramMap.put("end_date", end_date);

        String order_str = request.getParameter("order_str");
        paramMap.put("start_date", start_date);
        paramMap.put("end_date", end_date);
        paramMap.put("appid", appid);
        paramMap.put("group", group);
        paramMap.put("order_str", order_str);

        // 数据内容
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
        ;
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

        List<MicGameIncomeVo> list = micGameIncomeService.getPaysMicGameIncomeDataList(paramMap);
        for (MicGameIncomeVo vo : list) {
            if (vo != null) {
                vo.setAppname(appMap.get(vo.getAppid()) != null ? appMap.get(vo.getAppid()).get("app_name") + "" : "");
            } else {
                list = new ArrayList<>();
                break;
            }
        }
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)) {
            try {
                String[] split = value.split(";");
                for (int i = 0; i < split.length; i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0], s[1]);
                }
            } catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        ExportExcelUtil.export2(response, list, headerMap, "付费小游戏收支汇总_" + DateTime.now().toString("yyyyMMdd") + ".xls");
    }

}
