package com.wbgame.controller.advert.query.transport;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.UmengChannelTotalVo;
import com.wbgame.pojo.custom.AdvFeeVo;
import com.wbgame.service.AdService;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @description: 渠道产品查询明细
 * @author: huangmb
 * @date: 2021/06/05
 **/
@CrossOrigin
@RestController
@RequestMapping("/advert/umengChannelTotal")
public class UmengChannelTotalController {

    @Autowired
    private SomeService someService;

    @Autowired
    private AdService adService;

    @RequestMapping(value="/list", method={RequestMethod.GET,RequestMethod.POST})
    public String list(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try{
            String start = BlankUtils.checkNull(request, "start");
            String limit = BlankUtils.checkNull(request, "limit");
            int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // 当前页
            int pageNo = (pageStart / pageSize) + 1;
            PageHelper.startPage(pageNo, pageSize); // 进行分页
            String start_date = BlankUtils.checkNull(request, "start_date");
            String end_date = BlankUtils.checkNull(request, "end_date");
            String appkey = BlankUtils.checkNull(request, "appkey");
            String appid = BlankUtils.checkNull(request,"appid");
            String push_cha = BlankUtils.checkNull(request, "push_cha");
            String channel = BlankUtils.checkNull(request, "channel");
            String group = BlankUtils.checkNull(request,"group");
            String order_str = BlankUtils.checkNull(request,"order_str");
            String app_category = BlankUtils.checkNull(request,"app_category");
            String cha_media = BlankUtils.checkNull(request,"cha_media");
            String queryGroup = "";
            StringBuffer sb = new StringBuffer();

            if (!BlankUtils.checkBlank(group)){
                String[] groups = group.split(",");
                for (String str:groups){
                    if ("tdate".equals(str)){
                        sb.append("aa.tdate").append(",");
                    }
                    if ("app_category".equals(str)){
                        sb.append("ww.id").append(",");
                    }
                    if ("appid".equals(str)){
                        sb.append("aa.appid").append(",");
                    }
                    if ("cha_media".equals(str)){
                        sb.append("bb.cha_media").append(",");
                    }
                    if ("channel".equals(str)){
                        sb.append("aa.install_channel").append(",");
                    }

                }
                queryGroup = sb.toString().substring(0,sb.length()-1);
            }

            if (!BlankUtils.checkBlank(order_str)){
                order_str = order_str.replace("appid","aa.appid");
                order_str = order_str.replace("tdate","aa.tdate");
            }

            Map<Object, Object> parmMap = new HashMap<>();
            parmMap.put("start_date", start_date);
            parmMap.put("end_date", end_date);
            parmMap.put("appkey", appkey);
            parmMap.put("appid", appid);
            parmMap.put("push_cha", push_cha);
            parmMap.put("channel", channel==null?"":channel);
            parmMap.put("ctype", "1");
            parmMap.put("order_str",order_str);
            parmMap.put("group",queryGroup);
            parmMap.put("app_category",app_category);
            parmMap.put("cha_media",cha_media);

            List<UmengChannelTotalVo> list = someService.selectUmengChannelTotalNew(parmMap);
            //处理并表 null数据返回问题
            boolean flag = false;
            for (UmengChannelTotalVo uc : list) {
                if (uc == null){
                    flag = true;
                    break;
                }
                uc.setKeep_num1(uc.getKeep_num1()==null?"":uc.getKeep_num1() + "%");
                uc.setKeep_num2(uc.getKeep_num2()==null?"":uc.getKeep_num2() + "%");
                uc.setKeep_num3(uc.getKeep_num3()==null?"":uc.getKeep_num3() + "%");
                uc.setKeep_num4(uc.getKeep_num4()==null?"":uc.getKeep_num4() + "%");
                uc.setKeep_num5(uc.getKeep_num5()==null?"":uc.getKeep_num5() + "%");
                uc.setKeep_num6(uc.getKeep_num6()==null?"":uc.getKeep_num6() + "%");
                uc.setKeep_num7(uc.getKeep_num7()==null?"":uc.getKeep_num7() + "%");
                uc.setKeep_num14(uc.getKeep_num14()==null?"":uc.getKeep_num14() + "%");
                uc.setKeep_num30(uc.getKeep_num30()==null?"":uc.getKeep_num30() + "%");

                // 计算人均启动次数：日启动次数/活跃数	小数点2位，进行四舍五入
                if(uc.getLaunch() != null && uc.getActnum() != 0){
                    uc.setLaunch(new BigDecimal(uc.getLaunch()).divide(new BigDecimal(uc.getActnum()), 2, RoundingMode.HALF_UP).toString());
                } else {
                    uc.setLaunch("0");
                }

                // 广告darpu:当日广告收入/活跃数	小数点2位，不进行四舍五入
                if(uc.getAdv_fee() != null && uc.getActnum() != 0){
                    uc.setAdv_darpu(new BigDecimal(uc.getAdv_fee()).divide(new BigDecimal(uc.getActnum()), 2, RoundingMode.FLOOR).toString());
                }

                //设置在线时长
                if (uc.getDaily_duration()!=null){
                    uc.setDaily_duration(StringUtils.secondsToHHmmss(uc.getDaily_duration()));
                }
                //设置单次使用时长
                if (uc.getDaily_per_duration()!=null){
                    uc.setDaily_per_duration(StringUtils.secondsToHHmmss(uc.getDaily_per_duration()));
                }
            }

            //汇总新增用户、活跃用户、当日广告收入
            UmengChannelTotalVo total = someService.countUmengChannelTotalNew(parmMap);
            if (total!=null){
                total.setKeep_num1(total.getKeep_num1()==null?"":total.getKeep_num1() + "%");
                total.setKeep_num2(total.getKeep_num2()==null?"":total.getKeep_num2() + "%");
                total.setKeep_num3(total.getKeep_num3()==null?"":total.getKeep_num3() + "%");
                total.setKeep_num4(total.getKeep_num4()==null?"":total.getKeep_num4() + "%");
                total.setKeep_num5(total.getKeep_num5()==null?"":total.getKeep_num5() + "%");
                total.setKeep_num6(total.getKeep_num6()==null?"":total.getKeep_num6() + "%");
                total.setKeep_num7(total.getKeep_num7()==null?"":total.getKeep_num7() + "%");
                total.setKeep_num14(total.getKeep_num14()==null?"":total.getKeep_num14() + "%");
                total.setKeep_num30(total.getKeep_num30()==null?"":total.getKeep_num30() + "%");
                // 计算人均启动次数：日启动次数/活跃数	小数点2位，进行四舍五入
                if(total.getLaunch() != null && total.getActnum() != 0){
                    total.setLaunch(new BigDecimal(total.getLaunch()).divide(new BigDecimal(total.getActnum()), 2, RoundingMode.HALF_UP).toString());
                } else {
                    total.setLaunch("0");
                }
                // 广告darpu:当日广告收入/活跃数	小数点2位，不进行四舍五入
                if(total.getAdv_fee() != null && total.getActnum() != 0){
                    total.setAdv_darpu(new BigDecimal(total.getAdv_fee()).divide(new BigDecimal(total.getActnum()), 2, RoundingMode.FLOOR).toString());
                }
                total.setDaily_duration(StringUtils.secondsToHHmmss(total.getDaily_duration()));
                total.setDaily_per_duration(StringUtils.secondsToHHmmss(total.getDaily_per_duration()));
            }


            JSONObject result = new JSONObject();
            if (flag){
                result.put("data", new ArrayList<>());
                result.put("totalCount", 0);
            }else {
                long size = ((Page) list).getTotal();
                result.put("data", list);
                result.put("totalCount", size);
            }
            result.put("ret",1);
            result.put("total",total);
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    @RequestMapping(value="/export", method={RequestMethod.GET,RequestMethod.POST})
    public void export(ModelMap map, HttpServletRequest request, HttpServletResponse response) {
        //数据头
        String start_date = BlankUtils.checkNull(request, "start_date");
        String end_date = BlankUtils.checkNull(request, "end_date");
        String appkey = BlankUtils.checkNull(request, "appkey");
        String appid = BlankUtils.checkNull(request,"appid");
        String push_cha = BlankUtils.checkNull(request, "push_cha");
        String channel = BlankUtils.checkNull(request, "channel");
        String group = BlankUtils.checkNull(request,"group");
        String order_str = BlankUtils.checkNull(request,"order_str");
        String export_file_name = request.getParameter("export_file_name");
        String value = BlankUtils.checkNull(request,"value");
        String app_category = BlankUtils.checkNull(request,"app_category");
        String cha_media = BlankUtils.checkNull(request,"cha_media");

        String queryGroup = "";
        StringBuffer sb = new StringBuffer();

        if (!BlankUtils.checkBlank(group)){
            String[] groups = group.split(",");
            for (String str:groups){
                if ("tdate".equals(str)){
                    sb.append("aa.tdate").append(",");
                }
                if ("app_category".equals(str)){
                    sb.append("ww.id").append(",");
                }
                if ("appid".equals(str)){
                    sb.append("aa.appid").append(",");
                }
                if ("cha_media".equals(str)){
                    sb.append("bb.cha_media").append(",");
                }
                if ("channel".equals(str)){
                    sb.append("aa.install_channel").append(",");
                }

            }
            queryGroup = sb.toString().substring(0,sb.length()-1);
        }

        if (!BlankUtils.checkBlank(order_str)){
            order_str = order_str.replace("appid","aa.appid");
            order_str = order_str.replace("tdate","aa.tdate");
        }

        Map<Object, Object> parmMap = new HashMap<>();
        parmMap.put("start_date", start_date);
        parmMap.put("end_date", end_date);
        parmMap.put("appkey", appkey);
        parmMap.put("appid", appid);
        parmMap.put("push_cha", push_cha);
        parmMap.put("channel", channel==null?"":channel);
        parmMap.put("ctype", "1");
        parmMap.put("order_str",order_str);
        parmMap.put("group",queryGroup);
        parmMap.put("app_category",app_category);
        parmMap.put("cha_media",cha_media);

        List<UmengChannelTotalVo> list = someService.selectUmengChannelTotalNew(parmMap);
        for (UmengChannelTotalVo uc : list) {
            if (uc == null){
                list = new ArrayList<>();
                break;
            }
            uc.setKeep_num1(uc.getKeep_num1()==null?"":uc.getKeep_num1() + "%");
            uc.setKeep_num2(uc.getKeep_num2()==null?"":uc.getKeep_num2() + "%");
            uc.setKeep_num3(uc.getKeep_num3()==null?"":uc.getKeep_num3() + "%");
            uc.setKeep_num4(uc.getKeep_num4()==null?"":uc.getKeep_num4() + "%");
            uc.setKeep_num5(uc.getKeep_num5()==null?"":uc.getKeep_num5() + "%");
            uc.setKeep_num6(uc.getKeep_num6()==null?"":uc.getKeep_num6() + "%");
            uc.setKeep_num7(uc.getKeep_num7()==null?"":uc.getKeep_num7() + "%");
            uc.setKeep_num14(uc.getKeep_num14()==null?"":uc.getKeep_num14() + "%");
            uc.setKeep_num30(uc.getKeep_num30()==null?"":uc.getKeep_num30() + "%");

            // 计算人均启动次数：日启动次数/活跃数	小数点2位，进行四舍五入
            if(uc.getLaunch() != null && uc.getActnum() != 0)
                uc.setLaunch(new BigDecimal(uc.getLaunch()).divide(new BigDecimal(uc.getActnum()), 2, RoundingMode.HALF_UP).toString());
            else
                uc.setLaunch("0");

            // 广告darpu:当日广告收入/活跃数	小数点2位，不进行四舍五入
            if(uc.getAdv_fee() != null && uc.getActnum() != 0)
                uc.setAdv_darpu(new BigDecimal(uc.getAdv_fee()).divide(new BigDecimal(uc.getActnum()), 2, RoundingMode.FLOOR).toString());

            //设置在线时长
            if (uc.getDaily_duration()!=null){
                uc.setDaily_duration(StringUtils.secondsToHHmmss(uc.getDaily_duration()));
            }
            //设置单次使用时长
            if (uc.getDaily_per_duration()!=null){
                uc.setDaily_per_duration(StringUtils.secondsToHHmmss(uc.getDaily_per_duration()));
            }
        }
        //原有表头逻辑不变,自定义列改

        Map<String,String> head = new LinkedHashMap<>();
        if (value!=null){
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    head.put(s[0],s[1]);
                }
            }catch (Exception e) {
                Asserts.fail("自定义列导出异常");
            }
        }
        String fileName = export_file_name+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX2(response,list,head,fileName);
    }

    @RequestMapping(value="/syn", method={RequestMethod.POST})
    public String synAdv(HttpServletRequest request,HttpServletResponse response){

        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        List<UmengChannelTotalVo> keepList = new ArrayList<UmengChannelTotalVo>();
        JSONObject result = new JSONObject();
        try {
            int days = Days.daysBetween(DateTime.parse(start_date), DateTime.parse(end_date)).getDays();
            if(days > 150)
                return "\"ret\":0,\"msg\":\"日期跨度过长，请控制在150天内！\"";

            Map<String, Object> paramsMap = new HashMap<String, Object>();
            paramsMap.put("start_date",start_date);
            paramsMap.put("end_date",end_date);
            paramsMap.put("group", "push_cha");
            paramsMap.put("group_match", "bb.push_cha = aa.push_cha");
            List<AdvFeeVo> list = adService.selectAdvChannelFeeTotalHaiwai(paramsMap);

            System.out.println(start_date+"至"+end_date+" 同步PushCha parseArray == "+list.size());
            for (AdvFeeVo adv : list) {
                if(BlankUtils.checkBlank(adv.getPush_cha()))
                    continue;

                UmengChannelTotalVo uu = new UmengChannelTotalVo();
                uu.setTdate(adv.getAdv_dt());
                uu.setPush_cha(adv.getPush_cha());
                uu.setAdv_fee(adv.getAdv_fee()+"");
                keepList.add(uu);
            }

            adService.updateUmengPushCha(keepList);
            result.put("ret", 1);
            result.put("msg", "success");

            System.out.println(start_date+"至"+end_date+" 完成同步"+DateTime.now());
        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误消息："+e.getMessage());
        }
        return result.toJSONString();
    }


}
