package com.wbgame.controller.advert.query.transport;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.adb.UmengMonitorMapper;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.UmengAdIncomeVo;
import com.wbgame.pojo.UmengAdIncomeWarningVo;
import com.wbgame.pojo.advert.UmengAdIncomeReportVo;
import com.wbgame.pojo.custom.PageForList;
import com.wbgame.service.AdService;
import com.wbgame.service.PlatformDataService;
import com.wbgame.service.UmengService;
import com.wbgame.service.adb.AdvSDKService;
import com.wbgame.service.game.AppService;
import com.wbgame.utils.*;
import com.wbgame.utils.compare.CommonCompareUtils;
import com.wbgame.utils.compare.GapCompareUtils;
import io.swagger.annotations.ApiParam;
import jxl.Sheet;
import jxl.Workbook;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.wbgame.utils.ExportExcelUtil.extractHeader;


/**
 * @description: 渠道产品广告数据查询
 * @author: huangmb
 * @date: 2021/06/05
 **/
@CrossOrigin
@RestController
@RequestMapping("/advert/umengAdvIncome")
public class UmengAdvIncomeController {

    private static Logger logger = LoggerFactory.getLogger(UmengAdvIncomeController.class);

    @Autowired
    private AdService adService;

    @Autowired
    private UmengService umengService;

    @Autowired
    private AppService appService;

    @Autowired
    private AdvSDKService advSDKService;

    @Autowired
    private UmengMonitorMapper umengMonitorMapper;
    @Autowired
    private YyhzMapper yyhzMapper;

    @Autowired
    private PlatformDataService platformDataService;

    private static final String MEDIA_SOURCE = "媒体";
    private static final String SELF_STATISTICS_SOURCE = "自统计";
    //定义数据来源：1-媒体，2-自统计
    private static final String DATA_SOURCE_MEDIA = "1";
    private static final String DATA_SOURCE_SELF = "2";


    @RequestMapping(value="list", method={RequestMethod.GET,RequestMethod.POST})
    public Object list(ModelMap map, HttpServletRequest request,HttpServletResponse response) {

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        String start_date = BlankUtils.checkNull(request, "start_date");
        String end_date = BlankUtils.checkNull(request, "end_date");
        String appid = request.getParameter("appid");
        String media = request.getParameter("media");
        String channel = BlankUtils.checkNull(request, "channel");
        String group = request.getParameter("group");
        String gameName = request.getParameter("gameName");
        String token = request.getParameter("token");
        String is_x = request.getParameter("is_x");
        String temp_id = request.getParameter("temp_id");
        String temp_name = request.getParameter("temp_name");
        String state = request.getParameter("state");
        String active_temp_id = request.getParameter("active_temp_id");
        String active_temp_name = request.getParameter("active_temp_name");
        String large_ver = request.getParameter("large_ver");
        //广告违规类型
        String ad_violation_type = request.getParameter("ad_violation_type");
        //获取来源：1-媒体/2-自统计
        String source = request.getParameter("source");
        // 2024-10-29：渠道产品广告数据查询新增日期汇总功能
        String customDate = request.getParameter("custom_date");
        //应用自定义分组
        String appidTag = request.getParameter("appid_tag");
        //应用自定义分组是否反选
        String appidTagRev = request.getParameter("appid_tag_rev");
        //包名
        String packagename = request.getParameter("packagename");
        String prjid_group_id = BlankUtils.checkNull(request,"prjid_group_id");

        String order_str = request.getParameter("order_str");
        // dataSource 1 友盟 2 oppo
        String dataSource = request.getParameter("dataSource");
        String tableName = "umeng_ad_income";
        if ("2".equals(dataSource)){
            tableName = "oppo_ad_income";
        }
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
            start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        }

        Map<String,String> paramMap = new HashMap<>();
        order_str = generateOrderStrParam(order_str);
        group = generateGroupParam(group);
        paramMap.put("tableName",tableName);
        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("order_str",order_str);
        paramMap.put("group",group);
        paramMap.put("gameName",gameName);
        paramMap.put("channel",channel);
        paramMap.put("appid",appid);
        paramMap.put("media",media);
        paramMap.put("temp_id",temp_id);
        paramMap.put("temp_name",temp_name);
        paramMap.put("state",state);
        paramMap.put("dataSource",dataSource);
        paramMap.put("source",source);
        paramMap.put("active_temp_id",active_temp_id);
        paramMap.put("active_temp_name",active_temp_name);
        paramMap.put("custom_date",customDate);
        paramMap.put("appid_tag",appidTag);
        paramMap.put("appid_tag_rev",appidTagRev);
        paramMap.put("packagename",packagename);
        paramMap.put("large_ver",large_ver);
        paramMap.put("ad_violation_type",ad_violation_type);
        /* prjid_group_id需要解析为appid+cha_id然后进行筛选 */
        if(!BlankUtils.checkBlank(prjid_group_id)) {
            Map<String,String> params = new HashMap<>();
            params.put("ctype", "2");
            params.put("id", prjid_group_id);
            List<Map<String, Object>> groupList = yyhzMapper.selectPrjidGroupConfigConfig(params);

            if(groupList != null && groupList.size() > 0) {

                String collect = groupList.stream().filter(act -> !BlankUtils.checkBlank(act.getOrDefault("prjid","").toString()))
                        .map(act -> act.getOrDefault("prjid","").toString())
                        .collect(Collectors.joining(","));
                String collect2 = groupList.stream().filter(act -> !BlankUtils.checkBlank(act.getOrDefault("chastr","").toString()))
                        .flatMap(act -> Arrays.stream((act.getOrDefault("chastr","").toString()).split(",")))
                        .map(str -> "'"+str+"'")
                        .collect(Collectors.joining(","));

                String match_str = null;
                if(!BlankUtils.checkBlank(collect)){
                    // 项目id的集合时，需要转换为appid#cha_id
                    Map<String, Map<String, Object>> prjMap = adService.selectProjectidChannelMap();
                    for (String prjid : collect.split(",")) {
                        Map<String, Object> act = prjMap.get(prjid);
                        if (null != act) {
                            String match_appid_chaid = act.get("appid") +"#"+ act.get("cha_id");

                            if(BlankUtils.checkBlank(collect2)){
                                collect2 = "'"+match_appid_chaid+"'";
                            }else{
                                collect2 += ",'"+match_appid_chaid+"'";
                            }
                        }
                    }
                }

                if(!BlankUtils.checkBlank(collect2)){
                    match_str = String.format(" concat(c.appid,'#',c.channel) in (%s) ", collect2);
                }
                paramMap.put("match_str", "("+match_str+")");
            }
        }

        List<UmengAdIncomeReportVo> fList = appService.getUmengAdIncomeList(paramMap);
        //汇总数据
        UmengAdIncomeReportVo total = appService.getUmengAdIncomeSum(paramMap);
        if (total!=null){
            total.setAvgnum(total.getAvgnum());
            total.setDaily_duration(StringUtils.secondsToHHmmss(total.getDaily_duration()));
        }

        // 【新增】新增banner比例调整的后台页面
        if("b.tdate,b.appkey,b.channel".equals(group)){
            String query = String.format("select IFNULL(CONCAT(tdate,max_appid,max_cha),'1') mapkey,max_adsid from dn_tuning_adconfig_info where tdate BETWEEN '%s' AND '%s' ",start_date,end_date);
            Map<String, Map<String, Object>> adMap = adService.queryListMapOfKey(query);

            fList.forEach(act -> {
                String key = act.getTdate() + act.getAppid() + act.getChannel();
                Map<String, Object> act2 = adMap.get(key);
                if(act2 != null){
                    String[] split = act2.getOrDefault("max_adsid", "").toString().split("_");
                    if(split.length == 4 && split[1].equals("banner")){
                        act.setBanner_show_rate("90");
                        act.setNew_banner_show_rate("10");
                    }else{
                        act.setBanner_show_rate("10");
                        act.setNew_banner_show_rate("90");
                    }
                    // System.out.println(String.format("split==%s，banner==%s，new banner==%s",Arrays.toString(split),act.getBanner_show_rate(),act.getNew_banner_show_rate()));
                }
                act.setSum_pv_banner(act.getSum_pv_banner()+"%");
                act.setSum_pv_new_banner(act.getSum_pv_new_banner()+"%");
            });
        }

        //查询是否是x模式
        List<UmengAdIncomeReportVo> isXList=appService.isXList(paramMap);
        Map<String,UmengAdIncomeReportVo> xMap = new HashMap<>();
        isXList.forEach(t->{
            String key = t.getTdate()+"_"+t.getAppid()+"_"+t.getChannel();
            xMap.put(key,t);
        });
        //取前一天数据
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
        start_date = DateTime.parse(start_date,format).minusDays(1).toString("yyyy-MM-dd");

        paramMap.put("start_date",start_date);
        List<UmengAdIncomeReportVo> sList = appService.getUmengAdIncomeList(paramMap);
        Map<String,UmengAdIncomeReportVo> sMap = new HashMap<>();
        sList.forEach(t->{
            String key = t.getTdate()+"_"+t.getAppkey()+"_"+t.getChannel();
            sMap.put(key,t);
        });

        //取上周数据
        start_date = DateTime.parse(start_date,format).minusDays(7).toString("yyyy-MM-dd");
        paramMap.put("start_date",start_date);
        List<UmengAdIncomeReportVo> tList = appService.getUmengAdIncomeList(paramMap);
        Map<String,UmengAdIncomeReportVo> tMap = new HashMap<>();
        tList.forEach(t->{
            String key = t.getTdate()+"_"+t.getAppkey()+"_"+t.getChannel();
            tMap.put(key,t);
        });
        fList.forEach(t->{
            String tdate = t.getTdate();
            String btdate = DateTime.parse(tdate,format).minusDays(1).toString("yyyy-MM-dd");
            String ttdate = DateTime.parse(tdate,format).minusDays(7).toString("yyyy-MM-dd");

            String key = btdate+"_"+t.getAppkey()+"_"+t.getChannel();
            String tkey = ttdate+"_"+t.getAppkey()+"_"+t.getChannel();
            
            //处理avgnum
            String avgnum = t.getAvgnum();
            t.setAvgnum(strToTwoPercent(avgnum));
            //处理在线时长
            String dailyDuration = t.getDaily_duration();
            t.setDaily_duration(StringUtils.secondsToHHmmss(dailyDuration));

            //默认环比增长为0.00%
            t.setDau_arpu_rate("0.00%");
            t.setBanner_ecpm_rate("0.00%");
            t.setNative_banner_ecpm_rate("0.00%");

            t.setPlaque_video_ecpm_rate("0.00%");
            t.setNative_new_plaque_ecpm_rate("0.00%");
            t.setNative_msg_ecpm_rate("0.00%");
            t.setNative_splash_ecpm_rate("0.00%");
            t.setSuspend_icon_ecpm_rate("0.00%");
            t.setSplash_ecpm_rate("0.00%");
            t.setSystem_splash_ecpm_rate("0.00%");
            t.setNative_plaque_ecpm_rate("0.00%");
            t.setVideo_ecpm_rate("0.00%");
            t.setPlaque_ecpm_rate("0.00%");
            t.setNative_new_banner_ecpm_rate("0.00%");

            t.setAvgNumRate("0.00%");
            t.setDailyDurationRate("0.00%");

            String tAvgNum = "";
            String tDailyDuration = "";

            String ndau_arpu = "";
            String nbanner_ecpm = "";
            String nnative_banner_ecpm = "";
            String nplaque_video_ecpm = "";
            String nnative_new_plaque_ecpm = "";
            String nnative_msg_ecpm = "";
            String nnative_splash_ecpm = "";
            String nsuspend_icon_ecpm = "";
            String nsplash_ecpm = "";
            String nsystem_splash_ecpm = "";
            String nnative_plaque_ecpm = "";
            String nvideo_ecpm = "";
            String nplaque_ecpm = "";
            String nnative_new_banner_ecpm = "";

            UmengAdIncomeReportVo vo = sMap.get(key);
            if (vo!=null){
                ndau_arpu = vo.getDau_arpu();
                nbanner_ecpm = vo.getBanner_ecpm();
                nnative_banner_ecpm = vo.getNative_banner_ecpm();
                nplaque_video_ecpm = vo.getPlaque_video_ecpm();
                nnative_new_plaque_ecpm = vo.getNative_new_plaque_ecpm();
                nnative_msg_ecpm = vo.getNative_msg_ecpm();
                nnative_splash_ecpm = vo.getNative_splash_ecpm();
                nsuspend_icon_ecpm = vo.getSuspend_icon_ecpm();
                nsplash_ecpm = vo.getSplash_ecpm();
                nsystem_splash_ecpm = vo.getSystem_splash_ecpm();
                nnative_plaque_ecpm = vo.getNative_plaque_ecpm();
                nvideo_ecpm = vo.getVideo_ecpm();
                nplaque_ecpm = vo.getPlaque_ecpm();
                nnative_new_banner_ecpm = vo.getNative_new_banner_ecpm();
            }
            if (!BlankUtils.checkBlank(ndau_arpu)){
                t.setDau_arpu_rate(strToTwoNumber(t.getDau_arpu(),ndau_arpu));
            }
            if (!BlankUtils.checkBlank(nbanner_ecpm)){
                t.setBanner_ecpm_rate(strToTwoNumber(t.getBanner_ecpm(),nbanner_ecpm));
            }
            if (!BlankUtils.checkBlank(nnative_banner_ecpm)){
                t.setNative_banner_ecpm_rate(strToTwoNumber(t.getNative_banner_ecpm(),nnative_banner_ecpm));
            }
            if (!BlankUtils.checkBlank(nplaque_video_ecpm)){
                t.setPlaque_video_ecpm_rate(strToTwoNumber(t.getPlaque_video_ecpm(),nplaque_video_ecpm));
            }
            if (!BlankUtils.checkBlank(nnative_new_plaque_ecpm)){
                t.setNative_new_plaque_ecpm_rate(strToTwoNumber(t.getNative_new_plaque_ecpm(),nnative_new_plaque_ecpm));
            }
            if (!BlankUtils.checkBlank(nnative_msg_ecpm)){
                t.setNative_msg_ecpm_rate(strToTwoNumber(t.getNative_msg_ecpm(),nnative_msg_ecpm));
            }
            if (!BlankUtils.checkBlank(nnative_splash_ecpm)){
                t.setNative_splash_ecpm_rate(strToTwoNumber(t.getNative_splash_ecpm(),nnative_splash_ecpm));
            }
            if (!BlankUtils.checkBlank(nsuspend_icon_ecpm)){
                t.setSuspend_icon_ecpm_rate(strToTwoNumber(t.getSuspend_icon_ecpm(),nsuspend_icon_ecpm));
            }
            if (!BlankUtils.checkBlank(nsplash_ecpm)){
                t.setSplash_ecpm_rate(strToTwoNumber(t.getSplash_ecpm(),nsplash_ecpm));
            }
            if (!BlankUtils.checkBlank(nsystem_splash_ecpm)){
                t.setSystem_splash_ecpm_rate(strToTwoNumber(t.getSystem_splash_ecpm(),nsystem_splash_ecpm));
            }
            if (!BlankUtils.checkBlank(nnative_plaque_ecpm)){
                t.setNative_plaque_ecpm_rate(strToTwoNumber(t.getNative_plaque_ecpm(),nnative_plaque_ecpm));
            }
            if (!BlankUtils.checkBlank(nvideo_ecpm)){
                t.setVideo_ecpm_rate(strToTwoNumber(t.getVideo_ecpm(),nvideo_ecpm));
            }
            if (!BlankUtils.checkBlank(nplaque_ecpm)){
                t.setPlaque_ecpm_rate(strToTwoNumber(t.getPlaque_ecpm(),nplaque_ecpm));
            }
            if (!BlankUtils.checkBlank(nnative_new_banner_ecpm)){
                t.setNative_new_banner_ecpm_rate(strToTwoNumber(t.getNative_new_banner_ecpm(),nnative_new_banner_ecpm));
            }

            t.setTotal_banner_pv_rate("0.00%");
            t.setTotal_splash_pv_rate("0.00%");
            t.setTotal_video_pv_rate("0.00%");
            t.setTotal_plaque_pv_rate("0.00%");
            t.setTotal_suspend_icon_pv_rate("0.00%");
            t.setTotal_plaque_video_pv_rate("0.00%");
            t.setTotal_native_msg_pv_rate("0.00%");
            t.setDaily_duration_rate("0.00%");
            t.setAvgnum_rate("0.00%");

            String ttotal_banner_pv = "";
            String ttotal_splash_pv = "";
            String ttotal_video_pv = "";
            String ttotal_plaque_pv = "";
            String ttotal_suspend_icon_pv = "";
            String ttotal_plaque_video_pv = "";
            String ttotal_native_msg_pv = "";
            String tavgnum = "";
            String tdaily_duration = "";

            UmengAdIncomeReportVo to = tMap.get(tkey);
            if (to!=null){
                ttotal_banner_pv = to.getTotal_banner_pv();
                ttotal_splash_pv = to.getTotal_splash_pv();
                ttotal_video_pv = to.getTotal_video_pv();
                ttotal_plaque_pv = to.getTotal_plaque_pv();
                ttotal_suspend_icon_pv = to.getTotal_suspend_icon_pv();
                ttotal_plaque_video_pv = to.getTotal_plaque_video_pv();
                ttotal_native_msg_pv = to.getTotal_native_msg_pv();
                tAvgNum = to.getAvgnum();
                tDailyDuration = to.getDaily_duration();
                tavgnum = to.getAvgnum();
                tdaily_duration = to.getDaily_duration();
            }

            if (!BlankUtils.checkBlank(tAvgNum)){
                t.setAvgNumRate(strToTwoNumber(avgnum,tAvgNum));
            }
            if (!BlankUtils.checkBlank(tDailyDuration)){
                t.setDailyDurationRate(strToTwoNumber(dailyDuration,tDailyDuration));
            }
            if (!BlankUtils.checkBlank(ttotal_banner_pv)){
                t.setTotal_banner_pv_rate(strToTwoNumber(t.getTotal_banner_pv(),ttotal_banner_pv));
            }
            if (!BlankUtils.checkBlank(ttotal_splash_pv)){
                t.setTotal_splash_pv_rate(strToTwoNumber(t.getTotal_splash_pv(),ttotal_splash_pv));
            }
            if (!BlankUtils.checkBlank(ttotal_video_pv)){
                t.setTotal_video_pv_rate(strToTwoNumber(t.getTotal_video_pv(),ttotal_video_pv));
            }
            if (!BlankUtils.checkBlank(ttotal_plaque_pv)){
                t.setTotal_plaque_pv_rate(strToTwoNumber(t.getTotal_plaque_pv(),ttotal_plaque_pv));
            }
            if (!BlankUtils.checkBlank(ttotal_suspend_icon_pv)){
                t.setTotal_suspend_icon_pv_rate(strToTwoNumber(t.getTotal_suspend_icon_pv(),ttotal_suspend_icon_pv));
            }
            if (!BlankUtils.checkBlank(ttotal_plaque_video_pv)){
                t.setTotal_plaque_video_pv_rate(strToTwoNumber(t.getTotal_plaque_video_pv(),ttotal_plaque_video_pv));
            }
            if (!BlankUtils.checkBlank(ttotal_native_msg_pv)){
                t.setTotal_native_msg_pv_rate(strToTwoNumber(t.getTotal_native_msg_pv(),ttotal_native_msg_pv));
            }

            if (!BlankUtils.checkBlank(tavgnum)){
                t.setAvgnum_rate(strToTwoNumber(avgnum,tavgnum));
            }
            if (!BlankUtils.checkBlank(tdaily_duration)){
                t.setDaily_duration_rate(strToTwoNumber(dailyDuration,tdaily_duration));
            }
            t.setBanner_ctr(t.getBanner_ctr() + "%");
            t.setPlaque_ctr(t.getPlaque_ctr() + "%");
            t.setSplash_ctr(t.getSplash_ctr() + "%");
            t.setVideo_ctr(t.getVideo_ctr() + "%");
            t.setNative_banner_ctr(t.getNative_banner_ctr() + "%");
            t.setNative_msg_ctr(t.getNative_msg_ctr() + "%");
            t.setNative_plaque_ctr(t.getNative_plaque_ctr() + "%");
            t.setNative_splash_ctr(t.getNative_splash_ctr() + "%");
            t.setNative_new_banner_ctr(t.getNative_new_banner_ctr()+"%");
            t.setNative_new_plaque_ctr(t.getNative_new_plaque_ctr() + "%");
            t.setPlaque_video_ctr(t.getPlaque_video_ctr() + "%");
            t.setSuspend_icon_ctr(t.getSuspend_icon_ctr() + "%");
            
            //处理是否x模式
            String xappid=t.getAppid();
            String xkey = tdate+"_"+xappid+"_"+t.getChannel();
            UmengAdIncomeReportVo x_vo = xMap.get(xkey);
            if(null!=x_vo){
                t.setIs_x(x_vo.getIs_x());
            }else{
                t.setIs_x("0");
            }
        });


        //根据前端筛选条件 是否是x模式进行过滤
        if(!StringUtils.isBlank(is_x)){
            if("1".equals(is_x)){
                fList=fList.stream().filter(f ->"1".equals(f.getIs_x())).collect(Collectors.toList());
            }else{
                fList=fList.stream().filter(f -> !"1".equals(f.getIs_x())).collect(Collectors.toList());
            }
        }
        JSONObject ret = new JSONObject();
        //分页
        PageForList<UmengAdIncomeReportVo> pager = new PageForList<>(pageNo, pageSize, fList);
        pager.getResultList();
        ret.put("ret",1);
        ret.put("msg","success");
        ret.put("total",total);
        ret.put("data",pager.getResultList());
        ret.put("totalSize",fList.size());
        return ret;
    }

    private final static String DEFINED_RATE_FIELDS = "dau_arpu,native_banner_ecpm,banner_ecpm,plaque_video_ecpm,native_new_plaque_ecpm,native_msg_ecpm,native_splash_ecpm,suspend_icon_ecpm,splash_ecpm,system_splash_ecpm,native_plaque_ecpm,video_ecpm,plaque_ecpm,native_new_banner_ecpm,total_banner_pv,total_splash_pv,total_video_pv,total_plaque_pv,total_suspend_icon_pv,total_plaque_video_pv,total_native_msg_pv,avgnum,daily_duration,total_banner_click,total_splash_click,total_video_click,total_plaque_click,total_suspend_icon_click,total_plaque_video_click,total_native_msg_click,all_total_pv,all_total_click,total_banner_arpu,total_splash_arpu,total_video_arpu,total_native_msg_arpu,total_plaque_arpu,total_plaque_video_arpu,total_suspend_icon_arpu,all_total_ctr,all_total_request," +
            "total_splash_request,total_plaque_request,total_plaque_video_request,total_banner_request,total_video_request,total_native_msg_request,total_suspend_icon_request,all_total_avg_fill,total_splash_avg_fill,total_plaque_avg_fill,total_plaque_video_avg_fill,total_banner_avg_fill,total_video_avg_fill,total_native_msg_avg_fill,total_suspend_icon_avg_fill,all_total_fill,total_system_splash_fill,total_splash_fill,total_native_splash_fill,total_plaque_fill,total_native_new_plaque_fill,total_plaque_video_fill,total_banner_fill,total_native_new_banner_fill,total_video_fill,total_native_msg_fill,total_suspend_icon_fill,plaque_video_cpc,native_msg_cpc,native_new_banner_cpc,banner_cpc,native_new_plaque_cpc,video_cpc,native_splash_cpc,plaque_cpc,native_plaque_cpc,native_banner_cpc,suspend_icon_cpc,splash_cpc," +
            "splash_ctr,banner_ctr,plaque_ctr,video_ctr,native_banner_ctr,native_msg_ctr,native_plaque_ctr,native_splash_ctr,native_new_banner_ctr,native_new_plaque_ctr,plaque_video_ctr,suspend_icon_ctr";

    //渗透率相关字段
    private final static String SEEK_RATE_FIELDS = "show_splash_ad_active_cnt,show_plaque_ad_active_cnt,show_banner_ad_active_cnt,show_video_ad_active_cnt,show_msg_ad_active_cnt,show_icon_ad_active_cnt,click_splash_ad_active_cnt,click_plaque_ad_active_cnt,click_banner_ad_active_cnt,click_video_ad_active_cnt,click_msg_ad_active_cnt,click_icon_ad_active_cnt,show_total_ad_active_cnt,click_total_ad_active_cnt";

    //预警表相关字段
    private final static String WARN_RATE_FIELDS = "dau_arpu,native_banner_ecpm,banner_ecpm,plaque_video_ecpm,native_new_plaque_ecpm,native_msg_ecpm,native_splash_ecpm,suspend_icon_ecpm,splash_ecpm,system_splash_ecpm,native_plaque_ecpm,video_ecpm,plaque_ecpm,native_new_banner_ecpm,total_banner_pv,total_splash_pv,total_video_pv,total_plaque_pv,total_suspend_icon_pv,total_plaque_video_pv,total_native_msg_pv,avgnum,daily_duration,total_banner_click,total_splash_click,total_video_click,total_plaque_click,total_suspend_icon_click,total_plaque_video_click,total_native_msg_click,all_total_pv,all_total_click,total_banner_arpu,total_splash_arpu,total_video_arpu,total_native_msg_arpu,total_plaque_arpu,total_plaque_video_arpu,total_suspend_icon_arpu,all_total_ctr," +
            "plaque_video_cpc,native_msg_cpc,native_new_banner_cpc,banner_cpc,native_new_plaque_cpc,video_cpc,native_splash_cpc,plaque_cpc,native_plaque_cpc,native_banner_cpc,suspend_icon_cpc,splash_cpc," +
            "splash_ctr,banner_ctr,plaque_ctr,video_ctr,native_banner_ctr,native_msg_ctr,native_plaque_ctr,native_splash_ctr,native_new_banner_ctr,native_new_plaque_ctr,plaque_video_ctr,suspend_icon_ctr,total_splash_ctr";

    //gap率计算相关字段(人均pv,人均点击,总人均pv,总人均点击,ctr)
    private final static String GAP_RATE_FIELDS = "system_splash_pv,splash_pv,native_splash_pv,plaque_pv,native_plaque_pv,native_new_plaque_pv,plaque_video_pv,banner_pv,native_banner_pv,native_new_banner_pv,video_pv,native_msg_pv,suspend_icon_pv,system_splash_avg_click,splash_avg_click,native_splash_avg_click,plaque_avg_click,native_plaque_avg_click,native_new_plaque_avg_click,plaque_video_avg_click,banner_avg_click,native_banner_avg_click,native_new_banner_avg_click,video_avg_click,native_msg_avg_click,suspend_icon_avg_click,all_total_pv,total_splash_pv,total_plaque_pv,total_plaque_video_pv,total_banner_pv,total_video_pv,total_native_msg_pv,total_suspend_icon_pv,all_total_click,total_splash_click,total_plaque_click,total_plaque_video_click,total_banner_click,total_video_click,total_native_msg_click," +
            "total_suspend_icon_click,all_total_ctr,splash_ctr,native_splash_ctr,plaque_ctr,native_plaque_ctr,native_new_plaque_ctr,plaque_video_ctr,banner_ctr,native_banner_ctr,native_new_banner_ctr,video_ctr,native_msg_ctr,suspend_icon_ctr";

    //占比相关字段(人均pv,人均点击,总人均pv,总人均点击,ARPU)
    private final static Map<String,String> RATIO_RATE_FIELDS = new HashMap<String,String>(){
        {
            //人均pv
            put("all_total_pv","system_splash_pv,splash_pv,native_splash_pv,plaque_pv,native_plaque_pv,native_new_plaque_pv,plaque_video_pv,banner_pv,native_banner_pv,native_new_banner_pv,video_pv,native_msg_pv,suspend_icon_pv,all_total_pv,total_splash_pv,total_plaque_pv,total_plaque_video_pv,total_banner_pv,total_video_pv,total_native_msg_pv,total_suspend_icon_pv");
            //人均点击
            put("all_total_click","system_splash_avg_click,splash_avg_click,native_splash_avg_click,plaque_avg_click,native_plaque_avg_click,native_new_plaque_avg_click,plaque_video_avg_click,banner_avg_click,native_banner_avg_click,native_new_banner_avg_click,video_avg_click,native_msg_avg_click,suspend_icon_avg_click,all_total_click,total_splash_click,total_plaque_click,total_plaque_video_click,total_banner_click,total_video_click,total_native_msg_click,total_suspend_icon_click");
            //ARPU
            put("dau_arpu","total_splash_arpu,total_plaque_arpu,total_plaque_video_arpu,total_banner_arpu,total_video_arpu,total_native_msg_arpu,total_suspend_icon_arpu");
        }
    };

    /**
     * 渠道产品广告数据查询
     * modify: xiaoxh-2024-07-30：渠道产品广告数据新增自统计数据查询
     * modify: xiaoxh-2024-07-31：渠道产品广告数据 原生新样式插屏更名为msg/yuans插屏，调整原生新样式取值逻辑：计算公式：msg/yuans插屏=原生插屏+原生新样式插屏
     * modify: xiaoxh-2024-08-12：渠道产品广告数据 自统计查询调整
     * @param map
     * @param request 请求体
     * @param response 响应体
     * @return 查询结果
     */
    @ControllerLoggingEnhancer
    @RequestMapping(value="/v2/list", method={RequestMethod.GET,RequestMethod.POST})
    public Object listV2(ModelMap map, HttpServletRequest request,HttpServletResponse response) {

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        String start_date = BlankUtils.checkNull(request, "start_date");
        String end_date = BlankUtils.checkNull(request, "end_date");
        String appid = request.getParameter("appid");
        String media = request.getParameter("media");
        String channel = BlankUtils.checkNull(request, "channel");
        String group = request.getParameter("group");
        String gameName = request.getParameter("gameName");
        //String token = request.getParameter("token");
        String is_x = request.getParameter("is_x");
        String dimension = request.getParameter("dimension");
        String temp_id = request.getParameter("temp_id");
        String temp_name = request.getParameter("temp_name");
        String state = request.getParameter("state");
        String active_temp_id = request.getParameter("active_temp_id");
        String active_temp_name = request.getParameter("active_temp_name");
        String large_ver = request.getParameter("large_ver");
        //广告违规类型
        String ad_violation_type = request.getParameter("ad_violation_type");

        //获取来源：1-媒体/2-自统计
        String source = request.getParameter("source");
        // 2024-10-29：渠道产品广告数据查询新增日期汇总功能
        String customDate = request.getParameter("custom_date");
        //应用自定义分组
        String appidTag = request.getParameter("appid_tag");
        String appidTagName = request.getParameter("appid_tag_name");
        //应用自定义分组是否反选
        String appidTagRev = request.getParameter("appid_tag_rev");
        //包名
        String packagename = request.getParameter("packagename");

        String prjid_group_id = BlankUtils.checkNull(request,"prjid_group_id");
        if (BlankUtils.isBlank(dimension)) {
            return ReturnJson.toErrorJson("请选择环比同比");
        }

        String order_str = request.getParameter("order_str");
        // dataSource 1 友盟 2 oppo
        String dataSource = request.getParameter("dataSource");
        String tableName = "umeng_ad_income";
        if ("2".equals(dataSource)){
            tableName = "oppo_ad_income";
        }
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        /*if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
            start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        }*/

        Map<String,String> paramMap = new HashMap<>();
        order_str = DataTransUtils.generateOrderStrParam("b",order_str);
        group = generateGroupParam(group);
        paramMap.put("tableName",tableName);
        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("order_str",order_str);
        paramMap.put("group",group);
        paramMap.put("gameName",gameName);
        paramMap.put("channel",channel);
        paramMap.put("appid",appid);
        paramMap.put("media",media);
        paramMap.put("temp_id",temp_id);
        paramMap.put("temp_name",temp_name);
        paramMap.put("state",state);
        paramMap.put("dataSource",dataSource);
        paramMap.put("source",source);
        paramMap.put("active_temp_id",active_temp_id);
        paramMap.put("active_temp_name",active_temp_name);
        paramMap.put("custom_date",customDate);
        paramMap.put("appid_tag",appidTag);
        paramMap.put("appid_tag_name",appidTagName);
        paramMap.put("appid_tag_rev",appidTagRev);
        paramMap.put("packagename",packagename);
        paramMap.put("large_ver",large_ver);
        paramMap.put("ad_violation_type",ad_violation_type);
        /* prjid_group_id需要解析为appid+cha_id然后进行筛选 */
        if(!BlankUtils.checkBlank(prjid_group_id)) {
            Map<String,String> params = new HashMap<>();
            params.put("ctype", "2");
            params.put("id", prjid_group_id);
            List<Map<String, Object>> groupList = yyhzMapper.selectPrjidGroupConfigConfig(params);

            if(groupList != null && groupList.size() > 0) {

                String collect = groupList.stream().filter(act -> !BlankUtils.checkBlank(act.getOrDefault("prjid","").toString()))
                            .map(act -> act.getOrDefault("prjid","").toString())
                            .collect(Collectors.joining(","));
                String collect2 = groupList.stream().filter(act -> !BlankUtils.checkBlank(act.getOrDefault("chastr","").toString()))
                        .flatMap(act -> Arrays.stream((act.getOrDefault("chastr","").toString()).split(",")))
                            .map(str -> "'"+str+"'")
                            .collect(Collectors.joining(","));

                String match_str = null;
                if(!BlankUtils.checkBlank(collect)){
                    // 项目id的集合时，需要转换为appid#cha_id
                    Map<String, Map<String, Object>> prjMap = adService.selectProjectidChannelMap();
                    for (String prjid : collect.split(",")) {
                        Map<String, Object> act = prjMap.get(prjid);
                        if (null != act) {
                            String match_appid_chaid = act.get("appid") +"#"+ act.get("cha_id");

                            if(BlankUtils.checkBlank(collect2)){
                                collect2 = "'"+match_appid_chaid+"'";
                            }else{
                                collect2 += ",'"+match_appid_chaid+"'";
                            }
                        }
                    }
                }

                if(!BlankUtils.checkBlank(collect2)){
                    match_str = String.format(" concat(c.appid,'#',c.channel) in (%s) ", collect2);
                }
                paramMap.put("match_str", "("+match_str+")");
            }
        }


        //String concatStr = appService.getAppChannelList(paramMap);
        //paramMap.put("con_ac", concatStr);

        //汇总数据 --自定义时间段无汇总
        UmengAdIncomeReportVo total = null;
        if (StringUtils.isEmpty(customDate)) {
            //自定义时间段汇总不进行汇总数据查询
            if("2".equals(paramMap.get("dataSource"))) {
                total = appService.getUmengAdIncomeSum(paramMap);
            }else {
                total = umengMonitorMapper.getUmengAdIncomeSum(paramMap);
            }
        }

        if (total!=null){
            total.setAvgnum(total.getAvgnum());
            total.setDaily_duration(StringUtils.secondsToHHmmss(total.getDaily_duration()));
        }
        List<UmengAdIncomeReportVo> fList = getUmengAdIncomeReportVos(start_date, end_date, group, is_x, dimension, paramMap);

        //int totalCount = fList.size();
        PageForList<UmengAdIncomeReportVo> pager = new PageForList<>(pageNo, pageSize, fList);
        JSONObject ret = new JSONObject();
        ret.put("ret",1);
        ret.put("msg","success");
        ret.put("total",total);
        ret.put("data",pager.getResultList());
        ret.put("totalSize",pager.getTotalRows());
        return ret;
    }

    /**
     * 渠道产品广告数据查询--不分页数据
     * @param request 请求体
     * @return 查询结果
     */
    @ControllerLoggingEnhancer
    @RequestMapping(value="/v2/common", method={RequestMethod.GET,RequestMethod.POST})
    public Object listV2NotLimit(HttpServletRequest request) {
        String start_date = BlankUtils.checkNull(request, "start_date");
        String end_date = BlankUtils.checkNull(request, "end_date");
        String appid = request.getParameter("appid");
        String media = request.getParameter("media");
        String channel = BlankUtils.checkNull(request, "channel");
        String group = request.getParameter("group");
        String gameName = request.getParameter("gameName");
        String is_x = request.getParameter("is_x");
        String dimension = request.getParameter("dimension");
        String temp_id = request.getParameter("temp_id");
        String temp_name = request.getParameter("temp_name");
        String state = request.getParameter("state");
        //获取来源：1-媒体/2-自统计
        String source = request.getParameter("source");
        String active_temp_id = request.getParameter("active_temp_id");
        String active_temp_name = request.getParameter("active_temp_name");
        //包名
        String packagename = request.getParameter("packagename");
        String large_ver = request.getParameter("large_ver");
        String ad_violation_type = request.getParameter("ad_violation_type");

        String prjid_group_id = BlankUtils.checkNull(request,"prjid_group_id");
        if (BlankUtils.isBlank(dimension)) {
            return ReturnJson.toErrorJson("请选择环比同比");
        }
        String order_str = request.getParameter("order_str");
        // dataSource 1 友盟 2 oppo
        String dataSource = request.getParameter("dataSource");
        String tableName = "umeng_ad_income";
        if ("2".equals(dataSource)){
            tableName = "oppo_ad_income";
        }
        // 2024-10-29：渠道产品广告数据查询新增日期汇总功能
        String customDate = request.getParameter("custom_date");
        //应用自定义分组
        String appidTag = request.getParameter("appid_tag");
        String appidTagName = request.getParameter("appid_tag_name");
        //应用自定义分组是否反选
        String appidTagRev = request.getParameter("appid_tag_rev");

        /*if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
            start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        }*/
        Map<String,String> paramMap = new HashMap<>();
        order_str = DataTransUtils.generateOrderStrParam("b",order_str);
        group = generateGroupParam(group);
        paramMap.put("tableName",tableName);
        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("order_str",order_str);
        paramMap.put("group",group);
        paramMap.put("gameName",gameName);
        paramMap.put("channel",channel);
        paramMap.put("appid",appid);
        paramMap.put("media",media);
        paramMap.put("temp_id",temp_id);
        paramMap.put("temp_name",temp_name);
        paramMap.put("state",state);
        paramMap.put("dataSource",dataSource);
        paramMap.put("source",source);
        paramMap.put("active_temp_id",active_temp_id);
        paramMap.put("active_temp_name",active_temp_name);
        paramMap.put("custom_date",customDate);
        paramMap.put("appid_tag",appidTag);
        paramMap.put("appid_tag_name",appidTagName);
        paramMap.put("appid_tag_rev",appidTagRev);
        paramMap.put("packagename",packagename);
        paramMap.put("large_ver",large_ver);
        paramMap.put("ad_violation_type",ad_violation_type);
        /* prjid_group_id需要解析为appid+cha_id然后进行筛选 */
        if(!BlankUtils.checkBlank(prjid_group_id)) {
            List<String> matchList = new ArrayList<String>();
            Map<String, Map<String, Object>> prjMap = adService.selectProjectidChannelMap();
            for (String prjid : prjid_group_id.split(",")) {
                Map<String, Object> act = prjMap.get(prjid);
                if (null != act) {
                    String match_appid_chaid = act.get("appid") + "" + act.get("cha_id");
                    matchList.add(match_appid_chaid);
                } else {
                    matchList.add("notmatch");
                }
            }
            paramMap.put("match_str", "'" + String.join("','", matchList) + "'");
        }
        //String concatStr = appService.getAppChannelList(paramMap);
        //paramMap.put("con_ac", concatStr);

        //汇总数据
        UmengAdIncomeReportVo total = null;
        if("2".equals(paramMap.get("dataSource"))) {
            total = appService.getUmengAdIncomeSum(paramMap);
        }else {
            total = umengMonitorMapper.getUmengAdIncomeSum(paramMap);
        }
        List<UmengAdIncomeReportVo> fList = getUmengAdIncomeReportVos(start_date, end_date, group, is_x, dimension, paramMap);
        if (!ObjectUtils.isEmpty(total)){
            total.setTdate("汇总");
            total.setAvgnum(total.getAvgnum());
            total.setDaily_duration(StringUtils.secondsToHHmmss(total.getDaily_duration()));
            fList.add(total);
        }
        JSONObject ret = new JSONObject();
        ret.put("ret",1);
        ret.put("msg","查询成功");
        ret.put("data",fList);
        return ret;
    }





    @ControllerLoggingEnhancer
    @RequestMapping(value="/v2/export", method={RequestMethod.GET,RequestMethod.POST})
    public void exportV2(ModelMap map, HttpServletRequest request,HttpServletResponse response) {

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        String start_date = BlankUtils.checkNull(request, "start_date");
        String end_date = BlankUtils.checkNull(request, "end_date");
        String appid = request.getParameter("appid");
        String media = request.getParameter("media");
        String gameName = request.getParameter("gameName");
        String channel = BlankUtils.checkNull(request, "channel");
        String group = request.getParameter("group");
        String token = request.getParameter("token");
        String is_x = request.getParameter("is_x");
        String dimension = request.getParameter("dimension");
        String temp_id = request.getParameter("temp_id");
        String temp_name = request.getParameter("temp_name");
        String state = request.getParameter("state");

        String active_temp_id = request.getParameter("active_temp_id");
        String active_temp_name = request.getParameter("active_temp_name");

        String prjid_group_id = BlankUtils.checkNull(request,"prjid_group_id");
        if (BlankUtils.isBlank(dimension)) {
            return;
        }

        //包名
        String packagename = request.getParameter("packagename");
        String large_ver = request.getParameter("large_ver");
        String ad_violation_type = request.getParameter("ad_violation_type");

        String order_str = request.getParameter("order_str");
        // dataSource 1 友盟 2 oppo
        String dataSource = request.getParameter("dataSource");
        String tableName = "umeng_ad_income";
        if ("2".equals(dataSource)){
            tableName = "oppo_ad_income";
        }
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        /*if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
            start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        }*/
        // 2024-10-29：渠道产品广告数据查询新增日期汇总功能
        String customDate = request.getParameter("custom_date");
        //应用自定义分组
        String appidTag = request.getParameter("appid_tag");
        String appidTagName = request.getParameter("appid_tag_name");
        //应用自定义分组是否反选
        String appidTagRev = request.getParameter("appid_tag_rev");

        //获取来源：1-媒体/2-自统计
        String source = request.getParameter("source");
        Map<String,String> paramMap = new HashMap<>();
        order_str = DataTransUtils.generateOrderStrParam("b",order_str);
        group = generateGroupParam(group);
        paramMap.put("tableName",tableName);
        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("order_str",order_str);
        paramMap.put("group",group);
        paramMap.put("channel",channel);
        paramMap.put("gameName",gameName);
        paramMap.put("appid",appid);
        paramMap.put("media",media);
        paramMap.put("temp_id",temp_id);
        paramMap.put("temp_name",temp_name);
        paramMap.put("state",state);
        paramMap.put("dataSource",dataSource);
        paramMap.put("source",source);
        paramMap.put("active_temp_id",active_temp_id);
        paramMap.put("active_temp_name",active_temp_name);
        paramMap.put("custom_date",customDate);
        paramMap.put("appid_tag",appidTag);
        paramMap.put("appid_tag_name",appidTagName);
        paramMap.put("appid_tag_rev",appidTagRev);
        paramMap.put("packagename",packagename);
        paramMap.put("large_ver",large_ver);
        paramMap.put("ad_violation_type",ad_violation_type);
        /* prjid_group_id需要解析为appid+cha_id然后进行筛选 */
        if(!BlankUtils.checkBlank(prjid_group_id)) {
            List<String> matchList = new ArrayList<String>();
            Map<String, Map<String, Object>> prjMap = adService.selectProjectidChannelMap();
            for (String prjid : prjid_group_id.split(",")) {
                Map<String, Object> act = prjMap.get(prjid);
                if (null != act) {
                    String match_appid_chaid = act.get("appid") + "" + act.get("cha_id");
                    matchList.add(match_appid_chaid);
                } else {
                    matchList.add("notmatch");
                }
            }
            paramMap.put("match_str", "'" + String.join("','", matchList) + "'");
        }


        //String concatStr = appService.getAppChannelList(paramMap);
        //paramMap.put("con_ac", concatStr);

        List<UmengAdIncomeReportVo> list = getUmengAdIncomeReportVos(start_date, end_date, group, is_x, dimension, paramMap);

        String value = BlankUtils.checkNull(request,"value");
        Map head = new LinkedHashMap();
        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        getState(list);
        ExportExcelUtil.exportXLSX2(response,list,head,"渠道产品广告数据查询_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx");
    }

    private List<UmengAdIncomeReportVo> getUmengAdIncomeReportVos(String start_date, String end_date, String group, String is_x, String dimension, Map<String, String> paramMap) {
        List<UmengAdIncomeReportVo> fList = new ArrayList<>();
        String customDate = paramMap.get("custom_date");
        if (StringUtils.isEmpty(customDate)) {
            //非自定义时间段
            /* 区分为1-友盟计算数据时，使用adb源查询 */
            if("2".equals(paramMap.get("dataSource"))) {
                fList = appService.getUmengAdIncomeList(paramMap);
            }else {
                fList = umengMonitorMapper.getUmengAdIncomeList(paramMap);
            }
        } else {
            //为自定义时间段 格式： 2024-10-10,2024-10-15;2024-11-03,2024-11-05
            String[] split = customDate.split(";");
            for (String data : split) {
                String[] dateSplit = data.split(",");
                paramMap.put("start_date",dateSplit[0]);
                paramMap.put("end_date",dateSplit[1]);
                if("2".equals(paramMap.get("dataSource"))) {
                    fList.addAll(appService.getUmengAdIncomeList(paramMap));
                }else {
                    fList.addAll(umengMonitorMapper.getUmengAdIncomeList(paramMap));
                }
            }
        }
        // 【新增】新增banner比例调整的后台页面
        if("b.tdate,b.appkey,b.channel".equals(group)){
            String query = String.format("select IFNULL(CONCAT(tdate,max_appid,max_cha),'1') mapkey,max_adsid from dn_tuning_adconfig_info where tdate BETWEEN '%s' AND '%s' ", start_date, end_date);
            Map<String, Map<String, Object>> adMap = adService.queryListMapOfKey(query);

            fList.forEach(act -> {
                String key = act.getTdate() + act.getAppid() + act.getChannel();
                Map<String, Object> act2 = adMap.get(key);
                if(act2 != null){
                    String[] split = act2.getOrDefault("max_adsid", "").toString().split("_");
                    if(split.length == 4 && split[1].equals("banner")){
                        act.setBanner_show_rate("90");
                        act.setNew_banner_show_rate("10");
                    }else{
                        act.setBanner_show_rate("10");
                        act.setNew_banner_show_rate("90");
                    }
                }
                act.setSum_pv_banner(act.getSum_pv_banner()+"%");
                act.setSum_pv_new_banner(act.getSum_pv_new_banner()+"%");
            });
        }
        // 计算指定字段的同步环比，自统计gap值
        try {
            compareFieldsDataRateV2(dimension,paramMap, fList);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        // 临时功能名称Map查询
        Map<String, Map<String, Object>> tempNameMap = umengMonitorMapper.getTempNameForSingleid();

        //查询是否是x模式
        List<UmengAdIncomeReportVo> isXList=appService.isXList(paramMap);
        Map<String,UmengAdIncomeReportVo> xMap = new HashMap<>();
        isXList.forEach(t->{
            String key = t.getTdate()+"_"+t.getAppid()+"_"+t.getChannel();
            xMap.put(key,t);
        });
        //需要手动添加百分号的字段
        String addPercentageFields = "all_total_ctr,banner_ctr,plaque_ctr,plaque_ctr,splash_ctr,video_ctr,native_banner_ctr,native_msg_ctr,native_plaque_ctr,native_splash_ctr,native_new_banner_ctr,native_new_plaque_ctr,plaque_video_ctr,suspend_icon_ctr,all_total_fill,total_system_splash_fill,total_splash_fill,total_native_splash_fill,total_plaque_fill,total_native_new_plaque_fill,total_plaque_video_fill,total_banner_fill,total_native_new_banner_fill,total_video_fill,total_native_msg_fill,total_suspend_icon_fill,show_splash_ad_active_cnt,show_plaque_ad_active_cnt,show_banner_ad_active_cnt,show_video_ad_active_cnt,show_msg_ad_active_cnt,show_icon_ad_active_cnt,click_splash_ad_active_cnt,click_plaque_ad_active_cnt,click_banner_ad_active_cnt,click_video_ad_active_cnt,click_msg_ad_active_cnt,click_icon_ad_active_cnt,show_total_ad_active_cnt,click_total_ad_active_cnt";
        // 获取产品自定义分组权限控制数据
        List<Map<String, Object>> partnerList = platformDataService.selectAppPartnerList(null);
        Map<String, Date> appidTagMap = buildAppidTagMap(partnerList);
        fList.forEach(t->{
            String tdate = t.getTdate();
            //处理日期格式
            if (!StringUtils.isEmpty(group) && group.contains("tdate")) {
                Date date = DateUtils.to_yyyy_MM_dd(tdate);
                String week = DateUtil.dateToWeek(date);
                t.setTdate(tdate+"("+week+")");
            }else if (!StringUtils.isEmpty(group) && group.contains("week")) {
                String[] split = tdate.split("-");
                if (split.length >= 2) {
                    int year = Integer.parseInt(split[0]);
                    int week = Integer.parseInt(split[1]);
                    String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week);
                    String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week);
                    t.setTdate(year+"年第"+week+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                }
            }else if (!StringUtils.isEmpty(group) && group.contains("beek")) {
                String[] split = tdate.split("-");
                if (split.length >= 3) {
                    int year = Integer.parseInt(split[0]);
                    int week1 = Integer.parseInt(split[1]);
                    int week2 = Integer.parseInt(split[2]);
                    String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week1);
                    String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week2);
                    t.setTdate(year+"年第"+week1+"-"+week2+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                }
            }
            //处理avgnum
            String avgnum = t.getAvgnum();
            t.setAvgnum(strToTwoPercent(avgnum));
            //处理在线时长
            String dailyDuration = t.getDaily_duration();
            t.setDaily_duration(StringUtils.secondsToHHmmss(dailyDuration));
            //对指定字段添加百分号
            DataTransUtils.addDataPercentage(t,addPercentageFields);
            //处理是否x模式
            String xappid=t.getAppid();
            String xkey = tdate+"_"+xappid+"_"+t.getChannel();
            UmengAdIncomeReportVo x_vo = xMap.get(xkey);
            if(null!=x_vo){
                t.setIs_x(x_vo.getIs_x());
            }else{
                t.setIs_x("0");
            }
            // 筛选符合条件的产品自定义分组
            filterQualifiedAppidTag(t,paramMap.get("appid_tag_name"),appidTagMap);

            //功能标识条件搜索需展示搜索的功能名称(单模块)
            if (!StringUtils.isEmpty(paramMap.get("temp_id"))) {
                try {
                    String[] tempIds = t.getTemp_id().split("\\|");
                    String[] tempNames = t.getTemp_name().split("\\|");
                    for (int i = 0; i<tempIds.length;i++) {
                        if (tempIds[i].contains(paramMap.get("temp_id"))) {
                            t.setTemp_single_name(tempNames[i]);
                        }

                        Map<String, Object> tempObject = tempNameMap.get(paramMap.get("temp_id"));
                        if(tempObject != null){
                            t.setTemp_name(tempObject.get("tempStr").toString());
                        }
                    }
                }catch (Exception e) {
                    logger.error("功能名称(单模块)匹配失败:id={},name={}",t.getTemp_id(),t.getTemp_name());
                }
            }
        });

        //根据前端筛选条件 是否是x模式进行过滤
        if(!StringUtils.isBlank(is_x)){
            if("1".equals(is_x)){
                fList=fList.stream().filter(f ->"1".equals(f.getIs_x())).collect(Collectors.toList());
            }else{
                fList=fList.stream().filter(f -> !"1".equals(f.getIs_x())).collect(Collectors.toList());
            }
        }
        return fList;
    }

    private void filterQualifiedAppidTag(UmengAdIncomeReportVo reportVo, String queryAppidTags, Map<String, Date> appidTagMap) {
        String appidTags = reportVo.getAppid_tag();
        if (StringUtils.isEmpty(appidTags)) {
            return;
        }
        String latestAppidTag = null;
        Date latestDate = null;
        List<String> reportAppidTags = Arrays.asList(appidTags.split(","));
        if (StringUtils.isEmpty(queryAppidTags)) {
            //拿取数据中一条最新的产品自定义分组
            for (String appidTag : reportAppidTags) {
                Date currentDate  = appidTagMap.get(appidTag);
                if (currentDate != null && (latestDate == null || currentDate.after(latestDate))) {
                    latestDate = currentDate;
                    latestAppidTag = appidTag;
                }
            }
        } else {
            //根据查询条件和数据中的产品自定义分组获取查询条件中最新的一条
            List<String> collect = Arrays.stream(queryAppidTags.split(",")).filter(reportAppidTags::contains).collect(Collectors.toList());
            //拿取数据中一条最新的产品自定义分组
            for (String appidTag : collect) {
                Date currentDate  = appidTagMap.get(appidTag);
                if (currentDate != null && (latestDate == null || currentDate.after(latestDate))) {
                    latestDate = currentDate;
                    latestAppidTag = appidTag;
                }
            }
        }
        //封装最新的符合条件的产品自定义分组数据
        reportVo.setAppid_tag(latestAppidTag);
    }

    private Map<String, Date> buildAppidTagMap(List<Map<String, Object>> partnerList) {
        Map<String, Date> appidTagMap = new HashMap<>();
        for (Map<String, Object> partnerMap : partnerList) {
            String appidTag = partnerMap.get("appid_tag") + "";
            String createtime = partnerMap.get("createtime") + "";
            if (BlankUtils.isBlank(appidTag) || BlankUtils.isBlank(createtime)) {
                continue;
            }
            Date currentDate = DateUtil.strToDate(createtime);
            Date existingDate = appidTagMap.get(appidTag);
            if (existingDate == null || currentDate.after(existingDate)) {
                appidTagMap.put(appidTag,currentDate);
            }
        }
        return appidTagMap;
    }

    /**
     * 对指定数据进行公式计算（同比/环比/gap率/占比）
     * v2对比v1多了gap率和占比，且公式支持多选，v1只能单选
     * @param dimension 维度，支持多选，使用逗号隔开
     * @param paramMap  查询条件
     * @param fList     需要计算的数据
     */
    private void compareFieldsDataRateV2(String dimension, Map<String, String> paramMap, List<UmengAdIncomeReportVo> fList) throws NoSuchFieldException, IllegalAccessException {
        //维度为空或者数据为空，不进行数据公式计算
        if (StringUtils.isEmpty(dimension) || CollectionUtils.isEmpty(fList)) {
            return;
        }
        //分组group
        String group = paramMap.get("group");
        //自定义时间段
        String customDate = paramMap.get("custom_date");

        if (StringUtils.isEmpty(customDate) && (StringUtils.isEmpty(group) || (!group.contains("tdate") && !group.contains("week") && !group.contains("beek") && !group.contains("month")))) {
            //说明当前无日期维度数据
            return;
        }
        //数据来源 source： 1-媒体，2-自统计
        String source = paramMap.get("source");
        //获取比较维度
        String keySet = getGroupKeySet(group);
        //对处理的数据进行分组操作
        Map<String, List<UmengAdIncomeReportVo>> sourceMap = fList.stream().collect(Collectors.groupingBy(UmengAdIncomeReportVo::getSource));
        //如果是自定义多时间段查询，只有时间段之间进行比较
        if (!StringUtils.isEmpty(customDate)) {
            compareCustomDateRate(paramMap,sourceMap,keySet);
            return;
        }
        //计算方式：环比/同比/gap率/占比：dimension: yesterday-环比，dimension: week-同比，gap-gap率，占比-ratio
        String[] calculationMethods = dimension.split(",");
        for (String method : calculationMethods) {
            if ("ratio".equals(method)) {
                //占比计算
                CommonCompareUtils.ratioCompare(fList,RATIO_RATE_FIELDS);
            } else if ("gap".equals(method)) {
                //gap率
                calculateGapRate(keySet,sourceMap,paramMap);
            } else if ("yesterday".equals(method)) {
                //当所选维度 keySet 为空时，keyList 集合需要保证为空集合
                List<String> keyList = StringUtils.isEmpty(keySet) ? Lists.newArrayList() : Arrays.asList(keySet.split(","));
                if (sourceMap.containsKey(MEDIA_SOURCE)) {
                    //环比
                    List<String> tdateList = fList.stream().map(UmengAdIncomeReportVo::getTdate).distinct().sorted(Comparator.naturalOrder()).collect(Collectors.toList());
                    //根据周/月 获取对应的起始结束时间
                    String startDate = tdateList.get(0);
                    String endDate = tdateList.get(tdateList.size() - 1);
                    if (group.contains("tdate")) {
                        String start_date = paramMap.get("start_date");
                        String end_date = paramMap.get("end_date");
                        startDate = DateTime.parse(start_date,DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(1).toString("yyyy-MM-dd");
                        endDate = DateTime.parse(end_date,DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(1).toString("yyyy-MM-dd");
                    }else if (group.contains("week")) {
                        //week: 2024-44
                        startDate = WeekDateUtils.getWeekStartDate(WeekDateUtils.plusDate(startDate,-1,"week"));
                        endDate = WeekDateUtils.getWeekEndDate(WeekDateUtils.plusDate(endDate,-1,"week"));
                    } else if (group.contains("beek")) {
                        //beek: "2024-43-44", 周一 至 周日 为一周
                        String start = startDate.substring(0, 7);
                        String end = endDate.substring(0,5) +  endDate.substring(8,10);
                        startDate = WeekDateUtils.getWeekStartDate(WeekDateUtils.plusDate(start,-2,"week"));
                        endDate = WeekDateUtils.getWeekEndDate(WeekDateUtils.plusDate(end,-2,"week"));
                    } else {
                        //month: 2024-10
                        startDate = WeekDateUtils.getMonthStartDay(startDate,-1);
                        endDate = WeekDateUtils.getMonthEndDay(endDate,-1);
                    }
                    Map<String,String> param = new HashMap<>(paramMap);
                    param.put("start_date", startDate);
                    param.put("end_date", endDate);
                    param.put("source", DATA_SOURCE_MEDIA);
                    List<UmengAdIncomeReportVo> sList;
                    if("2".equals(param.get("dataSource"))) {
                        sList = appService.getUmengAdIncomeList(param);
                    } else {
                        sList = umengMonitorMapper.getUmengAdIncomeList(param);
                    }
                    try {
                        CommonCompareUtils.compare(sourceMap.get(MEDIA_SOURCE), sList, keyList, Arrays.asList(DEFINED_RATE_FIELDS.split(",")), UmengAdIncomeReportVo::getTdate, getFunctionByGroup(group));
                    } catch (NoSuchFieldException | IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                }
                //自统计
                if (sourceMap.containsKey(SELF_STATISTICS_SOURCE)) {
                    //自统计
                    List<UmengAdIncomeReportVo> list = sourceMap.get(SELF_STATISTICS_SOURCE);
                    List<UmengAdIncomeReportVo> sList;
                    if (sourceMap.containsKey(MEDIA_SOURCE)) {
                        sList = sourceMap.get(MEDIA_SOURCE);
                    } else {
                        Map<String,String> param = new HashMap<>(paramMap);
                        param.put("source", DATA_SOURCE_MEDIA);
                        if("2".equals(param.get("dataSource"))) {
                            sList = appService.getUmengAdIncomeList(param);
                        }else {
                            sList = umengMonitorMapper.getUmengAdIncomeList(param);
                        }
                    }
                    GapCompareUtils.compare(list,sList,keyList,Arrays.asList(DEFINED_RATE_FIELDS.split(",")),UmengAdIncomeReportVo::getTdate,UmengAdIncomeReportVo::getTdate);
                }
            } else if ("week".equals(method)){
                //同比
                if (sourceMap.containsKey(MEDIA_SOURCE)) {
                    Map<String,String> param = new HashMap<>(paramMap);
                    //根据查询条件获取需要进行数据比较的集合
                    String start_date = param.get("start_date");
                    String end_date = param.get("end_date");
                    String startDate = DateTime.parse(start_date, DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(7).toString("yyyy-MM-dd");
                    String endDate = DateTime.parse(end_date,DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(7).toString("yyyy-MM-dd");
                    param.put("start_date", startDate);
                    param.put("end_date", endDate);
                    param.put("source", DATA_SOURCE_MEDIA);
                    List<UmengAdIncomeReportVo> sList;
                    if("2".equals(param.get("dataSource"))) {
                        sList = appService.getUmengAdIncomeList(param);
                    }else {
                        sList = umengMonitorMapper.getUmengAdIncomeList(param);
                    }
                    //媒体
                    CommonCompareUtils.compare(sourceMap.get(MEDIA_SOURCE), sList, keySet, DEFINED_RATE_FIELDS, UmengAdIncomeReportVo::getTdate, 7);
                }
                if (sourceMap.containsKey(SELF_STATISTICS_SOURCE)) {
                    List<String> keyList = StringUtils.isEmpty(keySet) ? Lists.newArrayList() : Arrays.asList(keySet.split(","));
                    //自统计
                    List<UmengAdIncomeReportVo> list = sourceMap.get(SELF_STATISTICS_SOURCE);
                    List<UmengAdIncomeReportVo> mediaList;
                    if (sourceMap.containsKey(MEDIA_SOURCE)) {
                        mediaList = sourceMap.get(MEDIA_SOURCE);
                    } else {
                        Map<String, String> param = new HashMap<>(paramMap);
                        param.put("source",DATA_SOURCE_MEDIA);
                        if("2".equals(param.get("dataSource"))) {
                            mediaList = appService.getUmengAdIncomeList(param);
                        }else {
                            mediaList = umengMonitorMapper.getUmengAdIncomeList(param);
                        }
                    }
                    GapCompareUtils.compare(list,mediaList,keyList,Arrays.asList(DEFINED_RATE_FIELDS.split(",")),UmengAdIncomeReportVo::getTdate,UmengAdIncomeReportVo::getTdate);
                }
            } else if ("show".equals(method)) {
                //展示渗透率
                for (UmengAdIncomeReportVo reportVo : fList) {
                    reportVo.setAll_total_pv_rate(appendValues(reportVo.getAll_total_pv_rate(),reportVo.getShow_total_ad_active_cnt()));
                    reportVo.setTotal_splash_pv_rate(appendValues(reportVo.getTotal_splash_pv_rate(),reportVo.getShow_splash_ad_active_cnt()));
                    reportVo.setTotal_plaque_pv_rate(appendValues(reportVo.getTotal_plaque_pv_rate(),reportVo.getShow_plaque_ad_active_cnt()));
                    reportVo.setTotal_banner_pv_rate(appendValues(reportVo.getTotal_banner_pv_rate(),reportVo.getShow_banner_ad_active_cnt()));
                    reportVo.setTotal_video_pv_rate(appendValues(reportVo.getTotal_video_pv_rate(),reportVo.getShow_video_ad_active_cnt()));
                    reportVo.setTotal_native_msg_pv_rate(appendValues(reportVo.getTotal_native_msg_pv_rate(),reportVo.getShow_msg_ad_active_cnt()));
                    reportVo.setTotal_suspend_icon_pv_rate(appendValues(reportVo.getTotal_suspend_icon_pv_rate(),reportVo.getShow_icon_ad_active_cnt()));
                    reportVo.setTotal_plaque_video_pv_rate(appendValues(reportVo.getTotal_plaque_video_pv_rate(),null));
                }
            } else if ("click".equals(method)) {
                //点击渗透率
                for (UmengAdIncomeReportVo reportVo : fList) {
                    reportVo.setAll_total_click_rate(appendValues(reportVo.getAll_total_click_rate(),reportVo.getClick_total_ad_active_cnt()));
                    reportVo.setTotal_splash_click_rate(appendValues(reportVo.getTotal_splash_click_rate(),reportVo.getClick_splash_ad_active_cnt()));
                    reportVo.setTotal_plaque_click_rate(appendValues(reportVo.getTotal_plaque_click_rate(),reportVo.getClick_plaque_ad_active_cnt()));
                    reportVo.setTotal_banner_click_rate(appendValues(reportVo.getTotal_banner_click_rate(),reportVo.getClick_banner_ad_active_cnt()));
                    reportVo.setTotal_video_click_rate(appendValues(reportVo.getTotal_video_click_rate(),reportVo.getClick_video_ad_active_cnt()));
                    reportVo.setTotal_native_msg_click_rate(appendValues(reportVo.getTotal_native_msg_click_rate(),reportVo.getClick_msg_ad_active_cnt()));
                    reportVo.setTotal_suspend_icon_click_rate(appendValues(reportVo.getTotal_suspend_icon_click_rate(),reportVo.getClick_icon_ad_active_cnt()));
                    reportVo.setTotal_plaque_video_click_rate(appendValues(reportVo.getTotal_plaque_video_click_rate(),null));
                }
            }
        }
    }

    private String appendValues(String value, String appendValue) {
        String handleValue = StringUtils.isEmpty(appendValue) ? "0.00%" : appendValue + "%";
        return StringUtils.isEmpty(value) ? handleValue : value + "/" + handleValue;
    }

    private void calculateGapRate(String keySet,Map<String, List<UmengAdIncomeReportVo>> sourceMap, Map<String,String> paramMap) throws NoSuchFieldException, IllegalAccessException {
        List<String> keyList = StringUtils.isEmpty(keySet) ? Lists.newArrayList() : Arrays.asList(keySet.split(","));
        List<String> fieldList = Arrays.asList(GAP_RATE_FIELDS.split(","));
        if (sourceMap.containsKey(MEDIA_SOURCE)) {
            List<UmengAdIncomeReportVo> gapSelf;
            if (sourceMap.containsKey(SELF_STATISTICS_SOURCE)) {
                gapSelf = sourceMap.get(SELF_STATISTICS_SOURCE);
            } else {
                Map<String, String> param = new HashMap<>(paramMap);
                param.put("source",DATA_SOURCE_SELF);
                if("2".equals(param.get("dataSource"))) {
                    gapSelf = appService.getUmengAdIncomeList(param);
                }else {
                    gapSelf = umengMonitorMapper.getUmengAdIncomeList(param);
                }
            }
            GapCompareUtils.gapCompare(sourceMap.get(MEDIA_SOURCE),gapSelf,keyList,fieldList,UmengAdIncomeReportVo::getTdate,UmengAdIncomeReportVo::getTdate,true);
        }
        if (sourceMap.containsKey(SELF_STATISTICS_SOURCE)) {
            //自统计
            List<UmengAdIncomeReportVo> gapMedia;
            if (sourceMap.containsKey(MEDIA_SOURCE)) {
                gapMedia = sourceMap.get(MEDIA_SOURCE);
            } else {
                paramMap.put("source",DATA_SOURCE_MEDIA);
                if("2".equals(paramMap.get("dataSource"))) {
                    gapMedia = appService.getUmengAdIncomeList(paramMap);
                }else {
                    gapMedia = umengMonitorMapper.getUmengAdIncomeList(paramMap);
                }
            }
            GapCompareUtils.gapCompare(sourceMap.get(SELF_STATISTICS_SOURCE),gapMedia,keyList,fieldList,UmengAdIncomeReportVo::getTdate,UmengAdIncomeReportVo::getTdate,false);
        }
    }

    /**
     * 根据分组获取比较的函数公式
     * @param group 分组维度
     * @return 函数公式
     */
    private static Function<UmengAdIncomeReportVo, String> getFunctionByGroup(String group) {
        return reportVo -> {
            if (group.contains("tdate")) {
                return WeekDateUtils.plusDate(reportVo.getTdate(),-1,"tdate");
            } else if (group.contains("week")) {
                //week: 2024-44
                return WeekDateUtils.plusDate(reportVo.getTdate(),-1,"week");
            } else if (group.contains("beek")) {
                //beek: "2024-43-44"
                String yearWeek = reportVo.getTdate().substring(0, 7);
                String previousWeek = WeekDateUtils.plusDate(yearWeek,-2,"week");
                String lasWeek = WeekDateUtils.plusDate(yearWeek,-1,"week").substring(5,7);
                return previousWeek + "-" + lasWeek;
            } else {
                //month：2024-10
                return WeekDateUtils.plusDate(reportVo.getTdate(),-1,"month");
            }
        };
    }


    /**
     * 获取数据比较的维度
     * @param group 分组维度
     * @return 比较维度
     */
    private static String getGroupKeySet(String group) {
        String keySet = "appkey,channel";
        if (StringUtils.isEmpty(group)) {
            keySet = "";
        } else if (!group.contains("channel") && !group.contains("appkey")) {
            keySet = "";
        } else if (!group.contains("channel")) {
            keySet = "appkey";
        } else if (!group.contains("appkey")) {
            keySet = "channel";
        }
        return keySet;
    }

    /**
     * 自定义多时间段数据比值比较
     * @param paramMap 初始查询条件
     * @param sourceMap 自定义时间段数据
     * @param keySet 维度
     */
    private void compareCustomDateRate(Map<String, String> paramMap, Map<String, List<UmengAdIncomeReportVo>> sourceMap, String keySet) {
        // 自定义时间段计算环比操作
        if (sourceMap.containsKey(MEDIA_SOURCE)) {
            //媒体数据，数据之间进行比较
            Map<String, List<UmengAdIncomeReportVo>> flistMap = sourceMap.get(MEDIA_SOURCE).stream().collect(Collectors.groupingBy(UmengAdIncomeReportVo::getTdate));
            //对 时间段进行倒序排序操作
            List<String> sortTdateList = flistMap.keySet().stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(sortTdateList) && sortTdateList.size() >= 2) {
                //只有存在两个时间段以上才进行比较对应的环比操作，时间大的比较时间小的
                for (int i = 0; i < sortTdateList.size() - 1; i++) {
                    List<UmengAdIncomeReportVo> list = flistMap.get(sortTdateList.get(i));
                    List<UmengAdIncomeReportVo> sList = flistMap.get(sortTdateList.get(i+1));
                    try {
                        List<String> keyList = StringUtils.isEmpty(keySet) ? Lists.newArrayList() : Arrays.asList(keySet.split(","));
                        CommonCompareUtils.compare(list, sList, keyList, Arrays.asList(DEFINED_RATE_FIELDS.split(",")), data -> "custom", data -> "custom");
                    } catch (NoSuchFieldException | IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
        //自统计需要有gap计算和渗透率计算，使用不同的字段
        if (sourceMap.containsKey(SELF_STATISTICS_SOURCE)) {
            //自统计数据
            List<UmengAdIncomeReportVo> selfList = sourceMap.get(SELF_STATISTICS_SOURCE);
            // 计算自统计的gap值 公式: gap = (自统计-渠道)/自统计
            //渠道数据获取
            List<UmengAdIncomeReportVo> mediaList = new ArrayList<>();
            if (sourceMap.containsKey(MEDIA_SOURCE)) {
                mediaList = sourceMap.getOrDefault(MEDIA_SOURCE,Collections.emptyList());
            } else {
                //自定义时间段
                String customDate = paramMap.get("custom_date");
                //封装计算gap值的渠道数据的查询参数
                paramMap.put("source",DATA_SOURCE_MEDIA);
                boolean oppoDataSourceFlag = "2".equals(paramMap.get("dataSource"));
                //为自定义时间段 格式： 2024-10-10,2024-10-15;2024-11-03,2024-11-05
                String[] split = customDate.split(";");
                for (String data : split) {
                    String[] dateSplit = data.split(",");
                    paramMap.put("start_date",dateSplit[0]);
                    paramMap.put("end_date",dateSplit[1]);
                    if(oppoDataSourceFlag) {
                        mediaList.addAll(appService.getUmengAdIncomeList(paramMap));
                    }else {
                        mediaList.addAll(umengMonitorMapper.getUmengAdIncomeList(paramMap));
                    }
                }
            }
            try {
                //自统计gap计算
                List<String> keyList = StringUtils.isEmpty(keySet) ? Lists.newArrayList() : Arrays.asList(keySet.split(","));
                GapCompareUtils.compare(selfList,mediaList,keyList,Arrays.asList(DEFINED_RATE_FIELDS.split(",")),UmengAdIncomeReportVo::getTdate,UmengAdIncomeReportVo::getTdate);
                //渗透率计算
            }catch (NoSuchFieldException | IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 计算指定字段的同比环比，自统计gap值
     * @param dimension
     * @param paramMap
     * @param fList
     */
    private void compareFieldsDataRate(String dimension, Map<String, String> paramMap, List<UmengAdIncomeReportVo> fList) {
        if (CollectionUtils.isEmpty(fList)) return;
        //分组group
        String group = paramMap.get("group");
        //自定义时间段
        String customDate = paramMap.get("custom_date");
        //数据来源 source： 1-媒体，2-自统计
        String source = paramMap.get("source");
        final String DATA_SOURCE_MEDIA = "1";
        final String DATA_SOURCE_SELF = "2";

        if (StringUtils.isEmpty(customDate) && (StringUtils.isEmpty(group) ||
                (!group.contains("tdate") && !group.contains("week")
                && !group.contains("beek") && !group.contains("month")))) {
            //说明当前无日期维度数据
            return;
        }
        //维度
        String keySet = "appkey,channel";
        if (!group.contains("channel") && !group.contains("appkey")) {
            keySet = "";
        } else if (!group.contains("channel")) {
            keySet = "appkey";
        } else if (!group.contains("appkey")) {
            keySet = "channel";
        }
        boolean oppoDataSourceFlag = "2".equals(paramMap.get("dataSource"));
        String gapKeySet = keySet;
        //keySet = oppoDataSourceFlag ? keySet : StringUtils.isEmpty(keySet) ? "source" : keySet + ",source";
        keySet = StringUtils.isEmpty(keySet) ? "source" : keySet + ",source";
        String start_date = paramMap.get("start_date");
        String end_date = paramMap.get("end_date");
        //数据来源是否包含 媒体
        boolean mediaDataSourceFlag = StringUtils.isEmpty(source) || DATA_SOURCE_MEDIA.equals(source);
        if (mediaDataSourceFlag && group.contains("tdate") ) {
            List<UmengAdIncomeReportVo> sList;
            //按日维度数据计算同比环比
            paramMap.put("source", DATA_SOURCE_MEDIA);
            if ("week".equals(dimension)) {
                int lastDay = 7;
                String startDate = DateTime.parse(start_date, DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(lastDay).toString("yyyy-MM-dd");
                String endDate = DateTime.parse(end_date,DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(lastDay).toString("yyyy-MM-dd");
                paramMap.put("start_date", startDate);
                paramMap.put("end_date", endDate);
                if(oppoDataSourceFlag) {
                    sList = appService.getUmengAdIncomeList(paramMap);
                }else {
                    sList = umengMonitorMapper.getUmengAdIncomeList(paramMap);
                }
                try {
                    CommonCompareUtils.compare(fList, sList, keySet,
                            DEFINED_RATE_FIELDS, UmengAdIncomeReportVo::getTdate, lastDay);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            } else if ("yesterday".equals(dimension)) {
                int lastDay = 1;
                String startDate = DateTime.parse(start_date,DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(lastDay).toString("yyyy-MM-dd");
                String endDate = DateTime.parse(end_date,DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(lastDay).toString("yyyy-MM-dd");
                paramMap.put("start_date", startDate);
                paramMap.put("end_date", endDate);

                if(oppoDataSourceFlag) {
                    sList = appService.getUmengAdIncomeList(paramMap);
                }else {
                    sList = umengMonitorMapper.getUmengAdIncomeList(paramMap);
                }
                try {
                    CommonCompareUtils.compare(fList, sList, keySet,
                            DEFINED_RATE_FIELDS, UmengAdIncomeReportVo::getTdate, lastDay);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        //获取查询数据去重后的 日期数据
        // 按周，双周，月 计算环比操作，无同比
        if (mediaDataSourceFlag && (group.contains("week") || group.contains("beek") || group.contains("month"))) {
            List<String> tdateList = fList.stream().map(UmengAdIncomeReportVo::getTdate).distinct().sorted(Comparator.naturalOrder()).collect(Collectors.toList());
            //根据周/月 获取对应的起始结束时间
            String startDate = tdateList.get(0);
            String endDate = tdateList.get(tdateList.size() - 1);
            if (group.contains("week")) {
                //week: 2024-44
                startDate = WeekDateUtils.getWeekStartDate(WeekDateUtils.plusDate(startDate,-1,"week"));
                endDate = WeekDateUtils.getWeekEndDate(WeekDateUtils.plusDate(endDate,-1,"week"));
            } else if (group.contains("beek")) {
                //beek: "2024-43-44", 周一 至 周日 为一周
                String start = startDate.substring(0, 7);
                String end = endDate.substring(0,5) +  endDate.substring(8,10);
                startDate = WeekDateUtils.getWeekStartDate(WeekDateUtils.plusDate(start,-2,"week"));
                endDate = WeekDateUtils.getWeekEndDate(WeekDateUtils.plusDate(end,-2,"week"));
            } else {
                //month: 2024-10
                startDate = WeekDateUtils.getMonthStartDay(startDate,-1);
                endDate = WeekDateUtils.getMonthEndDay(endDate,-1);
            }
            paramMap.put("start_date", startDate);
            paramMap.put("end_date", endDate);
            paramMap.put("source", DATA_SOURCE_MEDIA);

            List<UmengAdIncomeReportVo> sList;
            if(oppoDataSourceFlag) {
                sList = appService.getUmengAdIncomeList(paramMap);
            } else {
                sList = umengMonitorMapper.getUmengAdIncomeList(paramMap);
            }
            Function<UmengAdIncomeReportVo, String> function = reportVo -> {
                if (group.contains("week")) {
                    //week: 2024-44
                    return WeekDateUtils.plusDate(reportVo.getTdate(),-1,"week");
                } else if (group.contains("beek")) {
                    //beek: "2024-43-44"
                    String yearWeek = reportVo.getTdate().substring(0, 7);
                    String previousWeek = WeekDateUtils.plusDate(yearWeek,-2,"week");
                    String lasWeek = WeekDateUtils.plusDate(yearWeek,-1,"week").substring(5,7);
                    return previousWeek + "-" + lasWeek;
                } else {
                    //month：2024-10
                    return WeekDateUtils.plusDate(reportVo.getTdate(),-1,"month");
                }
            };
            try {
                //当所选维度 keySet 为空时，keyList 集合需要保证为空集合
                List<String> keyList = StringUtils.isEmpty(keySet) ? Lists.newArrayList() : Arrays.asList(keySet.split(","));
                CommonCompareUtils.compare(fList, sList, keyList, Arrays.asList(DEFINED_RATE_FIELDS.split(",")), UmengAdIncomeReportVo::getTdate, function);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
        //自定义时间计算环比
        if (mediaDataSourceFlag && !StringUtils.isEmpty(customDate)) {
            // 自定义时间段计算环比操作
            //根据 tdate进行分组操作
            Map<String, List<UmengAdIncomeReportVo>> flistMap = fList.stream().collect(Collectors.groupingBy(UmengAdIncomeReportVo::getTdate));
            //对 时间段进行倒序排序操作
            List<String> sortTdateList = flistMap.keySet().stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(sortTdateList) && sortTdateList.size() >= 2) {
                //只有存在两个时间段以上才进行比较对应的环比操作，时间大的比较时间小的
                for (int i = 0; i < sortTdateList.size() - 1; i++) {
                    List<UmengAdIncomeReportVo> list = flistMap.get(sortTdateList.get(i)).stream().filter(data -> "媒体".equals(data.getSource())).collect(Collectors.toList());
                    List<UmengAdIncomeReportVo> sList = flistMap.get(sortTdateList.get(i+1)).stream().filter(data -> "媒体".equals(data.getSource())).collect(Collectors.toList());
                    try {
                        List<String> keyList = StringUtils.isEmpty(keySet) ? Lists.newArrayList() : Arrays.asList(keySet.split(","));
                        CommonCompareUtils.compare(list, sList, keyList, Arrays.asList(DEFINED_RATE_FIELDS.split(",")), data -> "custom", data -> "custom");
                    } catch (NoSuchFieldException | IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
        //下面为自统计gap值计算
        if (!DATA_SOURCE_MEDIA.equals(source)) {
            //计算自统计rate
            // 计算自统计的gap值 公式: gap = (自统计-渠道)/自统计
            Map<String,List<UmengAdIncomeReportVo>> collect = fList.stream().collect(Collectors.groupingBy(umengAdIncomeReport-> StringUtils.isEmpty(umengAdIncomeReport.getSource()) ? "媒体" : umengAdIncomeReport.getSource()));
            //查询的数据不存在自统计，不需要进行gap值计算
            if (!collect.containsKey("自统计")) return;
            //渠道数据获取
            List<UmengAdIncomeReportVo> mediaList = new ArrayList<>();
            if (mediaDataSourceFlag) {
                mediaList = collect.getOrDefault("媒体",Collections.emptyList());
            } else {
                //封装计算gap值的渠道数据的查询参数
                paramMap.put("source",DATA_SOURCE_MEDIA);
                if (StringUtils.isEmpty(customDate)) {
                    paramMap.put("start_date",start_date);
                    paramMap.put("end_date",end_date);
                    if(oppoDataSourceFlag) {
                        mediaList = appService.getUmengAdIncomeList(paramMap);
                    } else {
                        mediaList = umengMonitorMapper.getUmengAdIncomeList(paramMap);
                    }
                } else {
                    //为自定义时间段 格式： 2024-10-10,2024-10-15;2024-11-03,2024-11-05
                    String[] split = customDate.split(";");
                    for (String data : split) {
                        String[] dateSplit = data.split(",");
                        paramMap.put("start_date",dateSplit[0]);
                        paramMap.put("end_date",dateSplit[1]);
                        if(oppoDataSourceFlag) {
                            mediaList.addAll(appService.getUmengAdIncomeList(paramMap));
                        }else {
                            mediaList.addAll(umengMonitorMapper.getUmengAdIncomeList(paramMap));
                        }
                    }
                }
            }
            //自统计数据
            List<UmengAdIncomeReportVo> selfList = collect.get("自统计");
            try {
                List<String> keyList = StringUtils.isEmpty(gapKeySet) ? Lists.newArrayList() : Arrays.asList(gapKeySet.split(","));
                GapCompareUtils.compare(selfList,mediaList,keyList,Arrays.asList(DEFINED_RATE_FIELDS.split(",")),UmengAdIncomeReportVo::getTdate,UmengAdIncomeReportVo::getTdate);
            }catch (NoSuchFieldException | IllegalAccessException e) {
                throw new RuntimeException(e);
            }
            // 渗透率比值计算
            if (DATA_SOURCE_SELF.equals(source) && group.contains("channel") && group.contains("appkey")) {
                //自定义时间计算环比
                if (!StringUtils.isEmpty(customDate)) {
                    // 自定义时间段计算环比操作
                    //根据 tdate进行分组操作
                    Map<String, List<UmengAdIncomeReportVo>> flistMap = fList.stream().collect(Collectors.groupingBy(UmengAdIncomeReportVo::getTdate));
                    //对 时间段进行倒序排序操作
                    List<String> sortTdateList = flistMap.keySet().stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(sortTdateList) && sortTdateList.size() >= 2) {
                        //只有存在两个时间段以上才进行比较对应的环比操作，时间大的比较时间小的
                        for (int i = 0; i < sortTdateList.size() - 1; i++) {
                            List<UmengAdIncomeReportVo> list = flistMap.get(sortTdateList.get(i));
                            List<UmengAdIncomeReportVo> sList = flistMap.get(sortTdateList.get(i+1));
                            try {
                                List<String> keyList = StringUtils.isEmpty(keySet) ? Lists.newArrayList() : Arrays.asList(keySet.split(","));
                                CommonCompareUtils.compare(list, sList, keyList, Arrays.asList(SEEK_RATE_FIELDS.split(",")), data -> "custom", data -> "custom");
                            } catch (NoSuchFieldException | IllegalAccessException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }
                } else if (group.contains("week") || group.contains("beek") || group.contains("month")) {
                    List<String> tdateList = fList.stream().map(UmengAdIncomeReportVo::getTdate).distinct().sorted(Comparator.naturalOrder()).collect(Collectors.toList());
                    //根据周/月 获取对应的起始结束时间
                    String startDate = tdateList.get(0);
                    String endDate = tdateList.get(tdateList.size() - 1);
                    if (group.contains("week")) {
                        //week: 2024-44
                        startDate = WeekDateUtils.getWeekStartDate(WeekDateUtils.plusDate(startDate,-1,"week"));
                        endDate = WeekDateUtils.getWeekEndDate(WeekDateUtils.plusDate(endDate,-1,"week"));
                    } else if (group.contains("beek")) {
                        //beek: "2024-43-44", 周一 至 周日 为一周
                        String start = startDate.substring(0, 7);
                        String end = endDate.substring(0,5) +  endDate.substring(8,10);
                        startDate = WeekDateUtils.getWeekStartDate(WeekDateUtils.plusDate(start,-2,"week"));
                        endDate = WeekDateUtils.getWeekEndDate(WeekDateUtils.plusDate(end,-2,"week"));
                    } else {
                        //month: 2024-10
                        startDate = WeekDateUtils.getMonthStartDay(startDate,-1);
                        endDate = WeekDateUtils.getMonthEndDay(endDate,-1);
                    }
                    paramMap.put("start_date", startDate);
                    paramMap.put("end_date", endDate);
                    paramMap.put("source", DATA_SOURCE_SELF);

                    List<UmengAdIncomeReportVo> sList;
                    if(oppoDataSourceFlag) {
                        sList = appService.getUmengAdIncomeList(paramMap);
                    } else {
                        sList = umengMonitorMapper.getUmengAdIncomeList(paramMap);
                    }
                    Function<UmengAdIncomeReportVo, String> function = reportVo -> {
                        if (group.contains("week")) {
                            //week: 2024-44
                            return WeekDateUtils.plusDate(reportVo.getTdate(),-1,"week");
                        } else if (group.contains("beek")) {
                            //beek: "2024-43-44"
                            String yearWeek = reportVo.getTdate().substring(0, 7);
                            String previousWeek = WeekDateUtils.plusDate(yearWeek,-2,"week");
                            String lasWeek = WeekDateUtils.plusDate(yearWeek,-1,"week").substring(5,7);
                            return previousWeek + "-" + lasWeek;
                        } else {
                            //month：2024-10
                            return WeekDateUtils.plusDate(reportVo.getTdate(),-1,"month");
                        }
                    };
                    try {
                        //当所选维度 keySet 为空时，keyList 集合需要保证为空集合
                        List<String> keyList = StringUtils.isEmpty(keySet) ? Lists.newArrayList() : Arrays.asList(keySet.split(","));
                        CommonCompareUtils.compare(fList, sList, keyList, Arrays.asList(SEEK_RATE_FIELDS.split(",")), UmengAdIncomeReportVo::getTdate, function);
                    } catch (NoSuchFieldException | IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                } else {
                    if ("week".equals(dimension)) {
                        int lastDay = 7;
                        String startDate = DateTime.parse(start_date, DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(lastDay).toString("yyyy-MM-dd");
                        String endDate = DateTime.parse(end_date,DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(lastDay).toString("yyyy-MM-dd");
                        paramMap.put("start_date", startDate);
                        paramMap.put("end_date", endDate);
                        paramMap.put("source",DATA_SOURCE_SELF);
                        List<UmengAdIncomeReportVo> sList;
                        if(oppoDataSourceFlag) {
                            sList = appService.getUmengAdIncomeList(paramMap);
                        }else {
                            sList = umengMonitorMapper.getUmengAdIncomeList(paramMap);
                        }
                        try {
                            CommonCompareUtils.compare(fList, sList, keySet,
                                    SEEK_RATE_FIELDS, UmengAdIncomeReportVo::getTdate, lastDay);
                        } catch (NoSuchFieldException | IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                    } else if ("yesterday".equals(dimension)) {
                        int lastDay = 1;
                        String startDate = DateTime.parse(start_date,DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(lastDay).toString("yyyy-MM-dd");
                        String endDate = DateTime.parse(end_date,DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(lastDay).toString("yyyy-MM-dd");
                        paramMap.put("start_date", startDate);
                        paramMap.put("end_date", endDate);
                        paramMap.put("source",DATA_SOURCE_SELF);
                        List<UmengAdIncomeReportVo> sList;
                        if(oppoDataSourceFlag) {
                            sList = appService.getUmengAdIncomeList(paramMap);
                        }else {
                            sList = umengMonitorMapper.getUmengAdIncomeList(paramMap);
                        }
                        try {
                            CommonCompareUtils.compare(fList, sList, keySet,
                                    SEEK_RATE_FIELDS, UmengAdIncomeReportVo::getTdate, lastDay);
                        } catch (NoSuchFieldException | IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            }
        }
    }

    private String getTempName(String tempId){
        if(StringUtils.isEmpty(tempId)){
            return "";
        }
        String[] tempIdArray = tempId.split("\\|");
        StringBuilder resultTempName = new StringBuilder();
        Arrays.stream(tempIdArray).forEach(data -> {
            Map<String, Object> resultDateChannel = advSDKService.selectSDKDateChannel("'" + data.trim() + "'");
            String date = String.valueOf(resultDateChannel.get("date"));
            String channel = String.valueOf(resultDateChannel.get("cha"));
            String tempName = String.valueOf(resultDateChannel.get("tempName"));
            resultTempName.append(date).append("_").append(channel).append("_").append(tempName).append(" | ");
        });
        return resultTempName.substring(0, resultTempName.length() - 3);
    }

    @RequestMapping(value="export")
    public void export(ModelMap map, HttpServletRequest request, HttpServletResponse response) {
        response.setHeader("Access-Control-Allow-Origin", "*");

        String start_date = BlankUtils.checkNull(request, "start_date");
        String end_date = BlankUtils.checkNull(request, "end_date");
        String appid = request.getParameter("appid");
        String media = request.getParameter("media");
        String channel = BlankUtils.checkNull(request, "channel");
        String group = request.getParameter("group");
        String gameName = request.getParameter("gameName");
        String token = request.getParameter("token");
        String is_x = request.getParameter("is_x");
        String temp_id = request.getParameter("temp_id");
        String temp_name = request.getParameter("temp_name");
        String state = request.getParameter("state");
        String active_temp_id = request.getParameter("active_temp_id");
        String active_temp_name = request.getParameter("active_temp_name");
        String large_ver = request.getParameter("large_ver");
        //广告违规类型
        String ad_violation_type = request.getParameter("ad_violation_type");
        //获取来源：1-媒体/2-自统计
        String source = request.getParameter("source");
        // 2024-10-29：渠道产品广告数据查询新增日期汇总功能
        String customDate = request.getParameter("custom_date");
        //应用自定义分组
        String appidTag = request.getParameter("appid_tag");
        //应用自定义分组是否反选
        String appidTagRev = request.getParameter("appid_tag_rev");
        //包名
        String packagename = request.getParameter("packagename");
        String prjid_group_id = BlankUtils.checkNull(request,"prjid_group_id");

        String order_str = request.getParameter("order_str");
        // dataSource 1 友盟 2 oppo
        String dataSource = request.getParameter("dataSource");
        String tableName = "umeng_ad_income";
        if ("2".equals(dataSource)){
            tableName = "oppo_ad_income";
        }

        if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
            start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        }

        Map<String,String> paramMap = new HashMap<>();
        order_str = generateOrderStrParam(order_str);
        group = generateGroupParam(group);
        paramMap.put("tableName",tableName);
        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("order_str",order_str);
        paramMap.put("group",group);
        paramMap.put("gameName",gameName);
        paramMap.put("channel",channel);
        paramMap.put("appid",appid);
        paramMap.put("media",media);
        paramMap.put("temp_id",temp_id);
        paramMap.put("temp_name",temp_name);
        paramMap.put("state",state);
        paramMap.put("dataSource",dataSource);
        paramMap.put("source",source);
        paramMap.put("active_temp_id",active_temp_id);
        paramMap.put("active_temp_name",active_temp_name);
        paramMap.put("custom_date",customDate);
        paramMap.put("appid_tag",appidTag);
        paramMap.put("appid_tag_rev",appidTagRev);
        paramMap.put("packagename",packagename);
        paramMap.put("large_ver",large_ver);
        paramMap.put("ad_violation_type",ad_violation_type);
        /* prjid_group_id需要解析为appid+cha_id然后进行筛选 */
        if(!BlankUtils.checkBlank(prjid_group_id)) {
            Map<String,String> params = new HashMap<>();
            params.put("ctype", "2");
            params.put("id", prjid_group_id);
            List<Map<String, Object>> groupList = yyhzMapper.selectPrjidGroupConfigConfig(params);

            if(groupList != null && groupList.size() > 0) {

                String collect = groupList.stream().filter(act -> !BlankUtils.checkBlank(act.getOrDefault("prjid","").toString()))
                        .map(act -> act.getOrDefault("prjid","").toString())
                        .collect(Collectors.joining(","));
                String collect2 = groupList.stream().filter(act -> !BlankUtils.checkBlank(act.getOrDefault("chastr","").toString()))
                        .flatMap(act -> Arrays.stream((act.getOrDefault("chastr","").toString()).split(",")))
                        .map(str -> "'"+str+"'")
                        .collect(Collectors.joining(","));

                String match_str = null;
                if(!BlankUtils.checkBlank(collect)){
                    // 项目id的集合时，需要转换为appid#cha_id
                    Map<String, Map<String, Object>> prjMap = adService.selectProjectidChannelMap();
                    for (String prjid : collect.split(",")) {
                        Map<String, Object> act = prjMap.get(prjid);
                        if (null != act) {
                            String match_appid_chaid = act.get("appid") +"#"+ act.get("cha_id");

                            if(BlankUtils.checkBlank(collect2)){
                                collect2 = "'"+match_appid_chaid+"'";
                            }else{
                                collect2 += ",'"+match_appid_chaid+"'";
                            }
                        }
                    }
                }

                if(!BlankUtils.checkBlank(collect2)){
                    match_str = String.format(" concat(c.appid,'#',c.channel) in (%s) ", collect2);
                }
                paramMap.put("match_str", "("+match_str+")");
            }
        }
        List<UmengAdIncomeReportVo> list = appService.getUmengAdIncomeList(paramMap);


        // 【新增】新增banner比例调整的后台页面
        if("b.tdate,b.appkey,b.channel".equals(group)){
            String query = String.format("select IFNULL(CONCAT(tdate,max_appid,max_cha),'1') mapkey,max_adsid from dn_tuning_adconfig_info where tdate BETWEEN '%s' AND '%s' ",start_date,end_date);
            Map<String, Map<String, Object>> adMap = adService.queryListMapOfKey(query);

            list.forEach(act -> {
                String key = act.getTdate() + act.getAppid() + act.getChannel();
                Map<String, Object> act2 = adMap.get(key);
                if(act2 != null){
                    String[] split = act2.getOrDefault("max_adsid", "").toString().split("_");
                    if(split.length == 4 && split[1].equals("banner")){
                        act.setBanner_show_rate("90");
                        act.setNew_banner_show_rate("10");
                    }else{
                        act.setBanner_show_rate("10");
                        act.setNew_banner_show_rate("90");
                    }
                    // System.out.println(String.format("split==%s，banner==%s，new banner==%s",Arrays.toString(split),act.getBanner_show_rate(),act.getNew_banner_show_rate()));
                }
                act.setSum_pv_banner(act.getSum_pv_banner()+"%");
                act.setSum_pv_new_banner(act.getSum_pv_new_banner()+"%");
            });
        }

        //查询是否是x模式
        List<UmengAdIncomeReportVo> isXList=appService.isXList(paramMap);
        Map<String,UmengAdIncomeReportVo> xMap = new HashMap<>();
        isXList.forEach(t->{
            String key = t.getTdate()+"_"+t.getAppid()+"_"+t.getChannel();
            xMap.put(key,t);
        });
        list.forEach(t->{
            //处理avgnum
            t.setAvgnum(strToTwoPercent(t.getAvgnum()));
            //处理在线时长
            t.setDaily_duration(StringUtils.secondsToHHmmss(t.getDaily_duration()));
            t.setBanner_ctr(t.getBanner_ctr() + "%");
            t.setPlaque_ctr(t.getPlaque_ctr() + "%");
            t.setSplash_ctr(t.getSplash_ctr() + "%");
            t.setVideo_ctr(t.getVideo_ctr() + "%");
            t.setNative_banner_ctr(t.getNative_banner_ctr() + "%");
            t.setNative_msg_ctr(t.getNative_msg_ctr() + "%");
            t.setNative_plaque_ctr(t.getNative_plaque_ctr() + "%");
            t.setNative_splash_ctr(t.getNative_splash_ctr() + "%");
            t.setNative_new_banner_ctr(t.getNative_new_banner_ctr()+"%");
            t.setNative_new_plaque_ctr(t.getNative_new_plaque_ctr() + "%");
            t.setPlaque_video_ctr(t.getPlaque_video_ctr() + "%");
            t.setSuspend_icon_ctr(t.getSuspend_icon_ctr() + "%");
            
            //处理是否x模式
            String xkey = t.getTdate()+"_"+t.getAppid()+"_"+t.getChannel();
            UmengAdIncomeReportVo x_vo = xMap.get(xkey);
            if(null!=x_vo){
                if("1".equals(x_vo.getIs_x())){
                    t.setIs_x("有");
                }else{
                    t.setIs_x("无");
                }
            }else{
                t.setIs_x("无");
            }
        });
        //根据前端筛选条件 是否是x模式进行过滤
        if(!StringUtils.isBlank(is_x)){
            if("1".equals(is_x)){
                list=list.stream().filter(f ->"有".equals(f.getIs_x())).collect(Collectors.toList());
            }else{
                list=list.stream().filter(f -> "无".equals(f.getIs_x())).collect(Collectors.toList());
            }
        }
        String value = BlankUtils.checkNull(request,"value");
        Map head = new LinkedHashMap();
        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        ExportExcelUtil.exportXLSX2(response,list,head,"渠道产品广告数据查询_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx");
    }

    @RequestMapping(value="batchImport", method={RequestMethod.GET,RequestMethod.POST})
    public void batchImport(@RequestParam(value="file_name") MultipartFile file,
                                         String tdate, String channel, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try{
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("text/html;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            PrintWriter out = response.getWriter();
            JSONObject obj = new JSONObject();

            if(null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")){
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                // 获取广告code对应的appkey的Map集合
                Map<String, Map<String, Object>> codeMap = new HashMap<String, Map<String, Object>>();
                String query = "select ad_code,appkey,adtype,status from umeng_adcode_list where channel = '"+channel+"'";
                List<Map<String, Object>> npMap = adService.queryListMap(query);
                for (Map<String, Object> map : npMap) {
                    codeMap.put(map.get("ad_code").toString(), map);
                }

                //获取appid信息
                String appQuery = "select concat(umeng_key,'') as mapkey,app_name,id as appid from yyhz_0308.app_info ";
                Map<String, Map<String, Object>> appInfoMap =  adService.queryListMapOfKey(appQuery);

                // 通过appkey来聚集多条内容到一个应用中
                List<UmengAdIncomeVo> dataList = new ArrayList<UmengAdIncomeVo>();
                Map<String, UmengAdIncomeVo> valMap = new HashMap<String, UmengAdIncomeVo>();
                int column = sheet.getColumns();
                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    if(BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;

                    String[] vals = new String[4];
                    for (int c = 0; c < 4; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }
                    // 得到广告位ID的map对象
                    Map<String, Object> code = codeMap.get(vals[0]);
                    if(code == null){
                        obj.put("ret", 0);
                        obj.put("msg", "广告位ID "+vals[0]+" 没有找到，请仔细核对！");
                        out.print(obj.toString());
                        out.close();
                        return;
                    }
                    // code处于停用状态，不录入
                    if("close".equals(code.get("status").toString()))
                        continue;

                    String appkey = code.get("appkey").toString();
                    String adtype = code.get("adtype").toString();
                    UmengAdIncomeVo ua = valMap.get(appkey);
                    if(ua == null || ua.getAppkey() == null){
                        // 初始化应用数据
                        ua = new UmengAdIncomeVo();
                        ua.setTdate(tdate);
                        ua.setAppkey(appkey);
                        ua.setChannel(channel);
                        ua.setAdcode(vals[0]);

                        ua.setBanner_show("0");
                        ua.setPlaque_show("0");
                        ua.setSplash_show("0");
                        ua.setVideo_show("0");
                        ua.setNative_banner_show("0");
                        ua.setNative_plaque_show("0");
                        ua.setNative_splash_show("0");
                        ua.setPlaque_video_show("0");
                        ua.setNative_msg_show("0");

                        //2021-08-26 添加
                        ua.setSystem_splash_show("0");
                        ua.setNative_new_banner_show("0");
                        ua.setNative_new_plaque_show("0");
                        ua.setSuspend_icon_show("0");


                        ua.setBanner_income("0");
                        ua.setPlaque_income("0");
                        ua.setSplash_income("0");
                        ua.setVideo_income("0");
                        ua.setNative_banner_income("0");
                        ua.setNative_plaque_income("0");
                        ua.setNative_splash_income("0");
                        ua.setPlaque_video_income("0");
                        ua.setNative_msg_income("0");

                        //2021-08-26 添加
                        ua.setSystem_splash_income("0");
                        ua.setNative_new_banner_income("0");
                        ua.setNative_new_plaque_income("0");
                        ua.setSuspend_icon_income("0");

                        ua.setBanner_ecpm("0");
                        ua.setPlaque_ecpm("0");
                        ua.setSplash_ecpm("0");
                        ua.setVideo_ecpm("0");
                        ua.setNative_banner_ecpm("0");
                        ua.setNative_plaque_ecpm("0");
                        ua.setNative_splash_ecpm("0");
                        ua.setPlaque_video_ecpm("0");
                        ua.setNative_msg_ecpm("0");

                        //2021-08-26 添加
                        ua.setSystem_splash_ecpm("0");
                        ua.setNative_new_banner_ecpm("0");
                        ua.setNative_new_plaque_ecpm("0");
                        ua.setSuspend_icon_ecpm("0");

                        ua.setNative_msg_click("0");
                        ua.setPlaque_video_click("0");
                        ua.setNative_splash_click("0");
                        ua.setNative_plaque_click("0");
                        ua.setNative_banner_click("0");
                        ua.setVideo_click("0");
                        ua.setSplash_click("0");
                        ua.setPlaque_click("0");
                        ua.setBanner_click("0");

                        //2021-08-26 添加
                        ua.setSystem_splash_click("0");
                        ua.setNative_new_banner_click("0");
                        ua.setNative_new_plaque_click("0");
                        ua.setSuspend_icon_click("0");
                    }

                    // ***这里相同的一个应用数据，show和income数值需要叠加，并且ecpm需要自己计算***
                    BigDecimal k = new BigDecimal(1000);
                    if("banner".equals(adtype)){
                        ua.setBanner_show(new BigDecimal(ua.getBanner_show())
                                .add(new BigDecimal(vals[1])).toString());
                        ua.setBanner_income(new BigDecimal(ua.getBanner_income())
                                .add(new BigDecimal(vals[3])).toString());
                        ua.setBanner_click((Integer.parseInt(ua.getBanner_click())+Integer.parseInt(vals[2]))+"");

                        if(Integer.valueOf(ua.getBanner_show()) != 0
                                && Double.valueOf(ua.getBanner_income()) != 0){

                            ua.setBanner_ecpm(
                                    new BigDecimal(ua.getBanner_income())
                                            .multiply(k)
                                            .divide(new BigDecimal(ua.getBanner_show()), 2, RoundingMode.FLOOR)
                                            .toString()
                            );
                        }
                        // 当值为小数时，截取小数点至后两位
                        /*if(vals[3].indexOf(".") != -1 && vals[3].length() > (vals[3].indexOf(".")+2)){
							ua.setBanner_ecpm(vals[3].substring(0, vals[3].indexOf(".")+3));
						}*/
                    }else if("plaque".equals(adtype)){
                        ua.setPlaque_show(new BigDecimal(ua.getPlaque_show())
                                .add(new BigDecimal(vals[1])).toString());
                        ua.setPlaque_income(new BigDecimal(ua.getPlaque_income())
                                .add(new BigDecimal(vals[3])).toString());
                        ua.setPlaque_click((Integer.parseInt(ua.getPlaque_click())+Integer.parseInt(vals[2]))+"");

                        if(Integer.valueOf(ua.getPlaque_show()) != 0
                                && Double.valueOf(ua.getPlaque_income()) != 0){

                            ua.setPlaque_ecpm(
                                    new BigDecimal(ua.getPlaque_income())
                                            .multiply(k)
                                            .divide(new BigDecimal(ua.getPlaque_show()), 2, RoundingMode.FLOOR)
                                            .toString()
                            );
                        }

                    }else if("splash".equals(adtype)){
                        ua.setSplash_show(new BigDecimal(ua.getSplash_show())
                                .add(new BigDecimal(vals[1])).toString());
                        ua.setSplash_income(new BigDecimal(ua.getSplash_income())
                                .add(new BigDecimal(vals[3])).toString());
                        ua.setSplash_click((Integer.parseInt(ua.getSplash_click())+Integer.parseInt(vals[2]))+"");

                        if(Integer.valueOf(ua.getSplash_show()) != 0
                                && Double.valueOf(ua.getSplash_income()) != 0){

                            ua.setSplash_ecpm(
                                    new BigDecimal(ua.getSplash_income())
                                            .multiply(k)
                                            .divide(new BigDecimal(ua.getSplash_show()), 2, RoundingMode.FLOOR)
                                            .toString()
                            );
                        }
                    }else if("video".equals(adtype)){
                        ua.setVideo_show(new BigDecimal(ua.getVideo_show())
                                .add(new BigDecimal(vals[1])).toString());
                        ua.setVideo_income(new BigDecimal(ua.getVideo_income())
                                .add(new BigDecimal(vals[3])).toString());
                        ua.setVideo_click((Integer.parseInt(ua.getVideo_click())+Integer.parseInt(vals[2]))+"");

                        if(Integer.valueOf(ua.getVideo_show()) != 0
                                && Double.valueOf(ua.getVideo_income()) != 0){

                            ua.setVideo_ecpm(
                                    new BigDecimal(ua.getVideo_income())
                                            .multiply(k)
                                            .divide(new BigDecimal(ua.getVideo_show()), 2, RoundingMode.FLOOR)
                                            .toString()
                            );
                        }
                    }else if("nativebanner".equals(adtype)){
                        ua.setNative_banner_show(new BigDecimal(ua.getNative_banner_show())
                                .add(new BigDecimal(vals[1])).toString());
                        ua.setNative_banner_income(new BigDecimal(ua.getNative_banner_income())
                                .add(new BigDecimal(vals[3])).toString());
                        ua.setNative_banner_click((Integer.parseInt(ua.getNative_banner_click())+Integer.parseInt(vals[2]))+"");


                        if(Integer.valueOf(ua.getNative_banner_show()) != 0
                                && Double.valueOf(ua.getNative_banner_income()) != 0){

                            ua.setNative_banner_ecpm(
                                    new BigDecimal(ua.getNative_banner_income())
                                            .multiply(k)
                                            .divide(new BigDecimal(ua.getNative_banner_show()), 2, RoundingMode.FLOOR)
                                            .toString()
                            );
                        }
                    }else if("nativeplaque".equals(adtype)){
                        ua.setNative_plaque_show(new BigDecimal(ua.getNative_plaque_show())
                                .add(new BigDecimal(vals[1])).toString());
                        ua.setNative_plaque_income(new BigDecimal(ua.getNative_plaque_income())
                                .add(new BigDecimal(vals[3])).toString());
                        ua.setNative_plaque_click((Integer.parseInt(ua.getNative_plaque_click())+Integer.parseInt(vals[2]))+"");

                        if(Integer.valueOf(ua.getNative_plaque_show()) != 0
                                && Double.valueOf(ua.getNative_plaque_income()) != 0){

                            ua.setNative_plaque_ecpm(
                                    new BigDecimal(ua.getNative_plaque_income())
                                            .multiply(k)
                                            .divide(new BigDecimal(ua.getNative_plaque_show()), 2, RoundingMode.FLOOR)
                                            .toString()
                            );
                        }
                    }else if("nativesplash".equals(adtype)){
                        ua.setNative_splash_show(new BigDecimal(ua.getNative_splash_show())
                                .add(new BigDecimal(vals[1])).toString());
                        ua.setNative_splash_income(new BigDecimal(ua.getNative_splash_income())
                                .add(new BigDecimal(vals[3])).toString());
                        ua.setNative_splash_click((Integer.parseInt(ua.getNative_splash_click())+Integer.parseInt(vals[2]))+"");

                        if(Integer.valueOf(ua.getNative_splash_show()) != 0
                                && Double.valueOf(ua.getNative_splash_income()) != 0){

                            ua.setNative_splash_ecpm(
                                    new BigDecimal(ua.getNative_splash_income())
                                            .multiply(k)
                                            .divide(new BigDecimal(ua.getNative_splash_show()), 2, RoundingMode.FLOOR)
                                            .toString()
                            );
                        }
                    }else if("plaquevideo".equals(adtype)){
                        ua.setPlaque_video_show(new BigDecimal(ua.getPlaque_video_show())
                                .add(new BigDecimal(vals[1])).toString());
                        ua.setPlaque_video_income(new BigDecimal(ua.getPlaque_video_income())
                                .add(new BigDecimal(vals[3])).toString());
                        ua.setPlaque_video_click((Integer.parseInt(ua.getPlaque_video_click())+Integer.parseInt(vals[2]))+"");


                        if(Integer.valueOf(ua.getPlaque_video_show()) != 0
                                && Double.valueOf(ua.getPlaque_video_income()) != 0){

                            ua.setPlaque_video_ecpm(
                                    new BigDecimal(ua.getPlaque_video_income())
                                            .multiply(k)
                                            .divide(new BigDecimal(ua.getPlaque_video_show()), 2, RoundingMode.FLOOR)
                                            .toString()
                            );
                        }

                    }else if("nativemsg".equals(adtype)){
                        ua.setNative_msg_show(new BigDecimal(ua.getNative_msg_show())
                                .add(new BigDecimal(vals[1])).toString());
                        ua.setNative_msg_income(new BigDecimal(ua.getNative_msg_income())
                                .add(new BigDecimal(vals[3])).toString());
                        ua.setNative_msg_click((Integer.parseInt(ua.getNative_msg_click())+Integer.parseInt(vals[2]))+"");

                        if(Integer.valueOf(ua.getNative_msg_show()) != 0
                                && Double.valueOf(ua.getNative_msg_income()) != 0){

                            ua.setNative_msg_ecpm(
                                    new BigDecimal(ua.getNative_msg_income())
                                            .multiply(k)
                                            .divide(new BigDecimal(ua.getNative_msg_show()), 2, RoundingMode.FLOOR)
                                            .toString()
                            );
                        }
                    }
                    //2021-08-26 添加
                    else if("systemsplash".equals(adtype)){
                        ua.setSystem_splash_show(new BigDecimal(ua.getSystem_splash_show())
                                .add(new BigDecimal(vals[1])).toString());
                        ua.setSystem_splash_income(new BigDecimal(ua.getSystem_splash_income())
                                .add(new BigDecimal(vals[3])).toString());
                        ua.setSystem_splash_click(new BigDecimal(ua.getSystem_splash_click())
                                .add(new BigDecimal(vals[2])).toString());

                        if(Integer.valueOf(ua.getSystem_splash_show()) != 0
                                && Double.valueOf(ua.getSystem_splash_income()) != 0){

                            ua.setSystem_splash_ecpm(
                                    new BigDecimal(ua.getSystem_splash_income())
                                            .multiply(k)
                                            .divide(new BigDecimal(ua.getSystem_splash_show()), 2, RoundingMode.FLOOR)
                                            .toString()
                            );
                        }
                    }else if("nativenewplaque".equals(adtype)){
                        ua.setNative_new_plaque_show(new BigDecimal(ua.getNative_new_plaque_show())
                                .add(new BigDecimal(vals[1])).toString());
                        ua.setNative_new_plaque_income(new BigDecimal(ua.getNative_new_plaque_income())
                                .add(new BigDecimal(vals[3])).toString());
                        ua.setNative_new_plaque_click(new BigDecimal(ua.getNative_new_plaque_click())
                                .add(new BigDecimal(vals[2])).toString());

                        if(Integer.valueOf(ua.getNative_new_plaque_show()) != 0
                                && Double.valueOf(ua.getNative_new_plaque_income()) != 0){

                            ua.setNative_new_plaque_ecpm(
                                    new BigDecimal(ua.getNative_new_plaque_income())
                                            .multiply(k)
                                            .divide(new BigDecimal(ua.getNative_new_plaque_show()), 2, RoundingMode.FLOOR)
                                            .toString()
                            );
                        }
                    }else if("nativenewbanner".equals(adtype)){
                        ua.setNative_new_banner_show(new BigDecimal(ua.getNative_new_banner_show())
                                .add(new BigDecimal(vals[1])).toString());
                        ua.setNative_new_banner_income(new BigDecimal(ua.getNative_new_banner_income())
                                .add(new BigDecimal(vals[3])).toString());
                        ua.setNative_new_banner_click(new BigDecimal(ua.getNative_new_banner_click())
                                .add(new BigDecimal(vals[2])).toString());

                        if(Integer.valueOf(ua.getNative_new_banner_show()) != 0
                                && Double.valueOf(ua.getNative_new_banner_income()) != 0){

                            ua.setNative_new_banner_ecpm(
                                    new BigDecimal(ua.getNative_new_banner_income())
                                            .multiply(k)
                                            .divide(new BigDecimal(ua.getNative_new_banner_show()), 2, RoundingMode.FLOOR)
                                            .toString()
                            );
                        }
                    } else if("suspendicon".equals(adtype)){
                        ua.setSuspend_icon_show(new BigDecimal(ua.getSuspend_icon_show())
                                .add(new BigDecimal(vals[1])).toString());
                        ua.setSuspend_icon_income(new BigDecimal(ua.getSuspend_icon_income())
                                .add(new BigDecimal(vals[3])).toString());
                        ua.setSuspend_icon_click(new BigDecimal(ua.getSuspend_icon_click())
                                .add(new BigDecimal(vals[2])).toString());

                        if(Integer.valueOf(ua.getSuspend_icon_show()) != 0
                                && Double.valueOf(ua.getSuspend_icon_income()) != 0){

                            ua.setSuspend_icon_ecpm(
                                    new BigDecimal(ua.getSuspend_icon_income())
                                            .multiply(k)
                                            .divide(new BigDecimal(ua.getSuspend_icon_show()), 2, RoundingMode.FLOOR)
                                            .toString()
                            );
                        }
                    }


                    valMap.put(appkey, ua);
                }

                // 友盟key对应的活跃用户数据
                String sql = "select addnum,tdate,appname,appkey,actnum from umeng_channel_total"
                        + " where tdate = '"+tdate+"' and cname = '"+channel+"'";
                List<Map<String, Object>> appMap = adService.queryListMap(sql);

                Iterator<Map.Entry<String, UmengAdIncomeVo>> iterator = valMap.entrySet().iterator();
                while(iterator.hasNext()){
                    Map.Entry<String, UmengAdIncomeVo> next = iterator.next();
                    UmengAdIncomeVo value = next.getValue();

                    boolean flag = true;
                    // 展示收入内容的友盟key与数据库友盟key匹配，获取活跃用户数
                    for (Map<String, Object> map : appMap) {
                        if(value.getAppkey().equals(map.get("appkey").toString())){
                            value.setAppid(appInfoMap.get(map.get("appkey").toString()).get("appid")!=null?appInfoMap.get(map.get("appkey").toString()).get("appid").toString():"");
                            value.setAppname(map.get("appname").toString());
                            value.setActnum((Integer)map.get("actnum"));
                            value.setAddnum((Integer)map.get("addnum"));
                            flag = false;
                            break;
                        }
                    }
                    // 没有匹配到渠道里的这个应用
                    if(flag){
                        obj.put("ret", 0);
                        obj.put("msg", "广告位ID "+value.getAdcode()+" 没有匹配到渠道里的这个应用！");
                        out.print(obj.toString());
                        out.close();
                        return;
                    }

                    // 获得活跃数后计算其它数据
                    BigDecimal actnum = new BigDecimal(value.getActnum());
                    if(value.getActnum() != 0){
                        value.setTotal_income(
                                new BigDecimal(value.getBanner_income())
                                        .add(new BigDecimal(value.getPlaque_income()))
                                        .add(new BigDecimal(value.getSplash_income()))
                                        .add(new BigDecimal(value.getVideo_income()))
                                        .add(new BigDecimal(value.getNative_banner_income()))
                                        .add(new BigDecimal(value.getNative_plaque_income()))
                                        .add(new BigDecimal(value.getNative_splash_income()))
                                        .add(new BigDecimal(value.getPlaque_video_income()))
                                        .add(new BigDecimal(value.getNative_msg_income()))
                                        //2021-08-26 添加
                                        .add(new BigDecimal(value.getSystem_splash_income()))
                                        .add(new BigDecimal(value.getNative_new_plaque_income()))
                                        .add(new BigDecimal(value.getNative_new_banner_income()))
                                        .add(new BigDecimal(value.getSuspend_icon_income()))
                                        .toString()
                        );
                        value.setDau_arpu(
                                new BigDecimal(value.getTotal_income())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );

                        //新增占比
                        value.setAvgnum(new BigDecimal(value.getAddnum()).multiply(new BigDecimal(100))
                                .divide(actnum, 2, RoundingMode.HALF_UP)
                                +"%");

                        // 各类型 人均pv
                        value.setBanner_pv(
                                new BigDecimal(value.getBanner_show())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setPlaque_pv(
                                new BigDecimal(value.getPlaque_show())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setSplash_pv(
                                new BigDecimal(value.getSplash_show())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setVideo_pv(
                                new BigDecimal(value.getVideo_show())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setMsg_pv(
                                new BigDecimal(value.getNative_msg_show())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setPlaque_video_pv(
                                new BigDecimal(value.getPlaque_video_show())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );

                        //2021-08-26 添加
                        value.setSystem_splash_pv(
                                new BigDecimal(value.getSystem_splash_show())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setNative_new_plaque_pv(
                                new BigDecimal(value.getNative_new_plaque_show())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setNative_new_banner_pv(
                                new BigDecimal(value.getNative_new_banner_show())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setSuspend_icon_pv(
                                new BigDecimal(value.getSuspend_icon_show())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        //原生插屏人均pv
                        value.setNative_plaque_pv(
                                new BigDecimal(value.getNative_plaque_show())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        //原生banner人均pv
                        value.setNative_banner_pv(
                                new BigDecimal(value.getNative_banner_show())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        // 每用户平均收入 arpu
                        value.setBanner_arpu(
                                new BigDecimal(value.getBanner_income())
                                        .add(new BigDecimal(value.getNative_banner_income()))
                                        .add(new BigDecimal(value.getNative_new_banner_income()))
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setPlaque_arpu(
                                new BigDecimal(value.getPlaque_income())
                                        .add(new BigDecimal(value.getNative_plaque_income()))
                                        .add(new BigDecimal(value.getNative_new_plaque_income()))
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setSplash_arpu(
                                new BigDecimal(value.getSplash_income())
                                        .add(new BigDecimal(value.getNative_splash_income()))
                                        .add(new BigDecimal(value.getSystem_splash_income()))
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setVideo_arpu(
                                new BigDecimal(value.getVideo_income())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setMsg_arpu(
                                new BigDecimal(value.getNative_msg_income())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setPlaque_video_arpu(
                                new BigDecimal(value.getPlaque_video_income())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        //2021-08-26 添加
                        value.setSystem_splash_arpu(
                                new BigDecimal(value.getSystem_splash_income())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setNative_new_plaque_arpu(
                                new BigDecimal(value.getNative_new_plaque_income())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setNative_new_banner_arpu(
                                new BigDecimal(value.getNative_new_banner_income())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );
                        value.setSuspend_icon_arpu(
                                new BigDecimal(value.getSuspend_icon_income())
                                        .divide(actnum, 2, RoundingMode.HALF_UP)
                                        .toString()
                        );

                    }
                    dataList.add(value);
                }

                int result = adService.insertUmengAdIncomeList(dataList);
                // 添加到数据表中
                if(result > 0){
                    obj.put("ret", 1);
                    obj.put("msg", "上传文件成功");
                }else{
                    obj.put("ret", 0);
                    obj.put("msg", "文件数据有错误");
                }
            }else{
                obj.put("ret", 0);
                obj.put("msg", "上传的文件格式有误，需要.xls格式文件！");
            }
            out.print(obj.toString());
            out.close();

        }catch (Exception e) {
            //上传异常
            e.printStackTrace();
            PrintWriter pw;
            try {
                pw = response.getWriter();
                JSONObject obj = new JSONObject();
                obj.put("ret", 0);
                obj.put("msg", "导入数据失败");
                pw.print(obj.toString());
                pw.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        }
    }

    public static final Map<String, String> UMENG_CHANNEL_MEDIA_MAP = new HashMap<String, String>() {{
        put("oppo", "oppo");
        put("oppo2", "oppo");
        put("oppomj", "oppo");
        put("oppomj2", "oppo");
        put("oppoml", "oppo");
        put("h5_oppo", "oppo");
        put("uc", "oppo");

        put("vivo", "vivo");
        put("vivo2", "vivo");
        put("vivomj", "vivo");
        put("vivoml", "vivo");
        put("vivoml2", "vivo");
        put("h5_vivo", "vivo");

        put("huawei", "huawei");
        put("huawei2", "huawei");
        put("huaweiml", "huawei");
        put("huaweiml2", "huawei");
        put("h5_huawei", "huawei");
        put("google_huawei", "huawei");
        put("google_xiaomi", "xiaomi");

        put("xiaomi", "xiaomi");
        put("xiaomimj", "xiaomi");
        put("xiaomiml", "xiaomi");
        put("xiaomisd", "xiaomi");
        put("xiaomiwt", "xiaomi");
        put("xmyy", "xiaomi");
        put("xiaomiml2", "xiaomi");

        put("honor", "honor");
    }};

    public static final Map<String, List<String>> UMENG_MEDIA_LIST_MAP = new HashMap<String, List<String>>() {{
        put("xiaomi", Lists.newArrayList("xiaomi","xiaomimj","xiaomiml","xiaomisd","xiaomiwt","xmyy","xiaomiml2"));
        put("huawei", Lists.newArrayList("huawei","huawei2","huaweiml","huaweiml2"));
        put("vivo", Lists.newArrayList("vivo","vivo2","vivomj","vivoml","vivoml2"));
        put("oppo", Lists.newArrayList("oppo","oppo2","oppomj","oppomj2","oppoml"));
        put("honor", Lists.newArrayList("honor"));
    }};

    @RequestMapping(value = "/syn")
    public String syn(HttpServletRequest request, HttpServletResponse response) {
        String begin = request.getParameter("begin").replace("-", "");
        String end = request.getParameter("end").replace("-", "");
        String channel = request.getParameter("channel");
        String media = request.getParameter("media");
        if (BlankUtils.checkBlank(begin) || BlankUtils.checkBlank(end) || (BlankUtils.checkBlank(channel) && BlankUtils.checkBlank(media))) {
            return ReturnJson.error(Constants.ParamError);
        }
        if (BlankUtils.isBlank(media)) {
            return doSync(begin, end, channel);
        } else if (BlankUtils.isNotBlank(media)) {
            List<String> channels = UMENG_MEDIA_LIST_MAP.get(media);
            StringBuilder sb = new StringBuilder();
            for (String cha : channels) {
                String ret = doSync(begin, end, cha);
//                JSONObject jsonObject = JSONObject.parseObject(ret);
                sb.append(cha).append(":").append(ret).append("<br>");
            }
            return ReturnJson.success(sb);
        }
        return ReturnJson.toErrorJson("");
    }

    private String doSync(String begin, String end, String channel) {
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyyMMdd");
        DateTime from = DateTime.parse(begin, format);
        DateTime to = DateTime.parse(end, format);
        String[] oppoChannels = {"oppo","oppoml","oppo2","oppomj","vivo","vivoml","vivo2","xiaomi","xmyy","xiaomimj","huawei","huaweimj","google_huawei"};
        String result = "";
        //循环日期同步
        while (from.compareTo(to) <= 0) {
            logger.info("同步时间："+from.toString("yyyyMMdd")+",channel:"+ channel);
            try {
                String resp = umengService.syncChannelAppData(from.toString("yyyyMMdd"), channel);
                // 同步自统计数据--取数来源广告预计收入
                umengService.syncChannelSelfData(from.toString("yyyyMMdd"), channel);
                //处理oppo新增活跃
                if (Arrays.asList(oppoChannels).contains(channel)){
                    umengService.syncOppoAdData(from.toString("yyyyMMdd"), channel);
                    //处理oppo新增活跃 -- 自统计
                    umengService.syncOppoAdSelfData(from.toString("yyyyMMdd"), channel);
                }
                logger.info("channel:"+ channel +",syn:"+resp);
                JSONObject res = JSONObject.parseObject(resp);
                //ret不等于直接返回当前日期错误信息
                if (res.getInteger("ret") != 1) {
                    res.put("msg", "同步日期:" + from.toString("yyyy-MM-dd") + ": " + res.getString("msg"));
                    return res.toJSONString();
                } else {
                    result = resp;
                }
                from = from.plusDays(1);
            } catch (Exception e) {
                e.printStackTrace();
                return ReturnJson.toErrorJson("同步日期: " + from.toString("yyyyMMdd") + "：出现异常,请重试");
            }
        }
        return result;
    }

    /**
     * 同步广告渠道数据
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/sync")
    public String sync(HttpServletRequest request, HttpServletResponse response) {
        String begin = request.getParameter("begin").replace("-", "");
        String end = request.getParameter("end").replace("-", "");
        String channel = request.getParameter("channel");
        if (BlankUtils.checkBlank(begin) || BlankUtils.checkBlank(end) || BlankUtils.checkBlank(channel)) {
            return ReturnJson.error(Constants.ParamError);
        }
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyyMMdd");
        DateTime from = DateTime.parse(begin, format);
        DateTime to = DateTime.parse(end, format);
        String[] oppoChannels = {"oppo","oppoml","oppo2","oppomj","vivo","vivoml","vivo2","xiaomi","xmyy","xiaomimj","huawei","huaweimj","google_huawei"};
        String result = "";
        //循环日期同步
        while (from.compareTo(to) <= 0) {
            System.out.println("同步时间："+from.toString("yyyyMMdd"));
            try {
                String resp = umengService.syncChannelAppData(from.toString("yyyyMMdd"), channel);
                //
                if (Arrays.asList(oppoChannels).contains(channel)){
                    umengService.syncOppoAdData(from.toString("yyyyMMdd"),channel);
                }
                System.out.println("channel:"+channel+",syn:"+resp);
                JSONObject res = JSONObject.parseObject(resp);
                //ret不等于直接返回当前日期错误信息
                if (res.getInteger("ret") != 1) {
                    res.put("msg", "同步日期:" + from.toString("yyyy-MM-dd") + ": " + res.getString("msg"));
                    return res.toJSONString();
                } else {
                    result = resp;
                }
                from = from.plusDays(1);
            } catch (Exception e) {
                e.printStackTrace();
                return ReturnJson.toErrorJson("同步日期: "+from.toString("yyyyMMdd")+"：出现异常,请重试");
            }
        }
        return result;
    }


    /**
     *
     * @param data1 当前日期数据
     * @param data2 前一天数据
     * @return
     */
    private String strToTwoNumber(String data1,String data2){
        String rate ="0.00%";
        try {
            BigDecimal brate = new BigDecimal(data1)
                    .subtract(new BigDecimal(data2))
                    .divide(new BigDecimal(data2),4,RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2,RoundingMode.HALF_UP);
            rate = brate.toString()+"%";
        }catch (Exception e){
        }
        return rate;
    }

    /**
     * 保留两位小数-百分比
     * @param data
     * @return
     */
    private String strToTwoPercent(String data){
        try {
            BigDecimal twoPercent = new BigDecimal(data)
                    .multiply(new BigDecimal("100"))
                    .setScale(2,RoundingMode.UNNECESSARY);
            data = twoPercent.toString()+"%";
        }catch (Exception e){

        }
        return data;
    }

    /**
     * 组装group参数
     * @param group
     * @return
     */
    private String generateGroupParam(String group){
        if (!BlankUtils.checkBlank(group)){
            String[] groups = group.split(",");
            StringBuffer stringBuffer = new StringBuffer();
            for (int i=0;i<groups.length;i++){
                if (i!=groups.length-1){
                    stringBuffer.append("b.").append(groups[i]).append(",");
                }else {
                    stringBuffer.append("b.").append(groups[i]);
                }
            }
            group = stringBuffer.toString();
        }
        return group;
    }


    /**
     * 组装order_str参数
     * @param order_str
     * @return
     */
    private String generateOrderStrParam(String order_str){
        //排序特殊 因为total_income字段数据库为字符串
        if (!BlankUtils.checkBlank(order_str)){
            String order = order_str.split(" ")[0];
            String sort = order_str.split(" ")[1];
            order_str = "b."+ order +" " +sort;
        }else {
            order_str = "";
        }
        return order_str;
    }

    /**
     * 去除 % 对比给定的数值
     * ori < num
     * */
    private boolean percentageCompare(String ori, int num) {
        if (BlankUtils.isBlank(ori)) {
            return false;
        }

        String target = ori.replace("%", "");
        if (!BlankUtils.isNumeric(target)) {
            return false;
        }

        return Double.parseDouble(target) < num;
    }

    private final DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");

    @RequestMapping(value="warning/list", method={RequestMethod.POST})
    public Object warning(UmengAdIncomeWarningVo vo) {
        vo.setOrder_str(generateOrderStrParam(vo.getOrder_str()));
        vo.setGroup(generateGroupParam(vo.getGroup()));

        List<UmengAdIncomeReportVo> arpuList = new ArrayList<>();
        List<UmengAdIncomeReportVo> pvList = new ArrayList<>();
        List<UmengAdIncomeReportVo> ctrList = new ArrayList<>();

        String tableName = "umeng_ad_income";
        if ("2".equals(vo.getDataSource())){
            tableName = "oppo_ad_income";
        }
        vo.setTableName(tableName);
        int limit = vo.getLimit();
        int start = vo.getStart();
        vo.setAddNumCondition(" c.actnum > 100 ");
        HashMap<String, String> state = new HashMap<String, String>() {{put("state", vo.getState());}};
        vo.setCon_ac(appService.getAppChannelList(state));

        // 不分页告警数据
        List<UmengAdIncomeReportVo> list = appService.getUmengAdIncomeWarningList(vo);
        if (CollectionUtils.isEmpty(list)) {
            Map<String, List<UmengAdIncomeReportVo>> data = new HashMap<>();
            data.put("arpu", new ArrayList<>());
            data.put("pv", new ArrayList<>());
            data.put("ctr", new ArrayList<>());
            return ReturnJson.success(data);
        }


        //取前一天数据
        String startDate = vo.getStart_date();
        String endDate = vo.getEnd_date();
        /* 保证原查询结果，增加筛选条件“增长率计算”，可选择周同比或日环比计算数据 -孙文凤.20240418 */
        Integer lastDay; // 使用日环比
        if ("week".equals(vo.getDimension())) { // 使用周同比
            lastDay = 7;
        } else {
            lastDay = 1;
        }
        // 调整策略为，全部使用日环比或全部使用周同比，不再区分字段来使用
        String before_start = DateTime.parse(startDate,format).minusDays(lastDay).toString("yyyy-MM-dd");
        String before_end = DateTime.parse(endDate,format).minusDays(lastDay).toString("yyyy-MM-dd");
        vo.setStart_date(before_start);
        vo.setEnd_date(before_end);
        List<UmengAdIncomeReportVo> beforeList = appService.getUmengAdIncomeWarningList(vo);

        /*Map<String, UmengAdIncomeReportVo> dayBefore = beforeList.stream().collect(Collectors
                .toMap(bef -> bef.getTdate() + "_" + bef.getAppkey() + "_" + bef.getChannel(),
                        bef -> bef,
                        (x,y)-> x));*/


        // 补充每条记录的状态
        setState(arpuList, vo);
        // 环比同比处理
        String group = vo.getGroup();
        List<String> keyList = new ArrayList<>();
        if (!StringUtils.isEmpty(group)) {
            keyList.addAll(Stream.of("appkey", "channel").filter(group::contains).collect(Collectors.toList()));
        }
        try {
            CommonCompareUtils.compare(list, beforeList, keyList, Arrays.asList(WARN_RATE_FIELDS.split(",")), UmengAdIncomeReportVo::getTdate, lastDay);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }

        list.forEach( tt -> {

            // 保留原始的Vo
            UmengAdIncomeReportVo baseVo = new UmengAdIncomeReportVo();
            BeanUtils.copyProperties(tt, baseVo);
            //processData(baseVo, dayBefore, dayBefore, lastDay);

            // dau_arpu环比/同比 值下降20%
            String dauArpuRate = baseVo.getDau_arpu_rate();
            // 插屏人均pv环比/同比 值下降20%
            String totalPlaquePvRate = baseVo.getTotal_plaque_pv_rate();
            // 新样式插屏ctr环比/同比 值低于30%
            String nativeNewPlaqueCtr = (baseVo.getNative_new_plaque_ctr_rate()!=null?(baseVo.getNative_new_plaque_ctr_rate().replace("%","")):"");


            /* dau_arpu的范围类型筛选 */
            if(BlankUtils.isNotBlank(vo.getDau_arpu_match())){
                String[] split = vo.getDau_arpu_match().split(":");
                String p = split[0];
                String rule = split[1];
                String value = split[2];
                boolean rateRuleFlag = "涨幅".equals(rule) || "降幅".equals(rule);
                // 按照规则筛选匹配的dauArpu值下发
                // String dauArpu = t.getDau_arpu();
                // 改为同比/环比计算匹配的dauArpuRate值下发 -20240606
                String dauArpu = rateRuleFlag ? (baseVo.getDau_arpu_rate() != null?(baseVo.getDau_arpu_rate().replace("%","")):"") : baseVo.getDau_arpu();
                if (rule.equals("大于") && BlankUtils.getDouble(dauArpu) > BlankUtils.getDouble(value)) {
                    arpuList.add(baseVo);
                } else if ((rule.equals("大于等于") || rule.equals("涨幅")) && BlankUtils.getDouble(dauArpu) >= BlankUtils.getDouble(value)) {
                    arpuList.add(baseVo);
                } else if (rule.equals("小于") && BlankUtils.getDouble(dauArpu) < BlankUtils.getDouble(value)) {
                    arpuList.add(baseVo);
                } else if (rule.equals("小于等于") && BlankUtils.getDouble(dauArpu) <= BlankUtils.getDouble(value)) {
                    arpuList.add(baseVo);
                } else if (rule.equals("降幅") && BlankUtils.getDouble(dauArpu) <= -BlankUtils.getDouble(value)) {
                    arpuList.add(baseVo);
                }
            }else if (percentageCompare(dauArpuRate, -20)) {
                arpuList.add(baseVo);
            }

            /* 总人均pv的范围类型筛选 */
            if(BlankUtils.isNotBlank(vo.getTotal_pv_match())){
                String[] split = vo.getTotal_pv_match().split(":");
                String p = split[0];
                String rule = split[1];
                String value = split[2];
                // 改为同比/环比计算匹配的总pv值下发 -20240606
                String matchValue = null;
                //判断是否为 涨幅 或 降幅
                boolean rateRuleFlag = "涨幅".equals(rule) || "降幅".equals(rule);
                if ("splash_pv".equals(p)) {
                    //总人均pv-开屏（开屏（系统开屏+开屏+原生开屏））
                    matchValue = rateRuleFlag ? baseVo.getTotal_splash_pv_rate() : baseVo.getTotal_splash_pv();
                } else if ("plaque_pv".equals(p)) {   //total_plaque_pv --》plaque_pv
                    //人均pv-插屏
                    matchValue = rateRuleFlag ? baseVo.getPlaque_pv_rate() : baseVo.getPlaque_pv();
                } else if ("native_new_plaque_pv".equals(p)) {
                    //人均pv-(msg/yuans)插屏
                    matchValue = rateRuleFlag ? baseVo.getNative_new_plaque_pv_rate() : baseVo.getNative_new_plaque_pv();
                } else if ("plaque_video_pv".equals(p)) {  //total_plaque_video_pv --> plaque_video_pv
                    //人均pv-插屏视频
                    matchValue = rateRuleFlag ? baseVo.getPlaque_video_pv_rate() : baseVo.getPlaque_video_pv();
                } else if ("banner_pv".equals(p)) {  //total_banner_pv --> banner_pv
                    //人均pv-banner
                    matchValue = rateRuleFlag ? baseVo.getBanner_pv_rate() : baseVo.getBanner_pv();
                } else if ("native_new_banner_pv".equals(p)) {
                    //人均pv -(msg/yuans)banner
                    matchValue = rateRuleFlag ? baseVo.getNative_new_banner_pv_rate() : baseVo.getNative_new_banner_pv();
                } else if ("video_pv".equals(p)) {  //total_video_pv --> video_pv
                    //人均pv-视频
                    matchValue = rateRuleFlag ? baseVo.getVideo_pv_rate() : baseVo.getVideo_pv();
                } else if ("native_msg_pv".equals(p)) {  //total_native_msg_pv--->native_msg_pv
                    //人均pv-原生msg
                    matchValue = rateRuleFlag ? baseVo.getNative_msg_pv_rate() : baseVo.getNative_msg_pv();
                } else if ("suspend_icon_pv".equals(p)) { //total_suspend_icon_pv --> suspend_icon_pv
                    //人均pv-悬浮icon
                    matchValue = rateRuleFlag ? baseVo.getSuspend_icon_pv_rate() : baseVo.getSuspend_icon_pv();
                }


                // 按照规则筛选匹配的总pv值下发
                matchValue = (matchValue != null?(matchValue.replace("%","")):"");
                if (rule.equals("大于") && BlankUtils.getDouble(matchValue) > BlankUtils.getDouble(value)) {
                    pvList.add(baseVo);
                } else if ((rule.equals("大于等于") || rule.equals("涨幅")) && BlankUtils.getDouble(matchValue) >= BlankUtils.getDouble(value)) {
                    pvList.add(baseVo);
                } else if (rule.equals("小于") && BlankUtils.getDouble(matchValue) < BlankUtils.getDouble(value)) {
                    pvList.add(baseVo);
                } else if (rule.equals("小于等于") && BlankUtils.getDouble(matchValue) <= BlankUtils.getDouble(value)) {
                    pvList.add(baseVo);
                } else if (rule.equals("降幅") && BlankUtils.getDouble(matchValue) <= -BlankUtils.getDouble(value)) {
                    pvList.add(baseVo);
                }
            }else if (percentageCompare(totalPlaquePvRate, -20)) {
                pvList.add(baseVo);
            }

            // 通过 platform_threshold 字段来控制单个平台的ctr 告警阈值
            String platformThreshold = vo.getPlatform_threshold();
            if (BlankUtils.isNotBlank(platformThreshold)) {
                //格式：233ly:splash_ctr:大于等于:10,214ly:splash_ctr:大于等于:10
                for (String platform : platformThreshold.split(",")) {
                    //233ly::大于等于:30
                    String[] split = platform.split(":");
                    String channel = split[0];
                    String value = split[3];
                    String rule = split[2];
                    //比较类型
                    String type = split[1];
                    if (baseVo.getChannel().equals(channel)) {
                        //判断是否为 涨幅 或 降幅,涨幅和降幅使用增长率进行比较
                        boolean rateRuleFlag = "涨幅".equals(rule) || "降幅".equals(rule);
                        // ctr 新增筛选类型
                        String matchValue = null;
                        if ("splash_ctr".equals(type)) {
                            //ctr--开屏（系统开屏+开屏+原生开屏） 当前使用的是 ctr-开屏汇总 字段
                            matchValue = rateRuleFlag ? baseVo.getTotal_splash_ctr_rate() : baseVo.getTotal_splash_ctr();
                        } else if ("plaque_ctr".equals(type)) {
                            //ctr--插屏
                            matchValue = rateRuleFlag ? baseVo.getPlaque_ctr_rate() : baseVo.getPlaque_ctr();
                        } else if ("native_new_plaque_ctr".equals(type)) {
                            //ctr--(msg/yuan)插屏
                            matchValue = rateRuleFlag ? baseVo.getNative_new_plaque_ctr_rate() : baseVo.getNative_new_plaque_ctr();
                        } else if ("plaque_video_ctr".equals(type)) {
                            //ctr--插屏视频
                            matchValue = rateRuleFlag ? baseVo.getPlaque_video_ctr_rate() : baseVo.getPlaque_video_ctr();
                        } else if ("banner_ctr".equals(type)) {
                            //ctr--banner
                            matchValue = rateRuleFlag ? baseVo.getBanner_ctr_rate() : baseVo.getBanner_ctr();
                        } else if ("native_new_banner_ctr".equals(type)) {
                            //ctr--(msg/yuan)banner
                            matchValue = rateRuleFlag ? baseVo.getNative_new_banner_ctr_rate() : baseVo.getNative_new_banner_ctr();
                        } else if ("video_ctr".equals(type)) {
                            //ctr--视频
                            matchValue = rateRuleFlag ? baseVo.getVideo_ctr_rate() : baseVo.getVideo_ctr();
                        } else if ("native_msg_ctr".equals(type)) {
                            //ctr--原生msg
                            matchValue = rateRuleFlag ? baseVo.getNative_msg_ctr_rate() : baseVo.getNative_msg_ctr();
                        } else if ("suspend_icon_ctr".equals(type)) {
                            //ctr--悬浮icon
                            matchValue = rateRuleFlag ? baseVo.getSuspend_icon_ctr_rate() : baseVo.getSuspend_icon_ctr();
                        }
                        // 按照规则筛选匹配的总pv值下发
                        matchValue = (matchValue != null?(matchValue.replace("%","")):"");
                        if (rule.equals("大于") && BlankUtils.getDouble(matchValue) > BlankUtils.getDouble(value)) {
                            ctrList.add(baseVo);
                        } else if ((rule.equals("大于等于") || rule.equals("涨幅")) && BlankUtils.getDouble(matchValue) >= BlankUtils.getDouble(value)) {
                            ctrList.add(baseVo);
                        } else if (rule.equals("小于") && BlankUtils.getDouble(matchValue) < BlankUtils.getDouble(value)) {
                            ctrList.add(baseVo);
                        } else if (rule.equals("小于等于") && BlankUtils.getDouble(matchValue) <= BlankUtils.getDouble(value)) {
                            ctrList.add(baseVo);
                        } else if (rule.equals("降幅") && BlankUtils.getDouble(matchValue) <= -BlankUtils.getDouble(value)) {
                            ctrList.add(baseVo);
                        }
                    }
                }
            }else if (BlankUtils.isNumeric(nativeNewPlaqueCtr) && Double.parseDouble(nativeNewPlaqueCtr) < 30D) {// 默认规则
                ctrList.add(baseVo);
            }
            baseVo.setAll_total_ctr(baseVo.getAll_total_ctr() + "%");
            baseVo.setBanner_ctr(baseVo.getBanner_ctr() + "%");
            baseVo.setPlaque_ctr(baseVo.getPlaque_ctr() + "%");
            baseVo.setSplash_ctr(baseVo.getSplash_ctr() + "%");
            baseVo.setVideo_ctr(baseVo.getVideo_ctr() + "%");
            baseVo.setNative_banner_ctr(baseVo.getNative_banner_ctr() + "%");
            baseVo.setNative_msg_ctr(baseVo.getNative_msg_ctr() + "%");
            baseVo.setNative_plaque_ctr(baseVo.getNative_plaque_ctr() + "%");
            baseVo.setNative_splash_ctr(baseVo.getNative_splash_ctr() + "%");
            baseVo.setNative_new_banner_ctr(baseVo.getNative_new_banner_ctr()+"%");
            baseVo.setNative_new_plaque_ctr(baseVo.getNative_new_plaque_ctr() + "%");
            baseVo.setPlaque_video_ctr(baseVo.getPlaque_video_ctr() + "%");
            baseVo.setSuspend_icon_ctr(baseVo.getSuspend_icon_ctr() + "%");
            baseVo.setTotal_splash_ctr(baseVo.getTotal_splash_ctr() + "%");
            String avgnum = baseVo.getAvgnum();
            if (BlankUtils.isNumeric(avgnum)) {
                baseVo.setAvgnum(new BigDecimal(avgnum).multiply(new BigDecimal("100")).setScale(2,RoundingMode.HALF_UP) + "%");
            }
            //处理在线时长
            String dailyDuration = baseVo.getDaily_duration();
            baseVo.setDaily_duration(StringUtils.secondsToHHmmss(dailyDuration));

        });

        Map<String, List<UmengAdIncomeReportVo>> data = new HashMap<>();
        PageForList<UmengAdIncomeReportVo> arpuPager = new PageForList<>(start, limit, arpuList);
        PageForList<UmengAdIncomeReportVo> pvPager = new PageForList<>(start, limit, pvList);
        PageForList<UmengAdIncomeReportVo> ctrPager = new PageForList<>(start, limit, ctrList);

        data.put("arpu", arpuPager.getResultList());
        data.put("pv", pvPager.getResultList());
        data.put("ctr", ctrPager.getResultList());
        JSONObject ret = new JSONObject();
        ret.put("ret",1);
        ret.put("msg","success");
        ret.put("data",data);
        ret.put("arpuTotal", arpuList.size());
        ret.put("pvTotal", pvList.size());
        ret.put("ctrTotal", ctrList.size());
        return ret;
    }

    public void setState(List<UmengAdIncomeReportVo> inputList, UmengAdIncomeWarningVo vo){
        if(null != inputList && inputList.size() > 0){
            inputList.forEach(el -> {
                Map<String, String> byAppidChannel = appService.getStateByAppidChannel(el.getAppid(), el.getChannel(), vo.getState());
                if (null != byAppidChannel) {
                    el.setState(byAppidChannel.get("state"));
                }
            });
        }
    }

    @RequestMapping(value="newGameWarning", method={RequestMethod.POST})
    public Object newGameWarning(UmengAdIncomeWarningVo vo) {
        vo.setOrder_str(generateOrderStrParam(vo.getOrder_str()));
        vo.setGroup(generateGroupParam(vo.getGroup()));

        List<UmengAdIncomeReportVo> newGame = new ArrayList<>();

        String tableName = "umeng_ad_income";
        if ("2".equals(vo.getDataSource())){
            tableName = "oppo_ad_income";
        }
        vo.setTableName(tableName);
        int limit = vo.getLimit();
        int start = vo.getStart();

        vo.setAddNumCondition(" c.addnum > 100 ");

        HashMap<String, String> state = new HashMap<String, String>() {{put("state", vo.getState());}};
        vo.setCon_ac(appService.getAppChannelList(state));

        // 不分页告警数据
        List<UmengAdIncomeReportVo> list = appService.getUmengAdIncomeWarningList(vo);
        if (CollectionUtils.isEmpty(list)) {
            Map<String, List<UmengAdIncomeReportVo>> data = new HashMap<>();
            data.put("newGame", new ArrayList<>());
            return ReturnJson.success(data);
        }

        //取前三天新增小于50 数据
        String startDate = vo.getStart_date();
        String endDate = vo.getEnd_date();
        String threeBeforeStart = DateTime.parse(startDate,format).minusDays(3).toString("yyyy-MM-dd");
        String threeBeforeEnd = DateTime.parse(endDate,format).minusDays(1).toString("yyyy-MM-dd");
        vo.setAddNumCondition(" c.addnum >= 0");
        vo.setStart_date(threeBeforeStart);
        vo.setEnd_date(threeBeforeEnd);
        List<UmengAdIncomeReportVo> before = appService.getUmengAdIncomeWarningList(vo);

        Map<String, UmengAdIncomeReportVo> threeDayBefore = before.stream().collect(Collectors
                .toMap(bef -> bef.getTdate() + "_" + bef.getAppkey() + "_" + bef.getChannel(),
                        bef -> bef,
                        (x,y)-> x));

        // 过滤出需要展示的数据
        List<UmengAdIncomeReportVo> newGameList = new ArrayList<>();
        list.forEach(t -> {
            String tdate = t.getTdate();
            String oneDate = DateTime.parse(tdate, format).minusDays(1).toString("yyyy-MM-dd");
            String twoDate = DateTime.parse(tdate, format).minusDays(2).toString("yyyy-MM-dd");
            String threeDate = DateTime.parse(tdate, format).minusDays(3).toString("yyyy-MM-dd");

            String onekey = oneDate + "_" + t.getAppkey() + "_" + t.getChannel();
            String twokey = twoDate + "_" + t.getAppkey() + "_" + t.getChannel();
            String threekey = threeDate + "_" + t.getAppkey() + "_" + t.getChannel();

//            if (threeDayBefore.containsKey(onekey) && threeDayBefore.containsKey(twokey) && threeDayBefore.containsKey(threekey)) {
//                newGameList.add(t);
//            }
            if (!threeDayBefore.containsKey(onekey) || !threeDayBefore.containsKey(twokey) || !threeDayBefore.containsKey(threekey)) {
                newGameList.add(t);
            } else if (Integer.parseInt(threeDayBefore.get(onekey).getAddnum()) < 50 ||
                    Integer.parseInt(threeDayBefore.get(twokey).getAddnum()) < 50 ||
                    Integer.parseInt(threeDayBefore.get(threekey).getAddnum()) < 50) {
                newGameList.add(t);
            }

        });

        if (CollectionUtils.isEmpty(newGameList)) {
            Map<String, List<UmengAdIncomeReportVo>> data = new HashMap<>();
            data.put("newGame", new ArrayList<>());
            JSONObject ret = new JSONObject();
            ret.put("ret",1);
            ret.put("msg","success, empty");
            ret.put("data",data);
            ret.put("newGameTotal", 0);
            return ret;
        }

        // 过滤出来的appid作为条件减少 记录
        String appids = newGameList.stream().map(UmengAdIncomeReportVo::getAppid).filter(BlankUtils::isNotBlank).reduce((x, y) -> x + "," + y).orElse("");
        vo.setAppid(appids);


        /* 保证原查询结果，增加筛选条件“增长率计算”，可选择周同比或日环比计算数据 -孙文凤.20240418 */
        Integer lastDay; // 使用日环比
        if ("week".equals(vo.getDimension())) { // 使用周同比
            lastDay = 7;
        } else {
            lastDay = 1;
        }
        // 调整策略为，全部使用日环比或全部使用周同比，不再区分字段来使用
        String before_start = DateTime.parse(startDate,format).minusDays(lastDay).toString("yyyy-MM-dd");
        String before_end = DateTime.parse(endDate,format).minusDays(lastDay).toString("yyyy-MM-dd");
        vo.setStart_date(before_start);
        vo.setEnd_date(before_end);
        List<UmengAdIncomeReportVo> beforeList = appService.getUmengAdIncomeWarningList(vo);

        String group = vo.getGroup();
        List<String> keyList = new ArrayList<>();
        if (!StringUtils.isEmpty(group)) {
            keyList.addAll(Stream.of("appkey", "channel").filter(group::contains).collect(Collectors.toList()));
        }
        try {
            CommonCompareUtils.compare(newGameList, beforeList, keyList, Arrays.asList(WARN_RATE_FIELDS.split(",")), UmengAdIncomeReportVo::getTdate, lastDay);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        Map<String, List<UmengAdIncomeReportVo>> data = new HashMap<>();
        newGameList.forEach(el -> {
            Map<String, String> byAppidChannel = appService.getStateByAppidChannel(el.getAppid(), el.getChannel(), vo.getState());
            if (null != byAppidChannel) {
                el.setState(byAppidChannel.get("state"));
            }
            el.setAll_total_ctr(el.getAll_total_ctr() + "%");
            el.setBanner_ctr(el.getBanner_ctr() + "%");
            el.setPlaque_ctr(el.getPlaque_ctr() + "%");
            el.setSplash_ctr(el.getSplash_ctr() + "%");
            el.setVideo_ctr(el.getVideo_ctr() + "%");
            el.setNative_banner_ctr(el.getNative_banner_ctr() + "%");
            el.setNative_msg_ctr(el.getNative_msg_ctr() + "%");
            el.setNative_plaque_ctr(el.getNative_plaque_ctr() + "%");
            el.setNative_splash_ctr(el.getNative_splash_ctr() + "%");
            el.setNative_new_banner_ctr(el.getNative_new_banner_ctr()+"%");
            el.setNative_new_plaque_ctr(el.getNative_new_plaque_ctr() + "%");
            el.setPlaque_video_ctr(el.getPlaque_video_ctr() + "%");
            el.setSuspend_icon_ctr(el.getSuspend_icon_ctr() + "%");
            String avgnum = el.getAvgnum();
            if (BlankUtils.isNumeric(avgnum)) {
                el.setAvgnum(new BigDecimal(avgnum).multiply(new BigDecimal("100")).setScale(2,RoundingMode.HALF_UP) + "%");
            }
            //处理在线时长
            String dailyDuration = el.getDaily_duration();
            el.setDaily_duration(StringUtils.secondsToHHmmss(dailyDuration));
        });
        PageForList<UmengAdIncomeReportVo> collectPager = new PageForList<>(start, limit, newGameList);

        data.put("newGame", collectPager.getResultList());
        JSONObject ret = new JSONObject();
        ret.put("ret",1);
        ret.put("msg","success");
        ret.put("data",data);
        ret.put("newGameTotal", newGameList.size());
        return ret;
    }

    /**
     * 找出冻结的广告位
     * <p>1. 冻结告警需求：
     * <p>针对新样式原生插屏、原生msg字段
     * <p>告警条件：dau>100，新样式原生插屏或原生msg广告前一天有数据，第二天为0的产品；
     * @param vo
     * @return
     */
    @RequestMapping(value="selectFreezeWarning", method={RequestMethod.POST})
    public Object selectFreezeWarning(UmengAdIncomeWarningVo vo) {
        vo.setOrder_str(generateOrderStrParam(vo.getOrder_str()));
        vo.setGroup(generateGroupParam(vo.getGroup()));

        String tableName = "umeng_ad_income";
        if ("2".equals(vo.getDataSource())){
            tableName = "oppo_ad_income";
        }
        vo.setTableName(tableName);
        int limit = vo.getLimit();
        int start = vo.getStart();

        vo.setAddNumCondition(" c.actnum > 100 ");

        HashMap<String, String> state = new HashMap<String, String>() {{put("state", vo.getState());}};
        vo.setCon_ac(appService.getAppChannelList(state));

        // 不分页告警数据
        List<UmengAdIncomeReportVo> list = appService.getUmengAdIncomeWarningList(vo);
        Map<String, UmengAdIncomeReportVo> todayMap = list.stream().collect(Collectors
                .toMap(bef -> bef.getTdate() + "_" + bef.getAppkey() + "_" + bef.getChannel(),
                        bef -> bef,
                        (x,y)-> x));
        if (CollectionUtils.isEmpty(list)) {
            Map<String, List<UmengAdIncomeReportVo>> data = new HashMap<>();
            data.put("freeze", new ArrayList<>());
            JSONObject ret = new JSONObject();
            ret.put("ret",1);
            ret.put("msg","today no data");
            ret.put("data",data);
            ret.put("freezeTotal", 0);
            return ret;
        }

        //取前一天数据
        String startDate = vo.getStart_date();
        String endDate = vo.getEnd_date();
        String beforeStart = DateTime.parse(startDate,format).minusDays(1).toString("yyyy-MM-dd");
        String beforeEnd = DateTime.parse(endDate,format).minusDays(1).toString("yyyy-MM-dd");
        vo.setStart_date(beforeStart);
        vo.setEnd_date(beforeEnd);
        List<UmengAdIncomeReportVo> before = appService.getUmengAdIncomeWarningList(vo);

        /*Map<String, UmengAdIncomeReportVo> oneDayBefore = before.stream().collect(Collectors
                .toMap(bef -> bef.getTdate() + "_" + bef.getAppkey() + "_" + bef.getChannel(),
                        bef -> bef,
                        (x,y)-> x));*/

        // 过滤出需要展示的数据, 前一天有值，当天没值
        List<UmengAdIncomeReportVo> freezeList = new ArrayList<>();
        before.forEach(t -> {
            String tdate = t.getTdate();
            String oneDate = DateTime.parse(tdate, format).minusDays(-1).toString("yyyy-MM-dd");

            String onekey = oneDate + "_" + t.getAppkey() + "_" + t.getChannel();

            UmengAdIncomeReportVo e = todayMap.get(onekey);
            if (!todayMap.containsKey(onekey)) {

//                freezeList.add(t);
//            } else if ( (BlankUtils.getDouble(t.getNative_new_plaque_pv()) > 0
//                    && BlankUtils.getDouble(e.getNative_new_plaque_pv()) == 0)
//                    ||
//                    (BlankUtils.getDouble(t.getNative_msg_pv()) > 0
//                    && BlankUtils.getDouble(e.getNative_msg_pv()) == 0)
//            || ((BlankUtils.getDouble(t.getNative_banner_pv()) > 0
//                    && BlankUtils.getDouble(e.getNative_banner_pv()) == 0))) {

            }else{
                /* 原生广告位冻结预警条件变更：对比前一天降幅大于等于50%时告警 -孙文凤.20231130 */
                double before_plaque_pv = BlankUtils.getDouble(t.getNative_new_plaque_pv());
                double today_plaque_pv = BlankUtils.getDouble(e.getNative_new_plaque_pv());
                boolean match = (today_plaque_pv > 1 && ((before_plaque_pv-today_plaque_pv)/before_plaque_pv*100) >= 50);

                double before_msg_pv = BlankUtils.getDouble(t.getNative_msg_pv());
                double today_msg_pv = BlankUtils.getDouble(e.getNative_msg_pv());
                boolean match2 = (today_msg_pv > 1 && ((before_msg_pv-today_msg_pv)/before_msg_pv*100) >= 50);

                double before_banner_pv = BlankUtils.getDouble(t.getNative_banner_pv());
                double today_banner_pv = BlankUtils.getDouble(e.getNative_banner_pv());
                boolean match3 = (today_banner_pv > 1 && ((before_banner_pv-today_banner_pv)/before_banner_pv*100) >= 50);
                if(match || match2 || match3){

                    freezeList.add(e);
                    e.setBefore_native_new_plaque_pv(t.getNative_new_plaque_pv());
                    e.setBefore_native_msg_pv(t.getNative_msg_pv());
                    e.setBefore_native_banner_pv(t.getNative_banner_pv());
                }

            }
        });

        if (CollectionUtils.isEmpty(freezeList)) {
            Map<String, List<UmengAdIncomeReportVo>> data = new HashMap<>();
            data.put("freeze", new ArrayList<>());
            JSONObject ret = new JSONObject();
            ret.put("ret",1);
            ret.put("msg","success, empty");
            ret.put("data",data);
            ret.put("freezeTotal", 0);
            return ret;
        }

        // 过滤出来的appid作为条件减少 记录
        String appids = freezeList.stream().map(UmengAdIncomeReportVo::getAppid).reduce((x, y) -> x + "," + y).orElse("");
        vo.setAppid(appids);

        /* 保证原查询结果，增加筛选条件“增长率计算”，可选择周同比或日环比计算数据 -孙文凤.20240418 */
        Integer lastDay; // 使用日环比
        if ("week".equals(vo.getDimension())) { // 使用周同比
            lastDay = 7;
        } else {
            lastDay = 1;
        }
        // 调整策略为，全部使用日环比或全部使用周同比，不再区分字段来使用
        String before_start = DateTime.parse(startDate,format).minusDays(lastDay).toString("yyyy-MM-dd");
        String before_end = DateTime.parse(endDate,format).minusDays(lastDay).toString("yyyy-MM-dd");
        vo.setStart_date(before_start);
        vo.setEnd_date(before_end);
        List<UmengAdIncomeReportVo> beforeList = appService.getUmengAdIncomeWarningList(vo);

        /*Map<String, UmengAdIncomeReportVo> dayBefore = beforeList.stream().collect(Collectors
                .toMap(bef -> bef.getTdate() + "_" + bef.getAppkey() + "_" + bef.getChannel(),
                        bef -> bef,
                        (x,y)-> x));*/
        // 环比同比处理
        String group = vo.getGroup();
        List<String> keyList = new ArrayList<>();
        if (!StringUtils.isEmpty(group)) {
            keyList.addAll(Stream.of("appkey", "channel").filter(group::contains).collect(Collectors.toList()));
        }
        try {
            CommonCompareUtils.compare(freezeList, beforeList, keyList, Arrays.asList(WARN_RATE_FIELDS.split(",")), UmengAdIncomeReportVo::getTdate, lastDay);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        freezeList.forEach( t -> {
            t.setAll_total_ctr(t.getAll_total_ctr() + "%");
            t.setBanner_ctr(t.getBanner_ctr() + "%");
            t.setPlaque_ctr(t.getPlaque_ctr() + "%");
            t.setSplash_ctr(t.getSplash_ctr() + "%");
            t.setVideo_ctr(t.getVideo_ctr() + "%");
            t.setNative_banner_ctr(t.getNative_banner_ctr() + "%");
            t.setNative_msg_ctr(t.getNative_msg_ctr() + "%");
            t.setNative_plaque_ctr(t.getNative_plaque_ctr() + "%");
            t.setNative_splash_ctr(t.getNative_splash_ctr() + "%");
            t.setNative_new_banner_ctr(t.getNative_new_banner_ctr()+"%");
            t.setNative_new_plaque_ctr(t.getNative_new_plaque_ctr() + "%");
            t.setPlaque_video_ctr(t.getPlaque_video_ctr() + "%");
            t.setSuspend_icon_ctr(t.getSuspend_icon_ctr() + "%");
            // 环比同比处理
            //processData(t, dayBefore, dayBefore, lastDay);
            String avgnum = t.getAvgnum();
            if (BlankUtils.isNumeric(avgnum)) {
                t.setAvgnum(new BigDecimal(avgnum).multiply(new BigDecimal("100")).setScale(2,RoundingMode.HALF_UP) + "%");
            }
            //处理在线时长
            String dailyDuration = t.getDaily_duration();
            t.setDaily_duration(StringUtils.secondsToHHmmss(dailyDuration));
        });
        Map<String, List<UmengAdIncomeReportVo>> data = new HashMap<>();
        freezeList.forEach(el -> {
            Map<String, String> byAppidChannel = appService.getStateByAppidChannel(el.getAppid(), el.getChannel(), vo.getState());
            if (null != byAppidChannel) {
                el.setState(byAppidChannel.get("state"));
            }
        });
        PageForList<UmengAdIncomeReportVo> collectPager = new PageForList<>(start, limit, freezeList);

        data.put("freeze", collectPager.getResultList());
        JSONObject ret = new JSONObject();
        ret.put("ret",1);
        ret.put("msg","success");
        ret.put("data",data);
        ret.put("freezeTotal", freezeList.size());
        return ret;
    }

    @RequestMapping(value="chapingWarning/list", method={RequestMethod.POST})
    public Object chapingWarning(UmengAdIncomeWarningVo vo) {
        vo.setOrder_str(generateOrderStrParam(vo.getOrder_str()));
        vo.setGroup(generateGroupParam(vo.getGroup()));

        String tableName = "umeng_ad_income";
        if ("2".equals(vo.getDataSource())){
            tableName = "oppo_ad_income";
        }
        vo.setTableName(tableName);
        int limit = vo.getLimit();
        int start = vo.getStart();

        vo.setAddNumCondition(" c.actnum > 100 ");
        HashMap<String, String> state = new HashMap<String, String>() {{put("state", vo.getState());}};
        vo.setCon_ac(appService.getAppChannelList(state));

        // 不分页告警数据
        List<UmengAdIncomeReportVo> list = appService.getUmengAdIncomeWarningList(vo);
        if (CollectionUtils.isEmpty(list)) {
            Map<String, List<UmengAdIncomeReportVo>> data = new HashMap<>();
            data.put("chaping", new ArrayList<>());
            return ReturnJson.success(data);
        }

        //取前1天 数据
        String startDate = vo.getStart_date();
        String endDate = vo.getEnd_date();
        String threeBeforeStart = DateTime.parse(startDate,format).minusDays(1).toString("yyyy-MM-dd");
        vo.setAddNumCondition(" c.addnum >= 0");
        vo.setStart_date(threeBeforeStart);
        vo.setEnd_date(threeBeforeStart);
        List<UmengAdIncomeReportVo> before = appService.getUmengAdIncomeWarningList(vo);

        /*Map<String, UmengAdIncomeReportVo> threeDayBefore = before.stream().collect(Collectors
                .toMap(bef -> bef.getTdate() + "_" + bef.getAppkey() + "_" + bef.getChannel(),
                        bef -> bef,
                        (x,y)-> x));*/

        // 过滤出需要展示的数据
        List<UmengAdIncomeReportVo> chapingList = new ArrayList<>();
        list.forEach(t -> {
            Double plaquePv = BlankUtils.getDouble(t.getPlaque_pv());
            Double pv = BlankUtils.getDouble(t.getNative_new_plaque_pv());
            Double plaqueEcpm = BlankUtils.getDouble(t.getPlaque_ecpm());
            Double ecpm = BlankUtils.getDouble(t.getNative_new_plaque_ecpm());

            if (plaquePv > pv) {
                if (ecpm/plaqueEcpm > 1.5 && Math.abs(ecpm - plaqueEcpm) > 1) {
                    chapingList.add(t);
                }
            } else if (plaquePv < pv) {
                if (plaqueEcpm/ecpm > 1.5 && Math.abs(plaqueEcpm - ecpm) > 1) {
                    chapingList.add(t);
                }
            } else {
                if (ecpm/plaqueEcpm > 1.5 && Math.abs(ecpm - plaqueEcpm) > 1) {
                    chapingList.add(t);
                } else if (plaqueEcpm/ecpm > 1.5 && Math.abs(plaqueEcpm - ecpm) > 1) {
                    chapingList.add(t);
                }
            }

            t.setBanner_ctr(t.getBanner_ctr() + "%");
            t.setPlaque_ctr(t.getPlaque_ctr() + "%");
            t.setSplash_ctr(t.getSplash_ctr() + "%");
            t.setVideo_ctr(t.getVideo_ctr() + "%");
            t.setNative_banner_ctr(t.getNative_banner_ctr() + "%");
            t.setNative_msg_ctr(t.getNative_msg_ctr() + "%");
            t.setNative_plaque_ctr(t.getNative_plaque_ctr() + "%");
            t.setNative_splash_ctr(t.getNative_splash_ctr() + "%");
            t.setNative_new_banner_ctr(t.getNative_new_banner_ctr()+"%");
            t.setNative_new_plaque_ctr(t.getNative_new_plaque_ctr() + "%");
            t.setPlaque_video_ctr(t.getPlaque_video_ctr() + "%");
            t.setSuspend_icon_ctr(t.getSuspend_icon_ctr() + "%");

        });

        if (CollectionUtils.isEmpty(chapingList)) {
            Map<String, List<UmengAdIncomeReportVo>> data = new HashMap<>();
            data.put("chaping", new ArrayList<>());
            JSONObject ret = new JSONObject();
            ret.put("ret",1);
            ret.put("msg","success, empty");
            ret.put("data",data);
            ret.put("chapingTotal", 0);
            return ret;
        }

        // 过滤出来的appid作为条件减少 记录
        String appids = chapingList.stream().map(UmengAdIncomeReportVo::getAppid).reduce((x, y) -> x + "," + y).orElse("");
        vo.setAppid(appids);


        /* 保证原查询结果，增加筛选条件“增长率计算”，可选择周同比或日环比计算数据 -孙文凤.20240418 */
        Integer lastDay; // 使用日环比
        if ("week".equals(vo.getDimension())) { // 使用周同比
            lastDay = 7;
        } else {
            lastDay = 1;
        }
        // 调整策略为，全部使用日环比或全部使用周同比，不再区分字段来使用
        String before_start = DateTime.parse(startDate,format).minusDays(lastDay).toString("yyyy-MM-dd");
        String before_end = DateTime.parse(endDate,format).minusDays(lastDay).toString("yyyy-MM-dd");
        vo.setStart_date(before_start);
        vo.setEnd_date(before_end);
        List<UmengAdIncomeReportVo> beforeList = appService.getUmengAdIncomeWarningList(vo);

        Map<String, UmengAdIncomeReportVo> dayBefore = beforeList.stream().collect(Collectors
                .toMap(bef -> bef.getTdate() + "_" + bef.getAppkey() + "_" + bef.getChannel(),
                        bef -> bef,
                        (x,y)-> x));


        chapingList.forEach( t -> {

            // 环比同比处理
            processData(t, dayBefore, dayBefore, lastDay);
        });
        Map<String, List<UmengAdIncomeReportVo>> data = new HashMap<>();
        chapingList.forEach(el -> {
            Map<String, String> byAppidChannel = appService.getStateByAppidChannel(el.getAppid(), el.getChannel(), vo.getState());
            if (null != byAppidChannel) {
                el.setState(byAppidChannel.get("state"));
            }
        });
        PageForList<UmengAdIncomeReportVo> collectPager = new PageForList<>(start, limit, chapingList);

        data.put("chaping", collectPager.getResultList());
        JSONObject ret = new JSONObject();
        ret.put("ret",1);
        ret.put("msg","success");
        ret.put("data",data);
        ret.put("chapingTotal", chapingList.size());
        return ret;
    }

    @RequestMapping(value="warning/export", method={RequestMethod.POST})
    public Object export(UmengAdIncomeWarningVo vo,
                         @ApiParam("导出表头字段") String value,
                         @ApiParam("选择导出的子表") int subTable,
                         HttpServletResponse response) {
        vo.setStart(1);
        vo.setLimit(50000);
        List<UmengAdIncomeReportVo> data = null;
        if (subTable == 4) {
            JSONObject o = (JSONObject) newGameWarning(vo);
            if (o.getIntValue("ret") != 1){
                return o;
            }
            data = ((Map<String, List<UmengAdIncomeReportVo>>) o.get("data")).get("newGame");
        } else if (subTable == 5) {
            JSONObject o = (JSONObject) selectFreezeWarning(vo);
            if (o.getIntValue("ret") != 1){
                return o;
            }
            data = ((Map<String, List<UmengAdIncomeReportVo>>) o.get("data")).get("freeze");
        } else if (subTable == 6) {
            JSONObject o = (JSONObject) chapingWarning(vo);
            if (o.getIntValue("ret") != 1){
                return o;
            }
            data = ((Map<String, List<UmengAdIncomeReportVo>>) o.get("data")).get("chaping");
        } else {
            JSONObject o = (JSONObject) warning(vo);
            if (o.getIntValue("ret") != 1){
                return o;
            }
            if (subTable == 1) {
                data = ((Map<String, List<UmengAdIncomeReportVo>>) o.get("data")).get("arpu");
            } else if (subTable == 2) {
                data = ((Map<String, List<UmengAdIncomeReportVo>>) o.get("data")).get("pv");
            } else if (subTable == 3) {
                data = ((Map<String, List<UmengAdIncomeReportVo>>) o.get("data")).get("ctr");
            }
        }

        if (data == null) {
            return ReturnJson.toErrorJson("no data to export");
        }

        // 导出的时候，带上状态的中文说明
        getState(data);

        // 导出
        Map<String, String> headerMap = extractHeader(value);
        ExportExcelUtil.exportXLSX2(response, data, headerMap, "渠道产品广告数据预警表.xlsx");
        return ReturnJson.success("success");
    }

    private void getState(List<UmengAdIncomeReportVo> data){
        HashMap<String, String> stateCode = new HashMap<String, String>() {{
            put("0", "未提交-0");
            put("1", "已上线-1");
            put("2", "已下线-2");
            put("3", "未发布-3");
            put("4", "自动化审核中-4");
            put("5", "审核中-5");
            put("6", "审核驳回-6");
            put("7", "定时发布-7");
            put("8", "资质审核-8");
            put("9", "资质驳回-9");
            put("10", "资质通过-10");
            put("11", "已冻结-11");
            put("12", "报备成功-12");
            put("13", "撤销上线-13");
            put("14", "测试中-14");
            put("15", "升级中-15");
            put("16", "下线审核-16");
            put("17", "未定义-17");
            put("18", "下线驳回-18");
            put("19", "审核通过-19");
            put("20", "运营打回-20");
            put("21", "运营通过-21");
            put("22", "其他-22");
        }};
        data.forEach(el -> {
            String stateId = el.getState();
            if(null != stateId && !"".equals(stateId.trim())){
                el.setState(stateCode.get(stateId));
            }
        });
    }

    /**
     * @Param t 返回数据
     * @Param sMap 前一天数据
     * @Param tMap 前一周数据
     * */
    private void processData(UmengAdIncomeReportVo t, Map<String, UmengAdIncomeReportVo> sMap, Map<String, UmengAdIncomeReportVo> tMap, Integer lastDay) {
        String tdate = t.getTdate();
        String btdate = DateTime.parse(tdate,format).minusDays(1).toString("yyyy-MM-dd");
        String ttdate = DateTime.parse(tdate,format).minusDays(7).toString("yyyy-MM-dd");
        if(lastDay != null){ // 使用指定的日或周
            btdate = DateTime.parse(tdate,format).minusDays(lastDay).toString("yyyy-MM-dd");
            ttdate = DateTime.parse(tdate,format).minusDays(lastDay).toString("yyyy-MM-dd");
        }
//        System.out.println("t:"+JSON.toJSONString(t));

        String key = btdate+"_"+t.getAppkey()+"_"+t.getChannel();
        String tkey = ttdate+"_"+t.getAppkey()+"_"+t.getChannel();

        String avgnum = t.getAvgnum();
        if (BlankUtils.isNumeric(avgnum)) {
            t.setAvgnum(new BigDecimal(avgnum)
                    .multiply(new BigDecimal("100"))
                    .setScale(2,RoundingMode.HALF_UP) + "%");
        }
        //处理在线时长
        String dailyDuration = t.getDaily_duration();
        t.setDaily_duration(StringUtils.secondsToHHmmss(dailyDuration));
        //默认环比增长为0.00%
        t.setDau_arpu_rate("0.00%");
        t.setBanner_ecpm_rate("0.00%");
        t.setNative_banner_ecpm_rate("0.00%");

        t.setPlaque_video_ecpm_rate("0.00%");
        t.setNative_new_plaque_ecpm_rate("0.00%");
        t.setNative_msg_ecpm_rate("0.00%");
        t.setNative_splash_ecpm_rate("0.00%");
        t.setSuspend_icon_ecpm_rate("0.00%");
        t.setSplash_ecpm_rate("0.00%");
        t.setSystem_splash_ecpm_rate("0.00%");
        t.setNative_plaque_ecpm_rate("0.00%");
        t.setVideo_ecpm_rate("0.00%");
        t.setPlaque_ecpm_rate("0.00%");
        t.setNative_new_banner_ecpm_rate("0.00%");
        t.setNative_new_plaque_pv_rate("0.00%");
        t.setNative_msg_pv_rate("0.00%");
        t.setNative_banner_pv_rate("0.00%");

        t.setAvgNumRate("0.00%");
        t.setDailyDurationRate("0.00%");

        String tAvgNum = "";
        String tDailyDuration = "";

        String ndau_arpu = "";
        String nbanner_ecpm = "";
        String nnative_banner_ecpm = "";
        String nplaque_video_ecpm = "";
        String nnative_new_plaque_ecpm = "";
        String nnative_msg_ecpm = "";
        String nnative_splash_ecpm = "";
        String nsuspend_icon_ecpm = "";
        String nsplash_ecpm = "";
        String nsystem_splash_ecpm = "";
        String nnative_plaque_ecpm = "";
        String nvideo_ecpm = "";
        String nplaque_ecpm = "";
        String nnative_new_banner_ecpm = "";
        String nnative_new_plaque_pv = "";
        String nnative_msg_pv = "";
        String nnavive_banner_pv = "";

        UmengAdIncomeReportVo vo = sMap.get(key);
        if (vo!=null){
            ndau_arpu = vo.getDau_arpu();
            nbanner_ecpm = vo.getBanner_ecpm();
            nnative_banner_ecpm = vo.getNative_banner_ecpm();
            nplaque_video_ecpm = vo.getPlaque_video_ecpm();
            nnative_new_plaque_ecpm = vo.getNative_new_plaque_ecpm();
            nnative_msg_ecpm = vo.getNative_msg_ecpm();
            nnative_splash_ecpm = vo.getNative_splash_ecpm();
            nsuspend_icon_ecpm = vo.getSuspend_icon_ecpm();
            nsplash_ecpm = vo.getSplash_ecpm();
            nsystem_splash_ecpm = vo.getSystem_splash_ecpm();
            nnative_plaque_ecpm = vo.getNative_plaque_ecpm();
            nvideo_ecpm = vo.getVideo_ecpm();
            nplaque_ecpm = vo.getPlaque_ecpm();
            nnative_new_banner_ecpm = vo.getNative_new_banner_ecpm();
            nnative_new_plaque_pv = vo.getNative_new_plaque_pv();
            nnative_msg_pv = vo.getNative_msg_pv();
            nnavive_banner_pv = vo.getNative_banner_pv();
        }
        if (!BlankUtils.checkBlank(nnavive_banner_pv)){
            t.setNative_banner_pv_rate(strToTwoNumber(t.getNative_banner_pv(),nnavive_banner_pv));
        }
        if (!BlankUtils.checkBlank(nnative_new_plaque_pv)){
            t.setNative_new_plaque_pv_rate(strToTwoNumber(t.getNative_new_plaque_pv(),nnative_new_plaque_pv));
        }
        if (!BlankUtils.checkBlank(nnative_msg_pv)){
            t.setNative_msg_pv_rate(strToTwoNumber(t.getNative_msg_pv(),nnative_msg_pv));
        }
        if (!BlankUtils.checkBlank(ndau_arpu)){
            t.setDau_arpu_rate(strToTwoNumber(t.getDau_arpu(),ndau_arpu));
        }
        if (!BlankUtils.checkBlank(nbanner_ecpm)){
            t.setBanner_ecpm_rate(strToTwoNumber(t.getBanner_ecpm(),nbanner_ecpm));
        }
        if (!BlankUtils.checkBlank(nnative_banner_ecpm)){
            t.setNative_banner_ecpm_rate(strToTwoNumber(t.getNative_banner_ecpm(),nnative_banner_ecpm));
        }
        if (!BlankUtils.checkBlank(nplaque_video_ecpm)){
            t.setPlaque_video_ecpm_rate(strToTwoNumber(t.getPlaque_video_ecpm(),nplaque_video_ecpm));
        }
        if (!BlankUtils.checkBlank(nnative_new_plaque_ecpm)){
            t.setNative_new_plaque_ecpm_rate(strToTwoNumber(t.getNative_new_plaque_ecpm(),nnative_new_plaque_ecpm));
        }
        if (!BlankUtils.checkBlank(nnative_msg_ecpm)){
            t.setNative_msg_ecpm_rate(strToTwoNumber(t.getNative_msg_ecpm(),nnative_msg_ecpm));
        }
        if (!BlankUtils.checkBlank(nnative_splash_ecpm)){
            t.setNative_splash_ecpm_rate(strToTwoNumber(t.getNative_splash_ecpm(),nnative_splash_ecpm));
        }
        if (!BlankUtils.checkBlank(nsuspend_icon_ecpm)){
            t.setSuspend_icon_ecpm_rate(strToTwoNumber(t.getSuspend_icon_ecpm(),nsuspend_icon_ecpm));
        }
        if (!BlankUtils.checkBlank(nsplash_ecpm)){
            t.setSplash_ecpm_rate(strToTwoNumber(t.getSplash_ecpm(),nsplash_ecpm));
        }
        if (!BlankUtils.checkBlank(nsystem_splash_ecpm)){
            t.setSystem_splash_ecpm_rate(strToTwoNumber(t.getSystem_splash_ecpm(),nsystem_splash_ecpm));
        }
        if (!BlankUtils.checkBlank(nnative_plaque_ecpm)){
            t.setNative_plaque_ecpm_rate(strToTwoNumber(t.getNative_plaque_ecpm(),nnative_plaque_ecpm));
        }
        if (!BlankUtils.checkBlank(nvideo_ecpm)){
            t.setVideo_ecpm_rate(strToTwoNumber(t.getVideo_ecpm(),nvideo_ecpm));
        }
        if (!BlankUtils.checkBlank(nplaque_ecpm)){
            t.setPlaque_ecpm_rate(strToTwoNumber(t.getPlaque_ecpm(),nplaque_ecpm));
        }
        if (!BlankUtils.checkBlank(nnative_new_banner_ecpm)){
            t.setNative_new_banner_ecpm_rate(strToTwoNumber(t.getNative_new_banner_ecpm(),nnative_new_banner_ecpm));
        }

        t.setTotal_banner_pv_rate("0.00%");
        t.setTotal_splash_pv_rate("0.00%");
        t.setTotal_video_pv_rate("0.00%");
        t.setTotal_plaque_pv_rate("0.00%");
        t.setTotal_suspend_icon_pv_rate("0.00%");
        t.setTotal_plaque_video_pv_rate("0.00%");
        t.setTotal_native_msg_pv_rate("0.00%");
        t.setAll_total_pv_rate("0.00%");

        t.setNative_new_plaque_ctr_rate("0.00%");
        t.setBanner_ctr_rate("0.00%");
        t.setPlaque_ctr_rate("0.00%");
        t.setSplash_ctr_rate("0.00%");
        t.setVideo_ctr_rate("0.00%");
        t.setNative_msg_ctr_rate("0.00%");
        t.setNative_banner_ctr_rate("0.00%");
        t.setNative_plaque_ctr_rate("0.00%");
        t.setNative_splash_ctr_rate("0.00%");
        t.setPlaque_video_ctr_rate("0.00%");
        t.setSuspend_icon_ctr_rate("0.00%");
        t.setNative_new_banner_ctr_rate("0.00%");
        t.setTotal_splash_ctr_rate("0.00%");



        String ttotal_banner_pv = "";
        String ttotal_splash_pv = "";
        String ttotal_video_pv = "";
        String ttotal_plaque_pv = "";
        String ttotal_suspend_icon_pv = "";
        String ttotal_plaque_video_pv = "";
        String ttotal_native_msg_pv = "";
        String tall_total_pv = "";

        String tNative_new_plaque_ctr = "";
        String tBanner_ctr = "";
        String tPlaque_ctr = "";
        String tSplash_ctr = "";
        String tVideo_ctr = "";
        String tNative_msg_ctr = "";
        String tNative_banner_ctr = "";
        String tNative_plaque_ctr = "";
        String tNative_splash_ctr = "";
        String tPlaque_video_ctr = "";
        String tSuspend_icon_ctr = "";
        String tNative_new_banner_ctr = "";
        //ctr-开屏汇总:开屏（系统开屏+开屏+原生开屏）
        String total_splash_ctr = "";


        UmengAdIncomeReportVo to = tMap.get(tkey);
        if (to!=null){
            ttotal_banner_pv = to.getTotal_banner_pv();
            ttotal_splash_pv = to.getTotal_splash_pv();
            ttotal_video_pv = to.getTotal_video_pv();
            ttotal_plaque_pv = to.getTotal_plaque_pv();
            ttotal_suspend_icon_pv = to.getTotal_suspend_icon_pv();
            ttotal_plaque_video_pv = to.getTotal_plaque_video_pv();
            ttotal_native_msg_pv = to.getTotal_native_msg_pv();
            tall_total_pv = to.getAll_total_pv();

            tNative_new_plaque_ctr = to.getNative_new_plaque_ctr();
            tBanner_ctr = to.getBanner_ctr();
            tPlaque_ctr = to.getPlaque_ctr();
            tSplash_ctr = to.getSplash_ctr();
            tVideo_ctr = to.getVideo_ctr();
            tNative_msg_ctr = to.getNative_msg_ctr();
            tNative_banner_ctr = to.getNative_banner_ctr();
            tNative_plaque_ctr = to.getNative_plaque_ctr();
            tNative_splash_ctr = to.getNative_splash_ctr();
            tPlaque_video_ctr = to.getPlaque_video_ctr();
            tSuspend_icon_ctr = to.getSuspend_icon_ctr();
            tNative_new_banner_ctr = to.getNative_banner_ctr();
            total_splash_ctr = to.getTotal_splash_ctr();

            tAvgNum = to.getAvgnum();
            tDailyDuration = to.getDaily_duration();
        }

        if (!BlankUtils.checkBlank(tAvgNum)){
            t.setAvgNumRate(strToTwoNumber(avgnum,tAvgNum));
        }
        if (!BlankUtils.checkBlank(tDailyDuration)){
            t.setDailyDurationRate(strToTwoNumber(dailyDuration,tDailyDuration));
        }

        if (!BlankUtils.checkBlank(ttotal_banner_pv)){
            t.setTotal_banner_pv_rate(strToTwoNumber(t.getTotal_banner_pv(),ttotal_banner_pv));
        }
        if (!BlankUtils.checkBlank(ttotal_splash_pv)){
            t.setTotal_splash_pv_rate(strToTwoNumber(t.getTotal_splash_pv(),ttotal_splash_pv));
        }
        if (!BlankUtils.checkBlank(ttotal_video_pv)){
            t.setTotal_video_pv_rate(strToTwoNumber(t.getTotal_video_pv(),ttotal_video_pv));
        }
        if (!BlankUtils.checkBlank(ttotal_plaque_pv)){
            t.setTotal_plaque_pv_rate(strToTwoNumber(t.getTotal_plaque_pv(),ttotal_plaque_pv));
        }
        if (!BlankUtils.checkBlank(ttotal_suspend_icon_pv)){
            t.setTotal_suspend_icon_pv_rate(strToTwoNumber(t.getTotal_suspend_icon_pv(),ttotal_suspend_icon_pv));
        }
        if (!BlankUtils.checkBlank(ttotal_plaque_video_pv)){
            t.setTotal_plaque_video_pv_rate(strToTwoNumber(t.getTotal_plaque_video_pv(),ttotal_plaque_video_pv));
        }
        if (!BlankUtils.checkBlank(ttotal_native_msg_pv)){
            t.setTotal_native_msg_pv_rate(strToTwoNumber(t.getTotal_native_msg_pv(),ttotal_native_msg_pv));
        }
        if (!BlankUtils.checkBlank(tall_total_pv)){
            t.setAll_total_pv_rate(strToTwoNumber(t.getAll_total_pv(),tall_total_pv));
        }

        if(BlankUtils.isNotBlank(tNative_new_plaque_ctr)) {
            t.setNative_new_plaque_ctr_rate(strToTwoNumber(t.getNative_new_plaque_ctr(), tNative_new_plaque_ctr));
        }
        if(BlankUtils.isNotBlank(tBanner_ctr)) {
            t.setBanner_ctr_rate(strToTwoNumber(t.getBanner_ctr(), tBanner_ctr));
        }
        if(BlankUtils.isNotBlank(tPlaque_ctr)) {
            t.setPlaque_ctr_rate(strToTwoNumber(t.getPlaque_ctr(), tPlaque_ctr));

        }
        if(BlankUtils.isNotBlank(tSplash_ctr)) {
            t.setSplash_ctr_rate(strToTwoNumber(t.getSplash_ctr(), tSplash_ctr));

        }
        if(BlankUtils.isNotBlank(tVideo_ctr)) {
            t.setVideo_ctr_rate(strToTwoNumber(t.getVideo_ctr(), tVideo_ctr));

        }
        if(BlankUtils.isNotBlank(tNative_msg_ctr)) {
            t.setNative_msg_ctr_rate(strToTwoNumber(t.getNative_msg_ctr(), tNative_msg_ctr));

        }
        if(BlankUtils.isNotBlank(tNative_banner_ctr)) {
            t.setNative_banner_ctr_rate(strToTwoNumber(t.getNative_banner_ctr(), tNative_banner_ctr));

        }
        if(BlankUtils.isNotBlank(tNative_plaque_ctr)) {
            t.setNative_plaque_ctr_rate(strToTwoNumber(t.getNative_plaque_ctr(), tNative_plaque_ctr));

        }
        if(BlankUtils.isNotBlank(tNative_splash_ctr)) {
            t.setNative_splash_ctr_rate(strToTwoNumber(t.getNative_splash_ctr(), tNative_splash_ctr));

        }
        if(BlankUtils.isNotBlank(tPlaque_video_ctr)) {
            t.setPlaque_video_ctr_rate(strToTwoNumber(t.getPlaque_video_ctr(), tPlaque_video_ctr));

        }
        if(BlankUtils.isNotBlank(tSuspend_icon_ctr)) {
            t.setSuspend_icon_ctr_rate(strToTwoNumber(t.getSuspend_icon_ctr(), tSuspend_icon_ctr));

        }
        if(BlankUtils.isNotBlank(tNative_new_banner_ctr)) {
            t.setNative_new_banner_ctr_rate(strToTwoNumber(t.getNative_new_banner_ctr(), tNative_new_banner_ctr));

        }
        if (BlankUtils.isNotBlank(total_splash_ctr)) {
            t.setTotal_splash_ctr_rate(strToTwoNumber(t.getTotal_splash_ctr(), total_splash_ctr));
        }


        //人均pv相关字段补充增长率--20240909
        t.setPlaque_pv_rate("0.00%");
        t.setPlaque_video_pv_rate("0.00%");
        t.setBanner_pv_rate("0.00%");
        t.setNative_new_banner_pv_rate("0.00%");
        t.setVideo_pv_rate("0.00%");
        t.setSuspend_icon_pv_rate("0.00%");
        String tplaque_pv = "";
        String tplaque_video_pv = "";
        String tbanner_pv = "";
        String tnative_new_banner_pv = "";
        String tvideo_pv = "";
        String tsuspend_icon_pv = "";
        if (to!=null){
            tplaque_pv = to.getPlaque_pv();
            tplaque_video_pv = to.getPlaque_video_pv();
            tbanner_pv = to.getBanner_pv();
            tnative_new_banner_pv = to.getNative_new_banner_pv();
            tvideo_pv = to.getVideo_pv();
            tsuspend_icon_pv = to.getSuspend_icon_pv();
        }
        if (!BlankUtils.checkBlank(tplaque_pv)){
            t.setPlaque_pv_rate(strToTwoNumber(t.getPlaque_pv(),tplaque_pv));
            t.setPlaque_video_pv_rate(strToTwoNumber(t.getPlaque_video_pv(),tplaque_video_pv));
            t.setBanner_pv_rate(strToTwoNumber(t.getBanner_pv(),tbanner_pv));
            t.setNative_new_banner_pv_rate(strToTwoNumber(t.getNative_new_banner_pv(),tnative_new_banner_pv));
            t.setVideo_pv_rate(strToTwoNumber(t.getVideo_pv(),tvideo_pv));
            t.setSuspend_icon_pv_rate(strToTwoNumber(t.getSuspend_icon_pv(),tsuspend_icon_pv));
        }
    }

}
