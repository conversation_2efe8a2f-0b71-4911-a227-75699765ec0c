package com.wbgame.controller.advert.query.cash;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.wbgame.annotation.ControllerEnhancer;
import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.aop.ApiNeed;
import com.wbgame.aop.LoginCheck;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.common.SyncDataServiceHolder;
import com.wbgame.common.response.Result;
import com.wbgame.mapper.adb.DnwxBiAdtMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.adv2.reportEntity.ReportChina;
import com.wbgame.pojo.advert.oversea.BasePageResultResponse;
import com.wbgame.pojo.advert.oversea.BaseResult;
import com.wbgame.pojo.jettison.param.AppleRoiReportParam;
import com.wbgame.pojo.jettison.report.param.SpendReportParam;
import com.wbgame.pojo.param.CashPlatformDetailRequestParam;
import com.wbgame.pojo.response.DnChaCashTotalResponse;
import com.wbgame.service.AdService;
import com.wbgame.service.advert.oversea.CashPlatformDetailService;
import com.wbgame.service.syncdata.impl.OhayooCashSyncDataServiceImpl;
import com.wbgame.service.xyx.XyxService;
import com.wbgame.task.CashUpdateTask;
import com.wbgame.task.StatisticReportTask;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.HttpClientUtils;
import com.wbgame.utils.JxlUtil;
import jxl.read.biff.BiffException;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.alicp.jetcache.Cache.logger;
import static com.wbgame.common.constants.PlatformName.OHAYOO;

/**
 * @description: 变现平台明细表
 * @author: huangmb
 * @date: 2021/06/01
 **/
@CrossOrigin
@RestController
@RequestMapping("/advert/cashPlatformDetail")
public class CashPlatformDetailController {

    @Resource
    private SyncDataServiceHolder syncDataServiceHolder;
    @Resource
    private CashUpdateTask cashUpdateTask;

    @Autowired
    private AdService adService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private XyxService xyxService;

    /**
     * 变现平台明细表查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/list", method={RequestMethod.GET, RequestMethod.POST})
    public String list(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("dnappid");
        String cha_type = request.getParameter("cha_type");
        String cha_media = request.getParameter("cha_media");
        String cha_sub_launch = request.getParameter("cha_sub_launch");
        String cha_id = request.getParameter("cha_id");
        String agent = request.getParameter("agent");
        String placement_type = request.getParameter("placement_type");
        String ad_sid = request.getParameter("ad_sid");
        String detail_ad_sid = request.getParameter("detail_ad_sid");
        String country = request.getParameter("country");
        String open_type = request.getParameter("open_type");
        String out = request.getParameter("out");
        String sdk_code = request.getParameter("sdk_code");
        String sdk_appid = request.getParameter("sdk_appid");
        String appid_tag = request.getParameter("appid_tag");
        String appid_tag_rev = request.getParameter("appid_tag_rev");
        String order_str = request.getParameter("order_str");
        String groups = request.getParameter("groups");

        List<String> grouplist = new ArrayList<>();
        if(!"".equals(groups)) {
            grouplist = Arrays.asList(groups.split(","));//维度用groupList装起来
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {
            Map<String, Object> paramMap = new HashMap<String, Object>();

            if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
                start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
                end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            }
            paramMap.put("start_date", start_date);
            paramMap.put("end_date", end_date);
            paramMap.put("appid", appid);
            paramMap.put("cha_type", cha_type);
            paramMap.put("cha_media", cha_media);
            paramMap.put("cha_sub_launch", cha_sub_launch);
            paramMap.put("cha_id", cha_id);
            paramMap.put("agent", agent);
            paramMap.put("placement_type", placement_type);
            paramMap.put("ad_sid", ad_sid);
            paramMap.put("detail_ad_sid", detail_ad_sid);
            paramMap.put("country", country);
            paramMap.put("open_type", open_type);
            paramMap.put("out", out);
            paramMap.put("sdk_code", sdk_code);
            paramMap.put("sdk_appid", sdk_appid);
            paramMap.put("appid_tag", appid_tag);
            paramMap.put("appid_tag_rev", appid_tag_rev);
            if(grouplist.contains("ad_sid")){
                paramMap.put("ad_sid_group", "true");
            }
            paramMap.put("groups", grouplist);


            /* 处理中文及字母排序 */
            List<String> orderArr = Arrays.asList(new String[]{
                    "appname",
                    "app_category",
                    "cha_type",
                    "cha_media",
                    "cha_sub_launch",
                    "cha_id",
                    "agent",
                    "ad_sid",
                    "placement_type",
                    "open_type",
                    "country"
            });
            /* 在列表中的字段名，使用LOWER()进行修改 反之不进行处理 */
            if(!BlankUtils.checkBlank(order_str)) {
                for (String str : orderArr) {
                    if(order_str.contains(str)){
                        order_str = order_str.replace(str + " ", String.format("LOWER(%s)", str) + " ");
                    }
                }

                paramMap.put("order_str", order_str);
            }


            PageHelper.startPage(pageNo, pageSize);
            List<Map<String, Object>> list = adService.selectDnChaCashTotal(paramMap);
            list.forEach(act -> {
                act.put("date", act.get("date")+"");
                act.put("cpc", act.get("cpc")+"");
            });
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
            result.put("total", adService.selectDnChaCashTotalSum(paramMap));
        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 变现平台明细导出
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/export", method={RequestMethod.GET, RequestMethod.POST})
    public void export(HttpServletRequest request,HttpServletResponse response) {

        Map<String, String> headerMap = new LinkedHashMap<String, String>();

        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("dnappid");
        String cha_type = request.getParameter("cha_type");
        String cha_media = request.getParameter("cha_media");
        String cha_sub_launch = request.getParameter("cha_sub_launch");
        String cha_id = request.getParameter("cha_id");
        String agent = request.getParameter("agent");
        String placement_type = request.getParameter("placement_type");
        String ad_sid = request.getParameter("ad_sid");
        String country = request.getParameter("country");
        String open_type = request.getParameter("open_type");
        String out = request.getParameter("out");
        String sdk_code = request.getParameter("sdk_code");
        String sdk_appid = request.getParameter("sdk_appid");
        String appid_tag = request.getParameter("appid_tag");
        String appid_tag_rev = request.getParameter("appid_tag_rev");
        String order_str = request.getParameter("order_str");
        String groups = request.getParameter("groups");

        List<String> grouplist = new ArrayList<>();
        if(!"".equals(groups)) {
            grouplist = Arrays.asList(groups.split(","));//维度用groupList装起来
        }

        Map<String, Object> paramMap = new HashMap<String, Object>();
        if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
            start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        }
        paramMap.put("start_date", start_date);
        paramMap.put("end_date", end_date);
        paramMap.put("appid", appid);
        paramMap.put("cha_type", cha_type);
        paramMap.put("cha_media", cha_media);
        paramMap.put("cha_sub_launch", cha_sub_launch);
        paramMap.put("cha_id", cha_id);
        paramMap.put("agent", agent);
        paramMap.put("placement_type", placement_type);
        paramMap.put("ad_sid", ad_sid);
        paramMap.put("country", country);
        paramMap.put("open_type", open_type);
        paramMap.put("out", out);
        paramMap.put("sdk_code", sdk_code);
        paramMap.put("sdk_appid", sdk_appid);
        paramMap.put("appid_tag", appid_tag);
        paramMap.put("appid_tag_rev", appid_tag_rev);
        if(grouplist.contains("ad_sid")){
            paramMap.put("ad_sid_group", "true");
        }
        paramMap.put("groups", grouplist);

        /* 处理中文及字母排序 */
        List<String> orderArr = Arrays.asList(new String[]{
                "appname",
                "app_category",
                "cha_type",
                "cha_media",
                "cha_sub_launch",
                "cha_id",
                "agent",
                "ad_sid",
                "placement_type",
                "open_type",
                "country"
        });
        /* 在列表中的字段名，使用LOWER()进行修改 反之不进行处理 */
        if(!BlankUtils.checkBlank(order_str)) {
            for (String str : orderArr) {
                if(order_str.contains(str)){
                    order_str = order_str.replace(str + " ", String.format("LOWER(%s)", str) + " ");
                }
            }

            paramMap.put("order_str", order_str);
        }

        Map<String, Map<String, Object>> appMap = adService.getAppInfoMap();
        List<Map<String, Object>> contentList = adService.selectDnChaCashTotal(paramMap);
        contentList.forEach(act -> {
            act.put("date", act.get("date")+"");
            Map<String, Object> app = appMap.get(act.get("dnappid")+"");
            if(app != null) {
                act.put("gname", app.get("app_name") + "");
            }
            if(act.get("type") !=null) {
                if ("2".equals(act.get("type").toString())) {
                    act.put("type", "内容");
                } else {
                    act.put("type", "广告");
                }
            }
            if(act.get("out") !=null) {
                if ("1".equals(act.get("out").toString())) {
                    act.put("out", "应用外");
                } else {
                    act.put("out", "应用内");
                }
            }
        });

        //原有表头逻辑不变,自定义列改
        String value = BlankUtils.checkNull(request,"value");
        if (!BlankUtils.checkBlank(value)){
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        //ExportExcelUtil.export2(response,contentList,headerMap,"变现平台明细_" + DateTime.now().toString("yyyyMMdd")+ ".xls");

        String fileName = "变现平台明细_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX(response,contentList,headerMap,fileName);
    }

    /**
     * 同步变现平台明细数据到财务（预估收入明细查询）
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/syn", method={RequestMethod.GET,RequestMethod.POST})
    public String syn(String startDay, String endDay, HttpServletRequest request, HttpServletResponse response) {
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
           return ReturnJson.error(Constants.ErrorToken);
        }else{
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        Integer wait = (Integer)redisTemplate.opsForValue().get("synDnChaCashTotal");
        if(wait!=null && wait ==1){
            return "{\"ret\":0,\"msg\":\"有数据同步中，请稍后再同步\"}";
        }
        // 同步日期距离当前相差90天，则需要特殊审核
        DateTime sDay = DateTime.parse(startDay);
        DateTime today = DateTime.now();
        int days = Days.daysBetween(sDay, today).getDays();
        if(days > 75){
        	return "{\"ret\":0,\"msg\":\"同步日期距今过久，请联系管理员确认覆盖!\"}";
        }

        try {
            redisTemplate.opsForValue().set("synDnChaCashTotal",1);
            boolean flag = adService.syncDnChaCashTotal(startDay.replace("-", ""),endDay.replace("-", ""));
            boolean flag2 = false;
            String url = "http://adt.vigame.cn/api/invoking/all/china?startDay="+startDay+"&endDay="+endDay;
            String httpGet = HttpClientUtils.getInstance().httpGet(url);
            if(httpGet != null && 200 == JSONObject.parseObject(httpGet).getIntValue("code")){
                flag2 = true;
            }

            if(flag && flag2) {
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            }else {
                return "{\"ret\":1,\"msg\":\"同步失败，请稍候重试!\"}";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":1,\"msg\":\"操作失败!\"}";
        } finally {
            redisTemplate.delete("synDnChaCashTotal");
        }
    }

    /**
     * 同步变现平台明细数据到财务（预估收入明细查询）
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/synFinance", method={RequestMethod.GET,RequestMethod.POST})
    public String synFinance(String startDay, String endDay, HttpServletRequest request, HttpServletResponse response) {
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        }else{
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        Integer wait = (Integer)redisTemplate.opsForValue().get("synFinance");
        if(wait!=null && wait ==1){
            return "{\"ret\":0,\"msg\":\"有数据同步中，请稍后再同步\"}";
        }
        // 同步日期距离当前相差90天，则需要特殊审核
        DateTime sDay = DateTime.parse(startDay);
        DateTime today = DateTime.now();
        int days = Days.daysBetween(sDay, today).getDays();
        if(days > 75){
            return "{\"ret\":0,\"msg\":\"同步日期距今过久，请联系管理员确认覆盖!\"}";
        }

        int flag = 0;
        try {
            redisTemplate.opsForValue().set("synFinance",1);
            String delsql = "DELETE FROM finance_agent_income where date BETWEEN '"+startDay+"' AND '"+endDay+"' ";
            adService.execSql(delsql);

            String sql = "insert INTO finance_agent_income(date,member_id,dnappid,agent,revenue) "+
                    "select date,member_id,IF(dnappid is null or dnappid = 0,15265,dnappid),agent,SUM(revenue) revenue from dn_cha_cash_total "+
                    "where date BETWEEN '"+startDay+"' AND '"+endDay+"' and ((agent = 'GDT' and app_id !=0) or (agent != 'GDT') ) "+
                    "GROUP BY date,member_id,dnappid,agent "+
                    "ON DUPLICATE KEY UPDATE revenue = VALUES(revenue)";
            flag = adService.execSql(sql);
            // 请求接口
            if(flag>0) {
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            }else {
                return "{\"ret\":0,\"msg\":\"同步失败，请稍候重试!\"}";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        } finally {
            redisTemplate.delete("synFinance");
        }
    }

    @RequestMapping(value="/synXyx", method={RequestMethod.GET,RequestMethod.POST})
    public String synXyx(String startDay, String endDay, HttpServletRequest request) {
        // 同步日期距离当前相差90天，则需要特殊审核
        DateTime sDay = DateTime.parse(startDay);
        DateTime today = DateTime.now();
        int days = Days.daysBetween(sDay, today).getDays();
        if(days > 75){
            return ReturnJson.toErrorJson("同步日期距今过久，请联系管理员确认覆盖!");
        }
        xyxService.synXyxToken(startDay,endDay);
        return ReturnJson.success();
    }


    @ControllerLoggingEnhancer
    @PostMapping("/business/batch")
    public String batchSyncData(@ApiNeed({
            "businesses",
            "startDate",
            "endDate"}) String businesses, String startDate, String endDate) {

        DateTime sDay = DateTime.parse(startDate);
        DateTime today = DateTime.now();
        DateTime eDay = DateTime.parse(endDate);
        int days = Days.daysBetween(sDay, today).getDays();
        // 貌似sigmob平台只有7天内的数据可查
        if(days > 75){
            return ReturnJson.toErrorJson("同步日期距今过久，请联系管理员确认覆盖!");
        }

        List<String> busArr = Lists.newArrayList(businesses.split(","));
        if (CollectionUtils.isEmpty(busArr)) {
            return ReturnJson.toErrorJson("no valid platform");
        }

        for (String s : busArr) {
            if (!syncDataServiceHolder.getBusinessMap().containsKey(s)) {
                return ReturnJson.toErrorJson(String.format("%s is not a platform", s));
            }
        }

        if (busArr.size() > 1 && Days.daysBetween(sDay, eDay).getDays() > 7) {
            return ReturnJson.toErrorJson("同步多个平台不可超过七天");
        }

        try {
            String ret = cashUpdateTask.lockedAsyncGetChinaReport(startDate, endDate, busArr, 1);
            if (BlankUtils.isNotBlank(ret)) {
                return ret;
            }
        } catch (Exception e) {
            return ReturnJson.toErrorJson(e.getMessage());
        }
        return ReturnJson.success();
    }

    @ControllerLoggingEnhancer
    @PostMapping("/douyin")
    public String douyinSyncData(@ApiNeed({
            "startDate",
            "endDate",
            "module"})  String startDate, String endDate, String module) {
        if (module.equals("total")) {
            try {
                return cashUpdateTask.lockedSyncDYXyxTotal(startDate, endDate);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        } else if (module.equals("statistic")) {
            try {
                return cashUpdateTask.lockedSyncDYXyxStatistic(startDate, endDate);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        } else if (module.equals("ohayoo")) {
            try {
                return cashUpdateTask.ohayooEstimate(startDate, endDate);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }else {
            try {
                return cashUpdateTask.lockedSyncDYXyx(startDate, endDate);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @ControllerLoggingEnhancer
    @PostMapping("/importOhayoo")
    public Object importOhayoo(@RequestParam(value = "fileName") MultipartFile file,HttpServletResponse response) throws BiffException, IOException {
        OhayooCashSyncDataServiceImpl ohayooCashSyncDataService
                = (OhayooCashSyncDataServiceImpl) syncDataServiceHolder.getBusinessMap().get(OHAYOO);
        return ohayooCashSyncDataService.ohayooSpecialImportProcess(file,response);
    }

    @Resource
    private DnwxBiAdtMapper dnwxBiAdtMapper;

    /**
     * 同步华为海外数据
     * @param date
     * @return
     */
    @GetMapping("/overseaSync")
    public String overseaSync(String date) {
        List<ReportChina> reportChinas = dnwxBiAdtMapper.selectCashTotalHuaweiOversea(date);
        return ReturnJson.success(reportChinas);
    }

    @Autowired
    private CashPlatformDetailService cashPlatformDetailService;

    /**
     * 变现平台明细表查询
     */
//    @Operation(summary = "变现平台明细表查询")
    @PostMapping(value = "/oversea/list")
    @LoginCheck
    public Result<BasePageResultResponse<DnChaCashTotalResponse>> list(@RequestBody CashPlatformDetailRequestParam param, HttpServletRequest request) {
        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        HashMap<String, String> map = BlankUtils.handleReportApps(loginUser, param.getDnappid(), param.getApp_category());
        if (BlankUtils.isNotBlank(map.get("appid"))) {
            param.setDnappid(map.get("appid"));
        }
        if (BlankUtils.isNotBlank(map.get("app_category"))) {
            param.setApp_category(map.get("app_category"));
        }
        return cashPlatformDetailService.selectCashPlatformDetails(param);
    }

    /**
     * 变现平台明细导出
     *
     * @param response
     */
//    @Operation(summary = "变现平台明细导出")
    @PostMapping(value = "/oversea/export")
    @LoginCheck
    public void export(@RequestBody CashPlatformDetailRequestParam param, HttpServletResponse response, HttpServletRequest request) {
        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        HashMap<String, String> map = BlankUtils.handleReportApps(loginUser, param.getDnappid(), param.getApp_category());
        if (BlankUtils.isNotBlank(map.get("appid"))) {
            param.setDnappid(map.get("appid"));
        }
        if (BlankUtils.isNotBlank(map.get("app_category"))) {
            param.setApp_category(map.get("app_category"));
        }
        cashPlatformDetailService.exportCashPlatformDetails(param, response);
    }

}
