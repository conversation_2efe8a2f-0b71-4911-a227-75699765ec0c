package com.wbgame.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.*;
import com.wbgame.pojo.custom.AdTotalHourTwoVo;
import com.wbgame.pojo.custom.AdvFeeVo;
import com.wbgame.pojo.custom.PageForList;
import com.wbgame.pojo.custom.Pager;
import com.wbgame.service.AdService;
import com.wbgame.service.PayService;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.JxlUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 移动游戏后台数据查接口
 *
 * <AUTHOR>
 */
@Controller
public class ApkSelectDataController {

    @Autowired
    SomeService someService;

    @Autowired
    AdService adService;

    @Autowired
    PayService payService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 实时新增查询
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectNewData", method = {RequestMethod.POST})
    public @ResponseBody
    String selectNewData(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            String start = BlankUtils.checkNull(request, "start");
            String limit = BlankUtils.checkNull(request, "limit");

            if (BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(cu.getToken());
            if (cuser == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            String projectid = BlankUtils.checkNull(request, "projectid");
            String mmid = BlankUtils.checkNull(request, "mmid");
            Map<String, String> parmMap = new HashMap<>();
            parmMap.put("projectid", projectid);
            parmMap.put("mmid", mmid);
            // pageStart 和 limit设置值
            int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // 当前页
            int pageNo = (pageStart / pageSize) + 1;
            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<UserNewCountVo> list = someService.selectUserNewCount(parmMap);
            long size = ((Page) list).getTotal();
            JSONObject result = new JSONObject();
            result.put("data", list);
            result.put("ret", 1);
            result.put("totalCount", size);
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    public void pagerToJsonString(HttpServletResponse response, Pager pager) {
        String result = JSONArray.toJSONString(pager.getResultList());
        String jsonString = "{'totalCount':" + pager.getTotalRows() + ",'root':" + result + "}";
        try {
            response.setCharacterEncoding("utf-8");
            PrintWriter out = response.getWriter();
            out.print(jsonString);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 游戏时长
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectGameTime", method = {RequestMethod.POST})
    public @ResponseBody
    String selectGameTime(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            if (BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(cu.getToken());
            if (cuser == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            String projectid = BlankUtils.checkNull(request, "projectid");
            String productid = BlankUtils.checkNull(request, "productid");
            String begin = BlankUtils.checkNull(request, "begin");
            String end = BlankUtils.checkNull(request, "end");
            Map<Object, Object> parmMap = new HashMap<>();
            parmMap.put("projectid", projectid);
            parmMap.put("productid", productid);
            parmMap.put("begin", begin);
            parmMap.put("end", end);
            List<ApkGameTimeVo> list = someService.selectApkGameTime(parmMap);
            int nSingleSum = 0;
            int oSingleSum = 0;
            int nDaySum = 0;
            int oDaySum = 0;
            for (ApkGameTimeVo apkGameTimeVo : list) {
                if (apkGameTimeVo.getNsingle() == null) {
                    apkGameTimeVo.setNsingle("0");
                }
                if (apkGameTimeVo.getOsingle() == null) {
                    apkGameTimeVo.setOsingle("0");
                }
                if (apkGameTimeVo.getNday() == null) {
                    apkGameTimeVo.setNday("0");
                }
                if (apkGameTimeVo.getOday() == null) {
                    apkGameTimeVo.setOday("0");
                }

                nSingleSum += Integer.valueOf(apkGameTimeVo.getNsingle());
                oSingleSum += Integer.valueOf(apkGameTimeVo.getOsingle());
                oDaySum += Integer.valueOf(apkGameTimeVo.getOday());
                nDaySum += Integer.valueOf(apkGameTimeVo.getNday());
            }
            Map<Object, Object> map = new HashMap<>();
            map.put("nSingleSum", nSingleSum);
            map.put("oSingleSum", oSingleSum);
            map.put("nDaySum", nDaySum);
            map.put("oDaySum", oDaySum);
            JSONObject result = new JSONObject();
            result.put("data", list);
            result.put("total", map);
            result.put("ret", 1);
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * api广告数据 1为展示数 2为点击数
     *
     * @param request
     * @param response
     */
    @CrossOrigin
    @RequestMapping(value = "/selectAdTotalReport", method = {RequestMethod.POST})
    @ResponseBody
    public String selectAdTotalReport(HttpServletRequest request, HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        String adtype = BlankUtils.checkNull(request, "adtype");

        if (adtype == null || adtype.isEmpty())
            adtype = "1";

        AdTotalHourTwoVo ad = new AdTotalHourTwoVo();
        ad.setAdtype(Integer.valueOf(adtype));
        List<AdTotalHourTwoVo> list = someService.selectAdTotalHour(ad);

        PageForList<AdTotalHourTwoVo> pager = new PageForList<AdTotalHourTwoVo>(0, 100, list);
        JSONObject result = new JSONObject();
        result.put("ret", 1);
        result.put("data", pager.getResultList());
        return result.toString();
    }

    /**
     * 新增用户 七日统计
     *
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectNewUserTotal", method = {RequestMethod.POST})
    @ResponseBody
    public String selectNewUserTotal(HttpServletRequest request, HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        String appid = BlankUtils.checkNull(request, "appid");
        String createtime = BlankUtils.checkNull(request, "createtime");
        String endtime = BlankUtils.checkNull(request, "endtime");
        String pid = BlankUtils.checkNull(request, "pid");

        try {
            JSONObject result = new JSONObject();
            //根据appid查询
            if (appid != null && appid.length() > 0) {
                Map<String, Object> map = new HashMap<>();
                map.put("appid", appid);
                map.put("beginDt", createtime);
                map.put("endDt", endtime);
                List<ApkUserTotalVo> list = someService.selectApkUserTrendLine(map);
                if (list != null && list.size() > 0) {
                    Map<String, Object> resultMap = new HashMap<>();
                    for (ApkUserTotalVo apkUserTotalVo : list) {
                        resultMap.put(apkUserTotalVo.getCreatetime(), apkUserTotalVo.getCurrNew());
                    }
                    result.put("ret", 1);
                    result.put("data", resultMap);
                }
            } else {
                Map<String, Object> map = new HashMap<>();
                map.put("pid", pid);
                map.put("createtime", createtime);
                map.put("endtime", endtime);
                List<ProdutChannelDataVo> list = someService.selectNewUserByPid(map);
                if (list != null && list.size() > 0) {
                    Map<String, Object> resultMap = new HashMap<>();
                    for (ProdutChannelDataVo produtChannelDataVo : list) {
                        resultMap.put(produtChannelDataVo.getBy_date(), produtChannelDataVo.getNewCount());
                    }
                    result.put("ret", 1);
                    result.put("data", resultMap);
                }
            }
            return result.toString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
            return result.toString();
        }
    }

    /**
     * 导出新增用户
     *
     * @param map
     * @param request
     * @param response
     */
    @CrossOrigin
    @RequestMapping(value = "/newUserToExcel", method = {RequestMethod.GET, RequestMethod.POST})
    public void newUserToExcel(ModelMap map, HttpServletRequest request, HttpServletResponse response) {
        //数据头
        Map<String, String> headerMap = new LinkedHashMap<>();

        //数据内容
        String appid = BlankUtils.checkNull(request, "appid");
        String createtime = BlankUtils.checkNull(request, "createtime");
        String endtime = BlankUtils.checkNull(request, "endtime");
        String pid = BlankUtils.checkNull(request, "pid");
        Map<String, String> inMap = new LinkedHashMap<>();
        List<Map<String, Object>> contentList = new ArrayList<>();
        if (appid != null && appid.length() > 0) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("appid", appid);
            paramMap.put("beginDt", createtime);
            paramMap.put("endDt", endtime);
            List<ApkUserTotalVo> list = someService.selectApkUserTrendLine(paramMap);
            inMap.put("strDt", "yyyy-MM-dd");
            Map<String, Object> contentMap;
            for (ApkUserTotalVo temp : list) {
                headerMap.put("tdate", "日期");
                headerMap.put("param1", "新增用户");

                contentMap = new LinkedHashMap<>();
                contentMap.put("tdate", temp.getCreatetime());
                contentMap.put("param1", temp.getCurrNew());

                contentList.add(contentMap);
            }
        } else {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("pid", pid);
            paramMap.put("createtime", createtime);
            paramMap.put("endtime", endtime);
            List<ProdutChannelDataVo> list = someService.selectNewUserByPid(paramMap);
            inMap.put("strDt", "yyyy-MM-dd");
            Map<String, Object> contentMap;
            for (ProdutChannelDataVo temp : list) {
                headerMap.put("tdate", "日期");
                headerMap.put("param1", "新增用户");

                contentMap = new LinkedHashMap<>();
                contentMap.put("tdate", temp.getBy_date());
                contentMap.put("param1", temp.getNewCount());

                contentList.add(contentMap);
            }
        }

        String fileName = "新增用户统计_" + DateTime.now().toString("yyyyMMdd") + ".xls";
        //获得指定文件的物理路径
        //String path = request.getRealPath(JxlUtil.DEFAULTDIR + "/" + fileName);
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);

    }

    /**
     * 活跃用户 七日统计
     *
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectCurrUserTotal", method = {RequestMethod.POST})
    @ResponseBody
    public String selectCurrUserTotal(HttpServletRequest request, HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        String appid = BlankUtils.checkNull(request, "appid");
        String createtime = BlankUtils.checkNull(request, "createtime");
        String endtime = BlankUtils.checkNull(request, "endtime");
        String pid = BlankUtils.checkNull(request, "pid");

        try {
            JSONObject result = new JSONObject();
            //根据appid查询
            if (appid != null && appid.length() > 0) {
                Map<String, Object> map = new HashMap<>();
                map.put("appid", appid);
                map.put("beginDt", createtime);
                map.put("endDt", endtime);
                List<ApkUserTotalVo> list = someService.selectApkUserTrendLine(map);
                if (list != null && list.size() > 0) {
                    Map<String, Map<String, Object>> resultMap = new HashMap<>();
                    Map<String, Object> dauResultMap = new HashMap<>();
                    Map<String, Object> newResultMap = new HashMap<>();
                    Map<String, Object> oldResultMap = new HashMap<>();
                    for (ApkUserTotalVo apkUserTotalVo : list) {
                        dauResultMap.put(apkUserTotalVo.getCreatetime(), apkUserTotalVo.getCurrDau());
                        newResultMap.put(apkUserTotalVo.getCreatetime(), apkUserTotalVo.getCurrNew());
                        oldResultMap.put(apkUserTotalVo.getCreatetime(), apkUserTotalVo.getCurrDau() - apkUserTotalVo.getCurrNew());
                        resultMap.put("dau", dauResultMap);
                        resultMap.put("new", newResultMap);
                        resultMap.put("old", oldResultMap);
                    }
                    result.put("ret", 1);
                    result.put("data", resultMap);
                }
            } else {
                Map<String, Object> map = new HashMap<>();
                map.put("pid", pid);
                map.put("createtime", createtime);
                map.put("endtime", endtime);
                List<ProdutChannelDataVo> list = someService.selectDauUserByPid(map);
                List<ProdutChannelDataVo> newlist = someService.selectNewUserByPid(map);
                Map<String, Map<String, Object>> resultMap = new HashMap<>();
                if (list != null && list.size() > 0) {

                    Map<String, Object> dauResultMap = new HashMap<>();
                    Map<String, Object> newResultMap = new HashMap<>();
                    Map<String, Object> oldResultMap = new HashMap<>();
                    for (ProdutChannelDataVo produtChannelDataVo : list) {
                        dauResultMap.put(produtChannelDataVo.getBy_date(), produtChannelDataVo.getDauCount());
                        newlist.stream().filter(newProdutChannelDataVo -> newProdutChannelDataVo.getBy_priid().equals(produtChannelDataVo.getBy_priid())
                                && newProdutChannelDataVo.getBy_date().equals(produtChannelDataVo.getBy_date())).forEach(newProdutChannelDataVo -> {
                            if (newProdutChannelDataVo.getNewCount() != null && Integer.valueOf(produtChannelDataVo.getDauCount()) >= Integer.valueOf(newProdutChannelDataVo.getNewCount())) {
                                oldResultMap.put(produtChannelDataVo.getBy_date(), Integer.valueOf(produtChannelDataVo.getDauCount()) - Integer.valueOf(newProdutChannelDataVo.getNewCount()));
                            } else {
                                oldResultMap.put(produtChannelDataVo.getBy_date(), 0);
                            }

                        });

                    }
                    for (ProdutChannelDataVo produtChannelDataVo : newlist) {
                        newResultMap.put(produtChannelDataVo.getBy_date(), produtChannelDataVo.getNewCount());
                    }
                    resultMap.put("new", newResultMap);
                    resultMap.put("dau", dauResultMap);
                    resultMap.put("old", oldResultMap);
                    result.put("ret", 1);
                    result.put("data", resultMap);
                }
            }
            return result.toString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
            return result.toString();
        }
    }



    /**
     * 导出活跃用户
     *
     * @param map
     * @param request
     * @param response
     */
    @CrossOrigin
    @RequestMapping(value = "/dauUserToExcel", method = {RequestMethod.GET, RequestMethod.POST})
    public void dauUserToExcel(ModelMap map, HttpServletRequest request, HttpServletResponse response) {
        //数据头
        Map<String, String> headerMap = new LinkedHashMap<>();

        //数据内容
        String appid = BlankUtils.checkNull(request, "appid");
        String createtime = BlankUtils.checkNull(request, "createtime");
        String endtime = BlankUtils.checkNull(request, "endtime");
        String pid = BlankUtils.checkNull(request, "pid");
        Map<String, String> inMap = new LinkedHashMap<>();
        List<Map<String, Object>> contentList = new ArrayList<>();
        if (appid != null && appid.length() > 0) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("appid", appid);
            paramMap.put("beginDt", createtime);
            paramMap.put("endDt", endtime);
            List<ApkUserTotalVo> list = someService.selectApkUserTrendLine(paramMap);
            Map<String, Object> contentMap;
            if (list != null && list.size() > 0) {
                for (ApkUserTotalVo temp : list) {
                    headerMap.put("tdate", "日期");
                    headerMap.put("param1", "活跃用户");
                    headerMap.put("param2", "新用户");
                    headerMap.put("param3", "老用户");

                    contentMap = new LinkedHashMap<>();
                    contentMap.put("tdate", temp.getCreatetime());
                    contentMap.put("param1", temp.getCurrDau());
                    contentMap.put("param2", temp.getCurrNew());
                    contentMap.put("param3", temp.getCurrDau() - temp.getCurrNew());

                    contentList.add(contentMap);
                }
            }
        } else {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("pid", pid);
            paramMap.put("createtime", createtime);
            paramMap.put("endtime", endtime);
            List<ProdutChannelDataVo> list = someService.selectDauUserByPid(map);
            List<ProdutChannelDataVo> newlist = someService.selectNewUserByPid(map);
            Map<String, Object> contentMap;
            if (list != null && list.size() > 0) {
                Map<String, Object> dauResultMap = new HashMap<>();
                Map<String, Object> newResultMap = new HashMap<>();
                Map<String, Object> oldResultMap = new HashMap<>();
                for (ProdutChannelDataVo produtChannelDataVo : list) {
                    dauResultMap.put(produtChannelDataVo.getBy_date(), produtChannelDataVo.getDauCount());
                    newlist.stream().filter(newProdutChannelDataVo -> newProdutChannelDataVo.getBy_priid().equals(produtChannelDataVo.getBy_priid())
                            && newProdutChannelDataVo.getBy_date().equals(produtChannelDataVo.getBy_date())).forEach(newProdutChannelDataVo -> {
                        if (newProdutChannelDataVo.getNewCount() != null && Integer.valueOf(produtChannelDataVo.getDauCount()) >= Integer.valueOf(newProdutChannelDataVo.getNewCount())) {
                            oldResultMap.put(produtChannelDataVo.getBy_date(), Integer.valueOf(produtChannelDataVo.getDauCount()) - Integer.valueOf(newProdutChannelDataVo.getNewCount()));
                        } else {
                            oldResultMap.put(produtChannelDataVo.getBy_date(), 0);
                        }
                    });
                }
                for (ProdutChannelDataVo produtChannelDataVo : newlist) {
                    newResultMap.put(produtChannelDataVo.getBy_date(), produtChannelDataVo.getNewCount());
                }
                for (ProdutChannelDataVo temp : list) {
                    headerMap.put("tdate", "日期");
                    headerMap.put("param1", "活跃用户");
                    headerMap.put("param2", "新用户");
                    headerMap.put("param3", "老用户");

                    contentMap = new LinkedHashMap<>();
                    contentMap.put("tdate", temp.getBy_date());
                    contentMap.put("param1", temp.getDauCount());
                    contentMap.put("param2", newResultMap.get(temp.getBy_date()));
                    contentMap.put("param3", oldResultMap.get(temp.getBy_date()));
                    contentList.add(contentMap);
                }
            }
        }
        String fileName = "活跃用户统计_" + DateTime.now().toString("yyyyMMdd") + ".xls";
        //获得指定文件的物理路径
        //String path = request.getRealPath(JxlUtil.DEFAULTDIR + "/" + fileName);
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
    }

    /**
     * 启动次数统计
     *
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectStartTotal", method = {RequestMethod.POST})
    @ResponseBody
    public String selectStartTotal(HttpServletRequest request, HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        String appid = BlankUtils.checkNull(request, "appid");
        String createtime = BlankUtils.checkNull(request, "createtime");
        String endtime = BlankUtils.checkNull(request, "endtime");
        String pid = BlankUtils.checkNull(request, "pid");

        try {
            JSONObject result = new JSONObject();
            //根据appid查询
            if (appid != null && appid.length() > 0) {
                Map<String, Object> map = new HashMap<>();
                map.put("appid", appid);
                map.put("beginDt", createtime);
                map.put("endDt", endtime);
                List<ApkUserTotalVo> list = someService.selectApkUserTrendLine(map);
                if (list != null && list.size() > 0) {
                    Map<String, Object> resultMap = new HashMap<>();
                    int sum = 0;
                    for (ApkUserTotalVo apkUserTotalVo : list) {
                        resultMap.put(apkUserTotalVo.getCreatetime(), apkUserTotalVo.getCurrStart());
                        sum += apkUserTotalVo.getCurrStart();
                    }
                    result.put("total", sum);
                    result.put("data", resultMap);
                }
                result.put("ret", 1);
            } else {
                Map<String, Object> map = new HashMap<>();
                map.put("pid", pid);
                map.put("createtime", createtime);
                map.put("endtime", endtime);
                List<ProdutChannelDataVo> list = someService.selectStartCountByPid(map);
                if (list != null && list.size() > 0) {
                    Map<String, Object> resultMap = new HashMap<>();
                    int sum = 0;
                    for (ProdutChannelDataVo produtChannelDataVo : list) {
                        resultMap.put(produtChannelDataVo.getBy_date(), produtChannelDataVo.getStartCount());
                        if (produtChannelDataVo.getStartCount() != null) {
                            sum += Integer.valueOf(produtChannelDataVo.getStartCount());
                        }
                    }
                    result.put("total", sum);
                    result.put("data", resultMap);
                }
                result.put("ret", 1);
            }
            return result.toString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
            return result.toString();
        }
    }

    /**
     * 导出启动次数
     *
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/startTotalToExcel", method = {RequestMethod.POST})
    @ResponseBody
    public void startTotalToExcel(HttpServletRequest request, HttpServletResponse response) {

        Map<String, String> headerMap = new LinkedHashMap<>();
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        String appid = BlankUtils.checkNull(request, "appid");
        String createtime = BlankUtils.checkNull(request, "createtime");
        String endtime = BlankUtils.checkNull(request, "endtime");
        String pid = BlankUtils.checkNull(request, "pid");

        //根据appid查询
        if (appid != null && appid.length() > 0) {
            Map<String, Object> map = new HashMap<>();
            map.put("appid", appid);
            map.put("beginDt", createtime);
            map.put("endDt", endtime);
            Map<String, String> inMap = new LinkedHashMap<>();
            List<Map<String, Object>> contentList = new ArrayList<>();
            List<ApkUserTotalVo> list = someService.selectApkUserTrendLine(map);
            Map<String, Object> contentMap;
            if (list != null && list.size() > 0) {
                for (ApkUserTotalVo temp : list) {
                    headerMap.put("tdate", "日期");
                    headerMap.put("param1", "启动次数");

                    contentMap = new LinkedHashMap<>();
                    contentMap.put("tdate", temp.getCreatetime());
                    contentMap.put("param1", temp.getCurrStart());

                    contentList.add(contentMap);
                }
            }
            String fileName = "启动次数统计_" + DateTime.now().toString("yyyyMMdd") + ".xls";
            //获得指定文件的物理路径
            //String path = request.getRealPath(JxlUtil.DEFAULTDIR + "/" + fileName);
            JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
        } else {
            Map<String, Object> map = new HashMap<>();
            map.put("pid", pid);
            map.put("createtime", createtime);
            map.put("endtime", endtime);
            Map<String, String> inMap = new LinkedHashMap<>();
            List<Map<String, Object>> contentList = new ArrayList<>();
            List<ProdutChannelDataVo> list = someService.selectStartCountByPid(map);
            Map<String, Object> contentMap;
            for (ProdutChannelDataVo temp : list) {
                headerMap.put("tdate", "日期");
                headerMap.put("param1", "新增用户");

                contentMap = new LinkedHashMap<>();
                contentMap.put("tdate", temp.getBy_date());
                contentMap.put("param1", temp.getStartCount());

                contentList.add(contentMap);
            }
            String fileName = "启动次数统计_" + DateTime.now().toString("yyyyMMdd") + ".xls";
            //获得指定文件的物理路径
            //String path = request.getRealPath(JxlUtil.DEFAULTDIR + "/" + fileName);
            JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
        }
    }


}