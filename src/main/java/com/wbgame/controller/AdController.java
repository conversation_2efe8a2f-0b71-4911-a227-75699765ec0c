package com.wbgame.controller;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.master.UserInfoMapper;
import com.wbgame.pojo.*;
import com.wbgame.pojo.custom.*;
import com.wbgame.pojo.custom.AdvFeeVo;
import com.wbgame.utils.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.DeleteObjectsRequest;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.cdn.model.v20180510.PushObjectCacheRequest;
import com.aliyuncs.cdn.model.v20180510.PushObjectCacheResponse;
import com.aliyuncs.cdn.model.v20180510.RefreshObjectCachesRequest;
import com.aliyuncs.cdn.model.v20180510.RefreshObjectCachesResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.profile.DefaultProfile;
import com.cloud.apigateway.sdk.utils.Client;
import com.cloud.apigateway.sdk.utils.Request;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.service.AdService;

import jxl.Sheet;
import jxl.Workbook;

/**
 * apk广告接口相关
 * <AUTHOR>
 */
@Controller
public class AdController {
	
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private AdService adService;
	@Autowired
	UserInfoMapper userInfoMapper;

	
	@CrossOrigin
	@RequestMapping(value="/ad/insertTouTiaoAdTotal", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String insertTouTiaoAdTotal(HttpServletRequest request,HttpServletResponse response){
		String tdate = request.getParameter("tdate");
			
		// 请求时带入签名参数
		long user_id = 913;
		long timestamp = DateTime.now().getMillis() / 1000;
		long nonce = new Random().nextInt();
		String key_secret = "9ebb8255d8bde59720fceb6554c41b5e";
		List<String> args = new ArrayList<String>();
		args.add(key_secret);
		args.add(timestamp + "");
		args.add(nonce + "");
		Collections.sort(args);
		String join = String.join("", args);
		String sign = DigestUtils.sha1Hex(join);

		String url = "http://ad.toutiao.com/union/media/open/api/report/user";
		String app = "http://ad.toutiao.com/union/media/open/api/report/app";
		String slot = "http://ad.toutiao.com/union/media/open/api/report/slot";
		Map<String, Object> paramsMap = new HashMap<String, Object>();
		paramsMap.put("user_id", user_id);
		paramsMap.put("timestamp", timestamp);
		paramsMap.put("nonce", nonce);
		paramsMap.put("sign", sign);
		paramsMap.put("start_date", tdate);
		paramsMap.put("end_date", tdate);
		paramsMap.put("time_granularity", "STAT_TIME_GRANULARITY_DAILY");
		String params = "";
		Iterator<Entry<String, Object>> iterator = paramsMap.entrySet().iterator();
		while(iterator.hasNext()){
			Entry<String, Object> next = iterator.next();
			params = params + "&"+next.getKey()+"="+next.getValue();
		}
		params = params.substring(1);
		
		try {
			String httpGet = HttpRequest.get(slot+"?"+params, null);
			String data = JSONObject.parseObject(httpGet).getString("data");
			List<TouTiaoAdVo> list = JSONArray.parseArray(data, TouTiaoAdVo.class);
			if(list != null && list.size() > 0){
				int total = adService.insertTouTiaoAdTotal(list);
				
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			}
			return "{\"ret\":2,\"msg\":\"没有获取到数据!\"}";
			
		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	// 渠道产品广告数据
	@CrossOrigin
	@RequestMapping(value="/ad/umengAdIncomeImportExcel", method={RequestMethod.GET,RequestMethod.POST})
	public void umengAdIncomeImportExcel(@RequestParam(value="file_name")MultipartFile file, 
			String tdate, String channel, HttpServletRequest request,HttpServletResponse response) throws IOException{
		try{
			response.setHeader("Access-Control-Allow-Origin", "*");
			response.setContentType("text/html;charset=utf-8");  
			response.setCharacterEncoding("utf-8");
			PrintWriter out = response.getWriter();
			JSONObject obj = new JSONObject();
			
			String token = request.getParameter("token");
			// token验证
			if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
				obj.put("ret", 2);
				obj.put("msg", "令牌无效!");
				out.print(obj.toString());
				out.close();
				return;
			}else{
				redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
			}
			
			if(null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")){
				Workbook workbook = Workbook.getWorkbook(file.getInputStream());
				Sheet sheet = workbook.getSheet(0);
				
				// 获取广告code对应的appkey的Map集合
				Map<String, Map<String, Object>> codeMap = new HashMap<String, Map<String, Object>>();
				String query = "select ad_code,appkey,adtype,status from umeng_adcode_list where channel = '"+channel+"'";
				List<Map<String, Object>> npMap = adService.queryListMap(query);
				for (Map<String, Object> map : npMap) {
					codeMap.put(map.get("ad_code").toString(), map);
				}
				// 获取appid 信息
				String appQuery = "select concat(umeng_key,'') as mapkey,app_name,id as appid from yyhz_0308.app_info ";
				Map<String, Map<String, Object>> appInfoMap =  adService.queryListMapOfKey(appQuery);
				
				// 通过appkey来聚集多条内容到一个应用中
				List<UmengAdIncomeVo> dataList = new ArrayList<UmengAdIncomeVo>();
				Map<String, UmengAdIncomeVo> valMap = new HashMap<String, UmengAdIncomeVo>();
				int column = sheet.getColumns();
				int row = sheet.getRows();
				for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
					if(BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
						continue;
					
					String[] vals = new String[4];
					for (int c = 0; c < 4; c++) {
						vals[c] = sheet.getCell(c, r).getContents();
					}
					// 得到广告位ID的map对象
					Map<String, Object> code = codeMap.get(vals[0]);
					if(code == null){
						obj.put("ret", 0);
						obj.put("msg", "广告位ID "+vals[0]+" 没有找到，请仔细核对！");
						out.print(obj.toString());
						out.close();
						return;
					}
					// code处于停用状态，不录入
					if("close".equals(code.get("status").toString()))
						continue;
					
					String appkey = code.get("appkey").toString();
					String adtype = code.get("adtype").toString();
					UmengAdIncomeVo ua = valMap.get(appkey);
					if(ua == null || ua.getAppkey() == null){
						// 初始化应用数据
						ua = new UmengAdIncomeVo();
						ua.setTdate(tdate);
						ua.setAppkey(appkey);
						ua.setChannel(channel);
						ua.setAdcode(vals[0]);
						
						ua.setBanner_show("0");
						ua.setPlaque_show("0");
						ua.setSplash_show("0");
						ua.setVideo_show("0");
						ua.setNative_banner_show("0");
						ua.setNative_plaque_show("0");
						ua.setNative_splash_show("0");
						ua.setPlaque_video_show("0");
						ua.setNative_msg_show("0");
						
						ua.setBanner_income("0");
						ua.setPlaque_income("0");
						ua.setSplash_income("0");
						ua.setVideo_income("0");
						ua.setNative_banner_income("0");
						ua.setNative_plaque_income("0");
						ua.setNative_splash_income("0");
						ua.setPlaque_video_income("0");
						ua.setNative_msg_income("0");
						
						ua.setBanner_ecpm("0");
						ua.setPlaque_ecpm("0");
						ua.setSplash_ecpm("0");
						ua.setVideo_ecpm("0");
						ua.setNative_banner_ecpm("0");
						ua.setNative_plaque_ecpm("0");
						ua.setNative_splash_ecpm("0");
						ua.setPlaque_video_ecpm("0");
						ua.setNative_msg_ecpm("0");

						ua.setNative_msg_click("0");
						ua.setPlaque_video_click("0");
						ua.setNative_splash_click("0");
						ua.setNative_plaque_click("0");
						ua.setNative_banner_click("0");
						ua.setVideo_click("0");
						ua.setSplash_click("0");
						ua.setPlaque_click("0");
						ua.setBanner_click("0");
					}
					
					// ***这里相同的一个应用数据，show和income数值需要叠加，并且ecpm需要自己计算***
					BigDecimal k = new BigDecimal(1000);
					if("banner".equals(adtype)){
						ua.setBanner_show(new BigDecimal(ua.getBanner_show())
							.add(new BigDecimal(vals[1])).toString());
						ua.setBanner_income(new BigDecimal(ua.getBanner_income())
							.add(new BigDecimal(vals[3])).toString());
						ua.setBanner_click((Integer.parseInt(ua.getBanner_click())+Integer.parseInt(vals[2]))+"");
						
						if(Integer.valueOf(ua.getBanner_show()) != 0 
								&& Double.valueOf(ua.getBanner_income()) != 0){
							
							ua.setBanner_ecpm(
								new BigDecimal(ua.getBanner_income())
								.multiply(k)
								.divide(new BigDecimal(ua.getBanner_show()), 2, RoundingMode.FLOOR)
								.toString()
							);
						}
						// 当值为小数时，截取小数点至后两位
						/*if(vals[3].indexOf(".") != -1 && vals[3].length() > (vals[3].indexOf(".")+2)){
							ua.setBanner_ecpm(vals[3].substring(0, vals[3].indexOf(".")+3));
						}*/
					}else if("plaque".equals(adtype)){
						ua.setPlaque_show(new BigDecimal(ua.getPlaque_show())
							.add(new BigDecimal(vals[1])).toString());
						ua.setPlaque_income(new BigDecimal(ua.getPlaque_income())
							.add(new BigDecimal(vals[3])).toString());
						ua.setPlaque_click((Integer.parseInt(ua.getPlaque_click())+Integer.parseInt(vals[2]))+"");

						if(Integer.valueOf(ua.getPlaque_show()) != 0 
								&& Double.valueOf(ua.getPlaque_income()) != 0){
							
							ua.setPlaque_ecpm(
								new BigDecimal(ua.getPlaque_income())
								.multiply(k)
								.divide(new BigDecimal(ua.getPlaque_show()), 2, RoundingMode.FLOOR)
								.toString()
							);
						}
						
					}else if("splash".equals(adtype)){
						ua.setSplash_show(new BigDecimal(ua.getSplash_show())
							.add(new BigDecimal(vals[1])).toString());
						ua.setSplash_income(new BigDecimal(ua.getSplash_income())
							.add(new BigDecimal(vals[3])).toString());
						ua.setSplash_click((Integer.parseInt(ua.getSplash_click())+Integer.parseInt(vals[2]))+"");

						if(Integer.valueOf(ua.getSplash_show()) != 0 
								&& Double.valueOf(ua.getSplash_income()) != 0){
							
							ua.setSplash_ecpm(
								new BigDecimal(ua.getSplash_income())
								.multiply(k)
								.divide(new BigDecimal(ua.getSplash_show()), 2, RoundingMode.FLOOR)
								.toString()
							);
						}
					}else if("video".equals(adtype)){
						ua.setVideo_show(new BigDecimal(ua.getVideo_show())
							.add(new BigDecimal(vals[1])).toString());
						ua.setVideo_income(new BigDecimal(ua.getVideo_income())
							.add(new BigDecimal(vals[3])).toString());
						ua.setVideo_click((Integer.parseInt(ua.getVideo_click())+Integer.parseInt(vals[2]))+"");

						if(Integer.valueOf(ua.getVideo_show()) != 0 
								&& Double.valueOf(ua.getVideo_income()) != 0){
							
							ua.setVideo_ecpm(
								new BigDecimal(ua.getVideo_income())
								.multiply(k)
								.divide(new BigDecimal(ua.getVideo_show()), 2, RoundingMode.FLOOR)
								.toString()
							);
						}
					}else if("nativebanner".equals(adtype)){
						ua.setNative_banner_show(new BigDecimal(ua.getNative_banner_show())
							.add(new BigDecimal(vals[1])).toString());
						ua.setNative_banner_income(new BigDecimal(ua.getNative_banner_income())
							.add(new BigDecimal(vals[3])).toString());
						ua.setNative_banner_click((Integer.parseInt(ua.getNative_banner_click())+Integer.parseInt(vals[2]))+"");


						if(Integer.valueOf(ua.getNative_banner_show()) != 0 
								&& Double.valueOf(ua.getNative_banner_income()) != 0){
							
							ua.setNative_banner_ecpm(
								new BigDecimal(ua.getNative_banner_income())
								.multiply(k)
								.divide(new BigDecimal(ua.getNative_banner_show()), 2, RoundingMode.FLOOR)
								.toString()
							);
						}
					}else if("nativeplaque".equals(adtype)){
						ua.setNative_plaque_show(new BigDecimal(ua.getNative_plaque_show())
							.add(new BigDecimal(vals[1])).toString());
						ua.setNative_plaque_income(new BigDecimal(ua.getNative_plaque_income())
							.add(new BigDecimal(vals[3])).toString());
						ua.setNative_plaque_click((Integer.parseInt(ua.getNative_plaque_click())+Integer.parseInt(vals[2]))+"");

						if(Integer.valueOf(ua.getNative_plaque_show()) != 0 
								&& Double.valueOf(ua.getNative_plaque_income()) != 0){
							
							ua.setNative_plaque_ecpm(
								new BigDecimal(ua.getNative_plaque_income())
								.multiply(k)
								.divide(new BigDecimal(ua.getNative_plaque_show()), 2, RoundingMode.FLOOR)
								.toString()
							);
						}
					}else if("nativesplash".equals(adtype)){
						ua.setNative_splash_show(new BigDecimal(ua.getNative_splash_show())
							.add(new BigDecimal(vals[1])).toString());
						ua.setNative_splash_income(new BigDecimal(ua.getNative_splash_income())
							.add(new BigDecimal(vals[3])).toString());
						ua.setNative_splash_click((Integer.parseInt(ua.getNative_splash_click())+Integer.parseInt(vals[2]))+"");

						if(Integer.valueOf(ua.getNative_splash_show()) != 0 
								&& Double.valueOf(ua.getNative_splash_income()) != 0){
							
							ua.setNative_splash_ecpm(
								new BigDecimal(ua.getNative_splash_income())
								.multiply(k)
								.divide(new BigDecimal(ua.getNative_splash_show()), 2, RoundingMode.FLOOR)
								.toString()
							);
						}
					}else if("plaquevideo".equals(adtype)){
						ua.setPlaque_video_show(new BigDecimal(ua.getPlaque_video_show())
								.add(new BigDecimal(vals[1])).toString());
							ua.setPlaque_video_income(new BigDecimal(ua.getPlaque_video_income())
								.add(new BigDecimal(vals[3])).toString());
						ua.setPlaque_video_click((Integer.parseInt(ua.getPlaque_video_click())+Integer.parseInt(vals[2]))+"");


						if(Integer.valueOf(ua.getPlaque_video_show()) != 0
									&& Double.valueOf(ua.getPlaque_video_income()) != 0){
								
								ua.setPlaque_video_ecpm(
									new BigDecimal(ua.getPlaque_video_income())
									.multiply(k)
									.divide(new BigDecimal(ua.getPlaque_video_show()), 2, RoundingMode.FLOOR)
									.toString()
								);
							}
							
					}else if("nativemsg".equals(adtype)){
						ua.setNative_msg_show(new BigDecimal(ua.getNative_msg_show())
								.add(new BigDecimal(vals[1])).toString());
							ua.setNative_msg_income(new BigDecimal(ua.getNative_msg_income())
								.add(new BigDecimal(vals[3])).toString());
						ua.setNative_msg_click((Integer.parseInt(ua.getNative_msg_click())+Integer.parseInt(vals[2]))+"");

						if(Integer.valueOf(ua.getNative_msg_show()) != 0
									&& Double.valueOf(ua.getNative_msg_income()) != 0){
								
								ua.setNative_msg_ecpm(
									new BigDecimal(ua.getNative_msg_income())
									.multiply(k)
									.divide(new BigDecimal(ua.getNative_msg_show()), 2, RoundingMode.FLOOR)
									.toString()
								);
							}
					}
					valMap.put(appkey, ua);
				}
				
				// 友盟key对应的活跃用户数据
				String sql = "select addnum,tdate,appname,appkey,actnum from umeng_channel_total"
						+ " where tdate = '"+tdate+"' and cname = '"+channel+"'";
				List<Map<String, Object>> appMap = adService.queryListMap(sql);
				
				Iterator<Entry<String, UmengAdIncomeVo>> iterator = valMap.entrySet().iterator();
				while(iterator.hasNext()){
					Entry<String, UmengAdIncomeVo> next = iterator.next();
					UmengAdIncomeVo value = next.getValue();
					
					boolean flag = true;
					// 展示收入内容的友盟key与数据库友盟key匹配，获取活跃用户数
					for (Map<String, Object> map : appMap) {
						if(value.getAppkey().equals(BlankUtils.getString(map.get("appkey")))){
							value.setAppid(appInfoMap.get(BlankUtils.getString(map.get("appkey"))).get("appid")!=null?BlankUtils.getString(appInfoMap.get(BlankUtils.getString(map.get("appkey"))).get("appid")):"");
							value.setAppname(BlankUtils.getString(map.get("appname")));
							value.setActnum(BlankUtils.getInt(map.get("actnum")));
							value.setAddnum(BlankUtils.getInt(map.get("addnum")));
							flag = false;
							break;
						}
					}
					// 没有匹配到渠道里的这个应用
					if(flag){
						obj.put("ret", 0);
						obj.put("msg", "广告位ID "+value.getAdcode()+" 没有匹配到渠道里的这个应用！");
						out.print(obj.toString());
						out.close();
						return;
					}
					
					// 获得活跃数后计算其它数据
					BigDecimal actnum = new BigDecimal(value.getActnum());
					if(value.getActnum() != 0){
						value.setTotal_income(
							new BigDecimal(value.getBanner_income())
							.add(new BigDecimal(value.getPlaque_income()))
							.add(new BigDecimal(value.getSplash_income()))
							.add(new BigDecimal(value.getVideo_income()))
							.add(new BigDecimal(value.getNative_banner_income()))
							.add(new BigDecimal(value.getNative_plaque_income()))
							.add(new BigDecimal(value.getNative_splash_income()))
							.add(new BigDecimal(value.getPlaque_video_income()))
							.add(new BigDecimal(value.getNative_msg_income()))
							.toString()
						);
						value.setDau_arpu(
							new BigDecimal(value.getTotal_income())
							.divide(actnum, 2, RoundingMode.HALF_UP)
							.toString()
						);

						//新增占比
						value.setAvgnum(new BigDecimal(value.getAddnum()).multiply(new BigDecimal(100))
								.divide(actnum, 2, RoundingMode.HALF_UP)
								+"%");
						
						// 各类型 人均pv
						value.setBanner_pv(
							new BigDecimal(value.getBanner_show())
							.add(new BigDecimal(value.getNative_banner_show()))
							.divide(actnum, 2, RoundingMode.HALF_UP)
							.toString()
						);
						value.setPlaque_pv(
							new BigDecimal(value.getPlaque_show())
							.add(new BigDecimal(value.getNative_plaque_show()))
							.divide(actnum, 2, RoundingMode.HALF_UP)
							.toString()
						);
						value.setSplash_pv(
							new BigDecimal(value.getSplash_show())
							.add(new BigDecimal(value.getNative_splash_show()))
							.divide(actnum, 2, RoundingMode.HALF_UP)
							.toString()
						);
						value.setVideo_pv(
							new BigDecimal(value.getVideo_show())
							.divide(actnum, 2, RoundingMode.HALF_UP)
							.toString()
						);
						value.setMsg_pv(
							new BigDecimal(value.getNative_msg_show())
							.divide(actnum, 2, RoundingMode.HALF_UP)
							.toString()
						);
						value.setPlaque_video_pv(
							new BigDecimal(value.getPlaque_video_show())
							.divide(actnum, 2, RoundingMode.HALF_UP)
							.toString()
						);
						
						// 每用户平均收入 arpu
						value.setBanner_arpu(
							new BigDecimal(value.getBanner_income())
							.add(new BigDecimal(value.getNative_banner_income()))
							.divide(actnum, 2, RoundingMode.HALF_UP)
							.toString()
						);
						value.setPlaque_arpu(
							new BigDecimal(value.getPlaque_income())
							.add(new BigDecimal(value.getNative_plaque_income()))
							.divide(actnum, 2, RoundingMode.HALF_UP)
							.toString()
						);
						value.setSplash_arpu(
							new BigDecimal(value.getSplash_income())
							.add(new BigDecimal(value.getNative_splash_income()))
							.divide(actnum, 2, RoundingMode.HALF_UP)
							.toString()
						);
						value.setVideo_arpu(
							new BigDecimal(value.getVideo_income())
							.divide(actnum, 2, RoundingMode.HALF_UP)
							.toString()
						);
						value.setMsg_arpu(
							new BigDecimal(value.getNative_msg_income())
							.divide(actnum, 2, RoundingMode.HALF_UP)
							.toString()
						);
						value.setPlaque_video_arpu(
							new BigDecimal(value.getPlaque_video_income())
							.divide(actnum, 2, RoundingMode.HALF_UP)
							.toString()
						);
					}
					dataList.add(value);
				}
				
				int result = adService.insertUmengAdIncomeList(dataList);
				// 添加到数据表中
				if(result > 0){
					obj.put("ret", 1);
					obj.put("msg", "上传文件成功");
				}else{
					obj.put("ret", 0);
					obj.put("msg", "文件数据有错误");
				}
			}else{
				obj.put("ret", 0);
				obj.put("msg", "上传的文件格式有误，需要.xls格式文件！");
			}
			out.print(obj.toString());
			out.close();
			
		}catch (Exception e) {
			//上传异常                	
			e.printStackTrace();
			PrintWriter pw;
			try {
				pw = response.getWriter();
				JSONObject obj = new JSONObject();
				obj.put("ret", 0);
				obj.put("msg", "导入数据失败");
				pw.print(obj.toString());
				pw.close();
			} catch (IOException e1) {
				e1.printStackTrace();
			}
		}
	}
	
	// 广告操作日志查询
	/*@CrossOrigin
	@RequestMapping(value="/ad/chargeTotalQuery", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String chargeTotalQuery(ModelMap map, HttpServletRequest request,HttpServletResponse response) {
		String pStart = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String dateStart = BlankUtils.checkNull(request, "dateStart");
		String dateEnd = BlankUtils.checkNull(request, "dateEnd");
		String userName = BlankUtils.checkNull(request, "userName");
		String nickName = BlankUtils.checkNull(request, "nickName");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		// pageStart 和 limit设置值
		int pageStart = "".equals(pStart) == true ? 0 : Integer.parseInt(pStart);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		// 转载查询语句条件的集合
		Map<String, String> parmMap = new HashMap<String, String>();
		parmMap.put("userName", userName);
		parmMap.put("nickName", nickName);
		parmMap.put("beginDate", dateStart);
		parmMap.put("endDate", dateEnd);

		List<AdConfigLog> list = iJdAppPayService.browsAdLog(parmMap);
		
		redisTemplate.opsForValue().set("chargeTotalQueryToExcelList", list, 20 * 60, TimeUnit.SECONDS);
		
		PageForList<AdConfigLog> pager = new PageForList<AdConfigLog>(pageNo, pageSize, list);
		pagerToJsonString(response, pager);
		
		PageForList<ActConfigVo> pager = new PageForList<ActConfigVo>( pageNo, pageSize, list);
		String jsonString = "{'ret':1,'totalCount':"+pager.getTotalRows()+",'root':"+JSONArray.toJSONString(pager.getResultList())+"}";
		return jsonString;
	}
	@CrossOrigin
	@RequestMapping(value="/ad/chargeTotalQueryToExcel", method = RequestMethod.POST)
	public void chargeTotalQueryToExcel(ModelMap map, HttpServletRequest request,HttpServletResponse response) {

		//数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		//数据内容		 
		List<AdConfigLog> list = (List<AdConfigLog>)redisTemplate.opsForValue().get("chargeTotalQueryToExcelList");


		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("oper_dt", "yyyy-MM-dd H:m:s");
		List<Map<String, Object>> contentList = new ArrayList<Map<String,Object>>();
		Map<String, Object> contentMap = null;
		for(AdConfigLog temp : list){
			contentMap = new LinkedHashMap<String, Object>();
			headerMap.put("oper_dt","操作时间");	  
			contentMap.put("oper_dt", temp.getOper_dt());
			headerMap.put("oper_nm","操作人");	  
			contentMap.put("oper_nm", temp.getOper_nm());
			headerMap.put("menu_type","操作界面");	  
			contentMap.put("menu_type", menuChoose(temp.getMenu_type()));				
			headerMap.put("oper_type","操作类型");	  
			contentMap.put("oper_type", opeType(temp.getOper_type())); 
			headerMap.put("ad_sid","广告SID");	  
			contentMap.put("ad_sid", temp.getAd_sid());
			headerMap.put("ad_code","广告CODE");	  
			contentMap.put("ad_code", temp.getAd_code());
			headerMap.put("ad_appid","广告APPID");	  
			contentMap.put("ad_appid", temp.getAd_appid());
			headerMap.put("ad_type","广告类型");	  
			contentMap.put("ad_type", temp.getAd_type());
			headerMap.put("ad_pid","开通项目id");	  
			contentMap.put("ad_pid", temp.getAd_pid());
			headerMap.put("ad_status","开通状态");	  
			contentMap.put("ad_status", openStaus(temp.getAd_status()));
			headerMap.put("ad_per","展示比例");	  
			contentMap.put("ad_per", temp.getAd_per());

			contentList.add(contentMap);
		}
			
		String fileName = "广告操作日志表_"+DateTime.now().toString("yyyyMMddHHmmss")+".xls";
		JxlUtil.doSave(fileName,headerMap,contentList,inMap,null,null,request,response); 
	}*/

	@CrossOrigin
	@RequestMapping(value="/ad/alonedau", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String alonedau(HttpServletRequest request,HttpServletResponse response) {
		
		String pStart = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String dateStart = BlankUtils.checkNull(request, "dateStart");
		String dateEnd = BlankUtils.checkNull(request, "dateEnd");
		String channel = BlankUtils.checkNull(request, "channel");
		String chanmeNm = BlankUtils.checkNull(request, "chanmeNm");
		String groupMonth = BlankUtils.checkNull(request, "groupMonth");
		String type = BlankUtils.checkNull(request, "type");
		String companyid = BlankUtils.checkNull(request, "companyid");
		String appName = BlankUtils.checkNull(request, "appName");
		String channelName = BlankUtils.checkNull(request,"channelName");
		String departType = BlankUtils.checkNull(request, "departType");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		// pageStart 和 limit设置值
		int pageStart = "".equals(pStart) == true ? 0 : Integer.parseInt(pStart);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		/* 这个做权限控制处理  */
		
		// 转载查询语句条件的集合
		Map<String, String> parmMap = new HashMap<String, String>();
		parmMap.put("beginDate", dateStart);
		parmMap.put("endDate", dateEnd);
		parmMap.put("channel", channel);
		parmMap.put("chanmeNm", channelName);
		parmMap.put("groupMonth", groupMonth);
		parmMap.put("type", type);
		parmMap.put("companyid", companyid);
		parmMap.put("appName", appName);
		parmMap.put("departType", departType);
			
		List<BuYuNewCountVo> list = adService.queryAloneDau(parmMap);
		
		List<BuYuNewCountVo> buslist = new ArrayList<BuYuNewCountVo>();
		buslist.addAll(list);
		
		Long ydcont = 0l;
		Long ltcont = 0l;
		Long dxcont = 0l;
		Long sumnew = 0l;
		Long uncount = 0l;
		DecimalFormat df = new DecimalFormat("#.##");
		 	
		//2019.4.18 项目配置兼容性处理 yangk
		Map<String, BuYuNewCountVo> projectMap = (Map<String, BuYuNewCountVo>)redisTemplate.opsForValue().get("dnwx_project_config");
		
		if(projectMap==null){
			projectMap = new HashMap<String, BuYuNewCountVo>();
			List<BuYuNewCountVo> prijectList = adService.getProjectConfig();
			for (BuYuNewCountVo prijectVO : prijectList) {
				projectMap.put(prijectVO.getBy_priid(), prijectVO);
			}
			redisTemplate.opsForValue().set("dnwx_project_config", projectMap, 20 * 60, TimeUnit.SECONDS);
		}
		//2019.4.18 end
		
		for (BuYuNewCountVo buYuNewCountVo : buslist) {
			
			sumnew+=buYuNewCountVo.getBy_newcount();
			ydcont+=buYuNewCountVo.getYd_newcount();
			ltcont+=buYuNewCountVo.getLt_newcount();
			dxcont+=buYuNewCountVo.getDx_newcount();
			uncount+=buYuNewCountVo.getUnknow_count();
			if(buYuNewCountVo.getBy_newcount()!=0){
				String ltpre=df.format((double)buYuNewCountVo.getLt_newcount()/buYuNewCountVo.getBy_newcount()*100)+"%";
				buYuNewCountVo.setLt_zb(ltpre);
				String ydpre=df.format((double)buYuNewCountVo.getYd_newcount()/buYuNewCountVo.getBy_newcount()*100)+"%";
				buYuNewCountVo.setYd_zb(ydpre);
				String dxpre=df.format((double)buYuNewCountVo.getDx_newcount()/buYuNewCountVo.getBy_newcount()*100)+"%";
				buYuNewCountVo.setDx_zb(dxpre);
				String unpre=df.format((double)buYuNewCountVo.getUnknow_count()/buYuNewCountVo.getBy_newcount()*100)+"%";
				buYuNewCountVo.setUn_zb(unpre);
			}
			
			//2019.4.18 项目配置兼容性处理 yangk
			if(projectMap.get(buYuNewCountVo.getBy_priid())!=null){
				BuYuNewCountVo tempVo = projectMap.get(buYuNewCountVo.getBy_priid());
				buYuNewCountVo.setP_appNm(tempVo.getP_appNm());
				buYuNewCountVo.setP_ver(tempVo.getP_ver());
				buYuNewCountVo.setP_remark(tempVo.getP_remark());
				buYuNewCountVo.setP_priName(tempVo.getP_priName());
				buYuNewCountVo.setOs_type(tempVo.getOs_type());
			}
			//2019.4.18 end
		}
		BuYuNewCountVo totalVo = new BuYuNewCountVo();
		totalVo.setBy_newcount(sumnew);
		totalVo.setYd_newcount(ydcont);
		totalVo.setLt_newcount(ltcont);
		totalVo.setDx_newcount(dxcont);  
		totalVo.setUnknow_count(uncount);
		if(totalVo.getBy_newcount()!=0){
			String ltpre=df.format((double)totalVo.getLt_newcount()/totalVo.getBy_newcount()*100)+"%";
			totalVo.setLt_zb(ltpre);
			String ydpre=df.format((double)totalVo.getYd_newcount()/totalVo.getBy_newcount()*100)+"%";
			totalVo.setYd_zb(ydpre);
			String dxpre=df.format((double)totalVo.getDx_newcount()/totalVo.getBy_newcount()*100)+"%";
			totalVo.setDx_zb(dxpre);
			String unpre=df.format((double)totalVo.getUnknow_count()/totalVo.getBy_newcount()*100)+"%";
			totalVo.setUn_zb(unpre);
		}
		totalVo.setStrDt("总计:");
		buslist.add(totalVo);
		
		redisTemplate.opsForValue().set("alonedauToExcelList", buslist, 20 * 60, TimeUnit.SECONDS);
		
		PageForList<BuYuNewCountVo> pager = new PageForList<BuYuNewCountVo>( pageNo, pageSize, buslist);
		String jsonString = "{'ret':1,'totalCount':"+pager.getTotalRows()+",'root':"+JSONArray.toJSONString(pager.getResultList())+"}";
		return jsonString;
	}
	@CrossOrigin
	@RequestMapping(value="/ad/aloneDausaveToExcel", method = RequestMethod.POST)
	public void aloneDausaveToExcel(HttpServletRequest request,HttpServletResponse response) {
		//数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		headerMap.put("strDt","日期");	   

		//数据内容		 
		List<BuYuNewCountVo> list = (List<BuYuNewCountVo>)redisTemplate.opsForValue().get("alonedauToExcelList");


		Map<String, String> inMap = new LinkedHashMap<String, String>();  
		inMap.put("strDt", "yyyy-MM-dd");
		List<Map<String, Object>> contentList = new ArrayList<Map<String,Object>>();
		Map<String, Object> contentMap = null;
		if(list!=null && !list.isEmpty()){
			for(BuYuNewCountVo temp : list){
				contentMap = new LinkedHashMap<String, Object>();
				contentMap.put("strDt", temp.getStrDt());

				headerMap.put("by_priid","渠道");	  
				contentMap.put("by_priid", temp.getBy_priid());
				headerMap.put("p_ver","版本号");	  
				contentMap.put("p_ver", temp.getP_ver());
				headerMap.put("p_appNm","应用名称");	  
				contentMap.put("p_appNm", temp.getP_appNm());
				headerMap.put("by_newcount","活跃数");	  
				contentMap.put("by_newcount", temp.getBy_newcount());


				headerMap.put("yd_zb","移动占比");	  
				contentMap.put("yd_zb", temp.getYd_zb()); 

				headerMap.put("lt_zb","联通占比");	  
				contentMap.put("lt_zb", temp.getLt_zb()); 

				headerMap.put("dx_zb","电信占比");	  
				contentMap.put("dx_zb", temp.getDx_zb()); 

				headerMap.put("un_zb","未知占比");	  
				contentMap.put("un_zb", temp.getUn_zb()); 

				headerMap.put("p_ver","版本号");	  
				contentMap.put("p_ver", temp.getP_ver());

				headerMap.put("p_remark","产品类型");	  
				contentMap.put("p_remark", temp.getP_remark());

				headerMap.put("p_company","所属公司");	  
				contentMap.put("p_company", temp.getP_company());

				headerMap.put("depart_type","所属部门");	  
				contentMap.put("depart_type", temp.getDepart_type());
				contentList.add(contentMap);
			}

			String fileName = "单机活跃数报表_"+DateTime.now().toString("yyyyMMddHHmmss")+".xls";
			JxlUtil.doSave(fileName,headerMap,contentList,inMap,null,null,request,response);
		}
	}
	
	// 广点通数据信息
	@CrossOrigin
	@RequestMapping(value="/ad/selectGDTAdwordReport", method={RequestMethod.GET, RequestMethod.POST})
	public @ResponseBody String selectGDTAdwordReport(GDTWorkReportVo gdt,HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String mname = request.getParameter("mname");
		String pname = request.getParameter("pname");
		String ptype = request.getParameter("ptype");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		JSONObject result = new JSONObject();
		try {
			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(start_date) 
					|| BlankUtils.sqlValidate(end_date)
					|| BlankUtils.sqlValidate(mname)
					|| BlankUtils.sqlValidate(pname)
					|| BlankUtils.sqlValidate(ptype)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
				end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			}
						
			String sql = "select * from gdt_adwork_report where date BETWEEN '"+start_date+"' AND '"+end_date+"'";
			if(!BlankUtils.checkBlank(mname))
				sql = sql + " and MediumName like '%"+mname+"%'";
			if(!BlankUtils.checkBlank(mname))
				sql = sql + " and PlacementName like '%"+pname+"%'";
			if(!BlankUtils.checkBlank(mname))
				sql = sql + " and PlacementType like '%"+ptype+"%'";
			
			sql = sql + " order by RequestCount desc";
			
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<Map<String, Object>> list = adService.queryListMap(sql);
			list.forEach(map -> {
				map.put("Date", map.get("Date")+"");
			});
			long size = ((Page) list).getTotal();
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}
	
	@CrossOrigin
	@RequestMapping(value="/ad/exportGDTAdwordReport", method = RequestMethod.POST)
	public void exportGDTAdwordReport(HttpServletRequest request,HttpServletResponse response) {
		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String mname = request.getParameter("mname");
		String pname = request.getParameter("pname");
		String ptype = request.getParameter("ptype");
		
		if(BlankUtils.sqlValidate(start_date) 
				|| BlankUtils.sqlValidate(end_date)
				|| BlankUtils.sqlValidate(mname)
				|| BlankUtils.sqlValidate(pname)
				|| BlankUtils.sqlValidate(ptype)){
			return ;
		}
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
					
		String sql = "select * from gdt_adwork_report where date BETWEEN '"+start_date+"' AND '"+end_date+"'";
		if(!BlankUtils.checkBlank(mname))
			sql = sql + " and MediumName like '%"+mname+"%'";
		if(!BlankUtils.checkBlank(mname))
			sql = sql + " and PlacementName like '%"+pname+"%'";
		if(!BlankUtils.checkBlank(mname))
			sql = sql + " and PlacementType like '%"+ptype+"%'";
		
		sql = sql + " order by RequestCount desc";
		
		List<Map<String, Object>> contentList = adService.queryListMap(sql);
		contentList.forEach(map -> {
			map.put("Date", map.get("Date")+"");
		});
		
		//数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		headerMap.put("Date","日期");
		headerMap.put("MediumName","媒体名称");
		headerMap.put("PlacementName","广告位名称");
		headerMap.put("PlacementType","广告位类型");
		headerMap.put("RequestCount","请求量");
		headerMap.put("Pv","展示量");
		headerMap.put("Click","有效点击量");
		headerMap.put("Revenue","预计收入");
		headerMap.put("ECPM","ECPM");
		headerMap.put("ClickRate","点击率");

		Map<String, String> inMap = new LinkedHashMap<String, String>();  
		inMap.put("strDt", "yyyy-MM-dd");
		
		String fileName = "广点通数据信息_"+DateTime.now().toString("yyyyMMddHHmmss")+".xls";
		JxlUtil.doSave(fileName,headerMap,contentList,inMap,null,null,request,response); 
	}
	
	@CrossOrigin
	@RequestMapping(value="/ad/selectProductOutflow", method={RequestMethod.GET, RequestMethod.POST})
	public @ResponseBody String selectProductOutflow(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String productid = request.getParameter("productid");
		String projectid = request.getParameter("projectid");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		JSONObject result = new JSONObject();
		try {
			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(start_date) 
					|| BlankUtils.sqlValidate(end_date)
					|| BlankUtils.sqlValidate(projectid)
					|| BlankUtils.sqlValidate(productid)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = DateTime.now().minusDays(7).toString("yyyy-MM-dd");
				end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			}
						
			String sql = "select t.*,concat(truncate(t.outflow_num*100/t.act_num, 2),'%') as outflow_rate"
					+ " from apk_product_outflow t where t.tdate BETWEEN '"+start_date+"' AND '"+end_date+"'"
					+ " and t.product_id = '"+productid+"'";
			if(!BlankUtils.checkBlank(projectid))
				sql = sql + " and t.projectid = '"+projectid+"'";
			else
				sql = sql + " and t.projectid = 'all'";
			
			sql = sql + " order by t.tdate asc";
			
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<Map<String, Object>> list = adService.queryListMap(sql);
			long size = ((Page) list).getTotal();
			
			list.forEach(map -> {
				map.put("tdate", map.get("tdate")+"");
			});
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}
	@CrossOrigin
	@RequestMapping(value="/ad/exportProductOutflow", method = RequestMethod.POST)
	public void exportProductOutflow(HttpServletRequest request,HttpServletResponse response) {
		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String productid = request.getParameter("productid");
		String projectid = request.getParameter("projectid");
		
		// 筛除sql敏感字符注入
		if(BlankUtils.sqlValidate(start_date) 
				|| BlankUtils.sqlValidate(end_date)
				|| BlankUtils.sqlValidate(projectid)
				|| BlankUtils.sqlValidate(productid)){
			return ;
		}
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = DateTime.now().minusDays(7).toString("yyyy-MM-dd");
			end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
					
		String sql = "select t.*,concat(truncate(t.outflow_num*100/t.act_num, 2),'%') as outflow_rate"
				+ " from apk_product_outflow t where t.tdate BETWEEN '"+start_date+"' AND '"+end_date+"'"
				+ " and t.product_id = '"+productid+"'";
		if(!BlankUtils.checkBlank(projectid))
			sql = sql + " and t.projectid = '"+projectid+"'";
		else
			sql = sql + " and t.projectid = 'all'";
		
		sql = sql + " order by t.tdate asc";
		
		List<Map<String, Object>> contentList = adService.queryListMap(sql);
		contentList.forEach(map -> {
			map.put("tdate", map.get("tdate")+"");
		});
		
		//数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		headerMap.put("tdate","日期");
		headerMap.put("outflow_num","流失用户");
		headerMap.put("outflow_rate","流失率");

		Map<String, String> inMap = new LinkedHashMap<String, String>();  
		inMap.put("strDt", "yyyy-MM-dd");
		
		String fileName = "流失用户数据信息_"+DateTime.now().toString("yyyyMMddHHmmss")+".xls";
		JxlUtil.doSave(fileName,headerMap,contentList,inMap,null,null,request,response); 
	}
	
	@CrossOrigin
	@RequestMapping(value="/ad/selectProductSilent", method={RequestMethod.GET, RequestMethod.POST})
	public @ResponseBody String selectProductSilent(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String productid = request.getParameter("productid");
		String projectid = request.getParameter("projectid");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		JSONObject result = new JSONObject();
		try {
			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(start_date) 
					|| BlankUtils.sqlValidate(end_date)
					|| BlankUtils.sqlValidate(projectid)
					|| BlankUtils.sqlValidate(productid)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = DateTime.now().minusDays(7).toString("yyyy-MM-dd");
				end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			}
						
			String sql = "select t.*,concat(truncate(t.silent_num*100/t.add_num, 2),'%') as silent_rate"
					+ " from apk_product_silent t where t.tdate BETWEEN '"+start_date+"' AND '"+end_date+"'"
					+ " and t.product_id = '"+productid+"'";
			if(!BlankUtils.checkBlank(projectid))
				sql = sql + " and t.projectid = '"+projectid+"'";
			else
				sql = sql + " and t.projectid = 'all'";
			
			sql = sql + " order by t.tdate asc";
			
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<Map<String, Object>> list = adService.queryListMap(sql);
			long size = ((Page) list).getTotal();
			
			list.forEach(map -> {
				map.put("tdate", map.get("tdate")+"");
			});
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}
	@CrossOrigin
	@RequestMapping(value="/ad/exportProductSilent", method = RequestMethod.POST)
	public void exportProductSilent(HttpServletRequest request,HttpServletResponse response) {
		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String productid = request.getParameter("productid");
		String projectid = request.getParameter("projectid");
		
		// 筛除sql敏感字符注入
		if(BlankUtils.sqlValidate(start_date) 
				|| BlankUtils.sqlValidate(end_date)
				|| BlankUtils.sqlValidate(projectid)
				|| BlankUtils.sqlValidate(productid)){
			return ;
		}
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = DateTime.now().minusDays(7).toString("yyyy-MM-dd");
			end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
					
		String sql = "select t.*,concat(truncate(t.silent_num*100/t.add_num, 2),'%') as silent_rate"
				+ " from apk_product_silent t where t.tdate BETWEEN '"+start_date+"' AND '"+end_date+"'"
				+ " and t.product_id = '"+productid+"'";
		if(!BlankUtils.checkBlank(projectid))
			sql = sql + " and t.projectid = '"+projectid+"'";
		else
			sql = sql + " and t.projectid = 'all'";
		
		sql = sql + " order by t.tdate asc";
		
		List<Map<String, Object>> contentList = adService.queryListMap(sql);
		contentList.forEach(map -> {
			map.put("tdate", map.get("tdate")+"");
		});
		
		//数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		headerMap.put("tname","日期");
		headerMap.put("add_num","新增用户");
		headerMap.put("silent_num","沉默用户");
		headerMap.put("silent_rate","沉默率");

		Map<String, String> inMap = new LinkedHashMap<String, String>();  
		inMap.put("strDt", "yyyy-MM-dd");
		
		String fileName = "沉默用户数据信息_"+DateTime.now().toString("yyyyMMddHHmmss")+".xls";
		JxlUtil.doSave(fileName,headerMap,contentList,inMap,null,null,request,response); 
	}
	
	// 微信红包支付参数配置
	@CrossOrigin
	@RequestMapping(value="/ad/selectHongBaoConfig", method={RequestMethod.GET, RequestMethod.POST})
	public @ResponseBody String selectHongBaoConfig(HongBaoInfoVo hb,HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String packname = request.getParameter("packname");
		String appname = request.getParameter("appname");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		JSONObject result = new JSONObject();
		try {
			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(packname) 
					|| BlankUtils.sqlValidate(appname)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}
						
			String sql = "select * from wx_hongbao_info where 1=1 ";
			if(!BlankUtils.checkBlank(packname))
				sql = sql + " and packname = '"+packname+"'";
			if(!BlankUtils.checkBlank(appname))
				sql = sql + " and appname like '%"+appname+"%'";
			
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<Map<String, Object>> list = adService.queryListMap(sql);
			long size = ((Page) list).getTotal();
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}
	
	@CrossOrigin
	@RequestMapping(value="/ad/exportHongBaoConfig", method = RequestMethod.POST)
	public void exportHongBaoConfig(HttpServletRequest request,HttpServletResponse response) {
		String packname = request.getParameter("packname");
		String appname = request.getParameter("appname");
		
		List<Map<String, Object>> contentList = null;
		try {
			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(packname) 
					|| BlankUtils.sqlValidate(appname)){
				return ;
			}
						
			String sql = "select * from wx_hongbao_info where 1=1 ";
			if(!BlankUtils.checkBlank(packname))
				sql = sql + " and packname = '"+packname+"'";
			if(!BlankUtils.checkBlank(appname))
				sql = sql + " and appname like '%"+appname+"%'";
			
			contentList = adService.queryListMap(sql);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		//数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		headerMap.put("appname","游戏名称");
		headerMap.put("packname","包名");
		headerMap.put("mch_id","商户号");
		headerMap.put("wxappid","账号appid");
		headerMap.put("nick_name","提供方名称");
		headerMap.put("send_name","商户名称");
		headerMap.put("wishing","红包祝福语");
		headerMap.put("act_name","活动名称");
		headerMap.put("remark","备注");
		headerMap.put("key","密钥");
		headerMap.put("zspath","证书");
		headerMap.put("appsecret","AppSecret");
		headerMap.put("call_url","回调地址");

		Map<String, String> inMap = new LinkedHashMap<String, String>();  
		inMap.put("strDt", "yyyy-MM-dd");
		
		String fileName = "微信支付参数配置_"+DateTime.now().toString("yyyyMMddHHmmss")+".xls";
		JxlUtil.doSave(fileName,headerMap,contentList,inMap,null,null,request,response); 
	}
	@CrossOrigin
	@RequestMapping(value="/ad/hongBaoConfigHandle", method = RequestMethod.POST)
	public @ResponseBody String hongBaoConfigHandle(HongBaoInfoVo hb,String handle, HttpServletRequest request,
			HttpServletResponse response) throws IOException, Exception {
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		int result = 0;
		try {
			if("add".equals(handle)){
				result = adService.insertHongBaoConfig(hb);
			}else if("edit".equals(handle)){
				result = adService.updateHongBaoConfig(hb);
			}
			
			if(result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";
				
		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
		
	}
	
	// 广点通 媒体模块，广告位模块
	@CrossOrigin
	@RequestMapping(value="/ad/selectGdtMedium", method={RequestMethod.GET, RequestMethod.POST})
	public @ResponseBody String selectGdtMedium(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String medium_id = request.getParameter("medium_id");
		String medium_name = request.getParameter("medium_name");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		JSONObject result = new JSONObject();
		try {
			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(medium_id) 
					|| BlankUtils.sqlValidate(medium_name)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}
						
			String sql = "select * from gdt_adwork_medium where 1=1";
			if(!BlankUtils.checkBlank(medium_id))
				sql = sql + " and medium_id = '"+medium_id+"'";
			if(!BlankUtils.checkBlank(medium_name))
				sql = sql + " and medium_name like '%"+medium_name+"%'";
			sql = sql + " order by createtime desc";
			
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<Map<String, Object>> list = adService.queryListMap(sql);
			long size = ((Page) list).getTotal();
			
			list.forEach(map -> {
				map.put("createtime", map.get("createtime")+"");
			});
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}
	@CrossOrigin
	@RequestMapping(value="/ad/gdtMediumHandle", method = RequestMethod.POST)
	public @ResponseBody String gdtMediumHandle(GDTWorkMediumVo gm,String handle, HttpServletRequest request,
			HttpServletResponse response) throws IOException, Exception {
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		int result = 0;
		try {
			
			if("add".equals(handle)){
				String url = "https://api.e.qq.com/luna/v2/adnetwork_medium/create?auth_type=TokenAuth&token=";
				url = url + getTokenStr("9232481");
				
				String baseParam = "member_id="+gm.getMember_id()
						+ "&medium_name="+gm.getMedium_name()
						+ "&industry_id="+gm.getIndustry_id()
						+ "&os="+gm.getOs()
						+ "&keywords="+gm.getKeywords()
						+ "&package_name="+gm.getPackage_name()
						+ "&market_status="+gm.getMarket_status()
						+ "&description="+gm.getDescription();
				if(gm.getMarket_status() != null && "ONLINE".equals(gm.getMarket_status())){
					baseParam = baseParam + "&market_url="+gm.getMarket_url();
				}
				if(gm.getOs() != null && "IOS".equals(gm.getOs())){
					baseParam = baseParam + "&jail_break="+gm.getJail_break()=="1"?"true":"false";
				}
				
				String httpPost = httpPost(url, baseParam);
				if(httpPost != null && 0 == JSONObject.parseObject(httpPost).getIntValue("ret")){
					JSONObject object = JSONObject.parseObject(httpPost).getJSONObject("data");
					gm.setApp_id(object.getString("app_id"));
					gm.setMedium_id(object.getString("medium_id"));
					
					result = adService.insertGDTMedium(gm);
					return "{\"ret\":1,\"msg\":\"操作成功!\"}";
				}else{
					return "{\"ret\":0,\"msg\": "+httpPost+"}";
				}
				
			}else if("edit".equals(handle)){
				String url = "https://api.e.qq.com/luna/v2/adnetwork_medium/update?auth_type=TokenAuth&token=";
				url = url + getTokenStr("9232481");
				
				String baseParam = "member_id="+gm.getMember_id()
						+ "&medium_name="+gm.getMedium_name()
						+ "&industry_id="+gm.getIndustry_id()
						+ "&os="+gm.getOs()
						+ "&keywords="+gm.getKeywords()
						+ "&package_name="+gm.getPackage_name()
						+ "&market_status="+gm.getMarket_status()
						+ "&description="+gm.getDescription()
						+ "&medium_id="+gm.getMedium_id();
				if(gm.getMarket_status() != null && "ONLINE".equals(gm.getMarket_status())){
					baseParam = baseParam + "&market_url="+gm.getMarket_url();
				}
				if(gm.getOs() != null && "IOS".equals(gm.getOs())){
					baseParam = baseParam + "&jail_break="+gm.getJail_break()=="1"?"true":"false";
				}
				
				String httpPost = httpPost(url, baseParam);
				if(httpPost != null && 0 == JSONObject.parseObject(httpPost).getIntValue("ret")){
					result = adService.updateGDTMedium(gm);
					return "{\"ret\":1,\"msg\":\"操作成功!\"}";
				}else{
					return "{\"ret\":0,\"msg\": "+httpPost+"}";
				}
			}
			return "{\"ret\":0,\"msg\":\"无操作行为!\"}";
		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}
	@CrossOrigin
	@RequestMapping(value="/ad/selectGdtPlacement", method={RequestMethod.GET, RequestMethod.POST})
	public @ResponseBody String selectGdtPlacement(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String placement_id = request.getParameter("placement_id");
		String placement_name = request.getParameter("placement_name");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		JSONObject result = new JSONObject();
		try {
			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(placement_id) 
					|| BlankUtils.sqlValidate(placement_name)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}
						
			String sql = "select * from gdt_adwork_placement where 1=1";
			if(!BlankUtils.checkBlank(placement_id))
				sql = sql + " and placement_id = '"+placement_id+"'";
			if(!BlankUtils.checkBlank(placement_name))
				sql = sql + " and placement_name like '%"+placement_name+"%'";
			sql = sql + " order by createtime desc";
			
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<Map<String, Object>> list = adService.queryListMap(sql);
			long size = ((Page) list).getTotal();
			
			list.forEach(map -> {
				map.put("createtime", map.get("createtime")+"");
			});
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}
	@CrossOrigin
	@RequestMapping(value="/ad/gdtPlacementHandle", method = RequestMethod.POST)
	public @ResponseBody String gdtPlacementHandle(GDTWorkMediumVo gm,String handle, HttpServletRequest request,
			HttpServletResponse response) throws IOException, Exception {
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		int result = 0;
		try {
			
			if("add".equals(handle)){
				String url = "https://api.e.qq.com/luna/v2/adnetwork_placement/create?auth_type=TokenAuth&token=";
				url = url + getTokenStr("9232481");
				
				String baseParam = "member_id="+gm.getMember_id()
						+ "&medium_id="+gm.getMedium_id()
						+ "&placement_name="+gm.getPlacement_name()
						+ "&placement_type="+gm.getPlacement_type()
						+ "&placement_sub_type="+gm.getPlacement_sub_type()
						+ "&ad_pull_mode="+gm.getAd_pull_mode();
				
				String httpPost = httpPost(url, baseParam);
				if(httpPost != null && 0 == JSONObject.parseObject(httpPost).getIntValue("ret")){
					JSONObject object = JSONObject.parseObject(httpPost).getJSONObject("data");
					gm.setApp_id(object.getString("app_id"));
					gm.setPlacement_id(object.getString("placement_id"));
					
					result = adService.insertGDTPlacement(gm);
					return "{\"ret\":1,\"msg\":\"操作成功!\"}";
				}else{
					return "{\"ret\":0,\"msg\": "+httpPost+"}";
				}
				
			}else if("edit".equals(handle)){
				String url = "https://api.e.qq.com/luna/v2/adnetwork_placement/update?auth_type=TokenAuth&token=";
				url = url + getTokenStr("9232481");
				
				String baseParam = "member_id="+gm.getMember_id()
						+ "&medium_id="+gm.getMedium_id()
						+ "&placement_id="+gm.getPlacement_id()
						+ "&placement_name="+gm.getPlacement_name();
		
				String httpPost = httpPost(url, baseParam);
				if(httpPost != null && 0 == JSONObject.parseObject(httpPost).getIntValue("ret")){
					result = adService.updateGDTPlacement(gm);
					return "{\"ret\":1,\"msg\":\"操作成功!\"}";
				}else{
					return "{\"ret\":0,\"msg\": "+httpPost+"}";
				}
			}
			return "{\"ret\":0,\"msg\":\"无操作行为!\"}";
		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}
	
	@CrossOrigin
	@RequestMapping(value="/ad/permissionsHandle", method = RequestMethod.POST)
	public @ResponseBody String permissionsHandle(HttpServletRequest request,
			HttpServletResponse response) throws IOException, Exception {
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		//阿里云上传OSS参数
		//String endpoint = "http://oss-cn-shenzhen.aliyuncs.com";
		//String accessKeyId = "LTAImrFvdsZ2E6Vd";
	    //String accessKeySecret = "******************************";

        String endpoint = "https://oss-cn-shenzhen.aliyuncs.com";
        String accessKeyId = "LTAI5tAWSWUTUQaGVDDArFMq";
        String accessKeySecret = "******************************";
	    //String bucketName = "vmhome";
	    String bucketName = "dnwx-res";

		String con = request.getParameter("con");
		String content = request.getParameter("content");
		String flag = request.getParameter("flag"); // 是否内容base64编码
		String name = request.getParameter("name"); // 文件完整路径名称
		// 移除掉 "./"和"/"开头
		if(name.startsWith("/")){
			name = name.substring(1);
		}
		if(name.startsWith("./")){
			name = name.substring(2);
		}
		try {
			
			if("add".equals(con)){
				// 执行OSS上传操作判断
				OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
				boolean isexist = ossClient.doesObjectExist(bucketName, "role_json/"+ name);
				if(isexist){
					return "{\"ret\":2,\"msg\":\"文件已存在!\"}";
				}else{
					Path path = Paths.get(CommonUtil.tempPath+"/"+ name.substring(name.lastIndexOf("/")+1));
					Files.write(path, content.getBytes("UTF-8"),
							StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
					
					ObjectMetadata meta = new ObjectMetadata();
			        meta.setHeader("Content-Type", "application/json");
			        ossClient.putObject(bucketName, "role_json/"+ name, path.toFile(), meta);
			        Files.delete(path);
				}
				ossClient.shutdown();
				
				// 判断文件存在则返回 2
				return "{\"ret\":1,\"msg\":\"新增操作成功!\"}";
				
			}else if("edit".equals(con)){
				Path path = Paths.get(CommonUtil.tempPath+"/"+ name.substring(name.lastIndexOf("/")+1));
				Files.write(path, content.getBytes("UTF-8"),
						StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
				
				OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
				ObjectMetadata meta = new ObjectMetadata();
		        meta.setHeader("Content-Type", "application/json");
		        ossClient.putObject(bucketName, "role_json/"+ name, path.toFile(), meta);
		        ossClient.shutdown();
		        Files.delete(path);
				
				return "{\"ret\":1,\"msg\":\"修改成功!\"}";
			}else if("del".equals(con)){
				OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
		        ossClient.deleteObject(bucketName, "role_json/"+ name);
		        ossClient.shutdown();
				
				return "{\"ret\":1,\"msg\":\"删除成功!\"}";
			}else if("backup".equals(con)){
				OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
				final int maxKeys = 200;
				
				// 先删除role_json_backup目录下所有文件，然后将role_json目录下所有拷贝过来
				List<String> keys = new ArrayList<String>();
				ObjectListing backObjects = ossClient.listObjects(
						new ListObjectsRequest(bucketName).withMaxKeys(maxKeys).withPrefix("role_json_backup/"));
				for (OSSObjectSummary bk : backObjects.getObjectSummaries()) {
					keys.add(bk.getKey());
				}
				ossClient.deleteObjects(new DeleteObjectsRequest(bucketName).withKeys(keys));
				
				
				ObjectListing listObjects = ossClient.listObjects(
						new ListObjectsRequest(bucketName).withMaxKeys(maxKeys).withPrefix("role_json/"));
				for (OSSObjectSummary su : listObjects.getObjectSummaries()) {
					String newKey = su.getKey().replace("role_json", "role_json_backup");
					ossClient.copyObject(bucketName, su.getKey(), bucketName, newKey);
				}
		        ossClient.shutdown();
				
				return "{\"ret\":1,\"msg\":\"备份成功!\"}";
			}else if("check".equals(con)){
				String filename = name.substring(name.lastIndexOf("/")+1); // 去除目录的文件名
				OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
		        ossClient.getObject(new GetObjectRequest(bucketName, "role_json/"+ name), 
		        		new File(CommonUtil.tempPath+"/temp_"+ filename));
		        ossClient.shutdown();
		        
		        Path path = Paths.get(CommonUtil.tempPath+"/temp_"+ filename);
		        byte[] allBytes = Files.readAllBytes(path);
		        String contentStr = new String(allBytes, "UTF-8");
		        if(flag != null && "1".equals(flag)){ // 内容base64编码
		        	contentStr = Base64.encodeBase64String(allBytes);
		        }
		        Files.delete(path);
				
				return "{\"ret\":1,\"content\":\""+contentStr+"\"}";
			}
			
			return "{\"ret\":0,\"msg\":\"无操作行为!\"}";
		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}


	/**
	 * 获取cdn刷新过的url下拉列表
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 * @throws Exception
	 */
	@CrossOrigin
	@RequestMapping(value="/ad/selectCdnSerachUrl", method = {RequestMethod.POST,RequestMethod.GET})
	public @ResponseBody String selectCdnSerachUrl(HttpServletRequest request,HttpServletResponse response) {
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		}else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		JSONObject result = new JSONObject();
		List<RefreshCDNVo> list = new ArrayList<>();
		CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);
		if (cuser != null) {
			String username = cuser.getLogin_name();
			RefreshCDNVo vo = new RefreshCDNVo();
			vo.setUsername(username);
			list = userInfoMapper.selectRefreshCDNUrlList(vo);
		}
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", list.size());
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}


	// TODO 广告接口迁移 移动、变现、应用、财务、广告、暖暖使用待处理
	@CrossOrigin
	@RequestMapping(value="/ad/cdnRefreshObject", method = {RequestMethod.POST,RequestMethod.GET})
	public @ResponseBody String cdnRefreshObject(HttpServletRequest request,
			HttpServletResponse response) throws IOException, Exception {
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		String base = request.getParameter("url");
		String type = request.getParameter("type");
		String action = request.getParameter("action"); //操作类型  刷新 refresh 加载 loading

		//记录用户操作url
		try {
			if (!BlankUtils.checkBlank(base)) {
				String url = new String(Base64.decodeBase64(base));
				CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
				if (cuser != null) {
					String nickname = cuser.getLogin_name();
					RefreshCDNVo vo = new RefreshCDNVo();
					vo.setUrl(url);
					vo.setUsername(nickname);
					userInfoMapper.saveRefreshCDNUrl(vo);
				}
			}
		} catch (Exception e) {
			System.out.println("保存刷新cdn地址出错");
			e.printStackTrace();
		}
		try {
			String url = new String(Base64.decodeBase64(base));
			// 只能刷新账户所属的域名
			DefaultProfile profile = DefaultProfile.getProfile("cn-shenzhen", "LTAI26PJhDrD3yt1", "Xqw3VT3TuWF8Lwiou2l4oza7VQqJN1");
	        IAcsClient client = new DefaultAcsClient(profile);

	        try {
	        	if("refresh".equals(action)){
		        	RefreshObjectCachesRequest req = new RefreshObjectCachesRequest();
		        	if("dir".equals(type)) { // 默认为 File
			        	req.setObjectType("Directory");
			        	if(!url.endsWith("/")) {
			        		url = url + "/";
			        	}
			        }
			        req.setObjectPath(url);
			        RefreshObjectCachesResponse resp = client.getAcsResponse(req);

		        }else if("loading".equals(action)){
		        	PushObjectCacheRequest req = new PushObjectCacheRequest();
			        if("dir".equals(type)) { // 默认为 File
			        	if(!url.endsWith("/")) {
			        		url = url + "/";
			        	}
			        }
			        req.setObjectPath(url);
			        PushObjectCacheResponse resp =client.getAcsResponse(req);
		        }else if("push".equals(action)){ // 万物云刷新缓存
		        	String nonce = BlankUtils.getRandomNumber(10);
		    		String stamp = (DateTime.now().getMillis()/1000)+"";
		    		String tokens = "fc27b7e9-328f-440f-ada1-a133be1daba7";
		    		// 将 token、 stamp、 nonce、三个参数进行字典序排序
		    		String[] arr = new String[]{tokens, stamp, nonce};
		    		Arrays.sort(arr);

		    		String httpurl = "http://openapi.fjsdn.com/api/v1/cdn/push?client=dnwx"
		    				+ "&nonce="+nonce+"&stamp="+stamp;
		    		httpurl = httpurl+"&sign=" + DigestUtils.sha1Hex(arr[0]+arr[1]+arr[2]);

		    		Map<String, String> headMap= new HashMap<String, String>();
		    		headMap.put("Content-Type", "application/json");

		    		JSONObject param = new JSONObject();

	    			JSONObject item = new JSONObject();
	    			if("dir".equals(type)) { // 默认为 File
	    	        	if(!url.endsWith("/")) {
	    	        		url = url + "/";
	    	        	}
	    	        	item.put("uri", url);
	    	        	item.put("pushtype", 1);
		    		}else{
    	        		item.put("uri", url);
    	     	        item.put("pushtype", 0);
    	        	}
		    		param.put("content", item);

		    		String httpPost = HttpClientUtils.getInstance()
		    				.httpPost(httpurl, param.toJSONString(), headMap);
		    		JSONObject parseObj = JSONObject.parseObject(httpPost);
		    		System.out.println(httpPost);

		    		if(parseObj != null && "000000".equals(parseObj.getString("code"))){
						return "{\"ret\":1,\"msg\":\"万物云联刷新成功!\"}";
					}else if("060255".equals(parseObj.getString("code"))){
						return "{\"ret\":0,\"msg\":\"url或者目录不存在 !\"}";
					}else if("000001".equals(parseObj.getString("code"))){
						return "{\"ret\":0,\"msg\":\"未授权  !\"}";
					}else {
						return "{\"ret\":0,\"msg\":\"万物云联刷新失败!\"}";
					}
		        }else if("hwyun".equals(action)){ // 华为云cdn缓存刷新
		        	if("dir".equals(type))
		        		type = "directory";
		        	String result = recacheHWyun(type, url);
		        	JSONObject parseObj = JSONObject.parseObject(result);
		        	if(parseObj != null
		            		&& !BlankUtils.checkBlank(parseObj.getString("error"))){
		        		JSONObject error = parseObj.getJSONObject("error");
		        		return "{\"ret\":0,\"msg\":\"错误"+error.getString("error_code")+" :"
		        				+error.getString("error_msg")+"\"}";
		            }
		        }else{
		        	return "{\"ret\":0,\"msg\":\"无效操作!\"}";
		        }

	        } catch (ServerException e) {
	            e.printStackTrace();
	            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
	        } catch (ClientException e) {
	        	return "{\"ret\":0, \"msg\":\"ErrCode:"+e.getErrCode()+", ErrMsg:"+e.getErrMsg()+"\"}";
	        }
	        
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	@CrossOrigin
	@RequestMapping(value="/ad/nocdnRefreshObject", method = RequestMethod.POST)
	public @ResponseBody String nocdnRefreshObject(HttpServletRequest request,
												 HttpServletResponse response) throws IOException, Exception {

		String base = request.getParameter("url");
		String type = request.getParameter("type");
		String action = request.getParameter("action"); //操作类型  刷新 refresh 加载 loading
		try {
			String url = new String(Base64.decodeBase64(base));
			// 只能刷新账户所属的域名
			DefaultProfile profile = DefaultProfile.getProfile("cn-shenzhen", "LTAI26PJhDrD3yt1", "Xqw3VT3TuWF8Lwiou2l4oza7VQqJN1");
			IAcsClient client = new DefaultAcsClient(profile);

			try {
				if("refresh".equals(action)){
					RefreshObjectCachesRequest req = new RefreshObjectCachesRequest();
					if("dir".equals(type)) { // 默认为 File
						req.setObjectType("Directory");
						if(!url.endsWith("/")) {
							url = url + "/";
						}
					}
					req.setObjectPath(url);
					RefreshObjectCachesResponse resp = client.getAcsResponse(req);

				}else if("loading".equals(action)){
					PushObjectCacheRequest req = new PushObjectCacheRequest();
					if("dir".equals(type)) { // 默认为 File
						if(!url.endsWith("/")) {
							url = url + "/";
						}
					}
					req.setObjectPath(url);
					PushObjectCacheResponse resp =client.getAcsResponse(req);
				}else if("push".equals(action)){ // 万物云刷新缓存
					String nonce = BlankUtils.getRandomNumber(10);
					String stamp = (DateTime.now().getMillis()/1000)+"";
					String tokens = "fc27b7e9-328f-440f-ada1-a133be1daba7";
					// 将 token、 stamp、 nonce、三个参数进行字典序排序
					String[] arr = new String[]{tokens, stamp, nonce};
					Arrays.sort(arr);

					String httpurl = "http://openapi.fjsdn.com/api/v1/cdn/push?client=dnwx"
							+ "&nonce="+nonce+"&stamp="+stamp;
					httpurl = httpurl+"&sign=" + DigestUtils.sha1Hex(arr[0]+arr[1]+arr[2]);

					Map<String, String> headMap= new HashMap<String, String>();
					headMap.put("Content-Type", "application/json");

					JSONObject param = new JSONObject();

					JSONObject item = new JSONObject();
					if("dir".equals(type)) { // 默认为 File
						if(!url.endsWith("/")) {
							url = url + "/";
						}
						item.put("uri", url);
						item.put("pushtype", 1);
					}else{
						item.put("uri", url);
						item.put("pushtype", 0);
					}
					param.put("content", item);

					String httpPost = HttpClientUtils.getInstance()
							.httpPost(httpurl, param.toJSONString(), headMap);
					JSONObject parseObj = JSONObject.parseObject(httpPost);
					System.out.println(httpPost);

					if(parseObj != null && "000000".equals(parseObj.getString("code"))){
						return "{\"ret\":1,\"msg\":\"万物云联刷新成功!\"}";
					}else if("060255".equals(parseObj.getString("code"))){
						return "{\"ret\":0,\"msg\":\"url或者目录不存在 !\"}";
					}else if("000001".equals(parseObj.getString("code"))){
						return "{\"ret\":0,\"msg\":\"未授权  !\"}";
					}else {
						return "{\"ret\":0,\"msg\":\"万物云联刷新失败!\"}";
					}
				}else if("hwyun".equals(action)){ // 华为云cdn缓存刷新
					if("dir".equals(type))
						type = "directory";
					String result = recacheHWyun(type, url);
					JSONObject parseObj = JSONObject.parseObject(result);
					if(parseObj != null
							&& !BlankUtils.checkBlank(parseObj.getString("error"))){
						JSONObject error = parseObj.getJSONObject("error");
						return "{\"ret\":0,\"msg\":\"错误"+error.getString("error_code")+" :"
								+error.getString("error_msg")+"\"}";
					}
				}else{
					return "{\"ret\":0,\"msg\":\"无效操作!\"}";
				}

			} catch (ServerException e) {
				e.printStackTrace();
				return "{\"ret\":0,\"msg\":\"操作失败!\"}";
			} catch (ClientException e) {
				return "{\"ret\":0, \"msg\":\"ErrCode:"+e.getErrCode()+", ErrMsg:"+e.getErrMsg()+"\"}";
			}

			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}
	
	/**
	 * 上传文件到阿里云OSS
	 * @param filePath 文件目录+"/"+文件名
	 * @param ins
	 */
	public static void uploadOSS(String filePath, InputStream ins) throws IOException{
		//阿里云上传OSS参数
		String endpoint = "http://oss-cn-shenzhen.aliyuncs.com";
		String accessKeyId = "LTAImrFvdsZ2E6Vd";
	    String accessKeySecret = "******************************";
	    //String bucketName = "vmhome";
	    String bucketName = "dnwx-res";

	    //阿里云上传工具
        //OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        //ossClient.putObject(bucketName, filePath, ins);
        OSSUploadUtil.uploadDataOSS(bucketName, filePath, ins, null);
        //ossClient.shutdown();
        
	}
	public static String httpPost(String url, String baseParam) {  
        String respStr = null;  
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded");
        try {
        	StringEntity reqEntity = new StringEntity(baseParam);
        	httpPost.setEntity(reqEntity);
        	
            CloseableHttpResponse resp = httpClient.execute(httpPost);
            try {  
                HttpEntity resEntity = resp.getEntity();
                respStr = EntityUtils.toString(resEntity, "UTF-8");
            } finally {  
            	resp.close();  
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {  
            try {  
                httpClient.close();  
            } catch (IOException e) {  
                e.printStackTrace();  
            }  
        }  
        return respStr;  
    }
	public static String getTokenStr(String agid) {
		agid = "9232481";
		String appid = "9232481";
		String appkey = "4aa91de6d069d37704434b0bb8bd29a2";
		String time = (DateTime.now().minusMinutes(3).getMillis() / 1000L) + "";
		
		String sign = DigestUtils.sha1Hex(appid + appkey + time);
		String token = Base64.encodeBase64String((agid +","+ appid +","+ time +","+ sign).getBytes());
		return token;
	}
	
	/**
	 * 华为云刷新CDN缓存
	 * @param type 链接类型file|directory
	 * @param urls 链接，多个使用用逗号分隔
	 * @return
	 */
	public static String recacheHWyun(String type, String urls){
		CloseableHttpClient client = null;
        try {
        	Request request = new Request();
            request.setKey("X9NOYBNBDQK3NVJ2JWPH");
            request.setSecret("xNNQR7CAVl93RZpCvM5adhjyz0HyDF6ijyGDURED");

            request.setMethod("POST");
            String url = "https://cdn.myhuaweicloud.com/v1.0/cdn/refreshtasks?enterprise_project_id=0";
            request.setUrl(url);
            request.addHeader("Content-Type", "application/json");
            String json = "{ \"refreshTask\":{ \"type\":\""+type+"\", \"urls\":[ \""+urls+"\" ] } }";
            request.setBody(json);
            
            // 执行请求
            HttpRequestBase signedRequest = Client.sign(request);
            client = HttpClients.custom().build();
            HttpResponse response = client.execute(signedRequest);

            String result = EntityUtils.toString(response.getEntity(), "UTF-8");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (client != null) {
                    client.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
	}


}
