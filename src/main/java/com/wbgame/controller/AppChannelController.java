package com.wbgame.controller;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.wbgame.mapper.clean.master.UmengSynLogMapper;
import com.wbgame.mapper.master.AdvertMapper;
import com.wbgame.pojo.CurrUserVo;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.tfxt.TfxtMapper;
import com.wbgame.pojo.NpPostVo2;
import com.wbgame.pojo.advert.DnwxReyunConfigVo;
import com.wbgame.pojo.custom.AppChannelConfigVo;
import com.wbgame.pojo.custom.AppchannelAppidVo;
import com.wbgame.service.AdService;
import com.wbgame.service.UmengService;
import com.wbgame.task.GameTask;
import com.wbgame.utils.BlankUtils;

/**
 * app渠道数据接口相关
 * <AUTHOR>
 */
@CrossOrigin
@RestController
public class AppChannelController {

	private static Logger logger = LoggerFactory.getLogger(AppChannelController.class);
	
	@Autowired
    private AdService adService;
	@Autowired
	private UmengService umengService;
	@Autowired
	private AdMapper adMapper;
	@Autowired
	private TfxtMapper tfxtMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private UmengSynLogMapper umengSynLogMapper;
	@Autowired
	private AdvertMapper advertMapper;
    
    /**
     * 换量渠道游戏配置
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/app/selectChannelAppidConfig", method={RequestMethod.GET, RequestMethod.POST})
	public String selectChannelAppidConfig(AppChannelConfigVo app, HttpServletRequest request) throws IOException {
		
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		JSONObject result = new JSONObject();
		try {

			PageHelper.startPage(pageNo, pageSize);
			List<AppChannelConfigVo> list = advertMapper.getAppChannelConfigList(app);
			long size = ((Page) list).getTotal();
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}
	
	@RequestMapping(value="/app/channelAppidConfigHandle")
	public String channelAppidConfigHandle(String handle, AppChannelConfigVo app,
			HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		String username = "";
		CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
		if (currUserVo != null) {
			username = currUserVo.getLogin_name();
		}
		app.setCreate_user(username);
		app.setModify_user(username);
		int result = 0;
		try {
			String sql = null;
			if("add".equals(handle)){
				sql = "insert into app_channel_config(account,channel,tttoken,ttappid," +
						"ttparam,dnappid,cname,createtime,token,company_type," +
						"note,create_time,create_user,status) "+
					" values(#{obj.account},#{obj.channel},#{obj.tttoken},#{obj.ttappid}," +
						" #{obj.ttparam},#{obj.dnappid},#{obj.cname},now(),#{obj.atoken},#{obj.company_type}," +
						" #{obj.note},now(),#{obj.create_user},#{obj.status}) ";
				
			}else if("edit".equals(handle)){
				sql = "update app_channel_config set " +
						" tttoken=#{obj.tttoken},ttappid=#{obj.ttappid}," +
						" ttparam=#{obj.ttparam},dnappid=#{obj.dnappid}," +
						" token = #{obj.atoken}, cname=#{obj.cname}," +
						" company_type =#{obj.company_type}, note =#{obj.note}," +
						" modify_time = now(),modify_user = #{obj.modify_user} ,status = #{obj.status} "+
					" where account=#{obj.account} and channel=#{obj.channel} ";
				
			}else if("del".equals(handle)){
				sql = "delete from app_channel_config where account=#{obj.account} and channel=#{obj.channel}";
			}
			
			result = adService.execSqlHandle(sql, app);
			if(result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";
				
		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	//抓取页面数据进行同步（爬虫部分）
	@RequestMapping(value="/app/getAppChannelData", method={RequestMethod.POST,RequestMethod.GET})
	public String getAppChannelData(String handle, AppChannelConfigVo app,
			HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {
		
		// token验证
//		String token = request.getParameter("token");
//		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//			return "{\"ret\":2,\"msg\":\"token is error!\"}";
//		else
//			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		if(BlankUtils.checkBlank(app.getStartTime())
				|| BlankUtils.checkBlank(app.getEndTime())
				|| BlankUtils.checkBlank(app.getChannel())
				|| BlankUtils.checkBlank(app.getTttoken())){
			
			return "{\"ret\":0,\"msg\":\"同步所需参数值不能为空!\"}";
		}
		JSONObject ret = new JSONObject();
		//日期区间
		DateTime startTime = DateTime.parse(app.getStartTime());
		DateTime endTime = DateTime.parse(app.getEndTime());

		int result = 0;
		Map<String,String> retMap;
		try {
			if ("tt".equals(app.getChannel())) {
				Map<String, Object> listMap = null;
				listMap = GameTask.getToutiaoMicGameData(app.getStartTime(),app.getEndTime(), app.getTttoken());
				retMap = commonMicGameDataHandle(app,listMap);
				if (!"1".equals(retMap.get("ret"))){
					ret.put("ret",0);
					ret.put("msg",retMap.get("msg")+"-请联系管理员");
					return ret.toJSONString();
				}
				result = 1;
			} else {
				//遍历日期区间的日期,当日期大于结束时间跳出
				while (startTime.compareTo(endTime) <= 0) {
					app.setTdate(startTime.toString("yyyy-MM-dd"));
					Map<String, Object> listMap = null;
					if ("vivo".equals(app.getChannel()))
						listMap = GameTask.getVivoActAdd(app.getTdate(), app.getTttoken());
					else if ("oppo".equals(app.getChannel()))
						listMap = GameTask.getOppoActAdd(app.getTdate(), app);
					else if ("qq".equals(app.getChannel())) {
						listMap = GameTask.getQQActAdd(app.getTdate(), app);
					}else if ("wx".equals(app.getChannel()) || "wechat".equals(app.getChannel())) {
						String channel_appid_sql = "select appid,ttappid as mapkey from  wx_channel_manage where cid='wechat' and ttappid!='' ";
						Map<String, Map<String, Object>> wxChannelManageMap = adService.queryListMapOfKey(channel_appid_sql);
						listMap = GameTask.getWxActAdd(app.getTdate(), app, wxChannelManageMap);
					}
					else
						return "{\"ret\":0,\"msg\":\"未配置的平台渠道!\"}";
					if (listMap != null && (listMap.get("code") == null || (int) listMap.get("code") != -1)) {
						retMap = commonMicGameDataHandle(app, listMap);
						if (!"1".equals(retMap.get("ret"))){
							ret.put("ret",0);
							ret.put("msg",retMap.get("msg")+"-请联系管理员");
							return ret.toJSONString();
						}
					} else {
						return "{\"ret\":0,\"msg\":\"抽取渠道数据失败：" + listMap.get("message") + "\"}";
					}
					//加一天
					startTime = startTime.plusDays(1);
					result = 1;
				}
			}

			if(result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";
		} catch (Exception e) {
			logger.error("getAppChannelData error:",e);
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	public Map<String,String> commonMicGameDataHandle(AppChannelConfigVo app,Map<String, Object> listMap){
		Map<String,String> retMap = new HashMap<>();
		if ("wx".equals(app.getChannel())) {
			app.setChannel("wechat");
		}
		// 新增活跃单独入库
		try {
			List<AppchannelAppidVo> sinkList = (List<AppchannelAppidVo>) listMap.get("sink");
			if(sinkList.size()>0) {
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("sql1", "insert into toutiao_app_info(tdate,channel,ttappid,cname,actnum,addnum) values ");
				paramMap.put("sql2", " (#{li.tdate},'" + app.getChannel() + "',#{li.appid_key},#{li.appid},#{li.act_num},#{li.add_num}) ");
				paramMap.put("sql3", " ON DUPLICATE KEY UPDATE actnum=VALUES(actnum),addnum=VALUES(addnum)");
				paramMap.put("list", listMap.get("sink"));
				adService.batchExecSql(paramMap);
			}
		} catch (Exception e) {
			logger.error("新增活跃单独入库,写入报错:",e);
		}


		// 广告数据分析 PV和收入部分数据
		if ("oppo".equals(app.getChannel()) || "vivo".equals(app.getChannel())) {

			if (listMap.get("sink") == null || listMap.get("keep") == null || listMap.get("income") == null) {
				retMap.put("ret","0");
				retMap.put("msg","抽取的活跃、新增数据返回为空！");
				return retMap;
			}

			try {
				List<AppchannelAppidVo> sinkList = (List<AppchannelAppidVo>) listMap.get("sink");
				if(sinkList.size()>0) {
					Map<String, Object> paramMap = new HashMap<String, Object>();
					paramMap.put("sql1", "insert into app_channel_appid(tdate,channel,appid,add_num,act_num,appid_key,times) values ");
					paramMap.put("sql2", " (#{li.tdate},'" + app.getChannel() + "',#{li.appid},#{li.add_num},#{li.act_num},#{li.appid_key},#{li.times}) ");
					paramMap.put("sql3", " ON DUPLICATE KEY UPDATE act_num=VALUES(act_num),add_num=VALUES(add_num),times=VALUES(times)");
					paramMap.put("list", listMap.get("sink"));
					adService.batchExecSql(paramMap);
				}
				List<AppchannelAppidVo> keepList = (List<AppchannelAppidVo>) listMap.get("keep");
				if(keepList.size()>0){
					adMapper.updateAppChannelForKeepList(keepList);
				}
				List<AppchannelAppidVo> incomeList = (List<AppchannelAppidVo>) listMap.get("income");
				if(incomeList.size()>0){
					adMapper.updateAppChannelForIncomeList(incomeList);
				}
				retMap.put("ret","1");
			} catch (Exception e) {
				logger.error("广告数据分析-oppo,vivo,写入报错:",e);
			}
		} else if ("qq".equals(app.getChannel()) || "wechat".equals(app.getChannel()) || "tt".equals(app.getChannel())) {

			String chan = "qq".equals(app.getChannel()) ? "手Q" : "wechat".equals(app.getChannel()) ? "微信" : "字节";
			try {
				List<AppchannelAppidVo> sinkList = (List<AppchannelAppidVo>) listMap.get("sink");
				List<AppchannelAppidVo> keepList = (List<AppchannelAppidVo>) listMap.get("keep");
				if (listMap.get("sink")!=null&&sinkList.size()>0){
					Map<String, Object> paramMap = new HashMap<String, Object>();
					paramMap.put("sql1", "insert into app_channel_appid(tdate,channel,appid,add_num,act_num,income,appid_key,times,two_rate,ban_income,screen_income,traffic_income,video_income,ban_pv,screen_pv,traffic_pv,video_pv,open_income,open_pv,brick_income,brick_pv) values ");
					paramMap.put("sql2", " (#{li.tdate},'" + chan + "',#{li.appid},#{li.add_num},#{li.act_num},#{li.income},#{li.appid_key},#{li.times},#{li.two_rate},#{li.ban_income},#{li.screen_income},#{li.traffic_income},#{li.video_income},#{li.ban_pv},#{li.screen_pv},#{li.traffic_pv},#{li.video_pv},#{li.open_income},#{li.open_pv},#{li.brick_income},#{li.brick_pv}) ");
					paramMap.put("sql3", " ON DUPLICATE KEY UPDATE act_num=VALUES(act_num),add_num=VALUES(add_num),times=VALUES(times)");
					paramMap.put("list", sinkList);
					adMapper.batchExecSql(paramMap);

					adMapper.updateAppChannelForIncomeList(sinkList);
				}
				if (listMap.get("keep")!=null&&keepList.size()>0){
					adMapper.updateAppChannelForKeepList(keepList);
				}
				retMap.put("ret","1");
			} catch (Exception e) {
				logger.error("广告数据分析数据,写入报错:",e);
			}
		}

		// wechat抽取小游戏数据助手 中的新增用户渠道分析
		if ("wechat".equals(app.getChannel())) {
			List<JSONObject> list = GameTask.getWxActAddTwo(app.getTdate(), app);
			if (list != null && !list.isEmpty()) {
				try {
					Map<String, Object> paramMap = new HashMap<String, Object>();
					paramMap.put("sql1", "insert into wechat_add_channel(tdate,add_name,add_appid,channel,wxname,wxappid,addnum) values ");
					paramMap.put("sql2", " (#{li.tdate},#{li.add_name},#{li.add_appid},#{li.channel},#{li.wxname},#{li.wxappid},#{li.addnum}) ");
					paramMap.put("sql3", " ON DUPLICATE KEY UPDATE addnum=VALUES(addnum)");
					paramMap.put("list", list);
					adService.batchExecSql(paramMap);
				} catch (Exception e) {
					logger.error("wechat抽取小游戏数据助手中的新增用户渠道分析,写入报错:",e);
				}
			}
		}
		return retMap;
	}

	
    /**
     * 同步渠道信息维护数据至投放系统
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/syncDnChannelInfo", method={RequestMethod.POST})
    public String syncDnChannelInfo(HttpServletRequest request, HttpServletResponse response) {
    	
        try{
        	// token验证
    		String token = request.getParameter("token");
    		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
    			return "{\"ret\":2,\"msg\":\"token is error!\"}";
    		else
    			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            
            // dn_channel_type 和 dn_channel_info表
    		String query = "select * from dn_channel_type";
    		List<Map<String, Object>> listMap = adMapper.queryListMap(query);
    		try {
    			Map<String, Object> paramMap = new HashMap<String, Object>();
    			paramMap.put("sql1", "replace into dn_channel_type(type_id,type_name) values ");
    			paramMap.put("sql2", " (#{li.type_id},#{li.type_name}) ");
    			paramMap.put("sql3", " ");
    			paramMap.put("list", listMap);
    			tfxtMapper.batchExecSql(paramMap);
    		} catch (Exception e) {
    			e.printStackTrace();
    			return "{\"ret\":0,\"msg\":\"出现错误，请稍后重试！\"}";
    		}
    		
    		String query2 = "select * from dn_channel_info";
    		List<Map<String, Object>> listMap2 = adMapper.queryListMap(query2);
    		try {
    			Map<String, Object> paramMap = new HashMap<String, Object>();
    			paramMap.put("sql1", "replace into dn_channel_info(cha_id,cha_type,cha_media,cha_sub_launch,cha_id_byte,cha_name_byte,cha_sub_byte,cha_sub_name_byte,cha_remark,cha_ratio) values ");
    			paramMap.put("sql2", " (#{li.cha_id},#{li.cha_type},#{li.cha_media},#{li.cha_sub_launch},#{li.cha_id_byte},#{li.cha_name_byte},#{li.cha_sub_byte},#{li.cha_sub_name_byte},#{li.cha_remark},#{li.cha_ratio}) ");
    			paramMap.put("sql3", " ");
    			paramMap.put("list", listMap2);
    			tfxtMapper.batchExecSql(paramMap);

				//再写一份到清理王数据库
				try {
					umengSynLogMapper.batchExecSql(paramMap);
				}catch (Exception e){
					System.out.println("dn_channel_info 复制到清理王数据库失败");
					e.printStackTrace();
				}

    		} catch (Exception e) {
    			e.printStackTrace();
    			return "{\"ret\":0,\"msg\":\"出现错误，请稍后重试！\"}";
    		}
    			
            JSONObject result = new JSONObject();
        	result.put("ret",1);
            result.put("msg","操作成功");
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"出现错误，请稍后重试！\"}";
        }
    }
    
    /**
     * 查询变现平台明细的报价ecpm
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/app/getDnChaCashList", method={RequestMethod.GET,RequestMethod.POST})
    public String getDnChaCashList(HttpServletRequest request, HttpServletResponse response) {
    	
        try{
            
            String day = request.getParameter("date");
            if(BlankUtils.sqlValidate(day)){
            	return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }
            
            String query = "select ad_sid adsid,placement_id `code`,IFNULL(TRUNCATE(sum(aa.revenue)/sum(aa.pv)*1000,2),0) ecpm "+
    				"from dn_cha_cash_total aa where date = '"+day+"' and app_id != '0' and ad_sid is not null and ad_sid != '' "+
    				"GROUP BY ad_sid,placement_id";
            List<Map<String, Object>> queryList = adService.queryListMap(query);
            
            JSONObject result = new JSONObject();
        	result.put("ret",1);
        	result.put("data",queryList);
            result.put("msg","操作成功");
            return result.toJSONString();
            
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"出现错误，请稍后重试！\"}";
        }
    }
    
    /**
     * 同步变现收入预估数据
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/app/syncIncomeData", method={RequestMethod.POST})
    public String syncIncomeData(HttpServletRequest request, HttpServletResponse response) {
    	
    	try{
    		
    		String day = request.getParameter("tdate");
    		if(BlankUtils.checkBlank(day))
    			day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
    		
    		boolean resp = adService.syncIncomeData(day);
    		if(resp)
    			return "{\"ret\":1,\"msg\":\"同步成功！\"}";
    		else
    			return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
    	}catch(Exception e){
    		e.printStackTrace();
    		return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
    	}
    }
    
    /**
     * 同步变现收入预估数据.v2
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/app/syncIncomeDataTwo", method={RequestMethod.GET,RequestMethod.POST})
    public String syncIncomeDataTwo(HttpServletRequest request, HttpServletResponse response) {
    	
    	try{
    		
    		String day = request.getParameter("tdate");
    		if(BlankUtils.checkBlank(day))
    			day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
    		
    		/** 通过异步执行，不接收返回值 */
    		adService.syncIncomeDataTwo(day);
    		if(true)
    			return "{\"ret\":1,\"msg\":\"同步执行成功，请稍后查看数据！\"}";
    		else
    			return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
    	}catch(Exception e){
    		e.printStackTrace();
    		return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
    	}
    }
    
    /**
     * 同步变现收入预估数据.友盟
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/app/syncIncomeDataUmeng", method={RequestMethod.POST})
    public String syncIncomeDataUmeng(HttpServletRequest request, HttpServletResponse response) {
    	
    	try{
    		
    		String day = request.getParameter("tdate");
    		if(BlankUtils.checkBlank(day))
    			day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
    		
    		boolean resp = adService.syncIncomeDataUmeng(day);
    		if(resp)
    			return "{\"ret\":1,\"msg\":\"同步成功！\"}";
    		else
    			return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
    	}catch(Exception e){
    		e.printStackTrace();
    		return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
    	}
    }
    
    /**
     * 同步聚合综合查询
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/app/syncGroupCom", method={RequestMethod.POST})
    public String syncGroupCom(HttpServletRequest request, HttpServletResponse response) {
    	
    	try{
    		
    		String day = request.getParameter("tdate");
    		if(BlankUtils.checkBlank(day))
    			day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
    		
    		boolean resp = adService.syncGroupCom(day);
    		if(resp)
    			return "{\"ret\":1,\"msg\":\"同步成功！\"}";
    		else
    			return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
    	}catch(Exception e){
    		e.printStackTrace();
    		return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
    	}
    }
	
	/**
     * 小游戏互推-广告位配置调整 查询
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/ad/selectGamePushConfig", method={RequestMethod.GET, RequestMethod.POST})
	public String selectGamePushConfig(String pos, String adSwitch, HttpServletRequest request) throws IOException {
		
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		JSONObject result = new JSONObject();
		try {
			NpPostVo2 np = new NpPostVo2();
			np.setParam1(pos);
			np.setParam2(adSwitch);
			
			String sql = "select * from wx_pushpos_config where 1=1 ";
			if(!BlankUtils.checkBlank(np.getParam1()))
				sql += " and pos = #{obj.param1}";
			if(!BlankUtils.checkBlank(np.getParam2()))
				sql += " and adSwitch = #{obj.param2}";
			
			sql = sql + " order by id desc ";
			
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<Map<String, Object>> list = adService.queryListMapTwo(sql, np);
			long size = ((Page) list).getTotal();
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}
	
	/**
	 * 小游戏互推-广告位配置调整 新增修改删除操作
	 * @param handle
	 * @param app
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 * @throws Exception
	 */
	@RequestMapping(value="/ad/gamePushConfigHandle", method={RequestMethod.POST})
	public String gamePushConfigHandle(String handle,
			HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		int result = 0;
		try {
			NpPostVo2 np = new NpPostVo2();
			np.setParam1(request.getParameter("pos"));
			np.setParam2(request.getParameter("adSwitch"));
			np.setParam3(request.getParameter("startLv"));
			np.setParam4(request.getParameter("intevalType"));
			np.setParam5(request.getParameter("intervalValue"));
			np.setParam7(request.getParameter("id"));
			
			
			String sql = null;
			if("add".equals(handle)){
				sql = "insert into wx_pushpos_config(pos,adSwitch,startLv,intevalType,intervalValue ) "+
					"values(#{obj.param1},#{obj.param2},#{obj.param3},#{obj.param4},#{obj.param5} ) ";
				
			}else if("edit".equals(handle)){
				sql = "update wx_pushpos_config set pos=#{obj.param1},adSwitch=#{obj.param2},startLv=#{obj.param3},intevalType=#{obj.param4},intervalValue=#{obj.param5} "+
					"where id=#{obj.param7} ";
				
			}else if("del".equals(handle)){
				sql = "delete from wx_pushpos_config where id=#{obj.param7} ";
			}
			
			result = adService.execSqlHandle(sql, np);
			if(result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";
				
		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}
	
	/**
	 * 广告-热云参数配置V2.查询
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/advert/app/selectDnwxReyunConfigV2", method={RequestMethod.GET, RequestMethod.POST})
	public String selectDnwxReyunConfigV2(HttpServletRequest request,HttpServletResponse response) throws IOException {
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		String[] args = {"appid","cha_id","pid","adType","statu","cuser","event","eventType","buy_id","buy_act"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);
		
		JSONObject result = new JSONObject();
		try {
			String sql = "select * from dnwx_reyun_config_two where pid != 333351";
			if(!BlankUtils.checkBlank(paramMap.get("appid")))
				sql += " and appid = #{obj.appid}";
			if(!BlankUtils.checkBlank(paramMap.get("cha_id")))
				sql += " and cha_id = #{obj.cha_id}";
			if(!BlankUtils.checkBlank(paramMap.get("pid")))
				sql += " and pid = #{obj.pid}";
			if(!BlankUtils.checkBlank(paramMap.get("adType")))
				sql += " and adType = #{obj.adType}";
			if(!BlankUtils.checkBlank(paramMap.get("statu")))
				sql += " and statu = #{obj.statu}";
			if(!BlankUtils.checkBlank(paramMap.get("event")))
				sql += " and event = #{obj.event}";
			if(!BlankUtils.checkBlank(paramMap.get("eventType")))
				sql += " and eventType = #{obj.eventType}";
			if(!BlankUtils.checkBlank(paramMap.get("buy_id")))
				sql += " and buy_id = #{obj.buy_id}";
			if(!BlankUtils.checkBlank(paramMap.get("buy_act")))
				sql += " and buy_act = #{obj.buy_act}";
			if(!BlankUtils.checkBlank(paramMap.get("cuser")))
				sql += " and cuser = #{obj.cuser}";
			
			PageHelper.startPage(paramMap);
			List<Map<String, Object>> collect = adService.queryListMapTwo(sql, paramMap);
			List<DnwxReyunConfigVo> list = JSONArray.parseArray(JSONArray.toJSONString(collect), DnwxReyunConfigVo.class);
			long size = ((Page) collect).getTotal();
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}
	
	/**
	 * 广告-热云参数配置V2.操作
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value = "/advert/app/dnwxReyunConfigV2Handle", method={RequestMethod.GET,RequestMethod.POST})
	public String dnwxReyunConfigV2Handle(DnwxReyunConfigVo record ,HttpServletRequest request, HttpServletResponse response) {
		
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		Object object = redisTemplate.opsForValue().get(token);
		JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(object));

		int result = 0;
		try {
			record.setCuser(json.getString("login_name"));
			record.setEuser(json.getString("login_name"));
			
			/** 当广告类型为null时手动赋值，保证绝对有值 -caow.2022.08.15 */
            if(BlankUtils.checkBlank(record.getAdType())){
            	record.setAdType("0");
            }
			
			if ("add".equals(request.getParameter("handle"))) {
				String sql = "insert into dnwx_reyun_config_two(appid,cha_id,pid,buy_id,buy_act,`event`,eventType,times,`loop`,rate,adType,ecpmValue,ecpmType,action,timeType,checkTimes,levelType,`level`,statu,cuser,createtime,euser,endtime) "+
						"value(#{obj.appid},#{obj.cha_id},#{obj.pid},#{obj.buy_id},#{obj.buy_act},#{obj.event},#{obj.eventType},#{obj.times},#{obj.loop},#{obj.rate},#{obj.adType},#{obj.ecpmValue},#{obj.ecpmType},#{obj.action},#{obj.timeType},#{obj.checkTimes},#{obj.levelType},#{obj.level},#{obj.statu},#{obj.cuser},now(),#{obj.euser},now())";
				result = adService.execSqlHandle(sql, record);
				
			} else if ("edit".equals(request.getParameter("handle"))) {
				String sql = "update dnwx_reyun_config_two set appid=#{obj.appid},cha_id=#{obj.cha_id},pid=#{obj.pid},buy_id=#{obj.buy_id},buy_act=#{obj.buy_act},`event`=#{obj.event},eventType=#{obj.eventType},times=#{obj.times},`loop`=#{obj.loop},rate=#{obj.rate},adType=#{obj.adType},ecpmValue=#{obj.ecpmValue},ecpmType=#{obj.ecpmType},action=#{obj.action},timeType=#{obj.timeType},checkTimes=#{obj.checkTimes},levelType=#{obj.levelType},`level`=#{obj.level},statu=#{obj.statu},euser=#{obj.euser},endtime=now() "+
						"where id=#{obj.id}";
				result = adService.execSqlHandle(sql, record);
				
			} else if ("del".equals(request.getParameter("handle"))) {
				String sql = "delete from dnwx_reyun_config_two where id in ("+record.getId()+") ";
				result = adService.execSqlHandle(sql, record);
			}
			
			if (result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";

		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}
	
}
