package com.wbgame.controller.clean;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.ApiNeed;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.CleanYdMapper;
import com.wbgame.mapper.clean.master.SuperSafeControlV3Mapper;
import com.wbgame.mapper.slave2.YdSlave2Mapper;
import com.wbgame.pojo.AppWbPayInfo;
import com.wbgame.pojo.clean.*;
import com.wbgame.pojo.clean.coins.CoinsConfig;
import com.wbgame.pojo.clean.coins.CoinsWithdrawConfig;
import com.wbgame.pojo.clean.push.ConfigVo;
import com.wbgame.pojo.custom.AdMsgTotalVo2;
import com.wbgame.pojo.custom.AdMsgTotalVo3;
import com.wbgame.pojo.mobile.hw.result;
import com.wbgame.service.AdService;
import com.wbgame.service.clean.CleanSomeService;
import com.wbgame.service.clean.CleanYdService;
import com.wbgame.service.clean.ISuperLockGroupService;
import com.wbgame.utils.*;
import io.swagger.annotations.*;
import jxl.Sheet;
import jxl.Workbook;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019年12月23日
 */

@CrossOrigin
@RestController
@RequestMapping("/clean")
@Api(tags = "清理王")
@ApiSupport(author = "zhnagyong")
public class CleanYdController {

	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private CleanYdService ydService;
	@Autowired
	private CleanSomeService someService;
	@Autowired
	private CleanYdMapper ydMapper;
	@Autowired
	CleanYdMapper cleanYdMapper;
	@Autowired
	AdService adService;
	@Autowired
	YdSlave2Mapper ydSlave2Mapper;

	@Autowired
    SuperSafeControlV3Mapper superSafeControlV3Mapper;

	@Autowired
	private ISuperLockGroupService superLockGroupService;

	/**
	 * 超级清理王-渠道新闻页参数配置 查询
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectDnChannelConfig", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectDnChannelConfig(DnCleanChannelConfig dnCleanChannelConfig, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<DnCleanChannelConfig> list = ydService.selectDnChannelConfig(dnCleanChannelConfig);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 超级清理王-渠道新闻页参数配置 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectDnChannelConfigHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectDnChannelConfigHandle(DnCleanChannelConfig dnCleanChannelConfig, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		dnCleanChannelConfig.setUpdateUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydService.insertDnCleanChannelConfig(dnCleanChannelConfig);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydService.updateDnCleanChannelConfig(dnCleanChannelConfig);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydService.deleteDnCleanChannelConfig(dnCleanChannelConfig);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 超级清理王-渠道新闻页参数配置 打开关闭
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/updateDnChannelConfigOnOff", method = { RequestMethod.GET, RequestMethod.POST })
	public String updateDnChannelConfigOnOff(DnCleanChannelConfig dnCleanChannelConfig, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;
		dnCleanChannelConfig.setUpdateUser(username);
		result = ydService.updateDnChannelConfigOnOff(dnCleanChannelConfig);
		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	@CrossOrigin
	@RequestMapping(value = "/ad/getCityListTab", method = { RequestMethod.GET, RequestMethod.POST })
	public @ResponseBody String getCityListTab(HttpServletRequest request, HttpServletResponse response) {

		String province = "select id as `value`,`name` as label from np_province";
		String city = "select id as `value`,`name` as label,provice_id from np_city";
		List<Map<String, Object>> pList = ydService.queryListMap(province);
		List<Map<String, Object>> cList = ydService.queryListMap(city);
		for (Map<String, Object> pro : pList) {

			List<Map<String, Object>> children = new ArrayList<Map<String, Object>>();
			for (Map<String, Object> ct : cList) {
				if (pro.get("value").toString().equals(ct.get("provice_id").toString())) {
					children.add(ct);
				}
			}
			pro.put("children", children);
		}

		JSONObject result = new JSONObject();

		result.put("ret", 1);
		result.put("data", pList);

		return result.toJSONString();
	}

	/**
	 * 批量复制
	 *
	 * @param cu
	 * @param request
	 * @param response
	 * @return
	 */
	@CrossOrigin
	@RequestMapping(value = "/batchCopyNewAdOpenConfig", method = { RequestMethod.POST })
	public @ResponseBody String batchCopyNewAdOpenConfig(CurrUserVo cu, HttpServletRequest request,
			HttpServletResponse response) {
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		String opid = BlankUtils.checkNull(request, "con_oldPiad");
		String npid = BlankUtils.checkNull(request, "con_newPid");
		List<ConfigVo> list = someService.selectNewAdOpenConfigByPrj(opid);
		for (ConfigVo configVo : list) {
			configVo.setPrjmid(npid);
		}
		int res = someService.batchInsertNewAdOpenConfig(list);
		/*
		 * if(res>0){ refushCacheByPid(npid); }
		 */
		JSONObject result = new JSONObject();
		if (res > 0) {
			result.put("ret", 1);
			result.put("msg", "操作成功");
		} else {
			result.put("ret", 0);
			result.put("msg", "操作失败");
		}
		return result.toJSONString();
	}

	/**
	 * 批量复制广告位
	 *
	 * @param cu
	 * @param request
	 * @param response
	 * @return
	 */
	@CrossOrigin
	@RequestMapping(value = "/batchCopyNewAdNameOpenConfig", method = { RequestMethod.POST })
	public @ResponseBody String batchCopyNewAdNameOpenConfig(CurrUserVo cu, HttpServletRequest request,
			HttpServletResponse response) {
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		String seqids = BlankUtils.checkNull(request, "con_seqid");
		String npid = BlankUtils.checkNull(request, "con_oldPid");
		String name = BlankUtils.checkNull(request, "con_new_name");

		List<ConfigVo> list = someService.selectNewAdNameOpenConfig(seqids);

		for (ConfigVo configVo : list) {
			configVo.setName(name);
		}
		int res = someService.batchInsertNewAdOpenConfig(list);
		/*
		 * if(res>0){ refushCacheByPid(npid); }
		 */
		JSONObject result = new JSONObject();
		if (res > 0) {
			result.put("ret", 1);
			result.put("msg", "操作成功");
		} else {
			result.put("ret", 0);
			result.put("msg", "操作失败");
		}
		return result.toJSONString();

	}

	/**
	 * 批量复制广告源
	 *
	 * @param cu
	 * @param request
	 * @param response
	 * @return
	 */
	@CrossOrigin
	@RequestMapping(value = "/batchCopyNewSidAdOpenConfig", method = { RequestMethod.POST })
	public @ResponseBody String batchCopyNewSidAdOpenConfig(CurrUserVo cu, HttpServletRequest request,
			HttpServletResponse response) {
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		String seqids = BlankUtils.checkNull(request, "con_seqid");
		String npid = BlankUtils.checkNull(request, "con_oldPid");
		String sid = BlankUtils.checkNull(request, "con_sid_str");

		List<ConfigVo> list = someService.selectNewAdNameOpenConfig(seqids);

		for (ConfigVo configVo : list) {
			configVo.setAd_sid_str(sid);
		}
		int res = someService.batchInsertNewAdOpenConfig(list);
		/*
		 * if(res>0){ refushCacheByPid(npid); }
		 */
		JSONObject result = new JSONObject();
		if (res > 0) {
			result.put("ret", 1);
			result.put("msg", "操作成功");
		} else {
			result.put("ret", 0);
			result.put("msg", "操作失败");
		}
		return result.toJSONString();

	}

	/**
	 * 批量修改
	 *
	 * @param cu
	 * @param request
	 * @param response
	 * @return
	 */
	@CrossOrigin
	@RequestMapping(value = "/batchUpdateNewAdOpenConfig", method = { RequestMethod.POST })
	public @ResponseBody String batchUpdateNewAdConfig(CurrUserVo cu, HttpServletRequest request,
			HttpServletResponse response) {
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		String ad_sid = BlankUtils.checkNull(request, "ad_sid");
		String[] arr = ad_sid.split(",");
		List<String> slist = new ArrayList<>();
		for (String str : arr) {
			slist.add(str);
		}
		String old_key = BlankUtils.checkNull(request, "old_key");
		String new_key = BlankUtils.checkNull(request, "new_key");
		String new_statu = BlankUtils.checkNull(request, "new_statu");
		String new_city = BlankUtils.checkNull(request, "new_city");
		String ratio = BlankUtils.checkNull(request, "ratio");
		String agentPer = BlankUtils.checkNull(request, "agentPer");// 分配比例
		String showmodel = BlankUtils.checkNull(request, "showmodel");
		String round = BlankUtils.checkNull(request, "round");
		String limit_num = BlankUtils.checkNull(request, "limit_num");
		String limit_second = BlankUtils.checkNull(request, "limit_second");
		String con_oldPiad = BlankUtils.checkNull(request, "con_oldPiad");
		List<ConfigVo> list = someService.selectNewAdOpenConfigInfoById(slist);
		int ret;
		for (ConfigVo configVo : list) {
			configVo.setAd_sid_str(configVo.getAd_sid_str().replaceAll(old_key, new_key));
			if (new_statu != null && !new_statu.equals("")) {
				configVo.setActiv_statu(new_statu);
			}
			if (new_city != null && !new_city.equals("")) {
				configVo.setActiv_cityid(new_city);
			}
			if (ratio != null && !ratio.equals("")) {
				configVo.setRate(ratio);// 2019.3.19 yangk
										// 批量修改时杨玉振要求修改展示百分比字段不是分配比例字段
			}
			if (!StringUtils.isEmpty(agentPer)) {
				configVo.setAgentpecent(agentPer);// 2019.3.20 yangk
													// 批量修改时杨玉振要求增加分配比例字段
			}
			if (showmodel != null && !showmodel.equals("")) {
				configVo.setShowmodel(showmodel);
			}
			if (round != null && !round.equals("")) {
				configVo.setRound(round);
			}
			if (limit_num != null && !limit_num.equals("")) {
				configVo.setLimit_num(limit_num);
			}
			if (limit_second != null && !limit_second.equals("")) {
				configVo.setLimit_second(limit_second);
			}

		}
		ret = someService.batchupdateNewAdOpenConfig(list);
		/*
		 * if(ret>0){ refushCacheByPid(con_oldPiad); }
		 */
		JSONObject result = new JSONObject();
		if (ret > 0) {
			result.put("ret", 1);
			result.put("msg", "操作成功");
		} else {
			result.put("ret", 0);
			result.put("msg", "操作失败");
		}
		return result.toJSONString();

	}

	/**
	 * 新版广告开通配置
	 *
	 * @param cu
	 * @param request
	 * @param response
	 * @return
	 */
	@CrossOrigin
	@RequestMapping(value = "/selectNewAdOpenConfig", method = { RequestMethod.GET, RequestMethod.POST })
	public @ResponseBody String selectNewAdOpenConfig(CurrUserVo cu, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		String ad_sid = BlankUtils.checkNull(request, "ad_sid");
		String prjid = BlankUtils.checkNull(request, "ad_bd_sid");
		String status = BlankUtils.checkNull(request, "app_id");
		String sc_name = BlankUtils.checkNull(request, "sc_name");
		Map<String, String> parmMap = new HashMap<>();
		parmMap.put("ad_sid", ad_sid);
		parmMap.put("prjid", prjid);
		parmMap.put("status", status);
		parmMap.put("sc_name", sc_name);
		/*
		 * if("technical_test".equals(cuser.getOrg_id())){ // 技术部测试
		 * 112233,445561,778898,3333开头id String testids =
		 * "'112233','445561','778898'"; if(StringUtils.isEmpty(prjid) ||
		 * (!testids.contains(prjid) && !prjid.startsWith("3333"))){ // 添加额外条件
		 * parmMap.remove("prjid"); parmMap.put("testids", testids); } }
		 */

		/*
		 * if("xianghh".equals(cuser.getLogin_name())){ // 控制只显示指定应用项目
		 * Map<String, Map<String, Object>> pMap =
		 * adService.selectProjectidChannelMap(); List<String> collect =
		 * pMap.values().stream() .filter(act ->
		 * "37673".equals(act.get("appid")+"")) .map(act ->
		 * act.get("projectid")+"") .collect(Collectors.toList());
		 * parmMap.put("prjids", String.join(",", collect)); }
		 */

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<ConfigVo> list = someService.selectNewAdOpenConfigForSql(parmMap);

		String lock = "select pid from project_switch_lock where adc = 1";
		List<String> lockList = ydService.queryListString(lock);
		list.forEach(cf -> {
			if (lockList.contains(cf.getPrjmid()))
				cf.setIslock("1");
			else
				cf.setIslock("0");
		});

		long size = new PageInfo(list).getTotal();
		JSONObject result = new JSONObject();
		result.put("data", list);
		result.put("ret", 1);
		result.put("totalCount", size);
		return result.toJSONString();

	}

	/**
	 * 新版广告开通配置 修改新增
	 *
	 * @param cu
	 * @param request
	 * @param response
	 * @return
	 */
	@CrossOrigin
	@RequestMapping(value = "/handleNewAdOpenConfig", method = { RequestMethod.POST })
	public @ResponseBody String handleNewAdOpenConfig(CurrUserVo cu, HttpServletRequest request,
			HttpServletResponse response) {
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		int ret = 0;
		String status = BlankUtils.checkNull(request, "status");
		String ad_id = BlankUtils.checkNull(request, "ad_id");
		String addprjmid = BlankUtils.checkNull(request, "addprjmid");
		String adtype = BlankUtils.checkNull(request, "adtype");
		String add_pos = BlankUtils.checkNull(request, "add_pos");
		String add_rate = BlankUtils.checkNull(request, "add_rate");
		String add_agentpecent = BlankUtils.checkNull(request, "add_agentpecent");
		// String add_delaytime = BlankUtils.checkNull(request,
		// "add_delaytime");
		String add_round = BlankUtils.checkNull(request, "add_round");
		String city_statu = BlankUtils.checkNull(request, "city_statu");
		String cityid = BlankUtils.checkNull(request, "cityid");
		String add_telecom = BlankUtils.checkNull(request, "add_telecom");
		String handle = BlankUtils.checkNull(request, "handle");
		String seqid = BlankUtils.checkNull(request, "seqid");
		String limit_num = BlankUtils.checkNull(request, "limit_num");
		// String limit_date = BlankUtils.checkNull(request,"limit_date");
		String limit_second = BlankUtils.checkNull(request, "limit_second");
		String showmodel = BlankUtils.checkNull(request, "showmodel");
		ConfigVo infovo = new ConfigVo();
		infovo.setActiv_cityid(cityid);
		infovo.setActiv_statu(city_statu);
		infovo.setActiv_telecom(add_telecom);
		infovo.setAd_sid_str(ad_id);
		infovo.setAgentpecent(add_agentpecent);

		infovo.setDelaytime("0");
		infovo.setName(add_pos);
		infovo.setPrjmid(addprjmid);
		infovo.setRate("100");
		infovo.setRound(add_round);
		infovo.setStatu(status);
		infovo.setType(adtype);
		infovo.setSeqid(seqid);
		infovo.setRate(add_rate);
		infovo.setLimit_num(limit_num);
		infovo.setLimit_date("0");
		infovo.setLimit_second(limit_second);
		infovo.setShowmodel(showmodel);

		String lock = "select pid from project_switch_lock where pid = '" + addprjmid + "' and adc = 1";
		List<String> lockList = ydService.queryListString(lock);
		if (lockList != null && lockList.size() > 0) {
			return "{\"ret\":0,\"msg\":\"项目状态被锁定，暂时不允许修改！\"}";
		}

		/*
		 * if("xianghh".equals(cuser.getLogin_name())){ Map<String, Map<String,
		 * Object>> pMap = adService.selectProjectidChannelMap(); List<String>
		 * collect = pMap.values().stream() .filter(act ->
		 * "37673".equals(act.get("appid")+"")) .map(act ->
		 * act.get("projectid")+"") .collect(Collectors.toList());
		 * 
		 * if(!collect.contains(addprjmid)){ return
		 * "{\"ret\":0,\"msg\":\"暂时不允许操作 超级清理王 以外的项目！\"}"; } }
		 */

		if ("add".equals(handle)) {
			ret = someService.insertNewAdOpenConfig(infovo);
		}
		if ("update".equals(handle)) {
			ret = someService.updateNewAdOpenConfig(infovo);
		}
		/*
		 * if(ret > 0){ refushCacheByPid(addprjmid); }
		 */
		JSONObject result = new JSONObject();
		if (ret > 0) {
			result.put("ret", 1);
			result.put("msg", "操作成功");
		} else {
			result.put("ret", 0);
			result.put("msg", "操作失败");
		}
		return result.toJSONString();

	}

	/**
	 * 新版广告开通配置 删除
	 *
	 * @param cu
	 * @param request
	 * @param response
	 * @return
	 */
	@CrossOrigin
	@RequestMapping(value = "/deleteNewAdOpenConfig", method = { RequestMethod.POST })
	public @ResponseBody String deleteNewAdOpenConfig(CurrUserVo cu, HttpServletRequest request,
			HttpServletResponse response) {

		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		String seqids = BlankUtils.checkNull(request, "seqid");
		JSONObject result = new JSONObject();
		Map<Object, Object> map = new HashMap<>();
		map.put("seqids", seqids);
		int res = someService.deleteNewAdOpenConfig(map);
		/*
		 * if(res > 0){ refushCacheByPid(seqids); }
		 */
		if (res > 0) {
			result.put("ret", 1);
			result.put("msg", "删除成功");
		} else {
			result.put("ret", 0);
			result.put("msg", "删除失败");
		}
		return result.toJSONString();

	}

	/**
	 * 新版广告开通配置 打开/关闭
	 *
	 * @param cu
	 * @param request
	 * @param response
	 * @return
	 */
	@CrossOrigin
	@RequestMapping(value = "/openNewAdOpenConfig", method = { RequestMethod.POST })
	public @ResponseBody String openNewAdOpenConfig(CurrUserVo cu, HttpServletRequest request,
			HttpServletResponse response) {

		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		String refresh_pid = BlankUtils.checkNull(request, "refresh_pid");
		String ad_sids = BlankUtils.checkNull(request, "ad_sids");
		String pids_con = BlankUtils.checkNull(request, "pids_con");
		pids_con = pids_con.split(",")[0];
		String statu = BlankUtils.checkNull(request, "statu");
		if (statu.equals("0")) {
			statu = "1";
		} else {
			statu = "0";
		}

		String lock = "select pid from project_switch_lock where pid = '" + refresh_pid + "' and adc = 1";
		List<String> lockList = ydService.queryListString(lock);
		if (lockList != null && lockList.size() > 0) {
			return "{\"ret\":0,\"msg\":\"项目状态被锁定，暂时不允许修改！\"}";
		}

		// String pids_con = BlankUtils.checkNull(request, "pids_con");
		Map<Object, Object> map = new HashMap<>();
		map.put("ids", ad_sids);
		map.put("statu", statu);
		JSONObject result = new JSONObject();
		int res = someService.openNewAdOpenConfig(map);
		/*
		 * if(res > 0){ refushCacheByPid(pids_con); }
		 */
		if (res > 0) {
			result.put("ret", 1);
			result.put("msg", "操作成功");
		} else {
			result.put("ret", 0);
			result.put("msg", "操作失败");
		}
		return result.toJSONString();

	}

	/**
	 * 移动-用户信息维护 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/super/selectSuperGameConfig", method = RequestMethod.POST)
	public String selectSuperGameConfig(SuperGameConfig superGameConfig, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperGameConfig> list = ydService.selectSuperGameConfig(superGameConfig);
		long size = ((Page) list).getTotal();

		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 移动-用户信息维护 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/super/superGameConfigHandle", method = RequestMethod.POST)
	public String superGameConfigHandle(String value, HttpServletRequest request, SuperGameConfig superGameConfig,
			HttpServletResponse response) {

		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		int result = 0;

		if ("add".equals(request.getParameter("handle"))) {
			try {
				result = ydService.insertSuperGameConfig(superGameConfig);
			} catch (DataAccessException e) {
				return "{\"ret\":0,\"msg\":\"存在相同的id无法新增!\"}";
			}
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydService.updateSuperGameConfig(superGameConfig);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydService.deleteSuperGameConfig(superGameConfig);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 超级清理王-pid新闻页参数配置 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectDnChannelPid", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectDnChannelPid(DnCleanChannelConfig dnCleanChannelConfig, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<DnCleanChannelConfig> list = ydService.selectDnChannelPid(dnCleanChannelConfig);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 超级清理王-渠道新闻页参数配置 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectDnChannelPidHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectDnChannelPidHandle(DnCleanChannelConfig dnCleanChannelConfig, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		dnCleanChannelConfig.setUpdateUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydService.insertDnCleanChannelPid(dnCleanChannelConfig);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydService.updateDnCleanChannelPid(dnCleanChannelConfig);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydService.deleteDnCleanChannelPid(dnCleanChannelConfig);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 超级清理王-渠道新闻页参数配置 打开关闭
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/updateDnChannelPidOnOff", method = { RequestMethod.GET, RequestMethod.POST })
	public String updateDnChannelPidOnOff(DnCleanChannelConfig dnCleanChannelConfig, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;
		dnCleanChannelConfig.setUpdateUser(username);
		result = ydService.updateDnChannelPidOnOff(dnCleanChannelConfig);
		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 查询广告插屏间隔配置
	 *
	 * @param cu
	 * @param request
	 * @param response
	 * @return
	 */
	@CrossOrigin
	@RequestMapping(value = "/selectAdTableConfig", method = { RequestMethod.POST })
	public @ResponseBody String selectAdTableConfig(CurrUserVo cu, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String orgid = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			orgid = currUserVo.getOrg_id();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			orgid = WaibaoUser.getOrg_id();
		}
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		// 当前页
		int pageNo = (pageStart / pageSize) + 1;
		PageHelper.startPage(pageNo, pageSize); // 进行分页
		String prjid = BlankUtils.checkNull(request, "adc_prjmid");
		Map<Object, Object> parmMap = new HashMap<>();
		parmMap.put("prjmid", prjid);
		if ("technical_test".equals(orgid)) { // 技术部测试
												// 112233,445561,778898,3333开头id
			String testids = "'112233','445561','778898'";
			if (StringUtils.isEmpty(prjid) || (!testids.contains(prjid) && !prjid.startsWith("3333"))) {
				// 添加额外条件
				parmMap.remove("prjmid");
				parmMap.put("testids", testids);
			}
		}

		List<NpActiveFree> list = ydService.selectAdTableConfig(parmMap);
		long size = new PageInfo(list).getTotal();
		JSONObject result = new JSONObject();
		result.put("data", list);
		result.put("ret", 1);
		result.put("totalCount", size);
		return result.toJSONString();

	}

	/**
	 * 广告插屏间隔配置操作
	 *
	 * @param np
	 * @param handle
	 * @param cu
	 * @param request
	 * @param response
	 * @return
	 */
	@CrossOrigin
	@RequestMapping(value = "/handleAdTableConfig", method = { RequestMethod.POST })
	public @ResponseBody String handleAdTableConfig(NpActiveFree np, String handle, CurrUserVo cu,
			HttpServletRequest request, HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}
		int ret = 0;
		if ("add".equals(handle)) {
			try {
				ret = ydService.insertAdTableConfig(np);
			} catch (DataAccessException e) {
				return "{\"ret\":0,\"msg\":\"存在相同的pid无法新增!\"}";
			}

		}
		if ("update".equals(handle)) {
			ret = ydService.updateAdTableConfig(np);
		}
		JSONObject result = new JSONObject();
		if (ret > 0) {
			result.put("ret", 1);
			result.put("msg", "操作成功");
		} else {
			result.put("ret", 0);
			result.put("msg", "操作失败");
		}
		return result.toJSONString();
	}

	/**
	 * 广告插屏间隔配置复制
	 *
	 * @param np
	 * @param handle
	 * @param cu
	 * @param request
	 * @param response
	 * @return
	 */
	@CrossOrigin
	@RequestMapping(value = "/copyAdTableConfig", method = { RequestMethod.POST })
	public @ResponseBody String CopyAdTableConfig(NpActiveFree np, String handle, CurrUserVo cu,
			HttpServletRequest request, HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}
		int ret = 0;
		JSONObject result = new JSONObject();

		String oldPid = BlankUtils.checkNull(request, "con_oldPid");
		String newPid = BlankUtils.checkNull(request, "con_newPid");

		Map<Object, Object> parmMap = new HashMap<>();
		parmMap.put("prjmid", oldPid);
		List<NpActiveFree> list = ydService.selectAdTableConfig(parmMap);
		if (null != list && list.size() > 0) {
			list.get(0).setPrjmid(newPid);
		} else {
			result.put("ret", 0);
			result.put("msg", "重新选择");

			return result.toJSONString();
		}

		Map<Object, Object> Map = new HashMap<>();
		Map.put("prjmid", newPid);
		List<NpActiveFree> newlist = ydService.selectAdTableConfig(Map);
		if (null != newlist && newlist.size() > 0) {
			ret = ydService.updateAdTableConfig(list.get(0));
		} else {
			ret = ydService.insertAdTableConfig(list.get(0));
		}

		if (ret > 0) {
			result.put("ret", 1);
			result.put("msg", "操作成功");
		} else {
			result.put("ret", 0);
			result.put("msg", "操作失败");
		}
		return result.toJSONString();

	}

	@RequestMapping(value = "/createNewTable", method = { RequestMethod.GET, RequestMethod.POST })
	public @ResponseBody String createNewTable() {
		String[] table = ydMapper.selectNewTable();
		String month = DateTime.now().toString("yyyyMM");
		for (int i = 0; i < table.length; i++) {
			String tableName = table[i] + "_" + month;
			ydMapper.createNewTable(tableName);

		}
		return "ok";
	}

	@RequestMapping(value = "/createNewView", method = { RequestMethod.GET, RequestMethod.POST })
	public @ResponseBody String createNewView() {
		String[] table = ydMapper.selectNewTable();
		String month = DateTime.now().toString("yyyyMM");
		for (int i = 0; i < table.length; i++) {
			String tableName = table[i] + "_" + month;
			ydMapper.createNewView(table[i], tableName);
		}
		return "ok";
	}

	@RequestMapping(value = "/create", method = { RequestMethod.GET, RequestMethod.POST })
	public @ResponseBody String create() {
		String[] table = ydMapper.selectNewTable();
		String month = DateTime.now().toString("yyyyMM");
		for (int i = 0; i < table.length; i++) {
			String tableName = table[i];
			System.out.println("	@Scheduled(cron=\"03 */3 * * * ?\")");
			System.out.println("	public void syncDntjSuper_" + tableName + "(){");
			System.out.println("		DateTime dateTime = DateTime.now();");
			System.out.println("		String month = dateTime.toString(\"yyyyMM\");");
			System.out.println("");
			System.out.println("		boolean lock = lock(\"" + tableName
					+ "\", DateTime.now().plusSeconds(80).getMillis(), 80);");
			System.out.println("		if(lock){");
			System.out.println("			String ysql = \"insert into " + tableName
					+ "_\"+month+\"(`appid`, `prjid`, `sessionId`, `deviceId`, `versionName`, `channel`, `timestamp`, `eventId`, `param1`, `param2`) \"");
			System.out.println(
					"	 			+\" values(:appid, :prjid, :sessionId, :deviceId, :versionName, :channel, :timestamp, :eventId, :param1, :param2) \" ;");
			System.out.println("			int update = commonQueue(\"dntj_superclear_" + tableName + "\", ysql);");
			System.out.println("");
			System.out.println("			logger.info(\"执行dntj_superclear_" + tableName + " \" +update+ \"条\");");
			System.out.println("			}");
			System.out.println("		}");
			System.out.println("");
		}
		return month;

	}

	/**
	 * 超级清理王-清理王-tab控制 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperConfigTab", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperConfigTab(SuperConfigTab record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperConfigTab> list = ydService.selectSuperConfigTab(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 超级清理王-清理王-tab控制 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/superConfigTabHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String superConfigTabHandle(SuperConfigTab record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydService.insertSuperConfigTab(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydService.updateSuperConfigTab(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydService.deleteSuperConfigTab(record);
		} else if ("batch".equals(request.getParameter("handle"))) {
			if (!BlankUtils.checkBlank(record.getIds())) {
				result = ydService.updatebBatchTab(record);
			}
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 清理王-锁屏配置 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperConfigLock", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperConfigLock(SuperConfigLock record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperConfigLock> list = ydService.selectSuperConfigLock(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 清理王-锁屏配置 查询 先查旧版 在查新版
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperConfigLock/v2", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperConfigLockCheck(SuperConfigLock record, HttpServletRequest request,
			HttpServletResponse response) {
		JSONObject result = new JSONObject();

		List<SuperConfigLock> list = ydService.selectSuperConfigLock(record);

		SuperLockNew superLockNew = new SuperLockNew();
		superLockNew.setAppid(record.getAppid());
		superLockNew.setCha(record.getCha());
		superLockNew.setPrjid(record.getPrjid());
		superLockNew.setStatus(record.getStatus());
		List<SuperLockNew> list2 = ydMapper.selectSuperLockNew(superLockNew);

		result.put("ret", 1);
		if (list != null && list.size() > 0) {
			result.put("data", list);
		} else {
			result.put("data", list2);
		}
		return result.toJSONString();
	}

	/**
	 * 清理王-锁屏配置-操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/superConfigLockHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String superConfigLockHandle(SuperConfigLock record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydService.insertSuperConfigLock(record);
		} else if ("batch".equals(request.getParameter("handle"))) {
			if (!BlankUtils.checkBlank(record.getIds())) {
				result = ydService.updatebBatchLock(record);
			}
		}
		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 清理王-锁屏配置-修改
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/updateSuperConfigLock", method = { RequestMethod.GET, RequestMethod.POST })
	public String updateSuperConfigLock(SuperConfigLock record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);

		result = ydService.updateSuperConfigLock(record);

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 清理王-锁屏配置-状态修改
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/updateSuperConfigLockStatus", method = { RequestMethod.GET, RequestMethod.POST })
	public String updateSuperConfigLockStatus(SuperConfigLock record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);

		result = ydService.updateSuperConfigLockStatus(record);

		// 锁屏状态修改时增加对其他配置同步修改
		if (record.getCha() != null && record.getAppid() != null && !("").equals(record.getCha())
				&& !("").equals(record.getAppid())) {
			cleanYdMapper.execSql("update  super_config_deeplink set status = " + record.getStatus() + " where cha = '"
					+ record.getCha() + "' and appid = " + record.getAppid());
			cleanYdMapper.execSql("update  super_config_news set status = " + record.getStatus() + " where cha = '"
					+ record.getCha() + "' and appid = " + record.getAppid());
			cleanYdMapper.execSql("update  super_config_params set status = " + record.getStatus() + " where cha = '"
					+ record.getCha() + "' and appid = " + record.getAppid());
			cleanYdMapper.execSql("update  super_config_public set status = " + record.getStatus() + " where cha = '"
					+ record.getCha() + "' and appid = " + record.getAppid());
			cleanYdMapper.execSql("update  super_config_tab set status = " + record.getStatus() + " where cha = '"
					+ record.getCha() + "' and appid = " + record.getAppid());
		} else if (record.getPrjid() != null && !("").equals(record.getPrjid())) {
			cleanYdMapper.execSql("update  super_config_deeplink set status = " + record.getStatus() + " where prjid = "
					+ record.getPrjid());
			cleanYdMapper.execSql("update  super_config_news set status = " + record.getStatus() + " where prjid = "
					+ record.getPrjid());
			cleanYdMapper.execSql("update  super_config_params set status = " + record.getStatus() + " where prjid = "
					+ record.getPrjid());
			cleanYdMapper.execSql("update  super_config_public set status = " + record.getStatus() + " where prjid = "
					+ record.getPrjid());
			cleanYdMapper.execSql("update  super_config_tab set status = " + record.getStatus() + " where prjid = "
					+ record.getPrjid());
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 清理王-锁屏配置-删除
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/delSuperConfigLock", method = { RequestMethod.GET, RequestMethod.POST })
	public String delSuperConfigLock(SuperConfigLock record, HttpServletRequest request, HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);

		result = ydService.deleteSuperConfigLock(record);

		// 锁屏删除时增加对其他配置同步删除
		if (record.getCha() != null && record.getAppid() != null && !("").equals(record.getCha())
				&& !("").equals(record.getAppid())) {
			cleanYdMapper.execSql("delete from super_config_deeplink where cha = '" + record.getCha() + "' and appid = "
					+ record.getAppid());
			cleanYdMapper.execSql("delete from super_config_news where cha = '" + record.getCha() + "' and appid = "
					+ record.getAppid());
			cleanYdMapper.execSql("delete from super_config_params where cha = '" + record.getCha() + "' and appid = "
					+ record.getAppid());
			cleanYdMapper.execSql("delete from super_config_public where cha = '" + record.getCha() + "' and appid = "
					+ record.getAppid());
			cleanYdMapper.execSql("delete from super_config_tab where cha = '" + record.getCha() + "' and appid = "
					+ record.getAppid());
		} else if (record.getPrjid() != null && !("").equals(record.getPrjid())) {
			cleanYdMapper.execSql("delete from super_config_deeplink where prjid = " + record.getPrjid());
			cleanYdMapper.execSql("delete from super_config_news where prjid = " + record.getPrjid());
			cleanYdMapper.execSql("delete from super_config_params where prjid = " + record.getPrjid());
			cleanYdMapper.execSql("delete from super_config_public where prjid = " + record.getPrjid());
			cleanYdMapper.execSql("delete from super_config_tab where prjid = " + record.getPrjid());
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	@RequestMapping(value = "/wb/getAppNameList", method = { RequestMethod.GET, RequestMethod.POST })
	public String getAppNameList(String platform, HttpServletRequest request, HttpServletResponse response) {

		JSONObject result = new JSONObject();
		String where = " where channel_id = 51808"; // 小游戏平台，移动平台，广告统计平台
		if (platform != null && ("b".equals(platform) || "c".equals(platform))) {
			where = " where channel_id = 10118";
		}
		List<Map<String, Object>> list = ydMapper.selectAppInfoList(where);
		result.put("ret", 1);
		result.put("data", list);

		return result.toJSONString();
	}

	/**
	 * 清理王-新闻配置 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperConfigNews", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperConfigNews(SuperConfigNews record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		// token验证
		String token = request.getParameter("token");
		if ("dnwx1002dnwx1002".equals(token) || "wbtokenukpfllbbyt72hhamm0i7dr1g".equals(token)) {
			// 特殊不处理
		} else {
			// 校验权限
			if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
				return ReturnJson.error(Constants.ErrorToken);
			}
		}

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperConfigNews> list = ydService.selectSuperConfigNews(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 清理王-新闻配置 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/superConfigNewsHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String superConfigNewsHandle(SuperConfigNews record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		}

		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydService.insertSuperConfigNews(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydService.updateSuperConfigNews(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydService.deleteSuperConfigNews(record);
		} else if ("batch".equals(request.getParameter("handle"))) {
			if (!BlankUtils.checkBlank(record.getIds())) {
				result = ydService.updatebBatchNews(record);
			}
		}
		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 清理王-新闻Deeplink 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperConfigDeeplink", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperConfigDeeplink(SuperConfigDeeplink record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperConfigDeeplink> list = ydService.selectSuperConfigDeeplink(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 清理王-新闻Deeplink 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/superConfigDeeplinkHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String superConfigDeeplinkHandle(SuperConfigDeeplink record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);
		if (BlankUtils.checkBlank(record.getMaxNumber())) {
			record.setMaxNumber("-1");
		}
		if (BlankUtils.checkBlank(record.getMaxTimes())) {
			record.setMaxTimes("-1");
		}
		if ("add".equals(request.getParameter("handle"))) {
			result = ydService.insertSuperConfigDeeplink(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydService.updateSuperConfigDeeplink(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydService.deleteSuperConfigDeeplink(record);
		} else if ("batch".equals(request.getParameter("handle"))) {
			if (!BlankUtils.checkBlank(record.getIds())) {
				result = ydService.updatebBatchDeeplink(record);
			}
		}
		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 清理王-自定义 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@ApiOperation(value = "自定义参数配置 查询", notes = "自定义参数配置 查询", httpMethod = "POST")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "页码", name = "start", dataType = "String"),
			@ApiImplicitParam(value = "条数", name = "limit", dataType = "String")
	})
	@RequestMapping(value = "/selectSuperConfigParams", method = { RequestMethod.GET, RequestMethod.POST })
	public Result<List<SuperConfigParams>> selectSuperConfigParams(SuperConfigParams record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if ("dnwx1002dnwx1002".equals(token) || "wbtokenukpfllbbyt72hhamm0i7dr1g".equals(token)) {
			// 特殊不处理
		} else {
			// 校验权限
			if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
				return ResultUtils.failure(Constants.ErrorToken);
			}
		}

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();


		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperConfigParams> list = ydService.selectSuperConfigParams(record);
		long size = ((Page) list).getTotal();
		Result<List<SuperConfigParams>> success = ResultUtils.success(list);
		success.setTotalCount(size);
//		result.put("ret", 1);
//		result.put("data", list);
//		result.put("totalCount", size);

		return success;
	}

	/**
	 * 清理王-自定义 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@ApiOperation(value = "自定义参数编辑", notes = "自定义参数编辑", httpMethod = "POST")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "handle", value = "操作状态", dataType = "String"),
	})
	@RequestMapping(value = "/superConfigParamsHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public Result<String> superConfigParamsHandle(SuperConfigParams record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ResultUtils.failure(Constants.ErrorToken);
		}

		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);
		record.setCreateUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydService.insertSuperConfigParams(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydService.updateSuperConfigParams(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydService.deleteSuperConfigParams(record);
		}

		if (result > 0)
//			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			return ResultUtils.success();
		else
//			return "{\"ret\":0,\"msg\":\"无效操作!\"}";
		return ResultUtils.failure("无效操作");

	}

	/**
	 * 白名单配置查询
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectWhiteList", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectWhiteList(HttpServletRequest request, HttpServletResponse response,
			SuperWhiteList superWhiteList) {

		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}

		// pageStart 和 limit设置值
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperWhiteList> list = ydService.selectWhiteList(superWhiteList);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();

	}

	/**
	 * 白名单配置操作-新增
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/addWhiteList", method = { RequestMethod.GET, RequestMethod.POST })
	public String addWhiteList(SuperWhiteList superWhiteList, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}
		int result = 0;

		if (BlankUtils.checkBlank(superWhiteList.getType()) || BlankUtils.checkBlank(superWhiteList.getNum())) {
			return "{\"ret\":0,\"msg\":\"参数不能为空!\"}";
		}

		// 操作人
		superWhiteList.setCuser(username);
		result = ydService.insertWhiteList(superWhiteList);

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 白名单配置操作-删除
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/deleteWhiteList", method = { RequestMethod.GET, RequestMethod.POST })
	public String deleteWhiteList(SuperWhiteList superWhiteList, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		if (BlankUtils.checkBlank(superWhiteList.getType()) || BlankUtils.checkBlank(superWhiteList.getNum())) {
			return "{\"ret\":0,\"msg\":\"参数不能为空!\"}";
		}

		// 操作人
		superWhiteList.setCuser(username);
		result = ydService.deleteWhiteList(superWhiteList);

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 白名单配置导入
	 *
	 * @param file
	 * @param request
	 * @param response
	 * @throws IOException
	 */
	@CrossOrigin
	@RequestMapping(value = "/importWhiteList", method = { RequestMethod.POST })
	public String importWhiteList(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request,
			HttpServletResponse response) throws IOException {

		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setContentType("text/html;charset=utf-8");
		response.setCharacterEncoding("utf-8");
		// 记录可能异常的行
		int error = 0;
		try {
			PrintWriter out = response.getWriter();
			JSONObject obj = new JSONObject();

			if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
				List<SuperWhiteList> list = new ArrayList<>();
				Workbook workbook = Workbook.getWorkbook(file.getInputStream());
				Sheet sheet = workbook.getSheet(0);

				int column = sheet.getColumns();
				int row = sheet.getRows();
				SuperWhiteList swl;
				for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
					error = r + 1;
					if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
						continue;

					String[] vals = new String[column];
					for (int c = 0; c < column; c++) {
						vals[c] = sheet.getCell(c, r).getContents();
					}

					swl = new SuperWhiteList();

					// 类型有误
					if (!(WhiteListType.IMEI.equals(vals[0]) || WhiteListType.LSN.equals(vals[0]))) {
						obj.put("ret", 0);
						obj.put("msg", "第" + error + "行： '" + vals[0] + "' 类型有误");
						out.print(obj.toString());
						out.close();
						return null;
					}

					// 非空验证
					if (BlankUtils.checkBlank(vals[1])) {
						obj.put("ret", 0);
						obj.put("msg", "第" + error + "行： '" + vals[1] + "' 参数值不能为空");
						out.print(obj.toString());
						out.close();
						return null;
					}

					swl.setType(vals[0]);
					swl.setNum(vals[1]);
					swl.setDeviceModel(vals[2]);
					swl.setOwner(vals[3]);
					// 插入操作人
					swl.setCuser(username);
					list.add(swl);

				}

				ydService.insertBatchWhiteList(list);
				obj.put("ret", 1);
				obj.put("msg", "上传文件成功");
				out.write(obj.toString());
				out.close();
			} else {
				obj.put("ret", 0);
				obj.put("msg", "上传文件有误");
				out.print(obj.toString());
				out.close();
			}

		} catch (Exception e) {
			// 上传异常
			e.printStackTrace();
			PrintWriter pw;
			try {
				pw = response.getWriter();
				// 返回失败信息
				JSONObject obj = new JSONObject();
				obj.put("ret", 0);
				obj.put("msg", "第" + error + "行有误，请查看");
				pw.print(obj.toString());
				pw.close();
			} catch (IOException e1) {
				e.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 清理王-公共 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@ApiOperation(value = "全局参数配置 查询", notes = "全局参数配置 查询", httpMethod = "POST")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "页码", name = "start", dataType = "String"),
			@ApiImplicitParam(value = "条数", name = "limit", dataType = "String")
	})
	@RequestMapping(value = "/selectSuperConfigPublic", method = { RequestMethod.GET, RequestMethod.POST })
	public Result<List<SuperConfigPublic>> selectSuperConfigPublic(SuperConfigPublic record, HttpServletRequest request,
										  HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return ResultUtils.failure(Constants.ErrorToken);
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperConfigPublic> list = ydService.selectSuperConfigPublic(record);
		long size = ((Page) list).getTotal();
//		result.put("ret", 1);
//		result.put("data", list);
//		result.put("totalCount", size);

		Result<List<SuperConfigPublic>> res = ResultUtils.success(list);
		res.setTotalCount(size);

		return res;
	}

	/**
	 * 清理王-公共 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@ApiOperation(value = "全局参数 编辑", notes = "全局参数 编辑", httpMethod = "POST")
	@ApiImplicitParam(name = "handle", dataType = "String", required = true, value = "handle")
	@RequestMapping(value = "/superConfigPublicHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public Result<String> superConfigPublicHandle(SuperConfigPublic record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ResultUtils.failure(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydService.insertSuperConfigPublic(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydService.updateSuperConfigPublic(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydService.deleteSuperConfigPublic(record);
		} else if ("batch".equals(request.getParameter("handle"))) {
			if (!BlankUtils.checkBlank(record.getIds())) {
				result = ydService.updatebBatchPublic(record);
			}
		}

		if (result > 0)
//			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			return ResultUtils.success();
		else
//			return "{\"ret\":0,\"msg\":\"无效操作!\"}";
			return ResultUtils.failure("无效操作");
	}

	/**
	 * 清理王-监控 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperDrawRange", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperDrawRange(SuperDrawRange record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperDrawRange> list = ydService.selectSuperDrawRange(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 渠道信息维护 导出
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/exportSuperDrawRange", method = { RequestMethod.GET, RequestMethod.POST })
	public String exportDnChannelInfo(HttpServletRequest request, HttpServletResponse response, SuperDrawRange record) {
		// 数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// 数据内容
		List<SuperDrawRange> list = ydService.selectSuperDrawRange(record);

		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");
		List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
		Map<String, Object> contentMap = null;

		for (SuperDrawRange temp : list) {

			headerMap.put("tdate", "日期");
			headerMap.put("appid", "产品类型");
			headerMap.put("type", "碎片类型");
			headerMap.put("range1", "0%-20%");
			headerMap.put("range2", "20%-40%");
			headerMap.put("range3", "40%-60%");
			headerMap.put("range4", "60%-40%");
			headerMap.put("range5", "80%-60%");
			headerMap.put("range6", "80%-90%");
			headerMap.put("range7", "90%-95%");
			headerMap.put("range8", "100%以上");

			contentMap = new LinkedHashMap<String, Object>();
			contentMap.put("tdate", temp.getTdate());
			contentMap.put("appid", temp.getAppid());

			switch (temp.getType()) {
			case "1":
				contentMap.put("type", "iPhone12 Pro");
				break;
			case "2":
				contentMap.put("type", "Ipad Air4");
				break;
			case "3":
				contentMap.put("type", "小米耳机");
				break;
			case "4":
				contentMap.put("type", "200元话费");
				break;
			case "5":
				contentMap.put("type", "10G流量");
				break;
			case "6":
				contentMap.put("type", "100元红包");
				break;
			default:
				contentMap.put("type", temp.getType());
				break;
			}
			contentMap.put("range1", temp.getRange1());
			contentMap.put("range2", temp.getRange2());
			contentMap.put("range3", temp.getRange3());
			contentMap.put("range4", temp.getRange4());
			contentMap.put("range5", temp.getRange5());
			contentMap.put("range6", temp.getRange6());
			contentMap.put("range7", temp.getRange7());
			contentMap.put("range8", temp.getRange8());

			contentList.add(contentMap);
		}

		String fileName = "抽奖信息监控_" + DateTime.now().toString("yyyyMMdd") + ".xls";
		JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);

		return null;

	}

	/**
	 * 清理王-deeplink配置-修改
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/updateSuperConfigDeeplinkStatus", method = { RequestMethod.GET, RequestMethod.POST })
	public String updateSuperConfigDeeplinkStatus(SuperConfigDeeplink record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}
		int result = 0;

		record.setModifyUser(username);

		result = ydMapper.updateSuperConfigDeeplinkStatus(record);

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 清理王-新闻配置-修改
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/updateSuperConfigNewsStatus", method = { RequestMethod.GET, RequestMethod.POST })
	public String updateSuperConfigNewsStatus(SuperConfigNews record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);

		result = ydMapper.updateSuperConfigNewsStatus(record);

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 清理王-tab配置-修改
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/updateSuperConfigTabStatus", method = { RequestMethod.GET, RequestMethod.POST })
	public String updateSuperConfigTabStatus(SuperConfigTab record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}
		int result = 0;

		record.setModifyUser(username);

		result = ydMapper.updateSuperConfigTabStatus(record);

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 清理王-params配置-修改
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@ApiOperation(value = "自定义参数配置 状态修改", notes = "自定义参数配置 状态修改", httpMethod = "POST")
	@RequestMapping(value = "/updateSuperConfigParamsStatus", method = { RequestMethod.GET, RequestMethod.POST })
	public Result<String> updateSuperConfigParamsStatus(@ApiNeed({"status", "id"}) SuperConfigParams record, HttpServletRequest request,
												HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
//			return ReturnJson.error(Constants.ErrorToken);
			return ResultUtils.failure(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}
		int result = 0;

		record.setModifyUser(username);

		result = ydMapper.updateSuperConfigParamsStatus(record);

		if (result > 0)
//			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			return ResultUtils.success();
		else
//			return "{\"ret\":0,\"msg\":\"无效操作!\"}";
			return ResultUtils.failure("无效操作!");

	}

	/**
	 * 清理王-公共配置-修改
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/updateSuperConfigPublicStatus", method = { RequestMethod.GET, RequestMethod.POST })
	public String updateSuperConfigPublicStatus(SuperConfigPublic record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);

		result = ydMapper.updateSuperConfigPublicStatus(record);

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 清理王-公共配置-修改
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/updateSuperConfigWinmsgStatus", method = { RequestMethod.GET, RequestMethod.POST })
	public String updateSuperConfigWinmsgStatus(SuperConfigWinmsg record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);

		result = ydMapper.updateSuperConfigWinmsgStatus(record);

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 清理王-文案弹窗 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperConfigWinmsg", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperConfigWinmsg(SuperConfigWinmsg record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperConfigWinmsg> list = ydService.selectSuperConfigWinmsg(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 清理王-文案弹窗 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/superConfigWinmsgHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String superConfigWinmsgHandle(SuperConfigWinmsg record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydService.insertSuperConfigWinmsg(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydService.updateSuperConfigWinmsg(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydService.deleteSuperConfigWinmsg(record);
		} else if ("proCopy".equals(request.getParameter("handle"))) {
			String fromAppId = request.getParameter("fAppid");
			String toAppId = request.getParameter("tAppid");
			record.setAppid(fromAppId);
			List<SuperConfigWinmsg> list = ydService.selectSuperConfigWinmsg(record);
			if (list.size() == 0) {
				return "{\"ret\":0,\"msg\":\"该产品下没有配置有数据!\"}";
			}
			for (SuperConfigWinmsg vo : list) {
				vo.setAppid(toAppId);
				vo.setModifyUser(username);
			}
			result = ydService.insertSuperConfigWinmsgBatch(list);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 基础数据 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectCleanUmengBaseData", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectCleanUmengBaseData(HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		Map<String, String> map = new HashMap<String, String>();
		map.put("start_time", request.getParameter("start_time"));
		map.put("end_time", request.getParameter("end_time"));
		map.put("install_channel", request.getParameter("install_channel"));
		map.put("app_version", request.getParameter("app_version"));
		map.put("appid", request.getParameter("appid"));

		// x1 渠道类型 x2 媒体 appGroups 应用组
		map.put("x1", request.getParameter("x1"));
		map.put("x2", request.getParameter("x2"));
		map.put("appGroups", request.getParameter("appGroups"));

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<Map<String, String>> list = cleanYdMapper.selectCleanUmengBaseData(map);

		String sql4 = "SELECT DISTINCT cha_id `mapkey`,cha_type,cha_media FROM dn_channel_info ORDER BY cha_id";
		Map<String, Map<String, Object>> chaMap = adService.queryListMapOfKey(sql4);

		// 处理投放媒体字段
		for (Map<String, String> eachMap : list) {
			Map<String, Object> eachChaMap = chaMap.get(eachMap.get("install_channel"));
			if (eachChaMap != null) {
				if (eachChaMap.get("cha_media") != null) {
					eachMap.put("cha_media", eachChaMap.get("cha_media").toString());
				} else {
					eachMap.put("cha_media", "");
				}
			} else {
				eachMap.put("cha_media", "");
			}
		}
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("sum", cleanYdMapper.selectSumCleanUmengBaseData(map).get(0));
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 基础数据 导出
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/exportCleanUmengBaseData", method = { RequestMethod.GET, RequestMethod.POST })
	public String exportCleanUmengBaseData(HttpServletRequest request, HttpServletResponse response,
			SuperDrawRange record) {
		// 数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		Map<String, String> map = new HashMap<String, String>();
		map.put("start_time", request.getParameter("start_time"));
		map.put("end_time", request.getParameter("end_time"));
		map.put("install_channel", request.getParameter("install_channel"));
		map.put("app_version", request.getParameter("app_version"));
		map.put("appid", request.getParameter("appid"));

		// x1 渠道类型 x2 媒体 appGroups 应用组
		map.put("x1", request.getParameter("x1"));
		map.put("x2", request.getParameter("x2"));
		map.put("appGroups", request.getParameter("appGroups"));

		// 数据内容
		List<Map<String, String>> list = cleanYdMapper.selectCleanUmengBaseData(map);

		String sql4 = "SELECT DISTINCT cha_id `mapkey`,cha_type,cha_media FROM dn_channel_info ORDER BY cha_id";
		Map<String, Map<String, Object>> chaMap = adService.queryListMapOfKey(sql4);

		// 处理投放媒体字段
		for (Map<String, String> eachMap : list) {
			Map<String, Object> eachChaMap = chaMap.get(eachMap.get("install_channel"));
			if (eachChaMap != null) {
				if (eachChaMap.get("cha_media") != null) {
					eachMap.put("cha_media", eachChaMap.get("cha_media").toString());
				} else {
					eachMap.put("cha_media", "");
				}
			} else {
				eachMap.put("cha_media", "");
			}
		}

		String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
		Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");
		List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
		Map<String, Object> contentMap = null;

		for (Map<String, String> temp : list) {

			headerMap.put("ds", "日期");
			headerMap.put("appid", "产品");
			headerMap.put("cha_media", "投放媒体");
			headerMap.put("install_channel", "子渠道");
			headerMap.put("app_version", "版本");
			headerMap.put("add_num", "新增");
			headerMap.put("act_num", "活跃");
			headerMap.put("duration", "日使用时长");
			headerMap.put("duration2", "单次使用时长");
			headerMap.put("start_num", "启动次数");
			headerMap.put("break", "崩溃率");

			contentMap = new LinkedHashMap<String, Object>();
			contentMap.put("ds", temp.get("ds"));
			contentMap.put("appid", appMap.get(temp.get("appid")).get("app_name") + "_" + temp.get("appid"));
			contentMap.put("cha_media", temp.get("cha_media"));
			contentMap.put("install_channel", temp.get("install_channel"));
			contentMap.put("app_version", temp.get("app_version"));
			contentMap.put("add_num", temp.get("add_num"));
			contentMap.put("act_num", temp.get("act_num"));
			contentMap.put("duration", temp.get("duration"));
			contentMap.put("duration2", temp.get("duration2"));
			contentMap.put("start_num", temp.get("start_num"));
			contentMap.put("break", temp.get("break"));

			contentList.add(contentMap);
		}

		String fileName = "友盟基础数据_" + DateTime.now().toString("yyyyMMdd") + ".xls";
		JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);

		return null;

	}

	/**
	 * 基础数据下拉 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectCleanUmengBaseList", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectCleanUmengBaseList(HttpServletRequest request, HttpServletResponse response,String appid) {

		JSONObject result = new JSONObject();

		String sql = "SELECT DISTINCT install_channel FROM `umeng_base_data` where appid in ("+appid+")  and install_channel  != ''  and LENGTH(install_channel) < 30 ;";
		List<String> install_channel = cleanYdMapper.queryListString(sql);

		sql = "SELECT DISTINCT app_version FROM `umeng_base_data` where appid in ("+appid+")  and app_version  != '' ;";
		List<String> app_version = cleanYdMapper.queryListString(sql);

		sql = "SELECT DISTINCT ad_pos FROM `umeng_admsg_total3` where appid in ("+appid+")  and ad_pos  != '' ;";
		List<String> ad_pos = cleanYdMapper.queryListString(sql);

		result.put("ret", 1);
		result.put("install_channel", install_channel);
		result.put("app_version", app_version);
		result.put("ad_pos", ad_pos);

		return result.toJSONString();
	}

	/**
	 * 检查是否存在重复数据
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectCleanIsExist", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectCleanIsExist(HttpServletRequest request, HttpServletResponse response, SuperLockNew superLockNew) {

		JSONObject result = new JSONObject();
		String appid = request.getParameter("appid");
		String prjid = request.getParameter("prjid");
		String cha = request.getParameter("cha");
		String table = request.getParameter("table");

		String buy_act = request.getParameter("buy_act");
		String buy_id = request.getParameter("buy_id");

		String sql = "";
		if (!BlankUtils.checkBlank(buy_id) || !BlankUtils.checkBlank(buy_act)) {
			sql = " SELECT  * FROM  " + table + " where appid = '" + appid + "' and prjid = '" + prjid + "' and cha = '"
					+ cha + "' and buy_act =  '" + buy_act + "' and buy_id = '" + buy_id + "'";
		} else {
			sql = " SELECT  * FROM  " + table + " where appid = '" + appid + "' and prjid = '" + prjid + "' and cha = '"
					+ cha + "'";
		}

		//List<Map<String, Object>> list = cleanYdMapper.selectSuperLockNewExist(sql);

        if (cleanYdMapper.selectSuperLockNewExist(superLockNew, table) != null) {

            result.put("IsExist", 1);
        }
		result.put("ret", 1);
		//if (list != null && list.size() > 0) {
		//	result.put("IsExist", 1);
		//} else {

			// 查询新V2表
			//SuperLockGroup superLockGroup = new SuperLockGroup();
			//superLockGroup.setAppid(appid);
			//superLockGroup.setPrjid(prjid);
			//superLockGroup.setCha(cha);
			//superLockGroup.setBuy_act(buy_act);
			//superLockGroup.setBuy_id(buy_id);
			//if (superLockGroupService.checkAppidAndChaOrAppidAndPrjid(superLockGroup) > 0) {
            //
			//	result.put("IsExist", 1);
			//}
		//}

		return result.toJSONString();
	}

	/**
	 * 清理王-deeplink 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperDeeplinkNew", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperDeeplinkNew(SuperDeeplinkNew record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperDeeplinkNew> list = ydMapper.selectSuperDeeplinkNew(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 清理王-deeplink 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/superDeeplinkNewHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String superDeeplinkNewHandle(SuperDeeplinkNew record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertSuperDeeplinkNew(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateSuperDeeplinkNew(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteSuperDeeplinkNew(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 清理王-deeplink 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperDeeplinkTask", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperDeeplinkTask(SuperDeeplinkTask record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperDeeplinkTask> list = ydMapper.selectSuperDeeplinkTask(record);
		long size = ((Page) list).getTotal();

		result.put("ret", 1);

		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	@RequestMapping(value = "/selectSuperDeeplinkCfg", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperDeeplinkCfg(HttpServletRequest request, HttpServletResponse response) {
		JSONObject result = new JSONObject();

		result.put("ret", 1);

		String sql = " SELECT policyId, policyName FROM `super_deeplink_policy` ";
		result.put("policyId", cleanYdMapper.queryListMap(sql));

		sql = " SELECT dpId FROM `super_deeplink_new` ";
		result.put("dpId", cleanYdMapper.queryListString(sql));
		return result.toJSONString();
	}

	/**
	 * 清理王-deeplink 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/superDeeplinkTaskHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String superDeeplinkTaskHandle(SuperDeeplinkTask record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			List<SuperDeeplinkTask> list = ydMapper.selectSuperDeeplinkTask(new SuperDeeplinkTask(record.getDpId()));
			if (list != null && list.size() > 0) {
				return "{\"ret\":0,\"msg\":\"该dpId已配置!\"}";
			}
			result = ydMapper.insertSuperDeeplinkTask(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateSuperDeeplinkTask(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteSuperDeeplinkTask(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 清理王-deeplink 任务报表查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperDeeplinkReport", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperDeeplinkReport(HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		Map<String, String> map = new HashMap<String, String>();
		map.put("taskId", BlankUtils.checkNull(request, "taskId"));
		map.put("dpId", BlankUtils.checkNull(request, "dpId"));
		map.put("pid", BlankUtils.checkNull(request, "pid"));
		map.put("appid", BlankUtils.checkNull(request, "appid"));
		map.put("cha", BlankUtils.checkNull(request, "cha"));
		map.put("startTime", BlankUtils.checkNull(request, "startTime"));
		map.put("endTime", BlankUtils.checkNull(request, "endTime"));

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperDeeplinkReport> list = ydMapper.selectSuperDeeplinkReport(map);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 基础数据 导出
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/exportSuperDeeplinkReport", method = { RequestMethod.GET, RequestMethod.POST })
	public String exportSuperDeeplinkReport(HttpServletRequest request, HttpServletResponse response,
			SuperDrawRange record) {
		// 数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		Map<String, String> map = new HashMap<String, String>();
		map.put("taskId", BlankUtils.checkNull(request, "taskId"));
		map.put("dpId", BlankUtils.checkNull(request, "dpId"));
		map.put("pid", BlankUtils.checkNull(request, "pid"));
		map.put("appid", BlankUtils.checkNull(request, "appid"));
		map.put("cha", BlankUtils.checkNull(request, "cha"));
		map.put("startTime", BlankUtils.checkNull(request, "startTime"));
		map.put("endTime", BlankUtils.checkNull(request, "endTime"));
		// 数据内容
		List<SuperDeeplinkReport> list = ydMapper.selectSuperDeeplinkReport(map);

		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");
		List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
		Map<String, Object> contentMap = null;

		for (SuperDeeplinkReport temp : list) {

			headerMap.put("tdate", "日期");
			headerMap.put("appid", "产品");
			headerMap.put("cha", "渠道");
			headerMap.put("pid", "项目id");
			headerMap.put("dpId", "deeplinkId");
			headerMap.put("taskId", "任务id");
			headerMap.put("total", "目标拉起人数");
			headerMap.put("finish", "拉起成功总人数");
			headerMap.put("error", "拉起失败总人数");
			headerMap.put("undo", "无法拉起人数");
			headerMap.put("days", "当天拉起成功人数");
			headerMap.put("dayUserTotal", "当日用户群");
			headerMap.put("taskTotal", "总拉起人数");
			headerMap.put("userTotal", "用户群数量");

			contentMap = new LinkedHashMap<String, Object>();
			contentMap.put("tdate", temp.getTdate());
			contentMap.put("appid", temp.getAppid());
			contentMap.put("cha", temp.getCha());
			contentMap.put("pid", temp.getPid());
			contentMap.put("dpId", temp.getDpId());
			contentMap.put("taskId", temp.getTaskId());
			contentMap.put("total", temp.getTotal());
			contentMap.put("finish", temp.getFinish());
			contentMap.put("error", temp.getError());
			contentMap.put("undo", temp.getUndo());
			contentMap.put("days", temp.getDays());
			contentMap.put("dayUserTotal", temp.getDayUserTotal());
			contentMap.put("taskTotal", temp.getTaskTotal());
			contentMap.put("userTotal", temp.getUserTotal());

			contentList.add(contentMap);
		}

		String fileName = "deeplink任务报表_" + DateTime.now().toString("yyyyMMdd") + ".xls";
		JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);

		return null;

	}

	/**
	 * 清理王-deeplink 屏蔽策略 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperDeeplinkPolicy", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperDeeplinkPolicy(SuperDeeplinkPolicy record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperDeeplinkPolicy> list = ydMapper.selectSuperDeeplinkPolicy(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 清理王-deeplink 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/superDeeplinkPolicyHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String superDeeplinkPolicyHandle(SuperDeeplinkPolicy record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertSuperDeeplinkPolicy(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateSuperDeeplinkPolicy(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteSuperDeeplinkPolicy(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 应用新锁屏- 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperLockNew", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperLockNew(SuperLockNew record, HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		String token = request.getParameter("token");
		if ("dnwx1002dnwx1002".equals(token) || "wbtokenukpfllbbyt72hhamm0i7dr1g".equals(token)) {
			// 特殊不处理
		} else {
			// 校验权限
			if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
				return ReturnJson.error(Constants.ErrorToken);
			}
		}

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperLockNew> list = ydMapper.selectSuperLockNew(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 应用新锁屏 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/superLockNewHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String superLockNewHandle(SuperLockNew record, HttpServletRequest request, HttpServletResponse response) {
		String username = "";
		// 校验权限
		String token = request.getParameter("token");
		if ("dnwx1002dnwx1002".equals(token)) {
			username = "autoTest";
		} else {
			if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
				return ReturnJson.error(Constants.ErrorToken);
			}

			if (token.startsWith("wbtoken")) {
				com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue()
						.get(token);
				username = currUserVo.getLogin_name();
			} else {
				WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
				username = WaibaoUser.getUser_name();
			}
		}

		int result = 0;

		record.setModifyUser(username);
		record.setCuser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertSuperLockNew(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			record.setModifyTime(new Date().toString());
			result = ydMapper.updateSuperLockNew(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteSuperLockNew(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 友盟广告位
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectAppUmeng", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectAppUmeng(HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		String appid_group = request.getParameter("appid_group");
		String channel_group = request.getParameter("install_channel_group");
		String version_group = request.getParameter("app_version_group");
		String adpos_group = request.getParameter("ad_pos_group");

		String group_by = "tdate";
		if (!BlankUtils.checkBlank(appid_group)) {
			group_by += ",appid";
		}
		if (!BlankUtils.checkBlank(channel_group)) {
			group_by += ",install_channel";
		}
		if (!BlankUtils.checkBlank(version_group)) {
			group_by += ",app_version";
		}
		if (!BlankUtils.checkBlank(adpos_group)) {
			group_by += ",ad_pos";
		}

		JSONObject result = new JSONObject();
		Map<String, String> map = new HashMap<String, String>();
		map.put("install_channel", BlankUtils.checkNull(request, "install_channel"));
		map.put("app_version", BlankUtils.checkNull(request, "app_version"));
		map.put("appid", BlankUtils.checkNull(request, "appid"));
		map.put("ad_pos", BlankUtils.checkNull(request, "ad_pos"));

		String order = " act_num desc ";
		map.put("order", order);
		if (!("").equals(BlankUtils.checkNull(request, "order"))) {
			map.put("order", request.getParameter("order"));
		}

		map.put("group_by", group_by);
		map.put("startTime", BlankUtils.checkNull(request, "start_time"));
		map.put("endTime", BlankUtils.checkNull(request, "end_time"));

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<AdMsgTotalVo2> list = ydMapper.selectAppUmeng(map);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("sum", ydMapper.selectSumAppUmeng(map));
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 基础数据 导出
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/exportSelectAppUmeng", method = { RequestMethod.GET, RequestMethod.POST })
	public String exportSelectAppUmeng(HttpServletRequest request, HttpServletResponse response,
			SuperDrawRange record) {
		// 数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		Map<String, String> map = new HashMap<String, String>();

		String appid_group = request.getParameter("appid_group");
		String channel_group = request.getParameter("install_channel_group");
		String version_group = request.getParameter("app_version_group");
		String adpos_group = request.getParameter("ad_pos_group");

		String group_by = "tdate";
		if (!BlankUtils.checkBlank(appid_group)) {
			group_by += ",appid";
		}
		if (!BlankUtils.checkBlank(channel_group)) {
			group_by += ",install_channel";
		}
		if (!BlankUtils.checkBlank(version_group)) {
			group_by += ",app_version";
		}
		if (!BlankUtils.checkBlank(adpos_group)) {
			group_by += ",ad_pos";
		}
		map.put("group_by", group_by);

		map.put("install_channel", BlankUtils.checkNull(request, "install_channel"));
		map.put("app_version", BlankUtils.checkNull(request, "app_version"));
		map.put("appid", BlankUtils.checkNull(request, "appid"));
		map.put("startTime", BlankUtils.checkNull(request, "start_time"));
		map.put("endTime", BlankUtils.checkNull(request, "end_time"));
		map.put("ad_pos", BlankUtils.checkNull(request, "ad_pos"));

		String order = " act_num desc ";
		map.put("order", order);
		if (!("").equals(BlankUtils.checkNull(request, "order"))) {
			map.put("order", request.getParameter("order"));
		}

		// 数据内容
		List<AdMsgTotalVo2> list = ydMapper.selectAppUmeng(map);

		String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
		Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");
		List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
		Map<String, Object> contentMap = null;

		for (AdMsgTotalVo2 temp : list) {

			headerMap.put("tdate", "日期");
			headerMap.put("appid", "产品");
			headerMap.put("install_channel", "渠道");
			headerMap.put("app_version", "版本");
			headerMap.put("ad_pos", "广告位");
			headerMap.put("show_num", "展示数");
			headerMap.put("click_num", "点击数");
			headerMap.put("click_rate", "点击率");
			headerMap.put("unique_num", "独立用户数");
			headerMap.put("act_num", "活跃数");
			headerMap.put("ratio", "覆盖率");

			contentMap = new LinkedHashMap<String, Object>();
			contentMap.put("tdate", temp.getTdate());
			String appid = "";
			if (!BlankUtils.checkBlank(temp.getAppid()) && appMap.get(temp.getAppid()).get("app_name") != null) {
				appid = appMap.get(temp.getAppid()).get("app_name") + "_" + temp.getAppid();
			}
			contentMap.put("appid", appid);
			contentMap.put("install_channel", temp.getInstall_channel());
			contentMap.put("app_version", temp.getApp_version());
			contentMap.put("ad_pos", temp.getAd_pos());
			contentMap.put("show_num", temp.getShow_num());
			contentMap.put("click_num", temp.getClick_num());
			contentMap.put("click_rate", temp.getClick_rate());
			contentMap.put("unique_num", temp.getUnique_num());
			contentMap.put("act_num", temp.getAct_num());
			contentMap.put("ratio", temp.getRatio());

			contentList.add(contentMap);
		}

		String fileName = "友盟广告位数据_" + DateTime.now().toString("yyyyMMdd") + ".xls";
		ExportExcelUtil.export(response, contentList, headerMap, fileName);

		return null;

	}

	/**
	 * 友盟广告类型
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectAppUmengType", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectAppUmengType(HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		Map<String, String> map = new HashMap<String, String>();
		map.put("install_channel", BlankUtils.checkNull(request, "install_channel"));
		map.put("app_version", BlankUtils.checkNull(request, "app_version"));
		map.put("appid", BlankUtils.checkNull(request, "appid"));
		map.put("type", BlankUtils.checkNull(request, "type"));

		String order = " act_num desc ";
		map.put("order", order);
		if (!("").equals(BlankUtils.checkNull(request, "order"))) {
			map.put("order", request.getParameter("order"));
		}

		String group_by = "";
		if (("1").equals(BlankUtils.checkNull(request, "app_version_group"))) {
			group_by += ",app_version";
		}
		if (("1").equals(BlankUtils.checkNull(request, "install_channel_group"))) {
			group_by += ",install_channel";
		}
		map.put("group_by", group_by);
		map.put("startTime", BlankUtils.checkNull(request, "start_time"));
		map.put("endTime", BlankUtils.checkNull(request, "end_time"));

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<AdMsgTotalVo3> list = ydMapper.selectAppUmengType(map);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("sum", ydMapper.selectSumAppUmengType(map));
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 基础数据 导出
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/exportSelectAppUmengType", method = { RequestMethod.GET, RequestMethod.POST })
	public String exportSelectAppUmengType(HttpServletRequest request, HttpServletResponse response,
			SuperDrawRange record) {
		// 数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		Map<String, String> map = new HashMap<String, String>();
		map.put("install_channel", BlankUtils.checkNull(request, "install_channel"));
		map.put("app_version", BlankUtils.checkNull(request, "app_version"));
		map.put("appid", BlankUtils.checkNull(request, "appid"));
		map.put("startTime", BlankUtils.checkNull(request, "start_time"));
		map.put("endTime", BlankUtils.checkNull(request, "end_time"));
		map.put("type", BlankUtils.checkNull(request, "type"));

		String order = " act_num desc ";
		map.put("order", order);
		if (!("").equals(BlankUtils.checkNull(request, "order"))) {
			map.put("order", request.getParameter("order"));
		}
		String group_by = "";
		if (("1").equals(BlankUtils.checkNull(request, "app_version_group"))) {
			group_by += ",app_version";
		}
		if (("1").equals(BlankUtils.checkNull(request, "install_channel_group"))) {
			group_by += ",install_channel";
		}
		map.put("group_by", group_by);
		// 数据内容
		List<AdMsgTotalVo3> list = ydMapper.selectAppUmengType(map);

		String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
		;
		Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");
		List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
		Map<String, Object> contentMap = null;

		for (AdMsgTotalVo3 temp : list) {

			headerMap.put("tdate", "日期");
			headerMap.put("appid", "产品");
			headerMap.put("install_channel", "渠道");
			headerMap.put("app_version", "版本");
			headerMap.put("type", "广告类型");

			headerMap.put("load_num", "触发加载数");
			headerMap.put("loaded_num", "加载成功数");
			headerMap.put("loadfail_num", "加载失败数");
			headerMap.put("show_num", "触发展示数");
			headerMap.put("showed_num", "展示成功数");
			headerMap.put("show_rate", "展示率");

			headerMap.put("click_num", "点击数");
			headerMap.put("click_rate", "点击率");
			headerMap.put("unique_num", "独立数");
			headerMap.put("act_num", "活跃数");
			headerMap.put("unique_rate", "覆盖率");

			contentMap = new LinkedHashMap<String, Object>();
			contentMap.put("tdate", temp.getTdate());
			contentMap.put("appid", appMap.get(temp.getAppid()).get("app_name") + "_" + temp.getAppid());
			contentMap.put("install_channel", temp.getInstall_channel());
			contentMap.put("app_version", temp.getApp_version());
			contentMap.put("type", temp.getType());

			contentMap.put("load_num", temp.getLoad_num());
			contentMap.put("loaded_num", temp.getLoaded_num());
			contentMap.put("loadfail_num", temp.getLoadfail_num());
			contentMap.put("show_num", temp.getShow_num());
			contentMap.put("showed_num", temp.getShowed_num());
			contentMap.put("show_rate", temp.getShow_rate());
			contentMap.put("click_num", temp.getClick_num());
			contentMap.put("click_rate", temp.getClick_rate());
			contentMap.put("unique_num", temp.getUnique_num());
			contentMap.put("act_num", temp.getAct_num());
			contentMap.put("unique_rate", temp.getUnique_rate());

			contentList.add(contentMap);
		}

		String fileName = "友盟广告类型数据_" + DateTime.now().toString("yyyyMMdd") + ".xls";
		JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);

		return null;

	}

	/**
	 * deeplink包- 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperDeeplinkPackageReport", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperDeeplinkPackageReport(HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		Map<String, String> map = new HashMap<String, String>();
		map.put("startTime", BlankUtils.checkNull(request, "startTime"));
		map.put("endTime", BlankUtils.checkNull(request, "endTime"));
		map.put("packages", BlankUtils.checkNull(request, "packages"));

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperDeeplinkPackageReport> list = ydMapper.selectSuperDeeplinkPackageReport(map);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * deeplink 包名
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperDeeplinkPkg", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperDeeplinkPkg(HttpServletRequest request, HttpServletResponse response) {
		JSONObject result = new JSONObject();

		result.put("ret", 1);

		String sql = " SELECT DISTINCT packages FROM `super_deeplink_package_nums`; ";
		result.put("packages", cleanYdMapper.queryListMap(sql));

		return result.toJSONString();
	}

	/**
	 * tag模板列表- 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@ApiOperation(value = "tag模板列表- 查询", notes = "tag模板列表- 查询", httpMethod = "POST")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "start", value = "页码", dataType = "String"),
			@ApiImplicitParam(name = "limit", value = "条数", dataType = "String")
	})
	@RequestMapping(value = "/selectTagTemp", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectQpTagTemp(QpTagTemp record, HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<QpTagTemp> list = ydMapper.selectQpTagTemp(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * tag模板复制 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@RequestMapping(value = "/copySignTemp", method = { RequestMethod.GET, RequestMethod.POST })
	public String copySignTemp(QpSignTemp record, HttpServletRequest request, HttpServletResponse response)
			throws UnsupportedEncodingException {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}

		int result = ydMapper.copySignTemp(record);

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * tag模板列表 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@ApiOperation(value = "tag模板列表 操作", notes = "tag模板列表 操作", httpMethod = "POST")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "handle", value = "操作", dataType = "String")
	})
	@RequestMapping(value = "/tagTempHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String tagTempHandle(QpTagTemp record, HttpServletRequest request, HttpServletResponse response)
			throws UnsupportedEncodingException {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		if (!BlankUtils.checkBlank(record.getRes_url())) {
			String res_url = record.getRes_url();
			String values = new String(Base64.decodeBase64(res_url.replace(" ", "+")), "UTF-8");
			record.setRes_url(values);
			try {
				String fileLength = FileUtils.getFileLength(record.getRes_url());
				record.setRes_size(fileLength);
				// 加密方式:MD5(res_url+size)'
				record.setRes_md5(Base64.encodeBase64((record.getRes_url() + fileLength).getBytes()).toString());
			} catch (Exception e) {
				e.printStackTrace();
			}

		}

		if (!BlankUtils.checkBlank(record.getImg_url())) {
			String img_url = record.getImg_url();
			String values = new String(Base64.decodeBase64(img_url.replace(" ", "+")), "UTF-8");
			record.setImg_url(values);
			try {
				byte[] data = FileUtils.getImageFromNetByUrl(record.getImg_url());
				BufferedImage bi;
				bi = ImageIO.read(new ByteArrayInputStream(data));
				record.setImg_width(bi.getWidth() + "");
				record.setImg_height(bi.getHeight() + "");
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		if (!BlankUtils.checkBlank(record.getVideo_url())) {
			File file = null;
			String video_url = record.getVideo_url();
			String values = new String(Base64.decodeBase64(video_url.replace(" ", "+")), "UTF-8");
			record.setVideo_url(values);
			try {
				file = FileUtils.getFile(record.getVideo_url());
				it.sauronsoftware.jave.Encoder encoder = new it.sauronsoftware.jave.Encoder();
				it.sauronsoftware.jave.MultimediaInfo m = encoder.getInfo(file);
				record.setVideo_width(m.getVideo().getSize().getWidth() + "");// 获取视频宽高
				record.setVideo_height(m.getVideo().getSize().getHeight() + "");// 获取视频长高
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				file.delete();
			}
		}

		if (!BlankUtils.checkBlank(record.getMicro_video_url())) {
			File file = null;
			String micro_video_url = record.getMicro_video_url();
			String values = new String(Base64.decodeBase64(micro_video_url.replace(" ", "+")), "UTF-8");
			record.setMicro_video_url(values);
			try {
				file = FileUtils.getFile(record.getMicro_video_url());
				it.sauronsoftware.jave.Encoder encoder = new it.sauronsoftware.jave.Encoder();
				it.sauronsoftware.jave.MultimediaInfo m = encoder.getInfo(file);
				record.setMicro_video_width(m.getVideo().getSize().getWidth() + "");// 获取视频宽高
				record.setMicro_video_height(m.getVideo().getSize().getHeight() + "");// 获取视频长高
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				file.delete();
			}
		}

		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			Random random = new Random();
			record.setLike_cnt(random.nextInt(10000) + 100 + "");
			List<String> nativeList = Arrays.asList("#E57373", "#CE93D8", "#EA80FC", "#9575CD", "#42A5F5", "#448AFF",
					"#00BCD4", "#1DE9B6", "#FFB74D", "#A5D6A7", "#B39DDB", "#00C853", "#D32F2F");
			Collections.shuffle(nativeList);
			record.setPlcolor(nativeList.get(0));
			result = ydMapper.insertQpTagTemp(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			record.setModifyTime(new Date().toString());
			result = ydMapper.updateQpTagTemp(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteQpTagTemp(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * tag列表- 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@ApiOperation(value = "tag列表 查询", notes = "tag列表 查询", httpMethod = "POST")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "start", value = "页码", dataType = "String"),
			@ApiImplicitParam(name = "limit", value = "条数", dataType = "String")
	})
	@RequestMapping(value = "/selectConfigTag", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectConfigTag(QpConfigTag record, HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<QpConfigTag> list = ydMapper.selectQpConfigTag(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * tag列表 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@ApiOperation(value = "tag列表 操作", notes = "tag列表 操作", httpMethod = "POST")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "start", value = "页码", dataType = "String"),
			@ApiImplicitParam(name = "limit", value = "条数", dataType = "String")
	})
	@RequestMapping(value = "/configTagHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String configTagHandle(QpConfigTag record, HttpServletRequest request, HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertQpConfigTag(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			record.setModifyTime(new Date().toString());
			result = ydMapper.updateQpConfigTag(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteQpConfigTag(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	@RequestMapping(value = "/appCfgXML", method = { RequestMethod.GET, RequestMethod.POST })
	public void appCfgXML(HttpServletRequest request, HttpServletResponse response) {

		String appid = request.getParameter("appid");
		String prjid = request.getParameter("prjid");
		String cha = request.getParameter("cha");
		String v = request.getParameter("v");
		String imei = request.getParameter("imei");
		String oaid = request.getParameter("oaid");
		String lsn = request.getParameter("lsn");

		String url = "https://app.superclear.cn/check/appCfg?appid=" + appid + "&cha=" + cha + "&prjid=" + prjid + "&v="
				+ v + "&lsn=167216642&imei=863755042044136";

		// String xmlStr = "";
		String httpGet = HttpClientUtils.getInstance().httpGet(url);
		JSONObject rest = JSONObject.parseObject(httpGet);
		//

		// //JSON
		//
		// String jsonToXmlstr = jsonToXmlstr(rest,new StringBuffer() );
		// System.out.println("json转"+jsonToXmlstr);
		// xmlStr = XMLConverUtil.converterXml(rest);

		// String xml = "<data>";
		//
		// xml += "<adSources>";
		// for (Object obj : adSources) {
		// // 广告源及下属子列表
		// JSONObject cc = (JSONObject)obj;
		// JSONArray placements = cc.getJSONArray("placements");
		// cc.remove("placements");
		//
		// String xmlStr = XMLConverUtil.converterXmlProperties("adSource", cc);
		// xml += xmlStr;
		//
		// for (Object obj2 : placements) {
		// JSONObject dd = (JSONObject)obj2;
		// String xmlStr2 = XMLConverUtil.converterXmlProperties("placement",
		// dd);
		// xml += xmlStr2;
		// xml += "</placement>";
		// }
		//
		// xml += "</adSource>";
		// }
		// xml += "</adSources>";
		//
		//
		// xml += "<strategys>";
		// for (Object obj : strategys) {
		// // 广告策略及下属子列表
		// JSONObject cc = (JSONObject)obj;
		//
		// String xmlStr = XMLConverUtil.converterXmlProperties("strategy", cc);
		// xml += xmlStr;
		// xml += "</strategy>";
		// }
		// xml += "</strategys>";
		//
		// xml += "<adPositions>";
		// // 按广告策略分组，同时返回广告位列表
		// List<ExtendAdposVo> parseArray =
		// JSONArray.parseArray(adPositions.toJSONString(),
		// ExtendAdposVo.class);
		// Map<String, Set<String>> collect = parseArray.stream().collect(
		// Collectors.groupingBy(ExtendAdposVo::getStrategy,
		// Collectors.mapping(ExtendAdposVo::getName, Collectors.toSet())));
		// for (Entry<String, Set<String>> entry : collect.entrySet()) {
		// // 广告位及下属子列表
		// xml += "<adPosition strategy=\""+entry.getKey()+"\"
		// name=\""+entry.getValue()+"\">";
		// xml += "</adPosition>";
		// }
		// xml += "</adPositions>";
		//
		// xml += "<limit>";
		// // 全局广告展示策略
		// String xmlStr = XMLConverUtil.converterXml(policy);
		// xml += xmlStr;
		// xml += "</limit>";
		//
		//
		// xml += "</data>";
		//

		// 输出xml
		PrintWriter pw = null;
		try {
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Cache-Control", "no-cache");
			pw = response.getWriter();
			pw.print("");
			pw.flush();
		} catch (Exception var18) {
			var18.printStackTrace();
		} finally {
			if (pw != null) {
				pw.close();
			}
		}
	}

	/**
	 * 背景图 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectQpWeekUrl", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectQpWeekUrl(QpWeekUrl record, HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<QpWeekUrl> list = ydMapper.selectQpWeekUrl(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 背景图 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/QpWeekUrlHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String QpWeekUrlHandle(QpWeekUrl record, HttpServletRequest request, HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCuser(username);
		record.setEuser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertQpWeekUrl(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateQpWeekUrl(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteQpWeekUrl(record);
		} else if ("state".equals(request.getParameter("handle"))) {
			result = ydMapper.updateQpWeekUrlStatus(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 金句 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectQpWordsList", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectQpWordsList(QpWordsList record, HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<QpWordsList> list = ydMapper.selectQpWordsList(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 金句 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/QpWordsListHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String QpWordsListHandle(QpWordsList record, HttpServletRequest request, HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCuser(username);
		record.setEuser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertQpWordsList(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateQpWordsList(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteQpWordsList(record);
		} else if ("state".equals(request.getParameter("handle"))) {
			result = ydMapper.updateQpWordsListStatus(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 早安模块- 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectQpSignTemp", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectQpSignTemp(QpSignTemp record, HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<QpSignTemp> list = ydMapper.selectQpSignTemp(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * tag列表 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@RequestMapping(value = "/QpSignTempHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String QpSignTempHandle(QpSignTemp record, HttpServletRequest request, HttpServletResponse response)
			throws UnsupportedEncodingException {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		if (!BlankUtils.checkBlank(record.getRes_url())) {
			String res_url = record.getRes_url();
			String values = new String(Base64.decodeBase64(res_url.replace(" ", "+")), "UTF-8");
			record.setRes_url(values);
			try {
				String fileLength = FileUtils.getFileLength(record.getRes_url());
				record.setRes_size(fileLength);
				// 加密方式:MD5(res_url+size)'
				record.setRes_md5(Base64.encodeBase64((record.getRes_url() + fileLength).getBytes()).toString());
			} catch (Exception e) {
				e.printStackTrace();
			}

		}

		if (!BlankUtils.checkBlank(record.getImg_url())) {
			String img_url = record.getImg_url();
			String values = new String(Base64.decodeBase64(img_url.replace(" ", "+")), "UTF-8");
			record.setImg_url(values);
			try {
				byte[] data = FileUtils.getImageFromNetByUrl(record.getImg_url());
				BufferedImage bi;
				bi = ImageIO.read(new ByteArrayInputStream(data));
				record.setImg_width(bi.getWidth() + "");
				record.setImg_height(bi.getHeight() + "");
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		if (!BlankUtils.checkBlank(record.getVideo_url())) {
			File file = null;
			String video_url = record.getVideo_url();
			String values = new String(Base64.decodeBase64(video_url.replace(" ", "+")), "UTF-8");
			record.setVideo_url(values);
			try {
				file = FileUtils.getFile(record.getVideo_url());
				it.sauronsoftware.jave.Encoder encoder = new it.sauronsoftware.jave.Encoder();
				it.sauronsoftware.jave.MultimediaInfo m = encoder.getInfo(file);
				record.setVideo_width(m.getVideo().getSize().getWidth() + "");// 获取视频宽高
				record.setVideo_height(m.getVideo().getSize().getHeight() + "");// 获取视频长高
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				file.delete();
			}
		}

		if (!BlankUtils.checkBlank(record.getMicro_video_url())) {
			File file = null;
			String micro_video_url = record.getMicro_video_url();
			String values = new String(Base64.decodeBase64(micro_video_url.replace(" ", "+")), "UTF-8");
			record.setMicro_video_url(values);
			try {
				file = FileUtils.getFile(record.getMicro_video_url());
				it.sauronsoftware.jave.Encoder encoder = new it.sauronsoftware.jave.Encoder();
				it.sauronsoftware.jave.MultimediaInfo m = encoder.getInfo(file);
				record.setMicro_video_width(m.getVideo().getSize().getWidth() + "");// 获取视频宽高
				record.setMicro_video_height(m.getVideo().getSize().getHeight() + "");// 获取视频长高
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				file.delete();
			}
		}

		record.setModifyUser(username);
		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			Random random = new Random();
			record.setLike_cnt(random.nextInt(10000) + 100 + "");
			List<String> nativeList = Arrays.asList("#E57373", "#CE93D8", "#EA80FC", "#9575CD", "#42A5F5", "#448AFF",
					"#00BCD4", "#1DE9B6", "#FFB74D", "#A5D6A7", "#B39DDB", "#00C853", "#D32F2F");
			Collections.shuffle(nativeList);
			record.setPlcolor(nativeList.get(0));
			result = ydMapper.insertQpSignTemp(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			record.setModifyTime(new Date().toString());
			result = ydMapper.updateQpSignTemp(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteQpSignTemp(record);
		} else if ("state".equals(request.getParameter("handle"))) {
			result = ydMapper.updateQpSignTempStatus(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 卡片列表
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectCardTagConfig", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectCardTagConfig(CardTagConfig record, HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<CardTagConfig> list = ydMapper.selectCardTagConfig(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 卡片列表操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/CardTagConfigHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String CardTagConfigHandle(CardTagConfig record, HttpServletRequest request, HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCuser(username);
		record.setEuser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertCardTagConfig(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateCardTagConfig(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteCardTagConfig(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 卡片信息
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectCardInfoConfig", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectCardInfoConfig(CardInfoConfig record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<CardInfoConfig> list = ydMapper.selectCardInfoConfig(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 卡片信息操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/CardInfoConfigHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String CardInfoConfigHandle(CardInfoConfig record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCuser(username);
		record.setEuser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertCardInfoConfig(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateCardInfoConfig(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteCardInfoConfig(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	@RequestMapping(value = "/qpsort", method = { RequestMethod.GET, RequestMethod.POST })
	public String srot(String id, String tagid, int sort, HttpServletRequest request, HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		result = ydMapper.execSql(" update qp_tag_temp  set sort = " + sort + " where id = " + id);
		String sql = " SELECT * FROM `qp_tag_temp`  where id != " + id + "  and tagid = " + tagid + " and sort >=  "
				+ sort + " ORDER BY sort ";

		sort++;

		List<Map<String, Object>> queryListMap = ydMapper.queryListMap(sql);
		for (Map<String, Object> map : queryListMap) {
			ydMapper.execSql(" update qp_tag_temp  set sort = " + sort + " where id = " + map.get("id"));
			sort++;
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 卡片信息
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectQpPopVo", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectQpPopVo(QpPopVo record, HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<QpPopVo> list = ydMapper.selectQpPopVo(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	@RequestMapping(value = "/getQpPopTemp", method = { RequestMethod.GET, RequestMethod.POST })
	public String getQpPopTemp(HttpServletRequest request, HttpServletResponse response) {
		// token验证
		// String token = request.getParameter("token");
		// if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
		// return "{\"ret\":2,\"msg\":\"token is error!\"}";
		// else
		// redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		JSONObject result = new JSONObject();

		String sql = " SELECT CONCAT(id,'_',tagid,'_',temp_title) temp,id FROM `qp_tag_temp`; ";
		List<Map<String, Object>> queryListMap = ydMapper.queryListMap(sql);

		result.put("ret", 1);
		result.put("data", queryListMap);

		return result.toJSONString();
	}

	/**
	 * 卡片信息操作
	 *
	 * @param request
	 * @param response
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@RequestMapping(value = "/QpPopVoHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String QpPopVoHandle(QpPopVo record, HttpServletRequest request, HttpServletResponse response)
			throws UnsupportedEncodingException {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		String poppic = record.getPoppic();
		String values = new String(Base64.decodeBase64(poppic.replace(" ", "+")), "UTF-8");
		record.setPoppic(values);

		record.setCuser(username);
		record.setEuser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertQpPopVo(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateQpPopVo(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteQpPopVo(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 *
	 * @param request
	 * @param response
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@RequestMapping(value = "/changeRecord", method = { RequestMethod.GET, RequestMethod.POST })
	public String QpPopVoHandle(QpTagTemp record, HttpServletRequest request, HttpServletResponse response)
			throws UnsupportedEncodingException {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}

		int result = 0;

		if ("QpSignTemp".equals(request.getParameter("handle"))) {
			result = ydMapper.editQpSignTemp(record);
		} else if ("QpTagTemp".equals(request.getParameter("handle"))) {
			result = ydMapper.editQpTagTemp(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 页面快捷修改
	 * 
	 * @param request
	 * @param response
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@ApiOperation(value = "tag模板列表 开关", notes = "tag模板列表 开关", httpMethod = "POST")
	@RequestMapping(value = "/editQuickQpTagTempNum", method = { RequestMethod.GET, RequestMethod.POST })
	public String editQpTagTempNum(QpTagTemp record, HttpServletRequest request, HttpServletResponse response)
			throws UnsupportedEncodingException {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}

		int result = ydMapper.editQpTagTempNum(record);

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 步步赚后台页面配置-微信提现配置
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectCoinsWithdrawConfig", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectCoinsWithdrawConfig(CoinsWithdrawConfig record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		// String token = request.getParameter("token");
		// if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
		// return "{\"ret\":2,\"msg\":\"token is error!\"}";
		// else
		// redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<CoinsWithdrawConfig> list = cleanYdMapper.selectCoinsWithdrawConfig(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 步步赚后台页面配置-微信提现配置 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@RequestMapping(value = "/CoinsWithdrawConfigHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String CoinsWithdrawConfigHandle(CoinsWithdrawConfig record, HttpServletRequest request,
			HttpServletResponse response) throws UnsupportedEncodingException {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}
		int result = 0;

		record.setModifyUser(username);
		record.setCreateUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = cleanYdMapper.insertCoinsWithdrawConfig(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = cleanYdMapper.updateCoinsWithdrawConfig(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = cleanYdMapper.deleteCoinsWithdrawConfig(record);
		} else if ("copy".equals(request.getParameter("handle"))) {
			String newAppid = request.getParameter("newAppid");
			String newPrjid = request.getParameter("newPrjid");
			String newCha = request.getParameter("newCha");
			CoinsWithdrawConfig CoinsWithdrawConfig = new CoinsWithdrawConfig();
			CoinsWithdrawConfig.setAppid(newAppid);
			CoinsWithdrawConfig.setPrjid(newPrjid);
			CoinsWithdrawConfig.setCha(newCha);

			List<CoinsWithdrawConfig> list = cleanYdMapper.selectCoinsWithdrawConfig(CoinsWithdrawConfig);
			if (list != null && list.size() > 0) {
				return "{\"ret\":1,\"msg\":\"同种配置已存在!\"}";
			}

			List<CoinsWithdrawConfig> recordList = cleanYdMapper.selectCoinsWithdrawConfig(record);
			for (CoinsWithdrawConfig coinsConfig : recordList) {
				coinsConfig.setAppid(newAppid);
				coinsConfig.setPrjid(newPrjid);
				coinsConfig.setCha(newCha);
				coinsConfig.setModifyUser(username);
				coinsConfig.setCreateUser(username);
				result = cleanYdMapper.insertCoinsWithdrawConfig(coinsConfig);
			}
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 趣拍表情包 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectQpEmojiInfo", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectQpEmojiInfo(QpEmojiInfo record, HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<QpEmojiInfo> list = ydMapper.selectQpEmojiInfo(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 趣拍表情包 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/QpEmojiInfoHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String QpEmojiInfoHandle(QpEmojiInfo record, HttpServletRequest request, HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCreateUser(username);
		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertQpEmojiInfo(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateQpEmojiInfo(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteQpEmojiInfo(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 趣拍表情包 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectQpEmojiTitle", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectQpEmojiTitle(QpEmojiTitle record, HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<QpEmojiTitle> list = ydMapper.selectQpEmojiTitle(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 趣拍表情包 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/QpEmojiTitleHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String QpEmojiTitleHandle(QpEmojiTitle record, HttpServletRequest request, HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCreateUser(username);
		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertQpEmojiTitle(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateQpEmojiTitle(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteQpEmojiTitle(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 清理数据库配置页面 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectCleandbInfo", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectCleandbInfo(CleandbInfo record, HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<CleandbInfo> list = ydMapper.selectCleandbInfo(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 清理数据库配置页面 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/CleandbInfoHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String CleandbInfoHandle(CleandbInfo record, HttpServletRequest request, HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}

		int result = 0;

		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertCleandbInfo(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateCleandbInfo(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteCleandbInfo(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 表情包标题刷新
	 *
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value = "/refreshCache/title", method = { RequestMethod.GET, RequestMethod.POST })
	public String refreshCacheTitle(HttpServletRequest request, HttpServletResponse response) throws IOException {

		List<String> list = cleanYdMapper.queryListString(" select titleId from qp_emoji_title ");

		for (String string : list) {
			List<String> emojiUrl = cleanYdMapper
					.queryListString(" SELECT emojiUrl FROM `qp_emoji_info` WHERE titleId = '" + string
							+ "' and emojiUrl is not NULL ORDER BY sort LIMIT 3 ; ");
			String sql = "";
			if (emojiUrl == null || emojiUrl.size() == 0) {
				sql = " UPDATE `qp_emoji_title` SET `picture1` = '',`picture2` = '',`picture3` = '' WHERE `titleId` = '"
						+ string + "'; ";
			} else if (emojiUrl.size() == 1) {
				sql = " UPDATE `qp_emoji_title` SET `picture1` = '" + emojiUrl.get(0)
						+ "',`picture2` = '',`picture3` = '' WHERE `titleId` = '" + string + "'; ";
			} else if (emojiUrl.size() == 2) {
				sql = " UPDATE `qp_emoji_title` SET `picture1` = '" + emojiUrl.get(0) + "',`picture2` = '"
						+ emojiUrl.get(1) + "',`picture3` = '' WHERE `titleId` = '" + string + "'; ";
			} else if (emojiUrl.size() == 3) {
				sql = " UPDATE `qp_emoji_title` SET `picture1` = '" + emojiUrl.get(0) + "',`picture2` = '"
						+ emojiUrl.get(1) + "',`picture3` = '" + emojiUrl.get(2) + "' WHERE `titleId` = '" + string
						+ "'; ";
			}
			cleanYdMapper.execSql(sql);
		}

		HttpRequester resq = new HttpRequester();
		HttpRespons respons = resq.sendGet("https://edc.vigame.cn:6115/clean/refreshCache?mapid=8");
		return respons.getMessage().replace("\r\n", "").trim();
	}

	@CrossOrigin
	@RequestMapping(value = "/importQpEmojiInfo", method = { RequestMethod.GET, RequestMethod.POST })
	public String importQpEmojiInfo(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request,
			HttpServletResponse response) throws IOException {

		JSONObject obj = new JSONObject();
		try {
			if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
				List<QpEmojiInfo> list = new ArrayList<QpEmojiInfo>();
				Workbook workbook = Workbook.getWorkbook(file.getInputStream());
				Sheet sheet = workbook.getSheet(0);

				int row = sheet.getRows();
				for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
					if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
						continue;

					String[] vals = new String[7];
					for (int c = 0; c < 7; c++) {
						vals[c] = sheet.getCell(c, r).getContents();
					}

					QpEmojiInfo gad = new QpEmojiInfo();
					gad.setAppid(vals[0].trim());
					gad.setPrjid(vals[1].trim());
					gad.setCha(vals[2].trim());
					gad.setTitleId(vals[3].trim());
					gad.setEmojiUrl(vals[4].trim());
					gad.setStatus(vals[5].trim());
					gad.setSort(vals[6].trim());

					list.add(gad);

					obj.put("ret", 1);
					obj.put("msg", "上传文件成功");
				}
				ydMapper.insertQpEmojiInfoList(list);
			} else {
				obj.put("ret", 0);
				obj.put("msg", "上传文件有误，需要.xls文件!");
			}

		} catch (Exception e) {
			// 上传异常
			e.printStackTrace();
			obj.put("ret", 0);
			obj.put("msg", "导入失败");
		}

		return obj.toJSONString();
	}

	/**
	 * 地图城市 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperMapCity", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperMapCity(SuperMapCity record, HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperMapCity> list = ydMapper.selectSuperMapCity(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 地图城市 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/SuperMapCityHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String SuperMapCityHandle(SuperMapCity record, HttpServletRequest request, HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}
		int result = 0;

		record.setCreateUser(username);
		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertSuperMapCity(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateSuperMapCity(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteSuperMapCity(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 地图城市素材 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperMapMaterial", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperMapMaterial(SuperMapMaterial record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperMapMaterial> list = ydMapper.selectSuperMapMaterial(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 地图城市素材 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/SuperMapMaterialHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String SuperMapMaterialHandle(SuperMapMaterial record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCreateUser(username);
		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertSuperMapMaterial(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateSuperMapMaterial(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteSuperMapMaterial(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 地图配置 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperMapConfig", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperMapConfig(SuperMapConfig record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperMapConfig> list = ydMapper.selectSuperMapConfig(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 地图配置 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/SuperMapConfigHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String SuperMapConfigHandle(SuperMapConfig record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCreateUser(username);
		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertSuperMapConfig(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateSuperMapConfig(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteSuperMapConfig(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 趣拍配置 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperQupaiConfig", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperQupaiConfig(SuperQupaiConfig record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperQupaiConfig> list = ydMapper.selectSuperQupaiConfig(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 趣拍配置 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/SuperQupaiConfigHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String SuperQupaiConfigHandle(SuperQupaiConfig record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCreateUser(username);
		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertSuperQupaiConfig(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateSuperQupaiConfig(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteSuperQupaiConfig(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * Cenable控制 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectSuperLockCenable", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectSuperLockCenable(SuperLockCenable record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<SuperLockCenable> list = ydMapper.selectSuperLockCenable(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * Cenable控制 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/SuperLockCenableHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String SuperLockCenableHandle(SuperLockCenable record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCuser(username);
		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertSuperLockCenable(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateSuperLockCenable(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteSuperLockCenable(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 应用支付查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectAppWbPayInfo", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectAppWbPayInfo(HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		Map<String, String> map = new HashMap<String, String>();
		map.put("payname", request.getParameter("payname"));
		map.put("pid", request.getParameter("pid"));
		map.put("appid", request.getParameter("appid"));
		map.put("startTime", request.getParameter("startTime"));
		map.put("endTime", request.getParameter("endTime"));

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<AppWbPayInfo> list = ydMapper.selectAppWbPayInfo(map);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 应用支付数据 导出
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/exportAppWbPayInfo", method = { RequestMethod.GET, RequestMethod.POST })
	public String exportAppWbPayInfo(HttpServletRequest request, HttpServletResponse response) {
		// 数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		Map<String, String> map = new HashMap<String, String>();
		map.put("payname", BlankUtils.checkNull(request, "payname"));
		map.put("pid", BlankUtils.checkNull(request, "pid"));
		map.put("appid", BlankUtils.checkNull(request, "appid"));
		map.put("startTime", BlankUtils.checkNull(request, "startTime"));
		map.put("endTime", BlankUtils.checkNull(request, "endTime"));

		// 数据内容
		List<AppWbPayInfo> list = ydMapper.selectAppWbPayInfo(map);

		String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
		Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");
		List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
		Map<String, Object> contentMap = null;

		for (AppWbPayInfo temp : list) {

			headerMap.put("orderid", "自生成订单id");
			headerMap.put("uid", "渠道订单id");
			headerMap.put("money", "支付金额 单位分");
			headerMap.put("paytype", "支付类型");
			headerMap.put("imei", "openid");
			headerMap.put("pid", "项目id");
			headerMap.put("appid", "产品id");
			headerMap.put("orderstatus", "订单状态");
			headerMap.put("payname", "用户标识");
			headerMap.put("paynote", "支付备注");
			headerMap.put("createtime", "创建时间");
			headerMap.put("param1", "参数1");
			headerMap.put("param2", "参数2");
			headerMap.put("param3", "参数3");

			contentMap = new LinkedHashMap<String, Object>();

			contentMap.put("orderid", temp.getOrderid());
			contentMap.put("uid", temp.getUid());
			contentMap.put("money", temp.getMoney());
			contentMap.put("paytype", temp.getPaytype());
			contentMap.put("imei", temp.getImei());
			contentMap.put("pid", temp.getPid());
			contentMap.put("appid", appMap.get(temp.getAppid()).get("app_name") + "_" + temp.getAppid());
			contentMap.put("orderstatus", temp.getOrderid());
			contentMap.put("payname", temp.getPayname());
			contentMap.put("paynote", temp.getPaynote());
			contentMap.put("createtime", temp.getCreatetime());
			contentMap.put("param1", temp.getParam1());
			contentMap.put("param2", temp.getParam2());
			contentMap.put("param3", temp.getParam3());

			contentList.add(contentMap);
		}

		String fileName = "应用支付数据_" + DateTime.now().toString("yyyyMMdd") + ".xls";
		JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);

		return null;

	}

	/**
	 * 应用补丁管理
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectApplicationPatchInfo", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectApplicationPatchInfo(ApplicationPatchInfo record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<ApplicationPatchInfo> list = ydMapper.selectApplicationPatchInfo(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 应用补丁 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/applicationPatchInfoHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String applicationPatchInfoHandle(ApplicationPatchInfo record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCreateUser(username);
		record.setUpdateUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			String md5 = null;
			if (!BlankUtils.checkBlank(record.getPatchUrl())) {
				String patch_url = record.getPatchUrl();

				try {
					File file = FileUtils.getFile(patch_url);
					md5 = DigestUtils.md5Hex(FileUtils.getBytesByFile(file));
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			if (!BlankUtils.checkBlank(md5)) {
				record.setMd5(md5);
				List<ApplicationPatchInfo> exit = ydMapper.selectApplicationPatchInfo(record);
				if (exit != null && exit.size() > 0) {
					return ReturnJson.toErrorJson("已经存在重复的补丁");
				}
				result = ydMapper.insertApplicationPatchInfo(record);
			}

		} else if ("edit".equals(request.getParameter("handle"))) {
			String md5 = null;
			if (!BlankUtils.checkBlank(record.getPatchUrl())) {
				String patch_url = record.getPatchUrl();

				try {
					File file = FileUtils.getFile(patch_url);
					md5 = DigestUtils.md5Hex(FileUtils.getBytesByFile(file));
				} catch (Exception e) {
					e.printStackTrace();
				}
			}

			record.setMd5(md5);
			result = ydMapper.updateApplicationPatchInfo(record);

		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteApplicationPatchInfo(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	public static void main(String[] args) throws Exception {
		File file = FileUtils.getFile("https://a.vigame.cn/apk/push/kxp/patch.apk");
		String md5 = DigestUtils.md5Hex(FileUtils.getBytesByFile(file));
		System.out.println(md5);
	}

	/**
	 * 走路赚 计步管家配置 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectCoinsConfig", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectCoinsConfig(CoinsConfig record, HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// // token验证
		// String token = request.getParameter("token");
		// if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
		// return "{\"ret\":2,\"msg\":\"token is error!\"}";
		// else
		// redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<CoinsConfig> list = cleanYdMapper.selectCoinsConfig(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 走路赚 计步管家配置 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/CoinsConfigHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String CoinsConfigHandle(CoinsConfig record, HttpServletRequest request, HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCreateUser(username);
		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			try {
				result = cleanYdMapper.insertCoinsConfig(record);
			} catch (Exception e) {
				return "{\"ret\":1,\"msg\":\"该产品已配置!\"}";
			}

		} else if ("edit".equals(request.getParameter("handle"))) {
			result = cleanYdMapper.updateCoinsConfig(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = cleanYdMapper.deleteCoinsConfig(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 应用版本跟新 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectApplicationIterationConfig", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectApplicationIterationConfig(ApplicationIterationConfig record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// // token验证
		// String token = request.getParameter("token");
		// if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
		// return "{\"ret\":2,\"msg\":\"token is error!\"}";
		// else
		// redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<ApplicationIterationConfig> list = ydMapper.selectApplicationIterationConfig(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 应用版本跟新 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/ApplicationIterationConfigHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String ApplicationIterationConfigHandle(ApplicationIterationConfig record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCreateUser(username);
		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			ApplicationIterationConfig r = new ApplicationIterationConfig();
			r.setCha(record.getCha());
			r.setPrjid(record.getPrjid());
			r.setAppid(record.getAppid());
			List<ApplicationIterationConfig> list = ydMapper.selectApplicationIterationConfig(record);
			if (list != null && list.size() > 0) {
				return "{\"ret\":0,\"msg\":\"该配置已存在!\"}";
			}
			result = ydMapper.insertApplicationIterationConfig(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateApplicationIterationConfig(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteApplicationIterationConfig(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 应用安装激活 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectApplicationAutoActivation", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectApplicationAutoActivation(ApplicationAutoActivation record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		// token验证
		String token = request.getParameter("token");
		if ("dnwx1002dnwx1002".equals(token) || "wbtokenukpfllbbyt72hhamm0i7dr1g".equals(token)) {
			// 特殊不处理
		} else {
			// 校验权限
			if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
				return ReturnJson.error(Constants.ErrorToken);
			}
		}

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<ApplicationAutoActivation> list = ydMapper.selectApplicationAutoActivation(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 应用安装激活 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/ApplicationAutoActivationHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String ApplicationAutoActivationHandle(ApplicationAutoActivation record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCreateUser(username);
		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			Integer exist = ydMapper.selectAppExits(record);
            if (exist != null) {
                return ReturnJson.success("该应用已存在");
            }
            result = ydMapper.insertApplicationAutoActivation(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateApplicationAutoActivation(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteApplicationAutoActivation(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 趣拍类别配置 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectQupaiConfigClassify", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectQupaiConfigClassify(QupaiConfigClassify record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<QupaiConfigClassify> list = ydMapper.selectQupaiConfigClassify(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 趣拍类别配置操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/QupaiConfigClassifyHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String QupaiConfigClassifyHandle(QupaiConfigClassify record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertQupaiConfigClassify(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateQupaiConfigClassify(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteQupaiConfigClassify(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 一键换脸 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectQupaiAiZone", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectQupaiConfigClassify(QupaiAiZone record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		// if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
		// return "{\"ret\":2,\"msg\":\"token is error!\"}";
		// else
		// redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<QupaiAiZone> list = ydMapper.selectQupaiAiZone(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 一键换脸 分类下拉 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectQupaiAiZoneType", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectQupaiAiZoneType(QupaiAiZone record, HttpServletRequest request, HttpServletResponse response) {
		// token验证
		String token = request.getParameter("token");

		 if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			 return "{\"ret\":2,\"msg\":\"token is error!\"}";
		 else
			 redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		
		JSONObject result = new JSONObject();
		//0 国内，1 国外
		if(("1").equals(record.getForeign()) ){
			
			String sql = "SELECT configContent FROM `qupai_config_classify` where id =2;";
			List<String> list = ydMapper.queryListString(sql);
			JSONArray parse = (JSONArray) com.alibaba.fastjson.JSONArray.parse(list.get(0));
			parse.remove("Popular");
			parse.remove("NEW");

			result.put("ret", 1);
			result.put("data", parse);

			return result.toJSONString();
		}else {
			String sql = "SELECT configContent FROM `qupai_config_classify` where id =1;";
			List<String> list = ydMapper.queryListString(sql);
			JSONArray parse = (JSONArray) com.alibaba.fastjson.JSONArray.parse(list.get(0));
			parse.remove("热门");
			parse.remove("最新");

			result.put("ret", 1);
			result.put("data", parse);

			return result.toJSONString();
		}
		
	}

	/**
	 * 一键换脸操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/QupaiAiZoneHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String QupaiAiZoneHandle(QupaiAiZone record, HttpServletRequest request, HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;
		record.setCreateUser(username);
		record.setModifyUser(username);

		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertQupaiAiZone(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateQupaiAiZone(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteQupaiAiZone(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 推荐分区 查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectQupaiRecommendZone", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectQupaiRecommendZone(QupaiRecommendZone record, HttpServletRequest request,
			HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();

		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<QupaiRecommendZone> list = ydMapper.selectQupaiRecommendZone(record);
		long size = ((Page) list).getTotal();
		result.put("ret", 1);
		result.put("data", list);
		result.put("totalCount", size);

		return result.toJSONString();
	}

	/**
	 * 一键换脸操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/QupaiRecommendZoneHandle", method = { RequestMethod.GET, RequestMethod.POST })
	public String QupaiRecommendZoneHandle(QupaiRecommendZone record, HttpServletRequest request,
			HttpServletResponse response) {
		// 校验权限
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		int result = 0;

		record.setCreateUser(username);
		record.setModifyUser(username);
		if ("add".equals(request.getParameter("handle"))) {
			result = ydMapper.insertQupaiRecommendZone(record);
		} else if ("edit".equals(request.getParameter("handle"))) {
			result = ydMapper.updateQupaiRecommendZone(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			result = ydMapper.deleteQupaiRecommendZone(record);
		}

		if (result > 0)
			return "{\"ret\":1,\"msg\":\"操作成功!\"}";
		else
			return "{\"ret\":0,\"msg\":\"无效操作!\"}";

	}

	/**
	 * 应用-v2锁屏和D联合查询
	 *
	 * @param value
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/selectLcokAndDConfig", method = { RequestMethod.GET, RequestMethod.POST })
	public String selectLcokAndDConfig(SuperLockNew record, HttpServletRequest request, HttpServletResponse response) {
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return ReturnJson.error(Constants.ErrorToken);
		} else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		JSONObject result = new JSONObject();

//		String unionSql = "select t1.*,t2.cha d_cha,t2.prjid d_prjid,t2.appid d_appid,t2.scheduleReport d_scheduleReport,t2.rate d_rate,t2.status d_status,t2.atTime d_atTime "
//				+ "from super_lock_new t1 left join super_auto_activation t2 on IFNULL(t1.prjid,'') =  IFNULL(t2.prjid,'') and IFNULL(t1.cha,'') = IFNULL(t2.cha,'') and  t1.appid =  t2.appid where t1.appid = "
//				+ record.getAppid() + " union "
//				+ " select t1.*,t2.cha d_cha,t2.prjid d_prjid,t2.appid d_appid, t2.scheduleReport d_scheduleReport,t2.rate d_rate,t2.status d_status,t2.atTime d_atTime  "
//				+ " from super_lock_new t1 right join super_auto_activation t2 on  IFNULL(t1.prjid,'') =  IFNULL(t2.prjid,'') and IFNULL(t1.cha,'') = IFNULL(t2.cha,'') and  t1.appid =  t2.appid where t2.appid = "
//				+ record.getAppid();

        String unionSql = "select t1.*,t2.cha d_cha,t2.prjid d_prjid,t2.appid d_appid,t2.scheduleReport d_scheduleReport,t2.rate d_rate,t2.status d_status,t2.atTime d_atTime "
                + "from super_lock_new t1 left join super_auto_activation t2 on IFNULL(t1.prjid,'') =  IFNULL(t2.prjid,'') and IFNULL(t1.cha,'') = IFNULL(t2.cha,'') and  t1.appid =  t2.appid where t1.appid in ("
                + record.getAppid() + ") union "
                + " select t1.*,t2.cha d_cha,t2.prjid d_prjid,t2.appid d_appid, t2.scheduleReport d_scheduleReport,t2.rate d_rate,t2.status d_status,t2.atTime d_atTime  "
                + " from super_lock_new t1 right join super_auto_activation t2 on  IFNULL(t1.prjid,'') =  IFNULL(t2.prjid,'') and IFNULL(t1.cha,'') = IFNULL(t2.cha,'') and  t1.appid =  t2.appid where t2.appid in ("
                + record.getAppid() + ") ";

		List<Map<String, Object>> unionList = ydMapper.queryListMap(unionSql);

		if (unionList != null && unionList.size() > 0) {
			for (Map<String, Object> map : unionList) {
				String cha = (String) map.get("cha");
				String prjid = (String) map.get("prjid");
				String appid = (String) map.get("appid");
				if (cha == null) {
					map.put("cha", map.get("d_cha"));
				}
				if (prjid == null) {
					map.put("prjid", map.get("d_prjid"));
				}
				if (appid == null) {
					map.put("appid", map.get("d_appid"));
				}
				// 项目id 的关联 渠道
				String queryCha = adService.queryChaByPid((String) map.get("prjid"));
				map.put("queryCha", queryCha);
			}
		}

		result.put("ret", 1);
		result.put("data", unionList);

		return result.toJSONString();
	}

	/**
	 * 出包工具自动生成锁屏配置
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/v2LockAutoGeneration", method = { RequestMethod.GET, RequestMethod.POST })
	public String v2LockAutoGeneration(SuperLockNew record, String type, HttpServletRequest request,
			HttpServletResponse response) {

		/**
		 * 1.查询锁屏是否有配置 有 ：不处理 没有：添加对应的配置
		 * 
		 * type 100 wifi/清理 200 网赚 300 大字报 天气 相机
		 * 
		 */
		
		/** lock_template_config 表
	 	*   id  对应产品 remarks
	 	  	1        应用商店投放  (**ml)
			2 	投商店(**yy) wifi/清理
			3 	投商店(**yy) 网赚
			4 	投商店(**yy) 大字报 天气 相机
			5 	兜底包(csj，ks，dy，opwx，viwx，xmwx，hwwx) wifi/清理
			6 	兜底包(csj，ks，dy，opwx，viwx，xmwx，hwwx) 网赚
			7 	兜底包(csj，ks，dy，opwx，viwx，xmwx，hwwx) 大字报 天气 相机	
			8 	小渠道  xqd
		 */
		
		// 配置id
		int id = 0;
		
		JSONObject result = new JSONObject();
		String user = "AutoGeneration";

		
		// 参数校验
		if (BlankUtils.checkBlank(record.getAppid()) || BlankUtils.checkBlank(record.getCha())
				|| BlankUtils.checkBlank(type)) { 

			result.put("code", 200);
			result.put("message", "请求参数不全");
			return result.toJSONString();
		}

		try {
			List<SuperLockNew> list = ydMapper.selectSuperLockNew(record);
			if (list != null && list.size() > 0) {
				result.put("code", 200);
				result.put("message", "该配置已经存在");

				return result.toJSONString();
			}
			String appid = record.getAppid();
			String cha = record.getCha();
			// 应用商店投放 (**ml)
			if (cha.endsWith("ml")) {
				id = 1;
			} else if (cha.endsWith("yy")) {
				// 投商店(**yy)
				if (type.equals("100")) {
					id = 2;
				} else if (type.equals("300")) {
					id = 3;
				} else if (type.equals("200")) {
					id = 4;
				}

				//opcsj，vicsj，hwcsj，xmcsj
			} else if (cha.equals("csj") || cha.equals("ks") || cha.equals("dy") || cha.equals("opwx")
					|| cha.equals("viwx") || cha.equals("xmwx") || cha.equals("hwwx")|| cha.equals("opcsj")
					|| cha.equals("hwgdt") || cha.equals("hwtxzn")
					|| cha.equals("vicsj") || cha.equals("hwcsj")|| cha.equals("xmcsj")) {
				/**
				 * 兜底包 (csj，ks，dy，opwx，viwx，xmwx，hwwx)
				 */
				// 投商店(**yy)
				if (type.equals("100")) {
					id = 5;
				} else if (type.equals("300")) {
					id = 6;
				} else if (type.equals("200")) {
					id = 7;
				}

			} else if (cha.equals("xqd")) {
				// 小渠道 xqd
				id = 8;
			}

			if (id != 0) {
				SuperLockNew config = ydMapper.selectLockTemplateConfig(id);

				config.setAppid(appid);
				config.setCha(cha);
				config.setPrjid("");
				config.setModifyUser(user);
				config.setCuser(user);

				ydMapper.insertSuperLockNew(config);
			}

			result.put("code", 200);
			result.put("message", "ok");

			return result.toJSONString();
		} catch (Exception e) {
			e.printStackTrace();

			result.put("code", 500);
			result.put("message", "ok");

			return result.toJSONString();

		}

	}

	private boolean doesCHAExist(String cha) {

		for (String s : CHA.split(" ")) {

			if (s.equals(cha)) {

				return true;
			}
		}
		return false;
	}

	public static final String CHA = "opyy viyy hwyy xmyy txyy";
	public String toolRiskControlConfiguration(SuperLockNew record) {

		String appid = record.getAppid();
		String cha = record.getCha();
		JSONObject result = new JSONObject();
		result.put("code", 200);
		result.put("message", "ok");
		if (org.apache.commons.lang3.StringUtils.isBlank(appid)) {

//			return ReturnJson.error(Constants.ParamError);
			result.put("message", Constants.ParamError.getMsg());
			return result.toJSONString();
		}

		// 获取 (opyy+viyy+hwyy+xmyy+txyy）=无法识别-10001_viyy

		if (!org.apache.commons.lang3.StringUtils.isBlank(cha)) {

			List<AppSafeConfig> appSafeConfigList = ydMapper.selectAppSafeConfigByAppidAndCha();

			if (ObjectUtils.isEmpty(appSafeConfigList)) {

				result.put("message", "yy渠道自动生成风控配置模板(无法识别-10001_viyy)不存在!");
//				return ReturnJson.toErrorJson("yy渠道自动生成风控配置模板(无法识别-10001_viyy)不存在!");
				return result.toJSONString();
			}

			// 是否有重复数据
			if (!ydMapper.selectAppSafeConfigByAppidAndChaCondition(appid, cha).isEmpty()) {

				result.put("message", "yy渠道自动生成风控配置已存在!");
				return result.toJSONString();
//				return ReturnJson.toErrorJson("yy渠道自动生成风控配置已存在!");
			}

			AppSafeConfig config = appSafeConfigList.get(0);

			config.setAppid(record.getAppid());
			config.setCha(record.getCha());
			config.setCreateUser(record.getCuser());
			config.setModifyUser(record.getCuser());
			config.setPrjid(null);

			ydMapper.insertAppSafe(config);

		}

		return result.toJSONString();

	}

    /**
     * V3风控生成
     * @param record
     * @return
     */
    public String v3SubControlGeneration(SuperLockNew record) {

        String appid = record.getAppid();
        String cha = record.getCha();
        JSONObject result = new JSONObject();
        result.put("code", 200);
        result.put("message", "ok");

        if (!org.apache.commons.lang3.StringUtils.isBlank(appid)
                && !org.apache.commons.lang3.StringUtils.isBlank(cha)) {

            SuperSafeControlV3VO v3VO = cleanYdMapper.selectV3Template();
            if (v3VO == null) {

                result.put("message", "V3模板配置已存在");
                return result.toJSONString();
            }

            if (cleanYdMapper.selectV3ConfigExist(record.getAppid(), record.getCha()) != null) {

                result.put("message", "该V3配置已存在");
//				return ReturnJson.toErrorJson("yy渠道自动生成风控配置模板(无法识别-10001_viyy)不存在!");
                return result.toJSONString();
            }

            SuperSafeControlV3 superSafeControlV3 = new SuperSafeControlV3();
            v3VO.setPrjid(null);
            v3VO.setCreateUser(record.getCuser());
            v3VO.setModifyUser(record.getCuser());
            v3VO.setAppid(record.getAppid());
            v3VO.setCha(record.getCha());
            BeanUtils.copyProperties(v3VO, superSafeControlV3);


            superSafeControlV3Mapper.insertSuperSafe(superSafeControlV3);
            return result.toJSONString();
        }

        result.put("message", Constants.ParamError.getMsg());
        return result.toJSONString();

    }

	private static final String xiaomi = "xmwx-mtg-xmcsj-xmgdt-xmks-xmttzn-xiaomiml-xiaomi-xmyy";
	private static final String ovpb1 = "viwx-vitxzn-vicsj-vigdt-viks-vittzn-vivoml-vivo-viyy-opwx-optxzn-opcsj-opgdt-opks-opttzn-oppoml-oppo-opyy";
	private static final String yymr = "gdt-dy-csj-ks-tt-hwwx-hwtxzn-hwcsj-hwgdt-hwks-hwttzn-huawei-xmtxzn-txyy-baibao-hwyy";

	/**
	 * 返回常规配置对应的渠道id
	 * @param channel
	 * @return
	 */
	private String confirmChannelConfiguration(String channel) {

		if (org.apache.commons.lang3.StringUtils.isBlank(channel)) {
			return "-1";
		}

        for (String xiaomiCha : xiaomi.split("-")) {

            if (xiaomiCha.equals(channel)) {

                return "38373888";
            }
        }

        for (String ovpb1Cha : ovpb1.split("-")) {

            if (ovpb1Cha.equals(channel)) {

                return "38373999";
            }
        }

        for (String yymrCha : yymr.split("-")) {

            if (yymrCha.equals(channel)) {

                return "38373777";
            }
        }

		//return xiaomi.contains(channel) ? "38373888" : (ovpb1.contains(channel)? "38373999" : yymr.contains(channel) ? "38373777" : "38373999");
        return "38373999";
	}

	/**
	 * 新版自动生成配置接口
	 * @param record
	 * @param type
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/v2LockAutoGeneration2", method = { RequestMethod.GET, RequestMethod.POST })
	public String v2LockAutoGeneration2(SuperLockNew record, String type, HttpServletRequest request,
									   HttpServletResponse response) {


		String cha = record.getCha();
		String appid = record.getAppid();
		// 常规配置：全渠道（非xqd）+全线产品 = 38373999
		//特殊配置：xqd渠道+全线产品 = 38373997

		String user = "AutoGeneration";
		if (org.apache.commons.lang3.StringUtils.isBlank(cha) ||
				org.apache.commons.lang3.StringUtils.isBlank(appid)
//				||
//				ydMapper.selectAPPIdDoesItExist(appid) == 0

		) {

			return ReturnJson.error(Constants.ParamError);
		}

		boolean isAppid = ydMapper.selectAPPIdDoesItExist(appid) != 0;

		String res = "";
		if (isAppid) {

			if ("xqd".equals(cha)) {

				// 特殊配置
				List<SuperLockNew> news = ydMapper.selectLockNewXqd();

				if (news.isEmpty()) {
					res = "特殊配置模板不存在!";
				}
				List<SuperLockNew> superLockNews = ydMapper.selectSuperLockNew(record);
				if (superLockNews.size() > 0) {

					res =  "特殊配置已经存在!";
				}

				if (org.apache.commons.lang3.StringUtils.isBlank(res)) {


					SuperLockNew superLockNew = news.get(0);
					superLockNew.setAppid(appid);
					superLockNew.setCha(cha);
					superLockNew.setPrjid(null);
					superLockNew.setCuser(user);
					superLockNew.setModifyUser(user);
					ydMapper.insertSuperLockNew(superLockNew);
				}

			} else {

				// 非特殊配置
				List<SuperLockNew> news = ydMapper.selectLockNewNotXqd(confirmChannelConfiguration(cha));
				if (news.isEmpty()) {
					res = "常规配置模板不存在!";
				}
				List<SuperLockNew> superLockNews = ydMapper.selectSuperLockNew(record);
				if (superLockNews.size() > 0) {

					res = "常规配置已经存在!";
				}

				if (org.apache.commons.lang3.StringUtils.isBlank(res)) {

					SuperLockNew superLockNew = news.get(0);
					superLockNew.setAppid(appid);
					superLockNew.setCha(cha);
					superLockNew.setPrjid(null);
					superLockNew.setCuser(user);
					superLockNew.setModifyUser(user);
					ydMapper.insertSuperLockNew(superLockNew);
				}

			}
		}

		record.setCuser(user);
		String s = "";
		JSONObject result = new JSONObject();
		result.put("code", 200);
		result.put("message", "ok");
		if (!"xqd".equals(cha)) {

			//s = toolRiskControlConfiguration(record); // super_safe_control风控
            s = v3SubControlGeneration(record);
            result = JSONObject.parseObject(s);
		}
		result.put("message", result.get("message") + "-" + res);
		return result.toJSONString();
//		return ReturnJson.success();
	}
	
	@RequestMapping(value = "/test", method = { RequestMethod.GET, RequestMethod.POST })
	public String test(SuperLockNew record, String type, HttpServletRequest request,
			HttpServletResponse response) {
		//redisTemplate.opsForHash().put("test", "ss","ss" );
		//redisTemplate.expire("test", 20 * 60, TimeUnit.SECONDS);
		Map<String,String> map = new HashMap();
		map.put("aac", "aa");
		redisTemplate.opsForHash().putAll("test", map);
		return "ok";
	}
	
	/**
	 * 出包工具自动删除pid配置
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/v2LockAutoDelete", method = { RequestMethod.GET, RequestMethod.POST })
	public String v2LockAutoDelete(String pid, HttpServletRequest request,
			HttpServletResponse response) {

		
		JSONObject result = new JSONObject();
		
		// 参数校验
		if (BlankUtils.checkBlank(pid)) { 

			result.put("code", 200);
			result.put("message", "请求参数不全");
			return result.toJSONString();
		}

		try {
			//完成测试之后，需求单状态更改为已完成，然后后台自动根据相对应产品id把测试人员账号创建的需求单（测试配置）清除
			ydMapper.execSql("DELETE FROM `super_lock_new` where prjid = '"+pid+"' and cuser in ('panhd','wusihui','hegs','wangcr') and creatTime >= '2022-02-23'");
			
			result.put("code", 200);
			result.put("message", "ok");

			return result.toJSONString();
		} catch (Exception e) {
			e.printStackTrace();

			result.put("code", 500);
			result.put("message", "ok");

			return result.toJSONString();

		}

	}


	/**
	 *超前参数配置
	 *
	 * @param request
	 * @param record
	 *            参数
	 * @return
	 */
	@ApiOperation(value = "超前参数配置", notes = "超前参数配置")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "id", value = "id", required = false, type = "string"),
			@ApiImplicitParam(name = "appid", value = "产品id", required = true, type = "string"),
			@ApiImplicitParam(name = "cha", value = "渠道", required = false, type = "string"),
			@ApiImplicitParam(name = "prjid", value = "项目", required = false, type = "string"),
			@ApiImplicitParam(name = "showPrivacy", value = "隐私展示 0：显示； 1：不显示", required = true, type = "string"),
			@ApiImplicitParam(name = "showWallpaper", value = "壁纸展示  0：显示； 1：不显示", required = true, type = "string"),
			@ApiImplicitParam(name = "status", value = "状态  0：关闭； 1：开启", required = true, type = "string"),
			@ApiImplicitParam(name = "showPermission", value = "权限展示,0展示,1不展示,默认0", required = true, type = "string"),
			@ApiImplicitParam(name = "handle", value = "操作", required = true, type = "string")
	})
	@ApiResponses(value = {
//			@ApiResponse(code = 1, message = "操作成功",response = ActivityTakeVo.class),
//			@ApiResponse(code = 0, message = "操作失败",response = Constants.class)
	})
	@PostMapping(value = "/superAdvanceConfigHandle/handle")
	public String SuperAdvanceConfigHandle(HttpServletRequest request, @Validated SuperAdvanceConfigDTO record) {

		String token = request.getParameter("token");

		String username = "";
		if (token.startsWith("wbtoken")) {
			com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
			username = currUserVo.getLogin_name();
		} else {
			WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			username = WaibaoUser.getUser_name();
		}

		record.setCreateUser(username);
		record.setModifyUser(username);

		if ("add".equals(request.getParameter("handle"))) {

			return ydService.insertSuperAdvanceConfig(record);
		} else if ("edit".equals(request.getParameter("handle"))) {

			return ydService.updateSuperAdvanceConfig(record);
		} else if ("del".equals(request.getParameter("handle"))) {
			return ydService.deleteSuperAdvanceConfig(record);
		} else if ("batch".equals(request.getParameter("handle"))) {
			ydService.batchEditSuperAdvanceConfig(record);
		}
		return ReturnJson.success();
	}

	@ApiOperation(value = "超前参数配置查询", notes = "超前参数配置查询")
//	@ApiResponses(value = {
//			@ApiResponse(code = 1, message = "操作成功",response = ActivityTakeVo.class),
//			@ApiResponse(code = 0, message = "操作失败",response = Constants.class)
//	})
	@ApiImplicitParams({
			@ApiImplicitParam(name = "appid", value = "产品id", required = false, type = "string"),
			@ApiImplicitParam(name = "cha", value = "渠道", required = false, type = "string"),
			@ApiImplicitParam(name = "prjid", value = "项目", required = false, type = "string"),
			@ApiImplicitParam(name = "status", value = "状态  0：关闭； 1：开启", required = false, type = "string"),
			@ApiImplicitParam(name = "pageNum", value = "页码", required = false, type = "string"),
			@ApiImplicitParam(name = "pageSize", value = "每页条数", required = false, type = "string")
	})
	@PostMapping(value = "/selectSuperAdvanceConfigByCondition")
	public String selectSuperAdvanceConfigByCondition(HttpServletRequest request,
													  SuperAdvanceConfigDTO superAdvanceConfig,
													  @RequestParam(value = "pageNum") Integer pageNum,
													  @RequestParam(value = "pageSize") Integer pageSize) {

		// token验证
		String token = request.getParameter("token");
		if ("dnwx1002dnwx1002".equals(token) || "wbtokenukpfllbbyt72hhamm0i7dr1g".equals(token)) {
			// 特殊不处理
		} else {
			// 校验权限
			if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
				return ReturnJson.error(Constants.ErrorToken);
			}
		}

		return ydService.selectSuperAdvanceConfig(superAdvanceConfig, pageNum, pageSize);

	}

	@ApiOperation(value = "删除数盟白名单配置", notes = "删除数盟白名单配置")
	@PostMapping(value = "/deleteFilterListById")
	public String deleteFilterListById(HttpServletRequest request, @RequestBody DnwxFilterListDTO idListQuery) {

		try {
			checkTokenAndReturnUserName(request, idListQuery.getToken());
		} catch (Exception e) {
			return ReturnJson.error(Constants.ErrorToken);
		}
		return ydService.deleteFilterListById(idListQuery.getIdList());
	}

	@ApiOperation(value = "新增数盟白名单配置", notes = "新增数盟白名单配置")
	@PostMapping(value = "/insertFilterList")
	public String insertFilterList(HttpServletRequest request, @Validated DnwxFilterListDTO filterDTO) {

		try {
			filterDTO.setCreateUser(checkTokenAndReturnUserName(request, null));
		} catch (Exception e) {
			return ReturnJson.error(Constants.ErrorToken);
		}
		return ydService.insertFilterList(filterDTO);
	}

	@ApiOperation(value = "根据条件获取数盟白名单配置", notes = "根据条件获取数盟白名单配置")
	@ApiResponses(value = {
			@ApiResponse(code = 1, message = "操作成功",response = DnwxFilterListVO.class),
			@ApiResponse(code = 0, message = "操作失败",response = Constants.class)
	})
	@PostMapping(value = "/selectFilterList")
	public String selectFilterList(HttpServletRequest request, @Validated DnwxFilterListQuery query) {

		try {
			checkTokenAndReturnUserName(request, null);
		} catch (Exception e) {
			return ReturnJson.error(Constants.ErrorToken);
		}
		return ydService.selectFilterList(query);
	}

	/**
	 * 校验token，获取用户名
	 */
	public String checkTokenAndReturnUserName(HttpServletRequest request, String token) throws Exception {

		String headerToken = request.getHeader("token");
		token = org.apache.commons.lang3.StringUtils.isBlank(token) ?
				(org.apache.commons.lang3.StringUtils.isBlank(headerToken) ? request.getParameter("token") : headerToken) : token;

		if (org.apache.commons.lang3.StringUtils.isBlank(token) || !redisTemplate.hasKey(token)) {

			throw new RuntimeException();
		} else {

			String username;
			if (token.startsWith("wbtoken")) {
				com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
				username = currUserVo.getLogin_name();
			} else {
				WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
				username = WaibaoUser.getUser_name();
			}
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
			return username;
		}
	}

}
