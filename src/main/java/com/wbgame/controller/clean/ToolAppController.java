package com.wbgame.controller.clean;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.aop.ApiNeed;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.exception.UnLoginException;
import com.wbgame.mapper.clean.master.CleanYdMapper;
import com.wbgame.mapper.clean.master.FaceModelMapper;
import com.wbgame.mapper.slave2.YdSlave2Mapper;
import com.wbgame.pojo.clean.*;
import com.wbgame.pojo.clean.face.*;
import com.wbgame.pojo.clean.qp.QpChannelSort;
import com.wbgame.pojo.clean.wifi.HaiwaiWifiConfig;
import com.wbgame.service.AdService;
import com.wbgame.service.clean.CleanSomeService;
import com.wbgame.service.clean.CleanYdService;
import com.wbgame.service.faceTool.FaceToolService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtils;
import com.wbgame.utils.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;

/**
 * <AUTHOR>
 * @date 2022年02月28日
 */

@CrossOrigin
@RestController
@RequestMapping("/clean")
public class ToolAppController {

    public static final int DAY_MILLS_TANS_NUM = 24 * 3600 * 1000;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private CleanYdService ydService;
    @Autowired
    private CleanSomeService someService;
    @Autowired
    private CleanYdMapper ydMapper;
    @Autowired
    CleanYdMapper cleanYdMapper;
    @Autowired
    AdService adService;
    @Autowired
    YdSlave2Mapper ydSlave2Mapper;

    @Autowired
    private FaceModelMapper faceModelMapper;

    @Autowired
    private FaceModelController faceModelController;

    @Resource
    private FaceToolService faceToolService;

    private final static Logger logger = LoggerFactory.getLogger(FaceModelController.class);

    /**
     * 分控
     *
     * @param request
     * @param record
     * @return
     */
    @RequestMapping(value = "safe/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String safeQuery(HttpServletRequest request, AppSafeConfig record) {
        // 条件
        // 当前页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 进行分页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<AppSafeConfig> list = ydService.selectAppSafe(record);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }


//	private boolean doesCHAExist(String cha) {
//
//		for (String s : CHA.split(" ")) {
//
//			if (s.equals(cha)) {
//
//				return true;
//			}
//		}
//		return false;
//	}
//
//	public static final String CHA = "opyy viyy hwyy xmyy txyy";
//	@GetMapping(value = "/toolRiskControlConfiguration")
//	public String toolRiskControlConfiguration(AppSafeConfig record) {
//
//		String appid = record.getAppid();
//		String cha = record.getCha();
//
//		if (StringUtils.isBlank(appid)) {
//
//			return ReturnJson.error(Constants.ParamError);
//		}
//
//		// 获取 (opyy+viyy+hwyy+xmyy+txyy）=无法识别-10001_viyy
//
//		if (!StringUtils.isBlank(cha) && doesCHAExist(cha)) {
//
//			record.setCreateUser("AutoGeneration");
//			List<AppSafeConfig> appSafeConfigList = ydMapper.selectAppSafeConfigByAppidAndCha();
//
//			if (ObjectUtils.isEmpty(appSafeConfigList)) {
//
//				return ReturnJson.toErrorJson("配置模板不存在");
//			}
//
//			// 是否有重复数据
//			if (!ydMapper.selectAppSafeConfigByAppidAndChaCondition(appid, cha).isEmpty()) {
//
//				return ReturnJson.toErrorJson("配置已存在");
//			}
//
//			AppSafeConfig config = appSafeConfigList.get(0);
//
//			config.setAppid(record.getAppid());
//			config.setCha(record.getCha());
//			config.setPrjid(null);
//
//			return ReturnJson.success(ydMapper.insertAppSafe(config));
//
//		}
//
//		return ReturnJson.error(Constants.ParamError);
//
//	}

    /**
     * 操作
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "safe/handle")
    public String safeHandle(HttpServletRequest request, AppSafeConfig record) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        record.setCreateUser(username);
        record.setModifyUser(username);
        // 条件校验
        try {
            List list = JSON.parseObject(record.getWifiKey(), List.class);
        } catch (Exception e) {
            return ReturnJson.error(Constants.ParamError);
        }
        if (BlankUtils.checkBlank(record.getAppid())) {
            return ReturnJson.error(Constants.ParamError);
        }

        if ("add".equals(request.getParameter("handle"))) {
            AppSafeConfig temp = new AppSafeConfig();
            temp.setAppid(record.getAppid());
            temp.setCha("'" + record.getCha() + "'");
            temp.setPrjid(record.getPrjid());
            List<AppSafeConfig> exist = ydService.selectAppSafe(temp);
            if (exist != null && exist.size() != 0) {
                return ReturnJson.success("该应用已存在");
            }
            ydService.insertAppSafe(record);
        } else if ("edit".equals(request.getParameter("handle"))) {
            ydService.updateAppSafe(record);
        } else if ("del".equals(request.getParameter("handle"))) {
            ydService.deleteAppSafe(record);
        }
        return ReturnJson.success();
    }

    /**
     * 海外配置
     *
     * @param request
     * @param record
     * @return
     */
    @RequestMapping(value = "haiwaiWifiConfig/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String haiwaiWifiConfigQuery(HttpServletRequest request, HaiwaiWifiConfig record) {
        // 条件
        // 当前页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 进行分页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<HaiwaiWifiConfig> list = ydMapper.selectHaiwaiWifiConfig(record);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }

    /**
     * 海外配置操作
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "haiwaiWifiConfig/handle")
    public String haiwaiWifiConfigHandle(HttpServletRequest request, HaiwaiWifiConfig record) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        record.setCreateUser(username);
        record.setModifyUser(username);
        // 条件校验
        if (BlankUtils.checkBlank(record.getAppid())) {
            return ReturnJson.error(Constants.ParamError);
        }

        if ("add".equals(request.getParameter("handle"))) {
            HaiwaiWifiConfig temp = new HaiwaiWifiConfig();
            temp.setAppid(record.getAppid());
            temp.setPrjid(record.getPrjid());
            temp.setCha("'" + record.getCha() + "'");
            List<HaiwaiWifiConfig> exist = ydMapper.selectHaiwaiWifiConfig(temp);
            if (exist != null && exist.size() != 0) {
                return ReturnJson.success("该应用+渠道 或项目id已存在");
            }
            ydMapper.insertHaiwaiWifiConfig(record);
        } else if ("edit".equals(request.getParameter("handle"))) {
            ydMapper.updateHaiwaiWifiConfig(record);
        } else if ("del".equals(request.getParameter("handle"))) {
            ydMapper.deleteHaiwaiWifiConfig(record);
        }
        return ReturnJson.success();
    }

    /**
     * 趣拍渠道顺序配置
     *
     * @param request
     * @param record
     * @return
     */
    @RequestMapping(value = "qpChannelSort/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String qpChannelSortgQuery(HttpServletRequest request, QpChannelSort record) {
        // 条件
        // 当前页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 进行分页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<QpChannelSort> list = ydMapper.selectQpChannelSort(record);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }

    /**
     * 趣拍渠道顺序操作
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "qpChannelSort/handle")
    public String qpChannelSort(HttpServletRequest request, QpChannelSort record) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        record.setCreateUser(username);
        record.setModifyUser(username);

        if ("add".equals(request.getParameter("handle"))) {
            List<QpChannelSort> exist = ydMapper.selectQpChannelSort(record);
            if (exist != null && exist.size() != 0) {
                return ReturnJson.success("该应用已存在");
            }
            ydMapper.insertQpChannelSort(record);
        } else if ("edit".equals(request.getParameter("handle"))) {
            ydMapper.updateQpChannelSort(record);
        } else if ("del".equals(request.getParameter("handle"))) {
            ydMapper.deleteQpChannelSort(record);
        }
        return ReturnJson.success();
    }

    @RequestMapping(value = "qpSearchTagId/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String qpSearchTagId(HttpServletRequest request) {
        String sql = " SELECT id ,appid ,tag_title FROM `qp_config_tag` where tag_title = '热门'; ";
        List<Map<String, Object>> list = cleanYdMapper.queryListMap(sql);
        JSONObject result = new JSONObject();
        result.put("list", list);
        return ReturnJson.success(result);
    }

    @RequestMapping(value = "qpSearchTempId/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String qpSearchTempId(HttpServletRequest request, String tagid) {
        String sql = " SELECT id,temp_title FROM `qp_tag_temp` where  tagid = " + tagid + " ORDER BY modifyTime DESC ";
        List<Map<String, Object>> list = cleanYdMapper.queryListMap(sql);
        JSONObject result = new JSONObject();
        result.put("list", list);
        return ReturnJson.success(result);
    }

    /**
     * 应用外跳内
     *
     * @param request
     * @param record
     * @return
     */
    @RequestMapping(value = "superLockLotte/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String superLockLotteQuery(HttpServletRequest request, SuperLockLotte record) {
        // 条件
        // 当前页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 进行分页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<SuperLockLotte> list = ydMapper.selectSuperLockLotte(record);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }

    /**
     * 应用外跳内操作
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "superLockLotte/handle", method = {RequestMethod.GET, RequestMethod.POST})
    public String superLockLotteHandle(HttpServletRequest request, SuperLockLotte record) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        record.setCreateUser(username);
        record.setModifyUser(username);

        if ("add".equals(request.getParameter("handle"))) {
            SuperLockLotte sll = new SuperLockLotte();
            sll.setLotteName(record.getLotteName());
            List<SuperLockLotte> exist = ydMapper.selectSuperLockLotte(sll);
            if (exist != null && exist.size() != 0) {
                return ReturnJson.success("该动画名称已存在");
            }
            ydMapper.insertSuperLockLotte(record);
        } else if ("edit".equals(request.getParameter("handle"))) {
            ydMapper.updateSuperLockLotte(record);
        } else if ("del".equals(request.getParameter("handle"))) {
            ydMapper.deleteSuperLockLotte(record);
        }
        return ReturnJson.success();
    }

    /**
     * 国家配置表
     */
    @RequestMapping(value = "globalCountry/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String globalCountry(HttpServletRequest request, String tagid) {
        String sql = " SELECT countryName,country,countryNo FROM `global_country_config` where countryNo != '' ORDER BY id; ";
        List<Map<String, Object>> list = cleanYdMapper.queryListMap(sql);
        JSONObject result = new JSONObject();
        result.put("list", list);
        return ReturnJson.success(result);
    }

    /**
     * face puls 地区配置
     *
     * @param request
     * @param record
     * @return
     */
    @RequestMapping(value = "faceAreaConfig/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String faceAreaConfigQuery(HttpServletRequest request, FaceAreaConfig record) {
        // 条件
        // 当前页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 进行分页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<FaceAreaConfig> list = ydMapper.selectFaceAreaConfig(record);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }

    /**
     * face puls 地区操作
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "faceAreaConfig/handle", method = {RequestMethod.GET, RequestMethod.POST})
    public String faceAreaConfigHandle(HttpServletRequest request, FaceAreaConfig record) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        record.setCreateUser(username);
        record.setModifyUser(username);

        if ("add".equals(request.getParameter("handle"))) {
            FaceAreaConfig sll = new FaceAreaConfig();
            sll.setAreaName(record.getAreaName());
            List<FaceAreaConfig> exist = ydMapper.selectFaceAreaConfig(sll);
            if (exist != null && exist.size() != 0) {
                return ReturnJson.success("该地区名称已存在");
            }
            ydMapper.insertFaceAreaConfig(record);
        } else if ("edit".equals(request.getParameter("handle"))) {
            ydMapper.updateFaceAreaConfig(record);
        } else if ("del".equals(request.getParameter("handle"))) {
            ydMapper.deleteFaceAreaConfig(record);
        }
        return ReturnJson.success();
    }

    /**
     * face puls 分类
     *
     * @param request
     * @param record
     * @return
     */
    @RequestMapping(value = "faceClassConfig/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String faceClassConfigQuery(HttpServletRequest request, FaceClassConfig record) {
        // 条件
        // 当前页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 进行分页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<FaceClassConfig> list = ydMapper.selectFaceClassConfig(record);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }

    /**
     * face puls 分类
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "faceClassConfig/handle", method = {RequestMethod.GET, RequestMethod.POST})
    public String faceClassConfigHandle(HttpServletRequest request, FaceClassConfig record, String sortList) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        record.setCreateUser(username);
        record.setModifyUser(username);

        if ("add".equals(request.getParameter("handle"))) {
            FaceClassConfig sll = new FaceClassConfig();
            sll.setClassName(record.getClassName());
            List<FaceClassConfig> exist = ydMapper.selectFaceClassConfig(sll);
            if (exist != null && exist.size() != 0) {
                return ReturnJson.success("该分类名称已存在");
            }
            ydMapper.insertFaceClassConfig(record);
        } else if ("edit".equals(request.getParameter("handle"))) {
            ydMapper.updateFaceClassConfig(record);
        } else if ("del".equals(request.getParameter("handle"))) {
            ydMapper.deleteFaceClassConfig(record);
        } else if ("modifiySort".equals(request.getParameter("handle"))) {
            if (null != sortList) {
                List<FaceClassConfig> inventoryDTOs = JSON.parseArray(sortList, FaceClassConfig.class);
                for (FaceClassConfig faceClassConfig : inventoryDTOs) {
                    ydMapper.updateFaceClassConfig(faceClassConfig);
                }
            }
        }
        return ReturnJson.success();
    }

    /**
     * face puls 模板
     *
     * @param request
     * @param record
     * @return
     */
    @RequestMapping(value = "faceModelConfig/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String faceModelConfig(HttpServletRequest request, FaceModelConfig record) {
        // 条件
        // 当前页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 进行分页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页

        // shenl 4.25 新增根据创建时间和新增付费数来进行筛选
        long now = System.currentTimeMillis();
        if (BlankUtils.isNotBlank(record.getUpTime())) {
            String upTime = record.getUpTime();
            if (!BlankUtils.isNumeric(upTime)) {
                return ReturnJson.toErrorJson("illegal up time");
            }
            long passedMills = Long.parseLong(upTime) * DAY_MILLS_TANS_NUM;
            long createTime = now - passedMills;
            record.setCreateTime(DateUtils.parseTimestampToDateStr(createTime));
        }

        List<FaceModelConfig> list = ydMapper.selectFaceModelConfig(record);

        // 将已创建天数回填到数据中
        for (FaceModelConfig faceModelConfig : list) {
            try {
                long createTime = DateUtils.parseDateStrToTimestamp(faceModelConfig.getCreateTime());
                long upTime = (now - createTime) / DAY_MILLS_TANS_NUM + 1;
                faceModelConfig.setUpTime(String.valueOf(upTime));
            } catch (ParseException e) {
                logger.error("unexpected error when parse time: {}, ", faceModelConfig.getCreateTime(), e);
//                return ReturnJson.toErrorJson("parse time error");
            }
        }

        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }

    /**
     * face puls 模板排序
     *
     * @param request
     * @param record
     * @return
     */
    @RequestMapping(value = "faceModelSortConfig/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectFaceModelSortConfig(HttpServletRequest request, FaceModelConfig record) {
        // 条件
        // 当前页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 进行分页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<FaceModelConfig> list = ydMapper.selectFaceModelSortConfig(record);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }

    /**
     * face puls 模板
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "faceModelConfig/handle", method = {RequestMethod.GET, RequestMethod.POST})
    public String faceClassConfigHandle(HttpServletRequest request, FaceModelConfig record, String sortList) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        record.setCreateUser(username);
        record.setModifyUser(username);

        if ("add".equals(request.getParameter("handle"))) {
            FaceModelConfig sll = new FaceModelConfig();
            sll.setTempTitle(record.getTempTitle());
            List<FaceModelConfig> exist = ydMapper.selectFaceModelConfig(sll);
            if (exist != null && exist.size() != 0) {
                return ReturnJson.toErrorJson("该模板名称已存在");
            }

            File file = null;
            String video_url = record.getTempUrl();
            // 23.4.17 add for webp format resolve
            if (StringUtils.isNotBlank(video_url) && video_url.toLowerCase().endsWith(".webp")) {
                try {
                    webpFileRead(record, video_url);
                } catch (FileNotFoundException e) {
                    String errorMsg = String.format("file %s does not exit", video_url);
                    return ReturnJson.toErrorJson(errorMsg);
                } catch (IOException e) {
                    String errorMsg = String.format("file error: %s", e.getMessage());
                    return ReturnJson.toErrorJson(errorMsg);
                }
            } else {
                try {
                    file = FileUtils.getFile(video_url);
                    it.sauronsoftware.jave.Encoder encoder = new it.sauronsoftware.jave.Encoder();
                    it.sauronsoftware.jave.MultimediaInfo m = encoder.getInfo(file);
                    record.setVideoWidth(m.getVideo().getSize().getWidth() + "");// 获取视频宽高
                    record.setVideoHeight(m.getVideo().getSize().getHeight() + "");// 获取视频长高
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    file.delete();
                }
            }

            ydMapper.insertFaceModelConfig(record);

            // 新增脸拍模块的模板列表
            //CopyFaceModelConfig copyFaceModelConfig = new CopyFaceModelConfig();
            //BeanUtils.copyProperties(record, copyFaceModelConfig);
            //faceModelMapper.insertFaceModelConfig(copyFaceModelConfig);

        } else if ("edit".equals(request.getParameter("handle"))) {

            Integer exist = ydMapper.selectTempleTitle(record.getTempTitle());
            if (exist != null && !exist.equals(Integer.valueOf(record.getId()))) {
                return ReturnJson.toErrorJson("该模板名称已存在");
            }
            File file = null;
            String video_url = record.getTempUrl();
            // 23.4.17 add for webp format resolve
            if (StringUtils.isNotBlank(video_url) && video_url.toLowerCase().endsWith(".webp")) {
                try {
                    webpFileRead(record, video_url);
                } catch (FileNotFoundException e) {
                    String errorMsg = String.format("file %s does not exit", video_url);
                    return ReturnJson.toErrorJson(errorMsg);
                } catch (IOException e) {
                    String errorMsg = String.format("file error: %s", e.getMessage());
                    return ReturnJson.toErrorJson(errorMsg);
                }
            } else if (StringUtils.isNotBlank(video_url)) {
                try {
                    file = FileUtils.getFile(video_url);
                    it.sauronsoftware.jave.Encoder encoder = new it.sauronsoftware.jave.Encoder();
                    it.sauronsoftware.jave.MultimediaInfo m = encoder.getInfo(file);
                    record.setVideoWidth(m.getVideo().getSize().getWidth() + "");// 获取视频宽高
                    record.setVideoHeight(m.getVideo().getSize().getHeight() + "");// 获取视频长高
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    file.delete();
                }
            }
            ydMapper.updateFaceModelConfig(record);

            // 修改脸拍模块的模板列表
            //CopyFaceModelConfig copyFaceModelConfig = new CopyFaceModelConfig();
            //BeanUtils.copyProperties(record, copyFaceModelConfig);
            //faceModelMapper.updateFaceModelConfig(copyFaceModelConfig);
        } else if ("top".equals(request.getParameter("handle"))) {
            ydMapper.updateFaceModelConfigTop(record);
        } else if ("del".equals(request.getParameter("handle"))) {
            ydMapper.deleteFaceModelConfig(record);
        }
        if ("modifiySort".equals(request.getParameter("handle"))) {
            if (null != sortList) {
                List<FaceModelConfig> inventoryDTOs = JSON.parseArray(sortList, FaceModelConfig.class);
                for (FaceModelConfig faceModelConfig : inventoryDTOs) {
                    faceModelConfig.setClassId(Integer.parseInt(request.getParameter("classId")));
                    ydMapper.updateFaceModelConfigSort(faceModelConfig);
                }
            }
        }
        return ReturnJson.success();
    }

    /**
     * shenl
     * 批量下架功能，两个平台同步
     *
     * @param midListStr ","  分割mid列表
     * @return 更新数量比较
     */
    @ControllerLoggingEnhancer
    @RequestMapping(value = "/faceModelConfig/batch/offshelve", method = {RequestMethod.GET, RequestMethod.POST})
    public String batchOffShelve(@ApiNeed({"token", "midListStr"})
                                 @RequestParam String token,
                                 @RequestParam String midListStr) {
        if (StringUtils.isBlank(midListStr)) {
            return ReturnJson.toErrorJson("empty id list");
        }

        String loginUserName = LOGIN_USER_NAME.get();
        if (BlankUtils.isBlank(loginUserName)) {
            throw new UnLoginException("please login");
        }

        List<String> midList = Lists.newArrayList(midListStr.split(","));

        return ReturnJson.success(
                faceToolService.batchOffShelveModel(
                        loginUserName,
                        midList));

    }

    /**
     * shenl
     * 下架功能，两个平台同步
     *
     * @return 更新数量比较
     */
    @ControllerLoggingEnhancer
    @RequestMapping(value = "/faceModelConfig/handle/offshelve", method = {RequestMethod.POST})
    public String faceClassConfigHandleOffShelve(@ApiNeed({"token", "mid"})
                                                 @RequestParam String token,
                                                 @RequestParam String mid) {
        String loginUserName = LOGIN_USER_NAME.get();
        if (BlankUtils.isBlank(loginUserName)) {
            throw new UnLoginException("please login");
        }
        return ReturnJson.success(
                faceToolService.toolOffShelve(
                        loginUserName,
                        mid)
        );
    }

//    /**
//     * shenl
//     * 编辑功能，两个平台同步
//     *
//     * @return 更新数量比较
//     */
//    @CommonControllerEnhancer
//    @RequestMapping(value = "/faceModelConfig/handle/edit", method = {RequestMethod.POST})
//    public String faceClassConfigHandleEdit(@ApiNeed({"token", "id"})
//                                            @RequestParam String token,
//                                            @RequestParam int id,
//                                            @RequestAttribute CurrUserVo loginUser) {
//
//        if (loginUser == null || StringUtils.isBlank(loginUser.getLogin_name())) {
//            return ReturnJson.toErrorJson("login user is null");
//        }
//
//        return ReturnJson.success(
//                faceToolService.toolOffShelve(
//                        loginUser.getLogin_name(),
//                        id));
//    }

    private static void webpFileReadByByte(FaceModelConfig record, String video_url) throws IOException {
        URL url = new URL(video_url);
        //通过url的openStream获取url对象所表示资源的字节输入流
        InputStream inputStream = url.openStream();

//        FileInputStream fileInputStream = new FileInputStream(video_url);
        byte[] bytes = new byte[30];

        inputStream.read(bytes, 0, bytes.length);

        // webp 图片 获取文件头的27 28 位计算得到宽 29 30得到高
        int width = ((int) bytes[27] & 0xff) << 8 | ((int) bytes[26] & 0xff);
        int height = ((int) bytes[29] & 0xff) << 8 | ((int) bytes[28] & 0xff);
        record.setVideoWidth(String.valueOf(width));
        record.setVideoHeight(String.valueOf(height));
    }

    private static void webpFileRead(FaceModelConfig record, String video_url) throws IOException {
        if (record.getVideoHeight() != null && record.getVideoWidth() != null) {
            return;
        }

        URL url = new URL(video_url);
        //通过url的openStream获取url对象所表示资源的字节输入流

        try {
            BufferedImage image = ImageIO.read(url);
            record.setVideoWidth(String.valueOf(image.getWidth()));
            record.setVideoHeight(String.valueOf(image.getHeight()));
        } catch (Exception e) {
            logger.error("webp image read error: ", e);
        }
    }

    @PostMapping("/sync2Qupai")
    public Result<String> syncKaiXinPai(@RequestBody List<CopyFaceModelConfig> list) {

        List<CopyFaceModelConfig> getList = faceModelMapper.selectByModelNameList(list);

        List<CopyFaceModelConfig> addList = list.stream().filter(model -> {

            for (CopyFaceModelConfig getModel : getList) {

                if (getModel.getTempTitle().equals(model.getTempTitle())) {

                    return false;
                }
            }

            return true;
        }).collect(Collectors.toList());

        if (!ObjectUtils.isEmpty(addList)) {

            faceModelMapper.batchInsertCopyFaceModel(addList);
        }

        return ResultUtils.success();
    }

    /**
     * face puls 模板分类查詢
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "faceModelClassConfig/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String faceModelClassConfig(HttpServletRequest request, ModelClassConfig record) {

        // 条件
        // 当前页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 进行分页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<ModelClassConfig> list = ydMapper.selectModelClassConfig(record);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }

    /**
     * face puls 模板分类移除
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "deleteModelClassConfigbyClassId", method = {RequestMethod.GET, RequestMethod.POST})
    public String deleteModelClassConfigbyClassId(HttpServletRequest request, ModelClassConfig record) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        ydMapper.deleteModelClassConfigbyClassId(record);

        return ReturnJson.success();
    }

    /**
     * face puls 模板分类配置
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "faceModelClassConfig/handle", method = {RequestMethod.GET, RequestMethod.POST})
    public String faceModelClassConfigHandle(HttpServletRequest request, String modelId, String modelClass) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        List<String> classList = ydMapper.queryListString("SELECT DISTINCT classId FROM `face_model_class_config` where modelId = " + modelId);

        List<String> newClassList = new ArrayList<>();

        List<ModelClassConfig> upList = new ArrayList<ModelClassConfig>();

        List<ModelClassConfig> modelClassConfigList = JSON.parseArray(modelClass, ModelClassConfig.class);

        for (ModelClassConfig modelClassConfig : modelClassConfigList) {
            if (classList != null) {
                newClassList.add(modelClassConfig.getClassId());
                if (classList.contains(modelClassConfig.getClassId())) {
                    continue;
                }
                upList.add(modelClassConfig);
            }

        }

        if (upList.size() > 0) {
            ydMapper.insertModelClassConfigList(upList);
        }

        ydMapper.execSql("delete from `face_model_class_config` where classId not in (" + String.join(",", newClassList) + ") and modelId = " + modelId);

        // 调用脸拍的模板列表分类地区接口
        //faceModelController.faceModelClassConfigHandle(request, modelId, modelClass);
        return ReturnJson.success();
    }

    /**
     * face puls 模板分类配置批量操作
     */
    @PostMapping("/batchFaceModelClassConfigHandle")
    public Result<Integer> batchFaceModelClassConfigHandle(HttpServletRequest request, @RequestBody List<FaceModelConfig> faceModelConfigList) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        // 删除模板id对应的分类数据
        ydMapper.deleteFaceModelConfigBatch(faceModelConfigList
                .stream()
                .map(FaceModelConfig::getId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList()));

        // 新增模板id对应的分类数据
        if (faceModelConfigList.get(0).getClassId() != null) {

            ydMapper.batchClassArea(faceModelConfigList);
        }
        return ResultUtils.success();

    }

    /**
     * face puls 商品配置
     *
     * @param request
     * @param record
     * @return
     */
    @RequestMapping(value = "faceProductConfig/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String faceProductConfig(HttpServletRequest request, FaceProductConfig record) {
        // 条件
        // 当前页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 进行分页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<FaceProductConfig> list = ydMapper.selectFaceProductConfig(record);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }

    /**
     * face puls 商品配置 操作
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "faceProductConfig/handle", method = {RequestMethod.GET, RequestMethod.POST})
    public String faceProductConfigHandle(HttpServletRequest request, FaceProductConfig record, String sortList) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        record.setCreateUser(username);
        record.setModifyUser(username);

        if ("add".equals(request.getParameter("handle"))) {
            FaceProductConfig sll = new FaceProductConfig();
            sll.setProductName(record.getProductName());
            List<FaceProductConfig> exist = ydMapper.selectFaceProductConfig(sll);
            if (exist != null && exist.size() != 0) {
                return ReturnJson.success("该商品名称已存在");
            }
            ydMapper.insertFaceProductConfig(record);
        } else if ("edit".equals(request.getParameter("handle"))) {

            // 处理天数为"" 的情况,不是数字字符串则不修改天数,空串则默认为"0"
            String feeDay = record.getFeeDay();
            if (feeDay != null) {

                record.setFeeDay(feeDay.matches("\\d+") ? feeDay :
                        ("".equals(feeDay) ? "0" : ""));
            }

            ydMapper.updateFaceProductConfig(record);
        } else if ("del".equals(request.getParameter("handle"))) {
            ydMapper.deleteFaceProductConfig(record);
        } else if ("modifiySort".equals(request.getParameter("handle"))) {
            if (null != sortList) {
                List<FaceProductConfig> inventoryDTOs = JSON.parseArray(sortList, FaceProductConfig.class);
                for (FaceProductConfig FaceProductConfig : inventoryDTOs) {
                    ydMapper.updateFaceProductConfig(FaceProductConfig);
                }
            }
        }
        return ReturnJson.success();
    }

    /**
     * face puls 商品配置
     *
     * @param request
     * @param record
     * @return
     */
    @RequestMapping(value = "faceSwitchConfig/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String faceSwitchConfig(HttpServletRequest request, FaceSwitchConfig record) {
        // 条件
        // 当前页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 进行分页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<FaceSwitchConfig> list = ydMapper.selectFaceSwitchConfig(record);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }

    /**
     * face puls 商品配置 操作
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "faceSwitchConfig/handle", method = {RequestMethod.GET, RequestMethod.POST})
    public String faceSwitchConfigHandle(HttpServletRequest request, FaceSwitchConfig record, String sortList) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        record.setCreateUser(username);
        record.setModifyUser(username);

        // if ("add".equals(request.getParameter("handle"))) {
        // FaceProductConfig sll = new FaceProductConfig();
        //
        // ydMapper.insertFaceSwitchConfig(record);
        // } else

        if ("edit".equals(request.getParameter("handle"))) {
            ydMapper.updateFaceSwitchConfig(record);
        } else if ("del".equals(request.getParameter("handle"))) {
            ydMapper.deleteFaceSwitchConfig(record);
        }
        return ReturnJson.success();
    }

    /**
     * 游戏配置
     *
     * @param request
     * @param record
     * @return
     */
    @RequestMapping(value = "superUseLevel/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String superUseLevelQuery(HttpServletRequest request, SuperUseLevel record) {
        // 条件
        // 当前页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 进行分页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<SuperUseLevel> list = ydMapper.selectSuperUseLevel(record);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }

    /**
     * 游戏分层操作
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "superUseLevel/handle")
    public String superUseLevelHandle(HttpServletRequest request, SuperUseLevel record) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        record.setCreateUser(username);
        record.setModifyUser(username);
        // 条件校验
        if (BlankUtils.checkBlank(record.getAppid())) {
            return ReturnJson.error(Constants.ParamError);
        }

        if ("add".equals(request.getParameter("handle"))) {
            SuperUseLevel temp = new SuperUseLevel();
            temp.setAppid(record.getAppid());
            temp.setPrjid(record.getPrjid());
            temp.setCha("'" + record.getCha() + "'");
            List<SuperUseLevel> exist = ydMapper.selectSuperUseLevel(temp);
            if (exist != null && exist.size() != 0) {
                return ReturnJson.success("该应用已存在");
            }
            ydMapper.insertSuperUseLevel(record);
        } else if ("edit".equals(request.getParameter("handle"))) {
            ydMapper.updateSuperUseLevel(record);
        } else if ("del".equals(request.getParameter("handle"))) {
            ydMapper.deleteSuperUseLevel(record);
        } else if ("batch".equals(request.getParameter("handle"))) {
            return ydService.batchSuperUseLevel(record);
        }
        return ReturnJson.success();
    }

    /**
     * 悬浮球配置
     *
     * @param request
     * @param record
     * @return
     */
    @RequestMapping(value = "superConfigFloat/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String superConfigFloatQuery(HttpServletRequest request, SuperConfigFloat record) {
        // 条件
        // 当前页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 进行分页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<SuperConfigFloat> list = ydMapper.selectSuperConfigFloat(record);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }

    /**
     * 悬浮球操作
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "superConfigFloat/handle")
    public String superUseLevelHandle(HttpServletRequest request, SuperConfigFloat record) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        record.setCreateUser(username);
        record.setModifyUser(username);
        // 条件校验
        if (BlankUtils.checkBlank(record.getAppid())) {
            return ReturnJson.error(Constants.ParamError);
        }

        if ("add".equals(request.getParameter("handle"))) {
            SuperConfigFloat temp = new SuperConfigFloat();
            temp.setAppid(record.getAppid());
            temp.setPrjid(record.getPrjid());
            temp.setCha("'" + record.getCha() + "'");
            List<SuperConfigFloat> exist = ydMapper.selectSuperConfigFloat(temp);
            if (exist != null && exist.size() != 0) {
                return ReturnJson.success("该应用+ cha 或 项目 已存在");
            }
            ydMapper.insertSuperConfigFloat(record);
        } else if ("edit".equals(request.getParameter("handle"))) {
            ydMapper.updateSuperConfigFloat(record);
        } else if ("del".equals(request.getParameter("handle"))) {
            ydMapper.deleteSuperConfigFloat(record);
        }
        return ReturnJson.success();
    }

    /**
     * 新版新闻配置
     *
     * @param request
     * @param record
     * @return
     */
    @RequestMapping(value = "selectSuperConfigNewV2/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String selectSuperConfigNewV2Query(HttpServletRequest request, SuperConfigNewV2 record) {
        // 条件
        // 当前页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 进行分页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<SuperConfigNewV2> list = ydMapper.selectSuperConfigNewV2(record);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("list", list);
        result.put("total", size);
        return ReturnJson.success(result);
    }

    /**
     * 新版新闻配置操作
     *
     * @param request
     * @param record  参数
     * @return
     */
    @RequestMapping(value = "selectSuperConfigNewV2/handle")
    public String superConfigNewV2Handle(HttpServletRequest request, SuperConfigNewV2 record) {

        String token = request.getParameter("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        record.setCreateUser(username);
        record.setModifyUser(username);
        // 条件校验
        if (BlankUtils.checkBlank(record.getAppid())) {
            return ReturnJson.error(Constants.ParamError);
        }

        if ("add".equals(request.getParameter("handle"))) {
            SuperConfigNewV2 temp = new SuperConfigNewV2();
            temp.setAppid(record.getAppid());
            List<SuperConfigNewV2> exist = ydMapper.selectSuperConfigNewV2(temp);
            if (exist != null && exist.size() != 0) {
                return ReturnJson.success("该应用 已配置");
            }
            ydMapper.insertSuperConfigNewV2(record);
        } else if ("edit".equals(request.getParameter("handle"))) {
            ydMapper.updateSuperConfigNewV2(record);
        } else if ("del".equals(request.getParameter("handle"))) {
            ydMapper.deleteSuperConfigNewV2(record);
        }
        return ReturnJson.success();
    }
}
