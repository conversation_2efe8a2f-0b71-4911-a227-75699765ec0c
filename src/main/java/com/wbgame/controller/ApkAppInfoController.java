package com.wbgame.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.*;
import com.wbgame.utils.DateUtils;
import org.apache.commons.codec.binary.Base64;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.service.AdService;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;
import com.wbgame.utils.JxlUtil;

import jxl.Sheet;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;


/**
 * apk 列表接口
 *
 * <AUTHOR>
 */
@Controller
public class ApkAppInfoController {

    @Autowired
    SomeService someService;

    @Autowired
    AdService adService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 查询app信息
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectAppInfo", method = {RequestMethod.POST})
    public @ResponseBody
    String selectAppInfo(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        if (BlankUtils.checkBlank(cu.getToken()))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(cu.getToken());
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue()
                    .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
        }

        List<ApkAppInfoVo> list = someService.selectAppInfo();
        List<Map<String, String>> mapList = new ArrayList<>();
        for (ApkAppInfoVo apkAppInfoVo : list) {
            Map<String, String> map = new HashMap<>();
            map.put("appid", apkAppInfoVo.getAppid());
            map.put("appName", apkAppInfoVo.getGameName());
            mapList.add(map);
        }
        JSONObject result = new JSONObject();
        result.put("data", mapList);
        result.put("ret", 1);
        return result.toJSONString();
    }

    /**
     * apk用户统计接口
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectAppUserInfo", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String selectAppUserInfo(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        if (BlankUtils.checkBlank(cu.getToken()))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(cu.getToken());
        if (cuser == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        String appid = null;
        if (!"root".equals(CommonUtil.userMap.get(cuser.getLogin_name()).getOrg_id())) {
            appid = CommonUtil.userMap.get(cuser.getLogin_name()).getApp_group_b();
        }

        String start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        String end_date = DateTime.now().toString("yyyy-MM-dd");
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("start_date", start_date);
        paramMap.put("end_date", end_date);
        paramMap.put("appid", appid);

        List<ApkUserTotalVo> totallist = someService.selectApkUserTotalThree(paramMap);

        Map<String, Integer> yMap = new HashMap<>();
        Map<String, Integer> nowMap = new HashMap<>();
        yMap.put("new", totallist.get(0).getCurrNew());
        yMap.put("dau", totallist.get(0).getCurrDau());
        yMap.put("start", totallist.get(0).getCurrStart());
        yMap.put("users", totallist.get(0).getTodayUser());
        nowMap.put("new", totallist.get(1).getCurrNew());
        nowMap.put("dau", totallist.get(1).getCurrDau());
        nowMap.put("start", totallist.get(1).getCurrStart());
        nowMap.put("users", totallist.get(1).getTodayUser());

        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<ApkUserTotalVo> list = someService.selectApkUserTotalTwo(paramMap);
        long size = ((Page) list).getTotal();

        JSONObject result = new JSONObject();
        result.put("data", list);
        result.put("totalYesterday", yMap);
        result.put("totalToday", nowMap);
        result.put("ret", 1);
        result.put("totalCount", size);
        return result.toJSONString();
    }

    /**
     * 暖暖家园用户留存数据接口
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectNnjyAppUserKeep", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String selectNnjyAppUserKeep(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            String start = BlankUtils.checkNull(request, "start");
            String limit = BlankUtils.checkNull(request, "limit");

            String token = request.getParameter("token");

            // pageStart 和 limit设置值
            int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            int pageNo = (pageStart / pageSize) + 1;

            String beginDt = BlankUtils.checkNull(request, "beginDt");
            String startTime = beginDt;
            beginDt = beginDt.replace("-", "");

            String endDt = BlankUtils.checkNull(request, "endDt");
            String endTime = endDt;
            endDt = endDt.replace("-", "");

            String appName = BlankUtils.checkNull(request, "appName");
            String pid = BlankUtils.checkNull(request, "pid");
            String appid = BlankUtils.checkNull(request, "appid");
            String group_pid = BlankUtils.checkNull(request, "group_pid");
            String type = BlankUtils.checkNull(request, "type");

            String resp = "";
            if (!BlankUtils.checkBlank(type)) {
                for (int i = 1; i <= 30; i++) {
                    resp += "CONCAT(TRUNCATE(avg(keep" + i + ")*100, 0),'%') keep" + i + ",";

                    String kk = "";
                    for (int p = 1; p <= i; p++) {
                        kk += "+avg(keep" + p + ")";
                    }

                    resp += "TRUNCATE(1" + kk + ", 2) keepnum" + i + ",";
                }
            } else {
                int[] mm = {1, 2, 3, 4, 5, 6, 7, 14, 30};
                for (int i = 0; i < mm.length; i++) {
                    resp += "CONCAT(TRUNCATE(avg(keep" + mm[i] + ")*100, 0),'%') keep" + mm[i] + ",";

                    String kk = "";
                    for (int p = 1; p <= mm[i]; p++) {
                        kk += "+avg(keep" + p + ")";
                    }

                    resp += "TRUNCATE(1" + kk + ", 2) keepnum" + mm[i] + ",";
                }
            }


            String sql = "";
            String where = "";
            if (!BlankUtils.checkBlank(appid))
                where += " and product_id = " + appid;
            if (!BlankUtils.checkBlank(pid)) {
                where += " and projectid = " + pid;
            }
            String[] nnjyArray = {"37696", "37740", "37747"};

            //按产品查询需加权平均 渠道无需加权平均 暖暖家园项目需求
            if (BlankUtils.checkBlank(pid) && Arrays.asList(nnjyArray).contains(appid)) {
                sql = retentionSql(startTime, endTime, appid);
            } else {
                sql = "select mmdate dt,product_id appid,projectid pid," + resp + "SUM(usernum) usernum" +
                        " from product_keep_num_total" +
                        " where mmdate BETWEEN '" + beginDt + "' AND '" + endDt + "' " + where +
                        " group by mmdate,product_id" + ("ok".equals(group_pid) ? ",projectid" : "") +
                        " order by mmdate desc,usernum desc";
            }


            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<Map<String, Object>> list = adService.queryListMap(sql);
            list.forEach(map -> {
                JSONArray array = new JSONArray();
                if (!BlankUtils.checkBlank(type)) {
                    for (int i = 1; i <= 30; i++) {
                        JSONObject obj = new JSONObject();
                        obj.put("a", map.get("keep" + i));
                        obj.put("b", map.get("keepnum" + i));
                        obj.put("title", i + "日留存");
                        obj.put("key", "keep" + i);
                        array.add(obj);
                    }
                } else {
                    int[] mm = {1, 2, 3, 4, 5, 6, 7, 14, 30};
                    for (int p = 0; p < mm.length; p++) {
                        int i = mm[p];
                        JSONObject obj = new JSONObject();
                        obj.put("a", map.get("keep" + i));
                        obj.put("b", map.get("keepnum" + i));
                        obj.put("title", i + "日留存");
                        obj.put("key", "keep" + i);
                        array.add(obj);
                    }
                }
                map.put("keep", array);
            });
            long size = ((Page) list).getTotal();
            // 导出的excel数据存储
            redisTemplate.opsForValue().set("appNnjyUserKeepExcelList-" + token,
                    adService.queryListMap(sql), 20 * 60, TimeUnit.SECONDS);


            JSONObject result = new JSONObject();
            result.put("data", list);
            result.put("ret", 1);
            result.put("totalCount", size);
            return result.toJSONString();

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 用户数据详情接口
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectAppUserDetail", method = {RequestMethod.POST})
    public @ResponseBody
    String selectAppUserDetail(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            String appid = BlankUtils.checkNull(request, "appid");
            JSONObject result = new JSONObject();
            if (BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            if (BlankUtils.checkBlank(appid))
                return "{\"ret\":2,\"msg\":\"param is error!\"}";
            CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(cu.getToken());
            if (cuser == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }

            Map<String, Object> map = new HashMap<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            map.put("appid", appid);
            String time1 = sdf.format(new Date());
            map.put("time", time1);
            List<ApkUserTotalVo> list = someService.selectApkUserDetail(map);
            Map<String, Object> ymap = new HashMap<>();
            ymap.put("appid", appid);
            ymap.put("time", getPreDateNew(time1, 1));
            List<ApkUserTotalVo> list1 = someService.selectApkUserDetail(ymap);

            Map<String, Object> todayNewMap = new HashMap<>();
            Map<String, Object> yesterdayNewMap = new HashMap<>();
            Map<String, Object> yesterdayDauMap = new HashMap<>();
            Map<String, Object> todayDauMap = new HashMap<>();
            Map<String, Object> todayStartMap = new HashMap<>();
            Map<String, Object> yesterdayStartMap = new HashMap<>();
            int news = 0;
            int daus = 0;
            int starts = 0;
            for (ApkUserTotalVo apkUserTotalVo : list) {
                todayNewMap.put(apkUserTotalVo.getCreatetime().
                                substring(apkUserTotalVo.getCreatetime().length() - 2, apkUserTotalVo.getCreatetime().length()),
                        apkUserTotalVo.getCurrNew());
                todayDauMap.put(apkUserTotalVo.getCreatetime().
                                substring(apkUserTotalVo.getCreatetime().length() - 2, apkUserTotalVo.getCreatetime().length()),
                        apkUserTotalVo.getCurrDau());
                todayStartMap.put(apkUserTotalVo.getCreatetime().
                                substring(apkUserTotalVo.getCreatetime().length() - 2, apkUserTotalVo.getCreatetime().length()),
                        apkUserTotalVo.getCurrStart());
                news += apkUserTotalVo.getCurrNew();
                daus += apkUserTotalVo.getCurrDau();
                starts += apkUserTotalVo.getCurrStart();
            }

            for (ApkUserTotalVo apkUserTotalVo : list1) {
                yesterdayNewMap.put(apkUserTotalVo.getCreatetime().
                                substring(apkUserTotalVo.getCreatetime().length() - 2, apkUserTotalVo.getCreatetime().length()),
                        apkUserTotalVo.getCurrNew());
                yesterdayDauMap.put(apkUserTotalVo.getCreatetime().
                                substring(apkUserTotalVo.getCreatetime().length() - 2, apkUserTotalVo.getCreatetime().length()),
                        apkUserTotalVo.getCurrDau());
                yesterdayStartMap.put(apkUserTotalVo.getCreatetime().
                                substring(apkUserTotalVo.getCreatetime().length() - 2, apkUserTotalVo.getCreatetime().length()),
                        apkUserTotalVo.getCurrStart());
            }

            Map<String, Object> tyNewMap = new HashMap<>();
            tyNewMap.put("today", todayNewMap);
            tyNewMap.put("yesterday", yesterdayNewMap);
            Map<String, Object> tyDauMap = new HashMap<>();
            tyDauMap.put("today", todayDauMap);
            tyDauMap.put("yesterday", yesterdayDauMap);
            Map<String, Object> tyStartMap = new HashMap<>();
            tyStartMap.put("today", todayStartMap);
            tyStartMap.put("yesterday", yesterdayStartMap);
            Map<String, Object> newMap = new HashMap<>();
            newMap.put("new", tyNewMap);
            newMap.put("dau", tyDauMap);
            newMap.put("start", tyStartMap);
            Map<String, Object> totalMap = new HashMap<>();
            totalMap.put("news", news);
            totalMap.put("daus", daus);
            totalMap.put("starts", starts);
            newMap.put("total", totalMap);
            result.put("data", newMap);
            result.put("ret", 1);
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 用户数据整体趋势
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectAppUserTrend", method = {RequestMethod.POST})
    public @ResponseBody
    String selectAppUserTrend(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject result = new JSONObject();
            String appid = BlankUtils.checkNull(request, "appid");
            String beginDt = BlankUtils.checkNull(request, "beginDt");
            if (BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            if (BlankUtils.checkBlank(appid))
                return "{\"ret\":2,\"msg\":\"param is error!\"}";
            if (BlankUtils.checkBlank(beginDt))
                return "{\"ret\":2,\"msg\":\"param is error!\"}";
            CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(cu.getToken());
            if (cuser == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }

            Map<String, Object> param = new HashMap<>();
            param.put("appid", appid);
            param.put("createtime", beginDt);
            ApkUserTrendVo apkUserTrendVo = someService.selectApkUserTrend(param);
            Map<String, Object> map = new HashMap<>();
            map.put("totalUser", apkUserTrendVo.getTodayUser());
            map.put("sevenDau", apkUserTrendVo.getSevenDau());
            map.put("thirtyDau", apkUserTrendVo.getThirthDau());
            map.put("sevenTimes", apkUserTrendVo.getSevenTimes());

            result.put("data", map);
            result.put("ret", 1);
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 整体趋势折线图
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectAppUserTrendLine", method = {RequestMethod.POST})
    public @ResponseBody
    String selectAppUserTrendLine(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject result = new JSONObject();
            DecimalFormat df = new DecimalFormat("0.00");
            String appid = BlankUtils.checkNull(request, "appid");
            String beginDt = BlankUtils.checkNull(request, "beginDt");
            String endDt = BlankUtils.checkNull(request, "endDt");

            if (BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            if (BlankUtils.checkBlank(appid) || BlankUtils.checkBlank(beginDt) || BlankUtils.checkBlank(endDt))
                return "{\"ret\":2,\"msg\":\"param is error!\"}";
            CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(cu.getToken());
            if (cuser == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }

            Map<String, Object> param = new HashMap<>();
            param.put("appid", appid);
            param.put("beginDt", beginDt);
            param.put("endDt", endDt);
            List<ApkUserTotalVo> list = someService.selectApkUserTrendLine(param);
            Map<String, Object> newMap = new HashMap<>();
            Map<String, Object> dauMap = new HashMap<>();
            Map<String, Object> startMap = new HashMap<>();
            Map<String, Object> totalMap = new HashMap<>();
            Map<String, Object> timeUserMap = new HashMap<>();
            Map<String, Object> dayUserMap = new HashMap<>();
            Map<String, Object> dayStartMap = new HashMap<>();
            Map<String, Object> dateMap = new HashMap<>();
            for (ApkUserTotalVo apkUserTotalVo : list) {
                Map<String, Object> map = new HashMap<>();
                newMap.put(apkUserTotalVo.getCreatetime(), apkUserTotalVo.getCurrNew());
                dauMap.put(apkUserTotalVo.getCreatetime(), apkUserTotalVo.getCurrDau());
                startMap.put(apkUserTotalVo.getCreatetime(), apkUserTotalVo.getCurrStart());
                totalMap.put(apkUserTotalVo.getCreatetime(), apkUserTotalVo.getTodayUser());
                timeUserMap.put(apkUserTotalVo.getCreatetime(), apkUserTotalVo.getUtimes());
                dayUserMap.put(apkUserTotalVo.getCreatetime(), apkUserTotalVo.getSumtimes());

                if (apkUserTotalVo.getCurrDau() != 0) {
                    dayStartMap.put(apkUserTotalVo.getCreatetime(),
                            df.format((float) apkUserTotalVo.getCurrStart() / apkUserTotalVo.getCurrDau()));
                } else {
                    dayStartMap.put(apkUserTotalVo.getCreatetime(), 0);
                }

                map.put("new", apkUserTotalVo.getCurrNew());
                map.put("old", apkUserTotalVo.getCurrDau() - apkUserTotalVo.getCurrNew());
                map.put("dau", apkUserTotalVo.getCurrDau());

                if (apkUserTotalVo.getTodayUser() != 0) {
                    map.put("newPer", df.format((float) apkUserTotalVo.getCurrNew() / apkUserTotalVo.getCurrDau()));
                } else {
                    map.put("newPer", 0);
                }
                dateMap.put(apkUserTotalVo.getCreatetime(), map);
            }
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("new", newMap);
            resultMap.put("dau", dauMap);
            resultMap.put("start", startMap);
            resultMap.put("total", totalMap);
            resultMap.put("timeUse", timeUserMap);
            resultMap.put("dayUse", dayUserMap);
            resultMap.put("dayStart", dayStartMap);
            resultMap.put("dauGroup", dateMap);

            result.put("data", resultMap);
            result.put("ret", 1);
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 登陆天数图表
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectLoginTable", method = {RequestMethod.POST})
    public @ResponseBody
    String selectLoginTable(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject result = new JSONObject();
            String appid = BlankUtils.checkNull(request, "appid");
            String dt = BlankUtils.checkNull(request, "dt");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMM");
            String table = sdf1.format(sdf.parse(dt));
            if (BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(cu.getToken());
            if (cuser == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            Map<Object, Object> map = new HashMap<>();
            map.put("appid", appid);
            map.put("table", table);
            List<LoginTotalVo> list = someService.selectLoginTable(map);
            result.put("data", list);
            result.put("ret", 1);
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 登陆统计详情
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectLoginDetail", method = {RequestMethod.POST})
    public @ResponseBody
    String selectLoginDetail(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject result = new JSONObject();
            String appid = BlankUtils.checkNull(request, "appid");
            String dt = BlankUtils.checkNull(request, "dt");
            String loginday = BlankUtils.checkNull(request, "loginday");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMM");
            //分页
            String start = BlankUtils.checkNull(request, "start");
            String limit = BlankUtils.checkNull(request, "limit");
            String table = sdf1.format(sdf.parse(dt));
            if (BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(cu.getToken());
            if (cuser == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            Map<Object, Object> map = new HashMap<>();
            map.put("appid", appid);
            map.put("table", table);
            map.put("loginday", Integer.valueOf(loginday));
            int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // 当前页
            int pageNo = (pageStart / pageSize) + 1;
            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<LoginTotalVo> list = someService.selectLoginDetail(map);
            long size = ((Page) list).getTotal();
            result.put("data", list);
            result.put("ret", 1);
            result.put("totalCount", size);
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 导出详情
     *
     * @param request
     * @param response
     */
    @CrossOrigin
    @RequestMapping(value = "/selectLoginDetail/export", method = RequestMethod.POST)
    public @ResponseBody
    void exportLoginDetail(HttpServletRequest request, HttpServletResponse response) {

        String fileName = DateTime.now().toString("yyyyMMddHHmmssSS") + ".xls";
        File file = new File(request.getRealPath("/"), fileName);
        try {
            String appid = BlankUtils.checkNull(request, "appid");
            String dt = BlankUtils.checkNull(request, "dt");
            String loginday = BlankUtils.checkNull(request, "loginday");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMM");
            String table = sdf1.format(sdf.parse(dt));

            Map<Object, Object> map = new HashMap<>();
            map.put("appid", appid);
            map.put("table", table);
            map.put("loginday", Integer.valueOf(loginday));

            List<LoginTotalVo> list = someService.selectLoginDetail(map);

            WritableWorkbook wwb = Workbook.createWorkbook(file);
            //创建Excel工作表               创建Excel表的左下角表名(名称，第几个)
            WritableSheet sheet = wwb.createSheet("登陆用户详情表", 0);//创建sheet
            //ws.mergeCells(0, 0, 2, 1);//合并单元格(左列，左行，右列，右行)从第1行第1列到第2行第3列

            String[] title = {"appid", "imei", "projectid", "lsn", "登陆天数"};

            for (int i = 0; i < title.length; i++) {//添加标题
                Label l = new Label(i, 0, title[i], JxlUtil.getTitle());
                sheet.setColumnView(i, 20);
                sheet.addCell(l);
            }

            for (int k = 0; k < list.size(); k++) { // 每一行的内容
                String[] vals = {
                        list.get(k).getAppid(),
                        list.get(k).getImei(),
                        list.get(k).getProjectid(),
                        list.get(k).getLsn(),
                        String.valueOf(list.get(k).getLoginday()),
                };

                for (int m = 0; m < title.length; m++) { // 每一列的内容，从第二行开始插入
                    Label l = new Label(m, k + 1, vals[m], JxlUtil.getNormolCell());
                    sheet.addCell(l);
                }
            }
	     /*ws.setColumnView(0, 20);//设置列宽
	       ws.setRowView(0, 400);//设置行高 */
            wwb.write();
            wwb.close();

        } catch (Exception e) {
            e.printStackTrace();
            fileName = "无数据.xls";
        }

        response.addHeader("Cache-Control", "no-cache");
        response.addDateHeader("Expries", 0);
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
        OutputStream pw = null;
        FileInputStream input = null;
        try {
            pw = response.getOutputStream();
            input = new FileInputStream(file);
            int length = 0;
            byte buffer[] = new byte[2048];
            while ((length = input.read(buffer)) != -1) {
                pw.write(buffer, 0, length);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (pw != null){
                    pw.close();
                }
                if (input != null){
                    input.close();
                }
                file.delete();
            }catch (IOException ei){
                ei.printStackTrace();
            }

        }
    }

    /**
     * 整体趋势折线图加饼状图
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectAppUserTrendLineCake", method = {RequestMethod.POST})
    public @ResponseBody
    String selectAppUserTrendLineCake(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject result = new JSONObject();
            DecimalFormat df = new DecimalFormat("0.00");
            String appid = BlankUtils.checkNull(request, "appid");
            String beginDt = BlankUtils.checkNull(request, "beginDt");
            String endDt = BlankUtils.checkNull(request, "endDt");

            if (BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            if (BlankUtils.checkBlank(appid) || BlankUtils.checkBlank(beginDt) || BlankUtils.checkBlank(endDt))
                return "{\"ret\":2,\"msg\":\"param is error!\"}";
            CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(cu.getToken());
            if (cuser == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }

            beginDt = getPreDateNew(beginDt, 30);
            Map<String, Object> param = new HashMap<>();
            param.put("appid", appid);
            param.put("beginDt", beginDt);
            param.put("endDt", endDt);
            Map<String, Object> date1Map = new HashMap<>();
            Map<String, Object> date7Map = new HashMap<>();
            Map<String, Object> date30Map = new HashMap<>();
            Map<String, Object> typeMap = new HashMap<>();
            List<ApkUserTotalVo> list = someService.selectApkUserNewTrend(param);
            if (list != null) {
                for (ApkUserTotalVo apkUserTotalVo : list) {
                    Map<String, Object> map = new HashMap<>();
                    Map<String, Object> sevenmap = new HashMap<>();
                    Map<String, Object> thmap = new HashMap<>();
                    map.put("new", apkUserTotalVo.getCurrNew());
                    if (apkUserTotalVo.getYesterdayNew() != 0) {
                        map.put("rate", df.format((float) apkUserTotalVo.getKeep1() / apkUserTotalVo.getYesterdayNew()));
                    } else {
                        map.put("rate", 0.00);
                    }
                    if (apkUserTotalVo.getCurrNew() != 0) {
                        sevenmap.put("rate", df.format((float) apkUserTotalVo.getKeep7() / apkUserTotalVo.getCurrNew()));
                        thmap.put("rate", df.format((float) apkUserTotalVo.getKeep30() / apkUserTotalVo.getCurrNew()));
                    } else {
                        sevenmap.put("rate", 0.00);
                        thmap.put("rate", 0.00);
                    }
                    date1Map.put(apkUserTotalVo.getCreatetime(), map);
                    sevenmap.put("new", apkUserTotalVo.getCurrNew());
                    date7Map.put(apkUserTotalVo.getCreatetime(), sevenmap);
                    thmap.put("new", apkUserTotalVo.getCurrNew());
                    date30Map.put(apkUserTotalVo.getCreatetime(), thmap);
                }
            }
            typeMap.put("two", date1Map);
            typeMap.put("seven", date7Map);
            typeMap.put("thirty", date30Map);
            result.put("data", typeMap);
            result.put("ret", 1);
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 获取当前时间的前30天时间
     *
     * @return
     */
    public String getPreDateNew(String date, int num) {
        Calendar c = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            c.setTime(sdf.parse(date));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        int day1 = c.get(Calendar.DATE);
        c.set(Calendar.DATE, day1 - num);
        return sdf.format(c.getTime());
    }


    /**
     * 用户整体标签页饼状图
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/selectAppUserTrendCake", method = {RequestMethod.POST})
    public @ResponseBody
    String selectAppUserTrendCake(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject result = new JSONObject();
            String appid = BlankUtils.checkNull(request, "appid");
            String beginDt = BlankUtils.checkNull(request, "beginDt");

            if (BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            if (BlankUtils.checkBlank(appid) || BlankUtils.checkBlank(beginDt))
                return "{\"ret\":2,\"msg\":\"param is error!\"}";
            CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(cu.getToken());
            if (cuser == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            Map<String, Object> param = new HashMap<>();
            param.put("appid", appid);
            param.put("beginDt", beginDt);
            List<ApkUserVerVo> verList = someService.selectApkUserVer(param);
            Map<String, Object> newmap = new HashMap<>();
            Map<String, Object> daumap = new HashMap<>();
            Map<String, Object> totalmap = new HashMap<>();
            Map<String, Object> newchannelmap = new HashMap<>();
            Map<String, Object> dauchannelmap = new HashMap<>();
            Map<String, Object> totalchannelmap = new HashMap<>();
            List<ApkUserVerVo> channelList = someService.selectApkUserChannel(param);
            Map<String, Object> totalparam = new HashMap<>();
            totalparam.put("appid", appid);
            List<ApkUserVerVo> totalverList = someService.selectApkTotalVer(totalparam);
            List<ApkUserVerVo> totalchannelList = someService.selectApkTotalChannel(totalparam);
            if (verList != null) {
                for (ApkUserVerVo apkUserVerVo : verList) {
                    newmap.put(apkUserVerVo.getVer(), apkUserVerVo.getAddNum());
                    daumap.put(apkUserVerVo.getVer(), apkUserVerVo.getActNum());
                }
            }
            if (totalverList != null) {
                for (ApkUserVerVo apkUserVerVo : totalverList) {
                    totalmap.put(apkUserVerVo.getVer(), apkUserVerVo.getTodayUser());
                }
            }
            if (totalchannelList != null) {
                for (ApkUserVerVo apkUserVerVo : totalchannelList) {
                    totalchannelmap.put(apkUserVerVo.getChannel(), apkUserVerVo.getTodayUser());
                }
            }
            if (channelList != null) {
                for (ApkUserVerVo apkUserVerVo : channelList) {
                    newchannelmap.put(apkUserVerVo.getChannel(), apkUserVerVo.getAddNum());
                    dauchannelmap.put(apkUserVerVo.getChannel(), apkUserVerVo.getActNum());
                }
            }
            Map<String, Object> typeMap = new HashMap<>();
            Map<String, Object> channeltypeMap = new HashMap<>();
            Map<String, Object> resultMap = new HashMap<>();
            typeMap.put("new", newmap);
            typeMap.put("dau", daumap);
            typeMap.put("total", totalmap);
            channeltypeMap.put("new", newchannelmap);
            channeltypeMap.put("dau", dauchannelmap);
            channeltypeMap.put("total", totalchannelmap);
            resultMap.put("version", typeMap);
            resultMap.put("channel", channeltypeMap);

            result.put("data", resultMap);
            result.put("ret", 1);
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 导入
     *
     * @param file
     * @param request
     * @param response
     * @throws IOException
     */
    @CrossOrigin
    @RequestMapping(value = "/importChannelFee", method = {RequestMethod.POST})
    public void importChannelFee(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("text/html;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            PrintWriter out = response.getWriter();
            JSONObject obj = new JSONObject();

            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<AdvFeeVo> list = new ArrayList<>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int column = sheet.getColumns();
                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;

                    AdvFeeVo ki = new AdvFeeVo();
                    String[] vals = new String[column];
                    for (int c = 0; c < column; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }
                    ki.setPush_cha(vals[0]);
                    ki.setPri_id(vals[1]);
                    ki.setMmid(vals[2]);
                    if (vals[3] != null && !vals[3].equals("")) {
                        // 上传过来的日期格式
                        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy/MM/dd");
                        boolean flag = false;
                        try {
                            if (vals[3].split("/").length != 3
                                    || vals[3].split("/")[0].length() != 4) {
                                flag = true;
                            }

                            String adv_dt = DateTime.parse(vals[3], format).toString("yyyy-MM-dd");
                            ki.setAdv_dt(adv_dt);
                        } catch (Exception e) {
                            flag = true;
                        }

                        if (flag) {
                            obj.put("ret", 0);
                            obj.put("msg", "日期格式不正确，需要 yyyy/MM/dd 的格式！");
                            out.write(obj.toString());
                            out.close();
                            return;
                        }

                    }
                    //ki.setAdv_dt(vals[3]);
                    if (vals[4] == null || vals[4].equals(" ") || vals[4].length() == 0) {
                        ki.setGive_fee("0");
                    } else {
                        ki.setGive_fee(vals[4]);
                    }
                    //System.out.println(ki.getGive_fee());
                    ki.setNew_count(vals[5]);
                    //ki.setCpa(vals[6]);
                    ki.setTwo_retention(vals[7]);
                    ki.setDau(vals[8]);
                    if (vals[9] == null || vals[9].equals("") || vals[9].length() == 0) {
                        ki.setGet_fee("0");
                    } else {
                        ki.setGet_fee(vals[9]);
                    }
                    if (vals[10] == null || vals[10].equals("") || vals[10].length() == 0) {
                        ki.setGet_fee_expect("0");
                    } else {
                        ki.setGet_fee_expect(vals[10]);
                    }
                    //ki.setArpu(vals[11]);
                    ki.setAdv_fee(vals[12]);
                    //ki.setAdv_dau(vals[13]);
                    ki.setShow_count(vals[14]);
                    //ki.setShow_dau(vals[15]);
                    if (vals[16] == null || vals[16].equals("") || vals[16].length() == 0) {
                        ki.setClick_count("0");
                    } else {
                        ki.setClick_count(vals[16]);
                    }
                    //ki.setClick_rate(vals[17]);
                    //ki.setEcpm(vals[18]);
                    //ki.setCpc(vals[19]);
                    //ki.setAdv_total(vals[20]);
                    //ki.setGet_fee_total(vals[21]);
                    //ki.setTotal_fee(vals[22]);
                    //ki.setGive_fee_total(vals[23]);
                    //ki.setInput_output_ratio(vals[24]);
                    ki.setAdv_type(vals[17]);
                    ki.setOs_type(vals[18]);
                    ki.setCha_type(vals[19]);
                    ki.setAdv_fee_type(vals[20]);
                    list.add(ki);
                }

                someService.insertChannelFee(list);
                obj.put("ret", 1);
                obj.put("msg", "上传文件成功");
                out.write(obj.toString());
                out.close();
            } else {

                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls格式文件");
                out.write(obj.toString());
                out.close();
            }

        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            response.setCharacterEncoding("utf-8");
            PrintWriter pw;
            try {
                pw = response.getWriter();
                JSONObject obj = new JSONObject();
                //返回失败信息
                obj.put("ret", 0);
                obj.put("msg", e.getMessage());
                pw.write(obj.toString());
                pw.close();
            } catch (IOException e1) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 导入 TODO 广告数据导入  移动使用
     *
     * @param file
     * @param request
     * @param response
     * @throws IOException
     */
    @CrossOrigin
    @RequestMapping(value = "/importChannelFeeAdv", method = {RequestMethod.POST})
    public void importChannelFeeAdv(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("text/html;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            PrintWriter out = response.getWriter();
            JSONObject obj = new JSONObject();

            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<AdvChaFeeVo> list = new ArrayList<>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int column = sheet.getColumns();
                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;

                    AdvChaFeeVo ki = new AdvChaFeeVo();
                    String[] vals = new String[column];
                    for (int c = 0; c < column; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }
                    ki.setCha_name(vals[1]);
                    ki.setCha_game(vals[2]);
                    //兼容数据
                    if (vals[3] == null || vals[3].equals("")) {
                        obj.put("ret", 0);
                        obj.put("msg", "上传文件中广告类型不可为空");
                        out.write(obj.toString());
                        out.close();
                        return;
                    }
                    ki.setAd_pos(vals[3]);
                    ki.setAdv_fee(vals[4]);
                    ki.setShow_count(vals[5]);
                    ki.setClick_count(vals[6]);
                    if (vals[0] != null && !vals[0].equals("")) {
                        // 上传过来的日期格式
                        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy/MM/dd");
                        boolean flag = false;
                        try {
                            if (vals[0].split("/").length != 3
                                    || vals[0].split("/")[0].length() != 4) {
                                flag = true;
                            }

                            String adv_dt = DateTime.parse(vals[0], format).toString("yyyy-MM-dd");
                            ki.setAdv_dt(adv_dt);
                        } catch (Exception e) {
                            flag = true;
                        }

                        if (flag) {
                            obj.put("ret", 0);
                            obj.put("msg", "日期格式不正确，需要 yyyy/MM/dd 的格式！");
                            out.write(obj.toString());
                            out.close();
                            return;
                        }

                    }
                    ki.setBus_nm(vals[7]);
                    list.add(ki);

                }
                someService.insertChannelFeeAdv(list);
                obj.put("ret", 1);
                obj.put("msg", "上传文件成功");
                out.write(obj.toString());
                out.close();
            } else {
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls格式文件！");
                out.write(obj.toString());
                out.close();
            }

        } catch (Exception e) {
            //上传异常
            e.getMessage();
            response.setCharacterEncoding("utf-8");
            PrintWriter pw;
            try {
                pw = response.getWriter();
                JSONObject obj = new JSONObject();
                obj.put("ret", 0);
                obj.put("msg", e.getMessage());
                pw.write(obj.toString());
                pw.close();
            } catch (IOException e1) {
                e1.getMessage();
            }
        }
    }

    /**
     * 用户反馈提交接口
     *
     * @param request
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/custpost", method = {RequestMethod.POST})
    @ResponseBody
    public String userTalkBack(HttpServletRequest request, HttpServletResponse response) {
        //response.addHeader("Access-Control-Allow-Origin", "*");
        try {
            String params = request.getParameter("value");
            if (params != null && params.length() > 0) {
                String param = new String(Base64.decodeBase64(params));
                System.out.println("接收参数======" + param);
                JSONObject wxUserInfoVo = (JSONObject) JSONObject.parse(param);
                String msg = (String) wxUserInfoVo.get("msg");
                String contant = (String) wxUserInfoVo.get("contant");
                String imgurl = (String) wxUserInfoVo.get("imgurl");
                String value = (String) wxUserInfoVo.get("value");
                if (value == null) {
                    return "{ res:\"0\", msg:\"参数异常\" }";
                }
                UserTalkBack userTalkBack = new UserTalkBack();
                // String val = Base64Util.decode(value);
                String val = new String(Base64.decodeBase64(params));
                String[] vals = val.split("&");
                for (String str : vals) {
                    if (str.split("=")[0].equals("appid")) {
                        userTalkBack.setAppid(str.split("=")[1]);
                    }
                    if (str.split("=")[0].equals("pid")) {
                        userTalkBack.setPid(str.split("=")[1]);
                    }
                    if (str.split("=")[0].equals("lsn")) {
                        userTalkBack.setLsn(str.split("=")[1]);
                    }
                }
                userTalkBack.setMsg(msg);
                userTalkBack.setContant(contant);
                userTalkBack.setImgurl(imgurl);
                someService.insertUserTalkBack(userTalkBack);
                return "{ res:\"1\", msg:\"提交成功\" }";
            } else {
                return "{ res:\"0\", msg:\"参数异常\" }";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "{ res:\"0\", msg:\"参数异常\" }";
        }
    }

    /**
     * 生成按产品查加权平均sql
     *
     * @param startTime,endTime,appid
     * @return String
     */
    private String retentionSql(String startTime, String endTime, String appid) {
        {
            String sql = "";
            String leastDataDate = DateUtils.formatDateByPattern(new Date(), "yyyy-MM-dd");
            if (endTime.compareTo(leastDataDate) >= 0) {
                endTime = leastDataDate;
            }
            List<String> dateList = DateUtils.getDays(startTime, endTime);
            Collections.reverse(dateList);
            for (int n = 0; n < dateList.size(); n++) {
                String queryString = "";
                String paramString = "";
                String secondString = "";
                String thirdString = "";
                String fourthString = "";

                //第一部分
                String first = "Select DATE_FORMAT(c.mmdate,'%Y-%m-%d') dt,c.allNum usernum,c.product_id appid,c.projectid pid , ";

                for (int i = 1; i <= 30; i++) {
                    //第二部分 包含keep +TRUNCATE 层
                    //拼接keep层
                    paramString += "CONCAT(IFNULL(ROUND(SUM((c.keep" + i + "num /c.actNum" + i + " )* c.keep" + i + "num)/c.allNum *100,2),0) ,'%')keep" + i + " ,";
                    //拼接 TRUNCATE 部分
                    String kk = "";
                    for (int p = 1; p <= i; p++) {
                        kk += "+IFNULL(ROUND(SUM((c.keep" + p + "num /c.actNum" + p + " )* c.keep" + p + "num)/c.allNum,2),0)";
                    }
                    if (i <= 29) {
                        paramString += "TRUNCATE(1" + kk + ",2) keepnum" + i + ",";
                    } else {
                        paramString += "TRUNCATE(1" + kk + ",2) keepnum" + i;
                    }


                    secondString += "SUM( ROUND( usernum * keep" + i + ", 0 ) ) AS keep" + i + "num  ,";
                    secondString += "b.huoyue" + i + " as actNum" + i + " ,";


                    thirdString += "sum(keep" + i + " * usernum) AS huoyue" + i + " ,";
                }

                //第三部分
                String second = " from ( SELECT projectid,usernum,mmdate,product_id ,";
                String third = " b.totalNum as allNum  FROM  product_keep_num_total_new a,  ( Select ";
                second += secondString;
                third += thirdString + " SUM(usernum) as totalNum ";
                fourthString = "FROM product_keep_num_total_new WHERE product_id = " + appid + " AND mmdate = " + dateList.get(n) + " ) b WHERE a.product_id = " + appid + "  AND a.mmdate =" + dateList.get(n) + " GROUP BY a.projectid  ) c";
                //最终sql
                queryString += first + paramString + second + third + fourthString;
                if (n != dateList.size() - 1) {
                    sql += queryString + " UNION ALL ";
                } else {
                    sql += queryString;
                }
            }
            return sql;
        }
    }


}