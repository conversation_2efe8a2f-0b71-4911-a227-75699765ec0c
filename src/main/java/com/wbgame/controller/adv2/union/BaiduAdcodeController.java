package com.wbgame.controller.adv2.union;

import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.adv2.BaiduAdCodeVo;
import com.wbgame.service.adv2.adcode.BaiduAdcodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/10/23
 * @description
 **/
@RequestMapping("/adv2/baidu")
@CrossOrigin
@RestController
@Api("百度广告位相关请求")
public class BaiduAdcodeController {

    private static final String LOCK_BAIDU_FILE_BATCH_IMPORT = "lock_baidu_file_import";
    @Resource
    private BaiduAdcodeService baiduAdcodeService;
    @Resource
    private Redisson redisson;

    @ControllerLoggingEnhancer
    @PostMapping("/createAdcode")
    @ApiOperation("创建广告源")
    public Object createAdcode(BaiduAdCodeVo vo) {
        return baiduAdcodeService.createAdcode(vo);
    }

    @ControllerLoggingEnhancer
    @PostMapping("/list")
    @ApiOperation("查询广告源")
    public Object list(BaiduAdCodeVo vo, int start, int limit) {
        return ReturnJson.success(baiduAdcodeService.findBaiduAdCode(vo,start, limit));
    }

    @ControllerLoggingEnhancer
    @PostMapping("/import")
    @ApiOperation("上传广告位")
    public Object createAdcode(@RequestParam(value = "fileName") MultipartFile file) throws InterruptedException, IOException {
        RLock lock = redisson.getLock(LOCK_BAIDU_FILE_BATCH_IMPORT);
        if (lock.tryLock(1L, TimeUnit.SECONDS)) {
            try {
                return baiduAdcodeService.batchImport(file);
            } finally {
                lock.unlock();
            }
        } else {
            return ReturnJson.toErrorJson("有同步任务正在运行中，请稍后再试");
        }
    }
}
