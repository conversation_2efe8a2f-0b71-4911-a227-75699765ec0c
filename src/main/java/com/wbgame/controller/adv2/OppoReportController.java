package com.wbgame.controller.adv2;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.LoginCheck;
import com.wbgame.pojo.adv2.OppoReprtDTO;
import com.wbgame.pojo.jettison.report.InfoResult;
import com.wbgame.service.adv2.PlatformService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

import static com.alicp.jetcache.Cache.logger;

@CrossOrigin
@RestController
@RequestMapping("/adv2")
@Api(tags = "投放数据管理")
@ApiSupport(author = "xujingyu")
public class OppoReportController {

    @Autowired
    PlatformService platformService;

    @ApiOperation(value = "OPPO媒体报表查询")
    @PostMapping(value = "/oppo/list")
    @LoginCheck
    public InfoResult getOppoReport(@RequestBody OppoReprtDTO param) {
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = platformService.getOppoReport(param);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getOppoReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "OPPO媒体报表导出")
    @PostMapping(value = "/oppo/export")
    @LoginCheck
    public void getOppoReportExport(@RequestBody OppoReprtDTO param, HttpServletResponse response) {
        try {
            platformService.getOppoReportExport(param,response);
        } catch (Exception e) {
            logger.error("getOppoReportExport: ", e);
        }
    }
}
