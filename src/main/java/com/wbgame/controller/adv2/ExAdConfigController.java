package com.wbgame.controller.adv2;

import java.io.BufferedReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.mysql.jdbc.StringUtils;
import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.Asserts;
import com.wbgame.mapper.haiwaiad.HaiwaiCfgMapper;
import com.wbgame.mapper.master.AppInfoMapper;
import com.wbgame.pojo.adv2.*;
import com.wbgame.pojo.test.AppSignInfo;
import com.wbgame.service.AdService;
import com.wbgame.utils.ExportExcelUtil;
import io.swagger.annotations.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.adv2.Adv2Mapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.WaibaoUserVo;
import com.wbgame.service.adv2.Adv2Service;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpClientUtils;

import jxl.Sheet;
import jxl.Workbook;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;

/**
 * 新版本广告配置相关接口
 * <AUTHOR>
 */
@CrossOrigin
@RestController
@RequestMapping("/adv2")
@Api(tags = "变现相关")
public class ExAdConfigController {

	Logger logger = LoggerFactory.getLogger(ExAdConfigController.class);

	@Autowired
    private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private Adv2Service adv2Service;
	@Autowired
	private Adv2Mapper adv2Mapper;
	@Autowired
	private AdService adService;

	@Autowired
	private HaiwaiCfgMapper haiwaiCfgMapper;
	@Resource
	private AppInfoMapper appInfoMapper;

	/** 广告源类型*/
	public static final Map<String,String> SDK_AD_TYPE = new HashMap<String,String>(){{
		put("开屏","splash");
		put("原生开屏","natSplash");
		put("普通banner/模板","banner");
		put("banner自渲染","natBanner");
		put("信息流模板","yuans");
		put("信息流自渲染","msg");
		put("普通插屏/模板","plaque");
		put("自渲染插屏","natPlaque");
		put("插屏视频","plaqueVideo");
		put("视频","video");
		put("视频自渲染","natVideo");
		put("icon","icon");
		put("draw信息流","draw");
		put("box(底部4icon)","boxBanner");
		put("box(九宫格9icon)","boxPlaque");
	}};

	/** 广告位类型*/
	private static final Map<String,String>  AD_POS_AD_TYPE = new HashMap<String,String>(){{
		put("natBanner", "banner");
		put("nativeBanner", "banner");
		put("plaqueVideo", "plaque");
		put("natPlaque", "plaque");
		put("nativePlaque", "plaque");
		put("natSplash", "splash");
		put("natVideo", "video");
		put("yuans", "msg");
	}};


	/** 广告使用类型*/
	public static final Map<String,String> OPEN_AD_TYPE = new HashMap<String,String>(){{
		put("开屏", "splash");
		put("插屏", "plaque");
		put("视频", "video");
		put("信息流", "msg");
		put("banner", "banner");
		put("icon", "icon");
		put("draw信息流", "draw");
		put("box", "box");
	}};

	/** bidding模式 */
	private static final Map<String,String> BIDDING_MAP = new HashMap<String,String>(){{
		put("s2sbidding", "2");
		put("c2sbidding", "1");
		put("是", "1");
		put("否", "0");
	}};
	/** 应用内外 */
	private static final Map<String,String> OUT_MAP = new HashMap<String,String>(){{
		put("应用外", "1");
		put("应用内", "0");
	}};


	/**
	 * 应用列表
	 * @param platform
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="/app/getAppNameList", method={RequestMethod.GET,RequestMethod.POST})
	public String getAppNameList(String platform, HttpServletRequest request,HttpServletResponse response){

		JSONObject result = new JSONObject();
		try {
			String sql = "SELECT id,app_name FROM yyhz_0308.app_info where channel_id = 10118";
			List<Map<String, Object>> list = adv2Service.queryListMap(sql);
			result.put("ret", 1);
			result.put("data", list);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		return result.toJSONString();
	}

	/**
	 * 广告策略-查询
	 * @param request
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/app/selectExtendStrategy", method={RequestMethod.GET, RequestMethod.POST})
	public String selectExtendStrategy(ExtendStrategyVo ext,HttpServletRequest request) throws IOException {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		String[] args = {"strategy", "adpos_type", "model", "loadmodel", "floor", "note"};
		Map<String, String> params = BlankUtils.getParameter(request, args);

		JSONObject result = new JSONObject();
		try {
			String sql = "select * from dn_extend_strategy where 1=1";
			if(!BlankUtils.checkBlank(ext.getStrategy()))
				sql += " and strategy like concat('%',#{obj.strategy},'%')";
			if(!BlankUtils.checkBlank(ext.getAdpos_type()))
				sql += " and adpos_type = #{obj.adpos_type}";
			if(!BlankUtils.checkBlank(ext.getModel()))
				sql += " and model = #{obj.model}";
			if(!BlankUtils.checkBlank(ext.getLoadmodel()))
				sql += " and loadmodel = #{obj.loadmodel}";
			if(!BlankUtils.checkBlank(ext.getFloor()))
				sql += " and floor = #{obj.floor}";
			if(!BlankUtils.checkBlank(ext.getNote()))
				sql += " and `desc` like concat('%',#{obj.note},'%')";

			sql += " order by createtime desc";

			PageHelper.startPage(params); // 进行分页
			List<Map<String, Object>> list = adv2Service.queryListMapTwo(sql, ext);
			long size = ((Page) list).getTotal();
			list.forEach(act -> act.put("createtime", act.get("createtime")+""));
			list.forEach(act -> act.put("lasttime", act.get("lasttime")+""));

			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}

	/**
	 * 广告策略-新增修改操作
	 * @param handle=add、edit、del，新增、修改、删除
	 * @param request
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/app/extendStrategyHandle", method={RequestMethod.GET, RequestMethod.POST})
	public String extendStrategyHandle(String handle,ExtendStrategyVo ext,
									   HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}
		ext.setCuser(cuser);

		int result = 0;
		try {
			String sql = null;
			if("add".equals(handle)){
				sql = "insert into dn_extend_strategy(strategy,note,floor,adpos_type,`desc`,model,loadmodel,mulnum,loadmax,ctimeout,atimeout,`interval`,delay,showmax,createtime,lasttime,cuser,euser,clicklimit,clickmax,ltInterval,automodel,bidmodel,`loop`,ishigh,extra) "+
						"values(#{obj.strategy},#{obj.note},#{obj.floor},#{obj.adpos_type},#{obj.desc},#{obj.model},#{obj.loadmodel},#{obj.mulnum},#{obj.loadmax},#{obj.ctimeout},#{obj.atimeout},#{obj.interval},#{obj.delay},#{obj.showmax},now(),now(),#{obj.cuser},#{obj.cuser},#{obj.clicklimit},#{obj.clickmax},#{obj.ltInterval},#{obj.automodel},#{obj.bidmodel},#{obj.loop},#{obj.ishigh},#{obj.extra}) ";

			}else if("edit".equals(handle)){
				sql = "update dn_extend_strategy set note=#{obj.note},floor=#{obj.floor},adpos_type=#{obj.adpos_type},`desc`=#{obj.desc},model=#{obj.model},loadmodel=#{obj.loadmodel},mulnum=#{obj.mulnum},loadmax=#{obj.loadmax},ctimeout=#{obj.ctimeout},atimeout=#{obj.atimeout},`interval`=#{obj.interval},delay=#{obj.delay},showmax=#{obj.showmax},lasttime=now(),euser=#{obj.cuser},clicklimit=#{obj.clicklimit},clickmax=#{obj.clickmax},ltInterval=#{obj.ltInterval},automodel=#{obj.automodel},bidmodel=#{obj.bidmodel},`loop`=#{obj.loop},ishigh=#{obj.ishigh},extra=#{obj.extra} "+
						"where strategy = #{obj.strategy} ";

			}else if("del".equals(handle)){
				sql = "delete from dn_extend_strategy where strategy = #{obj.strategy} ";

			}

			result = adv2Service.execSqlHandle(sql, ext);
			if(result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";

		} catch (Exception e) {
			e.printStackTrace();

			if(e.toString().contains("Duplicate entry")) {
				return "{\"ret\":0,\"msg\":\"已有相同策略ID存在!\"}";
			}
			return "{\"ret\":0,\"msg\":\"操作失败，错误信息 : "+e.getMessage()+"\"}";
		}
	}

	/**
     * 广告策略-批量修改
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/app/batchEditExtendStrategy", method={RequestMethod.POST})
	public String batchEditExtendStrategy(String handle, ExtendStrategyVo app,
										  HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);


		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}
		app.setCuser(cuser);

		int result = 0;
		try {
			String set = "";
			if(app.getLoadmodel() != null){
				set += "loadmodel=#{obj.loadmodel},";
			}
			if(app.getLoop() != null){
				set += "`loop`=#{obj.loop},";
			}
			if(app.getIshigh() != null){
				set += "ishigh=#{obj.ishigh},";
			}
			if(app.getFloor() != null){
				set += "floor=#{obj.floor},";
			}

			if(app.getAutomodel() != null){
				set += "automodel=#{obj.automodel},";
			}
			if(app.getBidmodel() != null){
				set += "bidmodel=#{obj.bidmodel},";
			}
			if(app.getExtra() != null){
				set += "extra=#{obj.extra},";
			}

			String sql = "update dn_extend_strategy set "+set+" lasttime=now(),euser=#{obj.cuser} where strategy in ("+app.getStrategy()+") ";

			result = adv2Service.execSqlHandle(sql, app);
			if(result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";

		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	@ControllerLoggingEnhancer
	@RequestMapping(value="/app/syncAdsidToAdpos", method={RequestMethod.POST})
	public String syncAdsidToAdpos(HttpServletRequest request) throws IOException {
		String userName = LOGIN_USER_NAME.get();
		JSONObject jsonRequest = getJsonRequest(request);
		JSONArray jsonArray = jsonRequest.getJSONArray("voList");
		List<ExtendAdsidVo> apps = jsonArray.stream().map(o -> (JSONObject) o).map(o -> {
			ExtendAdsidVo extendAdsidVo = new ExtendAdsidVo();
			extendAdsidVo.setAppid(o.getString("appid"));
			extendAdsidVo.setCha_id(o.getString("cha_id"));
			return extendAdsidVo;
		}).collect(Collectors.toList());
		return adv2Service.syncAdsidToAdpos(apps, userName);
	}

	private JSONObject getJsonRequest(HttpServletRequest request) {
		JSONObject result = null;
		StringBuilder sb = new StringBuilder();
		try (BufferedReader reader = request.getReader();) {
			char[] buff = new char[1024 * 10];
			int len;
			while ((len = reader.read(buff)) != -1) {
				sb.append(buff, 0, len);
			}
			result = JSON.parseObject(sb.toString());
		} catch (IOException e) {
			logger.error("", e);
		}
		return result;
	}

	/**
     * 新版本广告源配置
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/app/selectExtendAdsid", method={RequestMethod.GET, RequestMethod.POST})
	public String selectExtendAdsid(ExtendAdsidVo app, HttpServletRequest request) throws IOException {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		Map<String, String> pageParams = BlankUtils.getParameter(request);
		JSONObject result = new JSONObject();
		try {
			PageHelper.startPage(pageParams); // 进行分页
			List<Map<String, Object>> list = adv2Service.selectDnExtendAdsidManage(app);
			long size = ((Page) list).getTotal();

			/*
				广告源管理和广告配置，加上广告源状态查询、渠道在线名称，包名，产品在线状态
				1、广告源状态可以从媒体广告位状态查询拉  1启动，2暂停
				2、渠道在线名称，包名，产品在线状态可以从综合系统-渠道产品id关联配置拉
			 */
			String query = "select adsid mapkey,statu from dnwx_cfg.dn_online_adpos_status where adsid is not null and statu is not null group by adsid";
			Map<String, Map<String, Object>> statuMap = adv2Service.queryListMapOfKey(query);

			String query2 = "select CONCAT(appid,channel) mapkey,tappname,packagename,state from yyhz_0308.adv_platform_app_info where channel is not null and state is not null";
			Map<String, Map<String, Object>> platformMap = adv2Service.queryListMapOfKey(query2);

			Map<String,String> STATE_MAP = new HashMap<String,String>(){{
				put("0","未提交");
				put("1","已上线");
				put("2","已下线");
				put("3","未发布");
				put("4","自动化审核中");
				put("5","审核中");
				put("6","审核驳回");
				put("7","定时发布");
				put("8","资质审核");
				put("9","资质驳回");
				put("10","资质通过");
				put("11","已冻结");
				put("12","报备成功");
				put("13","撤销上线");
				put("14","测试中");
				put("15", "升级中");
				put("16", "下线审核");
				put("17", "未定义");
				put("18", "下线驳回");
				put("19", "审核通过");
				put("20", "运营打回");
				put("21", "运营通过");
				put("22", "其他");
			}};

			list.forEach(act -> {
				Map<String, Object> statu = statuMap.get(act.getOrDefault("adsid", "").toString());
				if(statu != null){
					act.put("adsid_statu", ("1".equals(statu.getOrDefault("statu","").toString())?"开启":"2".equals(statu.getOrDefault("statu","").toString())?"暂停":"") );
				}
				Map<String, Object> platform = platformMap.get(act.get("appid")+""+act.get("cha_id"));
				if(platform != null){
					act.put("tappname", platform.get("tappname")+"");
					act.put("packagename", platform.get("packagename")+"");
					act.put("app_statu", (STATE_MAP.get(platform.getOrDefault("state", "").toString())!=null?STATE_MAP.get(platform.getOrDefault("state", "").toString()):"") );
				}

				act.put("createtime", act.get("createtime")+"");
				act.put("lasttime", act.get("lasttime")+"");
			});

			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}

	public static final Map<String, String> skdAdTypeMap = new HashMap<String, String>(){{
		put("splash","开屏");
		put("natSplash","原生开屏");
		put("banner","普通banner/模板");
		put("natBanner","banner自渲染");
		put("yuans","信息流模板");
		put("msg","信息流自渲染");
		put("plaque","普通插屏/模板");
		put("natPlaque","自渲染插屏");
		put("plaqueVideo","插屏视频");
		put("video","视频");
		put("natVideo","视频自渲染");
		put("icon","icon");
		put("draw","draw信息流");
	}};
	@ApiOperation(value = "广告源导出", notes = "广告源导出")
	@PostMapping("/app/exportExtendAdsid")
	@ControllerLoggingEnhancer
	@ApiImplicitParams({
			@ApiImplicitParam(name = "value", value = "导出头定义", dataType = "String")
	})
	public void exportExtendAdsid(ExtendAdsidVo app, String value, HttpServletResponse response) {
		List<Map<String, Object>> list = adv2Service.selectDnExtendAdsidManage(app);
		List<AppSignInfo> appSignArr = appInfoMapper.getAppSignArr(null);
		Map<String, String> appMap = appSignArr.stream().collect(Collectors.toMap(AppSignInfo::getId, AppSignInfo::getAppName));

		/*
			广告源管理和广告配置，加上广告源状态查询、渠道在线名称，包名，产品在线状态
			1、广告源状态可以从媒体广告位状态查询拉  1启动，2暂停
			2、渠道在线名称，包名，产品在线状态可以从综合系统-渠道产品id关联配置拉
		 */
		String query = "select adsid mapkey,statu from dnwx_cfg.dn_online_adpos_status where adsid is not null and statu is not null group by adsid";
		Map<String, Map<String, Object>> statuMap = adv2Service.queryListMapOfKey(query);

		String query2 = "select CONCAT(appid,channel) mapkey,tappname,packagename,state from yyhz_0308.adv_platform_app_info where channel is not null and state is not null";
		Map<String, Map<String, Object>> platformMap = adv2Service.queryListMapOfKey(query2);

		Map<String,String> STATE_MAP = new HashMap<String,String>(){{
			put("0","未提交");
			put("1","已上线");
			put("2","已下线");
			put("3","未发布");
			put("4","自动化审核中");
			put("5","审核中");
			put("6","审核驳回");
			put("7","定时发布");
			put("8","资质审核");
			put("9","资质驳回");
			put("10","资质通过");
			put("11","已冻结");
			put("12","报备成功");
			put("13","撤销上线");
			put("14","测试中");
			put("15", "升级中");
			put("16", "下线审核");
			put("17", "未定义");
			put("18", "下线驳回");
			put("19", "审核通过");
			put("20", "运营打回");
			put("21", "运营通过");
			put("22", "其他");
		}};

		list.forEach(act -> {
			Map<String, Object> statu = statuMap.get(act.getOrDefault("adsid", "").toString());
			if(statu != null){
				act.put("adsid_statu", ("1".equals(statu.get("statu").toString())?"开启":"2".equals(statu.get("statu").toString())?"暂停":"") );
			}
			Map<String, Object> platform = platformMap.get(act.get("appid")+""+act.get("cha_id"));
			if(platform != null){
				act.put("tappname", platform.get("tappname")+"");
				act.put("packagename", platform.get("packagename")+"");
				act.put("app_statu", (STATE_MAP.get(platform.get("state").toString())!=null?STATE_MAP.get(platform.get("state").toString()):"") );
			}

			act.put("sdk_adtype", skdAdTypeMap.get(BlankUtils.getString(act.get("sdk_adtype"))));

			int bidding = BlankUtils.getInt(act.get("bidding"));
			if (bidding == 0) {
				act.put("bidding", "否");
			} else if (bidding == 1){
				act.put("bidding", "c2sbidding");
			} else if (bidding == 2){
				act.put("bidding", "s2sbidding");
			}

			int out = BlankUtils.getInt(act.get("out"));
			if (out == 0) {
				act.put("out", "应用内");
			} else if (out == 1) {
				act.put("out", "应用外");
			}

			String appid = BlankUtils.getString(act.get("appid"));
			act.put("appid", appMap.get(appid) + "-" + appid);

			act.put("createtime", act.get("createtime")+"");
			act.put("lasttime", act.get("lasttime")+"");

		});

		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		if (!StringUtils.isNullOrEmpty(value)){
			//自定义列数据
			try {
				String[] split = value.split(";");
				for (int i = 0;i<split.length;i++) {
					String[] s = split[i].split(",");
					headerMap.put(s[0], s[1]);
				}
			}catch (Exception e) {
				e.printStackTrace();
				Asserts.fail("自定义列导出异常");
			}
		}
		ExportExcelUtil.export(response, list, headerMap, "广告源管理_" + DateTime.now().toString("yyyyMMdd") + ".xls");
	}

	@RequestMapping(value="/app/extendAdsidHandle", method={RequestMethod.POST})
	public String extendAdsidHandle(String handle, ExtendAdsidVo app,
									HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}
		app.setCuser(cuser);

		int result = 0;
		try {
			String sql = null;
			Map<String, String> typeMap = new HashMap<>();
			typeMap.put("natBanner", "banner");
			typeMap.put("nativeBanner", "banner");
			typeMap.put("plaqueVideo", "plaque");
			typeMap.put("natPlaque", "plaque");
			typeMap.put("nativePlaque", "plaque");
			typeMap.put("natSplash", "splash");
			typeMap.put("natVideo", "video");
			typeMap.put("yuans", "msg");

			if(!BlankUtils.checkBlank(app.getSdk_adtype())){
				String adtype = typeMap.get(app.getSdk_adtype());
				if(adtype != null)
					app.setAdpos_type(adtype);
				else
					app.setAdpos_type(app.getSdk_adtype());
			}

			if("add".equals(handle)){
				sql = "insert into dn_extend_adsid_manage(adsid,agent,sdk_code,sdk_appid,sdk_appkey,sdk_adtype,adpos_type,open_type,note,appid,cha_id,createtime,lasttime,cuser,cpmFloor,bidding,unit_id,params,`out`) "+
						"values(#{obj.adsid},#{obj.agent},#{obj.sdk_code},#{obj.sdk_appid},#{obj.sdk_appkey},#{obj.sdk_adtype},#{obj.adpos_type},#{obj.open_type},#{obj.note},#{obj.appid},#{obj.cha_id},now(),now(),#{obj.cuser},#{obj.cpmFloor},#{obj.bidding},#{obj.unit_id},#{obj.params},#{obj.out}) ";

				/* 底价值不为空，则操作快手api进行修改 */
				if(!BlankUtils.checkBlank(app.getCpmFloor()) && BlankUtils.isNumeric(app.getCpmFloor())) {

					String app_category = "3"; // 产品类型 ：1.超休闲 2.红包 3.应用;4、经典;5、暖暖
					String query = "select app_category from yyhz_0308.app_info where id='"+app.getAppid()+"'";
					List<String> catList = adv2Service.queryListString(query);
					if(catList != null && !catList.isEmpty())
						app_category = catList.get(0);

					if ("kuaishou".equals(app.getAgent())){
						boolean flag = modCpmFloor(app, app_category);
						if(!flag)
							return "{\"ret\":0,\"msg\":\"修改底价失败!\"}";
					}
				}
			}else if("edit".equals(handle)){
				sql = "update dn_extend_adsid_manage set appid=#{obj.appid},cha_id=#{obj.cha_id},agent=#{obj.agent},sdk_code=#{obj.sdk_code},sdk_appid=#{obj.sdk_appid},sdk_appkey=#{obj.sdk_appkey},sdk_adtype=#{obj.sdk_adtype},adpos_type=#{obj.adpos_type},open_type=#{obj.open_type},note=#{obj.note}," +
							"lasttime=now(),cuser=#{obj.cuser},cpmFloor=#{obj.cpmFloor},bidding=#{obj.bidding},unit_id=#{obj.unit_id},params=#{obj.params},`out`=#{obj.out} "+
						"where adsid = #{obj.adsid}";

				/* 底价值不为空，则操作快手api进行修改 */
				if(!BlankUtils.checkBlank(app.getCpmFloor()) && BlankUtils.isNumeric(app.getCpmFloor())) {

					String app_category = "3"; // 产品类型 ：1.超休闲 2.红包 3.应用;4、经典;5、暖暖
					String query = "select app_category from yyhz_0308.app_info where id='"+app.getAppid()+"'";
					List<String> catList = adv2Service.queryListString(query);
					if(catList != null && !catList.isEmpty()) {
						app_category = catList.get(0);
					}
					if ("kuaishou".equals(app.getAgent())){
						boolean flag = modCpmFloor(app, app_category);
						if (!flag)
							return "{\"ret\":0,\"msg\":\"修改底价失败!\"}";
					}
				}
			}else if("del".equals(handle)){
				sql = "delete from dn_extend_adsid_manage where adsid in ("+app.getAdsid()+")";

				/* 增加海外库的删除操作执行 */
//				try{
//					String sql2 = "delete from dn_extend_adsid_manage where adsid = #{obj.adsid}";
//					haiwaiCfgMapper.execSqlHandle(sql2, app);
//				} catch (Exception e) {
//					e.printStackTrace();
//				}
			}

			if(sql != null){
				result = adv2Service.execSqlHandle(sql, app);
			}

			if("batch".equals(handle)){
				String adsid = app.getAdsid();
				if(!BlankUtils.checkBlank(adsid) && adsid.split(",").length > 1){
					adsid = String.join("','", adsid.split(","));
				}
				app.setAdsid(adsid);
				result = adv2Mapper.editExtendAdsidVo(app);
			}

			if(result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";

		} catch (Exception e) {
			e.printStackTrace();

			if(e.toString().contains("Duplicate entry")) {
				return "{\"ret\":0,\"msg\":\"已有相同广告源存在!\"}";
			}
			return "{\"ret\":0,\"msg\":\"操作失败，错误信息 : "+e.getMessage()+"\"}";
		}
	}

	/**
	 * 修改广告位底价
	 * @param app 被修改对象
	 * @param app_category 所属账户区分
	 * @return true:false
	 */
	public static boolean modCpmFloor(ExtendAdsidVo app,String app_category){
		try {
			String host = "https://u.kuaishou.com";
			String url = "/api/position/modCpmFloor";
			TreeMap<String, String> params = new TreeMap<String, String>();
			params.put("ak", "5331");
			params.put("sk", "3b97e5164eeb504bab85830191a9269c");
			if(!"3".equals(app_category)){
				params.put("ak", "5052");
				params.put("sk", "abc36ec24e4526b7f5019e624d3169ec");
			}
			params.put("date", DateTime.now().toString("yyyy-MM-dd"));
			params.put("timestamp", (DateTime.now().getMillis()/1000)+"");

			String str = params.entrySet().stream()
					.map(act -> act.getKey()+"="+act.getValue()).collect(Collectors.joining("&"));
			String sign = DigestUtils.md5Hex(url+"?"+str);
			params.put("sign", sign);

			params.remove("sk");
			String base = params.entrySet().stream()
					.map(act -> act.getKey()+"="+act.getValue()).collect(Collectors.joining("&"));
			String link = host+url+"?"+base;

			Map<String, String> headMap = new HashMap<>();
			headMap.put("Content-Type", "application/json");
			String json = "{\"appId\":\""+app.getSdk_appid()+"\",\"positionId\":\""+app.getSdk_code()+"\",\"cpmFloor\":\""+app.getCpmFloor()+"\"}";
			String httpPost = HttpClientUtils.getInstance().httpPost(link, json, headMap);
			System.out.println("httpPost=="+httpPost);
			if(httpPost != null && "true".equals(JSONObject.parseObject(httpPost).getString("data"))){
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	@RequestMapping(value="/app/batchCopyExtendAdsid", method={RequestMethod.POST})
	public String batchCopyExtendAdsid(String handle, ExtendAdsidVo app,
									   HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}

		int result = 0;
		try {
			// 广告拓展名,替换拓展名,替换所属应用,替换子渠道,替换sdk-appid
			String x1 = request.getParameter("x1");
			String x2 = request.getParameter("x2");
			String x3 = request.getParameter("x3");
			String x4 = request.getParameter("x4");
			String x5 = request.getParameter("x5");
			String adsids = request.getParameter("adsids");
			String join = String.join("','", adsids.split(","));

			if(!BlankUtils.checkBlank(adsids) && !BlankUtils.checkBlank(x1)){
				String sql = "select * from dn_extend_adsid_manage where adsid in ('"+join+"')";
				List<Map<String, Object>> list = adv2Service.queryListMap(sql);
				list.forEach(act -> {
					act.put("adsid", (act.get("adsid")+"").replace(x1, x2));

					if(!BlankUtils.checkBlank(x3))
						act.put("appid", x3);
					if(!BlankUtils.checkBlank(x4))
						act.put("cha_id", x4);
					if(!BlankUtils.checkBlank(x5))
						act.put("sdk_appid", x5);


				});
				try {
					Map<String, Object> paramMap = new HashMap<String, Object>();
					paramMap.put("sql1", "insert into dn_extend_adsid_manage(adsid,agent,sdk_code,sdk_appid,sdk_appkey,sdk_adtype,adpos_type,open_type,note,appid,cha_id,createtime,lasttime,cuser,cpmFloor,bidding,unit_id,params,`out`) values ");
					paramMap.put("sql2", " (#{li.adsid},#{li.agent},#{li.sdk_code},#{li.sdk_appid},#{li.sdk_appkey},#{li.sdk_adtype},#{li.adpos_type},#{li.open_type},#{li.note},#{li.appid},#{li.cha_id},now(),now(),'"+cuser+"',#{li.cpmFloor},#{li.bidding},#{li.unit_id},#{li.params},#{li.out}) ");
					paramMap.put("sql3", " ");
					paramMap.put("list", list);
					adv2Service.batchExecSql(paramMap);

					result = 1;
				} catch (Exception e) {
					e.printStackTrace();
				}
			}

			if(result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";

		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	/**
     * 同步快手底价数据到广告源
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/app/syncKuaishouCpmFloor", method={RequestMethod.POST})
    public String syncKuaishouCpmFloor(HttpServletRequest request, HttpServletResponse response) {

		try{
			boolean resp = adv2Service.syncKuaishouCpmFloor();
			if(resp)
				return "{\"ret\":1,\"msg\":\"同步成功！\"}";
			else
				return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
		}catch(Exception e){
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
		}
    }


	/**
     * 新版本广告位配置
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/app/selectExtendAdpos", method={RequestMethod.GET, RequestMethod.POST})
	public String selectExtendAdpos(ExtendAdposVo app, HttpServletRequest request) throws IOException {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);


		Map<String, String> pageParams = BlankUtils.getParameter(request);
		JSONObject result = new JSONObject();
		try {

			PageHelper.startPage(pageParams); // 进行分页
			List<Map<String, Object>> list = adv2Service.selectDnExtendAdposManage(app);
			long size = ((Page) list).getTotal();

			list.forEach(act -> {
				act.put("date", act.get("createtime")+"");
				act.put("createtime", act.get("createtime")+"");
				act.put("lasttime", act.get("lasttime")+"");
			});

			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}

	/**
     * 新版本广告位配置-新增修改操作
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/app/extendAdposHandle", method={RequestMethod.POST})
	public String extendAdposHandle(String handle, ExtendAdposVo app,
									HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}
		app.setCuser(cuser);


		// 筛除sql敏感字符注入
		if(BlankUtils.sqlValidate(app.getId())){
			return "{\"ret\":0,\"msg\":\"param is error!\"}";
		}

		int result = 0;
		try {
			String sql = null;

			// 新增修改时，查询广告位管理相关维度配置的"广告策略"是否不一致，不一致则返回错误让页面告警
			// 过滤关闭的状态，判断配置列表中多条策略有中一条相同就行 -林小敏.20250110
			String where = " where appid=#{obj.appid} and user_group=#{obj.user_group} and adpos_type=#{obj.adpos_type} and statu=1 ";
			if(!BlankUtils.checkBlank(app.getCha_id())) {
				where += " and cha_id=#{obj.cha_id}";
			}else{
				where += " and IFNULL(cha_id,'') = '' ";
			}
			if(!BlankUtils.checkBlank(app.getPrjid())){
				where += " and prjid=#{obj.prjid}";
			}else{
				where += " and IFNULL(prjid,'') = '' ";
			}
			String checkQuery = "select * from dnwx_cfg.dn_extend_adconfig "+where;

			if("add".equals(handle)){

				sql = "insert into dn_extend_adpos_manage(appid,cha_id,prjid,user_group,adpos_type,adpos,strategy,adstyle,showrate,statu,note,delaySecond,startLv,endLv,lvInterval,xdelay,autoInterval,`out`,custom_param,prjid_group_id,createtime,lasttime,cuser) "+
						"values(#{obj.appid},#{obj.cha_id},#{obj.prjid},#{obj.user_group},#{obj.adpos_type},#{obj.adpos},#{obj.strategy},#{obj.adstyle},#{obj.showrate},#{obj.statu},#{obj.note},#{obj.delaySecond},#{obj.startLv},#{obj.endLv},#{obj.lvInterval},#{obj.xdelay},#{obj.autoInterval},#{obj.out},#{obj.custom_param},#{obj.prjid_group_id},now(),now(),#{obj.cuser}) ";

			}else if("edit".equals(handle)){
				List<Map<String, Object>> checkList = adv2Service.queryListMapTwo(checkQuery, app);
				if(checkList != null && !checkList.isEmpty()){
					List<String> strategyList = checkList.stream().filter(act -> act.get("strategy") != null)
													.map(act -> act.get("strategy").toString()).collect(Collectors.toList());
					List<String> edList = Arrays.asList(app.getStrategy().split(","));
					if(!strategyList.isEmpty() && !strategyList.containsAll(edList)){
						return "{\"ret\":0,\"msg\":\"与广告配置相同维度的广告策略不一致，请检查!\"}";
					}
				}

				sql = "update dn_extend_adpos_manage set appid=#{obj.appid},cha_id=#{obj.cha_id},prjid=#{obj.prjid},user_group=#{obj.user_group},adpos_type=#{obj.adpos_type},strategy=#{obj.strategy},adstyle=#{obj.adstyle},showrate=#{obj.showrate},statu=#{obj.statu},note=#{obj.note},lasttime=now(),euser=#{obj.cuser}, "+
						"delaySecond=#{obj.delaySecond},startLv=#{obj.startLv},endLv=#{obj.endLv},lvInterval=#{obj.lvInterval},xdelay=#{obj.xdelay},autoInterval=#{obj.autoInterval},`out`=#{obj.out},custom_param=#{obj.custom_param},prjid_group_id=#{obj.prjid_group_id} where id = #{obj.id} ";

			}else if("del".equals(handle)){
				sql = "delete from dn_extend_adpos_manage where id in ("+app.getId()+")";

				/* 增加海外库的删除操作执行 */
//				try{
//					String sql2 = "delete from dn_extend_adpos_manage where id in ("+app.getId()+")";
//					haiwaiCfgMapper.execSql(sql2);
//				} catch (Exception e) {
//					e.printStackTrace();
//				}
			}else if("editStatu".equals(handle)){
				sql = "update dn_extend_adpos_manage set statu=#{obj.statu},lasttime=now(),euser=#{obj.cuser} where id in ("+app.getId()+")";
			}

//			/* 刷新广告位管理的缓存 -延迟刷新 */
//			JSONObject info = new JSONObject();
//			info.put("mark", "newWjy");
//			info.put("appid", app.getAppid());
//
//			long millis = DateTime.now().plusSeconds(60).getMillis();
//			String id = "newWjy:"+millis;
//
//			Message message = new Message(id, info.toJSONString(), millis);
//			Boolean push = delayService.push(message);
//			if(!push)
//				return "{\"ret\":0,\"msg\":\"操作失败，请检查后重试!\"}";


			result = adv2Service.execSqlHandle(sql, app);
			if(result > 0) {
				/* 刷新广告位管理的缓存 */
				adv2Service.recacheDnExtendAd("301", app.getAppid());
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			}else {
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";
			}
		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	/**
     * 新版本广告位配置-批量复制
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/app/batchCopyExtendAdpos", method={RequestMethod.POST})
	public String batchCopyExtendAdpos(String handle, ExtendAdposVo app,
									   @RequestParam(value = "recache_appid", defaultValue = "13665") String recache_appid,
									   HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}
		app.setCuser(cuser);

		int result = 0;
		try {
			// 替换所属应用,替换子渠道,替换广告位,替换项目ID,替换用户分组
			String ids = request.getParameter("ids");
			String new_appid = request.getParameter("new_appid");
			String new_cha_id = request.getParameter("new_cha_id");
			String new_adpos = request.getParameter("new_adpos");
			String new_prjid = request.getParameter("new_prjid");
			String new_user_group = request.getParameter("new_user_group");
			// 增加广告源
			String new_strategy = request.getParameter("new_strategy");
			String new_custom_param = request.getParameter("new_custom_param");
			String new_prjid_group_id = request.getParameter("new_prjid_group_id");

			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(ids)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}

			if(!BlankUtils.checkBlank(ids)){
				String sql = "select * from dn_extend_adpos_manage where id in ("+ids+")";
				List<Map<String, Object>> storeCopyList = new ArrayList<>();
				List<Map<String, Object>> list = adv2Service.queryListMap(sql);
				list.forEach(act -> {
					// 替换内容
					if(new_adpos != null){
						act.put("adpos", new_adpos);
					}

					if(new_cha_id != null){
						act.put("cha_id", new_cha_id);
					}
					if(new_prjid != null){
						act.put("prjid", new_prjid);
					}
					if(new_user_group != null){
						act.put("user_group", new_user_group);
					}

					// 增加广告源
					if(new_strategy != null){
						act.put("strategy", new_strategy);
					}
					// 增加自定义参数
					if(new_custom_param != null){
						act.put("custom_param", new_custom_param);
					}
					if(new_prjid_group_id != null){
						act.put("prjid_group_id", new_prjid_group_id);
					}
					//替换应用名称，可多选，接收数据格式： 7777,8888
					if(new_appid != null){
						String[] split = new_appid.split(",");
						for (String appid : split) {
							Map<String, Object> copyMap = new HashMap<>(act);
							copyMap.put("appid", appid);
							storeCopyList.add(copyMap);
						}
					} else {
						storeCopyList.add(act);
					}

				});
				try {
					Map<String, Object> paramMap = new HashMap<String, Object>();
					paramMap.put("sql1", "insert into dn_extend_adpos_manage(appid,cha_id,prjid,user_group,adpos_type,adpos,strategy,adstyle,showrate,statu,note,delaySecond,startLv,endLv,lvInterval,xdelay,autoInterval,`out`,custom_param,prjid_group_id,createtime,lasttime,cuser) values ");
					paramMap.put("sql2", " (#{li.appid},#{li.cha_id},#{li.prjid},#{li.user_group},#{li.adpos_type},#{li.adpos},#{li.strategy},#{li.adstyle},#{li.showrate},#{li.statu},#{li.note},#{li.delaySecond},#{li.startLv},#{li.endLv},#{li.lvInterval},#{li.xdelay},#{li.autoInterval},#{li.out},#{li.custom_param},#{li.prjid_group_id},now(),now(),'"+cuser+"') ");
					paramMap.put("sql3", " ");
					paramMap.put("list", storeCopyList);
					adv2Service.batchExecSql(paramMap);

					result = 1;
				} catch (Exception e) {
					e.printStackTrace();
				}
			}


			if(result > 0) {
				/* 刷新广告位管理的缓存 */
				adv2Service.recacheDnExtendAd("301", recache_appid);
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			}else {
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";
			}
		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	/**
     * 新版本广告位配置-批量修改
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/app/batchEditExtendAdpos", method={RequestMethod.POST})
	public String batchEditExtendAdpos(String handle, ExtendAdposVo app,
									   @RequestParam(value = "recache_appid", defaultValue = "13665") String recache_appid,
									   HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// 筛除sql敏感字符注入
		if(BlankUtils.sqlValidate(app.getId())){
			return "{\"ret\":0,\"msg\":\"param is error!\"}";
		}

		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}
		app.setCuser(cuser);

		int result = 0;
		try {
			String set = "";
			if(app.getCha_id() != null){
				set += "cha_id=#{obj.cha_id},";
			}
			if(app.getPrjid() != null){
				set += "prjid=#{obj.prjid},";
			}
			if(app.getAdpos_type() != null){
				set += "adpos_type=#{obj.adpos_type},";
			}
			if(app.getUser_group() != null){
				set += "user_group=#{obj.user_group},";
			}

			if(app.getStrategy() != null){
				set += "strategy=#{obj.strategy},";
			}
			if(app.getAdstyle() != null){
				set += "adstyle=#{obj.adstyle},";
			}
			if(app.getXdelay() != null){
				set += "xdelay=#{obj.xdelay},";
			}
			if(app.getShowrate() >= 0){
				set += "showrate=#{obj.showrate},";
			}
			if(app.getDelaySecond() != null){
				set += "delaySecond=#{obj.delaySecond},";
			}
			if(app.getStartLv() != null){
				set += "startLv=#{obj.startLv},";
			}
			if(app.getEndLv() != null){
				set += "endLv=#{obj.endLv},";
			}
			if(app.getLvInterval() != null){
				set += "lvInterval=#{obj.lvInterval},";
			}
			if(app.getCustom_param() != null){
				set += "custom_param=#{obj.custom_param},";
			}
			if(app.getPrjid_group_id() != null){
				set += "prjid_group_id=#{obj.prjid_group_id},";
			}


			if(app.getOut() != null){
				set += "`out`=#{obj.out},";
			}
			if(app.getStatu() != null){
				set += "`statu`=#{obj.statu},";
			}
			if(app.getNote() != null){
				set += "`note`=#{obj.note},";
			}

			String sql = "update dn_extend_adpos_manage set "+set+" lasttime=now(),euser=#{obj.cuser} where id in ("+app.getId()+") ";
			result = adv2Service.execSqlHandle(sql, app);

			if(result > 0) {
				/* 刷新广告位管理的缓存 */
				adv2Service.recacheDnExtendAd("301", recache_appid);

				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			}else {
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";
			}

		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	/**
     * 新版本广告位配置-批量导入
     * @param request
     * @param file
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/app/importExtendAdpos", method = {RequestMethod.POST})
    public String importExtendAdpos(@RequestParam(value = "fileName") MultipartFile file, String token, HttpServletRequest request, HttpServletResponse response) throws IOException {

        JSONObject obj = new JSONObject();
        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<ExtendAdposVo> list = new ArrayList<ExtendAdposVo>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;

                    String[] vals = new String[11];
                    for (int c = 0; c < 11; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }

                    ExtendAdposVo gad = new ExtendAdposVo();

                    // 使用默认值
                    gad.setShowrate(100);
                    gad.setDelaySecond("0");
                    gad.setStartLv("0");
                    gad.setEndLv("0");
                    gad.setLvInterval("0");
                    gad.setXdelay("0");
                    gad.setAutoInterval("0");
                    gad.setCreatetime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
                    gad.setLasttime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));

                    try {
						// 操作人登录名获取
						String cuser = "";
						if(token.startsWith("wbtoken")){
							CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
							cuser = curr.getLogin_name();
						}else if(token.startsWith("waibaotoken")){
							WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
							cuser = curr.getUser_id();
						}
						gad.setCuser(cuser);
					} catch (Exception e) {
						e.printStackTrace();
					}

					gad.setAppid(vals[0]);
                    gad.setCha_id(vals[1]);
                    gad.setPrjid(vals[2]);
                    gad.setAdpos_type(vals[3]);
                    gad.setAdpos(vals[4]);
                    gad.setStrategy(vals[5]);
                    gad.setUser_group(vals[6]);
                    gad.setAdstyle(vals[7]);
                    gad.setOut(vals[8]);
                    gad.setStatu(vals[9]);
                    gad.setNote(vals[10]);

                    if(BlankUtils.checkBlank(vals[3])
							|| BlankUtils.checkBlank(vals[4])
							|| BlankUtils.checkBlank(vals[5])
							|| BlankUtils.checkBlank(vals[6])){
						obj.put("ret", 0);
                        obj.put("msg", "必填值不能为空，请检查后重试！");
                        return obj.toJSONString();
                    }

                    if(!"1".equals(vals[8]) && !"0".equals(vals[8])){
						obj.put("ret", 0);
                        obj.put("msg", "上传的数据中有格式错误，请检查后重试！");
                        return obj.toJSONString();
                    }
                    if(!"1".equals(vals[9]) && !"0".equals(vals[9])){
						obj.put("ret", 0);
                        obj.put("msg", "上传的数据中有格式错误，请检查后重试！");
                        return obj.toJSONString();
                    }

                    if(BlankUtils.checkBlank(vals[0]) || !BlankUtils.isNumeric(vals[0])){
						obj.put("ret", 0);
                        obj.put("msg", "上传的数据中有格式错误，请检查后重试！");
                        return obj.toJSONString();
                    }

                    list.add(gad);
                }

				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("sql1", "insert into dn_extend_adpos_manage(appid,cha_id,prjid,user_group,adpos_type,adpos,strategy,adstyle,showrate,statu,note,delaySecond,startLv,endLv,lvInterval,xdelay,autoInterval,`out`,createtime,lasttime,cuser) values ");
				paramMap.put("sql2", " (#{li.appid},#{li.cha_id},#{li.prjid},#{li.user_group},#{li.adpos_type},#{li.adpos},#{li.strategy},#{li.adstyle},#{li.showrate},#{li.statu},#{li.note},#{li.delaySecond},#{li.startLv},#{li.endLv},#{li.lvInterval},#{li.xdelay},#{li.autoInterval},#{li.out},now(),now(),#{li.cuser}) ");
				paramMap.put("sql3", " ");
				paramMap.put("list", list);
				adv2Service.batchExecSql(paramMap);

				/* 刷新广告位管理的缓存 */
				try {
					String appids = list.stream().map(act -> act.getAppid()).distinct().collect(Collectors.joining());
					adv2Service.recacheDnExtendAd("301", appids);
				}catch (Exception e){
					e.printStackTrace();
				}

                obj.put("ret", 1);
                obj.put("msg", "上传文件成功");
            } else {
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls文件!");
            }

        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            obj.put("ret", 0);
            obj.put("msg", "导入失败");
        }
        return obj.toJSONString();
    }

	/**
     * 新版本广告配置
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/app/selectExtendAdconfig", method={RequestMethod.GET, RequestMethod.POST})
	public String selectExtendAdconfig(ExtendAdconfigVo app, HttpServletRequest request) throws IOException {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);


		Map<String, String> pageParams = BlankUtils.getParameter(request);
		JSONObject result = new JSONObject();
		try {

			PageHelper.startPage(pageParams); // 进行分页
			List<Map<String, Object>> list = adv2Service.selectDnExtendAdconfig(app);
			long size = ((Page) list).getTotal();
			/*
				广告源管理和广告配置，加上广告源状态查询、渠道在线名称，包名，产品在线状态
				1、广告源状态可以从媒体广告位状态查询拉  1启动，2暂停
				2、渠道在线名称，包名，产品在线状态可以从综合系统-渠道产品id关联配置拉
			 */
			String query = "select adsid mapkey,statu from dnwx_cfg.dn_online_adpos_status where adsid is not null and statu is not null group by adsid";
			Map<String, Map<String, Object>> statuMap = adv2Service.queryListMapOfKey(query);

			String query2 = "select CONCAT(appid,channel) mapkey,tappname,packagename,state from yyhz_0308.adv_platform_app_info where channel is not null and state is not null";
			Map<String, Map<String, Object>> platformMap = adv2Service.queryListMapOfKey(query2);

			Map<String,String> STATE_MAP = new HashMap<String,String>(){{
				put("0","未提交");
				put("1","已上线");
				put("2","已下线");
				put("3","未发布");
				put("4","自动化审核中");
				put("5","审核中");
				put("6","审核驳回");
				put("7","定时发布");
				put("8","资质审核");
				put("9","资质驳回");
				put("10","资质通过");
				put("11","已冻结");
				put("12","报备成功");
				put("13","撤销上线");
				put("14","测试中");
				put("15", "升级中");
				put("16", "下线审核");
				put("17", "未定义");
				put("18", "下线驳回");
				put("19", "审核通过");
				put("20", "运营打回");
				put("21", "运营通过");
				put("22", "其他");
			}};

			list.forEach(act -> {
				Map<String, Object> statu = statuMap.get(act.getOrDefault("adsid", "").toString());
				if(statu != null){
					act.put("adsid_statu", ("1".equals(statu.get("statu").toString())?"开启":"2".equals(statu.get("statu").toString())?"暂停":"") );
				}

				Map<String, Object> platform = platformMap.get(act.get("appid")+""+act.get("cha_id"));
				if(platform != null){
					act.put("tappname", platform.get("tappname")+"");
					act.put("packagename", platform.get("packagename")+"");
					act.put("app_statu", (STATE_MAP.get(platform.get("state").toString())!=null?STATE_MAP.get(platform.get("state").toString()):"") );
				}

				act.put("createtime", act.get("createtime")+"");
				act.put("lasttime", act.get("lasttime")+"");
			});

			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}

	/**
	 * 新版本广告配置-导出
	 * @param app
	 * @param request
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/app/exportExtendAdconfig", method={RequestMethod.GET, RequestMethod.POST})
	public void exportExtendAdconfig(ExtendAdconfigVo app, String value, HttpServletRequest request, HttpServletResponse response) throws IOException {

		try {
			List<Map<String, Object>> list = adv2Service.selectDnExtendAdconfig(app);
			/*
				广告源管理和广告配置，加上广告源状态查询、渠道在线名称，包名，产品在线状态
				1、广告源状态可以从媒体广告位状态查询拉  1启动，2暂停
				2、渠道在线名称，包名，产品在线状态可以从综合系统-渠道产品id关联配置拉
			 */
			String query = "select adsid mapkey,statu from dnwx_cfg.dn_online_adpos_status where adsid is not null and statu is not null group by adsid";
			Map<String, Map<String, Object>> statuMap = adv2Service.queryListMapOfKey(query);

			String query2 = "select CONCAT(appid,channel) mapkey,tappname,packagename,state from yyhz_0308.adv_platform_app_info where channel is not null and state is not null";
			Map<String, Map<String, Object>> platformMap = adv2Service.queryListMapOfKey(query2);

			Map<String,String> STATE_MAP = new HashMap<String,String>(){{
				put("0","未提交");
				put("1","已上线");
				put("2","已下线");
				put("3","未发布");
				put("4","自动化审核中");
				put("5","审核中");
				put("6","审核驳回");
				put("7","定时发布");
				put("8","资质审核");
				put("9","资质驳回");
				put("10","资质通过");
				put("11","已冻结");
				put("12","报备成功");
				put("13","撤销上线");
				put("14","测试中");
				put("15", "升级中");
				put("16", "下线审核");
				put("17", "未定义");
				put("18", "下线驳回");
				put("19", "审核通过");
				put("20", "运营打回");
				put("21", "运营通过");
				put("22", "其他");
			}};

			Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");

			list.forEach(act -> {
				Map<String, Object> adstatu = statuMap.get(act.getOrDefault("adsid", "").toString());
				if(adstatu != null){
					act.put("adsid_statu", ("1".equals(adstatu.get("statu").toString())?"开启":"2".equals(adstatu.get("statu").toString())?"暂停":"") );
				}

				Map<String, Object> platform = platformMap.get(act.get("appid")+""+act.get("cha_id"));
				if(platform != null){
					act.put("tappname", platform.get("tappname")+"");
					act.put("packagename", platform.get("packagename")+"");
					act.put("app_statu", (STATE_MAP.get(platform.get("state").toString())!=null?STATE_MAP.get(platform.get("state").toString()):"") );
				}
				//bidding，out转换为中文
				if (act.containsKey("bidding") && !ObjectUtils.isEmpty(act.get("bidding"))) {
					int bidding = BlankUtils.getInt(act.get("bidding"));
					act.put("bidding", bidding==0?"否":(bidding==1?"c2sbidding":(bidding==2?"s2sbidding":"")));
				}
				if (act.containsKey("out") && !ObjectUtils.isEmpty(act.get("out"))) {
					int out = BlankUtils.getInt(act.get("out"));
					act.put("out", out==0?"应用内":(out==1?"应用外":""));
				}

				// 赋值应用名称
				Map<String, Object> apps = appMap.get(act.get("appid")+"");
				if(apps != null)
					act.put("appname", apps.get("app_name"));


				int statu = BlankUtils.getInt(act.get("statu"));
				act.put("statu", statu==0?"关闭":(statu==1?"开启":""));

				String is_newuser = act.get("is_newuser")+"";
				act.put("is_newuser", "0".equals(is_newuser)?"老用户":("1".equals(is_newuser)?"新用户":"all"));

				act.put("createtime", act.get("createtime")+"");
				act.put("lasttime", act.get("lasttime")+"");
			});

			Map<String, String> headerMap = new LinkedHashMap<String, String>();
			if (!StringUtils.isNullOrEmpty(value)){
				//自定义列数据
				try {
					String[] split = value.split(";");
					for (int i = 0;i<split.length;i++) {
						String[] s = split[i].split(",");
						headerMap.put(s[0], s[1]);
					}
				}catch (Exception e) {
					e.printStackTrace();
					Asserts.fail("自定义列导出异常");
				}
			}
			ExportExcelUtil.export(response, list, headerMap, "广告配置_" + DateTime.now().toString("yyyyMMdd") + ".xls");

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
     * 新版本广告配置-新增修改操作
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/app/extendAdconfigHandle", method={RequestMethod.POST})
	public String extendAdconfigHandle(String handle, ExtendAdconfigVo app,
									   HttpServletRequest request, HttpServletResponse response) throws IOException, Exception {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// 筛除sql敏感字符注入
		if(BlankUtils.sqlValidate(app.getId())){
			return "{\"ret\":0,\"msg\":\"param is error!\"}";
		}

		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}
		app.setCuser(cuser);

		int result = 0;
		try {
			String sql = null;


			// 新增修改时，查询广告位管理相关维度配置的"广告策略"是否不一致，不一致则返回错误让页面告警
			// 过滤关闭的状态，判断配置列表中多条策略有中一条相同就行 -林小敏.20250110
			String where = " where appid=#{obj.appid} and user_group=#{obj.user_group} and adpos_type=#{obj.adpos_type} and statu=1 ";
			if(!BlankUtils.checkBlank(app.getCha_id())) {
				where += " and cha_id=#{obj.cha_id}";
			}else{
				where += " and IFNULL(cha_id,'') = '' ";
			}
			if(!BlankUtils.checkBlank(app.getPrjid())){
				where += " and prjid=#{obj.prjid}";
			}else{
				where += " and IFNULL(prjid,'') = '' ";
			}
			String checkQuery = "select * from dnwx_cfg.dn_extend_adpos_manage "+where;

			if("add".equals(handle)){

				sql = "insert into dn_extend_adconfig(appid,cha_id,prjid,is_newuser,user_group,adpos_type,strategy,adsid,statu,ecpm,priority,rate,prjid_group_id,createtime,lasttime,cuser) "+
						"values(#{obj.appid},#{obj.cha_id},#{obj.prjid},#{obj.is_newuser},#{obj.user_group},#{obj.adpos_type},#{obj.strategy},#{obj.adsid},#{obj.statu},#{obj.ecpm},#{obj.priority},#{obj.rate},#{obj.prjid_group_id},now(),now(),#{obj.cuser}) ";

			}else if("edit".equals(handle)){
				List<Map<String, Object>> checkList = adv2Service.queryListMapTwo(checkQuery, app);
				if(checkList != null && !checkList.isEmpty()){
					List<String> strategyList = checkList.stream().filter(act -> act.get("strategy") != null)
													.map(act -> act.get("strategy").toString()).collect(Collectors.toList());
					List<String> edList = Arrays.asList(app.getStrategy().split(","));
					if(!strategyList.isEmpty() && !strategyList.containsAll(edList)){
						return "{\"ret\":0,\"msg\":\"与广告位管理相同维度的广告策略不一致，请检查!\"}";
					}
				}

				sql = "update dn_extend_adconfig set appid=#{obj.appid},cha_id=#{obj.cha_id},prjid=#{obj.prjid},is_newuser=#{obj.is_newuser},user_group=#{obj.user_group},adpos_type=#{obj.adpos_type},strategy=#{obj.strategy},statu=#{obj.statu},adsid=#{obj.adsid},ecpm=#{obj.ecpm},priority=#{obj.priority},rate=#{obj.rate},prjid_group_id=#{obj.prjid_group_id},lasttime=now(),cuser=#{obj.cuser} "+
						"where id = #{obj.id} ";

			}else if("del".equals(handle)){
				sql = "delete from dn_extend_adconfig where id in ("+app.getId()+")";

				/* 增加海外库的删除操作执行 */
//				try{
//					String sql2 = "delete from dn_extend_adconfig where id in ("+app.getId()+")";
//					haiwaiCfgMapper.execSql(sql2);
//				} catch (Exception e) {
//					e.printStackTrace();
//				}
			}else if("editStatu".equals(handle)){
				sql = "update dn_extend_adconfig set statu=#{obj.statu},lasttime=now(),cuser=#{obj.cuser} where id in ("+app.getId()+")";
			}

			result = adv2Service.execSqlHandle(sql, app);
			if(result > 0) {
				/* 刷新广告配置的缓存 */
				adv2Service.recacheDnExtendAd("300", app.getAppid());
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			}else{
					return "{\"ret\":0,\"msg\":\"无效操作!\"}";
			}
		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	/**
	 * 渠道广告自动提审过审-新增修改操作
	 * @param app
	 * @param request
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/wbgui/extendAdconfigHandle", method={RequestMethod.POST})
	public String wbguiextendAdconfigHandle(String handle, String app_category, String isTuiGuang, ExtendAdconfigVo app,
									   HttpServletRequest request, HttpServletResponse response) throws IOException, Exception {

		// token验证
//		String token = request.getParameter("token");
//		if(BlankUtils.checkBlank(token) || !"".equalsIgnoreCase(token))
//			return "{\"ret\":2,\"msg\":\"token is error!\"}";
//		else
//			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		boolean isTool = false;
		if("52".equals(app_category) || "48".equals(app_category)){
			isTool = true;
		}

		int result = 0;
		try {
			if("add".equals(handle)){
				String query = "select * from dn_extend_adconfig where appid=#{obj.appid} and cha_id=#{obj.cha_id} and ifnull(prjid,'')='' and adpos_type='video' and statu=1 and user_group='all' limit 1";
				if(isTool){
					// 工具E组产品做兼容处理
					query = "select * from dn_extend_adconfig where appid=#{obj.appid} and ifnull(cha_id,'')='' and ifnull(prjid,'')='' and adpos_type='video' and statu=1 and user_group='all' limit 1";
				}
				List<Map<String, Object>> list = adv2Service.queryListMapTwo(query, app);
				if(list != null && !list.isEmpty()){
					// 默认使用列表中第一条数据
					Map<String, Object> objectMap = list.get(0);

					// 为huawei渠道时，遍历list列表，获取adsid为Huawei_开头的那一条数据对象返回
					if("huawei".equals(app.getCha_id())) {
						Map<String, Object> total = list.stream().filter(act -> {
							return (act.get("adsid") != null && (act.get("adsid") + "").startsWith("Huawei_"));
						}).findFirst().orElse(null);

						if(total != null) {
							objectMap = total;
						}else {
							return "{\"ret\":0,\"msg\":\"操作失败：在广告配置中未匹配到该提审产品+子渠道的配置信息!\"}";
						}
					}

					// 当渠道为OPPO渠道推广的时候，在审核中的状态自动创建一条项目ID的视频广告，但是广告源后缀带上9999，从而让这条配置失效 -孙文凤.20240507
					if("1".equals(isTuiGuang)){
						objectMap.put("adsid", objectMap.get("adsid")+"_9999");
					}
					// 当配置来源为工具E组产品时，广告源后缀带上111，从而让这条配置失效 -孙文凤.20241208
					if(isTool){
						objectMap.put("adsid", objectMap.get("adsid")+"_111");
					}


					// 当为以下渠道时，广告策略要使用的和产品+子渠道维度一致 -林小敏.2024718
					// huawei、huawei2、huaweiml、huaweiml2、honor、honor2
					if("huawei".equals(app.getCha_id()) || "huawei2".equals(app.getCha_id()) || "huaweiml".equals(app.getCha_id()) || "huaweiml2".equals(app.getCha_id()) || "honor".equals(app.getCha_id()) || "honor2".equals(app.getCha_id())){
						objectMap.put("strategy", objectMap.get("strategy")+"");
					}else{
						objectMap.put("strategy", "video_waterfall");
					}

					objectMap.put("prjid", app.getPrjid());
					objectMap.put("cuser", "wbgui");

					String sql = "insert into dn_extend_adconfig(appid,cha_id,prjid,is_newuser,user_group,adpos_type,strategy,adsid,statu,ecpm,priority,rate,createtime,lasttime,cuser) "+
							"values(#{obj.appid},#{obj.cha_id},#{obj.prjid},#{obj.is_newuser},#{obj.user_group},#{obj.adpos_type},#{obj.strategy},#{obj.adsid},#{obj.statu},#{obj.ecpm},#{obj.priority},#{obj.rate},now(),now(),#{obj.cuser}) ";
					result = adv2Service.execSqlHandle(sql, objectMap);
				}else{
					return "{\"ret\":0,\"msg\":\"操作失败：在广告配置中未匹配到该提审产品+子渠道的配置信息!\"}";
				}

			}else if("del".equals(handle)){

				if(isTool){
					String sql = "delete from dn_extend_adconfig where appid=#{obj.appid} and prjid=#{obj.prjid} and adpos_type='video' and cuser='wbgui' ";
					adv2Service.execSqlHandle(sql, app);

					return "{\"ret\":1,\"msg\":\"操作成功!\"}";
				}else {
					/* 判断该维度子渠道+项目ID只有一条video数据的话 进行删除，否则不处理返回成功 */
					String query = "select * from dn_extend_adconfig where appid=#{obj.appid} and cha_id=#{obj.cha_id} and prjid=#{obj.prjid} and adpos_type='video' ";
					List<Map<String, Object>> configList = adv2Service.queryListMapTwo(query, app);

					if (configList != null) {
						String sql = "delete from dn_extend_adconfig where appid=#{obj.appid} and cha_id=#{obj.cha_id} and prjid=#{obj.prjid} and adpos_type='video' and cuser='wbgui' ";
						adv2Service.execSqlHandle(sql, app);

						return "{\"ret\":1,\"msg\":\"操作成功!\"}";
					} else {
						return "{\"ret\":1,\"msg\":\"操作成功!\"}";
					}
				}

			}


			if(result > 0) {
				/* 刷新广告配置的缓存 */
				adv2Service.recacheDnExtendAd("300", app.getAppid());
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			}else {
				return "{\"ret\":0,\"msg\":\"操作失败，未找到操作的配置信息！\"}";
			}

		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败，出现异常:"+e.getMessage()+"\"}";
		}
	}

	/**
     * 新版本广告配置-批量复制操作
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/app/batchCopyExtendAdconfig", method={RequestMethod.POST})
	public String batchCopyExtendAdconfig(String handle,
										  // recache_appid判断为null时使用默认值
										  @RequestParam(value = "recache_appid", defaultValue = "13665") String recache_appid,
										  HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}

		int result = 0;
		try {
			// 替换广告源,替换所属应用,替换新老用户,替换用户群,替换项目ID,替换子渠道,替换投放渠道,替换投放账户
			String new_adsid = request.getParameter("new_adsid");
			String new_appid = request.getParameter("new_appid");
			String new_is_newuser = request.getParameter("new_is_newuser");
			String new_user_group = request.getParameter("new_user_group");
			String new_prjid = request.getParameter("new_prjid");
			String new_cha_id = request.getParameter("new_cha_id");
			String new_strategy = request.getParameter("new_strategy");

			String ids = request.getParameter("ids");
			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(ids)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}

			if(!BlankUtils.checkBlank(ids)){
				String sql = "select * from dn_extend_adconfig where id in ("+ids+")";
				List<Map<String, Object>> list = adv2Service.queryListMap(sql);
				list.forEach(act -> {
					// 替换内容
					if(!BlankUtils.checkBlank(new_adsid))
						act.put("adsid", new_adsid);
					if(!BlankUtils.checkBlank(new_appid))
						act.put("appid", new_appid);
					if(!BlankUtils.checkBlank(new_is_newuser))
						act.put("is_newuser", new_is_newuser);
					if(!BlankUtils.checkBlank(new_user_group))
						act.put("user_group", new_user_group);
					if(!BlankUtils.checkBlank(new_strategy))
						act.put("strategy", new_strategy);

					// 取消参数的替换排它性，
					if(new_prjid != null){
						act.put("prjid", new_prjid);
					}
					if(new_cha_id != null){
						act.put("cha_id", new_cha_id);
					}

				});
				try {
					Map<String, Object> paramMap = new HashMap<String, Object>();
					paramMap.put("sql1", "insert into dn_extend_adconfig(appid,cha_id,prjid,is_newuser,user_group,adpos_type,strategy,adsid,statu,ecpm,priority,rate,prjid_group_id,createtime,lasttime,cuser) values ");
					paramMap.put("sql2", " (#{li.appid},#{li.cha_id},#{li.prjid},#{li.is_newuser},#{li.user_group},#{li.adpos_type},#{li.strategy},#{li.adsid},#{li.statu},#{li.ecpm},#{li.priority},#{li.rate},#{li.prjid_group_id},now(),now(),'"+cuser+"') ");
					paramMap.put("sql3", " ");
					paramMap.put("list", list);
					adv2Service.batchExecSql(paramMap);

					result = 1;
				} catch (Exception e) {
					e.printStackTrace();
				}
			}


			if(result > 0) {
				/* 刷新广告配置的缓存 */
				adv2Service.recacheDnExtendAd("300", recache_appid);
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			}else {
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";
			}
		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	/**
     * 新版本广告配置-批量修改操作
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/app/batchEditExtendAdconfig", method={RequestMethod.POST})
	public String batchEditExtendAdconfig(String handle,
										  @RequestParam(value = "recache_appid", defaultValue = "13665") String recache_appid,
										  HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}

		int result = 0;
		try {
			// 旧广告源关键字，替换广告源关键字，广告策略，广告类型，预估ecpm，分配比例，用户群，项目ID，子渠道
			String old_adsid = request.getParameter("old_adsid");
			String new_adsid = request.getParameter("new_adsid");
			String new_strategy = request.getParameter("new_strategy");
			String new_adpos_type = request.getParameter("new_adpos_type");
			String new_ecpm = request.getParameter("new_ecpm");
			String new_rate = request.getParameter("new_rate");
			String new_is_newuser = request.getParameter("new_is_newuser");
			String new_user_group = request.getParameter("new_user_group");
			String new_prjid = request.getParameter("new_prjid");
			String new_cha_id = request.getParameter("new_cha_id");

			String ids = request.getParameter("ids");
			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(ids)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}

			String set = "lasttime=now(),cuser='"+cuser+"'";
			if(!BlankUtils.checkBlank(new_adsid))
				set += ",adsid=REPLACE(adsid,'"+old_adsid+"','"+new_adsid+"') ";
			if(!BlankUtils.checkBlank(new_ecpm))
				set += ",ecpm="+new_ecpm;
			if(!BlankUtils.checkBlank(new_rate))
				set += ",rate="+new_rate;
			if(!BlankUtils.checkBlank(new_strategy))
				set += ",strategy='"+new_strategy+"'";
			if(!BlankUtils.checkBlank(new_adpos_type))
				set += ",adpos_type='"+new_adpos_type+"'";
			if(!BlankUtils.checkBlank(new_is_newuser))
				set += ",is_newuser='"+new_is_newuser+"'";
			if(!BlankUtils.checkBlank(new_user_group))
				set += ",user_group='"+new_user_group+"'";

			if(new_prjid != null)
				set += ",prjid='"+new_prjid+"'";
			if(new_cha_id != null)
				set += ",cha_id='"+new_cha_id+"'";

			if(!BlankUtils.checkBlank(ids)){

				String sql = "update dn_extend_adconfig set "+set+" where id in ("+ids+")";
				result = adv2Service.execSql(sql);
			}


			if(result > 0) {
				/* 刷新广告配置的缓存 */
				adv2Service.recacheDnExtendAd("300", recache_appid);
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			}else {
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";
			}

		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	/**
	 * 新版本广告配置-同步至广告位
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/app/extendAdconfigSync", method={RequestMethod.POST})
	public String extendAdconfigSync(HttpServletRequest request,HttpServletResponse response) throws IOException {

		String[] args = {"appid","cha_id","prjid","user_group","ischeck",
				"splash_use_strategy","banner_use_strategy","video_use_strategy","plaque_use_strategy","msg_use_strategy","icon_use_strategy"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);
		paramMap.put("cuser", cuser.getLogin_name());

		/** 查询条件 */
		String where = " where statu=1 and appid=#{obj.appid} and user_group=#{obj.user_group} ";
		if(!BlankUtils.checkBlank(paramMap.get("cha_id"))) {
			where += " and cha_id=#{obj.cha_id}";
		}else{
			where += " and (cha_id = '' or cha_id is null)";
		}
		if(!BlankUtils.checkBlank(paramMap.get("prjid"))){
			where += " and prjid=#{obj.prjid}";
		}else{
			where += " and (prjid = '' or prjid is null)";
		}


		/** 检验数据来源纬度是否有数据 */
		String query = "select adpos_type,strategy from dn_extend_adconfig " +where+
				" group by appid,cha_id,prjid,user_group,adpos_type ";
		List<Map<String, Object>> list = adv2Service.queryListMapTwo(query, paramMap);
		if (list == null || list.isEmpty()){
			return "{\"ret\":0,\"msg\":\"操作失败，同步的来源维度数据为空，请检查后重试!\"}";
		}

		if(BlankUtils.checkBlank(paramMap.get("ischeck"))){

			/** 检验产品维度数据是否存在 */
			String query1 = "select * from dnwx_cfg.dn_extend_adpos_manage ";
			query1 += where;

			List<Map<String, Object>> adposList = adv2Service.queryListMapTwo(query1, paramMap);
			if(adposList != null && adposList.size() > 0){
				return "{\"ret\":3,\"msg\":\"该产品维度数据已存在，确认是否需要覆盖？\"}";
			}
		}

		if(BlankUtils.checkBlank(paramMap.get("splash_use_strategy")) &&
				BlankUtils.checkBlank(paramMap.get("banner_use_strategy")) &&
				BlankUtils.checkBlank(paramMap.get("video_use_strategy")) &&
				BlankUtils.checkBlank(paramMap.get("plaque_use_strategy")) &&
				BlankUtils.checkBlank(paramMap.get("msg_use_strategy")) &&
				BlankUtils.checkBlank(paramMap.get("icon_use_strategy")) ){


			/** 检验广告配置中维度数据是否plaque有多个策略 */
			String query2 = "select adpos_type,strategy from dnwx_cfg.dn_extend_adconfig ";
			query2 += where;
			query2 += " group by adpos_type,strategy ";
			List<Map<String, Object>> adconfigList = adv2Service.queryListMapTwo(query2, paramMap);
			Map<String, List<String>> adposType = adconfigList.stream().collect(Collectors.groupingBy(act -> act.get("adpos_type").toString(), Collectors.mapping(act -> act.get("strategy").toString(), Collectors.toList()) ));


			Map<String, List<String>> collect = adposType.entrySet().stream().filter(entry -> entry.getValue().size() > 1).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
			if(collect != null && !collect.isEmpty()){

				JSONObject result = new JSONObject();
				result.put("ret", 5);
				result.put("msg", "该产品维度广告配置已存在多个策略，需要选择使用哪种策略覆盖？");
				result.put("strategy", collect);
				return result.toJSONString();
			}
		}
		return ReturnJson.success();
	}

	/**
	 * 新版本广告配置-实际同步至广告位
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@ApiOperation(value = "新版本广告配置-实际同步至广告位", notes = "新版本广告配置-实际同步至广告位")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "data", value = "json array", dataType = "json array")
	})
	@PostMapping(value="/app/doExtendAdconfigSync")
	public String doExtendAdconfigSync(HttpServletRequest request,HttpServletResponse response) throws IOException {
		// token验证
		String token = request.getHeader("Adadmin-Token");
		if (org.apache.commons.lang3.StringUtils.isBlank(token)) {
			token = request.getParameter("token");
		}
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		}
		else {
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		}
		CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);

//		String[] args = {"appid","cha_id","prjid","user_group","ischeck",
//				"splash_use_strategy","banner_use_strategy","video_use_strategy","plaque_use_strategy","msg_use_strategy","icon_use_strategy"};
//		Map<String, String> paramMap = BlankUtils.getParameter(request, args);

//		String list = request.getParameter("list");
//		JSONArray data = JSONObject.parseArray(list);
		JSONObject jsonRequest = getJsonRequest(request);
		JSONArray data = jsonRequest.getJSONArray("data");
		StringBuilder sb = new StringBuilder();
		data.stream().map(o -> (JSONObject) o).forEach(map -> {
			map.put("cuser", cuser.getLogin_name());
			HashMap<String, String> paramMap = new HashMap<>();
			map.forEach((k,v) -> paramMap.put(k, v.toString()));

			try {
				if("1".equals(map.get("ischeck"))){
					// 按照维度+广告位类型更新对应的广告策略
					String result = adv2Service.updateAdposFromAdconfig(paramMap);
					sb.append("appid:").append(map.getString("appid"))
							.append("cha id:").append(map.getString("cha_id"))
							.append("group:").append(map.getString("user_group"))
							.append(result).append("<br>");

				}else{
					// 写入对应维度的广告位配置
					String result = adv2Service.insertAdposFromAdconfig(paramMap);
					sb.append("appid:").append(map.getString("appid"))
							.append("cha id:").append(map.getString("cha_id"))
							.append("group:").append(map.getString("user_group"))
							.append(result).append("<br>");
				}

			} catch (Exception e) {
				sb.append("appid:").append(map.getString("appid"))
						.append("cha id:").append(map.getString("cha_id"))
						.append("group:").append(map.getString("user_group"))
						.append("操作异常").append("<br>");
				logger.error("同步广告位失败, ", e);
			}
		});
		return ReturnJson.success(sb.toString());
	}


	/**
     * 用户策略配置-查询
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/app/selectUserStrategy", method={RequestMethod.GET, RequestMethod.POST})
	public String selectUserStrategy(UserStrategyVo app, HttpServletRequest request) throws IOException {

		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		try {
			String sql = "select * from dn_extend_user_strategy where 1=1";
			if(!BlankUtils.checkBlank(app.getSid()))
				sql += " and sid like concat('%',#{obj.sid},'%')";
			if(!BlankUtils.checkBlank(app.getBrand()))
				sql += " and brand = #{obj.brand}";
			if(!BlankUtils.checkBlank(app.getBuy_act()))
				sql += " and buy_act = #{obj.buy_act}";
			if(!BlankUtils.checkBlank(app.getAndroid_ver()))
				sql += " and android_ver = #{obj.android_ver}";
			if(!BlankUtils.checkBlank(app.getDevice_type()))
				sql += " and device_type = #{obj.device_type}";
			if(!BlankUtils.checkBlank(app.getAction()))
				sql += " and action = #{obj.action}";
			if(!BlankUtils.checkBlank(app.getStatu()))
				sql += " and statu = #{obj.statu}";

			sql = sql + " order by createtime desc";

			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<Map<String, Object>> list = adv2Service.queryListMapTwo(sql, app);
			long size = ((Page) list).getTotal();

			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();

			if(e.toString().contains("Duplicate entry")) {
				return "{\"ret\":0,\"msg\":\"已有相同策略ID存在!\"}";
			}
			return "{\"ret\":0,\"msg\":\"操作失败，错误信息 : "+e.getMessage()+"\"}";
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}

	/**
     * 用户策略配置-新增修改删除操作
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/app/userStrategyHandle", method={RequestMethod.POST})
	public String userStrategyHandle(String handle, UserStrategyVo app,
									 HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}
		app.setEuser(cuser);

		int result = 0;
		try {
			String sql = null;
			if("add".equals(handle)){
				sql = "insert into dn_extend_user_strategy(sid,user_rate,brand,citycode,buy_act,android_ver,device_type,action,action_type,action_val,statu,createtime,euser,endtime) "+
						"values(#{obj.sid},#{obj.user_rate},#{obj.brand},#{obj.citycode},#{obj.buy_act},#{obj.android_ver},#{obj.device_type},#{obj.action},#{obj.action_type},#{obj.action_val},#{obj.statu},now(),#{obj.euser},now()) ";

			}else if("edit".equals(handle)){
				sql = "update dn_extend_user_strategy set user_rate=#{obj.user_rate},brand=#{obj.brand},citycode=#{obj.citycode},buy_act=#{obj.buy_act},android_ver=#{obj.android_ver},device_type=#{obj.device_type}, "+
						"action=#{obj.action},action_type=#{obj.action_type},action_val=#{obj.action_val},statu=#{obj.statu},euser=#{obj.euser} "+
						"where sid = #{obj.sid} ";

			}else if("del".equals(handle)){
				sql = "delete from dn_extend_user_strategy where sid = #{obj.sid}";

			}

			result = adv2Service.execSqlHandle(sql, app);
			if(result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";

		} catch (Exception e) {
			e.printStackTrace();
			if(e.toString().indexOf("Duplicate entry")>-1) {
				return "{\"ret\":0,\"msg\":\"已有相同策略id存在!\"}";
			}
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}


	/**
     * 广告策略设置-查询
     * @param record
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value = "/app/selectExtendAdinfoVo", method={RequestMethod.GET,RequestMethod.POST})
	public String selectExtendAdinfoVo(ExtendAdinfoVo record,HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		try {

			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<ExtendAdinfoVo> list = adv2Mapper.selectExtendAdinfoVo(record);
			long size = ((Page) list).getTotal();
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: " + e.getMessage());
		}
		return result.toJSONString();
	}


	/**
     * 广告策略设置-新增修改删除操作
     * @param record
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value = "/app/extendAdinfoVoHandle", method={RequestMethod.GET,RequestMethod.POST})
	public String extendAdinfoVoHandle(ExtendAdinfoVo record ,HttpServletRequest request, HttpServletResponse response) {
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);


		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}
		record.setCuser(cuser);
		record.setEuser(cuser);
		int result = 0;
		try {


			if ("add".equals(request.getParameter("handle"))) {

				String sql =  "select * from dn_extend_adinfo_manage where (appid=#{obj.appid} and cha_id=#{obj.cha_id} and cha_id != '') or prjid=#{obj.prjid}";
				List<Map<String, Object>> list = adv2Mapper.queryListMapTwo(sql, record);
				if(list != null && !list.isEmpty()){
					return "{\"ret\":2,\"msg\":\"已存在相同渠道或项目ID配置了该数据!\"}";
				}

				result = adv2Mapper.insertExtendAdinfoVo(record);
			} else if ("edit".equals(request.getParameter("handle"))) {

				result = adv2Mapper.updateExtendAdinfoVo(record);
			} else if ("del".equals(request.getParameter("handle"))) {
				result = adv2Mapper.deleteExtendAdinfoVo(record);
			} else if ("batch".equals(request.getParameter("handle"))) {
				result = adv2Mapper.updateBatchExtendAdinfoVo(record);
			}

			if (result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";

		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	/**
     * 广告策略设置-批量修改操作
     * @param app
     * @param request
     * @return
     * @throws IOException
     */
	@RequestMapping(value="/app/batchEditExtendAdinfoVo", method={RequestMethod.POST})
	public String batchEditExtendAdinfoVo(String handle, ExtendAdconfigVo app,
										  HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}

		int result = 0;
		try {
			// 新展示策略、新屏蔽策略、id集合
			String new_policy_name = request.getParameter("new_policy_name");
			String new_shield_name = request.getParameter("new_shield_name");
			String ids = request.getParameter("id");
			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(ids)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}

			String set = "endtime=now(),euser='"+cuser+"'";
			if(!BlankUtils.checkBlank(new_policy_name))
				set += ",policy_name='"+new_policy_name+"'";
			if(!BlankUtils.checkBlank(new_shield_name))
				set += ",shield_name='"+new_shield_name+"'";

			String sql = "update dn_extend_adinfo_manage set "+set+" where id in ("+ids+")";
			result = adv2Service.execSql(sql);

			if(result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";

		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	/**
	 *  屏蔽策略命名 查询
	 *
	 * @param record
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/app/selectExtendAdshieldVo", method={RequestMethod.GET,RequestMethod.POST})
	public String selectExtendAdshieldVo(ExtendAdshieldVo record,HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		try {

			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<ExtendAdshieldVo> list = adv2Mapper.selectExtendAdshieldVo(record);
			long size = ((Page) list).getTotal();
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: " + e.getMessage());
		}
		return result.toJSONString();
	}


	/**
	 *   屏蔽策略命名 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/app/extendAdshieldVoHandle", method={RequestMethod.GET,RequestMethod.POST})
	public String extendAdshieldVoHandle(ExtendAdshieldVo record ,HttpServletRequest request, HttpServletResponse response) {
		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		int result = 0;
		try {

			if ("add".equals(request.getParameter("handle"))) {
				result = adv2Mapper.insertExtendAdshieldVo(record);
			} else if ("edit".equals(request.getParameter("handle"))) {
				result = adv2Mapper.updateExtendAdshieldVo(record);
			} else if ("del".equals(request.getParameter("handle"))) {
				result = adv2Mapper.deleteExtendAdshieldVo(record);
			}

			if (result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";

		} catch (Exception e) {
			e.printStackTrace();

			if(e.toString().contains("Duplicate entry")) {
				return "{\"ret\":0,\"msg\":\"已有相同策略名存在!\"}";
			}
			return "{\"ret\":0,\"msg\":\"操作失败，错误信息 : "+e.getMessage()+"\"}";
		}
	}

	@RequestMapping(value = "/app/getExtendList", method={RequestMethod.GET,RequestMethod.POST})
	public String getExtendList(HttpServletRequest request, HttpServletResponse response) {
		JSONObject result = new JSONObject();

		result.put("ret", 1);
		result.put("policy_name", adv2Mapper.selectPolicyName());
		result.put("shield_name", adv2Mapper.selectShieldName());

		return result.toJSONString();
	}

	/**
	 *  广告展示间隔 查询
	 *
	 * @param record
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/app/selectExtendAdtsVo", method={RequestMethod.GET,RequestMethod.POST})
	public String selectExtendAdtsVo(ExtendAdtsVo record,HttpServletRequest request, HttpServletResponse response) {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		try {

			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<ExtendAdtsVo> list = adv2Mapper.selectExtendAdtsVo(record);
			long size = ((Page) list).getTotal();
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: " + e.getMessage());
		}
		return result.toJSONString();
	}


	/**
	 *   广告展示间隔 操作
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/app/extendAdtsVoHandle", method={RequestMethod.GET,RequestMethod.POST})
	public String extendAdtsVoHandle(ExtendAdtsVo record ,HttpServletRequest request, HttpServletResponse response) {

		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		int result = 0;
		try {

			if ("add".equals(request.getParameter("handle"))) {
				result = adv2Mapper.insertExtendAdtsVo(record);
			} else if ("edit".equals(request.getParameter("handle"))) {
				result = adv2Mapper.updateExtendAdtsVo(record);
			} else if ("del".equals(request.getParameter("handle"))) {
				result = adv2Mapper.deleteExtendAdtsVo(record);
			}

			if (result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";

		} catch (Exception e) {
			e.printStackTrace();

			if(e.toString().contains("Duplicate entry")) {
				return "{\"ret\":0,\"msg\":\"已有相同策略名存在!\"}";
			}
			return "{\"ret\":0,\"msg\":\"操作失败，错误信息 : "+e.getMessage()+"\"}";
		}
	}

	/**
	 * 用户流量策略配置-查询v2
	 * @param app
	 * @param request
	 * @return
	 * @throws IOException
	 */
	@Deprecated
	@RequestMapping(value="/app/selectUserStrategyV2", method={RequestMethod.GET, RequestMethod.POST})
	public String selectUserStrategyV2(UserStrategyV2Vo app, HttpServletRequest request) throws IOException {

		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		JSONObject result = new JSONObject();
		try {

			Map map = new HashMap();
			map.put("sid",app.getSid());
			map.put("group",app.getGroup());
			map.put("stype",app.getStype());
			map.put("param1",app.getParam1());
			map.put("param2",app.getParam2());
			map.put("param3",app.getParam3());

			List<UserStrategyV2Vo> configList = adv2Service.getUserStrategyConfigList(map);
			Map<String,List<UserStrategyV2Vo>> configMap = configList.stream().collect(Collectors.groupingBy(t->t.getSid()+"_"+t.getGroup()+"_"+t.getStype()));
			List<JSONObject> dataList = new ArrayList<>();
			for (Map.Entry<String,List<UserStrategyV2Vo>> each:configMap.entrySet()){
				JSONObject data = new JSONObject();
				String[] key = each.getKey().split("_");
				data.put("sid",key[0]);
				data.put("group",key[1]);
				data.put("stype",key[2]);
				List<UserStrategyV2Vo> list = each.getValue();
				if (list.size()>0){
					UserStrategyV2Vo first =list.get(0);
					JSONArray paramsArray = new JSONArray();
					for(UserStrategyV2Vo vo:list){
						JSONObject json = new JSONObject();
						json.put("param1",vo.getParam1());
						json.put("param2",vo.getParam2());
						json.put("param3",vo.getParam3());
						json.put("id",vo.getId());
						paramsArray.add(json);
					}
					data.put("params",paramsArray);
					data.put("cuser",first.getCuser());
					data.put("euser",first.getEuser());
					data.put("createtime",first.getCreatetime());
					data.put("note",first.getNote());
					data.put("endtime",first.getEndtime());
					dataList.add(data);
				}
			}
			//排序
			Collections.sort(dataList, new Comparator<Object>() {
				@Override
				public int compare(Object a, Object b) {
					JSONObject jsonA = (JSONObject) a;
					JSONObject jsonB = (JSONObject) b;
					String keyA = jsonA.getString("sid")+jsonA.getString("group");
					String keyB = jsonB.getString("sid")+jsonB.getString("group");

					if (keyA.compareTo(keyB)>0){
						return 1;
					}else if (keyA.compareTo(keyB)==0){
						return 0;
					}else {
						return -1;
					}
				}
			});
			result.put("ret", 1);
			result.put("data", dataList);
		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败，错误信息 : "+e.getMessage()+"\"}";
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}

	/**
	 * 用户流量策略配置-查询v3
	 * @param app
	 * @param request
	 * @return
	 * @throws IOException
	 */
	@ControllerLoggingEnhancer
	@RequestMapping(value="/app/selectUserStrategyV3", method={RequestMethod.GET, RequestMethod.POST})
	public String selectUserStrategyV3(UserStrategyV2Vo app, HttpServletRequest request) throws IOException {

		PageHelper.startPage(app.getStart(), app.getLimit());
		return ReturnJson.success(adv2Service.getUserStrategyConfigList(app));
	}

	/**
	 * 用户流量策略设置置-新增修改删除操作v2
	 * @param app
	 * @param request
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/app/userStrategyHandleV2", method={RequestMethod.POST})
	public String userStrategyHandleV2(String handle, UserStrategyV2Vo app,
									   HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}
		app.setEuser(cuser);
		app.setCuser(cuser);

		int result = 0;
		try {
			String sql = null;
			if("add".equals(handle)){
				//判断sid 与group是否存在
				String exitsSql = "select * from dn_extend_user_strategy_two where sid ='"+app.getSid()+"' and `group` = '"+app.getGroup()+"' " ;
				List<Map<String, Object>> list = adv2Service.queryListMapTwo(exitsSql, app);
				if (list!=null&&list.size()>0){
					return ReturnJson.toErrorJson("已经存在策略id+组名相同的配置");
				}
				String params = request.getParameter("params");
				JSONArray paramsArray = JSONArray.parseArray(params);

				List<UserStrategyV2Vo> list1 = new ArrayList<>();
				for(Object obj:paramsArray){
					UserStrategyV2Vo config = new UserStrategyV2Vo();
					JSONObject json = (JSONObject) obj;
					config.setSid(app.getSid());
					config.setGroup(app.getGroup());
					config.setStype(app.getStype());
					config.setUser_rate(app.getUser_rate());
					config.setNote(app.getNote());
					config.setCuser(app.getCuser());
					config.setEuser(app.getEuser());
					config.setParam1(json.getString("param1"));
					config.setParam2(json.getString("param2"));
					config.setParam3(json.getString("param3"));
					list1.add(config);
				}
				String sql1 = "insert into dn_extend_user_strategy_two(sid,`group`,stype,note,param1,param2,param3,cuser,createtime) "+
						" values ";
				String sql2 = "(#{li.sid},#{li.group},#{li.stype},#{li.note},#{li.param1},#{li.param2},#{li.param3},#{li.cuser},now())";
				String sql3 = "";
				Map<String,Object> paramMap =new HashMap<>();
				paramMap.put("sql1",sql1);
				paramMap.put("sql2",sql2);
				paramMap.put("sql3",sql3);
				paramMap.put("list",list1);
				result = adv2Service.batchExecSql(paramMap);
			}else if("edit".equals(handle)){
				String params = request.getParameter("params");
				JSONArray paramsArray = JSONArray.parseArray(params);
				List<UserStrategyV2Vo> list1 = new ArrayList<>();
				for(Object obj:paramsArray){
					UserStrategyV2Vo config = new UserStrategyV2Vo();
					JSONObject json = (JSONObject) obj;
					config.setSid(app.getSid());
					config.setGroup(app.getGroup());
					config.setStype(app.getStype());
					config.setNote(app.getNote());
					config.setCuser(app.getCuser());
					config.setEuser(app.getEuser());
					config.setId(json.getString("id"));
					config.setParam1(json.getString("param1"));
					config.setParam2(json.getString("param2"));
					config.setParam3(json.getString("param3"));
					list1.add(config);
				}
				logger.error("userStrategyHandleV2 edit:"+ JSON.toJSONString(list1));

				result = adv2Service.updateUserStrategyConfig(list1,app);
			}else if ("del".equals(handle)){
				if (app==null||BlankUtils.checkBlank(app.getGroup())||BlankUtils.checkBlank(app.getSid())){
					return "{\"ret\":0,\"msg\":\"无效操作!\"}";
				}
				sql = " delete from dn_extend_user_strategy_two where sid='"+app.getSid()+"' and `group`='"+app.getGroup()+"'";
				result = adv2Service.execSql(sql);
			}


			if(result > 0) {
				/* 公共刷新缓存 */
				try {
					Map<String, String> info = new HashMap<String, String>(){{
						put("mark", "newWjy");
						put("mapid", "304");
						put("appid", null);
						put("title", "用户群管理(新)");
						put("cuser", app.getCuser());
					}};
					adService.commonRecacheService(info);
				}catch (Exception e){
					e.printStackTrace();
				}

				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			}else {
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";
			}

		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	/**
	 * 用户流量策略设置-清理用户群分组操作
	 * @param app
	 * @param request
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/app/userGroupHandle", method={RequestMethod.POST})
	public String userGroupHandle(String handle, UserStrategyV2Vo app,
								  HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

		String REDIS_HASH_USER_STRATEGY = "dnwx_user_strategy_hash";

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		String appid = BlankUtils.checkNull(request, "appid");
		String cha_id = BlankUtils.checkNull(request, "cha_id");
		String prjid = BlankUtils.checkNull(request, "prjid");
		String mark = BlankUtils.checkNull(request, "mark"); // 安卓为android_id，IOS为idfa
		String usergroup = BlankUtils.checkNull(request, "usergroup");

		int result = 0;
		try {
			if("del".equals(handle)){
				redisTemplate.delete(REDIS_HASH_USER_STRATEGY+appid+cha_id+prjid+mark);
			}else{
				redisTemplate.opsForHash().put(REDIS_HASH_USER_STRATEGY+appid+cha_id+prjid+mark, "sid", usergroup);
			}

			if(result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";

		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败!\"}";
		}
	}

	/**
	 * 广告源批量导入
	 * @param file
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("app/batchImportAdsid")
	public Object batchImportAdsid(@RequestParam(value = "fileName") MultipartFile file,@RequestParam(value = "token") String token,
								   @RequestParam(value = "isSyncAdpos") Boolean isSyncAdpos, HttpServletRequest request, HttpServletResponse response){
		TreeMap<String,String> retMap = new TreeMap<>();
		try {
			String username = "";
			CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
			if (currUserVo != null) {
				username = currUserVo.getLogin_name();
			}
			if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {

				/* 广告源列表 */
				String query = "select adsid,sdk_code as mapkey from dnwx_cfg.dn_extend_adsid_manage GROUP BY sdk_code";
				Map<String, Map<String, Object>> adsidMap = adv2Service.queryListMapOfKey(query);
				/* 平台列表 */
				String query2 = "SELECT DISTINCT platform FROM yyhz_0308.dn_ad_cash_paltform ORDER BY platform";
				List<String> agentList = adv2Service.queryListString(query2);

				TreeMap<String,ExtendAdsidVo> adSidList = new TreeMap<>();
				TreeMap<String,ExtendAdconfigVo> adConfigList = new TreeMap<>();
				Workbook workbook = Workbook.getWorkbook(file.getInputStream());
				Sheet sheet = workbook.getSheet(0);

				int column = sheet.getColumns();
				int row = sheet.getRows();
				for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
					if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
						continue;
					try {
						ExtendAdsidVo ki = new ExtendAdsidVo();
						ExtendAdconfigVo adConfig = new ExtendAdconfigVo();
						String[] vals = new String[column];
						for (int c = 0; c < column; c++) {
							vals[c] = sheet.getCell(c, r).getContents();
						}

						// 验证参数有效性
						if(!BlankUtils.isNumeric(vals[9])){
							return ReturnJson.toErrorJson("第"+r+"行配置所属应用错误,要求为数值类型");
						}
						if(!BlankUtils.isNumeric(vals[14])){
							return ReturnJson.toErrorJson("第"+r+"行配置预估ecpm错误,要求为正的数值");
						}
						if(!BlankUtils.isNumeric(vals[15])){
							return ReturnJson.toErrorJson("第"+r+"行配置分配比例错误,要求为正的数值");
						}
						// 验证传入的平台参数必须为平台列表中的选项，且区分大小写
						if(!agentList.contains(vals[0].trim())){
							return ReturnJson.toErrorJson("第"+r+"行配置平台参数错误,要求在平台列表之内、并区分大小写！");
						}
						if(BlankUtils.checkBlank(vals[1]) || !SDK_AD_TYPE.keySet().contains(vals[1])){
							return ReturnJson.toErrorJson("第"+r+"行配置广告源类型错误,要求在广告源类型列表之类");
						}
						if(BlankUtils.checkBlank(vals[2]) || !OPEN_AD_TYPE.keySet().contains(vals[2])){
							return ReturnJson.toErrorJson("第"+r+"行配置广告使用类型错误,要求在广告使用类型列表之类");
						}
						if(BlankUtils.checkBlank(vals[3]) || !OUT_MAP.keySet().contains(vals[3])){
							return ReturnJson.toErrorJson("第"+r+"行配置应用内外错误");
						}
						if(BlankUtils.checkBlank(vals[4]) || !BIDDING_MAP.keySet().contains(vals[4])){
							return ReturnJson.toErrorJson("第"+r+"行配置bidding模式错误");
						}
						if(BlankUtils.checkBlank(vals[5])){
							return ReturnJson.toErrorJson("第"+r+"行配置广告拓展名错误,不能为空");
						}
						if(BlankUtils.checkBlank(vals[6])){
							return ReturnJson.toErrorJson("第"+r+"行配置sdk-code错误,不能为空");
						}
						if(BlankUtils.checkBlank(vals[7])){
							return ReturnJson.toErrorJson("第"+r+"行配置sdk-appid错误,不能为空");
						}

						// 平台
						ki.setAgent(vals[0].trim());
						//广告源类型
						String sdk_adtype = SDK_AD_TYPE.get(vals[1]);
						ki.setSdk_adtype(sdk_adtype);
						//广告使用类型
						String open_type = OPEN_AD_TYPE.get(vals[2]);
						ki.setOpen_type(open_type);
						//广告位类型 = 广告使用类型
						ki.setAdpos_type(open_type);
						//应用内外
						String out = OUT_MAP.get(vals[3]);
						ki.setOut(out);

						//bidding模式
						String bidding = BIDDING_MAP.get(vals[4]);
						ki.setBidding(bidding);
						//广告拓展名vals[5]
						//广告位id = 平台+sdk_adtype+appid+拓展名
						String adsid = vals[0].trim()+"_"+sdk_adtype+"_"+vals[9].trim()+"_"+vals[5].trim();
						ki.setAdsid(adsid);
						//sdk-code
						ki.setSdk_code(vals[6].trim());
						//sdk-appid
						ki.setSdk_appid(vals[7]);
						//sdk-appkey
						ki.setSdk_appkey(vals[8]);
						//应用
						ki.setAppid(vals[9]);
						//子渠道
						ki.setCha_id(vals[10]);
						//备注
						ki.setNote(vals[11]);
						ki.setCuser(username);
						String params = vals[16];
						try {
							if (BlankUtils.isNotBlank(params)) {
								JSONObject.parseObject(params);
							}
                        } catch (Exception e) {
                            return ReturnJson.toErrorJson("第"+r+"行配置自定义参数错误,请检查是否符合规范JSON格式");
                        }
						ki.setParams(params);

						adSidList.put(r+"",ki);

						/* 待替换的旧sdkcode，转换为adsid再进行匹配 -林小敏.20240311 */
						String old_adsid = "";
						if(vals[17] != null){
							Map<String, Object> act = adsidMap.get(vals[17].trim());
							if(act != null){
								String match_adsid = (act.get("adsid").toString());
								old_adsid = match_adsid;
							}
						}


						//以下为广告配置
						// 广告源
						adConfig.setAdsid(adsid);
						// 旧adsid存入id字段中
						adConfig.setId(old_adsid);

						//广告类型
						adConfig.setAdpos_type(open_type);

						//应用
						adConfig.setAppid(vals[9]);
						// 所属子渠道存入cha_type
						adConfig.setCha_type(vals[10]);
						//广告策略
						adConfig.setStrategy(vals[12]);
						//广告配置子渠道
						adConfig.setCha_id(vals[13]);
						//预估ecpm
						adConfig.setEcpm(new BigDecimal(vals[14]));
						//分配比例
						adConfig.setRate(Integer.parseInt(vals[15]));
						//开启状态
						adConfig.setStatu("1");
						adConfig.setCuser(username);
						adConfigList.put(r+"",adConfig);

					}catch (Exception e){
						logger.error("ks excel to vo error:",e);
						return ReturnJson.toErrorJson("第"+r+"行配置错误,请检查");
					}
				}
				logger.info("batchImportAdsid:"+JSON.toJSONString(adSidList));
				if (adSidList.size()>0){
					retMap = batchCreateAdSid(adSidList,adConfigList);
					if (isSyncAdpos != null && isSyncAdpos) {
						String syncRet = adv2Service.syncAdsidToAdpos(new ArrayList<>(adSidList.values()), username);
						if(retMap.size()>0) {
							retMap.put("同步到广告位结果:", syncRet);
						} else {
							retMap.put("全部导入成功<br>同步到广告位结果:", syncRet);
						}
						logger.info(syncRet);
					}
				}
			} else {
				return ReturnJson.toErrorJson("上传文件有误，需要.xls格式文件");
			}
		} catch (Exception e) {
			logger.error("batchImportAdsid error:",e);
			return ReturnJson.toErrorJson("上传文件失败,请联系管理员!错误信息:"+e.getMessage());
		}
		StringBuffer sb = new StringBuffer();
		if (retMap.size()>0){
			for (Map.Entry<String,String> ret:retMap.entrySet()){
				sb.append(ret.getKey()).append(ret.getValue()).append("<br>");
			}
			return ReturnJson.toErrorJson(sb.toString());
		}else {
			return ReturnJson.success("全部导入成功");
		}
	}

	public TreeMap<String,String> batchCreateAdSid(TreeMap<String,ExtendAdsidVo> adSidMap,TreeMap<String,ExtendAdconfigVo> adConfiMap){
		TreeMap<String,String> retMap = new TreeMap<>();

		HashMap<String, String> duplicateMap = new HashMap<>();
		ArrayList<String> toDelKey = new ArrayList<>();
		for (Map.Entry<String, ExtendAdsidVo> en : adSidMap.entrySet()) {
			final String sdkCode = en.getValue().getSdk_code();
			final String index = en.getKey();

			duplicateMap.computeIfPresent(sdkCode, (key, value) -> {
				retMap.put("文件中sdk_code冲突:" + sdkCode, "已去重自动生成第"+duplicateMap.get(sdkCode)+"行");
				toDelKey.add(index);
				return value;
			});
			duplicateMap.putIfAbsent(sdkCode, index);

		}

		toDelKey.forEach(adSidMap::remove);
		for (Map.Entry<String,ExtendAdsidVo> map:adSidMap.entrySet()){
			String row = map.getKey();
			ExtendAdsidVo adsidVo = map.getValue();
			ExtendAdconfigVo adconfigVo = adConfiMap.get(row);

			String codeCount =  "select count(*) from dn_extend_adsid_manage where sdk_code='" + adsidVo.getSdk_code() + "'";
			int count = adv2Mapper.selectCountSql(codeCount);
			if (count > 0) {
				retMap.put("第"+row+"行", adsidVo.getSdk_code()+" 该sdk_code已存在");
				continue;
			}

			String adsidQuerySql =  "select * from dn_extend_adsid_manage where adsid=#{obj.adsid} ";
			List<Map<String, Object>> list = adv2Service.queryListMapTwo(adsidQuerySql, adsidVo);

			//第一步判断是否已经存在
			if (list.size()>0){
				retMap.put("第"+row+"行","该广告id已存在");
				continue;
			}
			//第二步写入广告源配置表
			// 2024.1.4 林小敏 导入时加上params
			String adsidInsertSql = "insert into dn_extend_adsid_manage(adsid,agent,sdk_code,sdk_appid,sdk_appkey,sdk_adtype,adpos_type,open_type,note,appid,cha_id,bidding,createtime,lasttime,cuser,params,`out`) "+
					"values(#{obj.adsid},#{obj.agent},#{obj.sdk_code},#{obj.sdk_appid},#{obj.sdk_appkey},#{obj.sdk_adtype},#{obj.adpos_type},#{obj.open_type},#{obj.note},#{obj.appid},#{obj.cha_id},#{obj.bidding},now(),now(),#{obj.cuser},#{obj.params},#{obj.out}) ";

			try {
				int result = adv2Service.execSqlHandle(adsidInsertSql, adsidVo);
				if (result==0){
					retMap.put("第"+row+"行","该配置写入广告源管理表失败");
					continue;
				}
			}catch (Exception e){
				retMap.put("第"+row+"行","该配置写入广告源管理表失败");
				logger.error("batchCreateAdSid step1 error ",e);
				continue;
			}
			//第三步写入广告配置表
			/* 没有策略的话广告源正常导入，只是不需要写入广告配置 -林小敏.20230920 */
			/* 待替换的旧adsid有值则进行更新 -林小敏.20240311 */
			if(!BlankUtils.checkBlank(adconfigVo.getStrategy())) {
				String adconfigInsertSql = "insert into dn_extend_adconfig(appid,cha_id,adpos_type,strategy,adsid,statu,ecpm,rate,createtime,lasttime,cuser) " +
						"values(#{obj.appid},#{obj.cha_id},#{obj.adpos_type},#{obj.strategy},#{obj.adsid},#{obj.statu},#{obj.ecpm},#{obj.rate},now(),now(),#{obj.cuser}) ";

				String adconfigUpdateSql = "update dn_extend_adconfig set adsid=#{obj.adsid} where appid=#{obj.appid} and cha_id=#{obj.cha_type} " +
						" and adpos_type=#{obj.adpos_type} and adsid=#{obj.id}";

				String query = "select id from dn_extend_adconfig where appid=#{obj.appid} and cha_id=#{obj.cha_type} and adpos_type=#{obj.adpos_type} and adsid=#{obj.id}";
				List<Map<String, Object>> adList = adv2Service.queryListMapTwo(query, adconfigVo);

				/* 导入记录有待替换的旧adsid，有旧的adsid则进行更新、没有则新增 */
				if(!BlankUtils.checkBlank(adconfigVo.getId()) && (adList != null && !adList.isEmpty())){
					try {
						adv2Service.execSqlHandle(adconfigUpdateSql, adconfigVo);
					} catch (Exception e) {
						retMap.put("第" + row + "行", "该配置已经写入广告源管理表,但更新广告配置表失败");
						logger.error("batchCreateAdSid step2 error ", e);
					}

				}else{
					try {
						int result = adv2Service.execSqlHandle(adconfigInsertSql, adconfigVo);
						if (result == 0) {
							retMap.put("第" + row + "行", "该配置已经写入广告源管理表,但写入广告配置表失败");
						}
					} catch (Exception e) {
						retMap.put("第" + row + "行", "该配置已经写入广告源管理表,但写入广告配置表失败");
						logger.error("batchCreateAdSid step2 error ", e);
					}
				}

			}
		}
		return retMap;
	}

	@RequestMapping(value = "app/newAdsidList", method = {RequestMethod.GET, RequestMethod.POST})
	@ControllerLoggingEnhancer
	@ApiOperation(value = "新广告源查询", notes = "查询")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "appid", value = "产品id", dataType = "String"),
			@ApiImplicitParam(name = "channel", value = "渠道", dataType = "String"),
			@ApiImplicitParam(name = "adtype", value = "广告类型", dataType = "String"),
			@ApiImplicitParam(name = "strategy", value = "策略", dataType = "String")
	})
	public String newAdsidList(@RequestParam String appid, @RequestParam String channel,
							   @RequestParam String adtype, @RequestParam String strategy) {
		// 查询adconfig 中的adsid
		List<Map> maps = adv2Mapper.selectAllAdconfig(channel, appid, adtype, strategy);
		List<String> include = maps.stream().map(map -> map.get("adsid").toString()).distinct().collect(Collectors.toList());
		// 查询 源管理 中的adsid
		List<ExtendAdsidVo> voList = adv2Mapper.selectExtendAdsid(channel, appid, adtype);
		List<String> exclude = voList.stream().map(ExtendAdsidVo::getAdsid).distinct().collect(Collectors.toList());
		HashMap<String, List<String>> data = new HashMap<>();
		data.put("include", include);
		data.put("exclude", exclude);
		return ReturnJson.success(data);
	}

	@PostMapping(value = "app/newBatchInsert")
	@ControllerLoggingEnhancer
	@ApiOperation(value = "广告配置批量插入", notes = "广告配置批量插入")
	public String newBatchInsert(ExtendAdconfigVo vo) {
		String userName = LOGIN_USER_NAME.get();
		String adsidStr = vo.getAdsid();
		if (vo.getCha_id().equals("csj")) {
			vo.setCha_id("");
		}
		ArrayList<ExtendAdconfigVo> voList = new ArrayList<>();
		for (String adsid : adsidStr.split(",")) {
			ExtendAdconfigVo extendAdconfigVo = new ExtendAdconfigVo();
			BeanUtils.copyProperties(vo, extendAdconfigVo);
			extendAdconfigVo.setAdsid(adsid);
			extendAdconfigVo.setCuser(userName);
			voList.add(extendAdconfigVo);
		}
		return ReturnJson.success("成功插入 " + adv2Mapper.insertBatchAdConfig(voList));
	}



	/**
	 * 点击配置-查询
	 * @param app
	 * @param request
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/app/selectExtendClickConfig", method={RequestMethod.GET, RequestMethod.POST})
	public String selectExtendClickConfig(ExtendClickConfigVo app, HttpServletRequest request) throws IOException {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		Map<String, String> pageParams = BlankUtils.getParameter(request);
		JSONObject result = new JSONObject();
		try {
			PageHelper.startPage(pageParams); // 进行分页

			String sql = "select * from dn_extend_click_config where 1=1";
			if(!BlankUtils.checkBlank(app.getAppid()))
				sql += " and appid in ("+app.getAppid()+")";
			if(!BlankUtils.checkBlank(app.getApp_category()))
				sql += " and app_category like concat('%',#{obj.app_category},'%')";
			if(!BlankUtils.checkBlank(app.getCha_id()))
				sql += " and cha_id = #{obj.cha_id}";
			if(!BlankUtils.checkBlank(app.getPrjid()))
				sql += " and prjid = #{obj.prjid}";
			if(!BlankUtils.checkBlank(app.getUser_group()))
				sql += " and user_group = #{obj.user_group}";
			if(!BlankUtils.checkBlank(app.getApi_source()))
				sql += " and api_source like concat('%',#{obj.api_source},'%')";
			if(!BlankUtils.checkBlank(app.getOpen_type()))
				sql += " and open_type = #{obj.open_type}";
			if(!BlankUtils.checkBlank(app.getSdk_adtype()))
				sql += " and sdk_adtype = #{obj.sdk_adtype}";
			if(!BlankUtils.checkBlank(app.getClick_function()))
				sql += " and click_function like concat('%',#{obj.click_function},'%')";

			sql += " order by createtime desc";

			List<Map<String, Object>> list = adv2Service.queryListMapTwo(sql, app);
			long size = ((Page) list).getTotal();

			list.forEach(act -> {
				act.put("createtime", (act.get("createtime")!=null?act.get("createtime")+"":""));
				act.put("endtime", (act.get("endtime")!=null?act.get("endtime")+"":""));
			});

			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}

	/**
	 * 点击配置-新增修改删除操作
	 * @param handle=add、edit、del，新增、修改、删除
	 * @param app
	 * @param request
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/app/extendClickConfigHandle", method={RequestMethod.POST})
	public String extendClickConfigHandle(String handle, ExtendClickConfigVo app,
									   HttpServletRequest request, HttpServletResponse response) throws IOException, Exception {

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// 筛除sql敏感字符注入
		if(app.getId() != null && BlankUtils.sqlValidate(app.getId().toString())){
			return "{\"ret\":0,\"msg\":\"param is error!\"}";
		}

		// 操作人登录名获取
		String cuser = "";
		if(token.startsWith("wbtoken")){
			CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getLogin_name();
		}else if(token.startsWith("waibaotoken")){
			WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
			cuser = curr.getUser_id();
		}
		app.setCuser(cuser);
		app.setEuser(cuser);

		int result = 0;
		try {
			String sql = null;

			if("add".equals(handle)){
				sql = "insert into dn_extend_click_config(app_category,appid,cha_id,prjid,user_group,api_source,open_type,sdk_adtype,click_function,click_value,conversion_rate,custom_param,`statu`,cuser,createtime) "+
						"values(#{obj.app_category},#{obj.appid},#{obj.cha_id},#{obj.prjid},#{obj.user_group},#{obj.api_source},#{obj.open_type},#{obj.sdk_adtype},#{obj.click_function},#{obj.click_value},#{obj.conversion_rate},#{obj.custom_param},#{obj.statu},#{obj.cuser},now()) ";

			}else if("edit".equals(handle)){
				sql = "update dn_extend_click_config set app_category=#{obj.app_category},appid=#{obj.appid},cha_id=#{obj.cha_id},prjid=#{obj.prjid},user_group=#{obj.user_group},api_source=#{obj.api_source},open_type=#{obj.open_type},sdk_adtype=#{obj.sdk_adtype},click_function=#{obj.click_function},click_value=#{obj.click_value},conversion_rate=#{obj.conversion_rate},custom_param=#{obj.custom_param},`statu`=#{obj.statu},euser=#{obj.euser},endtime=now() "+
						"where id = #{obj.id} ";

			}else if("del".equals(handle)){
				sql = "delete from dn_extend_click_config where id in ("+app.getId()+")";
			}

			result = adv2Service.execSqlHandle(sql, app);
			if(result > 0)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"无效操作!\"}";

		} catch (Exception e) {
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"操作失败，错误信息 : "+e.getMessage()+"\"}";
		}
	}


}
