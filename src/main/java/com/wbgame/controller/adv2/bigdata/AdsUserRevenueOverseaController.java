package com.wbgame.controller.adv2.bigdata;

import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.pojo.adv2.bigdata.UserRevenueOverseaParam;
import com.wbgame.service.adv2.AdsUserRevenueOverseaService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2025/06/04
 * @description 用户展示分布-海外
 **/
@CrossOrigin
@RestController
@RequestMapping("/adb/app/oversea")
@ControllerLoggingEnhancer
public class AdsUserRevenueOverseaController {

    @Autowired
    private AdsUserRevenueOverseaService adsUserRevenueOverseaService;

    /**
     * 变现-活跃-新增用户广告展示-海外
     *
     * @param param 查询参数
     * @return 查询结果
     */
    @ApiOperation(value = "变现-新增用户展示频次", notes = "变现-新增用户展示频次", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "start", value = "页码", dataType = "String"),
            @ApiImplicitParam(name = "limit", value = "条数", dataType = "String"),
            @ApiImplicitParam(name = "appid", value = "产品id", dataType = "String"),
            @ApiImplicitParam(name = "channel", value = "渠道", dataType = "String"),
            @ApiImplicitParam(name = "start_date", value = "起始日期", dataType = "String"),
            @ApiImplicitParam(name = "end_date", value = "结束日期", dataType = "String"),
            @ApiImplicitParam(name = "user_type", value = "用户类型(输入新增)", dataType = "String"),
            @ApiImplicitParam(name = "adpos_type", value = "广告位类型", dataType = "String"),
            @ApiImplicitParam(name = "isDouyin", value = "是否请求抖音数据", dataType = "int")
    })
    @PostMapping(value = "/selectAdshowActuser")
    public String selectAdshowActuser(@RequestBody UserRevenueOverseaParam param) {
        return adsUserRevenueOverseaService.selectAdshowActuser(param);
    }

    /**
     * 变现-活跃用户广告展示导出-海外
     *
     * @param dto      导出条件
     * @param response HttpServletResponse
     */
    @ApiOperation(value = "变现-新增用户展示频次.导出", notes = "变现-新增用户展示频次.导出", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appid", value = "产品id", dataType = "String"),
            @ApiImplicitParam(name = "channel", value = "渠道", dataType = "String"),
            @ApiImplicitParam(name = "start_date", value = "起始日期", dataType = "String"),
            @ApiImplicitParam(name = "end_date", value = "结束日期", dataType = "String"),
            @ApiImplicitParam(name = "user_type", value = "用户类型", defaultValue = "新增", dataType = "String"),
            @ApiImplicitParam(name = "adpos_type", value = "广告位类型", dataType = "String"),
            @ApiImplicitParam(name = "value", value = "自定义导出字段", dataType = "String"),
            @ApiImplicitParam(name = "report", value = "文件名", dataType = "String"),
            @ApiImplicitParam(name = "isDouyin", value = "是否请求抖音数据", dataType = "int")
    })
    @RequestMapping(value = "/exportAdshowActuser", method = {RequestMethod.GET, RequestMethod.POST})
    public void exportAdshowActuser(@RequestBody UserRevenueOverseaParam dto, HttpServletResponse response) {
        adsUserRevenueOverseaService.exportAdshowActuser(response, dto);
    }

}
