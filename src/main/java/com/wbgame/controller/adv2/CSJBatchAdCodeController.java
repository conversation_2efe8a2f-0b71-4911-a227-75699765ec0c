package com.wbgame.controller.adv2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Sets;
import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.WaibaoUserVo;
import com.wbgame.pojo.adv2.*;
import com.wbgame.pojo.adv2.adcodeModule.CSJAdcodeModuleVo;
import com.wbgame.pojo.adv2.resultEntity.SyncResult;
import com.wbgame.service.adv2.AdCodeConfigService;
import com.wbgame.service.adv2.Adv2Service;
import com.wbgame.service.adv2.module.ModuleOperationService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpRequest;
import jxl.Sheet;
import jxl.Workbook;
import lombok.SneakyThrows;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;
import static com.wbgame.common.constants.AdcodeCommonConstants.TOBID_PREFIX;

/**
 * <AUTHOR>
 * @Classname CsjBatchAdCodeController
 * @Description TODO
 * @Date 2021/10/25 15:31
 */

@CrossOrigin
@RestController
@RequestMapping("/adv2/csjAdCode")
public class CSJBatchAdCodeController {


    @Autowired
    AdCodeConfigService adCodeConfigService;
    @Resource
    private Redisson redisson;

    @Resource
    private ModuleOperationService moduleOperationService;

    @Autowired
    private Adv2Service adv2Service;

    @Autowired
    RedisTemplate redisTemplate;

    private static final String PLATFORM = "headline";
    private String prefix;

    private static Logger logger = LoggerFactory.getLogger(CSJBatchAdCodeController.class);
    public static final String LOCK_CSJ_FILE_BATCH_IMPORT = "lock_csj_file_batch_import";
    private final static String CSJ_CODE_CREATE_URL = "https://open-api.pangle.cn/union/media/open_api/code/create";

    private final static String CSJ_CODE_CREATE_TEST_URL = "http://sandbox-callback.bytedance.com/union/media/open_api/code/create";

    private final static String CSJ_CODE_UPDATE_URL = "https://open-api.csjplatform.com/union/media/open_api/code/update";

    private final static Map<String, String> CSJ_AD_TYPE_MAP = new HashMap<String, String>() {{
        // 1 信息流 2 banner 3 开屏 5 激励视频 9 新插屏
        put("1", "msg");
        put("2", "banner");
        put("3", "splash");
        put("5", "video");
        put("9", "plaque");
    }};
    private final static Map<String, String> XXL_TEMPLETE_MAP = new HashMap<String, String>() {{
        put("1459", "1459_上⽂下图_1,2,3");
        put("1473", "1473_上图下⽂_1,2,3");
        put("1476", "1476_上⽂下图_1,2,3");
        put("1477", "1477_上⽂下图_1,2,3");
        put("1478", "1478_上⽂下图_1,2,3");
        put("1479", "1479_上⽂下图_1,2,3");
        put("1480", "1480_上图下文_1,2,3");
        put("1481", "1481_上图下⽂_1,2,3");
        put("1482", "1482_文字浮层_1,2,3");
        put("1483", "1483_文字浮层_1,2,3");
        put("1484", "1484_竖版_1,2,3");
        put("1485", "1485_竖版_1,2,3");
        put("1486", "1486_竖版_1,2,3");
        put("1487", "1487_左图右文_1,3");
        put("1488", "1488_左图右文_1,3");
        put("1489", "1489_左文右图_1,3");
        put("1490", "1490_左文右图_1,3");
        put("1491", "1491_三图_1,3");
    }};
    //SDK广告源类型
    private static Map<String, String> SDK_AD_TYPE_MAP = new HashMap<String, String>() {{
        put("开屏", "splash");
        put("原生开屏", "natSplash");
        put("普通banner/模板", "banner");
        put("banner自渲染", "natBanner");
        put("信息流模板", "yuans");
        put("信息流自渲染", "msg");
        put("普通插屏/模板", "plaque");
        put("自渲染插屏", "natPlaque");
        put("插屏视频", "plaqueVideo");
        put("视频", "video");
        put("视频自渲染", "natVideo");
        put("icon", "icon");
    }};
    //广告使用类型
    private static Map<String, String> OPEN_TYPE_MAP = new HashMap<String, String>() {{
        put("信息流-msg", "msg");
        put("banner-banner", "banner");
        put("开屏-splash", "splash");
        put("视频-video", "video");
        put("插屏-plaque", "plaque");
    }};

    //竞价类型 bidding_type
    private static Map<String, String> BIDDING_TYPE_MAP = new HashMap<String, String>() {{
        put("标准", "0");
        put("服务端竞价", "1");
        put("客户端竞价", "2");
    }};
    //是否使用M聚合 use_mediation
    private static Map<String, String> USE_MEDIATION_MAP = new HashMap<String, String>() {{
        put("否", "0");
        put("是", "1");
    }};
    //代码位类型 ad_slot_type
    private static Map<String, String> AD_SLOT_TYPE_MAP = new HashMap<String, String>() {{
        put("信息流", "1");
        put("Banner", "2");
        put("开屏", "3");
        put("插屏", "4");
        put("激励视频", "5");
        put("全屏视频", "6");
        put("Draw信息流", "7");
        put("贴片", "8");
        put("新插屏", "9");
    }};
    //渲染方式 render_type
    private static Map<String, String> RENDER_TYPE_MAP = new HashMap<String, String>() {{
        put("模板渲染", "1");
        put("自渲染", "2");
        put("非原生", "3");
    }};

    //广告样式 ad_categories
    private static Map<String, String> AD_CATEGORIES_MAP = new HashMap<String, String>() {{
        put("大图", "1");
        put("组图", "2");
        put("单图", "3");
        put("视频", "4");
        put("竖版视频", "5");
        put("竖图", "6");
        put("开屏直投大图", "7");
        put("开屏直投竖版视频", "8");
        put("宽图", "11");
        put("方图", "12");
        put("方视频", "13");
    }};
    //模版样式 tpl_list
    private static Map<String, String> TPL_LIST_MAP = new HashMap<String, String>() {{
        put("上文下图", "1459,1476,1477,1478,1479");
        put("上图下⽂", "1473,1480,1481");
        put("⽂字浮层", "1482,1483");
        put("竖版", "1484,1485,1486");
        put("左图右⽂", "1487,1488");
        put("左⽂右图", "1489,1490");
    }};
    //素材类型 accept_material_type
    private static Map<String, String> ACCEPT_MATERIAL_TYPE_MAP = new HashMap<String, String>() {{
        put("仅图片", "1");
        put("仅视频", "2");
        put("图片+视频", "3");
    }};
    //是否轮播 slide_banner
    private static Map<String, String> SLIDE_BANNER_MAP = new HashMap<String, String>() {{
        put("是", "1");
        put("否", "2");
    }};
    //宽高 width_height
    private static Map<String, String> WIDTH_HEIGHT_MAP = new HashMap<String, String>() {{
        put("600*300", "600*300");
        put("600*400", "600*400");
        put("600*500", "600*500");
        put("600*260", "600*260");
        put("600*90", "600*90");
        put("600*150", "600*150");
        put("640*100", "640*100");
        put("690*388", "690*388");
    }};
    //点睛 use_icon
    private static Map<String, String> USE_ICON_MAP = new HashMap<String, String>() {{
        put("是", "1");
        put("否", "2");
    }};
    //视频播放方向 orientation
    private static Map<String, String> ORIENTATION_MAP = new HashMap<String, String>() {{
        put("垂直（竖版）", "1");
        put("水平（横版）", "2");
    }};
    //回调设置 reward_is_callback
    private static Map<String, String> REWARD_IS_CALLBACK_MAP = new HashMap<String, String>() {{
        put("不要", "0");
        put("要", "1");
    }};
    //⼴告铺开⼤⼩ ad_rollout_size
    private static Map<String, String> AD_ROLLOUT_SIZE_MAP = new HashMap<String, String>() {{
        put("全屏", "1");
        put("半屏", "2");
    }};
    //endcard use_endcard
    private static Map<String, String> USE_ENDCARD_MAP = new HashMap<String, String>() {{
        put("不要", "1");
        put("要", "2");
    }};
    //app_type
    private final String TEMPLATE_TYPE_GAME = "1";

    private final String TEMPLATE_TYPE_TOOL = "2";


    /**
     * 查询穿山甲广告位
     *
     * @param request
     * @return
     */
    @RequestMapping("getList")
    public Object getCSJAdCodeList(HttpServletRequest request, CSJAdcodeVo vo) {
        JSONObject ret = new JSONObject();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.toErrorJson("登录token过期,请重新登录");
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<CSJAdcodeVo> list = adCodeConfigService.getHeadlineAdCodeList(vo);
        long size = ((Page) list).getTotal();
        ret.put("ret", 1);
        ret.put("data", list);
        ret.put("totalCount", size);
        return ret;
    }

    /**
     * 新建穿山甲广告位
     *
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("handle")
    public Object handleAdcdoe(HttpServletRequest request, CSJAdcodeVo vo) {
        String token = request.getHeader("token");
        if (StringUtils.isBlank(token)) {
            token = request.getParameter("token");
        }
        if (BlankUtils.checkBlank(token) && !redisTemplate.hasKey(token)) {
            return ReturnJson.toErrorJson("登录token过期,请重新登录");
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String username = "";
        if (token.startsWith("wbtoken")) {
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else if (token.startsWith("waibaotoken")) {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_id();
        }
        return handleAdcdoe(username, vo, false);
    }

    /**
     * isTobid true tobid广告源 不加入广告配置
     * @param username
     * @param vo
     * @param isTobid
     * @return
     */
    public Object handleAdcdoe(String username, CSJAdcodeVo vo, boolean isTobid) {
        JSONObject ret = new JSONObject();
        ret.put("ret", 0);
        ret.put("msg", "操作失败");

        JSONObject result = new JSONObject();
        AdCodeAccountVo account = new AdCodeAccountVo();
        if (vo == null) {
            return ReturnJson.toErrorJson("空vo");
        }
        account.setAppid(vo.getAppid());
        if (BlankUtils.checkBlank(vo.getApp_id()) && BlankUtils.isBlank(vo.getChannel())) {
            ret.put("ret", 0);
            ret.put("msg", "创建广告位失败,原因:平台产品id参数未传");
            return ret;
        }
        account.setTappid(vo.getApp_id());
        account.setChannel(vo.getChannel());
        account.setPlatform("'headline'");
        List<AdCodeAccountVo> list = adCodeConfigService.getAdCodeAccountList(account);
        account = list.stream().filter(t -> t.getAppid().equals(vo.getAppid()) && t.getPlatform().equals("headline")).findFirst().orElse(null);
        if (account == null) {
            ret.put("ret", 0);
            ret.put("msg", "创建广告位失败,原因:该产品未配置变现平台账号");
            return ret;
        }
        //判断广告源id是否已经存在
        prefix = PLATFORM;

        String sdkAdType = vo.getSdk_ad_type();
        String appid = vo.getAppid();
        String adExtentionName = vo.getAdExtensionName();
        String adsid = prefix + "_" + sdkAdType + "_" + appid + "_" + adExtentionName;
        ExtendAdsidVo adsidVo = adCodeConfigService.getDnAdSid(adsid);
        if (adsidVo != null) {
            ret.put("ret", 0);
            ret.put("msg", "创建广告位失败,原因:生成规则:广告平台_广告位类型_产品id_扩展名生成的ad-sid 已经在广告源表存在");
            return ret;
        }
        if (BlankUtils.checkBlank(vo.getAd_slot_type())) {
            ret.put("ret", 0);
            ret.put("msg", "创建广告位失败,原因:广告类型必填");
            return ret;
        }

        switch (vo.getAd_slot_type()) {
            case "1":     //信息流
                result = xxlAdcode(vo, account);
                break;
            case "2":     //banner
                result = bannerAdcode(vo, account);
                break;
            case "3":     //开屏
                result = kpAdcode(vo, account);
                break;
            case "5":     //激励视频
                result = jlspAdcode(vo, account);
                break;
            case "9":     //新插屏
                result = xcpAdcode(vo, account);
                break;
            default:
                break;
        }

        if ("1".equals(result.getString("ret"))) {
            //写入到自己的表
            vo.setCreateUser(username);
            vo.setAd_slot_id(result.getString("ad_slot_id"));
            vo.setApp_id(account.getTappid());
            vo.setRole_id(account.getTaccountid());
            vo.setUser_id(account.getTaccountid());
//            logger.info("csj handleAdcdoe info:" + JSON.toJSONString(vo));
            int succ = adCodeConfigService.saveHeadlineAdCode(vo);
            if (succ > 0) {
                ret.put("ret", 1);
                boolean saved = saveCSJAdCodeToAdSidManage(vo, account);
                if (saved) {
                    ret.put("msg", "操作成功");
                } else {
                    ret.put("msg", "新建广告位成功,但写入广告源管理表失败");
                }
                ret.put("ad_slot_id", result.getString("ad_slot_id"));
                ret.put("code", result.getString("ad_slot_id"));
                ret.put("adsid", adsid);
            }
            //以下渠道为+策略不为空进行写入广告配置表操作
            String[] channelList = {"csj", "apple", "huaweiml", "huaweiml2", "vivoml", "vivomj",
                    "vivoml2", "xiaomimj", "oppomj", "opposd", "vivosd", "huaweisd", "huawei", "huawei2", "honor"};
            if (!isTobid && Arrays.asList(channelList).contains(vo.getChannel()) && !BlankUtils.checkBlank(vo.getStrategy())) {
                JSONObject saveDnAdcode = saveAdcodeToDnExtendAdconfig(vo, account);
                if (!"1".equals(saveDnAdcode.get("ret"))) {
                    ret.put("msg", (ret.getString("msg") == null ? "" : ret.getString("msg")) + "-但写入广告配置表失败");
                }
            }
        } else {
            ret.put("ret", 0);
            ret.put("msg", "创建广告位失败,原因:" + result.getString("msg"));
        }
        return ret;
    }

    /**
     * 穿山甲-修改广告位
     *
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("update")
    public Object updateCSJAdcode(HttpServletRequest request, CSJAdcodeVo vo) {
        JSONObject ret = new JSONObject();
        ret.put("ret", 0);
        ret.put("msg", "操作失败");
        String token = request.getParameter("token");
        String handle = request.getParameter("handle");
        if (BlankUtils.checkBlank(token) && !redisTemplate.hasKey(token)) {
            return ReturnJson.toErrorJson("登录token过期,请重新登录");
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String username = "";
        if (token.startsWith("wbtoken")) {
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else if (token.startsWith("waibaotoken")) {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_id();
        }
        vo.setModifyUser(username);
        AdCodeAccountVo account = new AdCodeAccountVo();
        if (vo != null) {
            account.setAppid(vo.getAppid());
            account.setTappid(vo.getApp_id());
            account.setChannel(vo.getChannel());
            account.setPlatform("'headline'");
            List<AdCodeAccountVo> list = adCodeConfigService.getAdCodeAccountList(account);
            account = list.stream().filter(t -> t.getAppid().equals(vo.getAppid()) && t.getPlatform().equals("headline")).findFirst().orElse(null);
            if (account == null) {
                ret.put("ret", 0);
                ret.put("msg", "修改广告位cpm失败,原因:该产品未配置变现平台账号");
                return ret;
            }
        }
        vo.setModifyUser(username);
        if ("cpm".equals(handle)) {
            ret = updateCpm(account, vo);
        }
        try {
            if ("1".equals(ret.getString("ret"))) {
                adCodeConfigService.updateCSJCpm(vo);
            }
        } catch (Exception e) {
            logger.error("csj update cpm modify info error:", e);
        }
        return ret;
    }


    /**
     * 修改广告位
     *
     * @param account
     * @param vo
     * @return
     */
    public JSONObject updateCpm(AdCodeAccountVo account, CSJAdcodeVo vo) {
        JSONObject ret = new JSONObject();
        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");
        Map<String, Object> paramMap = new HashMap<>();
        String timestamp = System.currentTimeMillis() / 1000 + "";
        String nonce = String.valueOf(new Random().nextInt(10));

        paramMap.put("user_id", Integer.parseInt(account.getTaccountid()));
        paramMap.put("role_id", Integer.parseInt(account.getTaccountid()));
        paramMap.put("nonce", Integer.parseInt(nonce));
        paramMap.put("timestamp", Integer.parseInt(timestamp));
        paramMap.put("version", "1.0");
        paramMap.put("ad_slot_id", vo.getAd_slot_id());
        paramMap.put("cpm", vo.getCpm());
        paramMap.put("sign", sign(timestamp, nonce, account.getTsecrect()));

        String result = HttpRequest.httpPostJson(CSJ_CODE_UPDATE_URL, paramMap, headMap);
        if (!BlankUtils.checkBlank(result)) {
            JSONObject json = JSONObject.parseObject(result);
            if ("0".equals(json.getString("code"))) {
                ret.put("ret", 1);
            } else {
                ret.put("msg", json.getString("message"));
                logger.info("csj updateCpm fail:" + JSON.toJSONString(vo) + "=====" + json.getString("message"));
            }
        }
        return ret;
    }


    /**
     * 保存穿山甲创建的广告位至广告源管理表 dn_extend_adsid_manage
     *
     * @param vo
     * @param acount
     * @return
     */
    public boolean saveCSJAdCodeToAdSidManage(CSJAdcodeVo vo, AdCodeAccountVo acount) {
        ExtendAdsidVo app = new ExtendAdsidVo();
        try {
            //平台_广告源类型_产品id_扩展名
            String adType = CSJ_AD_TYPE_MAP.get(vo.getAd_slot_type());
            String sdkAdType = vo.getSdk_ad_type();
            String appid = acount.getAppid();
            String adExtentionName = vo.getAdExtensionName();
            String adsid = prefix + "_" + sdkAdType + "_" + appid + "_" + adExtentionName;
            //adsid
            app.setAdsid(adsid);
            //广告平台
            app.setAgent(PLATFORM);
            //广告位id
            app.setSdk_code(vo.getAd_slot_id());
            //广告平台产品id
            app.setSdk_appid(acount.getTappid());
            app.setSdk_appkey("");
            //SDK广告类型
            app.setSdk_adtype(sdkAdType);
            //广告平台广告类型
            app.setAdpos_type(adType);
            //备注
            app.setNote(vo.getRemark());
            //动能产品id
            app.setAppid(acount.getAppid());
            //子渠道
            app.setCha_id(vo.getChannel());
            //创建人
            app.setCuser(vo.getCreateUser());
            //cpm
            app.setCpmFloor(vo.getCpm());
            //广告使用类型
            app.setOpen_type(vo.getOpen_type());
            app.setParams(vo.getParams());
            //bidding模式 0 否 1是 bidding_type不是 0时bidding 为1
            if (!BlankUtils.checkBlank(vo.getBidding_type()) && !"0".equals(vo.getBidding_type())) {
                app.setBidding("1");
            } else {
                app.setBidding("0");
            }
            logger.info("saveCSJAdCodeToAdSidManage:" + JSON.toJSONString(app));
            String sql = "insert into dnwx_cfg.dn_extend_adsid_manage(adsid,agent,sdk_code,sdk_appid,sdk_appkey," +
                    "sdk_adtype,adpos_type,note,appid,cha_id,createtime,lasttime,cuser," +
                    "cpmFloor,open_type,bidding,params) " +
                    "values(#{obj.adsid},#{obj.agent},#{obj.sdk_code},#{obj.sdk_appid},#{obj.sdk_appkey}," +
                    "#{obj.sdk_adtype},#{obj.adpos_type},#{obj.note},#{obj.appid},#{obj.cha_id},now(),now()," +
                    "#{obj.cuser},#{obj.cpmFloor},#{obj.open_type},#{obj.bidding},#{obj.params}) ";
            int result = adv2Service.execSqlHandle(sql, app);
            if (result > 0) {
                return true;
            }
        } catch (Exception e) {
            logger.error("saveCSJAdCodeToAdSidManage error:", e);
        }

        return false;
    }

    /**
     * @param request
     * @param file
     * @return
     */
    public Object batchImport(String template_type, MultipartFile file) {
        TreeMap<String, String> retMap = new TreeMap<>();
        try {
            String username = LOGIN_USER_NAME.get();

            // 应用	子渠道	sdk广告源样式	广告使用类型	广告拓展名	备注	代码位名称	竞价类型	是否使⽤M聚合	cpm设置	广告平台
            // 广告类型	渲染⽅式	⼴告样式	模版样式	素材类型	是否轮播	广告尺寸
            // 点睛样式	视频播放⽅向	奖励名称	奖励数量	回调设置	回调url	⼴告铺开⼤⼩	跳过	endcard
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                TreeMap<Integer, CSJAdcodeVo> list = new TreeMap<>();

                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int column = sheet.getColumns();
                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                        continue;
                    }
                    try {
                        String[] vals = new String[column];
                        for (int c = 0; c < column; c++) {
                            vals[c] = sheet.getCell(c, r).getContents();
                        }
                        CSJAdcodeVo ki = resolveArrayToVo(template_type, vals);
                        if (ki == null) {
                            return ReturnJson.toErrorJson("第" + r + "行配置错误,请检查");
                        }
                        list.put(r, ki);
                    } catch (Exception e) {
                        logger.error("csj excel to vo error:", e);
                        return ReturnJson.toErrorJson("第" + r + "行配置错误,请检查");
                    }
                }
//                logger.info("csj batchImport:"+JSON.toJSONString(list));
                if (list.size() > 0) {
                    logger.info("上传文件中包含了数据 {} 条", list.size());
                    retMap = batchCreateAdcode(list, username);
                }
            } else {
                return ReturnJson.toErrorJson("上传文件有误，需要.xls格式文件");
            }
        } catch (Exception e) {
            logger.error("csj batchImport error:", e);
            return ReturnJson.toErrorJson("上传文件失败,请联系管理员!错误信息:" + e.getMessage());
        }
        StringBuffer sb = new StringBuffer();
        if (retMap.size() > 0) {
            for (Map.Entry<String, String> ret : retMap.entrySet()) {
                sb.append(ret.getKey() + ret.getValue()).append("\t");
            }
            return ReturnJson.success(sb.toString());
        } else {
            return ReturnJson.success("全部创建成功");
        }
    }

    @Nullable
    public CSJAdcodeVo resolveArrayToVo(String template_type, String[] vals) {
        CSJAdcodeVo ki = null;
        if (TEMPLATE_TYPE_GAME.equals(template_type)) {
            ki = getGameCSJAdcode(vals);
            ki.setTemplate_type(TEMPLATE_TYPE_GAME);
        }
        if (TEMPLATE_TYPE_TOOL.equals(template_type)) {
            ki = getToolCSJAdcode(vals);
            ki.setTemplate_type(TEMPLATE_TYPE_TOOL);
        }
        return ki;
    }

    /**
     * 批量上传文件的接口,为了避免多次误操作, 加上分布式锁
     *
     * @param file          上传的文件
     * @param template_type 类型 1 游戏, 2 工具
     * @return 结果
     * @throws InterruptedException
     */
    @RequestMapping("batchImport")
    @ControllerLoggingEnhancer
    public Object lockedBatchImport(@RequestParam(value = "fileName") MultipartFile file,
                                    @RequestParam(value = "template_type") String template_type) throws InterruptedException {
        RLock lock = redisson.getLock(LOCK_CSJ_FILE_BATCH_IMPORT);
        if (lock.tryLock(1L, TimeUnit.SECONDS)) {
            try {
                return batchImport(template_type, file);
            } finally {
                lock.unlock();
            }
        } else {
            return ReturnJson.toErrorJson("有同步任务正在运行中，请稍后再试");
        }
    }

    /**
     * 穿山甲通用广告位创建处理-1
     *
     * @param map
     * @param username
     * @return
     */
    public TreeMap batchCreateAdcode(TreeMap<Integer, CSJAdcodeVo> map, String username) {
        TreeMap<String, String> retMap = new TreeMap<>();
        int count = 0;
        for (Map.Entry<Integer, CSJAdcodeVo> m : map.entrySet()) {
            try {
                JSONObject ret = commonHandleAdcode(m.getValue(), username);
                if ("0".equals(ret.getString("ret"))) {
                    retMap.put("第" + m.getKey() + "行配置创建广告位失败,失败原因:", ret.getString("msg"));
                }
            } catch (Exception e) {
                retMap.put("第" + m.getKey() + "行配置创建广告位失败,失败原因:", "服务器出错,信息:" + e.getMessage());
                logger.error("csj batchCreateAdcode error:", e);
            } finally {
                count++;
            }
        }
        logger.info("commonHandleAdcode execute {} times", count);
        return retMap;
    }

    /**
     * 穿山甲通用广告位创建处理-2
     *
     * @param vo
     * @param username
     * @return
     */
    public JSONObject commonHandleAdcode(CSJAdcodeVo vo, String username) {
        JSONObject ret = new JSONObject();
        JSONObject result = new JSONObject();
        AdCodeAccountVo account = new AdCodeAccountVo();
        prefix = PLATFORM;
        if (vo != null) {
            account.setAppid(vo.getAppid());
            if (TEMPLATE_TYPE_GAME.equals(vo.getTemplate_type())) {
                if (BlankUtils.checkBlank(vo.getApp_id()) && BlankUtils.isBlank(vo.getChannel())) {
                    ret.put("ret", 0);
                    ret.put("msg", "创建广告位失败,原因:平台产品id参数未传");
                    return ret;
                }
                account.setTappid(vo.getApp_id());
                account.setChannel(vo.getChannel());
                account.setPlatform("'headline'");
            }
            List<AdCodeAccountVo> list = adCodeConfigService.getAdCodeAccountList(account);
            account = list.stream().filter(t -> t.getAppid().equals(vo.getAppid()) && t.getPlatform().equals("headline")).findFirst().orElse(null);
            if (account == null) {
                ret.put("ret", 0);
                ret.put("msg", "创建广告位失败,原因:该产品未配置变现平台账号");
                return ret;
            }
            //判断广告源id是否已经存在
            String agent = "headline";
            String sdkAdType = vo.getSdk_ad_type();
            String appid = vo.getAppid();
            String adExtentionName = vo.getAdExtensionName();
            String adsid = agent + "_" + sdkAdType + "_" + appid + "_" + adExtentionName;
            ExtendAdsidVo adsidVo = adCodeConfigService.getDnAdSid(adsid);
            if (adsidVo != null) {
                ret.put("ret", 0);
                ret.put("msg", "创建广告位失败,原因:生成规则:广告平台_广告位类型_产品id_扩展名生成的ad-sid 已经在广告源表存在");
                return ret;
            }
        }
        try {
            switch (vo.getAd_slot_type()) {
                case "1":     //信息流
                    result = xxlAdcode(vo, account);
                    break;
                case "2":     //banner
                    result = bannerAdcode(vo, account);
                    break;
                case "3":     //开屏
                    result = kpAdcode(vo, account);
                    break;
                case "5":     //激励视频
                    result = jlspAdcode(vo, account);
                    break;
                case "9":     //新插屏
                    result = xcpAdcode(vo, account);
                    break;
                default:
                    break;
            }
            if ("1".equals(result.getString("ret"))) {
                //写入到自己的表
                vo.setCreateUser(username);
                vo.setAd_slot_id(result.getString("ad_slot_id"));
                vo.setApp_id(account.getTappid());
                vo.setRole_id(account.getTaccountid());
                vo.setUser_id(account.getTaccountid());
//                logger.info("csj handleAdcdoe info:" + JSON.toJSONString(vo));
                int succ = adCodeConfigService.saveHeadlineAdCode(vo);
                if (succ > 0) {
                    ret.put("ret", 1);
                    boolean saved = saveCSJAdCodeToAdSidManage(vo, account);
                    if (saved) {
                        ret.put("msg", "操作成功");
                    } else {
                        ret.put("msg", "新建广告位成功,但写入广告源管理表失败");
                        // 写入源管理表失败直接不进入下一步了
                        return ret;
                    }
                    ret.put("ad_slot_id", result.getString("ad_slot_id"));
                }
                //以下渠道+策略不为空进行写入广告配置表操作
                String[] channelList = {"csj", "apple", "huaweiml", "huaweiml2", "vivoml", "vivomj",
                        "vivoml2", "xiaomimj", "oppomj", "opposd", "vivosd", "huaweisd", "huawei", "huawei2", "honor"};
                if (Arrays.asList(channelList).contains(vo.getChannel()) && !BlankUtils.checkBlank(vo.getStrategy())) {
                    JSONObject saveDnAdcode = saveAdcodeToDnExtendAdconfig(vo, account);
                    if (!"1".equals(saveDnAdcode.get("ret"))) {
                        ret.put("msg", (ret.getString("msg") == null ? "" : ret.getString("msg")) + "-但写入广告配置表失败");
                    }
                }
            } else {
                ret.put("ret", 0);
                ret.put("msg", "创建广告位失败,原因:" + result.getString("msg"));
            }
        } catch (Exception e) {
            ret.put("ret", 0);
            ret.put("msg", "创建广告位失败,原因:" + e.getMessage());
            logger.error("csj commonHandleAdcode error:", e);
        }
        return ret;
    }

    /**
     * 信息流广告位
     *
     * @param vo
     * @return
     */
    private JSONObject xxlAdcode(CSJAdcodeVo vo, AdCodeAccountVo account) {
        JSONObject ret = new JSONObject();
        ret.put("ret", 0);

        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");
        Map<String, Object> paramMap = new HashMap<>();
        String timestamp = System.currentTimeMillis() / 1000 + "";
        String nonce = String.valueOf(new Random().nextInt(10));


        paramMap.put("user_id", Integer.parseInt(account.getTaccountid()));
        paramMap.put("role_id", Integer.parseInt(account.getTaccountid()));
        paramMap.put("nonce", Integer.parseInt(nonce));
        paramMap.put("timestamp", Integer.parseInt(timestamp));
        paramMap.put("version", "1.0");
        paramMap.put("app_id", Integer.parseInt(account.getTappid()));
        paramMap.put("ad_slot_type", 1);
        paramMap.put("ad_slot_name", vo.getAd_slot_name());
        paramMap.put("use_mediation", vo.getUse_mediation());
        if (!BlankUtils.checkBlank(vo.getVideo_voice_control())) {
            paramMap.put("video_voice_control", Integer.parseInt(vo.getVideo_voice_control()));
        }
        //竞价类型 1) 可选值：0（标准）、1（服务端竞价）
        if (!BlankUtils.checkBlank(vo.getBidding_type())) {
            paramMap.put("bidding_type", Integer.parseInt(vo.getBidding_type()));
        }
        //cpm
        if (!BlankUtils.checkBlank(vo.getCpm())) {
            if (!isNumber(vo.getCpm()) || new BigDecimal(vo.getCpm()).compareTo(new BigDecimal("3000")) > 1) {
                ret.put("msg", "cpm输入不合法,账号为⼈⺠币结算上限为3000，⽀持 ⼩数点如：100.01，该参数必须⼤于0");
                return ret;
            }
            paramMap.put("cpm", vo.getCpm());
        }
        //渲染方式 1模板渲染 2 自渲染
        paramMap.put("render_type", Integer.parseInt(vo.getRender_type()));
        if (BlankUtils.isNotBlank(vo.getAd_categories())) {
            //⼴告样式 模版渲染必传
            JSONArray adTypeArray = JSONArray.parseArray(vo.getAd_categories());
            paramMap.put("ad_categories", adTypeArray);
        }
        if (BlankUtils.isNotBlank(vo.getAccept_material_type())) {
            //代码位可接受素材类型 1-仅图⽚，2-仅视频，3-图⽚+视频
            Integer acceptMaterialType = Integer.parseInt(vo.getAccept_material_type());
            paramMap.put("accept_material_type", acceptMaterialType);
        }

        //模版信息
        JSONArray tplArray = new JSONArray();
        if ("1".equals(vo.getRender_type())) {
            if (BlankUtils.checkBlank(vo.getTpl_list())) {
                ret.put("msg", "模版渲染必传模版信息");
                return ret;
            }
        }

        if (!BlankUtils.checkBlank(vo.getTpl_list())) {
            tplArray = JSONArray.parseArray(vo.getTpl_list());
            if ("1".equals(vo.getRender_type())) {
                paramMap.put("tpl_list", tplArray);
            }
            for (Object obj : tplArray) {
                JSONObject json = (JSONObject) obj;
                String tpl_id = json.getString("tpl_id");
                String config = XXL_TEMPLETE_MAP.get(tpl_id);
                String type = config.split("_")[2];
                String[] types = type.split(",");
                if (!Arrays.asList(types).contains(vo.getAccept_material_type())) {
                    ret.put("msg", "传⼊模版ID需结合accept_material_type类型进⾏选择输⼊，否则可能⽆法正确投放");
                    return ret;
                }
            }
        }

        paramMap.put("sign", sign(timestamp, nonce, account.getTsecrect()));
        String result = HttpRequest.httpPostJson(CSJ_CODE_CREATE_URL, paramMap, headMap);
//        logger.info("csj xxl result:" + result);
        if (!BlankUtils.checkBlank(result)) {
            JSONObject json = JSONObject.parseObject(result);
            if ("0".equals(json.getString("code"))) {
                JSONObject data = JSONObject.parseObject(json.getString("data"));
                ret.put("ret", 1);
                ret.put("ad_slot_id", data.getString("ad_slot_id"));
                ret.put("status", data.getString("status"));
            } else {
                ret.put("msg", json.getString("message"));
                logger.info("xxlAdcode fail:" + JSON.toJSONString(vo) + "=====" + json.getString("message"));
            }
        }
        return ret;
    }

    /**
     * banner广告位
     *
     * @param vo
     * @return
     */
    private JSONObject bannerAdcode(CSJAdcodeVo vo, AdCodeAccountVo account) {
        JSONObject ret = new JSONObject();
        ret.put("ret", 0);
        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");
        Map<String, Object> paramMap = new HashMap<>();
        String timestamp = System.currentTimeMillis() / 1000 + "";
        String nonce = String.valueOf(new Random().nextInt(10));

        paramMap.put("user_id", Integer.parseInt(account.getTaccountid()));
        paramMap.put("role_id", Integer.parseInt(account.getTaccountid()));
        paramMap.put("nonce", Integer.parseInt(nonce));
        paramMap.put("timestamp", Integer.parseInt(timestamp));
        paramMap.put("version", "1.0");
        paramMap.put("app_id", Integer.parseInt(account.getTappid()));
        paramMap.put("ad_slot_type", 2);
        paramMap.put("ad_slot_name", vo.getAd_slot_name());
        paramMap.put("use_mediation", vo.getUse_mediation());
        //竞价类型 1) 可选值：0（标准）、1（服务端竞价）
        if (!BlankUtils.checkBlank(vo.getBidding_type())) {
            paramMap.put("bidding_type", Integer.parseInt(vo.getBidding_type()));
        }
        //cpm
        if (!BlankUtils.checkBlank(vo.getCpm())) {
            if (!isNumber(vo.getCpm()) || new BigDecimal(vo.getCpm()).compareTo(new BigDecimal("3000")) > 1) {
                ret.put("msg", "cpm输入不合法,账号为⼈⺠币结算上限为3000，⽀持 ⼩数点如：100.01，该参数必须⼤于0");
                return ret;
            }
            paramMap.put("cpm", vo.getCpm());
        }
        //渲染方式 1模板渲染 2 自渲染
        paramMap.put("render_type", Integer.parseInt(vo.getRender_type()));
        paramMap.put("slide_banner", Integer.parseInt(vo.getSlide_banner()));
        //代码位尺寸 当渲染⽅式为模板渲染时必填，当渲染⽅式为⾃渲染时⽆效
        if ("1".equals(vo.getRender_type())) {
            if (BlankUtils.checkBlank(vo.getWidth()) || BlankUtils.checkBlank(vo.getHeight())) {
                ret.put("msg", "当渲染⽅式为模板渲染时必填，当渲染⽅式为⾃渲染 时⽆效");
                return ret;
            }
            paramMap.put("width", Integer.parseInt(vo.getWidth()));
            paramMap.put("height", Integer.parseInt(vo.getHeight()));
        }

        //广告样式 当渲染⽅式为⾃渲染时必填，当渲染⽅式为模板渲染 时该字段⽆效
        if ("2".equals(vo.getRender_type()) && !BlankUtils.checkBlank(vo.getAd_categories())) {
            JSONArray typeArray = JSONArray.parseArray(vo.getAd_categories());
            paramMap.put("ad_categories", typeArray);
        }

        paramMap.put("sign", sign(timestamp, nonce, account.getTsecrect()));
        String result = HttpRequest.httpPostJson(CSJ_CODE_CREATE_URL, paramMap, headMap);
//        logger.info("csj banner result:" + result);
        if (!BlankUtils.checkBlank(result)) {
            JSONObject json = JSONObject.parseObject(result);
            if ("0".equals(json.getString("code"))) {
                JSONObject data = JSONObject.parseObject(json.getString("data"));
                ret.put("ret", 1);
                ret.put("ad_slot_id", data.getString("ad_slot_id"));
                ret.put("status", data.getString("status"));
            } else {
                ret.put("msg", json.getString("message"));
                logger.info("bannerAdcode fail:" + JSON.toJSONString(vo) + "=====" + json.getString("message"));
            }
        }
        return ret;
    }


    /**
     * 开屏广告位
     *
     * @param vo
     * @return
     */
    private JSONObject kpAdcode(CSJAdcodeVo vo, AdCodeAccountVo account) {
        JSONObject ret = new JSONObject();
        ret.put("ret", 0);

        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");
        Map<String, Object> paramMap = new HashMap<>();
        String timestamp = System.currentTimeMillis() / 1000 + "";
        String nonce = String.valueOf(new Random().nextInt(10));

        paramMap.put("user_id", Integer.parseInt(account.getTaccountid()));
        paramMap.put("role_id", Integer.parseInt(account.getTaccountid()));
        paramMap.put("nonce", Integer.parseInt(nonce));
        paramMap.put("timestamp", Integer.parseInt(timestamp));
        paramMap.put("version", "1.0");
        paramMap.put("app_id", Integer.parseInt(account.getTappid()));
        paramMap.put("ad_slot_type", 3);
        paramMap.put("ad_slot_name", vo.getAd_slot_name());
        paramMap.put("orientation", BlankUtils.getInt(vo.getOrientation()));
        paramMap.put("use_mediation", vo.getUse_mediation());
        //竞价类型 1) 可选值：0（标准）、1（服务端竞价）
        if (!BlankUtils.checkBlank(vo.getBidding_type())) {
            paramMap.put("bidding_type", Integer.parseInt(vo.getBidding_type()));
        }
        //cpm
        if (!BlankUtils.checkBlank(vo.getCpm())) {
            if (!isNumber(vo.getCpm()) || new BigDecimal(vo.getCpm()).compareTo(new BigDecimal("3000")) > 1) {
                ret.put("msg", "cpm输入不合法,账号为⼈⺠币结算上限为3000，⽀持 ⼩数点如：100.01，该参数必须⼤于0");
                return ret;
            }
            paramMap.put("cpm", vo.getCpm());
        }

        //渲染方式 1模板渲染 3 ⾮原⽣
        paramMap.put("render_type", Integer.parseInt(vo.getRender_type()));
        //是否使⽤点睛样式
        if (!BlankUtils.checkBlank(vo.getUse_icon())) {
            paramMap.put("use_icon", Integer.parseInt(vo.getUse_icon()));
        }
        paramMap.put("sign", sign(timestamp, nonce, account.getTsecrect()));
        String result = HttpRequest.httpPostJson(CSJ_CODE_CREATE_URL, paramMap, headMap);
        logger.info("csj kp requst:{}, ret:{}", JSONObject.toJSONString(paramMap), result);
        if (!BlankUtils.checkBlank(result)) {
            JSONObject json = JSONObject.parseObject(result);
            if ("0".equals(json.getString("code"))) {
                JSONObject data = JSONObject.parseObject(json.getString("data"));
                ret.put("ret", 1);
                ret.put("ad_slot_id", data.getString("ad_slot_id"));
                ret.put("status", data.getString("status"));
            } else {
                ret.put("msg", json.getString("message"));
                logger.info("kpAdcode fail:" + JSON.toJSONString(vo) + "=====" + json.getString("message"));
            }
        }
        return ret;
    }

    /**
     * 新插屏广告位
     *
     * @param vo
     * @return
     */
    private JSONObject xcpAdcode(CSJAdcodeVo vo, AdCodeAccountVo account) {
        JSONObject ret = new JSONObject();
        ret.put("ret", 0);

        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");
        Map<String, Object> paramMap = new HashMap<>();
        String timestamp = System.currentTimeMillis() / 1000 + "";
        String nonce = String.valueOf(new Random().nextInt(10));

        paramMap.put("user_id", Integer.parseInt(account.getTaccountid()));
        paramMap.put("role_id", Integer.parseInt(account.getTaccountid()));
        paramMap.put("nonce", Integer.parseInt(nonce));
        paramMap.put("timestamp", Integer.parseInt(timestamp));
        paramMap.put("version", "1.0");
        paramMap.put("app_id", Integer.parseInt(account.getTappid()));
        paramMap.put("ad_slot_type", 9);
        paramMap.put("ad_slot_name", vo.getAd_slot_name());
        paramMap.put("use_mediation", vo.getUse_mediation());
        //竞价类型 1) 可选值：0（标准）、1（服务端竞价）
        if (!BlankUtils.checkBlank(vo.getBidding_type())) {
            paramMap.put("bidding_type", Integer.parseInt(vo.getBidding_type()));
        }
        //cpm
        if (!BlankUtils.checkBlank(vo.getCpm())) {
            if (!isNumber(vo.getCpm()) || new BigDecimal(vo.getCpm()).compareTo(new BigDecimal("3000")) > 1) {
                ret.put("msg", "cpm输入不合法,账号为⼈⺠币结算上限为3000，⽀持 ⼩数点如：100.01，该参数必须⼤于0");
                return ret;
            }
            paramMap.put("cpm", vo.getCpm());
        }
        //渲染方式 1模板渲染 3 ⾮原⽣
        if ("2".equals(vo.getAd_rollout_size())) {
            if ("3".equals(vo.getAd_rollout_size())) {
                ret.put("msg", "⼴告铺开⼤⼩为全屏时⼊参为1模板渲染，3⾮原⽣；⼴告铺开⼤⼩为半屏时只接收1模板渲染");
                return ret;
            }
        }
        paramMap.put("render_type", Integer.parseInt(vo.getRender_type()));
        //⼴告铺开⼤⼩ 1是全屏，2是半屏
        paramMap.put("ad_rollout_size", Integer.parseInt(vo.getAd_rollout_size()));

        //⼴告素材类型
        paramMap.put("accept_material_type", Integer.parseInt(vo.getAccept_material_type()));
        //视频播放⽅向/屏幕 ⽅向
        paramMap.put("orientation", Integer.parseInt(vo.getOrientation()));
        //n秒后显⽰跳过按钮 全屏数值范围为5-15s；插屏数值范围为0-15s
        Integer skipDuration = Integer.parseInt(vo.getSkip_duration());
        if ("1".equals(vo.getAd_rollout_size())) {
            if (skipDuration < 5 || skipDuration > 15) {
                ret.put("msg", "全屏数值范围为5-15s；插屏数值范围为0-15s");
                return ret;
            }
        } else if ("2".equals(vo.getAd_rollout_size())) {
            if (skipDuration < 0 || skipDuration > 15) {
                ret.put("msg", "全屏数值范围为5-15s；插屏数值范围为0-15s");
                return ret;
            }
        }
        paramMap.put("skip_duration", skipDuration);
        if (!BlankUtils.checkBlank(vo.getUse_endcard())) {
            paramMap.put("use_endcard", Integer.parseInt(vo.getUse_endcard()));
        }

        if (BlankUtils.isNotBlank("video_voice_control")) {
            paramMap.put("video_voice_control", vo.getVideo_voice_control());
        }

        paramMap.put("sign", sign(timestamp, nonce, account.getTsecrect()));
        String result = HttpRequest.httpPostJson(CSJ_CODE_CREATE_URL, paramMap, headMap);
//        logger.info("csj xcp result:" + result);
        if (!BlankUtils.checkBlank(result)) {
            JSONObject json = JSONObject.parseObject(result);
            if ("0".equals(json.getString("code"))) {
                JSONObject data = JSONObject.parseObject(json.getString("data"));
                ret.put("ret", 1);
                ret.put("ad_slot_id", data.getString("ad_slot_id"));
                ret.put("status", data.getString("status"));
            } else {
                ret.put("msg", json.getString("message"));
                logger.info("xcpAdcode fail:" + JSON.toJSONString(vo) + "=====" + json.getString("message"));
            }
        }
        return ret;
    }

    /**
     * 激励视频广告位
     *
     * @param vo
     * @return
     */
    private JSONObject jlspAdcode(CSJAdcodeVo vo, AdCodeAccountVo account) {
        JSONObject ret = new JSONObject();
        ret.put("ret", 0);

        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");
        Map<String, Object> paramMap = new HashMap<>();
        String timestamp = System.currentTimeMillis() / 1000 + "";
        String nonce = String.valueOf(new Random().nextInt(10));

        paramMap.put("user_id", Integer.parseInt(account.getTaccountid()));
        paramMap.put("role_id", Integer.parseInt(account.getTaccountid()));
        paramMap.put("nonce", Integer.parseInt(nonce));
        paramMap.put("timestamp", Integer.parseInt(timestamp));
        paramMap.put("version", "1.0");
        paramMap.put("app_id", Integer.parseInt(account.getTappid()));
        paramMap.put("ad_slot_type", 5);
        paramMap.put("ad_slot_name", vo.getAd_slot_name());
        paramMap.put("use_mediation", vo.getUse_mediation());
        //竞价类型 1) 可选值：0（标准）、1（服务端竞价）
        if (!BlankUtils.checkBlank(vo.getBidding_type())) {
            paramMap.put("bidding_type", Integer.parseInt(vo.getBidding_type()));
        }
        //cpm
        if (!BlankUtils.checkBlank(vo.getCpm())) {
            if (!isNumber(vo.getCpm()) || new BigDecimal(vo.getCpm()).compareTo(new BigDecimal("3000")) > 1) {
                ret.put("msg", "cpm输入不合法,账号为⼈⺠币结算上限为3000，⽀持 ⼩数点如：100.01，该参数必须⼤于0");
                return ret;
            }
            paramMap.put("cpm", vo.getCpm());
        }
        //渲染方式 1模板渲染 3 ⾮原⽣
        if ("2".equals(vo.getAd_rollout_size())) {
            if ("3".equals(vo.getAd_rollout_size())) {
                ret.put("msg", "⼴告铺开⼤⼩为全屏时⼊参为1模板渲染，3⾮原⽣；⼴告铺开⼤⼩为半屏时只接收1模板渲染");
                return ret;
            }
        }
        //固定值 1 模板渲染
        paramMap.put("render_type", 1);
        //视频播放⽅向 1（垂直）、2（⽔平）
        if (!BlankUtils.checkBlank(vo.getOrientation())) {
            paramMap.put("orientation", Integer.parseInt(vo.getOrientation()));
        }
        //奖励名称
        if (!BlankUtils.checkBlank(vo.getReward_name())) {
            paramMap.put("reward_name", vo.getReward_name());
        }
        //奖励数量
        if (!BlankUtils.checkBlank(vo.getReward_count())) {
            paramMap.put("reward_count", Integer.parseInt(vo.getReward_count()));
        }
        //奖励发放设置
        if (!BlankUtils.checkBlank(vo.getReward_is_callback())) {
            paramMap.put("reward_is_callback", Integer.parseInt(vo.getReward_is_callback()));
        }
        //回调url
        if ("1".equals(vo.getReward_is_callback())) {
            paramMap.put("reward_callback_url", vo.getReward_callback_url());
        }

        paramMap.put("sign", sign(timestamp, nonce, account.getTsecrect()));
        String result = HttpRequest.httpPostJson(CSJ_CODE_CREATE_URL, paramMap, headMap);
//        logger.info("csj jlsp result:" + result);
        if (!BlankUtils.checkBlank(result)) {
            JSONObject json = JSONObject.parseObject(result);
            if ("0".equals(json.getString("code"))) {
                JSONObject data = JSONObject.parseObject(json.getString("data"));
                ret.put("ret", 1);
                ret.put("ad_slot_id", data.getString("ad_slot_id"));
                ret.put("status", data.getString("status"));
            } else {
                ret.put("msg", json.getString("message"));
                logger.info("jlspAdcode fail:" + JSON.toJSONString(vo) + "=====" + json.getString("message"));
            }
        }
        return ret;
    }

    /**
     * 写入到广告配置表
     *
     * @return
     */
    public JSONObject saveAdcodeToDnExtendAdconfig(CSJAdcodeVo vo, AdCodeAccountVo account) {
        JSONObject ret = new JSONObject();
        try {
            String sql = "insert into dn_extend_adconfig(appid,is_newuser,user_group,adpos_type,strategy,adsid,statu,ecpm,priority,rate,createtime,lasttime,cuser) " +
                    "values(#{obj.appid},#{obj.is_newuser},#{obj.user_group},#{obj.adpos_type},#{obj.strategy},#{obj.adsid},#{obj.statu},#{obj.ecpm},#{obj.priority},#{obj.rate},now(),now(),#{obj.cuser}) ";

            if (!BlankUtils.checkBlank(vo.getChannel()) && !"csj".equals(vo.getChannel())) {
                sql = "insert into dn_extend_adconfig(appid,cha_id,is_newuser,user_group,adpos_type,strategy,adsid,statu,ecpm,priority,rate,createtime,lasttime,cuser) " +
                        "values(#{obj.appid},#{obj.cha_id},#{obj.is_newuser},#{obj.user_group},#{obj.adpos_type},#{obj.strategy},#{obj.adsid},#{obj.statu},#{obj.ecpm},#{obj.priority},#{obj.rate},now(),now(),#{obj.cuser}) ";
            }
            ExtendAdconfigVo config = new ExtendAdconfigVo();
            // 子渠道为 apple 则不填入
            config.setCha_id("apple".equals(vo.getChannel()) ? "" : vo.getChannel());
            config.setAppid(vo.getAppid());
            config.setIs_newuser("all");
            config.setUser_group("all");
            config.setAdpos_type(vo.getOpen_type());
            config.setStrategy(vo.getStrategy());

            String sdkAdType = vo.getSdk_ad_type();
            String appid = vo.getAppid();
            String adExtentionName = vo.getAdExtensionName();
            String adsid = prefix + "_" + sdkAdType + "_" + appid + "_" + adExtentionName;
            config.setAdsid(adsid);
            config.setStatu("1");
            //策略带rate 默认为0 其他默认值为3
            if (BlankUtils.checkBlank(vo.getCpm())) {
                config.setEcpm(new BigDecimal("0"));
            } else {
                if (vo.getStrategy().contains("rate")) {
                    config.setEcpm(new BigDecimal("0"));
                } else {
                    config.setEcpm(new BigDecimal(vo.getCpm()));
                }
            }
            //2022-09-01 开屏、banner、msg的无价格广告，在广告配置页面生成的预估ecpm默认为0；插屏、视频的无价格广告，在广告配置页面生成的预估ecpm默认为3
            String[] ecpm0 = {"splash", "banner", "msg"};
            if (Arrays.asList(ecpm0).contains(config.getAdpos_type())) {
                if (BlankUtils.checkBlank(vo.getCpm())) {
                    config.setEcpm(new BigDecimal("0"));
                }
            }

            String[] ecpm3 = {"plaque", "video"};
            if (Arrays.asList(ecpm3).contains(config.getAdpos_type())) {
                if (BlankUtils.checkBlank(vo.getCpm())) {
                    config.setEcpm(new BigDecimal("3"));
                }
            }


            config.setPriority(0);
            config.setRate(100);
            config.setCuser(vo.getCreateUser());
            int result = adv2Service.execSqlHandle(sql, config);
            if (result > 0) {
                ret.put("ret", "1");
                ret.put("msg", "ok");
            } else {
                ret.put("ret", "0");
                ret.put("ret", "写入广告配置表失败");
            }
        } catch (Exception e) {
            ret.put("ret", "0");
            ret.put("ret", "写入广告配置表失败");
            logger.error("saveAdcodeToDnExtendAdconfig error:", e);
        }
        return ret;
    }


    /**
     * @param timestamp
     * @param nonce
     * @param appsecrect
     * @return
     */
    private static String sign(String timestamp, String nonce, String appsecrect) {
        String sign = "";
        List<String> strings = Arrays.asList(appsecrect, timestamp, nonce);
        Collections.sort(strings);
        StringBuilder sb = new StringBuilder();
        for (String s : strings) {
            sb.append(s);
        }
        sign = DigestUtils.sha1Hex(sb.toString());
        return sign;
    }

    //金额验证 可带两位小数
    public static boolean isNumber(String str) {
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$"); // 判断小数点后2位的数字的正则表达式
        Matcher match = pattern.matcher(str);
        if (match.matches() == false) {
            return false;
        } else {
            return true;
        }
    }

    private CSJAdcodeVo getGameCSJAdcode(String[] vals) {
        CSJAdcodeVo ki = new CSJAdcodeVo();
        //应用
        ki.setAppid(vals[0]);
        //子渠道
        ki.setChannel(vals[1]);
        //sdk广告源样式 中文名称转成对应值
        String sdk_ad_type = SDK_AD_TYPE_MAP.get(vals[2]);
        ki.setSdk_ad_type(sdk_ad_type);
        //广告使用类型
        String open_type = OPEN_TYPE_MAP.get(vals[3]);
        ki.setOpen_type(open_type);
        //广告扩展名
        ki.setAdExtensionName(vals[4]);
        //备注
        ki.setRemark(vals[5]);
        //代码位名称
        ki.setAd_slot_name(vals[6]);
        //竞价类型 固定值 标准 0 服务端竞价 1 客户端竞价 2
        String bidding_type = BIDDING_TYPE_MAP.get(vals[7]);
        ki.setBidding_type(bidding_type);
        //是否使用M聚合 固定值 否 0 是 1
        ki.setUse_mediation(USE_MEDIATION_MAP.get(vals[8]));
        //cpm设置
        ki.setCpm(vals[9]);
        //广告类型 中文转换成对应值
        //广告平台 vals[10]
        //设置平台 tappid
        ki.setApp_id(vals[11]);

        String ad_slot_type = AD_SLOT_TYPE_MAP.get(vals[12]);
        ki.setAd_slot_type(ad_slot_type);
        //渲染方式 中文转换成对应值
        String render_type = RENDER_TYPE_MAP.get(vals[13]);
        ki.setRender_type(render_type);
        //广告样式 中文转换成对应值
        ki.setAd_categories(vals[14]);
        //模板样式 中文转换成对应值
        ki.setTpl_list(vals[15]);
        //素材类型 中文转成对应值
        ki.setAccept_material_type(vals[16]);
        //是否轮播 中文转成对应值 1非轮播 2轮播
        ki.setSlide_banner(vals[17]);
        //广告尺寸 600*400
        if (!BlankUtils.checkBlank(vals[18])) {
            String[] size = vals[18].split("\\*");
            ki.setWidth(size[0]);
            ki.setHeight(size[1]);
        }
        //点睛样式 中文转成对应值 是1 否0
        ki.setUse_icon(vals[19]);
        //视频播放方向
        ki.setOrientation(vals[20]);
        //奖励名称 中文转成对应
        ki.setReward_name(vals[21]);
        //奖励数量
        ki.setReward_count(vals[22]);
        //回调设置
        ki.setReward_is_callback(vals[23]);
        //回调url
        ki.setReward_callback_url(vals[24]);
        //广告铺开大小
        ki.setAd_rollout_size(vals[25]);
        //跳过
        ki.setSkip_duration(vals[26]);
        //endcard
        ki.setUse_endcard(vals[27]);
        //策略
        if (!BlankUtils.checkBlank(vals[28])) {
            ki.setStrategy(vals[28].trim());
        }
        //音频开关
//        ki.setVideo_voice_control(vals[29]);
        return ki;
    }

    private CSJAdcodeVo getToolCSJAdcode(String[] vals) {
        CSJAdcodeVo ki = new CSJAdcodeVo();
        //应用
        ki.setAppid(vals[0]);
        //子渠道
        ki.setChannel(vals[1]);
        //sdk广告源样式 中文名称转成对应值
        String sdk_ad_type = SDK_AD_TYPE_MAP.get(vals[2]);
        ki.setSdk_ad_type(sdk_ad_type);
        //广告使用类型
        String open_type = OPEN_TYPE_MAP.get(vals[3]);
        ki.setOpen_type(open_type);
        //广告扩展名
        ki.setAdExtensionName(vals[4]);
        //备注
        ki.setRemark(vals[5]);
        //代码位名称
        ki.setAd_slot_name(vals[6]);
        //竞价类型 固定值 标准 0 服务端竞价 1 客户端竞价 2
        String bidding_type = BIDDING_TYPE_MAP.get(vals[7]);
        ki.setBidding_type(bidding_type);
        //是否使用M聚合 固定值 否 0 是 1
        ki.setUse_mediation(USE_MEDIATION_MAP.get(vals[8]));
        //cpm设置
        ki.setCpm(vals[9]);
        //广告类型 中文转换成对应值
        String ad_slot_type = AD_SLOT_TYPE_MAP.get(vals[11]);
        ki.setAd_slot_type(ad_slot_type);
        //渲染方式 中文转换成对应值
        String render_type = RENDER_TYPE_MAP.get(vals[12]);
        ki.setRender_type(render_type);
        //广告样式 中文转换成对应值
        ki.setAd_categories(vals[13]);
        //模板样式 中文转换成对应值
        ki.setTpl_list(vals[14]);
        //素材类型 中文转成对应值
        ki.setAccept_material_type(vals[15]);
        //是否轮播 中文转成对应值 1非轮播 2轮播
        ki.setSlide_banner(vals[16]);
        //广告尺寸 600*400
        if (!BlankUtils.checkBlank(vals[17])) {
            String[] size = vals[17].split("\\*");
            ki.setWidth(size[0]);
            ki.setHeight(size[1]);
        }
        //点睛样式 中文转成对应值 是1 否0
        ki.setUse_icon(vals[18]);
        //视频播放方向
        ki.setOrientation(vals[19]);
        //奖励名称 中文转成对应
        ki.setReward_name(vals[20]);
        //奖励数量
        ki.setReward_count(vals[21]);
        //回调设置
        ki.setReward_is_callback(vals[22]);
        //回调url
        ki.setReward_callback_url(vals[23]);
        //广告铺开大小
        ki.setAd_rollout_size(vals[24]);
        //跳过
        ki.setSkip_duration(vals[25]);
        //endcard
        ki.setUse_endcard(vals[26]);
        //策略
        if (!BlankUtils.checkBlank(vals[27])) {
            ki.setStrategy(vals[27].trim());
        }
        //音频开关
//        ki.setVideo_voice_control(vals[28]);
        return ki;
    }

    public static void main(String[] args) {
        JSONObject ret = new JSONObject();
        System.out.println((ret.getString("msg") == null ? "" : ret.getString("msg")) + "-写入广告配置表失败");

        String[] ecpm3 = {"plaque", "video"};
        String a = "video";
        if (Arrays.asList(ecpm3).contains(a)) {
            System.out.println("=====");
        }
    }

}
