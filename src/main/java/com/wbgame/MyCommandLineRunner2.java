package com.wbgame;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.wbgame.mapper.master.PartnerMapper;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.wbgame.mapper.adb.DnwxBiMapper;
import com.wbgame.mapper.adb.UserRangeMapper;
import com.wbgame.mapper.adb.UserRangeTwoMapper;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.service.AdService;
import com.wbgame.service.UmengUserDetailService;
import com.wbgame.service.WbSysService;
import com.wbgame.service.WorldService;
import com.wbgame.utils.CommonUtil;

//@Component
//@Order(0) // 数值越小越先执行
public class MyCommandLineRunner2 implements CommandLineRunner {

	Logger logger = LoggerFactory.getLogger(MyCommandLineRunner2.class);

    @Autowired
    private WorldService worldService;
    @Autowired
    private AdService adService;
    @Autowired
    private WbSysService wbSysService;
    @Autowired
    private DnwxBiMapper dnwxBiMapper;
    @Autowired
	private UmengUserDetailService umengService;
    
    @Autowired
    private YyhzMapper yyhzMapper;
    @Autowired
    private UserRangeMapper userRangeMapper;
    @Autowired
    private UserRangeTwoMapper userRangeTwoMapper;
	@Autowired
	private PartnerMapper partnerMapper;
    

    @Override
    public void run(String... args) throws Exception {
        System.out.println("初始化缓存数据...");

            CommonUtil.cMap = worldService.selectWxAppConfigMap();
            CommonUtil.gMap = worldService.selectWxIconGnameAll();
            CommonUtil.fdsMap = worldService.selectWxFdsConfig();
            CommonUtil.bonusMap = worldService.selectWxBonusConfig();
            CommonUtil.helpMap = worldService.selectWxHelpConfig();
            CommonUtil.switchMap = worldService.selectWxPowerSwitch();
            CommonUtil.pageSwitchMap = worldService.selectWxPageSwitch();
            CommonUtil.templateMap = worldService.selectWxCustomTemplate();
            CommonUtil.redpackMap = worldService.selectWxRedPackConfig();
            CommonUtil.mpPayMap = worldService.selectWxMpPayConfig();
            CommonUtil.msgList = worldService.selectWxMoneyMsgList();

            CommonUtil.userMap = wbSysService.selectWbSysUserAllMap();
            CommonUtil.appGroupMap = wbSysService.selectWbSysAppGroupMap();

            CommonUtil.waibaoUserMap = wbSysService.selectWaibaoUserAllMap();
            //数据校验百分比
            CommonUtil.adMsgPer = worldService.selectAdMsgPer();
            
            System.out.println("start...");

            System.out.println("end...");
            

        System.out.println("测试dk秘钥生成成功...");
        System.out.println("初始化缓存成功...");
    }
}