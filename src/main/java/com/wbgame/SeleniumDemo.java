//package com.wbgame;
//
//import B.FI;
//import com.alibaba.fastjson.JSONObject;
//import com.wbgame.utils.BlankUtils;
//
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.FileNotFoundException;
//import java.nio.file.Files;
//import java.nio.file.Path;
//import java.nio.file.Paths;
//import java.util.*;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//import java.util.stream.Collectors;
//
//import com.wbgame.utils.FileUtil;
//import com.wbgame.utils.FileUtils;
//import org.apache.commons.codec.digest.DigestUtils;
//import org.openqa.selenium.By;
//import org.openqa.selenium.Cookie;
//import org.openqa.selenium.WebDriver;
//import org.openqa.selenium.chrome.ChromeDriver;
//import org.openqa.selenium.chrome.ChromeOptions;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//
//
//public class SeleniumDemo {
//
//	public static Logger logger = LoggerFactory.getLogger(SeleniumDemo.class);
//
//	private static List<String> splitString(String input, int maxLength) {
//		List<String> splitList = new ArrayList<>();
//		int length = input.length();
//		int startIndex = 0;
//
//		while (startIndex < length) {
//			int endIndex = Math.min(startIndex + maxLength, length);
//			String substring = input.substring(startIndex, endIndex);
//			splitList.add(substring);
//			startIndex += maxLength;
//		}
//
//		return splitList;
//	}
//
//
//
//	public static void main(String[] args) throws FileNotFoundException {
////		testGetPageList();
////		WebDriver driver = getWebDriver();
////		driver.findElement(By.xpath("")).sendKeys("13750000022");
//
//
//		// SSL Handler Wrapper
////		final JA3SSLEngineWrapper ja3Wrapper = new JA3SSLEngineWrapper(sslEngine);
////		// Accessing the finger print
////		final String ja3ClientSignature = (String) sslSession.getValue(JA3Constants.JA3_FINGERPRINT);
//
//
//
//		if(true){
//			return;
//		}
//		String content = "<p>&nbsp;</p> <div data-page-id=\"doxk42qURgM2cbdrmarhH1SfSGg\" data-docx-has-block-data=\"false\"> <div class=\"ace-line ace-line old-record-id-doxk4Aq6Cc2oM4iuMGavWCcMguc\" style=\"white-space: pre;\">尊敬的开发者，您好：</div> <div class=\"ace-line text-indent ace-line old-record-id-doxk4OSMQ4SYWEygOATICfPTZFe\" style=\"text-indent: 2em;\">为了米盟平台健康发展，我们会对合作应用的广告样式、数据等进行巡检。</div> <div class=\"ace-line text-indent ace-line old-record-id-doxk4AsKGMCI62s04clHybh6Dze\" style=\"text-indent: 2em;\">在近期巡检中发现您的应用（应用名称：空灵音符、APPID：2882303761520246559）<strong>不符合相关合作规范，存在虚假点击的风险</strong>，根据《小米移动广告联盟违规处理方法》https://dev.mi.com/distribute/doc/details?pId=1477 将对违规应用进行通知整改。</div> <div class=\"ace-line text-indent ace-line old-record-id-doxk4O6W2EmmWE8I2kpwRpJ9ypc\" style=\"text-indent: 2em;\">您的应用需在<strong>收到邮件起的14个工作日内完成整改并在小米开放平台提审</strong>，同时通过<strong>邮件的方式通知米盟</strong>整改完成（邮件格式见下文）。逾期未通过（含未处理）整改，我们将会对应用进行封禁处罚，届时会停止对贵司该款应用下发广告。</div> <div class=\"ace-line ace-line old-record-id-doxk4YOG00q0yG0aqo3oPOkWhuh\" style=\"white-space: pre;\">&nbsp;</div> <div class=\"ace-line ace-line old-record-id-doxk4cESsCEa0CmAwiU5nu44jBd\" style=\"white-space: pre;\"><strong>整改完成后邮件通知格式：</strong></div> <div class=\"ace-line ace-line old-record-id-doxk4Y6UcSQy6mYs6gRIDzRKH4f\" style=\"white-space: pre;\">主标题：【米盟应用违规整改完成】-公司名称-应用名称</div> <div class=\"ace-line ace-line old-record-id-doxk4kwQMWM6ewa42kr2JhD1FEd\" style=\"white-space: pre;\">收件人：<EMAIL></div> <div class=\"ace-line ace-line old-record-id-doxk4Umc0gEgygqqM0SJV2t4LPe\" style=\"white-space: pre;\">APPID:</div> <div class=\"ace-line ace-line old-record-id-doxk4c6eeO8MOYIeigdCf0MIRfd\" style=\"white-space: pre;\">包名：</div> <div class=\"ace-line ace-line old-record-id-doxk4yEusUykcoIU4IhBOmfyT3c\" style=\"white-space: pre;\">开发者ID：</div> <div class=\"ace-line ace-line old-record-id-doxk4SQE2SwK886AYQX4cQYcdud\" style=\"white-space: pre;\">开发者名称：</div> <div class=\"ace-line ace-line old-record-id-doxk4KiGIuigEmWUMgjMXBeGLgc\" style=\"white-space: pre;\">应用类型：APK游戏/RPK游戏/APK应用/RPK应用</div> </div>";
//		String name = "";
//		Matcher matcher = Pattern.compile("应用名称：(.*?)，APPID").matcher(content);
//		while (matcher.find()) {
//			name = matcher.group(1);
//		}
//		System.out.println("1 name=="+name);
//		if(BlankUtils.checkBlank(name)){
//			Matcher matcherB = Pattern.compile("应用名称：(.*?)、APPID").matcher(content);
//			while (matcherB.find()) {
//				name = matcherB.group(1);
//			}
//		}
//		System.out.println("2 name=="+name);
//	}
//
//	public static String getAccessCookies() {
//		WebDriver driver = getWebDriver();
//		driver.get("https://dev.mi.com/distribute?userId=1451992103");
//		driver.findElement(By.xpath("//*[@id=\"rc-tabs-0-panel-login\"]/form/div[1]/div[1]/div[2]/div/div/div/div/input ")).sendKeys("13750000022");
//		driver.findElement(By.xpath("//*[@id=\"rc-tabs-0-panel-login\"]/form/div[1]/div[2]/div/div[1]/div/input ")).sendKeys("fnyy1602");
//		driver.findElement(By.xpath("//*[@id=\"rc-tabs-0-panel-login\"]/form/div[1]/div[3]/label/span[1]/input")).click();
//
//		// 点击登录
//		driver.findElement(By.xpath("//*[@id=\"rc-tabs-0-panel-login\"]/form/div[1]/button")).click();
//		try {
//			Thread.sleep(3000L);
//		} catch (InterruptedException e) {
//			throw new RuntimeException(e);
//		}
//
//		List<String> list = new ArrayList<String>();
//		Set<Cookie> cookies = driver.manage().getCookies();
//		for (Cookie cookie : cookies) {
//			System.out.println(JSONObject.toJSONString(cookie));
//			list.add(cookie.getName()+"="+cookie.getValue());
//		}
//		String token = String.join("; ", list);
//		System.out.println("token=="+token);
//		return token;
//	}
//
//	public static WebDriver getWebDriver() {
//		//驱动地址
////		System.setProperty("webdriver.chrome.driver", "D:\\chromedriver.exe");
//
//		System.setProperty("webdriver.chrome.driver", "D:\\msedgedriver.exe");
//		ChromeOptions chromeOptions = new ChromeOptions();
//		//不显示图片
//		chromeOptions.addArguments("blink-settings=imagesEnabled=false");
//		//模拟浏览器高度宽度
//		chromeOptions.addArguments("-window-size=1920,1080");
//		chromeOptions.addArguments("--start-maximized");
//		//打开或者关闭浏览器视图
//		chromeOptions.addArguments("--no-sandbox");
//		chromeOptions.addArguments("--disable-dev-shm-usage");
//		chromeOptions.addArguments("--disable-gpu");
//		chromeOptions.addArguments("--headless");
//		return new ChromeDriver(chromeOptions);
//
//		//防止被检测
////		chromeOptions.setExperimentalOption("excludeSwitches",             Collections.singletonList("enable-automation"));
////		//开启代理
////		chromeOptions.addArguments("--proxy-server=http://" + "58.253.158.198:9999");
////		//修改下载路径
////		Map<String, Object> chromePrefs = new HashMap<>();
////		chromePrefs.put("download.default_directory", "D:\\download");
////		chromeOptions.setExperimentalOption("prefs", chromePrefs);
////		DesiredCapabilities caps = new DesiredCapabilities();
////		caps.setCapability(ChromeOptions.CAPABILITY, chromeOptions);
//
//	}
//
//}
