package com.wbgame.task.game;

import com.wbgame.pojo.game.config.query.AICustomConfigRequestParam;
import com.wbgame.pojo.game.config.response.AICustomConfigResponse;
import com.wbgame.pojo.game.report.query.AiCustomRequestParam;
import com.wbgame.pojo.game.report.response.AICustomResponse;
import com.wbgame.service.game.GameCommunityService;
import com.wbgame.utils.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/8 21:05
 * @description GameWarnTask
 */
@Slf4j
@Component
@RequestMapping("gameWarn")
public class GameWarnTask {

    private static final String LARK_CHAT_GROUP_ID = "oc_29230bfc18d512bbb89ec04e0e23b14a";

    public static final String DNWX_FEISHU_GROUP_MSG_URL = "https://edc.vigame.cn:6115/fs/sendGroupMsg";

    @Autowired
    private GameCommunityService communityService;

    /**
     * 同步AI客服消息日志
     */
    @Scheduled(cron = "00 30 */1 * * ?")
    public void syncAICustomData(){
        log.info("syncAICustomData start");

        DateTime now = DateTime.now();
        String start_date = now.plusHours(-1).toString("yyyy-MM-dd HH:00:00");
        String end_date = now.toString("yyyy-MM-dd HH:00:00");

        // 查询数据
        List<AICustomResponse> list = communityService.selectAICustomListOrigin(start_date,end_date);
        if (!list.isEmpty()){
            // 写入数据
            communityService.insertAICustomListBatch(list);
        }
        log.info("syncAICustomData end");
    }

    /**
     * 同步AI客服消息日志
     */
    @Scheduled(cron = "0 0 09 * * ?")
    @RequestMapping("aiCustomWarnDayTask")
    public void aiCustomWarnDayTask(){
        log.info("aiCustomWarnDayTask start");
        // 获取告警配置
        List<AICustomConfigResponse> warnConfigList = communityService.selectAICustomConfigList(new AICustomConfigRequestParam());
        if (!warnConfigList.isEmpty()){
            // 如果是前一天时间段告警
            for (AICustomConfigResponse each:warnConfigList){
                DateTime now = DateTime.now();
                String start_date = now.plusDays(-1).toString("yyyy-MM-dd 00:00:00");
                String end_date = now.toString("yyyy-MM-dd 00:00:00");

                AiCustomRequestParam param = new AiCustomRequestParam();
                param.setStart_date(start_date);
                param.setEnd_date(end_date);

                param.setOperator_status("0");
                param.setConfig(each);

                List<AICustomResponse> dayResultList = communityService.selectAiCustomListNew(param);
                if (!dayResultList.isEmpty()) {
                    eachDayWarn(dayResultList, param,each.getChat_id());
                }
            }

        }
        log.info("aiCustomWarnDayTask end");

    }




    /**
     * 同步AI客服消息日志
     */
    @Scheduled(cron = "00 35 */1 * * ?")
    @RequestMapping("aiCustomWarnHourTask")
    public void aiCustomWarnHourTask(){
        log.info("aiCustomWarnHourTask start");
        // 获取告警配置
        List<AICustomConfigResponse> warnConfigList = communityService.selectAICustomConfigList(new AICustomConfigRequestParam());
        if (!warnConfigList.isEmpty()){
            for (AICustomConfigResponse each:warnConfigList){
                DateTime now = DateTime.now();
                String start_date = now.plusHours(-1).toString("yyyy-MM-dd HH:00:00");
                String end_date = now.toString("yyyy-MM-dd HH:00:00");
                // 先处理当前时间段数据
                AiCustomRequestParam param = new AiCustomRequestParam();
                param.setStart_date(start_date);
                param.setEnd_date(end_date);
                param.setOperator_status("0");
                param.setConfig(each);

                List<AICustomResponse> resultList = communityService.selectAiCustomListNew(param);
                if (!resultList.isEmpty()){
                    eachHourWarn(resultList,param,each.getChat_id());
                }
            }
        }
        log.info("aiCustomWarnHourTask end");
    }

    private void eachHourWarn(List<AICustomResponse> list,AiCustomRequestParam param,String chatId){
        for (AICustomResponse each:list){
            String msg = param.getConfig().getApp_name().concat("用户问题告警：每小时推送前一小时用户问题").concat("\\n")
                    .concat("日期: ").concat(param.getStart_date()).concat("-").concat(param.getEnd_date()).concat("\\n")
                    .concat("提问: ").concat(each.getUser_question()).concat("\\n")
                    .concat("机器人回复: ").concat(each.getBot_answer()).concat("\\n")
                    .concat("提交时间: ").concat(each.getCommit_date()).concat("\\n")
                    .concat("问题类型: ").concat(each.getQuestion_type()).concat("\\n")
                    .concat("usercode: ").concat(each.getUsercode()).concat("\\n")
                    .concat("userid: ").concat(each.getUser_id()).concat("\\n")
                    .concat("用户分R标签: ").concat(each.getTag_r()).concat("\\n")
                    .concat("历史累充金额: ").concat(each.getAcc_iap_revenue()).concat("元").concat("\\n")
                    .concat("近七日累充金额: ").concat(each.getAcc_iap_revenue_7()).concat("元").concat("\\n")
                    .concat("近三十日累充金额: ").concat(each.getAcc_iap_revenue_30()).concat("元").concat("\\n")
                    .concat("三十日内活跃天数: ").concat(each.getDay_of_30_active()).concat("天").concat("\\n")
                    .concat("七日内活跃天数: ").concat(each.getDay_of_7_active()).concat("天").concat("\\n")
                    .concat("近七日累计活跃时长: ").concat(each.getActive_time()).concat("分钟").concat("\\n")
                    .concat("近七日平均活跃时长: ").concat(each.getAvg_active_time_7()).concat("分钟").concat("\\n");
            sendFSWarnMsg(msg,chatId,"");
        }
    }

    private void eachDayWarn(List<AICustomResponse> list,AiCustomRequestParam param,String chatId){
        for (AICustomResponse each:list){
            String msg = param.getConfig().getApp_name().concat("用户问题告警：每天9点发送前一天未处理问题").concat("\\n")
                    .concat("日期: ").concat(param.getStart_date().substring(0,10)).concat("\\n")
                    .concat("提问: ").concat(each.getUser_question()).concat("\\n")
                    .concat("机器人回复: ").concat(each.getBot_answer()).concat("\\n")
                    .concat("提交时间: ").concat(each.getCommit_date()).concat("\\n")
                    .concat("问题类型: ").concat(each.getQuestion_type()).concat("\\n")
                    .concat("usercode: ").concat(each.getUsercode()).concat("\\n")
                    .concat("userid: ").concat(each.getUser_id()).concat("\\n")
                    .concat("用户分R标签: ").concat(each.getTag_r()).concat("\\n")
                    .concat("历史累充金额: ").concat(each.getAcc_iap_revenue()).concat("\\n")
                    .concat("近七日累充金额: ").concat(each.getAcc_iap_revenue_7()).concat("\\n")
                    .concat("近三十日累充金额: ").concat(each.getAcc_iap_revenue_30()).concat("\\n")
                    .concat("三十日内活跃天数: ").concat(each.getDay_of_30_active()).concat("\\n")
                    .concat("七日内活跃天数: ").concat(each.getDay_of_7_active()).concat("\\n")
                    .concat("近七日累计活跃时长: ").concat(each.getActive_time()).concat("\\n")
                    .concat("近七日平均活跃时长: ").concat(each.getAvg_active_time_7()).concat("\\n");
            sendFSWarnMsg(msg,chatId,"");
        }
    }

    /**
     * 向指定群发送消息
     * @param message
     * @param chatId
     * @param userName
     * @return
     */
    public static boolean sendFSWarnMsg(String message,String chatId,String userName){
        boolean send = false;
        message = Base64.encodeBase64String(message.getBytes());
        Map<String,String> paramsMap = new HashMap<>();
        paramsMap.put("msg",message);
        paramsMap.put("chatId",chatId);
        paramsMap.put("userName",userName);
        paramsMap.put("robot","robot5");

        String body = HttpRequest.sendPost(DNWX_FEISHU_GROUP_MSG_URL,paramsMap);
        log.info("sendFSWarnMsg result = {}",body);
        return send;
    }

    public static void main(String[] args) {
        String start_date = DateTime.now().plusDays(-1).toString("yyyy-MM-dd 00:00:00");
        System.out.println(start_date.substring(0,10));
    }

}
