package com.wbgame.task;

import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.*;

import com.wbgame.mapper.redpack.RedpackAdMapper;
import com.wbgame.mapper.slave2.AdMsgMapper;
import com.wbgame.mapper.slave2.wz.WzRewardConfigMapper;
import com.wbgame.pojo.adv2.wz.HomeWzTiwaiConfig;
import org.apache.commons.codec.Encoder;
import org.apache.commons.codec.binary.Base64;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.jdbc.core.namedparam.SqlParameterSourceUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.wbgame.mapper.haiwaiad.HaiwaiCfgMapper;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.service.CustomService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;
import com.wbgame.utils.HttpClientUtils;

@Component
public class HaiwaiAdTask {

	Logger logger = LoggerFactory.getLogger(HaiwaiAdTask.class);

	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private YyhzMapper yyhzMapper;
	@Autowired
    private HaiwaiCfgMapper haiwaiCfgMapper;
	@Autowired
    private AdMsgMapper adMsgMapper;
	@Autowired
	private WzRewardConfigMapper wzRewardConfigMapper;


	public static String haiwaiFilter = "(appid in (select id from app_info where app_category in (15,16,20)) or cha_id='appleOversea' or cha_id='google_huawei')";

//	@Scheduled(cron="00 */30 * * * ?")
	public void syncAppInfo(){

		logger.info("执行syncAppInfo 开始...");

		/** app_info产品配置表 */
		try {
			String query = "select * from yyhz_0308.app_info ";
			List<Map<String, String>> list = yyhzMapper.queryListMapOne(query);

			if(list != null && list.size() > 0) {

				Map<String, Object> paramMap3 = new HashMap<String, Object>();
				paramMap3.put("sql1", "replace into dnwx_cfg.app_info(id,channel_id,app_id,app_name,create_time,sync_umeng,umeng_key,app_category,umeng_account,find_vals,os_type,xyx_id,reyun_key,bus_category,two_app_category) values ");
				paramMap3.put("sql2", " (#{li.id},#{li.channel_id},#{li.app_id},#{li.app_name},#{li.create_time},#{li.sync_umeng},#{li.umeng_key},#{li.app_category},#{li.umeng_account},#{li.find_vals},#{li.os_type},#{li.xyx_id},#{li.reyun_key},#{li.bus_category},#{li.two_app_category}) ");
				paramMap3.put("sql3", " ");
				paramMap3.put("list", list);
				haiwaiCfgMapper.batchExecSql(paramMap3);

				logger.info("执行syncAppInfo " +list.size()+ "条");
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("执行syncAppInfo 异常...");
		}
	}

	@Scheduled(cron="00 */5 * * * ?")
	public void syncHaiwaiAdconfig(){

		logger.info("执行syncHaiwaiAdconfig 开始...");
		String before = DateTime.now().minusMinutes(20).toString("yyyy-MM-dd HH:mm:ss");
		boolean isHour = (DateTime.now().getMinuteOfHour() == 0);

		boolean flag = true;
		/** 广告配置 */
		try {
			String query = "select * from dnwx_cfg.dn_extend_adconfig where "+haiwaiFilter;
			List<Map<String, String>> list = yyhzMapper.queryListMapOne(query);
			
			if(list != null && list.size() > 0) {

				// 判断当前为整点时间，则进行删除覆盖
				if (isHour) {
					String del = "delete from dnwx_cfg.dn_extend_adconfig where 1=1";
					haiwaiCfgMapper.execSql(del);
				}

				
				Map<String, Object> paramMap3 = new HashMap<String, Object>();
				paramMap3.put("sql1", "replace into dnwx_cfg.dn_extend_adconfig(id,appid,cha_id,prjid,buy_id,buy_act,is_newuser,user_group,adpos_type,strategy,adsid,statu,ecpm,priority,rate,createtime,lasttime,cuser) values ");
				paramMap3.put("sql2", " (#{li.id},#{li.appid},#{li.cha_id},#{li.prjid},#{li.buy_id},#{li.buy_act},#{li.is_newuser},#{li.user_group},#{li.adpos_type},#{li.strategy},#{li.adsid},#{li.statu},#{li.ecpm},#{li.priority},#{li.rate},#{li.createtime},#{li.lasttime},#{li.cuser}) ");
				paramMap3.put("sql3", " ");
				paramMap3.put("list", list);
				haiwaiCfgMapper.batchExecSql(paramMap3);
				
				logger.info("执行syncHaiwaiAdconfig1 " +list.size()+ "条");
			}
			
		} catch (Exception e) {
			flag = false;
			e.printStackTrace();
			logger.info("执行syncHaiwaiAdconfig1 异常...");
		}
		
		/** 广告源管理 */
		try {
			String query = "select * from dnwx_cfg.dn_extend_adsid_manage where "+haiwaiFilter;
			List<Map<String, String>> list = yyhzMapper.queryListMapOne(query);
			
			if(list != null && list.size() > 0) {

				// 判断当前为整点时间，则进行删除覆盖
				if (isHour) {
					String del = "delete from dnwx_cfg.dn_extend_adsid_manage where 1=1";
					haiwaiCfgMapper.execSql(del);
				}
				
				Map<String, Object> paramMap3 = new HashMap<String, Object>();
				paramMap3.put("sql1", "replace into dnwx_cfg.dn_extend_adsid_manage(adsid,agent,sdk_code,sdk_appid,sdk_appkey,sdk_adtype,adpos_type,open_type,note,appid,cha_id,createtime,lasttime,cuser,cpmFloor,bidding,unit_id) values ");
				paramMap3.put("sql2", " (#{li.adsid},#{li.agent},#{li.sdk_code},#{li.sdk_appid},#{li.sdk_appkey},#{li.sdk_adtype},#{li.adpos_type},#{li.open_type},#{li.note},#{li.appid},#{li.cha_id},#{li.createtime},#{li.lasttime},#{li.cuser},#{li.cpmFloor},#{li.bidding},#{li.unit_id}) ");
				paramMap3.put("sql3", " ");
				paramMap3.put("list", list);
				haiwaiCfgMapper.batchExecSql(paramMap3);
				
				logger.info("执行syncHaiwaiAdconfig2 " +list.size()+ "条");
			}
			
		} catch (Exception e) {
			flag = false;
			e.printStackTrace();
			logger.info("执行syncHaiwaiAdconfig2 异常...");
		}
		
		/** 广告位管理 */
		try {
			String query = "select * from dnwx_cfg.dn_extend_adpos_manage where "+haiwaiFilter;
			List<Map<String, String>> list = yyhzMapper.queryListMapOne(query);
			
			if(list != null && list.size() > 0) {

				// 判断当前为整点时间，则进行删除覆盖
				if (isHour) {
					String del = "delete from dnwx_cfg.dn_extend_adpos_manage where 1=1";
					haiwaiCfgMapper.execSql(del);
				}
				
				Map<String, Object> paramMap3 = new HashMap<String, Object>();
				paramMap3.put("sql1", "replace into dnwx_cfg.dn_extend_adpos_manage(id,appid,cha_id,prjid,user_group,adpos_type,adpos,strategy,adstyle,showrate,statu,note,delaySecond,startLv,endLv,lvInterval,xdelay,autoInterval,`out`,createtime,lasttime,cuser,buy_act,extraparam,custom_param) values ");
				paramMap3.put("sql2", " (#{li.id},#{li.appid},#{li.cha_id},#{li.prjid},#{li.user_group},#{li.adpos_type},#{li.adpos},#{li.strategy},#{li.adstyle},#{li.showrate},#{li.statu},#{li.note},#{li.delaySecond},#{li.startLv},#{li.endLv},#{li.lvInterval},#{li.xdelay},#{li.autoInterval},#{li.out},#{li.createtime},#{li.lasttime},#{li.cuser},#{li.buy_act},#{li.extraparam},#{li.custom_param}) ");
				paramMap3.put("sql3", " ");
				paramMap3.put("list", list);
				haiwaiCfgMapper.batchExecSql(paramMap3);
				
				logger.info("执行syncHaiwaiAdconfig3 " +list.size()+ "条");
			}
			
		} catch (Exception e) {
			flag = false;
			e.printStackTrace();
			logger.info("执行syncHaiwaiAdconfig3 异常...");
		}

		/** 广告策略设置-新 */
		try {
			String query = "select * from dnwx_cfg.dn_extend_adinfo_manage_new where "+haiwaiFilter;
			List<Map<String, String>> list = yyhzMapper.queryListMapOne(query);

			if(list != null && list.size() > 0) {

				if(isHour) {
					String del = "delete from dnwx_cfg.dn_extend_adinfo_manage_new where 1=1";
					haiwaiCfgMapper.execSql(del);
				}

				Map<String, Object> paramMap3 = new HashMap<String, Object>();
				paramMap3.put("sql1", "replace into dnwx_cfg.dn_extend_adinfo_manage_new(id,appid,cha_id,prjid,buy_id,buy_act,user_group,shield_name,policy_name,statu,cuser,createtime,euser,endtime,ad_type,reLoadInterval,checkInvalidInterval,plaque_interval,banner_interval,msg_interval,icon_interval,params,a_banner) values ");
				paramMap3.put("sql2", " (#{li.id},#{li.appid},#{li.cha_id},#{li.prjid},#{li.buy_id},#{li.buy_act},#{li.user_group},#{li.shield_name},#{li.policy_name},#{li.statu},#{li.cuser},#{li.createtime},#{li.euser},#{li.endtime},#{li.ad_type},#{li.reLoadInterval},#{li.checkInvalidInterval},#{li.plaque_interval},#{li.banner_interval},#{li.msg_interval},#{li.icon_interval},#{li.params},#{li.a_banner}) ");
				paramMap3.put("sql3", " ");
				paramMap3.put("list", list);
				haiwaiCfgMapper.batchExecSql(paramMap3);

				logger.info("执行syncHaiwaiAdconfig6 " +list.size()+ "条");
			}

		} catch (Exception e) {
			flag = false;
			e.printStackTrace();
			logger.info("执行syncHaiwaiAdconfig6 异常...");
		}
		/** 广告屏蔽策略管理-新 */
		try {
			String query = "select * from dnwx_cfg.dn_extend_shield_manage where 1=1 ";
			List<Map<String, String>> list = yyhzMapper.queryListMapOne(query);

			if(list != null && list.size() > 0) {

				if(isHour) {
					String del = "delete from dnwx_cfg.dn_extend_shield_manage where 1=1 ";
					haiwaiCfgMapper.execSql(del);
				}

				Map<String, Object> paramMap3 = new HashMap<String, Object>();
				paramMap3.put("sql1", "replace into dnwx_cfg.dn_extend_shield_manage(shield_name,shield_addr,shield_manu,shield_net,shield_ip,shield_num,splash_shield_addr,video_shield_addr,msg_shield_addr,banner_shield_addr,plaque_shield_addr,icon_shield_addr,note) values ");
				paramMap3.put("sql2", " (#{li.shield_name},#{li.shield_addr},#{li.shield_manu},#{li.shield_net},#{li.shield_ip},#{li.shield_num},#{li.splash_shield_addr},#{li.video_shield_addr},#{li.msg_shield_addr},#{li.banner_shield_addr},#{li.plaque_shield_addr},#{li.icon_shield_addr},#{li.note}) ");
				paramMap3.put("sql3", " ");
				paramMap3.put("list", list);
				haiwaiCfgMapper.batchExecSql(paramMap3);

				logger.info("执行syncHaiwaiAdconfig7 " +list.size()+ "条");
			}

		} catch (Exception e) {
			flag = false;
			e.printStackTrace();
			logger.info("执行syncHaiwaiAdconfig7 异常...");
		}

		if(flag) {
			try {
				String url = "https://edc.vigame.cn:6115/recacheHaiwaiWjy?mapid=300";
				String url1 = "https://edc.vigame.cn:6115/recacheHaiwaiWjy?mapid=301";
				String url2 = "https://edc.vigame.cn:6115/recacheHaiwaiWjy?mapid=302";

				HttpClientUtils.getInstance().httpGet(url);
				HttpClientUtils.getInstance().httpGet(url1);
				HttpClientUtils.getInstance().httpGet(url2);
				logger.info("执行syncHaiwaiAdconfig 刷新缓存...");
			} catch (Exception e) {
				e.printStackTrace();
				logger.info("执行syncHaiwaiAdconfig 刷新缓存异常...");
			}
		}else{
			// 飞书通知
			String msg = "syncHaiwaiAdconfig同步异常，请及时检查！";
			sendFailMsg(msg);
		}
		logger.info("执行syncHaiwaiAdconfig 完成...");

	}

	public static void sendFailMsg(String msg) {
		Map<String,String> sendMap = new HashMap<>();
		sendMap.put("msg",msg);
		sendMap.put("uname","caow");
		sendMap.put("robot","robot5");
		HttpClientUtils.getInstance().httpPost("https://edc.vigame.cn:6115/fs/sendMsg", sendMap);
	}

	@Scheduled(cron="00 */20 * * * ?")
	public void syncHaiwaiAdconfigTwo(){
		logger.info("执行syncHaiwaiAdconfigTwo 开始...");
		String before = DateTime.now().minusMinutes(20).toString("yyyy-MM-dd HH:mm:ss");

		boolean flag = true;
		/** 广告策略 */
		try {
			String query = "select * from dnwx_cfg.dn_extend_strategy where 1=1";
			List<Map<String, String>> list = yyhzMapper.queryListMapOne(query);

			if(list != null && list.size() > 0) {

				String del = "delete from dnwx_cfg.dn_extend_strategy where 1=1";
				haiwaiCfgMapper.execSql(del);

				Map<String, Object> paramMap3 = new HashMap<String, Object>();
				paramMap3.put("sql1", "replace into dnwx_cfg.dn_extend_strategy(strategy,note,floor,adpos_type,`desc`,model,loadmodel,mulnum,loadmax,ctimeout,atimeout,`interval`,delay,showmax,clicklimit,clickmax,ltInterval,automodel,bidmodel,`loop`,ishigh,extra,cuser,euser,createtime,lasttime) values ");
				paramMap3.put("sql2", " (#{li.strategy},#{li.note},#{li.floor},#{li.adpos_type},#{li.desc},#{li.model},#{li.loadmodel},#{li.mulnum},#{li.loadmax},#{li.ctimeout},#{li.atimeout},#{li.interval},#{li.delay},#{li.showmax},#{li.clicklimit},#{li.clickmax},#{li.ltInterval},#{li.automodel},#{li.bidmodel},#{li.loop},#{li.ishigh},#{li.extra},#{li.cuser},#{li.euser},#{li.createtime},#{li.lasttime}) ");
				paramMap3.put("sql3", " ");
				paramMap3.put("list", list);
				haiwaiCfgMapper.batchExecSql(paramMap3);

				logger.info("执行syncHaiwaiAdconfig4 " +list.size()+ "条");
			}

		} catch (Exception e) {
			flag = false;
			e.printStackTrace();
			logger.info("执行syncHaiwaiAdconfig4 异常...");
		}

		// dn_extend_user_strategy_two、dn_extend_adinfo_manage_new、dn_extend_shield_manage 增加同步任务
		/** 用户群管理-新 */
		try {
			String query = "select * from dnwx_cfg.dn_extend_user_strategy_two where 1=1";
			List<Map<String, String>> list = yyhzMapper.queryListMapOne(query);

			if(list != null && list.size() > 0) {

				String del = "delete from dnwx_cfg.dn_extend_user_strategy_two where 1=1";
				haiwaiCfgMapper.execSql(del);

				Map<String, Object> paramMap3 = new HashMap<String, Object>();
				paramMap3.put("sql1", "replace into dnwx_cfg.dn_extend_user_strategy_two(id,sid,`group`,note,stype,param1,param2,param3,cuser,createtime,euser,endtime) values ");
				paramMap3.put("sql2", " (#{li.id},#{li.sid},#{li.group},#{li.note},#{li.stype},#{li.param1},#{li.param2},#{li.param3},#{li.cuser},#{li.createtime},#{li.euser},#{li.endtime}) ");
				paramMap3.put("sql3", " ");
				paramMap3.put("list", list);
				haiwaiCfgMapper.batchExecSql(paramMap3);

				logger.info("执行syncHaiwaiAdconfig5 " +list.size()+ "条");
			}

		} catch (Exception e) {
			flag = false;
			e.printStackTrace();
			logger.info("执行syncHaiwaiAdconfig5 异常...");
		}

		if(flag) {
			try {
				String url3 = "https://edc.vigame.cn:6115/recacheHaiwaiWjy?mapid=304";

				HttpClientUtils.getInstance().httpGet(url3);
				logger.info("执行syncHaiwaiAdconfigTwo 刷新缓存...");
			} catch (Exception e) {
				e.printStackTrace();
				logger.info("执行syncHaiwaiAdconfigTwo 刷新缓存异常...");
			}
		}else {
			// 飞书通知
			String msg = "syncHaiwaiAdconfigTwo同步异常，请及时检查！";
			sendFailMsg(msg);
		}
		logger.info("执行syncHaiwaiAdconfigTwo 完成...");
	}

	@Scheduled(cron="00 */10 * * * ?")
	public void syncHaiwaiReyunconfig() {

		logger.info("syncHaiwaiReyunconfig 开始...");
		String before = DateTime.now().minusMinutes(20).toString("yyyy-MM-dd HH:mm:ss");

		/** 热云深度事件配置 */
		try {
			String query = "select * from dnwx_reyun_config_three where "+haiwaiFilter;
			List<Map<String, String>> list = yyhzMapper.queryListMapOne(query);

			if (list != null && list.size() > 0) {

				String del = "delete from dnwx_cfg.dnwx_reyun_config_three where 1=1";
				haiwaiCfgMapper.execSql(del);


				Map<String, Object> paramMap3 = new HashMap<String, Object>();
				paramMap3.put("sql1", "replace into dnwx_cfg.dnwx_reyun_config_three(id,appid,cha_id,pid,buy_id,buy_act,`event`,eventType,times,`loop`,rate,adType,ecpmValue,ecpmType,action,timeType,checkTimes,levelType,`level`,statu,sdk_type,logic,`out`,action_params,rule_rate,cuser,createtime,euser,endtime) values ");
				paramMap3.put("sql2", " (#{li.id},#{li.appid},#{li.cha_id},#{li.pid},#{li.buy_id},#{li.buy_act},#{li.event},#{li.eventType},#{li.times},#{li.loop},#{li.rate},#{li.adType},#{li.ecpmValue},#{li.ecpmType},#{li.action},#{li.timeType},#{li.checkTimes},#{li.levelType},#{li.level},#{li.statu},#{li.sdk_type},#{li.logic},#{li.out},#{li.action_params},#{li.rule_rate},#{li.cuser},#{li.createtime},#{li.euser},#{li.endtime}) ");
				paramMap3.put("sql3", " ");
				paramMap3.put("list", list);
				haiwaiCfgMapper.batchExecSql(paramMap3);

				logger.info("执行syncHaiwaiReyunconfig " + list.size() + "条");
			}

			String url = "https://edc.vigame.cn:6115/recacheHaiwaiWjy?mapid=203";
			HttpClientUtils.getInstance().httpGet(url);

		} catch (Exception e) {
			e.printStackTrace();
			logger.info("执行syncHaiwaiReyunconfig 异常...");

			// 飞书通知
			String msg = "syncHaiwaiReyunconfig同步异常，请及时检查！";
			sendFailMsg(msg);
		}


	}

	@Scheduled(cron="00 */15 * * * ?")
	public void syncHaiwaiMMparamconfig() {

		logger.info("syncHaiwaiMMparamconfig 开始...");
		String before = DateTime.now().minusMinutes(20).toString("yyyy-MM-dd HH:mm:ss");

		/** 分渠道全部配置v20 */
		try {
			String query = "select * from dnwx_mmparam_all_config where "+haiwaiFilter;
			List<Map<String, String>> list = yyhzMapper.queryListMapOne(query);

			if (list != null && list.size() > 0) {

				String del = "delete from dnwx_cfg.dnwx_mmparam_all_config where 1=1 ";
				haiwaiCfgMapper.execSql(del);


				Map<String, Object> paramMap3 = new HashMap<String, Object>();
				paramMap3.put("sql1", "replace into dnwx_cfg.dnwx_mmparam_all_config(id,appid,cha_id,pid,area,standard_type,tjTestModel,agreementFlag,agreementUrl,policyUrl,reportFlag,`update`,hw_login,sim_filter,redeem,clogin,sl,slMsg,app_audit,healthFlag,standard,audit,lottery,nofee,assets,city,advert,antiAddiction,mmnotify,note,createtime,euser,endtime,payLimit,wxshare,shareurl,report,dbFlag,shf,qpay,giftDelay,giftSequence,`lock`,persona,vlogo,atLaunch,atBack,atClick,safeCustomer,nosafeCustomer,cuser,createUser,auth_type) values ");
				paramMap3.put("sql2", " (#{li.id},#{li.appid},#{li.cha_id},#{li.pid},#{li.area},#{li.standard_type},#{li.tjTestModel},#{li.agreementFlag},#{li.agreementUrl},#{li.policyUrl},#{li.reportFlag},#{li.update},#{li.hw_login},#{li.sim_filter},#{li.redeem},#{li.clogin},#{li.sl},#{li.slMsg},#{li.app_audit},#{li.healthFlag},#{li.standard},#{li.audit},#{li.lottery},#{li.nofee},#{li.assets},#{li.city},#{li.advert},#{li.antiAddiction},#{li.mmnotify},#{li.note},#{li.createtime},#{li.euser},#{li.endtime},#{li.payLimit},#{li.wxshare},#{li.shareurl},#{li.report},#{li.dbFlag},#{li.shf},#{li.qpay},#{li.giftDelay},#{li.giftSequence},#{li.lock},#{li.persona},#{li.vlogo},#{li.atLaunch},#{li.atBack},#{li.atClick},#{li.safeCustomer},#{li.nosafeCustomer},#{li.cuser},#{li.createUser},#{li.auth_type}) ");
				paramMap3.put("sql3", " ");
				paramMap3.put("list", list);
				haiwaiCfgMapper.batchExecSql(paramMap3);

				logger.info("执行syncHaiwaiMMparamconfig " + list.size() + "条");
			}

			String url = "https://edc.vigame.cn:6115/recacheHaiwaiWjy?mapid=109";
			HttpClientUtils.getInstance().httpGet(url);
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("执行syncHaiwaiMMparamconfig 异常...");

			// 飞书通知
			String msg = "syncHaiwaiMMparamconfig同步异常，请及时检查！";
			sendFailMsg(msg);
		}

		// 分渠道联运配置 dnwx_charule_all_config, dnwx_rule_all_config两个表同步
		try {
			String query = "select * from dnwx_charule_all_config where "+haiwaiFilter;
			List<Map<String, String>> list = yyhzMapper.queryListMapOne(query);

			if (list != null && list.size() > 0) {

				String del = "delete from dnwx_cfg.dnwx_charule_all_config where 1=1 ";
				haiwaiCfgMapper.execSql(del);


				Map<String, Object> paramMap3 = new HashMap<String, Object>();
				paramMap3.put("sql1", "replace into dnwx_cfg.dnwx_charule_all_config(id,appid,cha_id,pid,mid,mname,stime,etime,note,weeks,wtime,`status`,createtime,cuser,euser,endtime) values ");
				paramMap3.put("sql2", " (#{li.id},#{li.appid},#{li.cha_id},#{li.pid},#{li.mid},#{li.mname},#{li.stime},#{li.etime},#{li.note},#{li.weeks},#{li.wtime},#{li.status},#{li.createtime},#{li.cuser},#{li.euser},#{li.endtime}) ");
				paramMap3.put("sql3", " ");
				paramMap3.put("list", list);
				haiwaiCfgMapper.batchExecSql(paramMap3);

				logger.info("执行syncHaiwaiMMparamconfig1 " + list.size() + "条");
			}


			String query2 = "select * from dnwx_rule_all_config ";
			List<Map<String, String>> list2 = yyhzMapper.queryListMapOne(query2);

			if (list2 != null && list2.size() > 0) {

				String del = "delete from dnwx_cfg.dnwx_rule_all_config where 1=1 ";
				haiwaiCfgMapper.execSql(del);


				Map<String, Object> paramMap3 = new HashMap<String, Object>();
				paramMap3.put("sql1", "replace into dnwx_cfg.dnwx_rule_all_config(mid,mname,area,standard_type,tjTestModel,agreementFlag,agreementUrl,policyUrl,reportFlag,`update`,hw_login,sim_filter,redeem,clogin,sl,slMsg,app_audit,healthFlag,standard,audit,lottery,nofee,assets,city,advert,antiAddiction,mmnotify,note,createtime,cuser,euser,endtime,payLimit,wxshare,shareurl,report,dbFlag,shf,qpay,giftDelay,giftSequence,`lock`,persona,vlogo,atLaunch,atBack,atClick,safeCustomer,nosafeCustomer,auth_type) values ");
				paramMap3.put("sql2", " (#{li.mid},#{li.mname},#{li.area},#{li.standard_type},#{li.tjTestModel},#{li.agreementFlag},#{li.agreementUrl},#{li.policyUrl},#{li.reportFlag},#{li.update},#{li.hw_login},#{li.sim_filter},#{li.redeem},#{li.clogin},#{li.sl},#{li.slMsg},#{li.app_audit},#{li.healthFlag},#{li.standard},#{li.audit},#{li.lottery},#{li.nofee},#{li.assets},#{li.city},#{li.advert},#{li.antiAddiction},#{li.mmnotify},#{li.note},#{li.createtime},#{li.cuser},#{li.euser},#{li.endtime},#{li.payLimit},#{li.wxshare},#{li.shareurl},#{li.report},#{li.dbFlag},#{li.shf},#{li.qpay},#{li.giftDelay},#{li.giftSequence},#{li.lock},#{li.persona},#{li.vlogo},#{li.atLaunch},#{li.atBack},#{li.atClick},#{li.safeCustomer},#{li.nosafeCustomer},#{li.auth_type}) ");
				paramMap3.put("sql3", " ");
				paramMap3.put("list", list2);
				haiwaiCfgMapper.batchExecSql(paramMap3);

				logger.info("执行syncHaiwaiMMparamconfig2 " + list2.size() + "条");
			}

			String url = "https://edc.vigame.cn:6115/recacheHaiwaiWjy?mapid=109";
			HttpClientUtils.getInstance().httpGet(url);
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("执行syncHaiwaiMMparamconfig2 异常...");

			// 飞书通知
			String msg = "syncHaiwaiMMparamconfig2同步异常，请及时检查！";
			sendFailMsg(msg);
		}

	}

	@Scheduled(cron="00 */15 * * * ?")
	public void syncHaiwaiGameOnlineconfig() {

		logger.info("syncHaiwaiGameOnlineconfig 开始...");
		String before = DateTime.now().minusMinutes(20).toString("yyyy-MM-dd HH:mm:ss");

		/** 游戏在线配置 */
		try {
			String query = "select * from game_online_config where "+(haiwaiFilter.replace("cha_id","channel"));
			List<Map<String, String>> list = adMsgMapper.queryListMapOne(query);

			if (list != null && list.size() > 0) {

				String del = "delete from dnwx_cfg.game_online_config where 1=1";
				haiwaiCfgMapper.execSql(del);


				Map<String, Object> paramMap3 = new HashMap<String, Object>();
				paramMap3.put("sql1", "replace into dnwx_cfg.game_online_config(pid,json,create_time,update_time,create_own,update_own,`status`,appid,channel,version,remark,city,effect_type,buy_id) values ");
				paramMap3.put("sql2", " (#{li.pid},#{li.json},#{li.create_time},#{li.update_time},#{li.create_own},#{li.update_own},#{li.status},#{li.appid},#{li.channel},#{li.version},#{li.remark},#{li.city},#{li.effect_type},#{li.buy_id}) ");
				paramMap3.put("sql3", " ");
				paramMap3.put("list", list);
				haiwaiCfgMapper.batchExecSql(paramMap3);

				logger.info("执行syncHaiwaiGameOnlineconfig " + list.size() + "条");
			}

			String url = "https://edc.vigame.cn:6115/recacheHaiwaiWjy?mapid=45";
			HttpClientUtils.getInstance().httpGet(url);

		} catch (Exception e) {
			e.printStackTrace();
			logger.info("执行syncHaiwaiGameOnlineconfig 异常...");

			// 飞书通知
			String msg = "syncHaiwaiGameOnlineconfig同步异常，请及时检查！";
			sendFailMsg(msg);
		}

	}

	@Scheduled(cron="00 */10 * * * ?")
	public void syncHaiwaiTiwaionfig() {

		logger.info("syncHaiwaiTiwaionfig 开始...");
		String before = DateTime.now().minusMinutes(20).toString("yyyy-MM-dd HH:mm:ss");

		/** 体外配置 */
		try {
			List<HomeWzTiwaiConfig> forList = wzRewardConfigMapper.queryAllWzTiwaiConfig(null);
			if(forList != null && forList.size() > 0){
				// 先删除后写入
				haiwaiCfgMapper.execSql("delete from dnwx_cfg.home_wz_tiwai_config where 1=1");

				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("sql1", "replace into dnwx_cfg.home_wz_tiwai_config(id,appid,channel,prjid,user_group,ver,`status`,content,cuser,createtime,euser,endtime) values ");
				paramMap.put("sql2", " (#{li.id},#{li.appid},#{li.channel},#{li.prjid},#{li.user_group},#{li.ver},#{li.status},#{li.content},#{li.cuser},#{li.createtime},#{li.euser},#{li.endtime}) ");
				paramMap.put("sql3", " ");
				paramMap.put("list", forList);
				haiwaiCfgMapper.batchExecSql(paramMap);

				logger.info("执行syncHaiwaiTiwaionfig " + forList.size() + "条");
			}

		} catch (Exception e) {
			e.printStackTrace();
			logger.info("执行syncHaiwaiTiwaionfig 异常...");

			// 飞书通知
			String msg = "syncHaiwaiTiwaionfig同步异常，请及时检查！";
			sendFailMsg(msg);
		}
	}

	/**
	 * 定时同步渠道游戏广告配置数据至海外，频率:每小时
	 */
	@Scheduled(cron = "0 0 * * * ?")
	public void syncHaiwaiAppChannelConfig() {
		logger.info("syncHaiwaiAppChannelConfig 开始...");
		/* 渠道游戏广告配置配置 */
		try {
			List<Map<String, String>> accountList = yyhzMapper.queryListMapOne("select * from app_channel_config");
			if(accountList != null && accountList.size() > 0){
				// 先删除后写入
				haiwaiCfgMapper.execSql("delete from dnwx_cfg.app_channel_config where 1=1");
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("sql1", "replace into dnwx_cfg.app_channel_config(account,channel,tttoken,ttappid,ttparam,token,dnappid,cname,createtime,company_type,note,create_time,create_user,modify_time,modify_user,status) values ");
				paramMap.put("sql2", " (#{li.account},#{li.channel},#{li.tttoken},#{li.ttappid},#{li.ttparam},#{li.token},#{li.dnappid},#{li.cname},#{li.createtime},#{li.company_type},#{li.note},#{li.create_time},#{li.create_user},#{li.modify_time},#{li.modify_user},#{li.status}) ");
				paramMap.put("sql3", " ");
				paramMap.put("list", accountList);
				haiwaiCfgMapper.batchExecSql(paramMap);
				logger.info("执行 syncHaiwaiAppChannelConfig " + accountList.size() + "条");
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("执行syncHaiwaiAppChannelConfig 异常...");
			// 飞书通知
			String msg = "syncHaiwaiAppChannelConfig同步异常，请及时检查！";
			sendFailMsg(msg);
		}
	}




}
