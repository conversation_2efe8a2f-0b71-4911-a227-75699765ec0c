package com.wbgame.task;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.wbgame.mapper.adv2.Adv2Mapper;
import com.wbgame.mapper.master.AdvertMapper;
import com.wbgame.service.adv2.Adv2Service;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.mapper.adb.DnwxBiMapper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.service.AdService;
import com.wbgame.service.BigdataService;
import com.wbgame.service.adb.CurrentDataService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.FeishuUtils;
import com.wbgame.utils.HttpClientUtils;
import com.wbgame.utils.MailTool;

import javax.annotation.Resource;

/**
 * 大数据采集来源变现数据相关
 * <AUTHOR>
 */
@Component
public class BigdataTask { 
	Logger logger = LoggerFactory.getLogger(BigdataTask.class);
	
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	
	@Autowired
	AdService adService;
	@Autowired
	BigdataService bigdataService;
	@Autowired
	CurrentDataService currentDataService;
	@Autowired
	AdMapper adMapper;

	@Autowired
	DnwxBiMapper dnwxBiMapper;
	@Resource
	private Adv2Service adv2Service;

	@Autowired
	private AdvertMapper advertMapper;

	/**
	 * 同步-检查调控红线ctr结果
	 */
	@Scheduled(cron="00 00 18 * * ?")
	public void checkRedLineCtrAsConfig(){

		try {
			logger.info("checkRedLineCtrAsConfig同步开始...");

			String today = DateTime.now().toString("yyyy-MM-dd");
			adv2Service.checkRedLineCtrAsConfig(today, "vivo");

			logger.info("checkRedLineCtrAsConfig同步完成...");
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("checkRedLineCtrAsConfig同步异常...\n"+e.getMessage());
		}
	}

	/**
	 * 同步-自动调控红线ctr
	 */
	@Scheduled(cron="00 00 12 * * ?")
	public void syncRedLineCtrAsConfig(){

		try {
			logger.info("syncRedLineCtrAsConfig同步开始...");

			String today = DateTime.now().toString("yyyy-MM-dd");
			adv2Service.syncRedLineCtrAsConfig(today, "vivo");

			logger.info("syncRedLineCtrAsConfig同步完成...");
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("syncRedLineCtrAsConfig同步异常...\n"+e.getMessage());
		}
	}

	
	/**
	 * 同步-ctr自动调控广告协议配置
	 */
	@Scheduled(cron="00 00 07,09 * * ?")
	public void syncAdsDnShowGapAutoctr(){
		
		try {
			/** 转发给ADB数据库 */
			logger.info("syncAdsDnShowGapAutoctr同步开始...");
			// 自统计点击率 实际数值=（前七天自统计CTR+前一天自统计CTR）/2，点击率gap=前七天平台CTR/前七天自统计CTR（注意不要-1）
			Map<String, String> paramMap = new HashMap<String, String>();
			paramMap.put("sdate", DateTime.now().minusDays(8).toString("yyyy-MM-dd"));
			paramMap.put("edate", DateTime.now().minusDays(2).toString("yyyy-MM-dd"));
			paramMap.put("yesterday", DateTime.now().minusDays(1).toString("yyyy-MM-dd"));
			dnwxBiMapper.insertAdsDnShowGapAutoctr(paramMap);
			dnwxBiMapper.insertAdsDnShowGapAutoctrAsOut(paramMap);

			logger.info("syncAdsDnShowGapAutoctr同步完成...");
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("syncAdsDnShowGapAutoctr同步异常...\n"+e.getMessage());
		}
	}

	/**
	 * 实时流白名单事件配置同步
	 */
	@Scheduled(cron="00 */3 * * * ?")
	public void syncRealtimeConfig(){

		try {
			/** 转发给ADB数据库 */
			logger.info("syncRealtimeConfig 同步至ADB开始...");

			String query3 = "select * from ads_event_realtime_config";
			List<Map<String, String>> collect = adMapper.queryListMapOne(query3);

			Map<String, Object> paramMap3 = new HashMap<String, Object>();
			paramMap3.put("sql1", "REPLACE INTO dnwx_bi.ads_event_realtime_config(appid,event_id,event_name,cuser,createtime,euser,endtime) values ");
			paramMap3.put("sql2", " (#{li.appid},#{li.event_id},#{li.event_name},#{li.cuser},#{li.createtime},#{li.euser},#{li.endtime}) ");
			paramMap3.put("sql3", " ");
			paramMap3.put("list", collect);
			currentDataService.batchExecSql(paramMap3);

			logger.info("syncRealtimeConfig 同步至ADB完成...");
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("syncRealtimeConfig 同步至ADB异常...");
		}
	}

	/**
	 * 实名认证接口监控
	 */
	@Scheduled(cron="00 */10 8-22 * * ?")
	public void syncCheckRealApi(){
		
		logger.info("实名认证接口监控同步 start...");
		String now = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
		String before = DateTime.now().minusMinutes(30).toString("yyyy-MM-dd HH:mm:ss");
		
		List<JSONObject> collect = new ArrayList<>();
		ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();

		
		/** 循环验证协议接口的有效性 */
		String query = "SELECT xx.*,IFNULL(TRUNCATE(xx.success_num/(xx.success_num+xx.fail_num)*100,2), 0) rate FROM "+
				"(select COUNT(DISTINCT CASE WHEN errCode = 200 THEN lsn END) as success_num, "+
				"			COUNT(DISTINCT CASE WHEN errCode != 200 THEN lsn END) as fail_num "+
				"from yyhz_0308.dnwx_real_log where  "+
				"createtime BETWEEN '"+before+"' and '"+now+"') xx ";
		System.out.println("real query=="+query);
		
		List<Map<String, Object>> list = adService.queryListMap(query);
		if(list != null && list.size() == 1){
			Map<String, Object> act = list.get(0);
			Integer success_num = Integer.valueOf(act.get("success_num").toString());
			Double rate = Double.valueOf(act.get("rate").toString());
			
			if(success_num < 20){
				String warnMsg = "实名认证数据异常，半小时内成功认证数少于20，请及时排查!\n";
				String warnMsg2 = "实名认证数据异常，半小时内成功认证数少于20，请及时排查!<br>";
				sendFsMsg(warnMsg);
				MailTool.sendMail("重点接口监控", warnMsg2, "<EMAIL>", null);
			}else if(success_num >= 20 && rate < 90d){
				
				String warnMsg = "实名认证数据异常，半小时内成功认证比例低于90%，请及时排查!\n";
				String warnMsg2 = "实名认证数据异常，半小时内成功认证比例低于90%，请及时排查!<br>";
				sendFsMsg(warnMsg);
				MailTool.sendMail("重点接口监控", warnMsg2, "<EMAIL>", null);
			}else{
	    		logger.info("实名认证数据正常，半小时内成功率无降低！");
			}
		}
	
		logger.info("实名认证接口监控同步 end...");
	}
	
	/**
	 * 重点接口监控
	 */
	@Scheduled(cron="00 */1 * * * ?")
	public void syncCheckAbled(){
		
		logger.info("重点接口监控同步 start...");
		String tdate = DateTime.now().toString("yyyy-MM-dd");
		List<JSONObject> collect = new ArrayList<>();
		ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();

		
		/** 循环验证协议接口的有效性 */
		String query = "select mark,title,url from dnwx_interface_check where flag=1 ";
		List<Map<String, String>> list = adService.queryListMapOne(query);
		for (Map<String, String> act : list) {
			JSONObject info = new JSONObject();
			info.put("mark", act.get("mark"));
			info.put("title", act.get("title"));
			info.put("url", act.get("url"));
			
			long startTime = System.currentTimeMillis();
			String httpGet = HttpClientUtils.getInstance().httpGet(act.get("url"), null, true);
			long endTime = System.currentTimeMillis();

			/** 计算请求响应时间 */
			long time = (endTime-startTime);
			if(httpGet == null){
				info.put("status", "fail");
				info.put("time", "5000");
				
				// 初次加入设置3分钟过期，3分钟内超过3次访问失败则进行预警消息发送
				String key = "dnwx_interface_check_"+act.get("mark");
				Long increment = opsForValue.increment(key, 1);
				if(increment == 1) { redisTemplate.expire(key, 3, TimeUnit.MINUTES); }
				
				if(increment >= 3){
					String warnMsg = act.get("title")+" 访问异常，请及时排查!\n"+act.get("url");
					String warnMsg2 = act.get("title")+" 访问异常，请及时排查!<br>"+act.get("url");
					sendFsMsg(warnMsg);
					MailTool.sendMail("重点接口监控", warnMsg2, "<EMAIL>", null);
					redisTemplate.delete(key);
				}
				
			}else{
				info.put("status", "success");
				info.put("time", time);
			}
			collect.add(info);
		}
		
		/** 入库统计结果 */
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sql1", "INSERT INTO dnwx_interface_check_info(tdate,mark,title,`status`,time) values ");
			paramMap.put("sql2", " ('"+tdate+"',#{li.mark},#{li.title},#{li.status},#{li.time}) ");
			paramMap.put("sql3", " ");
			paramMap.put("list", collect);
			adMapper.batchExecSql(paramMap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		logger.info("重点接口监控同步 end...");
	}
	
	/**
	 * 同步自推广收入分析
	 */
	@Scheduled(cron="00 20 10,11,16 * * ?")
	public void syncSelfPromotion(){
		String day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		adService.insertSelfPromotionIncome(day);
	}
	
	@Scheduled(cron="00 00 10,11,14,16 * * ?")
	public void syncDnCash(){
		logger.info("变现平台明细同步 start...");
		adService.syncDnChaCashTotal(null);
		logger.info("变现平台明细同步 end...");
	}
	
	/**
	 * 同步应用收支汇总表
	 */
	@Scheduled(cron="00 40 10,11,14,16,20 * * ?")
	public void syncDnAppRevenueTotal(){
		String day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		adService.syncDnAppRevenueTotal(day);
	}
	
	/**
	 * 同步应用分渠道收支数据汇总
	 */
	@Scheduled(cron="00 50 10,11,14,16,20,21 * * ?")
	public void syncAppChaRevenue(){
		String day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		String beforeday = DateTime.now().minusDays(2).toString("yyyy-MM-dd");
		try {
			adService.syncDnAppChaRevenueTotal(beforeday);
			adService.syncDnAppChaRevenueTotal(day);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		try {
			adService.syncDnAppChannelRevenueTotal(beforeday);
			adService.syncDnAppChannelRevenueTotal(day);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	/**
	 * 同步实时瀑布流监控数据
	 */
//	@Scheduled(cron="00 59 */1 * * ?")
	public void syncGroupDataTwo(){
		String day = DateTime.now().toString("yyyy-MM-dd");
		
		try {
			bigdataService.syncGroupDataTwo(day);
		} catch (Exception e) {
			e.printStackTrace();
		}
		try {
			bigdataService.syncGroupDataAdd(day);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 同步变现-广告位数据
	 * 
	 */
//	@Scheduled(cron="00 00 10,11,15 * * ?")
	public void syncAdposData(){
		
		String day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		try {
			bigdataService.syncAdposData(day);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	/**
	 * 同步变现-二级子渠道展示收入 
	 */
//	@Scheduled(cron="00 58 */2 * * ?")
	public void syncExtendSubchaTotal(){
		String day = DateTime.now().toString("yyyy-MM-dd");
		if("10".equals(DateTime.now().toString("HH")) || "14".equals(DateTime.now().toString("HH")))
			day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		
		bigdataService.syncExtendSubchaTotal(day);
	}
	
	/**
	 * 同步项目ID收入预估表
	 */
//	@Scheduled(cron="00 50 10,11,17,20 * * ?")
	public void syncPrjidIncome(){
		String day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		try {
			bigdataService.syncExtendPrjidIncome(day);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
	/**
	 * 同步变现收入校准，汇总数据校准，汇总数据-用户群
	 */
//	@Scheduled(cron="00 40 10,11,17,20 * * ?")
	public void syncIncomeRevise(){
		String day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		try {
			bigdataService.syncExtendIncomeRevise(day);
		} catch (Exception e) {
			e.printStackTrace();
		}
		try {
			bigdataService.syncExtendAdtypeRevise(day);
		} catch (Exception e) {
			e.printStackTrace();
		}
		try {
			bigdataService.syncExtendAdtypeReviseTwo(day);
		} catch (Exception e) {
			e.printStackTrace();
		}
		try {
			bigdataService.syncExtendAdtypeGroup(day);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 同步变现收入预估数据.v2
	 */
//	@Scheduled(cron="00 30 10,11,17,20 * * ?")
	public void syncIncomeDataV2(){
		String day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		bigdataService.syncIncomeDataTwo(day);
	}
	
	/**
	 * 同步展示GAP统计数据
	 */
//	@Scheduled(cron="00 20 10,11,17,20 * * ?")
	public void syncShowGapTotal(){
		String day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		bigdataService.syncShowGapTotal(day);
	}
	
	/**
	 * 同步聚合综合查询
	 */
	@Scheduled(cron="00 30 10,11,15,17,20 * * ?")
	public void syncGroupCom2(){
		String day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		
		try {
			/** 转发给ADB数据库 */
			logger.info("同步聚合综合查询 同步至ADB...");
			
			String query3 = "select * from dnwx_cfg.dn_extend_group_sum where tdate = '"+day+"'";
			List<Map<String, String>> collect = adMapper.queryListMapOne(query3);
			
			Map<String, Object> paramMap3 = new HashMap<String, Object>();
			paramMap3.put("sql1", "REPLACE INTO dnwx_bi.dn_extend_group_sum(tdate,appid,cha_id,adsid,adpos_type,strategy,ecpm,req_num,fill_num,show_num,click_num,income,platform_ecpm,platform_show,platform_fill,platform_req,ad_load_duration,agent,is_newuser,sdk_adtype) values ");
			paramMap3.put("sql2", " (#{li.tdate},#{li.appid},#{li.cha_id},#{li.adsid},#{li.adpos_type},#{li.strategy},#{li.ecpm},#{li.req_num},#{li.fill_num},#{li.show_num},#{li.click_num},#{li.income},#{li.platform_ecpm},#{li.platform_show},#{li.platform_fill},#{li.platform_req},#{li.ad_load_duration},#{li.agent},#{li.is_newuser},#{li.sdk_adtype}) ");
			paramMap3.put("sql3", " ");
			paramMap3.put("list", collect);
			dnwxBiMapper.batchExecSql(paramMap3);
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步聚合综合查询 同步至ADB异常...");
		}
	}
	/**
	 * 同步聚合综合查询
	 */
	@Scheduled(cron="00 20 10,11,15,17,20 * * ?")
	public void syncGroupCom(){
		String day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		bigdataService.syncGroupCom(day);
	}
	
	
	/**
	 * 同步sdk版本监控
	 */
	@Scheduled(cron="00 00 6 * * ?")
	public void syncSdkRelation(){
		logger.info("同步syncSdkRelation 开始...");
		try {
			String day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			
			String sql = "select aa.*,bb.sdk_name,bb.ver,bb.local_ver "+
				"from (SELECT tdate,projectid prjid,SUM(act_num) num FROM product_chaid_total WHERE tdate='"+day+"' GROUP BY projectid) aa "+
				"join (select project_id prjid,sdk_name,sdk_version ver,version local_ver from dnwx_client.wbgui_project_sdk_relation "+
				"		where sdk_name like '%头条%' or sdk_name like '%广点通%' or sdk_name like '%快手%' or sdk_name like '%百度%' "+
				"		or sdk_name like '%core-kinetic%' or sdk_name like '%admanager%' or 1=1) bb "+
				"on aa.prjid=bb.prjid";
			List<Map<String, Object>> list = adService.queryListMap(sql);
			System.out.println(sql);
			
			/** 给项目ID匹配对应的子渠道标识 */
            Map<String, Map<String, Object>> pcMap = adService.selectProjectidChannelMap();
            for (Map<String, Object> info : list) {
            	Map<String, Object> act = pcMap.get(info.get("prjid")+"");
            	if(act != null){
            		info.put("appid", act.get("appid")+"");
            		info.put("cha_id", act.get("cha_id")+"");
            	}
			}
            
            Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sql1", "insert into dn_extend_sdk_relation(tdate,appid,cha_id,prjid,sdk_name,ver,local_ver,num) values ");
			paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.cha_id},#{li.prjid},#{li.sdk_name},#{li.ver},#{li.local_ver},#{li.num}) ");
			paramMap.put("sql3", " ON DUPLICATE KEY UPDATE num=VALUES(num)");
			paramMap.put("list", list);
			adv2Service.batchExecSql(paramMap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		logger.info("同步syncSdkRelation 结束...");
	}
	
	/**
	 * 同步实时数据监控
	 */
	@Scheduled(cron="00 10 07-21 * * ?")
	public void syncDnGroupDataMonitor(){
		logger.info("同步syncDnGroupDataMonitor 开始...");
		
		String day = DateTime.now().toString("yyyy-MM-dd");
		String del = "delete from dnwx_bi.dn_extend_group_monitor where tdate = '"+day+"'";
		dnwxBiMapper.execSql(del);
		
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("tdate", day);
		dnwxBiMapper.insertDnGroupDataMonitor(paramMap);
		
		
		/*String sql = "SELECT tdate,appid,download_channel chaid,pid projectid,new_users add_num,act_users act_num "+
				"FROM ads_dim_users_info_4d_hourly WHERE tdate = '"+day+"'";
		List<Map<String, String>> proList = dnwxBiMapper.queryListMapOne(sql);
		
		Map<String, Object> paramMap2 = new HashMap<String, Object>();
		paramMap2.put("sql1", "update dnwx_bi.dn_extend_group_monitor set actnum=#{li.act_num},addnum=#{li.add_num} where tdate=#{li.tdate} and prjid=#{li.projectid} and cha_id=#{li.chaid} ");
		paramMap2.put("list", proList);
		dnwxBiMapper.batchExecSqlTwo(paramMap2);*/
		
		logger.info("同步syncDnGroupDataMonitor 完成...");
	}
	
	/**
	 * 同步实时数据监控.end
	 */
	@Scheduled(cron="00 58 23 * * ?")
	public void syncDnGroupDataMonitorEnd(){
		logger.info("同步syncDnGroupDataMonitorEnd 开始...");
		
		String day = DateTime.now().toString("yyyy-MM-dd");
		String del = "delete from dnwx_bi.dn_extend_group_monitor where tdate = '"+day+"'";
		dnwxBiMapper.execSql(del);
		
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("tdate", day);
		dnwxBiMapper.insertDnGroupDataMonitor(paramMap);
		
		logger.info("同步syncDnGroupDataMonitorEnd 完成...");
	}
	
	/**
	 * 同步变现数据汇总报表
	 */
	@Scheduled(cron="00 59 */1 * * ?")
	public void syncRevenueReport(){
		try {
			String date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			String sql = "delete from dnwx_cfg.dn_extend_revenue_report where date = '"+date+"'";
			adService.execSql(sql);
			
			adMapper.insertExtendRevenueReport(date);
		
			logger.info("同步syncRevenueReport 完成...");
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncRevenueReport 异常...");
		}
	}
	
	/**
	 * 发送飞书告警消息
	 * @param msg 消息内容
	 * @return
	 */
	public String sendFsMsg(String Msg){
		String tenant_access_token = (String) redisTemplate.opsForValue().get("tenant_access_token_key");
		if (BlankUtils.checkBlank(tenant_access_token)){
		    tenant_access_token = FeishuUtils.getTenantAccessToken();
		    if (!BlankUtils.checkBlank(tenant_access_token)){
		        redisTemplate.opsForValue().set("tenant_access_token_key", tenant_access_token, 75, TimeUnit.MINUTES);
		    }
		}
		String[] us = {"9eg79e76","1933f4b1"}; // , "11d51ae5"
		for (String userId : us) {
			FeishuUtils.sendSigleMsg(tenant_access_token, userId, Msg);
		}
		return "ok";
	}

	/**
	 * 每10分钟同步渠道产品id关联配置数据渠道产品使用通过状态查询appid和channel
	 */
	@Scheduled(cron = "30 */10 * * * ?")
	public void syncAppidChannelTask(){
		try {
			logger.info("同步渠道产品id关联配置数据渠道产品appid和channel开始");
			List<Map<String, String>> appChannelList = advertMapper.getAppChannelList(null);
			if (null != appChannelList && !appChannelList.isEmpty()) {
				//删除sql //大数据库和yyhz_0308库的表是同名
				String deleteSQL = "DELETE FROM ads_appid_channel_info";
				//新增数据sql参数封装
				Map<String, Object> insertParamMap = new HashMap<String, Object>();
				insertParamMap.put("sql1", "REPLACE INTO ads_appid_channel_info (con_ac,state) values ");
				insertParamMap.put("sql2", " (#{li.con_ac},#{li.state}) ");
				insertParamMap.put("sql3", " ");
				insertParamMap.put("list", appChannelList);
				//需要先删除再新增数据
				adMapper.execSql(deleteSQL);
				//新增数据表中 ads_appid_channel_info
				adMapper.batchExecSql(insertParamMap);
				//先执行删除数据后进行新增数据
				dnwxBiMapper.execSql(deleteSQL);
				dnwxBiMapper.batchExecSql(insertParamMap);
			}
			logger.info("同步渠道产品id关联配置数据渠道产品appid和channel结束");
		} catch (Exception e) {
			logger.error("同步渠道产品id关联配置数据渠道产品appid和channel异常，原因:",e);
			//程序出现异常，进行飞书通知告警操作
			FeishuUtils.sendMsgToGroupRobot5("oc_8e83054321d3e25a51f9e8dffbcd9d6b","同步渠道产品id关联配置数据渠道产品appid和channel异常,请及时处理！！","all");
		}
	}


}
