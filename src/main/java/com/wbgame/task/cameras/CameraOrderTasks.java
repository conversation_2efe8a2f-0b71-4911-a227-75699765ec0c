package com.wbgame.task.cameras;

import com.wbgame.mapper.adb.DnwxBiAdtMapper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.FeishuUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/12
 * @description
 **/
@Component
@Slf4j
public class CameraOrderTasks {

    @Resource
    private DnwxBiAdtMapper dnwxBiAdtMapper;
    @Resource
    private AdMapper adMapper;

    private Map<String, Map<Boolean, List<WbPayInfoUserInfo>>> cache = new HashMap<>();
    private Map<String, List<WbPayInfoUserInfo>> iosCache = new HashMap<>();

    /**
     * 每10分钟刷新一次安卓的付费人数
     */
    @Scheduled(cron = "0 5/10 8-23 * * ? ")
    public void refreshAndroidPayUser() {
        log.info("开始更新安卓付费人数");
        LocalDate today = LocalDate.now();
        try {
            doRefreshAndroidPayUser(today);
        } catch (Exception e) {

        }
        log.info("更新安卓付费人数完成");
        log.info("开始更新ios付费人数");
        doRefreshIosPayUser(today);
        log.info("结束更新ios付费人数");
    }

    /**
     * 凌晨刷新一次安卓的付费人数
     */
    @Scheduled(cron = "0 5 1 * * ? ")
    public void refreshAndroidPayUserYesterday() {
        for (int i = 1; i < 8; i++) {
            LocalDate today = LocalDate.now().minusDays(i);
            log.info("开始更新安卓付费人数，日期：{}", today);
            try {
                doRefreshAndroidPayUser(today);
            } catch (Exception e) {

            }
            log.info("更新安卓付费人数完成");
            log.info("开始更新ios付费人数，日期：{}", today);
            doRefreshIosPayUser(today);
            log.info("结束更新ios付费人数");
        }
    }

    @Scheduled(cron = "0 0/30 8-23 * * ?")
    public void refreshCancelUsers() {
        log.info("开始更新取消订阅用户");
        LocalDate today = LocalDate.now();
        List<WbPayInfoUserInfo> wbPayInfoUserInfos = adMapper.selectCameraCancelCount(today.toString());
        dnwxBiAdtMapper.insertCancelUserDetail(wbPayInfoUserInfos, today.toString());
        log.info("更新取消订阅用户完成");
    }

    /**
     * 凌晨刷新一次安卓的付费人数
     */
    @Scheduled(cron = "0 20 1 * * ? ")
    public void refreshCancelUserYesterday() {
        for (int i = 1; i < 3; i++) {
            LocalDate today = LocalDate.now().minusDays(i);
            log.info("开始更新取消订阅用户，日期：{}", today);
            List<WbPayInfoUserInfo> wbPayInfoUserInfos = adMapper.selectCameraCancelCount(today.toString());
            dnwxBiAdtMapper.insertCancelUserDetail(wbPayInfoUserInfos, today.toString());
            log.info("更新取消订阅用户完成");
        }
    }

    public void doRefreshAndroidPayUser(LocalDate today) {
        String date = today.toString();
        // 获取媒体数据
        List<CameraCostCounter> counters = dnwxBiAdtMapper.selectCamerasPayCount(date, 1);
        Set<String> mediaAppSet = counters.stream().map(CameraCostCounter::getAppid).collect(Collectors.toSet());
        // 获取订单表数据

        Map<String, CameraCostCounter> appIdCountMap = getPayCountCount(today);
        // 拼上订阅数据
        Map<String, CameraCostCounter> subscribeCountMap = getAndroidSubscribeCount(today);
//        Map<String, Long> appIdSubcribeCountMap = getSubscribeCountCount(today);


        // 拼在一起
        for (CameraCostCounter counter : counters) {
            String appid = counter.getAppid();
            CameraCostCounter cameraCostCounter = appIdCountMap.get(appid);
            if (cameraCostCounter != null) {
                counter.setPay_users(cameraCostCounter.getPay_users());
                counter.setPay_amount(cameraCostCounter.getPay_amount());
            }
            CameraCostCounter subscribeCounter = subscribeCountMap.get(appid);
            if (subscribeCounter != null) {
                counter.setSign_user(subscribeCounter.getSign_user());
                counter.setPay_user(subscribeCounter.getPay_user());
                counter.setCancel_user(subscribeCounter.getCancel_user());
            }
        }

        if (!mediaAppSet.containsAll(appIdCountMap.keySet())) {
            Set<String> temp = appIdCountMap.keySet();
            temp.removeAll(mediaAppSet);

            for (String selfCountApp : temp) {
                CameraCostCounter cameraCostCounter = appIdCountMap.get(selfCountApp);
                if (cameraCostCounter != null) {
                    CameraCostCounter selfCountData = CameraCostCounter.builder().pay_users(cameraCostCounter.getPay_users())
                            .appid(selfCountApp)
                            .tdate(date)
                            .rebatespend(0D)
                            .paycount(0)
                            .pay_amount(cameraCostCounter.getPay_amount())
                            .build();
                    CameraCostCounter subscribeCounter = subscribeCountMap.get(selfCountApp);
                    if (subscribeCounter != null) {
                        selfCountData.setSign_user(subscribeCounter.getSign_user());
                        selfCountData.setPay_user(subscribeCounter.getPay_user());
                        selfCountData.setCancel_user(subscribeCounter.getCancel_user());
                    }
                    counters.add(selfCountData);
                }
            }
        }
        dnwxBiAdtMapper.deleteAndroidCameraList(date, "dnwx_bi.ads_android_camera_series_and_camera_pay_cost_hourly");
        dnwxBiAdtMapper.batchInsertAndroidCameraList(counters, "dnwx_bi.ads_android_camera_series_and_camera_pay_cost_hourly");
//        System.out.println(counters);
    }

    private Map<String, CameraCostCounter> getPayCountCount(LocalDate date) {
        // 获取当天所有的单次付费用户
        String start = date + " 00:00:00";
        String end = date + " 23:59:59";
        List<WbPayInfoUserInfo> todayUsers = adMapper.selectCamerasPayCount(start, end);
        Map<Boolean, List<WbPayInfoUserInfo>> todayMap = dividePayAndSubscribe(todayUsers);


        // 获取历史的所有单次付费用户
        start = "2024-01-01 00:00:00";
        end = date.minusDays(1) + " 23:59:59";
        Map<Boolean, List<WbPayInfoUserInfo>> hisMap = getHistoryPayUsers(start, end);

        // 找出首次付费用户
        Map<String, CameraCostCounter> firstPayUser = findFirstPayUser(todayMap.get(false), hisMap.get(false), date.toString());

        // 找出首次订阅用户
        Map<String, CameraCostCounter> firstSubscribeUser = findFirstPayUser(todayMap.get(true), new ArrayList<>(), date.toString());
        if ( CollectionUtils.isEmpty(firstSubscribeUser)) {
            return firstPayUser;
        }
        firstSubscribeUser.forEach((k, v) -> firstPayUser.merge(k, v, (a, b) -> {
            a.setPay_amount(a.getPay_amount() + b.getPay_amount());
            a.setPay_users(a.getPay_users() + b.getPay_users());
            return a;
        }));
        return firstPayUser;
    }

    private Map<Boolean, List<WbPayInfoUserInfo>> getHistoryPayUsers(String start, String end) {
        if (cache.get(end) != null) {
            return cache.get(end);
        }
        cache = new HashMap<>();
        List<WbPayInfoUserInfo> historyUsers = adMapper.selectCamerasPayCount(start, end);
        Map<Boolean, List<WbPayInfoUserInfo>> hisMap = dividePayAndSubscribe(historyUsers);
        cache.put(end, hisMap);
        return hisMap;
    }

    private List<WbPayInfoUserInfo> getIosHistoryPayUsers(String start, String end) {
        if (iosCache.get(end) != null) {
            return iosCache.get(end);
        }
        iosCache = new HashMap<>();
        List<WbPayInfoUserInfo> historyUsers = adMapper.selectIOSCamerasPayCount(start, end);
        iosCache.put(end, historyUsers);
        return historyUsers;
    }

    public Map<String, CameraCostCounter> findFirstPayUser(List<WbPayInfoUserInfo> todayUsers, List<WbPayInfoUserInfo> historyUsers, String date) {
        Set<Object> sets = new HashSet<>(historyUsers);
        if (CollectionUtils.isEmpty(todayUsers)) {
            return new HashMap<>();
        }

        List<WbPayInfoUserInfo> validUser = todayUsers.stream().filter(user -> !sets.contains(user)).collect(Collectors.toList());

        dnwxBiAdtMapper.insertPayUserDetail(validUser, date);
        dnwxBiAdtMapper.insertPayUserDetail2(validUser, date);

        Map<String, Long> appIdCountMap = validUser.stream()
                .collect(Collectors.groupingBy(WbPayInfoUserInfo::getAppid, Collectors.counting()));
        Map<String, Double> amountMap = validUser.stream()
                .collect(Collectors.toMap(WbPayInfoUserInfo::getAppid, WbPayInfoUserInfo::getPrice, Double::sum));

        Map<String, CameraCostCounter> counter = new HashMap<>();
        for (String appid : appIdCountMap.keySet()) {
            CameraCostCounter cameraCostCounter = CameraCostCounter.builder()
                    .appid(appid)
                    .pay_amount(amountMap.getOrDefault(appid, 0D))
                    .pay_users(appIdCountMap.getOrDefault(appid, 0L).intValue())
                    .build();
            counter.put(appid, cameraCostCounter);
        }

        return counter;
    }

    /**
     * 把全部订单分成一次性和订阅订单，true对应的是订阅
     *
     * @param list
     * @return
     */
    private Map<Boolean, List<WbPayInfoUserInfo>> dividePayAndSubscribe(List<WbPayInfoUserInfo> list) {
        return list.stream().collect(Collectors.groupingBy(l -> l.getParam3().equals("pay")));
    }


    private Map<String, Long> getSubscribeCountCount(LocalDate date) {
        // 获取当天所有的单次付费用户
        String start = date + " 00:00:00";
        String end = date + " 23:59:59";
        List<WbPayInfoUserInfo> todayUsers = adMapper.selectCamerasSubscribeCount(start, end);
        // 获取历史的所有单次付费用户
        start = "2024-01-01 00:00:00";
        end = date.minusDays(1) + " 23:59:59";
        List<WbPayInfoUserInfo> historyUsers = adMapper.selectCamerasSubscribeCount(start, end);
        Set<Object> sets = new HashSet<>(historyUsers);
        List<WbPayInfoUserInfo> validUser = todayUsers.stream().filter(user -> !sets.contains(user)).collect(Collectors.toList());
        Map<String, Long> appIdCountMap = validUser.stream()
                .collect(Collectors.groupingBy(WbPayInfoUserInfo::getAppid, Collectors.counting()));
        return appIdCountMap;
    }

    public void doRefreshIosPayUser(LocalDate today) {
        String date = today.toString();
        // 获取媒体数据
        List<CameraCostCounter> counters = dnwxBiAdtMapper.selectCamerasPayCount(date, 2);
        Set<String> mediaAppSet = counters.stream().map(CameraCostCounter::getAppid).collect(Collectors.toSet());
        // 获取apple notice表数据
        Map<String, CameraCostCounter> appIdCountMap = getPayIosCount(today);
//        Map<String, Long> appIdSubcribeCountMap = getSubscribeCountCount(today);
        Map<String, CameraCostCounter> iosSubscribeCountMap = getIosSubscribeCount(today);
        // 拼在一起
        for (CameraCostCounter counter : counters) {
            String appid = counter.getAppid();
            CameraCostCounter cameraCostCounter = appIdCountMap.get(appid);
            if (cameraCostCounter != null) {
                counter.setPay_users(cameraCostCounter.getPay_users());
                counter.setPay_amount(cameraCostCounter.getPay_amount());
            }
            CameraCostCounter subscribe = iosSubscribeCountMap.get(appid);
            if (subscribe != null) {
                counter.setSign_user(subscribe.getSign_user());
                counter.setCancel_user(subscribe.getCancel_user());
            }
        }

        if (!mediaAppSet.containsAll(appIdCountMap.keySet())) {
            Set<String> temp = appIdCountMap.keySet();
            temp.removeAll(mediaAppSet);

            for (String selfCountApp : temp) {
                CameraCostCounter cameraCostCounter = appIdCountMap.get(selfCountApp);
                if (cameraCostCounter != null) {
                    CameraCostCounter selfCountData = CameraCostCounter.builder().pay_users(cameraCostCounter.getPay_users())
                            .appid(selfCountApp)
                            .tdate(date)
                            .rebatespend(0D)
                            .paycount(0)
                            .pay_amount(cameraCostCounter.getPay_amount())
                            .build();
                    counters.add(selfCountData);
                }
            }
        }

        // 获取特殊的两个产品数据
        List<CameraCostCounter> specialCounters = dnwxBiAdtMapper.selectSpecialCamerasPayCount(date);
        counters = counters.stream().filter(c ->
                !c.getAppid().equals("38846") && !c.getAppid().equals("39027")).collect(Collectors.toList());
        counters.addAll(specialCounters);
        dnwxBiAdtMapper.deleteAndroidCameraList(date, "dnwx_bi.ads_leying_and_camera_pay_cost");
        dnwxBiAdtMapper.batchInsertAndroidCameraList(counters, "dnwx_bi.ads_leying_and_camera_pay_cost");
//        System.out.println(counters);
    }

    private Map<String, CameraCostCounter> getIosSubscribeCount(LocalDate today) {
        String start = today + " 00:00:00";
        String end = today + " 23:59:59";
        List<CameraCostCounter> todayUsers = adMapper.selectIOSCamerasSubscribeCount(start, end);
        return todayUsers.stream().collect(Collectors.toMap(CameraCostCounter::getAppid, v -> v, (v1, v2) -> v2));
    }

    private static final Pattern LOWERCASE_PATTERN = Pattern.compile("[a-z]");

    private Map<String, CameraCostCounter> getPayIosCount(LocalDate date) {
        // 获取当天所有的单次付费用户
        String start = date + " 00:00:00";
        String end = date + " 23:59:59";
        List<WbPayInfoUserInfo> todayUsers = adMapper.selectIOSCamerasPayCount(start, end);
        todayUsers = todayUsers.stream().filter(info -> !containsLowerCase(info.getAndroidid())).collect(Collectors.toList());


        // 获取历史的所有单次付费用户
        start = "2024-01-01 00:00:00";
        end = date.minusDays(1) + " 23:59:59";
        List<WbPayInfoUserInfo> hisUsers = getIosHistoryPayUsers(start, end);
        // 找出首次订阅用户
        Map<String, CameraCostCounter> firstSubscribeUser = findFirstPayUser(todayUsers, hisUsers, date.toString());
        return firstSubscribeUser;
    }

    /**
     * 获取安卓的订阅数等数据
     */
    public Map<String, CameraCostCounter> getAndroidSubscribeCount(LocalDate date) {
        List<CameraCostCounter> counters = adMapper.selectAndroidCamerasSignCount(date.toString(), date.toString());
        return counters.stream().collect(Collectors.toMap(CameraCostCounter::getAppid, c -> c, (x, y) -> y));
    }

    public boolean containsLowerCase(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return LOWERCASE_PATTERN.matcher(str).find();
    }

    Map<String, Integer> warningMap = new HashMap<>();

    /**
     * 每十分钟检测一次gap值
     */
    @Scheduled(cron = "0 0/10 9-21 * * ?")
    public void dailyCheckPayUserGap() {
        log.info("检测gap值开始");
        LocalDate today = LocalDate.now();
        checkPayUserGap(today);
        log.info("检测gap值结束");
    }

    public void checkPayUserGap(LocalDate date) {
        String today = date.toString();
        String yesterday = date.minusDays(1).toString();
        String sevenDay = date.minusDays(7).toString();
        String thirtyDay = date.minusDays(30).toString();

        String submitSql = "select appid, count(1) count, max(create_time) create_time from dnwx_adt.self_submit_records " +
                "where create_time between '" + today + " 00:00:00' and '" + today + " 23:59:59' " +
                "and event_type = 'active_pay_server' " +
                "GROUP BY appid";
        List<Map<String, Object>> submit = dnwxBiAdtMapper.queryListMap(submitSql);
//        submit.stream().collect(Collectors.toMap(m -> BlankUtils.getString(m.get("appid"), m -> m)))
        Map<String, Map<String, Object>> appidSubmitMap = submit.stream().collect(Collectors.toMap(m -> BlankUtils.getString(m.get("appid")), m -> m));
        Set<String> submitAppList = appidSubmitMap.keySet();

        List<Map<String, Object>> todayGap = dnwxBiAdtMapper.selectCameraPayGap(today, today, new ArrayList<>(submitAppList));
        List<Map<String, Object>> yesterdayGap = dnwxBiAdtMapper.selectCameraPayGap(sevenDay, yesterday, new ArrayList<>(submitAppList));

        Map<String, Map<String, Object>> historyGapMap = yesterdayGap.stream().collect(Collectors.toMap(m -> BlankUtils.getString(m.get("appid")), m -> m));
        ArrayList<CameraStatisticPo> cameraStatisticPos = new ArrayList<>();

        for (Map<String, Object> map : todayGap) {
            try {
                CameraStatisticPo po = new CameraStatisticPo();
                String appid = BlankUtils.getString(map.get("appid"));
                double gap = BlankUtils.getDouble(map.get("gap"));

                Map<String, Object> hisMap = historyGapMap.get(appid);
                if (hisMap == null) {
                    continue;
                }
                double hisGap = BlankUtils.getDouble(hisMap.get("gap"));
                double v = gap - hisGap;
                int pay_users = BlankUtils.getInt(map.get("pay_users"));

                String submitUserSql = "select android_id from dnwx_adt.self_submit_records where create_time > '" + today + " 00:00:00' " +
                        " and event_type = 'active_pay_server' " +
                        " and appid = " + appid;
                List<Map<String, Object>> maps = dnwxBiAdtMapper.queryListMap(submitUserSql);
                Set<String> submitUsers = maps.stream().map(m -> BlankUtils.getString(m.get("android_id"))).collect(Collectors.toSet());
                List<String> selfCountUsers = dnwxBiAdtMapper.selectPayUserDetail(appid, today);

                Set<String> hisPaidUser = new HashSet<>();
                if (!CollectionUtils.isEmpty(selfCountUsers)) {
                    String userPayDetailSql = " select distinct androidid from wb_pay_info " +
                            "where createtime BETWEEN '2024-01-01 00:00:00' and '" + today + " 00:00:00' " +
                            "and orderstatus = 'SUCCESS' " +
                            "and appid = '" + appid + "' " +
                            "and androidid in  (" + selfCountUsers.stream().map(s -> "'" + s + "'").collect(Collectors.joining(",")) + ")";
//            log.info("请求用户历史付费记录：{}", userPayDetailSql);
                    List<Map<String, Object>> maps1 = adMapper.queryListMap(userPayDetailSql);
                    hisPaidUser = maps1.stream().map(m -> BlankUtils.getString(m.get("androidid"))).collect(Collectors.toSet());
                }
                HashSet<String> firstPayUsers = new HashSet<>(selfCountUsers);
                firstPayUsers.removeAll(hisPaidUser);

                Set<String> hisPaidUser30 = new HashSet<>();
                if (!CollectionUtils.isEmpty(firstPayUsers)) {
                    String thirtyDayPayUserSql = " select android_id from dnwx_adt.self_submit_records " +
                            " where create_time > '" + thirtyDay + " 00:00:00' " +
                            " and event_type = 'active' " +
                            " and android_id in (" + firstPayUsers.stream().map(s -> "'" + s + "'").collect(Collectors.joining(",")) + ")";
                    List<Map<String, Object>> thirtyDayPayUserList = dnwxBiAdtMapper.queryListMap(thirtyDayPayUserSql);
                    hisPaidUser30 = thirtyDayPayUserList.stream().map(m -> BlankUtils.getString(m.get("android_id"))).collect(Collectors.toSet());
                }
                po.setTdate(today);
                po.setAppid(appid);
                po.setAppName(BlankUtils.getString(map.get("app_name")));
                po.setTodayGap(String.format("%.2f", gap * 100) + "%");
                po.setHisGap(String.format("%.2f", hisGap * 100) + "%");
                po.setBias(String.format("%.2f", v * 100) + "%");
                po.setMediaPay(BlankUtils.getInt(map.get("paycount")));
                po.setSelfCountPay(pay_users);
                po.setHistoryPay(hisPaidUser.size());
                po.setHistoryUser(String.join(",", hisPaidUser));
                po.setFirstPay(firstPayUsers.size());
                po.setFirstPayUser(String.join(",", firstPayUsers));
                po.setSubmitCount(submitUsers.size());
                po.setSubmitCountUser(String.join(",", submitUsers));
                HashSet<String> submitLossUser = new HashSet<>(firstPayUsers);
                submitLossUser.removeAll(submitUsers);
                po.setSubmitLossUser(String.join(",", submitLossUser));
                HashSet<String> active30DayUser = new HashSet<>(firstPayUsers);
                active30DayUser.removeAll(hisPaidUser30);
                po.setActive30DayPay(active30DayUser.size());
                po.setActive30DayUser(String.join(",", active30DayUser));
                cameraStatisticPos.add(po);
                if (v >= 0.2 && pay_users > 8) {
                    Map<String, Object> submitDetail = appidSubmitMap.get(appid);
                    String key = today + appid;
                    if (warningMap.containsKey(key)) {
                        int warningNum = warningMap.get(key) + 1;
                        warningMap.put(key, warningNum);
                        if (warningNum % 18 != 0) {
                            continue;
                        }
                    }
                    String message = "日期：" + today
                            + "\nappid：" + appid
                            + "\n应用名：" + BlankUtils.getString(map.get("app_name"))
                            + "\n当日gap：" + String.format("%.2f", gap * 100) + "%"
                            + "\n历史Gap：" + String.format("%.2f", hisGap * 100) + "%"
                            + "\n偏差值：" + String.format("%.2f", v * 100) + "%"
                            + "\n"
                            + "\n媒体付费数:" + BlankUtils.getInt(map.get("paycount"))
                            + "\n综合付费数：" + pay_users
                            + "\n历史存在付费的用户数：" + hisPaidUser.size()
                            + "\n历史存在付费的用户：" + String.join(",", hisPaidUser)
                            + "\n实际首次付费数：" + firstPayUsers.size()
//                        + "\n实际首次付费用户：" + String.join(",", firstPayUsers)
                            + "\n自归因上报次数：" + submitUsers.size()
                            + "\n最后上报时间：" + BlankUtils.getString(submitDetail.get("create_time"))
                            + "\n自归因上报缺失用户：" + String.join(",", submitLossUser)
                            + "\n付费与激活时间相差超过30天的用户数：" + active30DayUser.size()
                            + "\n付费与激活时间相差超过30天的用户：" + String.join(",", active30DayUser);
                    log.info(message);
                    FeishuUtils.sendMultiUserMsg("shenling", message);
                    warningMap.putIfAbsent(key, 0);
                }
            }catch (Exception e) {
                log.error("error",e);
            }
        }
        dnwxBiAdtMapper.batchInsertCameraPayGap(cameraStatisticPos);
    }

}
