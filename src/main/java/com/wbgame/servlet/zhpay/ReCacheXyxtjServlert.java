package com.wbgame.servlet.zhpay;


import java.io.IOException;

import java.io.PrintWriter;
import java.util.List;


import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.wbgame.service.AdService;
import com.wbgame.utils.HttpClientUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * xyxtj项目下红包接口缓存
 * 
 */ 
@WebServlet(name="recacheXyxtj",urlPatterns="/recacheXyxtj")
public class ReCacheXyxtjServlert extends HttpServlet{

	private static final long serialVersionUID = 1L;

	private static Log logger = LogFactory.getLog(ReCacheXyxtjServlert.class);

	@Autowired
	private AdService adService;
  
	protected void doGet(HttpServletRequest request,  
			HttpServletResponse response) throws ServletException, IOException {

		response.setCharacterEncoding("UTF-8");
		response.setHeader("Access-Control-Allow-Origin", "*");
		String mapid=BlankUtils.checkNull(request, "mapid");//id

		PrintWriter out = response.getWriter();
		try {
			/** 拉取刷新列表，遍历刷新服务地址 */
			String sql = "select url from dn_recache_config where mark = 'xyxtj' ";
			List<String> urlList = adService.queryListString(sql);
			for (String url : urlList) {
				String resp = HttpClientUtils.getInstance().httpGet(url + "/refreshCache?pid=" + mapid );
				if(resp == null || !"ok".equals(resp.trim())){
					out.print("fail");
					out.close();
					return;
				}
			}
			out.print("ok");
			out.close();
		}catch (Exception e){
			logger.error("recacheXyxtj error:",e);
			out.print("fail");
			out.close();
		}

	}
	


	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp)throws ServletException, IOException {
		doGet(req,resp);
	}

	
}
