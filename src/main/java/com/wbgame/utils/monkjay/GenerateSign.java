package com.wbgame.utils.monkjay;


import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

public class GenerateSign {

	private static final Charset UTF_8 = StandardCharsets.UTF_8;

	public static String getSignStr(Map<String, String> params) {
		List<String> paramStr = new ArrayList<>(params.size());
		for (String key : params.keySet()) {
			paramStr.add(key + "=" + params.get(key));
		}

		Collections.sort(paramStr);
		return String.join("&",paramStr);
	}

	public static String generateSign(String content, String algorithm, String securityKey) {
		content += securityKey;
		MessageDigest md;
		try {
			md = MessageDigest.getInstance(algorithm);
		} catch (NoSuchAlgorithmException e) {
			throw new IllegalArgumentException(e);
		}
		return bytesToHex(md.digest(content.getBytes(UTF_8)));
	}

	private static String bytesToHex(byte[] bytes) {
		StringBuilder sb = new StringBuilder();
		for (byte b : bytes) {
			sb.append(String.format("%02x", b));
		}
		return sb.toString();
	}

	public static void main(String[] args) {
		System.out.printf("sign: %s\n", generateSign(getSignStr(null), "MD5", ""));
	}
}
