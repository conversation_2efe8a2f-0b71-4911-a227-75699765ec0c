package com.wbgame.utils.monkjay;

import java.util.Map;

/**
 * 穿山甲-聚合管理API工具类
 */
public class Utils {

	public static final String MEDIATION_URL = "https://www.csjplatform.com/union_media/open/api/mediation/";
	public static final String SECURITY_KEY = "w9cmX-Fdi7vJm0ZElOKZ1ZgsAR10sUwv4FrPMFgAvY0="; // 替换 SECURITY_KEY
	public static final String VERSION = "2.0";
	public static final String SIGN_TYPE = "MD5";

	public static Map<String, String> setSign(Integer userId, Integer roleId, Map<String, String> params) {
		long timestamp = System.currentTimeMillis();
		params.put("user_id", userId+"");
		params.put("role_id", roleId+"");
		params.put("version", VERSION);
		params.put("sign_type", SIGN_TYPE);
		params.put("timestamp", timestamp+"");
		// GenerateSign 类参考文档开头处的代码示例
		String signStr = GenerateSign.getSignStr(params);
		System.out.println("signStr："+signStr);
		String sign = GenerateSign.generateSign(signStr, SIGN_TYPE, SECURITY_KEY);
		System.out.println("sign："+sign);
		params.put("sign", sign);
		return params;
	}
}
