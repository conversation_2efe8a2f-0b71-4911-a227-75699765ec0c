package com.wbgame.utils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.http.HttpServletResponse;

import com.wbgame.common.Asserts;
import org.apache.poi.hssf.usermodel.HSSFCell;

import org.apache.poi.hssf.util.CellRangeAddress;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.jetbrains.annotations.NotNull;

/**
 * 导出Excel公共方法（大数据量）
 *
 * <AUTHOR>
 */
public class ExportExcelUtil {

    /*
     * 导出xls格式数据
     * */
    @Deprecated
    public static <T> void export2(HttpServletResponse response, List<T> dataList, Map<String, String> rowName, String title) {
        try {
            SXSSFWorkbook workbook = new SXSSFWorkbook(5000);                        // 创建工作簿对象
            SXSSFSheet sheet = workbook.createSheet("sheet");// 创建工作表
            sheet.setDefaultColumnWidth(12);                                   // 设置默认列宽

            // 产生表格标题行
            SXSSFRow rowm = sheet.createRow(0);
            SXSSFCell cellTiltle = rowm.createCell(0);

            //sheet样式定义【getColumnTopStyle()/getStyle()均为自定义方法 - 在下面  - 可扩展】
            XSSFCellStyle columnTopStyle = getAndSetXSSFCellStyleOne(workbook);//获取列头样式对象
            XSSFCellStyle style = getAndSetXSSFCellStyleTwo(workbook);                    //单元格样式对象

            sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, (rowName.size() - 1)));
            cellTiltle.setCellStyle(columnTopStyle);
            cellTiltle.setCellValue(title);

            // 定义所需列数
            SXSSFRow rowRowName = sheet.createRow(2);
            Set<String> heads = rowName.keySet();
            Iterator<String> it = heads.iterator();
            int columnNum = heads.size();

            for (int i = 0; i < columnNum; i++) {
                String tit = rowName.get(it.next());
                SXSSFCell cellRowName = rowRowName.createCell(i);                //创建列头对应个数的单元格
                cellRowName.setCellType(HSSFCell.CELL_TYPE_STRING);                //设置列头单元格的数据类型
                RichTextString text = new XSSFRichTextString(tit);
                cellRowName.setCellValue(text);                                    //设置列头单元格的值
                cellRowName.setCellStyle(columnTopStyle);
            }// 在索引2的位置创建行(最顶端的行开始的第二行)

            //将查询出的数据设置到sheet对应的单元格中
            for (int i = 0; i < dataList.size(); i++) {

                T obj = dataList.get(i);//遍历每个对象
                SXSSFRow row = sheet.createRow(i + 3);//创建所需的行数
                int j = 0;
                for (String key : heads) {
                    //通过反射方式获取对象数据
                    Field field = obj.getClass().getDeclaredField(key);
                    field.setAccessible(true);
                    String next =  field.get(obj) == null ? "" :  field.get(obj).toString();
                    //日期类型转换成yyyy-MM-dd
                    if (field.getType() == Date.class) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        next = sdf.format(field.get(obj));
                    }
                    SXSSFCell cell = row.createCell(j, SXSSFCell.CELL_TYPE_STRING); //设置单元格的数据类型
                    //设置单元格的值
                    cell.setCellValue(next);
                    cell.setCellStyle(style);                                    //设置单元格样式
                    j++;
                }
            }
            //让列宽随着导出的列长自动适应
//            for (int colNum = 0; colNum < columnNum; colNum++) {
//                int columnWidth = sheet.getColumnWidth(colNum) / 256;
//                for (int rowNum = 0; rowNum < sheet.getLastRowNum(); rowNum++) {
//                    SXSSFRow currentRow;
//                    //当前行未被使用过
//                    if (sheet.getRow(rowNum) == null) {
//                        currentRow = sheet.createRow(rowNum);
//                    } else {
//                        currentRow = sheet.getRow(rowNum);
//                    }
//                    if (currentRow.getCell(colNum) != null) {
//                        SXSSFCell currentCell = currentRow.getCell(colNum);
//                        if (currentCell.getCellType() == SXSSFCell.CELL_TYPE_STRING) {
//                            int length = currentCell.getStringCellValue().getBytes().length;
//                            if (columnWidth < length) {
//                                columnWidth = length;
//                            }
//                        }
//                    }
//                }
//                if (colNum == 0) {
//                    sheet.setColumnWidth(colNum, (columnWidth - 2) * 256);
//                } else {
//                    sheet.setColumnWidth(colNum, (columnWidth + 4) * 256);
//                }
//            }

            if (workbook != null) {
                try {
                    response.addHeader("Content-disposition", "attachment;filename="
                            + new String(title.getBytes(), "ISO-8859-1")); // 设置文件名
                    response.setContentType("APPLICATION/OCTET-STREAM");  //设置文件类型
                    response.setCharacterEncoding("ISO-8859-1"); // 设置编码
                    response.setHeader("Access-Control-Allow-Origin", "*"); //允许跨域
                    OutputStream out = response.getOutputStream();
                    workbook.write(out);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /*
     * 导出xls格式数据
     * */
    @Deprecated
    public static void export(HttpServletResponse response, List<Map<String, Object>> dataList, Map<String, String> rowName, String title) {
        try {
            SXSSFWorkbook workbook = new SXSSFWorkbook(5000);                        // 创建工作簿对象
            SXSSFSheet sheet = workbook.createSheet("sheet");// 创建工作表
            sheet.setDefaultColumnWidth(12);                                   // 设置默认列宽

            // 产生表格标题行
            SXSSFRow rowm = sheet.createRow(0);
            SXSSFCell cellTiltle = rowm.createCell(0);

            //sheet样式定义【getColumnTopStyle()/getStyle()均为自定义方法 - 在下面  - 可扩展】
            XSSFCellStyle columnTopStyle = getAndSetXSSFCellStyleOne(workbook);//获取列头样式对象
            XSSFCellStyle style = getAndSetXSSFCellStyleTwo(workbook);                    //单元格样式对象

            sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, (rowName.size() - 1)));
            cellTiltle.setCellStyle(columnTopStyle);
            cellTiltle.setCellValue(title);

            // 定义所需列数
            SXSSFRow rowRowName = sheet.createRow(2);
            Set<String> heads = rowName.keySet();
            Iterator<String> it = heads.iterator();
            int columnNum = heads.size();

            for (int i = 0; i < columnNum; i++) {
                String tit = rowName.get(it.next());
                SXSSFCell cellRowName = rowRowName.createCell(i);                //创建列头对应个数的单元格
                cellRowName.setCellType(HSSFCell.CELL_TYPE_STRING);                //设置列头单元格的数据类型
                RichTextString text = new XSSFRichTextString(tit);
                cellRowName.setCellValue(text);                                    //设置列头单元格的值
                cellRowName.setCellStyle(columnTopStyle);
            }// 在索引2的位置创建行(最顶端的行开始的第二行)

            //将查询出的数据设置到sheet对应的单元格中
            for (int i = 0; i < dataList.size(); i++) {

                Map<String, Object> obj = dataList.get(i);//遍历每个对象
                SXSSFRow row = sheet.createRow(i + 3);//创建所需的行数
                int j = 0;
                for (String key : heads) {
                    String next = obj.get(key) == null ? "" : obj.get(key).toString();
                    SXSSFCell cell = row.createCell(j, SXSSFCell.CELL_TYPE_STRING); //设置单元格的数据类型
                    //设置单元格的值
                    cell.setCellValue(next);
                    cell.setCellStyle(style);                                    //设置单元格样式
                    j++;
                }
            }
//            //让列宽随着导出的列长自动适应
//            for (int colNum = 0; colNum < columnNum; colNum++) {
//                int columnWidth = sheet.getColumnWidth(colNum) / 256;
//                for (int rowNum = 0; rowNum < sheet.getLastRowNum(); rowNum++) {
//                    SXSSFRow currentRow;
//                    //当前行未被使用过
//                    if (sheet.getRow(rowNum) == null) {
//                        currentRow = sheet.createRow(rowNum);
//                    } else {
//                        currentRow = sheet.getRow(rowNum);
//                    }
//                    if (currentRow.getCell(colNum) != null) {
//                        SXSSFCell currentCell = currentRow.getCell(colNum);
//                        if (currentCell.getCellType() == SXSSFCell.CELL_TYPE_STRING) {
//                            int length = currentCell.getStringCellValue().getBytes().length;
//                            if (columnWidth < length) {
//                                columnWidth = length;
//                            }
//                        }
//                    }
//                }
//                if (colNum == 0) {
//                    sheet.setColumnWidth(colNum, (columnWidth - 2) * 256);
//                } else {
//                    sheet.setColumnWidth(colNum, (columnWidth + 4) * 256);
//                }
//            }

            if (workbook != null) {
                try {
                    response.addHeader("Content-disposition", "attachment;filename="
                            + new String(title.getBytes(), "ISO-8859-1")); // 设置文件名
                    response.setContentType("APPLICATION/OCTET-STREAM");  //设置文件类型
                    response.setCharacterEncoding("ISO-8859-1"); // 设置编码
                    response.setHeader("Access-Control-Allow-Origin", "*"); //允许跨域
                    OutputStream out = response.getOutputStream();
                    workbook.write(out);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 获取并设置样式一
     */
    public static XSSFCellStyle getAndSetXSSFCellStyleOne(SXSSFWorkbook sxssfWorkbook) {
        XSSFCellStyle xssfCellStyle = (XSSFCellStyle) sxssfWorkbook.createCellStyle();
        XSSFDataFormat format = (XSSFDataFormat)sxssfWorkbook.createDataFormat();
        // 是否自动换行
        xssfCellStyle.setWrapText(false);
        // 水平居中
        xssfCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        xssfCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 前景颜色
        xssfCellStyle.setFillPattern(XSSFCellStyle.SOLID_FOREGROUND);
        xssfCellStyle.setFillForegroundColor(IndexedColors.AQUA.getIndex());
        // 边框
        xssfCellStyle.setBorderBottom(BorderStyle.THIN);
        xssfCellStyle.setBorderRight(BorderStyle.THIN);
        xssfCellStyle.setBorderTop(BorderStyle.THIN);
        xssfCellStyle.setBorderLeft(BorderStyle.THIN);
        xssfCellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        xssfCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        xssfCellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        xssfCellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        // 防止数字过长,excel导出后,显示为科学计数法,如:防止8615192053888被显示为8.61519E+12
        xssfCellStyle.setDataFormat(format.getFormat("0"));
        return xssfCellStyle;
    }

    /**
     * 获取并设置样式二
     */
    public static XSSFCellStyle getAndSetXSSFCellStyleTwo(SXSSFWorkbook sxssfWorkbook) {
        XSSFCellStyle xssfCellStyle = (XSSFCellStyle) sxssfWorkbook.createCellStyle();
        XSSFDataFormat format = (XSSFDataFormat)sxssfWorkbook.createDataFormat();
        // 是否自动换行
        xssfCellStyle.setWrapText(false);
        // 水平居中
        xssfCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 边框
        xssfCellStyle.setBorderBottom(BorderStyle.THIN);
        xssfCellStyle.setBorderRight(BorderStyle.THIN);
        xssfCellStyle.setBorderTop(BorderStyle.THIN);
        xssfCellStyle.setBorderLeft(BorderStyle.THIN);
        xssfCellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        xssfCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        xssfCellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        xssfCellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        // 垂直居中
        xssfCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 防止数字过长,excel导出后,显示为科学计数法,如:防止8615192053888被显示为8.61519E+12
        xssfCellStyle.setDataFormat(format.getFormat("0"));
        return xssfCellStyle;
    }

    /**
     * 解析通用的表头字段
     * @param value 前端传过来的表头字段
     * @return
     */
    @NotNull
    public static Map<String, String> extractHeader(String value) {
        Map<String, String> headerMap = new LinkedHashMap<>();
        if (!BlankUtils.checkBlank(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        return headerMap;
    }


    /*
     * 导出XLSX格式excel文件-新
     * */
    public static void exportXLSX(HttpServletResponse response, List<Map<String, Object>> dataList, Map<String, String> rowName, String title) {

        SXSSFWorkbook workbook = new SXSSFWorkbook(1000);// 创建工作簿对象
        try {
            SXSSFSheet sheet = workbook.createSheet("sheet");// 创建工作表
            sheet.setDefaultColumnWidth(12);// 设置默认列宽

            // 产生表格标题行
            SXSSFRow rowm = sheet.createRow(0);
            SXSSFCell cellTiltle = rowm.createCell(0);

            //sheet样式定义【getColumnTopStyle()/getStyle()均为自定义方法 - 在下面  - 可扩展】
            XSSFCellStyle columnTopStyle = getAndSetXSSFCellStyleOnePlanB(workbook);//获取列头样式对象
            XSSFCellStyle style = getAndSetXSSFCellStyleTwoPlanB(workbook);                    //单元格样式对象

            sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, (rowName.size() - 1)));
            cellTiltle.setCellStyle(columnTopStyle);
            cellTiltle.setCellValue(title);

            // 定义所需列数
            SXSSFRow rowRowName = sheet.createRow(2);
            Set<String> heads = rowName.keySet();
            Iterator<String> it = heads.iterator();
            int columnNum = heads.size();

            for (int i = 0; i < columnNum; i++) {
                String tit = rowName.get(it.next());
                SXSSFCell cellRowName = rowRowName.createCell(i);                //创建列头对应个数的单元格
                cellRowName.setCellType(HSSFCell.CELL_TYPE_STRING);                //设置列头单元格的数据类型
                RichTextString text = new XSSFRichTextString(tit);
                cellRowName.setCellValue(text);                                    //设置列头单元格的值
                cellRowName.setCellStyle(columnTopStyle);
            }// 在索引2的位置创建行(最顶端的行开始的第二行)

            //将查询出的数据设置到sheet对应的单元格中
            for (int i = 0; i < dataList.size(); i++) {

                Map<String, Object> obj = dataList.get(i);//遍历每个对象
                SXSSFRow row = sheet.createRow(i + 3);//创建所需的行数
                int j = 0;
                for (String key : heads) {
                    String next = obj.get(key) == null ? "" : obj.get(key).toString();
                    SXSSFCell cell = row.createCell(j, SXSSFCell.CELL_TYPE_STRING); //设置单元格的数据类型
                    //设置单元格的值
                    cell.setCellValue(next);
                    cell.setCellStyle(style);                                    //设置单元格样式
                    j++;
                }
            }

            if (workbook != null) {
                try {
                    response.addHeader("Content-disposition", "attachment;filename="
                            + new String(title.getBytes(), "ISO-8859-1")); // 设置文件名
                    response.setContentType("APPLICATION/OCTET-STREAM");  //设置文件类型
                    response.setCharacterEncoding("ISO-8859-1"); // 设置编码
                    response.setHeader("Access-Control-Allow-Origin", "*"); //允许跨域
                    OutputStream out = response.getOutputStream();
                    workbook.write(out);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {

                }
            }
        }

    }

    /*
     * 导出XLSX格式excel文件(不区分对象Object)-新
     * */
    public static <T> void exportXLSX2(HttpServletResponse response, List<T> dataList, Map<String, String> rowName, String title) {

        SXSSFWorkbook workbook = new SXSSFWorkbook(1000);                        // 创建工作簿对象
        try {
            SXSSFSheet sheet = workbook.createSheet("sheet");// 创建工作表
            sheet.setDefaultColumnWidth(12);                                   // 设置默认列宽

            // 产生表格标题行
            SXSSFRow rowm = sheet.createRow(0);
            SXSSFCell cellTiltle = rowm.createCell(0);

            //sheet样式定义【getColumnTopStyle()/getStyle()均为自定义方法 - 在下面  - 可扩展】
            XSSFCellStyle columnTopStyle = getAndSetXSSFCellStyleOnePlanB(workbook);//获取列头样式对象
            XSSFCellStyle style = getAndSetXSSFCellStyleTwoPlanB(workbook);                    //单元格样式对象

            sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, (rowName.size() - 1)));
            cellTiltle.setCellStyle(columnTopStyle);
            cellTiltle.setCellValue(title);

            // 定义所需列数
            SXSSFRow rowRowName = sheet.createRow(2);
            Set<String> heads = rowName.keySet();
            Iterator<String> it = heads.iterator();
            int columnNum = heads.size();

            for (int i = 0; i < columnNum; i++) {
                String tit = rowName.get(it.next());
                SXSSFCell cellRowName = rowRowName.createCell(i);                //创建列头对应个数的单元格
                cellRowName.setCellType(HSSFCell.CELL_TYPE_STRING);                //设置列头单元格的数据类型
                RichTextString text = new XSSFRichTextString(tit);
                cellRowName.setCellValue(text);                                    //设置列头单元格的值
                cellRowName.setCellStyle(columnTopStyle);
            }// 在索引2的位置创建行(最顶端的行开始的第二行)

            //将查询出的数据设置到sheet对应的单元格中
            for (int i = 0; i < dataList.size(); i++) {

                T obj = dataList.get(i);//遍历每个对象
                SXSSFRow row = sheet.createRow(i + 3);//创建所需的行数
                int j = 0;
                for (String key : heads) {
                    //通过反射方式获取对象数据
                    Field field = obj.getClass().getDeclaredField(key);
                    field.setAccessible(true);
                    String next =  field.get(obj) == null ? "" :  field.get(obj).toString();
                    //日期类型转换成yyyy-MM-dd
                    if (field.getType() == Date.class) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        next = sdf.format(field.get(obj));
                    }
                    SXSSFCell cell = row.createCell(j, SXSSFCell.CELL_TYPE_STRING); //设置单元格的数据类型
                    //设置单元格的值
                    cell.setCellValue(next);
                    cell.setCellStyle(style);                                    //设置单元格样式
                    j++;
                }
            }
            if (workbook != null) {
                try {
                    response.addHeader("Content-disposition", "attachment;filename="
                            + new String(title.getBytes(), "ISO-8859-1")); // 设置文件名
                    response.setContentType("APPLICATION/OCTET-STREAM");  //设置文件类型
                    response.setCharacterEncoding("ISO-8859-1"); // 设置编码
                    response.setHeader("Access-Control-Allow-Origin", "*"); //允许跨域
                    OutputStream out = response.getOutputStream();
                    workbook.write(out);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {

                }
            }
        }

    }

    /**
     * 导出XLSX格式excel文件(不区分对象Object)-(特别版-解决单元格长度限制问题)
     * @param response
     * @param dataList
     * @param rowName
     * @param title
     * @param <T>
     */
    public static <T> void exportXLSXSpecial(HttpServletResponse response, List<T> dataList, Map<String, String> rowName, String title) {

        SXSSFWorkbook workbook = new SXSSFWorkbook(1000);                        // 创建工作簿对象
        try {
            SXSSFSheet sheet = workbook.createSheet("sheet");// 创建工作表
            sheet.setDefaultColumnWidth(12);                                   // 设置默认列宽

            // 产生表格标题行
            SXSSFRow rowm = sheet.createRow(0);
            SXSSFCell cellTiltle = rowm.createCell(0);

            //sheet样式定义【getColumnTopStyle()/getStyle()均为自定义方法 - 在下面  - 可扩展】
            XSSFCellStyle columnTopStyle = getAndSetXSSFCellStyleOnePlanB(workbook);//获取列头样式对象
            XSSFCellStyle style = getAndSetXSSFCellStyleTwoPlanB(workbook);                    //单元格样式对象

            sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, (rowName.size() - 1)));
            cellTiltle.setCellStyle(columnTopStyle);
            cellTiltle.setCellValue(title);

            // 定义所需列数
            SXSSFRow rowRowName = sheet.createRow(2);
            Set<String> heads = rowName.keySet();
            Iterator<String> it = heads.iterator();
            int columnNum = heads.size();

            for (int i = 0; i < columnNum; i++) {
                String tit = rowName.get(it.next());
                SXSSFCell cellRowName = rowRowName.createCell(i);                //创建列头对应个数的单元格
                cellRowName.setCellType(HSSFCell.CELL_TYPE_STRING);                //设置列头单元格的数据类型
                RichTextString text = new XSSFRichTextString(tit);
                cellRowName.setCellValue(text);                                    //设置列头单元格的值
                cellRowName.setCellStyle(columnTopStyle);
            }// 在索引2的位置创建行(最顶端的行开始的第二行)
            //设置单元格最大长度
            resetCellMaxTextLength();
            //将查询出的数据设置到sheet对应的单元格中
            for (int i = 0; i < dataList.size(); i++) {

                T obj = dataList.get(i);//遍历每个对象
                SXSSFRow row = sheet.createRow(i + 3);//创建所需的行数
                int j = 0;
                for (String key : heads) {
                    //通过反射方式获取对象数据
                    Field field = obj.getClass().getDeclaredField(key);
                    field.setAccessible(true);
                    String next =  field.get(obj) == null ? "" :  field.get(obj).toString();
                    //日期类型转换成yyyy-MM-dd
                    if (field.getType() == Date.class) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        next = sdf.format(field.get(obj));
                    }
                    SXSSFCell cell = row.createCell(j, SXSSFCell.CELL_TYPE_STRING); //设置单元格的数据类型
                    //设置单元格的值
                    cell.setCellValue(next);
                    cell.setCellStyle(style);                                    //设置单元格样式
                    j++;
                }
            }

            if (workbook != null) {
                try {
                    response.addHeader("Content-disposition", "attachment;filename="
                            + new String(title.getBytes(), "ISO-8859-1")); // 设置文件名
                    response.setContentType("APPLICATION/OCTET-STREAM");  //设置文件类型
                    response.setCharacterEncoding("ISO-8859-1"); // 设置编码
                    response.setHeader("Access-Control-Allow-Origin", "*"); //允许跨域
                    OutputStream out = response.getOutputStream();
                    workbook.write(out);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {

                }
            }
        }

    }

    /**
     * 获取并设置样式一
     */
    public static XSSFCellStyle getAndSetXSSFCellStyleOnePlanB(SXSSFWorkbook sxssfWorkbook) {
        XSSFCellStyle xssfCellStyle = (XSSFCellStyle) sxssfWorkbook.createCellStyle();
        XSSFDataFormat format = (XSSFDataFormat)sxssfWorkbook.createDataFormat();
        // 是否自动换行
        xssfCellStyle.setWrapText(false);
        // 水平居中
        xssfCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        xssfCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 前景颜色
        xssfCellStyle.setFillPattern(XSSFCellStyle.SOLID_FOREGROUND);
        xssfCellStyle.setFillForegroundColor(IndexedColors.AQUA.getIndex());
        // 边框
        xssfCellStyle.setBorderBottom(BorderStyle.THIN);
        xssfCellStyle.setBorderRight(BorderStyle.THIN);
        xssfCellStyle.setBorderTop(BorderStyle.THIN);
        xssfCellStyle.setBorderLeft(BorderStyle.THIN);
        xssfCellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        xssfCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        xssfCellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        xssfCellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        // 防止数字过长,excel导出后,显示为科学计数法,如:防止8615192053888被显示为8.61519E+12
        xssfCellStyle.setDataFormat(format.getFormat("0"));
        return xssfCellStyle;
    }

    /**
     * 获取并设置样式二
     */
    public static XSSFCellStyle getAndSetXSSFCellStyleTwoPlanB(SXSSFWorkbook sxssfWorkbook) {
        XSSFCellStyle xssfCellStyle = (XSSFCellStyle) sxssfWorkbook.createCellStyle();
        XSSFDataFormat format = (XSSFDataFormat)sxssfWorkbook.createDataFormat();
        // 是否自动换行
        xssfCellStyle.setWrapText(false);
        // 水平居中
        xssfCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 边框
        xssfCellStyle.setBorderBottom(BorderStyle.THIN);
        xssfCellStyle.setBorderRight(BorderStyle.THIN);
        xssfCellStyle.setBorderTop(BorderStyle.THIN);
        xssfCellStyle.setBorderLeft(BorderStyle.THIN);
        xssfCellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        xssfCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        xssfCellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        xssfCellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        // 垂直居中
        xssfCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 防止数字过长,excel导出后,显示为科学计数法,如:防止8615192053888被显示为8.61519E+12
        xssfCellStyle.setDataFormat(format.getFormat("0"));
        return xssfCellStyle;
    }

    /**
     * 反射-设置单元格最大字符长度
     */
    public static void resetCellMaxTextLength() {
        SpreadsheetVersion excel2007 = SpreadsheetVersion.EXCEL2007;
        if (Integer.MAX_VALUE != excel2007.getMaxTextLength()) {
            Field field;
            try {
                field = excel2007.getClass().getDeclaredField("_maxTextLength");
                field.setAccessible(true);
                field.set(excel2007, Integer.MAX_VALUE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /*
     * 导出XLSX格式excel文件-新 转化为byte数组
     * */
    public static byte[] exportXLSX2Byte(HttpServletResponse response, List<Map<String, Object>> dataList, Map<String, String> rowName, String title) {

        SXSSFWorkbook workbook = new SXSSFWorkbook(1000);// 创建工作簿对象
        try {
            SXSSFSheet sheet = workbook.createSheet("sheet");// 创建工作表
            sheet.setDefaultColumnWidth(12);// 设置默认列宽

            // 产生表格标题行
            SXSSFRow rowm = sheet.createRow(0);
            SXSSFCell cellTiltle = rowm.createCell(0);

            //sheet样式定义【getColumnTopStyle()/getStyle()均为自定义方法 - 在下面  - 可扩展】
            XSSFCellStyle columnTopStyle = getAndSetXSSFCellStyleOnePlanB(workbook);//获取列头样式对象
            XSSFCellStyle style = getAndSetXSSFCellStyleTwoPlanB(workbook);                    //单元格样式对象

            sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, (rowName.size() - 1)));
            cellTiltle.setCellStyle(columnTopStyle);
            cellTiltle.setCellValue(title);

            // 定义所需列数
            SXSSFRow rowRowName = sheet.createRow(2);
            Set<String> heads = rowName.keySet();
            Iterator<String> it = heads.iterator();
            int columnNum = heads.size();

            for (int i = 0; i < columnNum; i++) {
                String tit = rowName.get(it.next());
                SXSSFCell cellRowName = rowRowName.createCell(i);                //创建列头对应个数的单元格
                cellRowName.setCellType(HSSFCell.CELL_TYPE_STRING);                //设置列头单元格的数据类型
                RichTextString text = new XSSFRichTextString(tit);
                cellRowName.setCellValue(text);                                    //设置列头单元格的值
                cellRowName.setCellStyle(columnTopStyle);
            }// 在索引2的位置创建行(最顶端的行开始的第二行)

            //将查询出的数据设置到sheet对应的单元格中
            for (int i = 0; i < dataList.size(); i++) {

                Map<String, Object> obj = dataList.get(i);//遍历每个对象
                SXSSFRow row = sheet.createRow(i + 3);//创建所需的行数
                int j = 0;
                for (String key : heads) {
                    String next = obj.get(key) == null ? "" : obj.get(key).toString();
                    SXSSFCell cell = row.createCell(j, SXSSFCell.CELL_TYPE_STRING); //设置单元格的数据类型
                    //设置单元格的值
                    cell.setCellValue(next);
                    cell.setCellStyle(style);                                    //设置单元格样式
                    j++;
                }
            }

            if (workbook != null) {
                try {

                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    workbook.write(outputStream);
                    return outputStream.toByteArray();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {

                }
            }
        }

        return null;
    }

}