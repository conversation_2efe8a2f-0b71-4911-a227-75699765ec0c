package com.wbgame.utils.gzip;

import org.apache.commons.codec.binary.Base64;

public class WjyUtils {
	
	
	/**
	 * 进行内容参数加密和GZIP压缩处理
	 * @param data 二进制数据
	 * @param aeskey aes签名key
	 * @return 二进制加密内容
	 */
	public static byte[] compress(String result, String aeskey) {
		try {
			String base64Str = Base64.encodeBase64String(
					AESUtil.encrypt(result.getBytes(), aeskey.getBytes(), aeskey.getBytes()));
			
			byte[] responseBytes = GZIPUtil.compress(base64Str);
			return responseBytes;
		} catch (Exception e) {
			
		}
		return null;
	}
	
	/**
	 * 进行内容参数解密和GZIP解压缩处理
	 * @param data 二进制数据
	 * @param aeskey aes签名key
	 * @return 解码内容字符串
	 */
	public static String uncompress(byte[] data, String aeskey) {
		try {
			String uncompress = GZIPUtil.uncompress(data);
			String strVal = new String(AESUtil.decrypt(Base64.decodeBase64(uncompress.replace(" ", "+")), aeskey.getBytes(), aeskey.getBytes()),"UTF-8");
			return strVal;
		} catch (Exception e) {
			
		}
		return null;
	}
	
}
