package com.wbgame.utils;

import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.jcodec.api.FrameGrab;
import org.jcodec.common.model.Picture;
import org.jcodec.scale.AWTUtil;
import org.springframework.util.StringUtils;
import javax.imageio.*;
import java.awt.image.BufferedImage;
import java.io.File;

@Slf4j
public class VideoUtil {

    //最大压缩次数
    private static int max_num = 10;

    /**
     * 获取视频的封面图并压缩
     * @param videoFile 视频文件
     * @param fileName
     * @return
     * @throws Exception
     */
    public static File getCoverImage(File videoFile,String fileName,Double maxSize) {
        if (StringUtils.isEmpty(fileName) || videoFile == null) {
            return null;
        }
        //抽帧作为封面
        File outputFile = new File(fileName);
        try {
            Picture picture = FrameGrab.getFrameFromFile(videoFile, 1);
            BufferedImage bufferedImage = AWTUtil.toBufferedImage(picture);
            ImageIO.write(bufferedImage, "jpg", outputFile);
            //判断是否需要压缩
            double size = outputFile.length()/1024;
            double old = outputFile.length()/1024;
            log.info("自动生成封面={},大小={},检查是否需要压缩",fileName,size);
            int index = 1;
            while (size >= maxSize && index < max_num) {
                log.info("压缩生成封面={},{}次,大小={}",fileName,index,size);
                Thumbnails.of(outputFile)
                        .scale(1.0)
                        .outputQuality(0.4)
                        .outputFormat("jpg")
                        .toFile(outputFile);
                outputFile = new File(fileName);
                size = outputFile.length()/1024;
                index++;
            }
            if (size > maxSize) {
                log.info("自动生成封面={},原大小={},压缩大小={},生成失败",outputFile.getName(),old,size);
                outputFile.delete();
                return null;
            }
            log.info("自动生成封面={},原大小={},压缩大小={},生成成功",outputFile.getName(),old,size);
            return outputFile;
        }catch (Exception e) {
            log.error("生成封面异常={}",e.getMessage(),e);
            return null;
        }
    }

    /**
     * 压缩图片
     *
     * @param outputFile
     * @param maxSize
     */
    public static File thumbnailsImage(File outputFile, Double maxSize) {
        try {
            //判断是否需要压缩
            double size = outputFile.length() / 1024;
            double old = outputFile.length() / 1024;
            log.info("自动生成封面={},大小={},检查是否需要压缩", outputFile.getName(), size);
            int index = 1;
            while (size >= maxSize && index < max_num) {
                log.info("压缩生成封面={},{}次,大小={}", outputFile.getName(), index, size);
                Thumbnails.of(outputFile)
                        .scale(1.0)
                        .outputQuality(0.4)
                        .outputFormat("jpg")
                        .toFile(outputFile);
                outputFile = new File(outputFile.getName());
                size = outputFile.length() / 1024;
                index++;
            }
            if (size > maxSize) {
                log.info("自动生成封面={},原大小={},压缩大小={},生成失败", outputFile.getName(), old, size);
                outputFile.delete();
                return null;
            }
            log.info("自动生成封面={},原大小={},压缩大小={},生成成功", outputFile.getName(), old, size);
            return outputFile;
        } catch (Exception e) {
            log.error("生成封面异常={}", e.getMessage(), e);
            return null;
        }
    }

}
