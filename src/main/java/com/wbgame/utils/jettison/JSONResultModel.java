package com.wbgame.utils.jettison;

import java.io.Serializable;



public class JSONResultModel implements Serializable {
	private static final long serialVersionUID = -5475146988170264776L;
	private int total;			
	private Object rows;		//分页
	private int ver;			//版本号
	private boolean ret = true;		//返回值
	private String msg;			//消息
	private int errcode;		//错误代码
	private int curPage;		//当前页

	public int getVer() {
		return this.ver;
	}

	public void setVer(int ver) {
		this.ver = ver;
	}
	public int getCurPage() {
		return curPage;
	}

	public void setCurPage(int curPage) {
		this.curPage = curPage;
	}
	public boolean isRet() {
		return this.ret;
	}
	                                                                                                                         
	public void setRet(boolean ret) {
		this.ret = ret;
	}

	public String getMsg() {
		return this.msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public int getErrcode() {
		return this.errcode;
	}

	public void setErrcode(int errcode) {
		this.errcode = errcode;
	}

	public int getTotal() {
		return this.total;
	}

	public void setTotal(int total) {
		this.total = total;
	}

	public Object getRows() {
		return rows;
	}

	public void setRows(Object rows) {
		this.rows = rows;
	}

	
}