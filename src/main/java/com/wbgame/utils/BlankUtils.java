package com.wbgame.utils;

import java.io.IOException;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.util.CollectionUtils;

public class BlankUtils {
	
	public static void main(String[] args) {
		
	}
	
	
	/**
	 * 传入微信的appid和appsecret
	 * 返回access_token字符串
	 * 
	 */
	public static String getWxAccess_Token(String appid, String appsecret){
		CloseableHttpClient client = HttpClients.createDefault();
		try {
			String tkurl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="+appid+"&secret="+appsecret;
			CloseableHttpResponse response = client.execute(new HttpGet(tkurl));
			String responseJson = EntityUtils.toString(response.getEntity());
			String access_token = JSONObject.parseObject(responseJson).getString("access_token");
			return access_token;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				client.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	
	
	/**
	 * 传入实例对象和字符串数组，对该类的所有属性按数组顺序遍历赋值
	 * @param obj
	 * @param vals
	 */
	public static void classForeachSet(Object obj,String[] vals){
		Class<?> c;
		try {
			c = Class.forName(obj.getClass().getCanonicalName());
			Field[] fields = c.getDeclaredFields();
			for (int i = 0; i < fields.length; i++) {
				if(vals.length-1 >= i){ // vals的最后一个下标
					fields[i].setAccessible(true);
					String type = fields[i].getGenericType().toString();
					if(vals[i] == null){ // 为null的话直接赋值，不做其它判断
						fields[i].set(obj, null);
						continue;
					}
					if("int".equals(type) || type.endsWith("Integer")){
						fields[i].set(obj, Integer.valueOf(vals[i]));
					}else if("long".equals(type) || type.endsWith("Long")){
						fields[i].set(obj, Long.valueOf(vals[i]));
					}else if("float".equals(type) || type.endsWith("Float")){
						fields[i].set(obj, Float.valueOf(vals[i]));
					}else if("double".equals(type) || type.endsWith("Double")){
						fields[i].set(obj, Double.valueOf(vals[i]));
					}else{
						fields[i].set(obj, vals[i]);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 传入实例对象，按顺序返回该对象属性的字符串数组
	 * @param obj
	 * @return
	 */
	public static String[] classForeachGet(Object obj){
		Class<?> c = null;
		try {
			c = Class.forName(obj.getClass().getCanonicalName());
			Field[] fields = c.getDeclaredFields();
			String[] vals = new String[fields.length]; 
			for(int i = 0; i < fields.length; i++){
				fields[i].setAccessible(true);
				if(null != fields[i].get(obj)){
					vals[i] = fields[i].get(obj).toString();
				}else{
					vals[i] = "";
				}
			}
			return vals;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 遍历输出打印该类的所有属性和值
	 * @param obj
	 * @param type 输出方式，0-默认字段&值、2-map格式遍历、3-set方法遍历、4-get方法遍历
	 */
	public static void classForeachEcho(Object obj, int... type){
		Class<?> c = null;  
		try {
			c = Class.forName(obj.getClass().getCanonicalName());
			Field[] fields = c.getDeclaredFields();
			for(Field f : fields){
				f.setAccessible(true);
				String field = f.toString().substring(f.toString().lastIndexOf(".")+1); //取出属性名称  
				
				if(type != null && type.length == 1){
					if(0 == type[0])
						System.out.println(field+",");
					if(1 == type[0])
						System.out.println("#{li."+field+"},");
					if(2 == type[0])
						System.out.println("params.put(\""+field+"\", rp.get"+captureName(field)+"());");
					else if(3 == type[0])
						System.out.println("info.set"+captureName(field)+"();");
					else if(4 == type[0])
						System.out.println("info.get"+captureName(field)+"();");
					
					continue;
				}
				
				// 输出正常形式
				System.out.print(obj.getClass().getSimpleName()+"." +field+ " --> ");
				
				if(null != f.get(obj) && f.get(obj).getClass().isArray()){ // 为数组，使用逗号隔开输出
					int length = Array.getLength(f.get(obj));
					Object[] os = new Object[length];
					for (int i = 0; i < os.length; i++) {
						os[i] = Array.get(f.get(obj), i);
						if(i != os.length-1){
							System.out.print(os[i] + ",");
						}else{
							System.out.print(os[i]);
						}
					}
				}else if(null != f.get(obj)){
					System.out.print(f.get(obj));
				}
				System.out.println();
			} 
		} catch (Exception e) {
			e.printStackTrace();
		}  
	}
	
	
	/**
	 * 通过不同类型数组生成建表sql语句
	 * @param tableName 表名
	 * @param obj 使用的数组对象
	 * @param vals 注释数组
	 */
	public static void classForeachCreateSql(String tableName, Object[] objs, String[] vals){
		try {
			System.out.println("CREATE TABLE `"+tableName+"` (");
			for(int i = 0; i < objs.length; i++){
				Object obj = objs[i];
				String type = obj.getClass().getTypeName();
				if("int".equals(type) || type.endsWith("Integer")){
					System.out.print("\t" + "`"+obj+"` int(10) DEFAULT NULL");
				}else if("long".equals(type) || type.endsWith("Long")){
					System.out.print("\t" + "`"+obj+"` bigint(20) DEFAULT NULL");
				}else if("float".equals(type) || type.endsWith("Float")
						|| "double".equals(type) || type.endsWith("Double")){
					System.out.print("\t" + "`"+obj+"` decimal(10,2) DEFAULT NULL");
				}else{
					System.out.print("\t" + "`"+obj+"` varchar(255) DEFAULT NULL");
				}
				
				// 有注释则加入
				if(vals != null && !vals[i].equals("")){
					System.out.print(" COMMENT '"+vals[i]+"'");
				}
				
				if(i == objs.length-1)
					System.out.println("");
				else
					System.out.println(",");
			}
			System.out.println(") ENGINE=InnoDB DEFAULT CHARSET=utf8;");
		} catch (Exception e) {
			e.printStackTrace();
		}  
	}
	
	/**
	 * 通过javabean生成建表sql语句
	 * @param tableName 表名
	 * @param obj 使用的javabean
	 * @param vals 注释数组
	 */
	public static void classForeachCreateSql(String tableName, Object obj, String[] vals){
		Class<?> c = null;  
		try {
			c = Class.forName(obj.getClass().getCanonicalName());
			Field[] fields = c.getDeclaredFields();
			System.out.println("CREATE TABLE `"+tableName+"` (");
			for(int i = 0; i < fields.length; i++){
				Field f = fields[i];
				f.setAccessible(true);
				String field = f.toString().substring(f.toString().lastIndexOf(".")+1); //取出属性名称  
				String type = f.getGenericType().toString();
				if("int".equals(type) || type.endsWith("Integer")){
					System.out.print("\t" + "`"+field+"` int(10) DEFAULT NULL");
				}else if("long".equals(type) || type.endsWith("Long")){
					System.out.print("\t" + "`"+field+"` bigint(20) DEFAULT NULL");
				}else if("float".equals(type) || type.endsWith("Float")
						|| "double".equals(type) || type.endsWith("Double")){
					System.out.print("\t" + "`"+field+"` decimal(10,2) DEFAULT NULL");
				}else{
					System.out.print("\t" + "`"+field+"` varchar(255) DEFAULT NULL");
				}
				
				// 有注释则加入
				if(vals != null && !vals[i].equals("")){
					System.out.print(" COMMENT '"+vals[i]+"'");
				}
				
				if(i == fields.length-1)
					System.out.println("");
				else
					System.out.println(",");
			}
			System.out.println(") ENGINE=InnoDB DEFAULT CHARSET=utf8;");
		} catch (Exception e) {
			e.printStackTrace();
		}  
	}
	
	/**
	 * 通过javabean生成插入sql语句
	 * @param tableName 表名
	 * @param obj 使用的javabean
	 */
	public static void classForeachInsertSql(String tableName, Object obj){
		Class<?> c = null;
		try {
			c = Class.forName(obj.getClass().getCanonicalName());
			Field[] fields = c.getDeclaredFields();
			
			String columns = "";
			String vals = "";
			for(int i = 0; i < fields.length; i++){
				Field f = fields[i];
				f.setAccessible(true);
				String field = f.toString().substring(f.toString().lastIndexOf(".")+1); //取出属性名称  
				columns = columns + field + ",";
				vals = vals +":"+ field + ",";
			}
			columns = columns.substring(0, columns.length()-1);
			vals = vals.substring(0, vals.length()-1);
			
			System.out.println("insert into "+tableName+"("+columns+") values("+vals+")");
		} catch (Exception e) {
			e.printStackTrace();
		}  
	}
	
	/**
	 * 通过javabean生成Mybatis插入sql语句
	 * @param tableName 表名
	 * @param obj 使用的javabean
	 */
	public static void classForeachMbInsertSql(String tableName, Object obj){
		Class<?> c = null;
		try {
			c = Class.forName(obj.getClass().getCanonicalName());
			Field[] fields = c.getDeclaredFields();
			
			String columns = "\n\t";
			String vals = "\n\t";
			for(int i = 0; i < fields.length; i++){
				Field f = fields[i];
				f.setAccessible(true);
				String field = f.toString().substring(f.toString().lastIndexOf(".")+1); //取出属性名称  
				
				if(i == (fields.length-1)){ // 最后一个
					columns = columns + field + "\n";
					vals = vals +"#{"+ field + "}\n";
				}else{
					columns = columns + field + ",\n\t";
					vals = vals +"#{"+ field + "},\n\t";
				}
			}
			
			System.out.println("insert into "+tableName+"("+columns+") values("+vals+")");
		} catch (Exception e) {
			e.printStackTrace();
		}  
	}
	
	/**
	 * 通过javabean生成Mybatis修改sql语句
	 * @param tableName 表名
	 * @param obj 使用的javabean
	 * @param where 作为修改条件的 参数名
	 */
	public static void classForeachMbUpdateSql(String tableName, Object obj, String... where){
		Class<?> c = null;
		try {
			c = Class.forName(obj.getClass().getCanonicalName());
			Field[] fields = c.getDeclaredFields();
			List<String> asList = Arrays.asList(where);
			
			System.out.println("update "+tableName+" set ");
			for(int i = 0; i < fields.length; i++){
				Field f = fields[i];
				f.setAccessible(true);
				String field = f.toString().substring(f.toString().lastIndexOf(".")+1); //取出属性名称  
				
				if(asList.contains(field))
					continue;
				
				if(i == (fields.length-1)){ // 最后一个
					System.out.println("\t"+field+" = "+"#{"+field+"}");
				}else{
					System.out.println("\t"+field+" = "+"#{"+field+"},");
				}
			}
			
			System.out.print("where ");
			for (int p = 0; p < where.length; p++) {
				if(p == 0){
					System.out.print(where[p]+" = "+"#{"+where[p]+"}");
				}else{
					System.out.print(" and "+where[p]+" = "+"#{"+where[p]+"}");
				}
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}  
	}

	
	/**
	 * 检查请求参数，为null则赋值为""
	 * @param request
	 * @param fieldName
	 * @return
	 */
	public static Object checkNum(Object obj) {
		Object	o = null == obj ? 0:obj;
		return o;
		
	}
	
	/**
	 * Int参数，为null则赋值为0
	 * @param request
	 * @param fieldName
	 * @return
	 */
	public static String checkNull(HttpServletRequest request, String fieldName) {
		String fieldValue = request.getParameter(fieldName);
		String temp = checkBlank(fieldValue) ? "" : fieldValue.trim();
		return temp;
	}
	
	/**
	 * 将http请求传递的参数存入指定Map返回
	 * @param request
	 * @param args 请求参数名数组
	 * @return 请求参数值的Map对象
	 */
	public static Map<String, String> getParameter(HttpServletRequest request, String... args) {
		return getParameter(true, request, args);
	}
	
	/**
	 * 将http请求传递的参数存入指定Map返回
	 * @param isLimit 是否分页true:false
	 * @param request
	 * @param args 请求参数名数组
	 * @return 请求参数值的Map对象
	 */
	public static Map<String, String> getParameter(boolean isLimit, HttpServletRequest request, String... args) {
		Map<String, String> paramMap = new HashMap<String, String>();
		for (String param : args) {
			paramMap.put(param, request.getParameter(param));
		}
		
		/** 处理分页 */
		if(isLimit){
			String start = checkNull(request, "start");
			String limit = checkNull(request, "limit");
			
			/** pageStart 和 limit设置值 */
			int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
			int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
			int pageNum = (pageStart / pageSize) + 1;
			
			paramMap.put("pageNum", pageNum+"");
			paramMap.put("pageSize", pageSize+"");
		}
		
		/** 处理日期默认值 */
		if(checkBlank(paramMap.get("sdate")) || checkBlank(paramMap.get("edate"))){
			paramMap.put("sdate", DateTime.now().minusDays(1).toString("yyyy-MM-dd"));
			paramMap.put("edate", DateTime.now().minusDays(1).toString("yyyy-MM-dd"));
		}
		
		/** 处理应用组数据 */
		List<String> apps = new ArrayList<>();
		if(!checkBlank(paramMap.get("appGroup"))){
			for (String id : paramMap.get("appGroup").split(",")) {
				List<String> group = CommonUtil.appGroupMap.get(id);
				if(group != null && group.size() > 0)
					apps.addAll(group);
			}
			if(apps != null && apps.size() > 0){
				paramMap.put("apps", String.join(",", apps));
			}
		}
		
		return paramMap;
	}
	
	/**
	 * 将应用组参数逻辑处理后存入指定查询Map
	 * @param paramMap 请求参数Map
	 * @return 
	 */
	public static void setAppGroup(Map<String, Object> paramMap) {
		
		/** 处理应用组数据 */
		if(paramMap.get("appGroup") != null && !checkBlank(paramMap.get("appGroup")+"")){
			List<String> apps = new ArrayList<>();
			for (String id : (paramMap.get("appGroup")+"").split(",")) {
				List<String> group = CommonUtil.appGroupMap.get(id);
				if(group != null && group.size() > 0)
					apps.addAll(group);
			}
			if(apps != null && apps.size() > 0){
				List<String> collect = apps.stream().distinct().collect(Collectors.toList());
				paramMap.put("apps", String.join(",", collect));
			}
		}
	}
	
	/**
	 * 判断字符串为数字?true:false
	 * @param str
	 * @return
	 */
	public static boolean isNumeric(String str) {
        // 该正则表达式可以匹配所有的数字 包括负数
        Pattern pattern = Pattern.compile("-?[0-9]+\\.?[0-9]*");
        String bigStr;
        try {
            bigStr = new BigDecimal(str).toString();
        } catch (Exception e) {
            return false; //异常 说明包含非数字。
        }

        // matcher是全匹配
        Matcher isNum = pattern.matcher(bigStr); 
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }
	
	
	/**
	 * 检查字符对象是否为空?true:false
	 * @param obj
	 * @return
	 */
	public static boolean checkBlank(String obj){
		if(obj == null || obj.isEmpty())
			return true;
		else
			return false;
	}
	
	/**
	 * 判断字符串是否可以转化为JSON对象?true:false
	 * 
	 * @param content
	 * @return
	 */
	public static boolean isJSONObject(String content) {
		if(checkBlank(content))
			return false;
		try {
			JSONObject jsonStr = JSONObject.parseObject(content);
			return true;
		} catch (Exception e) {
			return false;
		}
	}
	
	/**
	* 判断字符串是否可以转化为JSON数组?true:false
	* 
	* @param content
	* @return
	*/
	public static boolean isJSONArray(String content) {
	    if(checkBlank(content))
	        return false;
	    try {
	        JSONArray jsonStr = JSONArray.parseArray(content);
	        return true;
	    } catch (Exception e) {
	        return false;
	    }
	}
	
	/**
	 * 检查字符对象是否为字母、数字、中文组成
	 * @param obj
	 * @return
	 */
	public static boolean isLetterDigitOrChinese(String str) {  
		String regex = "^[A-Za-z0-9\u4e00-\u9fa5]+$";  
		return str.matches(regex);
	}  
	
	/**
	 * 设置数值保留两位小数
	 * 
	 * @param number
	 * @param type 1-不进行四舍五入、2-进行四舍五入
	 * @return
	 */
	public static String getNumFormat(double number, int... type){
		DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
		DecimalFormat df = new DecimalFormat("#0.00");//设置保留两位小数
		if(type == null)
			df.setRoundingMode(RoundingMode.FLOOR);//设置不进行四舍五入
		else if(type[0] == 1)
			df.setRoundingMode(RoundingMode.FLOOR);//设置不进行四舍五入
		else if(type[0] == 2)
			df.setRoundingMode(RoundingMode.HALF_UP);//设置进行四舍五入
		
		return df.format(number);
	}

	/**
	 * 首字母大写
	 * @param name
	 * @return
	 */
	public static String captureName(String name) {
		char[] cs = name.toCharArray();
		cs[0]-=32;
		return String.valueOf(cs);
	}
	
	/**
	 * 将_形式字段名改为驼峰形式
	 * @param strs
	 */
	public static void changeName(String[] strs){
		for(int i = 0; i < strs.length; i++){
			String[] sp = strs[i].split("_");
			for(int ii = 0; ii < sp.length; ii++){
				if(ii == 0)
					System.out.print(sp[ii]);
				else
					System.out.print(captureName(sp[ii]));
			}
		}
	}
	
	/** 
     * 生成特定位数的随机字符数字
     * @param length 
     * @return 
     */  
    public static String getRandomStr(int length) {  
    	String abc = "abcdefghijklmnopqrstuvwxyz0123456789";
		Random random = new Random();
		StringBuffer sf = new StringBuffer();
		
		for (int i = 0; i < length; i++) {
			int num = random.nextInt(abc.length());
			// System.out.println(num);
			sf.append(abc.charAt(num));
		}
		return sf.toString();
    }
    /** 
     * 生成特定位数的随机数字组合
     * @param length 
     * @return 
     */  
    public static String getRandomNumber(int length) {  
    	String abc = "0123456789";
    	Random random = new Random();
    	StringBuffer sf = new StringBuffer();
    	
    	for (int i = 0; i < length; i++) {
    		int num = random.nextInt(abc.length());
    		// System.out.println(num);
    		sf.append(abc.charAt(num));
    	}
    	return sf.toString();
    }
    
    /** 
     * 过滤掉sql注入关键字，包含?true:false
     * @param str 
     * @return 
     */ 
    public static boolean sqlValidate(String str) {
    	if(str == null) {return false;}
    	str = str.toLowerCase();
    	String badStr = "set|and|exec|execute|insert|select|delete|update|count|drop|*|%|chr|mid|master|" +
    			"truncate|char|declare|sitename|net user|xp_cmdshell|;|or|+|like'|execute|create|" +
    			"table|from|grant|use|user|group_concat|column_name|" +
    			"information_schema.columns|table_schema|union|where|order|by|" +
    			"chr|mid|master|truncate|declare|like|//|/|#";
    	String[] badStrs = badStr.split("\\|");
    	for (int i = 0; i < badStrs.length; i++) {
    		if (str.contains(badStrs[i])) {
    			return true;
    		}
    	}
    	return false;
    }
	
    /**
     * 判断字符串是否包含特殊字符
     * @param str
     * @return
     */
    public static boolean isSpecialChar(String str) {
        if(str.contains("�")){
        	return true;
        }else{
        	return false;
        }
    }

	/**
	 * 转换排序字段sql的处理
	 * @param order_str 排序源字符串
	 * @return
	 */
	public static String transformOrder(String order_str){
		return transformOrder(order_str,null,null,null);
	}
	/**
	 * 转换排序字段sql的处理
	 * @param order_str 排序源字符串
	 * @param groups	处理null值后排的维度字段数组
	 * @return
	 */
	public static String transformOrder(String order_str,String groups){
		return transformOrder(order_str,null,null,groups);
	}
	/**
	 * 转换排序字段sql的处理
	 * @param order_str 排序源字符串
	 * @param arr1	处理字符大小写排序的字段数组
	 * @param arr2	处理收入类数值为varchar排序的字段数组
	 * @return
	 */
	public static String transformOrder(String order_str,String[] arr1,String[] arr2){
		return transformOrder(order_str, arr1, arr2, null);
	}

	/**
	 * 转换排序字段sql的处理
	 * @param order_str 排序源字符串
	 * @param arr1	处理字符大小写排序的字段数组
	 * @param arr2	处理收入类数值为varchar排序的字段数组
	 * @param groups	处理null值后排的维度字段数组
	 * @return
	 */
	public static String transformOrder(String order_str,String[] arr1,String[] arr2,String groups){

		/* 处理中文及字母大小写排序 -mysql不需要 */
		List<String> orderArr1 = new ArrayList<String>();
		orderArr1.addAll(Arrays.asList(
				"appname",
				"app_category",
				"cha_type",
				"cha_media",
				"cha_sub_launch",
				"cha_id",
				"agent",
				"ad_sid",
				"adpos_type",
				"strategy",
				"user_group",
				"country"
		));
		if(arr1 != null){
			orderArr1.addAll(Arrays.asList(arr1));
		}

		/* 处理收入类数值为varchar排序 */
		List<String> orderArr2 = new ArrayList<String>();
		orderArr2.addAll(Arrays.asList(
				"income",
				"ecpm"
		));
		if(arr2 != null){
			orderArr2.addAll(Arrays.asList(arr2));
		}

		if(!BlankUtils.checkBlank(order_str)) {
			List<String> fieldList = new ArrayList<>();

			for (String field : order_str.split(",")) {
				String[] kval = field.split(" ");

				if(orderArr1.contains(kval[0])){
					/* 在列表中的字段名，使用LOWER()进行修改 反之不进行处理 */
					fieldList.add(String.format("LOWER(%s) %s", kval[0],kval[1]));

				}else if(orderArr2.contains(kval[0])){
					/* 在列表中的字段名，转换为数值类型再进行排序 */
					fieldList.add(String.format("CONVERT(%s, DECIMAL(20,2)) %s", kval[0],kval[1]));

				}else{
					fieldList.add(kval[0]+" "+kval[1]);
				}
			}
			String trans_str = String.join(",", fieldList);


			/* 处理null值后排的维度字段数组 */
			if(!BlankUtils.checkBlank(groups)){
				String elses = "";
				for (String item : groups.split(",")) {
					elses += "CASE WHEN "+item+" is null or "+item+"='' THEN 1 ELSE 0 END,";
				}
				trans_str = elses + trans_str;
			}
			
			return trans_str;
		}else{
			return null;
		}
	}


	/**
	 * 默认apache的判空工具
	 * @param cs 字符集
	 * @return
	 */
	public static boolean isBlank(CharSequence cs) {
		return StringUtils.isBlank(cs);
	}

	/**
	 * 默认apache的判空工具
	 * @param cs 字符集
	 * @return
	 */
	public static boolean isNotBlank(CharSequence cs) {
		return StringUtils.isNotBlank(cs);
	}

	/**
	 * 判断 一个本来该是数字的字段是否为空
	 * @param str object 预期是数字
	 * @return
	 */
	public static boolean isNotBlankNum(Object str) {
		return str!= null && StringUtils.isNotBlank(str.toString());
	}


	/**
	 * 默认 apache 的判断是否为数字, 注意,小数点和负数判断不出来
	 * @param cs
	 * @return
	 */
	public static boolean isNumeric(CharSequence cs) {
		return StringUtils.isNumeric(cs);
	}

	/**
	 * <p>Replaces all occurrences of a String within another String.</p>
	 *
	 * <p>A {@code null} reference passed to this method is a no-op.</p>
	 *
	 * <pre>
	 * StringUtils.replace(null, *, *)        = null
	 * StringUtils.replace("", *, *)          = ""
	 * StringUtils.replace("any", null, *)    = "any"
	 * StringUtils.replace("any", *, null)    = "any"
	 * StringUtils.replace("any", "", *)      = "any"
	 * StringUtils.replace("aba", "a", null)  = "aba"
	 * StringUtils.replace("aba", "a", "")    = "b"
	 * StringUtils.replace("aba", "a", "z")   = "zbz"
	 * </pre>
	 *
	 * @param text  原文
	 * @param searchString  待替换字段
	 * @param replacement  替换字段
	 * @return the text with any replacements processed,
	 *  {@code null} if null String input
	 */
	public static String replace(final String text, final String searchString, final String replacement) {
		return StringUtils.replace(text, searchString, replacement);
	}

	/**
	 * object 安全转 string
	 * @param object
	 * @return
	 */
	public static String getString(Object object) {
		return object == null ? "" : object.toString().trim();
	}

	/**
	 * object 安全转 int
	 * @param object
	 * @return
	 */
	public static int getInt(Object object) {
		return !isNotBlankNum(object) ? 0 : Integer.parseInt(object.toString());
	}
	/**
	 * object 安全转 long
	 * @param object
	 * @return
	 */
	public static long getLong(Object object) {
		return !isNotBlankNum(object) ? 0 : Long.parseLong(object.toString());
	}
	/**
	 * object 安全转 double
	 * @param object
	 * @return
	 */
	public static double getDouble(Object object) {
		return !isNotBlankNum(object) ? 0 :Double.parseDouble(object.toString());
	}

	/**
	 * 判断集合是否为空, 复用spring 的 CollectionUtils
	 * @param dateMap
	 * @return
	 */
	public static boolean isEmptyCollection(Collection<?> dateMap) {
		return CollectionUtils.isEmpty(dateMap);
	}

	/**
	 * 判断集合是否为空, 复用spring 的 CollectionUtils
	 * @param dateMap
	 * @return
	 */
	public static boolean isEmptyCollection(Map<?, ?> dateMap) {
		return CollectionUtils.isEmpty(dateMap);
	}

	/**
	 * 判断集合是否不为空, 复用spring 的 CollectionUtils
	 * @param dateMap
	 * @return
	 */
	public static boolean isNotEmptyCollection(Collection<?> dateMap) {
		return !CollectionUtils.isEmpty(dateMap);
	}

	/**
	 * 判断集合是否不为空, 复用spring 的 CollectionUtils
	 * @param dateMap
	 * @return
	 */
	public static boolean isNotEmptyCollection(Map<?, ?> dateMap) {
		return !CollectionUtils.isEmpty(dateMap);
	}

	/**
	 * 设置数值保留几位小数
	 *
	 * @param number
	 * @param point 小数点后几位
	 * @return
	 */
	public static String getNumFormatHalfUp(double number, int point){

		StringBuilder pattern = new StringBuilder("#0");
		if (point > 0) {
			pattern.append(".");
			for (int i = 0; i < point; i++) {
				pattern.append("0");
			}
		}

		DecimalFormat df = new DecimalFormat(pattern.toString());//设置保留两位小数
		df.setRoundingMode(RoundingMode.HALF_UP);//设置进行四舍五入
		return df.format(number);
	}

	public static String buildGetUrl(String url, Map<String, String> params) {
		StringBuilder sb = new StringBuilder(url);
		if (params != null && params.size() > 0) {
			if (url.contains("?")) {
				sb.append("&");
			} else {
				sb.append("?");
			}
			for (String key : params.keySet()) {
				sb.append(key).append("=").append(params.get(key)).append("&");
			}
			sb.deleteCharAt(sb.length() - 1);
		}
		return sb.toString();
	}

	/**
	 * 处理导出表头
	 * @param value 自定义列数据
	 * @return 表头Map
	 */
	public static Map<String, String> processExportHeader(String value) {
		Map<String, String> headerMap = new LinkedHashMap<String, String>();

		if (!BlankUtils.checkBlank(value)){
			//自定义列数据
			try {
				String[] split = value.split(";");
				for (int i = 0;i<split.length;i++) {
					String[] s = split[i].split(",");
					headerMap.put(s[0],s[1]);
				}
			}catch (Exception e) {
				e.printStackTrace();
			}
		}

		return headerMap;
	}

}
