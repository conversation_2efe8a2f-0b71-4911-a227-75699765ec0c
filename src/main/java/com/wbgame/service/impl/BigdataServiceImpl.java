package com.wbgame.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.wbgame.mapper.adb.DnwxBiMapper;
import com.wbgame.mapper.adv2.Adv2Mapper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.master.ApkMapper;
import com.wbgame.mapper.master.BigMapper;
import com.wbgame.mapper.usertag.UsertagMapper;
import com.wbgame.pojo.*;
import com.wbgame.pojo.adv2.DnAdsidCashTotalVo;
import com.wbgame.pojo.adv2.DnChaRevenueTotal;
import com.wbgame.pojo.adv2.ExtendAdsidVo;
import com.wbgame.pojo.custom.AdvFeeVo;
import com.wbgame.pojo.custom.*;
import com.wbgame.pojo.push.ApkProductKeepVo;
import com.wbgame.pojo.push.ApkProductStatsVo;
import com.wbgame.service.AdService;
import com.wbgame.service.BigdataService;
import com.wbgame.utils.BlankUtils;

import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

@Service("bigdataService")
public class BigdataServiceImpl implements BigdataService {

	Logger logger = LoggerFactory.getLogger(BigdataServiceImpl.class);
	
	@Autowired
	private AdMapper adMapper;
	@Autowired
	private BigMapper bigMapper;
	@Autowired
	private UsertagMapper usertagMapper;
	@Autowired
	private DnwxBiMapper dnwxBiMapper;
	@Resource
	private Adv2Mapper adv2Mapper;
	
	
	@Override
	public Map<String, DnAdsidCashTotalVo> getAdsidCashTotal(String date){
		
		String query = "select ad_sid mapkey,ad_sid adsid,dnappid appid,cha_id,agent,cha_type_name,cha_media,IFNULL(TRUNCATE(sum(aa.revenue)/sum(aa.pv)*1000,2),0) ecpm "+
				"from dn_cha_cash_total aa where date = '"+date+"' and app_id != '0' and dnappid != '0' and ad_sid is not null and ad_sid != '' and cha_id is not null "+
				"GROUP BY ad_sid";
		Map<String, DnAdsidCashTotalVo> ecpmMap = adMapper.getAdsidCashTotal(query);
		
		return ecpmMap;
	}
	@Override
	public Map<String, ExtendAdsidVo> getAdsidManageMap(){
		
		String query = "select adsid,adsid mapkey,open_type,sdk_code,sdk_appid,sdk_adtype from dnwx_cfg.dn_extend_adsid_manage group by adsid";
		Map<String, ExtendAdsidVo> adsidMap = adMapper.getAdsidManageMap(query);
		
		return adsidMap;
	}
	
	@Override
	public Map<String, DnChaRevenueTotal> getChaidManageMap(){
		
		String query = "select cha_id,cha_id mapkey,yy.type_name cha_type_name,cha_media,cha_sub_launch,cha_ratio from dn_channel_info xx,dn_channel_type yy where xx.cha_type=yy.type_id ";
		Map<String, DnChaRevenueTotal> chaidMap = adMapper.getChaidManageMap(query);
		
		return chaidMap;
	}
	
	@Override
	public int execSql(String sql) {
		return bigMapper.execSql(sql);
	}
	@Override
	public List<String> queryListString(String sql) {
		return bigMapper.queryListString(sql);
	}
	@Override
	public List<Map<String, Object>> queryListMap(String sql) {
		return bigMapper.queryListMap(sql);
	}
	@Override
	public List<Map<String, String>> queryListMapOne(String sql) {
		return bigMapper.queryListMapOne(sql);
	}
	@Override
	public Map<String, Map<String, Object>> queryListMapOfKey(String sql) {
		return bigMapper.queryListMapOfKey(sql);
	}
	@Override
	public <T> List<T> queryListBean(String sql, Class<T> clazz) {
		List<Map<String, String>> listBean = bigMapper.queryListMapOne(sql);
		return JSONArray.parseArray(JSONArray.toJSONString(listBean), clazz);
	}
	@Override
	public int execSqlHandle(String sql, Object obj) {
		return bigMapper.execSqlHandle(sql, obj);
	}
	@Override
	public List<Map<String, String>> queryListMapTwo(String sql, Object obj) {
		return bigMapper.queryListMapTwo(sql, obj);
	}
	@Override
	public int batchExecSql(Map<String, Object> paramMap) {
		return bigMapper.batchExecSql(paramMap);
	}
	@Override
	public int batchExecSqlTwo(Map<String, Object> paramMap) {
		return bigMapper.batchExecSqlTwo(paramMap);
	}
	
	

	@Override
	public boolean syncAdposData(String day) {
		logger.info("同步变现-广告位数据 开始...");
		
		String del = "delete FROM dnwx_bi.dn_extend_adpos_data where tdate = '"+day+"'";
		adMapper.execSql(del);
		
		// 获取广告源对应ecpm
//		String query = "select adsid,ecpm FROM dnwx_cfg.dn_extend_adconfig where adsid is not null group by adsid ";
//		List<Map<String, Object>> listMap = adMapper.queryListMap(query);
//		for (Map<String, Object> info : listMap) {
//			String sql = "update dnwx_bi.ads_admsg_adposdata_daily set ecpm=#{obj.ecpm} where tdate='"+day+"' and adsid=#{obj.adsid} ";
//			dnwxBiMapper.execSqlHandle(sql, info);
//		}
		
		
		/*Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("sql1", "update dnwx_bi.ads_admsg_adposdata_daily set ecpm=#{li.ecpm} where tdate='"+day+"' and adsid=#{li.adsid} ");
		paramMap.put("list", listMap);
		dnwxBiMapper.batchExecSqlTwo(paramMap);*/
		
		
		String query3 = "select tdate,appid,cha_id,prjid,adpos,adpos_type, dau,"+
							"TRUNCATE(SUM(income),2) income,SUM(show_num) show_num,SUM(click_num) click_num,device_num,device_num_adpos_type,device_num_adpos,device_num_prjid "+
					"from ("+
					"	select *,TRUNCATE(ecpm*show_num/1000,2) income from dnwx_bi.ads_admsg_adposdata_daily where tdate = '"+day+"' and adpos is not null "+
					") xx group by tdate,appid,cha_id,prjid,adpos,adpos_type";
		List<Map<String, Object>> queryList3 = dnwxBiMapper.queryListMap(query3);
		
		if(queryList3 != null && queryList3.size() > 0){
			try {
				
				Map<String, Object> paramMap3 = new HashMap<String, Object>();
				paramMap3.put("sql1", "insert into dnwx_bi.dn_extend_adpos_data(tdate,appid,cha_id,prjid,adpos,adpos_type,adsid,income,show_num,click_num,dau,device_num,device_num_adpos_type,device_num_adpos,device_num_prjid) values ");
				paramMap3.put("sql2", " (#{li.tdate},#{li.appid},#{li.cha_id},#{li.prjid},#{li.adpos},#{li.adpos_type},'',#{li.income},#{li.show_num},#{li.click_num},#{li.dau},#{li.device_num},#{li.device_num_adpos_type},#{li.device_num_adpos},#{li.device_num_prjid}) ");
				paramMap3.put("sql3", " ");
				paramMap3.put("list", queryList3);
				adMapper.batchExecSql(paramMap3);
				
				logger.info("同步变现-广告位数据 完成...");
				return true;
			} catch (Exception e) {
				e.printStackTrace();
				logger.info("同步变现-广告位数据 异常...");
			}
		}
		return false;
	}
	
	@Override
	public boolean syncGroupDataTwo(String day) {
		/*String del = "delete FROM dn_extend_group_data_two where tdate = '"+day+"'";
		adService.execSql(del);*/
		logger.info("同步聚合管理数据v2 开始...");
		
		// 从广告配置拉取 rate和strategy等参数
		String sql = "SELECT adsid mapkey,strategy,ecpm,rate "+
				"FROM dnwx_cfg.dn_extend_adconfig where statu = 1 GROUP BY adsid ";
		Map<String, Map<String, Object>> ecpmMap = adMapper.queryListMapOfKey(sql);
		
		// 从广告源管理拉取open_type使用类型
		Map<String, ExtendAdsidVo> adsidMap = getAdsidManageMap();
		
		String query = "SELECT '"+day+"' tdate,appid,cha_id,prjid,adsid,user_label user_group,adjusted_ecpm ecpm, "+
				"SUM(fail_num)+SUM(fill_num) req_num, "+
				"SUM(fill_num) fill_num, "+
				"SUM(selfshow_num) show_num, "+
				"SUM(click_num) click_num "+
				"FROM dnwx_bi.ads_admsg_v4_total_daily where tdate = '"+day+"' and (fail_num > 5 or fill_num > 2 or selfshow_num > 1) "+
				"GROUP BY appid,cha_id,prjid,adsid,user_label HAVING LENGTH(prjid) = 8 ";
		System.out.println("sql2 =="+query);
		List<Map<String, Object>> oneList = dnwxBiMapper.queryListMap(query);
		
		// 匹配adsid 赋值strategy,ecpm,rate
		for (Map<String, Object> act : oneList) {
			
			Map<String, Object> act2 = ecpmMap.get(act.get("adsid")+"");
			if(act2 != null){
            	act.put("strategy", act2.get("strategy")+"");
            	act.put("rate", act2.get("rate")+"");
			}
			
			ExtendAdsidVo act3 = adsidMap.get(act.get("adsid")+"");
			if(act3 != null){
				act.put("adpos_type", act3.getOpen_type());
			}else{
				act.put("adpos_type", "");
			}
		}
		
		if(oneList != null && oneList.size() > 0){
			try {
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("sql1", "REPLACE INTO dnwx_bi.dn_extend_group_data_two(tdate,appid,cha_id,prjid,user_group,adsid,adpos_type,statu,strategy,ecpm,rate,req_num,fill_num,show_num,click_num) values ");
				paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.cha_id},#{li.prjid},#{li.user_group},#{li.adsid},#{li.adpos_type},'1',#{li.strategy},#{li.ecpm},#{li.rate},#{li.req_num},#{li.fill_num},#{li.show_num},#{li.click_num}) ");
				paramMap.put("sql3", " ");
				paramMap.put("list", oneList);
				adMapper.batchExecSql(paramMap);
				
				logger.info("同步聚合管理数据v2 完成...");
				return true;
			} catch (Exception e) {
				e.printStackTrace();
				logger.info("同步聚合管理数据v2 异常...");
			}
		}
		return false;
	}
	
	@Override
	public boolean syncGroupDataAdd(String day) {
		
		logger.info("同步聚合管理v2-新用户 开始...");
		
		// 从广告配置拉取 ecpm和strategy等参数
		String sql = "SELECT adsid mapkey,strategy,ecpm,rate "+
				"FROM dnwx_cfg.dn_extend_adconfig where statu = 1 GROUP BY adsid ";
		Map<String, Map<String, Object>> ecpmMap = adMapper.queryListMapOfKey(sql);
		
		// 从广告源管理拉取open_type使用类型
		Map<String, ExtendAdsidVo> adsidMap = getAdsidManageMap();
		
		String query = "SELECT '"+day+"' tdate,appid,cha_id,prjid,adsid,user_label user_group, "+
				"SUM(add_user_fail_num)+SUM(add_user_fill_num) req_num, "+
				"SUM(add_user_fill_num) fill_num, "+
				"SUM(add_user_selfshow_num) show_num, "+
				"SUM(add_user_click_num) click_num "+
				"FROM dnwx_bi.ads_admsg_v4_total_daily where tdate = '"+day+"' and (add_user_fail_num > 5 or add_user_fill_num > 2 or add_user_selfshow_num > 1) "+
				"GROUP BY appid,cha_id,prjid,adsid,user_label HAVING LENGTH(prjid) = 8 ";
		System.out.println("sql2 =="+query);
		List<Map<String, Object>> oneList = dnwxBiMapper.queryListMap(query);
		
		// 匹配adsid 赋值strategy,ecpm,rate
		for (Map<String, Object> act : oneList) {
			
			Map<String, Object> act2 = ecpmMap.get(act.get("adsid")+"");
			if(act2 != null){
            	act.put("strategy", act2.get("strategy")+"");
            	act.put("ecpm", act2.get("ecpm")+"");
            	act.put("rate", act2.get("rate")+"");
			}
			
			ExtendAdsidVo act3 = adsidMap.get(act.get("adsid")+"");
			if(act3 != null){
				act.put("adpos_type", act3.getOpen_type());
			}else{
				act.put("adpos_type", "");
			}
		}
		
		if(oneList != null && oneList.size() > 0){
			try {
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("sql1", "REPLACE INTO dnwx_bi.dn_extend_group_data_add(tdate,appid,cha_id,prjid,user_group,adsid,adpos_type,statu,strategy,ecpm,rate,req_num,fill_num,show_num,click_num) values ");
				paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.cha_id},#{li.prjid},#{li.user_group},#{li.adsid},#{li.adpos_type},'1',#{li.strategy},#{li.ecpm},#{li.rate},#{li.req_num},#{li.fill_num},#{li.show_num},#{li.click_num}) ");
				paramMap.put("sql3", " ");
				paramMap.put("list", oneList);
				adMapper.batchExecSql(paramMap);
				
				logger.info("同步聚合管理v2-新用户 完成...");
				return true;
			} catch (Exception e) {
				e.printStackTrace();
				logger.info("同步聚合管理v2-新用户 异常...");
			}
		}
		return false;
	}
	
	@Override
	public boolean syncGroupCom(String day) {
		
		logger.info("同步聚合综合查询 开始...");
		
		// 先拉取现在的广告源，再拉取只配置的appid配置的广告源，优先使用appid配置的
		String query = "SELECT adsid as mapkey,appid,adpos_type,strategy,adsid,ecpm,is_newuser "+
				"FROM dnwx_cfg.dn_extend_adconfig WHERE cha_id in ('csj','csjys','baibao','kuaishou','xmcsj','opcsj','apple','google','dy','tt') AND adpos_type in ('plaque','plaqueVideo','video','msg','splash') "+
				"AND appid not in (select appid from yyhz_0308.clean_appid) "+
				"GROUP BY adsid ";
		Map<String, Map<String, Object>> chaMap = adMapper.queryListMapOfKey(query);

		String query2 = "SELECT adsid as mapkey,appid,adpos_type,strategy,adsid,ecpm,is_newuser "+
				"FROM dnwx_cfg.dn_extend_adconfig WHERE appid != '' and cha_id = '' and (prjid = '' or prjid is null) AND adpos_type in ('plaque','plaqueVideo','video','msg','splash') "+
				"AND appid not in (select appid from yyhz_0308.clean_appid) "+
				"GROUP BY adsid ";
		Map<String, Map<String, Object>> appMap = adMapper.queryListMapOfKey(query2);
		chaMap.putAll(appMap);
		
		Collection<Map<String, Object>> oneList = chaMap.values();

		
		String sql2 = "SELECT '"+day+"' tdate,concat(appid,'') appid,adsid,adsid mapkey, "+
				"SUM(fail_num)+SUM(fill_num) req_num, "+
				"SUM(fill_num) fill_num, "+
				"SUM(selfshow_num) show_num, "+
				"SUM(click_num) click_num "+
				"FROM dnwx_bi.ads_admsg_v4_total_daily WHERE tdate = '"+day+"' "+
				"GROUP BY adsid ";
		Map<String, Map<String, Object>> showMap = dnwxBiMapper.queryListMapOfKey(sql2);


		// 匹配adsid 赋值adpos_type,strategy,ecpm
		List<Map<String, Object>> resultList = oneList.stream().map(act -> {
			Map<String, Object> act2 = showMap.get(act.get("adsid"));
			if(act2 != null){
				act.put("req_num", act2.get("req_num"));
				act.put("fill_num", act2.get("fill_num"));
				act.put("show_num", act2.get("show_num"));
			}
			return act;
		}).collect(Collectors.toList());

		
		// 查询出adsid对应的agent内容
		String sql3 = "select appid,cha_id,adsid,agent,adpos_type,sdk_adtype,adsid mapkey from dnwx_cfg.dn_extend_adsid_manage";
		Map<String, Map<String, Object>> sidMap = adMapper.queryListMapOfKey(sql3);
		for (Map<String, Object> act : resultList) {
			Map<String, Object> sid = sidMap.get(act.get("adsid")+"");
			if(sid != null){
				act.put("agent", sid.get("agent"));
				act.put("sdk_adtype", sid.get("sdk_adtype"));
			}
		}
		
		
		if(resultList != null && resultList.size() > 0){
			try {
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("sql1", "REPLACE INTO dn_extend_group_sum(tdate,appid,cha_id,adsid,adpos_type,sdk_adtype,agent,strategy,ecpm,is_newuser,platform_req,platform_fill,platform_show) values ");
				paramMap.put("sql2", " ('"+day+"',#{li.appid},'csj',#{li.adsid},#{li.adpos_type},#{li.sdk_adtype},#{li.agent},#{li.strategy},#{li.ecpm},#{li.is_newuser},#{li.req_num},#{li.fill_num},#{li.show_num}) ");
				paramMap.put("sql3", " ");
				paramMap.put("list", resultList);
				adv2Mapper.batchExecSql(paramMap);
				
				
				String sql4 = "SELECT date,dnappid,ad_sid adsid,IFNULL(TRUNCATE(sum(revenue)/sum(pv)*1000,2),0) ecpm,SUM(click) click,SUM(pv) pv,SUM(return_count) return_count,SUM(request_count) request_count,TRUNCATE(SUM(revenue),2) revenue "+
						"FROM dn_cha_cash_total where date = '"+day+"' AND app_id != '' GROUP BY ad_sid ";
				List<Map<String, Object>> collect = adMapper.queryListMap(sql4);
				
				
				Map<String, Object> paramMap2 = new HashMap<String, Object>();
				paramMap2.put("sql1", "update dn_extend_group_sum set income=#{li.revenue},req_num=#{li.request_count},fill_num=#{li.return_count},show_num=#{li.pv},click_num=#{li.click},platform_ecpm=#{li.ecpm} where tdate='"+day+"' and adsid=#{li.adsid} ");
				paramMap2.put("list", collect);
				adv2Mapper.batchExecSqlTwo(paramMap2);
				
				logger.info("同步聚合综合查询 完成...");
				return true;
			} catch (Exception e) {
				e.printStackTrace();
				logger.info("同步聚合综合查询 异常...");
			}
			
		}
		return false;
	}
	

	@Override
	public boolean syncShowGapTotal(String day) {
		
		logger.info("同步syncShowGapTotal 开始...");
		try {
			
			String del = "delete FROM dnwx_bi.dn_show_gap where tdate = '"+day+"'";
			dnwxBiMapper.execSql(del);
			
			// 分别查询出变现平台明细数据和自统计数据，通过产品+广告类型+广告源关联起来，合并数值
			String sql = "select date tdate,dnappid appid,placement_type,open_type,ad_sid mapkey,agent, "+
					"IFNULL(SUM(request_count),0) request_count,IFNULL(SUM(return_count),0) return_count,IFNULL(SUM(pv),0) pv,IFNULL(SUM(click),0) click "+
					"from dn_cha_cash_total aa "+
					"where date = '"+day+"' and app_id != '0' and dnappid != '0' and ad_sid is not null "+
					"group by ad_sid";
			Map<String, Map<String, Object>> agentMap  = adMapper.queryListMapOfKey(sql);
			
			String sql2 = "SELECT adsid as mapkey,tdate,appid,adsid,IFNULL(SUM(selfshow_num),0) selfshow_num,IFNULL(SUM(click_num),0) click_num, "+
					"IFNULL(SUM(fail_num)+SUM(fill_num),0) req_num,IFNULL(SUM(fill_num),0) fill_num "+
					"FROM dnwx_bi.ads_admsg_v3_total_daily WHERE tdate = '"+day+"' and (fill_num > 10 or selfshow_num > 5)"+
					"GROUP BY appid,adsid"; 
			List<Map<String, Object>> twoList = dnwxBiMapper.queryListMap(sql2);
			
			// 从广告源管理拉取open_type使用类型
			Map<String, ExtendAdsidVo> adsidMap = getAdsidManageMap();
			
			List<Map<String, Object>> collect = twoList.stream().map(act -> {
				
				Map<String, Object> act2 = agentMap.get(act.get("adsid")+"");
				if(act2 != null){
					act.put("agent", act2.get("agent")+"");
					act.put("request_count", act2.get("request_count")+"");
					act.put("return_count", act2.get("return_count")+"");
					act.put("pv", act2.get("pv")+"");
					act.put("click", act2.get("click")+"");
				}else{
					act.put("agent", "");
					act.put("request_count", "0");
					act.put("return_count", "0");
					act.put("pv", "0");
					act.put("click", "0");
				}
				
				ExtendAdsidVo act3 = adsidMap.get(act.get("adsid")+"");
				if(act3 != null){
					act.put("adpos_type", act3.getOpen_type());
					act.put("sdk_adtype", act3.getSdk_adtype());
				}else{
					act.put("adpos_type", "");
					act.put("sdk_adtype", "");
				}
				
				return act;
			}).filter(Objects::nonNull).collect(Collectors.toList());
			
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sql1", "insert into dnwx_bi.dn_show_gap(tdate,appid,adpos_type,adsid,agent,request_count,return_count,pv,click,req_num,fill_num,selfshow_num,click_num) values ");
			paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.adpos_type},#{li.adsid},#{li.agent},#{li.request_count},#{li.return_count},#{li.pv},#{li.click},#{li.req_num},#{li.fill_num},#{li.selfshow_num},#{li.click_num}) ");
			paramMap.put("sql3", " ");
			paramMap.put("list", collect);
			dnwxBiMapper.batchExecSql(paramMap);
			
			logger.info("同步syncShowGapTotal 完成...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncShowGapTotal 异常...");
		}
		return false;
	}
	
//	@Async("aaaScheduler")
	@Override
	public void syncIncomeDataTwo(String day, String... appid) {
		logger.info(" 异步执行开始..."+Thread.currentThread().getName());
		
		/** 支持按照产品来同步数据 */
		String where = "";
		if(appid != null && appid.length > 0){
			where = " and appid in ("+String.join(",",appid)+") ";
		}
		
		String del = "delete FROM dnwx_bi.dn_extend_income_data_two where tdate = '"+day+"' "+where;
		dnwxBiMapper.execSql(del);
		
		// 获取广告统计数据，过滤后录入
		String sql1 = "SELECT '"+day+"' tdate,appid,cha_id,IFNULL(prjid,'0') prjid,adsid,user_label user_group,act_num,add_num, "+
				"SUM(fail_num)+SUM(fill_num) req_num, "+
				"SUM(fill_num) fill_num, "+
				"SUM(selfshow_num) show_num, "+
				"SUM(click_num) click_num "+
				"FROM dnwx_bi.ads_admsg_v4_total_daily where tdate = '"+day+"' and user_label not like '%𤋂%' "+where+
				"GROUP BY appid,cha_id,prjid,adsid,user_label";
		System.out.println("sql =="+sql1);
		List<Map<String, Object>> sinkList = dnwxBiMapper.queryListMap(sql1);
		
		// 从变现平台明细拉取ecpm、agent等信息
		Map<String, DnAdsidCashTotalVo> ecpmMap = getAdsidCashTotal(day);
		// 从广告源管理拉取open_type使用类型
		Map<String, ExtendAdsidVo> adsidMap = getAdsidManageMap();
		// 从子渠道配置拉取 cha_type_name、cha_media
		Map<String, DnChaRevenueTotal> chaidMap = getChaidManageMap();
		
		BigDecimal thousand = new BigDecimal("1000");
		
		// 匹配到对应adsid则赋值ecpm，否则返回空
		List<Map<String, Object>> collect = sinkList.stream()
				.filter(act -> act.get("adsid") != null && !(act.get("adsid")+"").contains("{"))
				.map(act -> {
					
					DnChaRevenueTotal act1 = chaidMap.get(act.get("cha_id")+"");
					if(act1 != null){
						act.put("cha_type_name", act1.getCha_type_name());
						act.put("cha_media", act1.getCha_media());
					}
					
					DnAdsidCashTotalVo act2 = ecpmMap.get(act.get("adsid")+"");
					if(act2 != null){
						act.put("ecpm", act2.getEcpm());
						act.put("agent", act2.getAgent());
						
						if(act2.getEcpm() != null && act.get("show_num") != null){
							// 计算收入金额
							BigDecimal multiply = new BigDecimal(act2.getEcpm()).multiply(new BigDecimal(act.get("show_num")+""));
							String income = multiply.divide(thousand,2,RoundingMode.DOWN).toString();
							act.put("income", income);
						}
					}
					
					ExtendAdsidVo act3 = adsidMap.get(act.get("adsid")+"");
					if(act3 != null){
						act.put("adpos_type", act3.getOpen_type());
						act.put("sdk_adtype", act3.getSdk_adtype());
					}else{
						act.put("adpos_type", "");
						act.put("sdk_adtype", "");
					}
					
					return act;
				})
				.collect(Collectors.toList());
		
		// 入库
		if(collect != null && collect.size() > 0){
			try {
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("sql1", "replace into dnwx_bi.dn_extend_income_data_two(tdate,appid,cha_id,prjid,adsid,adpos_type,user_group,sdk_adtype,agent,ecpm,income,req_num,fill_num,show_num,click_num,act_num,add_num,cha_type_name,cha_media) values ");
				paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.cha_id},#{li.prjid},#{li.adsid},#{li.adpos_type},#{li.user_group},#{li.sdk_adtype},#{li.agent},#{li.ecpm},#{li.income},#{li.req_num},#{li.fill_num},#{li.show_num},#{li.click_num},#{li.act_num},#{li.add_num},#{li.cha_type_name},#{li.cha_media}) ");
				paramMap.put("sql3", " ");
				paramMap.put("list", collect);
				dnwxBiMapper.batchExecSql(paramMap);
				
				// 更新新增、活跃数据
//				String sql = "SELECT tdate,projectid,chaid,IFNULL(SUM(act_num),0) act_num,IFNULL(SUM(add_num),0) add_num "+
//						"FROM product_chaid_total where tdate = '"+day+"' "+
//						"GROUP BY projectid,chaid";
//				List<ApkProductStatsVo> proList = queryListBean(sql, ApkProductStatsVo.class);
//				
//				Map<String, Object> paramMap2 = new HashMap<String, Object>();
//				paramMap2.put("sql1", "update dnwx_bi.dn_extend_income_data_two set act_num=#{li.act_num}, add_num=#{li.add_num} where tdate=#{li.tdate} and prjid=#{li.projectid} and cha_id=#{li.chaid} ");
//				paramMap2.put("list", proList);
//				adMapper.batchExecSqlTwo(paramMap2);
				
				System.out.println("同步变现收入预估数据 完成...");
			} catch (Exception e) {
				e.printStackTrace();
				System.out.println("同步变现收入预估数据 异常...");
			}
		}
		logger.info(" 异步执行结束..."+Thread.currentThread().getName());
	}
	
	
	@Override
	public boolean syncExtendIncomeRevise(String day, String... appid) {
		
		logger.info("同步syncExtendIncomeRevise 开始...");
		try {
			
			/** 支持按照产品来同步数据 */
			String where = "";
			if(appid != null && appid.length > 0){
				where = " and appid in ("+String.join(",",appid)+") ";
			}
			
			String del = "delete FROM dnwx_bi.dn_extend_revise_income where tdate = '"+day+"' "+where;
			dnwxBiMapper.execSql(del);
			
			// 获取广告统计数据，过滤后录入
			String sql1 = "SELECT * FROM dnwx_bi.dn_extend_income_data_two where tdate = '"+day+"' "+where;
			List<Map<String, Object>> oneList = dnwxBiMapper.queryListMap(sql1);
			
			// 分别查询出变现平台明细数据和gap数据，通过广告源关联起来，合并数值
			String sql2 = "SELECT adsid mapkey,IFNULL(TRUNCATE((sum(selfshow_num)-sum(pv))*100/sum(selfshow_num), 4),0) gap_val "+
					"FROM dnwx_bi.dn_show_gap WHERE tdate = '"+day+"' "+
					"GROUP BY adsid";
			Map<String, Map<String, Object>> gapMap = dnwxBiMapper.queryListMapOfKey(sql2);
			
			// 通过指定字段进行关联匹配然后赋值，首先设置默认值让其不为null，否则影响sql计算结果
			DecimalFormat df = new DecimalFormat("#0.00");
			oneList.forEach(sink -> sink.put("gap_val", 0));
			
			List<Map<String, Object>> collect = oneList.stream()
			.map(sink -> {
				if(sink.get("show_num") == null){
					sink.put("show_num", 0);
				}
				if(sink.get("ecpm") == null){
					sink.put("ecpm", 0);
				}
				
				Map<String, Object> gap = gapMap.get(sink.get("adsid")+"");
				if(gap != null){
					sink.put("gap_val", gap.get("gap_val")+"");
					BigDecimal ecpm = new BigDecimal(sink.get("ecpm")+"");
					BigDecimal show_num = new BigDecimal(sink.get("show_num")+"");
					BigDecimal rate = new BigDecimal("1").subtract(new BigDecimal(gap.get("gap_val")+"").multiply(new BigDecimal("0.01")));
					
					long revise_show = show_num.multiply(rate).longValue();
					sink.put("revise_show", revise_show);
					
					String revise_revenue = df.format(ecpm.multiply(new BigDecimal(revise_show)).divide(new BigDecimal("1000")).doubleValue());
					sink.put("revise_revenue", revise_revenue);
				}
				
				
				return sink;
			})
			.filter(Objects::nonNull).collect(Collectors.toList());
			
			/*Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sql1", "replace into dnwx_bi.dn_extend_revise_income(tdate,appid,cha_id,prjid,adsid,adpos_type,user_group,sdk_adtype,ecpm,self_show,gap_val,actnum,addnum,revise_show,revise_revenue,cha_type_name,cha_media) values ");
			paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.cha_id},#{li.prjid},#{li.adsid},#{li.adpos_type},#{li.user_group},#{li.sdk_adtype},#{li.ecpm},#{li.show_num},#{li.gap_val},#{li.act_num},#{li.add_num},ROUND(#{li.show_num}*(1-#{li.gap_val}*0.01),0), Concat(ROUND(#{li.ecpm}*(#{li.show_num}*(1-#{li.gap_val}*0.01))/1000, 2),''),#{li.cha_type_name},#{li.cha_media}) ");
			paramMap.put("sql3", " ");
			paramMap.put("list", collect);
			dnwxBiMapper.batchExecSql(paramMap);*/
			
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sql1", "replace into dnwx_bi.dn_extend_revise_income(tdate,appid,cha_id,prjid,adsid,adpos_type,user_group,sdk_adtype,ecpm,self_show,gap_val,actnum,addnum,revise_show,revise_revenue,cha_type_name,cha_media) values ");
			paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.cha_id},#{li.prjid},#{li.adsid},#{li.adpos_type},#{li.user_group},#{li.sdk_adtype},#{li.ecpm},#{li.show_num},#{li.gap_val},#{li.act_num},#{li.add_num},#{li.revise_show},#{li.revise_revenue},#{li.cha_type_name},#{li.cha_media}) ");
			paramMap.put("sql3", " ");
			paramMap.put("list", collect);
			dnwxBiMapper.batchExecSql(paramMap);
			
			logger.info("同步syncExtendIncomeRevise 完成...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncExtendIncomeRevise 异常...");
		}
		return false;
	}
	
	@Override
	public boolean syncExtendSubchaTotal(String day) {
		
		logger.info("同步syncExtendSubchaTotal 开始...");
		try {
			String del = "delete FROM dnwx_bi.dn_extend_subcha_total where tdate = '"+day+"'";
			String del2 = "delete FROM dnwx_bi.dn_extend_subcha_income where tdate = '"+day+"'";
			adMapper.execSql(del);
			adMapper.execSql(del2);
			
			// 获取自统计的二级子渠道级展示数据
			String sql1 = "select xx.appid,xx.chaid,xx.subcha,xx.adsid,xx.adpos_type,xx.num self_show,IFNULL(yy.new_users,0) addnum,IFNULL(yy.act_users,0) actnum "+
					"from dnwx_bi.ads_admsg_subcha_selfshow_daily xx "+
					"LEFT JOIN dnwx_bi.ads_dim_users_info_5d_hourly yy "+
					"ON xx.tdate=yy.tdate and xx.appid=yy.appid and xx.chaid=yy.download_channel and xx.subcha=yy.subcha "+
					"where xx.tdate='"+day+"' ";
			List<Map<String, String>> oneList = dnwxBiMapper.queryListMapOne(sql1);
			
			// 获取广告源对应的ecpm、子渠道等信息
			Map<String, DnAdsidCashTotalVo> ecpmMap = null;
			if(day.equals(DateTime.now().toString("yyyy-MM-dd")))
				ecpmMap = getAdsidCashTotal(DateTime.now().minusDays(1).toString("yyyy-MM-dd"));
			else
				ecpmMap = getAdsidCashTotal(day);
			
			for (Map<String, String> act : oneList) {
				DnAdsidCashTotalVo total = ecpmMap.get(act.get("adsid"));
				if(total != null){
					act.put("ecpm", total.getEcpm());
					act.put("agent", total.getAgent());
					act.put("cha_type_name", total.getCha_type_name());
					act.put("cha_media", total.getCha_media());
				}
			}
			
			// 写入到二级子渠道展示收入表
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sql1", "replace into dnwx_bi.dn_extend_subcha_income(tdate,appid,cha_id,sub_cha,adsid,adpos_type,agent,ecpm,self_show,revenue,actnum,addnum) values ");
			paramMap.put("sql2", " ('"+day+"',#{li.appid},#{li.chaid},#{li.subcha},#{li.adsid},#{li.adpos_type},#{li.agent},#{li.ecpm},#{li.self_show},(TRUNCATE(#{li.ecpm}*#{li.self_show}/1000,2)),#{li.actnum},#{li.addnum}) ");
			paramMap.put("sql3", " ");
			paramMap.put("list", oneList);
			adMapper.batchExecSql(paramMap);
			
			// 从二级子渠道展示收入表 dn_extend_subcha_income 抽取展示和收入
			bigMapper.insertExtendSubchaTotal(day);
			
			/*
			// 更新表的新增、活跃
			Map<String, Object> paMap = new HashMap<>();
			
			List<String> array = new ArrayList<String>();
			for (int i = 0; i < 50; i++) {
				String code = "" + i;
				if(i < 10){ code = "0" + i; }
				array.add(code);
			}
			paMap.put("array", array);
			paMap.put("today", day);
			bigMapper.insertPushSubChaActUser(paMap);
			bigMapper.insertPushSubChaAddUser(paMap);*/
			
			
			// 平台回传的收入/pv 数据除以二级子渠道的dau平均分配到各个二级子渠道
			List<Map<String, Object>> twoList = bigMapper.selectSubchaTotalOfBanner(day);
			if(twoList != null && !twoList.isEmpty()){
				Map<String, Object> paramMap2 = new HashMap<String, Object>();
				paramMap2.put("sql1", "update dnwx_bi.dn_extend_subcha_total set pv_banner=#{li.pv},revenue_banner=#{li.income},sum_revenue=(sum_revenue+#{li.income}) where tdate=#{li.tdate} and appid=#{li.appid} and cha_id=#{li.cha_id} and sub_cha=#{li.sub_cha} ");
				paramMap2.put("list", twoList);
				adMapper.batchExecSqlTwo(paramMap2);
			}
			
			logger.info("同步syncExtendSubchaTotal 完成...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncExtendSubchaTotal 异常...");
		}
		return false;
	}
	@Override
	public boolean syncExtendAdtypeRevise(String day) {
		
		logger.info("同步syncExtendAdtypeRevise 开始...");
		try {
			String del = "delete FROM dnwx_bi.dn_extend_revise_adtype where tdate = '"+day+"'";
			dnwxBiMapper.execSql(del);
			dnwxBiMapper.insertExtendAdtypeRevise(day);
			
//			adMapper.execSql(del);
			// 从变现收入校准 抽取校准后展示和收入
//			bigMapper.insertExtendAdtypeRevise(day);
			
			
			/*String sql = "SELECT * FROM umeng_user_channel_total WHERE tdate = '"+day+"'";
			List<ApkProductStatsVo> proList = queryListBean(sql, ApkProductStatsVo.class);
			
			Map<String, Object> paramMap2 = new HashMap<String, Object>();
			paramMap2.put("sql1", "update dnwx_bi.dn_extend_revise_adtype set actnum=#{li.act_num},addnum=#{li.add_num} where tdate=#{li.tdate} and appid=#{li.appid} and cha_id=#{li.install_channel} ");
			paramMap2.put("list", proList);
			adMapper.batchExecSqlTwo(paramMap2);*/
			
			logger.info("同步syncExtendAdtypeRevise 完成...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncExtendAdtypeRevise 异常...");
		}
		return false;
	}
	@Override
	public boolean syncExtendAdtypeReviseTwo(String day) {
		
		logger.info("同步syncExtendAdtypeReviseTwo 开始...");
		try {
			String del = "delete FROM dnwx_bi.dn_extend_revise_adtype_two where tdate = '"+day+"'";
			dnwxBiMapper.execSql(del);
			
			dnwxBiMapper.insertExtendAdtypeReviseTwo(day);
			
//			adMapper.execSql(del);
			// 从变现收入校准 抽取校准后展示和收入
//			bigMapper.insertExtendAdtypeReviseTwo(day);
			
			
			/*String sql = "SELECT tdate,appid,download_channel cha_id,new_users add_num,act_users act_num "+
					"FROM dnwx_bi.ads_dim_users_info_3d_hourly where tdate='"+day+"' ";
			List<Map<String, Object>> proList = dnwxBiMapper.queryListMap(sql);
			
			// 抽取大数据新增活跃数据
			String sql = "SELECT tdate,appid,cha_id,SUM(act_num) act_num,SUM(add_num) add_num "+
					"FROM dnwx_bi.ads_admsg_v4_total_daily where tdate='"+day+"' "+
					"GROUP BY appid,cha_id";
			List<Map<String, Object>> proList = dnwxBiMapper.queryListMap(sql);
			
			if(proList != null && !proList.isEmpty()){
				Map<String, Object> paramMap2 = new HashMap<String, Object>();
				paramMap2.put("sql1", "update dnwx_bi.dn_extend_revise_adtype_two set actnum=#{li.act_num},addnum=#{li.add_num} where tdate=#{li.tdate} and appid=#{li.appid} and cha_id=#{li.cha_id} ");
				paramMap2.put("list", proList);
				adMapper.batchExecSqlTwo(paramMap2);
			}*/
			
			logger.info("同步syncExtendAdtypeReviseTwo 完成...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncExtendAdtypeReviseTwo 异常...");
		}
		return false;
	}
	
	/*@Override
	public boolean syncExtendAdtypeGroup(String day) {
		
		logger.info("同步syncExtendAdtypeGroup 开始...");
		try {
			String del = "delete FROM dnwx_bi.dn_extend_group_adtype where tdate = '"+day+"'";
			adMapper.execSql(del);
			
			// 从变现收入校准 抽取校准后展示和收入
			bigMapper.insertExtendAdtypeGroup(day);
			
			// 分别赋值 次日留存、3日留存、7日留存
			int[] dates = {1,3,7}; 
			for (int i = 0; i < dates.length; i++) {
				int daynum = dates[i];
				String keep = "keep"+dates[i];
				String date = DateTime.parse(day).minusDays(daynum).toString("yyyy-MM-dd");
				
				String query = "SELECT tdate,appid,"+keep+" FROM `umeng_user_app_keep_three` where tdate='"+date+"'";
				List<Map<String, Object>> proList = adMapper.queryListMap(query);
				
				Map<String, Object> paramMap2 = new HashMap<String, Object>();
				paramMap2.put("sql1", "update dnwx_bi.dn_extend_group_adtype set "+keep+"=#{li."+keep+"} where tdate=#{li.tdate} and appid=#{li.appid} ");
				paramMap2.put("list", proList);
				adMapper.batchExecSqlTwo(paramMap2);
			}
			
			logger.info("同步syncExtendAdtypeGroup 完成...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncExtendAdtypeGroup 异常...");
		}
		return false;
	}*/
	
	@Override
	public boolean syncExtendAdtypeGroup(String day) {
		
		logger.info("同步syncExtendAdtypeGroup 开始...");
		try {
			String del = "delete FROM dnwx_bi.dn_extend_group_adtype where tdate = '"+day+"'";
			dnwxBiMapper.execSql(del);
			dnwxBiMapper.insertExtendAdtypeGroup(day);
			
			
			// 分别赋值 次日留存、3日留存、7日留存
			int[] dates = {1,3,7};
			for (int i = 0; i < dates.length; i++) {
				int daynum = dates[i];
				String keep = "keep"+dates[i];
				String date = DateTime.parse(day).minusDays(daynum).toString("yyyy-MM-dd");
				
				// 只拉取当天的appid列表，避免覆盖写入
				String query = "SELECT appid FROM dnwx_bi.dn_extend_group_adtype WHERE tdate='"+date+"' ";
				List<String> keyList = dnwxBiMapper.queryListString(query);
				
				String query2 = "SELECT tdate,appid,"+keep+" FROM `umeng_user_app_keep_three` where tdate='"+date+"'and appid in ("+String.join(",", keyList)+")";
				List<Map<String, Object>> proList = adMapper.queryListMap(query2);
				
				proList.forEach(act -> {
					String sql = "update dnwx_bi.dn_extend_group_adtype set "+keep+"=#{obj."+keep+"} where tdate=#{obj.tdate} and appid=#{obj.appid} ";
					dnwxBiMapper.execSqlHandle(sql, act);
				});
			}
			
			logger.info("同步syncExtendAdtypeGroup 完成...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncExtendAdtypeGroup 异常...");
		}
		return false;
	}
	
	@Override
	public boolean syncExtendPrjidIncome(String day) {
		
		logger.info("同步syncExtendPrjidIncome 开始...");
		try {
			String del = "delete FROM dnwx_bi.dn_extend_prjid_income where tdate = '"+day+"'";
			dnwxBiMapper.execSql(del);
			
			// 从变现收入校准 抽取展示、收入和新增活跃
			dnwxBiMapper.insertExtendPrjidIncome(day);
			
			logger.info("同步syncExtendPrjidIncome 完成...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncExtendPrjidIncome 异常...");
		}
		return false;
	}
	
	/**
	 * 转换特殊广告位类型为 普通类型
	 * @param name
	 * @return
	 */
	public static String converTypeName(String name){
		Map<String, String> typeMap = new HashMap<>();
		typeMap.put("natBanner", "banner");
		typeMap.put("nativeBanner", "banner");
		typeMap.put("plaqueVideo", "plaque");
		typeMap.put("natPlaque", "plaque");
		typeMap.put("nativePlaque", "plaque");
		typeMap.put("natSplash", "splash");
		typeMap.put("natVideo", "video");
		typeMap.put("yuans", "msg");
		
		// 转换type类型存储
		String adtype = typeMap.get(name);
		if(adtype != null)
			return adtype;
		else
			return name;
	}
	
}
