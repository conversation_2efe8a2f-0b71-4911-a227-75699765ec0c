package com.wbgame.service.impl;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.GetObjectRequest;
import com.wbgame.mapper.adb.ADBOrderMapper;
import com.wbgame.mapper.adb.DnwxBiAdtMapper;
import com.wbgame.mapper.adb.DnwxBiMapper;
import com.wbgame.mapper.adb.UmengMonitorMapper;
import com.wbgame.mapper.adv2.Adv2Mapper;
import com.wbgame.mapper.haiwaiad.HwWbPayInfoMapper;
import com.wbgame.mapper.master.*;
import com.wbgame.mapper.slave2.AdMsgMapper;
import com.wbgame.mapper.yxcgame.YxcAdMapper;
import com.wbgame.pojo.*;
import com.wbgame.pojo.adv2.DnAdsidCashTotalVo;
import com.wbgame.pojo.adv2.DnChaRevenueTotal;
import com.wbgame.pojo.advert.CashUnmatchParam;
import com.wbgame.pojo.advert.CashUnmatchVo;
import com.wbgame.pojo.advert.DnwxReyunConfigVo;
import com.wbgame.pojo.custom.AdvFeeVo;
import com.wbgame.pojo.custom.*;
import com.wbgame.pojo.push.ApkProductKeepVo;
import com.wbgame.pojo.push.ApkProductStatsVo;
import com.wbgame.service.AdService;
import com.wbgame.service.xyx.XyxService;
import com.wbgame.utils.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

@Service("adService")
public class AdServiceImpl implements AdService {

	Logger logger = LoggerFactory.getLogger(AdServiceImpl.class);
	
	@Autowired
	private SysconfigMapper sysconfigMapper;
	@Autowired
	private AdMapper adMapper;
	@Autowired
	private DnwxBiMapper dnwxBiMapper;
	@Autowired
	private ApkMapper apkMapper;
	@Autowired
	private AdMsgMapper admsgMapper;
	@Autowired
	private SomeMapper someMapper;
	@Autowired
	private YyhzMapper yyhzMapper;
	@Autowired
	private XyxService xyxService;
	@Autowired
	private YxcAdMapper yxcAdMapper;
	@Autowired
	private DnwxBiAdtMapper dnwxBiAdtMapper;
	@Autowired
	private YdMapper ydMapper;
	@Resource
	private ADBOrderMapper adbOrderMapper;
	@Resource
	private Adv2Mapper adv2Mapper;

	@Autowired
	private UmengMonitorMapper umengMonitorMapper;
	@Autowired
	private HwWbPayInfoMapper hwWbPayInfoMapper;

	@Override
	public int insertTouTiaoAdTotal(List<TouTiaoAdVo> list) {
		// 根据code获取到对应的name
		Map<String, Map<String, String>> codeMap = sysconfigMapper.selectAdCodeConfig();
		for (TouTiaoAdVo ta : list) {
			Map<String, String> amap = codeMap.get(ta.getAppid());
			Map<String, String> bmap = codeMap.get(ta.getAd_slot_id());
			if(amap != null){
				ta.setApp_name(amap.get("ad_name"));
			}
			if(bmap != null){
				ta.setSlot_name(bmap.get("ad_name"));
			}
		}
		
		return adMapper.insertTouTiaoAdTotal(list);
	}

	
	public List<AdvFeeVo> browsAdv(String sql) {
		return adMapper.browsAdv(sql);
	}
	public List<AdvFeeVo> browsAdv(Map<String, String> parmMap) {
		
		String begin = parmMap.get("beginDt");
		String end = parmMap.get("endDt");
		String pri = parmMap.get("pri");
		String mmid = parmMap.get("mmid");
		String channel = parmMap.get("channel");
		String type = parmMap.get("type");
		String groupByStr = parmMap.get("groupByStr");
		String chaType = parmMap.get("chaType");
		
		StringBuffer sb = new StringBuffer();
		StringBuffer os = new StringBuffer();
		if(!StringUtils.isEmpty(pri)){
			sb.append(" and b.pri_id = "+pri);
		}
		if(!StringUtils.isEmpty(mmid)){
			sb.append(" and b.mmid = "+mmid);
		}
		if(!StringUtils.isEmpty(channel)){
			sb.append(" and b.push_cha like '%"+channel+"%'");
		}
		if(!StringUtils.isEmpty(type)){
			if("2".equals(type)){//海外版查询界面
				sb.append(" and b.adv_type = '"+type+"'");
			}else{
				if(!"0".equals(type)){
					sb.append(" and b.os_type = '"+type+"'");
					os.append(" and a.os_type = '"+type+"'");
				}
				/*else{
					sb.append(" and (b.adv_type = 1 or b.adv_type = 3 or b.adv_type = 4 or b.adv_type = 5 or b.adv_type = 6)");
				}*/
				//国内版本查询 2019.3.4 yangk
				sb.append(" and b.adv_type != '2'");
				//end
			}
		}
		if(!StringUtils.isEmpty(chaType)){
			sb.append(" and b.cha_type = "+chaType);
		}
		
		if(groupByStr!=null && "cha".equals(groupByStr)){
			
			String sql = "select b.push_cha,b.cha_type,"
					       +"b.adv_dt,"
					       +"b.give_fee,"
					       +"b.new_count,"
					       +"b.cpa,"
					       +"b.two_retention,"
					       +"b.dau,"
					       +"b.get_fee,"
					       +"b.get_fee_expect,"
					       +"b.arpu,"
					       +"b.adv_fee,"
					       +"b.adv_fee_expect,"
					       +"b.adv_dau,"
					       +"b.show_count,"
					       +"b.show_dau,"
					       +"b.click_count,"
					       +"b.click_rate,"
					       +"b.ecpm,"
					       +"b.cpc,"
					       +"aa.give_fee_total,"
					       +"aa.get_fee_total,"
					       +"aa.adv_total,"
					        +"round(aa.get_fee_total+aa.adv_total,2) total_fee,"
					        +"(CASE WHEN aa.give_fee_total=0 THEN 0 ELSE round((aa.get_fee_total+aa.adv_total)/aa.give_fee_total*100,2) END) input_output_ratio"
					        +" from (select a.push_cha,a.adv_type,a.cha_type,"
					       +"a.adv_dt,"
					       +"sum(a.give_fee) give_fee,"
					       +"sum(a.new_count) new_count,"
					       +"(CASE WHEN sum(a.new_count)=0 THEN 0 ELSE round(sum(a.give_fee)/sum(a.new_count),2) END) cpa,"
					       +"max(a.two_retention) two_retention,"
					       +"sum(a.dau) dau,"
					       +"sum(a.get_fee) get_fee,";
					       if("2".equals(type)){//1:国内0.6;2:海外;3:0.7;4:1;5:0.475;6:0.25
					    	   sql = sql+"(CASE WHEN a.adv_type=4 THEN round(sum(a.get_fee),2) ELSE round(sum(a.get_fee)*0.7,2) END) get_fee_expect,";
					       }else{
					    	   sql = sql+"(CASE WHEN a.adv_type=3 THEN round(sum(a.get_fee)*0.7,2) WHEN a.adv_type=1 THEN round(sum(a.get_fee)*0.6,2) WHEN a.adv_type=5 THEN round(sum(a.get_fee)*0.475,2) WHEN a.adv_type=6 THEN round(sum(a.get_fee)*0.25,2) WHEN a.adv_type=4 THEN round(sum(a.get_fee),2) END) get_fee_expect,";
					       }
					       sql = sql
					       +"(CASE WHEN sum(a.new_count)=0 THEN 0 ELSE round(sum(a.get_fee)/sum(a.new_count),2) END) arpu,"
					       +"sum(a.adv_fee) adv_fee,"
					       +"(CASE WHEN a.adv_fee_type=2 THEN round(sum(a.adv_fee),2) WHEN a.adv_fee_type=1 THEN round(sum(a.adv_fee)*0.7,2) END) adv_fee_expect,"
					       +"(CASE WHEN sum(a.dau)=0 THEN 0 ELSE round(sum(a.adv_fee)/sum(a.dau),2) END) adv_dau,"
					       +"sum(a.show_count) show_count,"
					       +"(CASE WHEN sum(a.dau)=0 THEN 0 ELSE round(sum(a.show_count)/sum(a.dau),2) END) show_dau,"
					       +"sum(a.click_count) click_count,"
					       +"(CASE WHEN sum(a.show_count)=0 THEN 0 ELSE round(sum(a.click_count)/sum(a.show_count)*100,2) END) click_rate,"
					       +"(CASE WHEN sum(a.show_count)=0 THEN 0 ELSE round(sum(a.adv_fee)*1000/sum(a.show_count),2) END) ecpm,"
					       +"(CASE WHEN sum(a.click_count)=0 THEN 0 ELSE round(sum(a.adv_fee)/sum(a.click_count),2) END)cpc"
					       +" from ADV_CHANNEL_FEE a where adv_dt BETWEEN '"+begin+"' AND '"+end+"' "
					       +os.toString()
					       +" group by a.push_cha,a.adv_dt,a.adv_type,a.cha_type,a.adv_fee_type) b,(select b.push_cha,b.adv_type,";
					       if("2".equals(type)){//1:国内0.6;2:海外;3:0.7;4:1;5:0.475;6:0.25
					    	   sql = sql+"sum(b.give_fee) give_fee_total,(CASE WHEN b.adv_type=4 THEN round(sum(b.get_fee),2) ELSE round(sum(b.get_fee)*0.7,2) END) get_fee_total,(CASE WHEN b.adv_fee_type=2 THEN round(sum(b.adv_fee),2) WHEN b.adv_fee_type=1 THEN round(sum(b.adv_fee)*0.7,2) END) adv_total from ADV_CHANNEL_FEE b where 1=1";
					       }else{
					    	   sql = sql+"sum(b.give_fee) give_fee_total,(CASE WHEN b.adv_type=3 THEN round(sum(b.get_fee)*0.7,2) WHEN b.adv_type=1 THEN round(sum(b.get_fee)*0.6,2) WHEN b.adv_type=5 THEN round(sum(b.get_fee)*0.475,2) WHEN b.adv_type=6 THEN round(sum(b.get_fee)*0.25,2) WHEN b.adv_type=4 THEN round(sum(b.get_fee),2) END) get_fee_total,(CASE WHEN b.adv_fee_type=2 THEN round(sum(b.adv_fee),2) WHEN b.adv_fee_type=1 THEN round(sum(b.adv_fee)*0.7,2) END) adv_total from ADV_CHANNEL_FEE b where 1=1";
					       }
					       sql = sql+sb.toString()
					 +" group by b.push_cha,b.adv_type,b.adv_fee_type) aa"
					 +" where b.push_cha = aa.push_cha and b.adv_type = aa.adv_type";
				
				/*
				 * 解决跨天查询,输入mmid条件时sql异常问题. yangk 2019.4.26
				 * if(!StringUtils.isEmpty(mmid)){
					sql = sql + " and b.mmid = "+mmid;
				}*/
				if(!StringUtils.isEmpty(channel)){
					sql = sql + " and b.push_cha like '%"+channel+"%'";
				}
				if(!StringUtils.isEmpty(chaType)){
					sql = sql + " and b.cha_type = "+chaType;
				}
			sql = sql + " order by b.adv_dt,b.new_count desc";
			
			List<AdvFeeVo> list = adMapper.browsAdv(sql);
			return list;
		}else{
			String sql = "select a.push_cha,"
			       +"a.pri_id,"
			       +"a.adv_type,"
			       +"a.mmid,"
			       +"a.cha_type,"
			       +"a.adv_dt,"
			       +"round(a.give_fee,2) give_fee,"
			       +"a.new_count,"
			       +"(CASE WHEN a.new_count=0 THEN 0 ELSE round(a.give_fee/a.new_count,2) END) cpa,"
			       +"a.two_retention,"
			       +"round(a.dau,0) dau,"
			       +"a.get_fee,";
			       if("2".equals(type)){
			    	   sql = sql +"(CASE WHEN a.adv_type=4 THEN round(a.get_fee,2) ELSE round(a.get_fee*0.7,2) END) get_fee_expect,"; 
			       }else{
			    	   sql = sql+"(CASE WHEN a.adv_type=3 THEN round(a.get_fee*0.7,2) WHEN a.adv_type=1 THEN round(a.get_fee*0.6,2) WHEN a.adv_type=5 THEN round(a.get_fee*0.475,2) WHEN a.adv_type=6 THEN round(a.get_fee*0.25,2) WHEN a.adv_type=4 THEN round(a.get_fee,2) END) get_fee_expect,";
			       }
			       sql = sql
			       +"(CASE WHEN a.new_count=0 THEN 0 ELSE round(a.get_fee/a.new_count,2) END) arpu,"
			       +"a.adv_fee,"
			       +"(CASE WHEN a.adv_fee_type=2 THEN round(a.adv_fee,2) WHEN a.adv_fee_type=1 THEN round(a.adv_fee*0.7,2) END) adv_fee_expect,"
			       +"(CASE WHEN a.dau=0 THEN 0 ELSE round(a.adv_fee/a.dau,2) END) adv_dau,"
			       +"round(a.show_count,0) show_count,"
			       +"(CASE WHEN a.dau=0 THEN 0 ELSE round(a.show_count/a.dau,2) END) show_dau,"
			       +"round(a.click_count,0) click_count,"
			       +"(CASE WHEN a.show_count=0 THEN 0 ELSE round(a.click_count/a.show_count*100,2) END) click_rate,"
			       +"(CASE WHEN a.show_count=0 THEN 0 ELSE round(a.adv_fee*1000/a.show_count,2) END) ecpm,"
			       +"(CASE WHEN a.click_count=0 THEN 0 ELSE round(a.adv_fee/a.click_count,2) END)cpc,"
			       +"aa.give_fee_total,"
			       +"aa.get_fee_total,"
			       +"aa.adv_total,"
			       +"round(aa.get_fee_total+aa.adv_total,2) total_fee,"
			       +"(CASE WHEN aa.give_fee_total=0 THEN 0 ELSE round((aa.get_fee_total+aa.adv_total)/aa.give_fee_total*100,2) END) input_output_ratio"
			       +" from ADV_CHANNEL_FEE a,(select b.push_cha,b.adv_type,";
			       if("1".equals(type)){
			    	   sql = sql +"sum(b.give_fee) give_fee_total,(CASE WHEN b.adv_type=3 THEN round(sum(b.get_fee)*0.7,2) WHEN b.adv_type=1 THEN round(sum(b.get_fee)*0.6,2) WHEN b.adv_type=5 THEN round(sum(b.get_fee)*0.475,2) WHEN b.adv_type=6 THEN round(sum(b.get_fee)*0.25,2) WHEN b.adv_type=4 THEN round(sum(b.get_fee),2) END) get_fee_total,(CASE WHEN b.adv_fee_type=2 THEN round(sum(b.adv_fee),2) WHEN b.adv_fee_type=1 THEN round(sum(b.adv_fee)*0.7,2) END) adv_total from ADV_CHANNEL_FEE b where 1=1";
			       }else{
			    	   sql = sql +"sum(b.give_fee) give_fee_total,(CASE WHEN b.adv_type=4 THEN round(sum(b.get_fee),2) ELSE round(sum(b.get_fee)*0.7,2) END) get_fee_total,(CASE WHEN b.adv_fee_type=2 THEN round(sum(b.adv_fee),2) WHEN b.adv_fee_type=1 THEN round(sum(b.adv_fee)*0.7,2) END) adv_total from ADV_CHANNEL_FEE b where 1=1";
				      
			       }
			       sql = sql+sb.toString()
				   +" group by b.push_cha,b.adv_type,b.adv_fee_type) aa"
				   +" where a.push_cha = aa.push_cha and a.adv_type = aa.adv_type";
				
				sql = sql + " and a.adv_dt >= '"+begin+"' and a.adv_dt <= '"+end+"'";
				
				if(!StringUtils.isEmpty(pri)){
					sql = sql + " and a.pri_id = "+pri;
				}
				if(!StringUtils.isEmpty(mmid)){
					sql = sql + " and a.mmid = "+mmid;
				}
				if(!StringUtils.isEmpty(channel)){
					sql = sql + " and a.push_cha like '%"+channel+"%'";
				}
				if(!StringUtils.isEmpty(chaType)){
					sql = sql + " and a.cha_type = "+chaType;
				}
				if(!StringUtils.isEmpty(type)
						&& !"2".equals(type)
						&& !"0".equals(type)){
					sql = sql + " and a.os_type = '"+type+"'";
				}
				sql = sql + " order by a.adv_dt,a.new_count desc";
				
				List<AdvFeeVo> list = adMapper.browsAdv(sql);
				return list;
		}
		
	}

	@Override
	public List<AdvFeeVo> getNCount(String begin,String end) {
		String sql = "select by_priid pri_id,by_mmid mmid,by_date adv_dt,by_newcount n_count,push_newcount new_count from buyu_newcount_total where by_date >= '"+begin+"' and by_date <= '"+end+"' ";
		
		List<AdvFeeVo> list = adMapper.browsAdv(sql);
		return list;
	}
	@Override
	public int updateAdvFee(AdvFeeVo afv) {
		
		// String sqlStr = "update adv_channel_fee set give_fee=:give_fee,get_fee=:get_fee,adv_fee=:adv_fee,dau=:dau,new_count=:new_count where adv_dt=:adv_dt and push_cha=:push_cha and pri_id=:pri_id and cha_type=:cha_type";
		return adMapper.updateAdvFee(afv);
	}
	public int delAdvFee(Map<String, String> parmMap) {
		String dateStart = parmMap.get("beginDt");
		String dateEnd = parmMap.get("endDt");
		String cha_type = parmMap.get("cha_type");
		//2019.1.21 yangk 修改广告数据删除时删除全部
		String sql = "delete from adv_channel_fee where adv_dt >= '"+dateStart+"' and adv_dt <= '"+dateEnd+"' and (adv_type = 1 or adv_type = 3 or adv_type = 4 or adv_type = 5 or adv_type = 6)";
		if(!StringUtils.isEmpty(cha_type)){
			sql = "delete from adv_channel_fee where adv_dt >= '"+dateStart+"' and adv_dt <= '"+dateEnd+"' and cha_type = '"+cha_type+"' and (adv_type = 1 or adv_type = 3 or adv_type = 4 or adv_type = 5 or adv_type = 6)";
		}

		return adMapper.execSql(sql);
	}
	
	@Override
	public List<JdAppPayVo> browsAdTotal(Map<String, String> parmMap) {
		
		String dauDt = parmMap.get("dauDt");
		String prjId = parmMap.get("prjid");
		String adId = parmMap.get("adid");
		String endDt = parmMap.get("endDt");
		String sidGp = parmMap.get("sidGp");
		
		String sql;
		if(sidGp!=null && "sid".equals(sidGp)){
			sql = "select sid contentid,clickdate consumecode,advtype fee,sum(num) txt_info,act_num from ad_total_info "
					+"where  clickdate >= '"+dauDt+"'"
					+"and clickdate <= '"+endDt+"'";
		}else{
			if((prjId != null && !"".equals(prjId)
					||
					(adId != null && !"".equals(adId)))){
				sql = "select sid contentid,clickdate consumecode,advtype fee,sum(num) txt_info,prjid,act_num from ad_total_info "
					+"where  clickdate >= '"+dauDt+"'"
					+"and clickdate <= '"+endDt+"'";
			}else{
				sql = "select sid contentid,clickdate consumecode,advtype fee,sum(num) txt_info,act_num from ad_total_info "
					+"where  clickdate >= '"+dauDt+"'"
					+"and clickdate <= '"+endDt+"'";
			}
		}

		if(prjId != null && !"".equals(prjId)){
			sql  = sql + " and prjid = '" + prjId + "'";
		}
		if(adId != null && !"".equals(adId)){
			sql  = sql + " and sid like '%" + adId + "%'";
		}
		if(sidGp!=null && "sid".equals(sidGp)){
			sql = sql + " group by advtype,clickdate,sid";
		}else{
			if((prjId != null && !"".equals(prjId)
					||
					(adId != null && !"".equals(adId)))){
				sql = sql + " group by advtype,clickdate,sid,prjid";
			}else{
				sql = sql + " group by advtype,clickdate,sid";
			}
			
		}
		
		List<JdAppPayVo> convertList = new ArrayList<JdAppPayVo>();
		
		List<JdAppPayVo> list = adMapper.browsAdTotal(sql);
		
		Map<String,Map<Integer,Integer>> resMap = new HashMap<String, Map<Integer,Integer>>();
		
		String mkey = "";
		
		Map<Integer,Integer> temMap = null;
		Map<String,Integer> actMap = new HashMap<>();
		
		for (JdAppPayVo jdAppPayVo : list) {
			mkey = jdAppPayVo.getContentid()+"#"+jdAppPayVo.getConsumecode()+"#"+jdAppPayVo.getPrjid();
			if(jdAppPayVo.getFee()!=null && jdAppPayVo.getTxt_info()!=null 
					&& !"null".equals(jdAppPayVo.getTxt_info())
					&& !"null".equals(jdAppPayVo.getFee())){
				temMap = resMap.get(mkey);
				if(temMap!=null){
					temMap.put(jdAppPayVo.getFee(), jdAppPayVo.getTxt_info());
					resMap.put(mkey, temMap);
				}else{
					temMap = new HashMap<Integer, Integer>();
					temMap.put(jdAppPayVo.getFee(),jdAppPayVo.getTxt_info());
					resMap.put(mkey, temMap);
				}
			}
			actMap.put(mkey, jdAppPayVo.getAct_num());
		}
		
		Iterator<String> kset = resMap.keySet().iterator();
		
		String[] keyArr = null;
		Integer show = 0;
		Integer click = 0;
		Integer req = 0;
		Integer install = 0;
		Integer down = 0;
		DecimalFormat df = new DecimalFormat("#.##");
		String clickrate = "";
		String reqrate = "";
		
		while (kset.hasNext()) {
			mkey = kset.next();
			keyArr = mkey.split("#");
			JdAppPayVo jpv = new JdAppPayVo();
			jpv.setContentid(keyArr[0]);
			jpv.setConsumecode(keyArr[1]);
			if(prjId != null && !"".equals(prjId) || adId != null && !"".equals(adId)){
				jpv.setPrjid(keyArr[2]);
			}
			jpv.setAct_num(actMap.get(mkey)!=null?actMap.get(mkey):0);
			
			show = resMap.get(mkey).get(1)!=null? resMap.get(mkey).get(1):0;
			click = resMap.get(mkey).get(2)!=null?resMap.get(mkey).get(2):0;
			req = resMap.get(mkey).get(3)!=null?resMap.get(mkey).get(3):0;
			down = resMap.get(mkey).get(4)!=null?resMap.get(mkey).get(4):0;
			install = resMap.get(mkey).get(5)!=null?resMap.get(mkey).get(5):0;
			jpv.setFee(show);
			jpv.setTxt_info(click);
			jpv.setDownNum(down);
			jpv.setInstallNum(install);
			if(show!=null && show!=0){
				clickrate = df.format(Double.parseDouble(String.valueOf(click))/Double.parseDouble(String.valueOf(show))*100);
				jpv.setCilck_rate(clickrate+"%");
			}
			if(req!=null && req!=0){
				jpv.setReqNum(String.valueOf(req));
				reqrate =  df.format(Double.parseDouble(String.valueOf(show))/Double.parseDouble(String.valueOf(req))*100);
				jpv.setReq_rate(reqrate+"%");
			}
			convertList.add(jpv);
		}
		return convertList;
	}
	
	@Override
	public Map<String, Map<String, Object>> selectProjectidChannelMap() {
		return adMapper.selectProjectidChannelMap();
	}
	@Override
	public Map<String, Map<String, Object>> selectDauAndProjectMap(Map<String, Object> paramMap) {
		return adMapper.selectDauAndProjectMap(paramMap);
	}
	@Override
	public Map<String, Map<String, Object>> getAppInfoMap(String... channel) {
		String chan = "10118";
		if(channel != null && channel.length > 0)
			chan = channel[0];
		String query = "select CAST(id as char(20)) as mapkey, app_name from app_info where channel_id = "+chan;
		return adMapper.queryListMapOfKey(query);
	}
	
	@Override
	public Map<String, Map<String, Object>> getChannelAppInfoMap() {
		String query = "select appid as mapkey, tappname from adv_platform_app_info group by appid ";
		return adMapper.queryListMapOfKey(query);
	}
	@Override
  	public List<Map<String, Object>> selectAppInfoList(String where) {
		return adMapper.selectAppInfoList(where);
	}
	@Override
  	public List<Map<String, Object>> selectAppInfoListTwo(Map<String, String> paramMap) {
		List<Map<String, Object>> list = adMapper.selectAppInfoListTwo(paramMap);

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		list.forEach(act -> act.put("create_time", sdf.format(act.get("create_time"))));
		return list;
	}
	@Override
	public Map<String, DnAdsidCashTotalVo> getAdsidCashTotal(String date){
		
		String query = "select ad_sid mapkey,ad_sid adsid,dnappid appid,cha_id,agent,cha_type_name,cha_media,IFNULL(TRUNCATE(sum(aa.revenue)/sum(aa.pv)*1000,2),0) ecpm "+
				"from dn_cha_cash_total aa where date = '"+date+"' and app_id != '0' and dnappid != '0' and ad_sid is not null and ad_sid != '' and cha_id is not null "+
				"GROUP BY ad_sid";
		Map<String, DnAdsidCashTotalVo> ecpmMap = adMapper.getAdsidCashTotal(query);
		
		return ecpmMap;
	}
	@Override
	public Map<String, DnChaRevenueTotal> getChaidManageMap(){
		
		String query = "select cha_id,cha_id mapkey,yy.type_name cha_type_name,cha_media,cha_sub_launch,cha_ratio from dn_channel_info xx,dn_channel_type yy where xx.cha_type=yy.type_id ";
		Map<String, DnChaRevenueTotal> chaidMap = adMapper.getChaidManageMap(query);
		
		return chaidMap;
	}
	
	@Override
	public int execSql(String sql) {
		return adMapper.execSql(sql);
	}
	@Override
	public List<String> queryListString(String sql) {
		return adMapper.queryListString(sql);
	}
	@Override
	public List<Map<String, Object>> queryListMap(String sql) {
		return adMapper.queryListMap(sql);
	}
	@Override
	public List<Map<String, String>> queryListMapOne(String sql) {
		return adMapper.queryListMapOne(sql);
	}
	@Override
	public Map<String, Map<String, Object>> queryListMapOfKey(String sql) {
		return adMapper.queryListMapOfKey(sql);
	}
	@Override
	public <T> List<T> queryListBean(String sql, Class<T> clazz) {
		List<Map<String, String>> listBean = adMapper.queryListMapOne(sql);
		return JSONArray.parseArray(JSONArray.toJSONString(listBean), clazz);
	}
	@Override
	public int execSqlHandle(String sql, Object obj) {
		return adMapper.execSqlHandle(sql, obj);
	}
	@Override
	public List<NpPostVo> queryNpPost(String sql) {
		return adMapper.queryNpPost(sql);
	}
	@Override
	public List<Map<String, Object>> queryListMapTwo(String sql, Object obj) {
		return adMapper.queryListMapTwo(sql, obj);
	}
	@Override
	public int batchExecSql(Map<String, Object> paramMap) {
		return adMapper.batchExecSql(paramMap);
	}
	@Override
	public int batchExecSqlTwo(Map<String, Object> paramMap) {
		return adMapper.batchExecSqlTwo(paramMap);
	}
	
	// 渠道产品广告数据
	@Override
	public int insertUmengAdIncomeList(List<UmengAdIncomeVo> list) {
		try {
			// 增加adb表的数据入库
			umengMonitorMapper.insertUmengAdIncomeList(list);
		}catch (Exception e){
			e.printStackTrace();
		}
		return adMapper.insertUmengAdIncomeList(list);
	}
	@Override
	public List<UmengAdcodeVo> selectUmengAdcode(Map<String, String> paramMap) {
		return adMapper.selectUmengAdcode(paramMap);
	}
	@Override
	public int insertUmengAdcode(UmengAdcodeVo np) {
		return adMapper.insertUmengAdcode(np);
	}
	@Override
	public int updateUmengAdcode(UmengAdcodeVo np) {
		return adMapper.updateUmengAdcode(np);
	}
	@Override
	public int deleteUmengAdcode(UmengAdcodeVo np) {
		return adMapper.deleteUmengAdcode(np);
	}

	@Override
	public List<UmengAdcodeVo> selectUmengAdcodeByAdcode(Map<String, String> paramMap) {
		return adMapper.selectUmengAdcodeByAdcode(paramMap);
	}

	@Override
	public int batchUpdateUmengAdcode(UmengAdcodeVo np) {
		return adMapper.batchUpdateUmengAdcode(np);
	}

	// 渠道广告收入管理
	public List<AdvChaFeeVo> selectAdvChaFee(Map<String, String> parmMap) {
		
		String begin = parmMap.get("beginDt");
		String end = parmMap.get("endDt");
		//advType
		String advType = parmMap.get("advType");

		String channel = parmMap.get("channel");
		
		String gameNm = parmMap.get("gameNm");
		
		
		String sql = "select adv_dt,cha_name,cha_game,ad_pos,adv_fee,show_count,click_count from ADV_CHANNEL_FEE_CLIENT a where 1=1";
			
			sql = sql + " and a.adv_dt >= '"+begin+"' and a.adv_dt <= '"+end+"'";
			
			if(!StringUtils.isEmpty(channel)){
				sql = sql + " and trim(a.cha_name) = '"+channel+"'";
			}
			if(!StringUtils.isEmpty(advType)){
				sql = sql + " and a.ad_pos = '"+advType+"'";
			}
			if(!StringUtils.isEmpty(gameNm)){
				sql = sql + " and a.cha_game like '%"+gameNm+"%'";
			}
			
			List<AdvChaFeeVo> list = adMapper.selectAdvChaFee(sql);
			return list;				
	}

	@Override
	public int insertAdvChaFee(AdvChaFeeVo np) {
		return adMapper.insertAdvChaFee(np);
	}
	@Override
	public int updateAdvChaFee(AdvChaFeeVo np) {
		return adMapper.updateAdvChaFee(np);
	}
	@Override
	public int deleteAdvChaFee(AdvChaFeeVo np) {
		return adMapper.deleteAdvChaFee(np);
	}
	
	// 广告产品互推配置
	@Override
	public List<ActConfigVo> selectActConfig(Map<String, String> parmMap) {
		String appid = parmMap.get("appid");
		String cha_id = parmMap.get("cha_id");
		String pid = parmMap.get("pid");
		String status = parmMap.get("status");
		String link = parmMap.get("link");
		String icon = parmMap.get("icon");
		String aids = parmMap.get("aids");
		String isGameList =parmMap.get("isGameList");
		StringBuffer sb = new StringBuffer();
		if(appid != null && appid.length() > 0){
			sb.append(" and appid = '"+appid+"'");
		}
		if(cha_id != null && cha_id.length() > 0){
			sb.append(" and cha_id = '"+cha_id+"'");
		}
		if(pid != null && pid.length() > 0){
			sb.append(" and pid = '"+pid+"'");
		}
		if(status != null && status.length() > 0){
			sb.append(" and status = '"+status+"'");
		}
		if(link != null && link.length() > 0){
			sb.append(" and linkUrl like '%"+link+"%'");
		}
		if(icon != null && icon.length() > 0){
			sb.append(" and icon like '%"+icon+"%'");
		}
		if(aids != null && aids.length() > 0){
			sb.append(" and aid in ("+aids+")");
		}
		if(isGameList != null && isGameList.length() > 0){
			sb.append(" and gamelist is not null and LENGTH(trim(gamelist)) >0 ");
		}
        String sql = "select * from alone_act_config where 1=1 ";
        sql = sql + sb.toString() +" order by aid desc";
        
		List<ActConfigVo> list = adMapper.selectActConfig(sql);
		return list;
	}

	@Override
	public List<ActConfigVo> selectActConfigNew(Map<String, String> parmMap) {
		String appid = parmMap.get("appid");
		String cha_id = parmMap.get("cha_id");
		String pid = parmMap.get("pid");
		String status = parmMap.get("status");
		String link = parmMap.get("link");
		String icon = parmMap.get("icon");
		String aids = parmMap.get("aids");
		String isGameList =parmMap.get("isGameList");
		StringBuffer sb = new StringBuffer();
		if(appid != null && appid.length() > 0){
			sb.append(" and appid in ("+appid+")");
		}
		if(cha_id != null && cha_id.length() > 0){
			sb.append(" and cha_id in ("+cha_id+")");
		}
		if(pid != null && pid.length() > 0){
			sb.append(" and pid = '"+pid+"'");
		}
		if(status != null && status.length() > 0){
			sb.append(" and status = '"+status+"'");
		}
		if(link != null && link.length() > 0){
			sb.append(" and linkUrl like '%"+link+"%'");
		}
		if(icon != null && icon.length() > 0){
			sb.append(" and icon like '%"+icon+"%'");
		}
		if(aids != null && aids.length() > 0){
			sb.append(" and aid in ("+aids+")");
		}
		if(isGameList != null && isGameList.length() > 0){
			sb.append(" and gamelist is not null and LENGTH(trim(gamelist)) >0 ");
		}
		String sql = "select * from alone_act_config where 1=1 ";
		sql = sql + sb.toString() +" order by aid desc";
		List<ActConfigVo> list = adMapper.selectActConfig(sql);
		return list;
	}

	@Override
	public int insertActConfig(ActConfigVo act) {
		return adMapper.insertActConfig(act);
	}
	@Override
	public int updateActConfig(ActConfigVo act) {
		return adMapper.updateActConfig(act);
	}
	@Override
	public int updateActConfigByLinkUrl(String newLink, String aid) {
		String sql =  "update alone_act_config set linkUrl = "+newLink+" where aid in ("+aid+") ";
		int result = adMapper.execSql(sql);
		if(result > 0){
			return 1;
		}
		return 0;
	}
	@Override
	public int updateActConfigByIcon(String newIcon, String aid) {
		String sql =  "update alone_act_config set icon = "+newIcon+" where aid in ("+aid+") " ;
		int result = adMapper.execSql(sql);
		if(result > 0){
			return 1;
		}
		return 0;
	}
	@Override
	public int updatePlist(String newIcon, String oldLink, String oldIcon) {

		String sql = "";
		if(oldLink != null && oldLink.length() > 0 && !oldLink.equals("0")){
			sql = "update alone_act_config set plist = "+newIcon+" where linkUrl = '"+oldLink+"'";
		}
		if(oldIcon != null && oldIcon.length() > 0 && !oldIcon.equals("0")){
			sql = "update alone_act_config set plist = "+newIcon+" where icon = '"+oldIcon+"'";
		}
		if(oldIcon != null && oldIcon.length() > 0 && oldLink != null && oldLink.length() > 0 && !oldIcon.equals("0") && !oldLink.equals("0")){
			sql = "update alone_act_config set plist = "+newIcon+" where linkUrl = '"+oldLink+"' and icon = '"+oldIcon+"'";
		}
		int result = adMapper.execSql(sql);
		if(result > 0){
			return 1;
		}
		return 0;
	}
	
	@Override
	public List<BuYuNewCountVo> queryBuYuNew(Map<String, String> parmMap) {
		String beginDate = parmMap.get("beginDate");
		String endDate = parmMap.get("endDate");
		String channel = parmMap.get("channel");
		String mmid = parmMap.get("mmid");
		String chanmeNm = parmMap.get("chanmeNm");
		String groupMonth = parmMap.get("groupMonth");
		String type = parmMap.get("type");
		String companyid = parmMap.get("companyid");
		String appName = parmMap.get("appName");
		String osType = parmMap.get("osType");
		String departType = parmMap.get("departType");
		
		StringBuffer sb = new StringBuffer();
		StringBuffer tiaojian = new StringBuffer();
		
		
		sb.append(" and a.by_date >= '"+beginDate+"'");
		sb.append(" and a.by_date <= '"+endDate+"'");
		if(osType != null && osType != ""){
			sb.append(" and c.os_type = '"+osType+"'");
		}
		if(departType != null && departType != ""){
			sb.append(" and c.depart_type = '"+departType+"'");
		}
		
		if(channel!=null && !"".equals(channel)){
			sb.append(" and a.by_priid = '"+channel+"'");
		}
		if(mmid!=null && !"".equals(mmid)){
			sb.append(" and a.by_mmid = '"+mmid+"'");
		}
		if(chanmeNm!=null && !"".equals(chanmeNm)){
			sb.append(" and b.p_priName like '%"+chanmeNm+"%'");
		}
		if(type!=null && !"".equals(type)){
			sb.append(" and b.p_remark ='"+type+"'");  
		}
		if(companyid!=null && !"".equals(companyid)){
			sb.append(" and c.p_company = '"+companyid+"'");
		}
		if(appName!=null && !"".equals(appName)){
			sb.append(" and c.p_appId = '"+appName+"'");
		}
		if(groupMonth!=null && !"".equals(groupMonth)){
			tiaojian.append("c.depart_type departType,date_format(a.by_date,'%Y-%m') strDt,c.os_type,a.by_priid,a.by_mmid,sum(a.by_newcount) by_newcount,sum(a.push_newcount) push_newcount,sum(a.card_newcount) card_newcount,case when sum(a.yd_newcount) is null then 0 else sum(a.yd_newcount) end  yd_newcount ,case when sum(a.lt_newcount) is null then 0 else sum(a.lt_newcount) end  lt_newcount,case when sum(a.dx_newcount) is null then 0 else sum(a.dx_newcount) end  dx_newcount,b.p_priName,b.p_remark,c.p_ver,c.p_company,c.p_appNm");
			sb.append(" group by date_format(a.by_date,'%Y-%m'),a.by_priid,a.by_mmid,p_priName");
		}else{
			tiaojian.append(" a.by_date strDt,a.by_priid,c.os_type,a.by_mmid,a.by_newcount,a.push_newcount,a.card_newcount,case when a.yd_newcount is null then 0 else a.yd_newcount end  yd_newcount ,case when a.lt_newcount is null then 0 else a.lt_newcount end  lt_newcount,case when a.dx_newcount is null then 0 else a.dx_newcount end  dx_newcount,b.p_priName,b.p_remark,c.p_ver,c.p_company,c.p_appNm,c.depart_type departType");
			
		}
		
		String sql = "select "+tiaojian.toString()+" from buyu_newcount_total a LEFT JOIN zjh_project_info b on a.by_priid = b.p_pri and a.by_mmid = b.p_mmid LEFT JOIN tb_app_info c on a.by_priid = c.p_pri where 1 = 1 ";
		
		sql = sql + sb.toString() + " order by push_newcount desc";
		
		List<BuYuNewCountVo> list = adMapper.queryBuYuNew(sql);

		return list;
	}
	@Override
	public List<BuYuNewCountVo> queryAloneDau(Map<String, String> parmMap) {
		String beginDate = parmMap.get("beginDate");
		String endDate = parmMap.get("endDate");
		String channel = parmMap.get("channel");
		String chanmeNm = parmMap.get("chanmeNm");
		String groupMonth = parmMap.get("groupMonth");
		String type = parmMap.get("type");
		String companyid = parmMap.get("companyid");
		String appName = parmMap.get("appName");
		String departType = parmMap.get("departType");
		
		StringBuffer sb = new StringBuffer();
		StringBuffer tiaojian = new StringBuffer();
		
		
		sb.append(" and a.by_date >= '"+beginDate+"'");
		sb.append(" and a.by_date <= '"+endDate+"'");
		
		if(channel!=null && !"".equals(channel)){
			sb.append(" and a.by_priid = '"+channel+"'");
		}
		if(departType!=null && !"".equals(departType)){
			sb.append(" and c.depart_type = '"+departType+"'"); // 閮ㄩ棬
		}

		if(chanmeNm!=null && !"".equals(chanmeNm)){
			sb.append(" and b.p_priName like '%"+chanmeNm+"%'");
		}
		if(type!=null && !"".equals(type)){
			sb.append(" and a.p_remark ='"+type+"'");  
		}
		if(companyid!=null && !"".equals(companyid)){
			sb.append(" and c.p_company = '"+companyid+"'");
		}
		if(appName!=null && !"".equals(appName)){
			sb.append(" and c.p_appId = '"+appName+"'");
		}
		if(groupMonth!=null && !"".equals(groupMonth)){
			tiaojian.append("c.depart_type depart_type,date_format(a.by_date,'%Y-%m') strDt,sum(a.by_newcount) by_newcount,case when sum(a.yd_newcount) is null then 0 else sum(a.yd_newcount) end  yd_newcount ,case when sum(a.lt_newcount) is null then 0 else sum(a.lt_newcount) end  lt_newcount,case when sum(a.dx_newcount) is null then 0 else sum(a.dx_newcount) end  dx_newcount,case when sum(a.unknow_count) is null then 0 else sum(a.unknow_count) end  unknow_count");
			sb.append(" group by date_format(a.by_date,'%Y-%m')");
		}else{
			tiaojian.append(" a.by_date strDt,a.by_priid,a.by_newcount,case when a.yd_newcount is null then 0 else a.yd_newcount end  yd_newcount ,case when a.lt_newcount is null then 0 else a.lt_newcount end  lt_newcount,case when a.dx_newcount is null then 0 else a.dx_newcount end  dx_newcount,case when a.unknow_count is null then 0 else a.unknow_count end  unknow_count,c.p_ver,c.p_company,c.depart_type,c.p_appNm,a.p_remark");
			sb.append(" group by a.by_priid,date_format(a.by_date,'%Y-%m-%d')");
		}
		
		String sql = "select "+tiaojian.toString()+" from alone_dau_total a  LEFT JOIN zjh_project_info b on a.by_priid = b.p_pri LEFT JOIN tb_app_info c on a.by_priid = c.p_pri where 1 = 1 ";
		sql = sql + sb.toString() + " order by by_newcount desc";
		
		List<BuYuNewCountVo> list = adMapper.queryBuYuNew(sql);

		return list;
	}

	@Override
	public List<BuYuNewCountVo> getProjectConfig() {
		String sql = "select c.appid appid,a.pjId by_priid,a.versionName p_ver,a.gameName p_appNm,c.gameName p_remark,b.channel p_priName,a.platform os_type from dnwx_client.wbgui_formconfig a,dnwx_client.wbgui_channel b,dnwx_client.wbgui_gametype c  where a.channel = b.id and a.typeName=c.gameId";
		return adMapper.queryBuYuNew(sql);
	}

	// 落地页内容管理
	@Override
	public int insertLandingPageContent(List<LandingPageContentVo> list) {
		
		for (LandingPageContentVo lpc : list) {
			int result = uploadEditObject(lpc);
			if(result < 0){ return -1; }
		}
		
		int num = adMapper.insertLandingPageContent(list);
		return num;
	}
	@Override
	public int updateLandingPageContent(LandingPageContentVo lpc) {
		int result = uploadEditObject(lpc);
		if(result < 0){ return -1; }
		
		adMapper.updateLandingPageContent(lpc);
		return 1;
	}
	@Override
	public int deleteLandingPageContent(LandingPageContentVo lpc) {
		return adMapper.deleteLandingPageContent(lpc);
	}
	
	public int uploadEditObject(LandingPageContentVo lpc){
		// 阿里云上传OSS参数
		//String endpoint = "http://oss-cn-shenzhen.aliyuncs.com";
		//String accessKeyId = "LTAImrFvdsZ2E6Vd";
	    //String accessKeySecret = "******************************";

        String endpoint = "https://oss-cn-shenzhen.aliyuncs.com";
        String accessKeyId = "LTAI5tAWSWUTUQaGVDDArFMq";
        String accessKeySecret = "******************************";
	    //String bucketName = "vmhome";
	    String bucketName = "dnwx-res";

	    
	    String name = "";
	    String dir = "";
		String sql = "select * from landing_page_template where 1=1";
		List<Map<String, Object>> tpMap = queryListMap(sql); // 模版列表
		for (Map<String, Object> map : tpMap) {
			if(lpc.getTemplate_id().equals(map.get("id")+"")){
				// 渠道模板ID对应的模板文件名和目录
				String url = map.get("template_url").toString();
				name = url.substring(url.lastIndexOf("/")+1);
				dir = map.get("template_dir").toString();
			}
		}
		
		File ins = null;
		try {
			// 从OSS下载指定内容文件，修改title、down_url等参数，保存到本地文件，上传本地文件
	        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
	        File input = new File(CommonUtil.tempPath+"/"+dir+"_"+name);
			ossClient.getObject(new GetObjectRequest(bucketName, dir+"/"+name), input);
			Document doc = Jsoup.parse(input, "utf-8");
			doc.title(lpc.getTitle());
			
			//通过Document的select方法获取class为abc的Elements结点集合
			Element url_text = doc.getElementById("down_url");
			Element btn_text = doc.getElementById("buttom_set");
			if(url_text != null)
				url_text.html(lpc.getDown_url());
			if(btn_text != null)
				btn_text.html(lpc.getDown_text());
			
			// 将解析修改后的内容注入本地文件
			OutputStreamWriter osw = new OutputStreamWriter(new FileOutputStream("temp_"+lpc.getFile_name()), "utf-8");  
	        osw.write(doc.html());
	        osw.close();
	        input.delete();
	        
	        ins = new File("temp_"+lpc.getFile_name());
	        //ossClient.putObject(bucketName, lpc.getFile_catalog()+"/"+lpc.getFile_name(), ins);
            OSSUploadUtil.uploadDataOSS(bucketName, lpc.getFile_catalog()+"/"+lpc.getFile_name(), ins, null);
	        ossClient.shutdown();
	        
	        return 1;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if(ins != null)
				ins.delete();
		}
		return -1;
	}


	@Override
	public List<AdTotalHourTwoVo> queryHourReport(String sql) {
		return adMapper.queryHourReport(sql);
	}


	@Override
	public List<GDTWorkReportVo> selectGDTAdwordReport(String sql) {
		return adMapper.selectGDTAdwordReport(sql);
	}
	@Override
	public int insertGDTAdwordReport(List<GDTWorkReportVo> list) {
		return adMapper.insertGDTAdwordReport(list);
	}


	@Override
	public int insertGDTMedium(GDTWorkMediumVo np) {
		return adMapper.insertGDTMedium(np);
	}
	@Override
	public int updateGDTMedium(GDTWorkMediumVo np) {
		return adMapper.updateGDTMedium(np);
	}
	@Override
	public int insertGDTPlacement(GDTWorkMediumVo np) {
		return adMapper.insertGDTPlacement(np);
	}
	@Override
	public int updateGDTPlacement(GDTWorkMediumVo np) {
		return adMapper.updateGDTPlacement(np);
	}


	@Override
	public int insertHongBaoConfig(HongBaoInfoVo hb) {
		return adMapper.insertHongBaoConfig(hb);
	}

	@Override
	public int updateHongBaoConfig(HongBaoInfoVo hb) {
		return adMapper.updateHongBaoConfig(hb);
	}


	@Override
	public int updateUmengPushCha(List<UmengChannelTotalVo> keepList) {
		return adMapper.updateUmengPushCha(keepList);
	}


	@Override
	public List<AdvFeeVo> selectAdvChannelFeeTotal(Map<String, Object> paramMap) {
		return adMapper.selectAdvChannelFeeTotal(paramMap);
	}
	@Override
	public List<AdvFeeVo> selectAdvChannelFeeTotalTwo(Map<String, Object> paramMap) {
		return adMapper.selectAdvChannelFeeTotalTwo(paramMap);
	}
	@Override
	public AdvFeeVo selectAdvChannelFeeTotalThree(Map<String, Object> paramMap) {
		return adMapper.selectAdvChannelFeeTotalThree(paramMap);
	}
	@Override
	public int insertAdvChannelFeeTotalNewList(List<AdvFeeVo> list) {
		return adMapper.insertAdvChannelFeeTotalNewList(list);
	}
	@Override
	public List<AdvFeeVo> selectAdvChannelFeeTotalNew(Map<String, Object> paramMap) {
		return adMapper.selectAdvChannelFeeTotalNew(paramMap);
	}
	@Override
	public List<AdvFeeVo> selectAdvChannelFeeTotalHaiwai(Map<String, Object> paramMap) {
		return adMapper.selectAdvChannelFeeTotalHaiwai(paramMap);
	}

	@Override
	public Map<String, Map<String, Object>> selectAppinfoMap() {
		return adMapper.selectAppinfoMap();
	}
	
	@Override
	public int syncInsertAppKeepUserList(String today) {
		
		// 新增活跃数据入库
		Map<String, Object> paramMap = new HashMap<String, Object>();
		List<String> array = new ArrayList<String>();
		for (int i = 0; i < 30; i++) {
			String code = "" + i;
			if(i < 10){ code = "0" + i; }
			array.add(code);
		}
		paramMap.put("array", array);
		paramMap.put("today", today);
		paramMap.put("date", today.replace("-", ""));
		apkMapper.insertNewUserOne(paramMap);
		apkMapper.insertDauUserOne(paramMap);
		
		
		/*  
		  	插入昨天的信息，读取昨天的1日，2日，...一直到30日的留存appid,pid的组合信息，
			其中包括了各日期的留存值，再根据条件，appid、pid来获取多的留存放入其中，然后更新原有库内容
			按日期分成多个的List，然后执行多个，返回多个日期的appid、pid不同的留存数
		*/
		apkMapper.insertKeepUserOne(paramMap);
		
		/*
		 * 查询新增用户中创建时间与今日间隔天数的appid,pid,lsn
		 * 通过查询的信息与今日用户表连接查询，匹配出不同appid,pid的留存人数
		 * 将留存信息update到表中
		 * 
		 */
		DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
		for (int in = 1; in <= 30; in++) { // 30天
			String startday = DateTime.parse(today, format).minusDays(in).toString(format);
			System.out.println(today+"\t"+startday);
			paramMap.put("startday", startday);
			paramMap.put("daynum", in);
			
			List<ApkProductKeepVo> keep = apkMapper.selectKeepUserTwo(paramMap);
			if(keep != null && keep.size() > 0)
				apkMapper.updateKeepUserThree(keep);
		}
		
		return 1;
	}


	@Override
	public List<Map<String, Object>> selectPartnerApp(Map<String, Object> paramMap) {
		return adMapper.selectPartnerApp(paramMap);
	}
	@Override
	public Map<String,Object> selectPartnerAppSum(Map<String, Object> paramMap) {
		return adMapper.selectPartnerAppSum(paramMap);
	}
	@Override
	public List<Map<String, Object>> selectPartnerAppHaiwai2(Map<String, Object> paramMap) {
		return adMapper.selectPartnerAppHaiwai2(paramMap);
	}
	@Override
	public Map<String,Object> selectPartnerAppSumHaiwai2(Map<String, Object> paramMap) {
		return adMapper.selectPartnerAppSumHaiwai2(paramMap);
	}
	@Override
	public List<Map<String, Object>> selectPartnerInvestInfo(Map<String, String> paramMap) {
		return adMapper.selectPartnerInvestInfo(paramMap);
	}
	@Override
	public Map<String,Object> selectPartnerInvestInfoSum(Map<String, String> paramMap) {
		return adMapper.selectPartnerInvestInfoSum(paramMap);
	}
	@Override
	public List<Map<String, Object>> selectPartnerRevenueInfo(Map<String, String> paramMap) {
		return adMapper.selectPartnerRevenueInfo(paramMap);
	}
	@Override
	public Map<String,Object> selectPartnerRevenueInfoSum(Map<String, String> paramMap) {
		return adMapper.selectPartnerRevenueInfoSum(paramMap);
	}

	@Override
	public List<Map<String, Object>> selectDnChaDauTotal(Map<String, Object> paramMap) {
		return adMapper.selectDnChaDauTotal(paramMap);
	}
	@Override
	public Map<String, Object> selectDnChaDauTotalSum(Map<String, Object> paramMap) {
		return adMapper.selectDnChaDauTotalSum(paramMap);
	}


	@Override
	public List<Map<String, Object>> selectCpsPartnerReport(Map<String, Object> paramMap) {
		return adMapper.selectCpsPartnerReport(paramMap);
	}
	@Override
	public Map<String, Object> selectCpsPartnerReportSum(Map<String, Object> paramMap) {
		return adMapper.selectCpsPartnerReportSum(paramMap);
	}


	@Override
	public List<Map<String, Object>> selectDnChaCashTotal(Map<String, Object> paramMap) {
		List<Map<String, Object>> dnChaCashTotal = dnwxBiAdtMapper.selectDnChaCashTotal(paramMap);
		if (CollectionUtils.isEmpty(dnChaCashTotal)) {
			return dnChaCashTotal;
		}

		//将应用分类加上去
		Map<String, String> appCategoryMap = new HashMap<>();
		Map<String, String> channelTypeMap = new HashMap<>();

		List<String> groups = (List<String>)paramMap.get("groups");

		if (groups !=null && groups.contains("app_category")) {
			List<AppCategory> appCategoryNameList = someMapper.getAppCategorys();
			appCategoryMap = appCategoryNameList.stream()
					.collect(Collectors.toMap(appCategory -> appCategory.getId().toString(), appCategory -> appCategory.getName(), (k1, k2)->k1));
		}

		//将渠道类型加上去
		if (groups !=null && groups.contains("cha_type")) {
			List<DnChannelType> channelTypeList = ydMapper.selectDnChannelType();
			channelTypeMap = channelTypeList.stream()
					.collect(Collectors.toMap(dnChannelType -> dnChannelType.getTypeId().toString(), dnChannelType -> dnChannelType.getTypeName(), (k1, k2)->k1));
		}

		for (Map map : dnChaCashTotal) {
			if (!CollectionUtils.isEmpty(appCategoryMap)) {
				String app_category = appCategoryMap.get(com.wbgame.utils.StringUtils.changeNullToStr(map.get("app_category")));
				map.put("app_category",app_category);
			}

			if (!CollectionUtils.isEmpty(channelTypeMap)) {
				String channelType = channelTypeMap.get(com.wbgame.utils.StringUtils.changeNullToStr(map.get("cha_type")));
				map.put("cha_type",channelType);
			}
			map.put("fill_rate",map.get("fill_rate")==null?"0.0%":map.get("fill_rate")+"%");
			map.put("pv_rate",map.get("pv_rate")==null?"0.0%":map.get("pv_rate")+"%");
			map.put("click_rate",map.get("click_rate")==null?"0.0%":map.get("click_rate")+"%");
		}

		return dnChaCashTotal;
	}
	@Override
	public Map<String, Object> selectDnChaCashTotalSum(Map<String, Object> paramMap) {
		return dnwxBiAdtMapper.selectDnChaCashTotalSum(paramMap);
	}
	@Override
	public boolean syncDnChaCashTotal(String... tdate) {
		
		String sdate = DateTime.now().minusDays(1).toString("yyyyMMdd");
		String edate = DateTime.now().minusDays(1).toString("yyyyMMdd");
		if(tdate != null && tdate.length == 2){
			sdate = tdate[0];
			edate = tdate[1];
		}
		Map<String, String> accMap = new HashMap<>();
		accMap.put("3012665093", "7hKmoeiEmBd8x4p7AbxsVJDviMY89mgY");
		accMap.put("101080513854", "Rp52XGqaI,mw!qWSb57xn&=aTAWwcV=>");
		
		for (Entry<String, String> entry : accMap.entrySet()) {
			String member_id = entry.getKey();
			String secret = entry.getValue();
			
			String baseParam = "member_id="+member_id+"&start_date="+sdate+"&end_date="+edate;
			String token = getGdtTokenStr(member_id, secret);
			Map<String, String> headMap = new HashMap<>();
			headMap.put("token", token);
			
			String url = "https://api.adnet.qq.com/open/v1.0/report/get?"+baseParam;
			String httpGet = HttpClientUtils.getInstance().httpGet(url, headMap);

			JSONObject object = JSONObject.parseObject(httpGet);
			if(object != null && 0 == object.getIntValue("ret")
					&& object.getString("data") != null){
				
				List<JSONObject> array = JSONArray.parseArray(object.getJSONObject("data").getString("list"), JSONObject.class);
				if(array != null && array.size() > 0) {
					
					// 过滤掉 date值为空的数据
					List<JSONObject> collect = array.stream()
								.filter(act -> !BlankUtils.checkBlank(act.getString("date")))
								.collect(Collectors.toList());
					collect.forEach(act -> {
						// 返回的数据中带有千位逗号，不去除的话存表会有误
						act.put("request_count", act.getString("request_count").replace(",", ""));
						act.put("return_count", act.getString("return_count").replace(",", ""));
						act.put("ad_request_count", act.getString("ad_request_count").replace(",", ""));
						act.put("ad_return_count", act.getString("ad_return_count").replace(",", ""));
						act.put("pv", act.getString("pv").replace(",", ""));
						act.put("click", act.getString("click").replace(",", ""));
						act.put("revenue", act.getString("revenue").replace(",", ""));
						act.put("ecpm", act.getString("ecpm").replace(",", ""));
					});
					
					Map<String, Object> paramMap = new HashMap<String, Object>();
					paramMap.put("sql1", "replace into gdt_adwork_report_new(member_id,medium_name,app_id,placement_id,placement_name,placement_type,date,is_summary,request_count,return_count,ad_request_count,ad_return_count,pv,click,fill_rate,exposure_rate,click_rate,revenue,ecpm) values ");
					paramMap.put("sql2", " (#{li.member_id},#{li.medium_name},#{li.app_id},#{li.placement_id},#{li.placement_name},#{li.placement_type},#{li.date},#{li.is_summary},#{li.request_count},#{li.return_count},#{li.ad_request_count},#{li.ad_return_count},#{li.pv},#{li.click},#{li.fill_rate},#{li.exposure_rate},#{li.click_rate},#{li.revenue},#{li.ecpm}) ");
					paramMap.put("sql3", " ");
					paramMap.put("list", collect);
					batchExecSql(paramMap);
					
				}
			}
		}
		
		try {
			Thread.sleep(1000L);

			xyxService.synXyxToken(sdate,edate);
		} catch (InterruptedException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}

	@Override
	public boolean syncGroupCom(String day) {
		String del = "delete FROM dn_extend_group_sum where tdate = '"+day+"'";
		adv2Mapper.execSql(del);
		
		// 先拉取现在的广告源，再拉取只配置的appid配置的广告源，优先使用appid配置的
		String query = "SELECT adsid as mapkey,appid,adpos_type,strategy,adsid,ecpm,is_newuser "+
				"FROM dnwx_cfg.dn_extend_adconfig WHERE cha_id in ('csj','csjys','baibao','xmcsj','opcsj') AND adpos_type in ('plaque','plaqueVideo','video','msg','splash') "+
				"GROUP BY adsid ";
		Map<String, Map<String, Object>> chaMap = adv2Mapper.queryListMapOfKey(query);
		
		String query2 = "SELECT adsid as mapkey,appid,adpos_type,strategy,adsid,ecpm,is_newuser "+
				"FROM dnwx_cfg.dn_extend_adconfig WHERE appid != '' and cha_id = '' and prjid is null AND adpos_type in ('plaque','plaqueVideo','video','msg','splash') "+
				"GROUP BY adsid ";
		Map<String, Map<String, Object>> appMap = adv2Mapper.queryListMapOfKey(query2);
		chaMap.putAll(appMap);
		
		Collection<Map<String, Object>> oneList = chaMap.values();
		
		
		String sql2 = "SELECT '"+day+"' tdate,concat(appid,'') appid,adsid, "+
				"SUM(fail_num)+SUM(fill_num) req_num, "+
				"SUM(fill_num) fill_num, "+
				"SUM(selfshow_num) show_num, "+
				"SUM(click_num) click_num "+
				"FROM admsg_v3_total_2020 WHERE tdate = '"+day+"' "+
				"GROUP BY adsid ";
//		System.out.println("sql2 =="+sql2);
		List<Map<String, Object>> twoList = admsgMapper.queryListMap(sql2);
		
		// 匹配adsid 赋值adpos_type,strategy,ecpm
		List<Map<String, Object>> resultList = oneList.stream()
		.map(act -> 
			twoList.stream()
		        .filter(act2 -> Objects.equals(act2.get("adsid"), act.get("adsid")))
		        .findFirst().map(act2 -> {
		        	
		        	act.put("req_num", act2.get("req_num"));
		        	act.put("fill_num", act2.get("fill_num"));
		        	act.put("show_num", act2.get("show_num"));
		            return act;
		        })
		        .orElse(act)
	    )
	    .filter(Objects::nonNull).collect(Collectors.toList());
		
		// 查询出adsid对应的agent内容
		String sql3 = "select appid,cha_id,adsid,agent,adpos_type,sdk_adtype,adsid mapkey from dnwx_cfg.dn_extend_adsid_manage";
		Map<String, Map<String, Object>> sidMap = adMapper.queryListMapOfKey(sql3);
		for (Map<String, Object> act : resultList) {
			Map<String, Object> sid = sidMap.get(act.get("adsid")+"");
			if(sid != null){
				act.put("agent", sid.get("agent"));
				act.put("sdk_adtype", sid.get("sdk_adtype"));
			}
		}
		
		
		if(resultList != null && resultList.size() > 0){
			try {
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("sql1", "INSERT INTO dn_extend_group_sum(tdate,appid,cha_id,adsid,adpos_type,sdk_adtype,agent,strategy,ecpm,is_newuser,platform_req,platform_fill,platform_show) values ");
				paramMap.put("sql2", " ('"+day+"',#{li.appid},'csj',#{li.adsid},#{li.adpos_type},#{li.sdk_adtype},#{li.agent},#{li.strategy},#{li.ecpm},#{li.is_newuser},#{li.req_num},#{li.fill_num},#{li.show_num}) ");
				paramMap.put("sql3", " ");
				paramMap.put("list", resultList);
				adv2Mapper.batchExecSql(paramMap);
				
				
				String sql4 = "SELECT date,dnappid,ad_sid adsid,IFNULL(TRUNCATE(sum(revenue)/sum(pv)*1000,2),0) ecpm,SUM(click) click,SUM(pv) pv,SUM(return_count) return_count,SUM(request_count) request_count,TRUNCATE(SUM(revenue),2) revenue "+
						"FROM dn_cha_cash_total where date = '"+day+"' AND app_id != '' GROUP BY ad_sid ";
				List<Map<String, Object>> collect = adMapper.queryListMap(sql4);
				
				
				Map<String, Object> paramMap2 = new HashMap<String, Object>();
				paramMap2.put("sql1", "update dn_extend_group_sum set income=#{li.revenue},req_num=#{li.request_count},fill_num=#{li.return_count},show_num=#{li.pv},click_num=#{li.click},platform_ecpm=#{li.ecpm} where tdate='"+day+"' and adsid=#{li.adsid} ");
				paramMap2.put("list", collect);
				adv2Mapper.batchExecSqlTwo(paramMap2);
				
				logger.info("同步聚合综合查询 完成...");
				return true;
			} catch (Exception e) {
				e.printStackTrace();
				logger.info("同步聚合综合查询 异常...");
			}
		}
		return false;
	}
	@Override
	public boolean syncIncomeData(String day) {
		return true;
	}
	
	@Async("aaaScheduler")
	@Override
	public void syncIncomeDataTwo(String day) {
		logger.info(" 异步执行开始..."+Thread.currentThread().getName());
		
		String del = "delete FROM dn_extend_income_data_two where tdate = '"+day+"'";
		adMapper.execSql(del);
		
		// 获取广告统计数据，过滤后录入
		String sql1 = "SELECT '"+day+"' tdate,prjid,appid,cha_id,adsid,adpos_type,adpos_type as sdk_adtype, "+
				"SUM(fail_num)+SUM(fill_num) req_num, "+
				"SUM(fill_num) fill_num, "+
				"SUM(selfshow_num) show_num, "+
				"SUM(click_num) click_num "+
				"FROM admsg_v3_total_2020 where tdate = '"+day+"' "+
				"GROUP BY prjid,appid,cha_id,adsid";
		System.out.println("sql =="+sql1);
		List<Map<String, Object>> sinkList = admsgMapper.queryListMap(sql1);
		
		// 从变现平台明细拉取ecpm、agent等信息
		String query2 = "select ad_sid mapkey,dnappid appid,cha_id,agent,cha_type_name,cha_media,IFNULL(TRUNCATE(sum(aa.revenue)/sum(aa.pv)*1000,2),0) ecpm "+
				"from dn_cha_cash_total aa where date = '"+day+"' and app_id != '0' and ad_sid is not null and ad_sid != '' and cha_id is not null "+
				"GROUP BY ad_sid";
		System.out.println("sql2 =="+query2);
		Map<String, Map<String, Object>> ecpmMap = adMapper.queryListMapOfKey(query2);
		
		// 匹配到对应adsid则赋值ecpm，否则返回空
		List<Map<String, Object>> collect = sinkList.stream()
				.filter(act -> act.get("adsid") != null)
				.map(act -> {
					Map<String, Object> act2 = ecpmMap.get(act.get("adsid")+"");
					if(act2 != null){
						act.put("ecpm", act2.get("ecpm")+"");
						act.put("agent", act2.get("agent")+"");
						act.put("cha_type_name", act2.get("cha_type_name")+"");
					}
					act.put("adpos_type", converTypeName(act.get("adpos_type")+""));
					return act;
				})
				.collect(Collectors.toList());
		System.out.println("size=="+collect.size());
		
		// 入库
		if(collect != null && collect.size() > 0){
			try {
				String ins = "replace into dn_extend_income_data_two(tdate,prjid,appid,cha_id,adsid,adpos_type,sdk_adtype,agent,cha_type_name,ecpm,income,req_num,fill_num,show_num,click_num) "+
						"select '"+day+"' tdate,IFNULL(bb.projectid,'0') projectid,aa.* from  "+
						"	(select dnappid,cha_id,ad_sid,placement_type,placement_type sdktype,agent,cha_type_name, "+
						"			TRUNCATE(sum(aa.revenue)/sum(aa.pv)*1000,2) ecpm, "+
						"			TRUNCATE(sum(aa.revenue),2) income, "+
						"			TRUNCATE(sum(aa.request_count),0) request_count, "+
						"			TRUNCATE(sum(aa.return_count),0) return_count, "+
						"			TRUNCATE(sum(aa.pv),0) pv, "+
						"			TRUNCATE(sum(aa.click),0) click "+
						"		from dn_cha_cash_total aa  "+
						"		where date BETWEEN '"+day+"' AND '"+day+"'  "+
						"		and placement_type in ('banner') and app_id != '0' and dnappid != '0' and cha_id is not null "+
						"		group by dnappid,cha_id,ad_sid,placement_type "+
						"		having (sum(pv)+sum(revenue)) > 0 ) aa "+
						"left join "+
						"	(SELECT aa.gameName as gname,aa.pjId as projectid,aa.channelTag as cha_id, "+
						"			aa.versionName as ver,aa.channel as cid,bb.channel,cc.appid  "+
						"		FROM dnwx_client.wbgui_formconfig aa  "+
						"		JOIN dnwx_client.wbgui_channel bb on aa.channel = bb.id  "+
						"		JOIN dnwx_client.wbgui_gametype cc on aa.typeName = cc.gameId ) bb "+
						"on aa.dnappid=bb.appid and aa.cha_id=bb.cha_id "+
						"GROUP BY aa.ad_sid ";
				System.out.println("sql3 =="+ins);
				adMapper.execSql(ins);
				
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("sql1", "replace into dn_extend_income_data_two(tdate,appid,cha_id,prjid,adsid,adpos_type,sdk_adtype,agent,ecpm,cha_type_name,income,req_num,fill_num,show_num,click_num,act_num,add_num) values ");
				paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.cha_id},#{li.prjid},#{li.adsid},#{li.adpos_type},#{li.sdk_adtype},#{li.agent},#{li.ecpm},#{li.cha_type_name},(TRUNCATE(#{li.ecpm}*#{li.show_num}/1000,2)),#{li.req_num},#{li.fill_num},#{li.show_num},#{li.click_num},#{li.act_num},#{li.add_num}) ");
				paramMap.put("sql3", " ");
				paramMap.put("list", collect);
				adMapper.batchExecSql(paramMap);
				
				// 更新新增、活跃数据
				String sql = "SELECT tdate,projectid,chaid,IFNULL(SUM(act_num),0) act_num,IFNULL(SUM(add_num),0) add_num "+
						"FROM product_chaid_total where tdate = '"+day+"' "+
						"GROUP BY projectid,chaid";
				List<ApkProductStatsVo> proList = queryListBean(sql, ApkProductStatsVo.class);
				
				Map<String, Object> paramMap2 = new HashMap<String, Object>();
				paramMap2.put("sql1", "update dn_extend_income_data_two set act_num=#{li.act_num}, add_num=#{li.add_num} where tdate=#{li.tdate} and prjid=#{li.projectid} and cha_id=#{li.chaid} ");
				paramMap2.put("list", proList);
				adMapper.batchExecSqlTwo(paramMap2);
				
				System.out.println("同步变现收入预估2数据 完成...");
			} catch (Exception e) {
				e.printStackTrace();
				System.out.println("同步变现收入预估2数据 异常...");
			}
		}
		logger.info(" 异步执行结束..."+Thread.currentThread().getName());
	}
	
	@Override
	public boolean syncIncomeDataUmeng(String day) {
		
		String sql1 = "delete FROM dn_extend_income_data_umeng where tdate = '"+day+"'";
		adMapper.execSql(sql1);
		
		// 获取广告统计数据，过滤后录入
		String year = DateTime.parse(day).toString("yyyy");
		String query = "SELECT '"+day+"' tdate,concat(pid,'') prjid,ad_sid adsid, "+
				"SUM(req_num) + SUM(success_num) req_num, "+
				"SUM(success_num) fill_num, "+
				"SUM(selfshow_num) show_num, "+
				"SUM(click_num) click_num "+
				"FROM umeng_admsg_total where tdate = '"+day+"' "+
				"GROUP BY pid,ad_sid";
//		System.out.println("sql =="+query);
		List<Map<String, Object>> sinkList = admsgMapper.queryListMap(query);
		
		Map<String, String> typeMap = new HashMap<>();
		typeMap.put("natBanner", "banner");
		typeMap.put("nativeBanner", "banner");
		typeMap.put("plaqueVideo", "plaque");
		typeMap.put("natPlaque", "plaque");
		typeMap.put("nativePlaque", "plaque");
		typeMap.put("natSplash", "splash");
		typeMap.put("natVideo", "video");
		typeMap.put("yuans", "msg");
		
		Map<String, Map<String, Object>> prjMap = adMapper.selectProjectidChannelMap();
		for (Map<String, Object> act : sinkList) { // 匹配项目ID 赋值appid和cha_id
			Map<String, Object> prj = prjMap.get(act.get("prjid")+"");
			if(prj != null){
				act.put("appid", prj.get("appid")+"");
				act.put("cha_id", prj.get("cha_id")+"");
			}
			
			String[] split = (act.get("adsid")+"").split("_");
			if(split != null && split.length > 2){
				if("ios,android".contains(split[1]))
					act.put("sdk_adtype", split[2]);
				else
					act.put("sdk_adtype", split[1]);
				
				// 转换type类型存储
				String adtype = typeMap.get(act.get("sdk_adtype")+"");
				if(adtype != null)
					act.put("adpos_type", adtype);
				else
					act.put("adpos_type", act.get("sdk_adtype")+"");
			}
		}
		
		String query2 = "select dnappid appid,cha_id,ad_sid adsid,IFNULL(TRUNCATE(sum(aa.revenue)/sum(aa.pv)*1000,2),0) ecpm "+
				"from dn_cha_cash_total aa where date = '"+day+"' and app_id != '0' and ad_sid is not null and ad_sid != '' "+
				"GROUP BY ad_sid";
//		System.out.println("sql2 =="+query2);
		List<Map<String, Object>> queryList2 = adMapper.queryListMap(query2);
			
		// 匹配到对应adsid则赋值ecpm，否则返回空
		List<Map<String, Object>> collect = sinkList.stream()
			.filter(act -> {return (act.get("adsid") != null && !"banner,splash".contains(act.get("adpos_type")+""));})
			.map(act -> 
				queryList2.stream()
                .filter(act2 -> Objects.equals(act2.get("adsid"), act.get("adsid")))
                .findFirst().map(act2 -> {
                	act.put("ecpm", act2.get("ecpm")+"");
                    return act;
                }).orElse(act)
			)
            .filter(Objects::nonNull).collect(Collectors.toList());
		
		// 入库
		if(collect != null && collect.size() > 0){
			try {
				String ins = "replace into dn_extend_income_data_umeng(tdate,prjid,appid,cha_id,adsid,adpos_type,sdk_adtype,ecpm,income,req_num,fill_num,show_num,click_num) "+
						"select '"+day+"' tdate,bb.projectid,aa.* from  "+
						"	(select dnappid,cha_id,ad_sid,placement_type,placement_type sdktype, "+
						"			TRUNCATE(sum(aa.revenue)/sum(aa.pv)*1000,2) ecpm, "+
						"			TRUNCATE(sum(aa.revenue),2) income, "+
						"			TRUNCATE(sum(aa.request_count),0) request_count, "+
						"			TRUNCATE(sum(aa.return_count),0) return_count, "+
						"			TRUNCATE(sum(aa.pv),0) pv, "+
						"			TRUNCATE(sum(aa.click),0) click "+
						"		from dn_cha_cash_total aa  "+
						"		where date BETWEEN '"+day+"' AND '"+day+"'  "+
						"		and placement_type in ('banner','splash') and app_id != '0'  "+
						"		group by dnappid,cha_id,ad_sid,placement_type "+
						"		having (sum(pv)+sum(revenue)) > 0 ) aa "+
						"join  "+
						"	(SELECT aa.gameName as gname,aa.pjId as projectid,aa.channelTag as cha_id, "+
						"			aa.versionName as ver,aa.channel as cid,bb.channel,cc.appid  "+
						"		FROM dnwx_client.wbgui_formconfig aa  "+
						"		JOIN dnwx_client.wbgui_channel bb on aa.channel = bb.id  "+
						"		JOIN dnwx_client.wbgui_gametype cc on aa.typeName = cc.gameId ) bb "+
						"on aa.dnappid=bb.appid and aa.cha_id=bb.cha_id "+
						"GROUP BY aa.ad_sid ";
//				System.out.println("sql3 =="+ins);
				adMapper.execSql(ins);
				
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("sql1", "replace into dn_extend_income_data_umeng(tdate,appid,cha_id,prjid,adsid,adpos_type,sdk_adtype,ecpm,income,req_num,fill_num,show_num,click_num,act_num,add_num) values ");
				paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.cha_id},#{li.prjid},#{li.adsid},#{li.adpos_type},#{li.sdk_adtype},#{li.ecpm},(TRUNCATE(#{li.ecpm}*#{li.show_num}/1000,2)),#{li.req_num},#{li.fill_num},#{li.show_num},#{li.click_num},#{li.act_num},#{li.add_num}) ");
				paramMap.put("sql3", " ");
				paramMap.put("list", collect);
				adMapper.batchExecSql(paramMap);
				
				// 更新新增、活跃数据
				List<ApkProductStatsVo> proList = selectApkProductStats(day);
				
				Map<String, Object> paramMap2 = new HashMap<String, Object>();
				paramMap2.put("sql1", "update dn_extend_income_data_umeng set act_num=#{li.act_num}, add_num=#{li.add_num} where tdate=#{li.tdate} and prjid=#{li.projectid} ");
				paramMap2.put("list", proList);
				adMapper.batchExecSqlTwo(paramMap2);
				
				System.out.println("同步变现收入预估-友盟数据 完成...");
				return true;
			} catch (Exception e) {
				e.printStackTrace();
				System.out.println("同步变现收入预估-友盟数据 异常...");
			}
		}
		return false;
	}
	
	@Override
	public int updateCpsReportStatus(Map<String, Object> paramMap) {
		return adMapper.updateCpsReportStatus(paramMap);
	}

	@Override
	public int updateCpsReportDauIncome(Map<String, Object> paramMap) {
		return  adMapper.updateCpsReportDauIncome(paramMap);
	}
	
	@Override
	public List<ApkProductStatsVo> selectApkProductStats(String tdate) {
		
		String sql = "SELECT concat(yy.by_date,'') tdate,yy.by_priid projectid,"+
				"   SUBSTR(yy.by_priid FROM 1 FOR 5) product_id,"+
				"	SUM(yy.by_newcount) act_num,IFNULL(SUM(zz.push_newcount),0) add_num "+
				"FROM alone_dau_total yy "+
				"JOIN buyu_newcount_total zz "+
				"ON yy.by_date=zz.by_date and yy.by_priid=zz.by_priid "+
				"WHERE yy.by_date BETWEEN '"+tdate+"' AND '"+tdate+"' "+ 
				"GROUP BY yy.by_date,yy.by_priid";
		List<ApkProductStatsVo> proList = queryListBean(sql, ApkProductStatsVo.class);
		return proList;
	}


	@Override
	public int batchDelUmengAdcode(Map<String, Object> contentList) {
		
		return adMapper.batchDelUmengAdcode(contentList);
	}

	@Override
	public List<AdvertInferHourVo> queryAdvertHourReport(String sql) {
		return adMapper.queryAdvertHourReport(sql);
	}

    @Override
    public void batchInsertUmengAdcode(List<UmengAdcodeVo> list) {
		adMapper.batchInsertUmengAdcode(list);
    }
    @Override
    public void batchInsertUmengAdcode2(List<UmengAdcodeVo> list) {
		adMapper.batchInsertUmengAdcode2(list);
    }
    @Override
	public int insertDnwxReyunConfigThree(DnwxReyunConfigVo record) {
		return adMapper.insertDnwxReyunConfigThree(record);
	}
    @Override
	public int insertDnwxReyunConfigThreeRecord(DnwxReyunConfigVo record) {
		return adMapper.insertDnwxReyunConfigThreeRecord(record);
	}

    @Override
    public List<Map<String, Object>> selectDnAppChannelRevenueTotal(Map<String, Object> paramMap) {
    	return adMapper.selectDnAppChannelRevenueTotal(paramMap);
    }
    @Override
    public Map<String, Object> selectDnAppChannelRevenueTotalSum(Map<String, Object> paramMap) {
    	return adMapper.selectDnAppChannelRevenueTotalSum(paramMap);
    }
    
    @Override
	public List<Map<String, Object>> selectDnAppRevenueTotal(Map<String, Object> paramMap) {
		return adMapper.selectDnAppRevenueTotal(paramMap);
	}
	@Override
	public Map<String, Object> selectDnAppRevenueTotalSum(Map<String, Object> paramMap) {
		return adMapper.selectDnAppRevenueTotalSum(paramMap);
	}

	@Override
	public boolean syncDnAppRevenueTotal(String day, String... appid) {
		logger.info("同步syncDnAppRevenueTotal 开始...");
		
		/** 支持按照产品来同步数据 */
		String where = "";
		if(appid != null && appid.length > 0){
			where = " and appid in ("+String.join(",",appid)+") ";
		}
		
		String del = "delete FROM dn_app_revenue_total where tdate = '"+day+"' "+where;
		adMapper.execSql(del);
		
		
		// 先抽取新增活跃数据，再将返点消耗数据更新至表
		adMapper.insertDnAppRevenueTotal(day);
		adMapper.insertDnAppRevenueTotalTwo(day);

		// 特殊处理 只有付费收入但是没有广告收入的应用
		try {
			Map<String,Object> map = new HashMap<>();
			List<String> specialAppids = adMapper.getDnAppPayRevenueSpecicalAppids();
			if (specialAppids.size()>0){
				map.put("tdate",day);
				map.put("appid",specialAppids);
				adMapper.insertDnAppPayRevenueSpecical(map);
			}
		}catch (Exception e){
			logger.error("应用收支汇总表-处理只有付费收入但是没有广告收入的应用报错:",e);
		}

		// 友盟新增活跃
		String sql = "SELECT tdate,appid,SUM(act_num) actnum,SUM(add_num) addnum FROM umeng_user_channel_total where tdate = '"+day+"' GROUP BY tdate,appid";
		List<Map<String, Object>> listMap = adMapper.queryListMap(sql);
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sql1", "update dn_app_revenue_total set addnum=#{li.addnum},actnum=#{li.actnum} where tdate=#{li.tdate} and appid=#{li.appid} ");
			paramMap.put("list", listMap);
			adMapper.batchExecSqlTwo(paramMap);
		} catch (Exception e) {
			e.printStackTrace();
			
		}
		
		// 投放金额
		String sql2 = "SELECT `day` tdate,app appid,TRUNCATE(SUM(rebateSpend),2) rebate_consume FROM dnwx_adt.dn_report_spend_china where `day` = '"+day+"' GROUP BY app";
		List<Map<String, Object>> listMap2 = dnwxBiAdtMapper.queryListMap(sql2);
		try {
			Map<String, Object> paramMap2 = new HashMap<String, Object>();
			paramMap2.put("sql1", "update dn_app_revenue_total set rebate_consume=#{li.rebate_consume} where tdate=#{li.tdate} and appid=#{li.appid} ");
			paramMap2.put("list", listMap2);
			adMapper.batchExecSqlTwo(paramMap2);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		// 内容收入.baidu
		String sql3 = "SELECT tdate,appid,SUM(income) bd_revenue FROM baidu_cash_total where tdate = '"+day+"' GROUP BY appid";
		List<Map<String, Object>> listMap3 = adv2Mapper.queryListMap(sql3);
		try {
			Map<String, Object> paramMap3 = new HashMap<String, Object>();
			paramMap3.put("sql1", "update dn_app_revenue_total set bd_revenue=#{li.bd_revenue} where tdate=#{li.tdate} and appid=#{li.appid} ");
			paramMap3.put("list", listMap3);
			adMapper.batchExecSqlTwo(paramMap3);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		// 新增用户付费数.付费率
		String sql4 = "SELECT tdate,appid,reg_user_cnt,iap_reg_user_cnt,android_order_cnt,ios_order_cnt FROM dnwx_bi.ads_apk_isp_user_info_daily where tdate = '"+day+"' GROUP BY appid";
		List<Map<String, Object>> listMap4 = dnwxBiMapper.queryListMap(sql4);
		try {
			Map<String, Object> paramMap4 = new HashMap<String, Object>();
			paramMap4.put("sql1", "update dn_app_revenue_total set reg_user_cnt=#{li.reg_user_cnt},iap_reg_user_cnt=#{li.iap_reg_user_cnt},android_order_cnt=#{li.android_order_cnt},ios_order_cnt=#{li.ios_order_cnt} where tdate=#{li.tdate} and appid=#{li.appid} ");
			paramMap4.put("list", listMap4);
			adMapper.batchExecSqlTwo(paramMap4);
		} catch (Exception e) {
			e.printStackTrace();
		}

		// 支付收入
		try {
			Map<String, String> billMap = new HashMap<>();
			billMap.put("tdate", day);
			List<Map<String, Object>> list = adMapper.selectDnBillRevenueAppRatio(billMap);

			Map<String, Object> paramMap4 = new HashMap<String, Object>();
			paramMap4.put("sql1", "update dn_app_revenue_total set pay_revenue=#{li.pay_revenue},paynum=#{li.paynum} where tdate=#{li.tdate} and appid=#{li.appid} ");
			paramMap4.put("list", list);
			yyhzMapper.batchExecSqlTwo(paramMap4);
			logger.info("同步syncDnAppRevenueTotal 完成...");
			return true;
		}catch (Exception e){
			e.printStackTrace();
			logger.info("同步syncDnAppRevenueTotal 异常...");
		}
		return false;
	}
	
	
	@Override
	public boolean syncDnAppChannelRevenueTotal(String day, String... appid) {
		logger.info("同步syncDnAppChannelRevenueTotal 开始...");
		
		/** 支持按照产品来同步数据 */
		String where = "";
		if(appid != null && appid.length > 0){
			where = " and appid in ("+String.join(",",appid)+") ";
		}
		
		String del = "delete FROM dn_app_channel_revenue_total where tdate = '"+day+"' "+where;
		adMapper.execSql(del);
		
		
		// 先抽取新增活跃数据，再将返点消耗数据更新至表
		adMapper.insertDnAppChannelRevenueTotal(day);
		adMapper.insertDnAppChannelRevenueTotalTwo(day);
		
		/*
		// 特殊处理 只有付费收入但是没有广告收入的应用
		try {
			Map<String,Object> map = new HashMap<>();
			List<String> specialAppids = adMapper.getDnAppPayRevenueSpecicalAppids();
			if (specialAppids.size()>0){
				map.put("tdate",day);
				map.put("appid",specialAppids);
				adMapper.insertDnAppPayRevenueSpecical(map);
			}
		}catch (Exception e){
			logger.error("应用收支汇总表-处理只有付费收入但是没有广告收入的应用报错:",e);
		}*/
		
		// 友盟新增活跃
		String sql = "SELECT tdate,appid,install_channel channel,SUM(act_num) actnum,SUM(add_num) addnum FROM umeng_user_channel_total where tdate = '"+day+"' GROUP BY appid,install_channel";
		List<Map<String, Object>> listMap = adMapper.queryListMap(sql);
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sql1", "update dn_app_channel_revenue_total set addnum=#{li.addnum},actnum=#{li.actnum} where tdate=#{li.tdate} and appid=#{li.appid} and channel=#{li.channel} ");
			paramMap.put("list", listMap);
			adMapper.batchExecSqlTwo(paramMap);
		} catch (Exception e) {
			e.printStackTrace();
			
		}
		
		// 投放金额
		String sql2 = "SELECT `day` tdate,app appid,channel1 channel,TRUNCATE(SUM(rebateSpend),2) rebate_consume FROM dnwx_adt.dn_report_spend_china where `day` = '"+day+"' GROUP BY app,channel1";
		List<Map<String, Object>> listMap2 = dnwxBiAdtMapper.queryListMap(sql2);
		try {
			Map<String, Object> paramMap2 = new HashMap<String, Object>();
			paramMap2.put("sql1", "update dn_app_channel_revenue_total set rebate_consume=#{li.rebate_consume} where tdate=#{li.tdate} and appid=#{li.appid} and channel=#{li.channel} ");
			paramMap2.put("list", listMap2);
			adMapper.batchExecSqlTwo(paramMap2);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		/*
		// 内容收入.baidu
		String sql3 = "SELECT tdate,appid,SUM(income) bd_revenue FROM baidu_cash_total where tdate = '"+day+"' GROUP BY appid";
		List<Map<String, Object>> listMap3 = adMapper.queryListMap(sql3);
		try {
			Map<String, Object> paramMap3 = new HashMap<String, Object>();
			paramMap3.put("sql1", "update dn_app_revenue_total set bd_revenue=#{li.bd_revenue} where tdate=#{li.tdate} and appid=#{li.appid} ");
			paramMap3.put("list", listMap3);
			adMapper.batchExecSqlTwo(paramMap3);
		} catch (Exception e) {
			e.printStackTrace();
		}*/
		
		// 新增用户付费数.付费率
		String sql4 = "SELECT tdate,appid,channel,reg_user_cnt,iap_reg_user_cnt,android_order_cnt,ios_order_cnt FROM dnwx_bi.ads_apk_channel_isp_user_info_daily where tdate = '"+day+"' GROUP BY appid,channel";
		List<Map<String, Object>> listMap4 = dnwxBiMapper.queryListMap(sql4);
		try {
			Map<String, Object> paramMap4 = new HashMap<String, Object>();
			paramMap4.put("sql1", "update dn_app_channel_revenue_total set reg_user_cnt=#{li.reg_user_cnt},iap_reg_user_cnt=#{li.iap_reg_user_cnt},android_order_cnt=#{li.android_order_cnt},ios_order_cnt=#{li.ios_order_cnt} where tdate=#{li.tdate} and appid=#{li.appid} and channel=#{li.channel} ");
			paramMap4.put("list", listMap4);
			adMapper.batchExecSqlTwo(paramMap4);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		// 支付收入
		try {
			Map<String, String> billMap = new HashMap<>();
			billMap.put("tdate", day);
			List<Map<String, Object>> list = adMapper.selectDnBillRevenueAppChannelRatio(billMap);
			
			Map<String, Object> paramMap4 = new HashMap<String, Object>();
			paramMap4.put("sql1", "update dn_app_channel_revenue_total set pay_revenue=#{li.pay_revenue},paynum=#{li.paynum} where tdate=#{li.tdate} and appid=#{li.appid} and channel=#{li.channel}");
			paramMap4.put("list", list);
			yyhzMapper.batchExecSqlTwo(paramMap4);
			logger.info("同步syncDnAppChannelRevenueTotal 完成...");
			return true;
		}catch (Exception e){
			e.printStackTrace();
			logger.info("同步syncDnAppChannelRevenueTotal 异常...");
		}
		return false;
	}
	
	@Override
	public boolean syncDnAppChaRevenueTotal(String day, String... appid) {
		
		logger.info("同步syncDnAppChaRevenueTotal 开始...");
		
		/** 支持按照产品来同步数据 */
		String where = "";
		if(appid != null && appid.length > 0){
			where = " and appid in ("+String.join(",",appid)+") ";
		}
		
		String del = "delete FROM dn_app_cha_revenue_total where tdate = '"+day+"' "+where;
		adMapper.execSql(del);
		
		// 先抽取变现收入校准后的分渠道收支，使用ADB数据源中的表
		String query1 = "select aa.tdate,aa.appid,aa.cha_id,aa.cha_type_name,aa.cha_media,SUM(aa.revise_revenue) revenue "+
					"from dnwx_bi.ads_dn_extend_revise_income_daily aa where aa.tdate = '"+day+"' and aa.revise_show is not null "+
					"group by aa.appid,aa.cha_id";
		List<Map<String, String>> revenueList = dnwxBiMapper.queryListMapOne(query1);
		if(revenueList != null && !revenueList.isEmpty()){
			
			Map<String, DnChaRevenueTotal> chaidMap = getChaidManageMap();
			revenueList.forEach(act -> {
				DnChaRevenueTotal cha = chaidMap.get(act.get("cha_id"));
				if(cha != null){
					act.put("cha_sub_launch", cha.getCha_sub_launch());
				}
			});
			
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sql1", "INSERT IGNORE dn_app_cha_revenue_total(tdate,appid,cha_id,cha_type_name,cha_media,cha_sub_launch,ad_revenue) values ");
			paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.cha_id},#{li.cha_type_name},#{li.cha_media},#{li.cha_sub_launch},#{li.revenue}) ");
			paramMap.put("sql3", " ");
			paramMap.put("list", revenueList);
			adMapper.batchExecSql(paramMap);
		}
		// 兼容拉取老版本产品渠道收支
		adMapper.insertDnAppChaRevenueTwo(day);

		//处理特殊应用 只有付费收入但是没有广告收入
		try {
			Map<String,Object> map = new HashMap<>();
			List<String> specialAppids = adMapper.getDnAppPayRevenueSpecicalAppids();
			if (specialAppids.size()>0){
				map.put("tdate",day);
				map.put("appid",specialAppids);
				adMapper.insertDnChaAppPayRevenueSpecical(map);
			}
		}catch (Exception e){
			logger.error("应用分渠道收支数据-处理只有付费收入但是没有广告收入的应用报错:",e);
		}
		
		try {
			// 更新新增活跃数据
			String sql = "SELECT * FROM umeng_user_channel_total WHERE tdate = '"+day+"'";
			List<ApkProductStatsVo> proList = queryListBean(sql, ApkProductStatsVo.class);

			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sql1", "update dn_app_cha_revenue_total set actnum=#{li.act_num},addnum=#{li.add_num} where tdate=#{li.tdate} and appid=#{li.appid} and cha_id=#{li.install_channel} ");
			paramMap.put("list", proList);
			adMapper.batchExecSqlTwo(paramMap);


			// 更新投放返点消耗金额
			String sql2 = "SELECT `day` tdate,app appid,channel cha_sub_launch,TRUNCATE(SUM(rebateSpend),2) rebate_consume FROM dnwx_adt.dn_report_spend_china where `day` = '"+day+"' GROUP BY app,channel";
			List<Map<String, Object>> listMap2 = dnwxBiAdtMapper.queryListMap(sql2);

			Map<String, Object> paramMap2 = new HashMap<String, Object>();
			paramMap2.put("sql1", "update dn_app_cha_revenue_total set rebate_consume=#{li.rebate_consume} where tdate=#{li.tdate} and appid=#{li.appid} and cha_sub_launch=#{li.cha_sub_launch}");
			paramMap2.put("list", listMap2);
			adMapper.batchExecSqlTwo(paramMap2);
			
			try {
				// 更新支付收入数据
				Map<String, String> billMap = new HashMap<>();
				billMap.put("tdate", day);
				List<Map<String, Object>> list = adMapper.selectDnBillRevenue(billMap);

				Map<String, Object> paramMap4 = new HashMap<String, Object>();
				paramMap4.put("sql1", "update dn_app_cha_revenue_total set pay_revenue=#{li.bill_revenue} where tdate=#{li.tdate} and appid=#{li.appid} and cha_id=#{li.cha_id} ");
				paramMap4.put("list", list);
				yyhzMapper.batchExecSqlTwo(paramMap4);

			}catch (Exception e){
				logger.error("update dn_app_cha_revenue_total pay_revenue error:",e);
			}

			logger.info("同步syncDnAppChaRevenueTotal 完成...");
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncDnAppChaRevenueTotal 异常...");
			return false;
		}
		
		// 抽取到游戏分渠道收支表，移除应用组-3和海外应用组-15
		String delA = "delete FROM dn_game_cha_revenue_total where tdate = '"+day+"'";
		adMapper.execSql(delA);
		
		String sqlA = "replace into dn_game_cha_revenue_total(tdate,appid,cha_id,cha_type_name,cha_media,cha_sub_launch,addnum,actnum,rebate_consume,ad_revenue,pay_revenue) "+
				"select * from dn_app_cha_revenue_total where tdate = '"+day+"' and appid in (select id from app_info where bus_category = 1) ";
		adMapper.execSql(sqlA);
		
		try {
			// 这些渠道的广告收入要从变现平台明细表拉取，先设默认值为0
			String update = "update dn_game_cha_revenue_total set ad_revenue=0 "+
					"where tdate = '"+day+"' "+
					"and (cha_id in ('oppo','vivo','xiaomi','apple','xiaomimj','oppoml','jinli','oppo2') or cha_id in (SELECT cha_id FROM `dn_channel_info` where cha_type=8 and cha_id != 'ylyq')) ";
			adMapper.execSql(update);
			
			String sqlB = "select '"+day+"' tdate,dnappid appid,cha_id,agent,cha_type_name,cha_media,IFNULL(TRUNCATE(sum(aa.revenue),2),0) ad_revenue "+
					"from dn_cha_cash_total aa where date = '"+day+"' and app_id != '0' and dnappid != '0' and ad_sid != '' and cha_id is not null "+
					"and (cha_id in ('oppo','oppoml','oppo2','oppomj','vivo','vivo2','vivoml','xiaomi','xiaomimj','xiaomiml','apple','jinli') or cha_id in (SELECT cha_id FROM `dn_channel_info` where cha_type=8 and cha_id != 'ylyq')) "+
					"GROUP BY dnappid,cha_id";
			List<DnChaRevenueTotal> reList = queryListBean(sqlB, DnChaRevenueTotal.class);
			
			Map<String, Object> paramMap2 = new HashMap<String, Object>();
			paramMap2.put("sql1", "update dn_game_cha_revenue_total set ad_revenue=#{li.ad_revenue} where tdate=#{li.tdate} and appid=#{li.appid} and cha_id=#{li.cha_id}");
			paramMap2.put("list", reList);
			adMapper.batchExecSqlTwo(paramMap2);
			
			// 单独更新cps合作渠道的投放金额
			List<DnChannelInfo> chaList = someMapper.selectDnChannelRat();
			List<DnChaRevenueTotal> collect = reList.stream().map(dnc -> {
				// 判断子渠道标识，计算投放金额
				for (int i = 0; i < chaList.size(); i++) {
					if(dnc.getCha_id().equals(chaList.get(i).getChaId())){
						BigDecimal rot = new BigDecimal(chaList.get(i).getChaRatio());
						dnc.setRebate_consume(new BigDecimal(dnc.getAd_revenue()).multiply(rot).toString());
						return dnc;
					}
				}
				return null;
			}).filter(Objects::nonNull).collect(Collectors.toList());
			
			Map<String, Object> paramMap3 = new HashMap<String, Object>();
			paramMap3.put("sql1", "update dn_game_cha_revenue_total set rebate_consume=#{li.rebate_consume} where tdate=#{li.tdate} and appid=#{li.appid} and cha_id=#{li.cha_id}");
			paramMap3.put("list", collect);
			adMapper.batchExecSqlTwo(paramMap3);
			
			// 单独更新ios合作渠道的投放金额
			String sql5 = "SELECT `day` tdate,app appid,'apple' cha_id,TRUNCATE(SUM(rebateSpend),2) rebate_consume "+
				"FROM dnwx_adt.dn_report_spend_china where `day` = '"+day+"' and app in (SELECT DISTINCT appId from dnwx_adt.dn_group_app where groupId=12) "+
				"GROUP BY app";
			List<Map<String, Object>> listMap5 = dnwxBiAdtMapper.queryListMap(sql5);

			Map<String, Object> paramMap5 = new HashMap<String, Object>();
			paramMap5.put("sql1", "update dn_game_cha_revenue_total set rebate_consume=#{li.rebate_consume} where tdate=#{li.tdate} and appid=#{li.appid} and cha_id=#{li.cha_id}");
			paramMap5.put("list", listMap5);
			adMapper.batchExecSqlTwo(paramMap5);
			
			
			// 更新计费收入
			Map<String, String> billMap = new HashMap<>();
			billMap.put("tdate", day);
			List<Map<String, Object>> listMap4 = adMapper.selectDnBillRevenue(billMap);
			
			Map<String, Object> paramMap4 = new HashMap<String, Object>();
			paramMap4.put("sql1", "update dn_game_cha_revenue_total set bill_revenue=#{li.bill_revenue} where tdate=#{li.tdate} and appid=#{li.appid} and cha_id=#{li.cha_id}");
			paramMap4.put("list", listMap4);
			adMapper.batchExecSqlTwo(paramMap4);
			
			
			/** 同步至ADB表中 */
			String query = "select * from dn_game_cha_revenue_total where tdate='"+day+"'";
			List<Map<String, String>> gameList = adMapper.queryListMapOne(query);
			if(gameList != null && !gameList.isEmpty()){
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("sql1", "replace into dnwx_wjy.dn_game_cha_revenue_total(tdate,appid,cha_id,cha_type_name,cha_media,cha_sub_launch,addnum,actnum,rebate_consume,ad_revenue,bill_revenue,pay_revenue) values ");
				paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.cha_id},#{li.cha_type_name},#{li.cha_media},#{li.cha_sub_launch},#{li.addnum},#{li.actnum},#{li.rebate_consume},#{li.ad_revenue},#{li.bill_revenue},#{li.pay_revenue}) ");
				paramMap.put("sql3", " ");
				paramMap.put("list", gameList);
				dnwxBiMapper.batchExecSql(paramMap);
			}
			
			logger.info("同步syncDnGameChaRevenueTotal 完成...");
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncDnGameChaRevenueTotal 异常...");
			return false;
		}
		
		return true;
	}
	
	@Override
	public boolean syncShowGapTotal(String day) {
		
		logger.info("同步syncShowGapTotal 开始...");
		try {
			/** 临时处理vivo渠道yuans广告源无selfshow问题  */
			String update = "update admsg_v3_total_2020 set selfshow_num=show_num "+
					"where tdate='"+day+"' and cha_id='vivo' and adsid like '%yuans%' "+
					"and show_num > 10 and selfshow_num is null";
			admsgMapper.execSql(update);
			
			
			String del = "delete FROM dn_show_gap where tdate = '"+day+"'";
			adMapper.execSql(del);
			
			// 分别查询出变现平台明细数据和自统计数据，通过产品+广告类型+广告源关联起来，合并数值
			String sql = "select date tdate,dnappid appid,placement_type adpos_type,ad_sid adsid,agent,SUM(pv) pv,SUM(click) click,CONCAT(dnappid,placement_type,ad_sid) mapkey "+
					"from dn_cha_cash_total aa where date = '"+day+"' and app_id != '0' and dnappid != '0' and ad_sid is not null "+
					"group by dnappid,placement_type,ad_sid";
			List<Map<String, Object>> oneList = adMapper.queryListMap(sql);
			
			String sql2 = "SELECT adsid as mapkey,tdate,appid,adtype,adsid,IFNULL(SUM(selfshow_num),0) selfshow_num,IFNULL(SUM(click_num),0) click_num "+
					"FROM admsg_v3_total_2020 WHERE tdate = '"+day+"' "+
					"GROUP BY adsid";
			Map<String, Map<String, Object>> showMap = admsgMapper.queryListMapOfKey(sql2);
			
			List<Map<String, Object>> collect = oneList.stream().map(act -> {
					
				Map<String, Object> act2 = showMap.get(act.get("adsid")+"");
				if(act2 != null){
					act.put("selfshow_num", act2.get("selfshow_num")+"");
		        	act.put("click_num", act2.get("click_num")+"");
				}
				return act;
			})
		    .filter(Objects::nonNull).collect(Collectors.toList());
			
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sql1", "insert into dn_show_gap(tdate,appid,adpos_type,adsid,agent,pv,selfshow_num,click,click_num) values ");
			paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.adpos_type},#{li.adsid},#{li.agent},#{li.pv},#{li.selfshow_num},#{li.click},#{li.click_num}) ");
			paramMap.put("sql3", " ");
			paramMap.put("list", collect);
			adMapper.batchExecSql(paramMap);
			
			logger.info("同步syncShowGapTotal 完成...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncShowGapTotal 异常...");
		}
		return false;
	}
	
	@Override
	public boolean syncExtendIncomeRevise(String day) {
		
		logger.info("同步syncExtendIncomeRevise 开始...");
		try {
			String del = "delete FROM dn_extend_revise_income where tdate = '"+day+"'";
			adMapper.execSql(del);
			
			// 获取广告统计数据，过滤后录入
			String sql1 = "SELECT * FROM dn_extend_income_data_two where tdate = '"+day+"'";
			System.out.println("sql1 =="+sql1);
			List<Map<String, Object>> oneList = adMapper.queryListMap(sql1);
			
			// 分别查询出变现平台明细数据和gap数据，通过广告源关联起来，合并数值
			String sql2 = "SELECT adsid mapkey,IFNULL(TRUNCATE((sum(selfshow_num)-sum(pv))/sum(selfshow_num)*100, 1),0) pv_gap "+
					"FROM dn_show_gap WHERE tdate = '"+day+"' "+
					"GROUP BY adsid";
			System.out.println("sql2 =="+sql2);
			Map<String, Map<String, Object>> gapMap = adMapper.queryListMapOfKey(sql2);
			
			// 通过指定字段进行关联匹配然后赋值，首先设置默认值让其不为null，否则影响sql计算结果
			oneList.forEach(sink -> sink.put("gap_val", 0));
			
			List<Map<String, Object>> collect = oneList.stream()
			.map(sink -> {
				Map<String, Object> gap = gapMap.get(sink.get("adsid")+"");
				if(gap != null){
					sink.put("gap_val", gap.get("pv_gap")+"");
				}
				
				if(sink.get("show_num") == null){
					sink.put("show_num", 0);
				}
				return sink;
			})
			.filter(Objects::nonNull).collect(Collectors.toList());
			
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sql1", "replace into dn_extend_revise_income(tdate,appid,cha_id,prjid,adsid,adpos_type,ecpm,self_show,gap_val,actnum,addnum,revise_show,revise_revenue) values ");
			paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.cha_id},#{li.prjid},#{li.adsid},#{li.adpos_type},#{li.ecpm},#{li.show_num},#{li.gap_val},#{li.act_num},#{li.add_num},TRUNCATE(#{li.show_num}*(1-#{li.gap_val}*0.01),0), TRUNCATE(#{li.ecpm}*(#{li.show_num}*(1-#{li.gap_val}*0.01))/1000, 2) ) ");
			paramMap.put("sql3", " ");
			paramMap.put("list", collect);
			adMapper.batchExecSql(paramMap);
			
			logger.info("同步syncExtendIncomeRevise 完成...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncExtendIncomeRevise 异常...");
		}
		return false;
	}
	
	@Override
	public boolean syncExtendSubchaTotal(String day) {
		
		logger.info("同步syncExtendSubchaTotal 开始...");
		try {
			String del = "delete FROM dn_extend_subcha_total where tdate = '"+day+"'";
			adMapper.execSql(del);
			
			// 获取自统计的二级子渠道级展示数据
			String sql1 = "select appid,chaid,subcha,adsid,adpos_type,num self_show "+
					"from admsg_subcha_selfshow_"+(day.replace("-", ""))+" where adpos_type not in ('banner')";
			List<Map<String, String>> oneList = admsgMapper.queryListMapOne(sql1);
			
			// 获取广告源对应的ecpm、子渠道等信息
			Map<String, DnAdsidCashTotalVo> ecpmMap = null;
			if(day.equals(DateTime.now().toString("yyyy-MM-dd")))
				ecpmMap = getAdsidCashTotal(DateTime.now().minusDays(1).toString("yyyy-MM-dd"));
			else
				ecpmMap = getAdsidCashTotal(day);
			
			for (Map<String, String> act : oneList) {
				DnAdsidCashTotalVo total = ecpmMap.get(act.get("adsid"));
				if(total != null){
					act.put("ecpm", total.getEcpm());
					act.put("agent", total.getAgent());
					act.put("cha_type_name", total.getCha_type_name());
					act.put("cha_media", total.getCha_media());
				}
			}
			
			// 写入到二级子渠道展示收入表
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sql1", "replace into dn_extend_subcha_income(tdate,appid,cha_id,sub_cha,adsid,adpos_type,agent,ecpm,self_show,revenue,actnum,addnum) values ");
			paramMap.put("sql2", " ('"+day+"',#{li.appid},#{li.chaid},#{li.subcha},#{li.adsid},#{li.adpos_type},#{li.agent},#{li.ecpm},#{li.self_show},(TRUNCATE(#{li.ecpm}*#{li.self_show}/1000,2)),#{li.actnum},#{li.addnum}) ");
			paramMap.put("sql3", " ");
			paramMap.put("list", oneList);
			adMapper.batchExecSql(paramMap);
			
			// 从二级子渠道展示收入表 dn_extend_subcha_income 抽取展示和收入
			adMapper.insertExtendSubchaTotal(day);
			
			// 更新表的新增、活跃
			Map<String, Object> paMap = new HashMap<>();
			
			List<String> array = new ArrayList<String>();
			for (int i = 0; i < 50; i++) {
				String code = "" + i;
				if(i < 10){ code = "0" + i; }
				array.add(code);
			}
			paMap.put("array", array);
			paMap.put("today", day);
			apkMapper.insertPushSubChaActUser(paMap);
			apkMapper.insertPushSubChaAddUser(paMap);
			
			
			// 平台回传的收入/pv 数据除以二级子渠道的dau平均分配到各个二级子渠道
			List<Map<String, Object>> twoList = adMapper.selectSubchaTotalOfBanner(day);
			Map<String, Object> paramMap2 = new HashMap<String, Object>();
			paramMap2.put("sql1", "update dn_extend_subcha_total set pv_banner=#{li.pv},revenue_banner=#{li.income},sum_revenue=(sum_revenue+#{li.income}) where tdate=#{li.tdate} and appid=#{li.appid} and cha_id=#{li.cha_id} and sub_cha=#{li.sub_cha} ");
			paramMap2.put("list", twoList);
			adMapper.batchExecSqlTwo(paramMap2);
			
			logger.info("同步syncExtendSubchaTotal 完成...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncExtendSubchaTotal 异常...");
		}
		return false;
	}
	@Override
	public boolean syncExtendAdtypeRevise(String day) {
		
		logger.info("同步syncExtendAdtypeRevise 开始...");
		try {
			String del = "delete FROM dn_extend_revise_adtype where tdate = '"+day+"'";
			adMapper.execSql(del);
			
			// 从变现收入校准 抽取校准后展示和收入
			adMapper.insertExtendAdtypeRevise(day);
			
			// 抽取友盟新增活跃数据
			String sql = "SELECT * FROM umeng_user_channel_total WHERE tdate = '"+day+"'";
			List<ApkProductStatsVo> proList = queryListBean(sql, ApkProductStatsVo.class);
			
			// 兼容 疯狂自行车ios38079、涂鸦骑士-37998等 产品友盟子渠道缺少apple
			List<String> iosList = Arrays.asList("37998","38110","38088","38079","38104","38048","38050");
			// 这批应用将apple和App Store两个渠道的新增活跃合并为apple一个对象
			Map<String, ApkProductStatsVo> akMap = new HashMap<String, ApkProductStatsVo>();
			
			proList.stream()
					.filter(act -> iosList.contains(act.getAppid()))
					.forEach(act -> {
						// 判断这个appid为空，则直接存入，不为空则在原基础上取和再存入
						ApkProductStatsVo info = akMap.get(act.getAppid());
						if(info == null){
							act.setInstall_channel("apple");
							akMap.put(act.getAppid(), act);
						}else{
							info.setAct_num(info.getAct_num()+act.getAct_num());
							info.setAdd_num(info.getAdd_num()+act.getAdd_num());
							akMap.put(act.getAppid(), info);
						}
					});
			
			if(akMap != null && !akMap.isEmpty()){
				proList.addAll(akMap.values());
			}
			
			Map<String, Object> paramMap2 = new HashMap<String, Object>();
			paramMap2.put("sql1", "update dn_extend_revise_adtype set actnum=#{li.act_num},addnum=#{li.add_num} where tdate=#{li.tdate} and appid=#{li.appid} and cha_id=#{li.install_channel} ");
			paramMap2.put("list", proList);
			adMapper.batchExecSqlTwo(paramMap2);
			
			logger.info("同步syncExtendAdtypeRevise 完成...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncExtendAdtypeRevise 异常...");
		}
		return false;
	}
	@Override
	public boolean syncExtendPrjidIncome(String day) {
		
		logger.info("同步syncExtendPrjidIncome 开始...");
		try {
			String del = "delete FROM dn_extend_prjid_income where tdate = '"+day+"'";
			adMapper.execSql(del);
			
			// 从变现收入校准 抽取展示、收入和新增活跃
			adMapper.insertExtendPrjidIncome(day);
			
			logger.info("同步syncExtendPrjidIncome 完成...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncExtendPrjidIncome 异常...");
		}
		return false;
	}
	@Override
	public int insertSelfPromotionIncome(String tdate) {
		return adMapper.insertSelfPromotionIncome(tdate);
	}


	
	/**
	 * 转换特殊广告位类型为 普通类型
	 * @param name
	 * @return
	 */
	public static String converTypeName(String name){
		Map<String, String> typeMap = new HashMap<>();
		typeMap.put("natBanner", "banner");
		typeMap.put("nativeBanner", "banner");
		typeMap.put("plaqueVideo", "plaque");
		typeMap.put("natPlaque", "plaque");
		typeMap.put("nativePlaque", "plaque");
		typeMap.put("natSplash", "splash");
		typeMap.put("natVideo", "video");
		typeMap.put("yuans", "msg");
		
		// 转换type类型存储
		String adtype = typeMap.get(name);
		if(adtype != null)
			return adtype;
		else
			return name;
	}
	
	/**
	 * 获取GDT凭证
	 * @param member_id
	 * @param secret
	 * @return
	 */
	public static String getGdtTokenStr(String member_id, String secret) {
		// 608070200058 测试ID
		String time = (DateTime.now().minusMinutes(3).getMillis() / 1000L) + "";
		
		String sign = DigestUtils.sha1Hex(member_id + secret + time);
		String token = org.apache.commons.codec.binary.Base64.encodeBase64String((member_id +","+ time +","+ sign).getBytes());
		return token;
	}


	@Override
	public String queryChaByPid(String pid) {
		return adMapper.queryChaByPid(pid);
	}



	@Override
	public List<Map<String, Object>> selectGameEventInfo(Map<String, Object> paramMap) {
		return yxcAdMapper.selectGameEventInfo(paramMap);
	}

    @Override
    public List<Map<String, Object>> selectNewPayInfo(Map<String, Object> param) {
		if (param.get("type").toString().contains("outside")) {
			//海外数据查询hwWbPayInfoMapper
			return hwWbPayInfoMapper.selectNewPayInfo(param);
		}else {
			//国内数据
			List<Map<String, Object>> insideResult = adbOrderMapper.selectADBNewPayInfo(param);
			//查询“海外”tab下的数据
			List<Map<String, Object>> outsideResult = adMapper.selectNewPayInfo(param);
			insideResult.addAll(outsideResult);
			return insideResult;
		}
    }

	@Override
	public Map<String, Object> countNewPayInfo(Map<String, Object> param) {
		if (param.get("type").toString().contains("outside")) {
			//海外数据--调整数据来源0828
			return hwWbPayInfoMapper.countNewPayInfo(param);
		}else {
			Map<String, Object> insideTotalSum = adbOrderMapper.countADBNewPayInfo(param);
			Map<String, Object> outsideTotalSum = adMapper.countNewPayInfo(param);
			//两者数据进行汇总操作
			if (ObjectUtils.isEmpty(insideTotalSum) && ObjectUtils.isEmpty(outsideTotalSum)) {
				//汇总数据都为空
				return null;
			} else if (ObjectUtils.isEmpty(insideTotalSum)) {
				//国内为空
				return outsideTotalSum;
			} else if (ObjectUtils.isEmpty(outsideTotalSum)) {
				//海外为空
				return insideTotalSum;
			} else {
				//都不为空，汇总操作
				Map<String, Object> totalSumResult = new HashMap<>();
				//amount
				BigDecimal insideAmount = BigDecimal.valueOf(insideTotalSum.get("amount") == null ? 0.00 : (Double) insideTotalSum.get("amount"));
				BigDecimal outsideAmount = BigDecimal.valueOf(outsideTotalSum.get("amount") == null ? 0.00 : (Double) outsideTotalSum.get("amount"));
				//payNumber
				BigDecimal insidePayNumber = BigDecimal.valueOf(insideTotalSum.get("payNumber") == null ? 0L : (Long) insideTotalSum.get("payNumber"));
				BigDecimal outsidePayNumber = outsideTotalSum.get("payNumber") == null ? BigDecimal.ZERO : (BigDecimal) outsideTotalSum.get("payNumber");
				//payCount
				BigDecimal insidePayCount = BigDecimal.valueOf(insideTotalSum.get("payCount") == null ? 0L : (Long) insideTotalSum.get("payCount"));
				BigDecimal outsidePayCount = outsideTotalSum.get("payCount") == null ? BigDecimal.ZERO : (BigDecimal) outsideTotalSum.get("payCount");
				//汇总
				BigDecimal totalAmount = insideAmount.add(outsideAmount);
				BigDecimal totalPayNumber = insidePayNumber.add(outsidePayNumber);
				BigDecimal totalPayCount = insidePayCount.add(outsidePayCount);
				//数据封装
				totalSumResult.put("amount",totalAmount);
				totalSumResult.put("payNumber",totalPayNumber);
				totalSumResult.put("payCount",totalPayCount);
				//truncate(sum(xx.amount)/sum(xx.payNumber), 2) arpu
				if (totalPayNumber.compareTo(BigDecimal.ZERO) != 0) {
					BigDecimal arpu = totalAmount.divide(totalPayNumber, 2, RoundingMode.HALF_UP);
					totalSumResult.put("arpu",arpu);
				}
				return totalSumResult;
			}
		}
	}


	@Override
	public List<Map<String, Object>> selectDnPaysAppRevenueTotal(Map<String, Object> paramMap) {
		return adMapper.selectDnPaysAppRevenueTotal(paramMap);
	}
	@Override
	public Map<String, Object> selectDnPaysAppRevenueTotalSum(Map<String, Object> paramMap) {
		return adMapper.selectDnPaysAppRevenueTotalSum(paramMap);
	}



	@Override
	public List<Map<String, Object>> selectLaunchPayInfo(Map<String, Object> param) {
		return adbOrderMapper.selectADBLaunchPayInfo(param);
	}

	@Override
	public Map<String, Object> countLaunchPayInfo(Map<String, Object> param) {
		return adbOrderMapper.countADBLaunchPayInfo(param);
	}

    @Override
    public List<CashUnmatchVo> selectUnmatchAds(CashUnmatchParam param) {
        return adMapper.selectUnmatchAds(param);
    }


	@Async("commonRecacheScheduler")
	@Override
	public void commonRecacheService(Map<String, String> paramMap) {

		String title = "";
		if("NewWjy".equals(paramMap.get("mark")) && "203".equals(paramMap.get("mapid"))){
			title = "深度事件配置";
			paramMap.put("title", title);
		}

		logger.info("{} 刷新缓存 异步执行开始...", paramMap.get("title"));

		int status = 0;
		String respStr = "参数异常";

		try {
			String baseParam = "?mapid="+paramMap.get("mapid");
			if(!BlankUtils.checkBlank(paramMap.get("appid")))
				baseParam += "&appid="+paramMap.get("appid");

			/** 拉取刷新列表，遍历刷新服务地址 */
			String sql = String.format("select url from dn_recache_config where mark = '%s'", paramMap.get("mark"));
			List<String> urlList = adMapper.queryListString(sql);
			for (String url : urlList) {
				String resp = HttpClientUtils.getInstance().httpGetRecache(url + baseParam, null, null);
				if(resp == null || !"ok".equals(resp.trim())){

					status = 0;
					respStr = "刷新失败："+(url + baseParam);

					StringBuilder message = new StringBuilder();
					message.append("报错任务：").append(title).append("\n")
							.append("报错mapid：").append(paramMap.get("mark")+"-"+paramMap.get("mapid")).append("\n")
							.append("报错产品：").append(paramMap.get("appid")).append("\n")
							.append("报错返回：").append("刷新 "+(url + baseParam)+" 时失败，需要管理员进行排查！").append("\n");
					sendFailMsg(message.toString());

					break;
				}else{
					status = 1;
					respStr = "success";
				}
			}

		} catch (Exception e) {
			respStr = e.getMessage();
			logger.error("刷新缓存 异步执行异常...");

			// 异常时通知告警群飞书提示
			StringBuilder message = new StringBuilder();
			message.append("报错任务：").append(paramMap.get("title")).append("\n")
					.append("报错mapid：").append(paramMap.get("mark")+"-"+paramMap.get("mapid")).append("\n")
					.append("报错产品：").append(paramMap.get("appid")).append("\n")
					.append("报错返回：").append(e.getMessage()).append("\n");
			sendFailMsg(message.toString());
		}

		if(true){

			paramMap.put("status", status+"");
			paramMap.put("respStr", respStr);

			String sql = "insert into dn_recache_info(mark,mapid,appid,status,respStr,title,cuser,createtime) value(#{obj.mark},#{obj.mapid},#{obj.appid},#{obj.status},#{obj.respStr},#{obj.title},#{obj.cuser},now() )";
			yyhzMapper.execSqlHandle(sql, paramMap);
			logger.info("刷新缓存 异步执行完成...");
		}

	}
	public void sendFailMsg(String msg) {
		Map<String,String> sendMap = new HashMap<>();
		sendMap.put("msg",msg);
		sendMap.put("uname","caow");
		sendMap.put("robot","robot5");
		HttpClientUtils.getInstance().httpPost("https://edc.vigame.cn:6115/fs/sendMsg", sendMap);
	}


}
