package com.wbgame.service.impl.platform.vivo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.common.constants.PlatformConstants;
import com.wbgame.exception.APISocketTimeoutException;
import com.wbgame.pojo.adv2.PlatformAppInfoVo;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpRequest;
import com.wbgame.utils.platform.BeanUtils;
import com.wbgame.utils.platform.VivoUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Classname VivoAppInfoServiceImpl
 * @Description TODO
 * @Date 2023/11/21 15:44
 */
public class VivoAppInfoServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(VivoAppInfoServiceImpl.class);

    /**
     * vivo平台审核状态
     */
    private static final Map<String, String> VIVO_AUDIT_STATE_MAP = new HashMap<String, String>() {{
        put("0", "0");           //草稿 -> 未提交,页面爬虫 应用未提交状态
        put("1", "3");           //草稿 -> 未提交
        put("2", "5");           //待审核 -> 审核中
        put("3", "19");          //审核通过 -> 审核通过
        put("4", "6");           //审核不通过 ->审核驳回
    }};

    /**
     * vivo平台上架状态
     */
    private static final Map<String, String> VIVO_FINAL_STATE_MAP = new HashMap<String, String>() {{
        put("0", "3"); //待上架 -> 未发布
        put("1", "1"); //已上架 -> 已上线
        put("2", "2"); //已下架 -> 已下架
    }};

    /** vivo审核通过状态*/
    private static final String VIVO_STATE_AUDIT_SUCCESS = "3";

    private static final String VIVO_STATE_AUDIT_FAIL = "4";

    /**
     * 拉取vivo应用信息
     *
     * @param appList
     * @param accountList
     * @return
     */
    public static List<PlatformAppInfoVo> syncVivoAppInfo(List<PlatformAppInfoVo> appList, Map<String, Object> accountMap) {
        String client_id = accountMap.get("vivo_client_id") + "";
        String client_secret = accountMap.get("vivo_client_secret") + "";

        // 按包名去重
        List<PlatformAppInfoVo> queryList = BeanUtils.copyList(appList);
        queryList = queryList
                .stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                        new TreeSet<>(Comparator.comparing(o -> o.getPackagename()))), ArrayList::new));
        for (PlatformAppInfoVo appInfo : queryList) {
            String packageName = appInfo.getPackagename();
            Map<String, Object> params = new HashMap<>();
            params.put("packageName", packageName);
            String timestamp = System.currentTimeMillis() + "";
            String sign = VivoUtils.getVivoSign(client_id, client_secret, timestamp, params);
            JSONObject result = null;
            try {
                result = VivoUtils.getVivoAppInfo(client_id, packageName, timestamp, sign);
//                logger.info("getVivoAppInfo=="+result);
                if ("0".equals(result.getString("code"))) {
                    if ("0".equals(result.getString("subCode"))) {
                        JSONObject data = result.getJSONObject("data");
                        String version_name = data.getString("versionName");
                        String audit_status = data.getString("status");
                        String final_status = data.getString("saleStatus");
                        // 审核不通过原因
                        String unPassReason = data.getString("unPassReason");
                        // 上架类型：1-立即上架，2-定时上架
                        String onlineType = data.getString("onlineType");

                        //先取审核状态 审核通过再取上架状态
                        String state = "";
                        if (!VIVO_STATE_AUDIT_SUCCESS.equals(audit_status)) {
                            state = VIVO_AUDIT_STATE_MAP.get(audit_status);
                            if(!BlankUtils.checkBlank(unPassReason)) {
                                appInfo.setRefuse_reason(unPassReason);
                            }
                        } else {
                            state = VIVO_FINAL_STATE_MAP.get(final_status);
                            // 审核通过、设置了定时上架，但最终上架状态为未发布，为"定时发布"状态
                            if("0".equals(final_status) && "2".equals(onlineType)) {
                                state = "7"; // 定时发布
                            }
                        }
                        appInfo.setVersion(version_name);
                        appInfo.setState(state);
                        appInfo.setSyncTime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
                        appInfo.setSyncState(PlatformConstants.SYNC_APPINFO_STATE_SUCCESS);
                    }else if ("11011".equals(result.getString("subCode"))){
                        //subCode 11011 开发者账号不存在该应用
                        appInfo.setSyncState(PlatformConstants.SYNC_APPINFO_STATE_NOMATCH);
                        appInfo.setSyncTime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
                    }
                }
            } catch (Exception e) {
                logger.error("syncVivoAppInfo appid:" + appInfo.getAppid() + ",result:" + result);
                appInfo.setSyncState(PlatformConstants.SYNC_APPINFO_STATE_UNKOWN_ERROR);
            }

            //最后赋值
            for (PlatformAppInfoVo each:appList){
                String matchKey = each.getPackagename();
                for (PlatformAppInfoVo query:queryList){
                    String key = query.getPackagename();
                    if (matchKey.equals(key)){
                        each.setVersion(query.getVersion());
                        each.setState(query.getState());
                        each.setRefuse_reason(query.getRefuse_reason());
                        each.setSyncState(query.getSyncState());
                        each.setSyncTime(query.getSyncTime());
                    }
                }
            }
        }
        return appList;
    }


    /**
     * 拉取h5_vivo小游戏状态数据
     * @param appList 产品数据
     * @param accountMap 账号信息
     * @return 拉取结果
     */
    public static List<PlatformAppInfoVo> syncH5vivoAppInfo(List<PlatformAppInfoVo> appList, Map<String, Object> accountMap) {
        //获取cookie
        String cookie = accountMap.get("tttoken") + "";

        // 按包名去重
        List<PlatformAppInfoVo> queryList = BeanUtils.copyList(appList);
        queryList = queryList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PlatformAppInfoVo::getPackagename))), ArrayList::new));

        for (PlatformAppInfoVo appInfo : queryList) {
            appInfo.setSyncTime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));

            String packageName = appInfo.getPackagename();
            Map<String, String> params = new HashMap<>();
            params.put("name", packageName);
            params.put("status", "");
            params.put("currentPage", "1");
            params.put("currentPageNum", "1");
            params.put("timestamp",Long.toString(System.currentTimeMillis()));

            Map<String, String> headMap = new HashMap<>();
            headMap.put("cookie",cookie);
            headMap.put("accept-language","zh-CN,zh;q=0.9");
            headMap.put("accept","application/json, text/plain, */*");
            headMap.put("user-agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36");
            JSONObject result = null;
            try {
                result = VivoUtils.getH5VivoAppInfo(params,headMap);
                if (!"0".equals(result.getString("code"))) {
                    logger.error("syncVivoAppInfo appid:" + appInfo.getAppid() + ",result:" + result);
                    String syncState = "10201".equals(result.getString("code")) ? PlatformConstants.SYNC_APPINFO_STATE_TOKEN_ERROR : PlatformConstants.SYNC_APPINFO_STATE_UNKOWN_ERROR;
                    appInfo.setSyncState(syncState);
                    continue;
                }
                JSONObject jsonObject = result.getJSONObject("data");

                JSONArray jsonArray = jsonObject.getJSONArray("data");
                if (jsonArray == null || jsonArray.size() == 0) {
                    //产品未找到
                    appInfo.setSyncState(PlatformConstants.SYNC_APPINFO_STATE_NOMATCH);
                    continue;
                }
                //解析数据
                JSONObject data = jsonArray.getJSONObject(0);
                String version_name = data.getString("versionName");
                String audit_status = data.getString("status");
                String final_status = data.getString("saleStatus");

                //封装商店状态:0-未上架，1-已上架，2-已下架
                String storeSale = data.getString("rpkStoreSale");
                appInfo.setApp_store_sale(storeSale);

                // 上架类型：1-立即上架，2-定时上架
                String onlineType = data.getString("onlineType");

                //先取审核状态 审核通过再取上架状态
                String state = "";
                if (!VIVO_STATE_AUDIT_SUCCESS.equals(audit_status)) {
                    state = VIVO_AUDIT_STATE_MAP.get(audit_status);
                    if(VIVO_STATE_AUDIT_FAIL.equals(audit_status)) {
                        //审核不通过，获取失败原因
                        // 审核不通过原因
                        String unPassReason = VivoUtils.getH5VivoRefuseReason(headMap,data.getString("id"));
                        appInfo.setRefuse_reason(unPassReason);
                    }
                } else {
                    state = VIVO_FINAL_STATE_MAP.get(final_status);
                    // 审核通过、设置了定时上架，但最终上架状态为未发布，为"定时发布"状态
                    if("0".equals(final_status) && "2".equals(onlineType)) {
                        state = "7"; // 定时发布
                    }
                }
                appInfo.setVersion(version_name);
                appInfo.setState(state);
                appInfo.setSyncState(PlatformConstants.SYNC_APPINFO_STATE_SUCCESS);
            } catch (Exception e) {
                logger.error("syncVivoAppInfo appid:" + appInfo.getAppid() + ",result:" + result);
                appInfo.setSyncState(PlatformConstants.SYNC_APPINFO_STATE_UNKOWN_ERROR);
            }
        }
        //最后赋值
        for (PlatformAppInfoVo each:appList){
            String matchKey = each.getPackagename();
            for (PlatformAppInfoVo query:queryList){
                String key = query.getPackagename();
                if (matchKey.equals(key)){
                    each.setVersion(query.getVersion());
                    each.setState(query.getState());
                    each.setApp_store_sale(query.getApp_store_sale());
                    each.setRefuse_reason(query.getRefuse_reason());
                    each.setSyncState(query.getSyncState());
                    each.setSyncTime(query.getSyncTime());
                }
            }
        }
        return appList;
    }

    /**
     * 获取对应产品的商店状态信息
     * @param appList 产品数据
     * @param accountMap 账号配置
     */
    public static List<PlatformAppInfoVo> syncVivoAppStoreSale(List<PlatformAppInfoVo> appList, Map<String, Object> accountMap) {
        //获取cookie
        String cookie = accountMap.get("tttoken") + "";
        //定义产品类型
        //应用
        final String APP_TYPE = "1";
        //游戏
        final String GAME_TYPE = "4";
        //获取所有的应用列表数据
        JSONArray appStoreSaleList = getVivoAppStoreSale(cookie, APP_TYPE);
        //获取所有的游戏列表数据
        JSONArray gameStoreSaleList = getVivoAppStoreSale(cookie, GAME_TYPE);
        if (CollectionUtils.isEmpty(appStoreSaleList) && CollectionUtils.isEmpty(gameStoreSaleList)) {
            //数据爬取未获取成功
            throw new APISocketTimeoutException("api拉取数据异常");
        }
        appStoreSaleList.addAll(gameStoreSaleList);
        Map<String,String> appStoreSaleMap = new HashMap<>();
        for (int i = 0; i < appStoreSaleList.size(); i++) {
            JSONObject data = appStoreSaleList.getJSONObject(i);
            appStoreSaleMap.put(data.getString("packageName"),data.getString("appStoreSale"));
        }
        //封装对应产品的商店状态
        for (PlatformAppInfoVo appInfoVo : appList) {
            String packagename = appInfoVo.getPackagename();
            if (appStoreSaleMap.containsKey(packagename)) {
                String saleStatus = appStoreSaleMap.get(packagename);
                appInfoVo.setApp_store_sale(saleStatus);
            }
        }
        return appList;
    }

    private static JSONArray getVivoAppStoreSale(String cookie, String appType) {
        JSONArray resultArray = new JSONArray();
        try {
            int maxPageNum = 1;
            for (int i = 1; i <= maxPageNum; i++) {
                String appListPageUrl = String.format(VivoUtils.VIVO_APP_LIST_PAGE_URL, appType, i, System.currentTimeMillis());
                Map<String, String> headMap = new HashMap<>();
                headMap.put("cookie",cookie);
                headMap.put("accept-language","zh-CN,zh;q=0.9");
                headMap.put("accept","application/json, text/plain, */*");
                headMap.put("user-agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36");
                String response = HttpRequest.get(appListPageUrl, headMap);
                if (BlankUtils.checkBlank(response)){
                    continue;
                }
                //解析数据
                JSONObject result = JSONObject.parseObject(response);
                if (!"0".equals(result.getString("code"))) {
                    logger.error("syncVivoAppStoreSalefail,result:" + result);
                    continue;
                }
                JSONObject dataObject = result.getJSONObject("data");
                JSONArray jsonArray = dataObject.getJSONArray("data");
                if (jsonArray == null || jsonArray.size() == 0) {
                    //产品未找到
                    continue;
                }
                resultArray.addAll(jsonArray);
                maxPageNum = dataObject.getIntValue("pageCount");
            }
        } catch (Exception e) {
            logger.error("syncVivoAppStoreSale fail,error info:" + e);
        }
        return resultArray;
    }
}
