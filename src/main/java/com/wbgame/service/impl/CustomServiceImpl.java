package com.wbgame.service.impl;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.util.*;
import java.util.Map.Entry;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.master.CompetitorDataMapper;
import com.wbgame.pojo.custom.*;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.wbgame.mapper.master.SysconfigMapper;
import com.wbgame.mapper.slave.CustomMapper;
import com.wbgame.service.CustomService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Service("customService")
public class CustomServiceImpl implements CustomService {

	private static final Logger log = LoggerFactory.getLogger(CustomServiceImpl.class);
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	
	@Autowired
	SysconfigMapper sysconfigMapper;
	@Autowired
	CustomMapper customMapper;
	@Autowired
	private CompetitorDataMapper competitorDataMapper;
	
	
	@Override
	public List<CustomStatsVo> selectCustomStatsLog(Map<String, Object> paramMap) {
		
		DecimalFormat df = new DecimalFormat("#0.00");//设置保留两位小数
		df.setRoundingMode(RoundingMode.FLOOR);//设置不进行四舍五入
		
		List<CustomStatsVo> list = customMapper.selectCustomStatsLog(paramMap);
		for (CustomStatsVo cust : list) {
			cust.setAve_click_num(df.format(cust.getClick_num()*1.00d/cust.getClick_user_num()));
			
			// label为ID时正常是6位数值，加上所处位置则是202894##top
			if(cust.getLabel() != null && cust.getLabel().length() > 5){
				String[] split = cust.getLabel().split("##");
				cust.setLabel(split[0]); // 内容
				cust.setLabel_name(split[1]); // 位置
			}
		}
		return list;
	}

	@Transactional(value = "slaveTransactionManager")
	@Override
	public boolean syncSelectCustomStatsInfo(Map<String, Object> paramMap) {
		
		customMapper.deleteCustomStatsInfo(paramMap);
		
		// 导入统计新增数据和广告数据
		String day = paramMap.get("start_date").toString();
		paramMap.put("table_name", "app_login_"+day);
		paramMap.put("group", "provider");
		List<CustomStatsVo> login_List = selectCustomStatsLog(paramMap);
		customMapper.insertCustomStatsInfo(login_List);
		
		paramMap.put("group", "pid");
		paramMap.put("table_name", "app_custom_"+day);
		List<CustomStatsVo> custom_List = selectCustomStatsLog(paramMap);
		customMapper.insertCustomStatsInfo(custom_List);
		
		
		// 更新app_custom和app_show匹配的show_num
		List<CustomStatsVo> showList = customMapper.selectShowStatsInfo(paramMap);
		for (CustomStatsVo cust : showList) {
			if(cust.getLabel() != null && cust.getLabel().length() > 5){
				String[] split = cust.getLabel().split("##");
				cust.setLabel(split[0]); // 内容
				cust.setLabel_name(split[1]); // 位置
			}
		}
		customMapper.updateCustomStatsInfoForShow(showList);
		
		return true;
	}
	
	@Override
	public List<CustomStatsVo> selectCustomStatsInfo(Map<String, Object> paramMap) {
		return customMapper.selectCustomStatsInfo(paramMap);
	}
	@Override
	public List<CustomKeepUserVo> selectCustomRetained(Map<String, Object> paramMap) {
		return customMapper.selectCustomRetained(paramMap);
	}
	@Override
	public List<CustomKeepUserVo> selectCustomRetainedTwo(Map<String, Object> paramMap) {
		return customMapper.selectCustomRetainedTwo(paramMap);
	}
	@Override
	public List<AppDurationVo> selectAppDurationInfo(Map<String, Object> paramMap) {
		return customMapper.selectAppDurationInfo(paramMap);
	}
	@Override
	public List<Map<String, Object>> selectAppDurationTimes(Map<String, Object> paramMap) {
		return customMapper.selectAppDurationTimes(paramMap);
	}
	@Override
	public List<Map<String, Object>> selectAppDurationGameTimes(Map<String, Object> paramMap) {
		return customMapper.selectAppDurationGameTimes(paramMap);
	}
	@Override
	public List<AppDurationErrorVo> selectAppDurationErrorLog(Map<String, Object> paramMap) {
		return customMapper.selectAppDurationErrorLog(paramMap);
	}

	@Override
	public int insertKeepUserOne(String today) {
		// 插入今日的初始化数据
		return customMapper.insertKeepUserOne(today);
	}
	@Override
	public int insertKeepUserOneB(String today) {
		// 插入今日的初始化数据
		return customMapper.insertKeepUserOneB(today);
	}
	@Transactional(value = "slaveTransactionManager")
	@Override
	public int syncInsertKeepUserList(String today) {
		/*  
		  	插入昨天的信息，读取昨天的1日，2日，...一直到30日的留存appid,pid的组合信息，
			其中包括了各日期的留存值，再根据条件，appid、pid来获取多的留存放入其中，然后更新原有库内容
			按日期分成多个的List，然后执行多个，返回多个日期的appid、pid不同的留存数
		*/
		List<String> array = new ArrayList<>();
		for (int i = 0; i < 30; i++) {
			String code = "" + i;
			if(i < 10){ code = "0" + i; }
			array.add(code);
		}
		List<CustomKeepUserVo> dataList = new ArrayList<CustomKeepUserVo>();
		DateTimeFormatter format = DateTimeFormat.forPattern("yyyyMMdd");
		
		/*
		 * 查询新增用户中创建时间与今日间隔天数的appid,pid,lsn
		 * 通过查询的信息与今日用户表连接查询，匹配出不同appid,pid的留存人数
		 * 将留存信息update到表中
		 * 
		 */
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("today", today);
		Integer[] ins = {1,2,3,4,5,6,7,14,30};
		for (Integer in : ins) {
			String startday = DateTime.parse(today, format).minusDays(in).toString("yyyy-MM-dd");
			map.put("startday", startday);
			map.put("daynum", in);
			map.put("array", array);
			List<CustomKeepUserVo> keep = customMapper.selectKeepUserTwo(map);
			dataList.addAll(keep);
		}
		customMapper.updateKeepUserFour(dataList);
		
		return 1;
	}
	
	@Transactional(value = "slaveTransactionManager")
	@Override
	public int syncInsertKeepUserListB(String today) {
		/*  
		  	插入昨天的信息，读取昨天的1日，2日，...一直到30日的留存appid,pid的组合信息，
			其中包括了各日期的留存值，再根据条件，appid、pid来获取多的留存放入其中，然后更新原有库内容
			按日期分成多个的List，然后执行多个，返回多个日期的appid、pid不同的留存数
		*/
		List<String> array = new ArrayList<>();
		for (int i = 0; i < 30; i++) {
			String code = "" + i;
			if(i < 10){ code = "0" + i; }
			array.add(code);
		}
		List<CustomKeepUserVo> dataList = new ArrayList<CustomKeepUserVo>();
		DateTimeFormatter format = DateTimeFormat.forPattern("yyyyMMdd");
		
		/*
		 * 查询新增用户中创建时间与今日间隔天数的appid,lsn
		 * 通过查询的信息与今日用户表连接查询，匹配出不同appid的留存人数
		 * 将留存信息update到表中
		 * 
		 */
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("today", today);
		Integer[] ins = {1,2,3,4,5,6,7,14,30};
		for (Integer in : ins) {
			String startday = DateTime.parse(today, format).minusDays(in).toString("yyyy-MM-dd");
			map.put("startday", startday);
			map.put("daynum", in);
			map.put("array", array);
			List<CustomKeepUserVo> keep = customMapper.selectKeepUserTwoB(map);
			dataList.addAll(keep);
		}
		customMapper.updateKeepUserFourB(dataList);
		
		return 1;
	}
	
	@Transactional(value = "slaveTransactionManager")
	@Override
	public int syncInsertAddUserList(String today) {
		
		/**
		 * 1.获取到当日用户数据表根据appid,pid,lsn分组后的信息，通过一致性hash分别存入对应的表中
		 * 2.查询新增用户表时通过循环获取，单次获取一个表的appid,pid,add_num，存入redis的hash-key中，使用increment放置add_num
		 * 3.遍历hash-key得到所有的appid,pid以及总计的add_num，存入留存表中
		*/
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("today", today);
		List<CustomAddUserVo> addList = customMapper.selectAddUserLog(paramMap);
		
		Map<String, List<CustomAddUserVo>> arrayMap = new HashMap<String, List<CustomAddUserVo>>();
		for (int i = 0; i < 30; i++) {
			arrayMap.put(i + "", new ArrayList<CustomAddUserVo>());
		}
		
		for (CustomAddUserVo add : addList) { // 通过hashcode分别放置进30张表
			int code = (Math.abs((add.getAppid()+add.getPid()+add.getLsn()).hashCode()) % 30);
			arrayMap.get(code + "").add(add);
		}
		
		for (int p = 0; p < 30; p++) {
			Map<String, Object> valMap = new HashMap<>();
			if(p < 10)
				valMap.put("table_name", "app_custom_userlist_hash0"+p);
			else
				valMap.put("table_name", "app_custom_userlist_hash"+p);
			valMap.put("list", arrayMap.get(p + ""));
			customMapper.insertCustomUserList(valMap);
		}
		addList.clear(); // 清空释放，arrayMap不能释放因为批量操作有延时性
		
		HashOperations<String, Object, Object> opsForHash = redisTemplate.opsForHash();
		String key = "tj_app_custom_userlist_hash"+today;
		for (int i = 0; i < 30; i++) {
			if(i < 10)
				paramMap.put("table_name", "app_custom_userlist_hash0"+i);
			else
				paramMap.put("table_name", "app_custom_userlist_hash"+i);
			
			List<CustomKeepUserVo> userList = customMapper.selectCustomUserList(paramMap);
			for (CustomKeepUserVo add : userList) {
				opsForHash.increment(key, add.getAppid()+"#_&"+add.getPid(), add.getAdd_num());
			}
		}
		
		List<CustomKeepUserVo> upList = new ArrayList<CustomKeepUserVo>();
		Map<Object, Object> entries = opsForHash.entries(key);
		Iterator<Entry<Object, Object>> iterator = entries.entrySet().iterator();
		while(iterator.hasNext()){
			Entry<Object, Object> next = iterator.next();
			
			CustomKeepUserVo ap = new CustomKeepUserVo();
			String[] split = next.getKey().toString().split("#_&");
			ap.setAppid(split[0]);
			ap.setPid(split[1]);
			ap.setTdate(today);
			ap.setAdd_num((Integer)next.getValue());
			upList.add(ap);
		}
		customMapper.updateKeepUserFour(upList);
		redisTemplate.delete(key);
		return 1;
	}
	
	@Transactional(value = "slaveTransactionManager")
	@Override
	public int syncInsertAddUserListB(String today) {
		
		/**
		 * 1.获取到当日用户数据表根据appid,lsn分组后的信息，通过一致性hash分别存入对应的表中
		 * 2.查询新增用户表时通过循环获取，单次获取一个表的appid,add_num，存入redis的hash-key中，使用increment放置add_num
		 * 3.遍历hash-key得到所有的appid以及总计的add_num，存入留存表中
		*/
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("today", today);
		List<CustomAddUserVo> addList = customMapper.selectAddUserLogB(paramMap);
		
		Map<String, List<CustomAddUserVo>> arrayMap = new HashMap<String, List<CustomAddUserVo>>();
		for (int i = 0; i < 30; i++) {
			arrayMap.put(i + "", new ArrayList<CustomAddUserVo>());
		}
		
		for (CustomAddUserVo add : addList) { // 通过hashcode分别放置进30张表
			int code = (Math.abs((add.getAppid()+add.getLsn()).hashCode()) % 30);
			arrayMap.get(code + "").add(add);
		}
		
		for (int p = 0; p < 30; p++) {
			Map<String, Object> valMap = new HashMap<>();
			if(p < 10)
				valMap.put("table_name", "app_custom_userlist_hash_two0"+p);
			else
				valMap.put("table_name", "app_custom_userlist_hash_two"+p);
			valMap.put("list", arrayMap.get(p + ""));
			customMapper.insertCustomUserListB(valMap);
		}
		addList.clear(); // 清空释放，arrayMap不能释放因为批量操作有延时性
		
		HashOperations<String, Object, Object> opsForHash = redisTemplate.opsForHash();
		String key = "tj_app_custom_userlist_hash_two"+today;
		for (int i = 0; i < 30; i++) {
			if(i < 10)
				paramMap.put("table_name", "app_custom_userlist_hash_two0"+i);
			else
				paramMap.put("table_name", "app_custom_userlist_hash_two"+i);
			
			List<CustomKeepUserVo> userList = customMapper.selectCustomUserListB(paramMap);
			for (CustomKeepUserVo add : userList) {
				opsForHash.increment(key, add.getAppid(), add.getAdd_num());
			}
		}
		
		List<CustomKeepUserVo> upList = new ArrayList<CustomKeepUserVo>();
		Map<Object, Object> entries = opsForHash.entries(key);
		Iterator<Entry<Object, Object>> iterator = entries.entrySet().iterator();
		while(iterator.hasNext()){
			Entry<Object, Object> next = iterator.next();
			
			CustomKeepUserVo ap = new CustomKeepUserVo();
			ap.setTdate(today);
			ap.setAppid((String)next.getKey());
			ap.setAdd_num((Integer)next.getValue());
			upList.add(ap);
		}
		customMapper.updateKeepUserFourB(upList);
		redisTemplate.delete(key);
		return 1;
	}
	
	
	@Override
	public List<String> selectCustomChannelList(Map<String, Object> paramMap) {
		return sysconfigMapper.selectCustomChannelList(paramMap);
	}
	

	// partner统计信息
	@Override
	public List<Map<String, Object>> selectPartnerNameList() {
		return customMapper.selectPartnerNameList();
	}
	@Override
	public List<PartnerStatsVo> selectPartnerStatsInfo(Map<String, Object> paramMap) {
		return customMapper.selectPartnerStatsInfo(paramMap);
	}
	@Override
	public List<PartnerStatsVo> selectPartnerKeepInfo(Map<String, Object> paramMap) {
		return customMapper.selectPartnerKeepInfo(paramMap);
	}

	@Transactional(value = "slaveTransactionManager")
	@Override
	public boolean syncSelectPartnerStatsInfo(Map<String, Object> paramMap) {
		// 获取全部表名，一次获取全部表的统计数据，批量导入
		paramMap.put("table_name", "app_partner_user_%");
		List<String> tableList = customMapper.selectPartnerUserTables(paramMap);
		for (int i = 0; i < tableList.size(); i++) {
			tableList.set(i, tableList.get(i).replace("app_partner_user_", ""));
		}
		paramMap.put("list", tableList);
		List<PartnerStatsVo> add_list = customMapper.selectPartnerStatsLog(paramMap);
		
		paramMap.put("table_name", "app_partner_info_total");
		Map<String, Map<String, Object>> infoMap = customMapper.selectCustomStatsInfoMap(paramMap.get("start_date").toString());
		Map<String, Map<String, Object>> rateMap = sysconfigMapper.selectPartnerByCreater(null);
		if(infoMap != null && infoMap.size() > 0){
			// 处理后新增 = (当前总新增数-上一时段保存新增数)*当前展示比例+上一时段保存处理后新增
			for (PartnerStatsVo ps : add_list) {
				Map<String, Object> rate = rateMap.get(ps.getPid());
				int show_rate = 100;
				if(rate != null){
					show_rate = (int)rate.get("show_rate");
				}
				
				Map<String, Object> info = infoMap.get(ps.getAppid()+ps.getPid()+ps.getChannel());
				if(info != null){
					int add_num = (int)info.get("add_num");
					int bus_num = (int)info.get("bus_num");
					ps.setBus_num((ps.getAdd_num() - add_num) * show_rate / 100 + bus_num);
				}else{
					ps.setBus_num(ps.getAdd_num() * show_rate / 100);
				}
			}
			
		}else{ // 当天第一次导入，只考虑本次的展示比例，不用考虑上一时段的展示比例数
			for (PartnerStatsVo ps : add_list) {
				Map<String, Object> rate = rateMap.get(ps.getPid());
				int show_rate = 100;
				if(rate != null){
					show_rate = (int)rate.get("show_rate");
				}
				ps.setBus_num(ps.getAdd_num() * show_rate / 100);
			}
		}
		
		customMapper.deletePartnerStatsInfo(paramMap);
		customMapper.insertPartnerStatsInfo(add_list);
		
		return true;
	}
	@Transactional(value = "slaveTransactionManager")
	@Override
	public boolean syncSelectPartnerStatsInfoKeep(Map<String, Object> paramMap) {
		// 获取全部表名，插入今日的留存初始化数据
		paramMap.put("table_name", "app_partner_user_%");
		List<String> tableList = customMapper.selectPartnerUserTables(paramMap);
		for (int i = 0; i < tableList.size(); i++) {
			tableList.set(i, tableList.get(i).replace("app_partner_user_", ""));
		}
		paramMap.put("list", tableList);
		List<PartnerStatsVo> add_list = customMapper.selectPartnerStatsLog(paramMap);
		customMapper.insertPartnerStatsInfoKeep(add_list);
		
		// 查询出留存数量，修改对应日期的留存数
		List<PartnerStatsVo> keep_list = customMapper.selectPartnerKeepLog(paramMap);
		String today = paramMap.get("start_date").toString();
		for (PartnerStatsVo ps : keep_list) {
			int days = Days.daysBetween(DateTime.parse(ps.getTdate()), DateTime.parse(today)).getDays();
			if(days == 6){
				ps.setKeep7_num(ps.getAdd_num());
			}else if(days == 2){
				ps.setKeep3_num(ps.getAdd_num());
			}else if(days == 1){
				ps.setKeep2_num(ps.getAdd_num());
			}
		}
		customMapper.updatePartnerStatsInfoKeep(keep_list);
		return true;
	}

	@Override
	public Map<String, Map<String, Object>> selectPartnerByCreater(String creater) {
		return sysconfigMapper.selectPartnerByCreater(creater);
	}

	@Transactional(value = "slaveTransactionManager")
	@Override
	public boolean syncCustomStatsTotal(String day) {
		Map<String, Map<String, Object>> appMap = sysconfigMapper.selectAppInfoMap();
		List<CustomTotalStatsVo> ctsList = customMapper.selectCustomTotalStatsLog(day);
		for (CustomTotalStatsVo cts : ctsList) {
			Map<String, Object> map = appMap.get(cts.getAppid());
			if(map != null && map.size() > 0){
				cts.setAppname(map.get("app_name").toString());
			}
		}
		customMapper.insertCustomTotalStatsList(ctsList);
		return true;
	}
	@Transactional(value = "slaveTransactionManager")
	@Override
	public boolean syncCustomStatsTotalTwo(String day) {
		Map<String, Map<String, Object>> appMap = sysconfigMapper.selectAppInfoMap();
		List<CustomTotalStatsVo> ctsList = customMapper.selectCustomTotalStatsLogTwo(day);
		for (CustomTotalStatsVo cts : ctsList) {
			Map<String, Object> map = appMap.get(cts.getAppid());
			if(map != null && map.size() > 0){
				cts.setAppname(map.get("app_name").toString());
			}
		}
		customMapper.insertCustomTotalStatsListTwo(ctsList);
		return true;
	}
	
	@Override
	public List<CustomTotalStatsVo> selectCustomStatsTotalInfo(Map<String, Object> paramMap) {
		return customMapper.selectCustomStatsTotalInfo(paramMap);
	}
	@Override
	public List<CustomTotalStatsVo> selectCustomStatsTotalInfoTwo(Map<String, Object> paramMap) {
		return customMapper.selectCustomStatsTotalInfoTwo(paramMap);
	}
	@Override
	public List<CustomTotalStatsVo> selectCustomStatsTotalInfoThree(Map<String, Object> paramMap) {
		return customMapper.selectCustomStatsTotalInfoThree(paramMap);
	}
	@Override
	public List<CustomKeepUserVo> selectCustomStatsKeepInfo(Map<String, Object> paramMap) {
		return customMapper.selectCustomStatsKeepInfo(paramMap);
	}

	@Override
	public List<Map<String, Object>> queryListMap(String sql) {
		return customMapper.queryListMap(sql);
	}
	@Override
	public Map<String, Map<String, Object>> queryListMapOfKey(String sql) {
		return customMapper.queryListMapOfKey(sql);
	}





	/**
	 * 查询竞品数据分析列表
	 * @param paramMap 查询参数
	 * @return 竞品数据列表
	 */
	@Override
	public List<CompetitorDataVo> selectCompetitorDataList(Map<String, String> paramMap) {
		return competitorDataMapper.selectCompetitorDataList(paramMap);
	}

	/**
	 * 导出竞品数据分析表
	 * @param paramMap 查询参数
	 * @param request HTTP请求
	 * @param response HTTP响应
	 */
	@Override
	public void exportCompetitorData(Map<String, String> paramMap, HttpServletRequest request, HttpServletResponse response) {
		try {
			// 查询数据
			List<CompetitorDataVo> contentList = selectCompetitorDataList(paramMap);

			// 处理表头
			Map<String, String> headerMap = BlankUtils.processExportHeader(paramMap.get("value"));

			// 导出Excel
			String fileName = paramMap.get("export_file_name") + DateTime.now().toString("yyyyMMdd") + ".xlsx";
			ExportExcelUtil.exportXLSX2(response, contentList, headerMap, fileName);


		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 导入竞品数据日志
	 * @param paramMap 参数，包含文件和公司主体
	 * @return 是否导入成功
	 */
	@Override
	public String importCompetitorDataLog(Map<String, Object> paramMap) {
		try {
			String company = (String) paramMap.get("company");
			MultipartFile file = (MultipartFile) paramMap.get("file");
			if (file == null || file.isEmpty()) {
				return "文件不能为空";
			}

			// 先将文件内容读入字节数组
			byte[] fileBytes = file.getBytes();
			// 计算md5
			String md5 = DigestUtils.md5Hex(fileBytes);

			// 校验md5是否已存在
			if (competitorDataMapper.countByMd5(md5) > 0) {
				log.warn("文件已上传，md5: {}", md5);
				return "文件已上传过，MD5值："+md5;
			}

			// 文件写到指定的目录位置
			String fileName = file.getOriginalFilename();
			String fileDir = "/home/<USER>/competitorUtil";
			Files.write(Paths.get(fileDir + "/" + fileName), fileBytes);

			// 执行本地的cmd指令：cd fileDir 执行 java -jar DnDataTransformer.jar -d fileName -o .
			String result = generateData(fileDir, fileName);
			log.info("generateData result：" + result);


			Thread.sleep(1000L);
			// 读取文件内容，判断conPath有效且存在
			Path conPath = Paths.get(fileDir + "/dn.txt");
			if(conPath == null || !conPath.toFile().exists()){
				return "读取文件内容失败，请检查文件";
			}
			byte[] bytes = Files.readAllBytes(conPath);
			String content = new String(bytes, "UTF-8");
			log.info("generateData 解析内容：" + content);

			// 删除掉文件
			Files.deleteIfExists(conPath);
			if(BlankUtils.isJSONArray(content)) {
				List<JSONObject> dataList = JSON.parseArray(content, JSONObject.class);

				if (!dataList.isEmpty()) {
					dataList.forEach(act -> {
						act.put("_company", company);
						act.put("_md5", md5);
					});
					// 批量插入新数据
					competitorDataMapper.insertCompetitorDataBatch(dataList);
					return "导入成功";
				}
			}else{
				return "解析内容不为JSON";
			}

		} catch (Exception e) {
			e.printStackTrace();
			return "操作失败："+e.getMessage();
		}
		return "操作失败";
	}

	@Override
	public boolean deleteCompetitorDataByMd5(String md5) {
		int count = competitorDataMapper.deleteCompetitorDataByMd5(md5);
		return count > 0;
	}

	/**
	 * 处理导出表头
	 * @param value 自定义列数据
	 * @return 表头Map
	 */
	public static Map<String, String> processExportHeader(String value) {
		Map<String, String> headerMap = new LinkedHashMap<String, String>();

		if (!BlankUtils.checkBlank(value)){
			//自定义列数据
			try {
				String[] split = value.split(";");
				for (int i = 0;i<split.length;i++) {
					String[] s = split[i].split(",");
					headerMap.put(s[0],s[1]);
				}
			}catch (Exception e) {
				e.printStackTrace();
				Asserts.fail("自定义列导出异常");
			}
		}

		return headerMap;
	}

	/**
	 * 执行linux指令，返回打印结果
	 * @return
	 */
	public static String generateData(String fileDir, String fileName) {

		/**
		 * 执行本地的cmd指令：cd fileDir 执行 java -jar DnDataTransformer.jar -d fileName -o .
		 * 对于文件名称使用'引号括起，避免特殊字符 */
		String[] cmd = new String[] { "/bin/sh", "-c", "cd /home/<USER>/competitorUtil && /usr/local/jdk24/bin/java -jar DnDataTransformer.jar -d " + fileName + " -o dn.txt "};
//		windows版本
//		String[] cmd = new String[] { "cmd", "/c", "cd /d C:/Program Files/Java24/bin  && java -jar DnDataTransformer.jar -d " + fileDir+"/"+fileName + " -o "+fileDir+"/dn.txt "};
		try {
			Process ps = Runtime.getRuntime().exec(cmd);

			BufferedReader br = new BufferedReader(new InputStreamReader(ps.getInputStream()));
			StringBuffer sb = new StringBuffer();
			String line;
			while ((line = br.readLine()) != null) {
				sb.append(line).append("\n");
			}
			String result = sb.toString();

			return result;
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}


}
