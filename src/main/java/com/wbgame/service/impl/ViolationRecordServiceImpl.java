package com.wbgame.service.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.annotation.EnableMultiSelect;
import com.wbgame.common.Constants;
import com.wbgame.common.constants.RobotConstants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.adb.DnwxBiAdtMapper;
import com.wbgame.mapper.adb.DnwxBiMapper;
import com.wbgame.mapper.adv2.Adv2Mapper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.master.AppInfoByCategoryMapper;
import com.wbgame.mapper.master.PlatformDataMapper;
import com.wbgame.mapper.master.ViolationRecordMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.XiaomiViolationRecord;
import com.wbgame.pojo.XiaomiViolationRecordDTO;
import com.wbgame.pojo.XiaomiViolationRecordVo;
import com.wbgame.pojo.adv2.ExtendAdsidVo;
import com.wbgame.pojo.adv2.PlatformAppInfoVo;
import com.wbgame.service.ViolationRecordService;
import com.wbgame.service.impl.platform.xiaomi.XiaomiDataServiceImpl;
import com.wbgame.service.impl.platform.xiaomi.XiaomiErrorDetail;
import com.wbgame.service.impl.platform.xiaomi.XiaomiErrorDetailParam;
import com.wbgame.utils.*;
import org.apache.commons.compress.utils.Lists;
import org.apache.logging.log4j.util.Strings;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER;

/**
 * <AUTHOR>
 * @Description xiaomi违规记录业务实现层
 * @Date 2024/11/18 16:21
 */
@Service
public class ViolationRecordServiceImpl implements ViolationRecordService {
    Logger logger = LoggerFactory.getLogger(ViolationRecordServiceImpl.class);

    @Autowired
    private AdMapper adMapper;
    @Autowired
    private PlatformDataMapper platformDataMapper;
    @Autowired
    private ViolationRecordMapper violationRecordMapper;
    @Autowired
    private AppInfoByCategoryMapper appInfoByCategoryMapper;

    @Autowired
    private Adv2Mapper adv2Mapper;

    @Autowired
    private DnwxBiMapper dnwxBiMapper;

    @Autowired
    private DnwxBiAdtMapper dnwxBiAdtMapper;

    @Autowired
    private ExecutorService syncXiaomiViolation;

    public final static String HUAWEIMI_CHANNEL_CHAT_ID = "oc_46ce378de12a3ca025d7088e54d52aa5";
    public final static String HUAWEIMI_CHANNEL_CHAT_ID_TEST = "oc_7f7536f3db5a1c82118b989d3c17f200";

    private final static String VIOLATION_COUNT_GROUP_FIELDS = "company,punish_type,appid,appname,channel,online_name,tappid,package_name,temp_id";

    /**绑定模式*/
    public static final Map<String,String> BIDDING_MODE = new HashMap<String,String>(){{
        put("0","否");
        put("1","c2sbidding");
        put("2","s2sbidding");
    }};
    /**区分应用内外*/
    public static final Map<String,String> APP_STATUS = new HashMap<String,String>(){{
        put("0","应用内");
        put("1","应用外");
    }};
    /** 广告使用类型*/
    public static final Map<String,String> AD_USAGE_TYPE = new HashMap<String,String>(){{
        put("splash","开屏");
        put("plaque","插屏");
        put("video","视频");
        put("msg","信息流");
        put("banner","banner");
        put("icon", "icon");
        put("draw","draw信息流");
        put("box", "box");
    }};
    public static final Map<String, String> SKD_AD_TYPE = new HashMap<String, String>(){{
        put("splash","开屏");
        put("natSplash","原生开屏");
        put("banner","普通banner/模板");
        put("natBanner","banner自渲染");
        put("yuans","信息流模板");
        put("msg","信息流自渲染");
        put("plaque","普通插屏/模板");
        put("natPlaque","自渲染插屏");
        put("plaqueVideo","插屏视频");
        put("video","视频");
        put("natVideo","视频自渲染");
        put("icon","icon");
        put("draw","draw信息流");
    }};

    //广告位类型与变现平台
    public static final Map<String, String> ADSENSE_TYPE = new HashMap<String, String>(){{
        put("激励视频","video");
        put("插屏","plaque");
        put("横幅","banner");
        put("原生模板","yuans");
    }};

    /**
     * 查询小米违规数据所有的状态类型
     *
     * @return 查询结果
     */
    @Override
    public Result<List<String>> queryStatus() {
        List<String> statusList = violationRecordMapper.queryStatus();
        return ResultUtils.success(Constants.OK, statusList);
    }

    /**
     * 根据条件分页查询小米违规数据
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    @Override
    public Result<List<XiaomiViolationRecordVo>> queryList(XiaomiViolationRecordDTO dto) {
        if (StringUtils.isEmpty(dto.getGroup())) {
            //维度为空，无数据展示，直接空值返回
            return ResultUtils.success(Constants.OK, Lists.newArrayList(), null, 0L);
        } else {
            //分组字段格式转换成list并封装
            dto.setGroupList(Arrays.asList(dto.getGroup().split(",")));
        }
        //对请求参数进行处理操作
        //封装appid
        if (!StringUtils.isEmpty(dto.getAppCategory()) && StringUtils.isEmpty(dto.getAppid())) {
            List<Integer> categoryList = Arrays.stream(dto.getAppCategory().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            List<String> appidList = appInfoByCategoryMapper.getAppidList(categoryList);
            dto.setAppid(String.join(",", appidList));
        }
        //多选条件字段添加 ''
        DataTransUtils.transToSqls(dto, EnableMultiSelect.class);
        //根据是否统计违规次数看是否查询全部数据在内存中统计操作
        String violationCountFlag = dto.getViolation_count_flag();
        if (!StringUtils.isEmpty(violationCountFlag) && "ok".equals(violationCountFlag)) {
            //需要统计违规次数
            //不分页查询数据
            List<XiaomiViolationRecordVo> resultList = violationRecordMapper.queryGroupList(dto);
            long totalSize = resultList.size();
            // 汇总计算
            XiaomiViolationRecordVo total = violationRecordMapper.queryGroupTotal(dto);
            DataTransUtils.addDataPercentage(total,"ctr");
            //需要统计违规次数
            if (!StringUtils.isEmpty(dto.getViolation_count_flag())) {
                countViolationSummary(resultList,total,dto);
            }
            if (dto.isPageFlag()) {
                //需要分页，进行手动分页操作
                resultList = resultList.stream().skip((long) (dto.getStart() - 1) * dto.getLimit()).limit(dto.getLimit()).collect(Collectors.toList());
            }
            //数据格式转换操作
            transAdConfigFields(resultList);
            return ResultUtils.success(Constants.OK, resultList, total, totalSize);
        } else {
            //
            List<XiaomiViolationRecordVo> resultList;
            XiaomiViolationRecordVo total = null;
            long totalSize;
            if (dto.isPageFlag()) {
                //分页查询数据
                PageHelper.startPage(dto.getStart(), dto.getLimit());
                List<XiaomiViolationRecordVo> queryList = violationRecordMapper.queryGroupList(dto);
                PageResult<XiaomiViolationRecordVo> pageResult = PageResult.page(queryList);
                resultList = pageResult.getList();
                totalSize = pageResult.getTotalSize();
                // 汇总计算
                total = violationRecordMapper.queryGroupTotal(dto);
                DataTransUtils.addDataPercentage(total,"ctr");
            } else {
                //不分页查询数据
                resultList = violationRecordMapper.queryGroupList(dto);
                totalSize = resultList.size();
            }
            //数据格式转换操作
            transAdConfigFields(resultList);
            return ResultUtils.success(Constants.OK, resultList, total, totalSize);
        }
    }

    private void countViolationSummary(List<XiaomiViolationRecordVo> resultList, XiaomiViolationRecordVo total, XiaomiViolationRecordDTO dto) {
        if (CollectionUtils.isEmpty(resultList)) return;
        //需要汇总违规次数统计
        List<XiaomiViolationRecordVo> violationCounts = violationRecordMapper.queryViolationCounts(dto);
        if (CollectionUtils.isEmpty(violationCounts)) return;
        //根据统计对象是否有值获取key并分组操作
        try {
            List<String> groupList = dto.getGroupList();
            List<String> keyList = Arrays.stream(VIOLATION_COUNT_GROUP_FIELDS.split(",")).filter(groupList::contains).collect(Collectors.toList());
            //根据keyList进行分组
            Map<String, List<XiaomiViolationRecordVo>> grouped = violationCounts.stream().collect(Collectors.groupingBy(data -> generateMainKey(keyList, data)));
            Map<String, Map<String, Integer>> collect = grouped.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> sumViolationCount(entry.getValue())));

            //【横幅违规次数】、
            int banner_violation_count = 0;
            // 【原生违规次数】、
            int yuans_violation_count = 0;
            // 【插屏违规次数】、
            int plaque_violation_count = 0;
            // 【视频违规次数】、
            int video_violation_count = 0;
            // 应用违规次数
            int app_violation_count = 0;
            int total_violation_count = 0;
            Map<String, List<XiaomiViolationRecordVo>> recordMap = resultList.stream().collect(Collectors.groupingBy(record -> generateMainKey(keyList, record)));

            for (Map.Entry<String, List<XiaomiViolationRecordVo>> entry : recordMap.entrySet()) {
                String mainKey = entry.getKey();
                if (!collect.containsKey(mainKey)) continue;
                Map<String, Integer> recordCountMap = collect.get(mainKey);
                List<XiaomiViolationRecordVo> recordVos = entry.getValue();
                for (XiaomiViolationRecordVo record : recordVos) {
                    Integer totalViolationCount = recordCountMap.getOrDefault("total_violation_count", 0);
                    record.setTotal_violation_count(totalViolationCount);
                    Integer bannerViolationCount = recordCountMap.getOrDefault("banner_violation_count", 0);
                    record.setBanner_violation_count(bannerViolationCount);
                    Integer yuansViolationCount = recordCountMap.getOrDefault("yuans_violation_count", 0);
                    record.setYuans_violation_count(yuansViolationCount);
                    Integer plaqueViolationCount = recordCountMap.getOrDefault("plaque_violation_count", 0);
                    record.setPlaque_violation_count(plaqueViolationCount);
                    Integer videoViolationCount = recordCountMap.getOrDefault("video_violation_count", 0);
                    record.setVideo_violation_count(videoViolationCount);
                    Integer appViolationCount = recordCountMap.getOrDefault("app_violation_count", 0);
                    record.setApp_violation_count(appViolationCount);

                    //汇总数据
                    total_violation_count += totalViolationCount;
                    banner_violation_count += bannerViolationCount;
                    yuans_violation_count += yuansViolationCount;
                    plaque_violation_count += plaqueViolationCount;
                    video_violation_count += videoViolationCount;
                    app_violation_count += appViolationCount;
                }
            }
            //汇总数据
            if (!ObjectUtils.isEmpty(total)) {
                total.setTotal_violation_count(total_violation_count);
                total.setBanner_violation_count(banner_violation_count);
                total.setYuans_violation_count(yuans_violation_count);
                total.setPlaque_violation_count(plaque_violation_count);
                total.setVideo_violation_count(video_violation_count);
                total.setApp_violation_count(app_violation_count);
            }
        } catch (Exception e) {
            logger.error("计算违规次数异常");
        }
    }

    private Map<String, Integer> sumViolationCount(List<XiaomiViolationRecordVo> violationCounts) {
        Map<String, Integer> countMap = new HashMap<>();
        //【横幅违规次数】、
        int banner_violation_count = 0;
        // 【原生违规次数】、
        int yuans_violation_count = 0;
        // 【插屏违规次数】、
        int plaque_violation_count = 0;
        // 【视频违规次数】、
        int video_violation_count = 0;
        // 应用违规次数
        int app_violation_count = 0;
        for (XiaomiViolationRecordVo recordVo : violationCounts) {
            banner_violation_count += recordVo.getBanner_violation_count();
            yuans_violation_count += recordVo.getYuans_violation_count();
            plaque_violation_count += recordVo.getPlaque_violation_count();
            video_violation_count += recordVo.getVideo_violation_count();
            app_violation_count += recordVo.getApp_violation_count();
        }
        countMap.put("banner_violation_count",banner_violation_count);
        countMap.put("yuans_violation_count",yuans_violation_count);
        countMap.put("plaque_violation_count",plaque_violation_count);
        countMap.put("video_violation_count",video_violation_count);
        countMap.put("app_violation_count",app_violation_count);
        countMap.put("total_violation_count",banner_violation_count+yuans_violation_count+plaque_violation_count+video_violation_count);
        return countMap;
    }

    private String generateMainKey(List<String> keyList, XiaomiViolationRecordVo recordVo) {
        StringBuilder mainKey = new StringBuilder();
        try {
            Class<? extends XiaomiViolationRecordVo> clazz = recordVo.getClass();
            for (String fieldName : keyList) {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                Object obj = field.get(recordVo);
                String value;
                if ("violation_time".equals(fieldName)) {
                    value = obj == null ? "" : obj.toString().substring(0,10);
                } else {
                    value = obj == null ? "" : obj.toString();
                }
                mainKey.append(value).append("-");
            }
        } catch (Exception e) {
            logger.info("获取pojo的key异常");
        }
        return mainKey.toString();
    }

    /**
     * 小米违规库中数据格式转换成页面展示数据
     * @param resultList 小米违规数据
     */
    private void transAdConfigFields(List<XiaomiViolationRecordVo> resultList) {
        if (CollectionUtils.isEmpty(resultList)) return;
        //广告配置相关字段格式转换操作
        for (XiaomiViolationRecordVo recordVo : resultList) {
            //绑定模式
            if (!StringUtils.isEmpty(recordVo.getBidding_mode())) {
                recordVo.setBidding_mode(BIDDING_MODE.get(recordVo.getBidding_mode()));
            }
            //区分应用内外
            if (!StringUtils.isEmpty(recordVo.getIs_out_app())) {
                recordVo.setIs_out_app(APP_STATUS.get(recordVo.getIs_out_app()));
            }
            //sdk广告源类型
            if (!StringUtils.isEmpty(recordVo.getSdk_adtype())) {
                recordVo.setSdk_adtype(SKD_AD_TYPE.get(recordVo.getSdk_adtype()));
            }
            //广告使用类型
            if (!StringUtils.isEmpty(recordVo.getAd_usage_type())) {
                recordVo.setAd_usage_type(AD_USAGE_TYPE.get(recordVo.getAd_usage_type()));
            }
            //ctr封装百分号
            if (!StringUtils.isEmpty(recordVo.getCtr())) {
                recordVo.setCtr(recordVo.getCtr() + "%");
            }
        }
    }

    /**
     * 根据条件获取xiaomi违规记录数据
     *
     * @param startTime   开始时间-必填
     * @param endTime     结束时间-必填
     * @param account     平台账号-非必填
     * @param warningFlag 告警标记-默认false：true满足条件发送告警，false：满足条件也不告警
     * @param autoTriggerFlag 定时任务触发标记
     */
    @Override
    public void pullViolationRecords(String startTime, String endTime, String account, boolean warningFlag,boolean autoTriggerFlag) {

        logger.info("xiaomi违规记录数据拉取条件：startTime：{}，endTime：{}，account：{}，warningFlag：{}", startTime, endTime, account,warningFlag);
        //查询xiaomi平台的token信息sql组装,小米违规记录数据爬取只取爬虫状态开启并且是游戏主体
        String sql = "SELECT account,ttappid,ttparam,cname FROM yyhz_0308.app_channel_config WHERE channel = 'xiaomi' and status = 1 AND company_type = '游戏' ";
        if (!BlankUtils.checkBlank(account)) {
            sql += "and account ='" + account + "' ";
        }
        //查询xiaomi平台的token信息
        List<Map<String, Object>> tokenList = adMapper.queryListMap(sql);

        //根据账号分组操作
        Map<String, Map<String, Object>> accountMap = tokenList.stream().filter(data -> !ObjectUtils.isEmpty(data.get("account")))
                .collect(Collectors.toMap(data -> data.get("account").toString(), data -> data, (k1, k2) -> k1));

        PlatformAppInfoVo vo = new PlatformAppInfoVo();
        vo.setPlatform("xiaomi");
        vo.setTaccount(account);
        //过滤包名为空的的所有xiaomi平台appid
        List<PlatformAppInfoVo> appList = platformDataMapper.getPlatformAppInfoList(vo).stream().filter(t -> !BlankUtils.checkBlank(t.getPackagename())).collect(Collectors.toList());
        Map<String, List<PlatformAppInfoVo>> appMap = appList.stream().collect(Collectors.groupingBy(PlatformAppInfoVo::getTaccount));

        //存储数据集合
        List<XiaomiViolationRecord> storeList = new ArrayList<>();
        StringBuilder builder = new StringBuilder("小米违规记录数据拉取完毕");

        for (Map.Entry<String, Map<String, Object>> entry : accountMap.entrySet()) {
            String xiaomiAccount = entry.getKey();
            //当前账号不存在配置的应用，不进行数据拉取
            if (!appMap.containsKey(xiaomiAccount)) {
                continue;
            }
            List<PlatformAppInfoVo> appInfoVoList = appMap.get(xiaomiAccount);
            //根据tppid分组操作
            Map<String, List<PlatformAppInfoVo>> appGroupMap = appInfoVoList.stream().collect(Collectors.groupingBy(PlatformAppInfoVo::getTappid));
            //获取xiaomi平台请求api的相关token信息
            Map<String, Object> platformInfo = entry.getValue();
            //拉取的数据主体信息封装
            String company = Optional.ofNullable(platformInfo.get("cname")).map(Object::toString).orElse(Strings.EMPTY);
            builder.append(",账号：").append(xiaomiAccount).append(",主体：").append(company);

            //1:应用违规
            List<ArrayList<String>> recordList1 = XiaomiDataServiceImpl.xiaomiViolationRecords(platformInfo, startTime, endTime, XiaomiViolationRecord.PunishType.APP);
            storeList.addAll(transDataToViolationRecord(recordList1, XiaomiViolationRecord.PunishType.APP, appGroupMap,company));
            //2：广告位违规
            List<ArrayList<String>> recordList2 = XiaomiDataServiceImpl.xiaomiViolationRecords(platformInfo, startTime, endTime, XiaomiViolationRecord.PunishType.ADSENSE);
            storeList.addAll(transDataToViolationRecord(recordList2, XiaomiViolationRecord.PunishType.ADSENSE, appGroupMap,company));
            //4:广告位类型违规
            List<ArrayList<String>> recordList3 = XiaomiDataServiceImpl.xiaomiViolationRecords(platformInfo, startTime, endTime, XiaomiViolationRecord.PunishType.ADSENSE_TYPE);
            storeList.addAll(transDataToViolationRecord(recordList3, XiaomiViolationRecord.PunishType.ADSENSE_TYPE, appGroupMap,company));
        }
        if (!CollectionUtils.isEmpty(storeList)) {
            //查询当前需要拉取的时间范围已拉取的违规数据，用于查看状态是否变更
            XiaomiViolationRecordDTO dto = new XiaomiViolationRecordDTO();
            dto.setStartTime(startTime);
            dto.setEndTime(endTime);
            List<XiaomiViolationRecord> previousList = violationRecordMapper.queryList(dto);
            //标记状态变更时间并发送警告
            markStatusChangeWithSendWarning(warningFlag, storeList, previousList);
            //新增数据至xiaomi违规记录表中
            violationRecordMapper.batchDuplicateUpdate(storeList);
            //warningFlag：true需要告警操作，定时任务指定时间需要告警，手动触发不需要告警
            if (warningFlag) {
                //发送今日违规通知
                sendViolationAlertForToday(storeList, previousList);
            }
            //小米违规数据当前最大活跃量的项目ID的模块参数，广告配置等数据，手动触发时开始时间为选择的时间，自动触发只进行当日数据的获取
            syncXiaomiViolation.execute(() -> pullTransformFields(autoTriggerFlag ? endTime : startTime,endTime));
            //pullTransformFields(autoTriggerFlag ? endTime : startTime,endTime);
            //拉取数据成功打印
            logger.info(builder.toString());
        }
    }

    /**
     * 根据数据的 广告位id 获取广告源管理和广告配置数据并封装至小米违规数据中
     *
     * @param storeList 小米违规数据--当次拉取的新数据
     */
    private void wrapAdConfigWithAdsenseCode(List<XiaomiViolationRecord> storeList) {
        //获取所有的广告位id
        List<String> sdkCodeList = storeList.stream().map(XiaomiViolationRecord::getAdsense_id).filter(adsenseId -> !StringUtils.isEmpty(adsenseId)).collect(Collectors.toList());
        //广告位id数据为空，不需要进一步处理操作
        if (CollectionUtils.isEmpty(sdkCodeList)) return;

        //根据sdk-code查询广告源管理数据
        List<ExtendAdsidVo> extendAdsidVos = adv2Mapper.selectExtendAdsidByPubcode(sdkCodeList);
        Map<String, ExtendAdsidVo> adsidVoMap = extendAdsidVos.stream().collect(Collectors.toMap(ExtendAdsidVo::getSdk_code, Function.identity(), (data1, data2) -> data1));

        //根据sdk-code查询广告配置数据
        List<Map<String, Object>> adConfigList = adv2Mapper.selectAdconfig(sdkCodeList, "xmpb_B", 1);
        Map<String, Map<String, Object>> adConfigMap = adConfigList.stream().collect(Collectors.toMap(data -> (String) data.get("sdk_code"), Function.identity(), (data1, data2) -> data1));

        //数据封住
        for (XiaomiViolationRecord violationRecord : storeList) {
            String adsenseId = violationRecord.getAdsense_id();
            if (StringUtils.isEmpty(adsenseId)) {
                continue;
            }
            if (adsidVoMap.containsKey(adsenseId)) {
                //广告源管理相关数据封装
                ExtendAdsidVo adsidVo = adsidVoMap.get(adsenseId);
                violationRecord.setPlatform(adsidVo.getAgent());
                violationRecord.setSdk_adtype(adsidVo.getSdk_adtype());
                violationRecord.setAd_usage_type(adsidVo.getOpen_type());
                violationRecord.setIs_out_app(BlankUtils.getInt(adsidVo.getOut()));
                violationRecord.setBidding_mode(BlankUtils.getInt(adsidVo.getBidding()));
            }
            if (adConfigMap.containsKey(adsenseId)) {
                //封装广告配置数据 strategy
                Map<String, Object> configMap = adConfigMap.get(adsenseId);
                violationRecord.setAd_strategy(configMap.get("strategy") + "");
            }
        }
    }

    @Override
    public List<XiaomiErrorDetail> getXiaomiErrorDetail(XiaomiErrorDetailParam param) {
        if (BlankUtils.isBlank(param.getAppid())) {
            CurrUserVo currUserVo = LOGIN_USER.get();
            param.setAppid(currUserVo.getApp_group());
        }
        if (BlankUtils.isBlank(param.getAppCategory())) {
            CurrUserVo currUserVo = LOGIN_USER.get();
            param.setAppCategory(currUserVo.getApp_category());
        }
        List<XiaomiErrorDetail> xiaomiErrorDetails = adMapper.selectXiaomiErrorDetail(param);
        return xiaomiErrorDetails;
    }

    @Override
    public List<String> getXiaomiErrorConfig() {
        return adMapper.selectXiaomiErrorCodes();
    }

    /**
     * 根据条件获取xiaomi违规记录数据
     *
     * @param startTime   开始时间-必填
     * @param endTime     结束时间-必填
     * @param account     平台账号-非必填
     */
    @Override
    public void pullPlacementErrorDetail(String startTime, String endTime, String account) {

        logger.info("xiaomi拉取广告位错误码详情：startTime：{}，endTime：{}，account：{}", startTime, endTime, account);
        ////查询xiaomi平台的token信息sql组装
        String sql = "SELECT account,ttappid,ttparam,cname FROM yyhz_0308.app_channel_config WHERE channel = 'xiaomi' and status = 1 AND company_type = '游戏' ";
        if (!BlankUtils.checkBlank(account)) {
            sql += "and account ='" + account + "' ";
        }
        //查询xiaomi平台的token信息
        List<Map<String, Object>> tokenList = adMapper.queryListMap(sql);

        //根据账号分组操作
        Map<String, Map<String, Object>> accountMap = tokenList.stream().filter(data -> !ObjectUtils.isEmpty(data.get("account")))
                .collect(Collectors.toMap(data -> data.get("account").toString(), data -> data, (k1, k2) -> k1));

        PlatformAppInfoVo vo = new PlatformAppInfoVo();
        vo.setPlatform("xiaomi");
        vo.setTaccount(account);
        //过滤包名为空的的所有xiaomi平台appid
        List<PlatformAppInfoVo> appList = platformDataMapper.getPlatformAppInfoList(vo).stream().filter(t -> !BlankUtils.checkBlank(t.getPackagename())).collect(Collectors.toList());
        Map<String, List<PlatformAppInfoVo>> appMap = appList.stream().collect(Collectors.groupingBy(PlatformAppInfoVo::getTaccount));

        // 获取主体
        String sql1 = "select CONCAT(channel,'_',account)  as mapkey,cname from app_channel_config  ORDER BY createtime asc";
        Map<String, Map<String, Object>> cMap = adMapper.queryListMapOfKey(sql1);

        Map<String, PlatformAppInfoVo> tappidMap = appList.stream()
                .peek(po -> {
                    String key = po.getPlatform()+"_"+po.getTaccount();
                    String cname = cMap.get(key) != null ? String.valueOf(cMap.get(key).get("cname")) : "";
                    po.setCname(cname);
                })
                .collect(Collectors.toMap(PlatformAppInfoVo::getTappid, v -> v, (key1, key2) -> key2));

        //存储数据集合
        List<XiaomiViolationRecord> storeList = new ArrayList<>();
        StringBuilder builder = new StringBuilder("小米违规记录数据拉取完毕");

        for (Map.Entry<String, Map<String, Object>> entry : accountMap.entrySet()) {
            String xiaomiAccount = entry.getKey();
            //当前账号不存在配置的应用，不进行数据拉取
            if (!appMap.containsKey(xiaomiAccount)) {
                continue;
            }
            List<PlatformAppInfoVo> appInfoVoList = appMap.get(xiaomiAccount);
            //根据tppid分组操作
            Map<String, List<PlatformAppInfoVo>> appGroupMap = appInfoVoList.stream().collect(Collectors.groupingBy(PlatformAppInfoVo::getTappid));
            //获取xiaomi平台请求api的相关token信息
            Map<String, Object> platformInfo = entry.getValue();
            //拉取的数据主体信息封装
            String company = Optional.ofNullable(platformInfo.get("cname")).map(Object::toString).orElse(Strings.EMPTY);
            builder.append(",账号：").append(xiaomiAccount).append(",主体：").append(company);

            List<XiaomiErrorDetail> xiaomiErrorDetails = XiaomiDataServiceImpl.xiaomiPlacementErrorDetail(platformInfo, startTime, endTime, tappidMap);
            if (!CollectionUtils.isEmpty(xiaomiErrorDetails)) {
                adMapper.deleteXiaomiErrorDetail(startTime, xiaomiAccount);
                adMapper.insertXiaomiErrorDetail(xiaomiErrorDetails);
            }
        }
    }

    /**
     * 存在新增数据时发送当日违规通知
     *
     * @param storeList    拉取的最新数据
     * @param previousList 上一次的数据
     */
    private void sendViolationAlertForToday(List<XiaomiViolationRecord> storeList, List<XiaomiViolationRecord> previousList) {
        //当前时间
        String today = DateUtil.getDate();
        //获取当日违规时间的数据
        List<XiaomiViolationRecord> latestList = storeList.stream().filter(data -> today.equals(DateUtil.dateToStr(data.getViolation_time(), DateUtil.DATE_A))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(latestList)) {
            //不存在当日数据，无需告警，直接返回
            return;
        }
        //旧数据的违规时间为今日数据
        List<XiaomiViolationRecord> oldList = previousList.stream().filter(data -> today.equals(DateUtil.dateToStr(data.getViolation_time(), DateUtil.DATE_A))).collect(Collectors.toList());
        //总的违规数据比上一次拉取的数据多，存在新增，需要告警，不存在新增，不需要告警
        if (latestList.size() <= oldList.size()) {
            return;
        }
        //告警模板
        String temp = ("\n" + "告警标题：%s\n" + "违规时间：%s\n" + "处罚类型：%s\n" + "违规数量：%s\n" + "处罚类型：%s\n" + "违规数量：%s\n" + "处罚类型：%s\n" + "违规数量：%s\n");
        //数量存在增加的情况，需要发送告警
        //最新数据分组并计数
        Map<String, Long> collect = latestList.stream().collect(Collectors.groupingBy(XiaomiViolationRecord::getPunish_type, Collectors.counting()));
        //广告位违规
        Long latestAdsense = collect.getOrDefault(XiaomiViolationRecord.PunishType.ADSENSE.getName(), 0L);
        //广告位类型违规
        Long latestAdsenseType = collect.getOrDefault(XiaomiViolationRecord.PunishType.ADSENSE_TYPE.getName(), 0L);
        //应用违规
        Long latestApp = collect.getOrDefault(XiaomiViolationRecord.PunishType.APP.getName(), 0L);
        //告警数据封装
        String format = String.format(temp, "当日违规通知", today, "广告位违规", latestAdsense, "广告位类型违规", latestAdsenseType, "应用违规", latestApp);
        FeishuUtils.sendMsgCardToGroupRobot(HUAWEIMI_CHANNEL_CHAT_ID, RobotConstants.MESSAGE_ROBOT,format, "当日违规通知", "green", "linps,liangp,linxm");
        //FeishuUtils.sendMsgCardToGroupRobot(HUAWEIMI_CHANNEL_CHAT_ID_TEST, format, "当日违规通知", "green", "xuyx");
    }

    /**
     * 标记状态变更并发送变更通知
     *
     * @param warningFlag  是否通知标记
     * @param storeList    拉取的数据
     * @param previousList 上一次拉取的数据
     */
    private void markStatusChangeWithSendWarning(boolean warningFlag, List<XiaomiViolationRecord> storeList, List<XiaomiViolationRecord> previousList) {
        if (CollectionUtils.isEmpty(previousList) || CollectionUtils.isEmpty(storeList)) {
            return;
        }
        //根据主键字段作为key分组操作
        Map<String, XiaomiViolationRecord> oldRecordMap = previousList.stream().collect(Collectors.toMap(data -> data.getAppid() + data.getChannel() + data.getCompany() + data.getPunish_type() + DateUtil.dateToStr(data.getViolation_time(), DateUtil.DATE_B), data -> data));

        Date nowDate = new Date();
        String nowDateStr = DateUtil.dateToStr(nowDate, DateUtil.DATE_B);
        //应用违规类型告警模板
        String appTemp = ("\n" + "告警标题：%s\n" + "违规时间：%s\n" + "主体：%s\n" + "处罚类型：%s\n" + "违规应用：%s\n" + "状态：%s\n" + "状态变更时间：%s\n");
        //广告位类型违规模板
        String adsenseTemp = ("\n" + "告警标题：%s\n" + "违规时间：%s\n" + "主体：%s\n" + "处罚类型：%s\n" + "广告位类型：%s\n" + "违规应用：%s\n" + "状态：%s\n" + "状态变更时间：%s\n");
        for (XiaomiViolationRecord record : storeList) {
            String violationTime = DateUtil.dateToStr(record.getViolation_time(), DateUtil.DATE_B);
            String company = record.getCompany();
            String punishType = record.getPunish_type();
            //封装状态变更时间
            String mainKey = record.getAppid() + record.getChannel() + company + punishType + violationTime;
            //对 广告位违规 状态变更不需要告警处理，直接过滤
            boolean adsenseFlag = XiaomiViolationRecord.PunishType.ADSENSE.getName().equals(punishType);
            if (oldRecordMap.containsKey(mainKey)) {
                //查看状态是否变更
                XiaomiViolationRecord oldRecord = oldRecordMap.get(mainKey);
                String status = StringUtils.isEmpty(record.getStatus()) ? "" : record.getStatus();
                String oldStatus = StringUtils.isEmpty(oldRecord.getStatus()) ? "" : oldRecord.getStatus();
                if (status.equals(oldStatus)) {
                    //将旧的更新时间重新封装回新数据中
                    record.setLast_modified_time(oldRecord.getLast_modified_time());
                    continue;
                }
                //封装变更时间
                record.setLast_modified_time(nowDate);
                if (adsenseFlag || !warningFlag) {
                    //不发送告警
                    continue;
                }
                //发送状态变更告警至告警群
                String format;
                if (XiaomiViolationRecord.PunishType.ADSENSE_TYPE.getName().equals(punishType)) {
                    //广告位类型违规模板告警
                    format = String.format(adsenseTemp, "违规状态变更通知", violationTime, company, punishType, record.getAdsense_type() ,record.getOnline_name() + "-" + record.getTappid(), status, nowDateStr);
                } else {
                    //通用模板告警：应用违规类型告警
                    format = String.format(appTemp, "违规状态变更通知", violationTime, company, punishType, record.getOnline_name() + "-" + record.getTappid(), status, nowDateStr);
                }
                //"linps,liangp,linxm"
                FeishuUtils.sendMsgCardToGroupRobot(HUAWEIMI_CHANNEL_CHAT_ID, RobotConstants.MESSAGE_ROBOT,format, "违规状态变更通知", "blue", "linps,liangp,linxm");
                //FeishuUtils.sendMsgCardToGroupRobot(HUAWEIMI_CHANNEL_CHAT_ID_TEST, format, "违规状态变更通知", "blue", "xuyx");
            }
        }
    }

    /**
     * 构建小米违规数据
     *
     * @param recordList  平台excel解析的违规数据
     * @param punishType  处罚类型
     * @param appGroupMap 平台应用信息
     * @param company 开发者主体
     * @return 构建结果
     */
    private List<XiaomiViolationRecord> transDataToViolationRecord(List<ArrayList<String>> recordList, XiaomiViolationRecord.PunishType punishType, Map<String, List<PlatformAppInfoVo>> appGroupMap,String company) {
        List<XiaomiViolationRecord> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(recordList) || recordList.size() == 1) {
            //数据为空或者只存在标题行数据
            return resultList;
        }
        //解析 recordList
        for (int i = 1; i < recordList.size(); i++) {
            ArrayList<String> excel = recordList.get(i);
            //解析每一行数据
            XiaomiViolationRecord record = new XiaomiViolationRecord();
            //appId
            String tappid = excel.get(2);
            record.setTappid(tappid);
            //违规时间 -- 这个字段需要留意是否会有问题
            String violationTime = excel.get(8);
            // 封装应用名称，子渠道等数据
            //可能一个tappid对应了多个appid，所以需要根据绑定时间取值appid和channel数据
            PlatformAppInfoVo appInfo = chooseFirstAppInfo(appGroupMap, violationTime, tappid);
            //无配置的appid，直接过滤
            if (ObjectUtils.isEmpty(appInfo)) {
                continue;
            }
            //赋值appid，子渠道，应用名
            record.setAppid(appInfo.getAppid());
            record.setChannel(appInfo.getChannel());
            record.setAppname(appInfo.getAppname());
            //处罚类型
            record.setPunish_type(punishType.getName());
            //上线名称--平台上的应用名称
            record.setOnline_name(excel.get(1));
            //包名
            record.setPackage_name(excel.get(3));
            //广告位名称
            record.setAdsense_name(excel.get(4));
            //广告位id
            record.setAdsense_id(excel.get(5));
            //广告位类型名称
            record.setAdsense_type(excel.get(6));
            //违规类型
            record.setViolation_type(excel.get(7));
            //违规原因
            record.setViolation_reason(excel.get(9));
            //违规时间
            record.setViolation_time(DateUtil.strToDate(violationTime, DateUtil.DATE_B));
            //处理截止时间
            record.setDeadline("0".equals(excel.get(10)) ? null : DateUtil.strToDate(excel.get(10), DateUtil.DATE_B));
            //状态
            record.setStatus(excel.get(11));
            //封装开发者主体
            record.setCompany(company);
            resultList.add(record);
        }
        return resultList;
    }

    /**
     * 存在一个tappid对应多个appid时，根据绑定时间获取相近的appid
     *
     * @param appGroupMap   appid数据
     * @param violationTime 违规时间
     * @param tappid        平台appid
     * @return 时间相近的应用信息
     */
    private static PlatformAppInfoVo chooseFirstAppInfo(Map<String, List<PlatformAppInfoVo>> appGroupMap, String violationTime, String tappid) {
        if (!appGroupMap.containsKey(tappid)) {
            return null;
        }
        //violationTime
        List<PlatformAppInfoVo> appInfoVoList = appGroupMap.get(tappid);
        if (appInfoVoList.size() == 1) {
            return appInfoVoList.get(0);
        } else {
            if (StringUtils.isEmpty(violationTime) || violationTime.length() < 10) {
                return appInfoVoList.get(0);
            }
            String substring = violationTime.substring(0, 10);
            DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
            Optional<PlatformAppInfoVo> optional = appInfoVoList.stream().sorted(Comparator.comparing(PlatformAppInfoVo::getBindEndTime)).filter(data -> !DateTime.parse(data.getBindEndTime(), format).isBefore(DateTime.parse(substring, format))).findFirst();
            return optional.orElseGet(() -> appInfoVoList.get(0));
        }
    }


    /**
     * 拉取 违规产品当前最大活跃量的项目ID的模块。
     *
     * @param startTime
     * @param endTime
     */
    @Override
    public void pullTransformFields(String startTime, String endTime) {
        logger.info("小米违规数据当前最大活跃量的项目ID的模块参数：startTime:{},endTime:{}", startTime, endTime);
        //查询当前时间的违规数据
        XiaomiViolationRecordDTO dto = new XiaomiViolationRecordDTO();
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        List<XiaomiViolationRecord> recordList = violationRecordMapper.queryList(dto);
        if (CollectionUtils.isEmpty(recordList)) {
            logger.info("小米违规数据当前时间段为空");
            return;
        }
        // 获取 广告源管理，广告配置 表中的相关字段赋值至小米违规数据表中
        wrapAdConfigWithAdsenseCode(recordList);

        // 拉取 临时模块，并增加备注：违规产品当前最大活跃量的项目ID的模块。
        Map<String, List<XiaomiViolationRecord>> collect = recordList.stream().collect(Collectors.groupingBy(data -> DateUtil.dateToStr(data.getViolation_time(), DateUtil.DATE_A)));

        for (Map.Entry<String, List<XiaomiViolationRecord>> entry : collect.entrySet()) {
            String tdate = entry.getKey();
            List<XiaomiViolationRecord> value = entry.getValue();
            Set<String> appidSet = new HashSet<>();
            Set<String> channelSet = new HashSet<>();
            for (XiaomiViolationRecord record : value) {
                appidSet.add(record.getAppid());
                channelSet.add(record.getChannel());
            }
            Map<String, Map<String, String>> tempInfoMap = getMaxNumProjectTempId(tdate, appidSet, channelSet);
            for (XiaomiViolationRecord record : value) {
                String key = record.getAppid() + "_" + record.getChannel();
                if (!tempInfoMap.containsKey(key)) {
                    continue;
                }
                Map<String, String> map = tempInfoMap.get(key);
                record.setTemp_name(map.get("temp_name"));
                record.setTemp_id(map.get("temp_id"));
            }
            // 查询变现平台最近七天的收入数据
            calculateLastWeekRevenue(tdate,appidSet,channelSet,value);
        }
        //更新数据
        violationRecordMapper.batchInsert(recordList);
        logger.info("小米违规数据当前最大活跃量的项目ID的模块完成");
    }

    /**
     * 获取变现平台最近七天的收入数据,近七天展示、近七天点击等数据
     *
     * @param tdate      时间：2024-12-12
     * @param appidSet   产品id集合
     * @param channelSet 子渠道集合
     * @param recordList 小米违规记录数据
     */
    private void calculateLastWeekRevenue(String tdate, Set<String> appidSet, Set<String> channelSet, List<XiaomiViolationRecord> recordList) {
        if (CollectionUtils.isEmpty(recordList)) return;

        DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd");
        DateTime dateTime = DateTime.parse(tdate, formatter);
        //获取最近一周的起始日期
        String startDate = dateTime.minusWeeks(1).toString(formatter);
        String endDate = dateTime.minusDays(1).toString(formatter);
        //封装查询参数
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("groups", Arrays.asList("dnappid", "cha_id", "placement_id", "placement_type","sdk_code"));
        paramMap.put("start_date", startDate);
        paramMap.put("end_date", endDate);
        paramMap.put("appid", DataTransUtils.transToSql(String.join(",", appidSet)));
        paramMap.put("cha_id", DataTransUtils.transToSql(String.join(",", channelSet)));
        //变现平台明细数据查询
        List<Map<String, Object>> dnChaCashTotal = dnwxBiAdtMapper.selectDnChaCashTotal(paramMap);
        //获取小米违规记录数据的广告位id
        Set<String> adsenseIdSet = recordList.stream().map(XiaomiViolationRecord::getAdsense_id).filter(adsenseId -> !StringUtils.isEmpty(adsenseId)).collect(Collectors.toSet());
        //获取广告位类型值
        Collection<String> adsenseType = ADSENSE_TYPE.values();
        Map<String, BigDecimal> revenueMap = new HashMap<>();
        Map<String, BigDecimal> showMap = new HashMap<>();
        Map<String, BigDecimal> clickMap = new HashMap<>();
        //根据维度汇总数据
        for (Map<String, Object> dataMap : dnChaCashTotal) {
            String dnappid = dataMap.get("dnappid").toString();
            String cha_id = dataMap.get("cha_id").toString();
            String appKey = dnappid + "_" + cha_id;
            // 获取展示数-pv
            BigDecimal showNum = BigDecimal.valueOf((Long) dataMap.get("pv"));
            updateRevenueMap(showMap, appKey, showNum);
            //点击数-click
            BigDecimal clickNum = BigDecimal.valueOf((Long) dataMap.get("click"));
            updateRevenueMap(clickMap, appKey, clickNum);

            //封装产品的收入数据
            BigDecimal revenueNum = (BigDecimal) dataMap.get("revenue");
            updateRevenueMap(revenueMap, appKey, revenueNum);
            // 有值才做为key进行存储汇总收入数据
            //广告位类型
            Object placementTypeObj = dataMap.get("placement_type");
            if (!ObjectUtils.isEmpty(placementTypeObj) && adsenseType.contains(placementTypeObj.toString())) {
                String placementType = placementTypeObj.toString();
                String adTypeKey = appKey + "_" + placementType;
                updateRevenueMap(revenueMap, adTypeKey, revenueNum);
                //展示数
                updateRevenueMap(showMap, adTypeKey, showNum);
                //点击数
                updateRevenueMap(clickMap, adTypeKey, clickNum);
            }
            //sdk_code
            Object sdkCodeObj = dataMap.get("sdk_code");
            if (!ObjectUtils.isEmpty(sdkCodeObj) && adsenseIdSet.contains(sdkCodeObj.toString())) {
                String sdkCode = sdkCodeObj.toString();
                String adKey = appKey + "_" + sdkCode;
                updateRevenueMap(revenueMap, adKey, revenueNum);
                //展示数
                updateRevenueMap(showMap, adKey, showNum);
                //点击数
                updateRevenueMap(clickMap, adKey, clickNum);
            }
        }
        //收入数据封装
        for (XiaomiViolationRecord record : recordList) {
            String appKey = record.getAppid() + "_" + record.getChannel();
            String punishType = record.getPunish_type();
            if (XiaomiViolationRecord.PunishType.APP.getName().equals(punishType)) {
                //应用违规
                if (revenueMap.containsKey(appKey)) {
                    record.setLast_week_revenue(revenueMap.get(appKey).toString());
                }
                //展示数封装
                if (showMap.containsKey(appKey)) {
                    record.setLast_week_show(showMap.get(appKey).toString());
                }
                //点击数封装
                if (clickMap.containsKey(appKey)) {
                    record.setLast_week_click(clickMap.get(appKey).toString());
                }
            } else if (XiaomiViolationRecord.PunishType.ADSENSE_TYPE.getName().equals(punishType)) {
                //广告位类型违规
                String adTypeKey = appKey + "_" + ADSENSE_TYPE.get(record.getAdsense_type());
                if (revenueMap.containsKey(adTypeKey)) {
                    record.setLast_week_revenue(revenueMap.get(adTypeKey).toString());
                }
                //展示数封装
                if (showMap.containsKey(adTypeKey)) {
                    record.setLast_week_show(showMap.get(adTypeKey).toString());
                }
                //点击数封装
                if (clickMap.containsKey(adTypeKey)) {
                    record.setLast_week_click(clickMap.get(adTypeKey).toString());
                }
            } else {
                //广告位违规
                String adKey = appKey + "_" + record.getAdsense_id();
                if (revenueMap.containsKey(adKey)) {
                    record.setLast_week_revenue(revenueMap.get(adKey).toString());
                }
                //展示数封装
                if (showMap.containsKey(adKey)) {
                    record.setLast_week_show(showMap.get(adKey).toString());
                }
                //点击数封装
                if (clickMap.containsKey(adKey)) {
                    record.setLast_week_click(clickMap.get(adKey).toString());
                }
            }
        }
    }

    /**
     * 汇总收入数据
     *
     * @param revenueMap
     * @param appKey
     * @param revenueNum
     */
    private static void updateRevenueMap(Map<String, BigDecimal> revenueMap, String appKey, BigDecimal revenueNum) {
        if (revenueMap.containsKey(appKey)) {
            BigDecimal bigDecimal = revenueMap.get(appKey);
            revenueMap.put(appKey, bigDecimal.add(revenueNum));
        } else {
            revenueMap.put(appKey, revenueNum);
        }
    }

    /**
     * 根据时间，渠道获取最大覆盖量项目的temp_id,temp_name，存在多个使用 ”|“ 隔开
     *
     * @param tdate    时间，格式：2024-10-10
     * @param appids   应用id
     * @param channels 渠道
     * @return 查询处理结果
     */
    public Map<String, Map<String, String>> getMaxNumProjectTempId(String tdate, Set<String> appids, Set<String> channels) {
        Map<String, Map<String, String>> maxNumTempIdMap = new HashMap<>();
        String templateSql = "SELECT extend.*,project.temp_id,tempmodule.tempName FROM dn_extend_sdk_relation extend LEFT JOIN wbgui_project_sdk_relation project ON extend.prjid = project.project_id AND extend.sdk_name = project.sdk_name AND extend.ver = project.sdk_version AND extend.local_ver = project.version LEFT JOIN wbgui_tempmodule tempmodule ON project.temp_id = tempmodule.singleid WHERE extend.tdate = '%s' AND extend.appid in ('%s') AND extend.cha_id in ('%s')";
        List<Map<String, Object>> mapList = dnwxBiMapper.queryListMap(String.format(templateSql, tdate, String.join("','", appids), String.join("','", channels)));
        if (CollectionUtils.isEmpty(mapList)) return maxNumTempIdMap;
        //根据 appid，channel进行分组操作
        Map<String, List<Map<String, Object>>> collect = mapList.stream().collect(Collectors.groupingBy(data -> data.get("appid") + "_" + data.get("cha_id")));

        for (Map.Entry<String, List<Map<String, Object>>> entry : collect.entrySet()) {
            String key = entry.getKey();
            List<Map<String, Object>> values = entry.getValue();
            //获取最大的 覆盖量num
            Integer maxNum = values.stream().map(data -> ObjectUtils.isEmpty(data.get("num")) ? 0 : (Integer) data.get("num")).max(Integer::compareTo).orElse(0);
            //获取最大值的temp_id;
            LinkedHashSet<String> tempIdSet = new LinkedHashSet<>();
            LinkedHashSet<String> tempNameSet = new LinkedHashSet<>();
            for (Map<String, Object> value : values) {
                Object numObj = value.get("num");
                if (ObjectUtils.isEmpty(numObj)) continue;
                //对比最大覆盖量，获取覆盖量最大的temp_id
                String tempId = value.getOrDefault("temp_id", Strings.EMPTY).toString();
                String tempName = value.getOrDefault("tempName", Strings.EMPTY).toString();
                if (!StringUtils.isEmpty(tempId) && maxNum.equals(numObj)) {
                    tempIdSet.add(tempId);
                    tempNameSet.add(tempName);
                }
            }
            //存储 功能标识
            if (!CollectionUtils.isEmpty(tempIdSet)) {
                Map<String, String> map = new HashMap<>();
                map.put("temp_id", String.join("|", tempIdSet));
                map.put("temp_name", String.join("|", tempNameSet));
                maxNumTempIdMap.put(key, map);
            }
        }
        return maxNumTempIdMap;
    }
}
