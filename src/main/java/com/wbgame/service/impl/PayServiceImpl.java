package com.wbgame.service.impl;

import com.wbgame.mapper.master.SomeMapper;
import com.wbgame.pojo.PayInfoVo;
import com.wbgame.service.PayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 支付实现类
 *
 * <AUTHOR>
 */
@Service("payService")
public class PayServiceImpl implements PayService{

    @Autowired
    SomeMapper someMapper;

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryOppoPay(Map<String, String> parmMap) {
        try {


            String begin = parmMap.get("begin");
            String end  = parmMap.get("end");
            String prjId = parmMap.get("prjId");
            String app = parmMap.get("app");

            StringBuffer 	sql = new StringBuffer("  select date_format(a.creattime,'%Y-%m-%d') as creattime,a.prjid,sum(a.price) as amount,count(*) as payCount,count(distinct a.imsi) as payNumber, " +
                    "truncate(sum(a.price)/100/count(distinct a.imsi),2) as arpu,cc.p_appNm as app from oppo_pay_info a,(SELECT yy.p_appNm,yy.p_pri,yy.p_appId from tb_app_info yy where yy.p_pri in (select DISTINCT prjid from oppo_pay_info) ) cc  where   cc.p_pri=a.prjid    ");


            if(begin.equals(end)){
                sql.append("   and  date_format(a.creattime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.creattime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.prjid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.prjid,date_format(a.creattime,'%Y-%m-%d') order by a.creattime desc ");
            List<PayInfoVo> list = someMapper.queryOppoPay(sql.toString());
            for(PayInfoVo pay : list){
                pay.setPayWay("oppo");
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryOppoPayNew(Map<String, String> parmMap) {
        try {


            String begin = parmMap.get("begin");
            String end  = parmMap.get("end");
            String prjId = parmMap.get("prjId");
            String app = parmMap.get("app");

            StringBuffer 	sql = new StringBuffer("  select date_format(a.creattime,'%Y-%m-%d') as creattime,a.prjid,sum(a.price) as amount,count(*) as payCount,count(distinct a.imsi) as payNumber, " +
                    "truncate(sum(a.price)/100/count(distinct a.imsi),2) as arpu,cc.p_appNm as app from oppo_pay_info a,(SELECT yy.pjId as p_pri,yy.gameName as p_appNm from dnwx_client.wbgui_formconfig yy where yy.pjId in (select DISTINCT prjid from oppo_pay_info) ) cc  where   cc.p_pri=a.prjid    ");


            if(begin.equals(end)){
                sql.append("   and  date_format(a.creattime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.creattime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

           if( !app.equals("")){
               sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.prjid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.prjid,date_format(a.creattime,'%Y-%m-%d') order by a.creattime desc ");
            List<PayInfoVo> list = someMapper.queryOppoPayNew(sql.toString());
            for(PayInfoVo pay : list){
                pay.setPayWay("oppo");
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryAliPay(Map<String, String> parmMap) {
        try {

            String begin = parmMap.get("begin");
            String end  = parmMap.get("end");
            String prjId = parmMap.get("prjId");
            String[] str = begin.split("-");
            String app = parmMap.get("app");
            String s = str[0]+str[1];

            StringBuffer 	sql = new StringBuffer("  select date_format(a.createtime,'%Y-%m-%d') as creattime,a.prjid,sum(a.feenum) as amount,count(*) as payCount,count(distinct a.imsi) as payNumber, " +
                    "IFNULL(sum(a.feenum)/count(distinct a.imsi),0) as arpu,cc.p_appNm as app from alone_game_"+s+" a,(SELECT yy.p_appNm,yy.p_pri,yy.p_appId from tb_app_info yy ,(select DISTINCT prjid from alone_game_"+s+" where prjid is not null ) bb  where yy.p_pri =bb.prjid) cc " +
                    "where   cc.p_pri=a.prjid  and not(cp_order_id like '%HW%') and trade_status = 'TRADE_SUCCESS'  ");

		/*StringBuffer 	sql = new StringBuffer("select date_format(createtime,'%Y-%m-%d') as creattime,prjid,sum(feenum) as amount,count(*) as payCount,count(distinct imsi) as payNumber, sum(feenum)/100/count(distinct imsi) as arpu,p_appNm as app" +
				  " from alone_game_"+s+" left join tb_app_info on tb_app_info.p_pri = alone_game_"+s+".prjid  where  not(cp_order_id like '%HW%') and trade_status = 'TRADE_SUCCESS'  ");*/

            if(begin.equals(end)){
                sql.append("   and  date_format(a.createtime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.createtime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.prjid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.prjid,date_format(a.createtime,'%Y-%m-%d') order by a.createtime desc ");

            List<PayInfoVo> list = someMapper.queryAliPay(sql.toString());
            for(int i=0;i<list.size();i++){
                if(list.get(i).getAmount() != null){
                    list.get(i).setAmount(list.get(i).getAmount()*100);
                }
                list.get(i).setPayWay("支付宝");
            }
            for(int i=0;i<list.size();i++){
                if(list.get(i).getAmount() == null || list.get(i).getAmount() == Double.valueOf(0)){
                    list.remove(list.get(i));
                    i--;
                }
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryAliPayNew(Map<String, String> parmMap) {
        try {

            String begin = parmMap.get("begin");
            String end  = parmMap.get("end");
            String prjId = parmMap.get("prjId");
            String[] str = begin.split("-");
            String app = parmMap.get("app");
            String s = str[0]+str[1];

            StringBuffer 	sql = new StringBuffer("  select date_format(a.createtime,'%Y-%m-%d') as creattime,a.prjid,sum(a.feenum) as amount,count(*) as payCount,count(distinct a.imsi) as payNumber, " +
                    "IFNULL(sum(a.feenum)/count(distinct a.imsi),0) as arpu,cc.p_appNm as app from alone_game_"+s+" a,(SELECT yy.pjId as p_pri,yy.gameName as p_appNm from dnwx_client.wbgui_formconfig yy ,(select DISTINCT prjid from alone_game_"+s+" where prjid is not null ) bb  where yy.pjId =bb.prjid) cc " +
                    "where   cc.p_pri=a.prjid  and not(cp_order_id like '%HW%') and trade_status = 'TRADE_SUCCESS'  ");

		/*StringBuffer 	sql = new StringBuffer("select date_format(createtime,'%Y-%m-%d') as creattime,prjid,sum(feenum) as amount,count(*) as payCount,count(distinct imsi) as payNumber, sum(feenum)/100/count(distinct imsi) as arpu,p_appNm as app" +
				  " from alone_game_"+s+" left join tb_app_info on tb_app_info.p_pri = alone_game_"+s+".prjid  where  not(cp_order_id like '%HW%') and trade_status = 'TRADE_SUCCESS'  ");*/

            if(begin.equals(end)){
                sql.append("   and  date_format(a.createtime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.createtime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

           if( !app.equals("")){
               sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.prjid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.prjid,date_format(a.createtime,'%Y-%m-%d') order by a.createtime desc ");

            List<PayInfoVo> list = someMapper.queryAliPayNew(sql.toString());
            for(int i=0;i<list.size();i++){
                if(list.get(i).getAmount() != null){
                    list.get(i).setAmount(list.get(i).getAmount()*100);
                }
                list.get(i).setPayWay("支付宝");
            }
            for(int i=0;i<list.size();i++){
                if(list.get(i).getAmount() == null || list.get(i).getAmount() == Double.valueOf(0)){
                    list.remove(list.get(i));
                    i--;
                }
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryViewPay(Map<String, String> parmMap) {
        try {

            String begin = parmMap.get("begin");
            String end  = parmMap.get("end");
            String prjId = parmMap.get("prjId");
            String app = parmMap.get("app");

            StringBuffer 	sql = new StringBuffer("  select date_format(a.createtime,'%Y-%m-%d') as creattime,a.prjid,sum(a.orderAmount) as amount,count(*) as payCount,count(distinct a.imei) as payNumber, " +
                    "sum(a.orderAmount)/count(distinct a.imei) as arpu,cc.p_appNm as app from vivo_pay_result a,(SELECT yy.p_appNm,yy.p_pri,yy.p_appId from tb_app_info yy ,(select DISTINCT prjid from vivo_pay_result where prjid is not null ) bb  where yy.p_pri =bb.prjid) cc   where   cc.p_pri=a.prjid    ");

		/*StringBuffer 	sql = new StringBuffer("select date_format(createtime,'%Y-%m-%d') as creattime,prjid,sum(orderAmount) as amount,count(*) as payCount,count(distinct imei) as payNumber, sum(orderAmount)/count(distinct imei) as arpu,p_appNm as app" +
				  " from vivo_pay_result left join tb_app_info on tb_app_info.p_pri = vivo_pay_result.prjid  where 1=1 ");*/

            if(begin.equals(end)){
                sql.append("   and  date_format(a.createtime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.createtime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.prjid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.prjid,date_format(a.createtime,'%Y-%m-%d') order by a.createtime desc ");

            List<PayInfoVo> list = someMapper.queryViewPay(sql.toString());
            for(PayInfoVo pay : list){
                if(pay.getAmount() != null ){
                    pay.setAmount(pay.getAmount()*100);
                }
                pay.setPayWay("vivo");
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryViewPayNew(Map<String, String> parmMap) {
        try {

            String begin = parmMap.get("begin");
            String end  = parmMap.get("end");
            String prjId = parmMap.get("prjId");
            String app = parmMap.get("app");

            StringBuffer 	sql = new StringBuffer("  select date_format(a.createtime,'%Y-%m-%d') as creattime,a.prjid,sum(a.orderAmount) as amount,count(*) as payCount,count(distinct a.imei) as payNumber, " +
                    "sum(a.orderAmount)/count(distinct a.imei) as arpu,cc.p_appNm as app from vivo_pay_result a,(SELECT yy.pjId as p_pri,yy.gameName as p_appNm from dnwx_client.wbgui_formconfig yy ,(select DISTINCT prjid from vivo_pay_result where prjid is not null ) bb  where yy.pjId =bb.prjid) cc   where   cc.p_pri=a.prjid    ");

		/*StringBuffer 	sql = new StringBuffer("select date_format(createtime,'%Y-%m-%d') as creattime,prjid,sum(orderAmount) as amount,count(*) as payCount,count(distinct imei) as payNumber, sum(orderAmount)/count(distinct imei) as arpu,p_appNm as app" +
				  " from vivo_pay_result left join tb_app_info on tb_app_info.p_pri = vivo_pay_result.prjid  where 1=1 ");*/

            if(begin.equals(end)){
                sql.append("   and  date_format(a.createtime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.createtime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

           if( !app.equals("")){
               sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.prjid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.prjid,date_format(a.createtime,'%Y-%m-%d') order by a.createtime desc ");

            List<PayInfoVo> list = someMapper.queryViewPayNew(sql.toString());
            for(PayInfoVo pay : list){
                if(pay.getAmount() != null ){
                    pay.setAmount(pay.getAmount()*100);
                }
                pay.setPayWay("vivo");
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryWxPay(Map<String, String> parmMap) {
        try {

            String begin = parmMap.get("begin");
            String end  = parmMap.get("end");
            String prjId = parmMap.get("prjId");
            String app = parmMap.get("app");

            StringBuffer 	sql = new StringBuffer("  select date_format(a.createtime,'%Y-%m-%d') as creattime,a.prjid,sum(a.cash_fee) as amount,count(*) as payCount,count(distinct a.openid) as payNumber, " +
                    "sum(a.cash_fee)/100/count(distinct a.openid) as arpu,cc.p_appNm as app from Wx_payresult a,(SELECT yy.p_appNm,yy.p_pri,yy.p_appId from tb_app_info yy ,(select DISTINCT prjid from Wx_payresult where prjid is not null ) bb  where yy.p_pri =bb.prjid) cc   where   cc.p_pri=a.prjid    ");
		/*StringBuffer 	sql = new StringBuffer("select date_format(createtime,'%Y-%m-%d') as creattime,prjid,sum(cash_fee) as amount,count(*) as payCount,count(distinct openid) as payNumber, sum(cash_fee)/100/count(distinct openid) as arpu,p_appNm as app" +
					" from Wx_payresult left join  tb_app_info on tb_app_info.p_pri = Wx_payresult.prjid  where 1=1 and result_code = 'SUCCESS' ");*/

            if(begin.equals(end)){
                sql.append("   and  date_format(a.createtime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.createtime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.prjid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.prjid,date_format(a.createtime,'%Y-%m-%d') order by a.createtime desc ");

            List<PayInfoVo> list = someMapper.queryWxPay(sql.toString());
            for(PayInfoVo pay : list){
                pay.setPayWay("微信");
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryWxPayNew(Map<String, String> parmMap) {
        try {

            String begin = parmMap.get("begin");
            String end  = parmMap.get("end");
            String prjId = parmMap.get("prjId");
            String app = parmMap.get("app");

            StringBuffer 	sql = new StringBuffer("  select date_format(a.createtime,'%Y-%m-%d') as creattime,a.prjid,sum(a.cash_fee) as amount,count(*) as payCount,count(distinct a.openid) as payNumber, " +
                    "sum(a.cash_fee)/100/count(distinct a.openid) as arpu,cc.p_appNm as app from Wx_payresult a,(SELECT yy.pjId as p_pri,yy.gameName as p_appNm from dnwx_client.wbgui_formconfig yy ,(select DISTINCT prjid from Wx_payresult where prjid is not null ) bb  where yy.pjId =bb.prjid) cc   where   cc.p_pri=a.prjid    ");
		/*StringBuffer 	sql = new StringBuffer("select date_format(createtime,'%Y-%m-%d') as creattime,prjid,sum(cash_fee) as amount,count(*) as payCount,count(distinct openid) as payNumber, sum(cash_fee)/100/count(distinct openid) as arpu,p_appNm as app" +
					" from Wx_payresult left join  tb_app_info on tb_app_info.p_pri = Wx_payresult.prjid  where 1=1 and result_code = 'SUCCESS' ");*/

            if(begin.equals(end)){
                sql.append("   and  date_format(a.createtime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.createtime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.prjid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.prjid,date_format(a.createtime,'%Y-%m-%d') order by a.createtime desc ");

            List<PayInfoVo> list = someMapper.queryWxPayNew(sql.toString());
            for(PayInfoVo pay : list){
                pay.setPayWay("微信");
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryHWPay(Map<String, String> parmMap) {
        try {

            String begin = parmMap.get("begin");
            String end  = parmMap.get("end");
            String prjId = parmMap.get("prjId");
            String app = parmMap.get("app");


            String[] str = begin.split("-");
            String s = str[0]+str[1];

            StringBuffer 	sql = new StringBuffer("  select date_format(a.createtime,'%Y-%m-%d') as creattime,a.prjid,sum(a.feenum*100) as amount,count(*) as payCount,count(distinct a.imsi) as payNumber, " +
                    "sum(a.feenum)/count(distinct a.imsi) as arpu,cc.p_appNm as app from hw_pay_info a,(SELECT yy.p_appNm,yy.p_pri,yy.p_appId from tb_app_info yy ) cc   where   cc.p_pri=a.prjid and cp_order_id like '%HW%' and cp_result = 'success' and feenum < 328");

		/*StringBuffer 	sql = new StringBuffer("select date_format(createtime,'%Y-%m-%d') as creattime,prjid,sum(feenum) as amount,count(*) as payCount,count(distinct imsi) as payNumber, sum(feenum)/100/count(distinct imsi) as arpu,p_appNm as app" +
				  " from alone_game_"+s+" left join tb_app_info on tb_app_info.p_pri = alone_game_"+s+".prjid  where  cp_order_id like '%HW%' and cp_result = 'success'  "); */

            if(begin.equals(end)){
                sql.append("   and  date_format(a.createtime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.createtime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.prjid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.prjid,date_format(a.createtime,'%Y-%m-%d') order by a.createtime desc ");

            List<PayInfoVo> list = someMapper.queryHWPay(sql.toString());
            //System.out.println(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("华为");
            }
            return list;

        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryHWPayNew(Map<String, String> parmMap) {
        try {

            String begin = parmMap.get("begin");
            String end  = parmMap.get("end");
            String prjId = parmMap.get("prjId");
            String app = parmMap.get("app");


            String[] str = begin.split("-");
            String s = str[0]+str[1];

            StringBuffer 	sql = new StringBuffer("  select date_format(a.createtime,'%Y-%m-%d') as creattime,a.prjid,sum(a.feenum*100) as amount,count(*) as payCount,count(distinct a.imsi) as payNumber, " +
                    "sum(a.feenum)/count(distinct a.imsi) as arpu,cc.p_appNm as app from hw_pay_info a,(SELECT yy.pjId as p_pri,yy.gameName as p_appNm from dnwx_client.wbgui_formconfig yy ) cc   where   cc.p_pri=a.prjid and cp_order_id like '%HW%' and cp_result = 'success' and feenum < 328");

		/*StringBuffer 	sql = new StringBuffer("select date_format(createtime,'%Y-%m-%d') as creattime,prjid,sum(feenum) as amount,count(*) as payCount,count(distinct imsi) as payNumber, sum(feenum)/100/count(distinct imsi) as arpu,p_appNm as app" +
				  " from alone_game_"+s+" left join tb_app_info on tb_app_info.p_pri = alone_game_"+s+".prjid  where  cp_order_id like '%HW%' and cp_result = 'success'  "); */

            if(begin.equals(end)){
                sql.append("   and  date_format(a.createtime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.createtime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.prjid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.prjid,date_format(a.createtime,'%Y-%m-%d') order by a.createtime desc ");

            List<PayInfoVo> list = someMapper.queryHWPayNew(sql.toString());
            //System.out.println(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("华为");
            }
            return list;

        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryBDPay(Map<String, String> parmMap) {
        try {

            String begin = parmMap.get("begin");
            String end  = parmMap.get("end");
            String prjId = parmMap.get("prjId");
            String app = parmMap.get("app");

            StringBuffer 	sql = new StringBuffer("select date_format(a.ordertime,'%Y-%m-%d') as creattime,a.pid as prjid,"+
                    "sum(a.amount) as amount,count(*) as payCount,count(distinct a.imei) as payNumber,"+
                    "sum(a.amount)/count(distinct a.imei)/100 as arpu,cc.p_appNm as app"+
                    " from baidu_pay_info a,(SELECT yy.p_appNm,yy.p_pri,yy.p_appId"+
                    " from tb_app_info yy,(select DISTINCT pid from baidu_pay_info"+
                    " where pid is not null ) bb  where yy.p_pri =bb.pid) cc"+
                    " where cc.p_pri=a.pid and status = 'success' ");

            if(begin.equals(end)){
                sql.append("   and  date_format(ordertime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  ordertime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }
            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.pid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.pid,date_format(ordertime,'%Y-%m-%d') order by ordertime desc ");

            List<PayInfoVo> list = someMapper.queryBDPay(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("百度");
            }
            return list;

        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryBDPayNew(Map<String, String> parmMap) {
        try {

            String begin = parmMap.get("begin");
            String end  = parmMap.get("end");
            String prjId = parmMap.get("prjId");
            String app = parmMap.get("app");

            StringBuffer 	sql = new StringBuffer("select date_format(a.ordertime,'%Y-%m-%d') as creattime,a.pid as prjid,"+
                    "sum(a.amount) as amount,count(*) as payCount,count(distinct a.imei) as payNumber,"+
                    "sum(a.amount)/count(distinct a.imei)/100 as arpu,cc.p_appNm as app"+
                    " from baidu_pay_info a,(SELECT yy.pjId as p_pri,yy.gameName as p_appNm"+
                    " from dnwx_client.wbgui_formconfig yy,(select DISTINCT pid from baidu_pay_info"+
                    " where pid is not null ) bb  where yy.pjId =bb.pid) cc"+
                    " where cc.p_pri=a.pid and status = 'success' ");

            if(begin.equals(end)){
                sql.append("   and  date_format(ordertime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  ordertime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }
           if( !app.equals("")){
               sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.pid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.pid,date_format(ordertime,'%Y-%m-%d') order by ordertime desc ");

            List<PayInfoVo> list = someMapper.queryBDPayNew(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("百度");
            }
            return list;

        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryXMPay(Map<String, String> parmMap) {
        try {

            String begin = parmMap.get("begin");
            String end  = parmMap.get("end");
            String prjId = parmMap.get("prjId");
            String app = parmMap.get("app");

            StringBuffer 	sql = new StringBuffer("select date_format(a.payTime,'%Y-%m-%d') as creattime,a.pid as prjid,"+
                    "sum(a.payFee) as amount,count(*) as payCount,count(distinct a.imei) as payNumber,"+
                    "sum(a.payFee/100)/count(distinct a.imei/100) as arpu,cc.p_appNm as app"+
                    " from xiaomi_pay_info a,(SELECT yy.p_appNm,yy.p_pri,yy.p_appId"+
                    " from tb_app_info yy,(select DISTINCT pid from xiaomi_pay_info"+
                    " where pid is not null ) bb  where yy.p_pri =bb.pid) cc"+
                    " where cc.p_pri=a.pid ");

            if(begin.equals(end)){
                sql.append("   and  date_format(payTime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  payTime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }
            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.pid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.pid,date_format(payTime,'%Y-%m-%d') order by payTime desc ");

            List<PayInfoVo> list = someMapper.queryXMPay(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("小米");
            }
            return list;

        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryXMPayNew(Map<String, String> parmMap) {
        try {

            String begin = parmMap.get("begin");
            String end  = parmMap.get("end");
            String prjId = parmMap.get("prjId");
            String app = parmMap.get("app");

            StringBuffer 	sql = new StringBuffer("select date_format(a.payTime,'%Y-%m-%d') as creattime,a.pid as prjid,"+
                    "sum(a.payFee) as amount,count(*) as payCount,count(distinct a.imei) as payNumber,"+
                    "sum(a.payFee/100)/count(distinct a.imei/100) as arpu,cc.p_appNm as app"+
                    " from xiaomi_pay_info a,(SELECT yy.pjId as p_pri,yy.gameName as p_appNm"+
                    " from dnwx_client.wbgui_formconfig yy,(select DISTINCT pid from xiaomi_pay_info"+
                    " where pid is not null ) bb  where yy.pjId =bb.pid) cc"+
                    " where cc.p_pri=a.pid ");

            if(begin.equals(end)){
                sql.append("   and  date_format(payTime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  payTime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }
            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.pid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.pid,date_format(payTime,'%Y-%m-%d') order by payTime desc ");

            List<PayInfoVo> list = someMapper.queryXMPayNew(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("小米");
            }
            return list;

        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryJLPay(Map<String, String> paramMap) {
        try {

            String begin = paramMap.get("begin");
            String end  = paramMap.get("end");
            String prjId = paramMap.get("prjId");
            String app = paramMap.get("app");

            StringBuffer 	sql = new StringBuffer("select date_format(a.create_time,'%Y-%m-%d') as creattime,a.pid as prjid,"+
                    "sum(a.deal_price*100) as amount,count(*) as payCount,count(distinct a.imei) as payNumber,"+
                    "sum(a.deal_price)/count(distinct a.imei) as arpu,cc.p_appNm as app"+
                    " from jinli_pay_info a,(SELECT yy.p_appNm,yy.p_pri,yy.p_appId"+
                    " from tb_app_info yy,(select DISTINCT pid from jinli_pay_info"+
                    " where pid is not null ) bb  where yy.p_pri =bb.pid) cc"+
                    " where cc.p_pri=a.pid ");

            if(begin.equals(end)){
                sql.append("   and  date_format(create_time,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  date_format(create_time,'%Y-%m-%d') BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }
            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.pid  like \'%"+prjId+"%\'   ");
            }

            sql.append("group by a.pid,date_format(create_time,'%Y-%m-%d') order by create_time desc ");

            List<PayInfoVo> list = someMapper.queryJLPay(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("金立");
            }
            return list;

        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryJLPayNew(Map<String, String> paramMap) {
        try {

            String begin = paramMap.get("begin");
            String end  = paramMap.get("end");
            String prjId = paramMap.get("prjId");
            String app = paramMap.get("app");

            StringBuffer 	sql = new StringBuffer("select date_format(a.create_time,'%Y-%m-%d') as creattime,a.pid as prjid,"+
                    "sum(a.deal_price*100) as amount,count(*) as payCount,count(distinct a.imei) as payNumber,"+
                    "sum(a.deal_price)/count(distinct a.imei) as arpu,cc.p_appNm as app"+
                    " from jinli_pay_info a,(SELECT yy.pjId as p_pri,yy.gameName as p_appNm"+
                    " from dnwx_client.wbgui_formconfig yy,(select DISTINCT pid from jinli_pay_info"+
                    " where pid is not null ) bb  where yy.pjId =bb.pid) cc"+
                    " where cc.p_pri=a.pid ");

            if(begin.equals(end)){
                sql.append("   and  date_format(create_time,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  date_format(create_time,'%Y-%m-%d') BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }
           if( !app.equals("")){
               sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.pid  like \'%"+prjId+"%\'   ");
            }

            sql.append("group by a.pid,date_format(create_time,'%Y-%m-%d') order by create_time desc ");

            List<PayInfoVo> list = someMapper.queryJLPayNew(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("金立");
            }
            return list;

        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryMZPay(Map<String, String> paramMap) {
        try {

            String begin = paramMap.get("begin");
            String end  = paramMap.get("end");
            String prjId = paramMap.get("prjId");
            String app = paramMap.get("app");

            StringBuffer 	sql = new StringBuffer("select date_format(a.notify_time,'%Y-%m-%d') as creattime,a.pid as prjid,"+
                    "sum(a.total_price*100) as amount,count(*) as payCount,count(distinct a.imei) as payNumber,"+
                    "sum(a.total_price)/count(distinct a.imei) as arpu,cc.p_appNm as app"+
                    " from meizu_pay_info a,(SELECT yy.p_appNm,yy.p_pri,yy.p_appId"+
                    " from tb_app_info yy,(select DISTINCT pid from meizu_pay_info"+
                    " where pid is not null ) bb  where yy.p_pri =bb.pid) cc"+
                    " where cc.p_pri=a.pid ");

            if(begin.equals(end)){
                sql.append("   and  date_format(notify_time,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  notify_time BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }
            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.pid  like \'%"+prjId+"%\'   ");
            }

            sql.append("group by a.pid,date_format(notify_time,'%Y-%m-%d') order by notify_time desc ");

            List<PayInfoVo> list = someMapper.queryMZPay(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("魅族");
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryMZPayNew(Map<String, String> paramMap) {
        try {

            String begin = paramMap.get("begin");
            String end  = paramMap.get("end");
            String prjId = paramMap.get("prjId");
            String app = paramMap.get("app");

            StringBuffer 	sql = new StringBuffer("select date_format(a.notify_time,'%Y-%m-%d') as creattime,a.pid as prjid,"+
                    "sum(a.total_price*100) as amount,count(*) as payCount,count(distinct a.imei) as payNumber,"+
                    "sum(a.total_price)/count(distinct a.imei) as arpu,cc.p_appNm as app"+
                    " from meizu_pay_info a,(SELECT yy.pjId as p_pri,yy.gameName as p_appNm"+
                    " from  dnwx_client.wbgui_formconfig yy,(select DISTINCT pid from meizu_pay_info"+
                    " where pid is not null ) bb  where yy.pjId =bb.pid) cc"+
                    " where cc.p_pri=a.pid ");

            if(begin.equals(end)){
                sql.append("   and  date_format(notify_time,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  notify_time BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }
            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.pid  like \'%"+prjId+"%\'   ");
            }

            sql.append("group by a.pid,date_format(notify_time,'%Y-%m-%d') order by notify_time desc ");

            List<PayInfoVo> list = someMapper.queryMZPayNew(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("魅族");
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> querySXPay(Map<String, String> paramMap) {
        try {


            String begin = paramMap.get("begin");
            String end  = paramMap.get("end");
            String prjId = paramMap.get("prjId");
            String app = paramMap.get("app");

            StringBuffer 	sql = new StringBuffer("select date_format(a.ordertime,'%Y-%m-%d') as creattime,a.pid as prjid,"+
                    "sum(a.price*100) as amount,count(*) as payCount,count(distinct a.imei) as payNumber,"+
                    "truncate(sum(a.price)/count(distinct a.imei),2) as arpu,cc.p_appNm as app"+
                    " from sanxing_pay_info a,(SELECT yy.p_appNm,yy.p_pri,yy.p_appId"+
                    " from tb_app_info yy,(select DISTINCT pid from sanxing_pay_info"+
                    " where pid is not null ) bb  where yy.p_pri =bb.pid) cc"+
                    " where cc.p_pri=a.pid and status = 'success'");

            if(begin.equals(end)){
                sql.append("   and  date_format(a.ordertime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.ordertime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.pid  like \'%"+prjId+"%\'   ");
            }

            sql.append("group by a.pid,date_format(a.ordertime,'%Y-%m-%d') order by a.ordertime desc ");

            List<PayInfoVo> list = someMapper.querySXPay(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("三星");
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> querySXPayNew(Map<String, String> paramMap) {
        try {


            String begin = paramMap.get("begin");
            String end  = paramMap.get("end");
            String prjId = paramMap.get("prjId");
            String app = paramMap.get("app");

            StringBuffer 	sql = new StringBuffer("select date_format(a.ordertime,'%Y-%m-%d') as creattime,a.pid as prjid,"+
                    "sum(a.price*100) as amount,count(*) as payCount,count(distinct a.imei) as payNumber,"+
                    "truncate(sum(a.price)/count(distinct a.imei),2) as arpu,cc.p_appNm as app"+
                    " from sanxing_pay_info a,(SELECT yy.pjId as p_pri,yy.gameName as p_appNm"+
                    " from  dnwx_client.wbgui_formconfig yy,(select DISTINCT pid from sanxing_pay_info"+
                    " where pid is not null ) bb  where yy.pjId =bb.pid) cc"+
                    " where cc.p_pri=a.pid and status = 'success'");

            if(begin.equals(end)){
                sql.append("   and  date_format(a.ordertime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.ordertime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.pid  like \'%"+prjId+"%\'   ");
            }

            sql.append("group by a.pid,date_format(a.ordertime,'%Y-%m-%d') order by a.ordertime desc ");

            List<PayInfoVo> list = someMapper.querySXPayNew(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("三星");
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryLXPay(Map<String, String> paramMap) {
        try {

            String begin = paramMap.get("begin");
            String end  = paramMap.get("end");
            String prjId = paramMap.get("prjId");
            String app = paramMap.get("app");

            StringBuffer 	sql = new StringBuffer("select date_format(a.ordertime,'%Y-%m-%d') as creattime,a.pid as prjid,"+
                    "sum(a.price*100) as amount,count(*) as payCount,count(distinct a.imei) as payNumber,"+
                    "truncate(sum(a.price)/count(distinct a.imei),2) as arpu,cc.p_appNm as app"+
                    " from lianxiang_pay_info a,(SELECT yy.p_appNm,yy.p_pri,yy.p_appId"+
                    " from tb_app_info yy,(select DISTINCT pid from lianxiang_pay_info"+
                    " where pid is not null ) bb  where yy.p_pri =bb.pid) cc"+
                    " where cc.p_pri=a.pid and status = 'success'");

            if(begin.equals(end)){
                sql.append("   and  date_format(a.ordertime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.ordertime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.pid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.pid,date_format(a.ordertime,'%Y-%m-%d') order by a.ordertime desc ");

            List<PayInfoVo> list = someMapper.queryLXPay(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("联想");
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryLXPayNew(Map<String, String> paramMap) {
        try {

            String begin = paramMap.get("begin");
            String end  = paramMap.get("end");
            String prjId = paramMap.get("prjId");
            String app = paramMap.get("app");

            StringBuffer 	sql = new StringBuffer("select date_format(a.ordertime,'%Y-%m-%d') as creattime,a.pid as prjid,"+
                    "sum(a.price*100) as amount,count(*) as payCount,count(distinct a.imei) as payNumber,"+
                    "truncate(sum(a.price)/count(distinct a.imei),2) as arpu,cc.p_appNm as app"+
                    " from lianxiang_pay_info a,(SELECT yy.pjId as p_pri,yy.gameName as p_appNm"+
                    " from  dnwx_client.wbgui_formconfig yy,(select DISTINCT pid from lianxiang_pay_info"+
                    " where pid is not null ) bb  where yy.pjId =bb.pid) cc"+
                    " where cc.p_pri=a.pid and status = 'success'");

            if(begin.equals(end)){
                sql.append("   and  date_format(a.ordertime,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.ordertime BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.pid  like \'%"+prjId+"%\'   ");
            }
            sql.append("group by a.pid,date_format(a.ordertime,'%Y-%m-%d') order by a.ordertime desc ");

            List<PayInfoVo> list = someMapper.queryLXPayNew(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("联想");
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryTXPay(Map<String, String> paramMap) {
        try {

            String begin = paramMap.get("begin");
            String end  = paramMap.get("end");
            String prjId = paramMap.get("prjId");
            String app = paramMap.get("app");

            StringBuffer 	sql = new StringBuffer("select date_format(a.pay_time,'%Y-%m-%d') as creattime,a.pid as prjid,"+
                    "sum(a.zoneId*100) as amount,count(*) as payCount,count(distinct a.imei) as payNumber,"+
                    "sum(a.zoneId)/count(distinct a.imei) as arpu,cc.p_appNm as app"+
                    " from tx_pay_info a,(SELECT yy.p_appNm,yy.p_pri,yy.p_appId"+
                    " from tb_app_info yy,(select DISTINCT pid from tx_pay_info"+
                    " where pid is not null ) bb  where yy.p_pri =bb.pid) cc"+
                    " where cc.p_pri=a.pid ");

            if(begin.equals(end)){
                sql.append("   and  date_format(a.pay_time,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.pay_time BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.pid  like \'%"+prjId+"%\'   ");
            }

            sql.append("group by a.pid,date_format(a.pay_time,'%Y-%m-%d') order by a.pay_time desc ");

            List<PayInfoVo> list = someMapper.queryTXPay(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("腾讯");
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PayInfoVo> queryTXPayNew(Map<String, String> paramMap) {
        try {

            String begin = paramMap.get("begin");
            String end  = paramMap.get("end");
            String prjId = paramMap.get("prjId");
            String app = paramMap.get("app");

            StringBuffer 	sql = new StringBuffer("select date_format(a.pay_time,'%Y-%m-%d') as creattime,a.pid as prjid,"+
                    "sum(a.zoneId*100) as amount,count(*) as payCount,count(distinct a.imei) as payNumber,"+
                    "sum(a.zoneId)/count(distinct a.imei) as arpu,cc.p_appNm as app"+
                    " from tx_pay_info a,(SELECT yy.pjId as p_pri,yy.gameName as p_appNm"+
                    " from  dnwx_client.wbgui_formconfig yy,(select DISTINCT pid from tx_pay_info"+
                    " where pid is not null ) bb  where yy.pjId =bb.pid) cc"+
                    " where cc.p_pri=a.pid ");

            if(begin.equals(end)){
                sql.append("   and  date_format(a.pay_time,'%Y-%m-%d') =  \""+begin+"\"     ");
            }else
            {
                sql.append(" and  a.pay_time BETWEEN   \""+begin+"\" and  \""+end+"\"  ");
            }

            if( !app.equals("")){
                sql.append("   and cc.p_appNm like \'%"+app+"%\'   ");
            }
            if( !prjId.equals("")){
                sql.append("  and a.pid  like \'%"+prjId+"%\'   ");
            }

            sql.append("group by a.pid,date_format(a.pay_time,'%Y-%m-%d') order by a.pay_time desc ");

            List<PayInfoVo> list = someMapper.queryTXPayNew(sql.toString());
            for(int i=0;i<list.size();i++){
                list.get(i).setPayWay("腾讯");
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            List<PayInfoVo> rlist= new ArrayList<PayInfoVo>();
            return rlist;
        }
    }


}