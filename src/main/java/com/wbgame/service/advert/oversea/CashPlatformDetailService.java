package com.wbgame.service.advert.oversea;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.wbgame.common.Asserts;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.master.CommonMapper;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.mapper.master.finance.master.FinanceMapper;
import com.wbgame.mapper.adb.CashPlatformDetailMapper;
import com.wbgame.pojo.ChannelInfo;
import com.wbgame.pojo.advert.oversea.BasePageResultResponse;
import com.wbgame.pojo.param.CashPlatformDetailRequestParam;
import com.wbgame.pojo.response.DnChaCashTotalResponse;
import com.wbgame.utils.DataTransUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.jettison.StringUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 变现平台明细表 业务实现层
 * @Date 2025/3/18 16:02
 */
@Service
public class CashPlatformDetailService {


    @Autowired
    private CashPlatformDetailMapper cashPlatformDetailMapper;
    @Resource
    private FinanceMapper financeMapper;
    @Resource
    private YyhzMapper yyhzMapper;
    @Resource
    private CommonMapper commonMapper;

    /**
     * 查询变现平台明细表数据
     *
     * @param param 查询条件
     * @return 查询结果
     */
    public Result<BasePageResultResponse<DnChaCashTotalResponse>> selectCashPlatformDetails(CashPlatformDetailRequestParam param) {
        List<String> grouplist = new ArrayList<>();
        if (!StringUtils.isBlank(param.getGroups())) {
            grouplist = Arrays.asList(param.getGroups().split(","));//维度用groupList装起来
        }
        //时间为空赋值默认值
        if (StringUtils.isBlank(param.getStart_date()) || StringUtils.isBlank(param.getEnd_date())) {
            param.setStart_date(DateTime.now().minusDays(1).toString("yyyy-MM-dd"));
            param.setEnd_date(DateTime.now().minusDays(1).toString("yyyy-MM-dd"));
        }
        if (grouplist.contains("ad_sid")) {
            param.setAd_sid_group("true");
        }
        param.setGroupList(grouplist);
        /* 处理中文及字母排序 */
        List<String> orderArr = Lists.newArrayList("appname", "app_category", "cha_type", "cha_media", "cha_sub_launch", "cha_id", "agent", "ad_sid", "placement_type", "open_type", "country");
        /* 在列表中的字段名，使用LOWER()进行修改 反之不进行处理 */
        String orderStr = param.getOrder_str();
        if (!StringUtils.isBlank(orderStr)) {
            for (String str : orderArr) {
                if (orderStr.contains(str)) {
                    orderStr = orderStr.replace(str + " ", String.format("LOWER(%s)", str) + " ");
                }
            }
            //特殊处理操作：应用内外-out 属于数据库关键字
            if (orderStr.contains("out")) {
                orderStr = orderStr.replace("out", "`out`");
            }
            param.setOrder_str(orderStr);
        }
        PageHelper.startPage(param.getStart(), param.getLimit());
        List<DnChaCashTotalResponse> cashTotalList = selectDnChaCashTotal(param);
        //汇总栏
        DnChaCashTotalResponse total = cashPlatformDetailMapper.selectDnChaCashTotalSum(param);
        return ResultUtils.success(BasePageResultResponse.page(cashTotalList), total);
    }


    public List<DnChaCashTotalResponse> selectDnChaCashTotal(CashPlatformDetailRequestParam param) {
        //数据查询
        List<DnChaCashTotalResponse> cashTotalList = cashPlatformDetailMapper.selectDnChaCashTotal(param);
        if (CollectionUtils.isEmpty(cashTotalList)) {
            return cashTotalList;
        }
        //将应用分类加上去
        Map<String, String> appCategoryMap = new HashMap<>();
        Map<String, String> channelTypeMap = new HashMap<>();
        Map<String, String> countryMap = new HashMap<>();
        List<String> groups = param.getGroupList();
        //应用分类
        if (groups != null && groups.contains("app_category")) {
            List<Map<String, String>> appCategoryList = financeMapper.getAppCategory();
            appCategoryMap = appCategoryList.stream().collect(Collectors.toMap(map -> StringUtil.getString(map.get("id")), map -> StringUtil.getString(map.get("name")), (k1, k2) -> k2));
        }
        //将渠道类型加上去
        if (groups != null && groups.contains("cha_type")) {
            Map<String, ChannelInfo> channelInfoMap = yyhzMapper.selectAllChannelTypeList();
            channelTypeMap = channelInfoMap.values().stream().filter(value -> ObjectUtils.isNotEmpty(value.getCha_type())).collect(Collectors.toMap(data -> Integer.toString(data.getCha_type()), ChannelInfo::getType_name, (k1, k2) -> k1));
        }
        //国家
        if (groups != null && groups.contains("country")) {
            List<Map<String, String>> countryVos = commonMapper.selectCountryList();
            countryMap = countryVos.stream().collect(Collectors.toMap(x -> x.get("country_code"), x -> x.get("country_name"), (k1, k2) -> k1));
        }
        for (DnChaCashTotalResponse response : cashTotalList) {
            //封装应用分类
            response.setApp_category(appCategoryMap.getOrDefault(response.getApp_category(), response.getApp_category()));
            //封装渠道类型
            response.setCha_type(channelTypeMap.getOrDefault(response.getCha_type(), response.getCha_type()));
            //封装国家
            response.setCountry(countryMap.getOrDefault(response.getCountry(), response.getCountry()));
            //占比封装
            DataTransUtils.addDataPercentage(response, "fill_rate,pv_rate,click_rate");
        }
        return cashTotalList;
    }


    public void exportCashPlatformDetails(CashPlatformDetailRequestParam param, HttpServletResponse response) {
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        List<String> grouplist = new ArrayList<>();
        if (!StringUtils.isBlank(param.getGroups())) {
            grouplist = Arrays.asList(param.getGroups().split(","));//维度用groupList装起来
        }
        param.setGroupList(grouplist);
        if (StringUtils.isBlank(param.getStart_date()) || StringUtils.isBlank(param.getEnd_date())) {
            param.setStart_date(DateTime.now().minusDays(1).toString("yyyy-MM-dd"));
            param.setEnd_date(DateTime.now().minusDays(1).toString("yyyy-MM-dd"));
        }
        if (grouplist.contains("ad_sid")) {
            param.setAd_sid_group("true");
        }
        /* 处理中文及字母排序 */
        List<String> orderArr = Lists.newArrayList("appname", "app_category", "cha_type", "cha_media", "cha_sub_launch", "cha_id", "agent", "ad_sid", "placement_type", "open_type", "country");
        String orderStr = param.getOrder_str();
        /* 在列表中的字段名，使用LOWER()进行修改 反之不进行处理 */
        if (!StringUtils.isBlank(orderStr)) {
            for (String str : orderArr) {
                if (orderStr.contains(str)) {
                    orderStr = orderStr.replace(str + " ", String.format("LOWER(%s)", str) + " ");
                }
            }
            //特殊处理操作：应用内外-out 属于数据库关键字
            if (orderStr.contains("out")) {
                orderStr = orderStr.replace("out", "`out`");
            }
            param.setOrder_str(orderStr);
        }
        List<DnChaCashTotalResponse> cashTotalList = selectDnChaCashTotal(param);
        List<Map<String,String>> appNameList = financeMapper.getAppName();
        Map<String, String> appNameMap = new HashMap<>();
        appNameMap = appNameList.stream().collect(Collectors.toMap(map -> StringUtil.getString(map.get("id")),map -> StringUtil.getString(map.get("app_name")),(k1, k2)->k2));

        for (DnChaCashTotalResponse cashTotal : cashTotalList) {
            if (appNameMap.containsKey(cashTotal.getDnappid())) {
                cashTotal.setGname(appNameMap.get(cashTotal.getDnappid()));
            }
            if (!StringUtils.isBlank(cashTotal.getType())) {
                if ("2".equals(cashTotal.getType())) {
                    cashTotal.setType("内容");
                } else {
                    cashTotal.setType("广告");
                }
            }
            if (!StringUtils.isBlank(cashTotal.getOut())) {
                if ("1".equals(cashTotal.getOut())) {
                    cashTotal.setOut("应用外");
                } else {
                    cashTotal.setOut("应用内");
                }
            }
        }
        //原有表头逻辑不变,自定义列改
        String value = param.getValue();
        if (!StringUtils.isBlank(value)) {
            try {
                String[] split = value.split(";");
                for (int i = 0; i < split.length; i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0], s[1]);
                }
            } catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        String fileName = "变现平台明细海外_" + DateTime.now().toString("yyyyMMdd") + ".xlsx";
        ExportExcelUtil.exportXLSX2(response, cashTotalList, headerMap, fileName);
    }
}
