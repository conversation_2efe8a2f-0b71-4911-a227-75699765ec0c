package com.wbgame.service.advert;

import com.wbgame.pojo.advert.partnerNew.PartnerInvestGroupDTO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 合作方投放明细聚合查询服务接口
 * <AUTHOR>
 */
public interface PartnerInvestGroupService {

    /**
     * 合作方投放明细聚合查询
     * @param dto 查询参数
     * @param request HTTP请求
     * @return 查询结果
     * @throws IOException IO异常
     */
    String list(PartnerInvestGroupDTO dto, HttpServletRequest request) throws IOException;
    
    /**
     * 合作方产品收支导出
     * @param dto 查询参数
     * @param request HTTP请求
     * @param response HTTP响应
     */
    void export(PartnerInvestGroupDTO dto, HttpServletRequest request, HttpServletResponse response);
    

    /**
     * 处理投放数据
     * @param handle 操作类型
     * @param dto 查询参数
     * @return 操作结果
     * @throws Exception 异常
     */
    String handle(String handle, PartnerInvestGroupDTO dto) throws Exception;
    
    /**
     * 同步投放金额
     * @param dto 对象参数
     * sdate 日期
     * edate 日期
     * token 用户token
     * @return 操作结果
     */
    String sync(PartnerInvestGroupDTO dto);
}