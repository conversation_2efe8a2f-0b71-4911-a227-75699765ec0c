package com.wbgame.service.advert.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.adb.PartnerInvestGroupMapper;
import com.wbgame.pojo.AppCategory;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.advert.partnerNew.PartnerInvestGroupDTO;
import com.wbgame.service.AdService;
import com.wbgame.service.PartnerService;
import com.wbgame.service.SomeService;
import com.wbgame.service.advert.PartnerInvestGroupService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 合作方投放明细聚合查询服务实现类
 * <AUTHOR>
 */
@Service
public class PartnerInvestGroupServiceImpl implements PartnerInvestGroupService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private PartnerInvestGroupMapper partnerInvestGroupMapper;
    @Autowired
    private AdService adService;
    @Autowired
    private SomeService someService;

    /**
     * 合作方投放明细聚合查询
     * @param dto 查询参数
     * @param request HTTP请求
     * @return 查询结果
     * @throws IOException IO异常
     */
    @Override
    public String list(PartnerInvestGroupDTO dto, HttpServletRequest request) throws IOException {

        JSONObject result = new JSONObject();
        try {
            String token = dto.getToken() != null ? dto.getToken() : request.getParameter("token");
            CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);
            dto.setCuser(cuser.getLogin_name());
            // 不为管理员用户 则添加应用控制
            if(!"root".equals(cuser.getOrg_id())){
                if(!BlankUtils.checkBlank(cuser.getApp_group())){
                    dto.setApps(cuser.getApp_group());
                }
            }


            //自定义时段
            List<Map<String,String>> reportList = new ArrayList<>();
            if (dto.getCustom_date() != null && dto.getCustom_date().size() > 0) {
                for (int i = 0;i < dto.getCustom_date().size();i+=2) {
                    dto.setSdate(dto.getCustom_date().get(i));
                    dto.setEdate(dto.getCustom_date().get(i+1));
                    reportList.addAll(partnerInvestGroupMapper.selectPartnerInvestGroup(dto));
                }
            }else {
                reportList = partnerInvestGroupMapper.selectPartnerInvestGroup(dto);
            }

            //手动分页
            int totalSize = reportList.size();
            result.put("totalCount", totalSize);
            reportList = reportList.stream().skip((dto.getStart()-1)*dto.getLimit()).limit(dto.getLimit()).collect(Collectors.toList());

            // 调整为日期格式
            convertDateStr(reportList,dto);
            
            // 处理查询结果
            processQueryResults(reportList);

            // 设置返回结果
            result.put("ret", 1);
            result.put("data", reportList);

            // 非自定义日期才进行汇总
            if (dto.getCustom_date() == null || dto.getCustom_date().size() == 0) {
                result.put("total", partnerInvestGroupMapper.selectPartnerInvestGroupSum(dto));
            }

            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息: " + e.getMessage());
        }
    }

    /**
     * 日期转换为标准形式（联运报表）
     * @param param
     */
    public void convertDateStr(List<Map<String,String>> list, PartnerInvestGroupDTO param){
        try {
            for (Map<String,String> op : list) {
                if (param.getGroup() != null && param.getGroup().contains("day")) {
                    Date date = new SimpleDateFormat("yyyy-MM-dd").parse(op.get("day"));
                    String xinqi = DateUtil.dateToWeek(date);
                    op.put("day",(op.get("day")+"("+xinqi+")"));
                }else if (param.getGroup() != null &&  param.getGroup().contains("week")) {
                    String[] split = op.get("day").split("-");
                    if (split.length >= 2) {
                        Integer year = Integer.parseInt(split[0]);
                        Integer week = Integer.parseInt(split[1]);
                        String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week);
                        String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week);
                        op.put("day", year+"年第"+week+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                    }
                }else if (param.getGroup() != null &&  param.getGroup().contains("month")) {
                    //不做处理
                }else if (param.getGroup() != null &&  param.getGroup().contains("beek")) {
                    String[] split = op.get("day").split("-");
                    if (split.length >= 3) {
                        Integer year = Integer.parseInt(split[0]);
                        Integer week1 = Integer.parseInt(split[1]);
                        Integer week2 = Integer.parseInt(split[2]);
                        String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week1);
                        String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week2);
                        op.put("day", year+"年第"+week1+"-"+week2+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                    }
                }else if (param.getCustom_date() == null || param.getCustom_date().size() == 0) {
                    op.put("day",param.getSdate()+"至"+param.getEdate());
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 合作方产品收支导出
     * @param dto 查询参数
     * @param request HTTP请求
     * @param response HTTP响应
     */
    @Override
    public void export(PartnerInvestGroupDTO dto, HttpServletRequest request, HttpServletResponse response) {

        String token = dto.getToken() != null ? dto.getToken() : request.getParameter("token");
        CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);
        dto.setCuser(cuser.getLogin_name());
        // 不为管理员用户 则添加应用控制
        if(!"root".equals(cuser.getOrg_id())){
            if(!BlankUtils.checkBlank(cuser.getApp_group())){
                dto.setApps(cuser.getApp_group());
            }
        }

        //自定义时段
        List<Map<String,String>> reportList = new ArrayList<>();
        if (dto.getCustom_date() != null && dto.getCustom_date().size() > 0) {
            for (int i = 0;i < dto.getCustom_date().size();i+=2) {
                dto.setSdate(dto.getCustom_date().get(i));
                dto.setEdate(dto.getCustom_date().get(i+1));
                reportList.addAll(partnerInvestGroupMapper.selectPartnerInvestGroup(dto));
            }
        }else {
            reportList = partnerInvestGroupMapper.selectPartnerInvestGroup(dto);
        }

        // 调整为日期格式
        convertDateStr(reportList,dto);

        // 处理查询结果
        processQueryResults(reportList);

        // 转为List<Map<String, Object>> 格式
        List<Map<String, Object>> contentList = new ArrayList<>();
        for (Map<String, String> map : reportList) {
            Map<String, Object> obj = new HashMap<>();
            obj.putAll(map);
            contentList.add(obj);
        }

        // 处理表头
        Map<String, String> headerMap = BlankUtils.processExportHeader(dto.getValue());

        // 导出Excel
        String fileName = dto.getExport_file_name() + DateTime.now().toString("yyyyMMdd") + ".xlsx";
        ExportExcelUtil.exportXLSX(response, contentList, headerMap, fileName);
    }


    /**
     * 处理投放数据
     * @param handle 操作类型
     * @param dto 参数集合
     * @return 操作结果
     * @throws Exception 异常
     */
    @Override
    public String handle(String handle,PartnerInvestGroupDTO dto) throws Exception {
        int result = 0;
        try {
            if("edit".equals(handle)){

                result = partnerInvestGroupMapper.updateReduceAmount(dto);
            }

            if(result > 0)
                return ReturnJson.success("操作成功!");
            else
                return ReturnJson.toErrorJson("无效操作!");

        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("操作失败!");
        }
    }

    /**
     * 同步投放金额
     */
    @Override
    public String sync(PartnerInvestGroupDTO dto) {
        if(BlankUtils.sqlValidate(dto.getSdate()) || BlankUtils.checkBlank(dto.getSdate()))
            return ReturnJson.toErrorJson("同步日期不能为空！");
        
        CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(dto.getToken());

        try{
            int resp = partnerInvestGroupMapper.syncDataFromDnReport(dto);
            if(resp > 0)
                return ReturnJson.success("同步成功！");
            else
                return ReturnJson.toErrorJson("同步失败，请稍后重试！");
        }catch(Exception e){
            e.printStackTrace();
            return ReturnJson.toErrorJson("出现错误，请联系管理员排查！");
        }
    }


    /**
     * 处理用户权限
     * @param cuser 当前用户
     * @param paramMap 参数Map
     * @return 结果Map
     */
    private Map<String, Object> processUserPermissions(CurrUserVo cuser, Map<String, String> paramMap) {
        Map<String, Object> result = new JSONObject();
        
        if(cuser.getCompany() != null && !"1".equals(cuser.getCompany())){
            // 外部合作方账号带审核功能
            paramMap.put("check", "ok");
        }

        // 不为管理员用户 则添加应用控制
        if(!"root".equals(cuser.getOrg_id())){
            result.put("appList", new String[0]);
            
            if(!BlankUtils.checkBlank(cuser.getApp_group())){
                paramMap.put("apps", cuser.getApp_group());
                result.put("appList", cuser.getApp_group().split(","));
            }
        }else{
            result.put("appList", new String[0]);
        }
        
        return result;
    }


    /**
     * 处理查询结果
     * @param list 查询结果列表
     */
    private void processQueryResults(List<Map<String, String>> list) {
        // 应用名称赋值
        Map<String, Map<String, Object>> appMap = adService.getAppInfoMap();
        // 应用分类名称赋值
        List<AppCategory> appCategorys = someService.getAppCategorys();
        Map<String, String>appCategoryMap = new HashMap<>();
        appCategorys.stream().forEach(appCategory -> {
            appCategoryMap.put(appCategory.getId().toString(), appCategory.getName());
        });
        list.forEach(act -> {

            Map<String, Object> app = appMap.get(act.get("appid")+"");
            if(app != null)
                act.put("appname", app.get("app_name")+"");

            String appCategory = appCategoryMap.get(act.get("app_category") + "");
            if(appCategory != null)
                act.put("app_category", appCategory);
        });
    }


}