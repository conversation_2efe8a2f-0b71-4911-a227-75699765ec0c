package com.wbgame.service.advert.impl;

import com.wbgame.mapper.adb.DnwxBiMapper;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wbgame.service.AdService;
import com.wbgame.service.BigdataService;
import com.wbgame.service.advert.DnwxReyunConfigService;

import java.util.List;
import java.util.Map;

@Service
public class DnwxReyunConfigServiceImpl implements DnwxReyunConfigService {
    
    @Autowired
    private DnwxBiMapper dnwxBiMapper;

    @Override
    public List<Map<String, Object>> queryBehaviorAchievement(Map<String, String> paramMap) {
        StringBuilder sql = 
            new StringBuilder("select tdate,sdk_type,appid,download_channel cha_id,pid,ad_buy_channel buy_id,account buy_act,reyun_event `event`,reg_users,act_users,user_cnt,event_cnt,CONCAT(IFNULL(TRUNCATE(user_cnt/reg_users*100,2),0),'%') user_rate from dnwx_bi.ads_key_behavior_achievement_hourly where 1=1 ");
        
        if(!BlankUtils.checkBlank(paramMap.get("start_date")) && !BlankUtils.checkBlank(paramMap.get("end_date")))
            sql.append(" and tdate BETWEEN #{obj.start_date} and #{obj.end_date}");
        if(!BlankUtils.checkBlank(paramMap.get("appid")))
            sql.append(" and appid = #{obj.appid}");

        if(!BlankUtils.checkBlank(paramMap.get("cha_id")))
            sql.append(" and download_channel = #{obj.cha_id}");
        if(!BlankUtils.checkBlank(paramMap.get("pid")))
            sql.append(" and pid = #{obj.pid}");

        if(!BlankUtils.checkBlank(paramMap.get("buy_id")))
            sql.append(" and ad_buy_channel = #{obj.buy_id}");
        if(!BlankUtils.checkBlank(paramMap.get("buy_act")))
            sql.append(" and account = #{obj.buy_act}");
        if(!BlankUtils.checkBlank(paramMap.get("event")))
            sql.append(" and reyun_event = #{obj.event}");
        if(!BlankUtils.checkBlank(paramMap.get("sdk_type")))
            sql.append(" and sdk_type = #{obj.sdk_type}");

        sql.append(" order by tdate desc,reg_users desc");

        return dnwxBiMapper.queryListMapTwo(sql.toString(), paramMap);
    }
}