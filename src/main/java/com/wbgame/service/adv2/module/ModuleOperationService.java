package com.wbgame.service.adv2.module;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.adv2.AdvCommonAdCodeMapper;
import com.wbgame.pojo.adv2.adcodeModule.*;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.wbgame.controller.adv2.AdCodeAppConfigController.*;

//import static com.wbgame.controller.adv2.AdCodeAppConfigController.CSJ_PLATFORM;

/**
 * <AUTHOR>
 * @date 2023/7/3
 * @description
 **/
@Service
public class ModuleOperationService implements InitializingBean {

    @Resource
    private AdvCommonAdCodeMapper advCommonAdCodeMapper;

    public void deleteGroup(String groupName) {
        advCommonAdCodeMapper.deleteModuleGroup(groupName, null);
    }

    //  ########################  模板组

    @Resource
    private BaseModuleService csjModuleService;
    @Resource
    private BaseModuleService ksModuleService;
    @Resource
    private BaseModuleService oppoModuleService;
    @Resource
    private BaseModuleService ylhModuleService;
    @Resource
    private BaseModuleService vivoModuleService;
    @Resource
    private BaseModuleService xiaomiModuleService;
    public final Map<String, BaseModuleService> moduleServices = new HashMap<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        moduleServices.put(CSJ_PLATFORM, csjModuleService);
        moduleServices.put(KUAISHOU_PLATFORM, ksModuleService);
        moduleServices.put(YLH_PLATFORM, ylhModuleService);
        moduleServices.put(OPPO_PLATFORM, oppoModuleService);
        moduleServices.put(VIVO_PLATFORM, vivoModuleService);
        moduleServices.put(XIAOMI_PLATFORM, xiaomiModuleService);
    }

    private static void platformIdMapExtract(List<Object> voList, HashMap<String, List<Integer>> platformMap) {
        for (Object o : voList) {
            JSONObject obj = (JSONObject) o;
            String platform = obj.getString("platform");
            int id = obj.getIntValue("id");
            platformMap.putIfAbsent(platform, new ArrayList<>());
            platformMap.get(platform).add(id);
        }
    }

    public Map<String, List<Map<String, Object>>> findModuleGroup(String groupName) {

        List<ModuleGroupDto> groups = advCommonAdCodeMapper.selectModuleGroup(groupName);
        Set<String> names = groups.stream().map(ModuleGroupDto::getGroup_name).collect(Collectors.toSet());
        Map<String, List<ModuleGroupDto>> collect = groups.stream().collect(Collectors.groupingBy(ModuleGroupDto::getPlatform));

        HashMap<String, List<Map<String, Object>>> res = new HashMap<>();
        collect.forEach((platform, list) ->{
            for (String name : names) {
                List<Integer> ids = list.stream().filter(x-> x.getGroup_name().equals(name))
                        .map(ModuleGroupDto::getModule_id).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(ids)) {
                    continue;
                }
                res.putIfAbsent(name, new ArrayList<>());
                // TODO: 在所有平台中查找模板
                List<Map<String, Object>> maps = moduleServices.getOrDefault(platform, new EmptyModuleService()).selectModuleById(ids);
                res.get(name).addAll(maps);
            }
        });

        return res;
    }

    public void deleteGroupModule(String groupName, List<Object> voList) {
        ArrayList<HashMap<String, Object>> res = new ArrayList<>();
        voList.stream().map(o -> ((JSONObject) o)).forEach(map -> {
            HashMap<String, Object> obj = new HashMap<>();
            String platform = map.getString("platform");
            Integer id = map.getIntValue("id");
            obj.put("platform", platform);
            obj.put("id", id);
            res.add(obj);
        });
        advCommonAdCodeMapper.deleteModuleGroup(groupName, res);
    }

    public String createGroup(String groupName, List<Object> voList) {
        ArrayList<ModuleGroupDto> groups = new ArrayList<>();
        for (Object o : voList) {
            JSONObject obj = (JSONObject) o;
            String platform = obj.getString("platform");
            int id = obj.getIntValue("id");
            groups.add(new ModuleGroupDto(groupName, platform, id));
        }
        List<ModuleGroupDto> moduleGroupDtos = advCommonAdCodeMapper.selectModuleGroup(groupName);
        HashSet<ModuleGroupDto> dtos = new HashSet<>(moduleGroupDtos);
        List<ModuleGroupDto> collect = groups.stream().distinct()
                .filter(x -> !dtos.contains(x)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return ReturnJson.success();
        }
        return ReturnJson.success("成功 " + advCommonAdCodeMapper.createModuleGroup(collect));
    }
    //  ########################  模板
    public int createCSJModule(CSJAdcodeModuleVo vo) {
        return advCommonAdCodeMapper.insertCSJModule(vo);
    }

    public List<Map<String, Object>> findCSJModule() {
        return advCommonAdCodeMapper.selectCSJModule();
    }

    public int delCSJModule(Integer id) {
        return advCommonAdCodeMapper.deleteCSJModule(id);
    }

    public int createYLHModule(YLHAdcodeModuleVo vo) {
        return advCommonAdCodeMapper.insertYLHModule(vo);
    }

    public List<Map<String, Object>> findYLHModule() {
        return advCommonAdCodeMapper.selectYLHModule(null);
    }

    public int delYLHModule(Integer id) {
        return advCommonAdCodeMapper.deleteYLHModule(id);
    }

    public int updateYLHModule(YLHAdcodeModuleVo vo) {
        return advCommonAdCodeMapper.updateYLHModule(vo);
    }

    public int createKSModule(KSAdcodeModuleVo vo) {
        return advCommonAdCodeMapper.insertKSModule(vo);
    }

    public List<Map<String, Object>> findKSModule() {
        return advCommonAdCodeMapper.selectKSModule(null);
    }

    public int delKSModule(Integer id) {
        return advCommonAdCodeMapper.deleteKSModule(id);
    }

    public int updateKSModule(KSAdcodeModuleVo vo) {
        return advCommonAdCodeMapper.updateKSModule(vo);
    }

    public int updateCSJModule(CSJAdcodeModuleVo vo) {
        return advCommonAdCodeMapper.updateCSJModule(vo);
    }

    public List<Map<String, Object>> findOppoModule() {
        return advCommonAdCodeMapper.selectOppoModule(null);
    }

    public int createOppoModule(OppoAdcodeModuleVo vo) {
        return advCommonAdCodeMapper.insertOppoModule(vo);
    }

    public int updateOppoModule(OppoAdcodeModuleVo vo) {
        return advCommonAdCodeMapper.updateOppoModule(vo);
    }

    public int delOppoModule(Integer id) {
        return advCommonAdCodeMapper.deleteOppoModule(id);
    }
}
