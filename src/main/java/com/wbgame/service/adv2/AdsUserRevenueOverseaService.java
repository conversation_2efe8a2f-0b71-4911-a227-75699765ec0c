package com.wbgame.service.adv2;

import com.wbgame.pojo.adv2.bigdata.UserRevenueOverseaParam;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/6/4 15:59
 */
public interface AdsUserRevenueOverseaService {

    /**
     * 根据条件查询新增/活跃用户展示分布数据
     *
     * @param param 查询条件
     * @return 查询结果
     */
    String selectAdshowActuser(UserRevenueOverseaParam param);


    void exportAdshowActuser(HttpServletResponse response, UserRevenueOverseaParam param);
}
