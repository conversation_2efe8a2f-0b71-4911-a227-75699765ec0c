package com.wbgame.service.adv2.tobid;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.wbgame.common.ReturnJson;
import com.wbgame.controller.adv2.CSJBatchAdCodeController;
import com.wbgame.controller.adv2.ExAdConfigController;
import com.wbgame.controller.adv2.KSAdCodeController;
import com.wbgame.mapper.adv2.Adv2Mapper;
import com.wbgame.pojo.adv2.*;
import com.wbgame.service.adv2.adcode.SigmobAdCodeService;
import com.wbgame.service.adv2.adcode.YLHAdcodeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpClientUtils;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;
import static com.wbgame.controller.adv2.ExAdConfigController.OPEN_AD_TYPE;
import static com.wbgame.controller.adv2.ExAdConfigController.SDK_AD_TYPE;

/**
 * <AUTHOR>
 * @date 2023/9/6
 * @description
 **/
@Service
@Slf4j
public class ToBidGroupService {
    public static final String GROUP_CREATE_URL = "https://mmapi.sigmob.com/srv/open/api/union_mediation/group/create";
    public static final String GROUP_UPDATE_URL = "https://mmapi.sigmob.com/srv/open/api/union_mediation/group/update";
    public static final String GROUP_LIST_URL = "https://mmapi.sigmob.com/srv/open/api/union_mediation/group/list";
    public static final String ADSOURCE_CREATE_URL = "https://mmapi.sigmob.com/srv/open/api/union_mediation/ad_source/create";
    public static final String ADSOURCE_LIST_URL = "https://mmapi.sigmob.com/srv/open/api/union_mediation/ad_source/list";
    public static final String WATERFALL_UPDATE_URL = "https://mmapi.sigmob.com/srv/open/api/union_mediation/waterfall/update";
    public static final String WATERFALL_LIST_URL = "https://mmapi.sigmob.com/srv/open/api/union_mediation/waterfall/list";
    public static final String UNION_MEDIA_APP_LIST_URL = "https://mmapi.sigmob.com/srv/open/api/union_mediation/app/list";
    public static final String UNION_MEDIA_APP_AGG_LIST_URL = "https://mmapi.sigmob.com/srv/open/api/union_mediation/media_app/list";
    public static final String UNION_MEDIA_APP_AGG_CREATE_URL = "https://mmapi.sigmob.com/srv/open/api/union_mediation/media_app/create";
    public static final String UNION_MEDIA_AD_POS_LIST_URL = "https://mmapi.sigmob.com/srv/open/api/union_mediation/placement/list";
    public static final String UNION_MEDIA_AD_POS_CREATE_URL = "https://mmapi.sigmob.com/srv/open/api/union_mediation/placement/create";
    public static final String SIGMOB_ACCOUNT = "Sigmob-Sigmob";
    public static final String CSJ_ACCOUNT = "穿山甲-飞鸟";
    public static final String YLH_ACCOUNT = "腾讯广告-统掌";
    public static final String KS_ACCOUNT = "快手-动能";
    public static final String AD_MOB_ACCOUNT = "AdMob-dongneng";
    public static final String TOBID = "tobid";

    @Resource
    private SigmobAdCodeService sigmobAdCodeService;
    @Resource
    private YLHAdcodeService YLHAdcodeService;
    @Resource
    private CSJBatchAdCodeController CSJBatchAdCodeController;
    @Resource
    private KSAdCodeController KSAdCodeController;
    @Resource
    private Adv2Mapper adv2Mapper;
    @Resource
    private ExAdConfigController exAdConfigController;

    private Map<String, Integer> accountAdNetworkIdMap = new HashMap<String, Integer>() {{
        put(SIGMOB_ACCOUNT, 2037);
        put(CSJ_ACCOUNT, 2126);
        put(YLH_ACCOUNT, 2127);
        put(KS_ACCOUNT, 2128);
        put(AD_MOB_ACCOUNT, 2323);
    }};
    private Map<String, Integer> accountChannelMap = new HashMap<String, Integer>() {{
        put(SIGMOB_ACCOUNT, 9);
        put(CSJ_ACCOUNT, 13);
        put(YLH_ACCOUNT, 16);
        put(KS_ACCOUNT, 19);
        put(AD_MOB_ACCOUNT, 11);
    }};
    private Map<String, Integer> accountContentMap = new HashMap<String, Integer>() {{
        put(SIGMOB_ACCOUNT, 9);
        put(CSJ_ACCOUNT, 13);
        put(YLH_ACCOUNT, 16);
        put(KS_ACCOUNT, 19);
        put(AD_MOB_ACCOUNT, 11);
    }};

    private static final Map<String, Integer> adTypeMap = new HashMap<String, Integer>() {{
        put("msg", 5);
        put("splash", 2);
        put("video", 1);
        put("plaque", 4);
        put("banner", 7);
    }};

    private static final Map<String, Integer> adTypeUMap = new HashMap<String, Integer>() {{
        put("原生", 5);
        put("开屏", 2);
        put("激励", 1);
        put("插屏", 4);
        put("横幅", 7);
    }};

    /**
     * app 查询， 查出全部app
     *
     * @return 所有app信息
     * @throws IOException
     * @throws URISyntaxException
     * @throws NoSuchAlgorithmException
     */
    public JSONArray unionMediaAppList() throws IOException, URISyntaxException, NoSuchAlgorithmException {

        String s = doRequest(UNION_MEDIA_APP_LIST_URL, Lists.newArrayList());
        JSONObject jsonObject = JSONObject.parseObject(s);
        if (jsonObject == null || jsonObject.getIntValue("code") != 200) {
            log.info("tobid app 拉取失败, {}", s);
            return null;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        return data.getJSONArray("content");
    }

    /**
     * <p>查询应用聚合配置, 通过 appid 和 account获取，account 从定义的两个map里面找到对应的 聚合管理id
     * <p>这个id 主要应用在 创建广告源的 media_network_id 参数上</p>
     *
     * @return 所有聚合app信息
     * @throws IOException
     * @throws URISyntaxException
     * @throws NoSuchAlgorithmException
     */
    public Integer unionMediaAppGroupList(String app_id, String account) {
        Integer channel = accountChannelMap.get(account);
        Integer adNetworkId = accountAdNetworkIdMap.get(account);
        ImmutablePair<String, Object> channel_id = new ImmutablePair<>("channel_id", channel);
        ImmutablePair<String, Object> app_id1 = new ImmutablePair<>("app_id", app_id);
        String s = null;
        try {
            s = doRequest(UNION_MEDIA_APP_AGG_LIST_URL, Lists.newArrayList(app_id1, channel_id));
        } catch (IOException | URISyntaxException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        JSONObject jsonObject = JSONObject.parseObject(s);
        if (jsonObject == null || jsonObject.getIntValue("code") != 200) {
            log.info("tobid app 聚合拉取失败, {}", s);
            return null;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray jsonArray = data.getJSONArray("content");
        for (Object o : jsonArray) {
            JSONObject map = (JSONObject) o;
            if (adNetworkId == map.getIntValue("ad_network_id")) {
                return map.getIntValue("id");
            }
        }
        return null;
    }

    /**
     * <p>查询应用聚合配置
     * <p>这个id 主要应用在 创建广告源的 media_network_id 参数上</p>
     *
     * @return 所有聚合app信息
     * @throws IOException
     * @throws URISyntaxException
     * @throws NoSuchAlgorithmException
     */
    public JSONArray unionMediaAppAggListNoAccount(String app_id) {

        ImmutablePair<String, Object> app_id1 = new ImmutablePair<>("app_id", app_id);
        String s = null;
        try {
            s = doRequest(UNION_MEDIA_APP_AGG_LIST_URL, Lists.newArrayList(app_id1));
        } catch (IOException | URISyntaxException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        JSONObject jsonObject = JSONObject.parseObject(s);
        if (jsonObject == null || jsonObject.getIntValue("code") != 200) {
            log.info("tobid app 聚合拉取失败, {}", s);
            return null;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray jsonArray = data.getJSONArray("content");
        return jsonArray;
    }

    /**
     * 新建应用聚合配置
     *
     * @param app_id   tobid 的 app_id
     * @param account  对应平台账户
     * @param tappid   对应平台appid
     * @param tappname 平台应用名，ks平台需要使用, 其他平台直接null就可以
     * @return
     */
    public JSONObject unionMediaAppAggCreate(String app_id, String account, String tappid, String tappname) {
        HashMap<String, Object> map = new HashMap<>();
        Integer networkId = accountAdNetworkIdMap.get(account);
        map.put("app_id", app_id);
        map.put("ad_network_id", networkId);
        if (account.equals(KS_ACCOUNT)) {
            ToBidRequestV2.KsAppAuthContent ksAppAuthContent = new ToBidRequestV2.KsAppAuthContent();
            ksAppAuthContent.appId = tappid;
            ksAppAuthContent.appName = tappname;
            map.put("app_auth_content", ksAppAuthContent);
        } else {
            ToBidRequestV2.AppAuthContent appAuthContent = new ToBidRequestV2.AppAuthContent();
            appAuthContent.appId = tappid;
            map.put("app_auth_content", appAuthContent);
        }

        String s = null;
        try {
            s = ToBidRequestV2.commonRequest(map, UNION_MEDIA_APP_AGG_CREATE_URL);
        } catch (IOException | URISyntaxException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        JSONObject jsonObject = JSONObject.parseObject(s);
        if (jsonObject == null || jsonObject.getIntValue("code") != 200) {
            log.info("tobid app 聚合创建失败, {}", s);
            return null;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        return data;
    }

    /**
     * 获取 appid 对应的广告位
     *
     * @param app_id 平台appid
     * @return jsonArray 广告位list
     * @throws IOException
     * @throws URISyntaxException
     * @throws NoSuchAlgorithmException
     */
    public JSONArray unionMediaAdPosList(String app_id) throws IOException, URISyntaxException, NoSuchAlgorithmException {

        String s = doRequest(   UNION_MEDIA_AD_POS_LIST_URL, Lists.newArrayList(new ImmutablePair<>("app_id", app_id)));
        JSONObject jsonObject = JSONObject.parseObject(s);
        if (jsonObject == null || jsonObject.getIntValue("code") != 200) {
            log.info("tobid 广告位 拉取失败, {}", s);
            return null;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        return data.getJSONArray("content");
    }

    /**
     * 创建聚合广告位
     *
     * @param adPosName 广告位名称
     * @param app_id    平台appid
     * @param ad_type   广告位类型 激励：1，开屏：2，插屏：4，原生：5，横幅：7
     * @return
     * @throws IOException
     * @throws URISyntaxException
     * @throws NoSuchAlgorithmException
     */
    public JSONObject unionMediaAdPosCreate(String adPosName, String app_id, Integer ad_type) throws IOException, URISyntaxException, NoSuchAlgorithmException {

        String s = doRequest(UNION_MEDIA_AD_POS_CREATE_URL, Lists.newArrayList(
                new ImmutablePair<>("name", adPosName),
                new ImmutablePair<>("app_id", app_id),
                new ImmutablePair<>("ad_type", ad_type)
        ));
        JSONObject jsonObject = JSONObject.parseObject(s);
        if (jsonObject == null || jsonObject.getIntValue("code") != 200) {
            log.info("tobid 广告位 拉取失败, {}", s);
            return ReturnJson.failed("创建广告位失败" + jsonObject.getString("msg"));
        }
        return ReturnJson.successJSON(jsonObject.getJSONObject("data").get("pub_code"));
    }

    /**
     * 查询聚合广告位分组列表
     *
     * @param pub_code 聚合广告位code
     * @return 返回分组列表
     * @throws IOException
     * @throws URISyntaxException
     * @throws NoSuchAlgorithmException
     */
    public JSONArray unionMediaGroupList(String pub_code) throws IOException, URISyntaxException, NoSuchAlgorithmException {

        String s = doRequest(GROUP_LIST_URL, Lists.newArrayList(new ImmutablePair<>("pub_code", pub_code)));
        JSONObject jsonObject = JSONObject.parseObject(s);
        if (jsonObject == null || jsonObject.getIntValue("code") != 200) {
            log.info("tobid 广告位 拉取失败, {}", s);
            return null;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        return data.getJSONArray("content");
    }

    /**
     * 创建流量分组，当前规则固定
     *
     * @param groupName 分组名
     * @param pub_code  聚合广告位code
     * @return 是否成功
     */
    public boolean unionMediaGroupCreate(String groupName, String pub_code) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("rules", Lists.newArrayList(ToBidRequestV2.rule));
        params.put("name", groupName);
        params.put("pub_code", pub_code);
        try {
            String ret = ToBidRequestV2.commonRequest(params, GROUP_CREATE_URL);
            JSONObject jsonObject = JSONObject.parseObject(ret);
            if (jsonObject == null || jsonObject.getIntValue("code") != 200) {
                log.info("tobid 广告组 创建失败, {}", ret);
                return false;
            }
            return true;
        } catch (IOException | URISyntaxException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 流量分组开关，当前规则固定  TODO 有问题
     *
     * @param groupName 分组名
     * @param group_id  分组id
     * @param enabled   是否启用 0：未启用，1：启用
     * @return 是否成功
     */
    public boolean unionMediaGroupSwitch(int group_id, String groupName, int enabled) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("rules", Lists.newArrayList(ToBidRequestV2.rule));
        params.put("name", groupName);
        params.put("enabled", enabled);
        params.put("preset", enabled);
//        params.put("pub_code", pub_code);
        params.put("id", group_id);
        try {
            String ret = ToBidRequestV2.commonRequest(params, GROUP_UPDATE_URL);
            JSONObject jsonObject = JSONObject.parseObject(ret);
            if (jsonObject == null || jsonObject.getIntValue("code") != 200) {
                log.info("tobid 广告组 开关失败, {}", ret);
                return false;
            }
            return true;
        } catch (IOException | URISyntaxException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 用目标广告位id 创建tobid 广告源
     *
     * @param placementName         聚合广告位名
     * @param pub_code              聚合广告位id
     * @param account               账户， 在定义中的map中
     * @param appid                 tobid中的appid
     * @param header_bidding_switch 是否是应用内竞价广告源。 0:不是，1:是。创建成功后不可编辑 注意:Sigmob渠道无需填写
     * @param bidding_method        应用内竞价模式枚举值: 0:服务端竞价 1:客户端竞价， 当广告网络同时支持两种竞价方式且 header_bidding_switch=1 时必填。 广告网络是否支持应用内竞价，参考广告网络枚举
     * @param placement_id          第三方广告源的广告位id
     * @param price                 瀑布流中的排序价格，单位分。 应用内竞价广告源无须填写
     * @return
     */
    public Object toBidAdSourceCreate(String placementName, String pub_code, String account, String appid,
                                      Integer header_bidding_switch, Integer bidding_method,
                                      String placement_id, Integer price) {
        HashMap<String, Object> paraMap = new HashMap<>();
        paraMap.put("name", placementName);
        paraMap.put("pub_code", pub_code);
        Integer media_network_id = unionMediaAppGroupList(appid, account);
        if (media_network_id == null) {
            return "该账户并没有在这个应用下创建应用聚合配置";
        }
        paraMap.put("media_network_id", media_network_id);
        if (header_bidding_switch != null) {
            paraMap.put("header_bidding_switch", header_bidding_switch);
        }
        if (bidding_method != null) {
            paraMap.put("bidding_method", bidding_method);
        }
        if (price != null && price != 0) {
            paraMap.put("price", price);
        }
        ToBidRequestV2.SourceAuthContent sourceAuthContent = new ToBidRequestV2.SourceAuthContent();
        sourceAuthContent.placementId = placement_id;
        paraMap.put("source_auth_content", sourceAuthContent);

        String s = null;
        try {
            s = ToBidRequestV2.commonRequest(paraMap, ADSOURCE_CREATE_URL);
        } catch (IOException | URISyntaxException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }

        JSONObject jsonObject = JSONObject.parseObject(s);
        int code = jsonObject.getIntValue("code");
        if (code != 200) {
            return jsonObject.getString("msg");
        }
        return jsonObject.getJSONObject("data").getIntValue("id");
    }

    /**
     * 修改广告源在瀑布流的属性， 目前修改是否开启
     *
     * @param ad_source_id ToBid 体系内的广告源自增id，即查询广告源列表返回的id
     * @param group_id     流量分组ID，即查询流量分组中返回的id
     * @param enabled      是否在当前流量分组内启用。 0: 不启用 1: 启用
     * @return 修改成功
     */
    public JSONObject toBidAdSourceStatusUpdate(int ad_source_id, int group_id, int enabled) {
        HashMap<String, Object> paraMap = new HashMap<>();
        paraMap.put("ad_source_id", ad_source_id);
        paraMap.put("group_id", group_id);
        paraMap.put("status", 0);
        paraMap.put("enabled", enabled);

        String s = null;
        try {
            s = ToBidRequestV2.commonRequest(paraMap, WATERFALL_UPDATE_URL);
        } catch (IOException | URISyntaxException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }

        JSONObject jsonObject = JSONObject.parseObject(s);
        int code = jsonObject.getIntValue("code");
        if (code != 200) {
            return ReturnJson.failed(jsonObject.getString("msg"));
        }
        return ReturnJson.successJSON("成功");
    }

    /**
     * 该分组下广告源的开关状态列表
     *
     * @param group_id 分组id
     * @return
     */
    public Object toBidAdSourceStatusList(int group_id) {
        HashMap<String, Object> paraMap = new HashMap<>();
        paraMap.put("group_id", group_id);
        paraMap.put("status", 0);

        String s = null;
        try {
            s = ToBidRequestV2.commonRequest(paraMap, WATERFALL_LIST_URL);
        } catch (IOException | URISyntaxException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }

        JSONObject jsonObject = JSONObject.parseObject(s);
        int code = jsonObject.getIntValue("code");
        if (code != 200) {
            return ReturnJson.toErrorJson(jsonObject.getString("msg"));
        }

        JSONArray array = new JSONArray();
        JSONArray closeArray = jsonObject.getJSONObject("data").getJSONArray("free_ad_source_list");
        if (!CollectionUtils.isEmpty(closeArray)) {
            array.addAll(closeArray);
        }
        JSONArray openArray = jsonObject.getJSONObject("data").getJSONArray("ad_source_list");
        if (!CollectionUtils.isEmpty(openArray)) {
            array.addAll(openArray);
        }
        return ReturnJson.success(array);
    }

    /**
     * 根据定义的账户，转化vo 为对应创建广告位的vo，绑定广告源
     *
     * @param
     * @return
     */
    public Object toBidAdSourceCreate(String placementName, String pub_code, String account, String appid,
                                      Integer header_bidding_switch, Integer bidding_method,
                                      Integer price, Object obj) {

        JSONObject ret = null;
        String username = LOGIN_USER_NAME.get();
        if (SIGMOB_ACCOUNT.equals(account)) {
            SigmobAdVo vo = (SigmobAdVo) obj;
            vo.setCreateUser(username);
            vo.setModifyUser(username);
            placementName = "sigmob_" + placementName;
            ret = (JSONObject) sigmobAdCodeService.createAdCode(vo, true);
        } else if (YLH_ACCOUNT.equals(account)) {
            YLHAdcodeVo vo = (YLHAdcodeVo) obj;
            String adcode = YLHAdcodeService.createAdcode(vo, username, true);
            placementName = "ylh_" + placementName;
            ret = JSONObject.parseObject(adcode);
        } else if (KS_ACCOUNT.equals(account)) {
            KSAdcodeVo vo = (KSAdcodeVo) obj;
            placementName = "ks_" + placementName;
            ret = (JSONObject) KSAdCodeController.handleAdcdoe(username, vo, true);
        } else if (CSJ_ACCOUNT.equals(account)) {
            CSJAdcodeVo vo = (CSJAdcodeVo) obj;
            placementName = "csj_" + placementName;
            ret = (JSONObject) CSJBatchAdCodeController.handleAdcdoe(username, vo, true);
        }

        if (ret == null) {
            return ReturnJson.failed("广告位创建失败");
        }
        if (ret.getIntValue("ret") != 1) {
            return ret;
        }
        // 广告位创建成功，在广告源管理表也加上了相应的记录

        // 广告位
        String code = ret.getString("code");
        // 广告源管理表id
        String adsid = ret.getString("adsid");

        // 绑定到聚合广告位中
        Object aggRet = toBidAdSourceCreate(placementName, pub_code, account, appid,
                header_bidding_switch, bidding_method, code, price);
        // 失败处理
        if (aggRet == null || !StringUtils.isNumeric(aggRet.toString())) {
            String msg = aggRet == null ? null : aggRet.toString();
            return ReturnJson.failed("广告位创建成功，但绑定聚合广告位失败" + msg);
        }
        // 绑定广告位成功， 广告源管理表中更新tobid_id 字段，绑定到广告源，方便后续查询聚合广告源
        adv2Mapper.updateExtendAdsidTobidId(aggRet.toString(), adsid);

        return ReturnJson.successJSON();
    }

    /**
     * 请求聚合广告位下的广告源，拼接本地存储的广告源信息
     *
     * @param pubCode 聚合广告位
     * @return
     */
    public Object toBidAdSourceList(String pubCode) {
        try {
            String s = doRequest(ADSOURCE_LIST_URL, Lists.newArrayList(new ImmutablePair<>("pub_code", pubCode)));
            JSONObject jsonObject = JSONObject.parseObject(s);
            if (jsonObject == null || jsonObject.getIntValue("code") != 200) {
                return ReturnJson.toErrorJson(jsonObject == null ? null : jsonObject.getString("msg"));
            }
            JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("content");
            List<Integer> ids = jsonArray.stream().map(o -> (JSONObject) o)
                    .map(map -> map.getIntValue("id")).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(ids)) {
                return ReturnJson.toErrorJson("没有获取到有效的聚合广告源");
            }

            String tobidIds = ids.stream().map(id -> "\"" + id + "\"").reduce((x, y) -> x + "," + y).orElse(null);
            if (BlankUtils.isBlank(tobidIds)) {
                return ReturnJson.toErrorJson("tobid id 获取失败" + ids);
            }
            ArrayList<ExtendAdsidVo> ret = new ArrayList<>();
            List<ExtendAdsidVo> publist = adv2Mapper.selectExtendAdsidByTobidId(null, pubCode);
            if (!CollectionUtils.isEmpty(publist)) {
                ret.addAll(publist);
            }
            List<ExtendAdsidVo> voList = adv2Mapper.selectExtendAdsidByTobidId(tobidIds, null);
            if (!CollectionUtils.isEmpty(voList)) {
                ret.addAll(voList);
            }
            return ReturnJson.success(ret);
        } catch (IOException | URISyntaxException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws IOException, URISyntaxException, NoSuchAlgorithmException {

        ToBidGroupService toBidGroupService = new ToBidGroupService();
//        toBidGroupService.unionMediaAdPosList("3302");
//        toBidGroupService.unionMediaGroupList("****************");
//        toBidGroupService.unionMediaAppGroupList("31227", "快手-动能");
//        toBidGroupService.toBidAdSourceStatusUpdate(259086, 11570, 1);
//        toBidGroupService.toBidAdSourceStatusList(11570);
//        toBidGroupService.toBidAdSourceCreate("测试广告位1", "****************", "Sigmob-Sigmob",
//                "23207", null, null, "f1d423fa75c", 20);
//        toBidGroupService.toBidAdSourceCreate("测试广告位4", "****************", KS_ACCOUNT,
//                "23207", 1, 1, "**********", null);
        JSONArray array = toBidGroupService.unionMediaAdPosList("3302");
        Map<Integer, String> collect = array.stream().map(o -> (JSONObject) o).collect(
                Collectors.toMap(o -> o.getInteger("ad_type"), o -> o.getString("pub_code"), (x, y) -> x));
        String openType = "msg";
        String pubcode = collect.get(adTypeMap.get(openType));
    }

    public String adSourceList(String pubCode) throws IOException, URISyntaxException, NoSuchAlgorithmException {

        ImmutablePair<String, Object> pub_code = new ImmutablePair<>("pub_code", pubCode);
        String s = doRequest(ADSOURCE_LIST_URL, Lists.newArrayList(pub_code));
        return s;
    }

    private List<Pair<String, Object>> unionParamToList(Pair<String, Object>... pairs) {
        return new ArrayList<>(Arrays.asList(pairs));
    }

    /**
     * @param url    接口地址
     * @param params 接口需要的业务参数对（不包含公共参数_user,_t,_sign这三个用于签名校验的参数）
     * @throws IOException
     * @throws URISyntaxException
     * @throws NoSuchAlgorithmException
     */
    public static String doRequest(String url, List<Pair<String, Object>> params)
            throws IOException, URISyntaxException, NoSuchAlgorithmException {

        // 根据传入参数，创建http request参数对
        List<Pair<String, Object>> requestParams = new ArrayList<>(params);

        // 除了业务需要的请求参数外，需要追加时间戳以及_user参数用于签名验证
        // 服务端会校验时间戳参数 timestamp，请求时间在服务器时间±5s 内请求有效
        Pair<String, Object> timestampPair = new ImmutablePair<String, Object>("_t", System.currentTimeMillis());
        Pair<String, Object> userPair = new ImmutablePair<String, Object>("_user", USER_NAME);
        requestParams.add(timestampPair);
        requestParams.add(userPair);

        // 计算签名
        String signStr = sign(requestParams);

        // 别忘了签名也是参数的一部分
        Pair<String, Object> signPair = new ImmutablePair<String, Object>("_sign", signStr);
        requestParams.add(signPair);

        // 接口接收post方式的json对象，而不是form表单。所以需要把请求参数转换成json对象
        String json = buildRequestObjectJson(requestParams);

        HashMap<String, String> header = new HashMap<>();
        header.put(HttpHeaders.CONTENT_TYPE, "application/json");

        String post = HttpClientUtils.getInstance().httpPostThrow(url, json, header);
        return post;
    }

    /**
     * 变现账户名
     */
    private static final String USER_NAME = "<EMAIL>";

    /**
     * Sigmob Secret Key，从平台账户管理->Keys功能页获取。<br/>
     * 注意，这里要使用Sigmob Secret Key，而不是ToBid Secret Key
     */
    private static final String SECRET_Key = "8a6d6b88b016f092321195d54ba97296";

    /**
     * 签名使用 SHA-1(160bit)算法生成<br/>
     *
     * @param plainText
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String sha1(String plainText) throws NoSuchAlgorithmException {
        MessageDigest messageDigest = MessageDigest.getInstance("SHA");
        byte[] cipherBytes = messageDigest.digest(plainText.getBytes());
        String cipherStr = Hex.encodeHexString(cipherBytes);
        return cipherStr;
    }

    /**
     * 获取 URL 请求的所有参数对，按参数名升序排序后，将所有参数值做字符串连接<br/>
     * 在拼接后的字符串后，再追加secret key，计算出用于签名的字符串<br/>
     *
     * @param requestParams
     * @return
     */
    public static String prepaireSignPlainText(List<Pair<String, Object>> requestParams) {
        // 先对请求参数按照参数名升序排序
        requestParams.sort(Comparator.comparing(Pair::getKey));
        // 将排序后的参数对的value值按次拼成一个字符串
        StringBuilder sb = new StringBuilder();
        requestParams.forEach(temp -> {
            sb.append(temp.getValue());
        });

        /**
         * 注意，这里别忘了追加secretKey，用于签名
         */
        sb.append(SECRET_Key);
        return sb.toString();
    }

    /**
     * 获取 URL 请求的所有参数对，按参数名升序排序后，将所有参数值做字符串连接。<br/>
     * 在拼接后的字符串后，再追加secret key，计算出用于签名的字符串<br/>
     * 对得到的字符串进行SHA-1(160bit)哈希操作得到的值即为请求签名。<br/>
     *
     * @param requestParams
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String sign(List<Pair<String, Object>> requestParams) throws NoSuchAlgorithmException {
        String plainText = prepaireSignPlainText(requestParams);
        return sha1(plainText);
    }

    public static String buildRequestObjectJson(List<Pair<String, Object>> requestParams)
            throws JsonProcessingException {
        Map<String, Object> requestMap = new HashMap<String, Object>();
        requestParams.forEach(temp -> {
            requestMap.put(temp.getKey(), temp.getValue());
        });
        ObjectMapper om = new ObjectMapper();
        return om.writeValueAsString(requestMap);
    }

    /**
     * 聚合广告位查询
     *
     * @param appId
     * @return
     */
    public Object toBidUnionAdPosList(String appId) throws IOException, URISyntaxException, NoSuchAlgorithmException {
        JSONArray array = unionMediaAdPosList(appId);
        return ReturnJson.success(selectAdsidByPubcode(array));
    }

    public List<ExtendAdsidVo> selectAdsidByPubcode(JSONArray array) {
        List<String> pubcodes = array.stream().map(o -> (JSONObject) o)
                .map(map -> map.getString("pub_code")).collect(Collectors.toList());
        return adv2Mapper.selectExtendAdsidByPubcode(pubcodes);
    }

    public Object toBidAdSourceImport(MultipartFile file) throws IOException, BiffException {

        if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
            ArrayList<Pair<Integer, String>> recordMsg = new ArrayList<>();
            Workbook workbook = Workbook.getWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheet(0);

            int column = sheet.getColumns();
            int commonCol = 3;
            int row = sheet.getRows();
            for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                    continue;
                }

                // 通用列  参数获取
                String placementName = null;
                String tobid_appid = sheet.getCell(0, r).getContents();
                String pub_code = sheet.getCell(1, r).getContents();
                String account = sheet.getCell(2, r).getContents();
                Integer header_bidding_switch = null, bidding_method = null, price = null;
                if (!accountChannelMap.containsKey(account)) {
                    recordMsg.add(new ImmutablePair<>(r, "账户字段错误，账户不存在"));
                }

                String[] value = new String[column];
                for (int c = commonCol; c < column; c++) {
                    value[c - commonCol] = sheet.getCell(c, r).getContents();
                }

                Object vo = null;
                String openType = null;
                if (SIGMOB_ACCOUNT.equals(account)) {

                } else if (YLH_ACCOUNT.equals(account)) {
                    // "1" 代表游戏
                    YLHAdcodeVo ylh = YLHAdcodeService.resolveArrayToVo("1", value);
                    if (ylh == null) {
                        recordMsg.add(new ImmutablePair<>(r, "ylh, 创建失败 参数错误"));
                        continue;
                    }
                    placementName = ylh.getPlacement_name();
                    openType = ylh.getOpen_type();
                    String priceStrategyType = ylh.getPrice_strategy_type();
                    String realTimeBiddingType = ylh.getReal_time_bidding_type();
                    if ("BiddingPrice".equals(priceStrategyType)) {
                        header_bidding_switch = 1;
                        if ("Server_Bidding".equals(realTimeBiddingType)) {
                            bidding_method = 0;
                        } else if ("Client_Bidding".equals(realTimeBiddingType)) {
                            bidding_method = 1;
                        }
                    } else if ("TargetPrice".equals(priceStrategyType) || "Default".equals(priceStrategyType)) {
                        header_bidding_switch = 0;
                        price = BlankUtils.getInt(ylh.getEcpm_price()) * 100;
                    }
                    vo = ylh;
                } else if (KS_ACCOUNT.equals(account)) {
                    // 快手批量创建每次间隔2.5秒
                    try {
                        Thread.sleep(2500);
                    }catch (Exception e){

                    }
                    KSAdcodeVo ks = KSAdCodeController.resolveArrayToVo("1", value);
                    if (ks == null) {
                        recordMsg.add(new ImmutablePair<>(r, "ks, 创建失败 参数错误"));
                        continue;
                    }
                    openType = ks.getOpen_type();
                    placementName = ks.getName();
                    String priceStrategy = ks.getPriceStrategy();
                    if ("1".equals(priceStrategy)) {
                        header_bidding_switch = 0;
                        price = BlankUtils.getInt(ks.getCpm()) * 100;
                    } else if ("2".equals(priceStrategy)) {
                        header_bidding_switch = 1;
                        String biddingType = ks.getBidding_type();
                        if ("2".equals(biddingType)) {
                            bidding_method = 0;
                        } else if ("1".equals(biddingType)) {
                            bidding_method = 1;
                        }
                    }
                    vo = ks;
                } else if (CSJ_ACCOUNT.equals(account)) {
                    CSJAdcodeVo csj = CSJBatchAdCodeController.resolveArrayToVo("1", value);
                    if (csj == null) {
                        recordMsg.add(new ImmutablePair<>(r, "csj, 创建失败 参数错误"));
                        continue;
                    }
                    openType = csj.getOpen_type();
                    placementName = csj.getAd_slot_name();
                    String biddingType = csj.getBidding_type();
                    //竞价类型 固定值 标准 0 服务端竞价 1 客户端竞价 2
                    if (biddingType.equals("1")) {
                        header_bidding_switch = 1;
                        bidding_method = 0;
                    } else if (biddingType.equals("2")) {
                        header_bidding_switch = 1;
                        bidding_method = 1;
                    } else if (biddingType.equals("0")) {
                        header_bidding_switch = 0;
                        price = BlankUtils.getInt(csj.getCpm()) * 100;
                    }
                    vo = csj;
                }

                if (BlankUtils.isBlank(pub_code)) {
                    try {
                        JSONArray array = unionMediaAdPosList(tobid_appid);
                        Map<Integer, String> collect = array.stream().map(o -> (JSONObject) o).collect(
                                Collectors.toMap(o -> o.getInteger("ad_type"), o -> o.getString("pub_code"), (x, y) -> x));
                        pub_code = collect.get(adTypeMap.get(openType));
                    } catch (URISyntaxException | NoSuchAlgorithmException e) {
                        throw new RuntimeException(e);
                    }
                }
                if (BlankUtils.isBlank(pub_code)) {
                    recordMsg.add(new ImmutablePair<>(r, "没有对应的pubcode"));
                    continue;
                }
                JSONObject ret = (JSONObject) toBidAdSourceCreate(placementName, pub_code, account, tobid_appid,
                        header_bidding_switch, bidding_method, price, vo);
                if (ret.getIntValue("ret") == 0) {
                    String msg = ret.getString("msg");
                    recordMsg.add(new ImmutablePair<>(r, msg));
                }
            }

            StringBuilder sb = new StringBuilder();
            for (Pair<Integer, String> pair : recordMsg) {
                sb.append(String.format("第%d行，%s", pair.getKey(), pair.getValue())).append("<br>");
            }
            if (sb.length() != 0) {
                return ReturnJson.failed(sb.toString());
            }
            return ReturnJson.success();
        }
        return ReturnJson.failed("无法读取文件");
    }

    public Object toBidAdSourceAdsidImport(MultipartFile file) throws IOException, BiffException {

        if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
            Workbook workbook = Workbook.getWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheet(0);

            int row = sheet.getRows();
            StringBuilder sb = new StringBuilder();
            for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                    continue;
                }
                String adsid = sheet.getCell(0, r).getContents();
                String tobid_id = sheet.getCell(1, r).getContents();
                int i = adv2Mapper.updateExtendAdsidTobidId(tobid_id, adsid);
                if (i == 0) {
                    sb.append(String.format("第%d行插入失败", r)).append("<br>");
                }
            }
            return ReturnJson.success(sb.toString());
        }
        return ReturnJson.toErrorJson("文件读取失败");
    }

    private static Map<String, String> BIDDING_TYPE_MAP = new HashMap<String, String>() {{
        put("标准", "0");
        put("服务端竞价", "1");
        put("客户端竞价", "2");
    }};
    public Object toBidAdSourceUnionPosImport(MultipartFile file) throws IOException, BiffException {
        if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
            Workbook workbook = Workbook.getWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheet(0);

            String user = LOGIN_USER_NAME.get();
            int row = sheet.getRows();
            StringBuilder sb = new StringBuilder();
            TreeMap<String,ExtendAdsidVo> adSidList = new TreeMap<>();
            TreeMap<String,ExtendAdconfigVo> adConfigList = new TreeMap<>();
            for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                    continue;
                }
                ExtendAdsidVo extendAdsidVo = new ExtendAdsidVo();
                extendAdsidVo.setAgent(TOBID);
                extendAdsidVo.setSdk_appid(sheet.getCell(0, r).getContents());
                extendAdsidVo.setSdk_code(sheet.getCell(1, r).getContents());
                String sdkAdType = SDK_AD_TYPE.get(sheet.getCell(2, r).getContents());
                extendAdsidVo.setSdk_adtype(sdkAdType);
                String openType = OPEN_AD_TYPE.get(sheet.getCell(3, r).getContents());
                extendAdsidVo.setOpen_type(openType);
                extendAdsidVo.setBidding(BIDDING_TYPE_MAP.get(sheet.getCell(4, r).getContents()));
                String adExtentionName = sheet.getCell(5, r).getContents();

                extendAdsidVo.setUnit_id(sheet.getCell(6, r).getContents());
                String appid = sheet.getCell(7, r).getContents();
                extendAdsidVo.setAppid(appid);
                String adsid = TOBID + "_"+sdkAdType+"_"+appid+"_"+adExtentionName;
                String chaId = sheet.getCell(8, r).getContents();
                extendAdsidVo.setCha_id(chaId);
                extendAdsidVo.setNote(sheet.getCell(9, r).getContents());
                extendAdsidVo.setCuser(user);
                extendAdsidVo.setAdsid(adsid);
                adSidList.put(String.valueOf(r + 1), extendAdsidVo);

                ExtendAdconfigVo adConfig = new ExtendAdconfigVo();
                adConfig.setAdsid(adsid);
                //广告类型
                adConfig.setAdpos_type(openType);
                //应用
                adConfig.setAppid(appid);
                //广告策略
                adConfig.setStrategy(sheet.getCell(10, r).getContents());
                //广告配置子渠道
                adConfig.setCha_id(chaId);
                //预估ecpm
                String ecpm = sheet.getCell(11, r).getContents();
                if (BlankUtils.isNotBlank(ecpm)) {
                    adConfig.setEcpm(new BigDecimal(ecpm));
                }
                //分配比例
                adConfig.setRate(0);
                //开启状态
                adConfig.setStatu("1");
                adConfig.setCuser(user);
                adConfigList.put(String.valueOf(r + 1), adConfig);
            }
            TreeMap<String, String> retMap = exAdConfigController.batchCreateAdSid(adSidList, adConfigList);
            if (retMap.size()>0){
                for (Map.Entry<String,String> ret:retMap.entrySet()){
                    sb.append(ret.getKey()).append(ret.getValue()).append("<br>");
                }
                return ReturnJson.toErrorJson(sb.toString());
            }else {
                return ReturnJson.success("全部导入成功");
            }
        }
        return ReturnJson.toErrorJson("文件读取失败");
    }

    /**
     * <p>与上面方法 {@link ToBidGroupService#toBidAdSourceUnionPosImport(MultipartFile))}  不同的地方在于，这个方法需要创建
     * <p>聚合广告位，并绑定到广告源和广告管理
     * @param file
     * @return
     * @throws IOException
     * @throws BiffException
     */
    public Object toBidAdSourceUnionPosCreateImport(MultipartFile file) throws IOException, BiffException{
        if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
            Workbook workbook = Workbook.getWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheet(0);

            String user = LOGIN_USER_NAME.get();
            int row = sheet.getRows();
            StringBuilder sb = new StringBuilder();
            TreeMap<String,ExtendAdsidVo> adSidList = new TreeMap<>();
            TreeMap<String,ExtendAdconfigVo> adConfigList = new TreeMap<>();
            for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                    continue;
                }
                ExtendAdsidVo extendAdsidVo = new ExtendAdsidVo();
                extendAdsidVo.setAgent(TOBID);
                String adposName = sheet.getCell(0, r).getContents();
                Integer adposType = adTypeUMap.get(sheet.getCell(1, r).getContents());

                String sdkAppid = sheet.getCell(2, r).getContents();
                extendAdsidVo.setSdk_appid(sdkAppid);
                String sdkAdType = SDK_AD_TYPE.get(sheet.getCell(3, r).getContents());
                extendAdsidVo.setSdk_adtype(sdkAdType);
                String openType = OPEN_AD_TYPE.get(sheet.getCell(4, r).getContents());
                extendAdsidVo.setOpen_type(openType);
                extendAdsidVo.setBidding(BIDDING_TYPE_MAP.get(sheet.getCell(5, r).getContents()));
                String adExtentionName = sheet.getCell(6, r).getContents();

                extendAdsidVo.setUnit_id(sheet.getCell(7, r).getContents());
                String appid = sheet.getCell(8, r).getContents();
                extendAdsidVo.setAppid(appid);
                String adsid = TOBID + "_"+sdkAdType+"_"+appid+"_"+adExtentionName;
                ExtendAdsidVo dnAdSid = adv2Mapper.getDnAdSid(adsid);
                if (dnAdSid != null) {
                    sb.append(String.format("第%d行插入失败, 已经存在adsid", r)).append("<br>");
                    continue;
                }
                try {
                    JSONObject jsonObject = unionMediaAdPosCreate(adposName, sdkAppid, adposType);
                    if (jsonObject.getIntValue("ret") != 1) {
                        sb.append(String.format("第%d行插入失败, 创建广告位失败, %s", r,
                                jsonObject.getString("msg"))).append("<br>");
                        continue;
                    } else {
                        extendAdsidVo.setSdk_code(jsonObject.getString("data"));
                    }
                } catch (URISyntaxException | NoSuchAlgorithmException e) {
                    long now = System.currentTimeMillis();
                    sb.append(String.format("第%d行插入失败，报错追溯号： %d， 复制并寻找管理员处理", r,now)).append("<br>");
                    log.error("tracing {} unexpected error", now, e);
                    continue;
                }
                String chaId = sheet.getCell(9, r).getContents();
                extendAdsidVo.setAdsid(adsid);
                extendAdsidVo.setCha_id(chaId);
                extendAdsidVo.setNote(sheet.getCell(10, r).getContents());
                extendAdsidVo.setCuser(user);
                adSidList.put(String.valueOf(r + 1), extendAdsidVo);

                ExtendAdconfigVo adConfig = new ExtendAdconfigVo();
                adConfig.setAdsid(adsid);
                //广告类型
                adConfig.setAdpos_type(openType);
                //应用
                adConfig.setAppid(appid);
                //广告策略
                adConfig.setStrategy(sheet.getCell(11, r).getContents());
                //广告配置子渠道
                adConfig.setCha_id(chaId);
                //预估ecpm
                String ecpm = sheet.getCell(12, r).getContents();
                if (BlankUtils.isNotBlank(ecpm)) {
                    adConfig.setEcpm(new BigDecimal(ecpm));
                }
                //分配比例
                adConfig.setRate(0);
                //开启状态
                adConfig.setStatu("1");
                adConfig.setCuser(user);
                adConfigList.put(String.valueOf(r + 1), adConfig);
            }
            TreeMap<String, String> retMap = exAdConfigController.batchCreateAdSid(adSidList, adConfigList);
            if (retMap.size()>0){
                for (Map.Entry<String,String> ret:retMap.entrySet()){
                    sb.append(ret.getKey()).append(ret.getValue()).append("<br>");
                }
                return ReturnJson.toErrorJson(sb.toString());
            }else {
                return ReturnJson.success("全部导入成功");
            }
        }
        return ReturnJson.toErrorJson("文件读取失败");
    }
}
