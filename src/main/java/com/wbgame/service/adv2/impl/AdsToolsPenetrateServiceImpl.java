package com.wbgame.service.adv2.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.adb.AdsToolsPenetrateMapper;
import com.wbgame.pojo.adv2.bigdata.AdsInsideToolsPenetrateDataVo;
import com.wbgame.pojo.adv2.bigdata.AdsToolsPenetrateDataDTO;
import com.wbgame.pojo.adv2.bigdata.AdsOutsideToolsPenetrateDataVo;
import com.wbgame.service.AdService;
import com.wbgame.service.adv2.AdsToolsPenetrateService;
import com.wbgame.utils.DataTransUtils;
import com.wbgame.utils.UserPermissionsUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER;

/**
 * <AUTHOR>
 * @Description 工具产品功能渗透报表业务实现层
 * @Date 2024/12/24 14:26
 */
@Service
public class AdsToolsPenetrateServiceImpl implements AdsToolsPenetrateService {


    @Autowired
    private AdsToolsPenetrateMapper adsToolsPenetrateMapper;
    @Autowired
    private UserPermissionsUtils userPermissionsUtils;
    @Autowired
    private AdService adService;

    //是否新增用户状态码
    private final Map<String,String> isNewUserMap = new HashMap<String, String>() {{
       put("1","是");
       put("0","否");
    }};


    @Override
    public Result<Map<String, List<String>>> queryVersions(String tableName) {
        List<String> brandList = adsToolsPenetrateMapper.queryBrands(tableName);
        List<String> versionList = adsToolsPenetrateMapper.queryVersions(tableName);
        Map<String, List<String>> resultMap = new HashMap<>();
        resultMap.put("brands", brandList);
        resultMap.put("versions", versionList);
        return ResultUtils.success(resultMap);
    }

    /**
     * 工具海外产品功能渗透报表查询
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    @Override
    public Result<List<AdsOutsideToolsPenetrateDataVo>> queryOutsideList(AdsToolsPenetrateDataDTO dto) {
        //用户权限过滤
        String appidList = userPermissionsUtils.filterAppidByPermission(LOGIN_USER.get(), dto.getApp_category(), dto.getAppid());
        dto.setAppid(appidList);
        List<AdsOutsideToolsPenetrateDataVo> resultList;
        long totalSize;
        if (dto.isPageFlag()) {
            //分页页码校验
            dto.checkPageParams();
            //分页查询操作
            PageHelper.startPage(dto.getStart(), dto.getLimit());
            //获取当前操作人权限下的appid
            List<AdsOutsideToolsPenetrateDataVo> toolsPenetrateDataList = adsToolsPenetrateMapper.queryList(dto);
            PageInfo<AdsOutsideToolsPenetrateDataVo> pageInfo = new PageInfo<>(toolsPenetrateDataList);
            //结果集封装
            resultList = pageInfo.getList();
            totalSize = pageInfo.getTotal();
        } else {
            //不分页查询操作
            resultList = adsToolsPenetrateMapper.queryList(dto);
            totalSize = resultList.size();
        }
        String percentageFields = "topc_penetration,cmode_penetration,bhmode_penetration,create_uv_penetration,create_success_rate,ad_pv_success_rate,ad_uv_penetration";
        //查询应用信息
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);
        //封装应用名称
        for (AdsOutsideToolsPenetrateDataVo dataVo : resultList) {
            //封装百分号
            DataTransUtils.addDataPercentage(dataVo,percentageFields);
            //封装app_name
            if (appMap.containsKey(dataVo.getAppid())) {
                Map<String, Object> appInfoMap = appMap.get(dataVo.getAppid());
                dataVo.setApp_name(appInfoMap.containsKey("app_name") ? appInfoMap.get("app_name").toString() : null);
            }
            //是否新增用户
            dataVo.setIsnew(isNewUserMap.get(dataVo.getIsnew()));
        }
        //查询汇总数据
        AdsOutsideToolsPenetrateDataVo total = adsToolsPenetrateMapper.queryTotal(dto);
        //封装百分号
        DataTransUtils.addDataPercentage(total,percentageFields);
        //查询结果返回
        return ResultUtils.success(Constants.OK, resultList, total, totalSize);
    }

    /**
     * 工具国内产品功能渗透报表-分页查询接口
     *
     * @param dto 查询条件参数
     * @return 查询结果
     */
    @Override
    public Result<List<AdsInsideToolsPenetrateDataVo>> queryInsideList(AdsToolsPenetrateDataDTO dto) {
        //用户权限过滤
        String appidList = userPermissionsUtils.filterAppidByPermission(LOGIN_USER.get(), dto.getApp_category(), dto.getAppid());
        dto.setAppid(appidList);
        List<AdsInsideToolsPenetrateDataVo> resultList;
        long totalSize;
        if (dto.isPageFlag()) {
            //分页页码校验
            dto.checkPageParams();
            //分页查询操作
            PageHelper.startPage(dto.getStart(), dto.getLimit());
            //获取当前操作人权限下的appid
            List<AdsInsideToolsPenetrateDataVo> toolsPenetrateDataList = adsToolsPenetrateMapper.queryInsideList(dto);
            PageInfo<AdsInsideToolsPenetrateDataVo> pageInfo = new PageInfo<>(toolsPenetrateDataList);
            //结果集封装
            resultList = pageInfo.getList();
            totalSize = pageInfo.getTotal();
        } else {
            //不分页查询操作
            resultList = adsToolsPenetrateMapper.queryInsideList(dto);
            totalSize = resultList.size();
        }
        //需要封装百分号的字段
        String percentageFields = "agree_penetration,init_penetration,first_start_penetration,create_uv_penetration,create_success_rate,ad_fill_rate,start_force_rate,ad_pv_success_rate,ad_uv_penetration";
        //查询应用信息
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);
        //封装应用名称
        for (AdsInsideToolsPenetrateDataVo dataVo : resultList) {
            //封装百分号
            DataTransUtils.addDataPercentage(dataVo, percentageFields);
            //封装app_name
            if (appMap.containsKey(dataVo.getAppid())) {
                Map<String, Object> appInfoMap = appMap.get(dataVo.getAppid());
                dataVo.setApp_name(appInfoMap.containsKey("app_name") ? appInfoMap.get("app_name").toString() : null);
            }
            //是否新增用户
            dataVo.setIsnew(isNewUserMap.get(dataVo.getIsnew()));
        }
        //查询汇总数据
        AdsInsideToolsPenetrateDataVo total = adsToolsPenetrateMapper.queryInsideTotal(dto);
        //封装百分号
        DataTransUtils.addDataPercentage(total, percentageFields);
        //查询结果返回
        return ResultUtils.success(Constants.OK, resultList, total, totalSize);
    }
}
