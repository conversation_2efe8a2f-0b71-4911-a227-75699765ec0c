package com.wbgame.service.adv2.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.mapper.adb.bigdata.AdsUserRevenueOverseaMapper;
import com.wbgame.mapper.master.CommonMapper;
import com.wbgame.pojo.adv2.bigdata.UserRevenueOverseaParam;
import com.wbgame.service.adv2.AdsUserRevenueOverseaService;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.UserPermissionsUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/6/9 17:51
 */
@Service
public class AdsUserRevenueOverseaServiceImpl implements AdsUserRevenueOverseaService {

    @Autowired
    private UserPermissionsUtils userPermissionsUtils;
    @Autowired
    private AdsUserRevenueOverseaMapper adsUserRevenueOverseaMapper;
    @Autowired
    private CommonMapper commonMapper;

    /**
     * 根据条件查询新增/活跃用户展示分布数据
     *
     * @param param 查询条件
     * @return 查询结果
     */
    @Override
    public String selectAdshowActuser(UserRevenueOverseaParam param) {
        param.checkPageParams();
        //权限过滤
        String filteredAppids = userPermissionsUtils.filterAppidByPermission(LOGIN_USER.get(), param.getApp_category(), param.getAppid());
        param.setAppid(filteredAppids);
        // 新增用户增加维度  adpos_type_group --20241021 活跃用户也需要新增维度
        if ("ok".equalsIgnoreCase(param.getAdpos_type_group())) {
            param.setAdpos_type_group("adpos_type_group");
        }
        param.setUser("add".equals(param.getUser_type()) ? "addnum" : "actnum");
        param.setUser_type("add".equals(param.getUser_type()) ? "新增" : "活跃");
        JSONObject result = new JSONObject();
        try {
            PageHelper.startPage(param.getStart(), param.getLimit());
            List<Map<String, Object>> list = adsUserRevenueOverseaMapper.selectAdshowActuser(param);
            long size = ((Page) list).getTotal();
            //国家转换
            List<Map<String, String>> countryList = commonMapper.selectCountryList();
            Map<String, String> countryMap = countryList.stream().collect(Collectors.toMap(data -> data.getOrDefault("country_code", ""), data -> data.getOrDefault("country_name", ""), (k1, k2) -> k1));
            list.forEach(act -> {
                act.put("add_rate", act.get("add_rate") + "%");
                act.put("seep_rate", act.get("seep_rate") + "%");
                act.put("add_seep_rate", act.get("add_seep_rate") + "%");
                act.put("global_seep_rate", act.get("global_seep_rate") + "%");
                act.put("add_global_seep_rate", act.get("add_global_seep_rate") + "%");
                //国家编码转换操作
                String country = act.get("country") + "";
                if (countryMap.containsKey(country)) {
                    act.put("country", countryMap.get(country));
                }
                for (int i = 0; i < 15; i++) {
                    act.put("pv" + i, act.get("pv" + i) + "%");
                }
            });
            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    @Override
    public void exportAdshowActuser(HttpServletResponse response, UserRevenueOverseaParam param) {

        //用户权限过滤
        param.setAppid(userPermissionsUtils.filterAppidByPermission(LOGIN_USER.get(), param.getApp_category(), param.getAppid()));
        // 新增用户增加维度  adpos_type_group --20241021 活跃用户也需要新增维度
        if ("ok".equalsIgnoreCase(param.getAdpos_type_group())) {
            param.setAdpos_type_group("adpos_type_group");
        }
        param.setUser("add".equals(param.getUser_type()) ? "addnum" : "actnum");
        param.setUser_type("add".equals(param.getUser_type()) ? "新增" : "活跃");

        List<Map<String, Object>> contentList = adsUserRevenueOverseaMapper.selectAdshowActuser(param);

        List<Map<String, String>> countryList = commonMapper.selectCountryList();
        Map<String, String> countryMap = countryList.stream().collect(Collectors.toMap(data -> data.getOrDefault("country_code", ""), data -> data.getOrDefault("country_name", ""), (k1, k2) -> k1));

        contentList.forEach(act -> {
            // 赋值应用名称
            act.put("tdate", act.get("tdate") + "");
            //赋值国家
            String country = act.get("country") + "";
            if (countryMap.containsKey(country)) {
                act.put("country", countryMap.get(country));
            }
            act.put("add_rate", act.get("add_rate") + "%");
            act.put("seep_rate", act.get("seep_rate") + "%");
            act.put("add_seep_rate", act.get("add_seep_rate") + "%");
            act.put("global_seep_rate", act.get("global_seep_rate") + "%");
            act.put("add_global_seep_rate", act.get("add_global_seep_rate") + "%");
            for (int i = 0; i < 15; i++) {
                act.put("pv" + i, act.get("pv" + i) + "%");
            }
        });
        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = param.getValue().split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = param.getExport_file_name() + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx";
        ExportExcelUtil.exportXLSX(response, contentList, head, fileName);
    }
}
