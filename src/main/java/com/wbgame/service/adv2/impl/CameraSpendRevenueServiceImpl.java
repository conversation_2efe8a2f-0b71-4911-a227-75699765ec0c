package com.wbgame.service.adv2.impl;

import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.adb.CameraSpendRevenueMapper;
import com.wbgame.service.adv2.CameraSpendRevenueService;
import com.wbgame.utils.*;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.util.Strings;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER;

/**
 * <AUTHOR>
 * @Description 工具相机收支流水表 业务实现层
 * @Date 2024/10/28 11:08
 */
@Service
public class CameraSpendRevenueServiceImpl implements CameraSpendRevenueService {

    @Autowired
    private CameraSpendRevenueMapper cameraSpendRevenueMapper;
    @Autowired
    private UserPermissionsUtils userPermissionsUtils;

    /**
     * 根据查询条件查询相机收支流水表数据
     *
     * @param paramMap 查询条件
     * @return 查询结果
     */
    @Override
    public Result<List<Map<String, Object>>> queryList(Map<String, String> paramMap) {
        int startPage = Integer.parseInt(paramMap.getOrDefault("start", "1"));
        int limit = Integer.parseInt(paramMap.getOrDefault("limit", "100"));
        String userType = paramMap.get("user_type");
        if (!StringUtils.isEmpty(userType)) {
            //处理查询条件 渠道 数据的类型
            paramMap.put("user_type", DataTransUtils.transToSql(userType));
        }
        //用户权限appid添加
        String appids = userPermissionsUtils.filterAppidByPermission(LOGIN_USER.get(), paramMap.get("app_category"), paramMap.get("appid"));
        paramMap.put("appid", appids);

        //PageHelper.startPage(startPage,limit);
        // 判断是否为自定义选择时间段
        List<Map<String, Object>> mapList = new ArrayList<>();
        String custom_date = paramMap.get("custom_date");
        if(StringUtils.isBlank(custom_date)){
            mapList = cameraSpendRevenueMapper.queryList(paramMap);
        }else {
            String[] custom_date_list = custom_date.split(";");
            if (custom_date_list.length>=1){
                for (String each:custom_date_list){
                    String[] eachList = each.split(",");
                    paramMap.put("start_date",eachList[0]);
                    paramMap.put("end_date",eachList[1]);
                    List<Map<String, Object>> eachMapList = cameraSpendRevenueMapper.queryList(paramMap);
                    if (!eachMapList.isEmpty()){
                        mapList.addAll(eachMapList);
                    }
                }
            }
        }

        //手动分页
        List<Map<String, Object>> resultList = mapList.stream().skip((long) (startPage - 1) * limit).limit(limit).collect(Collectors.toList());
        //PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(mapList);
        for (Map<String, Object> map : resultList) {
            transferDataMapNew(map,paramMap);
        }
        //查询汇总数据
        Map<String, Object> totalMap = cameraSpendRevenueMapper.queryTotal(paramMap);
        transferDataMapNew(totalMap,null);
        //返回结果
        return ResultUtils.success(Constants.OK, resultList, totalMap, (long) mapList.size());
    }

    /**
     * 对 相机收支流水数据 处理日期和占比数据格式
     *
     * @param map 需要处理的相机收支流水数据
     */
    private void transferDataMap(Map<String, Object> map,Map<String, String> paramMap) {
        if (MapUtils.isEmpty(map)) return;
        //处理日期
        if (map.containsKey("tdate")) {
            String tdate = map.get("tdate") + Strings.EMPTY;
            if (MapUtils.isNotEmpty(paramMap) && !StringUtils.isEmpty(paramMap.get("value"))) {
                //导出功能
                map.put("tdate", tdate);
                map.put("xingqi",DateUtil.dateToWeek(tdate));
            } else {
                //查询功能
                map.put("tdate", tdate + "(" + DateUtil.dateToWeek(tdate) + ")");
            }
        } else {
            if (MapUtils.isNotEmpty(paramMap)) {
                map.put("tdate", paramMap.get("start_date") + "至" + paramMap.get("end_date"));
            }
        }
        //处理占比，百分号
        //profix_rate--毛利率
        if (map.containsKey("profix_rate")) {
            map.put("profix_rate", map.get("profix_rate") + "%");
        } else {
            map.put("profix_rate", "0.00%");
        }
        //renew_revenue_rate--
        if (map.containsKey("renew_revenue_rate")) {
            map.put("renew_revenue_rate", map.get("renew_revenue_rate") + "%");
        } else {
            map.put("renew_revenue_rate", "0.00%");
        }
    }

    /**
     * 对 相机收支流水数据 处理日期和占比数据格式
     *
     * @param map 需要处理的相机收支流水数据
     */
    private void transferDataMapNew(Map<String, Object> map,Map<String, String> paramMap) {
        if (MapUtils.isEmpty(map)) return;
        //处理日期
        if (map.containsKey("tdate")) {
            String tdate = map.get("tdate") + Strings.EMPTY;

            //导出功能
            if (MapUtils.isNotEmpty(paramMap) && !StringUtils.isEmpty(paramMap.get("value"))) {

                if (paramMap.get("group") != null && paramMap.get("group").contains("tdate")) {
                    map.put("xingqi",DateUtil.dateToWeek(tdate));
                }

                // 周
                else if (paramMap.get("group") != null && paramMap.get("group").contains("week")){
                    if (map.get("tdate")!=null){
                        String tdateData =  map.get("tdate").toString();
                        String[] split = tdateData.split("-");
                        if (split.length >= 2) {
                            Integer year = Integer.parseInt(split[0]);
                            Integer week = Integer.parseInt(split[1]);
                            String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week);
                            String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week);
                            map.put("tdate", year+"年第"+week+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                        }
                    }
                }
                // 月 暂不处理
                else if (paramMap.get("group") != null && paramMap.get("group").contains("month")) {
                    //不做处理
                }
                // 双周
                else if (paramMap.get("group") != null && paramMap.get("group").contains("beek")){
                    if (map.get("tdate")!=null){
                        String tdateData =  map.get("tdate").toString();
                        String[] split = tdateData.split("-");
                        if (split.length >= 3) {
                            Integer year = Integer.parseInt(split[0]);
                            Integer week1 = Integer.parseInt(split[1]);
                            Integer week2 = Integer.parseInt(split[2]);
                            String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week1);
                            String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week2);
                            map.put("tdate", year+"年第"+week1+"-"+week2+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                        }
                    }
                }
                // 自定义时间段
                else if (paramMap.get("custom_date")==null||paramMap.get("custom_date").length()==0) {
                    // "start_date", "end_date",
                    map.put("tdate",paramMap.get("start_date")+"至"+paramMap.get("end_date"));
                }


            } else {
                //查询功能

                // 日
                if (paramMap.get("group") != null && paramMap.get("group").contains("tdate")){
                    map.put("tdate", tdate + "(" + DateUtil.dateToWeek(tdate) + ")");
                }
                // 周
                else if (paramMap.get("group") != null && paramMap.get("group").contains("week")){
                    if (map.get("tdate")!=null){
                        String tdateData =  map.get("tdate").toString();
                        String[] split = tdateData.split("-");
                        if (split.length >= 2) {
                            Integer year = Integer.parseInt(split[0]);
                            Integer week = Integer.parseInt(split[1]);
                            String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week);
                            String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week);
                            map.put("tdate", year+"年第"+week+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                        }
                    }
                }
                // 月 暂不处理
                else if (paramMap.get("group") != null && paramMap.get("group").contains("month")) {
                    //不做处理
                }
                // 双周
                else if (paramMap.get("group") != null && paramMap.get("group").contains("beek")){
                    if (map.get("tdate")!=null){
                        String tdateData =  map.get("tdate").toString();
                        String[] split = tdateData.split("-");
                        if (split.length >= 3) {
                            Integer year = Integer.parseInt(split[0]);
                            Integer week1 = Integer.parseInt(split[1]);
                            Integer week2 = Integer.parseInt(split[2]);
                            String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week1);
                            String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week2);
                            map.put("tdate", year+"年第"+week1+"-"+week2+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                        }
                    }
                }
                // 自定义时间段
                else if (paramMap.get("custom_date")==null) {
                    // "start_date", "end_date",
                    map.put("tdate",paramMap.get("start_date")+"至"+paramMap.get("end_date"));
                }
            }
        } else {
            if (MapUtils.isNotEmpty(paramMap)) {
                map.put("tdate", paramMap.get("start_date") + "至" + paramMap.get("end_date"));
            }
        }
        //处理占比，百分号
        //profix_rate--毛利率
        if (map.containsKey("profix_rate")) {
            map.put("profix_rate", map.get("profix_rate") + "%");
        } else {
            map.put("profix_rate", "0.00%");
        }
        //renew_revenue_rate--
        if (map.containsKey("renew_revenue_rate")) {
            map.put("renew_revenue_rate", map.get("renew_revenue_rate") + "%");
        } else {
            map.put("renew_revenue_rate", "0.00%");
        }
    }


    /**
     * 导出接口
     *
     * @param response HttpServletResponse
     * @param paramMap 导出条件
     */
    @Override
    public void export(HttpServletResponse response, Map<String, String> paramMap) {
        String value = paramMap.get("value");
        String userType = paramMap.get("user_type");
        if (!StringUtils.isEmpty(userType)) {
            paramMap.put("user_type", DataTransUtils.transToSql(userType));
        }
        //用户权限appid添加
        String appids = userPermissionsUtils.filterAppidByPermission(LOGIN_USER.get(), paramMap.get("app_category"), paramMap.get("appid"));
        paramMap.put("appid", appids);


        // 判断是否为自定义选择时间段
        List<Map<String, Object>> mapList = new ArrayList<>();
        String custom_date = paramMap.get("custom_date");
        if(StringUtils.isBlank(custom_date)){
            mapList = cameraSpendRevenueMapper.queryList(paramMap);
        }else {
            String[] custom_date_list = custom_date.split(";");
            if (custom_date_list.length>=1){
                for (String each:custom_date_list){
                    String[] eachList = each.split(",");
                    paramMap.put("start_date",eachList[0]);
                    paramMap.put("end_date",eachList[1]);
                    List<Map<String, Object>> eachMapList = cameraSpendRevenueMapper.queryList(paramMap);
                    if (!eachMapList.isEmpty()){
                        mapList.addAll(eachMapList);
                    }
                }
            }
        }

        for (Map<String, Object> map : mapList) {
            //数据格式转换，日期
            transferDataMapNew(map,paramMap);
        }
        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = value.split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = paramMap.get("export_file_name") + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx";
        ExportExcelUtil.exportXLSX(response, mapList, head, fileName);
    }
}
