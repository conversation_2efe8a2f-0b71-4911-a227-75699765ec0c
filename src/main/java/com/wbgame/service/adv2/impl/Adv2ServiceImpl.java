package com.wbgame.service.adv2.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.wbgame.common.ReturnJson;
import com.wbgame.common.constants.SyncDataConstants;
import com.wbgame.mapper.adb.ClientSDKMapper;
import com.wbgame.mapper.adb.DnwxBiMapper;
import com.wbgame.mapper.adv2.MonetizationSummaryMapper;
import com.wbgame.mapper.master.UserInfoMapper;
import com.wbgame.mapper.master.yyhzCashReport.CashReportMapper;
import com.wbgame.pojo.adv2.*;
import com.wbgame.pojo.adv2.reportEntity.Channel;
import com.wbgame.pojo.adv2.reportEntity.ChinaMonetizationReport;
import com.wbgame.pojo.adv2.reportEntity.ReportChina;
import com.wbgame.pojo.product.DnwxX3X4ConfigManageV2Dto;
import com.wbgame.service.AdService;
import com.wbgame.service.WxGameAppTypeManageService;
import com.wbgame.task.HaiwaiAdTask;
import com.wbgame.utils.*;
import com.wbgame.utils.jettison.DateUtils;
import com.wbgame.utils.jettison.StringUtil;
import com.wbgame.utils.tool.DateToolUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.mapper.adv2.Adv2Mapper;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.NpPostVo;
import com.wbgame.service.adv2.Adv2Service;
import com.wbgame.utils.baidu.UbapiClient;
import com.wbgame.utils.baidu.UbapiClientSample;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

@Service("adv2Service")
public class Adv2ServiceImpl implements Adv2Service {

	Logger logger = LoggerFactory.getLogger(Adv2Service.class);

	public static final String REDLINE_CTR_CHAT_ID = "oc_b9dd8bb33b672112f7d8f2fea3862ca2";
	// 测试群组
//	public static final String REDLINE_CTR_CHAT_ID = "oc_4fb551de562588142c975440d4009af9";
	public static final String FS_MSG_GROUP = "https://edc.vigame.cn:6115/fs/sendGroupMsg";


	@Autowired
	private ClientSDKMapper clientSDKMapper;

	@Autowired
	private Adv2Mapper adv2Mapper;
	@Autowired
	private YyhzMapper yyhzMapper;
	@Autowired
	private DnwxBiMapper dnwxBiMapper;
	@Autowired
	private WxGameAppTypeManageService wxGameAppTypeManageService;
	@Autowired
	private AdService adService;
	@Autowired
	private UserInfoMapper userInfoMapper;


	@Resource
	private MonetizationSummaryMapper monetizationSummaryMapper;

	@Resource
	private CashReportMapper cashReportMapper;


	@Override
	public int execSql(String sql) {
		return adv2Mapper.execSql(sql);
	}

	@Override
	public List<String> queryListString(String sql) {
		return adv2Mapper.queryListString(sql);
	}

	@Override
	public List<Map<String, Object>> queryListMap(String sql) {
		return adv2Mapper.queryListMap(sql);
	}

	@Override
	public Map<String, Map<String, Object>> queryListMapOfKey(String sql) {
		return adv2Mapper.queryListMapOfKey(sql);
	}

	@Override
	public <T> List<T> queryListBean(String sql, Class<T> clazz) {
		List<Map<String, Object>> listBean = adv2Mapper.queryListMap(sql);
		return JSONArray.parseArray(JSONArray.toJSONString(listBean), clazz);
	}

	@Override
	public int execSqlHandle(String sql, Object obj) {
		return adv2Mapper.execSqlHandle(sql, obj);
	}

	@Override
	public List<NpPostVo> queryNpPost(String sql) {
		return adv2Mapper.queryNpPost(sql);
	}

	@Override
	public List<Map<String, String>> queryListMapOne(String sql) {
		return adv2Mapper.queryListMapOne(sql);
	}

	@Override
	public List<Map<String, Object>> queryListMapTwo(String sql, Object obj) {
		return adv2Mapper.queryListMapTwo(sql, obj);
	}

	@Override
	public int batchExecSql(Map<String, Object> paramMap) {
		return adv2Mapper.batchExecSql(paramMap);
	}

	@Override
	public int batchExecSqlTwo(Map<String, Object> paramMap) {
		return adv2Mapper.batchExecSqlTwo(paramMap);
	}


	@Override
	public List<Map<String, Object>> selectDnGroupData(Map<String, Object> paramMap) {
		return adv2Mapper.selectDnGroupData(paramMap);
	}

	@Override
	public Map<String, Object> selectDnGroupDataSum(Map<String, Object> paramMap) {
		return adv2Mapper.selectDnGroupDataSum(paramMap);
	}

	@Override
	public List<Map<String, Object>> selectAdposData(Map<String, Object> paramMap) {
		return adv2Mapper.selectAdposData(paramMap);
	}

	@Override
	public Map<String, Object> selectAdposDataSum(Map<String, Object> paramMap) {
		return adv2Mapper.selectAdposDataSum(paramMap);
	}

	@Override
	public List<Map<String, Object>> selectDnShowGapTotal(Map<String, Object> paramMap) {
		return adv2Mapper.selectDnShowGapTotal(paramMap);
	}

	@Override
	public Map<String, Object> selectDnShowGapTotalSum(Map<String, Object> paramMap) {
		return adv2Mapper.selectDnShowGapTotalSum(paramMap);
	}

	@Override
	public List<Map<String, Object>> selectSelfPromotionIncome(Map<String, Object> paramMap) {
		return adv2Mapper.selectSelfPromotionIncome(paramMap);
	}

	@Override
	public List<Map<String, Object>> selectDnExIncomeData(Map<String, Object> paramMap) {
		return adv2Mapper.selectDnExIncomeData(paramMap);
	}

	@Override
	public Map<String, Object> selectDnExIncomeDataSum(Map<String, Object> paramMap) {
		return adv2Mapper.selectDnExIncomeDataSum(paramMap);
	}

	@Override
	public List<Map<String, Object>> selectDnGroupCom(Map<String, Object> paramMap) {
		return adv2Mapper.selectDnGroupCom(paramMap);
	}

	@Override
	public Map<String, Object> selectDnGroupComSum(Map<String, Object> paramMap) {
		return adv2Mapper.selectDnGroupComSum(paramMap);
	}

	@Override
	public List<Map<String,Object>> selectExtendRevenueReport(Map<String, Object> paramMap) {
		return adv2Mapper.selectExtendRevenueReport(paramMap);
	}

	@Override
	public List<DnExtendGroupMonitorVo> selectExtendGroupMonitor(Map<String, Object> paramMap) {
		return adv2Mapper.selectExtendGroupMonitor(paramMap);
	}

	@Override
	public List<Map<String, Object>> selectExtendIncomeRevise(Map<String, Object> paramMap) {
		return adv2Mapper.selectExtendIncomeRevise(paramMap);
	}

	@Override
	public Map<String, Object> selectExtendIncomeReviseSum(Map<String, Object> paramMap) {
		return adv2Mapper.selectExtendIncomeReviseSum(paramMap);
	}

	@Override
	public List<Map<String, Object>> selectAdtypeTotalRevise(Map<String, Object> paramMap) {
		return adv2Mapper.selectAdtypeTotalRevise(paramMap);
	}

	@Override
	public Map<String, Object> selectAdtypeTotalReviseSum(Map<String, Object> paramMap) {
		return adv2Mapper.selectAdtypeTotalReviseSum(paramMap);
	}

	@Override
	public List<Map<String, Object>> selectPrjidTotalIncome(Map<String, Object> paramMap) {
		return adv2Mapper.selectPrjidTotalIncome(paramMap);
	}

	@Override
	public Map<String, Object> selectPrjidTotalIncomeSum(Map<String, Object> paramMap) {
		return adv2Mapper.selectPrjidTotalIncomeSum(paramMap);
	}

	@Override
	public List<Map<String, Object>> selectSubchaTotal(Map<String, Object> paramMap) {
		return adv2Mapper.selectSubchaTotal(paramMap);
	}

	@Override
	public Map<String, Object> selectSubchaTotalSum(Map<String, Object> paramMap) {
		return adv2Mapper.selectSubchaTotalSum(paramMap);
	}

	@Override
	public boolean syncKuaishouCpmFloor() {

		try {
			List<JSONObject> appList = getPosition("3");
			List<JSONObject> gameList = getPosition("1");
			appList.addAll(gameList);

			Map<String, Object> paramMap2 = new HashMap<String, Object>();
			paramMap2.put("sql1", "update dn_extend_adsid_manage set cpmFloor=#{li.cpm_floor} where sdk_appid=#{li.app_id} and sdk_code=#{li.position_id} ");
			paramMap2.put("list", appList);
			adv2Mapper.batchExecSqlTwo(paramMap2);

			return true;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public static List<JSONObject> getPosition(String app_category){

		List<JSONObject> data = new ArrayList<>();
		try {
			// 拉取快手报表数据
			String host = "https://u.kuaishou.com";
			String url = "/api/position/get";
			TreeMap<String, String> params = new TreeMap<String, String>();
			params.put("ak", "5331");
			params.put("sk", "3b97e5164eeb504bab85830191a9269c");
			if(!"3".equals(app_category)){
				params.put("ak", "5052");
				params.put("sk", "abc36ec24e4526b7f5019e624d3169ec");
			}
			params.put("date", DateTime.now().toString("yyyy-MM-dd"));
			params.put("timestamp", (DateTime.now().getMillis()/1000)+"");

			String str = params.entrySet().stream()
					.map(act -> act.getKey()+"="+act.getValue()).collect(Collectors.joining("&"));
			String sign = DigestUtils.md5Hex(url+"?"+str);
			params.put("sign", sign);

			params.remove("sk");
			String base = params.entrySet().stream()
					.map(act -> act.getKey()+"="+act.getValue()).collect(Collectors.joining("&"));
			String link = host+url+"?"+base;
			System.out.println("link："+link);

			Map<String, String> headMap = new HashMap<>();
			headMap.put("Content-Type", "application/json");
			for (int i = 1; i < 50; i++) {
				String json = "{\"page\":"+i+",\"pageSize\":100}";

				String httpPost = HttpClientUtils.getInstance().httpPost(link, json, headMap);
				if(httpPost != null && "1".equals(JSONObject.parseObject(httpPost).getString("result"))){
					JSONArray array = JSONObject.parseObject(httpPost).getJSONArray("data");
					for (Object object : array) {
						JSONObject info = (JSONObject)object;
						data.add(info);
						System.out.println(info.getString("name")+"\t"+info.getString("app_id")+"\t"+info.getString("position_id")+"\t"+info.getString("cpm_floor"));
					}
				}else{
					continue;
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		return data;
	}

	public static final String BAIDU_CHAT_ID = "oc_701c2386ac4e1bdc44f1663c454db1a7";

	@Override
	public boolean syncBaiduCashTotal(String tdate){
		logger.info("同步syncBaiduCashTotal 开始...");
		try {
			List<JSONObject> list = new ArrayList<>();
			String[] tokens = {
					"iOlQK24vJZa8s1Lk",
					"t8Mj25wOGv7sxPXY",
					"7wBXgAQHvCdy263n",
					"8YAvL2rq6bDgGl5U",
					"q3R2ExCeYkha61ZD",
					"08EG6dqPLxQz5tNo",
					"wLuF7leqMbGX913W",
					"xFa9K3Rz2VChtT7d",
					"se3YiZG768hBOWgz"
			};
			String[] access = {
					"国数汇量",
					"源码科技2020",
					"源流科技",
					"飞鸟与鱼FNYY",
					"拓鑫科技",
					"铭鑫科技1",
					"茂源互动",
					"全支付qzf",
					"漫城科技"
			};

			// 通过baidu的appId 匹配 动能appid
			String query = "select bdappid mapkey,appid from finance_baidu_account_config";
			Map<String, Map<String, Object>> bdMap = yyhzMapper.queryListMapOfKey(query);

			//通过dn_extend_adsid_manage配置匹配广告源
			List<Map> adConfigList = yyhzMapper.getAdConfig();
			Map<String,String> sidMap = new HashMap<>(adConfigList.size());
			Map<String,String> markMap = new HashMap<>(adConfigList.size());
			Map<String,String> positionMap = new HashMap<>(adConfigList.size());
			Map<String,String> openTypeMap = new HashMap<>(adConfigList.size());
			if(adConfigList != null && !adConfigList.isEmpty()){
				for(int i=0;i<adConfigList.size();i++){
					Map map = adConfigList.get(i);
					String code = map.get("sdk_code")==null?"":map.get("sdk_code").toString();
					if(!"".equals(code)){
						String ad_sid = map.get("ad_sid")==null?"":map.get("ad_sid").toString();
						String channelMark = map.get("cha_id")==null?"":map.get("cha_id").toString();
						String position = map.get("sdk_adtype")==null?"":map.get("sdk_adtype").toString();
						String open_type = map.get("open_type")==null?"":map.get("open_type").toString();

						sidMap.put(code,ad_sid);
						markMap.put(code,channelMark);
						positionMap.put(code,position);
						openTypeMap.put(code,open_type);
					}
				}
			}

			//通过dn_channel_info和dn_channel_type匹配渠道
			List<Map> channelList = yyhzMapper.getChannel();
			Map<String,String> channelMap = new HashMap<>(channelList.size());
			Map<String,String> channelTypeMap = new HashMap<>(channelList.size());
			Map<String,String> mediaMap = new HashMap<>(channelList.size());
			Map<String,String> typeIdMap = new HashMap<>(channelList.size());
			if(channelList != null && !channelList.isEmpty()){
				for(int i=0;i<channelList.size();i++){
					Map map = channelList.get(i);
					String mark = map.get("cha_id")==null?"":map.get("cha_id").toString();
					if(!"".equals(mark)){
						String channel = map.get("cha_sub_launch")==null?"":map.get("cha_sub_launch").toString();
						String channelType = map.get("type_name")==null?"":map.get("type_name").toString();
						String media = map.get("cha_media")==null?"":map.get("cha_media").toString();
						String typeId = map.get("cha_type")==null?"":map.get("cha_type").toString();

						channelMap.put(mark,channel);
						channelTypeMap.put(mark,channelType);
						mediaMap.put(mark,media);
						typeIdMap.put(mark,typeId);
					}
				}
			}
			tdate = tdate.replace("-", "");
			for (int i = 0; i < access.length; i++) {

				UbapiClient client = new UbapiClient("https://ubapi.baidu.com", tokens[i], UbapiClientSample.PRIVATE_KEY);

				String uri = "/ssp/1/sspservice/app/app-report/get-content-report?beginDate="+tdate+"&endDate="+tdate+"";
				HttpResponse response = client.get(uri);

				HttpEntity entity = response.getEntity();
				String res = EntityUtils.toString(entity, "UTF8");
				if(BlankUtils.isJSONObject(res) && "0".equals(JSONObject.parseObject(res).getString("code"))) {
					JSONArray array = JSONObject.parseObject(res).getJSONArray("data");
					for (int j = 0; j < array.size(); j++) {
						JSONObject info = array.getJSONObject(j);
						info.put("tdate", tdate);
						info.put("account", access[i]);
						info.put("bdappid", info.getString("appId"));
						info.put("type",2);
						Map<String, Object> act = bdMap.get(info.getString("appId"));
						if(act != null) {
							info.put("dnappid", act.get("appid"));
						}

						if(null == info.getString("view") || "null".equals(info.getString("view"))){
							continue;
						}
						if(null == info.getString("cpuVisitPV") || "null".equals(info.getString("cpuVisitPV"))){
							/* 访客PV为null时，暂时赋空值处理 */
							info.put("cpuVisitPV", 0);
							info.put("cpuDetailPV", 0);
						}
						list.add(info);

					}
				}else{
					String error = tdate+"百度content 错误返回：" + res.replace("\n", ",");
					FeishuUtils.sendMsgToGroupRobot(BAIDU_CHAT_ID,
							error,
							"all");
					logger.error(error);
					return false;
				}
				Thread.sleep(2000L);
			}
			// 入库百度pv和收入数据
			if(list.size()>0) {
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("sql1", "replace into baidu_cash_total(tdate,account,bdappid,appid,appName,view,income,cpuVisitPV,cpuDetailPV,click,ecpm,ctr) values ");
				paramMap.put("sql2", " (#{li.tdate},#{li.account},#{li.bdappid},#{li.dnappid},#{li.appName},#{li.view},#{li.income},#{li.cpuVisitPV},#{li.cpuDetailPV},#{li.click},#{li.ecpm},#{li.ctr}) ");
				paramMap.put("sql3", " ");
				paramMap.put("list", list);
				adv2Mapper.batchExecSql(paramMap);
			} else {
				FeishuUtils.sendMsgToGroupRobot(BAIDU_CHAT_ID,
						tdate + " 百度内容数据为空",
						"all");
			}
			// 更新 友盟新增活跃数据
			String sql2 = "SELECT tdate,appid,SUM(act_num) act_num,SUM(add_num) add_num FROM umeng_user_channel_total WHERE tdate='"+tdate+"' GROUP BY appid";
			List<Map<String, Object>> list2 = yyhzMapper.queryListMap(sql2);
			if(list2!=null && !list2.isEmpty()) {
				Map<String, Object> paramMap2 = new HashMap<String, Object>();
				paramMap2.put("sql1", "update baidu_cash_total set actnum=#{li.act_num},addnum=#{li.add_num} where tdate=#{li.tdate} and appid=#{li.appid} ");
				paramMap2.put("list", list2);
				adv2Mapper.batchExecSqlTwo(paramMap2);
			}

            /*// 同步收入数据到 应用收入汇总表
			Map<String, Object> paramMap4 = new HashMap<String, Object>();
			paramMap4.put("sql1", "update dn_app_revenue_total set bd_revenue=#{li.income} where tdate=#{li.tdate} and appid=#{li.dnappid} ");
			paramMap4.put("list", list);
			yyhzMapper.batchExecSqlTwo(paramMap4);*/

			for (int i = 0; i < access.length; i++) {

				UbapiClient client = new UbapiClient("https://ubapi.baidu.com", tokens[i], UbapiClientSample.PRIVATE_KEY);

				String uri = "/ssp/1/sspservice/app/app-report/get-app-report?beginDate="+tdate+"&endDate="+tdate+"";
				HttpResponse response = client.get(uri);

				HttpEntity entity = response.getEntity();
				String res = EntityUtils.toString(entity, "UTF8");
				if(BlankUtils.isJSONObject(res) && "0".equals(JSONObject.parseObject(res).getString("code"))) {
					JSONArray array = JSONObject.parseObject(res).getJSONArray("data");
					for (int j = 0; j < array.size(); j++) {
						JSONObject info = array.getJSONObject(j);
						info.put("tdate", tdate);
						info.put("account", access[i]);
						info.put("bdappid", info.getString("appId"));
						info.put("type",1);
						Map<String, Object> act = bdMap.get(info.getString("appId"));
						if(act != null) {
							info.put("dnappid", act.get("appid"));
						}
						list.add(info);
					}
				}else{
					String error = tdate+" 百度app 错误返回：" + res.replace("\n", ",");
					FeishuUtils.sendMsgToGroupRobot(BAIDU_CHAT_ID,
							error,
							"all");
					logger.error(error);
					return false;
				}
				Thread.sleep(2000L);
			}

			for(int i =0;i<list.size();i++){
				JSONObject jsonObject = list.get(i);
				String code = jsonObject.get("adPositionId")==null?"":jsonObject.get("adPositionId").toString();
				jsonObject.put("ad_sid",sidMap.get(code)==null?"msg":sidMap.get(code));
				jsonObject.put("placement_type",positionMap.get(code)==null?"msg":positionMap.get(code));
				jsonObject.put("open_type",openTypeMap.get(code)==null?"msg":openTypeMap.get(code));
				String channelMark = markMap.get(code);
				if(channelMark !=null && channelMark !=""){
					jsonObject.put("cha_id",channelMark==null?"baidu":channelMark);
					jsonObject.put("cha_sub_launch",channelMap.get(channelMark)==null?"百度信息流":channelMap.get(channelMark));
					jsonObject.put("cha_type_name",channelTypeMap.get(channelMark)==null?"自推广":channelTypeMap.get(channelMark));
					jsonObject.put("cha_media",mediaMap.get(channelMark)==null?"百度":mediaMap.get(channelMark));
					jsonObject.put("cha_type",typeIdMap.get(channelMark)==null?"7":typeIdMap.get(channelMark));
				}else{
					jsonObject.put("cha_id","baidu");
					jsonObject.put("cha_sub_launch","百度信息流");
					jsonObject.put("cha_type_name","自推广");
					jsonObject.put("cha_media","百度");
					jsonObject.put("cha_type","7");
				}

			}
			// 同步收入数据至 变现平台明细表
			String del = "delete from dn_cha_cash_total where date='"+tdate+"' and agent='Baidu'";
			yyhzMapper.execSql(del);

			if(list.size()>0){
				Map<String, Object> cashMap = new HashMap<String, Object>();
				cashMap.put("sql1", "INSERT INTO dn_cha_cash_total (member_id,app_id,placement_id,`date`,request_count,return_count,pv,click,revenue,dnappid,agent,placement_type,ad_sid,cha_type,cha_media,cha_sub_launch,cha_id,cha_type_name,open_type,country,source,`type`) values ");
				cashMap.put("sql2", " (#{li.account},#{li.bdappid},#{li.adPositionId},#{li.tdate},#{li.request},0,#{li.view},#{li.click},#{li.income},#{li.dnappid},'Baidu',#{li.placement_type},#{li.ad_sid},#{li.cha_type},#{li.cha_media},#{li.cha_sub_launch},#{li.cha_id},#{li.cha_type_name},#{li.open_type},'CN',1,#{li.type}) ");
				cashMap.put("sql3", " ");
				cashMap.put("list", list);
				yyhzMapper.batchExecSql(cashMap);
				//TODO ADB添加Baidu变现明细数据
				dnwxBiMapper.execSql(del);
				dnwxBiMapper.batchExecSql(cashMap);
			} else {
				FeishuUtils.sendMsgToGroupRobot(BAIDU_CHAT_ID,
						tdate + " 百度收入数据为空",
						"all");
			}

			// 更新 锁屏新闻内容成功展示人数，通过友盟key匹配动能appid
			// 2023 8 8 废弃，表已经废弃
//			String query3 = "select umeng_key as mapkey,id as appid,app_name from app_info where umeng_key != ''";
//			Map<String, Map<String, Object>> umMap = yyhzMapper.queryListMapOfKey(query3);
//
//			String sql3 = "SELECT '"+tdate+"' tdate,app_key,"+
//					"	COUNT(DISTINCT CASE WHEN event_name = 'B_lock_news_show_success' THEN umid END) news_num, "+
//					"	COUNT(DISTINCT CASE WHEN event_name = 'B_lock_news_screen_show' THEN umid END) show_num, "+
//					"	COUNT(DISTINCT CASE WHEN event_name = 'B_lock_screen_started' THEN umid END) finsh_num "+
//					"FROM `dwd_ump_log_uapp_event_json_di` "+
//					"WHERE ds = '"+tdate+"' "+
//					"AND (event_name='B_lock_news_show_success' or event_name='B_lock_news_screen_show' or event_name='B_lock_screen_started') GROUP BY app_key";
//			List<Map<String, String>> list3 = appUmengAdMapper.queryListMapOne(sql3);
//			list3.forEach(act -> {
//				Map<String, Object> map = umMap.get(act.get("app_key"));
//				if(map != null){
//					act.put("appid", map.get("appid")+"");
//				}
//			});
//			if(list3!=null && !list3.isEmpty()) {
//				Map<String, Object> paramMap3 = new HashMap<String, Object>();
//				paramMap3.put("sql1", "update baidu_cash_total set news_num=#{li.news_num},show_num=#{li.show_num},finsh_num=#{li.finsh_num} where tdate=#{li.tdate} and appid=#{li.appid} ");
//				paramMap3.put("list", list3);
//				adv2Mapper.batchExecSqlTwo(paramMap3);
//			}

			logger.info("同步syncBaiduCashTotal 成功...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("同步syncBaiduCashTotal 异常...");
		}
		return false;
	}


	@Override
	public ExtendAdsidVo getDnAdSid(String adsid) {
		return adv2Mapper.getDnAdSid(adsid);
	}

	@Override
	public List<UserStrategyV2Vo> getUserStrategyConfigList(Map map) {
		return adv2Mapper.getUserStrategyConfigList(map);
	}

	@Transactional(value = "adv2TransactionManager")
	@Override
	public int updateUserStrategyConfig(List<UserStrategyV2Vo> list, UserStrategyV2Vo config) {
		//先备份
		adv2Mapper.copyUserStrategyConfigList(config);
		//删除
		adv2Mapper.delUserStrategyConfig(config);
		//写入
		int succ3 = adv2Mapper.saveUserStrategyConfigList(list);
		return succ3;
	}


	@Override
	public List<Map<String, Object>> selectDnExtendAdconfig(ExtendAdconfigVo app){
		String sql = "select xx.*,concat(xx.createtime,'') date,yy.cha_type,m.sdk_code sdk_code,m.sdk_appid sdk_appid,m.bidding,m.out  from dn_extend_adconfig xx "+
				"LEFT JOIN (select cha_id chaid,cha_type from yyhz_0308.dn_channel_info) yy ON xx.cha_id=yy.chaid "+
				"LEFT JOIN dn_extend_adsid_manage m  ON xx.adsid=m.adsid " +
				"where 1=1";
		//最后操作时间条件查询
		if (!BlankUtils.checkBlank(app.getStart_date()) && !BlankUtils.checkBlank(app.getEnd_date())) {
			sql += " and xx.lasttime BETWEEN '"+app.getStart_date()+" 00:00:00' AND '"+app.getEnd_date()+" 23:59:59'";
		}
		if(!BlankUtils.checkBlank(app.getAppid()))
			sql += " and xx.appid in ("+app.getAppid()+")";
		if(!BlankUtils.checkBlank(app.getCha_type()))
			sql += " and yy.cha_type = #{obj.cha_type}";
		if(!BlankUtils.checkBlank(app.getCha_id()) && !"null".equals(app.getCha_id()))
			sql += " and xx.cha_id = #{obj.cha_id}";
		if ("null".equals(app.getCha_id()))
			sql += " and (ISNULL(xx.cha_id)=1 or LENGTH(trim(xx.cha_id))=0) ";
		if(!BlankUtils.checkBlank(app.getStatu()))
			sql += " and xx.statu = #{obj.statu}";
		if(!BlankUtils.checkBlank(app.getAdpos_type()))
			sql += " and xx.adpos_type = #{obj.adpos_type}";
		if(!BlankUtils.checkBlank(app.getStrategy()))
			sql += " and xx.strategy in ("+app.getStrategy()+")";

		if(!BlankUtils.checkBlank(app.getPrjid()) && !"null".equals(app.getPrjid()))
			sql += " and xx.prjid = #{obj.prjid}";
		if ("null".equals(app.getPrjid()))
			sql += " and (xx.prjid is null or xx.prjid = '') ";
		if(!BlankUtils.checkBlank(app.getAdsid()))
			sql += " and xx.adsid like concat('%',#{obj.adsid},'%')";
		if(!BlankUtils.checkBlank(app.getBuy_id()))
			sql += " and xx.buy_id = #{obj.buy_id}";
		if(!BlankUtils.checkBlank(app.getBuy_act()))
			sql += " and xx.buy_act = #{obj.buy_act}";
		if(!BlankUtils.checkBlank(app.getEcpms()))
			sql += " and xx.ecpm in ("+app.getEcpms()+")";
		if(!BlankUtils.checkBlank(app.getUser_group()))
			sql += " and xx.user_group = #{obj.user_group}";
		if(!BlankUtils.checkBlank(app.getIs_newuser()))
			sql += " and xx.is_newuser in ("+app.getIs_newuser()+")";
		if(!BlankUtils.checkBlank(app.getCuser()))
			sql += " and xx.cuser = #{obj.cuser}";
		if(!BlankUtils.checkBlank(app.getPrjid_group_id())) {

			List<String> idList = new ArrayList<String>();
			for (String id : app.getPrjid_group_id().split(",")) {
				idList.add(String.format("FIND_IN_SET('%s', xx.prjid_group_id) > 0", id));
			}
			sql += " and (" + String.join(" or ", idList) + ")";
		}

		if(!BlankUtils.checkBlank(app.getSdk_appid())){
			sql += " and m.sdk_appid = #{obj.sdk_appid}";
		}
		if(!BlankUtils.checkBlank(app.getSdk_code())){
			sql += " and m.sdk_code = #{obj.sdk_code}";
		}
		if (!BlankUtils.checkBlank(app.getBidding())) {
			sql += " and m.bidding = #{obj.bidding}";
		}

		/* 渠道产品自定义分组 */
		if(!BlankUtils.checkBlank(app.getAppid_tag())){
			if(!BlankUtils.checkBlank(app.getAppid_tag_rev())) {
				sql += " and CONCAT(xx.appid,'#',xx.cha_id) not in (" +app.getAppid_tag()+ ") ";
			}else {
				sql += " and CONCAT(xx.appid,'#',xx.cha_id) in (" +app.getAppid_tag()+ ") ";
			}
		}

		sql = sql + " order by xx.createtime desc";

		return queryListMapTwo(sql, app);
	}

	@Override
	public List<Map<String, Object>> selectDnExtendAdsidManage(ExtendAdsidVo app){
		String sql = "select *,concat(createtime,'') date from dn_extend_adsid_manage where 1=1";
		if(!BlankUtils.checkBlank(app.getAppid()))
			sql += " and appid in ("+app.getAppid()+")";
		if(!BlankUtils.checkBlank(app.getCha_id()))
			sql += " and cha_id = #{obj.cha_id}";
		if(!BlankUtils.checkBlank(app.getAgent()))
			sql += " and agent = #{obj.agent}";
		if(!BlankUtils.checkBlank(app.getAdsid()))
			sql += " and adsid like concat('%',#{obj.adsid},'%')";
		if(!BlankUtils.checkBlank(app.getSdk_adtype()))
			sql += " and sdk_adtype = #{obj.sdk_adtype}";
		if(!BlankUtils.checkBlank(app.getSdk_code()))
			sql += " and sdk_code like concat('%',#{obj.sdk_code},'%')";
		if(!BlankUtils.checkBlank(app.getSdk_appid()))
			sql += " and sdk_appid like concat('%',#{obj.sdk_appid},'%')";
		if(!BlankUtils.checkBlank(app.getOpen_type()))
			sql += " and open_type = #{obj.open_type}";
		if(!BlankUtils.checkBlank(app.getBidding()))
			sql += " and bidding = #{obj.bidding}";
		if(!BlankUtils.checkBlank(app.getCuser()))
			sql += " and cuser = #{obj.cuser}";
		if(!BlankUtils.checkBlank(app.getOut()))
			sql += " and `out` = #{obj.out}";

		sql = sql + " order by createtime desc";

		return queryListMapTwo(sql, app);
	}

	@Override
	public List<Map<String, Object>> selectDnExtendAdposManage(ExtendAdposVo app){
		String sql = "select * from dn_extend_adpos_manage where 1=1";
		if(!BlankUtils.checkBlank(app.getAppid()))
			sql += " and appid in ("+app.getAppid()+")";
		if(!BlankUtils.checkBlank(app.getCha_id()) && !("null").equals(app.getCha_id()))
			sql += " and cha_id in (" + app.getCha_id()+ ")";
		if ("null".equals(app.getCha_id()))
			sql += " and (cha_id is null or cha_id = '') ";

		if(!BlankUtils.checkBlank(app.getPrjid()) && !"null".equals(app.getPrjid()))
			sql += " and prjid = #{obj.prjid}";
		if ("null".equals(app.getPrjid()))
			sql += " and (prjid is null or prjid = '') ";

		if(!BlankUtils.checkBlank(app.getUser_group()))
			sql += " and user_group in ("+app.getUser_group()+")";
		if(!BlankUtils.checkBlank(app.getStatu()))
			sql += " and statu = #{obj.statu}";
		if(!BlankUtils.checkBlank(app.getAdpos_type()))
			sql += " and adpos_type = #{obj.adpos_type}";
		if(!BlankUtils.checkBlank(app.getStrategy()))
			sql += " and strategy = #{obj.strategy}";
		if(!BlankUtils.checkBlank(app.getAdpos()))
			sql += " and adpos like concat('%',#{obj.adpos},'%')";
		if(!BlankUtils.checkBlank(app.getAdstyle()))
			sql += " and adstyle =#{obj.adstyle}";
		if(!BlankUtils.checkBlank(app.getOut()))
			sql += " and `out` =#{obj.out}";
		if(!BlankUtils.checkBlank(app.getCuser()))
			sql += " and cuser =#{obj.cuser}";
		if(!BlankUtils.checkBlank(app.getLasttime())) {
			String[] split = app.getLasttime().split(",");
			if(split != null && split.length == 2) {
				sql += String.format(" and lasttime BETWEEN '%s 00:00:00' AND '%s 23:59:59'", split[0], split[1]);
			}
		}
		if(!BlankUtils.checkBlank(app.getFilter_id()))
			sql += " and id in ("+app.getFilter_id()+")";

		if(!BlankUtils.checkBlank(app.getPrjid_group_id())) {

			List<String> idList = new ArrayList<String>();
			for (String id : app.getPrjid_group_id().split(",")) {
				idList.add(String.format("FIND_IN_SET('%s', prjid_group_id) > 0", id));
			}
			sql += " and (" + String.join(" or ", idList) + ")";
		}

		if(!BlankUtils.checkBlank(app.getNote())){
			List<String> notes = new ArrayList<String>();
			String[] split = app.getNote().split(",");
			for (String str : split) {
				notes.add(" note like concat('%',"+str+",'%') ");
			}

			sql += " and ("+String.join(" or ", notes)+") ";
		}

		if(!BlankUtils.checkBlank(app.getAppid_tag())){
			if(!BlankUtils.checkBlank(app.getAppid_tag_rev())) {
				sql += " and CONCAT(appid,'#',cha_id) not in (" +app.getAppid_tag()+ ") ";
			}else {
				sql += " and CONCAT(appid,'#',cha_id) in (" +app.getAppid_tag()+ ") ";
			}
		}

		sql = sql + " order by createtime desc";

		return queryListMapTwo(sql, app);
	}


	@Override
	public String insertAdposFromAdconfig(Map<String, String> paramMap) {

		// 设置子渠道对应的渠道筛选值
		String chaId = paramMap.get("cha_id");
		if(!BlankUtils.checkBlank(chaId)){

			// 改造为适应所有渠道
			if (chaId.startsWith("xiaomi")) {
				paramMap.put("channel", "xiaomi");
			} else if (chaId.startsWith("huawei")) {
				paramMap.put("channel", "huawei");
			} else if (chaId.startsWith("oppo")) {
				paramMap.put("channel", "oppo");
			} else if (chaId.startsWith("vivo")) {
				paramMap.put("channel", "vivo");
			} else if ("xmyy".equals(chaId)) {
				paramMap.put("channel", "xiaomi");
			} else {
				paramMap.put("channel", chaId);
			}
		} else {
			paramMap.put("channel", "csj");
		}

		/* 增加ios产品同步配置：子渠道为apple或子渠道为空但产品识别为ios产品时（产品维度）-林小敏.20231031 */
		List<String> iosList = yyhzMapper.queryListString("select CONCAT(id,'') id from app_info where os_type=2");
		if(BlankUtils.checkBlank(chaId) && iosList.contains(paramMap.get("appid"))){
			paramMap.put("channel", "apple");
		}

		List<ExtendAdposVo> list = adv2Mapper.selectAdconfigToAdpos(paramMap);

		// 找到数据库中的配置json，根据json解析成配置，配置的模式为：  {condition ： {xxxx：xx},   result ： {xxxx:xx}}
		// 每一条condition中可能有多个，是and的关系， result同理，全部都需要赋值
		List<ExtendAdposVo> textConfigs = adv2Mapper.selectTextConfig(paramMap);
		List<String> configs = textConfigs.stream().map(ExtendAdposVo::getConfig).collect(Collectors.toList());
		List<Manage2AdposEntity> manage2AdposEntities = Manage2AdposEntity.parseJsonObjects(configs);

		list.addAll(textConfigs);

		if (CollectionUtils.isEmpty(list)) {
			return ReturnJson.toErrorJson("配置中缺少相关渠道，请检查配置");
		}

		// 创建key val形式的map，用于设置strategy默认值 -自推广渠道O\V\H\M
		Map<String, String> adposMap = new HashMap<String, String>(){{
				put("splash", "splash_rate");
				put("banner", "banner_rate");
				put("video", "video_waterfall");
				put("plaque", "plaque_waterfall");
				put("msg", "msg_waterfall");
				put("icon", "icon_waterfall_minivideo");
		}};

		/* 增加ios产品同步配置：子渠道为apple或子渠道为空但产品识别为ios产品时（产品维度）-林小敏.20231031 */
		if((BlankUtils.checkBlank(chaId) && iosList.contains(paramMap.get("appid"))) || "apple".equalsIgnoreCase(chaId)) {

			adposMap.put("splash", "splash_rate");
			adposMap.put("banner", "banner_rate");
			adposMap.put("video", "video_waterfall_bid_new_ios");
			adposMap.put("plaque", "plaque_waterfall_bid_new_ios");
			adposMap.put("msg", "msg_waterfall_bid_new_ios");
			adposMap.put("icon", "icon_waterfall_minivideo");
		}

		// 结果针对传入的use_strategy值进行更新
		String[] types = {"splash","banner","video","plaque","msg","icon"};
		String[] strategys = {"splash_use_strategy","banner_use_strategy","video_use_strategy","plaque_use_strategy","msg_use_strategy","icon_use_strategy"};
		Map<String,String> stMap = new HashMap<String,String>();
		for (int i = 0; i < strategys.length; i++) {
			stMap.put(types[i], strategys[i]);
		}

		list.forEach(act -> {
			// 针对list集合中缺少的strategy补全默认策略
			if(BlankUtils.checkBlank(act.getStrategy())) {
				act.setStrategy(adposMap.get(act.getAdpos_type()));
			}

			// 针对多个strategy的情况进行广告位类型覆盖，使用前端传入的strategy值
			// 增加限制条件，当该strategy为锁定状态策略时，不使用页面选择的源进行替换 -林小敏.20240320
			String strategy = stMap.get(act.getAdpos_type());
			if(strategy != null && !BlankUtils.checkBlank(paramMap.get(strategy))
					&& (act.getConfig_first() == null || 1 != act.getConfig_first())){

				act.setStrategy(paramMap.get(strategy));
			}


			// 针对oppo的特殊广告位sdk_msg_x3_out进行处理
			if(("oppo".equals(paramMap.get("channel")) || "xiaomi".equals(paramMap.get("channel")) || "vivo".equals(paramMap.get("channel"))) && "sdk_msg_x3_out".equals(act.getAdpos())){
				act.setStrategy("msg_waterfall_x3zs");
			}

			// 设置默认值
			act.setXdelay("0");
			act.setShowrate(100);
			act.setAutoInterval("0");
			act.setOut("0");
			act.setStatu("1");

			try {
				// 使用 manage2AdposEntities 更新 指定字段
				manage2AdposEntities.forEach(en -> {
					try {
						en.processConfig(act);
					} catch (NoSuchFieldException | IllegalAccessException e) {
						throw new RuntimeException(e);
					}
				});

				// 使用extra_config字段定义的配置更新adstyle
				if (BlankUtils.isNotBlank(act.getExtra_config())) {
					List<JSONObject> extraConfigs = JSONArray.parseArray(act.getExtra_config(), JSONObject.class);
					for (JSONObject config : extraConfigs) {
						String adstyle = config.getString("adstyle");
						if (BlankUtils.isBlank(adstyle)) {
							continue;
						}
						boolean equals = true;
						for (Map.Entry<String, Object> entry : config.entrySet()) {
							if (entry.getKey().equals("adstyle")) {
								continue;
							}
							if (!equals || entry.getValue() == null || BlankUtils.isBlank(entry.getValue().toString())) {
								equals = false;
								break;
							}
							equals = act.isMatch(entry.getKey(), entry.getValue().toString());
						}
						if (equals) {
							act.setAdstyle(adstyle);
						}
					}
				}
			} catch (Throwable e) {
				FeishuUtils.sendMsgToGroupRobot(SyncDataConstants.CHAT_ID,
						"广告配置同步至广告位时产生异常 " + e.getMessage(),
						"all");
			}
		});


		Map<String, Object> paramMap2 = new HashMap<String, Object>();
		paramMap2.put("sql1", "insert into dn_extend_adpos_manage(appid,cha_id,prjid,adpos_type,adpos,strategy,user_group,adstyle,xdelay,showrate,delaySecond,startLv,endLv,lvInterval,autoInterval,`out`,custom_param,statu,note,createtime,lasttime,cuser) values ");
		paramMap2.put("sql2", " (#{li.appid},#{li.cha_id},#{li.prjid},#{li.adpos_type},#{li.adpos},#{li.strategy},#{li.user_group},#{li.adstyle},#{li.xdelay},#{li.showrate},#{li.delaySecond},#{li.startLv},#{li.endLv},#{li.lvInterval},#{li.autoInterval},#{li.out},#{li.custom_param},#{li.statu},#{li.note},now(),now(),'"+paramMap.get("cuser")+"' ) ");
		paramMap2.put("sql3", " ");
		paramMap2.put("list", list);

		// 请求刷新缓存
		String url1 = "https://edc.vigame.cn:6115/recacheNewWjy?mapid=301";
		HttpClientUtils.getInstance().httpGet(url1);
		adv2Mapper.batchExecSql(paramMap2);

		return "{\"ret\":1,\"msg\":\"操作成功!\"}";
	}

	@Override
	public String updateAdposFromAdconfig(Map<String, String> paramMap) {
		String where = " where statu=1 and appid=#{obj.appid} and user_group=#{obj.user_group} ";
		if(!BlankUtils.checkBlank(paramMap.get("cha_id"))) {
			where += " and cha_id=#{obj.cha_id}";
		}else{
			where += " and (cha_id = '' or cha_id is null)";
		}
		if(!BlankUtils.checkBlank(paramMap.get("prjid"))){
			where += " and prjid=#{obj.prjid}";
		}else{
			where += " and (prjid = '' or prjid is null)";
		}

		String query = "select adpos_type,strategy from dn_extend_adconfig " +where+
				" group by appid,cha_id,prjid,user_group,adpos_type ";
		List<Map<String, Object>> list = queryListMapTwo(query, paramMap);

		// 结果针对use_strategy的值进行更新
		String[] strategys = {"splash_use_strategy","banner_use_strategy","video_use_strategy","plaque_use_strategy","msg_use_strategy","icon_use_strategy"};
		String[] types = {"splash","banner","video","plaque","msg","icon"};
		Map<String,String> stMap = new HashMap<String,String>();
		for (int i = 0; i < strategys.length; i++) {
			stMap.put(types[i], strategys[i]);
		}

		list.forEach(act -> {
			String strategy = stMap.get(act.get("adpos_type").toString());
			if(strategy != null && !BlankUtils.checkBlank(paramMap.get(strategy))){
				act.put("strategy", paramMap.get(strategy));
			}
		});

		for (Map<String, Object> act : list) {

			// 执行修改操作，增加adpos_type的条件来修改strategy
			String update = "update dn_extend_adpos_manage set strategy='"+act.get("strategy")+"' "+where+" and adpos_type='"+act.get("adpos_type")+"' "+
					"and (cha_id not in ('oppo','oppo2','oppomj','oppoml','opposd','xiaomi','xiaomimj','xiaomiml','xmyy','xiaomiwt','vivo','vivo2','vivoml','vivoml2','vivosd') or adpos != 'sdk_msg_x3_out') ";
			adv2Mapper.execSqlHandle(update, paramMap);
		}

		return "{\"ret\":1,\"msg\":\"操作成功!\"}";
	}

	@Override
	public List<Map<String, Object>> selectSdkRelationReport(Map<String, String> paramMap) {
		String temp_id = paramMap.get("temp_id");
		if(temp_id != null && !temp_id.trim().equals("")){
			String value = Arrays.stream(temp_id.trim().split(","))
					.map(input -> "'" + input + "'" + ",")
					.collect(Collectors.joining());
			paramMap.put("temp_id", value.substring(0, value.length() - 1));
		}

		return clientSDKMapper.selectSdkRelationReport(paramMap);
	}

	@Override
	public boolean syncMonetizationReport(Map<String, String> paramMap){
		// 主体数据同步
		List<String> dateList = DateUtils.getBetweenDates(paramMap.get("start_date"), paramMap.get("end_date"));
		for (int i = 0; i < dateList.size(); i++) {
			String day = dateList.get(i);
			addChinaMonetizationReportByDay(day);
		}

		// 小游戏更新部分字段
		try {
			// 17-微信-IAP，39-微信-IAA，43-抖音小游戏，47-快手小游戏
			String query = "select id from app_info where app_category in (17,39,43,47) ";
			List<String> appList = yyhzMapper.queryListString(query);
			String apps = String.join(",", appList);

			String query2 = "select tdate,appid,CONCAT('h5_',download_channel) cha_id,add_user_cnt add_num,active_user_cnt act_num from dnwx_bi.ads_user_iap_revenue_info_daily " +
					" where tdate BETWEEN #{obj.start_date} AND #{obj.end_date} and appid in ("+apps+") ";
			List<Map<String, Object>> forList = dnwxBiMapper.queryListMapTwo(query2, paramMap);

			/** 执行更新操作，写入新增活跃到 dn_adt.dn_report_monetization_summary_china 变现汇总表 */
			try {

				Map<String, Object> paramMap2 = new HashMap<String, Object>();
				paramMap2.put("sql1", "update dn_report_monetization_summary_china set dau=#{li.act_num},installs=#{li.add_num} where `day`=#{li.tdate} and app=#{li.appid} and cha_id=#{li.cha_id} and app in ("+apps+") ");
				paramMap2.put("list", forList);
				// 2023.8.1 切换至变现表
				adv2Mapper.batchExecSqlTwo(paramMap2);

				return true;
			} catch (Exception e) {
				e.printStackTrace();
			}

		} catch (Exception e) {
			e.printStackTrace();
			logger.info("变现汇总表新增活跃同步 异常...");
		}
		return false;
	}

	public String addChinaMonetizationReportByDay(String day) {
		List<ChinaMonetizationReport> chinaMonetizationReportList = yyhzMapper.getChinaMonetizationSummaryReportByDay(day);
		if (CollectionUtils.isEmpty(chinaMonetizationReportList)) {
			return ReturnJson.toErrorJson("chinaMonetizationReportList is null");
		}
		List<ReportChina> reportChinaList = dnwxBiMapper.getChinaMonetizationReportByDay(day);
		if (CollectionUtils.isEmpty(reportChinaList)) {
			return ReturnJson.toErrorJson("reportChinaList is null");
		}

		List<Channel> channelList = cashReportMapper.getChannel();
		Map<String, String> channelMap = new HashMap<>(channelList.size());
		Map<String, String> channelTypeMap = new HashMap<>(channelList.size());
		Map<String, String> mediaMap = new HashMap<>(channelList.size());
		if (!CollectionUtils.isEmpty(channelList)) {
			for (int i = 0; i < channelList.size(); i++) {
				Channel channel = channelList.get(i);
				String mark = channel.getCha_id();
				channelMap.put(mark, channel.getCha_sub_launch());
				channelTypeMap.put(mark, channel.getType_name());
				mediaMap.put(mark, channel.getCha_media());
			}
		}

		final String SPLIT = "_";
		Map<String, Double> renvenueByTypeMap = new HashMap<>(reportChinaList.size());
		Map<String, Long> impressionsByTypeMap = new HashMap<>(reportChinaList.size());
		Map<String, Double> outrenvenueByTypeMap = new HashMap<>(reportChinaList.size());
		Map<String, Long> outimpressionsByTypeMap = new HashMap<>(reportChinaList.size());
		for (int i = 0; i < reportChinaList.size(); i++) {
			ReportChina report = reportChinaList.get(i);
			StringJoiner joiner = new StringJoiner(SPLIT);
			joiner.add(report.getDate());
			joiner.add(StringUtil.getString(report.getDnappid()));
			joiner.add(report.getCha_id());
			joiner.add(report.getCountry());
			joiner.add(report.getOpen_type());
			String key = joiner.toString();

			renvenueByTypeMap.put(key, report.getRevenue());
			impressionsByTypeMap.put(key, Long.valueOf(report.getPv()));
			outimpressionsByTypeMap.put(key, Long.valueOf(report.getPv_out()));
			outrenvenueByTypeMap.put(key, report.getRevenue_out());
		}

		final String VIDEO = "video";
		final String PLAQUE = "plaque";
		final String BANNER = "banner";
		final String SPLASH = "splash";
		final String MSG = "msg";

		for (int i = 0; i < chinaMonetizationReportList.size(); i++) {
			ChinaMonetizationReport chinaMonetizationReport = chinaMonetizationReportList.get(i);

			String cha_id = chinaMonetizationReport.getCha_id();
			if (!Strings.isNullOrEmpty(cha_id)) {
				chinaMonetizationReport.setCha_type_name(StringUtil.getString(channelTypeMap.get(cha_id)));
				chinaMonetizationReport.setCha_media(StringUtil.getString(mediaMap.get(cha_id)));
				chinaMonetizationReport.setCha_sub_launch(StringUtil.getString(channelMap.get(cha_id)));
			}

			StringJoiner joiner = new StringJoiner(SPLIT);
			joiner.add(chinaMonetizationReport.getDay());
			joiner.add(chinaMonetizationReport.getApp());
			joiner.add(chinaMonetizationReport.getCha_id());
			joiner.add(chinaMonetizationReport.getCountry());
			String key = joiner.toString();

			String videoKey = key + SPLIT + VIDEO;
			String plaqueKey = key + SPLIT + PLAQUE;
			String bannerKey = key + SPLIT + BANNER;
			String splashKey = key + SPLIT + SPLASH;
			String msgKey = key + SPLIT + MSG;

			long impressions_video = impressionsByTypeMap.get(videoKey) == null ? 0 : impressionsByTypeMap.get(videoKey);
			long impressions_plaque = impressionsByTypeMap.get(plaqueKey) == null ? 0 : impressionsByTypeMap.get(plaqueKey);
			long impressions_banner = impressionsByTypeMap.get(bannerKey) == null ? 0 : impressionsByTypeMap.get(bannerKey);
			long impressions_splash = impressionsByTypeMap.get(splashKey) == null ? 0 : impressionsByTypeMap.get(splashKey);
			long impressions_msg = impressionsByTypeMap.get(msgKey) == null ? 0 : impressionsByTypeMap.get(msgKey);

			double revenue_video = renvenueByTypeMap.get(videoKey) == null ? 0 : renvenueByTypeMap.get(videoKey);
			double revenue_plaque = renvenueByTypeMap.get(plaqueKey) == null ? 0 : renvenueByTypeMap.get(plaqueKey);
			double revenue_banner = renvenueByTypeMap.get(bannerKey) == null ? 0 : renvenueByTypeMap.get(bannerKey);
			double revenue_splash = renvenueByTypeMap.get(splashKey) == null ? 0 : renvenueByTypeMap.get(splashKey);
			double revenue_msg = renvenueByTypeMap.get(msgKey) == null ? 0 : renvenueByTypeMap.get(msgKey);

			long outimpressions_video = outimpressionsByTypeMap.get(videoKey) == null ? 0 : outimpressionsByTypeMap.get(videoKey);
			long outimpressions_plaque = outimpressionsByTypeMap.get(plaqueKey) == null ? 0 : outimpressionsByTypeMap.get(plaqueKey);
			long outimpressions_banner = outimpressionsByTypeMap.get(bannerKey) == null ? 0 : outimpressionsByTypeMap.get(bannerKey);
			long outimpressions_splash = outimpressionsByTypeMap.get(splashKey) == null ? 0 : outimpressionsByTypeMap.get(splashKey);
			long outimpressions_msg = outimpressionsByTypeMap.get(msgKey) == null ? 0 : outimpressionsByTypeMap.get(msgKey);

			double outrevenue_video = outrenvenueByTypeMap.get(videoKey) == null ? 0 : outrenvenueByTypeMap.get(videoKey);
			double outrevenue_plaque = outrenvenueByTypeMap.get(plaqueKey) == null ? 0 : outrenvenueByTypeMap.get(plaqueKey);
			double outrevenue_banner = outrenvenueByTypeMap.get(bannerKey) == null ? 0 : outrenvenueByTypeMap.get(bannerKey);
			double outrevenue_splash = outrenvenueByTypeMap.get(splashKey) == null ? 0 : outrenvenueByTypeMap.get(splashKey);
			double outrevenue_msg = outrenvenueByTypeMap.get(msgKey) == null ? 0 : outrenvenueByTypeMap.get(msgKey);

			chinaMonetizationReport.setPv_video(impressions_video);
			chinaMonetizationReport.setPv_plaque(impressions_plaque);
			chinaMonetizationReport.setPv_banner(impressions_banner);
			chinaMonetizationReport.setPv_splash(impressions_splash);
			chinaMonetizationReport.setPv_msg(impressions_msg);

			chinaMonetizationReport.setRevenue_video(revenue_video);
			chinaMonetizationReport.setRevenue_plaque(revenue_plaque);
			chinaMonetizationReport.setRevenue_banner(revenue_banner);
			chinaMonetizationReport.setRevenue_splash(revenue_splash);
			chinaMonetizationReport.setRevenue_msg(revenue_msg);

			chinaMonetizationReport.setOut_pv_video(outimpressions_video);
			chinaMonetizationReport.setOut_pv_plaque(outimpressions_plaque);
			chinaMonetizationReport.setOut_pv_banner(outimpressions_banner);
			chinaMonetizationReport.setOut_pv_splash(outimpressions_splash);
			chinaMonetizationReport.setOut_pv_msg(outimpressions_msg);

			chinaMonetizationReport.setOut_revenue_video(outrevenue_video);
			chinaMonetizationReport.setOut_revenue_plaque(outrevenue_plaque);
			chinaMonetizationReport.setOut_revenue_banner(outrevenue_banner);
			chinaMonetizationReport.setOut_revenue_splash(outrevenue_splash);
			chinaMonetizationReport.setOut_revenue_msg(outrevenue_msg);
		}

		int count = monetizationSummaryMapper.getChinaMonetizationReportCount(day);
		if (count > 0) {
			monetizationSummaryMapper.delChinaMonetizationReport(day);
		}

		if (chinaMonetizationReportList.size() <= 10000) {
			monetizationSummaryMapper.batchAddChinaMonetizationReport(chinaMonetizationReportList);
		} else {
			List<List<ChinaMonetizationReport>> lists = Lists.partition(chinaMonetizationReportList, 10000);
			for (int i = 0; i < lists.size(); i++) {
				monetizationSummaryMapper.batchAddChinaMonetizationReport(lists.get(i));
			}
		}

		return ReturnJson.success();
	}

	@Override
	public List<JSONObject> getUserStrategyConfigList(UserStrategyV2Vo app) {
		List<UserStrategyV2Vo> configList = adv2Mapper.getUserStrategyConfigListV2(app);
		Map<String,List<UserStrategyV2Vo>> configMap = configList.stream().collect(Collectors.groupingBy(t->t.getSid()+"_"+t.getGroup()));
		List<JSONObject> dataList = new ArrayList<>();
		for (Map.Entry<String,List<UserStrategyV2Vo>> each:configMap.entrySet()){
			JSONObject data = new JSONObject();
			String[] key = each.getKey().split("_");
			data.put("sid",key[0]);
			data.put("group",key[1]);
			List<UserStrategyV2Vo> list = each.getValue();
			if (list.size()>0){
				UserStrategyV2Vo first =list.get(0);
				JSONArray paramsArray = new JSONArray();
				for(UserStrategyV2Vo vo:list){
					JSONObject json = new JSONObject();
					json.put("param1",vo.getParam1());
					json.put("param2",vo.getParam2());
					json.put("param3",vo.getParam3());
					json.put("id",vo.getId());
					paramsArray.add(json);
				}
				data.put("params",paramsArray);
				data.put("cuser",first.getCuser());
				data.put("euser",first.getEuser());
				data.put("createtime",first.getCreatetime());
				data.put("note",first.getNote());
				data.put("endtime",first.getEndtime());
				dataList.add(data);
			}
		}
		return dataList;
	}

	private static class ConfigCondition {
		private String appid;
		private String cha_id;

		public ConfigCondition(String appid, String cha_id) {
			this.appid = appid;
			this.cha_id = cha_id;
		}

		@Override
		public boolean equals(Object o) {
			if (this == o) return true;

			if (o == null || getClass() != o.getClass()) return false;

			ConfigCondition that = (ConfigCondition) o;

			return new EqualsBuilder().append(appid, that.appid).append(cha_id, that.cha_id).isEquals();
		}

		@Override
		public int hashCode() {
			return new HashCodeBuilder(17, 37).append(appid).append(cha_id).toHashCode();
		}
	}

	@Override
	public String syncAdsidToAdpos(List<ExtendAdsidVo> apps, String userName) {
		ArrayList<ExtendAdposVo> res = new ArrayList<>();

		// 同一批去重
		List<ConfigCondition> collect = apps.stream().map(o ->
				new ConfigCondition(o.getAppid(), o.getCha_id())).distinct().collect(Collectors.toList());

		for (ConfigCondition app : collect) {
			String chaId = app.cha_id;
			if (BlankUtils.isBlank(chaId)) {
				chaId = "csj";
			}
			String appid = app.appid;
			int count = adv2Mapper.selectDistinctAdpos(appid, chaId);
			if (count > 0) {
				continue;
			}

			/* 增加ios产品同步配置：子渠道为apple或子渠道为空但产品识别为ios产品时（产品维度）-林小敏.20231031 */
			boolean flag = false;
			List<String> iosList = yyhzMapper.queryListString("select CONCAT(id,'') id from app_info where os_type=2");
			if(BlankUtils.checkBlank(app.cha_id) && iosList.contains(app.appid)){
				chaId = "apple";
				flag = true;
			}

			List<ExtendAdposVo> extendAdposVos = selectDefaultConfig(chaId);

			for (ExtendAdposVo extendAdposVo : extendAdposVos) {
				// 使用extra_config字段定义的配置更新adstyle
				extendAdposVo.setAppid(appid);
				extendAdposVo.setCuser(userName);
				String extraConfig = extendAdposVo.getExtra_config();
				if (BlankUtils.isNotBlank(extraConfig)) {
					Manage2AdposEntity manage2AdposEntity = Manage2AdposEntity.parseJsonObject(extraConfig);
					try {
						manage2AdposEntity.processConfig(extendAdposVo);
					} catch (NoSuchFieldException | IllegalAccessException e) {
						logger.error("转化json出问题：", e);
						return ReturnJson.toErrorJson("转化json出问题," + e.getMessage());
					}
				}

				/* 是否带有子渠道 */
				if ("apple".equalsIgnoreCase(extendAdposVo.getCha_id()) && flag) {
					extendAdposVo.setCha_id("");
				}
				res.add(extendAdposVo);
			}
		}

		if (res.size()>0) {
			res.forEach(vo -> {
				if ("csj".equalsIgnoreCase(vo.getCha_id())) {
					vo.setCha_id("");
				}
			});
			int i = adv2Mapper.insertBatchAdpos(res);
			// 请求刷新缓存
			String url1 = "https://edc.vigame.cn:6115/recacheNewWjy?mapid=301";
			HttpClientUtils.getInstance().httpGet(url1);
			return ReturnJson.success("转化成功，共" + res.size() + "条数据，"+"存入" + i);
		}
		return ReturnJson.success("没有可以转化的广告位数据");
	}

	@Override
	public boolean recacheDnExtendAd(String mapid, String appid) {

		try {
			String url = String.format("https://edc.vigame.cn:6115/recacheNewWjy?mapid=%s&appid=%s", mapid, appid);
			String resp = HttpClientUtils.getInstance().httpGet(url);
			if(resp != null && "ok".equals(resp.trim())) {
				return true;
			}else{
				// 告警
				StringBuilder message = new StringBuilder();
				message.append("报错任务：").append("recacheDnExtendAd 刷新广告缓存").append("\n")
						.append("报错URL：").append(url).append("\n")
						.append("报错返回："+resp).append("\n");
				FeishuUtils.sendFeiShu(message.toString(), "caow");
			}
		}catch (Exception e){
			e.printStackTrace();
		}
		return false;
	}

	public static Map<String, String> CHA_AGENT_MAP = new HashMap(){{
		put("oppo", "oppo");
		put("oppo2", "oppo");
		put("vivo", "vivo");
		put("vivo2", "vivo");
		put("xiaomi", "xiaomi");
		put("xiaomiml", "xiaomi");
		put("xmyy", "xiaomi");
		put("xiaomiml2", "xiaomi");
		put("huawei", "huawei");
	}};
	public static Map<String, String> CHA_CTR_MAP = new HashMap(){{
		put("oppo", "45");
		put("oppo2", "45");
		put("vivo", "25");
		put("vivo2", "25");
		put("xiaomi", "20");
		put("xiaomiml", "20");
		put("xmyy", "20");
		put("xiaomiml2", "20");
	}};
	@Override
	public boolean checkRedLineCtrAsConfig(String tdate,String cha_id) {

		try {
			String ctr_val = CHA_CTR_MAP.get(cha_id);

			/**
			 * 1、筛选超出红线指标的配置，例如ctr大于45
			 * 2、增加广告位及x3x4配置的注水配置 (广告策略提前配置好，不同广告源类型使用固定的策略)
			 * 3、再次检查，是否需要继续减少注水值和不处理
			 */
			Map<String, String> paramMap = new HashMap<>();
			paramMap.put("tdate", tdate);
			paramMap.put("cha_id", "'"+cha_id+"'");
			paramMap.put("ctr_val", (Integer.valueOf(ctr_val) * (1 + 0.05)) + ""); // 允许5%的向上浮动
			List<ExtendAdposVo> list = dnwxBiMapper.selectDnRedLineClickCtr(paramMap);
			logger.info("check配置:{}", JSON.toJSONString(list));

			// 进行飞书告警，以下配置的广告位仍然ctr超出红线
			if (!CollectionUtils.isEmpty(list)) {
				// 仅显示上午查询出的红线CTR
				List<String> ctrList = adv2Mapper.selectDnRedlineCtrRecordsList(tdate);

				List<JSONObject> collect = list.stream()
						.filter(act -> ctrList.contains(act.getPrjid()+"#"+act.getUser_group()+"#"+act.getSdktype()))
						.map(act -> JSONObject.parseObject(JSON.toJSONString(act))).collect(Collectors.toList());
				// 赋值appname
				Map<String, Map<String, Object>> appMap = adService.getAppInfoMap();
				// 匹配实时的广告位数据
				Map<String, ExtendAdposVo> clickAndDau = dnwxBiMapper.selectDnExtendAdposClickAndDau(paramMap);
				collect.forEach(act -> {
					act.put("tdate", act.getString("note"));

					Map<String, Object> map = appMap.get(act.getString("appid"));
					if(map != null) {
						act.put("appname", map.get("app_name").toString());
					}

					// CONCAT(tdate,appid,cha_id,prjid,sdktype) mapkey
					String key = act.getString("tdate")+act.getString("appid")+act.getString("cha_id")+act.getString("prjid")+act.getString("sdktype");
					ExtendAdposVo dau = clickAndDau.get(key);
					if(dau != null) {
						act.put("show_num", dau.getShow_num());
						act.put("click_num", dau.getClick_num());
						act.put("dau", dau.getDau());
						act.put("ctr", dau.getCtr());
					}
				});

				/* 记录入库 */
				adv2Mapper.insertBatchDnRedlineCtrRecords(list);

				List<String> headerList =
						Arrays.asList("tdate,日期", "appid,应用ID", "appname,应用名称", "cha_id,子渠道", "prjid,项目ID", "user_group,用户群", "adpos_type,广告位类型", "sdktype,SDK广告源类型", "real_ctr,实际CTR"
								, "show_num,展示数", "click_num,点击数", "dau,项目id活跃用户数", "ctr,注水位置CTR");
				String title = "检查红线CTR结果_" + tdate;
				JSONObject result = createAndUpdateFeishuSheet(title, headerList, collect);
				System.out.println("send feishu result = " + result.toJSONString());

				return true;
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.error("checkRedLineCtrAsConfig 异常：", e);
			// 告警
			StringBuilder message = new StringBuilder();
			message.append("报错任务：").append("checkRedLineCtrAsConfig 检查红线CTR结果").append("\n")
					.append("报错信息：").append(e.getMessage()).append("\n");
			HaiwaiAdTask.sendFailMsg(message.toString());
		}

		return false;
	}
	@Override
	public boolean syncRedLineCtrAsConfig(String tdate,String cha_id) {
		/**
		 * 1、筛选超出红线指标的配置，例如ctr大于45
		 * 2、增加广告位及x3x4配置的注水配置 (广告策略提前配置好，不同广告源类型使用固定的策略)
		 * 3、再次检查，是否需要继续减少注水值和不处理
		 */
		Map<String, String> paramMap = new HashMap<>();
		paramMap.put("tdate", tdate);
		paramMap.put("cha_id", "'"+cha_id+"'");
		paramMap.put("ctr_val", CHA_CTR_MAP.get(cha_id));
		List<ExtendAdposVo> list = dnwxBiMapper.selectDnRedLineClickCtr(paramMap);
		logger.info("redLineClick配置:{}", JSON.toJSONString(list));

		/**
		 *条件：
		 * 1.异常点击（simuclick=1/2/3）的占比为0的产品 （来源：大数据中的ad_xxx_click的事件）
		 * 2.前一天消耗为0的产品 (产品+子渠道维度)
		 * 3.大版本不足76a3998687dd87cf以上的产品
		 * 满足以上任一条件，则判断该产品不需要添加注水
		 */
		List<String> simuClickData = dnwxBiMapper.selectSimuClickData(tdate);
		List<String> reportSpendData = dnwxBiMapper.selectReportSpendData(tdate);
		List<String> localverData = dnwxBiMapper.selectSdkLocalverData(tdate);
		logger.info("simuClickData:{}", JSON.toJSONString(simuClickData));
		logger.info("reportSpendData:{}", JSON.toJSONString(reportSpendData));
		logger.info("localverData:{}", JSON.toJSONString(localverData));
		list = list.stream().filter(act -> {
			String key = act.getAppid() + "#" + act.getCha_id();
			String key2 = act.getAppid() + "#" + act.getCha_id()+ "#" + act.getPrjid();
			// 1.异常点击（simuclick=1/2/3）的占比为0的产品
			if(simuClickData.contains(key)) {
				return false;
			}

			// 2.前一天消耗为0的产品
			if(reportSpendData.contains(key)) {
				return false;
			}

			// 3.大版本不足76a3998687dd87cf以上的产品
			if(localverData.contains(key2)) {
				return false;
			}

			return true;

		}).collect(Collectors.toList());

		logger.info("redLineClick过滤后配置:{}", JSON.toJSONString(list));


		// 记录红线指标的配置
		if(!CollectionUtils.isEmpty(list)) {
			try {

				// 发送飞书通知，以下配置的广告位仍然ctr超出红线
				List<JSONObject> collect = list.stream().map(act -> JSONObject.parseObject(JSON.toJSONString(act))).collect(Collectors.toList());
				// 赋值appname
				Map<String, Map<String, Object>> appMap = adService.getAppInfoMap();
				// 匹配实时的广告位数据
				Map<String, ExtendAdposVo> clickAndDau = dnwxBiMapper.selectDnExtendAdposClickAndDau(paramMap);
				collect.forEach(act -> {
					act.put("tdate", act.getString("note"));

					Map<String, Object> map = appMap.get(act.getString("appid"));
					if (map != null) {
						act.put("appname", map.get("app_name").toString());
					}

					// CONCAT(tdate,appid,cha_id,prjid,sdktype) mapkey
					String key = act.getString("tdate")+act.getString("appid")+act.getString("cha_id")+act.getString("prjid")+act.getString("sdktype");
					ExtendAdposVo dau = clickAndDau.get(key);
					if(dau != null) {
						act.put("show_num", dau.getShow_num());
						act.put("click_num", dau.getClick_num());
						act.put("dau", dau.getDau());
						act.put("ctr", dau.getCtr());
					}
				});

				/* 记录入库 */
				adv2Mapper.insertBatchDnRedlineCtrRecords(list);

				List<String> headerList =
						Arrays.asList("tdate,日期", "appid,应用ID", "appname,应用名称", "cha_id,子渠道", "prjid,项目ID", "user_group,用户群", "adpos_type,广告位类型", "sdktype,SDK广告源类型", "real_ctr,实际CTR"
								, "show_num,展示数", "click_num,点击数", "dau,项目id活跃用户数", "ctr,注水位置CTR");

				String title = "注水红线CTR配置_" + tdate;
				JSONObject result = createAndUpdateFeishuSheet(title, headerList, collect);
				System.out.println("send feishu result = " + result.toJSONString());
			} catch (Exception e) {
				e.printStackTrace();
			}
		}else{
			logger.info("没有需要注水的配置");
			// 发送提示
			StringBuilder message = new StringBuilder();
			message.append("\\n").append("注水红线CTR配置_" + tdate)
					.append("\\n").append("信息：").append("没有匹配到需要注水的配置");

			String msg = org.apache.commons.codec.binary.Base64.encodeBase64String(message.toString().getBytes());

			Map<String,String> paramsMap = new HashMap<>();
			paramsMap.put("msg",msg);
			paramsMap.put("userName","hehp");
			paramsMap.put("chatId",REDLINE_CTR_CHAT_ID);
			paramsMap.put("robot","robot5");

			String result = HttpRequest.httpPost(FS_MSG_GROUP, paramsMap, new HashMap<>());
			System.out.println("null send feishu result = " + result);
			return true;
		}

		// 查询现有注水广告位的配置情况，区分处理已有值和新增值
		Map<String, Map<String,Object>> adposMap = adv2Mapper.queryExistingAdposMap(null);
		logger.info("adposMap配置:{}", JSON.toJSONString(adposMap));

		// 增加广告位
		String temp_param = "{\"is_refresh\":1,\"refresh_time\":%s,\"refresh_type\":0,\"autoShow\":1,\"autoShow_time\":%s,\"rates\":[10],\"list_pos\":[{\"win_x\":200,\"win_y\":200}],\"list_rate\":[10]}";
		List<ExtendAdposVo> adposCollect = list.stream()
		.map(act -> {
			// 构建广告位配置的基本信息

			// time_ad997，给sdk源类型是yuans用；
			// time_ad998，给sdk源类型是plaque用；
			// time_ad999，给sdk源类型是msg用；
			String postion_name = act.getSdktype().equals("yuans")?"time_ad997":(act.getSdktype().equals("plaque")?"time_ad998":"time_ad999");
			String custom_param = String.format(temp_param, 60, 30);

			act.setAdpos(postion_name);
			String strategy = String.format("plaque_waterfall_%s_zs_%s", act.getSdktype(), CHA_AGENT_MAP.get(act.getCha_id()));
			act.setStrategy(strategy);
			// 配置使用默认值
			act.setAdstyle("");
			act.setShowrate(100);
			act.setXdelay("0");
			act.setAutoInterval("0");
			act.setOut("0");
			act.setStatu("1");
			act.setCuser("api");
			act.setCustom_param(custom_param);

			return act;
		})
		.map(act -> {
			String mapkey = act.getAppid() +"#"+ act.getCha_id() +"#"+ act.getPrjid() +"#"+ act.getUser_group() +"#"+ act.getAdpos();
			Map<String, Object> adpos = adposMap.get(mapkey);
			if(adpos == null){
				// 可能是子渠道维度配置的情况
				mapkey = act.getAppid() +"#"+ act.getCha_id() +"#"+ act.getUser_group() +"#"+ act.getAdpos();
				adpos = adposMap.get(mapkey);
			}

			// 匹配到已存在则进行减值
			if (adpos != null) {
				// 优先改autoShow_time，最低改到15s 改到autoShow_time=15还是CTR超出时，调整refresh_time，每次降低15s、最低改动至15s
				String refresh_time = BlankUtils.isNumeric(adpos.get("refresh_time").toString())?adpos.get("refresh_time").toString():"60";
				String autoShow_time = BlankUtils.isNumeric(adpos.get("autoShow_time").toString())?adpos.get("autoShow_time").toString():"30";
				if (Integer.parseInt(autoShow_time) > 15) {
					autoShow_time = String.valueOf(Integer.parseInt(autoShow_time) - 15);
				}else{
					if (Integer.parseInt(refresh_time) > 15) {
						refresh_time = String.valueOf(Integer.parseInt(refresh_time) - 15);
					}
				}

				// 重新设置
				act.setCustom_param(String.format(temp_param, refresh_time, autoShow_time));
				return act;
			}
			// 未匹配到则直接保留
			return act;
		})
		.collect(Collectors.toList());

		logger.info("待写入adpos:{}", JSON.toJSONString(adposCollect));
		try {
			// 先查询后插入或更新
			Map<String, Map<String,Object>> existingAdposMap = adv2Mapper.queryExistingAdposMap(adposCollect);

			// 记录已执行的子渠道集合
			Set<String> updatedMapkeySet = new TreeSet<>();

			// 分离需要更新和需要插入的记录
			List<ExtendAdposVo> toInsert = new ArrayList<>();
			List<ExtendAdposVo> toUpdate = new ArrayList<>();
			
			for (ExtendAdposVo adpos : adposCollect) {
				logger.info("adposVo内容:{}", JSON.toJSONString(adpos));

				String mapkey = adpos.getAppid()+"#"+adpos.getCha_id()+"#"+adpos.getPrjid()+"#"+adpos.getUser_group()+"#"+adpos.getAdpos();
				String mapkey2 = adpos.getAppid()+"#"+adpos.getCha_id()+"#"+adpos.getUser_group()+"#"+adpos.getAdpos();
				String mapkey3 = adpos.getAppid()+"#"+adpos.getCha_id()+"#"+adpos.getPrjid()+"#"+adpos.getUser_group();
				String mapkey4 = adpos.getAppid()+"#"+adpos.getCha_id()+"#"+adpos.getUser_group();
				// 如果存在相同维度的配置，则更新，按照项目ID维度修改
				if (existingAdposMap.containsKey(mapkey) && !updatedMapkeySet.contains(mapkey)) {
					updatedMapkeySet.add(mapkey); // 记录已执行

					adpos.setId(existingAdposMap.get(mapkey).get("id").toString()); // 设置ID用于更新
					toUpdate.add(adpos);
				}else if(existingAdposMap.containsKey(mapkey2) && !updatedMapkeySet.contains(mapkey2)){
					updatedMapkeySet.add(mapkey2); // 记录已执行

					// 如果存在相同维度的配置，则更新，按照子渠道维度修改
					adpos.setId(existingAdposMap.get(mapkey2).get("id").toString()); // 设置ID用于更新
					toUpdate.add(adpos);
				}else if(existingAdposMap.containsKey(mapkey3) && !updatedMapkeySet.contains(mapkey)){
					updatedMapkeySet.add(mapkey); // 按照新的adpos写入，所以使用mapkey1

					// 注水广告位不存在，按照项目ID维度新增
					toInsert.add(adpos);
				}else if(existingAdposMap.containsKey(mapkey4) && !updatedMapkeySet.contains(mapkey2)){
					updatedMapkeySet.add(mapkey2); // 按照新的adpos写入，所以使用mapkey2

					// 注水广告位不存在，按照子渠道维度新增
					adpos.setPrjid(null);
					toInsert.add(adpos);
				}

				// 单独打印updatedMapkeySet最新加入的一个元素
				if (updatedMapkeySet.size() > 0) {
					String lastUpdatedMapkey = updatedMapkeySet.stream().reduce((first, second) -> first).orElse(null);
					logger.info("最新加入的匹配元素:{}", lastUpdatedMapkey);
				}

			}

			logger.info("需要插入的配置:{}", JSON.toJSONString(toInsert));
			logger.info("需要更新的配置:{}", JSON.toJSONString(toUpdate));
			// 执行插入和更新操作
			if (!toInsert.isEmpty()) {
				adv2Mapper.insertBatchAdpos(toInsert);
				adv2Mapper.insertBatchAdposRecords(toInsert);
			}
			if (!toUpdate.isEmpty()) {
				toUpdate.forEach(act -> act.setCuser("api_update"));
				adv2Mapper.updateBatchAdpos(toUpdate);
				adv2Mapper.insertBatchAdposRecords(toUpdate);
			}
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}


		// 增加广告配置
		try {
			// 先查询后插入或更新
			List<ExtendAdposVo> adconfigList = adv2Mapper.queryExistingAdconfigList(adposCollect);
			Map<String, List<ExtendAdposVo>> existingAdconfigMap = adconfigList.stream()
					.collect(Collectors.groupingBy(ExtendAdposVo::getConfig));

			for (ExtendAdposVo adposVo : adposCollect) {
				logger.info("adconfigVo内容:{}", JSON.toJSONString(adposVo));

				String[] mapkeys = {
						adposVo.getAppid()+"#"+adposVo.getCha_id()+"#"+adposVo.getPrjid()+"#"+adposVo.getUser_group()+"#"+adposVo.getSdktype(),
						adposVo.getAppid()+"#"+adposVo.getPrjid()+"#"+adposVo.getUser_group()+"#"+adposVo.getSdktype(),
						adposVo.getAppid()+"#"+adposVo.getCha_id()+"#"+adposVo.getUser_group()+"#"+adposVo.getSdktype(),
						adposVo.getAppid()+"#"+adposVo.getUser_group()+"#"+adposVo.getSdktype()
				};
				// 如果存在相同维度的配置，则更新
				// 如果 产品+子渠道+项目id 不存在，则依次匹配 产品+子渠道、产品  维度的配置来进行操作
				if (existingAdconfigMap.containsKey(mapkeys[0]) || existingAdconfigMap.containsKey(mapkeys[1])
						|| existingAdconfigMap.containsKey(mapkeys[2]) || existingAdconfigMap.containsKey(mapkeys[3])) {

					for (int i = 0; i < mapkeys.length; i++) {

						List<ExtendAdposVo> existingAdconfig = existingAdconfigMap.get(mapkeys[i]);
						if (!CollectionUtils.isEmpty(existingAdconfig)) {
							System.out.println(String.format("\tmapkey匹配:%s", mapkeys[i]));

							System.out.println(String.format("\t待修改内容:策略=%s,内容=%s", adposVo.getStrategy(), JSON.toJSONString(existingAdconfig)));

							ExtendAdposVo info = new ExtendAdposVo();
							String ids = existingAdconfig.stream().map(ExtendAdposVo::getId).collect(Collectors.joining(","));
							info.setId(ids);
							info.setStrategy(adposVo.getStrategy());
							adv2Mapper.insertCopyExistingAdconfig(info);
							break;
						}
					}

				}
			}

		}catch (Exception e){
			e.printStackTrace();
			return false;
		}


		// 增加x3x4配置
		try {
			// 构建x配置的基本信息
			// 按照appid、channel、prjid进行去重后转为List
			String temp_content = "{\"postion_name\":\"%s\",\"show_rate\":100,\"x_type_rate\":100,\"delay_close_b\":0,\"delay_close_t\":0}";
			List<DnwxX3X4ConfigManageV2Dto> xconfigCollect = list.stream().map(act -> {
				// time_ad997，给sdk源类型是yuans用；
				// time_ad998，给sdk源类型是plaque用；
				// time_ad999，给sdk源类型是msg用；
				String postion_name = act.getSdktype().equals("yuans")?"time_ad997":(act.getSdktype().equals("plaque")?"time_ad998":"time_ad999");

				String x3_content = String.format(temp_content, postion_name);
				String x4_content = String.format(temp_content, postion_name);

				DnwxX3X4ConfigManageV2Dto add_dto = new DnwxX3X4ConfigManageV2Dto(act.getAppid(), act.getCha_id(), act.getPrjid(), 1,
						x3_content,
						x4_content,
						null,
						null,
						"api",
						DateTime.now().toString("yyyy-MM-dd HH:mm:ss"), 1);

				return add_dto;
			}).collect(Collectors.collectingAndThen(
				Collectors.toMap(
					dto -> dto.getAppid() + "#" + dto.getChannel() + "#" + dto.getPrjid(),
					Function.identity(),
					(existing, replacement) -> existing
				),
				map -> new ArrayList<>(map.values())
			));

			logger.info("写入x3x4:{}", JSON.toJSONString(xconfigCollect));

			// 先查询后插入或更新
			Map<String, DnwxX3X4ConfigManageV2Dto> existingXconfigMap = adv2Mapper.queryExistingXconfigMap(list);

			// 分离需要更新和需要插入的记录
			List<DnwxX3X4ConfigManageV2Dto> toInsert = new ArrayList<>();
			List<DnwxX3X4ConfigManageV2Dto> toUpdate = new ArrayList<>();
			String time_ad997 = String.format(temp_content, "time_ad997");
			String time_ad998 = String.format(temp_content, "time_ad998");
			String time_ad999 = String.format(temp_content, "time_ad999");
			List<JSONObject> postions = Arrays.asList(
					JSONObject.parseObject(time_ad997, com.alibaba.fastjson.parser.Feature.OrderedField),
					JSONObject.parseObject(time_ad998, com.alibaba.fastjson.parser.Feature.OrderedField),
					JSONObject.parseObject(time_ad999, com.alibaba.fastjson.parser.Feature.OrderedField)
			);

			for (DnwxX3X4ConfigManageV2Dto xconfig : xconfigCollect) {
				logger.info("xconfig开始匹配:{}", JSON.toJSONString(xconfig));
				String[] mapkeys = {
						xconfig.getPrjid(),
						xconfig.getAppid()+"#"+xconfig.getChannel()
				};
				// 如果存在相同维度的配置，则更新
				// 如果 产品+子渠道+项目id 不存在，则依次匹配 产品+子渠道、产品  维度的配置来进行操作
				if (existingXconfigMap.containsKey(mapkeys[0]) || existingXconfigMap.containsKey(mapkeys[1])) {

					DnwxX3X4ConfigManageV2Dto existingXconfig = null;

					for (int i = 0; i < mapkeys.length; i++) {
						existingXconfig = existingXconfigMap.get(mapkeys[i]);
						if(existingXconfig != null){
							logger.info("\tmapkey匹配:{}", mapkeys[i]);
							break;
						}
					}

					if(existingXconfig == null || !BlankUtils.isJSONObject(existingXconfig.getX3_content())){
						// X3_content内容不是有效的 JSON，则不处理
						continue;
					}
					if(existingXconfig.getX3_content() != null && existingXconfig.getX3_content().contains("time_ad999")){
						// 已加过time_ad999广告位，则不再处理
						continue;
					}


					logger.info("X3_content修改前:{}", existingXconfig.getX3_content());

					// 修改内容时保障jsonObject的key顺序
					// 匹配default_datas数组中ad_type为plaque的对象，进行修改
					// 需要在原有的基础上进行追加postion_name，默认增加三个位
					JSONObject x3JSONObject = JSON.parseObject(existingXconfig.getX3_content(), com.alibaba.fastjson.parser.Feature.OrderedField);
					x3JSONObject.getJSONArray("default_data").forEach(obj -> {
						JSONObject defaultData = (JSONObject) obj;
						if("plaque".equals(defaultData.getString("ad_type"))){
							defaultData.put("plaque_simu", 1);
							defaultData.put("yuans_simu", 1);
							defaultData.put("msg_simu", 1);
						}
					});
					
					x3JSONObject.getJSONArray("postionData").addAll(postions);
					String newContent = JSON.toJSONString(x3JSONObject, SerializerFeature.DisableCircularReferenceDetect);

					// 重新设置
					existingXconfig.setX3_content(newContent);
					existingXconfig.setCreate_user("api");
					existingXconfig.setCreate_time(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
					logger.info("X3_content修改后:{}", existingXconfig.getX3_content());

					toUpdate.add(existingXconfig);
				}else{
					// 如果不存在相同维度的配置，则插入
					// 需要完整构建一个新的对象，-暂不需要
					// toInsert.add(xconfig);
				}
			}

			logger.info("xconfig需要更新的配置:{}", JSON.toJSONString(toUpdate));
			if (!toUpdate.isEmpty()) {
				wxGameAppTypeManageService.addXconfig(toUpdate);
			}
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}

		return true;
	}
	@Override
	public boolean insertRedLineCtrAsAdpos(String mapid, String appid) {
		return false;
	}

	@Override
	public boolean insertRedLineCtrAsXconfig(String mapid, String appid) {
		return false;
	}


	private Map<String, List<ExtendAdposVo>> adposConfigMap = new HashMap<>(16);
	private Long updateTime = 0L;

	private List<ExtendAdposVo> selectDefaultConfig(String chaId) {
		List<ExtendAdposVo> extendAdposVos;
		if (System.currentTimeMillis() - updateTime > 60 * 1000 || !adposConfigMap.containsKey(chaId)) {
			extendAdposVos = adv2Mapper.selectAdsidToAdposDefaultConfig(chaId);
			adposConfigMap.put(chaId, extendAdposVos);
		} else {
			extendAdposVos = adposConfigMap.get(chaId);
		}
		return extendAdposVos;
	}


	/**
	 * 创建飞书电子表格并写入红线CTR数据
	 * @param title 标题
	 * @param headerList 表头列表
	 * @param dataList 数据列表
	 */
	public JSONObject createAndUpdateFeishuSheet(String title, List<String> headerList, List<JSONObject> dataList) {
		JSONObject resultMap = new JSONObject();
		try {
			// 获取飞书 tenant_access_token，飞书excel机器人参数
			String FS_EXCEL_ROBOT_APPID = "cli_a3a83a34a2bc100c";
			String FS_EXCEL_ROBOT_SECRECT = "biItpF9AVvMI7ShSTkmpEb18RGTK8DmU";
			String token = FeishuUtils.getTenantAccessTokenTwo(FS_EXCEL_ROBOT_APPID, FS_EXCEL_ROBOT_SECRECT);

			// 创建电子表格
			Map<String, String> headers = new HashMap<>();
			headers.put("Authorization", "Bearer " + token);
			headers.put("Content-Type", "application/json; charset=utf-8");


			//根目录下创建文件夹 目录以日期YYYY-MM-dd
			String folderName = DateTime.now().toString("yyyy-MM-dd");
			String rootFolderToken = FeishuUtils.getRootFolderMetaToken(token);
			if (BlankUtils.checkBlank(rootFolderToken)) {
				resultMap.put("error","获取元空间文件夹失败");
				resultMap.put("code","0");
				return resultMap;
			}
			//判断是否需要创建新文件夹 存在直接使用 不存在创建
			String folderToken = "";
			JSONObject exitsFolderJson = FeishuUtils.exitsSameFolder(token, rootFolderToken, folderName);
			if (exitsFolderJson == null) {
				folderToken = FeishuUtils.createFolder(token, rootFolderToken, folderName);
				if (BlankUtils.checkBlank(folderToken)) {
					resultMap.put("error","创建文件目录失败");
					resultMap.put("code","0");
					return resultMap;
				}
			} else {
				folderToken = exitsFolderJson.getString("token");
			}

			// 设置表格标题
			JSONObject createParams = new JSONObject();
			createParams.put("title", title);
			createParams.put("folder_token", folderToken);


			// 创建表格
			String FS_SPREAD_SHEETS_CREATE_URL = "https://open.feishu.cn/open-apis/sheets/v3/spreadsheets";
			String createResult = HttpClientUtils.getInstance().httpPost(FS_SPREAD_SHEETS_CREATE_URL, createParams.toJSONString(), headers);
			System.out.println("createResult: " + createResult);
			JSONObject createJson = JSONObject.parseObject(createResult);

			if (createJson != null && createJson.getInteger("code") == 0) {

				// 获取表格ID
				String spreadsheetToken = createJson.getJSONObject("data").getJSONObject("spreadsheet").getString("spreadsheet_token");
				System.out.println("spreadsheetToken:"+spreadsheetToken);


				/* 授权文档的权限：群组和个人用户 */
				//授权对象类型-群组
				String member_type = "openchat";
				String member_id = REDLINE_CTR_CHAT_ID;
				String FS_EXCEL_PERMISSION = "full_access";
/*
				//授权对象类型-用户
				member_type = "userid";
				member_id = "caow";

				List<SysUfsVo> feishuUserList = userInfoMapper.selectSysFsInfo(new SysUfsVo());
				SysUfsVo feishuUserInfo = feishuUserList.stream().filter(t->t.getLogin_name().equals("caow")).findFirst().orElse(null);
				if (feishuUserInfo == null){
					resultMap.put("error","飞书用户id不存在");
					resultMap.put("code","0");
					return resultMap;
				}
				member_id = feishuUserInfo.getFsuser_id();
				System.out.println("member_id："+member_id);
*/

				boolean permSucc = FeishuUtils.permissions(token,spreadsheetToken,"sheet",false,member_type,member_id,FS_EXCEL_PERMISSION);
				System.out.println("授权结果："+permSucc);

				/* 获取 sheetId信息 */
				JSONObject sheetJson = FeishuUtils.getSpreadFirstSheetMetaInfo(token,spreadsheetToken);
				System.out.println("sheetJson："+sheetJson.toJSONString());
				String sheetId = sheetJson.getString("sheetId");


				// 写入数据
				boolean bool = writeDataToFeishuSpreadsheet(token, spreadsheetToken, sheetId, headerList, dataList);
				if(bool) {
					// 发送飞书通知
					Map<String, String> sendMap = new HashMap<>();

					StringBuilder message = new StringBuilder();
					message.append("\\n").append(String.format("【%s 已生成】",title))
							.append("\\n").append("文档地址：")
							.append(String.format("https://feishu.cn/sheets/%s",spreadsheetToken));

					String msg = org.apache.commons.codec.binary.Base64.encodeBase64String(message.toString().getBytes());
					sendMap.put("msg",msg);
					sendMap.put("userName","hehp");
					sendMap.put("chatId",REDLINE_CTR_CHAT_ID);
					sendMap.put("robot","robot3");
					HttpClientUtils.getInstance().httpPost(FS_MSG_GROUP, sendMap, new HashMap<>());

					resultMap.put("code", "1");
					resultMap.put("spreadsheetToken", spreadsheetToken);
					resultMap.put("message", "创建飞书表格成功");
				} else {
					resultMap.put("error", "写入数据失败");
					resultMap.put("code", "0");
					// 飞书告警
					String message = String.format("飞书表格数据写入失败，请检查！\n日期：%s\n文档地址：https://feishu.cn/sheets/%s", title, spreadsheetToken);
					HaiwaiAdTask.sendFailMsg(message);
				}

				return resultMap;
			}
		} catch (Exception e) {
			e.printStackTrace();
			String message = String.format("飞书表格数据写入失败，请检查！\n错误信息：%s", e.getMessage());
			HaiwaiAdTask.sendFailMsg(message);
		}
		return null;
	}

	/**
	 * Writes data and styles to a Feishu spreadsheet.
	 *
	 * @param token              The Feishu API token.
	 * @param spreadsheetToken   The spreadsheet token.
	 * @param sheetId            The sheet ID.
	 * @param headerList       The list of header parameters in the format "HeaderName,FieldName".
	 * @param dataList           The list of data to be written.
	 */
	public static boolean writeDataToFeishuSpreadsheet(String token, String spreadsheetToken, String sheetId, List<String> headerList, List<JSONObject> dataList) {
		Map<String, String> headerMap = new LinkedHashMap<>();
		JSONArray headDataArray = new JSONArray();
		JSONArray dataArray = new JSONArray();
		JSONArray headStyleArray = new JSONArray();

		// 设置表头的样式配置
		JSONObject headerStyleData = new JSONObject();
		JSONArray headerStyleRangesArray = new JSONArray();
		JSONObject headerStyle = JSONObject.parseObject("{\"font\":{\"bold\":true,\"italic\":false,\"fontSize\":\"10pt/1.5\",\"clean\":false},\"textDecoration\":0,\"formatter\":\"\",\"hAlign\":1,\"vAlign\":1,\"foreColor\":\"#000000\",\"backColor\":\"#33CCCC\",\"borderType\":\"FULL_BORDER\",\"borderColor\":\"#000000\",\"clean\":false}");

		// 解析保存表头数据
		JSONObject headerData = new JSONObject();
		List<String> headersValue = new ArrayList<>();
		try {
			for (String param : headerList) {
				String[] s = param.split(",");
				headersValue.add(s[1]);
				headerMap.put(s[0], s[1]);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		JSONArray headerArray = new JSONArray();
		headerArray.add(headersValue);

		// 写入表头数据
		String headRightRangeLetter = StringUtils.numberToLetter(headersValue.size());
		String headRange = sheetId + "!" + "A1:" + headRightRangeLetter;

		headerData.put("range", headRange);
		headerData.put("values", headerArray);
		headDataArray.add(headerData);
		boolean headInsertSucc = FeishuUtils.batchInsertSpreadsheetData(token, spreadsheetToken, headDataArray);
		System.out.println("headInsertSucc==" + headInsertSucc);

		// 定义表头样式
		headerStyleRangesArray.add(headRange + "1");
		headerStyleData.put("ranges", headerStyleRangesArray);
		headerStyleData.put("style", headerStyle);
		headStyleArray.add(headerStyleData);

		boolean headStyleSucc = FeishuUtils.batchHandleStyle(token, spreadsheetToken, headStyleArray);
		System.out.println("headStyleSucc==" + headStyleSucc);

		// 组装数据行，必须从第二行开始
		JSONObject dataData = new JSONObject();
		JSONArray dataDataArray = FeishuUtils.assembleFeishuExcelData(dataList, headerMap);

		// 每2000条写入一次，防止飞书 Excel 出现 "request data too large" 错误
		if (dataDataArray.size() <= 2000) {
			String dataLetter = StringUtils.numberToLetter(headerMap.size());
			int dataSize = dataList.size() + 1;
			String dataRange = sheetId + "!" + "A2:" + dataLetter + dataSize;
			dataData.put("range", dataRange);
			dataData.put("values", dataDataArray);
			dataArray.add(dataData);
			boolean insertSucc = FeishuUtils.batchInsertSpreadsheetData(token, spreadsheetToken, dataArray);
			System.out.println("one insertSucc==" + insertSucc);
			return insertSucc;
		} else {
			int pageSize = 1999;
			int pageNum = (dataDataArray.size() / pageSize) + 1;
			int column = 0;
			for (int i = 0; i < pageNum; i++) {
				int fromIndex = pageSize * i;
				int toIndex = Math.min(fromIndex + pageSize, dataDataArray.size());
				List<?> eachDataArray = dataDataArray.subList(fromIndex, toIndex);
				int row = (i == 0) ? 2 : column + 1;
				column = (i == 0) ? eachDataArray.size() + 1 : column + eachDataArray.size();

				String dataLetter = StringUtils.numberToLetter(headerMap.size());
				String dataRange = sheetId + "!" + "A" + row + ":" + dataLetter + column;
				System.out.println("dataRange:" + dataRange);
				dataData.put("range", dataRange);
				dataData.put("values", eachDataArray);
				dataArray.add(dataData);
				boolean insertSucc = FeishuUtils.batchInsertSpreadsheetData(token, spreadsheetToken, dataArray);
				System.out.println("two insertSucc==" + insertSucc);
				return insertSucc;
			}
		}
		return false;
	}

	@Override
	public String syncMjuheAdSlots(String userName, String sdk_code) {
		logger.info("开始同步Mjuhe_前缀广告源的代码位配置，操作用户：{}", userName);

		try {
			// 1. 查询广告源管理表中Mjuhe_前缀的配置列表
			String sql = "SELECT * FROM dn_extend_adsid_manage WHERE sdk_appid=5698908 AND adsid LIKE 'Mjuhe_%'";
			if(!BlankUtils.checkBlank(sdk_code))
				sql += " AND sdk_code = '" +sdk_code+ "'";
			List<ExtendAdsidVo> mjuheConfigs = queryListBean(sql, ExtendAdsidVo.class);
			String sql2 = "SELECT CONCAT(appid,platform) mapkey,tappid,taccountid FROM adv_adcode_account_info";
			Map<String, Map<String, Object>> accountMap = queryListMapOfKey(sql2);

			if (CollectionUtils.isEmpty(mjuheConfigs)) {
				logger.info("未找到Mjuhe_前缀的广告源配置");
				return ReturnJson.success("未找到Mjuhe_前缀的广告源配置");
			}

			logger.info("找到{}个Mjuhe_前缀的广告源配置", mjuheConfigs.size());

			int totalProcessed = 0;
			int totalCreated = 0;
			int totalSkipped = 0;

			// 2. 遍历配置列表
			for (ExtendAdsidVo config : mjuheConfigs) {
				String sdkCode = config.getSdk_code();
				if (BlankUtils.checkBlank(sdkCode)) {
					logger.warn("广告源{}的sdk_code为空，跳过处理", config.getAdsid());
					totalSkipped++;
					continue;
				}

				logger.info("处理广告源：{}，sdk_code：{}", config.getAdsid(), sdkCode);

				try {
					// 3. 调用queryCode获取代码位列表
					String adSlotResponse = queryAdSlotsByCode(sdkCode);
					if (BlankUtils.checkBlank(adSlotResponse)) {
						logger.warn("调用queryCode返回空结果，sdk_code：{}", sdkCode);
						totalSkipped++;
						continue;
					}

					// 4. 解析响应并处理代码位
					int created = processAdSlotResponse(adSlotResponse, config, userName, accountMap);
					totalCreated += created;
					totalProcessed++;

					// 避免请求过于频繁
					Thread.sleep(1000);

				} catch (Exception e) {
					logger.error("处理广告源{}时发生异常：{}", config.getAdsid(), e.getMessage(), e);
					totalSkipped++;
				}
			}

			String resultMsg = String.format("同步完成！处理%d个配置，跳过%d个配置，创建%d个新代码位",
					totalProcessed, totalSkipped, totalCreated);
			logger.info(resultMsg);

			return ReturnJson.success(resultMsg);

		} catch (Exception e) {
			logger.error("同步Mjuhe广告源代码位配置时发生异常：{}", e.getMessage(), e);
			return ReturnJson.toErrorJson("同步失败：" + e.getMessage());
		}
	}

	/**
	 * 调用queryCode获取广告位代码列表
	 * @param sdkCode 广告位id
	 * @return API响应字符串
	 */
	private String queryAdSlotsByCode(String sdkCode) {
		try {
			// 使用固定的用户ID和角色ID调用queryCode
			Integer userId = 294224;
			Integer roleId = 294224;
			Integer adUnitId = Integer.parseInt(sdkCode);

			Map<String, String> params = new HashMap<>();
			params.put("ad_unit_id", adUnitId.toString());

			// 使用Utils.setSign设置签名
			Map<String, String> paramsMap = com.wbgame.utils.monkjay.Utils.setSign(userId, roleId, params);

			// 构建请求URL
			String paramsStr = paramsMap.entrySet().stream()
					.map(entry -> entry.getKey() + "=" + entry.getValue())
					.collect(Collectors.joining("&"));

			String url = com.wbgame.utils.monkjay.Utils.MEDIATION_URL + "waterfall_detail?" + paramsStr;
			logger.info("调用queryCode API，URL：{}", url);

			// 发起HTTP GET请求
			String response = HttpClientUtils.getInstance().httpGet(url);
			logger.info("queryCode API响应：{}", response);

			return response;

		} catch (Exception e) {
			logger.error("调用queryCode API失败，sdk_code：{}，错误：{}", sdkCode, e.getMessage(), e);
			return null;
		}
	}

	/**
	 * 处理API响应，解析ad_slot_list并创建新的广告源配置
	 * @param response API响应字符串
	 * @param originalConfig 原始配置
	 * @param userName 操作用户名
	 * @return 创建的新配置数量
	 */
	private int processAdSlotResponse(String response, ExtendAdsidVo originalConfig, String userName, Map<String, Map<String, Object>> accountMap) {
		try {
			if (!BlankUtils.isJSONObject(response)) {
				logger.warn("API响应不是有效的JSON格式");
				return 0;
			}

			JSONObject jsonResponse = JSONObject.parseObject(response);
			String code = jsonResponse.getString("code");

			// 判断code=100
			if (!"100".equals(code)) {
				logger.warn("API返回错误码：{}，message：{}", code, jsonResponse.getString("message"));
				return 0;
			}

			JSONObject data = jsonResponse.getJSONObject("data");
			if (data == null) {
				logger.warn("API响应中data字段为空");
				return 0;
			}

			JSONArray adSlotList = data.getJSONArray("ad_slot_list");
			if (adSlotList == null || adSlotList.isEmpty()) {
				logger.warn("ad_slot_list为空或不存在");
				return 0;
			}

			logger.info("找到{}个代码位", adSlotList.size());

			int createdCount = 0;

			// 遍历ad_slot_list数组
			for (int i = 0; i < adSlotList.size(); i++) {
				JSONObject adSlot = adSlotList.getJSONObject(i);

				String adSlotId = adSlot.getString("ad_slot_id");
				String adSlotName = adSlot.getString("ad_slot_name");
				Integer network = adSlot.getInteger("network");
				Integer biddingType = adSlot.getInteger("bidding_type");

				if (BlankUtils.checkBlank(adSlotId)) {
					logger.warn("代码位ad_slot_id为空，跳过");
					continue;
				}

				logger.info("代码位名：{}, 内容：{}", adSlotName, adSlot.toJSONString());
				// 检查代码位是否已存在
				if (isAdSlotExists(adSlotId)) {
					logger.info("代码位：{}，已存在 跳过创建", adSlotId);
					continue;
				}else{
					logger.info("代码位：{}, 可以执行创建", adSlotId, adSlot.toJSONString());
				}

				// 创建新的广告源配置
				if (createNewAdSlotConfig(adSlot, originalConfig, userName, accountMap)) {
					createdCount++;
					logger.info("成功创建代码位配置：{}", adSlotId);
				} else {
					logger.warn("创建代码位配置失败：{}", adSlotId);
				}
			}

			return createdCount;

		} catch (Exception e) {
			logger.error("处理API响应时发生异常：{}", e.getMessage(), e);
			return 0;
		}
	}

	/**
	 * 检查代码位是否已存在
	 * @param sdkCode 代码位ID
	 * @return true-已存在，false-不存在
	 */
	private boolean isAdSlotExists(String sdkCode) {
		try {
			String sql = "SELECT COUNT(1) FROM dn_extend_adsid_manage WHERE sdk_code = '" + sdkCode + "'";
			List<String> result = queryListString(sql);
			return result != null && !result.isEmpty() && Integer.parseInt(result.get(0)) > 0;
		} catch (Exception e) {
			logger.error("检查代码位是否存在时发生异常：{}", e.getMessage(), e);
			return false;
		}
	}

	/**
	 * 创建新的广告源配置
	 * @param adSlot 代码位信息
	 * @param originalConfig 原始配置
	 * @param userName 操作用户名
	 * @return true-创建成功，false-创建失败
	 */
	private boolean createNewAdSlotConfig(JSONObject adSlot, ExtendAdsidVo originalConfig, String userName,Map<String, Map<String, Object>> accountMap) {
		try {
			String adSlotId = adSlot.getString("ad_slot_id");
			String adSlotName = adSlot.getString("ad_slot_name");
			Integer network = adSlot.getInteger("network");
			String biddingType = adSlot.getString("bidding_type");

			// 设置新的代码位信息
			originalConfig.setSdk_code(adSlotId);

			// 根据network匹配平台参数
			String agent = mapNetworkToAgent(network);
			originalConfig.setAgent(agent);
			if(9 == network || 10 == network){
				originalConfig.setNote("network="+network);
			}

			// 根据bidding_type设置bidding字段
			originalConfig.setBidding(biddingType);

			// 生成新的adsid：沿用原有格式，但替换代码位部分
			String originalAdsid = originalConfig.getAdsid();
			String newAdsid = originalConfig.getAgent()+"_"+originalConfig.getAdpos_type()+"_"+originalConfig.getAppid()+"_"+originalConfig.getSdk_code();
			originalConfig.setAdsid(newAdsid);

			originalConfig.setCuser(userName);
			// 匹配设置产品+平台对应的tappid
			Map<String, Object> account = accountMap.get(originalConfig.getAppid() + originalConfig.getAgent());
			if(account != null){
				originalConfig.setSdk_appid(account.get("tappid").toString());
			}

			// 插入数据库
			int result = adv2Mapper.insertDnExtendAdsidManage(originalConfig);
			return result > 0;

		} catch (Exception e) {
			logger.error("创建新广告源配置时发生异常：{}", e.getMessage(), e);
			return false;
		}
	}

	/**
	 * 根据network值映射到平台参数
	 * @param network network值
	 * @return 平台名称
	 */
	public String mapNetworkToAgent(Integer network) {
		if (network == null) {
			return "unknown";
		}

		// 根据穿山甲聚合管理的network值映射
		switch (network) {

			case 1: return "headline"; // 穿山甲
			case 2: return "admob"; // admob
			case 3: return "GDT"; // 优量汇
			case 4: return "Mobvista"; // mintegral
			case 5: return "Baidu"; // 百度
			case 6: return "unity"; // unity
			case 7: return "kuaishou"; // 快手
			case 8: return "sigmob"; // sigmob
			case 9: return "gromore"; // 游可赢
			case 10: return "gm-adx"; // GM ADX
			default: return "network-" + network;
		}
	}



}
