package com.wbgame.service.adv2;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.pojo.NpPostVo;
import com.wbgame.pojo.adv2.*;

import java.util.List;
import java.util.Map;

public interface Adv2Service {
	
	
	// 通用执行语句和查询语句
	public int execSql(String sql); // 直接执行DML sql语句
	public List<String> queryListString(String sql);
	public List<Map<String, Object>> queryListMap(String sql);
	public List<Map<String, String>> queryListMapOne(String sql);
	public List<Map<String, Object>> queryListMapTwo(String sql, Object obj);
	public Map<String, Map<String, Object>> queryListMapOfKey(String sql);
	public <T> List<T> queryListBean(String sql, Class<T> clazz);
	public int execSqlHandle(String sql, Object obj);
	public List<NpPostVo> queryNpPost(String sql);
	public int batchExecSql(Map<String, Object> paramMap);
	public int batchExecSqlTwo(Map<String, Object> paramMap);
	
	
	/** 聚合数据查询 */
	public List<Map<String,Object>> selectDnGroupData(Map<String, Object> paramMap);
	/** 聚合数据查询汇总 */
	public Map<String,Object> selectDnGroupDataSum(Map<String, Object> paramMap);
	
	/** 聚合综合查询 */
	public List<Map<String,Object>> selectDnGroupCom(Map<String, Object> paramMap);
	/** 聚合综合查询汇总 */
	public Map<String,Object> selectDnGroupComSum(Map<String, Object> paramMap);
	
	/** 变现数据汇总报表 */
	public List<Map<String,Object>> selectExtendRevenueReport(Map<String, Object> paramMap);
	/** 实时数据监控 */
	public List<DnExtendGroupMonitorVo> selectExtendGroupMonitor(Map<String, Object> paramMap);
	
	/** 变现-广告位数据 */
	public List<Map<String,Object>> selectAdposData(Map<String, Object> paramMap);
	public Map<String,Object> selectAdposDataSum(Map<String, Object> paramMap);
	
	/** 变现预估收入 */
	public List<Map<String, Object>> selectDnExIncomeData(Map<String, Object> paramMap);
	public Map<String, Object> selectDnExIncomeDataSum(Map<String, Object> paramMap);
	/** 变现-数据gap统计 */
	public List<Map<String,Object>> selectDnShowGapTotal(Map<String, Object> paramMap);
	public Map<String,Object> selectDnShowGapTotalSum(Map<String, Object> paramMap);
	
	/** 变现-自推广收入分析 */
	public List<Map<String,Object>> selectSelfPromotionIncome(Map<String, Object> paramMap);
	/** 变现收入校准 */
	public List<Map<String,Object>> selectExtendIncomeRevise(Map<String, Object> paramMap);
	public Map<String,Object> selectExtendIncomeReviseSum(Map<String, Object> paramMap);
	/** 汇总数据校准 */
	public List<Map<String,Object>> selectAdtypeTotalRevise(Map<String, Object> paramMap);
	public Map<String,Object> selectAdtypeTotalReviseSum(Map<String, Object> paramMap);
	/** 项目ID收入预估 */
	public List<Map<String,Object>> selectPrjidTotalIncome(Map<String, Object> paramMap);
	public Map<String,Object> selectPrjidTotalIncomeSum(Map<String, Object> paramMap);
	
	/** 二级子渠道展示收入 */
	public List<Map<String,Object>> selectSubchaTotal(Map<String, Object> paramMap);
	public Map<String,Object> selectSubchaTotalSum(Map<String, Object> paramMap);
	
	/** 同步快手底价数据到广告源 */
	public boolean syncKuaishouCpmFloor();
	
	/** 同步baidu收入数据 */
	public boolean syncBaiduCashTotal(String tdate);


	/** 广告源id获取配置*/
	public ExtendAdsidVo getDnAdSid(String adsid);


	/** 查询用户群配置*/
	List<UserStrategyV2Vo> getUserStrategyConfigList(Map map);

	/** 更新用户群配置*/
	int updateUserStrategyConfig(List<UserStrategyV2Vo> list,UserStrategyV2Vo config);

	/** 查询广告配置 */
	public List<Map<String, Object>> selectDnExtendAdconfig(ExtendAdconfigVo app);
	/** 查询广告源管理配置 */
	public List<Map<String, Object>> selectDnExtendAdsidManage(ExtendAdsidVo app);
	/** 查询广告位管理配置 */
	public List<Map<String, Object>> selectDnExtendAdposManage(ExtendAdposVo app);


	/** 更新广告位配置广告策略 */
	public String insertAdposFromAdconfig(Map<String, String> paramMap);

	/** 写入对应维度的广告位配置 */
	public String updateAdposFromAdconfig(Map<String, String> paramMap);

	/** SDK版本监控 */
	public List<Map<String,Object>> selectSdkRelationReport(Map<String, String> paramMap);



	/** 变现汇总表数据同步 */
	public boolean syncMonetizationReport(Map<String, String> paramMap);

	List<JSONObject> getUserStrategyConfigList(UserStrategyV2Vo app);

	/**
	 * 同步 广告源数据到广告位数据
	 *
	 * @param apps 传递的广告源数据，主要需要 appid cha_id cuser 三个字段
	 * @param userName 操作人
	 * @return
	 */
	String syncAdsidToAdpos(List<ExtendAdsidVo> apps, String userName);

	/**
	 * 发起广告协议缓存刷新
	 * @param mapid
	 * @param appid
	 * @return
	 */
	public boolean recacheDnExtendAd(String mapid,String appid);

	/**
	 * 写入红线的ctr配置-广告位
	 * @param tdate
	 * @param cha_id
	 * @return
	 */
	public boolean syncRedLineCtrAsConfig(String tdate,String cha_id);
	/**
	 * 检查红线的ctr的状态
	 * @param tdate
	 * @param cha_id
	 * @return
	 */
	public boolean checkRedLineCtrAsConfig(String tdate,String cha_id);
	/**
	 * 写入红线的ctr配置-广告位
	 * @param mapid
	 * @param appid
	 * @return
	 */
	public boolean insertRedLineCtrAsAdpos(String mapid,String appid);
	/**
	 * 写入红线的ctr配置-x3x4配置
	 * @param mapid
	 * @param appid
	 * @return
	 */
	public boolean insertRedLineCtrAsXconfig(String mapid,String appid);

	/**
	 * 同步Mjuhe_前缀广告源的代码位配置
	 * 查询广告源管理表中Mjuhe_前缀的配置，调用queryCode获取代码位列表，
	 * 根据返回的ad_slot_list创建新的广告源配置
	 * @param userName 操作用户名
	 * @return 操作结果JSON字符串
	 */
	public String syncMjuheAdSlots(String userName);

}
