package com.wbgame.service;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.operate.TokenManageVo;
import com.wbgame.utils.PageResult;

import jxl.read.biff.BiffException;

import java.io.IOException;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

/**
 * @author: xugx
 * @createDate: 2023/01/03 11:44
 * @class: TokenManageService
 * @description:
 */
public interface TokenManageService {

	Result<PageResult<TokenManageVo>> tokenList(Integer start, Integer limit, TokenManageVo vo);
	
    Result<Integer> deleteTokenById(List<Integer> id);
    Result<Integer> addToken(TokenManageVo vo);
    
    Result<Integer> updateToken(TokenManageVo vo);
    
    Result<Integer> bathImport(MultipartFile file,String userName) throws BiffException, IOException ;
}
