package com.wbgame.service.xyx.impl;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.common.ProtocolConstant;
import com.wbgame.mapper.adb.DnwxBiAdtMapper;
import com.wbgame.mapper.adv2.Ad2Mapper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.master.YdMapper;
import com.wbgame.service.xyx.XyxService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpClientUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URI;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class XyxServiceImpl implements XyxService {

    Logger logger = LoggerFactory.getLogger(XyxServiceImpl.class);

    private static final String tokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential";

    private static final String detailUrl = "https://api.weixin.qq.com/publisher/stat?action=publisher_adunit_general&access_token=";

    @Resource
    private Ad2Mapper ad2Mapper;

    @Resource
    private YdMapper ydMapper;

    @Resource
    private AdMapper adMapper;

    @Resource
    private DnwxBiAdtMapper dnwxBiAdtMapper;

    @Override
    public int synXyxToken(String startTime,String endTime) {
        //异常信息
        String msg = "小游戏同步数据失败,同步数据量为0";
        //转换日期yyyyMMdd为yyyy-MM-dd
        if (!startTime.contains("-")) {
            startTime = convertDate(startTime);
        }
        if (!endTime.contains("-")) {
            endTime = convertDate(endTime);
        }
        //查询所有小游戏的appkey
        List<Map<String,Object>> list = ad2Mapper.selectXyxAppkey();
        // 去除重复得sdk_appid
        List<Map<String,Object>> filteredAppList = new ArrayList<>(list.size());
        Set<String> set = new HashSet<>(list.size());
        list.forEach(map -> {
            if(!set.contains(map.get("sdk_appid").toString())) {
                filteredAppList.add(map);
                set.add(map.get("sdk_appid").toString());
            } else {
                logger.warn("find repeat sdk_appid: {}", map.get("sdk_appid"));
            }
        });

        //查询广告源匹配表
        Map<String, JSONObject> xyxAdsidReport = getXyxAdsidReport();
        //数据集合
        List<JSONObject> data = new ArrayList<>();
        //遍历已配置的小游戏
        for (Map<String,Object> m : filteredAppList) {
            //请求获取token
            try {
                String access_token = HttpClientUtils.getInstance().httpGet(ProtocolConstant.hb_new_redpack_url + "/wx/api/getAccessToken?appid=" + m.get("appid"));
                //未请求到token跳过
                if (BlankUtils.checkBlank(access_token)) {
                    msg = "变现明细表:小游戏同步失败,未找到token";
                    logger.error("未找到token|appid:"+m.get("appid"));
                    continue;
                }
                //查询广告数据
                int page_size = 90;
                int page = 1;
                //循环到下一页未查询到数据
                while (true) {
                    String param = access_token + "&start_date="+startTime+"&end_date="+endTime+"&page="+page+"&page_size="+page_size;
                    String d = HttpClientUtils.getInstance().httpGet(detailUrl + param);
                    //未查询到跳过
                    if (BlankUtils.checkBlank(d)) {
                        msg = "变现明细表:小游戏同步失败,获取token错误";
                        break;
                    }
                    //解析json
                    try {
                        JSONObject j = JSONObject.parseObject(d);
                        if (j.getJSONArray("list") == null || j.getJSONArray("list").size() == 0) {
                            break;
                        }
                        List<JSONObject> list1 = j.getJSONArray("list").toJavaList(JSONObject.class);
                        for (JSONObject l : list1) {
                            JSONObject ad = l.getJSONObject("stat_item") == null ? new JSONObject() : l.getJSONObject("stat_item");
                            ad.put("ad_unit_id",l.getString("ad_unit_id"));
                            ad.put("ad_unit_name",l.getString("ad_unit_name"));
                            ad.put("income",ad.getBigDecimal("income").divide(new BigDecimal("100"),2,BigDecimal.ROUND_FLOOR));
                            JSONObject jsonObject = xyxAdsidReport.get(l.getString("ad_unit_id"));
                            if (jsonObject != null) {
                                for (String key :jsonObject.keySet()) {
                                    ad.put(key,jsonObject.getString(key));
                                }
                                data.add(ad);
                            }
                        }
                        page++;
                    }catch (Exception e) {
                        msg = "变现明细表:小游戏同步失败,异常信息:"+e.getMessage();
                        e.printStackTrace();
                        logger.error("请求广告数据json解析失败|appid:"+m.get("appid")+"|result:"+d);
                        break;
                    }
                }
            }catch (Exception e) {
                msg = "变现明细表:小游戏同步失败,异常信息:"+e.getMessage();
                e.printStackTrace();
                logger.error("请求失败|appid:"+m.get("appid")+"|result:"+e.getMessage());
                continue;
            }
        }
        int i = 0;
        if (data != null && data.size() >0) {
            //删除旧数据
            String del1 = "delete from dn_cha_cash_total where date BETWEEN '"+startTime+"' AND '"+endTime+"' "+
                    "and agent = 'weixin'";
            adMapper.execSql(del1);
            //插入数据
            i = ydMapper.batchInsertXyxAdvData(data);
            //TODO ADB添加小游戏变现明细数据
            dnwxBiAdtMapper.execSql(del1);
            dnwxBiAdtMapper.batchInsertXyxAdvData(data);
        }else{
            //监控发消息
            try {
                String httpPost = sendPost(ProtocolConstant.fs_send_url,msg+",同步时间:"+startTime+"到"+endTime,"huangmb,admin");
                logger.error("变现明细表:小游戏同步发送飞书监控失败:"+httpPost);
            }catch (Exception e) {

            }
        }
        return i;
    }

    /**
     * 发送POST请求
     * @param url
     * @return
     */
    private String sendPost(String url,String msg,String uname) throws Exception{
        CloseableHttpClient httpClient = HttpClients.createDefault();// 创建http实例
        CloseableHttpResponse response = null;
        try {
            HttpPost httpPost =new HttpPost();
            httpPost.setURI(new URI(url));
            List<NameValuePair> parms = new ArrayList<>();
            parms.add(new BasicNameValuePair("msg", msg));
            parms.add(new BasicNameValuePair("uname", uname));
            httpPost.setEntity(new UrlEncodedFormEntity(parms,"utf-8"));
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity(); //请求类型
            String content = EntityUtils.toString(entity, "utf-8");
            return content;
        }catch (Exception e) {
            throw e;
        }finally {
            httpClient.close();
            try {
                response.close();
            }catch (Exception e) {

            }
        }
    }


    public Map<String,JSONObject> getXyxAdsidReport() {
        Map<String,JSONObject> result = new HashMap<>();
        List<JSONObject> list1 = ad2Mapper.selectXyxAdsidReport();
        list1.forEach(l ->{
            result.put(l.getString("sdk_code"),l);
        });
        return result;
    }

    public String convertDate(String str){
        try {
            Date format1 = null;
            format1 = new SimpleDateFormat("yyyyMMdd").parse(str);
            String longDate = new SimpleDateFormat("yyyy-MM-dd").format(format1);
            return longDate;
        } catch (Exception e) {
            return str;
        }
    }

}
