package com.wbgame.service.clean.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.mapper.clean.master.SuperFaceUserMapper;
import com.wbgame.pojo.clean.face.SuperFaceUserDTO;
import com.wbgame.pojo.clean.face.SuperFaceUserVO;
import com.wbgame.service.clean.ISuperFaceUserService;
import com.wbgame.utils.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @authoer: zhangY
 * @createDate: 2022/7/19 17:15
 * @class: SuperFaceUserServiceImpl
 * @description:
 */
@Service
public class SuperFaceUserServiceImpl implements ISuperFaceUserService {

    @Autowired
    private SuperFaceUserMapper superFaceUserMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public PageResult<SuperFaceUserVO> selectAllByCondition(SuperFaceUserDTO superFaceUserDTO) {

        PageHelper.startPage(superFaceUserDTO.getStart(), superFaceUserDTO.getLimit());
        superFaceUserDTO.setEnd_date(superFaceUserDTO.getEnd_date() + " 23:59:59");
        List<SuperFaceUserVO> list = superFaceUserMapper.selectAllByCondition(superFaceUserDTO);
        PageResult<SuperFaceUserVO> pageResult = PageResult.page(new PageInfo<>(list));

        list = list.stream().peek(superFaceUserVO -> {

            // 将会员到期时间戳转换为时间
            if (!StringUtils.isBlank(superFaceUserVO.getExpiresDateMs())) {

                long expiresDateMs = Long.parseLong(superFaceUserVO.getExpiresDateMs());
                LocalDateTime dateTime = LocalDateTime.ofEpochSecond(expiresDateMs / 1000, 0, ZoneOffset.ofHours(8));
                superFaceUserVO.setExpiresDateMs(dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }

        }).collect(Collectors.toList());
        pageResult.setList(list);
        return pageResult;
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public int addMember(String userId, Integer day) {

        String expirationTime = superFaceUserMapper.getExpirationTime(userId);
        long expiresDateMs = StringUtils.isBlank(expirationTime) || Long.parseLong(expirationTime) <= System.currentTimeMillis() ?
                System.currentTimeMillis() : Long.parseLong(expirationTime);
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(expiresDateMs / 1000, 0, ZoneOffset.ofHours(8));

        // 添加天数
        long newTimeMilli = dateTime.plusDays(day).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();

        int i = superFaceUserMapper.addOrClearMember(userId, newTimeMilli + "");
        if (i > 0) {

            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("userId", userId);
            paramMap.put("expiresDateMs", newTimeMilli);
            try {

                i = this.flushCache(getThePathAccordingToTheEnvironment(), paramMap);

            } catch (Exception e) {
                e.printStackTrace();
                i = -1;
            }

        }

        return i;
    }

    /**
     * 刷新缓存
     * @param url
     * @param paramMap
     * @return
     */
    private int flushCache(String url, Map<String, Object> paramMap) {

        JSONObject result = restTemplate.getForObject(url, JSONObject.class, paramMap);
        Integer code = (Integer) result.get("code");
        String message = (String) result.get("message");
        return code == 200 && "ok".equals(message) ? 1 : -1;
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public int clearMember(String userId) {

        int i = superFaceUserMapper.clearMember(userId);

        if (i > 0) {

            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("userId", userId);
            paramMap.put("expiresDateMs", 0);
            try {

                i = this.flushCache(getThePathAccordingToTheEnvironment(), paramMap);

            } catch (Exception e) {
                e.printStackTrace();
                i = -1;
            }

        }
        return i;
    }

    private String getThePathAccordingToTheEnvironment() {

        if ("test".equals(active)) {

            return  "http://192.168.7.35:6408/face/wx/change/v1?userId={userId}&expiresDateMs={expiresDateMs}";

        }

        return  "https://app.superclear.cn/face/wx/change/v1?userId={userId}&expiresDateMs={expiresDateMs}";
    }

}
