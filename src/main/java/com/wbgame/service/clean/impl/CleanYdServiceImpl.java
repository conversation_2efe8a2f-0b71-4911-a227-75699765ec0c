package com.wbgame.service.clean.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.clean.master.CleanYdMapper;
import com.wbgame.mapper.master.DataMapper;
import com.wbgame.pojo.clean.*;
import com.wbgame.service.clean.CleanYdService;
import com.wbgame.utils.BlankUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CleanYdServiceImpl implements CleanYdService {

	@Autowired
	private CleanYdMapper ydMapper;

	@Autowired
	private DataMapper dataMapper;
	
	@Override
	public int updateDnCleanChannelConfig(DnCleanChannelConfig dnCleanChannelConfig) {
		return ydMapper.updateDnCleanChannelConfig(dnCleanChannelConfig);
	}

	@Override
	public int updateDnChannelConfigOnOff(DnCleanChannelConfig dnCleanChannelConfig) {
		return ydMapper.updateDnChannelConfigOnOff(dnCleanChannelConfig);
	}

	@Override
	public int insertDnCleanChannelConfig(DnCleanChannelConfig dnCleanChannelConfig) {
		return ydMapper.insertDnCleanChannelConfig(dnCleanChannelConfig);
	}

	@Override
	public int deleteDnCleanChannelConfig(DnCleanChannelConfig dnCleanChannelConfig) {
		return ydMapper.deleteDnCleanChannelConfig(dnCleanChannelConfig);
	}

	@Override
	public List<DnCleanChannelConfig> selectDnChannelConfig(DnCleanChannelConfig dnCleanChannelConfig) {
		return ydMapper.selectDnChannelConfig(dnCleanChannelConfig);
	}

	@Override
	public List<Map<String, Object>> queryListMap(String sql) {
		return ydMapper.queryListMap(sql);
	}
	@Override
	public List<String> queryListString(String sql) {
		return ydMapper.queryListString(sql);
	}

	@Override
	public int deleteSuperGameConfig(SuperGameConfig record) {
		return ydMapper.deleteSuperGameConfig(record);
	}

	@Override
	public int insertSuperGameConfig(SuperGameConfig record) {
		return ydMapper.insertSuperGameConfig(record);
	}

	@Override
	public List<SuperGameConfig> selectSuperGameConfig(SuperGameConfig record) {
		return ydMapper.selectSuperGameConfig(record);
	}

	@Override
	public int updateSuperGameConfig(SuperGameConfig record) {
		return ydMapper.updateSuperGameConfig(record);
	}

	@Override
	public List<DnCleanChannelConfig> selectDnChannelPid(DnCleanChannelConfig dnCleanChannelConfig) {
		return ydMapper.selectDnChannelPid(dnCleanChannelConfig);
	}

	@Override
	public int insertDnCleanChannelPid(DnCleanChannelConfig dnCleanChannelConfig) {
		return ydMapper.insertDnCleanChannelPid(dnCleanChannelConfig);
	}

	@Override
	public int updateDnCleanChannelPid(DnCleanChannelConfig dnCleanChannelConfig) {
		return ydMapper.updateDnCleanChannelPid(dnCleanChannelConfig);
	}

	@Override
	public int updateDnChannelPidOnOff(DnCleanChannelConfig dnCleanChannelConfig) {
		return ydMapper.updateDnChannelPidOnOff(dnCleanChannelConfig);
	}

	@Override
	public int deleteDnCleanChannelPid(DnCleanChannelConfig dnCleanChannelConfig) {
		return ydMapper.deleteDnCleanChannelPid(dnCleanChannelConfig);
	}
	
	@Override
	public List<NpActiveFree> selectAdTableConfig(Map<Object, Object> map) {
		return ydMapper.selectAdTableConfig(map);
	}

	@Override
	public int updateAdTableConfig(NpActiveFree activeFree) {
		return ydMapper.updateAdTableConfig(activeFree);
	}

	@Override
	public int insertAdTableConfig(NpActiveFree activeFree) {
		return ydMapper.insertAdTableConfig(activeFree);
	}

	@Override
	public int deleteSuperConfigTab(SuperConfigTab record) {
		return ydMapper.deleteSuperConfigTab(record);
	}

	@Override
	public int insertSuperConfigTab(SuperConfigTab record) {
		return ydMapper.insertSuperConfigTab(record);
	}

	@Override
	public List<SuperConfigTab> selectSuperConfigTab(SuperConfigTab record) {
		return ydMapper.selectSuperConfigTab(record);
	}

	@Override
	public int updateSuperConfigTab(SuperConfigTab record) {
		return ydMapper.updateSuperConfigTab(record);
	}

	@Override
	public int deleteSuperConfigLock(SuperConfigLock record) {
		return ydMapper.deleteSuperConfigLock(record);
	}

	@Override
	public int insertSuperConfigLock(SuperConfigLock record) {
		return ydMapper.insertSuperConfigLock(record);
	}

	@Override
	public List<SuperConfigLock> selectSuperConfigLock(SuperConfigLock record) {
		return ydMapper.selectSuperConfigLock(record);
	}

	@Override
	public int updateSuperConfigLock(SuperConfigLock record) {
		return ydMapper.updateSuperConfigLock(record);
	}

	@Override
	public int updatebBatchLock(SuperConfigLock record) {
		return ydMapper.updatebBatchLock(record);
	}

	@Override
	public int updatebBatchTab(SuperConfigTab record) {
		return ydMapper.updatebBatchTab(record);
	}

	@Override
	public int deleteSuperConfigNews(SuperConfigNews record) {
		return ydMapper.deleteSuperConfigNews(record);
	}

	@Override
	public int insertSuperConfigNews(SuperConfigNews record) {
		return ydMapper.insertSuperConfigNews(record);
	}

	@Override
	public List<SuperConfigNews> selectSuperConfigNews(SuperConfigNews record) {
		return ydMapper.selectSuperConfigNews(record);
	}

	@Override
	public int updateSuperConfigNews(SuperConfigNews record) {
		return ydMapper.updateSuperConfigNews(record);
	}

	@Override
	public int updatebBatchNews(SuperConfigNews record) {
		return ydMapper.updatebBatchNews(record);
	}

	@Override
	public List<SuperConfigDeeplink> selectSuperConfigDeeplink(SuperConfigDeeplink record) {
		return ydMapper.selectSuperConfigDeeplink(record);
	}

	@Override
	public int deleteSuperConfigDeeplink(SuperConfigDeeplink record) {
		return ydMapper.deleteSuperConfigDeeplink(record);
	}

	@Override
	public int updateSuperConfigDeeplink(SuperConfigDeeplink record) {
		return ydMapper.updateSuperConfigDeeplink(record);
	}

	@Override
	public int insertSuperConfigDeeplink(SuperConfigDeeplink record) {
		return ydMapper.insertSuperConfigDeeplink(record);
	}

	@Override
	public int updatebBatchDeeplink(SuperConfigDeeplink record) {
		return ydMapper.updatebBatchDeeplink(record);
	}

	@Override
	public List<SuperConfigParams> selectSuperConfigParams(SuperConfigParams record) {
		if (!BlankUtils.checkBlank(record.getCountry())){
			String[] countries = record.getCountry().split(",");
			record.setCountries(Arrays.asList(countries));
		}
		return ydMapper.selectSuperConfigParams(record);
	}

	@Override
	public int deleteSuperConfigParams(SuperConfigParams record) {
		return ydMapper.deleteSuperConfigParams(record);
	}

	@Override
	public int updateSuperConfigParams(SuperConfigParams record) {
		return ydMapper.updateSuperConfigParams(record);
	}

	@Override
	public int insertSuperConfigParams(SuperConfigParams record) {
		return ydMapper.insertSuperConfigParams(record);
	}

    @Override
    public List<SuperWhiteList> selectWhiteList(SuperWhiteList superWhiteList) {
        return ydMapper.selectWhiteList(superWhiteList);
    }

	@Override
	public int insertWhiteList(SuperWhiteList superWhiteList) {
		return ydMapper.insertWhiteList(superWhiteList);
	}

	@Override
	public int deleteWhiteList(SuperWhiteList superWhiteList) {
		return ydMapper.deleteWhiteList(superWhiteList);
	}

	@Override
	public void insertBatchWhiteList(List<SuperWhiteList> list) {
		ydMapper.insertBatchWhiteList(list);
	}


	@Override
	public int deleteSuperConfigPublic(SuperConfigPublic record) {
		return ydMapper.deleteSuperConfigPublic(record);
	}

	@Override
	public int updateSuperConfigPublic(SuperConfigPublic record) {
		return ydMapper.updateSuperConfigPublic(record);
	}

	@Override
	public int insertSuperConfigPublic(SuperConfigPublic record) {
		return ydMapper.insertSuperConfigPublic(record);
	}

	@Override
	public List<SuperConfigPublic> selectSuperConfigPublic(SuperConfigPublic record) {
		return ydMapper.selectSuperConfigPublic(record);
	}

	@Override
	public int updatebBatchPublic(SuperConfigPublic record) {
		return ydMapper.updatebBatchPublic(record);
	}

	@Override
	public List<SuperDrawRange> selectSuperDrawRange(SuperDrawRange record) {
		return ydMapper.selectSuperDrawRange(record);
	}

	@Override
	public int updateSuperConfigLockStatus(SuperConfigLock record) {
		return ydMapper.updateSuperConfigLockStatus(record);
	}

	@Override
	public List<SuperConfigWinmsg> selectSuperConfigWinmsg(SuperConfigWinmsg record) {
		return ydMapper.selectSuperConfigWinmsg(record);
	}

	@Override
	public int deleteSuperConfigWinmsg(SuperConfigWinmsg record) {
		return ydMapper.deleteSuperConfigWinmsg(record);
	}

	@Override
	public int updateSuperConfigWinmsg(SuperConfigWinmsg record) {
		return ydMapper.updateSuperConfigWinmsg(record);
	}

	@Override
	public int insertSuperConfigWinmsg(SuperConfigWinmsg record) {
		return ydMapper.insertSuperConfigWinmsg(record);
	}

	@Override
	public int insertSuperConfigWinmsgBatch(List<SuperConfigWinmsg> list) {
		return ydMapper.insertSuperConfigWinmsgBatch(list);
	}

	@Override
	public List<SuperLockNew> selectSuperLockNew(SuperLockNew record) {
		return ydMapper.selectSuperLockNew(record);
	}

	@Override
	public int deleteSuperLockNew(SuperLockNew record) {
		return ydMapper.deleteSuperLockNew(record);
	}

	@Override
	public int updateSuperLockNew(SuperLockNew record) {
		return ydMapper.updateSuperLockNew(record);
	}

	@Override
	public int insertSuperLockNew(SuperLockNew record) {
		return ydMapper.insertSuperLockNew(record);
	}

	@Override
	public int batchInsertDnGroupAppToClean(List<Map<String, Object>> list) {
		return ydMapper.batchInsertDnGroupAppToClean(list);
	}

	@Override
	public int delCleanDnGroupApp() {
		return ydMapper.delCleanDnGroupApp();
	}

	@Override
	public List<AppSafeConfig> selectAppSafe(AppSafeConfig record) {
		return ydMapper.selectAppSafe(record);
	}

	@Override
	public int deleteAppSafe(AppSafeConfig record) {
		return ydMapper.deleteAppSafe(record);
	}

	@Override
	public int updateAppSafe(AppSafeConfig record) {
		return ydMapper.updateAppSafe(record);
	}

	@Override
	public int insertAppSafe(AppSafeConfig record) {
		return ydMapper.insertAppSafe(record);
	}

	@Override
	@Transactional
	public String insertSuperAdvanceConfig(SuperAdvanceConfigDTO advanceConfigDTO) {

		// appid + 渠道 唯一； appid + 产品（可以自己输入） 唯一

		//appId是否存在
		if (ydMapper.selectAPPIdDoesItExist(advanceConfigDTO.getAppid()) == 0) {
			return ReturnJson.error(Constants.ParamError);
		}
		// 参数重置
		String cha = advanceConfigDTO.getCha();
		String prjid = advanceConfigDTO.getPrjid();
		boolean chaISNull = org.apache.commons.lang.StringUtils.isBlank(cha);
		boolean prjidIsNull = org.apache.commons.lang3.StringUtils.isBlank(prjid);

		if (!chaISNull) {

			advanceConfigDTO.setPrjid(null);
			// 渠道是否存在 && appId和渠道id 是否已有
			if (ydMapper.selectChaDoesItExist(advanceConfigDTO.getCha()) == 0 ||
					ydMapper.selectAdvanceByAppIdChaIdExist(advanceConfigDTO.getAppid(), advanceConfigDTO.getCha()) != null) {
				return ReturnJson.toErrorJson("渠道不存在或者产品+渠道有重复！");
			}
		} else if (!prjidIsNull) {

			advanceConfigDTO.setCha(null);
			if (ydMapper.selectAdvanceByAppIdPrjidExist(advanceConfigDTO.getAppid(), advanceConfigDTO.getPrjid()) != null) {

				return ReturnJson.toErrorJson("产品+项目id有重复");
			}
		} else {

			return ReturnJson.error(Constants.ParamError);
		}

		SuperAdvanceConfig advanceConfig = new SuperAdvanceConfig();
		BeanUtils.copyProperties(advanceConfigDTO, advanceConfig);

		ydMapper.insertSuperAdvanceConfig(advanceConfig);
		return ReturnJson.success();
	}

	@Override
	@Transactional
	public String updateSuperAdvanceConfig(SuperAdvanceConfigDTO advanceConfigDTO) {

		// 参数重置
//		String cha = advanceConfigDTO.getCha();
//		String prjid = advanceConfigDTO.getPrjid();
//		boolean chaISNull = org.apache.commons.lang.StringUtils.isBlank(cha);
//		boolean prjidIsNull = org.apache.commons.lang3.StringUtils.isBlank(prjid);
		String id = advanceConfigDTO.getId();

		SuperAdvanceConfig sourceConfig;
		// 根据id获取APPId和渠道 以及appid和产品的id数据
		sourceConfig = ydMapper.selectSuperAdvanceById(id == null ? -1 : Integer.parseInt(id));
		if (sourceConfig == null) {

			return ReturnJson.toErrorJson("配置不存在");
		} else {

//			String appid = sourceConfig.getAppid();
//			if (!chaISNull) {
//
//				if (ydMapper.selectChaDoesItExist(advanceConfigDTO.getCha()) == 0) {
//					ReturnJson.toErrorJson("渠道不存在");
//				}
//				advanceConfigDTO.setPrjid("");
////			sourceConfig = ydMapper.selectAdvanceByAppIdChaIdExist(appid, advanceConfigDTO.getCha());
//				sourceConfig.setCha(cha);
//
//			} else if (!prjidIsNull) {
//
//				advanceConfigDTO.setCha("");
//				sourceConfig = ydMapper.selectAdvanceByAppIdPrjidExist(appid, advanceConfigDTO.getPrjid());
//				if (sourceConfig != null) {
//
//					return ReturnJson.toErrorJson("应用和渠道重复或应用和产品重复");
//				}
//			} else {
//
//				return ReturnJson.error(Constants.ParamError);
//			}


			sourceConfig = new SuperAdvanceConfig();
			advanceConfigDTO.setAppid(null);
			advanceConfigDTO.setCha(null);
			advanceConfigDTO.setPrjid(null);
			advanceConfigDTO.setCreateTime(null);
			advanceConfigDTO.setCreateUser(null);

			sourceConfig.setId(Integer.valueOf(advanceConfigDTO.getId()));
			BeanUtils.copyProperties(advanceConfigDTO, sourceConfig);

			ydMapper.updateSuperAdvanceConfig(sourceConfig);
			return ReturnJson.success();
		}

	}

	@Override
	@Transactional
	public String deleteSuperAdvanceConfig(SuperAdvanceConfigDTO advanceConfigDTO) {
		return ydMapper.deleteSuperSuperAdvanceConfig(Integer.valueOf(advanceConfigDTO.getId())) > 0 ?
				ReturnJson.success() : ReturnJson.error();
	}

	@Override
	public String selectSuperAdvanceConfig(SuperAdvanceConfigDTO superAdvanceConfigDTO, Integer pageNum, Integer pageSize) {

		pageNum = pageNum == null || pageNum == 0 ? 1 : pageNum;
		pageSize = pageSize == null || pageSize == 0 ? 100 : pageSize;

		String appidStr = superAdvanceConfigDTO.getAppid();
		String chaStr = superAdvanceConfigDTO.getCha();
		String prjidStr = superAdvanceConfigDTO.getPrjid();

		appidStr = appidStr == null ? "" : appidStr.replaceAll("'|\\s*", "");
		chaStr = chaStr == null ? "" : chaStr.replaceAll("'|\\s*", "");
		prjidStr = prjidStr == null ? "" : prjidStr.replaceAll("'|\\s*", "");

		if (
				chaStr.startsWith(",") ||
				prjidStr.startsWith(",") ||
				appidStr.startsWith(",") ||
				appidStr.contains(",,") ||
				chaStr.contains(",,") ||
				prjidStr.contains(",,")) {

			ReturnJson.error(Constants.ParamError);
		}
		String[] strings = appidStr.length() == 0 ? null : appidStr.split(",");
		String[] chas = chaStr.length() == 0 ? null : chaStr.split(",");
		String[] prjids = prjidStr.length() == 0 ? null :prjidStr.split(",");

		PageHelper.startPage(pageNum, pageSize);
		List<SuperAdvanceConfigVO> superAdvanceConfigs = ydMapper.selectSuperAdvanceConfig(strings, chas, prjids, superAdvanceConfigDTO.getStatus());
		PageInfo<SuperAdvanceConfigVO> pageInfo = new PageInfo<>(superAdvanceConfigs);
		return ReturnJson.success(pageInfo);
	}


	@Override
	@Transactional
	public String batchEditSuperAdvanceConfig(SuperAdvanceConfigDTO advanceConfigDTO) {

		String idStrs = advanceConfigDTO.getId();
		idStrs = idStrs == null ? "" : idStrs.replaceAll("'|\\s*", "");

		if (idStrs.startsWith(",") || idStrs.contains(",,") || idStrs.length() == 0) {
			ReturnJson.error(Constants.ParamError);
		}

		List<Integer> idList = Arrays.stream(idStrs.split(",")).map(Integer::valueOf).
				collect(Collectors.toList());
		SuperAdvanceConfig config = new SuperAdvanceConfig();
		advanceConfigDTO.setId(null);
		BeanUtils.copyProperties(advanceConfigDTO, config);
		ydMapper.batchEditSuperAdvanceConfig(idList, config);
		return ReturnJson.success();
	}


	@Override
	@Transactional
	public String batchSuperUseLevel(SuperUseLevel superUseLevel) {

		if (superUseLevel != null) {

			superUseLevel.setCreateUser(null);
			String id = superUseLevel.getId();

			String prjid = superUseLevel.getPrjid();
			// 广告类型
//			String adType = superUseLevel.getAdType();
//
//			String adTime = superUseLevel.getAdTime() == null ? "" :
//					superUseLevel.getAdTime().replace("\\D", "");
//			String levelA = superUseLevel.getLevelA() == null ? "" :
//					superUseLevel.getLevelA().replace("\\D", "");
//			String levelB = superUseLevel.getLevelB() == null ? "" :
//					superUseLevel.getLevelB().replace("\\D", "");
//			String levelC = superUseLevel.getLevelC() == null ? "" :
//					superUseLevel.getLevelC().replace("\\D", "");
//			String status = superUseLevel.getStatus() == null ? "" :
//					superUseLevel.getLevelC().replace("\\D", "");
			if (
					StringUtils.isBlank(id) // ||
//					StringUtils.isBlank(prjid) ||
//					StringUtils.isBlank(adType) ||
//					StringUtils.isBlank(adTime) ||
//					StringUtils.isBlank(levelA) ||
//					StringUtils.isBlank(levelB) ||
//					StringUtils.isBlank(levelC) ||
//					StringUtils.isBlank(status)
			) {
				return ReturnJson.error(Constants.ParamError);
			}

			// 校验广告类型:前端写死的

			superUseLevel.setCreateUser(null);
			superUseLevel.setAppid(null);
			superUseLevel.setPrjid(null);
			superUseLevel.setCha(null);

			ydMapper.updateSuperUseLevel(superUseLevel);
			return ReturnJson.success();


			/**
			 * 下边是需要修改prjid的
			 */
			// 根据id获取批量修改的 appid 和 prjid  ===>super_user_level
			/*List<SuperUseLevel> levelList = ydMapper.selectSuperUseLevelByIds(superUseLevel);

			// 根据prjid获取 id 和 appid+prjid
			List<SuperUseLevel> checkRepeatList = ydMapper.selectAppIdByPrjId(superUseLevel.getPrjid());

			superUseLevel.setCha("");
			if (checkRepeatList.size() == 0) {

				// 判断要修改的appid是否有多条数据， 有则不允许修改
				if (levelList.size() != levelList.stream().map(SuperUseLevel::getAppid).distinct().count()) {
					return ReturnJson.toErrorJson("所选项存在相同产品id对应多条项目id，无法修改");
				}
				// 修改appid和产品id对应关系和其他列的数据
				superUseLevel.setCreateUser(null);
				superUseLevel.setAppid(null);
//				superUseLevel.setCha(null);

				int updateNum = ydMapper.updateSuperUseLevel(superUseLevel);

				return ReturnJson.success();
			} else {

				// 根据prjid获取的appid数据 判断需要修改的appid+prjid 是否重复
				List<SuperUseLevel> matchedData = levelList.stream().filter(useLevel -> {

					for (SuperUseLevel level : checkRepeatList) {
						if (
								level.getAppid().equals(useLevel.getAppid()) &&
								level.getPrjid().equals(superUseLevel.getPrjid()) &&
								!level.getId().equals(useLevel.getId())) {

							return true;
						}
					}
					return false;
				}).collect(Collectors.toList());

				if (matchedData.size() > 0) {

					StringBuilder builder = new StringBuilder();
					for (SuperUseLevel level : matchedData) {

						builder.append(level.getAppid()).append(" ");
					}
					return ReturnJson.toErrorJson("产品+项目id有重复" + "[产品id:(" + builder.toString() +")]");
				} else {

					// 修改appid和产品id对应关系和其他列的数据
					superUseLevel.setCreateUser(null);
					superUseLevel.setAppid(null);

					int updateNum = ydMapper.updateSuperUseLevel(superUseLevel);
					return ReturnJson.success();
				}
			}*/
		}
		return ReturnJson.error(Constants.ParamError);
	}


	@Override
	@Transactional
	public String deleteFilterListById(List<Long> idList) {

		return idList.isEmpty() ? ReturnJson.toErrorJson("param error") :
				ReturnJson.success(dataMapper.deleteFilterListById(idList));
	}

	@Override
	@Transactional
	public String insertFilterList(DnwxFilterListDTO filterDTO) {

		if (!StringUtils.isBlank(filterDTO.getAppid()) && dataMapper.countAppIdById(Integer.parseInt(filterDTO.getAppid())) == 0) {

			return ReturnJson.toErrorJson("产品不存在!");
		}

		String topic = filterDTO.getTopic();
		filterDTO.setTopic(StringUtils.isBlank(topic) ? "danger" : topic);
		DnwxFilterList filter = new DnwxFilterList();
		BeanUtils.copyProperties(filterDTO, filter);
		dataMapper.insertFilterList(filter);
		return ReturnJson.success();
	}

	@Override
	public String selectFilterList(DnwxFilterListQuery query) {

		PageHelper.startPage(query.getPageNum(), query.getPageSize());
		List<DnwxFilterListVO> filterList = dataMapper.selectFilterList(query);
		PageInfo<DnwxFilterListVO> pageInfo = new PageInfo<>(filterList);
		return ReturnJson.success(pageInfo);
	}
}
