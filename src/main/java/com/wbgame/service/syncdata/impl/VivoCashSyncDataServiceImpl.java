package com.wbgame.service.syncdata.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.hash.Hashing;
import com.wbgame.metric.cash.CashMetric;
import com.wbgame.pojo.adv2.businessAccountEntity.CashVivoAccount;
import com.wbgame.pojo.adv2.reportEntity.ReportChina;
import com.wbgame.pojo.adv2.reportEntity.ReportWrapper;
import com.wbgame.pojo.adv2.resultEntity.SyncResult;
import com.wbgame.service.syncdata.CashSyncDataService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtils;
import lombok.NoArgsConstructor;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.wbgame.common.constants.PlatformName.VIVO;

/**
 * <AUTHOR>
 * @date 4.7
 * @description vivo
 **/
@Service
@NoArgsConstructor
public class VivoCashSyncDataServiceImpl extends CashSyncDataService {

    private final String baseDataUrl = "https://adnet.vivo.com.cn/api/open/report";

    private final String catchUrl = "https://adnet.vivo.com.cn/api/report/getReportTableData";

    Logger logger = LoggerFactory.getLogger(VivoCashSyncDataServiceImpl.class);

    @Override
    public SyncResult syncData(String sdate, String edate) {
        try {
            for (String date : DateUtils.getHyphenDateList(sdate, edate)) {
                super.syncData(date, date);
            }
            return SyncResult.builder().code(0).success(true).build();
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @CashMetric(value = VIVO, step = "account")
    protected List<?> loadAccount() {
        List<CashVivoAccount> cashVivoAccounts = cashReportMapper.getValidCashVivoAccountList();
        if (CollectionUtils.isEmpty(cashVivoAccounts)) {
            logger.error("no valid vivo account");
            return null;
        }
        return cashVivoAccounts;
    }

    @Override
    @CashMetric(value = VIVO, step = "api")
    public Object apiResolve(String sdate, String edate, Object account) {
        final String fsdate = DateUtils.dateDeleteHyphen(sdate);
        final String fedate = DateUtils.dateDeleteHyphen(edate);

        CashVivoAccount vivoAccount = (CashVivoAccount) account;
        String accountName = vivoAccount.getAccount();
        logger.info("getVivoReport spider start, account is {}, startDay is {}, endDay is {}", accountName, sdate, edate);

//        JSONArray baseDatas = doGetBaseData(vivoAccount, sdate, edate);
        JSONArray baseDatas = metricInvoke(()->doGetBaseData(vivoAccount, fsdate, fedate), VIVO, STEP_API);

        if (CollectionUtils.isEmpty(baseDatas)) {
//            logger.info("vivo base data get error, info is empty");
            String format = String.format("%s base date error", accountName);
            failedProcess(VIVO, format);
            return null;
        }
        return baseDatas;
    }

    @Override
    protected List<ReportChina> doBuildReport(Object data, Object account, String sdate, String edate) {
        CashVivoAccount vivoAccount = (CashVivoAccount) account;
        JSONArray baseDatas = (JSONArray) data;
        List<ReportChina> reportChinas = new ArrayList<>();
//        Map<String, Double> catchMap = doGetCatch(vivoAccount, sdate, edate, 1, 2);
        Map<String, Double> catchMap = metricInvoke(()->doGetCatch(vivoAccount, sdate, edate, 1, 2), VIVO+"_catch", STEP_API);
        for (Object obj : baseDatas) {
            ReportChina reportChina = formReportEntity(obj);
            if (reportChina == null) {
                continue;
            }
            reportChina.setMember_id(((CashVivoAccount)account).getAccount());
            if (!CollectionUtils.isEmpty(catchMap)) {
                reportChina.setBuy_revenue(catchMap.getOrDefault(reportChina.getApp_id(), 0.0D));
                // 去除多余数据，一个appid，存在多个广告位，只赋值一次
                catchMap.remove(reportChina.getApp_id());
            } else {
                reportChina.setBuy_revenue(0.0D);
            }
            reportChinas.add(reportChina);
        }
        return reportChinas;
    }


    /*
        * json object format in json array
        * {
                "date": "********",
                "mediaId": "5c0303f26e1640f383b2fd0b76e88c76",
                "mediaName": "王牌战机对决",
                "positionId": "09f2afe8902a48e4ae357599b2e1bc98",
                "positionName": "王牌战机对决-原生模板信息流",
                "view": "3",
                "click": "1",
                "income": "0.04",
                "ecpm": "13.33",
                "cpc": "0.04",
                "clickRatio": "33.33"
            }*/
    @Override
    public ReportChina formReportEntity(Object obj) {
        JSONObject baseData = (JSONObject) obj;
        ReportChina report = ReportChina.builder()
                .date(DateUtils.changeDateStringToHyphen(baseData.getString("date")))
                .agent(VIVO)
                .app_id(baseData.getString("mediaId"))
                .placement_id(baseData.getString("positionId"))
                .pv(baseData.getIntValue("view"))
                .click(baseData.getIntValue("click"))
                .revenue(baseData.getDoubleValue("income"))
//                .country("CN")
//                .source(1)
                .configKey(baseData.getString("mediaId")+"_"+baseData.getString("positionId"))
                .build();

        return report;
    }

    @Override
    protected ReportWrapper buildWrapper(Object account, String date, List<ReportChina> reportChinas) {
        final CashVivoAccount vivoAccount = (CashVivoAccount) account;
        return new ReportWrapper(VIVO, vivoAccount.getAccount(), date, reportChinas);
    }

    private JSONArray doGetBaseData(CashVivoAccount vivoAccount, String startDate, String endDate) {
        try {
            long time = System.currentTimeMillis();
            String accountName = vivoAccount.getAccountName();
            String secretKey = vivoAccount.getSecretKey();
            String sign = Hashing.sha256()
                    .hashString(accountName + secretKey + time, StandardCharsets.UTF_8).toString();
//        String sign = DigestUtils.sha256Hex(accountName + secretKey + time);
            String token = Base64.encodeBase64String((time + "," + sign).getBytes());
            HashMap<String, String> params = new HashMap<>();
            HashMap<String, String> headers = new HashMap<>();
            headers.put("token", token);
            params.put("accountName", accountName);
            params.put("startDate", startDate);
            params.put("endDate", endDate);

            String httpGet = httpGet(baseDataUrl, params, headers, false);
            if (BlankUtils.isBlank(httpGet)) {
                apiErrorCounter.labels(VIVO, "empty").inc(1);
                logger.info("vivo get empty data, account: {}, start: {}, end: {}", accountName, startDate, endDate);
            }

            JSONObject jsonObject = JSONObject.parseObject(httpGet);
            if (jsonObject == null) {
                apiErrorCounter.labels(VIVO, "empty").inc(1);
                logger.info("vivo json object parse error, account: {}, start: {}, end: {}, data is {}", accountName, startDate, endDate, httpGet);
                return null;
            }
            int code = jsonObject.getIntValue("code");
            if (code != 0) {
                apiErrorCounter.labels(VIVO, jsonObject.getString("message")).inc(1);
                logger.info("vivo get error, account: {}, start: {}, end: {}, message is \"{}\"", accountName, startDate, endDate, jsonObject.getString("message"));
                return null;
            }

            return jsonObject.getJSONArray("data");
        } catch (Exception e) {
            apiErrorCounter.labels(VIVO, e.getMessage()).inc(1);
            logger.error("vivo get base data error: ", e);
            return null;
        }
    }

    private Map<String, Double> doGetCatch(CashVivoAccount vivoAccount,
                                           String startDate, String endDate, int page, int flowType) {

        int pageSize = 5000;
        String day = vivoAccount.getDay();
        String account = vivoAccount.getAccount();

        HashMap<String, String> params = new HashMap<>();
        HashMap<String, String> headers = new HashMap<>();
        headers.put("cookie", vivoAccount.getCookie());
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("dimensions", "mediaId");
        params.put("platformType", "");
        params.put("metrics", "view");
        params.put("flowType", String.valueOf(flowType));
        params.put("pageIndex", String.valueOf(page));
        params.put("pageSize", String.valueOf(pageSize));

        String httpGet = httpGet(this.catchUrl, params, headers, true);

        if (BlankUtils.isBlank(httpGet)) {
//            apiErrorCounter.labels(VIVO, "catchEmpty").inc(1);
//            logger.info("vivo catch data is null, page: {}, account: {}, day: {}", page, account, day);
            String format = String.format("%s %s catch empty", startDate+"-"+endDate, account);
            failedProcessLog(format);
            return null;
        }

        JSONObject jsonObject = JSONObject.parseObject(httpGet);

        if (jsonObject.getIntValue("code") != 1) {
//            apiErrorCounter.labels(VIVO, httpGet).inc(1);
//            logger.info("vivo catch get failed, page: {}, account: {}, day: {}, {}", page, account, day, httpGet);
            String format = String.format("%s %s catch error %s", startDate+"-"+endDate, account, httpGet);
            failedProcessLog(format);
            return null;
        }

        JSONObject data = jsonObject.getJSONObject("data");
        Map<String, Double> catchMap = data.getJSONArray("dataList").stream().map(obj -> (JSONObject) obj)
                .collect(Collectors.toMap(json -> json.getString("mediaUuid"),
                        json -> json.getDoubleValue("income"),
                        (k1, k2) -> k1));

        int totalCount = data.getIntValue("totalCount");
        int total_page = (totalCount - 1) / pageSize + 1;
        if (page < total_page) {
            String format = String.format("%s %s enter next page", startDate+"-"+endDate, account, httpGet);
            failedProcessLog(format);
            Map<String, Double>  nextPage= doGetCatch(vivoAccount, startDate, endDate, ++page, flowType);
            if (nextPage != null) {
                catchMap.putAll(nextPage);
            }
        }
        return catchMap;
    }


    public static void main(String[] args) {
//        long time = System.currentTimeMillis();
//        String accountName = "Jiahe2022";
//        String secretKey = "77a38dd72017429cbba4ea9c23c15e8b";
//        String sign = Hashing.sha256().hashString(accountName + secretKey + time, StandardCharsets.UTF_8).toString();
////        String sign = DigestUtils.sha256Hex(accountName + secretKey + time);
//        String token = Base64.encodeBase64String((time + "," + sign).getBytes());
//        HashMap<String, String> params = new HashMap<>();
//        HashMap<String, String> headers = new HashMap<>();
//        headers.put("token", token);
//        params.put("accountName", accountName);
//        params.put("startDate", "********");
//        params.put("endDate", "********");
//
//        VivoSyncDataServiceImpl vivoSyncDataService = new VivoSyncDataServiceImpl(null, null);
//        String httpGet = vivoSyncDataService.httpGet(vivoSyncDataService.baseDataUrl, params, headers, false);
//        JSONObject jsonObject = JSONObject.parseObject(httpGet);
//        System.out.println(token);
//        System.out.println(jsonObject);
        String date = "2023-06-16";

        VivoCashSyncDataServiceImpl vivoSyncDataService = new VivoCashSyncDataServiceImpl();
        CashVivoAccount cashVivoAccount = new CashVivoAccount();
        cashVivoAccount.setCookie("b_account_username=R2Rzg2NgA5d0pRPJKmAMvg%3D%3D; b_account_aid=Udrx%2BUWS4Og%3D; b_account_token=4ebce6a257b1b0db737133e2113d33c3.*************; b_account_salt=04oedawllr34fbJzhXKirg%3D%3D.*************; JSESSIONID=2FBF60505C56DF791E72621E2C90679B");
        cashVivoAccount.setAccountName("Erxiang2022");
        cashVivoAccount.setSecretKey("48eedf77d51f4d4f968a1b87a185bedd");
        cashVivoAccount.setAccount("<EMAIL>");
        vivoSyncDataService.doGetCatch(cashVivoAccount, date, date, 1, 2);


    }
}
