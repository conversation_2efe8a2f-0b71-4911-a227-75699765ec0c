package com.wbgame.service.finance;

import com.wbgame.pojo.finance.MediumFreeIProfitVo;
import com.wbgame.pojo.finance.MediumFreeImportVo;

import java.util.List;
import java.util.Map;

/**
 * @author: caow
 * @createDate: 2023/08/17
 * @class: FinMicGameRevenueService
 * @description: 财务-小游戏收入相关
 */
public interface FinMicGameRevenueService {

    /** 抖音小游戏收入汇总 */
    public List<Map<String,Object>> selectDouyinGameRevenueList(Map<String, String> paramMap);
    public Map<String,Object> selectDouyinGameRevenueListSum(Map<String, String> paramMap);

    /** 抖音小游戏月度数据更新 */
    public boolean updateMonthDouyinGameRevenue(String sdate,String edate);
}
