package com.wbgame.service.mobile;

import com.wbgame.pojo.game.report.RoiVo;
import com.wbgame.pojo.mobile.DayPvMonitorVo;
import com.wbgame.pojo.mobile.HourPvMonitorVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname BigDataService
 * @Description TODO
 * @Date 2022/1/17 16:06
 */
public interface BigDataService {
    /**
     *
     * @param map
     * @return
     */
    List<HourPvMonitorVo> getHourPvMonitorList(Map map);

    /**
     *
     * @param map
     * @return
     */
    List<DayPvMonitorVo> getDayPvMonitorList(Map map);

    /**
     * 产品ROI报表查询
     * @param map
     * @return
     */
    List<RoiVo> getRoiList(Map<String, String> map);


    /**
     * 产品ROI报表图查询
     * @param map
     * @return
     */
    List<RoiVo> getRoiListChart(Map<String, String> map);

    /**
     * 产品ROI报表查询-汇总
     * @param map
     * @return
     */
    RoiVo getRoiSum(Map<String, String> map);
}
