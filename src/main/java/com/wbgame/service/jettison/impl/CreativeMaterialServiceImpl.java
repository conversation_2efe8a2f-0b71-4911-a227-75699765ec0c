package com.wbgame.service.jettison.impl;

import com.alibaba.fastjson.JSONObject;
import com.volcengine.tos.model.object.ObjectMetaRequestOptions;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.tfxt.CreativeMaterialMapper;
import com.wbgame.pojo.jettison.param.AutoCoverParam;
import com.wbgame.pojo.jettison.param.CreativeMaterialParam;
import com.wbgame.pojo.jettison.param.HonorMaterialParam;
import com.wbgame.pojo.jettison.param.OppoMaterialParam;
import com.wbgame.pojo.jettison.vo.*;
import com.wbgame.service.jettison.CreativeMaterialService;
import com.wbgame.utils.*;
import com.wbgame.utils.jettison.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.List;

/**
 * @Description 创意素材库操作
 * <AUTHOR>
 * @Date 2024/10/30 18:30
 */
@Service
@Slf4j
public class CreativeMaterialServiceImpl implements CreativeMaterialService {

    private final static String url = "https://v.vzhifu.net/";

    private final static String bucket = "dnwx-video";

    @Resource
    private CreativeMaterialMapper creativeMaterialMapper;

    @Override
    public List<CreativeMaterialVo> selectCreativeMaterials(CreativeMaterialParam param) {

        List<CreativeMaterialVo> materials = creativeMaterialMapper.selectCreativeMaterials(param);
        for (CreativeMaterialVo materialVo : materials) {
            materialVo.setUrl(url+materialVo.getType()+"/"+ materialVo.getSignature());
        }

        return materials;
    }

    @Override
    public String getAutoCover(AutoCoverParam param) {
        //临时文件
        String path = "temp"+System.currentTimeMillis();
        File video = new File(path+".mp4");
        String coverName = path+"_cover.jpg";
        File coverImage = null;

        try {
            if (BlankUtils.checkBlank(param.getVideo_url()) || !param.getVideo_url().contains("mp4")) {
                return ReturnJson.toErrorJson("非正确的视频地址");
            }

            //当前条件素材规则
            List<VivoMaterialRule> rules = creativeMaterialMapper.selectVivoMaterialRule(param);
            if (rules == null || rules.size() == 0) {
                return ReturnJson.toErrorJson("无法找到该素材的规则");
            }
            VivoMaterialRule rule = rules.get(0);

            //判断当前条件是否需要封面
            if (rule.getCover_size() == null) {
                return ReturnJson.toErrorJson("当前创意类型下素材无需生成封面");
            }

            //限速获取数据
            byte[] data = HttpLimitUtil.download(param.getVideo_url(), 5);

            //下载视频到本地
            try (FileOutputStream fos = new FileOutputStream(video)){
                fos.write(data);
            } catch (IOException e) {
                log.error("读取视频文件地址失败,原因={}",e.getMessage(),e);
                return ReturnJson.toErrorJson("读取视频文件地址失败,原因="+e.getMessage());
            }

            //生成封面
            coverImage = VideoUtil.getCoverImage(video, coverName, StringUtil.getDouble(rule.getCover_size()));
            if (coverImage == null) {
                return ReturnJson.toErrorJson("生成封面失败,请采用其他方式");
            }

            //校验封面大小
            long size = coverImage.length()/1024;
            Integer cover_size = rule.getCover_size();
            if (cover_size < size) {
                return ReturnJson.toErrorJson("生成封面大小="+size+"KB超上限,,请采用其他方式");
            }

            //校验封面
            int width = 0;
            int height = 0;
            try {
                BufferedImage image = ImageIO.read(coverImage);
                width = image.getWidth();
                height = image.getHeight();
            }catch (Exception e) {
                return ReturnJson.toErrorJson("生成封面格式不正确,请采用其他方式");
            }
            if (width != rule.getCover_wide() || height != rule.getCover_height()) {
                return ReturnJson.toErrorJson("生成封面不兼容尺寸,请采用其他方式");
            }



            //上传封面
            String cover_url = "";
            String cover_signature = "";

            //生成md5
            try (FileInputStream inputStream = new FileInputStream(coverImage)){
                cover_signature = DigestUtils.md5Hex(inputStream);
            }catch (Exception e) {
                log.error("上传封面图成功={},获取md5失败",e.getMessage(),e);
                return ReturnJson.toErrorJson("上传封面图成功={},获取md5失败,无法生成md5的原因={}"+e.getMessage());
            }

            try (FileInputStream inputStream = new FileInputStream(coverImage)){
                ObjectMetaRequestOptions metadata = new ObjectMetaRequestOptions();
                String contentType = "image/jpg";
                metadata.setContentType(contentType);
                String s = VolcOSSUploadUtil.uploadDataOSS(bucket, "cover/" + cover_signature + ".jpg", inputStream, metadata);
                JSONObject jsonObject = JSONObject.parseObject(s);
                if ("200".equals(jsonObject.getString("errCode"))) {
                    cover_url = url + "cover/"+ cover_signature + ".jpg";
                }else {
                    return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+jsonObject.getString("errMsg"));
                }
            }catch (Exception e) {
                log.error("上传封面图失败={}",e.getMessage(),e);
                return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+e.getMessage());
            }

            //生成封面对象
            AutoCoverVo vo = new AutoCoverVo();
            vo.setCover_signature(cover_signature);
            vo.setCover_url(cover_url);

            return ReturnJson.success(vo);
        }catch (Exception e) {
            return ReturnJson.toErrorJson("生成封面出现异常:"+e.getMessage());
        }finally {
            if (video != null) {
                video.delete();
            }
            if (coverImage != null) {
                coverImage.delete();
            }
        }

    }

    @Override
    public String getAutoCoverOss(AutoCoverParam param) {
        //临时文件
        String path = "temp"+System.currentTimeMillis();
        String coverName = path+"_cover.jpg";
        File coverImage = new File(coverName);

        try {
            if (BlankUtils.checkBlank(param.getVideo_url()) || !param.getVideo_url().contains("mp4")) {
                return ReturnJson.toErrorJson("非正确的视频地址");
            }

            //当前条件素材规则
            List<VivoMaterialRule> rules = creativeMaterialMapper.selectVivoMaterialRule(param);
            if (rules == null || rules.size() == 0) {
                return ReturnJson.toErrorJson("无法找到该素材的规则");
            }
            VivoMaterialRule rule = rules.get(0);

            //判断当前条件是否需要封面
            if (rule.getCover_size() == null) {
                return ReturnJson.toErrorJson("当前创意类型下素材无需生成封面");
            }

            //oss参数
            String oss_img_url = param.getVideo_url()+"?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0";

            //限速获取数据
            byte[] data = HttpLimitUtil.download(oss_img_url, 5);

            //下载视频到本地
            try (FileOutputStream fos = new FileOutputStream(coverImage)){
                fos.write(data);
            } catch (IOException e) {
                log.error("读取视频文件地址失败,原因={}",e.getMessage(),e);
                return ReturnJson.toErrorJson("读取视频文件地址失败,原因="+e.getMessage());
            }

            //生成封面
            coverImage = VideoUtil.thumbnailsImage(coverImage, StringUtil.getDouble(rule.getCover_size()));
            if (coverImage == null) {
                return ReturnJson.toErrorJson("生成封面失败,请采用其他方式");
            }

            //校验封面大小
            long size = coverImage.length()/1024;
            Integer cover_size = rule.getCover_size();
            if (cover_size < size) {
                return ReturnJson.toErrorJson("生成封面大小="+size+"KB超上限,,请采用其他方式");
            }

            //校验封面
            int width = 0;
            int height = 0;
            try {
                BufferedImage image = ImageIO.read(coverImage);
                width = image.getWidth();
                height = image.getHeight();
            }catch (Exception e) {
                return ReturnJson.toErrorJson("生成封面格式不正确,请采用其他方式");
            }
            if (width != rule.getCover_wide() || height != rule.getCover_height()) {
                return ReturnJson.toErrorJson("生成封面不兼容尺寸,请采用其他方式");
            }

            //上传封面
            String cover_url = "";
            String cover_signature = "";

            //生成md5
            try (FileInputStream inputStream = new FileInputStream(coverImage)){
                cover_signature = DigestUtils.md5Hex(inputStream);
            }catch (Exception e) {
                log.error("上传封面图成功={},获取md5失败",e.getMessage(),e);
                return ReturnJson.toErrorJson("上传封面图成功={},获取md5失败,无法生成md5的原因={}"+e.getMessage());
            }

            try (FileInputStream inputStream = new FileInputStream(coverImage)){
                ObjectMetaRequestOptions metadata = new ObjectMetaRequestOptions();
                String contentType = "image/jpg";
                metadata.setContentType(contentType);
                String s = VolcOSSUploadUtil.uploadDataOSS(bucket, "cover/" + cover_signature + ".jpg", inputStream, metadata);
                JSONObject jsonObject = JSONObject.parseObject(s);
                if ("200".equals(jsonObject.getString("errCode"))) {
                    cover_url = url + "cover/"+ cover_signature + ".jpg";
                }else {
                    return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+jsonObject.getString("errMsg"));
                }
            }catch (Exception e) {
                log.error("上传封面图失败={}",e.getMessage(),e);
                return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+e.getMessage());
            }

            //生成封面对象
            AutoCoverVo vo = new AutoCoverVo();
            vo.setCover_signature(cover_signature);
            vo.setCover_url(cover_url);

            return ReturnJson.success(vo);
        }catch (Exception e) {
            return ReturnJson.toErrorJson("生成封面出现异常:"+e.getMessage());
        }finally {
            if (coverImage != null) {
                coverImage.delete();
            }
        }

    }

    @Override
    public List<VivoMaterialRule> getCreativeMaterialRule(AutoCoverParam param) {
        return creativeMaterialMapper.selectVivoMaterialRule(param);
    }

    @Override
    public String getLoadCover(MultipartFile file, AutoCoverParam param) {
        //判断当前是否图片
        if (!file.getContentType().startsWith("image")) {
            return ReturnJson.toErrorJson("预览图必须为图片类型");
        }

        //当前条件素材规则
        List<VivoMaterialRule> rules = creativeMaterialMapper.selectVivoMaterialRule(param);
        if (rules == null || rules.size() == 0) {
            return ReturnJson.toErrorJson("无法找到该素材的规则");
        }
        VivoMaterialRule rule = rules.get(0);

        //判断当前条件是否需要封面
        if (rule.getCover_size() == null) {
            return ReturnJson.toErrorJson("当前创意类型下素材无需生成预览图");
        }

        //校验格式
        String fileName = file.getOriginalFilename();
        if (fileName == null || !(fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") || fileName.endsWith(".png"))) {
            return ReturnJson.toErrorJson("图片格式不正确,预览图必须为jpg,jpeg,png格式");
        }

        //校验尺寸
        int width = 0;
        int height = 0;
        try (InputStream is = file.getInputStream()) {
            BufferedImage image = ImageIO.read(is);
            width = image.getWidth();
            height = image.getHeight();
        } catch (IOException e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("上传失败,无法识别尺寸");
        }
        if (width != rule.getCover_wide() || height != rule.getCover_height()) {
            return ReturnJson.toErrorJson("上传图片不兼容尺寸,请更换尺寸为:宽="+width+",高="+height);
        }

        //校验大小
        long fileSizeInBytes = file.getSize();
        long fileSizeInKB = fileSizeInBytes / 1024;
        if (fileSizeInKB > rule.getCover_size()) {
            return ReturnJson.toErrorJson("上传图片超出大小限制,请更换大小为"+rule.getSize()+"以下");
        }

        //上传封面
        String cover_url = "";
        String cover_signature = "";

        //生成md5
        try (InputStream inputStream = file.getInputStream()){
            cover_signature = DigestUtils.md5Hex(inputStream);
        }catch (Exception e) {
            log.error("上传封面图成功={},获取md5失败",e.getMessage(),e);
            return ReturnJson.toErrorJson("上传封面图成功={},获取md5失败,无法生成md5的原因={}"+e.getMessage());
        }

        try (InputStream inputStream = file.getInputStream()){
            ObjectMetaRequestOptions metadata = new ObjectMetaRequestOptions();
            String contentType = "image/jpg";
            metadata.setContentType(contentType);
            String s = VolcOSSUploadUtil.uploadDataOSS(bucket, "cover/" + cover_signature + ".jpg", inputStream, metadata);
            JSONObject jsonObject = JSONObject.parseObject(s);
            if ("200".equals(jsonObject.getString("errCode"))) {
                cover_url = url + "cover/"+ cover_signature + ".jpg";
            }else {
                return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+jsonObject.getString("errMsg"));
            }
        }catch (Exception e) {
            log.error("上传封面图失败={}",e.getMessage(),e);
            return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+e.getMessage());
        }

        //生成封面对象
        AutoCoverVo vo = new AutoCoverVo();
        vo.setCover_signature(cover_signature);
        vo.setCover_url(cover_url);

        return ReturnJson.success(vo);
    }

    @Override
    public List<OppoMaterialRule> getOppoCreativeMaterialRule(OppoMaterialParam param) {
        return creativeMaterialMapper.selectOppoMaterialRule(param);
    }

    @Override
    public String getOppoAutoCover(OppoMaterialParam param) {
        //临时文件
        String path = "temp"+System.currentTimeMillis();
        File video = new File(path+".mp4");
        String coverName = path+"_cover.jpg";
        File coverImage = null;

        try {
            if (BlankUtils.checkBlank(param.getVideo_url()) || !param.getVideo_url().contains("mp4")) {
                return ReturnJson.toErrorJson("非正确的视频地址");
            }

            //当前条件素材规则
            List<OppoMaterialRule> rules = creativeMaterialMapper.selectOppoMaterialRule(param);
            if (rules == null || rules.size() == 0) {
                return ReturnJson.toErrorJson("无法找到该素材的规则");
            }
            OppoMaterialRule rule = rules.get(0);

            //判断当前条件是否需要封面
            if (rule.getCover_size() == null) {
                return ReturnJson.toErrorJson("当前创意类型下素材无需生成封面");
            }

            //限速获取数据
            byte[] data = HttpLimitUtil.download(param.getVideo_url(), 5);

            //下载视频到本地
            try (FileOutputStream fos = new FileOutputStream(video)){
                fos.write(data);
            } catch (IOException e) {
                log.error("读取视频文件地址失败,原因={}",e.getMessage(),e);
                return ReturnJson.toErrorJson("读取视频文件地址失败,原因="+e.getMessage());
            }

            //生成封面
            coverImage = VideoUtil.getCoverImage(video, coverName, StringUtil.getDouble(rule.getCover_size()));
            if (coverImage == null) {
                return ReturnJson.toErrorJson("生成封面失败,请采用其他方式");
            }

            //校验封面大小
            long size = coverImage.length()/1024;
            Integer cover_size = rule.getCover_size();
            if (cover_size < size) {
                return ReturnJson.toErrorJson("生成封面大小="+size+"KB超上限,,请采用其他方式");
            }

            //校验封面
            int width = 0;
            int height = 0;
            try {
                BufferedImage image = ImageIO.read(coverImage);
                width = image.getWidth();
                height = image.getHeight();
            }catch (Exception e) {
                return ReturnJson.toErrorJson("生成封面格式不正确,请采用其他方式");
            }
            if (width != rule.getCover_wide() || height != rule.getCover_height()) {
                return ReturnJson.toErrorJson("生成封面不兼容尺寸,请采用其他方式");
            }



            //上传封面
            String cover_url = "";
            String cover_signature = "";

            //生成md5
            try (FileInputStream inputStream = new FileInputStream(coverImage)){
                cover_signature = DigestUtils.md5Hex(inputStream);
            }catch (Exception e) {
                log.error("上传封面图成功={},获取md5失败",e.getMessage(),e);
                return ReturnJson.toErrorJson("上传封面图成功={},获取md5失败,无法生成md5的原因={}"+e.getMessage());
            }

            try (FileInputStream inputStream = new FileInputStream(coverImage)){
                ObjectMetaRequestOptions metadata = new ObjectMetaRequestOptions();
                String contentType = "image/jpg";
                metadata.setContentType(contentType);
                String s = VolcOSSUploadUtil.uploadDataOSS(bucket, "cover/" + cover_signature + ".jpg", inputStream, metadata);
                JSONObject jsonObject = JSONObject.parseObject(s);
                if ("200".equals(jsonObject.getString("errCode"))) {
                    cover_url = url + "cover/"+ cover_signature + ".jpg";
                }else {
                    return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+jsonObject.getString("errMsg"));
                }
            }catch (Exception e) {
                log.error("上传封面图失败={}",e.getMessage(),e);
                return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+e.getMessage());
            }

            //生成封面对象
            AutoCoverVo vo = new AutoCoverVo();
            vo.setCover_signature(cover_signature);
            vo.setCover_url(cover_url);

            return ReturnJson.success(vo);
        }catch (Exception e) {
            return ReturnJson.toErrorJson("生成封面出现异常:"+e.getMessage());
        }finally {
            if (video != null) {
                video.delete();
            }
            if (coverImage != null) {
                coverImage.delete();
            }
        }
    }

    @Override
    public String getOppoAutoCoverOss(OppoMaterialParam param) {
        //临时文件
        String path = "temp"+System.currentTimeMillis();
        String coverName = path+"_cover.jpg";
        File coverImage = new File(coverName);
        try {
            if (BlankUtils.checkBlank(param.getVideo_url()) || !param.getVideo_url().contains("mp4")) {
                return ReturnJson.toErrorJson("非正确的视频地址");
            }

            //当前条件素材规则
            List<OppoMaterialRule> rules = creativeMaterialMapper.selectOppoMaterialRule(param);
            if (rules == null || rules.size() == 0) {
                return ReturnJson.toErrorJson("无法找到该素材的规则");
            }
            OppoMaterialRule rule = rules.get(0);

            //判断当前条件是否需要封面
            if (rule.getCover_size() == null) {
                return ReturnJson.toErrorJson("当前创意类型下素材无需生成封面");
            }

            //oss参数
            String oss_img_url = param.getVideo_url()+"?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0";

            //限速获取数据
            byte[] data = HttpLimitUtil.download(oss_img_url, 5);

            //下载视频到本地
            try (FileOutputStream fos = new FileOutputStream(coverImage)){
                fos.write(data);
            } catch (IOException e) {
                log.error("读取图片文件地址失败,原因={}",e.getMessage(),e);
                return ReturnJson.toErrorJson("读取图片文件地址失败,原因="+e.getMessage());
            }

            //生成封面
            coverImage = VideoUtil.thumbnailsImage(coverImage, StringUtil.getDouble(rule.getCover_size()));
            if (coverImage == null) {
                return ReturnJson.toErrorJson("生成封面失败,请采用其他方式");
            }

            //校验封面大小
            long size = coverImage.length()/1024;
            Integer cover_size = rule.getCover_size();
            if (cover_size < size) {
                return ReturnJson.toErrorJson("生成封面大小="+size+"KB超上限,,请采用其他方式");
            }

            //校验封面
            int width = 0;
            int height = 0;
            try {
                BufferedImage image = ImageIO.read(coverImage);
                width = image.getWidth();
                height = image.getHeight();
            }catch (Exception e) {
                return ReturnJson.toErrorJson("生成封面格式不正确,请采用其他方式");
            }
            if (width != rule.getCover_wide() || height != rule.getCover_height()) {
                return ReturnJson.toErrorJson("生成封面不兼容尺寸,请采用其他方式");
            }



            //上传封面
            String cover_url = "";
            String cover_signature = "";

            //生成md5
            try (FileInputStream inputStream = new FileInputStream(coverImage)){
                cover_signature = DigestUtils.md5Hex(inputStream);
            }catch (Exception e) {
                log.error("上传封面图成功={},获取md5失败",e.getMessage(),e);
                return ReturnJson.toErrorJson("上传封面图成功={},获取md5失败,无法生成md5的原因={}"+e.getMessage());
            }

            try (FileInputStream inputStream = new FileInputStream(coverImage)){
                ObjectMetaRequestOptions metadata = new ObjectMetaRequestOptions();
                String contentType = "image/jpg";
                metadata.setContentType(contentType);
                String s = VolcOSSUploadUtil.uploadDataOSS(bucket, "cover/" + cover_signature + ".jpg", inputStream, metadata);
                JSONObject jsonObject = JSONObject.parseObject(s);
                if ("200".equals(jsonObject.getString("errCode"))) {
                    cover_url = url + "cover/"+ cover_signature + ".jpg";
                }else {
                    return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+jsonObject.getString("errMsg"));
                }
            }catch (Exception e) {
                log.error("上传封面图失败={}",e.getMessage(),e);
                return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+e.getMessage());
            }

            //生成封面对象
            AutoCoverVo vo = new AutoCoverVo();
            vo.setCover_signature(cover_signature);
            vo.setCover_url(cover_url);

            return ReturnJson.success(vo);
        }catch (Exception e) {
            return ReturnJson.toErrorJson("生成封面出现异常:"+e.getMessage());
        }finally {
            if (coverImage != null) {
                coverImage.delete();
            }
        }
    }


    @Override
    public String getOppoLoadCover(MultipartFile file, OppoMaterialParam param) {
        //判断当前是否图片
        if (!file.getContentType().startsWith("image")) {
            return ReturnJson.toErrorJson("预览图必须为图片类型");
        }

        //当前条件素材规则
        List<OppoMaterialRule> rules = creativeMaterialMapper.selectOppoMaterialRule(param);
        if (rules == null || rules.size() == 0) {
            return ReturnJson.toErrorJson("无法找到该素材的规则");
        }
        OppoMaterialRule rule = rules.get(0);

        //判断当前条件是否需要封面
        if (rule.getCover_size() == null) {
            return ReturnJson.toErrorJson("当前创意类型下素材无需生成预览图");
        }

        //校验格式
        String fileName = file.getOriginalFilename();
        if (fileName == null || !(fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") || fileName.endsWith(".png"))) {
            return ReturnJson.toErrorJson("图片格式不正确,预览图必须为jpg,jpeg,png格式");
        }

        //校验尺寸
        int width = 0;
        int height = 0;
        try (InputStream is = file.getInputStream()) {
            BufferedImage image = ImageIO.read(is);
            width = image.getWidth();
            height = image.getHeight();
        } catch (IOException e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("上传失败,无法识别尺寸");
        }
        if (width != rule.getCover_wide() || height != rule.getCover_height()) {
            return ReturnJson.toErrorJson("上传图片不兼容尺寸,请更换尺寸为:宽="+width+",高="+height);
        }

        //校验大小
        long fileSizeInBytes = file.getSize();
        long fileSizeInKB = fileSizeInBytes / 1024;
        if (fileSizeInKB > rule.getCover_size()) {
            return ReturnJson.toErrorJson("上传图片超出大小限制,请更换大小为"+rule.getSize()+"以下");
        }

        //上传封面
        String cover_url = "";
        String cover_signature = "";

        //生成md5
        try (InputStream inputStream = file.getInputStream()){
            cover_signature = DigestUtils.md5Hex(inputStream);
        }catch (Exception e) {
            log.error("上传封面图成功={},获取md5失败",e.getMessage(),e);
            return ReturnJson.toErrorJson("上传封面图成功={},获取md5失败,无法生成md5的原因={}"+e.getMessage());
        }

        try (InputStream inputStream = file.getInputStream()){
            ObjectMetaRequestOptions metadata = new ObjectMetaRequestOptions();
            String contentType = "image/jpg";
            metadata.setContentType(contentType);
            String s = VolcOSSUploadUtil.uploadDataOSS(bucket, "cover/" + cover_signature + ".jpg", inputStream, metadata);
            JSONObject jsonObject = JSONObject.parseObject(s);
            if ("200".equals(jsonObject.getString("errCode"))) {
                cover_url = url + "cover/"+ cover_signature + ".jpg";
            }else {
                return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+jsonObject.getString("errMsg"));
            }
        }catch (Exception e) {
            log.error("上传封面图失败={}",e.getMessage(),e);
            return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+e.getMessage());
        }

        //生成封面对象
        AutoCoverVo vo = new AutoCoverVo();
        vo.setCover_signature(cover_signature);
        vo.setCover_url(cover_url);

        return ReturnJson.success(vo);
    }

    @Override
    public String getOppoLoadLogo(MultipartFile file, OppoMaterialParam param) {
        //判断当前是否图片
        if (!file.getContentType().startsWith("image")) {
            return ReturnJson.toErrorJson("预览图必须为图片类型");
        }

        //当前条件素材规则
        List<OppoMaterialRule> rules = creativeMaterialMapper.selectOppoMaterialRule(param);
        if (rules == null || rules.size() == 0) {
            return ReturnJson.toErrorJson("无法找到该素材的规则");
        }
        OppoMaterialRule rule = rules.get(0);

        //判断当前条件是否需要封面
        if (rule.getLogo_size() == null) {
            return ReturnJson.toErrorJson("当前创意类型下素材无需生成logo");
        }

        //校验格式
        String fileName = file.getOriginalFilename();
        if (fileName == null || !(fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") || fileName.endsWith(".png"))) {
            return ReturnJson.toErrorJson("图片格式不正确,预览图必须为jpg,jpeg,png格式");
        }

        //校验尺寸
        int width = 0;
        int height = 0;
        try (InputStream is = file.getInputStream()) {
            BufferedImage image = ImageIO.read(is);
            width = image.getWidth();
            height = image.getHeight();
        } catch (IOException e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("上传失败,无法识别尺寸");
        }
        if (width != rule.getLogo_wide() || height != rule.getLogo_height()) {
            return ReturnJson.toErrorJson("上传图片不兼容尺寸,请更换尺寸为:宽="+width+",高="+height);
        }

        //校验大小
        long fileSizeInBytes = file.getSize();
        long fileSizeInKB = fileSizeInBytes / 1024;
        if (fileSizeInKB > rule.getLogo_size()) {
            return ReturnJson.toErrorJson("上传图片超出大小限制,请更换大小为"+rule.getSize()+"以下");
        }

        //上传封面
        String logo_url = "";
        String logo_signature = "";

        //生成md5
        try (InputStream inputStream = file.getInputStream()){
            logo_signature = DigestUtils.md5Hex(inputStream);
        }catch (Exception e) {
            log.error("上传logo成功={},获取md5失败",e.getMessage(),e);
            return ReturnJson.toErrorJson("上传logo成功={},获取md5失败,无法生成md5的原因={}"+e.getMessage());
        }

        try (InputStream inputStream = file.getInputStream()){
            ObjectMetaRequestOptions metadata = new ObjectMetaRequestOptions();
            String contentType = "image/jpg";
            metadata.setContentType(contentType);
            String s = VolcOSSUploadUtil.uploadDataOSS(bucket, "cover/" + logo_signature + ".jpg", inputStream, metadata);
            JSONObject jsonObject = JSONObject.parseObject(s);
            if ("200".equals(jsonObject.getString("errCode"))) {
                logo_url = url + "cover/"+ logo_signature + ".jpg";
            }else {
                return ReturnJson.toErrorJson("生成logo失败,无法上传原因={}"+jsonObject.getString("errMsg"));
            }
        }catch (Exception e) {
            log.error("上传logo失败={}",e.getMessage(),e);
            return ReturnJson.toErrorJson("生成logo失败,无法上传原因={}"+e.getMessage());
        }

        //生成封面对象
        AutoCoverVo vo = new AutoCoverVo();
        vo.setCover_signature(logo_signature);
        vo.setCover_url(logo_url);

        return ReturnJson.success(vo);
    }

    @Override
    public List<HonorMaterialRule> getHonorCreativeMaterialRule(HonorMaterialParam param) {
        return creativeMaterialMapper.selectHonorMaterialRule(param);
    }

    @Override
    public String getHonorAutoCover(HonorMaterialParam param) {
        //临时文件
        String path = "temp"+System.currentTimeMillis();
        File video = new File(path+".mp4");
        String coverName = path+"_cover.jpg";
        File coverImage = null;

        try {
            if (BlankUtils.checkBlank(param.getVideo_url()) || !param.getVideo_url().contains("mp4")) {
                return ReturnJson.toErrorJson("非正确的视频地址");
            }

            //当前条件素材规则
            List<HonorMaterialRule> rules = creativeMaterialMapper.selectHonorMaterialRule(param);
            if (rules == null || rules.size() == 0) {
                return ReturnJson.toErrorJson("无法找到该素材的规则");
            }
            HonorMaterialRule rule = rules.get(0);

            //判断当前条件是否需要封面
            if (rule.getCover_size() == null) {
                return ReturnJson.toErrorJson("当前创意类型下素材无需生成封面");
            }

            //限速获取数据
            byte[] data = HttpLimitUtil.download(param.getVideo_url(), 5);

            //下载视频到本地
            try (FileOutputStream fos = new FileOutputStream(video)){
                fos.write(data);
            } catch (IOException e) {
                log.error("读取视频文件地址失败,原因={}",e.getMessage(),e);
                return ReturnJson.toErrorJson("读取视频文件地址失败,原因="+e.getMessage());
            }

            //生成封面
            coverImage = VideoUtil.getCoverImage(video, coverName, StringUtil.getDouble(rule.getCover_size()));
            if (coverImage == null) {
                return ReturnJson.toErrorJson("生成封面失败,请采用其他方式");
            }

            //校验封面大小
            long size = coverImage.length()/1024;
            Integer cover_size = rule.getCover_size();
            if (cover_size < size) {
                return ReturnJson.toErrorJson("生成封面大小="+size+"KB超上限,,请采用其他方式");
            }

            //校验封面
            int width = 0;
            int height = 0;
            try {
                BufferedImage image = ImageIO.read(coverImage);
                width = image.getWidth();
                height = image.getHeight();
            }catch (Exception e) {
                return ReturnJson.toErrorJson("生成封面格式不正确,请采用其他方式");
            }
            if (width != rule.getCover_wide() || height != rule.getCover_height()) {
                return ReturnJson.toErrorJson("生成封面不兼容尺寸,请采用其他方式");
            }



            //上传封面
            String cover_url = "";
            String cover_signature = "";

            //生成md5
            try (FileInputStream inputStream = new FileInputStream(coverImage)){
                cover_signature = DigestUtils.md5Hex(inputStream);
            }catch (Exception e) {
                log.error("上传封面图成功={},获取md5失败",e.getMessage(),e);
                return ReturnJson.toErrorJson("上传封面图成功={},获取md5失败,无法生成md5的原因={}"+e.getMessage());
            }

            try (FileInputStream inputStream = new FileInputStream(coverImage)){
                ObjectMetaRequestOptions metadata = new ObjectMetaRequestOptions();
                String contentType = "image/jpg";
                metadata.setContentType(contentType);
                String s = VolcOSSUploadUtil.uploadDataOSS(bucket, "cover/" + cover_signature + ".jpg", inputStream, metadata);
                JSONObject jsonObject = JSONObject.parseObject(s);
                if ("200".equals(jsonObject.getString("errCode"))) {
                    cover_url = url + "cover/"+ cover_signature + ".jpg";
                }else {
                    return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+jsonObject.getString("errMsg"));
                }
            }catch (Exception e) {
                log.error("上传封面图失败={}",e.getMessage(),e);
                return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+e.getMessage());
            }

            //生成封面对象
            AutoCoverVo vo = new AutoCoverVo();
            vo.setCover_signature(cover_signature);
            vo.setCover_url(cover_url);

            return ReturnJson.success(vo);
        }catch (Exception e) {
            return ReturnJson.toErrorJson("生成封面出现异常:"+e.getMessage());
        }finally {
            if (video != null) {
                video.delete();
            }
            if (coverImage != null) {
                coverImage.delete();
            }
        }
    }

    @Override
    public String getHonorAutoCoverOss(HonorMaterialParam param) {
        //临时文件
        String path = "temp"+System.currentTimeMillis();
        String coverName = path+"_cover.jpg";
        File coverImage = new File(coverName);

        try {
            if (BlankUtils.checkBlank(param.getVideo_url()) || !param.getVideo_url().contains("mp4")) {
                return ReturnJson.toErrorJson("非正确的视频地址");
            }

            //当前条件素材规则
            List<HonorMaterialRule> rules = creativeMaterialMapper.selectHonorMaterialRule(param);
            if (rules == null || rules.size() == 0) {
                return ReturnJson.toErrorJson("无法找到该素材的规则");
            }
            HonorMaterialRule rule = rules.get(0);

            //判断当前条件是否需要封面
            if (rule.getCover_size() == null) {
                return ReturnJson.toErrorJson("当前创意类型下素材无需生成封面");
            }

            //oss参数
            String oss_img_url = param.getVideo_url()+"?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0";

            //限速获取数据
            byte[] data = HttpLimitUtil.download(oss_img_url, 5);

            //下载视频到本地
            try (FileOutputStream fos = new FileOutputStream(coverImage)){
                fos.write(data);
            } catch (IOException e) {
                log.error("读取视频文件地址失败,原因={}",e.getMessage(),e);
                return ReturnJson.toErrorJson("读取视频文件地址失败,原因="+e.getMessage());
            }

            //生成封面
            coverImage = VideoUtil.thumbnailsImage(coverImage, StringUtil.getDouble(rule.getCover_size()));
            if (coverImage == null) {
                return ReturnJson.toErrorJson("生成封面失败,请采用其他方式");
            }

            //校验封面大小
            long size = coverImage.length()/1024;
            Integer cover_size = rule.getCover_size();
            if (cover_size < size) {
                return ReturnJson.toErrorJson("生成封面大小="+size+"KB超上限,,请采用其他方式");
            }

            //校验封面
            int width = 0;
            int height = 0;
            try {
                BufferedImage image = ImageIO.read(coverImage);
                width = image.getWidth();
                height = image.getHeight();
            }catch (Exception e) {
                return ReturnJson.toErrorJson("生成封面格式不正确,请采用其他方式");
            }
            if (width != rule.getCover_wide() || height != rule.getCover_height()) {
                return ReturnJson.toErrorJson("生成封面不兼容尺寸,请采用其他方式");
            }



            //上传封面
            String cover_url = "";
            String cover_signature = "";

            //生成md5
            try (FileInputStream inputStream = new FileInputStream(coverImage)){
                cover_signature = DigestUtils.md5Hex(inputStream);
            }catch (Exception e) {
                log.error("上传封面图成功={},获取md5失败",e.getMessage(),e);
                return ReturnJson.toErrorJson("上传封面图成功={},获取md5失败,无法生成md5的原因={}"+e.getMessage());
            }

            try (FileInputStream inputStream = new FileInputStream(coverImage)){
                ObjectMetaRequestOptions metadata = new ObjectMetaRequestOptions();
                String contentType = "image/jpg";
                metadata.setContentType(contentType);
                String s = VolcOSSUploadUtil.uploadDataOSS(bucket, "cover/" + cover_signature + ".jpg", inputStream, metadata);
                JSONObject jsonObject = JSONObject.parseObject(s);
                if ("200".equals(jsonObject.getString("errCode"))) {
                    cover_url = url + "cover/"+ cover_signature + ".jpg";
                }else {
                    return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+jsonObject.getString("errMsg"));
                }
            }catch (Exception e) {
                log.error("上传封面图失败={}",e.getMessage(),e);
                return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+e.getMessage());
            }

            //生成封面对象
            AutoCoverVo vo = new AutoCoverVo();
            vo.setCover_signature(cover_signature);
            vo.setCover_url(cover_url);

            return ReturnJson.success(vo);
        }catch (Exception e) {
            return ReturnJson.toErrorJson("生成封面出现异常:"+e.getMessage());
        }finally {
            if (coverImage != null) {
                coverImage.delete();
            }
        }
    }

    @Override
    public String getHonorLoadCover(MultipartFile file, HonorMaterialParam param) {
        //判断当前是否图片
        if (!file.getContentType().startsWith("image")) {
            return ReturnJson.toErrorJson("预览图必须为图片类型");
        }

        //当前条件素材规则
        List<HonorMaterialRule> rules = creativeMaterialMapper.selectHonorMaterialRule(param);
        if (rules == null || rules.size() == 0) {
            return ReturnJson.toErrorJson("无法找到该素材的规则");
        }
        HonorMaterialRule rule = rules.get(0);

        //判断当前条件是否需要封面
        if (rule.getCover_size() == null) {
            return ReturnJson.toErrorJson("当前创意类型下素材无需生成预览图");
        }

        //校验格式
        String fileName = file.getOriginalFilename();
        if (fileName == null || !(fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") || fileName.endsWith(".png"))) {
            return ReturnJson.toErrorJson("图片格式不正确,预览图必须为jpg,jpeg,png格式");
        }

        //校验尺寸
        int width = 0;
        int height = 0;
        try (InputStream is = file.getInputStream()) {
            BufferedImage image = ImageIO.read(is);
            width = image.getWidth();
            height = image.getHeight();
        } catch (IOException e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("上传失败,无法识别尺寸");
        }
        if (width != rule.getCover_wide() || height != rule.getCover_height()) {
            return ReturnJson.toErrorJson("上传图片不兼容尺寸,请更换尺寸为:宽="+width+",高="+height);
        }

        //校验大小
        long fileSizeInBytes = file.getSize();
        long fileSizeInKB = fileSizeInBytes / 1024;
        if (fileSizeInKB > rule.getCover_size()) {
            return ReturnJson.toErrorJson("上传图片超出大小限制,请更换大小为"+rule.getSize()+"以下");
        }

        //上传封面
        String cover_url = "";
        String cover_signature = "";

        //生成md5
        try (InputStream inputStream = file.getInputStream()){
            cover_signature = DigestUtils.md5Hex(inputStream);
        }catch (Exception e) {
            log.error("上传封面图成功={},获取md5失败",e.getMessage(),e);
            return ReturnJson.toErrorJson("上传封面图成功={},获取md5失败,无法生成md5的原因={}"+e.getMessage());
        }

        try (InputStream inputStream = file.getInputStream()){
            ObjectMetaRequestOptions metadata = new ObjectMetaRequestOptions();
            String contentType = "image/jpg";
            metadata.setContentType(contentType);
            String s = VolcOSSUploadUtil.uploadDataOSS(bucket, "cover/" + cover_signature + ".jpg", inputStream, metadata);
            JSONObject jsonObject = JSONObject.parseObject(s);
            if ("200".equals(jsonObject.getString("errCode"))) {
                cover_url = url + "cover/"+ cover_signature + ".jpg";
            }else {
                return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+jsonObject.getString("errMsg"));
            }
        }catch (Exception e) {
            log.error("上传封面图失败={}",e.getMessage(),e);
            return ReturnJson.toErrorJson("生成封面失败,无法上传原因={}"+e.getMessage());
        }

        //生成封面对象
        AutoCoverVo vo = new AutoCoverVo();
        vo.setCover_signature(cover_signature);
        vo.setCover_url(cover_url);

        return ReturnJson.success(vo);
    }

    @Override
    public String getHonorLoadLogo(MultipartFile file, HonorMaterialParam param) {
        //判断当前是否图片
        if (!file.getContentType().startsWith("image")) {
            return ReturnJson.toErrorJson("预览图必须为图片类型");
        }

        //当前条件素材规则
        List<HonorMaterialRule> rules = creativeMaterialMapper.selectHonorMaterialRule(param);
        if (rules == null || rules.size() == 0) {
            return ReturnJson.toErrorJson("无法找到该素材的规则");
        }
        HonorMaterialRule rule = rules.get(0);

        //判断当前条件是否需要封面
        if (rule.getLogo_size() == null) {
            return ReturnJson.toErrorJson("当前创意类型下素材无需生成logo");
        }

        //校验格式
        String fileName = file.getOriginalFilename();
        if (fileName == null || !(fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") || fileName.endsWith(".png"))) {
            return ReturnJson.toErrorJson("图片格式不正确,预览图必须为jpg,jpeg,png格式");
        }

        //校验尺寸
        int width = 0;
        int height = 0;
        try (InputStream is = file.getInputStream()) {
            BufferedImage image = ImageIO.read(is);
            width = image.getWidth();
            height = image.getHeight();
        } catch (IOException e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("上传失败,无法识别尺寸");
        }
        if (width != rule.getLogo_wide() || height != rule.getLogo_height()) {
            return ReturnJson.toErrorJson("上传图片不兼容尺寸,请更换尺寸为:宽="+width+",高="+height);
        }

        //校验大小
        long fileSizeInBytes = file.getSize();
        long fileSizeInKB = fileSizeInBytes / 1024;
        if (fileSizeInKB > rule.getLogo_size()) {
            return ReturnJson.toErrorJson("上传图片超出大小限制,请更换大小为"+rule.getSize()+"以下");
        }

        //上传封面
        String logo_url = "";
        String logo_signature = "";

        //生成md5
        try (InputStream inputStream = file.getInputStream()){
            logo_signature = DigestUtils.md5Hex(inputStream);
        }catch (Exception e) {
            log.error("上传logo成功={},获取md5失败",e.getMessage(),e);
            return ReturnJson.toErrorJson("上传logo成功={},获取md5失败,无法生成md5的原因={}"+e.getMessage());
        }

        try (InputStream inputStream = file.getInputStream()){
            ObjectMetaRequestOptions metadata = new ObjectMetaRequestOptions();
            String contentType = "image/jpg";
            metadata.setContentType(contentType);
            String s = VolcOSSUploadUtil.uploadDataOSS(bucket, "cover/" + logo_signature + ".jpg", inputStream, metadata);
            JSONObject jsonObject = JSONObject.parseObject(s);
            if ("200".equals(jsonObject.getString("errCode"))) {
                logo_url = url + "cover/"+ logo_signature + ".jpg";
            }else {
                return ReturnJson.toErrorJson("生成logo失败,无法上传原因={}"+jsonObject.getString("errMsg"));
            }
        }catch (Exception e) {
            log.error("上传logo失败={}",e.getMessage(),e);
            return ReturnJson.toErrorJson("生成logo失败,无法上传原因={}"+e.getMessage());
        }

        //生成封面对象
        AutoCoverVo vo = new AutoCoverVo();
        vo.setCover_signature(logo_signature);
        vo.setCover_url(logo_url);

        return ReturnJson.success(vo);
    }
}
