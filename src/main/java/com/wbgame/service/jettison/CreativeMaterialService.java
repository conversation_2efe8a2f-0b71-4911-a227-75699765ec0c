package com.wbgame.service.jettison;

import com.wbgame.pojo.jettison.param.AutoCoverParam;
import com.wbgame.pojo.jettison.param.CreativeMaterialParam;
import com.wbgame.pojo.jettison.param.HonorMaterialParam;
import com.wbgame.pojo.jettison.param.OppoMaterialParam;
import com.wbgame.pojo.jettison.vo.CreativeMaterialVo;
import com.wbgame.pojo.jettison.vo.HonorMaterialRule;
import com.wbgame.pojo.jettison.vo.OppoMaterialRule;
import com.wbgame.pojo.jettison.vo.VivoMaterialRule;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/30 18:30
 */
public interface CreativeMaterialService {

    List<CreativeMaterialVo> selectCreativeMaterials(CreativeMaterialParam param);

    String getAutoCover(AutoCoverParam param);

    String getAutoCoverOss(AutoCoverParam param);

    List<VivoMaterialRule> getCreativeMaterialRule(AutoCoverParam param);

    String getLoadCover(MultipartFile file, AutoCoverParam param);

    List<OppoMaterialRule> getOppoCreativeMaterialRule(OppoMaterialParam param);

    String getOppoAutoCover(OppoMaterialParam param);

    String getOppoAutoCoverOss(OppoMaterialParam param);

    String getOppoLoadCover(MultipartFile file, OppoMaterialParam param);

    String getOppoLoadLogo(MultipartFile file, OppoMaterialParam param);

    List<HonorMaterialRule> getHonorCreativeMaterialRule(HonorMaterialParam param);

    String getHonorAutoCover(HonorMaterialParam param);

    String getHonorAutoCoverOss(HonorMaterialParam param);

    String getHonorLoadCover(MultipartFile file, HonorMaterialParam param);

    String getHonorLoadLogo(MultipartFile file, HonorMaterialParam param);
}
