package com.wbgame.service.jettison.report;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.wbgame.common.Asserts;
import com.wbgame.mapper.master.jettison.DnwxAdtMapper;
import com.wbgame.mapper.master.jettison.JettisonCommonMapper;
import com.wbgame.mapper.tfxt.AdtMapper;
import com.wbgame.mapper.tfxt.PlatformMapper;
import com.wbgame.pojo.jettison.KuaiShouDeveloper;
import com.wbgame.pojo.jettison.report.InfoResult;
import com.wbgame.pojo.jettison.report.account.*;
import com.wbgame.pojo.jettison.report.dto.AccountDTO;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExcelUtils;
import com.wbgame.utils.jettison.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 文件导入层
 * @Create 2023-02-28
 */

@Service
@Slf4j
public class FileService {

    @Autowired
    AdtMapper adtMapper;
    @Autowired
    DnwxAdtMapper dnwxAdtMapper;
    @Autowired
    JettisonCommonMapper jettisonCommonMapper;
    @Autowired
    PlatformMapper platformDao;

    public InfoResult bzhanAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        List<AccountDTO> accountList = Lists.newArrayList();

        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();
        List<ArrayList<String>> arrayLists = ExcelUtils.readExcel(multipartFile);
        for (int i = 0; i < arrayLists.size(); i++) {
            if (i == 0) {
                //第一行为标题，不能为空
                for (int k = 0; k < 2; k++) {
                    if (BlankUtils.checkBlank(arrayLists.get(i).get(k))) {
                        Asserts.fail("第一行标题不能出现空的情况");
                    }
                }
            } else {
                AccountDTO account = new AccountDTO();
                account.setAccount(arrayLists.get(i).get(0));
                account.setClientId(arrayLists.get(i).get(1));
                account.setClientSecret(arrayLists.get(i).get(2));
                Integer type = getType(arrayLists.get(i).get(3));
                account.setType(type);
                account.setBusiness(arrayLists.get(i).get(4));
                account.setFirst_agent(arrayLists.get(i).get(5));
                if (!agentNameList.contains(account.getFirst_agent())) {
                    infoResult.setRet(0);
                    infoResult.setMsg("查无此一级代理商：" + account.getFirst_agent());
                    return infoResult;
                }
                account.setAgent(arrayLists.get(i).get(6));
                if (!agentNameList.contains(account.getAgent())) {
                    infoResult.setRet(0);
                    infoResult.setMsg("查无此二级代理商：" + account.getAgent());
                    return infoResult;
                }
                account.setPutUser(arrayLists.get(i).get(7));
                account.setRebate(StringUtil.getDouble(arrayLists.get(i).get(8)));
                account.setRemark(arrayLists.get(i).get(9));
                account.setGroupId(1);
                account.setCreateUser(username);
                accountList.add(account);
            }
        }

        if (CollectionUtils.isEmpty(accountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }

        int length = 500;
        if (accountList.size() <= length) {
            adtMapper.addBzhanAccount(accountList);
        } else {
            List<List<AccountDTO>> lists = Lists.partition(accountList, length);
            for (int i = 0; i < lists.size(); i++) {
                adtMapper.addBzhanAccount(lists.get(i));
            }
        }
        for (AccountDTO account : accountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                adtMapper.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    public InfoResult weiboAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        List<AccountDTO> accountList = Lists.newArrayList();

        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }
        List<ArrayList<String>> arrayLists = ExcelUtils.readExcel(multipartFile);
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();

        for (int i = 0; i < arrayLists.size(); i++) {
            if (i == 0) {
                //第一行为标题，不能为空
                for (int k = 0; k < 2; k++) {
                    if (BlankUtils.checkBlank(arrayLists.get(i).get(k))) {
                        Asserts.fail("第一行标题不能出现空的情况");
                    }
                }
            } else {
                AccountDTO account = new AccountDTO();
                account.setAccount(arrayLists.get(i).get(0));
                account.setClientId(arrayLists.get(i).get(1));
                account.setClientSecret(arrayLists.get(i).get(2));
                Integer type = getType(arrayLists.get(i).get(3));
                account.setType(type);
                account.setBusiness(arrayLists.get(i).get(4));
                account.setFirst_agent(arrayLists.get(i).get(5));
                if (!agentNameList.contains(account.getFirst_agent())) {
                    infoResult.setRet(0);
                    infoResult.setMsg("查无此一级代理商：" + account.getFirst_agent());
                    return infoResult;
                }
                account.setAgent(arrayLists.get(i).get(6));
                if (!agentNameList.contains(account.getAgent())) {
                    infoResult.setRet(0);
                    infoResult.setMsg("查无此二级代理商：" + account.getAgent());
                    return infoResult;
                }
                account.setPutUser(arrayLists.get(i).get(7));
                account.setRebate(StringUtil.getDouble(arrayLists.get(i).get(8)));
                account.setRemark(arrayLists.get(i).get(9));
                account.setAccountName(arrayLists.get(i).get(10));
                account.setGroupId(1);
                account.setCreateUser(username);
                account.setMedia("微博");
                accountList.add(account);
            }
        }

        if (CollectionUtils.isEmpty(accountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }

        int length = 500;
        if (accountList.size() <= length) {
            adtMapper.addWeiboAccount(accountList);
        } else {
            List<List<AccountDTO>> lists = Lists.partition(accountList, length);
            for (int i = 0; i < lists.size(); i++) {
                adtMapper.addWeiboAccount(lists.get(i));
            }
        }
        for (AccountDTO account : accountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                adtMapper.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    public InfoResult tttAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        List<AccountDTO> accountList = Lists.newArrayList();

        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }
        List<ArrayList<String>> arrayLists = ExcelUtils.readExcel(multipartFile);
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();
        List<Map> strategyList = dnwxAdtMapper.getStrategy();
        Map<String, String> strategyMap = strategyList.stream().
                collect(Collectors.toMap(map -> StringUtil.getString(map.get("strategyName")), map -> StringUtil.getString(map.get("id")), (k1, k2) -> k2));
        for (int i = 0; i < arrayLists.size(); i++) {
            if (i == 0) {
                //第一行为标题，不能为空
                for (int k = 0; k < 2; k++) {
                    if (BlankUtils.checkBlank(arrayLists.get(i).get(k))) {
                        Asserts.fail("第一行标题不能出现空的情况");
                    }
                }
            } else {
                AccountDTO account = new AccountDTO();
                //账号
                account.setAccount(arrayLists.get(i).get(1));
                account.setCreateUser(username);
                //没有父账号为管家账号,只需要秘钥
                if (!BlankUtils.checkBlank(arrayLists.get(i).get(0))) {
                    account.setParentAccount(arrayLists.get(i).get(0));
                    account.setAccountName(arrayLists.get(i).get(2));
                    account.setMedia(arrayLists.get(i).get(3));
                    Integer type = getType(arrayLists.get(i).get(4));
                    account.setType(type);
                    account.setFirst_agent(arrayLists.get(i).get(5));
                    if (!agentNameList.contains(account.getFirst_agent())) {
                        infoResult.setRet(0);
                        infoResult.setMsg("查无此一级代理商：" + account.getFirst_agent());
                        return infoResult;
                    }
                    account.setAgent(arrayLists.get(i).get(6));
                    if (!agentNameList.contains(account.getAgent())) {
                        infoResult.setRet(0);
                        infoResult.setMsg("查无此二级代理商：" + account.getAgent());
                        return infoResult;
                    }
                    account.setPutUser(arrayLists.get(i).get(7));
                    account.setRebate(StringUtil.getDouble(arrayLists.get(i).get(8)));
                    account.setRemark(arrayLists.get(i).get(9));
                    account.setBusiness(arrayLists.get(i).get(10));
                    account.setStrategy(strategyMap.get(StringUtil.getString(arrayLists.get(i).get(11))));
                    account.setAppId(arrayLists.get(i).get(12));
                    account.setArtist(arrayLists.get(i).get(13));
                    account.setGroupId(1);
                } else {
                    account.setClientId(arrayLists.get(i).get(14));
                    account.setClientSecret(arrayLists.get(i).get(15));
                }
                accountList.add(account);
            }
        }

        if (CollectionUtils.isEmpty(accountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }

        int length = 500;
        if (accountList.size() <= length) {
            dnwxAdtMapper.addTTTAccount(accountList);
        } else {
            List<List<AccountDTO>> lists = Lists.partition(accountList, length);
            for (int i = 0; i < lists.size(); i++) {
                dnwxAdtMapper.addTTTAccount(lists.get(i));
            }
        }
        for (AccountDTO account : accountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                adtMapper.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    public InfoResult toutiaoAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());

        Sheet sheet = xssfWorkbook.getSheetAt(0);
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        List<ToutiaoAccount> toutiaoAccountList = Lists.newArrayList();
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();
        List<Map> strategyList = dnwxAdtMapper.getStrategy();
        Map<String, String> strategyMap = strategyList.stream().
                collect(Collectors.toMap(map -> StringUtil.getString(map.get("strategyName")), map -> StringUtil.getString(map.get("id")), (k1, k2) -> k2));

        for (int i = 1; i < physicalNumberOfRows; i++) {
            Row row = sheet.getRow(i);
            if (null == row
                    || null == row.getCell(0)
                    || Strings.isNullOrEmpty(row.getCell(0).toString())) {
                continue;
            }
            int physicalNumberOfCells = row.getPhysicalNumberOfCells();

            ToutiaoAccount toutiaoAccount = new ToutiaoAccount();
            for (int j = 0; j < physicalNumberOfCells; j++) {
                Cell cell = row.getCell(j);
                switch (j) {
                    case 0:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        toutiaoAccount.setParentAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 1:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        toutiaoAccount.setAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 2:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        toutiaoAccount.setAccountName(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 3:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        toutiaoAccount.setMedia(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 4:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        String str = StringUtil.getString(cell.getStringCellValue());
                        Integer type = getType(str);
                        toutiaoAccount.setType(type);
                        break;
                    case 5:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        toutiaoAccount.setFirst_agent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(toutiaoAccount.getFirst_agent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此一级代理商:"+toutiaoAccount.getFirst_agent());
                            return infoResult;
                        }
                        break;
                    case 6:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        toutiaoAccount.setAgent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(toutiaoAccount.getAgent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此如二级代理商:"+toutiaoAccount.getAgent());
                            return infoResult;
                        }
                        break;
                    case 7:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        toutiaoAccount.setPutUser(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 8:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        toutiaoAccount.setRebate(StringUtil.getDouble(cell.getStringCellValue()));
                        break;
                    case 9:
                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            toutiaoAccount.setRemark(StringUtil.getString(cell.getStringCellValue()));
                        }
                        break;
                    case 10:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        if (StringUtil.getString(cell.getStringCellValue()).length() > 2) {
                            infoResult.setRet(0);
                            infoResult.setMsg("业务模式请填写成数字");
                            return infoResult;
                        }
                        toutiaoAccount.setBusiness(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 11:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        toutiaoAccount.setStrategy(strategyMap.get(StringUtil.getString(cell.getStringCellValue())));
                        break;
                    default:
                        break;
                }
            }

            toutiaoAccount.setGroupId(1);
            toutiaoAccount.setCreateUser(username);
            toutiaoAccountList.add(toutiaoAccount);
        }

        if (CollectionUtils.isEmpty(toutiaoAccountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }


        int length = 500;
        if (toutiaoAccountList.size() <= length) {
            platformDao.addToutiaoAccount(toutiaoAccountList);
        } else {
            List<List<ToutiaoAccount>> lists = Lists.partition(toutiaoAccountList, length);
            for (int i = 0; i < lists.size(); i++) {
                platformDao.addToutiaoAccount(lists.get(i));
            }
        }
        for (ToutiaoAccount account : toutiaoAccountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                platformDao.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    public InfoResult kuaishouAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());

        Sheet sheet = xssfWorkbook.getSheetAt(0);
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        List<KuaishouAccount> kuaishouAccountList = Lists.newArrayList();
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();
        List<Map> strategyList = dnwxAdtMapper.getStrategy();
        Map<String, String> strategyMap = strategyList.stream().
                collect(Collectors.toMap(map -> StringUtil.getString(map.get("strategyName")), map -> StringUtil.getString(map.get("id")), (k1, k2) -> k2));
        List<KuaiShouDeveloper> kuaiShouDevelopers = adtMapper.selectKuaiShouDeveloper();
        Map<Integer, KuaiShouDeveloper> developerMap = kuaiShouDevelopers.stream().
                collect(Collectors.toMap(d -> d.getId(), d -> d, (k1, k2) -> k2));

        for (int i = 1; i < physicalNumberOfRows; i++) {
            Row row = sheet.getRow(i);
            if (null == row
                    || null == row.getCell(0)
                    || Strings.isNullOrEmpty(row.getCell(0).toString())) {
                continue;
            }
            int physicalNumberOfCells = row.getPhysicalNumberOfCells();

            KuaishouAccount kuaishouAccount = new KuaishouAccount();
            for (int j = 0; j < physicalNumberOfCells; j++) {
                Cell cell = row.getCell(j);
                switch (j) {
                    case 0:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        kuaishouAccount.setAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 1:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        kuaishouAccount.setAccountName(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 2:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        kuaishouAccount.setMedia(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 3:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        String str = StringUtil.getString(cell.getStringCellValue());
                        Integer type = getType(str);
                        kuaishouAccount.setType(type);
                        break;
                    case 4:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        kuaishouAccount.setFirst_agent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(kuaishouAccount.getFirst_agent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此一级代理商:"+kuaishouAccount.getFirst_agent());
                            return infoResult;
                        }
                        break;
                    case 5:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        kuaishouAccount.setAgent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(kuaishouAccount.getAgent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此二级代理商:"+kuaishouAccount.getAgent());
                            return infoResult;
                        }
                        break;
                    case 6:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        kuaishouAccount.setPutUser(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 7:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        kuaishouAccount.setRebate(StringUtil.getDouble(cell.getStringCellValue()));
                        break;
                    case 8:
                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            kuaishouAccount.setRemark(StringUtil.getString(cell.getStringCellValue()));
                        }
                        break;
                    case 9:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        if (StringUtil.getString(cell.getStringCellValue()).length() > 2) {
                            infoResult.setRet(0);
                            infoResult.setMsg("业务模式请填写成数字");
                            return infoResult;
                        }
                        kuaishouAccount.setBusiness(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 10:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        kuaishouAccount.setStrategy(strategyMap.get(StringUtil.getString(cell.getStringCellValue())));
                        break;
                    case 11:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        kuaishouAccount.setDeveloper_id(StringUtil.getInt(cell.getStringCellValue()));
                        break;
                    default:
                        break;
                }
            }

            //开发者id为空默认为1
            if (kuaishouAccount.getDeveloper_id() == null) {
                kuaishouAccount.setDeveloper_id(1);
            }
            KuaiShouDeveloper kuaiShouDeveloper = developerMap.get(kuaishouAccount.getDeveloper_id());
            if (kuaiShouDeveloper == null) {
                infoResult.setRet(0);
                infoResult.setMsg("账号"+kuaishouAccount.getAccount()+"未找到开发者应用配置");
                return infoResult;
            }
            kuaishouAccount.setAppId(StringUtil.getLong(kuaiShouDeveloper.getAppid()));
            kuaishouAccount.setSecret(kuaiShouDeveloper.getSecret());
            kuaishouAccount.setUrl(kuaiShouDeveloper.getUrl().replaceAll("\\{account\\}",kuaishouAccount.getAccount()));
            kuaishouAccount.setGroupId(1);
            kuaishouAccount.setCreateUser(username);
            kuaishouAccountList.add(kuaishouAccount);
        }

        if (CollectionUtils.isEmpty(kuaishouAccountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }


        int length = 500;
        if (kuaishouAccountList.size() <= length) {
            platformDao.addKuaishouAccount(kuaishouAccountList);
        } else {
            List<List<KuaishouAccount>> lists = Lists.partition(kuaishouAccountList, length);
            for (int i = 0; i < lists.size(); i++) {
                platformDao.addKuaishouAccount(lists.get(i));
            }
        }
        for (KuaishouAccount account : kuaishouAccountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                platformDao.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    public InfoResult gdtAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());

        Sheet sheet = xssfWorkbook.getSheetAt(0);
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        List<TXAccount> txAccountList = Lists.newArrayList();
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();
        List<Map> strategyList = dnwxAdtMapper.getStrategy();
        Map<String, String> strategyMap = strategyList.stream().
                collect(Collectors.toMap(map -> StringUtil.getString(map.get("strategyName")), map -> StringUtil.getString(map.get("id")), (k1, k2) -> k2));

        for (int i = 1; i < physicalNumberOfRows; i++) {
            Row row = sheet.getRow(i);
            if (null == row
                    || null == row.getCell(0)
                    || Strings.isNullOrEmpty(row.getCell(0).toString())) {
                continue;
            }
            int physicalNumberOfCells = row.getPhysicalNumberOfCells();

            TXAccount txAccount = new TXAccount();
            for (int j = 0; j < physicalNumberOfCells; j++) {
                Cell cell = row.getCell(j);
                switch (j) {
                    case 0:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setParentAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 1:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 2:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setAccountName(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 3:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setMedia(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 4:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        String str = StringUtil.getString(cell.getStringCellValue());
                        Integer type = getType(str);
                        txAccount.setType(type);
                        break;
                    case 5:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setFirst_agent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(txAccount.getFirst_agent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此一级代理商:"+txAccount.getFirst_agent());
                            return infoResult;
                        }
                        break;
                    case 6:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setAgent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(txAccount.getAgent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此二级代理商:"+txAccount.getAgent());
                            return infoResult;
                        }
                        break;
                    case 7:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setPutUser(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 8:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setRebate(StringUtil.getDouble(cell.getStringCellValue()));
                        break;
                    case 9:
                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            txAccount.setRemark(StringUtil.getString(cell.getStringCellValue()));
                        }
                        break;
                    case 10:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        if (StringUtil.getString(cell.getStringCellValue()).length() > 2) {
                            infoResult.setRet(0);
                            infoResult.setMsg("业务模式请填写成数字");
                            return infoResult;
                        }
                        txAccount.setBusiness(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 11:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setStrategy(strategyMap.get(StringUtil.getString(cell.getStringCellValue())));
                        break;
                    case 12:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setAccountType(StringUtil.getInt(cell.getStringCellValue()));
                        break;
                    default:
                        break;
                }
            }
            if ("13".equals(txAccount.getBusiness())) {
                txAccount.setInstallType(3);
            } else {
                txAccount.setInstallType(1);
            }
            if (txAccount.getAccountType() == null) {
                txAccount.setAccountType(3);
            }
            txAccount.setGroupId(1);
            txAccount.setCreateUser(username);
            txAccountList.add(txAccount);
        }

        if (CollectionUtils.isEmpty(txAccountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }


        int length = 500;
        if (txAccountList.size() <= length) {
            platformDao.addTXAccount(txAccountList);
        } else {
            List<List<TXAccount>> lists = Lists.partition(txAccountList, length);
            for (int i = 0; i < lists.size(); i++) {
                platformDao.addTXAccount(lists.get(i));
            }
        }
        for (TXAccount account : txAccountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                platformDao.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    public InfoResult wxAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());

        Sheet sheet = xssfWorkbook.getSheetAt(0);
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        List<TXAccount> txAccountList = Lists.newArrayList();
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();
        List<Map> strategyList = dnwxAdtMapper.getStrategy();
        Map<String, String> strategyMap = strategyList.stream().
                collect(Collectors.toMap(map -> StringUtil.getString(map.get("strategyName")), map -> StringUtil.getString(map.get("id")), (k1, k2) -> k2));

        for (int i = 1; i < physicalNumberOfRows; i++) {
            Row row = sheet.getRow(i);
            if (null == row
                    || null == row.getCell(0)
                    || Strings.isNullOrEmpty(row.getCell(0).toString())) {
                continue;
            }
            int physicalNumberOfCells = row.getPhysicalNumberOfCells();

            TXAccount txAccount = new TXAccount();
            for (int j = 0; j < physicalNumberOfCells; j++) {
                Cell cell = row.getCell(j);
                switch (j) {
                    case 0:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setParentAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 1:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 2:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setAccountName(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 3:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setMedia(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 4:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        String str = StringUtil.getString(cell.getStringCellValue());
                        Integer type = getType(str);
                        txAccount.setType(type);
                        break;
                    case 5:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setFirst_agent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(txAccount.getFirst_agent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此一级代理商:"+txAccount.getFirst_agent());
                            return infoResult;
                        }
                        break;
                    case 6:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setAgent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(txAccount.getAgent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此二级代理商:"+txAccount.getAgent());
                            return infoResult;
                        }
                        break;
                    case 7:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setPutUser(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 8:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setRebate(StringUtil.getDouble(cell.getStringCellValue()));
                        break;
                    case 9:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setWxAccountId(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 10:
                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            txAccount.setRemark(StringUtil.getString(cell.getStringCellValue()));
                        }
                        break;
                    case 11:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        if (StringUtil.getString(cell.getStringCellValue()).length() > 2) {
                            infoResult.setRet(0);
                            infoResult.setMsg("业务模式请填写成数字");
                            return infoResult;
                        }
                        txAccount.setBusiness(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 12:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        txAccount.setStrategy(strategyMap.get(StringUtil.getString(cell.getStringCellValue())));
                        break;
                    default:
                        break;
                }
            }
            if ("13".equals(txAccount.getBusiness())) {
                txAccount.setInstallType(3);
            } else {
                txAccount.setInstallType(1);
            }
            txAccount.setAccountType(2);
            txAccount.setGroupId(1);
            txAccount.setCreateUser(username);
            txAccountList.add(txAccount);
        }

        if (CollectionUtils.isEmpty(txAccountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }


        int length = 500;
        if (txAccountList.size() <= length) {
            platformDao.addTXAccount(txAccountList);
        } else {
            List<List<TXAccount>> lists = Lists.partition(txAccountList, length);
            for (int i = 0; i < lists.size(); i++) {
                platformDao.addTXAccount(lists.get(i));
            }
        }
        for (TXAccount account : txAccountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                platformDao.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    public InfoResult aqyAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());

        Sheet sheet = xssfWorkbook.getSheetAt(0);
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        List<AQYAccount> aqyAccountList = Lists.newArrayList();
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();

        for (int i = 1; i < physicalNumberOfRows; i++) {
            Row row = sheet.getRow(i);
            if (null == row
                    || null == row.getCell(0)
                    || Strings.isNullOrEmpty(row.getCell(0).toString())) {
                continue;
            }
            int physicalNumberOfCells = row.getPhysicalNumberOfCells();

            AQYAccount aqyAccount = new AQYAccount();
            for (int j = 0; j < physicalNumberOfCells; j++) {
                Cell cell = row.getCell(j);
                switch (j) {
                    case 0:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        aqyAccount.setAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 1:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        aqyAccount.setAccountName(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 2:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        aqyAccount.setMedia(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 3:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        String str = StringUtil.getString(cell.getStringCellValue());
                        Integer type = getType(str);
                        aqyAccount.setType(type);
                        break;
                    case 4:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        aqyAccount.setFirst_agent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(aqyAccount.getFirst_agent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此一级代理商:"+aqyAccount.getFirst_agent());
                            return infoResult;
                        }
                        break;
                    case 5:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        aqyAccount.setAgent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(aqyAccount.getAgent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此二级代理商:"+aqyAccount.getAgent());
                            return infoResult;
                        }
                        break;
                    case 6:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        aqyAccount.setPutUser(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 7:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        aqyAccount.setRebate(StringUtil.getDouble(cell.getStringCellValue()));
                        break;
                    case 8:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        aqyAccount.setAppId(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 9:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        aqyAccount.setSecret(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 10:
                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            aqyAccount.setRemark(StringUtil.getString(cell.getStringCellValue()));
                        }
                        break;
                    case 11:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        if (StringUtil.getString(cell.getStringCellValue()).length() > 2) {
                            infoResult.setRet(0);
                            infoResult.setMsg("业务模式请填写成数字");
                            return infoResult;
                        }
                        aqyAccount.setBusiness(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    default:
                        break;
                }
            }

            aqyAccount.setGroupId(1);
            aqyAccount.setCreateUser(username);
            aqyAccountList.add(aqyAccount);
        }

        if (CollectionUtils.isEmpty(aqyAccountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }


        int length = 500;
        if (aqyAccountList.size() <= length) {
            platformDao.addAQYAccount(aqyAccountList);
        } else {
            List<List<AQYAccount>> lists = Lists.partition(aqyAccountList, length);
            for (int i = 0; i < lists.size(); i++) {
                platformDao.addAQYAccount(lists.get(i));
            }
        }
        for (AQYAccount account : aqyAccountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                platformDao.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    public InfoResult baiduAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());

        Sheet sheet = xssfWorkbook.getSheetAt(0);
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        List<BaiduAccount> baiduAccountList = Lists.newArrayList();
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();

        for (int i = 1; i < physicalNumberOfRows; i++) {
            Row row = sheet.getRow(i);
            if (null == row
                    || null == row.getCell(0)
                    || Strings.isNullOrEmpty(row.getCell(0).toString())) {
                continue;
            }
            int physicalNumberOfCells = row.getPhysicalNumberOfCells();

            BaiduAccount baiduAccount = new BaiduAccount();
            for (int j = 0; j < physicalNumberOfCells; j++) {
                Cell cell = row.getCell(j);
                switch (j) {
                    case 0:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        baiduAccount.setAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 1:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        baiduAccount.setAccountName(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 2:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        baiduAccount.setMedia(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 3:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        String str = StringUtil.getString(cell.getStringCellValue());
                        Integer type = getType(str);
                        baiduAccount.setType(type);
                        break;
                    case 4:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        baiduAccount.setFirst_agent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(baiduAccount.getFirst_agent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此一级代理商:"+baiduAccount.getFirst_agent());
                            return infoResult;
                        }
                        break;
                    case 5:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        baiduAccount.setAgent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(baiduAccount.getAgent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此二级代理商:"+baiduAccount.getAgent());
                            return infoResult;
                        }
                        break;
                    case 6:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        baiduAccount.setPutUser(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 7:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        baiduAccount.setRebate(StringUtil.getDouble(cell.getStringCellValue()));
                        break;
                    case 8:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        baiduAccount.setPassword(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 9:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        baiduAccount.setBaiduToken(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 10:
                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            baiduAccount.setRemark(StringUtil.getString(cell.getStringCellValue()));
                        }
                        break;
                    case 11:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        if (StringUtil.getString(cell.getStringCellValue()).length() > 2) {
                            infoResult.setRet(0);
                            infoResult.setMsg("业务模式请填写成数字");
                            return infoResult;
                        }
                        baiduAccount.setBusiness(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 12:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        baiduAccount.setParentAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 13:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        baiduAccount.setDataType(StringUtil.getInt(cell.getStringCellValue()));
                        break;
                    default:
                        break;
                }
            }

            baiduAccount.setGroupId(1);
            baiduAccount.setCreateUser(username);
            baiduAccountList.add(baiduAccount);
        }

        if (CollectionUtils.isEmpty(baiduAccountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }


        int length = 500;
        if (baiduAccountList.size() <= length) {
            platformDao.addBaiduAccount(baiduAccountList);
        } else {
            List<List<BaiduAccount>> lists = Lists.partition(baiduAccountList, length);
            for (int i = 0; i < lists.size(); i++) {
                platformDao.addBaiduAccount(lists.get(i));
            }
        }
        for (BaiduAccount account : baiduAccountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                platformDao.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    public InfoResult qttAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());

        Sheet sheet = xssfWorkbook.getSheetAt(0);
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        List<QttAccount> qttAccountList = Lists.newArrayList();
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();

        for (int i = 1; i < physicalNumberOfRows; i++) {
            Row row = sheet.getRow(i);
            if (null == row
                    || null == row.getCell(0)
                    || Strings.isNullOrEmpty(row.getCell(0).toString())) {
                continue;
            }
            int physicalNumberOfCells = row.getPhysicalNumberOfCells();

            QttAccount qttAccount = new QttAccount();
            for (int j = 0; j < physicalNumberOfCells; j++) {
                Cell cell = row.getCell(j);
                switch (j) {
                    case 0:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        qttAccount.setAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 1:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        qttAccount.setAccountName(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 2:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        qttAccount.setAppId(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 3:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        qttAccount.setRefreshToken(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 4:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        qttAccount.setMedia(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 5:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        String str = StringUtil.getString(cell.getStringCellValue());
                        Integer type = getType(str);
                        qttAccount.setType(type);
                        break;
                    case 6:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        qttAccount.setFirst_agent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(qttAccount.getFirst_agent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此一级代理商:"+qttAccount.getFirst_agent());
                            return infoResult;
                        }
                        break;
                    case 7:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        qttAccount.setAgent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(qttAccount.getAgent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此二级代理商:"+qttAccount.getAgent());
                            return infoResult;
                        }
                        break;
                    case 8:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        qttAccount.setPutUser(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 9:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        qttAccount.setRebate(StringUtil.getDouble(cell.getStringCellValue()));
                        break;
                    case 10:
                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            qttAccount.setRemark(StringUtil.getString(cell.getStringCellValue()));
                        }
                        break;
                    case 11:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        if (StringUtil.getString(cell.getStringCellValue()).length() > 2) {
                            infoResult.setRet(0);
                            infoResult.setMsg("业务模式请填写成数字");
                            return infoResult;
                        }
                        qttAccount.setBusiness(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    default:
                        break;
                }
            }

            qttAccount.setGroupId(1);
            qttAccount.setCreateUser(username);
            qttAccountList.add(qttAccount);
        }

        if (CollectionUtils.isEmpty(qttAccountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }


        int length = 500;
        if (qttAccountList.size() <= length) {
            platformDao.addQttAccount(qttAccountList);
        } else {
            List<List<QttAccount>> lists = Lists.partition(qttAccountList, length);
            for (int i = 0; i < lists.size(); i++) {
                platformDao.addQttAccount(lists.get(i));
            }
        }
        for (QttAccount account : qttAccountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                platformDao.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    public InfoResult oppoAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());

        Sheet sheet = xssfWorkbook.getSheetAt(0);
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        List<OppoAccount> oppoAccountList = Lists.newArrayList();
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();

        for (int i = 1; i < physicalNumberOfRows; i++) {
            Row row = sheet.getRow(i);
            if (null == row
                    || null == row.getCell(0)
                    || Strings.isNullOrEmpty(row.getCell(0).toString())) {
                continue;
            }
            int physicalNumberOfCells = row.getPhysicalNumberOfCells();

            OppoAccount oppoAccount = new OppoAccount();
            for (int j = 0; j <= physicalNumberOfCells; j++) {
                Cell cell = row.getCell(j);
                switch (j) {
                    case 0:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        oppoAccount.setAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 1:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        oppoAccount.setAccountName(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 2:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        oppoAccount.setMedia(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 3:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        String str = StringUtil.getString(cell.getStringCellValue());
                        Integer type = getType(str);
                        oppoAccount.setType(type);
                        break;
                    case 4:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        oppoAccount.setFirst_agent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(oppoAccount.getFirst_agent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此一级代理商:"+oppoAccount.getFirst_agent());
                            return infoResult;
                        }
                        break;
                    case 5:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        oppoAccount.setAgent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(oppoAccount.getAgent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此二级代理商:"+oppoAccount.getAgent());
                            return infoResult;
                        }
                        break;
                    case 6:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        oppoAccount.setPutUser(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 7:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        oppoAccount.setRebate(StringUtil.getDouble(cell.getStringCellValue()));
                        break;
                    case 8:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        oppoAccount.setApi_id(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 9:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        oppoAccount.setApi_key(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 10:
                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            oppoAccount.setRemark(StringUtil.getString(cell.getStringCellValue()));
                        }
                        break;
                    case 11:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        if (StringUtil.getString(cell.getStringCellValue()).length() > 2) {
                            infoResult.setRet(0);
                            infoResult.setMsg("业务模式请填写成数字");
                            return infoResult;
                        }
                        oppoAccount.setBusiness(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 12:
                    	if ( null==cell) {
                    		 infoResult.setRet(0);
                             infoResult.setMsg("投放账号主体不能为空");
                             return infoResult;
                    	}
                        String as=StringUtil.getString(cell.getStringCellValue());
                        if(StringUtil.is_nullString(as)){
                        	 infoResult.setRet(0);
                             infoResult.setMsg("投放账号主体不能为空");
                             return infoResult;
                        }
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        oppoAccount.setAccountSubject(as);
                        break;
                    default:
                        break;
                }
            }

            oppoAccount.setGroupId(1);
            oppoAccount.setCreateUser(username);
            oppoAccountList.add(oppoAccount);
        }

        if (CollectionUtils.isEmpty(oppoAccountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }


        int length = 500;
        if (oppoAccountList.size() <= length) {
            platformDao.addOppoAccount(oppoAccountList);
        } else {
            List<List<OppoAccount>> lists = Lists.partition(oppoAccountList, length);
            for (int i = 0; i < lists.size(); i++) {
                platformDao.addOppoAccount(lists.get(i));
            }
        }
        for (OppoAccount account : oppoAccountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                platformDao.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    public InfoResult huaweiAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());

        Sheet sheet = xssfWorkbook.getSheetAt(0);
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        List<HuaweiAccount> huaweiAccountList = Lists.newArrayList();
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();

        for (int i = 1; i < physicalNumberOfRows; i++) {
            Row row = sheet.getRow(i);
            if (null == row
                    || null == row.getCell(0)
                    || Strings.isNullOrEmpty(row.getCell(0).toString())) {
                continue;
            }
            int physicalNumberOfCells = row.getPhysicalNumberOfCells();

            HuaweiAccount huaweiAccount = new HuaweiAccount();
            for (int j = 0; j <= physicalNumberOfCells; j++) {
                Cell cell = row.getCell(j);
                switch (j) {
                    case 0:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        huaweiAccount.setAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 1:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        huaweiAccount.setAccountName(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 2:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        huaweiAccount.setIsCPD(StringUtil.getInt(cell.getStringCellValue()));
                        break;
                    case 3:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        huaweiAccount.setClient_id(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 4:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        huaweiAccount.setClient_secret(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 5:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        huaweiAccount.setMedia(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 6:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        String str = StringUtil.getString(cell.getStringCellValue());
                        Integer type = getType(str);
                        huaweiAccount.setType(type);
                        break;
                    case 7:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        huaweiAccount.setFirst_agent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(huaweiAccount.getFirst_agent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此一级代理商:"+huaweiAccount.getFirst_agent());
                            return infoResult;
                        }
                        break;
                    case 8:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        huaweiAccount.setAgent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(huaweiAccount.getAgent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此二级代理商:"+huaweiAccount.getAgent());
                            return infoResult;
                        }
                        break;
                    case 9:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        huaweiAccount.setPutUser(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 10:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        huaweiAccount.setRebate(StringUtil.getDouble(cell.getStringCellValue()));
                        break;
                    case 11:
                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            huaweiAccount.setRemark(StringUtil.getString(cell.getStringCellValue()));
                        }
                        break;
                    case 12:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        if (StringUtil.getString(cell.getStringCellValue()).length() > 2) {
                            infoResult.setRet(0);
                            infoResult.setMsg("业务模式请填写成数字");
                            return infoResult;
                        }
                        huaweiAccount.setBusiness(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 13:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        huaweiAccount.setPackage_name(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 14:
                    	if(null==cell){
                    		 infoResult.setRet(0);
                             infoResult.setMsg("投放账号主体不能为空");
                             return infoResult;
                    	}
                        String as=StringUtil.getString(cell.getStringCellValue());
                        if(StringUtil.is_nullString(as)){
	                   		 infoResult.setRet(0);
	                         infoResult.setMsg("投放账号主体不能为空");
	                         return infoResult;
                        }
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        huaweiAccount.setAccountSubject(as);
                        break;
                    default:
                        break;
                }
            }
            huaweiAccount.setGroupId(1);
            huaweiAccount.setCreateUser(username);
            huaweiAccountList.add(huaweiAccount);
        }

        if (CollectionUtils.isEmpty(huaweiAccountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }

        int length = 500;
        if (huaweiAccountList.size() <= length) {
            platformDao.addHuaweiAccount(huaweiAccountList);
        } else {
            List<List<HuaweiAccount>> lists = Lists.partition(huaweiAccountList, length);
            for (int i = 0; i < lists.size(); i++) {
                platformDao.addHuaweiAccount(lists.get(i));
            }
        }
        for (HuaweiAccount account : huaweiAccountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                platformDao.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    public InfoResult vivoAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());

        Sheet sheet = xssfWorkbook.getSheetAt(0);
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        List<VivoAccount> vivoAccountList = Lists.newArrayList();
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();

        for (int i = 1; i < physicalNumberOfRows; i++) {
            Row row = sheet.getRow(i);
            if (null == row
                    || null == row.getCell(0)
                    || Strings.isNullOrEmpty(row.getCell(0).toString())) {
                continue;
            }
            int physicalNumberOfCells = row.getLastCellNum();

            VivoAccount vivoAccount = new VivoAccount();
            for (int j = 0; j < physicalNumberOfCells; j++) {
                Cell cell = row.getCell(j);
                switch (j) {
                    case 0:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        vivoAccount.setAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 1:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        vivoAccount.setClient_id(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 2:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        vivoAccount.setClient_secret(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 3:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        String str = StringUtil.getString(cell.getStringCellValue());
                        Integer type = getType(str);
                        vivoAccount.setType(type);
                        break;
                    case 4:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        vivoAccount.setFirst_agent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(vivoAccount.getFirst_agent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此一级代理商:"+vivoAccount.getFirst_agent());
                            return infoResult;
                        }
                        break;
                    case 5:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        vivoAccount.setAgent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(vivoAccount.getAgent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此二级代理商:"+vivoAccount.getAgent());
                            return infoResult;
                        }
                        break;
                    case 6:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        vivoAccount.setPutUser(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 7:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        vivoAccount.setRebate(StringUtil.getDouble(cell.getStringCellValue()));
                        break;
                    case 8:
                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            vivoAccount.setRemark(StringUtil.getString(cell.getStringCellValue()));
                        }
                        break;
                    case 9:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        if (StringUtil.getString(cell.getStringCellValue()).length() > 2) {
                            infoResult.setRet(0);
                            infoResult.setMsg("业务模式请填写成数字");
                            return infoResult;
                        }
                        vivoAccount.setBusiness(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 10:
                    	if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            String as=StringUtil.getString(cell.getStringCellValue());
                            if(StringUtil.is_nullString(as)){
                            	 infoResult.setRet(0);
                                 infoResult.setMsg("投放账号主体不能为空");
                                 return infoResult;
                            }
                            vivoAccount.setAccountSubject(as);
                        }
                        break;
                    default:
                        break;
                }
            }
            vivoAccount.setGroupId(1);
            vivoAccount.setCreateUser(username);
            vivoAccountList.add(vivoAccount);
        }

        if (CollectionUtils.isEmpty(vivoAccountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }

        int length = 500;
        if (vivoAccountList.size() <= length) {
            platformDao.addVivoAccount(vivoAccountList);
        } else {
            List<List<VivoAccount>> lists = Lists.partition(vivoAccountList, length);
            for (int i = 0; i < lists.size(); i++) {
                platformDao.addVivoAccount(lists.get(i));
            }
        }
        for (VivoAccount account : vivoAccountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                platformDao.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    public InfoResult xiaomiAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());

        Sheet sheet = xssfWorkbook.getSheetAt(0);
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        List<XiaomiSpendAccount> xiaomiAccountList = Lists.newArrayList();
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();

        for (int i = 1; i < physicalNumberOfRows; i++) {
            Row row = sheet.getRow(i);
            if (null == row
                    || null == row.getCell(0)
                    || Strings.isNullOrEmpty(row.getCell(0).toString())) {
                continue;
            }
            int physicalNumberOfCells = row.getPhysicalNumberOfCells();

            XiaomiSpendAccount account = new XiaomiSpendAccount();
            for (int j = 0; j < physicalNumberOfCells; j++) {
                Cell cell = row.getCell(j);
                switch (j) {
                    case 0:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        account.setAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 1:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        account.setAccountName(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 2:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        account.setSignId(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 3:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        account.setSecretKey(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 4:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        String str = StringUtil.getString(cell.getStringCellValue());
                        Integer type = getType(str);
                        account.setType(type);
                        break;
                    case 5:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        account.setFirst_agent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(account.getFirst_agent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此一级代理商:"+account.getFirst_agent());
                            return infoResult;
                        }
                        break;
                    case 6:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        account.setAgent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(account.getAgent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此二级代理商:"+account.getAgent());
                            return infoResult;
                        }
                        break;
                    case 7:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        account.setPutUser(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 8:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        account.setRebate(StringUtil.getDouble(cell.getStringCellValue()));
                        break;
                    case 9:
                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            account.setRemark(StringUtil.getString(cell.getStringCellValue()));
                        }
                        break;
                    case 10:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        if (StringUtil.getString(cell.getStringCellValue()).length() > 2) {
                            infoResult.setRet(0);
                            infoResult.setMsg("业务模式请填写成数字");
                            return infoResult;
                        }
                        account.setBusiness(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 11:
                    	 if (cell != null) {
                             cell.setCellType(Cell.CELL_TYPE_STRING);
                             String as=StringUtil.getString(cell.getStringCellValue());
                             if(StringUtil.is_nullString(as)){
                             	 infoResult.setRet(0);
                                  infoResult.setMsg("投放账号主体不能为空");
                                  return infoResult;
                             }
                             account.setAccountSubject(as);
                         }
                         break;
                    default:
                        break;
                }
            }
            account.setGroupId(1);
            account.setCreateUser(username);
            xiaomiAccountList.add(account);
        }

        if (CollectionUtils.isEmpty(xiaomiAccountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }

        int length = 500;
        if (xiaomiAccountList.size() <= length) {
            platformDao.addXiaomiAccount(xiaomiAccountList);
        } else {
            List<List<XiaomiSpendAccount>> lists = Lists.partition(xiaomiAccountList, length);
            for (int i = 0; i < lists.size(); i++) {
                platformDao.addXiaomiAccount(lists.get(i));
            }
        }
        for (XiaomiSpendAccount account : xiaomiAccountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                platformDao.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    private Integer getType(String str) {
        Integer type = null;
        if (str.equals("自运营") || str.equals("自运营-分包")) {
            type = 1;
        } else if (str.equals("代运营") || str.equals("代运营-分包")) {
            type = 2;
        } else if (str.equals("自运营-直投")) {
            type = 3;
        } else if (str.equals("代运营-直投")) {
            type = 4;
        } else if (str.equals("自运营-OPPO直投")) {
            type = 5;
        } else if (str.equals("自运营-VIVO直投")) {
            type = 6;
        } else if (str.equals("自运营-华为直投")) {
            type = 7;
        } else if (str.equals("自运营-小米直投")) {
            type = 8;
        } else if (str.equals("代运营-OPPO直投")) {
            type = 9;
        } else if (str.equals("代运营-VIVO直投")) {
            type = 10;
        } else if (str.equals("代运营-华为直投")) {
            type = 11;
        } else if (str.equals("代运营-小米直投")) {
            type = 12;
        } else if (str.equals("自运营-OPPOMJ")) {
            type = 13;
        } else if (str.equals("代运营-OPPOMJ")) {
            type = 14;
        } else if (str.equals("自运营-小米直投2")) {
            type = 15;
        }
        return type;
    }

    public InfoResult ucAccount(MultipartFile multipartFile, String username) throws IOException {
        InfoResult infoResult = new InfoResult();
        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());

        Sheet sheet = xssfWorkbook.getSheetAt(0);
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        List<UcAccount> ucAccountList = Lists.newArrayList();
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();

        for (int i = 1; i < physicalNumberOfRows; i++) {
            Row row = sheet.getRow(i);
            if (null == row
                    || null == row.getCell(0)
                    || Strings.isNullOrEmpty(row.getCell(0).toString())) {
                continue;
            }
            int physicalNumberOfCells = row.getPhysicalNumberOfCells();

            UcAccount ucAccount = new UcAccount();
            for (int j = 0; j < physicalNumberOfCells; j++) {
                Cell cell = row.getCell(j);
                switch (j) {
                    case 0:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ucAccount.setAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 1:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ucAccount.setPassword(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 2:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ucAccount.setToken(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 3:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ucAccount.setMedia(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 4:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        String str = StringUtil.getString(cell.getStringCellValue());
                        Integer type = getType(str);
                        ucAccount.setType(type);
                        break;
                    case 5:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ucAccount.setFirst_agent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(ucAccount.getFirst_agent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此一级代理商:"+ucAccount.getFirst_agent());
                            return infoResult;
                        }
                        break;
                    case 6:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ucAccount.setAgent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(ucAccount.getAgent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此二级代理商:"+ucAccount.getAgent());
                            return infoResult;
                        }
                        break;
                    case 7:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ucAccount.setPutUser(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 8:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ucAccount.setRebate(StringUtil.getDouble(cell.getStringCellValue()));
                        break;
                    case 9:
                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            ucAccount.setRemark(StringUtil.getString(cell.getStringCellValue()));
                        }
                        break;
                    case 10:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        if (StringUtil.getString(cell.getStringCellValue()).length() > 2) {
                            infoResult.setRet(0);
                            infoResult.setMsg("业务模式请填写成数字");
                            return infoResult;
                        }
                        ucAccount.setBusiness(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    default:
                        break;
                }
            }
            ucAccount.setGroupId(1);
            ucAccount.setCreateUser(username);
            ucAccountList.add(ucAccount);
        }

        if (CollectionUtils.isEmpty(ucAccountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }

        int length = 500;
        if (ucAccountList.size() <= length) {
            platformDao.addUcAccount(ucAccountList);
        } else {
            List<List<UcAccount>> lists = Lists.partition(ucAccountList, length);
            for (int i = 0; i < lists.size(); i++) {
                platformDao.addUcAccount(lists.get(i));
            }
        }
        for (UcAccount account : ucAccountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                platformDao.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

    public InfoResult honorAccount(MultipartFile multipartFile, String username) throws Exception {
        InfoResult infoResult = new InfoResult();
        if (multipartFile.isEmpty()) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择文件上传");
            return infoResult;
        }

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());

        Sheet sheet = xssfWorkbook.getSheetAt(0);
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        List<HonorAccount> honorAccountList = Lists.newArrayList();
        List<String> agentNameList = jettisonCommonMapper.getAgentByName();

        for (int i = 1; i < physicalNumberOfRows; i++) {
            Row row = sheet.getRow(i);
            if (null == row
                    || null == row.getCell(0)
                    || Strings.isNullOrEmpty(row.getCell(0).toString())) {
                continue;
            }
            int physicalNumberOfCells = row.getPhysicalNumberOfCells();

            HonorAccount honorAccount = new HonorAccount();
            for (int j = 0; j < physicalNumberOfCells; j++) {
                Cell cell = row.getCell(j);
                switch (j) {
                    case 0:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        honorAccount.setAccount(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 1:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        honorAccount.setAccountName(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 2:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        honorAccount.setClient_id(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 3:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        honorAccount.setClient_secret(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 4:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        honorAccount.setMedia(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 5:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        String str = StringUtil.getString(cell.getStringCellValue());
                        Integer type = getType(str);
                        honorAccount.setType(type);
                        break;
                    case 6:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        honorAccount.setFirst_agent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(honorAccount.getFirst_agent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此一级代理商:"+honorAccount.getFirst_agent());
                            return infoResult;
                        }
                        break;
                    case 7:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        honorAccount.setAgent(StringUtil.getString(cell.getStringCellValue()));
                        if (!agentNameList.contains(honorAccount.getAgent())) {
                            infoResult.setRet(0);
                            infoResult.setMsg("查无此二级代理商:"+honorAccount.getAgent());
                            return infoResult;
                        }
                        break;
                    case 8:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        honorAccount.setPutUser(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 9:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        honorAccount.setRebate(StringUtil.getDouble(cell.getStringCellValue()));
                        break;
                    case 10:
                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            honorAccount.setRemark(StringUtil.getString(cell.getStringCellValue()));
                        }
                        break;
                    case 11:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        if (StringUtil.getString(cell.getStringCellValue()).length() > 2) {
                            infoResult.setRet(0);
                            infoResult.setMsg("业务模式请填写成数字");
                            return infoResult;
                        }
                        honorAccount.setBusiness(StringUtil.getString(cell.getStringCellValue()));
                        break;
                    case 12:
                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            String as=StringUtil.getString(cell.getStringCellValue());
                            if(StringUtil.is_nullString(as)){
                            	 infoResult.setRet(0);
                                 infoResult.setMsg("投放账号主体不能为空");
                                 return infoResult;
                            }
                            honorAccount.setAccountSubject(as);
                        }
                        break;
                    default:
                        break;
                }
            }
            honorAccount.setGroupId(1);
            honorAccount.setCreateUser(username);
            honorAccountList.add(honorAccount);
        }

        if (CollectionUtils.isEmpty(honorAccountList)) {
            infoResult.setRet(0);
            infoResult.setMsg("没有数据");
            return infoResult;
        }

        int length = 500;
        if (honorAccountList.size() <= length) {
            platformDao.addHonorAccount(honorAccountList);
        } else {
            List<List<HonorAccount>> lists = Lists.partition(honorAccountList, length);
            for (int i = 0; i < lists.size(); i++) {
                platformDao.addHonorAccount(lists.get(i));
            }
        }
        for (HonorAccount account : honorAccountList) {
            if (!Strings.isNullOrEmpty(account.getBusiness())) {
                platformDao.addAccountBusiness(account.getAccount(), account.getBusiness());
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("导入成功");
        return infoResult;
    }

}
