package com.wbgame.service.jettison.report;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.mysql.jdbc.StringUtils;
import com.wbgame.common.Asserts;
import com.wbgame.mapper.adb.AppleRoiReportMapper;
import com.wbgame.mapper.adb.DnwxBiAdtMapper;
import com.wbgame.mapper.adv2.MonetizationSummaryMapper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.master.SomeMapper;
import com.wbgame.mapper.master.jettison.DnwxAdtMapper;
import com.wbgame.mapper.master.jettison.JettisonCommonMapper;
import com.wbgame.mapper.tfxt.TfxtMapper;
import com.wbgame.pojo.AppCategory;
import com.wbgame.pojo.finance.FinanceReport;
import com.wbgame.pojo.jettison.param.GuanBaoParam;
import com.wbgame.pojo.jettison.report.InfoResult;
import com.wbgame.pojo.jettison.report.dto.*;
import com.wbgame.pojo.jettison.report.param.*;
import com.wbgame.service.AdService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.jettison.DateUtils;
import com.wbgame.utils.jettison.DoubleUtils;
import com.wbgame.utils.jettison.JsonUtils;
import com.wbgame.utils.jettison.StringUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 投放报表Service层
 * @Create 2020-08-08
 */

@Service
public class SpendReportService {

    @Autowired
    DnwxBiAdtMapper dnwxBiAdtMapper;
    @Autowired
    DnwxAdtMapper dnwxAdtMapper;
    @Autowired
    TfxtMapper tfxtMapper;
    @Autowired
    SomeMapper someMapper;
    @Autowired
    JettisonCommonMapper jettisonCommonMapper;
    @Resource
    private MonetizationSummaryMapper monetizationSummaryMapper;
    @Resource
    AppleRoiReportMapper appleRoiReportMapper;

    @Autowired
    AdService adService;
    @Resource
    AdMapper adMapper;

    @Autowired
    RedisTemplate redisTemplate;
    private static final Integer PAGE_NUM = 1;
    private static final Integer MAX_PAGE_SIZE = 100000;

    private static final String huawei_profit_roi_rate_key = "huawei_profit_roi_rate_key";

    private static final String countryJson = "[{\"label\":\"奥兰群岛(AX)\",\"value\":\"AX\"},{\"label\":\"黑山(ME)\",\"value\":\"ME\"},{\"label\":\"塞尔维亚(RS)\",\"value\":\"RS\"},{\"label\":\"澳门(MO)\",\"value\":\"MO\"},{\"label\":\"安提瓜和巴布达(AG)\",\"value\":\"AG\"},{\"label\":\"阿拉伯联合酋长国(AE)\",\"value\":\"AE\"},{\"label\":\"阿富汗(AF)\",\"value\":\"AF\"},{\"label\":\"阿尔及利亚(DZ)\",\"value\":\"DZ\"},{\"label\":\"阿塞拜疆(AZ)\",\"value\":\"AZ\"},{\"label\":\"阿尔巴尼亚(AL)\",\"value\":\"AL\"},{\"label\":\"亚美尼亚(AM)\",\"value\":\"AM\"},{\"label\":\"安道尔(AD)\",\"value\":\"AD\"},{\"label\":\"安哥拉(AO)\",\"value\":\"AO\"},{\"label\":\"美属萨摩亚(AS)\",\"value\":\"AS\"},{\"label\":\"阿根廷(AR)\",\"value\":\"AR\"},{\"label\":\"澳大利亚(AU)\",\"value\":\"AU\"},{\"label\":\"奥地利(AT)\",\"value\":\"AT\"},{\"label\":\"安圭拉(AI)\",\"value\":\"AI\"},{\"label\":\"巴林(BH)\",\"value\":\"BH\"},{\"label\":\"巴巴多斯(BB)\",\"value\":\"BB\"},{\"label\":\"博茨瓦纳(BW)\",\"value\":\"BW\"},{\"label\":\"百慕大(BM)\",\"value\":\"BM\"},{\"label\":\"比利时(BE)\",\"value\":\"BE\"},{\"label\":\"巴哈马(BS)\",\"value\":\"BS\"},{\"label\":\"孟加拉国(BD)\",\"value\":\"BD\"},{\"label\":\"伯利兹(BZ)\",\"value\":\"BZ\"},{\"label\":\"波斯尼亚和黑塞哥维那(BA)\",\"value\":\"BA\"},{\"label\":\"玻利维亚(BO)\",\"value\":\"BO\"},{\"label\":\"缅甸(MM)\",\"value\":\"MM\"},{\"label\":\"贝宁(BJ)\",\"value\":\"BJ\"},{\"label\":\"白俄罗斯(BY)\",\"value\":\"BY\"},{\"label\":\"所罗门群岛(SB)\",\"value\":\"SB\"},{\"label\":\"巴西(BR)\",\"value\":\"BR\"},{\"label\":\"不丹(BT)\",\"value\":\"BT\"},{\"label\":\"保加利亚(BG)\",\"value\":\"BG\"},{\"label\":\"文莱(BN)\",\"value\":\"BN\"},{\"label\":\"加拿大(CA)\",\"value\":\"CA\"},{\"label\":\"柬埔寨(KH)\",\"value\":\"KH\"},{\"label\":\"乍得(TD)\",\"value\":\"TD\"},{\"label\":\"斯里兰卡(LK)\",\"value\":\"LK\"},{\"label\":\"刚果（金）(CD)\",\"value\":\"CD\"},{\"label\":\"中国(CN)\",\"value\":\"CN\"},{\"label\":\"智利(CL)\",\"value\":\"CL\"},{\"label\":\"开曼群岛(KY)\",\"value\":\"KY\"},{\"label\":\"科科斯群岛(CC)\",\"value\":\"CC\"},{\"label\":\"喀麦隆(CM)\",\"value\":\"CM\"},{\"label\":\"科摩罗(KM)\",\"value\":\"KM\"},{\"label\":\"哥伦比亚(CO)\",\"value\":\"CO\"},{\"label\":\"北马里亚纳群岛(MP)\",\"value\":\"MP\"},{\"label\":\"哥斯达黎加(CR)\",\"value\":\"CR\"},{\"label\":\"中非共和国(CF)\",\"value\":\"CF\"},{\"label\":\"古巴(CU)\",\"value\":\"CU\"},{\"label\":\"佛得角(CV)\",\"value\":\"CV\"},{\"label\":\"库克群岛(CK)\",\"value\":\"CK\"},{\"label\":\"丹麦(DK)\",\"value\":\"DK\"},{\"label\":\"吉布提(DJ)\",\"value\":\"DJ\"},{\"label\":\"多米尼克(DM)\",\"value\":\"DM\"},{\"label\":\"多米尼加共和国(DO)\",\"value\":\"DO\"},{\"label\":\"厄瓜多尔(EC)\",\"value\":\"EC\"},{\"label\":\"埃及(EG)\",\"value\":\"EG\"},{\"label\":\"爱尔兰(IE)\",\"value\":\"IE\"},{\"label\":\"赤道几内亚(GQ)\",\"value\":\"GQ\"},{\"label\":\"爱沙尼亚(EE)\",\"value\":\"EE\"},{\"label\":\"厄立特里亚(ER)\",\"value\":\"ER\"},{\"label\":\"萨尔瓦多(SV)\",\"value\":\"SV\"},{\"label\":\"埃塞俄比亚(ET)\",\"value\":\"ET\"},{\"label\":\"捷克共和国(CZ)\",\"value\":\"CZ\"},{\"label\":\"法属圭亚那(GF)\",\"value\":\"GF\"},{\"label\":\"芬兰(FI)\",\"value\":\"FI\"},{\"label\":\"斐济(FJ)\",\"value\":\"FJ\"},{\"label\":\"马尔维纳斯群岛（福克兰）(FK)\",\"value\":\"FK\"},{\"label\":\"密克罗尼西亚(FM)\",\"value\":\"FM\"},{\"label\":\"法罗群岛(FO)\",\"value\":\"FO\"},{\"label\":\"法属波利尼西亚(PF)\",\"value\":\"PF\"},{\"label\":\"法国(FR)\",\"value\":\"FR\"},{\"label\":\"冈比亚(GM)\",\"value\":\"GM\"},{\"label\":\"加蓬(GA)\",\"value\":\"GA\"},{\"label\":\"格鲁吉亚(GE)\",\"value\":\"GE\"},{\"label\":\"加纳(GH)\",\"value\":\"GH\"},{\"label\":\"直布罗陀(GI)\",\"value\":\"GI\"},{\"label\":\"格林纳达(GD)\",\"value\":\"GD\"},{\"label\":\"格陵兰(GL)\",\"value\":\"GL\"},{\"label\":\"德国(DE)\",\"value\":\"DE\"},{\"label\":\"瓜德罗普(GP)\",\"value\":\"GP\"},{\"label\":\"关岛(GU)\",\"value\":\"GU\"},{\"label\":\"希腊(GR)\",\"value\":\"GR\"},{\"label\":\"危地马拉(GT)\",\"value\":\"GT\"},{\"label\":\"几内亚(GN)\",\"value\":\"GN\"},{\"label\":\"圭亚那(GY)\",\"value\":\"GY\"},{\"label\":\"海地(HT)\",\"value\":\"HT\"},{\"label\":\"洪都拉斯(HN)\",\"value\":\"HN\"},{\"label\":\"克罗地亚(HR)\",\"value\":\"HR\"},{\"label\":\"匈牙利(HU)\",\"value\":\"HU\"},{\"label\":\"冰岛(IS)\",\"value\":\"IS\"},{\"label\":\"印度尼西亚(ID)\",\"value\":\"ID\"},{\"label\":\"印度(IN)\",\"value\":\"IN\"},{\"label\":\"英属印度洋领地(IO)\",\"value\":\"IO\"},{\"label\":\"伊朗(IR)\",\"value\":\"IR\"},{\"label\":\"以色列(IL)\",\"value\":\"IL\"},{\"label\":\"意大利(IT)\",\"value\":\"IT\"},{\"label\":\"科特迪瓦(CI)\",\"value\":\"CI\"},{\"label\":\"伊拉克(IQ)\",\"value\":\"IQ\"},{\"label\":\"日本(JP)\",\"value\":\"JP\"},{\"label\":\"牙买加(JM)\",\"value\":\"JM\"},{\"label\":\"约旦(JO)\",\"value\":\"JO\"},{\"label\":\"肯尼亚(KE)\",\"value\":\"KE\"},{\"label\":\"吉尔吉斯斯坦(KG)\",\"value\":\"KG\"},{\"label\":\"朝鲜(KP)\",\"value\":\"KP\"},{\"label\":\"基里巴斯(KI)\",\"value\":\"KI\"},{\"label\":\"韩国(KR)\",\"value\":\"KR\"},{\"label\":\"圣诞岛(CX)\",\"value\":\"CX\"},{\"label\":\"科威特(KW)\",\"value\":\"KW\"},{\"label\":\"哈萨克斯坦(KZ)\",\"value\":\"KZ\"},{\"label\":\"老挝(LA)\",\"value\":\"LA\"},{\"label\":\"黎巴嫩(LB)\",\"value\":\"LB\"},{\"label\":\"拉脱维亚(LV)\",\"value\":\"LV\"},{\"label\":\"立陶宛(LT)\",\"value\":\"LT\"},{\"label\":\"利比里亚(LR)\",\"value\":\"LR\"},{\"label\":\"斯洛伐克(SK)\",\"value\":\"SK\"},{\"label\":\"列支敦士登(LI)\",\"value\":\"LI\"},{\"label\":\"莱索托(LS)\",\"value\":\"LS\"},{\"label\":\"卢森堡(LU)\",\"value\":\"LU\"},{\"label\":\"利比亚(LY)\",\"value\":\"LY\"},{\"label\":\"马达加斯加(MG)\",\"value\":\"MG\"},{\"label\":\"马提尼克(MQ)\",\"value\":\"MQ\"},{\"label\":\"摩尔多瓦(MD)\",\"value\":\"MD\"},{\"label\":\"马约特(YT)\",\"value\":\"YT\"},{\"label\":\"蒙古(MN)\",\"value\":\"MN\"},{\"label\":\"蒙特塞拉特(MS)\",\"value\":\"MS\"},{\"label\":\"马拉维(MW)\",\"value\":\"MW\"},{\"label\":\"马其顿(MK)\",\"value\":\"MK\"},{\"label\":\"马里(ML)\",\"value\":\"ML\"},{\"label\":\"摩纳哥(MC)\",\"value\":\"MC\"},{\"label\":\"摩洛哥(MA)\",\"value\":\"MA\"},{\"label\":\"毛里求斯(MU)\",\"value\":\"MU\"},{\"label\":\"毛里塔尼亚(MR)\",\"value\":\"MR\"},{\"label\":\"马耳他(MT)\",\"value\":\"MT\"},{\"label\":\"阿曼(OM)\",\"value\":\"OM\"},{\"label\":\"马尔代夫(MV)\",\"value\":\"MV\"},{\"label\":\"墨西哥(MX)\",\"value\":\"MX\"},{\"label\":\"马来西亚(MY)\",\"value\":\"MY\"},{\"label\":\"莫桑比克(MZ)\",\"value\":\"MZ\"},{\"label\":\"新喀里多尼亚(NC)\",\"value\":\"NC\"},{\"label\":\"纽埃(NU)\",\"value\":\"NU\"},{\"label\":\"诺福克岛(NF)\",\"value\":\"NF\"},{\"label\":\"尼日尔(NE)\",\"value\":\"NE\"},{\"label\":\"瓦努阿图(VU)\",\"value\":\"VU\"},{\"label\":\"尼日利亚(NG)\",\"value\":\"NG\"},{\"label\":\"荷兰(NL)\",\"value\":\"NL\"},{\"label\":\"挪威(NO)\",\"value\":\"NO\"},{\"label\":\"尼泊尔(NP)\",\"value\":\"NP\"},{\"label\":\"瑙鲁(NR)\",\"value\":\"NR\"},{\"label\":\"苏里南(SR)\",\"value\":\"SR\"},{\"label\":\"新西兰(NZ)\",\"value\":\"NZ\"},{\"label\":\"皮特凯恩群岛(PN)\",\"value\":\"PN\"},{\"label\":\"秘鲁(PE)\",\"value\":\"PE\"},{\"label\":\"中国南沙群岛(XS)\",\"value\":\"XS\"},{\"label\":\"巴基斯坦(PK)\",\"value\":\"PK\"},{\"label\":\"波兰(PL)\",\"value\":\"PL\"},{\"label\":\"巴拿马(PA)\",\"value\":\"PA\"},{\"label\":\"葡萄牙(PT)\",\"value\":\"PT\"},{\"label\":\"巴布亚新几内亚(PG)\",\"value\":\"PG\"},{\"label\":\"帕劳(PW)\",\"value\":\"PW\"},{\"label\":\"争议领土(XX)\",\"value\":\"XX\"},{\"label\":\"几内亚比绍(GW)\",\"value\":\"GW\"},{\"label\":\"卡塔尔(QA)\",\"value\":\"QA\"},{\"label\":\"留尼汪(RE)\",\"value\":\"RE\"},{\"label\":\"马绍尔群岛(MH)\",\"value\":\"MH\"},{\"label\":\"罗马尼亚(RO)\",\"value\":\"RO\"},{\"label\":\"菲律宾(PH)\",\"value\":\"PH\"},{\"label\":\"波多黎各(PR)\",\"value\":\"PR\"},{\"label\":\"俄罗斯(RU)\",\"value\":\"RU\"},{\"label\":\"卢旺达(RW)\",\"value\":\"RW\"},{\"label\":\"沙特阿拉伯(SA)\",\"value\":\"SA\"},{\"label\":\"圣皮埃尔和密克隆群岛(PM)\",\"value\":\"PM\"},{\"label\":\"圣基茨和尼维斯(KN)\",\"value\":\"KN\"},{\"label\":\"塞舌尔(SC)\",\"value\":\"SC\"},{\"label\":\"南非(ZA)\",\"value\":\"ZA\"},{\"label\":\"塞内加尔(SN)\",\"value\":\"SN\"},{\"label\":\"圣赫勒拿(SH)\",\"value\":\"SH\"},{\"label\":\"斯洛文尼亚(SI)\",\"value\":\"SI\"},{\"label\":\"塞拉利昂(SL)\",\"value\":\"SL\"},{\"label\":\"圣马力诺(SM)\",\"value\":\"SM\"},{\"label\":\"新加坡(SG)\",\"value\":\"SG\"},{\"label\":\"索马里(SO)\",\"value\":\"SO\"},{\"label\":\"西班牙(ES)\",\"value\":\"ES\"},{\"label\":\"圣卢西亚(LC)\",\"value\":\"LC\"},{\"label\":\"苏丹(SD)\",\"value\":\"SD\"},{\"label\":\"瑞典(SE)\",\"value\":\"SE\"},{\"label\":\"南乔治亚岛和南桑威奇群岛(GS)\",\"value\":\"GS\"},{\"label\":\"叙利亚(SY)\",\"value\":\"SY\"},{\"label\":\"瑞士(CH)\",\"value\":\"CH\"},{\"label\":\"特立尼达和多巴哥(TT)\",\"value\":\"TT\"},{\"label\":\"泰国(TH)\",\"value\":\"TH\"},{\"label\":\"塔吉克斯坦(TJ)\",\"value\":\"TJ\"},{\"label\":\"特克斯和凯科斯群岛(TC)\",\"value\":\"TC\"},{\"label\":\"托克劳(TK)\",\"value\":\"TK\"},{\"label\":\"汤加(TO)\",\"value\":\"TO\"},{\"label\":\"多哥(TG)\",\"value\":\"TG\"},{\"label\":\"圣多美和普林西比(ST)\",\"value\":\"ST\"},{\"label\":\"突尼斯(TN)\",\"value\":\"TN\"},{\"label\":\"东帝汶(TL)\",\"value\":\"TL\"},{\"label\":\"土耳其(TR)\",\"value\":\"TR\"},{\"label\":\"图瓦卢(TV)\",\"value\":\"TV\"},{\"label\":\"台湾(TW)\",\"value\":\"TW\"},{\"label\":\"土库曼斯坦(TM)\",\"value\":\"TM\"},{\"label\":\"坦桑尼亚(TZ)\",\"value\":\"TZ\"},{\"label\":\"乌干达(UG)\",\"value\":\"UG\"},{\"label\":\"英国(GB)\",\"value\":\"GB\"},{\"label\":\"乌克兰(UA)\",\"value\":\"UA\"},{\"label\":\"美国(US)\",\"value\":\"US\"},{\"label\":\"布基纳法索(BF)\",\"value\":\"BF\"},{\"label\":\"乌拉圭(UY)\",\"value\":\"UY\"},{\"label\":\"乌兹别克斯坦(UZ)\",\"value\":\"UZ\"},{\"label\":\"圣文森特和格林纳丁斯(VC)\",\"value\":\"VC\"},{\"label\":\"委内瑞拉(VE)\",\"value\":\"VE\"},{\"label\":\"英属维京群岛(VG)\",\"value\":\"VG\"},{\"label\":\"越南(VN)\",\"value\":\"VN\"},{\"label\":\"美属维京群岛(VI)\",\"value\":\"VI\"},{\"label\":\"梵蒂冈(VA)\",\"value\":\"VA\"},{\"label\":\"纳米比亚(NA)\",\"value\":\"NA\"},{\"label\":\"瓦利斯和富图纳(WF)\",\"value\":\"WF\"},{\"label\":\"西撒哈拉(EH)\",\"value\":\"EH\"},{\"label\":\"萨摩亚(WS)\",\"value\":\"WS\"},{\"label\":\"斯威士兰(SZ)\",\"value\":\"SZ\"},{\"label\":\"联合国中立区(XD)\",\"value\":\"XD\"},{\"label\":\"伊拉克沙特阿拉伯中立区(XE)\",\"value\":\"XE\"},{\"label\":\"也门(YE)\",\"value\":\"YE\"},{\"label\":\"赞比亚(ZM)\",\"value\":\"ZM\"},{\"label\":\"津巴布韦(ZW)\",\"value\":\"ZW\"},{\"label\":\"香港(HK)\",\"value\":\"HK\"},{\"label\":\"塞浦路斯(CY)\",\"value\":\"CY\"},{\"label\":\"法属南部领地(TF)\",\"value\":\"TF\"},{\"label\":\"美国本土外小岛屿(UM)\",\"value\":\"UM\"},{\"label\":\"巴勒斯坦(PS)\",\"value\":\"PS\"},{\"label\":\"南极洲(AQ)\",\"value\":\"AQ\"},{\"label\":\"布韦岛(BV)\",\"value\":\"BV\"},{\"label\":\"赫德岛和麦克唐纳群岛(HM)\",\"value\":\"HM\"},{\"label\":\"斯瓦尔巴群岛和扬马延岛(SJ)\",\"value\":\"SJ\"},{\"label\":\"圣巴泰勒米岛(BL)\",\"value\":\"BL\"},{\"label\":\"南苏丹(SS)\",\"value\":\"SS\"},{\"label\":\"荷兰加勒比区(BQ)\",\"value\":\"BQ\"},{\"label\":\"库拉索(CW)\",\"value\":\"CW\"},{\"label\":\"荷属圣马丁(SX)\",\"value\":\"SX\"},{\"label\":\"阿鲁巴(AW)\",\"value\":\"AW\"},{\"label\":\"布隆迪(BI)\",\"value\":\"BI\"},{\"label\":\"根西岛(GG)\",\"value\":\"GG\"},{\"label\":\"马恩岛(IM)\",\"value\":\"IM\"},{\"label\":\"泽西岛(JE)\",\"value\":\"JE\"},{\"label\":\"尼加拉瓜(NI)\",\"value\":\"NI\"},{\"label\":\"巴拉圭(PY)\",\"value\":\"PY\"},{\"label\":\"UN001(UN001)\",\"value\":\"UN001\"},{\"label\":\"未知国家(unknown)\",\"value\":\"unknown\"},{\"label\":\"其他国家(ZZ)\",\"value\":\"ZZ\"},{\"label\":\"荷属安的列斯群岛(AN)\",\"value\":\"AN\"},{\"label\":\"419(419)\",\"value\":\"419\"},{\"label\":\"纳戈尔诺-卡拉巴赫(NK)\",\"value\":\"NK\"},{\"label\":\"法国_IF(IF)\",\"value\":\"IF\"},{\"label\":\"南斯拉夫联盟共和国(YU)\",\"value\":\"YU\"},{\"label\":\"英国(UK)\",\"value\":\"UK\"},{\"label\":\"东帝汶(TP)\",\"value\":\"TP\"},{\"label\":\"科索沃(YK)\",\"value\":\"YK\"},{\"label\":\"欧盟(EU)\",\"value\":\"EU\"},{\"label\":\"未知国家(N/A)\",\"value\":\"N/A\"},{\"label\":\"亚太地区(AP)\",\"value\":\"AP\"},{\"label\":\"匿名代理(A1)\",\"value\":\"A1\"},{\"label\":\"卫星线路(A2)\",\"value\":\"A2\"},{\"label\":\"其他国家(O1)\",\"value\":\"O1\"},{\"label\":\"拉美地区(LM)\",\"value\":\"LM\"},{\"label\":\"北美地区(NM)\",\"value\":\"NM\"},{\"label\":\"非洲地区(AC)\",\"value\":\"AC\"},{\"label\":\"法国本土(FX)\",\"value\":\"FX\"},{\"label\":\"刚果(CG)\",\"value\":\"CG\"},{\"label\":\"圣马丁(MF)\",\"value\":\"MF\"}]";

    public InfoResult getOperationReport(OperationReportParam param) {
        getHuaweiProfitRoiRate(param);
        InfoResult infoResult = new InfoResult();
        Map<String,Object> map = new HashMap<>();
        String key = param.getKey();
        if (!Strings.isNullOrEmpty(key)) {
            String[] strings = key.split("&");
            param.setIndex(strings[0]);
            param.setSymbol(strings[1]);
            param.setNumber(StringUtil.getDouble(strings[2]));
        }
        List<OperationReportDTO> reportList = new ArrayList<>();
        //自定义时段
        if (param.getCustom_date() != null && param.getCustom_date().size() > 0) {
            for (int i = 0;i < param.getCustom_date().size();i+=2) {
                param.setStart_date(param.getCustom_date().get(i));
                param.setEnd_date(param.getCustom_date().get(i+1));
                reportList.addAll(dnwxBiAdtMapper.getOperationReport(param).getResult());
            }
        }else {
            Page<OperationReportDTO> reportPage = dnwxBiAdtMapper.getOperationReport(param);
            reportList = reportPage.getResult();
        }
        //自定义时段无汇总
        if (param.getCustom_date() == null || param.getCustom_date().size() == 0) {
            OperationReportDTO reportDTO = dnwxBiAdtMapper.getOperationSummary(param);
            //汇总ltv相关字段
            buildTotalLtvs(param, reportList, reportDTO);
            map.put("total",reportDTO);
        }
        //手动分页
        int totalSize = reportList.size();
        map.put("totalSize",totalSize);
        reportList = reportList.stream().skip((param.getStart()-1)*param.getLimit()).limit(param.getLimit()).collect(Collectors.toList());
        convertAppAndCountry(reportList,param);
        convertDateStr(reportList,param);

        // 20240516 sunwf 需要join adv_platform_pagedata_info 表，拿到oppo的平均使用时长并展示
//        dataPostProcess(reportList, param);

        infoResult.setRet(1);
        infoResult.setMsg("查询成功");
        map.put("list",reportList);
        infoResult.setData(map);
        return infoResult;
    }

    /**
     * 汇总ltv相关字段
     * @param param 请求参数
     * @param reportList 查询的所有结果数据集
     * @param reportDTO 非ltv相关字段的汇总数据集
     */
    private void buildTotalLtvs(OperationReportParam param, List<OperationReportDTO> reportList, OperationReportDTO reportDTO) {
        //当分组维度非 媒体或渠道，不进行汇总 ltv相关字段
        if (CollectionUtils.isEmpty(param.getGroup()) || (!param.getGroup().contains("ad_platform") && !param.getGroup().contains("channel"))) {
            return;
        }
        //需要汇总的数据为空，不进行处理
        if (reportList == null || reportDTO == null) {
            return;
        }
        //总数
        int totalSize = reportList.size();
        //汇总政策后LTV和政策后LTV1
        double totalPolicyLtv = 0.00;
        double totalPolicyLtv1 = 0.00;
        //ltv
        double totalLtv = 0.00;
        //lt
        double totalLt = 0.00;
        //estimatedProfit
        double totalProfit = 0.00;
        for (OperationReportDTO dto : reportList) {
            if (!StringUtils.isNullOrEmpty(dto.getPolicyLtv())) { // 排除"null"字符串（如果设置为排除）且不为空
                double policyLtv = Double.parseDouble(dto.getPolicyLtv());
                totalPolicyLtv += policyLtv;
            } else {
                dto.setPolicyLtv("0.00");
            }
            if (!StringUtils.isNullOrEmpty(dto.getPolicyLtv1())) {
                double policyLtv1 = Double.parseDouble(dto.getPolicyLtv1());
                totalPolicyLtv1 += policyLtv1;
            } else {
                dto.setPolicyLtv1("0.00");
            }
            if (!StringUtils.isNullOrEmpty(dto.getLtv())) {
                double ltv = Double.parseDouble(dto.getLtv());
                totalLtv += ltv;
            }
            if (!StringUtils.isNullOrEmpty(dto.getLt())) {
                double lt = Double.parseDouble(dto.getLt());
                totalLt += lt;
            }
            if (!StringUtils.isNullOrEmpty(dto.getEstimated_profit())) {
                double profit = Double.parseDouble(dto.getEstimated_profit());
                totalProfit += profit;
            }
        }
        if (totalSize == 0) {
            reportDTO.setPolicyLtv("0.00");
            reportDTO.setPolicyLtv1("0.00");
            reportDTO.setEstimated_profit("0.00");
            reportDTO.setLtv("0.00");
            reportDTO.setLt("0.00");
        } else {
            reportDTO.setPolicyLtv(String.format("%.2f", totalPolicyLtv / totalSize));
            reportDTO.setPolicyLtv1(String.format("%.2f", totalPolicyLtv1 / totalSize));
            reportDTO.setLtv(String.format("%.2f", totalLtv / totalSize));
            reportDTO.setLt(String.format("%.2f", totalLt / totalSize));
            reportDTO.setEstimated_profit(String.format("%.2f", totalProfit / totalSize));
        }
    }

    private void dataPostProcess(List<OperationReportDTO> reportList, OperationReportParam param) {
        // 第一期 限制条件
        // 1、媒体为oppo
        // 2  group 需要包含app, "app", "ad_platform", "channel" 这三个选项
        // 3  不支持自定义时段
        List group = param.getGroup();
        if (param.getAd_platform() != null && param.getAd_platform().size()== 1 && param.getAd_platform().get(0).equals("oppo")
                && !CollectionUtils.isEmpty(group) && param.getCustom_date() == null
                && group.contains("app") && group.contains("ad_platform")
                && group.contains("channel")) {
            if (group.contains("day")) {
                String sql = "SELECT t1.tdate, t1.platform, t1.appid, t1.tappid, t2.channel,SEC_TO_TIME(avg(TIME_TO_SEC(avg_duration))) avg_duration " +
                        " FROM `adv_platform_pagedata_info` t1\n" +
                        "left join adv_platform_app_info t2 on t1.appid = t2.appid and t1.tappid = t2.tappid\n" +
                        "where t1.platform='oppo' and t1.tdate between '"+param.getStart_date()+"' and '"+param.getEnd_date()+"' \n" +
                        "GROUP BY t1.tdate, t1.platform, t1.appid, t2.channel";
                List<Map<String, Object>> maps = adMapper.queryListMap(sql);
                Map<String, String> durationMap = maps.stream().collect(Collectors.toMap(x ->
                                BlankUtils.getString(x.get("tdate"))
                                        + BlankUtils.getString(x.get("platform"))
                                        + BlankUtils.getString(x.get("appid"))
                                        + BlankUtils.getString(x.get("channel")),
                        x -> BlankUtils.getString(x.get("avg_duration")), (x, y) -> x));
                reportList.forEach(report -> {
                    String mapKey = report.getDay().substring(0, 10) + report.getAd_platform()
                            + report.getAppId() + report.getChannel();
                    report.setAvg_duration(durationMap.get(mapKey));
                });
            } else if (group.contains("week")) {

            } else if (group.contains("month")) {

            }  else if (group.contains("beek")) {

            } else {
                String sql = "SELECT t1.platform, t1.appid, t1.tappid, t2.channel,SEC_TO_TIME(avg(TIME_TO_SEC(avg_duration))) avg_duration " +
                        "FROM `adv_platform_pagedata_info` t1\n" +
                        "left join adv_platform_app_info t2 on t1.appid = t2.appid and t1.tappid = t2.tappid\n" +
                        "where t1.platform='oppo' and t1.tdate between '"+param.getStart_date()+"' and '"+param.getEnd_date()+"' \n" +
                        "GROUP BY t1.platform, t1.appid, t2.channel";
                List<Map<String, Object>> maps = adMapper.queryListMap(sql);
                Map<String, String> durationMap = maps.stream().collect(Collectors.toMap(x ->
                                 BlankUtils.getString(x.get("platform"))
                                        + BlankUtils.getString(x.get("appid"))
                                        + BlankUtils.getString(x.get("channel")),
                        x -> BlankUtils.getString(x.get("avg_duration")), (x, y) -> x));
                reportList.forEach(report -> {
                    String mapKey = report.getAd_platform() + report.getAppId() + report.getChannel();
                    report.setAvg_duration(durationMap.get(mapKey));
                });
            }
        }
    }

    public InfoResult getOperationReportNotLimit(OperationReportParam param) {
        getHuaweiProfitRoiRate(param);
        InfoResult infoResult = new InfoResult();
        String key = param.getKey();
        if (!Strings.isNullOrEmpty(key)) {
            String[] strings = key.split("&");
            param.setIndex(strings[0]);
            param.setSymbol(strings[1]);
            param.setNumber(StringUtil.getDouble(strings[2]));
        }
        List<OperationReportDTO> reportList = new ArrayList<>();
        //自定义时段
        if (param.getCustom_date() != null && param.getCustom_date().size() > 0) {
            for (int i = 0;i < param.getCustom_date().size();i+=2) {
                param.setStart_date(param.getCustom_date().get(i));
                param.setEnd_date(param.getCustom_date().get(i+1));
                reportList.addAll(dnwxBiAdtMapper.getOperationReport(param).getResult());
            }
        }else {
            Page<OperationReportDTO> reportPage = dnwxBiAdtMapper.getOperationReport(param);
            reportList = reportPage.getResult();
        }
        convertDateStr(reportList,param);
        //自定义时段无汇总
        if (param.getCustom_date() == null || param.getCustom_date().size() == 0) {
            OperationReportDTO reportDTO = dnwxBiAdtMapper.getOperationSummary(param);
            if(null!=reportDTO){
                buildTotalLtvs(param, reportList, reportDTO);
                reportDTO.setDay("汇总");
            }
            if(null!=reportList&&reportList.size()>0){
                reportList.add(reportDTO);
            }
        }
        convertAppAndCountry(reportList,param);
        infoResult.setRet(1);
        infoResult.setMsg("查询成功");
        infoResult.setData(reportList);
        return infoResult;
    }
    public void getOperationReportExport(OperationReportParam param, HttpServletResponse response) {
        getHuaweiProfitRoiRate(param);
        PageHelper.startPage(PAGE_NUM, MAX_PAGE_SIZE);
        String key = param.getKey();
        if (!Strings.isNullOrEmpty(key)) {
            String[] strings = key.split("&");
            param.setIndex(strings[0]);
            param.setSymbol(strings[1]);
            param.setNumber(StringUtil.getDouble(strings[2]));
        }
        List<OperationReportDTO> reportList = new ArrayList<>();
        //自定义时段
        if (param.getCustom_date() != null && param.getCustom_date().size() > 0) {
            for (int i = 0;i < param.getCustom_date().size();i+=2) {
                param.setStart_date(param.getCustom_date().get(i));
                param.setEnd_date(param.getCustom_date().get(i+1));
                reportList.addAll(dnwxBiAdtMapper.getOperationReport(param).getResult());
            }
        }else {
            Page<OperationReportDTO> reportPage = dnwxBiAdtMapper.getOperationReport(param);
            reportList = reportPage.getResult();
        }
        if (CollectionUtils.isEmpty(reportList)) {
            return ;
        }

        convertAppAndCountry(reportList, param);
        convertDateStr(reportList,param);
        String value = param.getValue();
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        if (!StringUtils.isNullOrEmpty(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0], s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        dataPostProcess(reportList, param);
        ExportExcelUtil.exportXLSX2(response, reportList, headerMap, "联运产品数据报表_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }

    //获取华为毛利roi率
    public void getHuaweiProfitRoiRate(OperationReportParam param){
        if (param.getHuawei_profit_roi_rate() == null) {
            Double huawei_profit_roi_rate = (Double) redisTemplate.opsForValue().get(huawei_profit_roi_rate_key);
            param.setHuawei_profit_roi_rate(huawei_profit_roi_rate);
        }
        if (param.getHuawei_profit_roi_rate() == null) {
            param.setHuawei_profit_roi_rate(0.5);
        }
    }

    public InfoResult getOperationReport_v2(OperationReportParam param) {
        InfoResult infoResult = new InfoResult();
        Map<String,Object> map = new HashMap<>();
        List<OperationReportDTO> reportList = new ArrayList<>();
        //自定义时段
        if (param.getCustom_date() != null && param.getCustom_date().size() > 0) {
            for (int i = 0;i < param.getCustom_date().size();i+=2) {
                param.setStart_date(param.getCustom_date().get(i));
                param.setEnd_date(param.getCustom_date().get(i+1));
                reportList.addAll(dnwxBiAdtMapper.getOperationReport_v2(param).getResult());
            }
        }else {
            Page<OperationReportDTO> reportPage = dnwxBiAdtMapper.getOperationReport_v2(param);
            reportList = reportPage.getResult();
        }
        //自定义时段无汇总
        if (param.getCustom_date() == null || param.getCustom_date().size() == 0) {
            OperationReportDTO reportDTO = dnwxBiAdtMapper.getOperationSummary_v2(param);
            map.put("total",reportDTO);
        }
        //手动分页
        int totalSize = reportList.size();
        map.put("totalSize",totalSize);
        reportList = reportList.stream().skip((param.getStart()-1)*param.getLimit()).limit(param.getLimit()).collect(Collectors.toList());
        convertDateStr(reportList,param);

        infoResult.setRet(1);
        infoResult.setMsg("查询成功");
        map.put("list",reportList);
        infoResult.setData(map);
        return infoResult;
    }

    public void getOperationReportExport_v2(OperationReportParam param, HttpServletResponse response) {
        PageHelper.startPage(PAGE_NUM, MAX_PAGE_SIZE);
        List<OperationReportDTO> reportList = new ArrayList<>();
        //自定义时段
        if (param.getCustom_date() != null && param.getCustom_date().size() > 0) {
            for (int i = 0;i < param.getCustom_date().size();i+=2) {
                param.setStart_date(param.getCustom_date().get(i));
                param.setEnd_date(param.getCustom_date().get(i+1));
                reportList.addAll(dnwxBiAdtMapper.getOperationReport_v2(param).getResult());
            }
        }else {
            Page<OperationReportDTO> reportPage = dnwxBiAdtMapper.getOperationReport_v2(param);
            reportList = reportPage.getResult();
        }
        if (CollectionUtils.isEmpty(reportList)) {
            return ;
        }

        convertDateStr(reportList,param);
        String value = param.getValue();
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        if (!StringUtils.isNullOrEmpty(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0], s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        dataPostProcess(reportList, param);
        ExportExcelUtil.exportXLSX2(response, reportList, headerMap, "联运产品数据报表v2_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }


    /**
     * 日期转换为标准形式（联运报表）
     * @param param
     */
    public void convertDateStr(List<OperationReportDTO> list,OperationReportParam param){
        try {
            for (OperationReportDTO op : list) {
                if (param.getGroup() != null && param.getGroup().contains("day")) {
                    Date date = new SimpleDateFormat("yyyy-MM-dd").parse(op.getDay());
                    String xinqi = DateUtil.dateToWeek(date);
                    op.setDay(op.getDay()+"("+xinqi+")");
                }else if (param.getGroup() != null &&  param.getGroup().contains("week")) {
                    String[] split = op.getDay().split("-");
                    if (split.length >= 2) {
                        Integer year = Integer.parseInt(split[0]);
                        Integer week = Integer.parseInt(split[1]);
                        String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week);
                        String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week);
                        op.setDay(year+"年第"+week+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                    }
                }else if (param.getGroup() != null &&  param.getGroup().contains("month")) {
                    //不做处理
                }else if (param.getGroup() != null &&  param.getGroup().contains("beek")) {
                    String[] split = op.getDay().split("-");
                    if (split.length >= 3) {
                        Integer year = Integer.parseInt(split[0]);
                        Integer week1 = Integer.parseInt(split[1]);
                        Integer week2 = Integer.parseInt(split[2]);
                        String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week1);
                        String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week2);
                        op.setDay(year+"年第"+week1+"-"+week2+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                    }
                }else if (param.getCustom_date() == null || param.getCustom_date().size() == 0) {
                    op.setDay(param.getStart_date()+"至"+param.getEnd_date());
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 日期转换为标准形式（直投报表）
     * @param param
     */
    public void convertDateStrDirect(List<DirectReportDTO> list,DirectReportParam param){
        try {
            for (DirectReportDTO op : list) {
                if (param.getGroup() != null && param.getGroup().contains("day")) {
                    Date date = new SimpleDateFormat("yyyy-MM-dd").parse(op.getDay());
                    String xinqi = DateUtil.dateToWeek(date);
                    op.setDay(op.getDay()+"("+xinqi+")");
                }else if (param.getGroup() != null &&  param.getGroup().contains("week")) {
                    String[] split = op.getDay().split("-");
                    if (split.length >= 2) {
                        Integer year = Integer.parseInt(split[0]);
                        Integer week = Integer.parseInt(split[1]);
                        String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week);
                        String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week);
                        op.setDay(year+"年第"+week+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                    }
                }else if (param.getGroup() != null &&  param.getGroup().contains("month")) {
                    //不做处理
                }else if (param.getGroup() != null &&  param.getGroup().contains("beek")) {
                    String[] split = op.getDay().split("-");
                    if (split.length >= 3) {
                        Integer year = Integer.parseInt(split[0]);
                        Integer week1 = Integer.parseInt(split[1]);
                        Integer week2 = Integer.parseInt(split[2]);
                        String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week1);
                        String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week2);
                        op.setDay(year+"年第"+week1+"-"+week2+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                    }
                }else if (param.getCustom_date() == null || param.getCustom_date().size() == 0) {
                    op.setDay(param.getStart_date()+"至"+param.getEnd_date());
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    public InfoResult getSpendReport(SpendReportParam param, String username) {
        InfoResult infoResult = new InfoResult();
        String key = param.getKey();
        if (!Strings.isNullOrEmpty(key)) {
            String[] strings = key.split("&");
            param.setIndex(strings[0]);
            param.setSymbol(strings[1]);
            param.setNumber(StringUtil.getDouble(strings[2]));
        }
        if(param.getOrder_str() == null){
            param.setOrder_str("spend desc");
        }
        if (param.getGroup() == null) {
            param.setGroup(new ArrayList<>());
        }
        //处理账号分组
        if (!CollectionUtils.isEmpty(param.getAccount_group()) && CollectionUtils.isEmpty(param.getAccount())) {
            param.setAccount(handleAccountGroup(param));
        }
        Map<String,Object> map = new HashMap<>();
        List<String> groups = param.getGroup();
        List<SpendReportDTO> reportList = new ArrayList<>();

        //自定义时段
        if (param.getCustom_date() != null && param.getCustom_date().size() > 0) {
            for (int i = 0;i < param.getCustom_date().size();i+=2) {
                param.setStart_date(param.getCustom_date().get(i));
                param.setEnd_date(param.getCustom_date().get(i+1));
                reportList.addAll(dnwxBiAdtMapper.getSpendReport(param).getResult());
            }
            map.put("totalSize",reportList.size());
            //手动分页
            reportList = reportList.stream().skip((param.getStart()-1)*param.getLimit()).limit(param.getLimit()).
                    collect(Collectors.toList());
        }else {
            PageHelper.startPage(param.getStart(),param.getLimit());
            Page<SpendReportDTO> reportPage = dnwxBiAdtMapper.getSpendReport(param);
            reportList = reportPage.getResult();
            map.put("totalSize",reportPage.getTotal());
        }

        //处理日期格式
        convertSpendReportDate(reportList,param);

        //汇总字段
        SpendReportDTO spendReportDTO ;

        if (reportList.size() > 0) {
            if(groups != null && !groups.contains("media")){
                groups.add("media");
            }
            //自定义时段无汇总
            if (param.getCustom_date() == null || param.getCustom_date().size() == 0) {
                spendReportDTO = dnwxBiAdtMapper.getSpendReportSummary(param);
                addChinaReportIndicator(spendReportDTO, groups, username);
                map.put("total",spendReportDTO);
            }else{
                //自定义时段无汇总对象,自主根据集合遍历汇总消耗
                spendReportDTO = new SpendReportDTO();
                spendReportDTO.setSpend(0.00);
                for (SpendReportDTO s : reportList) {
                    spendReportDTO.setSpend(spendReportDTO.getSpend()+s.getSpend());
                }
            }
            if (spendReportDTO != null) {
                spendReportDTO.setBp(null);
                spendReportDTO.setMedia(null);
                convertAppAndCountry(reportList, param);
                addChinaReportListIndicator(reportList, groups, username, spendReportDTO.getSpend());
            }
            if(groups != null && groups.contains("type")) {
                List<Map<String,Object>> accountTypes = tfxtMapper.selectAccountType();
                Map<String,String> accountTypeMap = accountTypes.stream()
                        .collect(Collectors.toMap(m -> m.get("id")+"", m -> m.get("type")+"", (x, y) -> y));
                for (SpendReportDTO report : reportList) {
                    String type = report.getType();
                    String str = "";
                    if (null != type) {
                        str = accountTypeMap.get(type);
                    }
                    report.setType(str);
                }
            }
            if (groups!=null && groups.contains("transferType")) {
                List<Map> transferTypeList = dnwxAdtMapper.getTransferType();
                Map<String, String> trasferTypeMap = transferTypeList.stream().collect(Collectors.toMap(map1 -> StringUtil.getString(map1.get("id")),map1 -> StringUtil.getString(map1.get("strategyName")),(k1,k2)->k2));
                for (SpendReportDTO report : reportList) {
                    report.setTransferType(trasferTypeMap.get(report.getTransferType()));
                }
            }
        }

        infoResult.setRet(1);
        infoResult.setMsg("查询成功");
        map.put("list",reportList);
        infoResult.setData(map);
        return infoResult;
    }

    public InfoResult getSpendReportNotLimit(SpendReportParam param, String username) {
        InfoResult infoResult = new InfoResult();
        String key = param.getKey();
        if (!Strings.isNullOrEmpty(key)) {
            String[] strings = key.split("&");
            param.setIndex(strings[0]);
            param.setSymbol(strings[1]);
            param.setNumber(StringUtil.getDouble(strings[2]));
        }
        if(param.getOrder_str() == null){
            param.setOrder_str("spend desc");
        }
        //防止后续获取不到报错
        if (param.getGroup() == null) {
            param.setGroup(new ArrayList<>());
        }
        //处理账号分组
        if (!CollectionUtils.isEmpty(param.getAccount_group()) && CollectionUtils.isEmpty(param.getAccount())) {
            param.setAccount(handleAccountGroup(param));
        }

        List<String> groups = param.getGroup();
        List<SpendReportDTO> reportList = new ArrayList<>();

        //自定义时段
        if (param.getCustom_date() != null && param.getCustom_date().size() > 0) {
            for (int i = 0;i < param.getCustom_date().size();i+=2) {
                param.setStart_date(param.getCustom_date().get(i));
                param.setEnd_date(param.getCustom_date().get(i+1));
                reportList.addAll(dnwxBiAdtMapper.getSpendReport(param));
            }
        }else {
            reportList = dnwxBiAdtMapper.getSpendReport(param);
        }

        //处理日期格式
        convertSpendReportDate(reportList,param);

        //汇总字段
        SpendReportDTO spendReportDTO ;

        if (reportList.size() > 0) {
            if(!groups.contains("media")){
                groups.add("media");
            }

            if(groups != null && groups.contains("type")) {
                List<Map<String,Object>> accountTypes = tfxtMapper.selectAccountType();
                Map<String,String> accountTypeMap = accountTypes.stream()
                        .collect(Collectors.toMap(m -> m.get("id")+"", m -> m.get("type")+"", (x, y) -> y));
                for (SpendReportDTO report : reportList) {
                    String type = report.getType();
                    String str = "";
                    if (null != type) {
                        str = accountTypeMap.get(type);
                    }
                    report.setType(str);
                }
            }

            List<Map> transferTypeList = dnwxAdtMapper.getTransferType();
            Map trasferTypeMap = transferTypeList.stream().collect(Collectors.toMap(map -> StringUtil.getString(map.get("id")),map -> StringUtil.getString(map.get("strategyName")),(k1,k2)->k2));

            if(param.getGroup().contains("transferType")){
                for (SpendReportDTO report : reportList) {
                    String transerType = report.getTransferType();
                    String str1 = StringUtil.getString(trasferTypeMap.get(transerType));
                    if (!BlankUtils.checkBlank(str1)) {
                        report.setTransferType(str1);
                    }
                }
            }

            //自定义时段无汇总
            if (param.getCustom_date() == null || param.getCustom_date().size() == 0) {
                spendReportDTO = dnwxBiAdtMapper.getSpendReportSummary(param);
                addChinaReportIndicator(spendReportDTO, groups, username);
            }else{
                //自定义时段无汇总对象,自主根据集合遍历汇总消耗
                spendReportDTO = new SpendReportDTO();
                spendReportDTO.setSpend(0.00);
                for (SpendReportDTO s : reportList) {
                    spendReportDTO.setSpend(spendReportDTO.getSpend()+s.getSpend());
                }
            }
            if (spendReportDTO != null) {
                spendReportDTO.setDay("汇总");
                spendReportDTO.setBp(null);
                spendReportDTO.setMedia(null);
                convertAppAndCountry(reportList, param);
                addChinaReportListIndicator(reportList, groups, username, spendReportDTO.getSpend());
                reportList.add(spendReportDTO);
            }

        }

        infoResult.setRet(1);
        infoResult.setMsg("查询成功");
        infoResult.setData(reportList);
        return infoResult;
    }


    public void exportSpendReport(SpendReportParam param,String username,HttpServletResponse response) {
        PageHelper.startPage(PAGE_NUM, MAX_PAGE_SIZE);

        String key = param.getKey();
        if (!Strings.isNullOrEmpty(key)) {
            String[] strings = key.split("&");
            param.setIndex(strings[0]);
            param.setSymbol(strings[1]);
            param.setNumber(StringUtil.getDouble(strings[2]));
        }
        if(param.getOrder_str() == null){
            param.setOrder_str("spend desc");
        }
        //防止后续获取不到报错
        if (param.getGroup() == null) {
            param.setGroup(new ArrayList<>());
        }
        //处理账号分组
        if (!CollectionUtils.isEmpty(param.getAccount_group()) && CollectionUtils.isEmpty(param.getAccount())) {
            param.setAccount(handleAccountGroup(param));
        }

        List<String> groups = param.getGroup();
        List<SpendReportDTO> reportList = new ArrayList<>();

        //自定义时段
        if (param.getCustom_date() != null && param.getCustom_date().size() > 0) {
            for (int i = 0;i < param.getCustom_date().size();i+=2) {
                param.setStart_date(param.getCustom_date().get(i));
                param.setEnd_date(param.getCustom_date().get(i+1));
                reportList.addAll(dnwxBiAdtMapper.getSpendReport(param));
            }
        }else {
            reportList = dnwxBiAdtMapper.getSpendReport(param);
        }

        //处理日期格式
        convertSpendReportDate(reportList,param);

        if (CollectionUtils.isEmpty(reportList)) {
            return ;
        }

        if (reportList.size() > 0) {
            if(!groups.contains("media")){
                groups.add("media");
            }
            //汇总字段
            SpendReportDTO spendReportDTO ;
            //自定义时段无汇总
            if (param.getCustom_date() == null || param.getCustom_date().size() == 0) {
                spendReportDTO = dnwxBiAdtMapper.getSpendReportSummary(param);
            }else{
                //自定义时段无汇总对象,自主根据集合遍历汇总消耗
                spendReportDTO = new SpendReportDTO();
                spendReportDTO.setSpend(0.00);
                for (SpendReportDTO s : reportList) {
                    spendReportDTO.setSpend(spendReportDTO.getSpend()+s.getSpend());
                }
            }
            convertAppAndCountry(reportList, param);
            addChinaReportListIndicator(reportList, groups,username,spendReportDTO.getSpend());
        }


        String value = param.getValue();
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        if (!StringUtils.isNullOrEmpty(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0], s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        if(value.contains("type")) {
            List<Map<String,Object>> accountTypes = tfxtMapper.selectAccountType();
            Map<String,String> accountTypeMap = accountTypes.stream()
                    .collect(Collectors.toMap(m -> m.get("id")+"", m -> m.get("type")+"", (x, y) -> y));
            for (SpendReportDTO report : reportList) {
                String type = report.getType();
                String str = "";
                if (null != type) {
                    str = accountTypeMap.get(type);
                }
                report.setType(str);
            }
        }
        List<Map> transferTypeList = dnwxAdtMapper.getTransferType();
        Map trasferTypeMap = transferTypeList.stream().collect(Collectors.toMap(map -> StringUtil.getString(map.get("id")),map -> StringUtil.getString(map.get("strategyName")),(k1,k2)->k2));

        if(value.contains("transferType")){
            for (SpendReportDTO report : reportList) {
                String transerType = report.getTransferType();
                String str1 = StringUtil.getString(trasferTypeMap.get(transerType));
                if (!BlankUtils.checkBlank(str1)) {
                    report.setTransferType(str1);
                }
            }
        }

        if(value.contains("bidType")) {
            for (SpendReportDTO report : reportList) {
                String type = report.getBidType();
                String str = "";
                if (null != type) {
                    if (StringUtil.getInt(type) == 1) {
                        str = "最大转化";
                    } else if (StringUtil.getInt(type) == 2) {
                        str = "非最大转化";
                    } else {
                        str = "";
                    }
                }
                report.setBidType(str);

            }
        }
        ExportExcelUtil.exportXLSX2(response, reportList, headerMap, "投放细分数据报表_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");

    }

    /**
     * 处理账户分组
     * @param spendReportParam
     * @return
     */
    private List<String> handleAccountGroup(SpendReportParam spendReportParam) {

        List<String> account  = tfxtMapper.selectAccountsByGroup(spendReportParam);

        if (account == null || account.size() == 0) {
            account = Arrays.asList("-1");
        }

        return account;
    }

    /**
     * 日期转换为标准形式(国内投放报表)
     * @param param
     */
    public void convertSpendReportDate(List<SpendReportDTO> list,SpendReportParam param){
        try {
            for (SpendReportDTO op : list) {
                if (param.getGroup() != null && param.getGroup().contains("day")) {
                    Date date = new SimpleDateFormat("yyyy-MM-dd").parse(op.getDay());
                    String xinqi = DateUtil.dateToWeek(date);
                    //导出和查询不同处理
                    if (BlankUtils.checkBlank(param.getValue())) {
                        op.setDay(op.getDay()+"("+xinqi+")");
                    }else{
                        op.setXingqi(xinqi);
                    }
                }else if (param.getGroup() != null &&  param.getGroup().contains("week")) {
                    String[] split = op.getDay().split("-");
                    if (split.length >= 2) {
                        Integer year = Integer.parseInt(split[0]);
                        Integer week = Integer.parseInt(split[1]);
                        String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week);
                        String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week);
                        op.setDay(year+"年第"+week+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                    }
                }else if (param.getGroup() != null &&  param.getGroup().contains("month")) {
                    //不做处理
                }else if (param.getGroup() != null &&  param.getGroup().contains("beek")) {
                    String[] split = op.getDay().split("-");
                    if (split.length >= 3) {
                        Integer year = Integer.parseInt(split[0]);
                        Integer week1 = Integer.parseInt(split[1]);
                        Integer week2 = Integer.parseInt(split[2]);
                        String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week1);
                        String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week2);
                        op.setDay(year+"年第"+week1+"-"+week2+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                    }
                }
                else if (param.getCustom_date() == null || param.getCustom_date().size() == 0) {
                    op.setDay(param.getStart_date()+"至"+param.getEnd_date());
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    public InfoResult getAccountSpendReport(SpendReportParam param, String username) {
        InfoResult infoResult = new InfoResult();
        Map<String,Object> map = new HashMap<>();

        PageHelper.startPage(param.getStart(),param.getLimit());
        Page<AccountSpendReportDTO> reportPage = tfxtMapper.getAccountSpendReport(param);
        List<AccountSpendReportDTO> reportList = reportPage.getResult();
        //增加账号备注
        Map<String, String> remarkMap = new HashMap<>();
        if (param.getGroup().contains("account")) {
            List<HashMap> allAccountList = tfxtMapper.getChinaPlatformAccount();
            remarkMap = allAccountList.stream()
                    .collect(Collectors.toMap(x->StringUtil.getString(x.get("account")),x->StringUtil.getString(x.get("remark")),(k1,k2)->k2));
            for (AccountSpendReportDTO a : reportList) {
                a.setAccountRemark(remarkMap.get(a.getAccount()));
            }
        }

        if(param.getGroup().contains("type")) {
            for (AccountSpendReportDTO report : reportList) {
                String type = report.getType();
                String str = "";
                if (null != type && type != "") {
                    if (StringUtil.getInt(type) == 1) {
                        str = "自运营-分包";
                    } else if (StringUtil.getInt(type) == 2) {
                        str = "代运营-分包";
                    } else if (StringUtil.getInt(type) == 3) {
                        str = "自运营-直投";
                    } else if (StringUtil.getInt(type) == 4) {
                        str = "代运营-直投";
                    } else if (StringUtil.getInt(type) == 5) {
                        str = "自运营-OPPO直投";
                    } else if (StringUtil.getInt(type) == 6) {
                        str = "自运营-VIVO直投";
                    } else if (StringUtil.getInt(type) == 7) {
                        str = "自运营-华为直投";
                    } else if (StringUtil.getInt(type) == 8) {
                        str = "自运营-小米直投";
                    } else if (StringUtil.getInt(type) == 9) {
                        str = "代运营-OPPO直投";
                    } else if (StringUtil.getInt(type) == 10) {
                        str = "代运营-VIVO直投";
                    } else if (StringUtil.getInt(type) == 11) {
                        str = "代运营-华为直投";
                    } else if (StringUtil.getInt(type) == 12) {
                        str = "代运营-小米直投";
                    } else if (StringUtil.getInt(type) == 13) {
                        str = "自运营-OPPOMJ";
                    } else if (StringUtil.getInt(type) == 14) {
                        str = "代运营-OPPOMJ";
                    } else if (StringUtil.getInt(type) == 15) {
                        str = "自运营-小米直投2";
                    }
                }
                report.setType(str);
            }
        }

        if (param.getGroup() != null && param.getGroup().contains("app_type")) {
            for (AccountSpendReportDTO report : reportList) {
                if (report.getApp_type() == null) {
                    report.setApp_type("无");
                }else if ("1".equals(report.getApp_type())) {
                    report.setApp_type("新游");
                }else {
                    report.setApp_type("老游");
                }
            }
        }

        if (reportList.size() > 0) {
            AccountSpendReportDTO spendReportDTO = tfxtMapper.getAccountSpendReportSummary(param);
            infoResult.setRet(1);
            map.put("total",spendReportDTO);
            convertAppAndCountry(reportList,param);
        }
        infoResult.setRet(1);
        infoResult.setMsg("查询成功");
        map.put("list",reportList);
        map.put("totalSize",reportPage.getTotal());
        infoResult.setData(map);
        return infoResult;
    }

    public InfoResult getAccountSpendReportNotLimit(SpendReportParam param, String username) {
        InfoResult infoResult = new InfoResult();
        Map<String,Object> map = new HashMap<>();

        List<AccountSpendReportDTO> reportList = tfxtMapper.getAccountSpendReport(param);
        //增加账号备注
        Map<String, String> remarkMap = new HashMap<>();
        if (param.getGroup().contains("account")) {
            List<HashMap> allAccountList = tfxtMapper.getChinaPlatformAccount();
            remarkMap = allAccountList.stream()
                    .collect(Collectors.toMap(x->StringUtil.getString(x.get("account")),x->StringUtil.getString(x.get("remark")),(k1,k2)->k2));
            for (AccountSpendReportDTO a : reportList) {
                a.setAccountRemark(remarkMap.get(a.getAccount()));
            }
        }

        if(param.getGroup().contains("type")) {
            for (AccountSpendReportDTO report : reportList) {
                String type = report.getType();
                String str = "";
                if (null != type && type != "") {
                    if (StringUtil.getInt(type) == 1) {
                        str = "自运营-分包";
                    } else if (StringUtil.getInt(type) == 2) {
                        str = "代运营-分包";
                    } else if (StringUtil.getInt(type) == 3) {
                        str = "自运营-直投";
                    } else if (StringUtil.getInt(type) == 4) {
                        str = "代运营-直投";
                    } else if (StringUtil.getInt(type) == 5) {
                        str = "自运营-OPPO直投";
                    } else if (StringUtil.getInt(type) == 6) {
                        str = "自运营-VIVO直投";
                    } else if (StringUtil.getInt(type) == 7) {
                        str = "自运营-华为直投";
                    } else if (StringUtil.getInt(type) == 8) {
                        str = "自运营-小米直投";
                    } else if (StringUtil.getInt(type) == 9) {
                        str = "代运营-OPPO直投";
                    } else if (StringUtil.getInt(type) == 10) {
                        str = "代运营-VIVO直投";
                    } else if (StringUtil.getInt(type) == 11) {
                        str = "代运营-华为直投";
                    } else if (StringUtil.getInt(type) == 12) {
                        str = "代运营-小米直投";
                    } else if (StringUtil.getInt(type) == 13) {
                        str = "自运营-OPPOMJ";
                    } else if (StringUtil.getInt(type) == 14) {
                        str = "代运营-OPPOMJ";
                    } else if (StringUtil.getInt(type) == 15) {
                        str = "自运营-小米直投2";
                    }
                }
                report.setType(str);
            }
        }

        if (param.getGroup() != null && param.getGroup().contains("app_type")) {
            for (AccountSpendReportDTO report : reportList) {
                if (report.getApp_type() == null) {
                    report.setApp_type("无");
                }else if ("1".equals(report.getApp_type())) {
                    report.setApp_type("新游");
                }else {
                    report.setApp_type("老游");
                }
            }
        }

        if (reportList.size() > 0) {
            AccountSpendReportDTO spendReportDTO = tfxtMapper.getAccountSpendReportSummary(param);
            infoResult.setRet(1);
            map.put("total",spendReportDTO);
            convertAppAndCountry(reportList,param);
        }
        infoResult.setRet(1);
        infoResult.setMsg("查询成功");
        infoResult.setData(reportList);
        return infoResult;
    }

    public void getAccountSpendReportExport(SpendReportParam param, HttpServletResponse response) {
        PageHelper.startPage(PAGE_NUM, MAX_PAGE_SIZE);
        Page<AccountSpendReportDTO> reportPage = tfxtMapper.getAccountSpendReport(param);
        List<AccountSpendReportDTO> reportList = reportPage.getResult();
        if (CollectionUtils.isEmpty(reportList)) {
            return ;
        }
        for(AccountSpendReportDTO report : reportList){
            String type = report.getType();
            String str = "";
            if (null != type) {
                if (StringUtil.getInt(type) == 1) {
                    str = "自运营-分包";
                } else if (StringUtil.getInt(type) == 2) {
                    str = "代运营-分包";
                } else if (StringUtil.getInt(type) == 3) {
                    str = "自运营-直投";
                } else if (StringUtil.getInt(type) == 4) {
                    str = "代运营-直投";
                } else if (StringUtil.getInt(type) == 5) {
                    str = "自运营-OPPO直投";
                } else if (StringUtil.getInt(type) == 6) {
                    str = "自运营-VIVO直投";
                } else if (StringUtil.getInt(type) == 7) {
                    str = "自运营-华为直投";
                } else if (StringUtil.getInt(type) == 8) {
                    str = "自运营-小米直投";
                } else if (StringUtil.getInt(type) == 9) {
                    str = "代运营-OPPO直投";
                } else if (StringUtil.getInt(type) == 10) {
                    str = "代运营-VIVO直投";
                } else if (StringUtil.getInt(type) == 11) {
                    str = "代运营-华为直投";
                } else if (StringUtil.getInt(type) == 12) {
                    str = "代运营-小米直投";
                } else if (StringUtil.getInt(type) == 13) {
                    str = "自运营-OPPOMJ";
                } else if (StringUtil.getInt(type) == 14) {
                    str = "代运营-OPPOMJ";
                } else if (StringUtil.getInt(type) == 15) {
                    str = "自运营-小米直投2";
                }
            }
            report.setType(str);
        }

        if (param.getGroup() != null && param.getGroup().contains("app_type")) {
            for (AccountSpendReportDTO report : reportList) {
                if (report.getApp_type() == null) {
                    report.setApp_type("无");
                }else if ("1".equals(report.getApp_type())) {
                    report.setApp_type("新游");
                }else {
                    report.setApp_type("老游");
                }
            }
        }

        convertAppAndCountry(reportList, param);

        String value = param.getValue();
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        if (!StringUtils.isNullOrEmpty(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0], s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        ExportExcelUtil.exportXLSX2(response, reportList, headerMap, "投放账号汇总报表_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }

    public InfoResult getDirectReport(DirectReportParam param) {
        InfoResult infoResult = new InfoResult();
        Map<String,Object> map = new HashMap<>();
        String choose = param.getChoose();
        String key = param.getKey();
        if (!Strings.isNullOrEmpty(key)) {
            String[] strings = key.split("&");
            param.setIndex(strings[0]);
            param.setSymbol(strings[1]);
            param.setNumber(StringUtil.getDouble(strings[2]));
        }

        List<DirectReportDTO> reportList = new ArrayList<>();

        //自定义时段
        if (param.getCustom_date() != null && param.getCustom_date().size() > 0) {
            for (int i = 0;i < param.getCustom_date().size();i+=2) {
                param.setStart_date(param.getCustom_date().get(i));
                param.setEnd_date(param.getCustom_date().get(i+1));
                reportList.addAll(dnwxBiAdtMapper.getDirectReport(param).getResult());
            }
            map.put("totalSize",reportList.size());
            //手动分页
            reportList = reportList.stream().skip((param.getStart()-1)*param.getLimit()).limit(param.getLimit()).
                    collect(Collectors.toList());
        }else {
            PageHelper.startPage(param.getStart(),param.getLimit());
            Page<DirectReportDTO> reportPage = dnwxBiAdtMapper.getDirectReport(param);
            reportList = reportPage.getResult();
            map.put("totalSize",reportPage.getTotal());
        }

        if (reportList.size() > 0) {
            //自定义时段无汇总
            if (param.getCustom_date() == null || param.getCustom_date().size() == 0) {
                DirectReportDTO reportDTO = dnwxBiAdtMapper.getDirectReportSummary(param);
                addDirectReportIndex(reportDTO,choose);
                map.put("total",reportDTO);
            }
            infoResult.setRet(1);
            convertAppAndCountry(reportList,param);
            addDirectReportListIndex(reportList,choose);
            convertDateStrDirect(reportList,param);
        }
        infoResult.setRet(1);
        infoResult.setMsg("查询成功");
        map.put("list",reportList);
        infoResult.setData(map);
        return infoResult;
    }

    /**
     * 根据条件不分页查询直投数据报表
     * @param param 查询条件
     * @return 查询结果
     */
    public InfoResult getDirectReportNotLimit(DirectReportParam param) {
        InfoResult infoResult = new InfoResult();
        String choose = param.getChoose();
        String key = param.getKey();
        if (!Strings.isNullOrEmpty(key)) {
            String[] strings = key.split("&");
            param.setIndex(strings[0]);
            param.setSymbol(strings[1]);
            param.setNumber(StringUtil.getDouble(strings[2]));
        }

        List<DirectReportDTO> reportList = new ArrayList<>();

        //自定义时段
        if (param.getCustom_date() != null && param.getCustom_date().size() > 0) {
            for (int i = 0;i < param.getCustom_date().size();i+=2) {
                param.setStart_date(param.getCustom_date().get(i));
                param.setEnd_date(param.getCustom_date().get(i+1));
                reportList.addAll(dnwxBiAdtMapper.getDirectReport(param).getResult());
            }
        }else {
            Page<DirectReportDTO> reportPage = dnwxBiAdtMapper.getDirectReport(param);
            reportList = reportPage.getResult();
        }
        if (reportList.size() > 0) {
            infoResult.setRet(1);
            convertAppAndCountry(reportList,param);
            addDirectReportListIndex(reportList,choose);
            convertDateStrDirect(reportList,param);
            //自定义时段无汇总
            if (param.getCustom_date() == null || param.getCustom_date().size() == 0) {
                DirectReportDTO reportDTO = dnwxBiAdtMapper.getDirectReportSummary(param);
                addDirectReportIndex(reportDTO,choose);
                reportDTO.setDay("汇总");
                reportList.add(reportDTO);
            }
        }
        infoResult.setRet(1);
        infoResult.setMsg("查询成功");
        infoResult.setData(reportList);
        return infoResult;
    }


    public void getDirectReportExport(DirectReportParam param, HttpServletResponse response) {
        String choose = param.getChoose();
        List<DirectReportDTO> reportList = new ArrayList<>();

        String key = param.getKey();
        if (!Strings.isNullOrEmpty(key)) {
            String[] strings = key.split("&");
            param.setIndex(strings[0]);
            param.setSymbol(strings[1]);
            param.setNumber(StringUtil.getDouble(strings[2]));
        }

        //自定义时段
        if (param.getCustom_date() != null && param.getCustom_date().size() > 0) {
            for (int i = 0;i < param.getCustom_date().size();i+=2) {
                param.setStart_date(param.getCustom_date().get(i));
                param.setEnd_date(param.getCustom_date().get(i+1));
                reportList.addAll(dnwxBiAdtMapper.getDirectReport(param).getResult());
            }
        }else {
            Page<DirectReportDTO> reportPage = dnwxBiAdtMapper.getDirectReport(param);
            reportList = reportPage.getResult();
        }
        if (CollectionUtils.isEmpty(reportList)) {
            return ;
        }

        convertAppAndCountry(reportList, param);
        addDirectReportListIndex(reportList,choose);
        convertDateStrDirect(reportList,param);

        String value = param.getValue();
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        if (!StringUtils.isNullOrEmpty(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0], s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        ExportExcelUtil.exportXLSX2(response, reportList, headerMap, "直投数据报表_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }

    public InfoResult getSubscribeRoiReport(SubscribeRoiReportParam param) {
        InfoResult infoResult = new InfoResult();
        Map<String,Object> map = new HashMap<>();

        PageHelper.startPage(param.getStart(),param.getLimit());
        Page<SubscribeRoiReportDTO> reportPage = dnwxBiAdtMapper.getSubscribeRoiReport(param);
        reportPage.remove(null);
        List<SubscribeRoiReportDTO> reportList = reportPage.getResult();
        if (reportList.size() > 0) {
            SubscribeRoiReportDTO reportDTO = dnwxBiAdtMapper.getSubscribeRoiReportSummary(param);
            addSubscribeReportIndex(reportDTO);
            infoResult.setRet(1);
            map.put("total",reportDTO);
            //convertAppAndCountry(reportList,param);
            addSubscribeReportListIndex(reportList);
        }
        infoResult.setRet(1);
        infoResult.setMsg("查询成功");
        map.put("list",reportList);
        map.put("totalSize",reportPage.getTotal());
        infoResult.setData(map);
        return infoResult;
    }

    public void getSubscribeRoiReportExport(SubscribeRoiReportParam param, HttpServletResponse response) {
        PageHelper.startPage(PAGE_NUM, MAX_PAGE_SIZE);
        Page<SubscribeRoiReportDTO> reportPage = dnwxBiAdtMapper.getSubscribeRoiReport(param);
        List<SubscribeRoiReportDTO> reportList = reportPage.getResult();
        reportPage.remove(null);
        if (CollectionUtils.isEmpty(reportList)) {
            return ;
        }

        //convertAppAndCountry(reportList,param);
        addSubscribeReportListIndex(reportList);

        String value = param.getValue();
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        if (!StringUtils.isNullOrEmpty(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0], s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        ExportExcelUtil.exportXLSX2(response, reportList, headerMap, "订阅ROI报表_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }

    //整合投放内容
    public List<SpendReportDTO> addChinaReportListIndicator(List<SpendReportDTO> reportList, List<String> groups,String username,Double totalSpend) {
        if (CollectionUtils.isEmpty(reportList)) {
            return new ArrayList<>();
        }

        reportList.removeAll(Collections.singleton(null));

        Set<String> observedSet = new HashSet<>();
        Set<String> shutdownSet = new HashSet<>();
        Set<String> excellentSet = new HashSet<>();
        Set<String> shutdownAccountSet = new HashSet<>();
        Set<String> excellentAccountSet = new HashSet<>();

        List<Map<String,Object>> templates = tfxtMapper.selectCreativeTemplate();
        Map<String,String> creativeMap = templates.stream()
                .collect(Collectors.toMap(x -> StringUtil.getString(x.get("template_id")), x -> StringUtil.getString(x.get("template_name")), (k1, k2) -> k2));

        for (SpendReportDTO report : reportList) {
            double impressions = DoubleUtils.formatDouble(report.getImpressions());
            double clicks = DoubleUtils.formatDouble(report.getClicks());
            double installs = DoubleUtils.formatDouble(report.getInstalls());
            double convert = DoubleUtils.formatDouble(report.getConvert());
            double register = DoubleUtils.formatDouble(report.getRegister());
            double game_addiction = DoubleUtils.formatDouble(report.getGame_addiction());
            double spend = DoubleUtils.formatDouble(report.getSpend());
            double reActiveSpend = DoubleUtils.formatDouble(report.getReActiveSpend());
            double uninstallsSpend = DoubleUtils.formatDouble(report.getUninstallsSpend());
            double rebateSpend = DoubleUtils.formatDouble(report.getRebateSpend());
            double revenue = DoubleUtils.formatDouble(report.getRevenue());
            double revenue1 = DoubleUtils.formatDouble(report.getRevenue1());
            double revenue3 = DoubleUtils.formatDouble(report.getRevenue3());
            double revenue7 = DoubleUtils.formatDouble(report.getRevenue7());
            double revenue14 = DoubleUtils.formatDouble(report.getRevenue14());
            double revenue30 = DoubleUtils.formatDouble(report.getRevenue30());
            double payRevenue = DoubleUtils.formatDouble(report.getPayRevenue());
            double payRevenue1 = DoubleUtils.formatDouble(report.getPayRevenue1());
            double payRevenue3 = DoubleUtils.formatDouble(report.getPayRevenue3());
            double payRevenue7 = DoubleUtils.formatDouble(report.getPayRevenue7());
            double payRevenue14 = DoubleUtils.formatDouble(report.getPayRevenue14());
            double payRevenue30 = DoubleUtils.formatDouble(report.getPayRevenue30());
            double revenue_hour_24 = DoubleUtils.formatDouble(report.getRevenue_hour_24());
            double pay_hour_24 = DoubleUtils.formatDouble(report.getPay_hour_24());
            double download = DoubleUtils.formatDouble(report.getDownload());
            double reActive = DoubleUtils.formatDouble(report.getReActive());
            double uninstalls = DoubleUtils.formatDouble(report.getUninstalls());
            double payCount = DoubleUtils.formatDouble(report.getPayCount());//首日付费次数
            double payCount7 = DoubleUtils.formatDouble(report.getPayCount7());//7日付费次数
            double payCountFirst7 = DoubleUtils.formatDouble(report.getPayCountFirst7());
            double gamePayCount = DoubleUtils.formatDouble(report.getGamePayCount());//付费次数
            double addPayCount = DoubleUtils.formatDouble(report.getAddPayCount());//新增付费次数
            double bid = DoubleUtils.formatDouble(report.getBid());
            double retain1 = DoubleUtils.formatDouble(report.getRetain1());
            double retain3 = DoubleUtils.formatDouble(report.getRetain3());
            double retain7 = DoubleUtils.formatDouble(report.getRetain7());
            double b_installs = DoubleUtils.formatDouble(report.getB_install());
            double b_active = DoubleUtils.formatDouble(report.getB_active());
            double b_addRevenue = DoubleUtils.formatDouble(report.getB_addRevenue());
            double b_activeRevenue = DoubleUtils.formatDouble(report.getB_activeRevenue());
            double b_retain1 = DoubleUtils.formatDouble(report.getB_retain1());
            double b_addVideo = DoubleUtils.formatDouble(report.getB_addVideo());
            double b_addPlaque = DoubleUtils.formatDouble(report.getB_addPlaque());

            String clickRate = DoubleUtils.getDouble(clicks / impressions * 100) + "%"; //点击率
            String clickConvertRate = DoubleUtils.getDouble(installs / clicks * 100) + "%";
            String impressionConvertRate = DoubleUtils.getDouble(installs / impressions * 100) + "%";
            double cpi = DoubleUtils.formatDouble(DoubleUtils.getDouble(rebateSpend / installs));   //买量成本

            double clickLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue1 / clicks));//广告激活LTV1
            double installsLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue1 / installs));//广告激活LTV1
            double installsLtv3 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue3 / installs));//广告激活LTV3
            double installsLtv7 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue7 / installs));//广告激活LTV7
            double installsLtv14 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue14 / installs));//广告激活LTV14
            double installsLtv30 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue30 / installs));//广告激活LTV30
            double registerLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue1 / register));//广告注册LTV1
            double registerLtv3 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue3 / register));//广告注册LTV3
            double registerLtv7 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue7 / register));//广告注册LTV7
            double registerLtv14 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue14 / register));//广告注册LTV14
            double registerLtv30 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue30 / register));//广告注册LTV30
            double installsPayLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue1 / installs));//付费激活LTV1
            double installsPayLtv3 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue3 / installs));//付费激活LTV3
            double installsPayLtv7 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue7 / installs));//付费激活LTV7
            double installsPayLtv30 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue30 / installs));//付费激活LTV30
            double registerPayLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue1 / register));//付费注册LTV1
            double registerPayLtv3 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue3 / register));//付费注册LTV3
            double registerPayLtv7 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue7 / register));//付费注册LTV7
            double registerPayLtv30 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue30 / register));//付费注册LTV30
            double installsTotalLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue1+payRevenue1) / installs));//总激活LTV1
            double installsTotalLtv3 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue3+payRevenue3) / installs));//总激活LTV3
            double installsTotalLtv7 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue7+payRevenue7) / installs));//总激活LTV7
            double installsTotalLtv30 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue30+payRevenue30) / installs));//总激活LTV30
            double registerTotalLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue1+payRevenue1) / register));//总注册LTV1
            double registerTotalLtv3 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue3+payRevenue3) / register));//总注册LTV3
            double registerTotalLtv7 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue7+payRevenue7) / register));//总注册LTV7
            double registerTotalLtv30 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue30+payRevenue30) / register));//总注册LTV30
            double pay_hour_24_user = DoubleUtils.formatDouble(report.getPay_hour_24_user());//首日24小时付费人数
            double pay_hour_24_arpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(pay_hour_24 / pay_hour_24_user));//24小时付费arpu
            String installsRetainRate1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(retain1 / installs * 100)) + "%";//激活次留率
            String installsRetainRate3 = DoubleUtils.formatDouble(DoubleUtils.getDouble(retain3 / installs * 100)) + "%";//激活3留率
            String installsRetainRate7 = DoubleUtils.formatDouble(DoubleUtils.getDouble(retain7 / installs * 100)) + "%";//激活7留率
            String registerRetainRate1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(retain1 / register * 100)) + "%";//注册次留率
            String registerRetainRate3 = DoubleUtils.formatDouble(DoubleUtils.getDouble(retain3 / register * 100)) + "%";//注册3留率
            String registerRetainRate7 = DoubleUtils.formatDouble(DoubleUtils.getDouble(retain7 / register * 100)) + "%";//注册7留率
            double activePayCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / payCount));//首次付费成本
            String installsActivePayRate = DoubleUtils.formatDouble(DoubleUtils.getDouble(payCount / installs * 100)) + "%";//首次激活付费率
            String registerActivePayRate = DoubleUtils.formatDouble(DoubleUtils.getDouble(payCount / register * 100)) + "%";//首次注册付费率
            double payCountPer7 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payCount7 / payCountFirst7));
            double gamePayCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / gamePayCount));//付费成本
            String installsGamePayRate = DoubleUtils.formatDouble(DoubleUtils.getDouble(gamePayCount / installs * 100)) + "%";//激活付费率
            String registerGamePayRate = DoubleUtils.formatDouble(DoubleUtils.getDouble(gamePayCount / register * 100)) + "%";//注册付费率
            double addPayCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / addPayCount));//新增付费成本
            String installsAddPayRate = DoubleUtils.formatDouble(DoubleUtils.getDouble(addPayCount / installs  * 100)) + "%";//新增激活付费率
            String registerAddPayRate = DoubleUtils.formatDouble(DoubleUtils.getDouble(addPayCount / register  * 100)) + "%";//新增注册付费率
            double backActivateCount = DoubleUtils.formatDouble(report.getBackActivateCount());//vivo自定义激活数
            String backActivateRate = (DoubleUtils.getDouble(backActivateCount/installs * 100)) + "%";//vivo自定义激活达成率
            double first_cash_user = DoubleUtils.formatDouble(report.getFirst_cash_user());//首日变现金额
            double cash_user = DoubleUtils.formatDouble(report.getCash_user());//变现金额
            double add_arpu_register = DoubleUtils.formatDouble(DoubleUtils.getDouble( revenue1/ register));//新增arpu注册
            double add_arpu_cash = DoubleUtils.formatDouble(DoubleUtils.getDouble( revenue1/ first_cash_user));//新增arpu变现
            String adv_perm_rate = (DoubleUtils.getDouble(first_cash_user/register * 100)) + "%";//广告渗透率
            String register_cash_rate = (DoubleUtils.getDouble(cash_user/register * 100)) + "%";//注册变现率

            report.setBeforeRoi1("0%");
            report.setBeforePayRoi1("0%");
            report.setRevenueRate1("0%");
            report.setPayRevenueRate1("0%");
            if("vivo".equals(report.getMedia())){
                double beforeRevenue1 = 0;
                double beforePayRevenue1 = 0;

                beforeRevenue1 = DoubleUtils.formatDouble(report.getBeforeRevenue1());//分成前变现金额
                beforePayRevenue1 = DoubleUtils.formatDouble(report.getBeforePayRevenue1());

                // 1212 vivo 当天的数据需要用recoverRevenue等相关数据 替换 beforeRevenue数据
                String today = DateUtil.getTypeAToday();
                String week = DateUtil.dateToWeek(today);
                if ((today + "(" + week+ ")").equals(report.getDay())) {
                    beforeRevenue1 = DoubleUtils.formatDouble(report.getRecoveryRevenue());
                    beforePayRevenue1 = DoubleUtils.formatDouble(report.getRecoveryPayRevenue());
                    report.setBeforeRevenue1(beforeRevenue1);
                    report.setBeforePayRevenue1(beforePayRevenue1);
                }
                String beforeRoi1 = DoubleUtils.getDouble(beforeRevenue1 / spend * 100) + "%";
                String beforePayRoi1 = DoubleUtils.getDouble(beforePayRevenue1 / spend * 100) + "%";
                String revenueRate1 = DoubleUtils.getDouble( revenue1/ beforeRevenue1 * 100) + "%";
                String payRevenueRate1 = DoubleUtils.getDouble(payRevenue1 / beforePayRevenue1 * 100) + "%";
                report.setBeforeRoi1(beforeRoi1);
                report.setBeforePayRoi1(beforePayRoi1);
                report.setRevenueRate1(revenueRate1);
                report.setPayRevenueRate1(payRevenueRate1);
            }

            String roi1 = DoubleUtils.getDouble(revenue1 / spend * 100)+ "%";
            String roi3 = DoubleUtils.getDouble(revenue3 / spend * 100)+ "%";
            String roi7 = DoubleUtils.getDouble(revenue7 / spend * 100)+ "%";
            String roi14 = DoubleUtils.getDouble(revenue14 / spend * 100)+ "%";
            String roi30 = DoubleUtils.getDouble(revenue30 / spend * 100) + "%";
            String revenue_hour_roi24 = DoubleUtils.getDouble(revenue_hour_24 / spend * 100) + "%";
            String pay_hour_roi24 = DoubleUtils.getDouble(pay_hour_24 / spend * 100) + "%";
            String payRoi1 = DoubleUtils.getDouble(payRevenue1 / spend * 100) + "%";
            String payRoi3 = DoubleUtils.getDouble(payRevenue3 / spend * 100) + "%";
            String payRoi7 = DoubleUtils.getDouble(payRevenue7 / spend * 100) + "%";
            String payRoi30 = DoubleUtils.getDouble(payRevenue30 / spend * 100) + "%";
            String totalRoi1 = DoubleUtils.getDouble((revenue1+payRevenue1) / spend * 100) + "%";
            String totalRoi3 = DoubleUtils.getDouble((revenue3+payRevenue3) / spend * 100) + "%";
            String totalRoi7 = DoubleUtils.getDouble((revenue7+payRevenue7) / spend * 100) + "%";
            String totalRoi14 = DoubleUtils.getDouble((revenue14+payRevenue14) / spend * 100) + "%";
            String totalRoi30 = DoubleUtils.getDouble((revenue30+payRevenue30) / spend * 100) + "%";
            double totalLt3 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue3+payRevenue3) / (revenue1+payRevenue1)));
            double totalLt7 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue7+payRevenue7) / (revenue1+payRevenue1)));
            double totalLt14 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue14+payRevenue14) / (revenue1+payRevenue1)));
            double totalLt30 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue30+payRevenue30) / (revenue1+payRevenue1)));
            String convertRate = DoubleUtils.getDouble(convert / clicks * 100) + "%";
            String registerRate = DoubleUtils.getDouble(register / installs * 100) + "%";
            String addictionRate = DoubleUtils.getDouble(game_addiction / installs * 100) + "%";//关键行为率
            if("oppo".equals(report.getMedia())){
                addictionRate = DoubleUtils.getDouble(game_addiction / b_installs * 100) + "%";//关键行为率
                if (report.getRealTimeRevenue()!=null) {
                    String oppoRealTimeRevenue = DoubleUtils.getDouble(DoubleUtils.formatDouble(report.getRealTimeRoi()) * 100);
                    if (BlankUtils.isNotBlank(oppoRealTimeRevenue)) {
                        report.setRealTimeRoi(oppoRealTimeRevenue + "%");
                    }
                }
            }
            double convertSpend = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / convert));
            double registerSpend = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / register));
            double addictionSpend = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / game_addiction));//关键行为成本
            String installRate = DoubleUtils.getDouble(installs / clicks * 100) + "%";
            double avgShowSpend = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / impressions * 1000));
            String lt3 = DoubleUtils.getDouble(StringUtil.getDouble(revenue3) / StringUtil.getDouble(revenue1));
            String lt7 = DoubleUtils.getDouble(StringUtil.getDouble(revenue7) / StringUtil.getDouble(revenue1));
            String lt14 = DoubleUtils.getDouble(StringUtil.getDouble(revenue14) / StringUtil.getDouble(revenue1));
            String lt30 = DoubleUtils.getDouble(StringUtil.getDouble(revenue30) / StringUtil.getDouble(revenue1));
            double downloadcost = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / download));
            double preReActive = DoubleUtils.formatDouble(DoubleUtils.getDouble(reActiveSpend / reActive));//首日拉活均价
            double preUninstalls = DoubleUtils.formatDouble(DoubleUtils.getDouble(uninstallsSpend / uninstalls));
            String payRoi = DoubleUtils.getDouble((payRevenue) / rebateSpend * 100) + "%";//付费买量ROI
            String revenueRoi = DoubleUtils.getDouble((revenue) / rebateSpend * 100) + "%";//变现买量ROI
            String buyRoi = DoubleUtils.getDouble((revenue+payRevenue) / rebateSpend * 100) + "%";//总买量ROI

            double bp = DoubleUtils.formatDouble(DoubleUtils.getDouble(convertSpend / bid));
            double b_cpa = DoubleUtils.formatDouble(DoubleUtils.getDouble( rebateSpend/ b_installs));
            double b_addArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble( b_addRevenue/ b_installs));
            double b_activeArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble( b_activeRevenue/ b_active));
            double b_pvVideo = DoubleUtils.formatDouble(DoubleUtils.getDouble( b_addVideo/ b_installs));
            double b_pvPlaque = DoubleUtils.formatDouble(DoubleUtils.getDouble( b_addPlaque/ b_installs));
            String b_roi1 = DoubleUtils.formatDouble(DoubleUtils.getDouble( b_addRevenue / rebateSpend * 100)) + "%";
            String b_retainRate1 = DoubleUtils.formatDouble(DoubleUtils.getDouble( b_retain1/ b_installs * 100)) + "%";
            String spendRate = "";
            if(totalSpend != null){
                spendRate = DoubleUtils.getDouble(spend/totalSpend *100) + "%";
            }
            report.setBp(bp);
            report.setClickRate(clickRate);
            report.setClickConvertRate(clickConvertRate);
            report.setImpressionConvertRate(impressionConvertRate);
            report.setCpi(cpi);
            report.setSpend(DoubleUtils.formatDouble(DoubleUtils.getDouble(report.getSpend())));
            report.setRebateSpend(DoubleUtils.formatDouble(DoubleUtils.getDouble(report.getRebateSpend())));
            report.setDollarSpend(DoubleUtils.formatDouble(DoubleUtils.getDouble(report.getDollarSpend())));
            report.setDollarRebateSpend(DoubleUtils.formatDouble(DoubleUtils.getDouble(report.getDollarRebateSpend())));
            report.setRevenue(DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue)));
            report.setRevenue1(DoubleUtils.formatDouble(DoubleUtils.getDouble(report.getRevenue1())));

            report.setClickLtv1(clickLtv1);
            report.setInstallsLtv1(installsLtv1);
            report.setInstallsLtv3(installsLtv3);
            report.setInstallsLtv7(installsLtv7);
            report.setInstallsLtv14(installsLtv14);
            report.setInstallsLtv30(installsLtv30);
            report.setRegisterLtv1(registerLtv1);
            report.setRegisterLtv3(registerLtv3);
            report.setRegisterLtv7(registerLtv7);
            report.setRegisterLtv14(registerLtv14);
            report.setRegisterLtv30(registerLtv30);
            report.setInstallsPayLtv1(installsPayLtv1);
            report.setInstallsPayLtv3(installsPayLtv3);
            report.setInstallsPayLtv7(installsPayLtv7);
            report.setInstallsPayLtv30(installsPayLtv30);
            report.setRegisterPayLtv1(registerPayLtv1);
            report.setRegisterPayLtv3(registerPayLtv3);
            report.setRegisterPayLtv7(registerPayLtv7);
            report.setRegisterPayLtv30(registerPayLtv30);
            report.setInstallsTotalLtv1(installsTotalLtv1);
            report.setInstallsTotalLtv3(installsTotalLtv3);
            report.setInstallsTotalLtv7(installsTotalLtv7);
            report.setInstallsTotalLtv30(installsTotalLtv30);
            report.setRegisterTotalLtv1(registerTotalLtv1);
            report.setRegisterTotalLtv3(registerTotalLtv3);
            report.setRegisterTotalLtv7(registerTotalLtv7);
            report.setRegisterTotalLtv30(registerTotalLtv30);
            report.setRoi1(roi1);
            report.setRoi3(roi3);
            report.setRoi7(roi7);
            report.setRoi14(roi14);
            report.setRoi30(roi30);
            report.setRevenue_hour_roi24(revenue_hour_roi24);
            report.setPay_hour_roi24(pay_hour_roi24);
            report.setPay_hour_24_arpu(pay_hour_24_arpu);
            report.setPayRoi1(payRoi1);
            report.setPayRoi3(payRoi3);
            report.setPayRoi7(payRoi7);
            report.setPayRoi30(payRoi30);
            report.setTotalRoi1(totalRoi1);
            report.setTotalRoi3(totalRoi3);
            report.setTotalRoi7(totalRoi7);
            report.setTotalRoi14(totalRoi14);
            report.setTotalRoi30(totalRoi30);
            report.setTotalLt3(totalLt3);
            report.setTotalLt7(totalLt7);
            report.setTotalLt14(totalLt14);
            report.setTotalLt30(totalLt30);
            report.setConvertRate(convertRate);
            report.setRegisterRate(registerRate);
            report.setAddictionRate(addictionRate);
            report.setConvertRate(convertRate);
            report.setConvertSpend(convertSpend);
            report.setRegisterSpend(registerSpend);
            report.setAddictionSpend(addictionSpend);
            report.setInstallRate(installRate);
            report.setAvgShowSpend(avgShowSpend);
            report.setLt3(lt3);
            report.setLt7(lt7);
            report.setLt14(lt14);
            report.setLt30(lt30);
            report.setDownloadcost(downloadcost);
            report.setPreReActive(preReActive);
            report.setPreUninstalls(preUninstalls);
            report.setPayRoi(payRoi);
            report.setRevenueRoi(revenueRoi);
            report.setBuyRoi(buyRoi);
            report.setGamePayCost(gamePayCost);
            report.setInstallsGamePayRate(installsGamePayRate);
            report.setRegisterGamePayRate(registerGamePayRate);
            report.setActivePayCost(activePayCost);
            report.setInstallsActivePayRate(installsActivePayRate);
            report.setRegisterActivePayRate(registerActivePayRate);
            report.setAddPayCost(addPayCost);
            report.setInstallsAddPayRate(installsAddPayRate);
            report.setRegisterAddPayRate(registerAddPayRate);
            report.setPayCountPer7(payCountPer7);
            report.setInstallsRetainRate1(installsRetainRate1);
            report.setInstallsRetainRate3(installsRetainRate3);
            report.setInstallsRetainRate7(installsRetainRate7);
            report.setRegisterRetainRate1(registerRetainRate1);
            report.setRegisterRetainRate3(registerRetainRate3);
            report.setRegisterRetainRate7(registerRetainRate7);
            report.setB_cpa(b_cpa);
            report.setB_addArpu(b_addArpu);
            report.setB_activeArpu(b_activeArpu);
            report.setB_pvVideo(b_pvVideo);
            report.setB_pvPlaque(b_pvPlaque);
            report.setB_roi1(b_roi1);
            report.setB_retainRate1(b_retainRate1);
            report.setSpendRate(spendRate);
            report.setBackActivateRate(backActivateRate);
            report.setAdd_arpu_register(add_arpu_register);
            report.setAdd_arpu_cash(add_arpu_cash);
            report.setAdv_perm_rate(adv_perm_rate);
            report.setRegister_cash_rate(register_cash_rate);
            report.setBackActivateRate(report.getBackActivateRate() + "%");
            report.setActiveJRate(report.getActiveJRate() + "%");
            if (groups.contains("campaign")) {
                String campaignId = report.getCampaignId();
                int status = 1;
                if (observedSet.contains(campaignId)) {
                    status = 2;
                } else if (shutdownSet.contains(campaignId)) {
                    status = 3;
                } else if (excellentSet.contains(campaignId)) {
                    status = 4;
                }
                report.setStatus(status);
            } else if (groups.contains("account")) {
                String account = report.getAccount();
                int status = 1;
                if (shutdownAccountSet.contains(account)) {
                    status = 3;
                } else if (excellentAccountSet.contains(account)) {
                    status = 4;
                }
                report.setStatus(status);
            }

            if (report.getOs_type() != null) {
                switch (report.getOs_type()) {
                    case "1": report.setOs_type("安卓");break;
                    case "2": report.setOs_type("ios");break;
                    case "3": report.setOs_type("google");break;
                    case "4": report.setOs_type("小游戏");break;
                    default:break;
                }
            }

            if (report.getDelivery_mode() != null) {
                switch (report.getDelivery_mode()) {
                    case "1": report.setDelivery_mode("是");break;
                    case "0": report.setDelivery_mode("否");break;
                    case "": report.setDelivery_mode("巨量1.0");break;
                    default:break;
                }
            }else{
                report.setDelivery_mode("其他媒体");
            }

            if (report.getCreative_template() != null) {
                String s = creativeMap.get(report.getCreative_template());
                if (!BlankUtils.checkBlank(s)) {
                    report.setCreative_template(s);
                }
            }

            // 1212 vivo 当天的数据需要用recoverRevenue等相关数据 替换 beforeRevenue数据
            if ("vivo".equals(report.getMedia()) && DateUtil.getTypeAToday().equals(report.getDay())) {
                report.setBeforePayRoi1(report.getRecoveryPayRoi1());
                report.setBeforeRevenue1(report.getRecoveryRevenue());
                report.setBeforeRoi1(report.getRecoveryRoi1());
                report.setBeforePayRevenue1(report.getRecoveryPayRevenue());
            }

        }

        if ("diandx".equals(username)) {
            for (int i = 0; i < reportList.size(); i++) {
                SpendReportDTO report = reportList.get(i);
                SpendReportDTO newReport = new SpendReportDTO();
                newReport.setDay(report.getDay());
                newReport.setApp(report.getApp());
                newReport.setChannel(report.getChannel());
                newReport.setRebateSpend(report.getRebateSpend());
                newReport.setInstalls(report.getInstalls());
                newReport.setCpi(report.getCpi());
                reportList.set(i, newReport);
            }
        }
        return reportList;
    }


    //整合投放内容
    public SpendReportDTO addChinaReportIndicator(SpendReportDTO report, List<String> groups,String username) {
        if(report == null){
            return new SpendReportDTO();
        }

        Set<String> observedSet = new HashSet<>();
        Set<String> shutdownSet = new HashSet<>();
        Set<String> excellentSet = new HashSet<>();
        Set<String> shutdownAccountSet = new HashSet<>();
        Set<String> excellentAccountSet = new HashSet<>();
        //计划状态和账号状态
//        if (groups.contains("campaign")) {
//            observedSet = redisCache.getObservedSet();
//            shutdownSet = redisCache.getShutdownSet();
//            excellentSet = redisCache.getExcellentSet();
//        } else if (groups.contains("account")) {
//            shutdownAccountSet = redisCache.getShutdownAccountSet();
//            excellentSet = redisCache.getExcellentAccountSet();
//        }

        double impressions = DoubleUtils.formatDouble(report.getImpressions());
        double clicks = DoubleUtils.formatDouble(report.getClicks());
        double installs = DoubleUtils.formatDouble(report.getInstalls());
        double convert = DoubleUtils.formatDouble(report.getConvert());
        double register = DoubleUtils.formatDouble(report.getRegister());
        double game_addiction = DoubleUtils.formatDouble(report.getGame_addiction());
        double spend = DoubleUtils.formatDouble(report.getSpend());
        double reActiveSpend = DoubleUtils.formatDouble(report.getReActiveSpend());
        double uninstallsSpend = DoubleUtils.formatDouble(report.getUninstallsSpend());
        double rebateSpend = DoubleUtils.formatDouble(report.getRebateSpend());
        double revenue = DoubleUtils.formatDouble(report.getRevenue());
        double revenue1 = DoubleUtils.formatDouble(report.getRevenue1());
        double revenue3 = DoubleUtils.formatDouble(report.getRevenue3());
        double revenue7 = DoubleUtils.formatDouble(report.getRevenue7());
        double revenue14 = DoubleUtils.formatDouble(report.getRevenue14());
        double revenue30 = DoubleUtils.formatDouble(report.getRevenue30());
        double revenue_hour_24 = DoubleUtils.formatDouble(report.getRevenue_hour_24());
        double pay_hour_24 = DoubleUtils.formatDouble(report.getPay_hour_24());
        double payRevenue = DoubleUtils.formatDouble(report.getPayRevenue());
        double payRevenue1 = DoubleUtils.formatDouble(report.getPayRevenue1());
        double payRevenue3 = DoubleUtils.formatDouble(report.getPayRevenue3());
        double payRevenue7 = DoubleUtils.formatDouble(report.getPayRevenue7());
        double payRevenue14 = DoubleUtils.formatDouble(report.getPayRevenue14());
        double payRevenue30 = DoubleUtils.formatDouble(report.getPayRevenue30());
        double download = DoubleUtils.formatDouble(report.getDownload());
        double reActive = DoubleUtils.formatDouble(report.getReActive());
        double uninstalls = DoubleUtils.formatDouble(report.getUninstalls());
        double payCount = DoubleUtils.formatDouble(report.getPayCount());//首日付费次数
        double payCount7 = DoubleUtils.formatDouble(report.getPayCount7());//7日付费次数
        double payCountFirst7 = DoubleUtils.formatDouble(report.getPayCountFirst7());
        double gamePayCount = DoubleUtils.formatDouble(report.getGamePayCount());//付费次数
        double addPayCount = DoubleUtils.formatDouble(report.getAddPayCount());//新增付费次数
        double bid = DoubleUtils.formatDouble(report.getBid());
        double retain1 = DoubleUtils.formatDouble(report.getRetain1());
        double retain3 = DoubleUtils.formatDouble(report.getRetain3());
        double retain7 = DoubleUtils.formatDouble(report.getRetain7());
        double b_installs = DoubleUtils.formatDouble(report.getB_install());
        double b_active = DoubleUtils.formatDouble(report.getB_active());
        double b_addRevenue = DoubleUtils.formatDouble(report.getB_addRevenue());
        double b_activeRevenue = DoubleUtils.formatDouble(report.getB_activeRevenue());
        double b_retain1 = DoubleUtils.formatDouble(report.getB_retain1());
        double b_addVideo = DoubleUtils.formatDouble(report.getB_addVideo());
        double b_addPlaque = DoubleUtils.formatDouble(report.getB_addPlaque());
        double pay_hour_24_user = DoubleUtils.formatDouble(report.getPay_hour_24_user());

        String clickRate = DoubleUtils.getDouble(clicks / impressions * 100) + "%"; //点击率
        String clickConvertRate = DoubleUtils.getDouble(installs / clicks * 100) + "%";
        String impressionConvertRate = DoubleUtils.getDouble(installs / impressions * 100) + "%";
        double cpi = DoubleUtils.formatDouble(DoubleUtils.getDouble(rebateSpend / installs));   //买量成本

        double clickLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue1 / clicks));//广告激活LTV1
        double installsLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue1 / installs));//广告激活LTV1
        double installsLtv3 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue3 / installs));//广告激活LTV3
        double installsLtv7 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue7 / installs));//广告激活LTV7
        double installsLtv14 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue14 / installs));//广告激活LTV14
        double installsLtv30 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue30 / installs));//广告激活LTV30
        double registerLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue1 / register));//广告注册LTV1
        double registerLtv3 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue3 / register));//广告注册LTV3
        double registerLtv7 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue7 / register));//广告注册LTV7
        double registerLtv14 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue14 / register));//广告注册LTV14
        double registerLtv30 = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue30 / register));//广告注册LTV30
        double installsPayLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue1 / installs));//付费激活LTV1
        double installsPayLtv3 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue3 / installs));//付费激活LTV3
        double installsPayLtv7 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue7 / installs));//付费激活LTV7
        double installsPayLtv30 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue30 / installs));//付费激活LTV30
        double registerPayLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue1 / register));//付费注册LTV1
        double registerPayLtv3 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue3 / register));//付费注册LTV3
        double registerPayLtv7 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue7 / register));//付费注册LTV7
        double registerPayLtv30 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payRevenue30 / register));//付费注册LTV30
        double installsTotalLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue1+payRevenue1) / installs));//总激活LTV1
        double installsTotalLtv3 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue3+payRevenue3) / installs));//总激活LTV3
        double installsTotalLtv7 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue7+payRevenue7) / installs));//总激活LTV7
        double installsTotalLtv30 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue30+payRevenue30) / installs));//总激活LTV30
        double registerTotalLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue1+payRevenue1) / register));//总注册LTV1
        double registerTotalLtv3 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue3+payRevenue3) / register));//总注册LTV3
        double registerTotalLtv7 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue7+payRevenue7) / register));//总注册LTV7
        double registerTotalLtv30 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue30+payRevenue30) / register));//总注册LTV30
        double pay_hour_24_arpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(pay_hour_24 / pay_hour_24_user));//24小时付费arpu

        String installsRetainRate1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(retain1 / installs * 100)) + "%";//激活次留率
        String installsRetainRate3 = DoubleUtils.formatDouble(DoubleUtils.getDouble(retain3 / installs * 100)) + "%";//激活3留率
        String installsRetainRate7 = DoubleUtils.formatDouble(DoubleUtils.getDouble(retain7 / installs * 100)) + "%";//激活7留率
        String registerRetainRate1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(retain1 / register * 100)) + "%";//注册次留率
        String registerRetainRate3 = DoubleUtils.formatDouble(DoubleUtils.getDouble(retain3 / register * 100)) + "%";//注册3留率
        String registerRetainRate7 = DoubleUtils.formatDouble(DoubleUtils.getDouble(retain7 / register * 100)) + "%";//注册7留率
        double activePayCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / payCount));//首次付费成本
        String installsActivePayRate = DoubleUtils.formatDouble(DoubleUtils.getDouble(payCount / installs * 100)) + "%";//首次激活付费率
        String registerActivePayRate = DoubleUtils.formatDouble(DoubleUtils.getDouble(payCount / register * 100)) + "%";//首次注册付费率
        double payCountPer7 = DoubleUtils.formatDouble(DoubleUtils.getDouble(payCount7 / payCountFirst7));
        double gamePayCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / gamePayCount));//付费成本
        String installsGamePayRate = DoubleUtils.formatDouble(DoubleUtils.getDouble(gamePayCount / installs * 100)) + "%";//激活付费率
        String registerGamePayRate = DoubleUtils.formatDouble(DoubleUtils.getDouble(gamePayCount / register * 100)) + "%";//注册付费率
        double addPayCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / addPayCount));//新增付费成本
        String installsAddPayRate = DoubleUtils.formatDouble(DoubleUtils.getDouble(addPayCount / installs  * 100)) + "%";//新增激活付费率
        String registerAddPayRate = DoubleUtils.formatDouble(DoubleUtils.getDouble(addPayCount / register  * 100)) + "%";//新增注册付费率
        report.setBeforeRoi1("0%");
        report.setBeforePayRoi1("0%");
        report.setRevenueRate1("0%");
        report.setPayRevenueRate1("0%");
        if("vivo".equals(report.getMedia())){
            double beforeRevenue1 = DoubleUtils.formatDouble(report.getBeforeRevenue1());//分成前变现金额
            double beforePayRevenue1 = DoubleUtils.formatDouble(report.getBeforePayRevenue1());
            String beforeRoi1 = DoubleUtils.getDouble(beforeRevenue1 / spend * 100) + "%";
            String beforePayRoi1 = DoubleUtils.getDouble(beforePayRevenue1 / spend * 100) + "%";
            String revenueRate1 = DoubleUtils.getDouble( revenue1/ beforeRevenue1 * 100) + "%";
            String payRevenueRate1 = DoubleUtils.getDouble(payRevenue1 / beforePayRevenue1 * 100) + "%";
            report.setBeforeRoi1(beforeRoi1);
            report.setBeforePayRoi1(beforePayRoi1);
            report.setRevenueRate1(revenueRate1);
            report.setPayRevenueRate1(payRevenueRate1);
        }
        String roi1 = DoubleUtils.getDouble(revenue1 / spend * 100)+ "%";
        String roi3 = DoubleUtils.getDouble(revenue3 / spend * 100) + "%";
        String roi7 = DoubleUtils.getDouble(revenue7 / spend * 100) + "%";
        String roi14 = DoubleUtils.getDouble(revenue14 / spend * 100) + "%";
        String roi30 = DoubleUtils.getDouble(revenue30 / spend * 100) + "%";
        String payRoi1 = DoubleUtils.getDouble(payRevenue1 / spend * 100) + "%";
        String payRoi3 = DoubleUtils.getDouble(payRevenue3 / spend * 100) + "%";
        String payRoi7 = DoubleUtils.getDouble(payRevenue7 / spend * 100) + "%";
        String payRoi30 = DoubleUtils.getDouble(payRevenue30 / spend * 100) + "%";
        String totalRoi1 = DoubleUtils.getDouble((revenue1+payRevenue1) / spend * 100) + "%";
        String totalRoi3 = DoubleUtils.getDouble((revenue3+payRevenue3) / spend * 100) + "%";
        String totalRoi7 = DoubleUtils.getDouble((revenue7+payRevenue7) / spend * 100) + "%";
        String totalRoi14 = DoubleUtils.getDouble((revenue14+payRevenue14) / spend * 100) + "%";
        String totalRoi30 = DoubleUtils.getDouble((revenue30+payRevenue30) / spend * 100) + "%";
        double totalLt3 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue3+payRevenue3) / (revenue1+payRevenue1)));
        double totalLt7 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue7+payRevenue7) / (revenue1+payRevenue1)));
        double totalLt14 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue14+payRevenue14) / (revenue1+payRevenue1)));
        double totalLt30 = DoubleUtils.formatDouble(DoubleUtils.getDouble((revenue30+payRevenue30) / (revenue1+payRevenue1)));
        String revenue_hour_roi24 = DoubleUtils.getDouble(revenue_hour_24 / spend * 100) + "%";
        String pay_hour_roi24 = DoubleUtils.getDouble(pay_hour_24 / spend * 100) + "%";
        String convertRate = DoubleUtils.getDouble(convert / clicks * 100) + "%";
        String registerRate = DoubleUtils.getDouble(register / installs * 100) + "%";
        String addictionRate = DoubleUtils.getDouble(game_addiction / installs * 100) + "%";//关键行为率
        if("oppo".equals(report.getMedia())){
            addictionRate = DoubleUtils.getDouble(game_addiction / b_installs * 100) + "%";//关键行为率
            if (report.getRealTimeRevenue()!=null) {
                String oppoRealTimeRoi = DoubleUtils.getDouble(
                        DoubleUtils.formatDouble(report.getRealTimeRevenue() / report.getSpend()) * 100);
                if (BlankUtils.isNotBlank(oppoRealTimeRoi)) {
                    report.setRealTimeRoi(oppoRealTimeRoi + "%");
                }
            }
        }
        double convertSpend = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / convert));
        double registerSpend = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / register));
        double addictionSpend = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / game_addiction));//关键行为成本
        String installRate = DoubleUtils.getDouble(installs / clicks * 100) + "%";
        double avgShowSpend = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / impressions * 1000));
        String lt3 = DoubleUtils.getDouble(StringUtil.getDouble(revenue3) / StringUtil.getDouble(revenue1));
        String lt7 = DoubleUtils.getDouble(StringUtil.getDouble(revenue7) / StringUtil.getDouble(revenue1));
        String lt14 = DoubleUtils.getDouble(StringUtil.getDouble(revenue14) / StringUtil.getDouble(revenue1));
        String lt30 = DoubleUtils.getDouble(StringUtil.getDouble(revenue30) / StringUtil.getDouble(revenue1));
        double downloadcost = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / download));
        double preReActive = DoubleUtils.formatDouble(DoubleUtils.getDouble(reActiveSpend / reActive));//首日拉活均价
        double preUninstalls = DoubleUtils.formatDouble(DoubleUtils.getDouble(uninstallsSpend / uninstalls));
        String payRoi = DoubleUtils.getDouble((payRevenue) / rebateSpend * 100) + "%";//付费买量ROI
        String revenueRoi = DoubleUtils.getDouble((revenue) / rebateSpend * 100) + "%";//变现买量ROI
        String buyRoi = DoubleUtils.getDouble((revenue+payRevenue) / rebateSpend * 100) + "%";//总买量ROI

        double bp = DoubleUtils.formatDouble(DoubleUtils.getDouble(convertSpend / bid));

        double b_cpa = DoubleUtils.formatDouble(DoubleUtils.getDouble( rebateSpend/ b_installs));
        double b_addArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble( b_addRevenue/ b_installs));
        double b_activeArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble( b_activeRevenue/ b_active));
        double b_pvVideo = DoubleUtils.formatDouble(DoubleUtils.getDouble( b_addVideo/ b_installs));
        double b_pvPlaque = DoubleUtils.formatDouble(DoubleUtils.getDouble( b_addPlaque/ b_installs));
        String b_roi1 = DoubleUtils.formatDouble(DoubleUtils.getDouble( b_addRevenue / rebateSpend * 100)) + "%";
        String b_retainRate1 = DoubleUtils.formatDouble(DoubleUtils.getDouble( b_retain1/ b_installs * 100)) + "%";
        String spendRate = DoubleUtils.getDouble(spend/spend *100) + "%";
        double backActivateCount = DoubleUtils.formatDouble(report.getBackActivateCount());//vivo自定义激活数
        String backActivateRate = (DoubleUtils.getDouble(backActivateCount/installs * 100)) + "%";//vivo自定义激活达成率
        double first_cash_user = DoubleUtils.formatDouble(report.getFirst_cash_user());//首日变现金额
        double cash_user = DoubleUtils.formatDouble(report.getCash_user());//变现金额
        double add_arpu_register = DoubleUtils.formatDouble(DoubleUtils.getDouble( revenue1/ register));//新增arpu注册
        double add_arpu_cash = DoubleUtils.formatDouble(DoubleUtils.getDouble( revenue1/ first_cash_user));//新增arpu变现
        String adv_perm_rate = (DoubleUtils.getDouble(first_cash_user/register * 100)) + "%";//广告渗透率
        String register_cash_rate = (DoubleUtils.getDouble(cash_user/register * 100)) + "%";//注册变现率

        report.setAdd_arpu_register(add_arpu_register);
        report.setAdd_arpu_cash(add_arpu_cash);
        report.setAdv_perm_rate(adv_perm_rate);
        report.setRegister_cash_rate(register_cash_rate);
        report.setBackActivateRate(backActivateRate);
        report.setBp(bp);
        report.setClickRate(clickRate);
        report.setClickConvertRate(clickConvertRate);
        report.setImpressionConvertRate(impressionConvertRate);
        report.setCpi(cpi);
        report.setSpend(DoubleUtils.formatDouble(DoubleUtils.getDouble(report.getSpend())));
        report.setRebateSpend(DoubleUtils.formatDouble(DoubleUtils.getDouble(report.getRebateSpend())));
        report.setDollarSpend(DoubleUtils.formatDouble(DoubleUtils.getDouble(report.getDollarSpend())));
        report.setDollarRebateSpend(DoubleUtils.formatDouble(DoubleUtils.getDouble(report.getDollarRebateSpend())));
        report.setRevenue(DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue)));
        report.setRevenue1(DoubleUtils.formatDouble(DoubleUtils.getDouble(report.getRevenue1())));
        report.setClickLtv1(clickLtv1);
        report.setInstallsLtv1(installsLtv1);
        report.setInstallsLtv3(installsLtv3);
        report.setInstallsLtv7(installsLtv7);
        report.setInstallsLtv14(installsLtv14);
        report.setInstallsLtv30(installsLtv30);
        report.setRegisterLtv1(registerLtv1);
        report.setRegisterLtv3(registerLtv3);
        report.setRegisterLtv7(registerLtv7);
        report.setRegisterLtv14(registerLtv14);
        report.setRegisterLtv30(registerLtv30);
        report.setInstallsPayLtv1(installsPayLtv1);
        report.setInstallsPayLtv3(installsPayLtv3);
        report.setInstallsPayLtv7(installsPayLtv7);
        report.setInstallsPayLtv30(installsPayLtv30);
        report.setRegisterPayLtv1(registerPayLtv1);
        report.setRegisterPayLtv3(registerPayLtv3);
        report.setRegisterPayLtv7(registerPayLtv7);
        report.setRegisterPayLtv30(registerPayLtv30);
        report.setInstallsTotalLtv1(installsTotalLtv1);
        report.setInstallsTotalLtv3(installsTotalLtv3);
        report.setInstallsTotalLtv7(installsTotalLtv7);
        report.setInstallsTotalLtv30(installsTotalLtv30);
        report.setRegisterTotalLtv1(registerTotalLtv1);
        report.setRegisterTotalLtv3(registerTotalLtv3);
        report.setRegisterTotalLtv7(registerTotalLtv7);
        report.setRegisterTotalLtv30(registerTotalLtv30);
        report.setRoi1(roi1);
        report.setRoi3(roi3);
        report.setRoi7(roi7);
        report.setRoi14(roi14);
        report.setRoi30(roi30);
        report.setRevenue_hour_roi24(revenue_hour_roi24);
        report.setPay_hour_roi24(pay_hour_roi24);
        report.setPay_hour_24_arpu(pay_hour_24_arpu);
        report.setPayRoi1(payRoi1);
        report.setPayRoi3(payRoi3);
        report.setPayRoi7(payRoi7);
        report.setPayRoi30(payRoi30);
        report.setTotalRoi1(totalRoi1);
        report.setTotalRoi3(totalRoi3);
        report.setTotalRoi7(totalRoi7);
        report.setTotalRoi14(totalRoi14);
        report.setTotalRoi30(totalRoi30);
        report.setTotalLt3(totalLt3);
        report.setTotalLt7(totalLt7);
        report.setTotalLt14(totalLt14);
        report.setTotalLt30(totalLt30);
        report.setConvertRate(convertRate);
        report.setRegisterRate(registerRate);
        report.setAddictionRate(addictionRate);
        report.setConvertRate(convertRate);
        report.setConvertSpend(convertSpend);
        report.setRegisterSpend(registerSpend);
        report.setAddictionSpend(addictionSpend);
        report.setInstallRate(installRate);
        report.setAvgShowSpend(avgShowSpend);
        report.setLt3(lt3);
        report.setLt7(lt7);
        report.setLt14(lt14);
        report.setLt30(lt30);
        report.setDownloadcost(downloadcost);
        report.setPreReActive(preReActive);
        report.setPreUninstalls(preUninstalls);
        report.setPayRoi(payRoi);
        report.setRevenueRoi(revenueRoi);
        report.setBuyRoi(buyRoi);
        report.setGamePayCost(gamePayCost);
        report.setInstallsGamePayRate(installsGamePayRate);
        report.setRegisterGamePayRate(registerGamePayRate);
        report.setActivePayCost(activePayCost);
        report.setInstallsActivePayRate(installsActivePayRate);
        report.setRegisterActivePayRate(registerActivePayRate);
        report.setAddPayCost(addPayCost);
        report.setInstallsAddPayRate(installsAddPayRate);
        report.setRegisterAddPayRate(registerAddPayRate);
        report.setPayCountPer7(payCountPer7);
        report.setInstallsRetainRate1(installsRetainRate1);
        report.setInstallsRetainRate3(installsRetainRate3);
        report.setInstallsRetainRate7(installsRetainRate7);
        report.setRegisterRetainRate1(registerRetainRate1);
        report.setRegisterRetainRate3(registerRetainRate3);
        report.setRegisterRetainRate7(registerRetainRate7);
        report.setB_cpa(b_cpa);
        report.setB_addArpu(b_addArpu);
        report.setB_activeArpu(b_activeArpu);
        report.setB_pvVideo(b_pvVideo);
        report.setB_pvPlaque(b_pvPlaque);
        report.setB_roi1(b_roi1);
        report.setB_retainRate1(b_retainRate1);
        report.setSpendRate(spendRate);
        report.setBackActivateRate(report.getBackActivateRate() + "%");
        report.setActiveJRate(report.getActiveJRate() + "%");
        if (groups.contains("campaign")) {
            String campaignId = report.getCampaignId();
            int status = 1;
            if (observedSet.contains(campaignId)) {
                status = 2;
            } else if (shutdownSet.contains(campaignId)) {
                status = 3;
            } else if (excellentSet.contains(campaignId)) {
                status = 4;
            }
            report.setStatus(status);
        } else if (groups.contains("account")) {
            String account = report.getAccount();
            int status = 1;
            if (shutdownAccountSet.contains(account)) {
                status = 3;
            } else if (excellentAccountSet.contains(account)) {
                status = 4;
            }
            report.setStatus(status);
        }
        return report;
    }


    public List<? extends BaseReportDTO> convertAppAndCountry(List<? extends BaseReportDTO> list, BaseReportParam<? extends BaseReportParam> BaseReportParam) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        list.removeAll(Collections.singleton(null));
        List<String> groupList = BaseReportParam.getGroup();
        if (CollectionUtils.isEmpty(groupList)) {
            return list;
        }

        //应用
        Map<String, String> appMap = new HashMap<>();
        if (groupList.contains("app")) {
            List<AppDTO> appList = tfxtMapper.getAllApp(null);
            appMap = new HashMap<>(appList.size());
            String key, value;
            for (int i = 0; i < appList.size(); i++) {
                AppDTO app = appList.get(i);
                key = String.valueOf(app.getApp_id());
                value = app.getApp_name();
                appMap.put(key, value);
            }
        }

        //国家
        Map<String, String> countryMap = new HashMap<>();
        if (groupList.contains("country")) {
            List<Map> countryList = JsonUtils.json2list(countryJson, Map.class);
            countryMap = new HashMap<>(countryList.size());
            String key, value;
            for (int i = 0; i < countryList.size(); i++) {
                Map<String, String> map = countryList.get(i);
                key = map.get("value");
                value = map.get("label");
                countryMap.put(key, value);
            }
        }

        Map<String, String> remarkMap = new HashMap<>();
        if (groupList.contains("account")) {
            List<HashMap> allAccountList = tfxtMapper.getChinaPlatformAccount();
            remarkMap = allAccountList.stream()
                    .collect(Collectors.toMap(x->StringUtil.getString(x.get("account")),x->StringUtil.getString(x.get("remark")),(k1,k2)->k2));
        }

        //应用分类
        Map<String, String> appCategoryMap = new HashMap<>();
        if (groupList.contains("appCategory")) {
            List<AppCategory> appCategoryNameList = someMapper.getAppCategorys();
            appCategoryMap = appCategoryNameList.stream()
                    .collect(Collectors.toMap(appCategory->StringUtil.getString(appCategory.getId()),appCategory -> StringUtil.getString(appCategory.getName()),(k1,k2)->k2));
        }

        if (CollectionUtils.isEmpty(appMap)
                && CollectionUtils.isEmpty(countryMap)
                && CollectionUtils.isEmpty(appCategoryMap)) {
            return list;
        }

        for (int i = 0; i < list.size(); i++) {
            BaseReportDTO baseReport = list.get(i);
            baseReport.setAppId(baseReport.getApp());
            baseReport.setApp(appMap.get(baseReport.getApp()));
            baseReport.setCountry(countryMap.get(baseReport.getCountry()));
            baseReport.setAppCategory(appCategoryMap.get(baseReport.getAppCategory()));
            baseReport.setAccountRemark(remarkMap.get(baseReport.getAccount()));
        }

        return list;
    }

    public List<DirectReportDTO> addDirectReportListIndex(List<DirectReportDTO> reportList,String choose) {
        if (CollectionUtils.isEmpty(reportList)) {
            return new ArrayList<>();
        }
        reportList.removeAll(Collections.singleton(null));

        for (DirectReportDTO report : reportList) {
            double channelSpend = StringUtil.getDouble(report.getChannelSpend());
            double directSpend = StringUtil.getDouble(report.getDirectSpend());
            double directBuyAdd = StringUtil.getDouble(report.getDirectBuyAdd());

            double ttDirectSpend = StringUtil.getDouble(report.getTtDirectSpend());
            double ttDirectBuyAdd = StringUtil.getDouble(report.getTtDirectBuyAdd());
            double ttRevenue1 = StringUtil.getDouble(report.getTtRevenue1());
            double ttPayRevenue1 = StringUtil.getDouble(report.getTtPayRevenue1());
            double ttAfterPayRevenue1 = StringUtil.getDouble(report.getTtAfterPayRevenue1());
            double ttAddPayCount = StringUtil.getDouble(report.getTtAddPayCount());
            double ksDirectSpend = StringUtil.getDouble(report.getKsDirectSpend());
            double ksDirectBuyAdd = StringUtil.getDouble(report.getKsDirectBuyAdd());
            double ksRevenue1 = StringUtil.getDouble(report.getKsRevenue1());
            double ksPayRevenue1 = StringUtil.getDouble(report.getKsPayRevenue1());
            double ksAfterPayRevenue1 = StringUtil.getDouble(report.getKsAfterPayRevenue1());
            double ksAddPayCount = StringUtil.getDouble(report.getKsAddPayCount());
            double gdtDirectSpend = StringUtil.getDouble(report.getGdtDirectSpend());
            double gdtDirectBuyAdd = StringUtil.getDouble(report.getGdtDirectBuyAdd());
            double gdtRevenue1 = StringUtil.getDouble(report.getGdtRevenue1());
            double gdtPayRevenue1 = StringUtil.getDouble(report.getGdtPayRevenue1());
            double gdtAfterPayRevenue1 = StringUtil.getDouble(report.getGdtAfterPayRevenue1());
            double gdtAddPayCount = StringUtil.getDouble(report.getGdtAddPayCount());

            double umAddNum = StringUtil.getDouble(report.getUmAddNum());
            double channelAddNum = StringUtil.getDouble(report.getChannelAddNum());
            double umActiveNum = StringUtil.getDouble(report.getUmActiveNum());
            double channelActiveNum = StringUtil.getDouble(report.getChannelActiveNum());
            double revenue = StringUtil.getDouble(report.getRevenue());
            double revenueChannel = StringUtil.getDouble(report.getRevenueChannel());
            double revenueSelf = StringUtil.getDouble(report.getRevenueSelf());
            double afterPayRevenue = StringUtil.getDouble(report.getAfterPayRevenue());
            double channelBuyAdd = StringUtil.getDouble(report.getChannelBuyAdd());

            double totalRevenue = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue + afterPayRevenue));//总收入
            double buyAdd = DoubleUtils.formatDouble(DoubleUtils.getDouble(channelBuyAdd + directBuyAdd));//总激活
            double spend = DoubleUtils.formatDouble(DoubleUtils.getDouble(channelSpend + directSpend));//总消耗

            double profit = DoubleUtils.formatDouble(DoubleUtils.getDouble(totalRevenue - spend));//毛利
            String roi = StringUtil.getString(DoubleUtils.getDouble(totalRevenue / spend * 100)) + "%";//整体ROI
            String payRoi = StringUtil.getString(DoubleUtils.getDouble(afterPayRevenue / spend * 100)) + "%";//付费ROI
            String revenueRoi = StringUtil.getString(DoubleUtils.getDouble(revenue / spend * 100)) + "%";//变现ROI
            double channelCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(channelSpend / channelBuyAdd));//渠道成本
            double directCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(directSpend / directBuyAdd));//总直投成本

            double ttDirectCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(ttDirectSpend / ttDirectBuyAdd));//头条直投成本
            double ttPayCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(ttDirectSpend / ttAddPayCount));//头条付费成本
            double ttPayLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(ttPayRevenue1 / ttDirectBuyAdd));//头条付费LTV1
            double ttRevenueLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(ttRevenue1 / ttDirectBuyAdd));//头条变现LTV1
            double ttLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble((ttPayRevenue1+ttRevenue1) / ttDirectBuyAdd));//头条总LTV1
            String ttRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(ttRevenue1 / ttDirectSpend * 100))  + "%";//头条首日广告ROI
            String ttPayRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(ttPayRevenue1 / ttDirectSpend * 100)) + "%";//头条首日付费ROI(分成前)
            String ttAfterPayRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(ttAfterPayRevenue1 / ttDirectSpend * 100)) + "%";//头条首日付费ROI(分成后)
            String ttTotalRoi1 = StringUtil.getString(DoubleUtils.getDouble((ttRevenue1+ttAfterPayRevenue1) / ttDirectSpend * 100)) + "%";//头条total首日付费ROI(分成后)
            String ttAddPayRate = StringUtil.getString(DoubleUtils.getDouble(ttAddPayCount / ttDirectBuyAdd * 100)) + "%";//头条新增付费率
            double ksDirectCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(ksDirectSpend / ksDirectBuyAdd));//快手直投成本
            double ksPayCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(ksDirectSpend / ksAddPayCount));//快手付费成本
            double ksPayLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(ksPayRevenue1 / ksDirectBuyAdd));//快手付费LTV1
            double ksRevenueLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(ksRevenue1 / ksDirectBuyAdd));//快手变现LTV1
            double ksLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble((ksPayRevenue1+ksRevenue1) / ksDirectBuyAdd));//快手总LTV1
            String ksRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(ksRevenue1 / ksDirectSpend * 100)) + "%";//快手首日广告ROI
            String ksPayRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(ksPayRevenue1 / ksDirectSpend * 100)) + "%";//快手首日付费ROI(分成前)
            String ksAfterPayRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(ksAfterPayRevenue1 / ksDirectSpend * 100)) + "%";//快手首日首日付费ROI(分成后)
            String ksTotalRoi1 = StringUtil.getString(DoubleUtils.getDouble((ksRevenue1+ksAfterPayRevenue1) / ksDirectSpend * 100)) + "%";//快手Total付费ROI
            String ksAddPayRate = StringUtil.getString(DoubleUtils.getDouble(ksAddPayCount / ksDirectBuyAdd * 100)) + "%";//快手新增付费率
            double gdtDirectCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(gdtDirectSpend / gdtDirectBuyAdd));//广点通直投成本
            double gdtPayCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(gdtDirectSpend / gdtAddPayCount));//广点通付费成本
            double gdtPayLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(gdtPayRevenue1 / gdtDirectBuyAdd));//广点通付费LTV1
            double gdtRevenueLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(gdtRevenue1 / gdtDirectBuyAdd));//广点通变现LTV1
            double gdtLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble((gdtPayRevenue1+gdtRevenue1) / gdtDirectBuyAdd));//广点通总LTV1
            String gdtRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(gdtRevenue1 / gdtDirectSpend * 100)) + "%";//广点通首日广告ROI
            String gdtPayRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(gdtPayRevenue1 / gdtDirectSpend * 100)) + "%";//广点通首日付费ROI(分成前)
            String gdtAfterPayRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(gdtAfterPayRevenue1 / gdtDirectSpend * 100)) + "%";//广点通首日付费ROI(分成后)
            String gdtTotalRoi1 = StringUtil.getString(DoubleUtils.getDouble((gdtRevenue1+gdtAfterPayRevenue1) / gdtDirectSpend * 100)) + "%";//广点通total首日付费ROI(分成后)
            String gdtAddPayRate = StringUtil.getString(DoubleUtils.getDouble(gdtAddPayCount / gdtDirectBuyAdd * 100)) + "%";//广点通新增付费率

            double addCost = 0d,activeArpu = 0d,nativeAddNum = 0,payArpu = 0d,totalArpu=0d,activeArpuChannel=0d,activeArpuSelf=0d;
            String buyAddRate = null,addRate = null;
            if("channel".equals(choose)) {  //渠道计算
                addCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / channelAddNum));//总体激活成本
                activeArpuChannel = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenueChannel / channelActiveNum));//活跃arpu 渠道
                activeArpuSelf = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenueSelf / channelActiveNum));//活跃arpu 自建聚合
                activeArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue / channelActiveNum));//活跃arpu
                nativeAddNum = DoubleUtils.formatDouble(DoubleUtils.getDouble(channelAddNum - buyAdd));//自然新增
                buyAddRate = StringUtil.getString(DoubleUtils.getDouble(buyAdd / channelAddNum * 100)) + "%";//投放新增占比
                addRate = StringUtil.getString(DoubleUtils.getDouble(channelAddNum / channelActiveNum * 100)) + "%";//新增占比
                payArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(afterPayRevenue / channelActiveNum));//付费ARPU
                totalArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(totalRevenue / channelActiveNum));//整体ARPU
            }else{      //友盟计算
                addCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / umAddNum));//总体激活成本
                activeArpuChannel = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenueChannel / umActiveNum));//活跃arpu 渠道
                activeArpuSelf = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenueSelf / umActiveNum));//活跃arpu 自建聚合
                activeArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue / umActiveNum));//活跃arpu
                nativeAddNum = DoubleUtils.formatDouble(DoubleUtils.getDouble(umAddNum - buyAdd));//自然新增
                buyAddRate = StringUtil.getString(DoubleUtils.getDouble(buyAdd / umAddNum * 100)) + "%";//投放新增占比
                addRate = StringUtil.getString(DoubleUtils.getDouble(umAddNum / umActiveNum * 100)) + "%";//新增占比
                payArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(afterPayRevenue / umActiveNum));//付费ARPU
                totalArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(totalRevenue / umActiveNum));//整体ARPU
            }
            //增加字段买量ROI和总买量收入,渠道买量ROI、渠道买量收入、直投买量ROI、直投买量收入
            //  直投买量收入
            String directBuyRevenueTotal = DoubleUtils.getDouble(report.getDirectBuyRevenueTotal());
            // 渠道买量收入
            String channelBuyRevenueTotal = DoubleUtils.getDouble(report.getChannelBuyRevenueTotal());
            // 总买量收入
            String buyRevenueTotal = DoubleUtils.getDouble(StringUtil.getDouble(directBuyRevenueTotal) + StringUtil.getDouble(channelBuyRevenueTotal));
            // 买量ROI
            String buyRoi = DoubleUtils.getDouble(StringUtil.getDouble(buyRevenueTotal)/spend * 100) + "%";
            // 渠道买量ROI
            String channelBuyRoi = DoubleUtils.getDouble(StringUtil.getDouble(channelBuyRevenueTotal)/channelSpend * 100) + "%";
            // 直投买量ROI
            String directBuyRoi = DoubleUtils.getDouble(StringUtil.getDouble(directBuyRevenueTotal)/directSpend * 100) + "%";
            report.setBuyRoi(buyRoi);
            report.setBuyRevenueTotal(buyRevenueTotal);
            report.setChannelBuyRoi(channelBuyRoi);
            report.setDirectBuyRoi(directBuyRoi);
            report.setDirectBuyRevenueTotal(directBuyRevenueTotal);
            report.setChannelBuyRevenueTotal(channelBuyRevenueTotal);

            report.setActiveArpuChannel(activeArpuChannel);
            report.setActiveArpuSelf(activeArpuSelf);
            report.setSpend(spend);
            report.setTotalRevenue(totalRevenue);
            report.setAddCost(addCost);
            report.setActiveArpu(activeArpu);
            report.setPayArpu(payArpu);
            report.setTotalArpu(totalArpu);
            report.setProfit(profit);
            report.setRoi(roi);
            report.setPayRoi(payRoi);
            report.setRevenueRoi(revenueRoi);
            report.setChannelCost(channelCost);
            report.setDirectCost(directCost);
            report.setNativeAddNum(nativeAddNum);
            report.setBuyAddRate(buyAddRate);
            report.setAddRate(addRate);
            report.setTtDirectCost(ttDirectCost);
            report.setTtPayCost(ttPayCost);
            report.setTtPayLtv1(ttPayLtv1);
            report.setTtRevenueLtv1(ttRevenueLtv1);
            report.setTtLtv1(ttLtv1);
            report.setTtAddPayRate(ttAddPayRate);
            report.setTtRoi1(ttTotalRoi1);
            report.setTtPayRoi1(ttPayRevenueRoi1);
            report.setTtAfterPayRoi1(ttAfterPayRevenueRoi1);
            report.setTtRevenueRoi1(ttRevenueRoi1);
            report.setKsDirectCost(ksDirectCost);
            report.setKsPayCost(ksPayCost);
            report.setKsPayLtv1(ksPayLtv1);
            report.setKsRevenueLtv1(ksRevenueLtv1);
            report.setKsLtv1(ksLtv1);
            report.setKsAddPayRate(ksAddPayRate);
            report.setKsRoi1(ksTotalRoi1);
            report.setKsPayRoi1(ksPayRevenueRoi1);
            report.setKsAfterPayRoi1(ksAfterPayRevenueRoi1);
            report.setKsRevenueRoi1(ksRevenueRoi1);
            report.setGdtDirectCost(gdtDirectCost);
            report.setGdtPayCost(gdtPayCost);
            report.setGdtPayLtv1(gdtPayLtv1);
            report.setGdtRevenueLtv1(gdtRevenueLtv1);
            report.setGdtLtv1(gdtLtv1);
            report.setGdtAddPayRate(gdtAddPayRate);
            report.setGdtRoi1(gdtTotalRoi1);
            report.setGdtPayRoi1(gdtPayRevenueRoi1);
            report.setGdtAfterPayRoi1(gdtAfterPayRevenueRoi1);
            report.setGdtRevenueRoi1(gdtRevenueRoi1);
            ttRevenue1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(ttRevenue1));
            ksRevenue1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(ksRevenue1));
            gdtRevenue1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(gdtRevenue1));
            report.setTtRevenue1(ttRevenue1);
            report.setKsRevenue1(ksRevenue1);
            report.setGdtRevenue1(gdtRevenue1);
            report.setChannelRoi1(report.getChannelRoi1()+"%");
            report.setDirectRoi1(report.getDirectRoi1()+"%");
            report.setRoi1(report.getRoi1()+"%");
        }
        return reportList;
    }

    public DirectReportDTO addDirectReportIndex(DirectReportDTO report,String choose) {
        if (report == null) {
            return new DirectReportDTO();
        }

        double channelSpend = StringUtil.getDouble(report.getChannelSpend());
        double directSpend = StringUtil.getDouble(report.getDirectSpend());
        double directBuyAdd = StringUtil.getDouble(report.getDirectBuyAdd());

        double ttDirectSpend = StringUtil.getDouble(report.getTtDirectSpend());
        double ttDirectBuyAdd = StringUtil.getDouble(report.getTtDirectBuyAdd());
        double ttRevenue1 = StringUtil.getDouble(report.getTtRevenue1());
        double ttPayRevenue1 = StringUtil.getDouble(report.getTtPayRevenue1());
        double ttAfterPayRevenue1 = StringUtil.getDouble(report.getTtAfterPayRevenue1());
        double ttAddPayCount = StringUtil.getDouble(report.getTtAddPayCount());
        double ksDirectSpend = StringUtil.getDouble(report.getKsDirectSpend());
        double ksDirectBuyAdd = StringUtil.getDouble(report.getKsDirectBuyAdd());
        double ksRevenue1 = StringUtil.getDouble(report.getKsRevenue1());
        double ksPayRevenue1 = StringUtil.getDouble(report.getKsPayRevenue1());
        double ksAfterPayRevenue1 = StringUtil.getDouble(report.getKsAfterPayRevenue1());
        double ksAddPayCount = StringUtil.getDouble(report.getKsAddPayCount());
        double gdtDirectSpend = StringUtil.getDouble(report.getGdtDirectSpend());
        double gdtDirectBuyAdd = StringUtil.getDouble(report.getGdtDirectBuyAdd());
        double gdtRevenue1 = StringUtil.getDouble(report.getGdtRevenue1());
        double gdtPayRevenue1 = StringUtil.getDouble(report.getGdtPayRevenue1());
        double gdtAfterPayRevenue1 = StringUtil.getDouble(report.getGdtAfterPayRevenue1());
        double gdtAddPayCount = StringUtil.getDouble(report.getGdtAddPayCount());

        double umAddNum = StringUtil.getDouble(report.getUmAddNum());
        double channelAddNum = StringUtil.getDouble(report.getChannelAddNum());
        double umActiveNum = StringUtil.getDouble(report.getUmActiveNum());
        double channelActiveNum = StringUtil.getDouble(report.getChannelActiveNum());
        double revenue = StringUtil.getDouble(report.getRevenue());
        double revenueChannel = StringUtil.getDouble(report.getRevenueChannel());
        double revenueSelf = StringUtil.getDouble(report.getRevenueSelf());
        double afterPayRevenue = StringUtil.getDouble(report.getAfterPayRevenue());
        double channelBuyAdd = StringUtil.getDouble(report.getChannelBuyAdd());

        double totalRevenue = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue + afterPayRevenue));//总收入
        double buyAdd = DoubleUtils.formatDouble(DoubleUtils.getDouble(channelBuyAdd + directBuyAdd));//总激活
        double spend = DoubleUtils.formatDouble(DoubleUtils.getDouble(channelSpend + directSpend));//总消耗

        double profit = DoubleUtils.formatDouble(DoubleUtils.getDouble(totalRevenue - spend));//毛利
        String roi = StringUtil.getString(DoubleUtils.getDouble(totalRevenue / spend * 100)) + "%";//整体ROI
        String payRoi = StringUtil.getString(DoubleUtils.getDouble(afterPayRevenue / spend * 100)) + "%";//付费ROI
        String revenueRoi = StringUtil.getString(DoubleUtils.getDouble(revenue / spend * 100)) + "%";//变现ROI
        double channelCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(channelSpend / channelBuyAdd));//渠道成本
        double directCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(directSpend / directBuyAdd));//总直投成本

        double ttDirectCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(ttDirectSpend / ttDirectBuyAdd));//头条直投成本
        double ttPayCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(ttDirectSpend / ttAddPayCount));//头条付费成本
        double ttPayLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(ttPayRevenue1 / ttDirectBuyAdd));//头条付费LTV1
        double ttRevenueLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(ttRevenue1 / ttDirectBuyAdd));//头条变现LTV1
        double ttLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble((ttPayRevenue1+ttRevenue1) / ttDirectBuyAdd));//头条总LTV1
        String ttRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(ttRevenue1 / ttDirectSpend * 100))  + "%";//头条首日广告ROI
        String ttPayRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(ttPayRevenue1 / ttDirectSpend * 100)) + "%";//头条首日付费ROI(分成前)
        String ttAfterPayRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(ttAfterPayRevenue1 / ttDirectSpend * 100)) + "%";//头条首日付费ROI(分成后)
        String ttTotalRoi1 = StringUtil.getString(DoubleUtils.getDouble((ttRevenue1+ttAfterPayRevenue1) / ttDirectSpend * 100)) + "%";//头条total首日付费ROI(分成后)
        String ttAddPayRate = StringUtil.getString(DoubleUtils.getDouble(ttAddPayCount / ttDirectBuyAdd * 100)) + "%";//头条新增付费率
        double ksDirectCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(ksDirectSpend / ksDirectBuyAdd));//快手直投成本
        double ksPayCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(ksDirectSpend / ksAddPayCount));//快手付费成本
        double ksPayLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(ksPayRevenue1 / ksDirectBuyAdd));//快手付费LTV1
        double ksRevenueLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(ksRevenue1 / ksDirectBuyAdd));//快手变现LTV1
        double ksLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble((ksPayRevenue1+ksRevenue1) / ksDirectBuyAdd));//快手总LTV1
        String ksRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(ksRevenue1 / ksDirectSpend * 100)) + "%";//快手首日广告ROI
        String ksPayRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(ksPayRevenue1 / ksDirectSpend * 100)) + "%";//快手首日付费ROI(分成前)
        String ksAfterPayRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(ksAfterPayRevenue1 / ksDirectSpend * 100)) + "%";//快手首日首日付费ROI(分成后)
        String ksTotalRoi1 = StringUtil.getString(DoubleUtils.getDouble((ksRevenue1+ksAfterPayRevenue1) / ksDirectSpend * 100)) + "%";//快手Total付费ROI
        String ksAddPayRate = StringUtil.getString(DoubleUtils.getDouble(ksAddPayCount / ksDirectBuyAdd * 100)) + "%";//快手新增付费率
        double gdtDirectCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(gdtDirectSpend / gdtDirectBuyAdd));//广点通直投成本
        double gdtPayCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(gdtDirectSpend / gdtAddPayCount));//广点通付费成本
        double gdtPayLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(gdtPayRevenue1 / gdtDirectBuyAdd));//广点通付费LTV1
        double gdtRevenueLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(gdtRevenue1 / gdtDirectBuyAdd));//广点通变现LTV1
        double gdtLtv1 = DoubleUtils.formatDouble(DoubleUtils.getDouble((gdtPayRevenue1+gdtRevenue1) / gdtDirectBuyAdd));//广点通总LTV1
        String gdtRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(gdtRevenue1 / gdtDirectSpend * 100)) + "%";//广点通首日广告ROI
        String gdtPayRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(gdtPayRevenue1 / gdtDirectSpend * 100)) + "%";//广点通首日付费ROI(分成前)
        String gdtAfterPayRevenueRoi1 = StringUtil.getString(DoubleUtils.getDouble(gdtAfterPayRevenue1 / gdtDirectSpend * 100)) + "%";//广点通首日付费ROI(分成后)
        String gdtTotalRoi1 = StringUtil.getString(DoubleUtils.getDouble((gdtRevenue1+gdtAfterPayRevenue1) / gdtDirectSpend * 100)) + "%";//广点通total首日付费ROI(分成后)
        String gdtAddPayRate = StringUtil.getString(DoubleUtils.getDouble(gdtAddPayCount / gdtDirectBuyAdd * 100)) + "%";//广点通新增付费率

        double addCost = 0d,activeArpu = 0d,nativeAddNum = 0,payArpu = 0d,totalArpu=0d,activeArpuChannel=0d,activeArpuSelf=0d;
        String buyAddRate = null,addRate = null;
        if("channel".equals(choose)) {  //渠道计算
            addCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / channelAddNum));//总体激活成本
            activeArpuChannel = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenueChannel / channelActiveNum));//活跃arpu 渠道
            activeArpuSelf = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenueSelf / channelActiveNum));//活跃arpu 自建聚合
            activeArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue / channelActiveNum));//活跃arpu
            nativeAddNum = DoubleUtils.formatDouble(DoubleUtils.getDouble(channelAddNum - buyAdd));//自然新增
            buyAddRate = StringUtil.getString(DoubleUtils.getDouble(buyAdd / channelAddNum * 100)) + "%";//投放新增占比
            addRate = StringUtil.getString(DoubleUtils.getDouble(channelAddNum / channelActiveNum * 100)) + "%";//新增占比
            payArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(afterPayRevenue / channelActiveNum));//付费ARPU
            totalArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(totalRevenue / channelActiveNum));//整体ARPU
        }else{      //友盟计算
            addCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(spend / umAddNum));//总体激活成本
            activeArpuChannel = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenueChannel / umActiveNum));//活跃arpu 渠道
            activeArpuSelf = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenueSelf / umActiveNum));//活跃arpu 自建聚合
            activeArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue / umActiveNum));//活跃arpu
            nativeAddNum = DoubleUtils.formatDouble(DoubleUtils.getDouble(umAddNum - buyAdd));//自然新增
            buyAddRate = StringUtil.getString(DoubleUtils.getDouble(buyAdd / umAddNum * 100)) + "%";//投放新增占比
            addRate = StringUtil.getString(DoubleUtils.getDouble(umAddNum / umActiveNum * 100)) + "%";//新增占比
            payArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(afterPayRevenue / umActiveNum));//付费ARPU
            totalArpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(totalRevenue / umActiveNum));//整体ARPU
        }
        //增加字段买量ROI和总买量收入,渠道买量ROI、渠道买量收入、直投买量ROI、直投买量收入
        //增加字段买量ROI和总买量收入,渠道买量ROI、渠道买量收入、直投买量ROI、直投买量收入
        //  直投买量收入
        String directBuyRevenueTotal = DoubleUtils.getDouble(report.getDirectBuyRevenueTotal());
        // 渠道买量收入
        String channelBuyRevenueTotal = DoubleUtils.getDouble(report.getChannelBuyRevenueTotal());
        // 总买量收入
        String buyRevenueTotal = DoubleUtils.getDouble(StringUtil.getDouble(directBuyRevenueTotal) + StringUtil.getDouble(channelBuyRevenueTotal));
        // 买量ROI
        String buyRoi = DoubleUtils.getDouble(StringUtil.getDouble(buyRevenueTotal)/spend * 100) + "%";
        // 渠道买量ROI
        String channelBuyRoi = DoubleUtils.getDouble(StringUtil.getDouble(channelBuyRevenueTotal)/channelSpend * 100) + "%";
        // 直投买量ROI
        String directBuyRoi = DoubleUtils.getDouble(StringUtil.getDouble(directBuyRevenueTotal)/directSpend * 100) + "%";
        report.setBuyRoi(buyRoi);
        report.setBuyRevenueTotal(buyRevenueTotal);
        report.setChannelBuyRoi(channelBuyRoi);
        report.setDirectBuyRoi(directBuyRoi);
        report.setDirectBuyRevenueTotal(directBuyRevenueTotal);
        report.setChannelBuyRevenueTotal(channelBuyRevenueTotal);

        report.setActiveArpuChannel(activeArpuChannel);
        report.setActiveArpuSelf(activeArpuSelf);
        report.setSpend(spend);
        report.setTotalRevenue(totalRevenue);
        report.setAddCost(addCost);
        report.setActiveArpu(activeArpu);
        report.setPayArpu(payArpu);
        report.setTotalArpu(totalArpu);
        report.setProfit(profit);
        report.setRoi(roi);
        report.setPayRoi(payRoi);
        report.setRevenueRoi(revenueRoi);
        report.setChannelCost(channelCost);
        report.setDirectCost(directCost);
        report.setNativeAddNum(nativeAddNum);
        report.setBuyAddRate(buyAddRate);
        report.setAddRate(addRate);
        report.setTtDirectCost(ttDirectCost);
        report.setTtPayCost(ttPayCost);
        report.setTtPayLtv1(ttPayLtv1);
        report.setTtRevenueLtv1(ttRevenueLtv1);
        report.setTtLtv1(ttLtv1);
        report.setTtAddPayRate(ttAddPayRate);
        report.setTtRoi1(ttTotalRoi1);
        report.setTtPayRoi1(ttPayRevenueRoi1);
        report.setTtAfterPayRoi1(ttAfterPayRevenueRoi1);
        report.setTtRevenueRoi1(ttRevenueRoi1);
        report.setKsDirectCost(ksDirectCost);
        report.setKsPayCost(ksPayCost);
        report.setKsPayLtv1(ksPayLtv1);
        report.setKsRevenueLtv1(ksRevenueLtv1);
        report.setKsLtv1(ksLtv1);
        report.setKsAddPayRate(ksAddPayRate);
        report.setKsRoi1(ksTotalRoi1);
        report.setKsPayRoi1(ksPayRevenueRoi1);
        report.setKsAfterPayRoi1(ksAfterPayRevenueRoi1);
        report.setKsRevenueRoi1(ksRevenueRoi1);
        report.setGdtDirectCost(gdtDirectCost);
        report.setGdtPayCost(gdtPayCost);
        report.setGdtPayLtv1(gdtPayLtv1);
        report.setGdtRevenueLtv1(gdtRevenueLtv1);
        report.setGdtLtv1(gdtLtv1);
        report.setGdtAddPayRate(gdtAddPayRate);
        report.setGdtRoi1(gdtTotalRoi1);
        report.setGdtPayRoi1(gdtPayRevenueRoi1);
        report.setGdtAfterPayRoi1(gdtAfterPayRevenueRoi1);
        report.setGdtRevenueRoi1(gdtRevenueRoi1);
        ttRevenue1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(ttRevenue1));
        ksRevenue1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(ksRevenue1));
        gdtRevenue1 = DoubleUtils.formatDouble(DoubleUtils.getDouble(gdtRevenue1));
        report.setTtRevenue1(ttRevenue1);
        report.setKsRevenue1(ksRevenue1);
        report.setGdtRevenue1(gdtRevenue1);
        report.setChannelRoi1(report.getChannelRoi1()+"%");
        report.setDirectRoi1(report.getDirectRoi1()+"%");
        report.setRoi1(report.getRoi1()+"%");
        return report;
    }

    private List<SubscribeRoiReportDTO> addSubscribeReportListIndex(List<SubscribeRoiReportDTO> reportList){
        if (CollectionUtils.isEmpty(reportList)) {
            return new ArrayList<>();
        }
        reportList.removeAll(Collections.singleton(null));

        for(SubscribeRoiReportDTO report : reportList) {
            Double installs = StringUtil.getDouble(report.getInstalls());
            Double clicks = StringUtil.getDouble(report.getClicks());
            Double cost = StringUtil.getDouble(report.getCost());
            Double fee_user_cnt = StringUtil.getDouble(report.getFee_user_cnt());
            Double new_pay_user_cnt = StringUtil.getDouble(report.getNew_pay_user_cnt());
            Double new_pay_money = StringUtil.getDouble(report.getNew_pay_money());
            Double total_sub_user_cnt = StringUtil.getDouble(report.getTotal_sub_user_cnt());
            Double total_sub_money = StringUtil.getDouble(report.getTotal_sub_money());

            String installsRate = DoubleUtils.formatDouble(DoubleUtils.getDouble(installs / clicks * 100)) + "%";                      //激活率
            Double installsCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(cost / installs));                                     //激活成本
            String fee_user_rate = DoubleUtils.formatDouble(DoubleUtils.getDouble(fee_user_cnt / installs * 100)) + "%";               //试用率
            Double fee_user_cost = DoubleUtils.formatDouble(DoubleUtils.getDouble(cost / fee_user_cnt));                                //试用成本
            String new_pay_user_rate = DoubleUtils.formatDouble(DoubleUtils.getDouble(new_pay_user_cnt / installs * 100)) + "%";       //首日订阅率
            Double new_pay_user_cost = DoubleUtils.formatDouble(DoubleUtils.getDouble(cost / new_pay_user_cnt));                        //首日订阅成本
            Double new_pay_arpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(new_pay_money / installs));                            //首日订阅ARPU
            Double new_pay_arppu = DoubleUtils.formatDouble(DoubleUtils.getDouble(new_pay_money / new_pay_user_cnt));                   //首日订阅ARPPU
            String new_pay_roi = DoubleUtils.formatDouble(DoubleUtils.getDouble(new_pay_money / cost * 100)) + "%";                    //首日订阅Roi
            String total_sub_user_rate = DoubleUtils.formatDouble(DoubleUtils.getDouble(total_sub_user_cnt / installs * 100)) + "%";   //总订阅率
            Double total_sub_user_cost = DoubleUtils.formatDouble(DoubleUtils.getDouble(cost / total_sub_user_cnt));                    //总订阅成本
            Double total_sub_arpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(total_sub_money / installs));                        //总订阅ARPU
            Double total_sub_arppu = DoubleUtils.formatDouble(DoubleUtils.getDouble(total_sub_money / total_sub_user_cnt));             //总订阅ARPPU
            String total_sub_roi = DoubleUtils.formatDouble(DoubleUtils.getDouble(total_sub_money / cost * 100)) + "%";              //总订阅Roi

            report.setInstallsRate(installsRate);
            report.setInstallsCost(installsCost);
            report.setFee_user_rate(fee_user_rate);
            report.setFee_user_cost(fee_user_cost);
            report.setNew_pay_user_rate(new_pay_user_rate);
            report.setNew_pay_user_cost(new_pay_user_cost);
            report.setNew_pay_arpu(new_pay_arpu);
            report.setNew_pay_arppu(new_pay_arppu);
            report.setNew_pay_roi(new_pay_roi);
            report.setTotal_sub_user_rate(total_sub_user_rate);
            report.setTotal_sub_user_cost(total_sub_user_cost);
            report.setTotal_sub_arpu(total_sub_arpu);
            report.setTotal_sub_arppu(total_sub_arppu);
            report.setTotal_sub_roi(total_sub_roi);

        }
        return reportList;
    }

    private SubscribeRoiReportDTO addSubscribeReportIndex(SubscribeRoiReportDTO report){
        if (report == null) {
            return new SubscribeRoiReportDTO();
        }
        Double installs = StringUtil.getDouble(report.getInstalls());
        Double clicks = StringUtil.getDouble(report.getClicks());
        Double cost = StringUtil.getDouble(report.getCost());
        Double fee_user_cnt = StringUtil.getDouble(report.getFee_user_cnt());
        Double new_pay_user_cnt = StringUtil.getDouble(report.getNew_pay_user_cnt());
        Double new_pay_money = StringUtil.getDouble(report.getNew_pay_money());
        Double total_sub_user_cnt = StringUtil.getDouble(report.getTotal_sub_user_cnt());
        Double total_sub_money = StringUtil.getDouble(report.getTotal_sub_money());

        String installsRate = DoubleUtils.formatDouble(DoubleUtils.getDouble(installs / clicks  * 100)) + "%";                      //激活率
        Double installsCost = DoubleUtils.formatDouble(DoubleUtils.getDouble(cost / installs));                                     //激活成本
        String fee_user_rate = DoubleUtils.formatDouble(DoubleUtils.getDouble(fee_user_cnt / installs  * 100)) + "%";               //试用率
        Double fee_user_cost = DoubleUtils.formatDouble(DoubleUtils.getDouble(cost / fee_user_cnt));                                //试用成本
        String new_pay_user_rate = DoubleUtils.formatDouble(DoubleUtils.getDouble(new_pay_user_cnt / installs  * 100)) + "%";       //首日订阅率
        Double new_pay_user_cost = DoubleUtils.formatDouble(DoubleUtils.getDouble(cost / new_pay_user_cnt));                 //首日订阅成本
        Double new_pay_arpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(new_pay_money / installs));                            //首日订阅ARPU
        Double new_pay_arppu = DoubleUtils.formatDouble(DoubleUtils.getDouble(new_pay_money / new_pay_user_cnt));                   //首日订阅ARPPU
        String new_pay_roi = DoubleUtils.formatDouble(DoubleUtils.getDouble(new_pay_money / cost  * 100)) + "%";                    //首日订阅Roi
        String total_sub_user_rate = DoubleUtils.formatDouble(DoubleUtils.getDouble(total_sub_user_cnt / installs  * 100)) + "%";   //总订阅率
        Double total_sub_user_cost = DoubleUtils.formatDouble(DoubleUtils.getDouble(cost / total_sub_user_cnt));                    //总订阅成本
        Double total_sub_arpu = DoubleUtils.formatDouble(DoubleUtils.getDouble(total_sub_money / installs));                        //总订阅ARPU
        Double total_sub_arppu = DoubleUtils.formatDouble(DoubleUtils.getDouble(total_sub_money / total_sub_user_cnt));             //总订阅ARPPU
        String total_sub_roi = DoubleUtils.formatDouble(DoubleUtils.getDouble(total_sub_money / clicks  * 100)) + "%";              //总订阅Roi

        report.setInstallsRate(installsRate);
        report.setInstallsCost(installsCost);
        report.setFee_user_rate(fee_user_rate);
        report.setFee_user_cost(fee_user_cost);
        report.setNew_pay_user_rate(new_pay_user_rate);
        report.setNew_pay_user_cost(new_pay_user_cost);
        report.setNew_pay_arpu(new_pay_arpu);
        report.setNew_pay_arppu(new_pay_arppu);
        report.setNew_pay_roi(new_pay_roi);
        report.setTotal_sub_user_rate(total_sub_user_rate);
        report.setTotal_sub_user_cost(total_sub_user_cost);
        report.setTotal_sub_arpu(total_sub_arpu);
        report.setTotal_sub_arppu(total_sub_arppu);
        report.setTotal_sub_roi(total_sub_roi);

        return report;
    }

    public InfoResult getHourSpendReport(SpendReportParam param, String username) {
        InfoResult infoResult = new InfoResult();
        PageHelper.startPage(param.getStart(),param.getLimit());


        List<String> groups = param.getGroup();
        Page<SpendReportDTO> reportPage = dnwxBiAdtMapper.getHourSpendReport(param);
        List<SpendReportDTO> reportList = reportPage.getResult();

        Map<String,Object> map = new HashMap<>();
        if (reportList.size() > 0) {
            if(!param.getGroup().contains("media")){
                param.getGroup().add("media");
            }
            SpendReportDTO spendReportDTO = dnwxBiAdtMapper.getHourSpendReportSummary(param);
            addChinaReportIndicator(spendReportDTO, groups,username);
            spendReportDTO.setMedia(null);
            map.put("total",spendReportDTO);
            convertAppAndCountry(reportList, param);
            addChinaReportListIndicator(reportList, groups,username,null);
        }

        String today = LocalDate.now().toString();
        String yesterday = DateUtils.getDay(-1);

        List<SpendReportDTO> spendReportChartDay = dnwxBiAdtMapper.getHourSpendReportChart(param);

        param.setStart_date(today);
        param.setEnd_date(today);
        List<SpendReportDTO> spendReportChart = dnwxBiAdtMapper.getHourSpendReportChart(param);

        param.setStart_date(yesterday);
        param.setEnd_date(yesterday);
        List<SpendReportDTO> spendReportChart1 = dnwxBiAdtMapper.getHourSpendReportChart(param);


        map.put("chart",spendReportChart);
        map.put("chart1",spendReportChart1);
        map.put("chartDay",spendReportChartDay);


        infoResult.setRet(1);
        infoResult.setMsg("查询成功");
        map.put("list",reportList);
        map.put("totalSize",reportPage.getTotal());
        infoResult.setData(map);
        return infoResult;
    }

    public void exportHourSpendReport(SpendReportParam param,String username,HttpServletResponse response) {
        PageHelper.startPage(PAGE_NUM, MAX_PAGE_SIZE);
        List<String> groups = param.getGroup();

        String key = param.getKey();
        if (!Strings.isNullOrEmpty(key)) {
            String[] strings = key.split("&");
            param.setIndex(strings[0]);
            param.setSymbol(strings[1]);
            param.setNumber(StringUtil.getDouble(strings[2]));
        }

        Page<SpendReportDTO> reportPage = dnwxBiAdtMapper.getHourSpendReport(param);

        List<SpendReportDTO> reportList = reportPage.getResult();
        if (CollectionUtils.isEmpty(reportList)) {
            return ;
        }

        if (reportList.size() > 0) {
            convertAppAndCountry(reportList, param);
            addChinaReportListIndicator(reportList, groups,username,null);
        }

        String value = param.getValue();
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        if (!StringUtils.isNullOrEmpty(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0], s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        if(value.contains("type")) {
            for (SpendReportDTO report : reportList) {
                String type = report.getType();
                String str = "";
                if (null != type) {
                    if (StringUtil.getInt(type) == 1) {
                        str = "自运营-分包";
                    } else if (StringUtil.getInt(type) == 2) {
                        str = "代运营-分包";
                    } else if (StringUtil.getInt(type) == 3) {
                        str = "自运营-直投";
                    } else if (StringUtil.getInt(type) == 4) {
                        str = "代运营-直投";
                    } else if (StringUtil.getInt(type) == 5) {
                        str = "自运营-OPPO直投";
                    } else if (StringUtil.getInt(type) == 6) {
                        str = "自运营-VIVO直投";
                    } else if (StringUtil.getInt(type) == 7) {
                        str = "自运营-华为直投";
                    } else if (StringUtil.getInt(type) == 8) {
                        str = "自运营-小米直投";
                    } else if (StringUtil.getInt(type) == 9) {
                        str = "代运营-OPPO直投";
                    } else if (StringUtil.getInt(type) == 10) {
                        str = "代运营-VIVO直投";
                    } else if (StringUtil.getInt(type) == 11) {
                        str = "代运营-华为直投";
                    } else if (StringUtil.getInt(type) == 12) {
                        str = "代运营-小米直投";
                    } else if (StringUtil.getInt(type) == 13) {
                        str = "自运营-OPPOMJ";
                    } else if (StringUtil.getInt(type) == 14) {
                        str = "代运营-OPPOMJ";
                    } else if (StringUtil.getInt(type) == 15) {
                        str = "自运营-小米直投2";
                    }
                }
                report.setType(str);

            }
        }
        List<Map> transferTypeList = dnwxAdtMapper.getTransferType();
        Map trasferTypeMap = transferTypeList.stream().collect(Collectors.toMap(map -> StringUtil.getString(map.get("id")),map -> StringUtil.getString(map.get("strategyName")),(k1,k2)->k2));

        if(value.contains("transferType")){
            for (SpendReportDTO report : reportList) {
                String transerType = report.getTransferType();
                String str1 = StringUtil.getString(trasferTypeMap.get(transerType));
                report.setTransferType(str1);
            }
        }
        ExportExcelUtil.exportXLSX2(response, reportList, headerMap, "投放细分数据报表_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");

    }

    private double getDoubleTwo(Double a, Long b) {
        double d = 0;
        if (null != a
                && null != b
                && a > 0
                && b > 0) {
            d = DoubleUtils.formatDouble(DoubleUtils.getDouble(a / b));
        }
        return d;
    }


    public InfoResult getChinaReportCondition() {
        InfoResult infoResult = new InfoResult();
        List<ChannelDTO> channelList = jettisonCommonMapper.getChannel();
        List<String> channelTypeList = jettisonCommonMapper.getChannelTypeName();
        List<Map> artistList = jettisonCommonMapper.getArtist();
        List<Map> sdList = jettisonCommonMapper.getArtistByType("1");
        List<Map> zrList = jettisonCommonMapper.getArtistByType("2");

        Map map = new HashMap();
        List<String> list = Lists.newArrayList();
        map.put("channelTypes", channelTypeList);
        map.put("agents", list);
        map.put("putUsers", list);
        map.put("artists", list);

        if (!CollectionUtils.isEmpty(channelList)) {
            List<String> medias = Lists.newArrayList();
            for (ChannelDTO channel : channelList) {
                medias.add(channel.getCha_media());
            }
            medias = medias.stream()
                    .distinct()
                    .filter(s -> s != null)
                    .collect(Collectors.toList());
            map.put("medias", medias);
        }

        List<String> putUsers = jettisonCommonMapper.getPutUser();
        if(!CollectionUtils.isEmpty(putUsers)) {
            putUsers = putUsers.stream()
                    .distinct()
                    .filter(s -> s != null)
                    .collect(Collectors.toList());
            map.put("putUsers", putUsers);
        }

        List<String> agents = jettisonCommonMapper.getAgentByName();
        if(!CollectionUtils.isEmpty(agents)) {
            agents = agents.stream()
                    .distinct()
                    .filter(s -> s != null)
                    .collect(Collectors.toList());
            map.put("agents", agents);
        }

        if (!CollectionUtils.isEmpty(artistList)) {
            map.put("artists", artistList);
        }
        if (!CollectionUtils.isEmpty(sdList)) {
            map.put("sdlists", sdList);
        }
        if (!CollectionUtils.isEmpty(zrList)) {
            map.put("zrlists", zrList);
        }

        List<HashMap> allAccountList = tfxtMapper.getChinaPlatformAccount();
        map.put("accounts", allAccountList);

        List<String> gameNameList = jettisonCommonMapper.getGameName();
        if (!CollectionUtils.isEmpty(gameNameList)) {
            gameNameList = gameNameList.stream()
                    .distinct()
                    .filter(s -> s != null)
                    .collect(Collectors.toList());
            map.put("gameNames", gameNameList);
        }
        infoResult.setRet(1);
        infoResult.setData(map);
        return infoResult;
    }


    public InfoResult getArtist(){
        InfoResult infoResult = new InfoResult();
        List<Map> artistList = jettisonCommonMapper.getArtist();
        Map map = new HashMap();
        List<String> list = Lists.newArrayList();
        map.put("artists", list);
        if (!CollectionUtils.isEmpty(artistList)) {
            map.put("artists", artistList);
        }
        infoResult.setRet(1);
        infoResult.setData(map);

        return infoResult;
    }


    public InfoResult getAgent(){
        InfoResult infoResult = new InfoResult();
        Map map = new HashMap();
        List<Map<String,String>> list = Lists.newArrayList();
        map.put("agents", list);

        List<AgentDTO> agents = jettisonCommonMapper.getAgent();
        List<Map<String,String>> agentList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(agents)) {
            for(AgentDTO agent :agents){
                Map<String,String> agentMap = new HashMap<>();
                StringBuffer sb =new StringBuffer();
                agentMap.put("firstAgent",agent.getFirst_agent());
                for(int i=0;i<agents.size();i++){
                    if(StringUtil.getString(agentMap.get("firstAgent")).equals(agents.get(i).getFirst_agent())){
                        if (sb.length()==0){
                            sb.append(agents.get(i).getAgent());
                        }else{
                            sb.append(",").append(agents.get(i).getAgent());
                        }
                    }
                }
                agentMap.put("secondAgent",sb.toString());
                agentList.add(agentMap);
            }
            if (!CollectionUtils.isEmpty(agentList)) {
                agentList = agentList.stream()
                        .distinct()
                        .collect(Collectors.toList());
                map.put("agents", agentList);
            }
        }
        infoResult.setRet(1);
        infoResult.setData(map);
        return infoResult;
    }


    public InfoResult getAccountType(){
        InfoResult infoResult = new InfoResult();
        List<Map<String,String>> list = jettisonCommonMapper.getAccountType();
        infoResult.setRet(1);
        infoResult.setData(list);
        return infoResult;
    }


    public InfoResult getChannel(List<String> medias) {
        InfoResult  infoResult = new InfoResult();
        if (CollectionUtils.isEmpty(medias)) {
            infoResult.setRet(0);
            infoResult.setMsg("请选择媒体");
            return infoResult;
        }

        List<ChannelDTO> channelList = jettisonCommonMapper.getChannel();
        List<String> list = channelList.stream()
                .map(ChannelDTO::getCha_sub_launch)
                .distinct()
                .collect(Collectors.toList());

        infoResult.setRet(1);
        infoResult.setData(list);
        return infoResult;
    }


    public InfoResult getCompanyName() {
        InfoResult  infoResult = new InfoResult();
        List<String> list = jettisonCommonMapper.getCompanyName();
        infoResult.setRet(1);
        infoResult.setData(list);
        return infoResult;
    }

    public InfoResult getAdsenseType() {
        InfoResult  infoResult = new InfoResult();
        List<String> list = jettisonCommonMapper.getAdsenseType();
        infoResult.setRet(1);
        infoResult.setData(list);
        return infoResult;
    }

    public InfoResult getAdsensePosition() {
        InfoResult  infoResult = new InfoResult();
        List<String> list = jettisonCommonMapper.getAdsensePosition();
        infoResult.setRet(1);
        infoResult.setData(list);
        return infoResult;
    }

    public InfoResult getStrategys() {
        InfoResult  infoResult = new InfoResult();
        List<Map<String,String>> list = jettisonCommonMapper.getStrategys();
        infoResult.setRet(1);
        infoResult.setData(list);
        return infoResult;
    }

    public InfoResult getAddUserReport(AddUserReportParam param) {
        InfoResult infoResult = new InfoResult();
        PageHelper.startPage(param.getStart(),param.getLimit());
        Page<AddusersReportDTO> reportPage = tfxtMapper.getAddUserReport(param);
        List<AddusersReportDTO> reportList = reportPage.getResult();
        Map<String,Object> map = new HashMap<>();
        if (reportList.size() > 0) {
            AddusersReportDTO addusersReportDTO = tfxtMapper.getAddUserSummary(param);
            map.put("total",addusersReportDTO);
            convertAppAndCountry(reportList, param);
        }
        infoResult.setRet(1);
        infoResult.setMsg("查询成功");
        map.put("list",reportList);
        map.put("totalSize",reportPage.getTotal());
        infoResult.setData(map);
        return infoResult;
    }

    public void exportAddUserReport(AddUserReportParam param, HttpServletResponse response) {
        PageHelper.startPage(PAGE_NUM, MAX_PAGE_SIZE);
        Page<AddusersReportDTO> reportPage = tfxtMapper.getAddUserReport(param);
        List<AddusersReportDTO> reportList = reportPage.getResult();
        if (CollectionUtils.isEmpty(reportList)) {
            return ;
        }
        convertAppAndCountry(reportList,param);
        String value = param.getValue();
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        if (!StringUtils.isNullOrEmpty(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0], s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        ExportExcelUtil.exportXLSX2(response, reportList, headerMap, "新增对比报表_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }

    public InfoResult getMonetizationReport(MonetizationReportParam param) {
        InfoResult infoResult = new InfoResult();
        List<String> group = param.getGroup();
        if (!CollectionUtils.isEmpty(group)) {
            if (group.contains("cha_id")) {
                group.add("cha_sub_launch");
            }
        }
        param.setGroup2(String.join(",", group));


        /** 处理增加产品二级分类条件筛选 */
        if(!CollectionUtils.isEmpty(param.getTwo_app_category())){
            String where = "where channel_id = 10118 and two_app_category in ("+String.join(",",param.getTwo_app_category())+")";
            List<Map<String, Object>> appList = adService.selectAppInfoList(where);
            List<String> collect = appList.stream().map(act -> act.get("id")+"").collect(Collectors.toList());
            param.setTwo_app_category(collect);
        }
        PageHelper.startPage(param.getStart(),param.getLimit());
        Page<MonetizationReportDTO> reportPage = monetizationSummaryMapper.getMonetizationReport(param);
        List<MonetizationReportDTO> reportList = reportPage.getResult();
        Map<String,Object> map = new HashMap<>();
        if (reportList.size() > 0) {
            MonetizationReportDTO monetizationReportDTO = monetizationSummaryMapper.getMonetizationSummary(param);
            map.put("total",monetizationReportDTO);
            convertAppAndCountry(reportList, param);
            addChinaComputeIndex(monetizationReportDTO);
            addChinaComputeIndexList(reportList);
        }


        /** 增加环比数据的处理 -林小敏.20230404 */
        if(group.contains("day")){
            try {
                PageHelper.startPage(1,999999);

                String before = DateTime.parse(param.getStart_date()).minusDays(1).toString("yyyy-MM-dd");
                param.setStart_date(before);

                Page<MonetizationReportDTO> reportPage2 = monetizationSummaryMapper.getMonetizationReport(param);
                List<MonetizationReportDTO> twoList = reportPage2.getResult();
                if (twoList.size() > 0) {
                    addChinaComputeIndexList(twoList);
                }
                Map<String, MonetizationReportDTO> twoMap = new HashMap<>();
                twoList.forEach(act2 -> {
                    twoMap.put(act2.getMapkey(), act2);
                });

                reportList.stream().forEach(act -> {
                    //默认环比增长为0.00%
                    act.setInstalls_rate_match("0.00%");
                    act.setDau_arpu_match("0.00%");

                    String cur_date = (act.getDay());
                    String yesterday = DateTime.parse(cur_date).minusDays(1).toString("yyyy-MM-dd");
                    String key = (act.getMapkey()).replace(cur_date, yesterday);

                    // 设置各类型的环比数值
                    MonetizationReportDTO act2 = twoMap.get(key);
                    if(act2 != null){

                        if(act2.getInstalls_rate() != null && BlankUtils.isNumeric(act2.getInstalls_rate())){
                            act.setInstalls_rate_match(strToTwoNumber(act.getInstalls_rate().toString(),act2.getInstalls_rate().toString()));
                        }
                        if(act2.getDau_arpu() != null && BlankUtils.isNumeric(act2.getDau_arpu().toString())){
                            act.setDau_arpu_match(strToTwoNumber(act.getDau_arpu().toString(),act2.getDau_arpu().toString()));
                        }
                    }
                });

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        /** 新增占比增加%号处理 -林小敏.20230427 */
        reportList.forEach(act -> {
            act.setInstalls_rate((act.getInstalls_rate()==null?"0.00%":(act.getInstalls_rate()+"%")));
        });


        infoResult.setRet(1);
        infoResult.setMsg("查询成功");
        map.put("list",reportList);
        map.put("totalSize",reportPage.getTotal());
        infoResult.setData(map);
        return infoResult;
    }

    public void exportMonetizationReport(MonetizationReportParam param, HttpServletResponse response) {
        PageHelper.startPage(PAGE_NUM, MAX_PAGE_SIZE);

        List<String> group = param.getGroup();
        if (!CollectionUtils.isEmpty(group)) {
            if (group.contains("cha_id")) {
                group.add("cha_sub_launch");
            }
        }
        param.setGroup2(String.join(",", group));

        /** 处理增加产品二级分类条件筛选 */
        if(!CollectionUtils.isEmpty(param.getTwo_app_category())){
            String where = "where channel_id = 10118 and two_app_category in ("+String.join(",",param.getTwo_app_category())+")";
            List<Map<String, Object>> appList = adService.selectAppInfoList(where);
            List<String> collect = appList.stream().map(act -> act.get("id")+"").collect(Collectors.toList());
            param.setTwo_app_category(collect);
        }

        Page<MonetizationReportDTO> reportPage = monetizationSummaryMapper.getMonetizationReport(param);
        List<MonetizationReportDTO> reportList = reportPage.getResult();
        if (CollectionUtils.isEmpty(reportList)) {
            return ;
        }
        convertAppAndCountry(reportList,param);
        addChinaComputeIndexList(reportList);


        /** 增加环比数据的处理 -林小敏.20230404 */
        if(group.contains("day")){
            try {
                PageHelper.startPage(1,999999);

                String before = DateTime.parse(param.getStart_date()).minusDays(1).toString("yyyy-MM-dd");
                param.setStart_date(before);

                Page<MonetizationReportDTO> reportPage2 = monetizationSummaryMapper.getMonetizationReport(param);
                List<MonetizationReportDTO> twoList = reportPage2.getResult();
                if (twoList.size() > 0) {
                    addChinaComputeIndexList(twoList);
                }
                Map<String, MonetizationReportDTO> twoMap = new HashMap<>();
                twoList.forEach(act2 -> {
                    twoMap.put(act2.getMapkey(), act2);
                });

                reportList.stream().forEach(act -> {
                    //默认环比增长为0.00%
                    act.setInstalls_rate_match("0.00%");
                    act.setDau_arpu_match("0.00%");

                    String cur_date = (act.getDay());
                    String yesterday = DateTime.parse(cur_date).minusDays(1).toString("yyyy-MM-dd");
                    String key = (act.getMapkey()).replace(cur_date, yesterday);

                    // 设置各类型的环比数值
                    MonetizationReportDTO act2 = twoMap.get(key);
                    if(act2 != null){

                        if(act2.getInstalls_rate() != null && BlankUtils.isNumeric(act2.getInstalls_rate())){
                            act.setInstalls_rate_match(strToTwoNumber(act.getInstalls_rate().toString(),act2.getInstalls_rate().toString()));
                        }
                        if(act2.getDau_arpu() != null && BlankUtils.isNumeric(act2.getDau_arpu().toString())){
                            act.setDau_arpu_match(strToTwoNumber(act.getDau_arpu().toString(),act2.getDau_arpu().toString()));
                        }
                    }
                });

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        /** 新增占比增加%号处理 -林小敏.20230427 */
        reportList.forEach(act -> {
            act.setInstalls_rate((act.getInstalls_rate()==null?"0.00%":(act.getInstalls_rate()+"%")));
        });

        String value = param.getValue();
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        if (!StringUtils.isNullOrEmpty(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0], s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        ExportExcelUtil.exportXLSX2(response, reportList, headerMap, "变现汇总表_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }

    public List<MonetizationReportDTO> commonMonetizationReport(MonetizationReportParam param){
        PageHelper.startPage(PAGE_NUM, MAX_PAGE_SIZE);

        List<String> group = param.getGroup();
        if (!CollectionUtils.isEmpty(group)) {
            if (group.contains("cha_id")) {
                group.add("cha_sub_launch");
            }
        }
        param.setGroup2(String.join(",", group));

        /** 处理增加产品二级分类条件筛选 */
        if(!CollectionUtils.isEmpty(param.getTwo_app_category())){
            String where = "where channel_id = 10118 and two_app_category in ("+String.join(",",param.getTwo_app_category())+")";
            List<Map<String, Object>> appList = adService.selectAppInfoList(where);
            List<String> collect = appList.stream().map(act -> act.get("id")+"").collect(Collectors.toList());
            param.setTwo_app_category(collect);
        }

        Page<MonetizationReportDTO> reportPage = monetizationSummaryMapper.getMonetizationReport(param);
        List<MonetizationReportDTO> reportList = reportPage.getResult();
        if (CollectionUtils.isEmpty(reportList)) {
            return reportList;
        }
        convertAppAndCountry(reportList,param);
        addChinaComputeIndexList(reportList);


        /** 增加环比数据的处理 -林小敏.20230404 */
        if(group.contains("day")){
            try {
                PageHelper.startPage(1,999999);

                String before = DateTime.parse(param.getStart_date()).minusDays(1).toString("yyyy-MM-dd");
                param.setStart_date(before);

                Page<MonetizationReportDTO> reportPage2 = monetizationSummaryMapper.getMonetizationReport(param);
                List<MonetizationReportDTO> twoList = reportPage2.getResult();
                if (twoList.size() > 0) {
                    addChinaComputeIndexList(twoList);
                }
                Map<String, MonetizationReportDTO> twoMap = new HashMap<>();
                twoList.forEach(act2 -> {
                    twoMap.put(act2.getMapkey(), act2);
                });

                reportList.stream().forEach(act -> {
                    //默认环比增长为0.00%
                    act.setInstalls_rate_match("0.00%");
                    act.setDau_arpu_match("0.00%");

                    String cur_date = (act.getDay());
                    String yesterday = DateTime.parse(cur_date).minusDays(1).toString("yyyy-MM-dd");
                    String key = (act.getMapkey()).replace(cur_date, yesterday);

                    // 设置各类型的环比数值
                    MonetizationReportDTO act2 = twoMap.get(key);
                    if(act2 != null){

                        if(act2.getInstalls_rate() != null && BlankUtils.isNumeric(act2.getInstalls_rate())){
                            act.setInstalls_rate_match(strToTwoNumber(act.getInstalls_rate().toString(),act2.getInstalls_rate().toString()));
                        }
                        if(act2.getDau_arpu() != null && BlankUtils.isNumeric(act2.getDau_arpu().toString())){
                            act.setDau_arpu_match(strToTwoNumber(act.getDau_arpu().toString(),act2.getDau_arpu().toString()));
                        }
                    }
                });

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        /** 新增占比增加%号处理 -林小敏.20230427 */
        reportList.forEach(act -> {
            act.setInstalls_rate((act.getInstalls_rate()==null?"0.00%":(act.getInstalls_rate()+"%")));
        });
        return reportList;
    }


    private List<MonetizationReportDTO> addChinaComputeIndexList(List<MonetizationReportDTO> chinaMonetizationReportList) {
        if (CollectionUtils.isEmpty(chinaMonetizationReportList)) {
            return new ArrayList<>();
        }

        chinaMonetizationReportList.removeAll(Collections.singleton(null));

        for (int i = 0; i < chinaMonetizationReportList.size(); i++) {
            MonetizationReportDTO monetizationReport = chinaMonetizationReportList.get(i);

            double installs_rate = getInstallsRate(monetizationReport.getInstalls(), monetizationReport.getDau());
            double dau_arpu = getDauArpu(monetizationReport.getRevenue(), monetizationReport.getDau());
            double installs_arpu = getInstallsArpu(monetizationReport.getRevenue(), monetizationReport.getInstalls());

            String installs_rate_str = DoubleUtils.getDouble(installs_rate * 100) + "";
            monetizationReport.setInstalls_rate(installs_rate_str);
            monetizationReport.setDau_arpu(dau_arpu);
            monetizationReport.setInstalls_arpu(installs_arpu);

            double pv_per_video = getPvPer(monetizationReport.getPv_video(), monetizationReport.getDau());
            double pv_per_plaque = getPvPer(monetizationReport.getPv_plaque(), monetizationReport.getDau());
            double pv_per_banner = getPvPer(monetizationReport.getPv_banner(), monetizationReport.getDau());
            double pv_per_splash = getPvPer(monetizationReport.getPv_splash(), monetizationReport.getDau());
            double pv_per_msg = getPvPer(monetizationReport.getPv_msg(), monetizationReport.getDau());

            monetizationReport.setPv_per_video(pv_per_video);
            monetizationReport.setPv_per_plaque(pv_per_plaque);
            monetizationReport.setPv_per_banner(pv_per_banner);
            monetizationReport.setPv_per_splash(pv_per_splash);
            monetizationReport.setPv_per_msg(pv_per_msg);

            double ecpm_video = getEcpm(monetizationReport.getPv_video(), monetizationReport.getRevenue_video());
            double ecpm_plaque = getEcpm(monetizationReport.getPv_plaque(), monetizationReport.getRevenue_plaque());
            double ecpm_banner = getEcpm(monetizationReport.getPv_banner(), monetizationReport.getRevenue_banner());
            double ecpm_splash = getEcpm(monetizationReport.getPv_splash(), monetizationReport.getRevenue_splash());
            double ecpm_msg = getEcpm(monetizationReport.getPv_msg(), monetizationReport.getRevenue_msg());

            monetizationReport.setEcpm_video(ecpm_video);
            monetizationReport.setEcpm_plaque(ecpm_plaque);
            monetizationReport.setEcpm_banner(ecpm_banner);
            monetizationReport.setEcpm_splash(ecpm_splash);
            monetizationReport.setEcpm_msg(ecpm_msg);



            double dau_arpu_video = getDauArpu(monetizationReport.getRevenue_video(), monetizationReport.getDau());
            double dau_arpu_plaque = getDauArpu(monetizationReport.getRevenue_plaque(), monetizationReport.getDau());
            double dau_arpu_banner = getDauArpu(monetizationReport.getRevenue_banner(), monetizationReport.getDau());
            double dau_arpu_splash = getDauArpu(monetizationReport.getRevenue_splash(), monetizationReport.getDau());
            double dau_arpu_msg = getDauArpu(monetizationReport.getRevenue_msg(), monetizationReport.getDau());

            monetizationReport.setDau_arpu_video(dau_arpu_video);
            monetizationReport.setDau_arpu_plaque(dau_arpu_plaque);
            monetizationReport.setDau_arpu_banner(dau_arpu_banner);
            monetizationReport.setDau_arpu_splash(dau_arpu_splash);
            monetizationReport.setDau_arpu_msg(dau_arpu_msg);

            double out_installs_rate = getInstallsRate(monetizationReport.getOut_installs(), monetizationReport.getOut_dau());
            double out_dau_arpu = getDauArpu(monetizationReport.getOut_revenue(), monetizationReport.getOut_dau());
            double out_installs_arpu = getInstallsArpu(monetizationReport.getOut_revenue(), monetizationReport.getOut_installs());

            String out_installs_rate_str = DoubleUtils.getDouble(out_installs_rate * 100) + "";
            monetizationReport.setOut_installs_rate(out_installs_rate_str);
            monetizationReport.setOut_dau_arpu(out_dau_arpu);
            monetizationReport.setOut_installs_arpu(out_installs_arpu);

            long out_pv_total = monetizationReport.getOut_pv_video() + monetizationReport.getOut_pv_plaque()
                    + monetizationReport.getOut_pv_banner() + monetizationReport.getOut_pv_splash() + monetizationReport.getOut_pv_msg();
            monetizationReport.setOut_pv_total(out_pv_total);

            double out_pv_per_video = getPvPer(monetizationReport.getOut_pv_video(), monetizationReport.getOut_dau());
            double out_pv_per_plaque = getPvPer(monetizationReport.getOut_pv_plaque(), monetizationReport.getOut_dau());
            double out_pv_per_banner = getPvPer(monetizationReport.getOut_pv_banner(), monetizationReport.getOut_dau());
            double out_pv_per_splash = getPvPer(monetizationReport.getOut_pv_splash(), monetizationReport.getOut_dau());
            double out_pv_per_msg = getPvPer(monetizationReport.getOut_pv_msg(), monetizationReport.getOut_dau());
            double out_pv_per_total = getPvPer(out_pv_total, monetizationReport.getOut_dau());

            monetizationReport.setOut_pv_per_video(out_pv_per_video);
            monetizationReport.setOut_pv_per_plaque(out_pv_per_plaque);
            monetizationReport.setOut_pv_per_banner(out_pv_per_banner);
            monetizationReport.setOut_pv_per_splash(out_pv_per_splash);
            monetizationReport.setOut_pv_per_msg(out_pv_per_msg);
            monetizationReport.setOut_pv_per_total(out_pv_per_total);

            double out_ecpm_video = getEcpm(monetizationReport.getOut_pv_video(), monetizationReport.getOut_revenue_video());
            double out_ecpm_plaque = getEcpm(monetizationReport.getOut_pv_plaque(), monetizationReport.getOut_revenue_plaque());
            double out_ecpm_banner = getEcpm(monetizationReport.getOut_pv_banner(), monetizationReport.getOut_revenue_banner());
            double out_ecpm_splash = getEcpm(monetizationReport.getOut_pv_splash(), monetizationReport.getOut_revenue_splash());
            double out_ecpm_msg = getEcpm(monetizationReport.getOut_pv_msg(), monetizationReport.getOut_revenue_msg());
            double out_ecpm_total = getEcpm((long) monetizationReport.getOut_pv_total(), monetizationReport.getOut_revenue());

            monetizationReport.setOut_ecpm_video(out_ecpm_video);
            monetizationReport.setOut_ecpm_plaque(out_ecpm_plaque);
            monetizationReport.setOut_ecpm_banner(out_ecpm_banner);
            monetizationReport.setOut_ecpm_splash(out_ecpm_splash);
            monetizationReport.setOut_ecpm_msg(out_ecpm_msg);
            monetizationReport.setOut_ecpm_total(out_ecpm_total);


            double out_dau_arpu_video = getDauArpu(monetizationReport.getOut_revenue_video(), monetizationReport.getOut_dau());
            double out_dau_arpu_plaque = getDauArpu(monetizationReport.getOut_revenue_plaque(), monetizationReport.getOut_dau());
            double out_dau_arpu_banner = getDauArpu(monetizationReport.getOut_revenue_banner(), monetizationReport.getOut_dau());
            double out_dau_arpu_splash = getDauArpu(monetizationReport.getOut_revenue_splash(), monetizationReport.getOut_dau());
            double out_dau_arpu_msg = getDauArpu(monetizationReport.getOut_revenue_msg(), monetizationReport.getOut_dau());

            monetizationReport.setOut_dau_arpu_video(out_dau_arpu_video);
            monetizationReport.setOut_dau_arpu_plaque(out_dau_arpu_plaque);
            monetizationReport.setOut_dau_arpu_banner(out_dau_arpu_banner);
            monetizationReport.setOut_dau_arpu_splash(out_dau_arpu_splash);
            monetizationReport.setOut_dau_arpu_msg(out_dau_arpu_msg);

        }

        return chinaMonetizationReportList;
    }

    private MonetizationReportDTO addChinaComputeIndex(MonetizationReportDTO monetizationReport) {


        double installs_rate = getInstallsRate(monetizationReport.getInstalls(), monetizationReport.getDau());
        double dau_arpu = getDauArpu(monetizationReport.getRevenue(), monetizationReport.getDau());
        double installs_arpu = getInstallsArpu(monetizationReport.getRevenue(), monetizationReport.getInstalls());

        String installs_rate_str = DoubleUtils.getDouble(installs_rate * 100) + "%";
        monetizationReport.setInstalls_rate(installs_rate_str);
        monetizationReport.setDau_arpu(dau_arpu);
        monetizationReport.setInstalls_arpu(installs_arpu);

        double pv_per_video = getPvPer(monetizationReport.getPv_video(), monetizationReport.getDau());
        double pv_per_plaque = getPvPer(monetizationReport.getPv_plaque(), monetizationReport.getDau());
        double pv_per_banner = getPvPer(monetizationReport.getPv_banner(), monetizationReport.getDau());
        double pv_per_splash = getPvPer(monetizationReport.getPv_splash(), monetizationReport.getDau());
        double pv_per_msg = getPvPer(monetizationReport.getPv_msg(), monetizationReport.getDau());

        monetizationReport.setPv_per_video(pv_per_video);
        monetizationReport.setPv_per_plaque(pv_per_plaque);
        monetizationReport.setPv_per_banner(pv_per_banner);
        monetizationReport.setPv_per_splash(pv_per_splash);
        monetizationReport.setPv_per_msg(pv_per_msg);

        double ecpm_video = getEcpm(monetizationReport.getPv_video(), monetizationReport.getRevenue_video());
        double ecpm_plaque = getEcpm(monetizationReport.getPv_plaque(), monetizationReport.getRevenue_plaque());
        double ecpm_banner = getEcpm(monetizationReport.getPv_banner(), monetizationReport.getRevenue_banner());
        double ecpm_splash = getEcpm(monetizationReport.getPv_splash(), monetizationReport.getRevenue_splash());
        double ecpm_msg = getEcpm(monetizationReport.getPv_msg(), monetizationReport.getRevenue_msg());

        monetizationReport.setEcpm_video(ecpm_video);
        monetizationReport.setEcpm_plaque(ecpm_plaque);
        monetizationReport.setEcpm_banner(ecpm_banner);
        monetizationReport.setEcpm_splash(ecpm_splash);
        monetizationReport.setEcpm_msg(ecpm_msg);



        double dau_arpu_video = getDauArpu(monetizationReport.getRevenue_video(), monetizationReport.getDau());
        double dau_arpu_plaque = getDauArpu(monetizationReport.getRevenue_plaque(), monetizationReport.getDau());
        double dau_arpu_banner = getDauArpu(monetizationReport.getRevenue_banner(), monetizationReport.getDau());
        double dau_arpu_splash = getDauArpu(monetizationReport.getRevenue_splash(), monetizationReport.getDau());
        double dau_arpu_msg = getDauArpu(monetizationReport.getRevenue_msg(), monetizationReport.getDau());

        monetizationReport.setDau_arpu_video(dau_arpu_video);
        monetizationReport.setDau_arpu_plaque(dau_arpu_plaque);
        monetizationReport.setDau_arpu_banner(dau_arpu_banner);
        monetizationReport.setDau_arpu_splash(dau_arpu_splash);
        monetizationReport.setDau_arpu_msg(dau_arpu_msg);


        double out_installs_rate = getInstallsRate(monetizationReport.getOut_installs(), monetizationReport.getOut_dau());
        double out_dau_arpu = getDauArpu(monetizationReport.getOut_revenue(), monetizationReport.getOut_dau());
        double out_installs_arpu = getInstallsArpu(monetizationReport.getOut_revenue(), monetizationReport.getOut_installs());

        String out_installs_rate_str = DoubleUtils.getDouble(out_installs_rate * 100) + "";
        monetizationReport.setOut_installs_rate(out_installs_rate_str);
        monetizationReport.setOut_dau_arpu(out_dau_arpu);
        monetizationReport.setOut_installs_arpu(out_installs_arpu);

        double out_pv_per_video = getPvPer(monetizationReport.getOut_pv_video(), monetizationReport.getOut_dau());
        double out_pv_per_plaque = getPvPer(monetizationReport.getOut_pv_plaque(), monetizationReport.getOut_dau());
        double out_pv_per_banner = getPvPer(monetizationReport.getOut_pv_banner(), monetizationReport.getOut_dau());
        double out_pv_per_splash = getPvPer(monetizationReport.getOut_pv_splash(), monetizationReport.getOut_dau());
        double out_pv_per_msg = getPvPer(monetizationReport.getOut_pv_msg(), monetizationReport.getOut_dau());

        monetizationReport.setOut_pv_per_video(out_pv_per_video);
        monetizationReport.setOut_pv_per_plaque(out_pv_per_plaque);
        monetizationReport.setOut_pv_per_banner(out_pv_per_banner);
        monetizationReport.setOut_pv_per_splash(out_pv_per_splash);
        monetizationReport.setOut_pv_per_msg(out_pv_per_msg);

        double out_ecpm_video = getEcpm(monetizationReport.getOut_pv_video(), monetizationReport.getOut_revenue_video());
        double out_ecpm_plaque = getEcpm(monetizationReport.getOut_pv_plaque(), monetizationReport.getOut_revenue_plaque());
        double out_ecpm_banner = getEcpm(monetizationReport.getOut_pv_banner(), monetizationReport.getOut_revenue_banner());
        double out_ecpm_splash = getEcpm(monetizationReport.getOut_pv_splash(), monetizationReport.getOut_revenue_splash());
        double out_ecpm_msg = getEcpm(monetizationReport.getOut_pv_msg(), monetizationReport.getOut_revenue_msg());

        monetizationReport.setOut_ecpm_video(out_ecpm_video);
        monetizationReport.setOut_ecpm_plaque(out_ecpm_plaque);
        monetizationReport.setOut_ecpm_banner(out_ecpm_banner);
        monetizationReport.setOut_ecpm_splash(out_ecpm_splash);
        monetizationReport.setOut_ecpm_msg(out_ecpm_msg);

        double out_dau_arpu_video = getDauArpu(monetizationReport.getOut_revenue_video(), monetizationReport.getOut_dau());
        double out_dau_arpu_plaque = getDauArpu(monetizationReport.getOut_revenue_plaque(), monetizationReport.getOut_dau());
        double out_dau_arpu_banner = getDauArpu(monetizationReport.getOut_revenue_banner(), monetizationReport.getOut_dau());
        double out_dau_arpu_splash = getDauArpu(monetizationReport.getOut_revenue_splash(), monetizationReport.getOut_dau());
        double out_dau_arpu_msg = getDauArpu(monetizationReport.getOut_revenue_msg(), monetizationReport.getOut_dau());

        monetizationReport.setOut_dau_arpu_video(out_dau_arpu_video);
        monetizationReport.setOut_dau_arpu_plaque(out_dau_arpu_plaque);
        monetizationReport.setOut_dau_arpu_banner(out_dau_arpu_banner);
        monetizationReport.setOut_dau_arpu_splash(out_dau_arpu_splash);
        monetizationReport.setOut_dau_arpu_msg(out_dau_arpu_msg);

        return monetizationReport;
    }

    private double getInstallsRate(Long installs, Double dau) {
        double installs_rate = 0;
        if (null != installs
                && null != dau
                && installs > 0
                && dau > 0) {
            installs_rate = DoubleUtils.formatDouble(DoubleUtils.getDoubleByfive(installs / dau));
        }
        return installs_rate;
    }

    private double getInstallsArpu(Double revenue, Long installs) {
        double installs_arpu = 0;
        if (null != revenue
                && null != installs
                && revenue > 0
                && installs > 0) {
            installs_arpu = DoubleUtils.formatDouble(DoubleUtils.getDoubleByThree(revenue / installs));
        }
        return installs_arpu;
    }

    private double getPvPer(Long imperssions, Double dau) {
        double pv_per = 0;
        if (null != imperssions
                && null != dau
                && imperssions > 0
                && dau > 0) {
            pv_per = DoubleUtils.formatDouble(DoubleUtils.getDouble(imperssions / dau));
        }
        return pv_per;
    }

    private double getEcpm(Long imperssions, Double revenue) {
        double ecpm = 0;
        if (null != imperssions
                && null != revenue
                && imperssions > 0
                && revenue > 0) {
            ecpm = DoubleUtils.formatDouble(DoubleUtils.getDouble(revenue / imperssions * 1000));
        }
        return ecpm;
    }

    private double getDauArpu(Double revenue, Double dau) {
        double dau_arpu = 0;
        if (null != revenue
                && null != dau
                && revenue > 0
                && dau > 0) {
            dau_arpu = DoubleUtils.formatDouble(DoubleUtils.getDoubleByThree(revenue / dau));
        }
        return dau_arpu;
    }

    /**
     *
     * @param data1 当前日期数据
     * @param data2 前一天数据
     * @return
     */
    private String strToTwoNumber(String data1,String data2){
        String rate ="0.00%";
        try {
            BigDecimal brate = new BigDecimal(data1)
                    .subtract(new BigDecimal(data2))
                    .divide(new BigDecimal(data2),4,RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2,RoundingMode.HALF_UP);
            rate = brate.toString()+"%";
        }catch (Exception e){
        }
        return rate;
    }

    /**
     * 查询官包数据报表列表
     * @param param
     * @return
     */
    public List<GuanBaoReport> selectGuanBaoReport(GuanBaoParam param) {
        String group = param.getGroup();
        List<GuanBaoReport> list ;
        if (group.contains("transfer_type") || group.contains("account") || group.contains("putUser")) {
            list = dnwxAdtMapper.selectGuanBaoReport1(param);
        }else if (group.contains("channel")) {
            list = dnwxAdtMapper.selectGuanBaoReport2(param);
        }else {
            list = dnwxAdtMapper.selectGuanBaoReport3(param);
        }
        if (!param.getGroup().contains("day") && list.size() > 0) {
            String day = param.getStart_date()+"至"+param.getEnd_date();
            for (GuanBaoReport g : list) {
                g.setDay(day);
            }
        }
        return list;
    }

    /**
     * 查询官包数据报表汇总
     * @param param
     * @return
     */
    public GuanBaoReport countGuanBaoReport(GuanBaoParam param) {
        String group = param.getGroup();
        if (group.contains("transfer_type") || group.contains("account") || group.contains("putUser")) {
            return dnwxAdtMapper.countGuanBaoReport1(param);
        }else if (group.contains("channel")) {
            return dnwxAdtMapper.countGuanBaoReport2(param);
        }else {
            return dnwxAdtMapper.countGuanBaoReport3(param);
        }
    }

    /**
     * 获取创意形式
     * @return
     */
    public InfoResult getCreativeTemplate() {
        InfoResult  infoResult = new InfoResult();
        List<Map<String, Object>> list = tfxtMapper.selectCreativeTemplate();
        infoResult.setRet(1);
        infoResult.setData(list);
        return infoResult;
    }

    /**
     * 获取包名数据源
     * @return
     */
    public List<String> getPkgName() {
        List<String> pkgNameList = jettisonCommonMapper.getPkgName();
        return pkgNameList;
    }

    /**
     * 获取包名数据源
     * @return
     */
    public List<String> getShopIds() {
        List<String> shopIdList = jettisonCommonMapper.getShopId();
        return shopIdList;
    }

    public InfoResult getChannelAccountSpendReport(SpendReportParam param, String username) {
        InfoResult infoResult = new InfoResult();
        Map<String,Object> map = new HashMap<>();

        Page<FinanceReport> reportPage = tfxtMapper.getChannelAccountSpendReport(param);
        List<FinanceReport> reportList = reportPage.getResult();
        //增加账号备注
        Map<String, String> remarkMap = new HashMap<>();
        if (param.getGroup().contains("account")) {
            List<HashMap> allAccountList = tfxtMapper.getChinaPlatformAccount();
            remarkMap = allAccountList.stream()
                    .collect(Collectors.toMap(x->StringUtil.getString(x.get("account")),x->StringUtil.getString(x.get("remark")),(k1,k2)->k2));
            for (FinanceReport a : reportList) {
                a.setAccountRemark(remarkMap.get(a.getAccount()));
            }
        }

        if(param.getGroup().contains("type")) {
            for (FinanceReport report : reportList) {
                String type = report.getType();
                String str = "";
                if (null != type && type != "") {
                    if (StringUtil.getInt(type) == 1) {
                        str = "自运营-分包";
                    } else if (StringUtil.getInt(type) == 2) {
                        str = "代运营-分包";
                    } else if (StringUtil.getInt(type) == 3) {
                        str = "自运营-直投";
                    } else if (StringUtil.getInt(type) == 4) {
                        str = "代运营-直投";
                    } else if (StringUtil.getInt(type) == 5) {
                        str = "自运营-OPPO直投";
                    } else if (StringUtil.getInt(type) == 6) {
                        str = "自运营-VIVO直投";
                    } else if (StringUtil.getInt(type) == 7) {
                        str = "自运营-华为直投";
                    } else if (StringUtil.getInt(type) == 8) {
                        str = "自运营-小米直投";
                    } else if (StringUtil.getInt(type) == 9) {
                        str = "代运营-OPPO直投";
                    } else if (StringUtil.getInt(type) == 10) {
                        str = "代运营-VIVO直投";
                    } else if (StringUtil.getInt(type) == 11) {
                        str = "代运营-华为直投";
                    } else if (StringUtil.getInt(type) == 12) {
                        str = "代运营-小米直投";
                    } else if (StringUtil.getInt(type) == 13) {
                        str = "自运营-OPPOMJ";
                    } else if (StringUtil.getInt(type) == 14) {
                        str = "代运营-OPPOMJ";
                    } else if (StringUtil.getInt(type) == 15) {
                        str = "自运营-小米直投2";
                    }
                }
                report.setType(str);
            }
        }

        if (reportList.size() > 0) {
            FinanceReport spendReportDTO = tfxtMapper.getChannelAccountSpendReportSum(param);
            infoResult.setRet(1);
            map.put("total",spendReportDTO);
//            convertAppAndCountry(reportList,param);
        }
        infoResult.setRet(1);
        infoResult.setMsg("查询成功");
        map.put("list",reportList);
        map.put("totalSize",reportPage.getTotal());
        infoResult.setData(map);
        return infoResult;
    }

    public Map<String,Object> getAppleAdsName(String campaign, String adgroup, String keyward,String account) {
        List<Map<String,Object>> list = appleRoiReportMapper.selectAppleAdsName(campaign,adgroup,keyward,account);
        Map<String,Object> data = new HashMap<>();
        Map<String, String> campaignMap = new HashMap<>();
        Map<String, String> adgroupMap = new HashMap<>();
        Map<String, String> keywordMap = new HashMap<>();
        List<String> accountList = new ArrayList<>();
        if (list != null && list.size() > 0) {
            campaignMap = list.stream()
                    .collect(Collectors.toMap(
                            map -> String.valueOf(map.get("campaignId")),map -> String.valueOf(map.get("campaignName")), (k1, k2) -> k1));
            adgroupMap = list.stream()
                    .collect(Collectors.toMap(
                            map -> String.valueOf(map.get("adGroupId")),map -> String.valueOf(map.get("adGroupName")), (k1, k2) -> k1));
            keywordMap = list.stream()
                    .collect(Collectors.toMap(
                            map -> String.valueOf(map.get("keywordId")),map -> String.valueOf(map.get("keyword")), (k1, k2) -> k1));
            accountList = list.stream().map(map -> (String) map.get("account")).distinct().collect(Collectors.toList());
        }
        data.put("campaign",campaignMap);
        data.put("adgroup",adgroupMap);
        data.put("keyword",keywordMap);
        data.put("account",accountList);
        return data;
    }

}
