package com.wbgame.service.budgetWarning;

import BI.IF;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.ReturnJson;
import com.wbgame.common.strategyEnum.Dimension;
import com.wbgame.common.strategyEnum.Platform;
import com.wbgame.mapper.tfxt.BudgetWarningConfigMapper;
import com.wbgame.pojo.budgetWarning.BudgetWarningConfig;
import com.wbgame.pojo.jettison.param.BudgetWarningConfigVo;
import com.wbgame.pojo.jettison.param.BudgetWarningStrategyVo;
import com.wbgame.pojo.jettison.vo.BudgetWarningIndexVo;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/30
 * @description
 **/
@Service
public class BudgetWarningConfigService {

    @Resource
    private BudgetWarningConfigMapper budgetWarningConfigMapper;

    private static final Logger logger = LoggerFactory.getLogger(BudgetWarningConfigService.class);

    public List<BudgetWarningConfig> findAllWarningConfigNow(String media) {
        int nowHour = DateUtil.getNowHour();
        return findAllWarningConfig(media, nowHour);
    }

    /**
     * 找寻 指定媒体 下的所有告警配置项
     * @param media
     * @param hour
     * @return
     */
    public List<BudgetWarningConfig> findAllWarningConfig(String media, int hour) {
        return budgetWarningConfigMapper.selectBudgetWarningConfigByHour(media, hour);
    }


    public List<Map<String, Object>> selectAllStrategies(){
        return budgetWarningConfigMapper.selectAllStrategies();
    }

    /**
     * 获取告警指标数据列表-全部
     * @return
     */
    public List<BudgetWarningIndexVo> getBudgetWarningIndex() {
        return budgetWarningConfigMapper.getBudgetWarningIndex(null);
    }

    /**
     * 获取告警指标数据列表-带表名
     * @return
     */
    public List<BudgetWarningIndexVo> getBudgetWarningIndex(String table) {
        return budgetWarningConfigMapper.getBudgetWarningIndex(table);
    }

    /**
     * 查询告警指标策略配置
     * @param param
     * @return
     */
    public List<BudgetWarningStrategyVo> selectBudgetWarningStrategy(BudgetWarningStrategyVo param) {
        List<BudgetWarningStrategyVo> list =  budgetWarningConfigMapper.selectBudgetWarningStrategy(param);
        //指标策略视图转化
        List<BudgetWarningIndexVo> budgetWarningIndex = getBudgetWarningIndex();
        Map<String,String> indexMap ;
        indexMap = budgetWarningIndex.stream()
                .collect(Collectors.toMap(x -> x.getId(), x -> x.getIndex_name(), (k1, k2) -> k2));
        for (BudgetWarningStrategyVo b : list) {
            b.setStrategy_vo(getStrategyView(b.getStrategy(),indexMap));
        }
        return list;
    }

    /**
     * 转化指标策略视图
     * @param strategy
     * @return
     */
    public String getStrategyView(String strategy,Map<String,String> indexMap) {
        //指标策略视图
        JSONObject jsonObject = JSONObject.parseObject(strategy);
        return getStrategyView(jsonObject, indexMap);
    }

    /**
     * see {@link BudgetWarningConfigService#getStrategyView(String, Map)}
     * @param jsonObject
     * @param indexMap
     * @return
     */
    public String getStrategyView(JSONObject jsonObject,Map<String,String> indexMap) {
        //指标策略视图
        String strategy_vo = "";
        try {
            String relation = "and".equals(jsonObject.getString("relation")) ? "且" : "或";
            JSONArray strategies = jsonObject.getJSONArray("strategies");
            for (JSONObject j : strategies.toJavaList(JSONObject.class)) {
                //获取指标名
                String field = indexMap.get(j.getString("field"));
                String index = "";
                switch (j.getString("unity")) {
                    case "%": index = field + j.getString("compare") + j.getString("value")+"%";break;
                    case "%%": index = field + j.getString("compare") + "预算的" + j.getString("value")+"%";break;
                    default: index = field + j.getString("compare") + j.getString("value");break;
                }
                strategy_vo = strategy_vo + index + " " + relation + " ";
            }
            if (!BlankUtils.checkBlank(strategy_vo)) {
                strategy_vo = strategy_vo.substring(0,strategy_vo.length() - 3);
            }
        }catch (Exception e) {
            logger.error("告警指标策略配置:解析指标json失败",e);
        }
        return strategy_vo;
    }

    /**
     * 新增告警指标策略配置
     * @param param
     * @return
     */
    public String addBudgetWarningStrategy(BudgetWarningStrategyVo param) {
        if (BlankUtils.checkBlank(param.getName())
                || BlankUtils.checkBlank(param.getMedia())
                || BlankUtils.checkBlank(param.getStrategy())) {
            return ReturnJson.toErrorJson("必填参数不能为空");
        }
        //判断是否已创建过策略名
        BudgetWarningStrategyVo build = new BudgetWarningStrategyVo();
        build.setMedia(param.getMedia());
        build.setExist_name(param.getName());
        List<BudgetWarningStrategyVo> list =  budgetWarningConfigMapper.selectBudgetWarningStrategy(build);
        if (list != null && list.size() > 0) {
            return ReturnJson.toErrorJson("该媒体的策略名已存在");
        }
        //验证指标json是否正确
        String strategy = param.getStrategy();
        try {
            JSONObject jsonObject = JSONObject.parseObject(strategy);
            if (BlankUtils.checkBlank(jsonObject.getString("relation"))) {
                return ReturnJson.toErrorJson("未发现指标或且关系");
            }
            JSONArray strategies = jsonObject.getJSONArray("strategies");
            if (strategies == null) {
                return ReturnJson.toErrorJson("未找到指标集合");
            }
            List<JSONObject> strategyList = strategies.toJavaList(JSONObject.class);
            //验证重复指标集合
            List<String> ids = new ArrayList<>();
            //验证指标参数是否正确
            for (JSONObject j : strategyList) {
                if (BlankUtils.checkBlank(j.getString("field"))
                        || BlankUtils.checkBlank(j.getString("compare"))
                        || !BlankUtils.isNumeric(j.getString("value"))) {
                    return ReturnJson.toErrorJson("指标参数不能为空或指标值不为数字");
                }
                if (ids.contains(j.getString("field"))) {
                    return ReturnJson.toErrorJson("同一策略指标不可重复选择");
                }
                ids.add(j.getString("field"));
            }
        }catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("指标数据格式错误,请联系开发人员");
        }
        budgetWarningConfigMapper.addBudgetWarningStrategy(param);
        return ReturnJson.success("新增成功");
    }

    /**
     * 更新告警指标策略配置
     * @param param
     * @return
     */
    public String updateBudgetWarningStrategy(BudgetWarningStrategyVo param) {
        if (BlankUtils.checkBlank(param.getName())
                || BlankUtils.checkBlank(param.getMedia())
                || BlankUtils.checkBlank(param.getStrategy())) {
            return ReturnJson.toErrorJson("必填参数不能为空");
        }
        //判断该策略是否存在
        BudgetWarningStrategyVo build = new BudgetWarningStrategyVo();
        build.setId(param.getId());
        List<BudgetWarningStrategyVo> list =  budgetWarningConfigMapper.selectBudgetWarningStrategy(build);
        if (list == null || list.size() == 0) {
            return ReturnJson.toErrorJson("该策略不存在");
        }
        BudgetWarningStrategyVo old = list.get(0);
        if (!param.getUpdate_time().equals(old.getUpdate_time())) {
            return ReturnJson.toErrorJson("该策略已更新,请刷新页面后重试");
        }
        //验证指标json是否正确
        String strategy = param.getStrategy();
        try {
            JSONObject jsonObject = JSONObject.parseObject(strategy);
            if (BlankUtils.checkBlank(jsonObject.getString("relation"))) {
                return ReturnJson.toErrorJson("未发现指标或且关系");
            }
            JSONArray strategies = jsonObject.getJSONArray("strategies");
            if (strategies == null) {
                return ReturnJson.toErrorJson("未找到指标集合");
            }
            List<JSONObject> strategyList = strategies.toJavaList(JSONObject.class);
            //验证重复指标集合
            List<String> ids = new ArrayList<>();
            //验证指标参数是否正确
            for (JSONObject j : strategyList) {
                if (BlankUtils.checkBlank(j.getString("field"))
                        || BlankUtils.checkBlank(j.getString("compare"))
                        || !BlankUtils.isNumeric(j.getString("value"))) {
                    return ReturnJson.toErrorJson("指标参数不能为空或指标值不为数字");
                }
                if (ids.contains(j.getString("field"))) {
                    return ReturnJson.toErrorJson("同一策略指标不可重复选择");
                }
                ids.add(j.getString("field"));
            }
        }catch (Exception e) {
            logger.error("指标解析异常",e);
            return ReturnJson.toErrorJson("指标数据格式错误,请联系开发人员");
        }
        budgetWarningConfigMapper.updateBudgetWarningStrategy(param);
        return ReturnJson.success("更新成功");
    }

    /**
     * 删除告警指标策略配置
     * @param param
     * @return
     */
    public String deleteBudgetWarningStrategy(BudgetWarningStrategyVo param) {
        //查询告警配置是否存在使用该策略
        Long count = budgetWarningConfigMapper.countBudgetWarningConfigByStrategyId(param.getId());
        if (count > 0) {
            return ReturnJson.toErrorJson("该策略已被使用无法删除,请先移除该策略");
        }
        budgetWarningConfigMapper.deleteBudgetWarningStrategy(param);
        return ReturnJson.success("删除成功");
    }

    /**
     * 查询投放告警任务配置
     * @param param
     * @return
     */
    public List<BudgetWarningConfigVo> selectBudgetWarningConfig(BudgetWarningConfigVo param) {
        //处理告警用户集合
        if (!BlankUtils.checkBlank(param.getWarning_user())) {
            String[] split = param.getWarning_user().split(",");
            param.setWarning_user_list(Arrays.asList(split));
        }
        String strategyName = param.getStrategy_name();
        if (BlankUtils.isNotBlank(strategyName)) {
            BudgetWarningStrategyVo vo = new BudgetWarningStrategyVo();
            vo.setName(strategyName);
            List<BudgetWarningStrategyVo> budgetWarningStrategyVos = selectBudgetWarningStrategy(vo);
            if (budgetWarningStrategyVos != null && budgetWarningStrategyVos.size() > 0) {
                String ids = budgetWarningStrategyVos.stream().map(BudgetWarningStrategyVo::getId).reduce((a, b) -> a + "," + b).orElse("");
                param.setStrategy_id(ids);
            }
        }
        PageHelper.startPage(param.getStart(), param.getLimit());
        List<BudgetWarningConfigVo> list = budgetWarningConfigMapper.selectBudgetWarningConfig(param);
        return list;
    }

    /**
     * 新增投放告警任务配置
     * @param param
     * @return
     */
    public String addBudgetWarningConfig(BudgetWarningConfigVo param) {
        if (BlankUtils.checkBlank(param.getMedia()) || BlankUtils.checkBlank(param.getWarning_user())
        || BlankUtils.checkBlank(param.getDimention()) || BlankUtils.checkBlank(param.getGroup_by_field())
        || param.getTrigger_start() == null || param.getTrigger_end() == null
        || param.getTrigger_number() == null || param.getStrategy_id() == null) {
            return ReturnJson.toErrorJson("必填参数为空");
        }
        BudgetWarningStrategyVo p = new BudgetWarningStrategyVo();
        p.setId(param.getStrategy_id());
        List<BudgetWarningStrategyVo> list = budgetWarningConfigMapper.selectBudgetWarningStrategy(p);
        if (list == null || list.size() == 0) {
            return ReturnJson.toErrorJson("未找到指定策略");
        }
        if (param.getTrigger_start() >= param.getTrigger_end()) {
            return ReturnJson.toErrorJson("触发时段有误");
        }
        if (param.getTrigger_number() <= 0) {
            return ReturnJson.toErrorJson("触发次数有误");
        }

        String strategyId = param.getStrategy_id();
        BudgetWarningStrategyVo vo = new BudgetWarningStrategyVo();
        vo.setId(strategyId);
        List<BudgetWarningStrategyVo> budgetWarningStrategyVos = budgetWarningConfigMapper.selectBudgetWarningStrategy(vo);
        BudgetWarningStrategyVo sDetail = budgetWarningStrategyVos.get(0);
        // planid 维度不能查询roi报表
//        if (param.getGroup_by_field().equals(Dimension.planId.out())) {
//            if (sDetail.getStrategy().contains("roi")) {
//                return ReturnJson.toErrorJson("oppo计划维度不能使用roi报表相关指标");
//            }
//        }
        // oppo 媒体 只有planid、账户 维度才能使用预算相关指标
        if (param.getMedia().equals(Platform.OPPO.out())) {
            // 使用了预算相关的指标
            if (sDetail.getStrategy().contains("%%")) {
                // 除planid、账户维度以外不能使用预算相关指标
                if (!param.getGroup_by_field().equals(Dimension.planId.out())
                        && !param.getGroup_by_field().equals(Dimension.ACCOUNT.out())) {
                    return ReturnJson.toErrorJson("策略包含预算指标，oppo媒体只有账户和oppo计划维度才能使用预算相关指标");
                }
            }
        }

        budgetWarningConfigMapper.addBudgetWarningConfig(param);
        return ReturnJson.success("新增成功");
    }

    /**
     * 更新投放告警任务配置
     * @param param
     * @return
     */
    public String updateBudgetWarningConfig(BudgetWarningConfigVo param) {
        if (BlankUtils.checkBlank(param.getMedia()) || BlankUtils.checkBlank(param.getWarning_user())
                || BlankUtils.checkBlank(param.getDimention()) || BlankUtils.checkBlank(param.getGroup_by_field())
                || param.getTrigger_start() == null || param.getTrigger_end() == null
                || param.getTrigger_number() == null || param.getStrategy_id() == null) {
            return ReturnJson.toErrorJson("必填参数为空");
        }
        BudgetWarningStrategyVo p = new BudgetWarningStrategyVo();
        p.setId(param.getStrategy_id());
        List<BudgetWarningStrategyVo> list = budgetWarningConfigMapper.selectBudgetWarningStrategy(p);
        if (list == null || list.size() == 0) {
            return ReturnJson.toErrorJson("未找到指定策略");
        }
        if (param.getTrigger_start() > param.getTrigger_end()) {
            return ReturnJson.toErrorJson("触发时段有误");
        }
        if (param.getTrigger_number() <= 0) {
            return ReturnJson.toErrorJson("触发次数有误");
        }
        budgetWarningConfigMapper.updateBudgetWarningConfig(param);
        return ReturnJson.success("更新成功");
    }

    /**
     * 删除投放告警任务配置
     * @param param
     * @return
     */
    public String deleteBudgetWarningConfig(BudgetWarningConfigVo param) {
        budgetWarningConfigMapper.deleteBudgetWarningConfig(param);
        return ReturnJson.success("删除成功");
    }
}
