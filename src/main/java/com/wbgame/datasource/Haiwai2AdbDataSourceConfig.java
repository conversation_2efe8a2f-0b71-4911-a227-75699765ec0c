package com.wbgame.datasource;

import com.alibaba.druid.pool.DruidDataSource;
import com.github.pagehelper.PageInterceptor;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Properties;


@Configuration
@ConditionalOnProperty(prefix = "spring.datasource.haiwai2adb", name = "driver-class-name", havingValue = "com.mysql.jdbc.Driver")
@MapperScan(basePackages = Haiwai2AdbDataSourceConfig.PACKAGE, sqlSessionFactoryRef = "haiwai2adbSqlSessionFactory")
public class Haiwai2AdbDataSourceConfig {

    // 精确到 adb 目录，以便跟其他数据源隔离
    static final String PACKAGE = "com.wbgame.mapper.haiwai2adb";
    static final String MAPPER_LOCATION = "classpath:mapper/haiwai2adb/**/*.xml";

    @Value("${spring.datasource.haiwai2adb.url}")
    private String url;

    @Value("${spring.datasource.haiwai2adb.username}")
    private String username;

    @Value("${spring.datasource.haiwai2adb.password}")
    private String password;

    @Value("${spring.datasource.haiwai2adb.driver-class-name}")
    private String driverClass;
    
    
    @Value("${spring.datasource.initialSize}")
    private int initialSize;
    
    @Value("${spring.datasource.minIdle}")
    private int minIdle;
    
    @Value("${spring.datasource.maxActive}")
    private int maxActive;
    
    @Value("${spring.datasource.maxWait}")
    private int maxWait;
    
    @Value("${spring.datasource.timeBetweenEvictionRunsMillis}")
    private int timeBetweenEvictionRunsMillis;
    
    @Value("${spring.datasource.minEvictableIdleTimeMillis}")
    private int minEvictableIdleTimeMillis;
    
    @Value("${spring.datasource.validationQuery}")
    private String validationQuery;
    
    @Value("${spring.datasource.testWhileIdle}")
    private boolean testWhileIdle;
    
    @Value("${spring.datasource.testOnBorrow}")
    private boolean testOnBorrow;
    
    @Value("${spring.datasource.testOnReturn}")
    private boolean testOnReturn;
    
    @Value("${spring.datasource.removeAbandoned}")
    private boolean removeAbandoned;
    
    @Value("${spring.datasource.removeAbandonedTimeout}")
    private int removeAbandonedTimeout;
    
    @Value("${spring.datasource.poolPreparedStatements}")
    private boolean poolPreparedStatements;
    
    @Value("${spring.datasource.maxPoolPreparedStatementPerConnectionSize}")
    private int maxPoolPreparedStatementPerConnectionSize;

    
    @Bean(name = "haiwai2adbDataSource")
    public DataSource haiwai2adbDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName(driverClass);
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);

        // 解决 ResultSet is from UPDATE. No Data 错误
        dataSource.setConnectionInitSqls(new ArrayList<String>() {{
            add("SET NAMES utf8mb4");
        }});
        
        //configuration
        dataSource.setInitialSize(initialSize);
    	dataSource.setMinIdle(minIdle);
    	dataSource.setMaxActive(maxActive);
    	dataSource.setMaxWait(maxWait);
    	dataSource.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
    	dataSource.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
    	dataSource.setValidationQuery(validationQuery);
    	dataSource.setTestWhileIdle(testWhileIdle);
    	dataSource.setTestOnBorrow(testOnBorrow);
    	dataSource.setTestOnReturn(testOnReturn);
    	dataSource.setRemoveAbandoned(removeAbandoned);
    	dataSource.setRemoveAbandonedTimeout(removeAbandonedTimeout);
    	dataSource.setPoolPreparedStatements(poolPreparedStatements);
    	dataSource.setMaxPoolPreparedStatementPerConnectionSize(maxPoolPreparedStatementPerConnectionSize);
    	
        return dataSource;
    }

    @Bean(name = "haiwai2adbTransactionManager")
    public DataSourceTransactionManager haiwai2adbTransactionManager() {
        return new DataSourceTransactionManager(haiwai2adbDataSource());
    }

    @Bean(name = "haiwai2adbSqlSessionFactory")
    public SqlSessionFactory haiwai2adbSqlSessionFactory(@Qualifier("haiwai2adbDataSource") DataSource haiwai2adbDataSource)
            throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(haiwai2adbDataSource);
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources(Haiwai2AdbDataSourceConfig.MAPPER_LOCATION));
        //分页插件
        Interceptor interceptor = new PageInterceptor();
        Properties properties = new Properties();
        properties.setProperty("helperDialect", "mysql");
        properties.setProperty("offsetAsPageNum", "true");
        properties.setProperty("rowBoundsWithCount", "true");
        properties.setProperty("reasonable", "false");
        interceptor.setProperties(properties);
        sessionFactory.setPlugins(new Interceptor[] {interceptor});
        return sessionFactory.getObject();
    }
}