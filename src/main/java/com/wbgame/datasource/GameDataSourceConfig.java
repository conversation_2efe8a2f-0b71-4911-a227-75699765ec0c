package com.wbgame.datasource;

import com.alibaba.druid.pool.DruidDataSource;
import com.github.pagehelper.PageInterceptor;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.Properties;

@Configuration
@MapperScan(basePackages = GameDataSourceConfig.PACKAGE, sqlSessionFactoryRef = "gameSqlSessionFactory")
public class GameDataSourceConfig {

    // 精确到 game 目录，以便跟其他数据源隔离
    static final String PACKAGE = "com.wbgame.mapper.yxcgame";
    static final String MAPPER_LOCATION = "classpath:mapper/yxcgame/*.xml";

    @Value("${spring.datasource.game.url}")
    private String url;

    @Value("${spring.datasource.game.username}")
    private String username;

    @Value("${spring.datasource.game.password}")
    private String password;

    @Value("${spring.datasource.game.driver-class-name}")
    private String driverClass;
    
    
    @Value("${spring.datasource.initialSize}")
    private int initialSize;
    
    @Value("${spring.datasource.minIdle}")
    private int minIdle;
    
    @Value("${spring.datasource.maxActive}")
    private int maxActive;
    
    @Value("${spring.datasource.maxWait}")
    private int maxWait;
    
    @Value("${spring.datasource.timeBetweenEvictionRunsMillis}")
    private int timeBetweenEvictionRunsMillis;
    
    @Value("${spring.datasource.minEvictableIdleTimeMillis}")
    private int minEvictableIdleTimeMillis;
    
    @Value("${spring.datasource.validationQuery}")
    private String validationQuery;
    
    @Value("${spring.datasource.testWhileIdle}")
    private boolean testWhileIdle;
    
    @Value("${spring.datasource.testOnBorrow}")
    private boolean testOnBorrow;
    
    @Value("${spring.datasource.testOnReturn}")
    private boolean testOnReturn;
    
    @Value("${spring.datasource.removeAbandoned}")
    private boolean removeAbandoned;
    
    @Value("${spring.datasource.removeAbandonedTimeout}")
    private int removeAbandonedTimeout;
    
    @Value("${spring.datasource.poolPreparedStatements}")
    private boolean poolPreparedStatements;
    
    @Value("${spring.datasource.maxPoolPreparedStatementPerConnectionSize}")
    private int maxPoolPreparedStatementPerConnectionSize;

    
    @Bean(name = "gameDataSource")
    public DataSource gameDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName(driverClass);
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        
        //configuration
        dataSource.setInitialSize(initialSize);
    	dataSource.setMinIdle(minIdle);
    	dataSource.setMaxActive(maxActive);
    	dataSource.setMaxWait(maxWait);
    	dataSource.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
    	dataSource.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
    	dataSource.setValidationQuery(validationQuery);
    	dataSource.setTestWhileIdle(testWhileIdle);
    	dataSource.setTestOnBorrow(testOnBorrow);
    	dataSource.setTestOnReturn(testOnReturn);
    	dataSource.setRemoveAbandoned(removeAbandoned);
    	dataSource.setRemoveAbandonedTimeout(removeAbandonedTimeout);
    	dataSource.setPoolPreparedStatements(poolPreparedStatements);
    	dataSource.setMaxPoolPreparedStatementPerConnectionSize(maxPoolPreparedStatementPerConnectionSize);
    	
        return dataSource;
    }

    @Bean(name = "gameTransactionManager")
    public DataSourceTransactionManager gameTransactionManager() {
        return new DataSourceTransactionManager(gameDataSource());
    }

    @Bean(name = "gameSqlSessionFactory")
    public SqlSessionFactory gameSqlSessionFactory(@Qualifier("gameDataSource") DataSource gameDataSource)
            throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(gameDataSource);
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources(GameDataSourceConfig.MAPPER_LOCATION));
        //分页插件
        Interceptor interceptor = new PageInterceptor();
        Properties properties = new Properties();
        properties.setProperty("helperDialect", "mysql");
        properties.setProperty("offsetAsPageNum", "true");
        properties.setProperty("rowBoundsWithCount", "true");
        properties.setProperty("reasonable", "false");
        interceptor.setProperties(properties);
        sessionFactory.setPlugins(new Interceptor[] {interceptor});
        return sessionFactory.getObject();
    }
}