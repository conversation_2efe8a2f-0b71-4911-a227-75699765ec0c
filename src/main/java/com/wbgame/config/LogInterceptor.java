package com.wbgame.config;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.SysPathVo;
import com.wbgame.pojo.clean.WaibaoUserVo;
import com.wbgame.pojo.wbsys.WxActionRecord;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;

import com.wbgame.utils.StringUtils;
import org.apache.commons.codec.binary.Base64;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.ws.ResponseWrapper;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 操作日志统一处理类
 *
 * <AUTHOR>
 */
public class LogInterceptor implements HandlerInterceptor {

	@Autowired
	SomeService someService;

	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	
	private ThreadLocal<Long> startTime = new ThreadLocal<>();

	/**
	 * 前置处理
	 *
	 * @param httpServletRequest
	 * @param httpServletResponse
	 * @param o
	 * @return
	 * @throws Exception
	 */
	@Override
	public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) throws Exception {
		long millis = DateTime.now().getMillis();
		startTime.set(millis);
		return true;
	}

	@Override
	public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {
		

	}

	/**
	 * 后置处理
	 *
	 * @param o
	 * @param e
	 * @throws Exception
	 */
	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object o, Exception e) throws Exception {

		String url = request.getRequestURI();
		// 获取到所有请求参数的Map
		Map<String, String> valMap = new HashMap<String, String>();

		Map<String, String[]> parameterMap = request.getParameterMap();
		for (Entry<String, String[]> next : parameterMap.entrySet()) {
			valMap.put(next.getKey(), next.getValue()[0]);
		}
		String params = valMap.entrySet().stream()
				.map(entry -> entry.getKey() + ":" + entry.getValue()).collect(Collectors.joining(","));

		Long millis = startTime.get();
		long endTime = DateTime.now().getMillis();

		try {
			redisTemplate.opsForValue()
				.increment("dnwx_interface_" + ("ssds:" + url) + "_" + DateTime.now().toString("yyyyMMdd"), 1);
		} catch (Exception ex) {
			System.out.println("ssds:" + url + " 接口计数出现异常!");
		}
		try {
			redisTemplate.opsForValue()
				.increment("dnwx_resptime_" + ("ssds:" + url) + "_" + DateTime.now().toString("yyyyMMdd"), (endTime-millis));
		} catch (Exception ex) {
			System.out.println("ssds:" + url + " 响应时长计数出现异常!");
		}


		// 过滤投放历史接口的记录 -20231011
		boolean matches = (
				Pattern.matches("/common/get(.*?)", url) ||
				Pattern.matches("/common/(.*?)List", url) ||
				url.contains("/common/pageFind") ||
				url.contains("/common/recordSysRequestLog") ||
				url.contains("/common/channel") ||
				url.contains("pageTemplateHandle") ||
				url.contains("/app/gameDetailList") ||
				Pattern.matches("/wb/(.*?)Check_v3", url)
		);

		//过滤带select的接口地址，防止respBody太长
		if (
			(!url.contains("select") && !url.contains("get")
				&& !url.contains("/list") && !url.contains("/menu") && !url.contains("/market/pageFind")
				&& !url.contains("/logIn") && !url.contains("/login") && !url.contains("/export")
				&& !url.contains("/ad/permissionsHandle")
				&& !url.contains("/wb/V2login") && !url.contains("/wb/servicePage")
				&& !url.contains("/market/checkToken") && !url.contains("/adv2/app/convertedIncomeEstimateCkTwo")
				&& !url.contains("/app/convertedIncomeEstimateExportTwo") && !url.contains("/adv2/app/primevalApiAdvConfigCk")
				&& !url.contains("/wb/orgGrepSetCk") && !url.contains("/yd/hbBoundsList")
				&& !url.contains("/game/customer")&& !url.contains("wxIconPush")
				&& !url.contains("custpost") && !url.contains("/postscore")
				&& !url.contains("/superm/") && !url.contains("/some/")
				&& !url.contains("/clean/defaultConfig"))
				&& !url.equals("/error")
				&& !matches
				|| url.startsWith("/adv2")
				
			) {
			
			// 获取到响应结果
			Object respBody = request.getAttribute("respBody");


			String token = valMap.get("token") + "";
			// 记录操作行为
			WxActionRecord record = new WxActionRecord();
			record.setCpage(url);
			record.setRespBody(respBody==null?"":respBody.toString());
			if(url.startsWith("/adv2") || record.getRespBody().length() > 10240){
				record.setRespBody("操作成功！");
			}
			if(valMap.get("indexCode") != null){
				try {
					String index = new String(Base64.decodeBase64(valMap.get("indexCode")));
					record.setCtype(index);
				} catch (Exception e2) {
					e2.printStackTrace();
				}
			}

			//兼容投放接口，application/json尝试获取requestBody请求体
			if ("application/json".equals(request.getContentType())) {
				String bodyString = getBodyString(request);
				if (!BlankUtils.checkBlank(bodyString)) {
					params +=  BlankUtils.checkBlank(params) ? "requestBody:" + bodyString : ",requestBody:" + bodyString;
					//如果请求参数找不到token检查bodyString是否存在token
					if (StringUtils.isEmpty(valMap.get("token"))) {
						try {
							JSONObject jsonObject = JSONObject.parseObject(bodyString);
							if (!BlankUtils.checkBlank(jsonObject.getString("token"))) {
								token = jsonObject.getString("token");
							}
						}catch (Exception e1) {
						}
					}
					if (StringUtils.isEmpty(valMap.get("indexCode"))) {
						try {
							JSONObject jsonObject = JSONObject.parseObject(bodyString);
							if (!BlankUtils.checkBlank(jsonObject.getString("indexCode"))) {
								String index = new String(Base64.decodeBase64(jsonObject.getString("indexCode")));
								record.setCtype(index);
							}
						}catch (Exception e1) {
						}
					}
				}
			}

			record.setParams("操作内容: " + params);

			// 设置访问ip
			String client_ip = request.getHeader("x-forwarded-for");
			try {
				if (!BlankUtils.checkBlank(client_ip))
					record.setClient_ip(client_ip);
				else
					record.setClient_ip(request.getRemoteAddr());
			} catch (Exception e2) {

			}

			//TODO 分token处理不同系统日志,后期整合为通用处理
			if (token.startsWith("wbtoken")
					|| token.startsWith("adverttoken")
					|| token.startsWith("mintoken")
					|| token.startsWith("mobiletoken")) {
				CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
				if (cuser == null) {
					record.setCuser("异常用户");
				} else {
					record.setCuser(cuser.getLogin_name());
				}
			} else if (token.startsWith("cleantoken")) {
				//清理王
				com.wbgame.pojo.clean.WaibaoUserVo cuser = (com.wbgame.pojo.clean.WaibaoUserVo) redisTemplate.opsForValue().get(token);
				if (cuser == null) {
					record.setCuser("异常用户");
				} else {
					record.setCuser(cuser.getUser_id());
				}
			} else if (token.startsWith("waibaotoken")) {
				//投放和变现
				com.wbgame.pojo.WaibaoUserVo cuser = (com.wbgame.pojo.WaibaoUserVo) redisTemplate.opsForValue().get(token);
				if (cuser == null) {
					record.setCuser("异常用户");
				} else {
					record.setCuser(cuser.getUser_id());
				}
			} else if (token.startsWith("financetoken")) {
				//投放和变现
				com.wbgame.pojo.finance.WaibaoUserVo cuser = (com.wbgame.pojo.finance.WaibaoUserVo) redisTemplate.opsForValue().get(token);
				if (cuser == null) {
					record.setCuser("异常用户");
				} else {
					record.setCuser(cuser.getUser_id());
				}
			}else if (("dnwx1002dnwx1002").equals(token)) {
				//自动化测试配置需求
				record.setCuser("autoTest");
				
			}

			try {
				someService.insertLogInfo(record);
			}catch (DataIntegrityViolationException e1) {

			}

		}
	}

	/**
	 * 获取请求Body
	 *
	 * @param request
	 * @return
	 */
	public static String getBodyString(ServletRequest request) {
		StringBuilder sb = new StringBuilder();
		InputStream inputStream = null;
		BufferedReader reader = null;
		try {
			inputStream = request.getInputStream();
			reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
			String line = "";
			while ((line = reader.readLine()) != null) {
				sb.append(line);
			}
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (inputStream != null) {
				try {
					inputStream.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return sb.toString();
	}


}