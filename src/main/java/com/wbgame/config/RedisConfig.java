package com.wbgame.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration
public class RedisConfig {
	
	/*@Bean //消息监听容器(监听redis服务器中channel通道的信息写入)    
    RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory,   
                            MessageListenerAdapter listenerAdapter) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();    
        container.setConnectionFactory(connectionFactory);  
        
        //监听redis中一个channel通道 
        container.addMessageListener(listenerAdapter, new PatternTopic("channel"));    
        //这个container 可以添加多个 messageListener    
        return container;
    }    
      
    @Bean //消息监听适配器，把消息接受者/消息接受者的消息处理方法封装到适配器中(这个消息监听适配器是消息监听容器所需要的)  
    MessageListenerAdapter listenerAdapter(MessageReceiver receiver) {    
        //监听到消息手反射调用receiver的receiveMessage方法  
        return new MessageListenerAdapter(receiver, "receiveMessage");    
    }*/
	// 发送消息到通道  redisTemplate.convertAndSend("channel", "hello redis--"+i);
    
	/**
     * redisTemplate 序列化使用的jdkSerializeable, 存储二进制字节码, 所以自定义序列化类
     * @param redisConnectionFactory
     * @return
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<String, Object>();
        redisTemplate.setConnectionFactory(redisConnectionFactory);

        // 使用Jackson2JsonRedisSerialize 替换默认序列化
        Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(Object.class);

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);

        jackson2JsonRedisSerializer.setObjectMapper(objectMapper);

        /* 	
          	设置value的序列化规则和 key的序列化规则，
          	StringRedisTemplate的key和val默认都是StringRedisSerializer的序列化方式，即str.getBytes()
          	RedisTemplate的key默认是jdk序列化方式，将其改为StringRedisSerializer则两种方式的key可以通用
        */
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(jackson2JsonRedisSerializer);
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(jackson2JsonRedisSerializer);
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }
}