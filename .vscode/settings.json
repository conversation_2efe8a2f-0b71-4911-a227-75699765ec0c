{
    "editor.fontSize": 15,
    "update.mode": "none",
    "window.newWindowProfile": "默认",
    "java.completion.matchCase": "off",
    "editor.find.matchCase": false, // 不区分大小写
    "svn.path": "C:/Program Files/TortoiseSVN/bin/svn.exe",
    "svn.decorations.enabled": true,       // 启用文件资源管理器的状态图标
    "explorer.decorations.badges": true,   // 显示角标（如文件数量）
    "explorer.decorations.colors": true,   // 根据状态显示颜色
    "java.configuration.runtimes": [
        
        {
            "name": "JavaSE-1.8",
            "path": "C:/Program Files/Java/jdk1.8",
            "default": true
        },
        {
            "name": "JavaSE-21",
            "path": "C:/Program Files/Java24",
            "default": false
        }
    ],
    

    "java.configuration.maven.globalSettings": "D:/Maven/apache-maven-3.3.3/conf/settings.xml",
    "java.configuration.maven.userSettings": "D:/Maven/apache-maven-3.3.3/conf/settings.xml",
    "maven.executable.path": "D:/Maven/apache-maven-3.3.3/bin/mvn.cmd",
    "files.associations": {
        "*.cjson": "jsonc",
        "*.wxss": "css",
        "*.wxs": "javascript"
    },
    "emmet.includeLanguages": {
        "wxml": "html"
    },
    "minapp-vscode.disableAutoConfig": true,
    "editor.stickyScroll.enabled": false,
    "editor.codeActionsOnSave": {

    },
    "editor.dropIntoEditor.preferences": [
        
    ],
    "java.jdt.ls.java.home": "C:/Program Files/Java24"
    
}